Collecting mypy
  Downloading mypy-1.16.1-cp39-cp39-win_amd64.whl.metadata (2.2 kB)
Collecting typing_extensions>=4.6.0 (from mypy)
  Downloading typing_extensions-4.14.0-py3-none-any.whl.metadata (3.0 kB)
Collecting mypy_extensions>=1.0.0 (from mypy)
  Downloading mypy_extensions-1.1.0-py3-none-any.whl.metadata (1.1 kB)
Collecting pathspec>=0.9.0 (from mypy)
  Downloading pathspec-0.12.1-py3-none-any.whl.metadata (21 kB)
Requirement already satisfied: tomli>=1.1.0 in f:\software\md_vector_processor\venv\lib\site-packages (from mypy) (2.2.1)
Downloading mypy-1.16.1-cp39-cp39-win_amd64.whl (9.5 MB)
   ---------------------------------------- 9.5/9.5 MB 1.2 MB/s eta 0:00:00
Downloading mypy_extensions-1.1.0-py3-none-any.whl (5.0 kB)
Downloading pathspec-0.12.1-py3-none-any.whl (31 kB)
Downloading typing_extensions-4.14.0-py3-none-any.whl (43 kB)
Installing collected packages: typing_extensions, pathspec, mypy_extensions, mypy

Successfully installed mypy-1.16.1 mypy_extensions-1.1.0 pathspec-0.12.1 typing_extensions-4.14.0
