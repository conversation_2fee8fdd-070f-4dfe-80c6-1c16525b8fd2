#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Path导入错误修复验证脚本
"""

def verify_path_fixes():
    """验证Path导入修复"""
    print("🔍 验证Path导入修复...")
    
    try:
        # 检查文件内容
        with open('src/gui/widgets/vectorize.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # 1. 检查顶部导入
        import_found = False
        for i, line in enumerate(lines[:20]):
            if 'from pathlib import Path' in line:
                import_found = True
                print(f"✅ 第{i+1}行: 找到Path导入: {line.strip()}")
                break
        
        if not import_found:
            print("❌ 未找到Path导入")
            return False
        
        # 2. 检查局部变量冲突修复
        problematic_patterns = [
            'path = Path(',  # 应该改为 file_path_obj = Path(
        ]
        
        issues_found = []
        for i, line in enumerate(lines, 1):
            for pattern in problematic_patterns:
                if pattern in line and 'file_path_obj = Path(' not in line:
                    # 排除合法的用法
                    if 'index_path = Path(' not in line and 'original_path = Path(' not in line:
                        issues_found.append(f"第{i}行: {line.strip()}")
        
        if issues_found:
            print("❌ 发现潜在的局部变量冲突:")
            for issue in issues_found:
                print(f"   {issue}")
            return False
        else:
            print("✅ 未发现局部变量冲突")
        
        # 3. 检查Path使用
        path_usages = []
        for i, line in enumerate(lines, 1):
            if 'selected_index_path = Path(' in line:
                path_usages.append(f"第{i}行: {line.strip()}")
        
        if path_usages:
            print(f"✅ 找到Path使用: {len(path_usages)}处")
            for usage in path_usages:
                print(f"   {usage}")
        
        # 4. 检查file_path_obj使用
        file_path_obj_usages = []
        for i, line in enumerate(lines, 1):
            if 'file_path_obj = Path(' in line:
                file_path_obj_usages.append(f"第{i}行: {line.strip()}")
        
        if file_path_obj_usages:
            print(f"✅ 找到file_path_obj使用: {len(file_path_obj_usages)}处")
            for usage in file_path_obj_usages:
                print(f"   {usage}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("Path导入错误修复验证")
    print("=" * 60)
    
    success = verify_path_fixes()
    
    if success:
        print("\n🎉 Path导入错误修复验证通过！")
        print("\n📋 修复内容总结:")
        print("1. ✅ 在文件顶部添加了 'from pathlib import Path'")
        print("2. ✅ 将局部变量 'path = Path()' 改为 'file_path_obj = Path()'")
        print("3. ✅ 更新了所有相关的变量引用")
        print("4. ✅ 清理了Python缓存文件")
        
        print("\n🚀 现在可以正常使用向量化功能:")
        print("1. GUI已启动成功")
        print("2. 点击'向量化'标签页")
        print("3. 选择文件夹: test_training_data/raw_documents/enterprise_standards")
        print("4. 选择模型: nomic-embed-text")
        print("5. 点击'向量化'按钮")
        print("\n⚠️  如果仍有问题，请提供具体的错误信息")
    else:
        print("\n❌ 验证失败，需要进一步检查")

if __name__ == "__main__":
    main()
