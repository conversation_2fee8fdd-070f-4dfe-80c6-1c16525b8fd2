import os
import sys

print("基础测试开始")
print(f"当前工作目录: {os.getcwd()}")
print(f"Python版本: {sys.version}")

# 检查目录
pdf_dir = "test_training_data/raw_documents/enterprise_standards"
md_dir = "test_training_data/raw_documents_MD/enterprise_standards"

print(f"PDF目录存在: {os.path.exists(pdf_dir)}")
print(f"MD目录存在: {os.path.exists(md_dir)}")

if os.path.exists(pdf_dir):
    print(f"PDF目录内容: {os.listdir(pdf_dir)}")

if os.path.exists(md_dir):
    print(f"MD目录内容: {os.listdir(md_dir)}")

print("基础测试完成")
