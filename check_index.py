#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pickle
from pathlib import Path

print("检查索引0002状态...")

try:
    meta_file = Path("data/indices/0002.meta")
    idx_file = Path("data/indices/0002.idx")
    
    print(f"元数据文件存在: {meta_file.exists()}")
    print(f"索引文件存在: {idx_file.exists()}")
    
    if meta_file.exists():
        with open(meta_file, 'rb') as f:
            meta = pickle.load(f)
        
        print("索引0002元数据:")
        print(f"  向量数: {meta.get('total_vectors', '未知')}")
        print(f"  维度: {meta.get('dimension', '未知')}")
        print(f"  类型: {meta.get('index_type', '未知')}")
        print(f"  更新时间: {meta.get('last_update_time', '未知')}")
        
        if idx_file.exists():
            size_kb = idx_file.stat().st_size / 1024
            print(f"  索引文件大小: {size_kb:.2f} KB")
            
    print("检查完成")
    
except Exception as e:
    print(f"检查失败: {e}")
    import traceback
    traceback.print_exc()
