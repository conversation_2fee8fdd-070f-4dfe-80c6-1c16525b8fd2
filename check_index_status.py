#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查索引状态和向量数据的脚本
"""

import os
import sys
import json
import h5py
import numpy as np
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_index_files():
    """检查索引文件状态"""
    print("=" * 60)
    print("📁 检查索引文件状态")
    print("=" * 60)
    
    index_dir = Path("data/indices")
    if not index_dir.exists():
        print("❌ 索引目录不存在")
        return
    
    # 检查auto_standards_test索引
    test_index = index_dir / "auto_standards_test.idx"
    test_meta = index_dir / "auto_standards_test.meta"
    
    if test_index.exists():
        print(f"✓ 找到索引文件: {test_index}")
        print(f"  - 文件大小: {test_index.stat().st_size / 1024:.2f} KB")
        print(f"  - 修改时间: {test_index.stat().st_mtime}")
    else:
        print("❌ auto_standards_test.idx 不存在")
    
    if test_meta.exists():
        print(f"✓ 找到元数据文件: {test_meta}")
        print(f"  - 文件大小: {test_meta.stat().st_size} bytes")
        
        # 读取元数据
        try:
            with open(test_meta, 'r', encoding='utf-8') as f:
                meta_data = json.load(f)
            print(f"  - 元数据内容:")
            for key, value in meta_data.items():
                print(f"    {key}: {value}")
        except Exception as e:
            print(f"  - 读取元数据失败: {e}")
    else:
        print("❌ auto_standards_test.meta 不存在")

def check_vector_storage():
    """检查向量存储状态"""
    print("=" * 60)
    print("📊 检查向量存储状态")
    print("=" * 60)
    
    # 检查HDF5向量文件
    vector_file = Path("data/vectors/vectors.h5")
    if vector_file.exists():
        print(f"✓ 找到向量存储文件: {vector_file}")
        print(f"  - 文件大小: {vector_file.stat().st_size / 1024 / 1024:.2f} MB")
        
        try:
            with h5py.File(vector_file, 'r') as f:
                print(f"  - HDF5文件结构:")
                def print_structure(name, obj):
                    if isinstance(obj, h5py.Dataset):
                        print(f"    数据集 {name}: shape={obj.shape}, dtype={obj.dtype}")
                    elif isinstance(obj, h5py.Group):
                        print(f"    组 {name}:")
                
                f.visititems(print_structure)
                
                # 检查auto_standards_test相关数据
                if 'auto_standards_test' in f:
                    group = f['auto_standards_test']
                    print(f"  - auto_standards_test组:")
                    if 'vectors' in group:
                        vectors = group['vectors']
                        print(f"    向量数据: shape={vectors.shape}, dtype={vectors.dtype}")
                        print(f"    向量数量: {vectors.shape[0]}")
                        print(f"    向量维度: {vectors.shape[1]}")
                    if 'metadata' in group:
                        metadata = group['metadata']
                        print(f"    元数据: {len(metadata)} 条记录")
                else:
                    print("  - 未找到auto_standards_test相关数据")
                    
        except Exception as e:
            print(f"  - 读取向量文件失败: {e}")
    else:
        print("❌ 向量存储文件不存在")
    
    # 检查版本信息
    version_file = Path("data/vectors/version.json")
    if version_file.exists():
        try:
            with open(version_file, 'r', encoding='utf-8') as f:
                version_data = json.load(f)
            print(f"✓ 版本信息:")
            for key, value in version_data.items():
                print(f"    {key}: {value}")
        except Exception as e:
            print(f"  - 读取版本信息失败: {e}")

def check_metadata_storage():
    """检查元数据存储状态"""
    print("=" * 60)
    print("📋 检查元数据存储状态")
    print("=" * 60)
    
    metadata_dir = Path("data/vectors/metadata")
    if metadata_dir.exists():
        print(f"✓ 找到元数据目录: {metadata_dir}")
        
        # 列出所有元数据文件
        metadata_files = list(metadata_dir.glob("*.json"))
        print(f"  - 元数据文件数量: {len(metadata_files)}")
        
        for meta_file in metadata_files[:5]:  # 只显示前5个
            print(f"    {meta_file.name}")
            try:
                with open(meta_file, 'r', encoding='utf-8') as f:
                    meta_data = json.load(f)
                print(f"      记录数: {len(meta_data)}")
            except Exception as e:
                print(f"      读取失败: {e}")
        
        if len(metadata_files) > 5:
            print(f"    ... 还有 {len(metadata_files) - 5} 个文件")
    else:
        print("❌ 元数据目录不存在")

def test_index_loading():
    """测试索引加载"""
    print("=" * 60)
    print("🔧 测试索引加载")
    print("=" * 60)
    
    try:
        from src.indexer.builder import IndexBuilder
        
        config = {
            'indexing': {
                'index_type': 'hnsw',
                'metric': 'cosine',
                'ef_construction': 200,
                'ef_search': 100,
                'M': 16
            }
        }
        
        builder = IndexBuilder(config)
        test_index = Path("data/indices/auto_standards_test.idx")
        
        if builder.load_index(test_index):
            print(f"✓ 成功加载索引: {test_index}")
            print(f"  - 索引类型: {builder.index_type}")
            print(f"  - 向量维度: {builder.dimension}")
            print(f"  - 向量数量: {builder.total_vectors}")
            
            if hasattr(builder.index, 'get_current_count'):
                current_count = builder.index.get_current_count()
                print(f"  - 索引中实际向量数: {current_count}")
            
            if hasattr(builder.index, 'get_max_elements'):
                max_elements = builder.index.get_max_elements()
                print(f"  - 最大容量: {max_elements}")
            
            return True
        else:
            print("❌ 索引加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_ollama_models():
    """检查Ollama模型状态"""
    print("=" * 60)
    print("🤖 检查Ollama模型状态")
    print("=" * 60)
    
    try:
        import requests
        
        # 检查Ollama服务状态
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json()
                print(f"✓ Ollama服务运行正常")
                print(f"  - 可用模型数量: {len(models.get('models', []))}")
                
                for model in models.get('models', []):
                    print(f"    {model.get('name', 'Unknown')}")
                
                return True
            else:
                print(f"❌ Ollama服务响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 无法连接到Ollama服务: {e}")
            return False
            
    except ImportError:
        print("❌ requests库未安装，无法检查Ollama状态")
        return False

def main():
    """主函数"""
    print("🔍 开始检查向量化数据和索引状态...")
    print()
    
    # 检查索引文件
    check_index_files()
    print()
    
    # 检查向量存储
    check_vector_storage()
    print()
    
    # 检查元数据存储
    check_metadata_storage()
    print()
    
    # 测试索引加载
    index_ok = test_index_loading()
    print()
    
    # 检查Ollama模型
    ollama_ok = check_ollama_models()
    print()
    
    # 总结
    print("=" * 60)
    print("📊 检查结果总结")
    print("=" * 60)
    
    print(f"索引加载: {'✅ 正常' if index_ok else '❌ 异常'}")
    print(f"Ollama服务: {'✅ 正常' if ollama_ok else '❌ 异常'}")
    
    if not index_ok:
        print("\n🔧 索引问题可能的解决方案:")
        print("1. 重新运行向量化流程")
        print("2. 检查索引文件是否损坏")
        print("3. 清理缓存后重新创建索引")
    
    if not ollama_ok:
        print("\n🔧 Ollama问题可能的解决方案:")
        print("1. 启动Ollama服务: ollama serve")
        print("2. 检查模型是否已下载: ollama list")
        print("3. 下载所需模型: ollama pull nomic-embed-text")

if __name__ == "__main__":
    main()
