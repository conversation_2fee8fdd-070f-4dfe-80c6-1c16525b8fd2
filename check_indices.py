#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pickle
from pathlib import Path

def check_indices():
    """检查所有索引文件的状态"""
    print("=" * 60)
    print("📊 检查索引文件状态")
    print("=" * 60)
    
    indices_dir = Path('data/indices')
    idx_files = list(indices_dir.glob('*.idx'))
    
    print(f"找到 {len(idx_files)} 个索引文件:")
    print()
    
    for idx_file in sorted(idx_files):
        size_kb = idx_file.stat().st_size / 1024
        meta_file = idx_file.with_suffix('.meta')
        
        print(f"📁 {idx_file.name}")
        print(f"  大小: {size_kb:.2f} KB")
        
        if meta_file.exists():
            try:
                with open(meta_file, 'rb') as f:
                    metadata = pickle.load(f)
                
                print(f"  维度: {metadata.get('dimension', '未知')}")
                print(f"  向量数: {metadata.get('total_vectors', '未知')}")
                print(f"  索引类型: {metadata.get('index_type', '未知')}")
                print(f"  度量: {metadata.get('metric', '未知')}")
                
                # 判断是否为有效索引
                if size_kb > 1 and metadata.get('total_vectors', 0) > 0:
                    print("  ✅ 有效索引")
                else:
                    print("  ❌ 空索引")
                    
            except Exception as e:
                print(f"  ❌ 元数据读取失败: {e}")
        else:
            print("  ❌ 缺少元数据文件")
        
        print()

if __name__ == "__main__":
    check_indices()
