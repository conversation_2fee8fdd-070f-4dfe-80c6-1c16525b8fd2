import sys
import os
import logging
import winreg
from pathlib import Path
import subprocess

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_registry_versions():
    """检查注册表中的VC++版本"""
    versions = []
    registry_paths = [
        r"SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64",
        r"SOFTWARE\WOW6432Node\Microsoft\VisualStudio\14.0\VC\Runtimes\x64",
        r"SOFTWARE\Microsoft\DevDiv\VC\Servicing\14.0\RuntimeMinimum"
    ]
    
    for path in registry_paths:
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path, 0, 
                               winreg.KEY_READ | winreg.KEY_WOW64_64KEY) as key:
                version = winreg.QueryValueEx(key, "Version")[0]
                versions.append((path, version))
                logger.info(f"找到VC++版本: {version} (路径: {path})")
        except WindowsError:
            logger.debug(f"注册表路径不存在: {path}")
            
    return versions

def check_dll_files():
    """检查系统目录中的VC++ DLL文件"""
    system32 = Path(os.environ["SystemRoot"]) / "System32"
    dll_files = [
        "msvcp140.dll",      # C++ 标准库
        "vcruntime140.dll",  # C 运行时
        "vcruntime140_1.dll" # 额外的运行时组件
    ]
    
    for dll in dll_files:
        dll_path = system32 / dll
        if dll_path.exists():
            logger.info(f"找到 {dll}")
            try:
                # 获取文件版本信息
                result = subprocess.run(
                    ['powershell', f'(Get-Item "{dll_path}").VersionInfo.FileVersion'],
                    capture_output=True,
                    text=True
                )
                if result.stdout:
                    logger.info(f"{dll} 版本: {result.stdout.strip()}")
            except Exception as e:
                logger.warning(f"无法获取 {dll} 版本信息: {e}")
        else:
            logger.error(f"未找到 {dll}")

def recommend_action(versions):
    """根据检查结果提供建议"""
    if not versions:
        logger.warning("未找到任何VC++运行库版本")
        logger.info("建议操作:")
        logger.info("1. 下载并安装最新的VC++运行库:")
        logger.info("   https://aka.ms/vs/17/release/vc_redist.x64.exe")
        logger.info("2. 安装完成后重启系统")
        return False
    
    # 检查版本是否满足PyQt6要求
    min_required = "14.20"  # PyQt6需要的最低版本
    latest_version = max(v[1] for v in versions)
    
    if latest_version < min_required:
        logger.warning(f"当前VC++版本 ({latest_version}) 低于PyQt6要求的版本 ({min_required})")
        logger.info("建议更新到最新版本")
        return False
        
    return True

def main():
    logger.info("开始检查VC++运行库...")
    
    # 检查注册表中的版本
    versions = check_registry_versions()
    
    # 检查DLL文件
    check_dll_files()
    
    # 提供建议
    if recommend_action(versions):
        logger.info("VC++运行库检查通过，支持PyQt6")
        return 0
    return 1

if __name__ == "__main__":
    sys.exit(main())