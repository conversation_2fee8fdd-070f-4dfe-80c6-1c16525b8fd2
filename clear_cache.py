#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
程序缓存清理工具
确保每次更新后使用最新的程序代码
"""

import os
import sys
import shutil
import logging
from pathlib import Path

def clear_python_cache():
    """清理Python缓存文件"""
    print("🧹 开始清理Python缓存...")
    
    # 清理__pycache__目录
    cache_dirs = []
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            cache_dir = os.path.join(root, '__pycache__')
            cache_dirs.append(cache_dir)
    
    for cache_dir in cache_dirs:
        try:
            shutil.rmtree(cache_dir)
            print(f"✅ 删除缓存目录: {cache_dir}")
        except Exception as e:
            print(f"❌ 删除缓存目录失败 {cache_dir}: {e}")
    
    # 清理.pyc文件
    pyc_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                pyc_file = os.path.join(root, file)
                pyc_files.append(pyc_file)
    
    for pyc_file in pyc_files:
        try:
            os.remove(pyc_file)
            print(f"✅ 删除.pyc文件: {pyc_file}")
        except Exception as e:
            print(f"❌ 删除.pyc文件失败 {pyc_file}: {e}")
    
    print(f"🎉 Python缓存清理完成！删除了 {len(cache_dirs)} 个缓存目录和 {len(pyc_files)} 个.pyc文件")

def clear_import_cache():
    """清理Python导入缓存"""
    print("🔄 清理Python导入缓存...")
    
    # 清理sys.modules中的项目模块
    modules_to_remove = []
    for module_name in sys.modules.keys():
        if module_name.startswith('src.') or module_name == 'src':
            modules_to_remove.append(module_name)
    
    for module_name in modules_to_remove:
        try:
            del sys.modules[module_name]
            print(f"✅ 清理模块缓存: {module_name}")
        except Exception as e:
            print(f"❌ 清理模块缓存失败 {module_name}: {e}")
    
    print(f"🎉 导入缓存清理完成！清理了 {len(modules_to_remove)} 个模块")

def clear_log_cache():
    """清理日志缓存"""
    print("📝 清理日志缓存...")
    
    log_dirs = ['logs', 'temp', 'cache']
    cleared_files = 0
    
    for log_dir in log_dirs:
        log_path = Path(log_dir)
        if log_path.exists():
            for file_path in log_path.rglob('*'):
                if file_path.is_file() and file_path.suffix in ['.log', '.tmp', '.cache']:
                    try:
                        file_path.unlink()
                        cleared_files += 1
                        print(f"✅ 删除日志文件: {file_path}")
                    except Exception as e:
                        print(f"❌ 删除日志文件失败 {file_path}: {e}")
    
    print(f"🎉 日志缓存清理完成！删除了 {cleared_files} 个文件")

def restart_program():
    """重启程序"""
    print("🔄 准备重启程序...")
    print("请手动重新运行: python gui_run.py")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 MD向量处理器 - 缓存清理工具")
    print("=" * 60)
    
    try:
        # 清理各种缓存
        clear_python_cache()
        print()
        clear_import_cache()
        print()
        clear_log_cache()
        print()
        
        print("=" * 60)
        print("✨ 缓存清理完成！")
        print("💡 建议：现在重新启动程序以确保使用最新代码")
        print("🔧 命令：python gui_run.py")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 缓存清理过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
