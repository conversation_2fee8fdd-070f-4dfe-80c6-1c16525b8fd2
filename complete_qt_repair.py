#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PyQt6完整修复脚本
"""

import sys
import os
import subprocess
import logging
import shutil
from pathlib import Path

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def clean_qt_installation():
    """完全清理PyQt6安装"""
    logger.info("正在完全清理PyQt6安装...")
    
    # 卸载PyQt6相关包
    packages = ["PyQt6", "PyQt6-sip", "PyQt6-Qt6"]
    for pkg in packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "uninstall", "-y", pkg])
            logger.info(f"已卸载 {pkg}")
        except subprocess.CalledProcessError as e:
            logger.warning(f"卸载 {pkg} 失败: {e}")

    # 清理可能的残留文件
    site_packages = Path(sys.executable).parent / "Lib" / "site-packages"
    for item in site_packages.glob("PyQt6*"):
        try:
            if item.is_dir():
                shutil.rmtree(item)
            else:
                item.unlink()
            logger.info(f"已删除残留文件: {item}")
        except Exception as e:
            logger.warning(f"删除 {item} 失败: {e}")

def install_pyqt6():
    """安装PyQt6"""
    logger.info("正在安装PyQt6...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyQt6"])
        logger.info("PyQt6安装完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"安装PyQt6失败: {e}")
        return False

def verify_installation():
    """验证安装"""
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow
        from PyQt6.QtCore import QLibraryInfo
        
        logger.info("PyQt6已正确安装")
        logger.info(f"Qt版本: {QLibraryInfo.version().toString()}")
        
        # 简单测试Qt功能
        app = QApplication(sys.argv)
        window = QMainWindow()
        window.setWindowTitle("测试窗口")
        window.resize(300, 200)
        window.show()
        logger.info("Qt窗口测试成功")
        
        return True
    except Exception as e:
        logger.error(f"验证失败: {e}")
        return False

def main():
    # 1. 完全清理现有安装
    clean_qt_installation()
    
    # 2. 重新安装
    if not install_pyqt6():
        logger.error("安装失败")
        return 1
    
    # 3. 验证安装
    if not verify_installation():
        logger.error("验证失败")
        return 1
    
    logger.info("PyQt6修复完成")
    return 0

if __name__ == "__main__":
    sys.exit(main())