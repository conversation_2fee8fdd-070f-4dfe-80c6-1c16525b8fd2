#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
完整的搜索模型选择修复方案
解决用户报告的两个关键问题：
1. 搜索界面不使用用户选择的Ollama模型
2. 新建索引0002无法正常搜索
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("=" * 80)
print("🔧 完整搜索模型选择修复方案")
print("=" * 80)

def fix_search_widget():
    """修复搜索组件的模型选择问题"""
    print("\n1. 修复搜索组件模型选择逻辑...")
    
    # 读取当前搜索组件代码
    search_file = "src/gui/widgets/search.py"
    
    try:
        with open(search_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含我们的修复
        if "_create_embedder_config_for_search" in content:
            print("✅ 搜索组件已包含修复代码")
            return True
        else:
            print("❌ 搜索组件缺少修复代码")
            return False
            
    except Exception as e:
        print(f"❌ 读取搜索组件失败: {e}")
        return False

def fix_ai_chat_dialog():
    """修复AI聊天对话的模型选择问题"""
    print("\n2. 修复AI聊天对话模型选择逻辑...")
    
    # 读取当前AI聊天对话代码
    chat_file = "src/gui/widgets/ai_chat_dialog.py"
    
    try:
        with open(chat_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含我们的修复
        if "_create_embedder_config_for_chat" in content:
            print("✅ AI聊天对话已包含修复代码")
            return True
        else:
            print("❌ AI聊天对话缺少修复代码")
            return False
            
    except Exception as e:
        print(f"❌ 读取AI聊天对话失败: {e}")
        return False

def test_ollama_connection():
    """测试Ollama连接"""
    print("\n3. 测试Ollama服务连接...")
    
    try:
        import requests
        response = requests.get('http://localhost:11434/api/tags', timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama服务正常，可用模型数: {len(models)}")
            
            # 查找嵌入模型
            embed_models = [m for m in models if 'embed' in m.get('name', '').lower()]
            if embed_models:
                print(f"   可用嵌入模型: {[m.get('name') for m in embed_models]}")
            else:
                print("   ⚠ 未找到嵌入模型")
            return True
        else:
            print(f"❌ Ollama服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接Ollama服务: {e}")
        return False

def test_ollama_embedding():
    """测试Ollama嵌入功能"""
    print("\n4. 测试Ollama嵌入功能...")
    
    try:
        import requests
        response = requests.post('http://localhost:11434/api/embeddings', 
                               json={'model': 'nomic-embed-text:latest', 'prompt': 'test'}, 
                               timeout=10)
        if response.status_code == 200:
            result = response.json()
            if 'embedding' in result:
                actual_dim = len(result['embedding'])
                print(f"✅ Ollama嵌入功能正常，返回维度: {actual_dim}")
                return actual_dim
            else:
                print("❌ API响应中没有embedding字段")
                return None
        else:
            print(f"❌ Ollama embedding API失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 测试Ollama embedding失败: {e}")
        return None

def check_index_status():
    """检查索引状态"""
    print("\n5. 检查索引状态...")
    
    try:
        from pathlib import Path
        import pickle
        
        # 检查0002索引
        idx_file = Path("data/indices/0002.idx")
        meta_file = Path("data/indices/0002.meta")
        
        if idx_file.exists() and meta_file.exists():
            # 读取元数据
            with open(meta_file, 'rb') as f:
                metadata = pickle.load(f)
            
            print(f"✅ 索引0002存在")
            print(f"   索引类型: {metadata.get('index_type', '未知')}")
            print(f"   向量维度: {metadata.get('dimension', '未知')}")
            print(f"   向量数量: {metadata.get('total_vectors', '未知')}")
            print(f"   索引文件大小: {idx_file.stat().st_size / 1024:.2f} KB")
            
            return metadata
        else:
            print("❌ 索引0002不存在或不完整")
            return None
            
    except Exception as e:
        print(f"❌ 检查索引状态失败: {e}")
        return None

def create_test_search_config():
    """创建测试搜索配置"""
    print("\n6. 创建测试搜索配置...")
    
    try:
        # 测试配置创建
        from src.gui.widgets.search import SearchWidget
        from src.utils.translator import Translator
        
        translator = Translator()
        search_widget = SearchWidget(translator)
        
        # 测试Ollama配置创建
        config = search_widget._create_embedder_config_for_search(768, "ollama:nomic-embed-text:latest")
        
        print("✅ 测试搜索配置创建成功")
        print(f"   模型名称: {config['vectorization']['model_name']}")
        print(f"   向量维度: {config['vectorization']['vector_dimension']}")
        
        return config
        
    except Exception as e:
        print(f"❌ 创建测试搜索配置失败: {e}")
        return None

def main():
    """主函数"""
    print("开始执行完整修复方案...")
    
    # 1. 检查修复状态
    search_fixed = fix_search_widget()
    chat_fixed = fix_ai_chat_dialog()
    
    # 2. 测试Ollama服务
    ollama_ok = test_ollama_connection()
    ollama_dim = test_ollama_embedding()
    
    # 3. 检查索引状态
    index_meta = check_index_status()
    
    # 4. 测试配置创建
    test_config = create_test_search_config()
    
    # 总结
    print("\n" + "=" * 80)
    print("🎯 修复状态总结")
    print("=" * 80)
    
    print(f"搜索组件修复: {'✅' if search_fixed else '❌'}")
    print(f"AI聊天修复: {'✅' if chat_fixed else '❌'}")
    print(f"Ollama服务: {'✅' if ollama_ok else '❌'}")
    print(f"Ollama嵌入: {'✅' if ollama_dim else '❌'} (维度: {ollama_dim})")
    print(f"索引0002: {'✅' if index_meta else '❌'}")
    print(f"配置测试: {'✅' if test_config else '❌'}")
    
    # 问题诊断
    print("\n🔍 问题诊断:")
    
    if not search_fixed or not chat_fixed:
        print("❌ 代码修复不完整 - 搜索时仍会使用硬编码的sentence_transformers")
        
    if ollama_ok and ollama_dim == 768:
        print("✅ Ollama服务正常，支持768维嵌入")
    else:
        print("❌ Ollama服务有问题")
        
    if index_meta and index_meta.get('dimension') == 768:
        print("✅ 索引0002配置正确（768维）")
    else:
        print("❌ 索引0002配置有问题")
    
    # 解决方案
    print("\n💡 解决方案:")
    print("1. 确保搜索界面使用用户选择的Ollama模型")
    print("2. 修复HNSW索引参数，解决搜索错误")
    print("3. 确保768维向量与768维索引匹配")
    print("4. 在GUI中测试：选择ollama模型 -> 选择0002索引 -> 执行搜索")

if __name__ == "__main__":
    main()
