app:
  language: zh_CN
  name: "MD\u5411\u91CF\u5904\u7406\u5668"
  theme: dark_blue
  version: 1.0.0
indexing:
  cache_index: true
  default_k: 10
  hybrid_mode: hnsw_ivf
  hybrid_n_lists: 50
  hybrid_n_probes: 5
  incremental_index: true
  index_type: hybrid
  max_candidates: 100
  metric: cosine
  min_similarity: 0.5
  pq_bits: 8
  pq_m: 8
  quantization: pq
  rebuild_threshold: 1000
  use_gpu_index: false
logging:
  backup_count: 5
  console_log: true
  log_dir: logs
  log_file: processor.log
  log_level: INFO
  max_file_size: 10MB
performance:
  adaptive_batch_size: true
  batch_size: 64
  clear_cache_interval: 1000
  device: cuda
  gpu_memory_fraction: 0.8
  max_batch_size: 64
  memory_limit_gb: 44
  num_workers: 16
  pq_m: 8
  quantization_type: pq
  use_gpu: true
  use_memory_mapping: true
  use_quantization: true
storage:
  backup_enabled: true
  backup_interval: 86400
  base_dir: data/auto_electrical_standards
  compression: true
  max_backups: 7
  max_versions: 5
  metadata_format: pickle
  metadata_index: true
  vector_format: numpy
  versioning: true
system:
  memory_limit: 44G
  num_workers: 16
  temp_dir: temp
vectorization:
  batch_size: 64
  cache_enabled: true
  device: cuda
  domain: technical
  model_name: ollama_deepseek-r1_32b
  normalize_vectors: true
  pooling_method: mean_pooling
  vector_dimension: 768
