# 向量化配置
vectorization:
  model_name: "ollama_deepseek-r1_32b"
  vector_dimension: 768  # 更新为768维度以匹配Ollama模型输出
  batch_size: 64  # 增大批处理大小，利用24GB GPU内存
  device: "cuda"
  pooling_method: "mean_pooling"
  normalize_vectors: true
  cache_enabled: true
  domain: "technical"
  
  # GPU内存优化
  gpu_memory_optimization: true
  mixed_precision: true  # 使用FP16加速计算
  
# 性能优化
performance:
  use_gpu: true
  gpu_memory_fraction: 0.9  # 增加到90%，充分利用GPU
  num_workers: 16  # 使用一半CPU核心进行并行处理
  adaptive_batch_size: true
  max_batch_size: 128  # 增大最大批处理大小
  use_memory_mapping: true
  clear_cache_interval: 5000  # 增大清理间隔，减少I/O
  
  # 内存优化
  ram_cache_size: 16  # GB，用于缓存处理中的数据
  prefetch_batches: 4  # 预加载4个批次到内存
  
  # 存储优化
  use_ssd_for_critical: true  # 关键数据使用SSD
  temp_dir: "/ssd/temp"  # 临时文件使用SSD