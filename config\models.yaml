# 本地模型配置
local_models:
  # Ollama配置
  ollama:
    enabled: true
    api_url: "http://localhost:11434/api"
    default_model: "nomic-embed-text"
    models:
      # 向量化专用模型 (优先使用)
      - name: "nomic-embed-text"
        type: "embedding"
        vector_dimension: 768
        parameters: {}
      - name: "bge-m3"
        type: "embedding"
        vector_dimension: 1024
        parameters: {}
      - name: "all-minilm"
        type: "embedding"
        vector_dimension: 384
        parameters: {}

      # 大语言模型 (查询增强)
      - name: "deepseek-r1:32b"
        type: "llm"
        parameters:
          temperature: 0.1
          max_tokens: 4096
          top_p: 0.9
      - name: "deepseek-r1:8b"
        type: "llm"
        parameters:
          temperature: 0.2
          max_tokens: 2048
          top_p: 0.9
      - name: "deepseek-coder-v2:latest"
        type: "llm"
        parameters:
          temperature: 0.1
          max_tokens: 4096
      - name: "qwen2.5-coder:32b"
        type: "llm"
        parameters:
          temperature: 0.1
          max_tokens: 4096
      - name: "qwen3:30b-a3b"
        type: "llm"
        parameters:
          temperature: 0.5
          max_tokens: 4096
      - name: "llamafamily/llama3-chinese-8b-instruct:latest"
        type: "llm"
        parameters:
          temperature: 0.3
          max_tokens: 2048
      - name: "gemma3:27b"
        type: "llm"
        parameters:
          temperature: 0.2
          max_tokens: 3072
  
  # 本地Hugging Face模型配置
  huggingface:
    enabled: true
    models_dir: "models/huggingface"
    models:
      - name: "bert-base-uncased"
        parameters:
          max_length: 512
