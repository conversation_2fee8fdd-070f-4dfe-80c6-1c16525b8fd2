#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
诊断和修复搜索功能问题的脚本
"""

import os
import sys
import json
import requests
import h5py
import numpy as np
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_ollama_service():
    """检查Ollama服务状态"""
    print("=" * 60)
    print("🤖 检查Ollama服务状态")
    print("=" * 60)
    
    try:
        # 检查Ollama服务
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ Ollama服务运行正常")
            print(f"  - 可用模型数量: {len(models.get('models', []))}")
            
            available_models = []
            for model in models.get('models', []):
                model_name = model.get('name', 'Unknown')
                print(f"    📦 {model_name}")
                available_models.append(model_name)
            
            # 检查是否有嵌入模型
            embed_models = [m for m in available_models if 'embed' in m.lower() or 'nomic' in m.lower()]
            if embed_models:
                print(f"  - 找到嵌入模型: {embed_models}")
            else:
                print("  ⚠️ 未找到嵌入模型，建议安装: ollama pull nomic-embed-text")
            
            # 检查是否有对话模型
            chat_models = [m for m in available_models if any(x in m.lower() for x in ['qwen', 'deepseek', 'llama', 'gemma'])]
            if chat_models:
                print(f"  - 找到对话模型: {chat_models}")
            else:
                print("  ⚠️ 未找到对话模型，建议安装: ollama pull qwen2.5:7b")
            
            return True, available_models
        else:
            print(f"❌ Ollama服务响应异常: {response.status_code}")
            return False, []
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Ollama服务: {e}")
        print("  💡 解决方案:")
        print("    1. 启动Ollama服务: ollama serve")
        print("    2. 检查端口11434是否被占用")
        return False, []

def check_index_data():
    """检查索引数据状态"""
    print("=" * 60)
    print("📊 检查索引数据状态")
    print("=" * 60)
    
    # 检查auto_standards_test索引
    index_file = Path("data/indices/auto_standards_test.idx")
    meta_file = Path("data/indices/auto_standards_test.meta")
    
    if not index_file.exists():
        print("❌ auto_standards_test.idx 不存在")
        return False, 0
    
    if not meta_file.exists():
        print("❌ auto_standards_test.meta 不存在")
        return False, 0
    
    print(f"✅ 索引文件存在")
    print(f"  - 索引文件大小: {index_file.stat().st_size / 1024:.2f} KB")
    print(f"  - 元数据文件大小: {meta_file.stat().st_size} bytes")
    
    # 尝试加载索引
    try:
        from src.indexer.builder import IndexBuilder
        
        config = {
            'indexing': {
                'index_type': 'hnsw',
                'metric': 'cosine',
                'ef_construction': 200,
                'ef_search': 100,
                'M': 16
            }
        }
        
        builder = IndexBuilder(config)
        if builder.load_index(index_file):
            print(f"✅ 索引加载成功")
            print(f"  - 索引类型: {builder.index_type}")
            print(f"  - 向量维度: {builder.dimension}")
            print(f"  - 向量数量: {builder.total_vectors}")
            
            if hasattr(builder.index, 'get_current_count'):
                current_count = builder.index.get_current_count()
                print(f"  - 索引中实际向量数: {current_count}")
                return True, current_count
            else:
                return True, builder.total_vectors
        else:
            print("❌ 索引加载失败")
            return False, 0
    except Exception as e:
        print(f"❌ 索引加载出错: {e}")
        return False, 0

def check_vector_storage():
    """检查向量存储状态"""
    print("=" * 60)
    print("💾 检查向量存储状态")
    print("=" * 60)
    
    vector_file = Path("data/vectors/vectors.h5")
    if not vector_file.exists():
        print("❌ 向量存储文件不存在")
        return False, 0
    
    print(f"✅ 向量存储文件存在: {vector_file.stat().st_size / 1024 / 1024:.2f} MB")
    
    try:
        with h5py.File(vector_file, 'r') as f:
            print("📁 HDF5文件结构:")
            
            total_vectors = 0
            for key in f.keys():
                print(f"  📂 组: {key}")
                if key == 'auto_standards_test':
                    group = f[key]
                    for subkey in group.keys():
                        dataset = group[subkey]
                        print(f"    📄 {subkey}: shape={dataset.shape}, dtype={dataset.dtype}")
                        if subkey == 'vectors':
                            vector_count = dataset.shape[0]
                            vector_dim = dataset.shape[1]
                            total_vectors = vector_count
                            print(f"    ✅ 向量数量: {vector_count}")
                            print(f"    ✅ 向量维度: {vector_dim}")
            
            return True, total_vectors
    except Exception as e:
        print(f"❌ 读取向量文件失败: {e}")
        return False, 0

def test_embedding_consistency():
    """测试嵌入一致性"""
    print("=" * 60)
    print("🔧 测试嵌入一致性")
    print("=" * 60)
    
    try:
        # 使用修复后的配置
        config = {
            'vectorization': {
                'model_name': 'local:ollama_nomic-embed-text_latest',
                'vector_dimension': 768,
                'batch_size': 8,
                'device': 'cpu',
                'normalize_vectors': True
            },
            'local_models': {
                'ollama': {
                    'enabled': True,
                    'api_url': 'http://localhost:11434/api',
                    'default_model': 'nomic-embed-text:latest',
                    'models': []
                }
            }
        }
        
        from src.vectorizer.embeddings import TextEmbedding
        embedder = TextEmbedding(config)
        
        print(f"✅ TextEmbedding初始化成功")
        print(f"  - 配置的向量维度: {embedder.vector_dimension}")
        print(f"  - 使用Ollama: {embedder.use_ollama}")
        
        # 测试向量化
        test_text = "汽车电气系统标准测试"
        vector = embedder.encode_text(test_text)
        
        print(f"✅ 向量化测试成功")
        print(f"  - 输入文本: {test_text}")
        print(f"  - 输出向量维度: {vector.shape}")
        print(f"  - 向量范围: [{vector.min():.4f}, {vector.max():.4f}]")
        
        if vector.shape[0] == 768:
            print("✅ 向量维度正确 (768)")
            return True
        else:
            print(f"❌ 向量维度错误，期望768，实际{vector.shape[0]}")
            return False
            
    except Exception as e:
        print(f"❌ 嵌入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_functionality():
    """测试搜索功能"""
    print("=" * 60)
    print("🔍 测试搜索功能")
    print("=" * 60)
    
    try:
        # 加载索引
        from src.indexer.builder import IndexBuilder
        
        config = {
            'indexing': {
                'index_type': 'hnsw',
                'metric': 'cosine',
                'ef_construction': 200,
                'ef_search': 100,
                'M': 16
            }
        }
        
        builder = IndexBuilder(config)
        index_file = Path("data/indices/auto_standards_test.idx")
        
        if not builder.load_index(index_file):
            print("❌ 无法加载索引")
            return False
        
        # 创建嵌入器
        embed_config = {
            'vectorization': {
                'model_name': 'local:ollama_nomic-embed-text_latest',
                'vector_dimension': 768,
                'batch_size': 8,
                'device': 'cpu',
                'normalize_vectors': True
            },
            'local_models': {
                'ollama': {
                    'enabled': True,
                    'api_url': 'http://localhost:11434/api',
                    'default_model': 'nomic-embed-text:latest',
                    'models': []
                }
            }
        }
        
        from src.vectorizer.embeddings import TextEmbedding
        embedder = TextEmbedding(embed_config)
        
        # 执行搜索测试
        query_text = "汽车电气系统安全标准"
        query_vector = embedder.encode_text(query_text)
        
        print(f"✅ 查询向量生成成功: {query_vector.shape}")
        
        # 执行搜索
        distances, indices = builder.search(query_vector.reshape(1, -1), k=5)
        
        print(f"✅ 搜索执行成功")
        print(f"  - 距离: {distances}")
        print(f"  - 索引: {indices}")
        
        # 检查结果有效性
        valid_results = [i for i in indices[0] if i != -1]
        print(f"  - 有效结果数: {len(valid_results)}")
        
        if len(valid_results) > 0:
            print("✅ 搜索功能正常")
            return True
        else:
            print("❌ 搜索未返回有效结果")
            return False
            
    except Exception as e:
        print(f"❌ 搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 开始诊断搜索功能问题...")
    print()
    
    results = []
    
    # 1. 检查Ollama服务
    ollama_ok, models = check_ollama_service()
    results.append(("Ollama服务", ollama_ok))
    print()
    
    # 2. 检查索引数据
    index_ok, index_count = check_index_data()
    results.append(("索引数据", index_ok))
    print()
    
    # 3. 检查向量存储
    storage_ok, storage_count = check_vector_storage()
    results.append(("向量存储", storage_ok))
    print()
    
    # 4. 测试嵌入一致性
    embed_ok = test_embedding_consistency()
    results.append(("嵌入一致性", embed_ok))
    print()
    
    # 5. 测试搜索功能
    search_ok = test_search_functionality()
    results.append(("搜索功能", search_ok))
    print()
    
    # 总结
    print("=" * 60)
    print("📊 诊断结果总结")
    print("=" * 60)
    
    for name, status in results:
        status_text = "✅ 正常" if status else "❌ 异常"
        print(f"{name}: {status_text}")
    
    print()
    print(f"索引中的向量数: {index_count}")
    print(f"存储中的向量数: {storage_count}")
    
    # 问题分析和建议
    print()
    print("=" * 60)
    print("💡 问题分析和建议")
    print("=" * 60)
    
    if not ollama_ok:
        print("🔧 Ollama服务问题:")
        print("  1. 启动Ollama: ollama serve")
        print("  2. 安装嵌入模型: ollama pull nomic-embed-text")
        print("  3. 安装对话模型: ollama pull qwen2.5:7b")
    
    if index_count == 0 or storage_count == 0:
        print("🔧 向量数据问题:")
        print("  1. 重新运行向量化流程")
        print("  2. 检查向量化过程中的错误日志")
        print("  3. 确保使用正确的索引名称")
    
    if not embed_ok:
        print("🔧 嵌入一致性问题:")
        print("  1. 检查Ollama服务连接")
        print("  2. 确认nomic-embed-text模型已安装")
        print("  3. 检查向量维度配置")
    
    if not search_ok:
        print("🔧 搜索功能问题:")
        print("  1. 确保索引和嵌入器使用相同配置")
        print("  2. 检查索引文件完整性")
        print("  3. 重新构建索引")
    
    success_count = sum(status for _, status in results)
    total_count = len(results)
    
    if success_count == total_count:
        print("\n🎉 所有功能正常，搜索应该可以正常工作！")
        return 0
    else:
        print(f"\n⚠️ {total_count - success_count} 个功能存在问题，需要修复")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
