#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

import requests
import yaml
import logging
import numpy as np
from typing import Dict, Any

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def check_ollama_service():
    """检查Ollama服务状态"""
    logger = logging.getLogger(__name__)
    logger.info("🔍 检查Ollama服务状态...")
    
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json()
            logger.info(f"✅ Ollama服务运行正常")
            logger.info(f"  - 可用模型数量: {len(models.get('models', []))}")
            
            available_models = []
            for model in models.get('models', []):
                model_name = model.get('name', 'Unknown')
                logger.info(f"    📦 {model_name}")
                available_models.append(model_name)
            
            return True, available_models
        else:
            logger.error(f"❌ Ollama服务响应异常: {response.status_code}")
            return False, []
    except Exception as e:
        logger.error(f"❌ 无法连接到Ollama服务: {e}")
        return False, []

def test_ollama_embedding_direct():
    """直接测试Ollama嵌入API"""
    logger = logging.getLogger(__name__)
    logger.info("🔍 直接测试Ollama嵌入API...")
    
    # 测试nomic-embed-text模型
    model_name = "nomic-embed-text:latest"
    test_text = "这是一个测试文本，用于验证Ollama向量化功能。"
    
    try:
        endpoint = "http://localhost:11434/api/embeddings"
        payload = {
            "model": model_name,
            "prompt": test_text
        }
        
        logger.info(f"发送请求到: {endpoint}")
        logger.info(f"使用模型: {model_name}")
        logger.info(f"测试文本: {test_text}")
        
        response = requests.post(endpoint, json=payload, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        if 'embedding' in result:
            embedding = np.array(result['embedding'])
            logger.info(f"✅ 直接API调用成功")
            logger.info(f"  - 向量维度: {len(embedding)}")
            logger.info(f"  - 向量前5个值: {embedding[:5]}")
            return True, len(embedding)
        else:
            logger.error(f"❌ API返回结果中没有embedding字段: {result}")
            return False, 0
            
    except Exception as e:
        logger.error(f"❌ 直接API调用失败: {e}")
        return False, 0

def test_ollama_class():
    """测试OllamaEmbedding类"""
    logger = logging.getLogger(__name__)
    logger.info("🔍 测试OllamaEmbedding类...")
    
    try:
        from src.vectorizer.ollama import OllamaEmbedding
        
        # 创建配置
        config = {
            'local_models': {
                'ollama': {
                    'enabled': True,
                    'api_url': 'http://localhost:11434/api',
                    'default_model': 'nomic-embed-text:latest',
                    'models': []
                }
            },
            'vectorization': {
                'vector_dimension': 768
            }
        }
        
        logger.info("创建OllamaEmbedding实例...")
        ollama_embedder = OllamaEmbedding(config)
        
        logger.info(f"  - 服务可用: {ollama_embedder.service_available}")
        logger.info(f"  - 模型可用: {ollama_embedder.model_available}")
        logger.info(f"  - 向量维度: {ollama_embedder.vector_dimension}")
        
        if not ollama_embedder.service_available:
            logger.error("❌ OllamaEmbedding检测到服务不可用")
            return False
        
        if not ollama_embedder.model_available:
            logger.error("❌ OllamaEmbedding检测到模型不可用")
            return False
        
        # 测试向量化
        test_text = "这是一个测试文本，用于验证Ollama向量化功能。"
        logger.info(f"测试文本: {test_text}")
        
        vector = ollama_embedder.encode_text(test_text)
        logger.info(f"✅ OllamaEmbedding向量化成功")
        logger.info(f"  - 向量维度: {vector.shape}")
        logger.info(f"  - 向量前5个值: {vector[:5]}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ OllamaEmbedding测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_text_embedding_with_ollama():
    """测试TextEmbedding类使用Ollama"""
    logger = logging.getLogger(__name__)
    logger.info("🔍 测试TextEmbedding类使用Ollama...")
    
    try:
        from src.vectorizer.embeddings import TextEmbedding
        
        # 加载应用配置
        with open('config/app_config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 强制启用Ollama
        config['local_models'] = {
            'ollama': {
                'enabled': True,
                'api_url': 'http://localhost:11434/api',
                'default_model': 'nomic-embed-text:latest',
                'models': []
            }
        }
        
        config['vectorization'] = {
            'model_name': 'local:ollama_nomic-embed-text_latest',
            'batch_size': 8,
            'vector_dimension': 768,
            'normalize_vectors': True,
            'device': 'cpu'
        }
        
        logger.info("创建TextEmbedding实例...")
        embedder = TextEmbedding(config)
        
        logger.info(f"  - 使用Ollama: {embedder.use_ollama}")
        logger.info(f"  - 模型名称: {embedder.model_name}")
        logger.info(f"  - 向量维度: {embedder.vector_dimension}")
        
        if not embedder.use_ollama:
            logger.error("❌ TextEmbedding未启用Ollama")
            return False
        
        # 测试向量化
        test_text = "这是一个测试文本，用于验证TextEmbedding使用Ollama的功能。"
        logger.info(f"测试文本: {test_text}")
        
        vector = embedder.embed_texts([test_text])
        logger.info(f"✅ TextEmbedding使用Ollama向量化成功")
        logger.info(f"  - 向量形状: {vector.shape}")
        logger.info(f"  - 向量前5个值: {vector[0][:5]}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ TextEmbedding使用Ollama测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_vectorization_process():
    """模拟向量化过程中的Ollama调用"""
    logger = logging.getLogger(__name__)
    logger.info("🔍 模拟向量化过程中的Ollama调用...")
    
    try:
        # 模拟向量化界面的配置
        model_name = "local:ollama_nomic-embed-text_latest"
        vector_dimension = 768
        batch_size = 8
        
        # 准备向量化配置（模拟vectorize.py中的逻辑）
        vectorization_config = {
            'vectorization': {
                'model_name': model_name,
                'batch_size': batch_size,
                'vector_dimension': vector_dimension,
                'normalize_vectors': True,
                'device': 'cpu'
            }
        }
        
        # 添加本地模型配置
        vectorization_config['local_models'] = {
            'ollama': {
                'enabled': True,
                'api_url': 'http://localhost:11434/api',
                'default_model': 'nomic-embed-text:latest',
                'models': []
            }
        }
        
        logger.info("使用向量化配置创建TextEmbedding...")
        logger.info(f"  - 模型名称: {model_name}")
        logger.info(f"  - 向量维度: {vector_dimension}")
        logger.info(f"  - 批处理大小: {batch_size}")
        
        from src.vectorizer.embeddings import TextEmbedding
        embedder = TextEmbedding(vectorization_config)
        
        logger.info(f"  - 使用Ollama: {embedder.use_ollama}")
        logger.info(f"  - 实际模型名称: {embedder.model_name}")
        
        # 测试文本块（模拟文档分块）
        test_chunks = [
            "这是第一个文档块，包含一些测试内容。",
            "这是第二个文档块，用于验证批量处理。",
            "这是第三个文档块，测试Ollama向量化功能。"
        ]
        
        logger.info(f"测试 {len(test_chunks)} 个文本块...")
        
        # 批量向量化
        vectors = embedder.embed_texts(test_chunks)
        logger.info(f"✅ 模拟向量化过程成功")
        logger.info(f"  - 向量形状: {vectors.shape}")
        logger.info(f"  - 每个向量维度: {vectors.shape[1]}")
        
        # 检查是否使用了Ollama
        if hasattr(embedder, 'ollama_embedder') and embedder.ollama_embedder:
            logger.info("✅ 确认使用了Ollama嵌入器")
        else:
            logger.warning("⚠️ 可能未使用Ollama嵌入器")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 模拟向量化过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("=" * 80)
    logger.info("🔍 Ollama向量化调用诊断")
    logger.info("=" * 80)
    
    # 1. 检查Ollama服务
    service_ok, models = check_ollama_service()
    if not service_ok:
        logger.error("❌ Ollama服务检查失败，无法继续")
        return False
    
    # 2. 直接测试Ollama API
    api_ok, dimension = test_ollama_embedding_direct()
    if not api_ok:
        logger.error("❌ 直接API测试失败")
        return False
    
    # 3. 测试OllamaEmbedding类
    class_ok = test_ollama_class()
    if not class_ok:
        logger.error("❌ OllamaEmbedding类测试失败")
        return False
    
    # 4. 测试TextEmbedding使用Ollama
    text_embedding_ok = test_text_embedding_with_ollama()
    if not text_embedding_ok:
        logger.error("❌ TextEmbedding使用Ollama测试失败")
        return False
    
    # 5. 模拟完整向量化过程
    simulation_ok = simulate_vectorization_process()
    if not simulation_ok:
        logger.error("❌ 向量化过程模拟失败")
        return False
    
    logger.info("=" * 80)
    logger.info("🎉 所有Ollama调用测试通过！")
    logger.info("=" * 80)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
