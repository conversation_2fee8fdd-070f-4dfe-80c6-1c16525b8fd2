import sys
import os
import logging
from pathlib import Path
import subprocess

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_env():
    """检查Python环境"""
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"Python路径: {sys.executable}")
    logger.info(f"系统架构: {os.environ.get('PROCESSOR_ARCHITECTURE', 'unknown')}")

def check_qt_paths():
    """检查Qt相关路径"""
    python_dir = Path(sys.executable).parent
    site_packages = python_dir / "Lib" / "site-packages"
    qt_paths = {
        "PyQt6目录": site_packages / "PyQt6",
        "Qt6目录": site_packages / "PyQt6" / "Qt6",
        "Qt6 bin目录": site_packages / "PyQt6" / "Qt6" / "bin",
    }
    
    for name, path in qt_paths.items():
        if path.exists():
            logger.info(f"{name}存在: {path}")
        else:
            logger.error(f"{name}不存在: {path}")

def fix_qt_env():
    """修复Qt环境"""
    python_dir = Path(sys.executable).parent
    qt_bin = python_dir / "Lib" / "site-packages" / "PyQt6" / "Qt6" / "bin"
    
    if qt_bin.exists():
        # 添加Qt bin目录到PATH
        os.environ["PATH"] = f"{qt_bin};{os.environ['PATH']}"
        logger.info(f"已添加Qt bin目录到PATH: {qt_bin}")
    
    # 设置Qt插件路径
    os.environ["QT_PLUGIN_PATH"] = str(python_dir / "Lib" / "site-packages" / "PyQt6" / "Qt6" / "plugins")
    
    return qt_bin.exists()

def main():
    logger.info("开始诊断Qt环境...")
    check_python_env()
    check_qt_paths()
    
    if fix_qt_env():
        logger.info("环境修复完成")
        
        # 尝试导入PyQt6
        try:
            import PyQt6.QtCore
            logger.info("PyQt6导入成功")
            return 0
        except ImportError as e:
            logger.error(f"PyQt6导入失败: {e}")
            return 1
    else:
        logger.error("Qt环境修复失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())