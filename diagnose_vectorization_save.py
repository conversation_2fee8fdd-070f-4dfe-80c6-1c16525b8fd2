#!/usr/bin/env python
# -*- coding: utf-8 -*-

import h5py
import pickle
import numpy as np
from pathlib import Path
import logging

def check_vectorization_save_issue():
    """诊断向量化保存问题"""
    print("=" * 80)
    print("🔍 诊断向量化保存问题")
    print("=" * 80)
    
    # 1. 检查HDF5文件中的实际向量数据
    print("\n📊 检查HDF5向量存储:")
    vector_file = Path("data/vectors/vectors.h5")
    
    if not vector_file.exists():
        print("❌ 向量文件不存在")
        return
    
    total_vectors_in_hdf5 = 0
    total_groups = 0
    
    try:
        with h5py.File(vector_file, 'r') as f:
            print(f"📁 HDF5文件大小: {vector_file.stat().st_size / 1024 / 1024:.2f} MB")
            print(f"📁 总组数: {len(f.keys())}")
            
            # 统计所有向量
            for key in f.keys():
                if key.startswith('compressed_'):
                    total_groups += 1
                    # 尝试获取对应的ids组来计算向量数
                    ids_key = key.replace('compressed_', 'ids_')
                    if ids_key in f:
                        ids_data = f[ids_key]
                        vector_count = len(ids_data)
                        total_vectors_in_hdf5 += vector_count
                        if total_groups <= 5:  # 只显示前5个组的详情
                            print(f"  📂 {key}: {vector_count} 个向量")
            
            print(f"✅ HDF5中总向量数: {total_vectors_in_hdf5}")
            
    except Exception as e:
        print(f"❌ 读取HDF5文件失败: {e}")
        return
    
    # 2. 检查所有索引文件的向量计数
    print(f"\n📊 检查索引文件向量计数:")
    indices_dir = Path("data/indices")
    
    for idx_file in indices_dir.glob("*.idx"):
        meta_file = idx_file.with_suffix('.meta')
        if meta_file.exists():
            try:
                with open(meta_file, 'rb') as f:
                    metadata = pickle.load(f)
                
                index_name = idx_file.stem
                total_vectors = metadata.get('total_vectors', 0)
                dimension = metadata.get('dimension', 0)
                size_kb = idx_file.stat().st_size / 1024
                
                status = "✅" if total_vectors > 0 and size_kb > 1 else "❌"
                print(f"  {status} {index_name}: {total_vectors} 个向量 ({dimension}维, {size_kb:.1f}KB)")
                
                if index_name == 'auto_standards_test':
                    print(f"    🎯 目标索引状态: {total_vectors} 个向量")
                    
            except Exception as e:
                print(f"  ❌ {idx_file.stem}: 读取元数据失败 - {e}")
    
    # 3. 分析问题
    print(f"\n🔍 问题分析:")
    print(f"  📊 HDF5中实际存储的向量数: {total_vectors_in_hdf5}")
    print(f"  📊 HDF5中的数据组数: {total_groups}")
    
    if total_vectors_in_hdf5 > 0 and total_groups > 0:
        avg_vectors_per_group = total_vectors_in_hdf5 / total_groups
        print(f"  📊 平均每组向量数: {avg_vectors_per_group:.1f}")
        
        print(f"\n💡 发现的问题:")
        print(f"  1. HDF5文件中有 {total_vectors_in_hdf5} 个向量数据")
        print(f"  2. 但auto_standards_test索引显示向量数很少")
        print(f"  3. 这表明向量数据被保存到HDF5，但索引计数有问题")
        
        print(f"\n🔧 可能的原因:")
        print(f"  1. 索引保存时向量计数更新错误")
        print(f"  2. 向量添加到索引时计数逻辑有误")
        print(f"  3. 索引文件保存失败但HDF5保存成功")
    
    # 4. 检查最近的向量化日志
    print(f"\n📋 建议的修复步骤:")
    print(f"  1. 修复向量化代码中的计数逻辑")
    print(f"  2. 重建auto_standards_test索引")
    print(f"  3. 确保索引保存和HDF5保存同步")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    check_vectorization_save_issue()
