import sys
import os
import logging
from pathlib import Path

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_environment():
    """检查环境变量和路径"""
    logger.info(f"Python Version: {sys.version}")
    logger.info(f"Python Path: {sys.executable}")
    
    # 检查 PYTHONPATH
    logger.info(f"PYTHONPATH: {os.environ.get('PYTHONPATH', 'Not Set')}")
    
    # 检查 PATH
    path_entries = os.environ.get('PATH', '').split(os.pathsep)
    logger.info("Checking PATH entries:")
    for entry in path_entries:
        if 'qt' in entry.lower():
            logger.info(f"  Qt related PATH: {entry}")

def check_qt_installation():
    """检查 Qt 安装"""
    qt_base = Path(r"D:\Python\Lib\site-packages\PyQt6\Qt6")
    required_dlls = ['Qt6Core.dll', 'Qt6Gui.dll', 'Qt6Widgets.dll']
    
    logger.info("Checking Qt6 DLLs:")
    if qt_base.exists():
        bin_dir = qt_base / 'bin'
        if bin_dir.exists():
            for dll in required_dlls:
                dll_path = bin_dir / dll
                if dll_path.exists():
                    logger.info(f"  Found {dll} at {dll_path}")
                else:
                    logger.error(f"  Missing {dll}")
        else:
            logger.error(f"bin directory not found at {bin_dir}")
    else:
        logger.error(f"Qt6 base directory not found at {qt_base}")

if __name__ == "__main__":
    logger.info("Starting Qt diagnostic...")
    check_environment()
    check_qt_installation()