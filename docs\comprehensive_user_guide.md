# MD向量处理器完整使用指南

## 📋 目录

1. [双格式文档处理](#双格式文档处理)
2. [auto_standards索引操作指导](#auto_standards索引操作指导)
3. [可视化功能参数说明](#可视化功能参数说明)
4. [向量维度设置分析](#向量维度设置分析)
5. [完整操作流程](#完整操作流程)

---

## 🔄 双格式文档处理

### 文件夹结构设计

系统支持PDF和MD文件并存处理，目录结构如下：

```
test_training_data/
├── raw_documents/           # PDF文档目录
│   ├── enterprise_standards/
│   │   ├── pdfs/           # PDF文件
│   │   └── metadata/       # 元数据文件
├── raw_documents_MD/        # MD文档目录（新增）
│   ├── enterprise_standards/
│   │   ├── pdfs/           # MD文件（对应PDF）
│   │   └── metadata/       # 元数据文件（共享）
```

### 双格式校核功能

#### 使用方法

```bash
# 基础校核
python dual_format_validator.py -d test_training_data/raw_documents

# 自定义参数校核
python dual_format_validator.py \
  -d test_training_data/raw_documents \
  -o data/validation_reports \
  -s 0.8 \
  -c 0.9 \
  -l DEBUG
```

#### 校核指标

1. **内容完整性检测**
   - PDF文件可读性验证
   - MD文件格式正确性验证
   - 最小内容长度检查（默认100字符）

2. **相互校核评估**
   - 内容相似度计算（默认阈值0.7）
   - 结构完整性对比（页数vs章节数）
   - 质量等级评定（EXCELLENT/GOOD/POOR等）

3. **输出报告格式**
   - Excel格式详细报告
   - 支持追加更新，不删除已有内容
   - 包含统计汇总和改进建议

---

## 🎯 auto_standards索引操作指导

### 索引配置分析

基于代码分析，`auto_standards`索引使用HNSW结构：

```yaml
# 索引配置
indexing:
  index_type: "hnsw"
  metric: "cosine"
  ef_construction: 200
  M: 16
  max_elements: 100000
```

### 完整操作步骤

#### 第1步：环境准备

```bash
# 激活虚拟环境
cd f:\software\md_vector_processor
.\venv\Scripts\activate

# 启动GUI
python gui_run.py
```

#### 第2步：索引创建

1. **打开索引管理界面**
   - 点击侧边栏"INDEX"按钮
   - 选择"创建新索引"

2. **配置索引参数**
   ```
   索引名称: auto_standards
   索引类型: HNSW
   度量方式: cosine
   向量维度: 384（或根据模型调整）
   构建精度: 200
   连接数M: 16
   最大元素数: 100000
   ```

3. **选择数据源**
   - 数据目录: `test_training_data/raw_documents/enterprise_standards`
   - 包含子目录: 是
   - 文件类型: PDF + MD（双格式）

#### 第3步：向量化处理

1. **打开向量化界面**
   - 点击侧边栏"VECTORIZE"按钮

2. **配置向量化参数**
   ```
   模型选择: ollama_deepseek-r1_32b（推荐大模型）
   向量维度: 384
   批处理大小: 64
   计算设备: cuda（如果可用）
   标准化向量: 是
   ```

3. **执行向量化**
   - 选择目标索引: auto_standards
   - 开始处理
   - 监控进度和日志

#### 第4步：大模型查询

1. **打开搜索界面**
   - 点击侧边栏"SEARCH"按钮

2. **配置查询参数**
   ```
   目标索引: auto_standards
   查询模型: ollama_deepseek-r1_32b
   返回结果数: 10
   相似度阈值: 0.7
   ```

3. **执行查询**
   - 输入查询文本（支持中英文）
   - 点击搜索
   - 查看结果和相似度分数

---

## 📊 可视化功能参数说明

### 可视化方法对比

| 方法 | 适用场景 | 计算复杂度 | 参数设置 |
|------|----------|------------|----------|
| **PCA** | 线性降维，快速预览 | 低 | 无特殊参数 |
| **t-SNE** | 非线性降维，聚类可视化 | 高 | 困惑度(5-100) |
| **UMAP** | 保持全局结构 | 中 | 邻居数、最小距离 |

### 详细参数说明

#### 1. 数据选择参数
- **选择索引**: 选择要可视化的向量索引
- **最大点数**: 限制显示的向量数量（10-10000）
  - 目的：控制计算量和显示效果
  - 建议：小数据集用1000，大数据集用5000

#### 2. 可视化设置参数
- **方法选择**: PCA/t-SNE/UMAP
  - PCA：适合快速预览，计算速度快
  - t-SNE：适合发现聚类结构，效果好但慢
  - UMAP：平衡速度和效果

- **维度选择**: 2D/3D
  - 2D：便于观察和交互
  - 3D：更丰富的空间信息

- **困惑度** (仅t-SNE)：5-100
  - 低值(5-15)：关注局部结构
  - 高值(30-100)：关注全局结构
  - 建议：数据量的平方根

- **迭代次数** (仅t-SNE)：100-10000
  - 更多迭代 = 更好收敛
  - 建议：1000次（默认）

#### 3. 显示控制参数
- **显示标签**: 是否显示点标签
- **颜色映射**: 按类别着色
- **点大小**: 控制散点大小（便于观察）

### 可视化功能目的

1. **数据质量检查**
   - 发现异常向量
   - 检测聚类效果
   - 验证向量分布

2. **模型效果评估**
   - 观察语义相似性
   - 检查向量空间结构
   - 对比不同模型效果

3. **索引优化指导**
   - 确定最佳聚类数
   - 调整索引参数
   - 优化检索效果

---

## 🔧 向量维度设置分析

### 当前限制分析

代码中向量维度限制为64-1024：

```python
# src/gui/widgets/vectorize.py
self.vector_dim_spin.setRange(64, 1024)
self.vector_dim_spin.setValue(384)
```

### 限制原因分析

1. **内存考虑**
   - 1024维向量：每个向量4KB（float32）
   - 100万向量需要4GB内存
   - 更高维度会显著增加内存需求

2. **计算效率**
   - 维度越高，相似度计算越慢
   - 索引构建时间呈指数增长
   - GPU显存限制

3. **模型兼容性**
   - 大多数预训练模型输出维度≤1024
   - 常见维度：384(MiniLM)、768(BERT)、1024(大模型)

4. **检索精度**
   - 高维度可能导致"维度诅咒"
   - 相似度区分度下降

### 调整建议

#### 可以适当提高的情况
- **硬件充足**：64GB+内存，高端GPU
- **数据量大**：百万级文档需要更高维度
- **精度要求高**：专业领域需要细粒度区分

#### 建议的维度设置

| 应用场景 | 推荐维度 | 理由 |
|----------|----------|------|
| **快速原型** | 384 | 平衡效果和速度 |
| **生产环境** | 768 | 较好的语义表达能力 |
| **高精度需求** | 1024 | 最大化语义信息 |
| **大规模部署** | 512 | 平衡精度和资源消耗 |

#### 代码调整方案

如需提高维度限制，可修改：

```python
# 将最大值从1024提高到2048或4096
self.vector_dim_spin.setRange(64, 2048)
```

**注意事项**：
- 需要确保有足够的系统资源
- 建议先在小数据集上测试
- 监控内存和GPU使用情况
- 考虑使用降维技术优化存储
