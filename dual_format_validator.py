#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
双格式文档校核工具
独立运行的PDF和MD文件校核系统
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from dual_format_processor import DualFormatProcessor

def setup_logging(log_level='INFO'):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('dual_format_validation.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='双格式文档校核工具')
    parser.add_argument('--base-dir', '-d', required=True, 
                       help='基础文档目录路径（PDF目录）')
    parser.add_argument('--output-dir', '-o', default='data/dual_format_reports',
                       help='输出报告目录')
    parser.add_argument('--log-level', '-l', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    parser.add_argument('--min-similarity', '-s', type=float, default=0.7,
                       help='最小相似度阈值')
    parser.add_argument('--min-completeness', '-c', type=float, default=0.8,
                       help='最小完整性阈值')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 60)
    logger.info("双格式文档校核工具启动")
    logger.info("=" * 60)
    
    try:
        # 验证输入目录
        base_dir = Path(args.base_dir)
        if not base_dir.exists():
            logger.error(f"基础目录不存在: {base_dir}")
            return 1
        
        # 配置处理器
        config = {
            'output_dir': args.output_dir,
            'quality_thresholds': {
                'min_content_length': 100,
                'min_similarity': args.min_similarity,
                'max_error_rate': 0.1,
                'min_completeness': args.min_completeness
            }
        }
        
        # 创建处理器
        processor = DualFormatProcessor(config)
        
        # 执行校核
        logger.info(f"开始处理目录: {base_dir}")
        results = processor.process_dual_format_documents(str(base_dir))
        
        # 输出结果
        logger.info("=" * 60)
        logger.info("校核结果汇总:")
        logger.info("=" * 60)
        logger.info(f"总文档对数: {results['total_pairs']}")
        logger.info(f"已处理: {results['processed']}")
        logger.info(f"PDF有效: {results['pdf_valid']}")
        logger.info(f"MD有效: {results['md_valid']}")
        logger.info(f"双格式都有效: {results['both_valid']}")
        logger.info(f"相似度通过: {results['similarity_passed']}")
        
        if results['errors']:
            logger.warning(f"错误数量: {len(results['errors'])}")
            for error in results['errors'][:5]:  # 只显示前5个错误
                logger.warning(f"  - {error}")
        
        # 计算成功率
        if results['total_pairs'] > 0:
            success_rate = results['both_valid'] / results['total_pairs'] * 100
            similarity_rate = results['similarity_passed'] / results['total_pairs'] * 100
            
            logger.info(f"双格式有效率: {success_rate:.2f}%")
            logger.info(f"相似度通过率: {similarity_rate:.2f}%")
        
        logger.info(f"详细报告已保存到: {processor.report_file}")
        
        return 0
        
    except Exception as e:
        logger.error(f"校核过程中出现错误: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
