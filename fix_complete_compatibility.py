#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整的兼容性修复方案
解决 sentence_transformers 和相关依赖的兼容性问题
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_pip_command(command):
    """运行 pip 命令"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            logger.info(f"✓ 成功执行: {command}")
            return True
        else:
            logger.error(f"✗ 执行失败: {command}")
            logger.error(f"错误信息: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"✗ 执行命令时出错: {e}")
        return False

def test_import(module_name):
    """测试模块导入"""
    try:
        __import__(module_name)
        logger.info(f"✓ {module_name} 导入成功")
        return True
    except ImportError as e:
        logger.warning(f"✗ {module_name} 导入失败: {e}")
        return False

def main():
    print("=" * 60)
    print("完整的兼容性修复方案")
    print("=" * 60)
    
    # 方案1: 升级到最新兼容版本
    print("\n方案1: 升级到最新兼容版本组合")
    print("-" * 40)
    
    # 卸载可能冲突的包
    packages_to_uninstall = [
        "sentence-transformers",
        "transformers", 
        "huggingface-hub",
        "tokenizers",
        "accelerate"
    ]
    
    for package in packages_to_uninstall:
        logger.info(f"卸载 {package}...")
        run_pip_command(f"pip uninstall {package} -y")
    
    # 安装最新兼容版本
    compatible_packages = [
        "huggingface-hub>=0.20.0",
        "transformers>=4.30.0",
        "tokenizers>=0.13.0",
        "sentence-transformers>=2.3.0",
        "accelerate>=0.20.0"
    ]

    success = True
    for package in compatible_packages:
        logger.info(f"安装 {package}...")
        if not run_pip_command(f'pip install "{package}"'):
            success = False
            break
    
    if success:
        print("\n测试方案1结果...")
        if test_import("sentence_transformers"):
            print("🎉 方案1成功！sentence_transformers 现在可以正常使用了！")
            return True
    
    print("\n方案1失败，尝试方案2...")
    
    # 方案2: 使用特定的稳定版本组合
    print("\n方案2: 使用特定的稳定版本组合")
    print("-" * 40)
    
    # 再次卸载
    for package in packages_to_uninstall:
        run_pip_command(f"pip uninstall {package} -y")
    
    # 安装特定稳定版本
    stable_packages = [
        "huggingface-hub==0.19.4",
        "transformers==4.35.2", 
        "tokenizers==0.15.0",
        "sentence-transformers==2.2.2",
        "accelerate==0.24.1"
    ]
    
    success = True
    for package in stable_packages:
        logger.info(f"安装 {package}...")
        if not run_pip_command(f'pip install "{package}"'):
            success = False
            break
    
    if success:
        print("\n测试方案2结果...")
        if test_import("sentence_transformers"):
            print("🎉 方案2成功！sentence_transformers 现在可以正常使用了！")
            return True
    
    print("\n方案2失败，尝试方案3...")
    
    # 方案3: 最小化安装，只保留核心功能
    print("\n方案3: 最小化安装")
    print("-" * 40)
    
    # 卸载所有相关包
    for package in packages_to_uninstall:
        run_pip_command(f"pip uninstall {package} -y")
    
    # 只安装核心包
    core_packages = [
        "transformers==4.21.3",
        "huggingface-hub==0.16.4",
        "tokenizers==0.12.1"
    ]
    
    success = True
    for package in core_packages:
        logger.info(f"安装 {package}...")
        if not run_pip_command(f'pip install "{package}"'):
            success = False
            break
    
    if success:
        print("\n测试方案3结果...")
        if test_import("transformers"):
            print("✓ 方案3成功！transformers 库可以正常使用")
            print("注意: sentence_transformers 不可用，但系统会自动使用 transformers 作为替代")
            return True
    
    print("\n❌ 所有方案都失败了")
    print("建议手动检查依赖冲突或联系技术支持")
    return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n发生未预期的错误: {e}")
