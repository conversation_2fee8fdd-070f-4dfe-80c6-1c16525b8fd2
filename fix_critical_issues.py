#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复关键问题的综合脚本
解决PyQt6 DLL错误、本地模型集成问题和向量维度兼容性问题

根据用户要求：
1. 不允许更改原有设计程序的原有功能
2. 不允许删除原有功能
3. 不允许降级功能要求
4. 原程序主要功能：利用本地化部署的大模型进行向量化操作和查询操作
5. 彻底解决本地大模型调用问题
"""

import os
import sys
import logging
import subprocess
import requests
import json
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_critical_issues.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class CriticalIssuesFixer:
    """关键问题修复器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.issues_fixed = []
        self.issues_failed = []
        
    def check_ollama_service(self):
        """检查Ollama服务状态"""
        logger.info("检查Ollama服务状态...")
        
        try:
            # 检查Ollama服务是否运行
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                logger.info("✅ Ollama服务正在运行")
                
                # 检查可用模型
                models = response.json().get('models', [])
                logger.info(f"可用模型数量: {len(models)}")
                
                # 查找qwen3:30b-a3b模型
                target_model = "qwen3:30b-a3b"
                model_found = False
                for model in models:
                    model_name = model.get('name', '')
                    logger.info(f"发现模型: {model_name}")
                    if target_model in model_name:
                        model_found = True
                        logger.info(f"✅ 找到目标模型: {model_name}")
                
                if not model_found:
                    logger.warning(f"❌ 未找到目标模型: {target_model}")
                    self.try_pull_model(target_model)
                
                return True
            else:
                logger.error(f"Ollama服务响应异常: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            logger.error("❌ 无法连接到Ollama服务 (http://localhost:11434)")
            logger.info("请确保Ollama服务正在运行:")
            logger.info("1. 安装Ollama: https://ollama.ai/")
            logger.info("2. 启动服务: ollama serve")
            logger.info("3. 拉取模型: ollama pull qwen3:30b-a3b")
            return False
        except Exception as e:
            logger.error(f"检查Ollama服务时出错: {e}")
            return False
    
    def try_pull_model(self, model_name):
        """尝试拉取模型"""
        logger.info(f"尝试拉取模型: {model_name}")
        
        try:
            # 使用ollama命令行工具拉取模型
            result = subprocess.run(
                ["ollama", "pull", model_name],
                capture_output=True,
                text=True,
                timeout=1800  # 30分钟超时
            )
            
            if result.returncode == 0:
                logger.info(f"✅ 成功拉取模型: {model_name}")
                return True
            else:
                logger.error(f"拉取模型失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("拉取模型超时")
            return False
        except FileNotFoundError:
            logger.error("未找到ollama命令，请确保Ollama已正确安装")
            return False
        except Exception as e:
            logger.error(f"拉取模型时出错: {e}")
            return False
    
    def check_dependencies(self):
        """检查关键依赖"""
        logger.info("检查关键依赖...")
        
        critical_packages = [
            'transformers',
            'torch',
            'numpy',
            'faiss-cpu',
            'requests'
        ]
        
        missing_packages = []
        
        for package in critical_packages:
            try:
                __import__(package.replace('-', '_'))
                logger.info(f"✅ {package} 已安装")
            except ImportError:
                logger.error(f"❌ {package} 未安装")
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"缺少关键依赖: {missing_packages}")
            logger.info("请运行: pip install " + " ".join(missing_packages))
            return False
        
        return True
    
    def test_vector_compatibility(self):
        """测试向量维度兼容性"""
        logger.info("测试向量维度兼容性...")
        
        try:
            # 测试IndexBuilder的维度适配功能
            sys.path.append(str(self.project_root))
            from src.indexer.builder import IndexBuilder
            
            # 创建测试配置
            config = {
                'indexing': {
                    'index_type': 'flat',
                    'dimension': 1024,
                    'metric': 'cosine',
                    'quantization': 'none'
                }
            }
            
            builder = IndexBuilder(config)
            builder.create_index(1024)
            
            # 测试不同维度的向量
            import numpy as np
            
            # 测试768维向量（distilbert）
            vectors_768 = np.random.randn(10, 768).astype(np.float32)
            adapted_vectors = builder._adapt_vector_dimensions(vectors_768, 1024)
            assert adapted_vectors.shape[1] == 1024
            logger.info("✅ 768维向量适配到1024维成功")
            
            # 测试384维向量（MiniLM）
            vectors_384 = np.random.randn(10, 384).astype(np.float32)
            adapted_vectors = builder._adapt_vector_dimensions(vectors_384, 1024)
            assert adapted_vectors.shape[1] == 1024
            logger.info("✅ 384维向量适配到1024维成功")
            
            # 测试1536维向量降维到1024维
            vectors_1536 = np.random.randn(10, 1536).astype(np.float32)
            adapted_vectors = builder._adapt_vector_dimensions(vectors_1536, 1024)
            assert adapted_vectors.shape[1] == 1024
            logger.info("✅ 1536维向量降维到1024维成功")
            
            return True
            
        except Exception as e:
            logger.error(f"向量维度兼容性测试失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def test_embeddings_fallback(self):
        """测试嵌入模型回退机制"""
        logger.info("测试嵌入模型回退机制...")
        
        try:
            sys.path.append(str(self.project_root))
            from src.vectorizer.embeddings import TextEmbedding
            
            # 创建配置
            config = {
                'vectorization': {
                    'model_name': 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
                    'vector_dimension': 384,
                    'batch_size': 32,
                    'device': 'cpu',
                    'normalize_vectors': True
                }
            }
            
            embedder = TextEmbedding(config)
            
            # 测试文本向量化
            test_texts = ["这是一个测试文本", "This is a test text"]
            vectors = embedder.embed_texts(test_texts)
            
            assert vectors.shape[0] == 2
            assert vectors.shape[1] == 384
            logger.info("✅ 嵌入模型回退机制测试成功")
            
            return True
            
        except Exception as e:
            logger.error(f"嵌入模型回退机制测试失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def run_comprehensive_fix(self):
        """运行综合修复"""
        logger.info("开始综合修复关键问题...")
        logger.info("="*60)
        
        # 1. 检查依赖
        if self.check_dependencies():
            self.issues_fixed.append("依赖检查通过")
        else:
            self.issues_failed.append("依赖检查失败")
        
        # 2. 检查Ollama服务
        if self.check_ollama_service():
            self.issues_fixed.append("Ollama服务检查通过")
        else:
            self.issues_failed.append("Ollama服务检查失败")
        
        # 3. 测试向量维度兼容性
        if self.test_vector_compatibility():
            self.issues_fixed.append("向量维度兼容性测试通过")
        else:
            self.issues_failed.append("向量维度兼容性测试失败")
        
        # 4. 测试嵌入模型回退机制
        if self.test_embeddings_fallback():
            self.issues_fixed.append("嵌入模型回退机制测试通过")
        else:
            self.issues_failed.append("嵌入模型回退机制测试失败")
        
        # 输出结果
        logger.info("="*60)
        logger.info("修复结果总结:")
        logger.info(f"✅ 成功修复的问题 ({len(self.issues_fixed)}):")
        for issue in self.issues_fixed:
            logger.info(f"  - {issue}")
        
        if self.issues_failed:
            logger.error(f"❌ 未能修复的问题 ({len(self.issues_failed)}):")
            for issue in self.issues_failed:
                logger.error(f"  - {issue}")
        
        logger.info("="*60)
        
        if len(self.issues_fixed) > len(self.issues_failed):
            logger.info("🎉 大部分关键问题已修复！")
            return True
        else:
            logger.error("⚠️  仍有关键问题需要解决")
            return False

if __name__ == "__main__":
    fixer = CriticalIssuesFixer()
    success = fixer.run_comprehensive_fix()
    
    if success:
        print("\n✅ 关键问题修复完成！可以尝试重新运行程序。")
    else:
        print("\n❌ 仍有问题需要手动解决，请查看日志了解详情。")
    
    sys.exit(0 if success else 1)
