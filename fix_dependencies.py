#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复依赖版本兼容性问题的脚本
解决 "unhashable type: 'list'" 错误
"""

import subprocess
import sys
import os

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def fix_dependencies():
    """修复依赖版本问题"""
    print("开始修复依赖版本兼容性问题...")
    
    # 需要降级的包和目标版本
    packages_to_fix = [
        "aiohttp==3.8.6",  # 与 Python 3.9 兼容的版本
        "datasets==2.12.0",  # 您指定的版本
        "sentence-transformers==2.2.0",  # 稳定版本
        "typing-extensions==4.8.0",  # 兼容版本
        "multidict==6.0.4",  # aiohttp 依赖的兼容版本
        "yarl==1.9.4",  # aiohttp 依赖的兼容版本
    ]
    
    print("正在卸载可能有问题的包...")
    packages_to_uninstall = ["aiohttp", "datasets", "sentence-transformers", "multidict", "yarl"]
    
    for package in packages_to_uninstall:
        print(f"卸载 {package}...")
        success, stdout, stderr = run_command(f"pip uninstall {package} -y")
        if success:
            print(f"✓ 成功卸载 {package}")
        else:
            print(f"⚠ 卸载 {package} 时出现警告: {stderr}")
    
    print("\n正在安装兼容版本的包...")
    for package in packages_to_fix:
        print(f"安装 {package}...")
        success, stdout, stderr = run_command(f"pip install {package}")
        if success:
            print(f"✓ 成功安装 {package}")
        else:
            print(f"✗ 安装 {package} 失败: {stderr}")
            return False
    
    print("\n正在验证修复结果...")
    test_imports = [
        "import aiohttp; print('aiohttp version:', aiohttp.__version__)",
        "import datasets; print('datasets version:', datasets.__version__)",
        "from sentence_transformers import SentenceTransformer; print('sentence-transformers 导入成功')"
    ]
    
    for test_import in test_imports:
        print(f"测试: {test_import.split(';')[0]}...")
        success, stdout, stderr = run_command(f'python -c "{test_import}"')
        if success:
            print(f"✓ {stdout.strip()}")
        else:
            print(f"✗ 导入失败: {stderr}")
    
    return True

def test_vectorizer_import():
    """测试向量化模块导入"""
    print("\n测试向量化模块导入...")
    test_command = 'python -c "from src.vectorizer import TextEmbedding; print(\'TextEmbedding 导入成功\')"'
    success, stdout, stderr = run_command(test_command)
    
    if success:
        print("✓ 向量化模块导入成功！")
        return True
    else:
        print(f"✗ 向量化模块导入失败: {stderr}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("修复 'unhashable type: list' 错误")
    print("=" * 60)
    
    # 检查是否在虚拟环境中
    if not os.path.exists("venv"):
        print("错误: 未找到 venv 文件夹，请确保在项目根目录运行此脚本")
        sys.exit(1)
    
    # 修复依赖
    if fix_dependencies():
        print("\n" + "=" * 60)
        print("依赖修复完成！")
        print("=" * 60)
        
        # 测试向量化模块
        if test_vectorizer_import():
            print("\n🎉 所有问题已修复！现在可以正常使用向量化功能了。")
        else:
            print("\n⚠ 依赖已修复，但向量化模块仍有问题，请检查代码。")
    else:
        print("\n❌ 依赖修复失败，请手动检查错误信息。")
