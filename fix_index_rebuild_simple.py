#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

import h5py
import pickle
import numpy as np
import logging
import faiss

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def extract_all_vectors():
    """提取HDF5中的所有向量"""
    logger = logging.getLogger(__name__)

    vector_file = Path("data/vectors/vectors.h5")
    if not vector_file.exists():
        logger.error("向量文件不存在")
        return None, None, None

    all_vectors = []
    all_doc_ids = []

    logger.info("从HDF5提取向量...")

    try:
        with h5py.File(vector_file, 'r') as f:
            logger.info(f"HDF5文件包含 {len(f.keys())} 个数据集")

            # 获取向量维度
            vector_dim = f.attrs.get('vector_dim', 384)
            logger.info(f"向量维度: {vector_dim}")

            # 获取所有数据集名称
            dataset_names = list(f.keys())

            # 找到所有ids_开头的数据集
            ids_datasets = sorted([name for name in dataset_names if name.startswith('ids_')])
            logger.info(f"找到 {len(ids_datasets)} 个ID数据集")

            for ids_key in ids_datasets:
                try:
                    # 获取文档ID数据集
                    ids_dataset = f[ids_key]

                    # 检查是否为标量数据集（单个值）
                    if ids_dataset.shape == ():
                        # 标量数据集，读取单个值
                        doc_id = ids_dataset[()]
                        if isinstance(doc_id, bytes):
                            doc_id = doc_id.decode('utf-8')
                        doc_ids = [str(doc_id)]
                        num_vectors = 1
                    else:
                        # 数组数据集，读取所有值
                        ids_data = ids_dataset[:]
                        doc_ids = []
                        for doc_id in ids_data:
                            if isinstance(doc_id, bytes):
                                doc_ids.append(doc_id.decode('utf-8'))
                            else:
                                doc_ids.append(str(doc_id))
                        num_vectors = len(doc_ids)

                    # 获取对应的压缩向量数据集
                    compressed_key = ids_key.replace('ids_', 'compressed_')
                    if compressed_key in f:
                        compressed_dataset = f[compressed_key]

                        # 读取压缩数据
                        if hasattr(compressed_dataset, 'shape') and compressed_dataset.shape == ():
                            # 标量数据集，包含压缩的字节数据
                            compressed_bytes = compressed_dataset[()].tobytes()
                        else:
                            # 数组数据集
                            compressed_bytes = compressed_dataset[:].tobytes()

                        # 解压缩数据
                        import zlib
                        try:
                            decompressed_bytes = zlib.decompress(compressed_bytes)
                            decompressed = np.frombuffer(decompressed_bytes, dtype=np.float32)

                            # 重塑为向量矩阵
                            expected_size = num_vectors * vector_dim

                            if len(decompressed) == expected_size:
                                vectors = decompressed.reshape(num_vectors, vector_dim)
                                all_vectors.append(vectors)
                                all_doc_ids.extend(doc_ids)
                                logger.info(f"提取数据集 {ids_key}: {num_vectors} 个向量")
                            else:
                                logger.warning(f"数据集 {ids_key} 数据大小不匹配: 期望{expected_size}, 实际{len(decompressed)}")
                        except zlib.error as ze:
                            logger.error(f"解压缩数据集 {ids_key} 失败: {ze}")
                            continue
                    else:
                        logger.warning(f"未找到对应的压缩数据集: {compressed_key}")

                except Exception as e:
                    logger.error(f"处理数据集 {ids_key} 失败: {e}")
                    continue

            if all_vectors:
                combined_vectors = np.vstack(all_vectors)
                logger.info(f"总共提取 {len(combined_vectors)} 个向量，维度: {combined_vectors.shape[1]}")
                return combined_vectors, all_doc_ids, vector_dim
            else:
                logger.error("未提取到任何向量")
                return None, None, None

    except Exception as e:
        logger.error(f"读取HDF5文件失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def create_simple_index(vectors, doc_ids, vector_dim):
    """创建简单的FAISS索引"""
    logger = logging.getLogger(__name__)
    
    try:
        # 删除旧文件
        index_path = Path("data/indices/auto_standards_test.idx")
        meta_path = Path("data/indices/auto_standards_test.meta")
        
        if index_path.exists():
            index_path.unlink()
            logger.info("删除旧索引文件")
        
        if meta_path.exists():
            meta_path.unlink()
            logger.info("删除旧元数据文件")
        
        # 创建FAISS索引
        logger.info("创建FAISS索引...")
        index = faiss.IndexHNSWFlat(vector_dim, 32)
        index.metric_type = faiss.METRIC_INNER_PRODUCT  # cosine相似度
        
        # 归一化向量（用于cosine相似度）
        faiss.normalize_L2(vectors)
        
        # 添加向量
        logger.info(f"添加 {len(vectors)} 个向量到索引...")
        index.add(vectors)
        
        # 保存索引
        logger.info("保存索引文件...")
        faiss.write_index(index, str(index_path))
        
        # 创建元数据
        metadata = {
            'index_type': 'hnsw',
            'dimension': vector_dim,
            'metric': 'cosine',
            'total_vectors': len(vectors),
            'is_trained': True,
            'doc_ids': doc_ids,
            'creation_time': str(Path().cwd()),
            'quantization': 'none',
            'hnsw_m': 32,
            'hnsw_ef_construction': 200,
            'hnsw_ef_search': 50
        }
        
        # 保存元数据
        logger.info("保存元数据文件...")
        with open(meta_path, 'wb') as f:
            pickle.dump(metadata, f)
        
        # 验证
        logger.info("验证索引...")
        test_index = faiss.read_index(str(index_path))
        logger.info(f"索引验证成功: {test_index.ntotal} 个向量")
        
        # 验证元数据
        with open(meta_path, 'rb') as f:
            test_metadata = pickle.load(f)
        logger.info(f"元数据验证成功: {test_metadata['total_vectors']} 个向量")
        
        return True
        
    except Exception as e:
        logger.error(f"创建索引失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search(vector_dim):
    """测试搜索功能"""
    logger = logging.getLogger(__name__)
    
    try:
        index_path = Path("data/indices/auto_standards_test.idx")
        meta_path = Path("data/indices/auto_standards_test.meta")
        
        # 加载索引
        index = faiss.read_index(str(index_path))
        
        # 加载元数据
        with open(meta_path, 'rb') as f:
            metadata = pickle.load(f)
        
        logger.info(f"测试搜索功能...")
        logger.info(f"  - 索引向量数: {index.ntotal}")
        logger.info(f"  - 元数据向量数: {metadata['total_vectors']}")
        
        # 创建测试向量
        test_vector = np.random.random(vector_dim).astype(np.float32)
        test_vector = test_vector.reshape(1, -1)
        faiss.normalize_L2(test_vector)
        
        # 搜索
        distances, indices = index.search(test_vector, 5)
        logger.info(f"  - 搜索返回 {len(indices[0])} 个结果")
        logger.info(f"  - 距离: {distances[0]}")
        
        return True
        
    except Exception as e:
        logger.error(f"搜索测试失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("=" * 60)
    logger.info("🔧 简单索引重建")
    logger.info("=" * 60)
    
    # 1. 提取向量
    vectors, doc_ids, vector_dim = extract_all_vectors()
    if vectors is None:
        logger.error("向量提取失败")
        return False
    
    # 2. 创建索引
    success = create_simple_index(vectors, doc_ids, vector_dim)
    if not success:
        logger.error("索引创建失败")
        return False
    
    # 3. 测试搜索
    test_success = test_search(vector_dim)
    if not test_success:
        logger.error("搜索测试失败")
        return False
    
    logger.info("=" * 60)
    logger.info("🎉 索引重建完成！")
    logger.info("=" * 60)
    logger.info("建议:")
    logger.info("1. 重启应用程序")
    logger.info("2. 选择auto_standards_test索引")
    logger.info("3. 测试搜索功能")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
