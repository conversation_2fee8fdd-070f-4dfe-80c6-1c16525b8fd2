#!/usr/bin/env python
# -*- coding: utf-8 -*-

import h5py
import pickle
import numpy as np
from pathlib import Path
import logging

def fix_auto_standards_test_sync():
    """修复auto_standards_test索引与HDF5向量数据的同步问题"""
    print("=" * 80)
    print("🔧 修复auto_standards_test索引同步问题")
    print("=" * 80)
    
    # 1. 统计HDF5中的实际向量数
    vector_file = Path("data/vectors/vectors.h5")
    if not vector_file.exists():
        print("❌ 向量文件不存在")
        return False
    
    total_vectors = 0
    vector_dimension = None
    
    try:
        with h5py.File(vector_file, 'r') as f:
            print(f"📁 HDF5文件: {vector_file.stat().st_size / 1024 / 1024:.2f} MB")
            print(f"📊 总组数: {len(f.keys())}")
            
            # 统计所有向量
            for key in f.keys():
                if key.startswith('ids_'):
                    ids_data = f[key]
                    count = len(ids_data)
                    total_vectors += count
                    
                    # 获取向量维度
                    if vector_dimension is None:
                        compressed_key = key.replace('ids_', 'compressed_')
                        if compressed_key in f:
                            # 从HDF5属性获取维度信息
                            if 'vector_dim' in f.attrs:
                                vector_dimension = f.attrs['vector_dim']
                            else:
                                # 默认768维（Ollama模型）
                                vector_dimension = 768
            
            print(f"✅ HDF5中总向量数: {total_vectors}")
            print(f"✅ 向量维度: {vector_dimension}")
            
    except Exception as e:
        print(f"❌ 读取HDF5文件失败: {e}")
        return False
    
    # 2. 更新auto_standards_test索引元数据
    meta_file = Path("data/indices/auto_standards_test.meta")
    
    if not meta_file.exists():
        print("❌ auto_standards_test.meta 文件不存在")
        return False
    
    try:
        # 读取现有元数据
        with open(meta_file, 'rb') as f:
            metadata = pickle.load(f)
        
        print(f"📋 当前索引元数据:")
        print(f"  - 向量数: {metadata.get('total_vectors', 0)}")
        print(f"  - 维度: {metadata.get('dimension', 0)}")
        print(f"  - 已训练: {metadata.get('is_trained', False)}")
        
        # 更新元数据
        metadata['total_vectors'] = total_vectors
        metadata['dimension'] = vector_dimension or metadata.get('dimension', 768)
        metadata['is_trained'] = True
        metadata['last_update_time'] = str(Path(vector_file).stat().st_mtime)
        
        # 保存更新的元数据
        with open(meta_file, 'wb') as f:
            pickle.dump(metadata, f)
        
        print(f"✅ 索引元数据已更新:")
        print(f"  - 新向量数: {metadata['total_vectors']}")
        print(f"  - 维度: {metadata['dimension']}")
        print(f"  - 已训练: {metadata['is_trained']}")
        
    except Exception as e:
        print(f"❌ 更新元数据失败: {e}")
        return False
    
    # 3. 验证修复结果
    print(f"\n🔍 验证修复结果:")
    
    try:
        # 重新读取元数据验证
        with open(meta_file, 'rb') as f:
            updated_metadata = pickle.load(f)
        
        hdf5_vectors = total_vectors
        index_vectors = updated_metadata['total_vectors']
        
        if hdf5_vectors == index_vectors:
            print(f"✅ 同步成功: HDF5({hdf5_vectors}) = 索引({index_vectors})")
            return True
        else:
            print(f"❌ 同步失败: HDF5({hdf5_vectors}) ≠ 索引({index_vectors})")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def check_all_indices():
    """检查所有索引的状态"""
    print(f"\n📊 检查所有索引状态:")
    
    indices_dir = Path("data/indices")
    for idx_file in indices_dir.glob("*.idx"):
        meta_file = idx_file.with_suffix('.meta')
        if meta_file.exists():
            try:
                with open(meta_file, 'rb') as f:
                    metadata = pickle.load(f)
                
                index_name = idx_file.stem
                total_vectors = metadata.get('total_vectors', 0)
                dimension = metadata.get('dimension', 0)
                size_kb = idx_file.stat().st_size / 1024
                
                status = "✅" if total_vectors > 0 and size_kb > 1 else "❌"
                print(f"  {status} {index_name}: {total_vectors} 个向量 ({dimension}维, {size_kb:.1f}KB)")
                
            except Exception as e:
                print(f"  ❌ {idx_file.stem}: 读取失败 - {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    print("开始修复索引同步问题...")
    success = fix_auto_standards_test_sync()
    
    if success:
        print("\n🎉 修复完成！auto_standards_test索引已与HDF5数据同步")
    else:
        print("\n❌ 修复失败，请检查错误信息")
    
    # 检查所有索引状态
    check_all_indices()
    
    print(f"\n💡 建议:")
    print(f"  1. 重启应用程序以加载更新的索引")
    print(f"  2. 在搜索界面选择auto_standards_test索引")
    print(f"  3. 测试搜索功能是否正常工作")
