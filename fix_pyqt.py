import sys
import os
import subprocess
import logging
import winreg
from pathlib import Path

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(cmd):
    """运行命令并返回输出"""
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {e.stderr}")
        return None

def check_vcredist():
    """检查Visual C++ Redistributable安装状态"""
    try:
        key_path = r"SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64"
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, 
                           winreg.KEY_READ | winreg.KEY_WOW64_64KEY) as key:
            version = winreg.QueryValueEx(key, "Version")[0]
            logger.info(f"找到Visual C++ Redistributable版本: {version}")
            return True
    except WindowsError:
        logger.error("未找到Visual C++ Redistributable")
        return False

def setup_qt_environment():
    """设置Qt环境变量"""
    python_path = Path(sys.executable)
    qt_bin_path = python_path.parent / "Lib" / "site-packages" / "PyQt6" / "Qt6" / "bin"
    
    if qt_bin_path.exists():
        # 添加Qt bin目录到PATH
        current_path = os.environ.get('PATH', '')
        if str(qt_bin_path) not in current_path:
            os.environ['PATH'] = f"{qt_bin_path};{current_path}"
            logger.info(f"已添加Qt bin目录到PATH: {qt_bin_path}")
    else:
        logger.error(f"Qt bin目录不存在: {qt_bin_path}")

def repair_qt():
    """修复PyQt安装"""
    logger.info("开始修复PyQt...")
    
    # 1. 检查VC++
    if not check_vcredist():
        logger.error("请安装Visual C++ Redistributable 2015-2022")
        logger.info("下载地址: https://aka.ms/vs/17/release/vc_redist.x64.exe")
        return False
    
    # 2. 设置环境变量
    setup_qt_environment()
    
    # 3. 重新安装PyQt
    commands = [
        [sys.executable, "-m", "pip", "uninstall", "-y", "PyQt6", "PyQt6-Qt6", "PyQt6-sip"],
        [sys.executable, "-m", "pip", "cache", "purge"],
        [sys.executable, "-m", "pip", "install", "--no-cache-dir", "PyQt6==6.9.0"],
        [sys.executable, "-m", "pip", "install", "--no-cache-dir", "PyQt6-Qt6==6.9.1"],
        [sys.executable, "-m", "pip", "install", "--no-cache-dir", "PyQt6-sip==13.10.2"]
    ]
    
    for cmd in commands:
        try:
            logger.info(f"执行命令: {' '.join(cmd)}")
            subprocess.run(cmd, check=True)
        except subprocess.CalledProcessError as e:
            logger.error(f"命令执行失败: {e}")
            return False
    
    return True

if __name__ == "__main__":
    if repair_qt():
        print("\n修复完成，请重新运行测试脚本。")
    else:
        print("\n修复失败，请检查上述错误信息。")