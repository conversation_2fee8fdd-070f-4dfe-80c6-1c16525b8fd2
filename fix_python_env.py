import os
import sys
import winreg
import logging
from pathlib import Path
import subprocess

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def remove_path_from_registry(paths_to_remove):
    """从注册表中删除Python路径"""
    try:
        # 打开用户环境变量键
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                            "Environment", 
                            0, 
                            winreg.KEY_ALL_ACCESS)
        
        # 获取当前PATH值
        path_value = winreg.QueryValueEx(key, "PATH")[0]
        
        # 分割PATH为列表
        paths = path_value.split(os.pathsep)
        
        # 过滤出要保留的路径
        new_paths = [p for p in paths if not any(
            remove_path.lower() in p.lower() 
            for remove_path in paths_to_remove
        )]
        
        # 重新组合PATH
        new_path_value = os.pathsep.join(new_paths)
        
        # 更新注册表
        winreg.SetValueEx(key, "PATH", 0, winreg.REG_EXPAND_SZ, new_path_value)
        
        winreg.CloseKey(key)
        logger.info("成功从注册表中删除指定的Python路径")
        return True
    except Exception as e:
        logger.error(f"修改注册表失败: {e}")
        return False

def update_system_path():
    """更新系统PATH"""
    # 要删除的路径
    paths_to_remove = [
        r"D:\msys64\mingw64\bin",
        r"C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps"
    ]
    
    if remove_path_from_registry(paths_to_remove):
        logger.info("系统PATH已更新，需要重启命令提示符才能生效")
        
        # 创建批处理文件来设置正确的环境变量
        bat_content = '''@echo off
set PATH=D:\\Python;D:\\Python\\Scripts;%PATH%
set PYTHONPATH=D:\\Python
echo Python环境已设置
'''
        
        with open('set_python_env.bat', 'w') as f:
            f.write(bat_content)
        
        logger.info("已创建环境设置批处理文件: set_python_env.bat")
    else:
        logger.error("更新系统PATH失败")

def main():
    logger.info("开始修复Python环境...")
    update_system_path()
    logger.info("修复完成，请重启命令提示符")
    
if __name__ == "__main__":
    sys.exit(main())