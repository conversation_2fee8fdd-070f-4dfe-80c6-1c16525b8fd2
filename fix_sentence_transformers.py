#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复 sentence_transformers 和 huggingface_hub 兼容性问题
"""

import subprocess
import sys
import os

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, encoding='utf-8')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def fix_sentence_transformers():
    """修复 sentence_transformers 兼容性问题"""
    print("=" * 60)
    print("修复 sentence_transformers 和 huggingface_hub 兼容性问题")
    print("=" * 60)
    
    # 方案1: 降级 huggingface-hub 到兼容版本
    print("方案1: 降级 huggingface-hub 到与 sentence-transformers==2.2.0 兼容的版本")
    
    # sentence-transformers 2.2.0 兼容的 huggingface-hub 版本
    compatible_versions = [
        "huggingface-hub==0.14.1",  # 用户指定的版本
        "sentence-transformers==2.2.0",
    ]
    
    print("正在安装兼容版本...")
    for package in compatible_versions:
        print(f"安装 {package}...")
        success, stdout, stderr = run_command(f"pip install {package}")
        if success:
            print(f"✓ 成功安装 {package}")
        else:
            print(f"✗ 安装 {package} 失败: {stderr}")
            # 如果方案1失败，尝试方案2
            return try_alternative_solution()
    
    # 测试方案1
    print("\n测试方案1结果...")
    success, stdout, stderr = run_command('python -c "from sentence_transformers import SentenceTransformer; print(\'✓ sentence_transformers 导入成功\')"')
    if success:
        print("🎉 方案1成功！sentence_transformers 现在可以正常使用了。")
        return True
    else:
        print(f"⚠ 方案1失败: {stderr}")
        return try_alternative_solution()

def try_alternative_solution():
    """尝试替代解决方案"""
    print("\n" + "=" * 60)
    print("尝试方案2: 升级到兼容的新版本组合")
    print("=" * 60)
    
    # 方案2: 使用更新的兼容版本组合
    new_versions = [
        "huggingface-hub>=0.15.0,<0.20.0",
        "sentence-transformers>=2.2.2,<2.3.0",
        "transformers>=4.21.0,<4.25.0",
    ]
    
    print("正在安装新版本组合...")
    for package in new_versions:
        print(f"安装 {package}...")
        success, stdout, stderr = run_command(f"pip install '{package}'")
        if success:
            print(f"✓ 成功安装 {package}")
        else:
            print(f"⚠ 安装 {package} 时出现问题: {stderr}")
    
    # 测试方案2
    print("\n测试方案2结果...")
    success, stdout, stderr = run_command('python -c "from sentence_transformers import SentenceTransformer; print(\'✓ sentence_transformers 导入成功\')"')
    if success:
        print("🎉 方案2成功！sentence_transformers 现在可以正常使用了。")
        return True
    else:
        print(f"⚠ 方案2也失败: {stderr}")
        return try_manual_patch()

def try_manual_patch():
    """尝试手动补丁方案"""
    print("\n" + "=" * 60)
    print("尝试方案3: 手动补丁修复")
    print("=" * 60)
    
    print("创建兼容性补丁...")
    
    # 创建一个兼容性补丁文件
    patch_content = '''
# 兼容性补丁：为旧版本 sentence_transformers 提供 cached_download 函数
import sys
from huggingface_hub import hf_hub_download

# 如果 cached_download 不存在，创建一个兼容的版本
try:
    from huggingface_hub import cached_download
except ImportError:
    def cached_download(url, cache_dir=None, force_download=False, **kwargs):
        """兼容性函数：模拟旧版本的 cached_download"""
        # 从 URL 中提取仓库和文件信息
        if 'huggingface.co' in url:
            parts = url.split('/')
            if len(parts) >= 6:
                repo_id = f"{parts[-3]}/{parts[-2]}"
                filename = parts[-1]
                return hf_hub_download(
                    repo_id=repo_id,
                    filename=filename,
                    cache_dir=cache_dir,
                    force_download=force_download,
                    **kwargs
                )
        # 如果无法解析，返回原始 URL
        return url
    
    # 将函数添加到 huggingface_hub 模块中
    import huggingface_hub
    huggingface_hub.cached_download = cached_download
    sys.modules['huggingface_hub'].cached_download = cached_download
'''
    
    # 保存补丁文件
    patch_file = "huggingface_hub_patch.py"
    with open(patch_file, 'w', encoding='utf-8') as f:
        f.write(patch_content)
    
    print(f"✓ 补丁文件已创建: {patch_file}")
    
    # 测试补丁
    print("测试补丁效果...")
    test_command = f'python -c "import {patch_file[:-3]}; from sentence_transformers import SentenceTransformer; print(\'✓ 补丁成功，sentence_transformers 可用\')"'
    success, stdout, stderr = run_command(test_command)
    
    if success:
        print("🎉 方案3成功！补丁已生效。")
        print(f"请在使用 sentence_transformers 之前先导入补丁: import {patch_file[:-3]}")
        return True
    else:
        print(f"✗ 方案3失败: {stderr}")
        return False

def test_final_result():
    """测试最终结果"""
    print("\n" + "=" * 60)
    print("最终测试")
    print("=" * 60)
    
    tests = [
        ("torch", "import torch; print('torch version:', torch.__version__)"),
        ("transformers", "from transformers import AutoTokenizer; print('transformers 可用')"),
        ("sentence_transformers", "from sentence_transformers import SentenceTransformer; print('sentence_transformers 可用')"),
        ("项目模块", "from src.vectorizer import TextEmbedding; print('TextEmbedding 可用')"),
    ]
    
    all_passed = True
    for name, test_cmd in tests:
        print(f"测试 {name}...")
        success, stdout, stderr = run_command(f'python -c "{test_cmd}"')
        if success:
            print(f"✓ {stdout.strip()}")
        else:
            print(f"✗ {name} 失败: {stderr}")
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    print("开始修复 sentence_transformers 兼容性问题...")
    
    if fix_sentence_transformers():
        if test_final_result():
            print("\n🎉 所有问题已解决！现在可以完整使用 sentence_transformers 功能了。")
        else:
            print("\n⚠ 部分功能仍有问题，但 sentence_transformers 基本可用。")
    else:
        print("\n❌ 无法完全修复兼容性问题。建议使用当前的降级方案（transformers 库）。")
