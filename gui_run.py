#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MD向量处理器GUI启动脚本
"""

import os
import sys
import time
import logging
import threading
import traceback
import shutil
from pathlib import Path

# 清理缓存
def clear_cache():
    """清理Python缓存以确保使用最新代码"""
    try:
        # 清理__pycache__目录
        cache_dirs = []
        for root, dirs, files in os.walk('.'):
            if '__pycache__' in dirs:
                cache_dir = os.path.join(root, '__pycache__')
                cache_dirs.append(cache_dir)

        for cache_dir in cache_dirs:
            try:
                shutil.rmtree(cache_dir)
            except:
                pass

        # 清理sys.modules中的项目模块
        modules_to_remove = []
        for module_name in list(sys.modules.keys()):
            if module_name.startswith('src.') or module_name == 'src':
                modules_to_remove.append(module_name)

        for module_name in modules_to_remove:
            try:
                del sys.modules[module_name]
            except:
                pass

        if cache_dirs or modules_to_remove:
            print(f"🧹 已清理 {len(cache_dirs)} 个缓存目录和 {len(modules_to_remove)} 个模块缓存")

    except Exception as e:
        print(f"缓存清理警告: {e}")

# 修复编码问题
def fix_encoding():
    """修复Python编码问题"""
    # 设置UTF-8模式
    os.environ["PYTHONUTF8"] = "1"

    # 在Windows上，禁用旧的Windows文件系统编码
    if sys.platform.startswith('win'):
        os.environ["PYTHONLEGACYWINDOWSFSENCODING"] = "0"

    # 确保sys.path包含正确的库路径
    python_lib = os.path.join(os.path.dirname(sys.executable), 'Lib')
    if python_lib not in sys.path and os.path.exists(python_lib):
        sys.path.insert(0, python_lib)
    
    return True

# 在导入任何其他模块前修复编码
fix_encoding()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gui.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("gui_launcher")

def check_hardware():
    """检查硬件配置并返回推荐设置"""
    import psutil
    import platform
    
    hardware_info = {
        "cpu_cores": psutil.cpu_count(logical=False),
        "cpu_threads": psutil.cpu_count(logical=True),
        "memory_total": psutil.virtual_memory().total / (1024**3),  # GB
        "platform": platform.system(),
        "python_version": platform.python_version()
    }
    
    # 检查GPU
    gpu_info = {"available": False}
    try:
        # 尝试检测CUDA
        import torch
        if torch.cuda.is_available():
            gpu_info["available"] = True
            gpu_info["count"] = torch.cuda.device_count()
            gpu_info["name"] = torch.cuda.get_device_name(0)
            gpu_info["memory"] = torch.cuda.get_device_properties(0).total_memory / (1024**3)  # GB
    except:
        pass
    
    # 如果torch检测失败，尝试使用nvidia-smi
    if not gpu_info["available"]:
        try:
            import subprocess
            result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total', '--format=csv,noheader,nounits'], 
                                   stdout=subprocess.PIPE, text=True)
            if result.returncode == 0:
                gpu_info["available"] = True
                name, memory = result.stdout.strip().split(',')
                gpu_info["name"] = name.strip()
                gpu_info["memory"] = float(memory.strip()) / 1024  # GB
        except:
            pass
    
    hardware_info["gpu"] = gpu_info
    
    # 生成推荐设置
    recommendations = {}
    
    # CPU线程数推荐
    recommendations["num_workers"] = min(16, max(1, hardware_info["cpu_threads"] // 2))
    
    # 批处理大小推荐
    if gpu_info["available"]:
        if gpu_info.get("memory", 0) >= 16:
            recommendations["batch_size"] = 64
        elif gpu_info.get("memory", 0) >= 8:
            recommendations["batch_size"] = 32
        else:
            recommendations["batch_size"] = 16
    else:
        recommendations["batch_size"] = 8
    
    # 设备推荐
    recommendations["device"] = "cuda" if gpu_info["available"] else "cpu"
    
    # 内存使用推荐
    recommendations["memory_limit"] = min(48, max(4, int(hardware_info["memory_total"] * 0.7)))
    
    return hardware_info, recommendations

def optimize_config(recommendations):
    """根据硬件推荐优化配置文件"""
    config_path = Path("config/app_config.yaml")
    if not config_path.exists():
        logger.warning(f"配置文件不存在: {config_path}")
        return
    
    try:
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 备份原始配置
        backup_path = config_path.with_suffix('.yaml.bak')
        with open(backup_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f)
        
        # 更新配置
        if 'performance' not in config:
            config['performance'] = {}
        
        config['performance']['num_workers'] = recommendations["num_workers"]
        config['performance']['batch_size'] = recommendations["batch_size"]
        config['performance']['device'] = recommendations["device"]
        config['performance']['memory_limit_gb'] = recommendations["memory_limit"]
        
        # 保存更新后的配置
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f)
        
        logger.info(f"已根据硬件优化配置文件: {config_path}")
    except Exception as e:
        logger.error(f"优化配置文件时出错: {e}")

def setup_environment():
    """设置环境变量"""
    # 设置线程数
    os.environ["OMP_NUM_THREADS"] = "4"  # 限制OpenMP线程数
    os.environ["MKL_NUM_THREADS"] = "4"  # 限制MKL线程数
    
    # 设置CUDA环境变量
    os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
    
    # 设置PyTorch环境变量
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "max_split_size_mb:128"

from src.utils.import_checker import check_imports

def main():
    """主函数"""
    start_time = time.time()
    
    try:
        print("=" * 60)
        print("MD向量处理器GUI启动 - 高性能版本")
        print("=" * 60)

        # 清理缓存以确保使用最新代码
        clear_cache()

        # 检查必要的模块
        required_modules = {
            "numpy": "pip install numpy",
            "pandas": "pip install pandas",
            "torch": "pip install torch",
            "faiss": "pip install faiss-cpu or pip install faiss-gpu",
            "PyQt6": "pip install PyQt6",
        }
        
        if not check_imports(required_modules):
            print("请安装缺少的模块后重试")
            return
        
        # 检查硬件并优化配置
        print("\n检查硬件配置...")
        hardware_info, recommendations = check_hardware()
        
        print(f"CPU: {hardware_info['cpu_cores']}核心/{hardware_info['cpu_threads']}线程")
        print(f"内存: {hardware_info['memory_total']:.1f} GB")
        
        if hardware_info["gpu"]["available"]:
            print(f"GPU: {hardware_info['gpu'].get('name', 'Unknown')} ({hardware_info['gpu'].get('memory', 0):.1f} GB)")
        else:
            print("GPU: 未检测到")
        
        print("\n推荐设置:")
        print(f"工作线程数: {recommendations['num_workers']}")
        print(f"批处理大小: {recommendations['batch_size']}")
        print(f"计算设备: {recommendations['device']}")
        print(f"内存限制: {recommendations['memory_limit']} GB")
        
        # 优化配置
        print("\n优化配置...")
        optimize_config(recommendations)
        
        # 设置环境
        print("设置环境变量...")
        setup_environment()
        
        # 确保src目录在Python路径中
        current_dir = Path(__file__).parent
        if str(current_dir) not in sys.path:
            sys.path.insert(0, str(current_dir))

        print("\n检查 PyQt 安装情况...")
        try:
            try:
                import PyQt6
                print(f"找到 PyQt6，版本: {PyQt6.__version__ if hasattr(PyQt6, '__version__') else '未知'}")
                qt_version = 6
            except ImportError:
                print("未找到 PyQt6，尝试导入 PyQt5...")
                import PyQt5
                print(f"找到 PyQt5，版本: {PyQt5.__version__ if hasattr(PyQt5, '__version__') else '未知'}")
                qt_version = 5

            # 修改 src/gui 中的导入语句
            if qt_version == 5:
                print("检测到 PyQt5，修改导入语句...")
                gui_files = list(Path("src/gui").glob("**/*.py"))
                for file_path in gui_files:
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()

                    # 替换 PyQt6 为 PyQt5
                    if "PyQt6" in content:
                        content = content.replace("PyQt6", "PyQt5")
                        with open(file_path, "w", encoding="utf-8") as f:
                            f.write(content)
                        print(f"已修改: {file_path}")
        except Exception as e:
            print(f"检查 PyQt 安装情况时出错: {e}")
            traceback.print_exc()

        # 检查 QActionGroup 的位置
        print("\n检查 QActionGroup 的位置...")
        try:
            from PyQt6.QtWidgets import QActionGroup
            print("QActionGroup 在 PyQt6.QtWidgets 中")
        except ImportError:
            try:
                from PyQt6.QtGui import QActionGroup
                print("QActionGroup 在 PyQt6.QtGui 中")
            except ImportError:
                print("无法导入 QActionGroup")

        # 修复 app.py 中的 QActionGroup 导入
        try:
            print("修复 app.py 中的 QActionGroup 导入...")
            app_path = Path("src/gui/app.py")
            with open(app_path, "r", encoding="utf-8") as f:
                content = f.read()

            if "from PyQt6.QtWidgets import (" in content and "QActionGroup" in content:
                # 从 QtWidgets 移除 QActionGroup
                content = content.replace(
                    "    QToolBar, QSplitter, QTabWidget, QMessageBox, QFileDialog,\n    QActionGroup",
                    "    QToolBar, QSplitter, QTabWidget, QMessageBox, QFileDialog"
                )
                # 添加到 QtGui
                content = content.replace(
                    "from PyQt6.QtGui import QIcon, QPixmap, QFont, QAction",
                    "from PyQt6.QtGui import QIcon, QPixmap, QFont, QAction, QActionGroup"
                )
                with open(app_path, "w", encoding="utf-8") as f:
                    f.write(content)
                print("已修复 app.py 中的 QActionGroup 导入")
        except Exception as e:
            print(f"修复 app.py 中的 QActionGroup 导入时出错: {e}")

        # 修复 theme_manager.py 中的 QActionGroup 导入
        try:
            print("修复 theme_manager.py 中的 QActionGroup 导入...")
            theme_manager_path = Path("src/gui/theme/theme_manager.py")
            if theme_manager_path.exists():
                with open(theme_manager_path, "r", encoding="utf-8") as f:
                    content = f.read()

                if "from PyQt6.QtGui import QPalette, QColor" in content:
                    content = content.replace(
                        "from PyQt6.QtGui import QPalette, QColor",
                        "from PyQt6.QtGui import QPalette, QColor, QActionGroup"
                    )
                    with open(theme_manager_path, "w", encoding="utf-8") as f:
                        f.write(content)
                    print("已修复 theme_manager.py 中的 QActionGroup 导入")
        except Exception as e:
            print(f"修复 theme_manager.py 中的 QActionGroup 导入时出错: {e}")

        # 修复 translator.py 中的 set_language 方法
        try:
            print("修复 translator.py 中的 set_language 方法...")
            translator_path = Path("src/gui/i18n/translator.py")
            with open(translator_path, "r", encoding="utf-8") as f:
                content = f.read()

            if "self._notify_language_changed()" in content:
                content = content.replace(
                    "self._notify_language_changed()",
                    "# self._notify_language_changed() # 禁用观察者通知，避免可能的循环引用问题"
                )
                with open(translator_path, "w", encoding="utf-8") as f:
                    f.write(content)
                print("已修复 translator.py 中的 set_language 方法")
        except Exception as e:
            print(f"修复 translator.py 时出错: {e}")

        # 修复 app.py 中的 _on_language_changed 方法
        try:
            print("修复 app.py 中的 _on_language_changed 方法...")
            app_path = Path("src/gui/app.py")
            with open(app_path, "r", encoding="utf-8") as f:
                content = f.read()

            if "self.on_language_changed()" in content:
                content = content.replace(
                    "self.on_language_changed()",
                    "# self.on_language_changed() # 禁用语言变更回调，避免可能的循环引用问题"
                )
                with open(app_path, "w", encoding="utf-8") as f:
                    f.write(content)
                print("已修复 app.py 中的 _on_language_changed 方法")
        except Exception as e:
            print(f"修复 app.py 时出错: {e}")

        # 预加载资源
        print("\n预加载资源...")
        def preload_resources():
            try:
                # 预加载常用模块
                import numpy as np
                import pandas as pd
                import torch
                print("已预加载核心库")
                
                # 预加载项目模块
                from src.utils import helpers
                from src.storage import vector_store
                print("已预加载项目模块")
                
                return True
            except Exception as e:
                print(f"预加载资源时出错: {e}")
                return False
        
        preload_thread = threading.Thread(target=preload_resources)
        preload_thread.daemon = True
        preload_thread.start()
        preload_thread.join(timeout=5)  # 最多等待5秒

        # 尝试导入 src.gui
        print("\n导入GUI模块...")
        try:
            import src.gui
            print("成功导入 src.gui 模块")

            print("导入 run_gui 函数...")
            from src.gui import run_gui
            print("成功导入 run_gui 函数")

            # 计算启动时间
            elapsed = time.time() - start_time
            print(f"\n准备完成! 耗时: {elapsed:.2f}秒")
            print("=" * 60)
            print("正在启动GUI界面...")
            
            # 运行GUI
            run_gui()
            
        except Exception as e:
            print(f"导入或运行 GUI 时出错: {e}")
            traceback.print_exc()  # 这里使用了traceback模块

    except ImportError as e:
        logger.error(f"导入错误: {e}")
        print(f"错误: 无法导入必要的模块。请确保已安装所有依赖项:")
        print("pip install -r requirements-gui.txt")
        traceback.print_exc()  # 这里使用了traceback模块
        sys.exit(1)

    except Exception as e:
        logger.error(f"启动GUI时出错: {e}")
        print(f"错误: {e}")
        print("详细错误信息:")
        traceback.print_exc()  # 这里使用了traceback模块
        sys.exit(1)

if __name__ == "__main__":
    main()











