
# 兼容性补丁：为旧版本 sentence_transformers 提供 cached_download 函数
import sys
from huggingface_hub import hf_hub_download

# 如果 cached_download 不存在，创建一个兼容的版本
try:
    from huggingface_hub import cached_download
except ImportError:
    def cached_download(url, cache_dir=None, force_download=False, **kwargs):
        """兼容性函数：模拟旧版本的 cached_download"""
        # 从 URL 中提取仓库和文件信息
        if 'huggingface.co' in url:
            parts = url.split('/')
            if len(parts) >= 6:
                repo_id = f"{parts[-3]}/{parts[-2]}"
                filename = parts[-1]
                return hf_hub_download(
                    repo_id=repo_id,
                    filename=filename,
                    cache_dir=cache_dir,
                    force_download=force_download,
                    **kwargs
                )
        # 如果无法解析，返回原始 URL
        return url
    
    # 将函数添加到 huggingface_hub 模块中
    import huggingface_hub
    huggingface_hub.cached_download = cached_download
    sys.modules['huggingface_hub'].cached_download = cached_download
