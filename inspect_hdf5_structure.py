#!/usr/bin/env python
# -*- coding: utf-8 -*-

import h5py
import numpy as np
from pathlib import Path

def inspect_hdf5_structure():
    """详细检查HDF5文件结构"""
    vector_file = Path("data/vectors/vectors.h5")
    
    if not vector_file.exists():
        print("向量文件不存在")
        return
    
    print(f"HDF5文件大小: {vector_file.stat().st_size / 1024 / 1024:.2f} MB")
    
    with h5py.File(vector_file, 'r') as f:
        print(f"\n文件属性:")
        for attr_name, attr_value in f.attrs.items():
            print(f"  {attr_name}: {attr_value}")
        
        print(f"\n总组数: {len(f.keys())}")
        
        # 检查前几个组的结构
        keys = list(f.keys())
        print(f"\n前10个组:")
        for i, key in enumerate(keys[:10]):
            print(f"  {i+1}. {key}")
            item = f[key]
            
            if hasattr(item, 'keys'):
                # 这是一个组
                print(f"     类型: 组")
                print(f"     子项: {list(item.keys())}")
                for subkey in item.keys():
                    subitem = item[subkey]
                    if hasattr(subitem, 'shape'):
                        print(f"       {subkey}: shape={subitem.shape}, dtype={subitem.dtype}")
                    else:
                        print(f"       {subkey}: {type(subitem)}")
            else:
                # 这是一个数据集
                print(f"     类型: 数据集")
                print(f"     shape: {item.shape}")
                print(f"     dtype: {item.dtype}")
                
                # 如果是小数据集，显示内容
                if item.size < 10:
                    print(f"     内容: {item[:]}")
        
        # 检查ids_开头的组
        ids_keys = [k for k in keys if k.startswith('ids_')]
        print(f"\nids_开头的组数量: {len(ids_keys)}")
        
        if ids_keys:
            # 检查第一个ids组
            first_ids = ids_keys[0]
            print(f"\n检查第一个ids组: {first_ids}")
            ids_item = f[first_ids]
            
            print(f"  类型: {'组' if hasattr(ids_item, 'keys') else '数据集'}")
            
            if hasattr(ids_item, 'keys'):
                print(f"  子项: {list(ids_item.keys())}")
            else:
                print(f"  shape: {ids_item.shape}")
                print(f"  dtype: {ids_item.dtype}")
                print(f"  大小: {ids_item.size}")
                
                # 尝试读取数据
                try:
                    data = ids_item[()]  # 读取标量数据
                    print(f"  标量值: {data}")
                except:
                    try:
                        data = ids_item[:]  # 读取数组数据
                        print(f"  数组内容: {data}")
                    except Exception as e:
                        print(f"  读取失败: {e}")
        
        # 检查compressed_开头的组
        compressed_keys = [k for k in keys if k.startswith('compressed_')]
        print(f"\ncompressed_开头的组数量: {len(compressed_keys)}")
        
        if compressed_keys:
            # 检查第一个compressed组
            first_compressed = compressed_keys[0]
            print(f"\n检查第一个compressed组: {first_compressed}")
            compressed_item = f[first_compressed]
            
            print(f"  类型: {'组' if hasattr(compressed_item, 'keys') else '数据集'}")
            
            if hasattr(compressed_item, 'keys'):
                print(f"  子项: {list(compressed_item.keys())}")
            else:
                print(f"  shape: {compressed_item.shape}")
                print(f"  dtype: {compressed_item.dtype}")
                print(f"  大小: {compressed_item.size}")

if __name__ == "__main__":
    inspect_hdf5_structure()
