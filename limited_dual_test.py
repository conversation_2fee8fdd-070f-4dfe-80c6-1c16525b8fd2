#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
限制文件数量的双格式校对测试
"""

import os
import sys
from pathlib import Path

def main():
    print("=== 限制文件数量的双格式校对测试 ===")
    
    # 检查目录结构
    pdf_dir = "test_training_data/raw_documents/enterprise_standards"
    md_dir = "test_training_data/raw_documents_MD/enterprise_standards"
    
    print(f"PDF目录: {pdf_dir}")
    print(f"MD目录: {md_dir}")
    
    # 检查目录是否存在
    pdf_exists = os.path.exists(pdf_dir)
    md_exists = os.path.exists(md_dir)
    
    print(f"PDF目录存在: {pdf_exists}")
    print(f"MD目录存在: {md_exists}")
    
    if pdf_exists and md_exists:
        # 统计文件数量
        pdf_files = []
        md_files = []
        
        for root, dirs, files in os.walk(pdf_dir):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(root, file))
                    if len(pdf_files) >= 5:  # 限制为5个文件
                        break
            if len(pdf_files) >= 5:
                break
        
        for root, dirs, files in os.walk(md_dir):
            for file in files:
                if file.lower().endswith('.md'):
                    md_files.append(os.path.join(root, file))
                    if len(md_files) >= 5:  # 限制为5个文件
                        break
            if len(md_files) >= 5:
                break
        
        print(f"找到PDF文件: {len(pdf_files)} 个")
        print(f"找到MD文件: {len(md_files)} 个")
        
        # 显示前几个文件
        print("\nPDF文件示例:")
        for i, file in enumerate(pdf_files[:3]):
            print(f"  {i+1}. {file}")
        
        print("\nMD文件示例:")
        for i, file in enumerate(md_files[:3]):
            print(f"  {i+1}. {file}")
    
    # 尝试导入双格式处理器
    try:
        sys.path.insert(0, 'src')
        from dual_format_processor import DualFormatProcessor
        print("\n✅ 成功导入双格式处理器")
        
        # 创建处理器配置，限制处理文件数量
        config = {
            'output_dir': 'data/limited_test_reports',
            'quality_thresholds': {
                'min_content_length': 50,
                'min_similarity': 0.5,
                'max_error_rate': 0.2,
                'min_completeness': 0.5
            },
            'max_files_to_process': 3  # 限制处理文件数量
        }
        
        processor = DualFormatProcessor(config)
        print("✅ 成功创建处理器实例")
        
        if pdf_exists and md_exists:
            print("\n🔄 开始执行限制数量的双格式校对...")
            
            # 创建临时目录结构进行测试
            test_pdf_dir = "temp_test_pdf"
            test_md_dir = "temp_test_md"
            
            os.makedirs(test_pdf_dir, exist_ok=True)
            os.makedirs(test_md_dir, exist_ok=True)
            
            # 复制少量文件进行测试
            import shutil
            
            copied_files = 0
            for pdf_file in pdf_files[:2]:  # 只复制2个PDF文件
                try:
                    filename = os.path.basename(pdf_file)
                    shutil.copy2(pdf_file, os.path.join(test_pdf_dir, filename))
                    copied_files += 1
                    print(f"复制PDF文件: {filename}")
                except Exception as e:
                    print(f"复制PDF文件失败: {e}")
            
            for md_file in md_files[:2]:  # 只复制2个MD文件
                try:
                    filename = os.path.basename(md_file)
                    shutil.copy2(md_file, os.path.join(test_md_dir, filename))
                    copied_files += 1
                    print(f"复制MD文件: {filename}")
                except Exception as e:
                    print(f"复制MD文件失败: {e}")
            
            if copied_files > 0:
                print(f"\n开始处理 {copied_files} 个测试文件...")
                result = processor.process_dual_format_documents(test_pdf_dir, test_md_dir)
                print("✅ 双格式校对完成")
                print(f"结果: {result}")
            else:
                print("❌ 没有成功复制测试文件")
            
            # 清理临时目录
            try:
                shutil.rmtree(test_pdf_dir)
                shutil.rmtree(test_md_dir)
                print("✅ 清理临时目录完成")
            except:
                pass
                
        else:
            print("❌ 目录不完整，跳过处理")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
