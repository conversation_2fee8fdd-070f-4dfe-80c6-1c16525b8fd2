{"architectures": ["XLNetLMHeadModel"], "attn_type": "bi", "bi_data": false, "bos_token_id": 1, "clamp_len": -1, "d_head": 64, "d_inner": 3072, "d_model": 768, "dropout": 0.1, "end_n_top": 5, "eos_token_id": 2, "ff_activation": "gelu", "initializer_range": 0.02, "layer_norm_eps": 1e-12, "mem_len": null, "model_type": "xlnet", "n_head": 12, "n_layer": 12, "pad_token_id": 5, "reuse_len": null, "same_length": false, "start_n_top": 5, "summary_activation": "tanh", "summary_last_dropout": 0.1, "summary_type": "last", "summary_use_proj": true, "task_specific_params": {"text-generation": {"do_sample": true, "max_length": 250}}, "untie_r": true, "vocab_size": 32000}