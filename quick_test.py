#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import sys
import os

print("快速测试Ollama服务...")

try:
    # 测试Ollama服务
    response = requests.get('http://localhost:11434/api/tags', timeout=5)
    if response.status_code == 200:
        models = response.json().get('models', [])
        print(f"✅ Ollama服务正常，可用模型数: {len(models)}")
        
        # 查找嵌入模型
        embed_models = [m for m in models if 'embed' in m.get('name', '').lower()]
        if embed_models:
            print(f"   可用嵌入模型: {[m.get('name') for m in embed_models]}")
        else:
            print("   ⚠ 未找到嵌入模型")
    else:
        print(f"❌ Ollama服务响应异常: {response.status_code}")
        
    # 测试嵌入功能
    print("\n测试Ollama嵌入功能...")
    response = requests.post('http://localhost:11434/api/embeddings', 
                           json={'model': 'nomic-embed-text:latest', 'prompt': 'test'}, 
                           timeout=10)
    if response.status_code == 200:
        result = response.json()
        if 'embedding' in result:
            actual_dim = len(result['embedding'])
            print(f"✅ Ollama嵌入功能正常，返回维度: {actual_dim}")
        else:
            print("❌ API响应中没有embedding字段")
    else:
        print(f"❌ Ollama embedding API失败: {response.status_code}")
        
    # 检查索引0002
    print("\n检查索引0002...")
    from pathlib import Path
    idx_file = Path("data/indices/0002.idx")
    meta_file = Path("data/indices/0002.meta")
    
    if idx_file.exists() and meta_file.exists():
        print(f"✅ 索引0002存在，文件大小: {idx_file.stat().st_size / 1024:.2f} KB")
    else:
        print("❌ 索引0002不存在")
        
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n测试完成！")
