
#!/usr/bin/env python3
"""
快速测试PyQt6修复
"""

print("=" * 50)
print("快速测试PyQt6修复")
print("=" * 50)

# 测试PyQt6导入
print("1. 测试PyQt6导入...")
try:
    from PyQt6.QtWidgets import QApplication, QMessageBox
    print("✓ PyQt6导入成功")
    pyqt_available = True
except ImportError as e:
    print(f"✗ PyQt6导入失败: {e}")
    pyqt_available = False

# 测试向量化模块
print("\n2. 测试向量化模块...")
try:
    from src.vectorizer.embeddings import TextEmbedding
    print("✓ TextEmbedding导入成功")

    embedder = TextEmbedding()
    print("✓ TextEmbedding初始化成功")

    # 生成测试向量
    test_vectors = embedder.embed_texts(["测试文本1", "测试文本2"])
    print(f"✓ 向量生成成功，维度: {test_vectors.shape}")

except Exception as e:
    print(f"✗ 向量化模块测试失败: {e}")

# 测试索引模块
print("\n3. 测试索引模块...")
try:
    from src.indexer.builder import IndexBuilder
    print("✓ IndexBuilder导入成功")

    builder = IndexBuilder()
    print("✓ IndexBuilder初始化成功")

except Exception as e:
    print(f"✗ 索引模块测试失败: {e}")

print("\n" + "=" * 50)
print("测试完成")
print("=" * 50)