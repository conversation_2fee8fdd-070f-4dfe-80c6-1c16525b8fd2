#!/usr/bin/env python
# -*- coding: utf-8 -*-

import h5py
import numpy as np
import pickle
from pathlib import Path
import logging
from tqdm import tqdm

def rebuild_auto_standards_test_index():
    """重建auto_standards_test索引，从HDF5文件中读取所有向量数据"""
    print("=" * 80)
    print("🔧 重建auto_standards_test索引")
    print("=" * 80)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # 1. 检查HDF5文件
    vector_file = Path("data/vectors/vectors.h5")
    if not vector_file.exists():
        print("❌ 向量文件不存在")
        return False
    
    print(f"📁 向量文件: {vector_file.stat().st_size / 1024 / 1024:.2f} MB")
    
    # 2. 读取所有向量数据
    all_vectors = []
    all_ids = []
    
    try:
        with h5py.File(vector_file, 'r') as f:
            print(f"📊 HDF5文件包含 {len(f.keys())} 个组")
            
            # 收集所有compressed组
            compressed_groups = [key for key in f.keys() if key.startswith('compressed_')]
            print(f"📊 找到 {len(compressed_groups)} 个压缩向量组")
            
            for group_name in tqdm(compressed_groups, desc="读取向量数据"):
                try:
                    # 获取对应的ids组
                    ids_name = group_name.replace('compressed_', 'ids_')
                    
                    if ids_name in f:
                        # 读取压缩的向量数据
                        compressed_data = f[group_name][()]
                        ids_data = f[ids_name][:]
                        
                        # 解压向量数据
                        vectors = _decompress_vectors(compressed_data)
                        
                        if vectors is not None and len(vectors) > 0:
                            all_vectors.append(vectors)
                            all_ids.extend(ids_data)
                            
                except Exception as e:
                    logger.warning(f"跳过组 {group_name}: {e}")
                    continue
            
            if not all_vectors:
                print("❌ 未找到有效的向量数据")
                return False
            
            # 合并所有向量
            combined_vectors = np.vstack(all_vectors)
            combined_ids = np.array(all_ids)
            
            print(f"✅ 成功读取 {len(combined_vectors)} 个向量")
            print(f"✅ 向量维度: {combined_vectors.shape[1]}")
            
    except Exception as e:
        print(f"❌ 读取HDF5文件失败: {e}")
        return False
    
    # 3. 重建索引
    try:
        from src.indexer.builder import IndexBuilder
        
        # 创建索引配置
        config = {
            'indexing': {
                'index_type': 'hnsw',
                'metric': 'cosine',
                'ef_construction': 200,
                'ef_search': 100,
                'M': 16
            }
        }
        
        print("🔧 创建新的索引构建器...")
        builder = IndexBuilder(config)
        
        # 设置正确的维度
        builder.dimension = combined_vectors.shape[1]
        
        # 初始化索引
        builder._init_index()
        
        print("📥 添加向量到索引...")
        
        # 批量添加向量
        batch_size = 1000
        for i in tqdm(range(0, len(combined_vectors), batch_size), desc="添加向量"):
            batch_vectors = combined_vectors[i:i + batch_size]
            batch_ids = combined_ids[i:i + batch_size]
            
            # 添加到索引
            if hasattr(builder.index, 'add_items'):
                # HNSW索引
                for j, (vector, vector_id) in enumerate(zip(batch_vectors, batch_ids)):
                    builder.index.add_item(i + j, vector)
            else:
                # FAISS索引
                builder.index.add(batch_vectors)
        
        # 更新总向量数
        builder.total_vectors = len(combined_vectors)
        
        # 标记为已训练
        builder.is_trained = True
        
        print("💾 保存重建的索引...")
        
        # 保存索引
        index_file = Path("data/indices/auto_standards_test.idx")
        if builder.save_index(index_file):
            print(f"✅ 索引重建成功: {index_file}")
            print(f"✅ 总向量数: {builder.total_vectors}")
            print(f"✅ 向量维度: {builder.dimension}")
            return True
        else:
            print("❌ 索引保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 重建索引失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def _decompress_vectors(compressed_data):
    """解压向量数据"""
    try:
        import zlib
        import pickle
        
        # 尝试解压
        decompressed = zlib.decompress(compressed_data)
        vectors = pickle.loads(decompressed)
        
        return vectors
        
    except Exception as e:
        # 如果解压失败，可能数据没有压缩
        try:
            vectors = pickle.loads(compressed_data)
            return vectors
        except:
            # 最后尝试直接作为numpy数组
            try:
                return np.frombuffer(compressed_data, dtype=np.float32).reshape(-1, 768)
            except:
                return None

if __name__ == "__main__":
    success = rebuild_auto_standards_test_index()
    if success:
        print("\n🎉 索引重建完成！现在可以进行搜索了。")
    else:
        print("\n❌ 索引重建失败，请检查错误信息。")
