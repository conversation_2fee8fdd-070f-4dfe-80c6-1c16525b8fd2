#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

import h5py
import pickle
import numpy as np
import logging
from typing import List, Tu<PERSON>

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def extract_vectors_from_hdf5() -> Tuple[np.ndarray, List[str], int]:
    """从HDF5文件中提取所有向量和文档ID"""
    logger = logging.getLogger(__name__)
    
    vector_file = Path("data/vectors/vectors.h5")
    if not vector_file.exists():
        raise FileNotFoundError("向量文件不存在")
    
    all_vectors = []
    all_doc_ids = []
    vector_dimension = None
    
    logger.info("从HDF5文件提取向量数据...")
    
    with h5py.File(vector_file, 'r') as f:
        # 获取向量维度
        if 'vector_dim' in f.attrs:
            vector_dimension = f.attrs['vector_dim']
        else:
            # 从第一个向量组推断维度
            for key in f.keys():
                if key.startswith('compressed_'):
                    try:
                        # 尝试解压缩第一个向量组来获取维度
                        compressed_data = f[key][:]
                        decompressed = np.frombuffer(compressed_data, dtype=np.float32)
                        # 假设每个向量是连续存储的，从第一个向量推断维度
                        ids_key = key.replace('compressed_', 'ids_')
                        if ids_key in f:
                            num_vectors = len(f[ids_key])
                            if num_vectors > 0:
                                vector_dimension = len(decompressed) // num_vectors
                                break
                    except:
                        continue
        
        if vector_dimension is None:
            vector_dimension = 384  # 默认维度
        
        logger.info(f"向量维度: {vector_dimension}")
        
        # 提取所有向量组
        for key in sorted(f.keys()):
            if key.startswith('ids_'):
                # 获取文档ID
                doc_ids = f[key][:]
                doc_ids_list = [doc_id.decode('utf-8') if isinstance(doc_id, bytes) else str(doc_id) for doc_id in doc_ids]
                
                # 获取对应的压缩向量
                compressed_key = key.replace('ids_', 'compressed_')
                if compressed_key in f:
                    try:
                        compressed_data = f[compressed_key][:]
                        # 解压缩向量数据
                        decompressed = np.frombuffer(compressed_data, dtype=np.float32)
                        
                        # 重塑为向量矩阵
                        num_vectors = len(doc_ids_list)
                        if len(decompressed) == num_vectors * vector_dimension:
                            vectors = decompressed.reshape(num_vectors, vector_dimension)
                            all_vectors.append(vectors)
                            all_doc_ids.extend(doc_ids_list)
                            logger.info(f"提取组 {key}: {num_vectors} 个向量")
                        else:
                            logger.warning(f"组 {key} 向量数据大小不匹配，跳过")
                    except Exception as e:
                        logger.error(f"解压缩组 {key} 失败: {e}")
                        continue
    
    if all_vectors:
        combined_vectors = np.vstack(all_vectors)
        logger.info(f"总共提取 {len(combined_vectors)} 个向量，维度: {combined_vectors.shape[1]}")
        return combined_vectors, all_doc_ids, vector_dimension
    else:
        raise ValueError("未能从HDF5文件中提取任何向量")

def rebuild_index(vectors: np.ndarray, doc_ids: List[str], vector_dimension: int):
    """重建auto_standards_test索引"""
    logger = logging.getLogger(__name__)
    
    try:
        from src.indexer.builder import IndexBuilder
        
        # 删除旧的索引文件
        index_path = Path("data/indices/auto_standards_test.idx")
        meta_path = Path("data/indices/auto_standards_test.meta")
        
        if index_path.exists():
            index_path.unlink()
            logger.info("删除旧的索引文件")
        
        if meta_path.exists():
            meta_path.unlink()
            logger.info("删除旧的元数据文件")
        
        # 创建新的索引构建器
        logger.info("创建新的索引构建器...")
        builder = IndexBuilder(
            index_type='hnsw',
            dimension=vector_dimension,
            metric='cosine',
            index_path=str(index_path)
        )
        
        # 添加向量到索引
        logger.info(f"添加 {len(vectors)} 个向量到索引...")
        builder.add_vectors(vectors, doc_ids)
        
        # 训练索引
        logger.info("训练索引...")
        builder.train()
        
        # 保存索引
        logger.info("保存索引...")
        builder.save()
        
        # 验证索引
        logger.info("验证索引...")
        builder.load()
        
        # 检查索引状态
        total_vectors = builder.get_total_vectors()
        logger.info(f"✅ 索引重建成功！")
        logger.info(f"  - 索引文件: {index_path}")
        logger.info(f"  - 向量数量: {total_vectors}")
        logger.info(f"  - 向量维度: {vector_dimension}")
        
        return True
        
    except Exception as e:
        logger.error(f"重建索引失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_index():
    """验证重建的索引"""
    logger = logging.getLogger(__name__)
    
    try:
        from src.indexer.builder import IndexBuilder
        
        index_path = Path("data/indices/auto_standards_test.idx")
        meta_path = Path("data/indices/auto_standards_test.meta")
        
        if not index_path.exists() or not meta_path.exists():
            logger.error("索引文件或元数据文件不存在")
            return False
        
        # 加载索引
        builder = IndexBuilder.load_from_file(str(index_path))
        
        # 检查索引状态
        total_vectors = builder.get_total_vectors()
        dimension = builder.dimension
        
        logger.info(f"索引验证结果:")
        logger.info(f"  - 向量数量: {total_vectors}")
        logger.info(f"  - 向量维度: {dimension}")
        logger.info(f"  - 索引类型: {builder.index_type}")
        logger.info(f"  - 度量方式: {builder.metric}")
        
        # 测试搜索功能
        if total_vectors > 0:
            # 创建一个测试向量
            test_vector = np.random.random(dimension).astype(np.float32)
            results = builder.search(test_vector, k=5)
            logger.info(f"  - 搜索测试: 返回 {len(results)} 个结果")
        
        return True
        
    except Exception as e:
        logger.error(f"验证索引失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("=" * 80)
    logger.info("🔧 重建auto_standards_test索引")
    logger.info("=" * 80)
    
    try:
        # 1. 从HDF5提取向量
        logger.info("步骤1: 从HDF5文件提取向量数据")
        vectors, doc_ids, vector_dimension = extract_vectors_from_hdf5()
        
        # 2. 重建索引
        logger.info("步骤2: 重建索引")
        success = rebuild_index(vectors, doc_ids, vector_dimension)
        
        if not success:
            logger.error("索引重建失败")
            return False
        
        # 3. 验证索引
        logger.info("步骤3: 验证索引")
        verify_success = verify_index()
        
        if verify_success:
            logger.info("=" * 80)
            logger.info("🎉 auto_standards_test索引重建完成！")
            logger.info("=" * 80)
            logger.info("建议:")
            logger.info("1. 重启应用程序")
            logger.info("2. 在搜索界面选择auto_standards_test索引")
            logger.info("3. 测试搜索功能")
            return True
        else:
            logger.error("索引验证失败")
            return False
            
    except Exception as e:
        logger.error(f"重建过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
