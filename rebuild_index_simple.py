#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import h5py
import numpy as np
import zlib
from pathlib import Path
import sqlite3
import json

def rebuild_index_from_hdf5():
    """从HDF5文件重建索引"""
    print("=" * 60)
    print("从HDF5重建索引")
    print("=" * 60)
    
    # 文件路径
    vector_file = Path("data/vectors/vectors.h5")
    metadata_db = Path("data/vectors/metadata/metadata.db")
    index_file = Path("data/indices/auto_standards_test.idx")
    
    if not vector_file.exists():
        print(f"❌ 向量文件不存在: {vector_file}")
        return False
    
    if not metadata_db.exists():
        print(f"❌ 元数据数据库不存在: {metadata_db}")
        return False
    
    print(f"✅ 向量文件: {vector_file} ({vector_file.stat().st_size / 1024 / 1024:.2f} MB)")
    print(f"✅ 元数据数据库: {metadata_db}")
    print(f"✅ 目标索引: {index_file}")
    
    # 第一步：从HDF5提取所有向量
    print("\n1. 从HDF5提取向量...")
    vectors = []
    doc_ids = []
    
    try:
        with h5py.File(vector_file, 'r') as f:
            print(f"   HDF5文件属性:")
            for attr_name, attr_value in f.attrs.items():
                print(f"     {attr_name}: {attr_value}")
            
            keys = list(f.keys())
            print(f"   总数据集数: {len(keys)}")
            
            # 找到所有ids数据集
            ids_keys = [k for k in keys if k.startswith('ids_')]
            print(f"   ID数据集数: {len(ids_keys)}")
            
            for ids_key in ids_keys:
                try:
                    # 读取文档ID
                    ids_dataset = f[ids_key]
                    if ids_dataset.shape == ():
                        # 标量数据集
                        doc_id = ids_dataset[()]
                        if isinstance(doc_id, bytes):
                            doc_id = doc_id.decode('utf-8')
                        doc_ids.append(str(doc_id))
                        
                        # 读取对应的压缩向量
                        compressed_key = ids_key.replace('ids_', 'compressed_')
                        if compressed_key in f:
                            compressed_dataset = f[compressed_key]
                            if compressed_dataset.shape == ():
                                compressed_data = compressed_dataset[()]
                                if hasattr(compressed_data, 'tobytes'):
                                    compressed_bytes = compressed_data.tobytes()
                                    try:
                                        decompressed = zlib.decompress(compressed_bytes)
                                        vector = np.frombuffer(decompressed, dtype=np.float32)
                                        vectors.append(vector)
                                        print(f"   ✅ 提取向量: {doc_id}, 维度: {len(vector)}")
                                    except Exception as e:
                                        print(f"   ❌ 解压缩失败 {ids_key}: {e}")
                                        continue
                                else:
                                    print(f"   ⚠️  压缩数据格式异常: {compressed_key}")
                                    continue
                            else:
                                print(f"   ⚠️  压缩数据集不是标量: {compressed_key}")
                                continue
                        else:
                            print(f"   ⚠️  未找到对应的压缩数据集: {compressed_key}")
                            continue
                    else:
                        # 数组数据集
                        ids_data = ids_dataset[:]
                        for i, doc_id in enumerate(ids_data):
                            if isinstance(doc_id, bytes):
                                doc_id = doc_id.decode('utf-8')
                            doc_ids.append(str(doc_id))
                        
                        # 读取对应的压缩向量数组
                        compressed_key = ids_key.replace('ids_', 'compressed_')
                        if compressed_key in f:
                            compressed_dataset = f[compressed_key]
                            compressed_data = compressed_dataset[:]
                            for compressed_item in compressed_data:
                                try:
                                    if hasattr(compressed_item, 'tobytes'):
                                        compressed_bytes = compressed_item.tobytes()
                                        decompressed = zlib.decompress(compressed_bytes)
                                        vector = np.frombuffer(decompressed, dtype=np.float32)
                                        vectors.append(vector)
                                    else:
                                        print(f"   ⚠️  压缩项格式异常")
                                        continue
                                except Exception as e:
                                    print(f"   ❌ 解压缩数组项失败: {e}")
                                    continue
                        else:
                            print(f"   ⚠️  未找到对应的压缩数据集: {compressed_key}")
                            continue
                            
                except Exception as e:
                    print(f"   ❌ 处理数据集失败 {ids_key}: {e}")
                    continue
    
    except Exception as e:
        print(f"❌ 读取HDF5文件失败: {e}")
        return False
    
    print(f"\n   提取完成: {len(vectors)} 个向量, {len(doc_ids)} 个文档ID")
    
    if len(vectors) == 0:
        print("❌ 没有提取到任何向量")
        return False
    
    if len(vectors) != len(doc_ids):
        print(f"❌ 向量数量({len(vectors)})与文档ID数量({len(doc_ids)})不匹配")
        return False
    
    # 检查向量维度一致性
    vector_dims = [len(v) for v in vectors]
    unique_dims = set(vector_dims)
    print(f"   向量维度分布: {dict(zip(unique_dims, [vector_dims.count(d) for d in unique_dims]))}")
    
    if len(unique_dims) > 1:
        print("⚠️  发现不同维度的向量，将使用最常见的维度")
        most_common_dim = max(unique_dims, key=vector_dims.count)
        print(f"   使用维度: {most_common_dim}")
        
        # 过滤出相同维度的向量
        filtered_vectors = []
        filtered_doc_ids = []
        for i, (vector, doc_id) in enumerate(zip(vectors, doc_ids)):
            if len(vector) == most_common_dim:
                filtered_vectors.append(vector)
                filtered_doc_ids.append(doc_id)
        
        vectors = filtered_vectors
        doc_ids = filtered_doc_ids
        print(f"   过滤后: {len(vectors)} 个向量")
    
    # 转换为numpy数组
    vectors_array = np.array(vectors, dtype=np.float32)
    print(f"   向量数组形状: {vectors_array.shape}")
    
    # 第二步：重建索引
    print("\n2. 重建索引...")
    try:
        from src.indexer.builder import IndexBuilder
        
        # 创建索引构建器
        builder = IndexBuilder(
            index_type='hnsw',
            vector_dimension=vectors_array.shape[1],
            metric='cosine'
        )
        
        # 添加向量到索引
        print(f"   添加 {len(vectors_array)} 个向量到索引...")
        for i, (vector, doc_id) in enumerate(zip(vectors_array, doc_ids)):
            builder.add_vector(vector, doc_id)
            if (i + 1) % 1000 == 0:
                print(f"   已添加 {i + 1}/{len(vectors_array)} 个向量")
        
        # 保存索引
        print(f"   保存索引到: {index_file}")
        builder.save(str(index_file))
        
        print("✅ 索引重建完成!")
        
        # 验证索引
        print("\n3. 验证索引...")
        builder_verify = IndexBuilder.load(str(index_file))
        index_vector_count = builder_verify.get_vector_count()
        print(f"   索引中的向量数量: {index_vector_count}")
        
        if index_vector_count == len(vectors_array):
            print("✅ 索引验证成功!")
            return True
        else:
            print(f"❌ 索引验证失败: 期望{len(vectors_array)}, 实际{index_vector_count}")
            return False
            
    except Exception as e:
        print(f"❌ 重建索引失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = rebuild_index_from_hdf5()
    if success:
        print("\n🎉 索引重建成功!")
    else:
        print("\n💥 索引重建失败!")
