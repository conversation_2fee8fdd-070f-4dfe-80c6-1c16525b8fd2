#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PyQt6环境修复脚本
"""

import sys
import os
import subprocess
import logging
from pathlib import Path

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_qt_installation():
    """检查PyQt6安装情况"""
    try:
        import PyQt6
        from PyQt6.QtWidgets import QApplication
        logger.info("PyQt6已正确安装")
        return True
    except ImportError as e:
        logger.error(f"PyQt6导入错误: {e}")
        return False

def reinstall_pyqt6():
    """重新安装PyQt6"""
    logger.info("正在重新安装PyQt6...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "--force-reinstall", "PyQt6"])
        logger.info("PyQt6重新安装完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"重新安装PyQt6失败: {e}")
        return False

def verify_qt_environment():
    """验证Qt环境"""
    try:
        from PyQt6.QtCore import QLibraryInfo
        logger.info("Qt库路径:")
        logger.info(f"插件路径: {QLibraryInfo.path(QLibraryInfo.LibraryPath.PluginsPath)}")
        logger.info(f"库路径: {QLibraryInfo.path(QLibraryInfo.LibraryPath.LibraryPath)}")
        return True
    except Exception as e:
        logger.error(f"验证Qt环境失败: {e}")
        return False

def main():
    if not check_qt_installation():
        logger.warning("PyQt6安装有问题，尝试修复...")
        if not reinstall_pyqt6():
            logger.error("无法修复PyQt6安装")
            return 1
    
    if not verify_qt_environment():
        logger.error("Qt环境验证失败")
        return 1
    
    logger.info("PyQt6环境已修复")
    return 0

if __name__ == "__main__":
    sys.exit(main())