# 核心依赖
concurrent_log_handler>=0.9.19
PyQt6>=6.4.0
numpy>=1.21.0,<2.0.0
pandas>=1.3.0
pyyaml>=6.0
tqdm>=4.62.0

# CPU版本 PyTorch
torch>=2.0.0,<3.0.0
torchvision>=0.15.0,<1.0.0
torchaudio>=2.0.0,<3.0.0


# 机器学习和NLP
sentence-transformers>=2.2.0
transformers>=4.21.0,<5.0.0
tokenizers>=0.13.0,<0.22.0
datasets>=2.10.0,<3.0.0
accelerate>=0.18.0
optimum>=1.8.0

# ONNX Runtime CPU版本
onnxruntime>=1.14.0

# 文本处理
langdetect>=1.0.9
jieba>=0.42.1
nltk>=3.6.0
spacy>=3.2.0,<4.0.0
zhconv>=1.4.0
mecab-python3>=1.0.5
konlpy>=0.6.0
pythainlp>=3.1.0
pyarabic>=0.6.15
pymorphy2>=0.9.1
fugashi>=1.2.0
sudachipy>=0.6.7
darkdetect>=0.8.0

# 代码处理
pygments>=2.14.0
tree-sitter>=0.20.1

# 数据处理
h5py>=3.6.0
chardet>=5.0.0
python-frontmatter>=1.0.0

# 工具库
python-dotenv>=0.19.0
pathlib2>=2.3.0

# CPU版本向量处理
faiss-cpu>=1.7.0
blis==0.7.9

matplotlib>=3.4.0
hnswlib>=0.7.0