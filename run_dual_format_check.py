#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
安全的双格式校对运行脚本
限制处理文件数量，避免程序假死
"""

import os
import sys
import logging
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from dual_format_processor import DualFormatProcessor

def setup_logging():
    """设置日志"""
    # 清除现有的处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('dual_format_check.log', encoding='utf-8', mode='w'),
            logging.StreamHandler(sys.stdout)
        ],
        force=True
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 60)
    logger.info("双格式文档校对开始")
    logger.info("=" * 60)
    
    # 定义路径
    pdf_dir = "test_training_data/raw_documents/enterprise_standards"
    md_dir = "test_training_data/raw_documents_MD/enterprise_standards"
    
    logger.info(f"PDF目录: {pdf_dir}")
    logger.info(f"MD目录: {md_dir}")
    
    # 检查目录是否存在
    if not os.path.exists(pdf_dir):
        logger.error(f"PDF目录不存在: {pdf_dir}")
        return 1
        
    if not os.path.exists(md_dir):
        logger.error(f"MD目录不存在: {md_dir}")
        return 1
    
    try:
        # 配置处理器 - 限制处理文件数量
        config = {
            'output_dir': 'data/dual_format_reports',
            'max_files_to_process': 20,  # 限制处理20个文件
            'quality_thresholds': {
                'min_content_length': 100,
                'min_similarity': 0.7,
                'max_error_rate': 0.1,
                'min_completeness': 0.8
            }
        }
        
        # 创建处理器
        processor = DualFormatProcessor(config)
        logger.info("双格式处理器创建成功")
        
        # 执行校对
        logger.info("开始执行双格式校对...")
        results = processor.process_dual_format_documents(pdf_dir, md_dir)
        
        # 输出结果
        logger.info("=" * 60)
        logger.info("校对结果汇总:")
        logger.info("=" * 60)
        logger.info(f"总文档对数: {results['total_pairs']}")
        logger.info(f"已处理: {results['processed']}")
        logger.info(f"PDF有效: {results['pdf_valid']}")
        logger.info(f"MD有效: {results['md_valid']}")
        logger.info(f"双格式都有效: {results['both_valid']}")
        logger.info(f"相似度通过: {results['similarity_passed']}")
        
        if results['errors']:
            logger.warning(f"错误数量: {len(results['errors'])}")
            for error in results['errors'][:5]:  # 只显示前5个错误
                logger.warning(f"  - {error}")
        
        # 计算成功率
        if results['total_pairs'] > 0:
            success_rate = results['both_valid'] / results['total_pairs'] * 100
            similarity_rate = results['similarity_passed'] / results['total_pairs'] * 100
            
            logger.info(f"双格式有效率: {success_rate:.2f}%")
            logger.info(f"相似度通过率: {similarity_rate:.2f}%")
        
        logger.info(f"详细报告已保存")
        logger.info("=" * 60)
        logger.info("双格式校对完成")
        logger.info("=" * 60)
        
        return 0
        
    except Exception as e:
        logger.error(f"处理过程中出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
