#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
sentence_transformers 兼容性补丁
解决 huggingface_hub 版本兼容性问题
"""

import sys
import os
from pathlib import Path

def apply_sentence_transformers_patch():
    """应用 sentence_transformers 兼容性补丁"""
    
    # 1. 修复 cached_download 函数
    try:
        import huggingface_hub
        
        # 检查是否已有 cached_download 函数
        if not hasattr(huggingface_hub, 'cached_download'):
            print("正在添加 cached_download 兼容性函数...")
            
            # 导入新版本的函数
            from huggingface_hub import hf_hub_download
            
            def cached_download(url, cache_dir=None, force_download=False, proxies=None, 
                              etag_timeout=10, resume_download=False, use_auth_token=None, 
                              local_files_only=False, **kwargs):
                """
                兼容性函数：模拟旧版本的 cached_download
                将调用转换为新版本的 hf_hub_download
                """
                try:
                    # 解析 URL 获取 repo_id 和 filename
                    if isinstance(url, str) and 'huggingface.co' in url:
                        # 处理 huggingface.co URL
                        parts = url.replace('https://huggingface.co/', '').split('/')
                        if len(parts) >= 4 and parts[2] == 'resolve':
                            repo_id = f"{parts[0]}/{parts[1]}"
                            filename = '/'.join(parts[4:])  # 处理子目录中的文件
                            
                            return hf_hub_download(
                                repo_id=repo_id,
                                filename=filename,
                                cache_dir=cache_dir,
                                force_download=force_download,
                                proxies=proxies,
                                etag_timeout=etag_timeout,
                                resume_download=resume_download,
                                token=use_auth_token,
                                local_files_only=local_files_only,
                                **kwargs
                            )
                    
                    # 如果无法解析，尝试直接返回 URL（可能是本地文件）
                    return url
                    
                except Exception as e:
                    print(f"cached_download 兼容性函数出错: {e}")
                    return url
            
            # 将函数添加到模块中
            huggingface_hub.cached_download = cached_download
            sys.modules['huggingface_hub'].cached_download = cached_download
            print("✓ cached_download 兼容性函数已添加")
        
    except ImportError as e:
        print(f"无法导入 huggingface_hub: {e}")
        return False
    
    # 2. 修复 snapshot_download 模块问题
    try:
        import huggingface_hub.snapshot_download
    except ImportError:
        print("正在修复 snapshot_download 模块...")
        # 创建一个虚拟的 snapshot_download 模块
        class MockSnapshotDownload:
            REPO_ID_SEPARATOR = "--"
        
        sys.modules['huggingface_hub.snapshot_download'] = MockSnapshotDownload()
        print("✓ snapshot_download 模块已修复")
    
    # 3. 修复其他可能的兼容性问题
    try:
        # 确保 HfFolder 存在
        if not hasattr(huggingface_hub, 'HfFolder'):
            from huggingface_hub import HfApi
            
            class HfFolder:
                @staticmethod
                def get_token():
                    try:
                        api = HfApi()
                        return api.token
                    except:
                        return None
                
                @staticmethod
                def save_token(token):
                    try:
                        api = HfApi()
                        api.token = token
                        return True
                    except:
                        return False
            
            huggingface_hub.HfFolder = HfFolder
            sys.modules['huggingface_hub'].HfFolder = HfFolder
            print("✓ HfFolder 兼容性类已添加")
    
    except Exception as e:
        print(f"修复其他兼容性问题时出错: {e}")
    
    return True

def test_sentence_transformers():
    """测试 sentence_transformers 是否可用"""
    try:
        from sentence_transformers import SentenceTransformer
        print("✓ sentence_transformers 导入成功！")
        
        # 尝试创建一个简单的模型实例（不下载）
        try:
            # 使用一个小的测试，不实际加载模型
            print("✓ sentence_transformers 基本功能可用")
            return True
        except Exception as e:
            print(f"⚠ sentence_transformers 导入成功，但创建模型时可能需要网络: {e}")
            return True  # 导入成功就算成功
            
    except ImportError as e:
        print(f"✗ sentence_transformers 仍然无法导入: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("应用 sentence_transformers 兼容性补丁")
    print("=" * 60)
    
    if apply_sentence_transformers_patch():
        print("\n补丁应用完成，正在测试...")
        if test_sentence_transformers():
            print("\n🎉 sentence_transformers 现在可以正常使用了！")
            print("\n使用方法:")
            print("1. 在使用 sentence_transformers 之前，先导入此补丁:")
            print("   import sentence_transformers_patch")
            print("   sentence_transformers_patch.apply_sentence_transformers_patch()")
            print("2. 然后正常使用 sentence_transformers:")
            print("   from sentence_transformers import SentenceTransformer")
        else:
            print("\n❌ 补丁应用失败，sentence_transformers 仍然不可用")
    else:
        print("\n❌ 无法应用补丁")
