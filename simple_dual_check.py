#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的双格式校对测试
"""

import os
import sys
from pathlib import Path

def main():
    print("=== 双格式校对测试 ===")
    
    # 检查目录
    pdf_dir = "test_training_data/raw_documents/enterprise_standards"
    md_dir = "test_training_data/raw_documents_MD/enterprise_standards"
    
    print(f"PDF目录: {pdf_dir}")
    print(f"MD目录: {md_dir}")
    
    pdf_exists = os.path.exists(pdf_dir)
    md_exists = os.path.exists(md_dir)
    
    print(f"PDF目录存在: {pdf_exists}")
    print(f"MD目录存在: {md_exists}")
    
    if pdf_exists and md_exists:
        # 统计文件
        pdf_count = 0
        md_count = 0
        
        for root, dirs, files in os.walk(pdf_dir):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_count += 1
        
        for root, dirs, files in os.walk(md_dir):
            for file in files:
                if file.lower().endswith('.md'):
                    md_count += 1
        
        print(f"PDF文件数量: {pdf_count}")
        print(f"MD文件数量: {md_count}")
        
        # 尝试导入处理器
        try:
            sys.path.insert(0, 'src')
            from dual_format_processor import DualFormatProcessor
            print("✅ 成功导入双格式处理器")
            
            # 创建配置
            config = {
                'output_dir': 'data/dual_format_reports',
                'max_files_to_process': 5,  # 只处理5个文件
                'quality_thresholds': {
                    'min_content_length': 50,
                    'min_similarity': 0.5,
                    'max_error_rate': 0.2,
                    'min_completeness': 0.5
                }
            }
            
            processor = DualFormatProcessor(config)
            print("✅ 成功创建处理器")
            
            print("🔄 开始处理...")
            result = processor.process_dual_format_documents(pdf_dir, md_dir)
            
            print("✅ 处理完成")
            print(f"结果: {result}")
            
        except Exception as e:
            print(f"❌ 错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("=== 测试完成 ===")

if __name__ == "__main__":
    main()
