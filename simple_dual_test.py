#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的双格式校对测试
"""

import os
import sys
from pathlib import Path

def main():
    print("=== 简单双格式校对测试 ===")
    
    # 检查目录结构
    pdf_dir = "test_training_data/raw_documents/enterprise_standards"
    md_dir = "test_training_data/raw_documents_MD/enterprise_standards"
    
    print(f"PDF目录: {pdf_dir}")
    print(f"MD目录: {md_dir}")
    
    # 检查目录是否存在
    pdf_exists = os.path.exists(pdf_dir)
    md_exists = os.path.exists(md_dir)
    
    print(f"PDF目录存在: {pdf_exists}")
    print(f"MD目录存在: {md_exists}")
    
    if pdf_exists:
        # 统计PDF文件
        pdf_count = 0
        for root, dirs, files in os.walk(pdf_dir):
            pdf_count += len([f for f in files if f.lower().endswith('.pdf')])
        print(f"PDF文件数量: {pdf_count}")
    
    if md_exists:
        # 统计MD文件
        md_count = 0
        for root, dirs, files in os.walk(md_dir):
            md_count += len([f for f in files if f.lower().endswith('.md')])
        print(f"MD文件数量: {md_count}")
    
    # 尝试导入双格式处理器
    try:
        sys.path.insert(0, 'src')
        from dual_format_processor import DualFormatProcessor
        print("✅ 成功导入双格式处理器")
        
        # 创建处理器
        processor = DualFormatProcessor()
        print("✅ 成功创建处理器实例")
        
        if pdf_exists and md_exists:
            print("🔄 开始执行双格式校对...")
            result = processor.process_dual_format_documents(pdf_dir, md_dir)
            print("✅ 双格式校对完成")
            print(f"结果: {result}")
        else:
            print("❌ 目录不完整，跳过处理")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("=== 测试完成 ===")

if __name__ == "__main__":
    main()
