#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import sys
import os

print("🎯 简单模型选择测试")
print("=" * 50)

# 1. 测试Ollama服务
print("1. 测试Ollama服务...")
try:
    response = requests.get('http://localhost:11434/api/tags', timeout=5)
    if response.status_code == 200:
        models = response.json().get('models', [])
        embed_models = [m for m in models if 'embed' in m.get('name', '').lower()]
        print(f"✅ Ollama正常，嵌入模型: {len(embed_models)}")
        for model in embed_models:
            print(f"   - {model.get('name', '')}")
    else:
        print(f"❌ Ollama响应异常: {response.status_code}")
except Exception as e:
    print(f"❌ Ollama连接失败: {e}")

# 2. 测试配置创建逻辑
print("\n2. 测试配置创建逻辑...")
try:
    # 模拟配置创建
    def create_config(selected_model, index_dim):
        print(f"   输入: 模型={selected_model}, 维度={index_dim}")
        
        if not selected_model or selected_model in ["不使用大模型", "--- 本地大模型 ---", ""]:
            raise ValueError(f"用户未选择有效模型: {selected_model}")
        
        if 'ollama' in selected_model.lower():
            if index_dim != 768:
                raise ValueError(f"维度不匹配：Ollama输出768维，索引需要{index_dim}维")
            return {"type": "ollama", "dim": 768}
        elif 'sentence-transformers' in selected_model.lower():
            if index_dim != 384:
                raise ValueError(f"维度不匹配：ST输出384维，索引需要{index_dim}维")
            return {"type": "sentence-transformers", "dim": 384}
        else:
            raise ValueError(f"不支持的模型类型: {selected_model}")
    
    # 测试用例
    test_cases = [
        ("ollama:nomic-embed-text:latest", 768, True),
        ("sentence-transformers:model", 384, True),
        ("ollama:nomic-embed-text:latest", 384, False),  # 维度不匹配
        ("sentence-transformers:model", 768, False),     # 维度不匹配
        ("", 768, False),                                # 无效模型
        ("unsupported:model", 768, False),               # 不支持的类型
    ]
    
    for model, dim, should_succeed in test_cases:
        try:
            config = create_config(model, dim)
            if should_succeed:
                print(f"   ✅ {model} + {dim}维 → {config['type']}")
            else:
                print(f"   ❌ 应该失败但成功了: {model} + {dim}维")
        except ValueError as e:
            if not should_succeed:
                print(f"   ✅ 正确捕获错误: {model} + {dim}维 → {e}")
            else:
                print(f"   ❌ 不应该失败: {model} + {dim}维 → {e}")
        except Exception as e:
            print(f"   ❌ 意外错误: {model} + {dim}维 → {e}")

except Exception as e:
    print(f"❌ 配置创建测试失败: {e}")

print("\n🎯 测试完成！")
print("现在搜索界面将严格按照用户选择执行，不再有硬编码替代方案。")
