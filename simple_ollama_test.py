#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json

def test_ollama():
    """简单的Ollama连接测试"""
    
    print("测试Ollama连接...")
    
    try:
        # 测试基本连接
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        print(f"连接状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2)}")
            
            models = data.get('models', [])
            print(f"可用模型数量: {len(models)}")
            
            for model in models:
                print(f"模型: {model.get('name', 'unknown')}")
            
            return True
        else:
            print(f"连接失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"连接出错: {e}")
        return False

if __name__ == "__main__":
    test_ollama()
