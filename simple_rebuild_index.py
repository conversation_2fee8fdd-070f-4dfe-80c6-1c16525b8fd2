#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

import h5py
import numpy as np
import pickle
import logging
from tqdm import tqdm

def simple_rebuild():
    """简单重建auto_standards_test索引"""
    print("🔧 开始重建auto_standards_test索引...")
    
    # 检查文件
    vector_file = Path("data/vectors/vectors.h5")
    if not vector_file.exists():
        print("❌ 向量文件不存在")
        return False
    
    print(f"📁 向量文件存在: {vector_file.stat().st_size / 1024 / 1024:.2f} MB")
    
    # 读取向量数据
    try:
        with h5py.File(vector_file, 'r') as f:
            print(f"📊 HDF5文件包含 {len(f.keys())} 个组")
            
            # 找到所有向量组
            vector_groups = []
            for key in f.keys():
                if key.startswith('compressed_') or key.startswith('vectors_'):
                    vector_groups.append(key)
            
            print(f"📊 找到 {len(vector_groups)} 个向量组")
            
            if not vector_groups:
                print("❌ 未找到向量数据")
                return False
            
            # 统计总向量数
            total_vectors = 0
            for group_name in vector_groups:
                ids_name = group_name.replace('compressed_', 'ids_').replace('vectors_', 'ids_')
                if ids_name in f:
                    ids_data = f[ids_name]
                    total_vectors += len(ids_data)
            
            print(f"✅ 统计到总向量数: {total_vectors}")
            
    except Exception as e:
        print(f"❌ 读取HDF5文件失败: {e}")
        return False
    
    # 更新索引元数据
    try:
        index_file = Path("data/indices/auto_standards_test.idx")
        meta_file = Path("data/indices/auto_standards_test.meta")
        
        if meta_file.exists():
            # 读取现有元数据
            with open(meta_file, 'rb') as f:
                metadata = pickle.load(f)
            
            print(f"📋 当前元数据: {metadata.get('total_vectors', 0)} 个向量")
            
            # 更新向量数量
            metadata['total_vectors'] = total_vectors
            metadata['is_trained'] = True
            
            # 保存更新的元数据
            with open(meta_file, 'wb') as f:
                pickle.dump(metadata, f)
            
            print(f"✅ 元数据已更新: {total_vectors} 个向量")
            return True
        else:
            print("❌ 元数据文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 更新元数据失败: {e}")
        return False

if __name__ == "__main__":
    success = simple_rebuild()
    if success:
        print("🎉 索引重建完成！")
    else:
        print("❌ 索引重建失败！")
