#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    print("开始简单测试...")
    
    # 测试1: 基本导入
    print("1. 测试基本导入...")
    try:
        from src.gui.i18n import Translator
        print("   ✓ Translator导入成功")
    except Exception as e:
        print(f"   ✗ Translator导入失败: {e}")
        return False
    
    # 测试2: 向量化组件导入
    print("2. 测试向量化组件导入...")
    try:
        from src.gui.widgets.vectorize import VectorizeWidget
        print("   ✓ VectorizeWidget导入成功")
    except Exception as e:
        print(f"   ✗ VectorizeWidget导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试3: 创建组件实例
    print("3. 测试创建组件实例...")
    try:
        translator = Translator()
        widget = VectorizeWidget(translator)
        print("   ✓ VectorizeWidget实例创建成功")

        # 检查关键属性
        if hasattr(widget, 'logger'):
            print("   ✓ logger属性存在")
        else:
            print("   ✗ logger属性缺失")

        if hasattr(widget, '_split_text'):
            print("   ✓ _split_text方法存在")
        else:
            print("   ✗ _split_text方法缺失")

        if hasattr(widget, 'language_combo'):
            print("   ✓ language_combo属性存在")
        else:
            print("   ✗ language_combo属性缺失")

        if hasattr(widget, 'model_combo'):
            print("   ✓ model_combo属性存在")
        else:
            print("   ✗ model_combo属性缺失")

        # 测试_split_text方法
        print("4. 测试_split_text方法...")
        test_text = "这是第一段。\n\n这是第二段。\n\n这是第三段。"
        chunks = widget._split_text(test_text, "txt")
        print(f"   ✓ _split_text方法工作正常，分割出 {len(chunks)} 个块")

    except Exception as e:
        print(f"   ✗ 创建组件实例失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("✓ 所有测试通过！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
