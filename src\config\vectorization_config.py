from dataclasses import dataclass
from typing import Dict, Any, Optional
from pathlib import Path

@dataclass
class VectorizationConfig:
    """向量化配置类"""
    model_name: str = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
    batch_size: int = 32
    vector_dimension: int = 768  # 更新默认维度为768
    device: str = "cpu"
    normalize_vectors: bool = True
    chunk_size: int = 300
    overlap: int = 50
    max_chunks: int = 1000
    indices_dir: Path = Path("data/indices")
    vectors_dir: Path = Path("data/vectors")
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> "VectorizationConfig":
        return cls(**{
            k: v for k, v in config.items() 
            if k in cls.__dataclass_fields__
        })
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            field: getattr(self, field)
            for field in self.__dataclass_fields__
        }

    def validate(self) -> Optional[str]:
        """验证配置参数"""
        if self.batch_size < 1:
            return "批处理大小必须大于0"
        if self.vector_dimension < 64:
            return "向量维度必须大于等于64"
        if self.chunk_size < 50:
            return "文本块大小必须大于50"
        if self.overlap >= self.chunk_size:
            return "重叠大小必须小于文本块大小"
        return None