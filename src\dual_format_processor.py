#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
双格式文档处理和校核系统
支持PDF和MD文件的并存处理、内容校核和质量评估
"""

import os
import sys
import json
import logging
import hashlib
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import numpy as np
from difflib import SequenceMatcher
import re

# PDF处理
PDF_AVAILABLE = False
PDF_LIBS = []

def _install_missing_packages():
    """尝试安装缺失的包"""
    import subprocess
    import sys

    missing_packages = []

    # 检查pdfplumber
    try:
        import pdfplumber
        PDF_LIBS.append('pdfplumber')
    except ImportError:
        missing_packages.append('pdfplumber')

    # 检查PyPDF2
    try:
        import PyPDF2
        PDF_LIBS.append('PyPDF2')
    except ImportError:
        missing_packages.append('PyPDF2')

    # 检查markdown
    try:
        import markdown
    except ImportError:
        missing_packages.append('markdown')

    # 检查frontmatter
    try:
        import frontmatter
    except ImportError:
        missing_packages.append('python-frontmatter')

    if missing_packages:
        print(f"正在安装缺失的包: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("包安装成功，请重新运行程序")
            return True
        except subprocess.CalledProcessError as e:
            print(f"包安装失败: {e}")
            return False
    return True

# 尝试安装缺失的包
_install_missing_packages()

# 重新检查PDF处理库
try:
    import pdfplumber
    if 'pdfplumber' not in PDF_LIBS:
        PDF_LIBS.append('pdfplumber')
    PDF_AVAILABLE = True
except ImportError:
    pass

try:
    import PyPDF2
    if 'PyPDF2' not in PDF_LIBS:
        PDF_LIBS.append('PyPDF2')
    PDF_AVAILABLE = True
except ImportError:
    pass

if not PDF_AVAILABLE:
    logging.warning("PDF处理库不可用，请手动安装: pip install pdfplumber PyPDF2")
else:
    logging.info(f"可用的PDF处理库: {', '.join(PDF_LIBS)}")

# MD处理
try:
    import markdown
    import frontmatter
    MD_AVAILABLE = True
except ImportError:
    MD_AVAILABLE = False
    logging.warning("Markdown处理库不可用，请手动安装: pip install markdown python-frontmatter")

class DualFormatProcessor:
    """双格式文档处理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化双格式处理器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 输出目录设置
        self.output_dir = Path(self.config.get('output_dir', 'data/dual_format_reports'))
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 校核结果文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.report_file = self.output_dir / f'dual_format_validation_report_{timestamp}.xlsx'
        
        # 质量评估阈值
        self.quality_thresholds = {
            'min_content_length': 100,      # 最小内容长度
            'min_similarity': 0.7,          # PDF和MD最小相似度
            'max_error_rate': 0.1,          # 最大错误率
            'min_completeness': 0.8         # 最小完整性
        }
        
        # 初始化报告数据
        self.validation_results = []
        
    def process_dual_format_documents(self, pdf_dir: str, md_dir: str = None) -> Dict[str, Any]:
        """
        处理双格式文档目录

        Args:
            pdf_dir: PDF文档目录路径
            md_dir: MD文档目录路径（可选，如果不提供则自动推导）

        Returns:
            Dict: 处理结果统计
        """
        self.logger.info(f"开始处理双格式文档目录: PDF={pdf_dir}, MD={md_dir}")

        pdf_path = Path(pdf_dir)

        # 如果没有提供MD目录，则使用旧的推导方式
        if md_dir is None:
            md_path = Path(str(pdf_dir) + '_MD')
        else:
            md_path = Path(md_dir)

        # 确保目录存在
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF目录不存在: {pdf_path}")

        if not md_path.exists():
            self.logger.warning(f"MD目录不存在: {md_path}")
            self._create_md_directory_structure(pdf_path, md_path)
        
        # 扫描文档对
        document_pairs = self._find_document_pairs(pdf_path, md_path)
        
        # 处理每个文档对
        results = {
            'total_pairs': len(document_pairs),
            'processed': 0,
            'pdf_valid': 0,
            'md_valid': 0,
            'both_valid': 0,
            'similarity_passed': 0,
            'errors': []
        }

        # 限制处理数量以避免假死（可通过配置调整）
        max_files = self.config.get('max_files_to_process', 50)  # 默认最多处理50个文件
        if len(document_pairs) > max_files:
            self.logger.warning(f"文档对数量({len(document_pairs)})超过限制({max_files})，将只处理前{max_files}个")
            document_pairs = document_pairs[:max_files]
            results['total_pairs'] = len(document_pairs)

        for i, (pdf_path, md_path) in enumerate(document_pairs):
            if i % 10 == 0:  # 每处理10个文件输出一次进度
                self.logger.info(f"处理进度: {i}/{len(document_pairs)} ({i/len(document_pairs)*100:.1f}%)")
            try:
                result = self._process_document_pair(pdf_path, md_path)
                self.validation_results.append(result)
                
                results['processed'] += 1
                if result['pdf_valid']:
                    results['pdf_valid'] += 1
                if result['md_valid']:
                    results['md_valid'] += 1
                if result['pdf_valid'] and result['md_valid']:
                    results['both_valid'] += 1
                if result['similarity_score'] >= self.quality_thresholds['min_similarity']:
                    results['similarity_passed'] += 1
                    
            except Exception as e:
                error_msg = f"处理文档对失败 {pdf_path} <-> {md_path}: {e}"
                self.logger.error(error_msg)
                results['errors'].append(error_msg)
        
        # 保存报告
        self._save_validation_report()
        
        self.logger.info(f"双格式文档处理完成: {results}")
        return results
    
    def _create_md_directory_structure(self, pdf_dir: Path, md_dir: Path):
        """创建MD目录结构"""
        self.logger.info(f"创建MD目录结构: {md_dir}")
        
        for root, dirs, files in os.walk(pdf_dir):
            # 跳过metadata目录
            if 'metadata' in root:
                continue
                
            # 创建对应的MD目录
            relative_path = Path(root).relative_to(pdf_dir)
            md_target_dir = md_dir / relative_path
            md_target_dir.mkdir(parents=True, exist_ok=True)
            
            self.logger.debug(f"创建目录: {md_target_dir}")
    
    def _find_document_pairs(self, pdf_dir: Path, md_dir: Path) -> List[Tuple[Path, Path]]:
        """查找文档对"""
        pairs = []

        # 遍历PDF目录
        for pdf_file in pdf_dir.rglob('*.pdf'):
            # 跳过metadata目录中的文件
            if 'metadata' in str(pdf_file):
                continue

            # 构造对应的MD文件路径
            relative_path = pdf_file.relative_to(pdf_dir)
            md_file = md_dir / relative_path.with_suffix('.md')

            pairs.append((pdf_file, md_file))

        # 同时查找只有MD文件的情况（MD文件存在但PDF文件不存在）
        if md_dir.exists():
            for md_file in md_dir.rglob('*.md'):
                # 跳过metadata目录中的文件
                if 'metadata' in str(md_file):
                    continue

                # 构造对应的PDF文件路径
                relative_path = md_file.relative_to(md_dir)
                pdf_file = pdf_dir / relative_path.with_suffix('.pdf')

                # 检查是否已经在pairs中
                pair_exists = any(p[1] == md_file for p in pairs)
                if not pair_exists:
                    pairs.append((pdf_file, md_file))

        self.logger.info(f"找到 {len(pairs)} 个文档对")
        return pairs
    
    def _process_document_pair(self, pdf_path: Path, md_path: Path) -> Dict[str, Any]:
        """处理单个文档对"""
        result = {
            'timestamp': datetime.now().isoformat(),
            'pdf_path': str(pdf_path),
            'md_path': str(md_path),
            'pdf_exists': pdf_path.exists(),
            'md_exists': md_path.exists(),
            'pdf_valid': False,
            'md_valid': False,
            'pdf_content_length': 0,
            'md_content_length': 0,
            'similarity_score': 0.0,
            'completeness_score': 0.0,
            'quality_assessment': 'UNKNOWN',
            'errors': [],
            'recommendations': []
        }
        
        # 处理PDF文件
        if result['pdf_exists']:
            pdf_result = self._validate_pdf_content(pdf_path)
            result.update({
                'pdf_valid': pdf_result['valid'],
                'pdf_content_length': pdf_result['content_length'],
                'pdf_page_count': pdf_result.get('page_count', 0),
                'pdf_errors': pdf_result.get('errors', [])
            })
            if pdf_result.get('errors'):
                result['errors'].extend([f"PDF: {e}" for e in pdf_result['errors']])
        
        # 处理MD文件
        if result['md_exists']:
            md_result = self._validate_md_content(md_path)
            result.update({
                'md_valid': md_result['valid'],
                'md_content_length': md_result['content_length'],
                'md_section_count': md_result.get('section_count', 0),
                'md_errors': md_result.get('errors', [])
            })
            if md_result.get('errors'):
                result['errors'].extend([f"MD: {e}" for e in md_result['errors']])
        
        # 内容相似度比较
        if result['pdf_valid'] and result['md_valid']:
            similarity = self._calculate_content_similarity(
                pdf_result.get('content', ''),
                md_result.get('content', '')
            )
            result['similarity_score'] = similarity
            
            # 完整性评估
            completeness = self._assess_completeness(pdf_result, md_result)
            result['completeness_score'] = completeness
        
        # 质量评估
        result['quality_assessment'] = self._assess_quality(result)
        result['recommendations'] = self._generate_recommendations(result)
        
        return result

    def _validate_pdf_content(self, pdf_path: Path) -> Dict[str, Any]:
        """验证PDF内容"""
        result = {
            'valid': False,
            'content': '',
            'content_length': 0,
            'page_count': 0,
            'errors': []
        }

        # 首先检查文件是否存在
        if not pdf_path.exists():
            result['errors'].append(f"PDF文件不存在: {pdf_path}")
            return result

        # 检查PDF处理库
        if not PDF_AVAILABLE:
            # 尝试重新导入库
            try:
                import pdfplumber
                global PDF_LIBS, PDF_AVAILABLE
                if 'pdfplumber' not in PDF_LIBS:
                    PDF_LIBS.append('pdfplumber')
                PDF_AVAILABLE = True
            except ImportError:
                try:
                    import PyPDF2
                    if 'PyPDF2' not in PDF_LIBS:
                        PDF_LIBS.append('PyPDF2')
                    PDF_AVAILABLE = True
                except ImportError:
                    result['errors'].append("PDF处理库不可用，请安装: pip install pdfplumber PyPDF2")
                    return result

        # 尝试不同的PDF处理方法
        content_extracted = False

        # 方法1: 使用pdfplumber
        if 'pdfplumber' in PDF_LIBS and not content_extracted:
            try:
                import pdfplumber
                self.logger.info(f"使用pdfplumber处理PDF: {pdf_path}")
                with pdfplumber.open(pdf_path) as pdf:
                    result['page_count'] = len(pdf.pages)
                    content_parts = []

                    for page_num, page in enumerate(pdf.pages, 1):
                        try:
                            page_text = page.extract_text()
                            if page_text and page_text.strip():
                                content_parts.append(page_text.strip())
                            else:
                                self.logger.warning(f"第{page_num}页无文本内容")
                        except Exception as e:
                            self.logger.error(f"第{page_num}页提取失败: {e}")
                            result['errors'].append(f"第{page_num}页提取失败: {e}")

                    result['content'] = '\n\n'.join(content_parts)
                    result['content_length'] = len(result['content'])
                    content_extracted = True
                    self.logger.info(f"pdfplumber成功提取内容，长度: {result['content_length']}")

            except Exception as e:
                self.logger.error(f"pdfplumber处理失败: {e}")
                result['errors'].append(f"pdfplumber处理失败: {e}")

        # 方法2: 使用PyPDF2作为备用
        if 'PyPDF2' in PDF_LIBS and not content_extracted:
            try:
                import PyPDF2
                self.logger.info(f"使用PyPDF2处理PDF: {pdf_path}")
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    result['page_count'] = len(pdf_reader.pages)
                    content_parts = []

                    for page_num, page in enumerate(pdf_reader.pages, 1):
                        try:
                            page_text = page.extract_text()
                            if page_text and page_text.strip():
                                content_parts.append(page_text.strip())
                            else:
                                self.logger.warning(f"第{page_num}页无文本内容")
                        except Exception as e:
                            self.logger.error(f"第{page_num}页提取失败: {e}")
                            result['errors'].append(f"第{page_num}页提取失败: {e}")

                    result['content'] = '\n\n'.join(content_parts)
                    result['content_length'] = len(result['content'])
                    content_extracted = True
                    self.logger.info(f"PyPDF2成功提取内容，长度: {result['content_length']}")

            except Exception as e:
                self.logger.error(f"PyPDF2处理失败: {e}")
                result['errors'].append(f"PyPDF2处理失败: {e}")

        # 方法3: 基本文件信息提取（即使无法提取文本内容）
        if not content_extracted:
            try:
                # 至少尝试获取文件基本信息
                file_size = pdf_path.stat().st_size
                if file_size > 0:
                    result['errors'].append(f"PDF文件存在但无法提取文本内容，文件大小: {file_size} bytes")
                    # 设置一个最小的有效性，表示文件存在但内容无法读取
                    result['valid'] = False
                else:
                    result['errors'].append("PDF文件为空")
            except Exception as e:
                result['errors'].append(f"无法获取PDF文件信息: {e}")

        # 验证内容质量
        if content_extracted:
            if result['content_length'] < self.quality_thresholds['min_content_length']:
                result['errors'].append(f"内容长度不足: {result['content_length']} < {self.quality_thresholds['min_content_length']}")
                # 即使内容长度不足，如果提取到了内容，也标记为部分有效
                result['valid'] = result['content_length'] > 0
            else:
                result['valid'] = True
        else:
            result['errors'].append("所有PDF处理方法都失败了")

        return result

    def _validate_md_content(self, md_path: Path) -> Dict[str, Any]:
        """验证MD内容"""
        result = {
            'valid': False,
            'content': '',
            'content_length': 0,
            'section_count': 0,
            'errors': []
        }

        # 首先检查文件是否存在
        if not md_path.exists():
            result['errors'].append(f"MD文件不存在: {md_path}")
            return result

        # 检查MD处理库
        if not MD_AVAILABLE:
            # 尝试重新导入库
            try:
                import markdown
                import frontmatter
                global MD_AVAILABLE
                MD_AVAILABLE = True
            except ImportError:
                result['errors'].append("Markdown处理库不可用，请安装: pip install markdown python-frontmatter")
                return result

        try:
            self.logger.info(f"处理MD文件: {md_path}")

            # 尝试多种编码方式读取文件
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin-1']
            raw_content = None

            for encoding in encodings:
                try:
                    with open(md_path, 'r', encoding=encoding) as f:
                        raw_content = f.read()
                    self.logger.info(f"成功使用 {encoding} 编码读取MD文件")
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    self.logger.error(f"使用 {encoding} 编码读取失败: {e}")
                    continue

            if raw_content is None:
                result['errors'].append("无法使用任何编码读取MD文件")
                return result

            # 解析frontmatter
            try:
                import frontmatter
                post = frontmatter.loads(raw_content)
                result['content'] = post.content
                result['metadata'] = post.metadata
                self.logger.info("成功解析frontmatter")
            except Exception as e:
                self.logger.warning(f"frontmatter解析失败，使用原始内容: {e}")
                result['content'] = raw_content
                result['metadata'] = {}

            result['content_length'] = len(result['content'])

            # 统计章节数
            sections = re.findall(r'^#+\s+', result['content'], re.MULTILINE)
            result['section_count'] = len(sections)

            # 验证内容质量
            if result['content_length'] < self.quality_thresholds['min_content_length']:
                result['errors'].append(f"内容长度不足: {result['content_length']} < {self.quality_thresholds['min_content_length']}")
                # 即使内容长度不足，如果有内容，也标记为部分有效
                result['valid'] = result['content_length'] > 0
            else:
                result['valid'] = True

            self.logger.info(f"MD文件处理完成，内容长度: {result['content_length']}, 章节数: {result['section_count']}")

        except Exception as e:
            self.logger.error(f"MD文件处理失败: {e}")
            result['errors'].append(f"MD文件处理失败: {e}")

        except Exception as e:
            result['errors'].append(f"MD处理失败: {e}")

        return result

    def _calculate_content_similarity(self, pdf_content: str, md_content: str) -> float:
        """计算内容相似度"""
        try:
            # 文本预处理
            pdf_clean = self._clean_text(pdf_content)
            md_clean = self._clean_text(md_content)

            # 使用SequenceMatcher计算相似度
            similarity = SequenceMatcher(None, pdf_clean, md_clean).ratio()

            return round(similarity, 4)
        except Exception as e:
            self.logger.error(f"计算相似度失败: {e}")
            return 0.0

    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', text)
        # 转换为小写
        text = text.lower().strip()
        return text

    def _assess_completeness(self, pdf_result: Dict, md_result: Dict) -> float:
        """评估完整性"""
        try:
            pdf_length = pdf_result.get('content_length', 0)
            md_length = md_result.get('content_length', 0)

            if pdf_length == 0 and md_length == 0:
                return 0.0

            # 计算长度比率
            length_ratio = min(pdf_length, md_length) / max(pdf_length, md_length)

            # 考虑页面/章节数
            pdf_pages = pdf_result.get('page_count', 0)
            md_sections = md_result.get('section_count', 0)

            structure_score = 1.0
            if pdf_pages > 0 and md_sections > 0:
                structure_ratio = min(pdf_pages, md_sections) / max(pdf_pages, md_sections)
                structure_score = structure_ratio

            # 综合评分
            completeness = (length_ratio * 0.7 + structure_score * 0.3)
            return round(completeness, 4)

        except Exception as e:
            self.logger.error(f"评估完整性失败: {e}")
            return 0.0

    def _assess_quality(self, result: Dict) -> str:
        """评估整体质量"""
        try:
            # 基础检查
            if not result['pdf_exists'] and not result['md_exists']:
                return 'MISSING'

            if not result['pdf_valid'] and not result['md_valid']:
                return 'INVALID'

            # 单一格式有效
            if result['pdf_valid'] and not result['md_valid']:
                return 'PDF_ONLY'

            if result['md_valid'] and not result['pdf_valid']:
                return 'MD_ONLY'

            # 双格式都有效
            if result['pdf_valid'] and result['md_valid']:
                similarity = result.get('similarity_score', 0)
                completeness = result.get('completeness_score', 0)

                if similarity >= self.quality_thresholds['min_similarity'] and \
                   completeness >= self.quality_thresholds['min_completeness']:
                    return 'EXCELLENT'
                elif similarity >= 0.5 and completeness >= 0.6:
                    return 'GOOD'
                else:
                    return 'POOR'

            return 'UNKNOWN'

        except Exception as e:
            self.logger.error(f"质量评估失败: {e}")
            return 'ERROR'

    def _generate_recommendations(self, result: Dict) -> List[str]:
        """生成改进建议"""
        recommendations = []

        try:
            quality = result['quality_assessment']

            if quality == 'MISSING':
                recommendations.append("需要创建PDF和MD文件")
            elif quality == 'INVALID':
                recommendations.append("需要修复文件格式或内容错误")
            elif quality == 'PDF_ONLY':
                recommendations.append("建议创建对应的MD文件")
                recommendations.append("可以使用PDF转MD工具进行转换")
            elif quality == 'MD_ONLY':
                recommendations.append("建议获取原始PDF文件")
            elif quality == 'POOR':
                similarity = result.get('similarity_score', 0)
                completeness = result.get('completeness_score', 0)

                if similarity < self.quality_thresholds['min_similarity']:
                    recommendations.append(f"内容相似度过低({similarity:.2f})，建议重新校对MD文件")

                if completeness < self.quality_thresholds['min_completeness']:
                    recommendations.append(f"完整性不足({completeness:.2f})，建议补充缺失内容")

            # 检查具体错误
            if result.get('errors'):
                recommendations.append("需要修复以下错误:")
                recommendations.extend([f"  - {error}" for error in result['errors'][:3]])

        except Exception as e:
            self.logger.error(f"生成建议失败: {e}")
            recommendations.append("无法生成建议，请检查日志")

        return recommendations

    def _save_validation_report(self):
        """保存验证报告到Excel文件"""
        try:
            if not self.validation_results:
                self.logger.warning("没有验证结果可保存")
                return

            # 准备数据
            report_data = []
            for result in self.validation_results:
                row = {
                    '时间戳': result['timestamp'],
                    'PDF路径': result['pdf_path'],
                    'MD路径': result['md_path'],
                    'PDF存在': result['pdf_exists'],
                    'MD存在': result['md_exists'],
                    'PDF有效': result['pdf_valid'],
                    'MD有效': result['md_valid'],
                    'PDF内容长度': result['pdf_content_length'],
                    'MD内容长度': result['md_content_length'],
                    '相似度分数': result['similarity_score'],
                    '完整性分数': result['completeness_score'],
                    '质量评估': result['quality_assessment'],
                    '错误信息': '; '.join(result['errors']) if result['errors'] else '',
                    '改进建议': '; '.join(result['recommendations']) if result['recommendations'] else ''
                }
                report_data.append(row)

            # 创建DataFrame
            df = pd.DataFrame(report_data)

            # 保存到Excel（追加模式）
            if self.report_file.exists():
                # 读取现有数据
                existing_df = pd.read_excel(self.report_file)
                # 合并数据
                combined_df = pd.concat([existing_df, df], ignore_index=True)
                # 去重（基于PDF和MD路径）
                combined_df = combined_df.drop_duplicates(subset=['PDF路径', 'MD路径'], keep='last')
            else:
                combined_df = df

            # 保存到Excel
            with pd.ExcelWriter(self.report_file, engine='openpyxl') as writer:
                combined_df.to_excel(writer, sheet_name='验证报告', index=False)

                # 添加统计汇总表
                summary_data = self._generate_summary_statistics(combined_df)
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='统计汇总', index=False)

            self.logger.info(f"验证报告已保存到: {self.report_file}")

        except Exception as e:
            self.logger.error(f"保存验证报告失败: {e}")

    def _generate_summary_statistics(self, df: pd.DataFrame) -> List[Dict]:
        """生成统计汇总"""
        try:
            total_count = len(df)

            stats = [
                {'指标': '总文档对数', '数值': total_count, '百分比': '100.00%'},
                {'指标': 'PDF存在', '数值': df['PDF存在'].sum(), '百分比': f"{df['PDF存在'].sum()/total_count*100:.2f}%"},
                {'指标': 'MD存在', '数值': df['MD存在'].sum(), '百分比': f"{df['MD存在'].sum()/total_count*100:.2f}%"},
                {'指标': 'PDF有效', '数值': df['PDF有效'].sum(), '百分比': f"{df['PDF有效'].sum()/total_count*100:.2f}%"},
                {'指标': 'MD有效', '数值': df['MD有效'].sum(), '百分比': f"{df['MD有效'].sum()/total_count*100:.2f}%"},
                {'指标': '双格式都有效', '数值': (df['PDF有效'] & df['MD有效']).sum(), '百分比': f"{(df['PDF有效'] & df['MD有效']).sum()/total_count*100:.2f}%"},
            ]

            # 质量分布统计
            quality_counts = df['质量评估'].value_counts()
            for quality, count in quality_counts.items():
                stats.append({
                    '指标': f'质量-{quality}',
                    '数值': count,
                    '百分比': f"{count/total_count*100:.2f}%"
                })

            # 相似度统计
            if '相似度分数' in df.columns:
                similarity_scores = df['相似度分数'].dropna()
                if len(similarity_scores) > 0:
                    stats.extend([
                        {'指标': '平均相似度', '数值': f"{similarity_scores.mean():.4f}", '百分比': ''},
                        {'指标': '最高相似度', '数值': f"{similarity_scores.max():.4f}", '百分比': ''},
                        {'指标': '最低相似度', '数值': f"{similarity_scores.min():.4f}", '百分比': ''},
                    ])

            return stats

        except Exception as e:
            self.logger.error(f"生成统计汇总失败: {e}")
            return [{'指标': '统计失败', '数值': str(e), '百分比': ''}]
