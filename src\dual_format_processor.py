#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
双格式文档处理和校核系统
支持PDF和MD文件的并存处理、内容校核和质量评估
"""

import os
import sys
import json
import logging
import hashlib
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import numpy as np
from difflib import SequenceMatcher
import re

# PDF处理
try:
    import pdfplumber
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    logging.warning("PDF处理库不可用，请安装: pip install pdfplumber PyPDF2")

# MD处理
try:
    import markdown
    import frontmatter
    MD_AVAILABLE = True
except ImportError:
    MD_AVAILABLE = False
    logging.warning("Markdown处理库不可用，请安装: pip install markdown python-frontmatter")

class DualFormatProcessor:
    """双格式文档处理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化双格式处理器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 输出目录设置
        self.output_dir = Path(self.config.get('output_dir', 'data/dual_format_reports'))
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 校核结果文件
        self.report_file = self.output_dir / 'dual_format_validation_report.xlsx'
        
        # 质量评估阈值
        self.quality_thresholds = {
            'min_content_length': 100,      # 最小内容长度
            'min_similarity': 0.7,          # PDF和MD最小相似度
            'max_error_rate': 0.1,          # 最大错误率
            'min_completeness': 0.8         # 最小完整性
        }
        
        # 初始化报告数据
        self.validation_results = []
        
    def process_dual_format_documents(self, base_dir: str) -> Dict[str, Any]:
        """
        处理双格式文档目录
        
        Args:
            base_dir: 基础目录路径
            
        Returns:
            Dict: 处理结果统计
        """
        self.logger.info(f"开始处理双格式文档目录: {base_dir}")
        
        pdf_dir = Path(base_dir)
        md_dir = Path(str(base_dir) + '_MD')
        
        # 确保MD目录存在
        if not md_dir.exists():
            self.logger.warning(f"MD目录不存在: {md_dir}")
            self._create_md_directory_structure(pdf_dir, md_dir)
        
        # 扫描文档对
        document_pairs = self._find_document_pairs(pdf_dir, md_dir)
        
        # 处理每个文档对
        results = {
            'total_pairs': len(document_pairs),
            'processed': 0,
            'pdf_valid': 0,
            'md_valid': 0,
            'both_valid': 0,
            'similarity_passed': 0,
            'errors': []
        }
        
        for pdf_path, md_path in document_pairs:
            try:
                result = self._process_document_pair(pdf_path, md_path)
                self.validation_results.append(result)
                
                results['processed'] += 1
                if result['pdf_valid']:
                    results['pdf_valid'] += 1
                if result['md_valid']:
                    results['md_valid'] += 1
                if result['pdf_valid'] and result['md_valid']:
                    results['both_valid'] += 1
                if result['similarity_score'] >= self.quality_thresholds['min_similarity']:
                    results['similarity_passed'] += 1
                    
            except Exception as e:
                error_msg = f"处理文档对失败 {pdf_path} <-> {md_path}: {e}"
                self.logger.error(error_msg)
                results['errors'].append(error_msg)
        
        # 保存报告
        self._save_validation_report()
        
        self.logger.info(f"双格式文档处理完成: {results}")
        return results
    
    def _create_md_directory_structure(self, pdf_dir: Path, md_dir: Path):
        """创建MD目录结构"""
        self.logger.info(f"创建MD目录结构: {md_dir}")
        
        for root, dirs, files in os.walk(pdf_dir):
            # 跳过metadata目录
            if 'metadata' in root:
                continue
                
            # 创建对应的MD目录
            relative_path = Path(root).relative_to(pdf_dir)
            md_target_dir = md_dir / relative_path
            md_target_dir.mkdir(parents=True, exist_ok=True)
            
            self.logger.debug(f"创建目录: {md_target_dir}")
    
    def _find_document_pairs(self, pdf_dir: Path, md_dir: Path) -> List[Tuple[Path, Path]]:
        """查找文档对"""
        pairs = []
        
        # 遍历PDF目录
        for pdf_file in pdf_dir.rglob('*.pdf'):
            # 跳过metadata目录中的文件
            if 'metadata' in str(pdf_file):
                continue
                
            # 构造对应的MD文件路径
            relative_path = pdf_file.relative_to(pdf_dir)
            md_file = md_dir / relative_path.with_suffix('.md')
            
            pairs.append((pdf_file, md_file))
            
        self.logger.info(f"找到 {len(pairs)} 个文档对")
        return pairs
    
    def _process_document_pair(self, pdf_path: Path, md_path: Path) -> Dict[str, Any]:
        """处理单个文档对"""
        result = {
            'timestamp': datetime.now().isoformat(),
            'pdf_path': str(pdf_path),
            'md_path': str(md_path),
            'pdf_exists': pdf_path.exists(),
            'md_exists': md_path.exists(),
            'pdf_valid': False,
            'md_valid': False,
            'pdf_content_length': 0,
            'md_content_length': 0,
            'similarity_score': 0.0,
            'completeness_score': 0.0,
            'quality_assessment': 'UNKNOWN',
            'errors': [],
            'recommendations': []
        }
        
        # 处理PDF文件
        if result['pdf_exists']:
            pdf_result = self._validate_pdf_content(pdf_path)
            result.update({
                'pdf_valid': pdf_result['valid'],
                'pdf_content_length': pdf_result['content_length'],
                'pdf_page_count': pdf_result.get('page_count', 0),
                'pdf_errors': pdf_result.get('errors', [])
            })
            if pdf_result.get('errors'):
                result['errors'].extend([f"PDF: {e}" for e in pdf_result['errors']])
        
        # 处理MD文件
        if result['md_exists']:
            md_result = self._validate_md_content(md_path)
            result.update({
                'md_valid': md_result['valid'],
                'md_content_length': md_result['content_length'],
                'md_section_count': md_result.get('section_count', 0),
                'md_errors': md_result.get('errors', [])
            })
            if md_result.get('errors'):
                result['errors'].extend([f"MD: {e}" for e in md_result['errors']])
        
        # 内容相似度比较
        if result['pdf_valid'] and result['md_valid']:
            similarity = self._calculate_content_similarity(
                pdf_result.get('content', ''),
                md_result.get('content', '')
            )
            result['similarity_score'] = similarity
            
            # 完整性评估
            completeness = self._assess_completeness(pdf_result, md_result)
            result['completeness_score'] = completeness
        
        # 质量评估
        result['quality_assessment'] = self._assess_quality(result)
        result['recommendations'] = self._generate_recommendations(result)
        
        return result

    def _validate_pdf_content(self, pdf_path: Path) -> Dict[str, Any]:
        """验证PDF内容"""
        result = {
            'valid': False,
            'content': '',
            'content_length': 0,
            'page_count': 0,
            'errors': []
        }

        if not PDF_AVAILABLE:
            result['errors'].append("PDF处理库不可用")
            return result

        try:
            # 使用pdfplumber提取内容
            with pdfplumber.open(pdf_path) as pdf:
                result['page_count'] = len(pdf.pages)
                content_parts = []

                for page_num, page in enumerate(pdf.pages, 1):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            content_parts.append(page_text.strip())
                        else:
                            result['errors'].append(f"第{page_num}页无法提取文本")
                    except Exception as e:
                        result['errors'].append(f"第{page_num}页提取失败: {e}")

                result['content'] = '\n\n'.join(content_parts)
                result['content_length'] = len(result['content'])

                # 验证内容质量
                if result['content_length'] < self.quality_thresholds['min_content_length']:
                    result['errors'].append(f"内容长度不足: {result['content_length']} < {self.quality_thresholds['min_content_length']}")
                else:
                    result['valid'] = True

        except Exception as e:
            result['errors'].append(f"PDF处理失败: {e}")

        return result

    def _validate_md_content(self, md_path: Path) -> Dict[str, Any]:
        """验证MD内容"""
        result = {
            'valid': False,
            'content': '',
            'content_length': 0,
            'section_count': 0,
            'errors': []
        }

        if not MD_AVAILABLE:
            result['errors'].append("Markdown处理库不可用")
            return result

        try:
            if md_path.exists():
                # 读取MD文件
                with open(md_path, 'r', encoding='utf-8') as f:
                    raw_content = f.read()

                # 解析frontmatter
                try:
                    post = frontmatter.loads(raw_content)
                    result['content'] = post.content
                    result['metadata'] = post.metadata
                except:
                    result['content'] = raw_content
                    result['metadata'] = {}

                result['content_length'] = len(result['content'])

                # 统计章节数
                sections = re.findall(r'^#+\s+', result['content'], re.MULTILINE)
                result['section_count'] = len(sections)

                # 验证内容质量
                if result['content_length'] < self.quality_thresholds['min_content_length']:
                    result['errors'].append(f"内容长度不足: {result['content_length']} < {self.quality_thresholds['min_content_length']}")
                else:
                    result['valid'] = True
            else:
                result['errors'].append("MD文件不存在")

        except Exception as e:
            result['errors'].append(f"MD处理失败: {e}")

        return result

    def _calculate_content_similarity(self, pdf_content: str, md_content: str) -> float:
        """计算内容相似度"""
        try:
            # 文本预处理
            pdf_clean = self._clean_text(pdf_content)
            md_clean = self._clean_text(md_content)

            # 使用SequenceMatcher计算相似度
            similarity = SequenceMatcher(None, pdf_clean, md_clean).ratio()

            return round(similarity, 4)
        except Exception as e:
            self.logger.error(f"计算相似度失败: {e}")
            return 0.0

    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', text)
        # 转换为小写
        text = text.lower().strip()
        return text

    def _assess_completeness(self, pdf_result: Dict, md_result: Dict) -> float:
        """评估完整性"""
        try:
            pdf_length = pdf_result.get('content_length', 0)
            md_length = md_result.get('content_length', 0)

            if pdf_length == 0 and md_length == 0:
                return 0.0

            # 计算长度比率
            length_ratio = min(pdf_length, md_length) / max(pdf_length, md_length)

            # 考虑页面/章节数
            pdf_pages = pdf_result.get('page_count', 0)
            md_sections = md_result.get('section_count', 0)

            structure_score = 1.0
            if pdf_pages > 0 and md_sections > 0:
                structure_ratio = min(pdf_pages, md_sections) / max(pdf_pages, md_sections)
                structure_score = structure_ratio

            # 综合评分
            completeness = (length_ratio * 0.7 + structure_score * 0.3)
            return round(completeness, 4)

        except Exception as e:
            self.logger.error(f"评估完整性失败: {e}")
            return 0.0

    def _assess_quality(self, result: Dict) -> str:
        """评估整体质量"""
        try:
            # 基础检查
            if not result['pdf_exists'] and not result['md_exists']:
                return 'MISSING'

            if not result['pdf_valid'] and not result['md_valid']:
                return 'INVALID'

            # 单一格式有效
            if result['pdf_valid'] and not result['md_valid']:
                return 'PDF_ONLY'

            if result['md_valid'] and not result['pdf_valid']:
                return 'MD_ONLY'

            # 双格式都有效
            if result['pdf_valid'] and result['md_valid']:
                similarity = result.get('similarity_score', 0)
                completeness = result.get('completeness_score', 0)

                if similarity >= self.quality_thresholds['min_similarity'] and \
                   completeness >= self.quality_thresholds['min_completeness']:
                    return 'EXCELLENT'
                elif similarity >= 0.5 and completeness >= 0.6:
                    return 'GOOD'
                else:
                    return 'POOR'

            return 'UNKNOWN'

        except Exception as e:
            self.logger.error(f"质量评估失败: {e}")
            return 'ERROR'

    def _generate_recommendations(self, result: Dict) -> List[str]:
        """生成改进建议"""
        recommendations = []

        try:
            quality = result['quality_assessment']

            if quality == 'MISSING':
                recommendations.append("需要创建PDF和MD文件")
            elif quality == 'INVALID':
                recommendations.append("需要修复文件格式或内容错误")
            elif quality == 'PDF_ONLY':
                recommendations.append("建议创建对应的MD文件")
                recommendations.append("可以使用PDF转MD工具进行转换")
            elif quality == 'MD_ONLY':
                recommendations.append("建议获取原始PDF文件")
            elif quality == 'POOR':
                similarity = result.get('similarity_score', 0)
                completeness = result.get('completeness_score', 0)

                if similarity < self.quality_thresholds['min_similarity']:
                    recommendations.append(f"内容相似度过低({similarity:.2f})，建议重新校对MD文件")

                if completeness < self.quality_thresholds['min_completeness']:
                    recommendations.append(f"完整性不足({completeness:.2f})，建议补充缺失内容")

            # 检查具体错误
            if result.get('errors'):
                recommendations.append("需要修复以下错误:")
                recommendations.extend([f"  - {error}" for error in result['errors'][:3]])

        except Exception as e:
            self.logger.error(f"生成建议失败: {e}")
            recommendations.append("无法生成建议，请检查日志")

        return recommendations

    def _save_validation_report(self):
        """保存验证报告到Excel文件"""
        try:
            if not self.validation_results:
                self.logger.warning("没有验证结果可保存")
                return

            # 准备数据
            report_data = []
            for result in self.validation_results:
                row = {
                    '时间戳': result['timestamp'],
                    'PDF路径': result['pdf_path'],
                    'MD路径': result['md_path'],
                    'PDF存在': result['pdf_exists'],
                    'MD存在': result['md_exists'],
                    'PDF有效': result['pdf_valid'],
                    'MD有效': result['md_valid'],
                    'PDF内容长度': result['pdf_content_length'],
                    'MD内容长度': result['md_content_length'],
                    '相似度分数': result['similarity_score'],
                    '完整性分数': result['completeness_score'],
                    '质量评估': result['quality_assessment'],
                    '错误信息': '; '.join(result['errors']) if result['errors'] else '',
                    '改进建议': '; '.join(result['recommendations']) if result['recommendations'] else ''
                }
                report_data.append(row)

            # 创建DataFrame
            df = pd.DataFrame(report_data)

            # 保存到Excel（追加模式）
            if self.report_file.exists():
                # 读取现有数据
                existing_df = pd.read_excel(self.report_file)
                # 合并数据
                combined_df = pd.concat([existing_df, df], ignore_index=True)
                # 去重（基于PDF和MD路径）
                combined_df = combined_df.drop_duplicates(subset=['PDF路径', 'MD路径'], keep='last')
            else:
                combined_df = df

            # 保存到Excel
            with pd.ExcelWriter(self.report_file, engine='openpyxl') as writer:
                combined_df.to_excel(writer, sheet_name='验证报告', index=False)

                # 添加统计汇总表
                summary_data = self._generate_summary_statistics(combined_df)
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='统计汇总', index=False)

            self.logger.info(f"验证报告已保存到: {self.report_file}")

        except Exception as e:
            self.logger.error(f"保存验证报告失败: {e}")

    def _generate_summary_statistics(self, df: pd.DataFrame) -> List[Dict]:
        """生成统计汇总"""
        try:
            total_count = len(df)

            stats = [
                {'指标': '总文档对数', '数值': total_count, '百分比': '100.00%'},
                {'指标': 'PDF存在', '数值': df['PDF存在'].sum(), '百分比': f"{df['PDF存在'].sum()/total_count*100:.2f}%"},
                {'指标': 'MD存在', '数值': df['MD存在'].sum(), '百分比': f"{df['MD存在'].sum()/total_count*100:.2f}%"},
                {'指标': 'PDF有效', '数值': df['PDF有效'].sum(), '百分比': f"{df['PDF有效'].sum()/total_count*100:.2f}%"},
                {'指标': 'MD有效', '数值': df['MD有效'].sum(), '百分比': f"{df['MD有效'].sum()/total_count*100:.2f}%"},
                {'指标': '双格式都有效', '数值': (df['PDF有效'] & df['MD有效']).sum(), '百分比': f"{(df['PDF有效'] & df['MD有效']).sum()/total_count*100:.2f}%"},
            ]

            # 质量分布统计
            quality_counts = df['质量评估'].value_counts()
            for quality, count in quality_counts.items():
                stats.append({
                    '指标': f'质量-{quality}',
                    '数值': count,
                    '百分比': f"{count/total_count*100:.2f}%"
                })

            # 相似度统计
            if '相似度分数' in df.columns:
                similarity_scores = df['相似度分数'].dropna()
                if len(similarity_scores) > 0:
                    stats.extend([
                        {'指标': '平均相似度', '数值': f"{similarity_scores.mean():.4f}", '百分比': ''},
                        {'指标': '最高相似度', '数值': f"{similarity_scores.max():.4f}", '百分比': ''},
                        {'指标': '最低相似度', '数值': f"{similarity_scores.min():.4f}", '百分比': ''},
                    ])

            return stats

        except Exception as e:
            self.logger.error(f"生成统计汇总失败: {e}")
            return [{'指标': '统计失败', '数值': str(e), '百分比': ''}]
