#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MD向量处理器GUI模块
"""

import sys  # 添加这行导入语句
import os
import logging
from pathlib import Path

# 设置日志
logger = logging.getLogger(__name__)

def run_gui():
    """运行GUI应用"""
    try:
        # 尝试修复PyQt6 DLL路径问题
        import os
        import sys

        # 添加PyQt6 DLL路径
        try:
            pyqt6_bin = os.path.join(sys.prefix, 'Lib', 'site-packages', 'PyQt6', 'Qt6', 'bin')
            if os.path.exists(pyqt6_bin):
                os.environ['PATH'] = pyqt6_bin + os.pathsep + os.environ.get('PATH', '')
                print(f"✓ 已添加PyQt6 DLL路径: {pyqt6_bin}")
        except Exception as e:
            print(f"WARNING - 设置PyQt6路径时出错: {e}")

        # 导入必要的模块
        try:
            from PyQt6.QtWidgets import QApplication
            print("✓ 成功导入PyQt6")
        except ImportError as e:
            print(f"WARNING - PyQt6导入失败: {e}")
            try:
                from PyQt5.QtWidgets import QApplication
                print("✓ 成功导入PyQt5")
            except ImportError as e2:
                print(f"ERROR - PyQt5也导入失败: {e2}")
                print("\n=== PyQt安装指南 ===")
                print("请选择以下解决方案之一:")
                print("1. 安装PyQt5 (推荐):")
                print("   pip install PyQt5")
                print("2. 修复PyQt6 DLL问题:")
                print("   - 重新安装PyQt6: pip uninstall PyQt6 && pip install PyQt6")
                print("   - 或安装Microsoft Visual C++ Redistributable")
                print("3. 使用命令行版本 (无GUI):")
                print("   python cli_run.py")
                print("==================")

                # 尝试提供命令行替代方案
                try:
                    print("\n正在尝试启动命令行版本...")
                    from ..cli import run_cli
                    run_cli()
                    return
                except:
                    pass

                raise ImportError("无法导入PyQt6或PyQt5，请按照上述指南安装")
        
        from .app import MainWindow
        
        # 创建应用
        app = QApplication(sys.argv)
        app.setApplicationName("MD向量处理器")
        
        # 设置全局异常处理
        def exception_hook(exctype, value, traceback):
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"未捕获的异常: {exctype.__name__}: {value}")
            logger.error("详细信息:", exc_info=(exctype, value, traceback))
            # 继续调用原始的异常处理器
            sys.__excepthook__(exctype, value, traceback)
        
        sys.excepthook = exception_hook
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        # 运行应用
        return app.exec()
        
    except Exception as e:
        logger.error(f"运行GUI时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise

# 导出模块
__all__ = ['run_gui']




