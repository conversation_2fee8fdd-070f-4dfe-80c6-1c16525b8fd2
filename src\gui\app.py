#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GUI应用程序主模块
"""

import sys
import os
import logging
from pathlib import Path
from typing import Optional, List, Dict, Any

# 尝试导入 PyQt6，如果失败则使用 PyQt5
try:
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QPushButton, QLabel, QComboBox, QStatusBar, QMenuBar, QMenu,
        QToolBar, QSplitter, QTabWidget, QMessageBox, QFileDialog,
        QDockWidget
    )
    from PyQt6.QtCore import Qt, QSize, QTimer, QPropertyAnimation, QEasingCurve
    from PyQt6.QtGui import QIcon, QPixmap, QFont, QAction, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup
    QT_VERSION = 6
except ImportError:
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QPushButton, QLabel, QComboBox, QStatusBar, QMenuBar, QMenu,
        QToolBar, QSplitter, QTabWidget, QMessageBox, QFileDialog,
        QDockWidget, QActionGroup
    )
    from PyQt5.QtCore import Qt, QSize, QTimer, QPropertyAnimation, QEasingCurve
    from PyQt5.QtGui import QIcon, QPixmap, QFont
    from PyQt5.QtWidgets import QAction
    QT_VERSION = 5

# 导入自定义模块
from .i18n import Translator, Language
from .i18n.translator import get_translator
from .theme import ThemeManager, Theme
from .theme.theme_manager import get_theme_manager
from .widgets import (
    SidebarWidget, DashboardWidget, IndexWidget, VectorizeWidget,
    SearchWidget, VisualizeWidget, SettingsWidget, AboutWidget, RepairWidget
)
from .widgets.training_status import TrainingStatusWidget

logger = logging.getLogger(__name__)

INDEX_TYPES = {
    'basic': '基础索引',
    'ivf': 'IVF索引',
    'pq': 'PQ量化索引',
    'hybrid': '混合索引',
    'hnsw': 'HNSW索引'
}

class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        """初始化主窗口"""
        super().__init__()

        # 加载配置
        self.config = self._load_config()

        # 获取翻译器和主题管理器
        self.translator = get_translator()
        self.theme_manager = get_theme_manager()

        # 添加为观察者
        self.translator.add_observer(self)
        self.theme_manager.add_observer(self)

        # 设置窗口属性
        self.setWindowTitle(self.translator.get_text("app_title"))
        self.setMinimumSize(1024, 768)
        
        # 传递self作为parent
        self.vectorize_widget = VectorizeWidget(self.translator, self) 

        # 初始化UI
        self._init_ui()

        # 应用主题
        self.theme_manager.apply_theme(QApplication.instance())

    def _load_config(self):
        """加载配置文件"""
        try:
            from pathlib import Path
            from ..utils import ConfigUtils

            # 加载主配置
            config_path = Path('config/config.yaml')
            config = ConfigUtils.load_config(config_path)

            # 如果主配置加载失败，使用空字典
            if config is None:
                config = {}
                logger.warning(f"主配置文件 {config_path} 加载失败，使用默认配置")

            # 加载models.yaml配置（如果存在）
            models_config_path = Path("config/models.yaml")
            if models_config_path.exists():
                models_config = ConfigUtils.load_config(models_config_path)
                if models_config:
                    # 合并配置
                    config.update(models_config)
                    logger.info("已加载models.yaml配置")
                else:
                    logger.warning(f"models.yaml配置文件 {models_config_path} 加载失败")

            return config
        except Exception as e:
            logger.error(f"加载配置文件时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {}

    def _init_ui(self):
        """初始化UI组件"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局（改为垂直布局）
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建上部分布局（包含侧边栏和标签页）
        upper_widget = QWidget()
        upper_layout = QHBoxLayout(upper_widget)
        upper_layout.setContentsMargins(0, 0, 0, 0)
        upper_layout.setSpacing(0)

        # 创建侧边栏
        self.sidebar = SidebarWidget(self.translator)

        # 创建内容区域
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(10, 10, 10, 10)

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        self.tab_widget.setTabsClosable(False)
        self.tab_widget.setMovable(True)

        # 创建各个页面
        self.dashboard_widget = DashboardWidget(self.translator)
        self.index_widget = IndexWidget(self.translator)
        self.vectorize_widget = VectorizeWidget(self.translator)
        self.search_widget = SearchWidget(self.translator)
        self.visualize_widget = VisualizeWidget(self.translator)
        self.settings_widget = SettingsWidget(self.translator, self.theme_manager)
        self.repair_widget = RepairWidget(self.translator)
        self.about_widget = AboutWidget(self.translator)

        # 添加标签页
        self.tab_widget.addTab(self.dashboard_widget, self.translator.get_text("dashboard"))
        self.tab_widget.addTab(self.index_widget, self.translator.get_text("index"))
        self.tab_widget.addTab(self.vectorize_widget, self.translator.get_text("vectorize"))
        self.tab_widget.addTab(self.search_widget, self.translator.get_text("search"))
        self.tab_widget.addTab(self.visualize_widget, self.translator.get_text("visualize"))
        self.tab_widget.addTab(self.repair_widget, self.translator.get_text("repair_tool"))
        self.tab_widget.addTab(self.settings_widget, self.translator.get_text("settings"))
        self.tab_widget.addTab(self.about_widget, self.translator.get_text("about"))

        # 添加到内容布局
        self.content_layout.addWidget(self.tab_widget)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(self.sidebar)
        splitter.addWidget(self.content_widget)
        splitter.setStretchFactor(0, 1)  # 侧边栏
        splitter.setStretchFactor(1, 4)  # 内容区域

        # 添加到上部分布局
        upper_layout.addWidget(splitter)

        # 创建下部分（仅包含训练状态面板）
        self.training_status = TrainingStatusWidget()

        # 创建下部分容器
        lower_widget = QWidget()
        lower_layout = QVBoxLayout(lower_widget)
        lower_layout.setContentsMargins(10, 10, 10, 10)
        lower_layout.addWidget(self.training_status)

        # 添加到主布局
        main_layout.addWidget(upper_widget, stretch=4)  # 上部分占比更大
        main_layout.addWidget(lower_widget, stretch=1)  # 下部分占比较小

        # 创建菜单栏
        self._create_menu_bar()

        # 创建工具栏
        self._create_tool_bar()

        # 创建状态栏
        self._create_status_bar()

        # 连接信号和槽
        self._connect_signals()

    def _create_menu_bar(self):
        """创建菜单栏"""
        menu_bar = self.menuBar()

        # 文件菜单
        file_menu = menu_bar.addMenu(self.translator.get_text("file_menu"))

        open_action = QAction(self.translator.get_text("open_file"), self)
        open_action.triggered.connect(self._on_open_file)
        file_menu.addAction(open_action)

        save_action = QAction(self.translator.get_text("save_file"), self)
        save_action.triggered.connect(self._on_save_file)
        file_menu.addAction(save_action)

        save_as_action = QAction(self.translator.get_text("save_as"), self)
        save_as_action.triggered.connect(self._on_save_as)
        file_menu.addAction(save_as_action)

        file_menu.addSeparator()

        exit_action = QAction(self.translator.get_text("exit"), self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 编辑菜单
        edit_menu = menu_bar.addMenu(self.translator.get_text("edit_menu"))

        # 视图菜单
        view_menu = menu_bar.addMenu(self.translator.get_text("view_menu"))

        # 工具菜单
        tools_menu = menu_bar.addMenu(self.translator.get_text("tools_menu"))

        # 语言菜单
        language_menu = menu_bar.addMenu(self.translator.get_text("language_menu"))

        # 创建互斥的语言动作组
        lang_action_group = QActionGroup(self)
        lang_action_group.setExclusive(True)  # 设置为互斥

        # 添加支持的语言
        for lang in Language:
            lang_action = QAction(lang.display_name, self)
            lang_action.setCheckable(True)
            lang_action.setChecked(self.translator.current_language == lang)
            lang_action.triggered.connect(lambda checked, l=lang: self._on_language_changed(l))
            language_menu.addAction(lang_action)
            lang_action_group.addAction(lang_action)  # 添加到互斥组

        # 主题菜单
        theme_menu = view_menu.addMenu(self.translator.get_text("theme"))

        # 创建互斥的主题动作组
        theme_action_group = QActionGroup(self)
        theme_action_group.setExclusive(True)  # 设置为互斥

        # 添加支持的主题
        for theme in Theme:
            theme_action = QAction(theme.display_name, self)
            theme_action.setCheckable(True)
            theme_action.setChecked(self.theme_manager.current_theme == theme)
            theme_action.triggered.connect(lambda checked, t=theme: self._on_theme_changed(t))
            theme_menu.addAction(theme_action)
            theme_action_group.addAction(theme_action)  # 添加到互斥组

        # 帮助菜单
        help_menu = menu_bar.addMenu(self.translator.get_text("help_menu"))

        about_action = QAction(self.translator.get_text("about"), self)
        about_action.triggered.connect(lambda: self.tab_widget.setCurrentWidget(self.about_widget))
        help_menu.addAction(about_action)

    def _create_tool_bar(self):
        """创建工具栏"""
        tool_bar = self.addToolBar(self.translator.get_text("tools"))
        tool_bar.setMovable(True)
        tool_bar.setIconSize(QSize(24, 24))

        # 添加工具栏按钮
        open_action = QAction(self.translator.get_text("open_file"), self)
        open_action.triggered.connect(self._on_open_file)
        tool_bar.addAction(open_action)

        save_action = QAction(self.translator.get_text("save_file"), self)
        save_action.triggered.connect(self._on_save_file)
        tool_bar.addAction(save_action)

        tool_bar.addSeparator()

        settings_action = QAction(self.translator.get_text("settings"), self)
        settings_action.triggered.connect(lambda: self.tab_widget.setCurrentWidget(self.settings_widget))
        tool_bar.addAction(settings_action)

    def _create_status_bar(self):
        """创建状态栏"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # 添加状态标签
        self.status_label = QLabel(self.translator.get_text("status_ready"))
        status_bar.addWidget(self.status_label)

        # 添加语言标签
        self.language_label = QLabel(f"{self.translator.get_text('language')}: {self.translator.current_language.display_name}")
        status_bar.addPermanentWidget(self.language_label)

    def _connect_signals(self):
        """连接信号和槽"""
        # 侧边栏按钮点击
        self.sidebar.dashboard_button.clicked.connect(lambda: self.tab_widget.setCurrentWidget(self.dashboard_widget))
        self.sidebar.index_button.clicked.connect(lambda: self.tab_widget.setCurrentWidget(self.index_widget))
        self.sidebar.vectorize_button.clicked.connect(lambda: self.tab_widget.setCurrentWidget(self.vectorize_widget))
        self.sidebar.search_button.clicked.connect(lambda: self.tab_widget.setCurrentWidget(self.search_widget))
        self.sidebar.visualize_button.clicked.connect(lambda: self.tab_widget.setCurrentWidget(self.visualize_widget))
        self.sidebar.settings_button.clicked.connect(lambda: self.tab_widget.setCurrentWidget(self.settings_widget))
        self.sidebar.about_button.clicked.connect(lambda: self.tab_widget.setCurrentWidget(self.about_widget))

    def _on_open_file(self):
        """打开文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            self.translator.get_text("open_file"),
            "",
            "All Files (*)"
        )

        if file_path:
            self.status_label.setText(f"{self.translator.get_text('status_processing')} {file_path}")
            # TODO: 处理文件
            QTimer.singleShot(1000, lambda: self.status_label.setText(self.translator.get_text("status_ready")))

    def _on_save_file(self):
        """保存文件"""
        try:
            # 如果没有当前文件路径，则调用另存为
            if not hasattr(self, 'current_file_path') or not self.current_file_path:
                self._on_save_as()
                return

            # 获取当前活动标签页的内容
            current_widget = self.tab_widget.currentWidget()
            content = ""

            # 根据不同的标签页类型获取内容
            if hasattr(current_widget, 'get_content'):
                content = current_widget.get_content()
            elif hasattr(current_widget, 'toPlainText'):  # 如果是文本编辑器
                content = current_widget.toPlainText()
            else:
                # 如果无法获取内容，显示错误消息
                self.status_label.setText(self.translator.get_text("error_no_content"))
                return

            # 保存文件
            with open(self.current_file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # 更新状态栏
            self.status_label.setText(f"{self.translator.get_text('status_saved')} {self.current_file_path}")

        except Exception as e:
            # 显示错误消息
            self.status_label.setText(f"{self.translator.get_text('error_saving')}: {str(e)}")
            logger.error(f"保存文件时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _on_save_as(self):
        """另存为"""
        try:
            # 获取当前活动标签页的内容
            current_widget = self.tab_widget.currentWidget()
            content = ""

            # 根据不同的标签页类型获取内容
            if hasattr(current_widget, 'get_content'):
                content = current_widget.get_content()
            elif hasattr(current_widget, 'toPlainText'):  # 如果是文本编辑器
                content = current_widget.toPlainText()
            else:
                # 如果无法获取内容，显示错误消息
                self.status_label.setText(self.translator.get_text("error_no_content"))
                return

            # 打开保存文件对话框
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                self.translator.get_text("save_as"),
                "",
                "All Files (*);;"
                "Text Files (*.txt);;"
                "Markdown Files (*.md);;"
                "JSON Files (*.json);;"
                "CSV Files (*.csv);;"
                "Vector Files (*.vec)"
            )

            if file_path:
                # 保存文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                # 保存当前文件路径
                self.current_file_path = file_path

                # 更新状态栏
                self.status_label.setText(f"{self.translator.get_text('status_saved')} {file_path}")

                # 更新窗口标题
                self.setWindowTitle(f"{self.translator.get_text('app_title')} - {file_path}")

        except Exception as e:
            # 显示错误消息
            self.status_label.setText(f"{self.translator.get_text('error_saving')}: {str(e)}")
            logger.error(f"另存为文件时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _on_language_changed(self, language: Language):
        """语言变更处理"""
        try:
            logger.info(f"语言变更处理: {language.value}")

            # 直接更新窗口标题
            app_title = self.translator.translations.get(language, {}).get("app_title", "MD Vector Processor")
            logger.info(f"更新窗口标题为: {app_title}")
            self.setWindowTitle(app_title)

            # 设置当前语言
            self.translator.set_language(language)

            # 更新状态栏
            try:
                status_text = self.translator.translations.get(language, {}).get("status_ready", "Ready")
                logger.info(f"更新状态栏为: {status_text}")
                self.status_label.setText(status_text)

                language_text = self.translator.translations.get(language, {}).get("language", "Language")
                language_display = language.display_name
                logger.info(f"更新语言标签为: {language_text}: {language_display}")
                self.language_label.setText(f"{language_text}: {language_display}")
            except Exception as e:
                logger.error(f"更新状态栏时出错: {e}")

            # 清除并重新创建菜单栏
            try:
                logger.info("开始更新菜单")
                menu_bar = self.menuBar()
                menu_bar.clear()
                self._create_menu_bar()
                logger.info("菜单更新完成")
            except Exception as e:
                logger.error(f"更新菜单时出错: {e}")

            # 更新标签页标题
            try:
                if hasattr(self, 'tab_widget') and self.tab_widget is not None:
                    tab_count = self.tab_widget.count()
                    logger.info(f"开始更新 {tab_count} 个标签页")

                    # 更新标签页标题
                    tab_titles = [
                        "dashboard", "index", "vectorize", "search",
                        "visualize", "repair_tool", "settings", "about"
                    ]

                    for i in range(min(tab_count, len(tab_titles))):
                        try:
                            title_key = tab_titles[i]
                            new_title = self.translator.get_text(title_key)
                            self.tab_widget.setTabText(i, new_title)
                            logger.info(f"更新标签页 {i} 标题为: {new_title}")
                        except Exception as e:
                            logger.error(f"更新标签页 {i} 标题时出错: {e}")

                    # 遍历所有标签页组件，调用其语言更新方法
                    for i in range(tab_count):
                        try:
                            # 安全地获取标签页组件
                            widget = self.tab_widget.widget(i)

                            # 检查组件是否有效
                            if widget is not None:
                                widget_name = widget.__class__.__name__
                                logger.info(f"更新标签页组件 {i}: {widget_name}")

                                # 检查组件是否有语言变更方法
                                if hasattr(widget, 'on_language_changed'):
                                    widget.on_language_changed()
                                else:
                                    logger.warning(f"标签页 {i} ({widget_name}) 没有 on_language_changed 方法")
                            else:
                                logger.warning(f"标签页 {i} 组件为空")
                        except Exception as e:
                            # 捕获单个标签页更新的错误，但继续处理其他标签页
                            logger.error(f"更新标签页组件 {i} 时出错: {e}")
                            import traceback
                            logger.error(traceback.format_exc())
                else:
                    logger.warning("标签页组件不存在或为空")
            except Exception as e:
                logger.error(f"更新标签页时出错: {e}")
                import traceback
                logger.error(traceback.format_exc())

            logger.info("语言变更处理完成")
        except Exception as e:
            logger.error(f"语言变更处理出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _on_theme_changed(self, theme: Theme):
        """主题变更处理"""
        self.theme_manager.set_theme(theme)
        self.theme_manager.apply_theme(QApplication.instance())

    def on_language_changed(self):
        """语言变更回调 - 已在 _on_language_changed 方法中处理"""
        pass

    def on_theme_changed(self):
        """主题变更回调"""
        # 应用主题
        self.theme_manager.apply_theme(QApplication.instance())

    def _update_menus(self):
        """更新菜单文本"""
        try:
            logger.info("开始更新菜单")

            # 清除现有菜单栏
            menu_bar = self.menuBar()
            menu_bar.clear()

            # 重新创建菜单栏
            self._create_menu_bar()

            logger.info("菜单更新完成")
        except Exception as e:
            logger.error(f"更新菜单时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

def run_gui():
    """运行GUI应用程序"""
    import logging
    logger = logging.getLogger(__name__)

    try:
        print("正在初始化QApplication...")
        app = QApplication(sys.argv)

        # 设置应用程序属性
        print("正在设置应用程序属性...")
        app.setApplicationName("MD Vector Processor")
        app.setOrganizationName("MD Vector")
        app.setOrganizationDomain("mdvector.com")

        # 创建并显示主窗口
        print("正在创建主窗口...")
        main_window = MainWindow()

        # 初始化状态管理器
        print("正在初始化状态管理器...")
        from .state_manager import get_state_manager
        state_manager = get_state_manager()

        # 注册主要组件到状态管理器
        if hasattr(main_window, 'tab_widget'):
            for i in range(main_window.tab_widget.count()):
                widget = main_window.tab_widget.widget(i)
                tab_text = main_window.tab_widget.tabText(i)
                state_manager.register_component(tab_text, widget)

        # 自动选择最佳索引
        state_manager.auto_select_best_index()

        print("正在显示主窗口...")
        main_window.show()

        # 自动加载索引
        print("正在检查索引...")
        try:
            from pathlib import Path
            import pickle

            # 获取索引目录
            indices_dir = Path("data/indices")
            if not indices_dir.exists():
                # 创建索引目录
                indices_dir.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建索引目录: {indices_dir}")
                print(f"创建索引目录: {indices_dir}")

            # 获取所有索引文件
            index_files = list(indices_dir.glob("*.idx"))

            if index_files:
                # 检查索引文件是否有效
                valid_indices = []
                for idx_file in index_files:
                    meta_file = idx_file.with_suffix('.meta')
                    if meta_file.exists() and idx_file.stat().st_size > 0 and meta_file.stat().st_size > 0:
                        valid_indices.append(idx_file)
                    else:
                        logger.warning(f"发现无效索引文件: {idx_file}")
                        print(f"发现无效索引文件: {idx_file}")

                if valid_indices:
                    # 获取最新的有效索引文件
                    latest_index = max(valid_indices, key=lambda p: p.stat().st_mtime)

                    logger.info(f"找到最新索引: {latest_index}")
                    print(f"找到最新索引: {latest_index}")

                    # 延迟加载索引，确保GUI已完全初始化
                    def delayed_load_index():
                        try:
                            # 查找索引小部件
                            index_widget = None
                            if hasattr(main_window, 'tab_widget'):
                                for i in range(main_window.tab_widget.count()):
                                    widget = main_window.tab_widget.widget(i)
                                    if hasattr(widget, 'index_name_input'):  # 索引页面的特征
                                        index_widget = widget
                                        break

                            if index_widget:
                                # 尝试加载索引
                                success = index_widget._on_load_index(latest_index)
                                if success:
                                    # 更新状态栏
                                    main_window.status_label.setText(f"自动加载索引: {latest_index.stem}")
                                else:
                                    main_window.status_label.setText(f"自动加载索引失败: {latest_index.stem}")
                            else:
                                logger.warning("未找到索引小部件，无法自动加载索引")
                                print("未找到索引小部件，无法自动加载索引")
                        except Exception as e:
                            logger.error(f"延迟加载索引时出错: {e}")
                            print(f"延迟加载索引时出错: {e}")
                            import traceback
                            logger.error(traceback.format_exc())

                    # 使用QTimer延迟加载索引
                    QTimer.singleShot(2000, delayed_load_index)

                    # 更新状态栏
                    main_window.status_label.setText(f"准备加载索引: {latest_index.stem}")
                else:
                    logger.info("未找到有效的索引文件")
                    print("未找到有效的索引文件")
            else:
                logger.info("未找到索引文件")
                print("未找到索引文件")
        except Exception as e:
            print(f"自动加载索引时出错: {e}")
            import traceback
            traceback.print_exc()

        # 运行应用程序
        print("正在启动应用程序事件循环...")
        sys.exit(app.exec())
    except Exception as e:
        import traceback
        logger.error(f"运行GUI时出错: {e}")
        print(f"错误: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    run_gui()
