import json
import logging
import os
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class ParameterManager:
    """参数管理器，用于保存和加载参数设置"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = ParameterManager()
        return cls._instance
    
    def __init__(self):
        """初始化参数管理器"""
        self.params_dir = Path("data/params")
        self.params_dir.mkdir(parents=True, exist_ok=True)
        
        # 默认参数
        self.default_params = {
            'vectorization': {
                'model': "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
                'batch_size': 32,
                'vector_dimension': 768,  # 更新默认维度为768
                'normalize_vectors': True,
                'device': "cpu"
            },
            'fine_tuning': {
                'batch_size': 16,
                'learning_rate': 0.0001,
                'epochs': 3,
                'vector_dimension': 768,  # 更新默认维度为768
                'device': "cpu"
            },
            'indexing': {
                'index_type': "hybrid",
                'metric': "cosine",
                'quantization': "pq",
                'pq_m': 8,
                'pq_bits': 8
            }
        }
        
        # 当前参数
        self.current_params = self.default_params.copy()
        
        # 加载保存的参数
        self._load_params()
    
    def _load_params(self):
        """加载保存的参数"""
        params_file = self.params_dir / "last_params.json"
        if params_file.exists():
            try:
                with open(params_file, 'r', encoding='utf-8') as f:
                    saved_params = json.load(f)
                
                # 更新当前参数
                for category, params in saved_params.items():
                    if category in self.current_params:
                        self.current_params[category].update(params)
                
                logger.info("成功加载保存的参数设置")
            except Exception as e:
                logger.error(f"加载参数设置时出错: {e}")
    
    def save_params(self):
        """保存当前参数"""
        params_file = self.params_dir / "last_params.json"
        try:
            with open(params_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_params, f, ensure_ascii=False, indent=2)
            
            logger.info("成功保存参数设置")
        except Exception as e:
            logger.error(f"保存参数设置时出错: {e}")
    
    def get_params(self, category: str) -> Dict[str, Any]:
        """获取指定类别的参数"""
        return self.current_params.get(category, {}).copy()
    
    def set_params(self, category: str, params: Dict[str, Any]):
        """设置指定类别的参数"""
        if category in self.current_params:
            self.current_params[category].update(params)
            self.save_params()
    
    def get_param(self, category: str, param_name: str, default: Any = None) -> Any:
        """获取指定参数值"""
        category_params = self.current_params.get(category, {})
        return category_params.get(param_name, default)
    
    def set_param(self, category: str, param_name: str, value: Any):
        """设置指定参数值"""
        if category in self.current_params:
            self.current_params[category][param_name] = value
            self.save_params()
    
    def save_model_params(self, model_name: str, params: Dict[str, Any]):
        """保存模型特定的参数"""
        model_params_dir = self.params_dir / "models"
        model_params_dir.mkdir(exist_ok=True)
        
        # 清理模型名称，确保可以作为文件名
        safe_name = "".join(c if c.isalnum() or c in "._- " else "_" for c in model_name)
        model_params_file = model_params_dir / f"{safe_name}.json"
        
        try:
            with open(model_params_file, 'w', encoding='utf-8') as f:
                json.dump(params, f, ensure_ascii=False, indent=2)
            
            logger.info(f"成功保存模型 '{model_name}' 的参数设置")
        except Exception as e:
            logger.error(f"保存模型参数设置时出错: {e}")
    
    def load_model_params(self, model_name: str) -> Optional[Dict[str, Any]]:
        """加载模型特定的参数"""
        model_params_dir = self.params_dir / "models"
        if not model_params_dir.exists():
            return None
        
        # 清理模型名称，确保可以作为文件名
        safe_name = "".join(c if c.isalnum() or c in "._- " else "_" for c in model_name)
        model_params_file = model_params_dir / f"{safe_name}.json"
        
        if not model_params_file.exists():
            return None
        
        try:
            with open(model_params_file, 'r', encoding='utf-8') as f:
                params = json.load(f)
            
            logger.info(f"成功加载模型 '{model_name}' 的参数设置")
            return params
        except Exception as e:
            logger.error(f"加载模型参数设置时出错: {e}")
            return None