#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全局状态管理器

负责协调各组件间的状态同步和通信
"""

import logging
from typing import Dict, Any, List, Optional, Callable
from pathlib import Path
import time
from enum import Enum
from dataclasses import dataclass

# 尝试导入 PyQt6，如果失败则使用 PyQt5
try:
    from PyQt6.QtCore import QObject, pyqtSignal
    QT_VERSION = 6
except ImportError:
    from PyQt5.QtCore import QObject, pyqtSignal
    QT_VERSION = 5

class VectorizationState(Enum):
    IDLE = "idle"
    PROCESSING = "processing" 
    SUCCESS = "success"
    ERROR = "error"

@dataclass
class StateContext:
    state: VectorizationState
    message: str = ""
    progress: int = 0


class StateManager(QObject):
    """全局状态管理器"""
    
    # 信号定义
    index_created = pyqtSignal(str)  # 新索引创建
    index_updated = pyqtSignal(str)  # 索引更新
    vectors_added = pyqtSignal(str, int)  # 向量添加 (索引路径, 向量数量)
    model_changed = pyqtSignal(str)  # 模型变更
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        
        # 状态存储
        self._current_index = None
        self._current_model = None
        self._available_indices = {}
        self._available_models = {}
        self._index_stats = {}
        
        # 组件引用
        self._components = {}
        
        # 初始化
        self._refresh_indices()
        self._refresh_models()

        self._state = StateContext(VectorizationState.IDLE)
        self._observers: List[Callable[[StateContext], None]] = []
    
    def register_component(self, name: str, component: Any):
        """注册组件"""
        self._components[name] = component
        self.logger.info(f"注册组件: {name}")
    
    def get_component(self, name: str) -> Optional[Any]:
        """获取组件"""
        return self._components.get(name)
    
    def _refresh_indices(self):
        """刷新可用索引"""
        try:
            indices_dir = Path("data/indices")
            if not indices_dir.exists():
                return
            
            self._available_indices.clear()
            self._index_stats.clear()
            
            # 获取所有索引文件
            index_files = list(indices_dir.glob("*.idx"))
            
            for index_file in index_files:
                meta_file = index_file.with_suffix('.meta')
                if meta_file.exists():
                    try:
                        # 获取索引统计信息
                        stats = self._get_index_stats(index_file)
                        
                        self._available_indices[str(index_file)] = {
                            'path': str(index_file),
                            'name': index_file.stem,
                            'created_time': index_file.stat().st_ctime,
                            'modified_time': index_file.stat().st_mtime,
                            'size': index_file.stat().st_size,
                            'stats': stats
                        }
                        
                        self._index_stats[str(index_file)] = stats
                        
                    except Exception as e:
                        self.logger.warning(f"无法获取索引信息: {index_file}, {e}")
            
            self.logger.info(f"刷新了 {len(self._available_indices)} 个索引")
            
        except Exception as e:
            self.logger.error(f"刷新索引时出错: {e}")
    
    def _get_index_stats(self, index_path: Path) -> Dict[str, Any]:
        """获取索引统计信息"""
        try:
            from ..indexer import IndexBuilder
            
            # 创建临时索引构建器
            config = {
                'indexing': {
                    'index_type': 'flat',
                    'metric': 'cosine',
                    'quantization': 'none'
                }
            }
            
            builder = IndexBuilder(config)
            
            if builder.load_index(index_path):
                return {
                    'vector_count': builder.total_vectors,
                    'dimension': builder.dimension,
                    'index_type': type(builder.index).__name__,
                    'is_trained': getattr(builder.index, 'is_trained', True),
                    'loadable': True
                }
            else:
                return {
                    'vector_count': 0,
                    'dimension': 0,
                    'index_type': 'unknown',
                    'is_trained': False,
                    'loadable': False
                }
                
        except Exception as e:
            self.logger.warning(f"获取索引统计信息失败: {e}")
            return {
                'vector_count': 0,
                'dimension': 0,
                'index_type': 'unknown',
                'is_trained': False,
                'loadable': False,
                'error': str(e)
            }
    
    def _refresh_models(self):
        """刷新可用模型"""
        try:
            from ..utils.local_model_manager import get_local_model_manager
            
            model_manager = get_local_model_manager()
            
            # 获取所有可用模型
            all_models = list(model_manager.models.values())
            
            self._available_models.clear()
            
            for model in all_models:
                self._available_models[model.name] = {
                    'name': model.name,
                    'type': model.type,
                    'model_id': model.model_id,
                    'endpoint': model.endpoint,
                    'supports_embedding': model.supports_embedding,
                    'supports_chat': model.supports_chat,
                    'is_available': model.is_available,
                    'vector_dimension': model.vector_dimension
                }
            
            self.logger.info(f"刷新了 {len(self._available_models)} 个模型")
            
        except Exception as e:
            self.logger.error(f"刷新模型时出错: {e}")
    
    def get_available_indices(self) -> Dict[str, Dict[str, Any]]:
        """获取可用索引列表"""
        self._refresh_indices()
        return self._available_indices.copy()
    
    def get_available_models(self, supports_embedding: bool = None, supports_chat: bool = None) -> Dict[str, Dict[str, Any]]:
        """获取可用模型列表"""
        self._refresh_models()
        
        if supports_embedding is None and supports_chat is None:
            return self._available_models.copy()
        
        filtered_models = {}
        for name, model in self._available_models.items():
            if supports_embedding is not None and model['supports_embedding'] != supports_embedding:
                continue
            if supports_chat is not None and model['supports_chat'] != supports_chat:
                continue
            filtered_models[name] = model
        
        return filtered_models
    
    def get_latest_index(self) -> Optional[str]:
        """获取最新的索引"""
        if not self._available_indices:
            return None
        
        # 按修改时间排序，返回最新的
        latest = max(self._available_indices.values(), key=lambda x: x['modified_time'])
        return latest['path']
    
    def set_current_index(self, index_path: str):
        """设置当前索引"""
        if index_path in self._available_indices:
            self._current_index = index_path
            self.logger.info(f"设置当前索引: {index_path}")
            
            # 通知其他组件
            self._notify_components('index_changed', index_path)
        else:
            self.logger.warning(f"索引不存在: {index_path}")
    
    def set_current_model(self, model_name: str):
        """设置当前模型"""
        if model_name in self._available_models:
            self._current_model = model_name
            self.logger.info(f"设置当前模型: {model_name}")
            
            # 发送信号
            self.model_changed.emit(model_name)
            
            # 通知其他组件
            self._notify_components('model_changed', model_name)
        else:
            self.logger.warning(f"模型不存在: {model_name}")
    
    def get_current_index(self) -> Optional[str]:
        """获取当前索引"""
        return self._current_index
    
    def get_current_model(self) -> Optional[str]:
        """获取当前模型"""
        return self._current_model
    
    def notify_index_created(self, index_path: str):
        """通知索引创建"""
        self.logger.info(f"索引已创建: {index_path}")
        
        # 刷新索引列表
        self._refresh_indices()
        
        # 发送信号
        self.index_created.emit(index_path)
        
        # 通知组件
        self._notify_components('index_created', index_path)
    
    def notify_index_updated(self, index_path: str):
        """通知索引更新"""
        self.logger.info(f"索引已更新: {index_path}")
        
        # 刷新索引列表
        self._refresh_indices()
        
        # 发送信号
        self.index_updated.emit(index_path)
        
        # 通知组件
        self._notify_components('index_updated', index_path)
    
    def notify_vectors_added(self, index_path: str, vector_count: int):
        """通知向量添加"""
        self.logger.info(f"向量已添加到索引: {index_path}, 数量: {vector_count}")
        
        # 刷新索引列表
        self._refresh_indices()
        
        # 发送信号
        self.vectors_added.emit(index_path, vector_count)
        
        # 通知组件
        self._notify_components('vectors_added', index_path, vector_count)
    
    def _notify_components(self, event: str, *args):
        """通知组件事件"""
        for name, component in self._components.items():
            try:
                # 检查组件是否有对应的处理方法
                handler_name = f'on_state_{event}'
                if hasattr(component, handler_name):
                    handler = getattr(component, handler_name)
                    if callable(handler):
                        handler(*args)
            except Exception as e:
                self.logger.warning(f"通知组件 {name} 时出错: {e}")
    
    def get_index_stats(self, index_path: str) -> Optional[Dict[str, Any]]:
        """获取索引统计信息"""
        return self._index_stats.get(index_path)
    
    def auto_select_best_index(self):
        """自动选择最佳索引"""
        if not self._available_indices:
            return
        
        # 选择向量数量最多且可加载的索引
        best_index = None
        max_vectors = 0
        
        for index_path, info in self._available_indices.items():
            stats = info.get('stats', {})
            if stats.get('loadable', False):
                vector_count = stats.get('vector_count', 0)
                if vector_count > max_vectors:
                    max_vectors = vector_count
                    best_index = index_path
        
        if best_index:
            self.set_current_index(best_index)
            self.logger.info(f"自动选择最佳索引: {best_index} (向量数: {max_vectors})")

    def add_observer(self, observer: Callable[[StateContext], None]):
        self._observers.append(observer)
        
    def set_state(self, state: VectorizationState, message: str = "", progress: int = 0):
        self._state = StateContext(state, message, progress)
        self._notify_observers()
        
    def _notify_observers(self):
        for observer in self._observers:
            observer(self._state)


# 全局状态管理器实例
_state_manager = None

def get_state_manager() -> StateManager:
    """获取全局状态管理器实例"""
    global _state_manager
    if _state_manager is None:
        _state_manager = StateManager()
    return _state_manager
