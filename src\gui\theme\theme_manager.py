#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主题管理器模块，用于管理GUI主题
"""

import os
import json
import logging
from enum import Enum
from typing import Dict, Any, Optional, List
from pathlib import Path
import darkdetect

# 尝试导入 PyQt6，如果失败则使用 PyQt5
try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtGui import QPalette, QColor, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup, QActionGroup
    from PyQt6.QtCore import Qt
    QT_VERSION = 6
except ImportError:
    from PyQt5.QtWidgets import QApplication, QActionGroup
    from PyQt5.QtGui import QPalette, QColor
    from PyQt5.QtCore import Qt
    QT_VERSION = 5

# 设置日志记录器
logger = logging.getLogger(__name__)

try:
    from qt_material import apply_stylesheet

    # 检查并创建自定义主题目录
    custom_themes_dir = Path(__file__).parent / "qt_material_themes"
    if custom_themes_dir.exists():
        # 设置环境变量，指向自定义主题目录
        os.environ["QT_MATERIAL_THEME_FOLDER"] = str(custom_themes_dir)
        logger.info(f"使用自定义主题目录: {custom_themes_dir}")
except ImportError:
    logger.warning("无法导入 qt_material 库，将使用默认样式")
    apply_stylesheet = None

class Theme(Enum):
    """主题枚举"""
    SYSTEM = "system"
    LIGHT = "light"
    DARK = "dark"
    BLUE = "blue"
    GREEN = "green"
    RED = "red"
    PURPLE = "purple"
    ORANGE = "orange"
    TEAL = "teal"
    CYBERPUNK = "cyberpunk"
    MATRIX = "matrix"

    @property
    def display_name(self) -> str:
        """获取主题的显示名称"""
        names = {
            Theme.SYSTEM: "System Theme",
            Theme.LIGHT: "Light",
            Theme.DARK: "Dark",
            Theme.BLUE: "Blue",
            Theme.GREEN: "Green",
            Theme.RED: "Red",
            Theme.PURPLE: "Purple",
            Theme.ORANGE: "Orange",
            Theme.TEAL: "Teal",
            Theme.CYBERPUNK: "Cyberpunk",
            Theme.MATRIX: "Matrix",
        }
        return names.get(self, "Unknown")

class ThemeManager:
    """主题管理器类，用于管理GUI主题"""

    def __init__(self, default_theme: Theme = Theme.SYSTEM):
        """
        初始化主题管理器

        Args:
            default_theme: 默认主题
        """
        self.current_theme = default_theme
        self.observers = []
        self.theme_stylesheets = {}

        # 加载主题样式表
        self._load_theme_stylesheets()

    def _load_theme_stylesheets(self):
        """加载主题样式表"""
        try:
            # 获取样式表目录
            base_dir = Path(__file__).parent
            stylesheets_dir = base_dir / "stylesheets"

            # 确保目录存在
            if not stylesheets_dir.exists():
                stylesheets_dir.mkdir(parents=True, exist_ok=True)
                self._create_default_stylesheets(stylesheets_dir)

            # 加载所有主题的样式表
            for theme in Theme:
                file_path = stylesheets_dir / f"{theme.value}.qss"
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        self.theme_stylesheets[theme] = f.read()
                else:
                    logger.warning(f"主题样式表不存在: {file_path}")

        except Exception as e:
            logger.error(f"加载主题样式表时出错: {e}")

    def _create_default_stylesheets(self, stylesheets_dir: Path):
        """创建默认的主题样式表"""
        # 基础样式
        base_style = """
/* 基础样式 */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 10pt;
}

QMainWindow {
    background-color: $BACKGROUND_COLOR$;
}

QLabel {
    color: $TEXT_COLOR$;
}

QPushButton {
    background-color: $PRIMARY_COLOR$;
    color: $BUTTON_TEXT_COLOR$;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: $PRIMARY_COLOR_HOVER$;
}

QPushButton:pressed {
    background-color: $PRIMARY_COLOR_PRESSED$;
}

QPushButton:disabled {
    background-color: $DISABLED_COLOR$;
    color: $DISABLED_TEXT_COLOR$;
}

QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
    background-color: $INPUT_BACKGROUND$;
    color: $INPUT_TEXT_COLOR$;
    border: 1px solid $BORDER_COLOR$;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border: 1px solid $FOCUS_COLOR$;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url($DOWN_ARROW_ICON$);
    width: 12px;
    height: 12px;
}

QScrollBar:vertical {
    border: none;
    background-color: $SCROLLBAR_BACKGROUND$;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: $SCROLLBAR_HANDLE$;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: $SCROLLBAR_BACKGROUND$;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: $SCROLLBAR_HANDLE$;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

QMenuBar {
    background-color: $MENUBAR_BACKGROUND$;
    color: $MENUBAR_TEXT_COLOR$;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: $MENUBAR_ITEM_SELECTED$;
}

QMenu {
    background-color: $MENU_BACKGROUND$;
    color: $MENU_TEXT_COLOR$;
    border: 1px solid $MENU_BORDER_COLOR$;
}

QMenu::item {
    padding: 6px 20px;
}

QMenu::item:selected {
    background-color: $MENU_ITEM_SELECTED$;
}

QTabWidget::pane {
    border: 1px solid $TAB_BORDER_COLOR$;
    background-color: $TAB_BACKGROUND$;
}

QTabBar::tab {
    background-color: $TAB_UNSELECTED_BACKGROUND$;
    color: $TAB_UNSELECTED_TEXT_COLOR$;
    padding: 8px 12px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: $TAB_SELECTED_BACKGROUND$;
    color: $TAB_SELECTED_TEXT_COLOR$;
}

QStatusBar {
    background-color: $STATUSBAR_BACKGROUND$;
    color: $STATUSBAR_TEXT_COLOR$;
}

/* 自定义组件样式 */
#titleLabel {
    font-size: 18pt;
    font-weight: bold;
    color: $TITLE_COLOR$;
}

#sidebarWidget {
    background-color: $SIDEBAR_BACKGROUND$;
    min-width: 200px;
    max-width: 200px;
}

#contentWidget {
    background-color: $CONTENT_BACKGROUND$;
}

#dashboardWidget {
    background-color: $DASHBOARD_BACKGROUND$;
}

/* 动画效果相关样式 */
.animated-button {
    transition: background-color 0.3s;
}

.card {
    background-color: $CARD_BACKGROUND$;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
}

.card-title {
    font-size: 14pt;
    font-weight: bold;
    color: $CARD_TITLE_COLOR$;
    margin-bottom: 8px;
}

.card-content {
    color: $CARD_CONTENT_COLOR$;
}

/* 科技感元素 */
.tech-border {
    border: 1px solid $TECH_BORDER_COLOR$;
    border-radius: 4px;
}

.glow-effect {
    border: 1px solid $GLOW_COLOR$;
    box-shadow: 0 0 10px $GLOW_COLOR$;
}
"""

        # 亮色主题
        light_theme = base_style.replace("$BACKGROUND_COLOR$", "#f5f5f5")
        light_theme = light_theme.replace("$TEXT_COLOR$", "#333333")
        light_theme = light_theme.replace("$PRIMARY_COLOR$", "#2196F3")
        light_theme = light_theme.replace("$PRIMARY_COLOR_HOVER$", "#1E88E5")
        light_theme = light_theme.replace("$PRIMARY_COLOR_PRESSED$", "#1976D2")
        light_theme = light_theme.replace("$BUTTON_TEXT_COLOR$", "#FFFFFF")
        light_theme = light_theme.replace("$DISABLED_COLOR$", "#BDBDBD")
        light_theme = light_theme.replace("$DISABLED_TEXT_COLOR$", "#757575")
        light_theme = light_theme.replace("$INPUT_BACKGROUND$", "#FFFFFF")
        light_theme = light_theme.replace("$INPUT_TEXT_COLOR$", "#333333")
        light_theme = light_theme.replace("$BORDER_COLOR$", "#BDBDBD")
        light_theme = light_theme.replace("$FOCUS_COLOR$", "#2196F3")
        light_theme = light_theme.replace("$SCROLLBAR_BACKGROUND$", "#F5F5F5")
        light_theme = light_theme.replace("$SCROLLBAR_HANDLE$", "#BDBDBD")
        light_theme = light_theme.replace("$MENUBAR_BACKGROUND$", "#FFFFFF")
        light_theme = light_theme.replace("$MENUBAR_TEXT_COLOR$", "#333333")
        light_theme = light_theme.replace("$MENUBAR_ITEM_SELECTED$", "#E3F2FD")
        light_theme = light_theme.replace("$MENU_BACKGROUND$", "#FFFFFF")
        light_theme = light_theme.replace("$MENU_TEXT_COLOR$", "#333333")
        light_theme = light_theme.replace("$MENU_BORDER_COLOR$", "#BDBDBD")
        light_theme = light_theme.replace("$MENU_ITEM_SELECTED$", "#E3F2FD")
        light_theme = light_theme.replace("$TAB_BORDER_COLOR$", "#BDBDBD")
        light_theme = light_theme.replace("$TAB_BACKGROUND$", "#FFFFFF")
        light_theme = light_theme.replace("$TAB_UNSELECTED_BACKGROUND$", "#F5F5F5")
        light_theme = light_theme.replace("$TAB_UNSELECTED_TEXT_COLOR$", "#757575")
        light_theme = light_theme.replace("$TAB_SELECTED_BACKGROUND$", "#FFFFFF")
        light_theme = light_theme.replace("$TAB_SELECTED_TEXT_COLOR$", "#2196F3")
        light_theme = light_theme.replace("$STATUSBAR_BACKGROUND$", "#EEEEEE")
        light_theme = light_theme.replace("$STATUSBAR_TEXT_COLOR$", "#757575")
        light_theme = light_theme.replace("$TITLE_COLOR$", "#2196F3")
        light_theme = light_theme.replace("$SIDEBAR_BACKGROUND$", "#EEEEEE")
        light_theme = light_theme.replace("$CONTENT_BACKGROUND$", "#FFFFFF")
        light_theme = light_theme.replace("$DASHBOARD_BACKGROUND$", "#F5F5F5")
        light_theme = light_theme.replace("$CARD_BACKGROUND$", "#FFFFFF")
        light_theme = light_theme.replace("$CARD_TITLE_COLOR$", "#2196F3")
        light_theme = light_theme.replace("$CARD_CONTENT_COLOR$", "#333333")
        light_theme = light_theme.replace("$TECH_BORDER_COLOR$", "#2196F3")
        light_theme = light_theme.replace("$GLOW_COLOR$", "#2196F3")

        # 暗色主题
        dark_theme = base_style.replace("$BACKGROUND_COLOR$", "#121212")
        dark_theme = dark_theme.replace("$TEXT_COLOR$", "#FFFFFF")
        dark_theme = dark_theme.replace("$PRIMARY_COLOR$", "#2196F3")
        dark_theme = dark_theme.replace("$PRIMARY_COLOR_HOVER$", "#1E88E5")
        dark_theme = dark_theme.replace("$PRIMARY_COLOR_PRESSED$", "#1976D2")
        dark_theme = dark_theme.replace("$BUTTON_TEXT_COLOR$", "#FFFFFF")
        dark_theme = dark_theme.replace("$DISABLED_COLOR$", "#424242")
        dark_theme = dark_theme.replace("$DISABLED_TEXT_COLOR$", "#757575")
        dark_theme = dark_theme.replace("$INPUT_BACKGROUND$", "#1E1E1E")
        dark_theme = dark_theme.replace("$INPUT_TEXT_COLOR$", "#FFFFFF")
        dark_theme = dark_theme.replace("$BORDER_COLOR$", "#333333")
        dark_theme = dark_theme.replace("$FOCUS_COLOR$", "#2196F3")
        dark_theme = dark_theme.replace("$SCROLLBAR_BACKGROUND$", "#1E1E1E")
        dark_theme = dark_theme.replace("$SCROLLBAR_HANDLE$", "#424242")
        dark_theme = dark_theme.replace("$MENUBAR_BACKGROUND$", "#1E1E1E")
        dark_theme = dark_theme.replace("$MENUBAR_TEXT_COLOR$", "#FFFFFF")
        dark_theme = dark_theme.replace("$MENUBAR_ITEM_SELECTED$", "#333333")
        dark_theme = dark_theme.replace("$MENU_BACKGROUND$", "#1E1E1E")
        dark_theme = dark_theme.replace("$MENU_TEXT_COLOR$", "#FFFFFF")
        dark_theme = dark_theme.replace("$MENU_BORDER_COLOR$", "#333333")
        dark_theme = dark_theme.replace("$MENU_ITEM_SELECTED$", "#333333")
        dark_theme = dark_theme.replace("$TAB_BORDER_COLOR$", "#333333")
        dark_theme = dark_theme.replace("$TAB_BACKGROUND$", "#1E1E1E")
        dark_theme = dark_theme.replace("$TAB_UNSELECTED_BACKGROUND$", "#121212")
        dark_theme = dark_theme.replace("$TAB_UNSELECTED_TEXT_COLOR$", "#AAAAAA")
        dark_theme = dark_theme.replace("$TAB_SELECTED_BACKGROUND$", "#1E1E1E")
        dark_theme = dark_theme.replace("$TAB_SELECTED_TEXT_COLOR$", "#2196F3")
        dark_theme = dark_theme.replace("$STATUSBAR_BACKGROUND$", "#1E1E1E")
        dark_theme = dark_theme.replace("$STATUSBAR_TEXT_COLOR$", "#AAAAAA")
        dark_theme = dark_theme.replace("$TITLE_COLOR$", "#2196F3")
        dark_theme = dark_theme.replace("$SIDEBAR_BACKGROUND$", "#1E1E1E")
        dark_theme = dark_theme.replace("$CONTENT_BACKGROUND$", "#121212")
        dark_theme = dark_theme.replace("$DASHBOARD_BACKGROUND$", "#121212")
        dark_theme = dark_theme.replace("$CARD_BACKGROUND$", "#1E1E1E")
        dark_theme = dark_theme.replace("$CARD_TITLE_COLOR$", "#2196F3")
        dark_theme = dark_theme.replace("$CARD_CONTENT_COLOR$", "#FFFFFF")
        dark_theme = dark_theme.replace("$TECH_BORDER_COLOR$", "#2196F3")
        dark_theme = dark_theme.replace("$GLOW_COLOR$", "#2196F3")

        # 保存样式表
        with open(stylesheets_dir / "light.qss", 'w', encoding='utf-8') as f:
            f.write(light_theme)

        with open(stylesheets_dir / "dark.qss", 'w', encoding='utf-8') as f:
            f.write(dark_theme)

        # 系统主题使用空样式表，将根据系统设置动态选择亮色或暗色主题
        with open(stylesheets_dir / "system.qss", 'w', encoding='utf-8') as f:
            f.write("")

    def set_theme(self, theme: Theme):
        """
        设置当前主题

        Args:
            theme: 要设置的主题
        """
        if theme != self.current_theme:
            self.current_theme = theme
            # 通知所有观察者主题已更改
            self._notify_theme_changed()

    def apply_theme(self, app: QApplication):
        """
        应用当前主题到应用程序

        Args:
            app: QApplication实例
        """
        theme = self.current_theme

        # 如果是系统主题，根据系统设置选择亮色或暗色主题
        if theme == Theme.SYSTEM:
            system_theme = darkdetect.theme()
            if system_theme == "Dark":
                theme = Theme.DARK
            else:
                theme = Theme.LIGHT

        # 尝试使用qt_material库应用主题
        if apply_stylesheet is not None:
            try:
                # 定义主题映射
                theme_mapping = {
                    Theme.DARK: 'dark_blue.xml',
                    Theme.LIGHT: 'light_blue.xml',
                    Theme.BLUE: 'light_blue.xml',
                    Theme.GREEN: 'light_green.xml',
                    Theme.RED: 'light_red.xml',
                    Theme.PURPLE: 'light_purple.xml',
                    Theme.ORANGE: 'light_orange.xml',
                    Theme.TEAL: 'light_cyan.xml',
                    Theme.CYBERPUNK: 'dark_cyan.xml',
                    Theme.MATRIX: 'dark_green.xml'
                }

                # 获取对应的主题文件
                theme_file = theme_mapping.get(theme)

                if theme_file:
                    # 检查主题文件是否存在
                    try:
                        # 尝试应用主题
                        apply_stylesheet(app, theme=theme_file)
                        return
                    except FileNotFoundError:
                        # 如果主题文件不存在，尝试使用备用主题
                        logger.warning(f"主题文件 {theme_file} 不存在，尝试使用备用主题")

                        # 备用主题映射
                        fallback_mapping = {
                            'dark_green.xml': 'dark_blue.xml',
                            'light_green.xml': 'light_blue.xml',
                            'light_red.xml': 'light_blue.xml',
                            'light_purple.xml': 'light_blue.xml',
                            'light_orange.xml': 'light_blue.xml',
                            'light_cyan.xml': 'light_blue.xml',
                            'dark_cyan.xml': 'dark_blue.xml'
                        }

                        # 尝试使用备用主题
                        fallback_theme = fallback_mapping.get(theme_file)
                        if fallback_theme:
                            try:
                                apply_stylesheet(app, theme=fallback_theme)
                                return
                            except Exception as e:
                                logger.warning(f"使用备用主题 {fallback_theme} 失败: {e}")
            except Exception as e:
                logger.warning(f"使用qt_material应用主题失败: {e}")

        # 如果qt_material不可用或应用失败，使用自定义样式表
        stylesheet = self.theme_stylesheets.get(theme, "")
        app.setStyleSheet(stylesheet)

    def add_observer(self, observer):
        """
        添加主题变更观察者

        Args:
            observer: 观察者对象，必须实现on_theme_changed方法
        """
        if observer not in self.observers:
            self.observers.append(observer)

    def remove_observer(self, observer):
        """
        移除主题变更观察者

        Args:
            observer: 要移除的观察者对象
        """
        if observer in self.observers:
            self.observers.remove(observer)

    def _notify_theme_changed(self):
        """通知所有观察者主题已更改"""
        for observer in self.observers:
            if hasattr(observer, 'on_theme_changed'):
                observer.on_theme_changed()

# 创建全局主题管理器实例
theme_manager = ThemeManager()

def get_theme_manager() -> ThemeManager:
    """获取全局主题管理器实例"""
    return theme_manager
