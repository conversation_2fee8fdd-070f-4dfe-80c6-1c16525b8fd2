#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI对话窗口

提供独立的AI对话界面，支持基于搜索结果的智能问答
"""

from typing import List, Optional
# 尝试导入 PyQt6，如果失败则使用 PyQt5
try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QComboBox, QTextEdit, QFormLayout, QGroupBox, QTabWidget,
        QFileDialog, QProgressBar, QSpinBox, QLineEdit, QCheckBox,
        QTableWidget, QTableWidgetItem, QHeaderView, QRadioButton, 
        QButtonGroup, QMessageBox, QDialog, QDialogButtonBox,
        QSpacerItem, QSizePolicy, QFrame, QTextBrowser, QSlider,
        QColorDialog
    )
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSize
    from PyQt6.QtGui import QFont, QIcon, QPixmap, QTextCursor
    QT_VERSION = 6
except ImportError:
    from PyQt5.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QComboBox, QTextEdit, QFormLayout, QGroupBox, QTabWidget,
        QFileDialog, QProgressBar, QSpinBox, QLineEdit, QCheckBox,
        QTableWidget, QTableWidgetItem, QHeaderView, QRadioButton, 
        QButtonGroup, QMessageBox, QDialog, QDialogButtonBox,
        QSpacerItem, QSizePolicy, QFrame, QTextBrowser, QSlider,
        QColorDialog
    )
    from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSize
    from PyQt5.QtGui import QFont, QIcon, QPixmap, QTextCursor
    QT_VERSION = 5
from ..i18n import Translator


class ChatWorker(QThread):
    """AI对话工作线程"""

    finished = pyqtSignal(str)
    error = pyqtSignal(str)

    def __init__(self, model, query: str, context_texts: List[str]):
        super().__init__()
        self.model = model
        self.query = query
        self.context_texts = context_texts

    def run(self):
        """执行AI对话"""
        try:
            # 构建提示词
            context = "\n\n".join(self.context_texts) if self.context_texts else "没有找到相关内容"

            prompt = f"""基于以下上下文信息回答用户的问题：

上下文信息：
{context}

用户问题：{self.query}

请基于上下文信息给出准确、有用的回答。如果上下文信息不足以回答问题，请说明这一点。"""

            if self.model.type == "ollama":
                response = self._call_ollama_chat(prompt)
            elif self.model.type == "openai_compatible":
                response = self._call_openai_compatible_chat(prompt)
            else:
                response = f"暂不支持 {self.model.type} 类型的模型进行对话"

            self.finished.emit(response)

        except Exception as e:
            self.error.emit(str(e))

    def _call_ollama_chat(self, prompt: str) -> str:
        """调用Ollama模型进行对话"""
        try:
            import requests

            # 首先检查Ollama服务是否可用
            try:
                health_url = f"{self.model.endpoint}/api/tags"
                health_response = requests.get(health_url, timeout=5)
                if health_response.status_code != 200:
                    return "Ollama服务不可用，请确保Ollama已启动"
            except requests.exceptions.RequestException:
                return "无法连接到Ollama服务，请检查服务是否启动（端口11434）"

            url = f"{self.model.endpoint}/api/generate"
            data = {
                "model": self.model.model_id,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "num_predict": 1000
                }
            }

            # 增加超时时间到5分钟
            response = requests.post(url, json=data, timeout=300)
            response.raise_for_status()

            result = response.json()
            return result.get("response", "模型没有返回回答")

        except requests.exceptions.Timeout:
            return "模型响应超时，请尝试使用更小的模型或减少输入长度"
        except requests.exceptions.ConnectionError:
            return "连接错误，请确保Ollama服务正在运行"
        except Exception as e:
            return f"调用Ollama模型时出错: {str(e)}"

    def _call_openai_compatible_chat(self, prompt: str) -> str:
        """调用OpenAI兼容模型进行对话"""
        try:
            import requests

            url = f"{self.model.endpoint}/v1/chat/completions"
            headers = {
                "Content-Type": "application/json"
            }

            # 添加API密钥（如果有）
            api_key = self.model.parameters.get('api_key')
            if api_key:
                headers["Authorization"] = f"Bearer {api_key}"

            data = {
                "model": self.model.model_id,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": 1500,
                "temperature": 0.7
            }

            response = requests.post(url, json=data, headers=headers, timeout=300)
            response.raise_for_status()

            result = response.json()
            if "choices" in result and result["choices"]:
                return result["choices"][0]["message"]["content"]
            else:
                return "模型没有返回回答"

        except Exception as e:
            return f"调用OpenAI兼容模型时出错: {str(e)}"


class AIChatDialog(QDialog):
    """AI对话窗口"""

    def __init__(self, translator: Translator, parent=None):
        super().__init__(parent)
        self.translator = translator
        self.chat_history = []
        self.current_model = None
        self.search_widget = None  # 搜索组件引用

        # 设置日志
        import logging
        self.logger = logging.getLogger(__name__)

        self._init_ui()
        self._connect_signals()

    def _init_ui(self):
        """初始化UI"""
        self.setWindowTitle(self.translator.get_text("ai_chat_dialog", "AI智能对话"))
        self.setMinimumSize(800, 600)
        self.resize(1000, 700)

        # 主布局
        layout = QVBoxLayout(self)

        # 配置区域
        config_group = QGroupBox(self.translator.get_text("configuration", "配置"))
        config_layout = QVBoxLayout(config_group)

        # 模型选择行
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel(self.translator.get_text("select_model", "选择模型:")))
        self.model_combo = QComboBox()
        self.model_combo.addItem(self.translator.get_text("no_model_selected", "请选择模型"))
        model_layout.addWidget(self.model_combo)

        self.refresh_models_btn = QPushButton(self.translator.get_text("refresh_models", "刷新模型"))
        model_layout.addWidget(self.refresh_models_btn)
        model_layout.addStretch()
        config_layout.addLayout(model_layout)

        # 索引库选择行
        index_layout = QHBoxLayout()
        index_layout.addWidget(QLabel(self.translator.get_text("select_index", "选择索引库:")))
        self.index_combo = QComboBox()
        self.index_combo.addItem(self.translator.get_text("no_index_selected", "请选择索引库"))
        index_layout.addWidget(self.index_combo)

        self.refresh_indices_btn = QPushButton(self.translator.get_text("refresh_indices", "刷新索引"))
        index_layout.addWidget(self.refresh_indices_btn)
        index_layout.addStretch()
        config_layout.addLayout(index_layout)

        layout.addWidget(config_group)

        # 对话区域
        chat_group = QGroupBox(self.translator.get_text("chat_area", "对话区域"))
        chat_layout = QVBoxLayout(chat_group)

        # 对话历史
        self.chat_display = QTextEdit()
        self.chat_display.setReadOnly(True)
        self.chat_display.setFont(QFont("Microsoft YaHei", 10))
        chat_layout.addWidget(self.chat_display)

        # 输入区域
        input_group = QGroupBox(self.translator.get_text("input_area", "输入区域"))
        input_layout = QVBoxLayout(input_group)

        self.input_text = QTextEdit()
        self.input_text.setMaximumHeight(100)
        self.input_text.setPlaceholderText(self.translator.get_text("enter_question", "请输入您的问题..."))
        input_layout.addWidget(self.input_text)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.search_first_btn = QPushButton(self.translator.get_text("search_then_ask", "先搜索再问答"))
        self.search_first_btn.setObjectName("primaryButton")
        button_layout.addWidget(self.search_first_btn)

        self.direct_ask_btn = QPushButton(self.translator.get_text("direct_ask", "直接问答"))
        button_layout.addWidget(self.direct_ask_btn)

        self.clear_btn = QPushButton(self.translator.get_text("clear_chat", "清空对话"))
        button_layout.addWidget(self.clear_btn)

        button_layout.addStretch()

        self.send_btn = QPushButton(self.translator.get_text("send", "发送"))
        self.send_btn.setObjectName("primaryButton")
        button_layout.addWidget(self.send_btn)

        input_layout.addLayout(button_layout)
        chat_layout.addWidget(input_group)

        layout.addWidget(chat_group)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel(self.translator.get_text("ready", "就绪"))
        layout.addWidget(self.status_label)

        # 加载本地模型和索引
        self._load_local_models()
        self._load_indices()

        # 自动选择最新索引
        self._auto_select_latest_index()

    def _connect_signals(self):
        """连接信号"""
        self.refresh_models_btn.clicked.connect(self._load_local_models)
        self.model_combo.currentTextChanged.connect(self._on_model_changed)
        self.refresh_indices_btn.clicked.connect(self._load_indices)
        self.index_combo.currentTextChanged.connect(self._on_index_changed)
        self.search_first_btn.clicked.connect(self._on_search_then_ask)
        self.direct_ask_btn.clicked.connect(self._on_direct_ask)
        self.clear_btn.clicked.connect(self._on_clear_chat)
        self.send_btn.clicked.connect(self._on_send)
        self.input_text.textChanged.connect(self._on_input_changed)

    def _load_local_models(self):
        """加载本地模型"""
        try:
            from ...utils.local_model_manager import get_local_model_manager

            # 清空现有选项
            self.model_combo.clear()
            self.model_combo.addItem(self.translator.get_text("no_model_selected", "请选择模型"))

            # 获取本地模型管理器
            model_manager = get_local_model_manager()

            # 获取支持对话的本地模型
            chat_models = model_manager.get_available_models(supports_chat=True)

            if chat_models:
                # 添加分隔符
                self.model_combo.insertSeparator(self.model_combo.count())
                self.model_combo.addItem("--- 本地大模型 ---")

                # 添加本地模型
                for model in chat_models:
                    display_name = f"{model.type}:{model.model_id}"
                    self.model_combo.addItem(display_name)
                    # 存储模型对象作为用户数据
                    self.model_combo.setItemData(self.model_combo.count() - 1, model)

                self.status_label.setText(f"加载了 {len(chat_models)} 个本地对话模型")
            else:
                self.status_label.setText("没有找到可用的本地对话模型")

        except Exception as e:
            self.status_label.setText(f"加载模型时出错: {str(e)}")

    def _load_indices(self):
        """加载可用的索引库"""
        try:
            from pathlib import Path

            # 清空现有选项
            self.index_combo.clear()
            self.index_combo.addItem(self.translator.get_text("no_index_selected", "请选择索引库"))

            # 获取索引目录
            indices_dir = Path("data/indices")
            if not indices_dir.exists():
                self.status_label.setText("索引目录不存在")
                return

            # 获取所有索引文件
            index_files = list(indices_dir.glob("*.idx"))

            if index_files:
                # 添加分隔符
                self.index_combo.insertSeparator(self.index_combo.count())
                self.index_combo.addItem("--- 可用索引库 ---")

                # 按修改时间排序，最新的在前
                index_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

                for index_file in index_files:
                    # 检查是否有对应的元数据文件
                    meta_file = index_file.with_suffix('.meta')
                    if meta_file.exists():
                        # 显示索引名称和修改时间
                        import time
                        mod_time = time.strftime("%Y-%m-%d %H:%M", time.localtime(index_file.stat().st_mtime))
                        display_name = f"{index_file.stem} ({mod_time})"
                        self.index_combo.addItem(display_name)
                        # 存储索引文件路径作为用户数据
                        self.index_combo.setItemData(self.index_combo.count() - 1, str(index_file))

                self.status_label.setText(f"找到 {len(index_files)} 个索引库")
            else:
                self.status_label.setText("没有找到可用的索引库")

        except Exception as e:
            self.status_label.setText(f"加载索引库时出错: {str(e)}")

    def _auto_select_latest_index(self):
        """自动选择最新的索引库"""
        try:
            # 如果有可用的索引，自动选择最新的
            if self.index_combo.count() > 2:  # 除了"请选择"和分隔符
                # 选择第一个实际的索引（最新的）
                for i in range(self.index_combo.count()):
                    if self.index_combo.itemData(i):  # 有数据的项
                        self.index_combo.setCurrentIndex(i)
                        self._on_index_changed()
                        break
        except Exception as e:
            self.logger.warning(f"自动选择索引时出错: {e}")

    def on_state_vectors_added(self, index_path: str, vector_count: int):
        """状态管理器通知：向量已添加"""
        # 刷新索引列表
        self._load_indices()
        # 自动选择更新的索引
        self._auto_select_latest_index()
        self.status_label.setText(f"索引已更新，新增 {vector_count} 个向量")

    def on_state_index_created(self, index_path: str):
        """状态管理器通知：索引已创建"""
        # 刷新索引列表
        self._load_indices()
        # 自动选择新索引
        self._auto_select_latest_index()
        from pathlib import Path
        self.status_label.setText(f"新索引已创建: {Path(index_path).name}")

    def on_state_index_updated(self, index_path: str):
        """状态管理器通知：索引已更新"""
        # 刷新索引列表
        self._load_indices()
        from pathlib import Path
        self.status_label.setText(f"索引已更新: {Path(index_path).name}")

    def _on_model_changed(self):
        """模型选择变更"""
        current_text = self.model_combo.currentText()

        if current_text and current_text not in ["请选择模型", "--- 本地大模型 ---"]:
            self.current_model = self.model_combo.itemData(self.model_combo.currentIndex())
            self.send_btn.setEnabled(True)
            self.search_first_btn.setEnabled(True)
            self.direct_ask_btn.setEnabled(True)
            self.status_label.setText(f"已选择模型: {current_text}")
        else:
            self.current_model = None
            self.send_btn.setEnabled(False)
            self.search_first_btn.setEnabled(False)
            self.direct_ask_btn.setEnabled(False)
            self.status_label.setText("请选择一个模型")

    def _on_index_changed(self):
        """索引选择变更"""
        current_text = self.index_combo.currentText()

        if current_text and current_text not in ["请选择索引库", "--- 可用索引库 ---"]:
            index_path = self.index_combo.itemData(self.index_combo.currentIndex())
            if index_path:
                self.current_index_path = index_path
                self.status_label.setText(f"已选择索引库: {current_text}")
            else:
                self.current_index_path = None
        else:
            self.current_index_path = None

    def _on_input_changed(self):
        """输入文本变更"""
        has_text = bool(self.input_text.toPlainText().strip())
        has_model = self.current_model is not None

        self.send_btn.setEnabled(has_text and has_model)
        self.search_first_btn.setEnabled(has_text and has_model)
        self.direct_ask_btn.setEnabled(has_text and has_model)

    def _on_search_then_ask(self):
        """先搜索再问答"""
        query = self.input_text.toPlainText().strip()
        if not query:
            return

        # 执行搜索获取上下文
        try:
            if self.search_widget:
                context_texts = self._perform_search(query)
                if context_texts:
                    self.status_label.setText(f"找到 {len(context_texts)} 条相关内容")
                else:
                    self.status_label.setText("未找到相关内容，将进行直接问答")
                    context_texts = []
            else:
                self.status_label.setText("搜索功能不可用，进行直接问答")
                context_texts = []

            # 执行AI问答
            self._start_chat(query, context_texts)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"搜索时出错: {str(e)}")

    def _on_direct_ask(self):
        """直接问答"""
        query = self.input_text.toPlainText().strip()
        if not query:
            return

        # 直接问答，不提供上下文
        self._start_chat(query, [])

    def _on_send(self):
        """发送按钮（默认为先搜索再问答）"""
        self._on_search_then_ask()

    def _on_clear_chat(self):
        """清空对话"""
        self.chat_history.clear()
        self.chat_display.clear()
        self.status_label.setText("对话已清空")

    def _perform_search(self, query: str) -> List[str]:
        """执行搜索获取上下文"""
        try:
            # 如果没有选择索引库，返回空结果
            if not hasattr(self, 'current_index_path') or not self.current_index_path:
                self.status_label.setText("未选择索引库，无法搜索")
                return []

            # 直接使用选定的索引库进行搜索
            from pathlib import Path
            from ...indexer import IndexBuilder, VectorSearcher
            from ...vectorizer import TextEmbedding
            from ...storage import MetadataManager
            import pickle

            # 🔥 修复：读取索引元数据以确定正确的模型配置
            index_path = Path(self.current_index_path)
            meta_file = index_path.with_suffix('.meta')

            # 默认配置
            index_dimension = 768

            # 尝试读取索引元数据
            if meta_file.exists():
                try:
                    with open(meta_file, 'rb') as f:
                        metadata = pickle.load(f)
                    index_dimension = metadata.get('dimension', 768)
                except Exception as e:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"无法读取索引元数据: {e}")

            # 🔥 修复：根据索引维度和用户选择创建正确的配置
            config = self._create_embedder_config_for_chat(index_dimension)

            # 创建文本嵌入器
            embedder = TextEmbedding(config)

            # 创建索引构建器并加载索引
            builder = IndexBuilder(config)
            index_path = Path(self.current_index_path)

            if not builder.load_index(index_path):
                self.status_label.setText(f"无法加载索引库: {index_path.name}")
                return []

            # 生成查询向量
            query_vector = embedder.encode_text(query)

            # 创建搜索器
            searcher = VectorSearcher(config)
            searcher.index = builder.index

            # 执行搜索
            results = searcher.search(query_vector, k=5, min_similarity=0.1)

            # 获取元数据管理器
            metadata_manager = MetadataManager(config)

            # 收集搜索结果文本
            context_texts = []
            for result in results:
                try:
                    metadata = metadata_manager.get_metadata(result.id)
                    if metadata and 'text' in metadata:
                        text = metadata['text']
                        if text and len(text.strip()) > 10:
                            context_texts.append(text)
                except Exception as e:
                    continue

            # 清理资源
            embedder.cleanup()

            return context_texts

        except Exception as e:
            self.status_label.setText(f"搜索时出错: {str(e)}")
            return []

    def _create_embedder_config_for_chat(self, index_dimension: int) -> dict:
        """
        为AI聊天创建嵌入器配置，优先使用Ollama模型

        Args:
            index_dimension: 索引维度

        Returns:
            dict: 嵌入器配置
        """
        import logging
        logger = logging.getLogger(__name__)

        # 优先使用Ollama模型进行搜索
        if index_dimension == 768:
            # 使用Ollama模型（768维）
            config = {
                'indexing': {
                    'index_type': 'flat',
                    'metric': 'cosine',
                    'quantization': 'none'
                },
                'vectorization': {
                    'model_name': 'local:ollama_nomic-embed-text_latest',
                    'vector_dimension': 768,
                    'batch_size': 8,
                    'device': 'cpu',
                    'normalize_vectors': True
                },
                'local_models': {
                    'ollama': {
                        'enabled': True,
                        'api_url': 'http://localhost:11434/api',
                        'default_model': 'nomic-embed-text:latest',
                        'models': []
                    }
                },
                'storage': {
                    'base_dir': 'data/vectors'
                }
            }
            logger.info("AI聊天使用Ollama嵌入模型（768维）")
        else:
            # 使用sentence-transformers模型（384维）
            config = {
                'indexing': {
                    'index_type': 'flat',
                    'metric': 'cosine',
                    'quantization': 'none'
                },
                'vectorization': {
                    'model_name': 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
                    'vector_dimension': 384,
                    'batch_size': 32,
                    'device': 'cpu',
                    'normalize_vectors': True
                },
                'storage': {
                    'base_dir': 'data/vectors'
                }
            }
            logger.info("AI聊天使用sentence-transformers嵌入模型（384维）")

        return config

    def _start_chat(self, query: str, context_texts: List[str]):
        """开始AI对话"""
        if not self.current_model:
            QMessageBox.warning(self, "提示", "请先选择一个模型")
            return

        # 添加用户消息到对话历史
        self._add_message("用户", query)

        # 显示进度
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.status_label.setText("AI正在思考中...")

        # 禁用输入
        self.input_text.setEnabled(False)
        self.send_btn.setEnabled(False)
        self.search_first_btn.setEnabled(False)
        self.direct_ask_btn.setEnabled(False)

        # 创建工作线程
        self.chat_worker = ChatWorker(self.current_model, query, context_texts)
        self.chat_worker.finished.connect(self._on_chat_finished)
        self.chat_worker.error.connect(self._on_chat_error)
        self.chat_worker.start()

    def _on_chat_finished(self, response: str):
        """AI对话完成"""
        # 添加AI回答到对话历史
        self._add_message("AI", response)

        # 恢复界面
        self._restore_ui()

        # 清空输入框
        self.input_text.clear()

    def _on_chat_error(self, error_msg: str):
        """AI对话出错"""
        self._add_message("系统", f"错误: {error_msg}")
        self._restore_ui()

    def _restore_ui(self):
        """恢复UI状态"""
        self.progress_bar.setVisible(False)
        self.input_text.setEnabled(True)
        self.status_label.setText("就绪")
        self._on_input_changed()  # 重新检查按钮状态

    def _add_message(self, sender: str, message: str):
        """添加消息到对话历史"""
        import time
        timestamp = time.strftime("%H:%M:%S")

        # 添加到历史记录
        self.chat_history.append({
            'sender': sender,
            'message': message,
            'timestamp': timestamp
        })

        # 显示在界面上
        cursor = self.chat_display.textCursor()
        if QT_VERSION == 6:
            from PyQt6.QtGui import QTextCursor
            cursor.movePosition(QTextCursor.MoveOperation.End)
        else:
            from PyQt5.QtGui import QTextCursor
            cursor.movePosition(QTextCursor.End)

        # 设置样式
        if sender == "用户":
            cursor.insertHtml(f'<p style="color: #2196F3; font-weight: bold;">[{timestamp}] {sender}:</p>')
        elif sender == "AI":
            cursor.insertHtml(f'<p style="color: #4CAF50; font-weight: bold;">[{timestamp}] {sender}:</p>')
        else:
            cursor.insertHtml(f'<p style="color: #FF9800; font-weight: bold;">[{timestamp}] {sender}:</p>')

        cursor.insertHtml(f'<p style="margin-left: 20px; margin-bottom: 10px;">{message.replace(chr(10), "<br>")}</p>')

        # 滚动到底部
        self.chat_display.ensureCursorVisible()

    def set_search_widget(self, search_widget):
        """设置搜索组件引用"""
        self.search_widget = search_widget

    def update_language(self):
        """更新语言"""
        self.setWindowTitle(self.translator.get_text("ai_chat_dialog", "AI智能对话"))
        # 更新其他UI文本...
