#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
仪表盘小部件
"""

# 尝试导入 PyQt6，如果失败则使用 PyQt5
try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QFrame, QScrollArea, QSizePolicy, QGridLayout
    )
    from PyQt6.QtCore import Qt, QSize, QPropertyAnimation, QEasingCurve
    from PyQt6.QtGui import QFont, QColor, QPalette
    QT_VERSION = 6
except ImportError:
    from PyQt5.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QFrame, QScrollArea, QSizePolicy, QGridLayout
    )
    from PyQt5.QtCore import Qt, QSize, QPropertyAnimation, QEasingCurve
    from PyQt5.QtGui import QFont, QColor, QPalette
    QT_VERSION = 5

from ..i18n import Translator

class StatCard(QFrame):
    """统计卡片小部件"""

    def __init__(self, title: str, value: str, icon: str = None):
        """
        初始化统计卡片

        Args:
            title: 卡片标题
            value: 卡片值
            icon: 图标名称（可选）
        """
        super().__init__()

        # 设置样式
        self.setObjectName("statCard")
        self.setProperty("class", "card")
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.setFrameShadow(QFrame.Shadow.Raised)

        # 创建布局
        layout = QVBoxLayout(self)

        # 创建标题标签
        title_label = QLabel(title)
        title_label.setObjectName("cardTitle")
        title_label.setProperty("class", "card-title")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # 创建值标签
        value_label = QLabel(value)
        value_label.setObjectName("cardValue")
        value_label.setProperty("class", "card-content")
        value_label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        layout.addWidget(value_label)

        # 存储标签引用
        self.title_label = title_label
        self.value_label = value_label

    def update_data(self, title: str, value: str):
        """
        更新卡片数据

        Args:
            title: 新标题
            value: 新值
        """
        self.title_label.setText(title)

        # 创建动画效果
        self.animation = QPropertyAnimation(self.value_label, b"text")
        self.animation.setDuration(500)
        self.animation.setStartValue(self.value_label.text())
        self.animation.setEndValue(value)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.animation.start()

        self.value_label.setText(value)

class ActionCard(QFrame):
    """操作卡片小部件"""

    def __init__(self, title: str, description: str, button_text: str, icon: str = None):
        """
        初始化操作卡片

        Args:
            title: 卡片标题
            description: 卡片描述
            button_text: 按钮文本
            icon: 图标名称（可选）
        """
        super().__init__()

        # 设置样式
        self.setObjectName("actionCard")
        self.setProperty("class", "card")
        self.setFrameShape(QFrame.Shape.StyledPanel)
        self.setFrameShadow(QFrame.Shadow.Raised)

        # 创建布局
        layout = QVBoxLayout(self)

        # 创建标题标签
        title_label = QLabel(title)
        title_label.setObjectName("cardTitle")
        title_label.setProperty("class", "card-title")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # 创建描述标签
        desc_label = QLabel(description)
        desc_label.setObjectName("cardDescription")
        desc_label.setProperty("class", "card-content")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        # 创建按钮
        button = QPushButton(button_text)
        button.setObjectName("cardButton")
        button.setProperty("class", "animated-button")
        button.setCursor(Qt.CursorShape.PointingHandCursor)
        layout.addWidget(button)

        # 存储组件引用
        self.title_label = title_label
        self.desc_label = desc_label
        self.button = button

    def update_data(self, title: str, description: str, button_text: str):
        """
        更新卡片数据

        Args:
            title: 新标题
            description: 新描述
            button_text: 新按钮文本
        """
        self.title_label.setText(title)
        self.desc_label.setText(description)
        self.button.setText(button_text)

class DashboardWidget(QWidget):
    """仪表盘小部件"""

    def __init__(self, translator: Translator):
        """
        初始化仪表盘小部件

        Args:
            translator: 翻译器实例
        """
        super().__init__()

        self.translator = translator
        self.translator.add_observer(self)

        # 设置对象名，用于样式表
        self.setObjectName("dashboardWidget")

        # 初始化UI
        self._init_ui()

    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建欢迎标签
        welcome_label = QLabel(self.translator.get_text("welcome_message", "欢迎使用MD向量处理器"))
        welcome_label.setObjectName("welcomeLabel")
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        welcome_label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        main_layout.addWidget(welcome_label)

        # 创建统计卡片区域
        stats_header_layout = QHBoxLayout()
        stats_label = QLabel(self.translator.get_text("statistics", "统计信息"))
        stats_label.setObjectName("sectionLabel")
        stats_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        stats_header_layout.addWidget(stats_label)

        # 添加刷新按钮
        self.refresh_stats_btn = QPushButton(self.translator.get_text("refresh", "刷新"))
        self.refresh_stats_btn.setMaximumWidth(80)
        self.refresh_stats_btn.clicked.connect(self.refresh_stats)
        stats_header_layout.addWidget(self.refresh_stats_btn)
        stats_header_layout.addStretch()

        main_layout.addLayout(stats_header_layout)

        # 统计卡片布局
        stats_layout = QHBoxLayout()

        # 创建统计卡片
        self.vectors_card = StatCard(
            self.translator.get_text("total_vectors", "向量总数"),
            "0"
        )
        self.indices_card = StatCard(
            self.translator.get_text("total_indices", "索引总数"),
            "0"
        )
        self.models_card = StatCard(
            self.translator.get_text("total_models", "模型总数"),
            "0"
        )
        self.queries_card = StatCard(
            self.translator.get_text("total_queries", "查询总数"),
            "0"
        )

        # 添加卡片到布局
        stats_layout.addWidget(self.vectors_card)
        stats_layout.addWidget(self.indices_card)
        stats_layout.addWidget(self.models_card)
        stats_layout.addWidget(self.queries_card)

        main_layout.addLayout(stats_layout)

        # 创建操作卡片区域
        actions_label = QLabel(self.translator.get_text("quick_actions", "快速操作"))
        actions_label.setObjectName("sectionLabel")
        actions_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        main_layout.addWidget(actions_label)

        # 操作卡片布局
        actions_layout = QGridLayout()

        # 创建操作卡片
        self.index_card = ActionCard(
            self.translator.get_text("create_index", "创建索引"),
            self.translator.get_text("create_index_desc", "创建新的向量索引，支持多种索引类型和量化方法"),
            self.translator.get_text("create", "创建")
        )
        self.vectorize_card = ActionCard(
            self.translator.get_text("vectorize_data", "向量化数据"),
            self.translator.get_text("vectorize_data_desc", "将文本、图像或其他数据转换为向量表示"),
            self.translator.get_text("vectorize", "向量化")
        )
        self.search_card = ActionCard(
            self.translator.get_text("search_vectors", "搜索向量"),
            self.translator.get_text("search_vectors_desc", "在向量索引中搜索相似内容"),
            self.translator.get_text("search", "搜索")
        )
        self.visualize_card = ActionCard(
            self.translator.get_text("visualize_vectors", "可视化向量"),
            self.translator.get_text("visualize_vectors_desc", "可视化向量空间，探索数据分布"),
            self.translator.get_text("visualize", "可视化")
        )

        # 添加卡片到布局
        actions_layout.addWidget(self.index_card, 0, 0)
        actions_layout.addWidget(self.vectorize_card, 0, 1)
        actions_layout.addWidget(self.search_card, 1, 0)
        actions_layout.addWidget(self.visualize_card, 1, 1)

        main_layout.addLayout(actions_layout)

        # 添加弹性空间
        main_layout.addStretch()

        # 连接信号
        self._connect_signals()

        # 初始化数据更新（仅在启动时更新一次）
        self._force_update_stats()

        # 注册到状态管理器（暂时禁用，避免DLL错误）
        # try:
        #     from ..state_manager import get_state_manager
        #     state_manager = get_state_manager()
        #     state_manager.register_component("dashboard", self)
        # except Exception as e:
        #     import logging
        #     logger = logging.getLogger(__name__)
        #     logger.warning(f"注册到状态管理器失败: {e}")

    def _connect_signals(self):
        """连接信号和槽"""
        # 连接操作卡片按钮
        self.index_card.button.clicked.connect(self._on_create_index)
        self.vectorize_card.button.clicked.connect(self._on_vectorize_data)
        self.search_card.button.clicked.connect(self._on_search_vectors)
        self.visualize_card.button.clicked.connect(self._on_visualize_vectors)

    def _update_stats(self):
        """更新统计数据（简化版，避免DLL错误）"""
        try:
            # 直接使用备用方法，避免状态管理器的DLL问题
            self._force_update_stats()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"更新统计数据时出错: {e}")

            # 如果还是失败，显示默认值
            try:
                self.vectors_card.update_data(
                    self.translator.get_text("total_vectors", "向量总数"),
                    "0"
                )
                self.indices_card.update_data(
                    self.translator.get_text("total_indices", "索引总数"),
                    "0"
                )
                self.models_card.update_data(
                    self.translator.get_text("total_models", "模型总数"),
                    "3"
                )
                self.queries_card.update_data(
                    self.translator.get_text("total_queries", "查询总数"),
                    "0"
                )
            except Exception as e2:
                logger.error(f"显示默认统计数据也失败: {e2}")

    def _get_query_count(self):
        """获取查询次数（从日志或缓存中）"""
        try:
            # 尝试从缓存文件读取查询次数
            from pathlib import Path
            import json

            cache_file = Path("data/cache/query_stats.json")
            if cache_file.exists():
                with open(cache_file, 'r', encoding='utf-8') as f:
                    stats = json.load(f)
                    return stats.get('total_queries', 0)
            else:
                return 0
        except Exception:
            return 0

    def _get_indices_count_fallback(self):
        """备用方法：直接统计索引文件数量"""
        try:
            from pathlib import Path
            indices_dir = Path("data/indices")
            if indices_dir.exists():
                index_files = list(indices_dir.glob("*.idx"))
                return len(index_files)
            return 0
        except Exception:
            return 0

    def _get_vectors_count_fallback(self):
        """备用方法：直接统计向量数量"""
        try:
            from pathlib import Path
            import json
            import pickle

            total_vectors = 0
            indices_dir = Path("data/indices")

            if indices_dir.exists():
                for index_file in indices_dir.glob("*.idx"):
                    meta_file = index_file.with_suffix('.meta')
                    if meta_file.exists():
                        try:
                            # 尝试读取元数据文件
                            try:
                                with open(meta_file, 'rb') as f:
                                    metadata = pickle.load(f)
                                    total_vectors += metadata.get('vector_count', 0)
                            except Exception:
                                with open(meta_file, 'r', encoding='utf-8') as f:
                                    metadata = json.load(f)
                                    total_vectors += metadata.get('vector_count', 0)
                        except Exception:
                            continue

            return total_vectors
        except Exception:
            return 0

    def _get_models_count_fallback(self):
        """备用方法：统计可用模型数量"""
        try:
            # 统计默认模型
            default_models = [
                "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
                "sentence-transformers/all-MiniLM-L6-v2",
                "sentence-transformers/all-mpnet-base-v2"
            ]

            # 尝试检测本地模型
            local_models = 0
            try:
                import requests
                # 检查Ollama
                response = requests.get("http://localhost:11434/api/tags", timeout=2)
                if response.status_code == 200:
                    data = response.json()
                    local_models += len(data.get('models', []))
            except Exception:
                pass

            return len(default_models) + local_models
        except Exception:
            return 3  # 至少有3个默认模型

    def refresh_stats(self):
        """刷新统计数据（供外部调用）"""
        self._update_stats()

    def _force_update_stats(self):
        """强制更新统计数据，使用备用方法"""
        try:
            import logging
            logger = logging.getLogger(__name__)
            logger.info("强制更新统计数据")

            # 直接使用备用方法获取数据
            total_indices = self._get_indices_count_fallback()
            total_vectors = self._get_vectors_count_fallback()
            total_models = self._get_models_count_fallback()
            total_queries = self._get_query_count()

            logger.info(f"统计数据: 索引={total_indices}, 向量={total_vectors}, 模型={total_models}, 查询={total_queries}")

            # 更新卡片
            self.vectors_card.update_data(
                self.translator.get_text("total_vectors", "向量总数"),
                f"{total_vectors:,}"
            )
            self.indices_card.update_data(
                self.translator.get_text("total_indices", "索引总数"),
                str(total_indices)
            )
            self.models_card.update_data(
                self.translator.get_text("total_models", "模型总数"),
                str(total_models)
            )
            self.queries_card.update_data(
                self.translator.get_text("total_queries", "查询总数"),
                f"{total_queries:,}"
            )

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"强制更新统计数据时出错: {e}")

    def on_state_vectors_added(self, index_path: str, vector_count: int):
        """状态管理器通知：向量已添加"""
        self._update_stats()

    def on_state_index_created(self, index_path: str):
        """状态管理器通知：索引已创建"""
        self._update_stats()

    def on_state_index_updated(self, index_path: str):
        """状态管理器通知：索引已更新"""
        self._update_stats()

    def on_state_model_changed(self, model_name: str):
        """状态管理器通知：模型已变更"""
        self._update_stats()

    def _on_create_index(self):
        """创建索引按钮点击处理"""
        # 切换到索引页面
        # 获取主窗口
        main_window = self.window()

        # 检查主窗口是否有tab_widget属性
        if hasattr(main_window, 'tab_widget'):
            # 遍历所有标签页
            for i in range(main_window.tab_widget.count()):
                widget = main_window.tab_widget.widget(i)
                if hasattr(widget, 'index_name_input'):  # 索引页面的特征
                    main_window.tab_widget.setCurrentIndex(i)
                    break

    def _on_vectorize_data(self):
        """向量化数据按钮点击处理"""
        # 切换到向量化页面
        # 获取主窗口
        main_window = self.window()

        # 检查主窗口是否有tab_widget属性
        if hasattr(main_window, 'tab_widget'):
            # 遍历所有标签页
            for i in range(main_window.tab_widget.count()):
                widget = main_window.tab_widget.widget(i)
                if hasattr(widget, 'vectorize_button'):  # 向量化页面的特征
                    main_window.tab_widget.setCurrentIndex(i)
                    break

    def _on_search_vectors(self):
        """搜索向量按钮点击处理"""
        # 切换到搜索页面
        # 获取主窗口
        main_window = self.window()

        # 检查主窗口是否有tab_widget属性
        if hasattr(main_window, 'tab_widget'):
            # 遍历所有标签页
            for i in range(main_window.tab_widget.count()):
                widget = main_window.tab_widget.widget(i)
                if hasattr(widget, 'search_button'):  # 搜索页面的特征
                    main_window.tab_widget.setCurrentIndex(i)
                    break

    def _on_visualize_vectors(self):
        """可视化向量按钮点击处理"""
        # 切换到可视化页面
        # 获取主窗口
        main_window = self.window()

        # 检查主窗口是否有tab_widget属性
        if hasattr(main_window, 'tab_widget'):
            # 遍历所有标签页
            for i in range(main_window.tab_widget.count()):
                widget = main_window.tab_widget.widget(i)
                if hasattr(widget, 'plot_button'):  # 可视化页面的特征
                    main_window.tab_widget.setCurrentIndex(i)
                    break

    def on_language_changed(self):
        """语言变更回调"""
        try:
            logger.info(f"DashboardWidget开始语言更新，当前语言: {self.translator.current_language}")
            logger.info(f"翻译器可用语言: {list(self.translator.translations.keys())}")

            # 测试翻译器
            test_text = self.translator.get_text("welcome_message", "欢迎使用MD向量处理器")
            logger.info(f"测试翻译结果: welcome_message -> {test_text}")

            # 更新欢迎标签
            welcome_label = self.findChild(QLabel, "welcomeLabel")
            if welcome_label:
                welcome_label.setText(test_text)
                logger.info(f"成功更新welcomeLabel文本为: {test_text}")
            else:
                logger.warning("未找到welcomeLabel组件")

            # 更新区域标签
            section_labels = self.findChildren(QLabel, "sectionLabel")
            if len(section_labels) >= 2:
                section_labels[0].setText(self.translator.get_text("statistics", "统计信息"))
                section_labels[1].setText(self.translator.get_text("quick_actions", "快速操作"))
            else:
                logger.warning(f"找到的sectionLabel组件数量不足: {len(section_labels)}")

            # 更新刷新按钮
            if hasattr(self, 'refresh_stats_btn') and self.refresh_stats_btn:
                self.refresh_stats_btn.setText(self.translator.get_text("refresh", "刷新"))

            # 更新统计卡片
            self._update_stats()

            # 更新操作卡片
            if hasattr(self, 'index_card') and self.index_card:
                self.index_card.update_data(
                    self.translator.get_text("create_index", "创建索引"),
                    self.translator.get_text("create_index_desc", "创建新的向量索引，支持多种索引类型和量化方法"),
                    self.translator.get_text("create", "创建")
                )

            if hasattr(self, 'vectorize_card') and self.vectorize_card:
                self.vectorize_card.update_data(
                    self.translator.get_text("vectorize_data", "向量化数据"),
                    self.translator.get_text("vectorize_data_desc", "将文本、图像或其他数据转换为向量表示"),
                    self.translator.get_text("vectorize", "向量化")
                )

            if hasattr(self, 'search_card') and self.search_card:
                self.search_card.update_data(
                    self.translator.get_text("search_vectors", "搜索向量"),
                    self.translator.get_text("search_vectors_desc", "在向量索引中搜索相似内容"),
                    self.translator.get_text("search", "搜索")
                )

            if hasattr(self, 'visualize_card') and self.visualize_card:
                self.visualize_card.update_data(
                    self.translator.get_text("visualize_vectors", "可视化向量"),
                    self.translator.get_text("visualize_vectors_desc", "可视化向量空间，探索数据分布"),
                    self.translator.get_text("visualize", "可视化")
                )

            logger.info("DashboardWidget语言更新完成")

        except Exception as e:
            logger.error(f"DashboardWidget语言更新时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
