#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
索引小部件
"""

# 尝试导入 PyQt6，如果失败则使用 PyQt5
try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QComboBox, QSpinBox, QFormLayout, QGroupBox, QTabWidget,
        QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
        QProgressDialog
    )
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    QT_VERSION = 6
except ImportError:
    from PyQt5.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QComboBox, QSpinBox, QFormLayout, QGroupBox, QTabWidget,
        QTableWidget, QTable<PERSON>idgetI<PERSON>, QHeader<PERSON>iew, QMessageBox,
        QProgressDialog
    )
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    QT_VERSION = 5

from ..i18n import Translator

class IndexWidget(QWidget):
    """索引小部件"""

    def __init__(self, translator: Translator):
        """
        初始化索引小部件

        Args:
            translator: 翻译器实例
        """
        super().__init__()

        self.translator = translator
        self.translator.add_observer(self)

        # 设置对象名，用于样式表
        self.setObjectName("indexWidget")

        # 初始化UI
        self._init_ui()

    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建标题标签
        title_label = QLabel(self.translator.get_text("index_management", "索引管理"))
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        main_layout.addWidget(title_label)

        # 创建标签页控件
        tab_widget = QTabWidget()

        # 创建"创建索引"标签页
        create_tab = QWidget()
        create_layout = QVBoxLayout(create_tab)

        # 创建表单布局
        form_layout = QFormLayout()

        # 添加表单字段
        self.index_name_input = QComboBox()
        self.index_name_input.setEditable(True)
        self.index_name_input.addItems(["new_index", "test_index", "my_index"])

        self.index_type_combo = QComboBox()
        self.index_type_combo.addItems(["flat", "ivf", "hnsw", "hybrid"])

        self.dimension_spin = QSpinBox()
        self.dimension_spin.setRange(1, 10000)
        self.dimension_spin.setValue(384)

        self.metric_combo = QComboBox()
        self.metric_combo.addItems(["cosine", "l2", "ip"])

        self.quantization_combo = QComboBox()
        self.quantization_combo.addItems(["none", "pq", "opq", "sq"])

        # 添加字段到表单
        form_layout.addRow(self.translator.get_text("index_name", "索引名称:"), self.index_name_input)
        form_layout.addRow(self.translator.get_text("index_type", "索引类型:"), self.index_type_combo)
        form_layout.addRow(self.translator.get_text("dimension", "维度:"), self.dimension_spin)
        form_layout.addRow(self.translator.get_text("metric", "度量方式:"), self.metric_combo)
        form_layout.addRow(self.translator.get_text("quantization", "量化方法:"), self.quantization_combo)

        create_layout.addLayout(form_layout)

        # 添加高级选项组
        advanced_group = QGroupBox(self.translator.get_text("advanced_options", "高级选项"))
        advanced_layout = QFormLayout(advanced_group)

        self.n_lists_spin = QSpinBox()
        self.n_lists_spin.setRange(1, 1000)
        self.n_lists_spin.setValue(100)

        self.n_probes_spin = QSpinBox()
        self.n_probes_spin.setRange(1, 100)
        self.n_probes_spin.setValue(10)

        self.ef_construction_spin = QSpinBox()
        self.ef_construction_spin.setRange(1, 1000)
        self.ef_construction_spin.setValue(200)

        self.ef_search_spin = QSpinBox()
        self.ef_search_spin.setRange(1, 1000)
        self.ef_search_spin.setValue(100)

        advanced_layout.addRow(self.translator.get_text("n_lists", "聚类数量:"), self.n_lists_spin)
        advanced_layout.addRow(self.translator.get_text("n_probes", "探测数量:"), self.n_probes_spin)
        advanced_layout.addRow(self.translator.get_text("ef_construction", "构建精度:"), self.ef_construction_spin)
        advanced_layout.addRow(self.translator.get_text("ef_search", "搜索精度:"), self.ef_search_spin)

        create_layout.addWidget(advanced_group)

        # 添加按钮
        buttons_layout = QHBoxLayout()

        self.create_button = QPushButton(self.translator.get_text("create_index", "创建索引"))
        self.create_button.setObjectName("primaryButton")
        self.create_button.setCursor(Qt.CursorShape.PointingHandCursor)

        self.reset_button = QPushButton(self.translator.get_text("reset", "重置"))
        self.reset_button.setObjectName("secondaryButton")
        self.reset_button.setCursor(Qt.CursorShape.PointingHandCursor)

        buttons_layout.addWidget(self.reset_button)
        buttons_layout.addWidget(self.create_button)

        create_layout.addLayout(buttons_layout)

        # 创建"管理索引"标签页
        manage_tab = QWidget()
        manage_layout = QVBoxLayout(manage_tab)

        # 创建索引表格
        self.index_table = QTableWidget(0, 5)
        self.index_table.setHorizontalHeaderLabels([
            self.translator.get_text("name", "名称"),
            self.translator.get_text("type", "类型"),
            self.translator.get_text("dimension", "维度"),
            self.translator.get_text("vectors", "向量数"),
            self.translator.get_text("actions", "操作")
        ])
        self.index_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        manage_layout.addWidget(self.index_table)

        # 添加按钮
        manage_buttons_layout = QHBoxLayout()

        self.refresh_button = QPushButton(self.translator.get_text("refresh", "刷新"))
        self.refresh_button.setObjectName("secondaryButton")
        self.refresh_button.setCursor(Qt.CursorShape.PointingHandCursor)

        self.import_button = QPushButton(self.translator.get_text("import", "导入"))
        self.import_button.setObjectName("secondaryButton")
        self.import_button.setCursor(Qt.CursorShape.PointingHandCursor)

        manage_buttons_layout.addWidget(self.refresh_button)
        manage_buttons_layout.addWidget(self.import_button)

        manage_layout.addLayout(manage_buttons_layout)

        # 添加标签页到标签页控件
        tab_widget.addTab(create_tab, self.translator.get_text("create_index", "创建索引"))
        tab_widget.addTab(manage_tab, self.translator.get_text("manage_indices", "管理索引"))

        main_layout.addWidget(tab_widget)

        # 连接信号
        self._connect_signals()

    def _connect_signals(self):
        """连接信号和槽"""
        self.create_button.clicked.connect(self._on_create_index)
        self.reset_button.clicked.connect(self._on_reset_form)
        self.refresh_button.clicked.connect(self._on_refresh_indices)
        self.import_button.clicked.connect(self._on_import_index)

    def _on_create_index(self):
        """创建索引按钮点击处理"""
        try:
            # 获取表单数据
            index_name = self.index_name_input.currentText().strip()
            if not index_name:
                QMessageBox.warning(
                    self,
                    self.translator.get_text("warning", "警告"),
                    self.translator.get_text("index_name_required", "索引名称不能为空")
                )
                return

            index_type = self.index_type_combo.currentText()
            dimension = self.dimension_spin.value()
            metric = self.metric_combo.currentText()
            quantization = self.quantization_combo.currentText()
            n_lists = self.n_lists_spin.value()
            n_probes = self.n_probes_spin.value()
            ef_construction = self.ef_construction_spin.value()
            ef_search = self.ef_search_spin.value()

            # 创建配置
            config = {
                'indexing': {
                    'index_type': index_type,
                    'metric': metric,
                    'quantization': quantization,
                    'n_lists': n_lists,
                    'n_probes': n_probes,
                    'ef_construction': ef_construction,
                    'ef_search': ef_search
                }
            }

            # 显示进度对话框
            progress_dialog = QProgressDialog(
                self.translator.get_text("creating_index", "正在创建索引..."),
                self.translator.get_text("cancel", "取消"),
                0, 100,
                self
            )
            progress_dialog.setWindowTitle(self.translator.get_text("creating_index", "正在创建索引"))
            progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
            progress_dialog.setValue(10)

            # 导入必要的模块
            from pathlib import Path
            import os
            import logging
            from src.indexer.builder import IndexBuilder

            # 设置日志
            logger = logging.getLogger(__name__)

            # 创建索引目录
            indices_dir = Path("data/indices")
            indices_dir.mkdir(parents=True, exist_ok=True)

            # 创建索引文件路径
            index_path = indices_dir / f"{index_name}.idx"

            # 检查索引是否已存在
            if index_path.exists():
                reply = QMessageBox.question(
                    self,
                    self.translator.get_text("confirm", "确认"),
                    self.translator.get_text("index_exists", "索引已存在，是否覆盖？"),
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )
                if reply == QMessageBox.StandardButton.No:
                    progress_dialog.close()
                    return

            progress_dialog.setValue(20)

            # 创建索引构建器
            builder = IndexBuilder(config)

            # 创建索引
            logger.info(f"创建{dimension}维度的索引...")
            builder.create_index(dimension)

            progress_dialog.setValue(50)

            # 保存索引
            logger.info(f"保存索引到 {index_path}...")
            success = builder.save_index(index_path)

            progress_dialog.setValue(90)

            # 刷新索引列表
            self._on_refresh_indices()

            progress_dialog.setValue(100)
            progress_dialog.close()

            if success:
                QMessageBox.information(
                    self,
                    self.translator.get_text("success", "成功"),
                    self.translator.get_text("index_created", "索引创建成功")
                )
            else:
                QMessageBox.critical(
                    self,
                    self.translator.get_text("error", "错误"),
                    self.translator.get_text("index_creation_failed", "索引创建失败")
                )

        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"创建索引时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

            QMessageBox.critical(
                self,
                self.translator.get_text("error", "错误"),
                f"{self.translator.get_text('index_creation_failed', '索引创建失败')}: {str(e)}"
            )

    def _on_reset_form(self):
        """重置表单按钮点击处理"""
        self.index_name_input.setCurrentIndex(0)
        self.index_type_combo.setCurrentIndex(0)
        self.dimension_spin.setValue(384)
        self.metric_combo.setCurrentIndex(0)
        self.quantization_combo.setCurrentIndex(0)
        self.n_lists_spin.setValue(100)
        self.n_probes_spin.setValue(10)
        self.ef_construction_spin.setValue(200)
        self.ef_search_spin.setValue(100)

    def _on_refresh_indices(self):
        """刷新索引按钮点击处理"""
        try:
            # 导入必要的模块
            from pathlib import Path
            import pickle
            import logging

            # 设置日志
            logger = logging.getLogger(__name__)

            # 清空表格
            self.index_table.setRowCount(0)

            # 获取索引目录
            indices_dir = Path("data/indices")
            if not indices_dir.exists():
                return

            # 获取所有索引文件
            index_files = list(indices_dir.glob("*.idx"))

            # 更新其他页面的索引下拉框
            # 获取主窗口
            main_window = self.window()

            # 检查主窗口是否有tab_widget属性
            if hasattr(main_window, 'tab_widget'):
                # 遍历所有标签页
                for i in range(main_window.tab_widget.count()):
                    widget = main_window.tab_widget.widget(i)
                    # 更新搜索页面的索引下拉框
                    if hasattr(widget, 'index_combo'):
                        widget.index_combo.clear()
                    # 更新可视化页面的索引下拉框
                    if hasattr(widget, 'refresh_indices'):
                        widget.refresh_indices()

            # 添加索引到表格
            for i, index_file in enumerate(index_files):
                # 加载元数据
                try:
                    meta_file = index_file.with_suffix('.meta')
                    # 首先尝试以二进制方式读取（pickle格式）
                    try:
                        with open(meta_file, 'rb') as f:
                            metadata = pickle.load(f)
                    except Exception as e:
                        # 如果二进制读取失败，尝试以文本方式读取（JSON格式）
                        logger.debug(f"以二进制方式读取元数据失败: {e}，尝试以文本方式读取")
                        import json
                        with open(meta_file, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)

                    # 添加行
                    self.index_table.insertRow(i)

                    # 设置名称
                    name_item = QTableWidgetItem(index_file.stem)
                    self.index_table.setItem(i, 0, name_item)

                    # 设置类型
                    type_item = QTableWidgetItem(metadata.get('index_type', 'unknown'))
                    self.index_table.setItem(i, 1, type_item)

                    # 设置维度
                    dimension_item = QTableWidgetItem(str(metadata.get('dimension', 0)))
                    self.index_table.setItem(i, 2, dimension_item)

                    # 设置向量数
                    vectors_item = QTableWidgetItem(str(metadata.get('total_vectors', 0)))
                    self.index_table.setItem(i, 3, vectors_item)

                    # 创建操作按钮
                    actions_widget = QWidget()
                    actions_layout = QHBoxLayout(actions_widget)
                    actions_layout.setContentsMargins(0, 0, 0, 0)

                    # 加载按钮
                    load_button = QPushButton(self.translator.get_text("load", "加载"))
                    load_button.setObjectName("smallButton")
                    load_button.setCursor(Qt.CursorShape.PointingHandCursor)
                    load_button.clicked.connect(lambda checked, file=index_file: self._on_load_index(file))

                    # 删除按钮
                    delete_button = QPushButton(self.translator.get_text("delete", "删除"))
                    delete_button.setObjectName("smallDangerButton")
                    delete_button.setCursor(Qt.CursorShape.PointingHandCursor)
                    delete_button.clicked.connect(lambda checked, file=index_file: self._on_delete_index(file))

                    actions_layout.addWidget(load_button)
                    actions_layout.addWidget(delete_button)

                    self.index_table.setCellWidget(i, 4, actions_widget)

                    # 添加到其他页面的索引下拉框
                    if hasattr(main_window, 'tab_widget'):
                        for j in range(main_window.tab_widget.count()):
                            widget = main_window.tab_widget.widget(j)
                            if hasattr(widget, 'index_combo'):
                                widget.index_combo.addItem(index_file.stem)

                except Exception as e:
                    logger.error(f"加载索引元数据时出错: {e}")

        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"刷新索引列表时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _on_load_index(self, index_file):
        """
        加载索引按钮点击处理

        Args:
            index_file: 索引文件路径

        Returns:
            bool: 是否成功加载索引
        """
        try:
            # 导入必要的模块
            import logging
            from src.indexer.builder import IndexBuilder
            import pickle

            # 设置日志
            logger = logging.getLogger(__name__)

            # 检查索引文件是否存在
            if not index_file.exists():
                logger.error(f"索引文件不存在: {index_file}")
                QMessageBox.critical(
                    self,
                    self.translator.get_text("error", "错误"),
                    self.translator.get_text("index_file_not_found", "索引文件不存在")
                )
                return False

            # 检查元数据文件是否存在
            meta_file = index_file.with_suffix('.meta')
            if not meta_file.exists():
                logger.error(f"索引元数据文件不存在: {meta_file}")
                QMessageBox.critical(
                    self,
                    self.translator.get_text("error", "错误"),
                    self.translator.get_text("meta_file_not_found", "索引元数据文件不存在")
                )
                return False

            # 显示进度对话框
            progress_dialog = QProgressDialog(
                self.translator.get_text("loading_index", "正在加载索引..."),
                self.translator.get_text("cancel", "取消"),
                0, 100,
                self
            )
            progress_dialog.setWindowTitle(self.translator.get_text("loading_index", "正在加载索引"))
            progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
            progress_dialog.setValue(10)

            # 获取索引类型
            try:
                # 首先尝试以二进制方式读取（pickle格式）
                try:
                    with open(meta_file, 'rb') as f:
                        metadata = pickle.load(f)
                        index_type = metadata.get('index_type', 'flat')
                        metric = metadata.get('metric', 'cosine')
                        logger.info(f"成功从二进制元数据加载索引信息: 类型={index_type}, 度量={metric}")
                except Exception as e:
                    # 如果二进制读取失败，尝试以文本方式读取（JSON格式）
                    logger.debug(f"以二进制方式读取元数据失败: {e}，尝试以文本方式读取")
                    import json
                    with open(meta_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                        index_type = metadata.get('index_type', 'flat')
                        metric = metadata.get('metric', 'cosine')
                        logger.info(f"成功从JSON元数据加载索引信息: 类型={index_type}, 度量={metric}")
            except Exception as e:
                logger.error(f"加载索引元数据时出错: {e}")
                progress_dialog.close()
                QMessageBox.critical(
                    self,
                    self.translator.get_text("error", "错误"),
                    f"{self.translator.get_text('meta_file_corrupted', '索引元数据文件已损坏')}: {str(e)}"
                )
                return False

            # 创建配置
            config = {
                'indexing': {
                    'index_type': index_type,
                    'metric': metric
                }
            }

            progress_dialog.setValue(30)

            # 创建索引构建器
            builder = IndexBuilder(config)

            # 加载索引
            logger.info(f"从 {index_file} 加载索引...")
            success = builder.load_index(index_file)

            progress_dialog.setValue(90)

            if success:
                # 保存当前加载的索引信息
                self.current_index = {
                    'name': index_file.stem,
                    'path': index_file,
                    'builder': builder
                }

                progress_dialog.setValue(100)
                progress_dialog.close()

                QMessageBox.information(
                    self,
                    self.translator.get_text("success", "成功"),
                    self.translator.get_text("index_loaded", "索引加载成功")
                )

                # 更新搜索页面的当前索引
                search_widget = None

                # 获取主窗口
                main_window = self.window()

                # 检查主窗口是否有tab_widget属性
                if hasattr(main_window, 'tab_widget'):
                    # 遍历所有标签页
                    for i in range(main_window.tab_widget.count()):
                        widget = main_window.tab_widget.widget(i)
                        if hasattr(widget, 'index_combo'):
                            search_widget = widget
                            break

                if search_widget:
                    index = search_widget.index_combo.findText(index_file.stem)
                    if index >= 0:
                        search_widget.index_combo.setCurrentIndex(index)

                return True
            else:
                progress_dialog.close()
                QMessageBox.critical(
                    self,
                    self.translator.get_text("error", "错误"),
                    self.translator.get_text("index_loading_failed", "索引加载失败")
                )
                return False

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"加载索引时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

            QMessageBox.critical(
                self,
                self.translator.get_text("error", "错误"),
                f"{self.translator.get_text('index_loading_failed', '索引加载失败')}: {str(e)}"
            )
            return False

    def _on_delete_index(self, index_file):
        """删除索引按钮点击处理"""
        try:
            # 确认删除
            reply = QMessageBox.question(
                self,
                self.translator.get_text("confirm", "确认"),
                self.translator.get_text("confirm_delete_index", "确定要删除此索引吗？"),
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.No:
                return

            # 删除索引文件
            import os
            if index_file.exists():
                os.remove(index_file)

            # 删除元数据文件
            meta_file = index_file.with_suffix('.meta')
            if meta_file.exists():
                os.remove(meta_file)

            # 刷新索引列表
            self._on_refresh_indices()

            QMessageBox.information(
                self,
                self.translator.get_text("success", "成功"),
                self.translator.get_text("index_deleted", "索引删除成功")
            )

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"删除索引时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

            QMessageBox.critical(
                self,
                self.translator.get_text("error", "错误"),
                f"{self.translator.get_text('index_deletion_failed', '索引删除失败')}: {str(e)}"
            )

    def _on_import_index(self):
        """导入索引按钮点击处理"""
        try:
            # 选择索引文件
            # 尝试导入 PyQt6，如果失败则使用 PyQt5
            try:
                from PyQt6.QtWidgets import QFileDialog
            except ImportError:
                from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                self.translator.get_text("select_index_file", "选择索引文件"),
                "",
                "Index Files (*.idx);;All Files (*)"
            )

            if not file_path:
                return

            # 导入索引
            from pathlib import Path
            import shutil

            source_path = Path(file_path)
            source_meta_path = source_path.with_suffix('.meta')

            if not source_meta_path.exists():
                QMessageBox.critical(
                    self,
                    self.translator.get_text("error", "错误"),
                    self.translator.get_text("meta_file_not_found", "未找到元数据文件")
                )
                return

            # 获取索引名称
            index_name = source_path.stem

            # 创建目标路径
            indices_dir = Path("data/indices")
            indices_dir.mkdir(parents=True, exist_ok=True)

            target_path = indices_dir / source_path.name
            target_meta_path = target_path.with_suffix('.meta')

            # 检查是否已存在
            if target_path.exists():
                reply = QMessageBox.question(
                    self,
                    self.translator.get_text("confirm", "确认"),
                    self.translator.get_text("index_exists", "索引已存在，是否覆盖？"),
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.No:
                    return

            # 复制文件
            shutil.copy2(source_path, target_path)
            shutil.copy2(source_meta_path, target_meta_path)

            # 刷新索引列表
            self._on_refresh_indices()

            QMessageBox.information(
                self,
                self.translator.get_text("success", "成功"),
                self.translator.get_text("index_imported", "索引导入成功")
            )

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"导入索引时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

            QMessageBox.critical(
                self,
                self.translator.get_text("error", "错误"),
                f"{self.translator.get_text('index_import_failed', '索引导入失败')}: {str(e)}"
            )

    def on_language_changed(self):
        """语言变更回调"""
        try:
            # 更新标题
            title_label = self.findChild(QLabel, "titleLabel")
            if title_label:
                title_label.setText(
                    self.translator.get_text("index_management", "索引管理")
                )
            else:
                logger.warning("未找到titleLabel组件")

            # 更新表单标签
            form_layout = self.findChild(QFormLayout)
            if form_layout:
                try:
                    if form_layout.rowCount() > 0:
                        label_item = form_layout.itemAt(0, QFormLayout.ItemRole.LabelRole)
                        if label_item and label_item.widget():
                            label_item.widget().setText(self.translator.get_text("index_name", "索引名称:"))

                    if form_layout.rowCount() > 1:
                        label_item = form_layout.itemAt(1, QFormLayout.ItemRole.LabelRole)
                        if label_item and label_item.widget():
                            label_item.widget().setText(self.translator.get_text("index_type", "索引类型:"))

                    if form_layout.rowCount() > 2:
                        label_item = form_layout.itemAt(2, QFormLayout.ItemRole.LabelRole)
                        if label_item and label_item.widget():
                            label_item.widget().setText(self.translator.get_text("dimension", "维度:"))

                    if form_layout.rowCount() > 3:
                        label_item = form_layout.itemAt(3, QFormLayout.ItemRole.LabelRole)
                        if label_item and label_item.widget():
                            label_item.widget().setText(self.translator.get_text("metric", "度量方式:"))

                    if form_layout.rowCount() > 4:
                        label_item = form_layout.itemAt(4, QFormLayout.ItemRole.LabelRole)
                        if label_item and label_item.widget():
                            label_item.widget().setText(self.translator.get_text("quantization", "量化方法:"))
                except Exception as e:
                    logger.warning(f"更新表单标签时出错: {e}")
            else:
                logger.warning("未找到表单布局")

            # 更新高级选项组
            advanced_group = self.findChild(QGroupBox)
            if advanced_group:
                advanced_group.setTitle(self.translator.get_text("advanced_options", "高级选项"))

                advanced_layout = advanced_group.layout()
                if advanced_layout and isinstance(advanced_layout, QFormLayout):
                    try:
                        if advanced_layout.rowCount() > 0:
                            label_item = advanced_layout.itemAt(0, QFormLayout.ItemRole.LabelRole)
                            if label_item and label_item.widget():
                                label_item.widget().setText(self.translator.get_text("n_lists", "聚类数量:"))

                        if advanced_layout.rowCount() > 1:
                            label_item = advanced_layout.itemAt(1, QFormLayout.ItemRole.LabelRole)
                            if label_item and label_item.widget():
                                label_item.widget().setText(self.translator.get_text("n_probes", "探测数量:"))

                        if advanced_layout.rowCount() > 2:
                            label_item = advanced_layout.itemAt(2, QFormLayout.ItemRole.LabelRole)
                            if label_item and label_item.widget():
                                label_item.widget().setText(self.translator.get_text("ef_construction", "构建精度:"))

                        if advanced_layout.rowCount() > 3:
                            label_item = advanced_layout.itemAt(3, QFormLayout.ItemRole.LabelRole)
                            if label_item and label_item.widget():
                                label_item.widget().setText(self.translator.get_text("ef_search", "搜索精度:"))
                    except Exception as e:
                        logger.warning(f"更新高级选项标签时出错: {e}")

            # 更新按钮
            if hasattr(self, 'create_button') and self.create_button:
                self.create_button.setText(self.translator.get_text("create_index", "创建索引"))
            if hasattr(self, 'reset_button') and self.reset_button:
                self.reset_button.setText(self.translator.get_text("reset", "重置"))
            if hasattr(self, 'refresh_button') and self.refresh_button:
                self.refresh_button.setText(self.translator.get_text("refresh", "刷新"))
            if hasattr(self, 'import_button') and self.import_button:
                self.import_button.setText(self.translator.get_text("import", "导入"))

            # 更新表格标题
            if hasattr(self, 'index_table') and self.index_table:
                self.index_table.setHorizontalHeaderLabels([
                    self.translator.get_text("name", "名称"),
                    self.translator.get_text("type", "类型"),
                    self.translator.get_text("dimension", "维度"),
                    self.translator.get_text("vectors", "向量数"),
                    self.translator.get_text("actions", "操作")
                ])

            # 更新标签页标题
            tab_widget = self.findChild(QTabWidget)
            if tab_widget:
                if tab_widget.count() > 0:
                    tab_widget.setTabText(0, self.translator.get_text("create_index", "创建索引"))
                if tab_widget.count() > 1:
                    tab_widget.setTabText(1, self.translator.get_text("manage_indices", "管理索引"))

            logger.info("IndexWidget语言更新完成")

        except Exception as e:
            logger.error(f"IndexWidget语言更新时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
