#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Ollama集成模块
提供与Ollama API的集成，用于向量化操作
"""

import requests
import numpy as np
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

class OllamaEmbedding:
    """Ollama嵌入类，用于调用Ollama API进行向量化"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化Ollama嵌入类

        Args:
            config: 配置字典
        """
        self.logger = logging.getLogger(__name__)

        # 获取配置
        ollama_config = config.get('local_models', {}).get('ollama', {})
        self.api_url = ollama_config.get('api_url', 'http://localhost:11434/api')
        self.model_name = ollama_config.get('default_model', 'llama2')

        # 从模型列表中查找指定模型的配置
        models = ollama_config.get('models', [])
        for model in models:
            if model.get('name') == self.model_name:
                self.parameters = model.get('parameters', {})
                break
        else:
            self.parameters = {}

        # 向量维度 - 根据模型不同可能需要调整
        self.vector_dimension = config.get('vectorization', {}).get('vector_dimension', 768)  # 更新默认维度为768

        self.logger.info(f"初始化Ollama嵌入类，使用模型: {self.model_name}，API地址: {self.api_url}")

    def encode_text(self, text: str) -> np.ndarray:
        """
        对单个文本进行向量化

        Args:
            text: 输入文本

        Returns:
            np.ndarray: 文本向量
        """
        if not text:
            return np.zeros(self.vector_dimension)

        try:
            # 构建请求
            endpoint = f"{self.api_url}/embeddings"
            payload = {
                "model": self.model_name,
                "prompt": text,
                **self.parameters
            }

            # 发送请求
            response = requests.post(endpoint, json=payload)
            response.raise_for_status()

            # 解析响应
            result = response.json()
            if 'embedding' in result:
                embedding = np.array(result['embedding'])

                # 如果向量维度与配置不符，进行调整
                if len(embedding) != self.vector_dimension:
                    self.logger.warning(f"Ollama返回的向量维度 ({len(embedding)}) 与配置的维度 ({self.vector_dimension}) 不符")
                    if len(embedding) > self.vector_dimension:
                        # 截断
                        embedding = embedding[:self.vector_dimension]
                    else:
                        # 填充
                        padding = np.zeros(self.vector_dimension - len(embedding))
                        embedding = np.concatenate([embedding, padding])

                return embedding
            else:
                self.logger.error(f"Ollama API返回的结果中没有embedding字段: {result}")
                return np.zeros(self.vector_dimension)

        except Exception as e:
            self.logger.error(f"调用Ollama API进行向量化时出错: {e}")
            return np.zeros(self.vector_dimension)

    def encode_batch(self, texts: List[str]) -> np.ndarray:
        """
        批量对文本进行向量化

        Args:
            texts: 文本列表

        Returns:
            np.ndarray: 文本向量矩阵
        """
        if not texts:
            return np.array([])

        # 逐个处理文本
        embeddings = []
        for text in texts:
            embedding = self.encode_text(text)
            embeddings.append(embedding)

        return np.array(embeddings)

# 尝试导入 PyQt6，如果失败则使用 PyQt5
try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QComboBox, QTextEdit, QFormLayout, QGroupBox, QSpinBox,
        QTableWidget, QTableWidgetItem, QHeaderView, QRadioButton, QButtonGroup,
        QMessageBox
    )
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    QT_VERSION = 6
except ImportError:
    from PyQt5.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QComboBox, QTextEdit, QFormLayout, QGroupBox, QSpinBox,
        QTableWidget, QTableWidgetItem, QHeaderView, QRadioButton, QButtonGroup,
        QMessageBox
    )
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    QT_VERSION = 5

from ..i18n import Translator

class SearchWidget(QWidget):
    """搜索小部件"""

    def __init__(self, translator: Translator):
        """
        初始化搜索小部件

        Args:
            translator: 翻译器实例
        """
        super().__init__()

        self.translator = translator
        self.translator.add_observer(self)

        # 设置对象名，用于样式表
        self.setObjectName("searchWidget")

        # 初始化UI
        self._init_ui()

    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建标题标签
        title_label = QLabel(self.translator.get_text("vector_search", "向量搜索"))
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        main_layout.addWidget(title_label)

        # 创建表单布局
        form_layout = QFormLayout()

        # 添加表单字段
        self.index_combo = QComboBox()
        self.index_combo.addItems(["index1", "index2", "index3"])

        self.k_spin = QSpinBox()
        self.k_spin.setRange(1, 100)
        self.k_spin.setValue(10)

        # 添加字段到表单
        form_layout.addRow(self.translator.get_text("select_index", "选择索引:"), self.index_combo)
        form_layout.addRow(self.translator.get_text("k_nearest", "返回数量 (k):"), self.k_spin)

        main_layout.addLayout(form_layout)

        # 添加查询输入区域
        query_group = QGroupBox(self.translator.get_text("query_input", "查询输入"))
        query_layout = QVBoxLayout(query_group)

        self.query_input = QTextEdit()
        self.query_input.setPlaceholderText(self.translator.get_text("enter_query_here", "在此输入查询文本..."))
        query_layout.addWidget(self.query_input)

        # 本地大模型选择
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel(self.translator.get_text("local_llm", "本地大模型:")))

        self.llm_combo = QComboBox()
        self.llm_combo.addItem(self.translator.get_text("no_llm", "不使用大模型"))
        self._load_local_llm_options()
        model_layout.addWidget(self.llm_combo)

        # 添加刷新按钮
        self.refresh_llm_button = QPushButton(self.translator.get_text("refresh", "刷新"))
        self.refresh_llm_button.clicked.connect(self._load_local_llm_options)
        model_layout.addWidget(self.refresh_llm_button)

        model_layout.addStretch()
        query_layout.addLayout(model_layout)

        main_layout.addWidget(query_group)

        # 添加按钮
        buttons_layout = QHBoxLayout()

        self.clear_button = QPushButton(self.translator.get_text("clear", "清除"))
        self.clear_button.setObjectName("secondaryButton")
        self.clear_button.setCursor(Qt.CursorShape.PointingHandCursor)

        self.search_button = QPushButton(self.translator.get_text("search", "搜索"))
        self.search_button.setObjectName("primaryButton")
        self.search_button.setCursor(Qt.CursorShape.PointingHandCursor)

        buttons_layout.addWidget(self.clear_button)
        buttons_layout.addWidget(self.search_button)

        # 添加AI问答按钮
        self.ai_chat_button = QPushButton(self.translator.get_text("ai_chat", "AI问答"))
        self.ai_chat_button.setObjectName("primaryButton")
        self.ai_chat_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.ai_chat_button.setEnabled(False)  # 默认禁用，当选择了大模型后启用
        buttons_layout.addWidget(self.ai_chat_button)

        main_layout.addLayout(buttons_layout)

        # 添加结果区域
        results_group = QGroupBox(self.translator.get_text("search_results", "搜索结果"))
        results_layout = QVBoxLayout(results_group)

        # 创建结果表格
        self.results_table = QTableWidget(0, 3)
        self.results_table.setHorizontalHeaderLabels([
            self.translator.get_text("rank", "排名"),
            self.translator.get_text("similarity", "相似度"),
            self.translator.get_text("content", "内容")
        ])

        # 设置表格列宽
        self.results_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 排名列自适应内容
        self.results_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 相似度列自适应内容
        self.results_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # 内容列占满剩余空间

        # 设置表格行高
        self.results_table.verticalHeader().setDefaultSectionSize(60)  # 设置默认行高为60像素

        # 设置表格选择模式
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)  # 选择整行
        self.results_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)  # 单选

        # 设置表格样式
        self.results_table.setAlternatingRowColors(True)  # 交替行颜色
        self.results_table.setShowGrid(True)  # 显示网格线
        self.results_table.setWordWrap(True)  # 自动换行

        results_layout.addWidget(self.results_table)

        main_layout.addWidget(results_group)

        # 连接信号
        self._connect_signals()

    def _connect_signals(self):
        """连接信号和槽"""
        self.clear_button.clicked.connect(self._on_clear)
        self.search_button.clicked.connect(self._on_search)
        self.ai_chat_button.clicked.connect(self._on_ai_chat)
        self.llm_combo.currentTextChanged.connect(self._on_llm_selection_changed)

    def _on_clear(self):
        """清除按钮点击处理"""
        self.query_input.clear()
        self.results_table.setRowCount(0)

    def _on_search(self):
        """搜索按钮点击处理"""
        # 获取查询文本
        query_text = self.query_input.toPlainText().strip()
        if not query_text:
            return

        # 获取索引和k值
        index_name = self.index_combo.currentText()
        k = self.k_spin.value()

        try:
            # 获取主窗口
            main_window = self.window()

            # 查找索引小部件
            index_widget = None
            if hasattr(main_window, 'tab_widget'):
                for i in range(main_window.tab_widget.count()):
                    widget = main_window.tab_widget.widget(i)
                    if hasattr(widget, 'index_name_input'):  # 索引页面的特征
                        index_widget = widget
                        break

            # 检查是否有加载的索引
            if index_widget and hasattr(index_widget, 'current_index') and index_widget.current_index:
                # 获取索引构建器
                builder = index_widget.current_index.get('builder')

                if builder:
                    # 导入必要的模块
                    from src.vectorizer import TextEmbedding
                    import numpy as np

                    # 创建文本嵌入器
                    config = {
                        'vectorization': {
                            'model_name': 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
                            'vector_dimension': 384,
                            'batch_size': 32,
                            'device': 'cpu',
                            'normalize_vectors': True
                        }
                    }
                    try:
                        import logging
                        logger = logging.getLogger(__name__)
                        embedder = TextEmbedding(config)
                        logger.info("成功创建文本嵌入器")
                    except Exception as e:
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.error(f"创建文本嵌入器时出错: {e}")
                        import traceback
                        logger.error(traceback.format_exc())
                        # 显示模拟结果并返回
                        self._display_mock_results(k)
                        return

                    # 生成查询向量
                    query_vector = embedder.encode_text(query_text)

                    # 执行搜索 - 获取更多结果，以便过滤后仍有足够的结果
                    distances, indices = builder.search(query_vector.reshape(1, -1), k * 2)

                    # 记录搜索结果
                    logger.info(f"搜索结果 - 距离: {distances}, 索引: {indices}")

                    # 检查搜索结果是否为空
                    if indices.size == 0 or indices[0].size == 0:
                        logger.warning("搜索结果为空，显示模拟结果")
                        self._display_mock_results(k)
                        return

                    # 过滤无效的结果
                    valid_indices = []
                    valid_distances = []

                    for i, idx in enumerate(indices[0]):
                        if idx >= 0:  # 只保留有效的索引ID
                            valid_indices.append(idx)
                            valid_distances.append(distances[0][i])
                        else:
                            logger.warning(f"过滤掉无效的索引ID: {idx}")

                    # 如果过滤后结果为空，显示模拟结果
                    if not valid_indices:
                        logger.warning("过滤后搜索结果为空，显示模拟结果")
                        self._display_mock_results(k)
                        return

                    # 限制结果数量
                    if len(valid_indices) > k:
                        valid_indices = valid_indices[:k]
                        valid_distances = valid_distances[:k]

                    # 重新构建结果数组
                    indices = np.array([valid_indices])
                    distances = np.array([valid_distances])

                    # 显示实际搜索结果
                    self._display_search_results(distances, indices, builder)
                    return

            # 如果没有加载索引或搜索失败，显示模拟结果
            self._display_mock_results(k)

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"搜索向量时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 显示模拟结果
            self._display_mock_results(k)

    def _display_search_results(self, distances, indices, builder):
        """
        显示实际的搜索结果

        Args:
            distances: 距离数组
            indices: 索引数组
            builder: 索引构建器
        """
        # 清空表格
        self.results_table.setRowCount(0)

        # 导入必要的模块
        from src.storage import MetadataManager
        import os
        import logging

        # 确保数据目录存在
        os.makedirs('data/vectors/metadata', exist_ok=True)

        # 创建元数据管理器
        config = {'storage': {'base_dir': 'data/vectors'}}
        try:
            metadata_manager = MetadataManager(config)
            logger = logging.getLogger(__name__)
            logger.info("成功创建元数据管理器")
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"创建元数据管理器时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            metadata_manager = None

        # 添加搜索结果
        for i, (distance, idx) in enumerate(zip(distances[0], indices[0])):
            # 记录索引ID
            logger = logging.getLogger(__name__)
            logger.info(f"搜索结果 #{i+1} - 原始索引ID: {idx}, 距离: {distance}")

            # 处理无效的索引ID
            if idx < 0:
                logger.warning(f"检测到无效的索引ID: {idx}，跳过此结果")
                continue

            # 获取实际的文档ID
            real_doc_id = None

            if metadata_manager:
                all_metadata = metadata_manager.get_all_metadata()
                logger.debug(f"元数据管理器中的总记录数: {len(all_metadata)}")

                if all_metadata:
                    # 方法1: 尝试直接使用索引ID作为文档ID
                    if idx in all_metadata:
                        real_doc_id = idx
                        logger.info(f"直接匹配成功: 索引ID {idx} 对应文档ID {real_doc_id}")
                    else:
                        # 方法2: 按顺序映射（索引位置对应元数据ID）
                        metadata_ids = sorted(list(all_metadata.keys()))

                        if idx < len(metadata_ids):
                            real_doc_id = metadata_ids[idx]
                            logger.info(f"顺序映射: 索引位置 {idx} 对应文档ID {real_doc_id}")
                        else:
                            # 方法3: 尝试使用向量存储中的ID映射
                            try:
                                from ...storage import VectorStore
                                storage_config = {'storage': {'base_dir': 'data/vectors'}}
                                vector_store = VectorStore(storage_config)

                                # 这里需要实现从向量存储获取ID映射的方法
                                # 暂时跳过这个结果
                                logger.warning(f"索引ID {idx} 超出元数据范围 (0-{len(metadata_ids)-1})，尝试其他方法")

                                # 作为最后的尝试，使用索引ID本身
                                real_doc_id = idx
                                logger.info(f"使用原始索引ID作为文档ID: {real_doc_id}")

                            except Exception as e:
                                logger.error(f"尝试获取ID映射时出错: {e}")
                                continue
                else:
                    logger.warning("元数据为空，无法进行ID映射")
                    continue
            else:
                logger.warning("元数据管理器未初始化")
                continue

            # 计算相似度分数
            try:
                # 处理无效的距离值
                if distance == float('-inf') or distance == float('inf') or distance <= -3.4028234e+38:
                    logger.warning(f"检测到无效的距离值: {distance}，将被视为0相似度")
                    similarity = 0.0
                elif builder.metric == 'cosine' or builder.metric == 'ip':
                    # 对于内积或余弦相似度，距离实际上是相似度
                    # 使用改进的相似度计算方法

                    # 首先归一化到0-1范围
                    if distance > 1.0:
                        raw_similarity = 1.0
                    elif distance < -1.0:
                        raw_similarity = 0.0
                    elif distance < 0:
                        raw_similarity = 0.0
                    else:
                        raw_similarity = float(distance)

                    # 应用非线性变换，增强高相似度的区分度
                    # 使用sigmoid函数的变体
                    import math
                    if raw_similarity > 0.5:
                        # 高相似度区域使用更陡峭的曲线
                        similarity = 0.5 + 0.5 * math.tanh((raw_similarity - 0.5) * 4)
                    else:
                        # 低相似度区域使用更平缓的曲线
                        similarity = 0.5 * math.tanh(raw_similarity * 2)

                    # 确保范围在0-1之间
                    similarity = max(0.0, min(1.0, similarity))

                else:  # l2
                    # 对于L2距离，较小的距离表示较高的相似度
                    # 使用改进的转换函数将距离转换为相似度
                    import math

                    # 使用更平滑的转换函数
                    if distance > 10:  # 距离太大，相似度接近0
                        similarity = 0.0
                    else:
                        # 使用指数衰减函数，对小距离更敏感
                        similarity = float(math.exp(-distance * 0.5))

                    # 确保范围在0-1之间
                    similarity = max(0.0, min(1.0, similarity))

                # 记录相似度计算结果
                logger.debug(f"距离值: {distance}, 计算得到的相似度: {similarity}")

            except Exception as e:
                logger = logging.getLogger(__name__)
                logger.error(f"计算相似度时出错: {e}, 距离值: {distance}")
                similarity = 0.0

            # 应用相似度阈值过滤 - 跳过低相似度的结果
            if similarity < 0.25:  # 设置最小相似度阈值
                logger.debug(f"跳过低相似度结果: {similarity:.4f}")
                continue

            # 获取元数据
            content = "未找到内容"
            try:
                if metadata_manager and real_doc_id is not None:
                    # 记录日志
                    logger = logging.getLogger(__name__)
                    logger.info(f"尝试获取元数据，文档ID: {real_doc_id}")

                    # 获取元数据
                    metadata = metadata_manager.get_metadata(int(real_doc_id))

                    # 检查元数据
                    if metadata:
                        logger.info(f"成功获取到元数据，文档ID: {real_doc_id}, 键: {metadata.keys()}")
                    else:
                        logger.warning(f"未找到元数据，文档ID: {real_doc_id}")

                        # 尝试获取所有元数据
                        all_metadata = metadata_manager.get_all_metadata()
                        logger.info(f"数据库中的所有元数据ID: {list(all_metadata.keys())}")

                        # 检查数据库统计信息
                        stats = metadata_manager.get_metadata_stats()
                        logger.info(f"元数据统计信息: {stats}")

                    # 获取内容
                    if metadata:

                        # 提取文本内容
                        content = "查询结果内容:\n\n"

                        if 'text' in metadata:
                            text_content = metadata['text']
                            # 限制内容长度，避免显示过长
                            if len(text_content) > 500:
                                text_content = text_content[:500] + "..."
                            content += text_content
                        elif 'content' in metadata:
                            text_content = metadata['content']
                            if len(text_content) > 500:
                                text_content = text_content[:500] + "..."
                            content += text_content
                        elif 'raw_text' in metadata:
                            text_content = metadata['raw_text']
                            if len(text_content) > 500:
                                text_content = text_content[:500] + "..."
                            content += text_content
                        elif 'tokens' in metadata:
                            # 如果有tokens但没有原始文本，尝试从tokens重建文本
                            tokens = metadata['tokens']
                            if isinstance(tokens, list) and tokens:
                                text_content = " ".join(tokens)
                                if len(text_content) > 500:
                                    text_content = text_content[:500] + "..."
                                content += text_content
                            else:
                                content += "无法显示内容（仅有tokens）"
                        elif 'filename' in metadata:
                            content += f"文件: {metadata['filename']}\n"

                            # 如果有章节信息，添加章节内容
                            if 'sections' in metadata and metadata['sections']:
                                content += f"\n章节数: {len(metadata['sections'])}\n"

                                # 添加章节内容
                                for j, section in enumerate(metadata['sections'][:3]):  # 只显示前3个章节
                                    if 'title' in section:
                                        content += f"\n章节 {j+1}: {section['title']}\n"
                                    if 'content' in section:
                                        section_content = section['content']
                                        if len(section_content) > 200:
                                            section_content = section_content[:200] + "..."
                                        content += f"{section_content}\n"

                                if len(metadata['sections']) > 3:
                                    content += f"\n... 还有 {len(metadata['sections']) - 3} 个章节 ..."
                        else:
                            # 如果没有找到任何内容字段，显示所有可用的元数据键
                            content += f"未找到文本内容，可用的元数据字段: {', '.join(metadata.keys())}"

                        # 添加其他有用的元数据信息
                        if 'tags' in metadata:
                            content += f"\n\n标签: {', '.join(metadata['tags'])}"
                        if 'created_at' in metadata:
                            content += f"\n创建时间: {metadata['created_at']}"
                        if 'author' in metadata:
                            content += f"\n作者: {metadata['author']}"
                else:
                    content = "元数据管理器未初始化，无法获取内容"
            except Exception as e:
                logger = logging.getLogger(__name__)
                logger.error(f"获取元数据时出错: {e}")
                content = f"获取内容时出错: {str(e)}"

            # 添加行
            row = self.results_table.rowCount()
            self.results_table.insertRow(row)

            # 添加单元格
            rank_item = QTableWidgetItem(str(i+1))
            self.results_table.setItem(row, 0, rank_item)

            similarity_item = QTableWidgetItem(f"{similarity:.4f}")
            self.results_table.setItem(row, 1, similarity_item)

            content_item = QTableWidgetItem(content)
            content_item.setToolTip(content)  # 添加工具提示，方便查看完整内容
            self.results_table.setItem(row, 2, content_item)

    def _display_mock_results(self, k):
        """
        显示模拟的搜索结果

        Args:
            k: 结果数量
        """
        # 清空表格
        self.results_table.setRowCount(0)

        # 添加模拟结果
        for i in range(k):
            similarity = 1.0 - (i * 0.05)

            # 创建更有用的模拟内容
            if i == 0:
                content = "【模拟结果】未找到实际内容\n\n"
                content += "这是模拟搜索结果，相似度最高。\n\n"
                content += "要查看实际的搜索结果，请按照以下步骤操作：\n"
                content += "1. 创建索引（点击左侧的INDEX按钮）\n"
                content += "2. 向量化一些数据（点击左侧的VECTORIZE按钮）\n"
                content += "3. 加载索引（在INDEX页面的管理索引标签页中）\n"
                content += "4. 返回此页面进行搜索"
            elif i == 1:
                content = "【模拟结果】未找到实际内容\n\n"
                content += "这是模拟搜索结果，相似度次高。\n\n"
                content += "搜索功能使用向量相似度来查找与查询文本最相似的文档。\n"
                content += "系统会将查询文本转换为向量，然后在索引中查找最相似的向量。\n"
                content += "相似度分数范围从0到1，1表示完全匹配。"
            else:
                content = "【模拟结果】未找到实际内容\n\n"
                content += f"这是第 {i+1} 个模拟搜索结果，相似度为 {similarity:.4f}\n\n"
                content += "当前没有加载索引或索引中没有数据，所以显示的是模拟结果。\n"
                content += "请先创建并加载索引，然后向量化一些数据，再进行搜索。"

            # 添加行
            row = self.results_table.rowCount()
            self.results_table.insertRow(row)

            # 添加单元格
            rank_item = QTableWidgetItem(str(i+1))
            self.results_table.setItem(row, 0, rank_item)

            similarity_item = QTableWidgetItem(f"{similarity:.4f}")
            self.results_table.setItem(row, 1, similarity_item)

            content_item = QTableWidgetItem(content)
            content_item.setToolTip(content)  # 添加工具提示，方便查看完整内容
            self.results_table.setItem(row, 2, content_item)

    def on_language_changed(self):
        """语言变更回调"""
        try:
            import logging
            logger = logging.getLogger(__name__)

            # 更新标题
            title_label = self.findChild(QLabel, "titleLabel")
            if title_label:
                title_label.setText(
                    self.translator.get_text("vector_search", "向量搜索")
                )
            else:
                logger.warning("未找到titleLabel组件")

            # 更新表单标签
            form_layout = self.findChild(QFormLayout)
            if form_layout:
                try:
                    if form_layout.rowCount() > 0:
                        label_item = form_layout.itemAt(0, QFormLayout.ItemRole.LabelRole)
                        if label_item and label_item.widget():
                            label_item.widget().setText(self.translator.get_text("select_index", "选择索引:"))

                    if form_layout.rowCount() > 1:
                        label_item = form_layout.itemAt(1, QFormLayout.ItemRole.LabelRole)
                        if label_item and label_item.widget():
                            label_item.widget().setText(self.translator.get_text("k_nearest", "返回数量 (k):"))
                except Exception as e:
                    logger.warning(f"更新表单标签时出错: {e}")
            else:
                logger.warning("未找到表单布局")

            # 更新查询输入组
            group_boxes = self.findChildren(QGroupBox)
            if len(group_boxes) > 0:
                query_group = group_boxes[0]
                if query_group:
                    query_group.setTitle(self.translator.get_text("query_input", "查询输入"))

            # 更新按钮
            if hasattr(self, 'clear_button') and self.clear_button:
                self.clear_button.setText(self.translator.get_text("clear", "清除"))
            if hasattr(self, 'search_button') and self.search_button:
                self.search_button.setText(self.translator.get_text("search", "搜索"))

            # 更新结果组
            if len(group_boxes) > 1:
                results_group = group_boxes[1]
                if results_group:
                    results_group.setTitle(self.translator.get_text("search_results", "搜索结果"))

            # 更新表格标题
            if hasattr(self, 'results_table') and self.results_table:
                self.results_table.setHorizontalHeaderLabels([
                    self.translator.get_text("rank", "排名"),
                    self.translator.get_text("similarity", "相似度"),
                    self.translator.get_text("content", "内容")
                ])

            logger.info("SearchWidget语言更新完成")

        except Exception as e:
            logger.error(f"SearchWidget语言更新时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _load_local_llm_options(self):
        """加载本地大模型选项"""
        try:
            from ...utils.local_model_manager import get_local_model_manager

            # 清空现有选项
            self.llm_combo.clear()
            self.llm_combo.addItem(self.translator.get_text("no_llm", "不使用大模型"))

            # 获取本地模型管理器
            model_manager = get_local_model_manager()

            # 获取支持对话的本地模型
            chat_models = model_manager.get_available_models(supports_chat=True)

            if chat_models:
                # 添加分隔符
                self.llm_combo.insertSeparator(self.llm_combo.count())
                self.llm_combo.addItem("--- 本地大模型 ---")

                # 添加本地模型
                for model in chat_models:
                    display_name = f"{model.type}:{model.model_id}"
                    self.llm_combo.addItem(display_name)
                    # 存储模型对象作为用户数据
                    self.llm_combo.setItemData(self.llm_combo.count() - 1, model)

                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"加载了 {len(chat_models)} 个本地对话模型")

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"加载本地大模型时出错: {e}")

    def _on_llm_selection_changed(self):
        """大模型选择变更处理"""
        current_text = self.llm_combo.currentText()

        # 检查是否选择了有效的大模型
        if current_text and current_text not in ["不使用大模型", "--- 本地大模型 ---"]:
            self.ai_chat_button.setEnabled(True)
        else:
            self.ai_chat_button.setEnabled(False)

    def _on_ai_chat(self):
        """AI问答按钮点击处理"""
        try:
            from .ai_chat_dialog import AIChatDialog

            # 创建AI对话窗口
            chat_dialog = AIChatDialog(self.translator, self)
            chat_dialog.set_search_widget(self)  # 设置搜索组件引用

            # 如果有查询文本，预填充到对话窗口
            query_text = self.query_input.toPlainText().strip()
            if query_text:
                chat_dialog.input_text.setText(query_text)

            # 显示对话窗口
            chat_dialog.exec()

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"打开AI对话窗口时出错: {e}")

            QMessageBox.critical(self, "错误", f"无法打开AI对话窗口: {str(e)}")

    def perform_search_for_context(self, query: str) -> List[str]:
        """为AI对话提供搜索上下文"""
        try:
            # 临时设置查询文本
            original_query = self.query_input.toPlainText()
            self.query_input.setText(query)

            # 执行搜索
            self._on_search()

            # 收集搜索结果
            context_texts = []
            for row in range(min(5, self.results_table.rowCount())):  # 取前5个结果
                content_item = self.results_table.item(row, 2)
                if content_item:
                    content = content_item.text()
                    if content and not content.startswith("【模拟结果】") and len(content.strip()) > 10:
                        context_texts.append(content)

            # 恢复原始查询文本
            self.query_input.setText(original_query)

            return context_texts

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"为AI对话执行搜索时出错: {e}")
            return []

    def _call_local_llm(self, model, query: str, context_texts: List[str]) -> str:
        """
        调用本地大模型进行问答

        Args:
            model: 本地模型对象
            query: 用户查询
            context_texts: 上下文文本列表

        Returns:
            str: 模型回答
        """
        try:
            # 构建提示词
            context = "\n\n".join(context_texts) if context_texts else "没有找到相关内容"

            prompt = f"""基于以下上下文信息回答用户的问题：

上下文信息：
{context}

用户问题：{query}

请基于上下文信息给出准确、有用的回答。如果上下文信息不足以回答问题，请说明这一点。"""

            if model.type == "ollama":
                return self._call_ollama_chat(model, prompt)
            elif model.type == "openai_compatible":
                return self._call_openai_compatible_chat(model, prompt)
            else:
                return f"暂不支持 {model.type} 类型的模型进行对话"

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"调用本地大模型时出错: {e}")
            return f"调用模型时出错: {str(e)}"

    def _call_ollama_chat(self, model, prompt: str) -> str:
        """调用Ollama模型进行对话"""
        try:
            import requests

            # 首先检查Ollama服务是否可用
            try:
                health_url = f"{model.endpoint}/api/tags"
                health_response = requests.get(health_url, timeout=5)
                if health_response.status_code != 200:
                    return "Ollama服务不可用，请确保Ollama已启动"
            except requests.exceptions.RequestException:
                return "无法连接到Ollama服务，请检查服务是否启动（端口11434）"

            url = f"{model.endpoint}/api/generate"
            data = {
                "model": model.model_id,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "num_predict": 500
                }
            }

            # 增加超时时间到3分钟
            response = requests.post(url, json=data, timeout=180)
            response.raise_for_status()

            result = response.json()
            return result.get("response", "模型没有返回回答")

        except requests.exceptions.Timeout:
            return "模型响应超时，请尝试使用更小的模型或减少输入长度"
        except requests.exceptions.ConnectionError:
            return "连接错误，请确保Ollama服务正在运行"
        except Exception as e:
            return f"调用Ollama模型时出错: {str(e)}"

    def _call_openai_compatible_chat(self, model, prompt: str) -> str:
        """调用OpenAI兼容模型进行对话"""
        try:
            import requests

            url = f"{model.endpoint}/v1/chat/completions"
            headers = {
                "Content-Type": "application/json"
            }

            # 添加API密钥（如果有）
            api_key = model.parameters.get('api_key')
            if api_key:
                headers["Authorization"] = f"Bearer {api_key}"

            data = {
                "model": model.model_id,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": 1000,
                "temperature": 0.7
            }

            response = requests.post(url, json=data, headers=headers, timeout=60)
            response.raise_for_status()

            result = response.json()
            if "choices" in result and result["choices"]:
                return result["choices"][0]["message"]["content"]
            else:
                return "模型没有返回回答"

        except Exception as e:
            return f"调用OpenAI兼容模型时出错: {str(e)}"

    def _display_ai_response(self, response: str):
        """显示AI回答"""
        # 在结果表格中添加AI回答
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        # 添加单元格
        rank_item = QTableWidgetItem("AI")
        self.results_table.setItem(row, 0, rank_item)

        similarity_item = QTableWidgetItem("N/A")
        self.results_table.setItem(row, 1, similarity_item)

        ai_content = f"🤖 AI回答:\n\n{response}"
        content_item = QTableWidgetItem(ai_content)
        content_item.setToolTip(ai_content)
        self.results_table.setItem(row, 2, content_item)

        # 滚动到AI回答
        self.results_table.scrollToItem(content_item)