#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设置小部件
"""

# 尝试导入 PyQt6，如果失败则使用 PyQt5
try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QComboBox, QFormLayout, QGroupBox, QSpinBox, QCheckBox,
        QTabWidget, QSlider, QFileDialog, QLineEdit
    )
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    QT_VERSION = 6
except ImportError:
    from PyQt5.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QComboBox, QFormLayout, QGroupBox, QSpinBox, QCheckBox,
        QTabWidget, QSlider, QFileDialog, QLineEdit
    )
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    QT_VERSION = 5

from ..i18n import Translator, Language
from ..theme import ThemeManager, Theme

class SettingsWidget(QWidget):
    """设置小部件"""

    def __init__(self, translator: Translator, theme_manager: ThemeManager):
        """
        初始化设置小部件

        Args:
            translator: 翻译器实例
            theme_manager: 主题管理器实例
        """
        super().__init__()

        self.translator = translator
        self.theme_manager = theme_manager
        self.translator.add_observer(self)

        # 设置对象名，用于样式表
        self.setObjectName("settingsWidget")

        # 初始化UI
        self._init_ui()

    def _init_ui(self):
        """初始化UI组件"""
        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建标题标签
        title_label = QLabel(self.translator.get_text("settings", "设置"))
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        main_layout.addWidget(title_label)

        # 创建标签页控件
        tab_widget = QTabWidget()

        # 创建"常规设置"标签页
        general_tab = QWidget()
        general_layout = QVBoxLayout(general_tab)

        # 创建语言设置组
        language_group = QGroupBox(self.translator.get_text("language_settings", "语言设置"))
        language_layout = QFormLayout(language_group)

        self.language_combo = QComboBox()
        for lang in Language:
            self.language_combo.addItem(lang.display_name, lang)

        # 设置当前语言
        for i in range(self.language_combo.count()):
            if self.language_combo.itemData(i) == self.translator.current_language:
                self.language_combo.setCurrentIndex(i)
                break

        language_layout.addRow(self.translator.get_text("interface_language", "界面语言:"), self.language_combo)

        general_layout.addWidget(language_group)

        # 创建主题设置组
        theme_group = QGroupBox(self.translator.get_text("theme_settings", "主题设置"))
        theme_layout = QFormLayout(theme_group)

        self.theme_combo = QComboBox()
        for theme in Theme:
            self.theme_combo.addItem(theme.display_name, theme)

        # 设置当前主题
        for i in range(self.theme_combo.count()):
            if self.theme_combo.itemData(i) == self.theme_manager.current_theme:
                self.theme_combo.setCurrentIndex(i)
                break

        theme_layout.addRow(self.translator.get_text("interface_theme", "界面主题:"), self.theme_combo)

        general_layout.addWidget(theme_group)

        # 创建"向量化设置"标签页
        vectorize_tab = QWidget()
        vectorize_layout = QVBoxLayout(vectorize_tab)

        # 创建模型设置组
        model_group = QGroupBox(self.translator.get_text("model_settings", "模型设置"))
        model_layout = QFormLayout(model_group)

        self.default_model_combo = QComboBox()
        self.default_model_combo.addItems([
            "multilingual-minilm",
            "multilingual-mpnet",
            "bert-base-multilingual",
            "xlm-roberta"
        ])

        self.model_cache_dir = QLineEdit()
        self.model_cache_dir.setText("./models")

        self.browse_cache_button = QPushButton(self.translator.get_text("browse", "浏览"))
        self.browse_cache_button.setObjectName("secondaryButton")
        self.browse_cache_button.setCursor(Qt.CursorShape.PointingHandCursor)

        cache_layout = QHBoxLayout()
        cache_layout.addWidget(self.model_cache_dir)
        cache_layout.addWidget(self.browse_cache_button)

        model_layout.addRow(self.translator.get_text("default_model", "默认模型:"), self.default_model_combo)
        model_layout.addRow(self.translator.get_text("model_cache_dir", "模型缓存目录:"), cache_layout)

        vectorize_layout.addWidget(model_group)

        # 创建语言设置组
        language_group = QGroupBox(self.translator.get_text("language_settings", "语言设置"))
        language_layout = QFormLayout(language_group)

        self.language_combo = QComboBox()
        self.language_combo.addItems(["中文", "English"])
        self.language_combo.setCurrentText("中文")

        self.use_gpu_check = QCheckBox()
        self.use_gpu_check.setChecked(True)

        language_layout.addRow(self.translator.get_text("language", "语言:"), self.language_combo)
        language_layout.addRow(self.translator.get_text("use_gpu", "使用GPU:"), self.use_gpu_check)

        vectorize_layout.addWidget(language_group)

        # 创建"索引设置"标签页
        index_tab = QWidget()
        index_layout = QVBoxLayout(index_tab)

        # 创建索引设置组
        index_group = QGroupBox(self.translator.get_text("index_settings", "索引设置"))
        index_layout_form = QFormLayout(index_group)

        self.default_index_type_combo = QComboBox()
        self.default_index_type_combo.addItems(["flat", "ivf", "hnsw", "hybrid"])

        self.default_metric_combo = QComboBox()
        self.default_metric_combo.addItems(["cosine", "l2", "ip"])

        self.default_quantization_combo = QComboBox()
        self.default_quantization_combo.addItems(["none", "pq", "sq"])

        self.index_dir = QLineEdit()
        self.index_dir.setText("./indices")

        self.browse_index_button = QPushButton(self.translator.get_text("browse", "浏览"))
        self.browse_index_button.setObjectName("secondaryButton")
        self.browse_index_button.setCursor(Qt.CursorShape.PointingHandCursor)

        index_dir_layout = QHBoxLayout()
        index_dir_layout.addWidget(self.index_dir)
        index_dir_layout.addWidget(self.browse_index_button)

        index_layout_form.addRow(self.translator.get_text("default_index_type", "默认索引类型:"), self.default_index_type_combo)
        index_layout_form.addRow(self.translator.get_text("default_metric", "默认度量方式:"), self.default_metric_combo)
        index_layout_form.addRow(self.translator.get_text("default_quantization", "默认量化方法:"), self.default_quantization_combo)
        index_layout_form.addRow(self.translator.get_text("index_directory", "索引目录:"), index_dir_layout)

        index_layout.addWidget(index_group)

        # 创建"本地模型设置"标签页
        local_models_tab = self._create_local_models_tab()

        # 添加标签页到标签页控件
        tab_widget.addTab(general_tab, self.translator.get_text("general", "常规"))
        tab_widget.addTab(vectorize_tab, self.translator.get_text("vectorization", "向量化"))
        tab_widget.addTab(index_tab, self.translator.get_text("indexing", "索引"))
        tab_widget.addTab(local_models_tab, self.translator.get_text("local_models", "本地模型"))

        main_layout.addWidget(tab_widget)

        # 添加按钮
        buttons_layout = QHBoxLayout()

        self.reset_button = QPushButton(self.translator.get_text("reset", "重置"))
        self.reset_button.setObjectName("secondaryButton")
        self.reset_button.setCursor(Qt.CursorShape.PointingHandCursor)

        self.apply_button = QPushButton(self.translator.get_text("apply", "应用"))
        self.apply_button.setObjectName("primaryButton")
        self.apply_button.setCursor(Qt.CursorShape.PointingHandCursor)

        buttons_layout.addWidget(self.reset_button)
        buttons_layout.addWidget(self.apply_button)

        main_layout.addLayout(buttons_layout)

        # 连接信号
        self._connect_signals()

    def _create_local_models_tab(self):
        """创建本地模型设置标签页"""
        from .local_model_config import LocalModelConfigWidget

        # 创建本地模型配置小部件
        local_models_widget = LocalModelConfigWidget(self.translator)

        return local_models_widget

    def _connect_signals(self):
        """连接信号和槽"""
        self.language_combo.currentIndexChanged.connect(self._on_language_changed)
        self.theme_combo.currentIndexChanged.connect(self._on_theme_changed)
        self.browse_cache_button.clicked.connect(self._on_browse_cache_dir)
        self.browse_index_button.clicked.connect(self._on_browse_index_dir)
        self.reset_button.clicked.connect(self._on_reset)
        self.apply_button.clicked.connect(self._on_apply)

    def _on_language_changed(self, index):
        """语言变更处理"""
        language = self.language_combo.itemData(index)
        if language != self.translator.current_language:
            self.translator.set_language(language)

    def _on_theme_changed(self, index):
        """主题变更处理"""
        theme = self.theme_combo.itemData(index)
        if theme != self.theme_manager.current_theme:
            self.theme_manager.set_theme(theme)

    def _on_browse_cache_dir(self):
        """浏览模型缓存目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            self.translator.get_text("select_model_cache_directory", "选择模型缓存目录"),
            self.model_cache_dir.text()
        )

        if dir_path:
            self.model_cache_dir.setText(dir_path)

    def _on_browse_index_dir(self):
        """浏览索引目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            self.translator.get_text("select_index_directory", "选择索引目录"),
            self.index_dir.text()
        )

        if dir_path:
            self.index_dir.setText(dir_path)

    def _on_reset(self):
        """重置按钮点击处理"""
        # 重置语言
        for i in range(self.language_combo.count()):
            if self.language_combo.itemData(i) == Language.ENGLISH:
                self.language_combo.setCurrentIndex(i)
                break

        # 重置主题
        for i in range(self.theme_combo.count()):
            if self.theme_combo.itemData(i) == Theme.SYSTEM:
                self.theme_combo.setCurrentIndex(i)
                break

        # 重置其他设置
        self.default_model_combo.setCurrentIndex(0)
        self.model_cache_dir.setText("./models")
        self.batch_size_spin.setValue(32)
        self.use_gpu_check.setChecked(True)
        self.default_index_type_combo.setCurrentIndex(0)
        self.default_metric_combo.setCurrentIndex(0)
        self.default_quantization_combo.setCurrentIndex(0)
        self.index_dir.setText("./indices")

    def _on_apply(self):
        """应用按钮点击处理"""
        # 应用语言
        language = self.language_combo.itemData(self.language_combo.currentIndex())
        self.translator.set_language(language)

        # 应用主题
        theme = self.theme_combo.itemData(self.theme_combo.currentIndex())
        self.theme_manager.set_theme(theme)

        # TODO: 应用其他设置

    def on_language_changed(self):
        """语言变更回调"""
        try:
            # 更新标题
            title_label = self.findChild(QLabel, "titleLabel")
            if title_label:
                title_label.setText(
                    self.translator.get_text("settings", "设置")
                )
            else:
                logger.warning("未找到titleLabel组件")

            # 更新语言设置组
            group_boxes = self.findChildren(QGroupBox)
            if len(group_boxes) > 0:
                language_group = group_boxes[0]
                if language_group:
                    language_group.setTitle(self.translator.get_text("language_settings", "语言设置"))

                    language_layout = language_group.layout()
                    if language_layout and isinstance(language_layout, QFormLayout):
                        try:
                            if language_layout.rowCount() > 0:
                                label_item = language_layout.itemAt(0, QFormLayout.ItemRole.LabelRole)
                                if label_item and label_item.widget():
                                    label_item.widget().setText(self.translator.get_text("interface_language", "界面语言:"))
                        except Exception as e:
                            logger.warning(f"更新语言设置标签时出错: {e}")

            # 更新主题设置组
            if len(group_boxes) > 1:
                theme_group = group_boxes[1]
                if theme_group:
                    theme_group.setTitle(self.translator.get_text("theme_settings", "主题设置"))

                    theme_layout = theme_group.layout()
                    if theme_layout and isinstance(theme_layout, QFormLayout):
                        try:
                            if theme_layout.rowCount() > 0:
                                label_item = theme_layout.itemAt(0, QFormLayout.ItemRole.LabelRole)
                                if label_item and label_item.widget():
                                    label_item.widget().setText(self.translator.get_text("interface_theme", "界面主题:"))
                        except Exception as e:
                            logger.warning(f"更新主题设置标签时出错: {e}")

            # 更新模型设置组
            if len(group_boxes) > 2:
                model_group = group_boxes[2]
                if model_group:
                    model_group.setTitle(self.translator.get_text("model_settings", "模型设置"))

                    model_layout = model_group.layout()
                    if model_layout and isinstance(model_layout, QFormLayout):
                        try:
                            if model_layout.rowCount() > 0:
                                label_item = model_layout.itemAt(0, QFormLayout.ItemRole.LabelRole)
                                if label_item and label_item.widget():
                                    label_item.widget().setText(self.translator.get_text("default_model", "默认模型:"))

                            if model_layout.rowCount() > 1:
                                label_item = model_layout.itemAt(1, QFormLayout.ItemRole.LabelRole)
                                if label_item and label_item.widget():
                                    label_item.widget().setText(self.translator.get_text("model_cache_dir", "模型缓存目录:"))
                        except Exception as e:
                            logger.warning(f"更新模型设置标签时出错: {e}")

            # 更新语言设置组
            group_boxes = self.findChildren(QGroupBox)
            if len(group_boxes) > 3:
                language_group = group_boxes[3]
                if language_group:
                    language_group.setTitle(self.translator.get_text("language_settings", "语言设置"))

                    language_layout = language_group.layout()
                    if language_layout and isinstance(language_layout, QFormLayout):
                        try:
                            # 安全地获取标签项
                            if language_layout.rowCount() > 0:
                                language_item = language_layout.itemAt(0, QFormLayout.ItemRole.LabelRole)
                                if language_item and language_item.widget():
                                    language_item.widget().setText(
                                        self.translator.get_text("language", "语言:"))

                            if language_layout.rowCount() > 1:
                                use_gpu_item = language_layout.itemAt(1, QFormLayout.ItemRole.LabelRole)
                                if use_gpu_item and use_gpu_item.widget():
                                    use_gpu_item.widget().setText(
                                        self.translator.get_text("use_gpu", "使用GPU:"))
                        except Exception as e:
                            logger.warning(f"更新语言设置标签时出错: {e}")

            # 更新索引设置组
            if len(group_boxes) > 4:
                index_group = group_boxes[4]
                if index_group:
                    index_group.setTitle(self.translator.get_text("index_settings", "索引设置"))

                    index_layout = index_group.layout()
                    if index_layout and isinstance(index_layout, QFormLayout):
                        try:
                            # 安全地获取标签项
                            if index_layout.rowCount() > 0:
                                index_type_item = index_layout.itemAt(0, QFormLayout.ItemRole.LabelRole)
                                if index_type_item and index_type_item.widget():
                                    index_type_item.widget().setText(
                                        self.translator.get_text("default_index_type", "默认索引类型:"))

                            if index_layout.rowCount() > 1:
                                metric_item = index_layout.itemAt(1, QFormLayout.ItemRole.LabelRole)
                                if metric_item and metric_item.widget():
                                    metric_item.widget().setText(
                                        self.translator.get_text("default_metric", "默认度量方式:"))

                            if index_layout.rowCount() > 2:
                                quantization_item = index_layout.itemAt(2, QFormLayout.ItemRole.LabelRole)
                                if quantization_item and quantization_item.widget():
                                    quantization_item.widget().setText(
                                        self.translator.get_text("default_quantization", "默认量化方法:"))

                            if index_layout.rowCount() > 3:
                                directory_item = index_layout.itemAt(3, QFormLayout.ItemRole.LabelRole)
                                if directory_item and directory_item.widget():
                                    directory_item.widget().setText(
                                        self.translator.get_text("index_directory", "索引目录:"))
                        except Exception as e:
                            logger.warning(f"更新索引设置标签时出错: {e}")

            # 更新按钮
            if hasattr(self, 'browse_cache_button') and self.browse_cache_button:
                self.browse_cache_button.setText(self.translator.get_text("browse", "浏览"))
            if hasattr(self, 'browse_index_button') and self.browse_index_button:
                self.browse_index_button.setText(self.translator.get_text("browse", "浏览"))
            if hasattr(self, 'reset_button') and self.reset_button:
                self.reset_button.setText(self.translator.get_text("reset", "重置"))
            if hasattr(self, 'apply_button') and self.apply_button:
                self.apply_button.setText(self.translator.get_text("apply", "应用"))

            # 更新标签页标题
            tab_widget = self.findChild(QTabWidget)
            if tab_widget:
                if tab_widget.count() > 0:
                    tab_widget.setTabText(0, self.translator.get_text("general", "常规"))
                if tab_widget.count() > 1:
                    tab_widget.setTabText(1, self.translator.get_text("vectorization", "向量化"))
                if tab_widget.count() > 2:
                    tab_widget.setTabText(2, self.translator.get_text("indexing", "索引"))
                if tab_widget.count() > 3:
                    tab_widget.setTabText(3, self.translator.get_text("local_models", "本地模型"))

            logger.info("SettingsWidget语言更新完成")

        except Exception as e:
            logger.error(f"SettingsWidget语言更新时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
