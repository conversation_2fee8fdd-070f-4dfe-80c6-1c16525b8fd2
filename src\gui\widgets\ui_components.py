from typing import Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QProgressBar,
    QMessageBox
)

def create_text_tab(parent: QWidget) -> QWidget:
    """创建文本向量化标签页"""
    tab = QWidget(parent)
    layout = QVBoxLayout(tab)
    # 实现文本标签页UI
    return tab

def create_file_tab(parent: QWidget) -> QWidget:
    """创建文件向量化标签页"""
    tab = QWidget(parent)
    layout = QVBoxLayout(tab)
    # 实现文件标签页UI
    return tab

def setup_progress_bars(parent: QWidget):
    """设置进度条"""
    parent.progress_bar = QProgressBar()
    parent.file_progress_bar = QProgressBar()
    # 配置进度条属性

def show_message(
    parent: QWidget,
    title: str,
    message: str,
    type_: str = "info"
) -> None:
    """显示消息对话框"""
    try:
        if type_ == "error":
            QMessageBox.critical(parent, title, message)
        elif type_ == "warning":
            QMessageBox.warning(parent, title, message)
        else:
            QMessageBox.information(parent, title, message)
    except Exception as e:
        logging.error(f"显示消息失败: {e}")
        print(f"{title}: {message}")