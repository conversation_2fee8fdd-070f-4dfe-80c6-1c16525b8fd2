from typing import Optional
from pathlib import Path

def validate_text_input(text: str) -> Optional[str]:
    """验证文本输入"""
    if not text or not text.strip():
        return "文本不能为空"
    if len(text) > 1000000:
        return "文本长度超过限制(1MB)"
    return None

def validate_file_path(path: Path) -> Optional[str]:
    """验证文件路径"""
    if not path.exists():
        return f"文件不存在: {path}"
    if not path.is_file():
        return f"不是文件: {path}"
    if path.stat().st_size > 10000000:
        return f"文件大小超过限制(10MB): {path}"
    return None