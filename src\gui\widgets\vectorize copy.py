#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
向量化小部件
"""

import sys
import logging

logger = logging.getLogger(__name__)

# PyQt 导入处理
GUI_FRAMEWORK = None
try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QComboBox, QTextEdit, QFormLayout, QGroupBox, QTabWidget,
        QFileDialog, QProgressBar, QSpinBox, QLineEdit, QCheckBox,
        QDialog, QMessageBox
    )
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    GUI_FRAMEWORK = 'PyQt6'
except ImportError as e:
    logger.warning(f"PyQt6 导入失败: {e}")
    try:
        from PyQt5.QtWidgets import (
            QWidget, QVBoxLayout, QHBoxLayout, Q<PERSON>abel, Q<PERSON><PERSON><PERSON>utton,
            QComboBox, QTextEdit, QFormLayout, QGroupBox, QTabWidget,
            QFileDialog, QProgressBar, QSpinBox, QLineEdit, QCheckBox,
            QDialog, QMessageBox
        )
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        GUI_FRAMEWORK = 'PyQt5'
    except ImportError as e:
        logger.error(f"PyQt5 导入也失败: {e}")
        sys.exit("无法加载 GUI 框架，请确保正确安装 PyQt6 或 PyQt5")

# 验证 GUI 框架是否成功加载
if not GUI_FRAMEWORK:
    raise ImportError("未能成功加载任何 GUI 框架")

logger.info(f"使用 GUI 框架: {GUI_FRAMEWORK}")

from ..i18n import Translator

# 在文件顶部添加全局变量
_PYQT_AVAILABLE = True
try:
    from PyQt6.QtWidgets import QMessageBox, QApplication
    from PyQt6.QtCore import Qt
except ImportError:
    _PYQT_AVAILABLE = False
    import logging
    logger = logging.getLogger(__name__)
    logger.error("无法导入 PyQt6 模块，将使用备用方法显示消息")

# 然后创建一个通用的消息显示函数
def show_message(self, title, message, message_type="info"):
    """显示消息，处理 PyQt 不可用的情况"""
    try:
        if _PYQT_AVAILABLE:
            from PyQt6.QtWidgets import QMessageBox
            if message_type == "error":
                QMessageBox.critical(self, title, message)
            elif message_type == "warning":
                QMessageBox.warning(self, title, message)
            else:
                QMessageBox.information(self, title, message)
        else:
            # 备用方法：打印到控制台
            print(f"{title}: {message}")
            
            # 记录到日志
            import logging
            logger = logging.getLogger(__name__)
            if message_type == "error":
                logger.error(f"{title}: {message}")
            elif message_type == "warning":
                logger.warning(f"{title}: {message}")
            else:
                logger.info(f"{title}: {message}")
    except Exception as e:
        # 最后的备用方法
        print(f"{title}: {message}")
        print(f"显示消息时出错: {e}")

from typing import Optional
from PyQt6.QtWidgets import QWidget, QVBoxLayout
from ...config.vectorization_config import VectorizationConfig
from ...services.vectorization_service import VectorizationService
from ..state_manager import StateManager, VectorizationState
from .ui_components import (
    create_text_tab,
    create_file_tab,
    setup_progress_bars
)

class VectorizeWidget(QWidget):
    def __init__(
        self,
        translator,
        config: VectorizationConfig,
        vectorization_service: VectorizationService,
        state_manager: StateManager
    ):
        super().__init__()
        
        # 初始化属性
        self.translator = translator
        self.config = config
        self.vectorization_service = vectorization_service
        self.state_manager = state_manager
        
        # 初始化UI组件
        self.text_input = None
        self.file_list = None
        self.progress_bar = None
        self.file_progress_bar = None
        
        # 设置对象名
        self.setObjectName("vectorizeWidget")
        
        # 初始化UI
        self._init_ui()
        
        # 注册状态观察者
        self.state_manager.add_observer(self._on_state_changed)
        
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tabs = QTabWidget()
        
        # 添加文本向量化标签页
        text_tab = create_text_tab(self)
        tabs.addTab(text_tab, self.translator.get_text("text_vectorization"))
        
        # 添加文件向量化标签页
        file_tab = create_file_tab(self)
        tabs.addTab(file_tab, self.translator.get_text("file_vectorization"))
        
        layout.addWidget(tabs)
        
        # 设置进度条
        setup_progress_bars(self)
        
    def _on_state_changed(self, context: StateContext):
        """处理状态变化"""
        if context.state == VectorizationState.PROCESSING:
            self._update_progress(context.progress)
        elif context.state == VectorizationState.ERROR:
            self._show_error(context.message)
        elif context.state == VectorizationState.SUCCESS:
            self._show_success(context.message)

















































