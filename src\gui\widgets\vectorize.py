#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
向量化小部件
"""
import os, sys
import logging

# 设置PyQt6路径和DLL加载
try:
    pyqt6_bin = os.path.join(sys.prefix, 'Lib', 'site-packages', 'PyQt6', 'Qt6', 'bin')
    if os.path.exists(pyqt6_bin):
        os.environ['PATH'] = pyqt6_bin + os.pathsep + os.environ['PATH']
        print(f"✓ 已添加PyQt6路径: {pyqt6_bin}")
    else:
        print(f"WARNING - PyQt6路径不存在: {pyqt6_bin}")
except Exception as e:
    print(f"WARNING - 设置PyQt6路径时出错: {e}")
# 尝试导入 PyQt6，如果失败则使用 PyQt5
try:
    from PyQt6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QComboBox, QTextEdit, QFormLayout, QGroupBox, QTabWidget,
        QFileDialog, QProgressBar, QSpinBox, QLineEdit, QCheckBox,
        QDialog, QMessageBox
    )
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    QT_VERSION = 6
except ImportError:
    from PyQt5.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QComboBox, QTextEdit, QFormLayout, QGroupBox, QTabWidget,
        QFileDialog, QProgressBar, QSpinBox, QLineEdit, QCheckBox,
        QDialog, QMessageBox
    )
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    QT_VERSION = 5

try:
    from matplotlib.backends.backend_qt6agg import FigureCanvasQTAgg as FigureCanvas
except ImportError:
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas

from ..i18n import Translator

# 尝试导入qt_material，如果失败则跳过
try:
    import qt_material
    QT_MATERIAL_AVAILABLE = True
except ImportError:
    QT_MATERIAL_AVAILABLE = False
    print("WARNING - 无法导入 qt_material 库，将使用默认样式")

# 在文件顶部添加全局变量
_PYQT_AVAILABLE = True
_PYQT_WIDGETS = {}
_PYQT_CORE = {}

try:
    from PyQt6.QtWidgets import QMessageBox, QApplication, QProgressDialog, QDialog, QVBoxLayout, QLabel, QTextEdit, QPushButton, QTabWidget
    from PyQt6.QtCore import Qt

    # 缓存常用的PyQt6组件
    _PYQT_WIDGETS.update({
        'QMessageBox': QMessageBox,
        'QApplication': QApplication,
        'QProgressDialog': QProgressDialog,
        'QDialog': QDialog,
        'QVBoxLayout': QVBoxLayout,
        'QLabel': QLabel,
        'QTextEdit': QTextEdit,
        'QPushButton': QPushButton,
        'QTabWidget': QTabWidget
    })
    _PYQT_CORE['Qt'] = Qt

except ImportError as e:
    _PYQT_AVAILABLE = False
    import logging
    logger = logging.getLogger(__name__)
    logger.error(f"无法导入 PyQt6 模块，将使用备用方法显示消息: {e}")

    # 创建空的占位符
    _PYQT_WIDGETS = {}
    _PYQT_CORE = {}

# 然后创建一个通用的消息显示函数
def show_message(self, title, message, message_type="info"):
    """显示消息，处理 PyQt 不可用的情况"""
    try:
        if _PYQT_AVAILABLE and 'QMessageBox' in _PYQT_WIDGETS:
            QMessageBox = _PYQT_WIDGETS['QMessageBox']
            if message_type == "error":
                QMessageBox.critical(self, title, message)
            elif message_type == "warning":
                QMessageBox.warning(self, title, message)
            else:
                QMessageBox.information(self, title, message)
        else:
            # 备用方法：打印到控制台
            print(f"{title}: {message}")

            # 记录到日志
            import logging
            logger = logging.getLogger(__name__)
            if message_type == "error":
                logger.error(f"{title}: {message}")
            elif message_type == "warning":
                logger.warning(f"{title}: {message}")
            else:
                logger.info(f"{title}: {message}")
    except Exception as e:
        # 最后的备用方法
        print(f"{title}: {message}")
        print(f"显示消息时出错: {e}")

class VectorizeWidget(QWidget):
    """向量化小部件"""

    def __init__(self, translator: Translator, parent=None):
        """
        初始化向量化小部件

        Args:
            translator: 翻译器实例
            parent: 父组件（可选）
        """
        super().__init__(parent)

        self.translator = translator
        self.translator.add_observer(self)

        # 初始化组件引用（将在_init_ui中设置）
        self.language_combo = None
        self.model_combo = None
        self.logger = logging.getLogger(__name__)

        # 设置对象名，用于样式表
        self.setObjectName("vectorizeWidget")

        # 初始化UI
        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        import logging
        logger = logging.getLogger(__name__)
        logger.debug("开始初始化向量化小部件UI")
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        
        # 创建标题标签
        title_label = QLabel(self.translator.get_text("vectorize_data", "向量化数据"))
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        main_layout.addWidget(title_label)
        
        # 创建标签页控件
        tab_widget = QTabWidget()
        
        logger.debug("创建文本向量化标签页")
        # 创建"文本向量化"标签页
        text_tab = QWidget()
        text_layout = QVBoxLayout(text_tab)
        
        # 创建表单布局
        form_layout = QFormLayout()
        
        # 索引选择
        index_layout = QHBoxLayout()
        self.index_combo = QComboBox()
        self.index_combo.addItem(self.translator.get_text("create_new_index", "创建新索引"))
        index_layout.addWidget(self.index_combo)
        
        self.refresh_indices_btn = QPushButton(self.translator.get_text("refresh", "刷新"))
        self.refresh_indices_btn.clicked.connect(self._load_available_indices)
        index_layout.addWidget(self.refresh_indices_btn)
        
        # 新索引名称
        self.new_index_name_edit = QLineEdit()
        self.new_index_name_edit.setPlaceholderText(self.translator.get_text("enter_index_name", "输入索引名称"))
        
        # 模型选择
        self.text_model_combo = QComboBox()
        self._load_model_options(self.text_model_combo)

        # 设置组件引用（为了向后兼容）
        self.model_combo = self.text_model_combo

        # 语言/文件类型选择
        self.language_combo = QComboBox()
        self.language_combo.addItems([
            "auto", "text", "markdown", "json", "csv", "excel", "pdf", "docx", "html", "xml"
        ])

        # 添加字段到表单
        form_layout.addRow(self.translator.get_text("model", "模型:"), self.text_model_combo)
        form_layout.addRow(self.translator.get_text("file_type", "文件类型:"), self.language_combo)
        form_layout.addRow(self.translator.get_text("index", "索引:"), index_layout)
        
        # 文本向量化参数设置
        text_params_group = QGroupBox(self.translator.get_text("advanced_params", "高级参数"))
        text_params_layout = QFormLayout(text_params_group)
        
        # 批处理大小
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 1000)
        self.batch_size_spin.setValue(32)
        text_params_layout.addRow(self.translator.get_text("batch_size", "批处理大小:"), self.batch_size_spin)
        
        # 向量维度
        self.vector_dim_spin = QSpinBox()
        self.vector_dim_spin.setRange(64, 1024)
        self.vector_dim_spin.setValue(384)
        self.vector_dim_spin.setSingleStep(64)
        text_params_layout.addRow(self.translator.get_text("vector_dimension", "向量维度:"), self.vector_dim_spin)
        
        # 标准化向量
        self.normalize_check = QCheckBox()
        self.normalize_check.setChecked(True)
        text_params_layout.addRow(self.translator.get_text("normalize_vectors", "标准化向量:"), self.normalize_check)
        
        # 设备选择
        self.device_combo = QComboBox()
        self.device_combo.addItems(["cpu", "cuda"])
        text_params_layout.addRow(self.translator.get_text("device", "计算设备:"), self.device_combo)
        
        # 添加高级参数组
        text_layout.addWidget(text_params_group)
        
        # 其他表单字段...
        
        logger.debug("创建文件向量化标签页")
        # 创建"文件向量化"标签页
        file_tab = QWidget()
        file_layout = QVBoxLayout(file_tab)
        
        # 创建文件向量化表单布局
        file_form_layout = QFormLayout()

        # 模型选择
        self.file_model_combo = QComboBox()
        self._load_model_options(self.file_model_combo)

        # 索引选择
        file_index_layout = QHBoxLayout()
        self.file_index_combo = QComboBox()
        self.file_index_combo.addItem(self.translator.get_text("create_new_index", "创建新索引"))
        file_index_layout.addWidget(self.file_index_combo)

        self.file_refresh_indices_btn = QPushButton(self.translator.get_text("refresh", "刷新"))
        self.file_refresh_indices_btn.clicked.connect(self._load_available_indices)
        file_index_layout.addWidget(self.file_refresh_indices_btn)

        # 文件类型
        self.file_type_combo = QComboBox()
        self.file_type_combo.addItems([
            "auto", "text", "markdown", "json", "csv", "excel", "pdf", "docx", "html", "xml"
        ])

        # 向量维度
        self.file_vector_dim_spin = QSpinBox()
        self.file_vector_dim_spin.setRange(64, 1024)
        self.file_vector_dim_spin.setValue(384)
        self.file_vector_dim_spin.setSingleStep(64)

        # 计算设备
        self.file_device_combo = QComboBox()
        self.file_device_combo.addItems(["cpu", "cuda"])

        # 批处理大小
        self.file_batch_size_spin = QSpinBox()
        self.file_batch_size_spin.setRange(1, 1000)
        self.file_batch_size_spin.setValue(32)

        # 添加字段到表单
        file_form_layout.addRow(self.translator.get_text("model", "模型:"), self.file_model_combo)
        file_form_layout.addRow(self.translator.get_text("index", "索引:"), file_index_layout)
        file_form_layout.addRow(self.translator.get_text("file_type", "文件类型:"), self.file_type_combo)
        file_form_layout.addRow(self.translator.get_text("vector_dimension", "向量维度:"), self.file_vector_dim_spin)
        file_form_layout.addRow(self.translator.get_text("device", "计算设备:"), self.file_device_combo)
        file_form_layout.addRow(self.translator.get_text("batch_size", "批处理大小:"), self.file_batch_size_spin)

        file_layout.addLayout(file_form_layout)

        # 添加文本输入区域到文本标签页
        text_input_group = QGroupBox(self.translator.get_text("input_text", "输入文本"))
        text_input_layout = QVBoxLayout(text_input_group)

        self.text_input = QTextEdit()
        self.text_input.setPlaceholderText(self.translator.get_text("enter_text_here", "在此输入文本..."))

        text_input_layout.addWidget(self.text_input)
        text_layout.addWidget(text_input_group)

        # 添加按钮到文本标签页
        buttons_layout = QHBoxLayout()

        self.load_text_button = QPushButton(self.translator.get_text("load_file", "加载文件"))
        self.load_text_button.setObjectName("secondaryButton")
        self.load_text_button.setCursor(Qt.CursorShape.PointingHandCursor)

        self.vectorize_text_button = QPushButton(self.translator.get_text("vectorize", "向量化"))
        self.vectorize_text_button.setObjectName("primaryButton")
        self.vectorize_text_button.setCursor(Qt.CursorShape.PointingHandCursor)

        buttons_layout.addWidget(self.load_text_button)
        buttons_layout.addWidget(self.vectorize_text_button)

        text_layout.addLayout(buttons_layout)

        # 添加进度条到文本标签页
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p%")

        text_layout.addWidget(self.progress_bar)

        # 添加表单到文本标签页布局
        text_layout.addLayout(form_layout)

        # 添加文件选择区域
        file_input_group = QGroupBox(self.translator.get_text("select_files", "选择文件"))
        file_input_layout = QVBoxLayout(file_input_group)

        file_buttons_layout = QHBoxLayout()

        self.select_files_button = QPushButton(self.translator.get_text("select_files", "选择文件"))
        self.select_files_button.setObjectName("secondaryButton")
        self.select_files_button.setCursor(Qt.CursorShape.PointingHandCursor)

        self.select_folder_button = QPushButton(self.translator.get_text("select_folder", "选择文件夹"))
        self.select_folder_button.setObjectName("secondaryButton")
        self.select_folder_button.setCursor(Qt.CursorShape.PointingHandCursor)

        file_buttons_layout.addWidget(self.select_files_button)
        file_buttons_layout.addWidget(self.select_folder_button)

        file_input_layout.addLayout(file_buttons_layout)

        self.file_list = QTextEdit()
        self.file_list.setReadOnly(True)
        self.file_list.setPlaceholderText(self.translator.get_text("selected_files_shown_here", "选择的文件将显示在这里..."))

        file_input_layout.addWidget(self.file_list)

        file_layout.addWidget(file_input_group)

        # 添加按钮
        file_process_layout = QHBoxLayout()

        self.clear_files_button = QPushButton(self.translator.get_text("clear", "清除"))
        self.clear_files_button.setObjectName("secondaryButton")
        self.clear_files_button.setCursor(Qt.CursorShape.PointingHandCursor)

        self.vectorize_files_button = QPushButton(self.translator.get_text("vectorize", "向量化"))
        self.vectorize_files_button.setObjectName("primaryButton")
        self.vectorize_files_button.setCursor(Qt.CursorShape.PointingHandCursor)

        file_process_layout.addWidget(self.clear_files_button)
        file_process_layout.addWidget(self.vectorize_files_button)

        file_layout.addLayout(file_process_layout)

        # 添加进度条
        self.file_progress_bar = QProgressBar()
        self.file_progress_bar.setRange(0, 100)
        self.file_progress_bar.setValue(0)
        self.file_progress_bar.setTextVisible(True)
        self.file_progress_bar.setFormat("%p%")

        file_layout.addWidget(self.file_progress_bar)

        # 添加标签页到标签页控件
        tab_widget.addTab(text_tab, self.translator.get_text("text_vectorization", "文本向量化"))
        tab_widget.addTab(file_tab, self.translator.get_text("file_vectorization", "文件向量化"))

        main_layout.addWidget(tab_widget)

        # 连接信号
        self._connect_signals()

        # 加载可用索引
        self._load_available_indices()

        logger.debug("UI初始化完成")

    def _load_model_options(self, combo_box):
        """加载模型选项到下拉框"""
        try:
            # 清空现有选项
            combo_box.clear()
            
            # 添加默认模型
            default_models = [
                "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
                "sentence-transformers/all-MiniLM-L6-v2",
                "sentence-transformers/all-mpnet-base-v2",
                "ollama_deepseek-r1_32b",
                "ollama_llama3_8b"
            ]
            
            # 添加默认模型组
            combo_box.addItem("--- 默认模型 ---")
            combo_box.addItems(default_models)
                   
            # 加载Ollama模型（如果配置了）
            try:
                # 获取主窗口配置
                main_window = self.window()
                if hasattr(main_window, 'config') and main_window.config:
                    # 检查是否有Ollama配置
                    local_models = main_window.config.get('local_models', {})
                    ollama_config = local_models.get('ollama', {})
                    
                    if ollama_config.get('enabled', False):
                        # 获取Ollama模型列表
                        ollama_models = ollama_config.get('models', [])
                        if ollama_models:
                            # 添加分隔符
                            combo_box.insertSeparator(combo_box.count())
                            combo_box.addItem("--- Ollama模型 ---")
                            
                            # 添加支持向量化的Ollama模型
                            for model in ollama_models:
                                model_name = model.get('name')
                                # 检查模型是否支持向量化
                                if model.get('supports_embedding', True):  # 默认假设支持
                                    display_name = f"ollama_{model_name}"
                                    combo_box.addItem(display_name)
                                    # 存储模型信息作为用户数据
                                    model_data = {
                                        'type': 'ollama',
                                        'name': model_name,
                                        'parameters': model,
                                        'supports_embedding': True
                                    }
                                    combo_box.setItemData(combo_box.count() - 1, model_data)
            except Exception as e:
                self.logger.error(f"加载Ollama模型时出错: {e}")


            # 加载本地模型
            try:
                from ...utils.local_model_manager import get_local_model_manager
                model_manager = get_local_model_manager()
                
                # 获取支持向量化的本地模型
                local_models = model_manager.get_available_models(supports_embedding=True)
                
                if local_models:
                    # 添加分隔符
                    combo_box.insertSeparator(combo_box.count())
                    combo_box.addItem("--- 本地模型 ---")
                    
                    # 添加本地模型
                    for model in local_models:
                        display_name = f"{model.type}:{model.model_id}"
                        combo_box.addItem(display_name)
                        # 存储模型对象作为用户数据
                        combo_box.setItemData(combo_box.count() - 1, model)
                    
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.info(f"加载了 {len(local_models)} 个本地向量化模型")
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"加载本地模型时出错: {e}")
                import traceback
                logger.error(traceback.format_exc())
            
            # 设置默认选中项
            combo_box.setCurrentIndex(1)  # 选择第一个实际模型
            
            # 连接信号
            combo_box.currentIndexChanged.connect(self._on_model_changed)
            
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"加载模型选项时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _show_warning(self, message: str):
        """显示警告消息"""
        try:
            if _PYQT_AVAILABLE and 'QMessageBox' in _PYQT_WIDGETS:
                QMessageBox = _PYQT_WIDGETS['QMessageBox']
                QMessageBox.warning(self, "警告", message)
            else:
                self.logger.warning(f"警告: {message}")
                print(f"警告: {message}")
        except Exception as e:
            self.logger.warning(f"显示警告消息失败: {e}, 消息: {message}")
            print(f"警告: {message}")

    def _show_error_message(self, message: str):
        """显示错误消息"""
        try:
            if _PYQT_AVAILABLE and 'QMessageBox' in _PYQT_WIDGETS:
                QMessageBox = _PYQT_WIDGETS['QMessageBox']
                QMessageBox.critical(self, "错误", message)
            else:
                self.logger.error(f"错误: {message}")
                print(f"错误: {message}")
        except Exception as e:
            self.logger.error(f"显示错误消息失败: {e}, 消息: {message}")
            print(f"错误: {message}")

    def _show_info_message(self, message: str):
        """显示信息消息"""
        try:
            if _PYQT_AVAILABLE and 'QMessageBox' in _PYQT_WIDGETS:
                QMessageBox = _PYQT_WIDGETS['QMessageBox']
                QMessageBox.information(self, "信息", message)
            else:
                self.logger.info(f"信息: {message}")
                print(f"信息: {message}")
        except Exception as e:
            self.logger.info(f"显示信息消息失败: {e}, 消息: {message}")
            print(f"信息: {message}")

    def _split_text(self, text: str, file_type: str = "txt") -> list:
        """
        分割文本为块

        Args:
            text: 要分割的文本
            file_type: 文件类型

        Returns:
            list: 文本块列表
        """
        try:
            # 根据文件类型选择不同的分割策略
            if file_type.lower() in ['md', 'markdown']:
                # Markdown文件按标题分割
                chunks = self._split_markdown(text)
            elif file_type.lower() in ['txt', 'text']:
                # 普通文本按段落分割
                chunks = self._split_by_paragraphs(text)
            else:
                # 默认按段落分割
                chunks = self._split_by_paragraphs(text)

            # 过滤空块
            chunks = [chunk.strip() for chunk in chunks if chunk.strip()]

            # 如果没有分割出任何块，返回原文本
            if not chunks:
                chunks = [text]

            self.logger.info(f"文本分割完成，共 {len(chunks)} 个块")
            return chunks

        except Exception as e:
            self.logger.error(f"分割文本时出错: {e}")
            # 返回原文本作为单个块
            return [text]

    def _split_markdown(self, text: str) -> list:
        """按Markdown标题分割文本"""
        try:
            import re
            # 按标题分割（# ## ### 等）
            chunks = re.split(r'\n(?=#{1,6}\s)', text)
            return chunks
        except Exception as e:
            self.logger.warning(f"Markdown分割失败，使用段落分割: {e}")
            return self._split_by_paragraphs(text)

    def _split_by_paragraphs(self, text: str, max_chunk_size: int = 1000) -> list:
        """按段落分割文本"""
        try:
            # 按双换行符分割段落
            paragraphs = text.split('\n\n')
            chunks = []
            current_chunk = ""

            for paragraph in paragraphs:
                paragraph = paragraph.strip()
                if not paragraph:
                    continue

                # 如果当前块加上新段落不超过最大长度，则合并
                if len(current_chunk) + len(paragraph) + 2 <= max_chunk_size:
                    if current_chunk:
                        current_chunk += "\n\n" + paragraph
                    else:
                        current_chunk = paragraph
                else:
                    # 保存当前块
                    if current_chunk:
                        chunks.append(current_chunk)
                    current_chunk = paragraph

            # 添加最后一个块
            if current_chunk:
                chunks.append(current_chunk)

            return chunks

        except Exception as e:
            self.logger.error(f"段落分割失败: {e}")
            # 简单按长度分割
            chunk_size = max_chunk_size
            return [text[i:i+chunk_size] for i in range(0, len(text), chunk_size)]

    def _load_available_indices(self):
        """加载可用的索引"""
        try:
            from pathlib import Path
            import logging
            logger = logging.getLogger(__name__)
            
            # 清空现有选项
            self.index_combo.clear()
            if hasattr(self, 'file_index_combo'):
                self.file_index_combo.clear()  # 添加文件向量化的索引下拉框

            # 添加"创建新索引"选项
            self.index_combo.addItem(self.translator.get_text("create_new_index", "创建新索引"))
            if hasattr(self, 'file_index_combo'):
                self.file_index_combo.addItem(self.translator.get_text("create_new_index", "创建新索引"))
            
            # 获取索引目录
            indices_dir = Path("data/indices")
            if not indices_dir.exists():
                indices_dir.mkdir(parents=True, exist_ok=True)
                return
                
            # 获取所有索引文件
            index_files = list(indices_dir.glob("*.idx"))
            
            if index_files:
                # 添加分隔符
                self.index_combo.insertSeparator(self.index_combo.count())
                self.index_combo.addItem("--- 可用索引 ---")

                if hasattr(self, 'file_index_combo'):
                    self.file_index_combo.insertSeparator(self.file_index_combo.count())
                    self.file_index_combo.addItem("--- 可用索引 ---")
                
                # 按修改时间排序，最新的在前
                index_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                
                for index_file in index_files:
                    # 检查是否有对应的元数据文件
                    meta_file = index_file.with_suffix('.meta')
                    if meta_file.exists():
                        # 显示索引名称和修改时间
                        import time
                        mod_time = time.strftime("%Y-%m-%d %H:%M", time.localtime(index_file.stat().st_mtime))
                        display_name = f"{index_file.stem} ({mod_time})"
                        
                        # 添加到文本向量化索引下拉框
                        self.index_combo.addItem(display_name)
                        self.index_combo.setItemData(self.index_combo.count() - 1, str(index_file))

                        # 添加到文件向量化索引下拉框
                        if hasattr(self, 'file_index_combo'):
                            self.file_index_combo.addItem(display_name)
                            self.file_index_combo.setItemData(self.file_index_combo.count() - 1, str(index_file))
                
                # 设置默认选中项
                self.index_combo.setCurrentIndex(0)  # 默认选择"创建新索引"
                if hasattr(self, 'file_index_combo'):
                    self.file_index_combo.setCurrentIndex(0)
                
                logger.info(f"加载了 {len(index_files)} 个可用索引")
                
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"加载可用索引时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _connect_signals(self):
        """连接信号"""
        import logging
        logger = logging.getLogger(__name__)
        
        try:
            # 连接按钮信号
            if hasattr(self, 'vectorize_text_button'):
                self.vectorize_text_button.clicked.connect(self._on_vectorize_text)
            
            if hasattr(self, 'vectorize_files_button'):
                self.vectorize_files_button.clicked.connect(self._on_vectorize_files)
            
            if hasattr(self, 'select_files_button'):
                self.select_files_button.clicked.connect(self._on_select_files)

            if hasattr(self, 'select_folder_button'):
                self.select_folder_button.clicked.connect(self._on_select_folder)

            if hasattr(self, 'clear_files_button'):
                self.clear_files_button.clicked.connect(self._on_clear_files)
            
            if hasattr(self, 'load_text_button'):
                self.load_text_button.clicked.connect(self._on_load_text)
            
            # 连接索引选择信号
            if hasattr(self, 'index_combo'):
                self.index_combo.currentIndexChanged.connect(self._on_index_selected)
            
            if hasattr(self, 'file_index_combo'):
                self.file_index_combo.currentIndexChanged.connect(self._on_file_index_selected)
            
            # 连接刷新索引按钮
            if hasattr(self, 'refresh_indices_btn'):
                self.refresh_indices_btn.clicked.connect(self._load_available_indices)

            if hasattr(self, 'text_refresh_indices_btn'):
                self.text_refresh_indices_btn.clicked.connect(self._load_available_indices)

            if hasattr(self, 'file_refresh_indices_btn'):
                self.file_refresh_indices_btn.clicked.connect(self._load_available_indices)
            
            # 连接参数同步信号
            if hasattr(self, 'batch_size_spin') and hasattr(self, 'file_batch_size_spin'):
                self.batch_size_spin.valueChanged.connect(lambda v: self.file_batch_size_spin.setValue(v))
                self.file_batch_size_spin.valueChanged.connect(lambda v: self.batch_size_spin.setValue(v))
            
            # 不要连接这两个信号，因为它们可能导致问题
            # if hasattr(self, 'vector_dim_spin') and hasattr(self, 'file_vector_dim_spin'):
            #     self.vector_dim_spin.valueChanged.connect(lambda v: self.file_vector_dim_spin.setValue(v))
            #     self.file_vector_dim_spin.valueChanged.connect(lambda v: self.vector_dim_spin.setValue(v))
            
            logger.debug("信号连接完成")
        except Exception as e:
            logger.error(f"连接信号时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _on_load_text(self):
        """加载文本文件按钮点击处理"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            self.translator.get_text("load_text_file", "加载文本文件"),
            "",
            "All Files (*);;Text Files (*.txt);;Excel Files (*.xlsx *.xls);;Markdown Files (*.md *.markdown);;CSV Files (*.csv);;JSON Files (*.json)"
        )

        if file_path:
            # 导入日志模块
            import logging
            logger = logging.getLogger(__name__)

            # 使用文档加载器进行完整性检查
            try:
                from ...utils.document_loader import create_document_loader

                # 创建进度回调
                def progress_callback(current, total, message):
                    logger.info(f"加载进度: {current}/{total} - {message}")

                loader = create_document_loader(progress_callback)
                documents, doc_infos = loader.load_documents([file_path], validate_integrity=True)

                if file_path in documents:
                    text = documents[file_path]
                    self.text_input.setText(text)

                    # 显示加载信息
                    doc_info = doc_infos[0]
                    logger.info(f"=== 文档加载完成 ===")
                    logger.info(f"文件路径: {file_path}")
                    logger.info(f"文件大小: {doc_info.size} 字节")
                    logger.info(f"内容长度: {doc_info.content_length} 字符")
                    logger.info(f"检测编码: {doc_info.encoding}")
                    logger.info(f"加载时间: {doc_info.load_time:.3f} 秒")
                    logger.info(f"内容哈希: {doc_info.content_hash}")
                    logger.info(f"加载状态: {doc_info.status.value}")

                    # 根据文件类型设置语言选择
                    from pathlib import Path
                    path = Path(file_path)
                    file_ext = path.suffix.lower()

                    if hasattr(self, 'language_combo') and self.language_combo:
                        if file_ext in ['.xlsx', '.xls', '.xlsm']:
                            self.language_combo.setCurrentText("excel")
                        elif file_ext in ['.md', '.markdown']:
                            self.language_combo.setCurrentText("markdown")
                            # 对于Markdown文件，自动启用分块
                            if doc_info.content_length > 2000:
                                # 注意：chunk_size_spin和overlap_spin控件不存在，使用默认值
                                # 这些值将在向量化过程中使用默认的分块设置
                                self.logger.info(f"检测到大型Markdown文件({doc_info.content_length}字符)，将使用默认分块设置")
                        elif file_ext == '.csv':
                            self.language_combo.setCurrentText("csv")
                        elif file_ext == '.json':
                            self.language_combo.setCurrentText("json")
                        else:
                            self.language_combo.setCurrentText("text")
                    else:
                        logger.warning("language_combo 组件不存在，无法设置语言类型")

                    return

                else:
                    # 加载失败，使用原有的加载方法作为备用
                    doc_info = doc_infos[0] if doc_infos else None
                    error_msg = doc_info.error_message if doc_info else "未知错误"
                    logger.warning(f"文档加载器加载失败: {error_msg}，尝试使用备用方法")

            except Exception as e:

                logger.warning(f"文档加载器出错: {e}，使用备用加载方法")

            # 备用加载方法（原有逻辑）
            # 检查文件扩展名
            from pathlib import Path
            path = Path(file_path)
            file_ext = path.suffix.lower()
            text = None

            # 处理Excel文件
            if file_ext in ['.xlsx', '.xls', '.xlsm']:
                try:
                    # 设置语言类型为excel
                    if hasattr(self, 'language_combo') and self.language_combo:
                        self.language_combo.setCurrentText("excel")
                    else:
                        logger.warning("language_combo 组件不存在，无法设置语言类型")

                    # 导入pandas
                    import pandas as pd
                    # 读取Excel文件
                    df = pd.read_excel(file_path)
                    # 转换为文本格式
                    text = df.to_string(index=False)
                    logger.info(f"成功使用pandas读取Excel文件: {file_path}")
                except Exception as e:
                    logger.error(f"使用pandas读取Excel文件失败: {file_path}, {e}")
                    # 如果pandas读取失败，尝试其他方法

            # 处理Markdown文件
            elif file_ext in ['.md', '.markdown']:
                try:
                    # 设置语言类型为markdown
                    if hasattr(self, 'language_combo') and self.language_combo:
                        self.language_combo.setCurrentText("markdown")
                    else:
                        logger.warning("language_combo 组件不存在，无法设置语言类型")

                    # 尝试不同的编码
                    encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']
                    for encoding in encodings:
                        try:
                            with open(file_path, 'r', encoding=encoding) as f:
                                raw_content = f.read()

                            # 如果有frontmatter库，尝试解析frontmatter
                            try:
                                import frontmatter
                                post = frontmatter.loads(raw_content)
                                # 获取内容部分
                                text = post.content
                            except ImportError:
                                # 如果没有frontmatter库，直接使用原始内容
                                text = raw_content

                            logger.info(f"成功使用 {encoding} 编码读取Markdown文件: {file_path}")
                            break
                        except UnicodeDecodeError:
                            logger.warning(f"使用 {encoding} 编码读取Markdown文件失败: {file_path}")
                except Exception as e:
                    logger.error(f"读取Markdown文件失败: {file_path}, {e}")

            # 处理CSV文件
            elif file_ext == '.csv':
                try:
                    # 设置语言类型为csv
                    if hasattr(self, 'language_combo') and self.language_combo:
                        self.language_combo.setCurrentText("csv")
                    else:
                        logger.warning("language_combo 组件不存在，无法设置语言类型")

                    # 导入pandas
                    import pandas as pd
                    # 尝试不同的分隔符
                    for sep in [',', ';', '\t']:
                        try:
                            df = pd.read_csv(file_path, sep=sep)
                            # 转换为文本格式
                            text = df.to_string(index=False)
                            logger.info(f"成功使用pandas读取CSV文件(分隔符: {sep}): {file_path}")
                            break
                        except Exception:
                            continue
                except Exception as e:
                    logger.error(f"使用pandas读取CSV文件失败: {file_path}, {e}")

            # 处理JSON文件
            elif file_ext == '.json':
                try:
                    # 设置语言类型为json
                    if hasattr(self, 'language_combo') and self.language_combo:
                        self.language_combo.setCurrentText("json")
                    else:
                        logger.warning("language_combo 组件不存在，无法设置语言类型")

                    # 导入json
                    import json
                    # 尝试不同的编码
                    encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']
                    for encoding in encodings:
                        try:
                            with open(file_path, 'r', encoding=encoding) as f:
                                # 加载JSON
                                data = json.load(f)
                                # 格式化输出
                                text = json.dumps(data, indent=2, ensure_ascii=False)
                            logger.info(f"成功使用 {encoding} 编码读取JSON文件: {file_path}")
                            break
                        except UnicodeDecodeError:
                            logger.warning(f"使用 {encoding} 编码读取JSON文件失败: {file_path}")
                except Exception as e:
                    logger.error(f"读取JSON文件失败: {file_path}, {e}")

            # 在 _on_load_text 方法中，将直接访问 language_combo 的代码修改为先检查是否存在
            # 找到类似这样的代码：self.language_combo.setCurrentText("text")
            # 修改为：

            # 检查 language_combo 是否存在
            if hasattr(self, 'language_combo'):
                self.language_combo.setCurrentText("text")
            else:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning("language_combo 组件不存在，无法设置语言类型")

            # 处理其他文本文件
            if text is None:
                # 设置语言类型为text
                if hasattr(self, 'language_combo') and self.language_combo:
                    self.language_combo.setCurrentText("text")
                else:
                    logger.warning("language_combo 组件不存在，无法设置语言类型")

                # 尝试不同的编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']

                for encoding in encodings:
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            text = f.read()
                        logger.info(f"成功使用 {encoding} 编码读取文件: {file_path}")
                        break
                    except UnicodeDecodeError:
                        logger.warning(f"使用 {encoding} 编码读取文件失败: {file_path}")

                # 如果所有编码都失败，尝试二进制模式读取
                if text is None:
                    try:
                        with open(file_path, 'rb') as f:
                            binary_data = f.read()
                            # 尝试使用 latin-1 编码（可以处理任何字节序列）
                            text = binary_data.decode('latin-1')
                        logger.info(f"使用二进制模式读取文件: {file_path}")
                    except Exception as e:
                        logger.error(f"读取文件失败: {file_path}, {e}")
                        if _PYQT_AVAILABLE and 'QMessageBox' in _PYQT_WIDGETS:
                            QMessageBox = _PYQT_WIDGETS['QMessageBox']
                            QMessageBox.critical(
                                self,
                                "加载文件失败",
                                f"无法读取文件: {file_path}\n错误: {str(e)}"
                            )
                        else:
                            print(f"加载文件失败: {file_path}, 错误: {str(e)}")
                        return

            # 设置文本
            if text is not None:
                self.text_input.setText(text)
            else:
                if _PYQT_AVAILABLE and 'QMessageBox' in _PYQT_WIDGETS:
                    QMessageBox = _PYQT_WIDGETS['QMessageBox']
                    QMessageBox.critical(
                        self,
                        "加载文件失败",
                        f"无法读取文件: {file_path}\n尝试了多种编码但都失败了。"
                    )
                else:
                    print(f"加载文件失败: {file_path}, 尝试了多种编码但都失败了。")

    def _on_vectorize_text(self):
        """向量化文本按钮点击处理"""
        # 获取文本内容
        text = self.text_input.toPlainText().strip()
        if not text:
            return

        # 获取选项
        # 检查 model_combo 是否存在
        if hasattr(self, 'model_combo'):
            model_name = self.model_combo.currentText()
                # 获取模型数据
            model_data = self.model_combo.itemData(self.model_combo.currentIndex())    
        else:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning("model_combo 组件不存在，使用默认模型")
            model_name = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"  # 使用默认模型
            model_data = None
        
        # 检查 language_combo 是否存在
        if hasattr(self, 'language_combo'):
            file_type = self.language_combo.currentText()
        else:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning("language_combo 组件不存在，使用默认文件类型")
            file_type = "text"  # 使用默认文件类型
        
        # 检查 batch_size_spin 是否存在
        if hasattr(self, 'batch_size_spin'):
            batch_size = self.batch_size_spin.value()
        else:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning("batch_size_spin 组件不存在，使用默认批处理大小")
            batch_size = 32  # 使用默认批处理大小
        
        # 检查 vector_dim_spin 是否存在
        if hasattr(self, 'vector_dim_spin'):
            vector_dimension = self.vector_dim_spin.value()
        else:
            self.logger.warning("vector_dim_spin 组件不存在，使用默认向量维度")
            vector_dimension = 384

        # 获取用户选择的索引
        selected_index_path = None
        if hasattr(self, 'index_combo'):
            current_index = self.index_combo.currentIndex()
            if current_index > 0:  # 0是"创建新索引"选项
                index_data = self.index_combo.itemData(current_index)
                if index_data:
                    selected_index_path = Path(index_data)
                    self.logger.info(f"用户选择的索引: {selected_index_path}")
                else:
                    self.logger.info("用户选择创建新索引")
            else:
                self.logger.info("用户选择创建新索引")
        else:
            self.logger.warning("index_combo 组件不存在，将创建新索引")

        # 检查本地模型
        if hasattr(self, 'model_combo') and hasattr(self.model_combo, 'itemData'):
            local_model = self.model_combo.itemData(self.model_combo.currentIndex())
            if local_model:
                # 使用本地模型
                model_name = f"local:{local_model.name}"
        else:
            local_model = None

            # 处理本地模型
        use_local_model = False
        local_model_config = None
        
        if model_data:
            if isinstance(model_data, dict) and model_data.get('type') == 'ollama':
                # Ollama模型
                use_local_model = True
                local_model_config = {
                    'type': 'ollama',
                    'model_id': model_data.get('name'),
                    'parameters': model_data.get('parameters', {})
                }
                self.logger.info(f"使用Ollama模型进行向量化: {model_data.get('name')}")
            elif hasattr(model_data, 'type') and model_data.type in ['ollama', 'huggingface', 'openai_compatible']:
                # 本地模型管理器中的模型
                use_local_model = True
                local_model_config = {
                    'type': model_data.type,
                    'model_id': model_data.model_id,
                    'name': model_data.name,
                    'parameters': model_data.parameters
                }
                self.logger.info(f"使用本地模型进行向量化: {model_data.name} ({model_data.type})")
        
        # 如果模型名称以 "ollama_" 开头，但没有模型数据，尝试从配置中获取
        if not use_local_model and model_name.startswith("ollama_"):
            ollama_model_name = model_name[7:]  # 移除 "ollama_" 前缀
            use_local_model = True
            local_model_config = {
                'type': 'ollama',
                'model_id': ollama_model_name,
                'parameters': {}
            }
            self.logger.info(f"使用Ollama模型进行向量化: {ollama_model_name}")
        
        # 准备向量化配置
        vectorization_config = {
            'vectorization': {
                'model_name': model_name,
                'batch_size': batch_size,
                'vector_dimension': vector_dimension,
                'normalize_vectors': True,
                'device': 'cpu'
            }
        }
        
        # 如果使用本地模型，添加本地模型配置
        if use_local_model:
            if local_model_config['type'] == 'ollama':
                # 添加Ollama配置
                vectorization_config['local_models'] = {
                    'ollama': {
                        'enabled': True,
                        'api_url': 'http://localhost:11434/api',  # 默认值，可以从配置中获取
                        'default_model': local_model_config['model_id'],
                        'models': [local_model_config.get('parameters', {})]
                    }
                }
                
                # 获取主窗口配置中的Ollama API URL
                main_window = self.window()
                if hasattr(main_window, 'config') and main_window.config:
                    ollama_config = main_window.config.get('local_models', {}).get('ollama', {})
                    if ollama_config:
                        vectorization_config['local_models']['ollama']['api_url'] = ollama_config.get('api_url', 'http://localhost:11434/api')
            else:
                # 其他类型的本地模型
                vectorization_config['model_name'] = f"local:{local_model_config['name']}"
        
        # 显示进度对话框
        progress_dialog = None
        try:
            if _PYQT_AVAILABLE and 'QProgressDialog' in _PYQT_WIDGETS:
                QProgressDialog = _PYQT_WIDGETS['QProgressDialog']
                Qt = _PYQT_CORE.get('Qt')
                if QProgressDialog and Qt:
                    progress_dialog = QProgressDialog("正在向量化文本...", "取消", 0, 100, self)
                    progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
                    progress_dialog.setAutoClose(True)
                    progress_dialog.setMinimumDuration(500)  # 只有操作超过500ms才显示
                    progress_dialog.setValue(0)
                else:
                    raise ImportError("PyQt6组件不可用")
            else:
                raise ImportError("PyQt6不可用")
        except (ImportError, OSError, RuntimeError) as e:
            if hasattr(self, "logger") and self.logger:
                self.logger.warning(f"无法创建进度对话框，将使用控制台输出进度，原因: {e}")
            else:
                print(f"无法创建进度对话框，将使用控制台输出进度，原因: {e}")
            

        # 如果是Excel格式，尝试解析为表格
        if file_type == "excel" and text:
            try:
                # 导入pandas
                import pandas as pd
                import io

                # 尝试将文本解析为CSV格式（Excel文本通常是制表符或逗号分隔）
                # 尝试不同的分隔符
                for sep in ['\t', ',', ';']:
                    try:
                        df = pd.read_csv(io.StringIO(text), sep=sep)
                        # 转换为更易读的格式
                        text = df.to_string(index=False)
                        break
                    except Exception:
                        continue

                # 更新文本框
                self.text_input.setText(text)
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"解析Excel文本时出错: {e}")
                # 继续使用原始文本

        # 设置进度条
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)
            
        # 在后台线程中执行向量化
        import threading
        thread = threading.Thread(
            target=self._vectorize_text_thread,
            args=(text, model_name, file_type, batch_size, selected_index_path)
        )
        thread.daemon = True
        thread.start()

        try:
            # 导入必要的模块
            from src.vectorizer import TextEmbedding
            from src.storage import MetadataManager, VectorStore
            import numpy as np
            import logging
            import os
            import time
            import hashlib

            # 创建日志记录器
            logger = logging.getLogger(__name__)

            # 确保数据目录存在
            os.makedirs('data/vectors/metadata', exist_ok=True)

            # 创建向量存储和元数据管理器
            storage_config = {'storage': {'base_dir': 'data/vectors'}}
            vector_store = VectorStore(storage_config)
            metadata_manager = MetadataManager(storage_config)

            # 更新进度条
            self.progress_bar.setValue(10)

            # 创建文本嵌入器配置
            vectorization_config = {
                'vectorization': {
                    'model_name': model_name,
                    'vector_dimension': vector_dimension,
                    'batch_size': batch_size,
                    'device': 'cpu',
                    'normalize_vectors': True
                }
            }

            # 检查是否使用Ollama模型
            if model_name.startswith("ollama:"):
                # 获取Ollama模型名称
                ollama_model_name = model_name.split(":", 1)[1]

                # 获取主窗口配置
                main_window = self.window()
                if hasattr(main_window, 'config') and main_window.config:
                    # 检查是否有Ollama配置
                    local_models = main_window.config.get('local_models', {})
                    ollama_config = local_models.get('ollama', {})

                    if ollama_config.get('enabled', False):
                        # 添加Ollama配置
                        if 'local_models' not in vectorization_config:
                            vectorization_config['local_models'] = {}

                        vectorization_config['local_models']['ollama'] = {
                            'enabled': True,
                            'api_url': ollama_config.get('api_url', 'http://localhost:11434/api'),
                            'default_model': ollama_model_name,
                            'models': []
                        }

                        # 添加模型参数
                        for model in ollama_config.get('models', []):
                            if model.get('name') == ollama_model_name:
                                vectorization_config['local_models']['ollama']['models'] = [model]
                                break

            embedder = TextEmbedding(vectorization_config)
            logger.info(f"成功创建文本嵌入器，使用模型: {model_name}")

            # 更新进度条
            self.progress_bar.setValue(30)

            # 导入文本切片工具
            from src.utils.helpers import TextUtils

            # 将文本分割成较小的块
            chunk_size = 300  # 使用更合适的块大小，平衡语义完整性和精确匹配
            overlap = 50      # 设置较大的重叠大小，确保上下文连贯性

            # 对于非常大的文本，先进行预处理
            if len(text) > 1000000:  # 如果文本超过1MB
                logger.info(f"检测到大文本，长度: {len(text)}，进行预处理...")
                # 先按段落分割，避免一次性处理整个文本
                paragraphs = text.split('\n\n')
                chunks = []
                for i, para in enumerate(paragraphs):
                    if len(para) > chunk_size:
                        # 对大段落进行分割
                        para_chunks = TextUtils.split_text(para, chunk_size, overlap)
                        chunks.extend(para_chunks)
                    else:
                        chunks.append(para)
                    # 限制最大块数
                    if len(chunks) >= 1000:
                        logger.warning(f"达到最大块数限制(1000)，截断文本...")
                        break
            else:
                # 对于正常大小的文本，直接分割
                chunks = TextUtils.split_text(text, chunk_size, overlap)

            logger.info(f"文本已分割为 {len(chunks)} 个块")

            # 更新进度条
            self.progress_bar.setValue(40)

            # 处理每个文本块
            all_vectors = []
            all_doc_ids = []

            for i, chunk in enumerate(chunks):
                # 生成文档ID (使用文本块的哈希值)
                chunk_id = int(hashlib.md5((text[:50] + chunk[:50] + str(i)).encode()).hexdigest(), 16) % (10 ** 10)
                all_doc_ids.append(chunk_id)

                # 向量化文本块
                vector = embedder.encode_text(chunk)
                all_vectors.append(vector)

                # 更新进度条
                progress = 40 + int(20 * (i + 1) / len(chunks))
                self.progress_bar.setValue(progress)

            # 将所有向量合并为一个数组
            vectors_array = np.vstack(all_vectors)
            doc_ids_array = np.array(all_doc_ids)

            logger.info(f"成功向量化 {len(chunks)} 个文本块，向量维度: {vectors_array.shape}")

            # 更新进度条
            self.progress_bar.setValue(60)

            # 存储向量
            success = vector_store.store_vectors(vectors_array, doc_ids_array)
            if success:
                logger.info(f"成功存储 {len(chunks)} 个向量，ID: {all_doc_ids}")
            else:
                logger.error("存储向量失败")

            # 将向量添加到索引中
            try:
                from src.indexer import IndexBuilder
                from pathlib import Path

                # 获取选定的索引或创建新索引
                selected_index_text = self.index_combo.currentText()
                selected_index_data = self.index_combo.itemData(self.index_combo.currentIndex())

                if selected_index_text == self.translator.get_text("create_new_index", "创建新索引"):
                    # 创建新索引
                    new_index_name = self.new_index_name_edit.text().strip()
                    if not new_index_name:
                        # 生成默认名称
                        import time
                        new_index_name = f"index_{int(time.time())}"

                    indices_dir = Path("data/indices")
                    indices_dir.mkdir(parents=True, exist_ok=True)
                    latest_index = indices_dir / f"{new_index_name}.idx"
                    logger.info(f"将创建新索引: {latest_index}")
                elif selected_index_data:
                    # 使用选定的现有索引
                    latest_index = Path(selected_index_data)
                    logger.info(f"使用选定的索引: {latest_index}")
                else:
                    # 回退到自动选择最新索引
                    index_path = Path('data/indices')
                    index_files = list(index_path.glob('*.idx'))
                    if index_files:
                        latest_index = max(index_files, key=lambda x: x.stat().st_mtime)
                        logger.info(f"自动选择最新索引: {latest_index}")
                    else:
                        logger.warning("没有找到索引文件，将创建新索引")
                        latest_index = None

                # 创建索引构建器 - 使用简单的Flat索引避免训练问题
                config = {
                    'indexing': {
                        'index_type': 'flat',
                        'metric': 'cosine',
                        'quantization': 'none'  # 禁用量化
                    }
                }
                builder = IndexBuilder(config)

                # 如果有索引文件，加载它；否则创建新索引
                if latest_index and latest_index.exists():
                    if builder.load_index(latest_index):
                        logger.info(f"成功加载索引: {latest_index}")
                    else:
                        logger.error(f"加载索引失败: {latest_index}")
                        # 创建新索引
                        builder.create_index(vectors_array.shape[1])
                        logger.info(f"创建新索引，维度: {vectors_array.shape[1]}")
                else:
                    # 创建新索引
                    builder.create_index(vectors_array.shape[1])
                    logger.info(f"创建新索引，维度: {vectors_array.shape[1]}")

                # 添加向量到索引
                try:
                    # 检查维度兼容性
                    if hasattr(builder, 'dimension') and builder.dimension != vectors_array.shape[1]:
                        # 维度不匹配，尝试处理
                        adjusted_result = self._handle_dimension_mismatch(
                            builder, vectors_array, builder.dimension, vectors_array.shape[1], latest_index
                        )

                        if adjusted_result[0] is not None and adjusted_result[1]:
                            if isinstance(adjusted_result[0], Path):
                                # 创建了新索引
                                latest_index = adjusted_result[0]
                                logger.info(f"使用新索引: {latest_index}")
                            else:
                                # 调整了向量维度
                                vectors_array = adjusted_result[0]
                                logger.info(f"向量维度已调整为: {vectors_array.shape[1]}")
                        else:
                            logger.error("无法处理维度不匹配问题，跳过向量添加")
                            raise ValueError("向量维度不匹配且无法处理")

                    # 检查索引类型
                    index_type = type(builder.index).__name__
                    logger.info(f"索引类型: {index_type}")

                    # 根据索引类型选择添加方法
                    if index_type == 'IndexFlatIP' or index_type == 'IndexFlatL2':
                        # 对于Flat索引，使用add方法
                        builder.index.add(vectors_array)
                        logger.info(f"成功将向量添加到索引（使用add方法）")
                        success = True
                        # 更新索引状态
                        builder.total_vectors += len(vectors_array)
                    else:
                        # 对于其他索引类型，尝试使用add_vectors方法
                        success = builder.add_vectors(vectors_array, doc_ids_array)
                        logger.info(f"成功将向量添加到索引，ID: {all_doc_ids}")
                        # 更新索引状态
                        builder.total_vectors += len(vectors_array)

                    # 保存索引
                    if latest_index:
                        if builder.save_index(latest_index):
                            logger.info(f"成功保存索引: {latest_index}")
                        else:
                            logger.error(f"保存索引失败: {latest_index}")
                except Exception as e:
                    logger.error(f"将向量添加到索引时出错: {e}")
                    import traceback
                    logger.error(traceback.format_exc())
            except Exception as e:
                logger.error(f"将向量添加到索引时出错: {e}")
                import traceback
                logger.error(traceback.format_exc())

            # 更新进度条
            self.progress_bar.setValue(80)

            # 存储元数据
            # 为每个文本块存储元数据
            for i, (chunk_id, chunk) in enumerate(zip(all_doc_ids, chunks)):
                metadata = {
                    'text': chunk,
                    'original_text_start': int(i * (chunk_size - overlap) if i > 0 else 0),
                    'original_text_end': int(min((i + 1) * chunk_size - i * overlap, len(text))),
                    'chunk_index': int(i),
                    'total_chunks': int(len(chunks)),
                    'file_type': str(file_type),
                    'model': str(model_name),
                    'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'vector_dimension': int(all_vectors[i].shape[0])
                }

                success = metadata_manager.store_metadata(chunk_id, metadata)
                if success:
                    logger.info(f"成功存储元数据，ID: {chunk_id}, 块 {i+1}/{len(chunks)}")
                else:
                    logger.error(f"存储元数据失败，块 {i+1}/{len(chunks)}")

            # 更新进度条
            self.progress_bar.setValue(100)

            # 清理资源
            embedder.cleanup()

            # 通知状态管理器
            try:
                from ..state_manager import get_state_manager
                state_manager = get_state_manager()
                if latest_index:
                    state_manager.notify_vectors_added(str(latest_index), len(chunks))
            except Exception as e:
                logger.warning(f"通知状态管理器时出错: {e}")

            # 显示成功消息
            if _PYQT_AVAILABLE and 'QMessageBox' in _PYQT_WIDGETS:
                QMessageBox = _PYQT_WIDGETS['QMessageBox']
                QMessageBox.information(
                    self,
                    "向量化成功",
                    f"成功向量化文本并保存到数据库\n共 {len(chunks)} 个文本块\n文档ID: {all_doc_ids[0]}...等"
                )
            else:
                # 使用日志记录成功信息
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"向量化成功！共 {len(chunks)} 个文本块")

        except Exception as e:
            # 记录错误
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"向量化文本时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 显示错误消息
            if _PYQT_AVAILABLE and 'QMessageBox' in _PYQT_WIDGETS:
                QMessageBox = _PYQT_WIDGETS['QMessageBox']
                QMessageBox.critical(
                    self,
                    "向量化失败",
                    f"向量化文本时出错: {str(e)}"
                )
            else:
                # 使用日志记录错误信息
                print(f"向量化失败: {str(e)}")

            # 重置进度条
            self.progress_bar.setValue(0)

    def _vectorize_text_thread(self, text, model_name, file_type, batch_size, selected_index_path=None):
        """在后台线程中执行文本向量化"""
        try:
            import logging
            logger = logging.getLogger(__name__)
            
            # 导入必要的模块
            from ...vectorizer import TextEmbedding
            from ...storage import VectorStore, MetadataManager
            import numpy as np

            # 创建配置
            config = {
                'vectorization': {
                    'model_name': model_name,
                    'batch_size': batch_size,
                    'device': 'cpu',
                    'normalize_vectors': True
                },
                'storage': {
                    'base_dir': 'data/vectors'
                }
            }

            # 创建文本嵌入器
            embedder = TextEmbedding(config)

            # 创建向量存储
            vector_store = VectorStore(config)

            # 创建元数据管理器
            metadata_manager = MetadataManager(config)
            
            # 更新进度条
            self._update_progress(10)
            
            # 分割文本
            chunks = self._split_text(text, file_type)
            logger.info(f"文本已分割为 {len(chunks)} 个块")
            
            # 更新进度条
            self._update_progress(20)
            
            # 向量化文本
            all_vectors = []
            all_doc_ids = []
            
            # 批量处理
            for i in range(0, len(chunks), batch_size):
                batch = chunks[i:i+batch_size]
                
                # 生成文档ID (使用整数ID以保持数据库兼容性)
                import hashlib
                doc_ids = []
                for j, chunk in enumerate(batch):
                    # 生成基于内容和位置的唯一整数ID
                    chunk_id = int(hashlib.md5((chunk[:50] + str(i + j)).encode()).hexdigest(), 16) % (10 ** 10)
                    doc_ids.append(chunk_id)
                
                # 向量化
                vectors = embedder.encode_batch(batch)
                
                # 保存结果
                all_vectors.append(vectors)
                all_doc_ids.extend(doc_ids)
                
                # 保存元数据
                import time
                for j, doc_id in enumerate(doc_ids):
                    metadata = {
                        'text': str(batch[j]),
                        'source': 'text_input',
                        'timestamp': float(time.time())
                    }
                    metadata_manager.store_metadata(doc_id, metadata)
                
                # 更新进度条
                progress = 20 + int(40 * (i + len(batch)) / len(chunks))
                self._update_progress(progress)
            
            # 将所有向量合并为一个数组
            vectors_array = np.vstack(all_vectors)
            doc_ids_array = np.array(all_doc_ids)
            
            logger.info(f"成功向量化 {len(chunks)} 个文本块，向量维度: {vectors_array.shape}")
            
            # 更新进度条
            self._update_progress(60)
            
            # 存储向量
            success = vector_store.store_vectors(vectors_array, doc_ids_array)
            if success:
                logger.info(f"成功存储 {len(chunks)} 个向量，ID: {all_doc_ids}")
            else:
                logger.error("存储向量失败")
            
            # 将向量添加到索引中
            try:
                from src.indexer import IndexBuilder
                from pathlib import Path

                # 获取要使用的索引
                target_index = None

                if selected_index_path and selected_index_path.exists():
                    # 使用用户选择的索引
                    target_index = selected_index_path
                    logger.info(f"使用用户选择的索引: {target_index}")
                else:
                    # 如果用户选择创建新索引或选择的索引不存在，查找可用索引
                    index_path = Path('data/indices')
                    index_files = list(index_path.glob('*.idx'))

                    if index_files:
                        # 使用最新的索引文件作为备选
                        target_index = max(index_files, key=lambda x: x.stat().st_mtime)
                        logger.info(f"使用最新的索引文件作为备选: {target_index}")
                    else:
                        logger.warning("未找到任何索引文件，将创建新索引")

                if target_index:

                    # 创建索引构建器 - 使用简单的Flat索引避免训练问题
                    config = {
                        'indexing': {
                            'index_type': 'flat',
                            'metric': 'cosine',
                            'quantization': 'none'  # 禁用量化
                        }
                    }
                    builder = IndexBuilder(config)

                    # 加载索引
                    if builder.load_index(target_index):
                        logger.info(f"成功加载索引: {target_index}")

                        # 添加向量到索引
                        try:
                            # 检查维度兼容性
                            if hasattr(builder, 'dimension') and builder.dimension != vectors_array.shape[1]:
                                logger.warning(f"向量维度不匹配: 索引维度={builder.dimension}, 向量维度={vectors_array.shape[1]}")
                                # 简单处理：跳过不匹配的向量
                                logger.error("向量维度不匹配，跳过添加到索引")
                            else:
                                # 检查索引类型
                                index_type = type(builder.index).__name__
                                logger.info(f"索引类型: {index_type}")

                                # 根据索引类型选择添加方法
                                if index_type == 'IndexFlatIP' or index_type == 'IndexFlatL2':
                                    # 对于Flat索引，使用add方法
                                    builder.index.add(vectors_array)
                                    logger.info(f"成功将向量添加到索引（使用add方法）")
                                else:
                                    # 对于其他索引类型，使用add_vectors方法
                                    success = builder.add_vectors(vectors_array, doc_ids_array, metadata_manager)
                                    logger.info(f"成功将向量添加到索引，ID: {all_doc_ids}")

                                # 更新索引状态
                                builder.total_vectors += len(chunks)

                                # 保存索引
                                if builder.save_index(target_index):
                                    logger.info(f"成功保存索引: {target_index}")
                                else:
                                    logger.error(f"保存索引失败: {target_index}")
                        except Exception as e:
                            logger.error(f"将向量添加到索引时出错: {e}")
                            import traceback
                            logger.error(traceback.format_exc())
                    else:
                        logger.error(f"加载索引失败: {target_index}")
                else:
                    logger.warning("未找到可用的索引文件，无法将向量添加到索引")

            except Exception as e:
                logger.error(f"将向量添加到索引时出错: {e}")
                import traceback
                logger.error(traceback.format_exc())

        except Exception as e:
            # 记录错误
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"向量化文本时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _on_select_files(self):
        """选择文件按钮点击处理"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            self.translator.get_text("select_files", "选择文件"),
            "",
            "All Files (*)"
        )

        if file_paths:
            current_text = self.file_list.toPlainText()
            if current_text:
                self.file_list.setText(current_text + "\n" + "\n".join(file_paths))
            else:
                self.file_list.setText("\n".join(file_paths))

    def _on_select_folder(self):
        """选择文件夹按钮点击处理"""
        folder_path = QFileDialog.getExistingDirectory(
            self,
            self.translator.get_text("select_folder", "选择文件夹"),
            "test_training_data/raw_documents"  # 默认指向训练数据目录
        )

        if folder_path:
            # 扫描文件夹中的文件
            from pathlib import Path
            folder = Path(folder_path)

            # 支持的文件类型
            supported_extensions = {'.pdf', '.md', '.txt', '.docx', '.doc'}

            # 递归扫描文件
            found_files = []
            for ext in supported_extensions:
                found_files.extend(folder.rglob(f'*{ext}'))

            if found_files:
                # 显示找到的文件
                file_list = []
                for file_path in sorted(found_files):
                    file_list.append(str(file_path))

                current_text = self.file_list.toPlainText()
                if current_text:
                    self.file_list.setText(current_text + "\n" + "\n".join(file_list))
                else:
                    self.file_list.setText("\n".join(file_list))

                # 显示统计信息
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"在文件夹 {folder_path} 中找到 {len(found_files)} 个文件")

                # 按类型统计
                type_counts = {}
                for file_path in found_files:
                    ext = file_path.suffix.lower()
                    type_counts[ext] = type_counts.get(ext, 0) + 1

                stats_msg = f"文件统计: " + ", ".join([f"{ext}: {count}" for ext, count in type_counts.items()])
                logger.info(stats_msg)
            else:
                # 如果没有找到支持的文件，仍然添加文件夹路径
                current_text = self.file_list.toPlainText()
                if current_text:
                    self.file_list.setText(current_text + "\n" + folder_path)
                else:
                    self.file_list.setText(folder_path)

    def _on_clear_files(self):
        """清除文件按钮点击处理"""
        self.file_list.clear()

    def _on_vectorize_files(self):
        """向量化文件按钮点击处理"""
        # 获取文件列表
        file_paths = self.file_list.toPlainText().strip().split('\n')
        if not file_paths or not file_paths[0]:
            return

        # 检查 QMessageBox 是否可用
        message_box_available = _PYQT_AVAILABLE and 'QMessageBox' in _PYQT_WIDGETS
        if not message_box_available:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning("QMessageBox 不可用，将使用日志记录消息")
        
        # 获取选项
        # 检查 file_model_combo 是否存在
        if hasattr(self, 'file_model_combo'):
            model_name = self.file_model_combo.currentText()
            # 检查是否是本地模型
            local_model = self.file_model_combo.itemData(self.file_model_combo.currentIndex())
            if local_model:
                # 使用本地模型
                model_name = f"local:{local_model.name}"
        else:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning("file_model_combo 组件不存在，使用默认模型")
            model_name = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
            local_model = None
        
        # 检查 file_type_combo 是否存在
        if hasattr(self, 'file_type_combo'):
            language = self.file_type_combo.currentText()
        else:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning("file_type_combo 组件不存在，使用默认文件类型")
            language = "auto"
        
        # 检查 file_batch_size_spin 是否存在
        if hasattr(self, 'file_batch_size_spin'):
            batch_size = self.file_batch_size_spin.value()
        else:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning("file_batch_size_spin 组件不存在，使用默认批处理大小")
            batch_size = 32

        # 获取用户选择的索引
        selected_index_path = None
        if hasattr(self, 'file_index_combo'):
            current_index = self.file_index_combo.currentIndex()
            if current_index > 0:  # 0是"创建新索引"选项
                index_data = self.file_index_combo.itemData(current_index)
                if index_data:
                    selected_index_path = Path(index_data)
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.info(f"用户选择的索引: {selected_index_path}")
                else:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.info("用户选择创建新索引")
            else:
                import logging
                logger = logging.getLogger(__name__)
                logger.info("用户选择创建新索引")
        else:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning("file_index_combo 组件不存在，将创建新索引")

        # 设置进度条
        self.file_progress_bar.setValue(0)

        try:
            # 导入必要的模块
            from src.vectorizer import TextEmbedding
            from src.storage import MetadataManager, VectorStore
            import numpy as np
            import logging
            import os
            import time
            import hashlib
            from pathlib import Path

            # 创建日志记录器
            logger = logging.getLogger(__name__)

            # 确保数据目录存在
            os.makedirs('data/vectors/metadata', exist_ok=True)

            # 创建向量存储和元数据管理器
            storage_config = {'storage': {'base_dir': 'data/vectors'}}
            vector_store = VectorStore(storage_config)
            metadata_manager = MetadataManager(storage_config)

            # 更新进度条
            self.file_progress_bar.setValue(5)

            # 创建文本嵌入器配置
            vectorization_config = {
                'vectorization': {
                    'model_name': model_name,
                    'vector_dimension': 384,
                    'batch_size': batch_size,
                    'device': 'cpu',
                    'normalize_vectors': True
                }
            }

            # 检查是否使用Ollama模型
            if model_name.startswith("ollama:"):
                # 获取Ollama模型名称
                ollama_model_name = model_name.split(":", 1)[1]

                # 获取主窗口配置
                main_window = self.window()
                if hasattr(main_window, 'config') and main_window.config:
                    # 检查是否有Ollama配置
                    local_models = main_window.config.get('local_models', {})
                    ollama_config = local_models.get('ollama', {})

                    if ollama_config.get('enabled', False):
                        # 添加Ollama配置
                        if 'local_models' not in vectorization_config:
                            vectorization_config['local_models'] = {}

                        vectorization_config['local_models']['ollama'] = {
                            'enabled': True,
                            'api_url': ollama_config.get('api_url', 'http://localhost:11434/api'),
                            'default_model': ollama_model_name,
                            'models': []
                        }

                        # 添加模型参数
                        for model in ollama_config.get('models', []):
                            if model.get('name') == ollama_model_name:
                                vectorization_config['local_models']['ollama']['models'] = [model]
                                break

            embedder = TextEmbedding(vectorization_config)
            logger.info(f"成功创建文本嵌入器，使用模型: {model_name}")

            # 更新进度条
            self.file_progress_bar.setValue(10)

            # 处理每个文件
            total_files = len(file_paths)
            processed_files = 0

            for file_path in file_paths:
                file_path = file_path.strip()
                if not file_path:
                    continue

                path = Path(file_path)
                if not path.exists():
                    logger.warning(f"文件不存在: {file_path}")
                    continue

                # 如果是目录，跳过
                if path.is_dir():
                    logger.info(f"跳过目录: {file_path}")
                    continue

                try:
                    # 检查文件扩展名
                    file_ext = path.suffix.lower()
                    text = None

                    # 处理Excel文件
                    if file_ext in ['.xlsx', '.xls', '.xlsm']:
                        try:
                            import pandas as pd
                            # 读取Excel文件
                            df = pd.read_excel(file_path)
                            # 转换为文本格式
                            text = df.to_string(index=False)
                            logger.info(f"成功使用pandas读取Excel文件: {file_path}")
                        except Exception as e:
                            logger.error(f"使用pandas读取Excel文件失败: {file_path}, {e}")
                            # 如果pandas读取失败，尝试其他方法

                    # 处理Markdown文件
                    elif file_ext in ['.md', '.markdown']:
                        try:
                            # 尝试不同的编码
                            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']
                            for encoding in encodings:
                                try:
                                    with open(file_path, 'r', encoding=encoding) as f:
                                        raw_content = f.read()

                                    # 如果有frontmatter库，尝试解析frontmatter
                                    try:
                                        import frontmatter
                                        post = frontmatter.loads(raw_content)
                                        # 获取内容部分
                                        text = post.content
                                    except ImportError:
                                        # 如果没有frontmatter库，直接使用原始内容
                                        text = raw_content

                                    logger.info(f"成功使用 {encoding} 编码读取Markdown文件: {file_path}")
                                    break
                                except UnicodeDecodeError:
                                    logger.warning(f"使用 {encoding} 编码读取Markdown文件失败: {file_path}")
                        except Exception as e:
                            logger.error(f"读取Markdown文件失败: {file_path}, {e}")

                    # 处理其他文本文件
                    if text is None:
                        # 尝试不同的编码
                        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']

                        for encoding in encodings:
                            try:
                                with open(file_path, 'r', encoding=encoding) as f:
                                    text = f.read()
                                logger.info(f"成功使用 {encoding} 编码读取文件: {file_path}")
                                break
                            except UnicodeDecodeError:
                                logger.warning(f"使用 {encoding} 编码读取文件失败: {file_path}")

                        # 如果所有编码都失败，尝试二进制模式读取
                        if text is None:
                            try:
                                with open(file_path, 'rb') as f:
                                    binary_data = f.read()
                                    # 尝试使用 latin-1 编码（可以处理任何字节序列）
                                    text = binary_data.decode('latin-1')
                                logger.info(f"使用二进制模式读取文件: {file_path}")
                            except Exception as e:
                                logger.error(f"读取文件失败: {file_path}, {e}")
                                continue

                    # 导入文本切片工具
                    from src.utils.helpers import TextUtils

                    # 将文本分割成较小的块
                    chunk_size = 300  # 使用更合适的块大小，平衡语义完整性和精确匹配
                    overlap = 50      # 设置较大的重叠大小，确保上下文连贯性

                    # 对于非常大的文本，先进行预处理
                    if len(text) > 1000000:  # 如果文本超过1MB
                        logger.info(f"检测到大文本文件，长度: {len(text)}，进行预处理...")
                        # 先按段落分割，避免一次性处理整个文本
                        paragraphs = text.split('\n\n')
                        chunks = []
                        for i, para in enumerate(paragraphs):
                            if len(para) > chunk_size:
                                # 对大段落进行分割
                                para_chunks = TextUtils.split_text(para, chunk_size, overlap)
                                chunks.extend(para_chunks)
                            else:
                                chunks.append(para)
                            # 限制最大块数
                            if len(chunks) >= 1000:
                                logger.warning(f"达到最大块数限制(1000)，截断文件...")
                                break
                    else:
                        # 对于正常大小的文本，直接分割
                        chunks = TextUtils.split_text(text, chunk_size, overlap)

                    logger.info(f"文件 {file_path} 已分割为 {len(chunks)} 个块")

                    # 处理每个文本块
                    file_vectors = []
                    file_doc_ids = []

                    for i, chunk in enumerate(chunks):
                        # 生成文档ID (使用文本块的哈希值)
                        chunk_id = int(hashlib.md5((file_path + chunk[:50] + str(i)).encode()).hexdigest(), 16) % (10 ** 10)
                        file_doc_ids.append(chunk_id)

                        # 向量化文本块
                        vector = embedder.encode_text(chunk)
                        file_vectors.append(vector)

                    # 将所有向量合并为一个数组
                    vectors_array = np.vstack(file_vectors)
                    doc_ids_array = np.array(file_doc_ids)

                    logger.info(f"成功向量化文件: {file_path}，共 {len(chunks)} 个文本块，向量维度: {vectors_array.shape}")

                    # 存储向量
                    success = vector_store.store_vectors(vectors_array, doc_ids_array)
                    if success:
                        logger.info(f"成功存储 {len(chunks)} 个向量，ID: {file_doc_ids}")
                    else:
                        logger.error(f"存储向量失败: {file_path}")

                    # 将向量添加到索引中
                    try:
                        from src.indexer import IndexBuilder
                        from pathlib import Path

                        # 获取要使用的索引
                        target_index = None

                        if selected_index_path and selected_index_path.exists():
                            # 使用用户选择的索引
                            target_index = selected_index_path
                            logger.info(f"使用用户选择的索引: {target_index}")
                        else:
                            # 如果用户选择创建新索引或选择的索引不存在，查找可用索引
                            index_path = Path('data/indices')
                            index_files = list(index_path.glob('*.idx'))

                            if index_files:
                                # 使用最新的索引文件作为备选
                                target_index = max(index_files, key=lambda x: x.stat().st_mtime)
                                logger.info(f"使用最新的索引文件作为备选: {target_index}")
                            else:
                                logger.warning("未找到任何索引文件，将创建新索引")

                        if target_index:

                            # 创建索引构建器 - 使用简单的Flat索引避免训练问题
                            config = {
                                'indexing': {
                                    'index_type': 'flat',
                                    'metric': 'cosine',
                                    'quantization': 'none'  # 禁用量化
                                }
                            }
                            builder = IndexBuilder(config)

                            # 加载索引
                            if builder.load_index(target_index):
                                logger.info(f"成功加载索引: {target_index}")

                                # 添加向量到索引
                                try:
                                    # 检查维度兼容性
                                    if hasattr(builder, 'dimension') and builder.dimension != vectors_array.shape[1]:
                                        # 维度不匹配，尝试处理
                                        adjusted_result = self._handle_dimension_mismatch(
                                            builder, vectors_array, builder.dimension, vectors_array.shape[1], target_index
                                        )

                                        if adjusted_result[0] is not None and adjusted_result[1]:
                                            if isinstance(adjusted_result[0], Path):
                                                # 创建了新索引
                                                target_index = adjusted_result[0]
                                                logger.info(f"使用新索引: {target_index}")
                                            else:
                                                # 调整了向量维度
                                                vectors_array = adjusted_result[0]
                                                logger.info(f"向量维度已调整为: {vectors_array.shape[1]}")
                                        else:
                                            logger.error("无法处理维度不匹配问题，跳过此文件")
                                            continue  # 这里在文件循环中，可以使用continue

                                    # 检查索引类型
                                    index_type = type(builder.index).__name__
                                    logger.info(f"索引类型: {index_type}")

                                    # 根据索引类型选择添加方法
                                    if index_type == 'IndexFlatIP' or index_type == 'IndexFlatL2':
                                        # 对于Flat索引，使用add方法
                                        builder.index.add(vectors_array)
                                        logger.info(f"成功将向量添加到索引（使用add方法）")
                                        success = True
                                    else:
                                        # 对于其他索引类型，使用add_vectors方法并传入元数据管理器
                                        success = builder.add_vectors(vectors_array, doc_ids_array, metadata_manager)
                                        logger.info(f"成功将向量添加到索引，ID: {file_doc_ids}")

                                    # 更新索引状态
                                    builder.total_vectors += 1

                                    # 保存索引
                                    if builder.save_index(target_index):
                                        logger.info(f"成功保存索引: {target_index}")
                                    else:
                                        logger.error(f"保存索引失败: {target_index}")
                                except Exception as e:
                                    logger.error(f"将向量添加到索引时出错: {e}")
                                    import traceback
                                    logger.error(traceback.format_exc())
                            else:
                                logger.error(f"加载索引失败: {target_index}")
                        else:
                            logger.warning("未找到可用的索引文件，无法将向量添加到索引")
                    except Exception as e:
                        logger.error(f"将向量添加到索引时出错: {e}")
                        import traceback
                        logger.error(traceback.format_exc())

                    # 存储元数据
                    # 为每个文本块存储元数据
                    for i, (chunk_id, chunk) in enumerate(zip(file_doc_ids, chunks)):
                        metadata = {
                            'text': str(chunk),
                            'original_text_start': int(i * (chunk_size - overlap) if i > 0 else 0),
                            'original_text_end': int(min((i + 1) * chunk_size - i * overlap, len(text))),
                            'chunk_index': int(i),
                            'total_chunks': int(len(chunks)),
                            'filename': str(path.name),
                            'filepath': str(path),
                            'file_type': str(path.suffix),
                            'language': str(language),
                            'model': str(model_name),
                            'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                            'file_size': int(path.stat().st_size),
                            'vector_dimension': int(file_vectors[i].shape[0])
                        }

                        success = metadata_manager.store_metadata(chunk_id, metadata)
                        if success:
                            logger.info(f"成功存储元数据，ID: {chunk_id}, 块 {i+1}/{len(chunks)}")
                        else:
                            logger.error(f"存储元数据失败: {file_path}, 块 {i+1}/{len(chunks)}")

                except Exception as e:
                    logger.error(f"处理文件时出错: {file_path}, {e}")

                # 更新进度
                processed_files += 1
                progress = 10 + int(90 * processed_files / total_files)
                self.file_progress_bar.setValue(progress)

            # 更新进度条
            self.file_progress_bar.setValue(100)

            # 清理资源
            embedder.cleanup()

            # 通知状态管理器
            try:
                from ..state_manager import get_state_manager
                state_manager = get_state_manager()
                # 估算总向量数（每个文件的平均向量数）
                total_vectors = processed_files * 10  # 估算值
                if 'latest_index' in locals():
                    state_manager.notify_vectors_added(str(latest_index), total_vectors)
            except Exception as e:
                logger.warning(f"通知状态管理器时出错: {e}")

            # 显示成功消息
            if _PYQT_AVAILABLE and 'QMessageBox' in _PYQT_WIDGETS:
                QMessageBox = _PYQT_WIDGETS['QMessageBox']
                QMessageBox.information(
                    self,
                    "向量化成功",
                    f"成功向量化 {processed_files} 个文件"
                )
            else:
                print(f"向量化成功: 成功向量化 {processed_files} 个文件")

        except Exception as e:
            # 记录错误
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"向量化文件时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 显示错误消息（避免在错误处理中导入PyQt6）
            try:
                # 检查 QMessageBox 是否可用
                if message_box_available:
                    QMessageBox.critical(
                        self,
                        "向量化失败",
                        f"向量化文件时出错: {str(e)}"
                    )
                else:
                    # 如果 QMessageBox 不可用，只打印错误
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.error(f"向量化失败: {str(e)}")
                    print(f"向量化失败: {str(e)}")
            except Exception as msg_error:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"显示错误消息时出错: {msg_error}")
                print(f"向量化失败: {str(e)}")

            # 重置进度条
            self.file_progress_bar.setValue(0)

    def on_language_changed(self):
        """语言变更回调"""
        try:
            # 更新标题
            title_label = self.findChild(QLabel, "titleLabel")
            if title_label:
                title_label.setText(self.translator.get_text("vectorize_data", "向量化数据"))
            else:
                logger.warning("未找到titleLabel组件")

            # 更新表单标签
            form_layouts = self.findChildren(QFormLayout)
            if len(form_layouts) >= 1:
                # 文本向量化表单
                text_form = form_layouts[0]
                if text_form.rowCount() >= 3:
                    label_item = text_form.itemAt(0, QFormLayout.ItemRole.LabelRole)
                    if label_item and label_item.widget():
                        label_item.widget().setText(self.translator.get_text("model", "模型:"))

                    label_item = text_form.itemAt(1, QFormLayout.ItemRole.LabelRole)
                    if label_item and label_item.widget():
                        label_item.widget().setText(self.translator.get_text("index", "索引:"))

            # 更新组框标题
            group_boxes = self.findChildren(QGroupBox)
            for group_box in group_boxes:
                if hasattr(group_box, 'objectName'):
                    obj_name = group_box.objectName()
                    if "input_text" in obj_name or "输入文本" in group_box.title():
                        group_box.setTitle(self.translator.get_text("input_text", "输入文本"))
                    elif "advanced_params" in obj_name or "高级参数" in group_box.title():
                        group_box.setTitle(self.translator.get_text("advanced_params", "高级参数"))
                    elif "select_files" in obj_name or "选择文件" in group_box.title():
                        group_box.setTitle(self.translator.get_text("select_files", "选择文件"))
                    elif "vectorization_options" in obj_name or "向量化选项" in group_box.title():
                        group_box.setTitle(self.translator.get_text("vectorization_options", "向量化选项"))

            # 更新按钮
            if hasattr(self, 'load_text_button'):
                self.load_text_button.setText(self.translator.get_text("load_file", "加载文件"))
            if hasattr(self, 'vectorize_text_button'):
                self.vectorize_text_button.setText(self.translator.get_text("vectorize", "向量化"))
            if hasattr(self, 'select_files_button'):
                self.select_files_button.setText(self.translator.get_text("select_files", "选择文件"))
            if hasattr(self, 'select_folder_button'):
                self.select_folder_button.setText(self.translator.get_text("select_folder", "选择文件夹"))
            if hasattr(self, 'clear_files_button'):
                self.clear_files_button.setText(self.translator.get_text("clear", "清除"))
            if hasattr(self, 'vectorize_files_button'):
                self.vectorize_files_button.setText(self.translator.get_text("vectorize", "向量化"))
            if hasattr(self, 'refresh_indices_btn'):
                self.refresh_indices_btn.setText(self.translator.get_text("refresh", "刷新"))
            if hasattr(self, 'text_refresh_indices_btn'):
                self.text_refresh_indices_btn.setText(self.translator.get_text("refresh", "刷新"))
            if hasattr(self, 'file_refresh_indices_btn'):
                self.file_refresh_indices_btn.setText(self.translator.get_text("refresh", "刷新"))

            # 更新文件表单标签
            if len(form_layouts) >= 2:
                file_form = form_layouts[1]
                if file_form.rowCount() >= 6:
                    label_item = file_form.itemAt(0, QFormLayout.ItemRole.LabelRole)
                    if label_item and label_item.widget():
                        label_item.widget().setText(self.translator.get_text("model", "模型:"))

                    label_item = file_form.itemAt(1, QFormLayout.ItemRole.LabelRole)
                    if label_item and label_item.widget():
                        label_item.widget().setText(self.translator.get_text("index", "索引:"))

                    label_item = file_form.itemAt(2, QFormLayout.ItemRole.LabelRole)
                    if label_item and label_item.widget():
                        label_item.widget().setText(self.translator.get_text("file_type", "文件类型:"))

                    label_item = file_form.itemAt(3, QFormLayout.ItemRole.LabelRole)
                    if label_item and label_item.widget():
                        label_item.widget().setText(self.translator.get_text("vector_dimension", "向量维度:"))

                    label_item = file_form.itemAt(4, QFormLayout.ItemRole.LabelRole)
                    if label_item and label_item.widget():
                        label_item.widget().setText(self.translator.get_text("device", "计算设备:"))

                    label_item = file_form.itemAt(5, QFormLayout.ItemRole.LabelRole)
                    if label_item and label_item.widget():
                        label_item.widget().setText(self.translator.get_text("batch_size", "批处理大小:"))

            # 更新标签页标题
            tab_widget = self.findChild(QTabWidget)
            if tab_widget:
                tab_widget.setTabText(0, self.translator.get_text("text_vectorization", "文本向量化"))
                tab_widget.setTabText(1, self.translator.get_text("file_vectorization", "文件向量化"))

            # 更新占位符文本
            if hasattr(self, 'text_input'):
                self.text_input.setPlaceholderText(self.translator.get_text("enter_text_here", "在此输入文本..."))
            if hasattr(self, 'file_list'):
                self.file_list.setPlaceholderText(self.translator.get_text("selected_files_shown_here", "选择的文件将显示在这里..."))
            if hasattr(self, 'new_index_name_edit'):
                self.new_index_name_edit.setPlaceholderText(self.translator.get_text("enter_index_name", "输入索引名称"))
            if hasattr(self, 'file_new_index_name_edit'):
                self.file_new_index_name_edit.setPlaceholderText(self.translator.get_text("enter_index_name", "输入索引名称"))

            # 调用更新翻译方法
            self.update_translation()

            logger.info("VectorizeWidget语言更新完成")

        except Exception as e:
            logger.error(f"VectorizeWidget语言更新时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _on_model_changed(self, index):
        """处理模型选择变更"""
        try:
            # 获取当前选择的模型
            sender = self.sender()
            if not sender:
                return
            
            # 跳过分隔符和标题项
            if index <= 0 or sender.itemText(index) == "--- 本地模型 ---":
                # 选择下一个有效项
                sender.setCurrentIndex(index + 1)
                return
            
            # 获取模型信息
            model_name = sender.currentText()
            model_obj = sender.itemData(index)
            
            import logging
            logger = logging.getLogger(__name__)
            
            if model_obj:
                # 本地模型
                logger.info(f"选择了本地模型: {model_name}, 类型: {model_obj.type}")
                # 可以在这里根据模型类型设置其他参数
            else:
                # 预定义模型
                logger.info(f"选择了预定义模型: {model_name}")
                
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"处理模型变更时出错: {e}")

    def _on_index_selected(self, index):
        """处理索引选择变更"""
        try:
            # 跳过分隔符和标题项
            if self.index_combo.itemText(index) == "--- 可用索引 ---":
                # 选择下一个有效项
                self.index_combo.setCurrentIndex(index + 1)
                return

            # 获取选中的索引信息
            if index > 0:
                index_data = self.index_combo.itemData(index)
                if index_data:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.info(f"选择了索引: {self.index_combo.itemText(index)}")

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"处理索引选择变更时出错: {e}")

    def _on_file_index_selected(self, index):
        """处理文件索引选择变更"""
        try:
            # 跳过分隔符和标题项
            if self.file_index_combo.itemText(index) == "--- 可用索引 ---":
                # 选择下一个有效项
                self.file_index_combo.setCurrentIndex(index + 1)
                return

            # 获取选中的索引信息
            if index > 0:
                index_data = self.file_index_combo.itemData(index)
                if index_data:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.info(f"选择了文件索引: {self.file_index_combo.itemText(index)}")

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"处理文件索引选择变更时出错: {e}")

    def _update_progress(self, value):
        """更新进度条"""
        try:
            # 尝试使用 QApplication.processEvents() 确保 UI 更新
            if _PYQT_AVAILABLE and 'QApplication' in _PYQT_WIDGETS:
                QApplication = _PYQT_WIDGETS['QApplication']

                # 检查进度条是否存在
                if hasattr(self, 'progress_bar'):
                    self.progress_bar.setValue(value)
                elif hasattr(self, 'file_progress_bar'):
                    self.file_progress_bar.setValue(value)

                # 处理 Qt 事件，确保 UI 更新
                QApplication.processEvents()
            else:
                # 如果无法使用 PyQt6，则只记录进度
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"进度更新: {value}%")

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"更新进度时出错: {e}")

    def update_translation(self):
        """更新翻译"""
        try:
            # 更新文本向量化标签页
            if hasattr(self, 'index_combo'):
                # 更新"创建新索引"选项
                create_new_text = self.translator.get_text("create_new_index", "创建新索引")
                if self.index_combo.itemText(0) != create_new_text:
                    self.index_combo.setItemText(0, create_new_text)
            
            if hasattr(self, 'file_index_combo'):
                # 更新"创建新索引"选项
                create_new_text = self.translator.get_text("create_new_index", "创建新索引")
                if self.file_index_combo.itemText(0) != create_new_text:
                    self.file_index_combo.setItemText(0, create_new_text)
            
            # 重新加载索引以更新翻译
            self._load_available_indices()
            
        except Exception as e:
            import logging
            import traceback
            logger = logging.getLogger(__name__)
            logger.error(f"更新翻译时出错: {e}")
            logger.error(traceback.format_exc())

    def _load_saved_params(self):
        """加载保存的参数设置"""
        try:
            # 获取向量化参数
            params = self.param_manager.get_params('vectorization')
            
            # 设置批处理大小
            batch_size = params.get('batch_size', 32)
            if hasattr(self, 'batch_size_spin'):
                self.batch_size_spin.setValue(batch_size)
            if hasattr(self, 'file_batch_size_spin'):
                self.file_batch_size_spin.setValue(batch_size)
            
            # 设置向量维度
            vector_dim = params.get('vector_dimension', 384)
            if hasattr(self, 'vector_dim_spin'):
                self.vector_dim_spin.setValue(vector_dim)
            if hasattr(self, 'file_vector_dim_spin'):
                self.file_vector_dim_spin.setValue(vector_dim)
            
            # 设置标准化向量
            normalize = params.get('normalize_vectors', True)
            if hasattr(self, 'normalize_check'):
                self.normalize_check.setChecked(normalize)
            if hasattr(self, 'file_normalize_check'):
                self.file_normalize_check.setChecked(normalize)
            
            # 设置设备
            device = params.get('device', 'cpu')
            if hasattr(self, 'device_combo'):
                index = self.device_combo.findText(device)
                if index >= 0:
                    self.device_combo.setCurrentIndex(index)
            
            if hasattr(self, 'file_device_combo'):
                index = self.file_device_combo.findText(device)
                if index >= 0:
                    self.file_device_combo.setCurrentIndex(index)
                
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"加载保存的参数设置时出错: {e}")

    def _save_current_params(self):
        """保存当前参数设置"""
        try:
            # 获取当前参数
            params = {}

            if hasattr(self, 'text_model_combo'):
                params['model'] = self.text_model_combo.currentText()
            if hasattr(self, 'batch_size_spin'):
                params['batch_size'] = self.batch_size_spin.value()
            if hasattr(self, 'vector_dim_spin'):
                params['vector_dimension'] = self.vector_dim_spin.value()
            if hasattr(self, 'normalize_check'):
                params['normalize_vectors'] = self.normalize_check.isChecked()
            if hasattr(self, 'device_combo'):
                params['device'] = self.device_combo.currentText()
            
            # 保存参数
            self.param_manager.set_params('vectorization', params)
            
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"保存当前参数设置时出错: {e}")

    def _on_index_settings(self):
        """索引设置按钮点击处理"""
        try:
            from .index_settings import IndexSettingsWidget
            
            # 创建索引设置对话框
            settings_dialog = QDialog(self)
            settings_dialog.setWindowTitle(self.translator.get_text("index_settings", "索引设置"))
            settings_dialog.setMinimumWidth(500)
            settings_dialog.setMinimumHeight(400)
            
            # 创建布局
            layout = QVBoxLayout(settings_dialog)
            
            # 创建索引设置小部件
            settings_widget = IndexSettingsWidget(settings_dialog)
            layout.addWidget(settings_widget)
            
            # 连接设置保存信号
            settings_widget.settings_saved.connect(lambda: settings_dialog.accept())
            
            # 显示对话框
            settings_dialog.exec()
            
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"打开索引设置对话框时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())


    def _show_vectorization_result(self, vectors, texts):
        """显示向量化结果"""
        try:
            # 创建结果对话框
            if _PYQT_AVAILABLE and all(comp in _PYQT_WIDGETS for comp in ['QDialog', 'QVBoxLayout', 'QLabel', 'QTextEdit', 'QPushButton', 'QTabWidget']):
                QDialog = _PYQT_WIDGETS['QDialog']
                QVBoxLayout = _PYQT_WIDGETS['QVBoxLayout']
                QLabel = _PYQT_WIDGETS['QLabel']
                QTextEdit = _PYQT_WIDGETS['QTextEdit']
                QPushButton = _PYQT_WIDGETS['QPushButton']
                QTabWidget = _PYQT_WIDGETS['QTabWidget']

                dialog = QDialog(self)
                dialog.setWindowTitle("向量化结果")
                dialog.resize(600, 400)

                layout = QVBoxLayout(dialog)
            else:
                # PyQt6不可用，使用控制台输出
                self.logger.info("PyQt6不可用，在控制台显示向量化结果")
                print(f"\n向量化结果:")
                print(f"生成了 {len(vectors)} 个向量，每个向量维度: {len(vectors[0])}")
                return
            
            # 创建标签页
            tab_widget = QTabWidget()
            
            # 添加向量标签页
            vector_tab = QWidget()
            vector_layout = QVBoxLayout(vector_tab)
            
            vector_label = QLabel(f"生成了 {len(vectors)} 个向量，每个向量维度: {len(vectors[0])}")
            vector_layout.addWidget(vector_label)
            
            # 显示向量内容（前10个元素）
            vector_text = QTextEdit()
            vector_text.setReadOnly(True)
            
            vector_content = ""
            for i, vector in enumerate(vectors):
                vector_preview = ", ".join([f"{v:.4f}" for v in vector[:10]])
                vector_content += f"向量 {i+1} (前10个元素): [{vector_preview}, ...]\n\n"
            
            vector_text.setText(vector_content)
            vector_layout.addWidget(vector_text)
            
            tab_widget.addTab(vector_tab, "向量")
            
            # 添加文本标签页
            text_tab = QWidget()
            text_layout = QVBoxLayout(text_tab)
            
            text_label = QLabel(f"向量化的文本 ({len(texts)} 个片段):")
            text_layout.addWidget(text_label)
            
            text_content = QTextEdit()
            text_content.setReadOnly(True)
            
            text_display = ""
            for i, text_chunk in enumerate(texts):
                preview = text_chunk[:200] + "..." if len(text_chunk) > 200 else text_chunk
                text_display += f"文本 {i+1}:\n{preview}\n\n"
            
            text_content.setText(text_display)
            text_layout.addWidget(text_content)
            
            tab_widget.addTab(text_tab, "文本")
            
            # 添加标签页到布局
            layout.addWidget(tab_widget)
            
            # 添加关闭按钮
            close_button = QPushButton("关闭")
            close_button.clicked.connect(dialog.accept)
            layout.addWidget(close_button)
            
            # 显示对话框
            dialog.exec()
            
        except Exception as e:
            self.logger.error(f"显示向量化结果时出错: {e}")
            self._show_error_message(f"显示结果时出错: {str(e)}")

    def _handle_dimension_mismatch(self, builder, vectors_array, expected_dim, actual_dim, index_path):
        """处理向量维度不匹配的问题"""
        import logging
        import traceback
        logger = logging.getLogger(__name__)
        try:
            if hasattr(self, 'logger') and self.logger:
                self.logger.warning(f"向量维度不匹配: 索引期望 {expected_dim}，实际 {actual_dim}")
            else:
                logger.warning(f"向量维度不匹配: 索引期望 {expected_dim}，实际 {actual_dim}")

            # 询问用户如何处理
            if _PYQT_AVAILABLE and 'QMessageBox' in _PYQT_WIDGETS:
                QMessageBox = _PYQT_WIDGETS['QMessageBox']
                reply = QMessageBox.question(
                    self,
                    "维度不匹配",
                    f"现有索引期望 {expected_dim} 维向量，但当前模型生成 {actual_dim} 维向量。\n\n"
                    f"选择处理方式：\n"
                    f"- 是(Yes): 创建新索引（推荐）\n"
                    f"- 否(No): 尝试调整向量维度（可能影响质量）",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No | QMessageBox.StandardButton.Cancel
                )

                if reply == QMessageBox.StandardButton.Yes:
                    # 创建新索引
                    return self._create_new_index_for_vectors(builder, vectors_array, index_path)
                elif reply == QMessageBox.StandardButton.No:
                    # 尝试调整向量维度
                    return self._adjust_vector_dimensions(vectors_array, expected_dim)
                else:
                    return None, False
            else:
                # 非GUI模式，默认创建新索引
                if hasattr(self, 'logger') and self.logger:
                    self.logger.info("非GUI模式，默认创建新索引")
                else:
                    logger.info("非GUI模式，默认创建新索引")
                return self._create_new_index_for_vectors(builder, vectors_array, index_path)

        except Exception as e:
            if hasattr(self, 'logger') and self.logger:
                self.logger.error(f"处理维度不匹配时出错: {e}")
            else:
                logger.error(f"处理维度不匹配时出错: {e}")
                traceback.print_exc()
            return None, False

    def _create_new_index_for_vectors(self, builder, vectors_array, original_index_path):
        """为新维度的向量创建新索引"""
        try:
            from pathlib import Path
            import time

            # 生成新的索引文件名
            original_path = Path(original_index_path)
            timestamp = int(time.time())
            new_index_name = f"{original_path.stem}_dim{vectors_array.shape[1]}_{timestamp}.idx"
            new_index_path = original_path.parent / new_index_name

            # 创建新索引
            builder.create_index(vectors_array.shape[1])
            self.logger.info(f"创建新索引，维度: {vectors_array.shape[1]}")

            return new_index_path, True

        except Exception as e:
            self.logger.error(f"创建新索引时出错: {e}")
            return None, False

    def _adjust_vector_dimensions(self, vectors_array, target_dim):
        """调整向量维度"""
        try:
            current_dim = vectors_array.shape[1]

            if current_dim > target_dim:
                # 降维：使用PCA
                from sklearn.decomposition import PCA
                pca = PCA(n_components=target_dim)
                adjusted_vectors = pca.fit_transform(vectors_array)
                self.logger.info(f"使用PCA将向量从 {current_dim} 维降到 {target_dim} 维")
            else:
                # 升维：使用零填充
                import numpy as np
                padding = np.zeros((vectors_array.shape[0], target_dim - current_dim))
                adjusted_vectors = np.hstack([vectors_array, padding])
                self.logger.info(f"使用零填充将向量从 {current_dim} 维升到 {target_dim} 维")

            return adjusted_vectors, True

        except Exception as e:
            self.logger.error(f"调整向量维度时出错: {e}")
            return vectors_array, False

    def _show_error_message(self, message):
        """显示错误消息"""
        try:
            if _PYQT_AVAILABLE and 'QMessageBox' in _PYQT_WIDGETS:
                QMessageBox = _PYQT_WIDGETS['QMessageBox']
                QMessageBox.critical(self, "错误", message)
            else:
                self.logger.error(message)
                print(f"错误: {message}")
        except Exception as e:
            self.logger.error(f"显示错误消息失败: {e}, 原消息: {message}")
            print(f"错误: {message}")

    def _show_warning(self, message: str):
        """显示警告消息"""
        try:
            if _PYQT_AVAILABLE and 'QMessageBox' in _PYQT_WIDGETS:
                QMessageBox = _PYQT_WIDGETS['QMessageBox']
                QMessageBox.warning(self, "警告", message)
            else:
                self.logger.warning(f"警告: {message}")
                print(f"警告: {message}")
        except Exception as e:
            self.logger.warning(f"显示警告消息失败: {e}, 消息: {message}")
            print(f"警告: {message}")


















































