
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
索引构建器模块
负责构建和管理向量索引
支持多种索引类型和量化技术
"""

import logging
import os
from typing import Dict, Any, List, Optional, Union, Tuple, Callable
import numpy as np
import faiss
import hnswlib
from pathlib import Path
import json
import pickle
from tqdm import tqdm
import time
import threading
from concurrent.futures import ThreadPoolExecutor
from enum import Enum
import tempfile
import shutil

class IndexType(Enum):
    """索引类型枚举"""
    FLAT = "flat"
    IVF = "ivf"
    HNSW = "hnsw"
    HYBRID = "hybrid"
    PQ = "pq"
    OPQ = "opq"
    SQ = "sq"
    IVFPQ = "ivfpq"
    IVFOPQ = "ivfopq"
    HNSW_PQ = "hnsw_pq"
    HNSW_SQ = "hnsw_sq"

class QuantizationType(Enum):
    """量化类型枚举"""
    NONE = "none"
    PQ = "pq"      # Product Quantization
    OPQ = "opq"    # Optimized Product Quantization
    SQ = "sq"      # Scalar Quantization
    SCANN = "scann"  # Google's ScaNN

class IndexBuilder:
    """索引构建器类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化索引构建器

        Args:
            config: 配置字典，包含索引相关的配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 获取配置参数
        self.indexing_config = config.get('indexing', {})

        # 基本索引配置
        index_type_str = self.indexing_config.get('index_type', 'faiss')
        self.index_type = self._parse_index_type(index_type_str)
        self.metric = self.indexing_config.get('metric', 'cosine')  # 'cosine', 'l2', 'ip'

        # FAISS参数
        self.n_lists = self.indexing_config.get('n_lists', 100)
        self.n_probes = self.indexing_config.get('n_probes', 10)

        # HNSW参数
        self.ef_construction = self.indexing_config.get('ef_construction', 200)
        self.ef_search = self.indexing_config.get('ef_search', 100)
        self.M = self.indexing_config.get('M', 16)

        # 混合索引参数
        self.hybrid_mode = self.indexing_config.get('hybrid_mode', 'hnsw_ivf')
        self.hybrid_n_lists = self.indexing_config.get('hybrid_n_lists', 50)
        self.hybrid_n_probes = self.indexing_config.get('hybrid_n_probes', 5)

        # 量化参数
        quantization_str = self.indexing_config.get('quantization', 'none')
        self.quantization = self._parse_quantization_type(quantization_str)
        self.pq_m = self.indexing_config.get('pq_m', 8)
        self.pq_bits = self.indexing_config.get('pq_bits', 8)
        self.opq_m = self.indexing_config.get('opq_m', 8)
        self.opq_bits = self.indexing_config.get('opq_bits', 8)

        # 增量索引设置
        self.incremental_index = self.indexing_config.get('incremental_index', True)
        self.rebuild_threshold = self.indexing_config.get('rebuild_threshold', 1000)
        self.update_count = 0

        # GPU加速
        self.use_gpu_index = self.indexing_config.get('use_gpu_index', False)
        if self.use_gpu_index and not faiss.get_num_gpus():
            self.logger.warning("未检测到GPU，将使用CPU索引")
            self.use_gpu_index = False

        # 索引状态
        self.index = None
        self.dimension = None
        self.total_vectors = 0
        self.is_trained = False
        self.last_update_time = None

        # 线程锁，用于并发控制
        self.index_lock = threading.Lock()

    def _parse_index_type(self, index_type_str: str) -> str:
        """解析索引类型字符串"""
        if index_type_str == 'faiss':
            return 'flat'  # 默认使用Flat索引
        elif index_type_str == 'hnsw':
            return 'hnsw'
        elif index_type_str == 'hybrid':
            return 'hybrid'
        elif index_type_str in ['flat', 'ivf', 'pq', 'opq', 'ivfpq', 'ivfopq', 'hnsw_pq', 'hnsw_sq']:
            return index_type_str
        else:
            self.logger.warning(f"未知的索引类型: {index_type_str}，使用默认的flat索引")
            return 'flat'

    def _parse_quantization_type(self, quantization_str: str) -> str:
        """解析量化类型字符串"""
        if quantization_str in ['none', 'pq', 'opq', 'sq', 'scann']:
            return quantization_str
        else:
            self.logger.warning(f"未知的量化类型: {quantization_str}，不使用量化")
            return 'none'

    def _create_faiss_index(self, dimension: int) -> faiss.Index:
        """
        创建FAISS索引

        Args:
            dimension: 向量维度

        Returns:
            faiss.Index: FAISS索引对象
        """
        try:
            # 确定度量类型
            if self.metric == 'cosine':
                metric_type = faiss.METRIC_INNER_PRODUCT
                base_index = faiss.IndexFlatIP(dimension)
            elif self.metric == 'l2':
                metric_type = faiss.METRIC_L2
                base_index = faiss.IndexFlatL2(dimension)
            else:  # inner product
                metric_type = faiss.METRIC_INNER_PRODUCT
                base_index = faiss.IndexFlatIP(dimension)

            # 根据索引类型创建不同的索引
            if self.index_type == 'flat':
                # 使用基础Flat索引
                index = base_index

            elif self.index_type == 'ivf':
                # 创建IVF索引
                index = faiss.IndexIVFFlat(
                    base_index,
                    dimension,
                    self.n_lists,
                    metric_type
                )
                # 设置搜索时探测的聚类数量
                index.nprobe = self.n_probes

            elif self.index_type == 'pq' or self.quantization == 'pq':
                # 创建PQ索引（Product Quantization）
                index = faiss.IndexPQ(
                    dimension,
                    self.pq_m,
                    self.pq_bits,
                    metric_type
                )

            elif self.index_type == 'opq' or self.quantization == 'opq':
                # 创建OPQ索引（Optimized Product Quantization）
                try:
                    # 尝试使用IndexOPQ
                    index = faiss.IndexOPQ(
                        dimension,
                        self.opq_m,
                        self.opq_bits,
                        metric_type
                    )
                except AttributeError:
                    # 如果不支持IndexOPQ，使用替代方案
                    self.logger.warning("FAISS不支持IndexOPQ，使用替代方案")
                    # 创建OPQ矩阵 + PQ索引的组合
                    opq_matrix = faiss.OPQMatrix(dimension, self.opq_m)
                    pq_index = faiss.IndexPQ(
                        dimension,
                        self.opq_m,
                        self.opq_bits,
                        metric_type
                    )
                    # 组合成预处理索引
                    index = faiss.IndexPreTransform(opq_matrix, pq_index)

            elif self.index_type == 'ivfpq' or (self.index_type == 'ivf' and self.quantization == 'pq'):
                # 创建IVFPQ索引（IVF + Product Quantization）
                index = faiss.IndexIVFPQ(
                    base_index,
                    dimension,
                    self.n_lists,
                    self.pq_m,
                    self.pq_bits,
                    metric_type
                )
                index.nprobe = self.n_probes

            elif self.index_type == 'ivfopq' or (self.index_type == 'ivf' and self.quantization == 'opq'):
                # 创建IVFOPQ索引（IVF + Optimized Product Quantization）
                opq_matrix = faiss.OPQMatrix(dimension, self.opq_m)
                index = faiss.IndexIVFPQ(
                    base_index,
                    dimension,
                    self.n_lists,
                    self.opq_m,
                    self.opq_bits,
                    metric_type
                )
                index = faiss.IndexPreTransform(opq_matrix, index)
                index.nprobe = self.n_probes

            elif self.index_type == 'hybrid':
                # 创建混合索引
                if self.hybrid_mode == 'hnsw_ivf':
                    # HNSW作为粗量化器 + IVF
                    quantizer = faiss.IndexHNSWFlat(dimension, self.M)
                    quantizer.hnsw.efConstruction = self.ef_construction
                    quantizer.hnsw.efSearch = self.ef_search
                    index = faiss.IndexIVFFlat(
                        quantizer,
                        dimension,
                        self.hybrid_n_lists,
                        metric_type
                    )
                    index.nprobe = self.hybrid_n_probes
                else:  # ivf_hnsw
                    # IVF + HNSW作为细量化器
                    index = faiss.IndexIVFFlat(
                        base_index,
                        dimension,
                        self.hybrid_n_lists,
                        metric_type
                    )
                    index.nprobe = self.hybrid_n_probes
                    # 注意：这种组合在FAISS中没有直接实现，这里是一个近似

            else:
                # 默认使用Flat索引
                self.logger.warning(f"未支持的FAISS索引类型: {self.index_type}，使用默认的Flat索引")
                index = base_index

            # 如果启用GPU加速
            if self.use_gpu_index:
                try:
                    # 获取GPU资源
                    res = faiss.StandardGpuResources()
                    # 将索引转移到GPU
                    index = faiss.index_cpu_to_gpu(res, 0, index)
                    self.logger.info("成功将索引转移到GPU")
                except Exception as gpu_err:
                    self.logger.warning(f"GPU加速失败: {gpu_err}，将使用CPU索引")

            return index

        except Exception as e:
            self.logger.error(f"创建FAISS索引时出错: {e}")
            raise

    def _create_hnsw_index(self, dimension: int) -> hnswlib.Index:
        """
        创建HNSW索引

        Args:
            dimension: 向量维度

        Returns:
            hnswlib.Index: HNSW索引对象
        """
        try:
            # 设置相似度度量
            if self.metric == 'cosine':
                # 使用内积空间，对于归一化向量等价于余弦相似度，但性能更好
                space = 'ip'
            elif self.metric == 'l2':
                space = 'l2'
            else:
                space = 'ip'  # inner product

            # 创建索引
            index = hnswlib.Index(space=space, dim=dimension)

            # 初始化索引 - 设置更大的容量以支持增量添加
            max_elements = max(self.total_vectors * 2, 10000)  # 预留更多空间，最少10000
            index.init_index(
                max_elements=max_elements,
                ef_construction=self.ef_construction,
                M=self.M
            )

            # 设置搜索参数
            index.set_ef(self.ef_search)

            # 如果是HNSW+PQ混合索引，需要在外部处理
            if self.index_type == 'hnsw_pq' or (self.index_type == 'hnsw' and self.quantization == 'pq'):
                self.logger.info("创建HNSW+PQ混合索引，PQ量化将在添加向量时应用")
                # 注意：hnswlib本身不支持PQ，这里只是标记，实际量化在添加向量时处理

            # 如果是HNSW+SQ混合索引，需要在外部处理
            if self.index_type == 'hnsw_sq' or (self.index_type == 'hnsw' and self.quantization == 'sq'):
                self.logger.info("创建HNSW+SQ混合索引，SQ量化将在添加向量时应用")
                # 注意：hnswlib本身不支持SQ，这里只是标记，实际量化在添加向量时处理

            return index

        except Exception as e:
            self.logger.error(f"创建HNSW索引时出错: {e}")
            raise

    def _create_hybrid_index(self, dimension: int):
        """
        创建混合索引

        Args:
            dimension: 向量维度

        Returns:
            混合索引对象
        """
        try:
            if self.hybrid_mode == 'hnsw_ivf':
                # 使用FAISS的HNSW+IVF实现
                return self._create_faiss_index(dimension)
            elif self.hybrid_mode == 'ivf_hnsw':
                # 使用FAISS的IVF+HNSW实现
                return self._create_faiss_index(dimension)
            else:
                self.logger.warning(f"未知的混合索引模式: {self.hybrid_mode}，使用默认的HNSW+IVF")
                return self._create_faiss_index(dimension)
        except Exception as e:
            self.logger.error(f"创建混合索引时出错: {e}")
            raise

    def create_index(self, dimension: int) -> None:
        """
        创建新索引

        Args:
            dimension: 向量维度
        """
        try:
            self.dimension = dimension

            with self.index_lock:
                # 根据索引类型创建不同的索引
                if self.index_type in ['flat', 'ivf', 'pq', 'opq', 'ivfpq', 'ivfopq']:
                    self.index = self._create_faiss_index(dimension)
                elif self.index_type == 'hnsw':
                    self.index = self._create_hnsw_index(dimension)
                elif self.index_type == 'hybrid':
                    self.index = self._create_hybrid_index(dimension)
                elif self.index_type in ['hnsw_pq', 'hnsw_sq']:
                    # 创建基础HNSW索引，量化在添加向量时处理
                    self.index = self._create_hnsw_index(dimension)
                else:
                    self.logger.warning(f"未知的索引类型: {self.index_type}，使用默认的Flat索引")
                    self.index_type = 'flat'
                    self.index = self._create_faiss_index(dimension)

                self.is_trained = False
                self.total_vectors = 0
                self.last_update_time = time.time()
                self.update_count = 0

            self.logger.info(f"创建了新的{self.index_type}索引，维度: {dimension}")

        except Exception as e:
            self.logger.error(f"创建索引时出错: {e}")
            raise

    def _adapt_vector_dimensions(self, vectors: np.ndarray, target_dimension: int) -> np.ndarray:
        """适配向量维度到目标维度"""
        current_dimension = vectors.shape[1]
        if current_dimension == target_dimension:
            return vectors
        try:
            if current_dimension > target_dimension:
                # 降维：使用PCA或简单截断
                self.logger.info(f"降维从 {current_dimension} 到 {target_dimension}")
                try:
                    from sklearn.decomposition import PCA
                    pca = PCA(n_components=target_dimension)
                    adapted_vectors = pca.fit_transform(vectors)
                    self.logger.info("使用PCA进行降维")
                except ImportError:
                    adapted_vectors = vectors[:, :target_dimension]
                    self.logger.warning("sklearn不可用，使用简单截断进行降维")
            else:
                # 升维：使用零填充
                self.logger.info(f"升维从 {current_dimension} 到 {target_dimension}")
                padding_size = target_dimension - current_dimension
                padding = np.zeros((vectors.shape[0], padding_size), dtype=vectors.dtype)
                adapted_vectors = np.concatenate([vectors, padding], axis=1)
            return adapted_vectors.astype(np.float32)
        except Exception as e:
            self.logger.error(f"适配向量维度时出错: {e}")
            # 如果适配失败，返回原向量并抛出异常
            raise ValueError(f"无法适配向量维度从 {current_dimension} 到 {target_dimension}: {e}")

    def _apply_quantization(self, vectors: np.ndarray) -> np.ndarray:
        """
        应用量化处理

        Args:
            vectors: 输入向量矩阵

        Returns:
            np.ndarray: 量化后的向量矩阵
        """
        try:
            if self.quantization == 'none':
                return vectors

            elif self.quantization == 'pq':
                # 使用Product Quantization
                if not hasattr(self, 'pq_quantizer'):
                    # 创建PQ量化器
                    self.pq_quantizer = faiss.ProductQuantizer(
                        self.dimension, self.pq_m, self.pq_bits
                    )
                    # 训练量化器
                    self.pq_quantizer.train(vectors)

                # 应用量化
                try:
                    codes = np.zeros((vectors.shape[0], self.pq_quantizer.code_size), dtype='uint8')

                    # 尝试不同的参数顺序（不同版本的FAISS可能有不同的API）
                    try:
                        self.pq_quantizer.compute_codes(vectors, codes)
                    except TypeError:
                        # 如果上面的调用失败，尝试反转参数顺序
                        self.pq_quantizer.compute_codes(codes, vectors)

                    # 重建向量
                    quantized_vectors = np.zeros_like(vectors)

                    try:
                        self.pq_quantizer.decode(codes, quantized_vectors)
                    except TypeError:
                        # 如果上面的调用失败，尝试反转参数顺序
                        self.pq_quantizer.decode(quantized_vectors, codes)

                except Exception as e:
                    self.logger.warning(f"PQ量化失败: {e}，使用原始向量")
                    quantized_vectors = vectors.copy()

                return quantized_vectors

            elif self.quantization == 'opq':
                # 使用Optimized Product Quantization
                if not hasattr(self, 'opq_quantizer'):
                    # 创建OPQ量化器
                    self.opq_matrix = faiss.OPQMatrix(self.dimension, self.opq_m)
                    self.pq_quantizer = faiss.ProductQuantizer(
                        self.dimension, self.opq_m, self.opq_bits
                    )
                    # 训练量化器
                    self.opq_matrix.train(vectors)
                    transformed_vectors = np.zeros_like(vectors)
                    self.opq_matrix.apply_noalloc(vectors, transformed_vectors)
                    self.pq_quantizer.train(transformed_vectors)

                try:
                    # 应用变换
                    transformed_vectors = np.zeros_like(vectors)
                    try:
                        self.opq_matrix.apply_noalloc(vectors, transformed_vectors)
                    except (AttributeError, TypeError):
                        # 如果apply_noalloc不可用或参数不匹配，尝试其他方法
                        self.logger.warning("OPQ矩阵apply_noalloc方法失败，尝试替代方法")
                        transformed_vectors = vectors.copy()  # 使用原始向量作为后备

                    # 应用量化
                    codes = np.zeros((vectors.shape[0], self.pq_quantizer.code_size), dtype='uint8')

                    # 尝试不同的参数顺序
                    try:
                        self.pq_quantizer.compute_codes(transformed_vectors, codes)
                    except TypeError:
                        # 如果上面的调用失败，尝试反转参数顺序
                        self.pq_quantizer.compute_codes(codes, transformed_vectors)

                    # 重建向量
                    quantized_transformed = np.zeros_like(transformed_vectors)

                    try:
                        self.pq_quantizer.decode(codes, quantized_transformed)
                    except TypeError:
                        # 如果上面的调用失败，尝试反转参数顺序
                        self.pq_quantizer.decode(quantized_transformed, codes)
                except Exception as e:
                    self.logger.warning(f"OPQ量化失败: {e}，使用原始向量")
                    return vectors.copy()

                # 逆变换（近似）
                quantized_vectors = np.zeros_like(vectors)
                for i in range(vectors.shape[0]):
                    quantized_vectors[i] = np.dot(
                        quantized_transformed[i],
                        self.opq_matrix.A.T
                    )

                return quantized_vectors

            elif self.quantization == 'sq':
                # 使用Scalar Quantization (简单实现)
                if not hasattr(self, 'sq_min') or not hasattr(self, 'sq_max'):
                    self.sq_min = np.min(vectors, axis=0)
                    self.sq_max = np.max(vectors, axis=0)
                    self.sq_scale = (self.sq_max - self.sq_min) / 255.0
                    self.sq_scale[self.sq_scale == 0] = 1.0  # 避免除零

                # 量化到uint8
                quantized_uint8 = np.round(
                    (vectors - self.sq_min) / self.sq_scale
                ).astype(np.uint8)

                # 反量化
                quantized_vectors = (quantized_uint8.astype(np.float32) * self.sq_scale) + self.sq_min

                return quantized_vectors

            elif self.quantization == 'scann':
                # ScaNN量化 (简化实现)
                self.logger.warning("ScaNN量化尚未完全实现，使用PQ量化代替")
                return self._apply_quantization(vectors)  # 使用PQ代替

            else:
                self.logger.warning(f"未知的量化类型: {self.quantization}，不应用量化")
                return vectors

        except Exception as e:
            self.logger.error(f"应用量化时出错: {e}，返回原始向量")
            return vectors

    def add_vectors(self, vectors: np.ndarray, ids: Optional[np.ndarray] = None,
                   metadata_manager = None) -> bool:
        """
        向索引中添加向量

        Args:
            vectors: 向量矩阵
            ids: 向量ID数组（可选）
            metadata_manager: 元数据管理器实例（可选），用于同步索引和元数据

        Returns:
            bool: 是否成功添加
        """
        try:
            # 处理向量维度不匹配问题
            if vectors.shape[1] != self.dimension:
                self.logger.warning(f"向量维度不匹配: 期望 {self.dimension}，实际 {vectors.shape[1]}，尝试进行维度适配")
                vectors = self._adapt_vector_dimensions(vectors, self.dimension)

            # 应用量化（如果启用）
            if self.quantization != 'none' and self.index_type not in ['pq', 'opq', 'ivfpq', 'ivfopq']:
                # 对于已经内置量化的索引类型，不需要额外量化
                vectors = self._apply_quantization(vectors)

            # 如果提供了元数据管理器，验证所有ID都有对应的元数据
            if metadata_manager is not None and ids is not None:
                valid_ids = []
                valid_vectors = []

                # 获取所有元数据ID
                all_metadata = metadata_manager.get_all_metadata()
                metadata_ids = set(all_metadata.keys())

                # 验证每个ID
                for i, id_val in enumerate(ids):
                    if int(id_val) in metadata_ids:
                        valid_ids.append(id_val)
                        valid_vectors.append(vectors[i])
                    else:
                        self.logger.warning(f"ID {id_val} 没有对应的元数据，跳过此向量")

                # 更新向量和ID数组
                if len(valid_ids) < len(ids):
                    self.logger.warning(f"从 {len(ids)} 个向量中过滤出 {len(valid_ids)} 个有效向量")
                    if len(valid_ids) == 0:
                        self.logger.error("没有有效的向量可添加")
                        return False

                    vectors = np.vstack(valid_vectors)
                    ids = np.array(valid_ids)

            with self.index_lock:
                # 根据索引类型添加向量
                if self.index_type in ['flat', 'ivf', 'pq', 'opq', 'ivfpq', 'ivfopq', 'hybrid']:
                    # FAISS索引
                    if not self.is_trained and hasattr(self.index, 'train'):
                        self.index.train(vectors)
                        self.is_trained = True

                    if ids is not None:
                        self.index.add_with_ids(vectors, ids)
                    else:
                        self.index.add(vectors)

                elif self.index_type in ['hnsw', 'hnsw_pq', 'hnsw_sq']:
                    # HNSW索引
                    if ids is None:
                        ids = np.arange(self.total_vectors, self.total_vectors + len(vectors))
                    self.index.add_items(vectors, ids)

                else:
                    raise ValueError(f"未知的索引类型: {self.index_type}")

                # 更新索引状态
                self.total_vectors += len(vectors)
                self.last_update_time = time.time()
                self.update_count += 1

                # 检查是否需要重建索引（增量索引优化）
                if self.incremental_index and self.update_count >= self.rebuild_threshold:
                    self.logger.info(f"达到重建阈值 ({self.rebuild_threshold})，准备优化索引")
                    self.optimize_index()
                    self.update_count = 0

            self.logger.info(f"添加了 {len(vectors)} 个向量到索引")
            return True

        except Exception as e:
            self.logger.error(f"添加向量时出错: {e}")
            return False

    def batch_add_vectors(self, vectors: np.ndarray, batch_size: int = 1000,
                         ids: Optional[np.ndarray] = None, metadata_manager = None) -> bool:
        """
        批量添加向量到索引

        Args:
            vectors: 向量矩阵
            batch_size: 批处理大小
            ids: 向量ID数组（可选）
            metadata_manager: 元数据管理器实例（可选），用于同步索引和元数据

        Returns:
            bool: 是否成功添加所有批次
        """
        try:
            total_vectors = len(vectors)
            for i in tqdm(range(0, total_vectors, batch_size), desc="Adding vectors"):
                end_idx = min(i + batch_size, total_vectors)
                batch_vectors = vectors[i:end_idx]

                if ids is not None:
                    batch_ids = ids[i:end_idx]
                else:
                    batch_ids = None

                if not self.add_vectors(batch_vectors, batch_ids, metadata_manager):
                    return False

            return True

        except Exception as e:
            self.logger.error(f"批量添加向量时出错: {e}")
            return False

    def remove_vectors(self, ids: np.ndarray) -> bool:
        """
        从索引中移除向量

        Args:
            ids: 要移除的向量ID数组

        Returns:
            bool: 是否成功移除
        """
        try:
            with self.index_lock:
                if self.index_type == 'faiss':
                    if hasattr(self.index, 'remove_ids'):
                        self.index.remove_ids(ids)
                    else:
                        self.logger.warning("当前FAISS索引类型不支持移除操作")
                        return False
                else:  # hnsw
                    for id_ in ids:
                        self.index.mark_deleted(id_)

                self.total_vectors -= len(ids)
                self.last_update_time = time.time()

            self.logger.info(f"从索引中移除了 {len(ids)} 个向量")
            return True

        except Exception as e:
            self.logger.error(f"移除向量时出错: {e}")
            return False

    def save_index(self, file_path: Path) -> bool:
        """
        保存索引到文件

        Args:
            file_path: 保存路径

        Returns:
            bool: 是否成功保存
        """
        try:
            # 创建目录
            file_path.parent.mkdir(parents=True, exist_ok=True)

            with self.index_lock:
                # 根据索引类型保存索引
                if self.index_type in ['flat', 'ivf', 'pq', 'opq', 'ivfpq', 'ivfopq', 'hybrid']:
                    # FAISS索引使用faiss.write_index保存
                    faiss.write_index(self.index, str(file_path))
                elif self.index_type in ['hnsw', 'hnsw_pq', 'hnsw_sq']:
                    # HNSW索引使用save_index方法保存
                    self.index.save_index(str(file_path))
                else:
                    # 未知索引类型，尝试通用方法
                    self.logger.warning(f"未知的索引类型: {self.index_type}，尝试通用保存方法")
                    if hasattr(self.index, 'save_index'):
                        self.index.save_index(str(file_path))
                    elif hasattr(faiss, 'write_index'):
                        faiss.write_index(self.index, str(file_path))
                    else:
                        raise ValueError(f"无法保存索引类型: {self.index_type}")

                # 保存元数据
                metadata = {
                    'dimension': self.dimension,
                    'total_vectors': self.total_vectors,
                    'is_trained': self.is_trained,
                    'last_update_time': self.last_update_time,
                    'index_type': self.index_type,
                    'metric': self.metric,
                    'quantization': self.quantization,
                    'config': self.indexing_config
                }

                # 以二进制格式保存元数据
                with open(file_path.with_suffix('.meta'), 'wb') as f:
                    pickle.dump(metadata, f)

            self.logger.info(f"索引已保存到: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"保存索引时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def load_index(self, file_path: Union[str, Path]) -> bool:
        """
        加载索引文件

        Args:
            file_path: 索引文件路径

        Returns:
            bool: 是否成功加载
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                self.logger.error(f"索引文件不存在: {file_path}")
                return False

            # 尝试从元数据文件中获取索引类型
            meta_path = file_path.with_suffix('.meta')
            if meta_path.exists():
                try:
                    # 首先尝试以二进制方式读取（pickle格式）
                    try:
                        with open(meta_path, 'rb') as f:
                            metadata = pickle.load(f)
                            self.index_type = metadata.get('index_type', 'flat')
                            self.dimension = metadata.get('dimension', 0)
                            self.total_vectors = metadata.get('total_vectors', 0)
                            self.is_trained = metadata.get('is_trained', False)
                            self.metric = metadata.get('metric', 'cosine')
                            self.quantization = metadata.get('quantization', 'none')
                            self.indexing_config = metadata.get('config', {})
                            self.logger.info(f"从二进制元数据加载索引信息: 类型={self.index_type}, 维度={self.dimension}")
                    except Exception as e:
                        # 如果二进制读取失败，尝试以文本方式读取（JSON格式）
                        self.logger.debug(f"以二进制方式读取元数据失败: {e}，尝试以文本方式读取")
                        with open(meta_path, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                            self.index_type = metadata.get('index_type', 'flat')
                            self.dimension = metadata.get('dimension', 0)
                            self.total_vectors = metadata.get('total_vectors', 0)
                            self.is_trained = metadata.get('is_trained', False)
                            self.metric = metadata.get('metric', 'cosine')
                            self.quantization = metadata.get('quantization', 'none')
                            self.indexing_config = metadata.get('config', {})
                            self.logger.info(f"从JSON元数据加载索引信息: 类型={self.index_type}, 维度={self.dimension}")
                except Exception as e:
                    self.logger.warning(f"读取索引元数据时出错: {e}")
                    # 如果无法从元数据获取，尝试从文件名推断
                    if '_flat_' in file_path.name:
                        self.index_type = 'flat'
                    elif '_ivf_' in file_path.name:
                        self.index_type = 'ivf'
                    elif '_hnsw_' in file_path.name:
                        self.index_type = 'hnsw'
                    elif '_pq_' in file_path.name:
                        self.index_type = 'pq'
                    elif '_opq_' in file_path.name:
                        self.index_type = 'opq'
                    elif '_hybrid_' in file_path.name:
                        self.index_type = 'hybrid'
                    else:
                        # 如果索引文件存在，我们假设它是已训练的
                        self.is_trained = True
                
            # 加载索引文件
            try:
                if self.index_type in ['flat', 'ivf', 'ivfpq', 'ivfopq', 'pq', 'opq']:
                    # 使用FAISS加载这些索引类型
                    self.index = faiss.read_index(str(file_path))
                    self.dimension = self.index.d  # 从索引中获取维度
                    if hasattr(self.index, 'ntotal'):
                        self.total_vectors = self.index.ntotal
                
                elif self.index_type == 'hnsw':
                    # 加载HNSW索引
                    space = 'ip' if self.metric == 'cosine' else self.metric
                    self.index = hnswlib.Index(space=space, dim=self.dimension or 384)
                    self.index.load_index(str(file_path))
                    self.total_vectors = self.index.get_current_count()
                
                elif self.index_type == 'hybrid':
                    # 混合索引 - 尝试使用FAISS加载
                    try:
                        self.index = faiss.read_index(str(file_path))
                        self.dimension = self.index.d
                        if hasattr(self.index, 'ntotal'):
                            self.total_vectors = self.index.ntotal
                    except Exception as e:
                        self.logger.warning(f"使用FAISS加载混合索引失败: {e}，尝试其他方法")
                        # 尝试使用自定义方法加载混合索引
                        with open(file_path, 'rb') as f:
                            index_data = pickle.load(f)
                            self.index = index_data.get('index')
                            self.dimension = index_data.get('dimension', 384)
                            self.total_vectors = index_data.get('total_vectors', 0)
            
                else:
                    self.logger.error(f"不支持的索引类型: {self.index_type}")
                    return False
            
                self.logger.info(f"成功加载索引: {file_path}")
                return True
        
            except Exception as e:
                self.logger.error(f"加载索引文件时出错: {e}")
                import traceback
                self.logger.error(traceback.format_exc())
                return False
        
        except Exception as e:
            self.logger.error(f"加载索引时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def optimize_index(self) -> bool:
        """
        优化索引性能

        Returns:
            bool: 是否成功优化
        """
        try:
            with self.index_lock:
                # 根据索引类型进行不同的优化
                if self.index_type in ['flat', 'ivf', 'pq', 'opq', 'ivfpq', 'ivfopq', 'hybrid']:
                    # FAISS索引优化
                    if isinstance(self.index, faiss.IndexIVF):
                        # 调整探测数量
                        optimal_nprobe = max(1, min(self.n_lists // 10, 100))
                        self.index.nprobe = optimal_nprobe
                        self.logger.info(f"调整nprobe为: {optimal_nprobe}")

                        # 尝试重新平衡IVF索引（如果支持）
                        try:
                            self.logger.info("尝试重新平衡IVF聚类")
                            if hasattr(faiss, 'rebalance_clustering'):
                                faiss.rebalance_clustering(self.index)
                            else:
                                self.logger.warning("当前FAISS版本不支持rebalance_clustering")
                        except Exception as e:
                            self.logger.warning(f"重新平衡IVF聚类失败: {e}")

                    # 对于混合索引的特殊处理
                    if self.index_type == 'hybrid' and hasattr(self.index, 'quantizer') and hasattr(self.index.quantizer, 'hnsw'):
                        # 优化HNSW量化器
                        optimal_ef = min(self.ef_construction, max(100, self.total_vectors // 100))
                        self.index.quantizer.hnsw.efSearch = optimal_ef
                        self.logger.info(f"调整HNSW量化器efSearch为: {optimal_ef}")

                elif self.index_type in ['hnsw', 'hnsw_pq', 'hnsw_sq']:
                    # HNSW索引优化
                    optimal_ef = min(self.ef_construction, max(100, self.total_vectors // 100))
                    self.index.set_ef(optimal_ef)
                    self.logger.info(f"调整HNSW ef为: {optimal_ef}")

                    # 如果向量数量增长很多，考虑重建索引
                    current_max = self.index.get_max_elements()
                    if self.total_vectors > current_max * 0.9:  # 如果使用了超过90%的容量
                        new_max = max(current_max * 2, self.total_vectors * 1.5)
                        self.logger.info(f"调整HNSW最大元素数量: {current_max} -> {new_max}")

                        # 创建临时索引
                        temp_index = hnswlib.Index(space=self.index.space, dim=self.dimension)
                        temp_index.init_index(
                            max_elements=int(new_max),
                            ef_construction=self.ef_construction,
                            M=self.M
                        )
                        temp_index.set_ef(self.ef_search)

                        # 复制所有向量
                        all_ids = []
                        all_vectors = []

                        try:
                            # 尝试使用element_exists方法（如果存在）
                            if hasattr(self.index, 'element_exists'):
                                for i in range(self.total_vectors):
                                    if not self.index.element_exists(i):
                                        continue
                                    all_ids.append(i)
                                    all_vectors.append(self.index.get_items([i])[0])
                            else:
                                # 如果不存在element_exists方法，尝试使用get_items并捕获异常
                                self.logger.warning("HNSW索引不支持element_exists方法，使用替代方案")
                                for i in range(self.total_vectors):
                                    try:
                                        vector = self.index.get_items([i])[0]
                                        all_ids.append(i)
                                        all_vectors.append(vector)
                                    except Exception:
                                        # 如果获取失败，说明元素不存在或已删除
                                        continue
                        except Exception as e:
                            self.logger.warning(f"获取HNSW索引元素时出错: {e}")
                            # 如果无法获取元素，创建一个空的新索引
                            self.logger.warning("无法获取现有元素，将创建空索引")

                        if all_ids:
                            # 添加到新索引
                            temp_index.add_items(np.array(all_vectors), np.array(all_ids))
                            # 替换旧索引
                            self.index = temp_index

                self.last_update_time = time.time()
                self.update_count = 0

            self.logger.info("索引已优化")
            return True

        except Exception as e:
            self.logger.error(f"优化索引时出错: {e}")
            return False

    def search(self, query_vector: Union[np.ndarray, List[float]], k: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """
        搜索最近邻向量

        Args:
            query_vector: 查询向量，可以是numpy数组或浮点数列表
            k: 返回的最近邻数量

        Returns:
            Tuple[np.ndarray, np.ndarray]: 距离和索引数组的元组
        """
        try:
            # 确保查询向量是正确的形状和类型
            if isinstance(query_vector, list):
                query_vector = np.array(query_vector, dtype=np.float32)

            if query_vector.ndim == 1:
                query_vector = query_vector.reshape(1, -1)

            if query_vector.shape[1] != self.dimension:
                self.logger.warning(f"查询向量维度不匹配: 期望 {self.dimension}，实际 {query_vector.shape[1]}，尝试进行维度适配")
                query_vector = self._adapt_vector_dimensions(query_vector, self.dimension)

            # 确保索引中有向量
            if self.total_vectors == 0:
                self.logger.warning("索引中没有向量，无法执行搜索")
                # 返回有效的空结果
                empty_distances = np.zeros((1, k), dtype=np.float32)
                empty_indices = np.full((1, k), -1, dtype=np.int64)
                return empty_distances, empty_indices

            with self.index_lock:
                # 根据索引类型执行搜索
                if self.index_type in ['flat', 'ivf', 'pq', 'opq', 'ivfpq', 'ivfopq', 'hybrid']:
                    # FAISS索引搜索
                    distances, indices = self.index.search(query_vector, min(k, self.total_vectors))

                    # 处理无效的结果
                    if distances.size > 0:
                        # 将无效的距离值替换为0
                        invalid_mask = np.isnan(distances) | np.isinf(distances) | (distances < -1.0)
                        if np.any(invalid_mask):
                            self.logger.warning(f"检测到 {np.sum(invalid_mask)} 个无效的距离值，将被替换")
                            distances[invalid_mask] = 0.0
                            indices[invalid_mask] = -1

                    # 如果结果数量少于k，填充剩余部分
                    if distances.shape[1] < k:
                        pad_width = ((0, 0), (0, k - distances.shape[1]))
                        distances = np.pad(distances, pad_width, 'constant', constant_values=0)
                        indices = np.pad(indices, pad_width, 'constant', constant_values=-1)

                elif self.index_type in ['hnsw', 'hnsw_pq', 'hnsw_sq']:
                    # HNSW索引搜索
                    try:
                        indices, distances = self.index.knn_query(query_vector, k=min(k, self.total_vectors))

                        # HNSW返回的是相似度，如果使用距离度量，需要转换
                        if self.metric == 'cosine' or self.metric == 'ip':
                            # 对于内积或余弦相似度，较大的值表示更相似，转换为距离
                            distances = 1 - distances

                        # 处理无效的结果
                        invalid_mask = np.isnan(distances) | np.isinf(distances) | (distances < -1.0)
                        if np.any(invalid_mask):
                            self.logger.warning(f"检测到 {np.sum(invalid_mask)} 个无效的距离值，将被替换")
                            distances[invalid_mask] = 0.0
                            indices[invalid_mask] = -1

                        # 如果结果数量少于k，填充剩余部分
                        if distances.shape[1] < k:
                            pad_width = ((0, 0), (0, k - distances.shape[1]))
                            distances = np.pad(distances, pad_width, 'constant', constant_values=0)
                            indices = np.pad(indices, pad_width, 'constant', constant_values=-1)

                    except Exception as hnsw_err:
                        self.logger.error(f"HNSW搜索出错: {hnsw_err}，返回空结果")
                        distances = np.zeros((1, k), dtype=np.float32)
                        indices = np.full((1, k), -1, dtype=np.int64)

                else:
                    raise ValueError(f"未知的索引类型: {self.index_type}")

                return distances, indices

        except Exception as e:
            self.logger.error(f"搜索向量时出错: {e}")
            # 返回有效的空结果
            empty_distances = np.zeros((1, k), dtype=np.float32)
            empty_indices = np.full((1, k), -1, dtype=np.int64)
            return empty_distances, empty_indices

    def get_index_stats(self) -> Dict[str, Any]:
        """
        获取索引统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        try:
            with self.index_lock:
                # 基本统计信息
                stats = {
                    'index_type': self.index_type,
                    'dimension': self.dimension,
                    'total_vectors': self.total_vectors,
                    'is_trained': self.is_trained,
                    'last_update_time': self.last_update_time,
                    'metric': self.metric,
                    'quantization': self.quantization,
                    'update_count': self.update_count,
                    'incremental_index': self.incremental_index,
                    'rebuild_threshold': self.rebuild_threshold
                }

                # 根据索引类型添加特定统计信息
                if self.index_type in ['flat', 'ivf', 'pq', 'opq', 'ivfpq', 'ivfopq', 'hybrid']:
                    # FAISS索引
                    stats['faiss_index_type'] = type(self.index).__name__

                    # IVF相关统计
                    if isinstance(self.index, faiss.IndexIVF):
                        stats.update({
                            'n_lists': self.index.nlist,
                            'n_probes': self.index.nprobe
                        })

                    # PQ相关统计
                    if hasattr(self, 'pq_quantizer'):
                        stats.update({
                            'pq_m': self.pq_m,
                            'pq_bits': self.pq_bits,
                            'pq_code_size': self.pq_quantizer.code_size
                        })

                    # OPQ相关统计
                    if hasattr(self, 'opq_matrix'):
                        stats.update({
                            'opq_m': self.opq_m,
                            'opq_bits': self.opq_bits
                        })

                    # 混合索引相关统计
                    if self.index_type == 'hybrid':
                        stats.update({
                            'hybrid_mode': self.hybrid_mode,
                            'hybrid_n_lists': self.hybrid_n_lists,
                            'hybrid_n_probes': self.hybrid_n_probes
                        })

                    # GPU相关统计
                    stats['use_gpu_index'] = self.use_gpu_index
                    if self.use_gpu_index:
                        stats['gpu_resources'] = faiss.get_num_gpus()

                elif self.index_type in ['hnsw', 'hnsw_pq', 'hnsw_sq']:
                    # HNSW索引
                    stats.update({
                        'ef_construction': self.index.ef_construction,
                        'ef_search': self.index.ef,
                        'M': self.index.M,
                        'max_elements': self.index.get_max_elements(),
                        'current_count': self.index.get_current_count()
                    })

                    # 量化相关统计
                    if self.index_type in ['hnsw_pq', 'hnsw_sq'] or self.quantization != 'none':
                        if hasattr(self, 'pq_quantizer'):
                            stats.update({
                                'pq_m': self.pq_m,
                                'pq_bits': self.pq_bits
                            })
                        if hasattr(self, 'sq_min') and hasattr(self, 'sq_max'):
                            stats.update({
                                'sq_min_avg': float(np.mean(self.sq_min)),
                                'sq_max_avg': float(np.mean(self.sq_max))
                            })

                # 内存使用估计
                if self.index_type in ['flat', 'ivf']:
                    # Flat索引: 每个向量使用4字节/浮点数
                    mem_usage = self.total_vectors * self.dimension * 4
                elif self.index_type in ['pq', 'ivfpq']:
                    # PQ索引: 每个向量使用m字节
                    m_value = self.pq_m if hasattr(self, 'pq_m') else 8
                    mem_usage = self.total_vectors * m_value
                elif self.index_type in ['hnsw', 'hnsw_pq', 'hnsw_sq']:
                    # HNSW索引: 每个向量使用4字节/浮点数 + 链接开销
                    mem_usage = self.total_vectors * (self.dimension * 4 + self.M * 8)
                else:
                    mem_usage = 0

                stats['estimated_memory_usage'] = mem_usage
                stats['estimated_memory_usage_mb'] = mem_usage / (1024 * 1024)

                return stats

        except Exception as e:
            self.logger.error(f"获取索引统计信息时出错: {e}")
            return {}

if __name__ == "__main__":
    # 测试代码
    test_config = {
        'indexing': {
            'index_type': 'hybrid',  # 测试混合索引
            'metric': 'cosine',
            'n_lists': 100,
            'n_probes': 10,
            'ef_construction': 200,
            'ef_search': 100,
            'M': 16,
            'hybrid_mode': 'hnsw_ivf',
            'hybrid_n_lists': 50,
            'hybrid_n_probes': 5,
            'quantization': 'pq',  # 测试PQ量化
            'pq_m': 8,
            'pq_bits': 8,
            'incremental_index': True,
            'rebuild_threshold': 500
        }
    }

    print("创建索引构建器...")
    builder = IndexBuilder(test_config)

    # 测试创建索引
    dimension = 384
    print(f"创建{dimension}维度的索引...")
    builder.create_index(dimension)

    # 生成测试向量
    n_vectors = 1000
    print(f"生成{n_vectors}个测试向量...")
    test_vectors = np.random.rand(n_vectors, dimension).astype('float32')

    # 测试添加向量
    print("批量添加向量...")
    builder.batch_add_vectors(test_vectors, batch_size=100)

    # 测试优化索引
    print("优化索引...")
    builder.optimize_index()

    # 测试保存和加载索引
    index_path = Path('test_index.idx')
    print(f"保存索引到 {index_path}...")
    builder.save_index(index_path)

    print(f"从 {index_path} 加载索引...")
    builder.load_index(index_path)

    # 打印索引统计信息
    print("\n索引统计信息:")
    stats = builder.get_index_stats()
    print(json.dumps(stats, indent=2, default=str))

    # 测试不同的索引类型
    index_types = ['flat', 'ivf', 'pq', 'hnsw', 'ivfpq', 'hybrid']
    quantization_types = ['none', 'pq', 'opq', 'sq']

    print("\n测试不同的索引类型和量化方法:")
    for idx_type in index_types:
        for quant_type in quantization_types:
            # 跳过不兼容的组合
            if (idx_type in ['pq', 'ivfpq'] and quant_type != 'none'):
                continue

            test_config['indexing']['index_type'] = idx_type
            test_config['indexing']['quantization'] = quant_type

            try:
                print(f"\n测试 {idx_type} 索引 + {quant_type} 量化...")
                test_builder = IndexBuilder(test_config)
                test_builder.create_index(dimension)

                # 添加少量向量进行测试
                test_builder.add_vectors(test_vectors[:100])

                # 打印基本信息
                stats = test_builder.get_index_stats()
                print(f"  向量数量: {stats['total_vectors']}")
                print(f"  内存使用: {stats['estimated_memory_usage_mb']:.2f} MB")

            except Exception as e:
                print(f"  错误: {e}")

    # 清理测试文件
    print("\n清理测试文件...")
    if index_path.exists():
        index_path.unlink()
    if index_path.with_suffix('.meta').exists():
        index_path.with_suffix('.meta').unlink()

    print("测试完成!")
