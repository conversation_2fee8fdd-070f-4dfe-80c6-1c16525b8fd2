
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
元数据管理器模块
负责向量元数据的管理和检索
"""

import logging
from typing import Dict, Any, List, Optional, Union, Set, Tuple
from pathlib import Path
import json
import sqlite3
import threading
from datetime import datetime
import pickle
from tqdm import tqdm
import hashlib

class MetadataManager:
    """元数据管理器类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化元数据管理器

        Args:
            config: 配置字典，包含存储相关的配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 获取配置参数
        self.storage_config = config.get('storage', {})
        self.metadata_format = self.storage_config.get('metadata_format', 'json')
        self.cache_enabled = self.storage_config.get('cache_enabled', True)

        # 存储路径
        self.base_dir = Path(self.storage_config.get('base_dir', 'data/vectors'))
        self.metadata_dir = self.base_dir / 'metadata'
        self.metadata_dir.mkdir(parents=True, exist_ok=True)

        # 记录路径信息
        self.logger.info(f"元数据管理器初始化 - 基础目录: {self.base_dir}")
        self.logger.info(f"元数据管理器初始化 - 元数据目录: {self.metadata_dir}")

        # 数据库连接
        self.db_path = self.metadata_dir / 'metadata.db'
        self.logger.info(f"元数据管理器初始化 - 数据库路径: {self.db_path}")
        self.connection = None
        self._initialize_database()

        # 缓存
        self.cache: Dict[int, Dict[str, Any]] = {}
        self.cache_lock = threading.Lock()

        # 版本控制
        self.version_file = self.metadata_dir / 'version.json'
        self.current_version = self._load_version()

    def _initialize_database(self) -> None:
        """初始化SQLite数据库"""
        try:
            self.connection = sqlite3.connect(str(self.db_path), check_same_thread=False)
            cursor = self.connection.cursor()

            # 创建元数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS metadata (
                    id INTEGER PRIMARY KEY,
                    data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    version INTEGER DEFAULT 1,
                    checksum TEXT
                )
            """)

            # 创建索引表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS metadata_index (
                    id INTEGER,
                    key TEXT,
                    value TEXT,
                    FOREIGN KEY (id) REFERENCES metadata(id),
                    UNIQUE (id, key)
                )
            """)

            self.connection.commit()

        except Exception as e:
            self.logger.error(f"初始化数据库时出错: {e}")
            raise

    def _load_version(self) -> Dict[str, Any]:
        """
        加载版本信息

        Returns:
            Dict[str, Any]: 版本信息字典
        """
        if self.version_file.exists():
            try:
                with open(self.version_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"加载版本信息时出错: {e}")

        # 创建新的版本信息
        version_info = {
            'version': 1,
            'created_at': datetime.now().isoformat(),
            'last_updated': datetime.now().isoformat(),
            'total_records': 0
        }

        self._save_version(version_info)
        return version_info

    def _save_version(self, version_info: Dict[str, Any]) -> None:
        """
        保存版本信息

        Args:
            version_info: 版本信息字典
        """
        try:
            with open(self.version_file, 'w') as f:
                json.dump(version_info, f, indent=2)
        except Exception as e:
            self.logger.error(f"保存版本信息时出错: {e}")

    def _calculate_checksum(self, data: Dict[str, Any]) -> str:
        """
        计算元数据的校验和

        Args:
            data: 元数据字典

        Returns:
            str: 校验和字符串
        """
        try:
            data_str = json.dumps(data, sort_keys=True)
            return hashlib.md5(data_str.encode()).hexdigest()
        except Exception as e:
            self.logger.error(f"计算校验和时出错: {e}")
            return ""

    def _update_index(self, id: int, data: Dict[str, Any]) -> None:
        """
        更新元数据索引

        Args:
            id: 记录ID
            data: 元数据字典
        """
        try:
            cursor = self.connection.cursor()

            # 删除旧的索引
            cursor.execute("DELETE FROM metadata_index WHERE id = ?", (id,))

            # 添加新的索引
            for key, value in data.items():
                # 确保所有值都转换为基本数据类型
                if isinstance(value, (str, int, float, bool)):
                    # 确保ID是整数类型
                    safe_id = int(id) if not isinstance(id, int) else id
                    cursor.execute(
                        "INSERT INTO metadata_index (id, key, value) VALUES (?, ?, ?)",
                        (safe_id, str(key), str(value))
                    )
                elif hasattr(value, 'item'):  # numpy标量
                    safe_id = int(id) if not isinstance(id, int) else id
                    cursor.execute(
                        "INSERT INTO metadata_index (id, key, value) VALUES (?, ?, ?)",
                        (safe_id, str(key), str(value.item()))
                    )

            self.connection.commit()

        except Exception as e:
            self.logger.error(f"更新索引时出错: {e}")
            self.connection.rollback()

    def store_metadata(self, id: int, metadata: Dict[str, Any]) -> bool:
        """
        存储元数据

        Args:
            id: 记录ID
            metadata: 元数据字典

        Returns:
            bool: 是否成功存储
        """
        try:
            cursor = self.connection.cursor()

            # 确保ID是整数类型
            safe_id = int(id) if not isinstance(id, int) else id

            # 计算校验和
            checksum = self._calculate_checksum(metadata)

            # 检查记录是否存在
            cursor.execute("SELECT version FROM metadata WHERE id = ?", (safe_id,))
            result = cursor.fetchone()

            if result:
                # 更新现有记录
                version = result[0] + 1
                cursor.execute("""
                    UPDATE metadata
                    SET data = ?, updated_at = CURRENT_TIMESTAMP,
                        version = ?, checksum = ?
                    WHERE id = ?
                """, (json.dumps(metadata), version, checksum, safe_id))
            else:
                # 插入新记录
                cursor.execute("""
                    INSERT INTO metadata (id, data, version, checksum)
                    VALUES (?, ?, ?, ?)
                """, (safe_id, json.dumps(metadata), 1, checksum))

            # 更新索引
            self._update_index(safe_id, metadata)

            # 更新缓存
            if self.cache_enabled:
                with self.cache_lock:
                    self.cache[safe_id] = metadata

            # 更新版本信息
            self.current_version['version'] += 1
            self.current_version['last_updated'] = datetime.now().isoformat()
            cursor.execute("SELECT COUNT(*) FROM metadata")
            self.current_version['total_records'] = cursor.fetchone()[0]
            self._save_version(self.current_version)

            self.connection.commit()
            return True

        except Exception as e:
            self.logger.error(f"存储元数据时出错: {e}")
            self.connection.rollback()
            return False

    def get_metadata(self, id: int) -> Optional[Dict[str, Any]]:
        """
        获取元数据

        Args:
            id: 记录ID

        Returns:
            Optional[Dict[str, Any]]: 元数据字典
        """
        try:
            # 检查缓存
            if self.cache_enabled:
                with self.cache_lock:
                    if id in self.cache:
                        return self.cache[id]

            cursor = self.connection.cursor()
            cursor.execute("SELECT data FROM metadata WHERE id = ?", (id,))
            result = cursor.fetchone()

            if result:
                metadata = json.loads(result[0])

                # 更新缓存
                if self.cache_enabled:
                    with self.cache_lock:
                        self.cache[id] = metadata

                return metadata

            return None

        except Exception as e:
            self.logger.error(f"获取元数据时出错: {e}")
            return None

    def delete_metadata(self, id: int) -> bool:
        """
        删除元数据

        Args:
            id: 记录ID

        Returns:
            bool: 是否成功删除
        """
        try:
            cursor = self.connection.cursor()

            # 删除记录和索引
            cursor.execute("DELETE FROM metadata WHERE id = ?", (id,))
            cursor.execute("DELETE FROM metadata_index WHERE id = ?", (id,))

            # 更新缓存
            if self.cache_enabled:
                with self.cache_lock:
                    self.cache.pop(id, None)

            # 更新版本信息
            self.current_version['version'] += 1
            self.current_version['last_updated'] = datetime.now().isoformat()
            cursor.execute("SELECT COUNT(*) FROM metadata")
            self.current_version['total_records'] = cursor.fetchone()[0]
            self._save_version(self.current_version)

            self.connection.commit()
            return True

        except Exception as e:
            self.logger.error(f"删除元数据时出错: {e}")
            self.connection.rollback()
            return False

    def search_metadata(self, query: Dict[str, Any], limit: Optional[int] = None) -> List[Tuple[int, Dict[str, Any]]]:
        """
        搜索元数据

        Args:
            query: 查询条件字典
            limit: 返回结果数量限制

        Returns:
            List[Tuple[int, Dict[str, Any]]]: 匹配的记录列表 [(id, metadata), ...]
        """
        try:
            cursor = self.connection.cursor()

            # 构建查询
            conditions = []
            params = []
            for key, value in query.items():
                conditions.append("EXISTS (SELECT 1 FROM metadata_index WHERE metadata_index.id = metadata.id AND key = ? AND value = ?)")
                params.extend([key, str(value)])

            query_sql = "SELECT id, data FROM metadata"
            if conditions:
                query_sql += " WHERE " + " AND ".join(conditions)
            if limit:
                query_sql += f" LIMIT {limit}"

            cursor.execute(query_sql, params)
            results = cursor.fetchall()

            return [(id_, json.loads(data)) for id_, data in results]

        except Exception as e:
            self.logger.error(f"搜索元数据时出错: {e}")
            return []

    def batch_store_metadata(self, metadata_dict: Dict[int, Dict[str, Any]]) -> bool:
        """
        批量存储元数据

        Args:
            metadata_dict: ID到元数据的映射字典

        Returns:
            bool: 是否成功存储所有记录
        """
        try:
            for id_, metadata in tqdm(metadata_dict.items(), desc="Storing metadata"):
                if not self.store_metadata(id_, metadata):
                    return False
            return True

        except Exception as e:
            self.logger.error(f"批量存储元数据时出错: {e}")
            return False

    def get_all_metadata(self) -> Dict[int, Dict[str, Any]]:
        """
        获取所有元数据

        Returns:
            Dict[int, Dict[str, Any]]: ID到元数据的映射字典
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT id, data FROM metadata")
            results = cursor.fetchall()

            metadata_dict = {id_: json.loads(data) for id_, data in results}

            # 更新缓存
            if self.cache_enabled:
                with self.cache_lock:
                    self.cache.update(metadata_dict)

            return metadata_dict

        except Exception as e:
            self.logger.error(f"获取所有元数据时出错: {e}")
            return {}

    def clear_cache(self) -> None:
        """清除缓存"""
        if self.cache_enabled:
            with self.cache_lock:
                self.cache.clear()

    def get_metadata_stats(self) -> Dict[str, Any]:
        """
        获取元数据统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        try:
            cursor = self.connection.cursor()

            stats = {
                'version': self.current_version,
                'database_size': self.db_path.stat().st_size,
                'cache_size': len(self.cache) if self.cache_enabled else 0
            }

            # 获取记录数量
            cursor.execute("SELECT COUNT(*) FROM metadata")
            stats['total_records'] = cursor.fetchone()[0]

            # 获取索引统计
            cursor.execute("SELECT COUNT(DISTINCT key) FROM metadata_index")
            stats['indexed_fields'] = cursor.fetchone()[0]

            return stats

        except Exception as e:
            self.logger.error(f"获取统计信息时出错: {e}")
            return {}

    def cleanup(self) -> None:
        """清理资源"""
        try:
            if self.connection:
                self.connection.close()
        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")

if __name__ == "__main__":
    # 测试代码
    test_config = {
        'storage': {
            'base_dir': 'data/vectors',
            'metadata_format': 'json',
            'cache_enabled': True
        }
    }

    manager = MetadataManager(test_config)

    # 测试存储元数据
    test_metadata = {
        1: {'text': 'Document 1', 'tags': ['test', 'doc'], 'length': 100},
        2: {'text': 'Document 2', 'tags': ['test'], 'length': 200},
        3: {'text': 'Document 3', 'tags': ['doc'], 'length': 150}
    }

    print("存储元数据...")
    success = manager.batch_store_metadata(test_metadata)
    print(f"存储{'成功' if success else '失败'}")

    # 测试检索元数据
    print("\n检索元数据...")
    metadata = manager.get_metadata(1)
    print(f"ID 1的元数据: {metadata}")

    # 测试搜索元数据
    print("\n搜索元数据...")
    results = manager.search_metadata({'tags': 'test'})
    print(f"包含'test'标签的文档: {len(results)} 个")

    # 显示统计信息
    print("\n元数据统计信息:")
    stats = manager.get_metadata_stats()
    print(json.dumps(stats, indent=2))

    # 清理资源
    manager.cleanup()