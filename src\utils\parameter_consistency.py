"""
参数一致性检查工具

用于检查和确保向量化、索引和微调过程中使用一致的参数
"""

import logging
from typing import Dict, Any, List, Tuple

logger = logging.getLogger(__name__)

class ParameterConsistencyChecker:
    """参数一致性检查器"""
    
    @staticmethod
    def check_vectorization_and_fine_tuning(
        vectorization_params: Dict[str, Any],
        fine_tuning_params: Dict[str, Any]
    ) -> Tuple[bool, List[str]]:
        """
        检查向量化和微调参数的一致性
        
        Args:
            vectorization_params: 向量化参数
            fine_tuning_params: 微调参数
            
        Returns:
            Tuple[bool, List[str]]: (是否一致, 不一致项列表)
        """
        inconsistencies = []
        
        # 检查向量维度
        vec_dim = vectorization_params.get('vector_dimension')
        ft_dim = fine_tuning_params.get('vector_dimension')
        
        if vec_dim is not None and ft_dim is not None and vec_dim != ft_dim:
            inconsistencies.append(
                f"向量维度不一致: 向量化={vec_dim}, 微调={ft_dim}"
            )
        
        # 检查计算设备
        vec_device = vectorization_params.get('device')
        ft_device = fine_tuning_params.get('device')
        
        if vec_device is not None and ft_device is not None and vec_device != ft_device:
            inconsistencies.append(
                f"计算设备不一致: 向量化={vec_device}, 微调={ft_device}"
            )
        
        # 检查批处理大小（不强制一致，但提示）
        vec_batch = vectorization_params.get('batch_size')
        ft_batch = fine_tuning_params.get('batch_size')
        
        if vec_batch is not None and ft_batch is not None and vec_batch != ft_batch:
            logger.warning(f"批处理大小不同: 向量化={vec_batch}, 微调={ft_batch}")
        
        return len(inconsistencies) == 0, inconsistencies
    
    @staticmethod
    def check_model_compatibility(
        model_name: str,
        vector_dimension: int
    ) -> Tuple[bool, str]:
        """
        检查模型与向量维度的兼容性
        
        Args:
            model_name: 模型名称
            vector_dimension: 向量维度
            
        Returns:
            Tuple[bool, str]: (是否兼容, 不兼容原因)
        """
        # 已知模型的默认向量维度
        model_dimensions = {
            "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2": 384,
            "sentence-transformers/all-MiniLM-L6-v2": 384,
            "sentence-transformers/all-mpnet-base-v2": 768,
            "ollama_deepseek-r1_32b": 4096,
            "ollama_llama3_8b": 4096
        }
        
        # 检查模型是否在已知列表中
        if model_name in model_dimensions:
            expected_dim = model_dimensions[model_name]
            
            # 检查维度是否匹配
            if vector_dimension != expected_dim:
                return False, f"模型 {model_name} 的默认向量维度为 {expected_dim}，但设置为 {vector_dimension}"
        
        return True, ""
    
    @staticmethod
    def suggest_consistent_parameters(
        vectorization_params: Dict[str, Any],
        fine_tuning_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        建议一致的参数设置
        
        Args:
            vectorization_params: 向量化参数
            fine_tuning_params: 微调参数
            
        Returns:
            Dict[str, Any]: 建议的一致参数
        """
        # 创建建议参数字典
        suggested_params = {}
        
        # 向量维度（优先使用向量化参数）
        vec_dim = vectorization_params.get('vector_dimension')
        ft_dim = fine_tuning_params.get('vector_dimension')
        
        if vec_dim is not None and ft_dim is not None:
            suggested_params['vector_dimension'] = vec_dim
        elif vec_dim is not None:
            suggested_params['vector_dimension'] = vec_dim
        elif ft_dim is not None:
            suggested_params['vector_dimension'] = ft_dim
        else:
            suggested_params['vector_dimension'] = 768  # 更新默认值为768
        
        # 计算设备（优先使用GPU）
        vec_device = vectorization_params.get('device')
        ft_device = fine_tuning_params.get('device')
        
        if vec_device == 'cuda' or ft_device == 'cuda':
            suggested_params['device'] = 'cuda'
        else:
            suggested_params['device'] = 'cpu'
        
        # 批处理大小（取平均值）
        vec_batch = vectorization_params.get('batch_size')
        ft_batch = fine_tuning_params.get('batch_size')
        
        if vec_batch is not None and ft_batch is not None:
            suggested_params['batch_size'] = max(1, (vec_batch + ft_batch) // 2)
        elif vec_batch is not None:
            suggested_params['batch_size'] = vec_batch
        elif ft_batch is not None:
            suggested_params['batch_size'] = ft_batch
        else:
            suggested_params['batch_size'] = 16  # 默认值
        
        return suggested_params