#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Ollama集成模块
提供与Ollama API的集成，用于向量化操作
"""

import requests
import numpy as np
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

class OllamaEmbedding:
    """Ollama嵌入类，用于调用Ollama API进行向量化"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化Ollama嵌入类

        Args:
            config: 配置字典
        """
        self.logger = logging.getLogger(__name__)

        # 获取配置
        ollama_config = config.get('local_models', {}).get('ollama', {})
        self.api_url = ollama_config.get('api_url', 'http://localhost:11434/api')
        self.model_name = ollama_config.get('default_model', 'llama2')

        # 从模型列表中查找指定模型的配置
        models = ollama_config.get('models', [])
        for model in models:
            if model.get('name') == self.model_name:
                self.parameters = model.get('parameters', {})
                break
        else:
            self.parameters = {}

        # 向量维度 - 根据模型不同可能需要调整
        self.vector_dimension = config.get('vectorization', {}).get('vector_dimension', 768)  # 更新默认维度为768

        # 验证Ollama服务和模型是否可用
        self.service_available = self._check_service_availability()
        self.model_available = False

        if self.service_available:
            self.model_available = self._check_model_availability()
            if not self.model_available:
                self.logger.warning(f"模型 {self.model_name} 不可用，尝试拉取模型")
                self._try_pull_model()
        else:
            self.logger.error(f"Ollama服务不可用，API地址: {self.api_url}")
            self.logger.info("请确保Ollama服务正在运行，或者检查API地址是否正确")

        self.logger.info(f"初始化Ollama嵌入类，使用模型: {self.model_name}，API地址: {self.api_url}")

    def _check_service_availability(self) -> bool:
        """检查Ollama服务是否可用"""
        try:
            # 修正API端点 - Ollama的正确端点是 /api/tags
            base_url = self.api_url.rstrip('/api').rstrip('/')
            response = requests.get(f"{base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                self.logger.info(f"✓ Ollama服务可用，API地址: {base_url}/api")
                return True
            else:
                self.logger.error(f"Ollama服务响应错误，状态码: {response.status_code}")
                return False
        except Exception as e:
            self.logger.error(f"Ollama服务不可用，API地址: {self.api_url}")
            self.logger.error(f"检查Ollama服务可用性时出错: {e}")
            self.logger.info("请确保Ollama服务正在运行，或者检查API地址是否正确")
            return False

    def _check_model_availability(self) -> bool:
        """检查指定模型是否可用"""
        try:
            # 修正API端点
            base_url = self.api_url.rstrip('/api').rstrip('/')
            response = requests.get(f"{base_url}/api/tags", timeout=10)

            if response.status_code == 200:
                models_data = response.json()
                models = models_data.get('models', [])

                # 检查指定模型是否存在
                for model in models:
                    model_name = model.get('name', '')
                    if model_name == self.model_name:
                        self.logger.info(f"✓ 模型 {self.model_name} 可用")
                        return True

                # 模型不存在，尝试拉取
                self.logger.warning(f"模型 {self.model_name} 不存在，尝试拉取...")
                return self._try_pull_model()
            else:
                self.logger.error(f"获取模型列表失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"检查模型可用性时出错: {e}")
            return False

    def _try_pull_model(self) -> bool:
        """尝试拉取模型"""
        try:
            self.logger.info(f"尝试拉取模型: {self.model_name}")
            # 修正API端点
            base_url = self.api_url.rstrip('/api').rstrip('/')
            response = requests.post(
                f"{base_url}/api/pull",
                json={"name": self.model_name},
                timeout=300  # 5分钟超时
            )
            if response.status_code == 200:
                self.logger.info(f"成功拉取模型: {self.model_name}")
                self.model_available = True
                return True
            else:
                self.logger.error(f"拉取模型失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            self.logger.error(f"拉取模型时出错: {e}")
            return False

    def encode_text(self, text: str) -> np.ndarray:
        """
        对单个文本进行向量化

        Args:
            text: 输入文本

        Returns:
            np.ndarray: 文本向量
        """
        if not text:
            return np.zeros(self.vector_dimension)

        # 首先检查服务和模型是否可用
        if not self.service_available:
            # 重新检查服务可用性（可能初始化时网络问题）
            self.logger.info("重新检查Ollama服务可用性...")
            self.service_available = self._check_service_availability()
            if not self.service_available:
                self.logger.error("Ollama服务不可用，无法进行向量化")
                return self._fallback_to_sentence_transformers([text])[0]

        if not self.model_available:
            # 尝试重新检查模型可用性
            self.model_available = self._check_model_availability()
            if not self.model_available:
                self.logger.error(f"模型 {self.model_name} 不可用，回退到其他模型")
                return self._fallback_to_sentence_transformers([text])[0]

        try:
            # 构建请求 - 修正API端点
            base_url = self.api_url.rstrip('/api').rstrip('/')
            endpoint = f"{base_url}/api/embeddings"
            payload = {
                "model": self.model_name,
                "prompt": text,
                **self.parameters
            }

            # 发送请求 - 增加超时时间，因为嵌入模型可能需要更长时间
            response = requests.post(endpoint, json=payload, timeout=60)
            response.raise_for_status()

            # 解析响应
            result = response.json()
            if 'embedding' in result:
                embedding = np.array(result['embedding'])

                # 如果向量维度与配置不符，进行调整
                if len(embedding) != self.vector_dimension:
                    self.logger.warning(f"Ollama返回的向量维度 ({len(embedding)}) 与配置的维度 ({self.vector_dimension}) 不符")
                    if len(embedding) > self.vector_dimension:
                        # 截断
                        embedding = embedding[:self.vector_dimension]
                    else:
                        # 填充
                        padding = np.zeros(self.vector_dimension - len(embedding))
                        embedding = np.concatenate([embedding, padding])

                return embedding
            else:
                self.logger.error(f"Ollama API返回的结果中没有embedding字段: {result}")
                return np.zeros(self.vector_dimension)

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                self.logger.warning(f"模型 {self.model_name} 不存在，尝试回退到默认模型")
                return self._fallback_to_sentence_transformers([text])[0]
            else:
                self.logger.error(f"调用Ollama API进行向量化时出错: {e}")
                return self._fallback_to_sentence_transformers([text])[0]
        except Exception as e:
            self.logger.error(f"调用Ollama API进行向量化时出错: {e}")
            return self._fallback_to_sentence_transformers([text])[0]

    def _fallback_to_sentence_transformers(self, texts: List[str]) -> np.ndarray:
        """回退到transformers模型（兼容sentence-transformers）"""
        try:
            self.logger.info("回退到transformers模型进行向量化")

            # 避免循环导入，直接使用transformers库
            try:
                from transformers import AutoTokenizer, AutoModel
                import torch

                model_name = 'distilbert-base-uncased'
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                model = AutoModel.from_pretrained(model_name)

                # 批量处理文本
                embeddings = []
                for text in texts:
                    inputs = tokenizer(text, return_tensors='pt', truncation=True, padding=True, max_length=512)
                    with torch.no_grad():
                        outputs = model(**inputs)
                        # 使用[CLS] token的embedding
                        embedding = outputs.last_hidden_state[:, 0, :].squeeze().numpy()

                        # 调整维度
                        if len(embedding) != self.vector_dimension:
                            if len(embedding) > self.vector_dimension:
                                embedding = embedding[:self.vector_dimension]
                            else:
                                padding = np.zeros(self.vector_dimension - len(embedding))
                                embedding = np.concatenate([embedding, padding])

                        embeddings.append(embedding)

                return np.array(embeddings, dtype=np.float32)

            except Exception as e:
                self.logger.error(f"transformers回退也失败: {e}")
                raise e

        except Exception as e:
            self.logger.error(f"回退模型也失败: {e}")
            # 最后的后备方案：返回零向量而不是随机向量，保持一致性
            self.logger.warning(f"使用零向量作为最终后备方案，维度: {self.vector_dimension}")
            return np.zeros((len(texts), self.vector_dimension), dtype=np.float32)

    def encode_batch(self, texts: List[str]) -> np.ndarray:
        """
        批量对文本进行向量化

        Args:
            texts: 文本列表

        Returns:
            np.ndarray: 文本向量矩阵
        """
        if not texts:
            return np.array([])

        # 逐个处理文本
        embeddings = []
        for text in texts:
            embedding = self.encode_text(text)
            embeddings.append(embedding)

        return np.array(embeddings)

    def embed_texts(self, texts: List[str]) -> np.ndarray:
        """
        批量对文本进行向量化 (兼容接口)

        Args:
            texts: 文本列表

        Returns:
            np.ndarray: 文本向量矩阵
        """
        return self.encode_batch(texts)

# 为了兼容性，添加别名
OllamaEmbedding.embed_batch = OllamaEmbedding.encode_batch
