#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试正确的模型选择功能
验证搜索界面严格按照用户选择的模型执行搜索
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("=" * 80)
print("🎯 测试正确的模型选择功能")
print("=" * 80)

def test_ollama_models_available():
    """测试Ollama模型可用性"""
    print("\n1. 检查Ollama嵌入模型...")
    
    try:
        import requests
        response = requests.get('http://localhost:11434/api/tags', timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            embed_models = [m for m in models if 'embed' in m.get('name', '').lower()]
            
            print(f"✅ Ollama服务正常，总模型数: {len(models)}")
            print(f"✅ 可用嵌入模型数: {len(embed_models)}")
            
            for model in embed_models:
                model_name = model.get('name', '')
                print(f"   - {model_name}")
                
            return embed_models
        else:
            print(f"❌ Ollama服务响应异常: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 无法连接Ollama服务: {e}")
        return []

def test_model_dropdown_population():
    """测试模型下拉菜单填充"""
    print("\n2. 测试模型下拉菜单填充...")
    
    try:
        from src.gui.widgets.search import SearchWidget
        from src.utils.translator import Translator
        
        # 创建搜索组件
        translator = Translator()
        search_widget = SearchWidget(translator)
        
        # 加载模型选项
        search_widget._load_local_llm_options()
        
        # 检查下拉菜单内容
        combo_count = search_widget.llm_combo.count()
        print(f"✅ 模型下拉菜单加载成功，选项数: {combo_count}")
        
        print("   可用选项:")
        for i in range(combo_count):
            item_text = search_widget.llm_combo.itemText(i)
            print(f"   [{i}] {item_text}")
            
        return search_widget
        
    except Exception as e:
        print(f"❌ 测试模型下拉菜单失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

def test_ollama_config_creation():
    """测试Ollama配置创建"""
    print("\n3. 测试Ollama模型配置创建...")
    
    try:
        from src.gui.widgets.search import SearchWidget
        from src.utils.translator import Translator
        
        # 创建搜索组件
        translator = Translator()
        search_widget = SearchWidget(translator)
        
        # 测试Ollama模型配置
        test_model = "ollama:nomic-embed-text:latest"
        test_dimension = 768
        
        print(f"   测试模型: {test_model}")
        print(f"   索引维度: {test_dimension}")
        
        config = search_widget._create_embedder_config_for_search(test_dimension, test_model)
        
        print("✅ Ollama配置创建成功:")
        print(f"   模型名称: {config['vectorization']['model_name']}")
        print(f"   向量维度: {config['vectorization']['vector_dimension']}")
        print(f"   Ollama API: {config['local_models']['ollama']['api_url']}")
        print(f"   默认模型: {config['local_models']['ollama']['default_model']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ollama配置创建失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_sentence_transformers_config():
    """测试Sentence-transformers配置创建"""
    print("\n4. 测试Sentence-transformers模型配置创建...")
    
    try:
        from src.gui.widgets.search import SearchWidget
        from src.utils.translator import Translator
        
        # 创建搜索组件
        translator = Translator()
        search_widget = SearchWidget(translator)
        
        # 测试sentence-transformers模型配置
        test_model = "sentence-transformers:paraphrase-multilingual-MiniLM-L12-v2"
        test_dimension = 384
        
        print(f"   测试模型: {test_model}")
        print(f"   索引维度: {test_dimension}")
        
        config = search_widget._create_embedder_config_for_search(test_dimension, test_model)
        
        print("✅ Sentence-transformers配置创建成功:")
        print(f"   模型名称: {config['vectorization']['model_name']}")
        print(f"   向量维度: {config['vectorization']['vector_dimension']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Sentence-transformers配置创建失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_dimension_mismatch_error():
    """测试维度不匹配错误处理"""
    print("\n5. 测试维度不匹配错误处理...")
    
    try:
        from src.gui.widgets.search import SearchWidget
        from src.utils.translator import Translator
        
        # 创建搜索组件
        translator = Translator()
        search_widget = SearchWidget(translator)
        
        # 测试维度不匹配情况
        test_cases = [
            ("ollama:nomic-embed-text:latest", 384),  # Ollama模型但384维索引
            ("sentence-transformers:model", 768),     # ST模型但768维索引
        ]
        
        for model, dimension in test_cases:
            print(f"   测试: {model} + {dimension}维索引")
            try:
                config = search_widget._create_embedder_config_for_search(dimension, model)
                print(f"   ❌ 应该抛出维度不匹配错误，但没有")
                return False
            except ValueError as e:
                if "维度不匹配" in str(e):
                    print(f"   ✅ 正确捕获维度不匹配错误: {e}")
                else:
                    print(f"   ❌ 错误类型不正确: {e}")
                    return False
            except Exception as e:
                print(f"   ❌ 意外错误: {e}")
                return False
        
        print("✅ 维度不匹配错误处理正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试维度不匹配错误处理失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_invalid_model_error():
    """测试无效模型错误处理"""
    print("\n6. 测试无效模型错误处理...")
    
    try:
        from src.gui.widgets.search import SearchWidget
        from src.utils.translator import Translator
        
        # 创建搜索组件
        translator = Translator()
        search_widget = SearchWidget(translator)
        
        # 测试无效模型
        invalid_models = [
            None,
            "",
            "不使用大模型",
            "--- 本地大模型 ---",
            "unsupported:model:type"
        ]
        
        for model in invalid_models:
            print(f"   测试无效模型: {model}")
            try:
                config = search_widget._create_embedder_config_for_search(768, model)
                print(f"   ❌ 应该抛出错误，但没有")
                return False
            except ValueError as e:
                print(f"   ✅ 正确捕获错误: {e}")
            except Exception as e:
                print(f"   ❌ 意外错误类型: {e}")
                return False
        
        print("✅ 无效模型错误处理正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试无效模型错误处理失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """主函数"""
    print("开始测试正确的模型选择功能...")
    
    # 执行所有测试
    ollama_models = test_ollama_models_available()
    dropdown_ok = test_model_dropdown_population()
    ollama_config_ok = test_ollama_config_creation()
    st_config_ok = test_sentence_transformers_config()
    dimension_error_ok = test_dimension_mismatch_error()
    invalid_error_ok = test_invalid_model_error()
    
    # 总结
    print("\n" + "=" * 80)
    print("🎯 测试结果总结")
    print("=" * 80)
    
    print(f"Ollama模型可用: {'✅' if ollama_models else '❌'} ({len(ollama_models)} 个嵌入模型)")
    print(f"下拉菜单填充: {'✅' if dropdown_ok else '❌'}")
    print(f"Ollama配置创建: {'✅' if ollama_config_ok else '❌'}")
    print(f"ST配置创建: {'✅' if st_config_ok else '❌'}")
    print(f"维度错误处理: {'✅' if dimension_error_ok else '❌'}")
    print(f"无效模型处理: {'✅' if invalid_error_ok else '❌'}")
    
    all_ok = (ollama_models and dropdown_ok and ollama_config_ok and 
              st_config_ok and dimension_error_ok and invalid_error_ok)
    
    print(f"\n🎯 最终结论: {'✅ 修复成功' if all_ok else '❌ 仍有问题'}")
    
    if all_ok:
        print("\n✅ 现在搜索界面将严格按照用户选择的模型执行:")
        print("1. 用户选择ollama模型 → 使用Ollama API进行嵌入")
        print("2. 用户选择sentence-transformers → 使用本地transformers")
        print("3. 维度不匹配 → 抛出明确错误，要求解决")
        print("4. 模型不可用 → 抛出明确错误，要求解决")
        print("5. 不再有任何硬编码或强制替代方案")
    else:
        print("\n❌ 需要进一步修复")

if __name__ == "__main__":
    main()
