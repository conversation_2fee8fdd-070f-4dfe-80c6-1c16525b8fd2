#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试关键修复的简化脚本
"""

import sys
import logging
import requests
import numpy as np
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_ollama_service():
    """测试Ollama服务"""
    logger.info("测试Ollama服务...")
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            logger.info(f"✅ Ollama服务运行正常，发现 {len(models)} 个模型")
            
            # 查找目标模型
            target_models = ["qwen3:30b-a3b", "qwen", "llama"]
            found_models = []
            for model in models:
                model_name = model.get('name', '')
                for target in target_models:
                    if target in model_name.lower():
                        found_models.append(model_name)
                        break
            
            if found_models:
                logger.info(f"✅ 找到可用模型: {found_models}")
                return True
            else:
                logger.warning("⚠️  未找到目标模型，但服务正常")
                return True
        else:
            logger.error(f"❌ Ollama服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Ollama服务不可用: {e}")
        return False

def test_vector_dimensions():
    """测试向量维度适配"""
    logger.info("测试向量维度适配...")
    try:
        from src.indexer.builder import IndexBuilder
        
        config = {
            'indexing': {
                'index_type': 'flat',
                'dimension': 1024,
                'metric': 'cosine',
                'quantization': 'none'
            }
        }
        
        builder = IndexBuilder(config)
        builder.create_index(1024)
        
        # 测试不同维度
        test_cases = [
            (768, "distilbert维度"),
            (384, "MiniLM维度"),
            (1536, "大模型维度")
        ]
        
        for dim, desc in test_cases:
            vectors = np.random.randn(5, dim).astype(np.float32)
            adapted = builder._adapt_vector_dimensions(vectors, 1024)
            assert adapted.shape[1] == 1024
            logger.info(f"✅ {desc} ({dim}→1024) 适配成功")
        
        return True
    except Exception as e:
        logger.error(f"❌ 向量维度适配测试失败: {e}")
        return False

def test_embeddings():
    """测试嵌入模型"""
    logger.info("测试嵌入模型...")
    try:
        from src.vectorizer.embeddings import TextEmbedding
        
        config = {
            'vectorization': {
                'model_name': 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
                'vector_dimension': 384,
                'batch_size': 32,
                'device': 'cpu',
                'normalize_vectors': True
            }
        }
        
        embedder = TextEmbedding(config)
        vectors = embedder.embed_texts(["测试文本", "test text"])
        
        assert vectors.shape[0] == 2
        assert vectors.shape[1] == 384
        logger.info(f"✅ 嵌入模型测试成功，向量形状: {vectors.shape}")
        return True
    except Exception as e:
        logger.error(f"❌ 嵌入模型测试失败: {e}")
        return False

def test_ollama_fallback():
    """测试Ollama回退机制"""
    logger.info("测试Ollama回退机制...")
    try:
        from src.vectorizer.ollama import OllamaEmbedding
        
        config = {
            'local_models': {
                'ollama': {
                    'api_url': 'http://localhost:11434/api',
                    'default_model': 'nonexistent-model',
                    'models': []
                }
            },
            'vectorization': {
                'vector_dimension': 384
            }
        }
        
        ollama = OllamaEmbedding(config)
        vector = ollama.encode_text("测试文本")
        
        assert vector.shape[0] == 384
        logger.info(f"✅ Ollama回退机制测试成功，向量维度: {vector.shape[0]}")
        return True
    except Exception as e:
        logger.error(f"❌ Ollama回退机制测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始关键修复测试...")
    logger.info("="*50)
    
    tests = [
        ("Ollama服务", test_ollama_service),
        ("向量维度适配", test_vector_dimensions),
        ("嵌入模型", test_embeddings),
        ("Ollama回退机制", test_ollama_fallback)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
            failed += 1
    
    logger.info("="*50)
    logger.info(f"测试结果: ✅ {passed} 通过, ❌ {failed} 失败")
    
    if passed > failed:
        logger.info("🎉 大部分测试通过！系统基本可用")
        return True
    else:
        logger.error("⚠️  多个测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
