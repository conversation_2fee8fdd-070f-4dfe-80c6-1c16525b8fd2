#!/usr/bin/env python3
"""
测试向量维度不匹配修复的脚本
"""

import sys
import os
import logging
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_dimension_compatibility():
    """测试向量维度兼容性处理"""
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    try:
        # 导入必要的模块
        from src.vectorizer.embeddings import TextEmbedding
        from src.indexer.builder import IndexBuilder
        from src.storage.vector_store import VectorStore
        
        logger.info("开始测试向量维度兼容性...")
        
        # 1. 创建文本嵌入器
        logger.info("创建文本嵌入器...")
        embedder = TextEmbedding()
        
        # 2. 生成测试向量
        test_texts = [
            "这是第一个测试文本",
            "这是第二个测试文本", 
            "这是第三个测试文本"
        ]
        
        logger.info("生成测试向量...")
        vectors = embedder.embed_texts(test_texts)
        logger.info(f"生成向量维度: {vectors.shape}")
        
        # 3. 检查现有索引
        index_dir = Path("data/indices")
        if index_dir.exists():
            index_files = list(index_dir.glob("*.idx"))
            if index_files:
                latest_index = max(index_files, key=lambda x: x.stat().st_mtime)
                logger.info(f"找到现有索引: {latest_index}")
                
                # 4. 尝试加载索引并检查维度
                builder = IndexBuilder()
                if builder.load_index(latest_index):
                    logger.info(f"成功加载索引，维度: {builder.dimension}")
                    
                    if builder.dimension != vectors.shape[1]:
                        logger.warning(f"维度不匹配: 索引={builder.dimension}, 向量={vectors.shape[1]}")
                        
                        # 测试维度调整
                        if vectors.shape[1] > builder.dimension:
                            # 降维测试
                            from sklearn.decomposition import PCA
                            pca = PCA(n_components=builder.dimension)
                            adjusted_vectors = pca.fit_transform(vectors)
                            logger.info(f"PCA降维测试成功: {vectors.shape} -> {adjusted_vectors.shape}")
                        else:
                            # 升维测试
                            padding = np.zeros((vectors.shape[0], builder.dimension - vectors.shape[1]))
                            adjusted_vectors = np.hstack([vectors, padding])
                            logger.info(f"零填充升维测试成功: {vectors.shape} -> {adjusted_vectors.shape}")
                    else:
                        logger.info("向量维度匹配，无需调整")
                else:
                    logger.error("无法加载现有索引")
            else:
                logger.info("未找到现有索引文件")
        else:
            logger.info("索引目录不存在")
        
        # 5. 测试PyQt6可用性
        logger.info("测试PyQt6可用性...")
        try:
            from PyQt6.QtWidgets import QApplication, QMessageBox
            logger.info("✓ PyQt6可用")
        except ImportError as e:
            logger.warning(f"✗ PyQt6不可用: {e}")
        
        logger.info("✓ 所有测试完成")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("向量维度兼容性测试")
    print("=" * 50)
    
    success = test_dimension_compatibility()
    
    if success:
        print("\n✓ 测试成功完成")
        sys.exit(0)
    else:
        print("\n✗ 测试失败")
        sys.exit(1)
