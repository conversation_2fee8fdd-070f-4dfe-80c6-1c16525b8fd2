#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("开始直接重建测试...")

try:
    import h5py
    print("✅ h5py导入成功")
except Exception as e:
    print(f"❌ h5py导入失败: {e}")
    sys.exit(1)

try:
    import numpy as np
    print("✅ numpy导入成功")
except Exception as e:
    print(f"❌ numpy导入失败: {e}")
    sys.exit(1)

try:
    import zlib
    print("✅ zlib导入成功")
except Exception as e:
    print(f"❌ zlib导入失败: {e}")
    sys.exit(1)

from pathlib import Path

# 检查文件
vector_file = Path("data/vectors/vectors.h5")
print(f"向量文件存在: {vector_file.exists()}")
if vector_file.exists():
    print(f"文件大小: {vector_file.stat().st_size / 1024 / 1024:.2f} MB")

# 尝试打开HDF5文件
try:
    with h5py.File(vector_file, 'r') as f:
        print(f"HDF5文件打开成功")
        keys = list(f.keys())
        print(f"数据集数量: {len(keys)}")
        print(f"前10个键: {keys[:10]}")
        
        # 找到第一个ids数据集
        ids_keys = [k for k in keys if k.startswith('ids_')]
        print(f"ID数据集数: {len(ids_keys)}")
        
        if ids_keys:
            first_ids_key = ids_keys[0]
            print(f"处理第一个ID数据集: {first_ids_key}")
            
            ids_dataset = f[first_ids_key]
            print(f"ID数据集形状: {ids_dataset.shape}")
            print(f"ID数据集类型: {ids_dataset.dtype}")
            
            if ids_dataset.shape == ():
                # 标量数据集
                doc_id = ids_dataset[()]
                print(f"文档ID: {doc_id}")
                print(f"ID类型: {type(doc_id)}")
                
                # 查找对应的压缩数据集
                compressed_key = first_ids_key.replace('ids_', 'compressed_')
                print(f"查找压缩数据集: {compressed_key}")
                
                if compressed_key in f:
                    compressed_dataset = f[compressed_key]
                    print(f"压缩数据集形状: {compressed_dataset.shape}")
                    print(f"压缩数据集类型: {compressed_dataset.dtype}")
                    
                    if compressed_dataset.shape == ():
                        compressed_data = compressed_dataset[()]
                        print(f"压缩数据类型: {type(compressed_data)}")
                        print(f"压缩数据大小: {len(compressed_data) if hasattr(compressed_data, '__len__') else 'N/A'}")
                        
                        try:
                            if hasattr(compressed_data, 'tobytes'):
                                compressed_bytes = compressed_data.tobytes()
                                print(f"转换为字节成功，大小: {len(compressed_bytes)}")
                                
                                decompressed = zlib.decompress(compressed_bytes)
                                print(f"解压缩成功，大小: {len(decompressed)}")
                                
                                vector = np.frombuffer(decompressed, dtype=np.float32)
                                print(f"向量维度: {len(vector)}")
                                print(f"向量前5个值: {vector[:5]}")
                                
                                print("✅ 成功提取一个向量!")
                                
                            else:
                                print("❌ 压缩数据无法转换为字节")
                        except Exception as e:
                            print(f"❌ 处理压缩数据失败: {e}")
                    else:
                        print("❌ 压缩数据集不是标量")
                else:
                    print("❌ 未找到对应的压缩数据集")
            else:
                print("❌ ID数据集不是标量")
        else:
            print("❌ 未找到ID数据集")
            
except Exception as e:
    print(f"❌ 处理HDF5文件失败: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
