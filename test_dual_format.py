#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
双格式处理功能测试脚本
"""

import os
import sys
from pathlib import Path

def test_directory_structure():
    """测试目录结构"""
    print("🔍 检查目录结构...")
    
    base_dirs = [
        'test_training_data/raw_documents',
        'test_training_data/raw_documents_MD'
    ]
    
    for base_dir in base_dirs:
        path = Path(base_dir)
        if path.exists():
            print(f"✅ 目录存在: {base_dir}")
            
            # 检查子目录
            for item in path.rglob('*'):
                if item.is_dir():
                    print(f"  📁 {item.relative_to(path)}")
                elif item.suffix in ['.pdf', '.md', '.json']:
                    print(f"  📄 {item.relative_to(path)} ({item.suffix})")
        else:
            print(f"❌ 目录不存在: {base_dir}")

def create_sample_md_files():
    """创建示例MD文件"""
    print("\n📝 创建示例MD文件...")
    
    md_dir = Path('test_training_data/raw_documents_MD/enterprise_standards/pdfs')
    md_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建几个示例MD文件
    sample_files = [
        'sample_standard_001.md',
        'sample_standard_002.md',
        'sample_standard_003.md'
    ]
    
    for filename in sample_files:
        md_file = md_dir / filename
        if not md_file.exists():
            content = f"""# {filename.replace('.md', '').replace('_', ' ').title()}

## 概述

这是一个示例标准文档，用于测试双格式处理功能。

## 主要内容

### 1. 技术规范
- 规范要求1
- 规范要求2
- 规范要求3

### 2. 实施指南
- 实施步骤1
- 实施步骤2
- 实施步骤3

### 3. 质量控制
- 质量标准1
- 质量标准2
- 质量标准3

## 结论

本文档提供了完整的技术标准和实施指导。

---
*文档创建时间: 2024年*
*版本: 1.0*
"""
            with open(md_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 创建文件: {md_file}")
        else:
            print(f"📄 文件已存在: {md_file}")

def test_dual_format_processor():
    """测试双格式处理器"""
    print("\n🔧 测试双格式处理器...")

    try:
        # 添加src目录到路径
        sys.path.insert(0, str(Path(__file__).parent / 'src'))

        from dual_format_processor import DualFormatProcessor

        # 创建处理器
        config = {
            'output_dir': 'data/test_dual_format_reports',
            'quality_thresholds': {
                'min_content_length': 50,  # 降低阈值用于测试
                'min_similarity': 0.5,
                'max_error_rate': 0.2,
                'min_completeness': 0.5
            }
        }

        processor = DualFormatProcessor(config)
        print("✅ 双格式处理器创建成功")

        # 测试处理 - 使用新的文件夹结构
        pdf_base_path = 'test_training_data/raw_documents/enterprise_standards'
        md_base_path = 'test_training_data/raw_documents_MD/enterprise_standards'

        if Path(pdf_base_path).exists() and Path(md_base_path).exists():
            print(f"🔄 开始处理目录:")
            print(f"   PDF路径: {pdf_base_path}")
            print(f"   MD路径: {md_base_path}")

            # 统计文件数量
            pdf_count = sum(1 for f in Path(pdf_base_path).rglob('*.pdf'))
            md_count = sum(1 for f in Path(md_base_path).rglob('*.md'))

            print(f"📊 文件统计:")
            print(f"   PDF文件: {pdf_count} 个")
            print(f"   MD文件: {md_count} 个")

            # 执行双格式校对
            results = processor.process_dual_format_documents(pdf_base_path, md_base_path)

            print("\n📊 处理结果:")
            if isinstance(results, dict):
                for key, value in results.items():
                    print(f"  {key}: {value}")
            else:
                print(f"  报告文件: {results}")

        else:
            print(f"❌ 目录不存在:")
            print(f"   PDF目录: {pdf_base_path} - {'存在' if Path(pdf_base_path).exists() else '不存在'}")
            print(f"   MD目录: {md_base_path} - {'存在' if Path(md_base_path).exists() else '不存在'}")

    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保已安装必要的依赖包")
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print("双格式处理功能测试")
    print("=" * 60)
    
    # 测试目录结构
    test_directory_structure()
    
    # 创建示例文件
    create_sample_md_files()
    
    # 测试处理器
    test_dual_format_processor()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
