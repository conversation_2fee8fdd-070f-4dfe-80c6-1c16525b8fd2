#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试双格式处理器修复效果
"""

import sys
import os
from pathlib import Path

def test_dual_format_processor():
    """测试双格式处理器"""
    print("=== 测试双格式处理器 ===")
    
    try:
        # 导入双格式处理器
        sys.path.insert(0, 'src')
        from dual_format_processor import DualFormatProcessor
        print("✅ 成功导入双格式处理器")
        
        # 创建处理器
        config = {
            'output_dir': 'data/dual_format_reports',
            'max_files_to_process': 3,
            'quality_thresholds': {
                'min_content_length': 50,
                'min_similarity': 0.3,
                'max_error_rate': 0.5,
                'min_completeness': 0.3
            }
        }
        
        processor = DualFormatProcessor(config)
        print("✅ 成功创建处理器")
        
        # 检查文件夹结构
        main_dir = "test_training_data"
        pdf_subdir = "raw_documents"
        md_subdir = "raw_documents_MD"
        
        main_path = Path(main_dir)
        pdf_path = main_path / pdf_subdir
        md_path = main_path / md_subdir
        
        print(f"主目录: {main_path} - 存在: {main_path.exists()}")
        print(f"PDF子目录: {pdf_path} - 存在: {pdf_path.exists()}")
        print(f"MD子目录: {md_path} - 存在: {md_path.exists()}")
        
        if main_path.exists() and pdf_path.exists():
            print("🔄 开始双格式校对...")
            results = processor.process_dual_format_documents(main_dir, pdf_subdir, md_subdir)
            print("✅ 双格式校对完成")
            print(f"结果: {results}")
            return True
        else:
            print("❌ 测试文件夹结构不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试双格式处理器修复效果...")
    
    # 检查当前目录
    print(f"当前目录: {os.getcwd()}")
    
    # 测试双格式处理器
    success = test_dual_format_processor()
    
    if success:
        print("🎉 测试成功！")
    else:
        print("❌ 测试失败！")

if __name__ == "__main__":
    main()
