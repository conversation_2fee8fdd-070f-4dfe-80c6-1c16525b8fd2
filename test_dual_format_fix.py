#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试双格式处理器修复效果
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path.cwd()))

def test_libraries():
    """测试必要库的可用性"""
    print("=" * 60)
    print("测试库导入状态")
    print("=" * 60)
    
    # 测试PDF库
    try:
        import pdfplumber
        print("✓ pdfplumber 可用")
    except ImportError as e:
        print(f"✗ pdfplumber 不可用: {e}")

    try:
        import PyPDF2
        print("✓ PyPDF2 可用")
    except ImportError as e:
        print(f"✗ PyPDF2 不可用: {e}")

    # 测试MD库
    try:
        import markdown
        print("✓ markdown 可用")
    except ImportError as e:
        print(f"✗ markdown 不可用: {e}")

    try:
        import frontmatter
        print("✓ frontmatter 可用")
    except ImportError as e:
        print(f"✗ frontmatter 不可用: {e}")

def test_dual_format_processor():
    """测试双格式处理器"""
    print("\n" + "=" * 60)
    print("测试双格式处理器")
    print("=" * 60)
    
    try:
        from src.dual_format_processor import DualFormatProcessor
        print("✓ 双格式处理器导入成功")
        
        # 创建测试配置
        config = {
            'output_dir': 'data/dual_format_reports',
            'max_files_to_process': 3,  # 限制处理文件数量
            'quality_thresholds': {
                'min_content_length': 20,   # 非常低的要求用于测试
                'min_similarity': 0.1,
                'max_error_rate': 0.8,
                'min_completeness': 0.1
            }
        }
        
        processor = DualFormatProcessor(config)
        print("✓ 双格式处理器创建成功")
        print(f"  - 输出目录: {processor.output_dir}")
        print(f"  - 质量阈值: {processor.quality_thresholds}")
        
        return processor
        
    except Exception as e:
        print(f"✗ 双格式处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_single_file_processing(processor):
    """测试单个文件处理"""
    print("\n" + "=" * 60)
    print("测试单个文件处理")
    print("=" * 60)
    
    if not processor:
        print("✗ 处理器不可用，跳过文件处理测试")
        return
    
    # 查找测试文件
    test_pdf_dir = Path("test_training_data/raw_documents")
    test_md_dir = Path("test_training_data_MD/raw_documents")
    
    if not test_pdf_dir.exists():
        print(f"✗ PDF测试目录不存在: {test_pdf_dir}")
        return
        
    if not test_md_dir.exists():
        print(f"✗ MD测试目录不存在: {test_md_dir}")
        return
    
    # 查找第一个PDF文件进行测试
    pdf_files = list(test_pdf_dir.rglob("*.pdf"))
    if not pdf_files:
        print("✗ 未找到PDF测试文件")
        return
    
    test_pdf = pdf_files[0]
    # 构造对应的MD文件路径
    relative_path = test_pdf.relative_to(test_pdf_dir)
    test_md = test_md_dir / relative_path.with_suffix('.md')
    
    print(f"测试文件:")
    print(f"  PDF: {test_pdf}")
    print(f"  MD:  {test_md}")
    print(f"  PDF存在: {test_pdf.exists()}")
    print(f"  MD存在:  {test_md.exists()}")
    
    # 测试单个文档对处理
    try:
        result = processor._process_document_pair(test_pdf, test_md)
        print("\n处理结果:")
        for key, value in result.items():
            if key not in ['content']:  # 跳过内容字段以避免输出过长
                print(f"  {key}: {value}")
        
        return result
        
    except Exception as e:
        print(f"✗ 文件处理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主测试函数"""
    print("双格式处理器修复效果测试")
    print("=" * 60)
    
    # 测试库导入
    test_libraries()
    
    # 测试处理器
    processor = test_dual_format_processor()
    
    # 测试文件处理
    result = test_single_file_processing(processor)
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    if result:
        print("✓ 基本功能测试通过")
        if result.get('errors'):
            print(f"⚠ 发现错误: {result['errors']}")
        else:
            print("✓ 无错误发现")
    else:
        print("✗ 测试失败")

if __name__ == "__main__":
    main()
