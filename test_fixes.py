#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
"""

import sys
import os
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_pdf_available_fix():
    """测试PDF_AVAILABLE全局变量修复"""
    logger.info("=== 测试PDF_AVAILABLE全局变量修复 ===")
    
    try:
        # 导入双格式处理器
        sys.path.insert(0, 'src')
        from dual_format_processor import DualFormatProcessor
        
        # 创建处理器
        config = {
            'output_dir': 'data/dual_format_reports',
            'max_files_to_process': 5,
            'quality_thresholds': {
                'min_content_length': 50,
                'min_similarity': 0.3,
                'max_error_rate': 0.5,
                'min_completeness': 0.3
            }
        }
        
        processor = DualFormatProcessor(config)
        logger.info("✅ 双格式处理器创建成功")
        
        # 测试PDF验证函数
        test_pdf = Path("test_training_data/raw_documents/enterprise_standards/OEM_standards/04_大众/PV标准库/PV_1015_DE_2015-04_弹性体和塑料与轻金属的接触腐蚀_测试体积电阻率.pdf")
        if test_pdf.exists():
            result = processor._validate_pdf_content(test_pdf)
            logger.info(f"✅ PDF验证测试完成: {result.get('valid', False)}")
        else:
            logger.warning("测试PDF文件不存在，跳过PDF验证测试")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ PDF_AVAILABLE修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dual_format_folder_structure():
    """测试新的文件夹层级逻辑"""
    logger.info("=== 测试新的文件夹层级逻辑 ===")
    
    try:
        sys.path.insert(0, 'src')
        from dual_format_processor import DualFormatProcessor
        
        # 创建处理器
        config = {
            'output_dir': 'data/dual_format_reports',
            'max_files_to_process': 3,  # 只处理3个文件进行测试
            'quality_thresholds': {
                'min_content_length': 50,
                'min_similarity': 0.3,
                'max_error_rate': 0.5,
                'min_completeness': 0.3
            }
        }
        
        processor = DualFormatProcessor(config)
        logger.info("✅ 双格式处理器创建成功")
        
        # 测试新的文件夹层级
        main_dir = "test_training_data"
        pdf_subdir = "raw_documents"
        md_subdir = "raw_documents_MD"
        
        # 检查文件夹结构
        main_path = Path(main_dir)
        pdf_path = main_path / pdf_subdir
        md_path = main_path / md_subdir
        
        logger.info(f"主目录: {main_path} - 存在: {main_path.exists()}")
        logger.info(f"PDF子目录: {pdf_path} - 存在: {pdf_path.exists()}")
        logger.info(f"MD子目录: {md_path} - 存在: {md_path.exists()}")
        
        if main_path.exists() and pdf_path.exists():
            # 执行双格式校对
            results = processor.process_dual_format_documents(main_dir, pdf_subdir, md_subdir)
            logger.info(f"✅ 双格式校对完成: {results}")
            return True
        else:
            logger.warning("测试文件夹结构不存在，跳过双格式校对测试")
            return False
        
    except Exception as e:
        logger.error(f"❌ 文件夹层级测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_index_uniqueness():
    """测试索引唯一性修复"""
    logger.info("=== 测试索引唯一性修复 ===")
    
    try:
        # 检查现有索引文件
        indices_dir = Path("data/indices")
        if indices_dir.exists():
            index_files = list(indices_dir.glob("*.idx"))
            logger.info(f"现有索引文件数量: {len(index_files)}")
            for idx_file in index_files[:5]:  # 只显示前5个
                logger.info(f"  - {idx_file.name}")
        else:
            logger.info("索引目录不存在")
        
        logger.info("✅ 索引唯一性检查完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 索引唯一性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试修复效果...")
    
    results = []
    
    # 测试1: PDF_AVAILABLE修复
    results.append(("PDF_AVAILABLE修复", test_pdf_available_fix()))
    
    # 测试2: 文件夹层级逻辑
    results.append(("文件夹层级逻辑", test_dual_format_folder_structure()))
    
    # 测试3: 索引唯一性
    results.append(("索引唯一性", test_index_uniqueness()))
    
    # 输出测试结果
    logger.info("=" * 60)
    logger.info("测试结果汇总:")
    logger.info("=" * 60)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    total_tests = len(results)
    passed_tests = sum(1 for _, success in results if success)
    
    logger.info("=" * 60)
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {passed_tests}")
    logger.info(f"失败测试: {total_tests - passed_tests}")
    logger.info(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有测试通过！")
    else:
        logger.warning("⚠️ 部分测试失败，请检查修复效果")

if __name__ == "__main__":
    main()
