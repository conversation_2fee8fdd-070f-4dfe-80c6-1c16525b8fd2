#!/usr/bin/env python
# -*- coding: utf-8 -*-

import h5py
import numpy as np
import zlib
from pathlib import Path

def test_hdf5_read():
    """测试HDF5文件读取"""
    vector_file = Path("data/vectors/vectors.h5")
    
    if not vector_file.exists():
        print("向量文件不存在")
        return
    
    print(f"文件大小: {vector_file.stat().st_size / 1024 / 1024:.2f} MB")
    
    try:
        with h5py.File(vector_file, 'r') as f:
            print(f"文件属性:")
            for attr_name, attr_value in f.attrs.items():
                print(f"  {attr_name}: {attr_value}")
            
            keys = list(f.keys())
            print(f"\n总数据集数: {len(keys)}")
            
            # 找到ids数据集
            ids_keys = [k for k in keys if k.startswith('ids_')]
            print(f"ID数据集数: {len(ids_keys)}")
            
            # 测试读取第一个数据集
            if ids_keys:
                first_ids_key = ids_keys[0]
                print(f"\n测试读取: {first_ids_key}")
                
                ids_dataset = f[first_ids_key]
                print(f"  类型: {type(ids_dataset)}")
                print(f"  形状: {ids_dataset.shape}")
                print(f"  数据类型: {ids_dataset.dtype}")
                
                # 尝试读取数据
                if ids_dataset.shape == ():
                    # 标量
                    data = ids_dataset[()]
                    print(f"  标量值: {data} (类型: {type(data)})")
                else:
                    # 数组
                    data = ids_dataset[:]
                    print(f"  数组长度: {len(data)}")
                    print(f"  前几个值: {data[:5] if len(data) > 5 else data}")
                
                # 检查对应的压缩数据集
                compressed_key = first_ids_key.replace('ids_', 'compressed_')
                if compressed_key in f:
                    print(f"\n对应的压缩数据集: {compressed_key}")
                    compressed_dataset = f[compressed_key]
                    print(f"  形状: {compressed_dataset.shape}")
                    print(f"  数据类型: {compressed_dataset.dtype}")
                    
                    # 尝试读取压缩数据
                    if compressed_dataset.shape == ():
                        compressed_data = compressed_dataset[()]
                        print(f"  压缩数据类型: {type(compressed_data)}")
                        print(f"  压缩数据大小: {len(compressed_data.tobytes()) if hasattr(compressed_data, 'tobytes') else 'N/A'}")
                        
                        # 尝试解压缩
                        try:
                            if hasattr(compressed_data, 'tobytes'):
                                compressed_bytes = compressed_data.tobytes()
                                decompressed = zlib.decompress(compressed_bytes)
                                vectors = np.frombuffer(decompressed, dtype=np.float32)
                                print(f"  解压缩成功: {len(vectors)} 个float32值")
                                
                                # 尝试重塑
                                vector_dim = f.attrs.get('vector_dim', 384)
                                if len(vectors) % vector_dim == 0:
                                    num_vectors = len(vectors) // vector_dim
                                    reshaped = vectors.reshape(num_vectors, vector_dim)
                                    print(f"  重塑成功: {reshaped.shape}")
                                else:
                                    print(f"  无法重塑: {len(vectors)} 不能被 {vector_dim} 整除")
                        except Exception as e:
                            print(f"  解压缩失败: {e}")
                    else:
                        print(f"  压缩数据是数组，形状: {compressed_dataset.shape}")
                else:
                    print(f"  未找到对应的压缩数据集")
            
            # 统计总向量数
            total_vectors = 0
            for ids_key in ids_keys[:5]:  # 只检查前5个
                try:
                    ids_dataset = f[ids_key]
                    if ids_dataset.shape == ():
                        total_vectors += 1
                    else:
                        total_vectors += len(ids_dataset[:])
                except:
                    pass
            
            print(f"\n前5个数据集的向量总数: {total_vectors}")
            
    except Exception as e:
        print(f"读取失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_hdf5_read()
