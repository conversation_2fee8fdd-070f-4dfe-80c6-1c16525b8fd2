#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试导入修复后的模块
"""

import sys
import traceback

def test_imports():
    """测试各个模块的导入"""
    print("=" * 60)
    print("测试模块导入修复")
    print("=" * 60)
    
    print(f"Python 版本: {sys.version}")
    print()
    
    # 测试基础导入
    tests = [
        ("numpy", "import numpy as np"),
        ("torch", "import torch"),
        ("transformers", "from transformers import AutoTokenizer, AutoModel"),
        ("faiss", "import faiss"),
        ("sklearn", "from sklearn.decomposition import PCA"),
    ]
    
    for name, import_cmd in tests:
        try:
            exec(import_cmd)
            print(f"✓ {name} 导入成功")
        except Exception as e:
            print(f"✗ {name} 导入失败: {e}")
    
    print()
    print("测试可选依赖:")
    
    # 测试可选依赖
    optional_tests = [
        ("sentence_transformers", "from sentence_transformers import SentenceTransformer"),
        ("umap", "from umap import UMAP"),
    ]
    
    for name, import_cmd in optional_tests:
        try:
            exec(import_cmd)
            print(f"✓ {name} 可用")
        except Exception as e:
            print(f"⚠ {name} 不可用 (这是正常的): {e}")
    
    print()
    print("测试项目模块:")
    
    # 测试项目模块
    try:
        print("正在导入 TextEmbedding...")
        from src.vectorizer.embeddings import TextEmbedding
        print("✓ TextEmbedding 导入成功")
    except Exception as e:
        print(f"✗ TextEmbedding 导入失败:")
        print(traceback.format_exc())
        return False
    
    try:
        print("正在导入 VectorTransformer...")
        from src.vectorizer.transformer import VectorTransformer
        print("✓ VectorTransformer 导入成功")
    except Exception as e:
        print(f"✗ VectorTransformer 导入失败:")
        print(traceback.format_exc())
        return False
    
    try:
        print("正在导入整个 vectorizer 模块...")
        from src.vectorizer import TextEmbedding, VectorTransformer
        print("✓ vectorizer 模块导入成功")
    except Exception as e:
        print(f"✗ vectorizer 模块导入失败:")
        print(traceback.format_exc())
        return False
    
    print()
    print("🎉 所有测试通过！问题已修复。")
    return True

if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\n现在可以正常使用向量化功能了！")
    else:
        print("\n仍有问题需要解决。")
