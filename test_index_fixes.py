#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试索引保存修复
验证三个关键问题的修复效果：
1. 索引保存覆盖问题
2. 搜索界面索引无效问题  
3. 进度条不更新问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("=" * 80)
print("🔧 测试索引保存修复")
print("=" * 80)

def test_index_0002_status():
    """测试索引0002状态"""
    print("\n1. 检查索引0002状态...")
    
    try:
        from pathlib import Path
        import pickle
        
        # 检查索引文件
        idx_file = Path("data/indices/0002.idx")
        meta_file = Path("data/indices/0002.meta")
        
        if not idx_file.exists():
            print("❌ 索引文件不存在: 0002.idx")
            return False
            
        if not meta_file.exists():
            print("❌ 元数据文件不存在: 0002.meta")
            return False
        
        # 读取元数据
        with open(meta_file, 'rb') as f:
            metadata = pickle.load(f)
        
        print("✅ 索引0002文件存在")
        print(f"   索引类型: {metadata.get('index_type', '未知')}")
        print(f"   向量维度: {metadata.get('dimension', '未知')}")
        print(f"   总向量数: {metadata.get('total_vectors', '未知')}")
        print(f"   最后更新: {metadata.get('last_update_time', '未知')}")
        print(f"   索引文件大小: {idx_file.stat().st_size / 1024:.2f} KB")
        print(f"   元数据文件大小: {meta_file.stat().st_size} bytes")
        
        return metadata
        
    except Exception as e:
        print(f"❌ 检查索引状态失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_index_loading():
    """测试索引加载功能"""
    print("\n2. 测试索引加载功能...")
    
    try:
        from src.indexer.builder import IndexBuilder
        from pathlib import Path
        import pickle
        
        # 测试加载索引0002
        index_file = Path("data/indices/0002.idx")
        meta_file = Path("data/indices/0002.meta")
        
        if not index_file.exists() or not meta_file.exists():
            print("❌ 索引文件不存在，无法测试加载")
            return False
        
        # 读取元数据
        with open(meta_file, 'rb') as f:
            metadata = pickle.load(f)
        
        # 创建索引构建器
        config = {
            'indexing': {
                'index_type': metadata.get('index_type', 'hnsw'),
                'metric': metadata.get('metric', 'cosine'),
                'ef_construction': 400,
                'ef_search': 200,
                'M': 32
            }
        }
        
        builder = IndexBuilder(config)
        
        # 尝试加载索引
        if builder.load_index(index_file):
            print("✅ 索引加载成功")
            print(f"   加载后总向量数: {builder.total_vectors}")
            print(f"   索引维度: {builder.dimension}")
            print(f"   索引类型: {builder.index_type}")
            return True
        else:
            print("❌ 索引加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试索引加载失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_search_index_validation():
    """测试搜索界面索引验证"""
    print("\n3. 测试搜索界面索引验证...")
    
    try:
        from src.gui.widgets.search import SearchWidget
        from src.utils.translator import Translator
        
        # 创建搜索组件
        translator = Translator()
        search_widget = SearchWidget(translator)
        
        # 加载索引选项
        search_widget._load_available_indices()
        
        # 检查索引选项
        combo_count = search_widget.index_combo.count()
        print(f"✅ 索引下拉菜单加载成功，选项数: {combo_count}")
        
        print("   可用索引:")
        for i in range(combo_count):
            item_text = search_widget.index_combo.itemText(i)
            item_data = search_widget.index_combo.itemData(i)
            print(f"   [{i}] {item_text} (数据: {item_data})")
        
        # 检查是否包含0002索引
        has_0002 = False
        for i in range(combo_count):
            item_data = search_widget.index_combo.itemData(i)
            if item_data == "0002":
                has_0002 = True
                print(f"✅ 找到索引0002在位置 {i}")
                break
        
        if not has_0002:
            print("❌ 未找到索引0002")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 测试搜索界面索引验证失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_vector_count_consistency():
    """测试向量计数一致性"""
    print("\n4. 测试向量计数一致性...")
    
    try:
        from src.indexer.builder import IndexBuilder
        from pathlib import Path
        import pickle
        import numpy as np
        
        # 加载索引0002
        index_file = Path("data/indices/0002.idx")
        meta_file = Path("data/indices/0002.meta")
        
        if not index_file.exists() or not meta_file.exists():
            print("❌ 索引文件不存在")
            return False
        
        # 读取元数据
        with open(meta_file, 'rb') as f:
            metadata = pickle.load(f)
        
        meta_count = metadata.get('total_vectors', 0)
        print(f"📋 元数据中的向量数: {meta_count}")
        
        # 加载索引并检查实际向量数
        config = {
            'indexing': {
                'index_type': metadata.get('index_type', 'hnsw'),
                'metric': metadata.get('metric', 'cosine')
            }
        }
        
        builder = IndexBuilder(config)
        if builder.load_index(index_file):
            actual_count = builder.total_vectors
            print(f"📊 索引中的实际向量数: {actual_count}")
            
            if meta_count == actual_count:
                print("✅ 向量计数一致")
                return True
            else:
                print(f"❌ 向量计数不一致: 元数据={meta_count}, 实际={actual_count}")
                return False
        else:
            print("❌ 无法加载索引")
            return False
            
    except Exception as e:
        print(f"❌ 测试向量计数一致性失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_search_functionality():
    """测试搜索功能"""
    print("\n5. 测试搜索功能...")
    
    try:
        from src.indexer.builder import IndexBuilder
        from pathlib import Path
        import pickle
        import numpy as np
        
        # 加载索引0002
        index_file = Path("data/indices/0002.idx")
        meta_file = Path("data/indices/0002.meta")
        
        if not index_file.exists() or not meta_file.exists():
            print("❌ 索引文件不存在")
            return False
        
        # 读取元数据
        with open(meta_file, 'rb') as f:
            metadata = pickle.load(f)
        
        # 创建索引构建器
        config = {
            'indexing': {
                'index_type': metadata.get('index_type', 'hnsw'),
                'metric': metadata.get('metric', 'cosine'),
                'ef_construction': 400,
                'ef_search': 200,
                'M': 32
            }
        }
        
        builder = IndexBuilder(config)
        if not builder.load_index(index_file):
            print("❌ 无法加载索引")
            return False
        
        # 创建测试查询向量
        dimension = metadata.get('dimension', 768)
        query_vector = np.random.random((1, dimension)).astype(np.float32)
        
        print(f"🔍 执行搜索测试，查询向量维度: {query_vector.shape}")
        
        # 执行搜索
        distances, indices = builder.search(query_vector, k=5)
        
        print(f"✅ 搜索执行成功")
        print(f"   返回结果数: {len(indices[0])}")
        print(f"   距离: {distances[0][:3]}")
        print(f"   索引: {indices[0][:3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试搜索功能失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """主函数"""
    print("开始测试索引保存修复...")
    
    # 执行所有测试
    index_status = test_index_0002_status()
    loading_ok = test_index_loading()
    validation_ok = test_search_index_validation()
    count_ok = test_vector_count_consistency()
    search_ok = test_search_functionality()
    
    # 总结
    print("\n" + "=" * 80)
    print("🎯 修复测试结果")
    print("=" * 80)
    
    print(f"索引0002状态: {'✅' if index_status else '❌'}")
    print(f"索引加载功能: {'✅' if loading_ok else '❌'}")
    print(f"搜索界面验证: {'✅' if validation_ok else '❌'}")
    print(f"向量计数一致性: {'✅' if count_ok else '❌'}")
    print(f"搜索功能: {'✅' if search_ok else '❌'}")
    
    all_ok = index_status and loading_ok and validation_ok and count_ok and search_ok
    
    print(f"\n🎯 最终结论: {'✅ 修复成功' if all_ok else '❌ 仍有问题'}")
    
    if all_ok:
        print("\n✅ 修复验证通过，现在可以测试:")
        print("1. 向量化不会覆盖现有索引，而是累加")
        print("2. 搜索界面能正确识别和加载索引0002")
        print("3. 进度条会实时更新显示处理进度")
    else:
        print("\n❌ 部分功能仍有问题，需要进一步调试")
        
    if index_status and isinstance(index_status, dict):
        print(f"\n📊 索引0002当前状态:")
        print(f"   向量数量: {index_status.get('total_vectors', '未知')}")
        print(f"   向量维度: {index_status.get('dimension', '未知')}")
        print(f"   索引类型: {index_status.get('index_type', '未知')}")

if __name__ == "__main__":
    main()
