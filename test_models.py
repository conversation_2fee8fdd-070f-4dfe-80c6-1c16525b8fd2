import os
import re

def search_unhashable_models_usage(root_dir):
    # 匹配 models 作为 key 或 set 元素的常见用法
    patterns = [
        r"\[.*models.*\]",      # 作为 key
        r"set\s*\(.*models.*\)",# 转 set
        r"models\s+in\s+\w+",   # in set/dict
        r"\.add\s*\(.*models.*\)" # set.add
    ]
    combined_pattern = re.compile("|".join(patterns))
    for dirpath, _, filenames in os.walk(root_dir):
        for filename in filenames:
            if filename.endswith('.py'):
                filepath = os.path.join(dirpath, filename)
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    for idx, line in enumerate(f, 1):
                        if 'models' in line and combined_pattern.search(line):
                            print(f"⚠️ 可能有问题: {filepath}:{idx}: {line.strip()}")

if __name__ == "__main__":
    project_root = r"F:\software\md_vector_processor"
    search_unhashable_models_usage(project_root)