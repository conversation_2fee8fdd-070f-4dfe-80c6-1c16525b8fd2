#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试Ollama连接和API调用
"""

import requests
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ollama_connection():
    """测试Ollama服务连接"""
    
    # 测试不同的API端点
    base_urls = [
        'http://localhost:11434',
        'http://127.0.0.1:11434'
    ]
    
    for base_url in base_urls:
        logger.info(f"测试连接: {base_url}")
        
        try:
            # 1. 测试基本连接
            response = requests.get(f"{base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                logger.info(f"✓ 连接成功: {base_url}")
                
                # 解析可用模型
                models_data = response.json()
                models = models_data.get('models', [])
                logger.info(f"可用模型数量: {len(models)}")
                
                for model in models:
                    model_name = model.get('name', 'unknown')
                    model_size = model.get('size', 0)
                    logger.info(f"  - {model_name} (大小: {model_size / 1024 / 1024 / 1024:.1f} GB)")
                
                # 2. 测试嵌入API
                if models:
                    test_model = models[0]['name']
                    logger.info(f"\n测试嵌入API，使用模型: {test_model}")
                    
                    embed_payload = {
                        "model": test_model,
                        "prompt": "Hello, this is a test."
                    }
                    
                    embed_response = requests.post(
                        f"{base_url}/api/embeddings", 
                        json=embed_payload,
                        timeout=30
                    )
                    
                    if embed_response.status_code == 200:
                        embed_data = embed_response.json()
                        if 'embedding' in embed_data:
                            embedding = embed_data['embedding']
                            logger.info(f"✓ 嵌入API测试成功，向量维度: {len(embedding)}")
                            logger.info(f"向量前5个值: {embedding[:5]}")
                        else:
                            logger.error(f"嵌入响应中没有embedding字段: {embed_data}")
                    else:
                        logger.error(f"嵌入API调用失败: {embed_response.status_code}")
                        logger.error(f"响应内容: {embed_response.text}")
                
                return True
                
            else:
                logger.error(f"连接失败: {base_url}, 状态码: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            logger.error(f"无法连接到: {base_url}")
        except requests.exceptions.Timeout:
            logger.error(f"连接超时: {base_url}")
        except Exception as e:
            logger.error(f"连接出错: {base_url}, 错误: {e}")
    
    return False

def test_ollama_with_config():
    """使用配置文件测试Ollama"""
    
    # 模拟配置
    config = {
        'local_models': {
            'ollama': {
                'api_url': 'http://localhost:11434/api',
                'default_model': 'qwen3:30b-a3b',
                'models': [
                    {
                        'name': 'qwen3:30b-a3b',
                        'parameters': {}
                    }
                ]
            }
        },
        'vectorization': {
            'vector_dimension': 384
        }
    }
    
    logger.info("\n使用配置测试Ollama...")
    
    try:
        from src.vectorizer.ollama import OllamaEmbedding
        
        # 创建Ollama嵌入器
        ollama_embedder = OllamaEmbedding(config)
        
        # 测试向量化
        test_text = "这是一个测试文本，用于验证Ollama向量化功能。"
        logger.info(f"测试文本: {test_text}")
        
        vector = ollama_embedder.encode_text(test_text)
        logger.info(f"✓ 向量化成功，维度: {vector.shape}")
        logger.info(f"向量前5个值: {vector[:5]}")
        
        # 测试批量向量化
        test_texts = [
            "第一个测试文本",
            "第二个测试文本", 
            "第三个测试文本"
        ]
        
        vectors = ollama_embedder.encode_batch(test_texts)
        logger.info(f"✓ 批量向量化成功，形状: {vectors.shape}")
        
        return True
        
    except Exception as e:
        logger.error(f"配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("开始Ollama连接测试...")
    
    # 基本连接测试
    connection_ok = test_ollama_connection()
    
    if connection_ok:
        # 配置测试
        config_ok = test_ollama_with_config()
        
        if config_ok:
            logger.info("\n🎉 所有测试通过！Ollama集成正常工作。")
        else:
            logger.error("\n❌ 配置测试失败，请检查代码实现。")
    else:
        logger.error("\n❌ 无法连接到Ollama服务，请确保:")
        logger.error("1. Ollama服务正在运行")
        logger.error("2. 服务监听在 localhost:11434")
        logger.error("3. 至少有一个模型已下载")
        logger.error("\n启动Ollama服务命令:")
        logger.error("ollama serve")
        logger.error("\n下载模型命令示例:")
        logger.error("ollama pull qwen:7b")
