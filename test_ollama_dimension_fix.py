#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.vectorizer.embeddings import TextEmbedding
import numpy as np

def test_ollama_dimension_fix():
    """测试Ollama维度修复"""
    print("=" * 60)
    print("测试Ollama维度修复")
    print("=" * 60)
    
    # 配置使用Ollama
    config = {
        'vectorization': {
            'model_name': 'nomic-embed-text:latest',
            'use_ollama': True,
            'batch_size': 32,
            'device': 'cpu',
            'cache_dir': 'cache',
            'normalize_vectors': True
        }
    }
    
    try:
        # 创建TextEmbedding实例
        print("1. 创建TextEmbedding实例...")
        embedder = TextEmbedding(config)
        
        print(f"   配置的向量维度: {embedder.vector_dimension}")
        print(f"   使用Ollama: {embedder.use_ollama}")
        
        if embedder.use_ollama and hasattr(embedder, 'ollama_embedder'):
            print(f"   Ollama嵌入器向量维度: {embedder.ollama_embedder.vector_dimension}")
        
        # 测试单个文本向量化
        print("\n2. 测试单个文本向量化...")
        test_text = "这是一个测试文本"
        vector = embedder.encode_text(test_text)
        
        print(f"   输入文本: {test_text}")
        print(f"   输出向量形状: {vector.shape}")
        print(f"   向量维度: {len(vector)}")
        print(f"   向量类型: {type(vector)}")
        print(f"   向量前5个值: {vector[:5]}")
        
        # 验证维度是否正确
        if len(vector) == embedder.vector_dimension:
            print("   ✅ 向量维度匹配配置")
        else:
            print(f"   ❌ 向量维度不匹配: 期望{embedder.vector_dimension}, 实际{len(vector)}")
        
        # 测试批量向量化
        print("\n3. 测试批量向量化...")
        test_texts = [
            "第一个测试文本",
            "第二个测试文本", 
            "第三个测试文本"
        ]
        
        vectors = embedder.encode_batch(test_texts)
        print(f"   输入文本数量: {len(test_texts)}")
        print(f"   输出向量形状: {vectors.shape}")
        print(f"   每个向量维度: {vectors.shape[1] if len(vectors.shape) > 1 else 'N/A'}")
        
        # 验证批量结果
        if vectors.shape == (len(test_texts), embedder.vector_dimension):
            print("   ✅ 批量向量化维度正确")
        else:
            print(f"   ❌ 批量向量化维度错误: 期望({len(test_texts)}, {embedder.vector_dimension}), 实际{vectors.shape}")
        
        # 检查向量是否为零向量（表示失败）
        if np.allclose(vector, 0):
            print("   ⚠️  警告: 返回的是零向量，可能表示向量化失败")
        else:
            print("   ✅ 向量化成功，返回非零向量")
        
        print("\n4. 测试总结:")
        print(f"   - Ollama服务可用: {embedder.ollama_embedder.service_available if hasattr(embedder, 'ollama_embedder') else 'N/A'}")
        print(f"   - 向量维度: {embedder.vector_dimension}")
        print(f"   - 单个向量化: {'成功' if len(vector) == embedder.vector_dimension else '失败'}")
        print(f"   - 批量向量化: {'成功' if vectors.shape == (len(test_texts), embedder.vector_dimension) else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ollama_dimension_fix()
    if success:
        print("\n🎉 Ollama维度修复测试完成！")
    else:
        print("\n💥 Ollama维度修复测试失败！")
