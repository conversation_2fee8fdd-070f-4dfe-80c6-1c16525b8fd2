#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("=" * 60)
print("测试Ollama向量化修复")
print("=" * 60)

# 测试1: 检查Ollama服务
print("\n1. 检查Ollama服务...")
try:
    import requests
    response = requests.get('http://localhost:11434/api/tags', timeout=5)
    if response.status_code == 200:
        models = response.json().get('models', [])
        print(f"✅ Ollama服务正常，可用模型数: {len(models)}")
        for model in models[:3]:  # 显示前3个模型
            print(f"   - {model.get('name', 'unknown')}")
    else:
        print(f"❌ Ollama服务响应异常: {response.status_code}")
        sys.exit(1)
except Exception as e:
    print(f"❌ 无法连接Ollama服务: {e}")
    sys.exit(1)

# 测试2: 检查Ollama实际返回的向量维度
print("\n2. 检查Ollama向量维度...")
try:
    response = requests.post('http://localhost:11434/api/embeddings', 
                           json={'model': 'nomic-embed-text:latest', 'prompt': 'test'}, 
                           timeout=10)
    if response.status_code == 200:
        result = response.json()
        if 'embedding' in result:
            actual_dim = len(result['embedding'])
            print(f"✅ Ollama实际返回维度: {actual_dim}")
        else:
            print("❌ API响应中没有embedding字段")
            sys.exit(1)
    else:
        print(f"❌ Ollama embedding API失败: {response.status_code}")
        sys.exit(1)
except Exception as e:
    print(f"❌ 测试Ollama embedding失败: {e}")
    sys.exit(1)

# 测试3: 测试修复后的TextEmbedding类
print("\n3. 测试修复后的TextEmbedding类...")
try:
    from src.vectorizer.embeddings import TextEmbedding
    
    # 创建TextEmbedding实例，使用Ollama
    config = {
        'model_type': 'ollama',
        'model_name': 'nomic-embed-text:latest',
        'vector_dimension': 384,  # 这个应该被动态覆盖
        'device': 'cpu'
    }
    
    print(f"   初始配置维度: {config['vector_dimension']}")
    
    embedder = TextEmbedding(config)
    print(f"✅ TextEmbedding创建成功")
    print(f"   实际使用维度: {embedder.vector_dimension}")
    
    # 测试向量化
    test_text = "这是一个测试文本"
    vector = embedder.embed_text(test_text)
    
    if vector is not None:
        print(f"✅ 文本向量化成功")
        print(f"   向量维度: {len(vector)}")
        print(f"   向量类型: {type(vector)}")
        print(f"   向量前5个值: {vector[:5]}")
        
        # 验证维度是否正确
        if len(vector) == actual_dim:
            print("✅ 向量维度与Ollama实际返回维度一致")
        else:
            print(f"❌ 向量维度不一致: 期望{actual_dim}, 实际{len(vector)}")
    else:
        print("❌ 文本向量化失败")
        
except Exception as e:
    print(f"❌ TextEmbedding测试失败: {e}")
    import traceback
    traceback.print_exc()

# 测试4: 测试批量向量化
print("\n4. 测试批量向量化...")
try:
    test_texts = [
        "第一个测试文本",
        "第二个测试文本", 
        "第三个测试文本"
    ]
    
    vectors = embedder.embed_batch(test_texts)
    
    if vectors is not None and len(vectors) > 0:
        print(f"✅ 批量向量化成功")
        print(f"   向量数量: {len(vectors)}")
        print(f"   每个向量维度: {[len(v) for v in vectors]}")
        
        # 检查所有向量维度是否一致
        dims = [len(v) for v in vectors]
        if len(set(dims)) == 1:
            print("✅ 所有向量维度一致")
        else:
            print(f"❌ 向量维度不一致: {dims}")
    else:
        print("❌ 批量向量化失败")
        
except Exception as e:
    print(f"❌ 批量向量化测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 60)
print("Ollama向量化修复测试完成")
print("=" * 60)
