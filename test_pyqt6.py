import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_pyqt_installation():
    """测试 PyQt6 安装状态"""
    try:
        # 1. 测试基本导入
        from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton
        from PyQt6.QtCore import Qt
        logger.info("PyQt6 基本模块导入成功")
        
        # 2. 测试 Qt 版本
        from PyQt6.QtCore import QT_VERSION_STR, PYQT_VERSION_STR
        logger.info(f"Qt 版本: {QT_VERSION_STR}")
        logger.info(f"PyQt 版本: {PYQT_VERSION_STR}")
        
        # 3. 测试创建应用
        app = QApplication(sys.argv)
        logger.info("QApplication 创建成功")
        
        # 4. 测试创建窗口
        window = QMainWindow()
        window.setWindowTitle("PyQt6 测试")
        window.setGeometry(100, 100, 300, 200)
        
        # 5. 测试创建按钮
        button = QPushButton("测试按钮", window)
        button.setGeometry(100, 80, 100, 30)
        
        # 6. 显示窗口
        window.show()
        logger.info("窗口创建并显示成功")
        
        # 7. 检查 Qt 插件路径
        from PyQt6.QtCore import QLibraryInfo
        plugin_path = QLibraryInfo.path(QLibraryInfo.LibraryPath.PluginsPath)
        logger.info(f"Qt 插件路径: {plugin_path}")
        
        # 8. 验证插件目录是否存在
        if Path(plugin_path).exists():
            logger.info("Qt 插件目录存在")
        else:
            logger.warning("Qt 插件目录不存在")
        
        # 9. 运行应用
        return app.exec()
        
    except ImportError as e:
        logger.error(f"PyQt6 导入失败: {e}")
        return 1
    except Exception as e:
        logger.error(f"测试过程出错: {e}")
        return 1

if __name__ == "__main__":
    logger.info("开始测试 PyQt6 安装状态...")
    sys.exit(test_pyqt_installation())