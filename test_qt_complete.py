import sys
import os
import logging
from pathlib import Path

# 设置日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 先导入所有需要的 Qt 类
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton
from PyQt6.QtCore import QLibraryInfo

import os
os.environ['QT_DEBUG_PLUGINS'] = '1'  # 启用详细日志
os.environ['QT_LOGGING_RULES'] = 'qt.core.plugin.loader=true'

# 定义主窗口类
class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PyQt6 测试")
        self.setGeometry(100, 100, 400, 200)
        
        button = QPushButton("关闭", self)
        button.setGeometry(150, 80, 100, 40)
        button.clicked.connect(self.close)

def test_qt_import():
    """测试 PyQt6 导入"""
    try:
        logger.info("PyQt6 导入成功")
        return True
    except Exception as e:
        logger.error(f"PyQt6 导入错误: {e}")
        return False

def main():
    # 检查导入
    if not test_qt_import():
        logger.error("导入 PyQt6 失败，请先运行 repair_qt.py")
        return 1
    
    # 打印环境信息
    logger.info(f"Python 版本: {sys.version}")
    logger.info(f"Qt 插件路径: {QLibraryInfo.path(QLibraryInfo.LibraryPath.PluginsPath)}")
    logger.info(f"系统 PATH: {os.environ['PATH']}")
    
    # 创建应用
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
