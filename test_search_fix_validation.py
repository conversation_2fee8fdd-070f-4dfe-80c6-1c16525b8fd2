#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证搜索模型选择修复
测试用户报告的两个关键问题是否已解决：
1. 搜索界面不使用用户选择的Ollama模型
2. 新建索引0002无法正常搜索
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("=" * 80)
print("🔧 验证搜索模型选择修复")
print("=" * 80)

def test_ollama_service():
    """测试Ollama服务"""
    print("\n1. 测试Ollama服务...")
    
    try:
        import requests
        response = requests.get('http://localhost:11434/api/tags', timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama服务正常，可用模型数: {len(models)}")
            
            # 查找嵌入模型
            embed_models = [m for m in models if 'embed' in m.get('name', '').lower()]
            if embed_models:
                print(f"   可用嵌入模型: {[m.get('name') for m in embed_models]}")
                return True
            else:
                print("   ⚠ 未找到嵌入模型")
                return False
        else:
            print(f"❌ Ollama服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接Ollama服务: {e}")
        return False

def test_ollama_embedding():
    """测试Ollama嵌入功能"""
    print("\n2. 测试Ollama嵌入功能...")
    
    try:
        import requests
        response = requests.post('http://localhost:11434/api/embeddings', 
                               json={'model': 'nomic-embed-text:latest', 'prompt': 'test search query'}, 
                               timeout=15)
        if response.status_code == 200:
            result = response.json()
            if 'embedding' in result:
                actual_dim = len(result['embedding'])
                print(f"✅ Ollama嵌入功能正常，返回维度: {actual_dim}")
                return actual_dim
            else:
                print("❌ API响应中没有embedding字段")
                return None
        else:
            print(f"❌ Ollama embedding API失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 测试Ollama embedding失败: {e}")
        return None

def test_index_0002():
    """测试索引0002状态"""
    print("\n3. 测试索引0002状态...")
    
    try:
        from pathlib import Path
        import pickle
        
        # 检查0002索引
        idx_file = Path("data/indices/0002.idx")
        meta_file = Path("data/indices/0002.meta")
        
        if idx_file.exists() and meta_file.exists():
            # 读取元数据
            with open(meta_file, 'rb') as f:
                metadata = pickle.load(f)
            
            print(f"✅ 索引0002存在")
            print(f"   索引类型: {metadata.get('index_type', '未知')}")
            print(f"   向量维度: {metadata.get('dimension', '未知')}")
            print(f"   向量数量: {metadata.get('total_vectors', '未知')}")
            print(f"   索引文件大小: {idx_file.stat().st_size / 1024:.2f} KB")
            
            return metadata
        else:
            print("❌ 索引0002不存在或不完整")
            return None
            
    except Exception as e:
        print(f"❌ 检查索引状态失败: {e}")
        return None

def test_search_config_creation():
    """测试搜索配置创建"""
    print("\n4. 测试搜索配置创建...")
    
    try:
        # 模拟搜索组件配置创建
        from src.gui.widgets.search import SearchWidget
        from src.utils.translator import Translator
        
        # 创建搜索组件实例
        translator = Translator()
        search_widget = SearchWidget(translator)
        
        # 测试768维索引的配置创建
        config_768 = search_widget._create_embedder_config_for_search(768, "ollama:nomic-embed-text:latest")
        print("✅ 768维索引配置创建成功")
        print(f"   模型名称: {config_768['vectorization']['model_name']}")
        print(f"   向量维度: {config_768['vectorization']['vector_dimension']}")
        
        # 测试384维索引的配置创建
        config_384 = search_widget._create_embedder_config_for_search(384, "sentence-transformers")
        print("✅ 384维索引配置创建成功")
        print(f"   模型名称: {config_384['vectorization']['model_name']}")
        print(f"   向量维度: {config_384['vectorization']['vector_dimension']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试搜索配置创建失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_text_embedding_creation():
    """测试文本嵌入器创建"""
    print("\n5. 测试文本嵌入器创建...")
    
    try:
        from src.vectorizer import TextEmbedding
        
        # 创建Ollama配置
        ollama_config = {
            'vectorization': {
                'model_name': 'local:ollama_nomic-embed-text_latest',
                'vector_dimension': 768,
                'batch_size': 8,
                'device': 'cpu',
                'normalize_vectors': True
            },
            'local_models': {
                'ollama': {
                    'enabled': True,
                    'api_url': 'http://localhost:11434/api',
                    'default_model': 'nomic-embed-text:latest',
                    'models': []
                }
            }
        }
        
        # 创建嵌入器
        embedder = TextEmbedding(ollama_config)
        print("✅ Ollama文本嵌入器创建成功")
        
        # 测试向量化
        test_text = "测试搜索查询文本"
        vector = embedder.encode_text(test_text)
        print(f"✅ 文本向量化成功，向量形状: {vector.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试文本嵌入器创建失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_index_search():
    """测试索引搜索功能"""
    print("\n6. 测试索引搜索功能...")
    
    try:
        from src.indexer.builder import IndexBuilder
        from pathlib import Path
        import pickle
        import numpy as np
        
        # 加载索引0002
        index_file = Path("data/indices/0002.idx")
        meta_file = Path("data/indices/0002.meta")
        
        if not index_file.exists() or not meta_file.exists():
            print("❌ 索引0002文件不存在")
            return False
        
        # 读取元数据
        with open(meta_file, 'rb') as f:
            metadata = pickle.load(f)
        
        # 创建索引构建器 - 使用优化的HNSW参数
        config = {
            'indexing': {
                'index_type': metadata.get('index_type', 'hnsw'),
                'metric': metadata.get('metric', 'cosine'),
                'ef_construction': 400,  # 增加构建参数
                'ef_search': 200,       # 增加搜索参数
                'M': 32                 # 增加连接数
            }
        }
        
        builder = IndexBuilder(config)
        if not builder.load_index(index_file):
            print("❌ 无法加载索引0002")
            return False
        
        print("✅ 索引0002加载成功")
        
        # 创建测试查询向量（768维）
        query_vector = np.random.random((1, 768)).astype(np.float32)
        
        # 执行搜索
        distances, indices = builder.search(query_vector, k=5)
        print(f"✅ 搜索执行成功，返回结果数: {len(indices[0])}")
        print(f"   距离: {distances[0][:3]}")  # 显示前3个距离
        print(f"   索引: {indices[0][:3]}")   # 显示前3个索引
        
        return True
        
    except Exception as e:
        print(f"❌ 测试索引搜索失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """主函数"""
    print("开始验证搜索模型选择修复...")
    
    # 执行所有测试
    ollama_ok = test_ollama_service()
    ollama_dim = test_ollama_embedding()
    index_meta = test_index_0002()
    config_ok = test_search_config_creation()
    embed_ok = test_text_embedding_creation()
    search_ok = test_index_search()
    
    # 总结
    print("\n" + "=" * 80)
    print("🎯 修复验证结果")
    print("=" * 80)
    
    print(f"Ollama服务: {'✅' if ollama_ok else '❌'}")
    print(f"Ollama嵌入: {'✅' if ollama_dim == 768 else '❌'} (维度: {ollama_dim})")
    print(f"索引0002: {'✅' if index_meta else '❌'}")
    print(f"配置创建: {'✅' if config_ok else '❌'}")
    print(f"嵌入器创建: {'✅' if embed_ok else '❌'}")
    print(f"索引搜索: {'✅' if search_ok else '❌'}")
    
    # 问题诊断
    print("\n🔍 问题诊断:")
    
    if ollama_ok and ollama_dim == 768:
        print("✅ Ollama服务正常，支持768维嵌入")
    else:
        print("❌ Ollama服务有问题，需要检查")
        
    if index_meta and index_meta.get('dimension') == 768:
        print("✅ 索引0002配置正确（768维HNSW）")
    else:
        print("❌ 索引0002配置有问题")
    
    if config_ok and embed_ok:
        print("✅ 搜索配置和嵌入器创建正常")
    else:
        print("❌ 搜索配置或嵌入器创建有问题")
        
    if search_ok:
        print("✅ 索引搜索功能正常")
    else:
        print("❌ 索引搜索功能有问题")
    
    # 最终结论
    all_ok = ollama_ok and ollama_dim == 768 and index_meta and config_ok and embed_ok and search_ok
    
    print(f"\n🎯 最终结论: {'✅ 修复成功' if all_ok else '❌ 仍有问题'}")
    
    if all_ok:
        print("现在可以在GUI中测试：")
        print("1. 启动GUI：python gui_run.py")
        print("2. 选择索引：0002")
        print("3. 选择模型：ollama相关模型")
        print("4. 输入查询并点击搜索")
    else:
        print("需要进一步调试和修复")

if __name__ == "__main__":
    main()
