#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("=" * 60)
print("测试搜索界面模型选择修复")
print("=" * 60)

# 测试1: 验证搜索界面的模型配置创建
print("\n1. 测试搜索界面模型配置创建...")
try:
    from src.gui.widgets.search import SearchWidget
    from src.utils.translator import Translator
    
    # 创建翻译器
    translator = Translator()
    
    # 创建搜索组件
    search_widget = SearchWidget(translator)
    
    # 测试不同场景的配置创建
    print("✓ 搜索组件创建成功")
    
    # 测试场景1: 选择Ollama模型
    config1 = search_widget._create_embedder_config_for_search(768, "ollama:nomic-embed-text:latest")
    print(f"✓ Ollama模型配置创建成功")
    print(f"   模型名称: {config1['vectorization']['model_name']}")
    print(f"   向量维度: {config1['vectorization']['vector_dimension']}")
    
    # 测试场景2: 768维索引默认配置
    config2 = search_widget._create_embedder_config_for_search(768, None)
    print(f"✓ 768维默认配置创建成功")
    print(f"   模型名称: {config2['vectorization']['model_name']}")
    print(f"   向量维度: {config2['vectorization']['vector_dimension']}")
    
    # 测试场景3: 384维索引配置
    config3 = search_widget._create_embedder_config_for_search(384, None)
    print(f"✓ 384维配置创建成功")
    print(f"   模型名称: {config3['vectorization']['model_name']}")
    print(f"   向量维度: {config3['vectorization']['vector_dimension']}")
    
except Exception as e:
    print(f"❌ 搜索界面测试失败: {e}")
    import traceback
    traceback.print_exc()

# 测试2: 验证AI聊天对话的模型配置创建
print("\n2. 测试AI聊天对话模型配置创建...")
try:
    from src.gui.widgets.ai_chat_dialog import AIChatDialog
    from src.utils.translator import Translator
    
    # 创建翻译器
    translator = Translator()
    
    # 创建AI聊天对话组件
    chat_dialog = AIChatDialog(translator)
    
    print("✓ AI聊天对话组件创建成功")
    
    # 测试不同维度的配置创建
    config1 = chat_dialog._create_embedder_config_for_chat(768)
    print(f"✓ 768维聊天配置创建成功")
    print(f"   模型名称: {config1['vectorization']['model_name']}")
    print(f"   向量维度: {config1['vectorization']['vector_dimension']}")
    
    config2 = chat_dialog._create_embedder_config_for_chat(384)
    print(f"✓ 384维聊天配置创建成功")
    print(f"   模型名称: {config2['vectorization']['model_name']}")
    print(f"   向量维度: {config2['vectorization']['vector_dimension']}")
    
except Exception as e:
    print(f"❌ AI聊天对话测试失败: {e}")
    import traceback
    traceback.print_exc()

# 测试3: 验证TextEmbedding能正确处理Ollama配置
print("\n3. 测试TextEmbedding处理Ollama配置...")
try:
    from src.vectorizer.embeddings import TextEmbedding
    
    # 创建Ollama配置
    ollama_config = {
        'vectorization': {
            'model_name': 'local:ollama_nomic-embed-text_latest',
            'vector_dimension': 768,
            'batch_size': 8,
            'device': 'cpu',
            'normalize_vectors': True
        },
        'local_models': {
            'ollama': {
                'enabled': True,
                'api_url': 'http://localhost:11434/api',
                'default_model': 'nomic-embed-text:latest',
                'models': []
            }
        }
    }
    
    print("✓ Ollama配置创建成功")
    
    # 尝试创建TextEmbedding实例
    embedder = TextEmbedding(ollama_config)
    print(f"✓ TextEmbedding实例创建成功")
    print(f"   配置的向量维度: {embedder.vector_dimension}")
    print(f"   使用Ollama: {embedder.use_ollama}")
    
    if embedder.use_ollama:
        print(f"   Ollama嵌入器向量维度: {embedder.ollama_embedder.vector_dimension}")
    
    # 测试向量化
    test_text = "测试文本向量化"
    vector = embedder.encode_text(test_text)
    
    if vector is not None:
        print(f"✓ 文本向量化成功")
        print(f"   向量维度: {vector.shape}")
        print(f"   向量类型: {type(vector)}")
    else:
        print("❌ 文本向量化失败")
    
except Exception as e:
    print(f"❌ TextEmbedding测试失败: {e}")
    import traceback
    traceback.print_exc()

# 测试4: 检查Ollama服务状态
print("\n4. 检查Ollama服务状态...")
try:
    import requests
    
    # 检查服务
    response = requests.get('http://localhost:11434/api/tags', timeout=5)
    if response.status_code == 200:
        models = response.json().get('models', [])
        print(f"✅ Ollama服务正常，可用模型数: {len(models)}")
        
        # 查找嵌入模型
        embed_models = [m for m in models if 'embed' in m.get('name', '').lower()]
        if embed_models:
            print(f"   可用嵌入模型: {[m.get('name') for m in embed_models]}")
        else:
            print("   ⚠ 未找到嵌入模型")
    else:
        print(f"❌ Ollama服务响应异常: {response.status_code}")
        
except Exception as e:
    print(f"❌ 无法连接Ollama服务: {e}")

print("\n" + "=" * 60)
print("搜索界面模型选择修复测试完成")
print("=" * 60)
print("\n建议操作:")
print("1. 在GUI搜索界面选择Ollama模型")
print("2. 执行搜索操作，观察日志是否使用了正确的模型")
print("3. 检查是否不再出现sentence_transformers降级错误")
print("4. 验证搜索结果是否正确返回")
