# VW_01054_EN_2016-01_图纸_尺寸和公差；包容要求和独立性原则.pdf

## 文档信息
- 标题：
- 作者：
- 页数：71

## 文档内容
### 第 1 页
Group standard
VW 01054
Issue 2016-01
Class. No.:
02245
Descriptors:
dimensioning, dimension line, extension line, dimension line limiting, reference line, dimension figure,
parenthesized dimension, tube bend, hole spacing, radian measure, cone dimension, taper, inclination,
equal spacing tolerance, dimension, envelope requirement, envelope principle, independency principle,
tolerancing principle
Drawings
Dimensioning and Tolerancing; Envelope Requirement and Independency
Principle
Preface
Effective starting with this issue, the envelope requirement for sizes that previously applied by de‐
fault in all cases (the "tolerancing principle" as per Volkswagen standard VW 01054) is no longer
the only option for new drawings (i.e., drawings drafted after this standard's date of publication), as
this issue introduces the independency principle as an alternative. This means that, for every new
drawing, it is necessary to choose either the envelope requirement or the independency principle
and enter it into the title block. See section 3 and section 4 for details.
Previous issues
VW 01054: 1975-07, 1990-03, 1998-10, 2005-09, 2009-02, 2011-03, 2012-02
Changes
The following changes have been made to VW 01054: 2012-02:
–
Standard title expanded
–
Standard revised; new sections added and new outline defined
–
Technical responsibility changed
–
Preface added
–
Section 1 "Scope" changed
–
Section 2 "General dimensioning and tolerancing rules" added
–
Section 3 "General applicability of envelope requirement and independency principle" added
–
Section 4 ""Envelope requirement" and "independency principle" tolerancing types for Volks‐
wagen Group drawings" added
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Page 1 of 71
Technical responsibility
The Standards department
EKDV/4
Dr.-Ing. Norbert Wisla
Tel.: +49 5361 9 23370
EKDV/4 Norbert Wisla
EKDV
Tel.: +49 5361 9 48869
Maik Gummert
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2015-07c


### 第 2 页
Page 2
VW 01054: 2016-01
–
Section 5 "Features of dimensioning" added (combines the old sections 3, 4, 5, and 6); refer‐
ence to DIN 406-12 added
–
Section 5.1 "Dimension lines, extension lines" revised, amended, and restructured; old fig‐
ures 13, 17, 19 and corresponding texts deleted; old figure 18 deleted
–
Section 5.2 "Dimension line limiting" revised, amended, and restructured; Figure 24, Figure 25,
Figure 26, and Figure 27 added
–
Section 5.3 "Dimension figures": Regulations with respect to entering deviations added;
Figure 28 (old figure 23) changed;
–
Section 5.3.1 "Dimension letters" added
–
Section 5.3.2 "Position of graphic and letter symbols with dimension numbers" added
–
Section 5.4 "Special dimensions – inside parentheses, brackets, or frames ": Old section 7,
"Dimensions with labels," revised, amended, and restructured; Figure 36 added
–
Section 5.4.1 "Auxiliary dimensions" rewritten
–
Section 5.4.2 "Control dimensions": Second sentence amended
–
Section 5.4.4 "Theoretically exact dimensions": Figure 42 updated
–
Section 5.4.5 "Fixture dimensions for tool design": Figure 43 added
–
Section 5.5 "Inspection marking for inspection features": Revised and amended; evaluation
code "M" and corresponding text deleted; old figures 35 and 36 deleted; Figure 45, Figure 46,
Table 1 added
–
Section 5.5.1 "Inspection report" added
–
Section 5.6 "Reference lines": Old figure 23 and text concerning datums deleted
–
Section 6 "Entering dimensions" added; includes old sections 10 to 22
–
Section 6.8 "Spacings" added; includes selected and revised contents from the old sections 16
and 17
–
Section 6.9 "Parallel dimensioning, increasing dimensioning" added; includes selected and re‐
vised contents from the old sections 16 and 17
–
Section 6.10 "Symmetrical parts": Reference to Figure 104 added
–
Section 6.11 "Arc dimensions, effective lengths": Old figure 123 and corresponding text de‐
leted
–
Section 6.12 "Thread dimensions": Reference to standard VW 01179 added
–
Section 6.13 "Equal spacing tolerance" amended; text concerning the explanation of the sym‐
bols for the equal spacing tolerance, Figure 135, and Table 2 added
–
Section 6.15 "Bent tubes and rods (solid material)" revised and changed
–
Old section 22.1.3, "Tube center tangent," deleted
–
Old section 22.3, "Simplified representation with dimensions specified in table," deleted
–
Section 7 "Collective information" added
–
Section 7.1 "Note on undimensioned geometries in the data set" added
–
Section 7.2 "General tolerances for linear and angular sizes" added
–
Section 7.3 "Profile tolerances with respect to the data set and defined RPS reference system"
(old section 9, "Surface shape tolerance and trim tolerance with respect to the data set") re‐
vised
–
Section 8 "Applicable documents" updated
–
Appendix A added


### 第 3 页
Page 3
VW 01054: 2016-01
–
Appendix B added
–
Appendix C "Definitions" added


### 第 4 页
Page 4
VW 01054: 2016-01
Contents
Page
Scope ......................................................................................................................... 5
General dimensioning and tolerancing rules .............................................................. 5
General information .................................................................................................... 5
General rules .............................................................................................................. 5
General applicability of envelope requirement and independency principle .............. 7
General applicability of envelope requirement for sizes ............................................. 7
Envelope requirement ................................................................................................ 7
Independency principle .............................................................................................. 8
"Envelope requirement" and "independency principle" tolerancing types for
Volkswagen Group drawings ..................................................................................... 8
"Envelope requirement" tolerancing for Volkswagen Group drawings ....................... 8
Rule for drawings older than this issue of the standard ........................................... 10
Applicability of the independency principle to Volkswagen Group drawings ............ 11
Applicability of the envelope requirement or the independency principle to
Volkswagen Group drawings with an external restriction on use ............................. 12
Supplier drawings without a note specifying the tolerancing type ............................ 13
Features of dimensioning ......................................................................................... 13
Dimension lines, extension lines .............................................................................. 14
Dimension line limiting ............................................................................................. 16
Dimension figures .................................................................................................... 17
Dimension letters ..................................................................................................... 19
Position of graphic and letter symbols with dimension numbers .............................. 19
Special dimensions – inside parentheses, brackets, or frames ............................... 19
Auxiliary dimensions ................................................................................................ 19
Control dimensions .................................................................................................. 20
Final dimensions for parts manufactured using non-machining operations ............. 20
Theoretically exact dimensions ................................................................................ 21
Fixture dimensions for tool design ........................................................................... 22
Inspection marking for inspection features ............................................................... 22
Inspection report ...................................................................................................... 24
Reference lines ........................................................................................................ 24
Entering dimensions ................................................................................................. 25
Repeat dimensions .................................................................................................. 25
Radii ......................................................................................................................... 25
Diameter symbol ...................................................................................................... 28
Sphere ...................................................................................................................... 29
Square symbol, diagonal cross, widths across flats ................................................. 30
Cone, taper, incline .................................................................................................. 31
Grooves .................................................................................................................... 33
Spacings .................................................................................................................. 36
Parallel dimensioning, increasing dimensioning ...................................................... 36
Symmetrical parts .................................................................................................... 37
Arc dimensions, effective lengths ............................................................................. 38
Thread dimensions ................................................................................................... 39
Equal spacing tolerance ........................................................................................... 42
Workpiece edges ..................................................................................................... 43
Bent tubes and rods (solid material) ........................................................................ 43
Dimensioning in coordinate system and tolerancing ................................................ 44
Total length and perpendicularity of the tubes' end faces ........................................ 44
Collective information ............................................................................................... 46
1
2
2.1
2.2
3
3.1
3.1.1
3.2
4
4.1
4.1.1
4.2
4.3
4.3.1
5
5.1
5.2
5.3
5.3.1
5.3.2
5.4
5.4.1
5.4.2
5.4.3
5.4.4
5.4.5
5.5
5.5.1
5.6
6
6.1
6.2
6.3
6.4
6.5
6.6
6.7
6.8
6.9
6.10
6.11
6.12
6.13
6.14
6.15
6.15.1
6.15.2
7


### 第 5 页
Page 5
VW 01054: 2016-01
Note on undimensioned geometries in the data set ................................................. 46
General tolerances for linear and angular sizes ....................................................... 46
General tolerances for nominal dimensions without tolerance specification ............ 46
General tolerances for undimensioned linear and angular sizes ............................. 47
Profile tolerances with respect to the data set and defined RPS reference
system ...................................................................................................................... 48
Applicable documents .............................................................................................. 50
Envelope requirement explanations ......................................................................... 52
Taylor Principle ........................................................................................................ 52
Feature-of-size ......................................................................................................... 54
Sizes ........................................................................................................................ 57
Specification modifiers for sizes as per DIN EN ISO 14405-1 ................................. 59
Envelope requirement drawing note ........................................................................ 62
Independency principle explanations ....................................................................... 64
Length dimension tolerances ................................................................................... 64
Angular dimension tolerance .................................................................................... 66
Problem concerning standards when it comes to the general applicability of the
envelope requirements without a drawing note as per DIN 7167 ............................ 67
Modifications to Volkswagen standards (VW 01014, VW 01054, and VW 01155) .. 67
Definitions ................................................................................................................ 71
7.1
7.2
7.2.1
7.2.2
7.3
8
Appendix A
A.1
A.1.1
A.1.2
A.1.3
A.1.4
A.2
A.2.1
A.2.2
Appendix B
B.1
Appendix C
Scope
This standard applies to the dimensioning and tolerancing used in engineering drawings, in the
three-dimensional drawing-less process (3DZP), and in other documents involved in the develop‐
ment process.
This standard adheres to the international rules for engineering drawings to the greatest extent
possible and has been expanded to meet the specific requirements of the Volkswagen Group.
Details that are not covered in this standard must be selected as necessary to meet the specific
purpose at hand. Moreover, they must be unambiguous.
NOTE 1: The figures given in this standard are examples to illustrate the relevant rule. They are
complete only to the extent that they show the situation described. The indicated dimensions and
tolerances are intended only as example values.
General dimensioning and tolerancing rules
General information
In the design process, the components are designed with the help of CAD systems from geometri‐
cal features with nominal dimensions and ideal geometrical shapes. These designed nominal com‐
ponent models are referred to as 3-D models or data sets.
Due to the various factors involved in the manufacturing process, the components actually pro‐
duced will have dimensional, shape, and positional deviations. These deviations must be limited
with appropriate, function-related dimensional, location, orientation, runout, and form tolerances in
order to ensure that the component will work properly.
General rules
The following general dimensioning and tolerancing rules must be observed:
1  
2  
2.1  
2.2  


### 第 6 页
Page 6
VW 01054: 2016-01
–
The type of tolerancing (envelope requirement for sizes (see section A.1.1) or independency
principle) must be specified and entered into the drawing's title block.
–
A datum system must be defined before dimensioning. The nominal dimensions must be en‐
tered based on the datum features. The datum features must be toleranced relative to each
other. The tolerancing is carried out at the end in the following order: dimensional, location,
orientation, and form tolerances. The function and assembly of individual parts play a defining
role when it comes to the dimensioning and tolerancing.
–
The dimensions in a drawing are finished dimensions and indicate the end condition of the
part, including surface protection. Deviations from this, e.g., pre-turning dimensions, are
marked as such with text.
–
In order for the drawing to be easily understandable, dimensions must be entered outside the
graphical representation whenever possible and only once for each geometrical feature (ex‐
ception: repeat dimensions; see section 6.1).
–
If dimensions cannot be made to fit in small graphical representations, the representations, or
appropriate details, must be drawn using a larger scale (see VW 01050).
–
In assembly (ASSY) drawings, only the dimensions that are necessary for assembly or for fur‐
ther machining are entered, e.g., parts that are drilled once assembled. Because of this, di‐
mensions and tolerances used to manufacture parts must be specified in the part drawings.
In the 3-D drawing-less process, parts without their own drawing (no drawing) cannot be pro‐
cessed; see VW 01058 supplement 1, "Requirements" section. Separate drawings must be
drafted for these individual parts.
–
The dimensioning and tolerancing must be complete. This means that all necessary geometric
requirements needed in order for the parts to be manufactured and checked must be specified
in the drawing or 3-D model. The dimensional, form, and positional deviations relevant to func‐
tional capability must be limited for all geometrical features by using individual and/or general
tolerances.
–
The dimensioning and tolerancing must be unequivocal, i.e., it must be impossible for there to
be any ambiguities in downstream processes, e.g., production and/or quality assurance.
–
The form and positional tolerances must be entered as per DIN EN ISO 1101.
–
The datums and datum systems must be entered as per DIN EN ISO 5459.
–
The reference point system (RPS) datum systems must be entered as per VW 01055.
–
If dimensional, form, and positional tolerance specifications cannot describe all details, addi‐
tional, clear text specifications must be prepared and entered in German and English using the
"DoLittle" Volkswagen application, see VW 01058, section "Requirements."


### 第 7 页
Page 7
VW 01054: 2016-01
General applicability of envelope requirement and independency principle
By defining the type of tolerancing in an engineering drawing, i.e., whether the independency prin‐
ciple is being used or the envelope requirement for sizes is generally applicable, a design engineer
defines how the components will be checked afterwards. Because of this, it is absolutely necessary
for every single drawing to indicate the specific type of tolerancing that is being used.
General applicability of envelope requirement for sizes
When the envelope requirement for sizes as per DIN EN ISO 14405-1 (tolerancing for inspection
with gages; see section A.1) is generally applicable, the envelope requirement automatically ap‐
plies to all geometrical features (referred to as "features-of-size"), of "cylindrical surface" type (cyl‐
inders – e.g., hole, shaft, pin) or "two parallel opposite planes" type (e.g., width – e.g., thickness,
groove width, slot width, and key width), toleranced with dimensional tolerances (either "±" toleran‐
ces or ISO codes, e.g., H7) without a direct marking with symbol Ⓔ after the dimensional toler‐
ance.
Envelope requirement
The envelope requirement as per DIN EN ISO 14405-1 defines a correlation between dimensional
tolerance, form deviation, and parallelism deviation for sizes. It states that the dimensional devia‐
tion, the form deviation, and the parallelism deviation must not be greater than the dimensional tol‐
erance of the toleranced geometrical feature. In other words, it states that for proper fit, a feature-
of-size (see section A.1.1) of type "cylinder" or of type "two parallel opposite planes" must not pen‐
etrate a geometrically ideal envelope representing the maximum material size (go gage). At the
same time, no actual local size must exceed (e.g., hole and groove width – internal dimensions) or
fall below (e.g., shaft and key – external dimensions) the least material size (no go gage – two-
point size; see section A.1.2).
In practice, the envelope requirement is checked as per the rules of the Taylor Principle; see
section A.1.
The envelope requirement must only be used if the function requires a specific fit for two geometri‐
cal features (e.g., hole, shaft or groove, key) as per DIN EN ISO 286-1 and DIN EN ISO 286-2.
The envelope requirement must be used preferentially and sensibly for geometrical features that
are paired for a clearance fit. The geometrical features must be able to physically pass a go-gage
check as per the Taylor Principle.
The envelope requirement cannot be used for:
–
Features-of-size such as cones, wedges, spheres
–
Non-features-of-size such as:
– Complex geometrical features (for examples, see figure A.5) and
– Distances that are dimensions other than linear sizes (for examples, see figure A.6 to
figure A.8 and DIN EN ISO 14405-2).
3  
3.1  
3.1.1  


### 第 8 页
Page 8
VW 01054: 2016-01
Independency principle
The independency principle specifies that all dimensional, form, and positional tolerances in a
drawing must be checked and adhered to independently of each other unless a special note (see
section A.2) indicating otherwise has been entered.
This means that:
–
There is no correlation between the dimensional tolerance, shape deviation, and parallelism
deviation and all required form and parallelism tolerances for features-of-size must be speci‐
fied
–
By default, the sizes are local two-point sizes (see section A.1.2) as per DIN EN ISO 14405-1
i.e., they must be measured as the distance between two opposite points using what is refer‐
red to as the "two-point measuring method"
–
If the envelope requirement needs to apply to individual sizes due to functional reasons, the
requirement must be entered directly after the dimensional tolerance with the Ⓔ symbol; see
section A.1.3 and section A.1.4
For more information on the independency principle, see section A.2.
"Envelope requirement" and "independency principle" tolerancing types for Volkswagen
Group drawings
"Envelope requirement" tolerancing for Volkswagen Group drawings
If the "envelope requirement" as per DIN EN ISO 14405-1 applies to all sizes in Volkswagen Group
drawings, the draftsperson must use the current drawing frame for development (production draw‐
ing) as per VW 01014 with the integrated reference to VW 01054 in the "Unterlagen / References"
section (see figure 1) and by the entry of the text macro NO-A11, "Huellbedingung / envelope re‐
quirement", from VW 01014 in order to ensure that the requirement is applied (see figure 2). The
text can be entered automatically or manually depending on how the CAD systems being used are
programmed. The created note (see figure 2) has the same meaning as the drawing note as per
DIN EN ISO 14405-1 shown in figure 3.
Figure 1 – "Unterlagen / References" field
3.2  
4  
4.1  


### 第 9 页
Page 9
VW 01054: 2016-01
Legend
1
Text macro NO-A11 from VW 01014
Figure 2 – Example for the drawing note for the general applicability of the envelope requirement to
sizes on Volkswagen Group drawings
Figure 3 – Example for the drawing note for the general applicability of the envelope requirement to
sizes as per DIN EN ISO 14405-1 (conformity is checked with a gage)
The drawing frame in the currently valid issue of VW 01014 must always be used when drafting
new drawings and making drawing changes.
During the transition period, that is, until the drawing frame has been implemented in accordance
with the currently valid issue of VW 01014 for the CAD systems in use in the Volkswagen Group,
the drawing frame as per the previous issue of VW 010141) may be used.
After a drawing change, drawings that do not contain a note indicating the general applicability of
the envelope requirement or the independency principle (for an example, see figure 4) must no lon‐
ger be stored in the Engineering Data Management System (HyperKVS) with the old drawing
frame.
1)
For the use of the drawing frame as per VW 01014: 2012-09 (for an example, see figure 5), the note on the general applicability of
the envelope requirement or the independency principle must be entered next to the title block.


### 第 10 页
Page 10
VW 01054: 2016-01
Rule for drawings older than this issue of the standard
The envelope requirement is generally applicable to all Volkswagen Group drawings if:
1) They do not include a note indicating that the envelope requirement must be used and their ini‐
tial release date is before VW 01054, issue 2010-05 was published. For an example, see figure 4.
Figure 4 – Example showing the title block for a drawing without a note indicating the general ap‐
plicability of the envelope requirement to Volkswagen Group drawings
2) They were drafted in accordance with specifications of VW 01054, issue 2011-03 and 2012-02,
and contain the following text "Tolerierungsgrundsatz nach VW 01054. Tolerancing principle acc.
to VW 01054." in the title block or in the "Bemerkungen / Notes" field. For an example, see
figure 5.
Figure 5 – Example showing the old drawing note for the general applicability of the envelope re‐
quirement to Volkswagen Group drawings
4.1.1  


### 第 11 页
Page 11
VW 01054: 2016-01
Applicability of the independency principle to Volkswagen Group drawings
If the independency principle as per DIN EN ISO 8015 applies to Volkswagen Group drawings, the
draftsperson must use the current drawing frame for development (production drawing) as per
VW 01014 with the integrated reference to VW 01054 in the "Unterlagen / References" field (see
figure 1) and by the entry of text macro NO-A13, "Unabhaengigkeitsprinzip / independency princi‐
ple", from VW 01014 in order to ensure that the principle is applied (see figure 6). The text can be
entered automatically or manually depending on how the CAD systems being used are program‐
med.
The created note (see ) has the same meaning as the drawing note as per DIN EN ISO 8015
shown in figure 7.
Legend
1
Text macro NO-A13 from VW 01014
Figure 6 – Example for the drawing note for the independency principle for Volkswagen Group
drawings
Figure 7 – Example for the drawing note for the independency principle as per DIN EN ISO 8015.
4.2  


### 第 12 页
Page 12
VW 01054: 2016-01
Applicability of the envelope requirement or the independency principle to Volkswagen
Group drawings with an external restriction on use
For Volkswagen Group drawings with an external restriction on use, the tolerancing type specified
in the supplier drawing applies automatically.
When changing supplier drawings to Volkswagen Group drawings with an external restriction on
use, the instructions in section 4.1 and section 4.2 must be followed as appropriate. Current text
macros NO-A7 to NO-A10 from VW 01014 as per VW 01058, section "Changing supplier drawings
to Volkswagen AG drawings with a restriction on use," must be used; see figure 8.
Figure 8 – Example for the drawing note for the general applicability of the envelope requirement to
Volkswagen AG drawings with an external restriction on use
4.3  


### 第 13 页
Page 13
VW 01054: 2016-01
For more details on changing supplier drawings to Volkswagen Group drawings with an external
restriction on use, see:
–
"User Manual – Restrictions on Use for Data Providers and Data Recipients," available in Hy‐
perKVS. Path: "Extras Documentation/manuals" → "User documentation" → "Verwendungsbes‐
chränkung" (restriction on use)
–
"Criteria for Release-Compliant Drawings" (KfZ guideline). Available in HyperKVS, path "Ex‐
tras Dokumentation / Handbücher" → "Anwenderdokumentation" → "Stückliste und Freigabe"
(Extras documentation/manuals → User documentation → Bill of materials and release)
Supplier drawings without a note specifying the tolerancing type
If a supplier drawing needs to be changed to a Volkswagen Group drawing with a restriction on use
and does not contain a note indicating the general applicability of the envelope requirement or in‐
dependency principle, the independency principle applies automatically to the drawing as per
DIN EN ISO 8015.
In Germany, the envelope requirement applied to all features-of-size of type "cylinder" or type "two
parallel opposite planes" until April 2011 as per DIN 71672): 1987-01, provided the corresponding
drawings were based on DIN standards. Because of this, it is strongly recommended to contact the
supplier in order to clarify the requirements with regard to these drawings.
Features of dimensioning
Dimensioning is performed with the help of the features shown in figure 9.
Entering tolerances of length and angle dimensions, see DIN 406-12.
Legend
1
Dimension line
2
Dimension figure (dimension figure with symbol of tolerance class, dimension figure
with deviations)
3
Dimension line limiting
4
Extension line
5
Reference lines for entering dimensions
Figure 9 – Features of dimensioning
4.3.1  
5  
2)
DIN 7167: 1987-01 – Relationship between tolerances of size, form, and parallelism; envelope requirement without individual indica‐
tion on the drawing


### 第 14 页
Page 14
VW 01054: 2016-01
Dimension lines, extension lines
Dimension lines are generally drawn perpendicular to the corresponding part edges and parallel to
the indicated dimension between extension lines.
The dimension lines must be placed approximately 10 mm away from the part edges, see
figure 10. If otherwise impossible, they may also be entered between the shown part edges, see
figure 11. If there are several parallel dimension lines, they must be evenly spaced, if possible, at
intervals of approximately 7 mm. Center lines and part edges must not be used as dimension lines.
Dimension lines must be drawn in one continuous stroke with the dimension figure placed above
the dimension line, see figure 10 and figure 11, or as per figure 12 in case of insufficient space.
Figure 10
Figure 11
Figure 12
Dimension lines must not intersect with each other or with other lines. If it is unavoidable, they are
drawn with no breaks, see figure 13. Center lines, extension lines, and hatching must be interrup‐
ted in areas around dimensions, see figure 14.
Figure 13
Figure 14
Extension lines (with the same width as the dimension lines) connect the dimension lines to the
part edges. They are usually perpendicular to the dimension line. In individual cases, however, the
extension lines are allowed to be drawn at an angled position (preferably under 60°) parallel to
each other when necessary in order to make the drawing easier to understand; see figure 15. Cen‐
ter lines may be used as extension lines; in this case they are drawn as narrow solid lines outside
of the part edges, see figure 16.
5.1  


### 第 15 页
Page 15
VW 01054: 2016-01
Figure 15
Figure 16
Extension lines may be interrupted, if it is clearly recognizable where they are continued, see
figure 17.
Figure 17
For arc and angle dimensions, the dimension line is a circular arc positioned concentric to the cen‐
ter of the circle or the vertex of the angle; see figure 18 and figure 19. For chord dimensions, it runs
perpendicular to the median line; see figure 20.
For angle dimensions, the extension lines form an extension of the angle legs; for chord and arc
dimensions up to 90°, they run parallel to the median line. For more details on arc dimensions, see
section 6.11.
Figure 18
Figure 19
Figure 20
The dimension lines may be shortened in the case of views or sections that are only drawn up to
the line of symmetry – see figure 21 – and in the case of half sections – see figure 22. In this case,
the dimension lines have only one dimension line termination, but extend beyond the symmetry ax‐
is. Extension lines must not be drawn through from one view to another (full view/section) or drawn
parallel to hatching lines.


### 第 16 页
Page 16
VW 01054: 2016-01
Figure 21
Figure 22
If there is a large number of individual distances, dimension lines with only one dimension line ter‐
mination (dimensioning arrow) are permissible. In these cases, as per figure 28, the starting point
must be clearly marked with a circle (see figure 26).
Dimension line limiting
Arrowhead
Dimension lines are preferably terminated with a solid arrowhead. This arrowhead must have the
shape of an isosceles triangle with a vertex angle of 15° and a length equaling 10 times the width
of the dimension line. In the case of dimensioning arrows that need to be drawn in places where
there are black areas, recesses with an approx. width of 5 mm must be used; see figure 23.
Figure 23
Dot
Dimension lines may also be terminated by dots in the event of insufficient space, see figure 24
and figure 33. The dot has a diameter of 5 times the width of the dimension line. The center of the
dot is placed at the intersection point of the dimension line and extension line or part edge.
Slash
Dimension lines can be terminated by a slash that runs from the bottom left to the top right at 45°
relative to the orientation for reading the drawing and has a length equaling 12 times the width of
the dimension line. The center of the slash runs through the intersection point of the dimension line
and the extension line, see figure 25 and figure 27.
Circle for indicating the origin
Dimension lines can be terminated by an unfilled circle for indicating the origin. The circle has a
diameter of 8 times the width of the dimension line. The center of the circle is placed on the inter‐
section point between the dimension line and the line of the datum feature at the location where
the defined dimension begins; see figure 26. The symbol is used for increasing dimensions; see
figure 28 and figure 29.
5.2  


### 第 17 页
Page 17
VW 01054: 2016-01
Figure 24
Figure 25
Figure 26
Figure 27
Dimension figures
In engineering drawings, the unit for the dimension figure is the millimeter. This unit is not indica‐
ted; however, other units, e.g., µm, kg, ° (degrees), must be indicated after the relevant figure. De‐
viations are indicated in the same units as the nominal dimensions.
For the dimensions, only the dimension figures are valid, never the graphical representation.
Dimension figures must be written in a font size of 3.5 mm (exceptions: 2.5 mm) type style B, verti‐
cal as per DIN EN ISO 3098-2. Their position is dependent on the direction of the dimension line,
see figure 30 and figure 31. They are placed above the dimension line; if there is not enough
space, the examples in figure 32 and figure 35 must be followed. In exceptional cases, if it does
not impair clarity, datum lines may be used, see figure 33. The figures are still written in the direc‐
tion of the dimension line. The figures must not be separated or crossed by lines. They must also
not be positioned on edges or intersection points of lines. All dimension numbers must be readable
from the bottom or from the right side as appropriate for their orientation. In the case of increasing
dimension figures, the figures must be entered close to the dimension line termination, in parallel
with or tangential to and significantly above the dimension line, see figure 28 and figure 29.
The font size for the deviations must be the same as the size for the nominal dimensions.
The two deviations must always be indicated, even if one of the two deviations is zero. For exam‐
ples, see figure 40, figure 41, and DIN 406-12.
5.3  


### 第 18 页
Page 18
VW 01054: 2016-01
Figure 28
Figure 29
Figure 30
Figure 31
Figure 32
Figure 33
Figure 34
Figure 35
The numbers 6 and 9, as well as their combinations and combinations with the number 8, e.g., 68
or 89, must be followed by a dot, if there is a risk of confusion, see figure 34.
Dimensions with decimals generally contain a comma (it may be necessary to use a point instead
of a comma, depending on the settings in the CAD system).


### 第 19 页
Page 19
VW 01054: 2016-01
Dimension letters
Instead of dimension numbers, dimension letters (lowercase letters) may be used in table draw‐
ings, e.g., length = I, distance = d, diameter = dia, height = h, thickness = t, width = w. If there are
several diameters, lengths, etc., in one drawing, indices must be used with the dimension letters in
order to differentiate between them, e.g., d1, d2. The font size for the dimension letters is the same
as for dimension numbers. The numeric values for the letters are compiled in a table.
Position of graphic and letter symbols with dimension numbers
The following symbols (referred to as "prefixes") must be placed directly before the dimension
number without a space in between; see section 6.2, section 6.3, and section 6.5.
ø
Diameter
R
Radius
Square
Sø
Diameter of a spherical feature
SR
Radius of a spherical feature
⌒
Arc length
t=
Thickness of a workpiece (e.g., sheet)
Special dimensions – inside parentheses, brackets, or frames
Special dimensions (inside parentheses, brackets, or frames) are not used for the production of a
part and must be marked accordingly. Their meaning is preprinted on the bottom edge of the draw‐
ing (see figure 36). The meaning of other special dimensions must be explained in the drawing in
the "Bemerkungen / Notes" field.
Figure 36
Curly brackets { } are used to put multiple specifications together.
Auxiliary dimensions
Auxiliary dimensions (informational dimensions) are not required for the geometrical definition of a
part. They supply additional information and are used only for understanding. They indicate, for ex‐
ample:
–
The sum of length or angle dimensions (for examples, see figure 106 and figure 107) or
–
the dimensional relationship to other parts or to ruled lines (for an example, see figure 37).
So that they do not lead to overdetermination, they are placed inside parentheses and thus de‐
clared as auxiliary dimensions. Auxiliary dimensions are not allocated a tolerance and not consid‐
ered part of the contract.
5.3.1  
5.3.2  
5.4  
5.4.1  


### 第 20 页
Page 20
VW 01054: 2016-01
Control dimensions
Control dimensions are placed between square brackets [  ], see figure 36 b. They are used to indi‐
cate dimensions on parts that must be kept as finished dimensions in a next higher processing
stage, e.g.:
–
In ASSY drawings for dimensions that have already been dimensioned in a part drawing but
that must not change, or that may only change up to a specific value, during assembly, e.g.,
welding; for an example, see figure 38.
–
In part drawings as an inspection dimension in addition to the manufacturing dimensions, e.g.,
to ensure a proper fit after heat treatments.
Figure 37
Figure 38
Final dimensions for parts manufactured using non-machining operations
Final dimensions are identified with flattened arcs above and below the dimension number 
;
see figure 36 c) and figure 39. They are entered on the original drawing if, during acceptance,
Quality Assurance determines that a tolerance has been exceeded but that this will not affect the
vehicle's operation.
The marking must be used only on parts produced without cutting and only if the Development, In‐
spection, and Planning departments approve this on the inspection report. Final dimensions are
therefore not corrected in the drawing and are not checked during production; their marking is not
entered in the drawing change block either. When new tools are produced based on this drawing,
the dimension entered in the drawing represents the ideal.
Figure 39
5.4.2  
5.4.3  


### 第 21 页
Page 21
VW 01054: 2016-01
Theoretically exact dimensions
Theoretically exact dimensions are enclosed in a rectangle 
; see figure 36 d. They do not
have tolerances, are theoretically exact, and are specified for purely theoretical values (not func‐
tional dimensions), e.g., for distances between a datum plane or part edge to a measuring plane
used as a basis for dimensioning; see figure 40 and figure 41.
Figure 40
Figure 41
In connection with shape and positional deviations, theoretically exact dimensions are used to de‐
fine the ideal geometric shape and position of the tolerance zones (form and positional tolerancing
as per DIN EN ISO 1101). For an example, see figure 42.
Figure 42
5.4.4  


### 第 22 页
Page 22
VW 01054: 2016-01
Fixture dimensions for tool design
Fixture dimensions are placed between angle brackets <  >; see figure 43a. They are of impor‐
tance for tool design purposes only and are generally entered only if specifically requested by the
Planning department; see figure 44.
Figure 43
Figure 44
Inspection marking for inspection features
Inspection features are important geometrical properties such as dimension, form, position, and
surfaces of components and system ASSYs, whose non-compliance can have a large influence on
function, safety, installation capability, reliability, and further processing and therefore must be ad‐
hered to in particular.
Inspection features are defined jointly by Design Engineering, Production Planning, and Quality As‐
surance (purchaser) and are marked with an inspection marking. Inspection features must be
checked and documented in particular for the part inspection (contractor, e.g., production testing,
checking of outgoing goods by the supplier, or checking of incoming goods (purchase parts) by the
part purchaser).
The "inspection marking" graphic symbol (see figure 45 and table 1) consists of a frame as per
DIN 406-10 that can be divided into sections using vertical lines. Some valid examples are shown
in figure 46.
Figure 45 – Basic graphical symbol
"Test marking"
Figure 46 – Basic graphical symbol "Test marking"
divided into fields – Designs
If the random sample size, frequency of testing, and report contents are not specified by the pur‐
chaser, they must be agreed upon explicitly with the purchaser.
The divided fields can contain notes with respect to the percentage that must be tested, as well as
letter and/or number codes that are explained outside of the graphical representation in assembled
form.
Additional definitions must be specified in the drawing/test specification:
5.4.5  
5.5  


### 第 23 页
Page 23
VW 01054: 2016-01
–
The testing scope (random sample size – how many parts)
–
The frequency of testing (how often tests are performed)
–
How the measurement results must be recorded
For examples of the marking of inspection features and their explanation, see table 1.
It is hereby explicitly noted that inspection features are not a replacement for the usual production
tests and quality checks. Within the scope of its production responsibility, the internal or external
supplier is responsible for adhering to all the dimensional, form, and positional tolerances specified
in the drawing and to check them accordingly.
Table 1 – Examples of markings and explanation of the inspection features
No.
Symbols in the graphical representation
Explanation of the meanings in the drawing in the "Bemerkungen/Notes"
field
1
= functionally relevant feature (text for test
setup or reference to corresponding docu‐
ments)
2
= for acceptance 100% inspection
3
= adhere to Cpk value
4
= part must be checked in vertical position
5
= (text for test setup)
6
= ultrasonic testing on weld
 
 
If the test requirements are clear, the explanatory notes are omitted.


### 第 24 页
Page 24
VW 01054: 2016-01
Inspection report
The inspection report is used to record the results of the inspection feature checks in writing during
the component inspection, e.g., with the following scope:
–
Designation of the component or system tested
–
Designation of the current test requirements, e.g., production drawings or test specifications,
including change suffix
–
Test location/date
–
Test equipment used
–
Designation or reference system for each test datum point
–
Desired values with tolerances
–
Measured actual values; minimum, maximum, and average values; standard deviation
The contractor performs the production inspections and outgoing goods inspections as per its
scope of responsibilities and any existing special agreements.
The inspection reports must be submitted to the purchaser as proof of adherence to the inspection
features required in the drawing.
Reference lines
Reference lines are narrow solid lines that extend out from the graphical representation at an an‐
gle, such that they cannot be confused with part edges or other lines, see figure 47. They end with
a dot when indicating shown surfaces (see figure 47, figure 67, figure 83, figure 96, figure 97), with
an arrowhead when indicating part edges (see figure 44 and figure 48), and without a terminating
symbol for all other lines, e.g., dimension lines, center lines (see figure 27 and figure 33). Where
there is insufficient space, reference lines may also be used as datum lines for dimensions; see
figure 33.
Figure 47
Figure 48
5.5.1  
5.6  


### 第 25 页
Page 25
VW 01054: 2016-01
Entering dimensions
Repeat dimensions
As a rule, in a drawing, one dimension must have only one dimension figure. If, in exceptional ca‐
ses, a dimension needs to be repeated in the same drawing, the dimension number must still ap‐
pear only once. Both dimensions are marked with dimension letters, which are explained under
"Bemerkungen / Notes" in the lower left-hand corner of the drawing, see figure 49.
Figure 49
Radii
Radii are always marked by the capital letter R, which must be placed before the dimension figure.
The dimensioning arrowhead is drawn, wherever possible, from the inside pointing outwards, see
figure 50 to figure 52; if there is insufficient space, it may be drawn from the outside touching the
circular arc, see figure 53 to figure 55. In this case, the radius is drawn to the center point and
marked by a center line cross, if its position is required for operation, production, or acceptance.
Figure 50
Figure 51
Figure 52
Figure 53
Figure 54
Figure 55
If the center point cannot be indicated for small radii, see figure 56 and figure 57, or is outside the
drawing surface for large radii, the marking may be omitted, if the case is clear and unambiguous.
If, in exceptional cases, the view must include a dimension where the radius does not appear as a
circular arc – see figure 59 –, the radius starting point must always be indicated.
6  
6.1  
6.2  


### 第 26 页
Page 26
VW 01054: 2016-01
Figure 56
Figure 57
If there might be confusion in terms of the position of the center point, e.g., short circular arcs with
large radii, see figure 58, the radius must always be indicated from the inside, i.e., from the center
point.
Figure 58
Figure 59
If the radius is indicated by the width, e.g., figure 60, the circular form is expressed by a single R. If
the center point for large radii must be dimensioned, the extension of the dimension line that runs
to the circular arc must be directed to the actual center point; the bend must be a right angle, see
figure 61. Straight lines (no bend) must be used for drawings created using CAD technology.
Figure 60
Figure 61
Several radii with a common center point are drawn to an auxiliary circular arc only or end with a
shortened dimension line; see figure 62.
If required for clarity, auxiliary circles as per figure 63 are permissible.


### 第 27 页
Page 27
VW 01054: 2016-01
Figure 62
Figure 63
The dimension lines for several radii of the same size may be combined; see figure 64. Radii on
bent and drawn parts are dimensioned on the inside, as the outside is generally flattened; see
figure 65.
Figure 64
Figure 65
Instead of using sections to represent rounded shapes, the radius center point line, as a tangent
line, may be drawn and dimensioned for both constant and changing radii; see figure 66 (RE = tan‐
gent line).
Figure 66
If there are several views in the drawing, the evenly changing profile may be entered as per
figure 67.


### 第 28 页
Page 28
VW 01054: 2016-01
Figure 67
Constant radii used all around on parts that do not have a circular shape are specified only once
and identified with "all around"; see figure 68 If a radius is constant, but is not used all around, it is
identified with "constant" instead; see figure 69.
Figure 68
Figure 69
Radii for bent tubes are entered on the tubes' axis; see figure 70. In the case of steel sections, they
are entered on the inner edges instead; see figure 71.
Figure 70
Figure 71
Diameter symbol
Diameter symbol Ø is used to identify circular forms and is always placed before the dimension
number; see figure 72, figure 73, and figure 74. The symbol, a circle with a slash running through it
at an angle of 75°, is placed before the nominal dimension and is centered with respect to the di‐
mension figure.
6.3  


### 第 29 页
Page 29
VW 01054: 2016-01
Figure 72
Figure 73
Figure 74
Several diameter dimensions in one view are confusing and must therefore be drawn out from the
graphical representation, see figure 75.
Figure 75
Sphere
The capital letter "S" (for "sphere") is always inserted in front of the diameter or radius specification
of a sphere, see figure 76 and figure 77. For oval points, e.g., bolt or rod ends, the additional letter
"S" is omitted, see figure 78.
Figure 76
Figure 77
Figure 78
6.4  


### 第 30 页
Page 30
VW 01054: 2016-01
Square symbol, diagonal cross, widths across flats
The graphical symbol 
 is always positioned before the dimension figure. The length of only one
side of the square is given, see figure 79 and figure 80. Square shapes must preferably be dimen‐
sioned in the view in which the shape is recognizable, see figure 80.
Figure 79
Figure 80
If only one view is available, planar four-sided surfaces are marked by a diagonal cross (line width
same as for dimension lines), see figure 79. Surfaces that are not planar do not have a diagonal
cross, see figure 81.
Figure 81
Widths across flats are indicated as per figure 82. If the distance between wrench bearing surfaces
cannot be dimensioned in the graphical representation, the capital letters "SW" (width across flats)
must be placed before the dimension number; see figure 83.
Figure 82
Figure 83
NOTE:
–
Threaded joints, design and installation specifications as per VW 01110-1
–
Width across flats selection as per DIN 475-1
–
Tolerance of width across flats as per DIN ISO 272
6.5  


### 第 31 页
Page 31
VW 01054: 2016-01
Cone, taper, incline
The cone taper C is the ratio of the difference between two cone diameters and the distance be‐
tween them. It is computed using the following formula, see figure 84.
Figure 84
Graphic symbol 
 is always placed before the dimension number for the taper (as a ratio or per‐
centage) on a bent reference line. The direction of the graphical symbol must correspond to the
direction of the taper, see figure 85 and figure 86. For standard cones, see DIN 254.
Figure 85
Figure 86
Graphic symbol 
, which is used for the "cone" and "taper" geometrical features (height of trian‐
gle: 16 times the fonts' line width; base-to-height ratio of 1:2) is entered near the cone. The datum
line must be connected to the contour of the cone by a reference line, see figure 87. The datum
line is drawn parallel to the center line of the cone.
Figure 87
6.6  


### 第 32 页
Page 32
VW 01054: 2016-01
The graphical symbol 
 for inclines is always indicated before the dimension figure of the in‐
cline (as a ratio or in percentage). This specification must preferably be entered on a bent refer‐
ence line, see figure 88. However, it may also be entered on the line of the inclined surface – see
figure 89 – or horizontally – see figure 90 – in exceptional cases.
Figure 88
Figure 89
Figure 90
Graphic symbol 
 symbolizes the shape of the part at the point of the incline; see figure 88.
The angle of incline may also be indicated, for manufacturing reasons, as a reference or auxiliary
dimension, see figure 89.


### 第 33 页
Page 33
VW 01054: 2016-01
Grooves
Grooves are dimensioned as per figure 91 to figure 105. For grooves that are open (on at least one
side), the groove depth is dimensioned from the opposite side; see figure 91. For all other grooves,
the groove depth is dimensioned from the groove side; see figure 92 to figure 95. The depth of the
groove is the greatest distance between the outer diameter of the part body and the groove base.
Figure 91
Figure 92
Figure 93
For an example showing how to dimension the groove depth when the base of the groove runs
parallel to the envelope line of a cone, see figure 94. For an example showing how to dimension
the groove depth when the base of the groove runs parallel to the cone axis, in which case the
depth is dimensioned starting from the larger cylinder's envelope line, see figure 95.
Figure 94
Figure 95
Simplified dimensioning of the depth of grooves in the top view using the letter h, see figure 96, or
combined with the groove width, see figure 97.
6.7  


### 第 34 页
Page 34
VW 01054: 2016-01
Figure 96
Figure 97
Dimensioning of grooves for keys in cylindrical holes, see figure 98.
NOTE 2: For the adequate dimensioning of key grooves, it may also be necessary, where corre‐
sponding accuracy requirements exist, to include the shape and positional tolerances as per
DIN EN ISO 1101, e.g., for parallelism or symmetry.
Figure 98
For an example showing how to dimension grooves for keys in conical hub holes (base of groove
running parallel to the cone envelope line), see figure 99. For an example showing how to dimen‐
sion grooves for keys in conical hub holes (base of groove running parallel to the cone axis), see
figure 100.
Figure 99
Figure 100
For hubs with wedge-shaped grooves, the direction of the incline must be indicated by the graphi‐
cal symbol for "incline"; see figure 101.


### 第 35 页
Page 35
VW 01054: 2016-01
Figure 101
Complete dimensioning of grooves (recesses), e.g., for retaining rings, see figure 102 and
figure 103. Simplified dimensioning of grooves (recesses), e.g., for retaining rings, see figure 104
and figure 105.
Figure 102
Figure 103
Figure 104
Figure 105


### 第 36 页
Page 36
VW 01054: 2016-01
Spacings
Spacings with equal distances for equal geometrical features are indicated as per figure 106 and
figure 107. The linear distance or angular distance is indicated only once. The number of distances
is indicated in front of the symbol "×". The total size (sum of distances) is indicated as an auxiliary
dimension after the symbol "=" in parentheses. Spacings for rectangular holes are dimensioned
from the hole edges, see figure 108.
Figure 106
Figure 107
Figure 108
Angles are specified in degrees, minutes, and seconds (e.g., 30°33'18") or in a decimal notation
(e.g., 50.5°). The use of different notations in a drawing must be avoided. The tolerance must be
the same, respectively (e.g., 30°33'18" ±0°30' or 50.5° ±0.5°).
For the tolerancing of distances, it is recommended to use positional tolerances, see section A.1.1,
DIN EN ISO 14405-2, and DIN EN ISO 1101.
Parallel dimensioning, increasing dimensioning
For parallel dimensioning, the dimension lines are entered parallel in a direction of a common da‐
tum feature; for an example, see figure 109.
Figure 109
6.8  
6.9  


### 第 37 页
Page 37
VW 01054: 2016-01
For increasing dimensioning, starting from a datum feature for a dimensioning direction, only one
dimension line is entered, see figure 110 and figure 111. For each component edge to be dimen‐
sioned, an extension line is drawn. The first extension line represents the origin that is marked by a
circle symbol (see figure 26). Each dimension represents the distance from this datum feature/ori‐
gin.
Figure 110
Figure 111
For the tolerancing of distances, it is recommended to use positional tolerances, see section A.1.1,
DIN EN ISO 14405-2, and DIN EN ISO 1101.
Symmetrical parts
In the case of symmetrical geometrical features, the symmetrical dimensions are dimensioned only
once (exception: square; see figure 112) and the symmetry is emphasized by the symmetry axis
running perpendicular to the dimension lines regardless of whether dimensioning is from the inside
or outside; see figure 113 and figure 114. Symmetry symbols (
; see figure 27 and
figure 104) are not required in either arrangement. The symmetry must be adhered to with the usu‐
al workshop accuracy. If greater accuracy is required, the permissible coaxiality or symmetry must
be prescribed; for more details see also DIN EN ISO 1101. Broken parts must be shown such that
the break lines on both halves have the same distances, so as not to affect the symmetry, see
figure 115.
Figure 112
Figure 113
6.10  


### 第 38 页
Page 38
VW 01054: 2016-01
Figure 114
Figure 115
Arc dimensions, effective lengths
Graphic symbol 
 (diameter: 14 times the font's line width) is placed before the dimension
number; see figure 116.
For central angles up to 90°, the extension lines are drawn parallel to the median line of the angle.
Each arc dimension is entered with its own extension line. Consecutive arc dimensions and length
or angle dimensions that border on arc dimensions must not be entered on the same extension
line, see figure 117.
For arc dimensions see also section 3.
Figure 116
Figure 117
For central angles greater than 90°, the extension lines are drawn in the direction of the center of
the arc. Where the reference is not clear, the connection between the arc length and the dimension
figure must be marked by a line with an arrowhead and dot or circle on the dimension line, see
figure 118.
Consecutive arc dimensions or length or angle dimensions that border on arc dimensions are en‐
tered on an extension line, see figure 118.
Figure 118
6.11  


### 第 39 页
Page 39
VW 01054: 2016-01
If an effective length is specified, graphic symbol 
 must be used instead of the words "effective
length". The symbol must always be placed before the dimension number for the effective length;
see figure 119. The graphic symbol is a circle with a tangential, horizontal arrow at the bottom. The
circle has a diameter that is 10 times the font's line width. The length of the line with an arrowhead
corresponds to 1.5 times the diameter of the circle (arrowhead: vertex angle 15°, length 10 times
the font's line width). The dimensioning of the developed view as an auxiliary dimension is shown
as in figure 120. The developed view is drawn using the line type G as per VW 01050.
Figure 119
Figure 120
Thread dimensions
Standard threads are indicated with abbreviated names, see figure 121 and figure 122 (for a com‐
plete overview see VW 01031). The thread diameter is written without the diameter symbol and is
also deemed to include surface protection; see section 3.
Left-hand threads must bear the additional text "LH" or "left." Where right and left-hand threads of
equal size are present on the same part, the right-hand thread is also marked "RH" or "right," see
figure 123.
The tolerance class "medium" is the default and is not indicated; however, the tolerance classes
"fine" and "coarse" must be indicated after the thread designation, see figure 124 and figure 125.
For further detail on the thread system, see VW 01041.
Formed (rolled) threads must be dimensioned as per figure 131 by specifying VW 01178 or
VW 01179 next to the thread diameter.
Figure 121
Figure 122
Figure 123
Figure 124
Figure 125
6.12  


### 第 40 页
Page 40
VW 01054: 2016-01
For special threads, the profile must be drawn and all the dimensions necessary for manufacture
must be entered.
Thread ends are dimensioned like the ends of standard bolts. The spherical end is positioned in‐
side the thread length and its height is usually 1.5 times the thread pitch, see figure 126 and
figure 127.
Figure 126
Figure 127
Figure 128
Thread lengths, thread runouts: The useful thread is the thread length (without runout), see
figure 126, figure 127, and figure 128. The runout, see DIN 76-1, is usually not dimensioned. For
minimum lengths of engagement, see VW 01110-1.
Thread grooves for external threads are dimensioned as per figure 129 and for internal threads as
per figure 130; see DIN 76-1.
Figure 129
Figure 130
Figure 131 – Dimensioning of formed (ridged) internal threads
Blind tapped holes are dimensioned as per figure 128. Apart from the thread diameter, the minor-
diameter hole depth and the usable thread length are indicated. For minimum lengths of engage‐
ment, see VW 01110-1; for wire thread inserts, see DIN 8140-1.


### 第 41 页
Page 41
VW 01054: 2016-01
Sheet eyelets with thread must be drawn and dimensioned as per VW 01045.
The countersink on the thread is drawn and dimensioned. The usual countersink is 120° -10° and
is between d = external thread diameter and 1.05 x d, see figure 132 and figure 133. Larger coun‐
tersinks are indicated as per figure 134.
If there are several internal threads on the housing or other parts, the countersink may also be indi‐
cated in the drawing field in the "Notes" field. It would then read, e.g., "Countersink on both sides
of the internal thread 120° −10° from d to 1.05d." If the countersink is on one side only, then 120°
must be indicated in the graphical representation and the following is entered in the "Notes" field:
"Thread countersinks from d to 1.05d" (for d = external thread diameter, the relevant values must
be entered).
Figure 132
Figure 133
Figure 134


### 第 42 页
Page 42
VW 01054: 2016-01
Equal spacing tolerance
The equal spacing tolerance (graphical symbol 
) is the permissible difference between the larg‐
est and the smallest actual dimension (two-point measurement) on an individual geometrical fea‐
ture, for examples, see figure 136 and figure 137.
The equal spacing tolerance must be indicated if only a limited range of the dimension tolerance
must be used on an individual geometrical feature.
The equal spacing tolerance must be entered on the right next to the nominal dimension (with or
without indication of the dimensional tolerance) with the symbol 
 and the equal spacing toler‐
ance value (absolute value without sign), e.g., 3 ±0.3
0.15 or 80
0.2. For examples, see
figure 136 and figure 137.
The meaning of the symbol must be explained on the drawing in the "Bemerkungen / Notes" field;
for an example for a drawing note, see figure 135.
Figure 135
If the nominal dimension is entered without a dimensional tolerance, the general tolerances speci‐
fied in the drawing apply.
Figure 136
Figure 137
NOTE on figure 136: All thickness dimensions between 2.7 mm and 3.3 mm are permissible. How‐
ever, the difference in actual dimensions present on an individual part must not exceed 0.15 mm.
For examples, see table 2.
Table 2 – Examples for measurement results and conformity inspection
Example 1
Example 2
Largest actual dimension 3.07 mm
Smallest actual dimension 2.92 mm
Difference = 0.15 mm
Thickness dimension tolerance and equal spac‐
ing tolerance requirement met
Largest actual dimension 2.88 mm
Smallest actual dimension 2.71 mm
Difference = 0.17 mm
Thickness dimension tolerance tolerance re‐
quirement met
Equal spacing tolerance requirement not met
6.13  


### 第 43 页
Page 43
VW 01054: 2016-01
Workpiece edges
As a result of the relevant production process and depending on the workpiece strength, burrs
which are caused by cutting or grinding processes might occur on the edges of workpieces manu‐
factured either by metal-cutting or non-machining operations. If such burr formations must be re‐
moved or minimized due to the risk of injury or functional impairments, symbols as per VW 01088
must be entered in the drawings.
The following are defined in the standard:
–
Permissible burr
–
Sharp-edged
–
Permissible removal
If for functional reasons, certain shapes are required for the workpiece edges, such as
–
Chamfers,
–
Bevels,
–
Undercuts, etc.,
these must be dimensioned and toleranced accordingly in the drawing.
Bent tubes and rods (solid material)
The use of computer-controlled bending machines and measuring devices requires, for the bend‐
ing of tubes and bars, production-related dimensioning and tolerancing of parts. All arcs of a tube
or a rod must have an equally large bend radius. The largest possible bend radii must be preferred.
A straight section of length ≥ 1.5 x d (diameter of the tube/rod) must be positioned at the start, at
the end, and between two arcs, see figure 138. Deviations from this requirement must be agreed
upon with the Production department.
The dimensioning on the part is used for explanation purposes only.
Legend
R*
For rods (solid material), the bend radius is specified at the inside edge.
Figure 138
6.14  
6.15  


### 第 44 页
Page 44
VW 01054: 2016-01
Dimensioning in coordinate system and tolerancing
When using computerized entry, it is better to enter the theoretical coordinate dimensions and the
positional tolerances in a table rather than doing dimensioning directly on the part. In this case, it is
essential that the direction of coordinate dimensions be clearly defined using minus "−" and plus
"+" signs; see figure 139.
The general bend radius and the roundness tolerance in the bend area must also be specified in
the table; see numbers 1 and 2 in figure 139. When required, additional specifications must be pro‐
vided in the graphical representation.
Center axis of tubes3) (tube center)
The X-axis, Y-axis, and Z-axis coordinates for the start and end points of the individual axis
stretches must be specified, relative to the coordinate system origin "A," as theoretical coordinates
(coordinate dimensioning) in a table as per VW 01014 , code no.: NO-G10 (see figure 139).
The permissible positional deviations for the axis stretches are defined by entering "cylindrical po‐
sitional tolerances"; see DIN EN ISO 1101 and figure 140. The positional tolerances limit the axis
stretches' positional, orientation, and straightness deviations. The positional tolerances in the table
in figure 139 are equivalent to the dimensioning in figure 140.
Depending on the functional requirements, different positional tolerances may be indicated for ev‐
ery individual length.
Total length and perpendicularity of the tubes' end faces
The permissible deviation of the total length of the tubes must be defined with a positional toler‐
ance as shown in figure 139.
If exact perpendicularity between the end faces and the tube axis is required, the perpendicularity
must also be specified; see figure 139.
6.15.1  
6.15.1.1  
6.15.2  
3)
Hereinafter, the term "tube" also refers to rods.


### 第 45 页
Page 45
VW 01054: 2016-01
Legend
1
General toleranced bend radius
2
General roundness tolerance in the bend area
Figure 139 – Example for the dimensioning and tolerancing of a bent tube
Figure 140


### 第 46 页
Page 46
VW 01054: 2016-01
Collective information
Collective information (see DIN 30-10) can be used to reduce the dimensioning and tolerancing
work for entering repeated information of the same type and to make the drawings easier to under‐
stand.
General collective information generally applies to the whole drawing, e.g., general tolerances for
nominal dimensions without tolerance (figure 142, number 2), workpiece edge conditions
(figure 142, number 3), surface roughness (figure 142, number 4), or information as in figure 141.
The collective information must be entered in the "Bemerkungen / Notes" drawing field.
Note on undimensioned geometries in the data set
If not all of the required nominal dimensions and theoretical dimensions with respect to the compo‐
nent geometry are present in a 2-D drawing, then all dimensions that are not indicated must be
taken from the 3-D data set. To express this in the drawing, the text macro NO-F8 as per
VW 01014 (see figure 141) or similar wording is entered in the "Bemerkungen / Notes" drawing
field.
Figure 141 – Text macro NO-F8
The text macro NO-F8 or similar wording must be used with the table for general tolerances of un‐
dimensioned linear and angular sizes as compared to the data set and defined RPS reference sys‐
tem (text macro NO-G11 as per VW 01014). For an example, see figure 142.
General tolerances for linear and angular sizes
General tolerances for nominal dimensions without tolerance specification
The permissible deviations of dimensions that are produced with typical workshop accuracy must
be allocated to the nominal dimension ranges in the table for general tolerances (in the bottom left
drawing corner) and entered into the "finished" column, see figure 142, number 2. Here,
DIN ISO 2768-1 can be used as a template with general tolerances for linear and angular sizes. If
smaller tolerances are required or larger tolerances are economical, these must be entered in the
table. The permissible linear deviations must be indicated in mm and angular deviations must be
indicated in degrees (°).
The "raw" column is provided for entering process-related accuracy values for pressed parts, injec‐
tion-molded parts, and forgings with surfaces that are left untreated. The relevant standards con‐
cerning "General tolerances" (see DIN SPEC 23605) must be observed in these cases.
7  
7.1  
7.2  
7.2.1  


### 第 47 页
Page 47
VW 01054: 2016-01
Legend
1
General tolerances for undimensioned linear and angular sizes (text macro NO-G11 as
per VW 01014)
2
General tolerances for linear and angular sizes without tolerance specification
3
Note on undimensioned design models in the data set (text macro NO-F8 as per
VW 01014)
4
Surface roughness as per VW 13705 and VDA 2005
5
Workpiece edge states as per VW 01088
Figure 142 – Example for general tolerances for linear and angular sizes
General tolerances for undimensioned linear and angular sizes
The permissible deviations of undimensioned linear and angular sizes as compared to the data set
and defined RPS reference system that are produced with typical workshop accuracy must be en‐
tered in the table for general tolerances (text macro NO-G11 as per VW 01014) the same as in
section 7.2.1. The table (NO-G11) must be placed in the bottom left drawing field above the table
for general tolerances for nominal dimensions without tolerance specification, as shown in
figure 142, number 1, and must be filled accordingly. The table (NO-G11) must be used together
with the text macro NO-F8 (see section 7.1).
7.2.2  


### 第 48 页
Page 48
VW 01054: 2016-01
Profile tolerances with respect to the data set and defined RPS reference system
Surface profile tolerances and trim tolerances as compared to the data set and defined RPS refer‐
ence system are indicated in drawings as follows:
–
If a symmetric surface profile tolerance for all not directly toleranced surfaces with respect to
the data set and defined RPS reference system is required, the text macro NO-F27 as per
VW 01014 and the appropriate value (±1/2 of the tolerance width) is entered in the drawing in
the "Bemerkungen / Notes" field; for an example, see figure 143 and the text macro NO-F27
must be used together with the text macro NO-F8 (figure 141).
Figure 143 – Text macro NO-F27
–
If a symmetric surface profile tolerance for a certain surface with respect to the data set and
defined RPS reference system is required, the text macro NO-F28 as per VW 01014 (see
figure 144) is entered in the drawing in the "Bemerkungen / Notes" field and this surface is
marked in the graphical representation with a narrow dash-two dot line. The text macro NO-
F28 must be used together with the text macro NO-F8.
Figure 144 – Text macro NO-F28
–
In the case of asymmetrical surface profile tolerances of the surfaces as compared to the data
set and defined RPS reference system, it is necessary to cut through the respective surface
and to indicate the permissible deviations in the section by means of an arrow; for an example,
see figure 145.
Figure 145 – Example for entering the asymmetrical, permissible deviations
The text macro required for this purpose – NO-F29 as per VW 01014, see figure 146 – is entered in
the "Bemerkungen / Notes" drawing field.
Figure 146 – Text macro NO-F29
7.3  


### 第 49 页
Page 49
VW 01054: 2016-01
–
In the case of asymmetrical surface profile tolerances of the trim edges as compared to the
data set, it is necessary to cut through the respective trim edge and to indicate the permissible
deviations in the section by means of an arrow; for an example, see figure 147.
Figure 147
The toleranced range is specified by a wide dash-dot line and a symbol; for an example, see
figure 148.
Figure 148
The text macro required for this purpose – NO-F30 as per VW 01014, see figure 149 – is entered
together with the text macro NO-F8 (figure 141) in the "Bemerkungen / Notes" field.
Figure 149 – Text macro NO-F30
If different tolerances are required within the marked length range (wide dash-dot line), several
sections are produced. The transitions must be marked with an "X"; for an example, see
figure 150.
Figure 150


### 第 50 页
Page 50
VW 01054: 2016-01
Applicable documents
The following documents cited in this standard are necessary to its application.
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
VW 01014
Drawings; Drawing Frames and Text Macros
VW 01031
Thread Designations; Overview
VW 01041
Metric ISO Thread; Explanation of the Thread System
VW 01045
Sheet Eyelets with Metric ISO Thread; High Type
VW 01050
Technical Drawings; Scales, Lines, Hatching, Break Lines
VW 01054
Engineering Drawings; Dimensioning and Tolerancing; Envelope Re‐
quirement and Independency Principle
VW 01055
Reference Point System (RPS); Specifications in Drawings and 3-D
CAD Models
VW 01058
Drawings; Lettering
VW 01058
supplement 1
3-D Drawingless Process (3DZP)
VW 01088
Workpiece Edges; Definitions, Drawing Specifications
VW 01110-1
Threaded Joints; Design and Assembly Specifications
VW 01179
Metric ISO Thread; Properties and Dimension Limits for Helically
Formed Threads
VW 13705
Specification of Surface Texture; Geometrical Product Specifications -
Engineering Drawings
DIN 254
Geometrical product specifications (GPS) - Series of conical tapers and
taper angles; Values for setting taper angles and setting heights
DIN 406-12
Engineering drawing practice; dimensioning; tolerancing of linear and
angular dimensions
DIN 475-1
Widths across flats for bolts, screws, valves and fittings
DIN 76-1
Thread run-outs and thread undercuts - Part 1: For ISO metric threads in
accordance with DIN 13-1
DIN 8140-1
Wire thread inserts for ISO metric screw threads - Part 1: Dimensions,
technical specifications
DIN EN ISO 1101
Geometrical product specifications (GPS) - Geometrical tolerancing -
Tolerances of form, orientation, location and run-out
DIN EN ISO 14405-1
Geometrical product specifications (GPS) - Dimensional tolerancing -
Part 1: Linear Sizes
DIN EN ISO 14405-2
Geometrical product specifications (GPS) - Dimensional tolerancing -
Part 2: Dimensions other than linear sizes
8  


### 第 51 页
Page 51
VW 01054: 2016-01
DIN EN ISO 14660-2
Geometrical Product Specifications (GPS) - Geometrical features - Part
2: Extracted median line of a cylinder and a cone, extracted median sur‐
face, local size of an extracted feature
DIN EN ISO 17450-1
Geometrical product specifications (GPS) - General concepts - Part 1:
Model for geometrical specification and verification
DIN EN ISO 286-1
Geometrical product specification (GPS) - ISO code system for toleran‐
ces on linear sizes - Part 1: Basis of tolerances, deviations and fits
DIN EN ISO 286-2
Geometrical product specifications (GPS) - ISO code system for toleran‐
ces on linear sizes - Part 2: Tables of standard tolerance classes and
limit deviations for holes and shafts
DIN EN ISO 3098-2
Technical product documentation - Lettering - Part 2: Latin alphabet, nu‐
merals and marks
DIN EN ISO 5459
Geometrical product specifications (GPS) - Geometrical tolerancing -
Datums and datum systems
DIN EN ISO 8015
Geometrical product specifications (GPS) - Fundamentals - Concepts,
principles and rules
DIN ISO 272
Fasteners; Width across Flats for Hexagon Products
DIN ISO 2768-2
General tolerances; geometrical tolerances for features without individu‐
al tolerances indications
DIN SPEC 23605
Technical prduct specification (TPS) - Application guidance - Structured
and commented overview of ISO- and DIN-Standards for technical prod‐
uct dokumentation (TPD) and geometrical product specification (GPS)
VDA 2005
Geometrical product specification - Technical drawings - Specification of
surface texture; inclusive Appendix 1


### 第 52 页
Page 52
VW 01054: 2016-01
Envelope requirement explanations
Taylor Principle
Most workpieces are assembled with other components. Many of the surfaces on machine parts
play a functionally relevant role in terms of fit. This means that two toleranced geometrical features
(mating surfaces) are joined one inside the other so that one of the features will surround the other
at least partially. Normally, these mating surfaces will be cylindrical ("round fit") or plane-parallel
("plane fit"). When geometrical features of two different workpieces need to be mated, a special fit
characteristic may be required for the component's function, i.e., clearance fit, transition fit, or inter‐
ference fit; see DIN EN ISO 286-1.
The minimum clearance for clearance fits is the clearance that results when the dimensions of both
mating surfaces are right at the maximum material limit; see figure A.1.
Legend
1
Hole tolerance
2
Shaft tolerance
3
Minimum clearance
4
Maximum clearance
5
Minimum hole dimension
6
Maximum hole dimension
7
Minimum shaft dimension
8
Maximum shaft dimension
Figure A.1 – Diagram showing how to define a clearance fit
The minimum clearance, however, will only be present if the parts do not have any additional
shape deviations, i.e., are not, e.g., curved or untrue. In order for the clearance to be adhered to, it
would be necessary to determine the maximum possible dimensions of the two geometrical fea‐
tures being mated, e.g., a shaft and a hole, i.e., these maximum possible dimensions are the effec‐
tive size yielded by the maximum material size and the maximum shape deviations.
Long before there were form and positional tolerances, Taylor recognized this principle and, in
1905, invented the Taylor Principle with his patent application:
"The go inspection is a mating inspection with a gage that goes over the entire geometrical feature;
the no go inspection is an individual inspection in the two-point method."
The Taylor Principle limits the maximum dimension for each of the two mating features. This limit is
referred to as an "envelope." The envelope is equal to the maximum material size, i.e., the size at
which the geometrical feature's material has its greatest dimension (material volume) – outwards in
Appendix A (informative)  
A.1  


### 第 53 页
Page 53
VW 01054: 2016-01
the case of shafts, inwards in the case of holes. In order to be compatible with the fit, the geometri‐
cal feature must not pass through the envelope. The envelope represents the gage's go-side as
per the Taylor Principle (figure A.2 a). This means:
–
A shaft must remain within this envelope or, at the most, touch it and
–
A hole must remain outside this envelope or, at the most, touch it.
This will ensure a fit and adherence to the maximum material size. In addition, no actual local size
(two-point size) must exceed the least material size in the case of holes or fall below it in the case
of shafts. The two-point size represents the gage's no-go-side as per the Taylor Principle
(figure A.2 b).
Legend
a)
Go inspection as per the Taylor
Principle
b)
No-go inspection as per the Taylor
Principle
Figure A.2 – Taylor Principle for a hole
If there is an external feature-of-size of type "cylinder" (figure A.3 a), the geometrically ideal enve‐
lope (cylinder) with the maximum material size (maximum dimension ) of 20 mm must not be
passed through at any point (figure A.3 b and c). The least material size (minimum dimension) –
19.9 mm in this case – must not be fallen below at any point (figure A.3 b and c).
Unless otherwise specified, shape deviations and parallelism deviations in opposite envelope lines
fall within the dimensional tolerance.
Legend
a)
Drawing note
b), c)
Interpretation
Figure A.3 – Envelope principle with an external feature-of-size of type "cylinder" (shaft)


### 第 54 页
Page 54
VW 01054: 2016-01
If there is an internal feature-of-size of type "cylinder" (figure A.4 a), the geometrically ideal enve‐
lope (cylinder) with the maximum material size (minimum dimension) of 20 mm must not be fallen
below. In addition, the least material size (maximum dimension) of 20.1 mm must not be exceeded
at any point (figure A.4 b and c).
Unless otherwise specified, shape deviations and parallelism deviations in opposite envelope lines
fall within the dimensional tolerance.
Legend
a)
Drawing note
b), c)
Interpretation
Figure A.4 – Envelope requirement with an internal feature-of-size of type "cylinder" (hole)
Feature-of-size
A feature-of-size is a geometrical feature that is geometrically defined in an exact manner with a
linear length dimension or angular dimension only (what is referred to as a size).
A feature-of-size can be a cylinder, two parallel opposite planes, a sphere, a cone, or a wedge.
The envelope requirement can only be used for features-of-size
–
of type "cylindrical surface" (cylinders such as holes and shafts) or
–
of type "two parallel opposite planes" (widths such as slot width, groove width, tongue width,
key width).
The geometrical features must be able to physically pass a go-gage check as per the Taylor Princi‐
ple.
The envelope requirement cannot be used for
–
Features-of-size such as cones, wedges, spheres
–
Non-features-of-size such as
–
Complex geometrical features (for examples, see Figure A.5) and
–
Distances that are dimensions other than linear sizes (for examples, see figure A.6 to
figure A.8 and DIN EN ISO 14405-2).
A.1.1  


### 第 55 页
Page 55
VW 01054: 2016-01
Figure A.5 – Non-feature-of-size examples
"Other than linear sizes" means distances between
–
two real geometrical features – what are referred to as step dimensions (see figure A.6),
–
a real geometrical feature and a derived geometrical feature (see figure A.7),
–
two derived geometrical features (see figure A.8).
Real geometrical features are geometrical features that actually physically exist on a component.
Derived geometrical features are axes, center planes, symmetry planes, and center points that do
not actually physically exist on the component. These geometrical features must be metrologically
derived from the real geometrical features during a tolerance examination using association crite‐
ria. DIN EN ISO 14660-2 uses the least-squares criterion as the default association criterion. For
association criteria, see section A.1.2.
Figure A.6 – Distances between two real geometrical features (step dimensions)
Figure A.7 – Distances between a real geometrical feature and a derived geometrical feature
Figure A.8 – Distances between two derived geometrical features


### 第 56 页
Page 56
VW 01054: 2016-01
The use of what is referred to as "plus/minus tolerancing" for distances other than linear sizes will
result in ambiguities during verification. In order to avoid these ambiguities, it is recommended to
use geometric tolerancing. For examples, see figure A.9, figure A.10, and figure A.11.
Figure A.9 – Example showing the linear distance between two integral geometrical elements
Figure A.10 – Example showing the linear distance between an integral geometrical element
and a derived geometrical element
Legend
a)
Drawing note with plus/minus tolerancing (ambiguous)
b)
Verification (ambiguous)
c)
Drawing note with geometric tolerancing (unambiguous)
Figure A.11 – Example showing the linear distance between two derived geometrical elements


### 第 57 页
Page 57
VW 01054: 2016-01
Sizes
A size is a linear dimension that defines the theoretically ideal form and size of a feature-of-size in
an exact manner.
A distinction can be made between the following types of sizes based on the method used to deter‐
mine them (see table A.1).
An actual local size is a length dimension measured at the location (two-point size).
A two-point size (local size) is the distance between two opposite points on the real feature-of-size,
e.g., of type "cylinder" or of type "two parallel opposite planes"; for examples, see figure A.12. The
distance's orientation is defined perpendicular to the axis or center plane (see
DIN EN ISO 14660-2, local diameter of an extracted cylinder and local size of two parallel extrac‐
ted surfaces). A feature-of-size can have an infinite number of two-point sizes that are different
from each other in terms of their numeric value, see figure A.12.
Legend
a)
Feature-of-size of type "cylinder"
b)
Feature-of-size of type "two parallel
opposite planes"
1
Real workpiece surface
2
Measuring points
3
Associated least-squares circle
4
Center of least-squares circle
5
Associated least-squares planes
6
Center plane (symmetry plane)
from two least-squares planes
Figure A.12 – Examples of local two-point sizes
A direct global size is a size of the geometrical feature associated with the feature-of-size. It is cal‐
culated based on measuring points on the real feature-of-size by association with an ideal geomet‐
rical feature (what is referred to as a "substitute element") of the same type as that of the feature-
of-size.
The following association criteria, which are described in DIN EN ISO 14405-1, can be used for the
association operation:
–
Least-squares method (for an example, see figure A.13 a)
–
Maximum inscribed geometrical feature (for an example, see figure A.13 b)
–
Minimum circumscribed geometrical feature (for an example, see figure A.13 c)
The results obtained will depend on the selected association criterion; see figure A.13.
A.1.2  


### 第 58 页
Page 58
VW 01054: 2016-01
Legend
a)
"Least-squares" association criteri‐
on
b)
"Maximum inscribed geometrical
feature" association criterion
c)
"Minimum circumscribed geometri‐
cal feature" association criterion
1
Real workpiece surface
2
Measuring points
3
Associated least-squares circle
4
Associated maximum-inscribed-
geometrical-feature circle
5
Associated minimum-circumscri‐
bed-geometrical-feature circle
Figure A.13 – Example showing association criteria used with a circular geometrical feature
Table A.1 – Types of sizes
Sizes
Example
Local size
Two-point
size
(two-point
size)  
d - Values of two-point local size
Spherical size
 
d - Values of spherical local size


### 第 59 页
Page 59
VW 01054: 2016-01
Sizes
Example
Direct global sizes
Size calcula‐
ted using the
least-squares
method
 
1 - Real feature-of-size 2 - Associated feature-of-size
3 - Least-squares size
Maximum in‐
scribed size
 
1 - Real feature-of-size 2 - Associated feature-of-size
3 - Maximum inscribed size
Minimum cir‐
cumscribed
size
 
1 - Real feature-of-size 2 - Associated feature-of-size
3 - Minimum circumscribed size
Specification modifiers for sizes as per DIN EN ISO 14405-1
When using the independency principle, sizes without modifiers on the drawing must be interpreted
as two-point sizes. This is a default definition for sizes.
Modifiers (see table A.2) can be used to modify/complement the default definition if necessary.
The conditions that must be applied to the individual sizes, e.g., Ⓔ or other modifiers, must be
preferably defined in the simultaneous engineering team (SET) together with Test Planning and
Quality Assurance.
Table A.2 contains a selection of modifiers from DIN EN ISO 14405-1.
Table A.2 – Modifiers for sizes
Modifier
Description
Local two-point size
When using the independency principle, all sizes must be interpre‐
ted as two-point sizes as default even if this modifier is not inclu‐
ded. Because of this, this modifier is only used in combination with,
e.g.,
 and 
 (see section B.1.4) or with rank-order sizes such
as 
 and 
.
Local size, defined by a sphere
Used for: Flexible tubes, hoses
A.1.3  


### 第 60 页
Page 60
VW 01054: 2016-01
Modifier
Description
Dimension (size) of the geometrical feature associated using the
least-squares method
Dimension (size) of the maximum inscribed geometrical feature
Used only for clearance fits in combination with 
, (see
figure A.15). Not recommended, as implementing it in production
measuring equipment is difficult.
Dimension (size) of the minimum circumscribed geometrical fea‐
ture
Used only for clearance fits in combination with 
, (see
figure A.14). Not recommended, as implementing it in production
measuring equipment is problematic.
Table A.3 – General modifiers for sizes
Modifier and description
Drawing note example
Ⓔ
 
 
Envelope requirement
10 ±0.1Ⓔ
/length
 
 
any limited sub-area of the geo‐
metrical feature
10 ±0.1
/5
ACS
 
 
Any cross section
10 ±0.1
ACS


### 第 61 页
Page 61
VW 01054: 2016-01
Modifier and description
Drawing note example
SCS
 
 
Specific cross section
10 ±0.1
SCS
Number ×
 
 
More than one geometrical fea‐
ture
2 × 10 ±0.1Ⓔ
CT
 
 
Common tolerance
(see DIN EN ISO 14405-1)
10 ±0.1Ⓔ CT
 
Ⓕ
 
 
Free-state condition
10 ±0.1
Ⓕ


### 第 62 页
Page 62
VW 01054: 2016-01
Modifier and description
Drawing note example
or
 
 
between
10 ± 0.1A
B
Envelope requirement drawing note
As per the definition for the envelope requirement as per DIN EN ISO 14405-1, the envelope re‐
quirement can be used for an
- External feature-of-size (shaft, key width) as a simultaneous use of a combination of the two-point
size 
 at the lower limit of size (LLS) and the minimum circumscribed dimension 
 at the upper
limit of size (ULS)
- Internal feature-of-size (hole, groove width) as a simultaneous use of a combination of the two-
point size 
 at the upper limit of size (ULS) and the maximum inscribed dimension 
 at the low‐
er limit of size (LLS)
Based on these specifications in DIN EN ISO 14405-1, a distinction must be made between the
following
–
Envelope requirement, expressed with the help of a combination of dimension modification
symbols, see figure A.14 a) and figure A.15 a). Here, the conformity is checked by a measure‐
ment. The envelope can be simulated only with a limited number of measuring points with the
help of corresponding measurement software.
–
Envelope requirement, expressed with the help of the modification symbol Ⓔ, see figure A.14
b) and figure A.15 b). Here, the conformity inspection is performed with a gage (a full solid
measure). In this case, the envelope is simulated completely by the gage.
Legend
a)
Tolerancing with sizes and modifi‐
ers
b)
Tolerancing for a gage inspection
Figure A.14 – Examples of drawing notes for the envelope requirement
A.1.4  


### 第 63 页
Page 63
VW 01054: 2016-01
Legend
a)
Tolerancing with sizes and modifi‐
ers
b)
Tolerancing for a gage inspection
Figure A.15 – Examples of drawing notes for the envelope requirement
The two envelope requirement variants have the same meaning, but specify different verification
methods. This means that the results obtained are not equivalent. Because of this, the envelope
requirement or modifiers must be preferably defined, for the individual sizes, in the SET together
with Test Planning and Quality Assurance. It must be taken into account that, when using toleranc‐
ing based on the independency principle, the form of the geometrical feature is not limited by di‐
mensional tolerances. Because of this, shape deviations must be independently toleranced in a
functionally oriented manner.
Using process control is not possible when using the tolerancing designed for a gage inspection,
as this inspection method does not provide any measured values, but only an "okay (OK)" or "not
okay (NOK)" inspection result instead. Process control requires measurement results so that the
process situation can be determined. Using what are referred to as "mating sizes" – 
 or 
 –
and the two-point size 
 makes it possible to perform process control.


### 第 64 页
Page 64
VW 01054: 2016-01
Independency principle explanations
The independency principle is defined in DIN EN ISO 8015. When using the independency princi‐
ple, every requirement (geometrical product specifications (GPS)) specified on a drawing for di‐
mensional, form, and positional tolerances on a geometrical feature must be met independently of
other requirements unless a special note indicating otherwise (e.g., the dimension modifiers in
 DIN EN ISO 14405-1, such as Ⓔ – envelope requirement (see figure A.16), CZ – common toler‐
ance zone as per DIN EN ISO 1101 (see table A.3), or Ⓜ – maximum material condition as per
ISO 2692) has been additionally entered.
Figure A.16 – Envelope requirement drawing note
Length dimension tolerances
If no special specifications (modifiers) are placed after the length dimension tolerance, the dimen‐
sional tolerance only limits the actual local sizes (two-point sizes) of a geometrical feature, but not
the feature's shape and orientation deviations, e.g., roundness, cylindricity, straightness, and paral‐
lelism deviations for the envelope lines of a cylindrical geometrical feature or flatness and parallel‐
ism deviations for two parallel opposite planes.
For example, a shaft can have, in any cross section, a shape deviation in the form of a curve of
constant width within the roundness tolerance, and it can be bent by the magnitude of the straight‐
ness tolerance, regardless of whether the shaft has reached the maximum material size or not; see
figure A.17.
A.2  
A.2.1  


### 第 65 页
Page 65
VW 01054: 2016-01
Legend
a)
Drawing specification
b)
Meaning of drawing specification
1
Maximum dimension (local actual size)
2
Maximum permissible roundness deviation
3
Maximum permissible straightness deviation of cylinder axis
Figure A.17 – Meaning of drawing specification when using the independency principle
This means that the shape and parallelism deviations must always be limited with appropriate indi‐
vidual form and parallelism tolerances of the general tolerances for form and position as per
DIN ISO 2768-2.


### 第 66 页
Page 66
VW 01054: 2016-01
Angular dimension tolerance
An angular dimension tolerance specified in units of angle covers only the general orientation of
lines or of surface line elements, and not their shape deviations; see figure A.18.
The general orientation of the line derived from the actual surface (actual line) is the orientation of
the contact line with a geometrically ideal form (see figure A.18). The greatest distance between
the contact line and the actual line must be as small as possible.
Legend
1
Actual lines
2
Contact lines
Figure A.18
Shape deviations must be limited with individual form tolerances or general tolerances for form as
per DIN ISO 2768-2.
A.2.2  


### 第 67 页
Page 67
VW 01054: 2016-01
Problem concerning standards when it comes to the general applica‐
bility of the envelope requirements without a drawing note as per
DIN 7167
For Volkswagen Group drawings, the envelope requirement automatically applied to all features-of-
size of type "cylinder" (cylindrical surface – shaft or hole) or type "two parallel opposite planes"
(widths such as slot width, groove width, tongue width, key width) for decades until the end of 2011
as per DIN 71674): 1987-01. This provision applied as per DIN 7167 without a drawing note.
However, DIN 7167 had to be withdrawn at the end of 2011 because the following international
standards concerning GPS, which reverse the procedure in DIN 7167, were published:
DIN EN ISO 286-1: 2010-11, DIN EN ISO 14405-1: 2011-04, and DIN EN ISO 8015: 2011-09. This
resulted in the following condition: When a drawing does not contain a note indicating the general
applicability of the envelope requirement or the use of independency principle, the independency
principle automatically applies as per DIN EN ISO 8015. The independency principle is also implic‐
itly invoked as a result of the invocation principle in DIN EN ISO 8015, as soon as a portion of the
ISO GPS system is invoked in a mechanical engineering product specification (e.g., drawing), ei‐
ther by specifying an ISO code for fit tolerances – e.g., H7– or with a reference to general toleran‐
ces (e.g., ISO 2768 – m).
This new rule is the exact opposite of the old rule as per DIN 7167. As per the independency prin‐
ciple, the envelope requirement does not apply automatically. The dimensions of features-of-size
with ± tolerances and the fit tolerances as per DIN EN ISO 286-1 (ISO code, e.g., H7) now apply
as two-point sizes and must be verified with a two-point measurement.
This means that when a supplier now manufactures a product as per an old drawing that was toler‐
anced as per DIN 7167, the supplier is automatically working as per the independency principle as
per DIN EN ISO 8015, since the drawing will not contain a note indicating the use of the envelope
requirement.
Because of this, as per DIN EN ISO 14405-1, all old drawings that were drafted as per DIN 7167
without a note indicating the general applicability of the envelope requirement must be identified
with the following note (see figure 3) above the title block or, if financially possible, must be revised
as per the independency principle, i.e., if the envelope requirement must continue to apply to spe‐
cific sizes, the Ⓔ modifier must be included after the dimensional tolerance.
This put the validity of all drawings into question that were toleranced as per DIN 7167 and that did
not have a note regarding the envelope requirement. It would mean that all drawings based on
DIN 7167 must be provided with this marking.
Modifications to Volkswagen standards (VW 01014, VW 01054, and VW 01155)
As a solution to the problem described above, modifications were made to the relevant Volks‐
wagen standards. These modifications were gradually implemented even before the aforemen‐
tioned ISO GPS standards were published.
Appendix B (informative)  
B.1  
4)
DIN 7167: 1987-01 – Relationship between tolerances of size, form, and parallelism; envelope requirement without individual indica‐
tion on the drawing


### 第 68 页
Page 68
VW 01054: 2016-01
1st step – adding the "envelope principle" tolerancing principle to VW 01054 (February 2009)
In order to clearly specify that the envelope requirement without a drawing note as per DIN 71675)
applies to the Volkswagen Group's drawings, the following addition was added to standard
VW 01054, issue 2009-02, in February 2009:
Figure B.1
2nd step – rule for drawings with creation date of March 2011 or later
In order to comply with the new ISO GPS standards (especially DIN EN ISO 14405-1: 2011-04),
the following addition was added to VW 01054, issue 2011-03, in March 2011:
Figure B.2
In order to ensure that the general applicability of the envelope requirement is entered into Volks‐
wagen Group drawings, the following addition was added to VW 01054, issue 2011-03:
Figure B.3
In May 2011, the note indicating the general applicability of the envelope requirement used by de‐
fault at Volkswagen was added to all drawing frames as per VW 01014, issue 2011-05, and stand‐
ard VW 01054 was added to the "Unterlagen / References" field (see figure B.4).
5)
In earlier issues of the standard, the general applicability of the envelope requirement to sizes was specified to be the "envelope
principle."


### 第 69 页
Page 69
VW 01054: 2016-01
Figure B.4
3rd step – Rule for Volkswagen Group drawings drafted (released) before June 2011
Since drawings drafted (released) before March 2011 do not contain a note indicating the general
applicability of the envelope requirement as per DIN 7167, the following addition was added to
standard VW 01155 in June 2011, issue 2011-06, which is referenced by default on all drawings.
Figure B.5
This way, the general applicability of the envelope requirement without a drawing note was con‐
firmed for all drawings drafted (released) before 2011 by making an appropriate change in the rele‐
vant standard, making it possible to avoid having to revise (add a note to) the drawings.
The aforementioned modifications to the Volkswagen standards (VW 01014, VW 01054, and
VW 01155) ensured that the Volkswagen Group's old drawings that do not contain a note indicat‐
ing the general applicability of the applicable envelope requirements as per DIN 7167 would contin‐
ue to be valid and that new drawings would clearly specify the general applicability of the envelope
requirement used at Volkswagen.
4th step – adding the independency principle to VW 01054
By adding the independency principle to this issue of the standard, the general applicability of the
envelope requirement to new drawings (i.e., drawings drafted after this standard's date of


### 第 70 页
Page 70
VW 01054: 2016-01
publication) has ceased to apply. This means that, after this issue is published, each drawing must
specify, in its title block, whether it uses the general applicability of the envelope requirement to
sizes or the independency principle.
5th step – setting the independency principle as the default tolerancing principle for the Volks‐
wagen Group
As a result of international standard ISO 8015, the independency principle applies as the default
tolerancing principle. In the next development step, this default specification will also apply to
Volkswagen drawings. However, it will still be possible to use the envelope requirement for individ‐
ual geometrical features (sizes) if required in order to ensure the corresponding fit function.


### 第 71 页
Page 71
VW 01054: 2016-01
Definitions
The terms in DIN EN ISO 286-1, DIN EN ISO 14660-2, DIN EN ISO 17450-1 and the following
terms apply when using this document:
Distance: Dimension between two geometrical features that are considered not to be features-of-
size.
Datum feature: A datum feature is a real geometrical feature found on a part (workpiece) – e.g., an
edge, surface, or hole – that is used to form a datum. A datum feature can be a complete surface
or a part of this surface.
DoLittle: A translation program that supports the creation of multi-language texts (German and
English) in CAD designs based on a specific text catalog standardized by Volkswagen.
Actual size: The size of the associated integral feature-of-size determined with a measurement.
Linear size: A dimension in units of length that identifies a feature-of-size.
Maximum material size (MMS) is the limit of size that results in the maximum material volume. In
the case of external dimensions (shaft, key), it is the maximum dimension; in the case of internal
dimensions (hole, groove width), it is the minimum dimension.
Least material size (LMS) is the limit of size that results in the minimum material volume. In the
case of external dimensions (shaft, key), it is the minimum dimension; in the case of internal di‐
mensions (hole, groove width), it is the maximum dimension.
Plus/minus tolerancing: Tolerancing that uses deviation limits, limits for dimensions, or one-sided
dimensional limits.
NOTE: The "±" symbol must not be understood to mean that the deviation limits must always be
symmetrical relative to the nominal size.
Actual local size: A length dimension measured at a location (two-point size)
Local diameter of an extracted cylinder: The distance between two opposite points on the geomet‐
rical feature, where
–
The connecting line between the points includes the associated circle center point
–
The cross sections are perpendicular to the associated cylinder's axis formed by the extracted
surface
–
The cross sections are perpendicular to the associated cylinder's axis
Local size of two parallel extracted surfaces: The distance between two points on opposite extrac‐
ted surfaces, where
–
The connecting lines between pairs of opposite points are perpendicular to the associated
center plane,
–
The associated center plane is the center plane of two associated parallel planes obtained
from the extracted surfaces (i.e., the distance may deviate from the nominal value).
Angular size: A dimension in units of angle that identifies a feature-of-size.
Appendix C (normative)  

