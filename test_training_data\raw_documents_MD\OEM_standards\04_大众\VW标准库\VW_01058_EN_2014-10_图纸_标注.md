# VW_01058_EN_2014-10_图纸_标注.pdf

## 文档信息
- 标题：
- 作者：
- 页数：65

## 文档内容
### 第 1 页
Group standard
VW 01058
Issue 2014-10
Class. No.:
02245
Descriptors:
type of lettering, drawing designation, drawing specification, drawing note, lettering, drawing, restriction
on use, supplier protection, fundamental tolerancing principle
Drawings
Lettering
Preface
The engineering data management system (KVS) contains two drawing types for machining-based
shaping and non-machining-based shaping. The two types provide an overview of the binding use
of external standards (DIN, DIN EN, and DIN EN ISO) and internal standards (VW).
Machining shaping: FE0.000.610
Non-machining shaping: FE0.000.611
Previous issues
VW 01058: 1963-07, 1978-06, 1999-05, 2002-07, 2004-05, 2008-12, 2010-01, 2011-05, 2012-01
Changes
The following changes have been made to VW 01058: 2012-01:
–
"Preface" section added
–
Section 1 "Scope" changed
–
Section 2 "Requirements", 1st and 2nd paragraph added, 3rd and 5th paragraph changed
–
Section 3.2, 1st paragraph changed
–
Section 3.3 "Reading position of the drawing" changed, Figure 3 adapted to the new drawing
frames, Figure 4 added
–
Section 3.4 "Symbols with designations in the drawing frame" added
–
Section 4.1 "General information", 2nd and 3rd paragraph changed, written content of exam‐
ples standardized, Figure 6 adapted to the new drawing frames
–
Section 4.2 "Indication of units and symbols", example 1 changed
–
Section 4.3.2 "Field for permissible deviations and surfaces", 2nd paragraph expanded,
Figure 7, illustration of the upper and lower deviation of diameter ∅ 6 corrected, Figure 8,
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Page 1 of 65
Technical responsibility
The Standards department
EKDV/4
Uwe Stüber
Tel.: +49 5361 9 29063
EKDV/4 Uwe Stüber
EKDV
Tel.: +49 5361 9 29063
Maik Gummert
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Page 2
VW 01058: 2014-10
illustration of the upper and lower deviation of diameter ∅ 8 corrected Figure 7, Figure 8, and
Figure 9 adapted to the new drawing frames
–
Section 4.3.3 "Field for references", 1st paragraph changed, 2nd paragraph added, Figure 10
adapted to the new drawing frames
–
Section 4.4.1 "Marking of parts", Figure 12 and Figure 13 changed, 1st, 2nd and 3rd para‐
graph under Figure 12 changed
–
Section 4.4.2 "Numbering", Figure 21 adapted to the drawing frames, Figure 22 changed
–
Section 4.4.3 "Text macro (Volkswagen Group expertise protection)" added
–
Section 5 "Specifications in the title block" changed, Figure 26 changed and adapted to the
new drawing frames, legend changed and expanded
–
Section 5.1.1 "Part number", 1st and 3rd paragraph changed, last paragraph in Section 5.1.6
moved, Figure 27 and Figure 28 adapted to the new drawing frames
–
Section 5.1.2 "Designation", 1st paragraph changed
–
Section 5.1.3 "Sheet (drawing with several sheets)", 4th paragraph changed, Figure 29
changed and adapted to the new drawing frames, Figure 30 added
–
Section 5.1.4 "Safety documentation", note 11 added, Figure 31 adapted to the new drawing
frames, Figure 32 added
–
Section 5.1.6 "Scale" added
–
Section 5.1.7 "Format" added
–
Section 5.2 "Field for weight data", paragraph above Figure 37 changed
–
Section 5.5 "CAD system field" new
–
Section 5.6 "Copyright field" new
–
Section 5.7 "Change block", Note 12 added
–
Section 5.7.1 "Newly created drawings", 1st paragraph changed, Figure 72, Figure 74,
Figure 75 and Figure 76 changed, Figure 73 added
–
Section 5.7.2 "Changed drawings", note 14 and 15 added, Figure 80, Figure 81, Figure 82,
Figure 83, Figure 84, Figure 85, Figure 86, Figure 87, Figure 88, Figure 89 and Figure 90
changed, paragraph before Figure 87 and Figure 90 changed, under Figure 81: 2nd paragraph
changed (Approved: ...), 3rd paragraph changed (Changed: ..) Paragraphs under Figure 89
changed, paragraph above Figure 82 changed
–
Section 6 "Converting supplier drawings into Volkswagen AG drawings with restrictions on
use", note 18 and Figure 93 added, Figure 92 changed
–
Section 7 "Porsche drawings" added
–
Section 8 "Applicable documents" updated
–
Section A.1 "Examples of standard-compliant drawing types for Volkswagen AG production
drawings and for production drawings with restriction on use (supplier protection)" Figure A.1,
Figure A.2, Figure A.3 and Figure A.4 adapted to the new drawing frames
–
Standard edited
–
Technical responsibility changed
Contents
Page
Scope ......................................................................................................................... 4
Requirements ............................................................................................................. 4
Drawing frames, format sizes ..................................................................................... 5
1
2
3


### 第 3 页
Page 3
VW 01058: 2014-10
Format sizes for production drawings ........................................................................ 5
Oversize formats ........................................................................................................ 6
Reading position of the drawing ................................................................................. 6
Symbols with designations in the drawing frame ....................................................... 9
Drawing field .............................................................................................................. 9
General information .................................................................................................... 9
Indication of units and symbols ................................................................................ 10
Specifications in prescribed fields ............................................................................ 12
Notes field ................................................................................................................ 12
Field for permissible deviations and surfaces .......................................................... 12
Field for references .................................................................................................. 14
Specifications in the drawing field ............................................................................ 14
Marking of parts ....................................................................................................... 14
Numbering ................................................................................................................ 16
Text macro (Volkswagen Group expertise protection) ............................................. 23
Specifications in the title block ................................................................................. 23
Basic title block ........................................................................................................ 26
Part number ............................................................................................................. 26
Designation .............................................................................................................. 27
Sheet (drawing with several sheets) ........................................................................ 28
Safety documentation .............................................................................................. 29
Code letters, code symbols ...................................................................................... 30
Scale ........................................................................................................................ 31
Format ...................................................................................................................... 31
Field for weight data ................................................................................................. 31
Material specifications .............................................................................................. 33
Material field ............................................................................................................. 34
Material treatment field ............................................................................................. 39
Semi-finished product field ....................................................................................... 40
Surface protection field ............................................................................................ 42
Type approval field ................................................................................................... 43
CAD system field ...................................................................................................... 43
Copyright field .......................................................................................................... 43
Change block ........................................................................................................... 44
Newly created drawings ........................................................................................... 44
Changed drawings ................................................................................................... 45
Converting supplier drawings into Volkswagen AG drawings with restrictions on
use ........................................................................................................................... 51
Porsche drawings ..................................................................................................... 55
General information .................................................................................................. 55
Basics ....................................................................................................................... 55
Drawing formats ....................................................................................................... 55
Drawing creation ...................................................................................................... 57
Basic title block ........................................................................................................ 58
Notes/tolerance field ................................................................................................ 59
Change block ........................................................................................................... 59
In-house standards .................................................................................................. 60
Applicable documents .............................................................................................. 61
.................................................................................................................................. 63
Examples of standard-compliant drawing types for Volkswagen AG production
drawings and for production drawings with restriction on use (supplier
protection) ................................................................................................................ 63
3.1
3.2
3.3
3.4
4
4.1
4.2
4.3
4.3.1
4.3.2
4.3.3
4.4
4.4.1
4.4.2
4.4.3
5
5.1
5.1.1
5.1.2
5.1.3
5.1.4
5.1.5
5.1.6
5.1.7
5.2
5.3
5.3.1
5.3.2
5.3.3
5.3.4
5.4
5.5
5.6
5.7
5.7.1
5.7.2
6
7
7.1
7.2
7.3
7.4
7.5
7.6
7.7
7.8
8
Appendix A
A.1


### 第 4 页
Page 4
VW 01058: 2014-10
Examples of non-standard-compliant drawing types for Volkswagen AG
production drawings and for production drawings with restriction on use
(supplier protection) ................................................................................................. 65
A.2
Scope
This standard regulates the type of lettering used for objects and the use of drawing frames and
text macros as per VW 01014. This standard applies to engineering drawings and other technical
documents, e.g. production equipment drawings and PDAI (product detail assembly instructions).
Any regulations for documentation as per 3D drawingless (3DZP) methods that deviate from or
supplement the main part of this standard, are presented in VW 01058 supplement 1.
The figures given in this standard are examples to illustrate the relevant rule. They are complete
only to the extent that they show the situation described. The represented drawing frames and text
macros are subject to the latest version of VW 01014. The part numbers and standard part num‐
bers mentioned in the figures are examples without any reference to real part numbers or standard
part numbers.
Requirements
The "mm" measurement unit generally applies to a drawing and does not need to be added to the
measurement figure. Measurement units that deviate from this principle must be added to the
measurement figure.
Tolerance as per VW 01054.
For engineering drawings and other technical documentation (for description see section 1) of the
Volkswagen Group, the drawing frames in German and English, as per the latest version of
VW 01014 with the integrated Volkswagen AG copyright note in the drawing title block, must be
used (see section 5).
The use of these drawing frames ensures the reservation of all rights for the Volkswagen Group.
The text in the drawing field must be in German and English and an additional local language may
be added after consultation with the Design Engineering and Standards departments.
Engineering drawings and other technical documents (for a description see section 1) of the Volks‐
wagen Group must not contain any information regarding the supplier (e.g. supplier copyright
notes, supplier logos, etc.), even if created by a parts supplier.
NOTE 1: If it is absolutely necessary to name the supplier part number in an engineering drawing
or other technical documents (for a description see section 1) of Volkswagen AG (e.g. to facilitate
the assignment of Volkswagen part or item numbers and supplier part numbers for extensive AS‐
SYs), these supplier part numbers must not contain any reference to the supplier's identity.
Deviations from the rule in note 1 must be agreed upon between the responsible Design Engineer‐
ing department, the Standards department, and the department responsible for the release of the
drawing.
NOTE 2: Global Sourcing (world-wide procurement) is only possible with these drawings.
Exceptions are drawings of purchase parts which contain elements protected by patents and copy‐
rights. In this case, the supplier's drawing can be used as a basis to create a Volkswagen drawing
(restricted usage) with the consent of the rights' owner (supplier) by using title blocks NO-A7 to
NO-A10 as per VW 01014 (see section 6, "Converting supplier drawings into Volkswagen AG
drawings").
1  
2  


### 第 5 页
Page 5
VW 01058: 2014-10
The question as to whether a restriction on use applies or not is determined in contract negotia‐
tions between the responsible Procurement departments of the Volkswagen Group and the suppli‐
er. Before a drawing release, the person responsible for the component in the respective Design
Engineering department must verify that the drawing complies with the contractual agreements.
NOTE 3: Global Sourcing is not possible with these drawings.
For examples of standards-compliant and non-standards-compliant usage of title blocks for Volks‐
wagen AG production drawings as well as for drawings with restricted usage, see appendix A.
Drawing frames, format sizes
Format sizes for production drawings
The drawing must be produced in the smallest possible drawing format that still provides the nec‐
essary clarity and resolution. Table 1 shows the preferred sizes for trimmed sheets as well as the
drawing area, selected from ISO A series (see DIN EN ISO 216).
Table 1 – Formats of trimmed sheets and drawing areas
Dimensions in mm
Description
see
trimmed (T)
Drawing area
a1 a)
b1 a)
a2
± 0.5
b2
± 0.5
A0
Figure 1
841
1 189
821
1 159
A1
594
841
574
811
A2
420
594
400
564
A3
297
420
277
390
NOTE: For formats >A0, see DIN EN ISO 216
a)
For tolerances, see DIN EN ISO 216
Figure 1 – Formats A0 to A3
3  
3.1  


### 第 6 页
Page 6
VW 01058: 2014-10
Oversize formats
Drawings which are larger than A0 format are considered oversize format. For the CAD systems
used at Volkswagen, the oversize formats must be selected from table 2. The drawing frames for
oversize formats (formats > A0) must be created without templates using text macros (NO-A1, NO-
A6, NO-B1, NO-B3, NO-B6, and NO-B7) as per VW 01014, but should be avoided where possible.
Table 2 – Oversize formats
Dimensions in mm
Format
Final format
A0 plus
841 × 1 682
A0 plus 1
841 × 2 378
A0 plus 2
841 × 3 567
A0 plus 3
841 × 4 756
A0 plus 4
841 × 5 945
Strip formats may also be advantageous for certain space requirements. They are produced by
combining the dimensions of the short side of one A format (e.g. A3) with the long side of another,
larger A format (e.g. A1).
This results in a new format, e.g. with the code A3.1, see figure 2. The format designation must be
specified in the lower right-hand corner of the title block.
Figure 2 – Format system, overview
Reading position of the drawing
The drawing consists of the drawing field and the title block. It has a main reading position (title
block on the right), see figure 3, and a second reading position (title block at the bottom), see
figure 4, which is achieved by rotating the drawing clockwise by 90°. Text information and dimen‐
sions must be legible in these two reading positions.
3.2  
3.3  


### 第 7 页
Page 7
VW 01058: 2014-10
Figure 3 – Main reading position of the drawing


### 第 8 页
Page 8
VW 01058: 2014-10
Figure 4 – Second reading position of the drawing
The right-angled coordinate system (grid), created from the zero and grid lines, for defining the
form and position of individual parts, assemblies, and units in the vehicle is arranged in parallel to
the drawing border. For grid and work lines, see VW 01052.


### 第 9 页
Page 9
VW 01058: 2014-10
Symbols with designations in the drawing frame
For definitions of measurement symbols and designations see VW 01054 and figure 5.
For definitions of material identification symbols and designations see VDA 260 and figure 5.
Figure 5
Drawing field
General information
Entries in the drawing field are made with vertical standard font as per DIN EN ISO 3098-2, font
type B. Deviating from this, a similar font can be selected in a CAD system if the system in ques‐
tion cannot display the font specified above. The font size for
–
part numbers and designations of sections, details, views, work lines, work planes, and func‐
tional datum planes: 5 mm
–
dimensions and for other entries 3.5 mm.
–
In exceptional cases (e.g., standard part drawings, diagrams, machine-written tables, change
tables within the drawing title block), it is permissible to use a font size of 2.5 mm instead of
3.5 mm if there is a lack of space.
The lettering must be short and clear, contain all necessary information, and be as parallel to the
horizontal drawing border as possible, when viewed in the reading position. Texts generated with a
CAD system which features a multilingual text catalog must be created in German and English
using the DoLittle system (German in the upper section, English in the lower section and, if neces‐
sary, numerical information on the side, see figure 8, field "Remarks/notes"). Existing texts (text
macros) that are already contained in VW 01014 in German and English are provided in the re‐
spective CAD libraries and must be adopted 1:1. Several text macros must be expanded upon by
hand with additionally required information. The part numbers and designations must conform with
the TEIVON system (Part Number Assignment Online).
In order not to hinder work processes, another local language may be used, in exceptional cases,
in place of English and alongside German. In such cases, consultation with the appropriate depart‐
ment and the Standards department is required. For existing drawings which have already been
used as production templates, the second language can be omitted in the context of updates or
series development.
The cohesive appearance of the drawing must not be undermined by scattered entries. Information
added to a dimension or graphical representation must therefore be combined in a separate block
connected to the respective location with datum lines and reference arrows. Entries of a general
nature are made under "Notes" in the lower left part of the drawing field; see figure 8.
Sentences begin with capital letters, while individual words, keyword-type information, incomplete
sentences, or brief notes begin with a lower-case letter provided the first word is not a word usually
spelled with a capital first letter.
3.4  
4  
4.1  


### 第 10 页
Page 10
VW 01058: 2014-10
  
Examples:
welded all around
after knurling
without surface protection
only for part ...
up to thread diameter ∅
optional design
weld broached
cold-headed
For new or modified parts, only drawings are available in the early development stage, but no parts
lists or releases for procurement or acceptance. Drawings for parts that are subject to build sample
approval with regard to function, design, safety, and type approval must therefore be marked with
the note "Build sample approval required from Design Engineering" – code number NO-F5 as per
VW 01014; see figure 6.
Figure 6
Indication of units and symbols
In letterings, numbers that are not part of a standard designation must have units. This is because
such representations do not only include dimensions, but also other variables.
–
The unit must not be changed within a numerical indication.
Example: 1 kg + 0.5 kg or (1 + 0.5) kg instead of 1 kg + 500 g
Exception: 15° + 30´
The unit must be selected such that the value can be expressed with as few digits as possible.
Example: 2 kW instead of 2 000 W
Indicating deviations as percent values must be avoided.
–
In enumerations, the unit must be placed only once, behind the last number.
Example: 80, 100, and 150 mm
4.2  


### 第 11 页
Page 11
VW 01058: 2014-10
Numerical values that are linked by the mathematical symbols "+", "-", "x" and ":" are written
with the unit behind each number.
Example: 6 mm × 10 mm × 30 mm or 10 I + 15 I - 2.5 I
If, however, the "+", "-", or "±" signs are used to indicate a tolerance, the corresponding nota‐
tion is: (30 ± 2) m/s/(80 + 5) °C.
Ranges must be written out with "to". The mathematical symbol "..." is permissible; the sym‐
bols "-" or "÷" are not permissible.
Example: 250 Nm to 300 Nm or 250 Nm...300 Nm
The "°" symbol (degree as temperature) is part of the unit and must be placed directly next to
the unit without a space.
Example: 80 °C or (80 ± 5) °C
"°C" must not be used in formulas and combined units; Kelvin (K) must be used for this pur‐
pose.
Example: kJ/(kgK)
–
In the interest of international understanding, the abbreviations "max." and "min." are to be
used instead of "maximum dimension" and "minimum dimension" for specifications that must
not be exceeded or fallen below. The abbreviation must precede the number in written texts.
Examples: min. 400 rpm, min. ∅ 10 mm, and min. 25 Nm
In dimensions that relate to the graphical representation, "max." and "min." follow the numeri‐
cal value.
Example: R 1.5 max.
Mathematical symbols and specifications such as diameter, radius, sphere, and square are
placed in front of the number.
Example:
≥ 12 mm
∅ 20 mm
–
The preferred notation for fractions is in the form of decimal fractions. If proper or improper
fractions are unavoidable, the horizontal fraction line may be used.
Example: 2
3
It is also permissible to use the slash "/" or colon ":" in order to save space.
Example: 2/3, 2:3
–
Symbols or abbreviations may be used only if there is no risk of uncertainty or misinterpreta‐
tions.
For examples of entries that are directly associated with the dimensioning, see VW 01054.
Units
DIN 1301-1
Mathematical symbols
DIN 1302
Symbols
DIN 1304-1
Thermodynamics
DIN 1345
Writing and typesetting of formulae
DIN 1338
Physical quantities and equations
DIN 1313


### 第 12 页
Page 12
VW 01058: 2014-10
Specifications in prescribed fields
Notes field
Entries that are not tied to a specific location or entries that are best combined are made here.
For production, test, acceptance and installation conditions; references to regulations, Supply Spe‐
cifications, Component Performance Specifications (LAH), markings, connecting parts and match‐
ing parts, see figure 8.
Field for permissible deviations and surfaces
The permissible deviations of dimensions that may be produced with usual workshop accuracy
must be assigned to the nominal-dimension ranges of the table for general tolerances and entered
in the "finished" column.
NOTE 4: All lengths in mm, angles in degrees (°), see figure 7.
Tolerances of functional or fit dimensions must be specified beside the nominal dimension; see
figure 7.
Figure 7
The "raw" column (see figure 8) is provided for entry of process-related accuracy values for press‐
ed parts, injection-molded parts, and forgings with surfaces that are left untreated. The relevant
standards concerning "General tolerances" (see DIN SPEC 23605) must be observed in these ca‐
ses.
Dimensional deviations for treated surfaces are entered in the "finished" column.
NOTE 5: All lengths in mm, angles in degrees (°), see figure 7.
4.3  
4.3.1  
4.3.2  


### 第 13 页
Page 13
VW 01058: 2014-10
Figure 8
In the case of castings with surfaces that are left untreated, the tolerances must be obtained from
General Tolerance Standards DIN CEN ISO/TS 8062-2 and entered together with specification of
the accuracy level and the standard number in text macro NO-G8 as per VW 01014 preferably
near the "Notes" field.
Surface roughness. Specifications of the surface finish must be entered in this column as shown by
the examples as per VW 13705 and VDA 2005; see figure 7 and figure 8.
Workpiece edges. Edge conditions without a specific geometric shape, e.g. with burr, sharp-edged,
and free of burrs, are entered in the column intended for this purpose with symbol and code, as
shown in the examples as per VW 01088; see figure 7 and figure 8.
Surface protection. For the finished part, the entered dimensions are generally valid as the accept‐
ance dimensions, including the applied surface protection. If this is not applicable in exceptional
cases, the pre-printed sentence must be crossed out (see figure 9) and "All dimensions apply to
the finished state before surface treatment" must be entered in the "Notes" field.
For surface protection types, see VW 13750.
Figure 9


### 第 14 页
Page 14
VW 01058: 2014-10
Field for references
In the top-right drawing field (next to the change block) legally specified standards are entered as
standard (see VW 01014) which apply to all components in the Volkswagen Group. All additional
technical references in the drawing that are relevant for the manufacture of the component must be
summarized again in the "References" field, see figure 10. This is necessary to provide a clear
overview of the production and acceptance specifications and of the information about documents
required for order processes, which are scattered across the drawing. In multi-sheet drawings, the
specification is made only on sheet 1. Corrections that are required here due to drawing changes
in technical documentation are carried out without a change number and without change text.
As a general rule, the standards and other technical references included in the drawing have a dy‐
namic reference. This means the supplier must ensure that it always works in accordance with the
latest edition of the technical documentation and state-of-the-art technology. Changes and new ed‐
itions of technical documentation are automatically communicated to the supplier via the B2B
(http://www.vwgroupsupply.com/portal01/vw/pub). A requirement for this is that the supplier has or‐
dered a subscription for the technical documentation required. This ensures that the supplier is al‐
ways provided with the latest edition of Volkswagen technical documentation. In the case of exter‐
nal standards (e.g., DIN, ISO standards, etc.), the supplier carries sole responsibility for ensuring
he/she works according to the latest edition. The supplier must carry out a process to ensure timely
inspection, distribution and implementation of all technical documentation, specifications and
changes relevant to the purchaser, on the basis of the schedule requested by the purchaser. The
inspection period and subsequent purchaser notification must take no longer than two working
weeks, see ISO/TS 16949.
NOTE 6: In contrast to the drawing field/title block, types, tests etc. from the cited documents,
e.g., TL 238-A, are not entered in the field for references. Only the standard's identification number
is entered, in this case: TL 238.
NOTE 7: A different approach applies to TLD documentation (Technical Guideline for Documen‐
tation), see section 5.7.2.
Figure 10
Specifications in the drawing field
Marking of parts
Guidelines for part marking are contained in VW 10500 "Company Designation, Marking of Parts",
VW 10514 "Logos", VW 10550 "Country-of-Origin Marking", VW 10540-1 "Manufacturer's Code",
VW 10560 "Date Marking", and VDA 260 "Material Marking". The placement of the marking, includ‐
ing the part number, is specified in the drawing.
4.3.3  
4.4  
4.4.1  


### 第 15 页
Page 15
VW 01058: 2014-10
If the placement of the marking is at the discretion of the manufacturer, the entry is made near the
notes field. If the approximate location on the part is to be specified, figure 11 must be followed. If,
however, the marking must be put in a specific location, this position is indicated with a narrow
dash-dot-dot line in the graphical representation, and the additional specifications are entered un‐
der "Notes"; see figure 12.
Figure 11
Legend
1
The bracket on the right must be adapted to provide sufficient space for the material
code to be specified.
2
The listed marking types are illustrated in text macros NO-E2 (part marking) and NO-
E3 (statutory marking) as per VW 01014.
Figure 12


### 第 16 页
Page 16
VW 01058: 2014-10
The text macro (code number NO-E2 or, if necessary NO-E3, as per VW 01014) – which must be
supplemented by the font size and by the type and size in the case of date marking, a part number
and/or a logo – must be used in each drawing for part marking. Where necessary, the type of appli‐
cation must be specified, e.g. "stamped", "raised", "sunken" etc. If there is no entry, the size and
type are freely selectable in accordance with the applicable standard. For the logo, the type must
always be indicated. If this specification is missing, design "S" as per VW 10514 must be used.
If only a restricted marking is possible, due to a lack of space for example, the text macro for the
part marking should be crossed out, starting from the bottom up, see VW 10500. Further specifica‐
tions for marking must be added, if necessary.
Example:
Nominal voltage
Terminal designation
For valve springs, the manufacturer’s code is marked as per VW 10540-7.
The applicable part of the standard must be added behind standard number VW 10540.
If marking is not possible for geometrical or technical reasons, the text macro with the code num‐
ber NO-E2, see figure 12, is left out. The requirement for marking of parts as per VW 10500 must
then be crossed out in the "Unterlagen/References" field and below the title block, see figure 13.
Figure 13
Numbering
The following types of numbers are permissible:
Part No.
Single parts
ZSB (English 'ASSY')
no.
Assemblies
ZSS (English 'COM')
no.
Combinations
SGR (English 'WA')
no.
Welded assembly
ENT no.
Drafts
ANO (English 'ARA')
no.
Arrangements
ENB (English 'INS')
no.
Installations
TAB no.
Tables
LAH no.
Component Performance Specifications
WHT no.
Multi-use parts
Standard part no.
External or (Volkswagen) internal standard parts
4.4.2  


### 第 17 页
Page 17
VW 01058: 2014-10
Combination and assembly numbers must be indicated in the drawing field by means of the codes
"COM" and "ASSY", which must precede the number.
  
  
Examples:
ASSY 2D0 715 716
COM 1H0 803 245
Part or assembly numbers for which no drawings were produced are followed by the code "o. Z."
(German: ohne Zeichnung) or English "w/o drawing".
Examples:
ASSY 2D0 715 728 w/o drawing
1J0 105 217 w/o drawing
Part or ASSY numbers with uniform main groups and sub-groups may be written in the drawing
field using a simple notation with slashes.
Examples:
1H0 105 211/211 A
1J0/1JE 975 011 A
2D0 105 306 A/306 B
1J0/1JE/1JM 975 011 B
1H0 810 411 w/o drawing /412 w/o draw‐
ing
Combined entries are not permissible.
Example:
1H0/1HE 105 211/211 A
For standard part numbers, this notation is permissible only for indices.
Examples:
N 101 705 12/13/14
N 011 008 1/40/47
N 906 564 01/02
In assembly drawings (ZSB/ASSY), combination drawings (ZSS/COM), or installation drawings
(ENB/INS), single parts are designated with part or standard part numbers, or possibly also by
combination and assembly numbers. Wherever possible, the numbers must be clearly arranged in
groups and connected to the respective single part with a datum line or arrow; see figure 14.
Figure 15 illustrates the use of position numbers to identify parts. The number must have a font
height of 5 mm and must be placed in a circle with a diameter of 10 mm which is connected to the
part by a datum line.


### 第 18 页
Page 18
VW 01058: 2014-10
Figure 14
Figure 15
If parts without drawing (German 'o.Z.', English 'w/o drawing') are listed in ASSY drawings, detailed
specifications for this part, e.g. semi-finished product, material, etc., must be entered in the materi‐
al table in this ASSY drawing (code number NO-F39 as per VW 01014); see figure 16.


### 第 19 页
Page 19
VW 01058: 2014-10
Figure 16
If the dimensions are indicated in the drawing, enter the material and, if applicable, the material
treatment, surface protection, tests, and other specifications. Leave the"Standard/Semi-finished
product" field empty; see figure 17.
Figure 17
If the same part without drawing is used in various assemblies, the note "See drawing: KVS, ASSY
no." must be entered in the material table, see figure 18.


### 第 20 页
Page 20
VW 01058: 2014-10
NOTE 8: If the same part without drawing is used in additional vehicle projects (e.g., as a COP
part), the note (see drawing: KVS, ASSY no.) can be omitted. This means that the work necessary
for alterations to the part without drawing is greatly reduced.
The detailed specifications such as semi-finished product, material, etc. are only documented in
the first ASSY that is defined.
Figure 18
If ASSY numbers without drawings are also listed in drawings, the associated single parts must be
entered with a font size of 3.5 mm below the ASSY number, so that the scope of the assembly w/o
drawing can be recognized; see figure 19. Furthermore, these single-part numbers must be repea‐
ted at the respective locations with a font size of 5 mm (does not apply to perspective drawings).
Figure 19


### 第 21 页
Page 21
VW 01058: 2014-10
If an ASSY part without drawing contains another assembly without drawing, this ASSY part with
no drawing is repeated at the respective location with a font size of 5 mm, and the associated
single parts are listed with a font size of 3.5 mm, see figure 20.
NOTE 9: Part numbers specified on the respective part and belonging to the scope to be re‐
leased are written with a font size of 5 mm.
NOTE 10: Part numbers of parts which are not relevant with respect to the bill of materials (parts
not subject to release) are specified on the respective part with a font size of 3.5 mm and in paren‐
thesis.
NOTE 11: For parts which are identified using item numbers, with a table assigning the part num‐
ber to the item number, the part number font size is 3.5 mm. Part numbers of parts which are not
relevant with respect to the bill of materials (parts not subject to release) must then be put into pa‐
rentheses.
Figure 20
If an ASSY without drawing is listed with its single parts in another drawing, the single parts of the
ASSY without drawing must not be listed again. In this case, a cross-reference must be made to
the other drawing. Types that differ from each other can be shown in the same graphical represen‐
tation if the association of the details on the different parts is unambiguous. Depending on the as‐
sociation, "only" or "not" is placed in front of the part number; see figure 21.
The entry of the 3 part numbers in the basic title block (in a font size of 5 mm) is an exception; see
figure 21.
For the entry of part numbers in the basic title block, see section 5.1.1.
Figure 21


### 第 22 页
Page 22
VW 01058: 2014-10
For associated standard parts that cannot be individually discerned in the view, all numbers are
listed in block form from top to bottom. They are connected to the graphical representation only by
a datum line which ends at a vertical line in front of or behind the part numbers; see figure 22. If
necessary, the part designations must be written with a font size of 3.5 mm behind the number.
Figure 22
In drawings of modules delivered as one complete part that do not have a manufacturing release
for the single parts, single part numbers must not be entered. The required textual specifications
must be indicated in a font size of 3.5 mm, primarily for connecting parts; see figure 23.
Figure 23
If an ASSY drawing includes several identical parts, the part number must be written only once
with graphical representations that are clearly arranged. If the quantity of the identical parts is not
clear, then a connection with a datum line or the entry of multiple notes is advantageous. A simpli‐
fied notation may be used, primarily for drawings with several sheets, by replacing the part number
in the graphical representation with a capital letter in a circle with a diameter of ∅ 10 mm and ex‐
plaining it in the "Notes" field; the quantity of these parts is also indicated here. This method can
also be applied for other, repeating geometries such as holes, weld projections, swages, see
figure 24.


### 第 23 页
Page 23
VW 01058: 2014-10
Figure 24
The text macro with code NO-F2 as per VW 01014 must be used if several OEM parts are included
in a drawing. The letters used are crossed out.
Text macro (Volkswagen Group expertise protection)
The text macro (Volkswagen Group expertise protection, see figure 25) with the code NO-A12 as
per VW 01014 is used for components when it comes to protecting the Volkswagen Group's core
expertises. The drawing must therefore only be used in the Volkswagen Group. The text macro
must be directly inserted next to the drawing title block.
Figure 25
Specifications in the title block
The title block is structured as shown in figure 26. Font size as per table 3.
Basic title block
Section 5.1
Format
Section 3.1
4.4.3  
5  


### 第 24 页
Page 24
VW 01058: 2014-10
Part number
Section 5.1.1
Designation
Section 5.1.2
Sheet
Section 5.1.3
Safety documentation
Section 5.1.4
Scale
Section 5.1.6
Field for weight data
Section 5.2
Material specifica‐
tions
Section 5.3
"Material" field
Section 5.3.1
"Material treatment" field
Section 5.3.2
"Semi-finished product" field
Section 5.3.3
"Surface protection" field
Section 5.3.4
Type approval field
Section 5.4
CAD system field
Section 5.5
Copyright field
Section 5.6
Change block
Section 5.7


### 第 25 页
Page 25
VW 01058: 2014-10
Legend
1
Design responsibility: Name of the responsible design engineer, department name, tel‐
ephone no.
2
Drawn: Name of the drawing creator and name of the company if outsourced (the com‐
pany name in standard lettering must be specified in drawings with the Volkswagen AG
copyright field shown here. No company logo! ).
3
Changed: Name of the responsible design engineer
4
Approved: Name of the head of the Design Engineering department (code)
5
ENT no.: layout drawing number
6
EA no.: Engineering project number
7
Graphic symbol for projection method 1, see DIN ISO 128-30
8
Standards that are always specified for use
Figure 26


### 第 26 页
Page 26
VW 01058: 2014-10
Table 3 – Font sizes
Part numbers (at the level of the dots)
7 mm
For entry of 3 part numbers in the basic title block (exception)
see figure 21.
5 mm
TLD safety documentation
5 mm
Designation of the part or unit (see section 5.1.2)
5 mm and 3.5 mm
Scales (see VW 01050)
5 mm and 3.5 mm
Specifications in the material field
3.5 mm
Specifications in the change block
3.5 mm (2.5 mm)
Specifying information
3.5 mm
Basic title block
Part number
The part number for the basic or standard type is entered in the upper line of the basic title block.
This part number is the registration number of the drawing. Two part numbers may be entered in
the drawing title block. Further part numbers are positioned at the same level in the drawing field
on the left next to the basic title block; each part number must be entered in its entirety. For the
entry of weight information, the text macro "Note on weight indication" – code number NO-F44 as
per VW 01014 – must be used.
Part numbers of left-hand parts have odd end numbers, part numbers of right-hand parts have
even end numbers.
The part number is located between the upper dots (upper line) if it has an odd end digit and be‐
tween the lower dots (lower line) if it has an even end digit. If a suffix, e.g., "C", is added, it is
placed behind the fourth dot, in the size of the part number. The number groups are separated by a
dot, see figure 27.
For part number systems see VW 01098.
Figure 27
5.1  
5.1.1  


### 第 27 页
Page 27
VW 01058: 2014-10
If left-hand parts and right-hand parts are symmetrically opposite, only one part (usually the left-
hand part) is drawn. If, in exceptional cases, the right-hand part must be drawn, e.g. this being the
standard type for left-hand-drive vehicles, the part number for the right-hand part is located in the
upper line of the basic title block and the part number for the left-hand part is located in the lower
line of the basic title block.
The text macro "drawn, sym. opposite", code number NO-E5 as per VW 01014, is placed beside
the basic title block on the level of the part numbers; see figure 27.
Numbers of tabular drawings are entered as per figure 28. Such drawings can be simple lists in
table form or can contain similar parts with deviating individual dimensions listed in a table by using
dimensional letters. The corresponding part numbers without drawing must be assigned to the ta‐
ble in the drawing field.
Figure 28
Designation
The designation of the part must conform with the subject characteristic of the part-designation
system "TEIVON" (Part Number Assignment Online) VW 01098. The designation is restricted to 18
characters. Entries are made in the title field according to the marked subdivision. The standar‐
dized terms ZSB (English ASSY, assembly), ZSS (English COM, combination), ENB (English INS,
installation) etc. - see section 4.4.2 - are located below the three-digit subdivision in front of the
designation. Additional specifications are located below the designation (supplementary informa‐
tion), e.g., the designation of the assembly on the next highest level and/or the module in 3.5 mm
font size, see figure 27.
The "Volkswagen Guideline Concerning the Formation of Designations" (Volkswagen Richtlinien
für die Bildung von Benennungen), maintained by the central TEIVON office, contains rules for
forming designations. Examples:
With respect to clarity:
"Exhaust system" instead of "emission system" or "ex‐
haust emission system"
With respect to clarity:
"Heater valve" instead of "Valve, heater"
With respect to linguistic correctness:
"Roof antenna" instead of "Roof-antenna"
Installation locations and materials must not be part of the designation.
Standard parts and multi-use parts are system parts that may be used in different modules. There‐
fore, drawings of this type must include only the designation and no reference to a specific assem‐
bly or module.
5.1.2  


### 第 28 页
Page 28
VW 01058: 2014-10
If a German designation is entered in the field "Benennung", the English translation of the German
designation is entered with a font size of 3.5 mm in two lines in the "Title" field, observing the part-
designation system "TEIVON".
Sheet (drawing with several sheets)
If a part cannot be reproduced in adequate detail on a single drawing, e.g., if many sections and
views are necessary, subsequent sheets with the same part numbers must be used. Those sheets
must be handled as one drawing. Each sheet belonging to this drawing must include the specifica‐
tion of the sheet number and the total number of sheets in the field provided for this purpose in the
basic title block; see figure 29.
If a cover sheet (overview sheet) is created for drawing scopes with more than three sheets, this
sheet is assigned the sheet number "0" in the basic title block without any indication of the follow‐
ing sheets. The format size of the front page original is A3 (form FE 502). Furthermore, the respec‐
tive number of the subsequent sheets and the associated last change date of the subsequent
sheets (including sheet 1) must be listed in the form of a matrix in the drawing field.
Other affected drawing numbers or further organizational specifications may also be included in the
cover sheet, e.g., titles from the subsequent sheets or change entries for omission/addition of
sheets in the change block.
Changes of geometrical and/or technological specifications must be documented on the changed
sheet. Making change entries in all sheets is permissible and serves to improve the clarity of the
overall drawing, see figure 30.
For release, the changed sheet (and sheet 0, if applicable), together with the sheets not affected
by changes and with the drawing date of the changed sheet or the sheet 0 in KVS, is assigned a
new design status.
For specifications about safety documentation in multi-sheet drawings, see section 5.1.4.
Figure 29
5.1.3  


### 第 29 页
Page 29
VW 01058: 2014-10
Figure 30
Safety documentation
A clearly arranged symbol on production documents must indicate the legal and internal prescrip‐
tive standards that must be observed, which must not be infringed in subsequent modifications.
When newly creating or changing a document marked in this manner, it is therefore mandatory to
check the prescriptive standards (safety regulations, type approval).
The entry "TLD" is made in the "Safety doc." field of the basic title block (title block), see figure 29.
The TLD number of the respective TLD sheet indicates which modules or which characteristics
subject to mandatory documentation are affected by this. It is entered in the "References" field, see
figure 31. The respective Design Engineering department is responsible for correct entry of the
TLD number.
In the Volkswagen Group there are, to some extent, different rules in terms of safety documenta‐
tion for individual brands. An example here is the rules of the Volkswagen and Volkswagen Com‐
mercial Vehicles brands: Organizational Instruction No. 720/0 (OA 720/0) and Process Standard
2.1_EKSE-4_01_PS govern the marking and mandatory documentation requirements for prescrip‐
tive standards.s
NOTE 12: For electronic control units with diagnostic capabilities, the TLD number of the BSD
(build status documentation), - see figure 32 for example - must not be indicated in the "Unterla‐
gen/References" field in the drawing. For barcodes, the TLD number of the BSD must be indicated
in the drawing in the "Unterlagen/References" field. If the component (the engineering
5.1.4  


### 第 30 页
Page 30
VW 01058: 2014-10
document/drawing) is used in several vehicle projects, then the TLD 011000 B0 can be listed in the
"Unterlagen/References" field. In this case a list of the individually affected TLD numbers of the
BSD is not necessary.
Figure 31
Figure 32
Code letters, code symbols
Drawings that include the note "Color as per color combination" (also by means of surface specifi‐
cation) or "Grain as per color combination" in the material field are assigned a "K" with a font size
of 5 mm on the left beside the "Surface protection" field, in order to facilitate coordination; see
figure 33.
K = Color and/or grain according to color combination (German: Farbkombination)
Vehicle parts that form the connection to assemblies or units that affect other Design Engineering
departments are marked in the drawing for the purpose of coordination with the codes:
A
for bodywork parts (German: Aufbauteile)
E
for Electrical parts
F
for chassis parts (German: Fahrgestellteile)
G
for transmission parts (German: Getriebeteile)
M
for engine parts (German: Motorteile)
The marking is written with a font size of 5 mm on the left next to the "Safety doc." field.
5.1.5  


### 第 31 页
Page 31
VW 01058: 2014-10
For example, chassis parts (produced by Chassis Design Engineering department) that connect to
engine parts are assigned an "M", see figure 33. Bodywork parts that are connected to chassis
parts or engine parts that are connected to bodywork parts and vice-versa must be treated accord‐
ingly. If more than one Design Engineering department is concerned, the respective codes must be
written beside each other, separated by commas, such as "A, M".
Figure 33
Scale
For scale information see figure 27.
In the scale field of tabular drawings, the following is entered:
–
A horizontal line if the drawing does not include a graphical representation (tabular lists)
–
N/S (English 'no scale', German 'o.M.') if the graphical representation in the drawing is not true
to scale (for the entry of scale, see VW 01050)
Format
For format data see section 3 and figure 29.
Field for weight data
In order to determine the overall weight of a vehicle or of single units, the weight of all individual
parts is specified in the "errechnet/calculated" field, see figure 34.
Figure 34
The weight is specified in grams. Decimals are rounded to the nearest whole gram as per the
rounding rules defined in DIN 1333. 1 g must be used if the result is less than one gram.
5.1.6  
5.1.7  
5.2  


### 第 32 页
Page 32
VW 01058: 2014-10
Weight values which deviate from the calculated weight as a result from weighing the finished part
must be documented in the technical bill of materials.
Weight variations caused by dimensional tolerances are compensated by means of "averaging".
In the case of drawings with several part numbers, the weight data are assigned to the part num‐
bers for all parts if the weights differ from each other; see figure 35.
Figure 35
In detail drawings containing several alternative types (type A, etc.) with different weights, only the
weight of the heaviest type is entered.
In table drawings (hoses, wiring harnesses, profiles, etc.) and drawings with more than 2 part num‐
bers, the corresponding weight must be included in the table in the drawing field and specified in
the field provided. In the title block, "see drawing" is entered in the weight data field, see figure 36.
Figure 36
If individual parts consist of several materials, the weight proportion of each material must be indi‐
cated as well as the total weight of the individual part; see PS 2.1_NE-G/S_02 and figure 37.
In the title block, "s. drawing" is entered in the corresponding column of the weight data field, see
figure 36.
Figure 37


### 第 33 页
Page 33
VW 01058: 2014-10
In the case of consumables (greases, oils, sealants, etc.), the weight is entered in the technical bill
of materials or in the Design Engineering department's change list, not in the ASSY drawings.
The total weight is specified for assemblies, primarily typical purchase parts, such as alternators,
lamps, door locks, shock absorbers, starters, wiper motors, etc., that are not depicted in detail or
for which no detail drawings are required. If some of these single parts are depicted for functional
reasons, the following information must be placed at the single parts that are part of this scope.
e.g.
Weight: see door lock ASSY
or
Weight: see ASSY 6N0 821 216
If, however, a module completely ready for installation is fully detailed, the procedure is the same
as that for typical detail drawings.
Material specifications
Material information must be specified on drawings as follows (see section 5.3.1 for examples):
–
For materials as per DIN, EN, and ISO standards, specify the standard title and the code.
–
For materials as per Volkswagen standards, specify the Volkswagen titles.
–
For metallic and non-metallic materials, specify the appropriate Technical Supply Specifica‐
tion(s) (TL).
Regional standards such as SAE, ASTM, and/or JIS may be used for materials used in localized
parts.
Special materials and restrictions must be defined explicitly by means of chemical analyses, me‐
chanical characteristics, etc., and specified on the drawing, if required.
If materials are already governed by Volkswagen standards or TLs, only these standards and TLs
may be applied.
All the information in the material field is entered into the designated fields in 3.5 mm font size.
In ASSY drawings that include individual parts without drawings, the material-related specifications
for the parts without a drawing are entered into the material table (code number NO-F39 or
NO‑F40 as per VW 01014). The material table is located in the drawing field, on the left-hand side
of the "References" field.
The material field of the title block then contains "see drawing", and the relevant drawing field must
be entered behind this; see figure 38.
For complex assemblies including many parts without drawings consisting of the same material,
the material table (code number NO-F40 as per VW 01014) must be used. In this table, a material
can be assigned to several item numbers; therefore, it has to be entered once only.
The note "see drawing" or "see notes" is entered only once, preferably in the "Material" field. Other
affected fields (Semi-finished product, Material treatment, Surface protection) are left blank. A line
is drawn through fields that are not affected by the note; see figure 38.
If "see notes" is present in the "Material" field, and if another drawing is referenced in "Notes" in the
title block (e.g., "missing specifications see..."), the fields whose information is to be taken from the
other drawing remain empty.
5.3  


### 第 34 页
Page 34
VW 01058: 2014-10
Figure 38
If only a few parts without drawing are present in assembly drawings, the procedure as per
figure 39 may be used.
Figure 39
Detail drawings that allow optional types (e.g., different manufacturers) with the same part number
but with different materials or properties are listed individually in the material field; see figure 40.
Figure 40
If extensive information is required that will not fit in this field, this information must be entered into
the "Notes" field on the drawing. "See notes" is entered in the material field in this case.
Material field
Material, type of material (quality), and important properties are entered in this field.
If materials that are standardized as per DIN, DIN EN, or DIN EN ISO are used, the standard num‐
ber is followed by the code; see figure 41, figure 43, figure 44, figure 45, figure 46, figure 47,
figure 48, figure 51, figure 49, figure 50 and figure 52.
Material numbers for iron materials (steels) – if present – must be additionally indicated in paren‐
theses.
5.3.1  


### 第 35 页
Page 35
VW 01058: 2014-10
Materials that are not standardized as per DIN, DIN EN, or DIN EN ISO or that deviate from DIN,
DIN EN, or DIN EN ISO must generally be obtained from the Volkswagen standards, Technical
Supply Specifications (TL...), or Stahl-Eisen material sheets, and entered as per figure 54 and
figure 55. Non-metals also receive the complete material designation; see figure 51 and figure 52.
Figure 41 – 
Sheet as per DIN EN 10130
Sheet thickness 1 mm as per DIN EN 10131
Figure 42 – 
Cold-rolled strip as per DIN EN 10139
Grade DC03, lightly temper-rolled, bright
Strip thickness 0.8 mm as per DIN EN 10140
Figure 43 – 
Hot-dip galvanized strip or sheet as per DIN EN 10346
Grade DX51D with zinc coating
140 g/m2 in standard surface finish
Surface type A
Sheet thickness 1.5 mm as per DIN EN 10143


### 第 36 页
Page 36
VW 01058: 2014-10
The material indicated in drawings is the one sufficient for the application. Other materials can be
used for reasons related to production, delivery, or cost-effectiveness by submitting the pertinent
engineering change requests. However, these materials are only entered in the production release
or in order documents. This prevents optional material entries in drawings; these entries are only
used where foreign material entries are unavoidable due to Group manufacturing.
Figure 44 – 
Free-cutting steel as per DIN EN 10277-3: C ≤ 0.14%, S = 0.30%, +C = cold-drawn
Figure 45 – Cold-drawn, stainless steel wire as per DIN EN 10088-3 (X5CrNi18-10). Cold-worked
to a tensile strength between 1 000 and 1 250 MPa. Wire diameter: 1 mm.
For self-tapping screws, bolts, nuts, and similar parts, the symbol for the property class (hardness
class) is specified as per DIN EN ISO 898-1 (bolts, self-tapping screws, and studs),
DIN EN ISO 898-2 (nuts) and DIN EN ISO 898-5 (set screws) instead of the grade as a general
rule; see figure 46.
The grade that is suitable for the specific application (starting material) is then left to the discretion
of the manufacturer.


### 第 37 页
Page 37
VW 01058: 2014-10
Figure 46 – Steel with a tensile strength of at least (8 x 10 x 10) = 800 MPa and a yield point of at
least (8 x 8 x 10) = 640 MPa
Figure 47 – Quenched and tempered steel C45E as per DIN EN 10083-2, quenched and tempered
to a tensile strength of 750 MPa to 850 MPa.
Figure 48 – 
Polystyrene molding material, injection molded
light or weather stabilizer


### 第 38 页
Page 38
VW 01058: 2014-10
Figure 49 – Cold rolled, alloyed aluminum sheet as per DIN EN 485 in T6 condition (solution-an‐
nealed and heat-aged). Sheet thickness: 1.5 mm
Figure 50 – Alloyed aluminum cast as per DIN EN 1706
Figure 51 – 
Elastomer, quality G with Shore hardness = 50 as per VW 2.8.1
Figure 52 – 
Plastic as per TL 481
Figure 53 – Case hardened steel as per DIN EN 10084
  
  


### 第 39 页
Page 39
VW 01058: 2014-10
The Technical Supply Specification (TL) is entered in the material field; see figure 54.
Figure 54
If a TL with a type is required, the index indicating the type is entered, see figure 55.
Figure 55
Wherever possible, special materials are supplemented with analyses, see figure 56. Material des‐
ignations from other companies are not permissible. If they are unavoidable, such designations are
included in the production release and "Special material see release" is entered in the material
field; see figure 57.
Figure 56 – 
Copper-tin alloy with defined
composition
Figure 57 – 
Drawing specification for company-specific
material grades
Material treatment field
Specifications concerning the material treatment (quenched and tempered, hardened, annealed,
etc.) are entered in this field if they are not expressed as part of the material specification with a
code (see figure 42 and figure 44), or if they require deviating and/or additional specifications (see
figure 47 and figure 53).
If the treatment is not required for the entire part, the additional specification "see drawing" in this
field indicates that the pertinent area is marked in the graphical representation; see figure 58.
Figure 58
If different treatments are required on a single part, these treatments are specified at the particular
places in the drawing field and only "see drawing" is indicated here.
5.3.2  


### 第 40 页
Page 40
VW 01058: 2014-10
Hardness testing methods such as, e.g., HRC, HV, HBW, and Shore are entered without units, see
figure 47, figure 51 and figure 53. On the other hand, the ball indentation hardness is specified in
N/mm2 in the case of plastic parts.
Please refer to DIN ISO 15787 for information on the depiction of hardened parts and their specifi‐
cations in drawings.
Semi-finished product field
Only finished dimensions that indicate the final condition of the part are entered in the drawing
field. The Design Engineering department or Production Planning department define the ordering
dimensions, which are carried over into the production release.
For parts manufactured from sheet metal or strip metal – see figure 41 to figure 43, figure 49,
figure 59 – and dimensions of wires – see figure 60 – that are cut to length as a semi-finished prod‐
uct or are installed bent, the Design Engineering department will define the semi-finished product
dimensions such as thickness, diameter, width (if applicable), etc. Deformation causes process-re‐
lated deviations, which permit the specified tolerances to be exceeded.
Figure 59
Figure 60
The tolerance is omitted if the dimensional standard, which must be specified as well, unambigu‐
ously defines this. If it is not possible to use a dimensional standard or if standardized plus/minus
deviations must be shifted to one side, tolerance specifications are absolutely necessary; see
figure 61 and figure 62.
Figure 61
Figure 62
The width of strips with trim edges (GK, German: geschnittene Kanten) is not specified in the draw‐
ing (see figure 42) but is instead defined by the Production Planning department. If a mill edge
(NK, German: Naturkante) is required on the finished part, however, the strip width and the adden‐
dum "NK" must be specified in addition to the strip thickness; see figure 59.
Designations, such as "sheet," "strip," "tube," or "wire," etc., are not used, because these details
are indicated by the dimensional standard or by the drawing itself. If dimensions are specified that
deviate from the standard, the "approximation" symbol must be placed before the DIN number.
Only standardized semi-finished products must be entered in the "Semi-finished product" field.
For parts manufactured from sheet metal or strip metal that can be manufactured within a certain
thickness range without any functional impairments, enter text macro NO-F1 as per VW 01014 into
the drawing field so that scrap can be reused if necessary. In addition to the prescribed target
thickness in the basic title block, the permissible thickness range is entered in the text macro.
5.3.3  


### 第 41 页
Page 41
VW 01058: 2014-10
If a workpiece is manufactured from two different sheet metal thicknesses and the same semi-fin‐
ished product (tailored blanks), the difference in sheet metal thickness is specified as per figure 63.
"See drawing" is entered in the "Semi-finished product" field.
Figure 63
If, for functional reasons, a workpiece needs to be reinforced at different locations with a second
sheet (patchwork), the specifications on material and semi-finished products must be entered as
shown in figure 64.
Figure 64
If there are more than two reinforcing sheets in different sheet thicknesses and/or different materi‐
als, this may also be represented in tabular form; see figure 65. "See drawing" is entered in the
"Semi-finished product" field.
Figure 65


### 第 42 页
Page 42
VW 01058: 2014-10
Surface protection field
Specifications concerning the surface protection are entered in this field, mainly by using codes as
per VW 13750. The following text is entered if only certain areas of the workpiece are to be protec‐
ted: "See drawing". The affected location on the part is then marked by a strong dash-dot line and
connected to the code by a datum line.
If several alternative types of surface protection are permissible, all codes of the permissible surfa‐
ces are specified separated by slashes; see figure 66.
If different protection types are specified, however, the second (or further) protection type must be
placed in brackets and "see drawing" must be entered in the surface protection field, see figure 67.
The corresponding location on the part is clearly limited. For more information, see VW 13750.
Figure 66
Figure 67
Other surface treatments must be entered accordingly.
All dimensions in the drawing field apply to the finished part including surface protection; also see
section 4.3.2.
Figure 68
If parts are to be manufactured from semi-finished products with surface protection, as is the case
with e.g. zinc-coated or copper-plated steel wire, bar stock, or sheet metal, the type of protection is
part of the material or semi-finished product designation; see figure 43 and figure 68. The trim
edges produced in subsequent processing steps will have no surface protection.
"Bright", "oiled", "press bright", etc., are not regarded as a surface treatment. If necessary, these
specifications will be entered under "Notes" in the drawing field.
"Oiled" is specified in the drawing only if the part must be installed in oiled condition.
5.3.4  


### 第 43 页
Page 43
VW 01058: 2014-10
Type approval field
A "type approval number" is entered if it has been assigned to the respective component, see
figure 69. It is used to facilitate the authorities' approval process.
The type approval number is assigned by the Design Engineering department in consultation with
the Type Approval department.
Figure 69
In drawings encompassing several sheets, the type approval number is to be entered only on the
sheet that is directly affected by this.
CAD system field
The employed CAD system is indicated in the CAD system field, for an example see figure 26 and
figure 70.
Figure 70
Copyright field
See figure 26 and figure 71.
Figure 71
5.4  
5.5  
5.6  


### 第 44 页
Page 44
VW 01058: 2014-10
Change block
The change block is the history file of the drawing. It must clearly indicate the development of the
drawing, any previous editions, as well as every change. The descriptions are entered consecu‐
tively from the top down.
NOTE 13: The change block is only filled out in the company language (German) for the purpo‐
ses of saving space.
Newly created drawings
When creating a new drawing, enter a dash or horizontal line into the "No.", "Section", "Change",
and "Revision record and change date code" columns on the topmost line; see figure 72; the indi‐
cation of an AEKO no. (change in design number) or ECR no. (engineering change request num‐
ber) is also permissible, see figure 73.
The date is entered by the Design Engineering department.
Figure 72
Figure 73
If a part is drawn that was previously released without drawing (w/o drawing), the procedure as per
figure 74 must be followed.
Figure 74
5.7  
5.7.1  


### 第 45 页
Page 45
VW 01058: 2014-10
In the case of a part that is similar to a previously designed part, "Similar to part ... ... ..." is entered
in the first row, e.g. as per figure 75. This note is intended to draw attention to fixtures or tools that
may already be available.
Figure 75
If a drawing is redrawn without any changes, the entry is made as per figure 76. The first row must
contain the most recent change date of the preceding drawing.
Figure 76
Changed drawings
Every change in a drawing must be specified in the change block and must be assigned a change
number. The changed location is identified by the change number with a font size of 3.5 mm which
is displayed in a change circle of 8 mm diameter.
Exception:
In the case of the following changes, namely
–
weight data in the weight data field,
–
TLD safety documentation
–
type approval number in type approval field,
the change is not indicated by a change number or circle, but only by a corresponding text in the
change field.
Corrections to technical documents summarized at the upper right in the drawing field are not con‐
sidered to be a drawing change, see section 4.3.3.
NOTE 14: If a drawing is modified, the current drawing frame must always be used, which con‐
tains the note on the fundamental tolerancing principle as per VW 01054. In exceptional cases
where this may not be possible, the note must be placed in the notes field by means of text macro
NO-A11 as per VW 01014.
The change circle is placed in a clearly visible position as close as possible to the changed loca‐
tion. It should also be located to the right of the changed location if possible – in the case of dimen‐
sions, primarily on the extension of the dimension line, see figure 77. If, however, the dimension
line is longer than two section divisions, the change circle must be placed above the dimension fig‐
ure. If changes are necessary in the basic title block or in the material field, the change circle must
be located at the left edge of these fields if this can be done without causing ambiguity; see
figure 78.
5.7.2  


### 第 46 页
Page 46
VW 01058: 2014-10
If the allocation is not clearly recognizable because several change circles are located close to‐
gether or due to a lack of space, the circles are connected to the changed location with short da‐
tum lines; see figure 79. Change circles within the graphical representation must be avoided where
possible.
Figure 77
Figure 78
Figure 79
The change number must appear in the drawing field only once.
NOTE 15: Exception: When adding a new view to a drawing, the change number may be placed
onto the new view and the view arrow.
If a changed location is changed again in the same original, the previous change number remains
in the drawing field; it is repeated in the change block with a new date.
The following changes are entered in the change block:
Description: The type and scope of the change are briefly described under the same change num‐
ber in such a way as to indicate the state of the drawing before the change, see figure 80.
If the change text under a single change number encompasses two or more rows, the text is linked
by a bracket; see figure 81.
NOTE 16: Deviations to the procedures described above (figure 80 and figure 81) are entered in
the "3D drawingless process", see VW 01058 supplement 1.
Figure 80


### 第 47 页
Page 47
VW 01058: 2014-10
Figure 81
If necessary, the documents on which the change is based, e.g., control number, engineering
change request, AEKO, special design documentation sheet, supplier drawing, etc., must be en‐
tered behind the change description, see "Change date code" and figure 81.
Approved: The name of the head of the Design Engineering department (code) is entered here.
Change: The name of the design engineer who made the change or who arranged it to be made is
entered here.
Date:This is entered by the drawing compiler from the respective Design Engineering department.
If there are several changes or if the text, under a change number, encompasses several rows, the
date is entered in the first and last rows; see figure 80 and figure 81.
Section: Drawing formats sized DIN A2 and larger have section divisions at the edge (grid squares)
to facilitate the locating of the changed location. Therefore, the section containing the change circle
must be indicated for these drawings.
The change number is entered without circle in the "No." column. If the text under one change
number encompasses several rows, the number is entered in the last row; see figure 81 (change
number 2).
If several change numbers have the same change text, such changes must be summarized in the
change block, e.g.
Change 1, 2, 3, 4 and 5:
Notation 1...4/51), see figure 82
Change 5 and 7:
Notation 5/71), see figure 82
Figure 82
1)
The "/" symbol stands for "and" (enumeration).


### 第 48 页
Page 48
VW 01058: 2014-10
If a drawing is redrawn due to a change (e.g., a change block is full), the most recent change date
of the previous drawing must be entered in the first row. This is followed by the entry "Redrawn
with change"; see figure 83.
Figure 83
If the drawing was distributed in advance without having been granted a release, the change date
must be crossed out; see figure 84.
Figure 84
If, however, the shape of the part is to be changed to such an extent that the associated change
text cannot be clearly described, even using several rows, a short text – with change number
whenever possible – is entered, e.g., as per figure 85.
Released parts that are redesigned to such an extent that they are no longer interchangeable with
parts previously used in the vehicle or differ significantly with regard to the material, are issued in
new drawings with a new part number. See also part number systems VW 01098.
Figure 85


### 第 49 页
Page 49
VW 01058: 2014-10
If, for production-related reasons, foreign subsidiaries/associate companies can manufacture parts
only using processes that deviate from our drawing specifications or if special requirements must
be taken into account, e.g., for after-sales service or CKD parts, the parts in question are issued
with a new drawing and a new part number. Any change numbers in already released drawings,
which generally receive a suffix, must be removed. The entry in the change block must be made as
per figure 86.
Figure 86
If a production drawing is given a different part number (e.g., as required by after-sales service),
"number changed (was ...)" is entered in the first row of the change block, as shown in figure 86, or
the reason for the change is indicated by naming the engineering change request, an AEKO num‐
ber, or a model update item. The note "Drawing redrawn" is also permitted.
If safety documentation specifications are changed, the procedure is the same as with normal
drawing changes; however, the safety documentation code "TLD" in the basic title block and the
TLD number under "References" in the drawing field are not given a change number but are in‐
stead written in the change block in plain text, e.g.:
–
Symbol "TLD" and "TLD ... ... .. removed".
If part numbers and the associated sections, dimensions, and notes, etc., must be removed or add‐
ed in the drawing field, the change circle must be placed at the location of the part number. The
other affected details are listed in the change block according to the respective scope:
–
Part ... ... ... removed with the associated specifications, sections, notes, and dimensions or
part no. with ... added.
If ASSY or COM numbers without drawing are removed or added with the associated part num‐
bers, each affected part number must be issued a change number and additionally entered in the
change block. If special circumstances necessitate it, e.g., if there are plans to create a new draw‐
ing, the specifications to be removed must be crossed out on the drawing only.
If there are several released part numbers in a drawing and some of them will become invalid after
a certain date (e.g., with introduction of a new engineering project number), these part numbers
must be crossed out on the drawing. The following must be entered in the change block under‐
neath the corresponding change number:
–
Part ... ... ... is invalid, with all of the corresponding information, starting with engineering
project number ... .. (or a certain date).


### 第 50 页
Page 50
VW 01058: 2014-10
If, for drawings with several sheets, changes must be carried out to part numbers located in the
basic title block or to the left of it, the change numbers and the change text must only be entered
on sheet 1; see figure 87. The described procedure - see section 5.1.3 (figure 30) - is also permis‐
sible.
Figure 87
The part numbers of subsequent sheets are changed as per sheet 1 but do not receive any change
numbers. A note referring to the change text on sheet 1 is entered instead; see figure 88.
Figure 88
If sheets are added to a drawing that already includes several sheets, the total sheet number must
be adapted on all sheets. The change is entered with change text - without change number - on
sheet 1 only; see figure 89.
NOTE 17: For drawings with several sheets and cover sheet, see section 5.1.3.
Figure 89
In order to track changes from the P-release to the B-release, the procedure as per figure 90 must
be used:


### 第 51 页
Page 51
VW 01058: 2014-10
The name of the drawing compiler with current responsibility is entered into the "Change" column.
The change block index and change block designation are not deleted after B-release, rather they
are retained. The change block is not crossed out.
Figure 90
Converting supplier drawings into Volkswagen AG drawings with restrictions on use
Taking the necessary patent and copyright regulations and test and administrative requirements in‐
to account, supplier drawings can be modified with little work to produce Volkswagen AG engineer‐
ing drawings for supplier parts. With the respective supplier's agreement as a prerequisite, these
modified drawings must contain all of the information necessary for release, acceptance, and quali‐
ty monitoring.
The following method is used if supplier companies cannot produce drawings on Volkswagen AG
drawing templates due to reasons of copyright protection or similar reasons.
–
The title block and change block from Volkswagen AG code number NO-A7 are added to NO-
A10 (for sizes DIN-A3 to DIN-A0 and >DIN-A0 as per VW 01014) on the right, next to the dra‐
wing's title block, to drawings that must be provided to Volkswagen AG following agreement
between Design Engineering and external suppliers. The designation, Volkswagen part num‐
ber, material and surface specifications and any applicable safety codes, etc., are entered into
the added title block and change block; see figure 92.
NOTE 18: If the CAD system will not permit the aforementioned conversion procedure and
complete frames for drawings with restriction on use must be created, it must be ensured that
the title block has exactly the same content as the text macros NO-A7 to NO-A10 as per
VW 01014.
–
In the interest of conformity, the last change date of the supplier drawing must be entered un‐
der "Revision record and change date code", in addition to the Volkswagen change date.
–
Once the conversion of supplier drawings to Volkswagen AG drawings with restriction on use
has been effected and a first release has been issued for the Volkswagen AG, drawing
changes must be documented in the change block of text macros NO-A7 to NO-A10.
–
If necessary, the drawings must be completed with further detailed specifications or necessary
supplements, e.g. with requirements for part marking (NO-E2 as per VW 01014).
–
NOTE 19: If spacial-requirement problems arise when entering further data in the Volks‐
wagen drawing field, the Volkswagen title block must be moved to the right, see figure 93.
6  


### 第 52 页
Page 52
VW 01058: 2014-10
In order to ensure that copyrights or commercial property rights of suppliers are not violated by
modified Volkswagen drawings being passed on to unauthorized recipients (competitors), the com‐
pany name of the part manufacturer must be entered in the title block (NO-A2), see figure 91.
Figure 91


### 第 53 页
Page 53
VW 01058: 2014-10
Figure 92 – Example of a supplier drawing in the DIN A3 format and use of the text macro NO-A7 for the DIN A3 format


### 第 54 页
Page 54
VW 01058: 2014-10
Figure 93 – Example of a drawing with restriction on use; here, the Volkswagen title block (text


### 第 55 页
Page 55
VW 01058: 2014-10
macro NO A7) is moved to the right
Porsche drawings
General information
Section 7 "Porsche drawings" is intended to show the user that there are currently still differences
between Volkswagen and Porsche drawings (and the associated, connected processes). A total
standardization of the drawing processes at Volkswagen and Porsche is a stated objective and will
be achieved through a series of additional stages.
NOTE 20: The current, agreed status of section 7 "Porsche drawings" has a limited applicability
up to 2016-06-30.
Basics
Due to the systematic correlation between the engineering drawing, the entries in the basic title
block and Porsche data management (P-DMU), direct generation and use of the original Volks‐
wagen drawing frame is not possible.
Porsche AG therefore uses its own drawing frame, which is customized to Porsche systems and
work methods. These are based, both graphically and textually, on the drawing frame of the Volks‐
wagen Group.
If a release has been granted and a drawing saved in KVS, Volkswagen Group drawings created
by Porsche (hereinafter known also as the "Porsche drawing format") can be used, unchanged, by
additional Group brands as a same part, hat part, or carry-over part, without restrictions.
There are no plans to modify or expand Porsche drawing formats into a complete Volkswagen
Group standard for alternate usage cases such as these.
Porsche drawing formats may only be created or edited within a Porsche system environment and
only with associated tools, such as PAG-TBE (TitleBlockEditor). Otherwise, usage in Porsche sys‐
tems is not possible.
The following sections describe features of the Porsche drawing creation in the context of a cus‐
tomized Group drawing frame for Porsche.
Design details are only dealt with briefly in this standard; complete descriptions can be found in
VW 96019 "Drawing creation" (= PN 150).
Drawing formats
Drawing formats are available in sizes A3 to A0, in addition to special A0 sizes of 6 m, see
figure 94.
"Manufacturer's drawings with restriction on use" are also available in the sizes A3 to A0. These
consist of a strip that is to be placed directly onto the manufacturer's identification with a basic title
block and change block.
7  
7.1  
7.2  
7.3  


### 第 56 页
Page 56
VW 01058: 2014-10
Figure 94 – Porsche drawing format (here: A3)
The most conspicuous feature of Porsche drawings is a table to the left of the basic title block with
internal, system-based entries, see figure 95. Aside from Porsche usage, these entries are of no
relevance for drawings in the Volkswagen Group.
Figure 95 – Porsche system data fields
The Porsche system data fields contain:
–
In the "Revision" field, the drawing date of revision is entered as a 3-digit number. At Porsche,
this number is intended to indicate the date of revision; it is not a release date.
–
In the "Subjectivity to approval" field, markings such as CCC are entered.


### 第 57 页
Page 57
VW 01058: 2014-10
–
In the "Porsche material no." field, an administration number which serves to classify the mate‐
rial is entered.
–
In the "Description of version" field, an additional description of the given component can be
made using freely selected key words.
For manufacturer's identifications which have not, for legitimate reasons, been created in neutral
Porsche drawing formats, "Manufacturer's formats with company-related restrictions on use" must
be used, see figure 96.
NOTE 21: These formats do not include fields for surface and tolerance entries. This data must
be indicated on the manufacturer's identification in compliance with the applicable standards.
Figure 96 – Porsche drawing format with restriction on use
Drawing creation
Porsche creates its drawings based on applicable drawing standards such as ISO 128 and
DIN 406.
Design details and specific procedures are specified in VW 96019 (= PN 150).
Insofar as there is no explicit agreement to the contrary with other drawing users of the Volks‐
wagen Group, non-standard data and methods for Porsche which are nevertheless part of the
Volkswagen drawing guidelines are not entered for the purposes of Porsche drawings, nor are they
used in other forms.
This includes, for example, processes such as build sample approval, type approval, Volkswagen
standards in place of Porsche standards, TLD specifications, bilingual text entries, etc.
7.4  


### 第 58 页
Page 58
VW 01058: 2014-10
Basic title block
Entries in the basic title block are made solely with the entry tool PAG-TBE (TitleBlockEditor).
As a result of system-based linkage, it is not permissible to manipulate or fabricate (using a manual
text tool) a correct field entry.
For system-based reasons, only one part number can be entered in the "Part no." field, even if a
second number exists for a right-hand part, for example, see figure 97. Such second part numbers,
in addition to variant numbers are entered into the variable table so that they are close to the basic
title block, see figure 98.
Figure 97 – Basic title block
Figure 98 – Basic title block with variant table (shown here for two part numbers)
7.5  


### 第 59 页
Page 59
VW 01058: 2014-10
Notes/tolerance field
See figure 99.
In contrast to the original Volkswagen formats, no space is made for tolerance tables and these
should also not be manually introduced or re-introduced. Tolerances must be specified using suita‐
ble tolerance standards (e.g. DIN ISO 2768-1 and DIN ISO 2768-2) and directly-entered dimen‐
sional tolerances or by means of 3D toleration as per VW 96033 (= PN 265).
Text entries in the tolerance field are made solely with the entry tool PAG-TBE (TitleBlockEditor).
As a result of system-based linkage, it is not permissible to manipulate or fabricate (using a manual
text tool) these texts. Graphic symbols are entered with CATIA tools as allocated to the given sheet
(not to the view).
Figure 99 – Notes and tolerance field
Change block
The change block is adapted to Porsche processes in terms of content, see figure 100.
Figure 100 – Change block
The first row of the change block contains a date of revision number for the applicable date of revi‐
sion (Rev.). This is given precedence over the date (of revision) entered. The date of revision num‐
ber is identical to the "Revision" entry in the Porsche system data block.
All changes to a temporal process are collected in a date of revision number. That means that not
all individual measures receive just one uniquely-assigned number.
The date of revision number is not affixed as informative data to the relevant change in the draw‐
ing. The date of revision number can only be found under the field entry and in the change descrip‐
tion. For Porsche drawings that feature "balloons" with numerical entries, the numbers are not date
of revision numbers but rather, as a general rule, item numbers of a drawing bill of materials.
The "Checked" column contains a code from the responsible technical organization.
The first change row always indicates the drawing creation date (creation, not first release).
7.6  
7.7  


### 第 60 页
Page 60
VW 01058: 2014-10
Text entries in the change block are made solely with the entry tool PAG-TBE (TitleBlockEditor).
As a result of system-based linkage, it is not permissible to manipulate or fabricate (using a manual
text tool) these texts.
In-house standards
The specification of the in-house standards used in the drawing, whether they be PN… or VW…, is
the responsibility of the part owner insofar as there are no existing, project-specified usage specifi‐
cations for certain standards. In the case of equivalent standards, with regard to the effects on sub‐
sequent processes (an overall process does not exist; the specified process is leveraged...), the
applicable Volkswagen standards take precedence.
If a Porsche standard is used, the PN number is indicated directly and not the equivalent (in terms
of content) Volkswagen number 96xxx. Information in brackets that is added to the Volkswagen
reference is permissible, e.g., PN 265 (VW 96033).
NOTE 22: Currently not all Porsche standards are provided in NOLIS (Standards Online Informa‐
tion System, German: "Normen Online Informationssystem") by Volkswagen. An overview of the
content is provided in VW 96000 (reference list).
7.8  


### 第 61 页
Page 61
VW 01058: 2014-10
Applicable documents
The following documents cited in this standard are necessary to its application.
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
TLD 011000 B0
Build Status Documentation (Overview)
VW 01014
Engineering Drawings; Drawing Frames and Text Macros
VW 01050
Technical Drawings; Scales, Lines, Hatching, Break Lines
VW 01052
Engineering Drawings; Representations
VW 01054
Engineering Drawings; Dimensioning
VW 01058
supplement 1
3-D Drawingless Process (3DZP)
VW 01088
Workpiece Edges; Definitions, Drawing Specifications
VW 01098
Part Number System
VW 10500
Company Designation, Marking of Parts; Guidelines for Use
VW 10514
Logos; Marking of Vehicle Parts
VW 10540-1
Manufacturer's Code; for Vehicle Parts
VW 10540-7
Identification of Valve Springs
VW 10550
Country-of-Origin Marking; Vehicle Parts
VW 10560
Date Marking; Vehicle Parts
VW 13705
Specification of Surface Texture; Geometrical Product Specifications -
Engineering Drawings
VW 13750
Surface Protection for Metal Parts; Surface Protection Types, Codes
VW 96000
Management of Porsche Standards in Volkswagen Systems; Reference
List
VW 96019
PN 150 - Zeichnungserstellung; Grundlagen
VW 96033
General Tolerances of Components; Produced on the Basis of CAD/3D
Data
2.1_EKSE-4_01_PS
Documentation with the TLD System
B2B
supplier platform (vwgroupsupply)
DIN 1301-1
Units - Part 1: Unit names, unit symbols
DIN 1302
General mathematical symbols and concepts
DIN 1304-1
Letter symbols for physical quantities; symbols for general use
DIN 1313
Quantities
DIN 1333
Presentation of numerical data
8  


### 第 62 页
Page 62
VW 01058: 2014-10
DIN 1338
Writing and typesetting of formulae
DIN 1345
Thermodynamics; terminology
DIN CEN ISO/
TS 8062-2
Geometrical Product Specifications (GPS) - Dimensional and geometri‐
cal tolerances for moulded parts - Part 2: Rules
DIN EN ISO 216
Writing paper and certain classes of printed matter - Trimmed sizes - A
and B series, and indication of machine direction
DIN EN ISO 3098-2
Technical product documentation - Lettering - Part 2: Latin alphabet, nu‐
merals and marks
DIN EN ISO 898-1
Mechanical properties of fasteners made of carbon steel and alloy steel -
Part 1: Bolts, screws and studs with specified property classes - Coarse
thread and fine pitch thread
DIN EN ISO 898-2
Mechanical properties of fasteners made of carbon steel and alloy steel -
Part 2: Nuts with specified property classes - Coarse thread and fine
pitch thread
DIN EN ISO 898-5
Mechanical properties of fasteners made of carbon steel and alloy steel -
Part 5: Set screws and similar threaded fasteners with specified hard‐
ness classes - Coarse thread and fine pitch thread
DIN ISO 128-30
Technical drawings - General principles of presentation - Part 30: Basic
conventions for views
DIN ISO 15787
Technical product documentation - Heat-treated ferrous parts - Presen‐
tation and indications
DIN ISO 2768-1
General tolerances; tolerances for linear and angular dimensions without
individual tolerance indications
DIN ISO 2768-2
General tolerances; geometrical tolerances for features without individu‐
al tolerances indications
DIN SPEC 23605
Technical prduct specification (TPS) - Application guidance - Structured
and commented overview of ISO- and DIN-Standards for technical prod‐
uct dokumentation (TPD) and geometrical product specification (GPS)
ISO/TS 16949
Quality management systems - Particular requirements for the applica‐
tion of ISO 9001:2008 for automotive production and relevant service
part organizations
OA 720/0
Documentation of the Development, Production, and Distribution of Mo‐
tor Vehicles
PS 2.1_NE-G/S_02
Material Documentation
VDA 2005
Geometrical product specification - Technical drawings - Specification of
surface texture; inclusive Appendix 1
VDA 260
Components of motor vehicles - Marking of material
Sources for suppliers:
The documents and free tools of the Volkswagen Group may be retrieved over the B2B supplier
platform (http://www.vwgroupsupply.com) of the Volkswagen Group with an access authorization.
Contact via B2B Support or via e-mail: <EMAIL>.


### 第 63 页
Page 63
VW 01058: 2014-10
 
Examples of standard-compliant drawing types for Volkswagen AG production drawings
and for production drawings with restriction on use (supplier protection)
Volkswagen AG production drawings (no restriction on use), see figure A.1.
Figure A.1 – Instruction for standard-compliant creation of Volkswagen AG production drawings
Appendix A (informative)  
A.1  


### 第 64 页
Page 64
VW 01058: 2014-10
Drawings with restriction on use, see figure A.2
Figure A.2 – Instruction for standard-compliant creation of drawings with restriction on use


### 第 65 页
Page 65
VW 01058: 2014-10
Examples of non-standard-compliant drawing types for Volkswagen AG production
drawings and for production drawings with restriction on use (supplier protection)
See figure A.3 and figure A.4.
Figure A.3 – Example for an incorrect Volkswagen production drawing
Figure A.4 – Example for an incorrect drawing with restriction on use
A.2  

