# VW_01059_5_EN_2015-04_CAD&CAM数据要求_第5部分：CAD系统PTC Creo参数.pdf

## 文档信息
- 标题：
- 作者：
- 页数：29

## 文档内容
### 第 1 页
Group standard
VW 01059-5
Issue 2015-04
Class. No.:
22632
Descriptors:
CAD, CAM, requirement, Creo Elements/Pro, Pro/ENGINEER, CAD system, Pro E, Creo, Creo
Parametric
Requirements for CAD/CAM Data
CAD System PTC Creo Parametric
Previous issues
VW 01059-5: 2000-07, 2001-05, 2003-07, 2004-02, 2005-07, 2009-11, 2012-06
Changes
The following changes have been made to VW 01059-5: 2012-06:
–
CAD system name changed from Creo Elements/Pro to PTC Creo Parametric (abbreviated as
Creo)
–
Technical responsibility changed
–
Preface removed
–
Section 2: Terms "Creo," "ECA," "Full Check," "KSE," "KVS," "PDA," "Quick Check," and "ZP7"
added; "ProRepair" deleted; "VW ModelCheck" – term explanation modified, spelling changed
in entire document, all instances of "sub-part" changed to lower case in the German version of
the document.
–
Section 3.1: Supplier platform path modified
–
Section 3.2: "ModelCHECK Volkswagen configuration set" deleted; Group configuration files
"din.dtl" supplemented with "(up to release GRI 09.00.02)" and "vw_din.dtl" supplemented by
"(from release GRI 09.01.00)"; Reference to VW 01098 added; using the DoLittle application
prescribed; NOTE 1 deleted; use of "flexible modeling" banned; two last sentences added
–
Section 3.3.2: terms "Features", "Datums", "Skeleton", and "Templates" start with capital let‐
ters
–
Section 3.3.3: Reference to VW 01098 added; Table 1: word "Benennung (Designation)" capi‐
talized in the German version of the document; word "Designation" added in rows "Drawing
format" and "User defined feature"; in the legend for table 1: in "Designation", text in square
brackets changed from [-, _] to [-_]; in "c", words "part" and "assembly" written in italics, in
"company", abbreviation "co" corrected to be "po"
–
Section 3.3.4: Word "variants" added in text before "instances"
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Page 1 of 29
Technical responsibility
The Standards department
K-SIPE-2/2
Thomas Reineke
Tel.: +49 5361 9 49464
EKDV/4 Norbert Wisla
EKDV
Tel.: +49 5361 9 48869
Maik Gummert
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Page 2
VW 01059-5: 2015-04
–
Section 3.3.6: "Default layers" and associated text deleted
–
Section 3.4.1: Note 2 deleted and inserted as normal text
–
Section 3.4.2: 1st sentence: Word "(instances)" added in the German version of the document;
word "Instanzen" replaced with "instances" in the German version of the document
–
Section 3.4.4: Term "default layer" replaced with "template layer"; Figure 6 changed; require‐
ment to place threads on the "_THREADS" layer deleted; requirement "Layer rules must not
be defined for template and default layers" deleted; "default layers" changed to "template lay‐
ers"; sentence "Empty user layers must be deleted" added
–
Section 3.5.1: spelling of "GTOL Datums" changed to "Gtol datums"
–
Section *******: Spelling of "ModPar" changed to "Modpar"; 3rd and 5th sentence and tables 2
to 4 moved to Appendix A "Parameters"; SEMI_FINISH
–
Section *******: Last sentence added
–
Section *******: Text "Insert-Annotations-Notes-New" deleted
–
Section ******* "Tolerancing principle as per VW 01054" changed
–
Section *******: Use of DoLittle changed from optional to mandatory
–
Section *******: Title "Patterns" changed to "Muster" in the German version of the document;
text "or geometry pattern" added
–
Section 3.5.2: Text "For simple components" changed to "For simple parts (components)"
–
Section *******: 3rd paragraph, text "Insert – Shared Data – Copy Geometry..." replaced with
"Model; Copy Geometry"
–
Section *******: Abbreviation "(ZKG)" added
–
Section 5: 1st sentence, text "Creo Elements/Pro format" replaced with "PTC Creo Parametric
format"; 2nd sentence, text "Creo Elements/Pro parameters" replaced with "Creo parameters";
6th paragraph, text "Annotate; Edit;" added; terms "Tools −> Overlay" replaced with "Layout
−> Overlay" and term "Utilities" replaced with "Tools", text "ModelCHECK" changed to "VW
ModelCheck"; spelling of file type ".ASM" changed to ".asm"; paragraph with the text "The data
must be checked during the design process using the verification programs recommended for
the entire Volkswagen Group..." deleted and inserted in Section 5.1; text "Copy‐Geom-inde‐
pendent changed to "Copy geometry-independent"; last two paragraphs and Figure 8 added
–
Section 5.1 "Using VW ModelCheck" added
–
Section 5.2 "Impermissible messages during regeneration" added
–
Section 6: Term (BEMI) consistently added in titles
–
Section 6.4.1: Text "Volkswagen AG GRI template" changed to "Volkswagen AG production
equipment GRI template"
–
Section 6.4.2 "Dimensional tolerances (BEMI)" changed
–
Section 6.4.3 "Workpiece edges (BEMI)" added
–
Section 6.4.4 "Form and positional tolerances (BEMI)" changed
–
Section 7 "Applicable documents" updated
–
Appendix A "Parameters" added; Table A.1 sorted alphabetically for "Parameter name"; pa‐
rameter no. 14 "VW_NAME_ADDITION" and no. 27 "VW_TITLE_ADDITION" added; parame‐
ter no. 20 "RESP_DEVELOP" new brands (MN, SC, DU) added; previous rows no. 38
"VW_SEMI_FINISH_PD_ID", no. 39 "VW_SURF_PROTECTION_ID", and no. 75
"WEIGHT_CALC" and pertaining footnote e) deleted; row no. 77 "VW_USE_RESTRICTION"
added; 2 sentences permissible values for parameters "NAME_PREFIX" and "TITLE_PREFIX"
above Table A.2 added; spelling "SEMI_FINISH" changed to "SEMI FINISH"


### 第 3 页
Page 3
VW 01059-5: 2015-04
–
Old appendix B deleted
–
Appendix B; References to tables B.1 to B.4 added; Table B.1 "Standard screw thread as per
VW 11610": rows with M7 × 1.25 and M16 deleted; core hole diameter for M8 changed from
6.70 mm to 6.75 mm; Table B.2 "Fine-pitch thread as per VW 11610": rows with M4 × 0.50,
M5 × 0.50, M9× 0.75 deleted; core hole diameter for M6 changed from 5.20 mm to 5.25 mm,
M7 changed from 6.20 mm to 6.25 mm, M8 changed from 7.20 mm to 7.25 mm, M10 from
9.20 mm to 9.25 mm, and M10 × 1.25 from 8.80 mm to 8.75 mm; Table B.3 "Interference
thread as per VW 11516": Row with M9 × 0.75 deleted, core hole diameter for M12 × 1.50
changed from 8.20 mm to 10.25 mm
Contents
Page
Scope ......................................................................................................................... 4
Definitions .................................................................................................................. 4
Requirements ............................................................................................................. 6
System requirements ................................................................................................. 6
Specifications ............................................................................................................. 7
Conventions ............................................................................................................... 8
Coordinate systems ................................................................................................... 8
Naming conventions for features and datums ............................................................ 9
Naming conventions for file names of part, assembly, and drawing .......................... 9
Naming conventions for family tables ...................................................................... 11
Naming conventions for simplified representation ................................................... 11
Conventions for layers ............................................................................................. 12
Model structure ........................................................................................................ 12
Skeleton model ........................................................................................................ 12
Use of family tables .................................................................................................. 12
Model structure of DMU parts .................................................................................. 13
Using layers ............................................................................................................. 13
Model construction ................................................................................................... 14
Basics ....................................................................................................................... 14
Parameters ............................................................................................................... 14
References ............................................................................................................... 14
Features ................................................................................................................... 14
Dimensional tolerances, tolerances of form, and positional tolerances ................... 15
Tolerancing principle as per VW 01054 ................................................................... 15
Part information ........................................................................................................ 15
Patterns .................................................................................................................... 15
Variable rounds ........................................................................................................ 15
Modeling method ...................................................................................................... 16
Skeleton for complex parts ....................................................................................... 16
Cores, bodies, ribs ................................................................................................... 17
Blank, finished part, function part ............................................................................. 17
Documentation ......................................................................................................... 18
Data quality .............................................................................................................. 18
Using VW ModelCheck ............................................................................................ 19
Impermissible messages during regeneration ......................................................... 20
Rules for production equipment design (BEMI) ....................................................... 20
Scope (BEMI) ........................................................................................................... 20
Specification (BEMI) ................................................................................................. 20
Requirements (BEMI) ............................................................................................... 20
1
2
3
3.1
3.2
3.3
3.3.1
3.3.2
3.3.3
3.3.4
3.3.5
3.3.6
3.4
3.4.1
3.4.2
3.4.3
3.4.4
3.5
3.5.1
*******
*******
*******
*******
*******
*******
*******
*******
3.5.2
*******
*******
*******
4
5
5.1
5.2
6
6.1
6.2
6.3


### 第 4 页
Page 4
VW 01059-5: 2015-04
Naming conventions for part, assembly, and drawing (BEMI) ................................. 20
Model setup (BEMI) ................................................................................................. 22
Basics (BEMI) .......................................................................................................... 22
Dimensional tolerances (BEMI) ................................................................................ 22
Workpiece edges (BEMI) ......................................................................................... 22
Form and positional tolerances (BEMI) .................................................................... 22
Use of family tables (BEMI) ...................................................................................... 23
Processing information (BEMI) ................................................................................ 23
Applicable documents .............................................................................................. 23
Parameters ............................................................................................................... 24
.................................................................................................................................. 28
6.3.1
6.4
6.4.1
6.4.2
6.4.3
6.4.4
6.4.5
6.4.6
7
Appendix A
Appendix B
Scope
Volkswagen standard VW 01059-1 applies to both in-house and external cooperation of CAD/CAM
partners along the process chains. Building on that, this part 5 of the standard describes further
specifications for designing vehicle parts with the CAD system PTC Creo Parametric (abbreviated
as Creo in this document). It is part of each contract in which tasks concerning the manufacture or
design of vehicles parts are awarded to development partners.
See section 6 for special regulations regarding the design of production equipment.
Deviations from this standard are permitted in exceptional cases, but must be agreed upon in writ‐
ing by the development partners and must not have any negative effect on archiving, conversion,
or the Creo process chain. To efficiently use models in the process chain (numerical simulation,
manufacturing, etc.), the part-dependent specification must be created in the Performance Specifi‐
cation and the coordination with the process chain partners must be established by the design en‐
gineer awarding the order prior to the start of the design process.
Definitions
Creo-specific terms appear in italics in this standard.
In addition, commands and functions are placed in inverted commas.
Legacy data
All data created prior to the introduction of Pro/ENGINEER Wildfire 2.0 (i.e.,
before 2005-07-18).
 
Application
Auxiliary program based on an API for Creo.
 
Production
equipment/BEMI
The term production equipment (BEMI) comprises facilities, devices, equip‐
ment, tools, fixtures, and measuring and test equipment used for production
purposes, including wear parts of machines and fixtures.
 
BIN part
A special skeleton for complex castings. It is used predominantly for de‐
signing cylinder heads, transmission cases, and cylinder blocks and crank‐
cases. Working with BIN parts is very similar to working with skeleton mod‐
els and is described here in brief for informational purposes. Both BIN parts
and skeleton models are parts that contain planes, points, axes, and coor‐
dinate systems. The difference between the two is that BIN parts also in‐
clude various topological information and even more complex surfaces.
This is to ensure that the combination of multiple cores, slides, or tool surfa‐
ces produces a correct casting. As regards the interface geometry on these
1  
2  


### 第 5 页
Page 5
VW 01059-5: 2015-04
parts, here as well, the skeleton model serves as the link between the BIN
part and the other parts. It is used in all kinds of different situations, but it
primarily defines interfaces and installation conditions between individual
parts of an assembly.
 
Creo
Abbreviated name for PTC Creo Parametric
 
Digital mock-up part
(DMU part)
Proprietary Creo part with reduced data volume for further use in the DMU
process. The data volume is reduced by creating a solid or an envelope ge‐
ometry that consists primarily of copies of outer surfaces of the part which
determine the packaging space (for example, the descriptive geometry of
cores located inside the part is disregarded in this case). The DMU part ref‐
erences the published geometry of the basic part (e.g., finished part or as‐
sembly). A DMU part must be prepared for each DMU-relevant part or as‐
sembly. These generally include all parts defining packaging space.
 
DoLittle
DoLittle is an application that is integrated into the CAD environment and
that allows the EKDD/5 translation team to create bilingual drawing notes
online.
 
ECA
The ECA (Powertrain Engineering Center) is the PDM (product data man‐
agement) system of the Powertrain department.
The goal of the ECA is to consistently support the powertrain development
from the early phase to the production model phase.
The ECA is used to produce and manage both CAD structures and product
structures. Product structures may be used in the ECA for the processing of
batches (creation, ordering, and tracking of engine bills of materials), and
CAD structures are the geometric representation of a powertrain.
 
Individual part
The physical part as per the bill of materials (not the CAD model).
 
Vehicle part
A part installed in the vehicle, as opposed to production equipment.
 
Finished part
The finished part model is the blank model that has been supplemented
with machining.
 
FuBIT
An application by means of which holes and surfaces can be created and
dimensioned on complex geometries, such as for powertrain parts, in a
manner suitable for manufacturing.
 
Full Check
A check by means of VW ModelCheck with regeneration of the 3-D models
and 2-D drawings.
 
Function part
The function part model is an unfilleted blank with all draft angles and part‐
ing lines.
 
Group Reference In‐
stallation (GRI)
Uniform Creo installation in the Volkswagen Group.
 
KSE
Engineering master data recordation
 


### 第 6 页
Page 6
VW 01059-5: 2015-04
Complex part/sub-
part
Part that is logically described by several sub-parts which are created by
using associative copies of datums and geometries. For engines, for exam‐
ple, these are usually parts such as cylinder head, crankcase, oil pan upper
part, intake manifold. In Creo, this is also referred to as the skeleton meth‐
od or BIN part method.
 
KVS
HyperKVS, engineering data management system
 
Modpar
An application that allows for database-supported editing and maintenance
of CAD master data as well as for maintenance of the revision table.
 
OLE object
An object from another application (e.g., WORD document, EXCEL spread‐
sheet).
 
Part
A part that is created using Creo.
 
PDA
Product data type
 
Quick Check
A check by means of VW ModelCheck without regeneration of the 3-D
models and 2-D drawings
 
Blank
Describes the physical blank (unmachined; see also "Finished part"). The
blank is the basic part whose geometry is referenced by the finished part
(using an associative copy of the geometry).
 
Simplified representa‐
tion
Simplified representation
 
Skeleton, skeleton
model
Reference part, skeleton, framework, assembly skeleton, motion skeleton,
BIN part
 
Part
A part (individual part) is an object which, from the user’s point of view,
does not have to be broken down further, or which cannot be dismantled in
a non-destructive manner.
 
Template
Initial model
 
Tool
Software tool
 
VW ModelCheck
An application for checking the quality of Creo data. The summary report
generated by this tool can be printed, saved as a PDF file, and may be
used as a handover report.
ZP7
Checkpoint seven "End of vehicle assembly"
Requirements
System requirements
For development partners, information on the version of Creo currently used in the Volkswagen
Group and on additional applications can be found on the supplier platform http://www.vwgrou‐
psupply.com, heading: "Information" >> "Divisions" >> "Research and Development" >> "R&D
Services", menu item "Creo".
3  
3.1  


### 第 7 页
Page 7
VW 01059-5: 2015-04
Specifications
Development partners must obtain the current GRI templates, configuration files, and required ap‐
plications from the supplier platform http://www.vwgroupsupply.com.
All data must always be created with an absolute accuracy of 0.010 mm. New designs that are
based on legacy data with relative accuracy must be converted to absolute accuracy.
A Volkswagen AG GRI template must always be used as the initial model for a Creo design. Draw‐
ings must be based on a Volkswagen AG GRI template.
The Modpar application must be used to fill out the model parameters (master data) and to cre‐
ate/maintain the revision table (see section *******).
The following Group configuration files must be used:
–
config.sup
–
config.pro
–
din.dtl (up to release GRI 09.00.02)
–
vw_din.dtl (from release GRI 09.01.00)
–
"vw_font" font as per Volkswagen AG specifications.
All designations (for files, simplified representations, parameters, layer names, view names, fea‐
ture names, relations, etc.) must contain only letters, numbers, hyphens, and underscores [A to Z,
0 to 9, -, _ ]. Spaces, umlauts, and special characters are not permissible. Underscores must be
used for spaces, and hyphens must be used for periods. Deviating from the specifications above,
section 3.3.3 applies to part numbers (VW 01098) in the file name.
In German, the letter "ß" must be replaced by "ss" (exception: "Masze" (dimensions), in order to
avoid confusion with "Masse" (weight)).
When creating drawings, the parametric Volkswagen AG drawing frames as per VW 01014 as well
as the tables and symbols provided must be used. An overview drawing is available for the tables
and symbols (surface symbols), which can be accessed via"GRI-MKs; Symbols Overview" or "GRI-
MKs; Tables Overview".
Bilingual CAD modles and drawings in German and English must be created using the DoLittle
application. DoLittle is a tool facilitating the creation of multilingual CAD designs using a standar‐
dized text catalog specific to Volkswagen. The text elements created using DoLittle must only be
changed via the Technical Translations Department, EKDD/5. Modifications of the text elements
without using DoLittle are not permissible.
It is impermissible to embed OLE objects in drawings or models.
Parts and assemblies must not be saved with a cross section in the representation.
The Group language for Creo is English. To ensure the trouble-free use of the GRI configuration,
the English language environment must be used exclusively.
The use of Flexible Modeling is not permitted.
VW ModelCheck checks for compliance with the requirements of this standard. Without an "OK"
data quality check with VW ModelCheck and a pertinent test profile, persistent storage in certain
PDAs is not possible in KVS.
3.2  


### 第 8 页
Page 8
VW 01059-5: 2015-04
Conventions
Coordinate systems
Only right-hand coordinate systems (see figure 5) are used. Design is always performed on the ba‐
sis of one of the following coordinate systems:
–
Vehicle coordinate system (body coordinate system, KSK) as per VW 01052
–
Engine coordinate system (KSM)
–
Transmission coordinate system (KSG)
–
Front axle differential coordinate system (KSV)
–
Transfer case coordinate system (KST)
–
Rear axle differential coordinate system (KSH)
The design of non-type-specific parts or parts that are installed more than once is based on a local
coordinate system (see figure 1, figure 2, figure 3, and figure 4).
Figure 1 – Position of engine and transmission coordinate systems and origin of positioning for the
engine-transmission unit
Figure 2 – Example: transverse
installation
Figure 3 – Vehicle
coordinate system
Figure 4 – Example: longitudinal
installation
The origin of the engine coordinate system (KSM), see figure 1, figure 2, and figure 4, is located at
the intersection point of the main axis of the crankshaft and the engine flange plane of the cylinder
block and crankcase. The X axis is on the main crankshaft axis and points in the direction of the
engine. In the non-installed condition, the Z axis is perpendicular to the horizontal plane and corre‐
sponds to the main cylinder axis (in V engines, it is the angle bisector of the main cylinder axes).
3.3  
3.3.1  


### 第 9 页
Page 9
VW 01059-5: 2015-04
The origin of the transmission coordinate system (KSG), see figure 1, figure 2, and figure 4, is loca‐
ted at the intersection point of the transmission input shaft axis and the transmission flange plane.
The X axis is on the transmission input shaft axis and points in the direction of the transmission. In
the non-installed condition, the Z axis is perpendicular to the horizontal plane of the transmission.
If the engine-transmission unit is positioned with respect to the vehicle, the transmission coordinate
system (KSG) must be used (even if there is an intermediate plate between the engine and the
transmission).
The origin of the front axle differential coordinate system (KSV) is located at the intersection point
of the vertical plane running through the input shaft axis and the differential axis. The positive X ax‐
is points in the direction of travel, the Y axis points to the left (with respect to the direction of travel),
and the Z axis points upwards.
The origin of the transfer case coordinate system (KST) is located on the X axis of the transmission
coordinate system, displaced by the value "F." The orientation of the axes corresponds to that of
the transmission coordinate system.
The origin of the rear axle differential coordinate system (KSH) is located at the intersection point
of the vertical plane running through the input shaft axis and the differential axis. The positive X ax‐
is points opposite the direction of travel, the Y axis points to the right (with respect to the direction
of travel), and the Z axis points upwards.
These coordinate systems must not be created with any external references.
In parts and assemblies, these coordinate systems must be given meaningful names (e.g.,
CS_KSK, CS_KSM, CS_KSG, CS_KSV, CS_KST, CS_KSH) allowing for logical association.
In particular Volkswagen AG Creo applications that process coordinate systems in programs, spe‐
cific predefined coordinate system names may be used.
Figure 5 – Orientation of the coordinate systems (right-hand rule)
Naming conventions for features and datums
If functionally relevant, features and datums must be assigned meaningful English names. In skele‐
ton parts/assemblies, all features and/or datums resulting from the features of the template must
be renamed.
To avoid data check problems, the names of reference planes that are exclusively used for the
generation of cross sections must include the prefix XSEC_.
Naming conventions for file names of part, assembly, and drawing
The file name must not contain a date.
3.3.2  
3.3.3  


### 第 10 页
Page 10
VW 01059-5: 2015-04
The model, whose file name corresponds to the part number, must correspond to the later final
condition, i.e., as installed in the vehicle (for units, ZP7). If the representation on the drawing devi‐
ates from this (e.g., part represented stress-relieved, different bill of materials/design due to modifi‐
cation parts/circulating parts, installation examples, design modification due to ASSY machining),
the drawing model must be marked with
–
"drw"
in the file name.
Drawings must be generated from construction assemblies
–
const.asm
(e.g., for more complex parts such as a cylinder heads or cylinder blocks and crankcases).
If the final design of parts machined in the ASSY is not represented by assembly features but by
separate models, these are assigned the ending
–
pma.prt
The file name of multiple drawings only contains the part number of the leading part (upper part
number in the title block). To ensure clear identification, Creo objects must be named as per the
naming convention specified in table 1. Names in Creo are limited to 31 characters.
Only lower-case letters must be used.
VW 01058 and VW 01098 must be observed when naming parts, assemblies, and drawings.
The following general specifications apply to the notation:
–
A hyphen [-] must be used as both grouping mark and separator.
–
An underscore [_] must be used to indicate spaces.
Table 1 – Naming convention for design and development
Object
Draft
Preliminary status/release status
CAD model (prt, asm)
Part
Part as assembly
ASSY
Standard/multi-use part drawing (drw)
Designation_company_usr_project
xxx_yyy_zzz_qqa)-typeb)
Parts without own part number
Designation_company_usr_project
xxx_yyy_zzz_qqa)-ca)-suppla)-typeb)
Purchase parts
Purchase part number
Designation
Drawing symbol
Designation
Designation
Drawing sticker (text macro)
Designation
Designation
Drawing format
Designation
Designation
Mark up
Identical to model name
Identical to model name
Layout
Identical to model name
Identical to model name
User defined feature
Designation
Designation
Table of materials
Will be specified if required
 
a)
Optional name component. If the optional name components are omitted, the associated separators and spaces are also omitted.
b)
The types specified in the legend must be entered. If a part cannot be assigned to one of the types specified in the legend, this
name component is omitted.


### 第 11 页
Page 11
VW 01059-5: 2015-04
Legend for table 1:
Permissible characters in square brackets [ ]
Designation
Part designation: [a – z], [0 – 9], [-_]
c
component: used to indicate a component (component or assembly) that
does not have its own part number (e.g., intake manifold duct).
suppl
Supplement to describe subsets, partial models, or assembly objects: [a –
 z], [0 – 9], [-_]; a hyphen placed before the supplement as a separator is
mandatory.
company
As per VW 01099, e.g., au, vw, sk, se, po, la
project
In-house project designation, e.g., ab, d3, v65v, 5hp24.
qq
Index: [a – z], [0 – 9]
type
DMU part
dmu (digital mock-up)
Skeleton part
skel (skeleton)
Blank
rp (raw part)
Finished part
fp (finished part)
Function part
fcp (function part)
Semi-finished part
sfp (semi-finished part)
Part machined in ASSY
pma (part machined in assembly)
Design assembly
const (construction)
Developed sheets
flat# (with # indicating a number)
Flat pattern
flat
Drawing models deviating from the
final condition
drw (drawing)
Generic parts
g (generic model)
usr
User code
xxx
Vehicle class code, ent, tab, wsk, taw, n__ (for standard parts, "n" and 2 un‐
derscores): [a – z], [0 – 9]
yyy
Middle group, standard part serial number: [0 – 9]
zzz
End number, standard part serial number: [0 – 9]
Examples for table 1:
part (draft)
halter-kliko_au_tnebrr_v65v.prt
part (preliminary/release status)
057_103_477_cq.prt
Part as assembly
059_210_499_af-const.asm
Components without part number
059_210_499_af-c-kanal02.prt
DMU part
059_210_555_xy-dmu.prt
Naming conventions for family tables
If a family table is used, the variants (instances) are named the same way as ordinary models. The
generic model contains "g" as the type in the file name.
Naming conventions for simplified representation
If a simplified representation is used, meaningful names must be used.
3.3.4  
3.3.5  


### 第 12 页
Page 12
VW 01059-5: 2015-04
Examples:
–
RAWPART
–
TOOLEDPART
–
CALCULATION
–
INSPECTION
–
DMU
Conventions for layers
Template layers
Template layers are layers that are stored in the Creo initial model of the Volkswagen Group. The
template layers in the part, assembly, or drawing must never be deleted. The names of the tem‐
plate layers always begin with a "_" (underscore).
User layers
User layers are layers that are created by the user in order to assign elements. They are given
meaningful names but must not contain complete part numbers and designations with a date. The
names of user layers must not begin with a "_" (underscore) and must not contain any special
characters.
If, in an assembly, a component is placed on a layer, the name of this layer must begin with
"COMP_", e.g., "COMP_SKEL".
When exporting to neutral interfaces (IGES, STEP, etc.), layer IDs can be assigned in addition to
the layer names.
Model structure
Skeleton model
Large assemblies have a skeleton model, which is incorporated into the assembly as the first com‐
ponent. This component is then referenced by all subsequently incorporated components.
All skeleton elements must be assigned meaningful names (see section 3.3.2).
Skeletons must be assigned the ending
–
-skel.prt or
–
-bin.prt.
A differentiation is made between skeleton models and BIN parts. BIN parts are used predominant‐
ly for complex castings while skeleton models are used for assemblies, see section 2.
Use of family tables
The use of "*" entries in variants (instances) of the family table is not permissible. Depending on
the column type, pertinent values must be entered.
All instances of a family table must be verified.
3.3.6  
3.4  
3.4.1  
3.4.2  


### 第 13 页
Page 13
VW 01059-5: 2015-04
Model structure of DMU parts
DMU parts must be created for all DMU-relevant parts/assemblies. Parts/assemblies are relevant
to DMU if the envelope of the part determines the packaging space.
Examples:
Cylinder head cover
Relevant to DMU
Camshaft
Not relevant to DMU
All DMU parts must refer to a coordinate system that is specific to the engine, transmission, or ve‐
hicle and designated with respect to the project.
For assemblies relevant to DMU, the simplified representation DMU must be defined. It contains all
DMU-relevant parts. All other parts are excluded from this simplified representation.
Using layers
A logical layer structure must be created for complex parts.
In addition, a layer structure must set up according to features that belong together functionally or
in regard to manufacturing (e.g., LAGER_KURBELWELLE layer for all features that belong to the
corresponding bearing; core hole bores on CORE_HOLE_BORE layer).
Prior to a data transfer, all layers that are not relevant to the geometry must be hidden for all parts
and assemblies. This condition must be explicitly saved (save using "save status" and also save
the part or assembly).
Only the display options "hide" and "unhide" are permitted to be used. None of the layers must
have the status isolated. This can be recognized by a dash-dot frame in the layer tree (see
figure 6).
Figure 6 – Symbol for isolated layer (do not use)
Features with axes must lie at least on one template layer or user layer.
Empty user layers must be deleted.
Example:
For a cylinder block and crankcase for which the processing sides are divided into sides 1
through 6, layers named SIDE_1 through SIDE_6 are created. If SIDE_3 is to be processed, all
3.4.3  
3.4.4  


### 第 14 页
Page 14
VW 01059-5: 2015-04
side layers except SIDE_3 are hidden. Thus, only the features relevant to side 3 are visible in the
view of side 3.
Model construction
Basics
All CAD models and drawings must be created on the basis of a Volkswagen AG GRI template.
The first three features in the model are standard planes. The fourth feature is the standard coordi‐
nate system "CS_DRW". These four features must not be deleted and their names and sequence
must not be changed. The three standard planes must not be converted into datum planes ((Gtol
datums) and must not be used to create cross sections (Xsections).
For economic manufacturing, the metric ISO threads as specified in appendix B must be used.
Parameters
In order to effectively support development and manufacturing processes, the parameters of the
template must be assigned values to the extent necessary (see appendix A). The Modpar applica‐
tion assists the user with this.
The processes that must be supported include:
–
Preparing drawings
–
KSE
References
Datum planes/axes/points/curves or surfaces must be used as references if possible. The refer‐
enced reference elements must be located as early as possible in the origin history of the part or
assembly; for example, standard planes, the first defined axis, etc.
If a modeling strategy is specified with a separate skeleton part for more than one vehicle part,
then the information available in the skeleton part must be transferred to the sub-parts describing
the total part and referenced during modeling. Information available in the skeleton part must not
be recreated independently of the skeleton part in a sub-part.
All models must be submitted to the purchaser with all reference files required for regeneration.
Geometric external references must be mapped using the "data sharing feature."
References to import features are not permissible.
Features
Geometries, such as rounds, holes, chamfers, must not be modeled using a sketch unless this ge‐
ometry cannot be created using an existing round, hole, chamfer features, etc. Exceptions must be
documented using 3D notes (DoLittle). Exceptions are permissible for specific Creo applications
that create and recognize corresponding features and allow their manufacture.
Cuts must not be completely filled with protrusions. It is not permissible for a protrusion to be loca‐
ted completely within another protrusion.
3.5  
3.5.1  
*******  
*******  
*******  


### 第 15 页
Page 15
VW 01059-5: 2015-04
Dimensional tolerances, tolerances of form, and positional tolerances
Dimensional tolerances, tolerances of form, and positional tolerances must always be generated in
the part or assembly in order to be used in the subsequent processes. Generation in the drawing
without reference to the model is not permissible.
Tolerancing principle as per VW 01054
The application of the tolerancing principles for drawings of the Volkswagen Group is specified in
VW 01054 .
Part information
Additional part information must be generated in the 3-D model as notes. They must be referenced
in the drawing. This must be done using DoLittle.
Patterns
Complex geometries must be patterned via surface transform or geometry patterns.
Variable rounds
Variable rounds must not contain the radius value "zero."
*******  
*******  
*******  
*******  
*******  


### 第 16 页
Page 16
VW 01059-5: 2015-04
Modeling method
Parts (components) are generally modeled in the following sequence:
–
Control geometry (datums, points, axes, curves, surfaces)
–
Basic geometry (constructional features)
–
Details (features such as chamfers, rounds, cosmetics).
The basic strategy always follows the general rule:
Skeleton => Function part/blank => Finished part => Poss. DMU part.
For simple parts (components), a corresponding order (skeleton => function part/blank => finished
part) must be implemented by selecting suitable features within a single part. Corresponding layers
must be created (skeleton => SKEL, function part => FCP, blank => RP, and finished part => FP).
In addition, there must be layers for the blank elements (drafts and rounds). It must be possible to
generate the blank by hiding the processing elements of the finished part layer. Likewise, sup‐
pressing the rounding layer results in a function part. It is recommended to give the features mean‐
ingful names.
In the case of complex parts, the modeling scope must be divided into function part/blank/finished
part using associative surface copies in a construction assembly (see dashed line in figure 7).
In the case of highly complex parts (e.g., transmission case, cylinder head), the construction as‐
sembly additionally contains functional units, such as raised_screw_boss_component or rib_com‐
ponent_top. For clarification of the flow of references for complex parts, refer to figure 7:
Figure 7 – Reference flow for complex parts
Skeleton for complex parts
All skeleton elements must be given meaningful names or names relevant to manufacturing for
specific Creo applications (see section 3.3.2).
All basic design details are specified in the skeleton part in the form of reference elements and sur‐
faces. The functional dependencies between the draft variables, blank geometry, and machining
are created here.
Each piece of geometric information must only be defined once and is binding for the entire design
scope. As soon as a piece of information is used by more than one part or assembly, it must be
defined within the associated skeleton. If a piece of information is required multiple times, the cor‐
responding and already existing binding element must be referenced using an associative copy of
the information in the skeleton.
Blank references for castings must be provided (e.g., "engine center"). When using the blank/fin‐
ished part system, the same axis can be referenced for the sake of simplicity to create both blanks
3.5.2  
*******  


### 第 17 页
Page 17
VW 01059-5: 2015-04
(raised screw bosses) and finished parts. Milling can be incorporated into the skeleton part as a
surface. These surfaces can later be used for creating cross sections in the finished part via "Mod‐
el; Copy Geometry..." (this does not apply to holes). The machining surface, the axis for subse‐
quent machining, and the machined contour as a curve (circle) must all be available to describe a
raised screw boss in the skeleton.
In addition to the classical part skeleton described above, there are also assembly skeletons. They
contain functional installation coordinate systems for the parts that are combined in the assembly.
An assembly skeleton for a camshaft drive, for example, may also include the definition of the mo‐
tion sequence, such that various motion conditions are reproduced when a central dimension in the
assembly skeleton is changed. If this is the case, a 3-D note on the assembly skeleton must indi‐
cate the feature that controls the motion.
Example: /* reference plane PL_cam_angle, ID 06087 determines the angle of rotation for the as‐
sembly skeleton.
In assemblies, the finished part is used and not the construction assembly used to create the fin‐
ished part. If this is not observed, it is not possible to perform a correct weight analysis, because,
e.g., one part is represented by both a blank and finished part, both with different weights.
Cores, bodies, ribs
Relevant geometries are modeled in sub-parts, depending on the specific part. Cores such as the
water jacket core, oil core, etc., are found in complex parts such as cylinder heads or cylinder
blocks and crankcases and form natural cores that are modeled positively as volumes. Complex
ribbing can be created in a separate part. A body is understood to be any part that serves primarily
to describe the outer geometry (in the case of a cylinder block and crankcase comprising, e.g., cyl‐
inder_bank_surface, flange_front, flange_rear, etc.).
Blank, finished part, function part
The blank model is generated from the associated sub-parts (cores, bodies, and ribs). The geome‐
tries (raised screw bosses, drafts, fillets) missing in the sub-parts are supplemented.
The finished part model is derived from the blank model as a surface copy ("CopyGeom"). The sol‐
id generated from said surface copy is provided with cuts and holes used to represent machining. If
required, a function part, which serves as the basis for numerical strength simulations, etc., may be
created before the blank.
*******  
*******  


### 第 18 页
Page 18
VW 01059-5: 2015-04
Documentation
For highly complex parts such as cylinder heads, a drawing with the name "<object name>-
doc.drw" must be created that describes the construction assembly structure. Critical points in the
model (e.g., unavoidable "Geom Checks") must be marked with 3D notes (use DoLittle).
Data quality
The 3-D model and the drawing must be created in the parametric/associative PTC Creo Paramet‐
ric format. All Creo parameters and features required to change or control the model must be
present.
The 3-D model must be designed as a volume model in complete detail. Deviations, such as the
delivery of surface models, must be agreed upon with the purchaser in writing prior to delivery of
the model.
The 2-D drawing must be completely derived from the 3-D model. Views must not be sketched.
Drawings with imported 2-D geometry without a 3-D model are excluded from this. In this case, the
parameters are stored in the drawing and not in the model. This deviation from the standard must
be agreed upon in advance.
The generation of 2-D geometries in drawings must be avoided. They must be generated in the 3-
D model using cosmetics and/or curves.
When plotting drawings, it must be ensured that the drawing references the latest version of the
model. The drawing must not contain any unnecessary ancillary geometries (planes, etc.) in plotta‐
ble condition.
The Annotate; Edit; Convert to Draft Entities function is permissible only for illustration purposes.
Non-parametric dimensions must only be used when absolutely necessary. The dimensions must
be associative to the model in all cases.
Drawings must not be overlaid (Layout; Overlay).
2-D elements must not be located outside the drawing frame.
Views that are no longer required must be deleted using delete.
Suppressed (erased) views are not permissible.
Drawings of an assembly's simplified representations must also include the master representation
as a drawing model, even if it is not represented in any view (archiving!). The warning message
issued by VW ModelCheck must be tolerated.
Foreign formats (IGES, VDAFS, STEP) imported into Creo must only be used for installation tests
and adjacent parts.
It is possible for system suppliers to supply non-parametric models upon agreement (e.g., in order
to protect knowledge). These models must not have any external references to undelivered data
sets.
Models must be completely regenerative. This must be checked using Tools; Model Player. Incom‐
plete features are impermissible.
Parts must be stored with suppressed features in exceptional cases only.
Assemblies must not be stored with suppressed components. Exceptional cases include, for exam‐
ple, part families or design-related strategies.
An assembly (file type: .asm) must not be assigned a density.
4  
5  


### 第 19 页
Page 19
VW 01059-5: 2015-04
The use of interchange assemblies is not permitted since external references may be generated
thereby.
3-D models containing a shrinkwrap geometry must neither be used as data for a productive scope
nor be assigned the KVS status "V" (binding). If know-how is to be protected, other Creo features
must be used (e.g., "Copy Geometry-independent"). Shrinkwraps must only be used for informa‐
tional purposes and/or for DMU.
It must be possible to compute the following part properties from a 3-D model in the CAD system
(e.g., to analyze the part dynamics in the vehicle):
–
Part mass in [g]
–
Center of mass as the distance in the X, Y, and Z directions from the part reference system in
[mm]
–
Inertia moment as tensor around all inertia axes in [g*mm²]
If a non-parametric model is delivered (e.g., to protect knowledge), it must be ensured that the
CAD model is accompanied by the required part properties as parameters and the geometric posi‐
tion of the reference system (see figure 8).
Figure 8 – Weight calculation
Using VW ModelCheck
The data must be tested during design engineering using the test programs specified Group-wide
for Volkswagen AG (for Creo models: VW ModelCheck). The inspection report must be attached
with the data delivery. To ensure that the data can be regenerated, a check must be performed
using VW ModelCheck – Full Check. All faults marked with "Error" during testing and the "warn‐
ings" mentioned in section 5.2 must be eliminated. Furthermore, the number of "warnings" must be
kept to a minimum. New designs based on legacy data must also be OK when verified using
VW Model-Check.
5.1  


### 第 20 页
Page 20
VW 01059-5: 2015-04
 
Note:
With the Quick Check , similar to the "ModelCHECK Interactive" mode, the scope (All drawing
models/All levels) is checked for the formal criteria without regeneration. Especially for large as‐
semblies, this should save time, because errors in formal criteria can be quickly found and re‐
paired. This method is recommended for quick intermediate checks during the design phase.
With the Full Check, the user can apply VW ModelCheck completely including regeneration to a
drawing, assembly, or single part. The check routine corresponds to the old "ModelCHECK for
KVS" function (ModelCHECK regenerate/All drawing models/All levels). This mode is also used in
KVS and therefore is performed as preparation for KVS storage.
In contrast to PTC ModelCHECK, VW ModelCheck allows the data to be repaired.
Impermissible messages during regeneration
The following warning messages are impermissible and must be rectified:
–
Original reference not found, using alternate.
–
WARNING: CUT is entirely outside the model; model unchanged.
Exception: verification processing
–
WARNING: PROTRUSION is entirely inside the model; model unchanged.
–
"... self-intersecting geometry"
–
"Warning: Design intent is unclear ..."
Note: The error analysis can be performed with Geometry Check. If the warnings of the Geom‐
etry Checks absolutely cannot be avoided or repaired, they must be documented in detail in
the form of a bilingual 3-D note (use DoLittle).
Rules for production equipment design (BEMI)
Scope (BEMI)
The specifications in this section apply exclusively to the design of production equipment using the
Creo CAD system. All points not listed in this section must be obtained from the general text of the
standard above.
Deviations from these specifications for the design of production equipment are permitted in excep‐
tional cases, but must be agreed upon in writing by the development partners.
Specification (BEMI)
When creating drawings, the parametric Volkswagen AG drawing frames for production equipment
and the production equipment symbols and tables provided must be used.
Requirements (BEMI)
Naming conventions for part, assembly, and drawing (BEMI)
In order to ensure clear identification, production equipment Creo objects must be named as per
the naming convention as set forth in table 2 or table 3.
5.2  
6  
6.1  
6.2  
6.3  
6.3.1  


### 第 21 页
Page 21
VW 01059-5: 2015-04
Table 2 – Naming conventions for the design of production equipment
Object
Draft
Purchase part
Standard part
Release status
CAD model (prt, asm)
Part, part as assembly
ft_be_compa‐
ny_usr_projecta)
fbez_bestdat
nbez-znr_abmsg
sss_zusat_uu-vvv-
wwx_yyyyyy_zzz
CAD model (asm)
ASSY
 
 
 
000_zusat_uu-vvv-
wwx_yyyyyy_zzz
CAD drawing (drw)
 
 
 
uu-vvv-wwx_yyyyyy_zzz
Drawing symbol
Designation_companya) _bm
Drawing sticker
Designation_companya) _bm
Drawing format
Designation_companya) _bm
Mark up
Identical to model name
Layout
Identical to model name
User defined feature
Designation_bm
Table of materials
Will be specified if required
a)
Optional name component. If the optional name components are omitted, the associated separators or spaces are also omitted.
Table 3 – Naming conventions for foundry production equipment
Object
Draft
Purchase part
Standard part
Release status
CAD model/drawing
(prt, asm, drw)
ft_be_compa‐
ny_usr_projecta)
fbez_bestdat
nbez-znr_abmsg
uu-vvv-wwx_yyyyyy_zzz_ssa)-
sssa)-ssa) or optionally
yyyyyy_zusatz
Drawing symbol
Designation_companya) _bm
Drawing sticker
Designation_companya) _bm
Drawing format
Designation_companya) _bm
Mark up
Identical to model name
Layout
Identical to model name
a)
Optional name component. If the optional name components are omitted, the associated separators or spaces are also omitted.
Legend for table 2 and table 3:
ft
Manufacturing-related identifier
ax
Without identifier
af
Milled part
ad
Turned part
as
Welded part
an
Purchase part with rework
fbez
Company designation ([a – z], [0 – 9], max. 10 characters)
be
Designation: ([a – z], [0 – 9])
bestdat
Order data as per company documentation ([a – z], [0 – 9], max. 20 charac‐
ters)
nbez
Designation of the standard, e.g., DIN, EN ([a – z])
znr
Serial number of the corresponding standard, e.g., 612 ([0 – 9])


### 第 22 页
Page 22
VW 01059-5: 2015-04
abmsg
Dimension as per standard designation, e.g., M6x15x1_25 ([a – z], [0 – 9],
[_])
sss (only in table 2)
Production equipment part number with leading zeros (s: [0 – 9])
ss-sss-ss (only in ta‐
ble 3)
Foundry production equipment part number with leading zeros (s: [0 – 9]),
max. 9 characters including separators
zusat (only in table 2)
Supplement to describe subsets, partial models, or assembly objects (t: [a –
 z], [0 – 9]), 5 characters with leading zeros (e.g., DRK01, REF01)
zusatz (only in ta‐
ble 3)
Supplement to describe subsets, partial models, or assembly objects ([a –
 z], [0 – 9], [_]), max. 24 characters incl. separators (e.g., kok_fs_es_ein_06)
uu
Plant number (u: [0 – 9])
vv
Number of sub-group (v: [a – z], [0 – 9])
ww
Number of main group (w: [0 – 9])
x
Code letter for subdivision/department (x: [a – z])
yyyyyy
Serial number (y: [0 – 9]) with leading zeros
zzz
Line art (y: [0 – 9]) with leading zeros
bm
Abbreviation for "production equipment design"
Model setup (BEMI)
Basics (BEMI)
All CAD models for a production equipment design must be created on the basis of a Volks‐
wagen AG production equipment GRI template.
The following rules apply:
–
The designation of the datums available in the Volkswagen AG GRI templates can be expan‐
ded by a supplement (example: PL_XY_031 for item number 31 of the production equipment
single part).
–
The parametric Volkswagen AG production equipment drawing frames must be used to create
a drawing.
Dimensional tolerances (BEMI)
The permissible deviations of dimensions in parts that may be produced with accuracy typical of a
workshop must be entered on the drawing as per VW 01058 section "Field for permissible devia‐
tions and surfaces" in the table "General tolerances for nominal dimensions without tolerance".
Workpiece edges (BEMI)
The edge conditions without a certain geometric shape must be entered as per VW 01088 in the
field "Workpiece edges" on the drawing (see also VW 01058 section "Field for permissible devia‐
tions and surfaces").
Form and positional tolerances (BEMI)
Tolerances of form and positional tolerances must preferably be defined in the 3-D model as per
DIN EN ISO 1101. Simultaneous definition of tolerances in a 2-D drawing and in a 3-D model in
one data set is not permissible.
6.4  
6.4.1  
6.4.2  
6.4.3  
6.4.4  


### 第 23 页
Page 23
VW 01059-5: 2015-04
Use of family tables (BEMI)
It is permissible to represent variants using family tables.
Processing information (BEMI)
It is permissible to define 2-D processing information (2-D symbols) in the drawing.
Applicable documents
The following documents cited in this standard are necessary to its application.
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
VW 01014
Engineering Drawings; Drawing Frames and Text Macros
VW 01052
Engineering Drawings; Representations
VW 01054
Engineering Drawings; Dimensioning
VW 01058
Drawings; Lettering
VW 01059-1
Requirements for CAD/CAM Data; Representation of Technical Charac‐
teristics
VW 01088
Workpiece Edges; Definitions, Drawing Specifications
VW 01098
Part Number System
VW 01099
Codes for Plants, Subsidiaries/Associate Companies, and Partner Com‐
panies
VW 11535
Diameter of Drill and Thread Root Hole; VW Secure-Fit Thread
VW 11635
Minor-Diameter Holes for Threads; Drills and Limit Dimensions for Toler‐
ance Zone Position H
VW 13750
Surface Protection for Metal Parts; Surface Protection Types, Codes
DIN EN 10027-2
Designation systems for steel; numerical system
DIN EN ISO 1101
Geometrical product specifications (GPS) - Geometrical tolerancing -
Tolerances of form, orientation, location and run-out
VDA 260
Components of motor vehicles - Marking of material
6.4.5  
6.4.6  
7  


### 第 24 页
Page 24
VW 01059-5: 2015-04
Parameters
At least the parameters for vehicle parts (FE) and/or production equipment (BEMI) marked with
"YES" in the Volkswagen AG GRI template (see table A.1, second column) are designated and
must be provided with the correct values.
The status DESIGNATED (YES/NO) in table A.1, column 2, and the parameter type (TYPE) in col‐
umn 4 must be adhered to.
Table A.1 – Parameters in the GRI template
No.
DESIGNATED
Parameter name
TYPE
Explanation, designation (examples)a)
FE
BEMI
1
YES
NO
COMMENTS
String
Comments regarding development status
2
YES
NO
DEPARTMENT
String
Department ("EAOE“)
3
YES
NO
DRAWN_BY
String
Creator "John Doe/contractor"
4
YES
NO
DRW_DATEb)
String
Drawing date
("08.07.05", format: dd.mm.yy)
Date of drawing creation
5
YES
NO
ENG_PRJ_NO
String
Engineering project no. ("EA 111“)
6
YES
NO
LAYOUT_NO
String
ENT no. ("789 01")
7
YES
NO
MAT_TREATMENT
String
Material treatment
8
YES
NO
MAT_TREATMENT_2
String
Material treatment 2
9
YES
NO
MATERIAL
String
Material ("DIN EN 10130-DC01")
10
YES
NO
MATERIAL_2
String
Material 2
11
YES
NO
MATERIAL_3
String
Material 3
12
YES
NO
MOD_DATEc)
String
Change date
("12.07.05", format: dd.mm.yy)
The Modpar application must be used to fill in
the "MOD_DATE" parameter.
13
YES
NO
MODPAR_VERSION
String
Version number of last Modpar processing
("4.3")
14
YES
NO
VW_NAME_ADDITION
String
Additional designation from TEIVON German
15
YES
NO
NAME_PREFIX
String
German prefix designation ("ZSB")
(see table A.2 – Permissible prefixes)
16
YES
NO
NAMING
String
German designation ("Lager")
17
YES
NO
iiPART_NOd)
String
Part number ("123.456.789.AB")
18
YES
NO
PHONE_NO
String
Phone no. of person responsible
("+4953619123456", max. 13 digits)
19
YES
NO
PN_PREFIX
String
Part number prefix is omitted, for legacy data
only!
20
YES
NO
RESP_DEVELOP
String
Development responsibility as per VW 01099
("VW", "AU", "SK", "SE", "LA", "BU", "BY",
"PO", "MN", "SC", "DU")
21
YES
NO
RESPONSIBLE
String
Person responsible ("Jane Doe")
22
YES
NO
SAFETY_DOC
String
Safety document as per VW 01058 "TLD"
Appendix A (normative)  


### 第 25 页
Page 25
VW 01059-5: 2015-04
No.
DESIGNATED
Parameter name
TYPE
Explanation, designation (examples)a)
FE
BEMI
23
YES
NO
SEMI_FINISH_PD
String
Semi-finished product
24
YES
NO
SEMI_FINISH_PD_2
String
Semi-finished product 2
25
YES
NO
SURF_PROTECTION
String
Surface protection as per VW 13750 ("t610")
26
YES
NO
TITLE
String
English designation ("Bearing")
27
YES
NO
VW_TITLE_ADDITION
String
Additional designation from TEIVON English
28
YES
NO
TITLE_PREFIX
String
English prefix designation ("ASS")
(see table A.2 – Permissible prefixes)
29
YES
NO
TYPE_APPR_DOC
String
Type approval document
("036.906.032.AA", "ATS 200")
30
YES
NO
V_NO
String
V number ("V310", "V400")
31
YES
NO
VW_APPROVAL_REQUIRED
String
Mandatory build sample approval, no stand‐
ard value entered anymore!
32
YES
NO
VW_CAD_SYSTEM_KEY
String
CAD system and management system key
("PTC Creo Parametric")
33
NO
NO
VW_DENSITY_RELEVANT
Yes/No
Model relevant to weight calculation (for
VW ModelCheck check only); standard
value = YES; for skeletons, DMU parts, and
BIN parts = "NO"
34
YES
NO
VW_DRAWN_BY_COMPANY
String
Contractor
35
YES
NO
VW_MATERIAL_LABEL
String
Material marking as per VDA 260
36
NO
NO
VW_MATERIAL_NUMBER
String
Material number for metals as per
DIN EN 10027-2 ("1.7225")
37
NO
NO
VW_MISS_ID
String
MISS ID of the material from Modpar
38
NO
NO
VW_MODEL
Yes/No
Volkswagen model ("NO")
39
YES
NO
VW_OPENAIRWEATHER_ID
String
Open-air weathering code
40
NO
NO
VW_R_ANNEAL
String
 
41
NO
NO
VW_R_ASSOCIATED_RESOURCES
String
Associated production equipment
("11-38D 500121/122")
42
NO
NO
VW_R_BOM
Yes/No
Bill of materials ("YES")
43
NO
NO
VW_R_BOM_ITEM_NO
String
Bill of materials item number ("3")
44
NO
NO
VW_R_CONTRACTED_COMPANY
String
Company awarded the order
45
NO
NO
VW_R_HARDENING
String
Hardened
46
NO
NO
VW_R_INSERT_DEPTH
String
 
47
NO
NO
VW_R_MACHINE
String
Machine ("Maho 250")
48
NO
NO
VW_R_NAMING
String
Designation ("clamping device")
49
NO
NO
VW_R_NAMING_APPENDIX
String
Additional designation
50
NO
NO
VW_R_NUMBER_LEFT
String
Number, left
51
NO
NO
VW_R_NUMBER_RIGHT
String
Number, right
52
NO
NO
VW_R_ORDERING_DEPARTMENT
String
Department placing the order
53
NO
NO
VW_R_PART_DATE
String
Vehicle part date ("08.07.2005")
54
NO
NO
VW_R_PART_DATE_2
String
Vehicle part date 2


### 第 26 页
Page 26
VW 01059-5: 2015-04
No.
DESIGNATED
Parameter name
TYPE
Explanation, designation (examples)a)
FE
BEMI
55
NO
NO
VW_R_PART_DATE_3
String
Vehicle part date 3
56
NO
NO
VW_R_PART_DATE_4
String
Vehicle part date 4
57
NO
NO
VW_R_PART_DESIGN_RELEASE
String
Vehicle part design status 1
58
NO
NO
VW_R_PART_DESIGN_RELEASE_2
String
Vehicle part design status 2
59
NO
NO
VW_R_PART_DESIGN_RELEASE_3
String
Vehicle part design status 3
60
NO
NO
VW_R_PART_DESIGN_RELEASE_4
String
Vehicle part design status 4
61
NO
NO
VW_R_PART_NAMING
String
Vehicle part designation ("steering knuckle")
62
NO
NO
VW_R_PART_NAMING_2
String
Vehicle part designation 2
63
NO
NO
VW_R_PART_NAMING_3
String
Vehicle part designation 3
64
NO
NO
VW_R_PART_NAMING_4
String
Vehicle part designation 4
65
NO
NO
VW_R_PART_NO
String
Vehicle part number ("123.456.789.A")
66
NO
NO
VW_R_PART_NO_2
String
Vehicle part number 2
67
NO
NO
VW_R_PART_NO_3
String
Vehicle part number 3
68
NO
NO
VW_R_PART_NO_4
String
Vehicle part number 4
69
NO
NO
VW_R_PHONE_NO_DRAFTER
String
 
70
NO
NO
VW_R_STOCK_NO
String
Inventory number ("123456")
71
NO
NO
VW_R_TOP_LEVEL
Yes/No
("NO")
72
YES
NO
VW_REPR_PART
String
ASSY code from the TEIVON system ("Z",
"G", "-")
73
NO
NO
VW_RESOURCE
Yes/No
Volkswagen production equipment ("YES"),
Design Engineering ("NO")
74
YES
NO
VW_SAFETY_DOC_NO
String
Segmented TLD number
("TLD.123.456.78")
75
YES
NO
VW_SUPPLIER_PART_NO
String
Supplier part number
76
NO
NO
VW_TEIVON_ID
String
TEIVON ID from Modpar
77
YES
YES
VW_USE_RESTRICTION
String
Default "N"
For protected frame, the company name
must be entered.
a)
Examples in quotation marks are interpreted as strings.
b)
DRW_DATE: first issue date of the drawing, also first entry in the revision table of the drawing (which is created with Modpar).
c)
MOD_DATE: last change date of the model and drawing, also last entry in the revision table of the drawing (which is created with
Modpar).
d)
The PART_NO parameter (for part number) must be indicated for vehicle parts by using the three groups of the part number and the
optional index. Between the groups, a space must be used instead of a separating dot. The use of spaces is only required in the
PART_NO parameter. Section 3.3.3 applies to file names.
For the parameter NAME_PREFIX, only the values mentioned in table A.2 are permissible.
For the parameter TITLE_PREFIX, only the values mentioned in table A.2 are permissible.


### 第 27 页
Page 27
VW 01059-5: 2015-04
Table A.2 – Permissible prefixes
Symbol
German explanation
NAME_PREFIX
TITLE_PREFIX
English explanation
A
Anordnung
ANO
ARA
Arrangement
E
Einbau
ENB
INS
Installation
G
Schweißgruppe
SGR
WAS
Welded assembly
K
Klebeschilder
 
 
Adhesive labels
L
Lieferumfang
 
 
Scope of supply
S
Zusammenstellung
ZSS
COM
Combination
T
Table
TAB
TAB
Table assembly
W
Lieferabwicklung
 
 
Supply transaction
Y
Typprüfzeichnung
 
 
Type approval drawing
Z
Zusammenbau
ZSB
ASS
Assembly
The parameters in table A.3 are generated by applications and must not be changed manually.
Table A.3 – Application-specific parameters
Parameters
Application
VW_KSE_EXTRACTION
Starting with Modpar 5.0
PART_STATE
Standard part generator
PART_STATE _0
Standard part generator
PART_STATE _1
Standard part generator
PART_STATE _2
Standard part generator
PTC_WM_MODIFIED_BY
Windchill
PTC_WM_CREATED_ON
Windchill
PTC_WM_TEAM
Windchill
PTC_WM_ITERATION
Windchill
WGMREVKEY
Windchill
PTC_WM_VERSION
Windchill
PTC_WM_REVISION
Windchill
PTC_WM_LIFECYCLE_STATE
Windchill
PTC_WM_MODIFIED_ON
Windchill
PTC_WM_LOCATION
Windchill
PTC_WM_CREATED_BY
Windchill
PTC_WM_LIFECYCLE
Windchill


### 第 28 页
Page 28
VW 01059-5: 2015-04
 
For economic manufacturing, the metric ISO threads specified in table B.1, table B.2, table B.3,
and table B.4 must be used preferentially. The core hole diameters specified are average values.
The exact value depends on the material and can be obtained from the cited standard.
Table B.1 – Standard screw thread as per VW 11610
Thread diameter
Lead
Core hole diameter as per VW 11635
M4
0.70 mm
3.30 mm
M5
0.80 mm
4.20 mm
M6
1.00 mm
5.00 mm
M7
1.00 mm
6.00 mm
M8
1.25 mm
6.75 mm
M10
1.50 mm
8.45 mm
M12
1.75 mm
10.20 mm
Table B.2 – Fine-pitch thread as per VW 11610
Thread diameter
Lead
Core hole diameter as per VW 11635
M6 × 0.75
0.75 mm
5.25 mm
M7 × 0.75
0.75 mm
6.25 mm
M8 × 0.75
0.75 mm
7.25 mm
M8 × 1.00
1.00 mm
7.00 mm
M9 × 1.00
1.00 mm
8.00 mm
M10 × 0.75
0.75 mm
9.25 mm
M10 × 1.00
1.00 mm
9.00 mm
M10 × 1.25
1.25 mm
8.75 mm
M12 × 1.00
1.00 mm
11.00 mm
M12 × 1.25
1.25 mm
10.75 mm
M12 × 1.50
1.50 mm
10.50 mm
Table B.3 – Interference thread as per VW 11516
Thread diameter
Lead
Core hole diameter as per VW 11535
M6
1.00 mm
4.80 mm
M7
1.00 mm
6.00 mm
M8
1.25 mm
6.50 mm
M10 × 1.00
1.00 mm
8.80 mm
M12 × 1.50
1.50 mm
10.25 mm
M14 × 1.50
1.50 mm
12.25 mm
Appendix B (informative)  


### 第 29 页
Page 29
VW 01059-5: 2015-04
Table B.4 – Special thread M24 x 1.5 as per VW 01044
Thread diameter
Lead
Core hole diameter
M24 × 1.50
1.50 mm
21.90 mm

