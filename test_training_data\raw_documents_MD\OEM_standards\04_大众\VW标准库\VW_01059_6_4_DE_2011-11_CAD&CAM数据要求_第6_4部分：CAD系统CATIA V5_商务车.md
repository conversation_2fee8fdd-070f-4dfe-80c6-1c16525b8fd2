# VW_01059_6_4_DE_2011-11_CAD&CAM数据要求_第6_4部分：CAD系统CATIA V5_商务车.pdf

## 文档信息
- 标题：
- 作者：
- 页数：19

## 文档内容
### 第 1 页
Konzernnorm
VW 01059-6-4
Ausgabe 2015-11
Klass.-Nr.:
22632
Schlagwörter:
CAD, CAM, CATIA, KVS, TM, KPR, ZSB, DMU, OUT, Produktstruktur, PCA, IPP
Anforderungen an CAD/CAM-Daten – CAD-System CATIA V5-6
Teil 4: Produktstrukturen für die PDA TM
Frühere Ausgaben
VW 01059-6 Beiblatt 4: 2006-12
Änderungen
Gegenüber der VW 01059-6 Beiblatt 4: 2006-12 wurden folgende Änderungen vorgenommen:
–
Status des Dokuments von Beiblatt zu Norm geändert
–
Normtitel geändert
–
Version des CAD-Systems CATIA von V5 in V5-6 geändert
–
Fachverantwortung geändert
–
Norm vollständig überarbeitet, erwei<PERSON>t und neu gegliedert
–
Anwendungsbereich erweitert, alter Stand: das Beiblatt galt nur für die Marke Volkswagen
Nutzfahrzeuge, neuer Stand: die Norm gilt für den ganzen Volkswagen Konzern
Norm vor Anwendung auf Aktualität prüfen.
Die elektronisch erzeugte Norm ist authentisch und gilt ohne Unterschrift.
Seite 1 von 19
Fachverantwortung
Normung
K-SIPE-2/3
Stefan Biernoth
Tel.: +49 5361 9-48896
EKDV/4 Norbert Wisla
EKDV
Tel.: +49 5361 9-48869
Maik Gummert
Alle Rechte vorbehalten. Weitergabe oder Vervielfältigung ohne vorherige Zustimmung einer Normenabteilung des Volkswagen Konzerns nicht gestattet.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Seite 2
VW 01059-6-4: 2015-11
Inhalt
Seite
Anwendungsbereich ................................................................................................... 2
Allgemeine Regeln für die OUT-Adapter und das OUT-Product des Root-
Products ..................................................................................................................... 3
OUT-Adapter des Root-Products ............................................................................... 3
DMU-CATPart des Root-Products ............................................................................. 3
PCA-CATPart des Root-Products .............................................................................. 3
Oxx-CATPart des Root-Products ............................................................................... 4
OUT-CATProduct des Root-Products ........................................................................ 5
OUT-CATProduct des Root-Products mit einem DMU-CATPart ............................... 5
OUT-CATProduct des Root-Products mit einem PCA-CATPart ................................ 5
OUT-CATProduct des Root-Products mit zwei oder mehreren OUT-Adaptern ......... 5
Produktstrukturen ....................................................................................................... 6
KPR-Produktstrukturen für Einzelteile ........................................................................ 6
KPR-Produktstruktur mit einem INP-CATProduct, GEO-CATProduct und OUT-
CATProduct ................................................................................................................ 6
KPR-Produktstruktur mit einem GEO-CATPart und OUT-CATProduct ..................... 7
KPR-Produktstruktur mit einem GEO-CATProduct und OGNDeclaration ................. 7
ZSB-Produktstrukturen für Baugruppen ..................................................................... 8
ZSB-Produktstruktur mit mehreren Teilenummern .................................................... 8
ZSB-Produktstruktur mit einer Teilenummer (z.B. Kauf-ZSB) .................................. 14
Produktstruktur für die Zeichnungsableitungen ........................................................ 17
Know-How-Schutz mit IPP ....................................................................................... 18
Speichern mit IPP unter der PDA TM ...................................................................... 18
Ausprägung IP-Protection-Status „extract“ .............................................................. 18
Ausprägung IP-Protection-Status “generate“ ........................................................... 18
IP-Protection-Status „copy“ ...................................................................................... 18
IP-Protection-Status „stop“ ....................................................................................... 19
Speichern mit IPP unter der PDA TZ ....................................................................... 19
Information über die Regeln für die Konvertierung von Adaptern nach V4 .............. 19
Mitgeltende Unterlagen ............................................................................................ 19
1
2
2.1
2.1.1
2.1.2
2.1.3
2.2
2.2.1
2.2.2
2.2.3
3
3.1
3.1.1
3.1.2
3.1.3
3.2
3.2.1
3.2.2
3.3
4
4.1
4.1.1
4.1.2
4.1.3
4.1.4
4.2
5
6
Anwendungsbereich
Diese Norm gilt in Ergänzung zur VW 01059-6 für Produktstrukturen mit dem CAD-Typ KPR, ZSB
oder Z01-Z99 und der PDA TM im Root-Product.
Die hier beschriebenen Produktstrukturen gelten für Einzelteile und Baugruppen, sowie bei deren
Nutzung im Rahmen der IPP (siehe Abschnitt 4) und ihrer Verwendung zur Erzeugung von Zeich‐
nungsableitungen (siehe Abschnitt 3.3). In diesem Zusammenhang wird auch die PDA TZ ange‐
sprochen.
Bei der Nutzung von IPP muss zwischen der CATIA V5-6 Produktstruktur im CAD-System und
dem im HyperKVS unter der Ziel-PDA TM oder TZ persistent gespeicherten Ergebnis unterschie‐
den werden.
Im Abschnitt 3 wird beschrieben, wie CATIA V5-6 Produktstrukturen im CAD-System aufzubauen
sind. Im Abschnitt 4 wird beschrieben, wie sich die verschiedenen IPP-Mechanismen auf das Er‐
gebnis im HyperKVS auswirken.
1  


### 第 3 页
Seite 3
VW 01059-6-4: 2015-11
Allgemeine Regeln für die OUT-Adapter und das OUT-Product des Root-Products
Die OUT-Adapter des Root-Products müssen unter dem OUT-Product des Root-Products ange‐
ordnet sein.
OUT-Adapter des Root-Products (Abschnitt 2.1 bis Abschnitt 2.2) und das OUT-Product des Root-
Products (Abschnitt 2.2.1 bis Abschnitt 2.2.3) dürfen jeweils nur einmal in der Produktstruktur in‐
stanziiert werden.
OUT-Adapter des Root-Products
DMU-CATPart des Root-Products
Das DMU-CATPart des Root-Products ist das CATPart, das in HyperKVS automatisch in ein CA‐
TIA V4-Modell konvertiert und dieses anschließend dem VPM zur Verfügung gestellt wird. Das
DMU-CATPart des Root-Products enthält nur die komplette DMU-relevante Geometrie des Root-
Products (VW 01059-6-3).
Das DMU-CATPart des Root-Products ist durch folgende Merkmale gekennzeichnet:
–
es besitzt den CAD-Typ DMU
–
es ist wie in dem Bild 1 benannt
–
es ist unterhalb des zum Root-Product gehörenden OUT-CATProducts (siehe Abschnitt 2.2)
angeordnet
–
es darf nur einmal in der Produktstruktur verbaut sein.
Legende
1
identisch mit dem Root-Product
2
Standard für das DMU-CATPart
Bild 1 – Benennung des DMU-CATParts des Root-Products an einem Beispiel
PCA-CATPart des Root-Products
Das PCA-CATPart des Root-Products enthält die teilbeschreibende Geometrie des Root-Products
und zusätzlich die fertigungsrelevanten Informationen (siehe VW 01059-6-3).
Das PCA-CATPart des Root-Products ist durch folgende Merkmale gekennzeichnet:
–
es besitzt den CAD-Typ PCA
–
es ist wie in dem Bild 2 benannt
2  
2.1  
2.1.1  
2.1.2  


### 第 4 页
Seite 4
VW 01059-6-4: 2015-11
–
es ist unterhalb des zum Root-Product gehörenden OUT-CATProducts (siehe Abschnitt 2.2)
angeordnet
–
es darf nur einmal in der Produktstruktur verbaut sein
Legende
1
identisch mit dem Root-Product
2
Standard für das PCA-CATPart
Bild 2 – Benennung des PCA-CATParts des Root-Products an einem Beispiel
Oxx-CATPart des Root-Products
Das Oxx-CATPart des Root-Products ist ein spezieller Adapter. Er enthält die mit dem Datenab‐
nehmer abgestimmte Geometrie und Informationen (siehe VW 01059-6-3).
Das Oxx-CATPart des Root-Products ist durch folgende Merkmale gekennzeichnet:
–
es besitzt den CAD-Typ O01 bis O99
–
es ist wie in dem Bild 3 benannt
–
es ist unterhalb des zum Root-Product gehörenden OUT-CATProducts (siehe Abschnitt 2.2)
angeordnet
–
Die Kombination aus Teilenummer, CAD-Typ, PDA, Version und Ust-E1 darf innerhalb der
Root-Product-Struktur nur einmal vorkommen
Legende
1
identisch mit dem Root-Product
2
Standard für das Oxx-CATPart (von O01 bis O99)
3
Standard für das Oxx-CATPart (von O01 bis O99)
Bild 3 – Benennung des Oxx-CATParts des Root-Products an einem Beispiel
2.1.3  


### 第 5 页
Seite 5
VW 01059-6-4: 2015-11
OUT-CATProduct des Root-Products
Das OUT-CATProduct des Root-Products enthält alle für das Root-Product relevanten OUT-CAT‐
Parts (Abschnitt 2.1.1 bis Abschnitt 2.1.3).
Die möglichen Kombinationen von OUT-Adaptern unterhalb des OUT-CATProducts werden in
Abschnitt 2.2.1 bis Abschnitt 2.2.3 näher erläutert.
Das OUT-CATProduct des Root-Products ist durch folgende Merkmale gekennzeichnet:
–
es besitzt den CAD-Typ OUT
–
es ist wie in dem Bild 4 benannt
–
es enthält mindestens einen OUT-Adapter mit der für das Root-Product DMU-relevanten Geo‐
metrie (das DMU-CATPart nach Abschnitt 2.1.1 oder/und das PCA-CATPart nach
Abschnitt 2.1.2)
–
es kann weitere OUT-Adapter nach Abschnitt 2.1.3 enthalten
–
es ist auf der ersten Strukturebene unterhalb des Root-Products angeordnet
–
es darf nur einmal in der Produktstruktur verbaut sein
Legende
1
identisch mit dem Root-Product
2
Standard für das OUT-CATProduct
Bild 4 – Benennung des OUT-CATProducts an einem Beispiel
OUT-CATProduct des Root-Products mit einem DMU-CATPart
In diesem Anwendungsfall liegt unter dem OUT-CATProduct nur das DMU-CATPart nach
Abschnitt 2.1.1.
OUT-CATProduct des Root-Products mit einem PCA-CATPart
Unter dem OUT-CATProduct liegt nur das PCA-CATPart nach Abschnitt 2.1.2.
OUT-CATProduct des Root-Products mit zwei oder mehreren OUT-Adaptern
In diesem Anwendungsfall liegen zwei oder mehrere OUT-Adapter nach Abschnitt 2.1.2 bis
Abschnitt 2.1.3 unter dem OUT-CATProduct.
Es muss das DMU-CATPart nach Abschnitt 2.1.1 oder das PCA-CATPart nach Abschnitt 2.1.2 vor‐
handen sein.
Sind sowohl das DMU-CATPart nach Abschnitt 2.1.1 als auch das PCA-CATPart nach
Abschnitt 2.1.2 vorhanden, so wird das DMU-CATPart zur Bereitstellung für das VPM genutzt.
2.2  
2.2.1  
2.2.2  
2.2.3  


### 第 6 页
Seite 6
VW 01059-6-4: 2015-11
Produktstrukturen
In den folgenden Abschnitten werden Produktstrukturen von Einzelteilen (KPR) und Baugruppen
(ZSB; Z01-Z99, KPR) beschrieben.
Eine Baugruppe ist über das ZSB-Kennzeichen in der Stückliste definiert. Besitzt eine Baugruppe
kein ZSB-Kennzeichen in der Stückliste, so ist sie wie ein Einzelteil zu behandeln und ihr Root-
Product erhält den CAD-Typ KPR.
Für alle Produktstrukturen, die als Ergebnis nach Abschluss aller Speichervorgänge unter der PDA
TM persistent im HyperKVS verbleiben, gilt:
–
Es muss genau ein DMU-CATPart nach Abschnitt 2.1.1 oder genau ein PCA-CATPart nach
Abschnitt 2.1.2 vorhanden sein.
–
Es können zusätzliche OUT-Adapter (nach Abschnitt 2.1.2 bis Abschnitt 2.1.3) vorhanden
sein.
KPR-Produktstrukturen für Einzelteile
In KPR-Produktstrukturen muss ein OUT-CATProduct (nach Abschnitt 2.2.1 bis Abschnitt 2.2.3)
oder eine OGNDeklaration1) enthalten sein.
KPR-Produktstruktur mit einem INP-CATProduct, GEO-CATProduct und OUT-CATPro‐
duct
–
Dieses Produkt ist nach dem EVA-Prinzip aufgebaut.
–
Eingabe: Alle Referenzdaten und Input-Adapter unter dem INP-CATProduct abgelegen.
–
Verarbeitung: Alle Konstruktionsdaten unter dem GEO-CATProduct erstellen.
–
Ausgabe: Alle Ergebnisdaten unter dem OUT-CATProduct erzeugen.
Legende
1
Root-Product
2
Beispielstruktur
3
OUT-CATProduct des Root-Products nach Abschnitt 2.2
Bild 5 – Beispiel für eine KPR-Produktstruktur (INP-, GEO-, OUT-CATProduct)
3  
3.1  
3.1.1  
1)
Hinweis: Mit Hilfe der CAA „OutGen“ kann vom Anwender ein OUT-CATProduct in der KPR-Produktstruktur deklariert (vorbereitet)
werden. Dazu wird von CATIA ein Feature „OGNDeclaration“ im Root-CATProduct abgelegt. Die automatische Generierung des
OUT-CATProducts erfolgt während des persistenten Speicherns im KVS.


### 第 7 页
Seite 7
VW 01059-6-4: 2015-11
KPR-Produktstruktur mit einem GEO-CATPart und OUT-CATProduct
Legende
1
Root-Product
2
Beispielstruktur
3
OUT-CATProduct des Root-Products nach Abschnitt 2.2
Bild 6 – Beispiel für eine KPR-Produktstruktur (GEO-CATPart, OUT-CATProduct)
KPR-Produktstruktur mit einem GEO-CATProduct und OGNDeclaration
–
Diese KPR-Produktstruktur wird ohne OUT-CATParts, aber mit einer OGNDeclaration im KVS
unter der PDA TM gespeichert
–
Die Erzeugung des OUT-CATProducts mit einem oder mehreren OUT-CATParts erfolgt im
KVS automatisch entsprechend der OGNDeclaration.
Legende
1
Root-Product
2
Beispielstruktur
Bild 7 – Beispiel für eine KPR-Produktstruktur mit OGNDeclaration
3.1.2  
3.1.3  


### 第 8 页
Seite 8
VW 01059-6-4: 2015-11
ZSB-Produktstrukturen für Baugruppen
–
Das Root-Product einer ZSB-Produktstruktur muss den CAD-Typ ZSB oder Z01- Z99 besit‐
zen.
–
Es wird zwischen ZSB-Produktstrukturen mit mehreren Teilenummern und ZSB-Produktstruk‐
turen mit nur einer Teilenummer unterschieden.
–
Die ZSB-Produktstruktur muss ein OUT-CATProduct (siehe Abschnitt 2.2) oder eine OGNDec‐
laration besitzen.
–
Bei sehr großen ZSB-Produktstrukturen (z. B. Klimagerät, Sitz) mit dem CAD-Typ ZSB kann
bei der Archivierung im HyperKVS das DMU-CATPart zum (ZSB-)Root-Product nach
Abschnitt 2.1.1 und Abschnitt 2.2 mit Zustimmung des Bauteilverantwortlichen entfallen. Wenn
aber der ZSB nach Stückliste oder für den Nachfolgeprozess DMU relevant ist, muss ein se‐
parates DMU-CATPart unter der PDA TMU oder eine Hüllgeometrie (CGR, stl, etc.) unter der
PDA THS oder THD im HyperKVS archiviert und dem entsprechenden K.Stand zugeordnet
werden.
ZSB-Produktstruktur mit mehreren Teilenummern
–
Es existieren auch für Einzelteile und / oder Unterbaugruppen eigene Teilenummern.
–
Die Einzelteile liegen als CATParts oder mit KPR-Produktstruktur vor.
ZSB-Produktstruktur nach Stückliste
–
Der Strukturaufbau erfolgt entsprechend der Stücklistenstruktur
–
Der CAD-Typ des Root-Products kann ZSB (Bild 8) oder Z01-Z99 (Bild 9) sein
–
Es können auch Unter-ZSBs enthalten sein
–
Bei Verwendung eines RPS-Systems ist ein ZIN-CATPart2) erforderlich. Wird das Baugruppen-
RPS-System aus RPS-Elementen verschiedener Einzelteile definiert, so ist ein ZIN-CATPart
erforderlich. Ist das Baugruppen-RPS-System identisch mit dem RPS-System eines Einzel‐
teils, so kann auf das ZIN-CATPart verzichtet werden.
3.2  
3.2.1  
3.2.1.1  
2)
siehe RPS-Methodikleitfaden


### 第 9 页
Seite 9
VW 01059-6-4: 2015-11
Legende
1
Root-Product
2
Beispielstruktur
3
OUT-CATProduct des Root-Products nach Abschnitt 2.2
Bild 8 – Beispiel für eine ZSB-Produktstruktur nach Stückliste mit dem CAD-Typ ZSB


### 第 10 页
Seite 10
VW 01059-6-4: 2015-11
Legende
1
Root-Product
2
Beispielstruktur
3
OUT-CATProduct des Root-Products nach Abschnitt 2.2
Bild 9 – Beispiel für eine ZSB-Produktstruktur nach Stückliste mit CAD-Typ Z01


### 第 11 页
Seite 11
VW 01059-6-4: 2015-11
ZSB-Produktstruktur nach Bewegungsgruppen
–
Der ZSB wird für kinematische oder elastokinematische Bewegungen genutzt
–
Der CAD-Typ des Root-Products dieser Produktstruktur kann ZSB (Bild 10) oder Z01-Z99
(Bild 11) sein
Legende
1
Root-Product
2
Beispielstruktur
3
OUT-CATProduct des Root-Products nach Abschnitt 2.2
Bild 10 – Beispiel für eine ZSB-Produktstruktur nach Bewegungsgruppen mit CAD-Typ ZSB
3.2.1.2  


### 第 12 页
Seite 12
VW 01059-6-4: 2015-11
Legende
1
Root-Product
2
Beispielstruktur
3
OUT-CATProduct des Root-Products nach Abschnitt 2.2
Bild 11 – Beispiel für eine ZSB-Produktstruktur nach Bewegungsgruppen mit CAD-Typ Z02


### 第 13 页
Seite 13
VW 01059-6-4: 2015-11
ZSB-Produktstruktur nach Stückliste und Bewegungsgruppen
Legende
1
Root-Product
2
Beispielstruktur
3
OUT-CATProduct des Root-Products nach Abschnitt 2.2
Bild 12 – Beispiel für eine ZSB-Produktstruktur nach Stückliste und Bewegungsgruppen
3.2.1.3  


### 第 14 页
Seite 14
VW 01059-6-4: 2015-11
ZSB-Produktstruktur als flache Struktur
–
Alle Einzelteile sind direkt unter dem Root-Product angeordnet
–
Bei Verwendung eines RPS-Systems ist ein ZIN-CATPart erforderlich
Legende
1
Root-Product
2
Beispielstruktur
3
OUT-CATProduct des Root-Products nach Abschnitt 2.2
Bild 13 – Beispiel für eine flache ZSB-Produktstruktur mit mehreren Teilenummern
ZSB-Produktstruktur mit einer Teilenummer (z.B. Kauf-ZSB)
–
Die Teilenummer ist für alle Strukturelemente (CATParts und CATProduct`s) identisch
–
Die einzelnen Strukturelemente unterscheiden sich im CAD-Typ, optional in Benennung und
Kommentar
–
Die Struktur kann flach, durch Produkte mit dem CAD-Typ Z01-Z99 oder durch Bewegungs‐
gruppen mit dem CAD-Typ M01-M99 unterstrukturiert sein
3.2.1.4  
3.2.2  


### 第 15 页
Seite 15
VW 01059-6-4: 2015-11
ZSB-Produktstruktur als flache Struktur
–
Bei Verwendung eines RPS-Systems ist ein ZIN-CATPart erforderlich
Legende
1
Root-Product
2
Beispielstruktur
3
OUT-CATProduct des Root-Products nach Abschnitt 2.2
Bild 14 – Beispiel für eine ZSB-Produktstruktur als flache Struktur mit einer Teilenummer
3.2.2.1  


### 第 16 页
Seite 16
VW 01059-6-4: 2015-11
ZSB-Produktstruktur durch Produkte mit dem CAD-Typ Z01-Z99 unterstrukturiert
–
Die Unterstrukturierung erfolgt durch Produkte mit dem CAD-Typ Z01-Z99
–
Bei Verwendung eines RPS-Systems ist ein ZIN-CATPart erforderlich
Legende
1
Root-Product
2
Beispielstruktur
3
OUT-CATProduct des Root-Products nach Abschnitt 2.2
Bild 15 – Beispiel für eine ZSB-Produktstruktur mit Unterstruktur
3.2.2.2  


### 第 17 页
Seite 17
VW 01059-6-4: 2015-11
ZSB-Produktstruktur durch Produkte mit dem CAD-Typ M01-M99 unterstrukturiert
–
Die Unterstrukturierung erfolgt durch Produkte mit dem CAD-Typ M01-M99
Legende
1
Root-Product
2
Beispielstruktur
3
OUT-Product des Root-Products nach Abschnitt 2.2
Bild 16 – Beispiel für eine ZSB-Produktstruktur nach Bewegungsgruppen mit einer Teilenummer
Produktstruktur für die Zeichnungsableitungen
Die in Abschnitt 3.1 und Abschnitt 3.2 beschriebenen Produktstrukturen können zur Zeichnungsab‐
leitung verwendet werden.
Produktstrukturen die ausschließlich der Zeichnungsableitung dienen, sind in VW 01059-6-6 (wird
erstellt) geregelt.
*******  
3.3  


### 第 18 页
Seite 18
VW 01059-6-4: 2015-11
Know-How-Schutz mit IPP
Bei IPP (Intellectual Property Protection) handelt es sich um eine IT-basierte Methode des Know-
How-Schutzes im KVS.
Es wird zwischen zwei unterschiedlichen IPP-Methoden für die Ziel PDA TM (Abschnitt 4.1) und
TZ (Abschnitt 4.2) unterschieden.
Die Originalumfänge werden nach öffentlichen und schützenswerten Anteil aufgeteilt.
Nur der öffentliche Umfang verbleibt unter der Ziel-PDA.
Der Originalumfang verbleibt in der assoziierten PDA GES.
Zwischen den Dokumentversionen der Ziel PDA TM oder TZ und der PDA GES besteht im KVS
eine Beziehung.
Speichern mit IPP unter der PDA TM
Sofern das Root-Product die PDA TM und den CAD-Typ KPR oder ZSB besitzt und der IP-Protec‐
tion-Status den richtigen Wert hat, kann eine IPP im KVS durchgeführt werden.
Der IP-Protection-Status wird von VALIDAT ermittelt. Je nach Status und Inhalt der Ausgangsda-
tensätze wird im KVS die jeweilige Ausprägung der IPP angewendet.
Ist im Root-Product eine Property „VWG_IP_PROTECTION“ mit dem Wert OFF enthalten, findet
keine IPP im KVS statt.
Ausprägung IP-Protection-Status „extract“
Dieser IP-Protection-Status bedeutet, dass bereits genau einer der nachfolgenden OUT-Anteile
vorhanden ist:
–
Out-Product nach Abschnitt 2.2.1 (Es verbleibt nur das OUT-CATPart in der Ziel PDA TM)
–
Out-Product nach Abschnitt 2.2.2 (Es verbleibt nur das OUT-CATPart in der Ziel PDA TM)
–
Out-Product nach Abschnitt 2.2.3 (Es verbleibt nur das OUT-CATProduct in der Ziel PDA TM)
–
OUT-CATPart nach Abschnitt 2.1.1 (Es verbleibt nur das OUT-CATPart in der Ziel PDA TM)
–
OUT-CATPart nach Abschnitt 2.1.2 (Es verbleibt nur das OUT-CATPart in der Ziel PDA TM)
Der Original Umfang verbleibt in der assoziierten PDA GES.
Ausprägung IP-Protection-Status “generate“
Dieser IP-Protection-Status bedeutet, dass der öffentliche Umfang einer Konstruktion über das
Programm OUTGEN deklariert wurde und erst durch die Mechanismen der IPP im Umfeld des
KVS automatisch erzeugt wird.
Das Ergebnis folgt den Regeln nach Abschnitt 4.1.1.
IP-Protection-Status „copy“
Dieser IP-Protection-Status bedeutet, dass keine IPP angewendet wird.
Der Original Umfang verbleibt in der Ziel PDA TM.
4  
4.1  
4.1.1  
4.1.2  
4.1.3  


### 第 19 页
Seite 19
VW 01059-6-4: 2015-11
IP-Protection-Status „stop“
Dieser IP-Protection-Status bedeutet, dass mindestens ein VALIDAT Kriterium nicht erfüllt wurde,
oder dass das RootProdukt strukturelle Fehler aufweist. Damit ist die VALIDAT Prüfung KO.
Speichern mit IPP unter der PDA TZ
Bei der IPP für die PDA TZ wird kein IP-Protection-Status benötigt. Die Steuerung erfolgt aus‐
schließlich über das Vorhandensein von verlinkten Erzeugungsgeometrien. Dabei ist es auch uner‐
heblich, ob es sich bei den Erzeugungsgeometrien um einzelne CATParts oder um Productstruktu‐
ren handelt.
Sofern eine CATDrawing mit verlinkter Erzeugungsgeometrie unter der Ziel-PDA TZ im KVS ge‐
speichert, verbleibt nur die öffentliche CATDrawing unter der Ziel-PDA TZ.
Der Originalumfang wird in der assoziierten PDA GES gespeichert.
Information über die Regeln für die Konvertierung von Adaptern nach V4
Für beliebige CATProducts gelten nachfolgende Konvertierungsregeln im GINA/GRICOS Umfeld.
Eine Unterscheidung nach CAD-Typ findet nicht statt.
–
Ist ein DMU-CATPart (nach Abschnitt 2.1.1), aber kein PCA-CATPart (nach Abschnitt 2.1.2)
vorhanden, wird das DMU-CATPart konvertiert
–
Ist ein PCA-CATPart (nach Abschnitt 2.1.2), aber kein DMU-CATPart (nach Abschnitt 2.1.1)
vorhanden, wird das PCA-CATPart konvertiert
–
Ist ein DMU-CATPart (nach Abschnitt 2.1.1) und ein PCA-CATPart (nach Abschnitt 2.1.2) vor‐
handen, wird entweder das DMU-CATPart oder das PCA-CATPart konvertiert. Welches kon‐
vertiert wird, hängt von der Prioritätensteuerung ab, die für jede Konvertierungsmethode ein‐
zeln konfigurierbar ist.
–
Ist kein DMU-CATPart (nach Abschnitt 2.1.1) und kein PCA-CATPart (nach Abschnitt 2.1.2)
vorhanden, wird das gesamte CATProduct konvertiert.
Für die V5V4Migration ist allein das Vorhandensein des DMU-CATParts ausschlaggebend. Fehlt
dieses, wird die Migration nicht durchgeführt.
Mitgeltende Unterlagen
RPS Methodikleitfaden
https://eportal.wob.vw.vwg/jct_ep/web/zusammenarbeit-mit-partnern/Methoden_Prozesse/
4.1.4  
4.2  
5  
6  

