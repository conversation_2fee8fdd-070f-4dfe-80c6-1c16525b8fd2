# VW_01064_CH_2015-01_生产车辆上的模块标记.pdf

## 文档信息
- 标题：
- 作者：jianglei
- 页数：29

## 文档内容
### 第 1 页
VOLKSWAGEN 
企业标准 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   
VW01064 
发布 2015-01 
编号： 01152 
主题词：标记、组件标记、模块、模块目录、生成状态文件、可追溯性、安装检查、序列号、
 
 
条形码、代码 39、数据矩阵代码、BG 在线、RFID 
 
生产车辆上的模块标记 
生成状态文件-机械车辆零件代码 
 
过往版本 
VW 01064：1996-09，1999-05，2001-06，2001-11，2002-03，2002-09，2003-04，2003-12，
2007-11，2008-05，2010-09，2011-07，2013-11 
 
更改 
相较于 VW 01064：2013-11，有以下更改： 
——章节 1“范围”更改 
——章节 2.2“生成状态文件（BZD）”扩展 
——章节 3.1“一般要求”扩展 
——章节 3.3“数据采集”更改 
——章节 4“用 1-D 代码（条形码，代码 39）和 2-D 代码（数据矩阵代码）生成状态文件标
记”：增加第 2 段 
——章节 4.1.3“序列号”：第 1 段更改，第 3 段拓展 
——章节 4.2“实施案例”，图 5 更改 
——章节 5.1“用于零件安装检查和生成状态文件的二维代码的数据序列”：增加了 注释 5 
——章节 5.2.1“使用生成状态文件进行部件安装检查的标准数据内容”：修正了第 1 段，添
加矩形 DMC，描述更改，图 11 更改 
——章节 5.2.2“零件安装检查的最小数据内容”：第 1 段拓展，添加矩形 DMC，描述更改 
——章节 5.2.3“生成状态文件的最小数据内容”：第 1 段拓展，添加矩形 DMC，描述更改，
图 13 更改，注释 9 更改 
——章节 5.2.4“使用生成状态文件进行部件安装检查的最大数据内容”：添加矩形 DMC，描
述更改，图 14 更正 
——章节 6.2.1“当用于生产车辆的工厂时”，ISO/IEC TR 29158 代替 AIM DPM，总体质量从
“B”变为“3” 
——新增章节 6.2.2“当用于生产车辆的工厂时” 
——章节 6.3“纯文本信息（1-D 和 2-D 代码）”更改 
——章节 9“适用文件”更新 
 
 
 
 
 


### 第 2 页
1 
范围........................................................................................................................................... 4 
2 
一般说明，定义 ....................................................................................................................... 4 
2.1 
可追溯性 ....................................................................................................................... 4 
2.2 
生成状态文件（BZD） ............................................................................................... 4 
2.3 
模块 ............................................................................................................................... 5 
2.4 
模块数据 ....................................................................................................................... 5 
2.5 
零件识别（安装检测） ............................................................................................... 6 
2.6 
车辆部件 ....................................................................................................................... 6 
2.7 
复杂安装 ....................................................................................................................... 6 
3. 
标记的一般要求和数据处理的技术要求 ............................................................................... 6 
3.1 
一般要求 ....................................................................................................................... 6 
3.2 
标记类型 ....................................................................................................................... 7 
3.3 
数据获取 ....................................................................................................................... 7 
3.4 
标准配置 ....................................................................................................................... 8 
3.5 
使用 1-D 和 2-D 代码数据处理的技术要求 .............................................................. 8 
3.6 
 43 模校验位计算 ........................................................................................................ 9 
4. 
使用 1-D 代码（条形码，代码 39）和 2-D 代码（二维码）生成状态文件标记 .............. 9 
4.1 
生成状态文件 1-D 和 2-D 代码数据序列（模块数据） ........................................... 9 
4.1.1 
模块编号 ......................................................................................................... 10 
4.1.2 
制造商代码 ..................................................................................................... 10 
4.1.3 
序列号 ............................................................................................................. 10 
4.1.4 
43 模校验位计算 ............................................................................................ 11 
4.2 
应用示例 ..................................................................................................................... 11 
5 
使用生成状态文件标记零件安装检查 ................................................................................. 12 
5.1 
用于零件安装检查和生成状态文件的 2-D 代码的数据序列 ................................. 12 
5.1.1 
零件号 ............................................................................................................. 12 
5.1.2 
零件编码 ......................................................................................................... 12 
5.1.3 
DUNS 号 ......................................................................................................... 13 
5.1.4 
生产日期 ......................................................................................................... 13 
5.1.5 
模块数据 ......................................................................................................... 14 
5.1.6 
附加信息 ......................................................................................................... 14 
5.1.7 
数据序列中的特殊字符 ................................................................................. 14 
5.2 
2-D 代码示例 ............................................................................................................. 14 
5.2.1 
用于生成状态文件的部件安装检查的标准数据内容 ................................. 14 
5.2.2 
零件安装检测的最小数据内容 ............................................................................. 15 
5.2.3 
生成状态文件最小数据内容 ......................................................................... 16 
5.2.4 
使用生成状态文档检查部件安装的最大数据内容 ..................................... 16 
6 
标签设计和布局（1-D 代码 2-D 代码） ............................................................................. 17 
6.1 
条形码的代码符号（1-D 码） .................................................................................. 17 
6.2 
矩阵码的代码符号（2-D 代码） .............................................................................. 17 
6.2.1 
当用于生产车辆的工厂时 ............................................................................. 17 
6.2.2 
当用于生产发动机的工厂时 ......................................................................... 18 
6.3 
纯文本信息（1-D 和 2-D 代码） ............................................................................. 18 
6.4 
永久性连接到组件的单部件标签（1-D 和 2-D 代码） .......................................... 19 


### 第 3 页
6.4.1 
两部件或多部件标签（1-D 和 2-D 代码） .................................................. 19 
6.5 
ASSY 外表面标签（组标记） .................................................................................. 19 
6.6 
ASSY 转让票（组标记）.......................................................................................... 20 
6.7 
直接应用于材料中的标记（直接部分标记（DPM））1） ..................................... 20 
6.8 
校验 ............................................................................................................................. 20 
6.8.1 
校验码 ............................................................................................................. 20 
6.8.2 
验证 2-D 码..................................................................................................... 20 
7 
组标记——预先记录复杂组件的数据 ................................................................................. 21 
7.1 
生成状态文件组标记 ................................................................................................. 21 
7.1.1 
数据序列中的特殊字符 ................................................................................. 21 
7.2 
零件安装检查组标记 ................................................................................................. 21 
7.2.1 
数据序列中的特殊字符 ................................................................................. 22 
7.3 
数据交换 ..................................................................................................................... 22 
8 
使用 RFID 标签生成状态文件标记和零件标识 .................................................................. 22 
8.1 
技术实施 ..................................................................................................................... 23 
8.1.1 
唯一项目标识符（UII-bank 01） ................................................................. 23 
8.1.2 
用户存储器（UM-bank 11） ........................................................................ 23 
8.2 
用户存储器数据内容示例 ......................................................................................... 23 
8.2.1 
用于生成状态文档的部件安装检查的标准数据内容 ................................. 23 
8.2.2 
零件安装检查最小数据内容 ......................................................................... 24 
8.2.3 
生成状态文件最小数据内容 ......................................................................... 24 
8.2.4 
使用生成状态文件检查部件安装的最大数据内容 ..................................... 25 
9 
参考文件 ................................................................................................................................. 26 
10 
参考文献 ......................................................................................................................... 26 
附录 A（告知） ............................................................................................................................ 27 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 4 页
1 范围 
本标准描述了需要强制性生成状态文件（BZD）且用于生产车辆（具有通用经营许可证（ABE）
的车辆）以及 ASSY 中的部件的车辆部件的外部标记要求，受制于强制性的生成状态文件
（例如引擎）。标记中额数据是用于记录和追踪车辆部件（“生成状态文件”）和识别大众汽
车集团内的部件（“安装检查”） 
 
按照该标准的标记不能替代参照大众标准 VW 10500 的部件标记。 
本标准旨在： 
——负责制定标记的开发人员需强制生成状态文件 
——质量保证人员负责对组件上的标识进行抽样，并受制于强制性的生成状态文件 
——负责添加标记的车辆部件供应商必须提供强制性生成状态文件 
——必要时复杂组件供应商（ASSY）负责预先记录模块数据 
——生产计划人员负责建立数据记录程序 
 
大众汽车公司版本状态文件适用于以下大众汽车集团品牌： 
大众、奥迪、斯柯达、西亚特、大众商用车、宾利、兰博基尼 
 
注 1：此标记不适用于可诊断的组件。电子控制单元的标记在车间草图 WSK.013.29.E 中进
行了描述。 
注 2：本标准中描述的“用 RFID 标签生成状态文件标记和部件标识”仅适用于生产部件的“部
件安装检查”和/或”生成状态文件“。VW 01067 描述了样件是如何标记的。 
2 一般说明，定义 
2.1 
可追溯性 
可追溯性意味着能够确定产品或销售商品何时何地由谁获得、生产、加工、储存、运输、使
用或处置。此路线和流程跟踪也称为“追踪”。下游追踪（从生产者到消费者）和上游追踪（从
消费者到生产者）有所区别。 
2.2 
生成状态文件（BZD） 
在大众集团内部，生成状态文件分为两大类： 
——单个生成状态文件 
——批量生成状态文件（为每个集装箱生成状态文件） 
 
在单个生成状态文件的情况下，必须将标记添加到每个独立的组件。本标准适用于所述标记 
相比之下，批量生成状态文件不需要标记各个组件。相反，它基于使用物流过程和系统中使
用的标记。因此，本标准不适用于批量生成状态文件。 


### 第 5 页
标记中的数据用于记录和追踪大众汽车集团内的车辆部件（“生成状态文件”）。生成装填文
件用于建立一个清晰的链接，可以识别车辆识别号码和组件的某些特征之间的对应关系。这
些特性包括制造商和序列号，零件号以及电子控制单元的硬件和软件版本。这使得在保修索
赔（召回）的情况下可以精确确定设计哪些车辆识别号码。 
 
图 1 显示了在发生召回事件时的三个主要程序： 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
                 供应商  
 
 
 
 
 
 
 
 
 
大众集团 
图 1-基本召回流程 
2.3 
模块 
符合本集团标准的车辆部件或组件。每个模块都通过唯一的模块编号进行标识。 
 
模块数据在“BG 在线”系统中定义。关于强制性标记的说明可以在工程图和 TLD（技术文件
指南）表中找到。 
2.4 
模块数据 
模块数据是生成状态文件标记数据的一部分，记录用于可追溯性母的。 
 
为了能够唯一地识别车辆部件，需要以下生成状态文件数据（“模块数据”）： 
 
供应商意识到已
经交付的车辆部
件有缺陷 
供 应 商 分
析 有 缺 陷
的零件 
质量监控：有特定供
应商的缺陷零件引
起的保修索赔 
 
范围不清 
质量监控：有特定
供应商的缺陷零
件引起的保修索
赔 
 
所有生产都受到
影响 
基于其自身的质量数
据，供应商确定哪些车
辆部件有缺陷 
供应商 
档案 
根据组件序列号和/
或制造商代码搜索受
影响的车辆 
车辆 
档案 
识别缺陷车辆部件的
参考数据 
实际受到影响的车辆将
被召回 


### 第 6 页
模块编号——>车辆部件的类型 
制造商代码——>制造商和制造地点（如果适用） 
序列号——>车辆部件 
校验位——>模块 43 算法 
 
这些受制于强制性生成状态文件的数据用作特定车辆部件的参考，在生产过程中以车辆特定
方式记录，并存储在大众汽车集团的长期车辆档案中。这使得在出现故障时可以精确确定涉
及哪些车辆。 
模 块 数 据 的 语 法 在
BG
在 线 群 组 模 块 目 录 中 进 行 了 描 述 （ 联 系 人 ：
<EMAIL>） 
2.5 
零件识别（安装检测） 
除了上述模块数据之外，标记可以包括关于车辆部件的附加信息： 
——组件标号（VW 01098） 
——DUNS 号码（数据通用编号系统） 
——生产日期 
——零件版本 
 
此数据不适用于归档目的。相反，如果需要，它可用于正在进行中的制造过程，以验证车辆
部件是否已正确安装（技术设计、使用年限、制造商）。 
2.6 
车辆部件 
车辆部件或装配件，其零件号参考相应的技术设计。如果适用，零件号是安装检查的基础。 
2.7 
复杂安装 
包含多个模块的组件。例如，座椅组件包括侧面安全气囊，靠背装饰罩气囊，传感器垫和安
全带扣带传感器。如果在完成的 ASSY 中模块的个人数据不可再访问，则必须预先记录数
据（刹那间第 7 节）。 
3. 标记的一般要求和数据处理的技术要求 
3.1 一般要求 
该标记是与释放相关的组件属性，并且在样本检查过程中必须将其考虑在内。必须检查以下
内容： 
——可以在生产条件下记录数据 
——应用数据序列的内容 


### 第 7 页
——组件上的标记类型及其应用 
——保存期≥15 年 
 
在样品检查过程中检查标记。为此，供应商/制造商提供与生产过程中标记方式相同的组件。 
 
供应商/制造商确保正在进行的生产过程中满足此处所描述的标识要求。例如，可以通过随
机抽样来确保。 
从创建数据的那一刻起，保留期至少为 15 年（相当于文件分类系统（CSD）7.2 级）供应商
/制造商必须确保该数据集在整个保留期内保持唯一（≥15 年）。它必须纪律与标记的车辆部
件有关的、重要的、与质量相关的单个信息（例如，使用的原材料批次、采购部件的制造商、
测试盒设定值、制造地点和系统），建立一个明确的链接，使得确定该信息与相应的参考数
据之间的对应关系有可能，并存档该信息。如有必要，这将有可能获得相关功能、制造和材
料质量的明确信息。 
如果供应商制造的组件包含受制于强制性生成状态文件的组件（例如燃油箱组件内的燃油
泵），则供应商必须记录受强制生成状态文件限制的组件的数据，将数据添加到 ASSY 中，
并将这些数据至少保存 15 年。这也适用于由电子控制单元和机械部件组成的 ASSY（例如
带控制模块的前照灯）。 
如果不符合标记要求，则相关部件将被视为有缺陷，并且，必要时不得安装。 
3.2 标记类型 
目标是在制造过程中快速且经济高效地记录数据。 
 
有三种可用的标记类型，如下所示： 
——一维码（条形码）——>使用代码 39；见第 4 节 
——二维码（矩阵码）——>使用数据矩阵码，见第 4 节 
——RFID 标签——>见第 8 节 
 
如果需要部分或全部自动化数据纪律，标签标记有问题或车辆部件已配备 RFID 标签，则建
议使用 RFID 标签标记。 
注 3：无论是技术要求还是组织要求，必须始终与相关制造工厂商定，使用 RFID 标签标记。 
3.3 数据获取 
数据在车辆装备过程中或在相关的预装配区域进行记录。以下数据采集程序必须加以区分： 
 
直接输入：  
数据立即输入车辆部件的安装位置。通过扫描仪直接读取永久固定在组件
 
 
 
 
上的单件标签或直接标记。 
转移标签：  
数据是在车辆部件的安装位置获取的。在带有两个部分的标签中，可拆卸
 
 
 
 
和自粘部分贴在转移标签上的指定区域。通过使用扫描仪在下游序列点读
 
 
 
 
取转移标签输入数据。 
扫描：  
 
通过阅读 RFID 标签，数据被直接采集并输入到车辆部件的安装位置或后
 
 
 
 
 
续序列点。 


### 第 8 页
3.4 标准配置 
标记和数据记录的标准配置如下： 
——用标签上的二维码标记 
——在安装为孩子记录的数据 
 
与 1-D 代码相比，2-D 代码具有其他潜在的应用选项（安装检查）；读取精度高；需要更少
的空间；并且需要更少的标签材料——或者在直接标记的情况下不需要标签材料。 
 
在合理的情况下，上述选项允许偏离该标准配置。由于获取数据所涉及的技术条件，如果需
要，可以使用组合标记。 
3.5 使用 1-D 和 2-D 代码数据处理的技术要求 
下面描述的数据序列只允许使用图 2 中列出的 ASCII 字符： 
 
图例： 
- 
  
排除 
（1）  无限制 
（2）  仅限校验码 
（3） “#”字符放置在数据序列（分隔符）的前五个数据字段之间。及时字段中没有字符，
也必须始终使用分隔符。 
（4） “&”连接两个数据序列（连续字符，仅用于组标记） 
（5） “*”字符放在数据序列中模块数据的开始和结尾处。如果没有模块数据，则不使用 
（6） “=”字符终止数据序列（终止符） 
 
图 2 
在设计数据采集系统时，必须注意确保读取设备（固定式扫描仪，手持式扫描仪）的技术界
面和配置不会导致字符错误。问题示例：英文键盘（与德语键盘布局相比，“Y”和“Z”字符
交换）。 


### 第 9 页
3.6  
43 模校验位计算 
校验位用于根据标记中的纯文本信息验证手动条目。对于单字符验证为定义了 43 个不同的
字符（对于与校验和值 00-42 相对应的 ASCII 字符，请参见图 2）。 
 
校验位的值根据数据序列字符计算，不包括校验位。计算方法基于分配表（参见图 2）。 
 
校验位仅为模块数据计算（参见 4.1 节）。零件安装检查的数据字段（见 5.1 节）不用于计算
校验位。 
 
计算校验位： 
1） 使用分配表，确定数据序列中每个字符的校验值 
2） 计算所有校验值的综合 
3） 将这个数除以 43 
4） 使用除法余数和赋值表来确定校验位是什么 
 
示例： 
数据学序列中没有校验位  
065 KTO1234567 
总计 
 
 
 
 
 
0+6+5+38+20+29+24+1+2+3+4+5+6+7=150 
除  
 
 
 
 
 
150:43=3 余 21 
校验位  
 
 
 
 
21≜L（见图 2） 
➝数据序列 
 
 
 
 
*065 KTO1234567L* 
 
注 4：校验位是数据序列的一部分，不能与代码符号中的校验位混淆。 
4. 使用 1-D 代码（条形码，代码 39）和 2-D
代码（二维码）生成状态文件标记 
生成状态文件标记可以使用 1-D 代码或 2-D 代码。在这两种情况下，下面描述的数据序列
都适用。 
 
如果组件需要标记为部件安装检查和生成状态文件目的，则必须使用 2-D 代码。 
 
生成状态文件标记不会取代 V 10540-1 和 VW 10540-3 中的部件标记。如果适用，两个标记
必须相互独立地应用在组件上。 
4.1 生成状态文件 1-D 和 2-D 代码数据序列（模块数据） 
条形码和二维码的数据序列包含四个数据字段，在标准版本中包含十五个字符（例外包括发
动机模块号 005 和传输模块号 006，其中包含 21 个字符），请参见图 3： 


### 第 10 页
图 3 
这里描述的数据序列仅仅是一个例子。每个单独模块的结构必须从“BG 在线”系统中获取。 
 
“模块编号”和“序列号”数据字段只允许字符 0 至 9 和 A 至 Z。检验为允许符合图 2 的特殊字
符。 
4.1.1 模块编号 
模块编号用于识别车辆部件的类型。它有三个字符长度，可以使字母或是数字。示例：671=
前排乘客侧安全气囊模块。 
4.1.2 制造商代码 
制造商代码是大众汽车公司用于区分不同制造商及其制造地点的内部代码。有关定义，请参
阅 VE 10540-1。该字段有四个字符。在数据序列中，字母或字母数字三字符制造商代码必
须右对齐，并以空白符开头。 
 
示例：制造商：“ZFS”➝数据序列中的语法：“ZFS”。 
4.1.3 序列号 
序列号用于在模块意义相同时，唯一区别标识制造商的车辆部件或组件。序列号始终未字母
数字（允许的字符：0 至 9，A 至 Z）。每个单独组件的序列号长度在组模块目录中指定。 
 
和单个部件不同，定义的生产批次也可以被标记和区分开。在这种情况下，序列号将对应一
个批次号。 
 
序列号序列不会在零件号更改或车辆组件使用情况下结束。换句话说，序列号必须独立于零
件号。 
示例：  
 
邮箱=模块“065” 
 
 
 
 
供应商代码=“KTO”， 书面空白符“KTO” 
 
 
 
 
➝纯文本的数据序列：*065 KTOxxxxxxxP* 
 
模块数据在整个保留期间（≥15 年）以及供应给大众汽车平白的“KTO”供应商的所有燃油箱
模块数据 
模块编号 
制造商代码 
序列号 
CD 


### 第 11 页
必须保持唯一。在这种情况下，相应的技术设计和使用不相关。 
 
除非另有说明，并且只要满足该要求，供应商可以为期序列号选择组成。取决于数字的技术
方式，会产生大同大小的数字范围（参见图 4）。 
计数方法 
标记中的数字范围 
条目最大值（十进制） 
十进制，7 位字符 
0 000 001 .. 9 999 999 
1 .. 9 999 999 
字母数字，5 位字符 
00 001 .. ZZ ZZZ 
1 .. 60 466 175 
字母数字，6 位字符 
000 001 .. ZZZ ZZZ 
1 .. 2 176 782 335 
字母数字，7 位字符 
0 000 001 .. Z ZZZ ZZZ 
1 .. 78 364 164 095 
图 4：计数方法示例 
 
字母数据计数方法对应于使用数字和大写字母的 36 个数字系统。十进制和字母数字计数方
法的值比较： 
（1）an = （1）dec，..，
（9）an = (10) dec,..,(Z) an = (35) dec, (10) an = (36) dec,.., (ZZ) an = (1295) 
dec 
 
如果使用较小的数字范围，则可以使用供应商定义的可用代码（例如制造系统的代码，零件
代码）来填写序列号中剩余的空闲空间。 
 
所选的计数方法必须安全地超过预期的模块综述。推荐的计数方法是：字母数组，7 位字符。 
4.1.4 43 模校验位计算 
见第 3.6 节 
4.2 应用示例 
模块数据的标准数据序列由 15 个字符组成；请参见图 5.始终必须从“BG 在线”组模块目录收
集 准 确 的 语 法 。 有 关 数 据 序 列 的 信 息 可 以 从 群 组 生 成 状 态 文 件 （ BZD ） 获 取
（<EMAIL>） 
 
图 5 


### 第 12 页
5 使用生成状态文件标记零件安装检查 
必须使用 2-D 代码进行带有生成状态文件的零件安装检查标记。 
 
本节末尾介绍了应用示例。 
5.1 用于零件安装检查和生成状态文件的 2-D 代码的数据序
列 
二维码的数据序列由六个数据字段组成；见图 6. 
# 
零件
号 
# 
零件
编码 
# 
DUNS
号 
# 
生产
日期 
* 
模块
数据 
* 
= 
附加
信息 
图 6 
 
必须严格遵守各个数据字段的顺序。试图指定整个字符串中的绝对位置是没有意义的，因为
默写数据字段可能是可选的和/或具有可变长度，这取决于所讨论的用例。 
 
注 5：对于尚未推出的零件，必须考虑取消本标准在 2012-06 发布的使用代码。 
5.1.1 零件号 
零件号是包含识别和分类信息的复合代码。它的基本结构由 9 到 11 个字符组成。 
 
在用于零件安装检查和生成状态文件的 2-D 代码中，总是使用 11 个字符的零件号，后跟三
个字符的颜色代码。写入 14 个字符的数据字段时没有分隔据点或空格，左对齐。未使用的
字符用空格填充；见图 7. 
 
图 7 
注 6：VW 01098 描述了在大众集团中使用的零件号结构。 
5.1.2 零件编码 
“零件编码”数据字段是可选的。如果有问题的用例不需要这些信息，那么这些信息会被忽略，
而不被替换，并且数据字段的长度为 0.必须始终使用分隔符（#），即使该字段为空。零件代
码包含车辆部件上下文相关的信息。取决于相应的车辆组件类型，该数据字段的结构可以使
用各种格式和定义。例如，见图 8. 
 
 


### 第 13 页
车辆零件类型 
格式 
描述 
电池 
KKKAAAT 第 1 至第 3 位字符， K=电池容量 
第 4 至第 6 位字符，A=低温测试的电流 
第 7 位字符，T=电池技术 
第 8 至第 9 位字符，空格 
组件，普通，无软件 
Hhhh 
第 1 位字符，字母“L”指硬件版本 
第 2 至第 4 位字符，HW 版（参见 VW 80125，
RDID:FIA3） 
组件，普通，有软件 
HhhhSssss 
第 1 位字符，字母“L”指硬件版本 
第 2 至第 4 位字符，HW 版（参见 VW 80125，
RDID:FIA3） 
第 5 位字符，字母“S”指软件版本 
第 6 至 9 位字符，SW 版（参见 VW 80125，
RDID:F189） 
图 8 
 
此数据字段的内容必须由相应的开发人员指定并记录在零件图纸中。 
5.1.3 DUNS 号 
“DUNS 号”数据字段是可选的。如果有问题用例不需要这些信息，那么 DUNS 号会被忽略
而不被替换，而数据字段的长度为 0.必须始终使用分隔符（#），即使该字段为空。DUNS
号是一个国际标准化的供应商号码。数据字段有 9 个字符；见图 9. 
 
图 9 
注 7：不允许使用大众汽车供应商编号（KRIAS 系统）或其他代码编号。 
5.1.4 生产日期 
“生产日期”数据字段是可选的。如果有问题的用例不需要这些信息，那么生产日期会被忽略
而不被替换，并且数据字段的长度为 0.必须始终使用分隔符（#），即使该字段为空。生产日
期表示组件技术性完成时间（准备安装或准备交付）。 
 
“生产日期”数据字段始终有 6 位数字和 DDMMYY（日日月月年年）格式；参见图 10.如果
特定日期的信息不可用，则必须始终输入每周的第一个工作日。 
 
图 10 


### 第 14 页
5.1.5 模块数据 
如果标记仅用于部件标识目的，则模块数据可以省略而不用替换。在这种情况下，字符“*”
也被省略。 
 
第 4.1 节描述了模块数据的语法。 
 
注 8：校验位是专门为模块数据计算的。二维码的剩余数据字段不用于计算校验位。 
5.1.6 附加信息 
可有开发商或供应商自由获得。 
5.1.7 数据序列中的特殊字符 
“#”字符放置在数据序列的前四个数据字段（分隔符）之间。即使字段中没有字符，也必须
始终使用分隔符。 
 
“*”字符放置在数据序列中模块数据的开始和结尾处。如果没有模块数据，则不适用这些特
殊字符。 
 
“=”字符终止了生成状态文件和安装检查（终止符）的数据序列。 
5.2 2-D 代码示例 
5.2.1 用于生成状态文件的部件安装检查的标准数据内容 
用于生成状态文件的零件安装检查的标准数据包含“具有颜色代码的零件号”和“模块数据”
数据字段（在该示例中使用标准的 15 字符语法）。“零件代码”、“DUNS 编号”、和“生产日
期”数据字段为空白。 
 
与标准数据内容的偏差（见图 11）只有在合理的情况下才允许。 
每一个偏差都必须与参与过程中的各方讨论并商定。 
 
图 11 
使用第 6.2 节中的条件时，会得到以下代码符号；见表 1. 
 
 
 


### 第 15 页
表 1 
类型 
二维码示例 
条形码示例 
条码示例 
 
 
大小 
22x22 
16x36 
大小，单位：mm 
无空白区 
11.22x11.22 
8.16x24.48 
大小，单位：mm 
有空白区 
15.22x15.22 
12.13x28.48 
5.2.2 零件安装检测的最小数据内容 
如果组件几何上不允许使用标准数据内容，标准数据内容有可能会有所偏离。与标准数据内
容的偏差必须与流程中涉及到的各方进行讨论并达成一致。 
 
零件安装检查的最小数据内容不含生成状态文件，仅包含“零件号”数据段，仅包含 14 个字
符。“零件代码”、“DUNS 号”和“生产日期”数据字段没有任何内容，并且“模块数据”数据字
段被省略；见图 12. 
 
图 12 
使用 6.2 节中的条件时会得到以下代码符号; 见表 2。 
 
表 2 
类型 
二维码示例 
条形码示例 
条码示例 
 
 
大小 
18x18 
12x36 
大小，单位：mm 
无空白区 
9.18x9.18 
6.21x13.26 
大小，单位：mm 
有空白区 
13.18x13.18 
10.21x17.26 


### 第 16 页
5.2.3 生成状态文件最小数据内容 
如果几何图形使其绝对必要，则可能会偏离“仅限生成转台文件”用例的便准数据内容。与标
准数据内容的偏差必须与流程中涉及的各方进行讨论并达成一致。在这种情况下，第 4.1 节
中描述的生成状态文件数据序列被些微数据二维码；请参见图 13.该二维码的读取结果与代
码 39 的读取结果完全相同。 
 
图 13 
使用 6.2 节中的条件时会得到以下代码符号; 见表 3。 
 
表 3 
类型 
二维码示例 
条形码示例 
条码示例 
 
 
大小 
16x16 
12x26 
大小，单位：mm 
无空白区 
8.16x8.16 
6.21x13.26 
大小，单位：mm 
有空白区 
12.16x12.16 
10.21x17.26 
 
注 9：模块数据的语法在组模块目录中指定。 模块数据具有用于识别制造商的四字符代码
（制造商代码）。 按照 VW 10540-1 制造商的代码有三个字符，并且必须使用前导空白字符。 
联系方式：<EMAIL> 
5.2.4 使用生成状态文档检查部件安装的最大数据内容 
根据具体的使用情况，数据内容可以一直扩展到最大数据内容限制。 在这种情况下，所有
数据字段，包括 额外的数据，填写完成。 “零件代码”和“附加数据”数据字段的长度将取决
于具体使用情况; 见图 14。 
 
图 14 
代码符号的大小将取决于具体的数据内容。 使用上面的图 14，其中有 65 个字符，在使用
6.2 节中的条件时会产生以下代码符号; 见表 4。 
 
 
 
 


### 第 17 页
表 3 
类型 
二维码示例 
条形码示例 
条码示例 
 
 
大小 
32x32 
16x48 
大小，单位：mm 
无空白区 
16.32x16.32 
8.16x24.48 
大小，单位：mm 
有空白区 
20.32x20.32 
12.16x28.48 
6 标签设计和布局（1-D 代码 2-D 代码） 
6.1 条形码的代码符号（1-D 码） 
生成 1-D 代码时虑以下要求： 
- 必须使用符合 ISO / IEC 16388 的代码 39 
- 符合 DIN EN ISO / IEC 15416 的整体符号质量必须为 3.5 或更高 
- 模块宽度 x（见附录 A）：约 254 毫米 
- 模块宽度比例：至少 1：2.5 
- 间隙宽度比：与模块宽度比相同 
- 打印分辨率：至少 300 dpi 
- 空白区域：约每面 3 毫米 
- 条码高度：约 10 毫米 
- 校验位计算（自动）不适用 
- 这些要求产生的符号大小约为。对于 15 个字符的数据序列，为 63 x 10 毫米。 
- 偏差必须与有关各方讨论并商定 
6.2 矩阵码的代码符号（2-D 代码） 
生成二维码符号时必须考虑以下要求： 
6.2.1 当用于生产车辆的工厂时 
- 必须使用数据矩阵代码 
- 符合 ISO / IEC TR 29158（以前称为 AIM DPM-1 2006）的整体符号质量必须为 3 或更好。 
必须确保整个过程链到安装位置的符号质量 
- ECC 200 纠错 


### 第 18 页
- 模块尺寸 x（见附录 A）：至少 0.50 毫米 
- 打印机分辨率：300 dpi 或更高 
- 安静区每面至少 2 毫米; 只有在与所有相关方达成一致的情况下才允许进入一个安静的小
区。 
- 使用自动功能创建的矩阵大小和字符 
- 这些要求导致符号大小约为 20 毫米 x 20 毫米，用于完整填写数据序列。组标记会产生更
大的符号 
- 偏差必须与有关各方讨论并商定 
6.2.2 当用于生产发动机的工厂时 
- 必须使用数据矩阵代码 
- 符合 ISO / IEC TR 29158（以前称为 AIM DPM-2006）的整体符号质量必须为 3 或更好。
必须确保整个过程链到安装位置的符号质量 
- ECC 200 纠错 
- 模块尺寸 x（见附录 A）：至少 0.50 毫米 
- 打印机分辨率：300 dpi 或更高 
- 安静区域的大小至少为模块大小的四倍 
- 使用自动功能创建的矩阵大小和字符 
- 对于 32 至 36 个字符的标准字符串，这些要求以及 20 x 20 点的大小会产生大约 10 mm x 10 
mm 的符号大小。 
- 当涉及不同供应商的相同部件时，必须选择完全相同的标记方法，尺寸和位置 
- 该位置必须确保代码在组件安装后始终保持清晰 
- 数据矩阵代码（DMC）制造商必须提供文件证明符合可读性质量要求 
- 由于缺乏空间而产生的偏差必须明确指出，并且必须与有关各方进行讨论和商定。 
6.3 纯文本信息（1-D 和 2-D 代码） 
除了代码符号外，标记必须包含纯文本信息。如果代码符号有缺陷且无法辨认，或系统发生
故障，则次信息用于手动输入数据。必须满足以下要求： 
- 
1-D 代码：纯文本必须显示整个数据序列，包括校验位 
- 
2-D 代码：如果 2-D 代码包含“数据模块”或“零件号”数据字段，则必须以纯文本显示。
或者，所有数据字段都可以显示为纯文本。 
- 
“*”字符被添加到“模块数据”字段的纯文本数据的开头和结尾 
- 
为了便于阅读，零件号纯文本信息必须在前端编号、中间组、后端编号和后缀之间留有
空白。 
- 
如果可能，字体必须是“Thesis TheSans”。如果不可能，则必须使用“Arial”。 
- 
字体高度必须至少 2 毫米。 
- 
更好的选择是让纯文本出现在条形码下方（纯文本可能在特殊情况下位置不同）。 
- 
为了区分数字“0”和字母“O”，零必须有一条直线（例如， ClearType 字体“Consolas”；
ASCII 值 048）。 


### 第 19 页
6.4 永久性连接到组件的单部件标签（1-D 和 2-D 代码） 
标签类型和布局必须考虑以下要求： 
- 
标签必须是自粘性的，贴上后不得以外脱落。 
- 
如果必要的话，标签应可以反转（打印样式）。 
- 
不允许有打印污点 
- 
基材：用于热转移印刷机的带有白垩涂层的金属化薄膜；RAL 9010（纯白色-包括条码
字段）；或者银灰色。 
- 
字体：黑色 
 
标签应用于车辆部件时，必须是的在部件安装之后数据可见且技术上可读。位置和方向必须
在工程图纸中指定。 
 
允许将现有标签用于其他目的，并将其用于此处所述的标记。这些标签的现有内容不得对这
里描述的标记的可用性产生负面影响（例如，由于其他类型的代码符号）。 
6.4.1 两部件或多部件标签（1-D 和 2-D 代码） 
永久性连接到组件的单部件标签必须补充一个或多个额外的可拆卸标签部分。这些在制造过
程中分离，例如贴在转让票上。至少要使用的最后一个科拆卸零件包含代码符号。所有其他
部分至少包含纯文本信息。上述对单部件标签的要求相应地适用。 
 
可拆卸的标签部分不能过宽或过高，例如，它们不得大于转让票上用于黏贴这些部分的区域。
要使用的标签大小取决于代码符号的大小，包括空白区和纯文本信息。 
 
推荐尺寸： 
- 
1-D 代码：15 个字符的数据序列（标准数据序列）80 毫米 x20 毫米 
- 
1-D 代码：21 个字符的数据序列（模块 005=引擎，006=传输）：月 100 毫米 x20 毫米 
- 
2-D 代码：二维码代码标签的推荐尺寸为 20 毫米 x30 毫米 
 
标签的材料和定位必须确保可以快速轻松地拆下适当的部分而不会损坏它们。可拆卸部分不
得无意脱落。 
6.5 ASSY 外表面标签（组标记） 
稍后将不可访问的车辆部件需要多部件标签： 
- 
如果在车辆工厂直接录入数据，则为 2 部分 
- 
如果数据车辆工厂中记录并转移，则为 3 部分 
 
在复杂的 ASSY 的制造过程中，可拆卸部分随后在约定位置（例如，座椅：坐垫框架的外
部面板）永久地古锭刀 ASSY 的外部。 


### 第 20 页
6.6 ASSY 转让票（组标记） 
如果不能在 ASSY 的外面铁标签，供应商应提供 ASSY 转运票。它必须随复杂的 ASSY 一
起提供，并在下游制造阶段读取。 
 
必须提供以下内容： 
- 
供应商名称和 ASSY 名称 
- 
在特定车辆交付的情况下：车辆代码或车辆识别号 
- 
如果交付的不是特定车辆：ASSY 的分配编号，ASSSY 转让票 
- 
安装在 ASSY 中的所有模块条形码，且无法访问 
- 
供应商的验收通知（印章等），证明所提供数据的正确性。 
ASSY 转让票是车辆转让票的一部分。必须以不会在运输过程中脱落或损坏的方式将其布局
到 ASSY 上。其内容、布局和流程必须与相应的车辆工厂商定。 
6.7 直接应用于材料中的标记（直接部分标记（DPM））1） 
如果技术要求或其他要求（例如使用寿命标记、造型要求）需要，则 2-D 代码可以直接可
在组件材料或组件上。对纯文本信息的要求也必须考虑并相应地予以满足。 
 
可用的标记技术： 
- 
激光打标 
- 
微冲击 
- 
电化学蚀刻 
- 
喷墨打印 
 
这些技术中国的每一种都适用于某些应用，这取决于部件的预期寿命和材料混合或生产量以
及环境影响造成的磨损。 
6.8 校验 
6.8.1 校验码 
条码必须按照 DIN EN ISO / IEC 15416 进行验证。符合 DIN EN ISO / IEC 15416 的整体符号
分类值必须为 3.5 或更高。 
6.8.2 验证 2-D 码 
在用正方形单元验证代码时必须应用 ISO / IEC TR 29158（对应于 AIM DPM-2006）。 ISO / 
IEC TR 29158 涉及直接标记 2-D 码; 必须将规格应用于标签上的 2-D 码。 整体符号分类必
须至少为“B”级。 
注 10：使用直接标记需要适当的阅读器（DPM 扫描仪）。这些标志必须与所有相关部门讨


### 第 21 页
论并达成一致（从流水线采购到售后服务——这是为了确保在集团的任何地方都可以读取代
码）。 
 
1） 第 6.7 节来源：Congex 公司的“读取直接标记——10 重要方面”和“新标准允许可靠验证
二维数据矩阵代码”——Cognex 公司高级总监兼业务部经理 Carl W. Gerst III 编写的白皮
书 ，ID 产品。 
7 组标记——预先记录复杂组件的数据 
对于复杂的 ASSY，在某些情况下，单个部件上标记的数据将不再可用（例如座椅中的安全
气囊模块）。 在这些情况下，必须在制作 ASSY 时预先记录数据，以确保数据在 ASSY 的
安装位置可用。 
组标记只能使用矩阵代码实现。 
7.1 生成状态文件组标记 
供应商通过读取单个模块数据集预先记录数据（参见 4.1 节）。这些单独的数据序列被组合
以形成组数据序列（参见图 15） 
 
图 15 
7.1.1 数据序列中的特殊字符 
“＆”字符链接两个数据序列（连续字符）。 
“=”字符终止构建状态文档（终止符）的数据序列。 
7.2 零件安装检查组标记 
供应商通过读取单个数据集预先记录数据（参见第 5.1 节）。 这些单独的数据序列被组合以
形成组数据序列（见图 16）。 
 
图 16 
对于该组数据序列，会创建一个新的标记，然后永久的粘贴在复杂 ASSY 的外部。 
 


### 第 22 页
只有在复杂 ASSY 中的模块在下游过程中与 ASSY 分离，才允许进行这种预记录。事实之
后，更改（例如更换模块）不能集成到组标记中。 
 
为各个标签指定的要求也相应地适用。标记必须符合以下要求： 
- 
组标记中的每个模块都需要纯文本信息。 
- 
“&”用作两个数据集之间的分隔符 
- 
最后一个数据集包含终止符“=” 
 
正方形数据矩阵码可以由最多 144 行 x144 列组成。相应地，可以表示 1982 个 ASCII 字符。
这最多可以将 23 个数据集合在一起。由于可用性原因，此数字限制为 20. 
7.2.1 数据序列中的特殊字符 
“＃”字符放置在数据序列的前四个数据字段（分隔符）之间。 即使字段中没有字符，也必
须始终使用分隔符。 
“*”字符放置在数据序列中模块数据的开始和结尾处。 如果没有模块数据，则不使用这些特
殊字符。 
“＆”字符链接两个数据序列。 
“=”字符终止安装检查（终止符）的数据序列。 
7.3 数据交换 
供应商的制造区域被视为内部上游制造过程。 
供应商记录复杂 ASSY 中包含的所有模块的数据，并将此数据提交给相应的车辆工厂。 
 
只有在下游过程中不可能将数据分配给错误的组件或车辆，或者存在可用于纠正数据的适当
应急程序时，才允许进行预先记录。 
 
数据格式和整个技术实施均受双边协议约束。 
8 使用 RFID 标签生成状态文件标记和零件
标识 
本标准描述了用于生产零部件安装检查和/或生成状态文件的 RFID 数据结构。 VW 01067
描述了原型组件的标记。这两个用例中的每一个都已经准备好了。 
 
带有 RFID 标签标记的车辆组件必须将标记数据显示为纯文本;这可以在读取失败的情况下
手动输入数据。明文至少表示模块数据的内容以及最重要的生产特征（例如零件号，代，生
产时间），并且可以通过例如标签来实现。应用纯文本信息的要求与使用 1-D 代码的标记要
求类似。 


### 第 23 页
必须立即在安装地点记录RFID标签数据，以确保在读取错误的情况下可以手动输入数据（必
须可以查看零件或在必要时移除零件）。 
8.1 技术实施 
VW 01067 描述了所使用的 RFID 技术。 
 
允许的字符可以从 ASCII-Character-to-6-Bit-Compaction 替换表中收集; 参见 ODETTE 5510
和 JAIF Global RFID。 用于校验位的字符“$”和“％”已作为控制字符保留在 6 位代码中，因
此不能用于校验位。 因此，在写入标签之前，必须将其转换为允许的特殊字符“@”和“\”。 
 
标签被读取后，字符被转换回来：“@”变成“$”，“\”变成“％”。 
8.1.1 唯一项目标识符（UII-bank 01） 
VW 01067 和 VW 01067 RFID 数据矩阵教程描述了 UII 的内容和语法。 
8.1.2 用户存储器（UM-bank 11） 
用户存储器包含构建状态文档数据，就像数据矩阵代码一样（请参阅第 5.2 节）; 见图 17。 
8.2 用户存储器数据内容示例 
以下是对 4 种数据内容变体的描述，其内容具有与 2-D 代码标记相似的语法。其优点是，
这不需要对建造车辆的工厂的系统进行任何修改。 
 
在特殊情况下，允许偏离这些定义的数据内容变体。 部件信息也可以写入 UII，具体取决
于可用 RFID 标签的大小。在这种情况下，必须与所有相关方讨论并商定具体实施。 
8.2.1 用于生成状态文档的部件安装检查的标准数据内容 
用于生成状态文件的零件安装检查的标准数据包含“带有颜色代码的零件号”和“模块数据”
数据字段; 见图 17（见 5.2.1 节）。 
存储区域 
内容 
值 
位数 
用户存储器（bank 11） 
 
 
256 
 
DSFID+前驱十六进制 
0346 
2 
 
DI 模块数据 
Z 
1 
 
分隔符 
# 
 
 
零件号 
VVVHHHEEEXX 
 
 
颜色指数 
FFF 
 
 
分隔符 
# 
 


### 第 24 页
 
分隔符 
# 
 
 
分隔符 
# 
 
 
分隔符 
* 
 
 
模块数据 
BGR HHHLLLLLLLP 
 
 
分隔符 
* 
 
 
终止符 
= 
 
 
数据串终结 
！ 
1 
 
填充<空格> 
 
 
图 17 
 
数据标识符（DI）使用：Z; 见 ANSI MH 10.8.2 
 
构建状态文档数据的数据块包含 36 个字符;在 6 位编码的情况下，使用 240 位（包括控制字
符）。 
8.2.2 零件安装检查最小数据内容 
部件安装检查的最小数据内容不包含生成状态文件，仅包含“带色码的零件号”数据字段（请
参见第 5.2.2 节）; 见图 18。 
存储区域 
内容 
值 
位数 
用户存储器（bank 11） 
 
 
256 
 
DSFID+前驱十六进制 
0346 
2 
 
DI 模块数据 
Z 
1 
 
分隔符 
# 
 
 
零件号 
VVVHHHEEEXX 
 
 
颜色指数 
FFF 
 
 
分隔符 
# 
 
 
分隔符 
# 
 
 
分隔符 
# 
 
 
终止符 
= 
 
 
数据串终结 
！ 
1 
 
填充<空格> 
 
 
图 18 
数据标识符（DI）使用：Z; 见 ANSI MH 10.8.2。 
 
构建状态文档数据的数据块包含 19 个字符;在 6 位编码的情况下，使用 138 位（包括控制字
符）。 
8.2.3 生成状态文件最小数据内容 
生成状态文件的最小数据内容仅包含“生成状态文件”数据字段（请参见第 5.2.3 节）; 见图
19。 


### 第 25 页
存储区域 
内容 
值 
位数 
用户存储器（bank 11） 
 
 
256 
 
DSFID+前驱十六进制 
0346 
2 
 
DI 模块数据 
Z 
1 
 
模块数据 
BGR HHHLLLLLLLP 
 
 
数据串终结 
！ 
1 
 
填充<空格> 
 
 
图 19 
据标识符（DI）使用：Z; 见 ANSI MH 10.8.2。 
 
构建状态文档数据的数据块包含 19 个字符;在 6 位编码的情况下，使用 138 位（包括控制字
符）。 
 
注 11：模块数据的语法在组模块目录中指定。通常情况下，采购部件具有三字符制造商代
码（H），符合 VW 10540-1 标准，并具有领先的空白。联系方式：<EMAIL> 
8.2.4 使用生成状态文件检查部件安装的最大数据内容 
当使用生成状态文件进行零件安装检查的最大数据内容时，所有数据字段将被填写。请参阅
第 5.2.4 节）“部件代码”和“附加数据”数据字段的长度取决于具体使用情况; 见图 20。 
存储区域 
内容 
值 
位数 
用户存储器（bank 11） 
 
 
256 
 
DSFID+前驱十六进制 
0346 
2 
 
DI 模块数据 
Z 
1 
 
分隔符 
# 
 
 
零件号 
VVVHHHEEEXX 
 
 
颜色指数 
FFF 
 
 
分隔符 
# 
 
 
分隔符 
# 
 
 
零件编码 
TTTTTTTTT 
 
 
分隔符 
# 
 
 
DUNS 号 
DDDDDDD 
 
 
分隔符 
# 
 
 
生产日期 
DDMMYY 
 
 
分隔符 
* 
 
 
模块数据 
BGR HHHLLLLLLLP 
 
 
分隔符 
* 
 
 
终止符 
= 
 
 
附加信息 
ZZZZZZZ 
 
 
数据串终结 
！ 
1 
 
填充<空格> 
 
 
图 20 
 


### 第 26 页
数据标识符（DI）使用：Z; 见 ANSI MH 10.8.2。 
 
生成状态文件数据的数据块包含 66 个字符;在 6 位编码的情况下，使用 396 位（包括控制字
符）。 
9 参考文件 
本标准引用的以下文件对其应用是必需的。 
 
一些引用文件是德文原件的翻译。 德文术语在这些文件中的翻译可能与本标准中使用的术
语不同，导致术语不一致。 
 
标题以德文给出的标准可能仅以德文提供。 其他语言的版本可能来自颁发该标准的机构。 
 
VW 01067 
 
 
 
将自动识别码用于唯一对象标记；使用光学编码方法和/或射频 
 
 
 
 
 
 
识别（RFID） 
的序列化 
VW01067 RFID 二维码教程  
自动识别教程 - 唯一对象标记 
VW 01098 
 
 
 
零件号系统 
VW 10500 
 
 
 
公司名称、零件标记使用指南 
VW 10540-1  
 
 
汽车零件制造商代码 
VW 10540-3  
 
 
制造商代码；颁发范围用于海外工厂的制造商代码 
WSK.013.290.E 
 
 
电子控制单元识别板 
ANSI MH 10.8.2  
 
数据识别符和应用识别符标准 
DIN EN ISO/IEC 154 16 
信息技术——自动识别和数据采集技术——条码打印质量测试
 
 
 
 
 
 
规范；线性符号 
ISO/IEC 16388 
 
 
信息技术——自动识别和数据采集技术——Code39 代码符号规
 
 
 
 
 
 
范 
ISO/IEC TR 29158  
 
信息技术——自动识别数据采集技术——直接标记零件（DPM）
 
 
 
 
 
 
质量指南 
JAIF 全球 RFID 
 
 
物品等级标准，2011-08-10 
ODETTE 5510 
 
 
RFID 用于追踪汽车行业的零部件，表 2 
10 参考文献 
[1]  
 
VW 80115 KWsP 2000 服务识别电子控制单元，版本 3.0 
[2]  
 
VW 80125 电子车辆系统识别；版本 2.3 
[3]  
 
数据矩阵符号规范，例如：AIM DPM-2006，自动识别和移动协会 
[4]  
 
大众汽车模块目录 BG 在线系统 
[5]  
 
读取直接标记——10 个重要方面，Cognex 
[6]  
 
新标准允许可靠验证二维数据矩阵代码，Carl W. Gerst III, Cognex 
 


### 第 27 页
附录 A（告知） 
 
 
 
 
 
模块宽度 x（条形码）  
 
模块尺寸 x（二维码） 
图 A.1——条码符号的模块宽度/模块尺寸（X） 
 
图 A.2——标准标签 
 
图 A.3——带条形码的单部件标签 


### 第 28 页
 
图 A.4——多部件标签 
 
图 A.5——特殊应用类型 


### 第 29 页
 
图 A.6——组标识（示例含 8 个模块） 

