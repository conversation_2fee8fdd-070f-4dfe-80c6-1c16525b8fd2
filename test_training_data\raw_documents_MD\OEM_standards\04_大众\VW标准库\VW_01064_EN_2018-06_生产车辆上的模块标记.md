# VW_01064_EN_2018-06_生产车辆上的模块标记.pdf

## 文档信息
- 标题：
- 作者：
- 页数：27

## 文档内容
### 第 1 页
Group standard
VW 01064
Issue 2018-06
Class. No.:
01152
Descriptors:
BG-Online, BZD, Code 39, RFID, bar code, build status documentation, component marking, data
matrix, marking, module, modules catalog, parts installation check, serial number, traceability
Module Marking on Production Vehicles
Build Status Documentation – Coding on Mechanical Vehicle Parts
Previous issues
VW 01064: 1996-09, 1999-05, 2001-06, 2001-11, 2002-03, 2002-09, 2003-04, 2003-12, 2007-11,
2008-05, 2010-09, 2011-07, 2013-11, 2015-01
Changes
The following changes have been made to VW 01064: 2015-01:
–
Section 1 "Scope": 1st paragraph changed (vehicles with a general operating license (ABE)
deleted); 3rd paragraph changed (Porsche and MAN added)
–
Section 2.2 "Build status documentation (BZD)" revised
–
Section 2.2.1 "Build status documentation of vehicles at CP 8" revised and reference to batch
BZD deleted
–
Section 2.2.2 "BZD – Build status documentation of component" added
–
Section 2.4 "Module data": last paragraph revised
–
Section 3.3 "Data recording": 1st sentence of 3rd paragraph deleted
–
Section 4.1 "BZD – 1-D code data sequence" revised
–
Section 4.1.2 "Manufacturer’s code": blank space added to "ZFS"
–
Section 4.1.3 "Serial number": 2nd paragraph changed, 3rd paragraph expanded, 4th para‐
graph changed, blank space added to "KTO"
–
Section 4.2 "Implementation examples": Figure 5 changed
–
Section 5.1.3 "DUNS number": Figure 10 changed
–
Section 5.1.6 "Additional data": section heading changed, 2nd paragraph added
–
Section 5.1.7 "Special characters in the data sequence": last paragraph added
–
Section 5.2.1 "Standard data content for parts installation checks with build status documenta‐
tion": Figure 11 changed
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version controls.
Page 1 of 27
Technical responsibility
The Standards department
K-GQK-Z/3
Armin Witschi
Tel.: +49 5361 9 18168
K-ILI/5 Uwe Stüber
K-ILI
Tel.: +49 5361 9 29063
Uwe Wiesner
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2018-02


### 第 2 页
Page 2
VW 01064: 2018-06
–
Section 5.2.3 "Minimum data content for build status documentation": Figure 13 changed,
note 9 corrected
–
Section 5.2.4 "Maximum data content for parts installation checks with build status documen‐
tation": Figure 14 changed
–
Section 6.2.1 "When used in vehicle assembly plants" added, former content moved to
Section 6.2.2
–
Section 6.3 "Plain text information (1-D and 2-D codes)": list changed, former 5th bullet point
deleted, last bullet point changed
–
Section 7 "Group marking – Pre-recording data for complex assemblies" restructured and ex‐
panded
–
Section 8 "BZD – Marking and part identification with RFID tags": content deleted, reference to
VW 01067 added
–
Section 10 "Applicable documents" updated
–
Section 11 "Bibliography" updated
–
Appendix A "Examples" updated
Contents
Page
Scope ......................................................................................................................... 3
General notes, definitions .......................................................................................... 3
Traceability ................................................................................................................. 3
Build status documentation (BZD) ............................................................................. 3
Module ....................................................................................................................... 5
Module data ............................................................................................................... 5
Part identification (installation check) ......................................................................... 5
Vehicle component ..................................................................................................... 5
Complex assembly ..................................................................................................... 6
General requirements for marking and technical requirements for data
processing .................................................................................................................. 6
General requirements ................................................................................................ 6
Types of marking ........................................................................................................ 6
Data recording ............................................................................................................ 7
Standard configuration ............................................................................................... 7
Technical requirements for data processing using 1-D and 2-D codes ..................... 7
Modulo 43 check digit calculation .............................................................................. 8
BZD – Marking with a 1-D code (bar code, Code 39) ................................................ 9
BZD – 1-D code data sequence ................................................................................. 9
Implementation examples ........................................................................................ 11
Marking for parts installation checks with build status documentation ..................... 11
Data sequence of 2-D code for parts installation checks and build status
documentation .......................................................................................................... 11
Implementation examples for 2-D codes .................................................................. 14
Label design and layout (1-D code and 2-D code) ................................................... 17
Code symbol for bar code (1-D code) ...................................................................... 17
Code symbol for matrix code (2-D code) ................................................................. 18
Plain text information (1-D and 2-D codes) .............................................................. 19
One-part label permanently attached to the component (1-D and 2-D codes) ........ 20
Labeling outside on the ASSY (group marking) ....................................................... 20
ASSY transfer ticket (group marking) ....................................................................... 21
1
2
2.1
2.2
2.3
2.4
2.5
2.6
2.7
3
3.1
3.2
3.3
3.4
3.5
3.6
4
4.1
4.2
5
5.1
5.2
6
6.1
6.2
6.3
6.4
6.5
6.6


### 第 3 页
Page 3
VW 01064: 2018-06
Marking applied directly in the material (DPM – Direct Part Mark) ........................... 21
Verifications .............................................................................................................. 21
Group marking – Pre-recording data for complex assemblies ................................. 22
Special characters in the data sequence ................................................................. 22
Data exchange ......................................................................................................... 23
BZD – Marking and part identification with RFID tags .............................................. 23
BZD – Marking of JIS components ........................................................................... 23
Applicable documents .............................................................................................. 24
Bibliography ............................................................................................................. 24
Examples ................................................................................................................. 26
6.7
6.8
7
7.1
7.2
8
9
10
11
Appendix A
Scope
This Volkswagen standard (VW) describes the requirements for the exterior marking of vehicle
components that are subject to build status documentation (BZD) and are being used for produc‐
tion vehicles and for parts within an assembly (ASSY) that are subject to BZD (e.g., engines). The
data coded in the marking is used to document and trace vehicle components ("build status docu‐
mentation") and to identify parts ("parts installation check") within the Volkswagen Group.
Marking as per this standard does not replace part marking as per VW 10500.
This standard is addressed to:
–
Developers tasked with specifying a marking subject to BZD
–
Quality assurance personnel tasked with performing a sample inspection on a component
marking subject to BZD
–
Suppliers of vehicle parts tasked with implementing a marking subject to BZD
–
Suppliers of complex ASSYs tasked with pre-recording module data, if applicable
–
Manufacturing planning personnel tasked with planning the methods and procedures for data
recording
Volkswagen AG BZD applies to the following Volkswagen Group brands: Volkswagen, Audi, Ško‐
da, SEAT, Volkswagen Commercial Vehicles, Bentley, Lamborghini, Porsche, and MAN.
NOTE 1: This marking does not apply to diagnostics-enabled components. The markings for
electronic control units (ECUs) are described in workshop sketch WSK.013.290°E.
General notes, definitions
Traceability
Traceability refers to the ability to determine when, where, and by whom a product or marketable
good was obtained, produced, processed, stored, transported, used, or disposed of. This route and
process tracking is also referred to as "tracing." A distinction is made between downstream tracing
(from the producer to the consumer) and upstream tracing (from the consumer to the producer).
Build status documentation (BZD)
The topic of build status documentation is divided into 2 basic sections:
–
Build status documentation of vehicles at checkpoint (CP) 8
–
Build status documentation of component
1  
2  
2.1  
2.2  


### 第 4 页
Page 4
VW 01064: 2018-06
Build status documentation of vehicles at CP 8
Build status documentation of vehicles at CP8 is an established process and is defined by the fol‐
lowing Process Standards:
PS_1.4_999_1939_04 "Basisliste aktualisieren (Updating the Base List)"
PS_1.4_999_1939_05 "Festlegung und Umsetzung des BZD-Umfangs im Produktteam (Defining
and Implementing the BZD Scope in the Product Team)"
PS_1.4_999_1430_03 "Bauzustandsdokumentation durchführen (Carrying Out Build Status Docu‐
mentation)"
The data coded in the marking is used to document and trace vehicle components ("build status
documentation") within Volkswagen AG. BZD is used to establish a clear link that makes it possible
to identify the correspondence between a vehicle identification number (VIN) and certain character‐
istics of a component. These characteristics include manufacturer and serial numbers, part num‐
bers, and ECU hardware and software versions. This makes it possible, in the event of a warranty
claim (recall), to precisely determine which VINs are involved.
Figure 1 shows the 3 basic courses of action, e.g., in the event of a recall:
Figure 1 – Basic courses of action in the event of a recall
BZD – Build status documentation of component
The new process of build status documentation in the component is an extension to build status
documentation of vehicles at CP8.
In vehicle BZD, certain ASSYs are recorded up to CP8 and assigned to the VIN. Component BZD
provides these ASSYs with recorded data on individual parts and subassemblies (sub-ASSYs).
An explicit decision must be made for each component as to whether component marking is gui‐
ded by the specifications of this standard or by the Process Standard for component build status
documentation.
In this sense, section 4 and section 5 of this document apply to component build status documen‐
tation only to a limited extent.
Special circumstances for individual applications of component BZD are described in section 10 of
this document.
2.2.1  
2.2.2  


### 第 5 页
Page 5
VW 01064: 2018-06
Module
A vehicle component or assembly that is subject to marking as per this Group standard. Each mod‐
ule is identified by means of a unique module number.
Module data is defined in the "BG-Online" system. Information regarding mandatory marking can
be found in drawings and Technical Guideline for Documentation (TLD) sheets.
Module data
Module data are those data portions of the BZD marking data that are documented for purposes of
traceability.
The unambiguous identification of vehicle components is based on the availability of the following
BZD data ("module data"):
Module number → Type of vehicle component
Manufacturer's code → Manufacturer and place of manufacture
Serial number → Vehicle component
Check digit → Modulo 43 algorithm
This data subject to BZD is used as a reference to specific vehicle components, is recorded during
production in a vehicle-specific manner, and is stored in the long-term vehicle archive at Volks‐
wagen AG. This allows for a precise determination of the vehicles actually affected in the event of
irregularities.
The syntax of the module data is described in the Group Modules Catalog "BG-Online." The maxi‐
mum length of the module data is 30 characters. External suppliers must contact the BZD Office
via a Group employee who is also a relevant part owner.
Contact via: <EMAIL>
Part identification (installation check)
In addition to the aforementioned module data, a marking may include additional information re‐
garding the vehicle component:
–
Group part number (VW 01098)
–
DUNS number (Data Universal Numbering System)
–
Date of manufacture
–
Part version
This data is not intended for archiving purposes. Instead, it is meant for use during the ongoing
manufacturing process, if necessary, in order to verify that a vehicle component has been installed
correctly (technical design, age, manufacturer).
Vehicle component
Vehicle component or assembly with a part number that references the exact corresponding tech‐
nical design. If applicable, the part number is the basis for an installation check.
2.3  
2.4  
2.5  
2.6  


### 第 6 页
Page 6
VW 01064: 2018-06
Complex assembly
Assembly that contains several modules (e.g., seat ASSY: contains the side airbag, backrest trim
cover airbag, sensor mat, seat belt buckle with sensor). If the modules' individual data will no lon‐
ger be accessible in the finished ASSY, the data must be pre-recorded (see section 7).
General requirements for marking and technical requirements for data processing
General requirements
The marking is a release-relevant component property and must be taken into account during the
sample inspection process. The following must be checked:
–
Ability of the data to be recorded under production conditions
–
Content of the applied data sequence
–
Type of marking on the component and its application
–
Retention period ≥ 15 years
The marking is checked during the sample inspection process. For this purpose, the supplier/man‐
ufacturer provides components that are marked the same way as they are during production.
The supplier/manufacturer ensures that the marking requirements described here are met during
ongoing production. This can be ensured with random-sample tests, for example.
The retention period is at least 15 years from the moment the data is created (equivalent to Classi‐
fication System for Documents (CSD) class 7.2). The supplier/manufacturer must ensure that the
data set remains unique for the entire retention period (≥ 15 years). It must document important,
quality-relevant individual information concerning the marked vehicle component (e.g., batch of raw
materials used, manufacturers of purchase parts used, testing and setting values, place of manu‐
facture and system), establish a clear to the corresponding reference data, and archive the infor‐
mation. If necessary, this will then make it possible to obtain clear information regarding the quality
of functions, manufacturing, and materials.
If the supplier manufactures an ASSY containing a component subject to BZD (e.g., fuel supply
module inside the fuel tank ASSY), the supplier must recording the data for the component subject
to BZD, add the data to the data of the ASSY, and retain this data in its documentation for at least
15 years. This also applies to ASSYs made up of an electronic control unit (ECU) and a mechani‐
cal component (e.g., headlamp with control module).
If the requirements for marking are not met, the affected components are considered faulty and
might not be usable.
Types of marking
The goal is to recording data quickly and in a cost-effective manner during the manufacturing proc‐
ess. There are 3 possible marking types that can be used. They are as follows:
– 1-D code (bar code) → Code 39 is used; see section 4
– 2-D code (matrix code) → Data matrix code (DMC) is used; see section 4
– Radio Frequency Identification (RFID) tag → See section 8
Using RFID tag markings is advisable if partially or fully automated data recording is required,
marking with labels is problematic, or vehicle components are already equipped with an RFID tag.
2.7  
3  
3.1  
3.2  


### 第 7 页
Page 7
VW 01064: 2018-06
NOTE 2: The use of RFID tag markings must always be agreed upon with the manufacturing
plants in terms of both technical and organizational requirements.
Data recording
Data is recorded during the vehicle assembly process or at the relevant pre-assembly areas. A dis‐
tinction must be drawn between the following data recording procedures:
Direct entry:
The data is entered immediately at the point of fitment for the vehicle part.
A one-part label permanently attached to the component, or a direct mark‐
ing, is read directly by means of a scanner.
Transfer ticket:
Out of a label with 2 sections, the detachable and self-adhesive section is
affixed to a designated field on the transfer ticket. The data is entered by
means of a scanner reading the transfer ticket content at downstream se‐
quence points.
Scanning:
The data is recorded and entered directly at the point of fitment for the vehi‐
cle part, or at sequence points that follow soon after by reading the RFID
tags.
Standard configuration
The standard configuration for marking and data recording is as follows:
–
Marking with 2-D code on a label
–
Data recorded at point of fitment
In comparison to the 1-D code, the 2-D code has additional potential application options (installa‐
tion check); high reading accuracy; requires less space; and requires less label material – or none
in the case of a direct marking.
In justified cases, a configuration deviating from the standard configuration can be used. Combin‐
ing different types of markings is possible if required due to the technical conditions involved in da‐
ta recording.
Technical requirements for data processing using 1-D and 2-D codes
Only the ASCII characters listed in figure 2 are permissible for the data sequences described be‐
low:
3.3  
3.4  
3.5  


### 第 8 页
Page 8
VW 01064: 2018-06
Legend
-
Excluded
(1)
Unrestricted
(2)
N/A
(3)
The "#" character is placed between each of the first 5 data fields in a data sequence
(separator). Separators must always be used, even if there are no characters in the
field.
(4)
The "&" character links 2 data sequences (continuation character, for group markings
only).
(5)
The "*" character is placed at the beginning and end of the module data in the data se‐
quence. If there is no module data, these special characters are not used either.
(6)
The "=" character terminates the data sequence (terminator).
Figure 2 – Data sequence (ASCII characters)
When designing data recording systems, care must be taken to ensure that the technical interface
and the configuration of the reading device (stationary scanner, hand-held scanner) do not lead to
character errors. Problem example: English keyboard ("Y" and "Z" characters swapped when com‐
pared to a German keyboard layout).
Modulo 43 check digit calculation
The check digit is used to verify manual entries based on the plain text information in the marking.
43 different characters are defined for the single-character check digit (see figure 2 for the ASCII
characters corresponding to checksum values 00 – 42).
The value of the check digit is calculated based on the data sequence characters, excluding the
check digit itself. The calculation method is based on the assignment table (see figure 2).
The check digit is calculated for the module data only (see section 4.1). The data fields for the
parts installation check (see section 5.1) are not used to calculate the check digit.
3.6  


### 第 9 页
Page 9
VW 01064: 2018-06
Calculating the check digit:
1) Using the assignment table, determine the checksum value for each character in the data se‐
quence
2) Calculate the total of all checksum values
3) Divide this total by 43
4) Use the division remainder and the assignment table to determine what the check digit is
Example:
Data sequence without check digit
065 KTO1234567
Sum
0 + 6 + 5 + 38 + 20 + 29 + 24 + 1 + 2 + 3 + 4 + 5 + 6 + 
7 = 150
Division
150 / 43 = 3 remainder 21
Check digit
21 ≙ L (see figure 2)
→ Data sequence
*065 KTO1234567L*
NOTE 3: The check digit is part of the data sequence and must not be confused with a check
digit in the code symbol.
BZD – Marking with a 1-D code (bar code, Code 39)
Build status documentation markings can use either a 1-D code or a 2-D code. In both cases, the
data sequences described below apply.
If the component needs to be marked for the parts installation check and for build status documen‐
tation purposes, a 2-D code must be used.
The build status documentation marking does not supersede the part marking in VW 10540-1 and
VW 10540-3. If applicable, both markings must be applied on the component independently from
each other.
BZD – 1-D code data sequence
The BZD data sequence contains 4 data fields and, in the standard version, 15 characters (excep‐
tions include module no. 005 for engines and module no. 006 for transmissions, which have
21 characters); see figure 3:
Figure 3 – BZD data sequence
The data sequence described here is just an example. The syntax of each individual module and
the permissible characters per data field must be taken from the Group Modules Catalog "BG-On‐
line." The maximum length of the module data is 30 characters.
4  
4.1  


### 第 10 页
Page 10
VW 01064: 2018-06
Module number
The module number is used to identify the type of vehicle component. It is 3 characters long and
can be alphanumeric or numeric. Example: 671 = front passenger airbag module.
Manufacturer’s code
The manufacturer's code is an in-house code used by Volkswagen AG to distinguish between dif‐
ferent manufacturers and their places of manufacture. For a definition, see VW 10540-1. The field
is typically 4 characters long. In the data sequence, the alphabetical or alphanumeric 3-character
manufacturer's codes must be right-aligned and begin with a blank character.
Example: Manufacturer: "ZFS" → Syntax in data sequence: " ZFS".
Serial number
The serial number is used to uniquely identify a manufacturer's vehicle components or assemblies
when they are identical in the sense of modules. The serial number is always alphanumeric (per‐
missible characters; 0 – 9, A – Z). The serial number's length for each individual component is
specified in the Group Modules Catalog.
Components must always be marked with a serial number. If this is not reasonably feasible, the
simultaneous engineering team (SET) or engineering group can decide to use a batch number in‐
stead. This decision must be disclosed to the BZD Office (email <EMAIL>).
The serial number sequence does not end when the part number or date of manufacture is
changed, or if the vehicle component is used in a different vehicle model. In other words, the serial
number must be independent from the part number.
Example:
Fuel tank = module "065"
Supplier code = "KTO", written with blank character " KTO"
→ Data sequence in plain text: *065 KTOxxxxxxxP*
The module data must remain unique for the entire retention period (≥ 15 years) and for all fuel
tanks from the "KTO" supplier that are supplied to Volkswagen AG brands. The technical imple‐
mentation and usage are irrelevant here.
Unless otherwise specified, and provided this requirement is met, the supplier can choose the
composition for their serial numbers. Number ranges with different sizes will result depending on
how the number is counted (see figure 4).
Figure 4 – Examples of counting methods
4.1.1  
4.1.2  
4.1.3  


### 第 11 页
Page 11
VW 01064: 2018-06
The alphanumeric counting method corresponds to a 36-number system using numbers and capi‐
tal letters.
If smaller number ranges are used, the remaining free spaces in the serial numbers can be filled
out with useful codes as defined by the supplier (e.g., code for manufacturing system, part code).
The counting method chosen must assuredly exceed the anticipated total number of modules. The
recommended counting method is: alphanumeric, 7 characters.
Modulo 43 check digit calculation
See section 3.6.
Implementation examples
The standard data sequence for the module data consists of 15 characters; see figure 5. The exact
syntax must always be gathered from the "BG-Online" Group Modules Catalog. Information regard‐
ing data sequences can be requested from the Group BZD Office (baugruppeninfo@volks‐
wagen.de).
Figure 5 – Standard data sequence for module data
Marking for parts installation checks with build status documentation
A 2-D code must be used for the marking for parts installation checks with build status documenta‐
tion.
Implementation examples are described at the end of this section.
Data sequence of 2-D code for parts installation checks and build status documentation
The data sequence for the matrix code consists of 6 data fields; see figure 6.
Figure 6 – 2-D code data sequence
The order of the individual data fields must be strictly adhered to. Attempting to specify absolute
positions within the overall string would make no sense, as certain data fields may be optional
and/or have variable lengths depending on the use case in question.
NOTE 4: The elimination of the usage code in issue 2012-06 of this standard needs only be tak‐
en into account for components yet to be introduced.
4.1.4  
4.2  
5  
5.1  


### 第 12 页
Page 12
VW 01064: 2018-06
Part number
The part number is a composite code containing identification and classification information. Its ba‐
sic syntax consists of a 9- to 11-character code.
In the 2-D code for parts installation checks and build status documentation, the 11-character part
number, followed by the 3-character color code, is always used. The 14-character data field is writ‐
ten without separating periods or blank spaces, left-aligned. Unused characters are filled out with
blank spaces; see figure 7.
Figure 7 – Part number with color code (2-D code)
NOTE 5: VW 01098 describes the part number syntax used by Volkswagen AG.
Part code
The "Part code" data field is optional. If the use case in question does not require this information,
this information is omitted without replacement and the data field has a length of 0. Separators (#)
must always be used, even if the field is blank.
The part code contains context-specific information on the vehicle component. Various formats and
definitions are permissible for the syntax of this data field depending on the corresponding vehicle
component category. For examples, see figure 8.
Figure 8 – Part code (2-D code)
The contents of this data field must be specified by the appropriate developer and documented in
the part drawing.
DUNS number
The "DUNS number" data field is optional. If the use case in question does not require this informa‐
tion, the DUNS number is omitted without replacement and the data field has a length of 0. Sepa‐
rators (#) must always be used, even if the field is blank.
The DUNS number is an internationally standardized supplier number. The data field has 9 charac‐
ters; see figure 9.
5.1.1  
5.1.2  
5.1.3  


### 第 13 页
Page 13
VW 01064: 2018-06
Figure 9 – DUNS number (2-D code)
NOTE 6: The use of Volkswagen supplier numbers (KRIAS system) or other code numbers is im‐
permissible.
Date of manufacture
The "Date of manufacture" data field is optional. If the use case in question does not require this
information, the date of manufacture is omitted without replacement and the data field has a length
of 0. Separators (#) must always be used, even if the field is blank.
The date of manufacture indicates the time at which the component was technically completed
(ready for installation or ready for delivery).
The "Date of manufacture" data field always has 6 digits and the DDMMYY (DayDayMonthMon‐
thYearYear) format; see figure 10. If the information for the specific date is not available, the first
workday of the week must always be entered.
Figure 10 – Date of manufacture (2-D code)
Module data
If the marking is only intended for part identification purposes, the module data can be omitted
without replacement. The characters"*" are also omitted in this case.
Section 4.1 describes the syntax of the module data.
NOTE 7: The check digit is calculated exclusively for the module data. The 2-D code's remaining
data fields are not used to calculate the check digit.
Additional data
Freely available to developers and suppliers.
If it is necessary to structure the information within additional data, the minus sign "-" must be used
as a separator.
Special characters in the data sequence
The "#" character is placed between each of the first 4 data fields of a data sequence (separator).
Separators must always be used, even if there are no characters in the field.
The "*" character is placed at the beginning and end of the module data in the data sequence. If
there is no module data, these special characters are not used either.
The "=" character terminates the data sequence for build status documentation and installation
checks (terminator).
The "-" character is permissible as a separator within the additional data.
5.1.4  
5.1.5  
5.1.6  
5.1.7  


### 第 14 页
Page 14
VW 01064: 2018-06
Implementation examples for 2-D codes
Standard data content for parts installation checks with build status documentation
The standard data content for parts installation checks with build status documentation contains
the "Part number with color code" and "Module data" data fields (with a standard 15-character syn‐
tax in the example). The "Part code," "DUNS number," and "Date of manufacture" data fields are
blank.
Deviations from this standard data content (see figure 11) are only permissible in justified cases.
Every deviation must be agreed upon with all parties involved in the process.
Figure 11 – Standard data content (2-D code)
The following code symbols result when using the conditions in section 6.2; see table 1.
Table 1
Types
Square code symbol
Rectangular code symbol
Code symbol
Size in dots
22 × 22
16 × 36
Size in mm
without quiet zone
11.22 × 11.22
8.16 × 18.36
Size in mm
with quiet zone
15.22 × 15.22
12.16 × 18.36
Minimum data content for parts installation checks
Deviations from the standard data content are permissible if the component geometry does not al‐
low for the standard data content. This deviation from the standard data content must be agreed
upon with all parties involved in the process.
The minimum data content for parts installation checks without build status documentation contains
the "Part number" data field, with 14 characters, only. The "Part code," "DUNS number," and "Date
of manufacture" data fields do not have any content, and the "Module data" data field is omitted;
see figure 12.
Figure 12 – Minimum data content (2-D code)
5.2  
5.2.1  
5.2.2  


### 第 15 页
Page 15
VW 01064: 2018-06
The following code symbols result when using the conditions in section 6.2; see table 2.


### 第 16 页
Page 16
VW 01064: 2018-06
Table 2
Types
Square code symbol
Rectangular code symbol
Code symbol
Size in dots
18 × 18
12 × 26
Size in mm
without quiet zone
9.18 × 9.18
6.21 × 13.26
Size in mm
with quiet zone
13.18 × 13.18
10.21 × 17.26
Minimum data content for build status documentation
If absolutely mandatory due to the component geometry, deviations from the standard data content
are permissible for the "build status documentation only" use case. This deviation from the stand‐
ard data content must be agreed upon with all parties involved in the process. In this case, the
build status documentation data sequence described in section 4.1 is written as a data matrix
code; see figure 13. The read result for this 2-D code is exactly the same as that for Code 39.
Figure 13 – Minimum data content for BZD (2-D code)
The following code symbols result when using the conditions in section 6.2; see table 3.
Table 3
Types
Square code symbol
Rectangular code symbol
Code symbol
Size in dots
16 × 16
12 × 26
Size in mm
without quiet zone
8.16 × 8.16
6.21 × 13.26
Size in mm
with quiet zone
12.16 × 12.16
10.21 × 17.26
NOTE 8: The syntax of the module data is specified in the Group Modules Catalog. The module
data has a 4-character code (manufacturer's code) used to identify the manufacturer. The manu‐
facturer's code as per VW 10540-1 has 3 characters and must be used with a leading blank char‐
acter. Contact via: <EMAIL>
5.2.3  


### 第 17 页
Page 17
VW 01064: 2018-06
Maximum data content for parts installation checks with build status documentation
Depending on the specific use case, the data content can be expanded all the way to the maxi‐
mum data content limit. In this case, all data fields, incl. additional data, are filled out. The length of
the "Part code" and "Additional data" data fields will depend on the specific use case; see
figure 14.
Figure 14 – Maximum data content (2-D code)
The size of the code symbol will depend on the specific data content. Using figure 14 above, which
has 65 characters, results in the following code symbols when using the conditions in section 6.2;
see table 4
Table 4
Types
Square code symbol
Rectangular code symbol
Code symbol
Size in dots
32 × 32
16 × 48
Size in mm
without quiet zone
16.32 × 16.32
8.16 × 24.48
Size in mm
with quiet zone
20.32 × 20.32
12.16 × 28.48
Label design and layout (1-D code and 2-D code)
Code symbol for bar code (1-D code)
The following requirements must be taken into account when generating the 1-D code symbol:
–
Code 39 as per ISO/IEC 16388 must be used.
–
The overall symbol quality as per DIN EN ISO/IEC 15416 must be 3.5 or better.
–
Module width x (see appendix A): approx. 0.254 mm
–
Module width ratio: at least 1 : 2.5
–
Gap width ratio: same as module width ratio
–
Print resolution: at least 300 dpi
–
Quiet zone: at least 3 mm per side
–
Bar height: approx. 10 mm
–
Check digit calculation (automatic) does not apply
5.2.4  
6  
6.1  


### 第 18 页
Page 18
VW 01064: 2018-06
–
These requirements yield a symbol size of approx. 63 mm × 10 mm for a data sequence with
15 characters.
–
Deviations must be agreed upon with all parties involved.
Code symbol for matrix code (2-D code)
The following requirements must be taken into account when generating the 2-D code symbol:
When used in vehicle assembly plants
–
A data matrix code must be used.
–
The overall symbol quality as per ISO/IEC 15415 must be 3 or better. This symbol quality must
be ensured throughout the entire process chain up to the point of fitment.
–
ECC200 error correction
–
Module size x (see appendix A): at least 0.50 mm
–
Printer resolution: 300 dpi or higher
–
The quiet zone is at least 2 mm on each side; a small quiet zone is only permissible in agree‐
ment with all parties involved.
–
Matrix size and character set created with the automatic function
–
These requirements result in a symbol size of approx. 20 mm × 20 mm for a completely filled-
out data sequence. Larger symbols will result for group markings.
–
Deviations must be agreed upon with all parties involved.
When used in engine plants
–
A data matrix code must be used.
–
The overall symbol quality as per ISO/IEC TR 29158 (formerly AIM-DPM 2006) must be 3 or
better. This symbol quality must be ensured throughout the entire process chain up to the
point of fitment.
–
ECC200 error correction
–
Module size x (see appendix A): at least 0.50 mm
–
Printer resolution: 300 dpi or higher
–
The quiet zone is at least 4 times the module size.
–
Matrix size and character set created with the automatic function
–
These requirements, together with a size of 20 × 20 dots, result in a symbol size of approx.
10 mm × 10 mm for a standard string with 32 – 36 characters.
–
When identical components (across different suppliers) are involved, the exact same marking
method, size, and position must be chosen. The appropriate developer must document this in
the drawing.
–
The position must always be legible in the installed condition.
–
The DMC manufacturer must provide documentation verifying that the legibility quality require‐
ment is being met.
–
Deviations, e.g., resulting from a lack of space, must be expressly noted and must be agreed
upon with all parties involved.
6.2  
6.2.1  
6.2.2  


### 第 19 页
Page 19
VW 01064: 2018-06
When used in transmission plants
–
A data matrix code must be used.
–
The overall symbol quality as per ISO/IEC TR 29158 (formerly AIM-DPM 2006) must be 3 or
better. This symbol quality must be ensured throughout the entire process chain up to the
point of fitment.
–
ECC200 error correction
–
The quiet zone around the matrix must be at least 2 cells per side. The cell size must be selec‐
ted such that the individual cells are much larger than the largest interfering structures on the
surface.
Apart from this, the specifications in the document "Quality Requirements for Part Markings" apply.
Plain text information (1-D and 2-D codes)
In addition to the code symbol, the marking must include plain text information. It is used for man‐
ual data entry if the code symbol is flawed and illegible or in the event of system interruptions. The
requirements are as follows:
–
1-D code: The plain text must show the entire data sequence, including the check digit
–
2-D code: If the 2-D code includes the "Module data" or "Part number" data field, they must be
shown as plain text without fail. Optionally, all data fields can be shown as plain text.
–
The "*" character is added at the beginning and end of the plain text data for the "Module data"
data field.
–
In order to make it easier to read, the part number plain text information must have a blank
space between the front number, middle group, end number, and suffix.
–
The font height must be at least 2 mm.
–
Preferably, the plain text appears centered below the bar code (plain text may be positioned
differently in exceptional cases).
–
To distinguish between the digit "0" and the letter "O", the zero must be written with a slash; a
suitable font must be used for this, e.g., "Consolas" θ.
If the component geometry does not allow for sufficient space for the BZD plain text information,
the Product Team, SET, or the like can decide to do without the plain text. This decision must be
agreed upon with all parties involved in the process.
6.2.3  
6.3  


### 第 20 页
Page 20
VW 01064: 2018-06
One-part label permanently attached to the component (1-D and 2-D codes)
The following requirements must be taken into account for the label type and layout:
–
The label must be self-adhesive and must not inadvertently come off once it is affixed.
–
The printed image may be inverted if this is absolutely necessary (styling specification)
–
Print smear is not permissible
–
Substrate: Metalized film with chalk coating for thermal transfer printer; RAL 9010 (pure white
– including bar code field); alternatively, silver gray
–
Font: Jet black
The label must be applied to the vehicle component in such a way that the data will be visible and
technically legible after the component is installed. The position and orientation must be specified
in a drawing.
Taking existing labels initially intended for other purposes and using them for the marking descri‐
bed here is permissible. The existing contents of these labels must not have a negative impact on
the usability of the marking described here (e.g., due to other similar code symbols).
Two-part or multiple-part labels (1-D and 2-D codes)
The one-part label permanently attached to the component must be supplemented with one or
more additional, detachable label portions. These are detached during the manufacturing process
and, for example, affixed to the transfer ticket. At least the last detachable part to be used contains
the code symbol. All other parts at least contain the plain text information. The requirements speci‐
fied above for the one-part label apply accordingly.
Detachable label portions must not be excessively wide or tall, as, for example, they must not be
larger than the fields on the transfer ticket reserved for affixing these portions. The label size to be
used is based on the size of the code symbol, including the quiet zone and plain text information.
Recommended sizes:
–
1-D code: 15-character data sequence (standard data sequence): approx. 80 mm × 20 mm
–
1-D code: 21-character data sequence (modules 005 = engine, 006 = transmission): approx.
100 mm × 20 mm
–
2-D code: The recommended size for a matrix code label is 20 mm × 30 mm.
The labels' material and positioning must ensure that it is possible to quickly and easily detach the
appropriate portions without damaging them. The detachable portion must not inadvertently come
off.
Labeling outside on the ASSY (group marking)
Vehicle components that will later be inaccessible require a multi-part label:
–
2 parts if the data is recorded via direct entry at the vehicle plant
–
3 parts if the data is recorded via transfer ticket at in the vehicle plant
During the manufacturing process for the complex ASSY, the detachable portion is then perma‐
nently affixed to the outside of the ASSY at an agreed location (e.g., seat: outer panel of the seat
cushion frame).
6.4  
6.4.1  
6.5  


### 第 21 页
Page 21
VW 01064: 2018-06
ASSY transfer ticket (group marking)
If labeling on the outside of the ASSY is not possible, the supplier provides an ASSY transfer tick‐
et. It must be supplied with the complex ASSY and read in downstream manufacturing stages. The
following content must be provided:
–
Supplier name and ASSY designation
–
In the case of vehicle-specific deliveries: vehicle control number or VIN
–
If delivery is not vehicle-specific: assignment number of ASSY to ASSY transfer ticket
–
All bar codes of modules installed in the ASSY and inaccessible as a result
–
Acceptance notice (stamp, etc.) of the supplier attesting to the correctness of the data provi‐
ded
The ASSY transfer ticket is thus part of the vehicle transfer ticket. It must be affixed to the ASSY in
such a way that it will not come off or be damaged during transportation. Its content, layout, and
processes must be agreed upon with the appropriate vehicle plant.
Marking applied directly in the material (DPM – Direct Part Mark)1)
2-D codes can be inscribed directly in or on the component material if so required by technical or
other requirements (e.g., service life marking, styling requirements). The requirements for the plain
text information must also be taken into account and met accordingly.
Available marking techniques:
–
Laser marking
–
Dot peen marking
–
Electrochemical etching
–
Inkjet printing
The use of each one of these techniques is suitable for certain applications, depending on the
parts' life expectancy and material mix or production volume and wear caused by environmental
influences.
Verifications
Verifying bar codes
Bar codes must be verified as per DIN EN ISO/IEC 15416. The overall symbol classification as per
DIN EN ISO/IEC 15416 must have a value of 3.5 or better.
Verifying 2-D codes
2-D codes must be verified as per ISO/IEC 15415. The overall symbol quality must have a value of
3 or better.
For directly marked 2-D codes, ISO/IEC TR 29158 must be taken into account; the overall symbol
quality must also have a value of 3 or better in this case.
NOTE 9: The use of direct markings requires appropriate readers (DPM scanners). These mark‐
ings must be agreed upon with all departments involved (from Acquisition on the assembly line to
6.6  
6.7  
6.8  
6.8.1  
6.8.2  
1)
Section 6.7 source: "Das Lesen von Direktmarkierungen – 10 wichtige Aspekte" (Reading Direct Markings – 10 Important Aspects)
by Cognex and "Neue Standards verifizieren 2D Data Matrix Codes zuverlässig" (New Standards Allow for Reliable Verification of 2-
D Data Matrix Codes) – white paper by Carl W. Gerst III, Cognex Corporation, Senior Director & Business Unit Manager,
ID Products.


### 第 22 页
Page 22
VW 01064: 2018-06
After-Sales Service – this is to ensure that the codes can be read wherever necessary anywhere in
the Group).
Group marking – Pre-recording data for complex assemblies
In the case of complex ASSYs, the data marked on the vehicle component will no longer be acces‐
sible in some cases (e.g., airbag module in seat). In these cases, the data must be pre-recorded
while the ASSY is being made in order to ensure that the data will be available at the point of fit‐
ment of the ASSY.
Group markings can only be implemented using matrix code.
The supplier pre-records the data by reading the individual module data sets. These individual data
sequences are combined to form a group data sequence (see figure 15 and figure 16).
Figure 15 – Group marking 1
Figure 16 – Group marking 2
For this group data sequence, a new marking is created and then permanently affixed to the out‐
side of the complex ASSY.
This kind of pre-recording is only permissible if none of the modules in the complex ASSY will be
separated from the ASSY in downstream processes. Changes (e.g., replacing a module) cannot be
integrated into the group marking after the fact.
The requirements specified for individual labels apply accordingly. The marking must meet the fol‐
lowing requirements:
–
Plain text information is required for every module in a group marking.
–
The "&" character is used as a separator between 2 data sets.
–
The last data set contains terminator "=".
A square data matrix code can consist of a maximum of 144 rows × 144 columns. Therefore,
1 982 ASCII characters can be represented. This results in a maximum of 23 data sets that can be
grouped together. This number is limited to 20 due to usability reasons.
Special characters in the data sequence
The "#" character is placed between each of the first 4 data fields of a data sequence (separator).
Separators must always be used, even if there are no characters in the field. The "*" character is
placed at the beginning and end of the module data in the data sequence. If there is no module
data, these special characters are not used either.
7  
7.1  


### 第 23 页
Page 23
VW 01064: 2018-06
The "&" character links 2 data sequences.
The "=" character terminates the data sequence (terminator).
The "-" character is permissible as a separator within the additional data.
Data exchange
The supplier's manufacturing area is treated like an in-house upstream manufacturing process.
The supplier records the data for all modules contained in complex ASSYs and submits this data to
the appropriate vehicle plant.
This pre-recording is only permissible if it impossible for the data to be assigned to the wrong com‐
ponent or vehicle in downstream processes or if there are appropriate emergency procedures that
can be used to correct the data.
Data formats and the entire technical implementation are subject to bilateral agreement.
BZD – Marking and part identification with RFID tags
The use of RFID for build status documentation of production components is described in
VW 01067 (as of issue 2017). See also the information on the ONE.Konzern Business Platform
(supplier platform) at http://vwgroupsupply.com.
BZD – Marking of JIS components
The data matrix code is also used to mark just in sequence (JIS) components. In this case, the JIS
information is written in the additional data of the data matrix code (cf. Section 5.1.6). The syntax of
the JIS information when using the data matrix code as per VW 01064 is described in the Group
JIS Performance Specification. The minus sign must be used as a separator within the JIS infor‐
mation.
7.2  
8  
9  


### 第 24 页
Page 24
VW 01064: 2018-06
Applicable documents
The following documents cited are necessary to the application of this document:
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
PS_1.4_999_1430_03
Bauzustandsdokumentation durchführen (Carrying Out Build Status
Documentation)
PS_1.4_999_1939_04
Basisliste aktualisieren (Updating the Base List)
PS_1.4_999_1939_05
Festlegung und Umsetzung des BZD-Umfangs im Produktteam (Defin‐
ing and Implementing the BZD Scope in the Product Team)
VW 01067
Use of Auto ID for Unique Object Marking; Serialization Using Optical
Coding Methods and/or Radio Frequency Identification (RFID)
VW 01098
Part Number System
VW 10500
Company Designation, Marking of Parts; Guidelines for Use
VW 10540-1
Manufacturer's Code; for Vehicle Parts
VW 10540-3
Manufacturer's Code; Issuing Ranges for Foreign Plants Issuing Manu‐
facturer’s Codes
WSK.013.290°E
Identification Label for Electronic Control Units
DIN EN ISO/
IEC 15416
Information technology - Automatic identification and data capture tech‐
niques - Bar code print quality test specification; Linear symbols
ISO/IEC 15415
Information technology - Automatic identification and data capture tech‐
niques - Bar code symbol print quality test specification - Two-dimen‐
sional symbols
ISO/IEC 16388
Information technology - Automatic identification and data capture tech‐
niques - Code 39 bar code symbology specification
ISO/IEC TR 29158
Information technology - Automatic identification and data capture tech‐
niques - Direct Part Mark (DPM) Quality Guideline
Bibliography
[1]
VW 01068 "Module Marking for Engine and Transmission Manufacturing Plants"
[2]
LAH.DUM.909.H "Identification of Electronic Vehicle Systems – UDS 80125"
[3]
"Quality Requirements for Part Markings in Transmission Production at Volks‐
wagen Kassel"
[4]
Data Matrix Symbol Specification, e.g., with: AIM DPM-2006, Association for Automatic
Identification and Mobility
[5]
Group Modules Catalog System BG-Online
10  
11  


### 第 25 页
Page 25
VW 01064: 2018-06
[6]
Das Lesen von Direktmarkierungen – 10 wichtige Aspekte (Reading Direct Markings –
10 Important Aspects), Cognex
[7]
Neue Standards verifizieren 2-D-Data-Matrix-Codes zuverlässig (New Standards Allow
for Reliable Verification of 2-D Data Matrix Codes), Carl W. Gerst III, Cognex


### 第 26 页
Page 26
VW 01064: 2018-06
Examples
Figure A.1 – Module width/module size of code symbols (x)
Figure A.2 – Standard label with data matrix code
Figure A.3 – Standard label with bar code
Figure A.4 – Label with group marking
Appendix A (informative)  


### 第 27 页
Page 27
VW 01064: 2018-06
Figure A.5 – Label with group marking and additional data

