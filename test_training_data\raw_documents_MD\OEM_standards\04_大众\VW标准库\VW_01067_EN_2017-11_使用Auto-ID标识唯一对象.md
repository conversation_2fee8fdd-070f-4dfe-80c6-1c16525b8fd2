# VW_01067_EN_2017-11_使用Auto-ID标识唯一对象.pdf

## 文档信息
- 标题：
- 作者：
- 页数：37

## 文档内容
### 第 1 页
Group standard
VW 01067
Issue 2017-11
Class. No.:
01152
Descriptors:
coding, coding method, marking, RFID, Radio Frequency Identification, component marking, vehicle
marking, tool marking, reusable container marking, packaged item marking
Use of Auto ID for Unique Object Marking
Serialization Using Optical Coding Methods and/or Radio Frequency
Identification (RFID)
Preface
The aim of this Volkswagen standard (VW) is to ensure the unique marking (serialization) and au‐
tomated identification of objects using optical coding methods and/or Radio Frequency Identifica‐
tion (RFID). For this purpose, unique data structures (reference numbers) are defined that can be
stored using normal optical coding methods as well as on RFID tags, enabling the hybrid use of the
technologies. In this way, it is possible to use established processes such as optical coding (bar
code, 2-D code, data matrix code) and plain text as a back-up to advanced RFID technology.
Previous issues
VW 01067: 2012-10, 2013-06, 2014-09, 2015-10, 2016-11
Changes
The following changes have been made to VW 01067: 2016-11:
–
Section 2, abbreviations: BZD, DMC, DPM, CSD, TLD, and ASSY added
–
Section 4.2 added
–
Section 4.5.1 "Layout of number ranges", last paragraph changed
–
Section 4.7.1 "Layout of number ranges", warning information added
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Page 1 of 37
Technical responsibility
The Standards department
EVV/4
Dr. Malte Schmidt
Tel.: +49 5361 9 87011
K-ILI/5 Uwe Stüber
K-ILI
Tel.: +49 5361 9 29063
Uwe Wiesner
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2016-12


### 第 2 页
Page 2
VW 01067: 2017-11
Contents
Page
Scope ......................................................................................................................... 2
Symbols and abbreviations ........................................................................................ 4
Fundamentals of unique object marking (serialization) .............................................. 4
RFID technology ........................................................................................................ 4
Optical coding and plain text (labels) ......................................................................... 8
Representation of the data contents for unique object marking ................................. 9
Marking of prototype parts including notes in Component Performance
Specifications and drawings ....................................................................................... 9
Layout of number ranges ......................................................................................... 10
RFID marking of prototype parts .............................................................................. 11
Optical marking of prototype parts ........................................................................... 13
RFID use for BZD in production ............................................................................... 15
Layout of number ranges ......................................................................................... 16
RFID marking of production parts (BZD) .................................................................. 16
Optical marking of production parts (BZD) ............................................................... 17
Marking of tools ........................................................................................................ 19
Layout of number ranges ......................................................................................... 19
RFID marking of tools .............................................................................................. 20
Marking within the Volkswagen Group ..................................................................... 20
Optical marking of tools ............................................................................................ 20
Marking of reusable containers ................................................................................ 22
Layout of number ranges ......................................................................................... 22
RFID marking of reusable containers ....................................................................... 22
Optical marking of reusable containers .................................................................... 24
Marking of packaged items ...................................................................................... 25
Layout of number ranges ......................................................................................... 25
RFID marking of packaged items ............................................................................. 26
Optical marking of packaged items .......................................................................... 27
Marking of JIS packaged items ................................................................................ 29
Layout of number ranges ......................................................................................... 29
RFID marking of JIS packaged items ....................................................................... 29
Optical marking of JIS packaged items .................................................................... 30
Marking of vehicles .................................................................................................. 30
Layout of number ranges ......................................................................................... 30
RFID marking of vehicles ......................................................................................... 31
Optical marking of vehicles ...................................................................................... 33
Applicable documents .............................................................................................. 34
6-bit coding ............................................................................................................... 36
Examples (reference number for prototype parts) ................................................... 37
1
2
3
3.1
3.2
4
4.1
4.1.1
4.1.2
4.1.3
4.2
4.2.1
4.2.2
4.2.3
4.3
4.3.1
4.3.2
4.3.3
4.3.4
4.4
4.4.1
4.4.2
4.4.3
4.5
4.5.1
4.5.2
4.5.3
4.6
4.6.1
4.6.2
4.6.3
4.7
4.7.1
4.7.2
4.7.3
5
Appendix A
Appendix B
Scope
This standard describes unique object marking (serialization) using an optical coding method
and/or RFID. It is used for components, vehicles, tools, reusable containers, and packaged items.
The marking as per this standard does not replace part marking as per VW 10500 and VW 01064
or the marking of tools, auxiliary tools, test equipment, and gages (identification plate) as per
VW 34022.
1  


### 第 3 页
Page 3
VW 01067: 2017-11
NOTE 1: This marking does not apply to components that are diagnostics-enabled. The markings
for electronic control units are described in workshop sketch WSK.013.290°E, "Identification plate
for electronic control units."
This standard is intended for:
–
Developers specifying a unique object marking
–
Quality Assurance staff inspecting samples of a unique object marking
–
Vehicle part suppliers in charge of implementing a unique object marking
The contents of this standard are based on German Association of the Automotive Industry (VDA)
standards VDA 5500, VDA 5501, VDA 5509, VDA 5510, and VDA 5520, and take into account the
current status of International Organization for Standardization (ISO)/International Electrotechnical
Commission (IEC) standardization.


### 第 4 页
Page 4
VW 01067: 2017-11
Symbols and abbreviations
Application family identifier
Assembly
Build status documentation
Company code
Company identification number
Classification System for Documents
Dun & Bradstreet
Data identifier
Data matrix code
Direct part marking
Data structure format identifier
Data universal numbering system
End of transmission
Vehicle number
Group separator
Global transport label
Issuing agency code
International Telecommunications Union
Just in sequence
Group Business Platform
Object identifier
Part number
Part serial number
Radio frequency identification
Record separator
Serial number
Technical Guideline for Documentation
Ultra high-frequency
Unique item identifier
User memory
Unique partner identification key
Uniform Resource Locator
Pre-production approval testing vehicle
Vehicle identification number
alphanumeric
numeric
Fundamentals of unique object marking (serialization)
RFID technology
This document describes the use of passive UHF tags (frequency range: 860 MHz to 960 MHz) for
the purpose of unique object marking (serialization) and automatic tracking. The air interface corre‐
sponds to ISO/IEC 18000-63/EPC class 1 generation 2. When selecting the tag, the different fre‐
quency ranges defined by the ITU for UHF communication must be considered: Europe (865 MHz
to 868 MHz), USA (902 MHz to 928 MHz), and Japan (950 MHz to 960 MHz).
RFID tags have four logical memory areas/memory banks (MB) as per ISO/IEC 18000-63/EPC
class 1 generation 2.
2  
AFI
ASSY
BZD
CC
CIN
CSD
D&B
DI
DMC
DPM
DSFID
DUNS
EOT
FZN
GS
GTL
IAC
ITU
JIS
KBP
OID
PN
PSN
RFID
RS
SN
TLD
UHF
UII
UM
UPIK
URL
VFF
VIN
an
n
3  
3.1  


### 第 5 页
Page 5
VW 01067: 2017-11
MB00
Kill and password
MB01
UII
MB10
Tag identification (TID)
MB11
UM
The memory areas are specified and described in detail in the underlying standards.
The MB01 and MB11 areas are used for saving data relating to the object. The UII is stored in the
MB01 area along with mandatory control information. The UII contains an exclusively assigned,
unique reference number. After the initial write access, the MB01 memory area can be protected
(locked) to prevent any further write access. The MB11 area contains the UM. Optionally, addition‐
al payload can be stored in the UM. It must be noted that using the UM instead of the UII results in
a lower read/write performance.
The memory size of each area depends on the specific application. Table 1 shows the preferred
sizes of the UII and the additional UM. To ensure sufficient flexibility, the additional UM must be
between 128 bits and 512 bits. The nominal values are rounded up and adjusted to available mem‐
ory sizes to ensure that the respective application-specific storage requirements are fulfilled.


### 第 6 页
Page 6
VW 01067: 2017-11
Table 1 – Storage requirements per use case
Use case
Minimum storage requirement
Prototype parts
240-bit UII
Vehicles (distribution)
128-bit UII
Vehicles (pre-production)
128-bit UII + 128-bit UM
Tools
240-bit UII
Containers
240-bit UII
Packaged items
240-bit UII
The structure and layout of the data contents is based on ISO/IEC standards. A key feature of im‐
plementing RFID data structures as per ISO/IEC standards is that it conforms to and complies with
established bar code/DMC standards. This ensures the coexistence of bar code/DMC and RFID,
and the step-by-step migration to RFID.
An AFI, which can be used for filtering data, is stored in the tag's control information (ISO/
IEC 15961-1). The AFIs are defined in ISO 17363, ISO 17364, ISO 17365, ISO 17366, and
ISO 17367 as specific to the application. The complete structure of the control information, includ‐
ing referencing of the AFIs, is described in the underlying standards and recommendations (includ‐
ing VDA 5500, VDA 5501, VDA 5509, VDA 5510, and VDA 5520) and is therefore not explained in
more detail here.
A unique reference number is stored in the UII. The syntax and data structure of the reference
number is based on the JAIF Global Radio Frequency Identification (RFID) Item Level Standard,
ISO/IEC 15418, ISO 17363, ISO 17364, ISO 17365, ISO 17366, and ISO 17367. The layout specif‐
ic to the application can be taken from section 4. The UII concludes with the EOT. The EOT can be
removed if the UII's data structure uses the entire memory area available (MB01). Free bits are
padded with fill characters until the current 16-bit word is reached. For this, a fixed bit string (mono‐
morphic) is used as per ISO/IEC 15962. Details on this can be taken from table 2, which describes
the generic layout of the RFID data structure (MB01) conforming to ISO/IEC standards, including
the prefixed protocol control bits (PC header).


### 第 7 页
Page 7
VW 01067: 2017-11
Table 2 – Schematic layout of RFID data structures conforming to ISO/IEC standards
Bit location
(HEX)
Data type
Value
Size
Description
MB01: CRC + protocol control word
00 - 0F
CRC-16
Hardware
assigned
16 bits
Cyclic redundancy
check
10 - 14
Length
Variable
5 bits
Represents the number
of 16-bit words exclud‐
ing the PC field and the
attribute/AFI field.
15
PC bit 0x15
0b0 or 0b1
1 bit
0 = No valid user data,
or no
MB11
1 = Valid user data in
MB11
16
PC bit 0x16
0b0
1 bit
0 = "Extended PC word"
not used
17
PC bit 0x17
0b1
1 bit
1 = Data interpretation
rules based on ISO
18 - 1F
AFI
e.g.,
0x90, 0xA1, 0xA2,
0xA3
8 bits
AFI used as per ISO/
IEC 15961-1,
ISO 17363, ISO 17364,
ISO 17365, ISO 17366,
and ISO 17367
 
Subtotal
 
32 bits
 
MB01: UII
Start at location
20 Go to end of
data/end of
available mem‐
ory
Unique reference number (placeholder). For data contents specific to the appli‐
cation, see section 4.
End of trans-
mission
EOT
1 (alphanu‐
meric)
EOT(!)
Padding until the
end of the last
16-bit word
0b10,
0b1000,
0b100000,
0b10000010,
0b1000001000,
0b100000100000,
or
0b1000001000001
0
2, 4, 6, 8,
10, 12 or
14 bits
Padding as per ISO/
IEC 15962 (monomor‐
phic)
 
Subtotal
 
Variable
Up to 240 bits
 
Total MB01 bits:
 
Variable
Up to 272 bits
The data contents are coded in 6-bit. Only the letters, numbers, and select special characters high‐
lighted in appendix A may be used.


### 第 8 页
Page 8
VW 01067: 2017-11
Optical coding and plain text (labels)
The DMC is used for the unique identification of objects. Depending on the application, code 128
can also be used (e.g., GTL, reusable container). The data contents of the bar codes and DMCs
correspond to the RFID principle. Identical reference IDs are stored on both the 1-D/2-D code and
the RFID tag. The aim of this approach is to use optical and radio-frequency Auto ID methods in a
hybrid manner.
The bar code is structured as per ISO/IEC 15417 and is not described in further detail here. The
DMC is structured as per ISO/IEC 15418 and ISO/IEC 15434. The syntax consists of the message
header, followed by a format header for designating the embedded data structure, and finally the
format trailer, which is used as a final character. The individual data elements are identified by DIs
and separated by GSs.
Table 3 shows the generic layout of DMCs conforming to standards:
Table 3 – Schematic layout of DMCs conforming to ISO/IEC standards
Start sequence
[)>
RS
R
S
Format identifier
06
Group separator
G
S
Unique reference number
..
Group separator
G
S
Additional data element
..
RS
R
S
EOT
EOT
The syntax is structured in such a way that there is an option to code further data elements in addi‐
tion to the unique reference ID. The precondition is that the additional payload is marked using
standardized DIs (cf. ISO/IEC 15418), ensuring that these additional data contents are correctly in‐
terpreted across the Group. This is also intended to avoid or reduce proprietary solutions.
The data contents are coded in 8-bit. However, in order to ensure the synchronizing of bar
code/data matrix and RFID data structures, only the letters, numbers, and select special characters
used in 6-bit coding (see appendix A) may be used.
3.2  


### 第 9 页
Page 9
VW 01067: 2017-11
The bar codes/DMCs are printed on suitable labels. The code types, the design of the labels and
their contents must comply with the specifications of VW 01064 and VW 10500. If possible, the da‐
ta contents of the codes are presented in plain text on the labels. In the event that, in addition to
the optical codes, RFID is used, it is also recommended to print the RFID emblem as per ISO/
IEC 29160 for the purpose of optical marking. The generic variant (cf. Figure 1) or one of the fol‐
lowing application-specific variants can be used for this (cf. ISO/IEC 29160):
–
B1 (reusable container)
–
B3 (transport unit, packaged items)
–
B5 (product packaging)
–
B7 (product)
–
B8 (freight container)
The label must be suitably dimensioned for printing the RFID emblem.
Figure 1 – RFID emblem (generic)
Representation of the data contents for unique object marking
Marking of prototype parts including notes in Component Performance Specifications
and drawings
The electronic marking as per this standard (VW 01067) is mandatory for all prototype parts until
the start of the VFF. Deviations specific to a vehicle project and/or component are possible.
This standard describes the use of DMCs and RFID. Prototype parts are marked using DMCs as
standard. The RFID marking is explicitly mandatory for all prototype parts that are listed in the
RFID reference list. Further components may also be marked with RFID because of vehicle
project-specific requirements. The RFID reference list is available on the KBP http://vwgroupsup‐
ply.com under the path: Information > Divisions > Research and Development > TE-Logistik >
RFID.
Direct access:
Internally
Radio Frequency Identification
Externally
Radio Frequency Identification
Standard VW 01064 applies to the marking of production components for installation testing and
BZD.
Component Performance Specification and drawing notes
4  
4.1  


### 第 10 页
Page 10
VW 01067: 2017-11
For information in the drawings and Component Performance Specifications for the RFID marking,
see figure 2 (text macro NO-E4 from VW 01014):
Figure 2 – Text macro NO-E4 (RFID marking)
Layout of number ranges
The layout of the reference number for marking prototype parts is based on three number ranges,
which ensure that the number is unique; see table 4:
Table 4 – Layout of number ranges
 
Number range
Number of characters
1
CIN
9 characters (alphanumeric)
2
PN/Group
max. 20 characters (alphanumeric)
3
PSN
max. 9 characters (alphanumeric)
 
Total number of characters
max. 40 characters (alphanumeric)
The number of characters for the CIN, PN, and PSN must not exceed 33 characters (alphanumer‐
ic) in total. Including additional characters required for representing suitable RFID data structures
(cf. Section 4.1.2), this adds up to a data string of max. 40 characters (alphanumeric). This charac‐
ter length can be covered using established memory sizes (240-bit UII) so that the hybrid marking
of parts can be ensured using RFID and DMC.
The layout of the CIN depends on whether the prototype parts are marked by a contractor or within
the Group; see table 5:
Table 5 – Marking variants
 
Marking
CIN
Number of characters
1
By the contractor
Supplier DUNS (D&B)
9 characters (numeric)
2
Within the Group
Brand code + plant
+ cost center
2 characters (alphanumeric) + 2
characters (alphanumeric)
+ 5 characters (numeric)
The owner of the CIN assigns the reference number and ensures that it is unique.
The structure of the PN corresponds to VW 01098. PNs can have leading spaces. Leading spaces
must be given special consideration when implementing optical and RFID-based Auto ID systems.
It is absolutely mandatory that they are also coded to ensure that the completely coded character
string or parts thereof can be used as a reference to additional information stored by the IT system.
The PSN consists exclusively of uppercase letters and numbers.
4.1.1  


### 第 11 页
Page 11
VW 01067: 2017-11
RFID marking of prototype parts
The following AFI is used within the control information (MB01): A1 (product reference).
Marking by suppliers
Table 6 shows the layout of the unique reference number within the UII (MB01):
Table 6 – Example of RFID data structure (contractor)
 
Data content UII (MB01)
Number of characters
Value
1
DI
3 characters (alphanumeric)
37S
2
IAC
2 characters (alphanumeric)
UN (DUNS)
3
CIN
9 characters (numeric)
123456789
4
PN
max. 20 characters (alphanumeric)
_5G9945093A
5
Separator
1 character
+
6
PSN
max. 9 characters (alphanumeric)
BA7654321
7
EOT
1 character (alphanumeric)
EOT (!)
 
Number of characters
max. 40 characters (alphanumeric)
240 bit
Alphanumeric representation:
37SUN123456789 5G9945093+BA7654321!
The Audi and Porsche brands require, in addition, the representation of the color code (if applica‐
ble) and the component-specific technical version (if applicable). The composition of the pertinent
data structures must be taken from appendix B.
NOTE 2: Representing color codes and technical versions results in a longer PN. At the same
time, the combination of DUNS number, PN, and SN, including AutoID-specific control characters,
may have max. 40 characters (alphanumeric), i.e., the number of remaining characters for repre‐
senting the SN is limited to max. 5 to 6 characters (alphanumeric).
4.1.2  
4.1.2.1  


### 第 12 页
Page 12
VW 01067: 2017-11
Marking within the Volkswagen Group
Table 7 shows the layout of the unique reference number within the UII (MB01):
Table 7 – Example of RFID data structure (within the Group)
 
Data content UII (MB01)
Number of characters
Value
1
DI
3 characters (alphanumeric)
37S
2
IAC
2 characters (alphanumeric)
SC (marking
within the Group)
3
CIN
9 characters (alphanumeric)
*********
4
PN
max. 20 characters (alphanumeric)
_5G9945093A
5
Separator
1 character
+
6
PSN
max. 9 characters (alphanumeric)
BA7654321
7
EOT
1 character (alphanumeric)
EOT (!)
 
Number of characters
max. 40 characters (alphanumeric)
240 bit
Alphanumeric representation:
37SSC********* 5G9945093A+BA7654321!
The Audi and Porsche brands require, in addition, the representation of the color code (if applica‐
ble) and the component-specific technical version (if applicable). The composition of the pertinent
data structures must be taken from appendix B.
NOTE 3: Representing color codes and technical versions results in a longer PN. At the same
time, the combination of DUNS number, PN, and SN, including AutoID-specific control characters,
may have max. 40 characters (alphanumeric), i.e., the number of remaining characters for repre‐
senting the SN is limited to max. 5 to 6 characters (alphanumeric).
*******  


### 第 13 页
Page 13
VW 01067: 2017-11
Optical marking of prototype parts
DMCs are used for marking prototype parts.
Marking by suppliers
Table 8 shows how data matrix contents are represented conforming to ISO/IEC standards (incl.
control characters).
Table 8 – Optical coding of prototype parts (contractor)
Description
Data content
Start sequence
[)>
RS
R
S (ASCII)
Format identifier
06
GS
G
S (ASCII)
DI
37S
IAC
UN (DUNS)
CIN
123456789
PN/Group
_5Q9945093A
Separator
+
PSN
BA7654321
RS
R
S (ASCII)
EOT
EOT (ASCII)
Example: [)>R
S06G
S37SUN123456789 5Q9945093A+BA7654321R
S
EOT
Figure 3 shows an example of a data matrix label structured accordingly:
Figure 3 – Example of data matrix label
NOTE 4: Sample labels are shown under the link for Volkswagen employees VW 01067 – Sam‐
ple Labels for Prototype Parts v1.0 or for contractors VW 01067 – Sample Labels for Prototype
Parts v1.0.
If the example from table 8 is expanded to include a specific technical version (cf. Section 4.1.1),
the data content would look like this:
Generation "02H": [)>R
S06G
S37SUN123456789 5Q9945093A -02H+BA7654321R
S
EOT
Model phase "AS03": [)>R
S06G
S37SUN123456789 5Q9945093A -AS03+BA7654321R
S
EOT
4.1.3  
*******  


### 第 14 页
Page 14
VW 01067: 2017-11
Marking within the Volkswagen Group
Table 9 shows how data matrix contents are represented conforming to ISO/IEC standards (incl.
control characters).
Table 9 – Optical coding of prototype parts (within the Group)
Description
Data content
Start sequence
[)>
RS
R
S (ASCII)
Format identifier
06
GS
G
S (ASCII)
DI
37S
IAC
SC (marking within the Group)
Brand code + plant + cost center
*********
PN/Group
_5Q9945093
Separator
+
PSN
BA7654321
RS
R
S (ASCII)
EOT
EOT (ASCII)
Example: [)>R
S06G
S37SSC********* 5Q9945093A+BA7654321R
S
EOT
Figure 4 shows an example of a data matrix label structured accordingly:
Figure 4 – Example of data matrix label
NOTE 5: Sample labels are shown under the link for Volkswagen employees VW 01067 – Sam‐
ple Labels for Prototype Parts v1.0 or for contractors VW 01067 – Sample Labels for Prototype
Parts v1.0.
If the example from table 8 is expanded to include a specific technical version (cf. Section 4.1.1),
the data content would look like this:
Generation "02H": [)>R
S06G
S37SUN123456789 5Q9945093A -02H+BA7654321R
S
EOT
Model phase "AS03": [)>R
S06G
S37SUN123456789 5Q9945093A -AS03+BA7654321R
S
EOT
*******  


### 第 15 页
Page 15
VW 01067: 2017-11
RFID use for BZD in production
The electronic marking of production parts for the purpose of legally required BZD and/or installa‐
tion testing is realized using a DMC and/or RFID. The type of marking and the location of the mark‐
ing on the component are defined in the component drawing; for ASSYs without a PN, they are
defined in the Product Description Manual (PDM) sheet.
BZD: Each component is provided with a marking (DMC/RFID). Thus, the components can be allo‐
cated to VINs defined during the production process. This allows the vehicle components to be
documented and tracked, so that, in the event of a warranty claim (recall), the number of affected
vehicles can be narrowed down.
The documentation here relates to ASSYs. ASSYs here are understood to be vehicle components
or parts that are built together to form assemblies, which are subject to mandatory marking in the
sense of the present standard and are marked by means of a unique ASSY number. The accom‐
panying ASSY data is defined in the BG-ONLINE system. Instructions on mandatory marking can
be found in engineering drawings and TLD sheets.
The decision on which components are subject to mandatory BZD for each vehicle project is made
in the Product Team/Model Series Team based on the Process Standard 1.4_K-GQZ/I_01_PS
"Definition and Implementation of the BZD Scope in the Product Team." The status of the decision
on BZD is published by means of a special form of the TLD sheets TLD.011.xxx.B0 in the Engi‐
neering Data Management System (KVS).
The marking is a release-relevant component property and must be taken into account during the
sample inspection process. The following must be checked:
–
That the data can be recorded under production conditions
–
The contents of the applied data sequence
–
The type of marking on the component and its application
–
Retention period ≥ 15 years
The marking is checked during the sample inspection process. For this purpose, the supplier/man‐
ufacturer provides components that are marked the same way as they are during production. The
supplier/manufacturer ensures that the marking requirements described here are met during ongo‐
ing production (e.g., through random sample testing).
The retention period is at least 15 years from the moment the data is created (equivalent to CSD
class 7.2) The data must document important, quality-relevant individual information concerning
the marked vehicle component (e.g., batch of raw materials used, manufacturers of purchase parts
used, testing and setting values, place of manufacture and system), establish a clear link that
makes it possible to identify the correspondence between this information and the corresponding
reference data, and archive the information. If necessary, this will then make it possible to obtain
clear information on qualities with respect to function, manufacturing, and materials.
If the supplier manufactures an ASSY containing a component subject to mandatory BZD (e.g.,
fuel pump inside the fuel tank ASSY), the supplier must record the data for the component subject
to mandatory BZD, add the data to the ASSY's data, and retain this data in its documentation for at
least 15 years. This also applies to ASSYs made up of an electronic control unit and mechanical
components (e.g., headlamp with control module). If the requirements on the marking are not fulfil‐
led, the affected components are considered faulty and might not be usable.
Installation testing: Each component is provided with a marking (DMC/RFID). The coded data is
used in the ongoing production process to check the correct installation of a vehicle component
(technical design, age, manufacturer).
4.2  


### 第 16 页
Page 16
VW 01067: 2017-11
Layout of number ranges
The layout of the reference number for marking production parts is based on 4 number ranges,
which ensure that the number is unique; see table 10:
Table 10 – Layout of number ranges
 
Number range
Number of characters
1
CIN
9 characters (alphanumeric)
2
PN/Group
max. 14 characters (alphanumeric)
3
ASSY
max. 3 characters (alphanumeric)
4
PSN
max. 7 characters (alphanumeric)
 
Total number of characters
max. 40 characters (alphanumeric)
The number of characters for the CIN, PN, ASSY, and PSN must not exceed 32 characters (alpha‐
numeric) in total. Including additional characters required for representing suitable RFID data struc‐
tures (cf. section 4.2.2), this adds up to a data string of max. 40 characters (alphanumeric). This
character length corresponds to common memory sizes (240-bit UII) so that the hybrid marking of
parts can be ensured using RFID and DMC.
The owner of the CIN assigns the reference number and ensures that it is unique. The structure of
the PN corresponds to VW 01098. The PSN consists exclusively of uppercase letters and num‐
bers.
Note: The PSN must be formed independently from the PN and must be unique in combination
with the ASSY throughout the archival time period of 15 years.
An additional parameter is also stored on the RFID tag, see table 11.
Table 11 – Layout of number ranges
 
Number range
Number of characters
1
Manufacturer’s code
max. 4 characters (alpha‐
numeric)
RFID marking of production parts (BZD)
The following AFI is used within the control information (MB01): A1 (product reference). Table 3
shows the layout of the unique reference number within the UII (MB01).
Note: The layout of the BZD data sequence is described in the Group assembly catalog "BG-ON‐
LINE," cf. VW 01064, section 4.1 (<NAME_EMAIL>). In the present
case, BZD data with a 7-digit production number is assumed.
4.2.1  
4.2.2  


### 第 17 页
Page 17
VW 01067: 2017-11
Table 12 – Example of RFID data structure
 
Data content UII (MB01)
Number of characters
Value
1 DI
3 characters (alphanumeric)
37S
2 IAC
2 characters (alphanumeric)
UN (DUNS)
3 CIN
9 characters (numeric)
123456789
4 PN including
PN suffix + color code
max. 14 characters (alphanumeric)
5G4857705M RRA
5 Separator
1 character
*
6 ASSY
3 characters (alphanumeric)
Example: 209
7 Separator
1 character
+
8 PSN
max. 7 characters (alphanumeric)
4516616
 
Number of characters
max. 40 characters (alphanumeric)
240 bit
Alphanumeric representation:
37SUN1234567895G4857705M RRA*209+4516616
Note: If the color code is not used, the resulting empty spots are not filled in, i.e., the contents of
the data structure are as follows:
37SUN1234567895G4857705M*209+4516616!
After omitting the color code, sufficient memory is available in the UII area, i.e., the data structure
is terminated with EOT.
Table 13 shows the layout of the data in the UM:
Table 13 – Data content and structure for RFID
 
Data content UM (MB11)
Number of characters
Value
1
DSFID
2(hex)
03(hex)
2
Pre-Cursor (compaction code + rel. OID)
2(hex)
46(hex)
3a Byte count indicator switch
0(2)
 
3b Number of following bytes
5(10)
0+5 = 05(hex)
 
 
Data content UM (MB11)
Number of characters
Value
1
DI
1 character (alphanumeric)
V
2
Manufacturer’s code
3 – 4 characters (alphanumeric)
ABC
3
EOT
1 character (alphanumeric)
EOT (!)
 
Number of characters
max. 6 characters (alphanumeric)
36 bit
Optical marking of production parts (BZD)
The DMC as per VW 01064 is used for marking production parts. In addition to the classical label‐
ing, DPM is also possible. The DMC can have a square or also a rectangular design. For exam‐
ples, see table 14.
4.2.3  


### 第 18 页
Page 18
VW 01067: 2017-11
Table 14
Design
Square code symbol
Rectangular code symbol
Code symbol
Size in dots
24 x 24 dots
16 x 48 dots
Size in mm
without quiet zone
12.24 x 12.24
8.16 x 24.48
Size in mm
with quiet zone
16.24 x 16.24
12.16 x 28.48
The following quality requirements apply to the DMC:
–
Symbol quality as per ISO/IEC 15415 class B or 2 (label)
–
Symbol quality as per ISO/IEC TR 29158  is ≥ 3 (DPM)
–
ECC 200 error correction
–
Module size at least 0.50 mm
–
Printer resolution 300 dpi or higher
–
Quiet zone is at least 2 mm on each side
Note: Deviations from the quality standards specified above are permissible only in agreement with
all parties involved in the process. The supplier must verify compliance with the quality standards
and legibility of the DMC.
Table 15 – Example data content for DMC as per VW 01064
PN including PN suffix and color code
5G4857705M RRA
DUNS
123456789
ASSY
209
Manufacturer’s code
ABC
SN
4516616
Check digit
P (check digit)
Basic layout of the character string as per VW 01064:
#Part number#Part type#DUNS#Manufacture date*ASSY data*=Additional data                              
             
DMC coding of the example specified above:
#5G4857705M RRA##123456789#*209 ABC4516616P*=                                                                
 
For an example SmartLabel, see figure 5.


### 第 19 页
Page 19
VW 01067: 2017-11
Figure 5 – RFID SmartLabel with plain text and 2-D code
Additional details on the layout of the label and additional examples can be found in the document
"VW 01064 SampleLabels DE. Arbeitsstand.pptx."
The document can be found on the supplier platform http://www.vwgroupsupply.com >> Login >>
Information >> Divisions >> Production >> Radio Frequency Identification (RFID).
Marking of tools
Layout of number ranges
The layout of the unique reference number for marking tools complies with VW 34022 (based on
DIN 66277), and is based on two number ranges; see table 16:
Table 16 – Layout of number ranges
 
Number range
Number of characters
1
CIN
9 characters (alphanumeric)
2
Inventory/tool number
max. 18 characters (alphanumeric)
The layout of the CIN depends on whether the tools are marked by a contractor or within the
Group; see table 17:
Table 17 – Marking variants
 
 
Number range
Number of characters
1
By the
con‐
tractor
Supplier DUNS (D&B)
9 characters (numeric)
2
Within
the
Group
Brand code + plant
+ cost center
2 characters (alphanumeric) + 2 characters
(alphanumeric)
+ 5 characters (numeric)
The CIN owner assigns the inventory/tool number. The owner ensures that the reference number
is unique.
Optionally, the UM can be used to store additional payload; see VW 34022.
4.3  
4.3.1  


### 第 20 页
Page 20
VW 01067: 2017-11
RFID marking of tools
The following AFI is used within the control information (MB01): A1 (product reference).
Marking by suppliers
Table 18 shows the layout of the unique reference number within the UII (MB01):
Table 18 – Example of RFID data structure (contractor)
 
Data content UII
(MB01)
Number of characters
Value
1
DI
3 characters (alphanumeric)
25S
2
IAC
2 characters (alphanumeric)
UN (DUNS)
3
CIN
9 characters (numeric)
123456789
4
SN
max. 18 characters (alphanumeric)
ABC123456789012345
5
EOT
1 character (alphanumeric)
EOT (!)
 
Number of characters
max. 33 characters (alphanumeric)
198 bit
.
Alphanumeric representation:
25SUN123456789ABC123456789012345!
Marking within the Volkswagen Group
Table 19 shows the layout of the unique reference number within the UII (MB01):
Table 19 – Example of RFID data structure (within the Group)
 
Data content UII
(MB01)
Number of characters
Value
1
DI
3 characters (alphanumeric)
25S
2
IAC
2 characters (alphanumeric)
SC (marking within the
Group)
3
CIN
9 characters (alphanumeric)
*********
4
SN
18 characters (alphanumeric)
ABC123456789012345
5
EOT
1 character (alphanumeric)
EOT (!)
 
Number of characters
max. 33 characters (alphanumeric)
198 bit
Alphanumeric representation:
25SSC*********ABC123456789012345!
Optical marking of tools
The DMC is used for marking tools.
4.3.2  
*******  
4.3.3  
4.3.4  


### 第 21 页
Page 21
VW 01067: 2017-11
Marking by suppliers
Table 20 shows how data matrix contents are represented conforming to ISO/IEC standards (incl.
control characters).
Table 20 – Optical coding of tools (contractor)
Description
Data content
Start sequence
[)>
RS
R
S (ASCII)
Format identifier
06
GS
G
S (ASCII)
DI
25S
IAC
UN (DUNS)
CIN
123456789
SN
ABC123456789012345
RS
R
S (ASCII)
EOT
EOT (ASCII)
Example: [)>R
S06G
S25SUN123456789ABC123456789012345R
S
EOT
Figure 6 shows an example of a data matrix label structured accordingly:
Figure 6 – Example of data matrix label
Marking within the Volkswagen Group
Table 21 shows how data matrix contents are represented conforming to ISO/IEC standards (incl.
control characters).
Table 21 – Optical coding of tools (within the Group)
Description
Data content
Start sequence
[)>
RS
R
S (ASCII)
Format identifier
06
GS
G
S (ASCII)
DI
25S
IAC
SC (marking within the Group)
CIN
*********
SN
ABC123456789012345
*******  
*******  


### 第 22 页
Page 22
VW 01067: 2017-11
Description
Data content
RS
R
S (ASCII)
EOT
EOT (ASCII)
Example: [)>R
S06G
S25SSC*********ABC123456789012345R
S
EOT
Figure 7 shows an example of a data matrix label structured accordingly:
Figure 7 – Example of data matrix label
Marking of reusable containers
Layout of number ranges
The layout of the reference number for marking reusable containers is based on three number
ranges, which ensure that the number is unique; see table 22:
Table 22 – Layout of number ranges
 
Number range
Number of characters
1
CIN
9 characters (alphanumeric)
2
Container type
6 to 7 characters (alphanumeric)a)
3
SN
max. 9 characters (alphanumeric)
a)
Continuous text string, contains no spaces
The layout of the CIN depends on whether the reusable containers are marked by a contractor or
within the Group; see table 23:
Table 23 – Marking variants
 
Marking
CIN
Number of characters
1
By the contractor Supplier DUNS (D&B)
9 characters (numeric)
2
Within the Group
Brand code + plant
+ cost center
2 characters (alphanumeric) + 2
characters (alphanumeric)
+ 5 characters (numeric)
The owner of the CIN assigns the reference number and ensures that it is unique.
RFID marking of reusable containers
The following AFI is used within the control information (MB01): A3 (reusable container).
Marking by suppliers
Table 24 shows the layout of the unique reference number within the UII (MB01):
4.4  
4.4.1  
4.4.2  
4.4.2.1  


### 第 23 页
Page 23
VW 01067: 2017-11
Table 24 – Example of RFID data structure (contractor)
 
Data content UII
(MB01)
Number of characters
Value
1
DI
3 characters (alphanumeric)
26B to 29B
2
IAC
2 characters (alphanumeric)
UN (DUNS)
3
CIN
9 characters (numeric)
123456789
4
Container type
6 to 7 characters (alphanumeric)
A153097
5
Separator
1 character
+
6
SN
max. 9 characters (alphanumeric)
CS7148945
7
EOT
1 character (alphanumeric)
EOT (!)
 
Number of characters
max. 40 characters (alphanumeric)
240 bit
.
Alphanumeric representation:
26BUN123456789A153097+CS7148945!
The DI serves to differentiate between different types of containers; see table 25:
Table 25 – DI for reusable containers
 
DI
Container type
1
26B
Generic designation for reusable containers
2
27B
Large load carriers, pallets
3
28B
Small load carriers, self-supporting special packaging
4
29B
Auxiliary packaging (covers, separating layers, internal packaging)
Marking within the Volkswagen Group
Table 26 shows the layout of the unique reference number within the UII (MB01):
Table 26 – Example of RFID data structure (within the Group)
 
Data content UII
(MB01)
Number of characters
Value
1
DI
3 characters (alphanumeric)
26B to 29B
2
IAC
2 characters (alphanumeric)
SC (marking
within the Group)
3
CIN
9 characters (alphanumeric)
*********
4
Container type
6 to 7 characters (alphanumeric)
A153097
5
Separator
1 character
+
6
SN
max. 9 characters (alphanumeric)
CS7148945
7
EOT
1 character (alphanumeric)
EOT (!)
 
Number of characters
max. 40 characters (alphanumeric)
240 bit
.
4.4.2.2  


### 第 24 页
Page 24
VW 01067: 2017-11
Alphanumeric representation:
26BSC*********A153097+CS7148945!
The DI serves to differentiate between different types of containers; see table 27:
Table 27 – DI for reusable containers
 
DI
Container type
1
26B
Generic designation for reusable containers
2
27B
Large load carriers, pallets
3
28B
Small load carriers, self-supporting special packaging
4
29B
Auxiliary packaging (covers, separating layers, internal packaging)
Optical marking of reusable containers
The optical coding of reusable containers depends on the respective application scenario.
Code 128 is recommended for marking reusable containers, since it can achieve very large read
ranges. It is permissible to use DMCs for marking.
Marking by suppliers
Table 28 shows how code contents are represented conforming to ISO/IEC standards:
Table 28 – Optical coding of reusable containers (contractor)
Description
Data content
DI
26B
IAC
UN (DUNS)
CIN
123456789
Container type
A153097
Separator
+
SN
CS7148945
Example: 26BUN123456789A153097+CS7148945
Figure 8 shows an example of a bar code label structured accordingly:
Figure 8 – Example of a bar code label
4.4.3  
*******  


### 第 25 页
Page 25
VW 01067: 2017-11
Marking within the Volkswagen Group
Table 29 shows how code 128 is represented conforming to ISO/IEC standards:
Table 29 – Optical coding of reusable containers (within the Group)
Description
Data content
DI
26B
IAC
SC (marking within the Group)
CIN
*********
Container type
A153097
Separator
+
SN
CS7148945
Example: 26BSC*********A153097+CS7148945
Figure 9 shows an example of a bar code label:
Figure 9 – Example of a bar code label
Marking of packaged items
Layout of number ranges
The layout of the reference number for marking packaged items is based on two number ranges,
which ensure that the number is unique; see table 30:
Table 30 – Layout of number ranges
 
Number range
Number of characters
1
CIN
9 characters (alphanumeric)
2
Packaged item ID
9 characters (numeric)
The layout of the CIN depends on whether the packaged items are marked by a contractor or with‐
in the Group; see table 31:
*******  
4.5  
4.5.1  


### 第 26 页
Page 26
VW 01067: 2017-11
Table 31 – Marking variants
 
Marking
Number range
Number of characters
1
By the contractor Supplier DUNS (D&B)
9 characters (numeric)
2
Within the Group Brand code + plant
+ cost center
2 characters (alphanumeric) + 2
characters (alphanumeric)
+ 5 characters (numeric)
The CIN owner assigns the packaged item ID. The packaged item ID must not be repeated until
the number range from 000000001 to 999999999 has been used up for all Group plants.  Within
the Group, the owner ensures that the reference number is unique by adding the delivery slip date.
RFID marking of packaged items
The following AFI is used within the control information (MB01): A2 (transport units).
Among other things, the following variants are available for marking packaged items in MB01 as
per ISO 17365 (cf. ISO/IEC 15418):
–
1J (packaged item)
–
5J (mixed container)
–
6J (single-type container)
Implementation is described below using a simple packaged item as an example.
Marking by suppliers
Table 32 shows the layout of the unique reference number within the UII (MB01):
Table 32 – Example of RFID data structure (contractor)
 
Data content UII (MB01)
Number of characters
Value
1
DI
2 characters (alphanumeric)
1J
2
IAC
2 characters (alphanumeric)
UN (DUNS)
3
CIN
9 characters (numeric)
049977473
4
Packaged item ID
9 characters (numeric)
123456789
5
EOT
1 character (alphanumeric)
EOT (!)
 
Number of characters
max. 23 characters (alphanumeric)
138 bit
Alphanumeric representation:
1JUN049977473123456789!
4.5.2  
*******  


### 第 27 页
Page 27
VW 01067: 2017-11
Marking within the Volkswagen Group
Table 33 shows the layout of the unique reference number within the UII (MB01):
Table 33 – Example of RFID data structure (within the Group)
 
Data content UII
(MB01)
Number of characters
Value
1
DI
2 characters (alphanumeric)
1J
2
IAC
2 characters (alphanumeric)
SC (marking
within the Group)
3
Brand code + plant
+ cost center
9 characters (alphanumeric)
VW111620
4
Packaged item ID
9 characters (numeric)
123456789
5
EOT
1 character (alphanumeric)
EOT (!)
 
Number of characters
max. 23 characters (alphanumeric)
138 bit
Alphanumeric representation:
1JSC*********123456789!
Optical marking of packaged items
Code 128 is used for the optical marking of packaged items.
Marking by suppliers
The layout of the unique reference number and the representation of additional data contents is
based on the specifications contained in the Volkswagen Implementation Guideline Global Trans‐
port Label (GTL). Table 34 shows how code contents for the license plate are represented con‐
forming to ISO/IEC stands.
Table 34 – Optical coding of packaged items (contractor)
Description
Data content
DI
1J
IAC
UN (DUNS)
CIN
049977473
Packaged item ID
123456789
Example: 1JUN049977473123456789
*******  
4.5.3  
*******  


### 第 28 页
Page 28
VW 01067: 2017-11
Figure 10 shows an example of a GTL label structured accordingly:
Figure 10 – Example of GTL
Marking within the Volkswagen Group
Table 35 shows how code contents are represented conforming to ISO/IEC standards:
Table 35 – Optical coding of packaged items (within the Group)
Description
Data content
DI
1J
IAC
SC (marking within the Group)
Brand code + plant + cost center
*********
Packaged item ID
123456789
Example: 1JSC*********123456789
Figure 11 shows a Group bar code label structured accordingly:
Figure 11 – Example of a bar code label
*******  


### 第 29 页
Page 29
VW 01067: 2017-11
Marking of JIS packaged items
JIS packaged items are used for sequence processes and vehicle-based delivery processes. The
marking of these JIS packaged items is different from standard packaged items (see above). The
packaged items are marked using packaged item IDs. The packaged item IDs are transferred to
advanced ship notices (ASNs) in parallel (VDA 4987), and printed on the shipment documents
(VDA 4939).
Layout of number ranges
The layout of the reference number for marking packaged items is based on the following charac‐
ters, which ensure that the number is unique; see table 36:
Table 36 – Layout of number ranges
 
Number range
Number of characters
1
CIN
9 characters (alphanumeric)
2
Packaged item acronym
3 characters (alphanumeric)
3
Packaged item assembly line
2 characters (numeric)
4
Packaged item number
6 characters (numeric)
The CIN consists of the DUNS number (D&B) of the vehicle assembly plant. The packaged item
acronym is assigned by the vehicle assembly plant. The assembly line is transferred to the supplier
in the course of the call-offs in sync with production (DELJIT/Syncro or VDA 4986). The supplier
assigns the packaged item number, which establishes the uniqueness of the reference number
(000001 – 999999). After reaching 999999, the counting sequence starts again at 000001.
RFID marking of JIS packaged items
In addition to others, the following variants are available for marking packaged items as per
ISO 17365 (cf. ISO/IEC 15418):
–
3J (JIS packaged item, single)
–
4J (JIS packaged item, container)
Implementation is described below using a simple packaged item as an example. Table 37 shows
the layout of the unique reference number within the UII (MB01):
4.6  
4.6.1  
4.6.2  


### 第 30 页
Page 30
VW 01067: 2017-11
Table 37 – Example of RFID data structure (contractor)
 
Data content UII
(MB01)
Number of characters
Value
1
DI
2 characters (alphanumeric)
3J
2
IAC
2 characters (alphanumeric)
UN (DUNS)
3
CIN
9 characters (alphanumeric)
315016295
4
Packaged item acro‐
nym
3 characters (alphanumeric)
SIL
5
Packaged item assem‐
bly line
2 characters (alphanumeric)
03
6
Packaged item number
6 characters (alphanumeric)
001756
7
EOT
1 character (alphanumeric)
EOT (!)
 
Number of characters
max. 25 characters (alphanumeric)
150 bit
Alphanumeric representation:
3JUN315016295SIL01001536!
Optical marking of JIS packaged items
Code 128 is used for the optical marking of packaged items.
Example: 3JUN315016295SIL01001536
Figure 12 shows a bar code label structured accordingly:
Figure 12 – Example of a bar code label
Marking of vehicles
Layout of number ranges
The layout of the reference number for marking vehicles is based on two number ranges, which
ensure that the number is unique; see table 38:
4.6.3  
4.7  
4.7.1  


### 第 31 页
Page 31
VW 01067: 2017-11
Table 38 – Layout of number ranges
 
Number range
Number of characters
1
VIN
17 characters (alphanumeric)
2
FZN
11 characters (alphanumeric)
The layout of the reference number depends on whether the vehicles are marked as part of pre-
production tracking or as part of vehicle distribution (production); see table 39:
Table 39 – Marking variants
 
Marking
Vehicle marking
Number of characters
1
Vehicle
distribution
VIN
17 characters (numeric)
2
Pre-production
FZN
+ VIN
11 characters (alphanumeric)
+ 17 characters (alphanumeric)
The layout of the VIN for marking within vehicle distribution (production) corresponds to ISO 3779
and is assigned by the vehicle manufacturer.
Warning information
The VIN is considered personal information within the European Union (EU) and is thus subject to
the General Data Protection Regulation (EU-DSGVO), i.e., using the VIN requires a legal basis.
Therefore, the legal necessity or other need for the electronic read-out of the VIN on the vehicle
must be described, e.g., from the viewpoint of the manufacturer and retailer processes, and docu‐
mented in agreement with those responsible for data protection.
Within pre-production, vehicles (test mules) are marked with the FZN within the Group. The VIN is
not universally used within pre-production, meaning it is only employed as an additional, optional
attribute within vehicle marking. The vehicle manufacturer is responsible for ensuring that the refer‐
ence numbers are unique.
The data structures for vehicle marking are shown below.
RFID marking of vehicles
The following AFI is used within the control information (MB01): 90 (vehicle-based).
RFID marking of vehicles (vehicle distribution)
Table 40 shows the layout of the unique reference number within the UII (MB01). The layout corre‐
sponds to VDA 5520.
4.7.2  
4.7.2.1  


### 第 32 页
Page 32
VW 01067: 2017-11
Table 40 – Generic RFID data structure (vehicle distribution)
 
Data content UII
(MB01)
Number of characters
Value
1
DI
1 character (alphanumeric)
I
2
VIN
17 characters (alphanumeric)
WVWZZZ1JZ3W123456
3
EOT
1 character (alphanumeric)
EOT (!)
 
Number of characters
max. 19 characters (alphanumeric)
114 bit
Alphanumeric representation:
IWVWZZZ1JZ3W123456!
RFID marking of prototype vehicles (pre-production)
Table 41 shows the layout of the unique reference number within the UII (MB01) and the additional
use of UM (MB11):
Table 41 – Example of RFID data structure (pre-production)
 
Data content UII
(MB01)
Number of characters
Value
1
DI
2 characters (alphanumeric)
1Y
2
FZN
11 characters (alphanumeric)
VW462480574
3
EOT
1 character (alphanumeric)
EOT (!)
 
Number of characters
max. 14 characters (alphanumeric)
84 bit
 
 
Data content UM
(MB11)
Number of characters
Value
1
DSFID
2(hex)
03(hex)
2
Pre-cursor (compac‐
tion code + rel. OID)
2(hex)
46(hex)
3
Byte count indicator
switch
1(2)
‘0’(2)
4
Number of following
bytes
7(2)
N(2)
 
 
Data content UM
(MB11)
Number of characters
Value
1
DI
1 character (alphanumeric)
I
2
VIN
17 characters (alphanumeric)
WVWZZZ1JZ3W123456
3
EOT
1 character (alphanumeric)
EOT (!)
 
Number of characters
max. 19 characters (alphanumeric)
114 bit
Alphanumeric representation:
Unique item identifier (UII): 1YVW462480574!
*******  


### 第 33 页
Page 33
VW 01067: 2017-11
UM: IWVWZZZ1JZ3W123456!
Optical marking of vehicles
Optical marking of vehicles (vehicle distribution)
The optical coding and structure of labels for vehicle marking is based on VDA 5520.
Optical marking of prototype vehicles (pre-production)
Table 42 shows how data matrix contents are represented conforming to ISO/IEC standards (incl.
control characters).
Table 42 – Optical coding of vehicles (pre-production)
Description
Data content
Start sequence
[)>
RS
R
S (ASCII)
Format identifier
06
GS
G
S (ASCII)
DI
1Y
FZN
VW462480574
GS
G
S (ASCII)
DI
I
VIN
WVWZZZ1JZ3W123456
RS
R
S (ASCII)
EOT
EOT (ASCII)
Example: [)>R
S06G
S1YVW462480574G
SIWVWZZZ1JZ3W123456R
S
EOT
Figure 13 shows an example of a data matrix label structured accordingly:
Figure 13 – Example of data matrix label
4.7.3  
*******  
*******  


### 第 34 页
Page 34
VW 01067: 2017-11
Applicable documents
The following documents cited in this standard are necessary to its application.
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
VW 01014
Drawings; Drawing Frames and Text Macros
VW 01064
Module Marking on Production Vehicles; Build Status Documentation -
Codes on Mechanical Vehicle Parts
VW 01098
Part Number System
VW 10500
Company Designation, Marking of Parts; Guidelines for Use
VW 34022
Marking of Tools, Auxiliary Tools, Test Equipment, and Gages (Identifi‐
cation Plate); Requirements
DIN 66277
Information technology - Automatic identifcation and data capture techni‐
ques - Electronic identification plate
ISO/IEC 15415
Information technology - Automatic identification and data capture tech‐
niques - Bar code symbol print quality test specification - Two-dimen‐
sional symbols
ISO/IEC 15417
Information technology - Automatic identification and data capture tech‐
niques - Code 128 bar code symbology specification
ISO/IEC 15418
Information technology - Automatic identification and data capture tech‐
niques - GS1 Application Identifiers and ASC MH10 Data Identifiers and
maintenance
ISO/IEC 15434
Information technology - Automatic identification and data capture tech‐
niques - Syntax for high-capacity ADC media
ISO/IEC 15961-1
Information technology - Radio frequency identification (RFID) for item
management: Data protocol - Part 1: Application interface
ISO/IEC 15962
Information technology - Radio frequency identification (RFID) for item
management - Data protocol: data encoding rules and logical memory
functions
ISO/IEC 18000-63
Information technology - Radio frequency identification for item manage‐
ment - Part 63: Parameters for air interface communications at 860 MHz
to 960 MHz Type C
ISO/IEC 29160
Information technology - Radio frequency identification for item manage‐
ment - RFID Emblem
ISO/IEC TR 29158
Information technology - Automatic identification and data capture tech‐
niques - Direct Part Mark (DPM) Quality Guideline
ISO 17363
Supply chain applications of RFID - Freight containers
ISO 17364
Supply chain applications of RFID - Returnable transport items (RTIs)
and returnable packaging items (RPIs)
5  


### 第 35 页
Page 35
VW 01067: 2017-11
ISO 17365
Supply chain applications of RFID - Transport units
ISO 17366
Supply chain applications of RFID - Product packaging
ISO 17367
Supply chain applications of RFID - Product tagging
ISO 3779
Road vehicles - Vehicle identification number (VIN) - Content and struc‐
ture
VDA 4939
Shipment Documents; Version 3.1
VDA 4986
Datenübertragung von Produktionssynchronen Abrufen - Verfahrensbes‐
chreibung - Übertragung von Produktionssynchronen Lieferabrufdaten
per EDI mit EDIFACT und XML von Kunden an Lieferanten; Version 1.2
VDA 4987
Data Transfer of Despatch Advices - Process description - Transfer of
despatch advices by EDI with EDIFACT and XML; Version 1.3
VDA 5500
Basic Principles for RFID Application in the Automotive Industry; Version
1.2
VDA 5501
RFID for Container Management in the Supply Chain; Version 2.2
VDA 5509
AutoID/RFID-Application and Data Transfer for Tracking Parts and Com‐
ponents in the Vehicle Development Process; Version 2.4
VDA 5510
RFID for Tracking Parts and Components in the Automotive Industry;
Version 2.0
VDA 5520
RFID for vehicle identification in production, logistics and for service pur‐
poses; Version 2.0


### 第 36 页
Page 36
VW 01067: 2017-11
6-bit coding
Table A.1 – 6-bit coding table
Character
Binary
value
Charac‐
ter
Binary
value
Character
Binary
value
Character
Binary
value
Space
100000
0
110000
@
000000
P
010000
<EOT>
100001
1
110001
A
000001
Q
010001
<Reserved>
100010
2
110010
B
000010
R
010010
<FS>
100011
3
110011
C
000011
S
010011
<US>
100100
4
110100
D
000100
T
010100
<Reserved>
100101
5
110101
E
000101
U
010101
<Reserved>
100110
6
110110
F
000110
V
010110
<Reserved>
100111
7
110111
G
000111
W
010111
(
101000
8
111000
H
001000
X
011000
)
101001
9
111001
I
001001
Y
011001
*
101010
:
111010
J
001010
Z
011010
+
101011
;
111011
K
001011
[
011011
,
101100
<
111100
L
001100
\
011100
-
101101
=
111101
M
001101
]
011101
.
101110
>
111110
N
001110
<GS>
011110
/
101111
?
111111
O
001111
<RS>
011111
The following characters may be used to represent data content:
0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z.
Additional use of " " (space), + (plus), - (minus), * (asterisk), <EOT>, <RS>, <GS> as per specifica‐
tion.
Appendix A (normative)  


### 第 37 页
Page 37
VW 01067: 2017-11
Examples (reference number for prototype parts)
The following different valid variants are shown as examples (see figure B.1) to illustrate the struc‐
ture of the reference number, in particular the PN and the PSN:
Figure B.1 – Structure of the reference number
Appendix B (informative)  

