# VW_01110_2_EN_2018-12_螺栓连接_第2部分：安装和过程安全.pdf

## 文档信息
- 标题：
- 作者：
- 页数：41

## 文档内容
### 第 1 页
Group standard
VW 01110-2
Issue 2018-12
Class. No.:
61000
Descriptors:
CSD, IFH, MFU, assembly, assurance, category, joint, process, process assurance, residual torque,
retightening torque, screw, screw/nut driver system, screw/nut driver, test torque, threaded connection,
tightening speed
Threaded Connections
Part 2: Assembly and Process Assurance
Preface
At the time of this publication, the VW 01110 series of Volkswagen standards – under the general
title Threaded Connections – consists of the following parts:
–
Part 1: Design and Assembly Specifications
–
Part 2: Assembly and Process Assurance
–
Part 3: Parameter Settings for Monitored, Continuously Rotating Screw/Nut Driver Systems
–
Part 4: Testing and Evaluation of Threaded Connections
–
Part 5: Analysis Process for Complaints (draft)
Previous issues
VW 01110: 1975-10, 1993-12, 1998-12, 2001-10; VW 01110-2: 2008-07, 2012-07, 2015-03
Changes
The following changes have been made to VW 01110-2: 2015-03:
–
Section 2 expanded
–
Section 5 added
–
Former section 5.5 "Designation of parameters based on a multi-step assembly tightening pro‐
cedure" deleted
–
Section 6.1 and Appendix A revised
–
Table 1 revised and footers adapted
–
Section 6.3 revised
–
Section 6.4.2.1 revised
–
Section 6.4.2.2 revised
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature. A comma is used as the decimal sign.
The English translation is believed to be accurate. In case of discrepancies, the German version controls.
Page 1 of 41
Technical responsibility
The Standards department
Threaded Connection Technology
working group, Harald Möbus; see
appendix E
K-ILI/5 Tim Hofmann
K-ILI
Tel.: +49 5361 9 27995
Uwe Wiesner
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2018-11


### 第 2 页
Page 2
VW 01110-2: 2018-12
–
Section 6.4.3: Comment added
–
Table 2 revised
–
Section 7.2 revised and Table 3 added
–
Section 8.1: First paragraph specified in concrete terms
–
Section 8.2: Bullet points 5 and 7 revised
–
Section 8.5 "Requirements for auxiliary tools" added
–
Section 8.6 "Process assurance for manual assembly or emergency strategy" and Table 4
specified in concrete terms
–
Section 8.7.3.1 and Table 5 specified in concrete terms
–
Section ******* revised and Table 6 added
–
Section ******* specified in concrete terms
–
Section 8.7.3.4 revised
–
Section ******* specified in concrete terms
–
Section ******* added
–
Section ******* revised
–
Section ******* specified in concrete terms
–
Section 8.8 specified in concrete terms concerning rework
–
Section 8.9: Subsections "Determining the test torque" and "Determining the residual torque"
incorporated into Section 8.7.3
–
Section 8.10: Specifications added concerning the classification of threaded-connection data
–
Section 8.10.2.2 specified in concrete terms
–
Section 9 "Rework" subdivided and specifications on thread broaching revised
–
Table 9 and Table 10 revised
–
Section B.1: All tables revised
–
Section B.2 revised
–
Section B.3 specified in concrete terms
–
Standard edited and structure of the standard updated
Contents
Page
Scope ......................................................................................................................... 3
Definitions .................................................................................................................. 3
Abbreviations ............................................................................................................. 6
Designation ................................................................................................................ 7
Personnel qualifications ............................................................................................. 7
Assembly .................................................................................................................... 7
Classification of threaded connection types ............................................................... 7
Selection of possible screw/nut drivers ...................................................................... 8
Procurement and release of screw/nut driver systems ............................................ 10
Description of preferred assembly tightening procedures to be used ...................... 10
Specific characteristic values for threaded connections .......................................... 13
Influence of the tightening speed on manufacturability of the threaded
connection ................................................................................................................ 13
Test sequence for the use of screw/nut drivers ....................................................... 13
General information .................................................................................................. 13
1
2
3
4
5
6
6.1
6.2
6.3
6.4
6.5
6.6
7
7.1


### 第 3 页
Page 3
VW 01110-2: 2018-12
Target specifications for screw/nut drivers ............................................................... 13
Process assurance ................................................................................................... 16
General information .................................................................................................. 16
Requirements for the use of screw/nut drivers ......................................................... 16
Additional requirements for partially monitored screw/nut driver systems ............... 17
Additional requirements for monitored screw/nut driver systems ............................. 17
Requirements for auxiliary tools ............................................................................... 17
Process assurance for manual assembly or emergency strategy ............................ 17
Parameters for process assurance .......................................................................... 19
Adjustment work on threaded connection types of categories A and B ................... 23
Disassembly audits in the powertrain area .............................................................. 23
Threaded-connection data recording and documentation ........................................ 23
Rework ..................................................................................................................... 25
General information .................................................................................................. 25
Thread broaching ..................................................................................................... 25
Responsibilities ........................................................................................................ 26
Applicable documents .............................................................................................. 26
Bibliography ............................................................................................................. 27
Evaluation procedures for screw/nut drivers of categories A, B, or C ...................... 28
Detailed specifications regarding threaded-connection processes .......................... 29
Process assurance for threaded connections .......................................................... 29
Range limits for retightening torques ....................................................................... 34
Procedure for increasing the tightening speed ......................................................... 36
Checklists ................................................................................................................. 37
Checklist for threaded-connection workstation ........................................................ 37
Checklist for process optimization ............................................................................ 39
Comparison of assembly tightening procedures and production specifications
as per VW 01110-2: 2008-07 ................................................................................... 40
Responsible parties .................................................................................................. 41
7.2
8
8.1
8.2
8.3
8.4
8.5
8.6
8.7
8.8
8.9
8.10
9
9.1
9.2
10
11
12
Appendix A
Appendix B
B.1
B.2
B.3
Appendix C
C.1
C.2
Appendix D
Appendix E
Scope
This Volkswagen (VW) standard applies to the assembly of threaded connections with metric ISO
threads and non-metric threads in motor vehicles and assemblies. It also applies to process assur‐
ance with assembly tightening procedures that are standardized Group-wide. Retroactive applica‐
tion to existing systems/production equipment/sequence processes must be agreed upon with the
appropriate departments (Planning, Quality Assurance, and Production) for ongoing vehicle
projects, including major model upgrades (GPs).
NOTE 1: Requirements concerning the design, layout, and implementation of threaded connec‐
tions are defined in VW 01110-1.
Definitions
The following definitions apply when using this standard.
Line stop
A stop of the assembly line to allow assembly work to be carried out after the cycle time in the pro‐
duction cycle has elapsed
1  
2  


### 第 4 页
Page 4
VW 01110-2: 2018-12
Range limits for retightening torque
Upper and lower limits that determine the range of retightening torques evaluated as OK
False alarm rate
Frequency of false alarms triggered erroneously; EXAMPLE: A false alarm is when the measured
tightening torque value is outside of defined specifications, but does not receive a not-OK (NOK)
rating in a subsequent analysis of the threaded connection type.
Gradient monitoring
During the screw-tightening process, the differential quotient (gradient) of the torque/angle-of-rota‐
tion curve (ΔM/ΔW) is formed from the measured values supplied by the screw/nut driver. This gra‐
dient can be used for control and/or monitoring purposes.
Establishing capability (IFB)
The screw/nut driver is set and validated for the specific threaded connection type, taking into ac‐
count basic conditions and product influences.
Maintaining capability (IFH)
The repeat accuracy of the screw/nut drivers is checked at defined intervals.
Control parameters
Parameters that allow for an evaluation of the result achieved by the screw/nut driver by comparing
actual values against desired values
Line linking (line interconnection)
Linking (e.g., part entry or exit process on the assembly line) between product data and system
data, which is used to process an order (e.g., process specification) on a product-specific basis in
a defined production cycle, and to output a status message
Manual assembly
Threaded connection carried out with an unmonitored tool or a tool without line linking
Machine capability study (MFU)
The machine capability study is a qualitative evaluation of the stability and reproducibility of meas‐
urement results supplied by an assembly tool and evaluated on the basis of statistical methods. It
is performed using a suitable test tool by reference to the short-term dispersion, excluding product-
specific process influences, i.e., under consistent threaded connection type conditions. NOTE: See
also VDI/VDE 2645 Sheet 2 (VDI – Association of German Engineers; VDE – Association for Elec‐
trical, Electronic & Information Technologies).
Principle of multiple-party verification
Safeguarding of activities by involving several independent persons
Worker influence
Influence on the work result by worker behavior; EXAMPLE: In the case of threaded connections
assembled with a certain angle of rotation, worker influence is high if a support and gyroscope are
not used.


### 第 5 页
Page 5
VW 01110-2: 2018-12
Rework
The finishing of a threaded connection in a process deviating from the production process and in
line with specifications, by a specially trained and designated worker (not the worker responsible
for production assembly) using a defined tool NOTE: Carrying out a repeat tightening with a pro‐
duction tool during the assembly cycle is not considered rework.
Retightening torque
Lowest tightening torque measured when the already tightened screw (or nut) is turned further by a
few angular degrees
Retightening torque 1 (Mretighten1)
Retightening torque determined within 30 min after assembly
Retightening torque 2 (Mretighten2)
Retightening torque determined after dynamic or thermal loading (embedment) of the threaded
connection has taken place
Need-to-know-principle
Knowledge only as needed; i.e., information is provided only if it is immediately necessary for a
person to complete a specific task
Emergency strategy
The finishing of a threaded connection in line with specifications in the event that the production
tightening process is interrupted or breaks down, taking into account process assurance
Product-line type
Number of finished products of the same type
Test parameter
Parameter that allows a conclusion to be made on the quality of the threaded connection once it is
finished
Redundancy
Comparison of the measured values against those of a second, independent measuring circuit for
plausibility
Tightening curve
Graphical representation of the threaded connection, e.g., torque/angle-of-rotation curve
Screw/nut driver system
Screw/nut driver with a control system and the connections necessary for power supply and data
transmission
Screw/nut driver
Production equipment for the assembly of threaded connections


### 第 6 页
Page 6
VW 01110-2: 2018-12
Reliable process
Process in which the factors influencing the process are known and the required quality character‐
istics are kept within a controlled range by means of these factors
Screw yield point
Screw characteristic value (lower yield point or 0,2% offset proof stress), which refers to the stress
value up to which a threaded connection has no measurable plastic deformation under uniaxial and
low-torsion tensile load
Current-based plausibility
Evaluation of deviations between tightening torque desired and actual values based on the meas‐
ured motor current of the screw/nut driver
Systematic error
Unidirectional deviation identifiable as having been caused by measuring principles/measuring-
system errors
Transducer
Measured-value transmitter used between the screw/nut driver and the socket
Abbreviations
Machine capability index
Tightening torque, desired value
Calculated maximum tightening torque (reference value for the tightening
torque actual value) in torque-to-yield assembly (see VW 01126-2)
Calculated minimum tightening torque (reference value for tightening torque
actual value) in torque-to-yield assembly (see VW 01126-2)
Tightening torque, actual value
Retightening torque 1
Retightening torque 2
Tightening torque, upper limit (shutting-off)
Test torque
Residual torque
Tightening torque, threshold value
Tightening torque, upper limit value
Tightening torque, lower limit value
Tightening speed, desired value
Standard deviation
Time, actual value
Time, upper limit value
Time, lower limit value
Angle of rotation, desired value
Angle of rotation, actual value
Angle of rotation, upper limit (shutting-off)
Angle of rotation, upper limit value
Angle of rotation, lower limit value
Torque-controlled assembly tightening procedure
3  
Cmk
Mtighten
Mtighten, max
Mtighten, min
Mactual
Mretighten1
Mretighten2
Mupper
Mtest
Mresidual
Mthreshold
M+
M-
n
s
tactual
t+
t-
Atighten
Aactual
Aupper
A+
A-
AD method


### 第 7 页
Page 7
VW 01110-2: 2018-12
Threaded Connection Technology working group
Angle-controlled assembly tightening procedure
Carry-over part
Electronically commutated [motor] (brushless DC motor)
Electronic data processing
Failure modes and effects analysis
Establishing capability
Maintaining capability
Test passed
Classification System for Documents
Machine capability study
Not OK
Product Description Manual
Process capability study
Quality information system
Tabular drawing
Development
Technical Guideline for Documentation
Transfer ticket
Designation
Designation for threaded connections, e.g., in drawings:
Assembly and process assurance as per VW 01110-2
Personnel qualifications
A number of influences must be considered during assembly, which must be taken into account or
avoided in order to achieve "OK" results. This pertains not only to the machine and method, but
also to human and environmental influences. In addition to the specialized handling of screw/nut
drivers, knowledge for the evaluation of results achieved by the screw/nut driver, and knowledge of
the necessary documentation are also required. Guideline VDI/VDE-MT 2637 Sheet 1 contains the
basic principles for training, including periodic refresher courses and the corresponding documen‐
tation.
The content and level of qualification (familiarity, ability, mastery) must be defined and documented
within the appropriate department.
Assembly
Classification of threaded connection types
As per VDI/VDE 2862 Sheet 1, threaded connection types must be classified into categories A, B,
or C (see VW 01110-1, section "Categorization of threaded connection types"). The screw/nut driv‐
ers used must meet the requirements in table 1.
Threaded connection types of categories A and B require the use of monitored screw/nut driver
systems, which must be configured as per VW 01110-3. For category B, partially monitored
screw/nut driver systems may also be used if verification has been carried out and documented
according to the evaluation procedure in appendix A for screw/nut driver selection (for required
functionalities for partially monitored screw/nut driver systems, see section 8.3). This evaluation
AKV
AW method
COP
EC
EDP
FMEA
IFB
IFH
OK
CSD
MFU
NOK
PDM
PFU
QIS
TAB
E
TLD
WPK
4  
5  
6  
6.1  


### 第 8 页
Page 8
VW 01110-2: 2018-12
procedure must be carried out in agreement with the appropriate departments (Planning, Quality
Assurance, and Production). The documented result is part of the process release for partially
monitored screw/nut driver systems.
For threaded connections of category B, deviations from this requirement and the requirements lis‐
ted in table 1 are only permissible if the deviation(s) demonstrably do(es) not impair the quality of
the category-B threaded connection. The evaluation must be carried out by comparing the relative
number of failed assembly processes and quality impairments with and without the use of moni‐
tored screw/nut driver systems. Evidence of at least the same threaded-connection quality must be
provided by the party responsible at the respective plant. This evidence must be documented and
provided to the appropriate departments (Planning, Quality Assurance, and Production) for appro‐
val.
The use of a screw/nut driver with more monitoring than specified in table 1 for the respective
threaded connection type category is always permissible.
In all cases in which, following agreement between the appropriate departments (Planning, Quality
Assurance, and Production), the required screw/nut driver systems are not used for categories A
and B, the specifications in section 8.6 must be followed for process assurance.
Selection of possible screw/nut drivers
The selection of the screw/nut driver to be used in production must be made by the appropriate
Planning department as per table 1. For controlled screw/nut driver systems, the requirements in
Group Performance Specification "Controlled Screw/Nut Driver Systems" apply.
6.2  


### 第 9 页
Page 9
VW 01110-2: 2018-12
Table 1 – Requirement for screw/nut drivers in production and possible types of
documentation
 
 
Minimum requirement
 
Category as per
VDI/
VDE 2862 Sheet 1
Property
Actual
value
output
Status
output
Screw/nut drivera)
Type of documentationb)
A
Monitoredc)
X
X
Continuously rotating
screw/nut driver with con‐
trol system
Mretighten1 or
length change or
Mactual or
tightening curve or
Mtest
X
X
Pulse screw/nut driver
with control system
X
X
Electronic
torque wrench
Bd)
Monitoredc)
X
X
See category A
Mretighten1 or
length change or
Mactual or
tightening curve or
Mtest
Partially
monitorede)
—
X
Continuously rotating
screw/nut driver with con‐
trol system, but status out‐
put only
Mretighten1 or
Mtest or
length change
—
X
Pulse screw/nut driver with
control system,
but status output only
—
X
Electronic torque wrench
with only one transducer
C
Unmonitored
—
—
Click-type torque wrench
Mretighten1 or
Mtest or
MFU
—
—
Battery-powered screw/nut
driver
—
—
Pneumatic screw/nut driv‐
er
—
—
Pulse screw/nut driver
X Screw/nut driver meets this minimum requirement.
a)
For minimum requirements for the use of screw/nut drivers, see section 8.2. Line linking must be planned for on a product-specific
basis (applies particularly in the case of cordless screw/nut drivers).
b)
Monitoring of Mretighten1, Mactual, and Mtest values does not allow for any evaluation in terms of elastic or torque-to-yield assembly. Eval‐
uations of tightening curves are required for this.
c)
For minimum requirements, see section 8.4.
d)
For threaded connections of category B, under certain conditions, unmonitored tools of the category-C row can be used. For further
information, see section 8.6.
e)
For minimum requirements, see section 8.3.
The tools must be set to the nominal value (tightening torque, angle of rotation) (see section B.1);
deviations are necessary for pulse screw/nut drivers (see VW 10123).
Screw/nut driver correction factors must be used as needed to ensure that the product specifica‐
tions in the PDM, TAB, etc. are met by setting the tools to the nominal value (documentation re‐
quired).


### 第 10 页
Page 10
VW 01110-2: 2018-12
Procurement and release of screw/nut driver systems
The minimum requirements in Group Performance Specification "Controlled Screw/Nut Driver Sys‐
tems" must be met before the screw/nut driver systems are procured. This also includes the re‐
lease as per the test plan enclosed in that document. The list of released screw/nut driver systems
is available on the Threaded Connection Technology working group (AKV) portal.
Description of preferred assembly tightening procedures to be used
Torque-controlled assembly tightening procedure
In the AD18 procedure, the tightening torque (tightening torque desired value Mtighten) is the speci‐
fied control variable. If possible, the angle of rotation must be monitored.
Figure 1 provides a graphical representation of the curve progression and the parameters.
Legend
Mtighten
Tightening torque, desired value
Mactual
Tightening torque, actual value
Mthreshold
Tightening torque, threshold value
M+
Tightening torque, upper limit value
M-
Tightening torque, lower limit value
n
Tightening speed, desired value
tactual
Time, actual value
t+
Time, upper limit value
t-
Time, lower limit value
Aactual
Angle of rotation, actual value
Aupper
Angle of rotation, upper limit (shut‐
ting-off)
A+
Angle of rotation, upper limit value
A-
Angle of rotation, lower limit value
Figure 1 – Torque-controlled assembly tightening procedure (e.g., AD18 procedure)
Due to the dispersion of friction, this procedure typically has a large preload force dispersion, even
with a small dispersion of the applied tightening torque (tightening torque actual value Mactual).
The applied tightening angle of rotation (angle-of-rotation actual value Aactual) can be used as a ref‐
erence point for the preload force dispersion with monitored screw/nut driver systems.
6.3  
6.4  
6.4.1  


### 第 11 页
Page 11
VW 01110-2: 2018-12
Angle-controlled assembly tightening procedure
AW11 procedure (torque-to-yield assembly)
The angle-of-rotation control is the standard procedure for torque-to-yield assembly. Figure 2 pro‐
vides a graphical representation of the curve progression and the parameters.
Legend
Mactual
Tightening torque, actual value
Mupper
Tightening torque, upper limit (shut‐
ting-off)
Mthreshold
Tightening torque, threshold value
M+
Tightening torque, upper limit value
M-
Tightening torque, lower limit value
n
Tightening speed, desired value
tactual
Time, actual value
t+
Time, upper limit value
t-
Time, lower limit value
Atighten
Angle of rotation, desired value
Aactual
Angle of rotation, actual value
A+
Angle of rotation, upper limit value
A-
Angle of rotation, lower limit value
Figure 2 – AW11 procedure (angle-controlled torque-to-yield assembly tightening procedure)
The target variable is a specified tightening angle of rotation (angle-of-rotation desired value Atighten)
which is counted starting from a particular pre-tightening torque (tightening torque threshold value
Mthreshold). The threaded connection is tightened to the screw yield point in a defined manner by pre-
tightening to below the screw yield point and then applying the angle of rotation. The respective
screw strength and the thread friction determine the attainable preload force. The influence of fric‐
tion beneath the screw head on the preload force is no longer present if the parameters for Mthreshold
and Atighten are selected such that the screw yield point is reliably achieved (see VW 01110-4).
There will be a small preload force dispersion with a simultaneously larger dispersion of the result‐
ing final torque (tightening torque actual value Mactual). The procedure is most easily checked by
plotting the torque/angle-of-rotation curve. The screw yield point is exceeded in the upper area of
the torque/angle-of-rotation curve. This is characterized by a curve that flattens out.
6.4.2  
6.4.2.1  


### 第 12 页
Page 12
VW 01110-2: 2018-12
A support should be used for angle-controlled assembly tightening procedures that involve hand‐
held tools. If no support is used, the appropriate departments (Planning, Quality Assurance, and
Production) must ensure that the assembly specifications are met (torques, angles of rotation,
elastic, torque-to-yield).
AW12 procedure (assembly below the screw yield point)
In certain applications in which the screw yield point must not or cannot be exceeded, it is still pref‐
erable to use an angle-controlled tightening procedure. In these cases, the AW12 procedure is
suitable. It must be carried out such that the screw yield point is not reached. A reduction in the
friction influences results in a smaller preload force dispersion than in the AD18 procedure; howev‐
er, the dispersion of the AW11 procedure is not reached.
Pulse-controlled assembly tightening procedure
Pulse screw/nut driver systems can be used with the AD18 procedure. Monitored pulse screw/nut
drivers can also be used with AW procedures. For technical reasons, the target specifications
when using pulse screw/nut driver systems are fulfilled with different parameter settings. Therefore,
the use of pulse screw/nut driver systems must be agreed upon in advance between Production,
the appropriate Design Engineering department, and Quality Assurance.
When dimensioning pulse screw/nut driver, it must be noted that losses, e.g., due to extensions
(torsion bar), pressure fluctuations in the air supply lines, or associated oscillating masses on the
side of the threaded connection, can lead to a significant reduction in the maximum torque speci‐
fied by the manufacturer.
NOTE 2: In the case of soft threaded connection types, the angle-controlled assembly tightening
procedure may take a very long time, because the tightening speed is significantly reduced again
after reaching the screw yield point.
In these cases, the use of the pulse-controlled assembly tightening procedure (ASI10) is recom‐
mended. In this procedure, the screw yield point is reliably exceeded with a specified number of
pulses NN starting from the pre-tightening torque (tightening torque threshold value Mthreshold). If di‐
mensioning for the pulse screw/nut driver is adequate, a more or less constant pulse level will re‐
sult during final joining. This level will equal the tightening torque in the torque-to-yield range. The
angular measurement is used here as an additional control parameter and for a significant differen‐
tiation between the threaded connection types in the torque-to-yield range. In these cases, the
number of pulses starting from the pre-tightening torque will be specified for the softest threaded
connection type in each case.
Additional methods for threaded connection type monitoring
In addition, the screw/nut driver systems offer many options for tightening in a proper and reliable
manner on a case-by-case basis.
Examples include:
–
Screw-on running torque monitoring of prevailing torque type nuts
–
Gradient monitoring for stiff assembly parts
–
Monitoring of length of engagement for very long threads and thread-forming tapping screws
6.4.2.2  
6.4.3  
6.4.4  


### 第 13 页
Page 13
VW 01110-2: 2018-12
Specific characteristic values for threaded connections
Table 2 shows specific characteristic values for threaded connections (e.g., for thread-forming tap‐
ping screws, prevailing torque type nuts):
Table 2 – Explanation of the specific characteristic values for threaded connections
Threaded connection char‐
acteristic value
Symbol
Explanation
Tapping torque
Mtapping (Mscrew-
in)
Screw-in torque for self-tapping screws (see, e.g., VW 01127)
Screw-on running torque
Screw-off running torque
Mscrew-on running
Mscrew-off running
Torque for screw-on or screw-off without a preload force (e.g., for prevailing
torque type nuts or for chemical threadlocking)
Tear-off torque
Mtear-off
Torque that causes the screw to tear off (under preload force)
Over-tightening torque
Mover-tighten
Torque that will cause the destruction of the threads (e.g., with self-tapping
screws, nuts).
Influence of the tightening speed on manufacturability of the threaded connection
The joining of threaded connections requires time for adjusting components, surfaces, seals, corro‐
sion protection waxes, etc., to ensure a reliable assembly process and to prevent greater losses of
preload. The tightening speeds therefore have a direct influence on the degree of the achieved as‐
sembly and residual preload forces. As per VW 01129, release tests for new surface protection
types are conducted at 200 rpm during the screw-in phase and 20 rpm during the final tightening
phase. These speeds have also proven effective at eliminating stick-slip effects. Higher tightening
speeds are possible during production. For threaded connections of categories A and B, the tight‐
ening speed influence must be determined and evaluated by an appropriate department based on
statistical methods. Section B.3 describes the procedure.
If higher tightening speeds verified by tests during production (product and process) have proven
to be successful with COP threaded connection types1), it is permissible to adopt these tightening
speeds.
Test sequence for the use of screw/nut drivers
General information
Section B.1 describes the test sequence for the use of screw/nut driver systems, as well as type-
approved or alternatively tested screw/nut drivers. Their use in production is only permissible fol‐
lowing release by the appropriate department. To that end, an acceptance with specifications for
assembly and control parameters must be carried out and documented.
Target specifications for screw/nut drivers
A screw/nut driver must achieve a machine capability index Cmk ≥ 1,67 (with a tolerance of ±15%).
This statement is only based on recording at a single point in time and therefore cannot be used to
draw any conclusions as to the long-term stability of the screw/nut driver. This applies in particular
to special drives such as geared offset heads for which a machine capability index of only
Cmk ≥ 1,0 is possible and permissible. In addition, special drives have increased wear and must
6.5  
6.6  
7  
7.1  
7.2  
1)
For COP threaded connection types, all parts to be joined and technical specifications are identical.


### 第 14 页
Page 14
VW 01110-2: 2018-12
therefore be checked, greased, and serviced more often. The scope and cycles of this mainte‐
nance must be documented. Documentation of the actual values only is not sufficient.
The frequency of the IFH is based primarily on the screw/nut driver supplier's specifications, or
else on the recommendations provided in table 3. Screw/nut driver systems with intrinsic safety
measures are equipped with redundant measuring circuits (current plausibility as an alternative or
additionally). The results of these circuits are compared. When using systems like this, the periodic
IFH or MFU can be carried out at longer intervals.
Prerequisites for extension of the IFH test interval for controlled screw/nut driver systems:
–
At the time of procurement, the screw/nut driver systems were in compliance with Group Per‐
formance Specification "Controlled Screw/Nut Driver Systems"
–
Self-test of all system components relevant to the control variables and monitoring variables
–
Clearly specified tools, including sockets, extensions, etc. from qualified suppliers
–
Parameter settings for the screw/nut driver systems as per the requirements in VW 01110-3
–
The screw/nut driver is operated in the working range specified by the manufacturer.


### 第 15 页
Page 15
VW 01110-2: 2018-12
Table 3 – Intervals of IFH testing for controlled screw/nut driver systems
No
.
Controlled screw/nut driver system
IFH testing interval
 
Type
Drive
form
with current-based plausibility
without current-based plausi‐
bility
 
 
 
Time
Number of
threaded
connec‐
tions
Time
Number of
threaded
connec‐
tions
1
Straight
drive
Long checking in‐
terval
1 million
Long checking
interval
1 million
Angled
head
Medium checking
interval
0,5 million
Medium check‐
ing interval
0,5 million
Special
drive
Short checking in‐
terval
≤ 0,1 mil‐
lion
Short checking
interval
≤ 0,1 mil‐
lion
2
Straight
drive
Long checking in‐
terval
2 million
Long checking
interval
1 million
Angled
head
Medium checking
interval
1 million
Medium check‐
ing interval
0,5 million
Special
drive
Short checking in‐
terval
≤ 0,1 mil‐
lion
Short checking
interval
≤ 0,1 mil‐
lion
3
Straight
drive
Medium checking
interval
0,5 million
Medium check‐
ing interval
0,5 million
Angled
head
Short checking in‐
terval
0,25 mil‐
lion
Short checking
interval
0,25 million
Special
drive
Short checking in‐
terval
≤ 0,1 mil‐
lion
Short checking
interval
≤ 0,1 mil‐
lion
4
Straight
drive
Long checking in‐
terval
1 million
Short checking
interval
≤ 0,1 mil‐
lion
Angled
head
Medium checking
interval
0,5 million
≤ 0,1 mil‐
lion
Special
drive
Short checking in‐
terval
≤ 0,1 mil‐
lion
≤ 0,1 mil‐
lion
5
Straight
drive
Medium checking
interval
0,5 million
Short checking
interval
≤ 0,1 mil‐
lion
Angled
head
Short checking in‐
terval
0,25 mil‐
lion
≤ 0,1 mil‐
lion
Special
drive
Short checking in‐
terval
≤ 0,1 mil‐
lion
≤ 0,1 mil‐
lion
A Current source
M Motor
T Transducer


### 第 16 页
Page 16
VW 01110-2: 2018-12
Process assurance
General information
A tightening process can be considered a reliable process if, after a sufficient start-up phase, pro‐
cessing fluctuations can be identified by verifying the control and test parameters and, at the same
time, the false alarm rate has been minimized.
Each threaded connection is characterized by the totality of its parameters and its manufacturing
influences. For production assembly processes, the assembly tightening procedures as per
VW 01110-1, table "Design requirement (minimum functional requirement)," and the safety docu‐
mentation (see section 8.10.4), including the process parameters, must be taken into account for
purposes of process assurance. In addition, specifications are necessary in the form of Work In‐
structions or other documents (see also "Threaded connection workstation checklist" in
section C.1), which pertain to production equipment and components as well as personnel, parts
handling, process assurance, line linking, and rework.
Requirements for the use of screw/nut drivers
–
Personnel must be trained and instructed; see section 5.
–
The production equipment used (screw/nut drivers) must be adequately dimensioned and er‐
gonomically usable in terms of the specified assembly tightening procedure (for further re‐
quirements, see VW 01126-2).
–
The checking intervals and the tool changes must be selected such that any decrease in the
machine capability due to wear can be counteracted at an early stage.
–
A uniform and sufficient power supply must be ensured.
–
Finished threaded connections must be checked by random sample at periodic intervals, e.g.,
as per table 1. A test plan must be prepared for this; see section *******.
–
Suitable measures to prevent missed (i.e., non-tightened) threaded connections must be im‐
plemented (e.g., line linking, line stop, "OK" counting, position detection). The appropriate de‐
partments (Planning, Production, and Quality Assurance) will decide jointly on implementation
of the measure.
–
Loosening a threaded connection during the production process is only permissible if an
"NOK" status is present (does not apply to disassembly activities in the context of rework).
–
An emergency strategy must be defined for the event of a failure of the screw/nut driver sys‐
tem.
–
For "NOK" cases, a detailed rework strategy must be available that takes into account the re‐
spective error message.
For further requirements, see Group Performance Specification "Controlled Screw/Nut Driver Sys‐
tems."
8  
8.1  
8.2  


### 第 17 页
Page 17
VW 01110-2: 2018-12
Additional requirements for partially monitored screw/nut driver systems
–
Keeping-count function (visual indication when an individual OK result and an overall OK result
is achieved)
–
A directly or indirectly measured or effective control variable (not time)
–
NOK indication when a new tightening attempt is performed on an already-OK connection
–
Overall OK signal can be picked up by a higher-level control (e.g., line control)
Additional requirements for monitored screw/nut driver systems
Monitored screw/nut driver systems record the actual values by means of sensors during the tight‐
ening procedure and carry out a desired/actual value comparison. In addition, essential control pa‐
rameters such as the screw-in torque and angle of rotation must be recorded and referred to for
the evaluation of the results achieved by the screw/nut driver.
Requirements for auxiliary tools
The designated purpose, threaded connection type, and, if applicable, the set torque must be indi‐
cated on auxiliary tools. Color marking of auxiliary tools may be helpful for distinguishing manufac‐
turing and test tools. Rotating tools must be calibrated.
For auxiliary tools used for loosening, another way to distinguish them is to use auxiliary tools that
have loosening functionality only.
Process assurance for manual assembly or emergency strategy
For tools without line linking (manual assembly), multiple-party verification as per table 4 is re‐
quired as a process assurance measure for categories A and B. This also applies to threaded con‐
nections of category A when unmonitored tools are used. If unmonitored tools are used for threa‐
ded connections of category B, although the classification as per section 6.1 requires the use of
monitored or partially monitored tools, multiple-party verification must also be carried out as proc‐
ess assurance measure. If technically feasible, the work steps as per table 4 for worker 1 and
worker 2 must be performed at two separate workstations. Click-type torque wrenches (set to test
torque Mtest, see no. 2 and no. 4 in table 4) must be clearly labeled and marked in a reliable man‐
ner (e.g., with different colors) for differentiation of assembly tools.
The emergency strategy must be limited to the shortest possible period of time. To ensure compli‐
ance with this specification, the Maintenance department must keep at least one equivalent re‐
placement for the production tool at hand or available. Beginning and ending of the emergency
strategy period must be documented on a product-specific basis and archived in accordance with
the status of the threaded connections (see section 8.10.3).
Deviations from the specifications in this section that lead to the same result are permissible in
agreement with the appropriate departments (Planning, Quality Assurance, and Production), and
must be documented.
8.3  
8.4  
8.5  
8.6  


### 第 18 页
Page 18
VW 01110-2: 2018-12
Table 4 – Work steps for the individual assembly tightening procedures for process
assurance with manual assembly or emergency strategy (without documentation of
product-specific threaded-connection data)
No.
Assembly
tightening
procedure
With
monitored and
partially monitored
screw/nut driver
With
chemical
threadlocking
Work step
 
 
 
 
Worker 1
Worker 2
1
AD18
 
 
Tighten to Mtighten as per
Development specifications.
Retorque to Mtighten again as
per Development specifica‐
tions and documenta) the
check (e.g., stamp on the
transfer ticket, EDP)
A color marking can addition‐
ally be applied to support the
process.
No
No
 
 
Yes
No
2
AD18
 
 
Tighten to Mtighten as per
Development specifications.
Check with test torque Mtest
(see section *******) and
documenta) the check (e.g.,
stamp on the transfer ticket,
EDP)
A color marking can addition‐
ally be applied to support the
process.
No
Yes
 
 
Yes
Yes
3
AW11 and
AW12
No
Yes
No
1. Tighten to Mthreshold as per
Development specifications
and mark.
2. Apply Atighten as per
Development specifications.
Apply a new color marking
and documenta) the check
(e.g., stamp on the transfer
ticket, EDP)
4
AW11 and
AW12
Yes
Yes
No
Tighten to Mthreshold and with
Atighten as per Development
specifications.
Check with test torque Mtest
(see section *******) and
documenta) the check (e.g.,
stamp on the transfer ticket,
EDP)
A color marking can addition‐
ally be applied to support the
process.
a)
As long as the mandatory multiple-party verification is strictly observed, worker 2 is allowed to document the entire tightening proce‐
dure during the emergency strategy period, i.e., initial tightening by worker 1 and check tightening by worker 2.


### 第 19 页
Page 19
VW 01110-2: 2018-12
Parameters for process assurance
Assembly parameters and their specification
Assembly parameters are the target specifications determined by the design engineer and publish‐
ed in the PDM sheet, TAB, or drawing. The specification of assembly parameters is described in
VW 01110-1, "Determining the nominal thread diameter, property class, and assembly tightening
procedure" section.
Control parameters and their specification
Control parameters are used for threaded connection type monitoring in order to detect threaded
connection errors.
Tightening torques or angles of rotation in the tightening phases are the control parameters for
monitored and, to a limited extent, also for partially monitored screw/nut driver systems (see
VW 01110-3). Parameters are set as per VW 01110-3.
Test parameter
General information
The test parameters (see table 5) are determined on threaded connections that have been assem‐
bled with OK results, and may differ from the assembly parameters specified in the drawing.
With retightening, test, and residual torques, changes (trends) in the process or product can be de‐
tected.
Table 5 – Explanation of the test parameters
Test parameter
Symbol
Explanation
Retightening torque 1
Mretighten1
Prior to initial operating stress; see section *******
Lower range limit
Mretighten1, min
Minimum permissible value
Upper range limit
Mretighten1, max
Maximum permissible value
Retightening torque 2
Mretighten2
Subsequent to initial operating stress; see section *******
Lower range limit
Mretighten2, min
Minimum permissible value
Upper range limit
Mretighten2, max
Maximum permissible value
Test torque
Mtest
Defined torque for testing; see section *******
Residual torque
Mresidual
See section *******
Test frequency after process release
Responsibilities, the threaded connections to be tested, the quality characteristics to be tested, the
used test procedures, the test frequency, the random-sample size, and the type of documentation
must be agreed upon between Planning, Production, and Quality Assurance, and documented in a
test plan.
Typically, only systematic errors can be revealed with random-sample testing.
8.7  
8.7.1  
8.7.2  
8.7.3  
8.7.3.1  
*******  


### 第 20 页
Page 20
VW 01110-2: 2018-12
Prerequisites for reducing the test frequency indicated in table 6:
–
The threaded connections meet the requirements as per VW 01110-4.
–
If monitored screw/nut driver systems are available, these have been configured as per the
specifications in VW 01110-3.
–
The threaded connection process has proven stable; i.e., potential influences by the worker
have no effect on the quality of the threaded connection.
–
Experience with comparable threaded connection types from other vehicle projects allow con‐
clusions to be drawn regarding the robustness of the threaded connection.
–
Process stability has been statistically verified.
Table 6 – Frequency of retightening-torque testing
Category
Frequency of retightening-torque testing
 
Low worker influence during the tightening pro‐
cedure
High worker influence during the tightening pro‐
cedure
 
Start
Minimum
Start
Minimum
A
1 random sample/shift
1 random sam‐
ple/month
1 random sample/shift
1 random sam‐
ple/week
B
1 random sample/shift
1 random sam‐
ple/month
1 random sample/shift
1 random sam‐
ple/week
Ca)
1 random sam‐
ple/week
0b)
1 random sam‐
ple/week
0b)
One random sample should comprise 5 measurements. Deviations due to a high number of variants and/or reduced parts
availability are permissible in agreement with the appropriate departments (Planning, Quality Assurance, and Production)
and must be documented.
a)
Only threaded connections based on the friction-locking principle are measured. Threaded connections with positive locking, e.g.,
using plastic screws, are not measured.
b)
Threaded connections of category C that are subject to Technical Guideline Documentation (TLD) (see TLD homepage) must be
measured with a minimum test scope on one random sample per month.
If other process assurance measures are carried out, e.g., a tightness test on 100% of components
in threaded connections of piping, the test frequency can also be reduced.
The process monitoring for threaded connections is always carried out by means of Mretighten1 meas‐
urements. If required, additional Mretighten2 measurements can be carried out at an interval that is
reasonably defined (see also section 8.9). A deviating procedure applies to disassembly audits
(see section 8.9), in which typically only Mretighten2 measurements are carried out.
NOTE 3: Evaluations of tightening curves from the initial threaded-connection assembly of the
screw are permissible, as an alternative to product testing, based on VW 01110-4.
Determining the retightening torque
The Mretighten value is the lowest torque measured when the screw (or nut) is turned further by a few
angular degrees; see figure 3 and figure 4.
In many cases, static friction effects often lead to a torque peak, known as the breakaway torque.
However, this must not be interpreted as a retightening torque; see figure 4. With large grip ranges,
two breakaway processes may occur: one beneath the head and along the thread. In such cases,
the retightening torque must be determined after the second breakaway.
*******  


### 第 21 页
Page 21
VW 01110-2: 2018-12
Deviations are permissible in agreement with the appropriate departments (Planning, Quality As‐
surance, and Production) and must be documented.
Figure 3 – Determining the retightening
torque
Figure 4 – Static friction effects when
determining the retightening torque
Legend
M
Torque
Mretighten
Retightening torque
Mbreakaway
Breakaway torque
A
Angle of rotation (time)
Range limits for retightening torques
Definition of range limits for retightening torques is required for validating tightening processes.
Values taken from the respective design specifications form the basis and must be confirmed by
product testing. The range limits are determined as per section B.2.
Validation by means of retightening torque must be divided into different stages, as this is influ‐
enced by various basic conditions. While not many empirical values are available for new threaded
connection types, and therefore assumed initial values must be used, a large database of results
achieved by the screw/nut driver systems and their tightening curves from monitored assembly
and/or already-determined retightening torques will become available over the course of produc‐
tion.
The assumed start values are optimized at a later time with the aid of a statistical method (frequen‐
cy distribution ±(2 s to 3 s) values). The aim of this measure is to take into account different proc‐
ess-related influences (commonly referred to as the "5 Ms" – machine, method, material, man, and
measurement). This allows for a preferable accuracy in terms of range narrowing, in order to distin‐
guish actual errors from ostensible errors. However, because legal requirements must also be met
with the retightening torques, the method for defining ranges must be documented.
Details on determining the retightening torque
For torque-to-yield assembly, the range limits cannot be derived solely from statistical evaluations,
because the screw strength and the coefficient of friction also have an influence on the achieved
tightening torque as additional parameters. The range limits must also be guided by the
Mtighten min/Mtighten max values from VW 01126-2, table "Assembly preload force/tightening torque".
In individual cases, it might be necessary to additionally determine and statistically evaluate the
tightening torques Mactual in the same manner for a correct evaluation.
When using click-type torque wrenches for assembly, unchecked further tightening might lead to a
skewed distribution. This results in high dispersions, but this is not necessarily a disadvantage.
8.7.3.4  
*******  


### 第 22 页
Page 22
VW 01110-2: 2018-12
If chemical threadlocking is used (e.g., liquid adhesive, micro-encapsulated adhesive), a determi‐
nation of retightening torques is not permissible, or else the adhesive bond will be destroyed.
Only test torques (see table 5 and section *******) may be applied (for cyclical testing only).
If mechanical screw retainers are used (e.g., locking serration, non-metallic inserts), the application
of test torques is preferred.
NOTE 4: The adhesives will already have cured during assembly.
Measuring the retightening torque (Mretighten1) by means of continuously rotating and
measuring screw/nut driver equipment (EC screw/nut drivers)
The automated measurement of retightening torques with the aid of monitored screw/nut driver
systems (EC screw/nut drivers) is generally possible at screw-on points where hand-held or sta‐
tionary screw/nut driver systems are used for measuring. Screw/nut driver systems are assigned to
assembly/production systems by definition and are therefore subject to different test requirements
than measuring systems. However, to enable the use of screw/nut driver systems as a measuring
tool and thus for the measurement of retightening torques, the following conditions must be met:
–
The measuring inaccuracy of the measuring chain is ≤ 1% for the torque measurement.
–
The measuring inaccuracy of the measuring chain is ≤ 1° for the angle-of-rotation measure‐
ment.
–
The resolution of the angle of rotation is ≤ 0,25°.
Requirements for configuration and the necessary infrastructure are available in the form of a best-
practice document on the AKV portal.
Comparability of retightening torques
Because the basic conditions (environment, screw/nut driver and testing system used, etc.) may
vary from plant to plant, it is not possible to utilize the test parameters determined in one plant at
another plant, even for the same threaded connection types.
Determining the test torque
Test torques are used, e.g., to test threaded connections in which chemical threadlocking is used
to prevent the screws from loosening.
The test torque is used to check whether a screw assembly was successful and whether an angle
of rotation was applied, if applicable. It provides a qualitative statement only.
For the AD18 procedure, the test torque is 80% of the tightening torque; for the AW11 procedure, it
is 90% of the calculated minimum tightening torques as per VW 01126-2. For the AW12 procedure,
the test torque must be set to 1,2 × tightening torque threshold value Mthreshold.
Up until the test torque is reached in the direction of tightening, no head torsion is permissible.
Determining the residual torque
Figure 5 describes the method for determining the residual torque (Mresidual). The residual torque is
used in special cases when no reliable measurement is possible with the measurement of retight‐
ening torque 2 (Mretighten2) due to high static friction. This applies, in particular, to evaluations of
tightening torques in disassembly audits of powertrains (see Section 8.9). The tightening torque is
measured upon reaching the starting position.
*******  
*******  
*******  
*******  


### 第 23 页
Page 23
VW 01110-2: 2018-12
Figure 5 – Determination of the residual torque Mresidual:
Mark (1), loosen 10° to 30° (2), tighten again to marking (3)
Adjustment work on threaded connection types of categories A and B
For adjustment and alignment work (e.g., flaps, doors, lids), the monitored screw/nut driver equip‐
ment with the most recent settings must be used, taking into account section 8.6. Deviations are
only permissible following agreement between the Planning, Production, and Quality Assurance
departments, and must be documented.
Disassembly audits in the powertrain area
Retightening torque 2 (Mretighten2), test torque, and residual torque determinations are recommended
methods for disassembly audits in the powertrain area. The test is always performed after dynamic
loading on the threaded connection. However, the results of the three designated measuring meth‐
ods are not easily compared.
Loosening torques are not suitable for evaluating the quality of the threaded connection.
Threaded-connection data recording and documentation
General information
Threaded-connection data is classified as internal information that is subject to the need-to-know-
principle.
Threaded-connection data recording
Threaded-connection data recording during assembly
Threaded-connection data recording is preferred during assembly, because it can be carried out
without additional effort.
–
Recording of the product-specific actual values
–
Recording of the product-specific statuses (OK/NOK; for the documentation)
The preferred method is to record the actual values for torque and angle of rotation. The basis for
the use of actual-value recording is the process reliability of the screw/nut driver (see section 8.1 to
section 8.4 and section B.1).
If product-specific recording of the threaded-connection data is not possible for technical reasons,
then recording with a manufacturing reference (e.g., time stamp) must be carried out. In these
closed systems, only OK parts may leave the manufacturing area. The procedure must be agreed
upon with Planning, Production, and Quality Assurance, and must be documented.
8.8  
8.9  
8.10  
8.10.1  
8.10.2  
********  


### 第 24 页
Page 24
VW 01110-2: 2018-12
Threaded-connection data recording after assembly
–
Documentation of the retightening torques
–
Documentation of the screws/bolt extension (in special cases)
–
Documentation of the test with test torque
–
Documentation of cyclical tool testing
Documenting actual values
For threaded connections tightened by means of monitored screw/nut driver systems, the gener‐
ated data must be classified according to CSD and documented accordingly (see table 7), so that:
–
In the event of an error, tracing and therefore narrowing of affected threaded connections is
possible
–
Statistical evaluations are possible for limit value determination of the control parameters and
test parameters
–
Test cycles (product and process) can be reduced by evaluating actual values
–
Error analyses can be conducted to determine the cause of errors.
The CSD classification is independent of the threaded connection type category; see table 7.
Table 7 – CSD classification for documentation of threaded-connection data
Threaded-connection data
CSD classification
Designation
Scope
 
Production threaded
connection
Product-related threaded-connection data
7,4
Product-related status
7,2
Mretighten1 measured
values
Specific values related to the product line type (not product-related) and
independent of the TLD reference
7,2
MFU measured val‐
ues
Specific values (not product-related) and independent of the TLD refer‐
ence
7,1
IFB
Specific values related to the product line type (including threaded con‐
nection type simulator and not product-related, or excluding threaded
connection type simulator and product-related; see table B.1 and
table B.2) and independent of the TLD reference
7,4
Product-related status
7,2
IFH
Specific values related to the product line type (not product-related) and
independent of the TLD reference
7,4
Product-related status
7,2
Safety documentation
The recording options as per section 8.10.2 can also be used for safety documentation. The status
(OK/NOK) and, in random samples, Mretighten1 must be documented. The safety documentation must
be completed by the Production department assuming technical responsibility. All data that docu‐
ments the process reliability must be archived as per CSD (see table 8). The determination as to
how the safety documentation is to be completed must be agreed upon with the appropriate Quali‐
ty Assurance department of the plant responsible for the vehicle type. This must also be documen‐
ted.
8.10.2.2  
8.10.3  
8.10.4  


### 第 25 页
Page 25
VW 01110-2: 2018-12
The CSD classification for the safety documentation is independent of the threaded connection
type category; see table 8.
Table 8 – CSD classification for safety documentation of threaded-connection data
Threaded-connection data
CSD classification
Designation
Scope
 
Production threaded
connection
Product-related status
7,2
Mretighten1 measured
values
Specific values related to the product line type (not product-related)
and independent of the TLD reference
7,2
Rework
General information
Rework must be kept to a minimum (objective: zero faults). Therefore, qualified rework must not be
limited to simply indiscriminate retorquing.
The causes of rework must be determined and eliminated in the long term. Fasteners and parts
assembled incorrectly may need to be replaced. In the event of errors, automated repeat tightening
can be carried out, taking into account the specifications in VW 01110-3, in automatic assembly
processes for torque-controlled procedures or in the pre-tightening stage for angle-controlled pro‐
cedures. As a result, rework can be significantly reduced without considerable extra time.
Rework can be carried out at the following stations:
–
In the production segment (preferred, because no disassembly of the components is necessa‐
ry)
–
Outside of the production segments (separate rework station)
Rework on threaded connections is not subject to multiple-party verification, as specifically trained
workers perform the pertinent tasks. This applies to all initially tightened threaded connections, all
loosened threaded connections assembled with an OK result, and all threaded connections assem‐
bled with an NOK result. Assembling of the threaded connection is documented by the worker on
an individual, product-specific basis for each threaded connection type, either by a stamp on the
transfer ticket or an entry in EDP. This documentation obligation also applies to threaded connec‐
tions assembled with OK results that are loosened in the course of rework and must be reassem‐
bled. An additional color marking for process support or self-monitoring is permissible.
Thread broaching
Thread broaching of inner threads using threading taps in the 1st thread (roughing tap) or thread
formers, and threading of outer threads with dies, by trained personnel is permissible (any restric‐
tions must be taken from the associated PDM sheet, the TAB, or the drawing).
Thread broaching must typically be carried out manually.
If a continuously rotating screw/nut driver is used, the specification in VW 01110-3, table "Screw-in
torque Mtighten or M+ (pre-tightening stage 1) or Mthreshold (pre-tightening stage 2)" (column "Screw-in
torque for locking coating or prevailing torque type nuts") must be adhered to as the upper torque
limit for thread broaching. In order to prevent damage to the thread, the rotational speeds must be
limited during thread broaching (maximum 150 rpm).
9  
9.1  
9.2  


### 第 26 页
Page 26
VW 01110-2: 2018-12
Responsibilities
Table 9 and Table 10 describe the requirements and responsibilities for the pre-production phase
and for production. Changes of responsibilities are permissible if these deviations are recorded in
writing and the implementation of tasks is ensured.
The same procedure is permissible for the responsibilities stated in section 6 and section 8.
Table 9 – Responsibilities in the pre-production phase
Tasks
Responsible party
Procurement of the screw/nut driver as per Group Performance Specification "Controlled
Screw/Nut Driver Systems", VW 01110-1, and VW 01110-2
Planning
Implementation of product-specific documentation, (e.g., TLD for threaded connections, catego‐
ries A and B)
Planning
Setting of assembly parameters (specifications as per TAB, PDM, or drawing)
Planning
Setting of provisional control parameters as per VW 01110-3 in the screw/nut driver system
Planning
Measuring the retightening torques (Mretighten1) and defining the provisional range limits
Production
Releasing the test procedures and the provisional test parameters and control parameters
Quality Assurance
Table 10 – Responsibilities in the production phase
Tasks
Responsible party
Measuring and maintaining the quality documentation with the aid of statistical methods:
- Actual values
- Mretighten1 values
Production
Defining the limits for test parameters
Production
Releasing the limits for test parameters
Quality Assurance
Testing of screw/nut drivers according to specified process standards
Production
Measuring the Mretighten2 values (disassembly audits in the powertrain area, whole vehicle tests)
Quality Assurance
Monitored screw/nut driver systems:
Random checks of the assembly parameters and control parameters used
Quality Assurance
Optimizing the limits for test and control parameters
Production
Initiating error analyses
Production
Conducting error analyses
Production
Releasing the test procedures and the limits for test and control parameters
Quality Assurance
NOTE 5: VW 01110-1, "Responsibilities" section, describes the requirements and responsibilities
for the development phase.
Applicable documents
The following documents cited are necessary to the application of this document:
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
10  
11  


### 第 27 页
Page 27
VW 01110-2: 2018-12
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
VW 01110-1
Threaded Connections; Part 1: Design and Assembly Specifications
VW 01110-3
Threaded Connections; Part 3: Parameter Settings for Monitored, Con‐
tinuously Rotating Driving Tool Systems
VW 01110-4
Threaded Connections; Part 4: Testing and Evaluation of Threaded Con‐
nections
VW 01126-2
Tightening Torques for Threaded Connections; Part 2: Torque-to-Yield
Assembly
VW 01127
Self-Tapping Screw Connections; Application, Standard Values for Pilot
Hole Diameter, Specification of the Tightening Torques
VW 01129
Limit Values for Coefficients of Friction; Steel Mechanical Fasteners with
Metric ISO Threads
VW 10123
Measuring Equipment; Traceable Measuring Equipment for Pulse Driv‐
ing Tool Measurement
VDA Volume 6, Part 3
Qualitätsmanagement in der Automobilindustrie – Prozessaudit – Pro‐
duktentstehungsprozess, Serienproduktion, Dienstleistungsentstehung‐
sprozess, Erbringung der Dienstleistung (Quality Management in the Au‐
tomotive Industry – Process Audit – Product Development Process, Pro‐
duction, Service Development Process, Providing the Service)
VDI/VDE-MT 2637
Sheet 1
Qualification in fastening technology - Appropriate qualification for em‐
ployees and executives
VDI/VDE 2645 Sheet 2
Capability test for fastening technology - Machine capability test - MCT
VDI/VDE 2862 Sheet 1
Minimum restrictions for application of fastening systems and tools - Ap‐
plications in the automotive industry
Bibliography
[1]
VW 01110-5, Threaded Connections – Part 5: Analysis Process for Complaints
12  


### 第 28 页
Page 28
VW 01110-2: 2018-12
Evaluation procedures for screw/nut drivers of categories A, B, or C
For the evaluation procedures for screw/nut drivers with evaluation factor ranges, see figure A.1;
associated factors are specified in table A.1.
Figure A.1 – Evaluation procedures for screw/nut drivers of categories A, B, or C
The evaluation factor is the product of the individual factors (error effect, error frequency, probabili‐
ty of discovery, and error costs) as per table A.1.
Table A.1 – Factors for evaluation procedures for screw/nut drivers of categories A, B,
or C
Points
Factor
Error effect
Error frequency
Probability
of discovery
Error costs
10
Safety risk, breakdown
> 5 times daily
No testing/control loop in
place
> €1 000 per rework case
or breakdown
7
Significant impairment of
function
> 1 time weekly
Random-sample check
without sufficient proof of
capability
€500 to €1 000 per rework
case
5
Minor impairment of func‐
tion
The error is already known.
Network of several con‐
trol loops, carry-over
from OK predecessor
< €500 per rework case
3
Audit relevance of B/C er‐
rors
The error is possible, but
has not yet occurred.
100% worker-guided
Low rework costs
1
The error is perceived only
by experts
The error is unlikely.
100% system-integrated
testing
Negligible rework costs
Appendix A (normative)  


### 第 29 页
Page 29
VW 01110-2: 2018-12
Detailed specifications regarding threaded-connection processes
Process assurance for threaded connections
The use of technologies that are not described here require approval by the appropriate depart‐
ments (Planning, Quality Assurance, and Production) for the IFB and IFH processes (see table B.1
to table B.4).
Appendix B (normative)  
B.1  


### 第 30 页
Page 30
VW 01110-2: 2018-12
Table B.1 – IFB process (torque-controlled tightening)
 Notes and comments
1 New use of threaded connection type
2 Exchange of screw/nut driver(s), software, or control system
3 Check hardware and software of the screw/nut driver system.
4 Check the following data:
– Marking OK
– Basic conditions OK
– Categorization OK
– Threaded connection type assignment OK
5 Set screw/nut driver(s) to nominal value.
6 For the numerical simulations of an MFU or PFU, see VDI/VDE 2645 Sheet 2.
7 Optimize assembly sequence, etc.
8 Plot the torque/angle-of-rotation tightening curves for each new use (if possible).
9 Exchange of screw/nut driver(s), software, or control system
10 Retorque the threaded connection with nominal torque as per specifications
11 The release must meet the following minimum requirements:
– Exact assignment to the screw/nut driver
– Malfunction cause
– Measuring equipment used
– Who (name) carried out the MFU?
12 Mretighten1 within specifications (see section *******)
Tightening curve evaluation as per PDM specifications (AD18), screw-in torque
13 Analyze the error cause:
– Screw/nut driver(s)
– Problem with parts
– Design problem
For a checklist, see section C.2.
14 The screw/nut driver is OK with regard to its use for this threaded connection
type.
15 For the procedure, see section B.2


### 第 31 页
Page 31
VW 01110-2: 2018-12
Table B.2 – IFB process (angle-controlled tightening)
 Notes and comments
1 New use of threaded connection type
2 Exchange of screw/nut driver(s), software, or control system
3 Check hardware and software of the screw/nut driver system.
4 Check the following data:
– Marking OK
– Basic conditions OK
– Categorization OK
– Threaded connection type assignment OK
5 Set screw/nut driver(s) to nominal value.
6 For the numerical simulations of an MFU or PFU, see VDI/VDE 2645 Sheet 2.
7 Optimize screwing sequence, etc.
8 Plot the torque/angle-of-rotation tightening curves for each new use (if possible).
9 Exchange of screw/nut driver(s), software, or control system
10 The release must meet the following minimum requirements:
– Exact assignment to the screw/nut driver
– Malfunction cause
– Measuring equipment used
– Who (name) carried out the MFU?
11 Retorquing with test torque Mtest; see section *******
12 The tightening curve evaluation must be carried out as per figure 2 (AW11) by
personnel qualified for this task.
13 Analyze the error cause:
– Screw/nut driver(s)
– Problem with parts
– Design problem
For a checklist, see section C.2.
14 The screw/nut driver is OK with regard to its use for this threaded connection
type.
15 For the procedure, see section B.2


### 第 32 页
Page 32
VW 01110-2: 2018-12
Table B.3 – Process releases (torque-controlled tightening)
 Notes and comments
1
Classification as per VW 01110-1, "Categorization of threaded connec‐
tion types" section
2/3
The classification is as per VW 01110-1, appendix "Flowchart for cate‐
gorizing threaded connection types"
4/6
Specify the type of documentation, e.g.:
– Create a test list (hard copy) or
– Electronically (database)
5/8/9/16/17
Document the required number of Mretighten1 values.
7
The purpose of this requirement is to classify threaded connections in
terms of their embedment behavior (with/without embedment). When
Mretighten1 > Mtighten, max, see the procedure in section 8.7.3.4.
10/11/18
Are the Mretighten1 values within the range limits specified in section B.2?
12/20
Issued when the process is stable and the values are within the specifi‐
cations.
13
Retorquing required if Mretighten1 is not within the specified range limits.
14
If no optimization measures are possible on the threaded connection
type, the range limits specific to the threaded connection type must be
redefined.
15/21/22
Start of retorquing always must take place upon each new usage of a
threaded connection type after an MFU, and ends following the provi‐
sional process release.
19
Is the process reliable in terms of the new range limits specific to the
threaded connection type?
23
The appropriate design engineer must enter the results.
24
Definition of the limits for the monitoring angles in the screw/nut driver
control system (see section 8.7.2 and section 8.7.3).
25
Release form for the screw/nut driver.


### 第 33 页
Page 33
VW 01110-2: 2018-12
Table B.4 – Process releases (angle-controlled tightening)
 Notes and comments
1
Classification as per VW 01110-1, "Categorization of threaded connection
types" section
2
The classification is as per VW 01110-1, appendix "Flowchart for catego‐
rizing threaded connection types"
3
Specify the type of documentation, e.g.:
– Create a test list (hard copy) or
– Electronically (database)
4/9
Alternative to tightening curve evaluation. Proceed as per section 8.7.2.
5/6/11
Document the required number of Mretighten1 values.
7
The purpose of this requirement is to classify threaded connections in
terms of their embedment behavior (with/without embedment).
8
Are the Mretighten1 values within the range limits? See section *******.
7/8/9
The determination of quantity and tightening curve evaluation must be car‐
ried out as per section 8.7.2 and section 8.7.3 by personnel qualified for
this task.
10
If no optimization measures are possible on the threaded connection type,
the range limits specific to the threaded connection type must be rede‐
fined.
12
Is the process reliable in terms of the new range limits specific to the
threaded connection type?
13/15/16 Start of retorquing with the test torque Mtest always takes place upon each
new usage of a threaded connection type after an MFU, and ends follow‐
ing the provisional process release.
14
The appropriate design engineer must enter the results.
17
Definition of the limits for the monitoring angles in the screw/nut driver
control system (see section 8.7.2 and section 8.7.3).
18
Release form for the screw/nut driver.


### 第 34 页
Page 34
VW 01110-2: 2018-12
Range limits for retightening torques
General information
The range limits for the retightening torques 1 (Mretighten1) necessary for product testing must be de‐
termined depending on the processes. It is permissible to carry over range limits for retightening
torques from COP threaded connection types.
Procedure for carrying out the Mretighten1 test as per section 8.7.3.
Stage 1/pre-production phase: Determination of general range limits (as percentages)
for retightening torques
Specifying the range limit as percentages yields values with decimal digits. In these cases, the val‐
ues are rounded down to the measurement accuracy of the measuring instrument. Direct unambig‐
uous allocation of a specific value to an assembly parameter is not possible. In most cases, how‐
ever, the limits stated in formula (B.1) and formula (B.2) have proven to be useful (verified by E):
AD procedure: 0,8 × Mtighten ≤ Mretighten1 ≤ 1,2 × Mtighten
(B.1)
AW procedure: 0,8 × Mactual ≤ Mretighten1 ≤ 1,2 × Mactual
(B.2)
NOTE B.1: For AW procedures, Mtighten, min and Mtighten, max as per VW 01126-2 are used as refer‐
ence for evaluation of the Mretighten1 values, if Mactual is not available (unmonitored screw/nut driver
systems).
NOTE B.2: The range limit determination applies to threaded connections with metric threads and
that do not contain plastic materials.
NOTE B.3: For threaded connection types of category C, a smaller lower range limit is permissi‐
ble: 0,7 × Mtighten or 0,7 × Mactual
NOTE B.4: For threaded connection types of the categories B and C that use non-metric threads
and/or contain one or more clamped plastic parts, a smaller lower range limit is permissible:
0,5 × Mtighten or 0,5 × Mactual
If the retightening torques determined during product testing in the course of the production proc‐
ess reliably meet these requirements, these range limits may be established as generally applica‐
ble. If they do not meet the requirements, at least 50 Mretighten1 values of previously OK assembled
threaded connections (see table C.2; including checking the values for normal distribution, random‐
ness, and outliers) must be determined and the deviating range limits must be defined as per the
procedure specified in section B.2.3.
Stage 2/production phase: Empirical method based on the statistical frequency
distribution method for the production process
In the first step, the value range of the minimum 50 Mretighten values measured between the 2s and
3s dispersion limits of the frequency distribution, within which all measured Mretighten values lie (see
figure B.1), must be plotted.
B.2  
B.2.1  
B.2.2  
B.2.3  


### 第 35 页
Page 35
VW 01110-2: 2018-12
Legend
Fi
Relative cumulative frequency
Mretighten1
Retightening torque 1
Mretighten1, ma
x
Retightening torque 1, upper range limit
Mretighten1, min Retightening torque 1, lower range limit
Figure B.1 – Determining the range limits of retightening torques, taking into account multiple
random samples
In the second step, and if reasonable from a technical point of view, the range limits must be set to
full scale values as minimum and maximum values below or above the intersection points with the
distribution straight lines; see figure B.1. For torque-to-yield assembly processes, the upper limit
may be set significantly higher than the 3s dispersion limit for avoidance of false alarms. Mtighten, max
can be used as a reference value here as per VW 01126-2. By narrowly defining the range limits,
deviations can be responded to with the necessary sensitivity, and systematic threaded-connection
errors can be detected. However, this may result in apparent deviations to which no actual threa‐
ded-connection error is attributable. In these cases, a control process must be started for re-evalu‐
ating error sensitivity.
In the event of deviations from the specified range, the cause must be analyzed based on the
checklist in appendix C. It may also be necessary to carry out an analysis as per VW 01110-52),
the result of which must be documented. Ongoing production must be safeguarded at the same
time (see table 4).
2)
For the analysis process in the event of complaints, standard VW 01110-5 [1] is currently being prepared.


### 第 36 页
Page 36
VW 01110-2: 2018-12
The analyses may also result in defining new range limits. Changes to assembly specifications are
not permitted without the approval of the appropriate Design Engineering department. Deviations
from the limits specified in section B.2.2 may only be released by Development.
Because the empirical method takes into account all factors influencing the retightening torques for
the respective threaded connection type, the specification in effect in each case must be documen‐
ted according to CSD 7,2 and is valid exclusively for this threaded connection type. Documentation
that applies universally or that is to be used for various locations or installation circumstances is
not practical. The departments specified in table 10 are responsible.
Procedure for increasing the tightening speed
The friction properties of the threaded fasteners are also significantly influenced by the tightening
speed. High tightening speeds can significantly increase friction and its dispersions.
The tightening speeds for metric threaded connections must be based on the speeds described in
section 6.6.
If a higher tightening speed is desired, this must be implemented by first increasing the pre-tighten‐
ing speed.
To control the influence of speed, angle-of-rotation measurements can be applied. Because the
angle of rotation and the preload force generally have good correlation, the evaluation of the moni‐
toring angles provides a meaningful result.
With torque-controlled assembly, the dispersions (3s values) of the monitoring angles must not be
over 5% lower than the values based on the natural, normally distributed process dispersion that
was measured before the increase in speed. Outliers must not be evaluated in the evaluation of
process dispersion.
With torque-to-yield assembly, particularly high thread friction is significant because lower preload
forces can be achieved due to the resulting equivalent stresses. Very high frictions beneath the
screw head – particularly with concave screw head bearing surfaces – may lead to final torques
that may exceed the capability of the screw/nut driver.
After an increase in speed, the final torques with torque-to-yield assembly may increase by no
more than 20%. With the aid of tightening curves, it must be verified with the AW11 procedure that
the screw yield point is exceeded.
Additional increases in the tightening speeds are always based on changes of 200 rpm in the
screw-in step and 20 rpm in the final tightening step with respect to a speed.
B.3  


### 第 37 页
Page 37
VW 01110-2: 2018-12
Checklists
Checklist for threaded-connection workstation
See table C.1.
NOTE C.1: This checklist is recommended documentation for the release of a threaded connec‐
tion type, but is not mandatory.
Table C.1 – Checklist for threaded-connection workstation
Checklist for threaded-connection workstation
This checklist does not supersede the auditing procedure as per VDA Vol‐
ume 6, Part 3
Workstation no.:
Cost center:
1
Personnel/qualification
Points
1.1
Are the employees familiar with the system and have they been trained/instructed (with docu‐
mentation) regarding the features and potential errors in the threaded connections?
 
1.2
Are the responsibilities for parameter setting and maintenance clearly defined?
 
1.3
Have the employees been instructed and trained to do rework?
 
1.4
Does a personnel deployment plan exist, including substitute management and qualification doc‐
umentation?
 
2
Production equipment/facilities
 
2.1
Does the screw/nut driver system or the screw/nut driver comply with the requirements of
VW 01110-2?
 
2.2
Are the important process parameters documented by the screw/nut driver system for threaded
connections of categories A and B?
 
2.3
Has the screw/nut driver system been accepted by Quality Assurance and the appropriate de‐
partment (see VW 01110-2, section B.1)?
 
2.4
Is a flawless power supply of the screw/nut driver system ensured (air pressure, electrical pow‐
er)?
 
2.5
Is the screw/nut driver sufficiently dimensioned with respect to assembly tightening procedures
(for threaded connections of categories A and B, see VW 01110-2, section 8)?
Reference value for dimensioning of the assembly spindles as per VW 01126-2.
 
2.6
Are the assembly parameters specified as per the drawing (PDM, TAB) and have Work Instruc‐
tions been provided?
 
2.7
Have test parameters been determined on the finished product (e.g., retightening torques
Mretighten1/Mretighten2) and have the testing intervals been defined?
 
2.8
Has the tightening sequence (in the case of multiple tightening processes) been defined?1)
 
2.9
Are there measures in place that prevent missing individual tightening steps?
 
2.10
Are rework instructions provided and are they followed?1)
 
2.11
Are errors documented on a product-specific basis?
 
2.12
Are there suitable stations and enough time for rework?
 
2.13
Are the replacement intervals for screw/nut drivers and other tools specified and are they being
adhered to?
 
2.14
Has a release for production ramp-ups been issued and have settings and deviations been re‐
corded (test parameters independent of the different batches and shifts, first-piece release)?1)
 
Appendix C (informative)  
C.1  


### 第 38 页
Page 38
VW 01110-2: 2018-12
Checklist for threaded-connection workstation
This checklist does not supersede the auditing procedure as per VDA Vol‐
ume 6, Part 3
Workstation no.:
Cost center:
2.15
Is an emergency strategy in place in the event of screw/nut driver system or tool failure?1)
 
2.16
Is it possible to inadvertently assemble a threaded connection for a screw-on point not included
in the scope of work?
 
3
Parts handling
 
3.1
Is a potential mix-up of parts ruled out?
 
3.2
Are the fasteners stored and marked accordingly (transfer ticket, tracing)?
 
4
Error analysis/correction/continuous improvement
 
4.1
Are occurring errors documented in relation to the types and screw-on points (statistics)?
 
4.2
Is the threaded connection type data documented in relation to the product and is data stored
(reproducibility) and/or are error logs kept?
 
4.3
Can tightening curves be plotted?
 
4.4
Has the process capability of the screw/nut driver system been verified in accordance with
section B.1?1)
 
Evaluation
Number of
points
Degree of
fulfillment
as a %
Degree of fulfillment = Sum of all points achieved
Sum of all possible points
 × 100 (%)
max.
ac‐
tual
 
1. Personnel/qualification
 
 
 
2. Production equipment/de‐
vices
 
 
 
3. Transportation/parts han‐
dling
 
 
 
4. Error analysis/correction
 
 
 
Minimum requirements 81% to 91%
Total
 
 
 
Target specification 92% to 100%
Evalua‐
tion
E: does not apply
0 points:
not fulfilled
4 points: inade‐
quately fulfilled
6 points: partially ful‐
filled
8 points: mostly
fulfilled
10 points: com‐
pletely fulfilled
Degree of fulfill‐
ment
< 81% – immediate measures required
82% to 91% – containment actions and re-auditing re‐
quired
1) Questions of particular significance: Noncompliance may lead to downgrading.


### 第 39 页
Page 39
VW 01110-2: 2018-12
Checklist for process optimization
See table C.2.
For a process optimization checklist in the event of deviations from the Mretighten1 specifications, see
section B.2.
Table C.2 – Checklist for process optimization
No.
Action
Evaluation1)
Yes
No
1
Tightening specifications and sequence in place and set?
 
 
2
Assembly devices and joining aids available and correctly used?
 
 
3
Tool set correctly?
 
 
4
Was the tightening torque applied correctly?
 
 
5
Random sample of 5 test parts with the screw/nut driver and sensor on the product OK?
 
 
6
No deformations visible on the parts to be joined?
 
 
7
Has it been ensured that clamped parts (rubber-bonded bushings) cannot spring back?
 
 
8
Tightening curve evaluation conducted (stick-slip, joining process, etc.)?
 
 
9
Can the possibility of measurement, evaluation, or operator errors be excluded in the de‐
termination of the Mretighten1 values (evaluation via curve diagram)?
 
 
10
Is the threaded connection free of impermissible contaminants (e.g., wax, grease, PVC)?
 
 
11
Has the influence of the tightening speed been evaluated?
 
 
12
Do the components meet drawing specifications?
 
 
1) If a criterion is answered "No," process optimization is required prior to consultation with Development.
C.2  


### 第 40 页
Page 40
VW 01110-2: 2018-12
Comparison of assembly tightening procedures and production
specifications as per VW 01110-2: 2008-07
See table D.1.
Table D.1 – Comparison of assembly tightening procedures and production
specifications as per VW 01110-2: 2008-07
Assembly
tightening pro‐
cedure
quality class
Production specifications as per VW 01110-2: 2008-07, table 1
Quality
class
Method
group
Assembly tightening proce‐
dure
(tool)
Screw/nut drivers
(examples)
Screw
utilization
(approximate
value)
AD18
AD18
Below the screw yield point
torque-controlled,
manual
– Click-type torque wrenches
– Electronic hand-held wrench‐
es
> 50%
AD17
torque-controlled,
rotating screw/nut drivers
– Battery-powered screw/nut
drivers
> 55%
– Shut-off torque screw/nut
drivers
– Auto shut-off screw/nut driv‐
ers
ADI16
torque-controlled,
pulse screw/nut drivers,
electronically controlled
– Pulse screw/nut drivers
 (Volkswagen system QIS)
> 60%
AD15
torque-controlled,
rotating screw/nut drivers,
electronically controlled
– EC screw/nut drivers
– Electronically controlled
pneumatic screw/nut drivers
– Electronically monitored
hand-held wrenches (AW12
only)
> 65%
AW12
AW12
angle-controlled, below the
screw yield point, man‐
ual/rotating screw/nut driv‐
ers
AW11
AW11
torque-to-yield
angle-controlled,
manual
– Click-type torque wrenches
– Electronic hand-held wrench‐
es
approx. 100%
AW10
angle-controlled,
rotating screw/nut drivers,
electronically controlled
– EC screw/nut drivers
– Electronically controlled
pneumatic screw/nut drivers
ASI10
pulse-controlled,
pulse screw/nut drivers,
electronically controlled
– Pulse screw/nut drivers
 (Volkswagen system QIS)
AS10
yield-point-controlled,
rotating screw/nut drivers,
electronically controlled
– EC screw/nut drivers
Appendix D (informative)  


### 第 41 页
Page 41
VW 01110-2: 2018-12
Responsible parties
The parties assuming responsibility for this standard are listed in the Standards Online Information
System (NOLIS).
Appendix E (informative)  

