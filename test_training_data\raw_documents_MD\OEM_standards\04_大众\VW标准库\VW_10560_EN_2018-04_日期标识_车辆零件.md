# VW_10560_EN_2018-04_日期标识_车辆零件.pdf

## 文档信息
- 标题：
- 作者：
- 页数：13

## 文档内容
### 第 1 页
Group standard
VW 10560
Issue 2018-04
Class. No.:
01152
Descriptors:
Marking, date, date clock, date marking, parts marking
Date Marking
Vehicle Parts
Previous issues
VW 10560: 1980-12, 1991-12, 2000-12, 2007-12, 2010-12, 2015-02
Changes
The following changes have been made to VW 10560: 2015-02:
–
Section 3.2, Figure 3 updated and expanded
–
Section 4.1, Table 2 updated
–
Section 4.2 updated
–
Section 4.3 Table 3 (type T) added
–
Section 4.4, Table 5 updated
–
Section 4.5, Table 6 updated
–
Section 5.1.2, Table 8 updated and expanded
–
Section 5.2, heading adapted, Table 9 updated and expanded
–
Section 5.3, Figure 4, and Figure 5 updated
–
Section 6.1, Table 10 updated
–
Section 6.2.1, Figure 6 updated
–
Section 6.3.1, Table 12 updated
–
Section 6.3.2, Table 13 updated
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version controls.
Page 1 of 13
Technical responsibility
The Standards department
K-ILI/5
Uwe Stüber
Tel.: +49 5361 9 29063
K-ILI/5 Uwe Stüber
K-ILI
Tel.: +49 5361 9 29063
Uwe Wiesner
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2018-02


### 第 2 页
Page 2
VW 10560: 2018-04
Contents
Page
Scope ......................................................................................................................... 2
Requirements ............................................................................................................. 2
Date marking using symbols ...................................................................................... 3
Date clock (month, year) ............................................................................................ 3
Date grid (month and year) ........................................................................................ 4
Date marking (not encoded) ....................................................................................... 5
Week, year ................................................................................................................. 5
Shift, day, week, year ................................................................................................. 5
Year, serial number .................................................................................................... 5
Shift, day, week, year (double stamp) ........................................................................ 6
Shift, day, month, year (double stamp) ...................................................................... 6
Date marking (encoded and partially encoded) ......................................................... 7
Single-digit type .......................................................................................................... 7
Month ......................................................................................................................... 7
Year ............................................................................................................................ 7
Two-digit type (year, month) ...................................................................................... 7
Four-digit and five-digit type: Year, week, day of the week, shift ............................... 8
Date marking for special cases ................................................................................ 10
Date marking for vehicle glazing .............................................................................. 10
Date marking for SLI batteries ................................................................................. 10
Week, year (formation date) ..................................................................................... 10
Week, year after defined recharging ........................................................................ 11
Date marking for engine and transmission control modules .................................... 12
Day, month, year ...................................................................................................... 12
Shift, day, month, year ............................................................................................. 12
Drawing specification ............................................................................................... 12
Applicable documents .............................................................................................. 13
1
2
3
3.1
3.2
4
4.1
4.2
4.3
4.4
4.5
5
5.1
5.1.1
5.1.2
5.2
5.3
6
6.1
6.2
6.2.1
6.2.2
6.3
6.3.1
6.3.2
7
8
Scope
This standard specifies the marking of vehicle parts that must carry a date marking proving the
date of manufacture:
–
To comply with official regulations
–
To allow sorting by date of manufacture
–
To allow tracking of customer complaints (traceability of parts).
Examples are given in table 1 to table 13 and in figure 1 to figure 7.
Requirements
A date marking as specified in this standard must be applied to each vehicle part.
Whether date marking is necessary or not must be specified in the drawing (see text macro NO-E2
in Volkswagen standard VW 01014, NO-E2, figure 7). This also applies to the size and position of
the marking.
It must be ensured that the marking remains legible over the service life of the part. The marking
must be applied to the parts in a permanent and non-removable manner using suitable manufac‐
turing methods, e.g., embossing, stamping, electric discharge machining, casting.
1  
2  


### 第 3 页
Page 3
VW 10560: 2018-04
Typeface H (medium-spaced lettering) as per DIN 1451-4 must be used for the markings. If re‐
quired, the respective manufacturing method, e.g., "cast," "etched," "embossed," "stamped," or
"electric discharge machined" must be specified in the drawing. If there is no such specification in
the drawing, the choice of the manufacturing method is left to the manufacturer.
Further notes regarding the marking of parts are given in VW 10500.
Date marking using symbols
Date clock (month, year)
For type A, see figure 1, and for type B, see figure 2; for dimensions, see table 1.
Figure 1 – Type A
The months of manufacture must be marked in clockwise direction, with the markings being raised
between 0.5 mm and 0.8 mm.
The number of markings corresponds to the number of the calendar month in which the part was
manufactured.
Figure 2 – Type B
The arrow with year number in the marking tool can be rotated and replaced.
3  
3.1  


### 第 4 页
Page 4
VW 10560: 2018-04
Table 1 – Dimensions for type A and B
Dimensions in mm
Nominal
∅ d1
4
5
6
8
10
12
14
16
18
20
25
30
40
50
d2
3
3.8
4.5
6
7.5
9
10.5
12
13.5
15
19
23
30
38
d3
2
2.5
3
4
5
6
7
8
9
10
13
15
20
25
a
0.7
0.8
1
1.2
1.4
1.8
2
2.5
3
3.5
4
5
6
8
b
1.2
1.4
1.6
2
2.5
3
3.5
4
5
6
7
8
10
12.5
c
0.8
1
1.2
1.6
2
2.5
3
3
3
4
5
6
8
10
e ≈
0.5
0.6
0.7
1
1.2
1.5
1.7
2
2.3
2.5
3
3.8
5
6
f ≈
0.2
0.25
0.3
0.4
0.5
0.6
0.7
0.8
0.9
1
1.2
1.5
2
2.5
d2 ≈ 0.75 × d1
d3 ≈ d1
2
c ≈ 0.2 x d1
e ≈ d1
8
f ≈ 0.05 × d1
Date grid (month and year)
See figure 3.
Figure 3 – Type C
3.2  


### 第 5 页
Page 5
VW 10560: 2018-04
Date marking (not encoded)
Week, year
See table 2.
Table 2 – Type D
Marking
Example
Remarks
Week/year
07/18
7th week/year 2018
Shift, day, week, year
See table 3.
Table 3 – Type F
Marking
Example
Remarks
Shifta), dayb), week/year
II 3 07/18
or alternatively
B 3 07/18
2nd shift, 3rd day of
7th week/year 2018
a)
Alternative notation for shift I = A, shift II = B, shift III = C
b)
The first day of a week is Monday.
Year, serial number
See table 4.
Table 4 – Type T
Marking
Example
Remarks
Year/serial numbera)
18/236795
Year 2018/
236795th part in year 2018
a)
Year = two digit; serial number = six digit
4  
4.1  
4.2  
4.3  


### 第 6 页
Page 6
VW 10560: 2018-04
Shift, day, week, year (double stamp)
See table 5.
Table 5 – Type G
Marking
Example
Remarks
Shifta)
Dayb)
Week
Year
predominantly for castings
2nd shift, 5th day of
34th week, year 2018
c ≈ 0.2 d
l ≈ d
 
 
a)
Alternative notation for shift I = A, shift II = B, shift III = C
b)
The first day of a week is Monday.
Shift, day, month, year (double stamp)
See table 6.
Table 6 – Type H
Marking
Example
Remarks
Shifta)
Dayb)
Year
Month
predominantly for castings
2nd shift, 5th day,
year 2018, 9th month
c ≈ 0.2 d
l ≈ d
 
 
a)
Alternative notation for shift I = A, shift II = B, shift III = C
b)
The first day of a week is Monday.
4.4  
4.5  


### 第 7 页
Page 7
VW 10560: 2018-04
Date marking (encoded and partially encoded)
Single-digit type
Month
See table 7.
Table 7 – Type J
Month
Jan
Feb
Mar
Apr
May
June
July
Aug
Sept
Oct
Nov
Dec
Code
A
B
C
D
E
F
G
H
J
K
L
M
Year
See table 8.
Table 8 – Type K
Year
2001
2002
2003
2004
2005
2006
2007
2008
2009
2010
2011
2012
Code
1
2
3
4
5
6
7
8
9
A
B
C
Year
2013
2014
2015
2016
2017
2018
2019
2020
2021
2022
2023
2024
Code
D
E
F
G
H
J
K
L
M
N
P
R
Year
2025
2026
2027
2028
2029
2030
 
Code
S
T
V
W
X
Y
Two-digit type (year, month)
See table 9.
Table 9 – Type L
Month
Year
2001
2002
2003
2004
2005
2006
2007
2008
2009
2010
January
1A
2A
3A
4A
5A
6A
7A
8A
9A
AA
February
1B
2B
3B
4B
5B
6B
7B
8B
9B
AB
March
1C
2C
3C
4C
5C
6C
7C
8C
9C
AC
April
1D
2D
3D
4D
5D
6D
7D
8D
9D
AD
May
1E
2E
3E
4E
5E
6E
7E
8E
9E
AE
June
1F
2F
3F
4F
5F
6F
7F
8F
9F
AF
July
1G
2G
3G
4G
5G
6G
7G
8G
9G
AG
August
1H
2H
3H
4H
5H
6H
7H
8H
9H
AH
September
1J
2J
3J
4J
5J
6J
7J
8J
9J
AJ
October
1K
2K
3K
4K
5K
6K
7K
8K
9K
AK
November
1L
2L
3L
4L
5L
6L
7L
8L
9L
AL
December
1M
2M
3M
4M
5M
6M
7M
8M
9M
AM
5  
5.1  
5.1.1  
5.1.2  
5.2  


### 第 8 页
Page 8
VW 10560: 2018-04
Month
Year
2011
2012
2013
2014
2015
2016
2017
2018
2019
2020
January
BA
CA
DA
EA
FA
GA
HA
JA
KA
LA
February
BB
CB
DB
EB
FB
GB
HB
JB
KB
LB
March
BC
CC
DC
EC
FC
GC
HC
JC
KC
LC
April
BD
CD
DD
ED
FD
GD
HD
JD
KD
LD
May
BE
CE
DE
EE
FE
GE
HE
JE
KE
LE
June
BF
CF
DF
EF
FF
GF
HF
JF
KF
LF
July
BG
CG
DG
EG
FG
GG
HG
JG
KG
LG
August
BH
CH
DH
EH
FH
GH
HH
JH
KH
LH
September
BJ
CJ
DJ
EJ
FJ
GJ
HJ
JJ
KJ
LJ
October
BK
CK
DK
EK
FK
GK
HK
JK
KK
LK
November
BL
CL
DL
EL
FL
GL
HL
JL
KL
LL
December
BM
CM
DM
EM
FM
GM
HM
JM
KM
LM
Month
Year
2021
2022
2023
2024
2025
2026
2027
2028
2029
2030
January
MA
NA
PA
RA
SA
TA
VA
WA
XA
YA
February
MB
NB
PB
RB
SB
TB
VB
WB
XB
YB
March
MC
NC
PC
RC
SC
TC
VC
WC
XC
YC
April
MD
ND
PD
RD
SD
TD
VD
WD
XD
YD
May
ME
NE
PE
RE
SE
TE
VE
WE
XE
YE
June
MF
NF
PF
RF
SF
TF
VF
WF
XF
YF
July
MG
NG
PG
RG
SG
TG
VG
WG
XG
YG
August
MH
NH
PH
RH
SH
TH
VH
WH
XH
YH
September
MJ
NJ
PJ
RJ
SJ
TJ
VJ
WJ
XJ
YJ
October
MK
NK
PK
RK
SK
TK
VK
WK
XK
YK
November
ML
NL
PL
RL
SL
TL
VL
WL
XL
YL
December
MM
NM
PM
RM
SM
TM
VM
WM
XM
YM
Four-digit and five-digit type: Year, week, day of the week, shift
This type of date marking consists of the following elements: year code as per type K of this stand‐
ard (see table 8), uncoded specification of the week from 01 to 52 and specification of the day of
the week from 1 to 7 (type P, see figure 4). The marking may optionally include an additional shift
specification from A to C, i.e., shifts 1 to 3 (type R, see figure 5).
5.3  


### 第 9 页
Page 9
VW 10560: 2018-04
Legend
1
1st position: Year as per VW 10560, type K, see table 8, example: J = 2018
2
2nd and 3rd position: Week, example: 01 = 1st week (for single-digit weeks with lead‐
ing zero)
3
4th position: Day of the respective week, example: 5 = fifth day of the 1st week1)
Figure 4 – Type P
Legend
1
1st position: Year as per VW 10560, type K, see table 8, example: J = 2018
2
2nd and 3rd position: Week, example: 01 = 1st week (for single-digit weeks with lead‐
ing zero)
3
4th position: Day of the respective week, example: 5 = fifth day of the 1st week1)
4
5th position: Shift marking, example: A = shift 12)
Figure 5 – Type R
1)
The first day of a week is Monday.
2)
Alternative notation for shift I = A, shift II = B, shift III = C


### 第 10 页
Page 10
VW 10560: 2018-04
Date marking for special cases
Date marking for vehicle glazing
Type V – marking encoded
The marking includes the date of manufacture comprising month and year in encoded form.
The marking must be encoded as follows:
1.
Number for year of manufacture
Last digit of the year number of the year of manufacture (calendar year)
2.
Series of dots for month of manufacture, see table 10
Table 10 – Type V
Number of dots
Location of dots
Month
6
Left of the year number
= January (see 1st example)
1
Left of the year number
= June (see 2nd example)
6
Right of the year number
= July (see 3rd example)
1
Right of the year number
= December (see 4th example)
1st Example
1st half of the year
......8
= January
2018
2nd Example
.8
= June
2018
3rd Example
2nd half of the year
8......
= July
2018
4th Example
8.
= December
2018
Date marking for SLI batteries
Week, year (formation date)
Legend
a
03 = 3rd week
b
18 = year 2018
Figure 6 – Type M
6  
6.1  
6.2  
6.2.1  


### 第 11 页
Page 11
VW 10560: 2018-04
The formation date of the battery must be applied on the negative terminal as per figure 6. Batter‐
ies formed from Monday through Wednesday receive the date marking of the current week. Batter‐
ies formed from Thursday to Sunday receive the date marking of the following week. Sealed batter‐
ies as per VW 75073 deviate from this specification due to the quarantine period common for this
type of battery. For these batteries, the date marking specifies the week at the end of the quaran‐
tine period in which a test with an OK result was performed. The procedure used for the formation
date also applies to the specification of the week. Markings applied to the battery cover near the
negative terminal by means of hot stamping are permissible.
Week, year after defined recharging
If batteries must be recharged before delivery to the Volkswagen Group as per the conditions of
table 11, this must preferably be documented with laser engraving or hot stamping WW/YY
(week/year) in type D (see table 2) in the area around the negative terminal.
Table 11 – Recharging
Grouping
Recharging parameters
Ambient
temperature
(°C)a)
Battery
open-circuit volt‐
age (V)a)
Activity
Voltage
per battery
(V)a)
Duration
(h)a)
Max. charging cur‐
rent
per battery
(A)a)
< 12.35
Scrap
-
-
-
15 to 35
12.35 to 12.49
Recharging
14.8
12
30
> 12.49 to 12.59
Recharging
14.8
4
30
> 12.59 to 12.70
Recharging
14.8
2
30
> 12.70
Recharging, if de‐
livery on time with
≥ 12.70 is not pos‐
sible
14.8
2
30
a)
V = Voltage in volts
A = Amperage in amps
h = Time in hours
°C = Temperature in degrees Celsius
The recharging must be performed such that the battery has been allowed to adapt to the ambient
temperature at least 12 hours before charging begins. Groups of the same open-circuit voltage
ranges must be formed for recharging, see table 11. The batteries must be connected in parallel
for recharging. A series circuit with max. 8 batteries in series is acceptable in special cases.
Before leaving the charging station and being delivered to Volkswagen AG, a high-voltage tight‐
ness test must be conducted and documented for every battery.
6.2.2  


### 第 12 页
Page 12
VW 10560: 2018-04
Date marking for engine and transmission control modules
Day, month, year
See table 12.
Table 12 – Type N
Marking
Example
Remarks
Day.Month.Year
14.02.18
14.February.2018
Shift, day, month, year
See table 13.
Table 13 – Type O
Marking
Example
Remarks
Shifta).Day.Month.Year
II.14.02.18
2nd shift.14.February.2018
a)
Alternative notation for shift I = A, shift II = B, shift III = C
Drawing specification
Markings are specified in drawings using the CAD text macro NO-E2 as per VW 01014, see for
example figure 7.
Figure 7 – Text macro NO-E2
The size for the date marking is generally specified on the drawing in the unit "mm", see exam‐
ples 1 to 4:
1.
Drawing specification for a part marking using the type A (B) date of manufacturing with the
nominal diameter d1 = 16 mm:
Date marking VW 10560 – A16 (B16)
2.
Drawing specification for a part marking using the date grid with the nominal size C1:
Date marking VW 10560 – C1
6.3  
6.3.1  
6.3.2  
7  


### 第 13 页
Page 13
VW 10560: 2018-04
3.
Drawing specification for a part marking using the type D (F, J, K, L, N, O, P, R, V) date mark‐
ing with a font size of 6 mm:
Date marking VW 10560 – D6 (F6, J6, K6, L6, N6, O6, P6, R6, T6, V6)
4.
Drawing specification for a part marking using the type G (H, M) date marking with a diameter
d = 10 mm:
Date marking VW 10560 – G10 (H10, M10)
Applicable documents
The following documents cited are necessary to the application of this document:
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
VW 01014
Drawings; Drawing Frames and Text Macros
VW 10500
Company Designation, Marking of Parts; Guidelines for Use
VW 75073
SLI Batteries for 12 V Electric Systems; General Test Conditions
DIN 1451-4
Typefaces; lineal linear-antiqua; stenciled lettering for engraving and
other processes
8  

