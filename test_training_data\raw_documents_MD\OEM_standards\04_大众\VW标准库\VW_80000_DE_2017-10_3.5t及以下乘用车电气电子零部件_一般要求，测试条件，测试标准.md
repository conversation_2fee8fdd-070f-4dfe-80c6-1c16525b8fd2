# VW_80000_DE_2017-10_3.5t及以下乘用车电气电子零部件_一般要求，测试条件，测试标准.pdf

## 文档信息
- 标题：
- 作者：
- 页数：165

## 文档内容
### 第 1 页
Konzernnorm
VW 80000
Ausgabe 2017-10
Klass.-Nr.:
8MA00
Schlagwörter:
Komponente, elektrische Komponente, elektronische Komponente, Baugruppe, Prüfbedingung
Elektrische und elektronische Komponenten in Kraftfahrzeugen bis 3,5 t
Allgemeine Anforderungen, Prüfbedingungen und Prüfungen
Frühere Ausgaben
VW 80101: 1987-06, 1988-08, 1992-01, 1993-04, 1994-05, 1995-06, 1998-01, 1999-06, 2000-09,
2001-04, 2003-05, 2004-07, 2005-06, 2006-10, 2009-03; VW 80000: 2009-10, 2013-06
Änderungen
Gegenüber der VW 80000: 2013-06 wurden folgende Änderungen vorgenommen:
–
siehe Änderungshistorie auf Seite 2
Norm vor Anwendung auf Aktualität prüfen.
Die elektronisch erzeugte Norm ist authentisch und gilt ohne Unterschrift.
Seite 1 von 165
Fachverantwortung
Normung
EEIP/1
Dr. Torsten Polte
Tel.: +49 5361 9-36035
I/EE-61
Uwe Girgsdies
Tel.: +49 841 89-90836
EEG4
Mark Martins
Tel.: +49 711 911-84847
K-ILI/5 Dirk Beinker
K-ILI
Tel.: +49 5361 9-32438
Uwe Wiesner
Alle Rechte vorbehalten. Weitergabe oder Vervielfältigung ohne vorherige Zustimmung einer Normenabteilung des Volkswagen Konzerns nicht gestattet.
© Volkswagen Aktiengesellschaft
VWNORM-2016-08a


### 第 2 页
Seite 2 
VW 80000: 2017-10 
 
 
 
 
 
 
Änderungshistorie 
 
 
 
Ausgabedatum  
2013-02 
Redaktionelle Änderungen eingearbeitet. 
 
Teil I – Elektrische Anforderungen und Prüfungen 12 V 
Bordnetz:  
Grundsätzlich überarbeitet - jede Prüfung wurde auf aktuelle 
Anforderungen angepasst. 
 
Teil II – Umweltanforderungen und –prüfungen: 
Erweiterung auf Komponenten, die in mehreren Betriebsmodi 
beschrieben werden , Komponenten die an Kühlkreisläufen 
angeschlossen sind und Überarbeitung der Lebensdauerprüfungen.  
2017-05 
Grundlegende Überarbeitung 
 
Allgemein 
- 
Überarbeitung der Betriebsarten, deren Herleitung und 
Bezeichnung 
- 
Harmonisierung und Zusammenführung der allgemeinen 
Anforderungen für Teil I und Teil II 
 
Teil I – Elektrische Anforderungen und Prüfungen 12 V 
Bordnetz: 
- 
Ergänzung um Funktionsklassen  
- 
Neuordnung der Funktionszustände 
- 
Berücksichtigung von Fahrzeugen mit alternativen Antrieben 
- 
Parametertests neu definiert 
- 
E-03b, E-07b und E-23 hinzu 
- 
Allgemeine Anpassungen und Fehlerkorrekturen 
(Berücksichtigung aller eingegangenen CRs) 
 
Teil II – Umweltanforderungen und –prüfungen: 
- 
Ergänzungen zur Prüfdurchführung in den einzelnen 
Prüfungen 
- 
Ergänzung Dichtheitsanforderung 
- 
Hinzufügen der Druckwechselprüfung zu den mechanischen 
Prüfungen 


### 第 3 页
 
  
Seite 3 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Inhaltsverzeichnis 
 
1 
Geltungsbereich ..................................................................................................... 6 
2 
Normative Verweise ............................................................................................... 6 
3 
Begriffe und Definitionen ....................................................................................... 7 
3.1 
Begriffe und Abkürzungen ..................................................................................... 7 
3.2 
Spannungen und Ströme ..................................................................................... 10 
3.3 
Temperaturen ...................................................................................................... 10 
3.4 
Zeiten ................................................................................................................... 11 
3.5 
Innenwiderstand, Klemmenbezeichnung, Frequenz ............................................ 11 
3.6 
Standardtoleranzen ............................................................................................. 11 
3.7 
Standardwerte ..................................................................................................... 11 
4 
Allgemeine Anforderungen ................................................................................. 12 
4.1 
Abtastraten und Messwertauflösungen ................................................................ 12 
4.2 
Funktionszustände ............................................................................................... 12 
4.3 
Betriebsmodi ........................................................................................................ 13 
4.4 
Betriebszustände ................................................................................................. 13 
4.5 
Betriebsarten ....................................................................................................... 14 
4.6 
Durchtemperierung .............................................................................................. 18 
4.7 
Parameterprüfung ................................................................................................ 18 
4.8 
Kontinuierliche Parameterüberwachung mit Driftanalyse .................................... 21 
4.9 
Dichtheit ............................................................................................................... 22 
Teil I – Elektrische Anforderungen und Prüfungen 12 V Bordnetz ......................... 23 
5 
Allgemeine Anforderungen ................................................................................. 23 
5.1 
Spannungen und Ströme ..................................................................................... 23 
5.2 
Prüfspannungen .................................................................................................. 23 
5.3 
Funktionsklassen und Betriebsspannungsbereiche ............................................. 24 
5.4 
Schnittstellenbeschreibung .................................................................................. 24 
5.5 
Beschränkung der Durchführung ......................................................................... 24 
5.6 
Prüfablauf ............................................................................................................ 25 
6 
Prüfungsauswahltabelle ...................................................................................... 27 
7 
Elektrische Anforderungen und Prüfungen ....................................................... 29 
7.1 
E-01 Langzeit Überspannung .............................................................................. 29 
7.2 
E-02 Transiente Überspannung ........................................................................... 30 
7.3 
E-03 Transiente Unterspannung .......................................................................... 32 
7.4 
E-04 Jumpstart .................................................................................................... 34 
7.5 
E-05 Load Dump .................................................................................................. 35 
7.6 
E-06 Überlagerte Wechselspannung ................................................................... 36 
7.7 
E-07 Langsames Absenken und Anheben der Versorgungsspannung ............... 38 
7.8 
E-08 Langsames Absenken, schnelles Erhöhen der Versorgungsspannung ...... 40 
7.9 
E-09 Resetverhalten ............................................................................................ 42 
7.10 E-10 Kurze Unterbrechungen .............................................................................. 44 
7.11 E-11 Startimpulse ................................................................................................ 46 
7.12 E-12 Spannungsverlauf bei Bordnetzregelung .................................................... 50 
7.13 E-13 Unterbrechung Pin ...................................................................................... 51 
7.14 E-14 Unterbrechung Stecker ............................................................................... 53 
7.15 E-15 Verpolung .................................................................................................... 54 
7.16 E-16 Masseversatz .............................................................................................. 57 
7.17 E-17 Kurzschluss Signalleitung und Lastkreise ................................................... 58 


### 第 4 页
Seite 4 
VW 80000: 2017-10 
 
 
 
 
 
 
7.18 E-18 Isolationswiderstand .................................................................................... 60 
7.19 E-19 Ruhestrom ................................................................................................... 61 
7.20 E-20 Durchschlagfestigkeit .................................................................................. 62 
7.21 E-21 Rückspeisungen .......................................................................................... 63 
7.22 E-22 Überströme ................................................................................................. 65 
7.23 E-23 Ausgleichsströme mehrerer Versorgungsspannungen ............................... 66 
Teil II – Umweltanforderungen und –prüfungen ....................................................... 67 
8 
Einsatzprofil .......................................................................................................... 67 
8.1 
Lebensdauerauslegung ....................................................................................... 67 
8.2 
Temperaturkollektive ........................................................................................... 67 
9 
Prüfungsauswahl ................................................................................................. 69 
9.1 
Prüfauswahltabelle .............................................................................................. 69 
9.2 
Prüfablaufplan ...................................................................................................... 71 
10 
Mechanische Anforderungen und Prüfungen ................................................. 72 
10.1 M-01 Freier Fall ................................................................................................... 72 
10.2 M-02 Steinschlagprüfung ..................................................................................... 73 
10.3 M-03 Staubprüfung .............................................................................................. 74 
10.4 M-04 Vibrationsprüfung ....................................................................................... 75 
10.5 M-05 Mechanischer Schock................................................................................. 86 
10.6 M-06 Mechanisches Dauerschocken ................................................................... 87 
10.7 M-07 Druckwechselprüfung Kühlkreislauf ............................................................ 88 
11 
Klimatische Anforderungen und Prüfungen ................................................... 89 
11.1 K-01 Hoch-/Tieftemperaturlagerung .................................................................... 89 
11.2 K-02 Stufentemperaturtest ................................................................................... 90 
11.3 K-03 Tieftemperaturbetrieb .................................................................................. 91 
11.4 K-04 Nachlackiertemperatur ................................................................................ 92 
11.5 K-05 Temperaturschock (Komponente) ............................................................... 93 
11.6 K-06 Salzsprühnebelprüfung mit Betrieb, Außenraum ......................................... 94 
11.7 K-07 Salzsprühnebelprüfung mit Betrieb, Innenraum .......................................... 96 
11.8 K-08 Feuchte Wärme, zyklisch ............................................................................ 97 
11.9 K-09 Feuchte Wärme, zyklisch (mit Frost) ........................................................... 98 
11.10 K-10 Wasserschutz – IPX0 bis IPX6K ................................................................. 99 
11.11 K-11 Hochdruck-/Dampfstrahlreinigung ............................................................. 100 
11.12 K-12 Temperaturschock mit Schwallwasser ...................................................... 101 
11.13 K-13 Temperaturschock Tauchen ...................................................................... 104 
11.14 K-14 Feuchte Wärme konstant .......................................................................... 105 
11.15 K-15 Betauungs- und Klimaprüfung ................................................................... 108 
11.16 K-16 Temperaturschock (ohne Gehäuse) .......................................................... 114 
11.17 K-17 Sonnenbestrahlung ................................................................................... 115 
11.18 K-18 Schadgasprüfung ...................................................................................... 116 
12 
Chemische Anforderungen und Prüfungen .................................................. 117 
12.1 C-01 Chemische Prüfungen............................................................................... 117 
13 
Lebensdauerprüfungen ................................................................................... 120 
13.1 L-01 Lebensdauerprüfung mechanisch/hydraulischer Dauerlauf ....................... 120 
13.2 L-02 Lebensdauerprüfung Hochtemperaturdauerlauf ........................................ 121 
13.3 L-03 Lebensdauerprüfung Temperaturwechseldauerlauf .................................. 125 
Anhang A (normativ)  Prüfablauf ............................................................................. 129 
A.1 
Prüfablaufplan .................................................................................................... 129 
A.2 
Sequenzprüfungen ............................................................................................ 130 
A.3 
Prüfungen außerhalb der Sequenz (Parallele Prüfungen) ................................. 131 
A.4 
Lebensdauerprüfungen ...................................................................................... 132 


### 第 5 页
 
  
Seite 5 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Anhang B (normativ)  Typische Temperaturkollektive für verschiedene 
Einbaubereiche .......................................................................................................... 133 
B.1 
Temperaturkollektiv 1 ........................................................................................ 134 
B.2 
Temperaturkollektiv 2 ........................................................................................ 134 
B.3 
Temperaturkollektiv 3 ........................................................................................ 134 
B.4 
Temperaturkollektiv 4 ........................................................................................ 134 
Anhang C (normativ)   Berechnung zur Durchführung der Lebensdauerprüfung 
mechanisch/hydraulischer Dauerlauf ...................................................................... 135 
C.1 
Berechnung ....................................................................................................... 135 
C.2 
Beispiel Berechnung .......................................................................................... 137 
Anhang D (normativ)  Modelle zur Berechnung der Lebensdauerprüfung 
Hochtemperaturdauerlauf ......................................................................................... 139 
D.1 
Anpassung der Prüftemperaturen zur Reduzierung der Prüfzeit ....................... 139 
D.2 
Arrhenius-Modell ................................................................................................ 139 
D.3 
Beispiel Arrhenius-Modell: ................................................................................. 141 
D.4 
Arrhenius-Modell zur Verwendung bei Komponenten mit reduzierter Performance 
bei hohen Temperaturen ............................................................................................. 142 
D.5 
Beispiel Arrhenius-Modell zur Verwendung bei Komponenten mit reduzierter 
Performance bei hohen Temperaturen: ....................................................................... 143 
D.6 
Arrhenius-Modell zur Verwendung bei Komponenten an Kühlmittelkreisläufen . 145 
D.7 
Beispiel Arrhenius-Modell zur Verwendung bei Komponenten an 
Kühlmittelkreisläufen ................................................................................................... 149 
Anhang E (normativ)  Modelle zur Berechnung der Lebensdauerprüfung 
Temperaturwechseldauerlauf ................................................................................... 152 
E.1 
Anpassung der Prüftemperaturen zur Reduzierung der Prüfzyklen ................... 152 
E.2 
Coffin-Manson-Modell ........................................................................................ 152 
E.3 
Beispiel: ............................................................................................................. 154 
E.4 
Coffin-Manson – Modell zur Verwendung bei Komponenten an 
Kühlmittelkreisläufen ................................................................................................... 155 
E.5 
Beispiel Coffin-Manson-Modell zur Verwendung bei Komponenten an 
Kühlmittelkreisläufen ................................................................................................... 158 
Anhang F (normativ)  Modelle zur Berechnung der Prüfung Feuchte Wärme 
Konstant – Schärfegrad 2 ......................................................................................... 160 
F.1 
Lawson-Modell ................................................................................................... 160 
F.2 
Beispiel: ............................................................................................................. 161 
Anhang G (informativ)  Betauungsprüfung, Kammerprogrammierung und 
Diagramme ................................................................................................................. 162 
Anhang H Untersuchungsmethoden zur physikalischen Analyse ........................ 165 
 
 


### 第 6 页
Seite 6 
VW 80000: 2017-10 
 
 
 
 
 
 
1 Geltungsbereich 
Dieses Dokument legt Anforderungen, Prüfbedingungen und Prüfungen an elektrische, 
elektronische und mechatronische Komponenten und Systeme für den Einsatz in 
Kraftfahrzeugen bis 3,5 t fest.  
 
Zusätzliche oder abweichende Anforderungen, Prüfbedingungen und Prüfungen werden 
in den entsprechenden Lastenheften definiert. 
 
Zusätzlich gilt für Teil I: Die Anforderungen, Prüfbedingungen und Prüfungen beziehen 
sich auf das 12 V Bordnetz. Die Prüfungen sind, soweit nicht anders vermerkt, keine 
elektrischen Lebensdauerprüfungen. 
 
 
Hinweis: Die dargestellten Prüfungen dienen der Überprüfung eines Teils der 
geforderten Eigenschaften der Komponente und dienen nicht der 
Bauelementequalifikation oder der Qualifizierung des Fertigungsprozesses.  
 
2 Normative Verweise 
Tabelle 1: Normative Verweise 
ANSI/UL94 
Standard for Safety Tests for Flammability of Plastic Materials 
for Parts in Devices and Appliances 
DIN 75220 
Alterung von Kfz-Bauteilen in Sonnensimulationsanlagen 
DIN 72552-2 
Klemmenbezeichnungen in Kraftfahrzeugen:  
Bedeutung 
DIN EN 13018 
Zerstörungsfreie Prüfung - Sichtprüfung - Allgemeine 
Grundlagen; 
DIN EN ISO/IEC 
17025 
Allgemeine Anforderungen an die Kompetenz von Prüf- und  
Kalibrierlaboratorien 
DIN EN 60068-2-1 
Umgebungseinflüsse - Teil 2-1: Prüfverfahren –  
Prüfung A: Kälte 
DIN EN 60068-2-2 
Umgebungseinflüsse - Teil 2-2: Prüfverfahren –  
Prüfung B: Trockene Wärme 
DIN EN 60068-2-6 
Umgebungseinflüsse - Teil 2-6: Prüfverfahren - Prüfung Fc: 
Schwingen (sinusförmig) 
DIN EN 60068-2-11 
Umweltprüfungen Teil 2: Prüfungen – Prüfung Ka: Salznebel 
DIN EN 60068-2-14 
Umweltprüfungen Teil 2: Prüfungen – Prüfung N: 
Temperaturwechsel 
DIN EN 60068-2-27 
Umgebungseinflüsse - Teil 2-27: Prüfverfahren - Prüfung Ea 
und Leitfaden: Schocken 
DIN EN 60068-2-29 
Umweltprüfungen Teil 2: Prüfungen Eb und Leitfaden: 
Dauerschocken 
DIN EN 60068-2-30 
Umgebungseinflüsse – Teil 2-30: Prüfverfahren – 
Prüfung Db: Feuchte Wärme, zyklisch (12 + 12 Stunden) 
DIN EN 60068-2-38 
Umweltprüfungen Teil 2: Prüfungen – Prüfung Z/AD: 
Zusammengesetzte Prüfung, Temperatur/Feuchte, zyklisch 
DIN EN 60068-2-60 
Umweltprüfungen Teil 2: Prüfungen – Prüfung Ke: 
Korrosionsprüfung mit strömenden Mischgasen 


### 第 7 页
 
  
Seite 7 
VW 80000: 2017-10 
 
 
 
 
 
 
 
DIN EN 60068-2-64 
Umweltprüfungen Teil 2: Prüfverfahren Prüfungen Fh: 
Schwingen, Breitbandrauschen (digital geregelt) und Leitfaden 
DIN EN 60068-2-78 
Umweltprüfungen Teil 2-78: Prüfungen – Prüfung Cab: 
Temperatur/Feuchte, konstant 
DIN EN ISO 11124-2 
Vorbereitung von Stahloberflächen vor dem Auftragen von 
Beschichtungsstoffen - Anforderungen an metallische 
Strahlmittel - Teil 2: Hartguß, kantig (Grit) 
DIN EN ISO 20567-1 
Beschichtungsstoffe –  
Prüfung der Steinschlagfestigkeit von Beschichtungen –  
Teil 1: Multischlagprüfung 
DIN EN ISO 6270-2 
Beschichtungsstoffe - Bestimmung der Beständigkeit gegen 
Feuchtigkeit - Teil 2: Verfahren zur Beanspruchung von Proben 
in Kondenswasserklimaten 
ISO 12103-1 
Road vehicles — Test dust for filter evaluation — Part 1: 
Arizona test dust 
ISO 16750-3  
  
Road vehicles — Environmental conditions and testing for 
electrical and electronic equipment – Part 3: Mechanical loads 
ISO 16750-5 
Road vehicles — Environmental conditions and testing for 
electrical and electronic equipment – Part 5: Chemical loads 
ISO 20653 
Road vehicles — Degrees of protection (IP-Code) — Protection 
of electrical equipment against foreign objects, water and 
access. 
ISO 26262 
Straßenfahrzeuge – Funktionale Sicherheit 
 
3 Begriffe und Definitionen 
3.1 Begriffe und Abkürzungen 
Tabelle 2: Abkürzungen Elektrische Anforderungen und Prüfungen 
Begriff / Abkürzung 
Bedeutung 
Applikationssoftware 
Bezieht sich auf das Verhalten entsprechend der 
Funktionsklasse nach dieser Norm und den Funktionalitäten 
nach Lastenheft, beispielsweise: 
 Aufstartverhalten 
 Einschlafverhalten 
 Regelungen 
 Überlast-, Kurzschluss- und Spielschutz 
 Diagnosen 
Baugruppe 
Mit Bauelementen bestückter elektronischer 
Schaltungsträger (ohne Gehäuse) 
Bauelement / 
Bauelemente 
Elektrisches, elektronisches oder mechatronisches 
Bauelement 
(z. B. Widerstand, Kondensator, Transistor, IC, Relais) 
Betrieb 
(übergreifend) 
Betriebsmodus, in dem übergreifend aus allen relevanten 
Modi die minimale bzw. maximale Eigenerwärmung erzeugt 
wird. Dieser Betriebsmodus wird zusätzlich festgelegt. 
DAE 
Druckausgleichselement 


### 第 8 页
Seite 8 
VW 80000: 2017-10 
 
 
 
 
 
 
Derating 
Absichtlich eingeschränkte Funktion z. B. Veränderung der 
Leistungsaufnahme in Abhängigkeit von Spannung und/oder 
Temperatur 
DUT 
Device Under Test – siehe Prüfling 
Fahren 
Betriebsmode eines Fahrzeugs, das durch den Kunden 
entriegelt und in Betrieb genommen wurde (Kl. 15 an). 
Ein Fahrzeug mit elektrifiziertem Antrieb ist nicht mit einer 
Ladestation/Steckdose verbunden. 
Fahrzeugaufbau 
Betriebsmodus, der den Montageprozess widerspiegelt. Es 
wird auf Komponentenebene unterschieden zwischen nicht 
angeschlossenem Stecker „unverbaut“ und angeschlossenem 
Stecker „montage“. In beiden Fällen ist die Komponente 
spannungsfrei und stromlos. 
Funktionen 
Beinhaltet systemspezifische Funktionen und 
Diagnosefunktionen 
Hardware Freeze 
Der Zeitpunkt während der Entwicklung, ab dem 
Änderungen der Hardware nicht mehr möglich sind. 
ICT 
 
In Circuit Test 
höhere Verfügbarkeit Die Komponente benötigt für ihre Funktion eine Anforderung 
von mindestens ASIL A gemäß ISO 26262 an die 
elektrische Energieversorgung 
Klimakammer mit 
Betauungsoption 
Ein in der Klimakammer vorhandenes speziell geregeltes 
Wasserbad, mit dem die nötige Wassermenge als 
Wasserdampf umgesetzt wird. 
Die Intensität des Betauungsfilmes auf dem 
Schaltungsträger hängt von der thermischen Masse, 
relativen Luftfeuchte und dem Temperaturgradienten des 
Wasserbades ab. 
Während der Betauungsphase ist die Klimaregelung der 
Klimakammer abgeschaltet. Die Prüfraumtemperatur wird 
über das temperaturgeregelte Wasserbad geregelt. 
Komponente 
komplettes Gerät, Steuergerät oder Mechatronik (mit 
Gehäuse) 
Kurzschluss 
Ein Kurzschluss eines Lastausganges wird definiert durch 
einen niederohmigeren Belastungsfall als mit spezifizierter 
Last bis zum Grenzfall 0 Ω. Schleichender Kurzschluss ist 
eingeschlossen, d.h. Strom knapp unter der 
Kurzschlusserkennung. 
Ein Kurzschluss kann dauernd anliegen (Komponente in 
Betrieb / nicht in Betrieb). 
Laden 
Betriebsmodus eines Fahrzeugs mit elektrifiziertem Antrieb, 
das geparkt und mit einer Ladestation/Steckdose verbunden 
ist. Die Fahrzeugbatterie wird geladen. 


### 第 9 页
 
  
Seite 9 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Lastenheft 
Wird in dieser Norm als Überbegriff für beispielsweise:  
 Zeichnung 
 Bauteil- / Komponentenlastenheft 
 Funktionslastenheft 
 Querschnittslastenheft 
 Systemlastenheft 
 Erprobungslastenheft 
 Diagnoselastenheft  
 EMV Lastenheft  
 Qualitätslastenheft 
verwendet. 
On-Grid-Parken 
Betriebsmodus eines Fahrzeugs mit elektrifiziertem Antrieb, 
das geparkt und mit einer Ladestation/Steckdose verbunden 
ist. Die Fahrzeugbatterie wird nicht geladen. Das Fahrzeug 
kann mit der Ladestation kommunizieren, ein Ladevorgang 
ist abgeschlossen oder kann jederzeit gestartet werden. 
Eine erweiterte Nutzung der Fahrzeugbatterie z. B. als 
Pufferspeicher durch den Netzbetreiber ist möglich. 
Off-Grid-Parken 
Betriebsmodus eines Fahrzeugs, das geparkt ist. Ein 
Fahrzeug mit elektrifiziertem Antrieb ist nicht mit einer 
Ladestation/Steckdose verbunden. 
Power-User 
Realer Anwendungsfall mit maximaler vorstellbarer Nutzung  
Prüfling 
Das zu prüfende System oder die zu prüfende Komponente 
PTB 
Physikalisch-Technische Bundesanstalt 
PSD 
Power Spectral Density 
Schaltungsträger 
Unbestückter Verdrahtungsträger für Elektronik allgemein 
(unbestückte Leiterplatte, Keramik, Leadframe, Flexband, 
...) 
startrelevant 
Komponenten, die direkt oder indirekt für einen 
Verbrennungsmotorstartvorgang benötigt werden 
System 
Funktionell verknüpfte Komponenten, z. B. 
Bremsregelsystem (Steuergerät, Hydraulik, Sensoren) 
Vorkonditionierung 
Betriebsmodus eines Fahrzeugs mit elektrifiziertem Antrieb, 
das geparkt ist und dabei mit einer Ladestation/Steckdose 
verbunden sein kann. Das Fahrzeug führt eine thermische 
Konditionierung durch. Typischerweise betrifft dies eine 
Innenraum- und/oder eine HV-Batterievorkonditionierung. 
 
 
 
 


### 第 10 页
Seite 10 
VW 80000: 2017-10 
 
 
 
 
 
 
3.2 Spannungen und Ströme 
Tabelle 3: Abkürzung zu Spannungen und Strömen 
UN 
Nennspannung 
UBmin 
Untere Betriebsspannungsgrenze 
UB 
Betriebsspannung 
UBmax 
Obere Betriebsspannungsgrenze 
Umax 
Maximalspannung, die während einer Prüfung auftreten kann 
Umin 
Minimalspannung, die während einer Prüfung auftreten kann 
UPP 
Spitze-Spitze Spannung 
Ueff 
Effektivwert einer Spannung 
Utest 
Prüfspannung 
IN 
Nennstrom 
GND 
Geräte-Masse 
UA, UT, US, UR 
Spannungsebene des Startspannungsimpulses 
 
3.2.1 Spannungen bei Komponenten mit erweiterten Anforderungen 
Tabelle 4: Abkürzung zu erweiterten Spannungsdefinitionen 
UBmin,HV 
Untere Betriebsspannungsgrenze HV- Untere DC Betriebsspannung 
UB,HV 
Betriebsspannung HV- DC Betriebsspannung 
UBmax,HV 
Obere Betriebsspannungsgrenze HV- Obere DC Betriebsspannung 
3.3 Temperaturen 
Tabelle 5: Abkürzung zu Temperaturen 
Tmin 
minimale Umgebungstemperatur 
TRT 
Raumtemperatur 
Tmax 
maximale Umgebungstemperatur 
Top,min 
Minimale Umgebungstemperatur für Komponenten mit 
Überlastschutz / Tieftemperaturschutz, bei der noch volle 
Funktionalität gefordert wird 
Top,max 
Maximale Umgebungstemperatur für Komponenten mit 
Überlastschutz / Übertemperaturschutz, bei der noch volle 
Funktionalität gefordert wird 
Ttest 
Prüftemperatur 
3.3.1 Temperaturen bei Komponenten an Kühlmittelkreislauf 
Tabelle 6: Temperaturdefinitionen Kühlmittelkreislauf 
Tkühl,nom 
Nominale Kühlmitteltemperatur Kühlmittelkreislauf 
Tkühl,min 
Minimale Kühlmitteltemperatur Kühlmittelkreislauf 
Tkühl, max 
Maximale Kühlmitteltemperatur Kühlmittelkreislauf 
 
 
 


### 第 11 页
 
  
Seite 11 
VW 80000: 2017-10 
 
 
 
 
 
 
 
3.4 Zeiten 
Tabelle 7: Abkürzung zu Zeiten 
tPrüf 
Prüfdauer 
tBetrieb 
Betriebsdauer über Lebensdauer 
tr 
Anstiegszeit/Risetime (z. B. eines Spannungsverlaufes) 
tf 
Abfallzeit/Falltime (z. B. eines Spannungsverlaufes) 
3.5 Innenwiderstand, Klemmenbezeichnung, Frequenz 
Tabelle 8: Abkürzung zu Widerständen, Klemmen und Frequenzen 
Ri 
Innenwiderstand der Quelle inklusive des 
Versorgungsleitungssatzes (siehe Kapitel 5.1) 
Klemmenbezeichnungen 
Nach DIN 72552-2 
f 
Frequenz 
 
3.6 Standardtoleranzen 
Falls nicht anders angegeben, gelten die Toleranzen nach Tabelle 9. 
Die Toleranzen beziehen sich auf den geforderten Messwert. 
Tabelle 9: Standardtoleranzen 
Frequenzen 
± 1 % 
Temperaturen 
± 2 °C 
Luftfeuchtigkeit 
± 5 % 
Zeiten 
+ 5 %; 0 % 
Spannungen 
± 2 % 
Ströme 
± 2 % 
Vibrationen 
± 3 dB 
Vibration PSD 
± 5 % 
3.7 Standardwerte 
Falls nicht anders angegeben, gelten die Standardwerte nach Tabelle 10. 
Tabelle 10: Standardwerte 
Raumtemperatur 
TRT = 23 °C ± 5 °C  
Luftfeuchtigkeit 
Frel = 25 % bis 75 % relative Feuchte  
Prüftemperatur 
Ttest = TRT 
Betriebsspannung 
(für Prüfung) 
UB = 14 V 
 
 


### 第 12 页
Seite 12 
VW 80000: 2017-10 
 
 
 
 
 
 
4 Allgemeine Anforderungen 
4.1 Abtastraten und Messwertauflösungen 
Die Abtastrate bzw. Bandbreite des Messsystems ist der jeweiligen Prüfung 
anzupassen. Es müssen alle Messwerte mit allen Maximalwerten (Peaks) 
aufgezeichnet werden. 
 
Die Auflösung der Messwerte ist der jeweiligen Prüfung anzupassen. Es muss 
gewährleistet sein, dass auftretende Spannungsspitzen nicht zu einem Überlauf führen 
oder bei zu geringer Auflösung nicht messbar sind. Eine Datenreduktion/-abstraktion 
(z. B. Grenzwertüberwachung, Busbotschaftenauswertung) darf Auffälligkeiten nicht 
unterdrücken. 
4.2 Funktionszustände 
4.2.1 Allgemein 
Dieser Abschnitt beschreibt den Funktionszustand des Prüflings vor, während und nach 
der Prüfung. Das funktionale Verhalten (inklusive Derating z. B. in Bezug auf 
Temperatur und Spannung) der Komponente in den Funktionszuständen sowie die 
Kundenwahrnehmung (z. B. optisch, akustisch, haptisch, thermisch) ist durch den 
Auftraggeber in der Zeichnung oder im Lastenheft zu definieren. 
In allen Fällen müssen Speicherfunktionen immer im Funktionszustand A bleiben. Die 
Integrität der nicht flüchtigen Speicher muss zu jeder Zeit sichergestellt werden. Die 
zeitlichen Abläufe der Funktionszustände sind im Lastenheft anzugeben. Erlaubte 
Fehlerspeichereinträge sind mit dem Auftraggeber abzustimmen und festzuschreiben. 
Bei den Funktionszuständen A bis D ist keine Schädigung des Prüflings zulässig. 
Undefinierte Funktionen sind zu keinem Zeitpunkt zulässig. Die in den Datenblättern 
spezifizierten zulässigen Grenzwerte (z. B. elektrisch, thermisch, mechanisch) der im 
Prüfling verbauten elektrischen/elektronischen Bauelemente dürfen nicht überschritten 
werden. Der Nachweis erfolgt mindestens durch den P-02 Parametertest (klein) gemäß 
Kapitel 4.7.2. 
4.2.2 Funktionszustand A 
Der Prüfling erfüllt alle Funktionen wie spezifiziert. 
4.2.3 Funktionszustand B 
Wird nicht verwendet. 
4.2.4 Funktionszustand C 
Der Prüfling erfüllt während der Beaufschlagung mit den Prüfparametern eine oder 
mehrere Funktionen nicht. Nach Ende der Beaufschlagung mit den Prüfparametern 
muss der Prüfling automatisch oder durch die im Lastenheft spezifizierten externen 
Trigger sofort wieder Funktionszustand A erreichen. Undefinierte Funktionen sind zu 
keinem Zeitpunkt zulässig. 


### 第 13 页
 
  
Seite 13 
VW 80000: 2017-10 
 
 
 
 
 
 
 
4.2.5 Funktionszustand D1 
Der Prüfling erfüllt während der Beaufschlagung mit den Prüfparametern eine oder 
mehrere Funktionen nicht. Nach Ende der Beaufschlagung mit den Prüfparametern 
muss der Prüfling durch einen Klemmenwechsel (ggf. mit Busruhe) wieder 
Funktionszustand A erreichen 
4.2.6 Funktionszustand D2 
Der Prüfling erfüllt während der Beaufschlagung mit den Prüfparametern eine oder 
mehrere Funktionen nicht. Nach Ende der Beaufschlagung mit den Prüfparametern 
muss der Prüfling durch einen einfachen Eingriff (z. B. Austausch einer defekten 
Sicherung) wieder Funktionszustand A erreichen 
4.2.7 Funktionszustand E 
Der Prüfling erfüllt während der Beaufschlagung mit den Prüfparametern eine oder 
mehrere Funktionen nicht und muss nach Ende der Beaufschlagung mit den 
Prüfparametern repariert oder ausgetauscht werden. 
Die Anforderung auf Nichtentflammbarkeit nach UL94-v0 ist durch den Prüfling zu 
erfüllen. 
4.3 Betriebsmodi 
Bei Fahrzeugen mit rein verbrennungsmotorischem Antrieb kann der Betrieb des 
Fahrzeugs über seine Lebensdauer in der Regel in folgende Betriebsmodi unterteilt 
werden: 
 Fahren 
 Parken (im weiteren Dokument als Off-Grid Parken bezeichnet)  
 Fahrzeugaufbau 
 Betrieb (übergreifend) 
 
Bei Fahrzeugen mit elektrifiziertem Antrieb sind gegebenenfalls zusätzliche 
Betriebsmodi zu berücksichtigen:  
 Laden 
 Vorkonditionierung 
 On-Grid Parken 
 
Zur Ableitung der Testanforderungen sind alle für die Komponente relevanten 
Betriebsmodi zu berücksichtigen. 
4.4 Betriebszustände 
4.4.1 Allgemein 
Um bei den Prüfungen die unterschiedlichen in der Realität vorkommenden 
Belastungen der Komponente abbilden zu können, werden folgende Betriebszustände 
definiert. 
Falls für die Komponente weitere Betriebszustände relevant sind, sind diese mit dem 
Auftraggeber festzulegen. 


### 第 14 页
Seite 14 
VW 80000: 2017-10 
 
 
 
 
 
 
4.4.1.1 Betriebszustand – nicht elektrisch angeschlossen 
Der Prüfling ist spannungsfrei und unbestromt. Ein vorhandener Kühlmittelkreislauf ist 
nicht befüllt und die Anschlüsse sind abgedichtet. 
Es wird unterschieden zwischen  
- 
„unverbaut“ Prüfling ohne Stecker und Leitungssatz 
- 
 „montage“ Prüfling mit angeschlossenem Stecker und Leitungssatz 
4.4.1.2 Betriebszustand – elektrisch angeschlossen und niedrige Betriebslast  
Der Prüfling ist mit der im jeweiligen Betriebsmodus niedrigsten, real möglichen 
Betriebslast zu betreiben. Dies kann auch bedeuten dass der Prüfling ohne Betriebslast 
betrieben wird. 
Die Spannungsversorgung aller für die Komponente relevanten Spannungslagen (12 V 
Bordnetz, 48 V Bordnetz, HVAC und HVDC) und gegebenenfalls die Bus-Aktivität sind 
gemäß der realen Situation im Fahrzeug für den jeweiligen Betriebsmodus 
nachzubilden. 
Ein vorhandener Kühlmittelkreislauf ist zu befüllen, die Kühlmittelschläuche sind 
anzuschließen. Durchfluss und Temperatur des Kühlmediums sind bei Bedarf - wie im 
Lastenheft festgelegt – einzustellen. 
 
Bezeichnung des Betriebszustandes im weiteren Dokument mit „min“ 
4.4.1.1 Betriebszustand – elektrisch angeschlossen und hohe Betriebslast 
Der Prüfling ist mit maximaler Betriebslast gemäß Auslegungslastprofil zu betreiben 
(Power-User, aber kein Missbrauchsfall). 
Der Prüfling muss dabei so betrieben werden, dass er eine maximale Verlustleistung 
generiert (zum Beispiel durch realistische Maximierung einer kontinuierlichen 
Ausgangsleistung oder durch häufige Ansteuerung externer Lasten). 
Die Spannungsversorgung aller für die Komponente relevanten Spannungslagen (12 V 
Bordnetz, 48 V Bordnetz, HVAC und HVDC) und gegebenenfalls die Bus-Aktivität sind 
gemäß der realen Situation im Fahrzeug für den jeweiligen Betriebsmodus 
nachzubilden. 
Ein vorhandener Kühlmittelkreislauf ist zu befüllen, die Kühlmittelschläuche sind 
anzuschließen. Durchfluss und Temperatur des Kühlmediums sind bei Bedarf - wie im 
Lastenheft festgelegt – einzustellen. 
 
Bezeichnung des Betriebszustandes im weiteren Dokument mit „max“ 
4.5 Betriebsarten 
4.5.1 Allgemein 
Generell gilt:  
Die Betriebsart der Komponente ergibt sich aus der Zuordnung der Betriebszustände zu 
den jeweiligen Betriebsmodi.(BetriebsmodusBetriebszustand) 
 
Details der Betriebsarten, Betriebslasten (z. B. Ansteuerung, Busaktivität, 
Busbotschaften, Originalsensoren, Originalaktoren oder Ersatzbeschaltungen) und der 
erforderlichen Randbedingungen sind zwischen Auftraggeber und Auftragnehmer 
abzustimmen und zu dokumentieren.  


### 第 15 页
 
  
Seite 15 
VW 80000: 2017-10 
 
 
 
 
 
 
 
4.5.2 Betriebsarten für den Betriebszustand „nicht elektrisch 
angeschlossen“  
Dem Betriebsmodus „Fahrzeugaufbau“ wird der Betriebszustand „nicht elektrisch 
angeschlossen“ zugeordnet. Es werden folgende Betriebsarten der Komponente 
unterschieden:  
 
- 
Fahrzeugaufbauunverbaut: 
 
Hinweis: In früheren Ausgaben des Dokumentes wurde diese Betriebsart mit I.a 
bezeichnet. 
 
- 
Fahrzeugaufbaumontage: 
 
Hinweis: In früheren Ausgaben des Dokumentes wurde diese Betriebsart mit I.b 
bezeichnet. 
4.5.3 Betriebsarten für den Betriebszustand „elektrisch angeschlossen 
und niedrige Betriebslast“ 
Den Betriebsmodi „Fahren“, „Laden“, „Vorkonditionierung“, „On-Grid Parken“ und „Off-
Grid Parken“ wird der Betriebszustand „elektrisch angeschlossen und niedrige 
Betriebslast zugeordnet“. Es ergeben sich die Betriebsarten wie folgt:  
- 
Fahrenmin  
- 
Ladenmin 
- 
Vorkonditionierungmin  
- 
On-Grid Parkenmin  
- 
Off-Grid Parkenmin 
 
Aus diesen Betriebsarten mit niedriger Betriebslast ist diejenige Betriebsart zu 
bestimmen, bei der die Komponente die geringste Verlustleistung generiert. Diese wird 
zusätzlich zu den anderen Betriebsarten festgelegt und wie folgt bezeichnet: 
 
- 
Betriebmin  
 
Gibt es mehrere Betriebsarten mit niedriger Betriebslast, bei der die Komponente 
Verlustleistung generiert oder spezielle Funktionalitäten aufweist, muss die 
Komponente intermittierend in diesen Betriebsarten betrieben werden; dabei müssen 
alle Funktionalitäten der relevanten Betriebsarten berücksichtigt werden. 
4.5.4 Betriebsarten für den Betriebszustand „elektrisch angeschlossen 
und hohe Betriebslast“ 
Den Betriebsmodi „Fahren“, „Laden“, „Vorkonditionierung“, „On-Grid Parken“ und „Off-
Grid Parken“ wird der Betriebszustand „elektrisch angeschlossen und hohe 
Betriebslast“ zugeordnet. Es ergeben sich die Betriebsarten wie folgt:  
- 
Fahrenmax 
- 
Ladenmax 
- 
Vorkonditionierungmax  
- 
On-Grid Parkenmax 
- 
Off-Grid Parkenmax 
 


### 第 16 页
Seite 16 
VW 80000: 2017-10 
 
 
 
 
 
 
Aus diesen Betriebsarten mit hoher Betriebslast ist diejenige Betriebsart zu bestimmen, 
bei der die Komponente die größte Verlustleistung generiert. Diese wird zusätzlich zu 
den anderen Betriebsarten festgelegt und wie folgt bezeichnet:  
 
- 
Betriebmax 
 
Gibt es mehrere Betriebsarten mit hoher Betriebslast, bei der die Komponente eine 
signifikante Verlustleistung generiert oder spezielle Funktionalitäten aufweist, muss die 
Komponente intermittierend in diesen Betriebsarten betrieben werden; dabei müssen 
alle Funktionalitäten der relevanten Betriebsarten berücksichtigt werden. 
4.5.5 Übersicht der Betriebsarten 
Tabelle 11: Einteilung und Bezeichnung Betriebsarten 
Betriebsmodus 
Betriebsart: nicht 
elektrisch 
angeschlossen 
Betriebsart: elektrisch 
angeschlossen und 
niedrige Betriebslast 
Betriebsart: elektrisch 
angeschlossen und 
hohe Betriebslast 
Fahrzeugaufbau 
 
 Fahrzeugaufbauunverbaut 
(früher I.a) 
 
 
 Fahrzeugaufbaumontage 
(früher I.b) 
 
 
Fahren 
 
Fahrenmin 
Fahrenmax 
Laden 
 
Ladenmin 
Ladenmax 
Vorkonditionierung 
 
Vorkonditionierungmin 
Vorkonditionierungmax 
On-Grid Parken 
 
On-Grid Parkenmin 
On-Grid Parkenmax 
Off-Grid Parken 
 
Off-Grid Parkenmin 
Off-Grid Parkenmax 
 
 
 
 
übergreifend 
 
Betriebmin  
 
Betriebmax  
 
4.5.6 Anwendung der Betriebsarten 
Analog Tabelle 11 sind die Betriebsarten für die jeweilige Komponente vor Beginn der 
Erprobung festzulegen und mit dem Auftraggeber abzustimmen. 
Tabelle 12: Beispiel Betriebsarten – Prüfling elektrisch angeschlossen 
Betriebsart 
Autoradio mit 
Navigation 
Diebstahlwarnanlage 
On-Board-Lader 
Fahrenmin 
Komponente vom 
Fahrer abgeschaltet, 
BUS/µC’s aktiv 
Keine Funktion 
Keine Funktion 
Spannungsversorgunge
n (12 V-Bordnetz und 
ggf. HVDC) liegen an 
Fahrenmax 
Komponente 
eingeschaltet 
(Datenbusse, 
Laufwerke, Navigation, 
Endstufen), aktiv 
Keine Funktion 
Keine Funktion 
Spannungsversorgunge
n (12 V-Bordnetz und 
ggf. HVDC) liegen an 


### 第 17 页
 
  
Seite 17 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Ladenmin 
Nicht relevant 
Nicht relevant 
Ladevorgang mit 
niedriger Ladeleistung, 
Spannungsversorgunge
n (12 V-Bordnetz, HVAC 
und HVDC) liegen an 
Ladenmax 
Ladevorgang mit hoher 
Ladeleistung, 
Spannungsversorgunge
n (12 V-Bordnetz, HVAC 
und HVDC) liegen an 
Vorkonditionierungmin 
Kein Ladevorgang, 
Versorgung der zur 
Vorkonditionierung 
benötigten 
Komponenten mit 
niedriger Leistung, 
Spannungsversorgunge
n (12 V-Bordnetz, HVAC 
und HVDC) liegen an 
Vorkonditionierungmax 
Kein Ladevorgang, 
Versorgung der zur 
Vorkonditionierung 
benötigten 
Komponenten mit hoher 
Leistung, 
Spannungsversorgunge
n (12 V-Bordnetz, HVAC 
und HVDC) liegen an 
On-Grid Parkenmin 
Kein Ladevorgang, 
Power Line 
Communication aktiv 
On-Grid Parkenmax 
Off-Grid Parkenmin 
Komponente im Sleep-
Modus, Nachlauf 
beendet 
Vom Kunden deaktiviert 
Keine Funktion 
Kein Ladevorgang, 
Spannungsversorgung 
(nur 12 V-Bordnetz) 
liegt an 
Off-Grid Parkenmax 
Komponente 
eingeschaltet 
(Datenbusse, 
Laufwerke, Navigation, 
Endstufen), aktiv 
Funktion aktiv 
Betriebmin 
Komponente im Sleep-
Modus, Nachlauf 
beendet (entspricht Off-
Grid Parkenmin) 
Keine Funktion 
(entspricht Fahrenmin) 
Kein Ladevorgang, 
Spannungsversorgung 
(nur 12 V-Bordnetz) 
liegt an (enspricht Off-
Grid Parkenmin) 
Betriebmax 
Komponente 
eingeschaltet 
(Datenbusse, 
Laufwerke, Navigation, 
Endstufen), aktiv 
(entspricht Fahrenmax, 
Off-Grid Parkenmax ) 
Funktion aktiv 
(entspricht Off-Grid 
Parkenmax) 
Ladevorgang mit hoher 
Ladeleistung, 
Spannungsversorgunge
n (12 V-Bordnetz, HVAC 
und HVDC) liegen an 
(entspricht Ladenmax) 
 
 
 


### 第 18 页
Seite 18 
VW 80000: 2017-10 
 
 
 
 
 
 
4.6 Durchtemperierung 
Eine unter definierten Betriebsbedingungen einer konstanten Umgebungstemperatur 
ausgesetzte Komponente gilt ab dem Zeitpunkt als durchtemperiert, ab dem sich die 
Temperatur im weiteren zeitlichen Verlauf an keiner Stelle der Komponente um mehr 
als ±3 °C verändert. 
 
Diese Zeit bis zur vollständigen Durchtemperierung ist vom Auftragnehmer 
experimentell zu bestimmen und in der Prüfdokumentation anzugeben. 
Bei Temperaturwechselprüfungen sind die Prüflinge nach Erreichen der 
Durchtemperierung bei den vorgegebenen Temperatur-Eckwerten zusätzlich für eine 
definierte Zeit zu belassen, damit sich in der Komponente Spannungen in Dehnungen 
umsetzen können. Diese zusätzliche Haltezeit ist bei den jeweiligen Prüfungen 
angegeben. 
4.7 Parameterprüfung  
Im Lastenheft ist ein Satz sensitiver Parameter, sogenannter Schlüsselparameter, wie 
z. B. Ruhestromaufnahme, Betriebsströme, Ausgangsspannungen, 
Übergangswiderstände, Eingangsimpedanzen, Signalraten (Anstiegs- und Abfallzeiten), 
und Busspezifikationen zu definieren. Diese Parameter müssen vor dem Start und nach 
dem Ablauf jeder Prüfung auf ihre Übereinstimmung mit der Spezifikation überprüft 
werden. 
Bei Komponenten mit Anbindung an den Kühlmittelkreislauf sind die Parametertests bei 
TRT mit Tkühl,nom, bei Tmax mit Tkühl,max und bei Tmin mit Tkühl,min durchzuführen. 
Falls im Lastenheft nicht abweichend festgelegt, sind bei Komponenten mit HV-
Versorgung die Parametertests bei UBmin mit UBmin,HV, bei UB mit UB,HV und bei UBmax mit 
UBmax,HV durchzuführen. 
 
Die Parameterprüfung ist unmittelbar nach Abschluss der Prüfung durchzuführen. 
Die Zeit zwischen Ende der Prüfung und Durchführung der Parameterprüfung ist im 
Prüfbericht zu dokumentieren. 
 
 


### 第 19 页
 
  
Seite 19 
VW 80000: 2017-10 
 
 
 
 
 
 
 
4.7.1 P-01 Parametertest (Funktionstest)  
4.7.1.1 Zweck 
Der Parametertest (Funktionstest) soll den Nachweis der fehlerfreien Funktion eines 
Prüflings bei einer angegebenen Temperatur und bei den Spannungen UBmin, UB und 
UBmax erbringen. 
4.7.1.2 Prüfung 
Es müssen die Schlüsselparameter gemessen und aufgezeichnet werden. Das 
funktionale Verhalten bei einer angegebenen Temperatur und jeweils bei den 
Spannungen UBmin, UB und UBmax muss überprüft werden. 
Die Grundfunktionalitäten der Komponenten müssen gemessen werden. 
Bei Komponenten mit Fehlerspeicher muss der Inhalt des Fehlerspeichers ausgelesen 
werden. 
 
4.7.1.3 Anforderung 
Funktionszustand A. 
Änderungen der Werte der Schlüsselparameter, der Grundfunktionalität der 
Komponente oder der Fehlerspeichereinträge müssen bezüglich der vorangegangenen 
Prüfbelastungen gegenüber dem Neuzustand bewertet werden. 
Die Ergebnisse müssen im Prüfbericht dokumentiert werden. 
4.7.2 P-02 Parametertest (klein) 
4.7.2.1 Zweck 
Der Parametertest (klein) soll den Nachweis der fehlerfreien Funktion eines Prüflings 
bei Raumtemperatur und Betriebsspannung erbringen. 
4.7.2.2  Prüfung 
Es müssen die Schlüsselparameter gemessen und aufgezeichnet werden. Das 
funktionale Verhalten der Komponenten bei TRT und UB muss überprüft werden. 
Die Komponenten müssen im Rahmen einer Sichtprüfung nach DIN EN 13018 geprüft 
werden.  
Bei Komponenten mit Fehlerspeicher muss der Inhalt des Fehlerspeichers ausgelesen 
und dokumentiert werden. Anschließend ist der Fehlerspeichereintrag zu löschen. 
Es ist eine Dokumentation des Prüfablaufs und der Ergebnisse zu erstellen. 
4.7.2.3 Anforderung 
Funktionszustand A. 
Änderungen der Werte der Schlüsselparameter, des funktionalen Verhaltens oder der 
Fehlerspeichereinträge sowie Auffälligkeiten in der Sichtprüfung müssen bezüglich der 
vorangegangenen Prüfbelastungen gegenüber dem Neuzustand bewertet werden.  
Die Sichtprüfung ist auf äußere Beschädigungen / Veränderungen z. B. Risse, Auf-
/Abplatzungen, Verfärbungen, Verformungen etc. zu bewerten. Optische Auffälligkeiten 
sind nicht zulässig.  
Lose Teile im Inneren des Gerätes sind nicht zulässig. 


### 第 20 页
Seite 20 
VW 80000: 2017-10 
 
 
 
 
 
 
4.7.3 P-03 Parametertest (groß) 
4.7.3.1 Zweck 
Der Parametertest (groß) soll den Nachweis der fehlerfreien Funktion bei bestimmten 
Temperaturen und Spannungen erbringen. 
4.7.3.2 Prüfung 
Es müssen die Schlüsselparameter gemessen und das funktionale Verhalten der 
Komponenten bei den Temperaturen Tmax, TRT und Tmin jeweils bei den Spannungen 
UBmin, UB und UBmax gemessen werden. 
Interne und externe messbare Kenngrößen zur Bewertung der Genauigkeit und 
Funktion der Komponenten sind in diesem Test aufzuzeichnen.  
Es ist eine Referenz zu bilden, gegen die Veränderungen der Prüflinge durch 
Prüfbelastungen per Vergleich ermittelt werden können. 
Die Aufzeichnung ist im Prüfbericht zu dokumentieren und zu bewerten. Insbesondere 
betrifft dies folgende Daten: 
 Alle Funktionsgrößen 
 Farbort, Ausleuchtung und Kontrast von Leuchtmitteln und Anzeigen 
 Kennlinien (Sensoren, Wandler, Motoren) 
 Fehlerspeichereinträge 
 Reset- und Fehlerzählerstände 
 Kontrolle des EEPROM-Inhaltes 
 Zeitverlauf des aufgenommenen Stromes im Übergang von Betriebmin nach 
Betriebmax (Ziel: Ermittlung der Alterung elektrischer Bauelemente anhand 
Stromverlaufsänderungen) 
 Haptik 
 Akustik 
 Maßhaltigkeit (Verformungen), Spalte, Funktion von Clipsen 
 Betätigungskräfte / -momente 
 Bei dichten Prüflingen ist deren Dichtheit gemäß Kapitel 4.9 zu prüfen 
Die Komponenten müssen im Rahmen einer Sichtprüfung nach DIN EN 13018 geprüft 
werden. Zur Identifikation von losen Teilen im Inneren des Gerätes ist eine 
Schüttelprüfung per Hand durchzuführen. 
Bei Komponenten mit Fehlerspeicher muss der Inhalt des Fehlerspeichers ausgelesen 
und dokumentiert werden. Anschließend ist der Fehlerspeichereintrag zu löschen. 
Es ist eine Dokumentation des Prüfablaufs und der Ergebnisse zu erstellen. 
******* Anforderung 
Funktionszustand A 
Die vorgegebenen Toleranzen in Form und Funktion müssen eingehalten werden. 
Änderungen der Werte der Schlüsselparameter, des funktionalen Verhaltens oder 
Auffälligkeiten in der Sichtprüfung müssen bezüglich der vorangegangenen 
Prüfbelastungen gegenüber dem Neuzustand bewertet werden. 
Die Sichtprüfung ist auf äußere Beschädigungen / Veränderungen z. B. Risse, Auf-
/Abplatzungen, Verfärbungen, Verformungen etc. zu bewerten. Optische Auffälligkeiten 
sind nicht zulässig.  
Lose Teile im Inneren des Gerätes sind nicht zulässig. 
Fehlerspeichereinträge und Zählerveränderungen müssen genau diejenigen sein, die 
durch die Prüfung und die Funktionsanforderung hätten ausgelöst werden müssen. 


### 第 21 页
 
  
Seite 21 
VW 80000: 2017-10 
 
 
 
 
 
 
 
4.7.4 P-04 Physikalische Analyse 
4.7.4.1 Zweck 
Die physikalische Analyse ist nach jeder Erprobungsphase (B-Muster, C-Muster, ...) 
durchzuführen, um Veränderungen der Komponente im Vergleich zum Neuzustand zu 
erkennen. 
4.7.4.2 Prüfung 
Zur physikalischen Analyse erforderliche Untersuchungsmethoden gemäß Anhang H 
sind zwischen Auftraggeber und Auftragnehmer abzustimmen und zu dokumentieren. 
Alle Prüflinge müssen geöffnet und einer Sichtprüfung nach DIN EN 13018 unterzogen 
werden. 
Zeigt ein Prüfling Auffälligkeiten, muss die weitere Analyse ggf. unter Hinzunahme 
weiterer Prüflinge oder der unter Einsatz zusätzlicher Analysemethoden mit dem 
Auftraggeber abgestimmt werden. 
4.7.4.3 Anforderung 
Die Ergebnisse müssen im Prüfbericht dokumentiert und bewertet werden 
4.8 Kontinuierliche Parameterüberwachung mit Driftanalyse 
Während der gesamten Prüfung müssen die zu überwachenden Schlüsselparameter 
aufgezeichnet werden.  
 
Bei Komponenten mit Fehlerspeicher muss der Fehlerspeicher kontinuierlich überwacht 
und Einträge dokumentiert werden. 
 
Die aus der kontinuierlichen Parameterüberwachung gewonnenen Daten müssen auf 
Trends und Driften untersucht werden, um Auffälligkeiten, Alterung oder Fehlfunktionen 
der Komponente zu erkennen. 
 
 


### 第 22 页
Seite 22 
VW 80000: 2017-10 
 
 
 
 
 
 
4.9 Dichtheit 
4.9.1 Dichtheitsanforderung 
Die erforderliche Dichtheit einer Komponente bezogen auf den in sich geschlossenen 
Elektronikraum gegenüber der Umwelt oder anderen Räumen wie zum Beispiel den des 
Kühlmittelkanals eines Kühlmittelkreislaufs wird als Dichtheitsanforderung beschrieben. 
Die Dichtheitsanforderung wird als Grenzleckrate definiert und ist durch eine 
Dichtheitsprüfung nachzuweisen. 
 
Für die Grenzleckage bei Steuergeräten kann ein Initialwert von 1-10 cm³/min bei pΔ = 
300 mbar für Umweltbelastungen in Form von Wasser angenommen werden. Dieser 
muss komponentenspezifisch und in Abhängigkeit des umgebenden Mediums 
angepasst und durch Messungen verifiziert werden. 
 
Für Komponenten ohne geschlossenes Gehäuse ist die Definition und der Nachweis 
der Grenzleckrate nicht erforderlich. 
4.9.2 Dichtheitsprüfung 
Mit einer Dichtheitsprüfung wird die Einhaltung der komponentenspezifisch definierten 
Grenzleckrate des Elektronikraums einer Komponente nachgewiesen. 
Bei der Messung am Prüfling ist die Luftleckrate durch übliche Messverfahren (z. B. 
Absolutdruck-, Differenzdruck-, Massefluss- oder Volumendurchflussmessung) zu 
bestimmen. 
Die Komponente wird dazu über einen Zugang (z. B. DAE) mit einem definierten 
Prüfdruck beaufschlagt; nach einer Beruhigungszeit wird die Luftleckrate messtechnisch 
erfasst. 
Da Design und Anwendung bestimmen, welchem Druck eines Mediums die 
Komponente real ausgesetzt sein wird, ist der Prüfdruck so auszuwählen, dass er dem 
schärfsten Anwendungsfall im Feld entspricht. Dies kann auch ein Unterdruck sein. Ist 
anzunehmen, dass sich das Dichtsystem bei Über- und Unterdruck unterschiedlich 
verhält (z. B. anpressen von Dichtlippen), so ist die Prüfung mit Über- und Unterdruck 
durchzuführen. Der anzuwendende Prüfdruck ist vom Auftragnehmer mit dem 
Auftraggeber abzustimmen und zu dokumentieren. 
 
Die Dichtheitsprüfung ist im Rahmen des P-03 Parametertest (groß) gemäß Kapitel 
4.7.3 bei TRT durchzuführen. Während der Messung darf der Prüfling keinen 
Temperaturschwankungen ausgesetzt sein. Die gemessene Luftleckrate darf die 
komponentenspezifisch definierte Grenzleckrate nicht überschreiten und muss im 
Prüfbericht dokumentiert werden. Veränderungen der Luftleckrate müssen bewertet und 
im Prüfbericht dokumentiert werden. 
 
 


### 第 23 页
 
  
Seite 23 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Teil I – Elektrische Anforderungen und Prüfungen 12 V 
Bordnetz 
5 Allgemeine Anforderungen 
5.1 Spannungen und Ströme 
Die angegebenen Spannungsverläufe sind als Hüllkurve zu verstehen. Reale 
Spannungskurven sind mit beliebigem Verlauf innerhalb der vorgegebenen Prüf- und 
Referenzkurven zu erwarten. 
 
Alle Spannungs- und Stromangaben beziehen sich auf die Komponente (an deren 
Klemme). Dies gilt nicht für Prüfungen, bei denen der Innenwiderstand Ri spezifiziert ist. 
In diesem Fall beziehen sich die Spannungs- und Stromangaben auf die Quelle (siehe 
Bild 1). 
 
 
 
Legende 
US 
Quelle 
RL 
Leitungs- und Kontaktierungswiderstand 
Ri 
Innenwiderstand betrachtet an den Klemmen der 
Komponente Richtung Quelle 
Bild 1: Innenwiderstand  
 
Sämtliche Flankenbeschreibungen beziehen sich auf die 10 % bzw. 90 %-Werte der 
Spannung. 
5.2 Prüfspannungen 
Prüfspannungen, insbesondere die für Über- und Unterspannungsprüfungen, können 
von den Betriebsspannungsbereichen in Kapitel 5.3 signifikant abweichen und werden 
gesondert genannt. 
In dem für die Komponente gültigen Spannungsbereich muss jederzeit der 
Funktionszustand A (siehe Kapitel 4.2) erfüllt sein. 
US
Ri
RL
DUT


### 第 24 页
Seite 24 
VW 80000: 2017-10 
 
 
 
 
 
 
5.3 Funktionsklassen und Betriebsspannungsbereiche  
In einem DUT befinden sich zumeist mehrere Funktionen, die jeweils unterschiedlichen 
Funktionsklassen zugeordnet sein können. Die folgende Tabelle listet die für die 
jeweiligen Funktionsklassen geforderten Funktionszustände (A oder C, s. Kapitel 4.2) 
auf. 
Tabelle 13: Funktionsklassen und Betriebsspannungsbereiche 
Spannungsbereich [V] 
Prüfung 
Dauer 
Funktionsklasse 
1 2 3 4 5  6 
17 – 26 
E-04 
≤ 60 s 
C C C C C  A 
16 – 17 
E-01 
≤ 1 h 
A A A C C  A 
 
 
 
 
 
 
 
 
 
 
18 – 27 
E-05 
≤ 300 ms A A A C C  A 
17 – 18 
E-02 
≤ 400 ms A A A A A  A 
16 – 17 
E-02 
≤ 600 ms A A A A A  A 
 
 
 
 
 
 
 
 
 
 
9,8 – 16 
 
statisch 
A A A A A  A 
9 – 9,8 
 
A A C A C  C 
6 – 9 
 
A C C C C  C 
 
 
 
 
 
 
 
 
 
 
≥ 9 
E-03a 
500 ms A A A A A  A 
≥ 7 
E-11 
s. E-11 A A A A A  A 
≥ 6 
E-03b 
s. E-03b A A A C C  A 
≥ 4,5 
E-11, 
normal 
s. E-11 A C C C C  C 
≥ 3,2 
E-11, 
scharf 
s. E-11 A C C C C  C 
≥ 0 
E-10 
≤ 100 µs A A A A A  A 
 
Mindestanforderung für die Anwendung der Funktionsklassen: 
Funktionsklasse 1:  Funktionen, die für die Sicherstellung und Aufrechterhaltung der 
Energieversorgung notwendig oder startrelevant sind 
Funktionsklasse 2:  Funktionen mit höherer Verfügbarkeit (s. Tabelle 2) 
Funktionsklasse 3:  für den Fahrbetrieb notwendige Funktionen 
Funktionsklasse 4:  Komfortfunktionen, die bei „Motor aus” oder „Bordnetzversorgung 
nur aus Speicher“ erhalten bleiben müssen 
Funktionsklasse 5:  Komfortfunktionen, die bei Motorbetrieb „Motor an“ bzw. 
„Bordnetzversorgung aktiv“ vorhanden sein müssen 
Funktionsklasse 6:  Diagnose und Kommunikation 
5.4 Schnittstellenbeschreibung 
Alle Schnittstellen müssen in ihren Zuständen und elektrischen Eigenschaften 
vollständig beschrieben werden. Diese Beschreibung dient als Grundlage für die 
Bewertung der Prüfergebnisse und muss entsprechend detailliert sein.  
5.5 Beschränkung der Durchführung 
Das Prüflabor muss nach DIN EN ISO/IEC 17025 organisiert sein und betrieben 
werden. Alle zur Messung verwendeten Prüfmittel müssen nach DIN EN ISO/IEC 17025 
kalibriert werden (bzw. wie durch den Hersteller festgelegt oder empfohlen) und auf 
PTB oder ein anderes gleichwertiges nationales Normlabor zurückführbar sein. Die 


### 第 25 页
 
  
Seite 25 
VW 80000: 2017-10 
 
 
 
 
 
 
 
verwendeten Prüfgeräte, Betriebsmittel, Aufstellungen und Prüfverfahren dürfen das 
Verhalten des Prüflings (beispielsweise Stromaufnahme) nicht begrenzen/verfälschen. 
Diese sind zusammen mit den Genauigkeiten und dem Ablaufdatum der Kalibrierung im 
Prüfbericht zu dokumentieren. 
5.6 Prüfablauf 
Eine elektrische Prüfung beginnt, wenn der Prüfling komplett aufgestartet ist und sich im 
Funktionszustand A befindet. 
 
Die elektrische Belastung muss, soweit nicht anders vorgegeben, mit Originallasten 
realisiert und betrieben werden.  
 
Die spezifikationsgemäße Funktion des Bauteils ist in allen relevanten Betriebsphasen 
des Gerätes zu prüfen. Folgende Betriebsphasen sind zu prüfen:  
 Startphase/PowerUp 
 Betrieb in verschiedenen Funktionszuständen 
 Ausschaltphase/PowerDown 
 Sleepmode 
Weitere Betriebsphasen sind mit dem Auftraggeber abzustimmen. 
 
Spätestens ab C-Muster ist der Prüfling während der Prüfung mit Applikationssoftware 
zu betreiben. 
 
Haben Softwareumfang, Applikationsparameter oder Prozessorauslastung Einfluss auf 
das Prüfungsergebnis, dann sind bei deren Änderungen die betroffenen Prüfungen zu 
wiederholen. 
 
Die Softwareversion ist mit Funktionsumfang im Prüfbericht anzugeben.  
 
Die Öffnung von Prüfteilen, außer für die physikalische Analyse, ist vom Auftraggeber 
zu genehmigen. 
 
Die Reihenfolge der elektrischen Prüfungen ist frei wählbar. Zu jeder Prüfung sind die 
erlaubten Fehlerspeichereinträge sowie die Funktionszustände der Komponente 
festzuschreiben. 
 
Die Testfälle in einer Prüfung sind, soweit nicht in der Prüfungsauswahltabelle gemäß 
Kapitel 6 festgelegt, alle durchzuführen. 
 
Die elektrischen Prüfungen dürfen während einer Umweltprüfung (siehe Teil II) 
durchgeführt werden, wenn dies nicht den Prüfanforderungen der elektrischen Prüfung 
widerspricht und der Auftraggeber der Vorgehensweise zugestimmt hat. Wird der 
Prüfling bei kombinierten Prüfungen auffällig, sind die Prüfungen einzeln zu 
wiederholen. 
 
Im Lastenheft oder in Abstimmung mit dem Auftraggeber ist ein Satz sensitiver 
Parameter, sogenannte Schlüsselparameter, wie z. B. Ruhestromaufnahme, 
Betriebsströme, Ausgangsspannungen, Übergangswiderstände, Eingangsimpedanzen, 


### 第 26 页
Seite 26 
VW 80000: 2017-10 
 
 
 
 
 
 
Signalraten (Anstiegs- und Abfallzeiten) und Busspezifikationen zu definieren. Diese 
Parameter müssen vor dem Start und nach dem Ablauf jeder Prüfung auf ihre 
Übereinstimmung mit der Spezifikation überprüft werden. 
 
Während jeder Prüfung müssen die zu überwachenden Schlüsselparameter 
aufgezeichnet werden. Resets der Komponente sind in geeigneter Form zu überwachen 
und zu dokumentieren. 
 
Vor einer elektrischen Prüfung mit definiertem Innenwiderstand ist der Prüfaufbau am 
Prüflingsstecker mit einer Referenzmessung zu verifizieren und zu dokumentieren. Die 
Referenzmessung ist, soweit nicht in der Prüfung anders gefordert, mit einer Ersatzlast 
von 150 % des Laststroms der Betriebsart Betriebmax durchzuführen. 
 
Vor und nach jeder Prüfung sind die Prüflinge einem P-02 Parametertest (klein) gemäß 
Kapitel 4.7.2 nach Lastenheftvorgabe zu unterziehen. Bei klimatischen Belastungen 
erfolgt der Test innerhalb einer Stunde nach Abschluss der Prüfung. 
 
Vor der ersten und nach der letzten elektrischen Prüfung ist der P-03 Parametertest 
(groß) gemäß Kapitel 4.7.3 nach Lastenheftvorgabe durchzuführen. 
 
Die Messergebnisse und Daten der Vorher- / Nachherprüfungen dürfen sich nur 
innerhalb der spezifizierten zulässigen Toleranzen voneinander unterscheiden. 
Veränderungen der Messwerte größer der Messgenauigkeiten sind zu kennzeichnen. 
Die Messergebnisse müssen auf Trends und Driften untersucht werden um 
Auffälligkeiten, Alterung oder Fehlfunktionen der Komponente zu erkennen. 
 
Die physikalische Analyse gemäß Kapitel 4.7.4 ist nach Abschluss aller elektrischen 
Prüfungen an mindestens einem Prüfling durchzuführen. 
 
 


### 第 27 页
 
  
Seite 27 
VW 80000: 2017-10 
 
 
 
 
 
 
 
6 Prüfungsauswahltabelle 
Tabelle 14: Prüfungsauswahltabelle 
Prüfung 
Anzuwenden auf 
Zusätzlich vom Auftraggeber 
festzulegen 
E-01 Langzeit 
Überspannung  
Komponenten, die über das 
12 V Bordnetz versorgt 
werden 
für den Fahrbetrieb 
notwendige Komponente 
E-02 Transiente 
Überspannung  
Komponenten, die über das 
12 V Bordnetz versorgt 
werden 
keine 
E-03a Transiente 
Unterspannung  
Komponenten, die über das 
12 V Bordnetz versorgt 
werden 
keine 
E-03b Transiente 
Unterspannung  
Komponenten, die über das 
12 V Bordnetz versorgt 
werden 
Schärfegrad 
E-04 Jumpstart  
Komponenten, die über das 
12 V Bordnetz versorgt 
werden 
startrelevante/ nicht 
startrelevante Komponente 
E-05 Load Dump  
Komponenten in Fahrzeugen 
mit 12 V-Generator 
sicherheitsrelevante 
Komponente 
E-06 Überlagerte 
Wechselspannung  
Testfall 1 für alle 
Komponenten, Testfälle 2 
und 3 nur bei Fahrzeugen mit 
12 V-Generator, Testfall 4 nur 
bei Fahrzeugen mit DC/DC-
Wandler 
Testfälle auf Basis Anbindung 
im Bordnetz 
E-07 Langsames 
Absenken und Anheben 
der 
Versorgungsspannung  
alle Komponenten 
relevante Klemmenstatus 
E-08 Langsames 
Absenken, schnelles 
Erhöhen der 
Versorgungsspannung 
alle Komponenten 
relevante Klemmenstatus 
E-09 Resetverhalten  
alle Komponenten 
relevante Klemmenstatus, 
Testrand-bedingungen  
E-10 Kurze 
Unterbrechungen  
alle Komponenten 
keine 
E-11 Startimpulse  
Komponenten, die über das 
12 V Bordnetz versorgt 
werden, ggf. nicht anwendbar 
bei Fahrzeugen ohne 12 V-
Starter 
startrelevante/ nicht 
startrelevante Komponente 
E-12 Spannungsverlauf 
bei Bordnetzregelung 
Komponenten, die über das 
12 V Bordnetz versorgt 
werden 
keine 


### 第 28 页
Seite 28 
VW 80000: 2017-10 
 
 
 
 
 
 
Prüfung 
Anzuwenden auf 
Zusätzlich vom Auftraggeber 
festzulegen 
E-13 Unterbrechung Pin 
alle Komponenten 
relevante Klemmenstatus 
E-14 Unterbrechung 
Stecker  
alle Komponenten 
keine 
E-15 Verpolung  
Komponenten, die im 
Fahrzeug einer Verpolung 
ausgesetzt werden können 
Schärfegrad, Abschaltung der 
Komponente bei Verpolung 
E-16 Masseversatz 
alle Komponenten 
keine 
E-17 Kurzschluss 
Signalleitung und 
Lastkreise  
alle Komponenten 
keine 
E-18 
Isolationswiderstand  
Komponenten mit galvanisch 
getrennten Anteilen 
keine 
E-19 Ruhestrom  
Komponenten die 
dauerspannungsversorgt sind 
(z. B. Kl. 30, Kl. 30f, 
Kl. 30g,…) 
keine 
E-20 
Durchschlagfestigkeit  
Komponenten, mit induktiven 
Bauteilen (z. B. Motoren, 
Relais, Spulen) 
keine 
E-21 Rückspeisungen  
Komponenten, die elektrisch 
mit Kl. 15 oder anderen 
Klemmen mit Weckfunktion 
verbunden sind  
Schärfegrad 
E-22 Überströme 
Komponenten, die einen 
Ausgang haben 
keine 
E-23 Ausgleichsströme 
mehrerer 
Versorgungsspannungen 
Komponenten, die mit 
unabhängigen Kl. 30 versorgt 
sind 
keine 
 
 


### 第 29 页
 
  
Seite 29 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7 Elektrische Anforderungen und Prüfungen 
7.1 E-01 Langzeit Überspannung 
7.1.1 Zweck 
Es wird das Verhalten und die Beständigkeit der Komponente bei längerer 
Überspannung geprüft. 
Die Überspannung kann durch einen Fehler der erzeugenden Energiequelle verursacht 
werden und stellt einen Einfachfehler in der Energieversorgung nach. 
7.1.2 Prüfung  
Tabelle 15: Prüfparameter E-01 Langzeit Überspannung 
Betriebsart des Prüflings 
Betriebsart Betriebmax 
Umax 
17 V (+4 %, 0 %) 
Umin 
13,5 V 
tr 
< 10 ms 
tf 
< 10 ms 
t1 
60 min 
Ttest 
Tmax – 20 K 
Anzahl der Zyklen 
1 
Anzahl der Prüflinge 
mindestens 6 
 
 
Bild 2: Prüfimpuls E-01 Langzeit Überspannung 
Wenn nach der Mindestprüfdauer am Prüfling noch kein thermischer 
Beharrungszustand (< 1 K in 10 min) erreicht wurde, ist die Prüfung bis zum 
thermischen Beharrungszustand zu verlängern. 
7.1.3 Anforderung 
Siehe Tabelle 13: Funktionsklassen und Betriebsspannungsbereiche 
t1
tf
tr
Umin
Umax
U
t


### 第 30 页
Seite 30 
VW 80000: 2017-10 
 
 
 
 
 
 
7.2 E-02 Transiente Überspannung 
7.2.1 Zweck 
Aufgrund des Abschaltens von Verbrauchern und bei kurzen Gasstößen (Tip-In) kann 
es zu transienten Überspannungen im Bordnetz kommen. Diese Überspannungen 
werden mit dieser Prüfung simuliert. 
7.2.2 Prüfung  
Tabelle 16: Prüfparameter E-02 Transiente Überspannung 
Betriebsart des Prüflings 
Betriebsart Fahrenmax 
Umin 
16 V 
U1 
17 V 
Umax 
18 V (+4 %, 0 %) 
tr 
1 ms 
tf 
1 ms 
t1 
400 ms 
t2 
600 ms 
Anzahl der Prüflinge 
mindestens 6 
Testfall 1 
Ttest 
Tmax 
Anzahl Zyklen 
3 
t3  
2 s 
Testfall 2 
Ttest 
Tmin 
Anzahl Zyklen 
3 
t3 
2 s 
Testfall 3 
Ttest 
TRT 
Anzahl Zyklen 
100 
t3 
8 s 


### 第 31 页
 
  
Seite 31 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
 
Bild 3: Prüfimpuls E-02 Transiente Überspannung 
7.2.3 Anforderung 
Siehe Tabelle 13: Funktionsklassen und Betriebsspannungsbereiche 
 
 
t2
tf
tf
Umin
Umax
U
t
t1
tr
t3
U1


### 第 32 页
Seite 32 
VW 80000: 2017-10 
 
 
 
 
 
 
7.3 E-03 Transiente Unterspannung 
7.3.1 Zweck 
Aufgrund des Einschaltens von Verbrauchern kann es abhängig vom Zustand des 
Energiebordnetzes (z. B. Verfügbarkeit von Energiespeichern) zu transienten 
Unterspannungen kommen.  
7.3.2 Prüfung E-03a 
Tabelle 17: Prüfparameter E-03a Transiente Unterspannung 
Betriebsart des Prüflings 
Betriebsart Betriebmax 
Umax 
10,8 V 
Umin 
9 V 
tf 
1,8 ms 
t1 
500 ms 
tr 
1,8 ms 
t2 
1 s 
Anzahl Zyklen 
10 
Testfall 1 
 
Ttest 
Tmax 
Testfall 2 
 
Ttest 
Tmin 
 
 
Bild 4: Prüfimpuls E-03a Transiente Unterspannung 
 
 
t1
tf
Umin
Umax
U
t
tr
t2


### 第 33 页
 
  
Seite 33 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.3.3 Prüfung E-03b 
Tabelle 18: Prüfparameter E-03b Transiente Unterspannung 
Parameter 
Schärfegrad 1 
Betriebsart des Prüflings 
Betriebsart Betriebmax  
U1 
10,8 V 
U2 
6 V 
U3 
8 V 
U4 
9 V 
t1 
5 ms 
t2  
20 ms 
t3 
2 ms 
t4 
180 ms 
t5 
1 ms 
t6 
300 ms 
t7 
2 ms 
t8 
1 s 
Anzahl Zyklen 
10 
Testfall 1 
Ttest 
Tmax 
Testfall 2 
Ttest 
Tmin 
 
Schärfegrad 1 ist anzuwenden für Funktionen, die der Aufrechterhaltung der 
Fahrbereitschaft und der Energieversorgung dienen sowie Funktionen, die 
Anforderungen an eine höhere Verfügbarkeit haben. 
 
Bild 5: Prüfimpuls E-03b Transiente Unterspannung 
7.3.4 Anforderung 
Siehe Tabelle 13: Funktionsklassen und Betriebsspannungsbereiche 


### 第 34 页
Seite 34 
VW 80000: 2017-10 
 
 
 
 
 
 
7.4 E-04 Jumpstart 
7.4.1 Zweck 
Diese Prüfung simuliert die Fremdversorgung eines Fahrzeuges. Die maximale 
Prüfspannung ergibt sich aus Nutzkraftfahrzeugsystemen und ihren erhöhten 
Bordnetzspannungen. 
7.4.2 Prüfung  
Tabelle 19: Prüfparameter E-04 Jumpstart 
Betriebsart des Prüflings 
Betriebsart Betriebmax 
U0 
0 V 
U1 
3 V (+ 0 %, -15 %) 
U2 
10,8 V 
U3 
26 V (+4 %, 0 %) 
t1 
1 s 
t2 
0,5 s 
t3 
5 s 
t4 
1 s 
t5 
60 s 
tr 
< 2 ms 
tf 
< 100 ms 
Anzahl der Zyklen 
1 
Anzahl der Prüflinge 
mindestens 6 
 
 
Bild 6: Prüfimpuls E-04 Jumpstart 
7.4.3 Anforderung  
Siehe Tabelle 13: Funktionsklassen und Betriebsspannungsbereiche 


### 第 35 页
 
  
Seite 35 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.5 E-05 Load Dump 
7.5.1 Zweck 
Der Abwurf einer elektrischen Last in Verbindung mit einer Batterie mit reduzierter 
Pufferfähigkeit führt aufgrund der Generatoreigenschaften zu einem energiereichen 
Überspannungsimpuls. Dieser Impuls soll mit dieser Prüfung simuliert werden. 
7.5.2 Prüfung 
Tabelle 20: Prüfparameter E-05 Load Dump 
Betriebsart des Prüflings 
Betriebsart Fahrenmax 
Umin 
13,5 V 
Umax 
27 V (+4 %, 0 %) 
tr 
≤ 2 ms 
t1 
300 ms 
tf  
≤ 30 ms 
Pause zw. Zyklen 
1 min. 
Anzahl der Zyklen 
10 
Anzahl der Prüflinge 
mindestens 6 
 
 
Bild 7: Prüfimpuls E-05 Load Dump 
7.5.3 Anforderung 
Siehe Tabelle 13: Funktionsklassen und Betriebsspannungsbereiche 
 
 
Umin
Umax
U
t
tr
t1
tf


### 第 36 页
Seite 36 
VW 80000: 2017-10 
 
 
 
 
 
 
7.6 E-06 Überlagerte Wechselspannung 
7.6.1 Zweck 
Dem Bordnetz können Wechselspannungen überlagert sein. Die überlagerte 
Wechselspannung kann während des gesamten Motorlaufes anliegen. Diese Situation 
wird mit diesen Prüfungen simuliert. 
7.6.2 Prüfung 
Tabelle 21: Prüfparameter E-06 Überlagerte Wechselspannung 
Betriebsart des Prüflings 
Betriebsart Fahrenmax 
Umax 
UBmax 
Ri 
≤100 m 
Wobbelart 
Dreieck logarithmisch 
Anzahl der Zyklen 
15 
Anzahl der Prüflinge 
mindestens 6 
Testfall 1  
UPP 
2 V (+4 %, 0 %) 
Frequenzbereich 
15 Hz – 30 kHz 
Wobbelperiode t1 
2 min 
Testfall 2  
UPP 
3 V (+4 %, 0 %)  
für Komponenten zwischen Batterie und Generator, 
insbesondere bei generatorferner Batterieanbindung 
Frequenzbereich 
15 Hz – 30 kHz 
Wobbelperiode t1 
2 min 
Testfall 3  
UPP 
6 V (+4 %, 0 %)  
für alle Komponenten bei Fahrten ohne Batterie (Notlauf) 
oder bei generatornaher Anbindung 
Frequenzbereich 
15 Hz – 30 kHz 
Wobbelperiode t1 
2 min 
Testfall 4 
Upp 
1 V (+4%, 0%) 
für Komponenten mit Versorgung aus DC/DC-Wandler 
Frequenzbereich 
30 kHz – 200 kHz 
Wobbelperiode t1 
10 min 
 


### 第 37 页
 
  
Seite 37 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Bild 8: Prüfimpuls E-06 Überlagerte Wechselspannung 
7.6.2.1 Prüfaufbau 
Die Bordnetzverhältnisse sind mit den Fachabteilungen abzustimmen. Der Prüfaufbau 
ist detailliert zu dokumentieren inkl. Leitungsinduktivitäten, Leitungskapazitäten und 
Leitungswiderständen. 
7.6.3 Anforderung 
Testfall 1: Funktionszustand A 
Testfall 2: Funktionszustand A 
Testfall 3: 
a) Für den Fahrbetrieb notwendige Komponenten: 
Funktionszustand A 
b) Für alle anderen Komponenten:  
Funktionszustand C 
Testfall 4: Funktionszustand A 
 
 
 
Umax
U
t
UPP
t1


### 第 38 页
Seite 38 
VW 80000: 2017-10 
 
 
 
 
 
 
7.7 E-07 
Langsames 
Absenken 
und 
Anheben 
der 
Versorgungsspannung 
7.7.1 Zweck 
Simuliert wird das langsame Absenken und Anheben der Versorgungsspannung, wie es 
beim langsamen Entlade- und Ladevorgängen der Fahrzeugbatterie auftritt. 
7.7.2 Prüfung E-07a 
Tabelle 22: Prüfparameter E-07a Langsames Absenken und Anheben der Versorgungsspannung 
Betriebsart des Prüflings  
Betriebsart Betriebmin und Betriebmax 
Ist bei allen relevanten Status der 
Spannungsversorgungsklemmen (z. B. 
Kl. 15, Kl. 30, Kl. 87, ...) und ihren 
Kombinationen durchzuführen 
Startspannung 
UBmax (+4 %, 0 %) 
Spannungsänderungsgeschwindigkeit 0,5 V/min (+10 %, -10 %) 
U1 
UBmin  
t1 
Haltezeit bei U1 so lange, bis 
Fehlerspeicher komplett ausgelesen 
wurde 
Minimalspannung 
0 V  
U2 
UBmin  
t2 
Haltezeit bei U2 so lange, bis 
Fehlerspeicher komplett ausgelesen 
wurde 
Endspannung 
UBmax (+4 %, 0 %) 
Anzahl der Zyklen 
Je relevanten Klemmenstatus und ihren 
Kombinationen: 
1 Zyklus mit Betriebsart Betriebmin 
1 Zyklus mit Betriebsart Betriebmax 
Anzahl der Prüflinge 
3 
 
Bild 9: Prüfimpuls E-07a Langsames Absenken und Anheben der Versorgungsspannung 
t1
t2
UBmin
UBmax
U
t
0 V
U1
U2


### 第 39 页
 
  
Seite 39 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.7.3 Prüfung E-07b 
Tabelle 23: Prüfparameter E-07b Langsames Absenken und Anheben der Versorgungsspannung 
Betriebsart des Prüflings  
Betriebsart Betriebmin und Betriebmax 
Ist bei allen relevanten Status der 
Spannungsversorgungsklemmen (z. B. Kl. 15, 
Kl. 30, Kl. 87, ...) und ihren Kombinationen 
durchzuführen. 
Umax 
14,5 V 
Umin 
1,5 V 
Upp 
3 V 
U1 
UBmin + 1,5 V 
U2 
UBmin + 1,5 V 
Frequenz 
Sinus 50 Hz 
Wobbelperiode 
52 min 
Anzahl der Zyklen 
Je relevanten Klemmenstatus und ihren 
Kombinationen: 
1 Zyklus mit Betriebsart Betriebmin 
1 Zyklus mit Betriebsart Betriebmax 
Anzahl der Prüflinge 
3 
 
 
Bild 10: Prüfimpuls E-07b Langsames Absenken und Anheben der Versorgungsspannung 
7.7.4 Anforderung 
Die Bewertung des Prüfungsergebnisses ist abhängig vom Spannungsbereich, mit 
dem die Komponente während der Prüfung beaufschlagt wird. 
 
Unterschieden wird zwischen: 
a) Innerhalb der definierten Betriebsspannung der Komponente: 
Funktionszustand A 
b) Außerhalb der definierten Betriebsspannung der Komponente: 
Funktionszustand C 
 
 


### 第 40 页
Seite 40 
VW 80000: 2017-10 
 
 
 
 
 
 
7.8 E-08 
Langsames 
Absenken, 
schnelles 
Erhöhen 
der 
Versorgungsspannung 
7.8.1 Zweck 
Diese Prüfung simuliert das langsame Absinken der Batteriespannung auf 0 V und das 
schlagartige Wiederanlegen der Batteriespannung z. B. durch Anlegen einer 
Fremdstartquelle. 
7.8.2 Prüfung 
Tabelle 24: Prüfparameter E-08 Langsames Absenken, schnelles Erhöhen der 
Versorgungsspannung 
Betriebsart des Prüflings 
Betriebsart Betriebmin und Betriebmax 
Ist bei allen relevanten Status der 
Spannungsversorgungsklemmen (z. B. Kl. 15, Kl. 30, 
Kl. 87, ...) und ihren Kombinationen durchzuführen. 
Startspannung 
UBmax (+4 %, 0 %) 
Spannungsabfall 
0,5 V/min (+10 %, -10 %)  
U1 
UBmin  
t1 
Haltezeit bei U1 so lange, bis Fehlerspeicher komplett 
ausgelesen wurde 
Haltezeit bei UBmin 
Solange bis Fehlerspeicher vollständig ausgelesen wurde 
Minimalspannung 
0 V  
t2 
Mindestens 1 min, jedoch solange bis interne 
Kapazitäten vollständig entladen sind. 
Endspannung 
UBmax (+4 %, 0 %) 
tr 
≤ 0,5 s  
Anzahl der Zyklen 
Je relevanten Klemmenstatus und ihren Kombinationen: 
1 Zyklus mit Betriebsart Betriebmin 
1 Zyklus mit Betriebsart Betriebmax 
Anzahl der Prüflinge 
mindestens 6 
 
 
Bild 11: Prüfimpuls E-08 Langsames Absenken, schnelles Erhöhen der Versorgungsspannung 
UBmin
UBmax
U
t
0 V
tr
t2
t1
U1


### 第 41 页
 
  
Seite 41 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.8.3 Anforderung 
Die Bewertung des Prüfungsergebnisses ist abhängig vom Spannungsbereich, mit dem 
die Komponente während der Prüfung beaufschlagt wird. 
 
Unterschieden wird zwischen den Bereichen: 
a) Innerhalb der definierten Betriebsspannung der Komponente: 
Funktionszustand A 
b) Außerhalb der definierten Betriebsspannung der Komponente: 
Funktionszustand C 
 
 


### 第 42 页
Seite 42 
VW 80000: 2017-10 
 
 
 
 
 
 
7.9 E-09 Resetverhalten 
7.9.1 Zweck 
Es wird das Resetverhalten einer Komponente in ihrer Umgebung nachgebildet und 
geprüft. Testrandbedingungen (z. B. Verbund, Klemme, System) sind detailliert zu 
beschreiben. 
Eine beliebige zeitliche Ablauffolge von wiederholtem Ein/Aus-Schalten kommt im 
Betrieb vor und darf nicht zu einem undefinierten Verhalten der Komponente führen. 
Das Resetverhalten spiegelt sich in einer Spannungsvarianz und in einer zeitlichen 
Varianz wider. Um unterschiedliche Ausschaltzeiten zu simulieren, werden zwei 
unterschiedliche Prüfabläufe gefordert. Eine Komponente hat immer beide Abläufe zu 
durchlaufen. 
7.9.2 Prüfung 
Tabelle 25: Prüfparameter E-09 Resetverhalten 
Betriebsart des Prüflings 
Betriebsart Betriebmin, Fahrenmin und Betriebmax 
Ist bei allen relevanten Status der 
Spannungsversorgungsklemmen (z. B. Kl. 15, Kl. 30, 
Kl. 87, ...) und ihren Kombinationen durchzuführen. 
Umax 
UBmin (0 %, -4 %) 
Uth 
6 V 
U1 (Bereich Umax bis Uth) 
0,5 V 
U2 (Bereich Uth bis 0 V) 
0,2 V 
t2 
Mindestens ≥10 s und bis der Prüfling wieder eine 
100 % Betriebsfähigkeit erreicht hat (alle Systeme sind 
wieder fehlerfrei hochgefahren). 
tr 
≤ 10 ms 
tf 
≤ 10 ms 
Anzahl der Zyklen 
Für jeden Prüfablauf je relevanten Klemmenstatus und 
ihren Kombinationen: 
1 Zyklus mit Betriebsart Betriebmin 
1 Zyklus mit Betriebsart Fahrenmin 
1 Zyklus mit Betriebsart Betriebmax 
Anzahl der Prüflinge 
mindestens 3 
Testfall 1 
t1 
5 s 
Testfall 2 
t1 
100 ms 


### 第 43 页
 
  
Seite 43 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Bild 12: Prüfimpuls E-09 Resetverhalten 
 
7.9.3 Anforderung 
Funktionszustand A beim Wiedererreichen von Umax. 
 
In keinem Fall darf es zu undefinierten Betriebszuständen kommen. 
 
Es ist der Nachweis der Einhaltung des spezifizierten Schwellwertes zu erbringen und 
festzuhalten, ab welchem Spannungspegel die Komponente den Funktionszustand A 
erstmalig verlässt. 
Umax
Uth
0V
U
t
tf
U2
U1
t1
t2
tr


### 第 44 页
Seite 44 
VW 80000: 2017-10 
 
 
 
 
 
 
7.10 E-10 Kurze Unterbrechungen 
7.10.1 
Zweck 
Es wird das Verhalten der Komponente bei kurzen Unterbrechungen von 
unterschiedlicher Dauer simuliert.  
Testfall 1 bildet die Versorgungsspannungsunterbrechung an der Komponente ab. 
Testfall 2 bildet die Versorgungsspannungsunterbrechung im Bordnetz ab. 
Solche Unterbrechungen können durch Ereignisse wie z. B. Kontakt- und Leitungsfehler 
oder prellende Relais auftreten. 
7.10.2 
Prüfung 
Tabelle 26: Prüfparameter E-10 Kurze Unterbrechungen 
Betriebsart des Prüflings 
Betriebsart Betriebmax 
Utest 
11 V 
Z1 
S1 geschlossen 
Z2 
S1 offen 
tr 
≤ (0,1 x t1) 
tf  
≤ (0,1 x t1) 
Der Schalter S1 ist mit folgenden 
Sequenzen zu schalten: 
t1 
Schritte 
Schärfegrad 1 
10 µs bis 100 µs 
10 µs 
100 µs bis 1 ms 
100 µs 
1 ms bis 10 ms 
1 ms 
10 ms bis 100 ms 
10 ms 
100 ms bis 2 s 
100 ms 
Schärfegrad 2 
10 µs bis 100 µs 
10 µs 
100 µs bis 1 ms 
100 µs 
1 ms bis 200 ms  
1 ms 
200 ms bis 2 s 
100 ms 
t2 
> 10 s 
Das Halten der Prüfspannung Utest muss 
mindestens so lange dauern, bis der Prüfling 
und die Peripherie wieder eine 100 %-
Betriebsfähigkeit erreicht haben. 
Anzahl der Zyklen 
1 
Anzahl der Prüflinge 
mindestens 6 
Testfall 1 
S1 geschaltet, S2 statisch offen 
Testfall 2 
S1 geschaltet, S2 negiert zu S1 
 
Die Zeitdauer des Spannungseinbruchs erhöht sich in den in Tabelle 26 genannten 
Schritten. Dabei ergibt sich ein wie in Bild 13 gezeigtes Schema. 
 
Die Spannung am Prüfling kann durch den Prüfaufbau auf die maximale Spannung der 
Prüfung E-05 Load Dump (siehe Kapitel 7.5) begrenzt werden. 
 


### 第 45 页
 
  
Seite 45 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Bild 13: Zustandswechsel Schalter S1 E-10 Kurze Unterbrechungen 
******** 
Prüfaufbau 
 
Bild 14: Prinzipschaltung E-10 Kurze Unterbrechungen 
Der geschlossene Schalter S2 inklusive der notwendigen Leitungen ist mit einem 
Längswiderstand von <100 mΩ zu realisieren. 
******** 
Prüfablauf 
Je eine Referenzmessung mit 100 Ω (± 5 %) und 1 Ω (± 5 %) als Prüflingsersatz ist 
durchzuführen und zu dokumentieren. Der Nachweis der Flankensteilheit ist mit diesem 
Prüfaufbau zu erbringen. Als Widerstände sind induktivitätsarme Bauteile zu 
verwenden. 
Anschließend sind die Prüfungen gemäß Tabelle 26 durchzuführen. 
7.10.3 
Anforderung  
Für t1 < 100 µs: Funktionszustand A 
Für t1 ≥ 100 µs: Funktionszustand C 
 
Es ist festzuhalten, ab welchem Zeitwert t1 der Prüfling den Funktionszustand A 
erstmalig verlässt. 
Z2
Z1
S1
t
tf
t1
t1
t2
tr
Utest
S1
S2
DUT


### 第 46 页
Seite 46 
VW 80000: 2017-10 
 
 
 
 
 
 
7.11 E-11 Startimpulse 
7.11.1 
Zweck 
Beim Starten (Anlassen des Motors) fällt die Batteriespannung für einen kurzen 
Zeitraum auf einen niedrigen Wert, um dann wieder leicht anzusteigen. Die meisten 
Komponenten werden unmittelbar vor dem Starten kurz aktiviert, während des 
Anlassens deaktiviert und anschließend nach dem Anlassen bei laufendem Motor 
wieder aktiviert. Mit dieser Prüfung wird das Verhalten der Komponente bei 
startbedingten Spannungseinbrüchen untersucht. 
Der Startvorgang kann unter unterschiedlichen Fahrzeugstartsituationen erfolgen: 
Kaltstart und Warmstart (automatischer Wiederstart bei Start-Stop). Um beide Fälle 
abzudecken, werden zwei unterschiedliche Testfälle gefordert. Eine Komponente hat 
immer beide Abläufe zu durchlaufen. 
7.11.2 
Prüfung 
Tabelle 27: Prüfparameter E-11 Startimpulse 
Betriebsart des Prüflings 
Betriebsart Betriebmin, Fahrenmin und Betriebmax 
Gegebenenfalls sind in der jeweiligen Betriebsart 
weitere Betriebslasten festzulegen. 
Prüfimpuls  
- Kaltstart: Prüfimpuls „normal“ und „scharf“ nach 
Tabelle 28 
- Warmstart: Prüfablauf „kurz“ und „lang“ nach 
Tabelle 29 
Anzahl der Prüflinge 
mindestens 6 
 
 
 


### 第 47 页
 
  
Seite 47 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.11.2.1 
Testfall 1 – Kaltstart 
Tabelle 28: Prüfparameter E-11 Kaltstart 
Parameter 
Prüfimpuls „normal“ 
Prüfimpuls „scharf“ 
UB 
11,0 V  
11,0 V 
UT 
4,5 V (0 %, -4 %) 
3,2 V +0,2 V 
US 
4,5 V (0 %, -4 %) 
5,0 V (0 %, -4 %) 
UA 
6,5 V (0 %, -4 %) 
6,0 V (0 %, -4 %) 
UR 
2 V 
2 V 
tf 
≤ 1 ms 
≤ 1 ms 
t4 
0 ms 
19 ms 
t5 
0 ms 
≤ 1 ms 
t6 
19 ms 
329 ms 
t7 
50 ms 
50 ms 
t8 
10 s 
10 s 
tr 
100 ms 
100 ms 
f 
2 Hz 
2 Hz 
Pause zwischen Zyklen 
2 s 
2 s 
Prüfzyklen 
10 
10 
 
Legende 
a 
Kl. 50 aus 
b 
Kl. 50 an 
c 
Kl. 50 aus 
ttest 
Zyklus 
Bild 15: Prüfimpuls E-11 Kaltstart 
 
 
 
 
 
 
 
1/f 
U 
UB 
US 
UT 
tf 
t5 
t4 
t6 
t8 
t 
UA 
t7 
tr 
b 
c 
ttest 
UR 
a 


### 第 48 页
Seite 48 
VW 80000: 2017-10 
 
 
 
 
 
 
7.11.2.2 
Testfall 2 –Warmstart 
Tabelle 29: Prüfparameter E-11 Warmstart 
Parameter 
Prüfablauf „kurz“ 
Prüfablauf „lang“ 
UB 
 
11,0 V 
UT 
 
7,0 V (0 %, -4 %) 
US 
 
8,0 V (0 %, -4 %) 
UA 
 
9,0 V (0 %, -4 %) 
t50 
 
≥ 10 ms 
tf 
 
≤ 1 ms 
t4 
 
15 ms 
t5 
 
70 ms 
t6 
 
240 ms 
t7 
 
70 ms 
t8 
 
600 ms 
tr 
 
≤ 1 ms 
Pause zwischen Zyklen 
5 s 
20 s 
Prüfzyklen 
10 
100 
 
 
Legende 
a 
Kl. 50 aus 
b 
Kl. 50 an 
c 
Kl. 50 aus 
ttest 
Zyklus 
Bild 16: Prüfimpuls E-11 Warmstart 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
U 
UB 
US 
UT 
tf 
t5 
t4 
t6 
t8 
t 
UA 
t7 
tr 
b 
 
c 
 
ttest 
t50 
a 
 


### 第 49 页
 
  
Seite 49 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.11.3 
Anforderung 
Es darf zu keinem Fehlerspeichereintrag kommen. 
Das Fahrzeug muss in jedem Fall gestartet werden können. 
7.11.3.1 
Startrelevante Komponenten: 
 
Testfall 1 – Kaltstart: 
Prüfimpuls „normal“: Funktionszustand A 
Prüfimpuls „scharf“: Funktionszustand A 
 
Testfall 2 – Warmstart: 
Prüfablauf „lang“: Funktionszustand A 
Prüfablauf „kurz“: Funktionszustand A 
 
7.11.3.2 
Nicht startrelevante Komponenten: 
Testfall 1 – Kaltstart: 
Prüfimpuls „normal“: Funktionszustand C 
Prüfimpuls „scharf“: Funktionszustand C 
 
Testfall 2 – Warmstart: 
Prüfablauf „lang“: Funktionszustand A 
Prüfablauf „kurz“: Funktionszustand A 
 


### 第 50 页
Seite 50 
VW 80000: 2017-10 
 
 
 
 
 
 
7.12 E-12 Spannungsverlauf bei Bordnetzregelung 
7.12.1 
Zweck 
Simuliert wird das Verhalten des Bordnetzes bei Spannungsregelungen z. B. bei 
Einsatz von intelligenten Generator- oder DC/DC-Wandler-Regelungen. Durch die 
Regelung können sich Spannungsverläufe im Bereich zwischen konstanter Spannung 
bis zu permanenten Spannungsschwankungen entsprechend der Testfälle nach 
einstellen. Relevant ist dies für alle Lastfälle, die die Komponente bei „Motor läuft“ bzw. 
„Fahrzeug betriebsbereit“ einnehmen kann. 
7.12.2 
Prüfung 
Tabelle 30: Prüfparameter E-12 Spannungsverlauf bei Bordnetzregelung 
Betriebsart des Prüflings 
Betriebsart Fahrenmax 
Umin 
(11,8 V - U) (0 %, -4 %) 
Umax 
(16 V - U) (+4 %, 0 %) 
t1 
2 s 
tr 
400 ms 
tf 
400 ms 
Anzahl der Zyklen 
10 
Anzahl der Prüflinge 
mindestens 6 
Testfall 1 
U 
0 V 
Testfall 2 
U 
0,7 V 
Testfall 3 
U 
2 V 
 
Bild 17:Prüfimpuls E-12 Spannungsverlauf bei Bordnetzregelung 
7.12.3 
Anforderung 
Funktionszustand A  
 
Umin
Umax
U
t
tf
tr
t1
t1


### 第 51 页
 
  
Seite 51 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.13 E-13 Unterbrechung Pin 
7.13.1 
Zweck 
Simuliert wird die Leitungsunterbrechung von einzelnen Pins. Die Prüfung ist in zwei 
unterschiedlichen Betriebszuständen durchzuführen. Da diese Unterbrechung in ihrer 
zeitlichen Ausprägung vielfältig ausfallen kann (von Wackelkontakt bis zur dauerhaften 
Unterbrechung) sind verschiedene Impulsformen einzusetzen. 
7.13.2 
Prüfung 
Tabelle 31: Prüfparameter E-13 Unterbrechung Pin 
Betriebsart des Prüflings 
Betriebsart Betriebmin und Betriebmax 
 
Ist bei allen relevanten Status der 
Spannungsversorgungsklemmen (z. B. Kl. 15, Kl. 30, 
Kl. 87, ...) und ihren Kombinationen durchzuführen. 
Z1 
Zustand 1: Pin verbunden 
Z2 
Zustand 2: Pin unterbrochen 
tr 
≤ (0,1 x t1) 
tf 
≤ (0,1 x t1) 
Anzahl der Zyklen 
Für die beiden Testfälle und die relevanten 
Klemmenstatus gilt: 
3 Zyklen mit Betriebsart Betriebmin 
3 Zyklen mit Betriebsart Betriebmax 
 
Jede Prüfung ist separat zu bewerten. 
Anzahl der Prüflinge 
mindestens 6 
Testfall 1  
 
Jeder Pin ist für t = 10s abzuziehen und wieder 
anzulegen (langsamer Intervall) 
Testfall 2 
 
Impulspaket auf jeden Pin zur Simulation eines 
„Wackelkontakts“ () 
Anzahl der Impulse t2 im 
Impulspaket 
4 000 
a 
Impulspaket 
t1 
0,1 ms 
t2 
1 ms 
t3 
10 s 


### 第 52 页
Seite 52 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Bild 18: Prüfimpuls E-13 Unterbrechung Pin, Testfall 2 
7.13.2.1 
Prüfablauf  
Die Komponente ist an die Spannungsversorgung angeschlossen. 
Die Prüfung ist nicht auf die Versorgungspins (z. B. Kl. 15, Kl. 30, Kl. 87, ...) 
anzuwenden es sei denn, einer dieser Pins wird als Weckleitung verwendet.  
Die Prüfung ist auch auf Massepins (Kl. 31) anzuwenden. 
 
Die Spannung am Pin kann auf die maximale Spannung der Prüfung E-05 Load Dump 
(siehe Kapitel 7.5) begrenzt werden. 
 
Je eine Referenzmessung mit 1 kΩ (±5 %) und 1 Ω (±5 %) als Prüflingsersatz ist 
durchzuführen und zu dokumentieren. Der Nachweis der Flankensteilheit ist mit diesem 
Prüfaufbau zu erbringen. Als Widerstände sind induktivitätsarme Bauteile zu 
verwenden. 
 
Anschließend sind die Prüfungen gemäß Tabelle 31 durchzuführen. 
7.13.3 
Anforderung 
Für alle Testfälle Funktionszustand C. 
 
 
tr
t3
t2
t1
tf
Z2
Z1
Pin
t
a


### 第 53 页
 
  
Seite 53 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.14 E-14 Unterbrechung Stecker 
7.14.1 
Zweck 
Simuliert wird die Leitungsunterbrechung von Steckern. 
7.14.2 
Prüfung 
Tabelle 32: Prüfparameter E-14 Unterbrechung Stecker 
Betriebsart des Prüflings 
Betriebsart Betriebmin und Betriebmax 
Anzahl der Zyklen 
Jeder Stecker muss in beiden Betriebsarten einmal 
gezogen werden. 
Anzahl der Prüflinge 
mindestens 6 
7.14.2.1 
Prüfablauf  
Jeder Stecker ist für 10 s vom Prüfling abzuziehen und wieder aufzustecken. Hat der 
Prüfling mehrere Stecker, ist jeder Stecker einzeln zu prüfen. Die Reihenfolge ist zu 
variieren. Bei mehreren Steckern ist auch deren Kombinatorik abzuprüfen. 
7.14.3 
Anforderung 
Funktionszustand C 
 
 


### 第 54 页
Seite 54 
VW 80000: 2017-10 
 
 
 
 
 
 
7.15 E-15 Verpolung 
7.15.1 
Zweck 
Es wird die Beständigkeit des Prüflings gegen den verpolten Anschluss einer Batterie 
bei Fremdstarthilfe geprüft. Die Verpolung kann mehrfach auftreten und darf nicht zu 
einer Schädigung der Komponente führen. Die Verpolsicherheit muss für beliebige 
Spannungen bis zur minimalen Prüfspannung gewährleistet sein. Die 
Fahrzeugsicherung ist nicht Teil des Verpolschutzkonzeptes. 
7.15.2 
Prüfung 
Es müssen alle relevanten Anschlüsse bei originaler Beschaltung geprüft werden. 
Der Prüfling ist entsprechend der Verschaltung im Fahrzeug anzusprechen. 
Die Prüfung ist bei verschiedenen Spannungen zwischen 0 V und den in Tabelle 33 
vorgegebenen maximalen Werten durchzuführen. 
 
Die Stromaufnahme während der Prüfung ist zu protokollieren. 
Tabelle 33: Prüfparameter E-15 Verpolung 
Betriebsart des Prüflings 
Betriebsart Betriebmin (Verpolung statisch) 
Betriebsart Betriebmax (Verpolung dynamisch) 
Testfall 1 
Verpolung statisch nach Tabelle 34 
Testfall 2 
Verpolung dynamisch nach Tabelle 35 
Anzahl der Prüflinge 
mindestens 6 
7.15.2.1 
Testfall 1 - Verpolung statisch 
Dieser Testfall überprüft die Robustheit der Komponente bei verschiedenen 
Verpolspannungen, die sich abhängig vom Fahrzeugzustand einstellen können. 
 
Tabelle 34: Prüfparameter E-15 Verpolung statisch 
Umax 
0 V  
Umin 
-14,0 V  
∆U1 
- 1 V 
Schärfegrad 1 
Ri < 100 mΩ 
Schärfegrad 2 
Ri < 30 mΩ  
t1 
60 s 
 
Für eine Komponente, bei der die Betriebsspannung durch ein 
Relais bei Verpolung abgeschaltet wird, gilt abweichend: 
8 ms 
t2 
≥ 60 s, aber mindestens bis die Komponente den thermischen 
Zustand wie zu Beginn der Prüfung erreicht hat 
tr 
≤ 10 ms 
tf 
≤ 10 ms 
Anzahl der Zyklen 
1 


### 第 55 页
 
  
Seite 55 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Bild 19: Prüfimpuls E-15 Verpolung statisch 
7.15.2.2 
Testfall 2 - Verpolung dynamisch 
Dieser Testfall überprüft die Verpolung der Komponente im laufenden Betrieb bei nicht 
mehr startfähigem Fahrzeug. 
 
Tabelle 35: Prüfparameter E-15 Verpolung dynamisch 
Umax 
10,8 V  
Umin 
- 4,0 V 
Schärfegrad 1 
Ri < 100 mΩ 
Schärfegrad 2 
Ri < 30 mΩ  
t1 
60 s 
 
Für eine Komponente, bei der die Betriebsspannung durch ein 
Relais bei Verpolung abgeschaltet wird, gilt abweichend: 
8 ms 
t2 
≤ 5 min 
tr 
≤ 10 ms 
tf 
≤ 10 ms 
Anzahl der Zyklen 
3 
Umin
Umax
U
t
U1
t1
t2
tf
tr


### 第 56 页
Seite 56 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Bild 20: Testfall E-15 Verpolung dynamisch 
 
 
7.15.3 
Anforderung 
Während der Verpolung dürfen keine sicherheitsrelevanten Funktionen z. B. bei 
elektrischen Fensterhebern, elektrischem Schiebedach, Anlasser, usw. ausgelöst 
werden. 
 
Funktionszustand C 
 
Umin
Umax
U
t
0 V
t1
t2
tf
tr


### 第 57 页
 
  
Seite 57 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.16 E-16 Masseversatz 
7.16.1 
Zweck 
Potentialdifferenzen zwischen verschiedenen Masseanbindungsorten können 
Signalverfälschungen zwischen Komponenten an diesen Anbindungsorten verursachen. 
Es ist sicherzustellen, dass Potentialdifferenzen zwischen Massepunkten bis zu einer 
Höhe von statisch ± 1 V im elektrischen Verbund keine Beeinflussung von 
Komponentenfunktionen hervorrufen.  
7.16.2 
Prüfung 
Besitzt der Prüfling mehrere Spannungs- und Masseanschlüsse, ist die Prüfung für 
jeden Anschlusspunkt separat durchzuführen. 
 
Die Komponente wird wie unter Bild 21 verschaltet. 
Tabelle 36: Prüfparameter E-16 Masseversatz 
Betriebsart des Prüflings 
Betriebsart Betriebmax  
Dauer der Prüfung 
≥ 60 s 
U 
1 V  
Anzahl der Zyklen 
beide Schaltpositionen 
Anzahl der Prüflinge 
mindestens 6 
 
 
Legende 
B 
Bussystem 
S 
Signalleitung 
S1 
Zweipoliger (a/b) Umschalter 
TE 
Weitere Komponente z. B. Prüfreferenz, Teststand, Simulationssteuergerät, Aktor, 
Sensor oder Last 
Bild 21: Prinzipschaltung E-16 Masseversatz 
7.16.3 
Anforderung 
Funktionszustand A  
 
TE
DUT
B
S
UB
Kl.31
S1b
S1a
1
2
1
2
U


### 第 58 页
Seite 58 
VW 80000: 2017-10 
 
 
 
 
 
 
7.17 E-17 Kurzschluss Signalleitung und Lastkreise 
7.17.1 
Zweck 
Simuliert werden Kurzschlüsse an allen Geräteeingängen und -ausgängen sowie im 
Lastkreis. 
Alle Ein- und Ausgänge sind kurzschlussfest gegen Kl. 30 und Kl. 31 (bei aktivierten 
und nicht aktivierten Ausgängen mit und ohne Spannungsversorgung und mit und ohne 
Masseverbindung) auszulegen. 
Die Komponente ist gegen einen dauerhaft anliegenden Kurzschluss auszulegen. 
7.17.2 
Prüfung 
Tabelle 37: Prüfparameter E-17 Kurzschluss Signalleitung und Lastkreise 
Betriebsart des Prüflings 
Betriebsart Betriebmax  
Dauer der Prüfung 
Jede Kombination aus Prüfspannung und Testfall für 
jeweils 60 s 
Prüfspannungen 
UBmin und UBmax 
Testfall 1 
Jeder Pin abwechselnd an Kl. 30 und Kl. 31 mit 
Spannungsversorgung und mit Masseverbindung 
Testfall 2 
Jeder Pin abwechselnd an Kl. 30 und Kl. 31 ohne 
Spannungsversorgung und mit Masseverbindung 
Testfall 3 
Jeder Pin abwechselnd an Kl. 30 und Kl. 31 mit 
Spannungsversorgung und ohne Masseverbindung 
Anzahl der Prüflinge 
mindestens 6 
 
Wird die Spannungsversorgung/Masseversorgung über mehrere Pins zugeführt, muss 
auch die Kombinatorik berücksichtigt werden. 
******** 
Prüfaufbau  
Das verwendete Netzteil zur Prüfung muss die von der Komponente zu erwartenden 
Kurzschlussströme liefern können. Ist dies nicht möglich, ist eine Pufferung des 
Netzteils mit einer Autobatterie zulässig (UBmax ist in diesem Fall die maximale  
Ladespannung). 
 
 


### 第 59 页
 
  
Seite 59 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Bild 22: Prinzipschaltung  
 
Legende 
L  
 
Last 
E  
 
Eingang 
A  
 
Ausgang 
PWR   
Ausgang UB DUT 
GND   
Eingang / Ausgang Kl. 31 DUT 
 
******** 
Prüfablauf 
Bei Ein-, Ausgängen: Aufzeichnung und Bewertung des Zeitverlaufs des 
Kurzschlussstromes. 
Die Funktionsauswirkungen der Kurzschlüsse sind zu dokumentieren. 
 
7.17.3 
Anforderung 
Bei Ein- und Ausgängen (E und A): Funktionszustand C 
Bei durchgeschleiften Versorgungspannungen (PWR): Funktionszustand D2 
Bei Geräte-Masse (GND): Funktionszustand E. Dies gilt auch für den fahrzeugseitigen 
Kontakt- und Leitungssatz und ist durch die Komponente sicherzustellen. 
 
 


### 第 60 页
Seite 60 
VW 80000: 2017-10 
 
 
 
 
 
 
7.18 E-18 Isolationswiderstand 
7.18.1 
Zweck 
Ermittelt wird der Isolationswiderstand zwischen Bauteilen mit galvanischer Trennung. 
Es sind nur die galvanisch voneinander getrennten Pins zu betrachten, die im Fahrzeug 
angeschlossen werden und für Ihre Funktion Isolationseigenschaften benötigen.  
7.18.2 
Prüfung 
Tabelle 38: Prüfparameter E-18 Isolationswiderstand 
Betriebsart des Prüflings 
Betriebsart Fahrzeugaufbauunverbaut 
Prüfspannung 
500 V DC 
Prüfdauer 
60 s 
Prüfpunkte 
Anlegen der Prüfspannung an  
- 
Anschlüssen ohne galvanische Verbindung 
- 
zwischen Anschlusspins und elektrisch 
leitenden Gehäuse ohne galvanische 
Verbindung 
- 
zwischen Anschlusspins und einer Elektrode, 
die das Gehäuse umgibt, falls das Gehäuse 
nicht leitend ist 
– weitere, mit der jeweiligen Fachabteilung 
abgestimmte, Prüfpunkte 
Anzahl der Zyklen 
1 Zyklus muss durchlaufen werden, wobei jeder der 
oben genannten Punkte mindestens einmal abgeprüft 
werden muss. 
Anzahl der Prüflinge 
mindestens 6 
 
7.18.2.1 
Prüfablauf 
Diese Prüfung ist nach den Prüfungen „Feuchte Wärme, konstant“ sowie „Feuchte 
Wärme, zyklisch“ durchzuführen.  
Nach der Prüfung „Feuchte Wärme, konstant“ sind die Prüflinge 30 min abzulüften, 
bevor die Messung des Isolationswiderstands durchgeführt wird. 
Nach der Prüfung „Feuchte Wärme, zyklisch“ ist der Isolationswiderstand unmittelbar zu 
messen. 
7.18.3 
Anforderung 
Der Isolationswiderstand muss mindestens 10 M betragen.  
Nach der Prüfung ist Funktionszustand A nachzuweisen. 
 
 


### 第 61 页
 
  
Seite 61 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.19 E-19 Ruhestrom 
7.19.1 
Zweck 
Die Ruhestromaufnahme der Komponente soll ermittelt werden. 
7.19.2 
Prüfung 
Bei Komponenten, die eine Nachlauffunktion haben (z. B. Lüfter), ist die 
Ruhestromaufnahme erst nach Beendigung dieser Funktion zu ermitteln.  
Die Komponente ist mit der zugehörigen Peripherie und Beschaltung zu messen. 
 
Tabelle 39: Prüfparameter E-19 Ruhestrom 
Betriebsart des Prüflings 
Betriebsart Betriebmin  
Prüfspannung 
12,5 V (+4 %, 0 %) 
Anzahl der Prüflinge 
mindestens 6 
Testfall 1 
T 
Tmin 
Testfall 2 
T 
TRT 
Testfall 3 
T 
Tmax 
7.19.3 
Anforderung 
Grundsätzlich gilt für alle Prüflinge das Ziel einer Ruhestromaufnahme von 0 mA. 
 
Für Prüflinge, die nach Kl. 15 AUS betrieben werden müssen, gilt in der Ruhephase ein 
Ruhestromäquivalent (im Mittel über 12 h) ≤ 0,1 mA, entsprechend 1,2 mAh (oberhalb 
+40 °C ≤ 0,2 mA). Dieser ist in allen denkbaren Fahrzeugruhezuständen und jedem 
beliebigen 12 h-Zeitraum immer einzuhalten. Anderenfalls ist eine Freigabe der für 
Ruhestrommanagement zuständigen Fachabteilung notwendig.  
 
Nachlauffunktionen sind ebenfalls durch die für Ruhestrommanagement zuständige 
Fachabteilung freizugeben. 


### 第 62 页
Seite 62 
VW 80000: 2017-10 
 
 
 
 
 
 
7.20 E-20 Durchschlagfestigkeit 
7.20.1 
Zweck 
Simuliert wird die Durchschlagfestigkeit zwischen galvanisch voneinander getrennten 
Bauteilen des Prüflings, z. B. Steckerpins, Relais, Wicklungen oder Leitungen. Die 
Prüfung ist anzuwenden auf Komponenten, die induktive Bauelemente enthalten oder 
ansteuern. 
7.20.2 
Prüfung 
Tabelle 40: Prüfparameter E-20 Durchschlagfestigkeit 
Betriebsart des Prüflings 
Betriebsart Betriebmin 
Prüfspannung Ueff 
500 V AC, 50 Hz, sinusförmig 
Prüfdauer 
60 s 
Prüfpunkte 
Anlegen der Prüfspannung an 
- 
Anschlüssen ohne galvanische Verbindung. 
- 
zwischen Anschlusspins und elektrisch 
leitenden Gehäuse ohne galvanische 
Verbindung. 
- 
zwischen Anschlusspins und einer 
Elektrode, die das Gehäuse umgibt, falls 
das Gehäuse nicht leitend ist. 
- 
weitere, mit der jeweiligen Fachabteilung 
abgestimmte, Prüfpunkte. 
Anzahl der Zyklen 
1 Zyklus muss durchlaufen werden, wobei jeder 
der oben genannten Punkte mindestens einmal 
abgeprüft werden muss. 
Anzahl der Prüflinge 
mindestens 6 
 
7.20.2.1 
Prüfablauf 
Diese Prüfung ist nach den Prüfungen „Feuchte Wärme, konstant“ sowie „Feuchte 
Wärme, zyklisch“ durchzuführen.  
Nach der Prüfung „Feuchte Wärme, konstant“ sind die Prüflinge 30 min abzulüften, 
bevor die Messung des Isolationswiderstands durchgeführt wird. 
Nach der Prüfung „Feuchte Wärme, zyklisch“ ist der Isolationswiderstand unmittelbar zu 
messen. 
7.20.3 
Anforderung 
Funktionszustand C 
Spannungsdurchschläge und Lichtbögen sind nicht zulässig. 


### 第 63 页
 
  
Seite 63 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.21 E-21 Rückspeisungen 
7.21.1 
Zweck 
Die Unabhängigkeit geschalteter Klemmen ist sicherzustellen. 
Mit dieser Prüfung wird die Rückwirkungsfreiheit des DUTs auf geschaltete Klemmen 
(Kl. 15, Kl. 87, Kl. 30c, …) nachgewiesen.  
7.21.2 
Prüfung 
Tabelle 41: Prüfparameter E-21 Rückspeisungen 
Betriebsart des Prüflings 
Betriebsart Betriebmax  
Utest 
UBmax – 0,2 V 
Prüftemperaturen 
Tmax, TRT und Tmin 
Testfall 1 
Schärfegrad 1 
Schärfegrad 2 
R 
Nicht vorhanden 
≥ 10 kΩ 
S1 
offen 
offen 
S2 
geschlossen 
geschlossen 
Testfall 2 
R 
≥ 10 kΩ 
S1 
offen 
S2 
offen 
Anzahl der Prüflinge 
mindestens 6 
7.21.2.1 
Prüfungsablauf 
Der Prüfling ist entsprechend der Verschaltung im Fahrzeug anzuschließen (inkl. 
Sensoren, Aktoren, usw.) und im Normalbetrieb zu betreiben. Die Schalter S1 und S2 
sind dabei geschlossen. Zu messen ist der Spannungsverlauf an der zu prüfenden 
Klemme bei deren Abschaltung. Hierzu sind die Schalter gemäß Tabelle 41 zu öffnen. 
Die Abschaltung muss z. B. mit einem Relais oder einem Schalter (RSchalter_offen → ∞) 
erfolgen. Weitere evtl. vorhandene Spannungsquellen, wie z. B. die Kl. 30, dürfen 
während der Prüfung nicht abgetrennt oder abgeschaltet werden (entsprechend dem 
Verhalten im Fahrzeug).  
Der Spannungsverlauf an der zu prüfenden Klemme ist mit einem Messgerät V mit 
einem Eingangswiderstand ≥ 10 MΩ (z. B. Oszilloskop) zu messen. 
 
 


### 第 64 页
Seite 64 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Bild 23: Prinzipschaltung Prüfung   
Legende 
S1  
 
Schalter 1 
S2  
 
Schalter 2  
R  
 
Widerstand 
K  
 
Zu prüfende Klemme 
V 
 
Messgerät 
 
7.21.3 
Anforderung 
Spannungsrückspeisungen auf die zu prüfende Klemme sind nur bis zu einem Pegel 
von maximal 1 V zulässig. Dieser Spannungsbereich muss innerhalb von t = 20 ms ab 
dem Zeitpunkt der Abschaltung erreicht werden. 
 
Die Spannung an der unbeschalteten zu prüfenden Klemme muss innerhalb von t = 
20 ms, ab dem Zeitpunkt der Abschaltung, unterhalb einer Spannung von 1 V abfallen. 
 
Die Spannungszeitkurve muss stetig fallend verlaufen. Eine Unstetigkeit der Kurve 
durch positive Impulse ist nicht erlaubt. 


### 第 65 页
 
  
Seite 65 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.22 E-22 Überströme 
7.22.1 
Zweck 
Geprüft wird die Überstromfestigkeit von mechanischen Schaltern, elektronischen 
Ausgängen und Kontakten. Zu beachten sind auch höhere Ströme als im normalen 
Lastfall (z. B. maximaler Blockierstrom IBlock eines Motors). 
7.22.2 
Prüfung 
Tabelle 42: Prüfparameter E-22 Überströme 
Betriebsart des Prüflings 
Betriebsart Betriebmax 
Temperatur 
Tmax 
Prüfbedingung für elektronische 
Ausgänge 
Der Ausgang muss mind. das Dreifache der 
Nominallast unbeschadet aushalten können. 
tTest 
30 min 
Prüfbedingungen für geschaltete 
Ausgänge 
Für Komponenten mit einem IN ≤ 10 A:  
 
Itest = 3 x IN 
Für Komponenten mit einem IN > 10 A:  
 
Itest = 2 x IN, jedoch mind. 30 A und max. 
 
150 A 
Für Komponenten mit einem IBlock > 3 x IN:  
 
Itest = IBlock  
Unter Last ist einmal „AUS”, „EIN” und wieder 
„AUS“ zu schalten. 
 
Belastungsdauer 10 min 
 
Bei Mehrkontaktrelais, -schaltern ist jeder Kontakt 
einzeln zu prüfen. 
Anzahl der Prüflinge 
mindestens 6 
7.22.3 
Anforderung 
Funktionszustand A für mechanische Komponenten ohne Sicherung. Wenn 
Sicherungselemente im Lastkreis vorhanden sind, dürfen diese auslösen. 
 
Funktionszustand C für elektronische Ausgänge mit Überlasterkennung (Strom, 
Spannung, Temperatur). 
 
Zusätzlich dürfen bei einer Sichtprüfung alle Komponenten keine schädlichen 
Veränderungen, die die Funktion oder Lebensdauer einschränken, sichtbar sein 
(optische und elektrische Eigenschaften). 
 
 


### 第 66 页
Seite 66 
VW 80000: 2017-10 
 
 
 
 
 
 
7.23 E-23 Ausgleichsströme mehrerer Versorgungsspannungen 
7.23.1 
Zweck 
Bei Komponenten mit mehreren voneinander unabhängigen 
Versorgungsspannungseingängen, z. B. bei Versorgung durch voneinander 
unabhängigen 12 V Teilbordnetzen, wird mit dieser Prüfung die interne Unabhängigkeit 
dieser Versorgungszweige ermittelt.  
7.23.2 
Prüfung 
Tabelle 43: Prüfparameter E-23 Ausgleichsströme 
Betriebsart des Prüflings 
Betriebsart Fahrzeugaufbaumontage 
ttest 
60 s 
Prüfpunkte 
Anlegen der Prüfspannung zwischen  
─ 
beiden Versorgungsanschlüssen 
─ 
weiteren, mit der jeweiligen Fachabteilung des 
Auftraggebers abgestimmten, Prüfpunkten 
siehe Bild 24 
Anzahl der Zyklen 
1 
Anzahl der Prüflinge 
6 
Testfall 1 
Utest 
32 V 
Testfall 2 
Utest 
- 32 V 
 
 
Bild 24: Prinzipschaltbild E-23 Ausgleichsströme 
7.23.3 
Anforderung 
Der im Prüfaufbau gemessene Ausgleichsstrom darf 100 µA nicht überschreiten. Ein 
Einfachfehler darf die Unabhängigkeit der Versorgungszweige nicht aufheben oder 
gefährden. Nach dem Test ist der Funktionszustand A nachzuweisen. 


### 第 67 页
 
  
Seite 67 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Teil II – Umweltanforderungen und –prüfungen 
8 Einsatzprofil 
8.1 Lebensdauerauslegung 
In Tabelle 44 sind die typischen Parameter zur Lebensdauerauslegung 
zusammengefasst: 
Tabelle 44: Lebensdaueranforderungen 
Lebensdauer 
15 Jahre 
Betriebsdauer 
Fahren 
8 000 h 
Laufleistung 
300 000 km 
 
Bei Fahrzeugen mit elektrifizierten Antrieben sind gegebenenfalls zusätzliche 
Betriebsmodi zu berücksichtigen (siehe Kapitel 4.3).  
 
Die Betriebsdauer in den zusätzlichen Betriebsmodi (siehe Kapitel 4.3)  
 Betriebsdauer Laden,  
 Betriebsdauer Vorkonditionierung,  
 Betriebsdauer On-Grid Parken 
ist komponentenspezifisch im Lastenheft festzulegen. 
8.2 Temperaturkollektive 
Um die Temperaturbelastung, der eine Komponente am Einbauort im Fahrzeug 
ausgesetzt ist, vollständig zu beschreiben, ist neben der Angabe der minimalen 
Umgebungstemperatur Tmin und des maximalen Umgebungstemperaturbereiches 
Tmax zusätzlich die Verteilung notwendig, die angibt, wie lange die Komponente den 
verschiedenen Temperaturen zwischen Tmin und Tmax ausgesetzt ist. 
 
Bei Fahrzeugen mit alternativen Antrieben muss zwischen den Betriebsmodi 
„Fahren“, „Laden“, „Vorkonditionierung“ und „On-Grid Parken“ unterschieden und die 
jeweiligen Temperaturkollektive müssen sowohl für Umgebungs- als auch für die 
Kühlmittelkreislauftemperatur angegeben werden. 
 
Grundsätzlich ist diese Temperaturverteilung eine kontinuierliche Verteilung, da die 
Umgebungstemperatur der Komponente jeden Wert zwischen Tmin und Tmax 
annehmen kann. 
Zur Auslegung der Komponente und zur vereinfachten Berechnung von Prüfdauern 
mit Hilfe des beschleunigten Lebensdauermodells nach Arrhenius (siehe Anhang D), 
kann diese kontinuierliche Verteilung durch einige diskrete Temperaturstützstellen 
TFeld, i hinreichend gut beschrieben werden. Für jede Temperaturstützstelle ist jeweils 
der prozentuale Anteil pi der Betriebsdauer anzugeben, für den die Komponente der 
Stützstellentemperatur ausgesetzt ist. 
Das entsprechende Temperaturkollektiv hat somit die folgende allgemeine Form 
 


### 第 68 页
Seite 68 
VW 80000: 2017-10 
 
 
 
 
 
 
Tabelle 45: Temperaturkollektiv 
Temperatur in °C 
Verteilung 
TFeld.1 = Tmin 
p1 
TFeld.2 
p2 
… 
… 
TFeld.n = Tmax 
pn 
 
und basiert im Wesentlichen auf Feldmessungen und technischen Erfahrungen. 
 
Typische Temperaturkollektive für den Betriebsmodus Fahren in Bezug auf 
unterschiedliche Einbauräume sind im Anhang B angegeben. 
Die Anwendbarkeit dieser typischen Temperaturkollektive für eine spezifische 
Komponente ist z. B. durch Fahrzeugmessung, Simulation oder Erfahrungen zu 
verifizieren. Bei Abweichungen ist das Temperaturkollektiv komponentenspezifisch 
anzupassen. 
Für spezielle Einbauorte oder Einbausituationen (z. B. einem Einbauort nahe einer 
Wärmequelle) ist grundsätzlich ein komponentenspezifisches Temperaturkollektiv zu 
definieren. 
Das gültige Temperaturkollektiv ist im Lastenheft zu dokumentieren. 
Ergänzend zu den typischen Temperaturkollektiven sind im Anhang B typische Werte 
für den durchschnittlichen Temperaturhub angegeben, den eine Komponente im 
Fahrzeug im Betriebsmodus Fahren erfährt. 
Für komponentenspezifisch definierte oder angepasste Temperaturkollektive ist 
dieser Wert ebenfalls komponentenspezifisch festzulegen und im Lastenheft zu 
dokumentieren.  
 
 
 


### 第 69 页
 
  
Seite 69 
VW 80000: 2017-10 
 
 
 
 
 
 
 
9 Prüfungsauswahl 
9.1 Prüfauswahltabelle 
Tabelle 46: Prüfauswahltabelle 
Prüfung 
Anzuwenden auf 
Erforderliche Angabe 
M-01 Freier Fall 
alle Komponenten. 
Bei Komponenten, die offensichtlich 
bei der Prüfung beschädigt werden 
(z. B. bei Glaskörpern, hochsensiblen 
Messgebern), kann die Prüfung in 
Abstimmung mit dem Auftraggeber 
entfallen. Dies ist zu dokumentieren. 
 Keine 
M-02 Steinschlagprüfung 
Komponenten, die in Bereichen 
verbaut sind, die von Steinschlag 
betroffen sein können. 
 Keine 
M-03 Staubprüfung 
Alle Komponenten 
 IP-Schutzgrad 
 
Schutzgrad IP6KX 
Komponenten, in die kein Staub 
eindringen darf 
 
Schutzgrad IP5KX 
Komponenten, in die nur so viel Staub 
eindringen darf, dass die Funktion und 
die Sicherheit nicht beeinträchtig 
werden 
M-04 Vibrationsprüfung 
alle Komponenten 
Vibrationsprofil 
 
- gemäß Vibrationsprofil 
A 
Komponenten, die am Motor verbaut 
sind 
 
- gemäß Vibrationsprofil 
B 
Komponenten, die am Getriebe 
verbaut sind 
 
- gemäß Vibrationsprofil 
C 
Komponenten, die an der 
Ansaugsammelleitung entkoppelt 
verbaut sind 
 
- gemäß Vibrationsprofil 
D 
Komponenten, die an gefederten 
Massen verbaut sind (Karosserie) 
 
- gemäß Vibrationsprofil 
E 
Komponenten, die an ungefederten 
Massen verbaut sind (Rad, 
Aufhängung) 
M-05 Mechanischer Schock 
alle Komponenten 
 Keine 
M-06 Mechanisches 
Dauerschocken 
Komponenten, die in oder an Türen 
und Klappen montiert sind 
Anzahl der Schocks 
K-01 Hoch-
/Tieftemperaturlagerung 
alle Komponenten 
Keine 
K-02 Stufentemperaturtest 
alle Komponenten 
Keine 
K-03 Tieftemperaturbetrieb 
alle Komponenten 
Keine 
K-04 Nachlackiertemperatur 
Komponenten, die im Außenbereich 
montiert sind und an denen bei einer 
Nachlackierung erhöhte 
Temperaturen auftreten können. 
Keine 
K-05 Temperaturschock 
(Komponente) 
alle Komponenten 
-   
Prüfverfahren (Na oder Nc), 
wenn Nc: Prüfmedium 
 
gemäß  
 
DIN EN 60068-2-14 
Na (Luft-Luft) 
Komponenten, die nicht permanent in 
einer Flüssigkeit betrieben werden 
 


### 第 70 页
Seite 70 
VW 80000: 2017-10 
 
 
 
 
 
 
Prüfung 
Anzuwenden auf 
Erforderliche Angabe 
 
gemäß  
 DIN EN 60068-2-14 
Nc (Medium-Medium) 
Komponenten, die permanent in einer 
Flüssigkeit betrieben werden (IP X8) 
K-06 Salzsprühnebelprüfung 
mit Betrieb, Außenraum 
Komponenten, die im Außenbereich, 
Unterboden oder Motorraum montiert 
sind 
Keine 
K-07 Salzsprühnebelprüfung 
mit Betrieb, Innenraum 
Komponenten, die in exponierten 
Stellen im Innenraum montiert sind 
(z. B. Seitentaschen im Kofferraum, 
Türnassraum, Reserveradmulde) 
Keine 
K-08 Feuchte-Wärme, zyklisch  alle Komponenten 
Keine 
K-09 Feuchte-Wärme, zyklisch  
(mit Frost) 
alle Komponenten 
Keine 
K-10 Wasserschutz – IPX0 bis 
IPX6K 
alle Komponenten 
IP-Schutzgrad 
 
 
- Schutzgrad IPX0 
Komponenten, die keinen 
Wasserschutz benötigen 
 
- Schutzgrad IPX1 
Komponenten, bei denen senkrecht 
fallende Tropfen keine schädlichen 
Wirkungen haben dürfen 
 
- Schutzgrad IPX2 
Komponenten mit bis zu 15° Neigung 
in Einbaulage, bei denen senkrechten 
fallende Tropfen keine schädlichen 
Wirkungen haben dürfen 
 
- Schutzgrad IPX3 
Komponenten, bei denen 
Sprühwasser keine schädliche 
Wirkungen haben darf 
 
- Schutzgrad IPX4K 
Komponenten, bei denen 
Spritzwasser mit erhöhtem Druck 
keine schädliche Wirkungen haben 
darf 
 
- Schutzgrad IPX5 
Komponenten, bei denen 
Strahlwasser keine schädliche 
Wirkungen haben darf 
 
- Schutzgrad IPX6K 
Komponenten, bei denen starkes 
Strahlwasser mit erhöhtem Druck 
keine schädliche Wirkungen haben 
darf 
K-11 Hochdruck-
/Dampfstrahlreinigung 
Komponenten, die direkt einer 
Hochdruck-/Dampfstrahlreinigung 
oder Unterbodenwäsche ausgesetzt 
sein können 
Keine 
K-12 Temperaturschock mit 
Schwallwasser 
im Außenbereich oder Motorraum 
verbaute Komponenten, bei denen mit 
einem Wasserschwall gerechnet 
werden muss (z. B. beim Durchfahren 
von Pfützen)  
Keine 
K-13 Temperaturschock 
Tauchen 
unterhalb der Wattiefe verbaute 
Komponenten, bei denen mit 
zeitweiligem Eintauchen in (Salz-) 
Wasser gerechnet werden muss (z. B. 
beim Durchfahren von Gewässern) 
(IPX7) 
Keine 
K-14 Feuchte Wärme konstant alle Komponenten 
Schärfegrad 


### 第 71 页
 
  
Seite 71 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Prüfung 
Anzuwenden auf 
Erforderliche Angabe 
K-15 Betauungs- und 
Klimaprüfung 
Die Notwendigkeit der Prüfung ist 
komponentenspezifisch zu bewerten. 
Falls erforderlich ist die Prüfung im 
Lastenheft anzuziehen. 
 
Falls die Prüfung im Lastenheft 
angezogen ist, kann die Prüfung für 
Komponenten mit wasserdichten 
Gehäusen wahlweise als Prüfung K-
15 a Betauungsprüfung mit 
Baugruppen oder als Prüfung K-15 b 
Klimaprüfung für Komponenten mit 
wasserdichten Gehäusen 
durchgeführt werden; für 
Komponenten ohne wasserdichte 
Gehäuse ist die Prüfung als Prüfung 
K-15 a Betauungsprüfung mit 
Baugruppen durchzuführen. 
Keine 
K-16 Temperaturschock (ohne 
Gehäuse) 
Baugruppen aller Komponenten 
 Keine 
K-17 Sonnenbestrahlung 
Komponenten, die in Einbaulage 
direkter Sonnenbestrahlung 
ausgesetzt sind. 
Prüfprofil 
K-18 Schadgasprüfung 
Komponenten mit offenen Steck- und 
Schaltkontakten  
 Keine 
C Chemische Prüfungen 
alle Komponenten 
Chemikalien 
Betriebsart 
L-01 Lebensdauertest 
mechanisch/hydraulischer 
Dauerlauf 
Komponenten mit 
mechanischen/hydraulischen 
Betätigungs-/Funktionszyklen wie 
z. B. Bremsbetätigungen, 
Sitzverstellzyklen, Schalter-
/Tasterbetätigungen 
Anzahl Funktions-
/Betätigungszyklen 
L-02 Lebensdauertest 
Hochtemperaturdauerlauf 
alle Komponenten 
Prüfdauer 
L-03 Lebensdauertest 
Temperaturwechseldauerlauf 
alle Komponenten 
Anzahl Testzyklen 
9.2 Prüfablaufplan 
Ein komponentenspezifischer Prüfablaufplan ist im Lastenheft zu spezifizieren. 
 
Als Diskussionsgrundlage für Kooperationsprojekte zwischen mehreren OEMs (z. B. 
Industriebaukasten (IBK)) ist im Anhang A ein Prüfablaufplan dargestellt. 
 
 
 


### 第 72 页
Seite 72 
VW 80000: 2017-10 
 
 
 
 
 
 
10 Mechanische Anforderungen und Prüfungen 
10.1 M-01 Freier Fall 
10.1.1 
Zweck 
Diese Prüfung simuliert den freien Fall einer Komponente auf den Boden, wie er 
während der gesamten Prozesskette bis zum bestimmungsgemäßen Verbau der 
Komponente auftreten kann. 
 
Sie dient der Absicherung, dass eine bei einem Fall sichtbar unbeschädigte und 
deshalb im Fahrzeug verbaute Komponente keine verdeckten Schäden oder 
Vorschädigungen aufweist, z. B. interne Bauelementablösungen oder Risse. 
10.1.2 
Prüfung 
Tabelle 47: Prüfparameter M-01 Freier Fall 
Betriebsart des Prüflings 
Fahrzeugaufbauunverbaut  
Fallhöhe 
1 m 
Aufprallfläche 
Betonboden 
Prüfzyklus 
Für jeden der 3 Prüflinge jeweils ein Fall in beide 
Richtungen einer Raumachse (1. Prüfling: ±X,  
2. Prüfling: ±Y, 3. Prüfling: ±Z)  
Anzahl der Prüflinge 
3 
10.1.3 
Anforderung 
Der Prüfling muss mit bloßem Auge visuell untersucht werden und durch Schütteln 
auf gelockerte oder klappernde Teile geprüft werden. 
 
- 
Ist der Prüfling sichtbar beschädigt, sind die Beschädigungen im Prüfbericht 
zu dokumentieren.  
 
- 
Ist der Prüfling nicht sichtbar beschädigt, muss der Prüfling nach der Prüfung 
voll funktionsfähig sein und alle Parameter müssen innerhalb der Spezifikation 
liegen. Der Nachweis erfolgt durch einen P-03 Parametertest (groß) gemäß 
Kapitel 4.7.3.  
 
- 
Verdeckte Schäden sind nicht zulässig. 
 
 


### 第 73 页
 
  
Seite 73 
VW 80000: 2017-10 
 
 
 
 
 
 
 
10.2 M-02 Steinschlagprüfung 
10.2.1 
Zweck 
Diese Prüfung simuliert die mechanische Beanspruchung der Komponente durch 
Splittbewurf. 
Sie dient der Absicherung der Beständigkeit der Komponente gegenüber 
Fehlerbildern wie z. B. Deformation und Risse. 
10.2.2 
Prüfung 
Durchführung der Prüfung in Anlehnung an DIN EN ISO 20567-1, Prüfverfahren B, 
mit folgenden Parametern: 
Tabelle 48: Prüfparameter M-02 Steinschlagprüfung 
Betriebsart des Prüflings 
Fahrzeugaufbaumontage 
Menge Strahlungsmittel 
500 g 
Prüfdruck 
2 bar 
Beschussmaterial 
Hartgussgranulat nach DIN EN ISO 11124-2, 
Korngröße 4 bis 5 mm 
Prüffläche auf Prüfling 
Alle im Fahrzeug frei zugänglichen Flächen 
Auftreffwinkel 
54° zur Strahlrichtung 
Prüfeinrichtung 
Multisteinschlagprüfgerät nach 
DIN EN ISO 20567-1 
Anzahl der Zyklen 
2 
Anzahl der Prüflinge 
6 
10.2.3 
Anforderung 
Der Prüfling muss vor und nach der Prüfung voll funktionsfähig sein und alle 
Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt durch 
einen P-02 Parametertest (klein) gemäß Kapitel 4.7.2. 
 
Zusätzlich muss der Prüfling mit bloßem Auge visuell untersucht und durch Schütteln 
auf gelockerte oder klappernde Teile geprüft werden. 
Veränderungen/Beschädigungen sind im Prüfbericht zu dokumentieren und mit dem 
Auftraggeber zu bewerten. 
 
Eine Auswertung nach Kennwerten DIN EN ISO 20567-1 ist nicht erforderlich. 


### 第 74 页
Seite 74 
VW 80000: 2017-10 
 
 
 
 
 
 
10.3 M-03 Staubprüfung 
10.3.1 
Zweck 
Diese Prüfung simuliert die Staubbelastung der Komponente während des 
Fahrzeugbetriebs. 
Sie dient der Absicherung der Beständigkeit der Komponente gegenüber 
elektrischen und mechanischen Fehlerbildern. 
10.3.2 
Prüfung 
Durchführung der Prüfung gemäß ISO-20653 mit folgenden Parametern: 
Tabelle 49: Prüfparameter M-03 Staubprüfung 
Betriebsart des 
Prüflings 
Für elektrische / elektronische Komponenten: Betriebmin 
Für mechatronische Komponenten (z. B. für Komponenten mit 
drehenden Teilen, Bedienelemente): 
Intermittierend Betriebmax und Betriebmin nach Bild 25. 
Prüfaufbau 
Vertikale Strömungsrichtung gemäß ISO-20653:2006 Figure 1 
Zu erreichender 
Schutzgrad 
Wie im Lastenheft festgelegt 
Prüfdauer 
20 Zyklen mit je 20 Minuten 
Anzahl der Prüflinge 
6 
 
Bild 25: Prüfablauf M-03 Staubprüfung 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. Der Prüfaufbau (Einbaulage, Abdeckungen, Blenden, Situation im 
Betrieb) ist vom Auftragnehmer vorzuschlagen, mit dem Auftraggeber abzustimmen 
und zu dokumentieren. 
10.3.3 
Anforderung 
Der im Lastenheft geforderte Schutzgrad nach ISO 20653 muss erreicht werden. 
 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch einen P-02 Parametertest (klein) gemäß Kapitel 4.7.2. 
 
Zusätzlich muss der Prüfling mit bloßem Auge visuell untersucht werden. 
Veränderungen bzw. Beschädigungen sind im Prüfbericht zu dokumentieren und mit 
dem Auftraggeber zu bewerten. 
 
Betriebmin 
5s 
5 Minuten 
1 Zyklus / 20 Minuten 
Staub 
Betriebmax 


### 第 75 页
 
  
Seite 75 
VW 80000: 2017-10 
 
 
 
 
 
 
 
10.4 M-04 Vibrationsprüfung 
10.4.1 
Zweck 
Diese Prüfungen simulieren die Vibrationsbeanspruchung der Komponente im 
Betriebsmodus Fahren. 
Sie dient der Absicherung der Beständigkeit der Komponente gegenüber 
Fehlerbildern wie z. B. Bauelementablösungen und Materialermüdung. 
10.4.2 
Prüfung 
Die Prüfung erfolgt in Anlehnung an ISO 16750 Teil 3.  
Durchführung der Prüfung gemäß DIN EN 60068-2-6 für sinusförmige 
Schwinganregung und DIN EN 60068-2-64 für breitbandförmige Schwinganregung 
mit folgenden Parametern: 
 
Tabelle 50: Prüfparameter Vibration allgemein 
Betriebsart des Prüflings  
Intermittierend Fahrenmin und Fahrenmax  
(siehe Bild 26) 
Überlagerter Temperaturverlauf 
Wiederholender gemäß Bild 26 und 
Tabelle 44 
Frequenzdurchlaufzeit bei sinusförmiger 
Anregung 
1 Oktave/min, logarithmisch 
Vibrationsprofil A 
(für am Motor verbaute Komponenten) 
Schwingungsanregung, sinusförmig 
gemäß Bild 27 und Tabelle 52 
 
Schwingungsanregung, 
Breitbandrauschen 
gemäß Bild 28 und Tabelle 53 
Vibrationsprofil B  
(für am Getriebe verbaute Komponenten) 
Schwingungsanregung, sinusförmig 
gemäß Bild 29 und Tabelle 54 
 
Schwingungsanregung, 
Breitbandrauschen 
gemäß Bild 30 und Tabelle 55 
Vibrationsprofil C 
(für an der Ansaugsammelleitung 
entkoppelt verbaute Komponenten) 
Schwingungsanregung, sinusförmig 
gemäß Bild 31 und Tabelle 56 
Vibrationsprofil D 
(Karosserieanbauteile für an gefederten 
Massen verbaute Komponenten) 
Schwingungsanregung, 
Breitbandrauschen 
gemäß Bild 32 und Tabelle 57 
Vibrationsprofil E 
für ungefederte Massen (Fahrwerk) 
Schwingungsanregung, 
Breitbandrauschen 
gemäß Bild 33 und Tabelle 58 
Anzahl der Prüflinge 
6 
 
 
Komponenten, die an einer E-Maschine verbaut werden, sind mindestens nach 
Vibrationsprofil D zu prüfen. Dieses Prüfprofil berücksichtigt jedoch nicht die 
speziellen Vibrationsbelastungen, die von einer E-Maschine ausgehen. In der Praxis 


### 第 76 页
Seite 76 
VW 80000: 2017-10 
 
 
 
 
 
 
können diese speziellen Vibrationsbelastungen aber auftreten und die Komponente 
belasten. Daher müssen die speziellen Vibrationsbelastungen, die von einer E-
Maschine ausgehen, bei der Prüfung berücksichtigt werden. Dazu sind Messungen 
an der jeweiligen E-Maschine notwendig. 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
 
Mit dem Auftraggeber ist festzulegen, wie angeschlossene Leitungen (z. B. 
elektrische Leitungen, Kühlmittelschläuche, Hydraulikleitungen, …) im 
Versuchsaufbau zu befestigen sind. 
 
Bei Komponenten, die über Dämpfungselemente am Halter oder Fahrzeug verbaut 
sind, muss mit dem Auftraggeber festgelegt werden, ob 
 
- 
alle Prüflinge mit Dämpfungselementen, 
- 
alle Prüflinge ohne Dämpfungselemente oder 
- 
jeweils drei Prüflinge mit Dämpfungselementen und drei Prüflinge ohne 
Dämpfungselemente 
 
geprüft werden müssen. 
 
Die Abtastrate muss so gewählt werden, dass Unterbrechungen und Kurzschlüsse 
zweifelsfrei erkannt werden. 
Zusätzliche Prüfungen zur Absicherung der Festigkeit des Gesamtsystems aus 
Komponente, Halter und Anbauteilen im Zusammenbau sind mit dem Auftraggeber 
abzustimmen. 
 
Bild 26: Temperaturverlauf Vibration 
 
 
0
60
120
180
240
300
360
420
480
Temperatur   
Zeit in min. 
Tmin 
Tmax 
TRT 
Fahrenmin 
Fahrenmin 
Fahrenmax von 135min bis 410min 


### 第 77 页
 
  
Seite 77 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Tabelle 51: Temperaturverlauf Vibration 
Zeit in min 
Temperatur in °C 
0 
TRT 
60 
Tmin 
150 
Tmin 
300 
Tmax 
410 
Tmax 
480 
TRT 
 
Bei vorhandenem Kühlmittelkreislauf ist die Kühlmitteltemperatur der jeweiligen 
Prüftemperatur bis zu den Grenzen Tkühl,min und Tkühl,max nachzuführen. Außerhalb der 
Kühlmitteltemperaturgrenzen wird nur noch die Umgebungstemperatur variiert. 


### 第 78 页
Seite 78 
VW 80000: 2017-10 
 
 
 
 
 
 
10.4.2.1 
Vibrationsprofil A (Komponenten, die am Motor verbaut sind) 
Tabelle 52: Prüfparameter Vibration, sinusförmig für Motoranbauteile  
Schwingungsanregung 
sinusförmig 
Prüfdauer für jede 
Raumachse 
22 h 
Schwingungsprofil 
Kurve 1 ist für Komponenten, die an Motoren mit 5 oder 
weniger Zylindern montiert werden. 
Kurve 2 ist für Komponenten, die an Motoren mit 6 oder 
mehr Zylindern montiert werden. 
 
Für Komponenten, die in beiden Fällen verwendet 
werden können, werden die Kurven kombiniert. 
Kurve 1 in Bild 27 
Frequenz in Hz 
Amplitude der Beschleunigung in 
m/s² 
100 
100 
200 
200 
240 
200 
270 
100 
440 
100 
Kurve 2 in Bild 27 
Frequenz in Hz 
Amplitude der Beschleunigung in 
m/s² 
100 
100 
150 
150 
440 
150 
Kombination  
Frequenz in Hz 
Amplitude der Beschleunigung in 
m/s² 
100 
100 
150 
150 
200 
200 
240 
200 
255 
150 
440 
150 


### 第 79 页
 
  
Seite 79 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
 
Bild 27: Schwingungsprofil, sinusförmig für Motoranbauteile 
 
 
 
0
50
100
150
200
250
10
100
1000
Amplitude der Beschleunigung  in m/s²
Frequenz in Hz
Kurve 1
Kurve 2


### 第 80 页
Seite 80 
VW 80000: 2017-10 
 
 
 
 
 
 
Tabelle 53: Prüfparameter Vibration, Breitbandrauschen für Motoranbauteile  
Schwingungsanregung 
Breitbandrauschen 
Prüfdauer für jede Raumachse 
22 h 
Effektivwert der Beschleunigung 181 m/s² 
Schwingungsprofil Bild 28 
Frequenz in Hz Leistungsdichte-Spektrum 
in (m/s²)²/Hz 
10 
10 
100 
10 
300 
0,51 
500 
20 
2 000 
20 
 
 
 
Bild 28: Schwingungsprofil, Breitbandrauschen für Motoranbauteile 
 
 
0,1
1
10
100
10
100
1000
10000
Leistungsdichtespektrum in (m/s²)²/Hz
Frequenz in Hz


### 第 81 页
 
  
Seite 81 
VW 80000: 2017-10 
 
 
 
 
 
 
 
10.4.2.2 
Vibrationsprofil B (Komponenten, die am Getriebe verbaut sind) 
Tabelle 54: Prüfparameter Vibration, sinusförmig für Getriebeanbauteile 
Schwingungsanregung 
sinusförmig 
Prüfdauer für jede 
Raumachse 
22 h 
Schwingungsprofil Bild 29 
Frequenz in 
Hz 
Amplitude der Beschleunigung in 
m/s² 
100 
30 
200 
60 
440 
60 
 
 
Bild 29: Schwingungsprofil, sinusförmig für Getriebeanbauteile 
 
 
0
10
20
30
40
50
60
70
100
1000
Amplitude der Beschleunigung in m/s²
Frequenz in Hz


### 第 82 页
Seite 82 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Tabelle 55: Prüfparameter Vibration, Breitbandrauschen für Getriebeanbauteile  
Schwingungsanregung 
Breitbandrauschen 
Prüfdauer für jede Raumachse 
22 h 
Effektivwert der Beschleunigung 96,6 m/s² 
Schwingungsprofil Bild 30 
Frequenz in Hz Leistungsdichte-Spektrum 
in (m/s²)²/Hz 
10 
10 
100 
10 
300 
0,51 
500 
5 
2 000 
5 
 
 
Bild 30: Schwingungsprofil, Breitbandrauschen für Getriebeanbauteile 
 
 
0,1
1
10
10
100
1000
10000
Leistungsdichtespektrum in (m/s²)²/Hz
Frequenz in Hz


### 第 83 页
 
  
Seite 83 
VW 80000: 2017-10 
 
 
 
 
 
 
 
10.4.2.3 
Vibrationsprofil C (Komponenten, die an der 
Ansaugsammelleitung entkoppelt verbaut sind) 
Tabelle 56: Prüfparameter, sinusförmig für Komponenten an der entkoppelten 
Ansaugsammelleitung 
Schwingungsanregung 
sinusförmig 
Prüfdauer für jede 
Raumachse 
22 h 
Schwingungsprofil Bild 31 
Frequenz in 
Hz 
Amplitude der Beschleunigung in 
m/s² 
100 
90 
200 
180 
325 
180 
500 
80 
1 500 
80 
 
Bild 31: Schwingungsprofil, sinusförmig für Komponenten in der entkoppelten 
Ansaugsammelleitung 
 
 
0
20
40
60
80
100
120
140
160
180
200
100
1000
10000
Amplitude der Beschleunigung in m/s²
Frequenz in Hz


### 第 84 页
Seite 84 
VW 80000: 2017-10 
 
 
 
 
 
 
10.4.2.4 
Vibrationsprofil D (Komponenten, die an gefederten Massen 
verbaut sind (Karosserie)) 
Tabelle 57: Prüfparameter, Breitbandrauschen für gefederte Massen 
Schwingungsanregung 
Breitbandrauschen 
Prüfdauer für jede Raumachse 
8 h 
Effektivwert der Beschleunigung 30,8 m/s² 
Schwingungsprofil Bild 32 
Frequenz in Hz Leistungsdichte-Spektrum 
in (m/s²)²/Hz 
5 
0,884 
10 
20 
55 
6,5 
180 
0,25 
300 
0,25 
360 
0,14 
1 000 
0,14 
2 000 
0,14 
 
 
Bild 32: Schwingungsprofil, Breitbandrauschen für gefederte Massen 
 
 
0,1
1
10
100
1
10
100
1000
10000
Leistungsdichtespektrum in (m/s²)²/Hz
Frequenz in Hz


### 第 85 页
 
  
Seite 85 
VW 80000: 2017-10 
 
 
 
 
 
 
 
10.4.2.5 
Vibrationsprofil E (Komponenten, die an ungefederten Massen 
verbaut sind (Rad, Aufhängung) ) 
Tabelle 58: Prüfparameter, Breitbandrauschen für ungefederte Massen 
Schwingungsanregung 
Breitbandrauschen 
Prüfdauer für jede Raumachse 
8 h 
Effektivwert der Beschleunigung 107,3 m/s² 
Schwingungsprofil Bild 33 
Frequenz 
in Hz 
Leistungsdichte-Spektrum 
in (m/s²)²/Hz 
20 
200 
40 
200 
300 
0,5 
800 
0,5 
1 000 
3 
2 000 
3 
 
Bild 33: Schwingungsprofil, Breitbandrauschen für ungefederte Massen 
10.4.3 
Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-03 Parametertest (groß) 
gemäß Kapitel 4.7.3. 
 
Zusätzlich muss der Prüfling mit bloßem Auge visuell untersucht und durch Schütteln 
auf gelockerte oder klappernde Teile geprüft werden. 
 
0,1
1
10
100
1000
10
100
1000
10000
Leistungsdichtespektrum in (m/s²)²/Hz
Frequenz in Hz


### 第 86 页
Seite 86 
VW 80000: 2017-10 
 
 
 
 
 
 
10.5 M-05 Mechanischer Schock 
10.5.1 
Zweck 
Diese Prüfung simuliert die mechanische Beanspruchung der Komponente z. B. bei 
dem Überfahren von Bordsteinen etc. oder bei Unfällen. 
Sie dient der Absicherung der Beständigkeit der Komponente gegenüber 
Fehlerbildern wie z. B. Rissen und Bauelementablösungen. 
10.5.2 
Prüfung 
Durchführung der Prüfung gemäß DIN EN 60068-2-27 mit folgenden Parametern: 
 
Tabelle 59: Prüfparameter M-05 Mechanischer Schock 
Betriebsart des Prüflings 
Intermittierend Fahrenmin und 
Fahrenmax  
Spitzenbeschleunigung 
500 m/s2 
Schockdauer 
6 ms 
Schockform 
Halbsinus 
Anzahl Schocks je Richtung (±X, ±Y, ±Z) 
10 
Anzahl der Prüflinge 
6 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
 
Die Prüfung ist ohne Halter oder Anbauteile durchzuführen. Mit dem Auftraggeber ist 
festzulegen, wie angeschlossene Leitungen (z. B. elektrische Leitungen, 
Kühlmittelschläuche, Hydraulikleitungen, …) im Versuchsaufbau zu befestigen sind. 
 
Bei Komponenten, die über Dämpfungselemente am Halter oder Fahrzeug verbaut 
sind, muss mit dem Auftraggeber festgelegt werden, ob 
 
- 
alle Prüflinge mit Dämpfungselementen, 
- 
alle Prüflinge ohne Dämpfungselemente oder 
- 
jeweils drei Prüflinge mit Dämpfungselementen und drei Prüflinge ohne 
Dämpfungselemente 
 
geprüft werden müssen. 
 
Die Zeit zwischen den Schockimpulsen muss groß genug sein um ein komplettes 
Abklingen der vorherigen Schwingung sicherzustellen. 
 
10.5.3 
Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 


### 第 87 页
 
  
Seite 87 
VW 80000: 2017-10 
 
 
 
 
 
 
 
durch kontinuierliche Parameterüberwachung und einen P-02 Parametertest (klein) 
gemäß Kapitel 4.7.2. 
Zusätzlich muss der Prüfling mit bloßem Auge visuell untersucht und durch Schütteln 
auf gelockerte oder klappernde Teile geprüft werden. 
10.6 M-06 Mechanisches Dauerschocken 
10.6.1 
Zweck 
Diese Prüfung simuliert die Beschleunigungskräfte von Komponenten, die in Türen 
oder Klappen verbaut sind und beim Öffnen und Schließen hohe Beschleunigungen 
erfahren. 
Sie dient der Absicherung der Beständigkeit der Komponente gegenüber 
Fehlerbildern wie z. B. Bauelementablösungen und Materialermüdung. 
10.6.2 
Prüfung 
Durchführung der Prüfung gemäß DIN EN 60068-2-29 mit folgenden Parametern: 
 
Tabelle 60: Prüfparameter M-06 Mechanisches Dauerschocken 
Betriebsart des Prüflings 
Betriebmax 
Spitzenbeschleunigung 
300 m/s² 
Schockdauer 
6 ms 
Schockform 
Halbsinus 
Anzahl Schocks 
 
Einbaubereich 
Anzahl Schocks 
Fahrertür 
100 000 
Beifahrer- und Fondtüren 
50 000 
Kofferraumdeckel/Heckklappe 30 000 
Motorraumklappe 
3 000 
Ist die Komponente an mehreren Einbaubereichen 
verbaut, ist die höchste Anzahl der Schocks zu 
wählen. 
Einbaulage 
Der Prüfling muss sich in einer dem Fahrzeug 
entsprechenden Einbausituation auf der 
Prüfeinrichtung befinden. 
Anzahl der Prüflinge 
6 
 
10.6.3 
Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-02 Parametertest (klein) 
gemäß Kapitel 4.7.2. 
Zusätzlich muss der Prüfling mit bloßem Auge visuell untersucht und durch Schütteln 
auf gelockerte oder klappernde Teile geprüft werden. 
 
 
 


### 第 88 页
Seite 88 
VW 80000: 2017-10 
 
 
 
 
 
 
10.7  M-07 Druckwechselprüfung Kühlkreislauf 
10.7.1 
Zweck 
Diese Prüfung simuliert die Belastung der Komponente durch Schwankungen des 
Kühlmitteldrucks, 
sowie 
Zustände 
während 
der 
Nachheizphase 
und 
der 
Unterdruckbefüllung des Kühlsystems. Sie ist ausschließlich für Komponenten 
anzuwenden, 
die 
an 
einen 
Kühlmittelkreislauf 
angeschlossen 
sind. 
Sie dient dem Nachweis der mechanischen Festigkeit der von Druckschwankungen 
im Kühlkreislauf betroffenen Bauteile (z. B. Kühlplatten des Power-Moduls).  
10.7.2 
Prüfung 
Tabelle 61: Prüfparameter M-07 Druckwechselprüfung Kühlkreislauf 
Betriebsart des 
Prüflings 
Fahrzeugaufbaumontage 
Durchführung  
der Prüfung 
Teil 1 - Druckpulsationsprüfung: 
 Unterer Prüfdruck Pmin: 0,5 (-0,1) barabs 
 Oberer Prüfdruck Pmax: 2,0 (+0,1) barabs 
 Druckwechselfrequenz: 25-35 1/min 
 Druckwechselanzahl: 100.000 
 Prüftemperatur: Tkühl,max 
Teil 2 - Überdruckprüfung: 
 Prüfdruck P: 4,0 (+0,1) barabs 
 Prüfdauer 1 h 
 Prüftemperatur: Tkühl,max 
Teil 3 – Unterdruckprüfung (ohne Prüfmedium): 
 Prüfdruck P: 0,01 (-0,01) barabs 
 Prüfdauer 30 min 
 Prüftemperatur: TRT 
Prüfmedium 
Ist mit dem Auftraggeber  
abzustimmen.  
Anzahl 
Prüflinge 
6 
 
10.7.3 
Anforderung 
Der Prüfling muss vor und nach der Prüfung voll funktionsfähig sein und alle 
Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt durch 
einen P-03 Parametertest (groß) gemäß Kapitel 4.7.3. 
 
 
 


### 第 89 页
 
  
Seite 89 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11 Klimatische Anforderungen und Prüfungen 
11.1 K-01 Hoch-/Tieftemperaturlagerung 
11.1.1 
Zweck 
Diese Prüfung simuliert die thermische Beanspruchung der Komponente bei 
Lagerung und Transport. 
Sie dient dem Nachweis der Beständigkeit gegenüber Lagerung bei hohen oder 
tiefen Temperaturen wie z. B. beim Transport der Komponente (Flugzeug, 
Schiffscontainer). 
Wird die Prüfung am Anfang einer Prüfsequenz durchgeführt, dient sie auch der 
Angleichung aller Komponenten auf gleichen Ausgangsbedingungen. 
11.1.2 
Prüfung 
Tabelle 62: Prüfparameter K-01 Hoch-/Tieftemperaturlagerung 
Betriebsart des Prüflings 
Fahrzeugaufbauunverbaut 
Prüfdauer und Prüftemperatur 
2 Zyklen je 24 h (bestehend aus jeweils 12 h 
Lagerung bei Tmin und 12 h Lagerung bei Tmax) 
Anzahl der Prüflinge 
Wie im Prüfablaufplan im Lastenheft festgelegt. 
11.1.3 
Anforderung 
Der Prüfling muss vor und nach der Prüfung voll funktionsfähig sein und alle 
Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt durch 
einen P-03 Parametertest (groß) gemäß Kapitel 4.7.3. 
 
Zusätzlich muss der Prüfling mit bloßem Auge visuell untersucht und durch Schütteln 
auf gelockerte oder klappernde Teile geprüft werden. 
 


### 第 90 页
Seite 90 
VW 80000: 2017-10 
 
 
 
 
 
 
11.2 K-02 Stufentemperaturtest 
11.2.1 
Zweck 
Diese Prüfung simuliert den Betrieb der Komponente bei unterschiedlichen 
Umgebungstemperaturen. 
Sie dient der Absicherung der Komponente gegenüber Fehlfunktionen, die innerhalb 
eines kleinen Intervalls des Umgebungstemperaturbereiches auftreten können, sowie 
das Aufstartverhalten der Komponenten über den vollständigen 
Umgebungstemperaturbereiches. 
 
11.2.2 
Prüfung 
Tabelle 63: Prüfparameter K-02 Stufentemperaturtest 
Betriebsart des 
Prüflings 
Während des P-01 Parametertest (Funktionstest) gemäß 
Kapitel 4.7.1 Betriebmax, sonst Betriebmin 
Prüftemperatur 
Die Prüflinge sind mit einem Temperaturprofil gemäß Bild 34 
zu beaufschlagen. 
Die Temperaturänderung je Stufe beträgt 5 °C.  
Prüfablauf 
 
Der Prüfling ist bei jeder Temperaturstufe bis zur 
vollständigen Durchtemperierung (siehe Kapitel 0) zu halten. 
Anschließend ist ein P-01 Parametertest (Funktionstest) 
gemäß Kapitel 4.7.1 durchzuführen. 
 
Anzahl der Prüflinge 
Wie im Prüfablaufplan im Lastenheft festgelegt, jedoch 
mindestens 6 
 
Bild 34: Temperaturprofil Stufentemperaturtest 
Bei vorhandenem Kühlmittelkreislauf ist die Kühlmitteltemperatur der jeweiligen 
Prüftemperatur bis zu den Grenzen Tkühl,min und Tkühl,max nachzuführen. Außerhalb der 
Kühlmitteltemperaturgrenzen wird nur noch die Umgebungstemperatur variiert. 
 


### 第 91 页
 
  
Seite 91 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.2.3 
Anforderung 
Alle Parameter des Prüflings müssen bei jedem P-01 Parametertest (Funktionstest) 
gemäß Kapitel 4.7.1 innerhalb der Spezifikation liegen. 
11.3 K-03 Tieftemperaturbetrieb 
11.3.1 
Zweck 
Diese Prüfung simuliert die Beanspruchung der Komponente bei tiefen 
Temperaturen. 
Sie dient der Absicherung der Funktion der Komponente nach langer Parkzeit bzw. 
Fahrzeit bei extrem tiefen Temperaturen. 
11.3.2 
Prüfung 
Durchführung der Prüfung gemäß DIN EN 60068-2-1, Prüfung Ab, mit folgenden 
Parametern: 
 
Tabelle 64: Prüfparameter K-03 Tieftemperaturbetrieb 
Betriebsart des 
Prüflings 
12 h Off-Grid-Parkenmin (Kl. 30 Komponente UBmin) 
12 h Betriebmax bei UBmin 
12 h Off-Grid-Parkenmin (Kl. 30 Komponente UB) 
12 h Betriebmax bei UB 
Prüfdauer 
48 h 
Prüftemperatur 
Tmin 
Anzahl der Prüflinge  
6 
 
Auch bei wärmeabgebenden Komponenten ist die Prüfung gemäß DIN EN 60068-2-
1, Prüfung Ab, durchzuführen. 
Bei vorhandenem Kühlmittelkreislauf ist die minimale Kühlmitteltemperatur Tkühl,min 
einzustellen. 
 
Bei Komponenten mit hoher Verlustleistung ist bei der Prüfung in Betriebsart 
Betriebmax eine Erhöhung der Prüfkammertemperatur durch die Eigenerwärmung 
über Tmin in Abstimmung zwischen Auftragnehmer und Auftraggeber zulässig. 
 
11.3.3 
Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-02 Parametertest (klein) 
gemäß Kapitel 4.7.2. 
 
 


### 第 92 页
Seite 92 
VW 80000: 2017-10 
 
 
 
 
 
 
11.4 K-04 Nachlackiertemperatur 
11.4.1 
Zweck 
Diese Prüfung simuliert die Beanspruchung der Komponente beim Nachlackieren. 
Sie dient der Absicherung der Komponente hinsichtlich thermisch bedingter 
Fehlerbilder, z. B. Rissbildung in Löt-, Klebe-, Bond- und Schweißverbindungen und 
an Dichtungen und Gehäusen. 
11.4.2 
Prüfung 
Tabelle 65: Prüfparameter K-04 Nachlackiertemperatur 
Betriebsart des Prüflings 
Off-Grid Parkenmin 
Prüfdauer und Prüftemperatur  
15 min bei 130 °C und 1 h bei 110 °C 
Anzahl der Prüflinge 
6 
 
Bei vorhandenem Kühlmittelkreislauf ist die Temperatur des ruhenden Kühlmittels auf 
TRT festzulegen. 
11.4.3 
Anforderung 
Der Prüfling muss vor und nach der Prüfung voll funktionsfähig sein und alle 
Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt durch 
einen P-02 Parametertest (klein) gemäß Kapitel 4.7.2. 
 
 


### 第 93 页
 
  
Seite 93 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.5 K-05 Temperaturschock (Komponente)  
11.5.1 
Zweck 
Diese Prüfung simuliert die thermische Beanspruchung der Komponente durch 
schockartigen Temperaturwechsel während des Fahrzeugbetriebs. 
Sie dient der Absicherung der Komponente hinsichtlich thermisch bedingter 
Fehlerbilder, z. B. Rissbildung in Löt-, Klebe-, Bond- und Schweißverbindungen und 
an Dichtungen und Gehäusen. 
11.5.2 
Prüfung 
Durchführung der Prüfung gemäß DIN EN 60068-2-14 mit folgenden Parametern: 
 
Tabelle 66: Prüfparameter K-05 Temperaturschock (Komponente) 
Betriebsart des 
Prüflings LV 
Fahrzeugaufbaumontage 
Untere Temperatur / 
Temperatur des kalten 
Prüfbades 
Tmin 
Obere Temperatur / 
Temperatur des 
warmen Prüfbades 
Tmax 
Haltezeit bei oberer/ 
unterer Temperatur 
15 min nach vollständiger Durchtemperierung (siehe Kapitel 
0) 
Überführungsdauer 
(Luft-Luft, Medium-
Medium) 
≤ 30 s 
Prüfflüssigkeit für 
Prüfung Nc 
Flüssigkeit, in der die Komponente im Fahrzeug betrieben 
wird. 
Prüfung 
Gemäß DIN EN 60068-2-14 Na für Komponenten, die nicht 
permanent in einer Flüssigkeit betrieben werden. 
 
Gemäß DIN EN 60068-2-14 Nc für Komponenten, die 
permanent in einer Flüssigkeit betrieben werden (IP X8). 
Der Prüfling ist dabei so einzutauchen, so dass alle Seiten 
des Prüflings von mindestens 25 mm der Prüfflüssigkeit 
umgeben sind. 
Anzahl der Zyklen 
100 
Anzahl der Prüflinge 
6 
11.5.3 
Anforderung 
Der Prüfling muss vor und nach der Prüfung voll funktionsfähig sein und alle 
Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt durch 
einen P-03 Parametertest (groß) gemäß Kapitel 4.7.3. 
 
Bei Prüfung Medium-Medium zusätzlich: 
Die Flüssigkeit darf nicht eindringen. Der Prüfling darf erst nach Abschluss der 
gesamten Prüfsequenz gemäß Prüfablaufplan (Kapitel 9.2) geöffnet werden. 


### 第 94 页
Seite 94 
VW 80000: 2017-10 
 
 
 
 
 
 
11.6 K-06 Salzsprühnebelprüfung mit Betrieb, Außenraum 
11.6.1 
Zweck 
Diese Prüfung simuliert die Beanspruchung der Komponente in salzhaltiger 
Atmosphäre und salzhaltigem Wasser, wie sie in bestimmten Regionen der Erde und 
bei winterlichen Straßenverhältnissen auftreten können. 
Sie dient der Absicherung der Komponente gegenüber Fehlfunktion unter 
Salzbelastung z. B. durch Kurzschlüsse und Leckströme aufgrund Eindringens von 
Salz in die Komponente. 
11.6.2 
Prüfung 
Durchführung der Prüfung gemäß DIN EN 60068-2-11 mit folgenden Parametern: 
 
Tabelle 67: Prüfparameter K-06 Salzsprühnebelprüfung mit Betrieb, Außenraum 
Betriebsart des 
Prüflings 
 
Während der Besprühphase: Intermittierend jeweils 1 h Off-Grid 
Parkenmin und 1 h Betriebmax. 
 
Während der Ruhephase: Off-Grid Parkenmin 
Prüftemperatur 
35 °C 
Prüfzyklus 
Jeder Prüfzyklus besteht aus 8 h Besprühphase und 4 h Ruhezeit 
gemäß Bild 35 
Anzahl der 
Prüfzyklen 
12 Zyklen 
 
Für Komponenten mit erhöhten Anforderungen an die Dichtheit 
gemäß Kapitel 4.9.1 (z. B. Beaufschlagung durch Wasser bei 
geparktem Fahrzeug) ist die Anzahl der Zyklen 
komponentenspezifisch anzupassen. 
Anzahl der 
Prüflinge 
6 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden.  
 
Eine Reinigung der Prüflinge nach der Prüfung ist nicht zulässig. 
 
Bei vorhandenem Kühlmittelkreislauf ist die Kühlmitteltemperatur auf Prüftemperatur 
einzustellen. 
 
 


### 第 95 页
 
  
Seite 95 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Bild 35: Salzsprühnebelprüfung mit Betrieb, Außenraum - Besprühphasen 
11.6.3 
Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-02 Parametertest (klein) 
gemäß Kapitel 4.7.2. 


### 第 96 页
Seite 96 
VW 80000: 2017-10 
 
 
 
 
 
 
11.7 K-07 Salzsprühnebelprüfung mit Betrieb, Innenraum 
11.7.1 
Zweck 
Diese Prüfung simuliert die Beanspruchung der Komponente in salzhaltiger 
Atmosphäre wie sie in bestimmten Regionen der Erde auftreten kann. Sie dient der 
Absicherung der Komponente gegenüber Fehlfunktion unter Salzbelastung z. B. 
durch Kurzschlüsse und Leckströme aufgrund Eindringens von Salz in die 
Komponenten. 
11.7.2 
Prüfung 
Durchführung der Prüfung gemäß DIN EN 60068-2-11 mit folgenden Parametern: 
Tabelle 68: Prüfparameter K-07 Salzsprühnebelprüfung mit Betrieb, Innenraum 
Betriebsart des Prüflings 
 
Während der Besprühphase: intermittierend jeweils 55 
min Off-Grid Parkenmin und 5 min Betriebmax 
Während der Ruhephase Off-Grid Parkenmin 
Prüftemperatur 
35 °C 
Prüfzyklus 
Jeder Prüfzyklus besteht aus 8 h Besprühphase und 4 h 
Ruhezeit gemäß Bild 36 
Anzahl der Prüfzyklen 
2 
Anzahl der Prüflinge 
6 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. Der Prüfaufbau (Einbaulage, Abdeckungen, Blenden, Situation im 
Betrieb) sind vom Auftragnehmer vorzuschlagen, mit dem Auftraggeber abzustimmen 
und zu dokumentieren. 
Bei vorhandenem Kühlmittelkreislauf ist die Kühlmitteltemperatur auf Prüftemperatur 
einzustellen. 
 
Bild 36: Salzsprühnebelprüfung mit Betrieb, Innenraum – Besprühphasen 
11.7.3 
Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-03 Parametertest (groß) 
gemäß Kapitel 4.7.3. 
 
4 
8 
12 
t [h] 
Ruhezeit 
Sprühphase 
Elektrischer Betrieb 
1 Zyklus 
Ein 
Aus 
Betriebmax 
Off-Grid Parkenmin 


### 第 97 页
 
  
Seite 97 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.8 K-08 Feuchte Wärme, zyklisch 
11.8.1 
Zweck 
Diese Prüfung simuliert die thermische Beanspruchung der Komponente durch 
zyklischen Temperaturwechsel bei hoher Luftfeuchte während des Fahrzeugbetriebs. 
Sie dient der Absicherung der Beständigkeit der Komponente gegen feuchte Wärme. 
11.8.2 
Prüfung 
Durchführung der Prüfung gemäß DIN EN 60068-2-30 mit folgenden Parametern: 
 
Tabelle 69: Prüfparameter K-08 Feuchte Wärme, zyklisch 
Betriebsart des Prüflings 
Während des P-01 Parametertest 
(Funktionstest) Betriebmax, sonst Betriebmin 
Gesamtprüfdauer 
144 h 
Prüfvariante 
Variante 1 
Obere Prüftemperatur 
55 °C 
Anzahl der Zyklen 
6 
Anzahl der Prüflinge 
6 
 
Jeweils nach Erreichen der oberen und der unteren Prüftemperatur muss ein P-01 
Parametertest (Funktionstest) durchgeführt werden. 
 
Bei vorhandenem Kühlmittelkreislauf ist die Kühlmitteltemperatur der jeweiligen 
Prüftemperatur bis zu den Grenzen Tkühl,min und Tkühl,max nachzuführen. Außerhalb der 
Kühlmitteltemperaturgrenzen wird nur noch die Umgebungstemperatur variiert. 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
11.8.3 
Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-03 Parametertest (groß) 
gemäß Kapitel 4.7.3. 
 
 


### 第 98 页
Seite 98 
VW 80000: 2017-10 
 
 
 
 
 
 
11.9 K-09 Feuchte Wärme, zyklisch (mit Frost) 
11.9.1 
Zweck 
Diese Prüfung simuliert die thermische Beanspruchung (einschließlich Befrostung) 
der Komponente durch zyklischen Temperaturwechsel bei hoher Luftfeuchte 
während des Fahrzeugbetriebs. 
Sie dient der Absicherung der Beständigkeit der Komponenten gegen feuchte 
Wärme. 
11.9.2 
Prüfung 
Durchführung der Prüfung gemäß DIN EN 60068-2-38 mit folgenden Parametern: 
 
Tabelle 70: Prüfparameter K-09 Feuchte Wärme, zyklisch (mit Frost) 
Betriebsart des Prüflings 
Intermittierend, jeweils 40 min Betriebmin und 10 min 
Betriebmax. 
 
Für Komponenten mit sehr hoher Eigenerwärmung muss 
der Auftragnehmer mit dem Autraggeber abstimmen, ob 
die Dauer in der Betriebsart Betriebmax auf die Dauer 
verkürzt werden soll, die zum Prüfen der 
Gesamtfunktionaltät der Komponente benötigt wird. Die 
Zyklusdauer von 50 min muss dabei beibehalten werden. 
Gesamtprüfdauer 
240 h 
Anzahl der Zyklen 
10 
Abfolge der Prüfzyklen 
Die ersten fünf Zyklen müssen mit Kältephase und die 
restlichen Zyklen müssen ohne Kältephase durchgeführt 
werden. 
Anzahl der Prüflinge 
6 
 
Bei vorhandenem Kühlmittelkreislauf ist die Kühlmitteltemperatur der jeweiligen 
Prüftemperatur bis zu den Grenzen Tkühl,min und Tkühl,max nachzuführen. Außerhalb der 
Kühlmitteltemperaturgrenzen wird nur noch die Umgebungstemperatur variiert. 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
 
11.9.3 
Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-03 Parametertest (groß) 
gemäß Kapitel 4.7.3. 
 
 


### 第 99 页
 
  
Seite 99 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.10 
K-10 Wasserschutz – IPX0 bis IPX6K 
11.10.1 
Zweck 
Diese Prüfung simuliert die Beanspruchung der Komponente mit Wasser. 
Sie dient der Absicherung der Funktion der Komponente z. B. bei der 
Beaufschlagung mit Tauwasser, Regen, Spritzwasser. 
11.10.2 
Prüfung 
Durchführung der Prüfung gemäß ISO 20653 mit folgenden Parametern: 
Tabelle 71: Prüfparameter K-10 Wasserschutz – IPX0 bis IPX6K 
Betriebsart des 
Prüflings 
Intermittierend, jeweils 1 min Betriebmin und 1 min 
Betriebmax 
Geforderter Schutzgrad 
Wie im Lastenheft festgelegt 
Anzahl der Prüflinge 
6 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
11.10.3 
Anforderung 
Der im Lastenheft geforderte Schutzgrad nach ISO 20653 muss erreicht werden. 
 
Es darf kein Wasser eindringen. Der Prüfling darf erst nach Abschluss der gesamten 
Prüfsequenz geöffnet werden. 
 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-02 Parametertest (klein) 
gemäß Kapitel 4.7.2. 


### 第 100 页
Seite 100 
VW 80000: 2017-10 
 
 
 
 
 
 
11.11 
K-11 Hochdruck-/Dampfstrahlreinigung 
11.11.1 
Zweck 
Diese Prüfung simuliert die Beanspruchung der Komponente mit Wasser während 
der Fahrzeugreinigung. 
Sie dient der Absicherung der Funktion der Komponente bei Hochdruck-
/Dampfstrahlreinigung. 
11.11.2 
Prüfung 
Durchführung der Prüfung gemäß ISO 20653 mit folgenden Parametern: 
Tabelle 72: Prüfparameter 
Betriebsart des 
Prüflings 
Off-Grid Parkenmin 
Geforderter 
Schutzgrad 
IP X9K 
Wasserdruck 
Der Mindestdruck für den Dampfstrahl beträgt 10 000 kPa 
(100 bar), gemessen unmittelbar an der Düse. 
Wassertemperatur 
80 °C 
Durchführung 
Der Prüfling muss aus jeder am Fahrzeug frei zugänglichen 
Raumrichtung bestrahlt werden. 
Anzahl der Prüflinge 
6 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
11.11.3 
Anforderung 
Der Schutzgrad IP X9K ist nach ISO 20653 ist zu erreichen. 
 
Es darf kein Wasser eindringen. Der Prüfling darf erst nach Abschluss der gesamten 
Prüfsequenz gemäß Prüfablaufplan (Kapitel 9.2) geöffnet werden. 
 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-02 Parametertest (klein) 
gemäß Kapitel 4.7.2. 


### 第 101 页
 
  
Seite 101 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.12 
K-12 Temperaturschock mit Schwallwasser 
11.12.1 
Zweck 
Diese Prüfung simuliert die Beanspruchung der Komponente mit Schwallwasser bei 
der Fahrt durch Pfützen. 
Sie dient der Absicherung der Funktion der Komponente bei schockartiger 
Abkühlung durch Wasser. 
11.12.2 
Prüfung 
Tabelle 73: Prüfparameter K-12 Temperaturschock mit Schwallwasser 
Betriebsart des Prüflings 
Intermittierend Fahrenmin und Fahrenmax  
(siehe Bild 37) 
Durchführung der Prüfung 
Aufheizen des Prüflings auf Prüftemperatur. 
Anschließend zyklische Beschwallung des Prüflings 
gemäß Bild 37. Der Prüfling muss über die 
Gesamtbreite beschwallt werden. 
Zyklusdauer 
30 min 
Prüftemperatur 
Tmax 
Prüfmedium zum 
Beschwallen 
Leitungswasser mit 3 % – Gewichtsanteil Arizonastaub, 
fein nach ISO 12103-1. Permanente Vermischung 
muss sichergestellt sein. 
Schwallwassertemperatur 
0 bis +4 °C 
Schwalldüse 
Siehe Bild 38 
Schwalldauer 
3 s 
Wasserdurchfluss 
3 bis 4 Liter pro Schwall/Düse 
Abstand Düse zum Prüfling 300 bis 350 mm 
Anzahl der Zyklen 
100 
Anzahl der Prüflinge 
6 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
Der Prüfaufbau (Einbaulage, Abdeckungen, Blenden, Situation im Betrieb) sind vom 
Auftragnehmer vorzuschlagen, mit dem Auftraggeber abzustimmen und zu 
dokumentieren. 
 
Bei vorhandenem Kühlmittelkreislauf ist die Kühlmitteltemperatur der jeweiligen 
Prüftemperatur bis zu der Grenze Tkühl,max nachzuführen. Oberhalb der 
Kühlmitteltemperaturgrenze wird nur noch die Umgebungstemperatur variiert. 
 
Prüfaufbau gemäß Bild 39. 
 


### 第 102 页
Seite 102 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Bild 37: Schwallwasserprüfung Schwallzeiten 
 
Maße in mm 
 
Bild 38: Schwallwasserprüfung – Schwalldüse 
t1 (1 Zyklus) 
Ein 
Aus 
Betriebmax 
Off-Grid Parkenmin 
Betriebsart 
Schwall 
t2 
t3 
t1 = 30 min 
t2 = 14:57 min 
t3 = 3 s 


### 第 103 页
 
  
Seite 103 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Schwall
Prüfling
Schwalldüse
300 mm
bis
 350 mm
 
Bild 39: Prüfaufbau Schwallwasser 
11.12.3 
Anforderung 
Es darf kein Wasser eindringen. Der Prüfling darf erst nach Abschluss der gesamten 
Prüfsequenz geöffnet werden. 
 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-02 Parametertest (klein) 
gemäß Kapitel 4.7.2. 


### 第 104 页
Seite 104 
VW 80000: 2017-10 
 
 
 
 
 
 
11.13 
K-13 Temperaturschock Tauchen  
11.13.1 
Zweck 
Diese Prüfung simuliert die Beanspruchung der Komponente beim Eintauchen in 
Wasser. 
Die Prüfung dient der Absicherung der Funktion der Komponente bei sofortiger 
Abkühlung durch Untertauchen der erwärmten Komponente. 
11.13.2 
Prüfung 
Durchführung der Prüfung gemäß ISO 20653 mit folgenden Parametern: 
 
Tabelle 74: Prüfparameter K-13 Temperaturschock Tauchen 
Betriebsart des 
Prüflings 
Fahrenmax 
Geforderter 
Schutzgrad 
IP X7 
Durchführung der 
Prüfung 
Aufheizen auf Top,max  
Halten bei Top,max bis zur vollständigen Durchtemperierung 
(siehe Kapitel 0) zuzüglich 15 min. 
Vollständiges Eintauchen des Prüflings in das  
Prüfmedium <= 5 s  
Der Prüfling muss von allen Seiten mit mind. 25mm 
Prüfmedium umgeben sein. 
Prüfmedium 
0 °C kaltes, 5 %iges Salzwasser 
Tauchdauer 
5 min 
Anzahl der Zyklen 
20 
Anzahl der Prüflinge 
6 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
Bei vorhandenem Kühlmittelkreislauf ist die Kühlmitteltemperatur der jeweiligen 
Prüftemperatur bis zu der Grenze Tkühl,max nachzuführen. Oberhalb der 
Kühlmitteltemperaturgrenze wird nur noch die Umgebungstemperatur variiert. 
11.13.3 
Anforderung 
Es darf kein Wasser eindringen. Der Prüfling darf erst nach Abschluss der gesamten 
Prüfsequenz gemäß Prüfablaufplan (Kapitel 9.2) geöffnet werden. 
 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-02 Parametertest (klein) 
gemäß Kapitel 4.7.2. 


### 第 105 页
 
  
Seite 105 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.14 
K-14 Feuchte Wärme konstant 
11.14.1 
K-14 a Feuchte Wärme konstant – Schärfegrad 1 
11.14.1.1 Zweck 
Diese Prüfung simuliert die Belastung der Komponente durch feuchte Wärme. 
Sie dient der Absicherung der Beständigkeit der Komponente hinsichtlich durch 
feuchte Wärme verursachter Fehlerbilder, z. B. Korrosion, 
Migration/Dendritenwachstum, Aufquellen und Degradation von Kunststoffen, Dicht- 
und Vergussmassen. 
11.14.1.2 Prüfung 
Durchführung der Prüfung gemäß DIN EN 60068-2-78 mit folgenden Parametern: 
 
Tabelle 75: Prüfparameter K-14 Feuchte Wärme konstant – Schärfegrad 1 
Betriebsart des Prüflings 
Off-Grid Parkenmin 
 
Falls für die Komponente der Betriebsmodus On-Grid-
Parken relevant ist, ist an Stelle der Betriebsart Off-
Grid-Parkenmin mit der Betriebsart On-Grid Parkenmin 
zu prüfen. 
Prüftemperatur 
40 °C 
Luftfeuchtigkeit 
93 % relative Feuchte 
Prüfdauer 
21 Tage 
Anzahl der Prüflinge 
6 
11.14.1.3 Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-03 Parametertest (groß) 
gemäß Kapitel 4.7.3. 
 
Zusätzlich muss alle sieben Tage ein P-01 Parametertest (Funktionstest) gemäß 
Kapitel 4.7.1 durchgeführt werden. 
 
 


### 第 106 页
Seite 106 
VW 80000: 2017-10 
 
 
 
 
 
 
11.14.2 
K-14 b Feuchte Wärme konstant – Schärfegrad 2 
11.14.2.1 Zweck 
Diese Prüfung simuliert gerafft die Belastung der Komponente durch feuchte Wärme 
während der Fahrzeuglebensdauer. 
Sie dient der Absicherung der Qualität und Zuverlässigkeit der Komponente 
hinsichtlich durch feuchte Wärme verursachter Fehlerbilder, z. B. Korrosion, 
Migration/Dendritenwachstum, Aufquellen und Degradation von Kunststoffen, Dicht- 
und Vergussmassen. 
11.14.2.2 Prüfung 
Durchführung der Prüfung gemäß DIN EN 60068-2-78 mit folgenden Parametern: 
 
Tabelle 76: Prüfparameter K-14 Feuchte Wärme konstant – Schärfegrad 2 
Betriebsart des Prüflings Intermittierender Betrieb, jeweils 47 h Off-Grid Parkenmin 
und 1 h Betriebmax wiederholend bis zum Ende der 
Prüfdauer. 
 
Falls für die Komponente der Betriebsmodus On-Grid-
Parken relevant ist, ist an Stelle der Betriebsart Off-Grid-
Parkenmin mit der Betriebsart On-Grid Parkenmin zu prüfen. 
Prüfdauer 
Wie im Lastenheft festgelegt gemäß Kap. F.1 (Lawson-
Modell) 
Prüftemperatur 
65 °C 
Prüffeuchte 
93 % relative Feuchte 
Anzahl der Prüflinge 
6 
 
Bei vorhandenem Kühlmittelkreislauf ist die Kühlmitteltemperatur der jeweiligen 
Prüftemperatur bis zu der Grenze Tkühl,max nachzuführen. Oberhalb der 
Kühlmitteltemperaturgrenze wird nur noch die Umgebungstemperatur variiert. 
 
Vor Durchführung dieses Lebensdauertests ist zu prüfen, ob durch die hohe Raffung 
mit den Prüfparametern 65 °C und 93 % relative Feuchte die physikalischen Grenzen 
der in der Komponenten eingesetzten Materialien überschritten werden (z. B. 
Hydrolyse von Kunststoffen). Gegebenenfalls ist mit dem Auftraggeber die 
Anpassung der Prüftemperatur und Prüffeuchte bei gleichzeitiger Erhöhung der 
Prüfdauer gemäß „Lawson Model“ so abzustimmen (z. B. auf 55 °C und 93 % 
relative Feuchte), dass die physikalischen Grenzen der eingesetzten Materialien bei 
der Prüfung nicht überschritten werden. Dabei muss die Prüfschärfe aber insgesamt 
erhalten bleiben. Die Prüffeuchte darf einen Wert von 93 % relative Feuchte nicht 
übersteigen. 
 
Es ist sicherzustellen, dass während der Prüfung keine Betauung (auch keine lokale 
Betauung) am Prüfling auftritt.  
11.14.2.3 Abweichende Prüfung für Komponenten mit reduzierter 
Performance bei hohen Temperaturen  
Für Komponenten mit reduzierter Performance (z. B. Reduktion der 
Hintergrundbeleuchtung von LCDs) bei hohen Temperaturen ab Top,max (Top, max < 65 


### 第 107 页
 
  
Seite 107 
VW 80000: 2017-10 
 
 
 
 
 
 
 
°C) ist die Prüfung abweichend von Tabelle 76 nicht bei einer konstanten Temperatur 
von 65 °C, sondern mit folgenden Parametern durchzuführen (siehe Tabelle 77). 
 
Tabelle 77: Prüfparameter K-14 Feuchte Wärme konstant für Komponenten mit reduzierter 
Performance bei hohen Temperaturen 
Betriebsart des 
Prüflings 
Intermittierender Betrieb gemäß Bild 40 
Prüfdauer 
Wie im Lastenheft festgelegt gemäß Kap. F.1 (Lawson-
Modell). 
Die jeweiligen Rampenzeiten zwischen 65 °C und Top, max 
werden nicht auf die Prüfdauer angerechnet. 
Prüftemperatur 
Gemäß Bild 40 
Der Temperaturgradient ist so zu wählen, dass keine Betauung 
am Prüfling auftritt. 
Prüffeuchte 
93 % relative Feuchte 
Intervallzeit t1 
47 h 
Intervallzeit t2 
1 h 
Anzahl der 
Prüflinge 
6 
 
Bei vorhandenem Kühlmittelkreislauf ist die Kühlmitteltemperatur der jeweiligen 
Prüftemperatur bis zu der Grenze Tkühl,max nachzuführen. Oberhalb der 
Kühlmitteltemperaturgrenze wird nur noch die Umgebungstemperatur variiert. 
 
  
Bild 40: Temperaturprofil für die Prüfung von Komponenten mit reduzierter Performance bei 
hohen Temperaturen größer Top,max 


### 第 108 页
Seite 108 
VW 80000: 2017-10 
 
 
 
 
 
 
11.14.2.4 Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-03 Parametertest (groß) 
gemäß Kapitel 4.7.3. 
11.15 
K-15 Betauungs- und Klimaprüfung 
11.15.1 
K-15 a Betauungsprüfung mit Baugruppen 
11.15.1.1 Zweck 
Diese Prüfung simuliert die Betauung an elektronischen Baugruppen in 
Kraftfahrzeugen. 
Sie dient der Bewertung der Robustheit der elektronischen Baugruppe gegenüber 
Betauung. 
11.15.1.2 Prüfung 
Durchführung der Prüfung mit Baugruppen ohne Gehäuse mit folgenden 
Parametern: 
 
Tabelle 78: Prüfparameter K-15 a Betauungsprüfung mit Baugruppen 
Betriebsart des 
Prüflings 
Off-Grid Parkenmin 
Zusätzlich müssen P-01 Parametertests (Funktionstest) wie in der 
Zeile „Durchführung der Prüfung“ beschrieben durchgeführt 
werden.  
Prüfeinrichtung 
Klimakammer mit Betauungsoption (speziell geregeltes 
Wasserbad, mit dem die nötige Wassermenge in Wasserdampf 
umgesetzt wird). 
Während der Betauungsphase ist die Klimaregelung abgeschaltet. 
Die Prüfraumtemperatur wird über das temperierte Wasserbad 
geregelt. 
Durchführung 
der Prüfung 
1. Der Klimakammer verharrt für 60 min auf der 
Anfangstemperatur, um sicherzustellen, dass der Prüfling 
durchtemperiert ist. Anschließend beginnt die 
Betauungsphase. 
2. Im Bereich von 30 min nach Beginn bis 30 min vor Ende 
der Betauungsphase (nach Bild 43) wird bei jeder 
Wasserbad-Temperaturerhöhung um 10 K ein P-01 
Parametertest (Funktionstest), jedoch nur bei Spannung UB, 
durchgeführt. 
Der P-01 Parametertest (Funktionstest) ist mit möglichst 
geringer Verlustleistung für max. 2 min durchzuführen, da 
sich der Prüfling sonst zu stark erwärmt und keine 
Betauung mehr gegeben ist. 
Prüftemperatur  
Siehe Bild 43 


### 第 109 页
 
  
Seite 109 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Relative 
Prüfraumfeuchte 
Siehe Bild 43 
Während der Betauungsphase muss die relative Prüfraumfeuchte 
100 % r. H. (0 %, -5 %) betragen. 
Prüfdauer 
32,5 h (5 Zyklen à 6,5 h) 
Prüfmedium 
Destilliertes Wasser mit einer max. Leitfähigkeit von 5 µS/cm 
Lage des 
Prüflings 
Einbaulage wie im Fahrzeug.  
Zur Einhaltung der Einbaulage der Baugruppe in der 
Klimakammer sind Kunststoffhalter zu verwenden. 
Wird die Baugruppe in verschiedenen Einbaulagen genutzt, 
müssen die Prüflinge auch in unterschiedlichen Einbaulagen in 
der Klimakammer positioniert werden. 
Prüfaufbau 
Siehe Bild 41 
Während der Prüfung ist eine Kunststoffhaube nach Bild 42 zu 
verwenden, um bei unterschiedlichen Luftgeschwindigkeiten 
unerwünschte Effekte zu eliminieren. Die Haube muss so 
orientiert werden, dass die Schräge zur Prüfraumtür weist. 
Die Abmessungen der Kunststoffhaube sind an die 
Prüfraumgröße anzupassen. 
Der Abstand der Kunststoffhaube zur Prüfraumwand beträgt 10 % 
der Prüfraumbreite / -tiefe, jedoch mindestens 8 cm.  
Nach DIN EN ISO 6270-2 ist für die Dachneigung der 
Kunststoffhaube ein Winkel α von ≥ 12 ° zu verwenden. 
Prüfbedingung 
Die Betauungsprüfung muss erstmals vor der finalen Festlegung 
des Schaltungslayouts (Hardware Freeze), aber bereits an unter 
seriennahen Bedingungen gefertigten Baugruppen durchgeführt 
werden, um festgestellte Betauungsempfindlichkeiten zu 
optimieren, z. B. durch Layout- und Schaltungsänderungen. 
Bei Änderungen in der Baugruppenfertigung (z. B. 
Schaltungsträger, Lot, Flussmittel, Lötprozess, Layout, 
Standortverlagerung oder Bauelemente) muss die Prüfung erneut 
durchgeführt werden. 
Anzahl der 
Zyklen 
5 
Anzahl der 
Prüflinge 
6 Baugruppen 


### 第 110 页
Seite 110 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Prüfliing
Heizung
Wärmetauscher
(Kühlung)
Temperaturfühler /
- regler
Prüfraum
Prüfschranktür
 
Bild 41: Prüfaufbau K-15 a Betauungsprüfung mit Baugruppen 
α
 
Bild 42: Kunststoffhaube 


### 第 111 页
 
  
Seite 111 
VW 80000: 2017-10 
 
 
 
 
 
 
 
10
20
30
40
50
60
70
80
90
100
0
°C
%

1 Zyklus
Betauungsphase  **)
30
30
30
30
30
150
75
  Zeit (t) in min
1)
2)
Parametertest (Funktionstest) bei UB
Wasserbadtemperatur  1 K
Prüfraumtemperatur  3 K
3)
Feuchteverlauf nicht definiert
Prüfraumfeuchte
Wasserbadtemperatur < 20°C
**)     Aufzeichung der Prüfraumfeuchte und -temperatur, Temperaturdifferenz     < 15 °C
1)  Start der Trocknungsphase nach Erreichen
     von 75 °C Lufttemperatur
2)  Prüfling muss trocken sein   Frel  < 50 %
3)  Umschaltung von Klimaregelung auf 
     Wasserbadregelung
Trocknungsphase
15
 
Bild 43: Ablauf K-15 a Betauungsprüfung mit Baugruppen 
11.15.1.3 Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-03 Parametertest (groß) 
gemäß Kapitel 4.7.3. 
Zusätzlich muss die Baugruppe auf elektrochemische Migration (z.B. Ansätze von 
Silber- und Zinn-Wanderung) und Dendritenwachstum untersucht werden.  
Eine Bildung von elektrochemischer Migration/Dendritenwachstum ist nicht zulässig. 
Sonstige Veränderungen der Baugruppe (z. B. Korrosion, Kontamination) sind im 
Prüfbericht zu dokumentieren und mit dem Auftraggeber zu bewerten. 
Dem Prüfbericht müssen folgende Dokumentationen beigefügt werden: 
 
 1. Programmierung des Prüfschrankes 
 2. Parameter (Soll / Ist) eines Zyklus  
 3. Parameter (Soll / Ist) aller fünf Zyklen 
Beispiele siehe Anhang G. 
 


### 第 112 页
Seite 112 
VW 80000: 2017-10 
 
 
 
 
 
 
11.15.2 
K-15 b Klimaprüfung für Komponenten mit wasserdichten 
Gehäusen 
11.15.2.1 Zweck 
Diese Prüfung simuliert gerafft die Belastung der Komponente durch feuchte Wärme 
unter Berücksichtigung der Schutzwirkung von wasserdichten Gehäusen während 
der Fahrzeuglebensdauer. 
Sie dient der Absicherung der Qualität und Zuverlässigkeit der Komponente 
hinsichtlich durch feuchte Wärme verursachter Fehlerbilder, z. B. Korrosion, 
Migration/Dendritenwachstum, Aufquellen und Degradation von Kunststoffen, Dicht- 
und Vergussmassen. 
11.15.2.2 Prüfung 
Die Prüfung muss mit kompletten Komponenten (Gerät, Steuergerät, Mechatronik, … 
mit Gehäuse) durchgeführt werden. 
 
Die Prüfung muss als Sequenz von fünf Prüfblöcken durchgeführt werden gemäß 
Bild 44: 
 
 
Bild 44: Ablauf K-15 b Klimaprüfung für Komponenten mit wasserdichten Gehäusen 
 
Prüfblöcke 1, 3 und 5: 
Durchführung der Prüfung gemäß DIN EN 60068-2-78 mit folgenden Parametern: 
 
Tabelle 79: Prüfparameter K-15 b Klimaprüfung für Komponenten mit wasserdichten Gehäusen  
Prüfblöcke 1, 3 und 5 
Betriebsart des 
Prüflings 
Off-Grid Parkenmin 
12 Stunden nach Beginn des Prüfblocks und in der Folge jeweils 
nach 24 Stunden muss ein P-01 Parametertest (Funktionstest) 
durchgeführt werden. 
Falls für die Komponente der Betriebsmodus On-Grid-Parken 
relevant ist, ist an Stelle der Betriebsart Off-Grid-Parkenmin mit der 
Betriebsart On-Grid Parkenmin zu prüfen. 
Prüfblock 1
Feuchte Wärme
konstant
Prüfblock 3
Feuchte Wärme
konstant
Prüfblock 4
Feuchte Wärme 
zyklisch,
mit Frost
Prüfblock 2
Feuchte Wärme 
zyklisch,
 mit Frost
Prüfblock 5
Feuchte Wärme
Konstant
t


### 第 113 页
 
  
Seite 113 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Prüfdauer je 
Prüfblock 
Wie im Lastenheft festgelegt. 
Hinweis: 
Die Gesamtprüfdauer der Prüfung K-15 b (Prüfblöcke 1 bis 5) 
entspricht der Prüfdauer der Prüfung K-14 b Feuchte Wärme 
konstant – Schärfegrad 2. 
Davon entfallen jeweils 240 Stunden Prüfdauer auf die Prüfblöcke 
2 und 4. 
Die restliche Prüfdauer entfällt jeweils zu einem Drittel auf die 
Prüfblöcke 1, 3 und 5: 
PrüfdauerPrüfblock 1 = PrüfdauerPrüfblock 3 = PrüfdauerPrüfblock 5 
= 1/3 (Gesamtprüfdauer -2*240 Stunden).  
Prüftemperatur 
65 °C 
Prüffeuchte 
93 % relative Feuchte 
Anzahl der 
Prüflinge 
6 
 
Bei vorhandenem Kühlmittelkreislauf ist die Kühlmitteltemperatur der jeweiligen 
Prüftemperatur bis zu der Grenze Tkühl,max nachzuführen. Oberhalb der 
Kühlmitteltemperaturgrenze wird nur noch die Umgebungstemperatur variiert. 
 
Prüfblöcke 2 und 4: 
Durchführung der Prüfung gemäß DIN EN 60068-2-38 mit folgenden Parametern: 
 
Tabelle 80: Prüfparameter K-15 b Klimaprüfung für Komponenten mit wasserdichten Gehäusen  
Prüfblöcke 2 und 4 
Betriebsart des 
Prüflings 
Off-Grid Parkenmin 
12 Stunden nach Beginn des Prüfblocks und in der Folge jeweils 
nach 24 Stunden muss ein P-01 Parametertest (Funktionstest) 
durchgeführt werden. 
Falls für die Komponente der Betriebsmodus On-Grid-Parken 
relevant ist, ist an Stelle der Betriebsart Off-Grid-Parkenmin mit der 
Betriebsart On-Grid Parkenmin zu prüfen. 
Prüfdauer je 
Prüfblock 
240 h 
Anzahl der 
Zyklen 
10 
Abfolge der 
Prüfzyklen 
Die ersten fünf Zyklen müssen mit Kältephase und die restlichen 
Zyklen müssen ohne Kältephase durchgeführt werden. 
Anzahl der 
Prüflinge 
6 
 


### 第 114 页
Seite 114 
VW 80000: 2017-10 
 
 
 
 
 
 
Bei vorhandenem Kühlmittelkreislauf ist die Kühlmitteltemperatur der jeweiligen 
Prüftemperatur bis zu den Grenzen Tkühl,min und Tkühl,max nachzuführen. Außerhalb der 
Kühlmitteltemperaturgrenzen wird nur noch die Umgebungstemperatur variiert. 
 
11.15.2.3 Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt 
durch kontinuierliche Parameterüberwachung und einen P-03 Parametertest (groß) 
gemäß Kapitel 4.7.3. 
11.16 
K-16 Temperaturschock (ohne Gehäuse) 
11.16.1 
Zweck 
Diese Technologieprüfung simuliert keine reale Beanspruchung. 
Sie dient vielmehr dem Auffinden von Schwachstellen im Bereich der mechanischen 
Verbindungen auf Baugruppen wie z. B. Lötstellen. 
 
Die Prüfung ist ausschließlich mit der Baugruppe der Komponente ohne Gehäuse 
und mechanische Teile durchzuführen. Notwendige Halterungen sind so 
auszuführen, dass keine zusätzlichen mechanischen Spannungen auf die Baugruppe 
einwirken.  
11.16.2 
Prüfung 
Durchführung der Prüfung gemäß DIN EN 60068-2-14 mit folgenden Parametern: 
 
Tabelle 81: Prüfparameter K-16 Temperaturschock (ohne Gehäuse) 
Betriebsart des Prüflings 
Fahrzeugaufbauunverbaut 
Untere Temperatur 
Tmin 
Obere Temperatur 
Tmax 
Haltezeit bei oberer und unterer 
Temperatur 
15 min nach vollständiger Durchtemperierung 
(siehe Absatz 10.3) 
Überführungsdauer 
≤ 10 s 
Anzahl der Zyklen 
300 
Anzahl der Prüflinge 
6 Baugruppen 
11.16.3 
Anforderung 
Der Prüfling muss vor und nach der Prüfung voll funktionsfähig sein und alle 
Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt durch 
einen P-03 Parametertest (groß) gemäß Kapitel 4.7.3. 


### 第 115 页
 
  
Seite 115 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.17 
K-17 Sonnenbestrahlung 
11.17.1 
Zweck 
Diese Prüfung simuliert den Einfluss von Sonnenbestrahlung und UV-Licht auf die 
Komponente. 
Sie dient der Absicherung der Beständigkeit der Komponente gegen Schädigung 
durch Materialermüdung wie z. B. Risse und Verfärbungen. 
11.17.2 
Prüfung 
Durchführung der Prüfung gemäß DIN 75220 mit folgenden Parametern: 
 
Tabelle 82: Prüfparameter K-17 Sonnenbestrahlung 
Betriebsart des 
Prüflings 
Fahrzeugaufbauunverbaut 
Verwendete 
Prüfprofile 
Die Prüfprofile nach DIN 75220 werden abhängig vom 
Einbauraum der Komponente angewendet. 
Komponenten im 
Außenraum 
Verwendung des Profils Z-Out gemäß Tabelle 2 und Tabelle 
5 der DIN 75220 
Komponenten im 
Innenraum 
Verwendung des Profils Z-IN1 nach DIN 75220  
Prüfdauer 
25 Tage (15 Tage trocken, 10 Tage feucht) 
Anzahl der Zyklen 
1 
Anzahl der Prüflinge 
6 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
11.17.3 
Anforderung 
Der Prüfling muss vor und nach der Prüfung voll funktionsfähig sein und alle 
Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt durch 
einen P-03 Parametertest (groß) gemäß Kapitel 4.7.3. 
 
Zusätzlich muss der Prüfling mit bloßem Auge visuell untersucht werden. 
Veränderungen oder Beschädigungen sind im Prüfbericht zu dokumentieren und mit 
dem Auftraggeber zu bewerten. 


### 第 116 页
Seite 116 
VW 80000: 2017-10 
 
 
 
 
 
 
11.18 
K-18 Schadgasprüfung 
11.18.1 
Zweck 
Diese Prüfung simuliert den Einfluss von Schadgasen auf die Komponente, vor allem 
auf deren Steckerkontakte und Schalter. 
Sie dient der Absicherung der Beständigkeit der Komponente gegenüber 
Fehlerbildern wie z. B. Korrosion und Bauteilbeschädigungen. 
11.18.2 
Prüfung 
Durchführung der Prüfung gemäß DIN EN 60068-2-60 Methode 4 mit folgenden 
Parametern: 
Tabelle 83: Prüfparameter K-18 Schadgasprüfung 
Betriebsart des Prüflings 
Fahrzeugaufbaumontage 
Temperatur 
TRT 
Luftfeuchtigkeit 
75 % relative Feuchte 
Schadgaskonzentration 
SO2 
0,2 ppm 
H2S 
0,01 ppm 
NO2 
0,2 ppm 
Cl2 
0,01 ppm 
Prüfdauer 
21 Tage 
Anzahl der Prüflinge 
6 
11.18.3 
Anforderung 
Der Prüfling muss vor und nach der Prüfung voll funktionsfähig sein und alle 
Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt durch 
einen P-03 Parametertest (groß) gemäß Kapitel 4.7.3. 
 
Zusätzlich sind die Übergangswiderstände von Schaltern und Kontakten zu 
vermessen. Die Messwerte müssen innerhalb der Spezifikation liegen. 


### 第 117 页
 
  
Seite 117 
VW 80000: 2017-10 
 
 
 
 
 
 
 
12 Chemische Anforderungen und Prüfungen 
12.1 C-01 Chemische Prüfungen 
12.1.1 
Zweck 
Diese Prüfung simuliert die Beanspruchung der Komponente mit verschiedenen 
Chemikalien. 
Sie dient der Absicherung der Komponente gegenüber chemischen Veränderungen 
am Gehäuse und Beeinträchtigung der Funktion durch chemische Reaktionen. 
12.1.2 
Prüfung 
Tabelle 84: Prüfparameter Chemische Prüfungen 
Betriebsart des Prüflings 
Wie im Lastenheft festgelegt. 
Chemikalien 
Wie im Lastenheft festgelegt. 
Typische Chemikalien für die unterschiedlichen 
Einbauräume sind in Tabelle 85 angegeben. 
Konditionierung 
Wenn nicht anders spezifiziert, sind der Prüfling und 
die Chemikalie bei Normalklima zu lagern. 
 
Durchführung der Prüfung 
Die Prüfung erfolgt in Anlehnung an  
ISO 16750 Teil 5: 
1. Die Chemikalie muss bei TRT auf den Prüfling 
appliziert werden. Falls im Lastenheft nicht 
anders festgelegt, muss für jede Chemikalie 
eine geeignete Applikationsart gemäß Tabelle 
86 ausgewählt werden. Die ausgewählte 
Applikationsart ist im Prüfbericht zu 
dokumentieren.  
Es muss sichergestellt werden, dass der 
Prüfling äußerlich an allen verwendeten 
Materialien, Materialgrenzflächen (z. B. 
Dichtungen, Materialübergänge,…) und 
Labels ausreichend mit der Chemikalie 
bedeckt ist. 
2. Anschließend muss der Prüfling bei der in 
Tabelle 85 angegebenen Temperatur für die 
angegebene Wirkdauer gelagert werden. 
Anzahl der Prüflinge 
1 Prüfling pro Chemikalie. 
Mehrfachnutzung eines Prüflings für mehrere 
Chemikalien ist in Absprache mit dem Auftraggeber 
möglich 
 
Sicherheits- und Warnhinweise für die Chemikalien müssen beachtet werden. 
 
 


### 第 118 页
Seite 118 
VW 80000: 2017-10 
 
 
 
 
 
 
12.1.2.1 
Chemikalien 
Tabelle 85: Übersicht Chemikalien (siehe auch ISO 16750- 5)  
ID 
Chemikalie 
Temperatur des 
Prüflings 
Wirkdauer 
Beschreibung/ 
Verweis 
1 
Diesel 
Tmax 
22 h 
EN 590 
2 
"Bio" Diesel 
Tmax 
22 h 
EN 14214 
3 
Benzin, bleifrei 
TRT 
10 min 
EN 228 
4 
Kerosin 
TRT 
10 min 
ASTM 1655 
5 
Methanol 
TRT 
10 min 
CAS 67-56-1 
6 
Motoröl 
Tmax 
22 h 
Multigrade oil SAE 0W40, API SL/CF 
7 
Differential Öl 
Tmax 
22 h 
Hypoid gear oil SAE 75W140, API GL-5 
8 
Getriebeöl 
Tmax 
22 h 
ATF Dexron III 
9 
Hydraulikflüssigkeit 
Tmax 
22 h 
DIN 51 524-3 (HVLP ISO VG 46) 
10 
Fett 
Tmax 
22 h 
DIN 51 502 (KP2K-30) 
11 
Silikon öl 
Tmax 
22 h 
CAS 63148-58-3 (AP 100) 
12 
Batteriesäure 
TRT 
22 h 
37 % H2SO4 
13 
Bremsflüssigkeit 
Tmax 
22 h 
ISO 4926 
14 
Frostschutzmittel 
Tmax 
22 h 
Ethylenglycol (C2H6O2) – Wasser (Mischverhältnis 1:1) 
15 
Harnstoff 
Tmax 
22 h 
ISO 22241-1 
16 
Hohlraumschutz 
TRT 
22 h 
z. B. Unterbodenschutz, Fa. Teroson 1 
17 
Konservierungsmittel 
TRT 
22 h 
z. B. W550 (Fa. Pfinder) 1 
18 
Entkonservierungsmittel 
Tmax 
22 h 
z. B. Friapol 750 (Fa. Pfinder) 1 
19 
Windschutzscheibenreiniger 
TRT  
 
2 h 
5 % Anionische Tenside, destilliertes Wasser 
 
20 
Automotive Waschchemikalien 
TRT 
2 h 
CAS 25155-30-0 
CAS 9004-82-4 
21 
Innenreiniger / Cockpit-Spray 
TRT 
2 h 
z. B. Cockpit-spray (Fa. Motip) 1 
22 
Glasreiniger 
TRT 
2 h 
CAS 111-76-2 
23 
Felgenreiniger 
TRT 
2 h 
z. B. Xtreme (Sonax) 1 
24 
Kaltreiniger 
TRT 
22 h 
z. B. P3-Solvclean AK (Fa. Henkel) 1 
25 
Azeton 
TRT 
10 min 
CAS 67-64-1 
26 
Waschbenzin 
TRT 
10 min 
DIN 51 635 
27 
Ammoniakhaltiger Reiniger 
TRT 
22 h 
z. B. Ajax (Fa. Henkel) 1 
 
28 
Spiritus 
TRT 
10 min 
CAS 64-17-5 (Ethanol) 
29 
Kontaktspray 
Tmax 
22 h 
z. B. WD 40 1 
30 
Schweiß 
TRT 
22 h 
DIN 53 160 
31 
Kosmetische Produkte z. B. Creme 
TRT 
22 h 
z. B. Nivea, Kenzo 1 
32 
Koffein- und zuckerhaltiges Erfrischungsgetränk 
TRT 
22 h 
Cola 
 
33 
Enteiser (Luftfahrt) 
TRT 
2 h 
SAE AMS 1435A 
34 
E85 Kraftstoff  
TRT 
10 min 
DIN 51625 
 
Sonstige Chemikalien 
 
 
 
 
1) Beispielhafter Hersteller, genaue Chemikalien sind mit der Fachabteilung abzustimmen 
 


### 第 119 页
 
  
Seite 119 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Tabelle 86: Applikationsarten 
Kennzahl 
Applikationsmethode 
I  
Sprühen 
II  
Einpinseln 
III  
Wischen (z. B. mit Baumwolltuch) 
IV  
Übergießen 
V  
Kurzes Eintauchen 
VI 
Eintauchen 
12.1.3 
Anforderung 
Der Prüfling muss vor und nach der Prüfung voll funktionsfähig sein und alle 
Parameter müssen innerhalb der Spezifikation liegen. Der Nachweis erfolgt durch 
einen P-03 Parametertest (groß) gemäß Kapitel 4.7.3. 
 
Veränderungen an Beschriftungen und Markierungen sind im Prüfbericht zu 
dokumentieren und mit dem Auftraggeber abzustimmen. 


### 第 120 页
Seite 120 
VW 80000: 2017-10 
 
 
 
 
 
 
13 Lebensdauerprüfungen 
13.1 L-01 Lebensdauerprüfung mechanisch/hydraulischer Dauerlauf 
13.1.1 
Zweck 
Diese Prüfung simuliert die Funktions-/Betätigungszyklen der Komponente während 
der Fahrzeuglebensdauer.  
Sie dient der Absicherung der Qualität und Zuverlässigkeit der Komponente 
hinsichtlich der Funktions-Betätigungszyklen wie z. B. Bremsbetätigungen, 
Sitzverstellzyklen, Schalter-/Tasterbetätigungen usw.  
13.1.2 
Prüfung 
Details zur Prüfung sind entsprechend des Funktions-/Betätigungszyklus im 
Lastenheft zu definieren. 
 
Tabelle 87: Prüfparameter L-01 Lebensdauerprüfung mechanisch/hydraulischer Dauerlauf 
Betriebsart des 
Prüflings 
Betriebmax entsprechend Funktions-/Betätigungszyklus 
Prüftemperatur 
Die Funktions-/Betätigungszyklen sind bei den im 
Temperaturkollektiv angegebenen Temperaturen, zeitlich 
entsprechend ihrem prozentualen Anteil (siehe Anhang C), 
durchzuführen. 
 
Dabei sind mindestens zwei Temperaturrampen gemäß 
Anhang C zu durchfahren. 
Anzahl der Funktions-
/Betätigungszyklen 
Wie im Lastenheft festgelegt. 
Anzahl der Prüflinge 
6 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
 
Bei vorhandenem Kühlmittelkreislauf ist die Kühlmitteltemperatur der jeweiligen 
Prüftemperatur bis zu den Grenzen Tkühl,min und Tkühl,max nachzuführen. Außerhalb der 
Kühlmitteltemperaturgrenzen wird nur noch die Umgebungstemperatur variiert. 
13.1.3 
Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Schlüsselparameter müssen innerhalb der Spezifikation liegen. Der Nachweis 
muss durch eine kontinuierliche Parameterüberwachung erbracht werden. 
Zwischenmessungen bei 25 %, 50 % und 75 % der Prüfdauer und Parametertests 
gemäß Prüfablaufplan sind nur durchzuführen, wenn die Funktionen der 
Komponente während der Prüfung nicht in ausreichender Tiefe überwachbar sind. 
 
Die Zwischenmessungen müssen als P-03 Parametertest (groß) durchgeführt 
werden. 
 


### 第 121 页
 
  
Seite 121 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Die Daten der kontinuierlichen Parameterüberwachung müssen auf Driften, Trends 
und auffälliges Verhalten oder Anomalien bewertet werden. 
 
Für Komponenten an Kühlmittelkreisläufen: 
Für Komponenten mit beschichteten Kupferteilen im Kühlmittelpfad müssen diese 
Kupferteile nach der Prüfung mit einem Stereomikroskop bei 20-facher Vergrößerung 
untersucht werden. Dabei erkennbare Fehlstellen und Kupferkorrosion sind nicht 
zulässig. 
13.2 L-02 Lebensdauerprüfung Hochtemperaturdauerlauf 
13.2.1 
Zweck 
Diese Prüfung simuliert gerafft die thermische Beanspruchung der Komponente 
während des elektrischen Betriebs über die Fahrzeuglebensdauer. 
Sie dient der Absicherung der Qualität und Zuverlässigkeit der Komponente 
hinsichtlich thermisch bedingter Fehlerbilder wie z. B. Diffusion, Migration und 
Oxidation. 
13.2.2 
Prüfung 
13.2.2.1 
Prüfung für Komponenten ohne Anbindung an den 
Kühlmittelkreislauf ohne reduzierte Performance bei hohen 
Temperaturen 
Durchführung der Prüfung gemäß DIN EN 60068-2-2 mit folgenden Parametern: 
Tabelle 88: Prüfparameter L-02 Lebensdauerprüfung Hochtemperaturdauerlauf - Prüfung für 
Komponenten ohne Anbindung an den Kühlmittelkreislauf ohne reduzierte Performance bei 
hohen Temperaturen 
Betriebsart des 
Prüflings 
Intermittierend, 47 h Betriebmax und 1 h Off-Grid-Parkenmin. 
In der Betriebsart Betriebmax muss die Komponente 
intermittierend in allen relevanten Betriebsarten mit hoher 
Betriebslast betrieben werden. Das Verhältnis der Zeitanteile 
dieser Betriebsarten muss dabei dem Verhältnis der 
jeweiligen Teilprüfdauern entsprechen.  
Prüfdauer 
Für jeden relevanten Betriebsmodus gemäß Kapitel 4.3 muss 
die jeweilige Teilprüfdauer gemäß Anhang D.2 (Arrhenius-
Modell) berechnet werden. Die gesamte Prüfdauer ist die 
Summe aller Teilprüfdauern.  
Prüftemperatur 
Tmax 
Anzahl der Prüflinge 
6  
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
 
 


### 第 122 页
Seite 122 
VW 80000: 2017-10 
 
 
 
 
 
 
13.2.2.2 
Prüfung für Komponenten ohne Anbindung an den 
Kühlmittelkreislauf mit reduzierter Performance bei hohen 
Temperaturen 
Für Komponenten mit reduzierter Performance (z. B. Reduktion der 
Hintergrundbeleuchtung von LCDs) bei hohen Temperaturen ab Top,max ist die 
Prüfung gemäß Tabelle 89 nicht bei einer konstanten Prüftemperatur von Tmax, 
sondern mit einem Temperaturprofil mit folgenden Parametern durchzuführen: 
 
Durchführung der Prüfung gemäß DIN EN 60068-2-2 mit folgenden Parametern: 
Tabelle 89: Prüfparameter L-02 Lebensdauerprüfung Hochtemperaturdauerlauf -  
Betriebsart des 
Prüflings 
Intermittierend gemäß Bild 45 
In der Betriebsart Betriebmax und Betriebmax* muss die 
Komponente intermittierend in allen relevanten Betriebsarten 
mit hoher Betriebslast betrieben werden. Das Verhältnis der 
Zeitanteile dieser Betriebsarten muss dabei dem Verhältnis 
der jeweiligen Teilprüfdauern entsprechen. 
Prüfdauer 
Für jeden relevanten Betriebsmodus gemäß Kapitel 10.1 
muss die jeweilige Teilprüfdauer gemäß Anhang D.4 
(Arrhenius-Modell) berechnet werden. Die gesamte Prüfdauer 
ist die Summe aller Teilprüfdauern. 
Die jeweiligen Rampenzeiten zwischen Tmax und Top, max 
werden nicht auf die Prüfdauer angerechnet. 
Prüftemperatur 
gemäß Bild 45 
Intervallzeit t1 
Gemäß Anhang D.4 zu berechnen und im Lastenheft 
festzulegen. 
Intervallzeit t2 
Gemäß Anhang D.4 zu berechnen und im Lastenheft 
festzulegen. 
Anzahl der Prüflinge 
6 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
 


### 第 123 页
 
  
Seite 123 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Bild 45: Temperaturprofil für die Prüfung von Komponenten mit reduzierter Performance bei 
hohen Temperaturen.  
13.2.2.3 
Prüfung für Komponenten mit Anbindung an einen 
Kühlmittelkreislauf 
 
Durchführung der Prüfung gemäß DIN EN 60068-2-2 mit folgenden Parametern: 
 
 Tabelle 90: Prüfparameter L-02 Lebensdauerprüfung Hochtemperaturdauerlauf - Prüfung für 
Komponenten mit Anbindung an einen Kühlmittelkreislauf 
Betriebsart des 
Prüflings 
Intermittierend, 47 h Betriebmax und 1 h Off-Grid-Parkenmin. 
In der Betriebsart Betriebmax muss die Komponente 
intermittierend in allen relevanten Betriebsarten mit hoher 
Betriebslast betrieben werden. Das Verhältnis der Zeitanteile 
dieser Betriebsarten muss dabei dem Verhältnis der 
jeweiligen Teilprüfdauern entsprechen. 
Prüfdauer 
Für jeden relevanten Betriebsmodus gemäß Kapitel 4.3 muss 
die jeweilige Teilprüfdauer gemäß Anhang D.6 (Arrhenius-
Modell) berechnet werden. Die gesamte Prüfdauer ist die 
Summe aller Teilprüfdauern. 
Prüftemperatur  
Umgebung 
gemäß Anhang D.6 (Arrhenius-Modell zur Verwendung bei 
Komponenten an Kühlmittelkreisläufen) 
Prüftemperatur 
Kühlmittel 
gemäß Anhang D.6 (Arrhenius-Modell zur Verwendung bei 
Komponenten an Kühlmittelkreisläufen) 
Anzahl der Prüflinge 
6  
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 


### 第 124 页
Seite 124 
VW 80000: 2017-10 
 
 
 
 
 
 
13.2.3 
Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Schlüsselparameter müssen innerhalb der Spezifikation liegen. Der Nachweis 
muss durch eine kontinuierliche Parameterüberwachung erbracht werden. 
Zwischenmessungen bei 25 %, 50 % und 75 % der Prüfdauer und Parametertests 
gemäß Prüfablaufplan sind nur durchzuführen, wenn die Funktionen der 
Komponente während der Prüfung nicht in ausreichender Tiefe überwachbar sind. 
 
Die Zwischenmessungen müssen als P-03 Parametertest (groß) durchgeführt 
werden. 
 
Die Daten der kontinuierlichen Parameterüberwachung müssen auf Driften, Trends 
und auffälliges Verhalten oder Anomalien bewertet werden. 
 
Für Komponenten an Kühlmittelkreisläufen: 
Für Komponenten mit beschichteten Kupferteilen im Kühlmittelpfad müssen diese 
Kupferteile nach der Prüfung mit einem Stereomikroskop bei 20-facher Vergrößerung 
untersucht werden. Dabei erkennbare Fehlstellen und Kupferkorrosion sind nicht 
zulässig. 
 
 


### 第 125 页
 
  
Seite 125 
VW 80000: 2017-10 
 
 
 
 
 
 
 
13.3 L-03 Lebensdauerprüfung Temperaturwechseldauerlauf 
13.3.1 
Zweck 
Diese Prüfung simuliert gerafft die thermomechanische Beanspruchung der 
Komponente durch Temperaturwechsel während der Fahrzeuglebensdauer. 
Sie dient der Absicherung der Qualität und Zuverlässigkeit der Komponente 
hinsichtlich thermomechanisch bedingter Fehlerbilder, z. B. Alterung und Rissbildung 
in Löt-, Klebe-, Bond- und Schweißverbindungen, an Dichtungen und Gehäusen. 
13.3.2 
Prüfung 
Durchführung der Prüfung gemäß DIN EN 60068-2-14 mit folgenden Parametern: 
 
13.3.2.1 
Prüfung für Komponenten ohne Anbindung an den 
Kühlmittelkreislauf ohne reduzierte Performance bei tiefen oder 
hohen Temperaturen 
 
Tabelle 91: Prüfparameter L-03 Lebensdauerprüfung Temperaturwechseldauerlauf - Prüfung 
für Komponenten ohne Anbindung an den Kühlmittelkreislauf ohne reduzierte Performance bei 
tiefen oder hohen Temperaturen 
Betriebsart des 
Prüflings 
Intermittierend, Betriebmax und Off-Grid-Parkenmin  
gemäß Bild 46. 
Temperaturprofil 
Gemäß Bild 46. 
Minimale 
Prüftemperatur 
Tmin  
Maximale 
Prüftemperatur 
Tmax  
Temperaturgradient 
4 °C/min 
Falls der Temperaturgradient in der Prüfeinrichtung nicht 
realisierbar ist, kann der Temperaturgradient in Abstimmung 
mit dem Auftraggeber auf Werte bis minimal 2 °C/min 
reduziert werden. 
Haltezeiten bei 
Tmin und Tmax 
15 min nach vollständiger Durchtemperierung (siehe Kapitel 
0) 
Anzahl der Zyklen 
Die gesamte Prüfzyklenzahl ist unter Berücksichtigung aller 
relevanter Betriebsmodi (Kapitel 4.3) gemäß Anhang E.1 
(Coffin-Manson-Modell) zu berechnen. 
Anzahl der Prüflinge 
6 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
 


### 第 126 页
Seite 126 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Bild 46: Temperaturprofil L-03 Lebensdauerprüfung Temperaturwechseldauerlauf für 
Komponenten ohne reduzierte Performance bei tiefen oder hohen Temperaturen 
13.3.2.2 
Prüfung für Komponenten ohne Anbindung an den 
Kühlmittelkreislauf mit reduzierter Performance bei tiefen oder 
hohen Temperaturen 
Für Komponenten mit reduzierter Performance (z. B. Reduktion der 
Hintergrundbeleuchtung von LCDs) bei tiefen oder hohen Temperaturen unter Top,min 
bzw. über Top,max ist die Prüfung mit folgenden Parametern durchzuführen: 
 
Tabelle 92: Prüfparameter L-03 Lebensdauerprüfung Temperaturwechseldauerlauf – Prüfung 
für Komponenten ohne Anbindung an den Kühlmittelkreislauf mit reduzierter Performance bei 
tiefen oder hohen Temperaturen 
Betriebsart des Prüflings 
Intermittierend, Off-Grid Parkenmin und Betriebmax gemäß 
Bild 47 
Temperaturprofil 
Gemäß Bild 47 
Minimale Prüftemperatur 
Tmin 
Maximale Prüftemperatur 
Tmax  
Temperaturgradient 
4 °C/min 
  
Haltezeiten bei Tmin, Tmax, 
Top,min und Top,max  
15 min nach vollständiger Durchtemperierung 
(siehe Absatz 0) 
Anzahl der Zyklen 
Die gesamte Prüfzyklenzahl ist unter Berücksichtigung 
aller relevanter Betriebsmodi (Kapitel 4.3) gemäß 
Anhang D.3 (Coffin-Manson-Modell) zu berechnen. 
Anzahl der Prüflinge 
6 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 


### 第 127 页
 
  
Seite 127 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Bild 47: Temperaturprofil – Prüfung für Komponenten mit reduzierter Performance bei tiefen 
oder hohen Temperaturen  
13.3.2.3 
Prüfung für Komponenten am Kühlmittelkreislauf 
Für Komponenten an Kühlmittelkreisläufen ist die Prüfung mit folgenden Parametern 
durchzuführen: 
 
Tabelle 93: Prüfparameter L-03 Lebensdauerprüfung Temperaturwechseldauerlauf – Prüfung 
für Komponenten am Kühlmittelkreislauf 
Betriebsart des 
Prüflings 
Intermittierend, Off-Grid Parkenmin und Betriebmax gemäß 
Bild 46 beziehungsweise Bild 47 
Temperaturprofil 
Gemäß Bild 46 beziehungsweise Bild 47 
Minimale 
Prüftemperatur 
Tmin und Tkühl, min 
Maximale 
Prüftemperatur 
Tmax und Tkühl, max 
Temperaturgradient 
4 °C/min 
  
Haltezeiten bei Tmin, 
Tmax, Top,min und Top,max  
15 min nach vollständiger Durchtemperierung 
(siehe Kapitel 0) 
Anzahl der Zyklen 
Die gesamte Prüfzyklenzahl ist unter Berücksichtigung 
aller relevanter Betriebsmodi (Kapitel 4.3) gemäß Anhang 
D.5 (Coffin-Manson-Modell zur Verwendung bei 
Komponenten an Kühlmittelkreisläufen) zu berechnen. 
Anzahl der Prüflinge 
6 
 
Bei der Durchführung der Prüfung ist die Einbaulage der Komponente im Fahrzeug 
nachzubilden. 
 


### 第 128 页
Seite 128 
VW 80000: 2017-10 
 
 
 
 
 
 
13.3.3 
Anforderung 
Der Prüfling muss vor, während und nach der Prüfung voll funktionsfähig sein und 
alle Schlüsselparameter müssen innerhalb der Spezifikation liegen. Der Nachweis 
muss durch eine kontinuierliche Parameterüberwachung erbracht werden. 
Zwischenmessungen bei 25 %, 50 % und 75 % der Prüfdauer und Parametertests 
gemäß Prüfablaufplan sind nur durchzuführen, wenn die Funktionen der 
Komponente während der Prüfung nicht in ausreichender Tiefe überwachbar sind. 
 
Die Zwischenmessungen müssen als P-03 Parametertest (groß) durchgeführt 
werden. 
 
Die Daten der kontinuierlichen Parameterüberwachung müssen auf Driften, Trends 
und auffälliges Verhalten oder Anomalien bewertet werden. 
 
Für Komponenten an Kühlmittelkreisläufen: 
Für Komponenten mit beschichteten Kupferteilen im Kühlmittelpfad müssen diese 
Kupferteile nach der Prüfung mit einem Stereomikroskop bei 20-facher Vergrößerung 
untersucht werden. Dabei erkennbare Fehlstellen und Kupferkorrosion sind nicht 
zulässig. 
 
 


### 第 129 页
 
  
Seite 129 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Anhang A (normativ) 
 
Prüfablauf 
 
A.1 Prüfablaufplan 
 
Ein komponentenspezifischer Prüfablaufplan ist im Lastenheft zu spezifizieren. 
 
Die Prüfungen, die gemäß Prüfauswahltabelle für eine Komponente nicht 
erforderlich sind, müssen aus dem Prüfablaufplan herausgestrichen werden. 
 
Falls eine komponentenspezifische Anpassung der Testsequenz notwendig ist, kann 
der Prüfablaufplan angepasst werden. 
 
Alle Komponenten werden mit neuen, originalen Kontaktsystemen geprüft. 
Original Stecker/Kabel sind vor Prüfbeginn mit dem Prüfling zu verbinden. Erst nach 
Durchführung aller für die jeweiligen Komponenten erforderlichen Prüfungen dürfen 
diese zur physikalischen Analyse von der Komponente getrennt werden. Prüfungen 
mit Betriebsart Fahrzeugaufbauunverbaut sind davon ausgenommen. 
Der mit dem Kontaktsystem verwendete Leitungssatz muss so ausgelegt sein, dass 
er ohne weitere Zwischenverbindung aus dem Prüfraum herausreicht. 
 
 
 
 


### 第 130 页
Seite 130 
VW 80000: 2017-10 
 
 
 
 
 
 
A.2 Sequenzprüfungen 
 
Sind die Prüflinge aus der Prüfung M-01 Freier Fall nicht beschädigt, müssen zwei 
Prüflinge in der weiteren Sequenzprüfung verwendet werden. Andernfalls müssen 
die Reserveprüflinge verwendet werden. 
 
Alle Komponenten werden ab der Prüfung M-01 „Freier Fall“ mit originalem Stecker 
oder Adapter geprüft.  
 
Dichtheitsprüfung
Tieftemperaturbetrieb
Nachlackiertemperatur
Temperaturschock (Komponente)
Steinschlagprüfung
Salzsprühnebelprüfung mit Betrieb,Aussenraum
Staubprüfung (IP 5KX oder IP 6KX)
Feuchte Wärme, zyklisch (mit Frost)
Mechanischer Dauerschock
Mechanischer Schock
Vibrationsprüfung
Wasserschutz (IP X0 bis IP X6K)
Hochdruck-/Dampfstrahlreinigung IP X9K
Temperaturschock mit Schwallwasser
Temperaturschock Tauchen (IP X7)
+
+
4 (+2 Res.)
2
3
1
6
K-03
K-06
K-05
M-06
M-05
M-04
K-04
M-02
M-03
K-09
K-10
K-11
K-12
K-13
Stufentemperaturtest
K-02
Stufentemperaturtest
K-02
Dichtheitsprüfung
Freier Fall
M-01
Physikalische Analyse
P-04
Legende:
P-02 Parametertest (klein) nach der Prüfung
P-03 Parametertest (groß) nach der Prüfung
Hoch- / Tieftemperaturlagerung
K-01
7
 
Bild 48: Prüfablaufplan – Sequenzablauf 


### 第 131 页
 
  
Seite 131 
VW 80000: 2017-10 
 
 
 
 
 
 
 
A.3 Prüfungen außerhalb der Sequenz (Parallele Prüfungen) 
 
Dichtheitsprüfung
Salzsprühnebelprüfung mit Betrieb, Innenraum
Feuchte Wärme, zyklisch
Feuchte Wärme konstant - Schärfegrad 1
Betauungsprüfung mit Baugruppen
Temperaturschock (ohne Gehäuse)
Sonnenbestrahlung
Schadgasprüfung
Chemische Anforderungen und Prüfungen
K-07
K-16
K-14 a
C-01
K-08
K-15 a
K-17
K-18
Parametertest (groß)
P-03
Dichtheitsprüfung
Parametertest (groß)
P-03
Physikalische Analyse
P-04
Feuchte Wärme konstant - Schärfegrad 2
K-14 b
Klimaprüfung
K-15 b
Druckwechselprüfung
M-07
 2
 6
 6
 6
 6
 6
 6
 6
 6
 6
 X*
*) 1 Prüfling pro Chemikalie
Mehrfachnutzung eines Prüflings für 
mehrere Chemikalien ist in Absprache 
mit dem Auftraggeber möglich.
Hoch- / Tieftemperaturlagerung
K-01
 
Bild 49: Prüfablaufplan - Parallelprüfungen 
 
 


### 第 132 页
Seite 132 
VW 80000: 2017-10 
 
 
 
 
 
 
A.4 Lebensdauerprüfungen 
 
Dichtheitsprüfung
6
Stufentemperaturtest
K-02
Hoch- / Tieftemperaturlagerung
K-01
6
6
+
Stufentemperaturtest
K-02
Dichtheitsprüfung
Physikalische Analyse
P-04
Mechanisch / Hydraulischer 
Dauerlauf
L-01
25 %
50 %
75 %
P-03 Parametertest (groß)
Hochtemperaturdauerlauf
L-02
25 %
50 %
75 %
P-03 Parametertest (groß)
Temperaturwechseldauerlauf
L-03
25 %
50 %
75 %
P-03 Parametertest (groß)
 
  
Bild 50: Prüfablaufplan – Lebensdauer  
 
 


### 第 133 页
 
  
Seite 133 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Anhang B (normativ) 
 
Typische Temperaturkollektive für verschiedene Einbaubereiche 
 
Tabelle 94: Übersicht Einbauräume, typische Kollektive und Temperaturhübe 
Einbauraum der Komponente 
Kollektiv 
Nr. 
Temperaturhub in 
K 
Innenraum, ohne besondere Anforderung 
1 
36 
Karosserieanbau, ohne besondere 
Anforderungen 
1 
36 
Innenraum mit Sonneneinstrahlung 
2 
46 
Karosserieanbau Dach 
2 
46 
Motorraum, aber nicht am Motor 
3 
60 
am Kühler 
3 
60 
Motoranbau 
4 
75 
Getriebeanbau 
4 
75 
 
 


### 第 134 页
Seite 134 
VW 80000: 2017-10 
 
 
 
 
 
 
B.1 Temperaturkollektiv 1 
Tabelle 95: Temperaturkollektiv 1  
Temperatur in °C 
Verteilung in % 
-40 
6 
23  
20 
40  
65  
75  
8  
80  
1 
 
B.2 Temperaturkollektiv 2 
Tabelle 96: Temperaturkollektiv 2  
Temperatur in °C 
Verteilung in % 
-40  
6  
23 
20 
50 
65 
100 
8 
105 
1 
 
B.3 Temperaturkollektiv 3 
Tabelle 97: Temperaturkollektiv 3 
Temperatur in °C 
Verteilung in % 
-40  
6  
23  
20  
65  
65  
115  
8  
120  
1 
 
B.4 Temperaturkollektiv 4 
Tabelle 98: Temperaturkollektiv 4 
Temperatur in °C  Verteilung in % 
-40  
6  
23  
20  
85  
65  
135  
8  
140 
1  
 
 


### 第 135 页
 
  
Seite 135 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Anhang C (normativ)  
 
Berechnung zur Durchführung der Lebensdauerprüfung 
mechanisch/hydraulischer Dauerlauf 
C.1 Berechnung 
 
Für die Berechnung der abzusichernden mechanisch/hydraulischen Funktions-
/Betätigungszyklen wird das verwendete Temperaturkollektiv verwendet. 
 
Tabelle 99: Temperaturkollektiv 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Auf jede Temperatur TFeld, 1 … TFeld, n aus dem verwendeten Temperaturkollektiv 
werden mittels der folgenden Gleichung die abzusichernden 
mechanisch/hydraulischen Funktions-/Betätigungszyklen verteilt. 
 
 
i
Gesamt
T
p
n
n
Feld,i


 (1) 
 
Die mechanisch/hydraulischen Funktions- /Betätigungszyklen sind gemäß Anhang 
C.2, Bild 51: Temperaturverlauf mechanisch/hydraulischer Dauerlauf abzusichern. Es 
sind mindestens 2 Temperaturrampen durchzuführen. Dazu ist die Gleichung (1) wie 
folgt zu ergänzen:  
 
 
rampen
Temperatur
p
n
n
i
Gesamt
T
i
Feld,


 (2) 
 
wobei: 
nTFeld, i 
 
 
Anzahl der mechanisch/hydraulischen Funktions- 
 
 
 
/Betätigungszyklen für die Temperaturstufe TFeld, i 
nGesamt 
 
 
Anzahl der abzusichernden mechanisch/hydraulischen 
 
 
 
Funktions-/Betätigungszyklen 
pi 
 
 
Prozentualer Anteil der mechanisch/hydraulischen Funktions- 
 
 
 
/Betätigungszyklen, mit der die Komponente im Feld bei der  
 
 
 
Temperatur TFeld, i betrieben wird  
Temperaturrampen Anzahl der Temperaturrampen. Es sind mindestens zwei 
Temperaturrampen  durchzuführen 
 
Die Gesamtprüfdauer ergibt sich aus folgenden Gleichungen 
 
Temperatur in °C 
Verteilung in % 
TFeld, 1 
p1 
TFeld, 2 
p2 
TFeld, 3 
p3 
… 
… 
TFeld, n 
pn 


### 第 136 页
Seite 136 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Zyklus
T
Prüf_T
*t
n
t
Feld,i
i
Feld, 
 (3) 
 
 
 


Umtemperierzeit *Temperaturrampen
... t
t
t
Feld,n
Feld,1
Prüf_T
Prüf_T
Prüf_Gesamt




(4) 
wobei: 
tZyklus 
 
 
Zeit für einen abzusichernden mechanisch/hydraulischen  
 
 
 
Funktions-/Betätigungszyklus 
tPrüf_TFeld, i  
 
Prüfdauer für den Temperaturwert TFeld, i  
Umtemperierdauer Zeitdauer für die Umtemperierung zwischen den  
 
 
 
Temperaturwerten für eine Temperaturrampe bei einem  
 
 
 
Temperaturgradienten von 
min
2 C
 
 
Bei einem vorhandenen Kühlmittelkreislauf ist die Kühlmitteltemperatur der jeweiligen  
Prüftemperatur bis zu den Grenzen Tkühl,min und Tkühl,max nachzuführen. Außerhalb der 
Kühlmitteltemperaturgrenzen wird nur noch die Umgebungstemperatur variiert. 
 
 


### 第 137 页
 
  
Seite 137 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
C.2 Beispiel Berechnung 
 
Für ein Steuergerät mit dem in der folgenden Tabelle angegebenen 
Temperaturkollektiv 
 Tabelle 100: Beispieltemperaturkollektiv 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
und 
 
Anzahl der Funktions- /Betätigungszyklen 
100.000 
Zyklusdauer tZyklus  
 
 
 
8 s/Zyklus 
Umtemperierdauer  
 
 
 
240 min 
wird die Prüfdauer für die Lebensdauerprüfung mechanisch/hydraulischer Dauerlauf 
wie folgt berechnet: 
 
Mit Gleichung (1) werden die prozentualen Anteile der Funktions- /Betätigungszyklen 
der Komponente für alle Temperaturen (siehe Tabelle XX) des oben angegebenen 
Temperaturkollektives berechnet. 
 
 
nTFeld,1 = 
nGesamt * p1 
= 
100.000 Zyklen * 0,06 = 
6.000 Zyklen 
 
nTFeld,2 = 
nGesamt * p2 
= 
100.000 Zyklen * 0,20 = 
20.000 Zyklen 
 
nTFeld,3 = 
nGesamt * p3 
= 
100.000 Zyklen * 0,65 = 
65.000 Zyklen 
 
nTFeld,4 = 
nGesamt * p4 
= 
100.000 Zyklen * 0,08 = 
8.000 Zyklen 
 
nTFeld,5 = 
nGesamt * p5 
= 
100.000 Zyklen * 0,01 = 
1.000 Zyklen 
 
Mit Gleichung (2) werden die Teilprüfdauern für die jeweiligen Temperaturen 
berechnet: 
 
tPrüf_TFeld,1 = 
nTFeld1 * tZyklus = 
 6.000 Zyklen * 8 
s
Zyklus = 
48.000 s 
 
tPrüf_TFeld,2 = 
nTFeld2 * tZyklus = 
20.000 Zyklen * 8 
s
Zyklus = 
160.000 s 
 
tPrüf_TFeld,3 = 
nTFeld3 * tZyklus = 
65.000 Zyklen * 8 
s
Zyklus = 
520.000 s 
 
tPrüf_TFeld,4 = 
nTFeld4 * tZyklus = 
 8.000 Zyklen * 8 
s
Zyklus = 
64.000 s 
 
tPrüf_TFeld,5 = 
nTFeld5 * tZyklus = 
 1.000 Zyklen * 8 
s
Zyklus = 
8.000 s 
 
Mit Gleichung (3) wird die Gesamtprüfdauer für die Lebensdauerprüfung 
mechanisch/hydraulischer Dauerlauf berechnet: 
 
tPrüf_Gesamt = 48.000 s + 160.000 s + 520.000 s + 64.000 s + 8.000 s + 240 min * 60 s 
tPrüf_Gesamt = 814.400 s = 13.573 min = 226 
Die Gesamtprüfdauer für die Komponente beträgt tPrüf_Gesamt = 226 
 
Temperatur in °C 
Verteilung in % 
-40 
6 
23 
20 
60 
65 
100 
8 
105 
1 


### 第 138 页
Seite 138 
VW 80000: 2017-10 
 
 
 
 
 
 
-40
23
60
100
105
T
[°C]
t
a
b
c
d
e
1 Temperaturrampe
 
 
Bild 51: Temperaturverlauf mechanisch/hydraulischer Dauerlauf  
  
 


### 第 139 页
 
  
Seite 139 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Anhang D (normativ) 
 
Modelle zur Berechnung der Lebensdauerprüfung 
Hochtemperaturdauerlauf 
D.1 Anpassung der Prüftemperaturen zur Reduzierung der Prüfzeit 
Zur Reduzierung der Prüfdauer für Komponenten, die in mehreren Betriebsmodi aktiv 
betrieben werden, kann die Prüftemperatur Tmax (bzw. TKKL,max) zur Absicherung 
einzelner für die Komponente relevanter Betriebsmodi erhöht werden. Als 
Prüftemperatur kann dazu die absolute Maximaltemperatur Tmax (bzw. TKKL,max) über 
alle für die Komponente relevanten Betriebsmodi angewendet werden. Dabei muss 
die volle Funktionalität der Komponente gegeben sein. Zudem ist darauf zu achten, 
dass unter Berücksichtigung der je nach erforderlichen Betriebsart erzeugten 
Eigenerwärmung der Komponente, alle zur Komponente gehörigen Bauteile, 
Bauelemente und Werkstoffe nicht außerhalb ihrer jeweiligen Spezifikationsgrenzen 
(Temperaturgrenzen) betrieben werden 
 
Eine Anpassung der Prüftemperatur kann für einzelne oder mehrere für die 
Komponente relevante Betriebsmodi erfolgen. Die Prüfdauern müssen jeweils gemäß 
Anhang C berechnet werden.  
 
Details zur Erhöhung der Prüftemperatur und den daraus resultierenden Prüfdauern 
sind zwischen Auftraggeber und Auftragnehmer abzustimmen und zu dokumentieren. 
 
D.2 Arrhenius-Modell 
Für die Berechnung der Prüfdauer der Lebensdauerprüfung 
Hochtemperaturdauerlauf ist das prozentuale Temperaturkollektiv gemäß 
Einsatzprofil aus dem Lastenheft 
Tabelle 101: Temperaturkollektiv 
Temperatur in °C 
Verteilung in % 
TFeld.1 
p1 
TFeld.2 
p2 
… 
… 
TFeld.n 
pn 
 
und die Betriebsdauer tBetrieb des Fahrzeugs im Feld heranzuziehen.  
 
Zu jeder Temperatur TFeld,1 … TFeld,n wird mittels folgender Gleichung ein 
Beschleunigungsfaktor AT,1 … AT,n berechnet: 
 

































273,15
T
1
273,15
T
1
k
E
e
A
Feld,i
Test
A
T,i
 
(1) 
wobei: 
AT,i 
Beschleunigungsfaktor des Arrhenius-Modells 
EA 
Aktivierungsenergie EA = 0,45 eV 
k 
Boltzmann-Konstante (k = 8,617 x 10-5 eV/K) 
TTest 
Prüftemperatur in °C, in der Regel Tmax 


### 第 140 页
Seite 140 
VW 80000: 2017-10 
 
 
 
 
 
 
TFeld,i 
Feldtemperatur in °C nach Temperaturkollektiv gemäß Einsatzprofil 
-273,15 °C Absoluter Nullpunkt der Temperatur 
 
Die Gesamtprüfdauer für die Hochtemperaturdauerlaufprüfung ergibt sich aus den 
Beschleunigungsfaktoren gemäß 
 

i
i
T,
i
Betrieb
Prüf
A
p
t
t
  
 
(2) 
wobei: 
tprüf 
Prüfdauer (Stunden) des Lebensdauerprüfung Hochtemperaturdauerlauf 
 
tBetrieb Betriebsdauer (Stunden) im Feld 
pi 
Prozentualer Anteil der Betriebsdauer, bei der die Komponente im Feld bei 
der Temperatur TFeld,i betrieben wird. 
AT,i 
Beschleunigungsfaktor für Temperatur TFeld,i 
 
 
 
 
 


### 第 141 页
 
  
Seite 141 
VW 80000: 2017-10 
 
 
 
 
 
 
 
D.3 Beispiel Arrhenius-Modell:  
Für ein Steuergerät mit dem in der folgenden Tabelle angegebenen 
Temperaturkollektiv 
 Tabelle 102: Beispielkollektiv 
Temperatur in °C Verteilung in % 
-40 
6 
23 
20 
60 
65 
100 
8 
105 
1 
 
und einer Betriebsdauer von 8 000 h wird die Prüfdauer für den Lebensdauertest 
Hochtemperaturdauerlauf wie folgt berechnet: 
 
Mit Gleichung (1) und TTest = Tmax = 105 °C werden die Beschleunigungsfaktoren AT,i 
für alle fünf Temperaturen (siehe Tabelle 102) des oben angegebenen 
Temperaturkollektivs berechnet: 
 
 
AT,1 = 5369 
AT,2 = 45,8 
AT,3 = 6,46 
AT.4 = 1,20 
AT,5 = 1,00 
 
Die Betriebsdauer der Komponente beträgt tBetrieb = 8 000 h. 
 
Die Gesamtprüfdauer für den Lebensdauertest Hochtemperaturdauerlauf ergibt sich 
mit Gleichung. (2) als: 
 
 
 1452 Stunden
1,00
0,01
1,20
0,08
6,46
0,65
45,8
0,20
5369
0,06
8000 Stunden 
t Prüf
 











. 
 
 


### 第 142 页
Seite 142 
VW 80000: 2017-10 
 
 
 
 
 
 
D.4 Arrhenius-Modell zur Verwendung bei Komponenten mit reduzierter 
Performance bei hohen Temperaturen 
Für die Berechnung der Prüfdauer der Lebensdauerprüfung 
Hochtemperaturdauerlauf für Komponenten mit reduzierter Performance bei hohen 
Temperaturen ab Top,max wird das Temperaturkollektiv gemäß Einsatzprofil im 
Lastenheftes in die beiden Temperaturbereiche T ≤ Top,max und T>Top,max aufgeteilt: 
  
Tabelle 103: Temperaturkollektiv für T ≤ Top,max mit Prüftemperatur Top,max 
Temperatur in °C 
Verteilung in % 
TFeld.1 
p1 
TFeld.2 
p2 
… 
… 
TFeld.m (≤ Top,max) 
pm 
 
Tabelle 104: Temperaturkollektiv für Top,max < T ≤ Tmax mit Prüftemperatur Tmax 
Temperatur in °C 
Verteilung in % 
TFeld.m+1(> Top,max) 
pm+1 
TFeld.m+2 
pm+2 
… 
… 
TFeld.n 
pn 
 
Zu jeder Temperatur TFeld,1 … TFeld,m … TFeld,n wird mittels Gleichung (1) ein 
Beschleunigungsfaktor AT,1 … AT,m … AT,n berechnet, wobei für den 
Temperaturbereich T ≤ Top,max eine Prüftemperatur von TTest=Top, max und für den 
Temperaturbereich T>Top,max eine Prüftemperatur TTest=Tmax angenommen wird. 
 
Die erforderliche Prüfdauer top, max bei Prüftemperatur Top, max ergibt sich gemäß 
Gleichung (2) mit i=1 …m. 
 
Die erforderliche Prüfdauer tmax bei Prüftemperatur Tmax ergibt sich gemäß Gleichung 
(2) mit i=m+1 …n. 
 
Die Gesamtprüfdauer tGes ist die Summe aus top, max und tmax. 
 
Zur realitätsnahen Prüfung wird die Prüfung intermittierend bei den Prüftemperaturen 
Top, max bzw. Tmax durchgeführt (siehe Bild 45). 
Die Intervalldauer von typisch 48 h wird dabei im Verhältnis der Teilprüfdauern top, max 
und tmax aufgeteilt. 
 
 
m < n 
m < n 


### 第 143 页
 
  
Seite 143 
VW 80000: 2017-10 
 
 
 
 
 
 
 
D.5 Beispiel Arrhenius-Modell zur Verwendung bei Komponenten mit 
reduzierter Performance bei hohen Temperaturen:  
Für das Steuergerät gilt das Temperaturkollektiv nach Tabelle 105 und Tabelle 106.  
Bei einer Betriebsdauer von 8 000 h wird die Prüfdauer für den Lebensdauertest 
Hochtemperaturdauerlauf für Komponenten mit reduzierter Performance ab  
Top, max = 90 °C wie folgt berechnet: 
Die prozentualen Temperaturverteilung gemäß Einsatzprofil wird in die beiden 
Bereiche T ≤ Top,max und T>Top,max aufgeteilt: 
 
Tabelle 105: Beispielkollektiv für T ≤ 90 °C 
Temperatur in °C Verteilung in % 
-40 
6 
23 
20 
60 
65 
 
Tabelle 106: Beispielkollektiv für T > 90 °C 
Temperatur in °C Verteilung in % 
100 
8 
105 
1 
 
Mit Gleichung (1)und TTest = 90 °C werden die Beschleunigungsfaktoren AT,i für alle 
Temperaturen T ≤ 90 °C (siehe Tabelle 105) des ersten Teils des 
Temperaturkollektivs berechnet: 
 
AT,1 = 3035,79 
AT,2 = 25,88 
AT,3 = 3,65 
 
Hieraus ergibt sich eine Prüfdauer top, max bei einer Prüftemperatur von Top,max = 90 °C 
von 
 
 
 1487 Stunden
3,65
0,65
25,88
0,2
3035,79
0,06
8000 Stunden 
90 C)
(T
t
Test
op,max
 











 
Mit Gleichung (1) und TTest = 105 °C werden die Beschleunigungsfaktoren AT,i für alle 
Temperaturen T >90 °C (siehe Tabelle 106) des zweiten Teils des 
Temperaturkollektivs berechnet: 
AT.4 = 1,20 
AT,5 = 1,00 
 
Hieraus ergibt sich eine Prüfdauer tmax bei einer Prüftemperatur von Tmax = 105 °C 
von 
 
 
 612 Stunden
1,00
0,01
1,20
0,08
8000 Stunden 
105 C)
(T
t
Test
max
 










 


### 第 144 页
Seite 144 
VW 80000: 2017-10 
 
 
 
 
 
 
Die Gesamt-Prüfdauer für den Lebensdauertest Hochtemperaturdauerlauf ergibt sich 
als Summe der beiden Prüfdauern
 
 
 
 Stunden
2099
612 Stunden
 Stunden
1487
t
t
t
max
op, max
Ges





 
Die Prüfung wird gemäß Bild 45 intermittierend bei den Prüftemperaturen Top, max 
bzw. Tmax durchgeführt mit den Intervallzeiten 
 
 
t1 = 48 h * top,max / tGes = 48 h * 1487/2099 = 34 h 
 
t2 = 48 h * tmax / tGes = 48 h * 612/2099 = 14 h. 
 
 
 


### 第 145 页
 
  
Seite 145 
VW 80000: 2017-10 
 
 
 
 
 
 
 
D.6 Arrhenius-Modell zur Verwendung bei Komponenten an 
Kühlmittelkreisläufen 
 
Bei Komponenten mit Anbindung an den Kühlmittelkreislauf müssen alle relevanten 
Betriebsmodi i (siehe 4.3; i entspricht laufender Nummer der Modi) mit zugehörigen 
Temperaturverteilungen für Umgebung und Kühlmittelkreislauf berücksichtigt werden. 
Für den Lebensdauertest Hochtemperaturdauerlauf müssen für jeden relevanten 
Betriebsmodus i die Prüfdauern und Prüftemperaturen für die Umgebung und den 
Kühlmittelkreislauf berechnet werden wie im Folgenden beschrieben; die 
Gesamtprüfdauer ergibt sich aus der Summe der Prüfdauern für jeden relevanten 
Betriebsmodus i. 
 
Für jeden relevanten Betriebsmodus i müssen zur Berechnung der Prüfdauer für den 
Betriebsmodus i zunächst die Prüfdauer für die Umgebungstemperatur und den 
Kühlmittelkreislauf separat nach dem Arrhenius-Modell gemäß Anhang D.1 bzw. D.4 
berechnet werden. 
Da sich die daraus resultierenden Prüfdauern tprüf, Umgebung und tprüf, KKL in der Regel 
unterscheiden, die Komponente für den jeweiligen Betriebsmodus i aber nur bei einer 
einheitlichen Prüfdauer geprüft werden kann, ist eine Angleichung der Prüfdauern 
zwischen Umgebungstemperatur und Kühlmittelkreislauf erforderlich. 
 
Dabei ist die kürzere der beiden Prüfdauern tprüf, Umgebung und tprüf, KKL gemäß 
nachfolgendem Iterationsverfahren an die längere Prüfdauer anzupassen indem die 
Prüfung in mindestens zwei Teilprüfungen aufgeteilt wird und die Prüftemperaturen 
bis auf eine Teilprüfung bei allen anderen Teilprüfungen abgesenkt wird. 
 
Fall A: tprüf, Umgebung < tprüf, KKL 
 
Prüfdauer: 
Für tprüf, Umgebung < tprüf, KKL beträgt die Prüfdauer für den Betriebsmodus i 
tprüf, Mode i = tprüf, KKL. 
 
Prüftemperatur Kühlmittel: 
Die Prüftemperatur muss nach dem Arrhenius-Modell gemäß Anhang D.1 gewählt 
werden (in der Regel Tkühl,max). 
Prüftemperaturen Umgebungstemperatur: 
Die Prüftemperaturen müssen nach folgendem Algorithmus iterativ berechnet werden 
auf Basis des Temperaturkollektivs der Umgebungstemperatur des betrachteten 
Betriebsmodus i (Tabelle 107). 
 
 
Tabelle 107: Temperaturkollektiv Umgebung 
Temperatur in °C 
Verteilung in % 
TFeld.1 
p1 
TFeld.2 
p2 
… 
… 
TFeld.n 
pn 
 


### 第 146 页
Seite 146 
VW 80000: 2017-10 
 
 
 
 
 
 
1. Iterationsstart (m = 0): 
Die erste Teilprüfung muss bei der Prüftemperatur TFeld, n durchgeführt werden 
für die Teilprüfdauer tprüf, T_Feld, n = tBetrieb* pn. (Wobei tBetrieb der Betriebsdauer 
im Feld des betrachteten Betriebsmodus i in Stunden entspricht) 
 
2. Erste Iteration (m = 1): 
Durch die 1. Teilprüfung wird ein Teil der Prüfdauer für den Betriebsmodus i 
tprüf, Mode i abgedeckt, so dass sich eine durch die weiteren Teilprüfungen noch 
abzudeckende Restprüfdauer ergibt von 
tRest, 1 = tprüf, Mode i – tprüf, T_Feld, n. 
 
Durch die erste Teilprüfung ist zudem der Anteil pn der Temperaturverteilung 
der Umgebungstemperatur abgedeckt. Daher muss dieser Anteil pn für die 
weitere Berechnung auf pn = 0 gesetzt werden. 
Zur Festlegung der Prüftemperatur für die 2. Teilprüfung (m = 1) ist zunächst 
die Prüftemperatur Tangepasst mit Hilfe des Arrhenius-Modells gemäß D.1 bzw. 
D.4 so zu bestimmen, dass sich für die (mit pn = 0 angepasste) Verteilung der 
Umgebungstemperatur eine Prüfdauer in Höhe der Restprüfdauer tRest, 1 
ergibt. 
Ist die so ermittelte angepasste Prüftemperatur Tangepasst < TFeld, n-1, muss die 
2. Teilprüfung bei der Prüftemperatur TFeld, n-1 durchgeführt werden für die 
Prüfdauer 
tprüf, T_Feld, n-1 = tBetrieb * pn-1 
und es muss mindestens ein weiterer Iterationsschritt durchgeführt werden. 
Ist dagegen die ermittelte angepasste Prüftemperatur Tangepasst > TFeld, n-1, 
muss die 2. Teilprüfung bei der Prüftemperatur Tangepasst durchgeführt werden 
für die Prüfdauer 
tprüf, T_Feld, n-1 = tRest, 1 
und es muss kein weiterer Iterationsschritt durchgeführt werden 
(Iterationsende). 
 
3. Weitere Iterationen (m = 2, 3, …) 
Durch die ersten m Teilprüfungen wird ein Teil der Prüfdauer für den 
Betriebsmodus i tprüf, Mode i abgedeckt, so dass sich eine durch die weiteren 
Teilprüfungen noch abzudeckende Restprüfdauer ergibt von 






1
0
m
k
prüf, T
prüf, Mode i
Rest, m
Feld,n k
t
t
t
 
Durch die ersten m Teilprüfungen sind zudem die Anteile pn-k mit 
k = 0, 1, …, (m-1) der Temperaturverteilung der Umgebungstemperatur 
abgedeckt. Daher müssen diese Anteile pn-k für die weitere Berechnung auf 
pn-k = 0 gesetzt werden. 
Zur Festlegung der Prüftemperatur für die (m+1). Teilprüfung ist zunächst die 
Prüftemperatur Tangepasst mit Hilfe des Arrhenius-Modells gemäß D.1 bzw. D.4 
so zu bestimmen, dass sich für die (mit pn-k = 0 angepasste) Verteilung der 
Umgebungstemperatur eine Prüfdauer in Höhe der Restprüfdauer tRest, m 
ergibt. 
Ist die so ermittelte angepasste Prüftemperatur Tangepasst < TFeld, n-m, muss die 
(m+1). Teilprüfung bei der Prüftemperatur TFeld, n-m durchgeführt werden für die 
Prüfdauer 
tprüf, T_Feld, n-m = tBetrieb * pn-m 


### 第 147 页
 
  
Seite 147 
VW 80000: 2017-10 
 
 
 
 
 
 
 
und es muss mindestens ein weiterer Iterationsschritt durchgeführt werden. 
Ist dagegen die ermittelte angepasste Prüftemperatur Tangepasst > TFeld, n-m, 
muss die (m+1). Teilprüfung bei der Prüftemperatur Tangepasst durchgeführt 
werden für die Prüfdauer 
tprüf, T_Feld, n-m = tRest, m 
und es muss kein weiterer Iterationsschritt durchgeführt werden 
(Iterationsende). 
 
 
Fall B: tprüf, Umgebung > tprüf, KKL 
 
Prüfdauer: 
Für tprüf, Umgebung > tprüf, KKL beträgt die Prüfdauer für den Betriebsmodus i 
tprüf, Mode i = tprüf, Umgebung. 
 
Prüftemperatur Umgebung: 
Die Prüftemperatur muss nach dem Arrhenius-Modell gemäß Anhang D.1 bzw. D.4 
gewählt werden (in der Regel Tmax bzw. Tmax und Top, max). 
 
Prüftemperaturen Kühlmittel: 
Die Prüftemperaturen müssen nach folgendem Algorithmus iterativ berechnet werden 
auf Basis des Temperaturkollektivs für die Kühlmitteltemperatur des betrachteten 
Betriebsmodus i (Tabelle 108). 
 
Tabelle 108: Temperaturkollektiv Kühlmitteltemperatur 
Temperatur in °C 
Verteilung in %  
TFeld.1 
p1 
TFeld.2 
p2 
… 
… 
TFeld.n 
pn 
 
1. Iterationsstart (m = 0): 
Die erste Teilprüfung muss bei der Prüftemperatur TFeld, n durchgeführt werden 
für die Teilprüfdauer tprüf, T_Feld, n = tBetrieb* pn. (Wobei tBetrieb der Betriebsdauer 
im Feld des betrachteten Betriebsmodus i in Stunden entspricht) 
 
2. Erste Iteration (m = 1): 
Durch die erste Teilprüfung wird ein Teil der Prüfdauer für den Betriebsmodus 
i tprüf, Mode i abgedeckt, so dass sich eine durch die weiteren Teilprüfungen noch 
abzudeckende Restprüfdauer ergibt von 
tRest, 1 = tprüf, Mode i – tprüf, T_Feld, n. 
 
Durch die erste Teilprüfung ist zudem der Anteil pn der Temperaturverteilung 
der Kühlmitteltemperatur abgedeckt. Daher muss dieser Anteil pn für die 
weitere Berechnung auf pn = 0 gesetzt werden. 
Zur Festlegung der Prüftemperatur für die zweite Teilprüfung (m = 1) ist 
zunächst die Prüftemperatur Tangepasst mit Hilfe des Arrhenius-Modells gemäß 
D.1 so zu bestimmen, dass sich für die (mit pn = 0 angepasste) Verteilung der 
Kühlmitteltemperatur eine Prüfdauer in Höhe der Restprüfdauer tRest, 1 ergibt. 


### 第 148 页
Seite 148 
VW 80000: 2017-10 
 
 
 
 
 
 
Ist die so ermittelte angepasste Prüftemperatur Tangepasst < TFeld, n-1, muss die 
zweite Teilprüfung bei der Prüftemperatur TFeld, n-1 durchgeführt werden für die 
Prüfdauer 
tprüf, T_Feld, n-1 = tBetrieb * pn-1 
und es muss mindestens ein weiterer Iterationsschritt durchgeführt werden. 
Ist dagegen die ermittelte angepasste Prüftemperatur Tangepasst > TFeld, n-1, 
muss die 2. Teilprüfung bei der Prüftemperatur Tangepasst durchgeführt werden 
für die Prüfdauer 
tprüf, T_Feld, n-1 = tRest, 1 
und es muss kein weiterer Iterationsschritt durchgeführt werden 
(Iterationsende). 
 
 
3. Weitere Iterationen (m = 2, 3, …) 
Durch die ersten m Teilprüfungen wird ein Teil der Prüfdauer für den 
Betriebsmodus i tprüf, Mode i abgedeckt, so dass sich eine durch die weiteren 
Teilprüfungen noch abzudeckende Restprüfdauer ergibt von 
 
Durch die ersten m Teilprüfungen sind zudem die Anteile pn-k mit 
k = 0, 1, …, (m-1) der Temperaturverteilung der Kühlmitteltemperatur 
abgedeckt. Daher müssen diese Anteile pn-k für die weitere Berechnung auf 
pn-k = 0 gesetzt werden. 
Zur Festlegung der Prüftemperatur für die (m+1). Teilprüfung ist zunächst die 
Prüftemperatur Tangepasst mit Hilfe des Arrhenius-Modells gemäß D.1 so zu 
bestimmen, dass sich für die (mit pn-k = 0 angepasste) Verteilung der 
Kühlmitteltemperatur eine Prüfdauer in Höhe der Restprüfdauer tRest, m ergibt. 
Ist die so ermittelte angepasste Prüftemperatur Tangepasst < TFeld, n-m, muss die 
(m+1). Teilprüfung bei der Prüftemperatur TFeld, n-m durchgeführt werden für die 
Prüfdauer 
tprüf, T_Feld, n-m = tBetrieb * pn-m 
und es muss mindestens ein weiterer Iterationsschritt durchgeführt werden. 
Ist dagegen die ermittelte angepasste Prüftemperatur Tangepasst > TFeld, n-m, 
muss die (m+1). Teilprüfung bei der Prüftemperatur Tangepasst durchgeführt 
werden für die Prüfdauer 
tprüf, T_Feld, n-m = tRest, m 
und es muss kein weiterer Iterationsschritt durchgeführt werden 
(Iterationsende). 
 
 






1
0
m
k
prüf, T
prüf, Mode i
Rest, m
Feld,n k
t
t
t


### 第 149 页
 
  
Seite 149 
VW 80000: 2017-10 
 
 
 
 
 
 
 
D.7 Beispiel Arrhenius-Modell zur Verwendung bei Komponenten an 
Kühlmittelkreisläufen 
 
Für ein an den Kühlmittelkreislauf angeschlossenes Steuergerät mit dem in den 
folgenden Tabellen angegebenen Temperaturkollektiv für die Umgebungstemperatur 
und die Kühlmitteltemperatur 
Tabelle 109: Beispielkollektiv Umgebungstemperatur 
Temperatur in °C Verteilung in % 
-40 
6 
23 
20 
50 
65 
100 
8 
105 
1 
 
Tabelle 110: Beispielkollektiv Kühlmitteltemperatur 
Temperatur in 
°C 
Verteilung in % 
-40 
6 
23 
20 
50 
65 
75 
8 
80 
1 
 
und einer Betriebsdauer von 8 000 h wird die Prüfdauer für den Lebensdauertest 
Hochtemperaturdauerlauf wie folgt berechnet: 
 
Prüfdauer: 
Berechnung der Prüfdauern für Umgebungstemperatur und Kühlmitteltemperatur mit 
dem Arrhenius-Modell:  
tprüf, Umgebung = 1143 h 
tprüf, KKL = 2009 h 
Da tprüf, Umgebung < tprüf, KKL erfolgt die Berechnung nach dem unter D.6 beschriebenen 
Fall A. Die Prüfdauer für die Umgebungstemperatur muss auf tprüf, Mode,i = tprüf, KKL = 
2009 h angepasst werden. 
 
Prüftemperatur Kühlmittel: 
Die Prüftemperatur für das Kühlmittel beträgt gemäß dem Temperaturkollektiv TKKL, 
max = TFeld, 5 = 80°C. 
 
Iterative Berechnung Prüftemperaturen Umgebungstemperatur: 
1. Iterationsstart: 
Die erste Teilprüfung erfolgt bei TFeld, 5 = 105 °C. Die Prüfdauer beträgt 
tprüf, T_Feld, 5 = tBetrieb*p5 = 8000 h * 1 % = 80 h. 
 
2. Erste Iteration: 
Durch die erste Teilprüfung wurde bereits ein Teil der Prüfdauer für den 
Betriebsmodus i tprüf, Mode i abgedeckt, die Restprüfdauer muss daher 


### 第 150 页
Seite 150 
VW 80000: 2017-10 
 
 
 
 
 
 
neu berechnet werden: tRest, 1 = tprüf, Mode i – tprüf, T_Feld, 5 = 2009 h – 80 h = 
1929 h. 
Da durch die erste Teilprüfung der Anteil p5 der Temperaturverteilung 
abgedeckt ist wird für die weitere Berechnung durch das Arrhenius-
Modell p5 in der Temperaturverteilung auf p5 = 0 gesetzt, gemäß 
nachfolgender Tabelle. 
 
Tabelle 111: Angepasstes Temperaturkollektiv Umgebung nach erster Teilprüfung 
Temperatur in °C Verteilung in % 
-40 
6 
23 
20 
50 
65 
100 
8 
 
Um anschließend die Prüftemperatur für die 2. Teilprüfung bestimmen 
zu können muss die Prüftemperatur Tangepasst mit Hilfe des Arrhenius-
Modells gemäß C.1 so berechnet werden, dass sich eine Prüfdauer in 
Höhe der Restprüfdauer tRest, 1 = 1929 h ergibt. Unter Berücksichtigung 
der angepassten Temperaturverteilung der Umgebungstemperatur 
ergibt sich bei einer Temperatur Tangepasst = 89,5°C (genauer Wert: 
89,46 °C) die erforderliche Prüfdauer von 1929 h. 
Da aber Tangepasst < TFeld, 4 (also 89,5 °C < 100 °C) muss die 2. 
Teilprüfung bei der Prüftemperatur TFeld, 4 = 100 °C durchgeführt 
werden. 
Die Prüfdauer für die 2. Teilprüfung beträgt tprüf, T_Feld, 4 = tBetrieb*p4 = 
8000 h * 8 % = 640 h. 
 
3. Zweite Iteration 
Durch die 2. Teilprüfung wurde ein weiterer Teil der Prüfdauer für den 
Betriebsmodus i tprüf, Mode i abgedeckt, die Restprüfdauer ergibt sich 
somit zu: 
tRest, 2 = tprüf, Mode i – (tprüf, T_Feld, 5 + tprüf, T_Feld, 4) = 2009 h – 80 h - 640 h= 
1289 h. 
Durch die ersten zwei Teilprüfungen wurden die Anteile p5 und p4 des 
Temperaturkollektivs für die Umgebung bereits abgedeckt. Daher 
müssen für die weitere Iteration die Anteile p4 = p5 = 0 gesetzt werden, 
gemäß nachfolgender Tabelle. 
 
Tabelle 112: Angepasstes Temperaturkollektiv Umgebung nach 1. und 2. Teilprüfung 
Temperatur in °C Verteilung in % 
-40 
6 
23 
20 
50 
65 
 
Um anschließend die Prüftemperatur für die 3. Teilprüfung bestimmen 
zu können muss die Prüftemperatur Tangepasst mit Hilfe des Arrhenius-
Modells gemäß D.1so berechnet werden, dass sich eine Prüfdauer in 
Höhe der Restprüfdauer tRest, 2 = 1289 h ergibt. Unter Berücksichtigung 
der angepassten Temperaturverteilung der Umgebungstemperatur 


### 第 151 页
 
  
Seite 151 
VW 80000: 2017-10 
 
 
 
 
 
 
 
ergibt sich bei der Temperatur Tangepasst = 82 °C (genauer Wert: 82,17 
°C) die erforderliche Prüfdauer von 1289 h. 
Da Tangepasst > TFeld, 3 (also 82 °C > 50 °C) ist kein weiterer 
Iterationsdurchlauf notwendig. Die 3. und letzte Teilprüfung wird daher 
bei Tangepasst = 82 °C für die Prüfdauer tprüf, T_Feld, 3 = tRest, 3 = 1289 h 
durchgeführt. 
 
Insgesamt müssen 80 h bei 105 °C Umgebungstemperatur und 640 h bei 100 °C 
Umgebungstemperatur und 1289 h bei 82 °C Umgebungstemperatur geprüft werden. 
Die Kühlmitteltemperatur beträgt in diesem Beispiel während der gesamten 
Prüfdauer konstant 80 °C. 
 


### 第 152 页
Seite 152 
VW 80000: 2017-10 
 
 
 
 
 
 
Anhang E (normativ) 
 
Modelle zur Berechnung der Lebensdauerprüfung 
Temperaturwechseldauerlauf 
 
E.1 Anpassung der Prüftemperaturen zur Reduzierung der Prüfzyklen 
Zur Reduzierung der Prüfzyklenzahl für Komponenten, die in mehreren Betriebsmodi 
aktiv betrieben werden, kann die Prüftemperatur Tmax (bzw. TKKL,max) zur 
Absicherung einzelner für die Komponente relevanter Betriebsmodi erhöht werden. Als 
obere Prüftemperatur kann dazu die absolute Maximaltemperatur Tmax (bzw. 
TKKL,max) über alle für die Komponente relevanten Betriebsmodi angewendet werden. 
Dabei muss die volle Funktionalität der Komponente gegeben sein. Zudem ist darauf zu 
achten, dass unter Berücksichtigung der je nach erforderlichen Betriebsart erzeugten 
Eigenerwärmung der Komponente, alle zur Komponente gehörigen Bauteile, 
Bauelemente und Werkstoffe nicht außerhalb ihrer jeweiligen Spezifikationsgrenzen 
(Temperaturgrenzen) betrieben werden. 
Eine Anpassung der Prüftemperatur kann für einzelne oder mehrere für die 
Komponente relevante Betriebsmodi erfolgen. Die erforderlichen Zyklenzahlen müssen 
jeweils gemäß Anhang D berechnet werden.  
Details zur Erhöhung der Prüftemperatur und den daraus resultierenden 
Prüfzyklenzahlen sind zwischen Auftraggeber und Auftragnehmer abzustimmen und zu 
dokumentieren. 
 
E.2 Coffin-Manson-Modell  
Für die Berechnung der Prüfdauer der Lebensdauerprüfung 
Temperaturwechseldauerlauf sind die durchschnittliche Temperaturänderung der 
Komponente im Feld TFeld (vgl. Tabelle 94) und die Anzahl der Temperaturzyklen 
während der Lebensdauer im Feld NTempZyklenFeld heranzuziehen.  
Für die Anzahl der Temperaturzyklen im Feld werden2 Temperaturwechsel pro Tag 
angesetzt. Damit ergibt sich: 
 
NTempZyklenFeld = 2 * 365 * 15 (Jahre) = 10 950 Zyklen 
 
In Abhängigkeit der durchschnittlichen Temperaturänderung im Feld wird der 
Beschleunigungsfaktor des Coffin-Manson-Modells wie folgt berechnet: 
 
c
ΔT
ΔT
A
Feld
Test
CM







(3) 
wobei:  
ACM 
Beschleunigungsfaktor des Coffin-Manson-Modells 
TTest Temperaturdifferenz während eines Prüfzyklus (TTest = Tmax - Tmin) 
TFeld Durchschnittliche Temperaturdifferenz der Umgebungstemperatur der 
Komponente an ihrem Einbauort während der Lebensdauer im Feld 
c 
Parameter des Coffin-Manson-Modells. 
In dieser Norm wird für c ein fester Wert von 2,5 angesetzt 
 
 
 
Die Gesamtzahl der Prüfzyklen wird berechnet gemäß 


### 第 153 页
 
  
Seite 153 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
CM
Feld
TempZyklen
Prüf
A
N
N

   
(4) 
 
wobei: 
NPrüf 
Erforderlichen Anzahl der Prüfzyklen 
NTempZyklenFeld Anzahl der Temperaturzyklen während der Lebensdauer im Feld 
ACM 
Beschleunigungsfaktor des Coffin-Manson-Modells gemäß Gleichung (3) 
 
 
 
 


### 第 154 页
Seite 154 
VW 80000: 2017-10 
 
 
 
 
 
 
E.3 Beispiel:  
Für ein Steuergerät mit Tmin = -40 °C und Tmax = 105 °C, einer Lebensdauer im Feld von 
15 Jahren und einer durchschnittlichen Temperaturdifferenz im Feld von TFeld = 40 °C 
wird die Anzahl der Prüfzyklen (NPrüf) wie nachstehend berechnet: 
 
1. Die Anzahl der Temperaturzyklen im Feld:  
 
NTempZyklenFeld = 2 * 365 * 15 (Jahre) = 10 950 Zyklen 
 
2. Temperaturdifferenz während eines Prüfzyklus: 
 
TTest = 105 °C – (-40 °C)= 145 °C. 
 
3. Mit Gleichung (3) errechnet sich der Beschleunigungsfaktor des Coffin-Manson-
Modells als ACM = 25,02 
 
4. Damit berechnet sich die Anzahl der Prüfzyklen mit Gleichung (4) als: 
 
 Zyklen
 438
25,02
 Zyklen
10950
NPrüf


 
5. Die Haltezeit setzt sich aus der Zeit zur Durchtemperierung der Komponente plus 
15 min zusammen. Unter der Annahme, die Komponente ist nach 20 min 
durchtemperiert, ergibt sich somit eine Haltezeit von 35 min.  
 
6. Damit beträgt die Dauer für einen Zyklus: 
t𝑍𝑦𝑘𝑙𝑢𝑠 = 2∙ ((T𝑚𝑎𝑥 − T𝑚𝑖𝑛)
4 °𝐶/𝑚𝑖𝑛
+ t𝐻𝑎𝑙𝑡𝑒𝑧𝑒𝑖𝑡) 
 
7. Im Beispiel: t𝑍𝑦𝑘𝑙𝑢𝑠 = 2∙ (
(105 °C−(−40 °C))
4°𝐶/𝑚𝑖𝑛
+ 35min) = 142,5 𝑚𝑖𝑛 
 
8. Für 438 Zyklen beträgt die Gesamtprüfdauer damit 1040 h. 
 
 


### 第 155 页
 
  
Seite 155 
VW 80000: 2017-10 
 
 
 
 
 
 
 
E.4 Coffin-Manson – Modell zur Verwendung bei Komponenten an 
Kühlmittelkreisläufen 
Bei Komponenten mit Anbindung an den Kühlmittelkreislauf müssen alle relevanten 
Betriebsmodi i (siehe Kapitel 4.3; i entspricht laufender Nummer der Modi) mit ihren 
entsprechenden Temperaturhüben für Umgebung und Kühlmittelkreislauf berücksichtigt 
werden. 
Für den Lebensdauertest Temperaturwechseldauerlauf müssen die Ecktemperaturen 
und die Anzahl der Prüfzyklen für jeden relevanten Betriebsmodus i berechnet werden, 
wie im Folgenden beschrieben; die Gesamtprüfzyklenzahl ergibt sich aus der Summe 
der Teilprüfzyklenanzahlen für jeden relevanten Betriebsmodus i. 
 
Für jeden relevanten Betriebsmodus i müssen zur Berechnung der Prüfzyklenzahl für 
den Betriebsmodus i zunächst die Prüfzyklenzahlen für die Umgebungstemperatur und 
den Kühlmittelkreislauf separat nach dem Coffin-Manson-Modell gemäß Anhang C.7 
berechnet werden. 
Da sich die daraus resultierenden Prüfzyklenzahlen Nprüf, Umgebung und Nprüf, KKL in der 
Regel unterscheiden, die Komponente für den jeweiligen Betriebsmodus i aber nur bei 
einer einheitlichen Prüfzyklenzahl geprüft werden kann, ist eine Angleichung der 
Prüfzyklenzahlen zwischen Umgebungstemperatur und Kühlmittelkreislauf erforderlich. 
 
Dabei muss die kürzere der beiden Prüfzyklenzahlen Nprüf, Umgebung und Nprüf, KKL gemäß 
nachfolgender Berechnung an die längere Prüfzyklenzahl angepasst werden, indem die 
Prüfung in drei Teilprüfungen aufgeteilt wird. Dabei findet eine Teilprüfung bei vollem 
Temperaturhub zwischen Tmin und Tmax statt; die beiden anderen finden bei reduziertem 
Temperaturhub zwischen Tmin und TRT bzw. zwischen TRT und Tmax statt. 
 
Fall A: Nprüf, Umgebung > Nprüf, KKL 
 
Prüfzyklenzahl: 
Für Nprüf, Umgebung > Nprüf, KKL beträgt die Prüfzyklenzahl für den Betriebsmodus i 
Nprüf, Mode i = Nprüf, Umgebung. 
 
 
Prüfzyklenzahl Kühlmittel: 
 
Die Prüfzyklenzahl für das Kühlmittel Nprüf, KKL muss an die größere Prüfzyklenzahl für 
die Umgebung Nprüf, Umgebung angepasst werden. Dabei müssen die Prüfzyklen in 
folgenden drei Temperaturbereichen durchgeführt werden: 
 
1. xKKL Prüfzyklen müssen zwischen TKKL, min und TKKL, max durchgeführt werden. 
Der Beschleunigungsfaktor ACM, KKL, 1 wird nach dem Coffin-Manson-Modell 
berechnet mit ∆TTest, 1 = TKKL, max - TKKL, min 
2. ½ * (Nprüf, Mode i - xKKL) Prüfzyklen müssen zwischen TKKL, min und TRT durchgeführt 
werden. 
Der Beschleunigungsfaktor ACM, KKL, 2 wird nach dem Coffin-Manson-Modell 
berechnet mit ∆TTest, 2 = TRT - TKKL, min. 
3. ½ * (Nprüf, Mode i - xKKL) Prüfzyklen müssen zwischen TRT und TKKL, max durchgeführt 
werden. 


### 第 156 页
Seite 156 
VW 80000: 2017-10 
 
 
 
 
 
 
Der Beschleunigungsfaktor ACM, KKL, 3 wird nach dem Coffin-Manson-Modell 
berechnet, mit ∆TTest, 3 = TKKL, max – TRT. 
 
In Summe ergeben sich aus 1. bis 3. insgesamt NPrüf, mode i Temperaturzyklen. 
 
In Anlehnung an Gleichung (4) in Anhang E.1 ergibt sich:  




, 3
,
,
,
Pr
, 2
,
,
,
Pr
,1
,
2
1
2
1
CM KKL
KKL
üf Mode i
CM KKL
KKL
üf Mode i
CM KKL
KKL
TempZyklenFeld
A
x
N
A
x
N
A
x
N










 
Die Prüfzyklenzahl xKKL berechnet sich daraus wie folgt: 
 



, 3 
,
, 2
,
,1
,
, 3
,
, 2
,
,
,
Pr
2
1
2
CM KKL
CM KKL
KKL
CM
CM KKL
KKL
CM
Mode i
üf
Feld
TempZyklen
KKL
A
A
A
A
A
N
N
x







 
Durch Einsetzen von xKKL in die oben aufgeführten Punkte 1. bis 3. erhält man die 
Prüfzyklenanzahlen für die drei Teilprüfungen. 
 
Ist TKKL, op, max < TKKL, max oder TKKL, op,min > TKKL, min oder TUmgebung, op, max < TUmgebung, max 
oder TUmgebung, op,min > TUmgebung, min, müssen zusätzliche Haltezeiten bei den 
entsprechenden Temperaturen gemäß Bild 47 in Abschnitt 13.3.2.1 berücksichtigt 
werden.  
Die Temperaturwechselzyklen für die Umgebungstemperatur und für den 
Kühlmittelkreislauf verlaufen synchron während einer Prüfung. 
 
 
 
 
Fall B: Nprüf, Umgebung < Nprüf, KKL 
 
Prüfzyklenzahl: 
Für Nprüf, Umgebung < Nprüf, KKL beträgt die Prüfzyklenzahl für den Betriebsmodus i 
Nprüf, Mode i = Nprüf, KKL. 
 
 
Prüfzyklenzahl Umgebung: 
 
Die Prüfzyklenzahl für die Umgebung Nprüf, Umgebung muss an die größere Prüfzyklenzahl 
für das Kühlmittel Nprüf, KKL angepasst werden. Dabei müssen die Prüfzyklen in 
folgenden drei Temperaturbereichen durchgeführt werden: 
 
1. xUmgebung Prüfzyklen müssen zwischen TUmgebung, min und TUmgebung, max 
durchgeführt werden. Der Beschleunigungsfaktor ACM, Umgebung, 1 wird nach dem 
Coffin-Manson-Modell berechnet, mit ∆TTest, 1 = TUmgebung, max – TUmgebung, min. 
2. ½ * (Nprüf, Mode i – xUmgebung) Prüfzyklen müssen zwischen TUmgebung, min und TRT 
durchgeführt werden. Der Beschleunigungsfaktor ACM, Umgebung, 2 wird nach dem 
Coffin-Manson-Modell berechnet, mit ∆TTest, 2 = TRT – TUmgebung, min. 


### 第 157 页
 
  
Seite 157 
VW 80000: 2017-10 
 
 
 
 
 
 
 
3. ½ * (Nprüf, Mode i – xUmgebung) Prüfzyklen müssen zwischen TRT und TUmgebung, max 
durchgeführt werden. Der Beschleunigungsfaktor ACM, Umgebung, 3 wird nach dem 
Coffin-Manson-Modell berechnet, mit ∆TTest, 3 = TUmgebung, max – TRT. 
 
In Summe ergeben sich aus 1. bis 3. insgesamt NPrüf, mode i Temperaturzyklen. 
 
In Anlehnung an Gleichung (4) in Anhang E.1 ergibt sich:  




, 3
,
,
,
Pr
, 2
,
,
,
Pr
,1
,
2
1
2
1
CM Umgebung
Umgebung
Mode i
üf
CM Umgebung
Umgebung
üf Mode i
CM Umgebung
Umgebung
Feld
TempZyklen
A
x
N
A
x
N
A
x
N










 
Die Prüfzyklenzahl xUmgebung berechnet sich daraus wie folgt: 



, 3 
,
, 2
,
,1
,
, 3
,
, 2
,
,
,
Pr
2
1
2
CM Umgebung
CM Umgebung
Umgebung
CM
CM Umgebung
Umgebung
CM
Mode i
üf
Feld
TempZyklen
Umgebung
A
A
A
A
A
N
N
x







 
Durch Einsetzen von xUmgebung in die oben aufgeführten Punkte 1. bis 3. erhält man die 
Prüfzyklenanzahlen für die drei Teilprüfungen. 
 
Ist TUmgebung, op, max < TUmgebung, max oder TUmgebung, op,min > TUmgebung, min oder TKKL, op, max < 
TKKL, max oder TKKL, op,min > TKKL, min müssen zusätzliche Haltezeiten bei den 
entsprechenden Temperaturen gemäß Bild 45 in Abschnitt 16.3.2.1 berücksichtigt 
werden. 
Die Temperaturwechselzyklen für die Umgebungstemperatur und für den 
Kühlmittelkreislauf verlaufen synchron während einer Prüfung. 
 
 


### 第 158 页
Seite 158 
VW 80000: 2017-10 
 
 
 
 
 
 
E.5 Beispiel Coffin-Manson-Modell zur Verwendung bei Komponenten an 
Kühlmittelkreisläufen 
 
Für ein an den Kühlmittelkreislauf angeschlossenes Steuergerät mit dem 
Umgebungstemperaturbereich TUmgebung, min = -40 °C bis TUmgebung, max = 120 °C und dem 
Kühlmitteltemperaturbereich TKKL, min = -40 °C bis TKKL, max = 80 °C, einer Lebensdauer 
im Feld von 15 Jahren, einer durchschnittlichen Temperaturdifferenz der Umgebung im 
Feld von ∆TFeld, Umgebung = 60 K und einer durchschnittlichen Temperaturdifferenz des 
Kühlmittels im Feld von ∆TFeld, KKL = 36 K berechnet sich die Anzahl der Prüfzyklen für 
einen Betriebsmodus i wie folgt: 
 
Prüfzyklenzahl Umgebung und Kühlmittel: 
Die Berechnung der Prüfzyklenzahlen für die Umgebung und das Kühlmittel nach dem 
Coffin-Manson-Modell gemäß Anhang E.1 liefert folgende Werte: 
 
Nprüf, Umgebung = 943 Zyklen 
Nprüf, KKL = 540 Zyklen 
 
Da Nprüf, Umgebung > Nprüf, KKL beträgt die Prüfzyklenzahl für den Betriebsmodus i Nprüf, Mode i 
= Nprüf, Umgebung = 943 Zyklen. Die Anzahl der Prüfzyklen für das Kühlmittel ist 
anzupassen. 
 
Anpassung Prüfzyklenzahl Kühlmittel: 
Die Anpassung der Prüfzyklenzahl des Kühlmittels auf Nprüf, Mode i = 943 Zyklen erfolgt in 
drei Teilen: 
1. xKKL Prüfzyklen müssen zwischen TKKL, min = -40 °C und TKKL, max = 80 °C 
durchgeführt werden. Es ergibt sich der nach dem Coffin-Manson-Modell 
 
 berechnete Beschleunigungsfaktor ACM, KKL, 1 = 
5,
2
36
)
( 40
80








 

C
C
C
= 20,29. 
2. ½ * (943 – xKKL) Prüfzyklen müssen zwischen TKKL, min = -40 °C und TRT = 23 °C 
durchgeführt werden. Es ergibt sich der nach dem Coffin-Manson-Modell  
 
berechnete Beschleunigungsfaktor ACM, KKL, 2 = 
,5
2
36
)
( 40
23








 

C
C
C
= 4,05. 
3. ½ * (943 – xKKL) Prüfzyklen müssen zwischen TRT = 23 °C und TKKL, max = 80 °C 
durchgeführt werden. Es ergibt sich der nach dem Coffin-Manson-Modell  
 
berechnete Beschleunigungsfaktor ACM, KKL, 3 = 
5,2
36
23
80










C
C
C
= 3,15. 
 
 


### 第 159 页
 
  
Seite 159 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Für xKKL ergibt sich somit: 
 












, 3
,
, 2
,
, 1
,
, 3
,
, 2
,
,
,
Pr
2
1
2
CM KKL
CM KKL
KKL
CM
CM KKL
KKL
CM
Mode i
üf
Feld
TempZyklen
KKL
A
A
A
A
A
N
N
x




Zyklen
453
,315
,4 05
2
1
,29
20
,315
,4 05
2
943
10950







  
Für die drei Temperaturbereiche ergeben sich daher folgende Prüfzyklenzahlen, 
berechnet nach den Punkten 1. bis 3.: 
 
1. Zwischen TKKL, min = -40 °C und TKKL, max = 80 °C müssen 453 Zyklen 
durchgeführt werden 
2. Zwischen TKKL, min = -40 °C und TRT = 23 °C müssen 245 Zyklen durchgeführt 
werden 
3. Zwischen TRT = 23 °C und TKKL, max = 80 °C müssen 245 Zyklen durchgeführt 
werden 
 
Durch Addition der Teilprüfzyklen ergibt sich wieder die Gesamtprüfzyklenanzahl für 
den Betriebsmodus i Nprüf, Mode i = 943 Zyklen. 
 
Die Temperaturwechselzyklen für die Umgebungstemperatur und für den 
Kühlmittelkreislauf verlaufen synchron während einer Prüfung. 
 
 


### 第 160 页
Seite 160 
VW 80000: 2017-10 
 
 
 
 
 
 
Anhang F (normativ) 
 
Modelle zur Berechnung der Prüfung 
Feuchte Wärme Konstant – Schärfegrad 2 
 
F.1 Lawson-Modell 
Für die Berechnung der Prüfdauer der Prüfung Feuchte Wärme konstant – Schärfegrad 
2 ist die durchschnittliche Umgebungsfeuchte RHFeldParken und die durchschnittliche 
Umgebungstemperatur TFeldParken der Komponente im geparkten Fahrzeug 
heranzuziehen. 
Soweit im Lastenheft nicht anders definiert, sind die folgenden Werte für die 
Berechnung heranzuziehen: 
 
Tabelle 113: Durchschnittliche Umgebungsfeuchte und Umgebungstemperatur im geparkten 
Fahrzeug 
Einbauort 
Durchschnittliche 
Umgebungsfeuchte 
RHFeldParken 
Durchschnittliche 
Umgebungstemperatur 
TFeldParken 
Im Fahrgastraum / 
Kofferraum 
60 % relative Feuchte 
23 °C 
Außerhalb 
Fahrgastraum / 
Kofferraum 
65 % relative Feuchte 
23 °C 
  
In Abhängigkeit der durchschnittlichen Umgebungsfeuchte und Umgebungstemperatur 
im Feld wird der Beschleunigungsfaktor des Lawson-Modells wie folgt berechnet:  
 















 



































2
FeldParken
2
Prüf
FeldParken 
 
Prüf
A
T/RH
RH
b RH
 273,15
T
1
 273,15
T
1
k
E
e
A
  
(5) 
 
wobei:  
AT/RH 
Beschleunigungsfaktor des Lawson-Modells 
b 
Konstante ( b = 5,57 x 10-4) 
EA 
Aktivierungsenergie (EA = 0,4 eV) 
k 
Boltzmann-Konstante (k = 8,617 x 10-5 eV/K) 
TTest 
Prüftemperatur in °C 
TFeldParken 
Durchschnittliche Umgebungstemperatur in °C  
RHPrüf  
Relative Feuchte in % während der Prüfung 
RHFeldParken Durchschnittliche relative Feuchte in %  
-273,15 °C Absoluter Nullpunkt der Temperatur 
 
Die Prüfdauer der Prüfung Feuchte Wärme konstant – Schärfegrad 2 wird berechnet 
durch: 
T/RH
FeldParken
Prüf
A
t
t

   
(6) 


### 第 161 页
 
  
Seite 161 
VW 80000: 2017-10 
 
 
 
 
 
 
 
wobei:  
 
tPrüf 
Prüfdauer in h 
tFeldParken Nichtbetriebsdauer (Parkdauer) in h während der Lebensdauer im 
Feld (131.400 h, im ungünstigsten Fall, falls Fahrzeug nicht  
genutzt wird) 
AT/RH  
Beschleunigungsfaktor des Lawson-Modells gemäß Gleichung (5) 
 
F.2 Beispiel:  
 
Für ein im Motorraum verbautes Steuergerät wird die Prüfdauer wie folgt berechnet: 
 
1. Für die Komponente wird eine durchschnittliche Temperatur von 
TFeldParken = 23 °C und eine relative Feuchte von RHFeldParken = 65 %  
im geparkten Fahrzeug angenommen. 
Die Prüfbedingungen sind TTest = 65 °C und RHPrüf = 93 %. 
 
Mit Gleichung (5) ergeben diese Werte einen kombinierten 
Beschleunigungsfaktor des Lawson-Modells von AT/RH = 82,5 
 
2. Die Parkdauer im Feld beträgt tFeldParken = 131.400 h. 
Damit ergibt sich die Gesamt-Prüfdauer mit Gleichung (6) als: 
 
 
 Stunden
1593
82,5
 Stunden
131400
t Prüf


 
 
 


### 第 162 页
Seite 162 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Anhang G (informativ) 
 
Betauungsprüfung, Kammerprogrammierung und Diagramme 
 
 
Bild 52: Programmierung des Prüfschrankes 
Während des Temperaturanstiegs wird die Temperatur des Wasserbades als 
Regelgröße verwendet. Bei Erreichen der 80 °C wird auf Temperaturregelung der 
Klimakammer umgeschaltet (normaler Betrieb). 
 


### 第 163 页
 
  
Seite 163 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Bild 53: Ablauf der Betauungsprüfung, 1 Zyklus 
1. 
geregelte Wasserbadtemperatur 
2. 
sich ergebende Prüfraumtemperatur 
3. 
Ist-Luftfeuchtigkeit in der Prüfkammer 
 
1 
2 
3 


### 第 164 页
Seite 164 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Bild 54: Ablauf der Betauungsprüfung, 5 Zyklen 
 
 


### 第 165 页
 
  
Seite 165 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Anhang H 
 
Untersuchungsmethoden zur physikalischen Analyse 
 
• 
Weiterdrehmomente (z. B. Gehäuseverschraubung, Befestigungsschrauben auf 
dem Vibrationstisch, ...) 
• 
Lötstellendefekte  
• 
Bauelemente- / Leiterplattenverfärbungen (im Speziellen thermisch bedingte)  
• 
Schwer-/ Leichtgängigkeit, Schleifen, Spiel (bei mechanisch bewegten Teilen)  
• 
Abriebspuren  
• 
Sprünge, Risse, Verformungen von Materialien (im Speziellen bei Verguss- und 
Dichtstoffen). Eine geeignete Prüfmethode (Röntgen, CT, Schliffe,…) ist hierbei in 
Abstimmung auszuwählen  
• 
Trübung (insbesondere von Teilen optischer Sensorsysteme) 
• 
Zustand von Verrastungen und Verklipsungen  
• 
Korrosions- und Migrationsspuren 
• 
Bewertung von Kunststoffen auf die Hydrolysebeständigkeit (Insbesondere bei 
Komponenten mit eingelegten Stanzgittern und Kl. 30 Beschaltungen)  
• 
Beschädigung von Durchkontaktierungen von Leiterplatten, insbesondere 
Thermovias  
• 
Beschädigung der internen Anbindung (Paddles) von großen 
Elektrolytkondensatoren nach mechanischer Belastung (Vibration, mech. Schock, 
Falltest)  
• 
Steckerpinbeschädigungen (z. B. durch Strom, Temperatur, Reiben, Oxidation)  
• 
sonstige Auffälligkeiten  
• 
ICT-Ergebnis (In-Circuit-Test) 
 
Bewertung von Dichtflächen auf korrosive Unterwanderung: 
 
Unterwanderungen sind grundsätzlich unzulässig 
 
Unterwanderungen ≤ 50% der Dichtfläche sind mit dem Auftraggeber 
gemeinsam zu bewerten 
 
  
 Bild 55: Dichtgeometrien 

