# A 000 006 98 99_CH_2015-08_乘用车原理图编制.pdf

## 文档信息
- 标题：
- 作者：工程部-曹子菡
- 页数：40

## 文档内容
### 第 1 页
实施细则 
 
梅赛德斯奔驰乘用车原理图编制 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
                              版本 7.0 
 
 


### 第 2 页
发布 
专业领域/作用 
发布 
姓名 
日期 
签字 
戴姆勒公司原理
图纸编写人 
Redinger 
2010-07-28 
 
戴姆勒公司 E3 
专业领域 
Dauner 
 
 
 
 
变更索引/变更描述 
图纸编制过程实施细则更改文件 
ZGS 描述 
修改人（日期） 
批准（日期） 
戴姆勒公司 
戴姆勒公司 
001 
新建 
2007-08-10 
Drescher 
 
002 
-删除 LCable 
-调整 E3.Cable 
-更新了数据输入 
-增添了接线图布局 
2009-11-20 
Redinger 
 
003 
-纠正了绝缘位移连接器的端子形式并 
添加了开放式电缆的端子表 
-更新和扩展参考章节中的连接器名称和连
接器类型 
-在插头形式中重新命名“端子形式”一章 
-在 PDF 项目中添加“向 MBC 提供数据”一
章 
-取消“变更管理/版本控制”和“数据状态”
章节  
-将图纸名称更改为图纸标题，删除命名，扩
展图纸标题的描述 
-将“项目结构”一章添加到其他地方 
-删除“导线号”一章中的特定内容 
-在“线路名称”章节，添加了导线和特殊电
缆的详细信息。 
-在“零件图例”一章中，补充该系列的所
属关系。 
2010-07-28 
Redinger 
 


### 第 3 页
-在“可选属性”一章中，删除安装区域并
描述了新章节。 
-通过生产代码扩展 “线路编码”章节 
-在“物料号”一章中，删除发行相关的物
料号格式 
-在“各个电路图文件名称”和“项目系列
文件名”一章中调整系列项目格式“和更改
日期。 
004 
各章中的更改： 
-原理图导出的文件名 
-物料号格式 
-技术图纸 
-编辑日期和数据状态 
-分离点和电缆尾部组件的参考 
-端子和信号名称 
-系列名称，设计和车辆阶段 
-可选属性 
-向 MBC 提供数据 
-原理图 ELOG 文件 
 
新章节： 
-外部更改字段 
-特殊电缆的通用名称 
-孔位规格 
-护套表示 
-仅供参考 
-组件图例中的运用 
-系列名称，设计和车辆阶段 
-提供电路图库的数据 
-电路图库文件名 
-保险丝描述 
2012-02-17 
Redinger 
 
005 
章节的调整： 
-参考文献扩展到 SRS 测试 
-连接器名称章节改为插头形式 
-扩展项目结构 
-指定端子和信号名称 
-提供 MBC 数据 
-端子外壳的孔位名称 
 
新章节： 
-气动线 
-未压接屏蔽层 
-端子外壳上的屏蔽层 
-端子形式 
2013-07-16 
Redinger 
 


### 第 4 页
-实施方案/设计 
-总电路图文件名称 
-项目中小型供应商的电路图范围 
006 
章节中的更改： 
-项目结构 
-参考 
-连接器名称 
-插头名称 
-端子外壳上的孔位标记 
-“屏蔽层未压接”更名为“未压接屏蔽层” 
-MBC 的数据提供 
-原理图 ELOG 文件 
-缩写词和术语 
 
新章节： 
-回路 
- 48V 导线的标记 
-iSPM（包括子章节） 
-通用出口 
2014-02-07 
Redinger 
 
007 
各章中的更改： 
-未压接屏蔽层 
-端子外壳上的屏蔽连接 
-组件图例：双语名称（德/ 英） 
-设备和位置标示图句法的定义 
-数据状态 
-电路图编号 
-调整屏蔽描述 
-向 MBC 提供数据 
 
删除章节： 
-ELOG 输出 
-特殊电缆的通用命名 
-数据库 
2015-08-28 
Redinger 
 
 
 
 
 
 


### 第 5 页
 
目录 
变更索引/ 变更描述.............................................................................................................3 
目录........................................................................................................................................6 
1 范围....................................................................................................................8 
2 设计任务书的对象....................................................................................................8 
 2.1 一般要求....................................................................................................................................................8 
 2.2 目的 .............................................................................................................................................................8 
 2.3 任务分配 ...........................................................................................................................................9 
 2.4 过程可靠性..........................................................................................................................................9 
 2.5 技术 ............................................................................................................................. ......................9 
3 电路图内容...........................................................................................................................10 
 3.1 绘图框和图纸打印.........................................................................................................................10 
 3.2 设计图纸.......................................................................................................................................10 
 3.3 仅供参考................................................................................................................................11 
 3.4 项目结构......................................................................................................................................12 
 3.5 外部更改字段 .......................................................................................................................12 
 3.6 电路图视图 .............................................................................................................................13 
 3.7 概述 (分配视图) ...................................................................................................13 
 3.8 图纸描述...................................................................................................................13 
3.9 原理图的布局..........................................................................................................14 
3.10 图表描述 ........................................................................................................................15  
3.11 实施方案...................................................................................................................................16 
3.12 参考...............................................................................................................................................17 
3.13 接插件名称............................................................................................................................. ............19 
3.14 连接器命名 ..............................................................................................................................20 
3.15 端子形式 ............................................................................................................................................20 
3.16 端子护套孔位标记 ..................................................................................................20 
3.17 保险丝描述...........................................................................................................................21 
3.18 导线名称.......................................................................................................................21 
3.18.1 导线颜色 ............................................................................................................................. .......22 
3.18.2 线号...............................................................................................................................22 
3.18.3 导线命名 ..........................................................................................................................23 
3.18.4 48V 导线的标记 ............................................................................................23 
3.19 导线编码.................................................................................................................................23 
3.20 电缆描述 ....................................................................................................................................24 
3.21 端子和信号名称................................................................................................25 
3.22 组件图例......................................................................................................................................25 
3.22.1 参考资料 ..........................................................................................................................................25 
3.22.2 代码 ....................................................................................................................................................26 
3.22.3 运用 .......................................................................................................................................26 
3.23 等级概念 .......................................................................................................................................26 


### 第 6 页
3.24 孔位描述........................................................................................................................................26 
3.25 未压接屏蔽层.............................................................................................................27 
3.26 端子护套上的屏蔽接口 .............................................................................................28 
3.27 液压和气动量 ..................................................................................28 
3.28 回路................................................................................................................................................29 
3.29 项目中小型供应商的电路图范围 .......................................................29 
4 属性..........................................................................................................................................29 
4.1 可选属性 ..................................................................................................................................29 
4.1.1 系统组............................................................................................................................. ......30 
4.2 属性指标 ........................................................................................................................................31 
5 iSPM................................................................................................................................................ 32 
5.1 定义....................................................................................................................................................32 
5.2 一系列过程............................................................................................................................................32 
5.3 内容.............................................................................................................................................................33 
5.3.1 项目结构.......................................................................................................................................34 
5.3.2 设备标识符和位置标识符的应用 .......................................................................37 
5.3.3 未定义孔位..................................................................................................................37 
5.3.4 质量，保险丝，底座..................................................................................................37 
5.3.5 电缆设计...............................................................................................................................38 
5.3.6 代码 ........................................................................................................................................................38 
5.3.7 信号......................................................................................................................................................38 
6 数据提供......................................................................................................................39 
6.1 向 MBC 提供数据 .............................................................................................................39 
6.2 MBC 提供的数据 ........................................................................................................41 
6.3 为模块线路提供数据......................................................................................41 
7 数据格式和命名规范.............................................................................................42 
7.1 数据格式 ...................................................................................................................................42 
7.2 命名规范......................................................................................................... ...................42 
7.2.1 系列名称，设计和车辆阶段............................................................................................42 
7.2.2 零件编号格式..................................................................................................................43  
7.2.3 各个电路图的文件名 ...................................................................................................44 
7.2.4 系列项目的文件名.........................................................................................................45 
7.2.5 总电路图的文件名.......................................................................................................................45 
7.2.6 文件名库和软件扩展名......................................................................................................................45 
7.3 通用导出 .................................................................................................................................46 
8 相关文件..............................................................................................................47 
8.1 名称和规范...................................................................................................................47 
8.2 缩写和概念............................................................................................................47 
8.3 图表目录......................................................................................................................49 
9 附录............................................................................................................................................50 
 
 
 
 


### 第 7 页
 
 
1 范围 
这些规范包含戴姆勒股份公司，MBC 部门或其后续组织指定的规范电路图创建的要
求。这是为设计人员创建 MBC 部门所有车辆系列的线束开发原理图的强制要求。 
 
此处描述的内容描述了线束开发电路图的 MBC 的内容和方法要求。 
 
本规范未描述技术内容以及由 MBC 的规范表，标准和程序规定的事实。 供应商必须
遵守所有执行法规和标准，并始终使电路图适应当前状态。 
 
如果这些规范中没有描述或没有偏离完美文档的要求条件，则必须向 MBC 报告。 
 
与这些规范要求的所有偏差均需获得 MBC 的书面同意。 
 
2 设计任务书的对象 
规范的主题是用于产品开发的电路图的使用，内容和执行，其内容的描述以及作为产
品描述的数字 ELOG 和 PDF 文件的执行以及对线束开发中过程可靠性的要求。 
 
2.1 一般要求 
原理图和原理图原始数据是 MBC 的属性，适合内部使用 MBN 31 002。 RD / EKL 提供图
形框架和标题，库，字体和图层的模板文件，组件，参考，代码和线数据以及软件的必要
附加功能。带有 ConnyE 插件的电路图编辑器 E3.Cable，以下也称为电路图编辑器，用于
与 CONNECT 进行数据交换使用。 
 
电路图的内容在第 3 章“更改索引/更改说明”中进行了描述。公司特定的信息仅允许在约
定和约定的属性中使用，或者在部署到 MBC 之前将其删除。 
 
2.2 目的 
这些规范的目的是确定线束开发的开发计划的内容和形式；描述基本内容，并按照 MBC 的
文档指导创建统一的电路图外观。 
 
另一个目的是确保开发结果在开发中以及装配、售后与外部开发合作伙伴之间的互换性。 
 
2.3 任务分配  
负责在 MBC 内执行电路图及其技术内容的调试区域，在 RD / EK 电缆领域负责部门 RD / 
EKL，RD / EKW，在动力总成部门负责 RD / PEW 区域，在高压区域负责 RD / PEW 部门。 
在 EKS 以及 AMG 的 AMG / EBSE 部门和发动机部门负责置 AMG / ET14 及其后续组织。 
 
2.4 过程可靠性 
电路图列表构成了线组开发过程的逻辑默认值，并确定了线组的电气连接。 
 


### 第 8 页
网表必须从电路图自动生成，并用于进一步的开发过程。 
 
为了确保将系统中的电路图明确分配给生成的网表和图形文件，它们必须对应明确的
命名规范和已定义的数据格式（另请参见第 7 章数据格式和命名规范）。 
 
为确保过程的可靠性，供应商首次使用电路图工具受到 RD / EKL 的审查。 
 
2.5 技术  
电路图工具的使用通常由供应商开发合同规定，只有在与负责线束系列的团队协商后
才允许有偏差。 
 
用到的的软件版本，软件扩展，库和模板必须是 MBC 认证和批准的软件版本。涉及的
软件/版本以及实施日期必须与 RD / EKL 部门的“ DMU 和工具”小组协调。  
 
RD / EKL 发布的电路图编辑器所需功能的增强是通过负责的布线操作来提供的。 
 
在第 7.2.6 章文件名库和软件扩展中进行了概述。 
 
3 电路图内容 
3.1 绘图框和图纸打印 
根据 MBN 31 020 的规则使用 MBC 标准图纸格式、图纸框架和图纸字体字段。 RD / 
EKL 通过负责的电缆接线员为发布的电路图编辑器提供了相应的图纸框架和图纸字体
字段的选择。 
 
3.2 技术图纸 
命名 
电路图纸的命名必须用德文和英文描述系统和功能电路图的内容。 
例子 
                Schaltplan SAM/SRB hinten 
wiring diagram SAM /SRB back 
电路图编号 
每个电路图都必须有一个电路图编号（详见 7.2.2 章节中的编号格式）。此处不引用纸
张编号。 
纸张编号 
引用纸张编号且编号不为 0。 
编辑者 
必须在两个编辑字段中输入日期，并在名称的公司缩写和编辑者缩写中输入日期。 
日期 
每份图纸的日期必须采用 年-月-日 的形式。例如：2010-07-20 
数据状态 
对于数据状态，输入进行更改的日期。该日期也是接线图和线束 KBL 中接线图的数据
状态。只能在发现更改的电路图页面上更改数据状态。 
发行信息 


### 第 9 页
如果是 C1 / iSPM 项目，则在图纸头上方显示一个附加属性，图纸即源自此 C1 项目
（工作/公共）。 
 
          图 1：包含说明的绘图头 
 
3.3 仅供参考 
所有电路图在电路图图例中均以水印形式收到附加文本“仅供参考，不得更改服务不
受控制”。这是为了说明它们不是已发布的文档。 
 
3.4 项目结构 
系列项目的设置应类似于 iSPM 结构中的规范（请参阅 iSPM 章节：5.3.1 项目结构）。 
主组和组是固定的，子组可以根据需要扩展。 
 
不需要的其他结构阶段或结构取决于所使用的系统，并且必须与负责线束系列的团队
进行协调。但是默认情况下，出于清晰起见，不应没收更深层的子级别，并且原理图
应包含在下面。 
 
将主要组添加到系列其他区域/系列的例外，例如：作为 AMG 范围，必须予以记录。 
如果该系列和 AMG 的绝大部分电路图范围相同，并且只有少数电路图不同，则这是有
道理的。这样可以确保项目中的线号是唯一的，并且可以不重复地传输到接线图。
AMG 的不同电路图位于 AMG 目录下。当然，只需要创建还包括示意图的结构。如上
数据状态 
编辑日期 


### 第 10 页
所述，也适用于相同的结构规则。 
 
项目数据的指定：请参见第 7 章文件格式和命名规范。 
 
 
3.5 外部更改字段  
为了获得完整的变更历史记录，在外部变更字段中非正式地描述了中间和串行销售订
单或未发布原理图的所有变更。如果有变更项目，则应列出。 
 
外部更改框始终位于工程图标题的右侧，位于工程图框架之外，并由日期，更改文本
和编辑器字段组成。 
 
要创建批准图纸，在打印前会截断或隐藏非正式更改字段。 
 
                      表二：外部更改字段 
 
 
3.6 电路图视图  
如果未不负责线束建设的团队达成其他协议，则必须将接线图创建为系统接线图。电
路图应连贯并完整描述系统。 
 
相应的信息应尽可能显示在其他电路图表上，且无信号交叉引用（端口）。如果需要信
号交叉引用，则必须与负责线束系列的团队协调该程序，或者为避免这种情况，请针
对特定工具诸如以下功能视图，双针等表示。 
 
3.7 概述 (分配视图) 
分配视图必须创建主题尺寸，CAN，插值点，保险丝。所需的概述必须与负责线束构
建的团队达成共识。由负责线束系列的小组与区域 GSP 进行协调。 
 
3.8 图纸描述 
原理图和数据库编辑器的基本设置（例如层，网格，文本大小，文本字体等）在 E3 层
概念中描述，并在模板文件中定义。 
 
项目设置（请参见第 7.2.6 章文件名库和软件扩展名）通过负责的操作员在交换目录中
作为文件提供。 
 
电路图 电子点火开关 23 1-电动汽车-00-06.1 
变更号 和描述 
日期 


### 第 11 页
标签，字体和字体大小应按照 MBN 31 002 执行。 
 
3.9 原理图的布局 
 
                 图三：电路布局 
电路布局图分成以下三个方面： 
上方区域： 
 
控制仪  
 
系统确定装置 
中间区域： 
 
分离点 
 
支撑点 
 
接地位置 
下方区域： 
 
传感器 
 
执行器 
 
保险 
 
支撑点 
 
接地位置 
 
信号交叉引用（仅适用于特殊情况） 
电路图的逻辑部分是根据以下标准创建的： 
 
线路演示不应交叉 
 
电路图可以面向功能或基于 ECU。此处将对功能进行完整的描述，包括电源，地
线和总线。 
 
控制单元，传感器和执行器的每个插针都包含可读的信号或端子名称（根据 DIN 
72552 的端子名称）。 
 
线路最多绕两个电路图。 
 
对于保险丝，必须输入备用值。 


### 第 12 页
 
应避免信号交叉引用，因为电路图的可读性会大大降低。 
 
电路图中组件的内部布线可复制，必须在很大程度上确保对信号的跟踪。 
 
控制单元不需要详细表示内部接线的。 也可以选择链接外部文档。 
 
对于用户来说，如果可以改善接线图的可读性，则显示内部接线。 
 
扭曲的屏蔽电缆应以图形方式显示。 
 
插头的匹配销应带有边框以表示其整体性。 
 
插头不应在电路图上多次显示。 
 
在项目开始之前，必须与 MBC 的调试部门协调并定义电路图的内容及其布局。 
 
3.10 图表描述 
对于图形表示，仅使用 RD / EKL 批准的库的内容。在该库中，根据模板文件中指定的
设置提供了符号，块，子电路和属性指示器。 
 
必须使用 ConnyE 的功能对电路图中使用的组件和电路图内容进行标注，例如标注和
占用视图。 
 
符号库通过负责的操作员在交换目录中作为数据库副本提供。该库的命名规范在
“ 7.2.6 文件名库和软件扩展”一章中进行了描述。 
 
         图 4：扭曲和屏蔽图 
 


### 第 13 页
3.11 实施方案 
 
         图 5：设计的图形差异           
 
如果要在示意图中清楚地显示不同版本的车辆功能（另请参见第 4.1.1 节“系统
组”），则可以通过框架针对每种设计以图形方式将其汇总。另外，该实施例的缩写在
左上方的框架中示出。连接属性可用于输入设计以控制系统组中的线束。 


### 第 14 页
 
   
  图 6：连接属性中的设计 
 
3.12 参考 
电路图中使用的组件参考及其长名称必须符合 VZK（使用目的目录）的要求。MBC 通
过负责的操作员将此目录作为 CONNECT PARTS 数据库的数据导出提供。 
 
                  图 7：连接器参考 
原理图中组件参考的写法： 
<ref><trenner1><steckername><trenner2><Steckerform><trenner3><AKZ><trenner
4><OKZ> 
 
<ref> = Kurzbezeichnung des Verwendungszweck Katalogs 用途目录的简短描述 
<trenner1> = „*“ 
<steckername> = Steckername (max. 3 Stellen) 插件名称（最多 3 个？） 
<trenner2> = „-„ 
<trenner2> = „-„ 
<Steckerform > = Unterscheidung der Kontaktierungsseite, z. B. S=Steckerseite, 
B=Buchsenseite <插头形式> =接触侧的差异，例如 S =插头侧，B =插座侧 
如果有必要，如果需要（有关更多详细信息，请参见第 5.3.2 节“系统和位置代码的使
用”） 
<trenner3> = „_“ 
<AKZ> = V[Zahl]   V 数字 
<trenner4> = „_“ 
<OKZ> = O[Zahl]  O 数字 
 
例: 
N10/1*2-B 
N10/1*2-S 
N10/1*2-B_V1_O3         
如果未输入连接器名称，则将自动分配连接器“ 1”。 
名称 
登记 
缩写符号 
线号 
系统组 
参考 
插头名称 
插头形式 
 插头名称 
设备标记 
地点标记 


### 第 15 页
为了确保组件和连接器名称的一致性，必须使用 ConnyE 工具对参考名称和长名称
（DE / EN）进行修改。 
分离点上的引用在 REF 区域和连接器名称中相同。凹形外壳的插头形状= B，插头外壳
的插头形状=S。 这确保了插座壳体可安装在相应的连接器壳体上。 
 
对于带有电缆尾部的组件，触头外壳一侧的 REF 与组件相同。 如下例所示，行程传感
器后盖 HFS 标记为 REF = B24 / 16。插头端带有 REF，但以插头名称和插头形状（B24 
/ 16 * 1-S）进行补充。 
 
必须遵循以下参考前缀来检查 SRS 导线： 
 
爆管 
R12/*,即使它们作为电缆尾部组件存在 
气囊- 控制仪 
N2/* 
转向柱开关模块 
N80 
热保险丝 
K88, F108 
 
     
 
图 8：分离点                    图 9：电缆尾部组件 
该系列应采用 iSPM 的参考，必要时必须重新启用。 另请参见第 5 章主要章节 iSPM。 
 
注意：DE / EN 中的参考长名称在连接器上继承，可以通过组件属性进行查看。 对于
NX-Bridge，长名称是必需的，并且在连接器的接线图中默认情况下未显示长名称。 


### 第 16 页
 
 
 
 
3.13 接插件名称 
由于插头名称必须与语言无关，因此此处需要简单的命名。连接器名称将从 iSPM 中获
取。如果需要进一步的更改，则应在它们前面加上“ _”，然后再写上附加词。 
 
这是该扩展插件名称的示例： 
 
  图 10：带有接插件名称的更新 
 
未通过 iSPM 定义的接插件名称，则必须以与 EPDM（电子产品数据管理）相同的方式
进行选择。对于插头型号的扩展，适用以上原则。 
对于仍然不同的体系结构或硬件版本和安装位置，接插件名称将由系统和位置代码扩
展（请参见第 5.3.2 节“系统和位置代码的使用”）。系统标识符由 iSPM 集中指定。 
 
以下标识是可行的： 
- 0-9 
- A-Z 
- _ 
3.14 接插件命名 
接插件名称用于区分不同的触点载体或端子形式，并在后续工具中控制它们的处理。 
例: 
插座外壳 
X10/1*2-B 
插座外壳 
X10/1*2-S 
电缆接头 
W10*1-K 
电缆块侧 
W10*1-KB 
电缆连接器（例如，焊接连
接器） 
Z10/1*1-L 
电缆连接器块侧  
Z10/1*1-LB 
绝缘位移连接器 
Z20*1-T 
电缆端 
Z30*1-C 
电缆端块侧  
Z30*1-CB 
从 iSPM 中获得的插头名称 
电路图组件中带有接插件名称的变体 
 


### 第 17 页
 
3.15 端子形式  
端子形式用于区分不同的端子或端子形式，并控制其在后续工具中的处理。 
例子: 
LWL- 插头 
X 
LWL- 护套 
Y 
Flachbuchse 扁护套 
F 
Flachstecker 扁插头 
E 
Rundstecker 圆形接插件 
O 
Rundbuchse 圆形护套 
R 
Pneumatik 气动工具 
P 
Kabelschuh 端子 
K 
Lötverbinder 焊点 
L 
 
3.16 端子护套孔位标记 
对于端子外壳的插针标记，该规则适用于该标记取自相应零件的释放图。 此规则有以
下例外情况： 
 
单极端子外壳，在释放图中没有指定孔位的位置，其孔位名称始终为 x。此类组件
的示例是单接线的电缆接线头保险丝或超声波焊接。 
 
组件，例如组件图中未指定孔位的保险丝座，继电器等均配有孔位 E =输入或 A =
输出。继电器孔位获得端子名称。 
 
没有针号的同轴插头用小“ x”和大“ S”号针表示，在有多个腔室的插头中，孔
位标记为 x1，S1，x2 和 S2。孔位= x 是线路连接，孔位= S 是屏蔽。 
 
如果接收到 HSD 连接器，则载流电缆的插针应从 1-n 开始连续编号。如果仅存在
一个绝缘层，则将其标记为 S。如果使用了多个绝缘层，则这些绝缘层编号为 S1-
Sn。 
 
安全气囊连接器的销钉是根据连接器释放图中的气室名称定义的。 
 
创建电路图时，请确保组件孔位和所连接的线束插头已正确插入。单纯的图形接触是
不够的，并且在导出电路图数据时可能导致错误。 
 
3.17 保险丝描述 
保险丝将由库中以下所示的组件表示。除了参考的通用名称，连接器编号，插头形式
和插针描述外，还必须显示特定于保险丝的特性，例如保险丝位置，端子和保险丝
值。此内容也已导出。 


### 第 18 页
图 11：保险块 
 
3.18 导线名称 
为了确保线路名称和所需属性的一致性，必须使用 ConnyE 工具进行线路的修改。 
 
公司特定的或所谓的虚拟信息，例如 如果线束中仍存在电缆或特殊电缆，则线束开发
阶段可使用线型“ YY，XX”开发中，没有发布。当线路的空白字符释放设置不具有虚
设连接信息可以存在于该电路图更多。偏差需要负责的线束系列团队的同意和批准。 
 
使用 E3.Cable 软件时，必须使用 E3 库的“属性载体”属性块。 电路图中的可见线名
称包含线号，对于特殊电缆，其内部线号，线型，横截面，颜色以及必要时的编码。 
应该更多信息是必需的，因此会相应地调整 line 属性。 
 
 
3.18.1 导线颜色  
线条颜色根据 IEC 60 757（大写英文缩写）执行。除 IEC 60 757 之外，还为屏蔽层，
填料绝缘线，非绝缘和自然色电缆定义了以下术语和缩写： 
德文 
英文  
缩写 
Schirm  
Screen  
SC 
Beilauflitze  
drain wire  
DW 
Natur  
Nature  
NT 
Nicht isoliert  not isolated  NI 
 
3.18.2 线号 
线号由整数组成。 
为了能够将电缆的电线，屏蔽层或延长线直接分配给电缆，多芯电缆的电缆号用电线
指示器上的 1-2-位电线标记补充。 
例子： 
 
线号 
1000 
电缆编号 
1000 
电缆芯 
1000_10 (复合描述) 


### 第 19 页
 
 
                    图表 12：线号 
在导出期间，线号和电线标记具有不同的属性。 
                   
3.18.3 导线命名 
为了使电缆的名称清晰（= Id），有必要区分电表销售和电路图上的特殊电缆。 
 
仪表的唯一键是属性、横截面和颜色的复合值。颜色由基本的第二和第三颜色组成。第三
种颜色不应常规使用。 
该缩写在正常语言中也用作行类型。例如 Iy 是护套绝缘材料和线结构的组合。 
写法： 
<缩写>-<横截面>-<颜色> 
例: 
<Iy-0.75-GR/YEGN> 
特殊电缆的名称也由特殊电缆的缩写、横截面和特殊电缆中导体的颜色组成。与导线的区
别在于，特殊电缆的缩写由用来材料分组的识别字母和一个连续的数字组成。也可选择在
不同的护套颜色或不同的设计中补充其他内容。  
写法： 
<缩写>-<单芯线横截面 1 -n>-<彩色单芯线 1 -n> 
例： 
<B48/3-0.75-GR/YE> 
 
3.18.4 48V 导线的标记 
48V 车辆电气系统的线路应这样标记。 因此在线上提供了一个单独的属性。 
 
3.19 导线编码 
如有必要，电缆随附有 Connect 提供的代码。应该使用 IP 代码。如果 Dialog 没有提供


### 第 20 页
所需系列的 IP 代码，但使用的系列不同，则可以解锁 CONNECT 中所需系列的 IP 代
码。仅在特殊情况下，才使用 U-编码。 
 
该代码的使用或重新分配应与负责的线束系列小组协商。 
 
可以根据 ConnyE 的规范使用布尔运算进行行编码。使用 E3.Cable 对代码及其布尔运
算符的数量没有限制。 
 
经与系列团队协商，可以使用“U972”标记标准设备的生产线。 
 
3.20 电缆描述 
除使用的单芯电缆外，电缆、绞线或其他特殊电缆还应通过适当的图形符号标识。 
 
  图 13：扭绞符号     图 14：绝缘层图标 
 
3.21 端子和信号名称 
将根据 DIN 72552 或 EPDM 的端子和信号列表，以要求的端子或信号名称来描述图示
的连接端子。 
控制单元，传感器和执行器的每个孔位均包含可读信号或端子名称；如有必要，必须
相应安排内部电路图。 
 
在电气连接的线路上，必须在连接属性中填写信号属性。 


### 第 21 页
 
   图 15：信号和端子名称 
 
3.22 组件图例 
组件参考（第 3.12 章）和使用的代码应使用预期目的目录的德语和英语的详细说明在
图例中进行解析。必须在 CONNECT 中为代码和引用都分配了该系列的条目。在每次
导出数据之前，必须更新此组件图例。 
 
图例位于标题栏上方图形的右侧。 
 
长名称中的条目“ **未找到条目**”表示必须在 CONNECT 中输入使用目录或代码中
的条目，否则未分配任何序列。应避免该条目。 
 
3.22.1 参考  
格式： 
<参考>     <长名_德>      <字段标识符列表> 
<长名_英> 
例如： 
A1               Kombiinstrument（de）仪表        20N  
                 Instrument cluster (en) 仪表     
X20 / 1   Steckverbindung 3. Bremsleuchte （de）插头连接 3.刹车灯  3A 20B 30J 
            Connection 3. Brake light  (en)  
 
 
3.22.2 代码  
格式： 
<代码>       <长名称 De>      <字段识别列表 > 
<长名称 En> 
例子 : 
IP443        Lenkrad heizbar（de）      30B,56C 
heated steering wheel (en) 
导线信号名称 
孔位终端 


### 第 22 页
U20      Gültig für alle außer USA/Japan（de）   2A,16C 
applicable to all without USA/Japan (en) 
 
3.22.3 应用 
运用是指将电路图分配给系列，实施例和车辆阶段（另请参见第 7.2.1 章系列的名称，
设计和车辆阶段） 
格式： 
<代码> <系列> <类型> <车辆阶段名称> 
 
例子: 
C231 FC AJ2016.1 
C205 FW EF2.BL2 
 
3.23 等级概念  
等级概念是由 RD / EKL 部门集中创建、编辑和提供的。该措施为所有开发合作伙伴
（而不是公司特定的解决方案）创建统一的模板。 
 
3.24 孔位描述 
有些接插件可提供多个孔位编号。 
   
图 16：扩展槽描述 
从附图中可以看出，有一个孔位 A 和 B，它们都具有孔位 1 和 2。为了能够区分孔位，
孔位名称由孔位和孔位编号组成。在此示例中，孔位 A 有孔位名称 A1，A2，A3，
A4，孔位 B 有孔位名称 B1 和 B2。 
 
对于基于护套的新组件，流程如下：引线组供应商首先使用，将零件展示给其他引线
组供应商，然后创建并分发符号。所有带有护套显示的组件都应显示在总览图上（请
参见图 30：护套组件概述），并分发给所有线束供应商。 
  
3.25 未压接屏蔽层 
对于屏蔽层未连接到端子壳体的特殊屏蔽电缆，接线图必须使用电缆端部组件（E3。
电缆符号：组件→电缆端部）记录屏蔽层的开口端。这些可以在接线图中测量。屏蔽
显示为一行。屏蔽图标无法通过屏蔽线拉出。 
图例： 


### 第 23 页
 
图 17：未压接屏蔽层  
 
3.26 端子护套上的屏蔽接口 
HSD 和 Fakra 连接器的屏蔽电缆在触头外壳上接触。电路图中显示如下。屏蔽图标无
法通过屏蔽线拉出。 
 图 18：外壳上的屏蔽连接 


### 第 24 页
3.27 液压和气动量  
线束中有一些例如气动或液压管线，不在线束开发的责任范围内。 
 
以下规则适用于电路图： 
-接线图中不得混用电气和气动/液压互连 
-不得为气动/液压回路图的图纸标题中线束开发的部门名称。 必须输入负责部门的部
门名称。 
-不得在“连接”中设置气动/液压回路图。 
 
线束开发部门对这些气动/液压接线图的准确性不承担任何责任。 
 
3.28 回路 
出于安全方面的考虑，线路图中的回路或更改的布线方式不允许自定义要求。 因此将
路由点添加到原理图中的线。不得将此过程用于线束供应商的与生产相关的安装路
线。对于环形结构（双 H），必须手动将确切的布线路径分配给该线路，以获取电缆
（KBL）中的实际线路长度，也就是说，能使用路由点。 
 
           图 19：回路描述 
 
3.29 项目中小型供应商的电路图范围 
当授予一系列的线束范围时，通常会使用小型特殊设备供应商，例如 Parktronik 电
缆。由于这些体积也用于主电缆组中，并防止线号重复，因此这些范围必须集成到该
系列的电路图项目中。方式应与系列团队商定。 
 
4 属性  
MCG 软件扩展 ConnyE 的使用指定了强制属性的内容，例如名称，连接中的电缆/电
线，横截面，颜色，代码等。 
 


### 第 25 页
4.1 可选属性     
尚未根据内容定义可选属性功能。有关其他可选属性的要求，请联系 RD / EKL 中的
“ DMU 和工具”团队。然后根据需要扩展属性指示符，并在更新过程中将其提供给所
有外部合作伙伴。 
 
当前有以下可选属性： 
 
接触面和 PosNr 用于接触 
 
触点外壳中的零件号 
 
电缆系统组 
 
可选属性在原理图上不可见。 
                           
4.1.1 系统组 
线束供应商使用“系统组”属性来控制在哪些主线集中控制哪些线。可以将系统组属性分
配给行，但是它是不可见的。可以包含以下内容： 
 
铺设面积 
 
执行类型 
 
转向模式 
 
自定义内容 
 
以下术语已标准化： 
放置区域： 
缩写 
描述 
COC  
Cockpit 驾驶舱 
MRA  
Motorraum 发动机舱 
MOT  
Motor 发动机 
DACH  
Dach 顶棚 
MIKO  
Mittelkonsole 中控台 
TUER  
Tür 门 
SITZ  
Sitz 座椅 
GET  
Getriebe 传动装置 
HECK  
Heck 车尾 
HECKD  
Heckdeckel 后盖 
HECKK  
Heckklappe 尾门 
HA  
Hinterachse 后轮 
STO  
Stoßfänger 保险杠 
BOD  
Boden 地面 
VERD  
Verdeck 车顶 
RBA  
Rahmenbodenanlage 地板总成 
 
实施方案： 
缩写 
描述 
W  
Limousine 大轿车 
EV 
Elektrofahrzeug 电动汽车 


### 第 26 页
S  
Kombi 客货两用汽车 
C  
Coupé 双门小轿车 
A  
Cabrio 敞篷车 
X  
Sonderfahrzeuge 特种车辆 
V 
Verlängert 加长 
 
 
缩写  
描述 
LL  
Linkslenker 左侧驾驶汽车 
RL  
Rechtslenker 右侧驾驶汽车 
 
自定义内容： 
在系统组的末尾，您可以添加自定义内容。 
 
如果其中几个控制功能是必需的，则必须将它们串联为“ +”。对于空白区域，无需显
示。 
 
写法： 
<放置区域> + <执行类型> + <转向类型> + <自定义控制功能> 
例子： 
COC+W,V+LL+ICE 
COC,MRA+LL 
 
4.2 属性指标  
属性查看器定义了所有必需的属性。 属性查看器作为 MCG 库的一部分提供。 属性显
示中的更改可以作为 E3.Cable 中标准功能的更新接受。 
                            
5 iSPM                      
5.1 定义 
iSPM（集成的基础电路图管理）是 RD / EKL 领域中线路集开发领域中面向未来的电路图模
块化系统。 
 
在硬件方面，虚拟车辆的所有与电路图有关的电路图都存储在 E³.C1 电缆中，并用于系列
的初始填充。 
 
由于系统的原因，我们在这里看到了整个系统列表的链接（背景 ISO 26262）和潜在的联
合解决方案。 
 
在虚拟车辆 C1 中，所有已知的与电路相关的电路图都存储在 E3.Cable 中，并用于系列的
初始填充。 


### 第 27 页
          图 20:  使用 iSPM 的线束开发过程 
 
5.2 系列过程 
在已发布的 iSPM 模块化模块接线图中，经过测试的面向系统的基本电路图符合线组开发
中的系列团队进行的初始填充提供了预先开发转移（BMKL）。 这种转移将 200％iSPM 项
目减少到 100％系列基础项目。 系列基础图是为系列开发过程定制，集成和完善系统的基
础。在串联电路图中，例如，记录真实的基础和质量并建立分离点。每隔一定的时间，要
从中创建电缆集的电路图与 iSPM 进行比较，以便能够确定可能的偏差。 
 
如果生产线上出现偏差，则供应商必须描述和记录，例如：为什么要按规定使用另一条线/
颜色/横截面的原因（请参见第 5.3.5 节“线布局”）。 
 
        图表 21: iSPM 流程 
 
5.3 内容 
在 iSPM 中指定了以下与行相关的信息： 
 
包括系统标识符在内的组件的引用（另请参见章 3.12 参考和 5.3.2.1 系统标识） 
 
插头名称 
基础电路图开发 
在 Ispm-PRJ 中 
创建 BSP 
输出 BR 
基础项目 
BR 基础数据 
电路图纸开发系列 
建立BR-PRJ  
BR-数据 
线束开发 
创建线束 
电路图 Doku 
线束 Doku  
在发行的模块化套件的iSPM中面向系
统的基本电路图（虚拟汽车.C1） 
系列基本计划 
系列计划 
调整 
调整 
调整 


### 第 28 页
 
电缆横截面（包括设计标准），颜色和类型（如果需要，可以适应单个目标系列） 
 
  信号名称 
 
线组特定的组件，例如 CAN 分配器 
 
保险丝值和电源端子（如果需要，可以适应单个目标系列） 
 
未指定车辆特定的周长，例如： 
 
提供系统的保险丝盒 
 
接地螺栓 
 
焊接连接器取决于 BR 
 
分离点 
 
如果信息内容与系列示意图不同，下面将对其进行详细说明。 
 
        图 22：基本电路图和 BR 电路图与 KZL 和第三刹车灯的比较 
 
5.3.1 项目结构 
iSPM 原理图项目是来自不同体系结构的最新系统版本的最新版本的集合，这些版本可以用
作特定于模型的原理图的基础。 
电路图的结构按照以下条件进行： 
 
主组 
 组 
 
子组 
 工程师/硬件（HW） 
基础电路图 
BR-电路图 


### 第 29 页
 
            图 23：对系统进行分组的原理 
在子组下，根据架构显示不同的硬件变体。 
 
下面描述的属性在 E3.Cable 中用于结构级别。 这是一个例子： 
 
iSPM 主要组：           01 室内系统 
 
 
iSPM 组：              01 照明 
 
iSPM 系统：             02 尾灯底座一件式 
 
iSPM Architecture_HW：   Star2.3 
 
当前的主要组/子组可以在 iSPM 中找到。 
 
5.3.1.1 主要组 
主要组是来自模块组分类的行业。 
00 室内系统 
01 户外系统 
02 控制系统 
03 UI（Telematics，BAK） 
04 发动机/传动系 
05 特种车辆/个人 
 
5.3.1.2 组 
这些组基于模块组，并与系统列表协调。 
以下是示例： 
01 室内照明 
02 个座位 
03_空调 
04 电气系统 
05 防盗保护 
06_被动安全 
07_舒适度 
08_供给装置 
... 
主组 
组 
子组 


### 第 30 页
5.3.2 设备标识符和位置标识符的应用 
在 iSPM 中描述系统及其 EE 组件时，将检查已知参考，以查看它们对于新系列是否仍然有
效，并在必要时分配 BR = C1。对于新系统，将给出新的参考，以后在系列图中也应使用
这些参考。 
 
作为参考，iSPM 具有用于区分体系结构以及区分 EE 组件的几何层和触头外壳的其他属
性。 
 
所有组件都有一个系统标识符。例外情况是仅在 C1 中有效的例外，例如 X，Z，W，F。为
此，在位置代码之后立即在引用（第 3.12 章）之后。 
 
5.3.2.1 植物识别 
由于可以将不同的体系结构或硬件（HW）用于不同的系列，并且所有系列都应该能够从
同一 iSPM 电路图项目中路由，因此，多个 HW 必须能够同时存在于 iSPM 电路图项目中。 
尽管在不同的原理图页面上绘制了不同的硬件，但 REF 必须能够在不同的硬件之间保持一
致。该资产标签用于不同的硬件，并且在系列基础项目中部署后可用于组件。 为了将组件
追溯到 iSPM，工厂标识符必须保留在序列图中。（有关写法，请参见第 3.12 章参考） 
例： 
F152/1*1 KB_V1 
F152/1*1 KB_V1_O1 
 
5.3.2.2 位置指示器 
如果将带端子外壳的电气设备放置在整辆车的不同位置，则它们到处都应具有相同的
REF。在电路图中，位置代码用于连接器的不同几何位置（不同的安装位置）。（有关写
法，请参见第 3.12 章参考） 
例： 
F152/1*1 KB_O1 
F152/1*1 KB_V1_O1 
 
5.3.3 未定义孔位 
从逻辑的角度来看，可能已知系统，但是由于尚未指定 EE-组件的系列供应商，因此具体
的定义还不确定。此类系统也可以在 iSPM 中进行描述。为此，孔位会获得前缀“ nd”，
然后是 PIN 码。然后必须根据 EPDM 规范在系列图中修改此孔位名称。 
 
5.3.4 接地点，保险丝，支点 
在 iSPM 中对接地点，保险丝和支点进行了空白描述，所以在特定电路的电路图中必须进
行特定说明。 
 
5.3.5 电缆设计 
对于新的 BR，其横截面设计来自概念开发线组，因此已在 iSPM 中采用。在 iSPM 中输入
了最关键的标准，即至少有一个条目。设计标准对于无线电通信局团队的进一步处理很重
要，因此应记录在案。如果从系列基本图中更改了这些规格，则为证明并记录下来。 
 
注意：iSPM 原理图项目中的设计标准仅是建议。强制性的是 EPDM！ 


### 第 31 页
 
在以下示例中，基本电路图指定了 ly-1.5-gn，供应商在 ly-2.5-gn-gn 更改为： 
标准 
计量单位 
iSPM 电路图  
系列电路图 
导线类型 
 
Ly 
 
导线颜色 
 
Gn 
 
导线截面 
 
1,5 
 
导电路径的电压降
的电阻值 
mOhm 
 
 
短路保护保险丝或
Imax 驱动器 
A 
10 
15 
具有激活功能的电
源（leff 或 ipeak） 
A 
0,3 
1,5 
Ipeak 时间 
5 
 
 
接触 
 
 
 
环境因素 
 
 
 
特殊导线 
 
 
 
扬声器电缆设计标
准 
 
 
 
图表 25：电缆设计 
连续过程的条件是，在 iSPM 的基本电路图中至少定义了一个默认值，并且供应商的所有理
据都是空的。 在将基本电路图移交给该系列后，必须由线束供应商检查设计标准，并在生
产线上因规格更改的原因而发生偏差时进行记录。 
 
5.3.6 代码 
默认情况下，iSPM 中不使用 U 代码或 IP 代码。 如果在特殊情况下有此必要，则将授予单
独的“ C1-Code”。 
在序列图中，必须将 C1 代码替换为实际的序列代码，或在必要时进行修改。使用 ConnyE
退出时，不应再包含“ C1-Code”。 如果是这样，则必须发出错误通知。 
 
5.3.7 信号 
所有行在 iSPM 中都收到一个信号名称。这些将在序列图中继续使用。 
 
6 数据提供 
完成电路图后，图例和数据状态必须自动生成并保存。文件名是根据第 7.2 章命名规
范命名的。 
 
6.1 向 MBC 提供数据 
所用原理图编辑器的原始数据与 E3.Cable 一起使用，完成后即可获得整个项目数据，
图纸数据和电路图的数字产品描述的电缆图纸级别和每条线更改后与 MBC 有关。 
 
每个接线图相关的电路图更改后，将启动已更改电路图的 ConnyE（第 0 章）通用数据
输出。车辆阶段完成后，MBC 原理图项目的整个数据库可用。 
 


### 第 32 页
接线套件中引用的所有电路图或状态和图纸都必须传送到 DAG，并且必须归档在 DAG
服务器上的多用户项目中。 同时，这些机架必须通过通用导出传输到 Connect。因
此，供应商必须确保 E3.Cable Multi-User Project 中的参考电路图以及所有线束版本的
Connect 中的参考电路图，具有相应的单个和全部原理图 PDF 以及可用于所有接线图
的 XML 文件。 
 
                       图 26：数据传递示意图 
 
图表格式： 
 
来自 E3.Cable 中的 PDF 
系统列表： 
 
从 E3.Cable 通用导出所有系列 
 
当前电路图数据的数据设置在邮箱交换目录中，并通过 ENX 传输到 MBC。 邮箱目录
必须与运营商同意。 
操作员配置 ENX 机制，以便在 RD / EKL 的计算机上提供指定项目目录中的数据。 
 
电路图，V1 
电路图 1，V1 
电路图 2，V1 
线束图纸 1，V1 


### 第 33 页
有关文件名，请参见第 7.2 章命名约定。 
 
6.2 MBC 提供的数据 
来自当前原理图库的数据，模板文件，ConnyE，导线和组件数据以及来自 Connect 的
导出文件由操作员在参与供应商的邮箱交换目录中设置，并通过 ENX 传输给供应商。
交换目录是预定义的，可以通过“ DMU 和工具”团队（机械组件和工具）进行扩展。
有关文件名，请参见第 7.2 章命名规范。 
 
对方（供应商）的邮箱目录必须与操作员达成一致，并设置访问权限。 
操作员配置 ENX 机制，以便在供应商指定的入站邮箱中提供数据。 
 
从 Connect 导出的文件提供的文件名没有导出日期，但引用了具有关键字 Full 的内容
表示完整更新，而具有 DIFF 的内容表示增量更新。由于增量更新仅识别新功能，因此
此处未列出已删除的对象，因此至少需要每周一次完整更新。 
 
在 DIFF（基础上）理解到上一次完整更新的时间间隔。 
 
由于外部安装和 MBC 方面的向后兼容性和数据交换，E3. Cable 发布的版本 Patchlevel 
和 ConnyE 必须保持一致。为确保共同的转换时间， “ DMU 和工具”团队协调更改
日期。 
 
6.3 为模块线路提供数据 
为了处理诸如座椅，门，保险杠等模块，需提供这些尺寸所需的各个电路图。 
该任务由设计线束的团队负责。 
 
7 数据格式和命名规范 
7.1 数据格式 
诸如 SVG，PDF 和 XML 的文件格式已合并到 MGD / D 内部文档系统中，并且必须在
MBC 首次使用之前进行确认。 
 
7.2 命名规范 
为了使文件名更易于阅读，文件名的结构元素由下划线分隔。 
空格和特殊字符，例如文件名中的括号和变音符号不能是一般符号。 
 
7.2.1 系列名称，设计和车辆阶段 
需提供每个电路图的用途（系列名称，设计和阶段）。电路图有多种用途，例如，如果在一
系列的电路项目中；既有大型汽车又有两用汽车。 
在以下示例中，C205 系列的电路图在 FS 和 FW 版本中有效。 
 
需在电路图图例下的变体中写清用途。  
 
系列、执行类型和车辆阶段都是在 CONNECT 中进行维护，并且只有这些可以写在电路图
上。用途、车辆阶段的表示不用语言符号。 


### 第 34 页
 
    图表 27：用途（系列名称，设计，车辆阶段） 
 
7.2.2 零件编号格式 
工程图和文档中项目编号的拼写可以以结构化或非结构化形式进行，对于更好的可读性而
言结构化形式是更可取的。在导出文件中，将输入非结构化拼写。该系列必须在产品编号
中表现出来。 
 
在零件编号中以下符号是可取的：A-Z, 0-9, „-„ 
 
图纸中的非结构化例子: 
A2045401005 
HBE2128201034 
212820X034 
16754001020301 
 
图纸中的结构化例子: 
A204-540-10-05 
HBE212-820-10-34 
212-820 X-034 
167-540-010203-01 
 
结构化因素会使用一个空格符。 
 
******* 电路图编号： 
将来，电路图项目编号应如下所示： 
 
图表 28 电路图编号 
 
  
系列 
设计图小组 
iSPM 结构 
数字 
变体 
IP443 
名称 
可加热方向盘 
 
位置 
30B，56C 
用途 
C05FW EF2.BL2 
 


### 第 35 页
写法: 
 
<系列> - <KGU> - < iSPM 结构> - <数
字>-<页数> 
 
<系列>  
MBC 指定的序列号 
<KGU>  
原理图的构造子组始终为“ 540” 
< iSPM 结构>  
反映 iSPM 指定的结构中的原理图的排列
位置 
<数字>  
连续的两位数 
 
例子： 
167-540-010203-01 
222-540-030205-02 
 
7.2.3 各个电路图的文件名  
根据以下描述的文件名结构存储从原理图得出的数据。 
基础是图纸标题中的字段。 
 
写法 : 
<物料号>_ <页数>_< 数据
状态>.<文件扩展名> 
< 物料号>  
图纸字段中的零件编号（项目编号） 
< 页数>  
图纸字段（图纸）中的图纸编号 
<数据状态>  
来自绘图字体字段的数据状态，格式为 年-月-日 
 
例子: 
单- 电路图 PDF- 文件： 212—540-010203-02_1_2010-09-22.pdf 
 
备用文件名: 
电路图 PDF- 文件:  21254001020302_1_2010-09-22.pdf 
 
备用文件名不是首选。 
 
结构形式不正确的示例作为文件名： 
212_540_010203*02.01_1_2010-09-22.pdf 
 
注意： 
在上面的错误示例中，红色显示的字符不正确。物品编号中可能没有任何特殊字符，
例如“ _”，“ *”或“。”。此外，在项目编号中包括薄片编号“ 01”。由于这些结
构错误，原理图文件无法存储在 CONNECT HARNESS 中被导入。 
 
7.2.4 系列项目的文件名 
项目名称的结构元素由下划线分隔。 
写法： 
<模型> _ <车辆阶段> _ <数据状态>-<文件扩展名> 


### 第 36 页
 
类型指定示例： 
 
C212 = 212 系列车辆的型号名称 
D166 =电机的型号 166 
 
文件名例子: 
系列项目 
C212_E1A2_2000-06-30.e3s 
变更年限 
C212_AEJ07X_2000-06-30.e3s 
C166_AEJ07X_2000-06-30.xml  
总项目 
D166_AEJ07X_2000-06-30.e3p 
 
 
 
7.2.5 总电路图的文件名 
总电路图的文件名按照以下格式命名。 
 
写法： 
<系列 1> _ <该系列 1 的所有版本具有相同的车辆阶段>-<车辆阶段> _ 
<系列 n> _ <具有相同车辆阶段的所有类型的系列 n>-<车辆阶段 n> _ <日期> .pdf 
 
例子: 
C231_FAS-FA-FFH_AJ2014_C204_FC_MOPF_C231_FFH_EF1_2011-11-10.pdf 
 
7.2.6 文件名库和软件扩展名 
符号库 
MBC-E3-<E3-Cable-版本>-DAG-数据库-<日期 日-
月-年>.mdb 
配置文件 
MBC-E3-< E3-Cable-版本>-项目设置.e3t 
来自 CONNECT 的 XML 文件 
E3 完全导出 .xml (Full- Export) 
 
E3 导出_delta_of_delta.xml (Delta- 导出) 
ELOG 配置文件 
MBC-E3-ELOG 属性映射.xml 
软件扩展 
ConnyE-<版本> 
< E3-Cable-版本 >  
zeigt die Kompatibilität zur eingesetzten Software an 
表示所用软件的兼容性 
mdb  
Access Datenbankformat 访问数据库格式 
 
 
7.3 通用导出 
电路图 XML 文件是电路图的电子图像，并描述具有所有可用属性的连接和组件。另外
还包括标题信息。 
 
通用导出被用作线束开发过程、MBC 的 Connect Harness 数据库中的文档和内部搜索
的基础。 


### 第 37 页
通过通用导出，原理图项目的所有数据均以定义的 XML 生成，包括所有单独的电路图
和完整的电路图（以 PDF 格式）。之后，可以将不同的转换应用于 XML 文件以创建不
同的格式。供应商可以独立创建自己的转换信息。因此，各方都可以灵活地适应新的
技术要求。 
 
                 图表 29：通用导出 
 
8 相关文件       
8.1 名称和规范 
文件 
内容 
MBN 31 001  
Grundlagen für Konstruktions-Zeichnungen 设计图纸的基础 
MBN 31 002  
CAD-Zeichnungen; Beschriftung, Schriftarten und 
Schriftgrößen   CAD 图纸; 标签，字体和字体大小 
IEC 60 757  
Farbkennzeichnung von Leitungen  电线的颜色编码 
DIN EN 28 601  
Datenelemente und Austauschformate Informationsaustausch, 
Darstellung von Datum und Uhrzeit 
数据元素和交换格式信息交换，日期和时间表示 
VA 059 EI08  
VZK, Bauteilkurzbezeichnungen (Verwendungszweck) 
VZK，组件缩写（预期用途） 
 
 
 
8.2 缩写和概念 
缩写 
命名 
Ausführungsart  
实施方案 
Beschreibt die Karosserieform der Fahrzeuge 描述车辆的车身
形状 
Baumuster  
样品 
Baureihen oder Aggregate- Kennzeichen der Fahrzeugstückliste 
车辆零件清单的系列或集合标识 
通用导出 
转换信息 
莱尼 
XML 
DAS 
XML 
戴姆勒 
XML 


### 第 38 页
Basisschaltplan  
基础电路图 
Ein einzelner Systemschaltplan (z.B. Außenbeleuchtung vorne 
LED / STAR2.2), bestehend aus 1-n Blättern im iSPM-
Schaltplanprojekt  
iSPM 原理图项目中由 1-n 张纸组成的单个系统图（例如，
前 LED / STAR2.2 室外照明灯） 
Baureihenbasisprojekt  
系列基础项目 
Die Baureihen-spezifische Ausleitung aus dem iSPM --
Schaltplanprojekt, welche die Grundlage für das 
Baureihenprojekt bildet (mehrere Basisschaltpläne). 
来自 iSPM 电路图项目的特定于系列的衍生产品，构成了系
列项目（几个基本电路图）的基础。 
Baureihenschaltplan  
系列电路图 
 
Ein einzelner Systemschaltplan (z.B. Außenbeleuchtung vorne 
LED), bestehend aus 1-n Blättern im Baureihenprojekt 
该系列项目中由 1-n 个刀片组成的单个系统图（例如外部
LED 前灯） 
Baureihenprojekt  
系列项目 
Die Weiterentwicklung des Baureihenbasisprojekts in der 
Serienentwicklung 系列开发中系列基础项目的进一步开发 
BMKL  
B-Muster Klausur  B 类样品测试 
CADDS  
CAD- System zur Leitungssatzentwicklung 用于线束开发的
CAD 系统 
Datenstand 数据状态 
Datum der maschinellen Zeichnungsbearbeitung im Format DIN 
EN 28 601 , Siehe auch Kapitel Prozesssicherheit 
采用 DIN EN 28 601 格式的图纸处理日期，另请参见过程安
全章节 
Connect 连接 
Bauteiledatenbank der Leitungssatzentwicklung 线束开发的组
件数据库 
ConnyE  
Container für Softwareanpassungen der MBC  
MBC 的软件修改容器 
E3.Cable  
Schaltplaneditor der Firma ZUKEN  
ZUKEN 公司的电路图编辑者 
EPDM  
Elektronik-Produktdatenmanagement 
电子产品数据管理 
Fahrzeugphase  
Entwicklungsstufe eines Fahrzeugs (z.B. E- Fzg, Serie, 
Änderungsjahr, MOPF)   
车辆的开发阶段（例如，E-Fzg，系列，更改年份，
MOPF） 
Feldkenner  
Koordinatenfelder in Konstruktions-Zeichnungen, Beispiel YX 
= A-Z1-N 
施工图中的坐标字段，例如 YX = A-Z1-N 
Freigabezeichnung  
Zeichnung die in Smaragd mit Auftrag und ZGS freigegeben 
wird  
订单和 ZGS 在 Smaragd 中发布的图纸 
GSP  
Global Service Parts , Service Organisation der MBC 
MBC 服务组织全球服务部 
IP Code  
Offizielle Verkaufscode der Daimler AG, Sparte PKW 


### 第 39 页
戴姆勒股份公司轿车部门的正式销售代码 
iSPM  
integriertes SchaltPlanManagement 
电路图纸一体化管理 
Kennbuchstabe 标识符 
Klassifiziert Sachnummern f. Teile, Code, Baumuster usw. 
分类项目编号 f， 零件，代码，模型等 
KBL  
Kabelbaumliste nach ISO 10303- AP212 Spezifikation 
线束清单符合 ISO 10303- AP212 规范 
MBC  
Mercedes Benz Car / Development 
梅赛德斯奔驰 
PDF  
Portable Document Format, plattformübergreifendes 
Dateiformat 
便携式文档格式，跨平台文件格式 
RD/EKL  
Abteilung „Leitungssätze und Digitale Entwicklung“ 
“布线与数字化发展”部门 
RSync  
Remote Synchronisation , Verfahren zum Synchronisieren von 
Bibliotheken 远程同步，用于同步库的方法 
SVG  
Scalable Vector Graphics, Skalierbare Vektorgrafiken 
可缩放矢量图形，可缩放矢量图形 
SRM  
Sachnummern Recherche Management: System in dem die 
Teilenummern vergeben werden 
物料编号搜索管理：分配零件编号的系统 
Step AP212  
Step, Standard fort the exchange of Product Data ISO 10303-212 
AP212, Application Protocol for Electro technical design and 
installation 
步骤，标准要交换产品数据 ISO 10303-212 AP212，电子技
术设计和安装应用协议 
U-Code  
Bereichsinterne Code zur Steuerung von Leitungen im 
Schaltplan 
区域内代码，用于控制电路图中的线路 
VZK  
Verwendungszweck, Katalog der Bauteilkurzbezeichnungen 
(REF) 
预期用途，组件缩写目录（REF） 
XML  
extensible mark-up language 可扩展标记语言 
ZGS  
Zeichnungs- und Geometrie- Stand 图纸和几何状态 
 
 
 
 
8.3 图表目录 
图 1：包含说明的技术图纸................................................................................................................ 11 
图 2：外部更改字段........................................................................................................................... 12 
图 3：电路图布局.................................................................................................................................... 14 
图 4：绞线和屏蔽层的图表描述...................................................................................................... 15 
图 5：实施例的图形区别................................................................................................................... 16 


### 第 40 页
图 6：连接属性中的实现................................................................................................................... 17 
图 7：连接器参考................................................................................................................................. 17 
图 8： 分离点……………………………………………………………………………………………………………………18 
图 9：电缆尾部组件................................................................................................. 18 
图 10：变体插头名称.......................................................................................................................... 19 
图 11：备用块..................................................................................................................................... 21 
图 12：行号........................................................................................................................................... 22 
图 13：扭绞符号................................................................................................................................ 24 
图 14：屏蔽层图标............................................................................................................................... 24 
图 15：信号和端子名称................................................................................................................... 25 
图 16：护套图示.................................................................................................................................. 26 
图 17：未压接的绝缘层........................................................................................................................ 27 
图 18：外壳上的屏蔽连接.................................................................................................................. 28 
图 19：回路描述.............................................................................................................................. 29 
图 20 使用 iSPM 开发线束的过程............................................................................................. 32 
图 21：iSPM 流程................................................................................................................................. 33 
图 22：基本电路图和 BR 电路图与 KZL 和第三刹车灯的比较..................................... 34 
图 23：系统分组的原则............................................................................................................ 35 
图 24：使用示例尾灯的硬件变型................................................................................................ 36 
图 25：线路设计.................................................................................................................................. 38 
图 26：数据提供原理图...................................................................................................................... 40 
图 27：用途（系列，设计，车辆阶段）....................................................................................... 42 
图 28：电路图编号................................................................................................................................. 43 
图 29：通用出口................................................................................................................................... 46 
图 30：护套组件概述........................................................................................................................ 50 
 
9 附录 
 
       图表 30：护套组件一览 

