# A 001 002 29 99_EN_2011-09_诊断要求补充DPRS（MBN 10746）.pdf

## 文档信息
- 标题：Ausführungsvorschrift Diagnose
- 作者：<PERSON>; <PERSON>
- 页数：20

## 文档内容
### 第 1 页
 
Auftr.-Nr./order no.   
 
System / system 
 
fed.Abt./resp. 
dep.  
GSP/ODE
ZGS   
004 
ED-KB   
PY 
CAD    
- 
Datum / date 
Name / name 
Bearb./auth 2011-09-30 
<PERSON><PERSON>, <PERSON><PERSON>, 
<PERSON><PERSON>/check  
Bortolus 
Norm/stand 2011-09 
 
Benennung / title  
Diagnostic Requirements 
supplement to DPRS (MBN-10746)  
 
Freig./rel. 
2011-09 
 
 
DAIMLER 
Aktiengesellschaft 
© Daimler AG 
Schutzvermerk  DIN  34  beachten!  / 
Refer  to  protection  notice   DIN 34! 
Format / size 
A4
Blatt / sh. 
1
Sach-Nr. / basic number 
A 001 002 29 99 
Keine   Aenderung   ohne   Zustimmung   der   federfuehrenden   Konstruktion.   /   Any   alterations   are   subject   to   the   approval   of   the   design  
Diagnostic Requirements 
supplement to DPRS (MBN-10746)  
 
 
Ausführungsvorschrift 
 
 
 
 
Stand: 22.09.2011 
 
 
 
 
DAIMLER AG 
Aktiengesellschaft 
 
 
DIAGNOSTIC DEVELOPMENT 
 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 2 页
CONTENTS 
1 
SCOPE 
3 
2 
DIAGNOSTIC FUNCTIONAL REQUIREMENTS 
3 
2.1 
Manipulating of the ignition switch status via diagnostics 
3 
2.2 
Exceptions from normal operation during manufacturing 
3 
2.3 
ECU requirements when diagnostic communication is active 
3 
2.4 
Diagnostic jobs 
4 
3 
DOCUMENTATION REQUIREMENTS 
4 
4 
DEVELOPMENT PROCESS REQUIREMENTS 
4 
4.1 
ECU development sample phases 
5 
4.1.1 
Function related diagnostics: 
6 
4.1.2 
Component related diagnostics 
6 
4.2 
Sample phase deliveries 
7 
5 
VEHICLE NETWORK RELATED REQUIREMENTS 
7 
5.1 
Vehicle network signals 
7 
5.2 
General Network Requirements 
7 
5.3 
CAN Failure detection requirements 
7 
5.4 
LIN slave requirements 
8 
6 
FUEL CELL SPECIFIC REQUIREMENTS 
8 
6.1 
Engine Hour indicator 
8 
6.2 
Additional Environmental Data 
8 
7 
REFERENCES 
9 
7.1 
Normative references 
9 
7.2 
Process related references 
9 
ANNEX A (INFORMATIVE) 
10 
A.1 
Diagnostic Feature Roll-out 
10 
A.1.1 
Basic onboard enhanced diagnostic features 
10 
A.1.2 
Diagnostic features for manufacturing 
12 
A.1.3 
Diagnostic features for After Sales 
13 
 
GSP / ODE 
page 1 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 3 页
A.2 
Flash Re-programming Features 
14 
A.2.1 
Basic re-programming features 
14 
A.2.2 
Re-programming features relevant to the re-programming process 
15 
A.2.3 
Re-programming features relevant to system integration 
16 
ANNEX B (INFORMATIVE) 
17 
B.1 
Vehicle network signals - relevant to Failure Management 
17 
B.1.1 
System voltage (refer to DPRS Storage Condition 4: System Voltage) 
17 
B.1.2 
Ignition states 
17 
B.1.3 
Keyless-GO Ignition states 
18 
Generel information 
18 
Keyless-GO Ignition States illustrated 
18 
B.1.4 
Sub-network power status 
18 
B.1.5 
Transportation Mode (refer to DPRS Storage Condition 5: Transportation Mode Status 
Usage) 
19 
B.1.6 
Engine Start-up (refer to DPRS Storage Condition 11: Engine Startup) 
19 
B.2 
Vehicle network signals - relevant for mandatory environmental data 
19 
B.2.1 
External Tester Present Flag 
19 
B.2.2 
Odometer 
19 
 
 
GSP / ODE 
page 2 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 4 页
1 
SCOPE 
This document provides the framework for all diagnostic requirements which apply for a model-line 
project. This document covers the following areas: 
 
Additional diagnostic functional requirements: 
 
This section covers additional requirements which derive from the component specific features 
and capabilities. 
 
Documentation requirements: 
This section specifies additional documentation requirements which are not already covered in 
the applicable diagnostic requirements specifications. 
 
Development process requirements: 
o 
Mapping of diagnostic milestones onto MDS timeline 
o 
Deliverables for the individual diagnostic milestones 
o 
Model-line specific diagnostic requirements 
 
Vehicle Network related requirements: 
This section specifies general networking requirements, relevant signals for failure 
management and LIN slave requirements. 
 
Fuel Cell specific requirements 
This section covers fuel cell specific requirements. 
 
2 
Diagnostic Functional Requirements 
This section defines diagnostic requirements to be implemented for the component specific features. 
 
2.1 
Manipulating of the ignition switch status via diagnostics 
DRSTD 2.2-1 
The ECU implementing the ignition switch functionality (e.g. EIS) shall support a 
diagnostic command (I/O control) allowing to change the ignition signal transmitted on 
the connected communication bus(ses). 
DRSTD 2.2-2 
The manipulation of the ignition switch signal shall be independent from the 
personalization status of the respective key. 
DRSTD 2.2-4 
If the ECU implementing the ignition switch functionality receives the command 
requesting the ignition switch signal to be changed to “ignition_on” this ECU shall 
transmit this value on the bus.  
DRSTD 2.2-5 
The ECU implementing the clamp 15 relay functionality shall switch this relay when 
receiving the “ignition_on” signal. 
 
2.2 
Exceptions from normal operation during manufacturing 
DRSTD 2.2-6 
If required a production mode (i.e. roller dynamometer mode) shall be implemented. 
If such a special mode is needed and how this mode shall be implemented will be 
decided during the diagnostic definition meeting. 
DRSTD 2.2-7 
removed 
DRSTD 2.2-8 
Initial communication with each ECU shall be possible without diagnostic 
preconditions initiated from an external test tool (e.g. variant coding of Gateway-
ECUs). 
2.3 
ECU requirements when diagnostic communication is active 
DRSTD 2.3-1 
Independent from the diagnostic session or communication state active in an ECU, the 
ECUs shall not stop sending and processing messages required to keep the vehicle 
network in a stable state. 
 
GSP / ODE 
page 3 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 5 页
 
GSP / ODE 
page 4 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
 
Relevant messages are:  
 
network management messages 
 
ignition states 
NOTE –  
Requirement DRST 2.3-1 holds also true when disabling normal communication via service 
“Communication Control (0x28)”. 
 
2.4 
Diagnostic jobs 
DRSTD 2.4-1 
It requires approval by the diagnostic development team if the control of a diagnostic 
function (data retrieval / test sequence) in an ECU requires the implementation of 
program code (diagnostic job) in the diagnostic data description (odx-d). 
 
3 
Documentation requirements 
This chapter defines content and storage location for diagnostic information and diagnostic 
communication data to be documented during the development of a component. 
DRSTD 3-1 
Project specific and high-level diagnostic requirements which have impact on the 
hardware design of an ECU are specified in the hardware diagnostic definition 
meeting and resulting requirements shall be documented in the component's 
requirement specification in DOORS. 
DRSTD 3-2 
Diagnostic requirements which have impact on the diagnostic software application of 
an ECU are specified in the software diagnostic definition meeting. The resulting 
requirements shall be documented via CANdela according to the model line specific 
CANdela template. 
NOTE –  
This approach implies that the base variant (common diagnostic) of an ECU specific diagnostic 
data description (CANdela file) represents the diagnostic requirement specification for all 
diagnostic services and the related data content an ECU is required to support. 
DRSTD 3-3 
The documentation in CANdela shall be performed according to CANdela Authoring 
Guidelines (pls. refer to 7.2) to allow for a correct diagnostic data description. 
DRSTD 3-4 
The active diagnostic variant information indicates whether the ECU's software is still 
under development or in series-production (most significant bit of high byte: 1 = 
development, 0 = series production) 
DRSTD 3-5 
The active diagnostic version shall be increased with each diagnostic relevant 
software revision. This holds true for both application and bootloader software. 
Diagnostic relevant software revisions are: 
 
changes in diagnostic functionality, e.g. extension/ correction/ modification of 
diagnostic services 
 
changes in fault management, e.g. correction mature criteria (phantom failure)  
DRSTD 3-6 
The diagnostic variant and version information assigned to a particular ECU shall not 
be modified or changed by the ECU itself. 
4 
Development process requirements 
DRSTD 4-1 
In the case of subsequent modifications to software or hardware, the diagnostic 
functionality shall be implemented at the same time as the respective functionality of 
the electronic control unit. 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 6 页
4.1 
ECU development sample phases 
This chapter provides information about the diagnostic development phases, how these are mapped onto the MDS timeline and resulting deliveries for each 
phase. See Figure 1 for an overview of the diagnostic milestones and associated MDS quality gates. 
 
Figure 1: Mapping of diagnostic milestones to MDS -milestones 
 
GSP / ODE 
page 5 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 7 页
 
4.1.1 
Function related diagnostics: 
DRSTD 4.1-7 
For complex or innovative systems with distributed functions the diagnostic 
requirements shall be defined on a system or functional level within the system related 
diagnostic definition meeting. 
NOTE –  
For explicit and robust troubleshooting the D2C method shall be used in an iterative 
optimization process (pls.refer to DDC 090401 en). 
DRSTD 4.1-8 
The definition of hardware related diagnostic requirements for systems resp. functions 
shall be completed with diagnostic milestone D7. 
DRSTD 4.1-9 
The definition of software related diagnostic requirements for systems resp. functions 
shall be completed with diagnostic milestone D6. 
DRSTD 4.1-10 The function related diagnostic requirements shall be documented in DOORS. The 
documentation shall be completed by milestone D6. 
a.) For complex systems it is recommended to document the diagnostic requirements 
in the related diagnostic module of the system requirement specification. These 
system specific requirements shall be linked into the diagnostic module of the involved 
components as depicted in Figure 2. 
b.) Alternatively, for less complex systems documenting the diagnostic requirements 
directly in the diagnostic module of the involved components is allowed. 
DOORS component folder
DOORS system folder
Comp.-spec.
System-spec.
Diag.-module
Diag.-module
Komp.-LH
Diag.-Modul
Comp.-spec.
Diag.-module
Komp.-LH
Diag.-Modul
Comp.-spec.
Diag.-module
Comp.-spec.
Diag.-module
Doors-link
DOORS component folder
DOORS system folder
Comp.-spec.
Comp.-spec.
System-spec.
System-spec.
Diag.-module
Diag.-module
Diag.-module
Diag.-module
Komp.-LH
Komp.-LH
Diag.-Modul
Diag.-Modul
Comp.-spec.
Comp.-spec.
Diag.-module
Diag.-module
Komp.-LH
Komp.-LH
Diag.-Modul
Diag.-Modul
Comp.-spec.
Comp.-spec.
Diag.-module
Diag.-module
Comp.-spec.
Comp.-spec.
Diag.-module
Diag.-module
Doors-link
 
Figure 2: Documentation methode for function related diagnostic requirements 
 
4.1.2 
Component related diagnostics 
DRSTD 4.1-11 The definition of hardware related diagnostic requirements for components shall be 
completed with diagnostic milestone D7.. 
DRSTD 4.1-12 The definition of software related diagnostic requirements for components shall be 
completed with diagnostic milestone D6. 
DRSTD 4.1-13 The component related diagnostic requirements shall be documented in DOORS in 
the diagnostic module of the component and shall be finished with diagnostic 
milestone D6. 
DRSTD 4.1-14 The fault documentation shall be completed within milestone D5.  
 
NOTE –  
The approach and the content of diagnostic definition meetings are described in a standard 
agenda available in the document “Diagnostic Milestones” on the diagnostic portal (pls. refer to 
7.2). 
 
 
GSP / ODE 
page 6 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 8 页
4.2 
Sample phase deliveries 
This section defines the point in time when exactly the implementation of specific diagnostic and re-
programming features shall be completed. (based on MDS Quality Gates). 
DRSTD 4.2-2 
Basic onboard enhanced diagnostic features shall be completed by Qualtiy Gate E / 
Diagnostic Milestone D5 (please refer to A.1.1). 
DRSTD 4.2-3 
Basic reprogramming feature shall be completed by Qualtiy Gate E / Diagnostic 
Milestone D5 (please refer to A.2.1). 
DRSTD 4.2-4 
Diagnostic features for manufacturing shall be completed by Qualtiy Gate D / 
Diagnostic Milestone D4 (please refer to A.1.2) 
DRSTD 4.2-5 
Reprogramming process relevant features shall be completed by Qualtiy Gate D / 
Diagnostic Milestone D4 (please refer to A.2.2) 
DRSTD 4.2-6 
Diagnostic features for After Sales shall be completed by Qualtiy Gate C / Diagnostic 
Milestone D3 (please refer to A.1.3) 
DRSTD 4.2-7 
Re-programming features relevant for system integration shall be completed by 
Qualtiy Gate D / Diagnostic Milestone D4 (please refer to A.2.3) 
DRSTD 4.1-6 
The fault documentation and related DTC implementation according to MBN 10746 
shall be completed by milestone D4. 
 
Refer to Annex A (informative) for an overview of diagnostic and re-programming features to be 
completed by a certain MDS Quality Gate. 
 
5 
Vehicle Network related requirements  
5.1 
Vehicle network signals 
DRSTD 5-1 
The implementation of storage conditions and standard environmental data according 
to DPRS requires reception and processing of certain network signals. Pls. refer to 
Annex B (informative) for a detailed description of relevant signals. 
 
5.2 
General Network Requirements 
DRSTD 6-3 
The diagnostic gateway is defined as the ECU which is directly connected to the 
diagnostic link (connected to the diagnostic connector) of the vehicle. The diagnostic 
gateway shall use the same data link layer for diagnostics and flash re-programming 
as the external diagnostic test tool. 
DRSTD 6-4 
The same data link layer used for normal, non-diagnostic, inter-module data 
communication exchange between on-board control units shall be used for diagnostics 
and flash reprogramming. 
 
5.3 
CAN Failure detection requirements 
DRSTD 6-5 
Each CAN node shall support the electrical failure modes defined in Figure 3 (marked 
with an X in Figure 2), dependent on the used CAN transceiver type. 
 
 
GSP / ODE 
page 7 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 9 页
Transceiver Type
NXP TJA1040
NXP TJA1041A
wakeup via CAN
NXP UJA1065
wakeup via CAN
Freescale MC33742
wakeup via CAN
Infineon TLE6251G
wakeup via CAN
Failure Group
Failure Mode
Network Electrical
General Bus Failure
-
X
X
X
X
CAN High Circuit Low
-
R
X
X
R
CAN Low Circuit Low
-
R
X
X
R
CAN High Circuit High
-
R
X
X
R
CAN Low Circuit High
-
R
X
X
R
CAN High Circuit Open
-
-
-
-
-
CAN Low Circuit Open
-
-
-
-
-
CAN High and CAN Low Shorted
-
-
X
-
-
Single-Wire Mode
-
-
-
-
-
Transceiver Type
Infineon 
TLE6251DS
Infineon TLE7263E
wakeup via CAN
Bosch CY320
wakeup via CAN
AMIS 4266
TI SN65HVD1040
Failure Group
Failure Mode
Network Electrical
General Bus Failure
-
X
-
-
-
CAN High Circuit Low
-
R
-
-
-
CAN Low Circuit Low
-
R
-
-
-
CAN High Circuit High
-
R
-
-
-
CAN Low Circuit High
-
R
-
-
-
CAN High Circuit Open
-
-
-
-
-
CAN Low Circuit Open
-
-
-
-
-
CAN High and CAN Low Shorted
-
-
-
-
-
Single-Wire Mode
-
-
-
-
-
R = Recognition
X = supported failure mode
 
Figure 3: Supported Failure Modes of different CAN Transceivers 
 
5.4 
LIN slave requirements 
DRSTD 5.3-1:  The diagnostic functionality including the diagnostic communication method to be 
supported by a LIN slave component shall be defined on a case by case basis in the 
diagnostic definition meeting. 
 
Note:  Following items need to be considered when defining diagnostic LIN slave requirements: 
 
a) presuming there exist several HW variants of a specific LIN slave component, a method 
shall be supported allowing the external test tool to retrieve the HW part number  
b) implementation of the continuity check: 
b1) if the external test tool communicates directly with the slave the continuity of the LIN 
slave connector is checked implicitly (presuming the slaves responds). 
b2) if there is no direct communication between test tool and LIN slave component the 
respective LIN master components shall determine if the slave module is correctly 
connected. 
 
6 
Fuel Cell specific requirements 
6.1 
Engine Hour indicator 
An Engine Hour signal has to be sent out periodically by the Fuel Cell controller unit on the Fuel Cell 
bus. 
 
6.2 
Additional Environmental Data 
6.2.2 
In addition to the Standard Environmental Data set each ECU on the Fuel Cell bus shall store 
the current Engine Hour Indicator value as an Environmental Data when a fault occurs 
 
6.2.3 
If a valid Engine Hour Indicator value is not available the ECU shall use the default value 
[FFFF hex] indicating “data not available” 
 
 
GSP / ODE 
page 8 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 10 页
7 
References 
7.1 
Normative references 
MBN 10746 
Diagnostic Performance Requirements Standard 
A 001 002 30 99 
Standardized Diagnostic Data UDS 
 
7.2 
Process related references 
Please refer to DiagnosticPortal (http://diagnostics.e.corpintra.net/) for the following references: 
 
 
CANdela Authoring Guidelines 
 
 
Diagnostic Milestones 
 
GSP / ODE 
page 9 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 11 页
Annex A (informative) 
A.1 Diagnostic Feature Roll-out  
A.1.1 Basic onboard enhanced diagnostic features 
The software implementation of all diagnostic features listed below shall be completed by Qualtiy Gate 
E / Diagnostic Milestone D5 (pls. refer the the model line specific timeline according to MDS)  
 
 
 
Feature 
Service Identifier 
Description 
Session Handling 
 
$10 
 
Start Diagnostic Session 
 
$01 
Default Session – Pos. Resp. Required 
 
$81 
Default Session – No Pos. Resp. Required 
 
$02 
Programming Session – Pos. Resp. Required 
 
$82 
Programming Session – No Pos. Resp. Required 
 
$03 
Extended Diagnostic Session – Pos. Resp. Required 
 
$83 
Extended Diagnostic Session – No Pos. Resp. Required 
$11 
 
ECU Reset 
 
$01 
Hard Reset – Pos. Resp. Required 
 
$81 
Hard Reset – No Pos. Resp. Required 
 
$03 
Soft Reset – Pos. Resp. Required 
 
$83 
Soft Reset – No Pos. Resp. Required 
$3E 
 
Tester Present 
 
$00 
Tester Present - pos. Resp. Required 
 
$80 
Tester Present - no pos. Resp. Required 
Identification 
 
$22 
 
Read Data by Identifier 
 
 
Diagnostic Information Identification 
 
$F1 00 
Read Active Diagnostic Information 
 
 
Part Number Identification 
 
$F1 11 
Read Hardware Part Number 
 
$F1 21 
Read Software Part Number 
 
$F1 31 
Read ECU Part Number. 
 
 
Version Information 
 
$F1 50 
Read Hardware Version Information 
 
$F1 51 
Read Software Version Information 
 
$EF 00 
Read AUTOSAR SW Module Identification (if AUTOSAR BSW is 
used) 
 
$F1 53 
Read Boot Software Version Information 
 
 
Supplier Identification 
 
$F1 54 
Read Hardware Supplier Identification  
 
$F1 55 
Read Software Supplier Identification(s)  
 
GSP / ODE 
page 10 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 12 页
 
GSP / ODE 
page 11 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Feature 
Service Identifier 
Description 
Read/Clear DTC Information(Basic features) 
$19 
 
Read DTC Information 
 
$19,02,XX 
Report DTC by Status Mask  
$14 
 
Clear DTC Information 
 
$14,FF,FF,FF 
Clear DTC Information, All Groups 
Vehicle Coding 
 
$22 
 
Read Data by Identifier 
 
$xx,yy 
Read Vehicle Coding 
$2E 
 
Write Data by Identifier 
 
$xx,yy 
Write Vehicle Coding 
ECU Passiv Mode 
 
$A0 
 
Read Data By Identifier 
 
$01 
Enable ECU Passive Mode 
 
$02 
Disable ECU Passive Mode 
Stored Data (for initial operation) 
$22 
 
Read Data By Identifier 
 
$xx,yy 
 
$2E 
 
Write Data By Identifier 
 
$xx,yy 
 
Present Values (for initial operation) 
$22 
 
Read Data By Identifier 
 
$xx,yy 
 
Input Output Control By Identifier (for initial operation) 
$2F 
 
Routine Control 
 
$xx,yy,00 
Return Control to ECU 
 
$xx,yy,01 
Reset to Default 
 
$xx,yy,02 
Freez Current State 
 
$xx,yy,03 
Short Term Adjustment 
Routine Control (for initial operation) 
$31 
 
Routine Control 
 
$01,xx,yy 
Start Routine 
 
$02,xx,yy 
Stop Routine 
 
$03,xx,yy 
Request Routine Results 
Read additional 
Informations 
 
$22 
 
Read Data by Identifier 
 
$F1, 0F 
OSEK Module Information 
 
$F1, 60-6F 
SW Module Information 
 
$F1, 70-7F 
Physical Layer Channel Configuration 
 
$F1, 8C 
ECU Serial Number 
 
$F1, 90,A0 
VIN (Original/Current) 
 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 13 页
A.1.2 Diagnostic features for manufacturing 
The software implementation of all diagnostic features listed below shall be completed by Qualtiy Gate 
D / Diagnostic Milestone D4 (pls. refer the the model line specific timeline according to MDS)  
 
Feature 
Service Identifier 
Description 
Read/Clear DTC Information(Enhanced features inkl. ROE light) 
$19 
 
Read DTC Information 
 
$19,01 
Report Number of DTC By Status Mask 
 
$19,02,XX 
Read DTC Information by Status Mask  
 
$19,06 
Report DTC Extended Data By DTC Number  (Standard-
Umgebungsdaten) 
 
$19,0A 
Report Supported DTC 
$14 
 
Clear DTC Information 
 
$14,xx,xx,xx 
Clear DTC Information, Singel DTCs 
Security Access 
 
$27 
 
Security Access 
 
$0x 
Request Seed Level 0x 
 
$0y 
Send Key Level 0y 
Stored Data  (required for manufacturing) 
$22 
 
Read Data By Identifier 
 
$xx,yy 
 
$2E 
 
Write Data By Identifier 
 
$xx,yy 
 
Present Values (required for manufacturing) 
$22 
 
Read Data By Identifier 
 
$xx,yy 
 
Input Output Control By Identifier (required for manufacturing) 
$2F 
 
Routine Control 
 
$xx,yy,00 
Return Control to ECU 
 
$xx,yy,01 
Reset to Default 
 
$xx,yy,02 
Freez Current State 
 
$xx,yy,03 
Short Term Adjustment 
Routine Control  (required for manufacturing) 
$31 
 
Routine Control 
 
$01,xx,yy 
Start Routine 
 
$02,xx,yy 
Stop Routine 
 
$03,xx,yy 
Request Routine Results 
 
 
 
 
GSP / ODE 
page 12 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 14 页
A.1.3 Diagnostic features for After Sales 
The software implementation of all diagnostic features listed below shall be completed by Qualtiy Gate 
C / Diagnostic Milestone D3 (pls. refer the the model line specific timeline according to MDS)  
 
Feature 
Service Identifier 
Description 
Service Tracing Memory 
$22 
 
Read Data by Identifier 
 
$01 02 
Read Diagnostic Tracing Memory 
$22 
 
Write Data by Identifier 
 
$01 02 
Write Diagnostic Tracing Memory 
Stored Data  (required for after sales) 
$22 
 
Read Data By Identifier 
 
$xx,yy 
 
$2E 
 
Write Data By Identifier 
 
$xx,yy 
 
Present Values (required for after sales) 
$22 
 
Read Data By Identifier 
 
$xx,yy 
 
Input Output Control By Identifier (required for after sales) 
$2F 
 
Routine Control 
 
$xx,yy,00 
Return Control to ECU 
 
$xx,yy,01 
Reset to Default 
 
$xx,yy,02 
Freez Current State 
 
$xx,yy,03 
Short Term Adjustment 
Routine Control  (required for after sales) 
$31 
 
Routine Control 
 
$01,xx,yy 
Start Routine 
 
$02,xx,yy 
Stop Routine 
 
$03,xx,yy 
Request Routine Results 
 
 
GSP / ODE 
page 13 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 15 页
A.2 Flash Re-programming Features 
A.2.1 Basic re-programming features 
The software implementation of all reprogramming features listed below shall be completed by Quality 
Gate E / Diagnostic Milestone D5 (pls. refer the the model line specific timeline according to MDS)  
 
Feature 
 
 
Service Identifier 
 
Description 
Session Handling 
 
 
$10 
 
Start Diagnostic Session 
 
$01 
Default Session – Pos. Resp. Required 
 
$81 
Default Session – No Pos. Resp. Required 
 
$02 
Programming Session – Pos. Resp. Required 
 
$82 
Programming Session – No Pos. Resp. Required 
 
$03 
Extended Diagnostic Session – Pos. Resp. Required 
 
$83 
Extended Diagnostic Session – No Pos. Resp. Required 
$3E 
 
Tester Present 
 
$00 
Tester Present - pos. Resp. Required 
 
$80 
Tester Present - no pos. Resp. Required 
Security Access 
 
 
$27 
 
Security Access 
 
$05 
Request Seed Level 05 
 
$06 
Send Key Level 06 
Fingerprints 
 
 
$2E 
 
Write Data by Identifier 
 
$F1 5A 
Write Fingerprint 
Erase 
 
 
$31 
 
Routine Control 
 
$01 FF 00 Erase Routine – pos. Resp. Required 
Flash Download 
 
 
$34 
 
Request Download 
$36 
 
Transfer Data 
$37 
 
Request Transfer Exit 
CRC (Integrity) 
 
 
$31 
 
Routine Control 
 
$01 FF 01 Check Routine – pos. Resp. Required 
$11 
 
ECU Reset 
 
$01 
Hard Reset – Pos. Resp. Required 
 
$81 
Hard Reset – No Pos. Resp. Required 
 
 
 
GSP / ODE 
page 14 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 16 页
A.2.2 Re-programming features relevant to the re-programming process 
The software implementation of all reprogramming features listed below shall be completed by Quality 
Gate D / Diagnostic Milestone D4 (pls. refer the the model line specific timeline according to MDS)  
 
 
Feature 
 
 
Service Identifier 
 
Description 
Identification 
 
 
$22 
 
Read Data by Identifier 
 
 
Diagnostic Information Identification 
 
$F1 00 
Read Active Diagnostic Information 
 
 
Part Number Identification 
 
$F1 1x 
Read Hardware Part Number 
 
$F1 2x 
Read Software Part Number 
 
$F1 3x 
Read ECU Part Number. 
 
 
Version Information 
 
$F1 50 
Read Hardware Version Information 
 
$F1 51 
Read Software Version Information 
 
$F1 53 
Read Boot Software Version Information 
 
 
Supplier Identification 
 
$F1 54 
Read Hardware Supplier Identification  
 
$F1 55 
Read Software Supplier Identification(s)  
Fingerprints 
 
 
$22 
 
Read Data by Identifier 
 
$F1 5B 
Read Fingerprint 
Programming Countrer 
 
 
$22 
 
Read Data by Identifier 
 
$01 00 
Read Programming Attempt Counter 
Security 
(Authentification) 
 
 
$31 
 
Routine Control 
 
$01 FF 01 Check Routine – pos. Resp. Required 
 
 
 
 
GSP / ODE 
page 15 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 17 页
 
GSP / ODE 
page 16 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
A.2.3 Re-programming features relevant to system integration 
The software implementation of all reprogramming features listed below shall be completed by Quality 
Gate C / Diagnostic Milestone D3 (pls. refer the the model line specific timeline according to MDS)  
 
Feature 
 
 
Service Identifier 
 
Description 
Communication Parameter Switching 
 
$87 
 
Link Control 
 
$01 
Verify baudrate transition 
 
$02 
Verify baudrate transition 
 
$03 
Baudrate transition 
$2E 
 
Adjust ISO 15765-2 
Block Size and Stmin 
 
$010B 
(Byte 0) BlocksizeValue 
 
$010B 
(Byte 1) Stmin Value 
Communication Control 
 
 
$28 
 
Communication Control 
 
$01 01 
Enable Rx and Disable Tx – pos. Resp. Required 
 
$81 01 
Enable Rx and Disable Tx – no pos. Resp. Required 
 
$00 01 
Enable Rx and Tx – pos. Resp. Required 
 
$80 01 
Enable Rx and Tx – no pos. Resp. Required 
Control DTC Settings 
 
 
$85 
 
Control DTC Settings 
 
$01 
DTC Settings Type = on – pos. Resp. Required 
 
$81 
DTC Settings Type = on – no pos. Resp. Req. 
 
$02 
DTC Settings Type = off – pos. Resp. Required 
 
$82 
DTC Settings Type = off – no pos. Resp. Req. 
Fail Safe Reaction 
 
 
$31 
 
Routine Control 
 
$01 FF 05 Disable Fail Safe Reaction – Pos. Resp. Required 
 
$81 FF 05 Disable Fail Safe Reaction – No Pos. Resp. Required 
 
$02 FF 05 Enable Fail Safe Reaction – Pos. Resp. Required 
 
$82 FF 05 Enable Fail Safe Reaction – No Pos. Resp. Required 
Programming 
Preconditions 
 
 
$31 
 
Routine Control 
 
$01 FF 03 Check Programming Preconditions – Pos. Resp. Required 
 
 
 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 18 页
Annex B (informative) 
B.1 Vehicle network signals - relevant to Failure Management 
NOTE –  For further details concerning signal and message description refer to the corresponding 
VMM. 
 
B.1.1 System voltage (refer to DPRS Storage Condition 4: System Voltage) 
Extract of VMM common signal pool: 
 
Signal Name for system voltage =  PN14_SupBat_Volt 
 
B.1.2 Ignition states 
Extract of VMM common signal pool: 
 
Signal name for ignition states =  ISw_Stat and Ign_On_StProc_Inact 
 
Ignition lock (0) / IGN_LOCK 
Ignition off (15c) / IGN_OFF 
Ignition accessory (15r) / 
IGN_ACC 
Ignition on (15) / IGN_ON 
Ignition start (50) / IGN_START 
Signal not available / SNA 
Table 4: ignition states 
 
GSP / ODE 
page 17 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 19 页
B.1.3 Keyless-GO Ignition states 
Generel information 
Cars with KEYLESS-GO are equipped with an integrated KEYLESS-GO Funktion and a removeable 
Start-Stopp button. The Start - Stopp button must be in the ignition lock and the key must be inside the 
car. 
 
A push severel times on the Start-Stopp button equals the different ignition states in the ignition lock. 
Precondition, without an active brake. If the brake is active and you push the Start-Stopp button, the 
engine will start immediately. 
Keyless-GO Ignition States illustrated 
State 0 (IGN_LOCK): unpressed Start-Stopp 
button means not plugged key. 
 
State 1 (IGN_ACC/15r): push once the Start-
Stopp button. 
 
State 2 (IGN_ON/15): push two times the Start-
Stopp button. 
 
Note: The voltage supply will be shut off if the 
door will  be open at state 1 and 2. 
 
Ignition Start (IGN_START/50): active brake and 
push the Start-Stopp button. 
 
 
 
 
To plug:  
The system needs 2 seconds to detect the Start-
Stopp button. 
 
 
 
 
 
 
 
 
B.1.4 Sub-network power status 
The following two signals are necessary to determine whether the sub-networks are available and 
powered up:  
 
1.) Signal to avoid timeout DTC storage due to asynchronous transition of different networks in sleep 
state (OSEK network management only) 
 
Extract of VMM common signal pool: 
 
Signal name =  CGW_Rout_Stat 
 
2.) Signal to detect, that some ECUs are going to be switched-off from power supply (PREOPN).   
 
Extract of VMM common signal pool: 
 
Signal name for Cut-Off Switch state =  PN14_SupBatCutSw_Stat 
 
GSP / ODE 
page 18 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 20 页
 
GSP / ODE 
page 19 
Part No.: A 001 002 29 99   
©2011 Daimler AG 
 
Date: 2011-09 
 
 
B.1.5 Transportation Mode (refer to DPRS Storage Condition 5: Transportation Mode 
Status Usage) 
Extract of VMM common signal pool: 
 
Signal Name for transportation mode status =  PN14_ TransMd _ Stat 
 
B.1.6 Engine Start-up (refer to DPRS Storage Condition 11: Engine Startup) 
Extract of VMM common signal pool: 
 
Signal Name for engin start-up = EngRun_Stat 
 
B.2 Vehicle network signals - relevant for mandatory environmental data 
B.2.1 External Tester Present Flag 
Extract of VMM common signal pool: 
 
Signal Name for external Tester Present flag = DidA_ExtTest_Pres 
 
B.2.2 Odometer 
Extract of VMM common signal pool: 
 
Signal Name for odometer = Odo 
 
 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)

