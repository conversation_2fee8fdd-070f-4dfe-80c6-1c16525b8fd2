# A 002 006 32 99_CH_2014-03_ZGS004_梅赛德斯奔驰汽车线束图纸要求.pdf

## 文档信息
- 标题：<4D6963726F736F667420576F7264202D2041303032303036333239395F43685F323031342D30332D323120C3B7C8FCB5C2CBB9B1BCB3DBC6FBB3B5CFDFCAF8CDBCD6BDD2AAC7F32E646F6378>
- 作者：yanyinfeng
- 页数：129

## 文档内容
### 第 1 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  1 / 129 
 
执行规范 
 
 
 
 
 
 
 
 
  
梅赛德斯奔驰汽车线束图纸要求 
 
 
 
 
 
 
 
 
 
版本 4.0 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 2 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  2 / 129 
 
变更目录/变更描述 
线束图纸履历变更文件表 
ZGS(图纸几何状态) 
补充说明 
编制人（日期） 
Daimler AG 
001 
根据两个现行执行规范制定
新的文件，两个现行执行规范
是指“执行规范/线束编制”
A0000069799 和“执行规范/
线束插图”A0000026299 
Juric    2011‐12‐20 
002 
添加车间 01/12 和 02/12 图片
目录整合变更内容，更新章节
8.2“标准与规范” 
Rath 2012‐04‐2 和 2012‐11‐07
003 
更新章节 2.3.1 图纸上的变更
索引和 4.3 章节的回线或布
线路径 
Rath 2013‐09‐25 
004 
修改章节 7；整合 KBL 和 HCV
数据格式（章节 0 和章节 7.6）
Rath 2014‐03‐21 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 3 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  3 / 129 
 
目录   
1  框架条件—执行规范对象                                                                                                        9 
1.1 目标……………………………………………………………………………………………………………………………….9 
1.2 负责内容……………………………………………………………………………………………………………………….10 
1.3 术语……………………………………………………………………………………………………………………………….10 
2  线束二维图纸文件                                                                                                                  14                       
  2.1  线束二维图纸结构………………………………………………………………………………………………………16 
  2.2  多页图纸线束表示特征………………………………………………………………………………………………17 
  2.3  二维图纸线束表示特征……………………………………………………………………………………………...18 
    2.3.1  奔驰标题栏和变更历史（点 1、2、3）……………………………………………………………….19 
    2.3.2 AV 参考标准（点 4）……………………………………………………………………………………………..22 
    2.3.3  原理图表（点 5）…………………………………………………………………………………………………23 
    2.3.4 DMU 表（点 6）…………………………………………………………………………………………………….24 
    2.3.5  项目差异（点 7）…………………………………………………………………………………………………24 
    2.3.6  特殊验证的属性标识（点 8）……………………………………………………………………………..25 
    2.3.7 Master 线束图纸的模块表（点 9）……………………………………………………………………..25 
    2.3.8  导线表（点 10）………………………………………………………………………………………………….27 
    2.3.9  零件清单（点 11）………………………………………………………………………………………………28 
3  线束图纸数据格式和单位                                                                                                    30 
  3.1  导线长度…………………………………………………………………………………………………………………….30 
  3.2  线束重量…………………………………………………………………………………………………………………….30 
  3.3  日期输入…………………………………………………………………………………………………………………….30 
  3.4  位置编号…………………………………………………………………………………………………………………….30 
    3.4.1  位置编号格式……………………………………………………………………………………………………….31 
    3.4.2  其他位置编号……………………………………………………………………………………………………….31 
  3.5  零件标识……………………………………………………………………………………………………………………31 
  3.6 环境保护基本要求…………………………………………………………………………………………………….32 
4  二维图纸上线束零件表示法                                                                                                32 
  4.1  接插件表示法……………………………………………………………………………………………………………32 
    4.1.1  接插件—标记……………………………………………………………………………………………………….32 
 
 
 
 
 
 
 
 
 


### 第 4 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  4 / 129 
 
    4.1.2  接插件—表格……………………………………………………………………………………………………….33 
    4.1.3  标记提供……………………………………………………………………………………………………………….33 
    4.1.4  功能标识……………………………………………………………………………………………………………….34 
    4.1.5  多选用途……………………………………………………………………………………………………………….34 
    4.1.6  功能结构……………………………………………………………………………………………………………….34 
    4.1.7  塑壳命名……………………………………………………………………………………………………………...34 
    4.1.8  端子形状……………………………………………………………………………………………………………….34 
    4.1.9  搭铁端子……………………………………………………………………………………………………………….34 
    4.1.10  保险丝，继电器………………………………………………………………………………………………….37 
4.2  线束挂点连接                                                                                                                  38 
    4.2.1  焊接挂点………………………………………………………………………………………………………………38 
    4.2.2  压接………………………………………………………………..……………………………………………………..38 
    4.2.3  超声波焊接…………………………………………………………………………………………………………….38 
    4.2.4  其他关键词…………………………………………………………………………………………………………….39 
  4.3  回线或更改布线路径…………………………………………….…………………………………………………..41 
  4.4  位置表示法…………………………………………………………………………………………………………………41 
  4.5  分支方向…………………………………………………………………………………………………………………….42 
  4.6  固定件………………………………………………………………………………………………………………………..42 
  4.7  线束上固定件位置和插入方向…………………………………………………………………………….…..43 
    4.7.1  扎带位置………………………………………………………………………………………………………………..43 
    4.7.2  塑壳距离尺寸标注…………………………………………………………………………………………………43 
    4.7.3  图纸上固定点命名…………………………………………………………………………………………………44 
    4.7.4  固定件尺寸标注…………………………………………………………………………………………………….44 
  4.8  分支点表示法…………………………………………………………………………………………………………….45 
    4.8.1  非压接屏蔽层表示法……………………………………………………………………………………………..45 
  4.9  标签和标记………………………………………………………………………………………………………………….46 
  4.10  公差……………………………………………………………………………………………………………………………47 
    4.10.1  点到点公差……………………………………………………………………………………………………………47 
    4.10.2  固定件公差…………………………………………………………………………………………………………..48 
    4.10.3  橡胶件公差…………………………………………………………………………………………………………..49 
 
 
 
 
 
 
 
 
 


### 第 5 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  5 / 129 
 
5  线束尺寸检查                                                                                                                          49 
6  数据传输流程                                                                                                                          50 
  6.1  线束数据……………………………………………………………………………………………………………………..50 
  6.2  原理图数据…………………………………………………………………………………………………………………54 
7  线束开发中的数据格式                                                                                                          55 
  7.1  单线束、模块线束和 Master 线束………………………………………………………………..……………55 
    7.1.1  单线束…………………………………………………………………………………………………………………….55 
    7.1.2  模块线束…………………………………………………………………………………………………………………55 
    7.1.3 Master 线束……………………………………………………………………………………………………………..55 
  7.2 KBL 和 HCV 数据命名规范……………………………………………………………………………………………57 
  7.3 KBL………………………………………………………………………………………………………………………………..58 
    7.3.1  基本要求…………………………………………………………………………………………………………………58 
    7.3.2  内容和结构…………………………………………………………………………..………………………………..58 
    7.3.3  规定…………………………………………………………………………………………………………………..……59 
      *******  尺寸及单位……………………………………………………………………………………………………….59 
      7.3.3.2 Connect 数据库零件等级分类…………………………………………………………………………..60 
      7.3.3.3 Occurrence 分类 Id 命名…………………………………………………………………………………….60 
      7.3.3.4  外部参考资料……………………………………………………………………………………………………61 
      7.3.3.5  数据状态…………………………………………………………………………………………………………..62 
      ******* MBC 编号…………………………………………………………………………………………………………..62 
      *******  编号命名…………………………………………………………………………………………………………..63 
      ******* MBC 位置编号…………………………………………………………………………………………………...64 
      7.3.3.9  导线长度…………………………………………………………………………………………………………..64 
7.3.4 KBL Container……………………………………………………………………………………………………………65 
      7.3.4.1 KBL 版本……………………………………………………………………………………………………………..66 
      7.3.4.2  附件……………………………………………………………………………………………………….………….66 
      *******  坐标/Cartesian_point………………………………………………………………………………………..67 
      *******  盲堵….....................................................................................................................68 
      7.3.4.5  防水栓....................................................................................................................68 
 
 
 
 
 
 
 
 
 
 


### 第 6 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  6 / 129 
 
      *******  零部件...................................................................................................................69 
  7.3.4.7  塑壳.......................................................................................................................71 
      7.3.4.8  外部参考资料.......................................................................................................72 
      *******  固定件...................................................................................................................73 
      ********  端子.....................................................................................................................74 
  7.3.4.11  导线.....................................................................................................................75 
  ********  挂点.....................................................................................................................78 
  7.3.4.13  连接.....................................................................................................................78 
      7.3.4.14  分支.....................................................................................................................79 
  ********  尺寸单位.............................................................................................................80 
  ********  包扎保护.............................................................................................................80 
7.3.5 Harness Container..........................................................................................................81` 
    ******* Harness Container 属性..........................................................................................81 
    7.3.5.2  变更.......................................................................................................................83 
    7.3.5.3  附件设置...............................................................................................................84 
    *******  零部件设置...........................................................................................................84 
    *******  塑壳设置..............................................................................................................84 
    *******  固定件设置...........................................................................................................85 
    *******  导线设置...............................................................................................................85 
    7.3.5.8  端子设置...............................................................................................................85 
    7.3.5.9  包扎设置...............................................................................................................85 
    *******0  线束配置.............................................................................................................86 
    ********  连接.....................................................................................................................86 
7.3.6  模块..............................................................................................................................86 
    *******  模块属性...............................................................................................................86 
    7.3.6.2  模块变更...............................................................................................................87 
7.4  数字化接线表...................................................................................................................88 
7.5 TIFF………………………………………………………………………………………………………………………………..88 
 
 
 
 
 
 
 
 
 
 
 


### 第 7 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  7 / 129 
 
7.6 HCV………………………………………………………………………………………………………………………………..89 
  7.6.1 HCV 数据库结构………………………………… …………………………………………………………………..89 
  7.6.2 KBL……………………………………………………………………………………………………………………………90 
  7.6.3 Index.xml………………………………………………………………………………………………………………….90 
  7.6.4 SVG…………………………………………………………………………………………………………………………..95 
    *******  支持的 SVG 要素……………………………………………………………………………………………….96 
    7.6.4.2  转换…………………………………………………………………………………………………………………..101 
    7.6.4.3  注意事项…………………………………………………………………………………………………………..102 
    7.6.4.4 SVG 与 KBL 相互链接…………………………………………………………………………………………103 
    7.6.4.5  类型分类…………………………………………………………………………………………………………..104 
    7.6.4.6  对象及表示法……………………………………………………………………………………………………107 
    7.6.4.7  特征及内在逻辑……………………………………………………………………………………………….126 
8  参考资料                                                                                                                                127 
  8.1  标准提示……………………………………………………………………………………………………………………127 
  8.2  标准和规范………………………………………………………………………………………………………….……127 
  8.3  缩写和术语………………………………………………………………………………………………………….……129 
  8.4  附录……………………………………………………………………………………………………………………………131 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 8 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  8 / 129 
 
图片目录： 
图 1: Master 结构空间……………………………………………………………………………………………………………11 
图 2:  线束模块……………………………………………………………………………………………………………………….12 
图 3:  分支结构示例……………………………………………………………………………………………………………….13 
图 4:  多页图纸封面结构………………………………………………………………………………………………………..16 
图 5:  单页图纸结构………………………………………………………………………………………………………………..16 
图 6:  多页图纸标题栏特征…………………………………………………………………………………………………….17 
图 7:  线束图纸结构（多页图纸第 1 页）……………………………………………………………………………..18 
图 8:  第 2 页（多页图纸）…………………………………………………………………………………………………….19 
图 9:  标题栏必填项…………………………………………………………………………………………………………………20 
图 10:  图纸上接插件表示法—示例………………………………………………………………………………………..32 
图 11:  示例 1 采用余量的端子尺寸标注………………………………………………………………………….…….35 
图 12:  示例 2 采用参考尺寸的端子尺寸标注…………………………………………………………………..……35 
图 13:  示例 3 采用余量的折弯端子尺寸标注………………………………………………………….…………….35 
图 14:  配件目录—例：保险丝……………………………………………………………………………………………….37 
图 15:  挂点标记………………………………………………………………………………………………………………..……38 
图 16:  组装表盘式表示法（必须在图纸上注明）………………………………………………………………..41 
图 17:  图纸上视线表示法（至少在图纸上注明一次）…………………………………………………………42 
图 18:  固定件指向安装方向 9 点钟位置，尽管与图形表述不一致……………………………………...42 
图 19:  固定件位于安装方向 9 点钟位置，插入方向位于 6 点钟方向…………………………………..43 
图 20:  固定件尺寸标注（图纸摘录）…………………………………………………………………………………….44 
图 21:  绝缘线束包扎表示法…………………………………………………………………………………………………….45 
图 22:  固定件基准点尺寸标注……………………………………………………………………………………………….48 
图 23:  橡胶件贴合面………………………………………………………………………………………………………………49 
图 24:  数据/工具…………………………………………………………………………………………………………………….51 
图 25:  软件工具流程图…………………………………………………………………………………………………………..52 
图 26:  数据传输………………………………………………………………………………………………………………………53 
图 27:  单线束 KBL 结构………………………………………………………….………………………………………………55 
图 28：Master 线束 KBL 结构…………………………………………………………………………………………………56 
图 29：HCV‐Container 图表结构……………………………………………………………………………………………90 
 
 
 
 
 
 
 
 
 


### 第 9 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  9 / 129 
 
1  框架条件—执行规范对象 
 
该执行规范包含了戴姆勒集团对线束图纸的要求，它规定了奔驰汽车系列线束图纸编制的具
有约束力的准则。线束应与其他相应开发系统中的车载工具一起编制存档。图纸采用戴姆勒
集团的图纸框架和图纸标题栏制作完成，和图纸标题栏类似，KBL/HCV 数据需注明戴姆勒集
团版权所有。 
示例： 
戴姆勒集团版权所有，请遵守 DIN ISO 16016 中的保护权。 
 
规范中所述内容阐述了奔驰汽车线束图纸要求。 
 
该执行规范不涉及技术问题、使用材料以及戴姆勒集团设计任务书/标准/工艺指导文件中规
定的内容。供应商必须遵守所有执行规范和标准，并及时更新文件以符合最新状态版本。如
果该规范中必要的边界条件没有，或者出现偏差，供应商必须向戴姆勒集团指出。 
 
数据格式为.kbl/.hcv 的，其规范准则与系统软件 CADDS 无关。 
 
所有与要求不同的偏差需要获得戴姆勒集团的书面同意。 
 
1.1 目标 
重要目标如下： 
� 
确定产品图纸内容，保证线束图纸表现形状得到统一 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 10 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  10 / 129 
 
� 
KBL‐/HCV 数据中的线束内容图解用于支持奔驰车内部流程以及存档图纸文件提供 
� 
将线束图纸中的图表作为事实表格（Master 线束图纸） 
� 
保障线束产品文件的诞生过程 
� 
通过电子线束数据管理内部开发流程 
 
1.2 负责内容 
线束开发部门负责奔驰车车架及结构范围内线束文件、技术内容以及安装零部件如接插件、
导线、特殊导线和线束保护产品的执行与制定。 
 
接插件、线束开发、系统开发部门和线束开发中相关产品工程组共同协商确定接插件使用范
围。没有获得认可的材料和零件需要获得线束开发中相关产品工程组相关项目的特殊许可。 
 
1.3 术语 
总体结构空间（Bauraum Master） 
整车分成多个结构空间。每个结构空间或大一点的几何结构会制成一份二维图。该总体结构
空间（Baum Master）确定了汽车结构空间的最大范围/模块。这些范围通常情况下不能订购。 
 
 
 
 
 


### 第 11 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  11 / 129 
 
 
图 1：总体结构空间（Bauraum Master） 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 12 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  12 / 129 
 
模块 
线束按照编号编入一个模块中。Master 线束由单个模块图构成。一个线束模块对应一个配
置范围。模块的相互关系可以很复杂。模块不能通过默认成为完整的汽车属性。 
 
 
图 2：线束模块 
 
最有效的线束二维图纸文件是 2D‐Master 线束图纸。一个结构空间的所有模块都会在图纸上
出现并得到维护。涉及多个模块的变更也会记录在 Master 线束图纸上。与编制负责人协商
后可将该图纸释放。每份 Master 线束都会产生 2 个文件： 
—HCV 文件（参见章节 HCV） 
—TIF  文件（参见章节 TIFF） 
 
从 Master 线束中导出线束模块，结果会生成两份数据： 
—KBL 或 HCV 文件（参见章节 KBL/章节 HCV） 
—TIF  文件（参见章节 TIFF） 
 
 
 
 
 
需要模块 2 
需要模块 1 
或者 
需要模块 1 
需要模块 4 
和 
需要模块 1 


### 第 13 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  13 / 129 
 
分支段 
分支段起始和末端可以用黑色圆点标识（例如标准尺寸标注）。分支段不是通过安装固定件
来划分。固定件必须单独标上尺寸。 
只能在有明确专门功能的地方标注节点（例如分支出线、接插件等）。从设计角度来看分成
多个分支段在二维图纸上是不明显的。 
 
 
图 3：分支段结构示例 
 
固定件 
固定件是指固定导线用的带密封唇口的支架、电缆轨以及引导和/或固定线束的卡扣和支架
等。 
 
无图纸零件（KZ‐siehe‐Teil） 
它是一种缩写，表示没有自己的图纸但在表格图纸上出现的零件。 
 
表格图纸 
表格图纸是将一个或多个特征属性的零件总结在一张表格内的技术文件。必须将表格图纸编
号和零件编号区分开来。 
 
 
 
 
 
 
 
 
 
 
 
 
 
分支段 3 
分支段 4 
分支段 1 
分支段 2 


### 第 14 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  14 / 129 
 
2 线束二维图纸文件 
 
线束图纸文件可以分为两种类型。两种文档方法都可采用。线束供应商必须和戴姆勒负责人
或者项目部确定哪种线束范围采用何种文档方式编制。两种文档方式如下： 
1. 多页图纸线束文件 
2. 单页图纸线束文件 
两种文档方式按照下列奔驰标准组织结构： 
 
� 
MBN 31 001  产品描述基础 
 
该标准作为基本信息，目的在于统一技术产品文件中 CAD 和 MS‐Office 图纸上的产品描述。 
 
� 
MBN 31 020‐1  设计图纸中的标题栏 
 
该标准对图纸标题栏作了规定，图纸标题栏是设计框架(设计‐图纸模板)的从属结构之一。 
 
� 
MBN 31 020‐2 CAD 图纸范围 
 
该标准对由 CATIA  V5/V4[1]生成的 CAD 图纸中的不同类型图纸描述和说明作了规定，CATIA 
V5/V4 用于奔驰乘用车和卡车的产品文件编制。 
 
� 
MBN 31 020‐3  设计图纸变更 
 
该标准对设计图纸中的变更文件作了规定。 
 
� 
MBN 10 317    用于特殊证明的属性标识 
 
为了更好的理解，需要实施 MBN  10317 中的标识。此外还可促进安全关联属性确认中的示
例规范（DocMaster MBN 10317—培训）。 
 
 
 
 
         
         
 
         
         
         


### 第 15 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  15 / 129 
 
� 
A 019 8002  奔驰图纸双语（德语/英语）标题栏缩写命名规范 
 
该标准规定了双语（德语/英语）图纸标题栏中术语缩写的应用，例如：ZB 表示总成。 
 
� 
V 019 8029  图纸组织变更 
 
该工艺指导书对需要变更的图纸，无效或者需要补充的图纸处理作了规定。 
 
线束图纸基本准则： 
如果关联技术信息无法显示在一份 CAD 图纸上（一张图纸上），这些信息可以在其他格式图
纸上显示。 
 
注意： 
当单页图纸可以显示所有范围时，线束 Master 图纸（表格）可以通过单页图纸来释放。反
之，如果涉及到多个模块，单页图纸上无法显示所有范围时，可以采用多页图纸。 
 
下面章节将对两种文档类型的结构及内容规范逐一介绍。在此需注意特定表格只能在线束
Master 图纸上显示。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 16 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  16 / 129 
 
2.1 线束二维图纸结构 
 
两种文档类型基本准则是根据 MBN 31020‐1 编制二维图纸结构。多页图纸结构见下图所示： 
 
 
图 4：多页图纸封面结构 
 
当作为单独零件线束图纸编制时，该图纸必须与其相关表格放在一页上。 
 
 
图 5：单页图纸结构 
 
 
根据 MBN 31202‐1 的多页图纸
封面 
第 1 页 
第 2 页 
例如  图纸 
例如  表格 
图纸 
表格 
根据 MBN  31020‐1 的
单独零件图纸 


### 第 17 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  17 / 129 
 
2.2 多页图纸线束表示特征 
 
多页图纸结构按照标准 MBN 31020‐1 执行。 
 
多页图纸特征注意如下： 
      —图页管理一致 
 
      —在图纸封面上注明含有最新变更状态的历史变更记录 
 
      —页数保持连贯一致 
 
 
图 6：多页图纸标题栏特征 
 
 
 
 


### 第 18 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  18 / 129 
 
2.3 二维图纸线束表示特征 
 
线束二维图纸基本结构参见图 1.纸张中央即是图纸，给出的信息与表格说明见 1‐9 点。 
 
 
 
图 7：线束图纸结构（多页图纸第 1 页） 
 
1‐9 点说明如下。 
1. 奔驰设计图纸中的标题栏，按照 MBN 31020‐1 要求 
2. CAD 日期（版本） 
3. 图纸历史变更 
4. 参考引用的有效的执行规范标准 
5. 原理图表格，含/不含控制代码的原理图表格（与 Master 相关 masterbezogen） 
6. DMU 表格 
7. 供应商备注/提示 
8. 用于特殊证明的属性标识 
9. Master 中的模块表格 
 
 
 
 
100%电测试
Master 中 的
模块表格
DMU  表格 
原理图表格 
参考的执行规范标准
图纸历史变更 
供应商备注/提示 
CAD 日期 
奔驰设计图纸中的标题栏，
按照 MBN 31020‐1 要求 
*仅在释放表格时才需要（Master 图纸）


### 第 19 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  19 / 129 
 
图纸“第 2 页”至“n 页”图示见图 8。1 到 4 点保留，另外新增以下几点内容： 
 
� 
每个模块的线束表格（见第 10 点） 
� 
每个模块的零件清单（见第 11 点） 
� 
每个模块的原理图及控制代码表格（见第 5 点） 
� 
（每个模块的 AEM 见第 1 点） 
 
 
图 8：第 2 页（多页图纸） 
 
2.3.1 奔驰图纸标题栏和历史变更（第 1 点、第 2 点、第 3 点） 
 
奔驰图纸标题栏和历史变更填写时需遵守下列标准要求： 
 
� 
MBN 31 001  产品描述基础 
� 
MBN 31 020‐1  设计图纸中的标题栏 
� 
MBN 31 020‐2 CAD 图纸范围 
� 
MBN 31 020‐3  设计图纸变更 
� 
MBN 10 137  用于特殊验证的属性标识 
� 
A 019 8002 奔驰图纸双语（德语/英语）标题栏缩写命名规范 
 
 
 
每个模块的导线清单 
每个模块的零件清单 
每个模块的 AEM 表 参见第 1 点 
每个模块的原理图表及控制代码表 
CAD 日期 
符合MBN 31020‐1 的奔驰
设计图纸中的标题栏 
100%电测试
*仅在释放表格时才需要（Master 图纸）


### 第 20 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  20 / 129 
 
图纸标题栏必填项（含变更历史记录）见图 9：必填项用红色标出。 
 
 
图 9：奔驰标题栏必填项 
 
提示：如果纸张规格不是 R 时，画虚线区域的比例必须改为实际比例。 
 
当图纸中有表格描述时（Master 线束），图纸标题栏和变更索引必须给出表格编号。线束模
块中认可相关数据参见模块表格（第 9 点）。线束模块变更目录以表格形式阐述。 
 
表格图纸非常大的情况下（乘客舱、驾驶舱等）由戴姆勒负责人决定这些信息是否在多页图
纸第 2 页中显示。 
 
 
 
 
 
 
图纸比例 
日 期
姓 名
编制 
检查 
标准 
释放/认可 
负责部门 
订单号 
图纸几何状态 
零件名称 
重量 
基本编号 
纸张规格 
属性个数 
类型 


### 第 21 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  21 / 129 
 
变更标题栏中需注意： 
� 
范围广的图纸（如 Master 线束图纸）：其 ConnectCHANGE 中的变更文档可以采用
索引，该索引需编号，最大为三位数。 
� 
图纸上的变更就可以通过这来标记，这样图纸上只需给出标记符号，它随 ZGS 变动
而变动。也就是之前 ZGS 版本的标记会取消。 
� 
ConnectChange 中的变更内容要简洁： 
变更标题栏可以这样编辑： 
<项目><供应商编号><变更><年>如有必要的话<补充> 
示例： 
○ConnectCHANGE 中的变更记录：AEM 222‐11‐XXXX/13 
○简写：222‐11‐ XXXX/13 
� 
变更标题栏中需注明 ConnectCHANGE 中变更记录的相应标记符号。 
� 
方格中的变更信息也可以与戴姆勒负责人协商一致，在图纸上单独列出一张表。 
示例： 
 
 
 
 
 
 
 
 
 
 
 
图纸上的标记 
变更标题栏摘录 
索引号 
ConnectCHANGE 中的变更记录状态 


### 第 22 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  22 / 129 
 
� 
如果留出的变更一栏不够，则 Master 的变更标题栏和所有涉及模块用“变更新绘图
Mit Änderung neu gezeichnet”代替，即使原本在变更模块的部分地方，变更标题栏
还没完全填满也要这要执行。 
示例： 
更改前变更标题栏摘录： 
 
“变更新绘图”后变更标题栏摘录： 
 
2.3.2 AV 参考标准（点 4） 
 
执行规范标准“执行规范/线束编制”A000 006 97 99 和“执行规范/线束插图”A000 002 62 99
已整合成一份执行规范“线束编制执行规范”新标准号为  A 002 006 32 99. 
引用的执行规范标准（A0020063299）必须在线束图纸上列出。 
其他引用的已生效的线束零件（特殊导线、软管等）执行规范标准也需列出来。 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 23 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  23 / 129 
 
2.3.3  原理图表（点 5） 
 
为确保线束文档准确，必须将回路数据以表格形式列出来。 
 
表格形式如下： 
第 1 栏：按照执行规范“原理图”‐A 000 006 98 99 要求的原理图编号 
第 2 栏：原理图页码 
第 3 栏：原理图名称 
第 4 栏：根据执行规范“原理图”‐A 000 006 98 99 要求的原理图日期 
第 5 栏：回路代码 
 
下列回路生成的线束 
编号 
页码 
名称 
日期 
代码 
222-540-00-02 
1 
诊断原理图 
2011-01-05 
 
222-540-00-01 
2 
气动弹簧原理图
2011-01-05 
 
注意：上述列举的属性都需涵盖其中，表格布局可以有偏差。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 24 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  24 / 129 
 
2.3.4 DMU 表格（点 6） 
 
 
 
 
 
 
 
DMU 表格中必须列出线束编制时所需的所有三维模型，属性如下： 
          —三维模型编号 
          —DMU 模型名称 
          —模型版本（Smaragd 版本） 
          —三维模型导出日期（KBL 生成日期） 
 
下列 DMU 模型生成的线束 
编号 
名称 
版本 
日期 
HCA222546X086 
发动机舱前舱线束结构空间  RBA
003.43 
2011-01-11 
HCA222546X087 
动力总成线束结构空间  Mild 
005.54 
2011-04-12 
注意：编号、名称和版本是必填项，日期可选填。 
 
2.3.5  项目差异（点 7） 
 
为更好地理解图纸且没在设计任务书（LH）以及执行规范（AV）规定的所有协议可以
在“项目差异”中显示。在此需注意不能违背设计任务书以及执行规范中的条款。 
 
 
 
 
 
 
 
 
 
 
 
 
 
提示： 
� 
无论图纸是否从三维模型中导出时，必须输入 DMU 表格及其编号。 
� 
CATIA V5 版本以上的软件可以从 DMU 模型中生成几何信息，可用于线束开发过
程中。 


### 第 25 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  25 / 129 
 
2.3.6 用于特殊验证的属性标识（点 8） 
 
所有线束图纸上必须注明“100%电测试”。 
属性标识细节请查阅标准 MBN 10 317“用于特殊验证的属性标识”。 
 
2.3.7 Master 线束图纸模块表格（点 9） 
 
当图纸上需要表格数据（Master 线束）时，此表格必须显示在图纸上。 
 
表格必须包含线束的基础数据，也就是模块图纸标题栏中的所有信息。 
 
表格内容包括： 
1. 变量—线束模块变量缩写 
2. DAG 编号—戴姆勒线束编号 
3. 名称—线束模块命名 
4. 图纸几何状态—输入相应的 ZGS 号 
5. 订单号—线束认可时的 KEM 号 
6. 图纸日期—图纸日期（日期按照 DIN ISO 8601 中的年月日格式） 
7. 姓名—线束负责人 
8. 线束重量—线束重量[kg] 
9. 数据版本—（日期按照 DIN ISO 8601 中的年月日格式） 
10. 前线束编号—如果有，输入以前线束的零件号 
11. 数量/属性—按照 MBN 31 020‐1 和 MBN 10 317 标准填写 
12. ESD 标识—按照 MBN 31 020‐1 和 MBN 10 317 标准填写 
13. 回路代码—各个模块用到的代码，例如：IP494—适用于美国，来源：Dialog（戴姆
勒零件清单系统）和 Connect 
14. 类型—根据模块应用规范 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 26 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  26 / 129 
 
Master 线束模块表格示例参见附录。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 27 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  27 / 129 
 
2.3.8 导线清单（点 10） 
 
导线清单列举了所有使用到的导线及其连接点，尽可能置于图纸左边部分。 
表格图纸标题需给出线束模块编号及变量（图纸/标题栏需显示 SRM 模块名称，参见标
准 MBN 31 020‐1）。 
表格图纸按模块列出。 
表格图纸非常大的情况下（乘客舱、驾驶舱等）由戴姆勒负责人决定这些信息是否在多页图
纸第 2 页中显示。 
 
每根导线对应表格内容如下： 
文本行 
内容 
第 1 栏： 
线号 
第 2 栏： 
导线类型（4 
第 3 栏： 
线径 
第 4 栏： 
颜色（1 
第 5 栏： 
功能名称 REF（2  （导线起点）、接插件名、功能块（导线起
点） 
第 6 栏： 
端子名称（Pin），功能块（导线起点） 
第 7 栏： 
符合图纸坐标系的坐标指引，功能块（导线起点） 
第 8 栏： 
功能名称 REF（2 (导线末端）、接插件名、功能块（导线末端） 
第 9 栏： 
端子名称（Pin），功能块（导线末端） 
第 10 栏： 
符合图纸坐标系的坐标指引，功能块（导线末端） 
第 11 栏： 
端子零件编号（导线起点） 
第 12 栏： 
防水栓零件编号（导线起点） 
第 13 栏： 
端子零件编号（导线末端） 
第 14 栏： 
防水栓零件编号（导线末端） 
第 15 栏： 
导线长度（无附加）（3 
第 16 栏： 
导线长度（有附加，可选栏）（3 
 
 
 
 
 
 
 
 
 
 
 


### 第 28 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  28 / 129 
 
(1  导线颜色按照标准 IEC 60 757 
(2  功能名称按照 VA 059 EI08 中的使用目的目录表 
简称—全称 
示例：A2—带 CD 播放的收音机 
(3  每栏按照变量填写 
(4  导线结构按照导线执行规范标准  A000 002 61 99 
 
2.3.9 零件清单（点 11） 
该表格列出了在线束模块中用到的零件编号，每个零件编号表格形式如下： 
 
文本行 
内容 
第 1 栏： 
位置编号，符合 ConnectPARTS 要求 
第 2 栏： 
零件编号（戴姆勒集团） 
第 3 栏： 
符合 ConnectPARTS 要求的命名（图纸/标题栏需显示 SRM 模
块名称，参见标准 MBN 31 020‐1） 
第 4 栏： 
无图纸零件情况下的图纸编号 
第 5 栏： 
数量（每一栏按照表格图纸中的变量） 
 
按照第 2 栏中的零件编号依次输入整理。 
 
如果零件编号没有单个零件图纸，则在零件清单中的“零件图纸”将表格图纸编号填入
需输入零件编号的位置上。 
 
无图纸的编号扩大属性（kZ）与技术专业不相关，因此从图纸上去掉。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 29 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  29 / 129 
 
如果既没有单个零件图纸也没用表格图纸，则将该零件编号的现行执行规范输入表格图
纸中。 
 
表格图纸（Master 线束图纸）标题需给出线束模块编号及变量,这些表格按照模块列出。 
 
表格图纸非常大的情况下（乘客舱、驾驶舱等）由戴姆勒负责人决定这些信息是否在多页图
纸第 2 页中显示。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 30 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  30 / 129 
 
3 线束图纸数据格式和单位 
 
3.1  导线长度 
导线长度单位为毫米，且没有小数点。 
 
3.2  线束重量 
重量单位为千克，且保留三位小数。 
 
将称得的重量输进 PDM 系统 Smaragd/Sm@web 线束数据库中存档。如果没有重量给出，
则必须给出一个估算的重量。Master 线束没有重量说明。 
 
Master 线束图纸上包含线束模块估算的重量，并输进导出的 KBL 中。 
 
没有通过 Master 线束综合的线束模块，将重量数据输进标题栏和导出的 KBL 中（见图
9） 
 
3.3  日期输入 
图纸和 KBL 日期格式按照 DIN ISO 8601”数据存储和交换格式”完整表示方法如下： 
年月日 
例如：2009‐11‐23 
 
3.4  位置编号（POS‐Nr.） 
位置编号由三到四位数和产品系列的字母标记组成。ConnectPARTS 中受限制或未受限
制的认可编号零件都有相应的位置编号。 
 
缺失的位置编号需从零件负责人那获得。 
 
只有在 ConnectPARTS 中有记录的零件才有位置编号。 
A 和 N 开头的零件编号必须有位置编号。 
 
 
 
 
 
 
 
 
 
 


### 第 31 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  31 / 129 
 
3.4.1  位置编号格式 
位置编号符号由一个三到四位数和产品系列分类字母组成（P‐乘用车，G‐G  Klasse,Z‐配
件等） 
 
示例：P698 
 
产品系列字母符号在 ConnectPARTS 中按照 VA 0198027 规定。 
 
3.4.2  其他位置编号 
新零件还没有位置编号的且需要给位置编号时，可暂时使用位置编号 Z000，稍后可换
成 ConnectPARTS 中的位置编号。 
 
在 ConnectPARTS 中没有位置编号的 B8 零件可用位置编号 Z005 标识。 
 
Dummy 位置编号解释： 
      Z000=还未确定的功能块 
      Z001=还未确定的功能块，需要协商 
      Z002=试验零件（原型样件），参见图纸 
      Z003=最后装车时插入导线 
      Z004=非单个零件/包含在总成内 
      Z005=无位置编号的功能块，见图纸 
      Z006=可变功能块（KSL），见图纸 
 
3.5  零件标识 
每根线束在总装好的状态下应在看的见的位置按照标准 MBN  10435 进行标识。一些客
户定制线束(KSK)则会有条件限制，在此针对变量会使用”集体标签 Sammeletikett”。 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 32 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  32 / 129 
 
3.6  基本环境保护要求 
 
所有线束上需将标准 DBL 8585 以插图的形式列出。 
 
供应商必须遵守所有与其产品及服务有关的环境保护、危险品、劳动保护和运输方面的
现行法律法规。供应商需了解并遵守戴姆勒集团的环境保护要求。 
 
4 二维图纸上线束组件表示法 
4.1  接插件表示法 
举例，下面章节会对其详细解释： 
 
图 10：图纸上端子表示法示例 
 
4.1.1  接插件—标记 
根据 CAD 手册 CS040 中的图纸表示法接插件图示由侧视图和孔位剖面图组成。 
 
接插件标记必须包含以下内容： 
� 
接插件零件编号和位置编号（如果有） 
 
 
 


### 第 33 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  33 / 129 
 
� 
接插件颜色和代码 
� 
接插件功能模块 
� 
功能名称（REF）和简称（大写） 
 
4.1.2  接插件—表格 
接插件图表位于端子清单旁边，从端子清单上可以看到孔位配置明细。接插件标记可以
不用按照原尺寸标识，但必须与端子编号一一对应。 
 
端子清单内容： 
文本栏              内容                          说明 
1                        孔位                          插件插位编号 
2                        线号                          线号 
3                        类型                          导线结构类型 
4                        线径                          导线截面积 
5                        颜色                          导线颜色代码 
6                        模块                          可选模块 
7                        端子                          端子零件编号 
8                        端子材料                  端子材质（可选） 
9                        防水栓                      防水栓零件编号（可选） 
 
当相同接插件（如阀门）标记排在一起时，需要另外给出护套颜色或者代码。护套颜色
以德语及小写方式给出。 
 
未使用的插孔必须在表格中显示出来（见图 10）。 
 
4.1.3  标记提供 
CADDS 数据库中的符号标记可以在需要的时候以 SVG 格式提供。 
新符号标记由插件供应商或在组件管理框架下由 CAD 模型生成，并在 Smaragd 中以 SVG
格式发出（参见 CAD 手册 CS040 或 CS080）。 
线束供应商可以通过数据传输获得这些符号标记。线束供应商将新编制的符号标记通过
数据传输提交给戴姆勒集团使用，由戴姆勒集中分配。 
 
 
 
 
 
 
 
 


### 第 34 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  34 / 129 
 
4.1.4  功能标识（REF） 
每个接插件都有对应的功能标识（REF），功能标识来自于组件数据库。缺失的标识可以
向负责人索取或者激活释放。此要求通过 RD/EKL 部门按照工艺指导书 VA 059 EI08 进行
处理。 
 
4.1.5  多选用途 
每个零件用途对应相应的功能块，要求输入相应的功能标识。多种用途情况下可以选择
相应的功能标识。 
 
举例      左前上车灯    = E17/3 
              右前上车灯    = E17/4 
 
4.1.6  功能结构 
 
从 ConnectPARTS 中的 ConnyE 工具可以获取功能块及其结构，并通过原理图将功能结构
导入线束中。线束图纸上功能块采用简写方式。 
 
4.1.7  塑壳命名 
 
塑壳命名方式：最多六个字符组成。 
注意：EPDM 中现在只支持三个字符。 
 
4.1.8  端子形状 
 
通过 EPDM 系统或者 E3‐Cable 确定端子形状。 
 
4.1.9 搭铁端子 
 
如果没有其他规定，则采用符合标准 MBN DIN 46225 和 MBN DIN 46234 的德标搭铁端
子，并在图纸上给出相应说明。 
 
尺寸标注 
 
端子尺寸标注采用参考或余量方法。此方法适用于直的、折弯的以及特殊样式的端子。
搭铁端子尺寸标注是从参考点/节点到端子孔中心处。章节 4.10.1 所述公差适用于该尺
寸标注。其他特征如操作说明请参见图纸，在测量时需考虑这些方面。 
 
 
 


### 第 35 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  35 / 129 
 
 
图 11：示例 1  采用余量的端子尺寸标注   
 
 
图 12：示例 2  采用参考尺寸的端子尺寸标注 
 
 
图 13：示例 3  采用余量的折弯端子尺寸标注 
 
由于长度公差补偿原因，必须将端子前的最后一段尺寸用括号括起来/隐藏。 
 
 
 
 
 
 
 
 


### 第 36 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  36 / 129 
 
卡槽式端子 
卡槽式端子也同样采用参考或余量尺寸方法进行尺寸标注。示例见图 11 和图 12.德尔福
或者斯道克公司端子规范可从德尔福和斯道克公司获取。 
M6  端子套：表格图纸  A 000 982 67 02(德尔福)或者 A 172 982 13 02(斯道克) 
M8  端子套：表格图纸  A 002 982 54 02(德尔福) 
 
德标搭铁端子（DIN‐Kabelshuh） 
 
德标搭铁端子也同样采用参考或余量尺寸方法进行尺寸标注。标准长度为 25mm，不受
压接范围的截面积限制。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 37 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  37 / 129 
 
4.1.10  保险丝、继电器 
保险丝和继电器作为配件直接在图纸上接插件表格下面列出。 
 
图 14：配件目录—例：保险丝 
 
配件表格标题由前缀“配件 Zubehörteile  zu”和功能名称（REF）以及简写（大写）组
成。 
表格格式内容如下： 
� 
名称（保险丝/继电器）(Benennung) 
� 
颜色(Farbe) 
� 
孔位（继电器可以有空）(Pin) 
� 
插槽(Steckplatz) 
� 
客户零件编号（Kundenteilenummer） 
� 
模块（Modul） 
 
 
 
 
 
 
 
 
 
保险丝目录 


### 第 38 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  38 / 129 
 
4.2  线束挂点 
 
线束挂点是将多个线束点进行连接。连接尺寸标注应至中心点处，挂点分为密封和不密
封挂点。 
 
4.2.1  焊接挂点 
表示法： 
参见标准 A1240045699 
关键词：  LVERB_gedichtet (焊接_密封) 
                  LVERB_ungedichtet（焊接_不密封） 
 
4.2.2  压接挂点 
参见标准 A2110000299 
关键词：CVERB_gedichtet(压接_密封) 
                CVERB_ungedichtet（压接_不密封） 
 
4.2.3  超声波焊接 
参见标准 A0090000299 
关键词：USVERB_gedichtet(超声波焊接_密封) 
                USVERB_ungedichtet(超声波焊接_不密封) 
 
 
 
 
 
图 15：挂点标记 
 
 
 
压接连接                              焊接挂点                   超声波焊接                          
密封 
 
 
 
 
 
不密封 
 


### 第 39 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  39 / 129 
 
4.2.4  其他关键词 
除了 4.2.1 至 4.2.3 提到的关键词，也可使用下列关键词： 
关键词 
说明 
LTG_END_gedichtet 
导线末端密封 
LTG_END_ungedichtet 
导线末端不密封 
P3-1_Marker_9mm_rt 
红色标记  9mm 
P3-2_Marker_9mm_bl 
蓝色标记  9mm 
P3-3_Marker_9mm_gn 
绿色标记  9mm 
P3-4_Marker_9mm_ge 
黄色标记  9mm 
P3-5_Marker_9mm_ws 
白色标记  9mm 
P3-6_Marker_9mm_gr 
灰色标记  9mm 
P3-7_Marker_9mm_br 
棕色标记  9mm 
P3-8_Marker_9mm_or 
橙色标记  9mm 
P3-9_Marker_9mm_vi 
紫色标记  9mm 
G10_Fixierer_19mm_sw 
固定  G10 19mm  黑色 
G12_Fixierer_25mm_sw 
固定  G12 25mm  黑色 
G31_Fixierer_25mm_sw 
固定  G31 25mm  黑色 
G28_Fixierer_19mm_sw 
固定  G28 19mm  黑色 
G30_Fixierer_9mm_sw 
固定  G30 9mm  黑色 
G30_Fixierer_19mm_sw 
固定  G30 19mm  黑色 
G32_Fixierer_9mm_sw 
固定  G32 9mm  黑色 
G32_Fixierer_19mm_sw 
固定  G32 19mm  黑色 
G33_Fixierer_19mm_sw 
固定  G33 19mm  黑色 
P_Fixierer_9mm_sw 
固定  P 9mm  黑色 
P_Fixierer_19mm_sw 
固定  P 19mm  黑色 
P_Fixierer_19mm_sw_2 
固定  P 19mm  黑色 
P6-1_Fixierer_19mm_ge 
固定  P6/1 19mm  黄色 
P6-1_Fixierer_19mm_sw 
固定  P6/1 19mm  黑色 
TB-A0009826702 
端子  防扭绞 
TB-A1729821302 
端子  防扭绞和螺母 
DIN46225_46236_4.3 
德标端子 4.3 
DIN46225_46236_4.3_I 
德标端子 4.3  防接触 
DIN46225_46236_5.3 
德标端子 5.3 
DIN46225_46236_5.3_I 
德标端子 5.3  防接触 
DIN46225_46236_6.5 
德标端子  6.5 
DIN46225_46236_6.5_I 
德标端子  6.5  防接触 
DIN46225_46236_8.4 
德标端子  8.4 
DIN46225_46236_8.4_I 
德标端子  8.4 防接触 


### 第 40 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  40 / 129 
 
DIN46225_46236_10.5 
德标端子  10.5 
DIN46225_46236_10.5_I 
德标端子  10.5  防接触 
DIN46225_46236_13.5 
德标端子  13.5 
DIN46225_46236_13.5_I 
德标端子  13.5 防接触 
PUR_SCHL_14x8x70_gr 
泡沫塑料管（防震） 
PUR_SCHL_15x5x40_gr 
泡沫塑料管（防震） 
PUR_SCHL_15x5x70_gr 
泡沫塑料管（防震） 
PUR_SCHL_20x10x30_gr 
泡沫塑料管（防震） 
PUR_SCHL_20x10x70_gr 
泡沫塑料管（防震） 
PUR_SCHL_20x10x140_gr 
泡沫塑料管（防震） 
PUR_SCHL_25x15x50_gr 
泡沫塑料管（防震） 
PUR_SCHL_25x15x70_gr 
泡沫塑料管（防震） 
PUR_SCHL_35x20x70_gr 
泡沫塑料管（防震） 
PUR_SCHL_40x30x90_gr 
泡沫塑料管（防震） 
PUR_SCHL_60x45x100_gr 
泡沫塑料管（防震） 
T8_Schlauch_sw 
热缩管  135℃ 
T9_Schlauch_sw 
热缩管  135℃，热缩比 2:1 
T9-1_Schlauch_rt 
热缩管  135℃，热缩比 2:1 
T9-2_Schlauch_sw 
热缩管  135℃，热缩比 3:1 
T9-3_Schlauch_rt 
热缩管  135℃，热缩比 3:1 
T10_Schlauch_sw 
丁基导线尾套  105℃ 
T11_Schlauch_sw 
含热熔胶的热缩管 110℃，热缩比 3:1/4:1 
T11-1_Schlauch_sw 
含热熔胶的热缩管 125℃，热缩比 4:1 
T12_Schlauch_sw 
含内胶的热缩管 125℃，热缩比 4:1 
T13-1_Band_sw 
密封胶带  105℃ 
T13-2_Band_sw 
密封胶带  125℃ 
T14_Schlauch_sw 
热缩管 125℃，与胶带 T13/2_Band_sw 结合使用 
T15_Schlauch_trans 
线束连接管 150℃，透明 
T15-1_Schlauch_bu 
线束连接管‐套圈  125℃ 蓝色 
Butyl 
丁基，无规格说明 
Butyl_Terostat_7 
TEROSTAT‐VII 4MM WH 15MMX160MX2MM‐40/+80C丁
基密封胶带 
Butyl_35x19x1.14 
 
Butyl_50x19x1.5 
Butyl-3631FR 50X19X1.5MMBK -40/+105 C丁基密封胶带 
Butyl_50.8x22x1.92 
Butyl-3631FR 50.8X22X1.92BK -40/105 C丁基密封胶带 
Butyl_77x21.5x2.15 
Butyl-3631FR 77X21.5X2.15BK -40/+105 C丁基密封胶带 
US_Schutzband_sw 
超声波焊接布基胶带，标准A0090000299（干区） 
US_Schrumpfschlauch 
超声波焊接热缩管，标准A0090000299（湿区） 


### 第 41 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  41 / 129 
 
4.3 回线或更改布线路径 
安全相关或者客户要求的回线或图纸上更改布线路径是被允许的。这样需在原理图上增加一
个导线布线点（RoutingPoint），参见 AV 执行标准原理图编制 AV0000069899。此方法不能用
在生产相关布线中。回线结构（双 H‐Doppel‐H）必须手动采用准确的布线路径，以获得线束
中（kbl）实际导线长度，也就是此处不能采用导线布线点（RoutingPoint）。 
 
4.4  位置表示法 
 
图 16：组装表盘式表示法（必须在图纸上注明） 
 
线束在图纸上时展开铺平式表示的，工装板用俯视图表示，未标识的固定件位置与插入方向
一致为 6 点钟方向。 
 
视线方向在图纸主线束处标出，在线束走向中可能会重复标出，视线方向一般是从主干指向
分支。 
 
 
 
 
 
 
 
 
 
—未标识固定件位置为 6 点钟方向 
工装板在 
固定件位置
插入方向位置 


### 第 42 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  42 / 129 
 
 
图 17：图纸上视线表示法（至少在图纸上注明一次） 
 
4.5  分支方向 
 
在图纸上正确标出分支走向。 
 
图纸上出线方向必须与线束分支方向一致，如果与实际走向有出入，必须给出实际分支走向
提示图纸。 
 
T‐出线必须给出标识。 
 
4.6  固定件 
未标识的固定件在线束上的位置一般为 6 点钟方向，且指向工装板面。如果位置有偏差，则
用表盘式方法标识。固定件图形表示法上没有位置信息。 
 
线束上固定件位置 
线束上固定件位置采用三角形加表盘式方法表示。 
 
表盘式表示法：位置 
不用考虑固定件关于位置的图形表示法。 
 
 
图 18:  固定件指向安装方向 9 点钟位置，尽管与图形表述不一致 
 
 
 
 
 


### 第 43 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  43 / 129 
 
4.7  线束上固定件位置和插入方向 
 
线束上固定件位置和固定件插入方向不一致时，需要另外通过方框加表盘式表示法标识出插
入方向。 
 
图 19:  固定件位于安装方向 9 点钟位置，插入方向指向 6 点钟方向 
 
 
    线束上固定件位置 
 
 
线束上插入方向 
 
4.7.1  扎带位置 
扎带表示法需要更多信息时，可采用提示图纸法给出说明。 
 
4.7.2 塑壳距离尺寸标注 
许多情况下，线束绝缘碰不到塑壳入口，并在塑壳前 30mm 处止住，在这种情况下可以取
消尺寸标注。 
 
与该规则有偏差的地方需要单独标注尺寸。 
 
 
 
 


### 第 44 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  44 / 129 
 
4.7.3  图纸上固定点命名 
 
图纸上固定点命名规则如下： 
 
前缀”FX”+分隔符+<结构空间简写，2 个字符>+分隔符+<数字，最多 6 位数>或者 
前缀”FX”+分隔符+<结构空间简写，3 个字符>+分隔符+<数字，最多 5 位数> 
 
分隔符=”.” 
 
举例： 
FX.IR.1000 
 
4.7.4  固定件尺寸标注 
 
除了位置表示法图纸上还需标出固定件的准确位置。这些从基准点（如护套、分支等）出发
通过合适的参考尺寸标注。 
 
 
图 20:  固定件尺寸标注示例（图纸摘录） 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 45 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  45 / 129 
 
4.8  包扎表示法 
 
无绝缘电线用一根直线标识。 
 
绝缘线束包扎则采用下列方法： 
穿编织管 
 
穿玻纤管 
 
穿软管 
 
稀包 
密包 
 
穿波纹管 
 
图 21：绝缘线束包扎方式表示法 
 
包扎标识采用组件数据库中的缩略形式，并在每个分支段注明。 
 
包扎标识以六边形标识，例如 
 
注意：尽量采用最薄的软管。 
 
4.8.1 非压接屏蔽层表示法 
 
屏蔽层不压接到塑壳上的屏蔽线在图纸上用线段（分支段）表示。未压接屏蔽层因此在二维
图纸上无法清楚标识。请参见原理图编制规范  A0000069899,原理图非压接屏蔽层表示法章
节。 
 
 
 
 
 
 
 
 
 
 
G19


### 第 46 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  46 / 129 
 
4.9  标签和标记 
 
标签和标记尺寸标注的基准点位于标签和标记的起点处，通用公差适用于此处。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 47 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  47 / 129 
 
4.10  公差 
 
4.10.1  分支段（点到点）公差 
通用公差适用于每段导线长度。 
 
因安装固定件插入的线束段尺寸标注不影响多线束段的通用公差。 
 
 
 
 
 
 
 
 
 
 
通用公差说明（必须在图纸上标出） 
 
线束分支段从起点至末点进行尺寸标注。每段都需标注尺寸，分支尺寸标注的基准点是线束
的中心点。 
 
分支段基准点尺寸标注 
对长度要求严格的装配位置可采用基准点尺寸标注。格式如下： 
 
分支段的公差即通用公差。 
公差如有出入必须专门标识出来，例如：Fix Maß 
必须给出基准点或通过刻度线标识出来。 
多个分支段采用基准点尺寸标注时不能出现缩短额定尺寸的情况。 
线束段通用公差： 
线束段是指节点到节点（分支） 
分支尺寸标注的基准点是线束的中心点！ 
 
未标注公差的导线长度公差： 
100mm 及以下的                                                      +10mm 
1000mm 及以下的                                                    +20mm 
1000mm 以上的                                                        +30mm 
结构空间简写‐可选，1 个字符 
数字编号—必须提供 
基准点尺寸标注符号—必须显示 


### 第 48 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  48 / 129 
 
4.10.2  固定件公差 
连续型尺寸标注时，两个固定件之间的公差为+5mm，（注意固定件‐模块变量）。 
 
分支段最大允许的公差对该分支段安装的固定件公差之和进行了限制。 
 
 
 
 
固定件公差说明（必须在图纸上注明） 
 
如果必须采用其他公差，则必须与戴姆勒相关负责人单独协商确定。 
 
固定件基准点尺寸标注： 
对长度要求严格的装配位置可采用基准点尺寸标注。这需要供应商和戴姆勒相关负责人协商
确定。 
 
 
图 22：固定件基准点尺寸标注 
 
基准点和固定件之间的公差为+5mm。 
与此不同的其他公差必须标识出来。 
基准点和标注点之间的距离尽可能不超过 1000mm。 
必须给出基准点或通过刻度线标识出来。 
多个固定件采用基准点尺寸标注时不能出现缩短基本尺寸的情况。 
必须标出分支段尺寸。 
 
 
 
 
 
 
一个分支段上固定件之间的公差为  +5mm 
不允许超过分支段的公差 


### 第 49 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  49 / 129 
 
4.10.3  橡胶件公差 
 
针对橡胶件，通用公差适用于其连接面。 
 
 
图 23：橡胶件连接面 
 
如果有其他公差或尺寸标注，则必须与戴姆勒相关负责人协商确定。 
 
5  线束尺寸检查 
 
为统一线束图纸尺寸针对关键点需编制测量文件（产品测量手册）。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 50 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  50 / 129 
 
6  数据传输流程 
 
数据传输必须采用安全方式以及戴姆勒规定的数据传输通道，Smaragd 中的数据提供（例如
通过 SMA‐web）需注意归档标准 Smaragd A0598004。禁止通过邮件发送开发数据。 
 
6.1  线束数据 
 
线束数据传输会用到下列软件系统： 
� 
CONNECT—（线束、零件和变更） 
� 
ACM 
� 
E3.Cable (ConnyE) 
� 
Service – Dokumentation 
� 
Smaragd 或 Sm@Web 
� 
EPDM 
� 
DIALOG（零件清单系统） 
� 
ZGDOK 
� 
CATIA 
� 
线束开发软件工具（无标准系统） 
 
 
 
 
 
 
 
 
 


### 第 51 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  51 / 129 
 
 
图 24：数据/软件工具 
 
线束开发基本流程软件见下图： 
      —组件管理（Component Management） 
      —电路设计（Elektrologik Erstellung） 
      —三维线束设计（3D Leitungssatzverlegung） 
      —二维图纸制作（2D Zeichnungserstellung） 
      —官方认可和文件（Offizielle Freigabe und Dokumentation） 
      —变更管理过程（Change Management Prozess） 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 52 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  52 / 129 
 
 
 
图 25：软件工具流程图 
 
为使数据传输流程图清晰易懂，下面是开发流程图概览： 
 
 
 
 
 
 
 


### 第 53 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  53 / 129 
 
 
 
图 26：数据传输 
 
数据传输时间点需要与相关项目及负责编制人协商确定。 
 
ELOG/PDF 
每张原理图以 eLog 和 PDF 格式输出。 
将数据输进 Connect 中。原理图是线束生产的重要数据。 
 
COMP: 
术语 COMP 是用于组件数据描述的多个数据文件/数据格式，可以理解为多个文件及数据格
式为一体的数据包。数据包从 Connect Parts 中获得，这些数据包会自动传输给后续过程： 
—VZK 目录（x  单个文本文件，包含使用目的目录） 
—CLS 目录（Lcable 数据库） 
—零件目录（） 
—XML  零件目录（XML Teilekatalog） 
 
 
 
 
 
 
 
变更管理 
二维图纸制作
组件管理 
检查/报告/审核 
官方认可/文件   
戴姆勒 
供应商 
三维线束设计 
原理图流程


### 第 54 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  54 / 129 
 
戴姆勒负责将 COMP 数据传输至后续过程中。 
 
GEO 
GEO 是只需填写三维数据的 KBL 文件。 
 
6.2  原理图数据 
 
原理图和原理图原始数据所有权为奔驰车/开发所有，按照 MBN 31 002 内部使用。图纸框架
和标题栏、数据库、模板，功能、代码和导线数据以及软件其他附加功能数据为 RD/EKL 部
门使用，并且必须约束使用。原理图编辑软件是 E3.Cable,以下称为原理图编辑软件，它可以
通过 Plugin ConnyE（插件 Connye）实现与 CONNECT 的数据交换。 
 
原理图、sys/功能相关的第一个结构层面已规范好。第 2 个结构层面和编号范围由线束工程
小组规定，且必须在项目启动前协商确定好。 
 
原理图内容参见原理图制作规范  A0000069899 原理图内容这一章节。 
 
公司专用信息只能在协商允许情况下授权使用，或者在数据传输至 MBC/D 前删除掉。 
 
 
 
 
 
 
 
 


### 第 55 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  55 / 129 
 
7 线束开发中的数据格式 
7.1 单线束、模块线束和 Master 线束 
 
7.1.1  单线束 
单线束是指至少包含一根导线及附件的采购零件，它作为单个零件按照 MBN  10137 在自己
的图纸上记录存档。 
 
 
 
图 27：单线束的 KBL 结构 
 
7.1.2  模块线束 
模块线束是指至少包含一根导线及附件的采购零件，它作为单个零件没有自己的图纸，而是
在表格图纸上按照 MBN 10137 进行记录存档。 
 
7.1.3 Master 线束 
Master 线束是指由多个模块线束的总成件的表格组成（无图纸零件）。一般是指由多个模块
和/或可选 Add‐On 线束组成的 Master 线束。 
 
 
 
 
有自己图纸的线束 
零件数据库 
线束图纸 
MBC 编号 
名称 
版本 
Harness Container 
线束 
MBC 编号 
名称 
版本 
模块 
Harness Container 和模块的 MBC 编号、
名称和版本等一致。 


### 第 56 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  56 / 129 
 
Master 线束是 KSL(KSK)的基础。它包含基本模块，可以和不同零件、可选 Add‐On 线束组成
KSK 线束。一个 Master 线束由基本模块和模块线束组成。Master 的 HCV 或 KBL 数据一定包
含 Master 相关的模块线束。 
 
线束命名以 TAB ZB Elektr.Leitungssatz 开头。每个模块线束还会线束模块编号 V...。图纸必须
和 MBN 10137 规定的表格图纸相一致。 
 
 
 
图 28：Master 线束的 KBL 结构 
 
表格图纸有自己的编号，它不会隐藏采购零件。 
采购零件以编号显示在表格图纸上。 
 
 
 
 
 
 
 
 
无图纸零件的表格图纸 
线束图纸 
MBC 编号 
名称 
版本 
Harness Container 
线束 1 
无图纸零件 MBC 编号
名称 
版本 
模块 1 
线束 2 
无图纸零件 MBC 编号
名称 
版本 
模块 2 
线束 X 
无图纸零件 MBC 编号
名称 
版本 
模块 X 
零件数据库


### 第 57 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  57 / 129 
 
转换为 KBL 数据时表格显示为 Harness Container,采购零件按照模块号显示。 
 
7.2 KBL 和 HCV 数据命名规范 
命名规则： 
<MBC 编号>_<ZGS>_<数据状态>.kbl 
<MBC 编号>_<ZGS>_<数据状态>.hcv 
 
示例： 
ZGS:001 
MBC 编号：A1665406503 
数据状态：2013‐11‐21 
KBL 数据格式：kbl 
 
数据命名由不含空格/特殊字符的编号、ZGS(3 位数)、数据日期（年月日）和数据格式（kbl/hcv）
组成。分隔符采用下划线。Master 线束的 MBC 编号与表格图纸的 Master 线束编号/基本编
号一致。 
 
示例： 
A1665406503_001_20131121.kbl   
A1725408709_002_20100630.kbl   
A2225403908_012_20131203.hcv   
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 58 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  58 / 129 
 
7.3 KBL 
KBL 是线束图纸电子数据，它阐述了导线连接、零件、线束分支拓扑结构、二维及三维图和
导线物理长度及其线束基础源文件（原理图和 DMU‐3D 模型）。 
 
KBL 从开发工具中生成，必须也可以在没有图纸条件下能将线束（拓扑和电气）完整记录存
档，为编制源文件如原理图和 DMU 拓扑数据提供源数据和参考。 
 
KBL（线束清单）必须满足标准 ISO 10303‐212 和 KBL 版本的 XML 图要求，在 XML 图中采用
规定的字符填写方式。 
 
线束文件、奔驰车标准构件供应、零件清单系统 DIALOG 以及线束数据库内部查阅都会用到
KBL 数据。 
 
KBL 认可周期需与 MBC（奔驰车开发部门）协商确定。 
 
7.3.1  基本要求 
 
因为可以自由选择线束开发软件，用于存档的 KBL 作为输入数据会用于许多内部流程，因此
必须确定 KBL 结构和制定 KBL 规则。 
 
7.3.2  内容和结构 
Harness Container 和模块的 KBL 结构与 MBC 使用的文件格式表格图纸一致，可以按照 1:1 使
用。 
Klasse Harness 是指包含线束图纸内容的 Container，单根或多根线束信息会在其中记录存档。 
 
图纸标题栏属性如编号、名称、变更描述等要输入进 Klasse Harness 中相应的属性中。 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 59 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  59 / 129 
 
Master 线束： 
Master 线束的编号、版本、名称、变更说明（与 Master 图纸标题栏属性一致）要输入进 Klasse 
Harness 属性中。单个无图纸零件的技术信息及零件号、版本、名称和变更说明作为模块也
放置在 Klasse Harness 中。 
当前 ZGS（图纸几何状态）下的图纸标题栏显示的状态或图纸变更说明要输入进 Harness 
Container 中的 Klasse Change 内。 
表格图纸中包含无图纸零件表的状态或变更说明要输入进各自模块的 Klasse Change 内。 
 
单线束： 
单线束同样编号、版本、名称、变更说明要输入进 Klasse Harness 属性中。线束的技术信息
及零件号、版本、名称和变更说明作为模块也放置在 Klasse Harness 中。 
 
无图纸零件表格的编号和名称要输入进 Klasse Module 的属性中。 
 
每根线束会设置一个模块，线束的技术信息都记录在模块内。 
 
7.3.3  规定 
 
*******  尺寸和尺寸单位 
Klasse Units(Klasse 单位)中包含对所有 KBL 中应用到的尺寸单位的规定。 
 
规定如下： 
� 
导线长度和尺寸点到尺寸点之间的长度单位为 mm,且没有小数位。 
� 
每个子件如塑壳重量单位为 g，且没有小数位。 
� 
导线重量单位 g/m,且没有小数位。 
� 
线束模块重量单位为 kg,最多保留 3 位小数。 
 
这些规定必须应用到各自的开发工具中。 
 
 
 
 
 
 
 
 
 
 
 


### 第 60 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  60 / 129 
 
7.3.2 Connect 数据库零件等级分类 
该对照应能实现 Connect 数据库零件在 KBL  Container 中的各自大致分类，个别情况下根据
零件的类型和使用可以有出入。 
 
Connect  零件 KBL 分类 
包扎物                  线束保护包扎 
盲塞                      附件 
卡扣                      固定件 
子件                      附件 
防水栓                  附件 
电气元件              零部件 
卡扣（Halter）    固定件 
扎带                      固定件 
支架                      固定件 
搭铁端子              塑壳 
端子                      端子 
塑壳                      塑壳 
标准件                  端子/固定件 
管夹                      塑壳 
卡圈                      固定件 
软管                      线束保护包扎 
保险丝                  附件 
保险丝盒              塑壳 
其他                      零部件/附件 
橡胶件                  固定件 
 
7.3.3 Occurrence Klassen 分类 Id 命名 
分类和属性命名见 KBL 图。但 Klassen  的 Id 在图中没有定义，一般情况下可以自由选择。 
属性 Version_ID 用于系统标识 KBL 正确版本。由于 KBL 范围内的系统已开发出来，可以以不
同方式联系到该 Id,因此对某些范围内的 Id 命名作了规定。 
 
 
 
 
 
 
 
 
 


### 第 61 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  61 / 129 
 
下列 KBL‐Occurences 中的 ID‐前缀对 EE‐Browser 非常重要，结构必须按照下列命名： 
 
ID‐前缀 
对象类型 
ID_BNJ 
节点（挂点） 
ID_BNS 
分支段 
ID_CON 
塑壳 
ID_ACC 
附件 
ID_PLU 
盲塞 
ID_SEA 
防水栓 
ID_TER 
端子 
ID_TAP 
ID_WIR 
导线保护设置（包扎） 
基本导线设置（导线、电缆） 
ID_FIX 
固定设置（卡扣/固定件） 
ID_TXT 
TBD(过程信息) 
ID_TCN 
TBD 
ID_IMG 
TBD 
 
7.3.4 外部参考资料 
它是线束或模块中用到的子件外部参考资料清单或编制源文件如原理图和 DMU 拓扑数据用
到的外部资料清单。 
文件类型通过属性 Dokument_type 区分.每份参考资料有自己的 Id 编号，使用时可以调取出
来。 
文件类型”Circuit Wiring”和”Wiring Construction Unit”还需遵守： 
Master 线束：Master 线束模块引用的原理图和 DMU 模型需在 Ebene  Harness 的外部参考资
料中引用。如果 Master 的所有模块没有用到原理图和 DMU 模型，则需在 Ebene Modul 的外
部参考资料中引用。 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 62 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  62 / 129 
 
7.3.3.5  数据状态 
无论软件系统版本如何，线束图纸和原理图还要再增加一个数据版本标记并记录存档。 
 
该标记在编辑时会自动生成，并输入进图纸标题栏相应的区域内。它包含符合 DIN ISO 8601
标准格式的日期，并能实现图纸归类。当线束模块和/或 Master 线束出现变更时数据状态也
会相应调整。当 Master 线束的单个模块没有更改时，数据状态就会继续保留。 
 
示例：2013‐12‐01 
 
******* MBC 编号 
MBC 编号一般在属性<Part_number>输入，供应商编号在属性<Alias_id>中输入。 
 
根据 MBN 标准的编号                                                        A 222 540 93 00 
编号书写方式                                                                      AXXXXXXXXXX 
之前零件编号<Predecessor_part_number>的书写方式  AXXXXXXXXXX 
其他书写方式                                                                      A XXX XXX XX XX 
 
其他书写方式只适用于 Klassen Harness 和 Modul 中的属性<Part_number>. 
 
原理图编号： 
原理图编号由不含空格符/特殊字符的<MBC 编号>和页码组成，分隔符采用下划线。 
 
原理图编号=<MBC 编号>_<页码> 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 63 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  63 / 129 
 
米制产品（导线）属性<Part_number>书写需遵守以下规定： 
胶带、固定胶带和软管的编号为 Connect 零部件数据库中的简写。 
KBL 属性 Part_number 采用下列名称 
 
胶带示例： 
G19/4                                                      <零件号>/<颜色代码> 
 
固定胶带示例： 
G19/3                                                      <零件号>/<颜色代码> 
 
标识胶带示例： 
G19/1                                                      <零件号>/<颜色代码> 
 
软管示例： 
W4/6.11                                                  <零件号>/<颜色代码>.<额定宽度/软管内径> 
 
搭铁端子示例： 
DIN46225_46236_8.4                              <关键词> 
DIN46225_46236_13.5                            <关键词> 
 
挂点示例： 
LVERB_gedichtet_60‐90(焊接密封挂点)                  <关键词>_<额定尺寸> 
USVERB_ gedichtet_60‐90(超声波焊接密封挂点)    <关键词>_<额定尺寸> 
 
导线末端示例： 
LTG_END_gedichtet                                  <关键词> 
 
产品标识示例： 
MBN33015_Kennzeichnung(标识)          <标准> <关键词> 
 
*******  编号命名 
编号命名基础见操作手册 A 059 8031.SRM 中编号命名必须准确应用到 KBL 数据中。 
 
 
 
 
 
 
 


### 第 64 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  64 / 129 
 
******* MBC 位置编号 
位置编号是 MBC 零件的特征，它可以从 Connect 零件库中获取。在 KBL 零件数据中（如塑
壳）每个零件号的位置编号可以在属性<Abbreviation>中说明。 
 
 
7.3.3.9  导线长度 
为能准确计算电阻、铜重和其他电性能值必须在 Klasse<Wire_length>输入计算过的或测量得
到的导线长度值。 
长度类型<Length_type>值： 
 
DMU 长度                    =DMU 长度（如 CATIA;虚拟 DMU 长度） 
图纸长度                      =DMU 中整数化的长度，无附加 
端子长度                      =含插件的经过计算的长度‐增加到端子的距离 
生产长度                      =含插件的经过计算的长度‐有附加以及经过计算的生产余量 
增补                              =实际分支的生产余量（附加） 
 
成品认可时需给出经过计算或测量过的生产长度值。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 65 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  65 / 129 
 
 
 
7.3.4 KBL Container 
KBL  Container 除含有 Klassen  Harness、单位、参考、布线表以及导线子件清单的数据库外，
还包含 KBL 版本号 version_id. 
 
 
 
长度类型：图纸长度 
DMU 中塑壳末端之间的长度（整数化） 
长度类型：端子长度 
图纸上含塑壳到端子之间的长度（整数化） 
长度类型：生产长度 
含塑壳附加和生产余量的长度（整数化） 


### 第 66 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  66 / 129 
 
图纸上用到的子件如塑壳、导线等会在 KBL_Container 中显示其技术特征，线束模块用到时
可以从中获取引用。 
 
下面章节会对这些分类更详细描述，Harness Container 见章节 7.3.5. 
 
7.3.4.1 KBL  版本 
为确保应用软件的兼容性，必须遵守执行规范 KBL‐Version V2.3SR‐1. 
 
其他 KBL 版本的释放周期需与 MBC 协商一致。 
内容描述可以使用 KBL‐图中协商一致的字符表。 
 
属性 Version_ID 是为识别其他软件系统的 KBL 正确版本，并进行必要的管控调整，如在输进
数据库时进行调整。 
 
属性 Version_ID 允许输入方式： 
V2.2  或者  2.2 
V2.3  或者  2.3   
V2.3SR‐1  或者  2.3SR‐1 
 
示例：V2.3SR‐1 是 VDA 根据 Schema2.3SR‐1 规定的 KBLs 书写方式。 
 
 
 
7.4.3.2  附件 
该分类包含所有 Connect 中其他、盲塞、橡胶件、保险丝和管夹的子件/附件，这里的零件
不是用于固定以及不能直接划分到塑壳导线连接上的零件。 
 
 
 
 
 
 
 
 


### 第 67 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  67 / 129 
 
此外附件还可包括标签、色标胶带、消耗材料如焊锡。 
� 
必填属性 
 
Part_Number                                <MBC 编号>或<标准件编号> 
Abbreviation                                  <MBC 位置编号>，参见章节 ******* 
Description                                    零件名，参见章节 ******* 
External_references                      引用分类外部资料数据库 
Mass_information                        尺寸单位和数值 
 
 
� 
可选属性 
 
Company_name                            Connect 零件库中的供应商名称 
Version                                          供应商零件的零件版本 
Accessory_type                            <关键词> 
Material_information 
Material_key 
Alias_id/Scope                              供应商零件号/供应商标记 
 
*******  坐标系 
该分类包含线束挂点和零部件的 2D/3D 坐标。 
� 
属性内容 
 
id                                                  坐标编号 
坐标                                              XYZ 顺序的坐标清单 
 
 
 
 
 
 
 
 


### 第 68 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  68 / 129 
 
 
 
*******  盲塞 
该分类包含 Connect 中所有直接用于导线连接的盲塞零件。 
� 
必填属性 
Part_Number                                <MBC 编号> 
Description                                    零件名，参见章节 ******* 
Abbreviation                                  <MBC 位置编号>，参见章节 ******* 
External_references                      <MBC 编号>或 ID/Optional 
Wire_size/Minium                        最小线径 
Wire_size/Maxium                        最大线径 
Colour                                            盲塞颜色 
Plug_Type                                      <表格值 Listenwert>/Optional（可选） 
Mass_information                        尺寸单位和数值/每个重量 g 
 
� 
可选属性 
 
Company_name                            Connect 零件库中的供应商名称 
Version                                          供应商零件的零件版本 
Alias_id/Scope                              供应商零件号/供应商标记 
 
 
7.3.4.5  防水栓 
该分类包含 Connect 中所有直接用于导线连接的防水栓零件。 
 


### 第 69 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  69 / 129 
 
� 
必填属性 
Part_Number                                <MBC 编号> 
Description                                    零件名，参见章节 ******* 
Abbreviation                                  <MBC 位置编号>，参见章节 ******* 
External_references                      <MBC 编号>或 ID/Optional 
Wire_size/Minium                        最小线径 
Wire_size/Maxium                        最大线径 
Colour                                            防水栓颜色 
Seal_Type                                      <表格值 Listenwert>/Optional（可选） 
Mass_information                        尺寸单位和数值/每个重量 g 
 
� 
可选属性 
 
Company_name                            Connect 零件库中的供应商名称 
Version                                          供应商零件的零件版本 
Alias_id/Scope                              供应商零件号/供应商标记 
 
 
 
*******  零部件 
该分类包含 Connect 不直接归类到导线连接的所有零部件。 
� 
必填属性 
Part_Number                                <MBC 编号> 
Description                                    零件名，参见章节 ******* 
Abbreviation                                  <MBC 位置编号>，参见章节 ******* 
External_references                      <MBC 编号>/Optional 
Abbreviation                                  <MBC 位置编号> 
Mass_information                        尺寸单位和数值/每个重量 kg 
 
 
 
 
 
 


### 第 70 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  70 / 129 
 
 
 
� 
可选属性 
 
Company_name                            Connect 零件库中的供应商名称 
Version                                          供应商零件的零件版本 
Processing_information 
Alias_id/Scope                              供应商零件号/供应商标记 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 71 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  71 / 129 
 
7.3.4.7  塑壳 
该分类包含 Connect 中所有直接归类到导线连接的塑壳、管夹和搭铁端子零件。 
� 
必填属性 
 
Part_Number                                <MBC 编号> 
Abbreviation                                  <MBC 位置编号>，参见章节 ******* 
External_references                      <MBC 编号>/Optional 
Housing_type                                <关键词> 
Housing_colour                              塑壳颜色 
Housing_code                                塑壳名称 
Mass_information                        尺寸单位和数值/每个重量 kg 
Description                                    零件名，参见章节 ******* 
Slots/Number_of_cavities/Cavity_number    塑壳名称/孔位数/孔位号 
 
 
 
� 
可选属性 
 
Company_name                            Connect 零件库中的供应商名称/Optional（可选） 
Version                                          供应商零件的零件版本/Optional（可选） 
Material_information                    Optioanl(可选) 
Material_key                                  Optioanl(可选) 
Alias_id/Scope                              供应商零件号/供应商标记 
 
 
 
 
 
 
 


### 第 72 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  72 / 129 
 
7.3.4.8  外部参考 
该分类包含线束中用到的零件或编制源文件如原理图和 DMU 拓扑数据的外部参考资料清单。
这种情况下只需对外部进行定义。 
 
以下属性是必填项： 
 
 
� 
属性文件类型 Document_type 的关键词 
 
<Drawing>                      无图纸零件的提示图纸参考见数据库的零件层面。 
<Circuit Wiring>              参考原理图 
                                        如果原理图涉及到所有模块，外部参考的 ID 号引用 Harness(线
束层面)，否则引用 Modul 模块层面。 
<Wiring Construction Unit> DMU‐KBL 数据引用 
如果 DMU 模型涉及到所有模块，外部参考的 ID 号引用
Harness(线束层面)，否则引用 Modul 模块层面。 
        <Deviation Table>          与地域相关的零部件应用引用外部偏差表时需引用 Harness 线束
层面的。 
 
� 
属性文件号 Document_number 内容 
<MBC Sachnummer>          无图纸零件的提示图纸 
<Schaltplansachnummer>  原理图编号 
<MBC DMU‐Sachnummer> DMU‐KBL 编号 
<MBN...>                              标准件的标准号 
 
 
 
 
 
 
 
 
 
 


### 第 73 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  73 / 129 
 
� 
属性变更级别 Change_Level 内容 
JJJJ‐MM‐TT                    原理图的数据状态日期 
<Smaragdversion>        DMU 模型的 Smaragd 版本，示例：0004.001 
JJJJ‐MM‐TT                    无图纸零件的提示图纸上的日期 
 
� 
属性位置 Location 内容 
当没有属性 Description 时，其名称可以在这里填写 
引用数据的“名称” 
 
� 
属性数据格式 Data_format 内容 
可选 
 
� 
属性生成系统 Creating_system 清单 
<Connect>                来自数据库 Connect 系统的零件 
<e3s>                        来自开发系统 E3.Cable 中的原理图 
<Catia V5>                来自开发系统 Catia V5 中的 DMU KBL 数据 
<Siemens NX>          来自开发系统 Siemens NX 中的 DMU KBL 数据 
 
*******  固定件 
该分类包含 Connect 中其他、卡扣、包扎物、支架、橡胶件和卡圈的所有子件/附件，这些
零件是用于固定线束，但不能直接归类于导线连接。 
 
� 
必填属性内容 
 
Part_Number                                <MBC 编号>或<标准件编号> 
Company_name                            Connect 零件库中的供应商名称/Optional（可选） 
Version                                          供应商零件的零件版本/Optional（可选） 
Description                                    零件名，参见章节 ******* 
Abbreviation                                  <MBC 位置编号>，参见章节 ******* 
External_references                      <MBC 编号>/Optional 
 
 
 
 
 
 
 
 
 


### 第 74 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  74 / 129 
 
 
� 
可选属性 
 
Fixing_type                                    <关键词> 
Mass_information                          尺寸单位和数值 
Material_information                     
Material_key                                 
Alias_id/Scope                              供应商零件号/供应商标记 
 
********  端子 
该分类包含 Connect 中所有直接归类到导线连接的端子零件。 
 
� 
必填属性内容 
 
Part_Number                                <MBC 编号>或<标准件编号> 
Description                                    零件名，参见章节 ******* 
Abbreviation                                  <MBC 位置编号>，参见章节 ******* 
External_references                      <MBC 编号>/Optional（可选） 
Terminal_type                              <关键词>/Optional（可选） 
Plating_material                            镀层/Optional(可选) 
Cross_section_area                      最小‐最大截面积/Optional(可选) 
Mass_information                        尺寸单位和数值/单位重量 g 
 
 
 
 
 
 
 
 
 


### 第 75 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  75 / 129 
 
 
� 
可选属性 
 
Material_information 
Material_key                         
Company_name                            Connect 零件库中的供应商名称 
Version                                          供应商零件的零件版本 
Alias_id/Scope                              供应商零件号/供应商标记 
Outside_diameter                        外径 
 
7.3.4.11  导线 
为能清晰描述每个导线类型的所有可能出现的导线配置需要 500 个以上的编号。戴姆勒内部
流程中这些编号根据导线特征自动生成，并在内部流通使用。 
该编号（零件号）和名称（描述）由导线类型简称、线径、和线色组成，必要信息可以通过
ConnectParts 中的 ConnyE‐Schnittstelle 获得。 
 
示例中的值在 KBL 中必须按照要求填写。 
 
如果必须给出戴姆勒零件号或标准件编号，则需输进属性 Part_Number 中。否则采用下列表
示法： 
 
 
 
 
 
 
 
 
 
 
 


### 第 76 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  76 / 129 
 
� 
单线 ly‐0.35BK/RD 内容示例 
Part_Number                ly‐0.35          BK/RD              导线零件编号 
Description                    ly‐0.35BK/RD                        导线名称 
Abbrevation                  ly                                            导线类型/绝缘材料 
Cross_section_area      0.35                                        线径 
Cover_colour                                                              线色 
Colour_type(base colour) BK                                      主色 
Colour_type(second colour) RD                                  辅色 
WireType                          ly‐0.35                                导线类型/线径 
 
� 
可选属性 
Material_information 
Mass_information                                尺寸单位和数值/每米重量 kg 
Alias_id/Scope                                      供应商零件号/供应商标记 
Bend_radius                                          弯曲半径/Optional(可选) 
Outside_diameter                                外径 
 
 
� 
特殊导线 B48‐2*0.5‐RD/BK‐YE/BK 内容示例 
Part_Number                B48‐2*0.5‐RD/BK‐YE/BK      导线零件编号 
Abbrevation                  B48                                        导线类型/绝缘材料 
Description                    B48‐2*0.5‐RD/BK‐YE/BK      导线名称 
WireType                        B48‐2*0.5                            导线类型/线径 
Cover_colour                                                              线色 
Colour_type(base colour) NI                                      主色 
 
 
 
 
 
 
 
 


### 第 77 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  77 / 129 
 
 
� 
可选属性 
 
Cross_section_area                                            线径 
Material_information 
Mass_information                                              尺寸单位和数值/每米重量 kg 
Alias_id/Scope                                                    供应商零件号/供应商标记 
Bend_radius                                                      弯曲半径/Optional(可选) 
Outside_diameter                                              外径 
 
� 
特殊导线中芯线信息 
Core 
Part_Number                      B47‐0.5YE/BK 
Description                          B47‐0.5YE/BK 
Cover_colour                        OG                          特殊导线的外皮颜色 
Colour_type(base colour)    YE                            主色 
Colour_type(second colour) BK                            辅色 
Cross_section_area              0.5                            线径 
 
 
 
 
 
 


### 第 78 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  78 / 129 
 
********  节点 
该分类包含线束拓扑内分支和节点（零部件）。 
 
挂点 1 示例：零部件的连接点（ID_CON128） 
挂点 2 示例：线束拓扑内的一次分支点（Knoten,BNJ2） 
 
BNJ 和 CON 名称来源于 Catia. 
� 
必填属性 
id                                          开发软件导出的挂点编号 
Cartesian_point                    Cartesian_point(坐标点)分类中的连接编号 
Referenced_compenents      起点和末点处的零部件参考 
 
 
7.3.4.13  连接/布线 
该分类包含所有电气连接（导线）中用到的线束段清单。 
� 
属性信息 
id                                            Routing 布线编号 
Routed_wire                          Connection 分类中的连接编号 
Segments                                线束段（分支段）的 id 清单 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 79 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  79 / 129 
 
 
7.3.4.14  分支段 
该分类包含线束所有的分支段。 
� 
必填属性 
id                                                开发软件导出的挂点编号 
Virtual_length                            DMU 长度，单位：mm 
Physical_length                          物理长度，单位：mm 
End_node                                  分支尾端节点的 ID 号 
Start_node                                分支起点的 ID 号 
Center_curve                            degree=1,中线 
Control_points                          分支坐标清单 
 
 
 


### 第 80 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  80 / 129 
 
� 
可选属性 
Cross_section_area_information                                线径，单位：mm2 
Value_determination                                                  测量计算过的截面积 
Value_component                                                      测量过的计算过的数值 
Unit_component                                                        尺寸单位 
 
Protection_area                                                          导线保护描述 
Start_location                                                              0=节点开始处 
…End_location                                                              1=塑壳末端 
Associated_protection                                                导线保护媒介 
 
Fixing_assignment                                                      固定件 
Location                                                                      固定件位置 
Orientation                                                                  固定件安装方向 
Fixing                                                                          固定件 ID 号 
 
********  尺寸单位 
该分类包含线束中用到的尺寸单位规定。尺寸单位要明确，使用时可以调用出来，也可以参
见章节 *******. 
 
********  线束包扎 
该分类包含用于线束保护的所有套管和胶带。 
� 
必填属性 
id                                          线束保护的编号 
Part_Number                        <MBC 编号>或<关键词> 
Description                          零件名 
Abbreviation                        <MBC 位置编号>,参见章节 ******* 
External_references              External_references 的 ID 编号/Optional(可选) 
Mass_information                尺寸单位和数值/包扎范围 
 
 
 
 
 
 
 
 
 
 


### 第 81 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  81 / 129 
 
 
� 
可选属性 
Company_name                          Connect 零件库中的供应商名称 
Version                                          供应商零件的零件版本 
Processing_information                包扎距离的<关键词> 
Materialbezeichnung                    <关键词> 
 
7.3.5 Harness Container(线束容器) 
该分类…_occurrence 由 KBL 处理器生成，一般情况下由用途或库零件组成。此处只对数据库
中的有效数据作精确说明，这些库在其他过程中也会用到，甚至特别情况下也需进行编辑。 
 
下面将会对这些分类进行详细说明，模块分类详细说明见章节 7.3.6. 
 
******* Harness‐Container 属性 
� 
Harness 中必填属性 
 
Part_Number                                Master 线束:表格编号  <MBC 编号> 
Company_name                            Connect 零件库中的供应商名称<Company_name> 
Description                                    图纸标题栏中的<Benennung>,参见章节 ******* 
Abbreviation 
Version                                          图纸标题栏中的数据状态<Datenstand> 
Predecessor_part_number            前一版图纸<MBC 前一版零件编号>,参见章节 ******* 
 
 
 
 
 
 
 
 
 
 


### 第 82 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  82 / 129 
 
Copyright_note                                  戴姆勒集团版权所有，遵守 DIN ISO 16016 保护条款 
Mass_information                              尺寸单位和数值，单位：Kg 
Content                                              单线束：harness subset(线束子集) 
                                                            Master 线束：harness complete set(整套线束) 
 
� 
可选属性 
Alias_id/Scope                                  供应商零件号/供应商名称 
Material_information 
External_references                          所有外部参考资料的 ID 编号，定义见章节 7.3.4.8 
 
� 
需和项目组协商确定的属性（可选） 
Degree_of_maturity(成熟度)            实施日期，示例：从 Ae‐Paket2 起 
Car_classification_level_2                  配置/操纵，示例：V/W/X,LL 
Car_classification_level_3                  执行规格 
Model_year                                        生产年份，示例：2013 
 
Version 中版本名=文件的数据状态。编号和版本号可以和不同类型的文件如图纸、KBL 相联
系。 
线束版本识别时在 Version 中输入符合 DIN ISO 8601 标准的数据状态（Datenstand）的日期。
KBL 中属性<Version>在线束要素和模块要素下。 
 
属性 Harness→Version 是指 Master 线束的表格图纸的数据状态，或单线束的数据状态。 
 
属性 Module→Version 是指线束模块的数据状态，如果是单线束则和 Harness‐Version 一样。 
 
 
 
 
 
 
 
 
 
 


### 第 83 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  83 / 129 
 
 
表格/Master(Harness→Version)的数据状态与模块的数据状态（Module→Version）要区分开
来。 
 
7.3.5.2  变更 
线束变更按照标准 MBN  31  020‐3 要求显示在线束图纸上。将线束图纸的相关内容输入进
Change（Harness 或 Module 下）中。 
 
图纸上（来自图纸标题栏或无图纸零件的表格）相关变更信息属性如下： 
 
� 
属性 id 信息 
该属性包含 ZGS 号，见 ZGS 一栏 
文本示例：001 
 
� 
属性 Change_date 信息 
该属性是指变更日期，见 Änderungsdatum 一栏，按照标准 DIN  ISO  8601“数据要素及
格式”KBL 中的变更日期需显示完整。 
文本示例：2012‐10‐15 
 
� 
属性 Description 信息 
该属性是指变更描述。见 Änderungsbeschreibung 一栏 
文本示例：变更新绘图 
 
� 
属性 Approver_name 信息 
该属性是指（戴姆勒的）开发负责人,见 Bearbeit./auth.一栏 
示例：Mustermann 
 
� 
属性 Approver_department 信息 
该属性是指（戴姆勒的）负责部门，见 federf. Abteilung/resp.dep.一栏 
 
 
 


### 第 84 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  84 / 129 
 
示例：RD/EKL 
� 
属性 Change_request 信息 
该属性是指工程更改申请单号 KEM，见 Auftr.‐Nr./order no.一栏 
示例：YAP204711 
 
� 
属性 Responsible_designer 信息 
该属性是指供应商表中的开发负责人（供应商）。 
示例：Hirschmann/Ziegler 
 
� 
属性 Designer_department 信息： 
该属性是指供应商表中的公司部门（供应商）。 
示例：GT/SB12 
 
7.3.5.3 Accessory_occurence(附件设置) 
该分类是指线束中用到的附件应用。 
 
内容：      线束中用到的附件 
                  Accessory 中的库零件 
                  主要零件如塑壳 
                  坐标 
 
******* Component_occurence(零部件设置) 
内容：      线束中用到的插件和塑壳 
                  Accessory 中的库零件 
 
******* Connector_occurence(塑壳设置) 
内容：      Connector 中的插件和塑壳 
 
应用说明的功能块（REF）简称即为 ID 编号，其全命名需输入进属性 Description 内。Connect
数据库中需有功能块本身，并归类到相应的项目内。 
 
 
 
 
 
 
 
 
 


### 第 85 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  85 / 129 
 
� 
属性信息 
id                                                  VZK 中的功能块（REF）简称 
Description                                  VZK 中的全命名 
 
 
******* Fixing_occurence(固定件设置) 
该分类是指线束中所有用到的固定件应用。 
 
内容：                线束中用到的固定件 
                            线束中的参考部分，如线束分支 
                            坐标 
 
******* General_wire_occurence(导线设置) 
内容：                线束中用到的导线（General_wire） 
                            线束连接（Connection） 
                            该应用的操作说明 
                            生产过程中的导线长度 
 
� 
属性 Length_information 信息 
Length_type              Production_length                  生产过程中的长度 
Length_value            生产过程中的长度值 
 
7.3.5.8 Terminal_occurence(端子设置) 
内容：                线束中用到的端子（General_Terminal） 
                            相应塑壳上的接触点 
 
7.3.5.9 Wire_protection_occurence(导线保护设置) 
内容：                待保护的线束段 
                            待保护的线束分支 
                            Wire_protection 中用到的库零件 
� 
属性 Protection_length 内容 
Value_component  待保护的线束段长度数据，单位：mm 
 
 


### 第 86 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  86 / 129 
 
*******0 Harness_configuration(线束配置) 
该分类是指线束的所有属性以及所有模块汇总和 Change 中的图纸变更历史。 
 
内容：                    涉及模块（图纸上的无图纸零件） 
                                图纸上的变更历史 
 
******** Connection(连接) 
General_wire_occurrence 信号间的连接（通过 Connector_occurrence 清单中的两个接触点） 
 
7.3.6  模块（Module） 
 
*******  模块属性 
� 
模块中的必填属性信息 
Part_Number                                          <MBC 编号> 
Company_name                                      Connect 零件库中的供应商名称<Company_name> 
Description                                              <Benennung>,参见章节 ******* 
Abbreviation                                            表格图纸的模块变量 
Version                                                      单线束：数据状态<Datenstand>:图纸标题栏中的
数据状态 
Predecessor_part_number                  前一版图纸<MBC 前一版零件编号>,参见章节
******* 
Copyright_note                                  戴姆勒集团版权所有，遵守 DIN ISO 16016 保护条款 
Mass_information                              尺寸单位和数值，单位：Kg 
Content                                                  模块 
Module_configuration                        线束模块所有子件清单 
 
� 
可选属性 
Alias_id/Scope                                  供应商零件号/供应商名称 
Project_number                                项目名称，示例：BR172 
 
 
 
 
 
 
 
 
 
 


### 第 87 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  87 / 129 
 
External_references                            所有外部参考资料的 ID 编号清单，定义见章节
7.3.4.8 
� 
需与项目组协商确定的属性 
Degree_of_maturity(成熟度)            实施日期 
Material_information 
Car_classification_level_2                  配置/操纵 
Car_classification_level_3                  执行规格 
Model_year                                        生产年份 
 
注意：模块属性中 Part_Number 的 MBC 编号前 3 个数字必须与 Connect 数据库项目一致。 
示例：MBC 编号 A 222 540 93 00→项目 C222 
 
Master 线束中包含的模块线束在属性 Abbreviation 中显示模块变量编号。 
示例模块变量：V11 
单线束则属性 Abbreviation 不填。 
 
7.3.6.2  模块变更 
见章节 7.3.5.2 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 88 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  88 / 129 
 
7.4 数字化接线表 
原理图数据根据 AV 标准原理图编制进行编制，和项目组协商一致后输入进 ConnectHarness 
DV 系统中。该数据由一份 PDF 数据和 MBC 要求的数字化接线表（如 e.LOG）组成。 
 
7.5 TIFF 
戴姆勒集团通过电子放行流程将图纸数据输入进 Smaragd，并长期保存在 ZGDOK 系统中。 
数据按照文档管理系统 ZGDOK 要求进行编制，在 MBC 首次应用前需获得认可。 
 
ZGDOK 要求： 
TIFF G4 
像素要求：条形 
颜色深度：1 Bit 
分辨率：200‐400 DPI(每英寸点数) 
压缩：CCITT group 4 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 89 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  89 / 129 
 
7.6 HCV 
 
7.6.1 HCV 数据库结构 
HCV(Harness  Container  Viewing)是用来传输整套线束包括不同模块的所必要的数据信息。它
也可以用来传输单个线束模块的数据信息。 
它主要是用于 Master 认可释放，但也可以用于开发或计算以及样件制作的其他许多应用领
域。因为 HCV 不仅包含线束的图表描述，还包含所有技术信息，所以它能够解答产品本身
以及产品制造过程中的大多数问题。 
EE‐Browser（Viewer）软件可以读取、对比和分析 HCV Container 中的信息和内容。 
HCV 是一个压缩的数据库容器，它由下列内容组成： 
 
� 
包含整个线束模块的一份 KBL 数据 
� 
对线束图纸进行图表描述的一个或多个 SVG 数据，数据名称应按照内容进行命名，以
方 便 在 文 件 库 中 很 快 找 到 相 应 文 件 ， 数 据 命 名 以 .svg
结 尾 .( 示 例 ：
Topologie.svg,Deckblatt.svg,Stückliste.svg…) 
� 
描述模块零件清单的一份 Index‐XML 数据 
� 
其他选配数据如线束的 3D 轮廓模型 
� 
EE‐Browser 软件可以调整的可选择的“Redlining 红色显示”信息 
 
必须按照 ZIP 压缩标准进行压缩，不支持 RAR/TAR 或 GZIP。 
HCV  Container 中也可以含有其他数据（如 HP‐GL/2,TIFF,PDF），这要视过程要求而定。HCV 
Viewer  必须考虑到基本配置中包含数据（KBL,SVG(s),Index XML）。浏览系统也可以考虑其他
含有的数据，并对其进行调用。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 90 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  90 / 129 
 
 
图 29：HCV Container 结构 
 
7.6.2 KBL 
HCV Container 中必须包含一份 KBL 数据，KBL 数据结构必须按照 KBL Schema2.3 建立。 
 
7.6.3 Index.xml 
Index XML 是按模块分类的线束零件清单。在不同流程认可或调用时会用到这份数据。 
为减少每次从复杂的 KBL 数据结构中读取这份简单的基础信息所带来的麻烦，在编辑 HCV 
Container 时由各自的编辑系统的数据模型中生成数据，或由其他系统生成数据，并放进
Container 中。 
 
Index.xml 和 KBL 数据在版本和内容上必须一致。 
 
 
 
 
 
 
 
 


### 第 91 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  91 / 129 
 
 
EE‐Browser 可以读取和显示已经存在的数据内容，该功能可以在菜单 BOM(Active Modules/All 
Modules)中找到。 
 
所有属性或属性点必须填写完整（图表数据 Schemadatei 中的最小和最大 Occurrence 设置为
1）。 
Index XML 的 XML 图表分为 3 部分： 
 
1. Harness Container 中的元数据： 
 
ID<字符串>: 
Container 或数据的标识符，目前不再使用，现在只是权作保留。 
“id_08_15” 
 
creationTimestamp<字符串> 
编制 Index XML 时的时戳。由于兼容性的原因目前采用年月日（YYYY‐MM‐DD HH:MM:SS）
的格式。 


### 第 92 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  92 / 129 
 
“2014‐06‐01 20:07:34” 
 
creatingSystem<字符串>: 
生成 Index XML 的软件系统名称。 
“HarnessDesigner 1.5” 
 
supplier<字符串>: 
负责生成 Index XML 的 Connect 零件库中的供应商名称 
“Automotive Systems” 
 
harnessDrawing”字符串”: 
Master  线束的编号，编号中不能出现空格符或其他特殊字符。 
“A2225403908” 
 
2. 线束‐模块（Harness‐Module） 
该小节介绍 Master 线束的所有模块。 
 
partNumber<字符串>: 
模块编号，编号中不能出现空格符或其他特殊字符。 
“A2225404108” 
 
version<字符串>: 
模块的数据状态。这里可以输入版本信息，戴姆勒的日期格式为：YYYY‐MM‐DD. 
 
 


### 第 93 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  93 / 129 
 
“2011‐11‐07” 
 
description<字符串>: 
模块名称 
“ZB EL.LTG.SATZ LL COCKPIT RDU”驾驶舱线束 
 
code<字符串>: 
物流监控信息 
“+LL+TY0+889” 
 
kzflag<boolean>(“无图纸零件标记”): 
当模块没有自己的图纸，Flag 就设置为”true”。Master 范围的 Flag 会自动设置为”true”,
因为所有模块都是无图纸零件。 
无图纸零件的 Flag 规定如下： 
当 Harness Container 中的 harnessDrawing 为空时，kzflag 设置为”false”，否则将该输入栏
与 harnessModule 中的 partNumber 输入进行对比（不分大小写），如果不一样 Flag 设置
为”true”,否则为”false”. 
 
3. 模块零件： 
 
partNumber<字符串>: 
 
 


### 第 94 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  94 / 129 
 
零件号，编号中不能出现空格符或其他特殊字符。 
“A2129820126” 
 
drawing”字符串”: 
        描述零件的图纸号 
“A2129820126” 
 
supplier<字符串>: 
零件供应商名或简称 
“Coroplast” 
 
occurrences<整数>: 
零件数量，如果是包扎物则显示包扎段的数量 
“5” 
 
description<字符串>: 
零件名，（图纸上/标题栏上显示 SRM 名称，见标准 MBN 31 020‐1） 
“直端子 Buchsenkontakt(gerade)Au 0.14‐0.14)“ 
 
kzflag<boolean>(“无图纸零件标记”): 
单个零件一般没有自己的图纸，此处的 Flag 一直设置为”true”(→见表格图纸)。 
 
来自一份 Index XML 的摘录： 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 95 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  95 / 129 
 
 
 
7.6.4 SVG 
HCV  Container 中至少要包含一份 SVG 数据。能够制作多页格式的编辑系统每页应能导出一
份 SVG,并将其放入 Container 中。SVG 的数据命名应与每页名称一致（如“第一页”，“零件
表 Stückliste”），命名以“.svg“结尾。SVG 数据中包含线束图纸的图表说明。 
 
本标准描述了 5.99 版的 EE‐Browser 所支持的结构。描述问题参考可以采用版本为 9 的
Microsoft Internet Explorer. 
为使 SVG 模块化领域更加灵活和数据更加有效，以后的版本会做一些变更。在以后的开发
过程中要将整车的相关线束加入可视化，以便对线束之间的过渡和接口进行描述。这就大大
提高了对数据量和数据大小方面以及清晰方面的要求。因此在生成 HCV Container 尤其是 SVG
数据时要注意结构简单和表达有效。 
 
SVG 参考 W3C 标准（http://www.w3.org/TR/SVG11），但现行版本中只支持某些要素，并且
各自要素内部只允许支持特定的属性。下面章节将会对此进行介绍。 
 
 
 
 
 
 
 
 
 
 


### 第 96 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  96 / 129 
 
以后的版本中特定要素将不再支持，“ClipPath“就属于其中之一。在编制 SVG 数据时可以忽
视“ClipPathes“。 
 
不支持的 SVG 命令和属性即使出现 SVG 中，Browser 中也会忽略掉。 
 
Svg 中一开始会给出 Viewbox 的宽度、高度和大小。为使能在如 Internet Explorer 或其他程序
中显示 Svg,这些值在生成时要相应地描述出来。EE  Browser（EE 浏览器）不会使用该信息，
它会自己确定未给出的坐标，所有信息显示只支持世界坐标系（WCS），不支持用户坐标系
和不同单位。 
 
文档背景色固定为白色，可以不作规定。 
 
*******  支持的 SVG 要素 
SVG 文件中的图型要素和分组可以按任意顺序进行归类，结构化的列举清单可以方便识读。 
 
*******.1  分组 
<g transform(translate,rotate,scale)/> 
 
分组要素是对相关基础要素进行分组。分组深度任意。浏览器内的 Business Objetken 的所有
总结都需要该要素，如接插件表、接插件视图等。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 97 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  97 / 129 
 
举例： 
 
 
*******.2  线条 
 
x1,y1:WCS（世界坐标系）中的起点坐标 
x2,y2: WCS（世界坐标系）中的尾坐标 
style:支持颜色及线宽的 stroke（笔画）属性，颜色输入时既可以采用颜色名称（见 W3C 标
准），也可以采用 RGB 值。 
 
举例： 
 
*******.3  多线 
 
points:WCS 中的 XY 清单 
style:  支持颜色及线宽的 stroke 属性，颜色输入时既可以采用颜色名称（见 W3C 标准），也
可以采用 RGB 值。属性 fill 是指填充颜色或没有。不透明值为 0 时填充颜色设置为“ungefüllt
不填充“。 
 
举例： 
 
或者 


### 第 98 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  98 / 129 
 
*******.4  多边形 
 
 
points: XY 清单 
style:  支持颜色及线宽的 stroke 属性，颜色输入时既可以采用颜色名称（见 W3C 标准），也
可以采用 RGB 值。属性 fill 是指填充颜色或没有。不透明值为 0 时填充颜色设置为“ungefüllt
不填充“。 
 
举例： 
 
 
*******.5  路径 
 
大写字母表示绝对坐标，小写字母表示相对坐标。 
 
style:  支持颜色及线宽的 stroke 属性，颜色输入时既可以采用颜色名称（见 W3C 标准），也
可以采用 RGB 值。属性 fill 是指填充颜色或没有。不透明值为 0 时填充颜色设置为“ungefüllt
不填充“。 
举例： 
 
*******.6  圆形 
 
弧度 
 
曲线 
 
水平线 
 
线 
 
移动到 
 
垂直线 


### 第 99 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  99 / 129 
 
cx,cy:WCS 中的原点坐标 
r:半径 
style:  支持颜色及线宽的 stroke 属性，颜色输入时既可以采用颜色名称（见 W3C 标准），也
可以采用 RGB 值。属性 fill 是指填充颜色或没有。不透明值为 0 时填充颜色设置为“ungefüllt
不填充“。 
 
举例： 
 
 
*******.7  椭圆形 
 
 
cx,cy:WCS 中的原点坐标 
rx,ry:X 和 Y 轴半径 
style:  支持颜色及线宽的 stroke 属性，颜色输入时既可以采用颜色名称（见 W3C 标准），也
可以采用 RGB 值。属性 fill 是指填充颜色或没有。不透明值为 0 时填充颜色设置为“ungefüllt
不填充“。 
 
举例： 
 
 
*******.8  矩形 
 
 
x,y:WCS 中的原点坐标 
width:宽 
height:高 
style:  支持颜色及线宽的 stroke 属性，颜色输入时既可以采用颜色名称（见 W3C 标准），也
可以采用 RGB 值。属性 fill 是指填充颜色或没有。不透明值为 0 时填充颜色设置为“ungefüllt
不填充“。 
当前版本不支持倒角的 Rx 和 Ry 值。 
 
 
 
 


### 第 100 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  100 / 129 
 
举例： 
 
*******.9  文本 
 
x,y:WCS 的原点坐标，这里不支持 list of points. 
font‐weight:只能选择“粗体 bold”。 
 
style:通过属性 text‐anchor 可以进行水平方向定位，在 font‐family 中选择字体，不能预览字
体。在目标系中必须能够使用，否则就要返回默认。属性 fill(填充)可以修改文本颜色。颜色
输入时既可以采用颜色名称（见 W3C 标准），也可以采用 RGB 值。属性 text‐decorationx(修
饰)可以添加下划线、删除线和双删除线。属性 font‐size 可以修改字体。 
 
举例： 
 
 
*******.10 Tspan 
 
 
x,y:WCS 的原点坐标，这里不支持 list of points. 
font‐weight:只能选择“粗体 bold”。 
 
style:通过属性 text‐anchor 可以进行水平方向定位，属性 text‐decorationx(修饰)可以添加下划
线、删除线和双删除线。 
 
Tspan 只能和文本属性（text）一起使用。Tspan 可以兼容属性 text 的大小、颜色和字体。 
 
举例： 
 
 
 
 
 
 
 
 


### 第 101 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  101 / 129 
 
 
 
7.6.4.2  转换 
 
7.6.4.2.1  矩阵 
 
支持矩阵转换，可以采用下面矩阵属性（也可参见 W3C） 
 
SVG 中转换矩阵的矩阵属性排列： 
 
举例： 
 
 
7.6.4.2.2 Rotate(旋转) 
<rotate(angle)> 
旋转角度单位为度。 
举例： 
 
 
7.6.4.2.3 Scale 
<scale(x,y)> 
 


### 第 102 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  102 / 129 
 
举例： 
 
 
7.6.4.2.4 Translate 
<translate(x,y)> 
 
举例： 
 
 
注意：不支持 Skew Kommandos 
 
7.6.4.3  注意事项 
 
一般情况下 SVG 中可以传输 2 种文本。一种是上面提到的属性文本（text），还有一种是将
text 作为填充后的多边形进行转换。很多 SVG 转换软件使用第二种方法，因为采用这种方法
目标计算机就无需所使用的字体信息。常见的 CAD 图纸也采用这种方法，因为其文本较少。 
 
HCV  Container 中只使用第一种方法，这是因为不使用这种方法的话 SVG 数据就会太大—尤
其是内舱线束。 
 
此外还需注意：不要生成太多不必要的坐标组。这在显示（表述）时经常出现，如通过特定
的转换器 3D 转换成 2D 的接插件图片。这里经常会生成许多多线，这对图型描述来说完全
是多余的，后面也很难保存。 
 
当前版本不支持 Pattern‐Definitionen 和 Dash‐Arrays,这是因为浏览器内的 SVG 信息转换会导
致表述不一致。 
 
不支持文本路径 Textpathes. 
 
 
 
 
 
 
 
 


### 第 103 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  103 / 129 
 
7.6.4.4 SVG 与 KBL 相互链接 
为实现图型和物理要素之间的导航，会从 SVG 出发在分组要素内通过 KBL  识别（ID）过度
到 KBL 要素。 
 
KBL 中分配给不同对象的 ID 结构见下面示例。 
 
 
戴姆勒会使用这种方法，这是因为如果要用 SVG 图型描述的话意味着要在图型管理和传输
上增加相应的支出。此外这种方式可以在现有的编辑系统中进行。 
 
如果一个分组中的首个要素出现这样的注释<!‐kbl‐id:xyz......‐‐>,表示可以调用；否则表示该组
要素纯粹用于结构化。 
 
该注释包含一个或多个属于改组的 KBL 要素的 ID 号，多个 ID 通过空格符分开。 
相互链接既可以表述为“ist 为“（如接插件标记）,也可以表述为“gehört zu  属于“（如接插件
表）。第二种情况是注释处可以添加“Typ‐Spezifizierer 类型“（见下一章节），采用这种方法，
浏览器可以实现对物理对象的不同表述进行区分。 
 
 
 
 
 
 
ID_前缀 
对象类型 
 
包扎连接点 
线束分支段 
塑壳 
附件 
盲塞 
 
防水栓 
端子 
胶带（包扎和管类） 
导线 
 
卡扣（固定） 
文本如安装说明 
塑壳上的附件表 
图片，可以是安装说明 


### 第 104 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  104 / 129 
 
接插件标记（前视图）举例： 
 
7.6.4.5 Typ‐Spezifizierer 类型 
要求如下： 
 
7.6.4.5.1 type:ref 
type:ref  表示基本参考对象，没有更多详细说明。当对象要结合专门的附加图型如垂直箭头
或文本标签使用时会用到这一属性。Typ  ref 的要素不在选择之列，某些特定视图下如
Start‐End Connector 视图也不会显示。 
 
 
7.6.4.5.2 type:table 
type:table 表述的是总表格（如接插件表）。表格是以行和单元格作为结构显示。 
 
7.6.4.5.3 type:row 
type:row 表示表格内的行（非标题）。 


### 第 105 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  105 / 129 
 
 
7.6.4.5.4 type:cell 
type:cell 表示表格中每行的单元格。 
 
7.6.4.5.5 type:dimension 
type:dimension 表示尺寸标注对象。尺寸标注对象一般是指相应线束分支段的两个连接点
（Vertex‐Identifier），尺寸标注适用于此处（BNJ_BundleJunction）。尺寸标注文本和尺寸箭头
下面会分开一一举例。这可以不需要，一些数据举例会出现。第 3 段将会做以下总结。 
 
 
举例： 
 


### 第 106 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  106 / 129 
 
尺寸标注文本： 
 
 
尺寸箭头： 
 
 
总结： 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 107 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  107 / 129 
 
 
 
7.6.4.5.6 type:DocumentFrame 
type:DocumentFrame 表示页框分组。所有页框所需的图型对象都要列在下面（如方格说明）。 
 
7.6.4.6  对象及其表示法 
 
7.6.4.6.1  接插件符号标记 
接插件符号标记是指 KBL 中的塑壳。它直接置于匹配的连接点分组内，这样在选择连接点时
符号标记会自动选择出来。这里的连接点没有图型—它通过接插件符号标记显示—只有在显
示相应的 KBL 节点（挂点）时才需输入（参见下面的 Vertex 连接点章节）。 
 
 
 
 


### 第 108 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  108 / 129 
 
 
Svg  部分摘录： 
 
 
7.6.4.6.2  接插件表 
接插件表是指 KBL 中匹配的塑壳。它通过 Typ‐Angabe(类型—说明)规定出来。首先将介绍表
头的图型要素，然后介绍表格栏标题的说明。 
 
 
 
连接点符号标记 


### 第 109 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  109 / 129 
 
然后再介绍每行内容以及 KBL 中的匹配导线，这里会采用 Typ  row 方法。每行会继续划分，
以便对行内容如端子零件号进行说明。子划分是指 KBL 的端子对象，它采用 Typ cell 方法。
接下来是举例：子划分—端子零件号和端子材质，它们可以显示和隐藏。 
单元格旁边的标记必须为矩形填充，单元格背景和边框需分开表示。理想情况下应将单元格
填充设置为“ungefüllt 不填充“,不用再设置单元格背景，因为如果采用渲染方法的话某些视图
下看背景色会显示为灰色。 
 
 
 
Svg  部分摘录： 
 
 
 
 
 
 
 
 
 
 
 


### 第 110 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  110 / 129 
 
 
 
 
 
 
 
背景和单元格分开 
单元格背景色：透明 


### 第 111 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  111 / 129 
 
 
7.6.4.6.3  附件 
接插件的附件会在接插件表下面显示。它划分在接插件表一类。这些零件一般是指 KBL 中的
附件对象 Accessory。有时也会是零部件对象（Component）（大部分为此处举得例子：保险
丝）。 
附件一般也是通过表格显示出来，每个单元格通过 Typ row 进行标记。 
 
 
 
 
 


### 第 112 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  112 / 129 
 
Svg  部分摘录： 
 
 
当涉及到零部件（Component）时 KBL 描述为： 
 
 
 
 
 
 
 
 


### 第 113 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  113 / 129 
 
 
当涉及到附件（Accessory）时 KBL 描述为： 
 
7.6.4.6.4  表格垂直箭头 
这里的垂直箭头一般显示为表格的一部分（type:table），指的是 KBL 中的塑壳对象
（Connector）。垂直箭头一般和接插件表定义一起使用。 
 


### 第 114 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  114 / 129 
 
 
Svg  部分摘录： 
 
7.6.4.6.5  说明文本 
接插件和其他对象的说明是指 KBL 中的匹配对象，通过 type:ref  进行标记。 
 
Svg  部分摘录： 


### 第 115 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  115 / 129 
 
 
7.6.4.6.6  固定件 
固定件和卡扣是指 KBL 中匹配的固定件对象（Fixing）。 
 
SVG 中的表格描述： 
 
 
 


### 第 116 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  116 / 129 
 
SVG 中固定件的图型描述： 
 
KBL 中的固定属性要素： 
 
 
 
 


### 第 117 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  117 / 129 
 
 
7.6.4.6.7  挂点 
挂点是参考 KBL 中匹配的塑壳对象（Connector），并和其他接插件一样进行处理。 
 
 
 
 
 
 
 
 
 
 
 
 
 
Svg  符号标记部分摘录 


### 第 118 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  118 / 129 
 
 
 
 
 
Svg  表格部分摘录： 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 119 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  119 / 129 
 
 
附件 Svg  部分摘录： 
 
 
 
 


### 第 120 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  120 / 129 
 
 
相关 KBL 属性要素： 
 
 
 


### 第 121 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  121 / 129 
 
 
7.6.4.6.8 Vertex 
Vertex 是指 KBL 匹配的节点对象。 
 
相关 KBL 要素： 
 
 
7.6.4.6.9  分支段/包扎段/管类 
分支段原本是指线束上的保护方式，它参考的是 KBL 中的分支段对象（Segment）。分支段
该组表示 KBL 中匹配的线束保护对象（Wire‐Protection）。下面是部分包扎段举例。 
 
 
 
 
 
 
 
 
 


### 第 122 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  122 / 129 
 
 
分支段 SVG 部分摘录： 
 
相关 KBL 摘录： 
 


### 第 123 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  123 / 129 
 
 
7.6.4.6.10  支架 
支架及类似零件按照固定件进行处理，它参考的是 KBL 中的固定件对象（Fixing）。 
 
 
表格 SVG 摘录： 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 124 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  124 / 129 
 
 
支架标记符号的 SVG 摘录： 
 
7.6.4.6.11  时间刻度 
时间刻度符号（出线方向）一般参阅的是不同的对象。拿卡扣举例，卡扣可以采用时间刻度
法，或者通过连接点甚至一段线束段（Segment）相连接。它自己本身在 KBL 中没有实际的
物理对象。 
 
 


### 第 125 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  125 / 129 
 
SVG  部分摘录： 
 
7.6.4.6.12  尺寸标注 
尺寸标注是指两个相关的连接点（起点和末点），通过 type:dimension 进行标记。 
 
尺寸标注文本和尺寸标注箭头举例分开，因为它们采用的是不同的转换方式。尺寸标注当然
也可以单独分组。 
 
尺寸标注的 SVG 摘录： 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 126 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  126 / 129 
 
 
7.6.4.7 特征及内在逻辑 
为使线束设计表示法有意义，浏览器在显示分支段时必须采用逻辑关系。实际通过模块匹配
甚至与模块相一致的线束保护而在配置中隐藏的分支段，当这一分支段有导线并与匹配模块
一致时必须要显示出来。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 127 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  127 / 129 
 
8  参考资料 
8.1  标准提示 
本标准中图纸标题栏、变更栏以及标准提示只是举例，图纸的更新及实施由图纸编制人员负
责。 
8.2  标准和规范 
下表中列举的文件均能在 DocMaster 中获得。 
资料/文件号 
标题 
A0000026199 
AV  导线 
A0000069899 
原理图编制规范 
A0090000299 
超声波焊接标准 
A0198002 
奔驰图纸中英德双语标题栏简称的命名规范 
A0598004 
Smaragd 存档指南 
A0598031 
MBC 和 MB Vans A 和 H 开头的零件号的命名、缩写、首字
母缩写规范 
A1240045699 
焊接挂点执行规范 
A2110000299 
压接挂点执行规范 
CS040 
CATIA V5 线束零部件编制指南 
CS080 
Siemens NX 线束零部件编制指南 
DBL 8585 
禁用物质清单 
DIN 46234   
铜导线用无绝缘套的环形无焊点连接的搭铁端子 
DIN IEC 60757 
颜色标记代码 
DIN ISO 16016 
技术产品文件—限制文件和产品使用的保护通告 
DIN ISO 8601 
数据存储和交换形式∙信息交换—日期和时间的表示方法 
DIN 46225 
绝缘导线用带绝缘套的冲压爪形搭铁端子 
DIN 46234 
铜导线用无绝缘套的环形无焊点连接的搭铁端子 
ISO 10303‐212 
工业自动化系统和集成—产品数据表示和交换—第 212 部
分:应用协议:电工设计和安装 
MBN 10317 
特殊验证属性标记 
基础—零部件存档要求 
MBN 31001 
CAD 图纸：产品表示基础 
基础—零部件设计框架、表示法和标记 
MBN 31002 
CAD 图纸：标题文字类型及文字大小 
MBN 31020‐1 
设计图纸标题栏 
MBN 31020‐2 
CAD 图纸的图纸范围 
MBN 31020‐3 
设计图纸变更 
MBN 10435 
含有戴姆勒产品标识、零件号和识别号的零件标识 
V0198029 
图纸组织的变更 
V0198027 
产品文档系统内的控制标记 


### 第 128 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  128 / 129 
 
8.3  缩写和术语 
缩写 
名称/备注 
AV 
执行规范 
B8‐Teil 
供应件（zugesteuerte Teile） 
CADDS 
奔驰 MBC 线束开发范围用的基础 CAD 软件 
CGM 
计算机图形元文件 
Catia 
奔驰 MBC 用来 DMU 编制的 CAD 软件 
ConnectPARTS 
线束开发用的零部件数据库 
ConnectHARNESS 
线束和原理图查询数据库 
DS 
安全相关零件的存档 
Dialog 
戴姆勒集团的零件清单系统 
DXF 
AutoCAD 延伸文件 
ENX 
欧洲网络交换 
RD/EKL 
PKW/电气开发中的线束开发 
ELA 
导线防水栓 
ELOG 
按照 ISO 10303‐AP212 标准要求制作的原理图数据模型 
EngPortal 
奔驰的工程系统，可以查找数据和工具 
IP Code 
戴姆勒集团、Sparte PKW 的官方销售代码 
HCV 
用于浏览的线束容器 
LSC 
线束变更管理 
KBL 
按照 ISO 10303‐AP212 标准要求制作的线束数据模型（线束清单）
KEM 
设计实施通知单 
KLH 
线束设计任务书 
KSL 
客户定制线束 
MBC 
梅赛德斯奔驰汽车/PKW 开发 
REF 
符合 VA 059 EI08 要求的参考名（也可参见 VZK） 
Smaragd 
奔驰 MBC 用的 PDM 系统 
SRM 
Sachstamm Recherche Modul 
Stecker Leitfaden 
CAD 手册的一部分，描述的是接插件的表示法和应用 
SVG 
可伸缩向量图形 
TIFF 
带标影像档案格式 
U‐Code 
用于原理图中导线控制的内部代码 
VEC 
按照 ISO 10303‐AP212 标准要求制作的数据模型（汽车电气容器）
VZK 
使用目的目录 
XML 
可扩展标示语言 
ZGS 
图纸几何状态 
 
 


### 第 129 页
 
 
 
梅赛德斯‐奔驰 
®Daimler AG 请遵守 DIN ISO 
16016  专利保护/没有设计部
门许可不允许变更 
执行规范 
线束图纸要求 
A 002 006 32 99 
编制：Juric,Miroslav 
部门：RD/EKL 
日期：2014‐03‐21 
ZGS:0004 
订单号：YAP32942/14 
页数：  129 / 129 
 
8.4  附录 
Master 线束图纸中的模块表： 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 

