# A 002 006 32 99_DE_2016-11_ZGS008_梅赛德斯奔驰汽车线束图纸要求.pdf

## 文档信息
- 标题：Lastenheft zur Erstellung
- 作者：wadresc
- 页数：178

## 文档内容
### 第 1 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: <PERSON><PERSON><PERSON>, <PERSON> 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 1 von 178 
 
Ausführungsvorschrift 
 
 
 
 
Leitungssatzdokumentation  
für Mercedes-Benz PKW  
 
 
 
 
 
 
 
 
 
 
Version 8.0 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 2 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 2 von 178 
 
 
 Änderungsindex / Änderungsbeschreibung 
 
Änderungsdokumentation des Lebenslaufes Ausführungsvorschrift Leitungssatzzeichnung 
ZGS 
Nachtragsbeschreibung 
Bearbeitet (Datum) 
Daimler AG 
001 
Dokument neu erstellt aus den beiden gültigen AVs  
„Ausfg.Vorsch/Leitungssatzerstellung“ A0000069799 und  
„Ausfg.Vorsch/Leitungssatz Legende“  A0000026299.  
Juric 2011-12-20 
002 
Integration Änderungen aus Workshop 01/12 und 02/12 
Abbildungsverzeichnis eingefügt, Kapitel 8.2 „Normen und Vorschriften“ 
aktualisiert 
Rath 2012-04-25 und 
        2012-11-07 
003 
Kapitel 2.3.1 Änderungsindizes auf der Zeichnung und Kapitel 4.3 
Leitungsschlaufen bzw. geändertes Routing nach Abstimmung in 
Lieferantenreko aktualisiert. 
Rath 2013-09-25 
004 
Überarbeitung Kapitel 7; Integration Beschreibung des KBL- und HCV-
Datenformat (Kapitel 0 und Kapitel 8.6) 
Rath 2014-03-21 
005 
Kapitel 3 (Direktverkabelung von Masseleitungen auf Kabelschuh 
Massekonzept „ohne Splice“) hinzugefügt 
Juric 2014-06-13 
006 
komplette Überarbeitung, KBL 2.4  
MTC-Cabling-TP 
2015-08-
28 
007 
Kleine Korrekturen KBL 2.4, Überarbeitung Massekonzept „ohne Splice“ 
Juric 2016-07-31 
008 
Ergänzungen bei Abschnitten Referenzbezeichnungen, Dummy-POS-
Nummern, Lage Befestigungselement und Steckrichtung. Überarbeitung 
von Dokumentation von Leitungssatz-Stützpunkten, Darstellung von 
Textbändern, Darstellung von KSL-Steckern, Dokumentation von 
Schläuchen 
Neckenich 2016-11-25 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 3 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 3 von 178 
 
Inhaltsverzeichnis 
1 
Rahmenbedingungen – Gegenstand dieser Ausführungsvorschrift 
9 
1.1 
Ziele .................................................................................................................................................................... 10 
1.2 
Zuständigkeit ..................................................................................................................................................... 10 
1.3 
Verwendete Begriffe ......................................................................................................................................... 11 
2 
Dokumentation der Leitungssätze in 2D Zeichnungen 
14 
2.1 
Aufbau einer 2D Leitungssatzzeichnung ......................................................................................................... 16 
2.2 
Besonderheiten bei der Darstellung von Leitungssätzen in Mehrblattzeichnung ......................................... 17 
2.3 
Besonderheiten bei der Darstellung von Leitungssätzen in einer 2D Zeichnung ......................................... 18 
2.3.1 
MB Schriftfeld/ CAD-Datum/ Änderungshistorie Zeichnung (Punkt 1, 2, 3) ....................................... 20 
2.3.2 
Verweis auf die gültigen Ausführungsvorschriften (Punkt 4) ................................................................. 23 
2.3.3 
Tabelle Schaltpläne, Tabelle SP mit/ ohne Steuercode (Punkt 5) ......................................................... 23 
2.3.4 
Tabelle DMU (Punkt 6) .............................................................................................................................. 24 
2.3.5 
Bemerkungen/Hinweise Lieferantenspezifisch (Punkt 7) ...................................................................... 24 
2.3.6 
Kennzeichnung von Merkmalen zur besonderen Nachweisführung (Punkt 8) ...................................... 25 
2.3.7 
Tabelle der Module in der Masterleitungssatzzeichnung (Punkt 9) ....................................................... 25 
2.3.8 
Leitungssatztabellen pro Modul (Punkt 10) ............................................................................................ 26 
2.3.9 
Teilestückliste (Punkt 11) ......................................................................................................................... 27 
2.3.10 Dokumentation Leitungssatzfremde Umfänge in LS-Zeichnung ............................................................ 28 
2.3.11 Verweis auf Abweichtabellen auf der Leitungssatzzeichung .................................................................. 28 
3 
Direktverkabelung von Masseleitungen auf Kabelschuh Massekonzept „ohne Splice“ 
29 
3.1 
Anforderungen ................................................................................................................................................... 29 
3.2 
Verarbeitung ...................................................................................................................................................... 30 
3.3 
Grafische Vorgaben ........................................................................................................................................... 31 
4 
Datenformate und Einheiten der Leitungssatzzeichnung 
34 
4.1 
Leitungslängen .................................................................................................................................................. 34 
4.2 
Leitungssatz-Gewicht ........................................................................................................................................ 34 
4.3 
Angabe des Datums .......................................................................................................................................... 34 
4.4 
Positionsnummern ............................................................................................................................................ 34 
4.4.1 
Format der Positionsnummer ................................................................................................................... 35 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 4 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 4 von 178 
 
4.4.2 
Sonstige Positionsnummern ..................................................................................................................... 35 
4.5 
Teilkennzeichnung ............................................................................................................................................. 35 
4.6 
Allgemeine Anforderung Umweltschutz........................................................................................................... 36 
5 
Darstellung von Leitungssatzkomponenten in der 2D Zeichnung 
37 
5.1 
Darstellung der Kontaktierungsteile ................................................................................................................ 37 
5.1.1 
Kontaktierungsteil-Symbol ....................................................................................................................... 37 
5.1.2 
Kontaktierungsteil-Tabelle ........................................................................................................................ 38 
5.1.3 
Symbolbereitstellung ................................................................................................................................ 38 
5.1.4 
Referenzbezeichnungen (REF) .................................................................................................................. 38 
5.1.5 
Alternative Verwendung ........................................................................................................................... 39 
5.1.6 
Aufbau der Referenz ................................................................................................................................. 39 
5.1.7 
Steckername ............................................................................................................................................. 39 
5.1.8 
Kontaktform ............................................................................................................................................... 39 
5.1.9 
Kabelschuh ................................................................................................................................................ 39 
5.1.10 HSD-Stecker ...................................................................................... Fehler! Textmarke nicht definiert. 
5.1.11 Sicherungen, Relais ................................................................................................................................... 40 
5.2 
Dokumentation von Leitungssatz-Stützpunkten ............................................................................................. 41 
5.2.1 
Gültige Varianten....................................................................................................................................... 41 
5.2.2 
Darstellung ................................................................................................................................................ 41 
5.3 
Schlüsselwörter ................................................................................................................................................. 41 
5.4 
Leitungsschlaufen bzw. geändertes Routing ................................................................................................... 41 
5.5 
Lagedarstellung ................................................................................................................................................. 42 
5.6 
Ausbindungsrichtung ........................................................................................................................................ 44 
5.7 
Befestigungselemente ...................................................................................................................................... 44 
5.7.1 
Lage Befestigungselement zum Leitungssatz ......................................................................................... 44 
5.7.2 
Lage Befestigungselement und Steckrichtung zum Leitungssatz .......................................................... 44 
5.7.3 
Lage Kabelbinderschloss zum Leitungssatz ............................................................................................ 45 
5.7.4 
Benennung Befestigungspunkt in der Zeichnung.................................................................................... 46 
5.8 
Darstellung Segmente ...................................................................................................................................... 47 
5.8.1 
Darstellung Segmente mit Isolierung ....................................................................................................... 47 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 5 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 5 von 178 
 
5.8.2 
Darstellung nicht angeschlagene Schirme .............................................................................................. 47 
5.9 
Etiketten und Markierungen ............................................................................................................................. 48 
5.10 Bemaßungen ...................................................................................................................................................... 48 
5.10.1 Vermaßung Steckereingang ..................................................................................................................... 48 
5.10.2 Benennung Referenzpunktbemaßung ...................................................................................................... 48 
5.10.3 Bemaßung von Kabelschuhen .................................................................................................................. 48 
5.10.4 Bemaßung Befestigungselement.............................................................................................................. 50 
5.10.5 Referenzpunktbemaßung für Befestigungselemente: ............................................................................. 50 
5.11 Toleranzen ......................................................................................................................................................... 51 
5.11.1 Toleranzen für Segmente .......................................................................................................................... 51 
5.11.2 Toleranzen für Referenzpunktbemaßung von Segmenten ...................................................................... 52 
5.11.3 Toleranzen für Befestigungselemente ..................................................................................................... 52 
5.11.4 Toleranzen für Tüllen ................................................................................................................................. 52 
6 
Überprüfung der Leitungssatzmaße 
53 
7 
Datenbereitstellungsprozess 
54 
7.1 
Leitungssatzdaten ............................................................................................................................................. 54 
7.2 
Schaltplandaten ................................................................................................................................................ 56 
8 
Datenformate in der Leitungssatzentwicklung 
57 
8.1 
Einzelleitungs-, Modul- und Masterleitungssatz .............................................................................................. 57 
8.1.1 
Einzelleitungssatz ...................................................................................................................................... 57 
8.1.2 
Modulleitungssatz ..................................................................................................................................... 57 
8.1.3 
Masterleitungssatz .................................................................................................................................... 58 
8.2 
Namenskonvention für KBL- und HCV-Dateien ............................................................................................... 59 
8.3 
KBL (Kabelbaumliste) ........................................................................................................................................ 60 
8.3.1 
Allgemeine Anforderungen ....................................................................................................................... 60 
8.3.2 
Inhalte und Strukturen .............................................................................................................................. 60 
8.3.2.1 
Masterleitungssätze ......................................................................................................................... 60 
8.3.2.2 
Einzelleitungssätze ........................................................................................................................... 61 
8.3.3 
Festlegungen ............................................................................................................................................. 61 
8.3.3.1 
Maße und Maßeinheiten .................................................................................................................. 61 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 6 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 6 von 178 
 
******* 
Zuordnung der Teileklassen ............................................................................................................ 62 
8.3.3.3 
Namenskonventionen für technische Ids ("Klein-id") in den Occurrence-Klassen ...................... 63 
8.3.3.4 
Externe Referenzen .......................................................................................................................... 64 
8.3.3.5 
Datenstand ....................................................................................................................................... 64 
8.3.3.6 
MBC-Sachnummer ........................................................................................................................... 65 
8.3.3.7 
Benennung von Sachnummern ....................................................................................................... 65 
******* 
MBC-Positionsnummern .................................................................................................................. 65 
8.3.3.9 
Leitungslängen ................................................................................................................................. 66 
8.3.3.10 Wickelrückbindung ......................................................................................................................... 66 
8.3.3.11 Modellierung einseitig angeschlagener Leitungen ....................................................................... 68 
******** DS/ DZ Kennzeichung und ESD Kenner ....................................................................................... 68 
8.3.3.13 Darstellung der Kontaktierungsarten ............................................................................................ 70 
8.3.3.14 Darstellung von Textbändern ......................................................................................................... 75 
8.3.3.15 Darstellung von KSL-Steckern ....................................................................................................... 75 
8.3.3.16 Schläuche ........................................................................................................................................ 76 
8.3.4 
KBL Container ........................................................................................................................................... 78 
8.3.4.1 
KBL Version ...................................................................................................................................... 78 
8.3.4.2 
Zusatzteile/ Accessory .................................................................................................................... 79 
8.3.4.3 
Kartesische Koordinate/ Cartesian_point ..................................................................................... 81 
******* 
Blindstopfen/ Cavity Plug ............................................................................................................... 81 
******* 
Einzeladerdichtung/ Cavity Seal ..................................................................................................... 83 
******* 
Änderungen/ Change ...................................................................................................................... 85 
******* 
Änderungsbeschreibung/ Change Description ............................................................................. 86 
******* 
Komponenten/ Component ............................................................................................................ 87 
******* 
Komponentenbox/ Component Box ............................................................................................... 89 
******** Kontaktgehäuse/ Connector Housing .......................................................................................... 91 
******** Dimensionen/ Dimension Specification ....................................................................................... 93 
******** Standard Dimensionen/ Default Dimension Specification .......................................................... 93 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 7 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 7 von 178 
 
8.3.4.13 externe Referenzen/ External Reference ..................................................................................... 94 
******** Befestigungsteile/ Fixings ............................................................................................................. 95 
8.3.4.15 Kontakte/ General_Terminal ......................................................................................................... 97 
******** Leitungen/ General Wire ................................................................................................................ 99 
8.3.4.17 Gesamtleitungssatz (Harness) ..................................................................................................... 102 
8.3.4.18 Modulkonfiguration/ Module Configuration ............................................................................... 102 
8.3.4.19 Knoten/ Node ............................................................................................................................... 102 
8.3.4.20 Verbindungen/ Routing ................................................................................................................ 104 
******** Ausbindungen/ Segment ............................................................................................................. 105 
8.3.4.22 Maßeinheiten/ Units .................................................................................................................... 107 
8.3.4.23 Umwicklungen Kabelschutz/ Wire_protection ........................................................................... 107 
8.3.5 
Harness Container .................................................................................................................................. 109 
******* 
Attribute des Harness-Containers ................................................................................................ 109 
8.3.5.2 
Accessory_occurrence .................................................................................................................. 112 
8.3.5.3 
Cavity Plug occurrence .................................................................................................................. 113 
8.3.5.4 
Cavity Seal occurrence .................................................................................................................. 113 
******* 
Component_occurrence ................................................................................................................ 113 
******* 
Component box occurrence .......................................................................................................... 114 
8.3.5.7 
Verbindung/ Connection ............................................................................................................... 115 
******* 
Connector_occurrence .................................................................................................................. 116 
8.3.5.9 
Fixing_occurrence .......................................................................................................................... 117 
******** General_wire_occurrence ............................................................................................................ 118 
*******1 Terminal_occurrence .................................................................................................................... 120 
******** Wire_protection_occurrence ....................................................................................................... 120 
*******3 Verkabelungsgruppe (Wiring Group) ........................................................................................... 121 
*******4 Harness_configuration ................................................................................................................. 121 
8.3.6 
Module ..................................................................................................................................................... 124 
8.3.6.1 
Attribute der Module ...................................................................................................................... 124 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 8 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 8 von 178 
 
******* 
Modulfamilie/ Module Family ....................................................................................................... 126 
8.4 
Digitale Netzliste ............................................................................................................................................. 127 
8.5 
TIFF ................................................................................................................................................................... 127 
8.6 
HCV .................................................................................................................................................................. 127 
8.6.1 
Aufbau des HCV-Datencontainer ........................................................................................................... 127 
8.6.2 
KBL ........................................................................................................................................................... 128 
8.6.3 
Index.xml ................................................................................................................................................. 129 
8.6.4 
SVG .......................................................................................................................................................... 133 
******* 
Unterstützte SVG Elemente........................................................................................................... 134 
8.6.4.2 
Transformationen ........................................................................................................................... 139 
******* 
Wichtige Hinweise .......................................................................................................................... 141 
8.6.4.4 
Verlinkung zwischen SVG und KBL ............................................................................................... 142 
8.6.4.5 
Typ-Spezifizierer ............................................................................................................................. 142 
******* 
Objekte und ihre Darstellung ........................................................................................................ 146 
8.6.4.7 
Besonderheiten und implizite Logik bei der Darstellung ............................................................. 167 
9 
Mitgeltende Unterlagen 
168 
9.1 
Normative Hinweise ........................................................................................................................................ 168 
9.2 
Normen und Vorschriften ............................................................................................................................... 169 
9.3 
Abkürzungen und Begriffe .............................................................................................................................. 171 
9.4 
Abbildungsverzeichnis .................................................................................................................................... 173 
9.5 
Anhang ............................................................................................................................................................. 176 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 9 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 9 von 178 
 
1 Rahmenbedingungen – Gegenstand dieser 
Ausführungsvorschrift 
Diese Ausführungsvorschrift enthält die von der Daimler AG festgelegten Anforderungen an die 
Leitungssatzzeichnung. 
Sie 
stellt 
die 
verbindliche 
Vorgabe 
zur 
Erstellung 
von 
Dokumentationszeichnungen  des Leitungssatzes für alle Fahrzeugbaureihen des Bereiches MBC dar. 
Der Leitungssatz soll mit den Bordmitteln des entsprechenden Entwicklungssystems dokumentiert 
werden können. Die Zeichnungen werden mit Zeichnungsrahmen und Zeichnungsschriftfeldern der 
Daimler AG gefertigt, die KBL/HCV-Datei ist mit einen Copyright der Daimler AG analog 
Zeichnungsschriftfelder zu versehen. 
Beispiel:  
Copyright reserved Daimler AG, Schutzvermerk nach DIN ISO 16016 beachten!  
 
Der hier beschriebene Inhalt beschreibt die inhaltlichen und darstellerischen Anforderungen von MBC 
an eine Dokumentationszeichnung für das Produkt Leitungssatz.  
 
Diese Ausführungsvorschrift beschreibt keine  technischen Sachverhalte,  zu verwendende Materialien 
sowie Sachverhalte welche durch Lastenhefte, Normen und Verfahrensanweisungen der Daimler AG 
geregelt sind. Der Lieferant ist verpflichtet sämtliche Ausführungsvorschriften und Normen 
einzuhalten und die Dokumentation stets dem aktuellen Stand anzupassen. Sind für eine einwandfreie 
Dokumentation erforderliche Randbedingungen in dieser Ausführungsvorschrift nicht oder 
abweichend definiert, ist dies der Daimler AG anzuzeigen.  
 
Die Vorgaben bzgl. des Datenformats .kbl/ hcv beziehen sich nicht auf das System CADDS.  
 
Alle Abweichungen von den Anforderungen dieser Ausführungsvorschrift bedürfen der schriftlichen 
Zustimmung der Daimler AG.  
 
 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 10 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 10 von 178 
 
1.1 Ziele 
Die wichtigsten Ziele sind:  
 
Inhalte der Dokumentationszeichnung als Produktdokumentation festzulegen, 
sowie ein einheitliches Erscheinungsbild der Leitungssatz- Zeichnung sicherzustellen. 
 
Die Abbildung der Leitungssatz-Inhalte in einer KBL-/HCV-Datei zur Unterstützung der MBC 
internen Prozesse sowie Versorgung der Dokumentation mit archivierungsgerechten 
Zeichnungen. 
 
Die 
Abbildung 
der 
Leitungssatzzeichnung 
als 
Tabellensachverhalt 
(Master 
Leitungssatzzeichnung). 
 
Die Absicherung des Entstehungsprozesses der Leitungssatz- Produktdokumentation. 
 
Versorgung der internen Entwicklungsprozesse mit digitalen Leitungssatzdaten. 
 
1.2 Zuständigkeit 
Zuständig für die Ausführung der Leitungssatzdokumentation, der technischen Inhalte sowie der zu 
verbauenden 
Einzelteile 
wie 
 
Kontaktierungsteile, 
Leitungen, 
Sonderkabel 
und 
Leitungssatzumhüllungen im Bereich Fahrgestell und Aufbau der MBC ist die Abteilung 
Leitungssatzentwicklung.  
 
Die verwendeten Kontaktierungsumfänge sind zwischen den Bereichen Kontaktierung, Leitungssatz-
Entwicklung, 
den 
Systemfachabteilungen 
und 
dem 
zuständigen 
Baureihenteam 
in 
der 
Leitungssatzentwicklung abzustimmen! Nicht freigegebene Materialien und Teile bedürfen einer 
gesonderten Genehmigung des Baureihenprojektes, vertreten durch das zuständige Baureihenteam in 
der Leitungssatzentwicklung. 
 
 
 
 
 
 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 11 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 11 von 178 
 
1.3 Verwendete Begriffe 
Bauraum Master 
Das Gesamtfahrzeug wird in Bauräume unterteilt. Für jeden Bauraum oder für jede größere 
Geometrievarianz wird eine Bauraum-Master 2D Zeichnung erstellt. Der Bauraum Master definiert 
einen maximalen Gesamtumfang für einen bestimmten Fahrzeugbauraum / Variante. Der Umfang als 
solcher ist normalerweise nicht bestellbar.  
 
Abbildung 1: Bauraum Master 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 12 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 12 von 178 
 
Modul 
Ein Leitungssatz wird mit einer Sachnummer in einem Modul dokumentiert. Der Masterleitungssatz 
besteht aus einzelnen Modulzeichnungen. Ein Leitungssatzmodul spezifiziert dabei einen bestimmten 
Ausstattungsumfang. Die Abhängigkeiten der Module untereinander können unter Umständen 
komplex sein. Die Module müssen nicht per Default sinnvolle und vollständige Fahrzeugfeatures sein.  
 
 
Abbildung 2: Leitungssatzmodule 
Die effizienteste Art der 2D Dokumentation von Leitungssätzen ist die Dokumentation in einer 2D 
Master-Leitungssatzzeichnung. Alle Module eines Bauraumes werden auf einer Zeichnung zusammen 
erstellt und gepflegt. Somit können Änderungen, die mehrere Module betreffen, einmalig auf der 
Masterleitungssatzzeichnung dokumentiert werden. Diese Zeichnung kann je nach Absprache im 
Projekt mit dem verantwortlichen Sachbearbeiter freigegeben werden. Pro Masterleitungssatz 
entstehen dann 2 Files:  
- 
HCV File 
(siehe Kapitel HCV )  
- 
TIFF File 
(siehe Kapitel TIFF )  
 
Wird aus einem Masterleitungssatz ein Leitungssatzmodul abgeleitet, so entstehen als Ergebnis dieser 
Ableitung zwei Dateien:  
- 
KBL oder HCV File (siehe Kapitel KBL / Kapitel HCV )  
- 
TIFF File 
 
(siehe Kapitel TIFF)  
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 13 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 13 von 178 
 
Segment 
Ein Segment kann am Anfangs- und Endpunkt mit einem schwarzen Kreis gekennzeichnet sein (z.B. 
Standard-Bemaßung). Ein Segment wird nicht durch das Anbringen eines oder mehrerer 
Befestigungselemente aufgeteilt. Die Befestigungselemente müssen separat bemaßt werden. 
Knoten für Segmente dürfen nur an den Stellen verwendet werden, an denen auch eine fachliche 
Funktion ersichtlich ist (z.B. Ausbindung, Stecker, etc.). Eine Aufteilung in mehrere Segmente aus 
Sicht der Konstruktion darf in der 2D Zeichnung nicht sichtbar sein. 
 
 
Abbildung 3: Beispiel für den Aufbau von Segmenten 
 
Befestigungselement 
Befestigungselemente sind Kabelkanäle, Kabelschienen, Kabelführungen mit Dichtlippe, die über eine 
Leitungsfixierung verfügen, Clipse jeglicher Art und Halter die den Leitungssatz im Fahrzeug führen 
und/oder befestigen. 
 
kZ-siehe-Teil (keine Zeichnung siehe Teil) 
Es ist die Abkürzung für den Hinweis, dass ein Teil (Sachnummer) keine eigene Zeichnung hat, 
sondern auf einer Tabellen-Zeichnung dargestellt ist. 
 
Tabellen-Zeichnung 
Eine Tabellen-Zeichnung ist eine technische Zeichnung in der die Teile zu einer Grundausführung mit 
einem oder mehreren unterschiedlichen Merkmalen in einer Tabelle erfasst sind. Die Sachnummer der 
Tabellen-Zeichnung muss sich von den Sachnummern der Teile unterscheiden. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 14 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 14 von 178 
 
2 Dokumentation der Leitungssätze in 2D Zeichnungen 
Die Dokumentation eines Leitungssatzes in einer Leitungssatzzeichnung kann auf zwei 
unterschiedliche 
Arten 
erfolgen. 
Beide 
Dokumentationsmethoden 
sind 
zulässig. 
Der 
Leitungssatzlieferant muss mit dem zuständigen Daimler Sachbearbeiter bzw. Projekt die 
Entscheidung treffen, welche der Leitungssatzumfänge mit welcher Dokumentationsmethode 
dokumentiert werden. Dabei wird unterschieden in  
1. Dokumentation des Leitungssatzes in einer Mehrblattzeichnung 
2. Dokumentation des Leitungssatzes in einer Einzelblattzeichnung 
Beide Dokumentationsmethoden müssen nach folgenden MB Vorgaben aufgebaut sein: 
 
 
MBN 31 001    
Grundlagen der Produktdarstellung 
 
Die Norm dient als Basisinformation mit dem Ziel einer einheitlichen Produktdarstellung auf CAD- und 
MS – Office Zeichnungen der Technischen Produktdokumentation. 
 
 
MBN 31 020-1  
Schriftfelder in Zeichnungen 
 
Die Norm beschreibt Schriftfelder in Zeichnungen, die ein fest angeordneter Bestandteil eines 
Konstruktionsrahmens (Konstruktions-Zeichnungsvorlage) sind. 
 
 
MBN 31 020-2  
Zeichnungsfeld auf CAD-Zeichnungen 
 
Die Norm beschreibt die Darstellung und Angaben in den verschiedenen Zeichnungs-Arten für 
CAD-Zeichnungen erzeugt aus CATIA V5 / V4[1], die in der Produktdokumentation der  
Geschäftsfelder Mercedes-Benz-Pkw und Mercedes–Benz–Truck zur Anwendung kommen. 
 
 
MBN 31 020-3  
Änderungen in Konstruktionszeichnungen 
 
Die vorliegende Werk Norm MBN 31 020-3, Änderungen in Konstruktionszeichnungen, regelt die 
Dokumentation von Änderungen in Konstruktionszeichnungen. 
 
 
MBN 10 317-0   
Kennzeichnung von Merkmalen zur besonderen  
  
 
 
Nachweisführung Grundlagen - Dokumentationspflicht von  
  
 
 
Bauteilen/ Baugruppen 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 15 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 15 von 178 
 
 
MBN 10 317-2   
Kennzeichnung von Merkmalen zur besonderen  
  
 
 
Nachweisführung Spezifische Vorgaben und Anwendungsfälle  
  
 
 
(MBC, VAN und Buses) 
 
Zur besseren Verständigung ist die Kennzeichnung gemäß MBN 10 317 durchzuführen. Zusätzlich 
unterstützen die Beispielvorgaben bei der Festlegung von sicherheitsrelevanten Merkmalen. 
(DocMaster MBN 10 317–Schulung) 
 
 
A 999 80 02   
Benennungsfestlegung für Abkürzungen im zweisprachigen  
  
 
 
(deutsch/englisch) Schriftfeld der MB-Zeichnungen 
 
Diese Arbeitsanweisung regelt die Anwendung von Begriffsabkürzungen, die im zweisprachigen 
Zeichnungsschriftfeld (dt./eng.) vor oder hinter der Benennung eingetragen werden können. Beispiel: 
ZB für Zusammenbau. 
 
 
V 019 8029   
Änderungen in der Zeichnungsorganisation 
 
Die Verfahrensanweisung regelt die Abwicklung der Zeichnungen, die eine Veränderung erfahren, 
ungültig werden oder Nachfolgedokumente erfordern. 
 
Grundsätzlich gilt für die Leitungssatzzeichnung: 
Sind zusammenhängende, technische Informationen nicht auf einer CAD Zeichnung (auf einem 
Zeichnungsblatt) darstellbar, so können diese auf weiteren Format-Zeichnungsblätter angebracht 
werden. 
Hinweis: 
Eine 
Leitungssatz-Masterzeichnung 
(Tabellensachverhalt) 
kann 
auch 
mit 
einer 
Einzelblattzeichnung freigegeben werden, wenn der Umfang auf einer Einzelblattzeichnung darstellbar 
ist. Umgekehrt muss bei einem umfangreichen Leitungssatzmodul, das nicht auf einer 
Einzelblattzeichnung darstellbar ist, eine Mehrblattzeichnung erstellt werden. 
Im folgenden Kapitel werden die Vorgaben für die Struktur und den Inhalt der beiden 
Dokumentationsarten vorgegeben. Dabei ist zu beachten, dass bestimmte Tabellen nur bei einer 
Leitungssatz-Masterzeichnung auf der Zeichnung dargestellt werden müssen. 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 16 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 16 von 178 
 
2.1 Aufbau einer 2D Leitungssatzzeichnung 
Grundsätzlich gilt für beide Dokumentationsarten, dass der Aufbau von 2D Zeichnungen nach der 
MBN 31 020-1 zu erstellen ist. Der strukturelle Aufbau einer Mehrblattzeichnung ist im folgenden Bild 
dargestellt:  
 
 
Abbildung 4: Aufbau Mehrblattzeichnung 
Wenn eine Zeichnung als Einzelteil Leitungssatzzeichnung angelegt wird, dann muss diese Zeichnung 
so dargestellt werden, dass sowohl die Zeichnung als auch die zugehörigen Tabellen auf diesem 
einzigen Blatt enthalten sind.  
 
Abbildung 5: Aufbau Einzelblattzeichnung 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 17 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 17 von 178 
 
2.2 Besonderheiten 
bei 
der 
Darstellung 
von 
Leitungssätzen 
in 
Mehrblattzeichnung 
Eine Mehrblattzeichnung ist nach der MBN 31 020-1 aufzubauen.  
Besonderheiten der Mehrblattzeichnung auf die geachtet werden muss: 
- 
eine konsistente Blattverwaltung  
- 
Änderungshistorie mit den letzten Änderungsständen der Blätter im Deckblatt der 
Zeichnung. 
- 
konsistente Blattnummer jedes einzelnen Blattes. 
 
Abbildung 6: Besonderheiten auf Zeichnungskopf Mehrblattzeichnungen 
 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 18 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 18 von 178 
 
2.3 Besonderheiten bei der Darstellung von Leitungssätzen in einer 2D 
Zeichnung 
Der prinzipielle Aufbau einer 2D Leitungssatzzeichnung ist in der Abbildung 7: Aufbau der 
Leitungssatzzeichnung (bei Mehrblattzeichnungen Blatt1) dargestellt. Im Zentrum des Blatts steht die 
Zeichnung. Die anzugebenden Informationen und Tabellen sind unter den Punkten 1. – 9. beschrieben.  
 
Abbildung 7: Aufbau der Leitungssatzzeichnung (bei Mehrblattzeichnungen Blatt1) 
Es gelten die folgenden, beschriebenen Aspekte und Referenzen: 
Punkt 
Beschreibung 
Kapitelverweis 
1.  
MB Schriftfelder in Zeichnungen nach MBN 31 020-1 
2.3.1 
2.  
CAD Datum (Version) 
2.3.1 
3.  
Änderungshistorie Zeichnung 
2.3.1 
4.  
Verweis auf die gültigen Ausführungsvorschriften (AV) 
2.3.2 
5.  
Tabelle Schaltpläne, Tabelle SP mit/ohne Steuercode (masterbezogen) 
2.3.3 
6.  
Tabelle DMU 
2.3.4 
7.  
Bemerkungen/Hinweise Lieferantenspezifisch 
2.3.5 
8.  
Kennzeichnung von Merkmalen zur besonderen Nachweisführung 
2.3.6 
9.  
Tabelle Module im Master 
2.3.7 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 19 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 19 von 178 
 
Das Blatt „2.Blatt“ bis n ist schematisch in der Abbildung 8 dargestellt. Die Punkte 1.-4. und 8. von 
Abbildung 7 sind ebenfalls auf diesem Blatt hinterlegt. Zusätzlich sind folgende Punkte darauf zu 
dokumentieren: 
Punkt 
Beschreibung 
Kapitelverweis 
Siehe 1 
die Tabelle ÄM pro Modul 
 
05 
die Tabelle des Schaltplans und der Steuercode pro Modul 
2.3.3 
1.  MB Schriftfelder in Zeichnungen nach MBN 31 020-1 
2.3.1 
2.  CAD Datum (Version) 
2.3.1 
3.  Änderungshistorie Zeichnung 
2.3.1 
4.  Verweis auf die gültigen Ausführungsvorschriften (AV) 
2.3.2 
8.  Kennzeichnung von Merkmalen zur besonderen Nachweisführung 
2.3.6 
10. 
die Leitungssatztabellen pro Modul 
2.3.8 
11. 
die Teilestückliste pro Modul 
2.3.9 
 
 
Abbildung 8: Blatt 2 (bei einer Mehrblattzeichnung) 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 20 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 20 von 178 
 
2.3.1 MB Schriftfeld/ CAD-Datum/ Änderungshistorie Zeichnung (Punkt 1, 2, 3) 
Folgende Normen sind für die Befüllung des MB Schriftfeldes und der Änderungshistorie einzuhalten: 
MBN 31 001 
Grundlagen der Produktdarstellung 
MBN 31 020-1 
Schriftfelder in Zeichnungen 
MBN 31 020-2 
Zeichnungsfeld auf CAD-Zeichnungen 
MBN 31 020-3 
Änderungen in Konstruktionszeichnungen 
MBN 10 317-0 
Kennzeichnung 
von 
Merkmalen 
zur 
besonderen 
Nachweisführung 
Grundlagen - Dokumentationspflicht von Bauteilen/ Baugruppen 
MBN 10 317-2 
Kennzeichnung 
von 
Merkmalen 
zur 
besonderen 
Nachweisführung 
Spezifische Vorgaben und Anwendungsfälle (MBC, VAN und Buses) 
A 999 80 02 
Benennungsfestlegung für Abkürzungen im zweisprachigen 
(deutsch/englisch) Schriftfeld der MB-Zeichnungen 
 
Die Pflichtfelder inkl. Änderungshistorie eines Schriftkopfes sind in der Abbildung 9: Pflichtfelder des 
MB Schriftfeldes rot gekennzeichnet: 
 
Abbildung 9: Pflichtfelder des MB Schriftfeldes 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 21 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 21 von 178 
 
Hinweis: Sofern nicht das Format „R“ verwendet wird, so muss in das in Abbildung 9 gestrichelt 
dargestellte Feld Maßstab ein konkreter Maßstab eingetragen werden. 
 
Wenn in der Zeichnung ein Tabellensachverhalt dargestellt wird (Masterleitungssatz), dann bezieht 
sich sowohl der Schriftkopf als auch der Änderungsindex auf die Sachnummer der Tabelle. Die 
freigaberelevanten Daten der Leitungssatzmodule sind in der „Tabelle Module im Master“ (siehe 
Abschnitt 2.3.7) darzustellen. Die Änderungsindizes der dargestellten Leitungssatzmodule sind pro 
Modul in einer Tabelle darzustellen. 
Bei sehr großen Tabellenzeichnungen (Innenraum, Cockpit …) ist vom zuständigen Daimler 
Sachbearbeiter zu entscheiden, ob diese Informationen auf dem Blatt 2 einer Mehrblattzeichnung 
dargestellt werden sollen. 
Zu empfehlen ist folgende Dokumentation im Änderungsschriftkopf: 
 
Bei umfangreichen Zeichnungen (z.B. Masterleitungssätze) kann für die Dokumentation der 
einzelnen Änderungsmeldungen aus ConnectCHANGE ein Index verwendet werden. Dieser 
Index ist numerisch und kann maximal drei Stellen umfassen. 
 
Auf der Zeichnung können Änderungen durch diesen dann gekennzeichnet werden. Dabei sind 
nur Indizes auf der Zeichnung abgebildet, die auch mit diesem ZGS-Stand geändert wurden. 
D.h. Indizes von vorherigen ZGS-Ständen sind auf der Zeichnung zu löschen. 
 
Vereinfachte Schreibweise der Änderungsmeldung aus ConnectCHANGE: 
Im Änderungsschriftkopf können die Änderungsmeldungen aus ConnectCHANGE in einer 
verkürzten Schreibweise wie folgt angegeben werden: 
<Baureihe><Lieferantennummer><Änderungsmeldung><Jahr> ggf. <Nachtrag> 
Beispiel: 
o Änderungsmeldung aus ConnectCHANGE:  
AEM 222-11-XXXX/13 
o Verkürzte Schreibweise: 
 
 
222-11-XXXX/13 
 
Im Änderungsschriftkopf werden die Indizes mit den zugeordneten Änderungsmeldungen aus 
ConnectCHANGE angegeben. 
 
Die Angabe des Planquadrates/der Planquadrate, in der die Änderung durchgeführt wurde 
kann dabei auch in Abstimmung mit dem Daimler-Verantwortlichen in einer separaten Tabelle 
auf der Zeichnung außerhalb des Zeichnungsschriftkopfes erfolgen. 
 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 22 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 22 von 178 
 
Beispiel: 
 
 
Abbildung 10: Beispiel Änderungsmeldungen 
 
Reicht die Anzahl der freien Änderungszeilen für eine vollständige Dokumentation nicht mehr 
aus, so werden die Änderungsschriftköpfe vom Master und allen von der Änderung 
betroffenen Module „Mit Änderung neu gezeichnet“ bereinigt. Auch wenn eigentlich bei einem 
Teil der geänderten Module die Änderungsschriftköpfe noch nicht voll beschrieben sind. 
 
Beispiel:  
 
Abbildung 11: Beispiel eines Änderungsschriftkopfes "mit Änderung neu gezeichnet" 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 23 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 23 von 178 
 
Die Änderungsdokumentation auf dem Deckblatt der Mehrblattzeichnung ist analog MBN 31020-3 zu 
dokumentieren. Als Bearbeitungsdatum muss hier das Bearbeitungsdatum aus dem Zeichnungskopf 
des geänderten Blattes eingetragen werden. 
2.3.2 Verweis auf die gültigen Ausführungsvorschriften (Punkt 4) 
Die Ausführungsvorschriften „Ausfg.Vorsch/Leitungssatzerstellung“ A000 006 97 99 und  
„Ausfg.Vorsch/Leitungssatz Legende“  A000 002 62 99 sind zu einer Ausführungsvorschrift 
„Ausführungsvorschrift Leitungssatzerstellung“ mit der neuen SNR A 002 006 32 99 
zusammengefasst. 
Der Verweis auf diese AV (A0020063299) muss auf der Leitungssatzzeichnung aufgeführt sein.  
Weiterhin sind alle Verweise auf die freigegebenen Ausführungsvorschriften der Einzelteile des 
Leitungssatzes (Sonderleitungen, Schläuche …) aufzuführen. 
2.3.3 Tabelle Schaltpläne, Tabelle SP mit/ ohne Steuercode (Punkt 5) 
Für die lückenlose Dokumentation eines Leitungssatz ist es erforderlich die für die Erstellung des 
aktuellen Leitungssatz verwendeten Schaltplandatensätze in einer Tabelle aufzuführen. 
 
Pro Sachnummer ist eine Zeile in der folgenden Gliederung anzulegen:  
Spalte 1 Sachnummer der Schaltpläne aufgebaut nach der AV „Schaltplan“ – A 000 006 98 99 
Spalte 2 Blattnummer des Schaltplans 
Spalte 3 Benennung des Schaltplans 
Spalte 4 Datum ( Version ) des Schaltplans nach Vorgabe aus AV „Schaltplan“ – A 000 006 98 99 
Spalte 5 (Code) verwendeten Schaltplancode 
 
Leitungssatz entstand aus folgenden Schaltplänen: 
Harness developed using following wiring diagram: 
Sachnummer 
Blattnummer 
Benennung 
Datum 
Code 
Part number 
Sheet number 
Title 
Date 
Code 
222-540-00-02 
1 
Schaltplan Diagnose 
2011-01-05 
 
222-540-00-01 
2 
Schaltplan Luftfeder 
2011-01-05 
 
Hinweis: Die oben aufgeführten Attribute müssen enthalten sein, das Layout der Tabelle kann von der 
dargestellten Form abweichen. 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 24 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 24 von 178 
 
2.3.4 Tabelle DMU (Punkt 6) 
Hinweis:  
 
 
Die Angabe der DMU-Tabelle und der Sachnummer der DMU-Modelle ist zwingend notwendig, 
unabhängig davon, ob die Zeichnung von einem 3D-Modell abgeleitet worden ist oder nicht. 
 
Seit Einsatz Catia V5 werden Geometrieinformationen aus DMU–Modellen generiert und für den 
Leitungssatzentwicklungsprozess zur Verfügung gestellt. 
 
In der DMU Tabelle müssen alle verwendeten 3D-Modelle, die zur Erstellung des Leitungssatzes 
verwendet worden sind, aufgelistet werden. Dabei sind folgende Attribute aufzuführen:  
- 
Sachnummer des 3D-Modells 
- 
Benennung des DMU-Modells  
- 
Version (Smaragd-Version) des Modells 
- 
Datum der Ableitung des 3D-Modells (KBL-Erzeugungsdatum)  
 
Leitungssatz entstand aus folgenden DMU-Modellen: 
Harness developed using following DMU Models: 
Sachnummer 
Benennung 
Version 
Datum 
Part number 
Title 
Version 
Date 
HCA205540B001 
ZB EL.LTG.SATZ / DMU VORBAU IR LL RE 
0002.021 
2011-12-05 
HCA205540B162 
ZB EL.LTG.SATZ / DMU VORBAU MR 
W/S/A/C BATT RL 
0001.016 
2013-06-03 
Hinweis: Die Spalten Sachnummer, Benennung und Version sind verbindliche Angaben, das Datum ist 
optional. 
2.3.5 Bemerkungen/Hinweise Lieferantenspezifisch (Punkt 7) 
Alle Vereinbarungen, die zu einer besseren Lesbarkeit der Zeichnung dienen und nicht im Lastenheft 
(LH) bzw. Ausführungsvorschrift (AV) erfasst sind, können im Bereich „Baureihenspezifische 
Unterschiede“ dargestellt werden. Dabei ist zu beachten, dass keiner der Vorgaben aus dem LH bzw. 
der AV widersprochen wird.  
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 25 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 25 von 178 
 
2.3.6 Kennzeichnung von Merkmalen zur besonderen Nachweisführung (Punkt 8) 
Das Merkmal „100 % elektrische Prüfung“ muss immer auf allen Leitungssatzzeichnungen abgebildet 
sein. Details zu der Kennzeichnung von besonderen Merkmalen sind aus der MBN 10 317 
„Kennzeichnung von Merkmalen zur besonderen Nachweisführung“ zu entnehmen. 
2.3.7 Tabelle der Module in der Masterleitungssatzzeichnung (Punkt 9) 
Diese Tabelle muss immer dann auf der Zeichnung abgebildet sein, wenn die Zeichnung einen 
Tabellensachverhalt (Masterleitungssatz) abbildet.  
Jede Zeile der Tabelle enthält die Stammdaten eines Leitungssatzes, d.h. alle Informationen, die im 
Schriftkopf einer Modulzeichnung aufzufinden sind. 
Spaltennr. 
Spalten-Name 
Beschreibung 
1.  
Variante 
Kürzel für die Variante des gelisteten Leitungssatzmoduls 
2.  
DAG-Sachnummer 
Daimler Sachnummer des Leitungssatzes 
3.  
Benennung 
Eintragung der SRM - Benennung des Leitungssatzes 
4.  
Zeichnungsgeometriestand 
Eintragung ZGS zur Sachnummer 
5.  
Auftragsnummer 
Eintragung KEM (Freigabe Leitungssatz) 
6.  
Zeichnungsdatum 
Zeichnungsdatum [nach DIN ISO 8601 („JJJJ-MM-DD“)] 
7.  
Name 
Name des Leitungssatzverantwortlichen 
8.  
Leitungssatzgewicht 
Gewicht des Leitungssatz [kg] 
9.  
Datenstand/Version 
[Datum nach DIN ISO 8601 („JJJJ-MM-DD“)] 
10.  
Vorgängersachnummer 
Wenn vorhanden, die Sachnummer des Vorgänger - 
Leitungssatzes 
11.  
Anzahl/Merkmale 
nach Vorgabe MBN 31 020-1 und MBN 10 317 
auszufüllen 
12.  
ESD Kennzeichnung 
nach Vorgabe MBN 31 020-1 und MBN 10 317 
auszufüllen 
13.  
Schaltplancodierung 
alle Code die die Verwendung der einzelnen Module 
beschreiben. Bsp.: IP494 - für USA, Quelle: ist der 
Schaltplan (optional) 
Im Anhang ist eine exemplarische Tabelle eines Moduls eines Masterleitungssatzes dargestellt. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 26 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 26 von 178 
 
2.3.8 Leitungssatztabellen pro Modul (Punkt 10) 
Die Leitungssatztabelle listet die verwendeten Leitungen mit ihren Anschlusspunkten auf und ist 
vorzugsweise 
am 
linken 
Zeichnungsrand 
anzuordnen. 
In 
den 
Tabellenzeichnungen 
(Masterleitungssatzzeichnung) ist in der Überschrift der Tabelle die Sachnummer und Variante des 
Leitungssatzmoduls aufzuführen (auf der Zeichnung/Schriftfeld wird die SRM–Benennung dargestellt, 
siehe Norm MBN 31 020-1). Bei einer Tabellenzeichnung ist diese Tabelle pro Modul aufzuführen. Bei 
sehr großen Tabellenzeichnungen (Innenraum, Cockpit …) ist vom zuständigen Daimler Sachbearbeiter 
zu entscheiden ob diese Informationen auf dem Blatt 2 einer Mehrblattzeichnung dargestellt werden 
sollen. 
Pro Leitung ist eine Zeile in der folgenden Gliederung anzulegen: (4 
Spalten-Nr 
Beschreibung 
1.  
Leitungsnummer 
2.  
Typ der Leitung (4 
3.  
Angaben zu Querschnitt 
4.  
Angabe der Farben (1 
5.  
Bauteilbezeichnung REF (2  (Leitungsanfang), Steckername, Bauteil  (Leitungsanfang) 
6.  
Kontaktbezeichnung (Pin), Bauteil (Leitungsanfang) 
7.  
Feldkenner 
entsprechend 
der 
Zeichnungs-Koordinatenfelder, 
Bauteil 
(Leitungsanfang) 
8.  
Bauteilbezeichnung  REF (2 (Leitungsende), Steckername, Bauteil (Leitungsende) 
9.  
Kontaktbezeichnung (Pin), Bauteil (Leitungsende) 
10.  
Feldkenner entsprechend der Zeichnungs-Koordinatenfelder, Bauteil (Leitungsende) 
11.  
Sachnummer des Kontakt (Leitungsanfang) 
12.  
Sachnummer der am Kontakt verwendeten  Dichtungen  (Leitungsanfang) 
13.  
Sachnummer des Kontakt (Leitungsende) 
14.  
Sachnummer der am Kontakt verwendeten  Dichtungen  (Leitungsende) 
15.  
Leitungslängen (ohne Zuschlag) (3 
16.  
Leitungslängen (mit Zuschlag, optionale Spalte) (3 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 27 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 27 von 178 
 
(1 Leitungsfarben nach DIN IEC 60 757 
(2 Bauteilbezeichnung entsprechend Verwendungszweckkatalog nach VA 059 EI08 
   Referenz -    Langbezeichnung (deutsch)  
   Beispiel: A2 – Radio mit CD Player 
(3  je eine Spalte pro Variante bei Tabellenzeichnungen 
(4  Leitungsausführung nach AV Leitungen A 000 002 61 99 
2.3.9 Teilestückliste (Punkt 11) 
Die Tabelle listet alle Daimler-Teilesachnummern, die im Leitungssatzmodul verbaut sind, auf. Pro 
Sachnummer ist eine Zeile in der folgenden Gliederung anzulegen: 
 
Spalten-Nr 
Beschreibung 
1.  
POS-Nummer entsprechend der Vorgaben aus ConnectPARTS 
2.  
Sachnummer (Daimler AG) 
3.  
Benennung 
entsprechend 
der 
Vorgaben 
aus 
ConnectPARTS  
(auf der Zeichnung/Schriftfeld wird die SRM-Benennung dargestellt, siehe Norm MBN 
31 020-1) 
4.  
Zeichnungssachnummer bei KZ-siehe-Teil Sachverhalt 
5.  
Menge in Stück (je eine Spalte pro Variante bei Tabellenzeichnungen) 
 
Die Einträge in dieser Tabelle sind nach Spalte 2 (Sachnummer) aufsteigend zu sortieren. 
Existiert für eine Sachnummer keine Einzelteil-Zeichnung, so wird in der Teilestückliste auf der 
Zeichnung im Feld „Teilezeichnung“ die Sachnummer der Tabellen-Zeichnung aufgeführt in der diese 
Sachnummer beschrieben ist. 
Die Erweiterung der Sachnummer um das Attribut kZ ist technisch und fachlich nicht relevant und 
entfällt damit auf der Zeichnung. 
Gibt es weder eine Einzelteil-Zeichnung noch eine Tabellenzeichnung, so ist die gültige AV für die 
Sachnummer im Feld Teilezeichnung einzutragen. 
In den Tabellenzeichnungen (Masterleitungssatzzeichnung)  ist in der Überschrift der Tabelle die 
Sachnummer und Variante des Leitungssatzmoduls aufzuführen. Bei einer Tabellenzeichnung ist diese 
Tabelle pro Modul aufzuführen.  
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 28 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 28 von 178 
 
Bei sehr großen Tabellenzeichnungen (Innenraum, Cockpit …) ist vom zuständigen Daimler 
Sachbearbeiter zu entscheiden ob diese Informationen auf dem Blatt 2 einer Mehrblattzeichnung 
dargestellt werden sollen. 
2.3.10 Dokumentation Leitungssatzfremde Umfänge in LS-Zeichnung 
Es gibt Umfänge, die nicht von der Fachabteilung Leitungssatz verantwortet sind, aber zusammen mit 
dem Leitungssatz verlegt werden. Aus diesem Grund werden diese Umfänge in der LS-Zeichnung als 
reine Information dargestellt, d.h. diese sind nicht Bestandteil der KBL bzw. Leitungssatzstückliste. 
Beispiele hierfür sind Wasser- oder Pneumatik-/Hydraulikleitungen. 
2.3.11 Verweis auf Abweichtabellen auf der Leitungssatzzeichung 
Auf den Leitungssatzeichungen müssen lokale Abweichungen (AWT) der jeweiligen Länder 
dokumentiert werden. Dazu muss auf der jeweiligen betroffenen Leitungssatzeichung folgender 
Verweis eingetragen werden:  
Alternative Komponenten werden mit folgenden Abweichtabellen dokumentiert: 
<Liste der SNRs> 
 
Beispiel:  
Alternative Komponenten werden mit folgenden Abweichtabellen dokumentiert: 
A205 540 68 10, A205 540 69 10, A205 540 70 10 
 
 
 
 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 29 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 29 von 178 
 
3 Direktverkabelung von Masseleitungen auf Kabelschuh 
Massekonzept „ohne Splice“ 
3.1 Anforderungen 
1. Der dem Verbraucher nächstliegende Massebolzen, bezogen auf die effektive Leitungslänge, 
ist zu nutzen. Änderungen nur nach Absprache mit dem Auftraggeber.  
2. Der Masseanschluss für 48V erhält eigene Massebolzen, die hier zu verwendende Kabelfarbe 
lautet braun/violett.  
Der Anschluss weiterer Massen z.B. 12V an die 48V Masse ist unzulässig. Eine Verwechselung 
ist z.B. durch die Kabellänge auszuschließen. 
3. Bei der Verlegung der Masseleitungen sind spezielle Anforderungen der Steuergeräte zu 
berücksichtigen.  
z.B. Elektronik-, CAN-, EMV-Massen sind autark auszuführen und werden nicht mit 
Lastströmen oder pulsierenden Strömen gemischt. Redundante Leitungen müssen auf 
separate Massen geführt werden. 
4. Alle Massekabelschuhe einer Massestelle sind vom Auftragnehmer miteinander vormontiert 
(verclipst) anzuliefern. Es ist dabei auf eine bauraumoptimierte Anordnung (kompakte 
Verclipsung) der Kabelschuhe zu achten. Kabelschuhkonstellationen, die von dieser 
bauraumoptimierten Vorgabe abweichen müssen, werden mit dem Auftraggeber separat 
abgestimmt und in der Leitungssatzzeichnung über eine zusätzliche Grafik an der betroffenen 
Massestelle dokumentiert. 
5. Bei der Auslieferung des Leitungssatzes muss sichergestellt werden, dass jede Massestelle 
einen Kabelschuh mit lackschabender Mutter enthält, es sei denn es ist durch den 
Auftraggeber anders gewünscht. 
     
                          
                 
            
      
 
Abbildung 12: bauraumoptimierte Anordnung (kompakte Verclipsung) Kabelschuhe 
Die Art der Anordnung ist über das KBL-Attribut GND_TERMINAL_ARRANGEMENT zu 
beschreiben ( siehe AV Leitungsatz DMU Erstellung )  
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 30 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 30 von 178 
 
3.2 Verarbeitung 
1. Standardmäßig sind elektrische Kontakte in Crimptechnologie anzuschlagen, sofern durch die 
Bauteilvorgabe des elektrischen Kontakts keine andere Anschlagtechnologie vorgegeben wird 
(z.B. Schweißen). Bei der Verarbeitung sind generell neben den Vorgaben auf den Daimler-
Zeichnungen auch alle Vorgaben der Bauteilhersteller zu beachten und umzusetzen. Bei 
eventuellen Widersprüchen in den Vorgaben muss der Auftragnehmer diese proaktiv mit dem 
Auftraggeber abstimmen. 
2. Leitungen (Ltg.) pro Crimp  
 
Es dürfen, unter Berücksichtigung von Punkt „3.2.1“ bis zu 6 Leitungen pro Crimp 
verwendet werden.  
 
Es muss die Summe aller Ltg. zum Crimp passen.  
 
Es muss der gesamte Bündelquerschnitt zum ISO - Crimp passen.  
 
Es müssen die Vorgaben zur Überlötung eingehalten werden. 
Alle Prozessschritte beim Anschlagprozess müssen durch jeweils geeignete und mit dem 
Auftraggeber abgestimmte Maßnahmen überwacht werden. 
3. Unabhängig von bestehenden Regelungen bzgl. Überlöten gilt hiermit als gesetzt, dass 
Kontakte ab einem angeschlagenen (Gesamt-) Leitungsquerschnitt von 6 mm² (oder größer) 
generell zu überlöten sind, sofern nicht eine explizite Freigabe des Kontaktes durch Daimler 
vorliegt, die den Einsatz einer unverlöteten Anschlagtechnologie genehmigt (z.B. 
Schweißkontakte). 
4. Die Stromlast über einen Kabelschuh und Bolzen ist abhängig von der Blechdicke des 
Rohbaus, dem Bolzendurchmesser, Bolzenmaterial bzw. der Bolzengeometrie, Blechdicke des 
Kabelschuhs und von der Leitung. Es ist nicht zulässig, die Strombelastbarkeit eines 
Kabelschuhs ausschließlich an dem Leitungsquerschnitt zu bestimmen, da einige 
Kabelschuhe-Leitungs-Paarungen ausschließlich zur Reduzierung des Spannungsabfalles auf 
der Leitung entwickelt wurden. 
Aus den hier erwähnten Gründen sind viele Kabelschuhe in ConnectPARTS gesperrt. Hier kann 
durch den Konfektionär eine Freischaltung beim Kontaktierungsteam des Auftraggebers 
erlangt werden, wenn eine derartige Prüfung vollzogen wurde und der hier gemessene Strom 
als I.O. bewertet wurde. Ansonsten ist eine Verwendung unzulässig.  
Der maximale Summenstrom (Dauer + Peak) an einem Massebolzen über alle aufgelegten 
Leitungen ist zu dokumentieren und dem Auftraggeber vor der Zeichnungsumsetzung 
vorzustellen. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 31 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 31 von 178 
 
Dabei darf der Imax Bolzen und Imax Kabelschuh nicht überschritten werden.  
Beispiele:  
 
A003 546 22 40:  
auch für große Leitungsquerschnitte sind sowohl der Bolzen M6 als auch die 
Kabelschuh-Blechdicke 0,8 mm die begrenzenden Elemente. 
 
A172 982 30 02: 
auch für große Leitungsquerschnitte sind sowohl der Bolzen M6 als auch die 
Kabelschuh-Blechdicke 0,8 mm die begrenzenden Elemente. 
 
 
Abbildung 13: Auszug maximaler Summenstrom Massebolzen 
 
3.3 Grafische Vorgaben 
1. Grafik wie hier abgebildet in der Zeichnung als Masseanschluss verwenden 
 
 
Abbildung 14: Masseanschluss ohne/mit Schrumpfschlauch 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 32 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 32 von 178 
 
2. Dokumentiert wird nur die Tabelle des Kabelschuhs mit gefangener Mutter.  
Die Anzahl und die Sachnummern der restlichen Kabelschuhe ( ohne gefangene Mutter )  
sind nicht dokumentiert und sind kostenirrelevant, da im Klima-Adernpreis enthalten.  
 
Abbildung 15: Auszug Leitungssatzzeichnung 
3. Die Referenz (REF) der Masse bezeichnet nur den Masseanschluss, nicht die Nummer des 
Kabelschuhs. 
4. Bemaßung für Masse:   
Hier wird von Ausbindungspunkt, Referenzpunkt, Clip, Kabelkanal oder ähnlichem immer bis 
Lochmitte des Kabelschuhs bemaßt. 
Dabei gibt es bei der Konstellation von zwei und vier Kabelschuhen eine Besonderheit.  
Hier wird nicht die direkte Leitungslänge bemaßt, sondern die imaginäre Linie zur Lochmitte 
des Kabelschuhs. Siehe Abbildung 14 für Details. Die Überlängen bei zwei und mehr 
Kabelschuhen sind selbstständig zu ermitteln. Der Längenzuschlag soll vom Kabelschuh her 
kommend in den Hauptstrang zurückgeführt werden. Dort soll dieser sauber eingelegt oder 
eingebunden werden. 
 
 
Abbildung 16: Bemaßung der Massestelle bei einem und drei Kabelschuhen 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 33 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 33 von 178 
 
 
 
Abbildung 17: Bemaßung der Massestelle bei zwei und vier Kabelschuhen 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 34 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 34 von 178 
 
4 Datenformate und Einheiten der Leitungssatzzeichnung  
4.1 Leitungslängen 
Längenmaße sind in Millimeter und ohne Nachkommastellen aufzuführen. 
 
4.2 Leitungssatz-Gewicht 
Gewichtsangaben in Kilogramm sind auf drei Nachkommastellen zu begrenzen.  
Das gewogene Gewicht ist beim Archivieren von Leitungssatzdaten im PDM-System Smaragd / 
Sm@web einzutragen. Liegt dieses nicht vor, so muss ein Prognosegewicht angegeben werden. 
Masterleitungssätze enthalten keine Gewichtsangabe. 
Die 
Prognose-Gewichtsangabe 
bei 
Leitungssatzmodulen 
ist 
auf 
der 
Zeichnung 
des 
Masterleitungssatzes enthalten und in der daraus abgeleiteten KBL einzutragen. 
Bei Leitungssatzmodulen, die nicht über einem Masterleitungssatz zusammengefasst sind, ist die 
Gewichtsangabe auf der Zeichnung im Zeichnungskopf und der daraus abgeleiteten KBL einzutragen 
(vergleiche Abbildung 9). 
 
4.3 Angabe des Datums 
Nach der DIN ISO 8601 „Datenelemente und Austauschformate“ ist der Datenstand auf der Zeichnung 
und im KBL im erweiterten Format vollständig darzustellen: 
        JJJJ-MM-TT 
z.B.: 2009-11-23 
 
4.4 Positionsnummern 
Die Positionsnummer besteht aus einer drei- bis vierstelligen Zahl und einem Kennbuchstaben für die 
Produktgruppe. Die Positionsnummer wird für alle beschränkt oder unbeschränkt freigegebenen 
Sachnummernteile von ConnectPARTS vergeben. 
Fehlende Positionsnummern müssen beim Teileverantwortlichen eingefordert werden. 
 
Nur Teile, die in ConnectPARTS dokumentiert sind, dürfen eine POS-Nr. haben.  
A-Sachnummer und N-Sachnummer benötigen zwingend eine POS-Nummer. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 35 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 35 von 178 
 
4.4.1 Format der Positionsnummer 
Die Notation der Positionsnummer besteht aus einer drei- bis vierstelligen Zahl und einem 
Kennbuchstaben für die Klassifizierung der Produktgruppe: 
P 
PKW 
G 
G Klasse 
Z 
Zusatzteile 
… 
Bsp.: P698 
 
Die Kennbuchstaben für die Produktgruppe sind in ConnectPARTS nach den Regeln der VA 998027 
festgelegt.  
4.4.2 Sonstige Positionsnummern 
Werden Neuteile verwendet, für die noch keine Positionsnummer vergeben wird, ist die 
vorübergehende Positionsnummer Z000 zu verwenden und später gegen die Positionsnummer aus 
ConnectPARTS auszutauschen. 
B8 Teile, die in ConnectPARTS keine Positionsnummer bekommen, sind mit der Positionsnummer 
Z005 zu kennzeichnen. 
 
Erläuterung der Dummy POS- Nummern: 
Z000 = Bauteil noch nicht festgelegt 
Z001 = Bauteil noch nicht festgelegt Bitte Rücksprache 
Z002 = Versuchsteil siehe Zeichnung 
Z003 = Leitungen werden bei Endmontage gesteckt 
Z004 = kein Einzelteil / im ZB enthalten 
Z005 = Bauteil ohne POS – Nr. siehe Zeichnung 
Z006 = Bauteile variabel (KSL) siehe Zeichnung 
Z007 = Bauteile, die ein Schlüsselwort tragen 
 
4.5 Teilkennzeichnung 
Jeder Einzelleitungssatz muss eine Kennzeichnung nach MBN 10435 an einer im eingebauten Zustand 
einsehbaren Stelle erhalten. Bei manchen kundenspezifischen Leitungssätzen ist dies nur begrenzt 
möglich! Hierfür ist dann ein „Sammeletikett“ für die Variante zu verwenden. 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 36 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 36 von 178 
 
4.6 Allgemeine Anforderung Umweltschutz 
Auf allen Leitungssätzen ist die Norm DBL8585 in der Legende aufzuführen.  
Der Lieferant ist verpflichtet, zu jeder Zeit alle gültigen gesetzlichen Regelungen, die seine Lieferung 
oder 
Leistung 
betreffen, 
hinsichtlich 
Umweltschutz, 
Gefahrstoffrecht, 
Gefahrgutrecht, 
Arbeitssicherheit und Transport in aktueller Fassung zu berücksichtigen. Er hat die Umwelt-Leitlinien 
des Daimler-Konzerns zur Kenntnis genommen und orientiert sich bei seinen Tätigkeiten an deren 
Inhalten. 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 37 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 37 von 178 
 
5 Darstellung 
von 
Leitungssatzkomponenten 
in 
der 
2D 
Zeichnung 
5.1 Darstellung der Kontaktierungsteile 
Ein Beispiel, welches in den folgenden Abschnitten detailliert beschrieben wird: 
 
Abbildung 18: Beispiel-Darstellung Kontaktierungsteil auf Zeichnung 
5.1.1 Kontaktierungsteil-Symbol 
Die verwendete Kontaktierung ist in klassischer Form, bestehend aus Seitenansicht und Sicht auf die 
Kontakt-Bestückungsseite, entsprechend der Zeichnungs-Darstellung nach CAD-Handbuch CS080 
darzustellen.  
Die Kontaktierungsteilsymbole müssen mit folgenden Informationen beschriftet sein: 
 
Sachnummer und sofern vorhanden die Positionsnummer des Kontaktierungsteils 
 
Farbe und Codierung des Kontaktierungsteils 
 
Bauteilbenennung des Kontaktierungsteils 
 
Bauteilbezeichnung (REF) und Langbezeichnung (fett geschrieben) in Deutsch 
und darunter 
Bauteilbezeichnung (REF) und Langbezeichnung (fett geschrieben) in Englisch 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 38 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 38 von 178 
 
5.1.2 Kontaktierungsteil-Tabelle 
Ein Grafiksymbol der Kontaktierung ist neben der Kontaktliste mit Sicht auf die Bestückungsseite des 
Kontaktierungsteiles zu platzieren. Die Symboldarstellung muss nicht maßstäblich sein, muss aber 
den Bezug auf die Nummerierung der Kontakte herstellen. 
Inhalte der Kontaktliste am Graphiksymbol: 
Textspalte 
Titel 
Inhalt 
1 
Pin 
Kontaktnummer des Steckers 
2 
Ltg.Nr. 
Leitungsnummer 
3 
Typ 
Ausführungsart der Leitung 
4 
qmm 
Querschnitt der Leitung 
5 
Farbe 
Farbkodierung der Leitung 
6 
Mod 
optional nur bei Master 
7 
Kontakt  
Sachnummer des angeschlagenen Kontakts 
8 
KontaktMat 
Material Kontaktoberfläche 
9 
Dichtung 
Sachnummer der Einzeladerabdichtung 
Gehäusefarben sind in Deutsch und Kleinschrift anzugeben. 
Leere Kammern müssen im Tabellenfeld dargestellt werden (vergleiche Abbildung 18). 
5.1.3 Symbolbereitstellung 
Die Symbole der bisherigen CADDS-Bibliothek können bei Bedarf im Format SVG bereitgestellt 
werden. 
Neue Symbole werden vom Lieferanten des Steckers oder im Rahmen des Komponentenmanagement 
aus CAD-Modellen generiert und im Format SVG in Smaragd bereitgestellt (siehe auch CAD Handbuch 
CS080). 
Über den Datenaustausch erhält der LS-Lieferant diese Symbole auf Anfrage. Vom LS-Lieferanten neu 
erstellte Symbole werden über den Datenaustausch der DAG zur Verfügung gestellt und wiederum 
zentral verteilt.  
5.1.4 Referenzbezeichnungen (REF) 
Die dargestellten Kontaktierungsteile werden mit einer eindeutigen Bauteilbezeichnung (REF) 
aus der Komponentendatenbank versehen. Fehlende Referenzen sind anzufordern bzw. für die 
jeweilige Baureihe freizuschalten. Die Anforderung wird entsprechend Verfahrensanweisung VA 059 
EI08 über RD/FIL abgewickelt. 
Die Referenzen der Kontaktgehäuse (bspw. „N17/7*1-B“) müssen innerhalb eines Masters eindeutig 
sein. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 39 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 39 von 178 
 
5.1.5 Alternative Verwendung 
Für jede Verwendung eines Teiles ist die richtige REF einzutragen. Bei Mehrfachverwendung ist die 
entsprechende REF auszuwählen. 
z. B.  
Einstiegsleuchte vorne links  
= 
E17/3 
Einstiegsleuchte vorne rechts = 
E17/4 
5.1.6 Aufbau der Referenz 
Die Referenzen und deren Aufbau kommen über die Schnittstelle ConnyE aus ConnectPARTS und 
werden über den Schaltplan in den Leitungssatz durchgeschleust. Auf der Leitungssatzzeichnung ist 
die Langbezeichnung der REF aus ConnectPARTS zu verwenden.  
Hinweis: Die Referenz kann zusätzlich Informationen zu Anlage- und Ortskennzeichen tragen. Sollte 
kein Anlagekennzeichen benötigt werden/ verfügbar sein, so wird direkt nach der Referenz das 
Ortskennzeichen aufgeführt (Detailinformationen dazu sind der aktuellen AV Schaltplan sowie AV 
Leitungssatz DMU zu entnehmen). 
5.1.7 Steckername 
Der Steckername besteht aus max. 6 Zeichen.  
Hinweis: In EPDM werden aktuell nur 3-stellige Steckernamen unterstützt.  
5.1.8 Kontaktform 
Die Kontaktform der Pins wird in EPDM bzw. E3.Cable festgelegt. 
5.1.9 Kabelschuh 
Wenn nicht anders angegeben sind DIN-Kabelschuhe nach Spezifikation DIN 46225 und DIN 46234 zu 
verwenden und in der Zeichnung durch eine entsprechende Darstellung anzuzeigen. 
Rastkabelschuhe 
Rastkabelschuhe werden ebenfalls mit einem Referenz- bzw. Übermaß bis Lochmitte bemaßt 
entsprechend Abbildung 28 und 
Abbildung 29 dargestellt. Die einzuhaltenden Anordnungen der Rastkabelschuhe der Firma Delphi 
bzw. Stocko sind der entsprechenden Vorschrift von Delphi bzw. Stocko zu entnehmen.  
Kontaktschuh M6: Tabellenzeichnung  A 000 982 67 02 (Delphi) bzw. A 172 982 13 02 (Stocko) 
Kontaktschuh M8: Tabellenzeichnung  A 002 982 54 02 (Delphi) 
DIN-Kabelschuhe 
DIN-Kabelschuhe werden ebenfalls mit einer Referenz- bzw. Überbemaßung bis Lochmitte bemaßt. Es 
gilt eine Standardlänge von 25mm, unabhängig vom Querschnitt des Crimpbereichs. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 40 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 40 von 178 
 
5.1.10 Sicherungen, Relais 
Sicherungen und Relais sind als Zubehörteile direkt unterhalb der Kontaktierungsteil-Tabelle auf der 
Zeichnung aufzuführen. 
 
 
 
 
 
 
 
 
 
 
 
 
 
Abbildung 19: Dokumentation Zubehörteil - Beispiel Sicherung 
 
Die Überschrift der Tabelle setzt sich aus dem Präfix „Zubehörteile zu“, der Bauteilbezeichnung (REF) 
und Langbezeichnung (fett geschrieben) zusammen. 
Die Tabelle selbst ist nach folgendem Format aufgebaut: 
 
Benennung (Sicherung/Relais) 
 
Farbe 
 
Pin (bei Relais ggf. leer) 
 
Steckplatz 
 
Kundenteilenummer 
 
Modul 
 
 
 
 
Benennung 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 41 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 41 von 178 
 
5.2 Dokumentation von Leitungssatz-Stützpunkten 
Stützpunkte dienen dazu mehrere elektrische Leitungen miteinander zu verbinden. Durchgangs-
Stützpunkte werden zum Mittelpunkt bemaßt. Man unterscheidet dichte und undichte Stützpunkte.  
5.2.1 Gültige Varianten 
Die verschiedenen, gültigen Varianten und ihre AV-Sachnummern sind der TB AV A 009 000 38 99 zu 
entnehmen. 
5.2.2 Darstellung 
Die verschiedenen Stützpunkte sind wie folgt in der Zeichnung zu dokumentieren: 
 
Abbildung 20: Symbole Stützpunkte 
 
5.3 Schlüsselwörter 
Alle erlaubten Schlüsselwörter sind in der CONNECT Datenbank dokumentiert. 
 
5.4 Leitungsschlaufen bzw. geändertes Routing 
Leitungsschlaufen bzw. ein geändertes Routing in der Leitungssatzzeichnung sind für sicherheits-
relevante bzw. kundenspezifische Anforderungen zulässig. Dazu wird ein Routingpoint der Leitung im 
Schaltplan hinzugefügt, siehe AV-Schaltplanerstellung A0000069899. Dieses Vorgehen darf nicht bei 
produktionsrelevanten 
Verlegewegen 
des 
Leitungssatzlieferanten 
verwendet 
werden. 
Bei 
Ringstrukturen (Doppel-H) muss der exakte Verlegeweg manuell der Leitung zugewiesen werden, um 
die reale Leitungslänge im Leitungssatz (KBL) zu erhalten, d.h. auch hier darf kein Routingpoint 
verwendet werden. 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 42 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 42 von 178 
 
5.5 Lagedarstellung 
 
Abbildung 21: Darstellung der Uhrzeit mit Definition der Montagebrettebene (DE) 
Hinweis: Die Legende zur Definition von Uhrzeiten muss auf der Zeichnung vermerkt sein. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 43 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 43 von 178 
 
 
Abbildung 22: Darstellung der Uhrzeit mit Definition der Montagebrettebene (EN) 
Der Leitungssatz auf der Zeichnung ist flach ausgebreitet dargestellt. Die Draufsicht der Zeichnung ist 
in Montagebrettebene dargestellt. Nicht mit Lage gekennzeichnete Befestigungselemente 
entsprechen der Steckrichtung 6 Uhr. 
Die Sichtrichtung ist auf der Zeichnung am Hauptstrang anzugeben und im Verlauf des 
Leitungsstranges ggf. zu wiederholen. Die Sichtrichtung ist grundsätzlich vom Stamm in 
Abgangsrichtung.  
 
 
 
Abbildung 23: Darstellung der Blickrichtung auf der Zeichnung 
Hinweis: Die Darstellung der Blickrichtung muss mindestens einmal auf der Zeichnung vermerkt sein. 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 44 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 44 von 178 
 
5.6 Ausbindungsrichtung 
Ausbindungen sind in der Zeichnung lagerichtig darzustellen.  
Die Richtung der Darstellung einer Ausbindung auf der Zeichnung muss der Abgangsrichtung des 
Leitungssatzes entsprechen. Bei Abweichung der Darstellung zur Produktionsrichtung ist eine 
Hinweiszeichnung für die tatsächliche Abgangsrichtung zum Hauptstrang darzustellen.  
T-Ausbindungen müssen gekennzeichnet werden.  
 
5.7 Befestigungselemente 
Nicht gekennzeichnete Befestigungselemente haben grundsätzlich die Lage 6 Uhr zum Leitungssatz 
und zeigen zur Montagebrettebene. Weicht die Lage hiervon ab, ist dieses mit der Uhr zu 
kennzeichnen. Die grafische Darstellung der Befestigungselemente hat keine Aussage über die Lage. 
5.7.1 Lage Befestigungselement zum Leitungssatz 
Die Lage des Befestigungselements zum Leitungssatz wird durch die Darstellung des Dreiecks mit 
Angabe der Uhrzeit definiert. 
Die Definition der Lage durch die grafische Darstellung des Befestigungsteils wird nicht berücksichtigt.  
 
 
Abbildung 24: Befestigungselement zeigt in Montagebrettrichtung 9 Uhr, auch wenn grafisch anders 
dargestellt 
5.7.2 Lage Befestigungselement und Steckrichtung zum Leitungssatz 
Unterscheidet sich die Lage vom Befestigungselement zu der Steckrichtung des Befestigungselements 
zum Leitungssatz, so ist zusätzlich das Symbol Quadrat mit der Uhrzeit für die Steckrichtung 
darzustellen. 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 45 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 45 von 178 
 
 
Abbildung 25: Befestigungselement liegt zur Montagebrettebene 9 Uhr, die Steckrichtung liegt auf 6 
Uhr 
 
= Lage des Befestigungselements zum Leitungssatz 
 
 
 = Steckrichtung zum Leitungssatz 
 
Für die Sonderfälle einer parallel zum Leitungsstrang liegenden Steckrichtung (siehe Abbildung 26) 
sind die Symbole • für die Steckrichtung in Blickrichtung und x für die Steckrichtung entgegen der 
Blickrichtung zu verwenden. 
 
 
= Steckrichtung in Blickrichtung zum Leitungsstrang 
 
      
= Steckrichtung entgegen der Blickrichtung zum Leitungsstrang 
 
 
Abbildung 26: Beispiel einer Steckrichtung parallel zum Leitungsstrang 
5.7.3 Lage Kabelbinderschloss zum Leitungssatz 
Werden zur Darstellung eines Kabelbinderschlosses mehr Informationen benötigt, so ist eine 
Hinweiszeichnung anzugeben. 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 46 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 46 von 178 
 
5.7.4 Benennung Befestigungspunkt in der Zeichnung 
Die Benennung von Befestigungspunkte in der Zeichnung muss folgendem Format entsprechen und 
eindeutig sein: 
Präfix "FX"+ Trenner + <Bauraumkürzel, 2-stellig> + Trenner + <Zähler, max. 6-stellig> oder 
Präfix "FX"+ Trenner + <Bauraumkürzel, 3-stellig> + Trenner + <Zähler, max. 5-stellig> 
Trenner = “.“ 
Beispiel: 
FX.IR.1000 
Die Bezeichnung des Bauraumkürzels muss dabei den in CONNECT spezifizierten und in den 
Schaltplan übernommenen Verlegebereichen entsprechen. Sie werden bereits im DMU definiert. 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 47 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 47 von 178 
 
5.8 Darstellung Segmente 
Segmente ohne Isolierung werden als Einzellinie dargestellt. 
5.8.1 Darstellung Segmente mit Isolierung 
Bei 
Segmenten 
mit 
Isolierung 
wird 
die 
entsprechende 
Darstellung 
angezeigt. 
 
DE 
EN 
Symboldarstellung 
Geflechtschlauch 
Braided sleeve 
 
Glasseidenschlauch 
Glass silk sleeve 
Schlauch allg. 
Sleeve 
Sparbandagierung 
Spiral space tape 
Vollbandagierung 
Full space tape 
Längsbandagierung 
Longitudinal space tape 
Wellrohre 
Corrugated tube 
 
Als Isolierungskennzeichnung sind die Kurzformen der Komponentendatenbank zu verwenden und am 
jeweiligen Segment anzugeben.  
Die Spezifikation der Isolierung wird in einem Sechseck (z.B.: 
) dargestellt. 
Hinweis: Es soll immer möglichst der dünnste (eng-anliegendende) Schlauch verwendet werden. 
5.8.2 Darstellung nicht angeschlagene Schirme 
Geschirmte Leitungen, bei denen der Schirm nicht am Kontaktgehäuse angeschlagen wird, werden in 
der Zeichnung durch ein Segment dargestellt. Der nicht angeschlagene Schirm wird dabei nicht 
explizit in der 2D-Zeichnung dargestellt. Siehe hierzu auch die AV Schaltplanerstellung A0000069899, 
Kapitel Darstellung von nicht angeschlagenen Schirmen in Schaltplänen. 
Weitere Details zur Abbildung in der KBL finden sich in Kapitel 8.3.3.11. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 48 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 48 von 178 
 
5.9 Etiketten und Markierungen 
Der Bezugspunkt für die Bemaßung von Etiketten und Markierungen ist auf deren Anfangspunkt der 
Etiketten und Markierungen zu legen. Es gilt die Allgemeintoleranz. 
 
5.10 Bemaßungen 
5.10.1 Vermaßung Steckereingang 
In vielen Fällen wird die Isolierung des Leitungsbündels nicht bis zum Steckereingang ausgeführt und 
endet 30 mm vor dem Steckereingang. In diesem Fall kann die Vermaßung entfallen. 
Abweichungen von dieser Regel müssen separat vermaßt werden. 
5.10.2 Benennung Referenzpunktbemaßung 
An längenkritischen Verbauungsorten kann eine Referenzpunktbemaßung sinnvoll sein.  
Diese muss folgendem Format entsprechen: 
 
Abbildung 27: Benennung Referenzpunktbemaßung 
5.10.3 Bemaßung von Kabelschuhen 
Für die Bemaßung von Kabelschuhen wird ein Referenz- bzw. Übermaß verwendet. Dieses wird für 
gerade, gewinkelte Kabelschuhe und Sonderformen angewandt. Die Bemaßung wird vom 
Referenzpunkt/Segmentknoten bis zur Lochmitte des Kabelschuhs definiert. Für die Bemaßung gelten 
die in Kapitel 5.11.1 beschriebenen Toleranzen. Weitere Besonderheiten wie beispielsweise 
Verarbeitungshinweise sind den Zeichnungen zu entnehmen und sind bei der Vermessung zu 
berücksichtigen. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 49 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 49 von 178 
 
 
Abbildung 28: Bemaßung von Kabelschuhen mit Übermaß 
 
 
Abbildung 29: Bemaßung von Kabelschuhen mit Referenzmaß 
 
 
Abbildung 30: Bemaßung von gewickelten Kabelschuhen mit Übermaß 
 
Auf Grund des Längentoleranzausgleichs muss das "letzte" Segmentmaß vor dem Kabelschuh in 
Klammern geschrieben bzw. ausgeblendet werden. 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 50 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 50 von 178 
 
5.10.4 Bemaßung Befestigungselement 
Neben der Lagedarstellung muss auch die genaue Position eines Befestigungselementes auf der 
Zeichnung festgehalten werden. Diese ist von einem prominenten Bezugspunkt (z.B. Kontaktgehäuse, 
Ausbindung, etc.) ausgehend mit Hilfe eines geeigneten Maßes anzugeben. 
 
Abbildung 31: Beispiel Bemaßung Befestigungselemente (Auszug aus einer Zeichnung) 
5.10.5 Referenzpunktbemaßung für Befestigungselemente: 
An längenkritischen Verbauungsorten kann eine Referenzpunktbemaßung sinnvoll sein. Diese muss 
zwischen Lieferant und DAG Baureihen-Team abgestimmt werden. 
 
 
Abbildung 32: Referenzpunktbemaßung für Befestigungselemente 
Die Toleranz zwischen Referenzpunkt und Befestigungselement beträgt +5mm. Eine davon 
abweichende Toleranz muss mit einem Hinweistext gekennzeichnet sein. 
Der Abstand zwischen Referenzpunkt und Bemaßungspunkt sollte nach Möglichkeit 1000 mm nicht 
überschreiten. Der Referenzpunkt muss benannt und durch Maß Linie oder Maß Pfeil gekennzeichnet 
sein. Beim Abstand mehrerer Befestigungselementen bezogen auf einen Referenzpunkt darf kein 
Untermaß entstehen. Die Segmentbemaßung muss immer angegeben werden.  
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 51 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 51 von 178 
 
5.11 Toleranzen 
5.11.1 Toleranzen für Segmente 
Die Allgemeintoleranz gilt für Leitungslängen je Segment.  
Die Zwischenbemaßung von Segmenten durch das Anbringen von Befestigungsteilen hat keine 
Auswirkung auf die Allgemeintoleranz je Segment.  
 
 
Abbildung 33: Legende Allgemeintoleranz (DE) 
Hinweis: Die Legende zur Allgemeintoleranz muss auf der Zeichnung vermerkt sein. 
 
 
Abbildung 34: Legende Allgemeintoleranz (EN) 
Ein Segment wird von Anfangs- bis Endknoten bemaßt. Die Bemaßung muss je Segment dargestellt 
werden. Bezugspunkt für die Bemaßung von Ausbindungen ist die neutrale Faser des Leitungsbündels. 
Diese Standardtoleranzen sind zusätzlich im Datenformat KBL zu hinterlegen (siehe Kapitel ********). 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 52 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 52 von 178 
 
5.11.2 Toleranzen für Referenzpunktbemaßung von Segmenten 
Die Toleranz eines Segments von Ausbindung bis Ausbindung/Ende ist die Allgemeintoleranz.  
Eine davon abweichende Toleranz muss mit einem Hinweistext gekennzeichnet sein z. B: Fix Maß. 
Der Referenzpunkt muss benannt oder durch eine Maß Linie gekennzeichnet sein. 
Beim Bemaßen mehrerer Segmente bezogen auf einen Referenz-Punkt darf keine Verkürzung des 
Nennmaßes für eines der Segmente entstehen. 
5.11.3 Toleranzen für Befestigungselemente 
Bei Kettenbemaßung beträgt die Toleranz zwischen zwei Befestigungselementen +5mm. 
(Befestigungselement-Variante beachten) 
Die max. zulässige Toleranz eines Segmentes beschränkt die Summe der Toleranzen aller auf diesem 
Segment angebrachten Befestigungselemente. 
Toleranz zwischen Befestigungselementen auf einem Segment: +5mm 
Segment Toleranz darf nicht überschritten werden 
Legende: Toleranzen für Befestigungselemente (muss auf Zeichnung vermerkt sein) 
 
Sind abweichende Toleranzen nötig, so werden diese in Abstimmung mit dem DAG Baureihen-Team 
separat definiert.  
5.11.4 Toleranzen für Tüllen 
Für Tüllen gilt die Allgemeintoleranz bezogen auf die Einknüpfebene.  
 
 
Abbildung 35: Einknüpfebene einer Tülle 
Sind abweichende Toleranzen oder Bemaßungspunkte nötig, so werden diese in Abstimmung mit dem 
DAG Baureihen-Team separat definiert.  
 
 
Einknüpfebene
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 53 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 53 von 178 
 
6 Überprüfung der Leitungssatzmaße 
Für die einheitliche Sicherstellung der Maßangaben auf der Leitungssatzzeichnung ist für kritische 
Punkte eine Messdokumentation zum Leitungssatz-Produkt zu erstellen. (Messbuch der Baureihe) 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 54 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 54 von 178 
 
7 Datenbereitstellungsprozess 
Es gelten die allgemeinen von Daimler vorgegebenen Richtlinien zur Datensicherheit und zur 
Datenbereitstellung bzw. zum Datenaustausch. 
Für die Datenbereitstellung in Smaragd (z.B. über Sm@Web) ist die Archivierungsrichtlinie Smaragd 
A0598004 zu beachten. Es ist untersagt Entwicklungsdaten per Email zu versenden! 
Leitungssatzdaten sind ausschließlich über Smaragd bereitzustellen. 
7.1 Leitungssatzdaten 
Folgende Systeme werden bei der Datenbereitstellung der Leitungssatzdaten aktiv verwendet: 
 
Connect – (HARNESS, PARTS und CHANGE) 
 
ACM 
 
E3.Cable (ConnyE) 
 
Service – Dokumentation  
 
Smaragd bzw. Sm@Web 
 
EPDM 
 
DIALOG 
 
ZGDOK 
 
Siemens NX 
 
Leitungssatzentwicklungstool (kein vorgeschriebenes System) 
 
 
Abbildung 36: Daten/Tools 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 55 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 55 von 178 
 
Im 
folgenden 
Bild 
erkennt 
man 
die 
Toolabbildung 
von 
den 
Grundprozessen 
der 
Leitungssatzentwicklung:  
- 
Component Management 
- 
Elektrologik Erstellung 
- 
3D Leitungssatzverlegung 
- 
2D Zeichnungserstellung  
- 
Offizielle Freigabe und Dokumentation 
- 
Change Management Prozess 
 
 
Abbildung 37: Abbildung Prozesse auf Toollandschaft 
Die Zeitpunkte der Datenbereitstellung sind mit dem Baureihenprojekt und dem verantwortlichen 
Sachbearbeiter abzustimmen. 
Aus ConnectPARTS kommen verschieden Datenpakete, die dann an die Folgeprozesse automatisch 
versorgt werden:  
- 
VZK Katalog (xSeparated Textfile, beinhaltet den Verwendungszweck-Katalog) 
- 
CLS Katalog (Library für LCable)  
- 
Teilekatalog 
- 
XML Teilekatalog 
- 
Verwendungskatalog 
- 
Schlüsselwortwerte 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 56 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 56 von 178 
 
7.2 Schaltplandaten 
Die Schaltpläne und Schaltplanoriginaldaten sind Eigentum der Daimler AG (MBC/D) und für den 
internen Gebrauch entsprechend MBN 31 002 auszuführen. Zeichnungsrahmen und Schriftköpfe, 
Bibliotheken, Template-Dateien für Schriften und Ebenen, Bauteil-, Referenzen-, Code- und 
Leitungsdaten sowie die  notwendigen Zusatzfunktionen der Software werden von RD/FIL zur 
Verfügung gestellt und müssen verbindlich verwendet werden. Als Schaltplaneditor ist E3.Cable, 
nachfolgend auch Schaltplaneditor genannt, mit dem Plug-In ConnyE zum Datenaustausch mit 
CONNECT zu verwenden.  
 
Die erste Strukturebene der Schaltpläne, System/ Funktionsbezug sind fest vorgegeben. Die zweite 
Strukturebene und der Sachnummernkreis usw. werden inhaltlich vom Leitungssatz-Baureihenteam 
vorgegeben bzw. muss vor Projektstart abgesprochen und festgelegt werden. 
 
Die Inhalte der Schaltpläne sind im Kapitel Schaltplaninhalte in der AV Schaltplanerstellung 
A0000069899 beschrieben. 
Firmenspezifische Informationen sind nur in den vereinbarten und abgestimmten Attributen gestattet 
oder vor der Bereitstellung zur MBC/D zu entfernen. 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 57 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 57 von 178 
 
8 Datenformate in der Leitungssatzentwicklung 
8.1 Einzelleitungs-, Modul- und Masterleitungssatz 
8.1.1 Einzelleitungssatz 
Bei einem Einzelleitungssatz handelt es sich um ein bestellbares Teil aus einem Baukasten mit 
mindestens einer elektrischen Leitung und Anbauteilen. Er wird als Einzelteil auf einer eigenen 
Zeichnung nach MBN 10317 dokumentiert werden. 
 
Abbildung 38: Strukturierung KBL bei Einzelleitungssätzen 
8.1.2 Modulleitungssatz 
Bei einem Modulleitungssatz handelt es sich um ein bestellbares Teil aus einem Baukasten mit 
mindestens einer elektrischen Leitung und Anbauteilen. Er wird als Einzelteil nicht auf einer eigenen 
Zeichnung, sondern auf einer Tabellenzeichnung nach MBN 10317 dokumentiert werden. 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 58 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 58 von 178 
 
8.1.3 Masterleitungssatz 
Bei einem Masterleitungssatz handelt sich um eine Zeichnung vom Typ Tabelle mit mehreren 
bestellbaren Zusammenbauteilen vom Typ Modulleitungssatz (kZ-Teil). In der Regel handelt es sich 
hier um einen Bauraumleitungssatz mit mehreren dargestellten Varianten und / oder optionalen Add-
On Leitungssätzen.  
Der Masterleitungssatz bildet die Grundlage des Kundenspezifischen Leitungssatzes (KSL). Dieser 
enthält ein Grundmodul und kann mit den Varianten, optionalen Add-On Leitungssätzen, zu einem 
auftragsbezogenen, kundenspezifischen Leitungssatz zusammengestellt und fertig montiert werden. 
Ein Masterleitungssatz besteht i.d.R. somit aus einem Grundmodul und Modulleitungssätzen. Eine 
HCV bzw. KBL-Datei eines Masters beinhaltet immer alle zu diesem Master gehörenden 
Modulleitungssätze. 
Die Benennung eines Masterleitungssatzes beginnt mit TB ZB Elektr. Leitungssatz. Für jeden 
Modulleitungssatz wird zusätzlich eine laufende Variantennummer "(Bsp. V1, ...)" vergeben.  
Die Zeichnung muss einer Tabellenzeichnung nach MBN 10317 entsprechen. 
 
 
Abbildung 39: Strukturierung KBL bei Masterleitungssätzen 
Eine Tabellenzeichnung hat eine eigene Sachnummer, hinter der sich kein bestellbares Teil verbirgt. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 59 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 59 von 178 
 
Bestellbare Teile sind mit ihrer Sachnummer auf der Tabellenzeichnung dargestellt. 
Übertragen auf die KBL stellt die Tabelle den Harness-Container dar, in der bestellbare Teile mit ihrer 
Sachnummer als Modul abgebildet sind. 
 
8.2 Namenskonvention für KBL- und HCV-Dateien 
Syntax:  
<MBC Sachnummer>_<ZGS>_<Datenstand>.kbl 
<MBC Sachnummer>_<ZGS>_<Datenstand>.hcv 
 
Beispiele: 
ZGS = 001 
MBC Sachnummer = A1665406503 
Datenstand = 2013-11-21 
Dateiendung für KBL Dateien = kbl 
 
Der Dateinamen setzt sich aus der Sachnummer ohne Leerzeichen/Sonderzeichen, dem ZGS 
(3stellig), dem Datenstand im Basisformat (JJJJMMTT) und der Dateiendung kbl/hcv zusammen. Als 
Trenner ist ein Unterstrich zu verwenden. Bei Masterleitungssätzen entspricht die MBC-Sachnummer 
der Masterleitungssatznummer/ Sachnummer der Tabellenzeichnung. 
 
Beispiel: 
A1665406503_001_20131121.kbl 
A1725408709_002_20100630.kbl 
A2225403908_012_20131203.hcv 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 60 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 60 von 178 
 
8.3 KBL (Kabelbaumliste) 
Die KBL ist ein elektronisches Abbild der Leitungssatzzeichnung und beschreibt Leitungen, 
Verbindungen, Einzelteile, die Leitungssatztopologie mit ihren Abzweigen und Segmenten, die 
theoretischen und die physikalischen Längen der Leitungen sowie die Quelldokumente (Schaltplan 
und 3D-DMU-Modelle), die diesem Leitungssatz zu Grunde liegen.  
Die KBL wird aus dem Entwicklungswerkzeug generiert und muss den Leitungssatz (topologisch und 
elektrisch) auch ohne Zeichnung vollständig dokumentieren sowie die zur Erstellung verwendeten 
Quelldokumente wie Schaltplan- und DMU-Topologie-Daten dokumentieren und referenzieren.  
 
Die KBL muss dem Standard ISO 10303-212 und dem XML Schema der vereinbarten KBL- Version 
entsprechen. Es sind die im Schema vereinbarten Zeichensatzdeklarationen zu verwenden. 
 
Die Leitungssatz KBL-Datei wird zur Leitungssatzdokumentation, Baukasten-Versorgung des MBC 
Stücklistensystem DIALOG sowie für interne Recherchen in der Leitungssatz-Datenbank bei MBC 
verwendet. 
Die Release Zyklen der KBL-Version sind mit MBC abzustimmen. 
8.3.1 Allgemeine Anforderungen 
Da die Wahl der Leitungssatzentwicklungswerkzeuge freigestellt ist, die KBL zur Dokumentation und 
als Eingangsdatensatz für viele interne Prozesse genutzt wird, ist es notwendig, die Strukturen der KBL 
abzustimmen und einige Regeln zu vereinbaren. 
8.3.2 Inhalte und Strukturen 
Die KBL-Struktur mit Harnesscontainer und Modul entspricht der bei MBC verwendeten 
Dokumentationsform Tabellenzeichnung und kann 1:1 verwendet werden. Die Klasse Harness stellt 
den Container für den Inhalt einer Leitungssatzzeichnung dar, in dem die Inhalte eines oder mehrerer 
Leitungssätze dokumentiert werden.  
Die Attribute des Zeichnungskopfes wie Sachnummer, Benennung, Änderungsbeschreibung usw. 
werden in den entsprechenden Attributen der Klasse Harness abgelegt.  
8.3.2.1 Masterleitungssätze 
Bei Masterleitungssätzen werden Sachnummer, Version, Benennung und Änderungsbeschreibung der 
Mastersachnummer (entspricht den Attributen im Zeichnungsschriftkopf einer Masterzeichnung) in 
den Attributen der Klasse Harness abgebildet. Die technischen Inhalte der einzelnen kZ-Teile sowie 
deren Sachnummer, Version, Benennung und Änderungsbeschreibung werden innerhalb der Klasse 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 61 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 61 von 178 
 
Harness als Modul angelegt. 
Die im Zeichnungskopf unter dem aktuellen ZGS (Zeichnungs- und Geometriestand) eingetragenen 
Status oder Änderungsbeschreibungen der Zeichnung werden in der Klasse Change im Harness 
Container eingetragen. 
Status oder Änderungsbeschreibungen der in der Tabellenzeichnung enthaltenen kZ-Tabelle werden in 
der Klasse Change im jeweiligen Modul eingetragen. 
8.3.2.2 Einzelleitungssätze 
Bei 
Einzelleitungssätzen 
werden 
ebenso 
Sachnummer, 
Version, 
Benennung 
und 
Änderungsbeschreibung des Zeichnungskopfes in den Attributen der Klasse Harness abgebildet. Die 
technischen Inhalte des Leitungssatzes sowie deren Sachnummer, Version, Benennung und 
Änderungsbeschreibung werden innerhalb der Klasse Harness als Modul angelegt. 
 
Sachnummer und Benennung der kZ- Tabelle werden in den Attributen der Klasse Module angelegt. 
Für jeden Leitungssatz wird ein Modul angelegt, welches die technischen Inhalte des Leitungssatzes 
dokumentiert.  
8.3.3 Festlegungen 
8.3.3.1 Maße und Maßeinheiten 
Die Klasse Maßeinheiten/ Units enthält die Festlegung aller in der KBL verwendeten Maßeinheiten.  
Verwendung in KBL 
Maßerklärung 
Maßeinheiten 
Leitungen (General Wire) (1 
Gramm/ Meter 
[g/m] 
Länge 
[mm] 
Segment (1 
Länge 
[mm] 
Parts (Accessory, Cavity Plug, Cavity Seal, Component, 
Component Box, Connector Housing, Fixing, General Terminal) (1 
Gramm 
[g] 
Wire Protections (Schläuche) 
Gramm/ Meter 
[g/m] 
Wire Protections (Bandagierungen) 
Gramm/ Fläche 
[g/m²] 
Module/Harness/Harness Configuration (2 
Kilogramm 
[kg] 
(1 ohne Kommastellen 
(2 mit maximal drei Nachkommastellen 
Diese Festlegungen müssen im jeweiligen Entwicklungswerkzeug eingestellt werden. 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28
Festlegungen


### 第 62 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 62 von 178 
 
******* Zuordnung der Teileklassen 
Diese Gegenüberstellung soll eine grobe Zuordnung der Einzelteile aus der CONNECT Datenbank zu 
den einzelnen Klassen im KBL Container ermöglichen, kann aber im Einzelfall je nach Art und Einsatz 
des Teiles abweichen. 
 
CONNECT 
Teileklassen 
KBL-Klassen 
Bandagierungen 
Umwicklungen  Kabelschutz/ Wire_protection 
Blindstopfen 
Blindstopfen/ Cavity Plug 
Clip 
Befestigungselement/ Fixing 
Einzelleitung 
Leitungen/ General Wire 
Einzelteil/ Zusatzteil 
Zusatzteile/ Accessory 
Einzeladerdichtung (Ela) 
Einzeladerdichtung/ Cavity Seal 
Elektr. Komponenten 
Komponenten/ Component 
Halter 
Befestigungselement/ Fixing 
Kabelband 
Zusatzteile/ Accessory oder Befestigungselement/ Fixing 
Kabelkanal 
Befestigungselement/ Fixing 
Kabelschuh 
Kontaktgehäuse/ Connector Housing 
Kontakt  
Kontakte/ General Terminal 
Kontaktgehäuse 
Kontaktgehäuse/ Connector Housing 
Normteil 
Zusatzteile/ Accessory 
Potentialverteiler 
Komponentenbox/ Component Box 
Schelle 
Befestigungselement/ Fixing 
Schlauch 
Kabelschutz/ Wire Protection 
Sicherung 
Komponenten/ Component 
Sicherungsbox/-dose 
Komponentenbox/ Component Box 
Sonstiges 
Zusatzteile/ Accessory 
Sonderleitung 
Leitungen/ General Wire 
Steuergerät 
Komponentenbox/ Component Box 
Tülle 
Befestigungselement/ Fixing 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 63 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 63 von 178 
 
KBL Klassen ohne CONNECT Teilegruppen 
KBL-Klassen 
Beschreibung und Zuordnung 
Zusatzteile/ Accessory 
Butyl-Terostat 
Etiketten 
Farbband 
Markierer 
PUR-Schaumschlauch 
Komponenten/ Component 
Relais 
Kontaktgehäuse/ Connector Housing 
Splices 
8.3.3.3 Namenskonventionen für technische Ids ("Klein-id") in den Occurrence-Klassen  
Die Namensgebung der Klassen und Attribute ist generell im KBL- Schema vorgegeben. Allein die 
Namensgebung der technischen Ids ("id") der einzelnen Klassen ist nicht definiert und kann im 
Allgemeinen frei gewählt werden. Da System im Umfeld der KBL entwickelt wurden und in 
unterschiedlicher Weise auf diese technischen Ids ("id") zugreifen ist die Namensgebung für einige 
Bereiche reglementiert. 
 
ID Präfix 
Objekt Typ 
ID_ACC 
Accessory_occurrence 
ID_BNJ 
Node 
ID_BNS 
Segment 
ID_CBO 
Component_box_occurrence 
ID_CNF 
Harness_configuration 
ID_COM 
Component_occurrence 
ID_CON 
Component_box_connector_occurrence 
Connector_occurrence 
ID_FAM 
Module_family 
ID_FIX 
Fixing_occurrence 
ID_FUS 
Fuse_occurrence 
ID_MCNF 
Module_configuration 
ID_MOD 
Module 
ID_MUL 
Special_wire_occurrence 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 64 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 64 von 178 
 
ID_PLU 
Cavity_plug_occurrence 
ID_SEA 
Cavity_seal_occurrence 
ID_TAP 
Wire_protection_occurrence 
ID_TER 
Terminal_occurrence 
ID_WIR 
wire_occurrence 
core_occurrence 
ID_WRG 
Wiring_group 
 
8.3.3.4 Externe Referenzen 
Ist eine Liste aller Referenzen auf externe Dokumente zu den im Harness bzw. den Modulen 
verwendeten Einzelteilen oder den zur Erstellung verwendeten Quelldokumenten wie Schaltplan und 
DMU- Topologie Daten.  
Die Art der Dokumente wird über das Attribut Document_type unterschieden. Die Referenzen sind 
über die id eindeutig und werden aus der Verwendung heraus referenziert.  
Für die Dokumententypen „Circuit Wiring“ und „Wiring Construction Unit“ gilt zusätzlich: 
Bei Masterleitungssätzen müssen Referenzen auf Schaltpläne bzw. DMU-Modelle, die von den 
Modulen des Masterleitungssatz referenziert werden, in den External References auf Ebene Harness 
referenziert werden. 
Weitere Details sind im Kapitel 8.3.4.13 zu finden. 
8.3.3.5 Datenstand 
Unabhängig von Versionskennzeichen diverser am Prozess beteiligter Systeme wird in den 
Leitungssatzzeichnungen und Schaltplänen zusätzlich ein Merkmal Datenstand angelegt und 
dokumentiert. 
Das Merkmal wird bei der Bearbeitung automatisch angelegt und in einem eigenen Feld im 
Zeichnungskopf eingetragen. Es beinhaltet das Datum im erweiterten Format nach DIN ISO 8601 und 
soll die Zuordnung der Zeichnung mit abgeleiteten Dokumenten ermöglichen. Bei Änderung eines 
Leitungssatzmoduls und/oder des Masterleitungssatzes ist der Datenstand anzupassen. Ergeben sich 
an einzelnen Modulen eines Masterleitungssatz keine Änderungen, so ist der Datenstand 
beizubehalten. 
 
Beispiel: 2013-12-01 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 65 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 65 von 178 
 
8.3.3.6 MBC-Sachnummer 
MBC Sachnummern sind generell in das Attribut <Part_number>, die Lieferantensachnummern in das 
Attribut <Alias_id> einzutragen.  
 
Sachnummer nach MBN 
  
 
 
A 222 540 93 00 
Schreibweise der Sachnummer   
 
 
AXXXXXXXXXX 
Schreibweise der <Predecessor_part_number>:  
AXXXXXXXXXX 
Alternative Schreibweise Sachnummer  
 
A XXX XXX XX XX 
 
Die alternative Schreibweise für Sachnummern ist nur im Attribut <Part_number> der Klassen Harness 
und Modul erlaubt. 
 
Schaltplansachnummern: 
Die Schaltplansachnummer setzt sich aus der <MBC Sachnummer> ohne Leerzeichen/Sonderzeichen 
und der Blattnummer zusammen. Als Trenner ist ein Unterstrich zu verwenden. 
 
Schaltplansachnummer = <MBC Sachnummer>_<Blattnummer> 
8.3.3.7 Benennung von Sachnummern 
Grundlage für die Benennung von Sachnummern ist die Arbeitsanweisung A 059 8031. Die Benennung 
einer Sachnummer aus SRM ist exakt so in der KBL-Datei wieder zu verwenden. 
******* MBC-Positionsnummern 
Positionsnummern sind Besonderheiten von MBC-Teilen und können der CONNECT-Teilebibliothek 
entnommen werden. In der KBL-Teilebibliothek (z.B. Connector_housing) werden diese jeweils zu 
einer Sachnummer im Attribut <Abbreviation> beschrieben. 
 
 
Abbildung 40: Auszug Connector_housing 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 66 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 66 von 178 
 
8.3.3.9 Leitungslängen 
Um Berechnungen von Widerstand, Kupfergewicht und anderer elektrischer Werte exakt durchführen 
zu können sind die berechneten oder gemessenen Leitungslängen in der Klasse <Wire_length> 
anzugeben.  
Werte für <Length_type> 
Beschreibung 
DMU length 
Länge aus dem DMU (z.B. Siemens NX; DMU length virtuell) 
Drawing length 
gerundete Länge aus dem DMU ohne Zuschläge 
Terminal length 
berechnete Länge mit berechnetem Stecker-Zuschlag bis Kontakt 
Production length 
berechnete 
Länge 
mit 
berechnetem 
Stecker-Zuschlag 
und 
berechnetem Produktionszuschlag 
Supplement 
Produktionszuschlag für aktuelle Verbindung 
 
Bei Freigabe von fertigen Produkten ist die Production length in berechneter oder gemessener Form 
anzugeben. 
 
Abbildung 41: Darstellung Längentypen 
8.3.3.10 Wickelrückbindung 
Segmente mit Isolierungsschutz, die den Kabelsatz in einem Segment nicht vollbewickeln/ voll 
schützen, werden in der KBL auch als „Wickelrückbindung“ bezeichnet. Die Informationen zur 
Wickelrückbindung werden in der KBL im Bereich Ausbindung/ Segment in der Protection_area 
dokumentiert (Kapitel Ausbindungen/ Segment). Dabei werden der Start- und der Endpunkt (Anfang 
und Ende der Wicklung) in Prozenten [%] zur Leitungslänge (Physical length) angegeben. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 67 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 67 von 178 
 
Hinweis: immer vom Startpunkt ausgehend wird die prozentuale Berechnung ausgeführt; dargestellt 
an einem Beispiel: 
 
 
Abbildung 42: Wickelrückbindung 
Fall1: Segmentlänge: 240 mm/ Protection length: 210 mm 
Startpunkt/ Start_location (1): 
0 % 
Eintrag in Protection_area:  
0 
Endpunkt/ End_location (2):  
88 %  
Eintrag in Protection_area:  
0.875 
 
Fall2: Segmentlänge: 240 mm/ Protection length: 210 mm 
Startpunkt/ Start_location (1): 
12 % 
Eintrag in Protection_area:  
0.125 
Endpunkt/ End_location (2):  
100 %  
Eintrag in Protection_area:  
1 
 
Da die Bewicklung/ Isolierungsschutz vom Stecker ausgehend erst bei 30 mm beginnt, muss beim 
Startpunkt ein größerer Wert als 0 % eingeben werden. 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 68 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 68 von 178 
 
8.3.3.11 Modellierung einseitig angeschlagener Leitungen 
Damit eine einseitig angeschlagene Leitung in der KBL dokumentiert werden kann (eine Connection 
muss immer mit Start- und Endpunkt versehen sein), müssen folgende Änderungen/ Eintragungen bei 
der Klasse Kontaktgehäuse/ Connector_housing und der Klasse Connector_occurrence durchgeführt 
werden: 
Connector_occurrence: 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
Bauteilkurzbezeichnung 
(REF) 
aus VZK 
Beschreibung 
Description 
P 
Schirmende 
Lokale Beschreibung 
Localized_string 
P 
end of shield 
Gebrauch 
Usage 
P 
no end 
Beispiel: eine geschirmte Leitung ist am Startpunkt mit Schirm und Leitung am Kontaktgehäuse 
angeschlagen; beim Endpunkt ist nur die Leitung angeschlagen (der Schirm ist nicht angeschlagen) 
 
Kontaktgehäuse/ Connector_housing: 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
LTG_END_GEDICHTET 
/ 
LTG_END_UNGEDICHTET 
Beschreibung 
Description 
P 
offenes Leitungsende 
Lokale Beschreibung 
Localized_string 
O 
open end 
Masseninformation 
Mass_information 
P 
0 g 
Steckertyp 
Housing_type 
P 
no end 
Anzahl Pins 
Number_of_cavities 
P 
1 
******** DS/ DZ Kennzeichung und ESD Kenner 
 DS/ DZ Kennzeichung 
********.1
Die Dokumentation Sicherheit (DS) und die Dokumentation Zertifizierung (DZ) sind in der KBL, sowie 
auf den Leitungssatzzeichnungen zu ergänzen. 
Die Beschreibung der Merkmalserläuterung muss dem aktuellen Merkmalskatalog entsprechen. Der 
aktuelle Merkmalskatalog ist entweder in SMARAGD/ Engineering Portal zu entnehmen. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 69 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 69 von 178 
 
Details zu der Kennzeichnung von besonderen Merkmalen ist aus der MBN 10 317 „Kennzeichnung 
von Merkmalen zur besonderen Nachweisführung“ zu entnehmen (Kapitel 2.3.6). 
Abbildung der DS/ DZ Kennzeichung in der KBL, siehe Details in Kapitel 8.3.6.1 Attribute der Module. 
 ESD Kenner 
********.2
Die Entwicklung legt die Empfindlichkeit des Steuergeräts, Bauteils, ZB‘s oder LU’s gegen 
elektrostatische Entladungen und damit seine ESD-Relevanz fest. Diese Festlegung wird durch das 
Kennzeichen, im folgenden ESD-Kenner (electrostatic discharge) genannt, sowohl im System Smaragd 
und auch auf der Zeichnung (Abbildung 6), damit auch in der mitführenden KBL verankert, 
dokumentiert. Die Modellierung des ESD-Kenners, wird dabei über Processing_Instructions nach 
folgendem definierten Schema geschehen (Kapitel Attribute der Module): 
Klasse Module: Type="ESD" Value="J"/ "N" 
J 
Steuergerät/Bauteil/ ZB ist empfindlich gegen elektrostatische Entladung  
N 
Steuergerät/Bauteil/ ZB ist nicht empfindlich gegen elektrostatische Entladung  
Details zur Dokumentation des ESD-Kenners ist der A 059 80 30 zu entnehmen. 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 70 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 70 von 178 
 
8.3.3.13 Darstellung der Kontaktierungsarten 
Bei den Kontaktierungsarten werden folgende Ausführungen unterschieden und dokumentiert: 
Standardkontakt/ Doppelanschlag/ Brückenkontakt und Koax-Terminal. 
 
 Standardkontakt 
8.3.3.13.1
Beim Standardkontakt handelt es sich um einen Einzelkontakt einer Leitung (mit oder ohne einer EAD: 
Einzeladerabdichtung) auf einen Kontaktpunkt/ Steckpunkt (Cavity) in einem Steckergehäuse/ 
Kammer eines Steckers (Terminal). 
 
 
Abbildung 43: Kontaktierungsart Standardkontakt 
In der Abbildung 43 ist die grundsätzliche Dokumentation in der KBL beispielhaft dargestellt. 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 71 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 71 von 178 
 
 Doppelanschlag 
8.3.3.13.2
Beim Doppelanschlag, auch Mehrfachkontakt genannt, handelt es sich um einen Einzelkontakt mit 
zwei zugewiesenen bzw. zusammengecrimpten Leitung (nur ohne EAD möglich) auf einen 
Kontaktpunkt/ Steckpunkt (Cavity) in einem Steckergehäuse/ Kammer eines Steckers (Terminal). 
 
 
Abbildung 44: Kontaktierungsart Doppelanschlag 
 
In der Abbildung 44 ist die grundsätzliche Dokumentation in der KBL beispielhaft dargestellt. 
In der KBL werden damit zwei einzelne Leitungskontakte auf einen Kontaktpunkt/ Steckpunkt (Cavity) 
in einem Steckergehäuse/ Kammer eines Steckers dokumentiert. 
 
 
 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 72 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 72 von 178 
 
 Brückenkontakt 
8.3.3.13.3
Beim Brückenkontakt, handelt es sich um mindestens einen Zweierkontakt mit einer zugewiesenen 
Leitung (nur ohne EAD möglich) auf zwei Kontaktpunkt/ Steckpunkt (Cavity) in einem 
Steckergehäuse/ Kammer eines Steckers (Terminal). 
 
 
 
Abbildung 45: Kontaktierungsart Brückenkontakt 
In der Abbildung 45 ist die grundsätzliche Dokumentation in der KBL beispielhaft dargestellt. 
In der KBL wird damit ein einzelner Leitungskontakt auf zwei Kontaktpunkte / Steckpunkte (Cavity) in 
einem Steckergehäuse/ Kammer eines Steckers dokumentiert. 
 
 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 73 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 73 von 178 
 
 Koax-Terminal 
8.3.3.13.4
Beim Koax-Terminal handelt es sich um einen Anschlag eines Sonderkabels, bestehend aus einer 
Leitung mit einer Schirmung der Leitung. Für diese Sonderleitung werden zwei zugewiesene Leitungen 
auf zwei Kontaktpunkte/ Steckpunkte (Cavity) in einem Steckergehäuse/ Kammer eines Steckers 
zugewiesen. 
 
 
Abbildung 46: Kontaktierungsart Koax-Terminal 
In der Abbildung 46 ist die grundsätzliche Dokumentation in der KBL beispielhaft dargestellt. 
In der KBL wird damit eine Sonderleitung mit zwei Einzelleitungen auf zwei Kontaktpunkte/ 
Steckpunkte (Cavity) in einem Steckergehäuse/ Kammer eines Steckers dokumentiert. 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 74 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 74 von 178 
 
 Stecker mit mehreren Eintrittspunkten (HV-Leitungssatz) 
8.3.3.13.5
Beim HV-Leitungssatz gibt es Stecker mit aufgeschweißten Pins, da die HV-Leitungen nur 
Steckerkupplungen haben dürfen (Beispiel siehe Abbildung). Ltg. 1 und 3 müssen, aufgrund der 
Montage der HV-Leitungen, eine andere Segmentlänge aufweisen als die Ltg. 2. Damit in der KBL eine 
eindeutige Zuordnung von Leitungen, Segmenten und deren Länge möglich ist, werden Knoten 
erzeugt, die neben der Referenced_component zur Connector_occurrence zusätzlich via 
Referenced_cavity auf die jeweilige Cavity zeigen und somit dieselbe Stecker-REF verwenden. 
 
Abbildung 47: HV-Leitung mit Stecker Adapterplatte 
Abbildung in KBL:  
 
Abbildung 48: Abbildung HV-Leitung in KBL 
Neu mit KBL 2.4 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 75 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 75 von 178 
 
8.3.3.14 Darstellung von Textbändern 
Textbänder sind als Zusatzteil (Accessory) am Segment zu modellieren. Der zu beschreibende Text 
wird als „Id“ in der Accessory_occurrence verwaltet. Alle Textbänder müssen sich auf dasselbe Part  
mit der Part_number „TEXTBAND“ und dem Accessory_type „TEXTBAND“ beziehen (siehe Abbildung 
49). 
 
Abbildung 49: Darstellung von Textbändern in der KBL 
8.3.3.15 Darstellung von KSL-Steckern 
Für den Fall, dass  
 
an einem Steuergerät/Komponente elektrisch identische Kontaktgehäuse verwendet werden, 
die sich lediglich in ihrer Codierung auf Grund einer Varianz des Steuergerätes/Komponente 
unterscheiden,   
 
keine Duplizierung der Leitungen im Schaltplan erfolgen soll und  
 
diese einzelnen Kontaktgehäuse in separaten sogenannten Gehäusemodulen (Module, welche 
nur dieses Kontaktgehäuse als Teil besitzen) gesteuert werden, 
werden diese Kontaktgehäuse als KSL-Stecker abgebildet (Beispiel siehe Abbildung 50).  
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 76 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 76 von 178 
 
 
Abbildung 50: Beispiel eines KSL-Steckers 
Die Referenz der Stecker muss eindeutig sein. Daher wird ein Dummy-Stecker mit der korrekten 
Referenz des Schaltplans dargestellt. Dieser Dummy-Stecker trägt als Teilenummer das Schlüsselwort 
„DUMMY_KSL_CONNECTOR_<NUM_PINS>“. 
Die 
anderen 
Kontaktgehäuse 
werden 
als 
Connector_occurrence modelliert, sitzen jedoch nicht auf dem Topologieknoten (sind nicht an das 
Segment angeschlossen), sondern auf einem separaten Knoten abseits der Topologie, und tragen den 
Dummy-Stecker als Referenzelement (siehe Abbildung 51: Darstellung des KSL-Connector). Diese 
Connector_occurrences besitzen daher keine Contact_points. Ihre Referenz (Id) ist die Referenz des 
Referenzsteckers ergänzt um den Suffix „_GM<SEQUENCE_NUMBER>“. 
 
Abbildung 51: Darstellung des KSL-Connector 
Diese Abbildung sollte die Ausnahme sein und darf nur nach Rücksprache mit dem zuständigen BTV 
erfolgen. 
8.3.3.16 Schläuche 
Schläuche werden mit der Teilenummer des maximal möglichen Außendurchmessers dokumentiert 
(der gleichzeitig möglichst enganliegend ist).  
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 77 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 77 von 178 
 
 Enganliegende Schläuche in Bereichen variablen Durchmessers 
8.3.3.16.1
In Bündeln, bei denen auf Grund unterschiedlicher Modulzuordnungen der enthaltenen Leitungen der 
Durchmesser des Bündels variiert, muss der Außendurchmesser des Schlauches variieren. 
Entsprechend sind davon abweichende Schläuche derselben Schlauchfamilie zu verwenden. Dies ist 
zusätzlich als Hinweis auf der Zeichnung zu dokumentieren. 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 78 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 78 von 178 
 
8.3.4 KBL Container 
Der KBL Container enthält neben den Klassen Harness, Units, Referenzen, Routingtabellen und den 
Bibliotheken mit Leitungen und Einzelteilen das Attribut versions_id mit der Angabe zur KBL- Version.  
 
Abbildung 52: Auszug KBL Container (basierend KBL 2.4 SR1) 
Die auf der Zeichnung verwendeten Einzelteile wie Stecker, Leitungen usw. werden mit ihren 
technischen Merkmalen in Form einer Bibliothek einmalig in eigens vorgesehenen Klassen im 
KBL_Container angelegt und bei Verwendung in einem der Leitungssatzmodule von dort referenziert.  
In den folgenden Kapiteln werden diese Klassen näher beschrieben. Der Harness-Container wird in 
Kapitel 8.3.5 beschrieben. 
8.3.4.1 KBL Version 
Um die Kompatibilität zu den verwendeten Werkzeugen sicher zu stellen ist die zum Zeitpunkt des 
Erscheinens dieser Ausführungsvorschrift gültige KBL- Version V2.4SR-1 zu verwenden. Die Release 
Zyklen weiterer KBL- Version sind zwingend mit MBC abzustimmen. 
Für die Abbildung der Inhalte sind die im KBL- Schema vereinbarten Zeichensatzdeklarationen zu 
verwenden. 
Das Attribut Version_ID dient anderen Systemen dazu die richtige Version der KBLs zu identifizieren 
und notwendige Anpassungen z.B. beim Import in eine Datenbank zu steuern.  
Zugelassene Werte für das Attribut Version_ID: 
V2.2 
 
oder 
2.2 
V2.3 
 
oder 
2.3  
 
V2.3SR-1  
oder  
2.3 SR-1 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 79 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 79 von 178 
 
V2.4 SR 1  
oder  
2.4 SR-1 
Beispiel: V2.4SR-1 ist die vom VDA vorgeschriebene Schreibweise für KBLs nach Schema 2.4 SR-1 
 
 
Abbildung 53: Auszug KBL-Container (basierend KBL 2.4 SR1) 
8.3.4.2 Zusatzteile/ Accessory 
Diese Klasse enthält alle Einzel/ Zusatzteile der CONNECT Teilegruppen Sonstiges und die nicht zur 
Befestigung benötigt werden und nicht direkt einem Leitungsanschluss am Stecker zugeordnet 
werden können. Außerdem Teile wie Butyl-Terostat, Etiketten, Farbbänder, Markierer, Kabelband, 
Normteil, PUR-Schaumschlauch. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
<MBC Sachnummer> oder < 
Normteilsachnummer> 
Lieferantenname 
Company_name 
P 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
 
Lieferantensach-
nummer 
 
Anwendungsbereich 
 
Beschreibung 
 
Lokale Beschreibung 
Alias_id 
O 
 
Alias_id 
O 
Lieferantenteilenummer 
 
Scope 
 
O 
Lieferantenbezeichnung 
Description 
O 
 
Localized_string 
O 
 
Version 
Version 
O 
Teileversion 
bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
P 
<MBC 
Positionsnummer>, 
siehe Kapitel ******* 
Beschreibung 
Description 
P 
Teilebenennung 
Lokale Beschreibung 
 
Sprachencode 
 
Wert 
Localized_string 
P 
Teilebenennung auf Englisch 
Language_code 
P 
 
Value 
P 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 80 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 80 von 178 
 
Vorgängerteilenummer 
Predecessor_part_nu
mber 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Masseninformation 
 
Einheitskomponente 
 
Wertekomponente 
Mass_information 
P 
Masseneinheit und Wert 
Unit_component 
P 
Masseneinheit [g] 
Value_component 
P 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die Bibliothek in 
Klasse Externe Referenzen 
„sofern vorhanden“ 
Materialinformationen 
 
Materialschlüssel 
Material 
O 
 
Material_key 
O 
 
Zusatzteile-Typ 
Accessory_type 
P 
CONNECT-Klasse 
des 
Bauteils 
Prozessinformationen 
 
Instruktionstyp 
 
Instruktionswert 
Processing_instruction 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
 
 
Abbildung 54: Auszug Zusatzteile Pflicht/optionale Attribute (basierend KBL 2.4) 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 81 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 81 von 178 
 
8.3.4.3 Kartesische Koordinate/ Cartesian_point  
Diese Klasse enthält die 2D/3D Koordinaten für Knoten und Komponenten des Leitungssatzes. 
Alle Koordinaten sind bezogen auf ein rechtsdrehendes Koordinatensystem anzugeben. Die 
Koordinaten folgen dabei dem Maßstab 1.0 = 1 mm. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Koordinate 
 
Koordinate 
Koordinate 
Koordinate 
Coordinates 
P 
Liste der Koordinaten in 
der Reihenfolge XYZ 
Coordinates 
P 
Reihenfolge X 
Coordinates 
P 
Reihenfolge Y 
Coordinates 
P 
Reihenfolge Z 
 
 
Abbildung 55: Auszug Koordinate/ Cartesian_point 
 
******* Blindstopfen/ Cavity Plug 
Diese Klasse enthält alle Teile der CONNECT Teilegruppen Blindstopfen, die direkt einer Kammer 
zugeordneten werden können. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
<MBC Sachnummer> 
Lieferantenname 
Company_name 
P 
Lieferantenbezeichnung 
aus CONNECT Teilekatalog 
Alias ID 
 
Lieferantensachnummer 
Alias_id 
O 
 
Alias_id 
O 
Lieferantenteilenummer 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 82 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 82 von 178 
 
 
Anwendungsbereich 
 
Beschreibung 
 
Lokale Beschreibung 
Scope 
O 
Lieferantenbezeichnung 
Description 
O 
 
Localized_string 
O 
 
Version 
Version 
O 
Teileversion 
bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
P 
<MBC Positionsnummer>, 
siehe Kapitel ******* 
Beschreibung 
Description 
P 
Teilebenennung 
Lokale Beschreibung 
 
 
Sprachencode 
 
Wert 
Localized_string 
P 
Teilebenennung 
auf 
Englisch 
Language_code 
P 
 
Value 
P 
 
Vorgängerteilenummer 
Predecessor_part_ 
number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Masseninformation 
 
Einheitskomponente 
 
Wertekomponente 
Mass_information 
P 
Masseneinheit und Wert 
Unit_component 
P 
Masseneinheit [g] 
Value_component 
P 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die Bibliothek 
in Klasse Externe 
Referenzen 
„sofern 
vorhanden“ 
Materialinformationen 
 
Materialschlüssel 
Material 
O 
 
Material_key 
O 
 
Prozessinformationen 
 
Instruktionstyp 
 
Instruktionswert 
Processing_instruction 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
Farbe 
Colour 
P 
Farbe des Blindstopfens 
 
 
Abbildung 56: Auszug Cavity Plug 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 83 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 83 von 178 
 
******* Einzeladerdichtung/ Cavity Seal 
Diese Klasse enthält alle Teile der CONNECT Teilegruppen ELA (Einzeladerabdichtung), die direkt 
einem Leitungsanschluss zugeordnet werden können. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
<MBC Sachnummer> 
Lieferantenname 
Company_name 
P 
Lieferantenbezeichnung 
aus CONNECT Teilekatalog 
Alias ID 
 
Lieferantensachnummer 
 
Anwendungsbereich 
 
Beschreibung 
 
Lokale Beschreibung 
Alias_id 
O 
 
Alias_id 
O 
Lieferantenteilenummer 
Scope 
O 
Lieferantenbezeichnung 
Description 
O 
 
Localized_string 
O 
 
Version 
Version 
O 
Teileversion 
bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
P 
<MBC Positionsnummer>, 
siehe Kapitel ******* 
Beschreibung 
Description 
P 
Teilebenennung 
Lokale Beschreibung 
 
 
Sprachencode 
 
Wert 
Localized_string 
P 
Teilebenennung 
auf 
Englisch 
Language_code 
P 
 
Value 
P 
 
Vorgängerteilenummer 
Predecessor_part_ 
number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Masseninformation 
 
Einheitskomponente 
 
Wertekomponente 
Mass_information 
P 
Masseneinheit und Wert 
Unit_component 
P 
Masseneinheit [g] 
Value_component 
P 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die Bibliothek 
in Klasse Externe 
Referenzen 
„sofern 
vorhanden“ 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 84 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 84 von 178 
 
Materialinformationen 
 
Materialschlüssel 
Material 
O 
 
Material_key 
O 
 
Prozessinformationen 
 
Instruktionstyp 
 
Instruktionswert 
Processing_instruction 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
Dichtungstyp 
Seal_type 
P 
Dichtungstyp 
aus 
CONNECT 
("gehäusefixiert"/"kontakt
fixiert") 
Farbe 
Colour 
P 
Farbe der ELA 
Kabelgröße 
 
Einheitskomponente 
 
Minimalwert 
 
Maximalwert 
Wire_size 
P 
 
Unit_component 
P 
Einheit "mm" 
Minimum 
P 
Leitungsdurchmesser min 
Maximum 
P 
Leitungsdurchmesser max 
 
Abbildung 57: Auszug Cavity_Seal (basierend KBL 2.4) 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 85 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 85 von 178 
 
******* Änderungen/ Change 
Änderungen am Leitungssatz sind entsprechend den Anforderungen der MBN 31 020-3 in der 
Leitungssatzzeichnung zu erfassen und zu beschreiben. Die Inhalte der änderungsrelevanten Inhalte 
der Leitungssatzzeichnung sind in die Klasse Change (in der Klasse Harness als auch Module) zu 
übernehmen. 
Die änderungsrelevanten Informationen aus der Zeichnung (aus dem Zeichnungsschriftkopf bzw. aus 
der kZ-Tabelle) werden in folgende Attribute der Klasse Change befüllt. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
ZGS 
Beschreibung 
 
Description 
 
P 
Wert 
aus 
dem 
Feld 
Änderungsbeschreibung 
Lokale Beschreibung 
 
 Sprachencode 
 Wert 
Localized_string 
O 
 
Änderungsbeschreibung 
auf 
Englisch 
Language_code 
O 
 
Value 
O 
 
Änderungsmeldung 
Change_request 
P 
KEM-Bezeichnung, Wert aus dem 
Feld Auftr.-Nr./ order no. 
Änderungsdatum 
Change_date 
P 
Wert 
aus 
dem 
Feld 
Änderungsdatum 
verantwortlicher Designer 
Responsible_design
er 
P 
verantwortlicher 
Entwickler 
(Lieferant) 
aus 
der 
Lieferantentabelle 
verantwortliche Abteilung 
Designer_departme
nt 
P 
Abteilung, Firma (Lieferant) aus 
der Lieferantentabelle 
Genehmiger Name 
Approver_name 
P 
Wert aus dem Feld Bearbeit./auth 
Genehmiger Abteilung 
Approver_departme
nt 
P 
Wert 
aus 
dem 
Feld 
federf. 
Abteilung/ resp. dep. 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 86 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 86 von 178 
 
******* Änderungsbeschreibung/ Change Description 
Beinhaltet die Änderungsbeschreibungen aus ConnectCHANGE mit folgenden Attributen. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
 
Änderungsmeldung 
aus 
ConnectCHANGE 
Beschreibung 
Description 
P 
 
Beschreibung 
der 
Änderung 
(Betreff aus ConnectCHANGE) 
Lokale Beschreibung 
 
Sprachencode 
 
Wert 
Localized_string 
O 
 
Language_code 
O 
 
Value 
O 
 
Änderungsmeldung 
Change_request 
P 
Wert 
aus 
dem 
Feld 
"Änderungsgrund" 
in 
ConnectCHANGE 
Änderungsdatum 
Change_date 
P 
Änderungsdatum 
verantwortlicher Designer 
Responsible_ 
designer 
P 
verantwortlicher 
Entwickler 
(Lieferant) 
aus 
der 
Lieferantentabelle 
verantwortliche Abteilung 
Designer_ 
department 
P 
Abteilung, Firma (Lieferant) aus 
der Lieferantentabelle 
Genehmiger Name 
Approver_name 
P 
Wert aus dem Feld Bearbeit./auth 
Genehmiger Abteilung 
Approver_ 
department 
P 
Wert 
aus 
dem 
Feld 
federf. 
Abteilung/ resp. dep. 
Geänderte Elemente 
Changed_elemente 
P 
Verweis auf die im Zuge der 
Änderung geänderten Elemente 
Verwandte Änderungen 
Related_changes 
O 
 
Verweis 
auf 
die 
zugehörigen 
Änderungen der Klasse Change 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 87 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 87 von 178 
 
******* Komponenten/ Component 
Diese Klasse enthält alle Teile der CONNECT Teilegruppen elektrische Komponenten, Sicherungen und 
Relais, die nicht direkt einem Leitungsanschluss zugeordneten werden können. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
<MBC Sachnummer> 
Lieferantenname 
Company_name 
P 
Lieferantenbezeichnung 
aus CONNECT Teilekatalog 
Alias ID 
 
Lieferantensachnummer 
 
Anwendungsbereich 
Alias_id 
O 
 
Alias_id 
O 
Lieferantenteilenummer 
Scope 
O 
Lieferantenbezeichnung 
Version 
Version 
O 
Teileversion 
bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
P 
<MBC Positionsnummer>, 
siehe Kapitel ******* 
Beschreibung 
Description 
P 
Teilebenennung 
Lokale Beschreibung 
 
 
Sprachencode 
 
Wert 
Localized_string 
P 
Teilebenennung 
auf 
Englisch 
Language_code 
P 
 
Value 
P 
 
Vorgängerteilenummer 
Predecessor_part_ 
number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
 
Masseninformation 
 
Einheitskomponente 
 
Wertekomponente 
Mass_information 
P 
Masseneinheit und Wert 
Unit_component 
P 
Masseneinheit [g] 
Value_component 
P 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die Bibliothek 
in Klasse Externe 
Referenzen 
„sofern 
vorhanden“ 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 88 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 88 von 178 
 
Prozessinformationen 
 
 
Instruktionstyp 
 
Instruktionswert 
Processing_ 
instruction 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
 
Speziell für Sicherungen ist die Kindklasse "Fuse" zu benutzen. Diese instanziiert zusätzlich folgende 
Attribute: 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Sicherung (Fuse) 
 
 
Sicherungstyp 
 
Schlüssel 
 
 
 
Referenzsystem 
 
 
 
Nennstrom 
 
 
 
Farbe 
Fuse 
(Kind 
of 
Component) 
P 
 
Fuse_type 
P 
 
Key 
P 
Spalte 
SI_Typ 
in 
Sicherungsbelegungs-
tabelle (MEGA, MIDI, etc.) 
Reference_system 
P 
zugehörige Norm, in der 
der 
Sicherungstyp 
definiert ist 
Nominal_current 
P 
Ist-SI-Wert Sicherung aus 
Sicherungsbelegungs-
tabelle 
Colour 
P 
Farbe der Sicherung laut 
CONNECT Teilekatalog 
 
Abbildung 58: Auszug Komponenten/Component (basierend KBL 2.4) 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 89 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 89 von 178 
 
******* Komponentenbox/ Component Box 
Diese Klasse enthält alle Teile der CONNECT Teilegruppen Sicherungsbox/ -dose und 
Potentialverteiler. 
Bei der Dokumentation von 7-er Teilen ( Dialog Dokumentationsmethode)  ist zu beachten, dass diese 
Komponente zwecks konsistenter Verbindungen in der KBL vorhanden sein müssen, diese aber nicht 
von einem Modul ( Controlled-Components ) referenziert werden , da sie nicht ein Teil der Stückliste 
sind.  
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
<MBC Sachnummer> 
Lieferantenname 
Company_name 
P 
Lieferantenbezeichnung 
aus 
CONNECT Teilekatalog 
Alias ID 
 
Lieferantensachnummer 
 
Anwendungsbereich 
Alias_id 
O 
 
Alias_id 
O 
Lieferantenteilenummer 
Scope 
O 
Lieferantenbezeichnung 
Version 
Version 
O 
Teileversion 
bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
P 
<MBC 
Positionsnummer>, 
siehe Kapitel ******* 
Lokale Beschreibung 
 
Sprachencode 
 
Wert 
Localized_string 
P 
Teilebenennung auf Englisch 
Language_code 
P 
 
Value 
P 
 
Beschreibung 
Description 
P 
Teilebenennung 
Vorgängerteilenummer 
Predecessor_part_
number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Masseninformation 
 
Einheitskomponente 
 
Wertekomponente 
Mass_information 
P 
Masseneinheit und Wert 
Unit_component 
P 
Masseneinheit [g] 
Value_component 
P 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die Bibliothek in 
Klasse Externe 
Referenzen „sofern vorhanden“ 
Prozessinformationen 
 
Processing_ 
instruction 
O 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 90 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 90 von 178 
 
 
Instruktionstyp 
 
Instruktionswert 
Instruction_type 
O 
 
Instruction_value 
O 
 
Komponentenkammern 
 
Id 
 
 
 
Typ 
 
gültige Sicherungstypen 
 
Schlüssel 
 
 
 
Referenzsystem 
 
 
Minimalstrom 
 
Einheitskomponente 
 
Wertekomponente 
 
Maximalstrom 
 
 
Einheitskomponente 
 
 
Wertekomponente 
 
 
Komponentenpins 
 
 
Pinnummer 
Component_slots 
P 
 
Id 
P 
Benennung des Sicherungs-
/Relaissteckplatzes 
(z.B. 
"F125") 
Type 
P 
Auswahl: "fuse" oder "relais" 
Valid_fuse_types 
O 
 
Key 
O 
 
 
Spalte 
SI_Typ 
in 
Sicherungsbelegungstabelle 
(MEGA, MIDI, etc.) 
Reference_system 
O 
 
zugehörige Norm, in der der 
Sicherungstyp definiert ist 
Min_current 
O 
 
Unit_component 
O 
 
Value_component 
O 
 
Max_current 
P 
Max-Si-Wert 
(aus 
Sicherungsbelegungstabelle) 
Unit_component 
P 
Max-Si-Wert 
(aus 
Sicherungsbelegungstabelle) 
Value_component 
P 
Max-Si-Wert 
(aus 
Sicherungsbelegungstabelle) 
Component_ 
cavities 
P 
 
Cavity_number 
P 
 
Verbindungen 
 
 
Id 
 
 
 
Pins 
 
Komponentenkammern 
 
Connections 
P 
(nicht bei Steuergeräten &  
Potentialverteiler) 
Id 
P 
Verbindungsnummer 
von 
Innenverschaltung 
aus 
E3.Cable 
Cavities 
P 
 
Component_ 
cavities 
P 
 
Komponentenbox-Stecker 
 
Component_box_ 
connectors 
P 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 91 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 91 von 178 
 
 
Id 
 
 
kompatible 
Kontaktgehäuse 
 
integrierte Kammern 
Id 
P 
Nummer des Steckplatzes (z.B. 
"1/S") 
Compatible_ 
housings 
O 
 
Integrated_slots 
P 
 
******** Kontaktgehäuse/ Connector Housing 
Diese Klasse enthält alle Teile der CONNECT Teilegruppen Kontaktgehäuse, Kabelschuh sowie Splices, 
die direkt einem Leitungsanschluss zugeordneten werden können. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
<MBC Sachnummer> 
Lieferantenname 
Company_name 
P 
Lieferantenbezeichnung 
aus 
CONNECT Teilekatalog 
Alias ID 
 
Lieferantensachnummer 
 
Anwendungsbereich 
 
Beschreibung 
 
Lokale Beschreibung 
Alias_id 
O 
 
Alias_id 
O 
Lieferantenteilenummer 
Scope 
O 
Lieferantenbezeichnung 
Description 
O 
 
Localized_string 
O 
 
Version 
Version 
O 
Teileversion 
bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
P 
<MBC 
Positionsnummer>, 
siehe Kapitel ******* 
Beschreibung 
Description 
P 
Teilebenennung 
Lokale Beschreibung 
 
Sprachencode 
 
Wert 
Localized_string 
P 
Teilebenennung auf Englisch 
Language_code 
P 
 
Value 
P 
 
Vorgängerteilenummer 
Predecessor_part_
number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Masseninformation 
 
Einheitskomponente 
 
Wertekomponente 
Mass_information 
P 
Masseneinheit und Wert 
Unit_component 
P 
Masseneinheit [g] 
Value_component 
P 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die Bibliothek in 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 92 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 92 von 178 
 
Klasse Externe 
Referenzen „sofern vorhanden“ 
Materialinformationen 
 
Materialschlüssel 
Material 
O 
 
Material_key 
O 
 
Prozessinformationen 
 
 
Instruktionstyp 
 
Instruktionswert 
Processing_instruct
ion 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
Steckerfarbe 
Housing_colour 
P 
Gehäusefarbe 
Steckercode 
Housing_code 
P 
Codierung 
Steckertyp 
Housing_type 
P 
"splice" 
für 
Splice, 
"ring 
terminal" für Kabelschuhe, „no 
end“ für Routingpoints und Z-
Punkte, 
leer 
für 
Kontaktgehäuse 
Kammern 
 
Id 
 
Anzahl Pins 
 
 
Pins 
 
Pin-Nummer 
Slots 
P 
 
Id 
P 
 
Number_of_ 
cavities 
P 
 
Cavities 
P 
 
Cavity_number 
P 
 
 
 
Abbildung 59: Auszug Kontaktgehäuse/ Connector_housing 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 93 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 93 von 178 
 
******** Dimensionen/ Dimension Specification 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
O 
 
Dimensionswert 
Dimension_value 
O 
 
Segmente 
Segments 
O 
 
Toleranzangabe 
 
Unteres Limit 
 
Oberes Limit 
Tolerance_indication 
O 
 
Lower_limit 
O 
 
Upper_limit 
O 
 
Ursprung 
 
Origin 
 
O 
Referenzelemente 
der 
Bemaßung 
Ziel 
Target 
O 
Zielelement der Bemaßung 
Prozessinformationen 
 
 
Instruktionstyp 
 
Instruktionswert 
Processing_ 
instruction 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
******** Standard Dimensionen/ Default Dimension Specification 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Dimensionswerteskala 
 
Dimension_value_ 
range 
P 
 
 
Toleranztyp 
Tolerance_type 
P 
 
Toleranzangabe 
 
Unteres Limit 
 
Oberes Limit 
Tolerance_indication 
P 
 
Lower_limit 
P 
 
Upper_limit 
P 
 
Externe Referenzen 
External_references 
P 
 
 
Die Standardtoleranzen für Segmentlänge und den Abstand von Befestigungselementen (siehe Kapitel 
5.11) sind entsprechend zu dokumentieren (Beispieldokumentation siehe Abbildung 60).  
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 94 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 94 von 178 
 
 
Abbildung 60: KBL-Abbildung von Standardtoleranzen 
Als Tolerance_type sind nur die Begriffe aus nachfolgender Tabelle erlaubt: 
Werte für <Tolerance_type> 
Beschreibung 
Segment length 
Standardtoleranz für die Segmentlänge 
Fixing distance 
Standardtoleranz für den Abstand von Befestigungselementen 
8.3.4.13 externe Referenzen/ External Reference 
Diese Klasse enthält eine Liste aller Referenzen auf externe Dokumente zu den im Harness 
verwendeten Einzelteilen oder den zur Erstellung verwendeten Quelldokumenten wie Schaltplan und 
DMU-Topologiedaten. 
Folgende Attribute sind erforderlich: 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Dokumententyp 
Document_type 
P 
siehe Folgetabelle 
Dokumentennummer 
Document_number 
P 
<MBC 
Sachnummer>,  
<Schaltplansachnummer>, 
<MBC 
DMU-Sachnummer>, <MBN …> 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 95 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 95 von 178 
 
Change Level 
Change_level 
P 
"JJJJ-MM-TT" bzw. <Smaragdversion> 
Dateiname 
File_name 
P 
Benennung der Dokumenten-SNR 
Datenformat 
Data_format 
P 
 
Quellensystem 
Creating_system 
P 
"CONNECT", 
"e3s", 
"Catia 
V5", 
"Siemens NX" 
 
Innerhalb des Attributes Document_type werden folgende Schlüsselwörter eingesetzt: 
Werte für <Document_type> 
Beschreibung 
<Drawing> 
Externe Referenz auf Hinweiszeichnungen 
Bei kZ- siehe-Teilen  wird auf Teileebene in der Bibliothek 
referenziert. 
<Circuit Wiring> 
Externe Referenz auf Schaltpläne  
Die id wird in der Klasse External-References auf Ebene Harness 
referenziert, sofern der Schaltplan für alle Module relevant ist, 
ansonsten wird die id in der Klasse External-References auf Ebene 
Modul referenziert. 
<Wiring Construction Unit> 
Externe Referenz auf DMU-KBL Datensätze 
Die id wird in der Klasse External-References auf Ebene Harness 
referenziert, sofern das DMU-Modell für alle Module relevant ist, 
ansonsten wird die id in der Klasse External-References auf Ebene 
Modul referenziert. 
<Deviation Table> 
Externe Referenz auf die Abweichtabelle  
für die standortspezifische Teileverwendung wird auf Ebene 
Harness referenziert 
 
Abbildung 61. Auszug externe Referenzen (basierend KBL 2.4) 
******** Befestigungsteile/ Fixings 
Diese Klasse enthält folgende CONNECT Teilegruppen Halter, Clip, Kabelkänale, Tüllen und Schellen, 
die zur Befestigung des Leitungssatzes benötigt werden und nicht direkt einem Leitungsanschluss am 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 96 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 96 von 178 
 
Stecker zugeordnet werden können. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
<MBC Sachnummer> 
Lieferantenname 
Company_name 
P 
Lieferantenbezeichnung 
aus 
CONNECT Teilekatalog 
Alias ID 
 
Lieferantensachnummer 
 
Anwendungsbereich 
 
Beschreibung 
 
Lokale Beschreibung 
Alias_id 
O 
 
Alias_id 
O 
Lieferantenteilenummer 
Scope 
O 
Lieferantenbezeichnung 
Description 
O 
 
Localized_string 
O 
 
Version 
Version 
O 
Teileversion 
bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
P 
<MBC 
Positionsnummer>, 
siehe Kapitel ******* 
Beschreibung 
Description 
P 
Teilebenennung 
Lokale Beschreibung 
 
Sprachencode 
 
Wert 
Localized_string 
P 
Teilebenennung auf Englisch 
Language_code 
P 
 
Value 
P 
 
Vorgängerteilenummer 
Predecessor_part_
number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Masseninformation 
 
Einheitskomponente 
 
Wertekomponente 
Mass_information 
P 
Masseneinheit und Wert 
Unit_component 
P 
Masseneinheit [g] 
Value_component 
P 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die Bibliothek in 
Klasse Externe 
Referenzen „sofern vorhanden“ 
Änderung 
Change 
O 
 
Materialinformationen 
 
Materialschlüssel 
Material 
O 
 
Material_key 
O 
 
Prozessinformationen 
 
Processing_instruct
ion 
O 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 97 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 97 von 178 
 
 
Instruktionstyp 
 
Instruktionswert 
Instruction_type 
O 
 
Instruction_value 
O 
 
Befestigungstyp 
Fixing_type 
P 
CONNECT -Klasse 
 
 
Abbildung 62: Auszug Befestigungsteile / Fixings (basierend KBL 2.4) 
8.3.4.15 Kontakte/ General_Terminal 
Diese Klasse enthält alle Teile der CONNECT Teilegruppen Kontakte, die direkt einem 
Leitungsanschluss zugeordnet werden können. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
<MBC 
Sachnummer> 
oder 
<Normteilsachnummer> 
Lieferantenname 
Company_name 
P 
Lieferantenbezeichnung 
aus 
CONNECT Teilekatalog 
Alias ID 
 
Lieferantensachnummer 
 
Anwendungsbereich 
 
Beschreibung 
 
Lokale Beschreibung 
Alias_id 
O 
 
Alias_id 
O 
Lieferantenteilenummer 
Scope 
O 
Lieferantenbezeichnung 
Description 
O 
 
Localized_string 
O 
 
Version 
Version 
O 
Teileversion 
bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
P 
<MBC Positionsnummer> 
Beschreibung 
Description 
P 
Teilebenennung 
Lokale Beschreibung 
 
Sprachencode 
Localized_string 
P 
Teilebenennung auf Englisch 
Language_code 
P 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 98 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 98 von 178 
 
 
Wert 
Value 
P 
 
Vorgängerteilenummer 
Predecessor_part_
number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Masseninformation 
 
Einheitskomponente 
 
Wertekomponente 
Mass_information 
P 
Masseneinheit und Wert 
Unit_component 
P 
Masseneinheit [g] 
Value_component 
P 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die Bibliothek in 
Klasse Externe 
Referenzen „sofern vorhanden“ 
Änderung 
Change 
O 
 
Materialinformationen 
 
Materialschlüssel 
Material 
O 
 
Material_key 
O 
 
Prozessinformationen 
 
 
Instruktionstyp 
 
Instruktionswert 
Processing_instruct
ion 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
Kontakttyp 
Terminal_type 
P 
Kontaktfamilie (aus CONNECT) 
Überzugmaterial 
Plating_material 
P 
Beschichtung 
Querschnittsfläche 
 
Einheitskomponente 
 
Minimalwert 
 
Maximalwert 
Cross_section_area 
P 
 
Unit_component 
P 
Einheit Millimeter 
Minimum 
P 
Minimal-Anschlussquerschnitt 
Maximum 
P 
Maximal-Anschlussquerschnitt 
Außendurchmesser 
 
Einheitskomponente 
 
Minimalwert 
 
Maximalwert 
Outside_diameter 
P 
 
Unit_component 
P 
Einheit Millimeter 
Minimum 
P 
Minimal-Außendurchmesser 
Maximum 
P 
Maximal-Außendurchmesser 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 99 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 99 von 178 
 
 
Abbildung 63. Auszug Kontakte/ General-Terminal 
******** Leitungen/ General Wire 
Diese Klasse enthält alle Teile der CONNECT Teilegruppen Einzel- und Sonderleitungen. 
Um die möglichen Leitungskonfigurationen eines einzelnen Leitungstyps eindeutig zu beschreiben 
werden über 500 Sachnummern benötigt. Für die Daimler-internen Prozesse werden daher die 
Sachnummern aus den Merkmalen der Leitung automatisch generiert und in den internen Prozessen 
verwendet. Die Sachnummer (Part_Number) und Benennung (Description) wird aus der 
Kurzbezeichnung des Leitungstyps, dem Leiterquerschnitt und den Leiterfarben gebildet, die 
erforderlichen Einzelwerte werden über die ConnyE- Schnittstelle aus ConnectPARTS bereitgestellt. 
Die im Beispiel hervorgehoben Werte sind in der KBL unbedingt wie beschrieben zu befüllen. 
Sind Daimler-Sachnummern oder -Normteilnummern erforderlich, sind diese im Attribut Part_Number 
zu beschreiben. Ansonsten werden folgende Bezeichnungen verwendet: 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
<MBC Sachnummer> (scharfe 
bzw. generische) 
Lieferantenname 
Company_name 
P 
Lieferantenbezeichnung 
aus 
CONNECT Teilekatalog 
Alias ID 
 
Lieferantensachnummer 
 
Anwendungsbereich 
Alias_id 
O 
 
Alias_id 
O 
Lieferantenteilenummer 
Scope 
O 
Lieferantenbezeichnung 
Version 
Version 
O 
Teileversion 
bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
P 
Leitungsart/ Isolationsmaterial 
Beschreibung 
Description 
P 
Benennung der Leitung 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 100 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 100 von 178 
 
Lokale Beschreibung 
 
Sprachencode 
 
Wert 
Localized_string 
P 
Teilebenennung auf Englisch 
Language_code 
P 
 
Value 
P 
 
Reifegrad 
Degree_of_maturity 
O 
 
Masseninformation 
 
Einheitskomponente 
 
Wertekomponente 
Mass_information 
P 
Masseneinheit und Wert 
Unit_component 
P 
Masseneinheit [g pro Meter] 
Value_component 
P 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die Bibliothek in 
Klasse Externe 
Referenzen „sofern vorhanden“ 
Materialinformationen 
 
Materialschlüssel 
Material 
O 
 
Material_key 
O 
 
Prozessinformationen 
 
 
Instruktionstyp 
 
Instruktionswert 
Processing_ 
instruction 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
Leitungstyp 
Wire_type 
P 
Leitungstyp/ Querschnitt 
Biegeradius 
 
Einheitskomponente 
 
Wertekomponente 
Bend_radius 
O 
 
Unit_component 
O 
Biegeradiuseinheit (Millimeter) 
Value_component 
O 
Biegeradiuswert 
Querschnittsfläche 
 
Einheitskomponente 
 
 
Wertekomponente 
Cross_section_area 
P 
Nur für Einzelleitungen  
Unit_component 
P 
Querschnittseinheit 
(Quadratmillimeter) 
Value_component 
P 
Querschnittswert 
Außendurchmesser 
 
Einheitskomponente 
 
 
Wertekomponente 
Outside_diameter 
P 
 
Unit_component 
P 
Außendurchmessereinheit 
(Millimeter) 
Value_component 
P 
Außendurchmesserwert 
Ader 
 
Id 
 
Leitungstyp 
 
Querschnittsfläche 
 
Einheitskomponente 
 
Core 
P 
 
Id 
P 
 
Wire_type 
P 
Leitungstyp/ Querschnitt 
Cross_section_area 
P 
 
Unit_component 
P 
Querschnittseinheit 
(Quadratmillimeter) 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 101 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 101 von 178 
 
 
Wertekomponente 
 
Außendurchmesser 
 
Einheitskomponente 
 
 
Wertekomponente 
 
Biegeradius 
 Einheitskomponente 
 Wertekomponente 
 
Aderfarbe 
 
Farbtyp 
 
 
Farbwert 
Value_component 
P 
Querschnittswert 
Outside_diameter 
P 
 
Unit_component 
P 
Außendurchmessereinheit 
(Millimeter) 
Value_component 
P 
Außendurchmesserwert 
Bend_radius 
O 
 
Unit_component 
O 
Biegeradiuseinheit (Millimeter) 
Value_component 
O 
Biegeradiuswert 
Core_colour 
P 
Leitungsfarbe 
Colour_type 
P 
"base colour", "second colour", 
"third colour" 
Colour_value 
P 
 
Farbbezeichnung nach IEC 60 
757 
Isolationsfarbe 
 
Farbtyp 
 
 
Farbwert 
 
Cover_colour 
P 
Leitungsfarbe 
Colour_type 
P 
"base colour", "second colour", 
"third colour" 
Colour_value 
P 
Farbbezeichnung nach IEC 60 
757 
 
Abbildung 64: Auszug Leitungen/ General Wire Beispiel Iy-0.35 BK/RD 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 102 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 102 von 178 
 
Abbildung 65: Auszug Core Sonderleitung 
8.3.4.17 Gesamtleitungssatz (Harness) 
Siehe Kapitel 8.3.5 Harness Container 
8.3.4.18 Modulkonfiguration/ Module Configuration 
Die Modulkonfiguration ist zu nutzen, falls Codeanleitungen in der KBL tranportiert werden sollen. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Logistische 
Kontrollinformation 
Logistic_control_inf
ormation 
O 
Leitungscode 
Konfigurationstyp 
Configuration_type 
O 
„option code“ 
kontrollierte Komponente 
 
Controlled_compon
ents 
O 
Verweis 
auf 
zugehörige 
Leitungen 
 
8.3.4.19 Knoten/ Node 
Diese Klasse enthält die Abzweige und Endpunkte (Komponenten) innerhalb der Topologie des 
Leitungssatzes. Die Topologie-Information der 2D, 3D bzw. Formboard-Koordinaten ist in den KBL-
Attributen 
Prozessinformationen/ 
Processing_instruction 
nach 
dem 
definierten 
Schema 
dokumentiert. Die Angabe der DMU-Koordinaten innerhalb der Prozessinformationen ist dabei 
verpflichtend. 
Beispiel Node 1 zeigt den Anschlusspunkt einer Komponente (ID_CON128) 
Beispiel Node 4 zeigt eine einfache Verzweigung (Knoten, BNJ2) in der Leitungssatztopologie 
 
Die Namen BNJ und CON entstammen dem Quellsystem Siemens NX. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
 
Alias ID 
 
Lieferantensachnummer 
 
Anwendungsbereich 
 
Beschreibung 
Alias_id 
O 
 
Alias_id 
O 
 
Scope 
O 
 
Description 
O 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 103 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 103 von 178 
 
 
Lokale Beschreibung 
Localized_string 
O 
 
Kartesische 
Koordinate 
(Cartesian Point) 
Cartesian_point 
P 
laufende 
Nummer 
der 
Verbindung 
aus 
Klasse 
Cartesian_point 
Referenzierte Komponenten 
Referenced_ 
components 
P 
Referenz auf Komponente am 
Start/ Endpunkt 
Referenzierte Kammern 
Referenced_cavitie
s 
P 
Referenz auf Kammern bei 
Steckern 
mit 
mehreren 
Eintrittspunkten 
Prozessinformationen 
 
 
 
 
Instruktionstyp 
 
 
 
Instruktionswert 
Processing_ 
instruction 
P/O 
Information 
über 
DMU-
Koordinaten (Pflicht) sowie 2D- 
und 
Formboard-Koordinaten 
(Optional) 
Instruction_type 
P/O 
DMU_Coordinates 
(P)  
2D_Coordinates 
(O)  
FB_Coordinates                (O) 
Instruction_value 
P 
Koordinaten in "x/y/z" (mit "/" 
als Trennzeichen); Koordinaten 
in "x/y" 
 
 
Abbildung 66: Auszug Knoten/ Node (basierend KBL 2.4) 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 104 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 104 von 178 
 
8.3.4.20 Verbindungen/ Routing 
Diese Klasse enthält für alle elektrische Verbindungen (Leitungen) eine Liste der benutzten Segmente 
des Leitungssatzes. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
geroutetes Kabel 
Routed_wire 
P 
laufende 
Nummer 
der 
Verbindung 
aus 
Klasse 
Connection 
Segmente 
Segments 
P 
Liste 
der 
id’s 
der 
durchlaufenen Segmente 
 
 
 
Abbildung 67: Auszug Verbindungen/Routing 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 105 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 105 von 178 
 
******** Ausbindungen/ Segment 
Diese Klasse enthält alle Ausbindungen und Segmente des Leitungssatzes. 
 
 
Abbildung 68: Auszug Ausbindung/ Segment; Virtual/Physical Länge 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
 
Alias ID 
 
Lieferantensachnummer 
 
Anwendungsbereich 
 
Beschreibung 
 
Lokale Beschreibung 
Alias_id 
O 
 
Alias_id 
O 
 
Scope 
O 
 
Description 
O 
 
Localized_string 
O 
 
virtuelle Länge 
 
Einheitskomponente 
 
Wertekomponente 
Virtual_length 
P 
Wert aus DMU 
Unit_component 
P 
Längeneinheit (Millimeter) 
Value_component 
P 
Längenwert 
physikalische Länge 
 
Einheitskomponente 
 
Wertekomponente 
Physical_length 
P 
Realer Wert 
Unit_component 
P 
Längenmaßeinheit (Millimeter) 
Value_component 
P 
Längenwert 
Endvektor (3x) 
End_Vector 
P 
 
Startvektor (3x) 
Start_Vector 
P 
 
Endknoten 
End_node 
P 
ID 
des 
Endknoten 
des 
Segments 
Startknoten 
Start_node 
P 
 
ID des Anfangsknoten des 
Segments 
Form 
Form 
O 
 
Centercurve 
 
Grad 
B_spline_curve 
P 
 
Degree 
P 
degree =1, Mittellinie 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 106 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 106 von 178 
 
 
Kontrollpunkte 
Control_points 
P 
Liste der Segment-Koordinaten 
Prozessinformationen 
 
 
Instruktionstyp 
 
Instruktionswert 
 
Processing_ 
instruction 
P 
 
Instruction_type 
P 
"HCA-SNR"  
Instruction_value 
P 
HCA-SNR in der Segment 
definiert wurde  
Querschnittsfläche 
 
Wertefestlegung 
 
 
Fläche 
 
Einheitskomponente 
 
Wertekomponente 
 
Cross_section_area 
O 
Bündeldurchmesser in mm2 
Value_ 
determination 
O 
measured oder calculated 
 
Area 
O 
 
Unit_component 
O 
Maßeinheit (Quadratmillimeter) 
Value_component 
O 
gemessener und berechneter 
Wert 
zugeordnetes 
Befestigungselement 
 
Ort 
 
 
absoluter Ort 
 
 
Orientierung (3x) 
 
Befestigungselement 
Fixing_assignment 
P 
Befestigungselement 
 
Location 
P 
Platzierung 
des 
Befestigungselement (relativ) 
Absolute_location 
P 
Platzierung 
des 
Befestigungselement (absolut) 
Orientation 
P 
Ausrichtung , Montagerichtung 
Fixing 
P 
ID des Befestigungselement 
Fixings 
Schutzfläche 
 
 
Startort 
 
 
absoluter Startort 
 
 
Endort 
 
 
absoluter Endort 
 
 
angebundene 
Schutzfläche 
 
Prozessinformationen 
Protection_area 
P 
Beschreibung 
des 
Leitungsschutzes 
Start_location 
P 
Startort 
des 
Leitungssatzschutzes (relativ) 
Absolute_start_ 
location 
P 
Startort 
des 
Leitungssatzschutzes (absolut) 
End_location 
P 
Endort 
des 
Leitungssatzschutzes (relativ) 
Absolute_end_ 
location 
P 
Endort 
des 
Leitungssatzschutzes (absolut) 
Associated_ 
protection 
P 
Medium 
des 
verwendeten 
Leitungsschutzes 
Processing_ 
O 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 107 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 107 von 178 
 
 
 
Instruktionstyp 
 
Instruktionswert 
instruction 
Instruction_type 
O 
 
Instruction_value 
O 
 
 
 
 
Abbildung 69: Auszug Ausbindung/ Segment; Cross Section/Protection Areas (basierend 
KBL 2.4) 
8.3.4.22 Maßeinheiten/ Units 
Diese Klasse enthält die Festlegung aller im Harness verwendeten Maßeinheiten. Die Angaben sind 
eindeutig, und werden aus der Verwendung heraus referenziert. Siehe auch Kapitel 8.3.3.1 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Einheitsname 
Unit_name 
P 
 
SI Einheitsname 
Si_unit_name 
P 
 
SI Prefix 
Si_prefix 
P 
 
SI Dimension 
Si_dimension 
P 
 
8.3.4.23 Umwicklungen Kabelschutz/ Wire_protection 
Diese Klasse enthält alle Schläuche und Umwicklungen zum Kabelschutz. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
<MBC Sachnummer> 
Lieferantenname 
Company_name 
P 
Lieferantenbezeichnung 
aus 
CONNECT Teilekatalog 
 
Alias ID 
 
Lieferantensachnummer 
Alias_id 
O 
 
Alias_id 
O 
Lieferantenteilenummer 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 108 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 108 von 178 
 
 
Anwendungsbereich 
 
Beschreibung 
 
Lokale Beschreibung 
Scope 
O 
Lieferantenbezeichnung 
Description 
O 
 
Localized_string 
O 
 
Version 
Version 
O 
Teileversion 
bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
P 
<MBC Positionsnummer> 
Beschreibung 
Description 
P 
Teilebenennung 
Lokale Beschreibung 
 
Sprachencode 
 
Wert 
Localized_string 
P 
Teilebenennung auf Englisch 
Language_code 
P 
 
Value 
P 
 
Vorgängerteilenummer 
Predecessor_part_
number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Masseninformation 
 
 
Einheitskomponente 
 
 
 
Wertekomponente 
Mass_information 
 
P 
Masseneinheit und Wert in g/ 
m, bzw. g/ m2 
Unit_component 
 
 
P 
Masseneinheit (bei Schläuchen 
g/ m; bei Bandagierung g/ 
m2) 
Value_component 
P 
Wert 
Externe Referenzen 
External_reference 
P 
 
Materialinformationen 
 
 
Materialschlüssel 
Material 
 
P 
nur 
für 
Spar- 
und 
Vollbandagierungen 
Material_key 
P 
A-SNR des Bandes 
Prozessinformationen 
 
 
Instruktionstyp 
 
Instruktionswert 
Processing_instruct
ion 
P 
Verarbeitungshinweis 
bzw. 
Schlauchfamilie 
Instruction_type 
P 
"CAD_INDICATOR" 
Instruction_value 
P 
CAD-Kennzeichen 
Schutztyp 
Protection_type 
P 
"TUBE" bzw. "TAPE" 
typabhängige Parameter 
Type_dependent_p
arameter 
P 
Wert 
aus 
dem 
Feld 
"Schlauchfamilie" 
bzw. 
"Bandagierungstyp" 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 109 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 109 von 178 
 
 
Abbildung 70: Auszug Umwicklungen Kabelschutz/ Wire Protection (basierend KBL 2.4) 
8.3.5 Harness Container 
Die Klassen …_ occurrence werden vom KBL Prozessor generiert und bestehen in der Regel aus 
Referenzen auf Verwendungen oder Bibliotheksteilen. Explizit beschrieben werden hier nur Referenzen 
auf Nutzdaten aus den Bibliotheken die von anderen Prozessen an dieser Stelle erwartet werden bzw. 
für Inhalte für die besondere Regeln erarbeitet wurden. 
In den folgenden Kapiteln werden diese Klassen näher beschrieben. Die Klasse Module wird in Kapitel 
8.3.6 näher beschrieben. 
******* Attribute des Harness-Containers 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
<MBC 
Sachnummer> 
(bei 
Masterleitungssatz 
die 
Tabellensachnummer) 
Lieferantenname 
Company_name 
P 
Lieferantenbezeichnung 
aus 
CONNECT Teilekatalog 
Alias ID 
 Lieferantensachnummer 
 Anwendungsbereich 
 Beschreibung 
 Lokale Beschreibung 
 
 
Alias_id 
O 
 
Alias_id 
O 
Lieferantenteilenummer 
Scope 
O 
Lieferantenbezeichnung 
Description 
O 
 
Localized_string 
 
 
O 
 
Version 
Version 
P 
Datenstand 
aus 
Zeichnungsschriftfeld (Format 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 110 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 110 von 178 
 
YYYY-MM-DD) 
Beschreibung 
Description 
P 
<Benennung> 
aus 
Zeichnungsschriftfeld 
Lokale Beschreibung 
 
Sprachencode 
 
Wert 
Localized_string 
P 
Teilebenennung auf Englisch 
Language_code 
P 
 
Value 
P 
 
Vorgängerteilenummer 
Predecessor_part_
number 
P 
<MBC Vorgängersachnummer> 
Zeichnungsvorgänger; 
wenn 
kein Vorgänger: "-" 
Reifegrad 
Degree_of_maturity 
O 
 
Copyright-Bemerkung 
Copyright_note 
P 
Copyright reserved Daimler 
AG, Schutzvermerk nach DIN 
ISO 16016 beachten! 
Masseninformation 
 
Einheitskomponente 
 
Wertekomponente 
Mass_information 
P 
Masseneinheit und Wert 
Unit_component 
P 
Masseneinheit [kg] 
Value_component 
P 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die Bibliothek in 
Klasse Externe 
Referenzen „sofern vorhanden“ 
Änderung 
Change 
P 
Change ID groß ist ZGS 
Materialinformationen 
 
Materialschlüssel 
Material 
O 
 
Material_key 
O 
 
Prozessinformationen 
 
 
Instruktionstyp 
 
 
Instruktionswert 
Processing_instruct
ion 
P 
DS-/DZ-Merkmalserläuterung 
Instruction_type 
P 
DS/DZ-Merkmalserläuterung 
("DS"/ "DZ") 
Instruction_value 
P 
DS/DZ-Merkmalserläuterung 
("1=…, 
2=…, 
3=…") 
- 
Erläuterung mit = und Komma 
getrennt 
Projektnummer 
Project_number 
P 
Name der Baureihe(n) [laut 
Dialog], Bsp. C222 
 
Car Classification Level 2 
 
Car_classification_ 
level_2 
O 
Ausführungsart (FX, FW, FV, 
etc.) 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 111 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 111 von 178 
 
Car Classification Level 3 
 
Car_classification_ 
level_3 
O 
Lenkervariante 
(LL/RL/lenkungsneutral) 
Modelljahr 
Model_year 
P 
ÄJ/ E-Fzg/ etc. 
Inhalt 
Content 
P 
"harness 
subset" 
bei 
Einzelleitungsätzen, 
"harness 
complete 
set" 
bei 
Masterleitungssätzen 
 
In Version wird die Versionsbezeichnung = Datenstand des Dokuments hinterlegt. Über die 
Sachnummer und den Wert von Version werden verschiedenartige Dokumente wie z.B. Zeichnung und 
KBL verknüpft.  
Als Versionskenner des Leitungssatzes ist in Version das Datum vom Datenstand nach DIN ISO 8601 
einzutragen. Im KBL gibt es das Attribut <Version> unter dem Element Harness und unter dem 
Element Modul. 
 
Das Attribut Harness -> Version ist der Datenstand der Tabellenzeichnung bei Masterleitungssätzen 
bzw. der Datenstand des Einzelleitungssatzes; Beispiel: 
 
 
 
Das Attribut Module -> Version ist der Datenstand des Leitungssatzmodules. Bei Einzelleitungssätzen 
ist der Wert gleich wie in Harness-Version, Beispiel: 
 
 
 
 
Der Datenstand der Tabelle/Master (Harness -> Version) unterscheidet sich vom Datenstand beim 
Modul (Module -> Version). 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 112 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 112 von 178 
 
8.3.5.2 Accessory_occurrence 
In dieser Klasse ist die Verwendung aller benutzten Zubehörteile im Harness beschrieben. 
Inhalt:  Referenz auf die benutzten Zubehörteile im Harness 
 
Referenz auf das Bibliotheksteil in Klasse Accessory 
 
Referenz auf den Hauptteil wie z.B. eine Steckerverwendung 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
 
Alias ID 
 
Alias ID 
 
Anwendungsbereich 
 
Beschreibung 
 
Lokale Beschreibung 
Alias_id 
O 
 
Alias_id 
O 
 
Scope 
O 
 
Description 
O 
 
Localized_string 
O 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
 
Sprachencode 
 
Wert 
Localized_string 
O 
 
Language_code 
O 
 
Value 
O 
 
Platzierung 
 
U (3x) 
 
V (3x) 
 
Kartesische 
Koordinate (Cartesian 
Point) 
Placement 
P 
 
U 
P 
 
V 
P 
 
Cartesian_Point 
P 
 
Referenzelemente 
Reference_element 
P 
 
Bauteil 
Part 
P 
 
Installationsinstruktionen 
 
 
Instruktionstyp 
 
Instruktionswert 
 
Installation_instructi
on 
P 
 
Instruction_type 
P 
"HCA-SNR" 
Instruction_value 
P 
HCA-SNR in der Accessory-
Occurrence platziert wurde 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 113 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 113 von 178 
 
8.3.5.3 Cavity Plug occurrence 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Bauteil 
Part 
P 
 
Id 
Id 
P 
 
Installationsinstruktionen 
 
 
Instruktionstyp 
 
Instruktionswert 
 
Installation_instructi
on 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
8.3.5.4 Cavity Seal occurrence 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Bauteil 
Part 
P 
 
Id 
Id 
P 
 
Installationsinstruktionen 
 
 
Instruktionstyp 
 
Instruktionswert 
Installation_instructi
on 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
******* Component_occurrence 
Inhalt: Referenz auf die verwendeten Stecker und Kontaktgehäuse im Harness 
          Referenz auf das Bibliotheksteil in Klasse Accessory 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
Sicherungsnummer (SI-Nummer) 
Beschreibung 
Description 
P 
Funktion 
für 
Sicherungseinlegeblatt 
(Spalte 
SBT) 
Bauteil 
Part 
P 
 
befestigtes Element 
Mounting_ 
P 
darf 
nur 
auf 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 114 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 114 von 178 
 
element 
Component_Slot_Occurrence 
verweisen 
Installationsinstruktionen 
 
 
Instruktionstyp 
 
Instruktionswert 
 
Installation_ 
instruction 
P 
 
Instruction_type 
P 
"HCA-SNR" 
Instruction_value 
P 
HCA-SNR in der Component 
platziert wurde 
Fuse occurrence 
 
angehängte Verbraucher 
 
 
Id 
 
Beschreibung 
 
Lokale Beschreibung 
 
 
 
Sprachen-code 
 
 
Wert 
Fuse_occurrence 
P 
 
Attached 
Consumers 
P 
 
Id 
P 
 
Description 
P 
Verbraucher laut SBT 
Localized_string 
P 
Verbraucher 
laut 
Sicherungsbelegungstabelle 
(Englische Bezeichnung) 
Language_code 
 
P 
 
Value 
P 
 
******* Component box occurrence 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
REF der Device (z.B. F40) 
Beschreibung 
Description 
P 
Bezeichnung der Verwendung 
(zB PDC-E, …) 
Lokale Beschreibung 
 
 
Sprachencode 
 
Wert 
Localized_string 
 
P 
Bezeichnung der Verwendung 
auf Englisch 
Language_code 
P 
 
Value 
P 
 
Bauteil 
Part 
P 
 
Referenzelemente 
Reference_element 
P 
 
Komponentenstecker 
 
 
Bauteil 
Component_box_con
nectors 
P 
 
Part 
P 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 115 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 115 von 178 
 
 
Kammern 
Slots 
P 
 
Komponentenkammern 
 
Id 
 
Bauteil 
 
Komponentenpins 
 
Bauteil 
Component_slots 
P 
 
Id 
P 
REF der Verwendung (F40/ 1-S) 
Part 
P 
 
Component_cavities 
P 
 
Part 
P 
 
Kontaktpunkt 
 
Id 
 
anliegende Teile 
 
kontaktierter Pin 
 
Prozessinformationen 
 
 
Instruktionstyp 
 
Instruktionswert 
Contact_points 
P 
 
Id 
P 
 
Associated_parts 
P 
 
Contacted_cavity 
P 
 
Processing_instructi
on 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
Installationsinstruktionen 
 
 
Instruktionstyp 
 
Instruktionswert 
 
 
Installation_instructi
on 
P 
 
Instruction_type 
P 
"HCA-SNR" 
Instruction_value 
P 
HCA-SNR 
in 
der 
component_box_occurrence 
platziert wurde 
8.3.5.7 Verbindung/ Connection 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
 
Signalname 
Signal_name 
P 
 
Signaltyp 
Signal_type 
O 
"BUS", "ENERGY", "GROUND" 
oder weitere nach Abstimmung 
Nennspannung 
Nominal_voltage 
P 
(nur 
Masseleitungen) 
"12V", 
"24V", "48V", "HV"  
Externe Referenzen 
External_reference 
P 
Verweis auf Schaltplan-SNR inkl. 
Blatt-Nr aus der Verbindung 
kommt 
Leitung 
Wire 
P 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 116 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 116 von 178 
 
Extremitäten 
 
Position auf Leitung 
 
Kontaktpunkt 
 
ID 
 
anliegende 
Teile 
 
kontaktierter 
Pin 
Extremities 
P 
 
Position_on_wire 
P 
 
Contact_point 
P 
 
Id 
P 
 
Associated_parts 
 
P 
 
Contacted_cavity 
P 
 
******* Connector_occurrence 
Inhalt: Referenz auf die verwendeten Stecker und Kontaktgehäuse der Klasse Connector.  
Die REF eines Bauteiles wird als ID abgelegt. Die Langbezeichnung einer Bauteilreferenz der 
Verwendungsbezeichnung wird im Attribut Description beschrieben. Die Bauteilreferenzen selbst 
müssen in der CONNECT-Datenbank angelegt und der verwendeten Baureihe zugeordnet sein. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
 
Bauteilkurzbezeichnung 
(REF) 
aus VZK 
Beschreibung 
Description 
P 
Langbezeichnung aus VZK 
Lokale Beschreibung 
 
 
Sprachencode 
 
Wert 
Localized_string 
 
P 
Langbezeichnung aus VZK auf 
englisch 
Language_code 
P 
 
Value 
P 
 
Gebrauch 
Usage 
P 
"splice" 
bei 
Splice, 
"ring 
terminal" bei Kabelschuhen, "no 
end" 
bei 
Routing- 
und 
Z- 
Punkten, nicht verwendet sonst 
Platzierung 
 
U (3x) 
 
V (3x) 
 
Kartesische 
Koordinate (Cartesian 
Point) 
Placement 
P 
 
U 
P 
 
V 
P 
 
Cartesian_Point 
P 
 
Bauteil 
Part 
P 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 117 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 117 von 178 
 
Referenzelemente 
Reference_element 
O 
 
Kontaktpunkt 
 
Id 
 
anliegende Teile 
 
kontaktierter Pin 
Contact_points 
P 
 
Id 
P 
 
Associated_parts 
P 
 
Contacted_cavity 
P 
 
Installationsinstruktionen 
 
 
Instruktionstyp 
 
Instruktionswert 
 
Installation_ 
instruction 
P 
 
Instruction_type 
P 
"HCA-SNR" 
Instruction_value 
P 
HCA-SNR in der Connector-
Occurrence platziert wurde 
Kammern 
 
Bauteil 
 
angeschlossene 
Kammern 
 
Prozessinformationen 
 
 
Instruktionstyp 
 
Instruktionswert 
 
Pin 
 
Bauteil 
 
zugehörige Stopfen 
 
angeschlossene Pins 
Slots 
P 
 
Part 
P 
 
Mated_slots 
 
P 
 
Referenz 
auf 
Kammer 
des 
Gegensteckers 
Processing_ 
instruction 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
Cavities 
P 
 
Part 
P 
 
Associated_plug 
P 
 
Mated_cavities 
P 
Referenz 
auf 
Pin 
des 
Gegensteckers 
(bspw. 
Trennstellen) 
 
 
Abbildung 71: Auszug Connector_occurrence 
8.3.5.9 Fixing_occurrence 
In dieser Klasse ist die Verwendung aller benutzten Befestigungsteile im Harness beschrieben.  
Inhalt: Referenz auf die verwendeten Befestigungsteile (Fixing) im Leitungssatz 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 118 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 118 von 178 
 
Referenz auf die Bezugsteile wie z.B. ein Segment des Leitungsstrangs 
Referenz auf die Koordinaten für diese Verwendung 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
 
Sprachencode 
 
Wert 
Localized_string 
O 
 
Language_code 
O 
 
Value 
O 
 
Platzierung 
 
U (3x) 
 
V (3x) 
 
Kartesische 
Koordinate (Cartesian 
Point) 
Placement 
P 
 
U 
P 
 
V 
P 
 
Cartesian_Point 
P 
 
Bauteil 
Part 
P 
 
Installationsinstruktionen 
 
 
Instruktionstyp 
 
Instruktionswert 
 
Installation_instructi
on 
P 
 
Instruction_type 
P 
"HCA-SNR" 
Instruction_value 
P 
HCA-SNR 
in 
der 
Fixing-
Occurrence platziert wurde 
******** General_wire_occurrence 
Inhalt: Referenz auf die verwendeten Leitungen (General_Wire) im Leitungssatz 
Referenz auf die Verbindungen (Connection) des Leitungssatz 
Referenz auf die Verarbeitungshinweise für diese Verwendung 
Leitungslänge aus dem Produktionsprozess (DMU-Length/Produktions-Length) 
Inhalte für Attribut Length_information 
Length_type 
 
Production_length 
Längenangabe aus Produktionsprozess 
Length_value  
zugeschnittene Länge aus Produktionsprozess abgeleitet 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Bauteil 
Part 
P 
 
Installationsinstruktionen 
Installation_ 
O 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 119 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 119 von 178 
 
 
 
Instruktionstyp 
 
Instruktionswert 
instruction 
Instruction_type 
O 
 
Instruction_value 
O 
 
Längeninformation 
 
Längentyp 
 
Längenwert 
 Einheits-
komponente 
 Werte-
komponente 
Längeninformation 
P 
 
Length_type 
P 
 
Length_value 
P 
 
Unit_component 
 
P 
 
Value_component 
P 
 
Wire occurrence 
 
Drahtnummer 
 
Wire_occurrence 
P 
 
Wire_number 
P 
eindeutige 
Wire-ID 
aus 
Schaltplan 
Special Wire occurrence 
 
 
Sonderkabel-ID 
 
 
Core occurrence 
 
Drahtnummer 
 
 
Bauteil 
 
Längeninformation 
 
 
Längentyp 
(DMU, 
Production, 
Supplement) 
 
Längenwert 
 
Einheits-komponente 
 
 
Werte-komponente 
 
 
Installations-
instruktionen 
 
Instruktionstyp 
 
Instruktionswert 
Special_wire_occurr
ence 
P 
 
Special_wire_id 
P 
eindeutige Sonderkabel-ID aus 
Schaltplan 
Core_occurrence 
P 
 
Wire_number 
 
P 
eindeutige 
Wire-ID 
aus 
Schaltplan 
Part 
P 
 
Längeninformation 
 
P 
 
Length_type 
 
 
P 
„dmu 
length“, 
„production 
length“, „supplement length” 
 
Length_value 
P 
 
Unit_component 
 
P 
 
Value_component 
 
P 
 
Installation_ 
instruction 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 120 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 120 von 178 
 
*******1 Terminal_occurrence 
Inhalt: Referenz auf das verwendete Kontaktteil (General_Terminal) 
Referenz auf den Kontaktpunkt am verwendeten Stecker 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Bauteil 
Part 
P 
Referenz auf das verwendete 
Kontaktteil (General_Terminal) 
Id 
Id 
P 
 
Installationsinstruktionen 
 
 
Instruktionstyp 
 
Instruktionswert 
Installation_ 
instruction 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
******** Wire_protection_occurrence 
Inhalt:   Referenz auf den zu schützenden Abschnitt des Leitungsstrangs 
Referenz auf das zu schützende Segment des Leitungsstrangs 
Referenz auf das verwendete Bibliotheksteil in Klasse Wire_protektion 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
 
Alias ID 
 
Alias ID 
 
Anwendungsbereich 
 
Beschreibung 
 
Lokale Beschreibung 
Alias_id 
O 
 
Alias_id 
O 
 
Scope 
O 
 
Description 
O 
 
Localized_string 
O 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
 
Sprachencode 
 
Wert 
Localized_string 
O 
 
Language_code 
O 
 
Value 
O 
 
Schutzlänge 
 
Einheitskomponente 
 
Wertekomponente 
 
Protection_length 
P 
 
Unit_component 
P 
Längeneinheit (Millimeter) 
Value_component 
 
P 
Längenwert des  zu schützenden 
Abschnitts 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 121 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 121 von 178 
 
Bauteil 
Part 
P 
 
Installationsinstruktionen 
 
Instruktionstyp 
Instruktionswert 
 
Installation_instructi
on 
P 
 
Instruction_type 
P 
"HCA-SNR" 
Instruction_value 
P 
HCA-SNR 
in 
der 
wire_protection_occurrence 
platziert wurde 
*******3 Verkabelungsgruppe (Wiring Group) 
Ist 
nur 
vorgesehen 
für 
verdrillte 
Einzeladern 
(Präfix 
"ID_WRG") 
oder 
auch 
als 
„Bohrmaschinenleitungen“ bezeichnet.  
In CONNECT werden diese Leitungen mit einem Flag versehen. 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
 
Typ 
Type 
P 
CAD-Nr. 
der 
Verdrillungsbeschreibung 
(z.B. 
"B48") 
Zugewiesene Leitungen 
Assigned_wire 
P 
Verweis auf Wire_occurrences 
Prozessinformationen 
 
 
Instruktionstyp 
 
Instruktionswert 
 
Processing_instructi
on 
P 
Beschreibung der A-SNR der 
Verdrillungsbeschreibung 
Instruction_type 
P 
"Part_number" 
Instruction_value 
P 
Sachnummer 
der 
Verdrillungsbeschreibung 
Installationsinstruktionen 
 
 
Instruktionstyp 
 
Instruktionswert 
Installation_instructi
on 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
*******4 Harness_configuration 
Die Klasse enthält alle Attribute der Klasse Harness und zusätzlich eine Zusammenfassung aller 
angelegten Module sowie die Änderungshistorie der Zeichnung in der Klasse Change. 
 
Inhalt:   Referenz auf angelegte Module (kZ-Teile der Zeichnung) 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 122 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 122 von 178 
 
 
Referenz auf die Änderungshistorie der Zeichnung 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
<MBC Sachnummer> 
Lieferantenname 
Company_name 
P 
Lieferantenbezeichnung 
aus 
CONNECT Teilekatalog 
Alias ID 
 
Lieferantensachnummer 
 
Anwendungsbereich 
Alias_id 
O 
 
Alias_id 
O 
Lieferantenteilenummer 
Scope 
O 
Lieferantenbezeichnung 
Version 
Version 
P 
Datenstand 
aus 
Zeichnungsschriftfeld (Format 
YYYY-MM-DD) 
Beschreibung 
Description 
P 
 
Lokale Beschreibung 
 
Sprachencode 
 
Wert 
Localized_string 
P 
Teilebenennung auf Englisch 
Language_code 
P 
 
Value 
P 
 
Vorgängerteilenummer 
Predecessor_part_
number 
P 
 
Reifegrad 
Degree_of_maturity 
O 
 
Copyright-Bemerkung 
Copyright_note 
P 
Copyright reserved Daimler 
AG, Schutzvermerk nach DIN 
ISO 16016 beachten! 
Masseninformation 
 
Einheitskomponente 
 
Wertekomponente 
Mass_information 
P 
Masseneinheit und Wert 
Unit_component 
P 
Masseneinheit [kg] 
Value_component 
P 
Wert 
Änderung 
Change 
P 
Change ID groß ist ZGS 
Materialinformationen 
 
Materialschlüssel 
Material 
O 
 
Material_key 
O 
 
Prozessinformationen 
 
 
Instruktionstyp 
 
 
Instruktionswert 
Processing_ 
instruction 
P 
DS-/ DZ-Merkmalserläuterung 
Instruction_type 
P 
DS/ DZ-Merkmalserläuterung 
("DS"/ "DZ") 
Instruction_value 
P 
DS/ DZ-Merkmalserläuterung 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 123 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 123 von 178 
 
("1=…, 
2=…, 
3=…") 
- 
Erläuterung mit = und Komma 
getrennt 
Projektnummer 
Project_number 
P 
Name der Baureihe(n) [laut 
Dialog], Bsp. C222 
Car Classification Level 2 
 
Car_classification_ 
level_2 
O 
Ausführungsart (FX, FW, FV, 
etc.) 
Car Classification Level 3 
 
Car_classification_ 
level_3 
0 
Lenkervariante 
(LL/RL/lenkungsneutral) 
Modelljahr 
Model_year 
P 
ÄJ/ E-Fzg/ etc. 
Module 
Modules 
P 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 124 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 124 von 178 
 
8.3.6 Module 
8.3.6.1 Attribute der Module 
Hinweis: Die ersten 3 Ziffern der MBC-Sachnummer im Attribut Module->Part_Number müssen einer 
in der CONNECT-Datenbank angelegten Baureihe entsprechen. 
Beispiel: MBC Sachnummer A 222 540 93 00 -> Baureihe C222 muss in CONNECT angelegt sein. 
Bei Masterleitungssätzen werden die darin enthaltenen Modulleitungssätze mit einer eindeutigen 
Modulvariante im Attribut Abbreviation gekennzeichnet. Wird nur ein Einzelmodul, innerhalb des 
Masterleitungssatzes freigeben, muss ebenfalls im Attribut Abbreviation der Variantenkenner 
hochgezogen werden. 
Beispiel Modulvariante : V11 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Teilenummer 
Part_number 
P 
<MBC 
Sachnummer 
des 
Modulleitungssatzes> 
Lieferantenname 
Company_name 
P 
Lieferantenbezeichnung 
aus 
CONNECT Teilekatalog 
Alias ID 
 
Lieferantensachnummer 
 
Anwendungsbereich 
 
Beschreibung 
 
Lokale Beschreibung 
Alias_id 
O 
 
Alias_id 
O 
Lieferantenteilenummer 
Scope 
O 
Lieferantenbezeichnung 
Description 
O 
 
Localized_string 
O 
 
Version 
Version 
P 
Datenstand 
aus 
Zeichnungsschriftfeld (Format 
YYYY-MM-DD) 
PosNr 
Abbreviation 
P 
Modulvariante 
Beschreibung 
Description 
P 
<Benennung> 
Lokale Beschreibung 
 
Sprachencode 
 
Wert 
Localized_string 
O 
Teilebenennung auf Englisch 
Language_code 
O 
 
Value 
O 
 
Vorgängerteilenummer 
Predecessor_part_
number 
P 
<MBC Vorgängersachnummer> 
Zeichnungsvorgänger, 
wenn 
kein Vorgänger: "-" 
Reifegrad 
Degree_of_maturity 
O 
Einsatztermin 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 125 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 125 von 178 
 
Copyright-Bemerkung 
Copyright_note 
P 
Copyright reserved Daimler 
AG, Schutzvermerk nach DIN 
ISO 16016 beachten! 
Masseninformation 
 
Einheitskomponente 
 
Wertekomponente 
Mass_information 
P 
Masseneinheit und Wert 
Unit_component 
P 
Masseneinheit [kg] 
Value_component 
P 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die Bibliothek in 
Klasse Externe Referenzen 
Änderung 
 
Id 
 
Beschreibung 
 
Änderungsmeldung 
 
Änderungsdatum 
 
verantwortlicher 
Designer 
 
verantwortliche 
Abteilung 
 
Genehmiger Name 
 
Genehmiger Abteilung 
 
Change 
P 
 
Id 
P 
letzter ZGS-Stand 
Description 
P 
 
Change_request 
P 
KEM-Nummer 
Change_date 
P 
 
Responsible_ 
designer 
P 
 
Designer_ 
department 
P 
 
Approver_name 
P 
 
Approver_ 
department 
P 
 
Materialinformationen 
 
Materialschlüssel 
Material 
O 
 
Material_key 
O 
 
Prozessinformationen 
 
 
Instruktionstyp 
 
 
Instruktionswert 
Processing_ 
instruction 
P 
DS/ 
DZ-Merkmale; 
ESD-
Kenner 
Instruction_type 
P 
DS/ 
DZ-Merkmale 
("DS", 
"DZ"); ESD-Kenner ("ESD") 
Instruction_value 
P 
DS/ DZ-Merkmale ("1, 2, 3") - 
Nummer 
der 
in 
Harness 
definierten 
Merkmale 
mit 
Komma getrennt; "J"/ "N" 
Projektnummer 
Project_number 
P 
Name der Baureihe(n) [laut 
Dialog], Bsp. C222 
Car Classification Level 2 
Car_classification_ 
level_2 
O 
Ausführungsart (FX, FW, FV, 
etc.) 
Car Classification Level 3 
Car_classification_ 
O 
Lenkervariante 
(LL/ 
RL/ 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 126 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 126 von 178 
 
level_3 
lenkungsneutral) 
Car Classification Level 4 
Car_classification_ 
level_4 
O 
 
Modelljahr 
Model_year 
P 
ÄJ/ E-Fzg/ etc. 
Inhalt 
Content 
P 
"module" 
Modulfamilie 
Of_family 
P 
sofern Zuordnung möglich 
Modulkonfiguration 
 
 
 
Logistische 
Kontrollinformation 
 
Konfigurationstyp 
 
kontrollierte 
Komponente 
Module_ 
configuration 
 
P 
Liste mit Referenzen aller 
Einzelteile 
des 
Leitungssatzmoduls 
Logistic_control_ 
information 
P 
wenn Code bekannt, dann an 
dieser Stelle 
Configuration_type 
P 
"option code" 
Controlled_ 
components 
P 
 
******* Modulfamilie/ Module Family 
 
 
 
 
Bezeichnung 
KBL-Attribut 
Pflicht    [P] 
Optional [O] 
Bemerkung 
Id 
Id 
P 
 
Beschreibung 
Description 
P 
Bezeichnung der Modulfamilie 
Lokale Beschreibung 
 
 
Sprachencode 
 
Wert 
Localized_string 
P 
Bezeichnung der Modulfamilie 
(Englisch) 
Language_code 
P 
 
Value 
P 
 
Prozessinformationen 
 
 
Instruktionstyp 
 
Instruktionswert 
Processing_instruct
ion 
O 
 
Instruction_type 
O 
 
Instruction_value 
O 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 127 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 127 von 178 
 
8.4 Digitale Netzliste 
Schaltplandaten sind nach der AV Schaltplanerstellung zu erstellen und in der Abstimmung mit den 
Baureihenprojekten in das DV System ConnectHARNESS abzuliefern. Diese bestehen aus einer PDF-
Datei und einer von MBC geforderten digitalen Netzliste (generischer Export). 
 
8.5 TIFF 
Die Zeichnungsdaten werden im Hause Daimler AG in den elektronischen Freigabeprozess über 
Smaragd eingespeist und für die Langzeitarchivierung in das System ZGDOK eingestellt.  
Die Daten sind entsprechend der Anforderungen des Dokumentationssystems ZGDOK zu erstellen und 
vor dem ersten Einsatz bei MBC zu qualifizieren. 
 
Anforderungen des ZGDOK: 
Tagged Image File Format G4 
Anordnung der Pixel: 
Striped (Streifenformat) 
Farbtiefe: 
 
 
1 Bit 
Auflösung: 
 
 
200 bis 400 DPI (dots per inch) 
Komprimierung:  
 
CCITT Gruppe 4 
 
8.6 HCV 
8.6.1 Aufbau des HCV-Datencontainer 
Der Harness Container for Viewing dient zum Transport aller notwendigen Dateien, die einen 
gesamten Leitungssatz inklusive der verschiedenen Varianten bzw. Module beschreiben. Es können 
aber auch einzelne Leitungssatzmodule damit transportiert werden. 
Er wird hauptsächlich zur Masterfreigabe verwendet, ist aber auch in vielen anderen Einsatzgebieten 
der Entwicklung oder Kalkulation, sowie des Musterbaus gut einsetzbar. Da sowohl die grafische 
Repräsentation der Leitungssatzzeichnung als auch alle technischen Informationen darin enthalten 
sind, können die meisten Fragestellungen bezüglich des Produktes selber als auch der Fertigung 
beantwortet werden. 
Das Softwareprodukt E3.HarnessAnalyzer kann diese HCV Container lesen und deren Inhalt 
entsprechend visualisieren, vergleichen oder auch analysieren. 
Ein HCV ist ein komprimierter (ZIP) Datencontainer, bestehend aus: 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 128 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 128 von 178 
 
 
genau einer KBL, die den gesamten Leitungssatz mit den Varianten und Modulen beinhaltet 
 
einer oder mehrerer SVG Dateien, die die grafische Repräsentation der Leitungssatzzeichnung 
auf gegebenenfalls mehreren Seiten darstellt. Die Namen der Dateien sollten den Inhalt 
kennzeichnen, damit man sie im Dokumentenbaum leichter wiederfinden kann, und müssen 
die Endung .svg haben. (Bsp.: Topologie.svg, Deckblatt.svg, Stückliste.svg…) 
 
genau einer Index-XML Datei, die eine Modulstückliste repräsentiert 
 
anderer optionaler Dateien, wie z.B. 3D Konturmodelle des Leitungssatzes 
 
optionaler „Redlining“ Informationen, die der E3.HarnessAnalyzer dort einstellen kann  
 
Die Komprimierung muss nach ZIP Standard erfolgen, es werden keine RAR/TAR oder GZIP Archive 
unterstützt. 
Abhängig von Prozessanforderungen können in einem HCV Container auch weitere Dateien (z.B. HP-
GL/2, TIFF, PDF) enthalten sein. Ein HCV Viewer muss die im Mindestumfang enthaltenen Dateien 
(KBL, SVG(s), Index XML) für das Viewing berücksichtigen. Die Zusätzlich enthaltenen Dateien können 
vom Viewing System berücksichtigt und entsprechend angeboten/ausgewertet werden. 
 
 
Abbildung 72: schematischer Aufbau eines HCV-Containers 
8.6.2 KBL 
Ein HCV Container muss genau eine KBL Datei beinhalten. Die KBL Datei muss nach dem KBL Schema 
2.4 (siehe Kapitel 0) aufgebaut sein. 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 129 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 129 von 178 
 
8.6.3 Index.xml 
Die Index XML ist eine Stückliste des Leitungssatzes mit entsprechender Zuordnung der Teile zu den 
jeweiligen Modulen. Sie wird in verschiedenen Prozessabläufen der Freigabe oder Auswertung 
benötigt.  
Damit man diese einfache Basisinformation nicht jedes Mal aus der relativ komplexen Datenstruktur 
der KBL auslesen muss, wird die Datei aus dem Datenmodell des jeweiligen Autorensystems bei der 
Erstellung des HCV Containers erzeugt, oder aber von anderen Systemen generiert und in den 
Container eingestellt. 
 
Die Daten der Index.xml müssen mit den Daten der KBL-Datei in Version und Inhalt übereinstimmen: 
 
 
Abbildung 73: Darstellung Verbindung Index.xml und Harness.kbl 
Der E3.HarnessAnalyzer kann diese Datei, sofern vorhanden, lesen und deren Inhalt visualisieren. Die 
Funktion ist unter dem Menüpunkt BOM (Active Modules / All Modules) zu finden. 
 
Alle Attribute bzw. Attributknoten müssen geschrieben werden (die min und max Occurence sind in 
der entsprechenden Schemadatei auf 1 gesetzt) 
Das XML Schema der Index XML gliedert  sich in drei Teilbereiche: 
 
1. Die Metadaten des Harness-Containers: 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 130 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 130 von 178 
 
 
ID <string>: 
Identifier des Containers oder der Datei. Dieser wird zu Zeit nicht weiter ausgewertet und ist 
nur vorgehalten. 
 „id_08_15“ 
 
creationTimestamp<string>: 
Zeitstempel der Erstellung der Index XML. Hier wird aus Gründen der Kompatibilität zurzeit ein 
String Datentyp verwendet, wobei das Format YYYY-MM-DD HH:MM:SS vorzusehen ist: 
„2014-06-01 20:07:34“ 
 
creatingSystem<string>: 
Name des Autorensystems von dem die Index XML generiert worden ist. 
 „HarnessDesigner 1.5“ 
 
supplier<string>: 
Lieferantenname aus dem CONNECT-Teilekatalog, der die Index XML generiert hat. 
„Automotive Systems“ 
 
harnessDrawing<string>: 
Sachnummer des Harness Masters. Die Sachnummer ist ohne Leerzeichen oder sonstige 
Sonderzeichen zu schreiben. 
„A2225403908“ 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 131 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 131 von 178 
 
2. Die Harness-Module: 
In diesem Abschnitt werden alle Module des Harness Masters aufgeführt. 
 
partNumber<string>: 
Sachnummer des Moduls. Die Sachnummer ist ohne Leerzeichen oder sonstige 
Sonderzeichen zu schreiben. 
„A2225404108“ 
 
version<string>: 
Datenstand des Moduls. Hier kann eine Versionsinformation eingetragen werden. Im Daimler 
Umfeld ist die Datumsinformation des Tagesstandes im Format YYYY-MM-DD zu verwenden. 
„2011-11-07“ 
 
description<string>: 
Beschreibung bzw. Benennung des Moduls. 
„ZB EL.LTG.SATZ LL COCKPIT RDU“ 
 
code<string>: 
Logistik-Kontrollinformation in Form eines Optionscodes. 
„+LL+TY0+889“ 
 
kzflag<boolean> („keine Zeichnung Flag“): 
Wenn das Modul keine eigene Zeichnung hat, wird das Flag auf true gesetzt. Bei 
Masterumfängen ist das Flag automatisch auf true zu setzen, da alle Module als („kZ-siehe“) 
abgebildet sind. 
Mit folgender Vorschrift lässt sich das kzflag ermitteln: 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 132 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 132 von 178 
 
Wenn der Eintrag der harnessDrawing in der Sektion harnessContainer leer ist, wird das kzflag 
auf false gesetzt. Anderenfalls ist dieser Eintrag mit dem Eintrag der partNumber unter der 
Sektion harnessModule zu vergleichen (Case Insensitive). Bei Ungleichheit ist das Flag auf 
true zu setzten, sonst verbleibt es false. 
 
3. Die zu einem Modul gehörigen Teile: 
 
 
 
partNumber<string>: 
Sachnummer des Teils. Die Sachnummer ist ohne Leerzeichen oder sonstige Sonderzeichen 
zu schreiben. 
„A2129820126“ 
 
drawing<string>: 
Zeichnungsnummer, auf der das Teil dargestellt ist. 
„A2129820126“ 
 
 
supplier<string>: 
Lieferantenname des Teils oder entsprechendes Kurzzeichen aus ConnectPARTS. 
 „Coroplast“ 
 
occurences<integer>: 
Anzahl der Teile. Im Falle von Bandierung wird hier die Anzahl der bandierten Segmente  
angegeben. „5“ 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 133 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 133 von 178 
 
 
description<string>: 
Benennung des Teils. (auf der Zeichnung/Schriftfeld wird die SRM-Benennung dargestellt, 
siehe Norm MBN 31 020-1) 
„Buchsenkontakt (gerade) Au 0.14-0.14“ 
 
kZflag<boolean>(„keine Zeichnung Flag“): 
Einzelteile habe in der Regel keine eigene Zeichnung, daher ist das Flag hier immer auf true zu 
setzen (-> siehe Tabellenzeichnung). 
Ausschnitt aus einer Index XML: 
 
Abbildung 74: Ausschnitt einer Index-XML 
8.6.4 SVG 
Ein HCV Container muss mindestens eine SVG enthalten. Bei Autorensystemen mit Mehrblattfähigkeit 
soll für jedes Blatt eine einzelne SVG ausgeleitet und in den Container eingebracht werden. Die 
Dateinamen der SVGs entsprechen hierbei der Bezeichnung des jeweiligen Blattes (z.B. „Seite 1“, 
„Stücklisten“) mit der Dateiendung „.svg“. Die SVG Dateien enthalten die grafische Repräsentation der 
Leitungssatzzeichnung, wie sie dem Anwender im Autorensystem dargestellt wird. 
 
Dieser Stand der Spezifikation beschreibt die Strukturen, die der E3.HarnessAnalyzer in der Version 
6.1 unterstützt. Als Referenz für Fragen der Darstellung kann der Microsoft Internet Explorer in der 
Version 9 verwendet werden. 
Es sind in zukünftigen Versionen einige Änderungen nötig und sinnvoll, um besonders im Bereich der 
SVG 
Modellierung 
mehr 
Flexibilität 
und 
Dateneffizienz 
zu 
ermöglichen. 
In 
weiteren 
Entwicklungsschritten sollen die zusammengehörigen Leitungssätze eines gesamten Fahrzeuges mit in 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 134 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 134 von 178 
 
die Visualisierung einbezogen werden, um auch die Übergänge und Trennstellen zwischen den 
Leitungssätzen darstellen zu können. Damit steigen die Anforderungen an die zu verarbeitenden 
Datenmengen und Dateigrößen nochmals deutlich an. Daher ist bei der Generierung des HCV 
Containers und besonders der SVG Daten auf schlanke Strukturen und effiziente Darstellung zu 
achten. 
 
Die SVG orientiert sich an dem W3C Standard (http://www.w3.org/TR/SVG11), aber es werden in 
der aktuellen Version nur bestimmte Elemente unterstützt, und innerhalb des jeweiligen Elementes 
sind nur bestimmte Spezifizierer bzw. Attribute zugelassen. Diese werden in den nachfolgenden 
Abschnitten behandelt. 
 
Auch in zukünftigen Versionen werden bestimmte Elemente nicht unterstützt werden. Hierzu gehört in 
jedem Falle das „ClipPath“ Element. „ClipPathes“ werden bei der Verarbeitung der SVG und der 
Darstellung ignoriert. 
 
SVG Befehle und Attribute, die nicht unterstützt werden, dürfen trotzdem in der SVG vorkommen, sie 
werden vom Browser einfach ignoriert. 
 
In einer SVG werden zu Beginn die Breite und Höhe sowie die Größe der Viewbox angegeben. Damit 
die SVG z.B. auch im Internet Explorer oder anderen Programmen für sich alleine darstellbar ist, 
müssen diese Werte entsprechend bei der Generierung beschrieben werden. Der EE Browser 
verwendet diese Information nicht, er ermittelt die Koordinaten der umgebenden Begrenzung selbst 
und alle Anzeigen basieren auf dem World Koordinatensystem (WCS). Die Verwendung von User-
Koordinatensystemen und verschiedenen Einheiten wird nicht unterstützt. 
 
Die Hintergrundfarbe der Dokumente ist fest auf weiß eingestellt und kann nicht vorgegeben werden. 
******* Unterstützte SVG Elemente 
Die grafischen Elemente und Gruppen können innerhalb des SVG Dokumentknotens in beliebiger 
Reihenfolge angeordnet werden, wobei eine strukturierte Auflistung die Lesbarkeit deutlich 
vereinfacht. 
 
Gruppe 
*******.1
<g transform(translate,rotate,scale)/> 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 135 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 135 von 178 
 
Das Gruppenelement wird zur Gruppierung von zusammengehörigen Basiselementen benötigt. 
Gruppen können in beliebiger Tiefe geschachtelt werden. Dieses Element wird für alle 
Zusammenfassungen von Business Objekten innerhalb des Browsers gebraucht. Z.B. Steckertabellen, 
Steckeransichten usw. 
Beispiel: 
<g> 
    <g transform="translate(8976.7,46.8) rotate(-0.97)"> 
      <line x1="4" y1="0" x2="117" y2="0" style="stroke:black; stroke-width:0.5"/> 
      <polygon points="0 0 ,4 -1 ,4 1" style="fill:black" /> 
    </g> 
 </g> 
 
Linie 
*******.2
<line x1,y1,x2,y2,style(stroke;stroke-width)/>  
 
x1,y1: Startkoordinaten im WCS 
x2,y2: Endkoordinaten im WCS 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der Farbangabe ist 
sowohl die Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig.  
 
 
Beispiel: 
<line x1="25" y1="10" x2="25" y2="15" style="stroke:black; stroke-width:0.5 " /> 
 
 
 
 
Oder  
 
<line x1="25" y1="10" x2="25" y2="15" style="stroke: rgb(100,113,207); stroke-width:0.5 " /> 
 
 Polylinie 
*******.3
<polyline points(list of points) style(fill; opacity; stroke; stroke-width) /> 
 
points: eine Liste von XY Paaren im WCS 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der Farbangabe ist 
sowohl die Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig. Das 
fill Attribut legt die Füllfarbe fest oder steht auf none. Ein opacity Wert von 0 setzt die Füllung auf 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 136 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 136 von 178 
 
„ungefüllt“.  
 
Beispiel: 
<polyline points="8.453 -20.554,11.034 -8.973 ,8.41 -8.322 ,8.215 -9.189 " style="stroke:black; stroke-
width:0.1; fill:none" /> 
 
 Polygon 
*******.4
< polygon points(list of points) style(fill; opacity; stroke; stroke-width) /> 
 
points: eine Liste von XY Paaren 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der Farbangabe ist 
sowohl die Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig. Das 
fill Attribut legt die Füllfarbe fest oder steht auf none. Ein opacity Wert von 0 setzt die Füllung auf 
„ungefüllt“.  
 
Beispiel: 
<polygon points="-0.462 3.5 ,1.813 1.225 ,13.187 1.225" style="stroke:rgb(78,113,207); stroke-width:0.5; 
fill:none" /> 
 
 Path 
*******.5
<path d(A, a, C, c, Q, q, S, s, T, t, H, h, L, l, M, m V, v) style(fill, opacity, stroke, stroke-width) /> 
 
Angaben in Großbuchstaben kennzeichnen absolute Koordinaten, mit Kleinbuchstaben sind relative 
Koordinaten bezeichnet. 
 
 
A, a: Arc-Definition 
 
C, c, Q, q, S, s, T, t: Curve-Definition 
 
H, h: Horizontal-Line-Definition  
 
L, l: Line-Definition 
 
M, m: MoveTo-Definition 
 
V, v: Vertical-Line-Definition 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der 
Farbangabe ist sowohl die Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein 
RGB Wert zulässig. Das fill Attribut legt die Füllfarbe fest oder steht auf none. Ein opacity Wert 
von 0 setzt die Füllung auf „ungefüllt“.  
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 137 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 137 von 178 
 
 
Beispiel: 
<path d="M-8 -4 A17 17,0,0,1,8 -4" style="stroke:black; stroke-width:0.25; fill:none" /> 
 
 Circle 
*******.6
<circle cx,cy,r,style(fill, opacity, stroke, stroke-width)/> 
 
cx,cy: Ursprungskoordinaten in WCS 
r: Radius 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der Farbangabe ist 
sowohl die Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig. Das 
fill Attribut legt die Füllfarbe fest oder steht auf none. Ein opacity Wert von 0 setzt die Füllung auf 
„ungefüllt“.  
 
Beispiel: 
<circle cx="6545" cy="393" r="0.5" style="fill:rgb(78,113,207)" /> 
 
 Ellipse 
*******.7
<ellipse cx,cy,rx,ry,style(fill, opacity, stroke, stroke-width)/> 
 
cx,cy: Ursprungskoordinaten in WCS 
rx,ry: X- und Y-Achsen Radius 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der Farbangabe ist 
sowohl die Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig. Das 
fill Attribut legt die Füllfarbe fest oder steht auf none. Ein opacity Wert von 0 setzt die Füllung auf 
„ungefüllt“.  
 
Beispiel: 
< ellipse cx="6545" cy="393" rx="0.5" ry="1" style=" fill:none  stroke:blue  stroke-width:20/> 
 
 Rect 
*******.8
<rect x,y,width,height,style(fill, opacity, stroke, stroke-width) /> 
 
x,y: Ursprungskoordinaten in WCS 
width: Breite 
height: Höhe 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 138 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 138 von 178 
 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der Farbangabe ist 
sowohl die Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig. Das 
fill Attribut legt die Füllfarbe fest oder steht auf none. Ein opacity Wert von 0 setzt die Füllung auf 
„ungefüllt“. 
Die Werte Rx und Ry für gerundete Ecken werden in der aktuellen Version nicht unterstützt. 
 
Beispiel: 
<rect x="0" y="0" width="15" height="7" style="fill:rgb(255,255,255); opacity:0" /> 
 
 Text 
*******.9
<text x,y, font-weight, style (fill, font-family, font-size, text-anchor, text-decoration)>Value</> 
 
x,y: Ursprungskoordinaten in WCS, eine Liste von Punkten wird hier nicht unterstützt 
font-weight: Bei diesem Attribut wird nur der Wert “bold” verwendet.  
 
style: Mit dem Attribut text-anchor kann die horizontale Ausrichtung angegeben werden. Die font-
family erlaubt die Angabe des Fonts. Font-Verweise sind nicht möglich. Dieser muss auf dem 
Zielsystem verfügbar sein, ansonsten wird auf einen Default zurückgegriffen. Über das Attribut fill 
kann die Farbe des Textes gesetzt werden. Bei der Farbangabe ist sowohl die Verwendung des 
Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig. Mit der text-decoration können 
Unter-, Über-, und Durchstreichungen erstellt werden. Mit der font-size kann die Größe eingestellt 
werden. 
 
Beispiel: 
<text x="375" y="13.8"  font-weight="bold"  style="text-anchor:middle; font-family:Arial; font-size:3px; 
fill:black; text-decoration:underline">Dies ist ein Text</text> 
 Tspan 
*******.10
<tspan x,y,font-weight,style(text-anchor, text-decoration)>Value</ tspan > 
 
x,y: Ursprungskoordinaten in WCS, eine Liste von Punkten wird hier nicht unterstützt  
font-weight: Bei diesem Attribut wird nur der Wert “bold” verwendet.  
 
style: Mit dem Attribut text-anchor kann die horizontale Ausrichtung angegeben werden. Mit der text-
decoration können Unter-, Über-, und Durchstreichungen erstellt werden. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 139 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 139 von 178 
 
 
Der Tspan wird nur in Verbindung mit einem Text Element verwendet. Es werden die Angaben für die 
Größe, Farbe, Font vom Text Element übernommen. 
Beispiel: 
<text x="0" y="10" style="text-anchor:middle; font-family:Arial; font-size:12px; fill:black">X30/20<tspan 
x="0" y="3.214" style="text-anchor:middle">nach elektrischer Prüfung</tspan><tspan x="0" y="17.013" 
style="text-anchor:middle">alle Abgriffe in CAN-Verteiler stecken</tspan></text> 
8.6.4.2 Transformationen 
 
 Matrix 
8.6.4.2.1
<matrix(a,b,c,d,e,f)> 
 
Es werden die Matrix Transformationen unterstützt. Dabei können die Matrixelemente 
A00:a 
A01:c 
A10:b 
A11:d 
A03:e 
A13:f 
verwendet werden (siehe auch W3C). 
 
Anordnung der Matrix Elemente in der SVG Transformationsmatrix: 
 
 
 
Beispiel: 
<g transform="matrix(-1,0,0,-1,-4.4,39.6)"> 
 
 Rotate 
8.6.4.2.2
<rotate(angle)> 
Der Rotationswinkel ist in Grad anzugeben! 
 
Beispiel: 
<g transform="rotate(-90)" /> 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 140 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 140 von 178 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 141 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 141 von 178 
 
 
Scale 
8.6.4.2.3
<scale(x,y)> 
Beispiel: 
<g transform="scale(1,2)"> 
 
 Translate 
8.6.4.2.4
<translate(x,y)> 
 
Beispiel: 
<g transform="translate(0,1990) rotate(-90)" /> 
 
Hinweis: die Skew Kommandos werden nicht unterstützt. 
******* Wichtige Hinweise 
Grundsätzlich gibt es zwei Möglichkeiten Texte in einer SVG zu transportieren. Zum einen können SVG 
Text Elemente verwendet werden (siehe oben), zum anderen können Texte als gefüllte Polygone 
gewandelt werden. Viele SVG Konverter nutzen die zweitgenannte Möglichkeit, da man hier auf dem 
Zielrechner keine Information über den verwendeten Font benötigt. In typische CAD Zeichnungen lässt 
sich dieser Ansatz prinzipiell auch verwenden, da man hier nur wenige Texte hat. 
  
Für den Einsatz im HCV Container ist nur der erstgenannte Weg über die Verwendung von Text 
Elementen sinnvoll, da die SVG Dateigröße sonst viel zu groß wird, speziell bei entsprechenden 
Innenraumleitungssätzen! 
 
Weiterhin sollte darauf geachtet werden, nicht unnötig viele Koordinatensätze zu produzieren. Dies 
passiert häufig bei der Darstellung von z.B. Steckerbildern, die aus 3D Modellen mittels bestimmter 
Konverter in 2D überführt werden. Hier werden dann oft unzählige Polylinien mit vielen Vertexes 
generiert, die für die grafische Darstellung eigentlich völlig überflüssig sind, in ihrer Anzahl aber auch 
signifikant zum Speicherverbrauch beitragen. 
 
Die Verwendung von Pattern-Definitionen und Dash-Arrays wird in der aktuellen Version noch nicht 
unterstützt, da die rückwärtige Umsetzung von dieser SVG Informationen innerhalb des Browser nicht 
zu der gleichen Darstellung führt. 
 
Textpathes (Texte, die beliebigen Verläufen folgen) werden nicht unterstützt. 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 142 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 142 von 178 
 
8.6.4.4 Verlinkung zwischen SVG und KBL 
Um die Navigation zwischen Grafik und physikalischen Elementen zu ermöglichen, wird von der SVG 
aus innerhalb der Gruppenelemente auf Elemente der KBL über den internen KBL-Identifier (id) mittels 
definierter Kommentare verwiesen. 
Die in der KBL vergebenen Identifier für die verschieden Objekte sind nach folgendem Muster 
aufgebaut. Details zu den KBL vergebenen Identifier können im Kapitel Namenskonventionen für 
technische Ids ("Klein-id") in den Occurrence-Klassen nachgelesen werden. 
 
Diese Vorgehensweise ist bei Daimler zu verwenden, da eine SVG Schemaerweiterung einen 
entsprechenden Aufwand auch in der Verwaltung und Bereitstellung des Schemas bedeutet hätte. 
Zudem ist diese Erweiterung einfacher in vorhandene Autorensysteme einzubringen. 
 
Ist das erste Element innerhalb einer Gruppe ein solcher Kommentar <!--kbl-id:xyz ……-->, so wird er 
entsprechend ausgewertet; anderenfalls dient das Gruppenelement rein der Strukturierung. 
 
Dieser Kommentar enthält ein oder mehrere Identifier von KBL Elementen, die zu dieser Gruppe 
gehören. Mehrere Identifier sind durch Leerzeichen zu separieren. 
Die damit beschriebene Verlinkung kann entweder eine „ist“ (z.B. Steckersymbol) oder eine „gehört 
zu“ (z.B. Steckertabelle) Beziehung darstellen. Im zweiten Fall kann der Kommentar um einen 
zusätzlichen Typ-Spezifizierer (siehe folgender Abschnitt) erweitert werden, der es dem Browser 
ermöglicht, die verschiedenen Repräsentationen eines physikalischen Objektes gegebenenfalls auch 
unterscheiden zu können. 
Ein Steckersymbol (face view) wird z.B. wie folgt gekennzeichnet: 
 
<g> 
   <!--kbl-id:ID_CON167--> 
   …Grafikelemente des Steckersymbols… 
</g> 
8.6.4.5 Typ-Spezifizierer 
Folgende Typ-Spezifizierer sind bisher vereinbart: 
 
type:ref 
8.6.4.5.1
Der type:ref  bezeichnet ein allgemein referenzierendes Objekt ohne weitere Präzisierung. Es wird 
verwendet, wenn z.B. spezielle Zusatzgrafik wie Bezugspfeile oder Textfahnen mit Objekten verbunden 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 143 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 143 von 178 
 
werden soll. Elemente vom Typ ref werden nicht in die Selektion genommen und auch bei bestimmten 
Ansichten, wie z.B. der Start-End Connector Ansicht nicht dargestellt. 
 
<g> 
<!--kbl-id:ID_CON1;type:ref-->  
…Irgendwelche grafischen Elemente zum Stecker … 
</g> 
 
type:table 
8.6.4.5.2
Der type:table bezeichnet die Repräsentation von Tabellen als Ganzes (z.B. Steckertabelle). Innerhalb 
dieser Struktur können Zeilen und Zellen angegeben werden. 
 
<g> 
<!--kbl-id:ID_CON1;type:table-->  
…Inhalte und grafische Elemente der Tabelle … 
</g> 
 
type:row 
8.6.4.5.3
Der Type:row bezeichnet die Repräsentation einer inhaltlichen Zeile (nicht Überschrift) innerhalb einer 
Tabelle. 
 
<g> 
<!--kbl-id:ID_CON1;type:table-->  
…Inhalte und grafische Elemente der Tabelle … 
<g>  
<!--kbl-id:ID_W1;type:row-->  
…Inhalt der Tabellenzeile mit einem Bezug zur Leitung ID_W1… 
</g>  
</g> 
 
type:cell 
8.6.4.5.4
Der type:cell bezeichnet die Repräsentation einer Zelle innerhalb einer Zeile in der Tabelle. 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 144 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 144 von 178 
 
<g> 
<!--kbl-id:ID_CON1;type:table-->  
 …Inhalte und grafische Elemente der Tabelle … 
<g>  
<!--kbl-id:ID_W1;type:row-->  
…Inhalt der Tabellenzeile mit einem Bezug zur Leitung ID_W1… 
<g>  
<!--kbl-id:ID_SEA1;type:cell-->  
…Inhalt der Tabellenzelle mit einem Bezug zur Einzeldichtung SEA1…  
</g> 
</g>  
</g> 
 
type:dimension 
8.6.4.5.5
Der type:dimension  bezeichnet die Repräsentation eines Bemaßungsobjektes. Bemaßungsobjekte 
verweisen in der Regel auf die beiden Vertex-Identifier des entsprechenden Segments, für das die 
Bemaßung gilt (BNJ-BundleJunction). Im nachfolgenden Beispiel sind der Bemaßungstext und der 
Maßpfeil voneinander getrennt geschrieben worden. Das ist nicht zwingend nötig, findet sich aber in 
einigen Beispieldateien; im dritten Gruppenabschnitt ist das zusammengefasst worden. 
Beispiel: 
Bemaßungstext 
<g> 
<!--kbl-id:ID_BNJ1 ID_BNJ109;type:dimension--> 
<g transform="translate(6739.5,3) rotate(0) translate(0,-10)"> 
<text x="0" y="1.3" style="text-anchor:middle; font-family:CorpoS; font-size:5px; text-
decoration:underline; fill:black">50</text> 
</g> 
</g> 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 145 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 145 von 178 
 
Maßpfeil 
<g> 
<!--kbl-id:ID_BNJ1 ID_BNJ109;type:dimension--> 
<g transform="translate(6739.5,37) rotate(0)"> 
<line x1="-68.5" y1="-1" x2="-68" y2="-11" style="stroke:black; stroke-width:0.25" /> 
 <polygon points="-68.5 -10 ,-64 -11 ,-64.5 -9" style="fill:black" /> 
 <line x1="-3.5" y1="-10" x2="-64" y2="-10" style="stroke:black; stroke-width:0.25" /> 
 <line x1="68.5" y1="-1" x2="68" y2="-11" style="stroke:black; stroke-width:0.25 " /> 
 <polygon points="68.5 -10 ,64 -11 ,64.5 -9" style="fill:black" /> 
 <line x1="3.5" y1="-10" x2="64" y2="-10" style="stroke:black; stroke-width:0.25" /> 
 </g> 
</g> 
 
 
Zusammengefasst: 
<g> 
 <!--kbl-id:ID_BNJ1 ID_BNJ109;type:dimension--> 
 <g transform="translate(6739.5,3) rotate(0) translate(0,-10)"> 
<text x="0" y="1.3" style="text-anchor:middle; font-family:CorpoS; font-size:5px; text-
decoration:underline; fill:black">50</text> 
</g> 
<g transform="translate(6739.5,377) rotate(0)"> 
<line x1="-68.5" y1="-1" x2="-68" y2="-11" style="stroke:black; stroke-width:0.25" /> 
<polygon points="-68.5 -10 ,-64 -11 ,-64.5 -9" style="fill:black" /> 
<line x1="-3.5" y1="-10" x2="-64" y2="-10" style="stroke:black; stroke-width:0.25" /> 
<line x1="68.5" y1="-1" x2="68" y2="-11" style="stroke:black; stroke-width:0.25 " /> 
<polygon points="68.5 -10 ,64.5 -11 ,64.5 -9" style="fill:black" /> 
<line x1="3.5" y1="-10" x2="64" y2="-10" style="stroke:black; stroke-width:0.25" /> 
</g> 
</g> 
 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 146 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 146 von 178 
 
 
type:DocumentFrame 
8.6.4.5.6
Der type:DocumentFrame  bezeichnet die Gruppe des Blattrahmens. In dieser Gruppe sind alle 
grafischen Objekte unterzubringen, die für den Blattrahmen benötigt werden (Planquadratangaben 
usw.) 
<g> 
<!--kbl-id:DocumentFrame--> 
<rect x="0" y="0" width="10289" height="841" style="fill:rgb(255,255,255)" /> 
<rect x="0" y="0" width="10289" height="841" style="stroke:black; stroke-width:0.01; fill:none" 
/> 
<rect x="20" y="10" width="10259" height="821" style="stroke:black; stroke-width:0.5; fill:none" 
/> 
…. 
</g> 
 
******* Objekte und ihre Darstellung 
 
 Steckersymbol 
*******.1
Das Steckersymbol verweist auf das entsprechende Connector Objekt in der KBL. Hier ist es in der 
Gruppe der zugehörigen Vertex angelegt, sodass bei Selektion der Vertex das Symbol automatisch mit 
in die Selektion genommen wird. Die Vertex selbst hat keine Grafik - sie ist durch das Steckersymbol 
repräsentiert – hier ist nur der Verweis auf den zugehörigen node in der KBL eingetragen. (Kapitel 
Vertex). 
 
 
 
 
 
 
1 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 147 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 147 von 178 
 
SVG Teilausschnitt: 
<g> 
<!--kbl-id:ID_BNJ79--> 
<g> 
<!--kbl-id:ID_CON54--> 
<g transform="translate(3757.08,212.09) rotate(257.26) translate(-18.54,-2.1)"> 
<g transform="translate(0,0) scale(1,1) translate(18,25)"> 
<g transform="matrix(0,1,-1,0,-17.599,-25.599)"> 
<text x="4" y="-21.599" style="text-anchor:start; font-family:Arial; font-size:2.74px; 
fill:black">10</text>  
    …und weitere Kammertexte… 
</g> 
 
<line x1="-3" y1="21" x2="-3" y2="19" style="stroke:black; stroke-width:0.25 " /> 
  …und weitere Grafik… 
</g> 
</g> 
</g> 
</g> 
 
 
 Steckertabelle 
*******.2
Die Steckertabelle verweist auf das entsprechende Connector Objekt in der KBL. Sie wird über die 
Typ-Angabe spezifiziert. Zu Beginn werden die grafischen Elemente des Tabellenkopfes beschrieben. 
Nachfolgend kommt die Angabe der Spaltenüberschriften. 
Im Anschluss daran werden die einzelnen Zeilen jeweils als eine Gruppe geschrieben und auf das 
zugehörige Wire Objekt der KBL verwiesen, wobei diese Verweise vom Typ row zu setzen sind. 
Innerhalb einer Zeile werden Untergruppen verwendet, um die Zellinhalte für z.B. Kontaktsachnummer 
zu spezifizieren. Diese Untergruppen verweisen auf das Termination Objekt in der KBL und müssen auf 
den Typ cell gesetzt  werden. Im nachfolgenden Beispiel sind die Kontaktsachnummer und das 
Kontaktmaterial in einer solchen Untergruppe und können daher entsprechend ein- und ausgeblendet 
werden. 
Die Zellränder und Füllungen sind hier als gefüllte Rechtecke ausgeführt. Dabei ist zuerst der 
Zellhintergrund getrennt von der Umrandung angegeben. Idealerweise setzt man besser die Zellfüllung 
auf ungefüllt und beschreibt den Hintergrund der Zelle gar nicht weiter, denn der Rendering Prozess 
1 
Vertex des Symbols 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 148 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 148 von 178 
 
macht in bestimmten Ansichten aus explizit beschriebenen Farben einen grauen Hintergrund. 
 
 
 
SVG Teilausschnitt: 
 
 
<g> 
    <!--kbl-id:ID_CON54;type:table--> 
    <g transform="translate(3810.0,149.09) rotate(0)"> 
      <g> 
        <rect x="0" y="0" width="33" height="6" style="fill:rgb(255,255,255); opacity:0" /> 
<rect x="0" y="0" width="33" height="6" style="stroke:rgb(255,255,255); opacity:0; stroke-
width:0.25; fill:none" /> 
<text 
x="0.833" 
y="4.332" 
style="text-anchor:start; 
font-family:CorpoS; 
font-size:5px; 
fill:black">Kundenteilenr.:</text>   
…. 
      </g> 
 
      <g> 
 <rect x="0" y="25" width="8" height="6" style="stroke:black; stroke-width:0.25; fill:none" /> 
<text 
x="4" 
y="29.332" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
1 
2 
3 
5 
4 
6 
1 
Hintergrund und Zelle getrennt 
 Zelle mit „transparentem“ Hintergrund 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 149 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 149 von 178 
 
fill:black">Pin</text>  
      … 
        <g> 
<!--kbl-id:ID_WIR1239;type:row--> 
<rect x="0" y="31" width="8" height="6" style="fill:rgb(255,255,255)" /> 
<rect x="0" y="31" width="8" height="6" style="stroke:black; stroke-width:0.25; fill:none" /> 
<text 
x="4" 
y="35.332" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
fill:black">1</text> 
 
<rect x="8" y="31" width="18" height="6" style="fill:rgb(255,255,255)" /> 
<rect x="8" y="31" width="18" height="6" style="stroke:black; stroke-width:0.25; fill:none"/> 
<text 
x="17" 
y="35.332" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
fill:black">57</text> 
         ….. 
          <g> 
<!--kbl-id:ID_TER1837;type:cell--> 
<rect x="83" y="31" width="32" height="6" style="fill:rgb(255,255,255)" /> 
<rect x="83" y="31" width="32" height="6" style="stroke:black; stroke-width:0.25; fill:none" 
/> 
<text x="99" y="35.332" style="text-anchor:middle; font-family:CorpoS; font-size:5px; 
fill:black">A0055457626</text> 
          </g> 
          <g> 
            <!--kbl-id:ID_TER1837;type:cell--> 
            <rect x="115" y="31" width="27" height="6" style="fill:rgb(255,255,255)" /> 
<rect x="115" y="31" width="27" height="6" style="stroke:black; stroke-width:0.25; 
fill:none" /> 
<text x="128.5" y="35.332" style="text-anchor:middle; font-family:CorpoS; font-size:5px; 
fill:black">Sn</text>  
          </g> 
        </g> 
      </g> 
    </g> 
</g> 
2 
3 
5 
4 
6 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 150 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 150 von 178 
 
 
Zubehörteile 
*******.3
Zubehörteile zum Stecker werden meist unterhalb der Steckertabellen dargestellt. Sie sind innerhalb 
der Gruppe der Steckertabelle angeordnet. Diese Teile verweisen in der Regel auf Accessory Objekte 
der KBL. Manchmal wird auch auf ein Component Objekt verwiesen (meist bei Sicherungen, wie hier 
im Beispiel). 
Im Grunde handelt es sich bei dieser Darstellung wieder um eine Tabelle, und die einzelnen Zeilen 
werden wieder entsprechend über den Typ als row gekennzeichnet. 
 
SVG Teilausschnitt: 
<g> 
   <!--kbl-id:ID_CON132;type:table--> 
…..Hier ist der gesamte Block der eigentlichen Steckertabelle zu finden (siehe auch Abschnitt 
Steckertabelle)… 
<g> 
<rect x="0" y="75" width="35" height="7" style="stroke:rgb(255,255,255); opacity:0; stroke-
width:0.25; fill:none" /> 
<text x="0.833" y="79.756" font-weight="bold" style="text-anchor:start; font-family:CorpoS; font-
size:5px; fill:black">Zubehörteile zu K40/6*S1-B SRB li.</text>  
 
<rect x="0" y="82" width="35" height="6" style="stroke:rgb(255,255,255); opacity:0; stroke-
width:0.25; fill:none" /> 
<rect x="35" y="82" width="13" height="6" style="stroke:rgb(255,255,255); opacity:0; stroke-
width:0.25; fill:none" /> 
<text 
x="35.833" 
y="86.332" 
style="text-anchor:start; 
font-family:CorpoS; 
font-size:5px; 
1 
2 
1 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 151 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 151 von 178 
 
fill:black">Farbe</text> 
….. 
       <g> 
         <!--kbl-id:ID_ACC54;type:row--> 
<rect x="0" y="88" width="35" height="6" style="stroke:rgb(255,255,255); opacity:0; stroke-
width:0.25; fill:none" /> 
<text 
x="0.833" 
y="92.5" 
style="text-anchor:start; 
font-family:CorpoS; 
font-size:5px; 
fill:black">Sicherung 5 A</text>    
…… 
       </g> 
    </g> 
</g> 
 
Zugehörige Beschreibung in der KBL bei Verweisen auf eine Komponente: 
<Component_occurrence id="ID_ACC54"> 
     <Id>ID_ACC54</Id> 
     <Mounting>ID_CON132 id_370_774</Mounting> 
     <Part>id_99902_1</Part> 
</Component_occurrence> 
 
<Component id="id_99902_1"> 
   <Part_number>N000000004202</Part_number> 
…. 
   <Version>20110329-ST30611</Version> 
   <Abbreviation>P0026</Abbreviation> 
   <Description>Sicherung 5 A</Description> 
    
   <Processing_information id="id_336_1"> 
     <Instruction_type>Type</Instruction_type> 
     <Instruction_value>Fuse</Instruction_value> 
   </Processing_information> 
 </Component> 
 
Zugehörige Beschreibung in der KBL bei Verweisen auf ein Accessory Objekt: 
2 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 152 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 152 von 178 
 
<Accessory_occurrence id="ID_ACC65"> 
     <Id>ID_ACC65</Id> 
     <Placement id="id_345_197"> 
… 
     </Placement> 
     <Part>id_300_5</Part> 
     <Reference_element>ID_CON18</Reference_element> 
     <Installation_information id="id_329_1027"> 
       <Instruction_type>Colour</Instruction_type> 
       <Instruction_value>violett</Instruction_value> 
     </Installation_information> 
   </Accessory_occurrence> 
 
Bezugspfeile zur Tabelle: 
*******.4
Der Bezugspfeil wird hier quasi wie ein Stück der Tabelle gesehen (type:table) und verweist 
dementsprechend auch wieder auf das Connector Objekt in der KBL. Dieser Bezugspfeil könnte 
prinzipiell auch direkt mit in die Definition der Steckertabelle aufgenommen werden. 
 
SVG Teilausschnitt: 
<g> 
 <!--kbl-id:ID_CON54;type:table--> 
 <g transform="translate(3757.0,212.0) rotate(19.7)"> 
 <line x1="4" y1="0" x2="56.303" y2="0" style="stroke:black; stroke-width:0.5" /> 
 <polygon points="0 0 ,4 -1 ,4 1" style="fill:black" /> 
 </g> 
 </g> 
 
Hinweistexte 
*******.5
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 153 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 153 von 178 
 
Hinweise zu z.B. Steckern oder auch andere Objekten verweisen auf das zugehörige KBL Objekt und 
werden mit dem Typ Spezifizierer type:ref gekennzeichnet. 
 
 
SVG Teilausschnitt: 
<g> 
<!--kbl-id:ID_CON84;type:ref--> 
<g transform="translate(5491.5,82.5) rotate(0)"> 
<rect x="-59.5" y="-23.5" width="119" height="47" style="stroke:black; stroke-width:0.5; 
fill:none" /> 
<text x="0" y="-14.57" style="text-anchor:middle; font-family:Arial; font-size:10px; fill:red"> 
<tspan x="0" y="-3.071" style="text-anchor:middle">V6 = 8-fach = Cod.A</tspan> 
<tspan x="0" y="8.428" style="text-anchor:middle">V10 = 4-fach = Cod.B</tspan> 
</text> 
</g> 
</g> 
 
Fixings 
*******.6
Befestigungselemente und Clips verweisen auf das entsprechende Fixing Objekt in der KBL. 
 
 
 
 
 
 
 
 
 
 
 
1 
2 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 154 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 154 von 178 
 
Die Tabellendarstellung in der SVG: 
<g> 
   <!--kbl-id:ID_FIX18;type:table--> 
   <g transform="translate(3415.6,342.39) rotate(0) translate(-3415.6,-342.3)"> 
     <g> 
<rect x="3415.622" y="342.399" width="33" height="7" style="stroke:rgb(255,255,255); 
opacity:0; stroke-width:0.25; fill:none" /> 
<text 
x="3416.455" 
y="347.155" 
xml:space="preserve" 
font-weight="bold" 
style="text-
anchor:start; font-family:CorpoS; font-size:5px; fill:black">FX.COC.0017 </text> 
       ...  
     </g> 
   </g> 
 </g> 
 
Die grafische Darstellung des Fixings in der SVG: 
<g> 
   <!--kbl-id:ID_FIX18--> 
   <g transform="translate(3501.6,372.3) rotate(129.7) translate(-12,-12)"> 
     <g transform="translate(0,0) scale(1,1) translate(12,12)"> 
       <line x1="-10" y1="-2.5" x2="10" y2="-2.5" style="stroke:black; stroke-width:0.25" /> 
        <path d="M5 -7 A1 1,0,0,0,4 -8" style="stroke:black; stroke-width:0.25; fill:none" /> 
        ...  
     </g> 
  </g> 
</g> 
 
Das Fixingelement in der KBL: 
<Fixing_occurrence id="ID_FIX18"> 
     <Id>ID_FIX18</Id> 
     <Alias_id id="id_302_328"> 
       <Alias_id>FX.COC.0017</Alias_id> 
     </Alias_id> 
     … 
     <Part>id_323_9</Part> 
     <Installation_information id="id_329_1145"> 
1 
2 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 155 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 155 von 178 
 
       <Instruction_type>ADDITIONAL_ATTRIBUTE</Instruction_type> 
       <Instruction_value>Additional Attribute</Instruction_value> 
     </Installation_information> 
     <Installation_information id="id_329_1146"> 
       <Instruction_type>CustomerDrawNo</Instruction_type> 
       <Instruction_value>A0009956290</Instruction_value> 
     </Installation_information> 
     <Installation_information id="id_329_1147"> 
       <Instruction_type>COMMENT_1</Instruction_type> 
       <Instruction_value>Kabelband ( Befestigungsclip)</Instruction_value> 
     </Installation_information> 
   </Fixing_occurrence> 
 
<Fixing id="id_323_9"> 
   <Part_number>A0009956190</Part_number> 
   <Version>20110811-NI</Version> 
   <Abbreviation>P3187</Abbreviation> 
   <Description>Kabelband ( Befestigungsclip)</Description> 
</Fixing> 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 156 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 156 von 178 
 
 
Splices 
*******.7
Splices verweisen auf das zugehörige Connector Objekt in der KBL und werden wie sonstige Stecker 
auch behandelt. 
 
 
SVG Teilausschnitt Symbol: 
<g> 
    <!--kbl-id:ID_BNJ3--> 
    <g> 
      <!--kbl-id:ID_CON128--> 
      <g transform="translate(2315,5) rotate(179.9) translate(-8.9,-8.0)"> 
        <g transform="translate(0,0) scale(1,1) translate(9,8)"> 
          <line x1="-2.5" y1="1.5" x2="-2.5" y2="-1.5" style="stroke:black; stroke-width:0.18" /> 
          …. 
        </g> 
      </g> 
    </g> 
</g> 
 
 
1 
2 
3 
4 
1 
Vertex 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 157 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 157 von 178 
 
SVG Teilausschnitt Tabelle: 
<g> 
    <!--kbl-id:ID_CON128;type:table--> 
    <g transform="translate(2342,518) rotate(0)"> 
      <g> 
 
... 
        <rect x="0" y="12" width="53" height="7" style="stroke:rgb(255,255,255); opacity:0; stroke-
width:0.25; fill:none" /> 
        <text x="0.833" y="16.756" font-weight="bold" style="text-anchor:start; font-family:CorpoS; font-
size:5px; fill:black">Z68/25*1-L Endh. Daten LIN3 Klima</text> 
      </g> 
      <g> 
<rect x="0" y="19" width="17" height="6" style="stroke:black; stroke-width:0.25; fill:none" /> 
<text 
x="8.5" 
y="23.332" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
fill:black">Ltg.-Nr.</text> 
       ... 
        <g> 
          <!--kbl-id:ID_WIR1081;type:row--> 
          ... 
<text 
x="8.5" 
y="29.332" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
fill:black">2647</text> 
         ... 
        </g> 
      </g> 
 
 
2 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 158 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 158 von 178 
 
SVG Teilausschnitt Zubehör: 
      <g> 
<rect x="0" y="45" width="47" height="7" style="stroke:rgb(255,255,255); opacity:0; stroke-
width:0.25; fill:none" /> 
<text x="0.833" y="49.756" font-weight="bold" style="text-anchor:start; font-family:CorpoS; 
font-size:5px; fill:black">Zubehörteile zu Z68/25*1-L Endh. Daten LIN3 Klima</text> 
 
....  
        <g> 
          <!--kbl-id:ID_ACC35;type:row--> 
<rect x="0" y="58" width="47" height="6" style="stroke:rgb(255,255,255); opacity:0; stroke-
width:0.25; fill:none" /> 
<text 
x="0.833" 
y="62.5" 
style="text-anchor:start; 
font-family:CorpoS; 
font-size:5px; 
fill:black">Gewebeband schwarz</text> 
<rect x="47" y="58" width="50" height="6" style="fill:rgb(255,255,255); opacity:0" /> 
         .... 
        </g> 
      </g> 
    </g> 
  </g> 
 
Zugehöriges KBL Element: 
<Connector_occurrence id="ID_CON128"> 
     <Id>Z68/25*1-L</Id> 
     <Alias_id id="id_302_298"> 
       <Alias_id>Z68/25*1-L</Alias_id> 
     </Alias_id> 
     <Description>Endh. Daten LIN3 Klima</Description> 
     <Usage>splice</Usage> 
      
     <Part>id_315_38</Part> 
     <Contact_points id="id_372_821"> 
       <Id>2877_0</Id> 
       <Contacted_cavity>id_370_954</Contacted_cavity> 
     </Contact_points> 
     <Contact_points id="id_372_822"> 
4 
3 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 159 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 159 von 178 
 
       <Id>2726_0</Id> 
       <Contacted_cavity>id_370_954</Contacted_cavity> 
     </Contact_points> 
     <Contact_points id="id_372_823"> 
       <Id>2647_0</Id> 
       <Contacted_cavity>id_370_954</Contacted_cavity> 
     </Contact_points> 
…. 
     <Slots id="id_386_128"> 
       <Part>id_341_1</Part> 
       <Cavities id="id_370_954"> 
         <Part>id_308_1</Part> 
       </Cavities> 
     </Slots> 
 </Connector_occurrence> 
 
 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 160 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 160 von 178 
 
 
Vertex 
*******.8
Eine einzelne Vertex verweist auf das entsprechende Node Objekt in der KBL. 
 
<g> 
<!--kbl-id:ID_BNJ130--> 
<circle cx="7250.14" cy="528.65" r="0.5" style="fill:rgb(78,113,207)" /> 
</g> 
 
Zugehöriges KBL Element: 
<Node id="ID_BNJ130"> 
    
 
<Id>ID_BNJ130</Id> 
    
 
<Cartesian_point>id_307_117</Cartesian_point> 
 </Node> 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 161 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 161 von 178 
 
 
Segment / Bandierung / Rohr 
*******.9
Segmente werden eigentlich über den darauf liegenden Leitungsschutz dargestellt. Sie verweisen auf 
das Segment Objekt der KBL (Bundlesegment). Innerhalb der Segmentgruppe verweist der 
Leitungsschutz auf das entsprechende Wire-Protection Objekt in der KBL. Hier ist ein teilbandierter 
Abschnitt gezeigt. 
 
 
Der SVG Ausschnitt der Segmentdarstellung: 
<g> 
<!--kbl-id:ID_BNS6--> 
<path d="M5026.643 392.017, l4.837 0, ……" style="stroke:rgb(78,113,207); stroke-width:0.25;    
fill:none" /> 
<g> 
<!--kbl-id:ID_TAP63--> 
<path d="M5026.643 392.016, l33.861 0, .... "style="stroke:rgb(78,113,207); stroke-  
width:0.25; fill:none" /> 
 </g> 
 </g> 
Der zugehörige KBL Abschnitt: 
<Segment id="ID_BNS6"> 
   <Id>ID_BNS6</Id> 
   <Virtual_length id="id_334_768"> 
     <Unit_component>id_346_1</Unit_component> 
     <Value_component>150</Value_component> 
   </Virtual_length> 
   <Physical_length id="id_334_767"> 
     <Unit_component>id_346_1</Unit_component> 
     <Value_component>150</Value_component> 
1 
2 
1 
2 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 162 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 162 von 178 
 
   </Physical_length> 
   <End_node>ID_BNJ113</End_node> 
   <Start_node>ID_BNJ35</Start_node> 
   <Center_curve id="id_306_6"> 
     <Degree>1</Degree> 
     <Control_points>id_307_31 id_307_103</Control_points> 
   </Center_curve> 
…. 
   <Protection_area id="id_337_9"> 
     <Start_location>0.2</Start_location> 
     <End_location>1</End_location> 
     <Associated_protection>ID_TAP63</Associated_protection> 
   </Protection_area> 
 </Segment> 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 163 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 163 von 178 
 
 Kabelkanäle 
*******.10
Kabelkanäle und ähnlich Teile werden wie Fixings behandelt und verweisen daher auf das Fixing 
Objekt in der KBL. 
 
Der SVG Ausschnitt der Tabelle:  
<g> 
    <!--kbl-id:ID_FIX2;type:table--> 
    <g transform="translate(6839,264) rotate(0) translate(-6839,-264)"> 
<rect x="6839" y="264" width="33" height="7" style="stroke:rgb(255,255,255); opacity:0;   
stroke-width:0.25; fill:none" /> 
<text x="6839.833" y="268.756" xml:space="preserve" font-weight="bold" style="text-
anchor:start; font-family:CorpoS; font-size:5px; fill:black">FX.COC.0001 </text> 
        …… 
    </g> 
  </g> 
 
 
1 
2 
1 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 164 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 164 von 178 
 
Der SVG Ausschnitt der Kabelkanal Symbolgrafik: 
  <g> 
    <!--kbl-id:ID_FIX2--> 
    <g transform="translate(6808,377) rotate(-359.0122) translate(-1,-55)"> 
      <g transform="translate(0,0) scale(1,1) translate(221,60)"> 
        <polyline points="-117.305 22.474 ,63.927 22.474 ……./> 
 
…… 
      </g> 
    </g> 
  </g> 
 
 
2 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 165 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 165 von 178 
 
 Uhrzeiten 
*******.11
Die Symbole der Uhrzeiten (Ausbindungsrichtungen) können prinzipiell auf verschiedene Objekte 
verweisen. Zum Beispiel kann das Uhrzeitensymbol auf den dazugehörigen Clip verweisen oder mit 
einer Vertex bzw. einem Segment verbunden werden. Sie selber haben kein physikalisches Objekt in 
der KBL. 
 
SVG Teilausschnitt: 
<g> 
    <!--kbl-id:ID_FIX1--> 
    <g transform="translate(315.9,589.2) rotate(-89.3) translate(-14,-13)"> 
<line x1="11.238" y1="-8.838" x2="-0.013" y2="-8.838" style="stroke:black; stroke-
width:0.25" />  
…. Uhrzeitengrafik… 
    </g> 
</g> 
 
 
1 
1 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 166 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 166 von 178 
 
 Bemaßungen 
*******.12
Bemaßungen verweisen auf die beiden zugehörigen Vertexes (Start- und Endpunkt) und werden mit 
dem Typ dimension gekennzeichnet.  
Im nachfolgenden Beispiel sind Text und Maßpfeil voneinander getrennt gruppiert worden, da hier 
unterschiedliche Transformationen angewendet werden. Die Bemaßung kann natürlich auch in einer 
einzigen Gruppe untergebracht werden. 
 
 
SVG Abschnitt einer Bemaßung: 
<g> 
    <!--kbl-id:ID_BNJ46 ID_BNJ125;type:dimension--> 
    <g transform="translate(7769.0,377.61) rotate(329.21) translate(0,-10)"> 
<text x="0" y="1.332" style="text-anchor:middle; font-family:CorpoS; font-size:5px; text-
decoration:underline; fill:black">100</text> 
    </g> 
  </g> 
  <g> 
    <!--kbl-id:ID_BNJ46 ID_BNJ125;type:dimension--> 
    <g transform="translate(7769.01,377.61) rotate(329.21)"> 
<line x1="104.748" y1="-1" x2="104.748" y2="-11" style="stroke:black; stroke-width:0.25" /> 
      <polygon points="104.748 -10 ,100.748 -11 ,100.748 -9" style="fill:red" /> 
<line x1="4.952" y1="-10" x2="100.748" y2="-10" style="stroke:black; stroke-width:0.25" /> 
    </g> 
  </g> 
 
 
2 
1 
1 
2 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 167 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 167 von 178 
 
8.6.4.7 Besonderheiten und implizite Logik bei der Darstellung  
Um eine sinnvolle Darstellung des Routings machen zu können, muss der Browser bezüglich der 
Segmentdarstellung eine besondere Logik anwenden. Segmente, die eigentlich über ihre 
Modulzugehörigkeit bzw. der Modulzugehörigkeit des entsprechend dargestellten Leitungsschutzes in 
einer Konfiguration ausgeblendet würden, müssen trotzdem dargestellt werden, wenn es Leitungen 
gibt, die in diesem Segment laufen und entsprechend ihrer Modulzugehörigkeit aktiv sind. 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 168 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 168 von 178 
 
9 Mitgeltende Unterlagen  
9.1 Normative Hinweise 
Das in dieser Ausführungsvorschrift abgebildete Zeichnungsschriftfeld, das Änderungsschriftfeld 
sowie normative Hinweise sind nur Beispielhaft, für die Aktualität und Ausführung der Zeichnung ist 
der Zeichnungsersteller verantwortlich. 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 169 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 169 von 178 
 
9.2 Normen und Vorschriften  
Alle in der folgenden Tabelle aufgeführten Dokumente sind über DocMaster verfügbar. 
Unterlage/Dokument Inhalte/ Benennung 
A0000026199 
AV Leitungen 
A0000069899 
Ausführungsvorschrift Schaltplanerstellung 
A0040028599 
Ausführungsvorschrift Leitungssatz-DMU Erstellung 
A0090000299 
Sachnummerzuordnung für Eigenschaften von Stützpunkten in elektrischen 
Leitungssätzen 
A999 8002 
Benennungsfestlegung 
für 
Abkürzungen 
im 
zweisprachigen 
(deutsch/englisch) Schriftfeld der MB-Zeichnungen 
A0598004 
Archivierungsrichtlinie Smaragd  
A0598030 
Dokumentation des ESD-Kenners in Smaragd 
A0598031 
Festlegung von Benennungen, Abkürzungen und Akronyme 
zu A- und H- Sachnummern für MBC und MB Vans 
CS080 
Leitfaden zur Erstellung von Leitungssatzkomponenten in Siemens NX 
DBL 8585 
Stoffnegativliste für die Werkstoffauswahl 
DIN 46234 
Kabelschuhe für lötfreie Verbindungen, Ringform ohne Isolierhülse für 
Kupferleiter 
DIN IEC 60757 
Code zur Farbkennzeichnung  
DIN ISO 16016 
Technische Produktdokumentation – Schutzvermerke zur Beschränkung der 
Nutzung von Dokumenten und Produkten (ISO 16016:2000) 
DIN ISO 8601 
Datenelemente und Austauschformate – Darstellung von Datum und Uhrzeit 
DIN 46225 
Gestanzte Krallenkabelschuhe mit Isolierumfassung für isolierte Leitungen 
DIN 46234 
Kabelschuhe für lötfreie Verbindungen, Ringform ohne Isolierhülse für 
Kupferleiter 
ISO 10303-212 
Industrielle 
Automatisierungssysteme 
und 
Integration 
– 
Produktdatendarstellung und –austausch – Teil 212: Anwendungsprotokoll: 
Elektrische/elektrotechnische Systeme und Anlagen 
MBN 10317 
Kennzeichnung von Merkmalen zur besonderen Nachweisführung 
Grundlagen - Dokumentationspflicht von Bauteilen / Baugruppen 
MBN 31001 
CAD-Zeichnungen: Grundlagen der Produktdarstellung  
Basis-Konstruktionsrahmen, die Darstellung und Kennzeichnung von Teilen 
MBN 31002 
CAD-Zeichnungen: Beschriftung Schriftarten und Schriftgröße 
MBN 31020-1 
Schriftfelder in Zeichnungen 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 170 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 170 von 178 
 
MBN 31020-2 
Zeichnungsfeld auf CAD-Zeichnungen 
MBN 31020-3 
Änderungen in Konstruktionszeichnungen 
MBN 10435  
Kennzeichnung von Teilen mit Daimler-Warenzeichen, Sachnummer und 
Identmerkmalen 
******** 
Steuerungskennzeichen in den Systemen der Produktdokumentation 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 171 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 171 von 178 
 
9.3 Abkürzungen und Begriffe 
Abkürzung 
Benennung/ Bemerkung 
ACM 
Agile Change Management (ehemals NCM  New Change Management) 
AV 
Ausführungsvorschrift 
CADDS 
Basis- CAD- Software der MBC Leitungssatzentwicklungsumgebung 
Catia 
für die DMU- Bearbeitungen eingesetztes CAD- System bei MBC 
ConnectPARTS 
Komponentendatenbank der Leitungssatzentwicklung 
ConnectHARNESS 
Leitungssatz- und Schaltplan- Recherche Datenbank 
DE 
deutsch 
Dialog 
Stücklistensystem der Daimler AG  
DS 
Dokumentation Sicherheitsrelevanter Teile 
DMU 
Digital Mock-Up. Computersimulation eines Produktes für die Unterstützung von 
Entwicklung, Herstellung und Service 
DZ 
Dokumentation Zertifizierung 
DocMaster 
Dokumenten-Management 
EAD/ ELA 
Einzeladerabdichtung/ Einzelleitungsabdichtung für elektrische Leitungen 
ELOG 
Schaltplandatenmodell nach ISO 10303- AP212 Spezifikation 
EN 
englisch 
EPDM 
Electronics Product Data Management 
ESD 
Empfindlichkeit eines Bauteils gegen elektrostatische Entladungen (electrostatic 
discharge) 
HCV 
Harness Container for Viewing  
KBL 
Leitungssatzdatenmodell (Kabelbaumliste) nach ISO 10303- AP212 Spezifikation 
KEM 
Konstruktions- Einsatz- Meldung 
KSL 
Kundenspezifischer Leitungssatz 
kZ 
Abkürzung für den Hinweis, dass ein Teil (Sachnummer) keine eigene Zeichnung hat, 
sondern, auf einer Tabellen-Zeichnung dargestellt ist. 
MBC 
Mercedes Benz Car / Development , PKW - Entwicklung 
REF 
Referenzbezeichnung nach VA 059 EI08  (siehe auch VZK) 
RD/EKL 
Leitungssatzentwicklung in der Entwicklung PKW /Elektrik.  
Smaragd  
in MBC verwendetes PDM- System 
SRM 
Sachstamm Recherche Modul 
SVG 
Skalierbare Vektor Graphik 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 172 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 172 von 178 
 
 
 
 
TIFF 
Tagged Image File Format   
VZK 
Verwendungszweckkatalog  
XML 
extensible mark-up language 
ZGS 
Zeichnungs- und Geometrie- Stand 
ZGDOK 
Zeichnungs-Geometrie-Dokumentation Datenmanagement 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 173 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 173 von 178 
 
9.4 Abbildungsverzeichnis 
Abbildung 1: Bauraum Master .......................................................................................................................... 11 
Abbildung 2: Leitungssatzmodule ..................................................................................................................... 12 
Abbildung 3: Beispiel für den Aufbau von Segmenten .................................................................................... 13 
Abbildung 4: Aufbau Mehrblattzeichnung ....................................................................................................... 16 
Abbildung 5: Aufbau Einzelblattzeichnung...................................................................................................... 16 
Abbildung 6: Besonderheiten auf Zeichnungskopf Mehrblattzeichnungen .................................................. 17 
Abbildung 7: Aufbau der Leitungssatzzeichnung (bei Mehrblattzeichnungen Blatt1) ................................ 18 
Abbildung 8: Blatt 2 (bei einer Mehrblattzeichnung) ...................................................................................... 19 
Abbildung 9: Pflichtfelder des MB Schriftfeldes .............................................................................................. 20 
Abbildung 10: Beispiel Änderungsmeldungen ................................................................................................. 22 
Abbildung 11: Beispiel eines Änderungsschriftkopfes "mit Änderung neu gezeichnet" .............................. 22 
Abbildung 12: bauraumoptimierte Anordnung (kompakte Verclipsung) Kabelschuhe .............................. 29 
Abbildung 13: Auszug maximaler Summenstrom Massebolzen ..................................................................... 31 
Abbildung 14: Masseanschluss ohne/mit Schrumpfschlauch .......................................................................... 31 
Abbildung 15: Auszug Leitungssatzzeichnung ................................................................................................. 32 
Abbildung 16: Bemaßung der Massestelle bei einem und drei Kabelschuhen .............................................. 32 
Abbildung 17: Bemaßung der Massestelle bei zwei und vier Kabelschuhen ................................................. 33 
Abbildung 18: Beispiel-Darstellung Kontaktierungsteil auf Zeichnung ........................................................ 37 
Abbildung 19: Abbildung/Darstellung von HSD-Steckern ........................... Fehler! Textmarke nicht definiert. 
Abbildung 20: Dokumentation Zubehörteil - Beispiel Sicherung ................................................................... 40 
Abbildung 21: Symbole Stützpunkte ................................................................................................................. 41 
Abbildung 22: Darstellung der Uhrzeit mit Definition der Montagebrettebene (DE) .................................. 42 
Abbildung 23: Darstellung der Uhrzeit mit Definition der Montagebrettebene (EN) .................................. 43 
Abbildung 24: Darstellung der Blickrichtung auf der Zeichnung .................................................................. 43 
Abbildung 25: Befestigungselement zeigt in Montagebrettrichtung 9 Uhr, auch wenn grafisch anders 
dargestellt .................................................................................................................................................... 44 
Abbildung 26: Befestigungselement liegt zur Montagebrettebene 9 Uhr, die Steckrichtung liegt auf 6 Uhr
 ...................................................................................................................................................................... 45 
Abbildung 27: Beispiel einer Steckrichtung parallel zum Leitungsstrang ..................................................... 45 
Abbildung 28: Benennung Referenzpunktbemaßung ...................................................................................... 48 
Abbildung 29: Bemaßung von Kabelschuhen mit Übermaß ........................................................................... 49 
Abbildung 30: Bemaßung von Kabelschuhen mit Referenzmaß .................................................................... 49 
Abbildung 31: Bemaßung von gewickelten Kabelschuhen mit Übermaß ...................................................... 49 
Abbildung 32: Beispiel Bemaßung Befestigungselemente (Auszug aus einer Zeichnung) ............................ 50 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 174 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 174 von 178 
 
Abbildung 33: Referenzpunktbemaßung für Befestigungselemente .............................................................. 50 
Abbildung 34: Legende Allgemeintoleranz (DE) ............................................................................................. 51 
Abbildung 35: Legende Allgemeintoleranz (EN) ............................................................................................. 51 
Abbildung 36: Einknüpfebene einer Tülle ........................................................................................................ 52 
Abbildung 37: Daten/Tools ................................................................................................................................. 54 
Abbildung 38: Abbildung Prozesse auf Toollandschaft ................................................................................... 55 
Abbildung 39: Strukturierung KBL bei Einzelleitungssätzen ........................................................................ 57 
Abbildung 40: Strukturierung KBL bei Masterleitungssätzen ....................................................................... 58 
Abbildung 41: Auszug Connector_housing ...................................................................................................... 65 
Abbildung 42: Darstellung Längentypen .......................................................................................................... 66 
Abbildung 43: Wickelrückbindung ................................................................................................................... 67 
Abbildung 44: Kontaktierungsart Standartkontakt ........................................................................................ 70 
Abbildung 45: Kontaktierungsart Doppelanschlag ......................................................................................... 71 
Abbildung 46: Kontaktierungsart Brückenkontakt......................................................................................... 72 
Abbildung 47: Kontaktierungsart Koax-Terminal .......................................................................................... 73 
Abbildung 48: HV-Leitung mit Stecker Adapterplatte ................................................................................... 74 
Abbildung 49: Abbildung HV-Leitung in KBL ................................................................................................ 74 
Abbildung 50: Darstellung von Textbändern in der KBL ............................................................................... 75 
Abbildung 51: Beispiel eines KSL-Steckers ...................................................................................................... 76 
Abbildung 52: Darstellung des KSL-Connector ............................................................................................... 76 
Abbildung 53: Auszug KBL Container (basierend KBL 2.4 SR1) ................................................................. 78 
Abbildung 54: Auszug KBL-Container (basierend KBL 2.4 SR1) ................................................................. 79 
Abbildung 55: Auszug Zusatzteile Pflicht/optionale Attribute (basierend KBL 2.4) .................................... 80 
Abbildung 56: Auszug Koordinate/ Cartesian_point ....................................................................................... 81 
Abbildung 57: Auszug Cavity Plug.................................................................................................................... 82 
Abbildung 58: Auszug Cavity_Seal (basierend KBL 2.4) ................................................................................ 84 
Abbildung 59: Auszug Komponenten/Component (basierend KBL 2.4) ....................................................... 88 
Abbildung 60: Auszug Kontaktgehäuse/ Connector_housing ......................................................................... 92 
Abbildung 61: KBL-Abbildung von Standardtoleranzen ............................................................................... 94 
Abbildung 62. Auszug externe Referenzen (basierend KBL 2.4) .................................................................... 95 
Abbildung 63: Auszug Befestigungsteile / Fixings (basierend KBL 2.4) ........................................................ 97 
Abbildung 64. Auszug Kontakte/ General-Terminal ....................................................................................... 99 
Abbildung 65: Auszug Leitungen/ General Wire Beispiel Iy-0.35 BK/RD .................................................. 101 
Abbildung 66: Auszug Core Sonderleitung .................................................................................................... 102 
Abbildung 67: Auszug Knoten/ Node (basierend KBL 2.4)........................................................................... 103 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 175 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 175 von 178 
 
Abbildung 68: Auszug Verbindungen/Routing .............................................................................................. 104 
Abbildung 69: Auszug Ausbindung/ Segment; Virtual/Physical Länge ....................................................... 105 
Abbildung 70: Auszug Ausbindung/ Segment; Cross Section/Protection Areas (basierend KBL 2.4) ...... 107 
Abbildung 71: Auszug Umwicklungen Kabelschutz/ Wire Protection (basierend KBL 2.4) ..................... 109 
Abbildung 72: Auszug Connector_occurrence ............................................................................................... 117 
Abbildung 73: schematischer Aufbau eines HCV-Containers ...................................................................... 128 
Abbildung 74: Darstellung Verbindung Index.xml und Harness.kbl ........................................................... 129 
Abbildung 75: Ausschnitt einer Index-XML .................................................................................................. 133 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 176 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 176 von 178 
 
9.5 Anhang 
 
Tabelle der Module in der Masterleitungssatzzeichnung: 
 
 
 
 
 
 
 
 
 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 177 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 177 von 178 
 
Darstellung Tabellen pro Modul zu Kabelschuh zu einem Massebolzen: 
 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28


### 第 178 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ FIL 
 
Datum/date: 2016-11-25 
 
ZGS: 0008 
 Auftr.-Nr./order no.:   YAP4233116 
 
Seite/page: 178 von 178 
 
Beispiel für Allgemeine Legende in DE/ EN 
 
 
Unkontrollierte Kopie bei Ausdruck
: Yinfeng Yan, 2017-04-28

