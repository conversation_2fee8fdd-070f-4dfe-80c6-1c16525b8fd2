# A 006 006 46 99_DE_2018-08_KBL数据格式执行规范.pdf

## 文档信息
- 标题：Lastenheft zur Erstellung
- 作者：<PERSON><PERSON><PERSON>, <PERSON> (059)
- 页数：119

## 文档内容
### 第 1 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: <PERSON><PERSON><PERSON>, <PERSON> 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 1 von 119 
 
Ausführungsvorschrift 
 
 
 
 
Datenformat KBL 
für Mercedes-Benz PKW  
 
 
 
 
 
 
 
 
 
 
Version 3.0 
 
 
 
 


### 第 2 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: <PERSON><PERSON><PERSON>, <PERSON>./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 2 von 119 
 
 
 Änderungsindex / Änderungsbeschreibung 
 
Änderungsdokumentation des Lebenslaufes Ausführungsvorschrift Datenformat KBL 
ZGS 
Nachtragsbeschreibung 
Bearbeitet (Datum) 
Daimler AG 
001 
 
Neuerstellung aus der gültigen „Ausführungsvorschrift 
Leitungssatzdokumentation für Mercedes-Benz PKW“ 
A0020063299 ZGS008  
 
Neuer id-Präfix für Connections, Klarstellung Querschnittsflächen, 
Aktualisierte Harness-Attribute, Nachverfolgbarkeit von Topologie-
Objekten, Transport von Fertigungshinweisen 
Neckenich (2017-07-03) 
002 
 
Kennzeichnung von geforderten Schema-Attributen 
 
Kleinere Korrekturen und Umstrukturierungen 
 
Überarbeitung Teileklassenzuordnung 
 
Überarbeitung Maßeinheiten 
 
Erweiterung Kontaktierungsarten 
 
Neue Abschnitte: 
o Kennzeichnung von Referenzpunkten 
o Abbildung gedichteter Kabelschuhe 
o Abgangsrichtungen von Spezial-Kontaktgehäusen 
o Lage- und Steckrichtung von Befestigungselementen 
Neckenich (2018-01-08) 


### 第 3 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 3 von 119 
 
003 
 
Neue Abschnitte 
o Dokumentation geschlossene Kammern 
o Dokumentation vorkonfektionierter Stecker 
o Darstellung 48V-Stecker 
o Modellierung Routing-Point-Dummy 
o Modellierung Fixing_assignment_id 
o Modellierung ViewingStartPoint/ViewingEndPoint 
o Umgang mit Nachstecklösungen 
 
Aktualisierung Screenshots 
 
Kleine Korrekturen bei Pflichtattributen und Maßeinheiten 
 
Kleinere Zusatzerläuterungen 
Dr. Neckenich (2018-08-17) 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 4 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 4 von 119 
 
Inhaltsverzeichnis 
1 
Rahmenbedingungen – Gegenstand dieser Ausführungsvorschrift 
8 
1.1 
Ziele ................................................................................................................................................. 8 
1.2 
Zuständigkeit .................................................................................................................................. 9 
1.3 
Verwendete Begriffe ...................................................................................................................... 9 
1.4 
Einzelleitungs-, Modul- und Masterleitungssatz ........................................................................... 9 
1.4.1 
Einzelleitungssatz ................................................................................................................... 9 
1.4.2 
Modulleitungssatz ................................................................................................................ 10 
1.4.3 
Masterleitungssatz ............................................................................................................... 10 
1.5 
Aufbau des Dokuments................................................................................................................ 11 
2 
KBL (Kabelbaumliste) 
12 
3 
Inhalte und Strukturen 
13 
3.1 
Masterleitungssätze ..................................................................................................................... 13 
3.2 
Einzelleitungssätze ....................................................................................................................... 13 
4 
Festlegungen 
14 
4.1 
Namenskonvention für KBL- und HCV-Dateien .......................................................................... 14 
4.2 
Maße und Maßeinheiten .............................................................................................................. 15 
4.3 
Zuordnung der Teileklassen ........................................................................................................ 15 
4.4 
Namenskonventionen für technische ids ("Klein-id") in den Occurrence-Klassen .................. 17 
4.5 
Externe Referenzen ...................................................................................................................... 18 
4.6 
Datenstand ................................................................................................................................... 18 
4.7 
Sachnummern-Formate ............................................................................................................... 19 
4.7.1 
MBC Sachnummern ............................................................................................................. 19 
4.7.2 
Schaltplan-Sachnummern ................................................................................................... 19 
4.7.3 
Benennung von Sachnummern ........................................................................................... 19 
4.8 
MBC-Positionsnummern .............................................................................................................. 19 
4.9 
Wertefestlegungen ....................................................................................................................... 20 
4.9.1 
KBL-Version .......................................................................................................................... 20 
4.9.2 
Kontaktgehäuse – Typ ......................................................................................................... 20 
4.9.3 
Typen von Standardtoleranzen ............................................................................................ 20 


### 第 5 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 5 von 119 
 
4.9.4 
Dokumententypen ................................................................................................................ 21 
4.9.5 
Leitungsfarben...................................................................................................................... 21 
4.9.6 
Inhalte von Leitungssätze .................................................................................................... 21 
4.9.7 
Querschnittsflächen ............................................................................................................. 22 
4.9.8 
Gebrauch von Kontaktgehäusen ......................................................................................... 22 
4.9.9 
Leitungslängen ..................................................................................................................... 22 
4.9.10 Typen von Steckplätzen ....................................................................................................... 23 
4.9.11 Copyright-Kennzeichen ........................................................................................................ 23 
4.9.12 Nennspannungen ................................................................................................................. 24 
4.9.13 Quellensysteme .................................................................................................................... 24 
4.9.14 Datenformate ....................................................................................................................... 24 
4.10 DS/DZ-Kennzeichnung und ESD-Kenner .................................................................................... 25 
4.10.1 DS/DZ-Kennzeichnung ........................................................................................................ 25 
4.10.2 ESD-Kenner .......................................................................................................................... 26 
5 
KBL-Container 
27 
5.1 
Attribute des KBL-Containers ...................................................................................................... 28 
5.2 
Zusatzteil (Accessory) .................................................................................................................. 28 
5.3 
Kartesische Koordinate (Cartesian_point) .................................................................................. 30 
5.4 
Blindstopfen (Cavity_plug) ........................................................................................................... 31 
5.5 
Einzeladerdichtung (Cavity_seal) ................................................................................................ 33 
5.6 
Änderung (Change) ...................................................................................................................... 34 
5.7 
Änderungsbeschreibung (Change_description) ......................................................................... 36 
5.8 
Komponente (Component) .......................................................................................................... 37 
5.9 
Komponentenbox (Component_box) .......................................................................................... 40 
5.10 Kontaktgehäuse (Connector_housing) ....................................................................................... 43 
5.11 Standardtoleranz (Default_dimension_specification) ................................................................ 45 
5.12 Festlegungsmaße (Dimension_specification) ............................................................................. 47 
5.13 Externe Referenz (External_reference) ....................................................................................... 49 
5.14 Befestigungsteil (Fixing) ............................................................................................................... 50 
5.15 Kontakt (General_terminal) ......................................................................................................... 52 


### 第 6 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 6 von 119 
 
5.16 Leitungen (General_wire)............................................................................................................. 55 
5.17 Knoten (Node) .............................................................................................................................. 59 
5.18 Verlegung (Routing) ..................................................................................................................... 61 
5.19 Segment (Segment) ..................................................................................................................... 62 
5.20 Maßeinheit (Unit) .......................................................................................................................... 66 
5.21 Leitungsschutz (Wire_protection) ............................................................................................... 67 
6 
Harness Container 
69 
6.1 
Attribute des Harness-Containers ............................................................................................... 70 
6.2 
Accessory_occurrence ................................................................................................................ 73 
6.3 
Cavity_plug_occurrence .............................................................................................................. 74 
6.4 
Cavity_seal_occurrence .............................................................................................................. 74 
6.5 
Component_occurrence .............................................................................................................. 74 
6.6 
Component_box_occurrence ...................................................................................................... 76 
6.7 
Verbindung (Connection) ............................................................................................................. 78 
6.8 
Connector_occurrence ................................................................................................................ 80 
6.9 
Fixing_occurrence ........................................................................................................................ 82 
6.10 General_wire_occurrence ........................................................................................................... 83 
6.11 Terminal_occurrence ................................................................................................................... 85 
6.12 Wire_protection_occurrence ....................................................................................................... 85 
6.13 Verkabelungsgruppe (Wiring_group) .......................................................................................... 86 
6.14 Harness_configuration ................................................................................................................. 87 
6.15 Module (Module) .......................................................................................................................... 90 
6.16 Modulkonfiguration (Module_configuration) .............................................................................. 92 
6.17 Modulfamilie (Module_family) ..................................................................................................... 93 
7 
Detailerläuterungen 
95 
7.1 
Nachverfolgbarkeit der Topologie-Objekte ................................................................................. 95 
7.2 
Wickelrückbindung ....................................................................................................................... 95 
7.3 
Darstellung der Kontaktierungsarten .......................................................................................... 97 
7.3.1 
Standardkontakt .................................................................................................................. 97 
7.3.2 
Doppelanschlag .................................................................................................................... 99 


### 第 7 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 7 von 119 
 
7.3.3 
Brückenkontakt ................................................................................................................. 100 
7.3.4 
Komplexe Kontakte ........................................................................................................... 100 
******* 
HSD-Terminal ........................................................................................................... 101 
7.3.4.2 
FAKRA-/KOAX-Terminal .......................................................................................... 102 
7.3.4.3 
Ethernet-Terminal..................................................................................................... 103 
7.3.5 
Stecker mit mehreren Eintrittspunkten (HV-Leitungssatz) ............................................ 103 
7.3.6 
48V-Kontaktierung ............................................................................................................ 105 
7.4 
Modellierung einseitig angeschlagener Leitungen .................................................................. 105 
7.5 
Modellierung von Routing-Point-Dummys ............................................................................... 106 
7.6 
Darstellung von Textbändern ................................................................................................... 107 
7.7 
von KSL-Steckern ...................................................................................................................... 107 
7.8 
Transport von Fertigungshinweisen ......................................................................................... 108 
7.9 
Kennzeichnung von Referenz-Punkten .................................................................................... 109 
7.10 Abbildung gedichteter Kabelschuhe ........................................................................................ 109 
7.11 Abgangsrichtungen von Spezial-Kontaktgehäusen ................................................................. 110 
7.12 Ausgangs- und Endpunkt für Blickrichtung ............................................................................. 110 
7.13 Lage- und Steckrichtung von Befestigungselementen („Uhrzeiten“) ..................................... 111 
7.14 Ids von Kabelkanalausgängen, Ein-/Ausgängen T-Verteiler................................................... 112 
7.15 Modellierung vorkonfektionierter Kontakte ............................................................................ 112 
7.16 Modellierung geschlossener Kammern ................................................................................... 112 
7.17 Umgang mit Nachstecklösungen ............................................................................................. 113 
8 
Mitgeltende Unterlagen 
114 
8.1 
Normative Hinweise .................................................................................................................. 114 
8.2 
Normen und Vorschriften ......................................................................................................... 115 
8.3 
Abkürzungen und Begriffe ........................................................................................................ 116 
8.4 
Abbildungsverzeichnis .............................................................................................................. 117 
 
 


### 第 8 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 8 von 119 
 
1 Rahmenbedingungen – Gegenstand dieser 
Ausführungsvorschrift 
Diese Ausführungsvorschrift enthält die von der Daimler AG festgelegten Anforderungen an die das 
Datenformat KBL (Kabelbaumliste) zum Austausch von Leitungssatzdaten. Sie stellt die verbindliche 
Vorgabe zur Erstellung der Kabelbaumliste für alle Fahrzeugbaureihen des Bereiches MBC dar. Die KBL-
Datei ist mit einen Copyright der Daimler AG analog Zeichnungsschriftfelder zu versehen. 
Beispiel:  
Copyright reserved Daimler AG, Schutzvermerk nach DIN ISO 16016 beachten!  
 
Der hier beschriebene Inhalt beschreibt die inhaltlichen und strukturellen Anforderungen von MBC an 
das Datenformat KBL 2.4 SR-1.  
 
Diese Ausführungsvorschrift beschreibt keine technischen Sachverhalte,  zu verwendende Materialien 
sowie Sachverhalte welche durch Lastenhefte, Normen und Verfahrensanweisungen der Daimler AG 
geregelt sind. Der Lieferant ist verpflichtet sämtliche Ausführungsvorschriften und Normen einzuhalten 
und die Dokumentation stets dem aktuellen Stand anzupassen. Sind für eine einwandfreie 
Dokumentation erforderliche Randbedingungen in dieser Ausführungsvorschrift nicht oder abweichend 
definiert, ist dies der Daimler AG anzuzeigen.  
 
Alle Abweichungen von den Anforderungen dieser Ausführungsvorschrift bedürfen der schriftlichen 
Zustimmung der Daimler AG.  
 
1.1 Ziele 
Die wichtigsten Ziele sind:  
 
Inhalte des Datenaustauschformat KBL festzulegen 
 
Die Abbildung der Leitungssatzinhalte in einer KBL-Datei zur Unterstützung der MBC 
internen Prozesse sowie Versorgung der Dokumentation mit archivierungsgerechten 
Zeichnungen. 
 
Gewährleistung der korrekten Versorgung der internen Entwicklungsprozesse mit digitalen 
Leitungssatzdaten. 
 
Definierte Abbildung technischer Sachverhalte des Leitungssatzes zum fehlerfreien Austausch 
zwischen den Leitungssatz-Lieferanten 
 


### 第 9 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 9 von 119 
 
1.2 Zuständigkeit 
Zuständig für die Ausführung der KBL-Dokumentation und der technischen Inhalte ist die Abteilung 
Leitungssatzentwicklung.  
 
1.3 Verwendete Begriffe 
- 
 
1.4 Einzelleitungs-, Modul- und Masterleitungssatz 
1.4.1 Einzelleitungssatz 
Bei einem Einzelleitungssatz handelt es sich um ein bestellbares Teil aus einem Baukasten mit 
mindestens einer elektrischen Leitung und Anbauteilen. Er wird als Einzelteil auf einer eigenen 
Zeichnung nach MBN 10317 dokumentiert werden. 
 
Abbildung 1: Strukturierung KBL bei Einzelleitungssätzen 


### 第 10 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 10 von 119 
 
1.4.2 Modulleitungssatz 
Bei einem Modulleitungssatz handelt es sich um ein bestellbares Teil aus einem Baukasten mit 
mindestens einer elektrischen Leitung und Anbauteilen. Er wird als Einzelteil nicht auf einer eigenen 
Zeichnung, sondern auf einer Tabellenzeichnung nach MBN 10317 dokumentiert werden. 
 
1.4.3 Masterleitungssatz 
Bei einem Masterleitungssatz handelt sich um eine Zeichnung vom Typ Tabelle mit mehreren 
bestellbaren Zusammenbauteilen vom Typ Modulleitungssatz (kZ-Teil). In der Regel handelt es sich hier 
um einen Bauraumleitungssatz mit mehreren dargestellten Varianten und / oder optionalen Add-On 
Leitungssätzen.  
Der Masterleitungssatz bildet die Grundlage des Kundenspezifischen Leitungssatzes (KSL). Dieser 
enthält ein Grundmodul und kann mit den Varianten, optionalen Add-On Leitungssätzen, zu einem 
auftragsbezogenen, kundenspezifischen Leitungssatz zusammengestellt und fertig montiert werden. Ein 
Masterleitungssatz besteht i.d.R. somit aus einem Grundmodul und Modulleitungssätzen. Eine HCV bzw. 
KBL-Datei eines Masters beinhaltet immer alle zu diesem Master gehörenden Modulleitungssätze. 
Die Benennung eines Masterleitungssatzes beginnt mit TB ZB Elektr. Leitungssatz. Für jeden 
Modulleitungssatz wird zusätzlich eine laufende Variantennummer "(Bsp. V1, ...)" vergeben.  
Die Zeichnung muss einer Tabellenzeichnung nach MBN 10317 entsprechen. 
 
 
Abbildung 2: Strukturierung KBL bei Masterleitungssätzen 


### 第 11 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 11 von 119 
 
Eine Tabellenzeichnung hat eine eigene Sachnummer, hinter der sich kein bestellbares Teil verbirgt. 
Bestellbare Teile sind mit ihrer Sachnummer auf der Tabellenzeichnung dargestellt. 
Übertragen auf die KBL stellt die Tabelle den Harness-Container dar, in der bestellbare Teile mit ihrer 
Sachnummer als Modul abgebildet sind. 
 
1.5 Aufbau des Dokuments 
Die Dokumentation und Beschreibung der KBL-Klassen und der zugehörigen Attribute folgt dem 
nachstehenden Schema:  
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
 
 
Pflicht    [P] 
Optional [O] 
 
 
In der Spalte <Bezeichnung> ist die deutsche Übersetzung/Bezeichnung des KBL-Attributs hinterlegt. 
Das definierte Attribut aus dem KBL-Schema, welches in der jeweiligen Zeile beschrieben wird, findet 
sich in der Spalte <KBL-Attribut>.  Die Spalte <P/O> klassifiziert, ob das jeweilige KBL-Attribut ein 
verpflichtendes (P) oder ein optionales (O) Attribut für die Daimler KBL ist. Ist das Attribut in runden 
Klammern geschrieben, so bedeutet dies, dass dieses Attribut bei Nutzung dieser Strukturstufe auf 
Grund des KBL-Schemas sowieso verpflichtend ist. In der Spalte <Bemerkung> sind entsprechende 
Mapping-Bemerkungen o.Ä. hinterlegt. 
 
 


### 第 12 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 12 von 119 
 
2 KBL (Kabelbaumliste) 
Die KBL ist ein elektronisches Abbild des Leitungssatzes und beschreibt Leitungen, Verbindungen, 
Einzelteile, die Leitungssatztopologie mit ihren Abzweigen und Segmenten, die theoretischen und die 
physikalischen Längen der Leitungen sowie die Quelldokumente (Schaltplan und 3D-DMU-Modelle), die 
diesem Leitungssatz zu Grunde liegen.  
Die KBL wird aus dem Entwicklungswerkzeug generiert und muss den Leitungssatz (topologisch und 
elektrisch) auch ohne Zeichnung vollständig dokumentieren sowie die zur Erstellung verwendeten 
Quelldokumente wie Schaltplan- und DMU-Topologie-Daten dokumentieren und referenzieren.  
 
Die KBL muss dem Standard ISO 10303-212 und dem XML Schema der vereinbarten KBL-Version 
entsprechen. Es sind die im Schema vereinbarten Zeichensatzdeklarationen zu verwenden. 
 
Die Leitungssatz KBL-Datei wird zur Leitungssatzdokumentation, Baukasten-Versorgung des MBC 
Stücklistensystem DIALOG sowie für interne Recherchen in der Leitungssatz-Datenbank bei MBC 
verwendet. 
Die Releasezyklen der KBL-Version sind mit MBC abzustimmen. 
 
 
 


### 第 13 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 13 von 119 
 
3 Inhalte und Strukturen 
Die KBL-Struktur mit Harness-Container und Modul entspricht der bei MBC verwendeten 
Dokumentationsform Tabellenzeichnung und kann 1:1 verwendet werden. Die Klasse Harness stellt 
den Container für den Inhalt einer Leitungssatzzeichnung dar, in dem die Inhalte eines oder mehrerer 
Leitungssätze dokumentiert werden.  
Die Attribute des Zeichnungskopfes wie Sachnummer, Benennung, Änderungsbeschreibung usw. 
werden in den entsprechenden Attributen der Klasse Harness abgelegt.  
 
3.1 Masterleitungssätze 
Bei Masterleitungssätzen werden Sachnummer, Version, Benennung und Änderungsbeschreibung der 
Mastersachnummer (entspricht den Attributen im Zeichnungsschriftkopf einer Masterzeichnung) in den 
Attributen der Klasse Harness abgebildet. Die technischen Inhalte der einzelnen kZ-Teile sowie deren 
Sachnummer, Version, Benennung und Änderungsbeschreibung werden innerhalb der Klasse Harness 
als Module angelegt. 
Die im Zeichnungskopf unter dem aktuellen ZGS (Zeichnungs- und Geometriestand) eingetragenen 
Status oder Änderungsbeschreibungen der Zeichnung werden in der Klasse Change im Harness 
Container eingetragen. 
Status oder Änderungsbeschreibungen der in der Tabellenzeichnung enthaltenen kZ-Tabelle werden in 
der Klasse Change im jeweiligen Module eingetragen. 
 
3.2 Einzelleitungssätze 
Bei 
Einzelleitungssätzen 
werden 
ebenso 
Sachnummer, 
Version, 
Benennung 
und 
Änderungsbeschreibung des Zeichnungskopfes in den Attributen der Klasse Harness abgebildet. Die 
technischen Inhalte des Leitungssatzes sowie deren Sachnummer, Version, Benennung und 
Änderungsbeschreibung werden innerhalb der Klasse Harness als Module angelegt. 
 
Sachnummer und Benennung der kZ- Tabelle werden in den Attributen der Klasse Module angelegt. 
Für jeden Leitungssatz wird ein Modul angelegt, welches die technischen Inhalte des Leitungssatzes 
dokumentiert.  
 
 


### 第 14 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 14 von 119 
 
4 Festlegungen 
4.1 Namenskonvention für KBL- und HCV-Dateien 
Da die Wahl der Leitungssatzentwicklungswerkzeuge freigestellt ist und die KBL zur Dokumentation 
und als Eingangsdatensatz für viele interne Prozesse genutzt wird, ist es notwendig, die Strukturen der 
KBL abzustimmen und einige Regeln zu vereinbaren. 
 
Syntax:  
<MBC Sachnummer>_<ZGS>_<Datenstand>.kbl 
 
Beispiele: 
ZGS = 001 
MBC Sachnummer = A1665406503 
Datenstand = 20131121 
Dateiendung für KBL Dateien = kbl 
 
Der Dateinamen setzt sich aus der Sachnummer ohne Leerzeichen/Sonderzeichen, dem ZGS (3stellig), 
dem Datenstand im Basisformat (JJJJMMTT) und der Dateiendung kbl/hcv zusammen. Als Trenner ist 
ein Unterstrich zu verwenden. Bei Masterleitungssätzen entspricht die MBC-Sachnummer der 
Masterleitungssatznummer/ Sachnummer der Tabellenzeichnung. 
 
Beispiel: 
A1665406503_001_20131121.kbl 
A1725408709_002_20100630.kbl 
 
 
 
 


### 第 15 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 15 von 119 
 
4.2 Maße und Maßeinheiten 
Die Klasse Maßeinheiten (Units) enthält die Festlegung aller in der KBL verwendeten Maßeinheiten. 
Dabei sind nur die folgenden Werte zulässig: 
 
Verwendung in KBL 
Maßerklärung 
Maßeinheiten 
General_wire (Leitungen) (1 
Gramm/ Meter 
[g/m] 
Länge 
[mm] 
Segment (3 
Länge 
[mm] 
Fläche 
[mm²] 
Part (Accessory, Cavity Plug, Cavity Seal, Component, 
Component Box, Connector Housing, Fixing, General Terminal) 
(4 
Gramm 
[g] 
Wire_protection (Schläuche) 
Gramm/ Meter 
[g/m] 
Wire_protection (Bandagierungen) 
Gramm/ Fläche 
[g/m²] 
Wire_protection_occurrence 
Länge 
[mm] 
Module/Harness/Harness_configuration (2 
Kilogramm 
[kg] 
General_terminal (Kontakte) 
Fläche 
[mm²] 
Länge 
[mm] 
Cavity_seal (Einzeladerdichtungen) 
Länge 
[mm] 
Component_box / Fuse (Sicherungsboxen, Sicherungen) 
Ampere 
[A] 
(1 ohne Kommastellen  
(2 mit maximal drei Nachkommastellen 
(3 ohne Kommastellen bei <Physical_length>, mit maximal zwei Nachkommastellen bei restlichen Maßen 
(4 mit maximal zwei Nachkommastellen 
Diese Festlegungen müssen im jeweiligen Entwicklungswerkzeug eingestellt werden. 
 
4.3 Zuordnung der Teileklassen 
Diese Gegenüberstellung soll eine grobe Zuordnung der Teile aus der CONNECT-Datenbank zu den 
einzelnen Klassen im KBL-Container ermöglichen, kann aber im Einzelfall je nach Art und Einsatz des 
Teiles abweichen. 
 
CONNECT-Teileklassen 
KBL-Klassen 
AV Bandagierung  
(Vollumwicklung, Sparbandage, Längsbandage) 
Wire_protection 


### 第 16 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 16 von 119 
 
AV Bandagierung 
(Montagehilfsbandage, Fixierer, Farbmarkierer) 
Accessory 
Blindstopfen 
Cavity_plug 
Clip (Primärfunktion: Leitungshalter) 
Fixing 
Clip (Primärfunktion: Komponentenhalter) 
Accessory 
Einzelleitung 
General_wire 
Einzelteil/ Zusatzteil 
Accessory 
Einzeladerdichtung 
Cavity_seal 
Elektr. Komponenten 
Component 
Halter 
Accessory 
Kabelbinder 
Accessory 
Kabelkanal  
(ohne Merkmal Kabelkanal-Deckel) 
Fixing 
Kabelkanal 
(mit Merkmal Kabelkanal-Deckel) 
Accessory 
Kabelschuh 
Connector_housing 
Kontakt  
General_terminal 
Kontaktgehäuse 
Connector_housing 
Normteil 
Accessory 
Potentialverteiler 
Component_box 
Schlauch 
(alle außer Schlauchtyp Dichtband) 
Wire_protection 
Schlauch 
(Schlauchtyp Dichtband) 
Accessory 
Sicherung 
Component (Fuse) 
Sicherungsbox/-dose 
Component_box 
Sonstiges 
Accessory 
Sonderleitung 
General_wire 
Tülle 
Fixing 
 
KBL-Klassen ohne eindeutige CONNECT-Teilegruppen 
KBL-Klassen 
Beschreibung und Zuordnung 
Accessory 
Etiketten 
PUR-Schaumschlauch 


### 第 17 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 17 von 119 
 
Component 
Relais 
Connector_housing 
Splices 
 
4.4 Namenskonventionen für technische ids ("Klein-id") in den 
Occurrence-Klassen  
Die Namensgebung der Klassen und Attribute ist generell im KBL-Schema vorgegeben. Allein die 
Namensgebung der technischen Ids ("id") der einzelnen Klassen ist nicht definiert und kann im 
Allgemeinen frei gewählt werden. Da Systeme im Umfeld der KBL entwickelt wurden und in 
unterschiedlicher Weise auf diese technischen Ids ("id") zugreifen ist die Namensgebung für einige 
Bereiche reglementiert. 
 
id- Präfix 
Objekt-Typ 
ID_ACC 
Accessory_occurrence 
ID_BNJ 
Node 
ID_BNS 
Segment 
ID_CBO 
Component_box_occurrence 
ID_CNF 
Harness_configuration 
ID_CNN 
Connection 
ID_COM 
Component_occurrence 
ID_CON 
Component_box_connector_occurrence 
Connector_occurrence 
ID_FAM 
Module_family 
ID_FIX 
Fixing_occurrence 
ID_FUS 
Fuse_occurrence 
ID_MCNF 
Module_configuration 
ID_MOD 
Module 
ID_MUL 
Special_wire_occurrence 
ID_PLU 
Cavity_plug_occurrence 
ID_SEA 
Cavity_seal_occurrence 
ID_TAP 
Wire_protection_occurrence 


### 第 18 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 18 von 119 
 
ID_TER 
Terminal_occurrence 
ID_WIR 
Wire_occurrence 
Core_occurrence 
ID_WRG 
Wiring_group 
 
4.5 Externe Referenzen 
Externe Referenzen sind eine Liste aller Referenzen auf externe Dokumente zu den im Harness bzw. den 
Modulen verwendeten Einzelteilen oder den zur Erstellung verwendeten Quelldokumenten wie 
Schaltplan und DMU-Topologie Daten.  
Die Art der Dokumente wird über das Attribut <Document_type> unterschieden. Die Referenzen sind 
über die <id> eindeutig und werden aus der Verwendung heraus referenziert.  
 
Für die Dokumententypen „Circuit Wiring“ und „Wiring Construction Unit“ gilt zusätzlich: 
Bei Masterleitungssätzen müssen Referenzen auf Schaltpläne bzw. DMU-Modelle, die von allen Modulen 
des Masterleitungssatz referenziert werden, nur in den <External References> auf Ebene Harness 
referenziert werden. Lediglich die nicht gemeinsamen Verweise werden auf Ebene Module referenziert. 
Weitere Details zur Dokumentation sind im Kapitel 5.13 zu finden. 
 
Für Dummy-Teile, die im Leitungssatz verwendet werden (zu erkennen am Wert Z007 im Attribut 
<Abbreviation>), ist kein Verweis auf eine externe Referenz notwendig. 
 
4.6 Datenstand 
Unabhängig vom Versionskennzeichen diverser am Prozess beteiligter Systeme wird in den 
Leitungssatzzeichnungen und Schaltplänen zusätzlich ein Merkmal Datenstand angelegt und 
dokumentiert. 
Das Merkmal wird bei der Bearbeitung automatisch angelegt und in einem eigenen Feld im 
Zeichnungskopf eingetragen. Es beinhaltet das Datum im erweiterten Format nach DIN ISO 8601 und 
soll die Zuordnung der Zeichnung mit abgeleiteten Dokumenten ermöglichen. Bei Änderung eines 
Leitungssatzmoduls und/oder des Masterleitungssatzes ist der Datenstand anzupassen. Ergeben sich 
an einzelnen Modulen eines Masterleitungssatz keine Änderungen, so ist der Datenstand beizubehalten. 
Beispiel: 2013-12-01 
 
 


### 第 19 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 19 von 119 
 
 
4.7 Sachnummern-Formate 
4.7.1 MBC Sachnummern 
MBC Sachnummern sind generell in das Attribut <Part_number>, die Lieferantensachnummern in das 
Attribut <Alias_id> einzutragen.  
 
Sachnummer nach MBN 
  
 
 
A 222 540 93 00 
Schreibweise der Sachnummer   
 
 
AXXXXXXXXXX 
Schreibweise der <Predecessor_part_number>:  
AXXXXXXXXXX 
Alternative Schreibweise Sachnummer  
 
A XXX XXX XX XX 
 
Die alternative Schreibweise für Sachnummern ist nur im Attribut <Part_number> der Klassen Harness 
und Modul erlaubt. 
 
4.7.2 Schaltplan-Sachnummern 
Die Schaltplansachnummer setzt sich aus der <MBC Sachnummer> ohne Leerzeichen/Sonderzeichen 
und der Blattnummer zusammen. Als Trenner ist ein Unterstrich zu verwenden. 
 
 
Schaltplansachnummer = <MBC Sachnummer>_<Blattnummer> 
 
4.7.3 Benennung von Sachnummern 
Grundlage für die Benennung von Sachnummern ist die Arbeitsanweisung A 059 8031. Die Benennung 
einer Sachnummer aus SRM ist exakt so in der KBL-Datei wieder zu verwenden. 
 
4.8 MBC-Positionsnummern 
Positionsnummern sind Besonderheiten von MBC-Teilen und können der CONNECT-Teilebibliothek 
entnommen werden. In der KBL-Teilebibliothek (Parts-Klassen, z.B. Connector_housing) werden diese 
jeweils zu einer Sachnummer im Attribut <Abbreviation> beschrieben. 
 
 
 


### 第 20 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 20 von 119 
 
4.9 Wertefestlegungen 
4.9.1 KBL-Version 
Das Attribut <version_id> im KBL-container dient anderen Systemen dazu die richtige Version der 
KBLs zu identifizieren und notwendige Anpassungen z.B. beim Import in eine Datenbank zu steuern.  
 
Innerhalb des Attributes <version_id> sind nur folgende Werte zugelassen: 
 
Werte für <version_id> 
Beschreibung 
2.3 SR-1 
KBL-Version 2.3 SR-1 
2.4 SR-1 
KBL-Version 2.4 SR-1 
 
4.9.2 Kontaktgehäuse – Typ 
Um die verschiedenen Arten der Kontaktgehäuse zu unterscheiden, existiert in der Klasse 
Connector_housing das Attribut <Housing_type>. 
Dabei sind als <Housing_type> nur die Begriffe aus nachfolgender Tabelle erlaubt: 
Werte für <Housing_type> 
Beschreibung 
splice 
Splice 
ring terminal 
Kabelschuh 
no end 
Routing- oder Z-Punkt 
keine Verwendung 
bei allen anderen Fällen 
 
4.9.3 Typen von Standardtoleranzen 
Standardtoleranzen werden in der KBL durch die Klasse Default_dimension_specification abgebildet 
und können über das Attibut <Tolerance_type> in ihrem Typ unterschieden werden. 
Als <Tolerance_type> sind nur die Begriffe aus nachfolgender Tabelle erlaubt: 
Werte für <Tolerance_type> 
Beschreibung 
segment length 
Standardtoleranz für die Segmentlänge 
fixing distance 
Standardtoleranz für den Abstand von Befestigungselementen 
auf einem Segment 
 


### 第 21 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 21 von 119 
 
4.9.4 Dokumententypen 
Zur Klassifikation von Externen Referenzen innerhalb der Klasse External_reference existiert das Attribut 
<Document_type>. 
Innerhalb des Attributes <Document_type> sind nur folgende Werte erlaubt: 
Werte für <Document_type> Beschreibung 
Drawing 
Externe Referenz auf Hinweiszeichnungen 
Bei kZ- siehe-Teilen  wird auf Teileebene in der Bibliothek 
referenziert. 
Circuit Wiring 
Externe Referenz auf Schaltpläne  
Die <id> wird auf Ebene Harness referenziert, sofern der 
Schaltplan für alle Module relevant ist, ansonsten wird die <id> 
auf Ebene Module referenziert. 
Wiring Construction Unit 
Externe Referenz auf DMU-KBL Datensätze 
Die <id> wird auf Ebene Harness referenziert, sofern das DMU-
Modell für alle Module relevant ist, ansonsten wird die <id> auf 
Ebene Module referenziert. 
Deviation Table 
Externe Referenz auf die Abweichtabelle  
Die <id> wird für die standortspezifische Teileverwendung auf 
Ebene Harness referenziert. 
 
4.9.5 Leitungsfarben 
Leitungen können verschiedene Farben besitzen, welche in der KBL in der Klasse General_wire durch 
das Attribut <Colour_type> unterschieden werden. 
Für das Attribut <Colour_type> sind nur folgende Werte zulässig: 
Werte für <Colour_type> 
Beschreibung 
base colour 
Grundfarbe 
second colour 
1. Streifenfarbe 
third colour 
2. Streifenfarbe 
 
4.9.6 Inhalte von Leitungssätze 
Der Inhalt eines Leitungssatzes wird in der Klasse Harness über das Attribut <Content> beschrieben. 
Als <Content > sind nur die Begriffe aus nachfolgender Tabelle erlaubt: 


### 第 22 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 22 von 119 
 
Werte für <Content> 
Beschreibung 
harness subset 
Einzelleitungssätze 
harness complete set 
Masterleitungssätze 
 
4.9.7 Querschnittsflächen 
Objekte der Klasse Segment können verschiedene Arten von Querschnittsflächen haben, welche über 
das Attribut <Value_determination> unterschieden werden. 
Als <Value_determination> sind nur die Begriffe aus nachfolgender Tabelle erlaubt: 
Werte für 
<Value_determination> 
Beschreibung 
reserved 
Bauraumreservierung 
calculated 
berechnete Querschnittsfläche nach Routing 
measured 
gemessenes Bündel am realen physikalischen Leitungssatz 
 
4.9.8 Gebrauch von Kontaktgehäusen 
Die Nutzung der verschiedenen Kontaktgehäuse wird in der Verwendungsklasse Connector_occurrence 
durch das Attribut <Usage> näher klassifiziert. Analog zu den Kontaktgehäuse-Typen muss dabei 
zwischen den verschiedenen Arten unterschieden werden. 
Als <Usage> sind nur die Begriffe aus nachfolgender Tabelle erlaubt: 
Werte für <Usage> 
Beschreibung 
splice 
Splice 
ring terminal 
Kabelschuh 
no end 
Routing- oder Z-Punkt 
keine Verwendung 
bei allen anderen Fällen 
 
4.9.9 Leitungslängen 
Um Berechnungen von Widerstand, Kupfergewicht und anderer elektrischer Werte exakt durchführen 
zu 
können, 
sind 
die 
berechneten 
oder 
gemessenen 
Leitungslängen 
in 
der 
Klasse 
General_wire_occurrence als  <Length_information> anzugeben. 
Dabei sind als <Length_type> nur die folgenden Werte zulässig: 
Werte für <Length_type> 
Beschreibung 


### 第 23 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 23 von 119 
 
DMU length 
Länge aus dem DMU 
Drawing length 
gerundete Länge aus dem DMU ohne Zuschläge 
Terminal length 
gerundete Länge mit berechnetem Stecker-Zuschlag bis Kontakt 
Production length 
gerundete 
Länge 
mit 
berechnetem 
Stecker-Zuschlag 
und 
berechnetem Produktionszuschlag 
Bei Freigabe von fertigen Produkten ist die Production length in berechneter oder gemessener Form 
anzugeben. 
 
Abbildung 3: Darstellung Längentypen 
4.9.10 Typen von Steckplätzen 
Zur Unterscheidung der verschiedenen Arten von Steckplätzen in einer Sicherungsbox werden die 
Component_slots anhand des Attributs <Type> typifiziert.  
Dazu sind für <Type> nur die folgenden Werte zulässig: 
Werte für <Type> 
Beschreibung 
fuse 
Steckplatz für eine Sicherung 
relais 
Steckplatz für ein Relais 
 
4.9.11 Copyright-Kennzeichen 
Um die KBL-Datei analog zu den Zeichnungsdateien mit einem Copyright-Kennzeichen zu versehen 
existiert innerhalb der Klassen Harness, Harness_configuration und Module das Attribut 
<Copyright_note>.  


### 第 24 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 24 von 119 
 
Im Attribut <Copyright_note> ist zwingend folgender Text als Wert zu hinterlegen: 
Wert für <Copyright_note> 
Copyright reserved Daimler AG, Schutzvermerk nach DIN ISO 16016 beachten! 
 
4.9.12 Nennspannungen 
Für Masseleitungen werden die angeschlossenen Verbindungen der Klasse Connection über das Attribut 
<Nominal_voltage> in ihre Spannungslagen eingegliedert. 
Dazu sind für <Nominal_voltage> nur die folgenden Werte zulässig: 
Werte für <Nominal_voltage> 
Beschreibung 
12V 
Masseleitungen für 12V-Masse 
48V 
Masseleitungen für 48V-Masse 
HV 
Masseleitungen für HV-Masse 
 
4.9.13 Quellensysteme 
Für die verschiedenen Dokumente der Klasse External_reference existiert das Attribut 
<Creating_system>, welches das Quellensystem der Externen Referenz kennzeichnet. 
Dabei sind für <Creating_system> nur die folgenden Werte erlaubt: 
Werte für <Creating_system> 
Beschreibung 
Connect 
Dokumente aus CONNECT 
E3.Cable 
Dokumente aus E3.Cable (Bsp. Schaltpläne) 
Siemens NX 
Dokumente aus Siemens NX (Bsp. DMU-Modelle) 
 
4.9.14 Datenformate 
Für die verschiedenen Dokumente der Klasse External_reference existiert das Attribut <Data_format>, 
welches das Datenformat der Externen Referenz kennzeichnet. 
Dabei sind für <Data_format> nur die folgenden Werte erlaubt: 
Werte für <Data_format> 
Beschreibung 
TIFF 
Datenformat für Zeichnungen 
E3S 
Datenformat für Schaltpläne aus E3.Cable 
PRT 
Datenformat für DMU-Modelle 
XLS 
Datenformat für Abweichtabellen 


### 第 25 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 25 von 119 
 
4.10 DS/DZ-Kennzeichnung und ESD-Kenner 
4.10.1 DS/DZ-Kennzeichnung 
Die Dokumentation Sicherheit (DS) und die Dokumentation Zertifizierung (DZ) sind in der KBL zu 
ergänzen. Die Beschreibung der Merkmalserläuterung muss dem aktuellen Merkmalskatalog 
entsprechen. Der aktuelle Merkmalskatalog ist in SMARAGD/Engineering Portal zu entnehmen. Details 
zu der Kennzeichnung von besonderen Merkmalen sind aus der MBN 10 317 „Kennzeichnung von 
Merkmalen zur besonderen Nachweisführung“ zu entnehmen. 
Zur Abbildung der DS/DZ-Kennzeichnung in der KBL werden die Klassen Harness und Module genutzt. 
In der Klasse Harness erfolgt die Definition der verwendeten DS/DZ-Kennzeichnungen getrennt für DS 
und DZ-Merkmale, indem zunächst das jeweilige Merkmal als <Instruction_type> gesetzt wird und 
dessen Erläuterung im Anschluss mit Komma getrennt im <Instruction_value> steht. 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Prozessinformationen 
Processing_information 
P 
 
 
Objekt-id 
id 
P 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
P 
„DS“ 
Instruktionswert 
Instruction_value 
P 
„1-S = …, 2-S = …, 3-S = ….“ 
 
 
Abbildung 4: Definition eines DS-Kennzeichens in der Klasse <Harness> 
In der Klasse Module werden die in der Klasse Harness definierten Werte angezogen und referenziert, 
sofern sie für das Modul zutreffen. Dabei wird das referenzierte Kennzeichen als <Instruction_type> und 
die referenzierten Werte nach Komma getrennt als <Instruction_value> angelegt. 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Prozessinformationen 
Processing_information 
P 
 
 
Objekt-id 
id 
P 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
P 
„DS“ 
Instruktionswert 
Instruction_value 
P 
1-S, 2-S 
 


### 第 26 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 26 von 119 
 
 
Abbildung 5: Instanziierung eines DS-Kennzeichens in der Klasse <Module> 
 
4.10.2 ESD-Kenner 
Die Entwicklung legt die Empfindlichkeit des Steuergeräts, Bauteils, ZB‘s oder LU’s gegen 
elektrostatische Entladungen und damit seine ESD-Relevanz fest. Diese Festlegung wird durch das 
Kennzeichen, im folgenden ESD-Kenner (electrostatic discharge) genannt, sowohl im System Smaragd 
als auch auf der Zeichnung und somit ebenso in der mitführenden KBL dokumentiert. Die Modellierung 
des ESD-Kenners erfolgt dabei über <Processing_information> in der Klasse Module: 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Prozessinformationen 
Processing_information 
P 
 
 
Objekt-id 
id 
P 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
P 
„ESD“ 
Instruktionswert 
Instruction_value 
P 
„J“/ „N“ 
 
Ein Modul, welches empfindlich gegen elektrostatische Entladung ist, trägt den <Instruction_value> „J“, 
ein Modul, welches unempfindlich gegen elektrostatische Entladung ist, trägt den <Instruction_value> 
„N“. 
 
Abbildung 6: Beispiel eines positiven ESD-Kenners 
Details zur Dokumentation des ESD-Kenners sind der A 059 80 30 zu entnehmen. 
 


### 第 27 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 27 von 119 
 
5 KBL-Container 
Der KBL-Container enthält neben den verschiedenen Objekt-Klassen das Attribut <version_id> mit der 
Angabe zur KBL-Version. 
Die auf der Zeichnung verwendeten Einzelteile wie Stecker, Leitungen usw. werden mit ihren 
technischen Merkmalen in Form einer Bibliothek einmalig in eigens vorgesehenen Klassen im 
KBL_Container angelegt und bei Verwendung in einem der Leitungssatzmodule von dort referenziert.  
In den folgenden Kapiteln werden diese Klassen näher beschrieben. Der Harness-Container wird in 
Kapitel 6 beschrieben. 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
eigene Attribute 
attributes 
P 
siehe Abschnitt 5.1 
Zusatzteil 
Accessory 
P 
siehe Abschnitt 5.2 
Genehmigung 
Approval 
- 
wird nicht verwendet 
Zusammenbauteil 
Assembly_part 
- 
wird nicht verwendet 
Kartesische Koordinate 
Cartesian_point 
P 
siehe Abschnitt 5.3 
Blindstopfen 
Cavity_plug 
P 
siehe Abschnitt 5.4 
Einzeladerdichtung 
Cavity_seal 
P 
siehe Abschnitt 5.5 
Änderungsbeschreibung 
Change_description 
O 
siehe Abschnitt 5.7 
Co-Pack-Part 
Co_pack_part 
- 
wird nicht verwendet 
Komponente 
Component 
P 
siehe Abschnitt 5.8 
Komponentenbox 
Component_box 
P 
siehe Abschnitt 5.9 
Kontaktgehäuse 
Connector_housing 
P 
siehe Abschnitt 5.10 
Erzeugung 
Creation 
- 
wird nicht verwendet 
Standardtoleranz 
Default_dimension_specification 
P 
siehe Abschnitt 5.11 
Festlegungsmaß 
Dimension_specification 
P 
siehe Abschnitt 5.12 
Externe Referenz 
External_reference 
P 
siehe Abschnitt 5.13 
Befestigungselement 
Fixing 
P 
siehe Abschnitt 5.14 
Kontakt 
General_terminal 
P 
siehe Abschnitt 5.15 
Leitung 
General_wire 
P 
siehe Abschnitt 5.16 
Leitungssatz 
Harness 
P 
siehe Abschnitt 6 
Knoten 
Node 
P 
siehe Abschnitt 5.17 
Verlegung 
Routing 
P 
siehe Abschnitt 5.18 
Segment 
Segment 
P 
siehe Abschnitt 5.19 
Einheit 
Unit 
P 
siehe Abschnitt 5.20 


### 第 28 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 28 von 119 
 
Leitungsschutz 
Wire_protection 
P 
siehe Abschnitt 5.21 
 
5.1 Attribute des KBL-Containers 
Um die Kompatibilität zu den verwendeten Werkzeugen sicherzustellen, ist die zum Zeitpunkt des 
Erscheinens dieser Ausführungsvorschrift gültige KBL-Version 2.4 SR-1 zu verwenden. Die Release- 
Zyklen weiterer KBL-Versionen sind zwingend mit MBC abzustimmen. 
Für die Abbildung der Inhalte sind die im KBL-Schema vereinbarten Zeichensatzdeklarationen zu 
verwenden. 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Version 
version_id 
(P) 
siehe Abschnitt 4.9.1 
 
 
 
Abbildung 7: Auszug KBL-Container (basierend KBL 2.4 SR-1) 
 
5.2 Zusatzteil (Accessory) 
Diese Klasse enthält alle Teile, die als Zusatzteil zu einem anderen Teil fungieren oder sich am 
Leitungsstrang befinden und dabei nicht zur Befestigung benötigt werden sowie nicht direkt einem 
Leitungsanschluss am Stecker zugeordnet werden können.  
Dies betrifft Teile wie Butyl-Terostat, Etiketten, Farbbänder, Markierer, Kabelbinder, Normteil, PUR-
Schaumschlauch.  
Im Folgenden ein Schema-Ausschnitt aus der KBL. 
 


### 第 29 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 29 von 119 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Teilenummer 
Part_number 
(P) 
<MBC Sachnummer> oder    
<Normteilsachnummer> 
Lieferantenname 
Company_name 
(P) 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
Lieferantenteilenummer 
Anwendungsbereich 
Scope 
O 
Lieferantenbezeichnung 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Version 
Version 
(O) 
Teileversion bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
(P) 
<MBC Positionsnummer>, 
siehe Kapitel 4.8 
Beschreibung 
Description 
(P) 
Teilebenennung 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
englische Teilebenennung 
Vorgängerteilenummer 
Predecessor_part_number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Copyright-Information 
Copyright_note 
- 
 
Masseninformation 
Mass_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 


### 第 30 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 30 von 119 
 
Einheitskomponente 
Unit_component 
(P) 
Masseneinheit [g] 
Wertekomponente 
Value_component 
(P) 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die zugehörige 
Zeichnung (siehe Kapitel 
5.13) 
Änderung 
Change 
- 
 
Materialinformationen 
Material 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Materialschlüssel 
Material_key 
(O) 
 
Referenzsystem 
Material_reference_system 
O 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Zusatzteile-Typ 
Accessory_type 
P 
CONNECT-Klasse des 
Bauteils 
 
 
Abbildung 8: Auszug <Accessory> (basierend KBL 2.4) 
5.3 Kartesische Koordinate (Cartesian_point) 
Diese Klasse enthält die 2D/3D-Koordinaten für Knoten und Komponenten des Leitungssatzes. 
Alle Koordinaten sind bezogen auf ein rechtsdrehendes Koordinatensystem anzugeben. Die Koordinaten 
folgen dabei dem Maßstab 1.0 = 1 mm. Im Folgenden ein Schema-Ausschnitt aus der KBL. 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Koordinate 
Coordinates 
(P) 
Reihenfolge X 
Koordinate 
Coordinates 
(P) 
Reihenfolge Y 


### 第 31 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 31 von 119 
 
Koordinate 
Coordinates 
P 
Reihenfolge Z 
 
 
Abbildung 9: Auszug <Cartesian_point> 
5.4 Blindstopfen (Cavity_plug) 
Diese Klasse enthält alle Teile der CONNECT Teilegruppen Blindstopfen, die direkt einer Kammer 
zugeordneten werden können.  
 
Im Folgenden ein Schema-Ausschnitt aus der KBL. 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Teilenummer 
Part_number 
(P) 
<MBC Sachnummer> oder    
<Normteilsachnummer> 
Lieferantenname 
Company_name 
(P) 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 


### 第 32 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 32 von 119 
 
Alias-id 
Alias_id 
(O) 
Lieferantenteilenummer 
Anwendungsbereich 
Scope 
O 
Lieferantenbezeichnung 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Version 
Version 
(O) 
Teileversion bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
(P) 
<MBC Positionsnummer>, 
siehe Kapitel 4.8 
Beschreibung 
Description 
(P) 
Teilebenennung 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
englische Teilebenennung 
Vorgängerteilenummer 
Predecessor_part_number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Copyright-Information 
Copyright_note 
- 
 
Masseninformation 
Mass_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Masseneinheit [g] 
Wertekomponente 
Value_component 
(P) 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die zugehörige 
Zeichnung (siehe Kapitel 
5.13) 
Änderung 
Change 
- 
 
Materialinformationen 
Material 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Materialschlüssel 
Material_key 
(O) 
 
Referenzsystem 
Material_reference_system 
O 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Farbe 
Colour 
P 
Farbe des Blindstopfens 
Stopfentyp 
Plug_type 
- 
 


### 第 33 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 33 von 119 
 
 
5.5 Einzeladerdichtung (Cavity_seal) 
Diese Klasse enthält alle Teile der CONNECT Teilegruppe Einzeladerabdichtung, die direkt einem 
Leitungsanschluss zugeordnet werden können.  
Im Folgenden ein Schema-Ausschnitt aus der KBL. 
 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Teilenummer 
Part_number 
(P) 
<MBC Sachnummer> oder    
<Normteilsachnummer> 
Lieferantenname 
Company_name 
(P) 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
Lieferantenteilenummer 
Anwendungsbereich 
Scope 
O 
Lieferantenbezeichnung 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Version 
Version 
(O) 
Teileversion bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
(P) 
<MBC Positionsnummer>, 


### 第 34 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 34 von 119 
 
siehe Kapitel 4.8 
Beschreibung 
Description 
(P) 
Teilebenennung 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
englische Teilebenennung 
Vorgängerteilenummer 
Predecessor_part_number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Copyright-Information 
Copyright_note 
- 
 
Masseninformation 
Mass_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Masseneinheit [g] 
Wertekomponente 
Value_component 
(P) 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die zugehörige 
Zeichnung (siehe Kapitel 
5.13) 
Änderung 
Change 
- 
 
Materialinformationen 
Material 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Materialschlüssel 
Material_key 
(O) 
 
Referenzsystem 
Material_reference_system 
O 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Farbe 
Colour 
P 
Farbe der ELA 
Dichtungstyp 
Seal_type 
O 
 
Kabelgröße 
Wire_size 
P 
 
 
Objekt-id 
Id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Einheit "mm" 
Minimalwert 
Minimum 
(P) 
Leitungsdurchmesser min 
Maximalwert 
Maximum 
(P) 
Leitungsdurchmesser max 
 
5.6 Änderung (Change) 


### 第 35 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 35 von 119 
 
Änderungen am Leitungssatz sind entsprechend den Anforderungen der MBN 31020-3 in der 
Leitungssatzzeichnung zu erfassen und zu beschreiben. Die Inhalte der änderungsrelevanten Inhalte der 
Leitungssatzzeichnung sind in die Klasse Change (in der Klasse Harness als auch Module) zu 
übernehmen. 
Die änderungsrelevanten Informationen aus der Zeichnung (aus dem Zeichnungsschriftkopf bzw. aus 
der kZ-Tabelle) werden in folgende Attribute der Klasse Change befüllt.  
Im Folgenden ein Schema-Ausschnitt aus der KBL. 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Id 
Id 
P 
ZGS 
Beschreibung 
 
Description 
 
P 
Wert aus dem Feld 
Änderungsbeschreibung 
Lokale Beschreibung 
Localized_description 
O 
 
 
Objekt-id 
Id 
(O) 
automatisch generierte id 
Sprachencode 
Language_code 
(O) 
„En“ 
Wert 
Value 
(O) 
Englische 
Änderungsbeschreibung 
Änderungsmeldung 
Change_request 
P 
KEM-Bezeichnung, Wert aus 
dem Feld Auftr.-Nr./ order 
no. 


### 第 36 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 36 von 119 
 
Änderungsdatum 
Change_date 
P 
Wert aus dem Feld 
Änderungsdatum 
verantwortlicher Designer 
Responsible_designer 
(P) 
verantwortlicher Entwickler 
(Lieferant) aus der 
Lieferantentabelle 
verantwortliche Abteilung 
Designer_department 
(P) 
Abteilung, Firma (Lieferant) 
aus der Lieferantentabelle 
Genehmiger Name 
Approver_name 
P 
Wert aus dem Feld 
Bearbeit./auth 
Genehmiger Abteilung 
Approver_department 
P 
Wert aus dem Feld federf. 
Abteilung/ resp. dep. 
 
 
Abbildung 10: Auszug <Change> (basierend KBL 2.4) 
 
5.7 Änderungsbeschreibung (Change_description) 
Beinhaltet die Änderungsbeschreibungen aus ConnectCHANGE mit folgenden Attributen. 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Id 
Id 
P 
 
Änderungsmeldung aus 
ConnectCHANGE 
Beschreibung 
Description 
P 
 
Beschreibung der Änderung 
(Betreff aus 
ConnectCHANGE) 
Lokale Beschreibung 
Localized_description 
O 
 
 
Objekt-id 
Id 
(O) 
automatisch generierte id 
Sprachencode 
Language_code 
(O) 
„En“ 
Wert 
Value 
(O) 
Englische Beschreibung der 


### 第 37 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 37 von 119 
 
Änderung 
Änderungsmeldung 
Change_request 
P 
Wert aus dem Feld 
"Änderungsgrund" in 
ConnectCHANGE 
Änderungsdatum 
Change_date 
P 
Änderungsdatum 
verantwortlicher Designer 
Responsible_designer 
(P) 
verantwortlicher Entwickler 
(Lieferant) aus der 
Lieferantentabelle 
verantwortliche Abteilung 
Designer_department 
(P) 
Abteilung, Firma (Lieferant) 
aus der Lieferantentabelle 
Genehmiger Name 
Approver_name 
P 
Wert aus dem Feld 
Bearbeit./auth 
Genehmiger Abteilung 
Approver_department 
P 
Wert aus dem Feld federf. 
Abteilung/ resp. dep. 
Geänderte Elemente 
Changed_elemente 
O 
Verweis auf die im Zuge der 
Änderung geänderten 
Elemente  
(sofern vorhanden) 
Verwandte Änderungen 
Related_changes 
O 
 
Verweis auf die zugehörigen 
Änderungen der Klasse 
Change 
 
5.8 Komponente (Component) 
Diese Klasse enthält alle Teile der CONNECT Teilegruppen elektrische Komponenten, Sicherungen und 
Relais, die nicht direkt einem Leitungsanschluss zugeordneten werden können.  
Im Folgenden ein Schema-Ausschnitt aus der KBL. 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 


### 第 38 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 38 von 119 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Teilenummer 
Part_number 
(P) 
<MBC Sachnummer> oder    
<Normteilsachnummer> 
Lieferantenname 
Company_name 
(P) 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
Lieferantenteilenummer 
Anwendungsbereich 
Scope 
O 
Lieferantenbezeichnung 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Version 
Version 
(O) 
Teileversion bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
(P) 
<MBC Positionsnummer>, 
siehe Kapitel 4.8 
Beschreibung 
Description 
(P) 
Teilebenennung 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
englische Teilebenennung 
Vorgängerteilenummer 
Predecessor_part_number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Copyright-Information 
Copyright_note 
- 
 
Masseninformation 
Mass_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Masseneinheit [g] 
Wertekomponente 
Value_component 
(P) 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die zugehörige 
Zeichnung (siehe Kapitel 
5.13) 
Änderung 
Change 
- 
 
Materialinformationen 
Material 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 


### 第 39 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 39 von 119 
 
Materialschlüssel 
Material_key 
(O) 
 
Referenzsystem 
Material_reference_system 
O 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
 
Speziell für Sicherungen ist die Kindklasse Fuse zu benutzen.  Diese instanziiert zusätzlich folgende 
Attribute: 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
… 
… 
… 
… 
Typ 
Type 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Schlüssel 
Key 
(P) 
Spalte SI_Typ in Sicherungs-
belegungstabelle  
(MEGA, MIDI, etc.) 
Referenzsystem 
Reference_system 
(P) 
zugehörige Norm, in der der 
Sicherungstyp definiert ist 
Nennstrom 
Nominal_current 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
 
Wertekomponente 
Value_component 
(P) 
Ist-SI-Wert Sicherung aus 
Sicherungsbelegungstabelle 


### 第 40 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 40 von 119 
 
Farbe 
Colour 
P 
Farbe der Sicherung laut 
CONNECT Teilekatalog 
 
 
Abbildung 11: Auszug Komponenten/Component (basierend KBL 2.4) 
 
5.9 Komponentenbox (Component_box) 
Diese Klasse enthält alle Teile der CONNECT Teilegruppen Sicherungsbox/-dose und Potentialverteiler. 
Bei der Dokumentation von 7-er Teilen (Dialog Dokumentationsmethode)  ist zu beachten, dass diese 
Komponente zwecks konsistenter Verbindungen in der KBL vorhanden sein müssen, diese aber nicht 
von einem Modul (Attribut <Controlled_components>) referenziert werden, da sie nicht ein Teil der 
Stückliste sind.  
Im Folgenden ein Schema-Ausschnitt aus der KBL. 
 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 


### 第 41 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 41 von 119 
 
Teilenummer 
Part_number 
(P) 
<MBC Sachnummer> oder    
<Normteilsachnummer> 
Lieferantenname 
Company_name 
(P) 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
Lieferantenteilenummer 
Anwendungsbereich 
Scope 
O 
Lieferantenbezeichnung 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Version 
Version 
(O) 
Teileversion bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
(P) 
<MBC Positionsnummer>, 
siehe Kapitel 4.8 
Beschreibung 
Description 
(P) 
Teilebenennung 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
englische Teilebenennung 
Vorgängerteilenummer 
Predecessor_part_number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Copyright-Information 
Copyright_note 
- 
 
Masseninformation 
Mass_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Masseneinheit [g] 
Wertekomponente 
Value_component 
(P) 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die zugehörige 
Zeichnung (siehe Kapitel 
5.13) 
Änderung 
Change 
- 
 
Materialinformationen 
Material 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Materialschlüssel 
Material_key 
(O) 
 


### 第 42 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 42 von 119 
 
Referenzsystem 
Material_reference_system 
O 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Komponentenbox-Stecker 
Component_box_connectors 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Id 
Id 
(P) 
Nummer des Steckplatzes 
(z.B. "1/S") 
kompatible Kontakt-
gehäuse 
Compatible_housings 
O 
 
integrierte Kammern 
Integrated_slots 
P 
 
Komponentenkammern 
Component_slots 
 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Id 
Id 
(P) 
Benennung des Sicherungs-
/Relaissteckplatzes (z.B. 
"F125") 
Typ 
Type 
P 
siehe Abschnitt 4.9.10 
gültige Sicherungstypen 
Valid_fuse_types 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Schlüssel 
Key 
(O) 
Spalte SI_Typ in 
Sicherungsbelegungstabelle 
(MEGA, MIDI, etc.) 
Referenzsystem 
Reference_system 
(O) 
zugehörige Norm, in der der 
Sicherungstyp definiert ist 
Minimalstrom 
Min_current 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(O) 
Einheit „A“ 
Wertekomponente 
Value_component 
(O) 
 
Maximalstrom 
Max_current 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Einheit „A“ 
Wertekomponente 
Value_component 
(P) 
Max-Si-Wert (aus Sicherungs-
belegungstabelle) 


### 第 43 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 43 von 119 
 
Komponenten-Pins 
Component_cavities 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Pin-Nummer 
Cavity_number 
(P) 
 
Interne Verbindungen 
Connections 
P 
nur für Sicherungsboxen 
(nicht für Potentialverteiler) 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Id 
Id 
(P) 
Verbindungsnummer von 
Innenverschaltung aus 
E3.Cable 
Pins 
Cavities 
P 
 
Komponentenkammern 
Component_cavities 
P 
 
 
5.10 Kontaktgehäuse (Connector_housing) 
Diese Klasse enthält alle Teile der CONNECT Teilegruppen Kontaktgehäuse, Kabelschuh sowie Splices, 
die direkt einem Leitungsanschluss zugeordneten werden können.  
Im Folgenden ein Schema-Ausschnitt aus der KBL. 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Teilenummer 
Part_number 
(P) 
<MBC Sachnummer> oder    
<Normteilsachnummer> 


### 第 44 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 44 von 119 
 
Lieferantenname 
Company_name 
(P) 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
Lieferantenteilenummer 
Anwendungsbereich 
Scope 
O 
Lieferantenbezeichnung 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Version 
Version 
(O) 
Teileversion bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
(P) 
<MBC Positionsnummer>, 
siehe Kapitel 4.8 
Beschreibung 
Description 
(P) 
Teilebenennung 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
englische Teilebenennung 
Vorgängerteilenummer 
Predecessor_part_number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Copyright-Information 
Copyright_note 
- 
 
Masseninformation 
Mass_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Masseneinheit [g] 
Wertekomponente 
Value_component 
(P) 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die zugehörige 
Zeichnung (siehe Kapitel 
5.13) 
Änderung 
Change 
- 
 
Materialinformationen 
Material 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Materialschlüssel 
Material_key 
(O) 
 
Referenzsystem 
Material_reference_system 
O 
 
Prozessinformationen 
Processing_information 
O 
 


### 第 45 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 45 von 119 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Steckerfarbe 
Housing_colour 
P 
Gehäusefarbe 
Pflicht nur für 
Kontaktgehäuse 
Steckercode 
Housing_code 
P 
Codierung 
Pflicht nur für 
Kontaktgehäuse 
Steckertyp 
Housing_type 
P 
siehe Abschnitt 4.9.2 
Kammern 
Slots 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Id 
Id 
P 
 
Anzahl Pins 
Number_of_cavities 
(P) 
 
Pins 
Cavities 
(P) 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Pin-Nummer 
Cavity_number 
(P) 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
 
 
 
Abbildung 12: Auszug <Connector_housing> (basierend KBL 2.4) 
5.11 Standardtoleranz (Default_dimension_specification) 


### 第 46 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 46 von 119 
 
Standardtoleranzen werden genutzt, um übergreifende Toleranzen digital im KBL-Format zu 
dokumentieren. 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Wertebereich 
Dimension_value_range 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
 
Minimalwert 
Minimum 
(P) 
 
Maximalwert 
Maximum 
(P) 
 
Toleranztyp 
Tolerance_type 
P 
siehe Abschnitt 4.9.3 
Externe Referenzen 
External_references 
O 
 
Toleranzangabe 
Tolerance_indication 
P 
Toleranzangabe 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Untere Grenze 
Lower_limit 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
 
Wertekomponente 
Value_component 
(P) 
 
Obere Grenze 
Upper_limit 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
 
Wertekomponente 
Value_component 
(P) 
 
 


### 第 47 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 47 von 119 
 
Die Standardtoleranzen für Segmentlänge und den Abstand von Befestigungselementen (vergleiche 
AV Leitungssatz-Dokumentation) sind entsprechend zu dokumentieren (Beispieldokumentation siehe 
Abbildung 13).  
 
 
Abbildung 13: KBL-Abbildung von Standardtoleranzen 
 
 
 
5.12 Festlegungsmaße (Dimension_specification) 
Elemente der Klasse Dimension_specification werden genutzt, um Festlegungsmaße oder detaillierte 
Segmentmaße zu transportieren.  
Im Folgenden ein Schema-Ausschnitt aus der KBL. 
 


### 第 48 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 48 von 119 
 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Id 
Id 
P 
Id 
Dimensionswert 
Dimension_value 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Einheit „mm“ 
Wertekomponente 
Value_component 
(P) 
Längenwert 
Segmente 
Segments 
O 
Verwendung nur falls die 
Toleranz eines Segmentes 
eingeschränkt werden soll 
Referenzelement 
Origin 
(P) 
Referenz-/Bezugselement 
der Bemaßung 
Zielelement 
Target 
(P) 
Zielelement der Bemaßung 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 


### 第 49 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 49 von 119 
 
Instruktionswert 
Instruction_value 
(O) 
 
Toleranzangabe 
Tolerance_indication 
P 
Toleranzangabe 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Untere Grenze 
Lower_limit 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Längeneinheit [mm] 
Wertekomponente 
Value_component 
(P) 
Längenwert 
Obere Grenze 
Upper_limit 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Längeneinheit [mm] 
Wertekomponente 
Value_component 
(P) 
Längenwert 
 
 
5.13 Externe Referenz (External_reference) 
Diese Klasse enthält eine Liste aller Referenzen auf externe Dokumente zu den im Harness 
verwendeten Einzelteilen oder den zur Erstellung verwendeten Quelldokumenten wie Schaltplan und 
DMU-Topologiedaten. Im Folgenden ein Schema-Ausschnitt aus der KBL. 
 
 
 
 


### 第 50 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 50 von 119 
 
Folgende Attribute sind erforderlich: 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Dokumententyp 
Document_type 
(P) 
siehe Abschnitt 4.9.4 
Dokumentennummer 
Document_number 
(P) 
<MBC Sachnummer>,  
<Schaltplansachnummer>, 
<MBC DMU-Sachnummer>, 
<MBN …> 
Change Level 
Change_level 
(P) 
"JJJJ-MM-TT" bzw. 
<Smaragdversion> bzw. „-„ 
Dateiname 
File_name 
P 
Benennung der Dokumenten-
SNR 
Datenort 
Location 
- 
 
Datenformat 
Data_format 
(P) 
siehe Abschnitt 0 
Quellensystem 
Creating_system 
P 
siehe Abschnitt 0 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
 
 
Abbildung 14. Auszug <External_reference> (basierend KBL 2.4) 
 
5.14 Befestigungsteil (Fixing) 
Diese Klasse enthält Teile, die zur Befestigung des Leitungssatzes benötigt werden und nicht direkt 
einem Leitungsanschluss am Stecker zugeordnet werden können, sondern den Leitungsstrang direkt 
am Rohbau befestigen. Über das Attribut <Fixing_type> kann bei Befestigungselementen mit einem 
MultipleFixingAssignment unterschieden werden, in welcher Form sie darzustellen sind (vgl. AV 
Leitungssatzdokumentation). 
 


### 第 51 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 51 von 119 
 
Im Folgenden ein Schema-Ausschnitt aus der KBL. 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Teilenummer 
Part_number 
(P) 
<MBC Sachnummer> oder    
<Normteilsachnummer> 
Lieferantenname 
Company_name 
(P) 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
Lieferantenteilenummer 
Anwendungsbereich 
Scope 
O 
Lieferantenbezeichnung 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Version 
Version 
(O) 
Teileversion bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
(P) 
<MBC Positionsnummer>, 
siehe Kapitel 4.8 
Beschreibung 
Description 
(P) 
Teilebenennung 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
englische Teilebenennung 
Vorgängerteilenummer 
Predecessor_part_number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 


### 第 52 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 52 von 119 
 
Copyright-Information 
Copyright_note 
- 
 
Masseninformation 
Mass_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Masseneinheit [g] 
Wertekomponente 
Value_component 
(P) 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die zugehörige 
Zeichnung (siehe Kapitel 
5.13) 
Änderung 
Change 
- 
 
Materialinformationen 
Material 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Materialschlüssel 
Material_key 
(O) 
 
Referenzsystem 
Material_reference_system 
O 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Befestigungstyp 
Fixing_type 
P 
CONNECT-Klasse des 
Bauteils 
 
 
Abbildung 15: Auszug <Fixing> (basierend KBL 2.4) 
5.15 Kontakt (General_terminal) 
Diese Klasse enthält alle Teile der CONNECT Teilegruppen Kontakte, die direkt einem Leitungsanschluss 
zugeordnet werden können.  
Im Folgenden ein Schema-Ausschnitt aus der KBL. 
 


### 第 53 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 53 von 119 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Teilenummer 
Part_number 
(P) 
<MBC Sachnummer> oder    
<Normteilsachnummer> 
Lieferantenname 
Company_name 
(P) 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
Lieferantenteilenummer 
Anwendungsbereich 
Scope 
O 
Lieferantenbezeichnung 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Version 
Version 
(O) 
Teileversion bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
(P) 
<MBC Positionsnummer>, 
siehe Kapitel 4.8 
Beschreibung 
Description 
(P) 
Teilebenennung 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
englische Teilebenennung 


### 第 54 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 54 von 119 
 
Vorgängerteilenummer 
Predecessor_part_number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Copyright-Information 
Copyright_note 
- 
 
Masseninformation 
Mass_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Masseneinheit [g] 
Wertekomponente 
Value_component 
(P) 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die zugehörige 
Zeichnung (siehe Kapitel 
5.13) 
Änderung 
Change 
- 
 
Materialinformationen 
Material 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Materialschlüssel 
Material_key 
(O) 
 
Referenzsystem 
Material_reference_system 
O 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Kontakttyp 
Terminal_type 
P 
Kontaktfamilie (aus 
CONNECT) 
Überzugmaterial 
Plating_material 
P 
Abkürzung d. Beschichtung 
(Großbuchstabe + 
Kleinbuchstabe) 
Querschnittsfläche 
Cross_section_area 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Flächeneinheit [mm²] 
Minimalwert 
Minimum 
(P) 
min. Anschlussquerschnitt 
Maximalwert 
Maximum 
(P) 
max. Anschlussquerschnitt 
Außendurchmesser 
Outside_diameter 
O 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Längeneinheit [mm] 
Minimalwert 
Minimum 
(P) 
Minimal-Außendurchmesser 
Maximalwert 
Maximum 
(P) 
Maximal-Außendurchmesser 


### 第 55 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 55 von 119 
 
 
 
Abbildung 16: Auszug <General_terminal> 
 
5.16 Leitungen (General_wire) 
Diese Klasse enthält alle Teile der CONNECT Teilegruppen Einzel- und Sonderleitungen. Im Folgenden 
ein Schema-Ausschnitt aus der KBL. 
 
 
Um die möglichen Leitungskonfigurationen eines einzelnen Leitungstyps eindeutig zu beschreiben 
werden über 500 Sachnummern benötigt. Für die Daimler-internen Prozesse werden daher die 
Sachnummern aus den Merkmalen der Leitung automatisch generiert und in den internen Prozessen 
verwendet. Für Einzelleitungen werden die Sachnummer (<Part_number>) und Benennung 
(<Description>) aus der Kurzbezeichnung des Leitungstyps, dem Leiterquerschnitt und den Leiterfarben 
gebildet, die erforderlichen Einzelwerte werden über die ConnyE-Schnittstelle aus ConnectPARTS 
bereitgestellt. 
Die im Beispiel dargestellten Werte sind in der KBL unbedingt wie beschrieben zu befüllen.  
 
Sind Daimler-Sachnummern oder Normteilnummern erforderlich, sind diese im Attribut <Part_number> 


### 第 56 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 56 von 119 
 
zu beschreiben. 
Verdrillte Einzelleitungen („Bohrmaschinenleitungen“) werden analog zu den Einzelleitungen aus den 
einzelnen Merkmalen der Einzelleitungen kombiniert. Dazu wird das Attribut <Partnumber> als 
Kombination 
von 
<CAD-Kennzeichen>-<Anzahl 
Leiter>x<Querschnitt 
Einzelleitung> 
<Erste 
Leitungsfarbe>+<Zweite Leitungsfarbe> gebildet (bspw. B48/2-2x0.13 WH+WH/GN). 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Teilenummer 
Part_number 
(P) 
<MBC Sachnummer> oder    
<Normteilsachnummer> 
Lieferantenname 
Company_name 
(P) 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
Lieferantenteilenummer 
Anwendungsbereich 
Scope 
O 
Lieferantenbezeichnung 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Version 
Version 
(O) 
Teileversion bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
(P) 
Abkürzung d. 
Leitungsbeschreibung 
(Leitungstyp bei Einzelleit.,  
CAD-Nr bei Sonderleitungen) 
Beschreibung 
Description 
(P) 
Teilebenennung 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
englische Teilebenennung 
Vorgängerteilenummer 
Predecessor_part_number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Copyright-Information 
Copyright_note 
- 
 
Masseninformation 
Mass_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 


### 第 57 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 57 von 119 
 
Einheitskomponente 
Unit_component 
(P) 
Masseneinheit [g/m] 
Wertekomponente 
Value_component 
(P) 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die zugehörige 
Zeichnung (siehe Kapitel 
5.13) [nur für 
Sonderleitungen] 
Änderung 
Change 
- 
 
Materialinformationen 
Material 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Materialschlüssel 
Material_key 
(O) 
 
Referenzsystem 
Material_reference_system 
O 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Kabelbezeichner 
Cable_designator 
- 
 
Leitungstyp 
Wire_type 
P 
Bei Einzelleitungen: 
Kombination Abkürzung 
Leitungstyp - Querschnitt 
Bei Sonderleitungen: 
CAD-Nr 
Biegeradius 
Bend_radius 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(O) 
Längeneinheit [mm] 
Wertekomponente 
Value_component 
(O) 
Wert 
Querschnittsfläche 
Cross_section_area 
P 
nur für Einzelleitungen 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Flächeneinheit [mm²] 
Wertekomponente 
Value_component 
(P) 
Wert 
Außendurchmesser 
Outside_diameter 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Längeneinheit [mm] 
Wertekomponente 
Value_component 
(P) 
Wert 
Ader 
Core 
P 
nur für Sonderleitungen 


### 第 58 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 58 von 119 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Id 
Id 
P 
 
Kabelbezeichner 
Cable_designator 
- 
 
Leitungstyp 
Wire_type 
P 
 
Querschnittsfläche 
Cross_section_area 
(P) 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Flächeneinheit [mm²] 
Wertekomponente 
Value_component 
(P) 
Wert 
Biegeradius 
Bend_radius 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(O) 
Längeneinheit [mm] 
Wertekomponente 
Value_component 
(O) 
Wert 
Außendurchmesser 
Outside_diameter 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Längeneinheit [mm] 
Wertekomponente 
Value_component 
(P) 
Wert 
Aderfarbe 
Core_colour 
(P) 
Leitungsfarbe 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Farbtyp 
Colour_type 
(P) 
siehe Abschnitt 4.9.5 
Farbwert 
Colour_value 
(P) 
Farbbezeichnung nach  
IEC 60 757 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Isolationsfarbe 
Cover_colour 
(P) 
Leitungsfarbe 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Farbtyp 
Colour_type 
(P) 
siehe Abschnitt 4.9.5 
Farbwert 
Colour_value 
(P) 
Farbbezeichnung nach IEC 
60 757 
 


### 第 59 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 59 von 119 
 
 
 
Abbildung 17: Auszug <General_wire> (basierend KBL 2.4) 
 
5.17 Knoten (Node) 
Diese Klasse enthält die Abzweige und Endpunkte (Komponenten) innerhalb der Topologie des 
Leitungssatzes. Die Topologie-Information der 2D, 3D bzw. Formboard-Koordinaten ist in den KBL-
Attributen Processing_information nach dem definierten Schema dokumentiert. Die Angabe der DMU-
Koordinaten innerhalb der Prozessinformationen ist dabei verpflichtend. 
Im Folgenden ein Schema-Ausschnitt aus der KBL. 


### 第 60 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 60 von 119 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_BNJ“ 
Id 
Id 
(P) 
 
Alias ID 
Alias_id 
O 
siehe Abschnitt 7.1 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
 
Bereich 
Scope 
O 
 
Beschreibung 
Description 
O 
 
Lokalisierte Beschreibung 
Localized_description 
O 
 
Kartesische Koordinate 
Cartesian_point 
(P) 
 
Referenzierte Komponenten 
Referenced_components 
P 
Referenz auf zugewiesene 
Komponente 
Referenzierte Kammern 
Referenced_cavities 
P 
Referenz auf Kammern bei 
Steckern mit mehreren 
Eintrittspunkten (siehe 
Abschnitt *******) 


### 第 61 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 61 von 119 
 
Prozessinformationen 
Processing_instruction 
P/O 
Information über DMU-
Koordinaten (Pflicht) sowie 
2D- und Formboard-
Koordinaten (Optional) 
 
Objekt-id 
id 
(P/O
) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(P/O
) 
DMU_Coordinates (P)  
2D_Coordinates (O)  
FB_Coordinates (O) 
Instruktionswert 
Instruction_value 
(P/O
) 
3D-Koordinaten in "x y z"  
(mit " " als Trennzeichen);  
2D-Koordinaten in "x y" 
 
Beispiel Node 2 zeigt den Anschlusspunkt einer Komponente (ID_CON_A1) 
Beispiel Node 1 zeigt eine einfache Verzweigung in der Leitungssatztopologie 
 
 
Abbildung 18: Auszug <Node> (basierend KBL 2.4) 
 
5.18 Verlegung (Routing) 
Diese Klasse enthält für alle elektrische Verbindungen (Leitungen) eine Liste der benutzten Segmente 
des Leitungssatzes und stellt damit die Kombination zwischen Logik und Topologie dar.  
Im Folgenden ein Schema-Ausschnitt aus der KBL. 
 


### 第 62 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 62 von 119 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
geroutetes Kabel 
Routed_wire 
(P) 
laufende Nummer der 
Verbindung aus Klasse 
Connection 
Segmente 
Segments 
P 
Liste der id’s der 
durchlaufenen Segmente 
 
 
Abbildung 19: Auszug <Routing> 
 
5.19 Segment (Segment) 
Diese Klasse enthält alle Ausbindungen und Segmente des Leitungssatzes. Der dazugehörige Schema-
Ausschnitt ist im Folgenden abgebildet. 
 


### 第 63 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 63 von 119 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_BNS“ 
Id 
Id 
(P) 
 
Alias-Id 
Alias_id 
O 
siehe Abschnitt 7.1 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-Id 
Alias_id 
(O) 
 
Anwendungsbereich 
Scope 
O 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Start-Vektor 
Start_vector 
P 
 
End-Vektor 
End_vector 
P 
 


### 第 64 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 64 von 119 
 
virtuelle Länge 
Virtual_length 
P 
Wert aus DMU 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Längeneinheit [mm] 
Wertekomponente 
Value_component 
(P) 
Längenwert 
physikalische Länge 
Physical_length 
P 
Realer Wert 
(gerundet auf 5er-Stelle) 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Längeneinheit [mm] 
Wertekomponente 
Value_component 
(P) 
Längenwert 
Form 
Form 
O 
 
Endknoten 
End_node 
(P) 
ID des Endknoten des 
Segments 
Startknoten 
Start_node 
(P) 
ID des Anfangsknoten des 
Segments 
Centerkurve 
Center_curve 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Grad 
Degree 
(P) 
degree =1, Mittellinie 
Kontrollpunkte 
Control_points 
(P) 
Liste der Stützpunkte 
Querschnittsfläche 
Cross_section_area_inform
ation 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Wertefestlegung 
Value_determination 
(P) 
siehe Abschnitt 4.9.7 
Fläche 
Area 
(P) 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Flächeneinheit [mm²] 
Wertekomponente 
Value_component 
(P) 
Flächenwert 
zugeordnetes Objekt 
Fixing_assignment 
P 
sofern vorhanden 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
relativer Ort 
Location 
(P) 
Platzierung des Objekts 
(relativ) 
absoluter Ort 
Absolute_location 
P 
Platzierung des Objekts 
(absolut) 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Längeneinheit [mm] 


### 第 65 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 65 von 119 
 
Wertekomponente 
Value_component 
(P) 
Längenwert 
Orientierung (3x) 
Orientation 
(P) 
Ausrichtung 
Objekt 
Fixing 
(P) 
Verweis auf Befestigungs-
element oder Zusatzteil 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Prozessinformationen 
Processing_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(P) 
"HCA-SNR"  
Instruktionswert 
Instruction_value 
(P) 
HCA-SNR in der Segment 
definiert wurde  
Schutzfläche 
Protection_area 
P 
sofern vorhanden 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Startort 
Start_location 
(P) 
Start d. Leitungssatzschutzes 
(relativ) 
absoluter Startort 
Absolute_start_location 
P 
Start d. Leitungssatzschutzes 
(absolut) 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Längeneinheit [mm] 
Wertekomponente 
Value_component 
(P) 
Längenwert 
Ende 
End_location 
(P) 
Ende d. Leitungssatzschutzes 
(relativ) 
Absolutes Ende 
Absolute_end_location 
P 
Ende d. Leitungssatzschutzes 
(absolut) 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Längeneinheit [mm] 
Wertekomponente 
Value_component 
(P) 
Längenwert 
Bandagierungsrichtung 
Taping_direction 
- 
 
Gradient 
Gradient 
- 
 
 
Objekt-id 
id 
(-) 
automatisch generierte id 
Wertekomponente 
Unit_component 
(-) 
 
zugehöriges Schutzelement 
Associated_protection 
(P) 
Medium des verwendeten 


### 第 66 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 66 von 119 
 
Leitungsschutzes 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
 
 
 
Abbildung 20: Auszug <Segment>; Virtual/Physical Length 
 
5.20 Maßeinheit (Unit) 
Diese Klasse enthält die Festlegung aller im Harness verwendeten Maßeinheiten. Die Angaben sind 
eindeutig, und werden aus der Verwendung heraus referenziert. Siehe auch Kapitel 4.1. Im Folgenden 
ein Schema-Ausschnitt aus der KBL. 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitsname 
Unit_name 
P 
sofern nicht mit SI-Einheiten 
abbildbar 
SI Einheitsname 
Si_unit_name 
P 
sofern vorhanden 
SI Prefix 
Si_prefix 
P 
sofern vorhanden 


### 第 67 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 67 von 119 
 
SI Dimension 
Si_dimension 
P 
sofern vorhanden 
 
 
Abbildung 21: Auszug <Unit> (basierend KBL 2.4) 
5.21 Leitungsschutz (Wire_protection) 
Diese Klasse enthält alle Schläuche und Umwicklungen zum Kabelschutz. Im Folgenden ein Schema-
Ausschnitt aus der KBL. 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Teilenummer 
Part_number 
(P) 
<MBC Sachnummer> oder    
<Normteilsachnummer> 
Lieferantenname 
Company_name 
(P) 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
Lieferantenteilenummer 
Anwendungsbereich 
Scope 
O 
Lieferantenbezeichnung 


### 第 68 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 68 von 119 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Version 
Version 
(O) 
Teileversion bei 
Lieferantenteilen (ZGS) 
PosNr 
Abbreviation 
(P) 
<MBC Positionsnummer>, 
siehe Kapitel 4.8 
Beschreibung 
Description 
(P) 
Teilebenennung 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
englische Teilebenennung 
Vorgängerteilenummer 
Predecessor_part_number 
O 
 
Reifegrad 
Degree_of_maturity 
O 
 
Copyright-Information 
Copyright_note 
- 
 
Masseninformation 
Mass_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Masseneinheit  
[g/m bei Schläuchen, g/m² 
bei Bandagierung] 
Wertekomponente 
Value_component 
(P) 
Wert 
Externe Referenzen 
External_reference 
P 
Referenz auf die zugehörige 
Zeichnung (siehe Kapitel 
5.13) 
Änderung 
Change 
- 
 
Materialinformationen 
Material 
P 
nur für Spar- und 
Vollbandagierungen 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Materialschlüssel 
Material_key 
(P) 
A-SNR des Bandes 
Referenzsystem 
Material_reference_system 
O 
 
Prozessinformationen 
Processing_information 
P 
Verarbeitungshinweis bzw. 
Schlauchfamilie 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(P) 
"CAD_INDICATOR" 
Instruktionswert 
Instruction_value 
(P) 
CAD-Kennzeichen 


### 第 69 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 69 von 119 
 
Schutztyp 
Protection_type 
P 
"TUBE" bzw. "TAPE" 
typabhängige Parameter 
Type_dependent_parameter 
P 
Wert aus dem Feld 
"Schlauchfamilie" bzw. 
"Bandagierungstyp" 
 
 
Abbildung 22: Auszug <Wire_protection> (basierend KBL 2.4) 
 
6 Harness Container 
Die occurrence-Klassen werden vom KBL-Prozessor generiert und bestehen in der Regel aus Referenzen 
auf Verwendungen oder Bibliotheksteilen. Explizit beschrieben werden hier nur Referenzen auf 
Nutzdaten aus den Bibliotheken die von anderen Prozessen an dieser Stelle erwartet werden bzw. für 
Inhalte für die besondere Regeln erarbeitet wurden. 
In den folgenden Kapiteln werden diese Klassen näher beschrieben. Die Klasse Module wird in Kapitel 
6.15 näher beschrieben. 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
eigene Attribute 
attributes 
P 
siehe Abschnitt 6.1 
 
Schematic_connection 
- 
wird nicht verwendet 
 
Accessory_occurrence 
P 
siehe Abschnitt 6.2 
 
Assembly_part_occurrence 
- 
wird nicht verwendet 
 
Cavity_plug_occurrence 
P 
siehe Abschnitt 6.3 
 
Cavity_seal_occurrence 
P 
siehe Abschnitt 6.4 
 
Co_pack_occurrence 
- 
wird nicht verwendet 
 
Component_box_occurrence 
P 
siehe Abschnitt 6.6 
 
Conponent_occurrence 
P 
siehe Abschnitt 6.5 
 
Connection 
P 
siehe Abschnitt 6.7 
 
Connector_occurrence 
P 
siehe Abschnitt 6.8 
 
Fixing_occurrence 
P 
siehe Abschnitt 6.9 
 
General_wire_occurrence 
P 
siehe Abschnitt 6.10 
 
Special_terminal_occurrence 
- 
wird nicht verwendet 


### 第 70 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 70 von 119 
 
 
Terminal_occurrence 
P 
siehe Abschnitt 6.11 
 
Wire_protection_occurrence 
P 
siehe Abschnitt 6.15 
 
Wiring_group 
O 
siehe Abschnitt 6.12 
 
Harness_configuration 
P 
siehe Abschnitt 6.14 
 
Module 
P 
siehe Kapitel 6.15 
 
Module_configuration 
O 
siehe Abschnitt 6.16  
 
Module_families 
O 
siehe Abschnitt 6.17 
 
6.1 Attribute des Harness-Containers 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Teilenummer 
Part_number 
(P) 
<MBC Sachnummer> (bei 
Masterleitungssatz die 
Tabellensachnummer) 
Lieferantenname 
Company_name 
(P) 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
Lieferantenteilenummer 
Anwendungsbereich 
Scope 
O 
Lieferantenbezeichnung 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Version 
Version 
(P) 
Datenstand aus 
Zeichnungsschriftfeld 
(Format YYYY-MM-DD) 
PosNr 
Abbreviation 
- 
 
Beschreibung 
Description 
(P) 
<Benennung> aus 
Zeichnungsschriftfeld 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
englische Teilebenennung 


### 第 71 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 71 von 119 
 
Vorgängerteilenummer 
Predecessor_part_number 
P 
<MBC 
Vorgängersachnummer> 
Zeichnungsvorgänger; wenn 
kein Vorgänger: "-" 
Reifegrad 
Degree_of_maturity 
O 
 
Copyright-Information 
Copyright_note 
P 
siehe Abschnitt 0 
Masseninformation 
Mass_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Masseneinheit [kg] 
Wertekomponente 
Value_component 
(P) 
Wert 
Externe Referenzen 
External_references 
P 
Referenz auf externe 
Dokumente (siehe Kapitel 
5.13) 
Änderung 
Change 
P 
Change-ID groß ist ZGS 
Materialinformationen 
Material 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Materialschlüssel 
Material_key 
(O) 
 
Referenzsystem 
Material_reference_system 
O 
 
Prozessinformationen 
Processing_information 
P 
DS-/DZ-Merkmale 
(siehe Abschnitt 4.10.1) 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(P) 
(siehe Abschnitt 4.10.1) 
Instruktionswert 
Instruction_value 
(P) 
(siehe Abschnitt 4.10.1) 
Projektnummer 
Project_number 
P 
Name der Baureihe(n)  
[laut Dialog], Bsp. C222 
Bei Mehrfachnennungen ist 
ein „,“ als Trennzeichen zu 
verwenden 
Car Classification Level 2 
Car_classification_level_2 
(P) 
Ausführungsart als 
Kombination von BR-Derivat-
Bezeichnung 
(bspw. C177-FW, C247-FW) 
[bei Eindeutigkeit auch ohne 
BR] 
Bei Mehrfachnennungen ist 


### 第 72 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 72 von 119 
 
ein „,“ als Trennzeichen zu 
verwenden 
Car Classification Level 3 
Car_classification_level_3 
P 
Lenkervariante 
(LL/RL/lenkungsneutral) 
Car Classification Level 4 
Car_classification_level_4 
- 
 
Modelljahr 
Model_year 
(P) 
Modelljahr-Bezeichnung aus 
CONNECT 
Inhalt 
Content 
(P) 
siehe Abschnitt 4.9.6 
 
 
In Version wird die Versionsbezeichnung = Datenstand des Dokuments hinterlegt. Über die 
Sachnummer und den Wert von Version werden verschiedenartige Dokumente wie z.B. Zeichnung und 
KBL verknüpft.  
Als Versionskenner des Leitungssatzes ist in Version das Datum vom Datenstand nach DIN ISO 8601 
einzutragen. Im KBL gibt es das Attribut <Version> unter dem Element Harness und unter dem Element 
Module. 
 
Das Attribut <Version> der Klasse Harness ist bei Masterleitungssätzen der Datenstand der 
Tabellenzeichnung bzw. bei Einzelleitungssätzen der Datenstand des Einzelleitungssatzes; Beispiel: 
 
 
 
Das Attribut <Version> der Klasse Module ist der Datenstand des Leitungssatzmodules. Bei 
Einzelleitungssätzen ist der Wert gleich wie in der Klasse Harness.  
Beispiel: 
 
 
 
 


### 第 73 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 73 von 119 
 
6.2 Accessory_occurrence 
In dieser Klasse ist die Verwendung aller benutzten Zubehörteile im Harness beschrieben. Diese müssen 
sich dabei entweder auf ein Referenzteil beziehen (<Reference_element>) oder über ein 
<Fixing_assignment> am Segment lokalisiert sein. 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_ACC“ 
Id 
Id 
(P) 
 
Alias ID 
Alias_id 
O 
siehe Abschnitt 7.1 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
 
Anwendungsbereich 
Scope 
O 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Sprachencode 
Language_code 
(O) 
 
Wert 
Value 
O 
 
Platzierung 
Placement 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
U (3x) 
U 
(P) 
 
V (3x) 
V 
(P) 
 
Kartesische Koordinate 
Cartesian_point 
(P) 
 
Teil 
Part 
(P) 
Referenz auf das verwendete 
Bibliotheksteil 
Referenzelement 
Reference_element 
P 
sofern vorhanden 
Installationsinformationen 
Installation_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(P) 
"HCA-SNR" 
Instruktionswert 
Instruction_value 
(P) 
HCA-SNR in der Accessory-
Occurrence platziert wurde 


### 第 74 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 74 von 119 
 
 
6.3 Cavity_plug_occurrence 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_PLU“ 
Id 
Id 
P 
 
Teil 
Part 
(P) 
Referenz auf das verwendete 
Bibliotheksteil 
Installationsinformationen 
Installation_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
 
6.4 Cavity_seal_occurrence 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_SEA“ 
Id 
Id 
P 
 
Teil 
Part 
(P) 
Referenz auf das verwendete 
Bibliotheksteil 
Installationsinformationen 
Installation_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Austauschelement 
Replacing 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Ersetzter Blindstopfen 
Replaced 
(O) 
Verweis auf zu ersetzenden 
Blindstopfen 
 
6.5 Component_occurrence 


### 第 75 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 75 von 119 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_COM“ bzw. 
„ID_FUS“ 
Id 
Id 
(P) 
Sicherungsnummer (SI-
Nummer) 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
 
Anwendungsbereich 
Scope 
O 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Beschreibung 
Description 
P 
Funktion für 
Sicherungseinlege-blatt 
(Spalte SBT) 
Lokale Beschreibung 
Localized_description 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Sprachencode 
Language_code 
(O) 
 
Wert 
Value 
(O) 
 
befestigtes Element 
Mounting 
P 
darf nur auf 
Component_Slot_Occurrence 
verweisen 
Teil 
Part 
(P) 
Referenz auf das verwendete 
Bibliotheksteil 
Installationsinformationen 
Installation_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(P) 
"HCA-SNR" 
Instruktionswert 
Instruction_value 
(P) 
HCA-SNR in der Component-
Occurrence platziert wurde 
 
Die Kindklasse Fuse_occurrence implementiert dabei zusätzlich folgende Attribute: 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 


### 第 76 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 76 von 119 
 
Nennstrom 
Designed_operating_current 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(O) 
Stromeinheit [A] 
Wertekomponente 
Value_component 
(O) 
Stromwert 
Maximalstrom 
Maximum_operating_current 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(O) 
Stromeinheit [A] 
Wertekomponente 
Value_component 
(O) 
Stromwert 
angehängte Verbraucher 
AttachedConsumers 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Id 
Id 
(P) 
 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
 
Anwendungsbereich 
Scope 
O 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Beschreibung 
Description 
P 
Verbraucher laut SBT 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
Verbraucher laut 
Sicherungsbelegungstabelle 
(Englische Bezeichnung) 
 
6.6 Component_box_occurrence 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_CBO“ 
Id 
Id 
(P) 
REF der Device (z.B. F40) 
Alias ID 
Alias_id 
O 
siehe Abschnitt 7.1 


### 第 77 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 77 von 119 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
 
Anwendungsbereich 
Scope 
O 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Beschreibung 
Description 
P 
Bezeichnung der 
Verwendung (z.B. PDC-E, …) 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
Bezeichnung der 
Verwendung auf Englisch 
Platzierung 
Placement 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
U (3x) 
U 
(P) 
 
V (3x) 
V 
(P) 
 
Kartesische Koordinate 
Cartesian_point 
(P) 
 
Teil 
Part 
(P) 
Referenz auf das verwendete 
Bibliotheksteil 
Referenzelement 
Reference_element 
P 
sofern vorhanden 
Komponentenstecker 
Component_box_connectors 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_CON“ 
Bauteil 
Part 
(P) 
 
Kammern 
Slots 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Komponentenkammern 
Component_slots 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Id 
Id 
P 
REF der Verwendung  
(z.B. F40/ 1-S) 
Bauteil 
Part 
(P) 
 
Komponentenpins 
Component_cavities 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 


### 第 78 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 78 von 119 
 
Bauteil 
Part 
(P) 
 
Kontaktpunkt 
Contact_points 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Id 
Id 
(P) 
 
anliegende Teile 
Associated_parts 
P 
 
kontaktierter Pin 
Contacted_cavity 
(P) 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Installationsinformationen 
Installation_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(P) 
"HCA-SNR" 
Instruktionswert 
Instruction_value 
(P) 
HCA-SNR in der CompBox-
Occurrence platziert wurde 
 
6.7 Verbindung (Connection) 
Die Klasse Connection wird im KBL-Schema wie folgt dargestellt: 


### 第 79 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 79 von 119 
 
 
Instanzen der Klasse Connection sind damit wie folgt abzubilden: 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_CNN“ 
Id 
Id 
P 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Sprachencode 
Language_code 
(O) 
 
Wert 
Value 
(O) 
 
Signalname 
Signal_name 
P 
 
Signaltyp 
Signal_type 
O 
"BUS", "ENERGY", "GROUND" 
oder weitere nach 


### 第 80 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 80 von 119 
 
Abstimmung 
Nennspannung 
Nominal_voltage 
P 
nur für Masseleitungen 
siehe Abschnitt 0  
Externe Referenz 
External_references 
P 
Verweis auf Schaltplan-SNR 
inkl. Blatt-Nr aus der 
Verbindung kommt 
Realisierte schematische 
Verbindung 
Realized_schematic_connecti
on 
- 
 
Leitung 
Wire 
(P) 
Verweis auf zugeordnete 
Leitung 
Extremitäten 
Extremities 
(P) 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Position auf Leitung 
Position_on_wire 
(P) 
Nur 0 oder 1 erlaubt 
(Ausnahme: RoutingPoint für 
Rettungstrennstelle u. 
Schlaufe) 
Kontaktpunkt 
Contact_point 
(P) 
Referenz auf Kontaktpunkt 
an Connector_occurrence 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
 
Instruktionstyp 
Instruction_type 
(O) 
 
 
Instruktionswert 
Instruction_value 
(O) 
 
Installationsinformationen 
Installation_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
 
6.8 Connector_occurrence 
Die REF eines Bauteiles wird als <Id> abgelegt. Die Langbezeichnung einer Bauteilreferenz der 
Verwendungsbezeichnung wird im Attribut <Description> beschrieben. Die Bauteilreferenzen selbst 


### 第 81 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 81 von 119 
 
müssen in der CONNECT-Datenbank angelegt und der verwendeten Baureihe zugeordnet sein. 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_CON“ 
Id 
Id 
(P) 
Bauteilkurzbezeichnung (REF) 
aus VZK 
Alias ID 
Alias_id 
O 
siehe Abschnitt 7.1 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
 
Anwendungsbereich 
Scope 
O 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Beschreibung 
Description 
P 
Langbezeichnung aus VZK 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
Langbezeichnung aus VZK 
auf Englisch 
Gebrauch 
Usage 
P 
siehe Abschnitt 4.9.8 
Platzierung 
Placement 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
U (3x) 
U 
(P) 
 
V (3x) 
V 
(P) 
 
Kartesische Koordinate 
Cartesian_point 
(P) 
 
Teil 
Part 
(P) 
Referenz auf das verwendete 
Bibliotheksteil 
Referenzelement 
Reference_element 
O 
Nur im Sonderfall des KSL-
Connectors (siehe Abschnitt 
0) 
Kontaktpunkt 
Contact_points 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Id 
Id 
(P) 
 


### 第 82 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 82 von 119 
 
anliegende Teile 
Associated_parts 
P 
Verweis auf Terminals und 
Dichtungen 
(sofern vorhanden) 
kontaktierter Pin 
Contacted_cavity 
(P) 
Verweis auf zugehörigen Pin 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Installationsinformationen 
Installation_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(P) 
"HCA-SNR" 
Instruktionswert 
Instruction_value 
(P) 
HCA-SNR in der Connector-
Occurrence platziert wurde 
Kammern 
Slots 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
angeschlossene Kammern 
Mated_slots 
O 
 
Bauteil 
Part 
(P) 
 
Pins 
Cavities 
(P) 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
zugehörige Stopfen 
Associated_plug 
P 
(sofern vorhanden) 
angeschlossene Pins 
Mated_cavities 
O 
 
Bauteil 
Part 
(P) 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
 
6.9 Fixing_occurrence 
In dieser Klasse ist die Verwendung aller benutzten Befestigungsteile im Harness beschrieben.  


### 第 83 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 83 von 119 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_FIX“ 
Id 
Id 
(P) 
Referenz des Fixings  
(bspw. „FX.COC.0003“) 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O9 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
 
Anwendungsbereich 
Scope 
O 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Sprachencode 
Language_code 
(O) 
 
Wert 
Value 
(O) 
 
Platzierung 
Placement 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
U (3x) 
U 
(P) 
 
V (3x) 
V 
(P) 
 
Kartesische Koordinate 
Cartesian_point 
(P) 
 
Teil 
Part 
(P) 
Referenz auf das verwendete 
Bibliotheksteil 
Installationsinformationen 
Installation_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(P) 
"HCA-SNR" 
Instruktionswert 
Instruction_value 
(P) 
HCA-SNR in der Fixing-
Occurrence platziert wurde 
 
 
6.10 General_wire_occurrence 
 


### 第 84 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 84 von 119 
 
Alle Leitungsverwendungen müssen die Klasse General_wire_occurrence instanziieren: 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_WIR“ bzw. 
„ID_MUL“ 
Teil 
Part 
(P) 
Referenz auf das verwendete 
Bibliotheksteil 
Installationsinformationen 
Installation_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Längeninformation 
Length_information 
(P) 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Längentyp 
Length_type 
(P) 
siehe Abschnitt 4.9.9 
Längenwert 
Length_value 
(P) 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Längeneinheit [mm] 
Wertekomponente 
Value_component 
(P) 
Längenwert 
 
Für Instanzen der Kindklasse Wire_occurrence sind dabei zusätzlich die folgenden Attribute zu 
verwenden: 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Leitungsnummer 
Wire_number 
(P) 
eindeutige Wire-ID aus 
Schaltplan 
 
Für Instanzen der Kindklasse Special_wire_occurrence werden folgende Attribute zusätzlich 
implementiert: 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Sonderkabel-ID 
Special_wire_id 
(P) 
eindeutige Sonderkabel-ID 
aus Schaltplan 
Ader 
Core_occurrence 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_WIR“ 
Leitungsnummer 
Wire_number 
(P) 
eindeutige Wire-ID aus 


### 第 85 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 85 von 119 
 
Schaltplan 
Bauteil 
Part 
(P) 
 
Installationsinformationen 
Installation_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Längeninformation 
Length_information 
(P) 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Längentyp 
Length_type 
(P) 
siehe Abschnitt 4.9.9 
Längenwert 
Length_value 
(P) 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Längeneinheit [mm] 
Wertekomponente 
Value_component 
(P) 
Längenwert 
 
6.11 Terminal_occurrence 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_TER“ 
Id 
Id 
P 
 
Teil 
Part 
(P) 
Referenz auf das verwendete 
Bibliotheksteil 
Installationsinformationen 
Installation_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Austauschelement 
Replacing 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Ersetzter Blindstopfen 
Replaced 
(O) 
Verweis auf zu ersetzenden 
Blindstopfen 
 
6.12 Wire_protection_occurrence 
 


### 第 86 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 86 von 119 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_TAP“ 
Id 
Id 
(P) 
 
Alias ID 
Alias_id 
O 
siehe Abschnitt 7.1 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
 
Anwendungsbereich 
Scope 
O 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Sprachencode 
Language_code 
(O) 
 
Wert 
Value 
(O) 
 
Schutzlänge 
Protection_length 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Längeneinheit [mm] 
Wertekomponente 
Value_component 
(P) 
Längenwert 
Teil 
Part 
(P) 
Referenz auf das verwendete 
Bibliotheksteil 
Installationsinformationen 
Installation_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(P) 
"HCA-SNR" 
Instruktionswert 
Instruction_value 
(P) 
HCA-SNR in der WireProt.-
Occurrence platziert wurde 
 
6.13 Verkabelungsgruppe (Wiring_group) 
Ist nur vorgesehen für verdrillte Einzeladern (Präfix "ID_WRG") oder auch als „Bohrmaschinenleitungen“ 
bezeichnet. 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_WRG“ 


### 第 87 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 87 von 119 
 
Id 
Id 
(P) 
 
Typ 
Type 
P 
CAD-Nr. der 
Verdrillungsbeschreibung 
(z.B. "B48") 
Zugewiesene Leitungen 
Assigned_wire 
(P) 
Verweis auf 
Wire_occurrences 
Installationsinformationen 
Installation_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
Prozessinformationen 
Processing_information 
P 
Beschreibung der A-SNR der 
Verdrillungsbeschreibung 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(P) 
"Part_number" 
Instruktionswert 
Instruction_value 
(P) 
Sachnummer der 
Verdrillungsbeschreibung 
 
6.14 Harness_configuration 
Die Klasse enthält alle Attribute der Klasse Harness und zusätzlich eine Zusammenfassung aller 
angelegten Module sowie die Änderungshistorie der Zeichnung in der Klasse Change. 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Teilenummer 
Part_number 
(P) 
<MBC Sachnummer> (bei 
Masterleitungssatz die 
Tabellensachnummer) 
Lieferantenname 
Company_name 
(P) 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
Lieferantenteilenummer 
Anwendungsbereich 
Scope 
O 
Lieferantenbezeichnung 
Beschreibung 
Description 
O 
 


### 第 88 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 88 von 119 
 
Lokale Beschreibung 
Localized_description 
O 
 
Version 
Version 
(P) 
Datenstand aus 
Zeichnungsschriftfeld 
(Format YYYY-MM-DD) 
PosNr 
Abbreviation 
(-) 
 
Beschreibung 
Description 
(P) 
<Benennung> aus 
Zeichnungsschriftfeld 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
englische Teilebenennung 
Vorgängerteilenummer 
Predecessor_part_number 
P 
<MBC 
Vorgängersachnummer> 
Zeichnungsvorgänger; wenn 
kein Vorgänger: "-" 
Reifegrad 
Degree_of_maturity 
O 
 
Copyright-Information 
Copyright_note 
P 
siehe Abschnitt 0 
Masseninformation 
Mass_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Masseneinheit [kg] 
Wertekomponente 
Value_component 
(P) 
Wert 
Externe Referenzen 
External_references 
P 
Referenz auf externe 
Dokumente (siehe Kapitel 
5.13) 
Änderung 
Change 
P 
Change-ID groß ist ZGS 
Materialinformationen 
Material 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Materialschlüssel 
Material_key 
(O) 
 
Referenzsystem 
Material_reference_system 
(O) 
 
Prozessinformationen 
Processing_information 
P 
DS-/DZ-Merkmale  
(siehe Abschnitt 4.10.1) 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(P) 
(siehe Abschnitt 4.10.1) 
Instruktionswert 
Instruction_value 
(P) 
(siehe Abschnitt 4.10.1) 


### 第 89 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 89 von 119 
 
Projektnummer 
Project_number 
P 
Name der Baureihe(n)  
[laut Dialog], Bsp. C222 
Bei Mehrfachnennungen ist 
ein „,“ als Trennzeichen zu 
verwenden 
Car Classification Level 2 
Car_classification_level_2 
(P) 
Ausführungsart als 
Kombination von BR-Derivat-
Bezeichnung 
(bspw. C177-FW, C247-FW) 
[bei Eindeutigkeit auch ohne 
BR] 
Bei Mehrfachnennungen ist 
ein „,“ als Trennzeichen zu 
verwenden 
Car Classification Level 3 
Car_classification_level_3 
P 
Lenkervariante 
(LL/RL/lenkungsneutral) 
Car Classification Level 4 
Car_classification_level_4 
- 
 
Modelljahr 
Model_year 
(P) 
Modelljahr-Bezeichnung aus 
CONNECT 
Module 
Module 
(P) 
siehe Abschnitt 6.15 
 
 
 


### 第 90 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 90 von 119 
 
6.15 Module (Module) 
Hinweis: Die ersten 3 Ziffern der MBC-Sachnummer im Attribut <Part_number> der Klasse Module 
müssen einer in der CONNECT-Datenbank angelegten Baureihe entsprechen. 
 
Beispiel: MBC Sachnummer A 222 540 93 00 -> Baureihe C222 muss in CONNECT angelegt sein. 
Bei Masterleitungssätzen werden die darin enthaltenen Modulleitungssätze mit einer eindeutigen 
Modulvariante im Attribut <Abbreviation> gekennzeichnet. Wird nur ein Einzelmodul, innerhalb des 
Masterleitungssatzes freigeben, muss ebenfalls im Attribut <Abbreviation> der Variantenkenner 
hochgezogen werden. 
Die 
kennzeichnenden 
Attribute 
<Project_number>, 
<Car_classification_level_2> 
und 
<Car_classification_level_3> bezeichnen dabei nur die Kennzeichnungen der Erstfreigabe des Moduls – 
das Modul kann über eine spätere Verwendungsfreigabe auch in anderen Derivaten genutzt werden. 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_MOD“ 
Teilenummer 
Part_number 
(P) 
<MBC Sachnummer des 
Modulleitungssatzes> 
Lieferantenname 
Company_name 
(P) 
Lieferantenbezeichnung aus 
CONNECT Teilekatalog 
Alias ID 
Alias_id 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Alias-id 
Alias_id 
(O) 
Lieferantenteilenummer 
Anwendungsbereich 
Scope 
O 
Lieferantenbezeichnung 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
Version 
Version 
(P) 
Datenstand aus 
Zeichnungsschriftfeld 
(Format YYYY-MM-DD) 
PosNr 
Abbreviation 
(P) 
Modulvariante 
Beschreibung 
Description 
(P) 
Benennung 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 


### 第 91 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 91 von 119 
 
Sprachencode 
Language_code 
(P) 
„En“ 
Wert 
Value 
(P) 
englische Teilebenennung 
Vorgängerteilenummer 
Predecessor_part_number 
P 
<MBC 
Vorgängersachnummer> 
Zeichnungsvorgänger; wenn 
kein Vorgänger: "-" 
Reifegrad 
Degree_of_maturity 
O 
Einsatztermin 
Copyright-Information 
Copyright_note 
P 
siehe Abschnitt 0 
Masseninformation 
Mass_information 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Einheitskomponente 
Unit_component 
(P) 
Masseneinheit [kg] 
Wertekomponente 
Value_component 
(P) 
Wert 
Externe Referenzen 
External_references 
P 
Referenz auf externe 
Dokumente (siehe Kapitel 
5.13) 
Änderung 
Change 
P 
Change-ID groß ist ZGS 
Materialinformationen 
Material 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Materialschlüssel 
Material_key 
(O) 
 
Referenzsystem 
Material_reference_system 
O 
 
Prozessinformationen 
Processing_information 
P 
DS-/DZ-Merkmale und ESD-
Kenner (siehe Abschnitt 
4.10) 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(P) 
(siehe Abschnitt 4.10) 
Instruktionswert 
Instruction_value 
(P) 
(siehe Abschnitt 4.10) 
Projektnummer 
Project_number 
P 
Name der Baureihe(n)  
[laut Dialog], Bsp. C222 
Bei Mehrfachnennungen ist 
ein „,“ als Trennzeichen zu 
verwenden 
Car Classification Level 2 
Car_classification_level_2 
(P) 
Ausführungsart als 
Kombination von BR-Derivat-
Bezeichnung 
(bspw. C177-FW, C247-FW) 


### 第 92 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 92 von 119 
 
[bei Eindeutigkeit auch ohne 
BR] 
Bei Mehrfachnennungen ist 
ein „,“ als Trennzeichen zu 
verwenden 
Car Classification Level 3 
Car_classification_level_3 
P 
Lenkervariante 
(LL/RL/lenkungsneutral) 
Car Classification Level 4 
Car_classification_level_4 
O 
 
Modelljahr 
Model_year 
(P) 
Modelljahr-Bezeichnung aus 
CONNECT 
Inhalt 
Content 
(P) 
„module“ 
Modulfamilie 
Of_family 
P 
sofern Zuordnung möglich 
Modulkonfiguration 
Module_configuration 
(P) 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_MCNF“ 
Logistische 
Kontrollinformation 
Logistic_control_information 
(P) 
wenn Code bekannt, dann an 
dieser Stelle 
Konfigurationstyp 
Configuration_type 
P 
„option code“ 
kontrollierte Komponenten 
Controlled_components 
P 
Verweis auf zugehörige 
Objekte 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
 
6.16 Modulkonfiguration (Module_configuration) 
Die Modulkonfiguration ist zu nutzen, falls Codeanleitungen (beispielsweise eine Gruppierung von 
Leitungen) in der KBL transportiert werden sollen. Im Folgenden ein Schema-Ausschnitt aus der KBL. 


### 第 93 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 93 von 119 
 
 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(O) 
automatisch generierte id 
Präfix: „ID_MCNF“ 
Logistische Kontroll-
information 
Logistic_control_information 
(O) 
z.B. Leitungscode 
Konfigurationstyp 
Configuration_type 
O 
„option code“ 
kontrollierte Komponenten 
Controlled_components 
O 
Verweis auf zugehörige 
Objekte 
 
6.17 Modulfamilie (Module_family) 
In eienr Module_family können einander ausschließende Module zusammengefasst werden. 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Objekt-id 
id 
(P) 
automatisch generierte id 
Präfix: „ID_FAM“ 
Id 
Id 
(P) 
 
Beschreibung 
Description 
P 
Bezeichnung d. Modulfamilie 
Lokale Beschreibung 
Localized_description 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Sprachencode 
Language_code 
(P) 
„En“ 


### 第 94 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 94 von 119 
 
Wert 
Value 
(P) 
Bezeichnung der 
Modulfamilie (Englisch) 
Prozessinformationen 
Processing_information 
O 
 
 
Objekt-id 
id 
(O) 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
(O) 
 
Instruktionswert 
Instruction_value 
(O) 
 
 
 
 


### 第 95 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 95 von 119 
 
7 Detailerläuterungen 
Nicht alle im Leitungssatz auftretenden Fälle lassen sich über die einfache in den obigen Kapiteln 
gezeigte Abbildung darstellen. Dazu bündelt dieses Kapitel einige Sonderfälle und ihre definierte 
Abbildung in der KBL. 
 
7.1 Nachverfolgbarkeit der Topologie-Objekte 
Zur Gewährleistung eines sauberen Update-Mechanismus sowie zur Eineindeutigkeit der verwendeten 
Topologie-Objekte erhalten diese Objekte bei der Ausleitung als 3D-KBL aus Siemens NX einen GUID 
(Global Unique Identifier). Diese GUID wird den Objekten als <Alias_id> mitgegeben und muss über den 
Gesamtprozess erhalten bleiben. 
Die 
GUID 
wird 
dabei 
in 
den 
Klassen 
Accessory_occurrence, 
Connector_occurrence, 
Component_box_occurrence, Fixing_occurrence, Wire_protection_occurrence, Segment und Node 
jeweils als <Alias_id> wie folgt dokumentiert und transportiert: 
 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Alias Id 
Alias_id 
P 
 
 
Objekt-id 
id 
(P) 
automatisch generierte id 
Alias-id 
Alias_id 
(P) 
Wert der GUID 
Anwendungsbereich 
Scope 
P 
„GUID“ 
Beschreibung 
Description 
O 
 
Lokale Beschreibung 
Localized_description 
O 
 
 
 
Abbildung 23: Transport der GUID am Beispiel Connector_occurrence 
7.2 Wickelrückbindung 
Segmente mit Isolierungsschutz, die den Kabelsatz in einem Segment nicht vollbewickeln bzw. voll 
schützen, werden in der KBL auch als „Wickelrückbindung“ bezeichnet. Die Informationen zur 
Wickelrückbindung werden in der KBL in der Klasse Segment in der <Protection_area> dokumentiert 
(siehe Kapitel 5.19). Dabei werden der Start- und der Endpunkt (Anfang und Ende der Wicklung) in 


### 第 96 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 96 von 119 
 
Prozenten [%] zur Segmentlänge (Physical_length) angegeben. 
Hinweis: Die prozentuale Berechnung wird immer vom Startpunkt des Segments ausgehend ausgeführt; 
dargestellt an einem Beispiel: 
 
 
Abbildung 24: Wickelrückbindung 
Fall 1: Segmentlänge: 240 mm/ Schutzlänge: 210 mm (rote Zahlen) 
 
    Die Bewicklung startet direkt am Knoten und endet 30 mm vor dem Stecker (bei 87,5% der  
    Segmentlänge). 
Fall 2: Segmentlänge: 240 mm/ Schutzlänge: 210 mm (blaue Zahlen) 
 
   Da die Bewicklung/Isolierungsschutz vom Stecker ausgehend erst bei 30 mm beginnt, muss beim  
   Startpunkt ein größerer Wert als 0 % eingeben werden. 
 
 
 


### 第 97 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 97 von 119 
 
7.3 Darstellung der Kontaktierungsarten 
Bei den Kontaktierungsarten werden folgende Ausführungen unterschieden und dokumentiert: 
Standardkontakt, Doppelanschlag, Brückenkontakt und Komplexe Kontakte. 
 
7.3.1 Standardkontakt 
Beim Standardkontakt handelt es sich um einen Einzelkontakt einer Leitung (mit oder ohne einer 
Einzeladerabdichtung, der in die Kammer eines Kontaktgehäuses gesteckt wird. 
 
Abbildung 25: Kontaktierungsart Standardkontakt 
In der Abbildung 25 ist die grundsätzliche Dokumentation in der KBL beispielhaft dargestellt. Die 
Connection verweist dabei auf den an der Connector_occurrence definierten Contact_point. Dieser 
Contact_point trägt dabei den Kontakt (Terminal_occurrence) sowie ggf. die Einzeladerdichtung 
(Cavity_seal_occurrence) als <Associated_parts> und verweist mittels <Contacted_cavity> auf die 
kontaktierte Kammer des Kontaktgehäuses (Cavity_occurrence). 
 
 


### 第 98 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 98 von 119 
 
 
 


### 第 99 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 99 von 119 
 
7.3.2 Doppelanschlag 
Beim Doppelanschlag, auch Mehrfachkontakt genannt, handelt es sich um einen Einzelkontakt mit zwei 
zugewiesenen bzw. zusammengecrimpten Leitungen, die in dieselbe Kammer eines Kontaktgehäuses 
gesteckt werden. Dieses Konstrukt ist aus technischen Gründen nur ohne Einzeladerabdichtung 
möglich. 
 
 
Abbildung 26: Kontaktierungsart Doppelanschlag 
 
In der Abbildung 26 ist die grundsätzliche Dokumentation in der KBL beispielhaft dargestellt. 
In der KBL werden zeigen die Endpunkte der beiden Connections dabei auf denselben Contact_point 
am Kontaktgehäuse. Dieser Contact_point trägt dabei den Kontakt (Terminal_occurrence) als 
<Associated_parts> und verweist mittels <Contacted_cavity> auf die kontaktierte Kammer des 
Kontaktgehäuses (Cavity_occurrence). 
 


### 第 100 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 100 von 119 
 
7.3.3 Brückenkontakt 
Beim Brückenkontakt handelt es sich um mindestens einen Zweierkontakt, bei dem das Potential einer 
angeschlagenen Leitung über diesen (Brücken-)Kontakt in mehrere Kammern eines Kontaktgehäuses 
geleitet wird. 
 
 
 
Abbildung 27: Kontaktierungsart Brückenkontakt 
In der Abbildung 27 ist die grundsätzliche Dokumentation in der KBL beispielhaft dargestellt. 
In der KBL zeigt der Endpunkt der Connection dabei auf einen Contact_point am Kontaktgehäuse. Dieser 
Contact_point trägt dabei den Kontakt (Terminal_occurrence) als <Associated_parts> und verweist 
mittels <Contacted_cavity> auf die kontaktierten Kammern des Kontaktgehäuses (Cavity_occurrences). 
7.3.4 Komplexe Kontakte 
Bei komplexen Kontakten handelt es sich um mehrstufige Kontakte, bei denen mehrere AV Kammer-
Beziehungen am Kontaktgehäuse herrschen und somit an dem jeweiligen Leitungsende mehrere 
Anschlagteile verwaltet werden müssen. Zur weiteren Unterscheidung wird hier zwischen HSD-, 
FAKRA/KOAX- und Ethernet-Terminal unterschieden (siehe auch Abbildung 28). 


### 第 101 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 101 von 119 
 
 
Abbildung 28: Überblick KBL-Aufteilung Komplexe Kontakte 
Im Falle der HSD- und FAKRA-Terminals sind die Einzelkontakte der Leiter im Daimler-Umfeld derzeit 
als Zusammenbau innerhalb des Komplexen Kontaktes definiert. Daher müssen sie nicht einzeln 
verwaltet werden (siehe unten). 
******* HSD-Terminal 
Beim HSD-Terminal handelt es sich um einen Anschlag eines Sonderkabels, bestehend aus vier Leitern 
und einer Schirmung. Dabei wird dieses HSD-Terminal in eine Kammer des Kontaktgehäuses gesteckt. 
 
Abbildung 29: KBL-Abbildung HSD-Terminal 
In Abbildung 29 ist die grundsätzliche Dokumentation in der KBL grafisch dargestellt. 
In der KBL zeigen die Endpunkte der fünf Connections dabei auf fünf verschiedene Contact_points am 
Kontaktgehäuse. 
Die 
fünf 
Contact_points 
tragen 
dabei 
denselben 
Komplexen 
Kontakt 
(Terminal_occurrence) als <Associated_parts> und verweisen mittels <Contacted_cavity> auf die 
jeweilig kontaktierte Kammer des Kontaktgehäuses (Cavity_occurrence). 


### 第 102 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 102 von 119 
 
7.3.4.2 FAKRA-/KOAX-Terminal 
Beim Koax-Terminal handelt es sich um einen Anschlag eines Sonderkabels, bestehend aus einer 
Leitung mit einer Schirmung der Leitung. Dabei wird dieses Koax-Terminal in eine Kammer eines 
Kontaktgehäuses gesteckt. 
 
Abbildung 30: KBL-Abbildung FAKRA-/KOAX-Terminal 
 
 
Abbildung 31: Kontaktierungsart Koax-Terminal 
In Abbildung 30 und Abbildung 31 ist die grundsätzliche Dokumentation in der KBL beispielhaft 
dargestellt. 


### 第 103 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 103 von 119 
 
In der KBL zeigen die Endpunkte der beiden Connections dabei auf zwei verschiedene Contact_points 
am Kontaktgehäuse. Die beiden Contact_points tragen dabei denselben Komplexen Kontakt 
(Terminal_occurrence) als <Associated_parts> und verweisen mittels <Contacted_cavity> auf die 
jeweilig kontaktierte Kammer des Kontaktgehäuses (Cavity_occurrence). 
7.3.4.3 Ethernet-Terminal 
Beim Ethernet-Terminal handelt es sich um einen Anschlag eines Sonderkabels, bestehend aus zwei 
Leitern und einer Schirmung. Dabei wird dieses Ethernet-Terminal in eine Kammer des Kontaktgehäuses 
gesteckt. 
 
Abbildung 32: KBL-Abbildung Ethernet -Terminal 
In Abbildung 32 ist die grundsätzliche Dokumentation in der KBL grafisch dargestellt. 
In der KBL zeigen die Endpunkte der drei Connections dabei auf drei verschiedene Contact_points am 
Kontaktgehäuse. Die drei Contact_points tragen dabei denselben Komplexen Kontakt (Ethernet-
Kontakt) (Terminal_occurrence) als <Associated_parts>, die Contact_points der beiden Leiter zusätzlich 
den jeweiligen Stift-/Buchsenkontakt (Terminal_occurrence) und verweisen mittels <Contacted_cavity> 
auf die jeweilig kontaktierte Kammer des Kontaktgehäuses (Cavity_occurrence). 
 
7.3.5 Stecker mit mehreren Eintrittspunkten (HV-Leitungssatz) 
Beim HV-Leitungssatz gibt es Stecker mit aufgeschweißten Pins, da die HV-Leitungen nur 
Steckerkupplungen haben dürfen (Beispiel siehe Abbildung). Auf Grund der Montage der HV-Leitungen 
kann es dabei notwendig sein, dass zwei angeschlagene Leitungen eine andere Segmentlänge als eine 
dritte aufweisen müssen (vergleiche Abbildung 33).  


### 第 104 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 104 von 119 
 
 
Abbildung 33: HV-Adapterplatte mit mehreren Anschlusspunkten 
Damit in der KBL eine eindeutige Zuordnung von Leitungen, Segmenten und deren Länge möglich ist 
werden Knoten (Nodes) erzeugt, die neben der Referenced_component zur Connector_occurrence 
zusätzlich via Referenced_cavity auf die jeweilige Cavity_occurrence zeigen und somit dieselbe Stecker-
REF verwenden. 
Abbildung in KBL:  
 
Abbildung 34: Abbildung HV-Leitung in KBL 
 
 
 


### 第 105 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 105 von 119 
 
7.3.6 48V-Kontaktierung 
Besonders im Falle der 48V-Kontaktierung werden Leitungen über einen im Steckergehäuse 
befindlichen Kabelschuh kontaktiert. Die Zuordnung der verschiedenen Bauteile zu den KBL-Klassen ist 
dabei wie folgt: 
 
Abbildung 35: Abbildung der 48V-Kontaktierung 
7.4 Modellierung einseitig angeschlagener Leitungen 
Damit eine einseitig angeschlagene Leitung in der KBL dokumentiert werden kann (eine Connection 
muss immer mit Start- und Endpunkt versehen sein), muss ein virtueller Stecker erzeugt und bereits im 
Schaltplan referenziert sein.  
Für diesen virtuellen Stecker müssen folgende spezifische Eintragungen bei der Klasse 
Connector_housing und der Klasse Connector_occurrence durchgeführt werden: 
Connector_occurrence: 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Id 
Id 
P 
Bauteilkurzbezeichnung 
(REF) 
aus VZK 
Beschreibung 
Description 
P 
Langbezeichnung aus VZK 
Lokale Beschreibung 
Localized_description 
P 
Engl. Langbezeichnung aus VZK 
Gebrauch 
Usage 
P 
no end 
Beispiel: Eine geschirmte Leitung ist am Startpunkt mit Schirm und Leitung am Kontaktgehäuse 
angeschlagen; beim Endpunkt ist nur die Leitung angeschlagen (der Schirm ist nicht angeschlagen). 
 
Connector_housing: 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Teilenummer 
Part_number 
P 
LTG_END_GEDICHTET 
/ 


### 第 106 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 106 von 119 
 
LTG_END_UNGEDICHTET 
Beschreibung 
Description 
P 
Deutsche 
Beschreibung 
laut 
Schlüsselwort-Liste 
Lokale Beschreibung 
Localized_description 
P 
Englische 
Beschreibung 
laut 
Schlüsselwortliste 
Masseninformation 
Mass_information 
P 
0 g 
Steckertyp 
Housing_type 
P 
no end 
Anzahl Pins 
Number_of_cavities 
P 
1 (Pinbezeichnung = “x”) 
 
7.5 Modellierung von Routing-Point-Dummys 
Für den Sonderfall von Schlaufen oder Rettungstrennstellen wird im Schaltplan ein virtueller Stecker in 
Form eines sogenannten Routing Points gesetzt. Dieser virtuelle Stecker muss auch in der KBL 
entsprechend abgebildet werden.  
Für diesen virtuellen Stecker müssen folgende spezifische Eintragungen bei der Klasse 
Connector_housing und der Klasse Connector_occurrence durchgeführt werden: 
Connector_occurrence: 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Id 
Id 
P 
Bauteilkurzbezeichnung 
(REF) 
aus VZK 
Beschreibung 
Description 
P 
Langbezeichnung aus VZK 
Lokale Beschreibung 
Localized_description 
P 
Engl. Langbezeichnung aus VZK 
Gebrauch 
Usage 
P 
no end 
 
Connector_housing: 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Teilenummer 
Part_number 
P 
ROUTING_POINT_DUMMY 
Beschreibung 
Description 
P 
Deutsche 
Beschreibung 
laut 
Schlüsselwort-Liste 
Lokale Beschreibung 
Localized_description 
P 
Englische 
Beschreibung 
laut 
Schlüsselwortliste 
Masseninformation 
Mass_information 
P 
0 g 
Steckertyp 
Housing_type 
P 
no end 
Anzahl Pins 
Number_of_cavities 
P 
1 (Pinbezeichnung = “x”) 


### 第 107 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 107 von 119 
 
7.6 Darstellung von Textbändern 
Textbänder sind als Zusatzteil (Accessory) am Segment zu modellieren. Der zu beschreibende Text wird 
als <Id> in der Accessory_occurrence verwaltet. Alle Textbänder müssen sich auf dasselbe Part  mit der 
<Part_number> „TEXTBAND“ und dem <Accessory_type> „TEXTBAND“ beziehen (siehe Abbildung 36). 
 
Abbildung 36: Darstellung von Textbändern in der KBL 
 
7.7 von KSL-Steckern 
Für den Fall, dass  
 
an einem Steuergerät/Komponente elektrisch identische Kontaktgehäuse verwendet werden, 
die sich lediglich in ihrer Codierung auf Grund einer Varianz des Steuergerätes/Komponente 
unterscheiden,   
 
keine Duplizierung der Leitungen im Schaltplan erfolgen soll und  
 
diese einzelnen Kontaktgehäuse in separaten sogenannten Gehäusemodulen (Module, welche 
nur dieses Kontaktgehäuse als Teil besitzen) gesteuert werden, 
werden diese Kontaktgehäuse als KSL-Stecker abgebildet (Beispiel siehe Abbildung 37).  
 


### 第 108 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 108 von 119 
 
 
Abbildung 37: Beispiel eines KSL-Steckers 
Die Referenz der Stecker muss eindeutig sein. Daher wird ein Dummy-Stecker mit der korrekten 
Referenz des Schaltplans dargestellt. Dieser Dummy-Stecker trägt als Teilenummer (<Part_number>) 
das Schlüsselwort „DUMMY_KSL_CONNECTOR_<NUM_PINS>“. Die anderen Kontaktgehäuse werden 
als Connector_occurrence modelliert, sitzen jedoch nicht auf dem Topologieknoten (sind nicht an das 
Segment angeschlossen), sondern auf einem separaten Knoten (Node) abseits der Topologie, und 
tragen den Dummy-Stecker als <Reference_element> (siehe Abbildung 38: Darstellung des KSL-
Connector). Diese Connector_occurrences besitzen daher keine <Contact_points>. Ihre Referenz (<Id>) 
ist die Referenz des Referenzsteckers ergänzt um den Suffix „_GM<SEQUENCE_NUMBER>“. 
 
Abbildung 38: Darstellung des KSL-Connector 
Diese Abbildung sollte die Ausnahme sein und darf nur nach Rücksprache mit dem zuständigen BTV 
erfolgen. 
 
7.8 Transport von Fertigungshinweisen 
Alle Fertigungshinweise müssen auch digital in der KBL-Datei transportiert werden. Dazu müssen sie an 


### 第 109 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 109 von 119 
 
den jeweilig betroffenen Objekten (_occurrence-Klassen, Connection, Node, Segment) als 
<Installation_information> 
angefügt 
werden. 
Klassifiziert 
werden 
sie 
dabei 
durch 
den 
<Instruction_type> = „TextBasedInstruction“, der Hinweis selbst steht im Attribut <Instruction_value>. 
 
Installationsinformationen 
Installation_information 
P 
 
 
Objekt-id 
id 
P 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
P 
"TextBasedInstruction" 
Instruktionswert 
Instruction_value 
P 
Fertigungshinweis 
 
7.9 Kennzeichnung von Referenz-Punkten 
Alle Referenzpunkte müssen auch digital in der KBL-Datei transportiert werden. Dies ist nur an den 
Objekten Node sowie Fixing_assignment als <Processing_information> zulässig. Klassifiziert werden sie 
dabei durch den <Instruction_type> = „RefPointId“, die Id selbst steht im Attribut <Instruction_value>. 
 
Prozessinformationen 
Processing_information 
P 
 
 
Objekt-id 
id 
P 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
P 
"RefPointId" 
Instruktionswert 
Instruction_value 
P 
Id des Referenzpunktes 
 
7.10 Abbildung gedichteter Kabelschuhe 
Gedichtete Kabelschuhe können mit KBL 2.4-SR1 nur über einen Umweg dargestellt werden. Hierzu ist 
der Schlauch (Wire_protection_occurrence), der sich auf dem Segment befindet, als Zusatzteil 
(Accessory_occurrence) zusätzlich am Kabelschuh (Connector_occurrence) anzulegen.  
Die Accessory_occurrence wird dabei nicht modularisiert und verzeigert über <Reference_element> 
sowohl auf die Connector_occurrence als auch auf die Wire_protection_occurrence. 
Die <Protection_length> der Wire_protection_occurrence ist dabei die Summe der bedeckten 
Segmentlänge (Distanz zwischen <Start_location> und <End_location>) und der bedeckten 
Kabelschuhlänge (siehe Abbildung 39). 


### 第 110 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 110 von 119 
 
 
Abbildung 39: Abbildung gedichteter Kabelschuhe 
 
7.11 Abgangsrichtungen von Spezial-Kontaktgehäusen 
In speziellen Fällen kann es notwendig sein, die Abgangsrichtung eines Kontaktgehäuses in der 
Verbauung mitzutransportieren (Beispiel: variabler FAKRA-Stecker). Diese werden in der KBL am 
Kontaktgehäuse (Connector_occurrence) als <Installation_information> transportiert. Dabei wird die 
Abgangsrichtung („Links“, 
„Rechts“, 
„Oben“, 
„Unten“) als Wert zum <Instruction_type> 
CONNECTOR_DIRECTION geschrieben. 
 
Installationsinformationen 
Installation_information 
P 
 
 
Objekt-id 
id 
P 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
P 
"ConnectorDirection" 
Instruktionswert 
Instruction_value 
P 
Abgangsrichtung des 
Steckers 
 
7.12 Ausgangs- und Endpunkt für Blickrichtung 
Der Ausgangspunkt sowie sofern vorhanden der Endpunkt für die Blickrichtung zur Definition von Lage- 
und Steckrichtungen müssen digital in der KBL-Datei transportiert werden. Dies ist nur an den Objekten 
Node sowie Fixing_assignment als <Processing_information> zulässig. Klassifiziert wird er dabei durch 
den <Instruction_type> = „ViewingStartPoint“/“ViewingEndPoint“, der <Instruction_value> trägt 
lediglich den booleschen Ausdruck „true“. 


### 第 111 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 111 von 119 
 
Ausgangspunkt für Blickrichtung: 
Prozessinformationen 
Processing_information 
P 
 
 
Objekt-id 
id 
P 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
P 
"ViewingStartPoint" 
Instruktionswert 
Instruction_value 
P 
„true“ 
 
Endpunkt für Blickrichtung: 
Prozessinformationen 
Processing_information 
P 
 
 
Objekt-id 
id 
P 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
P 
"ViewingEndPoint" 
Instruktionswert 
Instruction_value 
P 
„true“ 
 
7.13 Lage- und Steckrichtung von Befestigungselementen („Uhrzeiten“) 
Die Lage- und Steckrichtung der Befestigungselemente (auch „Uhrzeiten“ genannt) am Segment muss 
für einen sauberen Datenaustausch-Prozess auch über die KBL transportiert werden. Diese werden in 
der KBL über spezielle <Processing_information> am Fixing_assignment transportiert. Dabei wird für die 
Lagerichtung als <Instruction_type> der Wert „FixingLocation“ genutzt, für die Steckrichtung der Wert 
„MoutingDirection“. 
 
Lagerichtung: 
Prozessinformationen 
Processing_information 
P 
 
 
Objekt-id 
id 
P 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
P 
"FixingLocation" 
Instruktionswert 
Instruction_value 
P 
Lagerichtung d. 
Befestigungselements 
 
Steckrichtung: 
Prozessinformationen 
Processing_information 
P 
 
 
Objekt-id 
id 
P 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
P 
"MountingDirection" 
Instruktionswert 
Instruction_value 
P 
Steckrichtung d. 
Befestigungselements 
 


### 第 112 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 112 von 119 
 
7.14 Ids von Kabelkanalausgängen, Ein-/Ausgängen T-Verteiler 
In speziellen Fällen kann es notwendig sein, die Ein- und Ausgänge von Kabelkanälen, T-Verteiler oder 
Verbindungsstücken eindeutig mit einer Id zu kennzeichnen. Diese Kennzeichnung wird in Form eine für 
das angeschlossene Teil eindeutigen „Id“ am Fixing_assignment des referenzierenden Segmentes 
transportiert, und zwar in Form einer <Processing_information>. 
 
Prozessinformationen 
Processing_information 
P 
 
 
Objekt-id 
id 
P 
automatisch generierte id 
Instruktionstyp 
Instruction_type 
P 
"Fixing_assignment_ID" 
Instruktionswert 
Instruction_value 
P 
Wert der eindeutigen Id 
 
7.15 Modellierung vorkonfektionierter Kontakte 
Für bestimmte Anwendungsfälle, in denen Kontakte bereits als Kaufteil im Kontaktgehäuse 
vorkonfektioniert sind und aus Prozessgründen (bspw. als Nachweis, dass Kontaktierung nicht 
vergessen wurde) dokumentiert werden müssen, kann ein virtueller Kontakt (<General_terminal>) mit 
dem Schlüsselwort „VORKON“ genutzt werden. 
 
Für diesen virtuellen Kontakt müssen folgende Eintragungen in der Klasse <General_terminal> erfolgen: 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Teilenummer 
Part_number 
P 
„VORKON“ 
Beschreibung 
Description 
P 
Deutsche 
Beschreibung 
laut 
Schlüsselwort-Liste 
Lokale Beschreibung 
Localized_description 
P 
Englische 
Beschreibung 
laut 
Schlüsselwortliste 
Masseninformation 
Mass_information 
P 
0 g 
Kontaktfamilie 
Terminal_type 
P 
“-“ 
Überzugmaterial 
Plating_material 
P 
“-“ 
 
7.16 Modellierung geschlossener Kammern 
Kontaktgehäuse mit zugespritzten Kontakten müssen vollständig modelliert werden, das bedeutet auch 
die zugespritzten Kammern müssen als Kammern am Kontaktgehäuse modelliert werden. Zur 
einheitlichen Modellierung wird ein virtueller Blindstopfen (<Cavity_plug>) mit dem Schlüsselwort 
„VERSCHLOSSEN“ verwendet. 


### 第 113 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 113 von 119 
 
Hierzu sind folgende Einträge für den virtuellen Blindstopfen in der Klasse <Cavity_plug> notwendig: 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Teilenummer 
Part_number 
P 
„VERSCHLOSSEN“ 
Beschreibung 
Description 
P 
Deutsche 
Beschreibung 
laut 
Schlüsselwort-Liste 
Lokale Beschreibung 
Localized_description 
P 
Englische 
Beschreibung 
laut 
Schlüsselwortliste 
Masseninformation 
Mass_information 
P 
0 g 
Farbe 
Colour 
P 
“-“ 
 
7.17 Umgang mit Nachstecklösungen 
In Sonderfällen (Behördenfahrzeuge, Sonderfahrzeuge) müssen Kontakte auch einzeln als 
Nachstecklösung geführt werden und müssen dazu als „Gehäuse“ eingesetzt werden. Zur einheitlichen 
Modellierung wird ein virtuelles Kontaktgehäuse (<Connetor_housing>) mit dem Schlüsselwort 
„NACHSTECK“ verwendet. 
Hierzu sind folgende Einträge für den virtuellen Blindstopfen in der Klasse <Connector_housing> 
notwendig: 
Bezeichnung 
KBL-Attribut 
P/O 
Bemerkung 
Teilenummer 
Part_number 
P 
„NACHSTECK“ 
Beschreibung 
Description 
P 
Deutsche 
Beschreibung 
laut 
Schlüsselwort-Liste 
Lokale Beschreibung 
Localized_description 
P 
Englische 
Beschreibung 
laut 
Schlüsselwortliste 
Masseninformation 
Mass_information 
P 
0 g 
Gehäusefarbe 
Housing_colour 
P 
“-“ 
Gehäusecodierung 
Housing_code 
P 
“-“ 
 


### 第 114 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 114 von 119 
 
8 Mitgeltende Unterlagen  
8.1 Normative Hinweise 
Die in dieser Ausführungsvorschrift abgebildeten Screenshots sowie normative Hinweise sind nur 
beispielhaft. Für die Aktualität ist der Zeichnungsersteller verantwortlich. 
 
 


### 第 115 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 115 von 119 
 
8.2 Normen und Vorschriften  
Alle in der folgenden Tabelle aufgeführten Dokumente sind über DocMaster verfügbar. 
Unterlage/Dokument Inhalte/ Benennung 
A0598030 
Dokumentation des ESD-Kenners in Smaragd 
A0598031 
Festlegung von Benennungen, Abkürzungen und Akronyme 
zu A- und H- Sachnummern für MBC und MB Vans 
DIN IEC 60757 
Code zur Farbkennzeichnung  
DIN ISO 16016 
Technische Produktdokumentation – Schutzvermerke zur Beschränkung der 
Nutzung von Dokumenten und Produkten (ISO 16016:2000) 
DIN ISO 8601 
Datenelemente und Austauschformate – Darstellung von Datum und Uhrzeit 
ISO 10303-212 
Industrielle 
Automatisierungssysteme 
und 
Integration 
– 
Produktdatendarstellung und –austausch – Teil 212: Anwendungsprotokoll: 
Elektrische/elektrotechnische Systeme und Anlagen 
MBN 10317 
Kennzeichnung von Merkmalen zur besonderen Nachweisführung 
Grundlagen - Dokumentationspflicht von Bauteilen / Baugruppen 
MBN 31020-3 
Änderungen in Konstruktionszeichnungen 
******** 
Steuerungskennzeichen in den Systemen der Produktdokumentation 


### 第 116 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 116 von 119 
 
8.3 Abkürzungen und Begriffe 
 
 
 
Abkürzung 
Benennung/ Bemerkung 
AV 
Ausführungsvorschrift 
ConnectPARTS 
Komponentendatenbank der Leitungssatzentwicklung 
ConnectHARNESS 
Leitungssatz- und Schaltplan-Recherche Datenbank 
DE 
Deutsch 
Dialog 
Stücklistensystem der Daimler AG  
DS 
Dokumentation Sicherheitsrelevanter Teile 
DMU 
Digital Mock-Up. Computersimulation eines Produktes für die Unterstützung von 
Entwicklung, Herstellung und Service 
DZ 
Dokumentation Zertifizierung 
DocMaster 
Dokumenten-Management 
EAD/ ELA 
Einzeladerabdichtung/ Einzelleitungsabdichtung für elektrische Leitungen 
EN 
Englisch 
ESD 
Empfindlichkeit eines Bauteils gegen elektrostatische Entladungen (electrostatic discharge) 
HCV 
Harness Container for Vehicles 
KBL 
Leitungssatzdatenmodell (Kabelbaumliste) nach ISO 10303-AP212 Spezifikation 
KEM 
Konstruktions-Einsatz-Meldung 
KSL 
Kundenspezifischer Leitungssatz 
kZ 
Abkürzung für den Hinweis, dass ein Teil (Sachnummer) keine eigene Zeichnung hat, 
sondern, auf einer Tabellen-Zeichnung dargestellt ist. 
MBC 
Mercedes Benz Car / Development , PKW – Entwicklung 
REF 
Referenzbezeichnung nach VA 059 EI08  (siehe auch VZK) 
Smaragd  
in MBC verwendetes PDM- System 
SRM 
Sachstamm Recherche Modul 
VZK 
Verwendungszweckkatalog  
XML 
extensible mark-up language 
ZGS 
Zeichnungs- und Geometrie-Stand 


### 第 117 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 117 von 119 
 
8.4 Abbildungsverzeichnis 
Abbildung 1: Strukturierung KBL bei Einzelleitungssätzen ............................................................................ 9 
Abbildung 2: Strukturierung KBL bei Masterleitungssätzen ......................................................................... 10 
Abbildung 3: Darstellung Längentypen ............................................................................................................ 23 
Abbildung 4: Definition eines DS-Kennzeichens in der Klasse <Harness> ................................................... 25 
Abbildung 5: Instanziierung eines DS-Kennzeichens in der Klasse <Module> ............................................. 26 
Abbildung 6: Beispiel eines positiven ESD-Kenners ........................................................................................ 26 
Abbildung 7: Auszug KBL-Container (basierend KBL 2.4 SR-1) .................................................................. 28 
Abbildung 8: Auszug <Accessory> (basierend KBL 2.4)................................................................................. 30 
Abbildung 9: Auszug <Cartesian_point> .......................................................................................................... 31 
Abbildung 10: Auszug <Change> (basierend KBL 2.4) .................................................................................. 36 
Abbildung 11: Auszug Komponenten/Component (basierend KBL 2.4) ....................................................... 40 
Abbildung 12: Auszug <Connector_housing> (basierend KBL 2.4) .............................................................. 45 
Abbildung 13: KBL-Abbildung von Standardtoleranzen ............................................................................... 47 
Abbildung 14. Auszug <External_reference> (basierend KBL 2.4) ............................................................... 50 
Abbildung 15: Auszug <Fixing> (basierend KBL 2.4) ..................................................................................... 52 
Abbildung 16: Auszug <General_terminal> ..................................................................................................... 55 
Abbildung 17: Auszug <General_wire> (basierend KBL 2.4) ........................................................................ 59 
Abbildung 18: Auszug <Node> (basierend KBL 2.4) ....................................................................................... 61 
Abbildung 19: Auszug <Routing> ..................................................................................................................... 62 
Abbildung 20: Auszug <Segment>; Virtual/Physical Length ......................................................................... 66 
Abbildung 21: Auszug <Unit> (basierend KBL 2.4) ........................................................................................ 67 
Abbildung 22: Auszug <Wire_protection> (basierend KBL 2.4).................................................................... 69 
Abbildung 23: Transport der GUID am Beispiel Connector_occurrence ....................................................... 95 
Abbildung 24: Wickelrückbindung ................................................................................................................... 96 
Abbildung 25: Kontaktierungsart Standardkontakt ....................................................................................... 97 
Abbildung 26: Kontaktierungsart Doppelanschlag ......................................................................................... 99 
Abbildung 27: Kontaktierungsart Brückenkontakt....................................................................................... 100 
Abbildung 28: Überblick KBL-Aufteilung Komplexe Kontakte .................................................................. 101 
Abbildung 29: KBL-Abbildung HSD-Terminal ............................................................................................. 101 
Abbildung 30: KBL-Abbildung FAKRA-/KOAX-Terminal ......................................................................... 102 
Abbildung 31: Kontaktierungsart Koax-Terminal ........................................................................................ 102 
Abbildung 32: KBL-Abbildung Ethernet -Terminal ..................................................................................... 103 
Abbildung 33: HV-Adapterplatte mit mehreren Anschlusspunkten ............................................................ 104 
Abbildung 34: Abbildung HV-Leitung in KBL .............................................................................................. 104 


### 第 118 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 118 von 119 
 
Abbildung 35: Abbildung der 48V-Kontaktierung ........................................................................................ 105 
Abbildung 36: Darstellung von Textbändern in der KBL ............................................................................. 107 
Abbildung 37: Beispiel eines KSL-Steckers .................................................................................................... 108 
Abbildung 38: Darstellung des KSL-Connector ............................................................................................. 108 
Abbildung 39: Abbildung gedichteter Kabelschuhe ...................................................................................... 110 


### 第 119 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Datenformat KBL 
 
A 006 006 46 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-17 
 
ZGS: 003 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 119 von 119 
 
 

