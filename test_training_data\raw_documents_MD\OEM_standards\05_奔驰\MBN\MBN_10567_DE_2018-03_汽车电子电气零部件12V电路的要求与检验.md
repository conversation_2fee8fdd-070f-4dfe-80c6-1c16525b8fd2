# MBN_10567_DE_2018-03_汽车电子电气零部件12V电路的要求与检验.pdf

## 文档信息
- 标题：
- 作者：<PERSON><PERSON><PERSON>, <PERSON> (019)
- 页数：45

## 文档内容
### 第 1 页
Mercedes-Benz 
MBN 10567 
Werknorm 
Ausgabe: 2018-03 
 
Übergangsfrist: 0 Monate 
 
Seiten insgesamt (inkl. Anhang): 45 
 
Fachbetreuer: <PERSON><PERSON><PERSON> 
 
E-Mail: <EMAIL> 
 
Werk: 059; Abt.: RD/UBB 
 
Tel.: +49 (0)176 309 202 55 
 
Copyright Daimler AG 
 
 
Elektrische und elektronische Komponenten im 
Kraftfahrzeug – 12 V Bordnetz – Anforderungen und 
Prüfungen 
 
Vorwort 
Diese Norm legt elektrische Anforderungen, Prüfbedingungen und Prüfungen an elektrische, elektronische 
und mechatronische Komponenten und Systeme für den Einsatz in Kraftfahrzeugen mit einem 12 V 
Bordnetz fest. 
 
 
Änderungen 
Erstausgabe 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: <PERSON>, 2018-11-23


### 第 2 页
MBN 10567:2018-03, Seite 2 
Copyright Daimler AG 
Inhaltsverzeichnis 
 
1 
Anwendungsbereich ...............................................................................................................3 
2 
Normative Verweisungen .......................................................................................................3 
3 
Begriffe und Definitionen ........................................................................................................4 
3.1 
Begriffe und Abkürzungen ......................................................................................................4 
3.2 
Spannungen und Ströme ........................................................................................................5 
3.3 
Temperaturen .........................................................................................................................5 
3.4 
Zeiten ......................................................................................................................................5 
3.5 
Innenwiderstand, Klemmenbezeichnung, Frequenz ..............................................................5 
4 
Allgemeine Anforderungen .....................................................................................................6 
4.1 
Allgemein ................................................................................................................................6 
4.2 
Spannungen und Ströme ........................................................................................................6 
4.3 
Temperaturangaben ...............................................................................................................6 
4.4 
Standardtoleranzen ................................................................................................................6 
4.5 
Standardwerte ........................................................................................................................7 
4.6 
Abtastraten und Messwertauflösungen ..................................................................................7 
4.7 
Prüfspannungen .....................................................................................................................7 
4.8 
Anzahl Prüflinge......................................................................................................................7 
4.9 
Funktionszustände .................................................................................................................8 
4.10 
Betriebsarten ..........................................................................................................................9 
4.11 
Schnittstellenbeschreibung und Schlüsselparameter.......................................................... 10 
4.12 
Beschränkung der Durchführung ......................................................................................... 10 
5 
Funktionsklassen und Betriebsspannungsbereiche ............................................................ 11 
6 
Prüfablauf und Parametertests ............................................................................................ 12 
6.1 
Prüfablauf ............................................................................................................................ 12 
6.2 
Parametertest (klein) ........................................................................................................... 13 
6.3 
Parametertest (groß) ........................................................................................................... 13 
6.4 
Physikalische Analyse ......................................................................................................... 14 
7 
Elektrische Anforderungen und Prüfungen ......................................................................... 15 
7.1 
Prüfung Betriebsspannungsbereich .................................................................................... 15 
7.2 
Prüfung Langzeit Überspannung ......................................................................................... 16 
7.3 
Prüfung Transiente Überspannung ..................................................................................... 17 
7.4 
Prüfung Transiente Unterspannung .................................................................................... 18 
7.5 
Prüfung Jumpstart ............................................................................................................... 20 
7.6 
Prüfung Load Dump ............................................................................................................ 21 
7.7 
Prüfung Überlagerte Wechselspannung ............................................................................. 22 
7.8 
Prüfung Langsames Absenken und Anheben der Versorgungsspannung ......................... 24 
7.9 
Prüfung Startimpulse ........................................................................................................... 26 
7.10 
Prüfung Resetverhalten ....................................................................................................... 30 
7.11 
Prüfung Kurze Unterbrechungen ......................................................................................... 32 
7.12 
Prüfung Unterbrechung Pin ................................................................................................. 34 
7.13 
Prüfung Unterbrechung Stecker .......................................................................................... 36 
7.14 
Prüfung Verpolung ............................................................................................................... 37 
7.15 
Prüfung Masseversatz ......................................................................................................... 40 
7.16 
Prüfung Ruhestrom ............................................................................................................. 41 
7.17 
Prüfung Rückspeisungen .................................................................................................... 42 
7.18 
Prüfung dual versorgter Komponenten ............................................................................... 44 
7.19 
Prüfung Ausgleichsströme mehrerer Versorgungsspannungen ......................................... 45 
 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 3 页
MBN 10567:2018-03, Seite 3 
Copyright Daimler AG 
1 
Anwendungsbereich 
Dieses Dokument legt Anforderungen, Prüfbedingungen und Prüfungen an elektrische, elektronische und 
mechatronische Komponenten und Systeme für den Einsatz in Kraftfahrzeugen mit einem 12 V Bordnetz 
fest. Die Prüfungen sind, soweit nicht anders vermerkt, keine elektrischen Lebensdauerprüfungen. 
Zusätzliche oder abweichende Anforderungen, Prüfbedingungen und Prüfungen werden in den 
entsprechenden Komponentenlastenheften definiert. 
Hinweis: Die dargestellten Prüfungen dienen der Überprüfung eines Teils der geforderten Eigenschaften 
der Komponente und dienen nicht der Bauelementequalifikation oder der Qualifizierung des 
Fertigungsprozesses. 
2 
Normative Verweisungen 
Die folgenden zitierten Dokumente sind für die Anwendung dieses Dokuments erforderlich. Bei datierten 
Verweisungen gilt nur die in Bezug genommene Ausgabe. Bei undatierten Verweisungen gilt die letzte 
Ausgabe des in Bezug genommenen Dokuments (einschließlich aller Änderungen). 
DIN 72552-2 
Klemmenbezeichnungen in Kraftfahrzeugen – Teil 2: Bedeutungen 
DIN EN 13018 
Zerstörungsfreie Prüfung - Sichtprüfung - Allgemeine Grundlagen 
DIN EN ISO/IEC 17025 
Allgemeine Anforderungen an die Kompetenz von Prüf- und 
Kalibrierlaboratorien 
ISO 26262 
Straßenfahrzeuge – Funktionale Sicherheit 
 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 4 页
MBN 10567:2018-03, Seite 4 
Copyright Daimler AG 
3 
Begriffe und Definitionen 
3.1 
Begriffe und Abkürzungen 
Tabelle 1: Abkürzungen Elektrische Anforderungen und Prüfungen 
Begriff / Abkürzung 
Bedeutung 
Applikationssoftware 
Applikationssoftware ist der Softwarestand in einer Komponente, der alle 
Funktionalitäten nach Lastenheft bzw. wie später im Fahrzeug enthält. Dies 
beinhaltet beispielsweise: 
• 
Aufstartverhalten 
• 
Einschlafverhalten 
• 
Regelungen 
• 
Überlast-, Kurzschluss- und Spielschutz 
• 
Diagnosen 
Bauteile / Bauelemente 
elektrisches, elektronisches oder mechatronisches Bauelement (z. B. 
Widerstand, Kondensator, Transistor, IC, Relais) 
Derating 
Absichtlich eingeschränkte Funktion z. B. Veränderung der 
Leistungsaufnahme in Abhängigkeit von Spannung und/oder Temperatur 
DUT 
Device Under Test – siehe Prüfling 
Funktionen 
beinhaltet systemspezifische Funktionen und Diagnosefunktionen 
höhere Verfügbarkeit 
Die Komponente benötigt für ihre Funktion eine Anforderung von 
mindestens ASIL A gemäß ISO 26262 an die elektrische 
Energieversorgung. 
Komponente 
komplettes Gerät, Steuergerät oder Mechatronik (mit Gehäuse) 
Lastenheft 
Wird in dieser Norm als Überbegriff für Zeichnung, Komponentenlastenheft 
oder Systemlastenheft verwendet. 
On-Grid Parken 
Betriebsmode eines Fahrzeugs mit alternativem Antrieb, das geparkt ist 
und dabei mit einer Ladestation / Steckdose verbunden ist, aber nicht 
geladen wird. In der Regel kann das Fahrzeug mit der Ladestation 
kommunizieren. 
Off-Grid Parken 
Betriebsmode eines Fahrzeugs mit alternativem Antrieb, das geparkt ist 
und dabei nicht mit einer Ladestation/Steckdose verbunden ist. 
Power-User 
realer Anwendungsfall mit maximaler vorstellbarer Nutzung 
Prüfling 
das zu prüfende System oder die zu prüfende Komponente 
PTB 
Physikalisch-Technische Bundesanstalt 
startrelevant 
Funktionen die direkt oder indirekt für einen 
Verbrennungsmotorstartvorgang benötigt werden 
System 
Funktionell verknüpfte Komponenten, z. B. Bremsregelsystem 
(Steuergerät, Hydraulik, Sensoren) 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 5 页
MBN 10567:2018-03, Seite 5 
Copyright Daimler AG 
3.2 
Spannungen und Ströme 
Tabelle 2: Abkürzung zu Spannungen und Strömen 
UN 
Nennspannung 
UBmin 
untere Betriebsspannungsgrenze 
UB 
Betriebsspannung 
UBmax 
obere Betriebsspannungsgrenze 
UPP 
Spitze-Spitze Spannung 
Utest 
Prüfspannung 
IN 
Nennstrom 
Itest 
Prüfstrom 
3.3 
Temperaturen 
Tabelle 3: Abkürzung zu Temperaturen 
Tmin 
minimale Umgebungstemperatur am Einbauort der Komponente 
TRT 
Raumtemperatur 
Tmax 
maximale Umgebungstemperatur am Einbauort der Komponente 
Ttest 
Prüftemperatur 
3.4 
Zeiten 
Tabelle 4: Abkürzung zu Zeiten 
tr 
Anstiegszeit / Risetime (z. B. eines Spannungsverlaufes) 
tf 
Abfallzeit / Falltime (z. B. eines Spannungsverlaufes) 
ttest 
Dauer der Prüfung 
3.5 
Innenwiderstand, Klemmenbezeichnung, Frequenz 
Tabelle 5: Abkürzung zu Widerständen, Klemmen und Frequenzen 
Ri 
Innenwiderstand der Quelle inklusive des Versorgungsleitungssatzes (siehe 
Bild 1: Innenwiderstand) 
Klemmenbezeichnungen 
nach DIN 72552-2 
f 
Frequenz 
 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23
额定电压
最小工作电压
工作电压
最大工作电压
峰峰电压
试验电压
额定电流
测试电流


### 第 6 页
MBN 10567:2018-03, Seite 6 
Copyright Daimler AG 
4 
Allgemeine Anforderungen 
4.1 
Allgemein 
Im Hinblick auf Sicherheitsanforderungen und Produktqualität sowie zur Erfüllung der Zertifizierungs-
anforderungen sind alle relevanten rechtlichen Vorschriften und Gesetze zu erfüllen. Zusätzlich gelten die 
relevanten Anforderungen des Daimler Konzerns. 
In Bezug auf Inhaltsstoffe und Wiederverwertbarkeit müssen Materialien, Verfahrens- und Prozesstechnik, 
Bauteile und Systeme alle geltenden gesetzlichen Bestimmungen erfüllen. 
4.2 
Spannungen und Ströme 
Die angegebenen Spannungsverläufe sind als Hüllkurve zu verstehen. Reale Spannungskurven sind mit 
beliebigem Verlauf innerhalb der vorgegebenen Prüf- und Referenzkurven zu erwarten. 
Alle Spannungs- und Stromangaben beziehen sich auf die Komponente (an deren Klemme). Dies gilt nicht 
für Prüfungen, bei denen der Innenwiderstand Ri spezifiziert ist. In diesem Fall beziehen sich die 
Spannungs- und Stromangaben auf die Quelle (siehe Bild 1: Innenwiderstand). 
 
Legende 
 
Bild 1: Innenwiderstand  
 
Sämtliche Flankenbeschreibungen beziehen sich auf die 10 % bzw. 90 %-Werte der Spannung. 
Eine ggf. notwendige Hysterese ist außerhalb der Betriebsspannungsgrenzen UBmin und UBmax gemäß 
Kapitel 5 zu legen. 
4.3 
Temperaturangaben 
Falls nicht anders angegeben, beziehen sich alle Temperaturangaben auf die Umgebungsluft des Prüflings. 
4.4 
Standardtoleranzen 
Falls nicht anders angegeben, gelten die Toleranzen nach Tabelle 6. 
Die Toleranzen beziehen sich auf den geforderten Messwert. 
Tabelle 6: Standardtoleranzen 
Frequenzen 
± 1 % 
Temperaturen 
± 2 °C 
Luftfeuchtigkeit 
± 5 % 
Zeiten 
0 % bis 5 % 
Spannungen 
± 2 % 
Ströme 
± 2 % 
US
Ri
RL
DUT
US 
Quelle 
RL 
Leitungs- und Kontaktierungswiderstand 
Ri 
Innenwiderstand betrachtet an den Klemmen der 
Komponente Richtung Quelle 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 7 页
MBN 10567:2018-03, Seite 7 
Copyright Daimler AG 
4.5 
Standardwerte 
Falls nicht anders angegeben, gelten die Standardwerte nach Tabelle 7. 
Tabelle 7: Standardwerte 
Raumtemperatur 
TRT = 23 °C ± 5 °C 
Luftfeuchtigkeit 
Frel = 25 % bis 75 % relative Feuchte 
Prüftemperatur 
Ttest = TRT 
Betriebsspannung (für Prüfung) 
UB = 14 V 
4.6 
Abtastraten und Messwertauflösungen 
Die Abtastrate bzw. Bandbreite des Messsystems ist der jeweiligen Prüfung anzupassen. Es müssen alle 
Messwerte mit allen Maximalwerten (Peaks) aufgezeichnet werden. 
Die Auflösung der Messwerte ist der jeweiligen Prüfung anzupassen. Es muss gewährleistet sein, dass 
auftretende Spannungsspitzen nicht zu einem Überlauf führen oder bei zu geringer Auflösung nicht messbar 
sind. Eine Datenreduktion/-abstraktion (z. B. Grenzwertüberwachung, Busbotschaftenauswertung) darf 
Auffälligkeiten nicht unterdrücken. 
4.7 
Prüfspannungen 
Prüfspannungen, insbesondere die für Über- und Unterspannungsprüfungen, können von den Betriebs-
spannungsbereichen (UBmin … UBmax) in Kapitel 5 signifikant abweichen und werden gesondert genannt. 
In dem für die Komponente/Funktion gültigen Betriebsspannungsbereich muss jederzeit der Funktions-
zustand A (siehe Kapitel 4.9) erfüllt sein. 
4.8 
Anzahl Prüflinge 
Soweit nicht anders vereinbart, müssen alle Prüfungen mit 3 Prüflingen durchgeführt werden. 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 8 页
MBN 10567:2018-03, Seite 8 
Copyright Daimler AG 
4.9 
Funktionszustände 
4.9.1 
Allgemein 
Dieser Abschnitt beschreibt den Funktionszustand des Prüflings vor, während und nach der Prüfung. Das 
funktionale Verhalten (inklusive Derating z. B. in Bezug auf Temperatur und Spannung) der Komponente in 
den Funktionszuständen sowie die Kundenwahrnehmung (z. B. optisch, akustisch, haptisch, thermisch) ist 
durch den Auftraggeber in der Zeichnung oder im Komponentenlastenheft zu definieren. 
In allen Fällen müssen nicht flüchtige Speicherfunktionen immer im Funktionszustand A bleiben. Die 
Integrität der nicht flüchtigen Speicher muss zu jeder Zeit sichergestellt werden. Die zeitlichen Abläufe der 
Funktionszustände sind im Komponentenlastenheft anzugeben. Erlaubte Fehlerspeichereinträge sind mit 
dem Auftraggeber abzustimmen und festzuschreiben. 
Bei den Funktionszuständen A bis D ist keine Schädigung des Prüflings zulässig. Undefinierte Funktionen 
sind zu keinem Zeitpunkt zulässig. Die in den Datenblättern spezifizierten zulässigen Grenzwerte (z. B. 
elektrisch, thermisch, mechanisch) der im Prüfling verbauten elektrischen/elektronischen Bauelemente 
dürfen nicht überschritten werden. Der Nachweis erfolgt mindestens durch den Parametertest (klein) gemäß 
Kapitel 6.2. 
4.9.2 
Funktionszustand A 
Der Prüfling erfüllt alle Funktionen wie im Lastenheft spezifiziert. 
4.9.3 
Funktionszustand B 
Wird nicht verwendet. 
4.9.4 
Funktionszustand C 
Der Prüfling erfüllt während der Beaufschlagung mit den Prüfparametern eine oder mehrere Funktionen 
nicht. Nach Ende der Beaufschlagung mit den Prüfparametern muss der Prüfling automatisch oder durch 
die im Lastenheft spezifizierten externen Trigger sofort wieder Funktionszustand A erreichen. Undefinierte 
Funktionen sind zu keinem Zeitpunkt zulässig. 
4.9.5 
Funktionszustand D 
Wird nicht verwendet. 
4.9.6 
Funktionszustand E 
Wird nicht verwendet. 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 9 页
MBN 10567:2018-03, Seite 9 
Copyright Daimler AG 
4.10 Betriebsarten 
4.10.1 Allgemein 
Die elektrischen, elektronischen und mechatronischen Komponenten und Systeme werden in verschieden-
en Betriebsarten betrieben, die entsprechend in den Prüfungen abzubilden sind. Details der Betriebsarten 
(z. B. Ansteuerung, Busaktivität, Busbotschaften, Originalsensoren, Originalaktoren), der Betriebsart-
übergänge (z. B. Power-Up, Power-Down) und der jeweils erforderlichen Randbedingungen sind zwischen 
Auftraggeber und Auftragnehmer abzustimmen und zu dokumentieren. Die jeweilige Betriebsart kann 
mehrere unterschiedliche Betriebslasten beinhalten, wenn sich z. B. Funktionen in einer Komponente 
ausschließen. D. h. die Prüfung muss ggf. mehrfach pro Betriebsart durchgeführt werden. Beispiele hierzu 
siehe Kapitel 4.10.4 Tabelle 8. 
4.10.2 Betriebsart I – Prüfling nicht elektrisch angeschlossen 
4.10.2.1 Betriebsart I.a 
Der Prüfling ist unbestromt, ohne Stecker und Leitungssatz. 
Ein vorhandener Kühlmittelkreislauf ist nicht befüllt und die Anschlüsse sind abgedichtet. 
4.10.2.2 Betriebsart I.b 
Der Prüfling ist unbestromt, jedoch mit angeschlossenen Steckern und Leitungssatz. 
Ein vorhandener Kühlmittelkreislauf ist befüllt und die Kühlmittelschläuche sind angeschlossen. 
4.10.3 Betriebsart II – Prüfling elektrisch angeschlossen 
4.10.3.1 Betriebsart II.a 
Der Prüfling ist ohne Betriebslast zu betreiben. 
Ein vorhandener Kühlmittelkreislauf ist zu befüllen, die Kühlmittelschläuche sind anzuschließen. Durchfluss 
und Temperatur des Kühlmediums sind bei Bedarf - wie im Komponentenlastenheft festgelegt - einzustellen 
4.10.3.2 Betriebsart II.b 
Der Prüfling ist mit minimaler Betriebslast zu betreiben. 
Der Prüfling muss dabei so betrieben werden, dass er eine minimale Eigenerwärmung erzeugt (z. B. durch 
Reduktion einer kontinuierlichen Ausgangsleistung oder durch seltene bzw. keine Ansteuerung externer 
Lasten). 
Ein vorhandener Kühlmittelkreislauf ist zu befüllen, die Kühlmittelschläuche sind anzuschließen. Durchfluss 
und Temperatur des Kühlmediums sind bei Bedarf - wie im Komponentenlastenheft festgelegt - einzustellen. 
4.10.3.3 Betriebsart II.c 
Der Prüfling ist mit maximaler Betriebslast zu betreiben (Power-User, aber kein Missbrauchsfall). 
Der Prüfling muss dabei so betrieben werden, dass er eine maximale Eigenerwärmung erzeugt (zum 
Beispiel durch realistische Maximierung einer kontinuierlichen Ausgangsleistung oder durch häufige 
Ansteuerung externer Lasten). 
Ein vorhandener Kühlmittelkreislauf ist zu befüllen, die Kühlmittelschläuche sind anzuschließen. Durchfluss 
und Temperatur des Kühlmediums sind bei Bedarf - wie im Komponentenlastenheft festgelegt - einzustellen. 
4.10.4 Beispiel Betriebsarten 
Tabelle 8: Beispiel Betriebsarten 
Beispielkomponente 
Betriebsart II.a 
Betriebsart II.b 
Betriebsart II.c 
Autoradio mit Navigation 
Komponente wie im 
geparkten Zustand 
(Sleep), Nachlauf 
beendet 
Komponente im 
fahrenden Fahrzeug. 
Komponente vom 
Fahrer abgeschaltet, 
BUS/µC’s aktiv, Kl. 15 
„EIN“ 
Komponente im 
fahrenden Fahrzeug. 
Komponente 
eingeschaltet (CD, 
Navigation, Endstufe, 
etc.) 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 10 页
MBN 10567:2018-03, Seite 10 
Copyright Daimler AG 
Diebstahlwarnanlage 
Keine Funktion im 
Fahrbetrieb 
Innenraum des geparkten Fahrzeuges wird 
überwacht 
Bremsregelsystem 
Komponente wie im 
geparkten Zustand. 
(Sleep), Nachlauf 
beendet 
Fahren ohne 
Bremsbetätigung 
Fahren mit häufigen 
Bremszyklen (kein 
Missbrauch wie z. B. 
ununterbrochener 
Bremsregelbetrieb) 
Fensterheber mit 
Steuergerät 
Komponente wie im 
geparkten Zustand. 
(Sleep), Nachlauf 
beendet 
Kommunikation und 
Diagnose aktiv, kein 
Fenterheberlauf 
angefordert 
1) Fensterheberlauf (n-
Mal) aktiv 
2) Blockiererkennung / 
Einklemmschutz 
On-Board-Lader 
Off-Grid Parken, 
Fahrbetrieb 
On-Grid Parken (nur 
Power Line 
Communication, kein 
Ladebetrieb), Fahrzeug-
konditionierung 
Ladebetrieb 
HV-Batterie (Batterie-
managementsystem) 
Off-Grid Parken 
On-Grid Parken mit 
Power Line 
Communication 
Fahrbetrieb, 
Ladebetrieb 
 
4.11 Schnittstellenbeschreibung und Schlüsselparameter 
Alle Schnittstellen müssen in ihren Zuständen und elektrischen Eigenschaften vollständig beschrieben 
werden. 
Im Komponentenlastenheft oder in Abstimmung mit dem Auftraggeber ist ein Satz sensitiver Parameter, 
sogenannte Schlüsselparameter, wie z. B. Ruhestromaufnahme, Betriebsströme, Ausgangsspannungen, 
Übergangswiderstände, Eingangsimpedanzen, Signalraten (Anstiegs- und Abfallzeiten) und Bus-
spezifikationen zu definieren. 
Diese Beschreibungen dienen als Grundlage für die Bewertung der Prüfergebnisse und müssen 
entsprechend detailliert sein. 
4.12 Beschränkung der Durchführung 
Das Prüflabor muss nach DIN EN ISO/IEC 17025 organisiert sein und betrieben werden. Alle zur Messung 
verwendeten Prüfmittel müssen nach DIN EN ISO/IEC 17025 kalibriert werden (bzw. wie durch den 
Hersteller festgelegt oder empfohlen) und auf PTB oder ein anderes gleichwertiges nationales Normlabor 
zurückführbar sein. Die verwendeten Prüfgeräte, Betriebsmittel, Aufstellungen und Prüfverfahren dürfen 
das Verhalten des Prüflings (beispielsweise Stromaufnahme) nicht begrenzen/verfälschen. 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 11 页
MBN 10567:2018-03, Seite 11 
Copyright Daimler AG 
5 
Funktionsklassen und Betriebsspannungsbereiche 
In einem Prüfling befinden sich zumeist mehrere Funktionen, die jeweils unterschiedlichen Funktions-
klassen zugeordnet sein können. Tabelle 9 gibt eine Übersicht über die möglichen Funktionsklassen und 
Betriebsspannungsbereiche. Die Funktionsklassen einer Komponente sind durch den Auftraggeber im 
Lastenheft festzuschreiben. 
Tabelle 9: Funktionsklassen und Betriebsspannungsbereiche 
Spannungsbereich [V] 
Prüfung 
Dauer 
Funktionsklasse 
1 
2 
3 
4 
5  6 
17 – 26 
7.5 Prüfung Jumpstart 
≤ 60 s 
C 
C 
C 
C 
C  A 
16 – 17 
7.2 Prüfung Langzeit Überspannung 
≤ 1 h 
A 
A 
A 
C 
C  A 
 
 
 
 
 
 
 
 
 
 
18 – 32 
7.6 Prüfung Load Dump 
≤ 300 ms 
A 
A 
A 
C 
C  A 
17 – 18 
7.3 Prüfung Transiente Überspannung ≤ 400 ms 
A 
A 
A 
A 
A  A 
16 – 17 
7.3 Prüfung Transiente Überspannung ≤ 600 ms 
A 
A 
A 
A 
A  A 
 
 
 
 
 
 
 
 
 
 
16 – 17 
 
statisch 
A 
A 
C 
C 
C  A 
9,8 – 16 
 
A 
A 
A 
A 
A  A 
9 – 9,8 
 
A 
A 
A 
A 
C  A 
8 – 9 
 
A 
A 
C 
C 
C  A 
6 – 8 
 
A 
C 
C 
C 
C  C 
 
 
 
 
 
 
 
 
 
 
≥ 9 
7.4 Prüfung Transiente Unterspannung 
500 ms 
A 
A 
A 
A 
A  A 
≥ 7 
7.9 Prüfung Startimpulse, Testfall 2 
s. Prüfung A 
A 
A 
A 
A  A 
≥ 6 
7.4 Prüfung Transiente Unterspannung s. Prüfung A 
A 
A 
C 
C  A 
≥ 4,5 
7.9 Prüfung Startimpulse, Testfall 1 
s. Prüfung A 
C 
C 
C 
C  C 
≥ 3,2 
7.9 Prüfung Startimpulse, Testfall 1 
s. Prüfung A 
C 
C 
C 
C  C 
≥ 0 
7.11 Prüfung Kurze Unterbrechungen 
≤ 100 µs 
A 
A 
A 
A 
A  A 
 
 
 
 
 
 
 
 
 
 
UBmin = 6  UBmax = 17 
7.1 Prüfung Betriebsspannungsbereich 
statisch 
A 
X 
 
 
 
 
 
 
UBmin = 8 
UBmax = 17 
7.1 Prüfung Betriebsspannungsbereich 
 
X 
 
 
 
 X 
UBmin = 9 
UBmax = 16 
7.1 Prüfung Betriebsspannungsbereich 
 
 
X 
X 
 
 
 
UBmin = 9,8 UBmax = 16 
7.1 Prüfung Betriebsspannungsbereich 
 
 
 
 
X  
 
 
Mindestanforderung für die Anwendung der Funktionsklassen: 
Funktionsklasse 1:  
Funktionen, die für die Sicherstellung und Aufrechterhaltung der 
Energieversorgung notwendig oder startrelevant sind 
Funktionsklasse 2:  
sicherheitsrelevante Funktionen 
Funktionsklasse 3:  
für den Fahrbetrieb notwendige Funktionen 
Funktionsklasse 4:  
Komfortfunktionen, die bei „Motor aus” oder „Bordnetzversorgung nur aus 
Speicher“ erhalten bleiben müssen 
Funktionsklasse 5:  
Komfortfunktionen, die bei Motorbetrieb „Motor an“ bzw. „Bordnetzversorgung 
aktiv“ vorhanden sein müssen 
Funktionsklasse 6:  
Diagnose und Kommunikation 
 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 12 页
MBN 10567:2018-03, Seite 12 
Copyright Daimler AG 
6 
Prüfablauf und Parametertests 
6.1 
Prüfablauf 
Eine elektrische Prüfung beginnt, wenn der Prüfling komplett aufgestartet ist und sich im Funktionszustand 
A befindet. 
Die elektrische Belastung muss, soweit nicht anders vorgegeben, mit Originallasten realisiert und betrieben 
werden. 
Die spezifikationsgemäße Funktion des Bauteils ist in allen relevanten Betriebsphasen des Gerätes zu 
prüfen. Folgende Betriebsphasen sind zu prüfen: 
• 
Startphase/Power-Up 
• 
Betrieb in verschiedenen Funktionszuständen 
• 
Ausschaltphase/Power-Down 
• 
Sleepmode 
Weitere Betriebsphasen sind mit dem Auftraggeber abzustimmen. 
Spätestens ab C-Muster ist der Prüfling während der Prüfung mit Applikationssoftware zu betreiben. 
Haben Softwareumfang, Applikationsparameter oder Prozessorauslastung Einfluss auf das Prüfungs-
ergebnis, dann sind bei deren Änderungen die betroffenen Prüfungen zu wiederholen. 
Die Softwareversion ist mit Funktionsumfang im Prüfbericht anzugeben. 
Die Öffnung von Prüfteilen, außer für die physikalische Analyse, ist vom Auftraggeber zu genehmigen. 
Die Reihenfolge der elektrischen Prüfungen ist frei wählbar. Zu jeder Prüfung sind die erlaubten Fehler-
speichereinträge sowie die Funktionszustände der Komponente festzuschreiben. 
Die Testfälle in einer Prüfung sind alle durchzuführen. Die Schärfegrade sind mit dem Auftraggeber 
abzustimmen. Die jeweilige Betriebsart (Kapitel 4.10) kann mehrere Betriebslasten beinhalten, wenn sich 
z. B. Funktionen in einer Komponente ausschließen. Eine Prüfung muss also ggf. mehrfach pro Betriebsart 
durchgeführt werden. 
Während jeder Prüfung müssen die zu überwachenden Schlüsselparameter gemäß Kapitel 4.11 
aufgezeichnet werden. Diese Parameter müssen vor dem Start und nach dem Ablauf jeder Prüfung auf ihre 
Übereinstimmung mit der Spezifikation überprüft werden. Resets der Komponente sind in geeigneter Form 
zu überwachen und zu dokumentieren. 
Vor einer elektrischen Prüfung mit definiertem Innenwiderstand ist der Prüfaufbau am Prüflingsstecker mit 
einer Referenzmessung zu verifizieren und zu dokumentieren. Die Referenzmessung ist, soweit nicht in der 
Prüfung anders gefordert, mit einer Ersatzlast von ≥ 120% des max. Laststroms der Betriebsart II.c 
durchzuführen. 
Vor und nach jeder Prüfung sind die Prüflinge einem Parametertest (klein) gemäß Kapitel 6.2 nach 
Lastenheftvorgabe zu unterziehen. Bei zeitlich und örtlich fortlaufenden Tests kann die Nachher- und 
Vorherprüfung zwischen zwei Prüfungen zu einem Parametertest (klein) zusammengefasst werden. 
Vor der ersten und nach der letzten elektrischen Prüfung ist der Parametertest (groß) gemäß Kapitel 6.3 
nach Lastenheftvorgabe durchzuführen. 
Die Messergebnisse und Daten der Vorher- / Nachherprüfungen dürfen sich nur innerhalb der spezifizierten 
zulässigen Toleranzen voneinander unterscheiden. Veränderungen der Messwerte größer der Mess-
genauigkeiten sind zu kennzeichnen. Die Messergebnisse müssen auf Trends und Driften untersucht 
werden um Auffälligkeiten, Alterung oder Fehlfunktionen der Komponente zu erkennen. 
Die Prüfung Physikalische Analyse gemäß 6.4 ist nach Abschluss aller elektrischen Prüfungen an 
mindestens einem Prüfling durchzuführen. 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 13 页
MBN 10567:2018-03, Seite 13 
Copyright Daimler AG 
6.2 
Parametertest (klein) 
6.2.1 Zweck 
Der Parametertest (klein) soll den Nachweis der fehlerfreien Funktion eines Prüflings bei Raumtemperatur 
und Betriebsspannung erbringen. 
6.2.2  Prüfung 
Es müssen die Schlüsselparameter gemäß Kapitel 4.11 gemessen und aufgezeichnet werden. Das 
funktionale Verhalten der Komponenten bei TRT und UB muss überprüft werden. 
Die Komponenten müssen im Rahmen einer Sichtprüfung nach DIN EN 13018 geprüft werden. Zur 
Identifikation von losen Teilen im Inneren des Gerätes ist eine Schüttelprüfung per Hand durchzuführen. 
Bei Komponenten mit Fehlerspeicher muss der Inhalt des Fehlerspeichers ausgelesen und dokumentiert 
werden. Anschließend ist der Fehlerspeichereintrag zu löschen. 
Es ist eine Dokumentation des Prüfablaufs und der Ergebnisse zu erstellen. 
6.2.3 Anforderung 
Funktionszustand A. 
Änderungen der Werte der Schlüsselparameter, des funktionalen Verhaltens oder der Fehlerspeicher-
einträge sowie Auffälligkeiten in der Sichtprüfung müssen bezüglich der vorangegangenen Prüfbelastungen 
gegenüber dem Neuzustand bewertet werden. 
Die Sichtprüfung ist auf äußere Beschädigungen / Veränderungen z. B. Risse, Auf-/Abplatzungen, Ver-
färbungen, Verformungen etc. zu bewerten. Optische Auffälligkeiten sind nicht zulässig. 
Lose Teile im Inneren des Gerätes sind nicht zulässig. 
6.3 
Parametertest (groß) 
6.3.1 Zweck 
Der Parametertest (groß) soll den Nachweis der fehlerfreien Funktion bei bestimmten Temperaturen und 
Spannungen erbringen. 
6.3.2 Prüfung 
Es müssen die Schlüsselparameter gemäß Kapitel 4.11 gemessen und das funktionale Verhalten der 
Komponenten bei den Temperaturen Tmax, TRT und Tmin jeweils bei den Spannungen UBmin, UB und UBmax 
gemessen werden. 
Interne und externe messbare Kenngrößen zur Bewertung der Genauigkeit und Funktion der Komponenten 
sind in diesem Test aufzuzeichnen.  
Es ist eine Referenz zu bilden, gegen die Veränderungen der Prüflinge durch Prüfbelastungen per Vergleich 
ermittelt werden können. 
Die Aufzeichnung ist im Prüfbericht zu dokumentieren und zu bewerten. Insbesondere betrifft dies folgende 
Daten: 
• 
Alle Funktionsgrößen 
• 
Farbort, Ausleuchtung und Kontrast von Leuchtmitteln und Anzeigen 
• 
Kennlinien (Sensoren, Wandler, Motoren) 
• 
Fehlerspeichereinträge 
• 
Reset- und Fehlerzählerstände 
• 
Kontrolle des EEPROM-Inhaltes 
• 
Zeitverlauf des aufgenommenen Stromes im Übergang von Betriebsart II.a nach II.c (Ziel: 
Ermittlung der Alterung elektrischer Bauelemente anhand Stromverlaufsänderungen) 
• 
Haptik 
• 
Akustik 
• 
Maßhaltigkeit (Verformungen), Spalte, Funktion von Clipsen 
• 
Betätigungskräfte / -momente 
• 
Bei dichten Prüflingen ist deren Dichtigkeit zu prüfen (z. B.: mittels Über- und Unterdruck, …). Das 
Verfahren zur Prüfung der Dichtigkeit ist mit dem Auftraggeber abzustimmen. 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 14 页
MBN 10567:2018-03, Seite 14 
Copyright Daimler AG 
Die Komponenten müssen im Rahmen einer Sichtprüfung nach DIN EN 13018 geprüft werden. Zur 
Identifikation von losen Teilen im Inneren des Gerätes ist eine Schüttelprüfung per Hand durchzuführen. 
Bei Komponenten mit Fehlerspeicher muss der Inhalt des Fehlerspeichers ausgelesen und dokumentiert 
werden. Anschließend ist der Fehlerspeichereintrag zu löschen. 
Es ist eine Dokumentation des Prüfablaufs und der Ergebnisse zu erstellen. 
6.3.3 Anforderung 
Funktionszustand A 
Die vorgegebenen Toleranzen in Form und Funktion müssen eingehalten werden. 
Änderungen der Werte der Schlüsselparameter, des funktionalen Verhaltens oder Auffälligkeiten in der 
Sichtprüfung müssen bezüglich der vorangegangenen Prüfbelastungen gegenüber dem Neuzustand 
bewertet werden. 
Die Sichtprüfung ist auf äußere Beschädigungen / Veränderungen z. B. Risse, Auf-/Abplatzungen, 
Verfärbungen, Verformungen etc. zu bewerten. Optische Auffälligkeiten sind nicht zulässig.  
Lose Teile im Inneren des Gerätes sind nicht zulässig. 
Fehlerspeichereinträge und Zählerveränderungen müssen genau diejenigen sein, die durch die Prüfung 
und die Funktionsanforderung hätten ausgelöst werden müssen. 
6.4 
Physikalische Analyse 
6.4.1 Zweck 
Die physikalische Analyse ist nach jeder Erprobungsphase (B-Muster, C-Muster, ...) durchzuführen, um 
Veränderungen der Komponente im Vergleich zum Neuzustand zu erkennen. 
6.4.2 Prüfung 
Bei der physikalischen Analyse muss der Prüfling geöffnet werden und es muss eine Sichtprüfung nach DIN 
EN 13018 durchgeführt werden. Veränderungen gegenüber dem Neuzustand sind mit möglichst 
zerstörungsfreien Methoden zu ermitteln und zu bewerten. Der Umfang sowie die Analysemethode sind mit 
dem Auftraggeber abzustimmen. Einige Beispiele hierfür sind in Tabelle 10 genannt. 
 
Tabelle 10: exemplarische Untersuchungsmethoden  
Prüfinhalte 
Erläuterungen 
Bauelemente- / 
Leiterplattenverfärbungen 
im Speziellen thermisch bedingte 
Abriebspuren 
 
Sprünge, Risse, Verformungen 
von Materialien 
(im Speziellen bei Verguss- und Dichtstoffen). Eine geeignete 
Prüfmethode (Röntgen, CT, Schliffe,…) ist hierbei in Abstimmung 
auszuwählen 
Trübung (insbesondere von Teilen optischer Sensorsysteme) 
Korrosions- und Migrationsspuren 
z. B. Silber- und Zinnmigration 
Beschädigung von 
Durchkontaktierungen von 
Leiterplatten 
z. B. Thermovias 
Steckerpinbeschädigungen 
z. B. durch Strom, Temperatur, Reiben, Oxydation 
sonstige Auffälligkeiten 
 
 
6.4.3 Anforderung 
Die Ergebnisse müssen im Prüfbericht dokumentiert und bewertet werden. 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 15 页
MBN 10567:2018-03, Seite 15 
Copyright Daimler AG 
7 
Elektrische Anforderungen und Prüfungen 
7.1 
Prüfung Betriebsspannungsbereich 
7.1.1 
Zweck 
Simuliert wird das Verhalten bei minimaler und maximaler dauerhafter Bordnetzspannung sowie bei 
Spannungsregelungen, z. B. bei Einsatz von intelligenten Generator- oder DC/DC-Wandler-Regelungen. 
Durch die Regelung können sich beliebige Spannungsverläufe im Bereich zwischen konstanter Spannung 
bis zu permanenten Spannungsschwankungen entsprechend der Tabelle 11 einstellen. 
7.1.2 
Prüfung 
Die Prüfung ist mit den in Tabelle 11 und Bild 2 dargestellten Parametern durchzuführen. 
Tabelle 11: Prüfparameter Prüfung Betriebsspannungsbereich 
Betriebsart des Prüflings 
II.c 
U1 
UBmin 
U2 
UBmax 
t1 
≥ 10 s 
tr 
(U2 - U1) / 10 V/s 
tf 
tr 
Anzahl der Zyklen 
10 
 
 
Bild 2: Prüfimpuls Prüfung Betriebsspannungsbereich 
7.1.3 
Anforderung 
Funktionszustand A 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 16 页
MBN 10567:2018-03, Seite 16 
Copyright Daimler AG 
7.2 
Prüfung Langzeit Überspannung 
7.2.1 
Zweck 
Es wird das Verhalten und die Beständigkeit der Komponente bei längerer Überspannung geprüft. 
Die Überspannung kann durch einen Fehler der erzeugenden Energiequelle verursacht werden und stellt 
einen Einfachfehler in der Energieversorgung nach. 
7.2.2 
Prüfung 
Die Prüfung ist mit den in Tabelle 12 und Bild 3 dargestellten Parametern durchzuführen. 
Tabelle 12: Prüfparameter Prüfung Langzeit Überspannung 
Betriebsart des Prüflings 
II.c 
U1 
13,5 V 
U2 
17 V 
tr 
< 10 ms 
tf 
< 10 ms 
t1 
60 min 
Ttest 
Tmax – 20 K 
Anzahl der Zyklen 
1 
 
 
Bild 3: Prüfimpuls Prüfung Langzeit Überspannung 
Wenn nach der Mindestprüfdauer am Prüfling noch kein thermischer Beharrungszustand (< 1 K in 10 min) 
erreicht wurde, ist die Prüfung bis zum thermischen Beharrungszustand zu verlängern. 
7.2.3 
Anforderung 
Funktionszustand gemäß Kapitel 5 Funktionsklassen und Betriebsspannungsbereiche. 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 17 页
MBN 10567:2018-03, Seite 17 
Copyright Daimler AG 
7.3 
Prüfung Transiente Überspannung 
7.3.1 
Zweck 
Aufgrund des Abschaltens von Verbrauchern und bei kurzen Gasstößen (Tip-In) kann es zu transienten 
Überspannungen im Bordnetz kommen. Diese Überspannungen werden mit dieser Prüfung simuliert. 
7.3.1 
Prüfung  
Die Prüfung ist mit den in Tabelle 13 und Bild 4 dargestellten Parametern durchzuführen. 
Tabelle 13: Prüfparameter Prüfung Transiente Überspannung 
Betriebsart des Prüflings 
II.c 
U1 
15,5 V 
U2 
18 V 
U3 
17 V 
tr 
1 ms 
tf 
1 ms 
t1 
400 ms 
t2 
600 ms 
t3 
8 s 
Anzahl Zyklen 
100 
 
 
Bild 4: Prüfimpuls Prüfung Transiente Überspannung 
7.3.2 
Anforderung 
Funktionszustand gemäß Kapitel 5 Funktionsklassen und Betriebsspannungsbereiche. 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 18 页
MBN 10567:2018-03, Seite 18 
Copyright Daimler AG 
 
7.4 
Prüfung Transiente Unterspannung 
7.4.1 
Zweck 
Aufgrund des Einschaltens von Verbrauchern kann es abhängig vom Zustand des Bordnetzes (z. B. Verfüg-
barkeit von Energiespeichern) zu transienten Unterspannungen kommen. 
7.4.2 
Prüfung 
Die Prüfung ist mit den in Tabelle 14 und Bild 5 dargestellten Parametern in beiden Testfällen durch-
zuführen. 
Tabelle 14: Prüfparameter Prüfung Transiente Unterspannung 
Parameter 
Schärfegrad 1 
Schärfegrad 2 
Betriebsart des Prüflings 
II.c 
II.c 
U1 
10,8 V 
10,8 V 
U2 
9 V 
6 V 
U3 
10,8 V 
8 V 
U4 
10,8 V 
9 V 
t1 
1,8 ms 
5 ms 
t2  
500 ms 
100 ms 
t3 
1,8 ms 
2 ms 
t4 
0 ms 
100 ms 
t5 
0 ms 
1 ms 
t6 
0 ms 
300 ms 
t7 
0 ms 
2 ms 
t8 
1 s 
1 s 
Anzahl Zyklen 
10 pro Testfall 
10 pro Testfall 
Testfall 1 
Ttest 
Tmax 
Tmax 
Testfall 2 
Ttest 
Tmin 
Tmin 
 
Schärfegrad 1 und 2 sind immer anzuwenden. 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 19 页
MBN 10567:2018-03, Seite 19 
Copyright Daimler AG 
 
Bild 5: Prüfimpuls Prüfung Transiente Unterspannung 
 
7.4.3 
Anforderung 
Funktionszustand gemäß Kapitel 5 Funktionsklassen und Betriebsspannungsbereiche. 
Innerhalb der Betriebsspannung der Funktionsklasse ist Funktionszustand A nachzuweisen. 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 20 页
MBN 10567:2018-03, Seite 20 
Copyright Daimler AG 
 
7.5 
Prüfung Jumpstart 
7.5.1 
Zweck 
Diese Prüfung simuliert die Fremdversorgung eines Fahrzeuges. Die maximale Prüfspannung ergibt sich 
aus Nutzkraftfahrzeugsystemen und ihren erhöhten Bordnetzspannungen. 
7.5.2 
Prüfung 
Die Prüfung ist mit den in Tabelle 15 und Bild 6 dargestellten Parametern durchzuführen. 
Tabelle 15: Prüfparameter Prüfung Jumpstart 
Betriebsart des Prüflings 
II.c 
U0 
0 V 
U1 
3 V 
U2 
10,8 V 
U3 
26 V 
t1 
1 s 
t2 
0,5 s 
t3 
5 s 
t4 
≥ 10 s 
t5 
60 s 
tr 
≤ 2 ms 
tf 
≤ 100 ms 
Anzahl der Zyklen 
1 
 
 
Bild 6: Prüfimpuls Prüfung Jumpstart 
7.5.3 
Anforderung 
Funktionszustand gemäß Kapitel 5 Funktionsklassen und Betriebsspannungsbereiche. 
Innerhalb der Betriebsspannung der Funktionsklasse ist Funktionszustand A nachzuweisen. 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 21 页
MBN 10567:2018-03, Seite 21 
Copyright Daimler AG 
7.6 
Prüfung Load Dump 
7.6.1 
Zweck 
Der Abwurf einer elektrischen Last in Verbindung mit einer Batterie mit reduzierter Pufferfähigkeit führt 
aufgrund der Generatoreigenschaften zu einem energiereichen Überspannungsimpuls. Dieser Impuls soll 
mit dieser Prüfung simuliert werden. 
7.6.2 
Prüfung 
Die Prüfung ist mit den in Tabelle 16 und Bild 7 dargestellten Parametern durchzuführen. 
Tabelle 16: Prüfparameter Prüfung Load Dump 
Betriebsart des Prüflings 
II.c 
U1 
13,5 V 
U2 
32 V 
Ri 
≤ 30 mΩ 
t1 
300 ms 
t2 
1 min 
tr 
≤ 2 ms 
tf  
≤ 30 ms 
Anzahl der Zyklen 
10 
 
 
Bild 7: Prüfimpuls Prüfung Load Dump 
7.6.3 
Anforderung 
Funktionszustand gemäß Kapitel 5 Funktionsklassen und Betriebsspannungsbereiche. 
Innerhalb der Betriebsspannung der Funktionsklasse ist Funktionszustand A nachzuweisen. 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 22 页
MBN 10567:2018-03, Seite 22 
Copyright Daimler AG 
7.7 
Prüfung Überlagerte Wechselspannung 
7.7.1 
Zweck 
Dem Bordnetz können Wechselspannungen überlagert sein. Die überlagerte Wechselspannung kann 
während des gesamten Generator- bzw. DC/DC-Wandler-Betriebs anliegen. Diese Situation wird mit diesen 
Prüfungen simuliert. 
7.7.2 
Prüfung 
Die Prüfung ist mit den in Tabelle 17 und Bild 8 dargestellten Parametern durchzuführen. 
Tabelle 17: Prüfparameter Prüfung Überlagerte Wechselspannung 
Betriebsart des Prüflings 
II.c 
U1 
UBmax 
Ri 
≤100 mΩ 
Wobbelart 
Dreieck logarithmisch 
Anzahl der Zyklen 
15 Zyklen je Testfall 
Testfall 1  
UPP 
3 V 
für Komponenten mit Versorgung aus Generator 
Frequenzbereich 
15 Hz – 30 kHz 
Wobbelperiode t1 
2 min 
Testfall 2  
UPP 
6 V  
für Komponenten mit Versorgung aus Generator bei Fahrten ohne 
Batterie (Notlauf) 
Frequenzbereich 
15 Hz – 30 kHz 
Wobbelperiode t1 
2 min 
Testfall 3  
UPP 
2 V 
für Komponenten mit Versorgung ausschließlich aus DC/DC-Wandler 
Frequenzbereich 
15 Hz – 30 kHz 
Wobbelperiode t1 
2 min 
Testfall 4 
UPP 
1 V 
für Komponenten mit Versorgung aus DC/DC-Wandler 
Frequenzbereich 
30 kHz – 200 kHz 
Wobbelperiode t1 
10 min 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 23 页
MBN 10567:2018-03, Seite 23 
Copyright Daimler AG 
 
Bild 8: Prüfimpuls Prüfung Überlagerte Wechselspannung 
 
7.7.2.1 Prüfaufbau 
Der Prüfaufbau ist detailliert zu dokumentieren inkl. Leitungsinduktivitäten, Leitungskapazitäten und 
Leitungswiderständen. 
7.7.3 
Anforderung 
Testfall 1: Funktionszustand A 
Testfall 2: 
a) Für den Fahrbetrieb notwendige Komponenten: 
Funktionszustand A 
b) Für alle anderen Komponenten: 
Funktionszustand C 
Testfall 3: Funktionszustand A 
Testfall 4: Funktionszustand A 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 24 页
MBN 10567:2018-03, Seite 24 
Copyright Daimler AG 
7.8 
Prüfung Langsames Absenken und Anheben der Versorgungsspannung 
7.8.1 
Zweck 
Simuliert wird das langsame Absenken und Anheben der Versorgungsspannung, wie es beim langsamen 
Entlade- und Ladevorgängen der Fahrzeugbatterie auftritt. 
7.8.2 
Prüfung 
Die Prüfung ist mit den in Tabelle 18 dargestellten Parametern in beiden Testfällen durchzuführen. 
Tabelle 18: Prüfparameter Prüfung Langsames Absenken und Anheben der Versorgungsspannung 
Betriebsart des Prüflings 
II.a und II.c 
U1 
10,8 V 
U2 
0 V 
∆U 
0,5 V/min 
Anzahl der Zyklen 
1 Zyklus je Betriebsart und Testfall 
Testfall 1: Wie im Bild 9 dargestellt. 
Testfall 2: Mit überlagerter Wechselspannung, wie im Bild 10 dargestellt. 
Upp 
3 V 
Frequenz 
Sinus 50 Hz 
 
 
Bild 9: Prüfimpuls Prüfung Langsames Absenken und Anheben der Versorgungsspannung 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 25 页
MBN 10567:2018-03, Seite 25 
Copyright Daimler AG 
 
Bild 10: Prüfimpuls Prüfung Langsames Absenken und Anheben der Versorgungsspannung 
7.8.3 
Anforderung 
Die Bewertung des Prüfungsergebnisses ist abhängig vom Spannungsbereich, mit dem die Komponente 
während der Prüfung beaufschlagt wird. 
Unterschieden wird zwischen: 
a) Innerhalb der Betriebsspannung der Funktionsklasse: 
Funktionszustand A 
b) Außerhalb der Betriebsspannung der Funktionsklasse: 
Funktionszustand C 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 26 页
MBN 10567:2018-03, Seite 26 
Copyright Daimler AG 
7.9 
Prüfung Startimpulse 
7.9.1 
Zweck 
Beim Starten (Anlassen des Motors) fällt die Batteriespannung für einen kurzen Zeitraum auf einen 
niedrigen Wert und steigt dann in mehreren Stufen wieder an. Die meisten Komponenten werden 
unmittelbar vor dem Starten kurz aktiviert, während des Anlassens deaktiviert und anschließend nach dem 
Anlassen bei laufendem Motor wieder aktiviert. Mit dieser Prüfung wird das Verhalten der Komponente bei 
startbedingten Spannungseinbrüchen untersucht. 
Der Startvorgang kann unter unterschiedlichen Fahrzeugstartsituationen erfolgen: Kaltstart und Warmstart 
(automatischer Wiederstart bei Start-Stopp). Um beide Fälle abzudecken, werden zwei unterschiedliche 
Testfälle definiert. 
7.9.2 
Prüfung 
Die Prüfung ist mit den in Tabelle 19 dargestellten Parametern in beiden Testfällen durchzuführen. 
Tabelle 19: Prüfparameter Prüfung Startimpulse 
Betriebsart des Prüflings 
II.b und II.c 
Gegebenenfalls sind in der jeweiligen Betriebsart weitere 
Betriebslasten festzulegen, um die Besonderheiten des Starts 
abzubilden. 
Testfall 1: Kaltstart 
Prüfimpuls „normal“ und „scharf“ nach Tabelle 20, Bild 11 
Testfall 2: Warmstart 
Prüfimpuls nach Tabelle 21, Bild 12 
 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 27 页
MBN 10567:2018-03, Seite 27 
Copyright Daimler AG 
7.9.2.1 Testfall 1: Kaltstart 
Tabelle 20: Prüfparameter Testfall 1: Kaltstart 
Parameter 
Prüfimpuls „normal“ 
Prüfimpuls „scharf“ 
U1 
11,0 V 
11,0 V 
U2 
4,5 V 
3,2 V +0,2 V 
U3 
4,5 V 
5,0 V 
U4 
6,5 V 
6,0 V 
U5 
2 V 
2 V 
t1 
2 s 
2 s 
t2 
≤ 1 ms 
≤ 1 ms 
t3 
19 ms 
19 ms 
t4 
0 ms 
≤ 1 ms 
t5 
0 ms 
329 ms 
t6 
50 ms 
50 ms 
t7 
10 s 
10 s 
t8 
100 ms 
100 ms 
f 
2 Hz 
2 Hz 
Anzahl der Zyklen 
10 pro Betriebsart 
10 pro Betriebsart 
 
 
Legende 
a 
Kl. 50 aus 
b 
Kl. 50 an 
 
Bild 11: Prüfimpuls Testfall 1: Kaltstart 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 28 页
MBN 10567:2018-03, Seite 28 
Copyright Daimler AG 
7.9.2.2 Testfall 2: Warmstart 
Tabelle 21: Prüfparameter Testfall 2: Warmstart 
U1 
11,0 V 
U2 
7,0 V 
U3 
8,0 V 
U4 
9,0 V 
t1 
5 s 
t2 
≤ 1 ms 
t3 
15 ms 
t4 
70 ms 
t5 
240 ms 
t6 
70 ms 
t7 
600 ms 
t8 
≤ 1 ms 
Anzahl der Zyklen 
100 pro Betriebsart 
 
 
Legende 
a 
Kl. 50 aus 
b 
Kl. 50 an 
Bild 12: Prüfimpuls Testfall 2: Warmstart 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 29 页
MBN 10567:2018-03, Seite 29 
Copyright Daimler AG 
7.9.3 
Anforderung 
Es darf zu keinem Fehlerspeichereintrag kommen. 
Das Fahrzeug muss in jedem Fall gestartet werden können. 
Funktionszustand gemäß Kapitel 5 Funktionsklassen und Betriebsspannungsbereiche. 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 30 页
MBN 10567:2018-03, Seite 30 
Copyright Daimler AG 
7.10 Prüfung Resetverhalten 
7.10.1 Zweck 
Es wird das Resetverhalten einer Komponente in ihrer Umgebung nachgebildet und geprüft. 
Testrandbedingungen (z. B. Verbund, Klemme, System) sind detailliert zu beschreiben. 
Eine beliebige zeitliche Ablauffolge von wiederholtem Ein/Aus-Schalten kommt im Betrieb vor. 
Das Resetverhalten spiegelt sich in einer Spannungsvarianz und in einer zeitlichen Varianz wider. Um 
unterschiedliche Ausschaltzeiten zu simulieren, werden zwei unterschiedliche Prüfabläufe definiert. 
7.10.2 Prüfung 
Die Prüfung ist mit den in Tabelle 22 und Bild 13 dargestellten Parametern in beiden Testfällen durch-
zuführen. 
Tabelle 22: Prüfparameter Prüfung Resetverhalten 
Betriebsart des Prüflings 
II.a, II.b und II.c 
U1 
UBmin 
U2 
6 V 
∆U1 (Bereich U1 bis U2) 
0,5 V 
∆U2 (Bereich U2 bis 0 V) 
0,2 V 
t2 
≥ 10 s, bis der Prüfling 100 % Betriebsfähigkeit erreicht hat 
tr 
≤ 10 ms 
tf 
≤ 10 ms 
Anzahl der Zyklen 
1 Zyklus je Betriebsart und Testfall 
Testfall 1 
t1 
5 s 
Testfall 2 
t1 
100 ms 
 
Bild 13: Prüfimpuls Prüfung Resetverhalten 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 31 页
MBN 10567:2018-03, Seite 31 
Copyright Daimler AG 
7.10.3 Anforderung 
Funktionszustand A beim Wiedererreichen von U1. 
In keinem Fall darf es zu undefinierten Verhalten der Komponente kommen. 
Es ist der Nachweis der Einhaltung des spezifizierten Schwellwertes zu erbringen und festzuhalten, ab 
welchem Spannungspegel die Komponente den Funktionszustand A erstmalig verlässt. 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 32 页
MBN 10567:2018-03, Seite 32 
Copyright Daimler AG 
7.11 Prüfung Kurze Unterbrechungen 
7.11.1 Zweck 
Es wird das Verhalten der Komponente bei kurzen Unterbrechungen von unterschiedlicher Dauer simuliert. 
Solche Unterbrechungen können durch Ereignisse wie z. B. Kontakt- und Leitungsfehler oder prellende 
Relais auftreten. 
7.11.2 Prüfung 
Tabelle 23: Prüfparameter Prüfung Kurze Unterbrechungen 
Betriebsart des Prüflings 
II.c 
Utest 
11 V 
tr 
≤ (0,1 t1) 
tf  
≤ (0,1 t1) 
Der Schalter S1 ist mit folgenden 
Sequenzen zu schalten: 
t1 
Schritte 
10 µs bis 100 µs 
10 µs 
100 µs bis 1 ms 
100 µs 
1 ms bis 10 ms 
1 ms 
10 ms bis 100 ms 
10 ms 
100 ms bis 2 s 
100 ms 
t2 
> 10 s 
Das Halten der Prüfspannung Utest muss mindestens so lange 
dauern, bis der Prüfling und die Peripherie wieder eine 100 %-
Betriebsfähigkeit erreicht haben. 
Anzahl der Zyklen 
1 
 
Die Zeitdauer des Spannungseinbruchs durch den in Bild 15 dargestellten Prüfaufbau erhöht sich in den in 
Tabelle 23 genannten Schritten. Dabei ergibt sich ein wie in Bild 14 gezeigtes Schema. 
Die Spannung am Prüfling kann durch den Prüfaufbau auf die maximale Spannung der Prüfung Load Dump 
(siehe Kapitel 7.6) begrenzt werden. 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 33 页
MBN 10567:2018-03, Seite 33 
Copyright Daimler AG 
 
Bild 14: Zustandswechsel Schalter S1 Prüfung Kurze Unterbrechungen 
 
******** Prüfaufbau 
 
 
Bild 15: Prinzipschaltung Prüfung Kurze Unterbrechungen 
 
******** Prüfablauf 
Je eine Referenzmessung mit 100 Ω (± 5 %) und 1 Ω (± 5 %) als Prüflingsersatz ist durchzuführen und zu 
dokumentieren. Der Nachweis der Flankensteilheit ist mit diesem Prüfaufbau zu erbringen. Als Widerstände 
sind induktivitätsarme Bauteile zu verwenden. 
Anschließend sind die Prüfungen gemäß Tabelle 23 durchzuführen. 
7.11.3 Anforderung 
Für t1 < 100 µs: Funktionszustand A 
Für t1 ≥ 100 µs: Funktionszustand C 
Es ist festzuhalten, ab welchem Zeitwert t1 der Prüfling den Funktionszustand A erstmalig verlässt. 
 
 
Z2
Z1
S1
t
tf
t1
t1
t2
tr
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 34 页
MBN 10567:2018-03, Seite 34 
Copyright Daimler AG 
7.12 Prüfung Unterbrechung Pin 
7.12.1 Zweck 
Simuliert wird die Leitungsunterbrechung von einzelnen Pins. Da diese Unterbrechung in ihrer zeitlichen 
Ausprägung vielfältig ausfallen kann (von Wackelkontakt bis zur dauerhaften Unterbrechung) sind 
verschiedene Impulsformen definiert. 
7.12.2 Prüfung 
Die Prüfung ist mit den in Tabelle 24 dargestellten Parametern in beiden Testfällen durchzuführen. 
Tabelle 24: Prüfparameter Prüfung Unterbrechung Pin 
Betriebsart des Prüflings 
II.a und II.c 
Z1 
Zustand 1: Pin verbunden 
Z2 
Zustand 2: Pin unterbrochen 
tr 
≤ (0,1 t1) 
tf 
≤ (0,1 t1) 
Anzahl der Zyklen 
3 Zyklen je Betriebsart, Testfall und Pin 
Testfall 1  
 
Jeder Pin ist für t = 10s abzuziehen und wieder anzulegen 
(langsamer Intervall) 
Testfall 2 
 
Impulspaket gemäß Bild 16 auf jeden Pin zur Simulation eines 
„Wackelkontakts“ 
Anzahl der Impulse t2 im 
Impulspaket 
4 000 
a 
Impulspaket 
t1 
0,1 ms 
t2 
1 ms 
t3 
10 s 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 35 页
MBN 10567:2018-03, Seite 35 
Copyright Daimler AG 
 
Bild 16: Prüfimpuls Prüfung Unterbrechung Pin, Testfall 2 
 
7.12.2.1 Prüfablauf 
Die Komponente ist an die Spannungsversorgung angeschlossen. 
Die Prüfung ist nicht auf die Versorgungspins (z. B. Kl. 15, Kl. 30, Kl. 87, ...) anzuwenden es sei denn, einer 
dieser Pins wird als Weckleitung verwendet. 
Die Prüfung ist auch auf Massepins (Kl. 31) anzuwenden. 
Die Spannung am Pin kann auf die maximale Spannung der Prüfung Load Dump (siehe Kapitel 7.6) 
begrenzt werden. 
Je eine Referenzmessung mit 1 kΩ (±5 %) und 1 Ω (±5 %) als Prüflingsersatz ist durchzuführen und zu 
dokumentieren. Der Nachweis der Flankensteilheit ist mit diesem Prüfaufbau zu erbringen. Als Widerstände 
sind induktivitätsarme Bauteile zu verwenden. 
Anschließend sind die Prüfungen gemäß Tabelle 24 durchzuführen. 
7.12.3 Anforderung 
Für alle Testfälle Funktionszustand C. 
 
 
tr
t3
t2
t1
tf
Z2
Z1
Pin
t
a
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 36 页
MBN 10567:2018-03, Seite 36 
Copyright Daimler AG 
7.13 Prüfung Unterbrechung Stecker 
7.13.1 Zweck 
Simuliert wird die Leitungsunterbrechung von Steckern. 
7.13.2 Prüfung 
Die Prüfung ist mit den in Tabelle 25 dargestellten Parametern durchzuführen. 
Tabelle 25: Prüfparameter Prüfung Unterbrechung Stecker 
Betriebsart des Prüflings 
II.a und II.c 
ttest 
10 s 
Anzahl der Zyklen 
1 Zyklus je Betriebsart und Stecker 
7.13.2.1 Prüfablauf  
Jeder Stecker ist für ttest vom Prüfling abzuziehen und wieder aufzustecken. Hat der Prüfling mehrere 
Stecker, ist jeder Stecker einzeln zu prüfen. Die Reihenfolge ist zu variieren. Bei mehreren Steckern ist 
auch deren Kombinatorik abzuprüfen. 
7.13.3 Anforderung 
Funktionszustand C 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 37 页
MBN 10567:2018-03, Seite 37 
Copyright Daimler AG 
7.14 Prüfung Verpolung 
7.14.1 Zweck 
Es wird die Beständigkeit des Prüflings gegen den verpolten Anschluss einer Batterie bei Fremdstarthilfe 
geprüft. Die Verpolung kann mehrfach auftreten und darf nicht zu einer Schädigung der Komponente führen. 
Die Verpolsicherheit muss für beliebige Spannungen bis zur minimalen Prüfspannung gewährleistet sein. 
Die Fahrzeugsicherung ist nicht Teil des Verpolschutzkonzeptes. 
7.14.2 Prüfung 
Es müssen alle relevanten Anschlüsse bei originaler Beschaltung geprüft werden. 
Der Prüfling ist entsprechend der Verschaltung im Fahrzeug anzusprechen.  
Die Stromaufnahme während der Prüfung ist zu protokollieren. 
Die Prüfung ist mit beiden Testfällen gemäß Tabelle 26 durchzuführen. 
Tabelle 26: Prüfparameter Prüfung Verpolung 
Testfall 1: Verpolung statisch 
Prüfimpuls nach Tabelle 27, Bild 17 
Testfall 2: Verpolung dynamisch 
Prüfimpuls nach Tabelle 28, Bild 18 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 38 页
MBN 10567:2018-03, Seite 38 
Copyright Daimler AG 
7.14.2.1 Testfall 1: Verpolung statisch 
Dieser Testfall überprüft die Robustheit der Komponente bei verschiedenen Verpolspannungen, die sich 
abhängig vom Fahrzeugzustand einstellen können. 
Tabelle 27: Prüfparameter Prüfung Verpolung statisch 
Betriebsart des Prüflings 
II.a 
U1 
0 V  
U2 
-14,0 V  
∆U1 
-1 V 
Ri 
≤ 30 mΩ 
t1 
60 s 
t2 
≥ 60 s, aber mindestens bis die Komponente den thermischen Zustand 
wie zu Beginn der Prüfung erreicht hat 
tr 
≤ 10 ms 
tf 
≤ 10 ms 
Anzahl der Zyklen 
1 
 
 
Bild 17: Prüfimpuls Prüfung Verpolung statisch 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 39 页
MBN 10567:2018-03, Seite 39 
Copyright Daimler AG 
7.14.2.2 Testfall 2: Verpolung dynamisch 
Dieser Testfall überprüft die Verpolung der Komponente im laufenden Betrieb bei nicht mehr startfähigem 
Fahrzeug. 
Tabelle 28: Prüfparameter Prüfung Verpolung dynamisch 
Betriebsart des Prüflings 
II.b 
U1 
10,8 V  
U2 
-4,0 V 
Ri 
≤ 30 mΩ 
t1 
60 s 
t2 
≤ 5 min 
tr 
≤ 10 ms 
tf 
≤ 10 ms 
Anzahl der Zyklen 
3 
 
 
Bild 18: Testfall Prüfung Verpolung dynamisch 
 
7.14.3 Anforderung 
Während der Verpolung dürfen keine sicherheitsrelevanten Funktionen z. B. bei elektrischen Fenster-
hebern, elektrischem Schiebedach, Anlasser, usw. ausgelöst werden. 
Funktionszustand C 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 40 页
MBN 10567:2018-03, Seite 40 
Copyright Daimler AG 
7.15 Prüfung Masseversatz 
7.15.1 Zweck 
Potentialdifferenzen zwischen verschiedenen Masseanbindungsorten können Signalverfälschungen 
zwischen Komponenten an diesen Anbindungsorten verursachen. Es ist sicherzustellen, dass 
Potentialdifferenzen zwischen Massepunkten bis zu einer Höhe von statisch ± 1 V im elektrischen Verbund 
keine Beeinflussung von Komponentenfunktionen hervorrufen. 
7.15.2 Prüfung 
Besitzt der Prüfling mehrere Spannungs- und Masseanschlüsse, ist die Prüfung für jeden Anschlusspunkt 
separat durchzuführen. 
Die Komponente wird wie unter Bild 19 verschaltet und gemäß Tabelle 29 geprüft. 
Tabelle 29: Prüfparameter Prüfung Masseversatz 
Betriebsart des Prüflings 
II.c 
Utest 
UB 
ttest 
≥ 60 s 
U1 
1 V  
Anzahl der Zyklen 
1 Zyklus pro Testfall 
Testfall 1 
S1 
Position 1 
Testfall 2 
S1 
Position 2 
  
 
Legende 
B 
Bussystem 
S 
Signalleitung 
S1 
Zweipoliger (a/b) Umschalter 
TE 
Weitere Komponente z. B. Prüfreferenz, Teststand, Simulationssteuergerät, Aktor, Sensor oder Last 
Bild 19: Prinzipschaltung Prüfung Masseversatz 
7.15.3 Anforderung 
Funktionszustand A 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 41 页
MBN 10567:2018-03, Seite 41 
Copyright Daimler AG 
7.16 Prüfung Ruhestrom 
7.16.1 Zweck 
Die Ruhestromaufnahme der Komponente soll ermittelt werden. 
7.16.2 Prüfung 
Bei Komponenten, die eine Nachlauffunktion haben (z. B. Lüfter), ist die Ruhestromaufnahme erst nach 
Beendigung dieser Funktion zu ermitteln. 
Die Komponente ist mit der zugehörigen Peripherie und Beschaltung zu messen. 
Die Prüfung ist mit den in Tabelle 30 dargestellten Parametern in allen Testfällen durchzuführen. 
Tabelle 30: Prüfparameter Prüfung Ruhestrom 
Betriebsart des Prüflings 
II.a 
Utest 
12,5 V 
Testfall 1 
Ttest 
Tmin 
Testfall 2 
Ttest 
40°C 
Testfall 3 
Ttest 
Tmax 
7.16.3 Anforderung 
Grundsätzlich gilt für alle Prüflinge das Ziel einer Ruhestromaufnahme von 0 mA. Die maximalen Grenz-
werte sind in Tabelle 31 mit Itest definiert. 
Tabelle 31: Anforderungen Prüfung Ruhestrom 
Testfall 1 
Itest 
≤ 0,1 mA 
Testfall 2 
Itest 
≤ 0,1 mA 
Testfall 3 
Itest 
≤ 0,2 mA 
Für Prüflinge, die nach Kl. 15 AUS betrieben werden müssen, darf in der Ruhephase über 12 h max. 
≤ 1,2 mAh (oberhalb 40°C entsprechend ≤ 2,4 mAh) Energie verbraucht werden. Dieser Verbrauch ist in 
allen denkbaren Fahrzeugruhezuständen und jedem beliebigen 12 h-Zeitraum immer einzuhalten. 
Anderenfalls ist eine Freigabe der für Ruhestrommanagement zuständigen Fachabteilung notwendig. 
Nachlauffunktionen sind ebenfalls durch die für Ruhestrommanagement zuständige Fachabteilung 
freizugeben. 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 42 页
MBN 10567:2018-03, Seite 42 
Copyright Daimler AG 
7.17 Prüfung Rückspeisungen 
7.17.1 Zweck 
Die Unabhängigkeit geschalteter Klemmen ist sicherzustellen. 
Mit dieser Prüfung wird die Rückwirkungsfreiheit des Prüflings auf geschaltete Klemmen (Kl. 15, Kl. 87, 
Kl. 30c, …) sowie Weckleitungen nachgewiesen. 
7.17.2 Prüfung 
Die Prüfung ist mit den in Tabelle 32 und Bild 20 dargestellten Parametern in beiden Testfällen durch-
zuführen. 
Tabelle 32: Prüfparameter Prüfung Rückspeisungen 
Betriebsart des Prüflings 
II.c 
Utest 
UBmax - 0,2 V 
Ttest 
Tmax, TRT und Tmin 
R1 
≥ 10 kΩ 
Anzahl der Zyklen 
1 Zyklus je Prüftemperatur in beiden Testfällen 
Testfall 1 
S2 
geschlossen 
Testfall 2 
S2 
offen 
7.17.2.1 Prüfungsablauf 
Der Prüfling ist entsprechend der Verschaltung im Fahrzeug anzuschließen (inkl. Sensoren, Aktoren, usw.) 
und entsprechend der in Tabelle 32 geforderten Betriebsart zu betreiben. Abhängig vom Testfall kann die 
Betriebsart ggf. nicht erreicht werden. Der Schalter S1 ist dabei geschlossen. Zu messen ist der 
Spannungsverlauf an der zu prüfenden Klemme bei deren Abschaltung durch S1. 
Die Abschaltung muss z. B. mit einem Relais oder einem Schalter (RSchalter_offen → ∞) erfolgen. Weitere evtl. 
vorhandene Spannungsquellen, wie z. B. die Kl. 30, dürfen während der Prüfung nicht abgetrennt oder 
abgeschaltet werden (entsprechend dem Verhalten im Fahrzeug).  
Der Spannungsverlauf an der zu prüfenden Klemme ist mit einem Messgerät V mit einem 
Eingangswiderstand ≥ 10 MΩ (z. B. Oszilloskop) zu messen. 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 43 页
MBN 10567:2018-03, Seite 43 
Copyright Daimler AG 
 
 
Bild 20: Prinzipschaltung Prüfung Rückspeisungen 
 
Legende 
S1  
 
Schalter 1 
S2  
 
Schalter 2 
K  
 
Zu prüfende Klemme 
V 
 
Messgerät 
7.17.3 Anforderung 
Es gelten die Anforderungen gemäß Tabelle 33. 
Tabelle 33: Anforderungen Prüfung Rückspeisungen 
U1 
≤ 1 V 
t1 
≤ 20 ms 
Spannungsrückspeisungen auf die zu prüfende Klemme sind nur bis zu einem Pegel von maximal U1 
zulässig. 
Die Spannung an der unbeschalteten zu prüfenden Klemme muss innerhalb von t1, ab dem Zeitpunkt der 
Abschaltung, unterhalb U1 abfallen. 
Die Spannungszeitkurve muss stetig fallend verlaufen. Eine Unstetigkeit der Kurve durch positive Impulse 
ist nicht erlaubt. 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 44 页
MBN 10567:2018-03, Seite 44 
Copyright Daimler AG 
7.18 Prüfung dual versorgter Komponenten 
Bei Komponenten mit mehreren voneinander unabhängigen Versorgungsspannungseingängen, z. B. bei 
Versorgung durch voneinander unabhängigen 12 V Teilbordnetzen Kl. 30 und Kl. 30s, ist die Prüfung nach 
Kapitel 7.19 (Prüfung Ausgleichsströme mehrerer Versorgungsspannungen) durchzuführen. 
Des Weiteren sind die Prüfungen des Kapitels 7 mit der in Tabelle 34 dargestellten Variantenmatrix durch-
zuführen. 
Tabelle 34: Variantenmatrix bei dual versorgten Komponenten 
Bordnetz 1 
Bordnetz 2 
Funktionszustand 
Prüfimpuls (1) 
Prüfimpuls (1) 
s. Prüfung (1) 
Prüfimpuls (1) 
Kl. 31 
s. Prüfung (1) 
Kl. 31 
Prüfimpuls (1) 
s. Prüfung (1) 
Prüfimpuls (1) 
UB 
A 
UB 
Prüfimpuls (1) 
A 
(1): Jeweilige Prüfung bzw. Prüfimpuls siehe Kapitel 7. 
 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 45 页
MBN 10567:2018-03, Seite 45 
Copyright Daimler AG 
7.19 Prüfung Ausgleichsströme mehrerer Versorgungsspannungen 
7.19.1 Zweck 
Bei Komponenten mit mehreren voneinander unabhängigen Versorgungsspannungseingängen, z. B. bei 
Versorgung durch voneinander unabhängigen 12 V Teilbordnetzen Kl. 30 und Kl. 30s, wird mit dieser 
Prüfung die interne Unabhängigkeit dieser Versorgungszweige ermittelt. 
7.19.2 Prüfung 
Die Prüfung ist mit den in Tabelle 35 dargestellten Parametern in beiden Testfällen durchzuführen. 
Tabelle 35: Prüfparameter Prüfung Ausgleichsströme mehrerer Versorgungsspannungen 
Betriebsart des Prüflings 
I.b 
ttest 
60 s 
Prüfpunkte 
Anlegen der Prüfspannung Utest zwischen  
• 
beiden Versorgungsanschlüssen Kl. 30 und Kl. 30s 
• 
ggf. weiteren, mit der jeweiligen Fachabteilung des 
Auftraggebers abgestimmten, Prüfpunkten 
siehe Bild 21 
Anzahl der Zyklen 
1 Zyklus je Testfall 
Testfall 1 
Utest 
32 V 
Testfall 2 
Utest 
- 32 V 
 
 
Bild 21: Prinzipschaltbild Prüfung Ausgleichsströme mehrerer Versorgungsspannungen 
7.19.3 Anforderung 
Der im Prüfaufbau gemessene Ausgleichsstrom darf Itest ≤ 100 µA nicht überschreiten. Nach dem Test ist 
der Funktionszustand A nachzuweisen. 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-23

