# ES_6L2T-1274-AA_EN_1984-08_导线回路描述&颜色规范.pdf

## 文档信息
- 标题：Microsoft Word - psf-draft-August2004.doc
- 作者：rreini
- 页数：17

## 文档内容
### 第 1 页
 
Engineering Specification 
PART NAME 
PART NUMBER 
Specification - Wire Circuit Description & Color 
ES-6L2T-1274-AA 
LET A 
A 
A 
A 
A 
A 
A 
A 
A 
A 
A 
A 
A 
A 
A 
A 
A 
 
 
FR 1 
2 
3 
4 
5 
6 
7 
8 
9 
10 
11 
12 
13 
14 
15 
16 
17 
 
 
LET  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
FR  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
LET 
FR 
REVISIONS 
DR 
CK 
REFERENCE 
840820 
A 
1-17 Initial Release of ES-6L2T-1274-AA 
RR 
PS 
ES-5C3T-1274-AA 
840820 
A 
 
AE00-E-11686562-000 
 
 
PREPARED/APPROVED BY 
 
 
 
 
 
 
R. W. Reini 
 
 
 
 
 
 
CHECKED BY 
DETAILED BY 
 
 
 
 
 
 
P. Simmons 
 
 
 
 
 
 
 
CONCURRENCE/APPROVAL 
 
 
 
 
 
 
SIGNATURES 
 
 
 
 
 
 
Design Engineering Supervisor 
 
 
 
 
 
 
 
 
 
 
 
 
 
Design Engineering Management 
 
 
 
 
 
 
 
 
 
 
 
 
 
Manufacturing Engrg. 
 
 
 
 
 
 
 
 
 
 
 
 
 
Quality Control 
 
 
 
 
 
 
 
 
 
 
 
 
 
Purchasing 
 
 
 
 
 
 
 
 
 
 
 
 
 
Supplier Quality Assistance 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
FRAME 1 
OF 
17 
REV A 
 
PD 
May 1988 
3947a1e 
 
(Previous editions may NOT be used) 


### 第 2 页
 
Engineering Specification 
FRAME 
2 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
 
1.0 
Introduction 
 
1.1 
Purpose and Scope 
 
The purpose of this document is to provide the information necessary for understanding the Next 
Generation Circuit Numbering System (NGCNS) project’s Potential System/subsystem Functional (PSF) 
circuit numbering, naming, and color-coding definitions. 
 
1.2 
Document Road Map 
 
Section 1 
Introduction 
The document overview section describes the purpose and scope of this document along with the 
conventions used and is intended to serve as a road map for the reader. 
Section  2  
Tables 
This section points to key tables used for this next generation circuit numbering system project’s Potential 
System/subsystem Functional (PSF) circuit numbering, naming, color-coding and connector numbering 
definitions. 
Section  3  
Function Selection 
This section contains a technical overview of the function selection process. 
Section  4  
Assignment Examples 
This section contains several assignment examples.   
Section  5  
Function Tables 
This section contains a complete collection of all of the applicable function tables. 
Section  6  
Statistics 
This section contains the statistics on the count of circuits per wire shield color. 
Appendix  A  
Abbreviations, Acronyms, & Terms 
This section provides a standard listing of the Abbreviations, Acronyms, and Terms used in this program.  
 
1.3 
Format of Circuit Numbers 
 
The circuit designator is always a coded expression structured of five (5) alphanumeric characters.  The 
letters I, O and Q are not used because they can be misread as numbers. 
 
1.3.1 Potential/Category – First Character 
 
The first character is used to define the Potential/Category.  The character employed is taken from the key 
word (in English) of the applicable Potential’s description.  A pointer to the potentials is listed in Section 
2.0, Tables, Table 2-1. 
 
1.3.2 System – Second Character 
 
The second character is used to define the System.  The letter is taken from the key word (in English) of 
the applicable System’s description.  A pointer to the systems is listed in Section 2.0, Tables, Table 2-2. 
 
1.3.3 Subsystem – Third Character 


### 第 3 页
 
Engineering Specification 
FRAME 
3 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
 
The third alphanumeric character is used to define the Subsystem.  Letters are used to differentiate normal 
size Subsystems within a System.  Numbers are used only for a larger number of Subsystems. A single 
numeric digit (0, 1, 2, 3, . . .) in this position is used in conjunction with the 2-digit function number in order 
to cover all the functions of a single Subsystem.  A pointer to the subsystems is listed in Section 2.0, 
Tables, Table 2-3. 
 
1.3.4 Function Category – Fourth and Fifth Characters 
 
The fourth and fifth characters (read as one expression) are used to define the Function.  The numbers 
used are from 01 to 99.  Guidance to defining and selecting Function-Descriptions is explained in Section 
3.0, Function Selection.   
Functions are listed in the Function Tables. Because of their great number and potential growth they are 
listed in a separate document, a pointer to which can be found in Section 5.0, Function Tables.  That 
document will grow as new features are added to the vehicles. 
 
1.4 
Circuit Number 
 
Each circuit number is combined from:  Potential, System/Subsystem, Function (thus the abbreviation 
PSF).  
 
A circuit consists of one or more cutleads. Cutleads are differentiated with a suffix to the circuit number. 
The majority of the circuit numbers have a 1-letter suffix.  Circuits with more than 23 cutleads or with 
numerious design changes will have a 2-letter suffix appended.  The letters I, O and Q are not used 
because they can be misread as numbers. 
 
Here are some examples of circuit designations. 
 
For logical schematics:  SBP01, GLN15, CD133  (note: these have no suffix for cutleads) 
For physical schematics and harnesses with cutlead suffixes:  SBP01A, GLN15ZA, GD133H 
 
Each cutlead of a circuit shall be given a cutlead suffix designation that is unique within that circuit 
throughout all harnesses carrying the circuit in the entire vehicle platform.  Equivalent cutleads, with the 
same lengths, wire specs and gauges, in different harness levels of the same harness family shall carry the 
same suffix. 
 
1.5 
Format of Connection C-Numbers 
 
The formats for connection C-numbers depend on the type of connection being made. 
 
1.5.1 On-Device 
 
The format for an on-device C-number is: 
 
C#AA##-A 
 
C 
Connector 


### 第 4 页
 
Engineering Specification 
FRAME 
4 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
# 
Numeric – Location Code (defined in 
section 2.4, Table 2-4) 
AA 
Alpha – System Code (defined in 
section 2.4, Table 2-5) 
## 
Numeric – Two-Digit Function Code 
(defined in section 2.4, Table 2-5) 
A 
Alpha – used if there are multiple 
connectors on the same device or 
connectors on multiple devices with the 
same function 
 
1.5.2 In-line 
 
The format for an in-line connector C-number is: 
 
C##-A 
 
C 
Connector 
First # 
Numeric – Location Code of the 
connector (defined in section 2.4, Table 
2-4) 
Second # 
Numeric – Location Code of the 
connecting system (defined in section 
2.4, Table 2-4) 
A 
Alpha – Multiple in-line connectors in 
same location to same connecting 
system 
 
1.5.3 Ground Connector 
 
The format for a ground connector C-number is: 
 
G#AA##-A 
 
G 
Ground 
First # 
Numeric – Location Code (defined in 
section 2.4, Table 2-4) 
First A 
Alpha – System Code (defined in 
section 2.4, Table 2-5) 
Second A 
Alpha – Subsystem Code (defined in 
section 2.4, Table 2-5) 
Second and Third # 
Numeric – two-digit function code 
(defined in section 2.4, Table 2-5) 
Third A 
Alpha – Multiple grounds in same 
location with same function 
 
1.5.4 Splice 


### 第 5 页
 
Engineering Specification 
FRAME 
5 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
 
The format for a splice C-number is: 
 
S#AA##-A 
 
S 
Splice 
First # 
Numeric – Location Code (defined in 
section 2.4, Table 2-4) 
First A 
Alpha – System Code (defined in 
section 2.4, Table 2-5) 
Second A 
Alpha – Subsystem Code (defined in 
section 2.4, Table 2-5) 
Second and Third # 
Numeric – two-digit function code 
(defined in section 2.4, Table 2-5) 
Third A 
Alpha – Multiple splices in same 
location with same function 
 
 
2.0 
Tables 
 
2.1 
Purpose 
 
This section explains the potentials, systems, and their multiple subsystem names in tabular format. 
 
2.2 
Wire Colors 
 
Wire colors are defined in Section 3.0 of this specification. 
 
2.3 
Principle Schematics 
 
Pointers to Principle schematics (schematics that illustrate the principles behind this specification) can be 
found in section 4.0.  One of those schematics, the Splice Example, also: a) explains the usage of cutlead 
suffixes, b) provides guidance on how to handle wire assemblies used twice in a vehicle, and c) provides 
guidance on how to handle wire assemblies used for left-hand drive (LHD) and right-hand drive (RHD) 
vehicles. 
2.4 
Pointers To Tables 
Pointers to the main tables in the specification are shown below. 
Table 2-1.  Potentials  
This table can be found at https://f1.ford.com/eRoom/EESE/EESECAE/0_2105d9 in the file "ES-6L2T-
1274-AA.xls" and the sheet "Potential-, System-Table."  
Table 2-2.  Systems 
This table can be found at https://f1.ford.com/eRoom/EESE/EESECAE/0_2105d9 in the file "ES-6L2T-
1274-AA.xls" and the sheet "Potential-, System-Table."  


### 第 6 页
 
Engineering Specification 
FRAME 
6 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
Table 2-3.  Subsystem Names  
This table can be found at https://f1.ford.com/eRoom/EESE/EESECAE/0_2105d9 in the file "ES-6L2T-
1274-AA.xls" and the sheet "Subsystem Table."  
Table 2-4.  Location code document 
This table can be found at https://f1.ford.com/eRoom/EESE/EESECAE/0_2105d9 in the file "ES-6L2T-
1274-AA.xls" and the sheet "Location Code Table." 
Table 2-5.  Device Connector 
This table can be found at https://f1.ford.com/eRoom/EESE/EESECAE/0_2105d9 in the file "ES-6L2T-
1274-AA.xls" and the sheet "Device Connector." �
 
3.0 
Function Selection 
 
3.1 
Purpose 
 
This section contains a technical overview of the function selection process. 
 
3.2 
Function Selection 
 
3.2.1 How to select the right Function Description 
 
NOTE:  All cutleads of a circuit must have the same circuit number.  They differentiate only in the 
cutleads suffix. 
A circuit starts at a device and may go to one or more connections in different physical versions.  
Therefore a "from... to..."  type Function description is not applicable. 
Devicess where a circuit starts are: 
� A Switch or Relay output feeding one or multiple loads, 
� A Ground Point supporting one or multiple loads 
� A Diagnostic Connector feed by one or multiple Modules. 
� Special case for Bus connections between Modules, not connected to diagnostic: the Module from the 
base Vehicle-Option-Level gives the name to the circuit. 
� Special case for multiple switched grounds feeding one CONTROL MODULE input: they get one 
common name. (e.g. all AJAR - SWITCHES # NON DRIVER DOORS) 
Devices that receive circuits are final LOADS, in their simple version.  Their connecting wires are named 
for the device before and after them. 
The feed wire for a final load is named for the related SWITCH -, RELAY - or MODULE output, and the 
ground wire is named for the related GROUND or GROUND support from a MODULE.  The following 
component types are examples for that group of components: 
- 
DIODE, CAPACITOR 
- 
LAMP, BEAM 
- 
MOTOR, FAN, BLOWER, PUMP 


### 第 7 页
 
Engineering Specification 
FRAME 
7 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
- 
SOLENOID, ACTUATOR, VALVE 
- 
HEATER 
- 
HORN, BUZZER, SOUNDER, SPEAKER 
- 
GAUGE, CLOCK, INDICATOR 
- 
CIGAR LIGHTER, POWER OUTLET 
 
3.2.2 Functions of subsystem CONTROL MODULES are still used if multiple modules 
are compound. 
 
 
e.g. PCM may compound TRANSMISSION, PASSIVE ANTI THEFT, and FOUR WHEEL DRIVE 
 
3.3 
Guidance to define Function Descriptions 
 
1. In general it should be precise as needed but not over-specified to allow usage in subsystems which 
are different realized 
2. The words across all Function-Descriptions should be consistent (use the same word for the same 
thing).  If different words are hard established, both are used to support text search, e.g. MOON/SUN 
ROOF. 
3. There should be no identical Function-Description added in a further Subsystem.  The previous point 
should help to avoid this situation. 
 
3.4 
Wire Markings 
 
This information can be found at https://f1.ford.com/eRoom/EESE/EESECAE/0_2105d9 in the file "ES-
6L2T-1274-AA.xls" and the sheet "Wire marking."  
Table 3- 1.  Circuit Number – Potential/Category. 
This information can be found at https://f1.ford.com/eRoom/EESE/EESECAE/0_2105d9 in the file "ES-
6L2T-1274-AA.xls" and the sheet "Wire marking." 
 
3.5 
Identification Colors 
 
Identification colors are just used to differentiate wires that include the variant without a stripe.  
Identification colors are assigned indirectly with a number in the Function table.  This is necessary to avoid 
Base- and Identification-color duplication.   
Table 3- 2.  Circuit Number – Identification Colors. 
This information can be found at https://f1.ford.com/eRoom/EESE/EESECAE/0_2105d9 in the file "ES-
6L2T-1274-AA.xls" and the sheet "Wire marking."  
 
3.6 
Color Abbreviations Regarding IEC 57 
 


### 第 8 页
 
Engineering Specification 
FRAME 
8 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
This information can be found at https://f1.ford.com/eRoom/EESE/EESECAE/0_2105d9 in the file "ES-
6L2T-1274-AA.xls" and the sheet "Wire marking." 
Table 3- 3.  Color Definition & Closest Match Between Standards. 
 
This information can be found at https://f1.ford.com/eRoom/EESE/EESECAE/0_2105d9 in the file "ES-
6L2T-1274-AA.xls" and the sheet "Wire marking." 
 
4.0 
Assignment Examples 
 
4.1 
Purpose 
 
This section contains several assignment examples. 
 
4.2 
Circuit Number Examples 
 
Figure 4-1.  Assignment Example.  
These figures can be found at https://f1.ford.com/eRoom/EESE/EESECAE/0_2105d9 in the file "ES-6L2T-
1274-AA.xls" and the sheets "Assignment Example" and "Ctrl Mod. Example." 
 
  Figure 4-2.  Splice Example.  
This figure can be found at https://f1.ford.com/eRoom/EESE/EESECAE/0_2105d9 in the file "ES-6L2T-
1274-AA.xls" and the sheet "Splice Example." 
 
 
4.3 
C-Number Examples 
 
4.3.1 On-Device Connector 
 
Connector C5ME07 
 
C stands for Connector 
5 is the location – in this case, driver door 
M is the system – in this case, Multimedia 
E is the subsystem – in this case, Entertainment 
07 is the function code – in this case, SPEAKER - LEFT FRONT DOOR (WOOFER) 
 
4.3.2 In-Line Connector 
 
Connector C23-A 
 
C stands for Connector 
2 is the location of the connector – in this case, Instrument Panel 
3 is the location of the connecting system – in this case, Body 
A means the first of multiple in-lines in the I/P connecting to the body harness 


### 第 9 页
 
Engineering Specification 
FRAME 
9 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
 
4.3.3 Ground Connector 
 
Ground G2D134 
 
G stands for Ground 
2 is the location of the ground – in this case, Instrument Panel 
D is the system – in this case, Distribution 
1 is the subsystem – in this case, Ground 
34 is the function – in this case, left-hand A-pillar 
 
4.3.4 Splice 
 
Splice S2DB06 
 
S stands for Splice 
2 is the location of the splice – in this case, Instrument Panel 
D is the system – in this case, Distribution 
B is the subsystem – in this case, Bus Diagnostics 
06 is the function – in this case, CAN Medium Speed High 
 
5.0 
Function Tables 
 
This section points to the tables for all of the currently assigned functions.  These tables are subject to be 
changed as required.     
 
Information on function tables can be found at https://f1.ford.com/eRoom/EESE/EESECAE/0_2105d9 in 
the file "ES-6L2T-1274-AA.xls" and the sheet "Function Table – C3P Data." 
 
6.0 
REMOVED 
 


### 第 10 页
 
Engineering Specification 
FRAME 
10 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
 
APPENDIX A:  ABBREVIATIONS, ACRONYMS AND TERMS 
 
A.1 
Purpose 
 
This Appendix contains a standard listing of the Abbreviations, Acronyms, and Terms used in this program. 
 
A.2 
Abbreviations 
 
��������
������������
�����������������������������������������������������
���
����������������������������
�
����
��������������
�
���
����������������������������
�
���
����������������������������
�
������
��������������������������� ��!���"�
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�����
������������������.�������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
����
�����������������
�
�/��
���������������#�������
�
�
�
�
�����
����"�������������������
�
��0�
����� �0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
����
���������������� ��!���"�
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
���'���
���������������� ��!���"'����������������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
���'���
���������������� ��!���"'�����������������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�'��
�������(������� �
�
����
��������!�
�
����
����"��������".����������������
�
��0�
��(�����������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
����
��(��������������������
�
�'��
����� ������ �����
�
���
�".���������
�
�0��
�"$�������".���������������
�
���0�
�������������)�����..�!�0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�1��
����"�����1�(����������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
���
�����$�������"�2)������3�
�
�#��
�(�����(�#��������������� !�
�
����
����������������
�
�
�
�
�
�
�
�%���
��(!�%������������$�!���"��.������������
�
����
�����!���(�(�����"���
����$���$����!���.�������������������(� ����4������ ��5�
�0��
����0�..�(�
�
��,���
��(!���(�����"$�!�
�������������+1�����.����$������������".��"������������%�(�����������(�*����������
������ ����%������������$�!���"�-�


### 第 11 页
 
Engineering Specification 
FRAME 
11 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
��������
������������
�����������������������������������������������������
�
�
�
��
�������'����� ��(��
�
�'��
����!������
�
����
��".�������(�(����� ��
�
��6�
����������������6��)����
�
��1��
���������������1�������������(�
%������"������!�������������� ���7���������.����$�������(������ �����������
����(��(������������������������������������".����� ��(��������������������"�������-�
����
���"���������������������
�������������+1��0�������".��!�
��'�8�
��".��������'�����8����!�
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�%�
���(����(�%"�������
�
�%���
���.������%� ������� �����������(�����
�
�*�
���(����(�*""����!�
�
���
������"�����'��������������������!.��
����.�����**���������(�����."����.�����!.���
����
����������������)���
�
��0�
���������������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�1��
�!�����1�(��(���!�������
�
���
���������"�
�
��0�
����������"�� �0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�#���
�����������!�#����$�����"���������1�(��
��������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�
�
�
(��
���������������
�
��0�
������������0�(����2%�&3�
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
��0%��
���� ���0%��
0����(��� !����)��������������!���"���������"�(�����������(��������������(�������(�
��(�.�������7�(������������(�����.�� �(�� ����������(����.�"������.����(����-��
����9����������������������"�����-�
����
��������������������
����������������(�� ��������������������)�������������������������(������""��������
)�������%�&-�
�01�
�������0�"��!�1����������
������.�$����!����������������"���������"����%�&�$!��.����!�� �����%�&��.�!������
�((�����������������"���������$����������(-�
����
�����������!��������!���
�
�1��
��!��"��1����� ��� ��2�3�
�
��0�
������������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
����
��� �����������������(�������� �
��.���"����)��������������.����$�������.������ �����(�����."�������6��9������9��
��(�%+��(�� ��������������������(���������-�
����
��� �����������$�����(�-�
�����!�����:�(���"��'������"$������(������.����������%�&���$�!���"��������-�
�#�1�
���� ��#���������������������1�.����
�
�
�
�
%����
%������������������������
�
%����
%��������������"��������"������������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
%�+�
%���.��������"������+.���������
�
%����
%��������������"�������".����������������
"�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
%���
%�����������������������2���$� 3�
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
%���
%� ��������������".��������
�
%����
%� ��������������".����������������
�
%�&�
%������������������&����2"�(���3�
����;$�����;��������$�!���"�)����������������$�!���"�������������!���(����(��������
��������������""�����������


### 第 12 页
 
Engineering Specification 
FRAME 
12 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
��������
������������
�����������������������������������������������������
%�*��
%����������������$����������* ��������!���"�
�
%'%�
%���������'%����������
�
%%��
%����������%� ���������������2�����������03�
�
%%�1+0�
%����������!�%����$������ ��""�$���1��(�
+��!�0�"��!�
1��(�+��!�0�"��!������"�!�$��)����������2.�� ��""�(3���(������(����������������
�� ����-�
%%�%�
%���������'%�����������!���"��%� ������� �
�
%*��
%����������*�����"������������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
%���
���(�%��������������������
�������������+1��0�������".��!�
%0��
%�������0� ��������".���$����!�
�
%+��
%�(�+�������
������(������������"$�!�����������������������"$�!�.�����)���������������������
�������������$�!���"��������������(-�
%��
%����������������!.��
�
%1+0�
%����$���1��(�+��!�0�"��!�
�
%��
%� ������� ��.������������
�
%���
%��������������������� ��
�
%�+��
%����������������+��������!�
�
%���
%�������������$����!���� ��"�
�
%����
%���!�������� ���� ���������"����
�
�
�
�
�����
���(������"��������������������
�������������+1�9����"���!��������(������������2���(�������,�����������������39�
)�����������.����$������<�
=������(�����."������(�����������������������������������6������������(�����
(�� ��������
=������(�����."������(����������������������������������������������9�����
(�� ����������(����������������������
=������(�����."�������%+��(�� ���������.�$��������
��&�
��������������&����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�%0�
������%�(�0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�%&�
����(�%����������&����
������������(���(���������(������������(�������������-�
�*%0�
�����������*��� ����(�%����������0�(����
�
�*�+��
������*��������+���
�
�*0�
�����*�(��������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�0%��
��������0�(���,�%����������!����
0����(��� !����)��������������!���"���������"�(�����������(��������������(�������(�
��(�.�������7�(������������(�����.�� �(�� ����������(����.�"������.����(����-��
����9����������������������"�����-�
����0�
�������".���������(���0�(��������
�
�����
���(�������,������������������
�
�1�
���������1��(�
�
�1&�
����(�1�.�����$���&����
�
����
���������������������(����
�
����
������������������
�
�
�
�
�%0�
��������%�����������0�(����
���%�&�����������������������!�����������������!�$���(�����������$�!���"�-�
�
�
�
����
�������������"���
�
��0�
�����(������� ���0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�%��
�!$��(�%������������������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�%/�
��:�(���"���
�����>?���"$����(�������(�$!���;@;��!"$���.����(�� �����������
��+��
���()������"���(�+.������ ������� !�
�


### 第 13 页
 
Engineering Specification 
FRAME 
13 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
��������
������������
�����������������������������������������������������
���
���()�!��������
�
��0�
�����(������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�'��
���()����
�
�7�
����7�
�
�
�
�
*��0�
*��� ����(������ �0�������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
*���
*������!�����.��������
��"��$��)����������������$!���������"���� �-�
*��
*�����"������������"�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
*��0�
*������ �������������������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
*���
*���������������������"����
�
*���
*��� ����(���������������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
*���
*��������".��������
�
*0��
*�����0���� ����.��������
��"��$��)����������������"���� ��-�
*'+�
*�.������+��.���
�
*'��
*�����"����������
�
*�0�
*�����"����������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
*�0�
*����������������0�(����
�
*�+�
*�����������������(��(��+� ���7������
6���&-�-�$���(����������������� ���7������)�����(�����������(��(������
��""������������!���"�9������������(������9����-�
*���
*��������������� ��!���"�
�
*#��
*�����������#��������!��"����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�
�
�
8>�
8�$�6�"$���+���
+�����������������"��������"�.��(������������������(�'���)���������
8��
8��������������
�
8�*�
8���������������9�*��-�
�
�
�
�
A�0�
A��.�������0�"��!�
�
�"'��
����"������.��������
&��������.��(��������0������"������"�����!���"-�
�
�
�
��0�
�� ���� ���������0�(����2%�&3�
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
���
�����������
�
����
��)������������
�
�+��
��"���(�+.������ ������� !�
������.��"��!��.������ ������� !��������"�(����"�!��������.�������(��������������
�!���"���������)���������$����.��.����:������������������"����.������ ������� !-�
�1�
�����1����
�
��$�
�������� ���������$���2$���43�
��)������(���$���2$���43������(�����(������ ����$���-�
����
�������� ����������!���
��)������(����!��������(�����(� ���.�����!���-�
��6�
�������� ���������6�$$���2$����4���B3�
��)������(���6�$$��������(�����(������ ����6�$$����C�!��2�3D-�
�
�
�
0����
0��������������������
�
0��
0���� ���������"�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
""�
"����"������
�
".��
"�����.��������
&��������.��(��������%� �����"������"�����!���"-�
01��
0����������� �1�E����"���������"����
�


### 第 14 页
 
Engineering Specification 
FRAME 
14 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
��������
������������
�����������������������������������������������������
01���
0����.��:�1������������������
�
01+0�
0����(�1��(�+��!�0�"��!�
�
"��
"���������(�
�
0�$�
0������ ���������$���2$���F3�
�� �������(���$��������(�����(������ ����$����2$���F�������!��9�$���B��������)���(���
6�$$��9�$���>G������)��(����$���B>��������� �)��(3-�
0���
0������ ����������!���
�� �������(����!��������(�����(� ���.�����!���-�
0�0�
0�"��!������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
0�6�
0������ ���������6�$$���2$��������F3�
�� �������(���6�$$��������(�����(� ���.����6�$$����C�!��2�3D-�
0�+��
0����������������+��������!�
�
0�&�
0������"������"��&����
�
0H�
0�(���H����
�
�
�
�
6'��
�����..����$���
�
6��+�
6������"�����������"������+.���������
�
6�#�
6�#� ������"�(����
�
6�$$���
I��!���J������$���K���)��6�$$����J��!���
�
6���
6�)������������������������
�������)������������"��(����!�������������������������$���(�(�� ����������(����.�
"������.����(����-�
6����
6�:��������������.��(���������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
6�#�
6�����������#�������
�
6#1�0�
6���#��������1��(�"��������0�"��!�
������������!�.�)���(�1��(�"��������0�"��!������"�������������(�������������%�&�
���.�)���(�(�)�-�
�
�
�
+���**�
+������(���� ��������**�
������**����������"������ ���������������� ��"����������(��"�������(�� �������-�
+���
+������(������� �������
�
+��
+.����������
+.������ ����������)��������%�&��:��������������"����.������ ������� !-�
+���
+�����"����� ��""�$���
�
�
�
�
�>44��
��������������&����2����������"�(���3�
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
����
����"���������"��!���"�
�
����
��� ��"��������!����"�
���"������(���(��������.����$�����������(�����."�������������������".�����9�
��$�!���"9����.�������2�-�-���� �������9��%0���$�!���"9�---3-�
�����
��������������������!���"�
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
���
�����������".����'������(���������
�
��0�
��)����������������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
���
������"���������"����2���.��3�
�
����
���(�������������������"����2���.��3�
�
��0�
������ ���������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�%���
��)��������%����������������."����
��.���"����
�
�%+�
���(����%� ������� �+������
�+1���������!����.����$���������������� �����(�����."�����������".�����
��$�!���"-�
��0%��
���������0%��
0����(��� !����)��������������!���"���������"�(�����������(��������������(�������(�
��(�.�������7�(������������(�����.�� �(�� ����������(����.�"������.����(����-��
����9����������������������"�����-�
�*��
����"�����*����������
�����!�����:�(���"��'������ ������((������������.���������.�������%�&�(���������
"�!�$����������(�$!����������)������������ �������)���������� ������������������(����
)����������%�&-�
�0��
��� ��"�0��� �"�������"�
���"������(���(��������.����$���������������� �����(�����."�����������������


### 第 15 页
 
Engineering Specification 
FRAME 
15 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
��������
������������
�����������������������������������������������������
.���������2�-�-�����������9���������---3�
�����
���(�������������..��������������
�
�16���
�
����9�1������9�6������9������9���(���)������� ��������� �������������������
�1+�
���(����1�E�����+�(���
�������'���"����(�����������������E�����������$�!���"�(�� �������������������..�������
��������������� ���7�����-�
����
��)������(�� ������"�(����
#��������"�(�����������""���������)��������������������*�+�������������������(����
����������� ���(�������������$�!�����$��������� �,�����!���-�
����
�����������!���"'��$�!���"������������
����������"$���� 9���"�� 9���(���������(�� �(�����������
���0�
��)������(�� ������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
��0�
������ ���������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
����
��� ��"�������� ����"�
�������������������"��� �"�������"�������(�$!�������� ��"�0��� ���(������� �����
.��(����(�����."����.���������������������.�� ��"-�
��&�
����������������&����
�
����
�������$"���������������
�
�#��
���(����#�������������.������������
�
��0�
��������(���0�(�����(�
�
�
�
�
1�0�
1��(�"��������0�"��!�
%�&�"�"��!����)�����(����"�!�$��)����������)�����(����"�!�$�����(����"-�������
"�"��!����������(�)�������%�&��������.�)��-�
1���
1�"�������������������������!�"�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
1���
1�����������.�������
�
1��0�
1�����������.�������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
1���
1�"�������"������������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
1%�
1�(����(�%"�������
�
1%0�
1����%����������0�(����
�
1%��
1�"����%���!��������
�
1��
1� ���������
�
1*0�
1����*��� ����(�0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
1A%�
1�"���'A�!�����%���!�
��������(���(������E����!'(� ������������������������������$�!���"�
10��
1����0�����E�����
�
16+�%�
1������� �6�(��
%�&�������������(���"���� ��������������������$���(�(����������������""����������
1+0�
1��(�+��!�0�"��!�
%�&�0�"��!�����������������$�!���"��.������������(�(�� ���������������(���������
�������!�$�����(-�
1�6�
1����� '���$�$����!�6�"$���
�0%����"$���)������������������L�����$�$����!������������
1�+�
1� ��������(�������+.�����
�
11�
1� ���1����
�
1'1�
1�E����'1��.�����
�
1���
1�����������$��������
�
1���
1����������������
�
�
�
�
��%�
������!��������"������%� ������� �
�
��1��
��"���������1�(����������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�����
��"������"�������".����������������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
�����
����������!���� ��������!���"�
���$���(�(�� ��������!���"���".����(�������������������)������(����()����������
��������� ��������������.����(����������������������� ��(�(�(�� �������-�
��*��
������� �����"��*��� ����(��� ���"�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������


### 第 16 页
 
Engineering Specification 
FRAME 
16 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
��������
������������
�����������������������������������������������������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
��0�
�.��(���������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
����
����(��(����.���������������
�����.����������(��(�"�����������������������������""���������������'.�������������
����)�������"�(�������""����������-�
����
�!���"����� ���.������������
���� ���.�����������������.����(���(������(������"�������$���������.�������������
�.��������!���"������$�!���"-�
�%��
������%���(�(�
�
�8��
�"����8����������:�
�
����
�������(��������
�
�+��
������+��������!�
�
���
�����������������!.��
����������.�����!.�9�(�����.�(���(������������������9�)�����������(�������������
��(������!�������������������� ���!����������������(��� ��
�����
��$�!���"���� ��"��.��������.������������
�
�1��
��..��"������1����������!���"�
�
�����
��$�!���"��.���������� ��������.������������
�����2�����**3�����"������E����(�����������������������%�&���$�!���"������
.����(����.������������"�������$��������(�� ���������.�$�����������������%�&�������
��..���-�
���1�
��$�!���"���� ��"�1�E����"�����
�
���
��$�!���"����"�2)����0�3�
�
����
�!���"���"��.����(�
�
�'��
����)����
�
�
�
�
���
����������������
�
����
���$��(�����(�
�
���
�����������������
�
����
������������������!���"�
�
�*��
�����"������*(���������������(��
�
�6+�%�
�����"����� �6�(��
%�&������������������""����������)�����������"����������%�&�������������������-�
����
��������� ��"����"�2)������3�
�
����
���������������������������
�
�
�
�
&60�
&��� ��(�6�"�����
�
�
�
�
#��0�
#�������������(���������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
#����
#����$������������)���������� �
�
#�0�
#��������!��"����0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
#%0��
#�������%"�� ���!�0���� ���!���"�
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
#*��
#�������*"� ����������
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
#*6�
#�������*(�������������6�"$����
�
#��0�
#����$������(���������0�(����
#���������$�!���"�%�&'"�(�����������""���������)��������������������*�+��������
�����������(��������������� ���(�������������$�!�����$��������� �,�����!���-�
#��
#������������������!.���
�
�
�
�
��1�
����()�(�������"���1�E����"�����
����������.������ ��(�����������(�����.�� ����������
����
����()�(�������"�����"�� �
�����.������(��� ��.�������)����$!���������(�����."����.�� ����������"���� ��"�
*".��"����������..���������8�$�>����BF�"�����-�
��1�
����()�(������ ��1�E����"�����
�
�*�%�
����()�(��*��� ����(�����(��(������
%� ������� �
�


### 第 17 页
 
Engineering Specification 
FRAME 
17 
OF 
17 
REV. LET.   A 
PART NO.   ES-6L2T-1274-AA 
 
PD 
May 92 
3947a2e 
 
(Previous editions may be used) 
 
 
 
��������
������������
�����������������������������������������������������
�����
����()�(�����(��������.�������.�������������
�
 
A.3 
Acronyms 
 
��1��
���������������1�������������(�
%%�%��
%����������%�����������!���"��%� ������� �
%/�%��
�
0�������������)�����.���(������.�� ��"�
�
�0%��
��������0�(���,�%�����������!����
�%0��
��������%����������%�&�
*�+� �
*�����������������(��(�+.���������
A�0� �
A��.�������0�"��!��
�
+���**�
�
+��$���(���� ����������������"�����"���������� ���������
�*�� �
����"�����*(�������������
�16���
�
����9�1������9�6������9������9���(���)������� ��������� �������������������
�
1�0� �
1��(�"��������0�"��!�
1+0��
1��(�+��!�0�"��!�
�
��1��
����()�(�������"���1�E����"�����
���� �
����()�(�������"�����"�� �
��1��
����()�(������ ��1�E����"�����
�
�*�%��
����()�(��*��� ����(�����(��(������%� ������� �
�����
����()�(�����(��������.�������.�������������
�
 
A.4 
Terms 
 
������ ���
�
2������ ��3��� ���� ������������������(��.��!-�
$���
�
�"�������(����������������".�������""��(�
�!��� �
����.������ ���$����2�)��6�$$���3���"$���(�4����F�
�
*����$!���
�
��"��.����(2�3�$��)�����)�����"����(����$!���-�
"��
�
0���������(��
6�$$���
�����$�������)����$$����"������$!���
�
����$!���
�������(�"��������!����
��0���������(���0�(��������
����.�0�(��
0�(������$�������K������������)�.�)��9�(��"���9�E��������������-�
�
��  ���
��.�!���������������������()������(�������������
@�
�
�����:�(������ �����:�(���"�����"$���
�
 
 

