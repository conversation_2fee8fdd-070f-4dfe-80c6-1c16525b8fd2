# VW_01059_1_EN_2011-11_CAD&CAM数据要求_第1部分：技术特性的呈现.pdf

## 文档信息
- 标题：VW01059-1_2001-01_EN_withdrawn.pdf
- 作者：EWEHMEY
- 页数：9

## 文档内容
### 第 1 页
Klass.-Nr. 22 63 2
January 2001
Requirements for CAD/CAM Data
Representation of Technical Characteristics
VW
010 59-1
Konzernnorm
Descriptors:
CAD/CAM, CAD, drawing, data model, 3D model, CATIA, Pro/E
Page 1 of 9
Fachverantwortung/Technical responsibility
Normung/Standards (EZTN, 1733)
EZTI           Dr. Schwenke, Tel. 76892
Fischer       Tel.: +49-5361-927995
Sobanski
Vertraulich. Alle Rechte vorbehalten. Weitergabe oder Vervielfältigung ohne vorherige schriftl. Zustimmung einer Normenabteilung des Volkswagen Konzerns nicht gestattet.
Vertragspartner erhalten die Norm nur über die zuständige Beschaffungsabteilung.
Confidential. All rights reserved. No part of this document may be transmitted or reproduced without the prior written permission of a Standards Department of the Volkswagen Group.
Parties to a contract can only obtain this standard via the responsible procurement department.
©
©©
© VOLKSWAGEN AG
Norm vor Anwendung auf Aktualität prüfen / Check standard for current issue prior to usage.
Form FE 41 -12,00
Changes
The following changes have been made as compared to the first issue:
�
standard completely revised
Previous Issues
06.93
Contents
1
Scope ................................................................................................................................2
2
Definitions..........................................................................................................................2
3
Requirements ....................................................................................................................2
4
Functional requirements ....................................................................................................3
4.1
CAD drawings....................................................................................................................3
4.1.1
Drawing frames .................................................................................................................3
4.1.2
Title block ..........................................................................................................................3
4.1.3
Limit frame.........................................................................................................................4
4.2
3D models in general.........................................................................................................5
4.2.1
Vehicle coordinate system.................................................................................................5
4.2.2
Cast blanks........................................................................................................................5
4.3
3D models – surface models .............................................................................................6
4.3.1
Material thickness..............................................................................................................6
4.3.2
Bore, bore depth and bore axis..........................................................................................6
4.3.3
Holes, passages, edge finishes .........................................................................................7
4.3.4
Spot welds and welded seams...........................................................................................7
4.3.5
Trim contours and edge contours for panels......................................................................8
5
Referenced Standards.......................................................................................................9
The English translation is believed to be accurate. In case
of discrepancies the German version shall govern.


### 第 2 页
Page 2
VW  010 59-1: 2001-01
1 
Scope
This standard is valid for all departments of the VOLKSWAGEN Group that generate, modify,
receive or transmit CAD model data.
2 
Definitions
CAD/CAM
Computer Aided Design/Computer Aided Manufacturing
Model data
Geometric, technical and administrative information for the description
of a product
CATIA
CAD/CAM main system for the process sequences “body” and
“electrics”
Pro/ENGINEER
CAD/CAM basic system for the process sequence “units”
IGES
Initial Graphics Exchange Specification for the transmission of design
models; see NISTIR 4412
VDAIS
Acronym (VDA IGES Subset) for the specification of an IGES subset;
see VDMA/VDA 66 319
STEP
Standard for the exchange of product model data; ISO 103 03 series
of standards
3 
Requirements
The CAD/CAM model (dimensional description and engineering technology including technical
documentation) is the basis for all activities in the process sequence.
Detailed requirements for a CAD/CAM data exchange are specified in the process sequence for
each individual project. Existing process sequences shall not be interrupted. On-time change
service is performed with CAD.
The CAD/CAM model shall be kept as small as possible. It shall not contain redundant data.
Design models shall exactly correspond to the actual dimension (nominal dimension) of the product
to be specified. Nominal dimensions shall comply with the design model (except for drawing
representation of standard parts).
All CAD/CAM models shall have a logical structure. If layers are used, layers No. 0 to 254 shall be
used exclusively. The structure is specified acc. to process sequence and division in other parts of
this standard.
VW 010 59 – 3
CATIA for vehicle parts
VW 010 59 – 3, Appendix 1
Process Chain Specific Layer Assignment
VW 010 59 – 4
CATIA for operating equipment
VW 010 59 – 5
Pro/ENGINEER
Neutral data formats shall list the copyright note in the commentary field of the exchange header.
Part number, change date and title of the CAD model shall comply with the drawing information.


### 第 3 页
Page 3
VW  010 59-1: 2001-01
4 
Functional requirements
4.1 
CAD drawings
For CAD drawings the standards to be observed for the creation of drawings continue to be valid.
Especially the following requirements must be met:
VW 010 14
Drawing Frames and Text Macros
VW 010 50
Drawings – scale, lines, hatching, dash-dotted lines
VW 010 52
Drawings – representation
VW 010 54
Drawings – dimensioning
VW 010 55
Reference point system – RPS – drawings
VW 010 56
Drawings – Form and Position Tolerances
VW 010 58
Drawings – Drawing Entries
VW 13 705
Representation of surface finish
CAD systems shall be configured in such a way that the requirements specified in the VW
standards can be complied with.
4.1.1 
Drawing frames
The lower left trim edge corner shall serve as the reference point for the CAD drawing frame of
Volkswagen vehicle parts with the formats A3 to A0 and A0plus.
4.1.2 
Title block
VW 010 58 specifies the drawing entries. Additional specification for CAD drawings:
CAD drawings do not carry any signature.
The drawing verification, which is still carried out, is no longer documented on the drawing.
CAD system and administration system code (part number, product data type, status of
development) shall be entered in the line “CAD-System und Verwaltungssystem-Schlüssel”, in
order to ensure that plots can be clearly assigned. The entry can also be made electronically
(automatically when plotting).
If the CAD process sequence is interrupted, the content in the "CAD-System und
Verwaltungssystem-Schlüssel" field shall be deleted.
The same rules apply to ENT drawings (layout) and PDM drawings (product detail mounting
instructions).


### 第 4 页
Page 4
VW  010 59-1: 2001-01
4.1.3 
Limit frame
For data exchange between different CAD systems using the neutral IGES or VDAIS data format,
the model views shall be limited. The element in the IGES/VDAIS specification transmitting this
limit is termed “drawing”.
In the same manner as the individual model views the entire drawing frame shall be inserted into
such a drawing (see Figure 1).
Figure 1 – Views and drawing frames with drawing
Note: The terms “view” and “drawing” have been utilized as they are defined in the IGES
specification. The corresponding terms in the CAD systems or in the interface specification STEP
may be different (Example: CAD system CATIA: drawing = VIEW FRAME).
Views
Drawing
Drawing
frame


### 第 5 页
Page 5
VW  010 59-1: 2001-01
4.2 
3D models in general
4.2.1 
Vehicle coordinate system
The design of vehicle parts is in principle based on the vehicle or engine coordinate system
(VW 010 52).
Local coordinate systems with fixed reference to the vehicle coordinate system are permissible for
components which are not tied to one model (engine, gearbox, chassis, ...). The assignment to the
relevant vehicle coordinate system shall predominantly be found in the ASSY (assembly).
Additional and local coordinate systems (machine-dependent) are permissible for CAM procedures
(e.g. NC, measuring technology).
4.2.2 
Cast blanks
Blank models are designed without dimensional tolerances using the blank nominal dimensions,
including machining allowance. If necessary, machining allowance should be stored on a separate
layer.
The cast blank CAD/CAM model shall contain mold separation, direction of mold separation and
work levels. They are stored on a separate layer in the CAD system CATIA (see VW 010 59-3
Appendix 1).
The direction of mold separation shall be identified on the mold separation using 3 elements (foot,
head and connecting line) (Figure 2). The line is 100mm long.
The surface model of a cast blank shall have a structure which corresponds to the mold separation,
i.e., beginning from the mold separation, the core shall consist of draft angles in the direction of
mold separation.
Entformungsrichtung
Formteilung
Figure 2 - Entry of mold separation and direction of mold separation
direction of mold separation
mold separation


### 第 6 页
Page 6
VW  010 59-1: 2001-01
4.3 
3D models – surface models
4.3.1 
Material thickness
In a CAD/CAM model, material thickness is specified using a line perpendicular to the designed
surface which has the length of material thickness x 100; its foot lies in the designed surface
(Figure 3). This line is stored on a separate layer in the CAD system CATIA (see VW 010 59-3
Appendix 1).
For this, one suitable reference surface shall be chosen.
Figure 3 - Representation of material thickness
4.3.2 
Bore, bore depth and bore axis
For describing bores in surface models, the center point (where the axis brakes through the
surface), the axis and a circle with nominal diameter on the surface shall be exchanged.
Bore depth shall be specified by the center point and a second point on the bore axis.
perpendicular to the surface


### 第 7 页
Page 7
VW  010 59-1: 2001-01
4.3.3 
Holes, passages, edge finishes
Holes without collar and clearance cuts shall be described with a 3D contour on the surface.
For holes with collar, the collar shall be described, the same applies to indentations.
Holes without collar and clearance cuts shall be described using faces. The surfaces must be
blanked.
Chamfers (VW 010 73) and/or edge finishes (VW 010 88) shall be clearly represented or, if
necessary, exchanged additionally.
4.3.4 
Spot welds and welded seams
In 3D CAD models, the position of each spot weld shall be identified with two elements (line and
symbol, see Figure 4).The line shall be exactly perpendicular to the surface. The length of the line
is 3 x dPmin (minimum diameter of the spot weld dPmin acc. to VW 011 05-1) on both sides of the
point of penetration through the surface.For two-sheet and three-sheet welding the same symbol is
used (Figure 4).
Figure 4 - Identification of a spot weld in 3D CAD models
diameter of the symbol = 4mm
Symbol for two- and three-
sheet welding


### 第 8 页
Page 8
VW  010 59-1: 2001-01
4.3.5 
Trim contours and edge contours for panels
The trim contour represents the edge contour of the face (Figure 5).
Edge contours of a group of surfaces and/or trim contours shall be structured in addition to the
base surface.
Figure 5 – Trim contour


### 第 9 页
Page 9
VW  010 59-1: 2001-01
5 
Referenced Standards
VDA 4950
CA data exchange
VDA 4955
Scope and quality of CAD/CAM data
VDMA/VDA 66 318
Rules for CAD data exchange
VDMA/VDA 66 319
Specification of an IGES subset (VDA IS); basic model and 
dimensioning
VW 010 14
Drawing Frames and Text Macros
VW 010 50
Drawings – scale, lines, hatching, dash-dotted lines
VW 010 52
Drawings – representation
VW 010 54
Drawings – dimensioning
VW 010 55
Reference point system – RPS – drawings
VW 010 56
Drawings – Form and Position Tolerances
VW 010 58
Drawings – Drawing Entries
VW 010 59-2
Data Quality
VW 010 59-3
CATIA for vehicle parts
VW 010 59-3 Appendix 1
Process Chain Specific Layer Assignment
VW 010 59-4
CATIA for operating equipment
VW 010 59-5
Pro/ENGINEER
VW 010 73
Radii and chamfers
VW 010 88
Edge finishes, drawing specifications
VW 011 05 (series of standards)
Spot welding
VW 13 705
Representation of surface finish

