# VW_01059_5_DE_2015-04_CAD&CAM数据要求_第5部分：CAD系统PTC Creo参数.pdf

## 文档信息
- 标题：
- 作者：
- 页数：29

## 文档内容
### 第 1 页
Konzernnorm
VW 01059-5
Ausgabe 2015-04
Klass.-Nr.:
22632
Schlagwörter:
CAD, CAM, Anforderung, Creo Elements/Pro, Pro/ENGINEER, CAD-System, Pro/E, Creo, Creo
Parametric
Anforderungen an CAD/CAM-Daten
CAD-System PTC Creo Parametric
Frühere Ausgaben
VW 01059-5: 2000-07, 2001-05, 2003-07, 2004-02, 2005-07, 2009-11, 2012-06
Änderungen
Gegenüber der VW 01059-5: 2012-06 wurden folgende Änderungen vorgenommen:
–
CAD-Systemname von Creo Elements/Pro in PTC Creo Parametric (kurz: Creo) geändert
–
Fachverantwortung geändert
–
Vorwort entfernt
–
Abschnitt 2: Begriff<PERSON> „Creo“, „ECA“, „Full Check“, „KSE“, „KVS“, „PDA“, „Quick Check“ und
„ZP7“ hinzugefügt; „ProRepair“ entfernt; „VW ModelCheck“ – Begriffserklärung angepasst,
Schreibweise im ganzen Dokument geändert; „sub-part“ kleingeschrieben und im ganzen Do‐
kument geändert
–
Abschnitt 3.1: Pfad zur Lieferantenplattform angepasst
–
Abschnitt 3.2: „ModelCHECK Volkswagen Konfiguration Set“ entfernt; Konzern-Konfigurations‐
dateien „din.dtl“ um „(bis Release GRI 09.00.02)“ und „vw_din.dtl“ um „(ab Release GRI
09.01.00)“ ergänzt; Verweis auf VW 01098 zugefügt; Anwendung der Applikation DoLittle ge‐
nerell vorgeschrieben; ANMERKUNG 1 entfernt; Verwendung von „Flexible Modellng“ verbo‐
ten, die beiden letzten Sätze hinzugefügt
–
Abschnitt 3.3.2: Begriffe „Features“, „Datums“, „Skeleton“ und „Templates“ beginnen mit Groß‐
buchstaben
–
Abschnitt 3.3.3: Verweis auf VW 01098 zugefügt; Tabelle 1: das Wort „benennung“ groß ge‐
schrieben; das Wort „Benennung“ in den Zeilen „Zeichnungsformat“ und „User Defined Fea‐
ture“ hinzugefügt; in der Legende für Tabelle 1: bei „Benennung“ Text in Klammer von [-, _]
nach [-_] geändert; bei „c“ Wörter „part“ und „assembly“ kursiv geschrieben, bei „firma“ Abkür‐
zung „co“ auf „po“ korrigiert
–
Abschnitt 3.3.4: Wort „Varianten“ vor dem Wort „instances“ im Text hinzugefügt;
Norm vor Anwendung auf Aktualität prüfen.
Die elektronisch erzeugte Norm ist authentisch und gilt ohne Unterschrift.
Seite 1 von 29
Fachverantwortung
Normung
K-SIPE-2/2
Thomas Reineke
Tel.: +49 5361 9-49464
EKDV/4 Norbert Wisla
EKDV
Tel.: +49 5361 9-48869
Maik Gummert
Alle Rechte vorbehalten. Weitergabe oder Vervielfältigung ohne vorherige Zustimmung einer Normenabteilung des Volkswagen Konzerns nicht gestattet.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Seite 2
VW 01059-5: 2015-04
–
Abschnitt 3.3.6: „Default-Layer“ und zugehörige Texte entfernt
–
Abschnitt 3.4.1: Anmerkung 2 entfernt und als normalen Text eingeführt
–
Abschnitt 3.4.2: erster Satz: Wort „(instances)“ zugefügt; zweiter Satz: Wort „Instanzen“ durch
„instances“ ersetzt
–
Abschnitt 3.4.4: Begriff „Default-Layer“ durch „Template-Layer“ ersetzt; Bild 6 geändert; Forde‐
rung um Gewinde auf den Layer „_THREADS“ zu platzieren entfernt; Forderung „Für Templa‐
te- und Default-Layer dürfen keine Layer Rules definiert werden“ entfernt; „Default-Layer“ auf
„Template-Layer“ geändert; Satz „Leere Benutzer-Layer sind zu löschen“ hinzugefügt
–
Abschnitt 3.5.1: Schreibweise von „GTOL Datums“ auf „Gtol datums“ geändert;
–
Abschnitt 3.5.1.1: Schreibweise von „ModPar“ auf „Modpar“ geändert; dritter und fünfter Satz
und Tabellen 2 bis 4 in Anhang A „Parameter“ versetzt; SEMI_FINISH
–
Abschnitt 3.5.1.2: letzter Satz hinzugefügt
–
Abschnitt 3.5.1.3: Text „Insert-Annotations-Notes-New“ entfernt
–
Abschnitt 3.5.1.5 „Tolerierungsgrundsatz nach VW 01054“ geändert
–
Abschnitt 3.5.1.6: DoLittle Anwendung von optional auf obligatorisch geändert
–
Abschnitt 3.5.1.7: Überschrift von „Patterns“ auf „Muster“ umbenannt; Text „oder geometry
pattern“ zugefügt
–
Abschnitt 3.5.2: Text „Bei einfachen parts“ auf „Bei einfachen Bauteilen (parts)“ geändert
–
Abschnitt *******: dritter Absatz, Text „Insert - Shared Data - Copy Geometry...“ durch „Model;
Copy Geometry“ ersetzt
–
Abschnitt *******: Abkürzung „(ZKG)“ zugefügt
–
Abschnitt 5: erster Satz, Text „Creo Elements/Pro-Format“ durch „PTC Creo Parametric-For‐
mat“ ersetzt; zweiter Satz Text „Creo Elements/Pro-Parameter“ durch „Creo-Parameter“ er‐
setzt; sechster Absatz, Text „Annotate; Edit;“ zugefügt; Begriffe „Tools -> Overlay“ durch
„Layout -> Overlay“ und „Utilities“ durch „Tools“ ersetzt, Text „ModelCHECK“ auf „VW Model‐
Check“ geändert; Schreibweise, Dateityp: „.ASM“ auf „.asm“ geändert; Absatz mit dem Text “
Die Daten sind mit den für die Volkswagen AG konzernweit empfohlenen Prüfprogrammen...”
entfernt und in Abschnitt 5.1 eingeführt; Text „Copy‐Geom-independent auf „Copy Geometry-
independent“ geändert; die beiden letzten Absätze und Bild 8 hinzugefügt
–
Abschnitt 5.1 „Verwendung von VW ModelCheck“ hinzugefügt
–
Abschnitt 5.2 „Unzulässige Meldungen bei der Regenerierung“ hinzugefügt
–
Abschnitt 6: Begriff (BEMI) in den Überschriften durchgängig zugefügt
–
Abschnitt 6.4.1: Text „Volkswagen AG GRI-Templates“ auf „Volkswagen AG Betriebsmittel-
GRI-Templates“ geändert
–
Abschnitt 6.4.2 „Maßtoleranzen (BEMI)“ geändert
–
Abschnitt 6.4.3 „Werkstückkanten (BEMI)“ hinzugefügt
–
Abschnitt 6.4.4 „Form- und Lagetoleranzen (BEMI)“ geändert
–
Abschnitt 7 „Mitgeltende Unterlagen“ aktualisiert
–
Anhang A „Parameter“ hinzugefügt; Tabelle A.1 sortiert alphabetisch nach „Parameter Name“;
Parameter Nr. 14 „VW_NAME_ADDITION“ und Nr. 27 „VW_TITLE_ADDITION“ hinzugefügt;
Parameter Nr. 20 „RESP_DEVELOP“ neue Marken (MN, SC, DU) ergänzt; alte Zeilen Nr. 38
„VW_SEMI_FINISH_PD_ID“, Nr. 39 „VW_SURF_PROTECTION_ID“ und Nr. 75
„WEIGHT_CALC“ und zugehörige Fußnote e) entfernt; Zeile Nr. 77 „VW_USE_RESTRICTI‐
ON“ hinzugefügt; zwei Sätze zulässige Werte für den Parameter „NAME_PREFIX“ und „TI‐
TLE_PREFIX“ über Tabelle A.2 hinzugefügt; Schreibweise von „SEMI_FINISH“ auf „SEMI FI‐
NISH“ geändert


### 第 3 页
Seite 3
VW 01059-5: 2015-04
–
alter Anhang B entfernt
–
Anhang B; verweise auf Tabellen B.1 bis B.4 hinzugefügt; Tabelle B.1 „Regelgewinde nach
VW 11610“: Zeilen mit M7 × 1.25 und M16 entfernt; Kernlochdurchmesser bei M8 von 6.70
mm auf 6.75 mm geändert; Tabelle B.2 „Feingewinde nach VW 11610“: Zeilen mit M4 × 0.50,
M 5 × 0.50, M9 × 0.75 entfernt; Kernlochdurchmesser bei M6 von 5.20 mm auf 5.25 mm, M7
von 6.20 mm auf 6.25 mm, M8 von 7.20 mm auf 7.25 mm, M10 von 9.20 mm auf 9.25 mm und
M10 × 1.25 von 8.80 mm auf 8.75 mm geändert; Tabelle B.3 „Festsitzgewinde nach VW
11516“: Zeile mit M9 × 0.75 entfernt, Kernlochdurchmesser bei M12 × 1.50 von 8.20 mm nach
10.25 mm geändert
Inhalt
Seite
Anwendungsbereich ................................................................................................... 4
Begriffe ....................................................................................................................... 4
Anforderungen ........................................................................................................... 7
Systemvoraussetzungen ............................................................................................ 7
Vorgaben .................................................................................................................... 7
Konventionen ............................................................................................................. 8
Koordinatensysteme .................................................................................................. 8
Namenskonventionen für Features und Datums ...................................................... 10
Namenskonventionen für Dateinamen von part, assembly, drawing ....................... 10
Namenskonventionen für Family Tables .................................................................. 12
Namenskonventionen für Simplified Representation ............................................... 12
Konventionen für Layer ............................................................................................ 12
Struktur der Modelle ................................................................................................. 13
Skelett ...................................................................................................................... 13
Verwendung von Family Tables ............................................................................... 13
Modellstruktur von DMU-Parts ................................................................................. 13
Verwendung von Layern .......................................................................................... 13
Modellaufbau ............................................................................................................ 14
Grundlagen .............................................................................................................. 14
Parameter ................................................................................................................ 14
Referenzen ............................................................................................................... 15
Features ................................................................................................................... 15
Maßtoleranzen, Form- und Lagetoleranzen ............................................................. 15
Tolerierungsgrundsatz nach VW 01054 ................................................................... 15
Bauteilinformationen ................................................................................................ 15
Muster ...................................................................................................................... 15
Variable Rounds ....................................................................................................... 15
Modellierungsmethodik ............................................................................................ 16
Skelett für komplexe Bauteile ................................................................................... 16
Kerne, Körper, Rippen ............................................................................................. 17
Rohteil, Fertigteil, Funktionsteil ................................................................................ 17
Dokumentation ......................................................................................................... 18
Datenqualität ............................................................................................................ 18
Verwendung von VW ModelCheck .......................................................................... 19
Unzulässige Meldungen bei der Regenerierung ...................................................... 20
Regelungen für die Betriebsmittelkonstruktion (BEMI) ............................................ 20
Anwendungsbereich (BEMI) .................................................................................... 20
Vorgaben (BEMI) ..................................................................................................... 20
Anforderungen (BEMI) ............................................................................................. 21
1
2
3
3.1
3.2
3.3
3.3.1
3.3.2
3.3.3
3.3.4
3.3.5
3.3.6
3.4
3.4.1
3.4.2
3.4.3
3.4.4
3.5
3.5.1
3.5.1.1
3.5.1.2
3.5.1.3
3.5.1.4
3.5.1.5
3.5.1.6
3.5.1.7
3.5.1.8
3.5.2
*******
*******
*******
4
5
5.1
5.2
6
6.1
6.2
6.3


### 第 4 页
Seite 4
VW 01059-5: 2015-04
Namenskonventionen für part, assembly, drawing (BEMI) ...................................... 21
Modellaufbau (BEMI) ............................................................................................... 22
Grundlagen (BEMI) .................................................................................................. 22
Maßtoleranzen (BEMI) ............................................................................................. 22
Werkstückkanten (BEMI) ......................................................................................... 23
Form- und Lagetoleranzen (BEMI) ........................................................................... 23
Verwendung von family tables (BEMI) ..................................................................... 23
Bearbeitungsangaben (BEMI) .................................................................................. 23
Mitgeltende Unterlagen ............................................................................................ 23
Parameter ................................................................................................................ 24
.................................................................................................................................. 28
6.3.1
6.4
6.4.1
6.4.2
6.4.3
6.4.4
6.4.5
6.4.6
7
Anhang A
Anhang B
Anwendungsbereich
Für die interne und externe Zusammenarbeit von CAD/CAM-Partnern entlang der Prozessketten
gilt die Norm VW 01059-1. Darauf aufbauend beschreibt dieser Teil 5 der Norm weitergehende
Festlegungen für die Konstruktion von Fahrzeugteilen mit dem CAD-System PTC Creo Parametric
(in diesem Dokument kurz Creo bezeichnet). Sie ist Bestandteil jedes Vertrages über die Vergabe
von Aufgaben der Fertigung oder Konstruktion von Fahrzeugteilen an Entwicklungspartner.
Besondere Regelungen für die Betriebsmittelkonstruktion siehe Abschnitt 6.
Abweichungen von dieser Norm sind in Ausnahmefällen möglich, müssen aber zwischen den Ent‐
wicklungspartnern schriftlich vereinbart werden und dürfen keine negativen Auswirkungen auf Ar‐
chivierung, Konvertierung und Creo-Prozesskette haben. Für die effiziente Modellverwendung in
der Prozesskette (Berechnung, Fertigung usw.) ist die teileabhängige Spezifikation im Lastenheft
und die Abstimmung mit den Prozesskettenpartnern vor Konstruktionsbeginn vom auftraggeben‐
den Konstrukteur zu erstellen.
Begriffe
Creo-spezifische Begriffe sind in dieser Norm kursiv geschrieben.
Befehle und Funktionen sind zusätzlich in Hochkommata gestellt.
Altdaten
Alle Daten, die vor Einführung von Pro/ENGINEER Wildfire 2.0 (vor dem
18.07.2005) erstellt wurden.
 
Applikation
Hilfsprogramm auf Basis einer API für Creo.
 
Betriebsmittel/BEMI
Betriebsmittel (BEMI) ist eine zusammenfassende Bezeichnung für Anla‐
gen, Geräte, Einrichtungen, Werkzeuge, Vorrichtungen, Messzeuge, Prüf‐
mittel einschließlich maschinen- und vorrichtungsgebundener Verschleiß‐
teile, die zur betrieblichen Leistungserstellung (Fertigung) dienen.
 
BIN-Part
Ein spezielles Skelett für komplexe Gussbauteile. Es wird überwiegend zur
Konstruktion von Zylinderköpfen, Getriebegehäusen und Zylinderkurbelge‐
häusen eingesetzt. Die Arbeitsweise mit BIN-Parts ist der mit Skeleton Mo‐
dels sehr ähnlich und wird hier zur Information kurz beschrieben. Beides
sind Teile, die Ebenen, Punkte, Achsen und Koordinatensysteme enthalten.
Der Unterschied besteht darin, dass BIN-Parts zusätzlich viele topologische
Informationen bis hin zu komplexeren Flächen enthalten. Hierüber wird
sichergestellt, dass aus dem Zusammenspiel mehrerer Kerne, Schieber
1  
2  


### 第 5 页
Seite 5
VW 01059-5: 2015-04
oder Werkzeugflächen ein korrektes Gussbauteil entsteht. Für die Schnitt‐
stellengeometrie an diesen Bauteilen ist auch hier das Skeleton Model Bin‐
deglied zwischen dem BIN-Part und den anderen Bauteilen. Es wird überall
eingesetzt, definiert aber im Wesentlichen Schnittstellen und Einbaubedin‐
gungen zwischen einzelnen Teilen in der Baugruppe.
 
Creo
Kurzbezeichnung für PTC Creo Parametric
 
Digital Mock Up-Part
(DMU-Part)
Eigenes Creo-Bauteil mit reduziertem Datenvolumen zur weiteren Verwen‐
dung im DMU-Prozess. Die Reduktion des Datenvolumens wird durch das
Erzeugen eines Volumenkörpers oder einer Hüllengeometrie erreicht, der
primär aus Kopien der Bauraum bestimmenden, äußeren Flächen des Bau‐
teils besteht (zum Beispiel wird die beschreibende Geometrie innenliegen‐
der Kerne vernachlässigt). Das DMU-Part referenziert auf entsprechend
publizierte Geometrie des Basisbauteils (z. B. Fertigteil oder Baugruppe).
Ein DMU-Part muss für jedes DMU-relevante Bauteil bzw. Baugruppe an‐
gefertigt werden. Dies sind im Allgemeinen alle Bauraum bestimmenden
Bauteile.
 
DoLittle
DoLittle ist eine in die CAD-Umgebung integrierte Applikation, die die on‐
line-Erstellung zweisprachiger Notizen durch das EKDD/5-Übersetzerteam
ermöglicht.
 
ECA
Das ECA (Engineering Center Aggregate) ist ein PDM (Produkt-Daten-Ma‐
nagement) -System der Aggregate-Entwicklung.
Ziel des ECA ist die durchgängige Unterstützung der Aggregate-Entwick‐
lung von der frühen Phase bis zur Serienbaustufe.
Mit dem ECA werden sowohl CAD- als auch Produktstrukturen erzeugt und
verwaltet. Produktstrukturen können im ECA für die Abwicklung von Baulo‐
sen (Aufbau, Bestellung und Verfolgung von Motorstücklisten) genutzt
werden, CAD-Strukturen stellen die geometrische Repräsentation eines
Aggregates dar.
 
Einzelteil
Das physikalische Bauteil entsprechend Stückliste (nicht das CAD-Modell).
 
Fahrzeugteil
Ein Teil, das im Fahrzeug verbaut wird im Unterschied zu Betriebsmitteln.
 
Fertigteil
Das Modell des Fertigteils ist das um die mechanische Bearbeitung erwei‐
terte Modell des Rohteils.
 
FuBIT
Eine Applikation, mit deren Hilfe Bohrungen und Flächen an komplexen
Geometrien, wie zum Beispiel an Aggregateteilen, fertigungsgerecht er‐
zeugt und bemaßt werden.
 
Full Check
Eine VW ModelCheck-Prüfung mit Regenerierung der 3D-Modelle und 2D-
Zeichnungen.
 
Funktionsteil
Das Modell des Funktionsteils ist ein unverrundetes Rohteil mit allen Form‐
schrägen und Formtrennungen.
 


### 第 6 页
Seite 6
VW 01059-5: 2015-04
Group Reference In‐
stallation (GRI)
Einheitliche Creo-Installation im Volkswagen Konzern.
 
KSE
Konstruktionsstammdatenerfassung
 
Komplexes Bau‐
teil/sub-part
Bauteil, das sinnvoller Weise durch mehrere sub-parts beschrieben wird,
welche unter Verwendung von assoziativen Kopien von Bezügen und Geo‐
metrien erstellt werden. Z. B. für Motoren sind dies üblicherweise Bauteile
wie Zylinderkopf, Zylinderkurbelgehäuse, Ölwannenoberteil, Saugrohr. In
Creo wird dies auch Skeletontechnik oder BIN-Parttechnik genannt.
 
KVS
HyperKVS, Konstruktionsdatenverwaltungssystem
 
Modpar
Eine Applikation, die Datenbank-gestütztes Editieren und Pflegen von
CAD-Stammdaten sowie die Pflege der Änderungstabelle ermöglicht.
 
OLE-Objekt
Ein Objekt aus einer anderen Anwendung (z. B. WORD-Dokument, EX‐
CEL-Tabelle)
 
Part
Ein Bauteil, das mit Creo erzeugt wird.
 
PDA
Produktdatenart
 
Quick Check
Eine VW ModelCheck-Prüfung ohne Regenerierung der 3D-Modelle und
2D-Zeichnungen.
 
Rohteil
Beschreibt das physische Rohteil (ohne mechanische Bearbeitung, siehe
auch „Fertigteil“). Das Rohteil ist das Basisbauteil, dessen Geometrie (unter
Verwendung einer assoziativen Kopie der Geometrie) von dem Fertigteil re‐
ferenziert wird.
 
Simplified
Representation
Vereinfachte Darstellung
 
Skelett, Skeleton Mo‐
del
Referenzteil, Skeleton, Gerippe, Zusammenbauskelett, Bewegungsskelett,
BIN-Part.
 
Teil
Ein Teil (Einzelteil) ist ein Objekt, für dessen weitere Aufgliederung aus
Sicht des Anwenders kein Bedürfnis besteht oder das nicht zerstörungsfrei
zerlegt werden kann.
 
Template
Start-Vorlage
 
Tool
Software-Werkzeug
 
VW ModelCheck
Eine Applikation zur Qualitätsprüfung von Creo Daten. Der erzeugte Zu‐
sammenfassungsbericht kann in Form einer PDF-Datei gespeichert, ge‐
druckt und als Übergabeprotokoll verwendet werden.
ZP7
Zählpunkt Sieben „Ende Wagenmontage“


### 第 7 页
Seite 7
VW 01059-5: 2015-04
Anforderungen
Systemvoraussetzungen
Informationen über die aktuell im Volkswagen Konzern eingesetzte Version von Creo und Zusat‐
zapplikationen sind für Entwicklungspartner der Lieferantenplattform http://www.vwgroupsup‐
ply.com, Rubrik „Informationen“ >> „Geschäftsbereiche“ >> „Forschung und Entwicklung“ >> „F&E
Dienstleistungen“, Menüpunkt „Creo“ zu entnehmen.
Vorgaben
Die aktuellen GRI-Templates, Konfigurationsdateien und erforderliche Applikationen sind von den
Entwicklungspartnern der Lieferantenplattform http://www.vwgroupsupply.com zu entnehmen.
Alle Daten sind ausschließlich mit absoluter Genauigkeit 0,010 mm zu erstellen. Neukonstruktio‐
nen, die auf Altdaten mit relativer Genauigkeit basieren, sind auf absolute Genauigkeit umzustel‐
len.
Als Startmodell einer Creo-Konstruktion ist immer ein GRI-Template der Volkswagen AG zu benut‐
zen. Zeichnungen sind auf einem Volkswagen AG GRI-Template aufzubauen.
Zum Ausfüllen der Modell-Parameter (Stammdaten) und zum Erstellen/Pflegen der revision table
(Änderungsspiegel) ist die Applikation Modpar zu verwenden (siehe Abschnitt 3.5.1.1).
Folgende Konzern-Konfigurationsdateien müssen benutzt werden:
–
config.sup
–
config.pro
–
din.dtl (bis Release GRI 09.00.02)
–
vw_din.dtl (ab Release GRI 09.01.00)
–
Textfont „vw_font“ gemäß Volkswagen AG-Vorgabe.
Jegliche Benennungen (Dateien, Simplified Representations, Parameter, Layer-Namen, View-Na‐
men, Feature-Namen, Relations usw.) dürfen nur Buchstaben, Ziffern, Bindestrich und Unterstrich
enthalten [A bis Z, 0 bis 9, -, _ ]. Leerzeichen, Umlaute und Sonderzeichen sind nicht zulässig.
Leerzeichen sind durch Unterstriche zu ersetzen und Punkte durch Bindestrich. Für Teilnummern
(VW 01098) im Dateinamen gilt abweichend Abschnitt 3.3.3.
Im Deutschen ist der Buchstabe „ß“ durch „ss“ zu ersetzen (Ausnahme: „Masze“, um eine Ver‐
wechslung mit Masse zu verhindern).
Zur Zeichnungserstellung sind die parametrischen Zeichnungsrahmen der Volkswagen AG nach
VW 01014 sowie die bereitgestellten Tabellen und Symbole zu verwenden. Zu den Tabellen und
Symbolen (Oberflächenzeichen) ist eine Übersichtszeichnung vorhanden, aufzurufen über „GRI-
MKs; Symbols Overview“ bzw. „GRI-MKs; Tables Overview“.
Zweisprachige CAD-Modelle und -Zeichnungen in Deutsch und Englisch sind mit der Applikation
DoLittle auszuführen. DoLittle unterstützt bei der Erstellung mehrsprachiger CAD-Konstruktionen
auf Basis eines standardisierten, VW-spezifischen Textkataloges. Änderungen der mit DoLittle er‐
stellten Textinhalte dürfen nur über das Team Technische Übersetzungen, EKDD/5, abgewickelt
werden. Änderungen dieser Textinhalte ohne Verwendung von DoLittle sind nicht zulässig.
Das Einbetten von OLE-Objekten in Zeichnungen oder Modellen ist untersagt.
Bauteile und Baugruppen dürfen nicht mit Schnitt in der Darstellung gespeichert werden.
3  
3.1  
3.2  


### 第 8 页
Seite 8
VW 01059-5: 2015-04
Die Konzernsprache für Creo ist Englisch. Es ist nur die Verwendung der englischen Sprachumge‐
bung zulässig, um die Sicherstellung der reibungslosen Arbeit mit der GRI-Konfiguration zu ge‐
währleisten.
Die Verwendung von Flexible Modeling ist nicht gestattet.
VW ModelCheck prüft die Einhaltung der Forderungen dieser Norm. Ohne eine „OK“-Datenquali‐
tätsprüfung mit VW ModelCheck und passendem Prüfprofil ist ein persistentes Speichern in be‐
stimmten PDA im KVS nicht möglich.
Konventionen
Koordinatensysteme
Es werden nur rechtsdrehende Koordinatensysteme (siehe Bild 5) verwendet. Die Konstruktion er‐
folgt grundsätzlich bezogen auf eines der folgenden Koordinatensysteme:
–
Das Fahrzeugkoordinatensystem (Koordinatensystem Karosserie, KSK) entsprechend
VW 01052,
–
das Koordinatensystem Motor (KSM),
–
das Koordinatensystem Getriebe (KSG),
–
das Koordinatensystem Vorderachsgetriebe (KSV),
–
das Koordinatensystem Verteilergetriebe (KST),
–
das Koordinatensystem Hinterachsgetriebe (KSH).
Für nicht typgebundene oder mehrfach eingebaute Bauteile erfolgt die Konstruktion bezogen auf
ein lokales Koordinatensystem, siehe Bild 1, Bild 2, Bild 3 und Bild 4.
Bild 1 – Lage des Motor- und Getriebekoordinatensystems und Ursprung der Positionierung des
Triebsatzes
3.3  
3.3.1  


### 第 9 页
Seite 9
VW 01059-5: 2015-04
Bild 2 – Beispiel Quereinbau
Bild 3 – Fahrzeug‐
koordinatensystem
Bild 4 – Beispiel Längseinbau
Der Ursprung des Motor-Koordinatensystems KSM, siehe Bild 1, Bild 2 und Bild 4, liegt im Schnitt‐
punkt der Hauptachse der Kurbelwelle mit der Motorflanschebene des Zylinderkurbelgehäuses.
Die X-Achse liegt auf der Hauptachse der Kurbelwelle und weist in Richtung Motor. Die Z-Achse
steht im nicht eingebauten Zustand senkrecht zur horizontalen Ebene und korrespondiert mit der
Zylinderhauptachse (bei V-Motoren ist es die Winkelhalbierende der Zylinderhauptachsen).
Der Ursprung des Getriebe-Koordinatensystems KSG, siehe Bild 1, Bild 2 und Bild 4, liegt im
Schnittpunkt der Achse der Antriebswelle mit der Getriebeflanschebene. Die X-Achse liegt auf der
Achse der Antriebswelle und weist in Richtung Getriebe. Die Z-Achse steht im nicht eingebauten
Zustand senkrecht zur horizontalen Ebene des Getriebes.
Wird der Triebsatz zum Fahrzeug positioniert, ist das Getriebe-Koordinatensystem KSG zu ver‐
wenden (auch wenn ein Zwischenblech zwischen Motor und Getriebe existiert).
Der Ursprung des Vorderachsgetriebe-Koordinatensystems KSV liegt im Schnittpunkt der senk‐
rechten Ebene durch die Eingangswellen-Achse mit der Differential-Achse. Die positive X-Achse
zeigt in Fahrtrichtung, die Y-Achse zeigt in Fahrtrichtung links und die Z-Achse zeigt nach oben.
Der Ursprung des Verteilergetriebe-Koordinatensystems KST liegt um den Betrag „F“ verschoben
auf der X-Achse des Getriebe-Koordinatensystems. Die Achsausrichtung entspricht der des Ge‐
triebe-Koordinatensystems.
Der Ursprung des Hinterachsgetriebe-Koordinatensystems KSH liegt im Schnittpunkt der senk‐
rechten Ebene durch die Eingangswellen-Achse mit der Differential-Achse. Die positive X-Achse
zeigt entgegen der Fahrtrichtung, die Y-Achse zeigt in Fahrtrichtung rechts und die Z-Achse zeigt
nach oben.
Die Erzeugung dieser Koordinatensysteme darf keine externe Referenz aufweisen.
In parts und assemblies sind die Koordinatensysteme mit sprechenden Namen (z. B. CS_KSK,
CS_KSM, CS_KSG, CS_KSV, CS_KST, CS_KSH) zu versehen, so dass eine logische Zuordnung
erfolgen kann.
In speziellen Creo-Anwendungen der Volkswagen AG, die Koordinatensysteme in Programmen
verarbeiten, können bestimmte vordefinierte Koordinatensystem-Namen verwendet werden.


### 第 10 页
Seite 10
VW 01059-5: 2015-04
Bild 5 – Orientierung der Koordinatensysteme (Rechte-Hand-Regel)
Namenskonventionen für Features und Datums
Features und Datums sind bei funktionaler Relevanz mit sprechenden Namen in Englisch zu ver‐
sehen. In Skeleton-parts/-assemblies sind alle Features bzw. Datums umzubenennen, die den
Features des Templates folgen.
Um Probleme bei der Datenprüfung zu vermeiden, sind Bezugsebenen, die ausschließlich zur
Querschnittserzeugung genutzt werden, mit vorangestelltem XSEC_ zu benennen.
Namenskonventionen für Dateinamen von part, assembly, drawing
Der Dateiname darf kein Datum enthalten.
Das Modell, das vom Dateinamen her der Teilnummer entspricht, muss dem späteren Endzustand
(wie im Fahrzeug verbaut) entsprechen (bei Aggregaten ZP7). Sollte die Darstellung der Zeich‐
nung davon abweichen (z. B. Teil entspannt dargestellt, andere Stückliste/Gestalt durch Um‐
bau-/Umlaufteile, Einbaubeispiele, Gestaltänderung durch ZSB-Bearbeitung), so ist das Zeich‐
nungsmodell durch
–
„drw“
im Dateinamen zu kennzeichnen.
Zeichnungen sollten von Konstruktionsbaugruppen
–
const.asm
(z. B. bei komplexeren Teilen wie Zylinderköpfen, Zylinderkurbelgehäusen) erstellt werden.
Wird die endgültige Gestalt von ZSB-bearbeiteten Teilen nicht durch Baugruppen-Features son‐
dern durch eigene Modelle dargestellt, erhalten diese die Endung
–
pma.prt
Mehrfachzeichnungen tragen im Dateinamen nur die Teilnummer des führenden Teils (obere Teil‐
nummer im Schriftfeld). Creo-Objekte sind zur eindeutigen Identifikation nach der Namenskonven‐
tion gemäß Tabelle 1 zu benennen. Dabei ist auf die in Creo maximal zulässige Namenslänge von
31 Zeichen zu achten.
Es dürfen nur Kleinbuchstaben verwendet werden.
Bei der Benennung sind VW 01058 und VW 01098 zu beachten.
Für die Schreibweise gilt generell:
–
als Gliederungs- und Trennzeichen den Bindestrich [-]
–
als Leerzeichen den Unterstrich [_]
3.3.2  
3.3.3  


### 第 11 页
Seite 11
VW 01059-5: 2015-04
verwenden.
Tabelle 1 – Namenskonvention bei Konstruktion und Entwicklung
Objekt
Entwurf
Vorabstand/Freigabestand
CAD-Modell (prt, asm)
Teil
Teil als assembly
ZSB
Norm-/Wiederholteil Zeichnung (drw)
Benennung_firma_usr_projekt
xxx_yyy_zzz_qqa)-typb)
Teile ohne eigene Teilnummer
Benennung_firma_usr_projekt
xxx_yyy_zzz_qqa)-ca)-erga)-typb)
Fremdteile
Fremdteilnummer
Benennung
Zeichnungssymbol
Benennung
Benennung
Zeichnungsaufkleber (Wiederholtext)
Benennung
Benennung
Zeichnungsformat
Benennung
Benennung
Mark Up
wie Modellname
wie Modellname
Layout
wie Modellname
wie Modellname
User Defined Feature
Benennung
Benennung
Material Tabelle
Wird bei Bedarf festgelegt
 
a)
Optionaler Namensbestandteil. Bei Entfall optionaler Namensbestandteile entfallen auch zugehörige Trennungs- und Leerzeichen.
b)
Die in der Legende genannten Typen sind einzutragen. Wenn ein Part keinem der in der Legende genannten Typen zuzuordnen ist,
entfällt dieser Zusatz.
Legende für Tabelle 1:
erlaubte Zeichen in eckigen Klammern [ ]
Benennung
Teilbenennung: [a-z], [0-9], [-_]
c
component: kennzeichnet eine Komponente (part oder assembly) ohne ei‐
gene Teilnummer (z. B. Kanal eines Saugrohres)
erg
Ergänzung zur Beschreibung von Untermengen, Teilmodellen oder Bau‐
gruppenobjekten: [a-z], [0-9], [-_]; der Bindestrich vor der Ergänzung ist als
Separator unbedingt erforderlich.
firma
entsprechend VW 01099, z. B. au, vw, sk, se, po, la
projekt
intern verwendete Projektbezeichnung, z. B. ab, d3, v65v, 5hp24
qq
Index: [a-z], [0-9]
typ
DMU-Part
dmu (digital mock up)
Skelett-Part
skel (skeleton)
Rohteil
rp (raw part)
Fertigteil
fp (finished part)
Funktionsteil
fcp (function part)
Halbfertigteil
sfp (semi finished part)
Teil im ZSB bearbeitet
pma (part machined in assembly)
Konstruktionsbaugruppe
const (construction)
abgewickelte Bleche
flat# (mit # für eine Ziffer)
Endabwicklung
flat


### 第 12 页
Seite 12
VW 01059-5: 2015-04
vom Endzustand abweichende
Zeichnungsmodelle
drw (drawing)
generische Teile
g (generic model)
usr
Benutzerkürzel
xxx
Fahrzeugklassenschlüssel, ent, tab, wsk, taw, n__ (für Normteile „n“ und
2 Unterstriche): [a-z], [0-9]
yyy
Mittelgruppe, Normteilzählnummer: [0-9]
zzz
Endnummer, Normteilzählnummer: [0-9]
Beispiele zu Tabelle 1:
part (Entwurf)
halter-kliko_au_tnebrr_v65v.prt
part (Vorab-/Freigabestand)
057_103_477_cq.prt
Teil als assembly
059_210_499_af-const.asm
Komponenten ohne Teilnummer
059_210_499_af-c-kanal02.prt
DMU-Part
059_210_555_xy-dmu.prt
Namenskonventionen für Family Tables
Wird eine family table benutzt, gelten für die Benennung der Varianten (instances) die gleichen Re‐
geln wie für normale Modelle. Das generische Modell enthält als Typ „g“ im Dateinamen.
Namenskonventionen für Simplified Representation
Wird eine simplified representation benutzt, dann sind sprechende Namen zu verwenden.
Beispiele:
–
RAWPART
–
TOOLEDPART
–
CALCULATION
–
INSPECTION
–
DMU
Konventionen für Layer
Template-Layer
Template-Layer sind Layer, die im Creo-Startmodell des Volkswagen Konzerns hinterlegt sind. Die
Template-Layer im part, assembly bzw. drawing dürfen keinesfalls gelöscht werden. Die Namen
der Template-Layer beginnen immer mit einem „_“ (Unterstrich).
Benutzer-Layer
Benutzer-Layer sind Layer, die vom Anwender erzeugt werden, um Elemente zuzuordnen. Sie
werden mit sprechenden Bezeichnungen benannt, dürfen jedoch keine kompletten Teilnummern
und Benennungen mit Datum enthalten. Namen der Benutzer-Layer dürfen nicht mit einem „_“ (Un‐
terstrich) beginnen und keine Sonderzeichen im Namen enthalten.
Wird in einer Baugruppe eine Komponente auf einen Layer gelegt, muss dieser Layer mit
„COMP_“ beginnen z. B. „COMP_SKEL“.
Beim Export zu neutralen Schnittstellen (IGES, STEP, ...) dürfen neben dem Layernamen auch
Layer-Id's vergeben werden.
3.3.4  
3.3.5  
3.3.6  


### 第 13 页
Seite 13
VW 01059-5: 2015-04
Struktur der Modelle
Skelett
Große assemblies besitzen ein Skeleton-Model, welches als erste Komponente in das assembly
eingebaut wird. Darauf referenzieren alle nachfolgend eingebauten Komponenten.
Alle Elemente des Skeletts sind mit sprechenden Namen zu versehen (siehe Abschnitt 3.3.2).
Skelette sind mit der Endung
–
-skel.prt oder
–
-bin.prt
zu versehen.
Es wird unterschieden zwischen Skeleton Models und BIN-Parts. BIN-Parts werden meist für kom‐
plexe Gussbauteile genutzt, Skeleton Models für Baugruppen, siehe Abschnitt 2.
Verwendung von Family Tables
Die Verwendung von „*“-Einträgen in Varianten (instances) der Familientabelle ist nicht gestattet.
Je nach Spaltentyp sind entsprechende Werte einzutragen.
Alle instances einer family table sind zu verifizieren.
Modellstruktur von DMU-Parts
Für alle DMU-relevanten parts/assemblies sind DMU-Parts anzufertigen. DMU-Relevanz ist gege‐
ben, wenn die Außenhülle des Bauteils bauraumbestimmend ist.
Beispiele:
Zylinderkopfhaube
ist DMU-relevant
Nockenwelle
ist nicht DMU-relevant
Alle DMU-Parts müssen sich auf ein Motor-, Getriebe- oder Fahrzeug-spezifisches Koordinaten‐
system beziehen, welches projektspezifisch benannt ist.
Für DMU-relevante assemblies ist die simplified representation DMU zu definieren. Hierin sind alle
DMU-relevanten Bauteile enthalten. Alle anderen Teile sind in dieser vereinfachten Darstellung
ausgeschlossen.
Verwendung von Layern
Für komplexe Bauteile ist eine sinnvolle Layerstruktur anzulegen.
Zusätzlich sollte eine Layerstruktur nach funktionell oder fertigungstechnisch zusammen gehören‐
den Features aufgebaut werden (z. B. Layer LAGER_KURBELWELLE für alle Features, die zur
entsprechenden Lagerung gehören; Kernlochbohrungen auf CORE_HOLE_BORE).
Vor einer Datenübergabe müssen bei allen Bauteilen und Baugruppen alle Layer, die nicht Geo‐
metrie-relevant sind, ausgeblendet sein. Dieser Zustand muss explizit gespeichert werden (über
„save status“ speichern und zusätzlich part bzw. assembly speichern).
Es darf grundsätzlich nur mit der Darstellungsoption „hide“ (Ausblenden) und „unhide“ (Anzeigen)
gearbeitet werden. Es darf kein Layer den Status isolated haben. Zu erkennen ist dies an dem
strichpunktierten Rahmen im Layer Tree, siehe Bild 6.
3.4  
3.4.1  
3.4.2  
3.4.3  
3.4.4  


### 第 14 页
Seite 14
VW 01059-5: 2015-04
Bild 6 – Symbol für isolated Layer (nicht verwenden)
Features mit Achsen müssen mindestens auf einem Template-Layer oder einem Benutzer-Layer
liegen.
Leere Benutzer-Layer sind zu löschen.
Beispiel:
Für ein Zylinderkurbelgehäuse, bei dem die Bearbeitungsseiten in Seite 1 bis 6 aufgeteilt sind,
werden die Layer SEITE_1 bis Layer SEITE_6 angelegt. Soll an SEITE_3 gearbeitet werden,
werden alle Seiten-Layer außer SEITE_3 ausgeblendet. Damit sind bei Ansicht der Seite 3 nur
noch die für Seite 3 relevanten Features sichtbar.
Modellaufbau
Grundlagen
Alle CAD-Modelle und Zeichnungen müssen auf der Basis eines Volkswagen AG GRI-Templates
erzeugt werden. Die ersten drei Features im Modell sind Standard-Ebenen. Das vierte Feature ist
das Standard-Koordinatensystem „CS_DRW“. Diese vier Features dürfen weder gelöscht noch ihre
Benennung oder Reihenfolge verändert werden. Die drei Standard-Ebenen dürfen nicht in Bezugs-
Ebenen (Gtol datums) gewandelt oder zur Erzeugung von Querschnitten (Xsections) verwendet
werden.
Für eine rationelle Fertigung sind die metrischen ISO-Gewinde im Anhang B zu verwenden.
Parameter
Zur effektiven Unterstützung von Entwicklungs- und Fertigungsprozessen sind die Parameter des
Templates in erforderlichem Maß mit Werten zu belegen, siehe Anhang A. Hierbei unterstützt den
Anwender die Applikation Modpar.
Zu unterstützende Prozesse sind z. B.:
–
Zeichnungserstellung
–
KSE
3.5  
3.5.1  
3.5.1.1  


### 第 15 页
Seite 15
VW 01059-5: 2015-04
Referenzen
Als Referenzen sind möglichst datum-planes/-axis/-points/-curves bzw. surfaces zu verwenden.
Die referenzierten Bezugselemente sollten möglichst weit vorn in der Entstehungsgeschichte des
parts bzw. assemblies liegen, z. B. Standard-Ebenen, die erste definierte Achse usw.
Wird eine teileübergreifende Modellierungsstrategie mit einem separaten Skelettteil festgelegt, so
sind die im Skelettteil vorhandenen Informationen in die das Gesamtbauteil beschreibenden sub-
parts zu transferieren und bei der Modellierung zu referenzieren. Eine im Skelettteil vorhandene In‐
formation darf nicht in einem sub-part neu und unabhängig vom Skelettteil erzeugt werden.
Alle Modelle sind grundsätzlich mit sämtlichen zum Regenerieren erforderlichen Referenz-Dateien
an den Auftraggeber zu übergeben.
Geometrische externe Referenzen sind durch „data sharing feature“ abzubilden.
Referenzen auf Import-Features sind unzulässig.
Features
Die Modellierung von Geometrien, wie z. B. rounds, holes, chamfers mit Hilfe eines Sketches ist
nur zulässig, wenn sich diese Geometrie nicht mit den vorhandenen Features round, hole, chamfer
usw. erzeugen lässt. Ausnahmen sind mit 3D-Notes (Dolittle verwenden) zu dokumentieren. Aus‐
nahmen sind für spezielle Creo-Anwendungen zulässig, die entsprechende Features erzeugen, er‐
kennen und deren Fertigung zulassen.
Cuts dürfen nicht vollständig mit protrusions aufgefüllt werden. Eine protrusion darf niemals voll‐
ständig innerhalb einer anderen protrusion liegen.
Maßtoleranzen, Form- und Lagetoleranzen
Zur Verwendung in nachfolgenden Prozessen sind Maßtoleranzen und Form- und Lagetoleranzen
stets im part bzw. assembly zu erzeugen. Eine Erzeugung in der Zeichnung ohne Referenz zum
Modell ist unzulässig.
Tolerierungsgrundsatz nach VW 01054
Die Anwendung der Tolerierungsgrundsätze für Zeichnungen des Volkswagen Konzerns ist in der
VW 01054 geregelt.
Bauteilinformationen
Zusätzliche Bauteilinformationen sollen im 3D-Modell als notes erzeugt werden. In der Zeichnung
muss auf diese referenziert werden. Hierfür muss Dolittle verwendet werden.
Muster
Komplexe Geometrien sind über surface transform oder geometry pattern zu mustern.
Variable Rounds
Variable rounds dürfen keinen Radiuswert Null enthalten.
3.5.1.2  
3.5.1.3  
3.5.1.4  
3.5.1.5  
3.5.1.6  
3.5.1.7  
3.5.1.8  


### 第 16 页
Seite 16
VW 01059-5: 2015-04
Modellierungsmethodik
Der allgemeine Aufbau von Bauteilen (parts) erfolgt in der Reihenfolge:
–
Steuergeometrie (datums, points, axis, curves, surfaces)
–
Basisgeometrie (constructional features) und
–
Detaillierung (Features wie chamfers, rounds, cosmetics).
Die grundsätzliche Strategie folgt immer den Regeln:
Skelett => Funktions-/Rohteil => Fertigteil => evtl. DMU-Part.
Bei einfachen Bauteilen (parts) ist eine entsprechende Ordnung (Skelett => Funktions-/Rohteil =>
Fertigteil) durch Wahl geeigneter Konstruktionselemente innerhalb eines Einzelteils zu realisieren.
Es sollten entsprechende Layer erzeugt werden (Skelett => SKEL, Funktionsteil => FKT, Rohteil
=> RT und Fertigteil => FT). Darüber hinaus sollte es Layer für die Rohteilelemente geben (drafts
und rounds). Durch Unterdrücken der Bearbeitungs-Elemente des Layers Fertigteil muss das Roh‐
teil erzeugt werden können. Entsprechend ergibt ein Unterdrücken des Rundungs-Layers ein
Funktionsteil. Eine sinnvolle Benennung der Konstruktionselemente ist empfehlenswert.
Bei komplexen Bauteilen ist eine Aufteilung des Modellierungsumfangs in Funktionsteil/Roh‐
teil/Fertigteil mittels assoziativer Flächenkopien in einer Konstruktionsbaugruppe notwendig (siehe
Strichlinie in Bild 7).
Bei hochkomplexen Bauteilen (z. B. Getriebegehäuse, Zylinderkopf) enthält die Konstruktionsbau‐
gruppe zusätzlich funktionale Einheiten wie z. B. Butzenpart, Rippenpart_oben. Zur Verdeutlichung
des Referenzflusses für komplexe Bauteile dient Bild 7:
Bild 7 – Referenzfluss für komplexe parts
Skelett für komplexe Bauteile
Alle Elemente des Skeletts sind mit sprechenden bzw. für spezielle Creo-Anwendungen mit ferti‐
gungsrelevanten Namen zu versehen (siehe Abschnitt 3.3.2).
Im Skelettteil werden alle wesentlichen konstruktiven Details in Form von Bezugselementen und
Flächen festgelegt. Hier werden die funktionalen Abhängigkeiten zwischen den Entwurfsgrößen,
der Rohteilgeometrie und den Bearbeitungen geschaffen.
3.5.2  
*******  


### 第 17 页
Seite 17
VW 01059-5: 2015-04
Jede geometrische Information darf nur einmal definiert werden und ist verbindlich für den gesam‐
ten Konstruktionsumfang. Sobald eine Information von mehr als einem Bauteil oder einer Baugrup‐
pe verwendet wird, ist diese Information in dem zugehörigen Skelett zu definieren. Wird eine Infor‐
mation mehrmals benötigt, so ist jeweils das entsprechende, vorhandene verbindliche Element
über eine assoziative Kopie der Information aus dem Skelett zu referenzieren.
Rohteilbezüge bei Gussteilen sind vorzusehen (z. B. „Mitte Motor“). Zwecks Vereinfachung kann
bei Anwendung des Roh-Fertigteil-Konzepts sowohl bei der Rohteilerzeugung (Butzen) als auch
bei der Fertigteilerzeugung auf dieselbe Achse referenziert werden. Die Fräsbearbeitung kann als
Fläche im Skelettteil eingebracht werden. Diese Flächen können später im Fertigteil zur Schnitter‐
zeugung über „Model; Copy Geometry“ verwendet werden (dies gilt nicht für Bohrungen). Zur Be‐
schreibung eines Butzens im Skelett sollte die Bearbeitungsfläche, die Achse für die spätere Bear‐
beitung und die bearbeitete Kontur als Kurve (Kreis) vorliegen.
Neben dem oben beschriebenen klassischen Bauteilskelett existieren auch Zusammenbauskelet‐
te. Diese enthalten funktionale Einbau-Koordinatensysteme für die Teile, die in der Baugruppe zu‐
sammengestellt sind. In einem Baugruppenskelett für z. B. einen Nockenwellentrieb kann der Be‐
wegungsablauf definiert sein, so dass durch Ändern eines zentralen Maßes im Baugruppenskelett
verschiedene Bewegungszustände wiedergegeben werden. Ist dies der Fall, muss als 3D-Notiz
des Baugruppenskeletts auf das Konstruktionselement hingewiesen werden, welches den Bewe‐
gungszustand steuert.
Beispiel: /* Bezugsebene PL_Nockenwinkel, ID 06087 bestimmt den Rotationswinkel des Baugrup‐
penskeletts.
In Zusammenbauten wird das Fertigteil verwendet und nicht die zur Erstellung des Fertigteils ver‐
wendete Konstruktionsbaugruppe. Andernfalls ist es nicht möglich, eine korrekte Massenanalyse
durchzuführen, weil z. B. ein Teil durch Roh- und Fertigteil (mit unterschiedlichen Massen) reprä‐
sentiert wird.
Kerne, Körper, Rippen
Die Modellierung der relevanten Geometrien erfolgt in je nach Bauteil aufgeteilten sub-parts. Kerne
wie Wassermantelkern, Ölkern, usw. sind bei komplexen Bauteilen wie Zylinderkopf oder Zylinder‐
kurbelgehäuse (ZKG) anzutreffen und bilden natürliche Kerne, die positiv als Volumen modelliert
werden. Aufwendige Verrippungen lassen sich in einem eigenen Bauteil erzeugen. Als Körper wird
jedes Bauteil verstanden, das vorwiegend der Beschreibung von Außengeometrie dient (im Fall
ZKG bestehend z. B. aus Zylinderbankscheibe, Flansch_vorn, Flansch_hinten usw.).
Rohteil, Fertigteil, Funktionsteil
Das Modell des Rohteils wird aus den zugehörigen sub-parts (Kerne, Körper und Rippen) gebildet.
Die in den sub-parts noch fehlende Geometrie (Butzen, Formschrägen, Verrundungen) wird er‐
gänzt.
Das Modell des Fertigteils wird als Flächenkopie („CopyGeom“) aus dem Rohteilmodell abgeleitet.
Das aus dieser Flächenkopie erzeugte Solid wird mit cuts und holes beaufschlagt, die die mecha‐
nische Bearbeitung darstellen. Bei Bedarf kann vor dem Rohteil ein Funktionsteil erstellt werden,
das als Grundlage für Festigkeitsberechnungen usw. dient.
*******  
*******  


### 第 18 页
Seite 18
VW 01059-5: 2015-04
Dokumentation
Für ein hochkomplexes Bauteil wie einen Zylinderkopf ist eine drawing mit dem Namen        
„<objektname>-doc.drw“ zu erzeugen, die die Konstruktionsbaugruppenstruktur beschreibt. Kriti‐
sche Stellen im Modell (z. B. nicht vermeidbare „Geom Checks“) sollten mit 3D-Notes markiert
werden (Dolittle verwenden).
Datenqualität
Das 3D-Modell und die Zeichnung müssen im parametrisch/assoziativen PTC Creo Parametric-
Format erstellt sein. Alle zum Verändern/Steuern des Modells notwendigen Creo-Parameter und
Konstruktionselemente müssen vorhanden sein.
Das 3D-Modell ist vollständig detailliert als Volumenmodell auszuführen. Abweichungen, wie z. B.
die Lieferung von Flächenmodellen, sind vor Abgabe schriftlich mit dem Auftraggeber zu vereinba‐
ren.
Die 2D-Zeichnung ist vollständig vom 3D-Modell abzuleiten. Ansichten dürfen nicht skizziert
werden. Ausgenommen sind Zeichnungen mit importierter 2D-Geometrie ohne 3D-Modell. Dabei
werden die Parameter statt im Modell in der Drawing abgelegt. Diese Abweichung von der Norm
ist vorher zu vereinbaren.
Das Erzeugen von 2D-Geometrie auf Zeichnungen ist zu vermeiden. Sie ist durch cosmetics oder
curves im 3D-Modell zu erzeugen.
Beim Plotten von Zeichnungen ist immer darauf zu achten, dass sich die Zeichnung auch auf den
neuesten Stand des Modells referenziert. Sie darf im plotfähigen Zustand keine unnötigen Hilfsge‐
ometrien (planes usw.) beinhalten.
Die Funktion Annotate; Edit; Convert to Draft Entities ist nur zu Illustrationszwecken zulässig.
Nichtparametrische Bemaßungen sind auf ein unumgängliches Minimum zu beschränken. Die Ma‐
ße müssen in jedem Fall assoziativ zum Modell sein.
Zeichnungen dürfen nicht überlagert werden (Layout; Overlay).
Außerhalb des Zeichnungsrahmens dürfen sich keine 2D-Elemente befinden.
Nicht mehr benötigte Ansichten müssen mit delete gelöscht werden.
Unterdrückte (erased) Ansichten sind nicht zulässig.
Zeichnungen von simplified representations einer Baugruppe sollen auch die Master-Darstellung
als Zeichnungs-Modell beinhalten, auch wenn diese in keiner Ansicht dargestellt ist (Archivierung!).
Die dadurch entstehende Warning in VW ModelCheck ist zu tolerieren.
In Creo eingelesene Fremdformate (IGES, VDAFS, STEP) dürfen nur zum Zweck von Einbauun‐
tersuchungen und Ankonstruktionen verwendet werden.
Für Systemlieferanten ist es nach Vereinbarung (z. B. aus Gründen des Know-How-Schutzes)
möglich, nichtparametrische Modelle zu liefern. Diese Modelle dürfen keine externen Referenzen
zu nicht gelieferten Datensätzen haben.
Modelle müssen vollständig regenerierbar sein. Dies ist mit Tools; Model Player zu prüfen. Unvoll‐
ständige Konstruktionselemente (incomplete features) dürfen nicht enthalten sein.
Parts dürfen nur in Ausnahmefällen mit unterdrückten Konstruktionselementen gespeichert
werden.
4  
5  


### 第 19 页
Seite 19
VW 01059-5: 2015-04
Baugruppen dürfen nicht mit unterdrückten Komponenten gespeichert werden. Ausnahmefälle sind
z. B. Teilefamilien oder konstruktionsbedingte Strategien.
Einer Baugruppe (Dateityp: .asm) darf keine Dichte zugewiesen werden.
Die Verwendung von Austauschbaugruppen (Interchange Assemblies) ist nicht gestattet, da hier‐
durch externe Referenzen entstehen können.
3D-Modelle, die eine Shrinkwrap-Geometrie beinhalten, dürfen nie als Daten für einen produktiven
Umfang dienen und dürfen auch nie den KVS-Status „V“ (verbindlich) erhalten. Falls ein Know-
How geschützt werden soll, dann sind andere Creo-Features zu benutzen (z. B. „Copy Geomtry‑in‐
dependent“). Shrinkwraps dürfen nur zu Informationszwecken bzw. für DMU verwendet werden.
Aus einem 3D-Modell müssen sich folgende Bauteileigenschaften (z. B. zur Analyse der Bauteildy‐
namik im Fahrzeug) im CAD-System berechnen lassen:
–
Masse des Bauteils in [g]
–
Massenschwerpunkt als Abstand in x, y, z-Richtung zum Bauteil-Bezugssystem in [mm]
–
Massenträgheitsmoment als Tensor um alle Trägheitsachsen in [g*mm²]
Bei Lieferung eines nicht parametrischen Models (z. B. aus Gründen des Know-How-Schutzes) ist
sicherzustellen, dass die erforderlichen Bauteileigenschaften als Parameter und die geometrische
Lage des Bezugssystems dem CAD-Model beigefügt werden, siehe Bild 8.
Bild 8 – Massenberechnung
Verwendung von VW ModelCheck
Die Daten müssen mit den für die Volkswagen AG konzernweit vorgeschriebenen Prüfprogram‐
men (für Creo-Modelle: VW ModelCheck) konstruktionsbegleitend getestet werden. Das Prüfproto‐
koll ist bei der Datenlieferung beizulegen. Um die Regenerierbarkeit der Daten sicherzustellen, ist
mit VW ModelCheck – Full Check zu prüfen. Alle bei der Prüfung mit „Error“ gekennzeichneten
Fehler und die in Abschnitt 5.2 genannten „Warnings“ sind zu beseitigen. Des Weiteren ist die An‐
zahl der „Warnings“ auf ein Minimum zu reduzieren. Auch auf Altdaten basierende Neukonstruktio‐
nen müssen VW Model-Check-OK sein.
5.1  


### 第 20 页
Seite 20
VW 01059-5: 2015-04
 
Hinweis:
Mit dem Quick Check analog dem Modus „ModelCHECK Interactive“ wird der Umfang (All Drawing
Models/All Levels) auf die formalen Kriterien ohne Regenerierung überprüft. Hierdurch ist gerade
bei großen Baugruppen eine Zeitersparnis zu erzielen, da Fehler in formalen Kriterien schnell ge‐
funden und repariert werden können. Diese Methode wird zur schnellen Zwischenprüfung, wäh‐
rend der Konstruktionsphase empfohlen.
Mit dem Full Checkhat der Anwender die Möglichkeit, VW ModelCheck komplett inkl. Regenerie‐
rung auf eine Zeichnung, Baugruppe oder ein Einzelteil anzuwenden. Die Checkroutine entspricht
der alten Funktion „ModelCHECK for KVS“(ModelCHECK Regenerate/All Drawing Modells/All Le‐
vels). Dieser Modus wird auch im KVS angewendet, und wird somit zur Vorbereitung auf die KVS
Ablage ausgeführt.
VW ModelCheck ermöglicht gegenüber dem PTC ModelCHECK eine Reparatur der Daten.
Unzulässige Meldungen bei der Regenerierung
Die folgenden Warnmeldungen sind unzulässig und müssen behoben werden:
–
Original reference not found, using alternate.
–
WARNING: CUT is entirely outside the model; model unchanged.
Ausnahme: Kontrollbearbeitung
–
WARNING: PROTRUSION is entirely inside the model; model unchanged.
–
„... self-intersecting geometry“
–
„Warning: Design intent is unclear ...“
Hinweis: Die Fehleranalyse kann mit Geometry Check durchgeführt werden. Lassen sich die
Warnungen des Geometry Checks absolut nicht vermeiden bzw. reparieren, so müssen sie als
zweisprachige 3D-Note (DoLittle verwenden) detailliert dokumentiert werden.
Regelungen für die Betriebsmittelkonstruktion (BEMI)
Anwendungsbereich (BEMI)
Die Festlegungen in diesem Abschnitt gelten ausschließlich für die Konstruktion von Betriebsmit‐
teln mit dem CAD-System Creo. Hier nicht aufgeführte Punkte sind dem vorherigen, allgemeinen
Normentext zu entnehmen.
Abweichungen von diesen Festlegungen für die Konstruktion von Betriebsmitteln sind in Ausnah‐
mefällen möglich, müssen aber zwischen den Entwicklungspartnern schriftlich vereinbart werden.
Vorgaben (BEMI)
Zur Zeichnungserstellung sind die parametrischen Betriebsmittel-Volkswagen AG-Zeichnungsrah‐
men und die bereitgestellten Betriebsmittel-Symbole sowie -Tabellen zu verwenden.
5.2  
6  
6.1  
6.2  


### 第 21 页
Seite 21
VW 01059-5: 2015-04
Anforderungen (BEMI)
Namenskonventionen für part, assembly, drawing (BEMI)
Betriebsmittel-Creo-Objekte sind zur eindeutigen Identifikation nach der Namenskonvention nach
Tabelle 2 oder Tabelle 3 zu benennen.
Tabelle 2 – Namenskonventionen bei der Betriebsmittelkonstruktion
Objekt
Entwurf
Kaufteil
Normteil
Freigabestand
CAD-Modell (prt, asm)
Teil, Teil als assembly
ft_be_firma_usr_projekta)
fbez_bestdat
nbez-znr_abmsg
sss_zusat_uu-vvv-
wwx_yyyyyy_zzz
CAD-Modell (asm)
ZSB
 
 
 
000_zusat_uu-vvv-
wwx_yyyyyy_zzz
CAD-Zeichnung (drw)
 
 
 
uu-vvv-wwx_yyyyyy_zzz
Zeichnungssymbol
Benennung_firmaa) _bm
Zeichnungsaufkleber
Benennung_firmaa) _bm
Zeichnungsformat
Benennung_firmaa) _bm
Mark up
wie Modellname
Layout
wie Modellname
User defined feature
Benennung_bm
Material Tabelle
wird bei Bedarf festgelegt
a)
Optionaler Namensbestandteil. Bei Entfall optionaler Namensbestandteile entfallen auch zugehörige Trennungs- oder Leerzeichen.
Tabelle 3 – Namenskonventionen für Gießerei-Betriebsmittel
Objekt
Entwurf
Kaufteil
Normteil
Freigabestand
CAD-Modell/-Zeichnung
(prt, asm, drw)
ft_be_firma_usr_projekta)
fbez_bestdat
nbez-znr_abmsg
uu-vvv-wwx_yyyyyy_zzz_ssa)-
sssa)-ssa) oder wahlweise
yyyyyy_zusatz
Zeichnungssymbol
Benennung_firmaa) _bm
Zeichnungsaufkleber
Benennung_firmaa) _bm
Zeichnungsformat
Benennung_firmaa) _bm
Mark up
wie Modellname
Layout
wie Modellname
a)
Optionaler Namensbestandteil. Bei Entfall optionaler Namensbestandteile entfallen auch zugehörige Trennungs- oder Leerzeichen.
Legende für Tabelle 2 und Tabelle 3:
ft
fertigungstechnische Kennung
ax
ohne Kennung
af
Frästeil
ad
Drehteil
as
Schweißteil
an
Kaufteil mit Nacharbeit
fbez
Firmenbezeichnung ([a-z], [0-9], max. 10 Zeichen)
6.3  
6.3.1  


### 第 22 页
Seite 22
VW 01059-5: 2015-04
be
Benennung ([a-z], [0-9])
bestdat
Bestelldaten entspr. Firmenunterlage ([a-z], [0-9], max. 20 Zeichen)
nbez
Bezeichnung der Norm, z. B. DIN, EN ([a-z])
znr
Zählnummer der entsprechenden Norm, z. B. 612 ([0-9])
abmsg
Abmessung entsprechend Normbezeichnung, z. B. M6x15x1_25 ([a-z],
[0-9], [_])
sss (nur Tab. 2)
Betriebsmittel-Teilnummer mit führenden Nullen (s: [0-9])
ss-sss-ss (nur Tab. 3) Gießerei-Betriebsmittel-Teilnummer mit führenden Nullen (s: [0-9]) max. 9
Zeichen incl. Trennzeichen
zusat (nur Tab. 2)
Zusatz zur Beschreibung von Untermengen, Teilmodellen oder Bau‐
gruppenobjekten (t: [a-z], [0-9]) 5 Zeichen mit führenden Nullen (z. B.
DRK01, REF01)
zusatz (nur Tab. 3)
Zusatz zur Beschreibung von Untermengen, Teilmodellen oder Bau‐
gruppenobjekten ([a-z], [0-9], [_]) max. 24 Zeichen incl. Trennzeichen (z. B.
kok_fs_es_ein_06)
uu
Werksnummer (u: [0-9])
vv
Untergruppen-Nummer (v: [a-z], [0-9])
ww
Hauptgruppen-Nummer (w: [0-9])
x
Kennbuchstabe für Bereich/Abteilung (x: [a-z])
yyyyyy
Zählnummer (y: [0-9]) mit führenden Nullen
zzz
Strichausführung (z: [0-9]) mit führenden Nullen
bm
Kurzzeichen für „Betriebsmittelkonstruktion“
Modellaufbau (BEMI)
Grundlagen (BEMI)
Alle CAD-Modelle für eine Betriebsmittelkonstruktion müssen auf der Basis eines Volkswagen AG
Betriebsmittel-GRI-Templates erzeugt werden.
Hierbei gelten folgende Regelungen:
–
Die Bezeichnung der in den Volkswagen AG GRI-Templates vorhandenen Datums kann mit
einem Zusatz ergänzt werden (Beispiel: PL_XY_031 für Positionsnummer 31 des Betriebsmit‐
tel-Einzelteiles).
–
Zur Zeichnungserstellung sind die parametrischen Betriebsmittel-Volkswagen AG-Zeichnungs‐
rahmen zu verwenden.
Maßtoleranzen (BEMI)
Die zulässigen Abweichungen von Maßen an Bauteilen, die mit werkstattüblicher Genauigkeit ge‐
fertigt werden dürfen, sind nach VW 01058 Abschnitt „Feld für zulässige Abweichungen und Ober‐
flächen“ in der Tabelle „Allgemeintoleranzen für Nennmaße ohne Toleranz“ auf der Zeichnung ein‐
zutragen.
6.4  
6.4.1  
6.4.2  


### 第 23 页
Seite 23
VW 01059-5: 2015-04
Werkstückkanten (BEMI)
Die Kantenzustände ohne bestimmte geometrische Form sind nach VW 01088 in dem Feld „Werk‐
stückkanten“ auf der Zeichnung einzutragen, siehe auch VW 01058 Abschnitt „Feld für zulässige
Abweichungen und Oberflächen“.
Form- und Lagetoleranzen (BEMI)
Form- und Lagetoleranzen nach DIN EN ISO 1101 sind vorzugsweise im 3D-Modell zu definieren.
Das gleichzeitige definieren von Toleranzen in 2D-Zeichnung und in 3D-Model in einem Datensatz
ist nicht zulässig.
Verwendung von family tables (BEMI)
Die Darstellung von Varianten über family tables ist zulässig.
Bearbeitungsangaben (BEMI)
Die Definition von 2D-Bearbeitungsangaben (2D-Symbole) in der Zeichnung ist zulässig.
Mitgeltende Unterlagen
Die folgenden in der Norm zitierten Dokumente sind zur Anwendung dieser Norm erforderlich:
VW 01014
Zeichnungen; Zeichnungsrahmen und Wiederholtexte
VW 01052
Zeichnungen; Darstellungen
VW 01054
Zeichnung; Bemaßung
VW 01058
Zeichnungen; Beschriftungen
VW 01059-1
Anforderungen an CAD/CAM-Daten; Darstellung Technischer Sachver‐
halte
VW 01088
Werkstückkanten; Begriffe, Zeichnungsangaben
VW 01098
Teilnummernsystem
VW 01099
Kennung von Werken, Beteiligungsgesellschaften und Firmen
VW 11535
Metrisches ISO-Gewinde; Bohrer- und Gewindekernloch-Durchmesser;
VW-Festsitzgewinde
VW 11635
Kernlöcher für Gewinde; Bohrer und Grenzmaße für Toleranzfeldlage H
VW 13750
Oberflächenschutz für Metallteile; Schutzarten, Kurzzeichen
DIN EN 10027-2
Bezeichnungssysteme für Stähle; Teil 2: Nummernsystem
DIN EN ISO 1101
Geometrische Produktspezifikation (GPS) - Geometrische Tolerierung -
Tolerierung von Form, Richtung, Ort und Lauf
VDA 260
Bauteile von Kraftfahrzeugen - Kennzeichnung der Werkstoffe
6.4.3  
6.4.4  
6.4.5  
6.4.6  
7  


### 第 24 页
Seite 24
VW 01059-5: 2015-04
Parameter
Mindestens die im Volkswagen AG GRI-Template enthaltenen mit YES versehenen Parameter für
Fahrzeugteile und/oder Betriebsmittel (siehe Tabelle A.1, Spalte 2) sind designated und mit korrek‐
ten Werten zu belegen.
Der Status DESIGNATED (YES/NO) in Tabelle A.1, Spalte 2 und der Parametertyp (TYPE) in
Spalte 4 sind zwingend einzuhalten.
Tabelle A.1 – Parameter im GRI-Template
Nr.
DESIGNATED
Parameter Name
TYPE
Erläuterung, Bezeichnung (Beispiele)a)
FE
BEMI
1
YES
NO
COMMENTS
String
Bemerkungen zum Entwicklungsstand
2
YES
NO
DEPARTMENT
String
Abteilung („EAOE“)
3
YES
NO
DRAWN_BY
String
Bearbeiter „Mustermann/Partnerfirma“
4
YES
NO
DRW_DATEb)
String
Zeichnungsdatum
(„08.07.05“ Format: dd.mm.yy)
Datum der Zeichnungserstellung
5
YES
NO
ENG_PRJ_NO
String
EA-Nr. („EA 111“)
6
YES
NO
LAYOUT_NO
String
ENT-Nr. („789 01“)
7
YES
NO
MAT_TREATMENT
String
Werkstoff-Behandlung
8
YES
NO
MAT_TREATMENT_2
String
Werkstoff-Behandlung 2
9
YES
NO
MATERIAL
String
Werkstoff („DIN EN 10130-DC01“)
10
YES
NO
MATERIAL_2
String
Werkstoff 2
11
YES
NO
MATERIAL_3
String
Werkstoff 3
12
YES
NO
MOD_DATEc)
String
Änderungsdatum
(„12.07.05“ Format: dd.mm.yy)
Zur Befüllung des Parameters „MOD_DATE“
ist die Applikation Modpar zu verwenden.
13
YES
NO
MODPAR_VERSION
String
Versionsnummer der letzten Modpar-Bear‐
beitung („4.3“)
14
YES
NO
VW_NAME_ADDITION
String
Zusatzbenennung aus TEIVON Deutsch
15
YES
NO
NAME_PREFIX
String
Benennung Präfix Deutsch („ZSB“)
(siehe Tabelle A.2 - Zulässige Präfixe)
16
YES
NO
NAMING
String
Benennung Deutsch („Lager“)
17
YES
NO
iiPART_NOd)
String
Teilnummer („123 456 789 AB“)
18
YES
NO
PHONE_NO
String
Telefon-Nr. des Verantwortlichen
(„+4953619123456“ max. 13 Stellen)
19
YES
NO
PN_PREFIX
String
Teilnummer Präfix bleibt leer, nur für Altda‐
ten!
20
YES
NO
RESP_DEVELOP
String
Entwicklungsverantwortung nach VW 01099
(„VW“, „AU“, „SK“, „SE“, „LA“, „BU“, „BY“,
„PO“, „MN“, „SC“, „DU“)
21
YES
NO
RESPONSIBLE
String
Verantwortlicher („Musterfrau“)
22
YES
NO
SAFETY_DOC
String
Sicherheitsdokument nach VW 01058 „TLD“
Anhang A (normativ)  


### 第 25 页
Seite 25
VW 01059-5: 2015-04
Nr.
DESIGNATED
Parameter Name
TYPE
Erläuterung, Bezeichnung (Beispiele)a)
FE
BEMI
23
YES
NO
SEMI_FINISH_PD
String
Halbzeug
24
YES
NO
SEMI_FINISH_PD_2
String
Halbzeug 2
25
YES
NO
SURF_PROTECTION
String
Oberflächenschutz nach VW 13750 („t610“)
26
YES
NO
TITLE
String
Benennung Englisch („Bearing“)
27
YES
NO
VW_TITLE_ADDITION
String
Zusatzbenennung aus TEIVON Englisch
28
YES
NO
TITLE_PREFIX
String
Benennung Präfix Englisch („ASS“)
(siehe Tabelle A.2 - Zulässige Präfixe)
29
YES
NO
TYPE_APPR_DOC
String
Typ-Prüf-Dokument
(„*********** AA“, „ATS 200“)
30
YES
NO
V_NO
String
V-Nummer („V310“, „V400“)
31
YES
NO
VW_APPROVAL_REQUIRED
String
Baumusterpflicht, kein Standardwert mehr
eingetragen!
32
YES
NO
VW_CAD_SYSTEM_KEY
String
CAD-System und Verwaltungssystem-
Schlüssel („PTC Creo Parametric“)
33
NO
NO
VW_DENSITY_RELEVANT
Yes / No
Modell ist für Gewichtsberechnung relevant
(nur für VW ModelCheck-Prüfung); Standard‐
wert = „YES“; bei Skeletons, DMU-Parts und
BIN-Parts = „NO“
34
YES
NO
VW_DRAWN_BY_COMPANY
String
Partnerfirma
35
YES
NO
VW_MATERIAL_LABEL
String
Materialkennzeichnung nach VDA 260
36
NO
NO
VW_MATERIAL_NUMBER
String
Werkstoffnummer für Metalle nach
DIN EN 10027-2 („1.7225“)
37
NO
NO
VW_MISS_ID
String
MISS-Id des Werkstoffes aus Modpar
38
NO
NO
VW_MODEL
Yes / No VW-Modell („NO“)
39
YES
NO
VW_OPENAIRWEATHER_ID
String
Freibewitterungskennzeichen
40
NO
NO
VW_R_ANNEAL
String
 
41
NO
NO
VW_R_ASSOCIATED_RESOURCES
String
Dazugehörige Betriebsmittel
(„11-38D 500121/122“)
42
NO
NO
VW_R_BOM
Yes / No Stückliste („YES“)
43
NO
NO
VW_R_BOM_ITEM_NO
String
Stücklistenposition („3“)
44
NO
NO
VW_R_CONTRACTED_COMPANY
String
Beauftragte Firma
45
NO
NO
VW_R_HARDENING
String
Gehärtet
46
NO
NO
VW_R_INSERT_DEPTH
String
 
47
NO
NO
VW_R_MACHINE
String
Maschine („Maho 250“)
48
NO
NO
VW_R_NAMING
String
Benennung („Spannvorrichtung“)
49
NO
NO
VW_R_NAMING_APPENDIX
String
Benennung Zusatz
50
NO
NO
VW_R_NUMBER_LEFT
String
Nummer links
51
NO
NO
VW_R_NUMBER_RIGHT
String
Nummer rechts
52
NO
NO
VW_R_ORDERING_DEPARTMENT
String
Auftrag gebende Abteilung
53
NO
NO
VW_R_PART_DATE
String
Datum Fahrzeugteil („08.07.2005“)
54
NO
NO
VW_R_PART_DATE_2
String
Datum Fahrzeugteil 2


### 第 26 页
Seite 26
VW 01059-5: 2015-04
Nr.
DESIGNATED
Parameter Name
TYPE
Erläuterung, Bezeichnung (Beispiele)a)
FE
BEMI
55
NO
NO
VW_R_PART_DATE_3
String
Datum Fahrzeugteil 3
56
NO
NO
VW_R_PART_DATE_4
String
Datum Fahrzeugteil 4
57
NO
NO
VW_R_PART_DESIGN_RELEASE
String
K-Stand Fahrzeugteil 1
58
NO
NO
VW_R_PART_DESIGN_RELEASE_2
String
K-Stand Fahrzeugteil 2
59
NO
NO
VW_R_PART_DESIGN_RELEASE_3
String
K-Stand Fahrzeugteil 3
60
NO
NO
VW_R_PART_DESIGN_RELEASE_4
String
K-Stand Fahrzeugteil 4
61
NO
NO
VW_R_PART_NAMING
String
Benennung Fahrzeugteil („Schwenklager“)
62
NO
NO
VW_R_PART_NAMING_2
String
Benennung Fahrzeugteil 2
63
NO
NO
VW_R_PART_NAMING_3
String
Benennung Fahrzeugteil 3
64
NO
NO
VW_R_PART_NAMING_4
String
Benennung Fahrzeugteil 4
65
NO
NO
VW_R_PART_NO
String
Fahrzeugteilnummer („123.456.789.A“)
66
NO
NO
VW_R_PART_NO_2
String
Fahrzeugteilnummer 2
67
NO
NO
VW_R_PART_NO_3
String
Fahrzeugteilnummer 3
68
NO
NO
VW_R_PART_NO_4
String
Fahrzeugteilnummer 4
69
NO
NO
VW_R_PHONE_NO_DRAFTER
String
 
70
NO
NO
VW_R_STOCK_NO
String
Inventarnummer („123456“)
71
NO
NO
VW_R_TOP_LEVEL
Yes / No („NO“)
72
YES
NO
VW_REPR_PART
String
ZSB-Kennzeichen aus dem System TEIVON
(„Z“, „G“, „-“)
73
NO
NO
VW_RESOURCE
Yes / No VW Betriebsmittel („YES“),
Technische Entwicklung („NO“)
74
YES
NO
VW_SAFETY_DOC_NO
String
Segmentierte TLD-Nummer
(„TLD.123.456.78“)
75
YES
NO
VW_SUPPLIER_PART_NO
String
Zuliefererteilnummer
76
NO
NO
VW_TEIVON_ID
String
TEIVON-Id aus Modpar
77
YES
YES
VW_USE_RESTRICTION
String
Default „N”
Für Protected-Rahmen ist der Firmenname
einzugeben
a)
Beispiele in Anführungszeichen werden als Strings interpretiert.
b)
DRW_DATE: Datum der Erst-Erstellung der Zeichnung, ist auch 1. Eintrag in der (durch Modpar erzeugten) Revision-Table auf der
Zeichnung
c)
MOD_DATE: Letztes Änderungsdatum des Modells bzw. der Zeichnung, ist auch letzter Eintrag in der ( durch Modpar erzeugten)
Revision-Table auf der Zeichnung.
d)
Bei Fahrzeugteilen muss der Parameter PART_NO (für Teilnummer) durch die drei Gruppen der Teilnummer und des optionalen
Indexes angegeben werden. Zwischen den Gruppen ist statt des trennenden Punktes ein Leerzeichen anzugeben. Die Angabe der
Leerzeichen ist ausschließlich im Parameter PART_NO erforderlich. Für den Dateinamen gilt Abschnitt 3.3.3
Für den Parameter NAME_PREFIX sind nur die in Tabelle A.2 genannten Werte zulässig.
Für den Parameter TITLE_PREFIX sind nur die in Tabelle A.2 genannten Werte zulässig.


### 第 27 页
Seite 27
VW 01059-5: 2015-04
Tabelle A.2 – Zulässige Präfixe
Kurzzeichen
Deutsche Erklärung
NAME_PREFIX
TITLE_PREFIX
Englische Erklärung
A
Anordnung
ANO
ARA
arrangement
E
Einbau
ENB
INS
installation
G
Schweißgruppe
SGR
WAS
welded assembly
K
Klebeschilder
 
 
adhesive plates
L
Lieferumfang
 
 
supply scope
S
Zusammenstellung
ZSS
COM
combination
T
Tabelle
TAB
TAB
table assembly
W
Lieferabwicklung
 
 
supply transaction
Y
Typprüfzeichnung
 
 
type approval drawing
Z
Zusammenbau
ZSB
ASS
assembly
Die Parameter der Tabelle A.3 werden von Applikationen erzeugt und dürfen manuell nicht verän‐
dert werden.
Tabelle A.3 – Applikationsspezifische Parameter
Parameter
Applikation
VW_KSE_EXTRACTION
Ab Modpar 5.0
PART_STATE
Normteil-Generator
PART_STATE _0
Normteil-Generator
PART_STATE _1
Normteil-Generator
PART_STATE _2
Normteil-Generator
PTC_WM_MODIFIED_BY
Windchill
PTC_WM_CREATED_ON
Windchill
PTC_WM_TEAM
Windchill
PTC_WM_ITERATION
Windchill
WGMREVKEY
Windchill
PTC_WM_VERSION
Windchill
PTC_WM_REVISION
Windchill
PTC_WM_LIFECYCLE_STATE
Windchill
PTC_WM_MODIFIED_ON
Windchill
PTC_WM_LOCATION
Windchill
PTC_WM_CREATED_BY
Windchill
PTC_WM_LIFECYCLE
Windchill


### 第 28 页
Seite 28
VW 01059-5: 2015-04
 
Aus Gründen der rationellen Fertigung sind die in der Tabelle B.1, Tabelle B.2, Tabelle B.3 und
Tabelle B.4 angegebenen metrischen ISO-Gewinde bevorzugt einzusetzen. Bei den angegebenen
Kernlochdurchmessern handelt es sich um Durchschnittswerte. Der genaue Wert ist abhängig vom
Werkstoff und kann der zitierten Norm entnommen werden.
Tabelle B.1 – Regelgewinde nach VW 11610
Gewindedurchmesser
Steigung
Kernlochdurchmesser nach VW 11635
M4
0.70 mm
3.30 mm
M5
0.80 mm
4.20 mm
M6
1.00 mm
5.00 mm
M7
1.00 mm
6.00 mm
M8
1.25 mm
6.75 mm
M10
1.50 mm
8.45 mm
M12
1.75 mm
10.20 mm
Tabelle B.2 – Feingewinde nach VW 11610
Gewindedurchmesser
Steigung
Kernlochdurchmesser nach VW 11635
M6 × 0.75
0.75 mm
5.25 mm
M7 × 0.75
0.75 mm
6.25 mm
M8 × 0.75
0.75 mm
7.25 mm
M8 × 1.00
1.00 mm
7.00 mm
M9 × 1.00
1.00 mm
8.00 mm
M10 × 0.75
0.75 mm
9.25 mm
M10 × 1.00
1.00 mm
9.00 mm
M10 × 1.25
1.25 mm
8.75 mm
M12 × 1.00
1.00 mm
11.00 mm
M12 × 1.25
1.25 mm
10.75 mm
M12 × 1.50
1.50 mm
10.50 mm
Tabelle B.3 – Festsitzgewinde nach VW 11516
Gewindedurchmesser
Steigung
Kernlochdurchmesser nach VW 11535
M6
1.00 mm
4.80 mm
M7
1.00 mm
6.00 mm
M8
1.25 mm
6.50 mm
M10 × 1.00
1.00 mm
8.80 mm
M12 × 1.50
1.50 mm
10.25 mm
M14 × 1.50
1.50 mm
12.25 mm
Anhang B (informativ)  


### 第 29 页
Seite 29
VW 01059-5: 2015-04
Tabelle B.4 – Sondergewinde M 24 x 1.5 nach VW 01044
Gewindedurchmesser
Steigung
Kernlochdurchmesser
M24 × 1.50
1.50 mm
21.90 mm

