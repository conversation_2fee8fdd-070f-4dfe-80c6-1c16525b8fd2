# VW_01059_6_3_DE_2011-11_CAD&CAM数据要求_第6_3部分：CAD系统CATIA V5_过程链适配器.pdf

## 文档信息
- 标题：
- 作者：
- 页数：19

## 文档内容
### 第 1 页
Konzernnorm
VW 01059-6-3
Ausgabe 2015-11
Klass.-Nr.:
22632
Schlagwörter:
CAD, Prozesskette, Adapter, Prozesskettenadapter, PCA, CATIA, CAM, DMU
Anforderungen an CAD/CAM-Daten – CAD-System CATIA V5-6
Teil 3: Prozesskettenadapter (PCA), DMU-CATPart und optionale Adapter
Frühere Ausgaben
VW 01059-6 Beiblatt 3: 2006-12, 2007-07, 2010-11, 2011-11, 2012-06
Änderungen
Gegenüber der VW 01059-6 Beiblatt 3: 2012-06 wurden folgende Änderungen vorgenommen:
–
Status des Dokuments von Beiblatt zu Norm geändert
–
Normtitel ergänzt
–
Version des CAD-Systems CATIA von V5 in V5-6 geändert
–
Fachverantwortung geändert
–
Abschnitte 1 bis 6: Überarbeitet und umstrukturiert; Tabellen wurden fehlerbereinigt und die
Tabellenformatierung angepasst; Tabellen 1 bis 6: Fußnoten 2 und 3 hinzugefügt
–
Abschnitt 5 „Inhalte von optionalen Adapterparts“: Inhalte von optionalen Adapterparts zuge‐
fügt
–
Abschnitt 6 „Allgemeingültige Tabellen“ zugefügt: Tabellen aufgeteilt und umstrukturiert
–
Abschnitt 7 „Mitgeltende Unterlagen“ aktualisiert
Inhalt
Seite
Anwendungsbereich ................................................................................................... 2
Abkürzungen und Begriffe .......................................................................................... 2
Abkürzungen .............................................................................................................. 2
Output-Adapter ........................................................................................................... 2
Inhalt eines Prozesskettenadapters PCA ................................................................... 3
Blechteil (Sheet Metal Part) ....................................................................................... 4
1
2
2.1
2.2
3
3.1
Norm vor Anwendung auf Aktualität prüfen.
Die elektronisch erzeugte Norm ist authentisch und gilt ohne Unterschrift.
Seite 1 von 19
Fachverantwortung
Normung
K-SIPE-2/3
Stefan Biernoth
Tel.: +49 5361 9-48896
EKDV/4 Norbert Wisla
EKDV
Tel.: +49 5361 9-48869
Maik Gummert
Alle Rechte vorbehalten. Weitergabe oder Vervielfältigung ohne vorherige Zustimmung einer Normenabteilung des Volkswagen Konzerns nicht gestattet.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Seite 2
VW 01059-6-3: 2015-11
Gussteil (Cast Metal Part) .......................................................................................... 6
Kunststoffteil (Plastic Part) ......................................................................................... 7
Profil (Profile Part) ...................................................................................................... 8
Sonstige Teile (Other Parts) ..................................................................................... 10
Inhalt eines DMU-CATParts ..................................................................................... 11
Inhalte von optionalen Adapterparts ........................................................................ 12
Adapter „Oxx_TMT“ für 3DZP .................................................................................. 12
Allgemeingültige Tabellen ........................................................................................ 13
Mitgeltende Unterlagen ............................................................................................ 19
3.2
3.3
3.4
3.5
4
5
5.1
6
7
Anwendungsbereich
Diese Norm gilt in Ergänzung zur VW 01059-6 für die Erzeugung von Prozesskettenadaptern
(Process Chain Adapter = PCA) und DMU-CATParts für Einzelteile und weiteren optionalen Adap‐
tern.
In den nachfolgenden Tabellen werden die weiterzugebenden Elemente aufgeteilt in drei verschie‐
dene Prioritätsstufen:
–
P – Pflichtelement für alle Bauteile
–
S – Pflichtelement für alle Bauteile, wenn vorhanden. Falls ein solches Element topologisch
nicht vorhanden sein kann, ist eine Verwendung als optionales Element zugelassen; (Um‐
schaltbarkeit zu Optional, „Switch“)
–
O – Optionales Element, welches bei Verfügbarkeit im Bauteil weitergegeben werden muss. In
Absprache zwischen Datenersteller und Datenabnehmer können optionale Elemente zu
Pflichtelementen werden.
Als Pflichtelemente gekennzeichnete Informationen müssen in einem freigegebenen Bauteilstand
vorhanden sein. Optionale Elemente müssen, falls sie im Konstruktionsprozess nicht entstanden
sind, nicht zusätzlich erzeugt werden.
Abkürzungen und Begriffe
Abkürzungen
LPS
Lokales-Positionier-System; siehe VW 01055
 
RB
Referenz-Bearbeitung; siehe VW 01055
 
3DZP
3D Zeichnungsloser Prozess; siehe Intranet http://catia.wob.vw.vwg:8080,
Methodes & Guidelines, 3D Zeichnungsloser Prozess (3DZP) bzw.
http://www.vwgroupsupply.com, F&E Dienstleistungen, 3DZP.
Weitere Abkürzungen und Begriffe siehe VW 01059-6-1.
Output-Adapter
Output-Adapter sind Dokumente, in denen prozesskettenrelevante geometrische und nichtgeomet-
rische Informationen zu Bauteilen an die Datenabnehmer weitergegeben werden. Output-Adapter
fassen das (vorläufige) Konstruktionsergebnis zusammen. Zu den Output-Adaptern gehören Doku‐
mente der CAD-Typen OUT, DMU, PCA und O01 – O99. Alle Output-Adapter folgen der in der
1  
2  
2.1  
2.2  


### 第 3 页
Seite 3
VW 01059-6-3: 2015-11
VW 01059-6 Abschnitt 3.2.2 genannten Namenskonvention. Regeln der Benennung und
Strukturierung von Output-Adaptern sind in der VW 01059-6-4 definiert.
Output-Adapter, die außerhalb einer CAD-Struktur archiviert werden, müssen nur in der TEIVON-
Nummer und der Versionsnummer mit dem Root-Product des zugrunde liegenden Quelldokuments
übereinstimmen.
Zur Deklaration der weiterzugebenden Elemente und zur Erzeugung der Adapter-CATParts soll die
konzernweit abgestimmte CATIA V5-6 Zusatzapplikation „OutGen“ der Volkswagen AG genutzt
werden.
Output-Adapter können sein:
PCA
Das CATPart mit dem CAD-Typ PCA (Process Chain Adapter) bündelt und
strukturiert als Prozesskettenadapter alle für die Datenabnehmer notwendi‐
gen geometrischen und nichtgeometrischen Informationen. Die enthaltene
Geometrie und Parameter müssen „published“ sein. Der PCA darf keinerlei
Links zu anderen Dokumenten enthalten, wenn dieser allein oder in einem
OUT-CATProduct archiviert wird.
Wird ein PCA unter einer DMU-relevanten PDA als einzelnes CATPart ar‐
chiviert, dient dieses auch der Absicherung des DMU-Prozesses.
 
DMU
Das CATPart mit dem CAD-Typ DMU stellt die das Fahrzeugbauteil reprä‐
sentierende Geometrie für den nachfolgenden DMU-Prozess als Solid/Vo‐
lumen oder wenn technisch nicht anders möglich, als Fläche (bei einseiti‐
ger Fläche mit Materialvektor) zur Verfügung.
 
O01 bis O99
Die CATParts mit dem CAD-Typ Oxx sind spezielle Adapter, die Elemente
aus dem Geometriebereich des KPR oder anderen Output-Adaptern entwe‐
der isoliert oder assoziativ zur Verfügung stellen. Sie müssen alle für die
Datenabnehmer notwendigen geometrischen und nichtgeometrischen Infor‐
mationen enthalten.
Inhalt eines Prozesskettenadapters PCA
Der Prozesskettenadapter dient zur strukturierten Datenübergabe an alle Datenabnehmer in paral‐
lelen und nachfolgenden Prozessen.
Entsprechend dem verwendeten Material, seiner Bearbeitung (Herstellverfahren) und der ange‐
wandten Konstruktionsmethodik wird im Folgenden unterschieden nach:
–
Blechteilen (Sheet Metal Part), siehe Tabelle 1
–
Gussteilen (Cast Metal Part), siehe Tabelle 2
–
Kunststoffteilen (Plastic Part), siehe Tabelle 3
–
Profilen (Profile Part) , siehe Tabelle 4 und
–
sonstigen Teilen (Other Parts), siehe Tabelle 5.
Ein Parameter „type_of_design“ ist mit dem Konzern-Strukturpart bereits im Konstruktionsbauteil
(GEO, G01, …) enthalten und muss entsprechend eingestellt werden.
 
 
3  


### 第 4 页
Seite 4
VW 01059-6-3: 2015-11
Des Weiteren wird in den Tabellen unterschieden zwischen den zeitlichen Phasen:
–
Entwurf: Arbeitsstände als Teilemodell oder Entwurf und
–
Freigabe: als Teilemodell archivierte Datenstände zu Prototypen-Baustufen, spätestens je‐
doch P- und B-Freigabe.
Entsprechend sind Elemente, die in einer frühen Phase noch optional sind, bei einem freigaberele‐
vanten Stand des Bauteils Pflicht.
Blechteil (Sheet Metal Part)
Tabelle 1 – Elemente bei Blechteilen
Elementbeschreibung
Structure/Elementname1)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Fertigteil Solid
(ggf. aufgedicktes Flächenmo‐
dell oder Zusammenführung der
Tailored Blanks)
PartBody[_*]
Body mit Eigenschaft PartBody 2), 3)
 
Regelung für ZSB PCAs:
Der PartBody muss leer sein. Die beschreibenden
Bodies, die die Geometrie enthalten, folgen dann
dieser Nomenklatur.
 
Body[_*]  
 
Body
O
S
S
Fertigteil Fläche
(einseitige Beschreibung der
konstruierten Seite)
Part_Geometry/Part_Geometry[_*]
bzw. bei Zusammenführung mehrerer Teile
Part_Geometry_„Beschreibender_Name“
Surface
P
P
S
Materialvektor4)
Material_Vector/Material_Vector[_*]
Line
P
P
S
Bauteilfläche beschnitten unge‐
locht
Part_Geometry_Trimmed_Non_Pierced/Part_Geo‐
metry_Trimmed_Non_Pierced[_*]
Surface
O
S
H
RPS-Elemente
Siehe Tabelle 8 „RPS-Elemente“
LPS-Elemente
Siehe Tabelle 9 „LPS Elemente“
RB-Elemente
Siehe Tabelle 10 „RB-Elemente“
LTA-Elemente
Siehe Tabelle 11 „LTA-Elemente“
Parameter und Property
Siehe Tabelle 12 „Parameter und Property“
Achsensysteme
Siehe Tabelle 13 „Achsensysteme“
Anlage- und Flanschbereiche
Contact_Areas/Contact_Area_"Kontaktbauteil-
Name"
Geometrical
Set,
Curve,
Surface
O
S
Curve
= S
Sur‐
face
= H
Bauteilfläche unbeschnitten
(Flächenmodell des Blechteils
vor den Beschnittoperationen)
Part_Geometry_Untrimmed/Part_Geometry_Un‐
trimmed[_*]
(ggf. die Flächen fortlaufend nummerieren _nn)
Surface
O
O
H
Außenbeschnittkontur des Bau‐
teils als Kurvenelement
Boundaries/Outer_Trim_Contour[_*]
Curve
O
O
H
3.1  


### 第 5 页
Seite 5
VW 01059-6-3: 2015-11
Elementbeschreibung
Structure/Elementname1)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Beschnittkonturen für den inne‐
ren Beschnitt
(mehrere Einzelkonturen mög‐
lich, keine Löcher)
Boundaries/Inner_Trim_Contour_„Beschreiben‐
der_Name“
(ggf. fortlaufend nummerieren _nn)
Curve
O
O
H
Bauteilfläche unbeschnitten vor‐
gespannt
Part_Geometry_Untrimmed_Prestressed/Part_Ge‐
ometry_Untrimmed_Prestressed[_*]
Surface
O
O
H
Bauteilfläche beschnitten unge‐
locht vorgespannt
Part_Geometry_Trimmed_Non_Pierced_Prestress
ed/Part_Geometry_Trimmed_Non_Pierced_Prest‐
ressed[_*]
Surface
O
O
H
Fertigteilfläche vorgespannt
Part_Geometry/Part_Geometry_Prestressed[_*]
Surface
O
O
H
Bauteilfläche abgewickelt
Part_Geometry_Unfolded/Part_Geometry_Unfol‐
ded[_*]
Surface
O
O
H
Tailored Blank Flächen
Part_Geometry/Part_Geometry_n,
Part_Geometry/Material_Vector_n,
(n als Zählnummer beginnend mit 2)
Surface,
Line
O
O
S
Tailored Blank Parameter
(Parameter für Material, Materi‐
aldicke, Dichte und Gewicht
werden analog direkt nach dem
Standardelement im Struktur‐
baum angeordnet)
Parameters/material_thickness_n,
Parameters/material_density_n,
Parameters/weight_n,
(n als Zählnummer beginnend mit 2)
Parameter
O
O
–
Haupt-Entformungsrichtung
Main_Tooling_Direction/Main_Tooling_Directi‐
on[_*]
Line
O
O
H
1) Es wird die Struktur der Geometrischen bzw. Parameter Sets mit einem „/“ getrennt aufgelistet. Der letzte Eintrag ist der
Elementname. Unter gleichem Namen erfolgt die Publikation im PCA-CATPart
2) Regelung für ZSB PCAs: Der PartBody muss leer sein. Die beschreibenden Bodies, die die Geometrie enthalten, folgen
dann dieser Nomenklatur.
3) Die Regel des leeren PartBodys gilt aktuell (GRC 5.4.x.) nicht für mit OutGen generierte PCA-ZSB-Adapter (technische
Gründe).
4) Der Materialvektor beginnt auf der Null-Fläche und zeigt in Offsetrichtung die Materialdicke an. (Vektorlänge=100 × Ma‐
terialdicke).


### 第 6 页
Seite 6
VW 01059-6-3: 2015-11
Gussteil (Cast Metal Part)
Tabelle 2 – Elemente bei Gussteilen
Elementbeschreibung
Structure/Elementname1)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Fertigteil Solid
PartBody[_*] 2), 3)
Body mit Eigenschaft PartBody
 
Regelung für ZSB PCAs:
Der PartBody muss leer sein. Die beschreibenden
Bodies, die die Geometrie enthalten, folgen dann
dieser Nomenklatur.
 
Body[_*]
Body
S
S
S
Fertigteil Fläche
Part_Geometry/Part_Geometry[_*]
bzw. bei Zusammenführung mehrerer Teile
Part_Geometry_„Beschreibender_Name“
Surface
P
P
S
Ausformrichtung
Information/Molding_Direction[_*]
Line
P
P
S
Werkzeugtrennlinie
Mold_Parting_Line/Mold_Parting_Line[_*]
Curve
O
P
S
projizierte Fläche
(Sprengfläche vom Werkzeug
Hauptentformungsrichtung)
Projected_Surface/Projected_Surface[_*]
Surface
O
P
H
Bauteilflächen Werkzeuginnen‐
teil ohne Rippen
Inner_Surfaces/Inner_Surfaces_wi‐
thout_Ribs_nn[_*]
Surface
O
S
H
Bauteilflächen Werkzeuginnen‐
teil mit Rippen
Inner_Surfaces/Inner_Surfaces_with_Ribs_nn Sur‐
face[_*]
Surface
O
P
H
Bauteilflächen Werkzeugaußen‐
teil ohne Rippen
Outer_Surfaces/
Outer_Surfaces_without_Ribs_nn[_*]
Surface
O
S
H
Bauteilflächen Werkzeugaußen‐
teil mit Rippen
Outer_Surfaces/Outer_Surfaces_with_Ribs_nn[_*]
Surface
O
P
H
RPS-Elemente
Siehe Tabelle 8 „RPS-Elemente“
LPS-Elemente
Siehe Tabelle 9 „LPS Elemente“
RB-Elemente
Siehe Tabelle 10 „RB-Elemente“
LTA-Elemente
Siehe Tabelle 11 „LTA-Elemente“
Parameter und Property
Siehe Tabelle 12 „Parameter und Property“
Achsensysteme
Siehe Tabelle 13 „Achsensysteme“
Anlage- und Flanschbereiche
Contact_Areas/Contact_Area_"Kontaktbauteil-
Name"
Geometrical
Set,
Curve,
Surface
O
S
Curve
= S
Sur‐
face
= H
Schieberrichtung
Information/Slide_Direction[_*]
(bei mehreren Slide_Direction_01…n_[*]
Line
O
O
S
Bauteilflächen Schieber ohne
Rippen
Slide_Surfaces/Slide_Surfaces_without_Ribs_nn[_*]
Surface
O
O
H
3.2  


### 第 7 页
Seite 7
VW 01059-6-3: 2015-11
Elementbeschreibung
Structure/Elementname1)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Bauteilflächen Schieber mit Rip‐
pen
Slide_Surfaces/Slide_Surfaces_with_Ribs_nn[_*]
Surface
O
O
H
Bauteilflächen, welche durch
mechanisches Bearbeiten ent‐
stehen
Machining/Featurename des selektierten Elements
Surface,
Curve,
Geometrical
Set
O
O
H
1) Es wird die Struktur der Geometrischen bzw. Parameter Sets mit einem „/“ getrennt aufgelistet. Der letzte Eintrag ist der
Elementname. Unter gleichem Namen erfolgt die Publikation im PCA-CATPart.
2) Regelung für ZSB PCAs: Der PartBody muss leer sein. Die beschreibenden Bodies, die die Geometrie enthalten, folgen
dann dieser Nomenklatur.
3) Die Regel des leeren PartBodys gilt aktuell (GRC 5.4.x.) nicht für mit OutGen generierte PCA-ZSB-Adapter (technische
Gründe).
Kunststoffteil (Plastic Part)
Tabelle 3 – Elemente bei Kunststoffteilen
Elementbeschreibung
Structure/Elementname1)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Fertigteil Solid
(ggf. aufgedicktes Flächenmo‐
dell)
PartBody[_*] 2), 3)
Body mit Eigenschaft PartBody
 
Regelung für ZSB PCAs:
Der PartBody muss leer sein. Die beschreibenden
Bodies, die die Geometrie enthalten, folgen dann
dieser Nomenklatur.
 
Body[_*]
 
Body
S4)
S4)
S
Fertigteil Fläche
(einseitige Beschreibung der
konstruierten Seite, Extract aus
Solid möglich)
Part_Geometry/Part_Geometry[_*]
bzw. bei Zusammenführung mehrerer Teile
Part_Geometry_„Beschreibender_Name“
Surface
S4)
S4)
S
Werkzeugtrennlinie
Mold_Parting_Line/Mold_Parting_Line[_*]
Curve
O
S
S
Anlage- und Flanschbereiche
Contact_Areas/Contact_Area_„Kontaktbauteil-
Name“
Geometrical
Set,
Curve,
Surface
O
O
Curve
= S
Sur‐
face
= H
RPS-Elemente
Siehe Tabelle 8 „RPS-Elemente“
LPS-Elemente
Siehe Tabelle 9 „LPS Elemente“
RB-Elemente
Siehe Tabelle 10 „RB-Elemente“
LTA-Elemente
Siehe Tabelle 11 „LTA-Elemente“
Parameter und Property
Siehe Tabelle 12 „Parameter und Property“
Achsensysteme
Siehe Tabelle 13 „Achsensysteme“
3.3  


### 第 8 页
Seite 8
VW 01059-6-3: 2015-11
Elementbeschreibung
Structure/Elementname1)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Entformungsrichtung
Information/Direction_of_Mold_Separation[_*]
Line
O
O
S
Schieberrichtung(en)
Information/Slide_Direction (bei mehreren
Slide_Direction_01…n_[*]
Line
O
O
S
Separierte Teilflächen
Mold_Separation/Fixed_Side und Moving_Side
Surface
O
O
H
1) Es wird die Struktur der Geometrischen bzw. Parameter Sets mit einem „/“ getrennt aufgelistet. Der letzte Eintrag ist der
Elementname. Unter gleichem Namen erfolgt die Publikation im PCA-CATPart.
2) Regelung für ZSB PCAs: Der PartBody muss leer sein. Die beschreibenden Bodies, die die Geometrie enthalten, folgen
dann dieser Nomenklatur.
3) Die Regel des leeren PartBodys gilt aktuell (GRC 5.4.x.) nicht für mit OutGen generierte PCA-ZSB-Adapter (technische
Gründe).
4) Entweder Fertigteil-Solid oder Fertigteil-Fläche, eines von beiden ist Pflicht, das jeweils andere ist damit optional
Profil (Profile Part)
Tabelle 4 – Elemente bei Profilbauteilen
Elementbeschreibung
Structure/Elementname1)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Fertigteil Solid
PartBody[_*] 2), 3)
Body mit Eigenschaft PartBody
 
Regelung für ZSB PCAs:
Der PartBody muss leer sein. Die beschreibenden
Bodies, die die Geometrie enthalten, folgen dann
dieser Nomenklatur.
 
Body[_*]
 
Body
O
S
S
Fertigteilfläche
(Extract aus Solid möglich)
Part_Geometry/Part_Geometry[_*]
bzw. bei Zusammenführung mehrerer Teile
Part_Geometry_„Beschreibender_Name“
Surface
P
P
S
Beschnittkonturen am Profilan‐
fang und Profilende
Boundaries/Outer_Trim_Contour_01[_*]
und
Boundaries/Outer_Trim_Contour_02[_*]
(sind immer 2 Konturen )
Curve
P
P
S
Bauteilgeometrie beschnitten
aber ungelocht
(Extract vom Solid möglich)
Part_Geometry_Trimmed_Non_Pierced/Part_Geo‐
metry_Trimmed_Non_Pierced
Surface
O
S
H
Innere Beschnittkonturen ohne
Standardlöcher
Boundaries/Inner_Trim_Contour_01[_*]
und fortlaufend, ggf. auch Featurename überneh‐
men oder Beschreibung anhängen (ggf. fortlaufend
nummerieren _nn)
Curve
O
O
H
3.4  


### 第 9 页
Seite 9
VW 01059-6-3: 2015-11
Elementbeschreibung
Structure/Elementname1)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Anlage- und Flanschbereiche
Contact _Areas/Contact_Area_"Kontaktbauteil-
Name"
Geometrical
Set,
Curve,
Surface
O
O
Curve
= S
Sur‐
face
= H
RPS-Elemente
Siehe Tabelle 8 „RPS-Elemente“
LPS-Elemente
Siehe Tabelle 9 „LPS Elemente“
RB-Elemente
Siehe Tabelle 10 „RB-Elemente“
LTA-Elemente
Siehe Tabelle 11 „LTA-Elemente“
Parameter und Property
Siehe Tabelle 12 „Parameter und Property“
Achsensysteme
Siehe Tabelle 13 „Achsensysteme“
Werkzeugtrennung für
IHU-Teile
oder
Werkzeugtrennlinie
IHU_Mold_Parting/IHU_Mold_Parting[_*]
oder
IHU_Mold_Parting/IHU_Mold_Parting_Line[_*]
Curve
O
O
S
1) Es wird die Struktur der Geometrischen bzw. Parameter Sets mit einem „/“ getrennt aufgelistet. Der letzte Eintrag ist der
Elementname. Unter gleichem Namen erfolgt die Publikation im PCA-CATPart.
2) Regelung für ZSB PCAs: Der PartBody muss leer sein. Die beschreibenden Bodies, die die Geometrie enthalten, folgen
dann dieser Nomenklatur.
3) Die Regel des leeren PartBodys gilt aktuell (GRC 5.4.x.) nicht für mit OutGen generierte PCA-ZSB-Adapter (technische
Gründe).


### 第 10 页
Seite 10
VW 01059-6-3: 2015-11
Sonstige Teile (Other Parts)
Dieser Bauteiltyp ist nur zulässig, wenn das Bauteil in keine der vorgenannten Bauteilkategorien
hinein passt, z. B. Glasbauteile, Verbundbauteile usw.
Sämtliche im Folgeprozess weiter zu verwendenden Elemente müssen bilateral zwischen Datener‐
zeuger und Datenabnehmer abgestimmt werden. Diese Elemente sind entsprechend im PCA be‐
reitzustellen.
Tabelle 5 – Elemente bei sonstigen Teilen
Elementbeschreibung
Structure/Elementname1)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Fertigteil Solid
(ggf. aufgedicktes Flächenmo‐
dell)
PartBody[_*] 2), 3)
Body mit Eigenschaft PartBody
 
Regelung für ZSB PCAs:
Der PartBody muss leer sein. Die beschreibenden
Bodies, die die Geometrie enthalten, folgen dann
dieser Nomenklatur.
 
Body[_*]
Body
S4)
S4)
S
Fertigteil Fläche
(einseitige Beschreibung der
konstruierten Seite, Extract aus
Solid möglich)
Part_Geometry/Part_Geometry[_*]
bzw. bei Zusammenführung mehrerer Teile
Part_Geometry_„Beschreibender Name“
Surface
S4)
S4)
S
Materialvektor5)
Material_Vector/Material_Vector[_*]
Line
O
O
S
RPS-Elemente
Siehe Tabelle 8 „RPS-Elemente“
 
 
 
 
LPS-Elemente
Siehe Tabelle 9 „LPS Elemente“
 
 
 
 
RB-Elemente
Siehe Tabelle 10 „RB-Elemente“
 
 
 
 
LTA-Elemente
Siehe Tabelle 11 „LTA-Elemente“
 
 
 
 
Parameter und Property
Siehe Tabelle 12 „Parameter und Property“
 
 
 
 
Achsensysteme
Siehe Tabelle 13 „Achsensysteme“
 
 
 
 
1) Es wird die Struktur der Geometrischen bzw. Parameter Sets mit einem „/“ getrennt aufgelistet. Der letzte Eintrag ist der
Elementname. Unter gleichem Namen erfolgt die Publikation im PCA-CATPart.
2) Regelung für ZSB PCAs: Der PartBody muss leer sein. Die beschreibenden Bodies, die die Geometrie enthalten, folgen
dann dieser Nomenklatur.
3) Die Regel des leeren PartBodys gilt aktuell (GRC 5.4.x.) nicht für mit OutGen generierte PCA-ZSB-Adapter (technische
Gründe).
4) Entweder Fertigteil Solid oder Fertigteil Fläche, eines von beiden ist Pflicht, das jeweils andere Element ist damit optio‐
nal.
5) Der Materialvektor beginnt auf der Null-Fläche und zeigt in Offsetrichtung die Materialdicke an. (Vektorlänge=100 × Ma‐
terialdicke).
3.5  


### 第 11 页
Seite 11
VW 01059-6-3: 2015-11
Inhalt eines DMU-CATParts
Ein DMU-CATPart dient zur strukturierten Datenübergabe an den DMU-Prozess.
Im DMU-CATPart dürfen nicht mehr als die in der Tabelle 6 genannte Elemente enthalten sein.
Die in der Tabelle 6 genannten Elemente des DMU-CATParts sind unabhängig vom verwendeten
Material, seiner Bearbeitung (Herstellverfahren) und der angewandten Konstruktionsmethodik ein‐
heitlich zu erzeugen. Die Einstellungen des Parameters „type_of_design“ im Strukturpart haben
keine Auswirkungen auf den Inhalt.
Tabelle 6 – Elemente eines DMU-CATParts
Elementbeschreibung
Geometrical Set Structure/Elementname1)
Elementtyp
Opt./
Pflicht/
Switch
Hide/
Show
Fertigteil Solid
(ggf. aufgedicktes Flächenmo‐
dell oder Zusammenführung
der Tailored Blanks)
PartBody[_*] 2), 3)
Body mit Eigenschaft PartBody
 
Regelung für ZSB DMUs:
Der PartBody muss leer sein. Die beschreibenden
Bodies, die die Geometrie enthalten, folgen dann
dieser Nomenklatur.
Body[_*]
Body
S 4)
S
Fertigteil Fläche
(einseitige Beschreibung der
konstruierten Seite, ggf. mehre‐
re bei Tailored Blanks)
Part_Geometry/Part_Geometry[_*]
Surface
S 4)
S 5)
Achsensystem
(Ursprungsachsensystem 0;0;0)
Siehe Tabelle 13 – Achsensysteme
Materialvektor6)
Material_Vector/Material_Vector[_*]
Line
S 4)
S
Parameter Type of Design
Parameters/type_of_design
Parameter
O
–
Property CAD_Roboter
CAD_Roboter
Property
O
–
1) Es wird die Struktur der Geometrischen bzw. Parameter Sets mit einem „/“ getrennt aufgelistet. Der letzte Eintrag ist der
Elementname.
2) Regelung für ZSB DMUs: Der PartBody muss leer sein. Die beschreibenden Bodies, die die Geometrie enthalten, folgen
dann dieser Nomenklatur.
3) Die Regel des leeren PartBodys gilt aktuell (GRC 5.4.x.) nicht für mit OutGen generierte DMU-ZSB-Adapter (technische
Gründe).
4) Entweder Fertigteil-Solid oder Fertigteil-Fläche ist Pflicht. Das andere Element ist dann optional. Sofern keine Solidgeo‐
metrie geliefert werden kann, ist neben der Fertigteil-Fläche (falls einseitige Flächenableitung) zusätzlich der Materialvek‐
tor in Aufdickungsrichtung zu übergeben.
5) Sofern ein Fertigteil Solid vorhanden ist, muss die dazugehörige Fertigteil-Fläche in das Hide
6) Der Materialvektor beginnt auf der Null-Fläche und zeigt in Offsetrichtung die Materialdicke an. (Vektorlänge=100 × Ma‐
terialdicke). Ggf. mehrere bei Tailored Blanks)
4  


### 第 12 页
Seite 12
VW 01059-6-3: 2015-11
Inhalte von optionalen Adapterparts
Adapter „Oxx_TMT“ für 3DZP
Dieser Adapter wird separat mit der Applikation OutGen erzeugt und einzeln im KVS gespeichert.
Er dient zur Erzeugung von 3DZP-Dokumenten im KVS.
Tabelle 7 – Elemente bei Adapter 3DZP
Elementbeschreibung
Structure/Elementname1)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Hilfsgeometrie
FTA_Auxiliary_Geometry
Geometrical
Set
S
S
S
PMI
Annotation Set.1
Annotation
P
P
S
 
Captures
Annotation Set.1/Captures
Capture
P
P
S
Ansichten
Annotation Set.1/Views
View
P
P
S
Bezugselemente
Annotation Set.1/Datums
Datum
S
S
S
Bezugsrahmen
Annotation Set.1/Reference Frames
Reference
Frame
S
S
S
Form- und Lagetoleranzen
Annotation Set.1/Geometrical Tolerances
Geometrical
Tolerance
S
S
S
Bemaßungen
Annotation Set.1/Dimensions
Dimension
S
S
S
Rauheiten
Annotation Set.1/Roughness
Roughness
S
S
S
Annotationen
Annotation Set.1/Notes
Note
S
S
S
Begrenzte Fläche
Annotation Set.1/Restricted Areas
Restricted
Area
S
S
S
Konstruktionsgeometrie
(FTA-spezifische Hilfsgeo‐
metrie)
Annotation Set.1/Construction geometries
Construction
geometry
S
S
S
zusätzliche Elemente zu Pro‐
duktdokumentation (frühe
Zeichnungsinhalte)
siehe Tabelle 1 bis Tabelle 5 je nach Bauteilart2)
1) Es wird die Struktur der Geometrischen bzw. Parameter Sets mit einem „/“ getrennt aufgelistet. Der letzte Eintrag ist der
Elementname. Unter gleichem Namen erfolgt die Publikation im PCA-CATPart.
2) Damit der 3DZP-Prozess alle Informationen erhalten kann, müssen alle weiteren Informationen je Bauteilart nach der
PCA-Vorgaben ebenfalls mitgeliefert werden.
5  
5.1  


### 第 13 页
Seite 13
VW 01059-6-3: 2015-11
Allgemeingültige Tabellen
Tabelle 8 – RPS-Elemente
Elementbeschreibung
Structure/Elementname1)
Structure/Beispiel 2)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
RPS-Elemente sind grundsätzlich über die VW 01055 genormt. Die VW 01059-6-3 zeigt lediglich die zu benutzenden
Strukturelemente auf.
RPS-Elemente
RPS_Elements/RPS_001_Hxy
Geometrisches Set mit nachfolgend definiertem Inhalt
(alle weiteren RPS-Elemente werden fortlaufend num‐
meriert, die Aufnahme-Richtungen werden im Namen
des Geometrical Set bzw. in den Elementnamen ver‐
merkt [VW 01055 5)])
Geometrical
Set
O
S
S
 
RPS -Punkt
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_Point
Point
– 4)
S
 
RPS-Ebene
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_Plane
Plane
– 4)
H
 
RPS-Aufnahmerichtung
(im RPS-Punkt in Off‐
setrichtung
(L = 10 × Materialdi‐
cke))
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_
Hole_Direction
Line
– 4)
S
 
RPS-Achsen
(Mittelachsen des RPS-
Punkt bzw. RPS-Flä‐
che)
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_
Centerline_1
und
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_
Centerline_2
Line
– 4)
S
 
RPS -Fläche
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_Surface
Surface
– 4)
S
 
RPS-Boundaries
RPS_Elements/RPS_001_Fy/RPS_001_Fy_
Boundary_Inner
und
RPS_Elements/RPS_001_Fy/RPS_001_Fy_
Boundary_Outer
und
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_
Boundary_Hole
Curve
– 4)
H
 
RPS-Annotations
Annotation Set.1/Notes/RPS_01_Hxy
Annotation
– 4)
 
RPS-Feature
RPS_Features/RPS_001_Hxy
Feature (CAA
RPS)
O
O
S
RPS-Tabelle
Tables/RPS_Table”index”
(Index kann ggf. durch Teilenummer ersetzt werden)
Feature (CAA
RPS)
O
O/P3)
–
RPS-System
RPS_Systems/*
Feature (CAA
RPS)
O
O
H
Parameter
RPS-Abmessung A
(bzw. Durchmesser)
RPS_Elements/RPS_001_Hxy/Size_A
Parameter
O
O
–
6  


### 第 14 页
Seite 14
VW 01059-6-3: 2015-11
Elementbeschreibung
Structure/Elementname1)
Structure/Beispiel 2)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Parameter
RPS-Abmessung B
(bzw. Dicke der Ringflä‐
che)
RPS_Elements/RPS_001_Hxy/Size_B
Parameter
O
O
–
Parameter RPS-Beschrei‐
bung
RPS_Elements/RPS_001_Hxy/Description
Parameter
O
O
–
1) Es wird die Struktur der Geometrischen bzw. Parameter Sets mit einem „/“ getrennt aufgelistet. Der letzte Eintrag ist der
Elementname. Unter gleichem Namen erfolgt die Publikation im PCA-CATPart.
2) Kursiv geschriebene Structure/Elementnamen sind Beispiele
3) Für die 3DZP Nutzung Pflicht
4) Ergibt sich aus der Steuerung des übergeordneten RPS-Elementes
5) Die Benennung in der z. Z. gültigen VW01055 (Stand 2009-06) weicht ab, eine Änderung der VW 01055 hinsichtlich
dieser Diskrepanz ist eingeleitet. Anwendung der hier gezeigten Namenskonvention erfolgt auf eigene Gefahr. Master ist
die VW 01055.
Tabelle 9 – LPS Elemente
Elementbeschreibung
Structure/Elementname1)
Structure/Beispiel 2)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
LPS-Elemente sind grundsätzlich über die VW 01055 genormt. Die VW 01059-6-3 zeigt lediglich die zu benutzenden
Strukturelemente auf.
LPS-Elemente
LPS_Elements/L04_001_Hxy
Geometrisches Set mit nachfolgend definiertem In‐
halt (alle weiteren LPS-Elemente werden fortlaufend
nummeriert, die Aufnahme-Richtungen werden im
Namen des Geometrical Set bzw. in den Elementna‐
men vermerkt [VW 01055 5)])
Geometrical
Set
O
S
S
 
LPS -Punkt
LPS_Elements/L04_001_Hxy/L04_001_Hxy_Point
Point
– 4)
S
 
LPS-Ebene
LPS_Elements/L04_001_Hxy/L04_001_Hxy_Plane
Plane
– 4)
H
 
LPS-Aufnahmerichtung
(im LPS-Punkt in Offset‐
richtung
(L = 10 × Materialdicke))
LPS_Elements/L04_001_Hxy/L04_001_Hxy_
Hole_Direction
Line
– 4)
S
 
LPS-Achsen
(Mittelachsen des LPS-
Punkt bzw. LPS-Fläche)
LPS_Elements/L04_001_Hxy/L04_001_Hxy_
Centerline_1
und
LPS_Elements/L04_001_Hxy/L04_001_Hxy_
Centerline_2
Line
– 4)
S
 
LPS -Fläche
LPS_Elements/L04_001_Hxy/L04_001_Hxy_Surface
Surface
– 4)
S


### 第 15 页
Seite 15
VW 01059-6-3: 2015-11
Elementbeschreibung
Structure/Elementname1)
Structure/Beispiel 2)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
 
LPS-Boundaries
LPS_Elements/L04_001_Fy/L04_001_Fy_
Boundary_Inner
und
LPS_Elements/L04_001_Fy/L04_001_Fy_
Boundary_Outer
und
LPS_Elements/L04_001_Hxy/L04_001_Fy_Hxy_
Boundary_Hole
Curve
– 4)
H
LPS-Annotations
Annotations Set.1/Notes/L04_01_Hxy
Annotation
O
S
S
LPS-Feature
LPS_Features/L04_01_Hxy
Feature (CAA
LPS)
O
O
S
LPS-Tabelle
Tables/LPS_Table”index”
(Index kann ggf. durch Teilenummer ersetzt werden)
Feature (CAA
LPS)
O
O/P3)
–
LPS-System
LPS_Systems/*
Feature (CAA
LPS)
O
O
H
Parameter
LPS-Abmessung A
(bzw. Durchmesser)
LPS_Elements/L04_001_Hxy/Size_A
Parameter
O
O
–
Parameter
LPS-Abmessung B
(bzw. Dicke der Ringfläche)
LPS_Elements/L04_001_Hxy/Size_B
Parameter
O
O
–
Parameter LPS-Beschrei‐
bung
LPS_Elements/L04_001_Hxy/Description
Parameter
O
O
–
1) Es wird die Struktur der Geometrischen bzw. Parameter Sets mit einem „/“ getrennt aufgelistet. Der letzte Eintrag ist der
Elementname. Unter gleichem Namen erfolgt die Publikation im PCA-CATPart.
2) Kursiv geschriebene Structure/Elementnamen sind Beispiele
3) Für die 3DZP Nutzung Pflicht
4) Ergibt sich aus der Steuerung des übergeordneten LPS-Elementes
5) siehe VW 01055


### 第 16 页
Seite 16
VW 01059-6-3: 2015-11
Tabelle 10 – RB-Elemente
Elementbeschreibung
Structure/Elementname1)
Structure/Beispiel 2)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
RB-Elemente sind grundsätzlich über die VW 01055 genormt. Die VW 01059-6-3 zeigt lediglich die zu benutzenden
Strukturelemente auf.
RB-Elemente
RB_Elements/RB_001_Hxy
Geometrisches Set mit nachfolgend definiertem In‐
halt (alle weiteren RB-Elemente werden fortlaufend
nummeriert, die Aufnahme-Richtungen werden im
Namen des Geometrical Set bzw. in den Elementna‐
men vermerkt [VW 01055 5)])
Geometrical
Set
O
S
S
 
RB -Punkt
RB_Elements/RB_001_Hxy/RB_001_Hxy_Point
Point
– 4)
S
 
RB-Ebene
RB_Elements/RB_001_Hxy/RB_001_Hxy_Plane
Plane
– 4)
H
 
RB-Aufnahmerichtung
(im RB-Punkt in Offset‐
richtung
(L = 10 × Materialdicke))
RB_Elements/RB_001_Hxy/RB_001_Hxy_
Hole_Direction
Line
– 4)
S
 
RB-Achsen
(Mittelachsen des RB-
Punkt bzw. RB-Fläche)
RB_Elements/RB_001_Hxy/RB_001_Hxy_
Centerline_1
und
RB_Elements/RB_001_Hxy/RB_001_Hxy_
Centerline_2
Line
– 4)
S
 
RB -Fläche
RB_Elements/RB_001_Hxy/RB_001_Hxy_Surface
Surface
– 4)
S
 
RB-Boundaries
RB_Elements/RB_001_Fy/RB_001_Fy_
Boundary_Inner
und
RB_Elements/RB_001_Fy/RB_001_Fy_
Boundary_Outer
und
RB_Elements/RB_001_Hxy/RB_001_Hxy_
Boundary_Hole
Curve
– 4)
H
RB-Annotations
Annotations Set.1/Notes/RB_01_Hxy
Annotation
O
S
S
RB-Feature
RB_Features/RB_001_Hxy
Feature (CAA
RB)
O
O
S
RB-Tabelle
Tables/RB_Table”index”
(Index kann ggf. durch Teilenummer ersetzt werden)
Feature (CAA
RB)
O
O/P3)
–
RB-System
RB_Systems/*
Feature (CAA
RB)
O
O
H
Parameter
RB-Abmessung A
(bzw. Durchmesser)
RB_Elements/RB_001_Hxy/Size_A
Parameter
O
O
–
Parameter
RB-Abmessung B
(bzw. Dicke der Ringfläche)
RB_Elements/RB_001_Hxy/Size_B
Parameter
O
O
–


### 第 17 页
Seite 17
VW 01059-6-3: 2015-11
Elementbeschreibung
Structure/Elementname1)
Structure/Beispiel 2)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Parameter RB-Beschreibung RB_Elements/RB_001_Hxy/Description
Parameter
O
O
–
1) Es wird die Struktur der Geometrischen bzw. Parameter Sets mit einem „/“ getrennt aufgelistet. Der letzte Eintrag ist der
Elementname. Unter gleichem Namen erfolgt die Publikation im PCA-CATPart.
2) Kursiv geschriebene Structure/Elementnamen sind Beispiele
3) Für die 3DZP Nutzung Pflicht
4) Ergibt sich aus der Steuerung des übergeordneten RB-Elementes
5) RB findet in der z. Z. gültigen VW01055 (Stand 2009-06) keine Erwähnung, eine Änderung der VW 01055 hinsichtlich
der Benennung ist eingeleitet.
Tabelle 11 – LTA-Elemente
Elementbeschreibung
Structure/Elementname1)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
LTA-Elemente
Hole_Elements/LTA_„Index“_„Beschreiben‐
der_Name“
(„Index“ als fortlaufende Loch-/Lochelemente-Num‐
merierung, Syntax Kleinbuchstaben + Ziffern)
Geometrical
Set
O
O
S
Loch-Mittelpunkt
Hole_Elements/LTA_„Index“
_„Beschreibender_Name“/LTA_„Index“_Point
Point
– 3)
S
Loch-Austrittspunkt
Hole_Elements/LTA_„Index“
_„Beschreibender_Name“/LTA_“Index“_ThickPoint
Point
– 3)
H
Loch-Achsen
Hole_Elements/LTA_„Index“
_„Beschreibender_Name“/LTA_„Index“_Centerline_1
und
Hole_Elements/LTA_„Index“
_„Beschreibender_Name“/LTA_„Index“_Centerline_2
Line
– 3)
S
Loch-Richtung
(Locherzeugungs-Rich‐
tung)
Hole_Elements/LTA_„Index“
_„Beschreibender_Name“/LTA_“Index“_Hole_
Direction
Line
– 3)
S
Loch-Boundary
Hole_Elements/LTA_„Index“
_„Beschreibender_Name“/LTA_„Index“_Bounda‐
ry_Hole
Curve
– 3)
S
Loch-Annotations
Annotations Set.1/Notes/LTA_„Index“
Annotation
O
S
S
Loch-Feature
LTA_Features/LTA_”Index”
Feature (CAA
LTA)
O
O
S
Loch-Tabelle
Tables/LTA_Table„Index“
(Index kann ggf. durch Teilenummer ersetzt werden)
Feature (CAA
LTA)
O
O/P2)
–
1) Es wird die Struktur der Geometrischen bzw. Parameter Sets mit einem „/“ getrennt aufgelistet. Der letzte Eintrag ist der
Elementname. Unter gleichem Namen erfolgt die Publikation im PCA-CATPart.
2) Ist bei Nutzung von LTA für die 3DZP Nutzung Pflicht, sofern keine RPS-Tabelle vorliegt
3) Ergibt sich aus der Steuerung des übergeordneten LTA-Elementes


### 第 18 页
Seite 18
VW 01059-6-3: 2015-11
Tabelle 12 – Parameter und Property
Elementbeschreibung
Structure/Elementname1)
Structure/Beispiel 2)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Parameter und Property
 
Parameter Material
Parameters/material
Parameter
P
P
–
 
Parameter Dichte
Parameters/material_density
Parameter
O
P
–
 
Parameter Materialdicke
Parameters/material_thickness
Parameters/material_thickness_”index” 3)
Parameter
P
P
–
 
Parameter Gewicht
Parameters/weight
Parameter
O
P
–
 
Parameter Type of De‐
sign
Parameters/type_of_design
Parameter
P
P4)
–
 
Parameter Symmetrie
Parameters/symmetry
Parameter
P
P
–
 
Parameter right-
hand_part_number
Parameters/right-hand_part_number
Parameter
O
O
–
 
Property CAD_Roboter
CAD_Roboter
Property
O
O
–
1) Es wird die Struktur der Geometrischen bzw. Parameter Sets mit einem „/“ getrennt aufgelistet. Der letzte Eintrag ist der
Elementname. Unter gleichem Namen erfolgt die Publikation im PCA-CATPart.
2) Kursiv geschriebene Structure/Elementnamen sind Beispiele.
3) Index beginnt mit „2“
4) Entfällt bei Gußteilen
Tabelle 13 – Achsensysteme
Elementbeschreibung
Geometrical Set Structure/Elementname1)
Elementtyp
Ent‐
wurf
Frei‐
gabe
Hide/
Show
Achsensysteme
 
Achsensystem
(Ursprungsachsensystem
0;0;0)
Axis Systems/Absolute Axis System
Axis System
P
P
H
Zusätzliche Achsensyste‐
me
Axis Systems/“Ursprungsbenennung“[_*]
Axis System
O
P
H
1) Es wird die Struktur der Geometrischen bzw. Parameter Sets mit einem „/“ getrennt aufgelistet. Der letzte Eintrag ist der
Elementname. Unter gleichem Namen erfolgt die Publikation im PCA-CATPart.


### 第 19 页
Seite 19
VW 01059-6-3: 2015-11
Mitgeltende Unterlagen
Die folgenden in der Norm zitierten Dokumente sind zur Anwendung dieser Norm erforderlich:
VW 01055
Referenz-Punkt-Systematik (RPS); Angaben in Zeichnungen und 3D-
CAD-Modellen
VW 01059-6
Anforderungen an CAD/CAM-Daten - CAD-System CATIA V5-6
VW 01059-6-1
Anforderungen an CAD/CAM-Daten - CAD-System CATIA V5-6 - Teil 1:
Begriffe
VW 01059-6-4
Anforderungen an CAD/CAM-Daten - CAD-System CATIA V5-6 - Teil 4:
Produktstrukturen für die PDA TM
7  

