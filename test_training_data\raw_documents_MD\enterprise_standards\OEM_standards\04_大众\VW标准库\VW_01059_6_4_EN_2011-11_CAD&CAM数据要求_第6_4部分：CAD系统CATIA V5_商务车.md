# VW_01059_6_4_EN_2011-11_CAD&CAM数据要求_第6_4部分：CAD系统CATIA V5_商务车.pdf

## 文档信息
- 标题：
- 作者：
- 页数：19

## 文档内容
### 第 1 页
Group standard
VW 01059-6-4
Issue 2015-11
Class. No.:
22632
Descriptors:
CAD, CAM, CATIA, KVS, TM, KPR, ZSB, DMU, OUT, Product structure, PCA, IPP
Requirements for CAD/CAM Data – CATIA V5-6 CAD System
Part 4: Product Structures for Product Data Type TM
Previous issues
VW 01059-6 Supplement 4: 2006-12
Changes
The following changes have been made to VW 01059-6 Supplement 4: 2006-12:
–
Status of the document changed from supplement to standard
–
Standard title changed
–
CAD system changed from CATIA V5 to V5-6
–
Technical responsibility changed
–
Standard completely revised, expanded, and restructured
–
Area of application expanded. Old version: Supplement was only valid for Volkswagen Com‐
mercial Vehicles brand. New version: Standard valid for the entire Volkswagen Group.
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Page 1 of 19
Technical responsibility
The Standards department
K-SIPE-2/3
Stefan Biernoth
Tel.: +49 5361 9 48896
EKDV/4 Norbert Wisla
EKDV
Tel.: +49 5361 9 48869
Maik Gummert
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Page 2
VW 01059-6-4: 2015-11
Contents
Page
Scope ......................................................................................................................... 2
General rules for the OUT adapters and the OUT products of the root product. ....... 3
OUT adapters of the root product .............................................................................. 3
DMU CATPart of the root product .............................................................................. 3
PCA CATPart of the root product (PCA = process chain adapter) ............................. 3
Oxx CATPart of the root product ................................................................................ 4
OUT CATProduct of the root product ......................................................................... 4
OUT CATProduct of the root product with a DMU CATPart ...................................... 5
OUT CATProduct of the root product with a PCA CATPart ....................................... 5
OUT CATProduct of the root product with two or more OUT adapters ...................... 5
Product structures ...................................................................................................... 6
Design-product (KPR) product structures for individual parts .................................... 6
KPR product structure with an INP CATProduct, GEO CATProduct, and OUT
CATProduct ................................................................................................................ 6
KPR product structure with a GEO CATPart and OUT CATProduct ......................... 7
KPR product structure with a GEO CATProduct and OGNDeclaration ..................... 7
ASSY product structures for assemblies .................................................................... 8
ASSY product structure with multiple part numbers ................................................... 8
ASSY Product structure with one part number (e.g., purchase ASSY) .................... 14
Product structure for drawing derivations ................................................................. 17
Intellectual property protection ................................................................................. 18
Saving with IPP under product data type TM ........................................................... 18
Characteristic of IP protection status "extract" ......................................................... 18
Characteristic of IP protection status "generate" ...................................................... 18
IP protection status "copy" ....................................................................................... 18
IP protection status "stop" ........................................................................................ 19
Saving with IPP under product data type TZ ............................................................ 19
Information about the rules for converting adapters after V4. .................................. 19
Applicable documents .............................................................................................. 19
1
2
2.1
2.1.1
2.1.2
2.1.3
2.2
2.2.1
2.2.2
2.2.3
3
3.1
3.1.1
3.1.2
3.1.3
3.2
3.2.1
3.2.2
3.3
4
4.1
4.1.1
4.1.2
4.1.3
4.1.4
4.2
5
6
Scope
This standard supplements Volkswagen standard VW 01059-6 for product structures with CAD
type KPR, ZSB, or assembly components Z01 to Z99, and the product data type (PDA) TM (part
model) in the root product.
The product structures described here apply to individual parts and assemblies, as well as to their
use in the intellectual property protection (IPP) process (see section 4) and their use in the creation
of drawing derivations (see section 3.3). Product data type TZ (part drawing) is also addressed in
this context.
When IPP is used, the CATIA V5-6 product structure in the CAD system must be differentiated
from the result that is persistently saved in HyperKVS under the target product data type TM or TZ.
Section 3 describes how CATIA V5-6 product structures must be structured in the CAD system.
Section 4 describes how the various IPP mechanisms affect the result in HyperKVS.
1  


### 第 3 页
Page 3
VW 01059-6-4: 2015-11
General rules for the OUT adapters and the OUT products of the root product.
The OUT adapters of the root product must be arranged under the OUT product of the root prod‐
uct.
OUT adapters of the root product (section 2.1 to section 2.2) and the OUT product of the root prod‐
uct (section 2.2.1 to section 2.2.3) may each only be instanced once in the product structure.
OUT adapters of the root product
DMU CATPart of the root product
The DMU-CATPart of the root product is the CATPart that is automatically converted to a CAT‐
IA V4 model in HyperKVS. This model is then made available to the Virtual Product Model (VPM).
The DMU CATPart of the root product contains the complete geometry of the root product that is
relevant to Digital Mock-Up (DMU) (VW 01059-6-3).
The DMU CATPart of the root product is characterized by the following:
–
It has CAD type DMU.
–
It is named as per figure 1.
–
It is arranged under the OUT CATProduct associated with the root product (see section 2.2).
–
It may only be included once in the product structure.
Legend
1
Identical to the root product
2
Standard for the DMU CATPart
Figure 1 – Example showing the designation of the DMU CATPart of the root product
PCA CATPart of the root product (PCA = process chain adapter)
The PCA CATPart of the root product contains the part-describing geometry of the root product
and the information needed for manufacturing (see VW 01059-6-3).
The PCA CATPart of the root product is characterized by the following:
–
It has CAD type PCA.
–
It is named as per figure 2.
–
It is arranged under the OUT CATProduct associated with the root product (see section 2.2).
–
It may only be included once in the product structure.
2  
2.1  
2.1.1  
2.1.2  


### 第 4 页
Page 4
VW 01059-6-4: 2015-11
Legend
1
Identical to the root product
2
Standard for the PCA CATPart
Figure 2 – Example showing the designation of the PCA CATPart of the root product
Oxx CATPart of the root product
The Oxx CATPart of the root product is a special adapter. It contains the geometry and information
agreed upon with the data receiver (see VW 01059-6-3).
The Oxx CATPart of the root product is characterized by the following:
–
It has CAD type O01 to O99.
–
It is named as per figure 3.
–
It is arranged under the OUT CATProduct associated with the root product (see section 2.2).
–
The combination of the part number, CAD type, product data type, version, and substructure
level 1 (Ust-E1) may only occur once within the root product structure.
Legend
1
Identical to the root product
2
Standard for the Oxx CATPart (from O01 to O99)
3
Standard for the Oxx CATPart (from O01 to O99)
Figure 3 – Example showing the designation of the Oxx CATPart of the root product
OUT CATProduct of the root product
The OUT CATProduct of the root product contains all of the OUT-CATParts relevant to the root
product (section 2.1.1 to section 2.1.3).
2.1.3  
2.2  


### 第 5 页
Page 5
VW 01059-6-4: 2015-11
The possible combinations of OUT adapters below the OUT CATProduct are explained in more de‐
tail in section 2.2.1 to section 2.2.3.
The OUT CATProduct of the root product is characterized by the following:
–
It has CAD type OUT.
–
It is named as per figure 4.
–
It contains at least one OUT adapter with the geometry relevant to the root product DMU (the
DMU CATPart as per section 2.1.1 and/or the PCA CATPart as per section 2.1.2).
–
It can contain other OUT adapters as per section 2.1.3.
–
It is arranged on the first structure level below the root product.
–
It may only be included once in the product structure.
Legend
1
Identical to the root product
2
Standard for the OUT CATProduct
Figure 4 – Example showing the designation of the OUT CATProduct
OUT CATProduct of the root product with a DMU CATPart
In this use case, only the DMU CATPart is located below the OUT CATProduct as per
section 2.1.1
OUT CATProduct of the root product with a PCA CATPart
Only the PCA CATPart is located below the OUT CATProduct as per section 2.1.2.
OUT CATProduct of the root product with two or more OUT adapters
In this use case, two or more OUT adapters as per section 2.1.2 to section 2.1.3 are located below
the OUT CATProduct.
Either the DMU CATPart as per section 2.1.1 or the PCA CATPart as per section 2.1.2 must be
present.
If both the DMU CATPart as per section 2.1.1 and the PCA CATPart as per section 2.1.2 are
present, then the DMU CATPart is used to provide data to the VPM.
2.2.1  
2.2.2  
2.2.3  


### 第 6 页
Page 6
VW 01059-6-4: 2015-11
Product structures
The following sections describe the product structures of individual parts (KPR) and assemblies
(ZSB, Z01 to Z99, KPR).
An assembly is defined by its assembly code in the bill of materials (BOM). If an assembly does
not have an assembly code in the bill of materials, it must be handled as an individual part and its
root product contains the CAD type KPR.
The following applies to all product structures that, after all saving procedures have been comple‐
ted, persistently remain as the result in HyperKVS under the product data type TM:
–
Exactly one DMU CATPart as per section 2.1.1 or exactly one PCA CATPart as per
section 2.1.2 must be present.
–
Additional OUT adapters (as per section 2.1.2 to section 2.1.3) may be present.
Design-product (KPR) product structures for individual parts
KPR product structures must contain an OUT CATProduct (as per section 2.2.1 to section 2.2.3) or
an OGNDeclaration1).
KPR product structure with an INP CATProduct, GEO CATProduct, and OUT
CATProduct
–
This product is structured as per the input–process–output (IPO) principle.
–
Input: Store all reference data and input adapters under the INP CATProduct.
–
Processing: Create all design data under the GEO CATProduct.
–
Output: Generate all results data under the OUT CATProduct.
Legend
1
Root product
2
Example structure
3
OUT CATProduct of the root product as per section 2.2
Figure 5 – Example of a KPR product structure (INP CATProduct, GEO CATProduct, OUT
CATProduct)
3  
3.1  
3.1.1  
1)
Note: The user can use the CAA application "OutGen" to declare (prepare) an OUT CATProduct in the KPR product structure. CAT‐
IA stores an "OGNDeclaration" feature in the root CATProduct for this purpose. The OUT CATProduct is automatically generated
during persistent saving in the engineering data management system (KVS).


### 第 7 页
Page 7
VW 01059-6-4: 2015-11
KPR product structure with a GEO CATPart and OUT CATProduct
Legend
1
Root product
2
Example structure
3
OUT CATProduct of the root product as per section 2.2
Figure 6 – Example of a KPR product structure (GEO CATPart, OUT CATProduct)
KPR product structure with a GEO CATProduct and OGNDeclaration
–
This KPR product structure is saved in KVS under the product data type TM without OUT
CATParts but with an OGNDeclaration.
–
The OUT CATProduct is automatically generated in KVS with one or more OUT CATParts as
per the OGNDeclaration.
Legend
1
Root product
2
Example structure
Figure 7 – Example of a KPR product structure with OGNDeclaration
3.1.2  
3.1.3  


### 第 8 页
Page 8
VW 01059-6-4: 2015-11
ASSY product structures for assemblies
–
The root product of an ASSY product structure must have CAD type ZSB or Z01 to Z99.
–
ASSY product structures with multiple part numbers are differentiated from ASSY product
structures with only one part number.
–
The ASSY product structure must have an OUT CATProduct (see section 2.2) or an OGNDe‐
claration.
–
For very large ASSY product structures (e.g., HVAC unit, seat) with CAD type ZSB, the DMU
CATPart for the ASSY root product as per section 2.1.1 and section 2.2 can be omitted if it is
archived in HyperKVS and if the part owner agrees. However, if the ASSY is DMU-relevant as
per the BOM or for the subsequent process, then a separate DMU CATPart must be archived
in HyperKVS under the product data type TMU (geometry prepared for DMU) , or an envelope
geometry (CGR, stl, etc.) must be archived in HyperKVS under product data type THS (part
envelope, static) or TDH (part envelope, dynamic), and it must be assigned to the respective
design version in either case.
ASSY product structure with multiple part numbers
–
Individual parts and/or pre-assemblies also have their own part numbers.
–
The individual parts are available as CATParts or with a KPR product structure.
ASSY product structure as per BOM
–
The structure follows the BOM structure.
–
The CAD type of the root product can be ZSB (figure 8) or Z01 to Z99 (figure 9).
–
It can also contain pre-assemblies.
–
If an RPS is used, an ASSY info part (ZIN) CATPart 2) is required. If the assembly RPS is de‐
fined from RPS elements of various individual parts, then a ZIN CATPart is required. If the as‐
sembly RPS is identical to the RPS of an individual part, then the ZIN CATPart can be omitted.
3.2  
3.2.1  
*******  
2)
See RPS methodology guide


### 第 9 页
Page 9
VW 01059-6-4: 2015-11
Legend
1
Root product
2
Example structure
3
OUT CATProduct of the root product as per section 2.2
Figure 8 – Example of an ASSY product structure as per BOM with CAD type ZSB


### 第 10 页
Page 10
VW 01059-6-4: 2015-11
Legend
1
Root product
2
Example structure
3
OUT CATProduct of the root product as per section 2.2
Figure 9 – Example of an ASSY product structure as per BOM with CAD type Z01


### 第 11 页
Page 11
VW 01059-6-4: 2015-11
ASSY product structure as per motion groups
–
The ASSY is used for kinematic or elastokinematic motions.
–
The CAD type of the root product of this product structure can be ZSB (figure 10) or Z01 to
Z99 (figure 11).
Legend
1
Root product
2
Example structure
3
OUT CATProduct of the root product as per section 2.2
Figure 10 – Example of an ASSY product structure as per motion groups with CAD type ZSB
3.2.1.2  


### 第 12 页
Page 12
VW 01059-6-4: 2015-11
Legend
1
Root product
2
Example structure
3
OUT CATProduct of the root product as per section 2.2
Figure 11 – Example of an ASSY product structure as per motion groups with CAD type Z02


### 第 13 页
Page 13
VW 01059-6-4: 2015-11
ASSY product structure as per BOM and motion groups
Legend
1
Root product
2
Example structure
3
OUT CATProduct of the root product as per section 2.2
Figure 12 – Example of an ASSY product structure as per BOM and motion groups
*******  


### 第 14 页
Page 14
VW 01059-6-4: 2015-11
ASSY product structure as a flat structure
–
All individual parts are arranged directly below the root product.
–
If an RPS is used, a ZIN CATPart is required.
Legend
1
Root product
2
Example structure
3
OUT CATProduct of the root product as per section 2.2
Figure 13 – Example of a flat ASSY product structure with multiple part numbers
ASSY Product structure with one part number (e.g., purchase ASSY)
–
The part number is identical for all structure elements (CATParts and CATProducts).
–
The individual structure elements differ in their CAD type, and perhaps in designation and
comment.
–
The structure can be flat or have the following substructures: products with CAD type Z01 to
Z99 or motion groups with CAD type M01 to M99.
*******  
3.2.2  


### 第 15 页
Page 15
VW 01059-6-4: 2015-11
ASSY product structure as a flat structure
–
If an RPS is used, a ZIN CATPart is required.
Legend
1
Root product
2
Example structure
3
OUT CATProduct of the root product as per section 2.2
Figure 14 – Example of a flat ASSY product structure with one part number
*******  


### 第 16 页
Page 16
VW 01059-6-4: 2015-11
ASSY product structure with following substructures: products with CAD type Z01 to Z99
–
Substructuring created by products with CAD type Z01 to Z99
–
If an RPS is used, a ZIN CATPart is required.
Legend
1
Root product
2
Example structure
3
OUT CATProduct of the root product as per section 2.2
Figure 15 – Example of an ASSY product structure with substructure
3.2.2.2  


### 第 17 页
Page 17
VW 01059-6-4: 2015-11
ASSY product structure with following substructures: products with CAD type M01 to
M99
–
Substructuring is created by products with CAD type M01 to M99
Legend
1
Root product
2
Example structure
3
OUT product of the root product as per section 2.2
Figure 16 – Example of a flat ASSY product structure as per motion groups with one part number
Product structure for drawing derivations
The product structures described in section 3.1 and section 3.2 can be used to derive drawings.
Product structures that serve exclusively for drawing derivation are governed by VW 01059-6-6
(currently being drafted).
*******  
3.3  


### 第 18 页
Page 18
VW 01059-6-4: 2015-11
Intellectual property protection
Intellectual property protection (IPP) is an IT-based method deployed in KVS to protect know-how.
There are two different IPP methods: one for target product data type TM (section 4.1) and one for
the target product data type TZ (section 4.2).
The original scopes are divided according to public portions and protected portions.
Only the public scope remains under the target product data type.
The original scope remains in the associated product data type GES (protected).
In KVS, there is a link between the target product data TM or TZ and the product data type GES.
Saving with IPP under product data type TM
If the root product has the product data type TM and the CAD type KPR or ZSB and if the IP pro‐
tection status has the correct value, then IPP can be carried out in KVS.
VALIDAT determines the IP protection status. The IPP characteristic is applied in KVS depending
on the status and content of the output data sets.
If the property "VWG_IP_PROTECTION" in the root product has the value OFF, then IPP is not
carried out in KVS.
Characteristic of IP protection status "extract"
This IP protection status means that exactly one of the following OUT portions is already present:
–
OUT product as per section 2.2.1 (only the OUT CATPart remains in the target product data
type TM)
–
OUT product as per section 2.2.2 (only the OUT CATPart remains in the target product data
type TM)
–
OUT product as per section 2.2.3 (only the OUT CATProduct remains in the target product da‐
ta type TM)
–
OUT CATPart as per section 2.1.1 (only the OUT CATPart remains in the target product data
type TM)
–
OUT CATPart as per section 2.1.2 (only the OUT CATPart remains in the target product data
type TM)
The original scope remains in the associated product data type GES.
Characteristic of IP protection status "generate"
This IP protection status means that the public scope of the design has been declared using the
OUTGEN program and is initially generated automatically by the mechanisms of IPP in the KVS
environment.
The result follows the rules specified in section 4.1.1.
IP protection status "copy"
This IP protection status means that no IPP is applied.
The original scope remains in the target product data type TM.
4  
4.1  
4.1.1  
4.1.2  
4.1.3  


### 第 19 页
Page 19
VW 01059-6-4: 2015-11
IP protection status "stop"
This IP protection status means that at least one VALIDAT criterion has not been fulfilled or that
the root product has structural errors. The VALIDAT test is thus KO.
Saving with IPP under product data type TZ
No IP protection status is required for IPP with product data type TZ. This process is controlled ex‐
clusively by the presence of the linked generation geometries. It is irrelevant here whether the gen‐
eration geometries are individual CATParts or product structures.
Provided a CATDrawing with linked generation geometry is saved in KVS under the target product
data type TZ, only the public CATDrawing remains under the target product data type TZ.
The original scope is saved in the associated product data type GES.
Information about the rules for converting adapters after V4.
The following conversion rules apply to CATProducts in the Geometric Interface Navigator (GI‐
NA)/Group Reference Installation Conversion Server (GRICOS) environment. There is no differen‐
tiation according to CAD types.
–
If there is a DMU CATPart (as per section 2.1.1) but no PCA CATPart (as per section 2.1.2),
then the DMU CATPart is converted.
–
If there is a PCA CATPart (as per section 2.1.2) but no DMU CATPart (as per section 2.1.1),
then the PCA CATPart is converted.
–
If there is a DMU CATPart (as per section 2.1.1) and a PCA CATPart (as per section 2.1.2),
then either the DMU CATPart or the PCA CATPart is converted. Which one is converted de‐
pends on the priority control, which can be configured individually for each conversion method.
–
If there is no DMU CATPart (as per section 2.1.1) and no PCA CATPart (as per section 2.1.2),
then the entire CATProduct is converted.
The presence of the DMU CATPart alone is decisive for V4 -> V5 migration. If the DMU CATPart is
missing, no migration is carried out.
Applicable documents
RPS Methodology Guideline
https://eportal.wob.vw.vwg/jct_ep/web/zusammenarbeit-mit-partnern/Methoden_Prozesse/
4.1.4  
4.2  
5  
6  

