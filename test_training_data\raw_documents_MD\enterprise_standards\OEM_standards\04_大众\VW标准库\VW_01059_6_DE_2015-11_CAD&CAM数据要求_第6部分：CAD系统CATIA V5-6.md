# VW_01059_6_DE_2015-11_CAD&CAM数据要求_第6部分：CAD系统CATIA V5-6.pdf

## 文档信息
- 标题：
- 作者：
- 页数：14

## 文档内容
### 第 1 页
Konzernnorm
VW 01059-6
Ausgabe 2015-11
Klass.-Nr.:
22632
Schlagwörter:
CAD, CAM, CATIA, HyperKVS
Anforderungen an CAD/CAM-Daten – CAD-System CATIA V5-6
Frühere Ausgaben
VW 01059-6: 2005-07, 2006-12, 2007-03, 2008-02, 2008-03, 2009-05, 2010-04, 2010-11, 2012-06
Änderungen
Gegenüber der VW 01059-6: 2012-06 wurden folgende Änderungen vorgenommen:
–
Version des CAD-Systems CATIA von V5 in V5-6 geändert
–
Fachverantwortung geändert
–
Normative Änderung: Status der Beiblätter zu dieser Norm von Beiblatt in eine Norm geändert
–
Abschnitt 2: zwei<PERSON> hinzugefügt
–
Abschnitt 3: <PERSON><PERSON><PERSON>, <PERSON><PERSON>: Text „in dem sie die VWGRCLite einzusetzen, siehe
Abschnitt 3.4“ hinzugefügt
–
Abschnitt 3.1: dritter und vierter Spiegelstrich: „optional“ durch „falls erforderlich“ ersetzt; fünf‐
ter Spiegelstrich: Texte „für den parametrischen Umfang“ und „zum parametrischen Umfang“
hinzugefügt
–
Abschnitt 3.2.2: letzter Spiegelstrich vor dem Bild 1 hinzugefügt; Bild 1: „Bezeichnung“ durch
„Benennung“ ersetzt; Tabelle 1: bei Kinematik „KIN-Part (kinematic part)“ durch „Kinematik
Geometrie“ und „spezielle KIN-Parts“ durch „Kinematik Geometrie differenziert“ ersetzt; bei Ki‐
nematik → KIN → CATProduct „-“ auf „+“ geändert; letzter Absatz mit der Ausnahme vor der
Tabelle 1 entfernt
–
Abschnitt 3.2.3.1: Satz mit dem Verweis auf „Auftrag gebende Fachabteilung“ entfernt
–
alter Abschnitt 3.3 entfernt, nachfolgende Abschnitte umnummeriert
–
Abschnitt 3.4 „Voreinstellungen (Settings)“: Satz mit VWGRCLite-Verpflichtung hinzugefügt;
Verweis auf Engineering Portal und zugehörigen Text entfernt; Satz über „Startpaket für die
Betriebsmittelkonstruktion und Methodenplanung vom Audi“ entfernt
–
Abschnitt 3.5.1 „Fahrzeug-Einzelteile“: erste zwei Absätze neu, Absatz über „Fahrzeug-Teile‐
modellen, die als CATPart ins HyperKVS gespeichert werden“ entfernt; im dritten Absatz über
„Fahrzeug-Teilemodelle, die durch direkte Archivierung oder durch IPP-Prozesse ...“: Text
Norm vor Anwendung auf Aktualität prüfen.
Die elektronisch erzeugte Norm ist authentisch und gilt ohne Unterschrift.
Seite 1 von 14
Fachverantwortung
Normung
K-SIPE-2/3
Stefan Biernoth
Tel.: +49 5361 9-48896
EKDV/4 Norbert Wisla
EKDV
Tel.: +49 5361 9-48869
Maik Gummert
Alle Rechte vorbehalten. Weitergabe oder Vervielfältigung ohne vorherige Zustimmung einer Normenabteilung des Volkswagen Konzerns nicht gestattet.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Seite 2
VW 01059-6: 2015-11
„Produktdatenart TM“ durch „(Ziel)-PDA TM“ ersetzt; fünfter Absatz umd Verweis auf VW
01059-6-4 ergänzt; Bild 2 gändert; letzter Satz im Abschnitt entfernt
–
Abschnitt 3.5.2 „Zeichnungsdokumente (CATDrawing)“: erster Satz um Text „den CATDra‐
wings“ und zweiter Satz um Text „der den CATDrawings“ ergänzt
–
Abschnitt 3.6 „Genauigkeiten“: erster Absatz über Genauigkeitsvorgaben von VW 01059-2
entfernt; im zweitem Absatz: Text „u.a. mit Generative Shape Design“ entfernt
–
Abschnitt 3.7 „LTA-Elemente“: Textformulierung abgeändert
–
Abschnitt 3.11 „Zeichnungsableitung“: zweiter Spiegelstrich: Text „Nur Datensätze mit Update“
durch „Nur aktualisierte (Update) Datensätze“ ersetzt; vierter Spiegelstrich Text „Volkswagen-
Audi-Standard-Rahmen nach VW 01014“durch Text „Zeichnungsrahmen für Entwicklung nach
VW 01014“ ersetzt; achter Spiegelstrich: Text „(gilt für die Marken Volkswagen und Volkswa‐
gen Nutzfahrzeuge, andere Marken können DoLittle auch nutzen)“ entfernt
–
Abschnitt 4: Mitgeltende Unterlagen aktualisiert
–
Abschnitt 5: Literaturverzeichnis aktualisiert
Inhalt
Seite
Anwendungsbereich ................................................................................................... 3
Abkürzungen und Begriffe .......................................................................................... 3
Anforderungen ........................................................................................................... 3
Allgemeine Hinweise .................................................................................................. 3
Namenskonvention .................................................................................................... 4
Generelle Festlegungen ............................................................................................. 4
Namenskonventionen der Technischen Entwicklung ................................................. 5
Namenskonvention der Produktion ............................................................................ 7
Infrastruktur ................................................................................................................ 8
Voreinstellungen (Settings) ........................................................................................ 8
Speichern im HyperKVS ............................................................................................ 8
Fahrzeug-Einzelteile .................................................................................................. 9
Zeichnungsdokumente (CATDrawing) ....................................................................... 9
Genauigkeiten .......................................................................................................... 10
LTA-Elemente .......................................................................................................... 10
RPS-Elemente ......................................................................................................... 10
Link-Management .................................................................................................... 10
Layer ........................................................................................................................ 11
Zeichnungsableitung ................................................................................................ 11
Skizzierer (Sketcher) ................................................................................................ 12
Power Copy und User Defined Feature (UDF) ........................................................ 12
Knowledge-Ware ...................................................................................................... 12
CAA-Applikationen ................................................................................................... 12
Mitgeltende Unterlagen ............................................................................................ 13
Literaturverzeichnis .................................................................................................. 13
1
2
3
3.1
3.2
3.2.1
3.2.2
3.2.3
3.3
3.4
3.5
3.5.1
3.5.2
3.6
3.7
3.8
3.9
3.10
3.11
3.12
3.13
3.14
3.15
4
5


### 第 3 页
Seite 3
VW 01059-6: 2015-11
Anwendungsbereich
Diese Norm enthält Vorgaben zum Arbeiten mit dem CAD-System CATIA V5-6. Zusätzliche Regel‐
ungen sind in den folgenden Normen festgelegt:
–
VW 01059-6-1 enthält für CAD-System CATIA V5-6 spezifische Begriffe.
–
VW 01059-6-2 enthält Ergänzungen und Abweichungen für die Elektrik-Entwicklung.
–
VW 01059-6-3 enthält Ergänzungen zu den Prozesskettenadaptern PCA und DMU CATPart.
–
VW 01059-6-4 enthält Ergänzungen zu Produktstrukturen für die PDA TM.
–
VW 01059-6 Beiblatt 5 „Anforderungen an CAD/CAM/PDM - CAD-System CATIA Version 5 -
CONNECT“ enthält Ergänzungen für CAD-System CATIA V5 – CONNECT.
Anmerkung: Nachfolge Norm VW 01059-6-5 Anforderungen an CAD/CAM/PDM - CAD-Sys‐
tem CATIA V5-6 - Teil 5: CONNECT (zur Zeit der Veröffentlichung im Entwurfsstadium).
Abkürzungen und Begriffe
Abkürzungen und Begriffe siehe VW 01059-6-1.
CATIA-spezifische Begriffe sind kursiv geschrieben.
Anforderungen
Volkswagen Konzerngesellschaften sind verpflichtet, die aktuelle Volkswagen CATIA Konzern-Re‐
ferenz zu verwenden.
Partnerfirmen sind verpflichtet, die bereitgestellten Standard- und Settingseinstellungen des aktuel‐
len CATIA/ENOVIA Releaselevels des Volkswagen Konzerns zu verwenden, indem sie die
VWGRCLite einsetzen, siehe Abschnitt 3.4. Die eingesetzten Lizenzen müssen kompatibel zu der
von Volkswagen eingesetzten Lizenzkonfiguration sein. Weiterführende Lizenzkonfigurationen sind
bilateral mit dem Auftrag gebenden Fachbereich abzustimmen.
Allgemeine Hinweise
Die Konstruktion erfordert eine sorgfältige Planung, bevor mit der Geometrieerzeugung begonnen
wird, sowie eine sorgfältige Durchführung. Folgende Punkte müssen beachtet werden:
–
Das 3D-Modell ist vollständig detailliert als Solid-, Flächenmodell oder Kombination aus bei‐
dem auszuführen. Es muss im parametrisch/assoziativen CATIA V5-6 Format erstellt sein. Al‐
le zum Verändern oder Steuern des Modells notwendigen CATIA V5-6 Parameter und Kon‐
struktionselemente müssen vorhanden sein. Abweichungen hiervon erfordern eine begründete
schriftliche Vereinbarung. Betriebsmittelkonstruktionen sind ausschließlich als Solidkonstrukti‐
on zu erstellen; ausgenommen davon sind die eingebundenen Fahrzeugdaten, Adapter, Steu‐
erparts und Skelette.
–
Für die technische Entwicklung gilt:
–
Für konzerninterne Konstruktionsumfänge ist das Konzern-Strukturpart zu verwenden.
–
Systemlieferanten dürfen darüber hinaus das OEM-Startpart für ihre Konstruktionsumfän‐
ge verwenden. In diesem Fall ist durch den Systemlieferanten ein Prozesskettenadapter
(falls erforderlich ein DMU-CATPart) an den Auftraggeber zu liefern. Die genauen Vorga‐
ben werden durch das Bauteil- Lastenheft geregelt.
–
Konstruktionsunterstützende Entwicklungspartner müssen im Regelfall das Konzern-
Strukturpart verwenden. Sie dürfen das OEM-Startpart nur in Abstimmung mit ihren
1  
2  
3  
3.1  


### 第 4 页
Seite 4
VW 01059-6: 2015-11
Bauteilabnehmern/Bauteilverantwortlichen für den parametrischen Umfang einsetzen. In
diesem Fall ist vom Entwicklungspartner zusätzlich zum parametrischen Umfang ein Pro‐
zesskettenadapter (falls erforderlich ein DMU-CATPart) an den Auftraggeber zu liefern.
Die genauen Vorgaben werden durch das Bauteil-Lastenheft geregelt.
–
Es sind die Methodiken der Fachbereiche sowie die Normen, d.h. die Teile zu dieser Norm zu
beachten.
–
Vor dem persistenten Speichern eines Datensatzes im KVS sind nicht mehr benötigte Elemen‐
te zu löschen. Verbindungen zu Katalogen und Projektordnern müssen getrennt werden.
–
CATIA V5-6 CATParts sollten eine Größe von 150 MByte nicht überschreiten, da ansonsten
z. B. Probleme bei der Konvertierung in andere Datenformate auftreten können.
–
Das rechtshändige Ursprungsachsensystem (von CATIA generiert bzw. in freigegebenen
Struktur-Parts oder Master-Modellen vorhanden) muss seine Koordinatenachsen im absoluten
Ursprung des CATParts (X, Y und Z = 0) haben, darf in seiner Lage nicht verändert und nicht
umbenannt werden.
–
Alle Achsensysteme müssen unsichtbar (hide) sein, bevor die Datei ins HyperKVS gespeichert
wird.
AUSNAHME: Für Dokumente der PDA BM, MP und TMP dürfen Achsensysteme sichtbar
(show) bleiben.
–
CATIA V5-6 Settings müssen so eingestellt sein, dass CATParts nicht mit dem „Hybrid Design
Modus“ erzeugt werden können.
Namenskonvention
Generelle Festlegungen
–
Dateinamen dürfen nur Großbuchstaben, Ziffern, Bindestrich und Unterstrich enthalten [A bis
Z, 0 bis 9, -, _ ]. Leerzeichen, Umlaute und Sonderzeichen sind nicht zulässig. Leerzeichen
sind durch Unterstriche zu ersetzen und Punkte durch Bindestrich.
–
Der Dateiname ohne Dateierweiterung darf max. 70 Zeichen haben.
–
Der Dateiname darf nicht auf Betriebssystemebene umbenannt werden.
–
Fehlende Zeichen werden durch Unterstriche „_“ aufgefüllt.
–
Die einzelnen Namensteile werden durch Unterstriche getrennt.
–
PartNumber und Dateiname (FileName) müssen gleich sein, der InstanceName kann ggf. ab‐
weichen (Abweichungen sind mit dem Auftraggeber vorher abzustimmen).
–
Umlaute und nationale Sonderzeichen sind für Benennungen generell nicht zulässig.
ANMERKUNG 1 : Zur Sicherstellung nachfolgend benannter und weiterer Namenskonventionen
soll die CATIA V5-6 Zusatzapplikation NTool der Volkswagen AG eingesetzt werden. Diese ist in‐
nerhalb des Volkswagen Konzerns und für alle Zulieferer und Entwicklungspartner verfügbar und
setzt alle gegenwärtig bekannten und vorgeschriebenen Namenskonventionen als jeweils eigen‐
ständiges Profil um.
ANMERKUNG 2 : Sheet-Namen dürfen nur Buchstaben, Ziffern, Bindestrich, Punkt und Unter‐
strich enthalten [A bis Z, a bis z, 0 bis 9, -, _ , .]. Leerzeichen, Umlaute und Sonderzeichen sind
nicht zulässig.
3.2  
3.2.1  


### 第 5 页
Seite 5
VW 01059-6: 2015-11
Namenskonventionen der Technischen Entwicklung
Für freizugebende CAD-Daten der Technischen Entwicklung, ausgenommen der Elektrik-Entwick‐
lung (siehe VW 01059-6-2) sind bezüglich der Namensgebung (Beispiel in Bild 1), zusätzlich zu
den generellen Festlegungen folgende Vorgaben zu beachten:
–
Die Trennung der Teilenummer erfolgt durch Unterstriche „_“.
–
Bei Verwendung der (KVS) Unterstruktur Ebene 1 ist im Dateinamen die entsprechende Num‐
mer (3 Ziffern) einzutragen, andernfalls werden im Dateinamen drei Unterstriche eingetragen.
–
Normteile erhalten nur die (13- oder 14-stellige) Teilenummer. Beispiele: N___123_456_78,
N___023_456_7, 111_222_333_AA. Sie werden ausschließlich durch die Normung EKDV/3
festgelegt.
–
Bei der Vergabe von Dateinamen ist sicherzustellen, dass die einzelnen Attributwerte im Da‐
teinamen den Attributwerten des HyperKVS-Schlüssels entsprechen, zu welchem der Daten‐
satz zugeordnet wird. Besonders zu berücksichtigen sind die Attribute:
–
Teilenummer
–
PDA
–
(KVS-) Dokumentversion und
–
(KVS-) Unterstruktur (Ebene 1: Alternative, Blatt)
–
Wird eine Datumsangabe im Namen gemacht, so ist sie im Format JJJJ_MM_TT oder
JJMMTT auszuführen und muss an Stelle 61-70 im Namen stehen Beispiel:
0ZZ_000_000____GEO_TM__001_____SHEET_METAL_PART_____________2012_06_06
Bild 1 – Namenskonvention der TE
3.2.2  


### 第 6 页
Seite 6
VW 01059-6: 2015-11
HINWEIS 1 zur Tabelle 1: Zurzeit vereinbarte CAD-Typen (für CATPart, CATProduct, CATDrawing
und CATAnalysis) sind in der Tabelle 1 zusammengestellt.
Bei Verwendung einer CATProduct-Struktur für die Bauteilkonstruktion ist der CAD-Typ KPR und
für die Baugruppenkonstruktion der CAD-Typ ZSB im Rootproduct zu nutzen.
HINWEIS 2 zur Tabelle 1: Die Tabelle gibt einen Überblick, wann welcher CAD-Typ zulässig ist.
Die genaue Verwendung der CAD-Typen ist in den CAD-Handbüchern bzw. Methodik-Unterlagen
der Marken und Fachbereiche geregelt.
Tabelle 1 – CAD-Typen und ihre Verwendung
Verwendung
CAD-Typ
Kurzbeschreibung
CATIA V5-6 Dokument
CATPro‐
duct
CATPart
CATDra‐
wing
Bauteil
KPR
Konstruktionsprodukt
+
–
– (+)
Input
INP
Inputgeometrie allgemein
+
+
–
I01-I99
Inputgeometrie differenziert
–
+
–
A01-A99
spezielle Adapter
–
+
–
SKE
Skelett
+
+
–
S01-S99
Skelett differenziert
–
+
–
Geometrie
GEO
Konstruktionsdaten allgemein
+
+
– (+)
G01-G99 Konstruktionsdaten differenziert
–
+
– (+)
Verbindungs‐
technik
VER
Root-Fügegruppe
+
–
–
VEG
Fügegruppe
+
–
–
VEE
Fügeelement/-bauteil
–
+
–
Output
OUT
Outputgeometrie allgemein
+
–
– (+)
O01-O99 Outputgeometrie differenziert
–
+
– (+)
DMU
DMU-Adapter
–
+
– (+)
PCA
Prozesskettenadapter
–
+
– (+)
Drawing
DRW
Teilzeichnung
+
–
+
Baugruppe
ZSB
Root für Baugruppe
+
–
– (+)
Z01-Z99
Unterbaugruppe des ZSB
+
–
– (+)
ZIN
Adapter für ZSB-Zusatzinformationen
(z. B. RPS)
–
+
–
Kinematik
KIN
Kinematik Geometrie
+
+
–
K01-K99
Kinematik Geometrie differenziert
–
+
–
R01-R99
vereinfachte Geometrie-Repräsentati‐
on
–
+
–
M01-M99 Bewegungsgruppen
+
–
–
Legende:
+ Zuweisung erlaubt
– Zuweisung verboten
(+) Verweis auf das Dokument, das als Quelle für die Zeichnung verwendet wurde


### 第 7 页
Seite 7
VW 01059-6: 2015-11
Tabelle 2 – zusätzliche CAD-Typen und ihre Verwendung
Verwendung
CAD-Typ
Kurzbeschreibung
CATIA V5-6 Dokument
CATAnalysis
FEM
CAE
CAE-Dokument
+
Legende:
+ Zuweisung erlaubt
– Zuweisung verboten
(+) Verweis auf das Dokument, das als Quelle für die Zeichnung verwendet wurde
AUSNAHME nach Zustimmung durch den Auftrag gebenden Fachbereich: Wird unter der PDA
TMG ein kompletter Datenumfang (2D mit 3D) gespeichert, ist für die Root-Datei CATDrawing die
Kombination DRW (CAD-Typ) und TZ (PDA) zulässig.
ANMERKUNG 3 : Die Bauteil-Geometrieerzeugung darf nur in der Geometriestruktur erfolgen.
Genaueres regeln die Methodiken der Fachbereiche und Marken.
ANMERKUNG 4 : Andere CAD-Typen als DRW für ein CATDrawing sind eingeschränkt zulässig.
Die Verwendung der mit (+) gekennzeichneten CAD-Typen gibt dabei an, welches Dokument die
Basis für die Zeichnung ist. Genaueres dazu regelt eine Methodik des jeweiligen Fachbereiches.
Namenskonvention der Produktion
Methodenplanung
Die Namenskonvention für Methodenpläne ist beschrieben durch die
–
39D 22000 [5] – „CAD-Richtlinie zur Methodenplanerstellung mit CATIA V5“.
Diese Richtlinie ist verbindlich für die Marken Volkswagen und Audi sowie für Fremdfirmen, die im
Auftrag dieser Marken arbeiten.
ANMERKUNG 5 : Die Richtlinie dient als Basis für die durchgängige Gestaltung des Methoden‐
plans mit dem CAD-System CATIA V5-6. Damit wird eine durchgängige Verwendbarkeit der Daten
für nachfolgende Abteilungen sichergestellt. Sie ergänzt für den Bereich Methodenplanung diese
Norm und die Audi-Richtlinie 1D300046 [7].
Betriebsmittelkonstruktion
Die Namenskonvention für die unterschiedlichen Sparten der Betriebsmittelkonstruktion wird fach‐
bereichsspezifisch geregelt. Folgende Richtlinien sind vom jeweiligen Auftraggeber zu beziehen:
Konstruktion von Anlagen und Vorrichtungen:
–
1D3119 [2] – Ergänzungsrichtlinie für die Konstruktion von Anlagen und Vorrichtungen mit CA‐
TIA V5 (Audi)
–
39D 22001 [6] – Basisrichtlinie für die Konstruktion von Anlagen und Vorrichtungen mit CATIA
V5 (Facharbeitskreis Anlagen- und Vorrichtungskonstruktion mit CATIA V5 der deutschen Au‐
tomobilindustrie)
–
39D 22002 [1] – Ergänzungsrichtlinie für die Konstruktion von Anlagen und Vorrichtungen mit
CATIA V5
3.2.3  
3.2.3.1  
3.2.3.2  


### 第 8 页
Seite 8
VW 01059-6: 2015-11
Konstruktion von Presswerkzeugen:
–
39D 944 [3] – Basisrichtlinie für die Konstruktion von Presswerkzeugen mit CATIA V5 (Fachar‐
beitskreis Betriebsmittelkonstruktion mit CATIA V5 der Deutschen Automobilindustrie)
–
39D 945 [4] – Ergänzungsrichtlinie für die Konstruktion von Presswerkzeugen mit CATIA V5
(Audi, Volkswagen)
Infrastruktur
Die Software CATIA V5-6 darf nur in englischer Sprache ausgeführt werden.
Das Betriebssystem kann in der jeweils nationalen Sprachumgebung verbleiben.
CATIA V5-6 Dokumente dürfen nur in der CATIA-Umgebung mit CATIA-Funktionen kopiert und
umbenannt werden (z. B. „Save Management“, „New from“, „Send To“ oder „Save As“ oder ver‐
gleichbare Applikationen), und nicht mit dem Explorer/Dateimanager oder dem copy-Befehl auf Be‐
triebssystemebene.
Voreinstellungen (Settings)
Die aktuellen Standardumgebungen des Volkswagen Konzerns sind zu verwenden.
Für Partnerfirmen gilt:
Ab der GRC 4.5.0 ist es für Entwicklungspartner verpflichtend die VWGRCLite einzusetzen.
Quelle: http://www.vwgroupsupply.com.
Wenn für bestimmte Benutzergruppen (z. B. Fachabteilungen, Teams) weiterführende Verände‐
rungen an den Settings vorgenommen werden, so sind diese dem entsprechenden Konstruktions‐
partner mitzuteilen.
Speichern im HyperKVS
Datenaustausch darf nur über HyperKVS, die HyperKVS-Zwischenablage oder über OFTP erfol‐
gen (siehe HyperKVS CATIA V5 Datenübertragung; Hinweise für Partnerfirmen).
VALIDAT prüft die Einhaltung der Forderungen dieser Norm. Ohne eine „OK“-Datenqualitätsprü‐
fung mit VALIDAT und passendem Prüfprofil ist ein persistentes Speichern in bestimmten PDA im
KVS nicht möglich.
Im KVS muss bei der persistenten Speicherung in Unterstrukturen der entsprechende Unterstruk‐
turtyp gewählt und die Nummer (gemäß der Angabe im Dateinamen) eingetragen werden.
Für die PDA TM ist der (KVS) Unterstrukturtyp „Alternative“, für die PDA TZ der (KVS) Unterstruk‐
turtyp „Blatt“ zu nutzen.
Abweichungen hiervon, sowie die Nutzung kombinierter (KVS) Unterstrukturebenen (z. B. „Alterna‐
tive“ und „Blatt“) erfordern eine dokumentierte Methodik einer Marke oder einer Konzernstelle.
3.3  
3.4  
3.5  


### 第 9 页
Seite 9
VW 01059-6: 2015-11
Fahrzeug-Einzelteile
Für Fahrzeug-Einzelteile ist die Abgabe des DMU-Adapters nach Norm VW 01059-6-3für jedes
freizugebene Bauteil unter PDA TM verpflichtend. Der DMU-Adapter allein ist aber in der Regel
nicht für eine Freigabe ausreichend.
Diese Verpflichtung entfällt, sofern der PCA-Adapter exakt der Beschreibung nach Norm
VW 01059-6-3 entspricht.
Fahrzeug-Teilemodelle, die durch direkte Archivierung oder durch IPP-Prozesse als CATProduct
unter der (Ziel)-PDA TM im HyperKVS verbleiben, müssen zur Absicherung des weiteren Prozes‐
ses in der Produktstruktur genau einen DMU-Adapter und weitere in der VW 01059-6-3 definierte
Output-Adapter enthalten (siehe Bild 2 ).
Weitere zu erstellende Adapter oder über die in der VW 01059-6-3 genannten Elemente hinausge‐
hende Informationen sind bilateral abzustimmen.
Weitere Informationen zum Inhalt und der Verwendung von den Output-Adaptern sind den Normen
VW 01059-6-3 und VW 01059-6-4 zu entnehmen.
Bild 2 – Beispiel einer KPR-Struktur
Die Produktstruktur ist entsprechend den Konstruktionsvorgaben des Fachbereiches (Konstrukti‐
onsmethodik) erweiterbar. Es können zusätzliche Unterstrukturebenen in Form von CATProducts
und CATParts erstellt werden.
Die Links gehen von oben nach unten (z. B. von den Konstruktionsdaten zu den Outputdaten).
Kreuzreferenzen sind nicht zulässig.
Zeichnungsdokumente (CATDrawing)
Werden abgeleitete Zeichnungsdokumente (CATDrawing) im HyperKVS gespeichert, so sind die
den CATDrawings zugrunde liegenden 3D-CAD-Dokumente (CATPart  oder CATProduct) eben‐
falls mit bereitzustellen. Eine Weiter- bzw. Wiederverwendbarkeit ist sicherzustellen.
Gegenwärtig ist eine Bereitstellung mehrerer CATDrawings  und der den CATDrawings zugrunde
liegenden 3D-CAD-Daten in nur einem einzigen TAR-Archiv nur dann zulässig, wenn vorher ent‐
sprechende Links zu den CATDrawings vom Anwender manuell gesetzt werden. Für die Marken‐
gruppe Audi gilt: Es darf pro TAR-Archiv nur ein CATDrawing geben.
Weitere Hinweise die Zeichnungsdokumente selbst betreffend werden in Abschnitt 3.11 gegeben.
3.5.1  
3.5.2  


### 第 10 页
Seite 10
VW 01059-6: 2015-11
Genauigkeiten
Bei der Gestaltung von Flächen wird mit der voreingestellten Genauigkeit gearbeitet:
Lageabweichung
≤ 0,001 mm
Winkelabweichung (Tangentenstetigkeit)
≤ 0,5 Grad
„tolerant modelling“ ist so einzusetzen, dass die Toleranzen für das fertige Bauteil eingehalten
werden.
Für topologische Mengen (z. B. Addition von Flächen mit der Funktion Join) sind folgende Ge‐
nauigkeiten zugelassen:
Lageabweichung
≤ 0,01 mm
Winkelabweichung (Tangentenstetigkeit)
≤ 0,5 Grad
Für Mischdaten, Daten von CATIA V5-6 und migrierte Fremddaten (aus CATIA V4, IGES, STEP
usw.) ist eine Genauigkeit von 0,02 mm für Lücken und 1 Grad Tangentenstetigkeit zugelassen.
Die Verwendung von Übernahmegeometrien ist immer bilateral zu klären.
Zum Speichern von Daten in das HyperKVS sind die mit der Volkswagen-Referenz verteilten vor‐
gegebenen Darstellungsgenauigkeiten einzuhalten (Tools, Options, General, Display, Performan‐
ces):
3D accuracy
Default = Fixed 0.20
Curves’ accuracy ratio
Default = 1.00 x 3D accuracy
2D accuracy
Default = Fixed 0.02
LTA-Elemente
Das Erzeugen von Funktionslöchern in der Konstruktion wird durch die Applikation „LTA“ (Loch-
Tool-Applikation) unterstützt.
RPS-Elemente
RPS-Elemente sind nach VW 01055 zu strukturieren. Dies wird durch die Applikation „RPS“ (Refe‐
renz-Punkt-Systematik) unterstützt.
Link-Management
Es sind nur publizierte Geometrien und Parameter zu verlinken.
Nachfolgend genannte Angaben gelten für Dokumente, die im HyperKVS archiviert werden.
Zulässig sind:
–
Links zu Design Tables
–
Instance-Links zu CATPart, CATProduct, cgr-Files
–
KWE-Links zu CATPart, CATProduct, CATDrawing, CATProcess, CATAnalysis
–
Hyperlinks zu CATIA V5-6 Dokumenten
–
CCP-Links zu CATParts
–
Import Context Links zu CATParts / CATProducts
–
View-Link, Sub Catalog-Link
–
Components ohne Unterstruktur
3.6  
3.7  
3.8  
3.9  


### 第 11 页
Seite 11
VW 01059-6: 2015-11
Unzulässig sind:
–
Links in jeder Form zu CATIA V4 Model-Dateien.
Ausnahme: Die Verwendung von CATIA V4 Modellen eingebunden in eine CATIA V5-6 Pro‐
duktstruktur (CATProduct) ist nur im eigenen Projektumfeld zulässig. CATProducts, die im Hy‐
perKVS archiviert werden, dürfen keine V4 Modelle enthalten. Context-Links zu einem CATIA
V4 Modell innerhalb einer Produktstruktur sind unzulässig. Entsprechende Links bzw. CAT‐
Parts müssen isoliert werden. V5-6 Zeichnungsableitungen aus CATIA V4 Modellen sind nur
dann zulässig, wenn anschließend jeder View-Link bzw. das ganze CATDrawing isoliert wird.
–
Material Link
–
OLE Link (drawing),
–
Components mit darunter befindlichen physischen Dokumenten oder weiteren Components,
–
Catalog File component Links, ausgenommen in Katalogen.
Layer
Die Verwendung von Layern ist untersagt. Ausnahmen sind nur nach Absprache mit dem Fachbe‐
reich möglich. Wenn Layer verwendet werden, muss der Filter beim Speichern so eingestellt
werden, dass alle Elemente sichtbar sind.
Zeichnungsableitung
–
Vor dem Update der Zeichnung muss das CATPart oder CATProduct aktualisiert worden sein.
–
Nur aktualisierte (Update) Datensätze dürfen ins HyperKVS gespeichert werden.
–
Bevor das CATDrawing ins HyperKVS gespeichert wird, müssen die aktualisierten Views ge‐
sperrt (lock) werden.
–
Zeichnungsrahmen sind grundsätzlich im Hintergrund (background) und mit Maßstab (scale)
1:1 des entsprechenden Sheet zu erzeugen. Details (z. B. Wiederholtexte) werden auf einem
separaten Detail Sheet erstellt und auf dem entsprechenden Zeichnungsblatt instanziert
(Zeichnungsrahmen für Entwicklung nach VW 01014 werden in einem Catalog und per Makro
zur Verfügung gestellt).
–
Zeichnungen mit Lieferantenschutz siehe VW 01058
–
Externe Links zu Catalog-Elementen dürfen nicht vorhanden sein (Expose 2D component).
–
Es dürfen nur die CEG1- oder CEG2-Konfigurationen verwendet werden. Wichtigstes Unter‐
scheidungsmerkmal: CEG1 hat 0,35 mm sichtbare Körperkanten, CEG2 verwendet 0,5 mm
sichtbare Körperkanten.
–
Das Format eines jeden in der Zeichnung verwendeten Blattes muss in seinen Abmaßen so
eingestellt werden, dass es deckungsgleich mit dem verwendeten Zeichnungsrahmen ist. Der
relevante Zeichnungsinhalt ist so anzuordnen, dass er vollständig in den Blattgrenzen enthal‐
ten ist.
–
Zweisprachige CAD-Zeichnungen in Deutsch und Englisch sind mit dem Programm „DoLittle“
auszuführen. Die Zweisprachigkeit regelt die Norm VW01058. DoLittle unterstützt bei der Er‐
stellung mehrsprachiger CAD-Konstruktionen auf Basis eines standardisierten, Volkswagen
spezifischen Textkataloges. Änderungen der mit DoLittle erstellten Textinhalte dürfen nur über
das Team Technische Übersetzungen, EKDD/5, abgewickelt werden. Änderungen dieser Tex‐
tinhalte in den CAD-Daten ohne Verwendung von DoLittle sind nicht zulässig.
3.10  
3.11  


### 第 12 页
Seite 12
VW 01059-6: 2015-11
Werden Änderungen an CATIA V4 Zeichnungen nach Abschaltung von CATIA V4 in CATIA V5-6
durchgeführt, muss dies geschehen durch:
–
Migration der 3D-Daten und Neuerstellung der Zeichnung unter CATIA V5-6 oder
–
Migration der Zeichnung. Diese Zeichnung ist mit dem Vermerk „aus CATIA V4 migriert durch
GRICOS / migrated from CATIA V4 by GRICOS“ im Feld „CAD-System und Verwaltungssys‐
tem-Schlüssel“ zu kennzeichnen.
Welche Arbeitsweise im Einzelfall wirtschaftlicher ist, muss vom Fachbereich entschieden werden.
Die genaue Arbeitsweise zur Migration einer Zeichnung regelt das „Handbuch V4-Migration“ [8].
Skizzierer (Sketcher)
Skizzen (Sketches) müssen vollständig definiert werden. Zur Entkopplung von den Standard H-
und V-Richtungen sind die sog. „Positioned Sketches“ zu verwenden.
Fahrzeug-Normteile stellen eine Ausnahme dar; sie sind nicht parametrisiert.
Power Copy und User Defined Feature (UDF)
Power Copies können frei verwendet werden. Gegebenenfalls werden diese vom Auftraggeber zur
Verfügung gestellt.
Die Verwendung von UDF ist mit dem Fachbereich abzustimmen. Im Fall einer Anwendung müs‐
sen im Zuge der Datenabgabe bei der Volkswagen AG sämtliche Feature Definition Files mitgelie‐
fert werden, andernfalls sind die Daten unbrauchbar und werden abgelehnt.
ANMERKUNG 6 : Bei der Erzeugung eines UDF wird die Interface-Definition im sog. CATGScript
abgelegt. Diese steht dann im „Directory of File Types“.
Entsprechende CATParts müssen mit SendTo / Directory vollständig exportiert werden, da nur so
eine Weiterverwendung der Daten gewährleistet ist.
Knowledge-Ware
Rules, Checks und Formulas können frei verwendet werden. Weitere verwendete Knowledge-Wa‐
re-Elemente sind wegen zusätzlich zu nutzender Lizenzen bei der Weiterverwendung mit dem
Fachbereich abzustimmen.
CAA-Applikationen
CAA-Applikationen dürfen nur verwendet werden, wenn die damit erzeugten Objekte/Objektgrup‐
pen mit CATIA-Volkswagen-Software bearbeitet werden können (siehe auch Downloadbereiche
von Volkswagen und Audi, Abschnitt 3.4). Es dürfen nur CAA-Applikationen mit File Type Libra‐
ries1) genutzt werden, die von Volkswagen autorisiert sind.
3.12  
3.13  
3.14  
3.15  
1)
Eigene in CAA-Applikationen erzeugte Objekttypen werden in sogenannten *.CATfct-Dateien deklariert. Diese müssen auf dem
Rechner vorhanden sein, der diese Daten visualisieren oder auf diese zugreifen will. Ist dies nicht der Fall, können die CAD-Daten
nicht verwendet werden.


### 第 13 页
Seite 13
VW 01059-6: 2015-11
Mitgeltende Unterlagen
Die folgenden in der Norm zitierten Dokumente sind zur Anwendung dieser Norm erforderlich:
VW 01014
Zeichnungen; Zeichnungsrahmen und Wiederholtexte
VW 01055
Referenz-Punkt-Systematik (RPS); Angaben in Zeichnungen und 3D-
CAD-Modellen
VW 01058
Zeichnungen; Beschriftungen
VW 01059-6-1
Anforderungen an CAD/CAM-Daten - CAD-System CATIA V5-6 -Teil 1:
Begriffe
VW 01059-6-2
Anforderungen an CAD/CAM-Daten - CAD-System CATIA V5-6 - Teil 2:
Elektrische Leitungsverlegung
VW 01059-6-3
Anforderungen an CAD/CAM-Daten - CAD-System CATIA V5-6 - Teil 3:
Prozesskettenadapter (PCA), DMU-CATPart und optionalen Adaptern
VW 01059-6-4
Anforderungen an CAD/CAM-Daten - CAD-System CATIA V5-6 - Teil 4:
Produktstrukturen für die PDA TM
Literaturverzeichnis
[1]
39D 22002 - Ergänzungsrichtlinie für die Konstruktion von Anlagen und Vorrichtungen
mit CATIA V5.Partnerfirmen erhalten alle gültigen Betriebsmittelnormen auf der Konzern
Business Plattform (http://www.vwgroupsupply.com) unter Online-Normentexte / Be‐
triebsmittel.
[2]
1D3119 - Richtlinie zur zeichnungslosen Erstellung von Betriebsmitteln unter CATIA V5
(Audi). Konstruktions- und Arbeitsrichtlinien können direkt über die Auftrag gebenden
Fachbereiche, aus dem Konzern Business Plattform (http://www.vwgroupsupply.com)
bezogen werden.
[3]
39D 944 - Basisrichtlinie für die Konstruktion von Presswerkzeugen mit CATIA V5
(Facharbeitskreis Betriebsmittelkonstruktion mit CATIA V5 der Deutschen Automobilin‐
dustrie). Partnerfirmen erhalten alle gültigen Betriebsmittelnormen auf der Konzern Busi‐
ness Plattform (http://www.vwgroupsupply.com) unter Online-Normentexte / Betriebsmit‐
tel.
[4]
39D 945 - Ergänzungen zur Basisrichtlinie für die Konstruktion von Presswerkzeugen
mit CATIA V5. Partnerfirmen erhalten alle gültigen Betriebsmittelnormen auf der Kon‐
zern Business Plattform (http://www.vwgroupsupply.com) unter Online-Normentexte /
Betriebsmittel.
[5]
39D 22000 - CAD-Richtlinie zur Methodenplanerstellung mit CATIA V5 (Expertenkreis
Methodenplanung mit CATIA V5). Partnerfirmen erhalten alle gültigen Betriebsmittelnor‐
men auf der Konzern Business Plattform (http://www.vwgroupsupply.com) unter Online-
Normentexte / Betriebsmittel.
[6]
39D 22001 - Basisrichtlinie für die Konstruktion von Anlagen und Vorrichtungen mit CA‐
TIA V5 (Facharbeitskreis Anlagen- und Vorrichtungskonstruktion mit CATIA V5 der deut‐
schen Automobilindustrie). Partnerfirmen erhalten alle gültigen Betriebsmittelnormen auf
der Konzern Business Plattform (http://www.vwgroupsupply.com) unter Online-Normen‐
texte / Betriebsmittel.
4  
5  


### 第 14 页
Seite 14
VW 01059-6: 2015-11
[7]
1D300046 - Richtlinie zur Methodenplanerstellung (Audi). Konstruktions- und Arbeits‐
richtlinien können direkt über die Auftrag gebenden Fachbereiche, aus der Konzern
Business Plattform (http://www.vwgroupsupply.com) bezogen werden.
[8]
Handbuch Konvertierung V4 nach V5. Das Handbuch kann direkt über das Volkswagen
Lieferantenportal unter http://www.vwgroupsupply.com bezogen werden. Im Volkswagen
Intranet befindet sich das Handbuch unter http://catia.wob.vw.vwg:8080/, Rubrik „Quality
Management“, Zeile „Interoperability“.

