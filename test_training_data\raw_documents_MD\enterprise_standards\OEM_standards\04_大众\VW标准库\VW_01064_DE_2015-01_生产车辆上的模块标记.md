# VW_01064_DE_2015-01_生产车辆上的模块标记.pdf

## 文档信息
- 标题：
- 作者：
- 页数：28

## 文档内容
### 第 1 页
Konzernnorm
VW 01064
Ausgabe 2015-01
Klass.-Nr.:
01152
Schlagwörter:
Kennzeichnung, Bauteilkennzeichnung, Baugruppe, Baugruppenkatalog, Bauzustandsdokumentation,
Rückverfolgbarkeit, Verbauprüfung, Seriennummer, Barcode, Code 39, Data Matrix, BG-Online, RFID,
BZD
Baugruppenkennzeichnung an Serienfahrzeugen
BZD – Codierung an mechanischen Fahrzeugteilen
Frühere Ausgaben
VW 01064: 1996-09, 1999-05, 2001-06, 2001-11, 2002-03, 2002-09, 2003-04, 2003-12, 2007-11,
2008-05, 2010-09, 2011-07, 2013-11
Änderungen
Gegenüber der VW 01064: 2013-11 wurden folgende Änderungen vorgenommen:
–
Abschnitt 1 „Anwendungsbereich“ geändert
–
Abschnitt 2.2 „BZD – Bauzustandsdokumentation“ erweitert
–
Abschnitt 3.1 „Allgemeine Anforderungen“ erweitert
–
Abschnitt 3.3 „Datenerfassung“ geändert
–
Abschnitt 4 „BZD – Kennzeichnung mit dem 1-D-Code (Strichcode, CODE 39) und dem 2-D-
Code (Data Matrix)“, 2. Absatz hinzugefügt
–
Abschnitt 4.1.3 „Seriennummer (Serialnummer)“ , 1. Absatz geändert, 3. Absatz erweitert
–
Abschnitt 4.2 „Ausführungsbeispiele“, Bild 5 geändert
–
Abschnitt 5.1 „Datenfolge des 2-D-Codes für Teileverbauprüfung und Bauzustandsdokumenta‐
tion“, Anmerkung 5 hinzugefügt
–
Abschnitt 5.2.1 „Standardbedatung für Teileverbauprüfung mit Bauzustandsdokumentation“, 1.
Absatz korrigiert, rechteckige DMC aufgenommen und Beschreibung geändert, Bild 11 geän‐
dert
–
Abschnitt 5.2.2 „Minimalbedatung für Teileverbauprüfung“, 1. Absatz erweitert, rechteckige
DMC aufgenommen und Beschreibung geändert
–
Abschnitt 5.2.3 „Minimalbedatung für Bauzustandsdokumentation“ 1. Absatz erweitert, rechte‐
ckige DMC aufgenommen und Beschreibung geändert, Bild 13 geändert und Anmerkung 9 ge‐
ändert
Norm vor Anwendung auf Aktualität prüfen.
Die elektronisch erzeugte Norm ist authentisch und gilt ohne Unterschrift.
Seite 1 von 28
Fachverantwortung
Normung
K-GQZ/I
Armin Witschi
Tel.: +49 5361 9-18168
EKDV/4 Uwe Stüber
EKDV
Tel.: +49 5361 9-29063
Maik Gummert
Alle Rechte vorbehalten. Weitergabe oder Vervielfältigung ohne vorherige Zustimmung einer Normenabteilung des Volkswagen Konzerns nicht gestattet.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Seite 2
VW 01064: 2015-01
–
Abschnitt 5.2.4 „Maximalbedatung für Teileverbauprüfung mit Bauzustandsdokumentation“,
rechteckige DMC aufgenommen und Beschreibung geändert, Bild 14 korrigiert
–
Abschnitt 6.2.1 „Einsatz in fahrzeugbauenden Werken“ hinzugefügt, ISO/IEC TR 29158 anstatt
AIM DPM, Gesamtqualität von „B“ auf „3“ geändert
–
Abschnitt 6.2.2 „Einsatz in Motoren-Werken“ neu hinzugefügt
–
Abschnitt 6.3 „Klarschriftinformation (1-D und 2-D-Code)“ , Teer Spiegelstrich geändert
–
Abschnitt 9 „Mitgeltende Unterlagen“ aktualisiert
Inhalt
Seite
Anwendungsbereich ................................................................................................... 3
Allgemeine Hinweise, Begriffe ................................................................................... 3
Rückverfolgbarkeit ..................................................................................................... 3
BZD – Bauzustandsdokumentation ............................................................................ 4
Baugruppe .................................................................................................................. 4
Baugruppendaten ....................................................................................................... 4
Teileidentifikation (Verbauprüfung) ............................................................................ 5
Fahrzeugbauteil ......................................................................................................... 5
Komplexer Zusammenbau ......................................................................................... 5
Allgemeine Anforderungen an die Kennzeichnung und technische
Anforderungen an die Datenverarbeitung .................................................................. 5
Allgemeine Anforderungen ......................................................................................... 5
Formen der Kennzeichnung ....................................................................................... 6
Datenerfassung .......................................................................................................... 6
Standardkonfiguration ................................................................................................ 7
Technische Anforderungen an die Datenverarbeitung mit 1-D- und 2-D-Code ......... 7
Prüfzeichenberechnung nach Modulo 43 ................................................................... 8
BZD – Kennzeichnung mit dem 1-D-Code (Strichcode, CODE 39) und dem 2-
D-Code (Data Matrix) ................................................................................................. 8
BZD – Datenfolge 1-D-Code und 2-D-Code (Baugruppendaten) ............................... 8
Ausführungsbeispiele ............................................................................................... 10
Kennzeichnung für Teileverbauprüfung mit Bauzustandsdokumentation ................ 11
Datenfolge des 2-D-Codes für Teileverbauprüfung und
Bauzustandsdokumentation ..................................................................................... 11
Ausführungsbeispiele für 2-D-Codes ....................................................................... 13
Ausführung und Gestaltung der Etiketten (1-D-Code und 2-D-Code) ...................... 16
Codesymbol für den Strichcode (1-D-Code) ............................................................ 16
Codesymbol für den Matrix Code (2-D-Code) .......................................................... 16
Klarschriftinformation (1-D und 2-D-Code) ............................................................... 17
Einteiliges, bauteilfestes Etikett (1-D und 2-D-Code) ............................................... 18
Etikettierung außen am ZSB (Sammelkennzeichnung) ........................................... 18
ZSB-Begleitkarte (Sammelkennzeichnung) ............................................................. 19
Kennzeichnung direkt im Material (Direktmarkierung DPM – Direct Part Mark) ...... 19
Verifizierungen ......................................................................................................... 19
Sammelkennzeichnung – Vorerfassung von komplexen Zusammenbauten ........... 20
BZD – Sammelkennzeichnung ................................................................................. 20
Teileverbauprüfung – Sammelkennzeichnung ......................................................... 20
Datenaustausch ....................................................................................................... 21
BZD – Kennzeichnung und Teileidentifikation mit Transponder (RFID) ................... 21
Technische Ausführung ........................................................................................... 21
1
2
2.1
2.2
2.3
2.4
2.5
2.6
2.7
3
3.1
3.2
3.3
3.4
3.5
3.6
4
4.1
4.2
5
5.1
5.2
6
6.1
6.2
6.3
6.4
6.5
6.6
6.7
6.8
7
7.1
7.2
7.3
8
8.1


### 第 3 页
Seite 3
VW 01064: 2015-01
Ausführungsbeispiele für Bedatung des User Memory ............................................ 22
Mitgeltende Unterlagen ............................................................................................ 25
Literaturverzeichnis .................................................................................................. 25
.................................................................................................................................. 26
8.2
9
10
Anhang A
Anwendungsbereich
Diese Norm beschreibt die Anforderungen zur äußerlichen Kennzeichnung von BZD-pflichtigen
Fahrzeugbauteilen für Serienfahrzeuge (Fahrzeuge mit ABE) und BZD-pflichtigen Komponenten-
Bauteilen (z. B. Motor). Die in der Kennzeichnung codierten Daten dienen zur Dokumentation und
Rückverfolgung von Fahrzeugbauteilen („Bauzustandsdokumentation") und der Teileidentifikation
(„Verbauprüfung") innerhalb des Volkswagen Konzerns.
Die Kennzeichnung nach dieser Norm ersetzt nicht die Teilekennzeichnung nach VW10500.
Diese Norm richtet sich an:
–
Entwickler, welche eine BZD-pflichtige Kennzeichnung spezifizieren sollen
–
Qualitätssicherer, welche eine BZD-pflichtige Kennzeichnung am Bauteil bemustern sollen
–
Lieferanten von Fahrzeugteilen, welche eine BZD-pflichtige Kennzeichnung realisieren sollen
–
Lieferanten von komplexen ZSB, welche ggf. Baugruppendaten vorerfassen sollen
–
Fertigungsplaner, welche die Ablauforganisation zur Erfassung planen sollen
Die Bauzustandsdokumentation der Volkswagen AG gilt für folgende Marken des Volkswagen-
Konzerns: Volkswagen, Audi, Skoda, Seat, Volkswagen Nutzfahrzeuge, Bentley, Lamborghini.
ANMERKUNG 1 Diese Kennzeichnung gilt nicht für diagnosefähige Bauteile. Die Kennzeichnun‐
gen für Steuergeräte sind in der WSK.013.290 E beschrieben.
ANMERKUNG 2 Die in dieser Norm beschriebene „BZD-Kennzeichnung und Teileidentifikation
mit Transponder (RFID)“ gilt nur für die „Teileverbauprüfung“ und/oder „Bauzustandsdokumentati‐
on“ von Serienbauteilen. Die Kennzeichnung von Prototypenbauteilen wird durch die VW 01067
beschrieben.
Allgemeine Hinweise, Begriffe
Rückverfolgbarkeit
Rückverfolgbarkeit bedeutet, dass zu einem Produkt oder einer Handelsware jederzeit festgestellt
werden kann, wann und wo und durch wen die Ware gewonnen, hergestellt, verarbeitet, gelagert,
transportiert, verbraucht oder entsorgt wurde. Diese Weg- und Prozessverfolgung wird auch Tra‐
cing genannt, es wird zwischen: Downstream Tracing (abwärts gerichtete Verfolgung – vom Erzeu‐
ger zum Verbraucher) und Upstream Tracing (aufwärtsgerichtete Rückverfolgung – vom Verbrau‐
cher zum Erzeuger) unterschieden.
1  
2  
2.1  


### 第 4 页
Seite 4
VW 01064: 2015-01
BZD – Bauzustandsdokumentation
Die Bauzustandsdokumentation im Volkswagen-Konzern ist in zwei Hauptkategorien gegliedert:
–
Einzel-Erfassungs-BZD
–
Chargen-BZD (Behälter-Erfassung)
Bei der Einzelerfassungs-BZD muss jedes einzelne Bauteil mit einer Kennzeichnung versehen
werden. Auf diese Kennzeichnung bezieht sich die vorliegende Norm.
Die Chargen-BZD hingegen benötigt keine Kennzeichnung einzelner Bauteile sondern basiert auf
der Nutzung von Kennzeichnungen der Logistik-Prozesse und -Systeme. Daher gilt die vorliegen‐
de Norm nicht für die Chargen-BZD.
Die in der Kennzeichnung codierten Daten dienen zur Dokumentation und Rückverfolgung von
Fahrzeugbauteilen („Bauzustandsdokumentation“) innerhalb der Volkswagen AG. Bei der BZD
werden bestimmte Eigenschaften eines Bauteils eindeutig einer Fahrgestellnummer zugeordnet.
Eigenschaften sind z. B. Hersteller- und Seriennummer, Teilnummern, Hardware- und Software‐
version von Steuergeräten. Dies ermöglicht im Schadensfall (Rückruf) eine genaue Eingrenzung
der Fahrgestellnummern der betroffenen Fahrzeuge.
Bild 1 zeigt die drei wesentlichen Vorgehensweisen z. B. bei einem Rückruf:
Bild 1 – Wesentliche Vorgehensweise bei einem Rückruf
Baugruppe
Fahrzeugbauteil oder Zusammenbau, welcher im Sinne dieser Konzern-Norm kennzeichnungsre‐
levant ist. Jede Baugruppe wird über eine eindeutige Baugruppennummer identifiziert.
Baugruppendaten werden im System „BG-Online“ definiert. Hinweise zur Kennzeichnungspflicht
finden sich in technischen Zeichnungen und TLD-Blättern.
Baugruppendaten
Baugruppendaten sind diejenigen Datenanteile der BZD-Kennzeichnung, welche zwecks Rückver‐
folgbarkeit dokumentiert werden.
Die eindeutige Identifikation von Fahrzeugbauteilen basiert auf dem Vorhandensein folgender
BZD-Daten („Baugruppendaten“):
Baugruppen-Nummer → Art des Fahrzeugbauteils
Herstellercode → Hersteller und ggf. Herstellort
Seriennummer → Fahrzeugbauteil
2.2  
2.3  
2.4  


### 第 5 页
Seite 5
VW 01064: 2015-01
Prüfzeichen → Modulo 43 Algorithmus
Diese BZD-pflichtigen Daten dienen als Referenz auf Fahrzeugbauteile, werden in der Fertigung
fahrzeugbezogen erfasst und im Fahrzeugarchiv der Volkswagen AG langzeitarchiviert. Im Fehler‐
fall ist eine präzise Ermittlung der tatsächlich betroffenen Fahrzeuge möglich.
Der Aufbau der Baugruppendaten ist im Konzern-Baugruppen-Katalog BG-Online beschrieben
(Kontakt über: <EMAIL>).
Teileidentifikation (Verbauprüfung)
Eine Kennzeichnung kann neben den o. g. Baugruppendaten weitere ergänzende Informationen
zum Fahrzeugbauteil enthalten:
–
Konzern-Teilnummer (VW 01098)
–
DUNS-Nummer (Data Universal Numbering System)
–
Herstelldatum
–
Versionsstand des Teiles
Diese Daten sind nicht zur Archivierung vorgesehen, sondern werden bei Bedarf im laufenden Fer‐
tigungsprozess genutzt, um den korrekten Verbau eines Fahrzeugbauteils (technische Ausführung,
Alter, Hersteller) zu prüfen.
Fahrzeugbauteil
Fahrzeugkomponente oder Zusammenbau mit einer Teilnummer, welche dessen technische Aus‐
führung exakt referenziert. Die Teilnummer ist ggf. Grundlage für eine Verbauprüfung.
Komplexer Zusammenbau
Zusammenbau, welcher mehrere Baugruppen beinhaltet Hz. B. ZSB Sitz: enthält Seitenairbag, Air‐
bag Lehnenbezug, Sensormatte, Gurtschloss mit Sensor). Sind im vorgefertigten Zustand des
ZSBs die Einzeldaten der Baugruppen nicht mehr zugänglich, so ist eine Vorerfassung erforderlich
(siehe Abschnitt 7).
Allgemeine Anforderungen an die Kennzeichnung und technische Anforderungen an die
Datenverarbeitung
Allgemeine Anforderungen
Die Kennzeichnung ist eine freigaberelevante Eigenschaft des Bauteils und bei der Bemusterung
mit zu berücksichtigen. Zu prüfen sind:
–
Erfassbarkeit der Daten unter Fertigungsbedingungen
–
Inhalt der aufgebrachten Datenfolge
–
Ausführung und Anbringung der Kennzeichnung auf dem Bauteil
–
Aufbewahrungsdauer ≥ 15 Jahre
Die Überprüfung der Kennzeichnung erfolgt im Rahmen der Bemusterung. Der Lieferant bzw. Her‐
steller stellt für diesen Zweck serienkonform gekennzeichnete Bauteile zur Verfügung.
Der Lieferant bzw. Hersteller sichert ab, dass die hier beschriebenen Anforderungen zur Kenn‐
zeichnung in der laufenden Serienfertigung eingehalten werden. Dies kann z. B. durch Stichpro‐
benprüfungen erfolgen.
2.5  
2.6  
2.7  
3  
3.1  


### 第 6 页
Seite 6
VW 01064: 2015-01
Die Aufbewahrungsdauer beträgt mindestens 15 Jahre ab Erstellung der Daten (entspricht KSU-
Klasse 7.2). Der Lieferant bzw. der Hersteller hat sicherzustellen, dass der Datensatz über den Ar‐
chivierungszeitraum betrachtet (≥ 15 Jahre) eindeutig ist. Er dokumentiert wichtige qualitätsrele‐
vante Einzelinformationen zum gekennzeichneten Fahrzeugbauteil z. B. Charge verwendeter Roh‐
materialien, Hersteller verwendeter Zulieferteile, Prüf- und Einstellwerte, Fertigungsort und -anlage
usw.), ordnet diese den Referenzdaten zu und archiviert diese. Bei Bedarf sind dann eindeutige
Aussagen zur Funktions-, Herstell- oder Materialqualität möglich.
Stellt der Lieferant einen ZSB her, in dem sich ein BZD-pflichtiges Bauteil befindet z. B. Kraftstoff‐
pumpe innerhalb des ZSB Tank), dann muss der Lieferant das BZD-pflichtige Bauteil erfassen,
dessen Daten den Daten des ZSBs hinzufügen und für mindestens 15 Jahre in seiner Dokumenta‐
tion abspeichern. Das trifft auch auf ZSBs aus einem Steuergerät und einem mechanischen Bau‐
teil zu (z. B. Scheinwerfer mit Steuergerät).
Sind die Anforderungen zur Kennzeichnung nicht erfüllt, gelten betroffene Bauteile als fehlerhaft
und können gegebenenfalls nicht verbaut werden.
Formen der Kennzeichnung
Ziel ist eine schnelle, kostengünstige Erfassung der Daten im Fertigungsablauf. Es sind 3 mögliche
Formen der Kennzeichnung vorgesehen und zu unterscheiden:
– 1-D-Code (Strichcode, Barcode) → verwendet wird CODE 39, siehe Abschnitt 4
– 2-D-Code (Matrixcode) → verwendet wird Data Matrix, siehe Abschnitt 4
– Transponder (RFID) → siehe Abschnitt 8
Die Kennzeichnung mittels Transponder ist sinnvoll, wenn eine teil- oder vollautomatisierte Erfas‐
sung gewünscht wird, eine Kennzeichnung mit Etiketten problematisch ist oder Fahrzeugbauteile
ohnehin mit einem Transponder ausgerüstet sind.
ANMERKUNG 3 Die Anwendung der Kennzeichnung mittels Transponder ist grundsätzlich mit
den betreffenden Fertigungswerken technisch und organisatorisch abzustimmen.
Datenerfassung
Die Datenerfassung erfolgt im Ablauf der Fahrzeugmontage oder den beteiligten Vormontageberei‐
chen. Folgende Vorgehensweisen zur Erfassung sind zu unterscheiden:
Direkteingabe :
Sofortige Eingabe der Daten am Verbauort des Fahrzeugteils. Einteiliges,
bauteilfestes Etikett, bzw. die Direktmarkierung, wird direkt mittels Scanner
eingelesen.
Wagenprüfkarte:
Erfassung der Daten am Verbauort des Fahrzeugteils. Von einem zweige‐
teilten Etikett wird dazu der abreißbare, selbstklebende Teil in ein vorgese‐
henes Feld der Wagenprüfkarte geklebt. Eingabe der Daten mittels Scan‐
ner an später folgenden Ablaufpunkten durch Lesen der Wagenprüfkarte‐
ninhalte.
Abtastung :
Erfassung und Eingabe der Daten direkt am Verbauort des Fahrzeugteils
oder an zeitnah folgenden Ablaufpunkten durch Auslesen der Transponder.
3.2  
3.3  


### 第 7 页
Seite 7
VW 01064: 2015-01
Standardkonfiguration
Standard bezüglich Kennzeichnung und Erfassung ist:
–
Kennzeichnung durch 2-D-Code mit Label
–
Erfassung am Verbauort
Die Kennzeichnung mittels 2-D-Code bietet gegenüber dem 1-D-Code erweiterte Anwendungs‐
möglichkeiten (Verbauprüfung) und hohe Lesesicherheit, hat weniger Platzbedarf und benötigt we‐
niger Etikettenmaterial, bzw. bei Direktmarkierung gar kein Etikettenmaterial mehr.
In begründeten Fällen kann im Rahmen der o. g. Möglichkeiten von der Standardkonfiguration ab‐
gewichen werden. Mischformen der Kennzeichnung sind möglich, wenn technische Randbedin‐
gungen bei der Datenerfassung dies erfordern.
Technische Anforderungen an die Datenverarbeitung mit 1-D- und 2-D-Code
Für die nachfolgend beschriebenen Datenfolgen sind nur die in Bild 2 aufgeführten ASCII-Zeichen
zulässig:
Legende
-
ausgeschlossen
(1)
uneingeschränkt
(2)
nur für Prüfziffer
(3)
Das Zeichen „#“ steht jeweils zwischen den fünf ersten Datenfeldern einer Datenfolge
(Trennzeichen). Trennzeichen sind immer zu setzen, auch wenn ein Feldinhalt ganz
entfällt.
(4)
Das Zeichen „&“ verbindet jeweils zwei Datenfolgen (Fortsetzungszeichen, nur bei
Sammelkennzeichnungen).
(5)
Das Zeichen „*“ steht am Anfang und Ende der Baugruppendaten in der Datenfolge.
Entfallen die Baugruppendaten, so entfallen auch diese Sonderzeichen.
(6)
Das Zeichen „=“ schließt die Datenfolge ab (Ende-Zeichen).
Bild 2
3.4  
3.5  


### 第 8 页
Seite 8
VW 01064: 2015-01
Bei der Realisierung von Erfassungssystemen ist darauf zu achten, dass die technische Anbin‐
dung und Konfiguration des Lesegerätes (Stationärscanner, Handscanner) zu keinen
Zeichenfehlern führt. Problembeispiel: englische Tastatur (Zeichen „Y" und „Z" vertauscht).
Prüfzeichenberechnung nach Modulo 43
Das Prüfzeichen dient der Verifikation einer Handeingabe anhand der auf der Kennzeichnung ent‐
haltenen Klarschriftinformation. Für das 1-stellige Prüfzeichen sind 43 verschiedene Zeichen defi‐
niert (zum Prüfsummenwert 00...42 korrespondierende ASCII-Zeichen siehe Bild 2).
Der Wert des Prüfzeichens bestimmt sich aus den Zeichen der Datenfolge ohne Prüfzeichen. Das
Berechnungsverfahren erfolgt auf Basis der Zuordnungstabelle (siehe Bild 2).
Die Prüfzeichenberechnung erfolgt nur für die Baugruppendaten (vgl. Abschnitt 4.1). Die Datenfel‐
der für Teileverbauprüfung (vgl. Abschnitt 5.1) werden nicht in die Prüfzeichenberechnung einbe‐
zogen.
Berechnung des Prüfzeichens:
1) zu jedem Zeichen der Datenfolge Prüfsummenwert aus Zuordnungstabelle ermitteln
2) die Summe aller Prüfsummenwerte bilden
3) die Summe durch 43 dividieren
4) anhand Divisionsrest aus Zuordnungstabelle das Prüfzeichen bestimmen
Beispiel:
Datenfolge ohne Prüfzeichen
065 KTO1234567
Summe
0+6+5+38+20+29+24+1+2+3+4+5+6+7 = 150
Division
150 : 43 = 3 Rest 21
Prüfzeichen
21 ≙ L (siehe Bild 2)
→ Datenfolge
*065 KTO1234567L*
ANMERKUNG 4 Das Prüfzeichen ist Teil der Datenfolge und nicht mit einem Prüfzeichen
des Codesymbols zu verwechseln.
BZD – Kennzeichnung mit dem 1-D-Code (Strichcode, CODE 39) und dem 2-D-Code
(Data Matrix)
Eine Kennzeichnung für die Bauzustandsdokumentation kann alternativ mit einem 1-D-Code oder
einem 2-D-Code erfolgen. In beiden Fällen gilt die unten beschriebene Datenfolge.
Soll das Bauteil für Teileverbauprüfung und Bauzustandsdokumentation gekennzeichnet werden,
dann ist ein 2-D-Code zu benutzen.
Die Kennzeichnung zur Bauzustandsdokumentation ersetzt nicht die Teilekennzeichnung nach
VW 10540-1 und VW 10540-3; ggf. sind beide Kennzeichnungen unabhängig voneinander auf
dem Bauteil anzubringen.
BZD – Datenfolge 1-D-Code und 2-D-Code (Baugruppendaten)
Die Datenfolge für den Strichcode, bzw. Matrix Code, enthält 4 Datenfelder und hat im Standardfall
15 Stellen (Ausnahme z. B. BG-Nr. 005 Motor und BG-Nr. 006 Getriebe mit 21 Stellen), siehe
Bild 3:
3.6  
4  
4.1  


### 第 9 页
Seite 9
VW 01064: 2015-01
Bild 3
Die hier beschriebene Datenfolge ist als Beispiel zu sehen. Der Aufbau jeder einzelnen Baugruppe
ist dem System „BG-Online“ zu entnehmen.
Für die Datenfelder BG-Nr. und Seriennummer sind nur die Zeichen 0 bis 9 und A bis Z zulässig.
Für das Prüfzeichen sind auch Sonderzeichen nach Bild 2 zulässig.
Baugruppennummer (BG-Nr.)
Die Baugruppennummer kennzeichnet die Art des Fahrzeugbauteils. Sie ist 3 Stellen lang
und kann alphanumerisch oder numerisch sein. Beispiel: 671 = Airbagmodul Beifahrer.
Herstellercode
Der Herstellercode ist ein bei der Volkswagen AG intern verwendeter Code zur Unterscheidung
von Herstellern und deren Fertigungsorten. Zur Definition siehe VW 10540-1. Das Feld ist 4 Stellen
lang. Die alphabetischen oder alphanumerischen 3-stelligen Herstellercodes sind in der Datenfolge
rechtsbündig und mit führendem Leerzeichen zu schreiben.
Beispiel : Hersteller „ZFS“ → Schreibweise in Datenfolge „ZFS“.
Seriennummer (Serialnummer)
Die Seriennummer dient zur eindeutigen Unterscheidung der im Sinne von Baugruppen gleicharti‐
gen Fahrzeugbauteile oder Zusammenbauten eines Herstellers. Die Seriennummer ist grundsätz‐
lich alphanumerisch (zulässige Zeichen 0 bis 9, A bis Z), die Länge der Seriennummer wird für je‐
des Bauteil im Konzern-Baugruppen-Katalog vorgegeben.
Anstelle von Einzelteilen können auch festgelegte Produktionschargen gekennzeichnet und unter‐
schieden werden. Dann entspricht die Seriennummer einer Chargennummer.
Eine Seriennummerierung endet nicht durch Wechsel der Teilnummer oder durch Verwendung des
Fahrzeugbauteils, die Seriennummer ist also unabhängig von der Teilnummer zu bilden.
Beispiel:
Kraftstofftank = Baugruppe „065“
Lieferantenkennzeichen = „KTO“, Schreibweise mit Leerzeichen „KTO“
→ Datenfolge in Klarschrift : *065 KTOxxxxxxxP*
Die Baugruppendaten müssen über den Archivierungszeitraum gesehen (≥ 15 Jahre) und für alle
Kraftstofftanks des Lieferanten „KTO", welche an die Marken der Volkswagen AG geliefert werden,
eindeutig sein. Technische Ausführung und Verwendung sind dabei unwesentlich.
Die Bildung einer Seriennummer ist, wenn nicht bereits vorgegeben, unter Einhaltung dieser Vo‐
raussetzung vom Lieferanten selber festlegbar. Je nach Art der Zählung ergeben sich unterschied‐
lich große Nummernkreise (siehe Bild 4).
4.1.1  
4.1.2  
4.1.3  


### 第 10 页
Seite 10
VW 01064: 2015-01
Bild 4 – Beispiele für mögliche Zählweisen
Die alphanumerische Zählweise entspricht einem 36er Zahlensystem unter Verwendung von Zif‐
fern und Großbuchstaben. Wertevergleich für dezimale und alphanumerische Zählung:
(1) an = (1) dez, .., (9) an = (9) dez, (A) an = (10) dez, .., (Z) an = (35) dez, (10) an = (36) dez, ..,
(ZZ) an = (1295) dez
Bei Nutzung kürzerer Nummernkreise können ggf. verbleibende freie Stellen der Seriennummer
nach eigener Festlegung des Lieferanten sinnvoll gefüllt werden (z. B. Kennzeichen für Fertigungs‐
anlage, Teileart).
Es ist jene Zählweise zu wählen, welche die zu erwartende Gesamtstückzahl an Baugruppen si‐
cher überschreitet. Empfohlene Zählweise ist: alphanumerisch, 7 Stellen.
Prüfzeichenberechnung nach Modulo 43
Siehe Abschnitt 3.6.
Ausführungsbeispiele
Die Standarddatenfolge der Baugruppendaten besteht aus 15 Stellen, siehe Bild 5, der genaue
Aufbau ist immer dem Konzern-Baugruppen-Katalog „BG-Online“ zu entnehmen. Die Information
zur Datenfolge kann über das Konzern-BZD-Office erfragt werden (baugruppeninfo@volkswa‐
gen.de).
Bild 5
4.1.4  
4.2  


### 第 11 页
Seite 11
VW 01064: 2015-01
Kennzeichnung für Teileverbauprüfung mit Bauzustandsdokumentation
Zur Kennzeichnung für eine Teileverbauprüfung mit Bauzustandsdokumentation ist ein 2-D-Code
zu benutzen.
Ausführungsbeispiele werden am Ende dieses Kapitels beschrieben.
Datenfolge des 2-D-Codes für Teileverbauprüfung und Bauzustandsdokumentation
Die Datenfolge für den Matrix Code besteht aus 6 Datenfeldern, siehe Bild 6.
Bild 6
Die Reihenfolge der einzelnen Datenfelder ist bindend. Absolute Positionsangaben innerhalb der
gesamten Zeichenfolge sind nicht sinnvoll, da je nach Anwendungsfall einzelne Datenfelder optio‐
nal sein können bzw. variable Länge haben können.
ANMERKUNG 5 Der Entfall des Verwendungskennzeichens in der Ausgabe 2012-06 dieser
Norm muss nur bei neu einsetzenden Bauteilen berücksichtigt werden.
Teilnummer
Die Teilnummer ist ein Verbundschlüssel mit identifizierendem und klassifizierendem Inhalt. Der
Grundaufbau ist 9- bis 11-stellig.
Im 2-D-Code für Teileverbauprüfung und Bauzustandsdokumentation wird immer die 11-stellige
Teilnummer gefolgt von dem 3-stelligen Farbkennzeichen benutzt. Das 14-stellige Datenfeld wird
linksbündig ohne gliedernde Punkte oder Leerzeichen beschrieben, nicht belegte Stellen werden
mit Leerzeichen gefüllt, siehe Bild 7 .
Bild 7
ANMERKUNG 6 Die Teilnummernstruktur der Volkswagen AG ist in der VW 01098 beschrieben.
Teileart
Das Datenfeld „Teileart“ ist optional. Wenn der Anwendungsfall diese Information nicht erfordert,
dann entfällt diese Information ersatzlos und das Datenfeld hat die Länge 0. Die Trennzeichen (#)
sind immer zu setzen, auch wenn der Feldinhalt leer ist.
Die Teileart enthält kontextspezifische Angaben zum Fahrzeugbauteil. Für die Struktur dieses Da‐
tenfeldes sind – abhängig von der Fahrzeugbauteil-Kategorie – verschiedene Formate und Definiti‐
onen zulässig. Beispiele siehe Bild 8.
5  
5.1  
5.1.1  
5.1.2  


### 第 12 页
Seite 12
VW 01064: 2015-01
Bild 8
Die Inhalte dieses Datenfeldes sind vom zuständigen Entwickler festzulegen und in der Teilezeich‐
nung zu dokumentieren.
DUNS-Nummer
Das Datenfeld „DUNS-Nummer“ ist optional. Wenn der Anwendungsfall diese Information nicht er‐
fordert, dann entfällt die DUNS-Nummer ersatzlos und das Datenfeld hat die Länge 0. Die Trenn‐
zeichen (#) sind immer zu setzen, auch wenn der Feldinhalt leer ist.
Die DUNS-Nummer ist eine international genormte Lieferantennummer. Das Datenfeld ist 9-stellig,
siehe Bild 9.
Bild 9
ANMERKUNG 7 Die Verwendung der VW-Lieferantennummer (System KRIAS) oder anderer
Schlüsselnummern ist nicht zulässig.
Herstelldatum
Das Datenfeld „Herstelldatum“ ist optional. Wenn der Anwendungsfall diese Information nicht erfor‐
dert, dann entfällt das Herstelldatum ersatzlos und das Datenfeld hat die Länge 0. Die Trennzei‐
chen (#) sind immer zu setzen, auch wenn der Feldinhalt leer ist.
Das Herstelldatum beschreibt den Zeitpunkt der technischen Fertigstellung des Bauteils (verbau‐
fertiger bzw. anlieferfertiger Zustand).
Das Datenfeld „Herstelldatum“ ist immer 6-stellig in der Form TTMMJJ (TagTagMonatMonatJahr‐
Jahr), siehe Bild 10. Wenn die Tagesinformation nicht zur Verfügung steht, ist immer der erste Ar‐
beitstag der Woche einzutragen.
Bild 10
Baugruppendaten
Erfolgt die Kennzeichnung nur zwecks Teileidentifikation, dann können die Baugruppendaten er‐
satzlos entfallen. Die Zeichen „*“ entfallen ebenfalls.
Der Aufbau der Baugruppendaten ist in Abschnitt 4.1 beschrieben.
5.1.3  
5.1.4  
5.1.5  


### 第 13 页
Seite 13
VW 01064: 2015-01
ANMERKUNG 8 Die Berechnung der Prüfziffer erfolgt ausschließlich für die Baugruppendaten.
Die übrigen Datenfelder des 2-D-Codes werden nicht in die Prüfzeichenberechnung einbezogen.
Zusatzinformationen
Für Entwickler und Lieferanten frei verfügbar.
Sonderzeichen in der Datenfolge
Das Zeichen „#“ steht jeweils zwischen den vier ersten Datenfeldern einer Datenfolge (Trennzei‐
chen). Trennzeichen sind immer zu setzen, auch wenn ein Feldinhalt ganz entfällt.
Das Zeichen „*“ steht am Anfang und Ende der Baugruppendaten in der Datenfolge. Entfallen die
Baugruppendaten, so entfallen auch diese Sonderzeichen.
Das Zeichen „=“ schließt die Datenfolge für Bauzustandsdokumentation und Verbaukontrolle ab
(Ende-Zeichen).
Ausführungsbeispiele für 2-D-Codes
Standardbedatung für Teileverbauprüfung mit Bauzustandsdokumentation
Die Standardbedatung für Teileverbauprüfung mit Bauzustandsdokumentation enthält die Datenfel‐
der „Teilnummer mit Farbkennzeichen“ und „Baugruppendaten“ (im Beispiel mit 15-stelligem Stan‐
dardaufbau). Die Datenfelder „Teileart“, „DUNS-Nummer“ und „Herstelldatum“ sind leer.
Von dieser Standardbedatung (siehe Bild 11) darf nur in begründeten Fällen abgewichen werden.
Jede Abweichung ist mit allen Prozessbeteiligten abzustimmen.
Bild 11
Mit den Randbedingungen aus Abschnitt 6.2 entstehen folgende Codesymbole, siehe Tabelle 1.
Tabelle 1
Ausführung
Quadratisches Codesymbol
Rechteckiges Codesymbol
Codesymbol
Größe in Dots
22 × 22
16 × 36
Größe in mm
ohne Beruhigungszone
11,22 × 11,22
8,16 × 24,48
Größe in mm
mit Beruhigungszone
15,22 × 15,22
12,16 × 28,48
5.1.6  
5.1.7  
5.2  
5.2.1  


### 第 14 页
Seite 14
VW 01064: 2015-01
Minimalbedatung für Teileverbauprüfung
Von der Standardbedatung kann abgewichen werden, wenn die Bauteilegeometrie eine Standard‐
bedatung nicht zulässt. Diese Abweichung von der Standardbedatung ist mit allen Prozessbeteilig‐
ten abzustimmen.
Die Minimalbedatung für eine Teileverbauprüfung ohne Bauzustandsdokumentation enthält nur
das Datenfeld „Teilnummer“ 14-stellig. Die Datenfelder „Teileart“, „DUNS-Nummer“ und „Herstell‐
datum“ haben keinen Inhalt, das Datenfeld „Baugruppendaten“ ist entfallen, siehe Bild 12.
Bild 12
Mit den Randbedingungen aus Abschnitt 6.2 entstehen folgende Codesymbole, siehe Tabelle 2
Tabelle 2
Ausführung
Quadratisches Codesymbol
Rechteckiges Codesymbol
Codesymbol
Größe in Dots
18 × 18
12 × 36
Größe in mm
ohne Beruhigungszone
9,18 × 9,18
6,21 × 13,26
Größe in mm
mit Beruhigungszone
13,18 × 13,18
10,21 × 17,26
Minimalbedatung für Bauzustandsdokumentation
Wenn die Bauteilgeometrie es zwingend erfordert, dann kann für den Anwendungsfall der reinen
Bauzustandsdokumentation von der Standardbedatung abgewichen werden. Diese Abweichung
von der Standardbedatung ist mit allen Prozessbeteiligten abzustimmen. In diesem Fall wird die
unter Abschnitt 4.1 beschriebene BZD-Datenfolge als Data Matrix Code geschrieben, siehe
Bild 13. Das Leseergebnis dieses 2-D-Codes entspricht genau dem des Codes 39.
Bild 13
Mit den Randbedingungen aus Abschnitt 6.2 entstehen folgende Codesymbole, siehe Tabelle 3.
5.2.2  
5.2.3  


### 第 15 页
Seite 15
VW 01064: 2015-01
Tabelle 3
Ausführung
Quadratisches Codesymbol
Rechteckiges Codesymbol
Codesymbol
Größe in Dots
16 × 16
12 × 26
Größe in mm
ohne Beruhigungszone
8,16 × 8,16
6,21 × 13,26
Größe in mm
mit Beruhigungszone
12,16 × 12,16
10,21 × 17,26
ANMERKUNG 9 Der Aufbau der Baugruppendaten ist im Konzern-Baugruppen-Katalog vorgege‐
ben. Für die Verschlüsselung des Herstellers ist in den Baugruppendaten ein 4-stelliger Schlüssel
(Hersteller-Code) vorgesehen. Der Hersteller-Code nach VW 10540-1 ist dreistellig und muss mit
führendem Leerzeichen verwendet werden. Kontakt über: <EMAIL>
Maximalbedatung für Teileverbauprüfung mit Bauzustandsdokumentation
Je nach Anwendungsfall kann die Bedatung erweitert werden bis zur Maximalbedatung. Hier sind
alle Datenfelder inklusive Zusatzdaten gefüllt. Die Länge der Datenfelder „Teileart“ „ und „Zusatz‐
daten“ hängt vom jeweiligen Anwendungsfall ab, siehe Bild 14.
Bild 14
Die Größe des Codesymbols ist abhängig vom Dateninhalt. Das oben gezeigte Bild 14 mit 65 Zei‐
chen führt mit den Randbedingungen aus Abschnitt 6.2 zu folgenden Codesymbolen, siehe
Tabelle 4
Tabelle 4
Ausführung
Quadratisches Codesymbol
Rechteckiges Codesymbol
Codesymbol
Größe in Dots
32 × 32
16 × 48
Größe in mm
ohne Beruhigungszone
16,32 × 16,32
8,16 × 24,48
Größe in mm
mit Beruhigungszone
20,32 × 20,32
12,16 × 28,48
5.2.4  


### 第 16 页
Seite 16
VW 01064: 2015-01
Ausführung und Gestaltung der Etiketten (1-D-Code und 2-D-Code)
Codesymbol für den Strichcode (1-D-Code)
Bei der Erzeugung des 1-D-Codesymbols sind folgende Anforderungen zu beachten:
–
Verwendung CODE 39 nach ISO/IEC 16388
–
Gesamtsymbolqualität nach DIN EN ISO/IEC 15416 muss 3,5 oder besser sein
–
Modulbreite x (siehe Anhang A): ca. 0,254 mm
–
Modulbreitenverhältnis: mindestens 1 : 2,5
–
Lückenbreitenverhältnis: wie Modulbreitenverhältnis
–
Druckauflösung: mindestens 300 dpi
–
Ruhezone: mindestens 3 mm je Seite
–
Höhe der Striche: ca. 10 mm
–
Prüfziffernberechnung (automatisch) entfällt
–
Entsprechend diesen Angaben ergibt sich eine Symbolgröße von ca. 63 mm × 10 mm bei ei‐
ner Datenfolge mit 15 Zeichen
–
Abweichungen sind mit allen Beteiligten abzustimmen
Codesymbol für den Matrix Code (2-D-Code)
Bei der Erzeugung des 2-D-Codesymbols sind folgende Anforderungen zu beachten:
Einsatz in fahrzeugbauenden Werken
–
Verwendung Data Matrix Code
–
Gesamtsymbolqualität nach ISO/IEC TR 29158 (ehemals AIM DPM-1 2006) liegt bei 3 oder
besser, diese Symbolqualität ist über die gesamte Prozesskette bis zum Einbauort sicherzu‐
stellen
–
Fehlerkorrektur ECC 200
–
Modulgröße x (siehe Anhang A) mindestens 0,50 mm
–
Druckerauflösung 300 dpi oder höher
–
Ruhezone beträgt mindestens 2 mm je Seite, eine kleine Beruhigungszone ist nur in Abstim‐
mung mit allen Beteiligten zulässig
–
Matrixgröße und Zeichensatz über Auto-Funktion erstellen
–
entsprechend diesen Angaben ergibt sich eine Symbolgröße von ca. 20 mm × 20 mm für eine
vollständig gefüllte Datenfolge, für Sammelkennzeichnungen ergeben sich größere Symbole.
–
Abweichungen sind mit allen Beteiligten abzustimmen
6  
6.1  
6.2  
6.2.1  


### 第 17 页
Seite 17
VW 01064: 2015-01
Einsatz in Motoren-Werken
–
Verwendung Data Matrix Code
–
Gesamtsymbolqualität nach ISO/IEC TR 29158 (ehemals AIM-DPM 2006) liegt bei 3 oder bes‐
ser, diese Symbolqualität ist über die gesamte Prozesskette bis zum Einbauort sicherzustellen
–
Fehlerkorrektur ECC 200
–
Modulgröße x (siehe Anhang A) mindestens 0,50 mm
–
Druckerauflösung 300 dpi oder höher
–
Ruhezone beträgt mindestens 4-fache Modulgröße
–
Matrixgröße und Zeichensatz über Auto-Funktion erstellen
–
entsprechend dieser Angaben ergibt sich eine Symbolgröße bei 20 × 20 Dots von ca. 10 mm ×
10 mm für einen Standardstring mit 32 bis 36 Zeichen
–
bei gleichen Bauteilen (Lieferanten übergreifend) ist das gleiche Kennzeichnungsverfahren,
die gleiche Größe sowie die gleiche Position zu wählen
–
Position muss im verbauten Zustand immer lesbar sein
–
Nachweis über die Qualitätseinhaltung der Lesbarkeit muss vom Hersteller des DMC geliefert
werden
–
Abweichungen z. B. durch Platzmangel müssen ausdrücklich vermerkt werden und sind mit al‐
len Beteiligten abzustimmen
Klarschriftinformation (1-D und 2-D-Code)
Neben dem Codesymbol muss die Kennzeichnung eine Klarschriftinformation enthalten. Sie dient
der Handeingabe der Daten, wenn das Codesymbol defekt und unlesbar ist oder Anlagenstörun‐
gen vorliegen. Anforderungen sind:
–
1-D-Code: Die Klarschrift zeigt die vollständige Datenfolge inklusive Prüfziffer
–
2-D-Code: Enthält der 2-D-Code das Datenfeld „Baugruppendaten“ oder „Teilnummer“, sind
diese verpflichtend als Klarschrift darzustellen, optional können alle Datenfelder als Klarschrift‐
information aufgebracht werden.
–
die Klarschriftdaten des Datenfeldes „Baugruppendaten“ werden am Anfang und Ende um das
Zeichen „*“ ergänzt
–
die Klarschriftinformation der Teilnummer erfolgt zur besseren Lesbarkeit mit je einem Leerzei‐
chen zwischen Vornummer, Mittelgruppe, Endnummer und Index
–
die Schriftart sollte möglichst „Thesis the Sans“ sein, sollte dies nicht möglich sein, ist „Arial“
einzusetzen
–
die Schrifthöhe beträgt mindestens 2 mm
–
die Klarschrift erscheint vorzugsweise zentriert unter dem Barcode (andere Formen der Anord‐
nung sind in Ausnahmefällen zulässig)
–
zur Unterscheidung zwischen Ziffer „0“ und Buchstabe „O“ ist die Null gestrichen (z. B. Clear-
Type Font „Consolas“, ASCII-Wert 048) zu schreiben
6.2.2  
6.3  


### 第 18 页
Seite 18
VW 01064: 2015-01
Einteiliges, bauteilfestes Etikett (1-D und 2-D-Code)
Bei Ausführung und Gestaltung des Etiketts sind folgende Anforderungen zu berücksichtigen:
–
das Etikett ist selbstklebend und darf sich nach Aufkleben nicht unbeabsichtigt lösen
–
das Druckbild kann invers erfolgen, wenn dies zwingend erforderlich ist (Designvorgabe)
–
der Aufdruck darf nicht nachträglich verschmieren
–
Untergrund: RAL 9010 (reinweiß – inklusive Barcodefeld), alternativ silbergraue, metallisierte
Folie mit Kalküberzug für Thermotransferdrucker
–
Schrift: Tiefschwarz
Die Anbringung am Fahrzeugbauteil ist so auszuführen, dass die Daten im eingebauten Zustand
sichtbar und technisch lesbar sind. Die Lage und Orientierung sind in einer technischen Zeichnung
festzulegen.
Es ist zulässig, wenn ein bereits vorhandenes, anderweitigen Zwecken dienendes Etikett für die
hier beschriebene Kennzeichnung mit genutzt wird. Die weiteren Inhalte dieses Etiketts dürfen die
Handhabung der hier beschriebenen Kennzeichnung nicht beeinträchtigen (z. B. durch weitere
gleichartige Codesymbole).
Zweiteilige oder mehrteilige Etiketten (1-D und 2-D-Code)
Das einteilige, bauteilfeste Etikett ist um einen oder mehrere abreißbare Etikettenteile zu ergän‐
zen. Diese werden in der Fertigung abgetrennt und z. B. in die Wagenprüfkarte geklebt. Mindes‐
tens der zuletzt zu verwendende, abreißbare Teil enthält das Codesymbol. Alle anderen beinhalten
mindestens die Klarschriftinformation. Es gelten sinngemäß die Anforderungen wie für das einteili‐
ge Etikett.
Für abreißbare Etikettenteile ist ein unnötig breites oder hohes Format unzulässig, da z. B. in der
Wagenprüfkarte vorgegebene Felder zum Einkleben nicht überdeckt werden dürfen. Das zu ver‐
wendende Etikettenformat orientiert sich an der Größe des Codesymbols inklusive Ruhezone und
Klarschriftinformation. Empfohlene Formate sind:
–
1-D-Code: Datenfolge 15 Zeichen (Standarddatenfolge): ca. 80 mm × 20 mm
–
1-D-Code: Datenfolge 21 Zeichen (Baugruppen 005 = Motor, 006 = Getriebe):
ca. 100 mm × 20 mm
–
2-D-Code: Empfohlenes Format für ein Etikett mit Matrix Code ist 20 mm × 30 mm
Etikettenmaterial und Anordnung sind so zu wählen, dass eine schnelle und einfache Trennung
der Teile ohne Beschädigung möglich ist. Der abreißbare Teil darf sich nicht unbeabsichtigt lösen.
Etikettierung außen am ZSB (Sammelkennzeichnung)
Ein später unzugängliches Fahrzeugbauteil benötigt ein mehrteiliges Etikett:
–
2 Teile, wenn im Fahrzeugwerk die Daten über Direkteingabe erfasst werden
–
3 Teile, wenn im Fahrzeugwerk die Daten über Wagenprüfkarte erfasst werden
Im Fertigungsprozess des komplexen ZSBs wird der abreißbare Teil dann am ZSB außen und
bauteilfest an einer vereinbarten Stelle angeklebt (z. B. Sitz: Außenblech des Sitzgestells).
6.4  
6.4.1  
6.5  


### 第 19 页
Seite 19
VW 01064: 2015-01
ZSB-Begleitkarte (Sammelkennzeichnung)
Wenn eine Etikettierung außen am ZSB nicht möglich ist, stellt der Lieferant eine ZSB-Begleitkarte
bereit. Sie wird mit dem komplexen ZSB ausgeliefert und im weiteren Fertigungsverlauf eingele‐
sen. Folgende Inhalte sind vorzusehen:
–
Lieferantenname und Bezeichnung des ZSBs
–
wenn fahrzeugbezogene Anlieferung: Kenn-Nummer oder Fahrgestellnummer des Fahrzeugs
–
wenn nicht fahrzeugbezogene Anlieferung: Zuordnungsnummer ZSB zu ZSB-Begleitkarte
–
alle Barcodes der im ZSB eingebauten, unzugänglichen Baugruppen
–
Abnahmevermerk (Stempel o. ä) des Lieferanten für Korrektheit der übergebenen Daten
Die ZSB-Begleitkarte wird damit zum Bestandteil der Wagenprüfkarte des Fahrzeugs. Sie ist trans‐
portsicher am ZSB anzubringen und inhaltlich, gestalterisch und ablauforganisatorisch mit dem be‐
treffenden Fahrzeugwerk abzustimmen.
Kennzeichnung direkt im Material (Direktmarkierung DPM – Direct Part Mark)1)
2-D-Codes können direkt in oder auf das Material des Bauteils gebracht werden, wenn technische
oder sonstige Anforderungen (z. B. Lebensdauerkennzeichnung, Designanspruch) dies erfordern.
Die Anforderungen an die Klarschriftinformation sind ebenfalls zu berücksichtigen und sinngemäß
einzuhalten.
Mögliche Markierungstechniken:
–
Lasermarkierung
–
Nadelprägung
–
Elektrochemische Ätzung
–
Tintenstrahldruck
Der Einsatz einer dieser Techniken eignet sich, je nach Lebenserwartung und Materialmix oder
Abnutzung durch Umwelteinflüsse und Produktionsvolumen der Teile, für bestimmte Anwendun‐
gen.
Verifizierungen
Verifizierung von Barcodes
Die Verifizierung der Barcodes ist nach DIN EN ISO/IEC 15416 vorzunehmen. Die Gesamtsymbol‐
klassifizierung nach DIN EN ISO/IEC 15416 muss einen Wert von 3,5 oder besser aufweisen.
Verifizierung von 2-D-Codes
Zur Verifizierung von Codes mit quadratischen Zellen ist die ISO/IEC TR 29158 (entspricht AIM-
DPM 2006) anzuwenden. Die ISO/IEC TR 29158 bezieht sich auf direktmarkierte 2-D-Codes, die
Vorgaben sind sinngemäß auf 2-D-Codes auf Etiketten zu übertragen. Die Gesamtsymbolklassifi‐
zierung muss mindestens Klasse „B“ oder besser sein.
ANMERKUNG 10 Bei Einsatz von Direktmarkierungen sind entsprechende Lesegeräte erforder‐
lich (DPM-Scanner). Diese sind mit allen beteiligten Bereichen abzustimmen (von der Erfassung
an der Linie bis hin zum Kundendienst, um zu gewährleisten, dass die Codes konzernweit ausles‐
bar sind).
6.6  
6.7  
6.8  
6.8.1  
6.8.2  
1)
Quelle Abschnitt 6.7 „Das Lesen von Direktmarkierungen – 10 wichtige Aspekte“ Firma Cognex und „Neue Standards verifizieren
2D Data Matrix Codes zuverlässig“ – White Paper von Carl W. Gerst III, Cognex Corporation, Senior Director & Business Unit Ma‐
nager, ID-Produkte.


### 第 20 页
Seite 20
VW 01064: 2015-01
Sammelkennzeichnung – Vorerfassung von komplexen Zusammenbauten
Für komplexe ZSBs sind die am Fahrzeugbauteil gekennzeichneten Daten ggf. nicht mehr zugäng‐
lich (z. B. Airbagmodul im Sitz). In derartigen Fällen ist eine Vorerfassung innerhalb der ZSB Ferti‐
gung erforderlich, so dass die Daten am Verbauort des ZSBs verfügbar sind.
Sammelkennzeichnungen sind nur mit Matrix Code realisierbar.
BZD – Sammelkennzeichnung
Der Lieferant führt eine Vorerfassung durch Einlesen der einzelnen Baugruppendatensätze (siehe
Abschnitt 4.1) durch. Diese Einzeldatenfolgen werden zu einer Gesamtdatenfolge kombiniert
(siehe Bild 15).
Bild 15
Sonderzeichen in der Datenfolge
Das Zeichen „&“ verbindet jeweils zwei Datenfolgen (Fortsetzungszeichen).
Das Zeichen „=“ schließt die Datenfolge für Bauzustandsdokumentation ab (Ende-Zeichen).
Teileverbauprüfung – Sammelkennzeichnung
Der Lieferant führt eine Vorerfassung durch Einlesen der Einzeldatensätze (siehe Abschnitt 5.1)
durch. Diese Einzeldatenfolgen werden zu einer Gesamtdatenfolge kombiniert (siehe Bild 16).
Bild 16
Zu dieser Gesamtdatenfolge wird eine neue Kennzeichnung erstellt, welche dann außen am kom‐
plexen ZSB bauteilfest aufgeklebt wird.
Diese Form der Vorerfassung ist nur zulässig, wenn alle im komplexen ZSB vereinigten Baugrup‐
pen aus Sicht der späteren Fertigungsabläufe zusammen bleiben. Änderungen (z. B. Austausch
einer Baugruppe) können in der Sammelkennzeichnung nicht nachgeführt werden.
Es gelten sinngemäß die gleichen Anforderungen wie für Einzeletiketten. Bei der Gestaltung ist zu
beachten:
–
für jede Baugruppe in einer Sammelkennzeichnung ist eine Klarschriftinformation erforderlich
–
zwischen 2 Datensätzen trennt das Zeichen „&“
–
der letzte Datensatz enthält das Ende-Zeichen „=“
7  
7.1  
7.1.1  
7.2  


### 第 21 页
Seite 21
VW 01064: 2015-01
Ein quadratischer Data Matrix Code kann aus max. 144 Zeilen × 144 Spalten bestehen. Damit sind
1982 ASCII-Zeichen darstellbar. Daraus ergibt sich eine max. Anzahl verkettbarer Datensätze von
23. Aus gebrauchstechnischen Gründen ist die Anzahl auf 20 beschränkt.
Sonderzeichen in der Datenfolge
Das Zeichen „#“ steht jeweils zwischen den vier ersten Datenfeldern einer Datenfolge (Trennzei‐
chen). Trennzeichen sind immer zu setzen, auch wenn ein Feldinhalt ganz entfällt.
Das Zeichen „*“ steht am Anfang und Ende der Baugruppendaten in der Datenfolge. Entfallen die
Baugruppendaten, so entfallen auch diese Sonderzeichen.
Das Zeichen „&“ verbindet jeweils zwei Datenfolgen.
Das Zeichen „=“ schließt die Datenfolge für Verbaukontrolle ab (Ende-Zeichen).
Datenaustausch
Der Fertigungsbereich des Lieferanten wird wie eine interne Vorfertigung behandelt. Der Lieferant
erfasst alle im komplexen ZSB verbauten Baugruppen und leitet diese Daten an das betreffende
Fahrzeugwerk weiter.
Diese Vorerfassung ist nur zulässig, wenn eine Fehlzuordnung der Daten zum späteren Fahrzeug
ablauforganisatorisch ausgeschlossen werden kann bzw. entsprechende Notorganisationen zur
Datenkorrektur vorhanden sind.
Datenformate und die gesamte technische Realisierung sind bilateral zu vereinbaren.
BZD – Kennzeichnung und Teileidentifikation mit Transponder (RFID)
Die vorliegende Norm beschreibt die Datenstrukturen für RFID zur Teileverbauprüfung und/oder
Bauzustandsdokumentation von Serienbauteilen. Die Beschreibung zur Kennzeichnung von Proto‐
typen-Bauteilen erfolgt durch VW 01067. Beide Anwendungsfälle sind miteinander abgestimmt.
Fahrzeugbauteile mit Kennzeichnung über Transponder müssen eine Klarschriftinformation auf‐
weisen, so dass im Falle einer Fehllesung die Handeingabe der Daten möglich ist. Die Klarschrift‐
information zeigt mindestens den Inhalt der Baugruppendaten und darüber hinaus die wichtigsten
Fertigungsmerkmale (z. B. Teilnummer, Generationsstand, Fertigungszeit) an und kann z. B. über
ein Etikett realisiert werden. Die Anforderungen zur Anbringung der Klarschriftinformation sind
sinngemäß wie bei Kennzeichnungen mit 1-D-Code.
Die Erfassung von Transponderdaten muss zeitnah am Verbauort stattfinden, um im Falle von
Fehllesungen die Möglichkeit zur Handeingabe der Daten sicherzustellen (Teil muss eingesehen
oder ggf. wieder demontiert werden können).
Technische Ausführung
Die eingesetzte RFID-Technologie ist in VW 01067 beschrieben.
Der zulässige Zeichenumfang ergibt sich aus der ASCII-Character-to-6-Bit-Compaction Substituti‐
on Table, vgl. ODETTE 5510 und JAIF Global RFID. Die für die Prüfziffer verwendeten Zeichen „$“
und „%“ sind im 6-Bit-Code bereits als Steuerzeichen reserviert und können deshalb nicht für die
Prüfziffer verwendet werden. Sie müssen deshalb vor dem Beschreiben der Tags in die erlaubten
Sonderzeichen „@“ und „\“ übersetzt werden.
Nach dem Auslesen der Tags werden sie wieder rückübersetzt: „@“ in „$“ und „“ in „%“.
7.2.1  
7.3  
8  
8.1  


### 第 22 页
Seite 22
VW 01064: 2015-01
Unique Item Identifier (UII – Bank 01)
Inhalt und Aufbau des UII sind in der VW 01067 und in dem VW 01067 Tutorial RFID Data Matrix
beschrieben.
User Memory (UM - Bank 11)
Der User Memory enthält die Daten zur Bauzustandsdokumentation analog dem Data Matrix Code
(vgl. Abschnitt 5.2), siehe Bild 17.
Ausführungsbeispiele für Bedatung des User Memory
Nachfolgend sind 4 Bedatungsvarianten beschrieben, deren Inhalt analog zur Kennzeichnung mit
2-D-Code aufgebaut ist. Der Vorteil liegt darin, dass keine Anpassungsarbeiten an den Systemen
in den fahrzeugbauenden Werken erforderlich sind.
Von diesen definierten Bedatungsvarianten kann in Ausnahmefällen abgewichen werden. Je nach
Größe der verfügbaren RFID-Tags können Teilinformationen auch in den UII geschrieben werden.
In diesem Fall ist die Umsetzung mit allen Beteiligten abzustimmen.
Standardbedatung für Teileverbauprüfung mit Bauzustandsdokumentation
Die Standardbedatung für Teileverbauprüfung mit Bauzustandsdokumentation enthält die Datenfel‐
der „Teilnummer mit Farbkennzeichen“ und „Baugruppendaten“, siehe Bild 17 (vgl. Abschnitt 5.2.1.
Bild 17
Verwendete Data Identifier (DI): Z, siehe ANSI MH 10.8.2.
Der Datenblock für BZD-Daten umfasst 36 Zeichen; bei 6-Bit-Codierung werden 240 Bit (inklusive
Steuerzeichen) belegt.
8.1.1  
8.1.2  
8.2  
8.2.1  


### 第 23 页
Seite 23
VW 01064: 2015-01
Minimalbedatung für Teileverbauprüfung
Die Minimalbedatung für Teileverbauprüfung ohne Bauzustandsdokumentation enthält nur das Da‐
tenfeld „Teilnummer mit Farbkennzeichen“ (vgl. Abschnitt 5.2.2), siehe Bild 18.
Bild 18
Verwendete Data Identifier (DI): Z, siehe ANSI MH 10.8.2.
Der Datenblock für BZD-Daten umfasst 19 Zeichen; bei 6-Bit-Codierung werden 138 Bit (inklusive
Steuerzeichen) belegt.
Minimalbedatung für Bauzustandsdokumentation
Die Minimalbedatung für Bauzustandsdokumentation enthält nur das Datenfeld „Bauzustandsdoku‐
mentation“ (vgl. Abschnitt 5.2.3), siehe Bild 19.
Bild 19
Verwendete Data Identifier (DI): Z, siehe ANSI MH 10.8.2.
Der Datenblock für BZD-Daten umfasst 19 Zeichen; bei 6-Bit-Codierung werden 138 Bit (inklusive
Steuerzeichen) belegt.
ANMERKUNG 11 Der Aufbau der Baugruppendaten ist im Konzern-Baugruppen-Katalog vorge‐
geben. Kaufteile haben in der Regel einen 3-stelligen Herstellercode (H) nach VW 10540-1 mit ei‐
nem führenden Leerzeichen. Kontakt über: <EMAIL>
8.2.2  
8.2.3  


### 第 24 页
Seite 24
VW 01064: 2015-01
Maximalbedatung für Teileverbauprüfung mit Bauzustandsdokumentation
Bei Maximalbedatung für Teileverbauprüfung mit Bauzustandsdokumentation sind alle Datenfelder
gefüllt. (vgl. Abschnitt 5.2.4). Die Länge der Datenfelder „Teileart“ und „Zusatzdaten“ hängt vom je‐
weiligen Anwendungsfall ab, siehe Bild 20.
Bild 20
Verwendete Data Identifier (DI): Z, siehe ANSI MH 10.8.2.
Der Datenblock für BZD-Daten umfasst 66 Zeichen; bei 6-Bit-Codierung werden 396 Bit inklusive.
Steuerzeichen) belegt.
8.2.4  


### 第 25 页
Seite 25
VW 01064: 2015-01
Mitgeltende Unterlagen
Die folgenden in der Norm zitierten Dokumente sind zur Anwendung dieser Norm erforderlich:
VW 01067
Einsatz von Auto-ID zur eindeutigen Objektkennzeichnung; Serialisie‐
rung mit Hilfe von optischen Codierungsverfahren und/oder Radio-Fre‐
quency Identification (RFID)
VW 01067 Tutorial
RFID Data Matrix
Auto-ID-Tutorial - Unique Object Identifications
VW 01098
Teilnummernsystem
VW10500
Firmenbezeichnung, Teilekennzeichnung; Richtlinien für die Anwendung
VW 10540-1
Hersteller-Code für Fahrzeugteile
VW 10540-3
Hersteller-Code; Vergabebereiche für Herstellercode-vergebende Aus‐
landswerke
WSK.013.290 E
Typenschild für Steuergeräte
ANSI MH 10.8.2
Data Identifier an Application Identifier Standard
DIN EN ISO/IEC
15416
Informationstechnik - Verfahren der automatischen Identifikation und Da‐
tenerfassung - Testspezifikation für Strichcodedruckqualität; Lineare
Symbole
ISO/IEC 16388
Informationstechnik - Verfahren der automatischen Identifikation und Da‐
tenerfassung - Spezifikationen für Strichcode-Symbologien; Code 39
ISO/IEC TR 29158
Informationstechnik - Automatische Identifikation und Datenerfassungs‐
verfahren - Qualitätsrichtlinie für die Direktmarkierung von Teilen (DPM)
JAIF Global RFID
Item-Level Standard, 10. August 2011
ODETTE 5510
RFID for Tracking of Parts & Components in the Automotive Industry,
Table 2
Literaturverzeichnis
[1]
VW 80115 Steuergeräteidentifikation mit KWP-2000 Diensten, Version 3.0
[2]
VW 80125 Identifikation elektronischer Fahrzeugsysteme, Version 2.3
[3]
Symbologiespezifikation Data Matrix z. B. über: AIM DPM 2006, Industrieverband für
automatische Datenerfassung
[4]
VW-Baugruppenkatalog System BG-Online
[5]
Das Lesen von Direktmarkierungen – 10 wichtige Aspekte, Fa. Cognex
[6]
Neue Standards verifizieren 2D Data Matrix Codes zuverlässig, Carl W. Gerst III, Fa.
Cognex
9  
10  


### 第 26 页
Seite 26
VW 01064: 2015-01
 
Bild A.1 – Modulbreite/Modulgröße von Codesymbolen (x)
Bild A.2 – Standardetikett
Bild A.3 – einteilige Etiketten mit Strichcode
Anhang A (informativ)  


### 第 27 页
Seite 27
VW 01064: 2015-01
Bild A.4 – mehrteilige Etiketten
Bild A.5 – spezielle Anbringungsformen


### 第 28 页
Seite 28
VW 01064: 2015-01
Bild A.6 – Sammelkennzeichnung (Beispiel mit 8 Baugruppen)

