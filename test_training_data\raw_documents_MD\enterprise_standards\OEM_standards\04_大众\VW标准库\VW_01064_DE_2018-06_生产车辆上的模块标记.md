# VW_01064_DE_2018-06_生产车辆上的模块标记.pdf

## 文档信息
- 标题：
- 作者：
- 页数：25

## 文档内容
### 第 1 页
Konzernnorm
VW 01064
Ausgabe 2018-06
Klass.-Nr.:
01152
Schlagwörter:
BG-Online, BZD, Barcode, Baugruppe, Baugruppenkatalog, Bauteilkennzeichnung,
Bauzustandsdokumentation, Code 39, Data Matrix, Kennzeichnung, RFID, Rückverfolgbarkeit,
Seriennummer, Verbauprüfung
Baugruppenkennzeichnung an Serienfahrzeugen
BZD – Codierung an mechanischen Fahrzeugteilen
Frühere Ausgaben
VW 01064: 1996-09, 1999-05, 2001-06, 2001-11, 2002-03, 2002-09, 2003-04, 2003-12, 2007-11,
2008-05, 2010-09, 2011-07, 2013-11, 2015-01
Änderungen
Gegenüber der VW 01064: 2015-01 wurden folgende Änderungen vorgenommen:
–
Abschnitt 1 „Anwendungsbereich“ 1. <PERSON><PERSON><PERSON><PERSON> ge<PERSON><PERSON><PERSON> (Fahrzeuge mit ABE gestrichen), 3. Ab-
sat<PERSON> ge<PERSON><PERSON><PERSON> (Porsche und MAN hinzugefügt)
–
<PERSON><PERSON><PERSON>nitt 2.2 „BZD – Bauzustandsdokumentation“ überarbeitet
–
Abschnitt 2.2.1 „Bauzustandsdokumentation Fahrzeuge am Zählpunkt (ZP) 8“ überarbeitet
und Hinweis auf Chargen-BZD gelöscht
–
Abschnitt 2.2.2 „BZD – Bauzustandsdokumentation Komponente“ neu aufgenommen
–
Abschnitt 2.4 „Baugruppendaten“, letzter Absatz überarbeitet
–
Abschnitt 3.3 „Datenerfassung“, 1. Satz des 3. Absatzes gelöscht
–
Abschnitt 4.1 „BZD – Datenfolge 1-D-Code“ überarbeitet
–
Abschnitt 4.1.2 „Herstellercode“, Leerzeichen bei „ZFS“ hinzugefügt
–
Abschnitt 4.1.3 „Seriennummer (Serialnummer)“, 2. Absatz geändert, 3. Absatz erweitert, 4.
Absatz geändert, Leerzeichen bei „KTO“ hinzugefügt
–
Abschnitt 4.2 „Ausführungsbeispiele“, Bild 5 geändert
–
Abschnitt 5.1.3 „DUNS-Nummer“, Bild 10 geändert
–
Abschnitt 5.1.6 „Zusatzdaten“, Titel geändert, 2. Absatz hinzugefügt
–
Abschnitt 5.1.7 „Sonderzeichen in der Datenfolge“, letzter Absatz hinzugefügt
–
Abschnitt 5.2.1 „Standardbedatung für Teileverbauprüfung mit Bauzustandsdokumentation“,
Bild 11 geändert
Norm vor Anwendung auf Aktualität prüfen.
Die elektronisch erzeugte Norm ist authentisch und gilt ohne Unterschrift.
Seite 1 von 25
Fachverantwortung
Normung
K-GQK-Z/3
Armin Witschi
Tel.: +49 5361 9-18168
K-ILI/5 Uwe Stüber
K-ILI
Tel.: +49 5361 9-29063
Uwe Wiesner
Alle Rechte vorbehalten. Weitergabe oder Vervielfältigung ohne vorherige Zustimmung einer Normenabteilung des Volkswagen Konzerns nicht gestattet.
© Volkswagen Aktiengesellschaft
VWNORM-2018-02


### 第 2 页
Seite 2
VW 01064: 2018-06
–
Abschnitt 5.2.3 „Minimalbedatung für Bauzustandsdokumentation“, Bild 13 geändert, Anmer-
kung 9 korrigiert
–
Abschnitt 5.2.4 „Maximalbedatung für Teileverbauprüfung mit Bauzustandsdokumentation“,
Bild 14 geändert
–
Abschnitt 6.2.1 „Einsatz in fahrzeugbauenden Werken“ neu hinzugefügt, alter Inhalt nach
Abschnitt 6.2.2 verschoben
–
Abschnitt 6.3 „Klarschriftinformation (1-D und 2-D-Code)“, Liste geändert, alter 5. Punkt ge-
löscht, letzter Punkt geändert
–
Abschnitt 7 „Sammelkennzeichnung – Vorerfassung von komplexen Zusammenbauten“ neu
strukturiert und ergänzt
–
Abschnitt 8 „BZD – Kennzeichnung und Teileidentifikation mit Transponder (RFID)“, Inhalt ge-
löscht, Verweis auf VW 01067 hinzugefügt
–
Abschnitt 10 „Mitgeltende Unterlagen“ aktualisiert
–
Abschnitt 11 „Literaturverzeichnis“ aktualisiert
–
Anhang A „Beispiele“ aktualisiert
Inhalt
Seite
Anwendungsbereich ................................................................................................... 3
Allgemeine Hinweise, Begriffe ................................................................................... 3
Rückverfolgbarkeit ..................................................................................................... 3
BZD – Bauzustandsdokumentation ............................................................................ 3
Baugruppe .................................................................................................................. 4
Baugruppendaten ....................................................................................................... 5
Teileidentifikation (Verbauprüfung) ............................................................................. 5
Fahrzeugbauteil ......................................................................................................... 5
Komplexer Zusammenbau ......................................................................................... 5
Allgemeine Anforderungen an die Kennzeichnung und technische
Anforderungen an die Datenverarbeitung .................................................................. 6
Allgemeine Anforderungen ......................................................................................... 6
Formen der Kennzeichnung ....................................................................................... 6
Datenerfassung .......................................................................................................... 7
Standardkonfiguration ................................................................................................ 7
Technische Anforderungen an die Datenverarbeitung mit 1-D- und 2-D-Code .......... 7
Prüfzeichenberechnung nach Modulo 43 ................................................................... 8
BZD – Kennzeichnung mit dem 1-D-Code (Strichcode, CODE 39) ........................... 9
BZD – Datenfolge 1-D-Code ...................................................................................... 9
Ausführungsbeispiele ............................................................................................... 11
Kennzeichnung für Teileverbauprüfung mit Bauzustandsdokumentation ................ 11
Datenfolge des 2-D-Codes für Teileverbauprüfung und
Bauzustandsdokumentation ..................................................................................... 11
Ausführungsbeispiele für 2-D-Codes ....................................................................... 14
Ausführung und Gestaltung der Etiketten (1-D-Code und 2-D-Code) ...................... 16
Codesymbol für den Strichcode (1-D-Code) ............................................................ 16
Codesymbol für den Matrix Code (2-D-Code) .......................................................... 17
Klarschriftinformation (1-D und 2-D-Code) ............................................................... 18
Einteiliges, bauteilfestes Etikett (1-D und 2-D-Code) ............................................... 19
Etikettierung außen am ZSB (Sammelkennzeichnung) ........................................... 19
ZSB-Begleitkarte (Sammelkennzeichnung) ............................................................. 20
1
2
2.1
2.2
2.3
2.4
2.5
2.6
2.7
3
3.1
3.2
3.3
3.4
3.5
3.6
4
4.1
4.2
5
5.1
5.2
6
6.1
6.2
6.3
6.4
6.5
6.6


### 第 3 页
Seite 3
VW 01064: 2018-06
Kennzeichnung direkt im Material (Direktmarkierung DPM – Direct Part Mark) ...... 20
Verifizierungen ......................................................................................................... 20
Sammelkennzeichnung – Vorerfassung von komplexen Zusammenbauten ........... 21
Sonderzeichen in der Datenfolge ............................................................................. 21
Datenaustausch ....................................................................................................... 22
BZD – Kennzeichnung und Teileidentifikation mit Transponder (RFID) ................... 22
BZD – Kennzeichnung von JIS-Bauteilen ................................................................ 22
Mitgeltende Unterlagen ............................................................................................ 23
Literaturverzeichnis .................................................................................................. 23
Beispiele ................................................................................................................... 24
6.7
6.8
7
7.1
7.2
8
9
10
11
Anhang A
Anwendungsbereich
Diese Norm beschreibt die Anforderungen zur äußerlichen Kennzeichnung von BZD-pflichtigen
Fahrzeugbauteilen für Serienfahrzeuge und BZD-pflichtigen Komponenten-Bauteilen (z. B. Motor).
Die in der Kennzeichnung codierten Daten dienen zur Dokumentation und Rückverfolgung von
Fahrzeugbauteilen („Bauzustandsdokumentation“) und der Teileidentifikation („Verbauprüfung“)
innerhalb des Volkswagen Konzerns.
Die Kennzeichnung nach dieser Norm ersetzt nicht die Teilekennzeichnung nach VW10500.
Diese Norm richtet sich an:
–
Entwickler, welche eine BZD-pflichtige Kennzeichnung spezifizieren sollen
–
Qualitätssicherer, welche eine BZD-pflichtige Kennzeichnung am Bauteil bemustern sollen
–
Lieferanten von Fahrzeugteilen, welche eine BZD-pflichtige Kennzeichnung realisieren sollen
–
Lieferanten von komplexen ZSB, welche ggf. Baugruppendaten vorerfassen sollen
–
Fertigungsplaner, welche die Ablauforganisation zur Erfassung planen sollen
Die Bauzustandsdokumentation der Volkswagen Aktiengesellschaft gilt für folgende Marken des
Volkswagen Konzerns: Volkswagen, Audi, Skoda, Seat, Volkswagen Nutzfahrzeuge, Bentley, Lam-
borghini, Porsche, MAN
ANMERKUNG 1 Diese Kennzeichnung gilt nicht für diagnosefähige Bauteile. Die Kennzeichnun-
gen für Steuergeräte sind in der WSK.013.290 E beschrieben.
Allgemeine Hinweise, Begriffe
Rückverfolgbarkeit
Rückverfolgbarkeit bedeutet, dass zu einem Produkt oder einer Handelsware jederzeit festgestellt
werden kann, wann und wo und durch wen die Ware gewonnen, hergestellt, verarbeitet, gelagert,
transportiert, verbraucht oder entsorgt wurde. Diese Weg- und Prozessverfolgung wird auch Tra-
cing genannt, es wird zwischen: Downstream Tracing (abwärts gerichtete Verfolgung – vom Erzeu-
ger zum Verbraucher) und Upstream Tracing (aufwärtsgerichtete Rückverfolgung – vom Verbrau-
cher zum Erzeuger) unterschieden.
BZD – Bauzustandsdokumentation
Das Thema Bauzustandsdokumentation gliedert sich in zwei grundsätzliche Abschnitte:
–
Bauzustandsdokumentation Fahrzeuge am Zählpunkt (ZP) 8
–
Bauzustandsdokumententation Komponente
1  
2  
2.1  
2.2  


### 第 4 页
Seite 4
VW 01064: 2018-06
Bauzustandsdokumentation Fahrzeuge am Zählpunkt (ZP) 8
Die Bauzustandsdokumentation Fahrzeuge am ZP8 ist ein etablierter Prozess und durch folgende
Prozessstandards definiert:
PS_1.4_999_1939_04 „Basisliste aktualisieren“
PS_1.4_999_1939_05 „Festlegung und Umsetzung des BZD-Umfangs im Produktteam“
PS_1.4_999_1430_03 „Bauzustandsdokumentation durchführen“
Die in der Kennzeichnung codierten Daten dienen zur Dokumentation und Rückverfolgung von
Fahrzeugbauteilen („Bauzustandsdokumentation“) innerhalb der Volkswagen Aktiengesellschaft.
Bei der BZD werden bestimmte Eigenschaften eines Bauteils eindeutig einer Fahrgestellnummer
zugeordnet. Eigenschaften sind z. B. Hersteller- und Seriennummer, Teilnummern, Hardware- und
Softwareversion von Steuergeräten. Dies ermöglicht im Schadensfall (Rückruf) eine genaue Ein-
grenzung der Fahrgestellnummern der betroffenen Fahrzeuge.
Bild 1 zeigt die drei wesentlichen Vorgehensweisen z. B. bei einem Rückruf:
Bild 1 – Wesentliche Vorgehensweise bei einem Rückruf
BZD – Bauzustandsdokumentation Komponente
Der neu entstandene Prozess der Bauzustandsdokumentation in der Komponente ergänzt die
Bauzustandsdokumentation Fahrzeuge am ZP8.
In der Fahrzeug-BZD werden bis ZP8 bestimmte ZSBs (ZSB = Zusammenbau) erfasst und der
Fahrgestellnummer zugeordnet. Die Komponenten-BZD liefert zu diesen ZSBs die Erfassung von
Einzelteilen und Unter-ZSBs.
Für jedes Bauteil muss explizit entschieden werden, ob sich die Bauteilkennzeichnung nach den
Vorgaben dieser Norm richtet oder nach dem Prozessstandard Bauzustandsdokumentation Kom-
ponente.
Insofern gelten Abschnitt 4 und Abschnitt 5 dieses Dokuments nur eingeschränkt für die Bauzus-
tandsdokumentation Komponente.
Im Abschnitt 10 dieses Dokuments werden Besonderheiten für einzelne Anwendungen der Kom-
ponenten-BZD beschrieben.
Baugruppe
Fahrzeugbauteil oder Zusammenbau, welcher im Sinne dieser Konzern-Norm kennzeichnungsre-
levant ist. Jede Baugruppe wird über eine eindeutige Baugruppennummer identifiziert.
2.2.1  
2.2.2  
2.3  


### 第 5 页
Seite 5
VW 01064: 2018-06
Baugruppendaten werden im System „BG-Online“ definiert. Hinweise zur Kennzeichnungspflicht
finden sich in technischen Zeichnungen und TLD-Blättern.
Baugruppendaten
Baugruppendaten sind diejenigen Datenanteile der BZD-Kennzeichnung, welche zwecks Rückver-
folgbarkeit dokumentiert werden.
Die eindeutige Identifikation von Fahrzeugbauteilen basiert auf dem Vorhandensein folgender
BZD-Daten („Baugruppendaten“):
Baugruppen-Nummer → Art des Fahrzeugbauteils
Herstellercode → Hersteller und Herstellort
Seriennummer → Fahrzeugbauteil
Prüfzeichen → Modulo 43 Algorithmus
Diese BZD-pflichtigen Daten dienen als Referenz auf Fahrzeugbauteile, werden in der Fertigung
fahrzeugbezogen erfasst und im Fahrzeugarchiv der Volkswagen Aktiengesellschaft langzeitarchi-
viert. Im Fehlerfall ist eine präzise Ermittlung der tatsächlich betroffenen Fahrzeuge möglich.
Der Aufbau der Baugruppendaten ist im Konzern-Baugruppen-Katalog „BG-Online“ beschrieben.
Die maximale Länge der Baugruppendaten beträgt 30 Zeichen. Externe Lieferanten müssen sich
über einen Bauteileverantwortlichen Konzern-Mitarbeiter an das BZD-Office wenden.
Kontakt über: <EMAIL>
Teileidentifikation (Verbauprüfung)
Eine Kennzeichnung kann neben den o. g. Baugruppendaten weitere ergänzende Informationen
zum Fahrzeugbauteil enthalten:
–
Konzern-Teilnummer (VW 01098)
–
DUNS-Nummer (Data Universal Numbering System)
–
Herstelldatum
–
Versionsstand des Teiles
Diese Daten sind nicht zur Archivierung vorgesehen, sondern werden bei Bedarf im laufenden Fer-
tigungsprozess genutzt, um den korrekten Verbau eines Fahrzeugbauteils (technische Ausführung,
Alter, Hersteller) zu prüfen.
Fahrzeugbauteil
Fahrzeugkomponente oder Zusammenbau mit einer Teilnummer, welche die technische Ausfüh-
rung exakt referenziert. Die Teilnummer ist ggf. Grundlage für eine Verbauprüfung.
Komplexer Zusammenbau
Zusammenbau, welcher mehrere Baugruppen beinhaltet Hz. B. ZSB Sitz: enthält Seitenairbag, Air-
bag Lehnenbezug, Sensormatte, Gurtschloss mit Sensor). Sind im vorgefertigten Zustand des
ZSBs die Einzeldaten der Baugruppen nicht mehr zugänglich, so ist eine Vorerfassung erforderlich
(siehe Abschnitt 7).
2.4  
2.5  
2.6  
2.7  


### 第 6 页
Seite 6
VW 01064: 2018-06
Allgemeine Anforderungen an die Kennzeichnung und technische Anforderungen
an die Datenverarbeitung
Allgemeine Anforderungen
Die Kennzeichnung ist eine freigaberelevante Eigenschaft des Bauteils und bei der Bemusterung
mit zu berücksichtigen. Zu prüfen sind:
–
Erfassbarkeit der Daten unter Fertigungsbedingungen
–
Inhalt der aufgebrachten Datenfolge
–
Ausführung und Anbringung der Kennzeichnung auf dem Bauteil
–
Aufbewahrungsdauer ≥ 15 Jahre
Die Überprüfung der Kennzeichnung erfolgt im Rahmen der Bemusterung. Der Lieferant bzw. Her-
steller stellt für diesen Zweck serienkonform gekennzeichnete Bauteile zur Verfügung.
Der Lieferant bzw. Hersteller sichert ab, dass die hier beschriebenen Anforderungen zur Kenn-
zeichnung in der laufenden Serienfertigung eingehalten werden. Dies kann z. B. durch Stichpro-
benprüfungen erfolgen.
Die Aufbewahrungsdauer beträgt mindestens 15 Jahre ab Erstellung der Daten (entspricht KSU-
Klasse 7.2). Der Lieferant bzw. der Hersteller hat sicherzustellen, dass der Datensatz über den Ar-
chivierungszeitraum betrachtet (≥ 15 Jahre) eindeutig ist. Er dokumentiert wichtige qualitätsrele-
vante Einzelinformationen zum gekennzeichneten Fahrzeugbauteil z. B. Charge verwendeter Roh-
materialien, Hersteller verwendeter Zulieferteile, Prüf- und Einstellwerte, Fertigungsort und -anlage
usw.), ordnet diese den Referenzdaten zu und archiviert diese. Bei Bedarf sind dann eindeutige
Aussagen zur Funktions-, Herstell- oder Materialqualität möglich.
Stellt der Lieferant einen ZSB her, in dem sich ein BZD-pflichtiges Bauteil befindet z. B. Kraftstoff-
fördereinheit innerhalb des ZSB Tank), dann muss der Lieferant das BZD-pflichtige Bauteil erfas-
sen, dessen Daten den Daten des ZSBs hinzufügen und für mindestens 15 Jahre in seiner Doku-
mentation abspeichern. Das trifft auch auf ZSBs aus einem Steuergerät und einem mechanischen
Bauteil zu (z. B. Scheinwerfer mit Steuergerät).
Sind die Anforderungen zur Kennzeichnung nicht erfüllt, gelten betroffene Bauteile als fehlerhaft
und können gegebenenfalls nicht verbaut werden.
Formen der Kennzeichnung
Ziel ist eine schnelle, kostengünstige Erfassung der Daten im Fertigungsablauf. Es sind 3 mögliche
Formen der Kennzeichnung vorgesehen und zu unterscheiden:
– 1-D-Code (Strichcode, Barcode) → verwendet wird CODE 39, siehe Abschnitt 4
– 2-D-Code (Matrixcode) → verwendet wird Data Matrix, siehe Abschnitt 4
– Transponder (RFID) → siehe Abschnitt 8
Die Kennzeichnung mittels Transponder ist sinnvoll, wenn eine teil- oder vollautomatisierte Erfas-
sung gewünscht wird, eine Kennzeichnung mit Etiketten problematisch ist oder Fahrzeugbauteile
ohnehin mit einem Transponder ausgerüstet sind.
ANMERKUNG 2 Die Anwendung der Kennzeichnung mittels Transponder ist grundsätzlich mit
den Fertigungswerken technisch und organisatorisch abzustimmen.
3  
3.1  
3.2  


### 第 7 页
Seite 7
VW 01064: 2018-06
Datenerfassung
Die Datenerfassung erfolgt im Ablauf der Fahrzeugmontage oder den beteiligten Vormontageberei-
chen. Folgende Vorgehensweisen zur Erfassung sind zu unterscheiden:
Direkteingabe :
Sofortige Eingabe der Daten am Verbauort des Fahrzeugteils. Einteiliges,
bauteilfestes Etikett, bzw. die Direktmarkierung, wird direkt mittels Scanner
eingelesen.
Wagenprüfkarte:
Von einem zweigeteilten Etikett wird dazu der abreißbare, selbstklebende
Teil in ein vorgesehenes Feld der Wagenprüfkarte geklebt. Eingabe der Da-
ten mittels Scanner an später folgenden Ablaufpunkten durch Lesen der
Wagenprüfkarteninhalte.
Abtastung :
Erfassung und Eingabe der Daten direkt am Verbauort des Fahrzeugteils
oder an zeitnah folgenden Ablaufpunkten durch Auslesen der Transponder.
Standardkonfiguration
Standard bezüglich Kennzeichnung und Erfassung ist:
–
Kennzeichnung durch 2-D-Code mit Label
–
Erfassung am Verbauort
Die Kennzeichnung mittels 2-D-Code bietet gegenüber dem 1-D-Code erweiterte Anwendungs-
möglichkeiten (Verbauprüfung) und hohe Lesesicherheit, hat weniger Platzbedarf und benötigt we-
niger Etikettenmaterial, bzw. bei Direktmarkierung gar kein Etikettenmaterial mehr.
In begründeten Fällen kann von der Standardkonfiguration abgewichen werden. Mischformen der
Kennzeichnung sind möglich, wenn technische Randbedingungen bei der Datenerfassung dies er-
fordern.
Technische Anforderungen an die Datenverarbeitung mit 1-D- und 2-D-Code
Für die nachfolgend beschriebenen Datenfolgen sind nur die in Bild 2 aufgeführten ASCII-Zeichen
zulässig:
3.3  
3.4  
3.5  


### 第 8 页
Seite 8
VW 01064: 2018-06
Legende
-
ausgeschlossen
(1)
uneingeschränkt
(2)
entfallen
(3)
Das Zeichen „#“ steht jeweils zwischen den fünf ersten Datenfeldern einer Datenfolge
(Trennzeichen). Trennzeichen sind immer zu setzen, auch wenn ein Feldinhalt ganz
entfällt.
(4)
Das Zeichen „&“ verbindet jeweils zwei Datenfolgen (Fortsetzungszeichen, nur bei
Sammelkennzeichnungen).
(5)
Das Zeichen „*“ steht am Anfang und Ende der Baugruppendaten in der Datenfolge.
Entfallen die Baugruppendaten, so entfallen auch diese Sonderzeichen.
(6)
Das Zeichen „=“ schließt die Datenfolge ab (Ende-Zeichen).
Bild 2 – Datenfolge (ASCII-Zeichen)
Bei der Realisierung von Erfassungssystemen ist darauf zu achten, dass die technische Anbin-
dung und Konfiguration des Lesegerätes (Stationärscanner, Handscanner) zu keinen Zeichenfeh-
lern führt. Problembeispiel: englische Tastatur (Zeichen „Y“ und „Z“ vertauscht).
Prüfzeichenberechnung nach Modulo 43
Das Prüfzeichen dient der Verifikation einer Handeingabe anhand der auf der Kennzeichnung ent-
haltenen Klarschriftinformation. Für das 1-stellige Prüfzeichen sind 43 verschiedene Zeichen defi-
niert (zum Prüfsummenwert 00...42 korrespondierende ASCII-Zeichen siehe Bild 2).
Der Wert des Prüfzeichens bestimmt sich aus den Zeichen der Datenfolge ohne Prüfzeichen. Das
Berechnungsverfahren erfolgt auf Basis der Zuordnungstabelle (siehe Bild 2).
Die Prüfzeichenberechnung erfolgt nur für die Baugruppendaten (vgl. Abschnitt 4.1). Die Datenfel-
der für Teileverbauprüfung (vgl. Abschnitt 5.1) werden nicht in die Prüfzeichenberechnung einbezo-
gen.
3.6  


### 第 9 页
Seite 9
VW 01064: 2018-06
Berechnung des Prüfzeichens:
1) zu jedem Zeichen der Datenfolge Prüfsummenwert aus Zuordnungstabelle ermitteln
2) die Summe aller Prüfsummenwerte bilden
3) die Summe durch 43 dividieren
4) anhand Divisionsrest aus Zuordnungstabelle das Prüfzeichen bestimmen
Beispiel:
Datenfolge ohne Prüfzeichen
065 KTO1234567
Summe
0+6+5+38+20+29+24+1+2+3+4+5+6+7 = 150
Division
150 : 43 = 3 Rest 21
Prüfzeichen
21 ≙ L (siehe Bild 2)
→ Datenfolge
*065 KTO1234567L*
ANMERKUNG 3 Das Prüfzeichen ist Teil der Datenfolge und nicht mit einem Prüfzeichen
des Codesymbols zu verwechseln.
BZD – Kennzeichnung mit dem 1-D-Code (Strichcode, CODE 39)
Eine Kennzeichnung für die Bauzustandsdokumentation kann alternativ mit einem 1-D-Code oder
einem 2-D-Code erfolgen. In beiden Fällen gilt die unten beschriebene Datenfolge.
Soll das Bauteil für Teileverbauprüfung und Bauzustandsdokumentation gekennzeichnet werden,
dann ist ein 2-D-Code zu benutzen.
Die Kennzeichnung zur Bauzustandsdokumentation ersetzt nicht die Teilekennzeichnung nach
VW 10540-1 und VW 10540-3; ggf. sind beide Kennzeichnungen unabhängig voneinander auf
dem Bauteil anzubringen.
BZD – Datenfolge 1-D-Code
Die BZD-Datenfolge enthält 4 Datenfelder und hat im Standardfall 15 Stellen (Ausnahme z. B. BG-
Nr. 005 Motor und BG-Nr. 006 Getriebe mit 21 Stellen), siehe Bild 3:
Bild 3 – BZD-Datenfolge
Die hier beschriebene Datenfolge ist als Beispiel zu sehen. Der Aufbau jeder einzelnen Baugruppe
und die zulässigen Zeichen je Datenfeld sind dem Konzern-Baugruppen-Katalog „BG-Online“ zu
entnehmen. Die maximale Länge der Baugruppendaten beträgt 30 Zeichen.
Baugruppennummer (BG-Nr.)
Die Baugruppennummer kennzeichnet die Art des Fahrzeugbauteils. Sie ist 3 Stellen lang
und kann alphanumerisch oder numerisch sein. Beispiel: 671 = Airbagmodul Beifahrer.
4  
4.1  
4.1.1  


### 第 10 页
Seite 10
VW 01064: 2018-06
Herstellercode
Der Herstellercode ist ein bei der Volkswagen Aktiengesellschaft intern verwendeter Code zur Un-
terscheidung von Herstellern und deren Fertigungsorten. Zur Definition siehe VW 10540-1. Das
Feld ist in der Regel 4 Stellen lang. Die alphabetischen oder alphanumerischen 3-stelligen Herstel-
lercodes sind in der Datenfolge rechtsbündig und mit führendem Leerzeichen zu schreiben.
Beispiel : Hersteller „ZFS“ → Schreibweise in Datenfolge „ ZFS“.
Seriennummer (Serialnummer)
Die Seriennummer dient zur eindeutigen Unterscheidung der im Sinne von Baugruppen gleicharti-
gen Fahrzeugbauteilen oder Zusammenbauten eines Herstellers. Die Seriennummer ist grundsätz-
lich alphanumerisch (zulässige Zeichen 0 bis 9, A bis Z), die Länge der Seriennummer wird für je-
des Bauteil im Konzern-Baugruppen-Katalog vorgegeben.
Grundsätzlich sind die Bauteile mit einer Seriennummer zu kennzeichnen. Wenn dies nicht sinnvoll
möglich ist, kann im SET oder der Fachgruppe entschieden werden, auf eine Chargennummer
auszuweichen. Diese Entscheidung ist dem BZD-Office mitzuteilen (Mail baugruppenin-
<EMAIL>).
Eine Seriennummerierung endet nicht durch Wechsel der Teilnummer oder des Herstelltages oder
durch Verwendung des Fahrzeugbauteils in einem anderen Fahrzeugmodell, die Seriennummer ist
also unabhängig von der Teilnummer zu bilden.
Beispiel:
Kraftstofftank = Baugruppe „065“
Lieferantenkennzeichen = „KTO“, Schreibweise mit Leerzeichen „ KTO“
→ Datenfolge in Klarschrift : *065 KTOxxxxxxxP*
Die Baugruppendaten müssen über den Archivierungszeitraum gesehen (≥ 15 Jahre) und für alle
Kraftstofftanks des Lieferanten „KTO“, welche an die Marken der Volkswagen Aktiengesellschaft
geliefert werden, eindeutig sein. Technische Ausführung und Verwendung sind dabei nicht zu be-
rücksichtigen.
Die Bildung einer Seriennummer ist, wenn nicht bereits vorgegeben, unter Einhaltung dieser Vo-
raussetzung vom Lieferanten selber festlegbar. Je nach Art der Zählung ergeben sich unterschied-
lich große Nummernkreise (siehe Bild 4).
Bild 4 – Beispiele für mögliche Zählweisen
4.1.2  
4.1.3  


### 第 11 页
Seite 11
VW 01064: 2018-06
Die alphanumerische Zählweise entspricht einem 36er Zahlensystem unter Verwendung von Zif-
fern und Großbuchstaben.
Bei Nutzung kürzerer Nummernkreise können ggf. verbleibende freie Stellen der Seriennummer
nach eigener Festlegung des Lieferanten sinnvoll gefüllt werden (z. B. Kennzeichen für Fertigungs-
anlage, Teileart).
Es ist jene Zählweise zu wählen, welche die zu erwartende Gesamtstückzahl an Baugruppen si-
cher überschreitet. Empfohlene Zählweise ist: alphanumerisch, 7 Stellen.
Prüfzeichenberechnung nach Modulo 43
Siehe Abschnitt 3.6.
Ausführungsbeispiele
Die Standarddatenfolge der Baugruppendaten besteht aus 15 Stellen, siehe Bild 5, der genaue
Aufbau ist immer dem Konzern-Baugruppen-Katalog „BG-Online“ zu entnehmen. Die Information
zur Datenfolge kann über das Konzern-BZD-Office erfragt werden (baugruppenin-
<EMAIL>).
Bild 5 – Standartdatenfolge Baugruppendaten
Kennzeichnung für Teileverbauprüfung mit Bauzustandsdokumentation
Zur Kennzeichnung für eine Teileverbauprüfung mit Bauzustandsdokumentation ist ein 2-D-Code
zu benutzen.
Ausführungsbeispiele werden am Ende dieses Kapitels beschrieben.
Datenfolge des 2-D-Codes für Teileverbauprüfung und
Bauzustandsdokumentation
Die Datenfolge für den Matrix Code besteht aus 6 Datenfeldern, siehe Bild 6.
Bild 6 – Datenfolge 2-D-Code
Die Reihenfolge der einzelnen Datenfelder ist bindend. Absolute Positionsangaben innerhalb der
gesamten Zeichenfolge sind nicht sinnvoll, da je nach Anwendungsfall einzelne Datenfelder optio-
nal sein können bzw. variable Länge haben können.
ANMERKUNG 4 Der Entfall des Verwendungskennzeichens in der Ausgabe 2012-06 dieser
Norm muss nur bei neu einsetzenden Bauteilen berücksichtigt werden.
4.1.4  
4.2  
5  
5.1  


### 第 12 页
Seite 12
VW 01064: 2018-06
Teilnummer
Die Teilnummer ist ein Verbundschlüssel mit identifizierendem und klassifizierendem Inhalt. Der
Grundaufbau ist 9- bis 11-stellig.
Im 2-D-Code für Teileverbauprüfung und Bauzustandsdokumentation wird immer die 11-stellige
Teilnummer gefolgt von dem 3-stelligen Farbkennzeichen benutzt. Das 14-stellige Datenfeld wird
linksbündig ohne gliedernde Punkte oder Leerzeichen beschrieben, nicht belegte Stellen werden
mit Leerzeichen gefüllt, siehe Bild 7 .
Bild 7 – Teilnummer mit Farbkennzeichen (2-D-Code)
ANMERKUNG 5 Die Teilnummernstruktur der Volkswagen Aktiengesellschaft ist in der
VW 01098 beschrieben.
Teileart
Das Datenfeld „Teileart“ ist optional. Wenn der Anwendungsfall diese Information nicht erfordert,
dann entfällt diese Information ersatzlos und das Datenfeld hat die Länge 0. Die Trennzeichen (#)
sind immer zu setzen, auch wenn der Feldinhalt leer ist.
Die Teileart enthält kontextspezifische Angaben zum Fahrzeugbauteil. Für die Struktur dieses Da-
tenfeldes sind – abhängig von der Fahrzeugbauteil-Kategorie – verschiedene Formate und Definiti-
onen zulässig. Beispiele siehe Bild 8.
Bild 8 – Teileart (2-D-Code)
Die Inhalte dieses Datenfeldes sind vom zuständigen Entwickler festzulegen und in der Teilezeich-
nung zu dokumentieren.
DUNS-Nummer
Das Datenfeld „DUNS-Nummer“ ist optional. Wenn der Anwendungsfall diese Information nicht er-
fordert, dann entfällt die DUNS-Nummer ersatzlos und das Datenfeld hat die Länge 0. Die Trenn-
zeichen (#) sind immer zu setzen, auch wenn der Feldinhalt leer ist.
Die DUNS-Nummer ist eine international genormte Lieferantennummer. Das Datenfeld ist 9-stellig,
siehe Bild 9.
5.1.1  
5.1.2  
5.1.3  


### 第 13 页
Seite 13
VW 01064: 2018-06
Bild 9 – DUNS-Nummer (2-D-Code)
ANMERKUNG 6 Die Verwendung der VW-Lieferantennummer (System KRIAS) oder anderer
Schlüsselnummern ist nicht zulässig.
Herstelldatum
Das Datenfeld „Herstelldatum“ ist optional. Wenn der Anwendungsfall diese Information nicht erfor-
dert, dann entfällt das Herstelldatum ersatzlos und das Datenfeld hat die Länge 0. Die Trennzei-
chen (#) sind immer zu setzen, auch wenn der Feldinhalt leer ist.
Das Herstelldatum beschreibt den Zeitpunkt der technischen Fertigstellung des Bauteils (verbau-
fertiger bzw. anlieferfertiger Zustand).
Das Datenfeld „Herstelldatum“ ist immer 6-stellig in der Form TTMMJJ (TagTagMonatMonatJahr-
Jahr), siehe Bild 10. Wenn die Tagesinformation nicht zur Verfügung steht, ist immer der erste Ar-
beitstag der Woche einzutragen.
Bild 10 – Herstelldatum (2-D-Code)
Baugruppendaten
Erfolgt die Kennzeichnung nur zwecks Teileidentifikation, dann können die Baugruppendaten er-
satzlos entfallen. Die Zeichen „*“ entfallen ebenfalls.
Der Aufbau der Baugruppendaten ist in Abschnitt 4.1 beschrieben.
ANMERKUNG 7 Die Berechnung der Prüfziffer erfolgt ausschließlich für die Baugruppendaten.
Die übrigen Datenfelder des 2-D-Codes werden nicht in die Prüfzeichenberechnung einbezogen.
Zusatzdaten
Für Entwickler und Lieferanten frei verfügbar.
Falls die Notwendigkeit besteht, innerhalb der Zusatzdaten die Information zu strukturieren, darf
das Minus-Zeichen „-“ als Trennzeichen benutzt werden.
Sonderzeichen in der Datenfolge
Das Zeichen „#“ steht jeweils zwischen den vier ersten Datenfeldern einer Datenfolge (Trennzei-
chen). Trennzeichen sind immer zu setzen, auch wenn ein Feldinhalt ganz entfällt.
Das Zeichen „*“ steht am Anfang und Ende der Baugruppendaten in der Datenfolge. Entfallen die
Baugruppendaten, so entfallen auch diese Sonderzeichen.
Das Zeichen „=“ schließt die Datenfolge für Bauzustandsdokumentation und Verbaukontrolle ab
(Ende-Zeichen).
Das Zeichen „-“ ist als Trennzeichen innerhalb der Zusatzdaten zulässig.
5.1.4  
5.1.5  
5.1.6  
5.1.7  


### 第 14 页
Seite 14
VW 01064: 2018-06
Ausführungsbeispiele für 2-D-Codes
Standardbedatung für Teileverbauprüfung mit Bauzustandsdokumentation
Die Standardbedatung für Teileverbauprüfung mit Bauzustandsdokumentation enthält die Datenfel-
der „Teilnummer mit Farbkennzeichen“ und „Baugruppendaten“ (im Beispiel mit 15-stelligem
Standardaufbau). Die Datenfelder „Teileart“, „DUNS-Nummer“ und „Herstelldatum“ sind leer.
Von dieser Standardbedatung (siehe Bild 11) darf nur in begründeten Fällen abgewichen werden.
Jede Abweichung ist mit allen Prozessbeteiligten abzustimmen.
Bild 11 – Standardbedatung (2-D-Code)
Mit den Randbedingungen aus Abschnitt 6.2 entstehen folgende Codesymbole, siehe Tabelle 1.
Tabelle 1
Ausführung
Quadratisches Codesymbol
Rechteckiges Codesymbol
Codesymbol
Größe in Dots
22 × 22
16 × 36
Größe in mm
ohne Beruhigungszone
11,22 × 11,22
8,16 × 18,36
Größe in mm
mit Beruhigungszone
15,22 × 15,22
12,16 × 18,36
Minimalbedatung für Teileverbauprüfung
Von der Standardbedatung kann abgewichen werden, wenn die Bauteilegeometrie eine Standard-
bedatung nicht zulässt. Diese Abweichung von der Standardbedatung ist mit allen Prozessbeteilig-
ten abzustimmen.
Die Minimalbedatung für eine Teileverbauprüfung ohne Bauzustandsdokumentation enthält nur das
Datenfeld „Teilnummer“ 14-stellig. Die Datenfelder „Teileart“, „DUNS-Nummer“ und „Herstelldatum“
haben keinen Inhalt, das Datenfeld „ „Baugruppendaten“ ist entfallen, siehe Bild 12.
Bild 12 – Minimalbedatung (2-D-Code)
Mit den Randbedingungen aus Abschnitt 6.2 entstehen folgende Codesymbole, siehe Tabelle 2
5.2  
5.2.1  
5.2.2  


### 第 15 页
Seite 15
VW 01064: 2018-06
Tabelle 2
Ausführung
Quadratisches Codesymbol
Rechteckiges Codesymbol
Codesymbol
Größe in Dots
18 × 18
12 × 26
Größe in mm
ohne Beruhigungszone
9,18 × 9,18
6,21 × 13,26
Größe in mm
mit Beruhigungszone
13,18 × 13,18
10,21 × 17,26
Minimalbedatung für Bauzustandsdokumentation
Wenn die Bauteilgeometrie es zwingend erfordert, dann kann für den Anwendungsfall der reinen
Bauzustandsdokumentation von der Standardbedatung abgewichen werden. Diese Abweichung
von der Standardbedatung ist mit allen Prozessbeteiligten abzustimmen. In diesem Fall wird die
unter Abschnitt 4.1 beschriebene BZD-Datenfolge als Data Matrix Code geschrieben, siehe
Bild 13. Das Leseergebnis dieses 2-D-Codes entspricht genau dem des Codes 39.
Bild 13 – Minimalbedatung für BZD (2-D-Code)
Mit den Randbedingungen aus Abschnitt 6.2 entstehen folgende Codesymbole, siehe Tabelle 3.
Tabelle 3
Ausführung
Quadratisches Codesymbol
Rechteckiges Codesymbol
Codesymbol
Größe in Dots
16 × 16
12 × 26
Größe in mm
ohne Beruhigungszone
8,16 × 8,16
6,21 × 13,26
Größe in mm
mit Beruhigungszone
12,16 × 12,16
10,21 × 17,26
ANMERKUNG 8 Der Aufbau der Baugruppendaten ist im Konzern-Baugruppen-Katalog vorgege-
ben. Für die Verschlüsselung des Herstellers ist in den Baugruppendaten ein 4-stelliger Schlüssel
(Hersteller-Code) vorgesehen. Der Hersteller-Code nach VW 10540-1 ist dreistellig und mit führen-
dem Leerzeichen zu verwenden. Kontakt über: <EMAIL>
5.2.3  


### 第 16 页
Seite 16
VW 01064: 2018-06
Maximalbedatung für Teileverbauprüfung mit Bauzustandsdokumentation
Je nach Anwendungsfall kann die Bedatung erweitert werden bis zur Maximalbedatung. Hier sind
alle Datenfelder inklusive Zusatzdaten gefüllt. Die Länge der Datenfelder „Teileart“ „ und „Zusatz-
daten“ hängt vom jeweiligen Anwendungsfall ab, siehe Bild 14.
Bild 14 – Maximalbedatung (2-D-Code)
Die Größe des Codesymbols ist abhängig vom Dateninhalt. Das oben gezeigte Bild 14 mit 65 Zei-
chen führt mit den Randbedingungen aus Abschnitt 6.2 zu folgenden Codesymbolen, siehe
Tabelle 4
Tabelle 4
Ausführung
Quadratisches Codesymbol
Rechteckiges Codesymbol
Codesymbol
Größe in Dots
32 × 32
16 × 48
Größe in mm
ohne Beruhigungszone
16,32 × 16,32
8,16 × 24,48
Größe in mm
mit Beruhigungszone
20,32 × 20,32
12,16 × 28,48
Ausführung und Gestaltung der Etiketten (1-D-Code und 2-D-Code)
Codesymbol für den Strichcode (1-D-Code)
Bei der Erzeugung des 1-D-Codesymbols sind folgende Anforderungen zu beachten:
–
Verwendung CODE 39 nach ISO/IEC 16388
–
Gesamtsymbolqualität nach DIN EN ISO/IEC 15416 muss 3,5 oder besser sein
–
Modulbreite x (siehe Anhang A): ca. 0,254 mm
–
Modulbreitenverhältnis: mindestens 1 : 2,5
–
Lückenbreitenverhältnis: wie Modulbreitenverhältnis
–
Druckauflösung: mindestens 300 dpi
–
Ruhezone: mindestens 3 mm je Seite
–
Höhe der Striche: ca. 10 mm
–
Prüfziffernberechnung (automatisch) entfällt
–
Entsprechend diesen Angaben ergibt sich eine Symbolgröße von ca. 63 mm × 10 mm bei ei-
ner Datenfolge mit 15 Zeichen
–
Abweichungen sind mit allen Beteiligten abzustimmen
5.2.4  
6  
6.1  


### 第 17 页
Seite 17
VW 01064: 2018-06
Codesymbol für den Matrix Code (2-D-Code)
Bei der Erzeugung des 2-D-Codesymbols sind folgende Anforderungen zu beachten:
Einsatz in fahrzeugbauenden Werken
–
Verwendung Data Matrix Code
–
Gesamtsymbolqualität nach ISO/IEC 15415 muss 3 oder besser sein. Diese Symbolqualität ist
über die gesamte Prozesskette bis zum Einbauort sicherzustellen.
–
Fehlerkorrektur ECC 200
–
Modulgröße x (siehe Anhang A) mindestens 0,50 mm
–
Druckerauflösung 300 dpi oder höher
–
Beruhigungszone beträgt mindestens 2 mm je Seite, eine kleine Beruhigungszone ist nur in
Abstimmung mit allen Beteiligten zulässig.
–
Matrixgröße und Zeichensatz über Auto-Funktion erstellen.
–
Entsprechend diesen Angaben ergibt sich eine Symbolgröße von ca. 20 mm × 20 mm für eine
vollständig gefüllte Datenfolge, für Sammelkennzeichnungen ergeben sich größere Symbole.
–
Abweichungen sind mit allen Beteiligten abzustimmen.
Einsatz in Motoren-Werken
–
Verwendung Data Matrix Code
–
Gesamtsymbolqualität nach ISO/IEC TR 29158 (ehemals AIM-DPM 2006) liegt bei 3 oder bes-
ser. Diese Symbolqualität ist über die gesamte Prozesskette bis zum Einbauort sicherzu-
stellen.
–
Fehlerkorrektur ECC 200
–
Modulgröße x (siehe Anhang A) mindestens 0,50 mm
–
Druckerauflösung 300 dpi oder höher
–
Ruhezone beträgt mindestens 4-fache Modulgröße.
–
Matrixgröße und Zeichensatz über Auto-Funktion erstellen.
–
Entsprechend dieser Angaben ergibt sich eine Symbolgröße bei 20x20 Dots von ca. 10 mm ×
10 mm für einen Standardstring mit 32 bis 36 Zeichen.
–
Bei gleichen Bauteilen (Lieferanten übergreifend) ist das gleiche Kennzeichnungsverfahren,
die gleiche Größe sowie die gleiche Position zu wählen. Das ist vom zuständigen Entwickler in
der technischen Zeichnung zu dokumentieren.
–
Position muss immer lesbar im verbauten Zustand sein.
–
Nachweis über die Qualitätseinhaltung der Lesbarkeit muss vom Hersteller des DMC geliefert
werden.
–
Abweichungen, z. B. durch Platzmangel, müssen ausdrücklich vermerkt werden und sind mit
allen Beteiligten abzustimmen.
6.2  
6.2.1  
6.2.2  


### 第 18 页
Seite 18
VW 01064: 2018-06
Einsatz in Getriebe-Werken
–
Verwendung Data Matrix Code
–
Gesamtsymbolqualität nach ISO/IEC TR 29158 (ehemals AIM-DPM 2006) liegt bei 3 oder bes-
ser. Diese Symbolqualität ist über die gesamte Prozesskette bis zum Einbauort sicherzu-
stellen.
–
Fehlerkorrektur ECC 200
–
Die Ruhezone um die Matrix herum muss je Seite mindestens die Größe von 2 Zellen aufwei-
sen. Die Zellgröße muss so gewählt werden, dass die einzelnen Zellen weit größer als die
größten Störstrukturen auf der Oberfläche sind.
Im Übrigen gelten die Angaben des Dokuments „Qualitätsanforderungen für Teilemarkierungen“.
Klarschriftinformation (1-D und 2-D-Code)
Neben dem Codesymbol muss die Kennzeichnung eine Klarschriftinformation enthalten. Sie dient
der Handeingabe der Daten, wenn das Codesymbol defekt und unlesbar ist oder Anlagenstörun-
gen vorliegen. Anforderungen sind:
–
1-D-Code: Die Klarschrift zeigt die vollständige Datenfolge inklusive Prüfziffer
–
2-D-Code: Enthält der 2-D-Code das Datenfeld „Baugruppendaten“ oder „Teilnummer“, sind
diese verpflichtend als Klarschrift darzustellen, optional können alle Datenfelder als Klarschrift-
information aufgebracht werden.
–
die Klarschriftdaten des Datenfeldes „Baugruppendaten“ werden am Anfang und Ende um das
Zeichen „*“ ergänzt
–
die Klarschriftinformation der Teilnummer erfolgt zur besseren Lesbarkeit mit je einem Leerzei-
chen zwischen Vornummer, Mittelgruppe, Endnummer und Index
–
die Schrifthöhe beträgt mindestens 2 mm
–
die Klarschrift erscheint vorzugsweise zentriert unter dem Barcode (andere Formen der Anord-
nung sind in Ausnahmefällen zulässig)
–
zur Unterscheidung zwischen Ziffer „0“ und Buchstabe „O“ ist die Null gestrichen zu schreiben,
dazu ist ein geeigneter Font zu benutzen, z. B. „Consolas“ θ.
Wenn die Geometrie des Bauteils keinen ausreichenden Platz für die BZD-Klarschriftinformation
bietet, dann kann im Produktteam, SET o.ä. beschlossen werden, auf die Klarschrift zu verzichten.
Diese Entscheidung ist mit allen Prozessbeteiligten abzustimmen.
6.2.3  
6.3  


### 第 19 页
Seite 19
VW 01064: 2018-06
Einteiliges, bauteilfestes Etikett (1-D und 2-D-Code)
Bei Ausführung und Gestaltung des Etiketts sind folgende Anforderungen zu berücksichtigen:
–
das Etikett ist selbstklebend und darf sich nach Aufkleben nicht unbeabsichtigt lösen
–
das Druckbild kann invers erfolgen, wenn dies zwingend erforderlich ist (Designvorgabe)
–
der Aufdruck darf nicht nachträglich verschmieren
–
Untergrund: RAL 9010 (reinweiß – inklusive Barcodefeld), alternativ silbergraue, metallisierte
Folie mit Kalküberzug für Thermotransferdrucker
–
Schrift: Tiefschwarz
Die Anbringung am Fahrzeugbauteil ist so auszuführen, dass die Daten im eingebauten Zustand
sichtbar und technisch lesbar sind. Die Lage und Orientierung sind in einer technischen Zeichnung
festzulegen.
Es ist zulässig, wenn ein bereits vorhandenes, anderweitigen Zwecken dienendes Etikett für die
hier beschriebene Kennzeichnung mit genutzt wird. Die weiteren Inhalte dieses Etiketts dürfen die
Handhabung der hier beschriebenen Kennzeichnung nicht beeinträchtigen (z. B. durch weitere
gleichartige Codesymbole).
Zweiteilige oder mehrteilige Etiketten (1-D und 2-D-Code)
Das einteilige, bauteilfeste Etikett ist um einen oder mehrere abreißbare Etikettenteile zu ergän-
zen. Diese werden in der Fertigung abgetrennt und z. B. in die Wagenprüfkarte geklebt. Mindes-
tens der zuletzt zu verwendende, abreißbare Teil enthält das Codesymbol. Alle anderen beinhalten
mindestens die Klarschriftinformation. Es gelten sinngemäß die Anforderungen wie für das einteili-
ge Etikett.
Für abreißbare Etikettenteile ist ein unnötig breites oder hohes Format unzulässig, da z. B. in der
Wagenprüfkarte vorgegebene Felder zum Einkleben nicht überdeckt werden dürfen. Das zu ver-
wendende Etikettenformat orientiert sich an der Größe des Codesymbols inklusive Ruhezone und
Klarschriftinformation. Empfohlene Formate sind:
–
1-D-Code: Datenfolge 15 Zeichen (Standarddatenfolge): ca. 80 mm × 20 mm
–
1-D-Code: Datenfolge 21 Zeichen (Baugruppen 005 = Motor, 006 = Getriebe):
ca. 100 mm × 20 mm
–
2-D-Code: Empfohlenes Format für ein Etikett mit Matrix Code ist 20 mm × 30 mm
Etikettenmaterial und Anordnung sind so zu wählen, dass eine schnelle und einfache Trennung
der Teile ohne Beschädigung möglich ist. Der abreißbare Teil darf sich nicht unbeabsichtigt lösen.
Etikettierung außen am ZSB (Sammelkennzeichnung)
Ein später unzugängliches Fahrzeugbauteil benötigt ein mehrteiliges Etikett:
–
2 Teile, wenn im Fahrzeugwerk die Daten über Direkteingabe erfasst werden
–
3 Teile, wenn im Fahrzeugwerk die Daten über Wagenprüfkarte erfasst werden
Im Fertigungsprozess des komplexen ZSBs wird der abreißbare Teil dann am ZSB außen und bau-
teilfest an einer vereinbarten Stelle angeklebt (z. B. Sitz: Außenblech des Sitzgestells).
6.4  
6.4.1  
6.5  


### 第 20 页
Seite 20
VW 01064: 2018-06
ZSB-Begleitkarte (Sammelkennzeichnung)
Wenn eine Etikettierung außen am ZSB nicht möglich ist, stellt der Lieferant eine ZSB-Begleitkarte
bereit. Sie wird mit dem komplexen ZSB ausgeliefert und im weiteren Fertigungsverlauf eingele-
sen. Folgende Inhalte sind vorzusehen:
–
Lieferantenname und Bezeichnung des ZSBs
–
wenn fahrzeugbezogene Anlieferung: Kenn-Nummer oder Fahrgestellnummer des Fahrzeugs
–
wenn nicht fahrzeugbezogene Anlieferung: Zuordnungsnummer ZSB zu ZSB-Begleitkarte
–
alle Barcodes der im ZSB eingebauten, unzugänglichen Baugruppen
–
Abnahmevermerk (Stempel o. ä) des Lieferanten für Korrektheit der übergebenen Daten
Die ZSB-Begleitkarte wird damit zum Bestandteil der Wagenprüfkarte des Fahrzeugs. Sie ist trans-
portsicher am ZSB anzubringen und inhaltlich, gestalterisch und ablauforganisatorisch mit dem be-
treffenden Fahrzeugwerk abzustimmen.
Kennzeichnung direkt im Material (Direktmarkierung DPM – Direct Part Mark)1)
2-D-Codes können direkt in oder auf das Material des Bauteils gebracht werden, wenn technische
oder sonstige Anforderungen (z. B. Lebensdauerkennzeichnung, Designanspruch) dies erfordern.
Die Anforderungen an die Klarschriftinformation sind ebenfalls zu berücksichtigen und sinngemäß
einzuhalten.
Mögliche Markierungstechniken:
–
Lasermarkierung
–
Nadelprägung
–
Elektrochemische Ätzung
–
Tintenstrahldruck
Der Einsatz einer dieser Techniken eignet sich, je nach Lebenserwartung und Materialmix oder Ab-
nutzung durch Umwelteinflüsse und Produktionsvolumen der Teile, für bestimmte Anwendungen.
Verifizierungen
Verifizierung von Barcodes
Die Verifizierung der Barcodes ist nach DIN EN ISO/IEC 15416 vorzunehmen. Die Gesamt-Sym-
bolklasse nach DIN EN ISO/IEC 15416 muss einen Wert von 3,5 oder besser aufweisen.
Verifizierung von 2-D-Codes
Die Verifizierung von 2-D-Codes ist nach ISO/IEC 15415 vorzunehmen. Die Gesamtsymbolqualität
muss einen Wert von 3 oder besser aufweisen.
Bei direktmarkierten 2-D-Codes ist die ISO/IEC TR 29158 zu berücksichtigen, die Gesamtsymbol-
qualität muss auch hier einen Wert von 3 oder besser aufweisen.
ANMERKUNG 9 Bei Einsatz von Direktmarkierungen sind entsprechende Lesegeräte erforderlich
(DPM-Scanner). Diese sind mit allen beteiligten Bereichen abzustimmen (von der Erfassung an
der Linie bis hin zum Kundendienst, um zu gewährleisten, dass die Codes konzernweit auslesbar
sind).
6.6  
6.7  
6.8  
6.8.1  
6.8.2  
1)
Quelle Abschnitt 6.7 „Das Lesen von Direktmarkierungen – 10 wichtige Aspekte“ Firma Cognex und „Neue Standards verifizieren
2D Data Matrix Codes zuverlässig“ – White Paper von Carl W. Gerst III, Cognex Corporation, Senior Director & Business Unit
Manager, ID-Produkte.


### 第 21 页
Seite 21
VW 01064: 2018-06
Sammelkennzeichnung – Vorerfassung von komplexen Zusammenbauten
Für komplexe ZSBs sind die am Fahrzeugbauteil gekennzeichneten Daten ggf. nicht mehr zugäng-
lich (z. B. Airbagmodul im Sitz). In derartigen Fällen ist eine Vorerfassung innerhalb der ZSB Ferti-
gung erforderlich, so dass die Daten am Verbauort des ZSBs verfügbar sind.
Sammelkennzeichnungen sind nur mit Matrix Code realisierbar.
Der Lieferant führt eine Vorerfassung durch Einlesen der einzelnen Baugruppendatensätze durch.
Diese Einzeldatenfolgen werden zu einer Gesamtdatenfolge kombiniert (siehe Bild 15 und Bild 16).
Bild 15 – Sammelkennzeichnung 1
Bild 16 – Sammelkennzeichnung 2
Zu dieser Gesamtdatenfolge wird eine neue Kennzeichnung erstellt, welche dann außen am kom-
plexen ZSB bauteilfest aufgeklebt wird.
Diese Form der Vorerfassung ist nur zulässig, wenn alle im komplexen ZSB vereinigten Baugrup-
pen aus Sicht der späteren Fertigungsabläufe zusammen bleiben. Änderungen (z. B. Austausch
einer Baugruppe) können in der Sammelkennzeichnung nicht nachgeführt werden.
Es gelten sinngemäß die gleichen Anforderungen wie für Einzeletiketten. Bei der Gestaltung ist zu
beachten:
–
für jede Baugruppe in einer Sammelkennzeichnung ist eine Klarschriftinformation erforderlich
–
zwischen 2 Datensätzen trennt das Zeichen „&“
–
der letzte Datensatz enthält das Ende-Zeichen „=“
Ein quadratischer Data Matrix Code kann aus max. 144 Zeilen × 144 Spalten bestehen. Damit sind
1982 ASCII-Zeichen darstellbar. Daraus ergibt sich eine max. Anzahl verkettbarer Datensätze von
23. Aus gebrauchstechnischen Gründen ist die Anzahl auf 20 beschränkt.
Sonderzeichen in der Datenfolge
Das Zeichen „#“ steht jeweils zwischen den vier ersten Datenfeldern einer Datenfolge (Trennzei-
chen). Trennzeichen sind immer zu setzen, auch wenn ein Feldinhalt ganz entfällt. Das Zeichen „*“
steht am Anfang und Ende der Baugruppendaten in der Datenfolge. Entfallen die Baugruppenda-
ten, so entfallen auch diese Sonderzeichen.
Das Zeichen „&“ verbindet jeweils zwei Datenfolgen.
Das Zeichen „=“ schließt die Datenfolge ab (Ende-Zeichen).
Das Zeichen „-“ ist als Trennzeichen innerhalb der Zusatzdaten zulässig.
7  
7.1  


### 第 22 页
Seite 22
VW 01064: 2018-06
Datenaustausch
Der Fertigungsbereich des Lieferanten wird wie eine interne Vorfertigung behandelt. Der Lieferant
erfasst alle im komplexen ZSB verbauten Baugruppen und leitet diese Daten an das betreffende
Fahrzeugwerk weiter.
Diese Vorerfassung ist nur zulässig, wenn eine Fehlzuordnung der Daten zum späteren Fahrzeug
ablauforganisatorisch ausgeschlossen werden kann bzw. entsprechende Notorganisationen zur
Datenkorrektur vorhanden sind.
Datenformate und die gesamte technische Realisierung sind bilateral zu vereinbaren.
BZD – Kennzeichnung und Teileidentifikation mit Transponder (RFID)
Der Einsatz von RFID für die Bauzustandsdokumentation von Serienbauteilen ist in der VW 01067
(ab Ausgabe 2017) beschrieben. Siehe dazu auch die Informationen in der ONE.Konzern Business
Plattform (Lieferantenplattform) http://www.vwgroupsupply.com.
BZD – Kennzeichnung von JIS-Bauteilen
Der DataMatrixCode wird auch zur Kennzeichnung von JIS-Bauteilen genutzt. In diesem Fall wird
die JIS-Information in die Zusatzdaten des DataMatrixCodes geschrieben (vgl. Abschnitt 5.1.6).
Der Aufbau der JIS-Informationen bei Verwendung des DataMatrixCode nach VW01064 ist im
Konzern-JIS-Lastenheft beschrieben. Als Trennzeichen innerhalb der JIS-Information wird das Mi-
nuszeichen benutzt.
7.2  
8  
9  


### 第 23 页
Seite 23
VW 01064: 2018-06
Mitgeltende Unterlagen
Die folgenden zitierten Dokumente sind zur Anwendung dieses Dokuments erforderlich:
PS_1.4_999_1430_03
Bauzustandsdokumentation durchführen
PS_1.4_999_1939_04
Basisliste aktualisieren
PS_1.4_999_1939_05
Festlegung und Umsetzung des BZD-Umfangs im Produktteam
VW 01067
Einsatz von Auto-ID zur eindeutigen Objektkennzeichnung; Serialisie-
rung mit Hilfe von optischen Codierungsverfahren und/oder Radio-Fre-
quency Identification (RFID)
VW 01098
Teilnummernsystem
VW10500
Firmenbezeichnung, Teilekennzeichnung; Richtlinien für die Anwendung
VW 10540-1
Hersteller-Code für Fahrzeugteile
VW 10540-3
Hersteller-Code; Vergabebereiche für Herstellercode-vergebende Aus-
landswerke
WSK.013.290 E
Typenschild für Steuergeräte
DIN EN ISO/IEC
15416
Informationstechnik - Verfahren der automatischen Identifikation und Da-
tenerfassung - Testspezifikation für Strichcodedruckqualität; Lineare
Symbole
ISO/IEC 15415
Informationstechnik - Automatische Identifikation und Datenerfassungs-
verfahren - Testspezifikation für Strichcode-Druckqualität - 2D-Symbole
ISO/IEC 16388
Informationstechnik - Verfahren der automatischen Identifikation und Da-
tenerfassung - Spezifikationen für Strichcode-Symbologien; Code 39
ISO/IEC TR 29158
Informationstechnik - Automatische Identifikation und Datenerfassungs-
verfahren - Qualitätsrichtlinie für die Direktmarkierung von Teilen (DPM)
Literaturverzeichnis
[1]
VW 01068 „Baugruppenkennzeichnung für Motor- und Getriebeherstellende Werke“
[2]
LAH DUM 909 H „Identifikation elektroinscher Fahrzeugsysteme - UDS 80125“
[3]
„Qualitätsanforderungen für Teilemarkierungen der Getriebefertigung
   Volkswagen Kassel“
[4]
Symbologiespezifikation Data Matrix z. B. über: AIM DPM 2006, Industrieverband für
automatische Datenerfassung
[5]
Konzern-Baugruppenkatalog System BG-Online
[6]
Das Lesen von Direktmarkierungen – 10 wichtige Aspekte, Fa. Cognex
[7]
Neue Standards verifizieren 2D Data Matrix Codes zuverlässig, Carl W. Gerst III, Fa.
Cognex
10  
11  


### 第 24 页
Seite 24
VW 01064: 2018-06
Beispiele
Bild A.1 – Modulbreite/Modulgröße von Codesymbolen (x)
Bild A.2 – Standardetikett mit Data Matrix Code
Bild A.3 – Standardetikett mit Strichcode
Bild A.4 – Etikett mit Sammelkennzeichnung
Anhang A (informativ)  


### 第 25 页
Seite 25
VW 01064: 2018-06
Bild A.5 – Etikett mit Sammelkennzeichnung und Zusatzdaten

