# VW_01067_DE_2017-11_使用Auto-ID标识唯一对象.pdf

## 文档信息
- 标题：
- 作者：
- 页数：36

## 文档内容
### 第 1 页
Konzernnorm
VW 01067
Ausgabe 2017-11
Klass.-Nr.:
01152
Schlagwörter:
Codierung, Codierungsverfahren, Kennzeichnung, RFID, Bauteilkennzeichnung,
Fahrzeugkennzei<PERSON>nung, Werkzeugkennzei<PERSON>ng, Mehrwegbehälter<PERSON>,
Packstückkennzeichnung
Einsatz von Auto-ID zur eindeutigen Objektkennzeichnung
Serialisierung mithilfe von optischen Codierungsverfahren und/oder Radio
Frequency Identification (RFID)
Vorwort
Ziel der vorliegenden Norm ist die unverwechselbare Kennzeichnung (Serialisierung) und automa-
tisierte Identifizierung von Objekten mithilfe optischer Codierungsverfahren und/oder Radio Fre-
quency Identification (RFID). Zu diesem Z<PERSON> werden eindeutige Datenstrukturen (Referenz-
Nummern) definiert, die sowohl mit üblichen optischen Codierungsverfahren als auch mit RFID-
Transpondern abgebildet werden können, sodass ein hybrider Technologieeinsatz ermöglicht wird.
Bei Einsatz fortgeschrittener RFID-Techniken besteht damit die Möglichkeit, als Backup auf etab-
lierte Verfahren wie die optische Codierung (Barcode, 2D-Code, DataMatrix) und Klarschrift zu-
rückzugreifen.
Frühere Ausgaben
VW 01067: 2012-10, 2013-06, 2014-09, 2015-10, 2016-11
Änderungen
Gegenüber der VW 01067: 2016-11 wurden folgende Änderungen vorgenommen:
–
Abschnitt 2, Abkürzungen: BZD, DMC, DPM, KSU, TLD und ZSB hinzugefügt
–
Abschnitt 4.2 hinzugefügt
–
Abschnitt 4.5.1 „Gestaltung der Nummernkreise“, letzter Absatz geändert
–
Abschnitt 4.7.1 „Gestaltung der Nummernkreise“, Warnhinweis hinzugefügt
Norm vor Anwendung auf Aktualität prüfen.
Die elektronisch erzeugte Norm ist authentisch und gilt ohne Unterschrift.
Seite 1 von 36
Fachverantwortung
Normung
EVV/4
Dr. Malte Schmidt
Tel.: +49 5361 9-87011
K-ILI/5 Uwe Stüber
K-ILI
Tel.: +49 5361 9-29063
Uwe Wiesner
Alle Rechte vorbehalten. Weitergabe oder Vervielfältigung ohne vorherige Zustimmung einer Normenabteilung des Volkswagen Konzerns nicht gestattet.
© Volkswagen Aktiengesellschaft
VWNORM-2016-12


### 第 2 页
Seite 2
VW 01067: 2017-11
Inhalt
Seite
Anwendungsbereich ................................................................................................... 2
Symbole und Abkürzungen ........................................................................................ 3
Grundlagen der eindeutigen Objektkennzeichnung (Serialisierung) .......................... 4
RFID-Technologie ...................................................................................................... 4
Optische Codierung und Klarschrift (Labels) ............................................................. 7
Abbildung der Dateninhalte zur eindeutigen Objektkennzeichnung ........................... 8
Kennzeichnung von Prototypenteilen inkl. Bauteillastenheft- und
Zeichnungseinträgen .................................................................................................. 8
Gestaltung der Nummernkreise ................................................................................. 9
RFID-Kennzeichnung von Prototypenteilen ............................................................. 10
Optische Kennzeichnung von Prototypenteilen ....................................................... 12
RFID-Einsatz zur Bauzustandsdokumentation (BZD) in der Serie .......................... 14
Gestaltung der Nummernkreise ............................................................................... 15
RFID-Kennzeichnung von Serienteilen (BZD) ......................................................... 15
Optische Kennzeichnung von Serienteilen (BZD) .................................................... 17
Kennzeichnung von Werkzeugen ............................................................................ 18
Gestaltung der Nummernkreise ............................................................................... 18
RFID-Kennzeichnung von Werkzeugen ................................................................... 19
Kennzeichnung innerhalb des Konzerns .................................................................. 19
Optische Kennzeichnung von Werkzeugen ............................................................. 19
Kennzeichnung von Mehrwegbehältern ................................................................... 21
Gestaltung der Nummernkreise ............................................................................... 21
RFID-Kennzeichnung von Mehrwegbehältern ......................................................... 21
Optische Kennzeichnung von Mehrwegbehältern .................................................... 23
Kennzeichnung von Packstücken ............................................................................ 24
Gestaltung der Nummernkreise ............................................................................... 24
RFID-Kennzeichnung von Packstücken ................................................................... 25
Optische Kennzeichnung von Packstücken ............................................................. 26
Kennzeichnung von JIS-Packstücken ...................................................................... 28
Gestaltung der Nummernkreise ............................................................................... 28
RFID-Kennzeichnung von JIS-Packstücken ............................................................ 28
Optische Kennzeichnung von JIS-Packstücken ....................................................... 29
Kennzeichnung von Fahrzeugen ............................................................................. 29
Gestaltung der Nummernkreise ............................................................................... 29
RFID-Kennzeichnung von Fahrzeugen .................................................................... 30
Optische Kennzeichnung von Fahrzeugen .............................................................. 32
Mitgeltende Unterlagen ............................................................................................ 33
6-bit Codierung ......................................................................................................... 35
Beispiele (Referenznummer für Prototypenteile) ..................................................... 36
1
2
3
3.1
3.2
4
4.1
4.1.1
4.1.2
4.1.3
4.2
4.2.1
4.2.2
4.2.3
4.3
4.3.1
4.3.2
4.3.3
4.3.4
4.4
4.4.1
4.4.2
4.4.3
4.5
4.5.1
4.5.2
4.5.3
4.6
4.6.1
4.6.2
4.6.3
4.7
4.7.1
4.7.2
4.7.3
5
Anhang A
Anhang B
Anwendungsbereich
Diese Norm beschreibt die eindeutige Objektkennzeichnung (Serialisierung) mithilfe optischer Co-
dierungsverfahren und/oder RFID. Sie findet Anwendung für Bauteile, Fahrzeuge, Werkzeuge,
Mehrwegbehälter und Packstücke.
Die Kennzeichnung nach dieser Norm ersetzt nicht die Teilekennzeichnung nach VW 10500 und
VW 01064, sowie die Kennzeichnung von Werkzeugen, Hilfswerkzeugen, Prüfeinrichtungen und
Lehren (Typenschild) nach VW 34022.
1  


### 第 3 页
Seite 3
VW 01067: 2017-11
ANMERKUNG 1 Diese Kennzeichnung gilt nicht für diagnosefähige Bauteile. Die Kennzeichnun-
gen für Steuergeräte sind in der WSK.013.290 E „Typenschild für Steuergeräte“ beschrieben.
Diese Norm richtet sich an:
–
Entwickler, die eine eindeutige Objektkennzeichnung spezifizieren
–
Qualitätssicherer, die eine eindeutige Objektkennzeichnung bemustern
–
Lieferanten von Fahrzeugteilen, die eine eindeutige Objektkennzeichnung realisieren
Die Inhalte dieser Norm sind angelehnt an die VDA 5500, VDA 5501, VDA 5509, VDA 5510,
VDA 5520 und berücksichtigt den Status Quo der ISO/IEC-Normierung.
Symbole und Abkürzungen
Application Family Identifier
Bauzustandsdokumentation
Company Code
Company Identification Number
Dun & Bradstreet
Data Identifier
DataMatrix Code
Direct Part Marking
Data Structure Format Identifier
Data Universal Numbering System
End of Transmission
Fahrzeugnummer
Group Separator
Global Transport Label
Issueing Agency Code
International Telecommunications Union
Just in Sequence
Konzern Business Plattform
Klassifizierungssysthematik für Unterlagen
Object Identifier
Part Number
Part Serial Number
Radio Frequency Identification
Record Separator
Serial Number
Technische Leitlinie Dokumentation
Ultra High-Frequency
Unique Item Identifier
User Memory
Unique Partner Identification Key
Unique Resource Locator
Vorserien-Freigabe Fahrzeug
Vehicle Identification Number
Zusammenbau
alphanumerisch
numerisch
2  
AFI
BZD
CC
CIN
D & B
DI
DMC
DPM
DSFID
DUNS
EOT
FZN
GS
GTL
IAC
ITU
JIS
KBP
KSU
OID
PN
PSN
RFID
RS
SN
TLD
UHF
UII
UM
UPIK
URL
VFF
VIN
ZSB
an
n


### 第 4 页
Seite 4
VW 01067: 2017-11
Grundlagen der eindeutigen Objektkennzeichnung (Serialisierung)
RFID-Technologie
Dieses Dokument beschreibt den Einsatz von passiven UHF-Transpondern (Frequenzbereich 860
MHz bis 960 MHz) zum Zweck der eindeutigen Kennzeichnung von Objekten (Serialisierung) und
automatischen Verfolgung. Die Luftschnittstelle entspricht ISO/IEC 18000-63/EPC Class1 Gen 2.
Bei der Wahl des Transponders sind die von der International Telecommunications Union (ITU) un-
terschiedlich festgelegten Frequenzbereiche für UHF-Kommunikation zu berücksichtigen: Europa
(865 MHz bis 868 MHz), USA (902 MHz bis 928 MHz) und Japan (950 MHz bis 960 MHz).
RFID-Transponder verfügen gemäß ISO/IEC 18000-63/EPC Class 1 Gen 2 über vier logische
Speicherbereiche/Memory Banks (MB).
MB00
Kill und Passwort
MB01
Unique Item Identifier (UII)
MB10
Tag Identification (TID)
MB11
User Memory (UM)
Eine detaillierte Spezifizierung und Beschreibung der Speicherbereiche ist den zugrundeliegenden
Normen zu entnehmen.
Zum Speichern von objektbezogenen Daten werden die Bereiche MB01 und MB11 genutzt. Im Be-
reich MB01 wird neben erforderlicher Steuerinformation der Unique Item Identifier (UII) hinterlegt.
Der UII beinhaltet eine exklusiv vergebene, eindeutige Referenznummer. Der Speicherbereich
MB01 kann nach dem initialen Beschreiben vor weiteren Schreibeingriffen geschützt werden
(lock). Innerhalb des Bereichs MB11 befindet sich der User Memory (UM). Auf dem UM können
optional weitere Nutzdaten hinterlegt werden. Dabei ist zu berücksichtigen, dass der UM gegen-
über dem UII über eine geringere Schreib-/Leseperformanz verfügt.
Die Speichergröße der jeweiligen Bereiche variiert in Abhängigkeit vom Anwendungszweck.
Tabelle 1 zeigt die präferierten Größen des UII und des zusätzlichen UM. Der zusätzliche UM sollte
zwischen 128 Bits und 512 Bits betragen, um ausreichend Flexibilität zu gewährleisten. Die Nenn-
werte sind aufgerundet und an verfügbare Speichergrößen angepasst, sodass sichergestellt ist,
dass die jeweiligen anwendungsspezifischen Speicherbedarfe abgedeckt sind.
3  
3.1  


### 第 5 页
Seite 5
VW 01067: 2017-11
Tabelle 1 – Speicherbedarf je Anwendungszweck
Anwendungszweck
minimaler Speicherbedarf
Prototypenbauteile
240 Bit UII
Fahrzeuge (Distribution)
128 Bit UII
Fahrzeuge (Vorserie)
128 Bit UII + 128 Bit UM
Werkzeuge
240 Bit UII
Behälter
240 Bit UII
Packstücke
240 Bit UII
Der Aufbau und die Gestaltung der Dateninhalte folgen ISO/IEC-Standards. Wesentliches Merkmal
der ISO/IEC-orientierten Umsetzung von RFID-Datenstrukturen ist die Konformität und Kompatibili-
tät mit etablierten Barcode/DataMatrix-Standards. Auf diese Weise wird die Koexistenz von Barc-
ode/DataMatrix und RFID sowie die schrittweise Migration zu RFID gewährleistet.
In der Steuerinformation des Transponders wird ein Application Family Identifier hinterlegt (AFI),
der zur Datenfilterung genutzt werden kann (ISO/IEC 15961-1). Die AFIs werden in ISO 17363,
ISO 17364, ISO 17365, ISO 17366 und ISO 17367 applikationsspezifisch benannt. Der vollständi-
ge Aufbau der Steuerinformation inklusive Referenzierung des AFIs ist in den zugrundeliegenden
Normen und Empfehlungen beschrieben (u. a. VDA 5500, VDA 5501, VDA 5509, VDA 5510,
VDA 5520) und wird deshalb an dieser Stelle nicht weitergehend erläutert.
Im UII wird eine eindeutige Referenznummer hinterlegt. Die Syntax und Datenstruktur der Refe-
renznummer basiert auf JAIF Global Radio Frequency Identification (RFID) Item Level Standard,
ISO/IEC 15418 sowie ISO 17363, ISO 17364, ISO 17365, ISO 17366 und ISO 17367. Die applika-
tionsspezifische Gestaltung ist dem Abschnitt 4 zu entnehmen. Der UII wird mit End of Transmissi-
on (EOT) abgeschlossen. Wenn die Datenstruktur des UII den gesamten verfügbaren Speicherbe-
reich belegt (MB01), dann kann EOT entfallen. Freie Bits werden bis zum Erreichen des laufenden
16-bit Worts mit Füllzeichen (Padding) aufgefüllt. Gemäß ISO/IEC 15962 wird dazu eine festgeleg-
te Bitfolge verwendet (monomorphic). Details dazu sind der Tabelle 2 zu entnehmen, die den gene-
rischen Aufbau der ISO/IEC-konformen RFID-Datenstruktur (MB01) inklusive der vorangestellten
Protokoll-Kontrollbits (PC-Header) beschreibt.


### 第 6 页
Seite 6
VW 01067: 2017-11
Tabelle 2 – Schematischer Aufbau von ISO/IEC-konformen RFID-Datenstrukturen
Bit Location
(HEX)
Data Type
Value
Size
Description
MB01: CRC + Protocol Control Word
00 - 0F
CRC-16
Hardware
assigned
16 bits
Cyclic Redundancy
Check
10 - 14
Length
Variable
5 bits
Represents the number
of 16-bit words exclu-
ding the PC field and
the Attribute/AFI field.
15
PC bit 0x15
0b0 or 0b1
1 bit
0 = No valid User Data,
or no
MB11
1 = Valid User Data in
MB11
16
PC bit 0x16
0b0
1 bit
0 = “Extended PC word“
not used
17
PC bit 0x17
0b1
1 bit
1 = Data interpretation
rules based on ISO
18 - 1F
AFI
z. B.
0x90, 0xA1, 0xA2,
0xA3
8 bits
Application Family Iden-
tifier used according to
ISO/IEC 15961-1,
ISO 17363, ISO 17364,
ISO 17365, ISO 17366
and ISO 17367
 
Subtotal
 
32 bits
 
MB01: UII
Start at location
20 Go to end of
data / end of
available me-
mory
Eindeutige Referenznummer (Platzhalter). Applikationsspezifische Daten-
inhalte siehe Abschnitt 4.
End of Trans-
mission
EOT
1 an
EOT(!)
Padding until the
end of the last
16-bit word
0b10,
0b1000,
0b100000,
0b10000010,
0b1000001000,
0b100000100000,
or
0b1000001000001
0
2, 4, 6, 8,
10, 12 or
14 bits
Padding according to
ISO/IEC 15962 (mono-
morphic )
 
Subtotal
 
Variable
Up to 240 bits
 
Total MB01
bits:
 
Variable
Up to 272 bits
Die Dateninhalte werden 6-bit codiert. Es dürfen ausschließlich die im Anhang A markierten Buch-
staben, Ziffern und ausgewählten Sonderzeichen verwendet werden.


### 第 7 页
Seite 7
VW 01067: 2017-11
Optische Codierung und Klarschrift (Labels)
Für die eindeutige Identifizierung von Objekten wird der DataMatrix-Code verwendet. Applikations-
bedingt kann auch Code 128 eingesetzt werden (z. B. Global Transport Label, Mehrwegbehälter).
Die Dateninhalte der Barcodes und DataMatrix-Codes entsprechen dem RFID-Prinzip. Sowohl auf
1D-/2D-Code als auch auf dem RFID-Transponder werden identische Referenz-IDs hinterlegt. Ziel
dieser Vorgehensweise ist der hybride Einsatz von optischen und radiofrequenten AutoID-Metho-
den.
Der Aufbau der Barcodes richtet sich nach ISO/IEC 15417 und wird an dieser Stelle nicht weiter
beschrieben. Der Aufbau der DataMatrix-Codes folgt ISO/IEC 15418 und ISO/IEC 15434. Die Syn-
tax besteht aus dem Message Header, gefolgt von einem Format Header zur Benennung der ein-
gebetteten Datenstruktur und schließlich aus dem Format Trailer als Schlusszeichen. Die einzel-
nen Datenelemente werden mithilfe von DI identifiziert und mithilfe von Group Separators getrennt.
Tabelle 3 zeigt den generischen Aufbau standardkonformer DataMatrix-Codes:
Tabelle 3 – Schematischer Aufbau von ISO/IEC-konformen DataMatrix-Codes
Startsequenz
[)>
Record Separator
R
S
Format Identificator
06
Group Separator
G
S
Eindeutige Referenznummer
..
Group Separator
G
S
Weiteres Datenelement
..
Record Separator
R
S
End of Transmission
EOT
Die Syntax ist so aufgebaut, dass neben der eindeutigen Referenz-ID optional auch zusätzliche
Datenelemente codiert werden können. Voraussetzung ist, dass die zusätzlichen Nutzdaten mithil-
fe von standardisierten DI gekennzeichnet werden (vgl. ISO/IEC 15418), um unternehmensüber-
greifend die richtige Interpretation dieser zusätzlichen Dateninhalte zu gewährleisten. Auf diese
Weise sollen auch proprietäre Lösungen vermieden oder reduziert werden.
Die Dateninhalte werden 8-bit codiert. Um die Synchronisierung von Barcode-/DataMatrix- und
RFID-Datenstrukturen sicherzustellen, dürfen jedoch nur die in der 6-bit Codierung üblichen Buch-
staben, Ziffern und ausgewählte Sonderzeichen (siehe Anhang A) verwendet werden.
3.2  


### 第 8 页
Seite 8
VW 01067: 2017-11
Die Barcodes/DataMatrix-Codes werden auf geeignete Labels gedruckt. Die Ausführung der Co-
des und die Gestaltung der Etiketten und deren Inhalte richten sich nach den Vorgaben der
VW 01064 und VW 10500. Auf den Labels werden, sofern möglich, die Dateninhalte der Codes in
Klarschrift dargestellt. Für den Fall, dass neben den optischen Codes zusätzlich RFID eingesetzt
wird, wird zwecks optischer Kennzeichnung empfohlen, zusätzlich das RFID-Emblem nach ISO/
IEC 29160 anzudrucken. Dabei kann die generische Variante (vgl. Bild 1) oder eine der folgenden
anwendungsbezogenen Varianten verwendet werden (vgl. ISO/IEC 29160):
–
B1 (Mehrwegbehälter)
–
B3 (Transporteinheit, Packstücke)
–
B5 (Produktverpackung)
–
B7 (Produkt)
–
B8 (Frachtcontainer)
Voraussetzung für das Abdrucken des RFID-Emblems ist, dass das Label über geeignete Abmaße
verfügt.
Bild 1 – RFID-Emblem (generisch)
Abbildung der Dateninhalte zur eindeutigen Objektkennzeichnung
Kennzeichnung von Prototypenteilen inkl. Bauteillastenheft- und
Zeichnungseinträgen
Die elektronische Kennzeichnung nach dieser Norm (VW 01067) gilt verpflichtend für alle Prototy-
penteile bis zum Beginn der VFF. Abweichungen sind fahrzeugprojekt- und/oder bauteilspezifisch
möglich.
Diese Norm beschreibt die Verwendung von DataMatrix und/RFID. Prototypenteile werden stan-
dardmäßig mithilfe von DataMatrix gekennzeichnet. Die RFID-Kennzeichnung gilt explizit verpflich-
tend für alle Prototypenteile, die in der RFID-Referenzliste geführt werden. Weitere Bauteile kön-
nen aufgrund fahrzeugprojektspezifischer Notwendigkeit ebenfalls mit RFID gekennzeichnet
werden. Die RFID-Referenzliste steht auf der Konzern Business Plattform (KBP) http://vwgroup-
supply.com unter dem Pfad: Informationen > Geschäftsbereiche > Forschung und Entwicklung >
TE-Logistik > RFID bereit.
Direktzugriff:
Intern
Radio Frequency Identification
Extern
Radio Frequency Identification
4  
4.1  


### 第 9 页
Seite 9
VW 01067: 2017-11
Für die Kennzeichnung von Serienbauteilen zum Zweck der Verbauprüfung und Bauzustandsdoku-
mentation ist die VW 01064 anzuwenden.
Bauteillastenheft- und Zeichnungseinträge
Angabe in Zeichnungen und Bauteillastenheften für die RFID-Kennzeichnung, siehe Bild 2 (Wie-
derholtext NO-E4 aus VW 01014):
Bild 2 – Wiederholtext NO-E4 (RFID-Kennzeichnung)
Gestaltung der Nummernkreise
Die Gestaltung der eindeutigen Referenznummer zur Kennzeichnung von Prototypenteilen wird auf
Basis von drei Nummernkreisen sichergestellt, siehe Tabelle 4:
Tabelle 4 – Gestaltung Nummernkreise
 
Nummernkreis
Anzahl Zeichen
1
Company Identification Number (CIN)
9 Stellen (an)
2
Part Number (PN)/Teilenummer (Konzern)
max. 20 Stellen (an)
3
Part Serial Number (PSN)
max. 9 Stellen (an)
 
Anzahl der Zeichen (gesamt)
max. 40 Stellen (an)
Die Anzahl der Zeichen für CIN, PN und PSN darf insgesamt nicht 33 Zeichen (an) überschreiten.
Inklusive zusätzlicher Zeichen, die für die Abbildung von geeigneten RFID-Datenstrukturen erfor-
derlich sind (vgl. Abschnitt 4.1.2), ergibt sich ein Datenstring von max. 40 Zeichen (an). Diese Zei-
chenlänge kann mithilfe gängiger Speichergrößen abgedeckt werden (240 bit UII), sodass die hyb-
ride Kennzeichnung von Bauteilen mithilfe von RFID und DataMatrix sichergestellt werden kann.
Die Gestaltung der CIN hängt davon ab, ob die Prototypenteile extern oder intern gekennzeichnet
werden, siehe Tabelle 5:
Tabelle 5 – Varianten der Kennzeichnung
 
Kennzeichnung
Company Identification Number
(CIN)
Anzahl Zeichen
1
extern
DUNS des Lieferanten (D & B)
9 Stellen (n)
2
intern
Markenkürzel + Werk
+ Kostenstelle
2 Stellen (an) + 2 Stellen (an)
+ 5 Stellen (n)
Der Besitzer der CIN vergibt die Referenznummer und stellt die Eindeutigkeit sicher.
Der Aufbau der Part Number (PN)/Teilenummer entspricht VW 01098. Teilenummern können über
führende Leerzeichen verfügen. Führende Leerzeichen sind bei der Implementierung optischer
und RFID-basierter AutoID-Systeme besonders zu berücksichtigen. Sie sind zwingend mitzucodie-
ren, um sicherzustellen, dass die vollständige codierte Zeichenkette oder Teile der codierten Zei-
chenkette als Referenz auf IT-systemseitig geführte Zusatzinformationen genutzt werden können.
Die PSN besteht ausschließlich aus Großbuchstaben und Ziffern.
4.1.1  


### 第 10 页
Seite 10
VW 01067: 2017-11
RFID-Kennzeichnung von Prototypenteilen
Innerhalb der Steuerinformation (MB01) wird folgender AFI verwendet: A1 (Produktbezug).
Kennzeichnung durch Lieferanten
Tabelle 6 zeigt den Aufbau der eindeutigen Referenznummer innerhalb des UII (MB01):
Tabelle 6 – Exemplarische RFID-Datenstruktur (extern)
 
Dateninhalt UII (MB01)
Anzahl Zeichen
Wert
1
Data Identifier (DI)
3 Stellen (an)
37S
2
Issueing Agency Code (IAC)
2 Stellen (an)
UN (DUNS)
3
Company Identification Number (CIN)
9 Stellen (n)
*********
4
Part Number (PN)/Teilenummer
max. 20 Stellen (an)
_5G9945093A
5
Trennzeichen
1 Stelle
+
6
Part Serial Number (PSN)
max. 9 Stellen (an)
BA7654321
7
End of Transmission
1 Stelle (an)
EOT (!)
 
Anzahl der Zeichen
max. 40 Stellen (an)
240 bit
Alphanumerische Darstellung:
37SUN********* 5G9945093+BA7654321!
Die Marken Audi und Porsche fordern zusätzlich die Abbildung des Farbcodes (falls vorhanden)
sowie den bauteilspezifischen technischen Stand (falls vorhanden). Der Aufbau der entsprechen-
den Datenstrukturen ist dem Anhang B zu entnehmen.
ANMERKUNG 2 Die Abbildung von Farbcodes und technischen Ständen führt zu einer längeren
Teilenummer. Gleichzeitig darf die Kombination aus DUNS-Nummer, Teilenummer und Serialnum-
mer inkl. AutoID-spezifischer Steuerzeichen maximal 40 (an) betragen, d. h., die Anzahl der ver-
bleibenden Zeichen für die Abbildung der Serialnummer ist auf maximal 5 bzw. 6 Zeichen (an) be-
grenzt.
4.1.2  
4.1.2.1  


### 第 11 页
Seite 11
VW 01067: 2017-11
Kennzeichnung innerhalb des Konzerns
Tabelle 7 zeigt den Aufbau der eindeutigen Referenznummer innerhalb des UII (MB01):
Tabelle 7 – Exemplarische RFID-Datenstruktur (intern)
 
Dateninhalt UII (MB01)
Anzahl Zeichen
Wert
1
Data Identifier (DI)
3 Stellen (an)
37S
2
Issueing Agency Code (IAC)
2 Stellen (an)
SC (firmeninterne
Kennzeichnung)
3
Company Identification Number (CIN)
9 Stellen (an)
*********
4
Part Number (PN)/Teilenummer
max. 20 Stellen (an) _5G9945093A
5
Trennzeichen
1 Stelle
+
6
Part Serial Number (PSN)
max. 9 Stellen (an)
BA7654321
7
End of Transmission
1 Stelle (an)
EOT (!)
 
Anzahl der Zeichen
max. 40 Stellen (an) 240 bit
Alphanumerische Darstellung:
37SSC********* 5G9945093A+BA7654321!
Die Marken Audi und Porsche fordern zusätzlich die Abbildung des Farbcodes (falls vorhanden)
sowie den bauteilspezifischen technischen Stand (falls vorhanden). Der Aufbau der entsprechen-
den Datenstrukturen ist dem Anhang B zu entnehmen.
ANMERKUNG 3 Die Abbildung von Farbcodes und technischen Ständen führt zu einer längeren
Teilenummer. Gleichzeitig darf die Kombination aus DUNS-Nummer, Teilenummer und Serialnum-
mer inkl. AutoID-spezifischer Steuerzeichen maximal 40 (an) betragen, d. h., die Anzahl der ver-
bleibenden Zeichen für die Abbildung der Serialnummer ist auf maximal 5 bzw. 6 Zeichen (an) be-
grenzt.
4.1.2.2  


### 第 12 页
Seite 12
VW 01067: 2017-11
Optische Kennzeichnung von Prototypenteilen
Im Rahmen der Kennzeichnung von Prototypenteilen werden DataMatrix-Codes eingesetzt.
Kennzeichnung durch Lieferanten
Tabelle 8 zeigt die ISO/IEC-konforme Abbildung der DataMatrix-Inhalte (inkl. Steuerzeichen).
Tabelle 8 – Optische Codierung von Prototypenteilen (extern)
Beschreibung
Dateninhalt
Start-Sequenz
[)>
Record Separator (RS)
R
S (ASCII)
Formatidentifikator
06
Group Separator (GS)
G
S (ASCII)
Data Identifier (DI)
37S
Issueing Agency Code (IAC)
UN (DUNS)
Company Identification Number (CIN)
*********
Part Number (PN)/Teilenummer (Konzern)
_5Q9945093A
Trennzeichen
+
Part Serial Number (PSN)
BA7654321
Record Separator (RS)
R
S (ASCII)
End of Transmission (EOT)
EOT (ASCII)
Beispiel: [)>R
S06G
S37SUN********* 5Q9945093A+BA7654321R
S
EOT
Bild 3 zeigt ein entsprechend aufgebautes, exemplarisches DataMatrix-Label:
Bild 3 – Exemplarisches DataMatrix-Label
ANMERKUNG 4 Beispiel-Labels sind unter dem Link für Volkswagen-Mitarbeiter VW01067 -
SampleLabels for Prototype Parts v1.0 oder für externe Lieferanten VW01067 - SampleLabels for
Prototype Parts v1.0 dargestellt.
Wird das Beispiel aus Tabelle 8 um einen spezifischen technischen Stand erweitert (vgl.
Abschnitt 4.1.1), dann gestalten sich die Dateninhalte wie folgt:
Generationsstand „02H“: [)>R
S06G
S37SUN********* 5Q9945093A -02H+BA7654321R
S
EOT
Änderungsstand „AS03“: [)>R
S06G
S37SUN********* 5Q9945093A -AS03+BA7654321R
S
EOT
4.1.3  
4.1.3.1  


### 第 13 页
Seite 13
VW 01067: 2017-11
Kennzeichnung innerhalb des Konzerns
Tabelle 9 zeigt die ISO/IEC-konforme Abbildung der DataMatrix-Inhalte (inkl. Steuerzeichen).
Tabelle 9 – Optische Codierung von Prototypenteilen (intern)
Beschreibung
Dateninhalt
Start-Sequenz
[)>
Record Separator (RS)
R
S (ASCII)
Formatidentifikator
06
Group Separator (GS)
G
S (ASCII)
Data Identifier (DI)
37S
Issueing Agency Code (IAC)
SC (firmeninterne Kennzeichnung)
Markenkürzel + Werk + Kostenstelle
*********
Part Number (PN)/Teilenummer (Konzern)
_5Q9945093
Trennzeichen
+
Part Serial Number (PSN)
BA7654321
Record Separator (RS)
R
S (ASCII)
End of Transmission (EOT)
EOT (ASCII)
Beispiel: [)>R
S06G
S37SSC********* 5Q9945093A+BA7654321R
S
EOT
Bild 4 zeigt ein entsprechend aufgebautes, exemplarisches DataMatrix-Label:
Bild 4 – Exemplarisches DataMatrix-Label
ANMERKUNG 5 Beispiel-Labels sind unter dem Link für Volkswagen-Mitarbeiter VW01067 -
SampleLabels for Prototype Parts v1.0 oder für externe Lieferanten VW01067 - SampleLabels for
Prototype Parts v1.0 dargestellt.
Wird das Beispiel aus Tabelle 8 um einen spezifischen technischen Stand erweitert (vgl .
Abschnitt 4.1.1), dann gestalten sich die Dateninhalte wie folgt:
Generationsstand „02H“: [)>R
S06G
S37SUN********* 5Q9945093A -02H+BA7654321R
S
EOT
Änderungsstand „AS03“: [)>R
S06G
S37SUN********* 5Q9945093A -AS03+BA7654321R
S
EOT
4.1.3.2  


### 第 14 页
Seite 14
VW 01067: 2017-11
RFID-Einsatz zur Bauzustandsdokumentation (BZD) in der Serie
Die elektronische Kennzeichnung von Serienteilen zum Zweck der gesetzlich vorgeschriebenen
Bauzustandsdokumentation (BZD) und/ oder der Verbauprüfung wird mithilfe von DataMatrix und/
oder RFID umgesetzt. Die Art der Kennzeichnung und der Kennzeichnungsort auf dem Bauteil
werden in der Bauteilezeichnung festgelegt, bei ZSBs ohne Teilenummer im PDM-Blatt.
Bauzustandsdokumentation: Jedes Bauteil wird mit einer Kennzeichnung (DataMatrix/ RFID)
versehen. Damit können die Bauteile im Fertigungsprozess bestimmten Fahrgestellnummern zu-
geordnet werden. Auf diese Weise wird die Dokumentation und Rückverfolgung von Fahrzeugbau-
teilen ermöglicht, sodass im Schadensfall (Rückruf) die Anzahl der betroffenen Fahrzeuge einge-
grenzt werden kann.
Die Dokumentation bezieht sich dabei auf Baugruppen. Darunter sind Fahrzeugbauteile oder Zu-
sammenbauten zu verstehen, die im Sinn der vorliegenden Norm kennzeichnungspflichtig sind und
über eine eindeutige Baugruppennummer gekennzeichnet werden. Die Definition der einhergehen-
den Baugruppendaten erfolgt im System BG-ONLINE. Hinweise zur Kennzeichnungspflicht finden
sich in technischen Zeichnungen und TLD-Blättern.
Die Entscheidung, welche Bauteile je Fahrzeugprojekt BZD-pflichtig sind, wird im Produktteam /
Modellreihenteam auf Basis des Prozessstandards 1.4_K-GQZ/I_01_PS „Festlegung und Umset-
zung des BZD-Umfangs im Produktteam“ getroffen. Der Entscheidungsstand zur BZD wird über ei-
ne Sonderform der TLD-Blätter TLD.011.xxx.B0 im KVS veröffentlicht.
Die Kennzeichnung ist eine freigaberelevante Eigenschaft des Bauteils und ist bei der Bemuste-
rung mit zu berücksichtigen. Zu prüfen sind:
–
Erfassbarkeit der Daten unter Fertigungsbedingungen
–
Inhalt der aufgebrachten Datenfolge
–
Ausführung und Anbringung der Kennzeichnung auf dem Bauteil
–
Aufbewahrungsdauer ≥15 Jahre
Die Überprüfung der Kennzeichnung erfolgt im Rahmen der Bemusterung. Der Lieferant bzw. Her-
steller stellt für diesen Zweck serienkonform gekennzeichnete Bauteile zur Verfügung. Der Liefe-
rant bzw. Hersteller sichert ab, dass die hier beschriebenen Anforderungen zur Kennzeichnung in
der laufenden Serienfertigung eingehalten werden (z. B. durch Stichprobenprüfungen).
Die Aufbewahrungsdauer beträgt mindestens 15 Jahre ab Erstellung der Daten (entspricht KSU-
Klasse 7.2). Dokumentiert werden wichtige qualitätsrelevante Einzelinformationen zum gekenn-
zeichneten Fahrzeugbauteil (z. B. Charge verwendeter Rohmaterialien, Hersteller verwendeter Zu-
lieferteile, Prüf- und Einstellwerte, Fertigungsort und -anlage usw.), ordnet diese den Referenzda-
ten zu und archiviert diese. Bei Bedarf sind dann eindeutige Aussagen zur Funktions-, Herstell-
oder Materialqualität möglich.
Stellt der Lieferant einen ZSB her, in dem sich ein BZD-pflichtiges Bauteil befindet (z. B. Kraftstoff-
pumpe innerhalb des ZSB Tank), dann muss der Lieferant das BZD-pflichtige Bauteil erfassen,
dessen Daten den Daten des ZSB hinzufügen und für mindestens 15 Jahre in seiner Dokumentati-
on abspeichern. Das trifft auch auf ZSBs aus einem Steuergerät und mechanischen Bauteilen zu
(z. B. Scheinwerfer mit Steuergerät). Sind die Anforderungen zur Kennzeichnung nicht erfüllt, gel-
ten betroffene Bauteile als fehlerhaft und können gegebenenfalls nicht verbaut werden.
Verbauprüfung: Jedes Bauteil wird mit einer Kennzeichnung (DataMatrix/ RFID) versehen. Die
codierten Daten werden im laufenden Fertigungsprozess genutzt, um den korrekten Verbau eines
Fahrzeugbauteils (technische Ausführung, Alter, Hersteller) zu prüfen.
4.2  


### 第 15 页
Seite 15
VW 01067: 2017-11
Gestaltung der Nummernkreise
Die Gestaltung der eindeutigen Referenznummer zur Kennzeichnung von Serienteilen wird auf Ba-
sis von vier Nummernkreisen sichergestellt, siehe Tabelle 10.
Tabelle 10 – Gestaltung Nummernkreise
 
Nummernkreis
Anzahl Zeichen
1
Company Identification Number (CIN)
9 Stellen (an)
2
Part Number (PN)/Teilenummer (Konzern)
max. 14 Stellen (an)
3
Baugruppe (BGR)
max. 3 Stellen (an)
4
Part Serial Number (PSN)
max. 7 Stellen (an)
 
Anzahl der Zeichen (gesamt)
max. 40 Stellen (an)
Die Anzahl der Zeichen für CIN, PN, BGR und PSN darf insgesamt 32 Zeichen (an) nicht über-
schreiten. Inklusive zusätzlicher Zeichen, die für die Abbildung von geeigneten RFID-Datenstruktu-
ren erforderlich sind (vgl. Abschnitt 4.2.2), ergibt sich ein Datenstring von max. 40 Zeichen (an).
Diese Zeichenlänge entspricht gängigen Speichergrößen (240 bit UII), sodass die hybride Kenn-
zeichnung von Bauteilen mithilfe von RFID und DataMatrix sichergestellt werden kann.
Der Besitzer der CIN vergibt die Referenznummer und stellt die Eindeutigkeit sicher. Der Aufbau
der Part Number (PN)/Teilenummer entspricht VW 01098. Die PSN besteht ausschließlich aus
Großbuchstaben und Ziffern.
Hinweis: Die PSN muss unabhängig von der Teilenummer gebildet werden und in Kombination mit
der BGR über den Archivierungszeitraum von 15 Jahren eindeutig sein.
Zusätzlich wird ein weiterer Parameter auf dem RFID-Transponder hinterlegt, siehe Tabelle 11.
Tabelle 11 – Gestaltung Nummernkreise
 
Nummernkreis
Anzahl Zeichen
1
Herstellercode
max. 4 Stellen (an)
RFID-Kennzeichnung von Serienteilen (BZD)
Innerhalb der Steuerinformation (MB01) wird folgender AFI verwendet: A1 (Produktbezug).
Tabelle 3 zeigt den Aufbau der eindeutigen Referenznummer innerhalb des UII (MB01).
Hinweis: Der Aufbau der BZD-Datenfolge ist im Konzern-Baugruppen-Katalog „BGOnline“ be-
schrieben vgl. VW 01064 Kap 4.1 (Kontakt über <EMAIL>). Im vorliegen-
den Fall wird von BZD-Daten mit einer 7-stelligen Seriennummer ausgegangen.
4.2.1  
4.2.2  


### 第 16 页
Seite 16
VW 01067: 2017-11
Tabelle 12 – Exemplarische RFID-Datenstruktur
 
Dateninhalt UII (MB01)
Anzahl Zeichen
Wert
1 Data Identifier (DI)
3 Stellen (an)
37S
2 Issueing Agency Code (IAC)
2 Stellen (an)
UN (DUNS)
3 Company Identification Number (CIN)
9 Stellen (n)
*********
4 Part Number (PN)/Teilenummer inkl.
Index + Farbcode
max. 14 Stellen (an)
5G4857705M RRA
5 Trennzeichen
1 Stelle
*
6 Baugruppe (BGR)
3 Stellen (an)
Bsp: 209
7 Trennzeichen
1 Stelle
+
8 Part Serial Number (PSN)
max. 7 Stellen (an)
4516616
 
Anzahl der Zeichen
max. 40 Stellen (an)
240 bit
Alphanumerische Darstellung:
37SUN*********5G4857705M RRA*209+4516616
Hinweis: Im Fall, dass der Farbcode nicht verwendet wird, werden die entstehenden Leerzeichen
nicht aufgefüllt, d. h., die Inhalte der Datenstruktur lauten wie folgt:
37SUN*********5G4857705M*209+4516616!
Nach Entfall des Farbcodes ist ausreichend Speicherplatz im UII-Bereich verfügbar, d. h., die Da-
tenstruktur wird mit EOT abgeschlossen.
Tabelle 13 zeigt den Aufbau der Daten im UM:
Tabelle 13 – Datainhalt und Struktur für RFID
 
Dateninhalt UM (MB11)
Anzahl Zeichen
Wert
1
Data Structure Format Identifier (DSFID)
2(hex)
03(hex)
2
Pre-Cursor (Compaction Code + Rel.-OID)
2(hex)
46(hex)
3a Byte Count Indicator Switch
0(2)
 
3b Anzahl der folgenden Bytes
5(10)
0+5 = 05(hex)
 
 
Dateninhalt UM (MB11)
Anzahl Zeichen
Wert
1
Data Identifier (DI)
1 Stelle (an)
V
2
Herstellercode
3-4 Stellen (an)
ABC
3
End of Transmission (EOT)
1 Stelle (an)
EOT (!)
 
Anzahl der Zeichen
max. 6 Stellen (an)
36 bit


### 第 17 页
Seite 17
VW 01067: 2017-11
Optische Kennzeichnung von Serienteilen (BZD)
Im Rahmen der Kennzeichnung von Serienteilen wird der DataMatrix-Code nach VW 01064 einge-
setzt. Neben dem klassischem Labelling ist auch Direct Part Marking (DPM) möglich. Der Data-
Matrix-Code kann dabei sowohl quadratisch als auch rechteckig ausgeführt werden. Beispiele
siehe Tabelle 14.
Tabelle 14
Ausführung
Quadratisches Codesymbol
Rechteckiges Codesymbol
Codesymbol
Größe in Dots
24 x 24 dots
16 x 48 dots
Größe in mm
ohne Beruhigungszone
12,24 x 12,24
8,16 x 24,48
Größe in mm
mit Beruhigungszone
16,24 x 16,24
12,16 x 28,48
Für den DataMatrix-Code gelten folgende Qualitätsanforderungen:
–
Symbolqualität nach ISO/IEC 15415 Klasse B bzw. 2 (Label)
–
Symbolqualität nach ISO/IEC TR 29158  liegt ≥ 3 (DPM)
–
Fehlerkorrektur ECC 200
–
Modulgröße mindestens 0,50 mm
–
Druckerauflösung 300 dpi oder höher
–
Ruhezone beträgt mindestens 2 mm je Seite
Hinweis: Abweichungen von den o. g. Qualitätsstandards sind nur in Abstimmung mit allen Pro-
zessbeteiligten zulässig. Der Nachweis über die Einhaltung der Qualitätsstandards sowie der Les-
barkeit des DataMatrix-Codes ist durch den Lieferanten zu erbringen.
Tabelle 15 – Exemplarischer Dateninhalt für DataMatrix nach VW 01064
Teilnummer inkl. Index und Farbcode
5G4857705M RRA
DUNS
*********
Baugruppe
209
Herstellercode
ABC
Serialnummer
4516616
Prüfziffer
P (Prüfziffer)
Grundsätzlicher Aufbau des Zeichenstrings nach VW 01064:
#Teilnummer#Teilart#DUNS#Herstelldatum*Baugruppendaten*=Zusatzdaten                                  
         
DMC-Codierung des o. g. Beispiels:
#5G4857705M RRA##*********#*209 ABC4516616P*=                                                                
 
4.2.3  


### 第 18 页
Seite 18
VW 01067: 2017-11
Exemplarisches SmartLabel siehe Bild 5.
Bild 5 – RFID-SmartLabel mit Klarschrift und 2D-Code
Weitere Details zum Aufbau des Labels sowie zusätzliche Beispiele sind dem Dokument
„VW01064 SampleLabels DE. Arbeitsstand.pptx“ zu entnehmen.
Das Dokument ist der Lieferantenplattform http://www.vwgroupsupply.com >> Login >> Informatio-
nen >> Geschäftsbereiche >> Produktion >> Radio Frequency Identification (RFID) zu entnehmen.
Kennzeichnung von Werkzeugen
Gestaltung der Nummernkreise
Die Gestaltung der eindeutigen Referenznummer zur Kennzeichnung von Werkzeugen entspricht
VW 34022 (in Anlehnung an DIN 66277) und wird auf Basis von zwei Nummernkreisen realisiert,
siehe Tabelle 16:
Tabelle 16 – Gestaltung Nummernkreise
 
Nummernkreis
Anzahl Zeichen
1
Company Identification Number (CIN)
9 Stellen (an)
2
Inventar-/Werkzeugnummer
max. 18 Stellen (an)
Die Gestaltung der CIN hängt davon ab, ob die Werkzeuge extern oder intern gekennzeichnet
werden, siehe Tabelle 17:
Tabelle 17 – Varianten der Kennzeichnung
 
 
Nummernkreis
Anzahl Zeichen
1
extern
DUNS des Lieferanten (D & B)
9 Stellen (n)
2
intern
Markenkürzel + Werk
+ Kostenstelle
2 Stellen (an) + 2 Stellen (an)
+ 5 Stellen (n)
Die Inventar-/Werkzeugnummer wird durch den Besitzer der CIN vergeben. Der Besitzer stellt die
Eindeutigkeit der Referenznummer sicher.
Optional kann der UM zur Speicherung zusätzlicher Nutzdaten genutzt werden, siehe VW 34022.
4.3  
4.3.1  


### 第 19 页
Seite 19
VW 01067: 2017-11
RFID-Kennzeichnung von Werkzeugen
Innerhalb der Steuerinformation (MB01) wird folgender AFI verwendet: A1 (Produktbezug).
Kennzeichnung durch Lieferanten
Tabelle 18 zeigt den Aufbau der eindeutigen Referenznummer innerhalb des UII (MB01):
Tabelle 18 – Exemplarische RFID-Datenstruktur (extern)
 
Dateninhalt UII (MB01)
Anzahl Zeichen
Wert
1
Data Identifier (DI)
3 Stellen (an)
25S
2
Issueing Agency Code
(IAC)
2 Stellen (an)
UN (DUNS)
3
Company Identification
Number (CIN)
9 Stellen (n)
*********
4
Serial Number (SN)
max. 18 Stellen (an)
ABC*********012345
5
End of Transmission
1 Stelle (an)
EOT (!)
 
Anzahl der Zeichen
max. 33 Stellen (an)
198 bit
.
Alphanumerische Darstellung:
25SUN*********ABC*********012345!
Kennzeichnung innerhalb des Konzerns
Tabelle 19 zeigt den Aufbau der eindeutigen Referenznummer innerhalb des UII (MB01):
Tabelle 19 – Exemplarische RFID-Datenstruktur (intern)
 
Dateninhalt UII (MB01)
Anzahl Zeichen
Wert
1
Data Identifier (DI)
3 Stellen (an)
25S
2
Issueing Agency Code
(IAC)
2 Stellen (an)
SC (firmeninterne Kennzeich-
nung)
3
Company Identification
Number (CIN)
9 Stellen (an)
*********
4
Serial Number (SN)
18 Stellen (an)
ABC*********012345
5
End of Transmission
1 Stelle (an)
EOT (!)
 
Anzahl der Zeichen
max. 33 Stellen (an)
198 bit
Alphanumerische Darstellung
25SSC*********ABC*********012345!
Optische Kennzeichnung von Werkzeugen
Im Rahmen der Kennzeichnung von Werkzeugen wird der DataMatrix-Code eingesetzt.
4.3.2  
4.3.2.1  
4.3.3  
4.3.4  


### 第 20 页
Seite 20
VW 01067: 2017-11
Kennzeichnung durch Lieferanten
Tabelle 20 zeigt die ISO/IEC-konforme Abbildung der DataMatrix-Inhalte (inkl. Steuerzeichen).
Tabelle 20 – Optische Codierung von Werkzeugen (extern)
Beschreibung
Dateninhalt
Start-Sequenz
[)>
Record Separator (RS)
R
S (ASCII)
Formatidentifikator
06
Group Separator (GS)
G
S (ASCII)
Data Identifier (DI)
25S
Issueing Agency Code (IAC)
UN (DUNS)
Company Identification Number (CIN)
*********
Serial Number (SN)
ABC*********012345
Record Separator (RS)
R
S (ASCII)
End of Transmission (EOT)
EOT (ASCII)
Beispiel: [)>R
S06G
S25SUN*********ABC*********012345R
S
EOT
Bild 6 zeigt ein entsprechend aufgebautes, exemplarisches DataMatrix-Label:
Tabelle 21 – Optische Codierung von Werkzeugen (intern)
描述
数据内容
Start-Sequenz
[)>
Record Separator (RS)
R
S (ASCII)
Formatidentifikator
06
Group Separator (GS)
G
S (ASCII)
Data Identifier (DI)
25S
Issueing Agency Code (IAC)
SC (firmeninterne Kennzeichnung)
Company Identification Number (CIN)
*********
Serial Number (SN)
ABC*********012345
Record Separator (RS)
R
S (ASCII)
End of Transmission (EOT)
EOT (ASCII)
4.3.4.1  
Bild 6 – Exemplarisches DataMatrix-Label
4.3.4.2  
康采恩内部标识
Tabelle 21 zeigt die ISO/IEC-konforme Abbildung der DataMatrix-Inhalte (inkl. Steuerzeichen).


### 第 21 页
Seite 21
VW 01067: 2017-11
Beispiel: [)>R
S06G
S25SSC*********ABC*********012345R
S
EOT
Bild 7 zeigt ein entsprechend aufgebautes, exemplarisches DataMatrix-Label:
Bild 7 – Exemplarisches DataMatrix-Label
Kennzeichnung von Mehrwegbehältern
Gestaltung der Nummernkreise
Die Gestaltung der eindeutigen Referenznummer zur Kennzeichnung von Mehrwegbehältern wird
auf Basis von drei Nummernkreisen sichergestellt, siehe Tabelle 22:
Tabelle 22 – Gestaltung der Nummernkreise
数值范围
字符数量
1
Company Identification Number (CIN)
9 Stellen (an)
2
Behältertyp
6 bis 7 Stellen (an)a)
3
Serial Number (SN)
max. 9 Stellen (an)
a)
Zusammenhängender Textstring, beinhaltet keine Leerzeichen
Die Gestaltung der CIN hängt davon ab, ob die Mehrwegbehälter extern oder intern gekennzeich-
net werden, siehe Tabelle 23:
Tabelle 23 – Varianten der Kennzeichnung
标识
Company Identification Number
(CIN)
字符数量
1
extern
DUNS des Lieferanten (D & B)
9 Stellen (n)
2
intern
Markenkürzel + Werk
+ Kostenstelle
2 Stellen (an) + 2 Stellen (an)
+ 5 Stellen (n)
Der Besitzer der CIN vergibt die Referenznummer und stellt die Eindeutigkeit sicher.
RFID-Kennzeichnung von Mehrwegbehältern
Innerhalb der Steuerinformation (MB01) wird folgender AFI verwendet: A3 (Mehrwegbehälter).
Kennzeichnung durch Lieferanten
Tabelle 24 zeigt den Aufbau der eindeutigen Referenznummer innerhalb des UII (MB01):
4.4  
4.4.1  
4.4.2  
4.4.2.1  


### 第 22 页
Seite 22
VW 01067: 2017-11
Tabelle 24 – Exemplarische RFID-Datenstruktur (extern)
Dateninhalt UII (MB01)
字符数量
Wert
1
Data Identifier (DI)
3 Stellen (an)
26B bis 29B
2
Issueing Agency Code (IAC)
2 Stellen (an)
UN (DUNS)
3
Company Identification Number (CIN)
9 Stellen (n)
*********
4
Behältertyp
6 bis 7 Stellen (an)
A153097
5
Trennzeichen
1 Stelle
+
6
Serial Number (SN)
max. 9 Stellen (an)
CS7148945
7
End of Transmission
1 Stelle (an)
EOT (!)
Anzahl der Zeichen
max. 40 Stellen (an)
240 bit
.
Alphanumerische Darstellung:
26BUN*********A153097+CS7148945!
Der Data Identifier dient dabei der Unterscheidung der Behälterarten, siehe Tabelle 25:
Tabelle 25 – Data Identifier für Mehrwegbehälter
Data
Identifier
Behälterart
1
26B
Generische Bezeichnung für Mehrwegbehälter
2
27B
Großladungsträger, Paletten
3
28B
Kleinladungsträger, selbsttragende Spezialverpackungen
4
29B
Hilfsverpackungen (Deckel, Zwischenlagen, innere Verpackungen)
Kennzeichnung innerhalb des Konzerns
Tabelle 26 zeigt den Aufbau der eindeutigen Referenznummer innerhalb des UII (MB01):
Tabelle 26 – Exemplarische RFID-Datenstruktur (intern)
Dateninhalt UII (MB01)
字符数量
Wert
1
Data Identifier (DI)
3 Stellen (an)
26B bis 29B
2
Issueing Agency Code (IAC)
2 Stellen (an)
SC (firmeninterne
Kennzeichnung)
3
Company Identification Number
(CIN)
9 Stellen (an)
*********
4
Behältertyp
6 bis 7 Stellen (an)
A153097
5
Trennzeichen
1 Stelle
+
6
Serial Number (SN)
max. 9 Stellen (an)
CS7148945
7
End of Transmission
1 Stelle (an)
EOT (!)
Anzahl der Zeichen
max. 40 Stellen (an)
240 bit
.
4.4.2.2  


### 第 23 页
Seite 23
VW 01067: 2017-11
Alphanumerische Darstellung:
26BSC*********A153097+CS7148945!
Der Data Identifier dient dabei der Unterscheidung der Behälterarten, siehe Tabelle 27:
Tabelle 27 – Data Identifier für Mehrwegbehälter
Data
Identifier
Behälterart
1
26B
Generische Bezeichnung für Mehrwegbehälter
2
27B
Großladungsträger, Paletten
3
28B
Kleinladungsträger, selbsttragende Spezialverpackungen
4
29B
Hilfsverpackungen (Deckel, Zwischenlagen, innere Verpackungen)
Optische Kennzeichnung von Mehrwegbehältern
Die optische Codierung von Mehrwegbehältern hängt vom jeweiligen Anwendungsszenario ab.
Mithilfe des Codes 128 können sehr hohe Lesereichweiten erzielt werden, sodass Code 128 für
die Kennzeichnung von Mehrwegbehältern empfohlen wird. Die Kennzeichnung mithilfe von Data-
Matrix-Codes ist zulässig.
Kennzeichnung durch Lieferanten
Tabelle 28 zeigt die ISO/IEC-konforme Abbildung der Code-Inhalte:
Tabelle 28 – Optische Codierung von Mehrwegbehältern (extern)
描述
数据内容
Data Identifier (DI)
26B
Issueing Agency Code (IAC)
UN (DUNS)
Company Identification Number (CIN)
*********
Behältertyp
A153097
Trennzeichen
+
Serial Number (SN)
CS7148945
Beispiel: 26BUN*********A153097+CS7148945
Bild 8 zeigt ein entsprechend aufgebautes, exemplarisches Barcode-Label:
Bild 8 – Exemplarisches Barcode-Label
4.4.3  
4.4.3.1  


### 第 24 页
Seite 24
VW 01067: 2017-11
Kennzeichnung innerhalb des Konzerns
Tabelle 29 zeigt die ISO/IEC-konforme Abbildung des Codes 128:
Tabelle 29 – Optische Codierung von Mehrwegbehältern (intern)
描述
数据内容
Data Identifier (DI)
26B
Issueing Agency Code (IAC)
SC (firmeninterne Kennzeichnung)
Company Identification Number (CIN)
*********
Behältertyp
A153097
Trennzeichen
+
Serial Number (SN)
CS7148945
Beispiel: 26BSC*********A153097+CS7148945
Bild 9 zeigt ein exemplarisches Barcode-Label:
Bild 9 – Exemplarisches Barcode-Label
Kennzeichnung von Packstücken
Gestaltung der Nummernkreise
Die Gestaltung der eindeutigen Referenznummer zur Kennzeichnung von Packstücken wird auf
Basis von zwei Nummernkreisen sichergestellt, siehe Tabelle 30:
Tabelle 30 – Gestaltung der Nummernkreise
数值范围
字符数量
1
Company Identification Number (CIN)
9 Stellen (an)
2
Packstück-ID
9 Stellen (n)
Die Gestaltung der CIN hängt davon ab, ob die Packstücke extern oder intern gekennzeichnet
werden, siehe Tabelle 31
4.4.3.2  
4.5  
4.5.1  


### 第 25 页
Seite 25
VW 01067: 2017-11
Tabelle 31 – Varianten der Kennzeichnung
Kennzeichnung
Nummernkreis
Anzahl Zeichen
1
extern
DUNS des Lieferanten (D & B)
9 Stellen (n)
2
intern
Markenkürzel + Werk
+ Kostenstelle
2 Stellen (an) + 2 Stellen (an)
+ 5 Stellen (n)
Die Packstück-ID wird durch den Besitzer der CIN vergeben. Die Packstück-ID darf sich bis zum
Aufbrauchen des Nummernkreises von ********* bis ********* für alle Konzernwerke nicht
wiederholen.  Konzernintern wird die Eindeutigkeit durch das Hinzufügen des Lieferscheindatums
sichergestellt.
RFID-Kennzeichnung von Packstücken
Innerhalb der Steuerinformation (MB01) wird folgender AFI verwendet: A2 (Transporteinheiten).
Gemäß ISO 17365 stehen für die Kennzeichnung von Packstücken in MB01 u. a. folgende Varian-
ten zur Verfügung (vgl. ISO/IEC 15418):
–
1J (Packstück)
–
5J (Mischgebinde)
–
6J (sortenreines Gebinde)
Im Folgenden wird die Umsetzung am Beispiel eines einfachen Packstücks beschrieben.
Kennzeichnung durch Lieferanten
Tabelle 32 zeigt den Aufbau der eindeutigen Referenznummer innerhalb des UII (MB01):
Tabelle 32 – Exemplarische RFID-Datenstruktur (extern)
数据内容 UII (MB01)
字符数量
值
1
Data Identifier (DI)
2 Stellen (an)
1J
2
Issueing Agency Code
(IAC)
2 Stellen (an)
UN (DUNS)
3
Company Identification
Number (CIN)
9 Stellen (n)
*********
4
Packstück-ID
9 Stellen (n)
*********
5
End of Transmission
1 Stelle (an)
EOT (!)
Anzahl der Zeichen
max. 23 Stellen (an)
138 bit
Alphanumerische Darstellung:
1JUN******************!
4.5.2  
4.5.2.1  


### 第 26 页
Seite 26
VW 01067: 2017-11
Kennzeichnung innerhalb des Konzerns
Tabelle 33 zeigt den Aufbau der eindeutigen Referenznummer innerhalb des UII (MB01):
Tabelle 33 – Exemplarische RFID-Datenstruktur (intern)
 
Dateninhalt UII (MB01)
Anzahl Zeichen
Wert
1
Data Identifier (DI)
2 Stellen (an)
1J
2
Issueing Agency Code
(IAC)
2 Stellen (an)
SC (firmeninterne
Kennzeichnung)
3
Markenkürzel + Werk
+ Kostenstelle
9 Stellen (an)
VW111620
4
Packstück-ID
9 Stellen (n)
*********
5
End of Transmission
1 Stelle (an)
EOT (!)
 
Anzahl der Zeichen
max. 23 Stellen (an)
138 bit
Alphanumerische Darstellung:
1JSC******************!
Optische Kennzeichnung von Packstücken
Für die optische Kennzeichnung von Packstücken wird Code 128 eingesetzt.
Kennzeichnung durch Lieferanten
Die Darstellung der eindeutigen Referenznummer und die Abbildung zusätzlicher Dateninhalte
richtet sich nach den Vorgaben des VW Implementation Guideline Global Transport Label (GTL).
Tabelle 34 zeigt die ISO/IEC-konforme Abbildung der Code-Inhalte für das sogenannte License
Plate.
Tabelle 34 – Optische Codierung von Packstücken (extern)
Beschreibung
Dateninhalt
Data Identifier (DI)
1J
Issueing Agency Code (IAC)
UN (DUNS)
Company Identification Number (CIN)
*********
Packstück-ID
*********
Beispiel: 1JUN******************
4.5.2.2  
4.5.3  
4.5.3.1  


### 第 27 页
Seite 27
VW 01067: 2017-11
Bild 10 zeigt ein entsprechend aufgebautes, exemplarisches GTL-Label:
Bild 10 – Exemplarisches Global Transport Label
Kennzeichnung innerhalb des Konzerns
Tabelle 35 zeigt die ISO/IEC-konforme Abbildung der Code-Inhalte:
Tabelle 35 – Optische Codierung von Packstücken (intern)
Beschreibung
Dateninhalt
Data Identifier (DI)
1J
Issueing Agency Code (IAC)
SC (firmeninterne Kennzeichnung)
Markenkürzel + Werk + Kostenstelle
*********
Packstück-ID
*********
Beispiel: 1JSC******************
Bild 11 zeigt ein entsprechend aufgebautes, internes Barcode-Label:
Bild 11 – Exemplarisches Barcode-Label
4.5.3.2  


### 第 28 页
Seite 28
VW 01067: 2017-11
Kennzeichnung von JIS-Packstücken
In Sequenzabwicklungen und fahrzeugbezogenen Anlieferprozessen werden sogenannte JIS-
Packstücke verwendet, die sich in ihrer Kennzeichnung von Standardpackstücken (s. o.) unter-
scheiden. Die Packstücke werden mithilfe von Packstück-IDs gekennzeichnet. Die Packstück-IDs
werden parallel dazu in Lieferavisen (VDA 4987) übertragen und auf den Sendungsbelegen
(VDA 4939) gedruckt.
Gestaltung der Nummernkreise
Die Gestaltung der eindeutigen Referenznummer zur Kennzeichnung von JIS-Packstücken wird
auf Basis folgender Kennungen sichergestellt, siehe Tabelle 36:
Tabelle 36 – Gestaltung der Nummernkreise
 
Nummernkreis
Anzahl Zeichen
1
Company Identification Number (CIN)
9 Stellen (an)
2
Packstück-Akronym
3 Stellen (an)
3
Packstück-Montagelinie
2 Stellen (n)
4
Packstück-Nummer
6 Stellen (n)
Die CIN besteht aus der DUNS-Nummer (D & B) des fahrzeugbauenden Werks. Das Packstück-
Akronym wird durch das fahrzeugbauende Werk vergeben. Die Montagelinie wird im Rahmen der
produktionssynchronen Abrufe (DELJIT/Syncro oder VDA 4986) an den Lieferanten übertragen.
Die Packstücknummer wird durch den Lieferanten vergeben und stellt die Eindeutigkeit der Refe-
renznummer her (000001-999999). Nach Erreichen der 999999 beginnt die Zählfolge wieder bei
000001.
RFID-Kennzeichnung von JIS-Packstücken
Gemäß ISO 17365 stehen für die Kennzeichnung von Packstücken u. a. folgende Varianten zur
Verfügung (vgl. ISO/IEC 15418):
–
3J (JIS-Packstück, einfach)
–
4J (JIS-Packstück, Gebinde)
Im Folgenden wird die Umsetzung am Beispiel eines einfachen Packstücks beschrieben.
Tabelle 37 zeigt den Aufbau der eindeutigen Referenznummer innerhalb des UII (MB01):
4.6  
4.6.1  
4.6.2  


### 第 29 页
Seite 29
VW 01067: 2017-11
Tabelle 37 – Exemplarische RFID-Datenstruktur (extern)
 
Dateninhalt UII (MB01)
Anzahl Zeichen
Wert
1
Data Identifier (DI)
2 Stellen (an)
3J
2
Issueing Agency Code
(IAC)
2 Stellen (an)
UN (DUNS)
3
Company Identification
Number (CIN)
9 Stellen (an)
*********
4
Packstück-Akronym
3 Stellen (an)
SIL
5
Packstück-Montagelinie
2 Stellen (an)
03
6
Packstück-Nummer
6 Stellen (an)
001756
7
End of Transmission
1 Stelle (an)
EOT (!)
 
Anzahl der Zeichen
max. 25 Stellen (an)
150 bit
Alphanumerische Darstellung:
3JUN*********SIL01001536!
Optische Kennzeichnung von JIS-Packstücken
Für die optische Kennzeichnung von Packstücken wird Code 128 eingesetzt.
Beispiel: 3JUN*********SIL01001536
Bild 12 zeigt ein entsprechend aufgebautes Barcode-Label:
Bild 12 – Exemplarisches Barcode-Label
Kennzeichnung von Fahrzeugen
Gestaltung der Nummernkreise
Die Gestaltung der eindeutigen Referenznummer zur Kennzeichnung von Fahrzeugen wird auf Ba-
sis von zwei Nummernkreisen sichergestellt, siehe Tabelle 38:
Tabelle 38 – Gestaltung der Nummernkreise
 
Nummernkreis
Anzahl Zeichen
1
Vehicle Identification Number (VIN)
17 Stellen (an)
2
Fahrzeugnummer (FZN)
11 Stellen (an)
4.6.3  
4.7  
4.7.1  


### 第 30 页
Seite 30
VW 01067: 2017-11
Der Aufbau der Referenznummer hängt davon ab, ob die Fahrzeuge im Rahmen der Vorserienver-
folgung oder im Rahmen der Fahrzeugdistribution (Serie) gekennzeichnet werden, siehe
Tabelle 39:
Tabelle 39 – Varianten der Kennzeichnung
 
Kennzeichnung
Fahrzeugkennzeichnung
Anzahl Zeichen
1
Fahrzeug-
distribution
Vehicle Identification Number
(VIN)
17 Stellen (n)
2
Vorserie
Fahrzeugnummer (FZN)
+ Vehicle Identification Number
(VIN)
11 Stellen (an) + 17 Stellen (an)
Der Aufbau der VIN für die Kennzeichnung innerhalb der Fahrzeugdistribution (Serie) entspricht
ISO 3779 und wird vom Fahrzeughersteller vergeben.
Warnhinweis
Die Fahrzeugidentifizierungsnummer (VIN) gilt innerhalb der europäischen Union (EU) als
personenbezogene Information und unterliegt damit der Datenschutzgrundverordnung (EU-
DSGVO), d. h., die Nutzung der VIN bedarf einer rechtlichen Grundlage.
Daher ist die gesetzliche oder sonstige Notwendigkeit, z. B. aus Sicht der Hersteller- und
Händler-Prozesse, für ein elektronisches Auslesen der VIN am Fahrzeug zu beschreiben
und in Abstimmung mit den jeweils verantwortlichen Datenschutzbeauftragten zu dokumen-
tieren.
Innerhalb der Vorserie werden Fahrzeuge (Entwicklungsträger) mit der internen Fahrzeugnummer
(FZN) gekennzeichnet. Die VIN wird innerhalb der Vorserie nicht durchgängig genutzt, sodass sie
im Rahmen der Fahrzeugkennzeichnung lediglich als zusätzliches, optionales Attribut geführt wird.
Der Fahrzeughersteller trägt die Verantwortung für die Eindeutigkeit der Referenznummern.
Im Folgenden werden die Datenstrukturen zur Fahrzeugkennzeichnung dargestellt.
RFID-Kennzeichnung von Fahrzeugen
Innerhalb der Steuerinformation (MB01) wird folgender AFI verwendet: 90 (Fahrzeugbezug).
RFID-Kennzeichnung von Fahrzeugen (Fahrzeugdistribution)
Tabelle 40 zeigt den Aufbau der eindeutigen Referenznummer innerhalb des UII (MB01). Der Auf-
bau entspricht VDA 5520.
4.7.2  
4.7.2.1  


### 第 31 页
Seite 31
VW 01067: 2017-11
Tabelle 40 – Exemplarische RFID-Datenstruktur (Fahrzeugdistribution)
 
Dateninhalt UII (MB01)
Anzahl Zeichen
Wert
1
Data Identifier (DI)
1 Stelle (an)
I
2
Vehicle Identification
Number (VIN)
17 Stellen (an)
WVWZZZ1JZ3W123456
3
End of Transmission
1 Stelle (an)
EOT (!)
 
Anzahl der Zeichen
max. 19 Stellen (an)
114 bit
Alphanumerische Darstellung:
IWVWZZZ1JZ3W123456!
RFID-Kennzeichnung von Prototypenfahrzeugen (Vorserie)
Tabelle 41 zeigt den Aufbau der eindeutigen Referenznummer innerhalb des UII (MB01) und die
zusätzliche Nutzung des UM (MB11):
Tabelle 41 – Exemplarische RFID-Datenstruktur (Vorserie)
 
Dateninhalt UII (MB01)
Anzahl Zeichen
Wert
1
Data Identifier (DI)
2 Stellen (an)
1Y
2
Fahrzeug-Nummer (FZN)
11 Stellen (an)
VW462480574
3
End of Transmission
1 Stelle (an)
EOT (!)
 
Anzahl der Zeichen
max. 14 Stellen (an)
84 bit
 
 
Dateninhalt UM (MB11)
Anzahl Zeichen
Wert
1
Data Structure Format Identifier
(DSFID)
2(hex)
03(hex)
2
Pre-Cursor (Compaction Code
+ Rel.-OID)
2(hex)
46(hex)
3
Byte Count Indicator Switch
1(2)
‘0’(2)
4
Anzahl der folgenden Bytes
7(2)
N(2)
 
 
Dateninhalt UM (MB11)
Anzahl Zeichen
Wert
1
Data Identifier (DI)
1 Stelle (an)
I
2
Vehicle Identification Number (VIN)
17 Stellen (an)
WVWZZZ1JZ3W123456
3
End of Transmission (EOT)
1 Stelle (an)
EOT (!)
 
Anzahl der Zeichen
max. 19 Stellen (an)
114 bit
Alphanumerische Darstellung:
Unique Item Identifier (UII): 1YVW462480574!
User Memory (UM): IWVWZZZ1JZ3W123456!
4.7.2.2  


### 第 32 页
Seite 32
VW 01067: 2017-11
Optische Kennzeichnung von Fahrzeugen
Optische Kennzeichnung von Fahrzeugen (Fahrzeugdistribution)
Die optische Codierung und Gestaltung der Labels zur Fahrzeugkennzeichnung folgt VDA 5520.
Optische Kennzeichnung von Prototypenfahrzeugen (Vorserie)
Tabelle 42 zeigt die ISO/IEC-konforme Abbildung der DataMatrix-Inhalte (inkl. Steuerzeichen).
Tabelle 42 – Optische Codierung von Fahrzeugen (Vorserie)
Beschreibung
Dateninhalt
Start-Sequenz
[)>
Record Separator (RS)
R
S (ASCII)
Formatidentifikator
06
Group Separator (GS)
G
S (ASCII)
Data Identifier (DI)
1Y
Fahrzeug-Nummer (FZN)
VW462480574
Group Separator (GS)
G
S (ASCII)
Data Identifier (DI)
I
Vehicle Identification Number (VIN)
WVWZZZ1JZ3W123456
Record Separator (RS)
R
S (ASCII)
End of Transmission (EOT)
EOT (ASCII)
Beispiel: [)>R
S06G
S1YVW462480574G
SIWVWZZZ1JZ3W123456R
S
EOT
Bild 13 zeigt ein entsprechend aufgebautes, exemplarisches DataMatrix-Label:
Bild 13 – Exemplarisches DataMatrix-Label
4.7.3  
4.7.3.1  
4.7.3.2  


### 第 33 页
Seite 33
VW 01067: 2017-11
Mitgeltende Unterlagen
Die folgenden in der Norm zitierten Dokumente sind zur Anwendung dieser Norm erforderlich:
VW 01014
Zeichnungen; Zeichnungsrahmen und Wiederholtexte
VW 01064
Baugruppenkennzeichnung an Serienfahrzeugen; BZD - Codierung an
mechanischen Fahrzeugteilen
VW 01098
Teilnummernsystem
VW 10500
Firmenbezeichnung, Teilekennzeichnung; Richtlinien für die Anwendung
VW 34022
Kennzeichnung von Werkzeugen, Hilfswerkzeugen, Prüfeinrichtungen
und Lehren (Typenschild); Anforderungen
DIN 66277
Informationstechnik - Automatische Identifikation und Datenerfassungs-
verfahren - ElektronischesTypenschild
ISO/IEC 15415
Informationstechnik - Automatische Identifikation und Datenerfassungs-
verfahren - Testspezifikation für Strichcode-Druckqualität - 2D-Symbole
ISO/IEC 15417
Informationstechnik - Verfahren der automatischen Identifikation und Da-
tenerfassung - Spezifikationen für Strichcode-Symbologien; Code 128
ISO/IEC 15418
Informationstechnik - Automatische Datenerfassung und Identifikation -
GS1 Datenbezeichner und ASC MH10 Datenbezeichner und deren Pfle-
ge
ISO/IEC 15434
Informationstechnik; Automatische Identifikation und Datenerfassungs-
verfahren; Informationstechnik - Transfer-Syntax für Medien zur automa-
tischen Datenerfassung mit hoher Kapazität
ISO/IEC 15961-1
Informationstechnik - Identifizierung von Waren mittels Hochfrequenz
(RFID) für das Management des Warenflusses - Datenprotokoll - Teil 1:
Anwendungsschnittstelle (API)
ISO/IEC 15962
Informationstechnik - Identifizierung von Waren mittels Hochfrequenz
(RFID) für das Management des Warenflusses - Datenprotokoll: Regeln
für die Datencodierung und Funktionen des logischen Datenspeichers
ISO/IEC 18000-63
Informationstechnik - Identifizierung von Waren mittels Hochfrequenz
(RFID) für das Management des Warenflusses - Teil 63: Parameter für
die Kommunikation auf Frequenzen von 860-960 MHz Typ C
ISO/IEC 29160
Informationstechnik - Automatische Identifikation und Datenerfassungs-
verfahren - RFID Emblem
ISO/IEC TR 29158
Informationstechnik - Automatische Identifikation und Datenerfassungs-
verfahren - Qualitätsrichtlinie für die Direktmarkierung von Teilen (DPM)
ISO 17363
Radiofrequente Identifikationstechnik (RFID) in der Logistikkette -
Frachtcontainer
ISO 17364
Radiofrequente Identifikationstechnik (RFID) in der Logistikkette - Wie-
derverwendbare Ladungsträger (RTIs und RPIs)
ISO 17365
Radiofrequente Identifikationstechnik (RFID) in der Logistikkette - Trans-
porteinheiten
ISO 17366
Radiofrequente Identifikationstechnik (RFID) in der Logistikkette - Pro-
duktverpackung
5  


### 第 34 页
Seite 34
VW 01067: 2017-11
ISO 17367
Radiofrequente Identifikationstechnik (RFID) in der Logistikkette - An-
wendung an Produkten
ISO 3779
Straßenfahrzeuge - Fahrzeugidentifizierungsnummer (VIN) - Inhalt und
Aufbau
VDA 4939
Sendungsbelege; Version 3.1
VDA 4986
Datenübertragung von Produktionssynchronen Abrufen - Verfahrensbe-
schreibung - Übertragung von Produktionssynchronen Lieferabrufdaten
per EDI mit EDIFACT und XML von Kunden an Lieferanten; Version 1.2
VDA 4987
Datenübertragung von Lieferavisen - Verfahrensbeschreibung - Übertra-
gung von Lieferavisen per EDI mit EDIFACT und XML; Version 1.3
VDA 5500
Grundlagen zum RFID-Einsatz in der Automobilindustrie; Version 1.2
VDA 5501
RFID im Behältermanagement der Supply Chain; Version 2.2
VDA 5509
AutoID/RFID-Einsatz und Datentransfer zur Verfolgung von Bauteilen
und Komponenten in der Fahrzeugentwicklung; Version 2.4
VDA 5510
RFID zur Verfolgung von Teilen und Baugruppen in der Automobilindust-
rie; Version 2.0
VDA 5520
RFID zur Identifikation von Fahrzeugen in Produktion, Logistik und zur
Realisierung von Dienstleistungen; Version 2.0


### 第 35 页
Seite 35
VW 01067: 2017-11
6-bit Codierung
Tabelle A.1 – 6-bit Codierungstabelle
Character
Binary
Value
Charac-
ter
Binary
Value
Character
Binary
Value
Character
Binary
Value
Space
100000
0
110000
@
000000
P
010000
<EOT>
100001
1
110001
A
000001
Q
010001
<Reserved>
100010
2
110010
B
000010
R
010010
<FS>
100011
3
110011
C
000011
S
010011
<US>
100100
4
110100
D
000100
T
010100
<Reserved>
100101
5
110101
E
000101
U
010101
<Reserved>
100110
6
110110
F
000110
V
010110
<Reserved>
100111
7
110111
G
000111
W
010111
(
101000
8
111000
H
001000
X
011000
)
101001
9
111001
I
001001
Y
011001
*
101010
:
111010
J
001010
Z
011010
+
101011
;
111011
K
001011
[
011011
,
101100
<
111100
L
001100
\
011100
-
101101
=
111101
M
001101
]
011101
.
101110
>
111110
N
001110
<GS>
011110
/
101111
?
111111
O
001111
<RS>
011111
Folgende Zeichen dürfen für die Abbildung von Dateninhalten verwendet werden:
0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z.
Zusätzliche Verwendung von „ “ (Leerzeichen), + (Plus), - (Minus), * (Asterisk), <EOT>, <RS>,
<GS> gemäß Vorgabe.
Anhang A (normativ)  


### 第 36 页
Seite 36
VW 01067: 2017-11
Beispiele (Referenznummer für Prototypenteile)
Zur Veranschaulichung des Aufbaus der Referenznummer, speziell der Part Number (PN)/Teile-
nummer und der Part Serial Number (PSN), sind nachfolgend verschiedene, valide Varianten bei-
spielhaft dargestellt, siehe Bild B.1:
Bild B.1 – Aufbau Referenznummer
Anhang B (informativ)  

