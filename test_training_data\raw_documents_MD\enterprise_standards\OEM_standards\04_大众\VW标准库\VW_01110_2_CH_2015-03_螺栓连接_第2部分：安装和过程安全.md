# VW_01110_2_CH_2015-03_螺栓连接_第2部分：安装和过程安全.pdf

## 文档信息
- 标题：
- 作者：zhangqun
- 页数：16

## 文档内容
### 第 1 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
 
类别号：61000 
关键词：螺杆连接，安装，过程安全，螺杆，连接，过程，安全，螺杆系统，螺杆模具，类
别，装配转数 
 
螺杆连接 
第二部分：安装和过程安全 
 
前言 
目前标题为螺杆连接的系列标准 VW 01110 由以下部分组成： 
—（部分 1）：设计和安装说明 
— 部分 2：安装和过程安全 
— 部分 4：螺杆连接的检测和评估 
 
目前系列标准正在准备的部分： 
— 部分 3：EC—螺杆系统的参数化 
— 部分 5：螺杆情况分析 
 
之前的版本 
VW 01110：1975—10, 1998—12； VW 01110—2: 2008—07, 2012—07 
 
变更 
相对于 VW 01110—2：2012—07 版本，有如下变更： 
— 段落 2，增加“概念”； 
— 段落 3，增加“符合和缩写”； 
— 表 1，类别 B: 电子扭矩螺丝刀只用测纸指示器进行记录； 
— 段落 7.1 具体的说明； 
— 段落 7.2 详细规定了关于防止忘记连接螺杆的“操作螺纹工具的最低要求”； 
— 段落 7.5 详细规定了关于应急策略的“手动安装或应急策略的过程安全”； 
— 之前的“定义重新拧紧扭矩”段落记录在第 2 段中； 
— 图 6：编辑之前的图片 4； 
— 起草新的段落 7.7“类别 A 和 B 的螺丝箱的调整工作” 
— 段落 7.8：取消整车试验； 
— 段落 7.8.1 详细规定了关于动力试验“确定试验参数的一般信息”； 
— 段落 7.9.1.1“在安装过程中螺纹数据的记录”扩充； 
— 段落 8“返工”编辑和扩充； 
— 段落 9“责任”编辑； 
— 更新表 C.1 中的专业负责人； 
— 编辑修订标准并且更新标准的结构。 
 


### 第 2 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
1. 应用范围 
本标准适用于在机动车辆和组件上装配公制 ISO 螺纹和非公制螺纹的螺杆连接，以及采用康
采恩范围内协调安装和拧紧程序的过程保证。对现有设施/设备/工艺流程的回顾性应用应与
专业部门（规划，质量保证）就正在进行的车辆项目，包括大型产品升级（GP）达成一致。 
注 1：螺杆连接的结构设计，设计和执行的要求参见 VW 01110—1。 
 
2. 概念 
以下概念适用于本文档。 
 
3. 符号和缩写 
 
4. 描述 
螺杆连接的描述，例如，在图纸中： 
安装和过程安全根据 VW 01110—2 
 
5. 安装 
5.1 装配速度对螺纹连接工艺的影响 
螺杆连接的连接需要时间来调整部件，表面，垫圈，防腐蚀蜡等，以确保安全的装配过程并
避免严重的设置损失。因为，装配速度直接影响达到的装配的和剩余的预夹紧力的强度。根
据 VW 01129，新型表面保护类型的放行测试在旋入阶段以 200rpm 进行，在最终安装阶段
以 20rpm 进行。 这些速度也证明可以避免粘滑效应。 在生产中可以实现更高的安装速度。 
对于螺杆类别 A 和 B，确定装配速度的影响并且由专业部门根据统计方法评估。该过程在
A.5 节中描述。 
 
5.2 拧紧情况的分类 
拧紧情况必须根据 VDI/VDE 2862 第 1 页分为 A,B 和 C 三个类别。（参见 VW 01110—1，段落
4.3“根据设计的结构对拧紧情况进行分类”）。使用的螺丝刀必须符合表 1 的要求。 
原则上，必须使用针对 A 类和 B 类监控的螺杆连接系统。如果根据关于螺丝刀选择以及记
录的 A.6 节在评估过程后进行验证，那么部分监控的紧固系统也可以用于 B 类（关于部分监
控的紧固系统的必要功能，请参见第 7.3 节）。该评估程序将与负责的专业部门（规划，质
量保证和生产）协商进行。记录的结果是部分监控的紧固系统的过程批准的一部分。 
在负责 A 类和 B 类的专业部门（规划，质量保证和生产）协商后未使用所需螺杆系统的所
有情况下，必须按照第 7.5 节进行过程保护。 
 
5.3 选择可能的螺丝刀 
可以由负责的计划部门根据表 1 的要求选择用于批量生产的螺丝刀。通过选择螺丝刀，可以
实现较低的预紧力分配（有关详细信息，请参阅康采恩设计说明书“受控螺杆螺杆系统”）。 
 
 
 
 
 
 
 


### 第 3 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
表 1—生产中螺杆系统的要求和可能的文档类型 
类别根据
VDI/VDE 
2862 第 1 页 
属性 
最低要求 
技术上可行
的文档类型 
实
际
值
输
出 
状态输
出 
拧紧工具 
类别 A 
监控的 
X 
X 
带有控制功能的螺丝刀 
MNA1或 
长度变化或 
MI或 
扭矩图表或 
MP 
X 
X 
带有控制功能的电动螺丝刀 
X 
X 
带有控制功能的脉冲螺丝刀 
X 
X 
电子扭矩螺丝刀 
类别 B 
监控的 
X 
X 
参见类别 A 
MNA1 或 
长度变化或 
MI 或 
扭矩图表或 
MP 
部分监
控的 
— 
X 
带有控制功能的螺丝刀，但
是只有状态输出 
MNA1 或 MP 
— 
X 
带有控制功能的电动螺丝
刀，但是只有状态输出 
— 
X 
带有控制功能的电动脉冲螺
丝刀，但是只有状态输出 
— 
X 
电子扭矩扳手，只带有一个
传感器 
类别 C 
没有监
控 
— 
X 
弯头扳手 
MNA1 或 
MP 或 MFU 
— 
X 
电动螺丝刀 
— 
X 
空气螺丝刀 
— 
X 
脉冲螺丝刀 
X 拧紧工具满足这些最低要求。 
a）在过度弹性安装的情况下，监测 MNA1，MI 和 MP 值是不够的，需要进行螺杆曲线评估。 
b）扭矩公差±15％，旋转角度公差±15°。线路连接应与产品相关（特别适用于无线螺丝
刀）。 
c）在一定前提条件下使用 A 类和 B 类配件，工具没有监控的情况使用 C 类配件，进一步的
信息见 7.5 节。 
d）最低要求见 7.4 节。 
e）最低要求见 7.3 节。 
 
 
 
必须将工具设置为额定值（扭矩，旋转角度）（参见 A.1 节）； 脉冲扳手工具需要偏差。 
 
5.4 螺杆系统的采购和认可 


### 第 4 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
采购螺杆系统的前提是根据康采恩设计说明书“可控紧固系统”的最低要求。通常由规划和
质量保证根据康采恩设计说明书清单进行认可。如果您有任何疑问，负责人可以对该标准（见
附件 C）进行支持。 
 
5.5 参数的命名基于多级装配紧固过程 
紧固程序的复杂性通过三步安装和拧紧程序来说明，其中用图 1 中的参数进行了详细的说明。 
 
插图说明 
1 带有旋入式控制的预紧固阶段 
2 带有 AD 程序的预紧固阶段 
3 带有 AW 程序的最终紧固阶段 
 
M 扭矩 
W 旋转角度 
有关参数，请参阅表 2。 
 
图 1—多级装配紧固过程的例子—参数命名 
 
 
 
 
 
 
 
 
 
 
 


### 第 5 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
 
表 2—安装拧紧程序中可能的参数 
 
缩写 
调整
参数 
参数命名 
缩写 
调整
参数 参数命名 
MA 
X 
扭矩设定值 
P1 
  
末端压力设定值 
MS 
X 
扭矩设临界值 
WA 
X 
旋转角度设定值 
MI 
  
扭矩实际值 
WI 
  
旋转角度实际值 
M+ 
  
扭矩上公差 
W+ 
  
旋转角度上公差 
M— 
  
扭矩下公差 
W— 
  
旋转角度下公差 
MO 
  
切换的扭矩上限 
WO 
  
旋转角度上极限值（切换
的） 
MU 
  
切换的扭矩下限 
WEMO 
  
旋转角度旋入平均值上部 
MEMO 
  
扭矩旋入平均值上
部 
WEMU 
  
旋转角度旋入平均值下部 
MEMU 
  
扭矩旋入平均值下
部 
WEBO 
  
旋入范围角度上部 
ME+ 
  
旋入扭矩上公差 
WEBU 
  
旋入范围角度下部 
ME— 
  
旋入扭矩下公差 
WERO 
  
旋入后角上部 
N 
  
扭矩实际值—平均
值的脉冲数量 
WERU 
  
旋入后角下部 
n 
X 
转数设定值 
WGI 
  
旋转角度总实际值 
NA 
X 
脉冲数量设定值 
WG+ 
  
旋转角度总上公差 
NI 
  
脉冲数量实际值 
WG— 
  
旋转角度总下公差 
N+ 
  
脉冲数量上公差 
tA 
X 
时间设定值 
N— 
  
脉冲数量下公差 
tI 
  
时间实际值 
NGI 
  
脉冲数量总实际值 t+ 
  
时间上公差 
NG+ 
  
脉冲数量总上公差 t— 
  
时间下公差 
NG— 
  
脉冲数量总下公差 tGI 
  
时间总实际值 
P 
X 
压力设定值 
tG+ 
  
时间总上公差 
P0 
  
起始压力 
tG— 
  
时间总下公差 
 
5.6 主要使用的安装紧固程序的描述 
5.6.1 控制扭矩的安装紧固程序 
在 AD18—程序中紧固扭矩（扭矩设定值 MA）进行预先设定控制变量。 


### 第 6 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
 
插图说明 
1         拧紧级别 
M        扭矩 
W        旋转角度 
参数参见表 2 
图 2—控制扭矩的安装紧固程序（例如：AD18—程序） 
 
由于摩擦的分布，在该过程中即使小范围分布所施加的紧固扭矩（扭矩实际值 MI），通常也
大范围的设置夹紧力分布。 
作为预夹紧力分布的依据点，在监控的螺杆系统中能够考虑到所施加的紧固旋转角度（旋转
角度实际值 WI）。 
 
5.6.2 控制旋转角度的安装紧固程序 
5.6.2.1  AW11—程序（过弹性安装） 
旋转角度控制是过弹性安装的标准程序。图 3 描述了曲线变化和参数。 


### 第 7 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
 
插图说明 
1     拧紧步骤 
2     拧紧步骤 
M    扭矩 
W    旋转角 
图 3—AW11—程序（控制旋转角度的过弹性安装紧固程序） 
目标尺寸是指定的紧固旋转角度（旋转角度设定值 WA），其从特定预紧固扭矩（扭矩阈值
MS）计数。通过螺杆延伸极限内的预紧固和随后施加的转角组成了螺杆连接并且被紧固到
了屈服极限。同时相应的螺杆强度和螺纹摩擦决定了可实现的预紧力。下头部摩擦对预紧力
已不再有影响。在最终扭矩（扭矩实际值 MI）的大范围分布的同时设置一个小范围预夹紧
力分布。过程的控制最简单是通过扭矩—旋转角度曲线来记录。在扭矩—旋转角度—曲线的
上部超过屈服极限。这通过展平曲线标明。 
在使用手持工具的控制旋转角度安装紧固程序中应首先设置一个支架。如果没有设置支架，
需要由负责专业部门（规划，质量保证和生产）保证满足安装要求（扭矩，角度，弹性，超
弹性）。 
5.6.2.2 AW12—程序（安装不超过屈服极限） 
在应用的过程中不应该超过屈服极限，拧紧的过程是择优进行的，旋转的角度是可以控制的。
实例是锥形螺杆连接，不足的零件强度或不足的紧固能力。在 AW12—程序中设置成不达到
螺杆屈服强度。为了避免摩擦的影响设置一个小的预夹紧力分布，但是没有达到 AW11—程
序的分布。 
5.6.3 脉冲控制的安装紧固程序 
在 AD18—程序中可以使用脉冲式螺杆系统，在 AW—程序中也可以使用监控脉冲式螺丝刀。
在使用脉冲式螺杆系统时，目标规格在技术上遵守其他参数设置；因此在安装脉冲式螺杆系
统之前有必要与生产，设计和质量咨询。 


### 第 8 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
在给脉冲式工具标定尺寸时，应该考虑损耗，例如，通过延伸（扭力杆），供气管路中的压
力波动或螺旋接合侧的共振质量，可以导致制造商规定的最大扭矩的明显降低。 
在这种情况下建议使用脉冲控制的安装紧固程序，以预定的脉冲数量 NA 从预紧固扭矩 MS
开始确保超过屈服极限。在脉冲工具尺寸足够的情况下，在末端添加期间设置大致恒定的脉
冲高度，其对应于超弹性范围中的紧固扭矩。角度测量用作附加的控制变量，并且用于在过
度弹性范围内螺纹情况的显著区分。因此从预紧扭矩开始脉冲数的规格根据各自最软的拧紧
情况。 
5.6.4 用于监控拧紧情况的附加程序 
此外，拧紧系统提供了许多可能性，在个别情况也可以恰当的安全的进行拧紧。 
例如： 
— 带有夹紧件对螺母进行拧开扭矩监控， 
— 对刚难以活动的安装部件进行梯度监控， 
— 对具有非常长的螺纹以及自攻螺杆情况进行拧紧深度监控。 
 
5.7 特殊的螺杆连接参数 
表 3 列出了特殊的拧紧参数（例如：对于自攻拧紧，带有夹紧件的螺母等）： 
表 3 ——特殊螺杆连接参数 
 
螺杆拧紧参数 
缩写 
解释 
螺纹形式扭矩 
MF（Me） 在螺纹形式拧紧的情况下（VW 01127） 
拧开扭矩 
拧开扭矩 
MAU 
MAB 
没有预夹紧力的拧开扭矩（例如：带有夹紧件的
螺母或在化学安全装置） 
脱落扭矩 
MAR 
导致螺杆脱落的扭矩（在预夹紧力下） 
启动扭矩 
MLB 
（MLBA） 
（MLBL） 
在紧固方向或松开方向在第一次相对运动中测量
的扭矩。 
剥离扭矩 
MW 
螺丝刀的设置值。在紧固扭矩中，螺丝刀持续被
定义。 
超扭矩 
Mü 
导致螺纹破坏的扭矩（例如：在螺纹成型螺杆，
螺母中） 
 
6 拧紧工具的测试流程 
6.1 综述 
 
拧紧系统和拧紧工具安装的测试流程在 A.1 段落中描述。在负责的专业部门认可后才允许进
行安装。对此建立带有安装参数和控制参数规定的验收记录（参见 A.1 段落）。作为移动的
测量仪器安装一个测量箱子是普遍的，对于 MFU 可以设置一个试验台。 
 
6.2 拧紧工具的目标规范 
拧紧工具必须达到 Cmk≥1.67 的机器能力指数。这个结论基于瞬间拍摄，因此，对长期稳
定性没有任何结论。这特别适用于特殊的驱动，例如平板驱动和预装变速器，对此只允许
Cmk≥1.0 的机械能力指数。对此显示磨损增加，因此应经常检测，涂油以及保养。只记录
一个实际值是不足够的。 
另外自检型拧紧系统还具有冗余测量电路，相互比较其结果。当使用这样的系统时，可以以


### 第 9 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
更长的间隔完成循环的 IFH 或 MFU。 
决定拧紧过程安全的不仅仅是使用的拧紧工具，实际上而是在第 7 段中描述的过程安全方法。 
7 过程安全 
7.1 综述 
如果在足够长的时间之后，螺丝拧紧过程可被描述为安全过程，通过限制控制和测试参数，
可以可靠地检测过程波动，同时将误报率降至最低。 
每个螺杆连接由其总体参数及其制造影响决定。在批量生产的安装中，为了过程安全考虑根
据 VW 01110—1 的安装紧固程序，表 5“设计要求（最低功能要求）”，和安全文件（见第
7.9.2 节），包括过程参数。此外，工作说明书中的规格或更多文件是必要的（参见 A.3 节中
的拧入工位清单），工装和组件以及人员，零件处理，过程保护，线路连接和返工。 
 
7.2 驱动拧紧工具的最低要求 
— 工作人员必须接受培训和指导。 
— 使用的设备（拧紧工具）必须与指定的设备相关，装配足够的尺寸并且符合人体工程学
的用途。 
— 选择检查频率和工具切换，以便可以早期抵抗机器磨损相关的恶化。 
— 必须确保均匀和充足的能源供应。 
— 在首次使用螺杆连接件或不合格部分增加时对安装的连接元件进行检查。 
— 完成的螺杆连接定期，例如，通过 MNA 值或随机进行检查。 
— 应提供防止忘记螺杆连接的适当措施（例如，线打结，传送带停止，合格的计数，位置
检测，颜色标记）。 
该措施由相关部门一起决定（规划，生产和质量保证）。 
— 只有在不合格信号后才允许松开螺杆连接。 
— 必须为紧固系统的故障设置紧急策略。 
— 对于不合格情况，必须提供一个的精确返工解决方案，进行相应错误报告（有关详细信
息，请参见拧紧分析 3））。 
更多信息可在康采恩设计说明书“受控螺杆系统”中找到。 
 
7.3 监控零件的拧紧系统的进一步最低要求 
— 计数功能（当达到单个合格和总合格时，进行光学报告）； 
— 直接或间接测量或有效的控制变量（不是时间）； 
— 在已经合格的螺杆连接中重新检查出一个不合格的连接； 
— 通过更高级别的控制（例如：皮带控制）可以获得总的合格信号。 
 
7.4 监控紧固系统/技术的要求 
受监控的紧固系统使用传感器记录紧固期间的实际值并执行实际/设定值的对比。另外，必
须记录重要的控制参数如拧紧扭矩和角度，用于评估拧紧结果。 
 
7.5 手动装配的过程保护或紧急策略 
在工具未监控的螺杆连接中以及无需线打结（手动安装）的工具进行螺杆连接适用多监控原
则。如果技术上可行，在不同的工作场所执行此设计。必须清楚地标记测试工具。 
表 4 给出了小批量中单件紧固的程序和急停测量的步骤，其中没有产品相关的拧紧数据文档。 
紧急策略必须限制在尽可能短的安装时间内。为了确保关于维修的这一规范至少为系列工具
制定一个等效的替代或可以被接触。紧急战略的开始和结束的记录都与产品有关。 


### 第 10 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
导致相同结果的偏离本节的要求需要与负责的专业部门（规划，质量保证和生产）协商并且
必须记录在案。 
表 4 — 手工安装或急停策略过程安全中对于单个安装紧固程序的工作步骤（没有产品相关
的拧紧数据文档） 
序
号 
安装紧
固程序 
带有监
控的拧
紧工具 
带有化
学安全 
工作步骤 
工人 1 
工人 2 
1 
AD18 
否 
否 
根据 TE 规范拧紧到 MA 
根据 TE 规范重新再次
nachknicken 到 MA 
并且记录测试 
（例如：WPK，EDP 中的
印章，有色 
信号等） 
是 
否 
2 
AD18 
否 
是 
根据 TE 规范拧紧到 MA 
使用测量扭矩 MP 进行
nachknicken（参见段
落 7.8.2）并且记录测
试（例如在 WPK,EDV 中
的印章，颜色标记等） 
是 
是 
3 
AW11 和
AW12 
否 
是 
否 
 
1.根据 TE 规范拧紧到
MS 
并标记。 
2.根据 TE 规范制定额
定角度 
并且记录螺杆连接（例
如 WPK，EDP 中的印章） 
 
 
再次颜色标记和 
记录螺杆连接 
（例如 
WPK，EDP 中的印章和颜
色标记等。） 
 
4 
AW11 和
AW12 
是 
是 
否 
根据 TE 规范的规格 
紧固和记录 
螺杆连接（例如 
在 WPK，EDP 的印章，
颜色标记等。） 
nachknicken 测试扭矩
MP 和记录 
螺杆连接（例如 
WPK，EDP 的印章，颜色
标记等。） 
7.6 过程安全参数 
7.6.1 安装参数及其确定 
安装参数是由设计者确定的参数，位于 PDM-页，TAB 或已发布的图纸的目标规范。安装参
数的确定在 VW 01110-1 的 4.2 节“安装紧固程序的选择”中描述。 
7.6 控制参数及其确定 
控制参数用于监控拧紧情况，为了识别拧紧错误。 
控制参数也可以是： 
—螺杆旋入扭矩， 
— 螺杆旋入角度， 


### 第 11 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
— 比较扭矩实际值与扭矩设定值， 
— 比较旋转选择角度实际值与旋转角度设定值。 
这些控制参数只能通过监控的紧固系统来确定和评估。控制参数的范围限制（参见表 2）基
于 EC 螺丝拧紧系统 4）参数的规格）。 
 
7.6.3 测试参数 
7.6.3.1 综述 
测试参数（见表 5）适用于确定合格的执行螺杆连接并且不能直接使用图纸中指定的相关的
安装参数。 
通过拧紧，测试和残余扭矩，可以检测过程或产品的变化（趋势）。 
 
表 5—测试参数的解释 
测试参数 
缩写 
解释 
拧紧扭矩 1 
MNA1 
在首次应力之前，其他的指示参见 7.6.3.3 节 
下部范围极限 MNA1,min 
最小允许的设定值 
上部范围极限 MNA1,max 
最大允许的设定值 
拧紧扭矩 2 
MNA2 
在首次应力之后 
下部范围极限 MNA2，min 最小允许的设定值 
上部范围极限 MNA2，max 最大允许的设定值 
测试扭矩 
MP 
用于过程保障，参见 7.8.2 节 
剩余扭矩 
MR 
用于拆卸审核，参见 7.8.3 节 
 
7.6.3.2 测试频率 
测试的频率与规划，生产和质量保证进行确定，并且记录在测试计划中。 
使用抽样测试通常只可以发现系统错误。 
必须如此频繁地进行产品检查，以致由于系统错误而导致的故障制造产品不能带到客户面前。
如果系列过程受到其他措施的保护，则可以降低测试频率。 
注 2：产品测试可接受螺栓额定值。 
如果需要，在合理的距离内进行 MNA2 测量（另见第 7.8 节）。 
 
7.6.3.3 拧紧扭矩的测定 
MNA 值是螺钉（或螺母）继续转动时产生的最小扭矩，为了测量少数角度，参见图 4 和图
5。 
通常静摩擦效应导致峰值扭矩，即所谓的起步扭矩，但是，这可能不被视为紧固扭矩，参见
图 5。 
偏差与负责的专业部门协调（规划，质量保证和生产），必须记录在案。 


### 第 12 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
 
图 4—紧固扭矩的阐明 
 
图 5—阐明紧固扭矩时的静摩擦效应 
 
插图说明 
1   扭矩 
2   旋转角度（时间） 
3   紧固扭矩 
4   起步扭矩 
 
7.6.3.4 紧固扭矩的范围限制 
初步确定一段时间内的紧固扭矩范围限制，如果没有足够的实际值，可以使用 A.2 节中的说
明。 
随后，使用统计方法频率分布凭经验确定紧固扭矩的范围限制。 
 
在检查正态分布，随机性和异常值后，至少测量 50 次，优选图形确定频率分布的 2S 和 3S
散射极限之间的值范围，其中列出了所有测量的值（见图 6）。作为范围限制应仅为满刻度
值作为带有分配级别的交叉点下面和上面的最小值和最大值，就此而言，在技术上是合理的。
由于范围限制的狭窄定义是在偏差上实现的，因此敏感地做出反应并且检测到拧紧错误。 
 
然而，这也导致可能的偏差，即没有实际的拧紧误差，而是由于在一个方向上的影响因素。
在这些情况下可能需要不必要的返工，但同时无法识别的错误的风险减少了。使用替代统计
方法时（例如，中位数图）等于等效误差灵敏度或描述的平衡值。或者，可以永久使用 A.2
节中的范围限制。 
 
如果偏离指定范围，则必须同时进行原因分析 5）并且平行生产得到保障（见表 4）。 
 


### 第 13 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
根本原因分析的结果也可以是确定新的范围限制。未经负责设计的同意不可以修订安装说明。 
 
插图说明 
Fi              相对频率 
MNA1          紧固扭矩 1 
MNA1，min     下限范围 
MNA1，max     上限范围 
 
图 6——在考虑多个抽样时说明紧固扭矩的范围限制 
 
7.6.3.5 用于说明紧固扭矩的特殊性 
在过度弹性安装的情况下，范围限制不能仅从统计评估中得出，因为螺杆强度和摩擦系数也
作为其他参数确定需要实现的扭矩。范围限制还应基于 VW 01126-2，表 1“预载力/扭矩”
的 M / M +值。 
在个别情况下，除了以相同方式确定紧固扭矩 MI 并进行统计评估之外，可能还需要进行正
确的评估。 
使用扭力扳手装配时，由于不受控制的撕裂而导致分布偏斜。这导致大的分布，这不一定产
生缺点。 
使用化学螺纹保险（例如液体胶水或微胶囊粘合剂），粘合剂的固化在组装期间已经发生。 因
此粘接没有被破坏，在这些连接中不允许紧固扭矩，而指示检测扭矩（参见表 5 和 7.8.2）。 
 
7.6.3.6 紧固扭矩的可比性 
由于边界条件（使用的环境，拧紧和测试系统）因工厂而异，因此即使使用相同的螺钉，也
不可能使用在其他工厂中确定的测试参数。 
 
7.7  A 类和 B 类螺丝刀的调整工作 
对于设置和矫直工作（例如襟翼，门，盖），在最后一次设置之后，使用监控的螺丝拧紧技
术。只有经过专业部门规划，生产和质量保证协议才能产生偏差。 


### 第 14 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
7.8 设备范围的分解审核 
7.8.1 确定测试参数的一般信息 
拧紧扭矩 2（MNA2），测试扭矩和残余扭矩说明是在设备范围中在拆卸审核中推荐的方法。
提到的三种测量方法的结果不容易比较。检查始终在螺钉连接动态负载后进行。 
松动扭矩不适合评估螺钉连接的质量。 
 
7.8.2 确定测试扭矩 
在 AD18 程序中，测试扭矩为紧固扭矩的 80％，在 AW11 程序中为 VW01126-2 最小紧固扭
矩的 90％。偏离这一点需要技术开发的批准。对于 AW12 程序，测试扭矩确定在批量生产
并与技术开发和质量保证协商确认。 
在达到拧紧方向上的测试扭矩之前，不允许头部扭曲。 
测试扭矩用于测试例如其中的螺旋接头，使用了防止螺钉松动的化学措施。 
 
7.8.3 残余扭矩的确定 
残余扭矩 MR 的确定在图 7 中描述。剩余扭矩在特殊的情况中使用，当使用 2 MNA2 紧固扭矩
测量，通过高静摩擦力不可能实现可靠的测量。在设备拆卸审核中的扭矩评估尤其如此。 
 
 
图 7—残余扭矩 MR 的说明 
标记（1），从 10°松开到 30°（2），在此紧固到标记（3） 
 
7.9 螺杆数据记录 
7.9.1 螺杆数据记录的可能性 
可以使用各种形式进行记录，其中在安装期间或之后，在一个测量中记录是不同的。 
优选的是在安装期间记录螺杆数据，因为它可以在没有额外工作的情况下完成。 
 
7.9.1.1 在安装期间记录螺杆数据 
— 记录产品相关的实际值 
— 记录产品相关的状态（文档合格/不合格） 
优选应用扭矩和旋转角度实际值的记录。 
使用实际值记录的基础是螺杆驱动工具的过程可靠性（参见第 7 节和 A.1 节）。 
如果由于技术原因不能实现产品相关的拧紧数据的记录 
由于技术原因，不可能实现产品相关的螺杆数据的记录，那么必须进行制造相关的记录（例
如时间印章）。 
在这些封闭的设备概念中只允许制造合格的零件。该措施需要与规划，生产和质量协商确认。 
 
7.9.1.2 在安装后记录螺杆数据 
— 记录紧固扭矩 


### 第 15 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
— 记录螺杆长度（在特殊的情况下） 
— 记录测试扭矩 
— 记录循环工具测试 
在抽样试验中记录紧固扭矩（MNA1, MNA2），螺杆长度或试验扭矩（MP）。 
 
7.9.2 安全文档 
根据第 7.9.1 节的记录选项也可用于安全文档。必须记录状态（合格/不合格）和随机 MNA。
安全文件由相应的生产部门负责，其中记录过程安全性的所有数据，类似 KSU 的方式进行
存档。必须以这样的方式进行全存档，必须获得负责车间的各自质量安全的一致认可。 
 
7.9.3 记录实际值用于评估拧紧质量 
对于使用监控拧紧系统的拧紧，必须根据 KSU 将实际的拧紧值拧紧，因此： 
- 如果出现错误，可以实现追踪以及对受影响的拧紧进行限制， 
- 进行统计评估以确定控制和测试参数的极限值， 
- 通过评估实际值来减少测试周期（产品和过程） 
- 可以执行错误分析以确定错误原因。 
 
8 返修 
基本上必须实现尽可能少的返修（目标：零错误）。因此合格的返工不能只限制总的弯曲。 
以下位置可以进行返工： 
- 在生产阶段（首选形式，因为不需要拆除部件）； 
- 非生产阶段（返工工位）。 
一般来说，在执行拧紧的返工而不是多眼原则时适用，因为执行工作由经过专门培训的工人
执行。组件的安装是由执行工作人员亲自记录每次拧紧情况的，或者通过 WPK 中的印章或
EDP 中的条款。允许过程支持的附加颜色签名或自我控制。这也适用于合格组装的部件，在
返工过程中拆除并重新组装。 
 
必须确定并从长远角度纠正导致返工的原因。或者必须更换紧固件和错误安装的部件。在扭
矩控制方法或在选择角度控制过程中的紧固步骤如果发生故障，可以在机械化装配中使用自
动重复拧紧。因此，可以在没有任何显著的额外时间的情况下显著减少误差分量。 
 
允许专业人员内螺纹和外螺纹的手动螺纹室（带 1 档螺纹刀）（限制可以在相关的 PDM 表，
TAB 或图纸中找到。 
 
9. 职责 
对于预生产阶段和系列，在表 6 和表 7 中描述需求和负责人。如果以书面形式确定并确保其
实施，则允许不承担责任。 
 
表 6——在前期制作阶段的责任 
任务 
负责 
拧紧工具的采购根据设计说明书，VW 01110-1 和 VW 01110-2 
规划 
实施与产品相关的文档（例如：TLD 螺栓连接，类别 A 和 B） 
规划 
参数设置（根据 TAB,PDM 或图纸的规定） 
规划 


### 第 16 页
康采恩标准                                                           VW 01110-2 
版本 2015-03 
定义拧紧系统中的初步控制参数 
规划 
在拧紧系统中设置控制参数 
规划 
测量紧固扭矩（MNA1）并设置临时范围限制 
生产 
发布初步测试参数和测试方法 
质保 
 
表 7——生产阶段的责任 
任务 
负责 
使用统计方法测量和维护质量文件： 
- 实际价值 
- MNA1 值 
生产 
定义测试参数的范围限制 
生产 
发布测试参数的范围限制 
质量保证 
根据既定的工艺标准检查拧紧工具 
生产 
测量 MNA2 值（设备范围拆卸审核，整车检查） 
质量保证 
螺杆监控工位站： 
装配，测试和控制参数的随机控制 
质量保证 
优化测试和控制参数以及测试方法 
生产 
启动错误分析 
生产 
执行错误分析 
生产 
发布测试和控制参数和测试方法 
质量保证 
注释 3 VW 01110-1 第 5 节“责任”中描述了开发阶段的要求和责任。 
 
10 其他适用文件 
此标准引用的文档如下： 
VW 01110-1 
螺栓连接；设计和安装说明 
VW 01110-2 
连接技术；过弹性拧紧安装的紧固扭矩 
VW 01127 
板材螺杆连接；应用；核心孔直径的参考值 
VW 01129 
摩擦数限制值；带有公制 ISO 螺纹的机械紧固件 
VDA 第 6 卷 第 3 部分 汽车行业的质量管理 - 过程审核 - 产品开发过程，批量
生产，服务开发过程，提供服务 
VDI/VDE 2645 页 2 
螺纹技术机械能力研究 - 机械能力研究 - MFU 
VDI/VDE 2862 页 1 
汽车行业安装紧固系统和工具应用的最低要求 
 
 
附录 略 

