# VW_01110_2_EN_2015-03_螺栓连接_第2部分：安装和过程安全.pdf

## 文档信息
- 标题：
- 作者：
- 页数：35

## 文档内容
### 第 1 页
Group standard
VW 01110-2
Issue 2015-03
Class. No.:
61000
Descriptors:
threaded joint, assembly, process assurance, bolt, screw, joint, process, assurance, driving tool system,
driving tool, category, tightening speed
Threaded Connections
Part 2: Assembly and Process Assurance
Preface
At the time of this publication, the series of Volkswagen standards VW 01110 – under the general
title Threaded Connections – is made up of the following parts:
–
(Part 1:) Design and Assembly Specifications
–
Part 2: Assembly and Process Assurance
–
Part 4: Testing and Evaluation of Threaded Connections
At the time of this publication, the following parts of this series of standards are currently being pre‐
pared with the following working titles:
–
Part 3: Configuration of EC Driving Tool Systems
–
Part 5: Threaded Joint Type Analysis
Previous issues
VW 01110: 1975-10, 1998-12; VW 01110-2: 2008-07, 2012-07
Changes
The following changes have been made to VW 01110-2: 2012-07:
–
Section 2 "Definitions" expanded;
–
Section 3 "Symbols and abbreviations" expanded;
–
Table 1, category B: Electronic torque wrench with only one measurement sensor added;
–
Section 7.1 specified in concrete terms;
–
Section 7.2 "Minimum requirements for the operation of driving tools" concerning measures
against missing threaded connections specified in concrete terms;
–
Section 7.5 "Process assurance for manual assembly or backup strategy" concerning backup
strategy specified in concrete terms;
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Page 1 of 35
Technical responsibility
The Standards department
Threaded Connection Technology
working group, Harald Möbus; see
appendix C
EKDV/3 Tim Hofmann
EKDV
Tel.: +49 5361 9 27995
Maik Gummert
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Page 2
VW 01110-2: 2015-03
–
Previous section "Definition of the retightening torque" incorporated in section 2;
–
Figure 6: Previous figure 4 revised;
–
New section 7.7 "Adjustment work on threaded joint types of categories A and B" added;
–
Section 7.8: Whole vehicle tests removed;
–
Section 7.8.1 "General information on determining test parameters" concerning dynamic test‐
ing specified in concrete terms;
–
Section 7.9.1.1 "Threaded connection recording during assembly" expanded;
–
Section 8 "Rework" revised and expanded;
–
Section 9 "Responsibilities" revised;
–
Responsible parties in table C.1 updated;
–
Standard edited and structurally updated.
Contents
Page
Scope ......................................................................................................................... 3
Definitions .................................................................................................................. 3
Symbols and abbreviations ........................................................................................ 5
Description ................................................................................................................. 6
Assembly .................................................................................................................... 6
Influence of the tightening speed on producibility of the threaded connection ........... 6
Classification of threaded joint types .......................................................................... 6
Selection of possible driving tools .............................................................................. 6
Procurement and release of driving tool systems ...................................................... 8
Designation of parameters based on a multi-step assembly tightening
procedure ................................................................................................................... 8
Description of preferred assembly tightening procedures ........................................ 10
Specific characteristic values for threaded connections .......................................... 13
Test sequence of driving tools ................................................................................. 13
General information .................................................................................................. 13
Target specifications for driving tools ....................................................................... 13
Process assurance ................................................................................................... 14
General information .................................................................................................. 14
Minimum requirements for the operation of driving tools ......................................... 14
Additional minimum requirements for partially monitored driving tool systems ........ 15
Requirements for monitored driving tool systems/technology .................................. 15
Process assurance for manual assembly or backup strategy .................................. 15
Parameters for process assurance .......................................................................... 16
Adjustment work on threaded joint types of categories A and B .............................. 20
Disassembly audits in the powertrain area .............................................................. 20
Recording the fastening data ................................................................................... 21
Rework ..................................................................................................................... 22
Responsibilities ........................................................................................................ 23
Applicable documents .............................................................................................. 24
Detailed specifications concerning threaded connection processes ........................ 25
Process assurance for threaded connections .......................................................... 25
Range limits for retightening torques ....................................................................... 29
Threaded joint workstation checklist ........................................................................ 30
Process optimization checklist ................................................................................. 32
Procedure for increasing the assembly speed ......................................................... 32
1
2
3
4
5
5.1
5.2
5.3
5.4
5.5
5.6
5.7
6
6.1
6.2
7
7.1
7.2
7.3
7.4
7.5
7.6
7.7
7.8
7.9
8
9
10
Appendix A
A.1
A.2
A.3
A.4
A.5


### 第 3 页
Page 3
VW 01110-2: 2015-03
Evaluation procedure for category B driving tools .................................................... 33
Comparison of assembly tightening procedures and production specifications
as per VW 01110-2: 2008-07 ................................................................................... 34
List of responsible parties ........................................................................................ 35
A.6
Appendix B
Appendix C
Scope
This standard applies to the assembly of threaded connections with metric ISO threads and non-
metric threads in motor vehicles and assemblies. It also applies to process assurance with assem‐
bly tightening procedures standardized Group-wide. Retroactive application to existing sys‐
tems/production equipment/sequence processes must be agreed upon with the appropriate depart‐
ments (Planning, Quality Assurance) for ongoing vehicle projects, including major model upgrades
(GPs).
NOTE 1: Requirements concerning the design, layout, and implementation of threaded connec‐
tions are defined in Volkswagen standard VW 01110-1.
Definitions
The following definitions apply to the application of this standard.
Backup strategy
Finishing of a threaded connection in line with specifications, in the event that the production tight‐
ening process is interrupted or breaks down, taking into account the process assurance measures
Driving tool
Production equipment for the assembly of threaded connections
Driving tool system
Driving tool with control system and the connections necessary for the power supply and data
transfer
Establishing the capability (IFB, German abbreviation)
The driving tool is set for the special threaded joint type, taking into account preconditions and
product influences.
Gradient monitoring
During tightening, the differential quotient (gradient) of the torque/angle-of-rotation curve (ΔMA/Δϑ)
is formed from the measured values that the driving tool supplies. The gradient can be used for
control and/or monitoring.
Line linking (line interconnection)
Linking (e.g., for part entry and exit) between product data and system data, which is used to proc‐
ess an order (e.g., process specification) on a product-specific basis in a defined production seg‐
ment and to output a status message
Line stop
A stop of the assembly line to allow assembly work to be carried out after the cycle time has
elapsed in the production segment
1  
2  


### 第 4 页
Page 4
VW 01110-2: 2015-03
Machine capability test (MCT)
The machine capability test is a qualitative evaluation of the stability and reproducibility of meas‐
urement results that are delivered by an assembly tool and evaluated on the basis of statistical
methods. It is performed using a suitable testing tool with reference to the short-term dispersion,
excluding product-specific process influences, i.e., under consistent threaded joint type conditions.
NOTE: See also VDI/VDE 2645 sheet 2.
Maintaining the capability (IFH, German abbreviation)
The reproducibility accuracy of the driving tools is checked at defined intervals.
Manual assembly
Threaded connection with non-monitored tool and a tool without line linking
Measurement kit
Mobile analysis device with traceably calibrated equipment for recording and plotting measured
values (e.g., torque and angle of rotation)
Monitoring parameters
Parameter resulting from the target/actual comparison of the driving tool process, which enables
an evaluation of the threaded connection result.
Range limit of retightening torques
Upper and lower limit which defines the range of retightening torques evaluated as OK
Reliable process
Process in which the factors influencing the process are known and the required quality features
are adhered to
Retightening torque1)
Lowest torque measured when the bolt (or nut) is turned further by a few angular degrees
Retightening torque 1 (MNA1)
Retightening torque determined within 30 min after assembly
Retightening torque 2 (MNA2)
Retightening torque determined after dynamic or thermal loading (setting) of the threaded connec‐
tion has taken place
Rework
In a process deviating from the production process and in line with specifications, the finishing of a
threaded connection that has been evaluated as NOK, carried out by a specially trained additional
worker using a defined tool    NOTE: Carrying out a repeat threaded connection with a production
tool during the assembly cycle is not considered rework.
Systematic error
Unidirectional deviation with causes that can be identified by their principle
1)
Retightening torques (MNA values) are also referred to as residual torques.


### 第 5 页
Page 5
VW 01110-2: 2015-03
Test bench
Traceably calibrated equipment for checking driving tools
Test parameter
Value that allows a conclusion to be made on threaded connection quality once it is finished
Transducer
Measurement recorder and sensor used between the driving tool spindle and the socket
Yield point
Material characteristic value which refers to the stress up to which a material has no measurable
plastic deformation under uniaxial and torsion-free tensile load
Symbols and abbreviations
Torque-controlled assembly tightening procedure
Angle-controlled assembly tightening procedure
Carry-over part
Classification System for Documents
Electronically commutated [motor] (brushless DC motor)
Electronic data processing
Failure modes and effects analysis
Establishing capability
Maintaining capability
Machine capability test
Rework
Not OK (not passed)
Test passed
Process capability test
Product description manual
Quality information system
Table
Design Engineering
Technical Guideline for Documentation
Transfer ticket
3  
AD procedure
AW procedure
COP
CSD
EC
EDP
FMEA
IFB
IFH
MCT
NA
NOK
OK
PCT
PDM
QIS
TAB
TE
TLD
WPK


### 第 6 页
Page 6
VW 01110-2: 2015-03
Description
Description for threaded connections, e.g., in drawings:
Assembly and process assurance as per VW 01110-2
Assembly
Influence of the tightening speed on producibility of the threaded connection
The joining of threaded connections requires time for adjusting components, surfaces, seals, corro‐
sion protection waxes, etc., to ensure a reliable assembly process and to prevent greater setting
losses. The assembly speeds have therefore a direct impact on the amount of the assembly and
residual preload forces. As per VW 01129, release tests for new surface protection types are car‐
ried out at 200 rpm during the screw-in step and 20 rpm during the final tightening step. These
speeds have also proven effective at eliminating stick-slip effects. Higher tightening speeds are
possible during production. For threaded connections of categories A and B, the tightening speed
influence must be determined and evaluated by an appropriate department based on statistical
methods. Section A.5 describes the procedure.
If higher tightening speeds verified by tests during production (product and process) have proven
to be successful with COP threaded joint types2) on the predecessor model, it is permissible to
adopt these tightening speeds.
Classification of threaded joint types
As per VDI/VDE 2862 sheet 1, threaded joint types must be classified into categories A, B, or C
(see VW 01110-1, section 4.3 "Categorization of threaded joint types by design"). The driving tools
used must correspond to the requirements in table 1.
Monitored driving tool systems must always be used for threaded joint types of categories A and B.
For category B, partially monitored driving tool systems may also be used if the verification has
been carried out and documented according to the evaluation procedure as stated in section A.6
for driving tool selection (for required functionalities for partially monitored driving tool systems, see
section 7.3). This evaluation procedure must be carried out in cooperation with the appropriate de‐
partments (Planning, Quality Assurance, and Production). The documented result is part of the
process release for the partially monitored driving tool system.
The specifications in section 7.5 must be followed for process assurance purposes in all cases in
which, following coordination between the appropriate departments (Planning, Quality Assurance,
and Production), the required driving tool systems are not used for categories A and B.
Selection of possible driving tools
The selection of the driving tool to be used in production can be determined by the appropriate
Planning department in line with the requirements in table 1. The selection of the driving tools can
enable a lower preload force dispersion to be achieved (for further information, see Group Perform‐
ance Specification "Controlled Driving Tool Systems").
4  
5  
5.1  
5.2  
5.3  
2)
In the case of COP threaded joint types, all parts to be connected and PDM specifications are identical to the predecessor model.


### 第 7 页
Page 7
VW 01110-2: 2015-03
Table 1 – Requirement for driving tool systems in production and possible types of doc‐
umentation
Category as per
VDI/
VDE 2862 sheet 1
Property
Minimum requirement
Technically possible
type of documentationa)
Actual
value
output
Status
output
Driving toolb)
Cat. Ac)
Monitoredd)
X
X
Driving tool with control
MNA1or
length change or
MI or
torque curve or
MP
X
X
Cordless driving tool with
control
X
X
Pulse driving tool with
control
X
X
Electronic torque wrench
Cat. Bc)
Monitoredd)
X
X
see cat. A
MNA1 or
length change or
MI or
torque curve or
MP
Partially
monitorede)
—
X
Driving tool with control,
but only status output
MNA1 or
MP
—
X
Cordless driving tool with
control, but only status
output
—
X
Cordless pulse driving
tool with control, but only
status output
—
X
Electronic torque wrench
with only one measure‐
ment sensor
Cat. C
Not moni‐
tored
—
—
Click-type torque wrench
MNA1 or
MP or
MFU
—
—
Cordless driving tool
—
—
Pneumatic driving tool
—
—
Pulse driving tool
X     Driving tool meets this minimum requirement.
a)
Monitoring of MNA1, MI, and MP values is not sufficient for assembly beyond the elastic limit; tightening curve evaluation is required.
b)
Torque tolerance of ±15% and rotation angle tolerance of ±15°. Line linking must be planned for on a product-specific basis (applies
particularly in the case of cordless driving tools).
c)
Can be used for threaded connections of categories A and B, with certain prerequisites, with non-monitored tools from the catego‐
ry C row. For further information, see section 7.5.
d)
For the minimum requirement, see section 7.4.
e)
For the minimum requirement, see section 7.3.
The tools must be set to the nominal value (torque, angle of rotation) (see section A.1); deviations
are necessary for non-monitored pulse driving tools.


### 第 8 页
Page 8
VW 01110-2: 2015-03
Procurement and release of driving tool systems
Procurement of the driving tool systems presupposes minimum requirements as per Group Per‐
formance Specification "Controlled Driving Tool Systems." The release is issued jointly by the
Planning and Quality Assurance departments on the basis of the Group Performance Specification
checklist. For any questions, the responsible parties for this standard (see appendix C) can provide
support.
Designation of parameters based on a multi-step assembly tightening procedure
The complexity of a driving tool program is shown with a three-step assembly tightening procedure
as an example, along with an indication of the parameters, in figure 1.
Legend
1
Pre-tightening step with screw-in check
2
Pre-tightening step with AD procedure
3
Final tightening step with AW procedure
  
M
Tightening torque
W
Angle of rotation
Parame‐
ters
See table 2.
Figure 1 – Example for multi-step assembly tightening procedure – Designation of the parameters
5.4  
5.5  


### 第 9 页
Page 9
VW 01110-2: 2015-03
Table 2 – Possible parameters for assembly tightening procedures
Symbol
Control
variable
Parameter designation
Symbol
Control
variable
Parameter designation
MA
X
Tightening torque, target value
p1
 
Final pressure, target value
MS
X
Tightening torque, threshold
WA
X
Angle of rotation, target value
MI
 
Tightening torque, actual value
WI
 
Angle of rotation, actual value
M+
 
Tightening torque, upper tolerance
W+
 
Angle of rotation, upper tolerance
M-
 
Tightening torque, lower tolerance
W-
 
Angle of rotation, lower tolerance
MO
 
Tightening torque upper limit, switching
WO
 
Angle of rotation, upper limit , switch‐
ing
MU
 
Tightening torque lower limit, switching
WEMO
 
Angle of rotation, screw-in average,
top
MEMO
 
Tightening torque screw-in average, top
WEMU
 
Angle of rotation, screw-in average,
bottom
MEMU
 
Tightening torque screw-in average, bot‐
tom
WEBO
 
Screw-in area angle, top
ME+
 
Screw-in torque, upper tolerance
WEBU
 
Screw-in area angle, bottom
ME−
 
Screw-in torque, lower tolerance
WERO
 
Screw-in retrospect angle, top
N
 
Number of actual value-average torque
pulses
WERU
 
Screw-in retrospect angle, bottom
n
X
Speed, target value
WGI
 
Angle of rotation, total, actual value
NA
X
Number of pulses, target value
WG+
 
Angle of rotation, total, upper toler‐
ance
NI
 
Number of pulses, actual value
WG−
 
Angle of rotation, total, lower toler‐
ance
N+
 
Number of pulses, upper tolerance
tA
X
Time, target value
N–
 
Number of pulses, lower tolerance
tI
 
Time, actual value
NGI
 
Number of pulses, total, actual value
t+
 
Time, upper tolerance
NG+
 
Number of pulses, total, upper tolerance
t−
 
Time, lower tolerance
NG-
 
Number of pulses, total, lower tolerance
tGI
 
Time, total, actual value
p
X
Pressure, target value
tG+
 
Time, total, upper tolerance
p0
 
Initial pressure, target value
tG-
 
Time, total, lower tolerance


### 第 10 页
Page 10
VW 01110-2: 2015-03
Description of preferred assembly tightening procedures
Torque-controlled assembly tightening procedure
In the AD18 procedure, the tightening torque (torque target value MA) is the specified control varia‐
ble. If possible, the angle of rotation must be monitored.
Figure 2 shows the curve progression and the parameters graphically.
Legend
1
Screwing step
 
M
Tightening torque
W
Angle of rotation
Parame‐
ters
See table 2.
Figure 2 – Torque-controlled assembly tightening procedure (e.g., AD18 procedure)
Due to the friction dispersion, this procedure usually has a large preload force dispersion, even
with a small dispersion of the applied tightening torque (torque actual value MI).
The applied tightening angle of rotation (angle of rotation actual value WI) can be used as a refer‐
ence point for the preload force dispersion with monitored driving tool systems.
Angle-controlled assembly tightening procedure
AW11 procedure (assembly beyond the elastic limit)
The angle-of-rotation control is a standard procedure for assembly beyond the elastic limit.
Figure 3 shows the curve progression and the parameters graphically.
5.6  
5.6.1  
5.6.2  
5.6.2.1  


### 第 11 页
Page 11
VW 01110-2: 2015-03
Legend
1
Screwing step
2
Screwing step
  
M
Tightening torque
W
Angle of rotation
Parame‐
ters
See table 2.
Figure 3 – AW11 procedure (angle-controlled assembly tightening procedure beyond the elastic
limit)
The target variable is a specified tightening angle of rotation (angle of rotation target value WA) that
is counted starting at a particular initial torque (torque threshold MS). The threaded connection is
tightened to the yield point in a defined manner through a pre-tightening method below the yield
point for the bolt and a subsequent application of the angle of rotation. The respective screw/bolt
strength and the thread friction determine the achievable preload force. The influence of the screw
head friction on the preload force no longer exists. There is a small preload force dispersion with a
larger dispersion of the resulting final torque (torque actual value MI). The procedure is most easily
checked by recording of the torque/angle-of-rotation curve. The yield point is exceeded in the up‐
per area of the torque/angle-of-rotation curve. This is characterized by a curve that becomes flat‐
ter.
Preferably, a support is used for angle-controlled assembly tightening methods using hand-held
tools. If no support is used, the appropriate departments (Planning, Quality Assurance, and Pro‐
duction) must make sure that the assembly specifications are achieved (torques, angles, elastic,
beyond the elastic limit).
AW12 procedure (assembly below the yield point of the screw)
In certain applications where the bolt yield point must not or cannot be exceeded, it is nevertheless
preferable to tighten using an angle-controlled procedure. Examples of this include taper-threaded
connections, insufficient component strength, or insufficient driving tool capacity. In these cases,
the AW12 procedure is suitable. It must be used in a way that the bolt yield point is not reached. A
reduction in the friction influences results in a smaller preload force dispersion; however, the dis‐
persion of the AW11 procedure is not reached.
5.6.2.2  


### 第 12 页
Page 12
VW 01110-2: 2015-03
Pulse-controlled assembly tightening procedure
Pulse driving tool systems can be used with the AD18 procedure. Monitored pulse driving tools can
also be used with the AW procedure. The target specifications for the use of pulse driving tool sys‐
tems are met with different parameter settings for technical reasons. Because of this, an agree‐
ment between the Production department and the appropriate Design Engineering and Quality As‐
surance departments is necessary before the driving tool is used.
When dimensioning pulse tools, it must be noted that losses, e.g., due to extensions (torsion bar),
pressure fluctuations in the air supply lines, or associated oscillating masses on the side of the
threaded connection, can lead to a significant reduction in the maximum torque specified by the
manufacturer. In the case of soft joints and long clamping lengths, the angle-controlled assembly
tightening procedure can take a very long time. This is because the tightening speed will be signifi‐
cantly reduced again after reaching the yield point.
In these cases, it is recommended to use the pulse-controlled assembly tightening procedure. In
this procedure, the yield point is reliably exceeded with a specified number of pulses NA starting
from the initial torque Ms. If dimensioning for the pulse driving tool is adequate, a more or less con‐
stant pulse level will result during final joining. This level will equal the tightening torque in the
range beyond the elastic limit. The angular measurement is used here as an additional monitoring
parameter and for a meaningful differentiation between the threaded joint types in the range be‐
yond the elastic limit. In these cases, the number of pulses starting from the initial torque will be
specified for the softest threaded joint type.
Additional threaded joint type monitoring methods
In addition, the driving tool systems offer many options for tightening in a proper and process-se‐
cure manner on a case-by-case basis.
Examples include:
–
Screw-on running torque monitoring of prevailing torque type nuts
–
Gradient monitoring for stiff assembly parts
–
Screw-in depth monitoring for very long threads and thread-forming tapping screws
5.6.3  
5.6.4  


### 第 13 页
Page 13
VW 01110-2: 2015-03
Specific characteristic values for threaded connections
Table 3 shows specific characteristic values for threaded connections (e.g. for thread-forming tap‐
ping screws, prevailing torque type nuts, etc.):
Table 3 – Explanation of the specific threaded connection characteristic values
Threaded connection char‐
acteristic value
Symbol
Explanation
Tapping torque
MF (Me)
for self-tapping screws (VW 01127)
Screw-on tightening torque
Screw-off loosening torque
MAU
MAB
Torque for fastening and unfastening (e.g., for prevailing torque type nuts or
for chemical locking agents)
Tear-off torque
MAR
Torque that causes the screw/bolt to tear off (under preload force)
Breakaway torque
MLB
MLBA
Torque that is measured at the first relative movement in the tightening direc‐
tion (MLBA)
or loosening direction (MLBL)
MLBL
Shut-off torque
MW
Set value for a driving tool. Tightening torque at which the driving tool stops
in a defined manner.
Over-tightening torque
Mü
Torque that will cause the destruction of the threads (e.g. with self-tapping
screws, nuts).
Test sequence of driving tools
General information
Section A.1 describes the test sequence for the use of driving tool systems and tools. This use is
only permissible following release by the appropriate department. To that end, an acceptance pro‐
tocol with specifications for assembly and control parameters must be created (see Section A.1).
The use of a measurement kit as a mobile measuring instrument is customary. For the MCT, a test
bench can be used.
Target specifications for driving tools
A driving tool must achieve a machine capability index of Cmk ≥ 1.67. This statement is only based
on a recording at a single point in time and therefore cannot be used to draw any conclusions as to
the long-term stability. This applies in particular to special drives such as geared offset heads and
gear attachments for which a machine capability index of only Cmk ≥ 1.0 is possible and permissi‐
ble. In addition, they have increased wear and must therefore be checked, greased, and serviced
more often. Documentation of only the actual values is insufficient.
In addition, inherently secure driving tool systems have redundant measuring circuits whose results
are compared with one another. When using systems like this, the periodic IFH or MCT can be car‐
ried out at longer intervals.
Decisive for reliable tightening processes are not only the driving tools used, but primarily the
methods for process assurance described in section 7.
5.7  
6  
6.1  
6.2  


### 第 14 页
Page 14
VW 01110-2: 2015-03
Process assurance
General information
A tightening process can be considered a reliable process if, after a sufficient run-in phase, pro‐
cessing fluctuations can be reliably identified by narrowing the control and test parameters and, at
the same time, the false alarm rate has been minimized.
Each threaded connection is characterized by the totality of its parameters and its influences on
manufacturing. For production assembly processes, the assembly tightening procedures as per
VW 01110-1, table 5 "Requirements for design (minimum functional requirements)," and the safety
documentation (see section 7.9.2), including the process parameters, must be taken into account
for purposes of process assurance. In addition, specifications are necessary in the form of work
instructions or other documents (see also "Threaded connection workstation checklist" in
section A.3), which pertain to production equipment and components as well as personnel, parts
handling, process assurance, line linking, and rework.
Minimum requirements for the operation of driving tools
–
Personnel must be trained and instructed.
–
The production equipment used (driving tools) must be adequately dimensioned and ergonom‐
ically usable in relation to the specified assembly tightening procedure.
–
The checking schedule and the tool changes must be selected such that any worsening in the
machine capacity due to wear can be counteracted early.
–
A uniform and sufficient supply of power must be ensured.
–
The fasteners that are used must be checked at first use or if there is any increase in the num‐
ber of threaded connections that are not OK.
–
Completed threaded connections must be checked by means of random sampling at intervals,
e.g., via MNA values.
–
Suitable measures against missing threaded connections must be planned for (e.g., line link‐
ing, line stop, OK counting, position detection, color marking). The pertinent departments
(Planning, Production, and Quality Assurance) will decide jointly on implementation of the
measure.
–
Unscrewing a threaded connection is only permissible after an NOK signal.
–
A backup strategy must be defined for the event of a failure of the driving tool system.
–
In the case of NOK results, a precise rework solution must be in place, which takes into ac‐
count the error message accordingly (for further details, see the threaded joint type analy‐
sis 3)).
For further information, see Group Performance Specification "Controlled Driving Tool Systems."
7  
7.1  
7.2  
3)
Standard VW 01110-5 for the threaded joint type analysis is currently being prepared.


### 第 15 页
Page 15
VW 01110-2: 2015-03
Additional minimum requirements for partially monitored driving tool systems
–
Keeping-count function (visual indication when an individual OK result and an overall OK result
is achieved);
–
A directly or indirectly measured or effective control variable (not time);
–
NOK indication when a new tightening attempt is performed on an already-OK threaded con‐
nection;
–
Overall OK signal can be picked up by a higher-level control (e.g., line control).
Requirements for monitored driving tool systems/technology
Monitored driving tool systems record the actual values by means of sensors during the tightening
procedure and carry out a target/actual value comparison. In addition, essential control parameters
such as the screw-in torque and angle of rotation must be recorded and referred to for the evalua‐
tion of the threaded connection results.
Process assurance for manual assembly or backup strategy
For threaded connections with non-monitored tools and tools without line linking (manual assem‐
bly), the principle of multiple evaluators applies. If technically possible, these checks must be per‐
formed at separate workstations. Test tools must be clearly marked.
Table 4 provides the work items for the individual assembly tightening procedures for limited pro‐
duction and for a backup strategy, during which no product-specific threaded connection data doc‐
umentation is carried out.
The backup strategy must be limited to the shortest possible operating time. To ensure compliance
with this specification, Maintenance must keep at least one equivalent replacement for the produc‐
tion tool at hand or available. The start and end of the backup strategy must be documented on a
product-specific basis.
Deviations from the specifications in this section that lead to the same result are permissible follow‐
ing approval by the appropriate departments (Planning, Quality Assurance, and Production), and
must be documented.
7.3  
7.4  
7.5  


### 第 16 页
Page 16
VW 01110-2: 2015-03
Table 4 – Work items for the individual assembly tightening procedures in process as‐
surance for manual assembly methods or backup strategy (without product-specific
threaded connection data documentation)
No.
Assembly
tightening
procedure
With
monitored driving tool
With chemical
locking agent
Work item
 
 
 
 
Worker 1
Worker 2
1
AD18
 
 
Tighten to MA as per TE spe‐
cifications
Retorque with MA as per TE
specifications and document
the test (e.g., stamp in the
WPK, EDP, color marking,
etc.)
No
No
 
 
Yes
No
2
AD18
 
 
Tighten to MA as per TE spe‐
cifications
Retorque with test torque MP
(see section 7.8.2) and docu‐
ment the test (e.g., stamp in
the WPK, EDP, color marking,
etc.)
No
Yes
 
 
Yes
Yes
3
AW11 and
AW12
No
Yes
No
1. Tighten to Ms as per TE
specifications and mark.
2. Apply the nominal angle as
per TE specifications and
document the threaded con‐
nection (e.g., stamp in the
WPK, EDP, etc.)
Mark with new color marking
and document the threaded
connection (e.g., stamp in the
WPK, EDP, color marking,
etc.)
 
 
4
AW11 and
AW12
Yes
Yes
No
Tighten as per TE specifica‐
tions and document the
threaded connection (e.g.,
stamp in the WPK, EDP, col‐
or marking, etc.)
Retorque with test torque MP
and document the threaded
connection (e.g., stamp in the
WPK, EDP, color marking,
etc.)
Parameters for process assurance
Assembly parameters and their specification
Assembly parameters are the target specifications determined by the design engineer and publish‐
ed in the PDM sheet, TAB, or drawing. The specification of assembly parameters is described in
VW 01110-1, section 4.2 "Selection of the assembly tightening procedure."
Control parameters and their specification
Control parameters are used for threaded joint type monitoring in order to detect threaded connec‐
tion errors.
7.6  
7.6.1  
7.6.2  


### 第 17 页
Page 17
VW 01110-2: 2015-03
Control parameters may include, among others:
–
Screw-in torques,
–
Screw-in angle of rotation,
–
Comparison of torque actual value to torque target value,
–
Comparison of angle-of-rotation actual value to angle-of-rotation target value.
These control parameters can only be determined and evaluated with monitored driving tool sys‐
tems. The range limits of the control parameters (see table 2) are determined as per the configura‐
tion specifications of EC driving tool systems4).
Test parameter
General information
The test parameters (see table 5) are determined on threaded connections that have been comple‐
ted with OK results, and must not be referenced directly with the assembly parameters indicated in
the drawing.
With retightening, test, and residual torques, changes (trends) in the process or product can be de‐
tected.
Table 5 – Explanation of the test parameters
Test parameter
Symbol
Explanation
Retightening torque 1
MNA1
Before the first load; for additional information, see section 7.6.3.3.
Lower range limit
MNA1, min
Minimum permissible target value
Upper range limit
MNA1, max
Maximum permissible target value
Retightening torque 2
MNA2
After the first load
Lower range limit
MNA2, min
Minimum permissible target value
Upper range limit
MNA2, max
Maximum permissible target value
Test torque
MP
For process assurance; see section 7.8.2
Remaining torque
MR
For disassembly audits; see section 7.8.3
Test frequency
The frequency of testing must be defined in agreement between the Planning, Production, and
Quality Assurance departments and documented in a test plan.
Usually, only systematic errors can be discovered with random-sample testing.
The product tests must be performed so often that faultily finished products that can be traced back
to a systematic error do not reach the customer. The testing frequency may be reduced if the pro‐
duction process has been validated by other measures.
NOTE 2: Tightening curve evaluations are permissible as product testing.
If necessary, the MNA2 measurements are performed at a reasonably explained interval (see also
section 7.8).
7.6.3  
7.6.3.1  
7.6.3.2  
4)
Standard VW 01110-3 is currently being prepared.


### 第 18 页
Page 18
VW 01110-2: 2015-03
Determining the retightening torque
The MNA value is the lowest torque measured when the bolt (or nut) is turned further by a few angu‐
lar degrees; see figure 4 and figure 5.
In many cases, static friction effects often lead to a peak torque, known as the breakaway torque.
However, this must not be interpreted as a retightening torque; see figure 5.
Deviations are permissible in agreement with the appropriate departments (Planning, Quality As‐
surance, and Production) and must be documented.
Figure 4 – Determining the retightening tor‐
que
Figure 5 – Static friction effects when deter‐
mining the retightening torque
Legend
1
Tightening torque
2
Angle of rotation (time)
3
Retightening torque
4
Breakaway torque
Range limits of retightening torques
A preliminary definition of the range limits of retightening torques for a period without sufficient ac‐
tual values can be made with the information from section A.2.
Following this, the range limits of retightening torques are determined empirically with the aid of the
statistical method for frequency distribution.
After testing for the normal distribution, contingencies, and outliers from at least 50 measured val‐
ues, preferably graphically, the range of values between the 2-sigma and 3-sigma scattering limits
of the frequency distributions must be determined, within which all measured values lie (see
figure 6). As the range limits, only full scale values must be selected as the minimum and maxi‐
mum value below and above the intersection points with the straight distribution lines, provided this
is technically possible. By narrowly defining the range limits, deviations can be responded to with
appropriate sensitivity, and threaded connection errors can be detected.
However, this can also yield potential deviations, to which no actual threaded connection error can
be assigned; rather, these deviations are a result of the influencing variables acting in one direc‐
tion. These cases can result in unnecessary rework; however, the risk of undetected errors is re‐
duced at the same time. When using alternative statistical procedures (e.g., median chart), empha‐
sis must be placed on equivalent error sensitivity or the described balance. As an alternative, the
range limits from section A.2 can be used permanently.
7.6.3.3  
7.6.3.4  


### 第 19 页
Page 19
VW 01110-2: 2015-03
In the event of a deviation from the defined range, a causal analysis5) must be carried out and pro‐
duction must be validated in parallel (see table 4).
The causal analysis may also result in defining new range limits. Changes to the assembly specifi‐
cations are not permitted without the approval of the appropriate Design Engineering department.
Legend
Fi
Relative cumulative frequency
MNA1
Retightening torque 1
MNA1, min
Lower range limit
MNA1, max
Upper range limit
Figure 6 – Determining the range limits of retightening torques, taking into account multiple random
samples
Details on determining the retightening torque
For assembly beyond the elastic limit, the range limits cannot be derived solely from statistical
evaluations, because the bolt strength and the coefficient of friction, as additional parameters, also
influence the torque achieved. The range limits must also be guided by the M−/M+ values from
VW 01126-2, table 1 "Preload force/torque."
In individual cases, it might be necessary to additionally determine and statistically evaluate the
tightening torques MI in the same manner for a correct evaluation.
*******  
5)
Standard VW 01110-5 for the threaded joint type analysis is currently being prepared.


### 第 20 页
Page 20
VW 01110-2: 2015-03
For assembly carried out with click-type torque wrenches, there is an uneven distribution due to
uncontrolled excessive tightening. This leads to extensive dispersions; however, this is not
necessarily a disadvantage.
When chemical locking agents are used (e.g. liquid adhesive or microencapsulated adhesive), the
adhesive cures already in the assembly phase. In order not to destroy the adhesive bond, retight‐
ening torques must not be determined for these connections. Instead, only test torques must be
determined (see table 5 and section 7.8.2).
Comparability of retightening torques
Because the boundary conditions (environment, threaded connection and testing system used,
etc.) will vary from plant to plant, it is not possible to utilize the testing parameters determined in
one plant in another plant, even for the same types of threaded connections.
Adjustment work on threaded joint types of categories A and B
For adjustment and alignment work (e.g., flaps, doors, lids), the monitored driving tool technique
after the most recent adjustment must be used. Deviations are only permissible following agree‐
ment with the Planning, Production, and Quality Assurance departments.
Disassembly audits in the powertrain area
General information on determining test parameters
Retightening torque 2 (MNA2), test torque, and residual torque determinations are recommended
methods for disassembly audits in the powertrain area. However, the results of the three designa‐
ted measuring methods are not easily compared. The test is always performed after dynamic load‐
ing on the threaded connection.
Loosening torques are not suitable for evaluating the quality of the threaded connection.
Determining the test torque
For the AD18 procedure, the test torque is 80% of the tightening torque; for the AW11 procedure,
90% of the minimum tightening torques from VW 01126-2. Deviations require approval by Design
Engineering. For the AW12 procedure, the test torques must be defined in series production and
agreed upon with Design Engineering and Quality Assurance.
Torsion of the head is not permitted up to the point the test torque is reached in the direction of
tightening.
Test torques are used, for example, for the testing of threaded connections that use chemical
methods to prevent the screws/bolt from loosening.
Determining the remaining torque
Figure 7 describes the determination of the residual torque MR. The residual torque is used in spe‐
cial cases when no reliable measurement is possible with the measurement of retightening torque
2 MNA2 due to high static friction. This applies, in particular, to evaluations of torques with the disas‐
sembly audits of powertrains.
7.6.3.6  
7.7  
7.8  
7.8.1  
7.8.2  
7.8.3  


### 第 21 页
Page 21
VW 01110-2: 2015-03
Figure 7 – Determination of the residual torque MR:
Mark (1), loosen 10° to 30° (2), tighten again to marking (3)
Recording the fastening data
Options for recording the fastening data
The recording can be conducted in different ways. A distinction is made between measurement
during the assembly and measurement after the assembly.
Threaded connection recording is preferred during assembly, because it can be carried out without
additional work.
Threaded connection recording during assembly
–
Recording of the product-specific actual values
–
Recording of the product-specific statuses (OK/not OK; for the documentation)
Recording of the actual values for torque and angle of rotation is the preferred method. The basis
for the use of actual-value recording is the process reliability of the driving tool (see section 7 and
section A.1).
If product-specific recording of the threaded connection data is not possible for technical reasons,
then recording with a manufacturing reference (e.g., time stamp) must be carried out. In these
closed systems, only OK parts must leave the manufacturing area. The procedure must be agreed
upon with Planning, Production, and Quality Assurance.
Threaded connection recording after assembly
–
Recording of the retightening torques
–
Recording of the screw/bolt extension (in special cases)
–
Recording of the test torque
–
Documentation of the cyclical tool testing
The retightening torques (MNA1, MNA2), the screw/bolt extension, or the test torque (MP) are recor‐
ded using random samples.
Safety documentation
The recording options as per section 7.9.1 can also be used for the safety documentation. The sta‐
tus (OK/NOK) and, in random samples, MNA must be documented. The safety documentation must
be completed by the appropriate Production department with technical responsibility. All data that
documents the process reliability must be archived as per CSD. The determination as to how the
safety documentation is to be completed must be coordinated with the appropriate Quality Assur‐
ance department of the plant responsible for the vehicle type.
7.9  
7.9.1  
7.9.1.1  
7.9.1.2  
7.9.2  


### 第 22 页
Page 22
VW 01110-2: 2015-03
Actual-value recording for evaluating the quality of the threaded connection
For threaded connections that are bolted with monitored driving tool systems, the threaded con‐
nection actual values must also be documented as per CSD, so that:
–
In the event of an error, tracing and therefore isolation of affected threaded connections is pos‐
sible,
–
Statistical evaluations are possible for limit value determination of the monitoring and test pa‐
rameters,
–
A reduction in test cycles (product and process) can occur due to evaluation of the actual val‐
ues,
–
Error analyses can be conducted for determining the causes of errors.
Rework
Rework must be kept to a minimum (objective: zero defects). Therefore, qualified rework must not
be limited to simply indiscriminate re-torquing.
Rework can be carried out at the following stations:
–
In the production segment (preferred, because no disassembly of the components is necessa‐
ry);
–
Outside of the production segments (separate rework station).
In general, the principle of multiple evaluators does not apply to rework carried out on threaded
connections, because it is carried out by specially trained workers. Assembly of the components is
documented by the worker on a personal basis for each threaded joint type, either by a stamp on
the WPK or an entry in EDP. An additional color marking for process support or self-monitoring is
permissible. This also applies to components that are installed OK, which are dismantled in the
course of rework and must be reassembled.
The causes of rework must be determined and eliminated in the long term. If necessary, joining
elements and defectively assembled parts must be replaced. For torque-controlled procedures or
in the pre-tightening step for angle-controlled procedures, automated repeat tightening can be car‐
ried out in automatic assembly processes in the case of errors. As a result, the error rate can be
significantly reduced without considerable extra time.
Manual threading (with taps and dies in the 1st winding) of inner threads and outer threads by
trained personnel is permissible (any restrictions must be taken from the associated PDM sheet,
the TAB, or the drawing).
7.9.3  
8  


### 第 23 页
Page 23
VW 01110-2: 2015-03
Responsibilities
Table 6 and Table 7 describe the requirements and responsibilities for the pre-series-production
phase and for production. Deviating responsibilities are permissible if they are specified in writing
and their performance is ensured.
Table 6 – Responsibilities in the pre-production phase
Tasks
Person responsible
Procurement of the driving tool as per the Performance Specification, VW 01110-1, and
VW 01110-2
Planning
Implementation of product-related documentation, (e.g., TLD for threaded connections, catego‐
ries A and B)
Planning
Setting of parameters (specifications as per TAB, PDM, or drawing)
Planning
Specification of the provisional control parameters in the driving tool system
Planning
Setting of the monitoring parameters in the driving tool system
Planning
Measuring the retightening torques (MNA1) and defining the provisional range limits
Production
Releasing the provisional test parameters and test methods
Quality Assurance
Table 7 – Responsibilities in the production phase
Tasks
Person responsible
Measuring and maintaining the quality documentation with the aid of statistical methods:
- Actual value
- MNA1 values
Production
Definition of the range limits for test parameters
Production
Release of the range limits for test parameters
Quality Assurance
Testing of driving tools according to specified process standards
Production
Measuring the MNA2 values (disassembly audits in the powertrain area, whole vehicle tests)
Quality Assurance
Monitored threaded joint workstations:
Random checks of the assembly, test, and monitoring parameters used
Quality Assurance
Optimizing the test and control parameters and test methods
Production
Initiating error analyses
Production
Conducting error analyses
Production
Releasing the test and control parameters and test methods
Quality Assurance
NOTE 3: VW 01110-1, section 5 "Responsibilities" describes the requirements and responsibili‐
ties for the development phase.
9  


### 第 24 页
Page 24
VW 01110-2: 2015-03
Applicable documents
The following documents cited in this standard are necessary to its application.
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
VW 01110-1
Threaded Joints; Design and Assembly Specifications
VW 01126-2
Joining Technology; Tightening Torques for Screw/Bolt Assembly Be‐
yond the Elastic Limit
VW 01127
Self-Tapping Screw Connections; Application, Standard Values for Pilot
Hole Diameter, Specification of the Tightening Torques
VW 01129
Limit Values for Coefficients of Friction; Mechanical Fasteners with Met‐
ric ISO Threads
VDA Volume 6, part 3
Quality Management in the Automotive Industry – Process Audit – Prod‐
uct Development Process, Serial Production, Service Development
Process, Providing the Service
VDI/VDE 2645 sheet 2
Capability test for fastening technology - Machine capability test - MCT
VDI/VDE 2862 sheet 1
Minimum restrictions for application of fastening systems and tools - Ap‐
plications in the automotive industry
10  


### 第 25 页
Page 25
VW 01110-2: 2015-03
Detailed specifications concerning threaded connection processes
Process assurance for threaded connections
The use of technologies that are not described here require approval by the appropriate depart‐
ments (Planning, Quality Assurance, and Production) for the IFB and IFH processes (see
figure A.1 to figure A.4).
IFB process (torque-controlled tightening)
Figure A.1 – IFB process (torque-controlled tightening)
Appendix A (normative)  
A.1  
A.1.1  


### 第 26 页
Page 26
VW 01110-2: 2015-03
IFB process (angle-controlled tightening)
Figure A.2 – IFB process (angle-controlled tightening)
A.1.2  


### 第 27 页
Page 27
VW 01110-2: 2015-03
Process releases (torque-controlled tightening)
Figure A.3 – Process releases (torque-controlled tightening)
A.1.3  


### 第 28 页
Page 28
VW 01110-2: 2015-03
Process releases (angle-controlled tightening)
Figure A.4 – Process releases (angle-controlled tightening)
A.1.4  


### 第 29 页
Page 29
VW 01110-2: 2015-03
Range limits for retightening torques
The range limits for the retightening torques 1 MNA1 that are required for product testing must be
determined as a function of the processes (does not apply to screws/bolts with a chemical locking
agent).
Procedure for carrying out the MNA1 test as per section 7.6.3.
The determination of range limits as percentages yields values with decimal places. In these ca‐
ses, the values are rounded down to the measurement accuracy of the measuring instrument.
Direct unambiguous allocation of a specific value to assembly parameter MA is not possible. In
most cases, however, the following limits have proven to be useful (verified by TE):
Range limits for threaded joint type categories A and B:
Determine the values as per the IFB sequence and the process release (see section A.1).
For the retightening torques determined with metric threads without plastic parts in the threaded
connection, the range limits indicated in equation (A.1) apply:
0.80 × MA ≤ MNA1 ≤ 1.2 × MA
(A.1)
Range limits for threaded joint type category C:
Determine the values as per the IFB sequence and the process release (see section A.1).
For the retightening torques determined with metric threads without plastic parts in the threaded
connection, the range limits indicated in equation (A.2) apply:
0.70 × MA ≤ MNA1 ≤ 1.2 × MA
(A.2)
Range limits for threaded joint type categories B and C:
Determine the values as per the IFB sequence and the process release (see section A.1).
For the retightening torques determined for threaded connections with
–
Non-metric thread
–
One or more clamped plastic parts
the range limits indicated in equation (A.3) apply:
0.50 × MA ≤ MNA1 ≤ 1.2 × MA
(A.3)
If the retightening torques for threaded joint type categories A, B, or C do not meet these specifica‐
tions, the range limits of the MNA1 values will be determined on threaded connections that were pre‐
viously assembled with an OK result. Deviating range limits for MNA1 values must be preferably
documented in the PDM sheet or TAB for tightening torques. Deviations must be approved by the
appropriate department (see section 9).
A.2  


### 第 30 页
Page 30
VW 01110-2: 2015-03
Threaded joint workstation checklist
See table A.1.
Table A.1 – Threaded joint workstation checklist
Threaded joint workstation checklist
This checklist is not meant to supersede the auditing procedure as per VDA Vol‐
ume 6, part 3
Workstation no.:
Cost center:
1
Personnel/qualification
  
Points
1.1
Do the employees know the system and have they verifiably been instructed regarding the fea‐
tures and potential faults in the threaded connections?
 
1.2
Are responsibilities clear for the setting of parameters and maintenance?
 
1.3
Have the employees been instructed and trained to do rework?
 
1.4
Does a personnel allocation plan exist, including substitute management and qualification docu‐
mentation?
 
2
Production equipment/facilities
  
 
2.1
Does the driving tool system or the driving tool adhere to essential requirements of
VW 01110-2?
 
2.2
Are the important process parameters documented by the driving tool system for category A and
B threaded joints?
 
2.3
Has the driving tool system been accepted by Quality Assurance and the appropriate depart‐
ment (see VW 01110-2, section A.1)?
 
2.4
Is a faultless power supply of the driving tool system ensured (air pressure, electrical power)?
 
2.5
Is the driving tool sufficiently dimensioned with respect to assembly tightening procedures
(for category A and B connections, see VW 01110-2, section 7)
Reference value for dimensioning of the assembly spindles as per VW 01126-2, M+ +20%.
 
2.6
Are the assembly parameters specified as per the drawing (PDM, TAB) and have work instruc‐
tions been provided?
 
2.7
Have test parameters been determined on the finished product (e.g., retightening torques
MNA1/MNA2) and have the testing intervals been defined?
 
2.8
Has the bolting sequence (multiple threaded connections) been specified?1)
 
2.9
Are there measures that prevent the omission of individual tightening steps (e.g., color mark‐
ings)?
 
2.10
Are rework instructions provided and are they followed?1)
 
2.11
Are errors documented in relation to the product?
 
2.12
Is there suitable space and enough time of for rework?
 
2.13
Are the change intervals for driving tools and other tools specified and are they being adhered
to?
 
2.14
Has a release for production ramp-ups been issued and have settings and deviations been re‐
corded (test parameters independent of the different batches and shifts, first-piece release)?1)
 
2.15
Is a backup strategy in place in the event of system or tool failure?1)
 
2.16
Is it possible to inadvertently make a threaded connection for a screw point not included in the
scope of work?
 
A.3  


### 第 31 页
Page 31
VW 01110-2: 2015-03
Threaded joint workstation checklist
This checklist is not meant to supersede the auditing procedure as per VDA Vol‐
ume 6, part 3
Workstation no.:
Cost center:
3
Parts handling
  
 
3.1
Can the possibility of a mix-up of parts be excluded?
 
3.2
Are the joining elements stored and identified accordingly (transfer ticket, tracing)?
 
4
Error analysis/correction/continuous improvement
  
 
4.1
Are occurring errors documented in relation to the types and threaded joints (statistics)?
 
4.2
Is the threaded joint type data documented in relation to the product and is data stored (reprodu‐
cibility) and/or are error logs kept?
 
4.3
Can tightening curves be displayed?
 
4.4
Has the process capability of the system been demonstrated analogous to section A.1?
 
Evaluation
Number of
points
Degree of
fulfillment
as a %
Degree of fulfillment = Sum of all points achieved
Sum of all possible points
 × 100 (%)
max.
Ac‐
tual
 
1. Personnel/qualification
 
 
 
2. Production equipment/de‐
vices
 
 
 
3. Transportation/parts han‐
dling
 
 
 
4. Error analysis/correction
 
 
 
Minimum requirements 81% to 91%
Total
 
 
 
Target specification 92% to 100%
Evalua‐
tion
E = does not ap‐
ply
0 pts = Not
met
4 pts = Inade‐
quately met
6 pts = Partially met
8 pts = Mostly
met
10 pts = Met
completely
Degree of fulfill‐
ment
<81%, immediate action required
82% to 91%, remedial action and re-auditing required
1) Questions of special significance: noncompliance may lead to downgrading.


### 第 32 页
Page 32
VW 01110-2: 2015-03
Process optimization checklist
See table A.2.
For a process optimization checklist in the event of deviations from the MNA1 specifications, see
section A.2.
Table A.2 – Process optimization checklist
No.
Action
Evaluation1)
Yes
No
1
Tightening specifications and sequence in place and set?
 
 
2
Assembly devices and joining aids available and correctly used?
 
 
3
Tool set correctly?
 
 
4
Was the tightening torque applied correctly?
 
 
5
Random sample of 5 test parts with the driving tool and transducer on the product OK?
 
 
6
No deformation visible on the parts to be joined?
 
 
7
Has it been assured that clamped parts (rubber-bonded bushings) cannot spring back?
 
 
8
Tightening curve evaluation conducted (stick-slip, joining process, etc.)?
 
 
9
Can the possibility of measurement, evaluation, or operator errors be excluded in the de‐
termination of the MNA1 values (evaluation via curve diagram)?
 
 
10
Is the threaded connection free of impermissible contaminants (e.g., wax, grease, PVC)?
 
 
11
Influence of tightening speed evaluated?
 
 
12
Are the components in compliance with the drawing specification?
 
 
1) If a criterion has been answered with "No," process optimization is required before consultation with Design Engineer‐
ing.
Procedure for increasing the assembly speed
The friction behavior of the threaded fastening element is also significantly influenced by the tight‐
ening speed. High tightening speeds can significantly increase friction and its dispersions.
The assembly speeds must be based on the speeds described in section 5.6.
If a higher tightening speed is desired, this must first be implemented by increasing the pre-tighten‐
ing speed.
To control the influence of speed, angle-of-rotation measurements can be applied. Because the
angle of rotation and the preload force generally have good correlation, the evaluation of the moni‐
toring angle provides a meaningful result.
With torque-controlled assembly, the dispersions (3-sigma values) of the monitoring angle must not
be more than 5% lower than the values based on the natural, normally distributed process disper‐
sion that was measured before the increase in speed. Outliers must not be evaluated in the evalu‐
ation of process dispersion.
With assembly beyond the elastic limit, particularly high thread friction is significant because only
lower preload forces can be achieved due to the resulting equivalent stresses. Extremely high
screw head friction – particularly with concave screw head contact surfaces – means that the re‐
quired final torques will sometimes not be achieved due to the insufficient driving tool capacity.
A.4  
A.5  


### 第 33 页
Page 33
VW 01110-2: 2015-03
After an increase in speed, the final torques with assembly beyond the elastic limit must increase
by no more than 20%. With the aid of tightening curves, it must be verified with the AW11 proce‐
dure that the yield point is exceeded.
Additional increases in the tightening speeds are always based on changes with respect to a
speed of 200 rpm in the screw-in step and 20 rpm in the final tightening step.
Evaluation procedure for category B driving tools
See figure A.5.
Figure A.5 – Evaluation procedure for category B driving tools
The evaluation factor is the product of the individual factors (error effect, error frequency, probabili‐
ty of discovery, and error costs).
A.6  


### 第 34 页
Page 34
VW 01110-2: 2015-03
Comparison of assembly tightening procedures and production spe‐
cifications as per VW 01110-2: 2008-07
See table B.1.
Table B.1 – Comparison of assembly tightening procedures and production specifica‐
tions as per VW 01110-2: 2008-07
Assembly
tightening pro‐
cedure
quality class
Production specifications as per VW 01110-2: 2008-07, table 1
Quality
class
Method
group
Assembly tightening proce‐
dure
(tool)
Tightening tools
(examples)
Bolt
utilization
(approximate
value)
AD18
AD18
below the yield point
torque-controlled,
manual
- Click-type torque wrench
- Electronic hand-held wrench  
>50%
AD17
torque-controlled,
rotary wrenches
- Cordless driving tools
>55 %
- Stall driving tools
- Clutch driving tools
ADI16
torque-controlled,
pulse driving tools,
electronically controlled
- Pulse driving tools (Volks‐
wagen system QIS)
>60 %
AD15
torque-controlled,
rotary wrenches,
electronically controlled
- EC driving tool
- Electronically controlled
pneumatic
driving tool
- Electronically monitored
hand-held
wrench (AW12 only)  
>65 %
AW12
AW12
Angle-controlled, below the
yield point, manual/rotary
wrenches
AW11
AW11
Beyond the elastic limit
Angle-controlled,
manual
- Click-type torque wrench
- Electronic hand-held wrench  
approx. 100%
AW10
Angle-controlled,
rotary wrenches,
electronically controlled
- EC driving tools
- Electronically controlled
pneumatic driving tools
ASI10
pulse-controlled,
pulse driving tools,
electronically controlled
- Pulse driving tools
  (Volkswagen system QIS)
AS10
yield-point-controlled
rotary wrenches
electronically controlled
- EC driving tools
Appendix B (informative)  


### 第 35 页
Page 35
VW 01110-2: 2015-03
List of responsible parties
See table C.1. Version: 2015-03.
Table C.1 – Responsible parties
1
Harald Möbus
<EMAIL>
Ph.: +49 5361 9 39663
2
Gisbert Bartsch
<EMAIL>
Ph.: +49 561 490 4336
3
Detlef Cornels
<EMAIL>
Ph.: +49 375 55 2548
4
Julia Gardeya
<EMAIL>
Ph.: +49 5361 9 193305
5
Markus Gentemann
<EMAIL>
Ph.: +49 511 798 5151
6
Jan Gregor
<EMAIL>
Ph.: +49 5361 9 123391
7
Thomas Grünberg
<EMAIL>
Ph.: +49 5361 9 90739
8
Erdal Güldali
<EMAIL>
Ph.: +49 511 798 4211
9
Kay-Uwe Hellwig
<EMAIL>
Ph.: +49 5361 9 85531
10
Matthias Jesser
<EMAIL>
Ph.: +49 5361 9 48810
11
Rainer Jorde
<EMAIL>
Ph.: +49 5361 9 33243
12
Annegret Kühne
<EMAIL>
Ph.: +49 5361 9 44700
13
Heinz-Peter Luebking
<EMAIL>
Ph.: +49 4921 86 3891
14
Michael Lutz
<EMAIL>
Ph.: +49 7132 31 73758
15
Chris Mayerhoeffer
<EMAIL>
Ph.: +49 7132 31 742144
16
Josef Moser
<EMAIL>
Ph.: +49 5361 9 23083
17
Anton Schemmerer
<EMAIL>
Ph.: +49 841 89 570019
18
Martin Schoerwerth
<EMAIL>
Ph.: +49 841 89 43173
19
Harald Schott
<EMAIL>
Ph.: +49 531 298 4570
20
Detlef Treybig
<EMAIL>
Ph.: +49 5361 9 73150
21
Martin Wilke
<EMAIL>
Ph.: +49 5361 9 123794
Appendix C (informative)  

