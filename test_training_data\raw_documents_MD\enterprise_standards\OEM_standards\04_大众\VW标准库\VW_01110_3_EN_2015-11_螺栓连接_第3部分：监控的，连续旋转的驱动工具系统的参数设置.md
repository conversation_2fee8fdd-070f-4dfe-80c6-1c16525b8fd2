# VW_01110_3_EN_2015-11_螺栓连接_第3部分：监控的，连续旋转的驱动工具系统的参数设置.pdf

## 文档信息
- 标题：
- 作者：
- 页数：21

## 文档内容
### 第 1 页
Group standard
VW 01110-3
Issue 2015-11
Class. No.:
61000
Descriptors:
driving tool system, threaded connection, parameter setting, driving tool, tightening process
Threaded Connections
Part 3: Parameter Settings for Monitored, Continuously Rotating Driving Tool
Systems
Preface
At the time of this publication, the series of Volkswagen standards VW 01110 – under the general
title Threaded Connections – consists of the following parts:
–
(Part 1:) Design and Assembly Specifications
–
Part 2: Assembly and Process Assurance
–
Part 3: Parameter Settings for Monitored, Continuously Rotating Driving Tool Systems
–
Part 4: Testing and Evaluation of Threaded Connections
At the time of this publication, the following parts of this series of standards are currently being pre‐
pared:
–
Part 5: Joint Type Analysis
Contents
Page
Scope ......................................................................................................................... 2
Definitions .................................................................................................................. 3
Symbols and abbreviations ........................................................................................ 3
Requirements ............................................................................................................. 4
Multi-step parameter setting ....................................................................................... 4
Responsibilities .......................................................................................................... 4
Driving tools ............................................................................................................... 4
Data analysis .............................................................................................................. 4
Fastening step configuration ...................................................................................... 5
Sequence diagram ..................................................................................................... 5
General multi-step tightening process ........................................................................ 6
Positioning step (target value: angle of rotation) ........................................................ 7
Positioning step for pre-tightened bolts (target value: torque) ................................... 8
1
2
3
4
4.1
4.2
4.3
4.4
5
5.1
5.2
5.3
5.4
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Page 1 of 21
Technical responsibility
The Standards department
I/GQ-W1
Martin Schoerwerth
Tel.: +49 841 89 43173
EKDV/3 Tim Hofmann
EKDV
Tel.: +49 5361 9 27995
Maik Gummert
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Page 2
VW 01110-3: 2015-11
Pre-tightening step 1 (target value: torque) ................................................................ 9
Pre-tightening step 1 (target value: angle of rotation) .............................................. 10
Pre-tightening step 2 (target value: torque) .............................................................. 11
Final tightening step (target value: angle of rotation) AW11/AW12 ......................... 12
Final tightening step (target value: torque) AD18 ..................................................... 13
Specifications for setting parameters ....................................................................... 14
Common variant combinations with recommendations for possible use cases ....... 14
Variant combination 1 .............................................................................................. 14
Variant combination 2 .............................................................................................. 15
Variant combination 3 .............................................................................................. 16
Variant combination 4 .............................................................................................. 16
Variant combination 5 .............................................................................................. 17
Applicable documents .............................................................................................. 17
Control and screw-in torques ................................................................................... 18
Examples for defining torque tolerances and desired value .................................... 19
Definition of W− and W+ as well as WA .................................................................... 19
Defining W− and W+ as well as WA by means of screwing curves .......................... 19
List of responsible parties ........................................................................................ 21
5.5
5.6
5.7
5.8
5.9
6
7
7.1
7.2
7.3
7.4
7.5
8
Appendix A
Appendix B
B.1
B.2
Appendix C
Scope
This standard defines the procedure for setting the parameters of monitored, continuously rotating
driving tool systems. The fastening process to be realized preferably as a directly fastened threa‐
ded connection – if this is technically possible and useful – is described in multiple steps that can
be combined with each other in different ways.
This standard presents the most common combination variants that can be expanded or modified
in accordance with the needs, technical possibilities, and personnel qualifications, for example:
–
With loosening/maintenance steps between the individual steps,
–
For repair steps,
–
For fastener status code (SSC) controlled program sequences1),
–
For joining point detection, joining point angle tightening (AFW) method,
–
For control functions (e.g., review, stick-slip detection),
–
Redundancy (activate),
–
Plausibility (power control).
This standard defines requirements on responsibilities, driving tools, and data analysis.
This standard applies only to new projects or new procurement. Retroactive effects to existing sys‐
tems/production equipment/sequence processes must be agreed upon with the appropriate depart‐
ments (Planning, Quality Assurance) for ongoing vehicle projects, including major model upgrades
(GP).
1  
1)
See Group Performance Specification "Controlled Driving Tool Systems"


### 第 3 页
Page 3
VW 01110-3: 2015-11
Definitions
Directly fastened threaded connection
Threaded connection that can be mounted without manual pre-screwing of the bolt/nut and there‐
fore enables production times to be reduced
Fastening control
Control that is used to detect errors during the fastening procedure and thus to prevent damage to
the thread
Monitored driving tool system
System that detects actual values (by means of torque sensor, angle of rotation sensor) during the
fastening procedure and compares the actual and target values, wherein the fastening procedure
results can also be documented
Rehit
Fastening test of an already tightened fastener
Symbols and abbreviations
Torque tightening method
Joining point angle tightening method
Arrangement drawing
Angle (of rotation) tightening method
Major model upgrade
Tightening torque, upper tolerance
Tightening torque, desired value (target value), positioning step with pre-
tightened bolt
Tightening torque, desired value (target value), pre-tightening step 1
Tightening torque, desired value (target value), pre-tightening step 2
Tightening torque, desired value (target value), final tightening step
Tightening torque, actual value
Tightening torque, threshold value, positioning step
Tightening torque, threshold value, pre-tightening step 1
Tightening torque, threshold value, pre-tightening step 2
Tightening torque, threshold value, final tightening step
Tightening torque, lower tolerance
Product description manual
Fastener status code
Drawing in tabular form
Angle of rotation, upper tolerance
Angle of rotation, desired value (target value), positioning step
Angle of rotation, desired value (target value), pre-tightening step
Angle of rotation, desired value (target value), final tightening step
Angle of rotation, actual value
Angle of rotation, lower tolerance
Standard deviation
2  
3  
AD
AFW
ANO
AW
GP
M+
MA01
MA1
MA2
MA3
MI
MS1
MS2
MS3
MS4
M−
PDM
SSC
TAB
W+
WA1
WA2
WA3
WI
W−
s


### 第 4 页
Page 4
VW 01110-3: 2015-11
Requirements
Multi-step parameter setting
Monitored, continuously rotating driving tool systems must use a multi-step parameter setting proc‐
ess.
To reduce the complexity and expense for the parameter setting and error detection processes, as
well as to reduce the amount of data, the number of parametrized fastening steps must be restric‐
ted to the necessary amount.
Responsibilities
The Planning, Quality Assurance, and Production departments decide what variant combinations
are to be used for the specific joint type.
It must be defined who is responsible for maintaining and documenting the parameters.
The responsibilities are described in VW 01110-2.
Driving tools
All driving tools must fulfill the requirements of the appropriate Performance Specification and the
applicable Group standards.
The machine capability as per VW 01110-2 (verification of supplier, type approval) must be verified
before the driving tools can be used.
Data analysis
It must be possible to analyze the data over the entire fastening procedure as well as during parts
of this procedure. The parameter designations (abbreviations) from the Group Performance Speci‐
fication "Controlled Driving Tool Systems" apply.
4  
4.1  
4.2  
4.3  
4.4  


### 第 5 页
Page 5
VW 01110-3: 2015-11
Fastening step configuration
Sequence diagram
Legend
a
For AW11 as per VW 01126-2. For AW12, M− = 1.1 × Ms and M+ as per VW 01126-2.
Figure 1 – Sequence diagram for the fastening step configuration
5  
5.1  


### 第 6 页
Page 6
VW 01110-3: 2015-11
General multi-step tightening process
Legend
A
Positioning step/soft start
with angle of rotation or torque target value
B
Pre-tightening step 1
with angle of rotation or torque target value
C
Pre-tightening step 2
with torque target value
D
Final tightening step
with angle of rotation target value for AW11/AW12 or torque for AD18
  
M
Torque
W
Angle of rotation
1
Socket
2, 3
Part
4
Bolt (deformed plastically in final tightening step, AW11)
Control window
Figure 2 – Multi-step tightening (overview, schematic diagram)
The individual steps of the tightening process are defined in figure 3 to figure 9 and table 1 to
table 7.
5.2  


### 第 7 页
Page 7
VW 01110-3: 2015-11
Positioning step (target value: angle of rotation)
Legend
A
Positioning step/soft start
MS1
Tightening torque threshold value in the positioning step
W+
Angle of rotation, upper tolerance
W−
Angle of rotation, lower tolerance
WA1
Angle of rotation, desired value (target value)
M+
Tightening torque, upper tolerance
M−
Tightening torque, lower tolerance
Control window
Figure 3 – Schematic diagram for positioning step
Table 1 – Required parameters and tolerances for the positioning step (target value:
angle of rotation)
Characteristic
Parameter and tolerance value
Recommended rotational
speed n
50 rpm
MS1
0 Nm
W+
WA1 + 10°
W−
WA1 − 10°
WA1
180° (thread without clamping or adhesive coating max. 720°, thread with clamping or ad‐
hesive coating max. 360°)
M+
see values in table A.1
M−
≤ 0 Nm (due to signal processing/gears in driving tool)
Notes:
–
Position the fastener with socket and position the counterthread with low rotational speed so
that the tool is switched off close to M+ if possible (delayed cut-off at high rotational speed) in
order to prevent thread damage
–
It is possible to detect "skewed setting" and rehit.
–
Not OK if WI < W− for MI ≥ M+
5.3  


### 第 8 页
Page 8
VW 01110-3: 2015-11
Positioning step for pre-tightened bolts (target value: torque)
Legend
A
Positioning step/soft start
MS1
Tightening torque threshold value in the positioning step
W+
Angle of rotation, upper tolerance
W−
Angle of rotation, lower tolerance
MA01
Tightening torque, desired value (target value)
M+
Tightening torque, upper tolerance
M−
Tightening torque, lower tolerance
Control window
Figure 4 – Schematic diagram for positioning step for pre-tightened bolts
Table 2 – Required parameters and tolerances for positioning step for pre-tightened
bolts
Characteristic
Parameter and tolerance value
Recommended rotational speed n
50 rpm
MS1a)
Values as per table A.1 and < MA01
W+
720°
W−
1°
MA01
≤ MA from the pre-tightening step
M+
1.15 × MA01
M−
0.85 × MA01
a)
The angle of rotation, desired value (WA) is not applicable in this method.
Notes:
–
Positioning the fastener with socket at low rotational speed
–
Detecting "skewed setting" and rehit not required, because this is a pre-tightened threaded
connection. Rehit can be detected in one of the subsequent steps.
5.4  


### 第 9 页
Page 9
VW 01110-3: 2015-11
Pre-tightening step 1 (target value: torque)
Legend
B
Pre-tightening step 1
MA1
Tightening torque, desired value (target value)
MS2
Tightening torque, threshold value
W+
Angle of rotation, upper tolerance
W−
Angle of rotation, lower tolerance
M+
Tightening torque, upper tolerance
M−
Tightening torque, lower tolerance
Control window
Figure 5 – Schematic diagram of pre-tightening step 1 (target value: torque)
Table 3 – Required parameters and tolerances for pre-tightening step 1 (target value:
torque)
Characteristic
Parameter and tolerance value
Recommended rotational speed n
200 rpm
MA1
Value, see table A.2
MS2a)
0 Nm
W+
Setting as per examples in section B.1 or section B.2
W−
Setting as per examples in section B.1 or section B.2
M+
1.15 × MA1 (up to maximum 1.40 × MA1 for hard threaded connectionb))
M−
0.85 × MA1
a)
The angle of rotation, desired value (WA) is not applicable in this method.
b)
At high rotational speeds, the torque MA1 can be significantly exceeded.
Notes:
–
It is possible to detect "skewed setting"2) and rehit.
–
It is possible to detect a bolt that is too short or too long.
–
It is possible to detect thread seizing/thread damage.
5.5  
2)
Under some circumstances with thread damage due to high rotational speed; dependent on set value MA1. Therefore an upstream
positioning step must be used if possible.


### 第 10 页
Page 10
VW 01110-3: 2015-11
Pre-tightening step 1 (target value: angle of rotation)
Legend
B
Pre-tightening step 1
MS2
Tightening torque, threshold value
W+
Angle of rotation, upper tolerance
W−
Angle of rotation, lower tolerance
WA2
Angle of rotation, desired value (target value)
M+
Tightening torque, upper tolerance
M−
Tightening torque, lower tolerance
Control window
Figure 6 – Schematic diagram of pre-tightening step 1 (target value: angle of rotation)
Table 4 – Required parameters and tolerances for pre-tightening step 1 (target value:
angle of rotation)
Characteristic
Parameter and tolerance value
Recommended rotational speed n
>> 200 rpm
MS2a)
0 Nm
W+
WA2 + 10°
W−
WA2 − 10°
WA2
Setting as per examples in section B.1 or section B.2 (minimum requirement)
M+
Value, see table A.2
M−
≤ 0 Nm (due to signal processing/gears in driving tool)
a)
The torque desired value (MA) is not applicable in this method.
Notes:
–
It is possible to detect "skewed setting" and rehit.
–
It is possible to detect a bolt that is too short.
–
It is possible to detect thread seizing/thread damage.
5.6  


### 第 11 页
Page 11
VW 01110-3: 2015-11
Pre-tightening step 2 (target value: torque)
Legend
C
Pre-tightening step 2
MA2
Tightening torque, desired value
(target value)
MS3
Tightening torque, threshold value
W+
Angle of rotation, upper tolerance
W−
Angle of rotation, lower tolerance
M+
Tightening torque, upper tolerance
M−
Tightening torque, lower tolerance
Control window
Figure 7 – Schematic diagram of pre-tightening step 2 (target value: torque)
Table 5 – Required parameters and tolerances for pre-tightening step 2 (target value:
torque)
Characteristic
Parameter and tolerance value
Recommended rotation‐
al speed n
20 rpm
MA2
50% of the final tightening step for AD18 as per technical specifications (PDM, TAB, ANO) or to
MS for AW11/AW12 as per technical specifications (PDM, TAB, ANO)
MS3a)
MA from pre-tightening step 1
W+
Step 3 of the sequence diagram in figure 1, then step 4 of the sequence diagram in figure 1
(+ 3 × s)
W−
Step 3 of the sequence diagram in figure 1, then step 4 of the sequence diagram in figure 1
(− 3 × s)
M+
1.15 × MA2b)
M−
0.85 × MA2
a)
Without pre-tightening step 1: Setting MS3 (= MS1) torque threshold value = value as per table A.2 rigidly.
b)
If technically required (i.e., if without pre-tightening step 1 and n ≥ 200 rpm, because at high rotational speeds the torque MA2 is
significantly exceeded), an increase to a maximum 40% can be implemented (applies only for AD18 as per PDM in the final tighten‐
ing step).
Notes:
–
It is possible to detect thread seizing/thread damage or component defects.
–
It is possible to detect rehit.
5.7  


### 第 12 页
Page 12
VW 01110-3: 2015-11
–
Without positioning and pre-tightening steps in advance, under some circumstances, skewed
setting with thread damage is possible.
–
Deviations of coefficients of friction can be detected.
Final tightening step (target value: angle of rotation) AW11/AW12
Legend
D
Final tightening step
MS4
Tightening torque, threshold value
W+
Angle of rotation, upper tolerance
W−
Angle of rotation, lower tolerance
WA3
Angle of rotation, desired value (target value)
M+
Tightening torque, upper tolerance
M−
Tightening torque, lower tolerance
Control window
Figure 8 – Schematic diagram of final tightening step (target value: angle of rotation) AW11/AW12
Table 6 – Required parameters and tolerances for final tightening step (target value:
angle of rotation) AW11/AW12
Characteristic
Parameter and tolerance value
Recommended rotational
speed n
20 rpm
MS4a)
MS as per technical specifications (PDM, TAB, ANO)
W+
WA3 + 5°
W−
WA3 − 5°
WA3
Nominal value as per technical specifications (PDM, TAB, ANO)
M+
Step 3 of the sequence diagram in figure 1, then step 4 of the sequence diagram in figure 1
(+ 3 × s)
M−
Step 3 of the sequence diagram in figure 1, then step 4 of the sequence diagram in figure 1
(− 3 × s)
a)
The torque desired value (MA) is not applicable in this method.
5.8  


### 第 13 页
Page 13
VW 01110-3: 2015-11
Final tightening step (target value: torque) AD18
Legend
D
Final tightening step
MA3
Tightening torque, desired value (target value)
MS4
Tightening torque, threshold value
W+
Angle of rotation, upper tolerance
W−
Angle of rotation, lower tolerance
M+
Tightening torque, upper tolerance
M−
Tightening torque, lower tolerance
Control window
Figure 9 – Schematic diagram of final tightening step (target value: torque) AD18
Table 7 – Required parameters and tolerances for final tightening step (target value:
torque) AD18
Characteristic
Parameter and tolerance value
Recommended rotation‐
al speed n
20 rpm
MA3
Nominal value as per technical specifications (PDM, TAB, ANO)
MS4a)
MA2 from pre-tightening step 2 (or pre-tightening step 1 if pre-tightening step 2 not present)
W+
Step 3 of the sequence diagram in figure 1, then step 4 of the sequence diagram in figure 1
(+ 3 × s)
W−
Step 3 of the sequence diagram in figure 1, then step 4 of the sequence diagram in figure 1
(− 3 × s)
M+
1.15 × MA3
M−
0.85 × MA3
a)
The angle of rotation, desired value (WA) is not applicable in this method.
5.9  


### 第 14 页
Page 14
VW 01110-3: 2015-11
Specifications for setting parameters
The parameter settings for fastening control must be selected so that thread destruction (e.g.,
skewed setting of the bolt) is detected and prevented at the onset.
The control parameters must be selected so that normally the entire fastening procedure is moni‐
tored.
The function of the positioning step (target value: angle of rotation) and the pre-tightening step 1
can be influenced by the operator for the variant combinations 1 to 3 (see figure 10 to figure 12).
For these variant combinations, joint type problems are optimally detected only if there are positive
locking connections between the socket and bolt and between the bolt and internal thread. In gen‐
eral, training of the operator will be required.
The fastening procedure must be monitored in such a way that the largest spectrum of potential
errors will be detected with the largest possible probability, e.g.:
–
Bolts/nuts set skewed,
–
Insufficient component strength in the threaded connection,
–
Other underlying parts (cable ties, weld beads, etc.),
–
Repeated fastening for a threaded connection already evaluated as OK (rehit),
–
Prevention of unnecessary not-OK status.
The control window of final torques or final angles of rotation must be centered on tolerances as
per technical specifications (PDM, TAB, ANO). The speeds must be selected as per VW 01110-2,
with reference to VW 01129.
Deviations from these specifications must be tested separately for functional reliability and error
detection and agreed upon with the appropriate departments.
Common variant combinations with recommendations for possible use cases
Common variant combinations are shown in figure 10 to figure 14.
Variant combination 1
–
Directly fastened threaded connections with handheld, monitored, continuously rotating driving
tool systems (no manual pre-screwing of screw/bolt/nut for mounting)
–
Automatic fastening stations
–
Hand-operated fastening stations
6  
7  
7.1  


### 第 15 页
Page 15
VW 01110-3: 2015-11
Legend
A
Positioning step/soft start
B
Pre-tightening step 1
C
Pre-tightening step 2
D
Final tightening step
Figure 10 – Variant combination 1
Variant combination 2
–
Automatic fastening stations
–
Hand-operated fastening stations
Legend
A
Positioning step/soft start
B
Pre-tightening step 1
C
Pre-tightening step 2
D
Final tightening step
Figure 11 – Variant combination 2
7.2  


### 第 16 页
Page 16
VW 01110-3: 2015-11
Variant combination 3
–
For hard joints (pre-tightening step 1 can be eliminated).
–
For already pre-tightened threaded connections, e.g., with battery-operated or pneumatic driv‐
ing tool (with head bearing surface). With positioning step for pre-tightened bolts.
Legend
A
Positioning step/soft start
C
Pre-tightening step 2
D
Final tightening step
Figure 12 – Variant combination 3
Variant combination 4
–
Pre-tightening or pre-screwing with pneumatic/battery-operated screw driving tool
Legend
B
Pre-tightening step (with battery-op‐
erated or pneumatic screw driving
tool)
C
Pre-tightening step
D
Final tightening step
M
Torque
MA
Switch-off torque for pre-tightening
by means of pneumatic/battery-op‐
erated screw driving tool
MS1
Tightening torque threshold value in
the positioning step
W
Angle of rotation
Figure 13 – Variant combination 4
MA must not be greater than the value as per table A.2.
7.3  
7.4  


### 第 17 页
Page 17
VW 01110-3: 2015-11
Variant combination 5
Legend
C
Pre-tightening step
D
Final tightening step
M
Torque
W
Angle of rotation
Figure 14 – Variant combination 5
Applicable documents
The following documents cited in this standard are necessary to its application.
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
VW 01110-2
Threaded Connections; Part 2: Assembly and Process Assurance
VW 01126-2
Joining Technology; Tightening Torques for Screw/Bolt Assembly Be‐
yond the Elastic Limit
VW 01129
Limit Values for Coefficients of Friction; Mechanical Fasteners with Met‐
ric ISO Threads
7.5  
8  


### 第 18 页
Page 18
VW 01110-3: 2015-11
Control and screw-in torques
Table A.1 – Control torque M+ or MS1 for the positioning step
Thread
M+ or MS1 in the positioning step
max.
M6
2 Nm
M8
3 Nm
M10
4 Nm
M12 × 1.5
5 Nm
M14 × 1.5
6 Nm
Table A.2 – Screw-in torque MA or M+ (pre-tightening step 1) or MS (pre-tightening
step 2)
Thread
Screw-in torque without lock‐
ing and adhesive coating
Screw-in torque for adhesive
coating
Screw-in torque for locking
coating or nuts with clamping
part
 
max.
max.
max.
M6
  3 Nm
  3 Nm
  4.5 Nm
M8
  5 Nm
  6 Nm
  9.0 Nm
M10
  7 Nm
  9 Nm
15.5 Nm
M12 × 1.5
10 Nm
13 Nm
21.5 Nm
M14 × 1.5
15 Nm
21 Nm
31.0 Nm
Appendix A (normative)  


### 第 19 页
Page 19
VW 01110-3: 2015-11
Examples for defining torque tolerances and desired value
Definition of W− and W+ as well as WA
Procedure for defining W− and W+ as well as WA (see pre-tightening step 1), see also figure B.1:
1.
Marking the bolt/screw head
2.
a) Screwing in the bolt/screw up to the head bearing surface and determining the screw-in an‐
gle of rotation up to the head bearing surface after pre-screwing (e.g., 5 × 360° = 1 800°)
b) Defining the lower tolerance or desired value by means of the screw-in angle of rotation:
    W− or WA = 1 800° × 0.5 = 900°
c) Defining the upper tolerance by means of the screw-in angle of rotation: W
+ = 1 800° × 1.5 = 2 700°
Legend
T1
Part 1
T2
Part 2
Figure B.1 – Defining W− and W+ as well as WA (see pre-tightening step 1)
Defining W− and W+ as well as WA by means of screwing curves
Procedure for defining W− and W+ as well as WA by means of screwing curves (see pre-tightening
step 1), see also figure B.2:
1.
Determining the maximum screw-in torque as per table A.2
2.
Determining the screw-in angle of rotation using the screw-in curve
3.
a) Defining the lower tolerance or desired value by means of the screw-in angle of rotation:
    W− or WA = 1 850 ° × 0.5 = 925 °
b) Defining the upper tolerance by means of the screw-in angle of rotation: W
+ = 1 850 ° × 1.5 ≈ 2 700°
Appendix B (informative)  
B.1  
B.2  


### 第 20 页
Page 20
VW 01110-3: 2015-11
For determining the screwing curves, the screwing process must be started only when the driving
tool is contacting the screw-in point.
Legend
1
Maximum screw-in torque as per table A.2 (e.g., 7 Nm for M10)
M
Torque
W
Angle of rotation
Figure B.2 – Definition of W− and W+ as well as WA by means of screwing curves (see pre-tighten‐
ing step 1)


### 第 21 页
Page 21
VW 01110-3: 2015-11
List of responsible parties
Table C.1 indicates the responsible parties for version 2015-11.
Table C.1 – Responsible parties
1
Martin Schoerwerth
<EMAIL>
Phone: +49 841 89 43173
2
Eckhard Behm
<EMAIL>
Phone: +49 ************
3
Matthias Jesser
<EMAIL>
Phone: +49 5361 9 48810
4
Heinz-Peter Luebking
<EMAIL>
Phone: +49 4921 86 3891
5
Jürgen Rammelkamp
<EMAIL>
Phone: +49 ************
6
Andreas Sarter
<EMAIL>
Phone: +49 7132 31 73638
7
Martin Wilke
<EMAIL>
Phone: +49 5361 9 123794
Appendix C (informative)  

