# MBN_31001_DE_2004-05_CAD图纸：产品基本结构框架，零件说明和标识.pdf

## 文档信息
- 标题：Microsoft Word - mbn  31001_2004-05.doc
- 作者：
- 页数：15

## 文档内容
### 第 1 页
Mercedes-Benz
  MBN 31 001
Technische Norm
Ausgabedatum:  2004-05
Normgruppe:  31
Seiten insgesamt (inkl. <PERSON>hang): 15
Fachbetreuer: Mogwitz / Schloz
Werk 19; Abt.: EP/QIN
Tel.: 3 49 05 / 3 49 81
CAD-Zeichnungen
Grundlagen der Produktdarstellung
Basis-Konstruktionsrahmen,
die Darstellung und Kennzeichnung von Teilen
CAD-drawings; general principles of product-presentation, basic design limits, presentation and
identification of parts
Vorwort
Die Norm dient als Basisinformation mit dem Ziel einer einheitlichen Produktdarstellung auf CAD-
Zeichnungen der Technischen Produktdokumentation.
Alle in dieser Norm aufgeführten Regelwerke stehen im System „Standards Information System“
(SIS) zur Verfügung.
Änderungen
Gegenüber der Ausgabe 2001-12 wurden folgende Änderungen vorgenommen:
-Norm auf neuer Normvorlage dargestellt ; Ordnungsnummer 31 051 entfallen
-Titel war „Grundlagen für CAD-Zeichnungen; Zeichnungsrahmen, Rahmengrößen, Darstellen von
 Teilen, Kennzeichnen von Teilen“
-neuen Abschnitt 3, Abkürzungen, Akronyme..., eingefügt; nachfolgend alle weiteren Abschnitte
-Sachverhalt CATIA-Version V5 aufgenommen
-Anzahl aufgeführter Regelwerke korrigiert
Copyright DaimlerChrysler 2004
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 2 页
MBN 31 001, 2004-05, Seite 2
Copyright DaimlerChrysler 2004
Inhaltsverzeichnis
1
Anwendungsbereich................................................................................................................................3
2
Normative Verweisungen ........................................................................................................................3
3
Abkürzungen, Akronyme (Kurzwörter), Definitionen & Symbole ............................................................3
4
Konstruktionsrahmen ..............................................................................................................................3
4.1
Arten  und Größen von Konstruktionsrahmen .................................................................................3
4.2
Bereitstellung von Konstruktionsrahmen .........................................................................................5
4.3
Elemente für den Konstruktionsrahmen...........................................................................................6
5
Darstellen der Teile in CAD-Zeichnungen...............................................................................................7
5.1
Darstellung allgemein und Anordnung der Ansicht..........................................................................7
5.2
Ansichten im Karosseriebau ............................................................................................................8
5.3
Netzliniendarstellung........................................................................................................................8
5.4
Schrägbilder auf Zeichnungen.........................................................................................................9
5.5
Symmetrische Teile und spiegelbildlich gleiche Teile......................................................................9
5.6
Ansichten .........................................................................................................................................9
5.7
Schnitte ............................................................................................................................................9
5.8
Linien................................................................................................................................................9
5.9
Schraffuren.......................................................................................................................................9
5.10
Beschriftung und Schriftgrößen .......................................................................................................9
5.11
Maßstäbe zum Darstellen der Teile und Abbildungsmaßstäbe von CAD-Zeichnungen bei der
Erzeugung ......................................................................................................................................................9
5.12
Maßeintragungen und Bemaßung in Schrägbildern auf CAD-Zeichnungen .................................10
5.13
Toleranzeintragungen ....................................................................................................................10
5.14
Oberflächenbeschaffenheit ............................................................................................................10
5.15
Oberflächenschutzangaben ...........................................................................................................10
5.16
Wärmebehandlungsangaben.........................................................................................................10
5.17
Schweiß- und Lötangaben .............................................................................................................10
5.18
Gewinde und Gewindeeinsätze .....................................................................................................10
5.19
Werkstückkanten............................................................................................................................10
5.20
Zentrierbohrungen..........................................................................................................................11
5.21
Freistiche........................................................................................................................................11
5.22
Radien............................................................................................................................................11
5.23
Wortangaben im Zeichnungsfeld einer CAD-Zeichnung ...............................................................11
5.24
Benennungsgebung für das Dargestellte in einer CAD-Zeichnung...............................................11
5.25
Einheiten, Formelzeichen, mathematische Zeichen und Begriffe .................................................11
6
Kennzeichnen von Teilen in CAD-Zeichnungen ...................................................................................12
6.1
Kennzeichnung von  Erzeugnisteilen.............................................................................................12
6.2
Kennzeichnung der Werkstoffe bei Bauteilen von Kraftfahrzeugen ..............................................12
6.3
Kennzeichnung von Aufnahme-, Fixier-,Justierlöcher-/konturen-, Lackierungs- und
Schweißlöcher ..............................................................................................................................................12
6.4
Kennzeichnung dokumentationspflichtiger Teile in CAD-Zeichnungen.........................................12
6.5
Signatur-Kenner  für elektronische Kraftfahrzeug-Steuergeräte in CAD-Zeichnungen.................12
Anhang A (informativ)  Die gebräuchlichsten Benennungsvorsätze (Textbaustein 1) mit ihren Definitionen
eines Benennungstextes ..............................................................................................................................13
Anhang B (informativ)  Textbaustein 4 des Benennungstextes; Muster ergänzender technischer Merkmale
zu Textbaustein 2 .........................................................................................................................................15
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 3 页
MBN 31 001, 2004-05, Seite 3
Copyright DaimlerChrysler 2004
1 
Anwendungsbereich
Diese Norm beschreibt keinen eigenen normativen Sachstand, sondern ist als Nachschlagewerk aufge-
baut, der allgemeine Regeln zur Ausführung von CAD-Zeichnungen und zur graphischen Darstellung von
Teilen enthält. Ziel ist eine einheitlichen Ausführung bei CAD-Zeichnungen.
2 
Normative Verweisungen
Der Abschnitt enthält keine Daten; Norm ist ein Nachschlagewerk.
3 
Abkürzungen, Akronyme (Kurzwörter), Definitionen & Symbole
- CAD-Zeichnung
durch ein Rechnerprogramm erzeugte Konstruktions-Zeichnung, die durch einen Plotter oder Drucker
gedruckt oder auf einem Bildschirm angezeigt wird
- CATIA
ist das CAD-System in der Version für Mercedes-Benz
- Konstruktionsrahmen
sind genormte Zeichenflächenbegrenzungen, die zur Erstellung einer Konstruktions-Zeichnung aus der
Generierung von 3D-CAD-Modellen zur Verfügung stehen
- Konstruktions-Zeichnung
ist eine Technische Zeichnung, die einen Gegenstand in seinem vorhergesehenen Endzustand darstellt
- SIS
SIS ist die Abkürzung für das Normen-System  „Standards Information System“
- SRM
SRM ist die Abkürzung für  „Sachstamm- Recherche- und -Management -System“
- Zusatzfeld
ein Zusatzfeld ist die Summe von Datenfeldern, die eine notwendige Ergänzung zu einem Basis-
Schriftfeld darstellen
4 
Konstruktionsrahmen
Konstruktionsrahmen sind genormte Zeichenflächenbegrenzungen, die zur Zeichnungserstellung aus der
Generierung aus 3D-CAD-Modellen zur Verfügung stehen.
4.1 
Arten  und Größen von Konstruktionsrahmen
Konstruktionsrahmen aller genormten DIN-Größen und Mercedes-Benz-spezifische-Übergrößen stehen
zur Verfügung.
Weiterhin werden Konstruktionsrahmen DIN A4 für das PC-System „Winword “ angeboten.
Das Querformat DIN A4 ist nicht zugelassen.
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 4 页
MBN 31 001, 2004-05, Seite 4
Copyright DaimlerChrysler 2004
Die Gestaltung der Konstruktionsrahmen entspricht der Norm DIN EN ISO 5457. Verändert gegenüber
DIN EN ISO 5457 sind:
- Ränder und Begrenzungen von 5 auf  8 mm, Unterseite Format auf  12 mm für Formate > DIN A4
- bei Formaten mit Feldeinteilung (Planquadrate) beträgt die Feldlänge 100  statt  50 mm.
Ein Konstruktionsrahmen beinhaltet Zeichenfläche (das Zeichnungsfeld) und Zeichnungs-Schriftfeld.
Zeichnungsfeld-Beschreibung siehe Werknorm MBN 31 020-2. Die in Werknorm MBN 31 020-1 beschrie-
benen Zeichnungsschriftfelder sind in der unteren rechten Ecke im Zeichnungsrahmen angeordnet. Das
Format DIN A4 macht eine Ausnahme.
Alle Mercedes-Benz-spezifischen-Übergrößen enthalten ebenfalls ein Schriftfeld in der unteren rechten
Ecke, aber zusätzlich die Aufteilung der Mikroverfilmung nach DIN 19052 Teil 4. Im Bedarfsfall ist vom
Konstrukteur die Kennzeichnung der einzelnen Aufnahmeteile nach dem Muster im Bild 1 auszuführen.
Die Anschreibung der Kennung erfolgt  7 mm über der Zeichenflächenbegrenzung und ist in einer Schrift-
größe von 7 mm auszuführen.
Bild 1: Anschreibung der Mikrofilm-Kennzeichnung von Aufnahmeteilen in Mercedes-Benz-spezifischen-
Übergrößen
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 5 页
MBN 31 001, 2004-05, Seite 5
Copyright DaimlerChrysler 2004
Die Größen der Konstruktionsrahmen in DV-Systemen entsprechen in ihrer Größe dem Papierendformat
der A-Reihe DIN EN ISO 216. Die freigegebenen Größen sind in Tabelle 1 aufgeführt.
Tabelle 1
Maße (beschnittenes Format) der genormten DIN-Format-Größen und der Mercedes-Benz-
spezifischen-Übergrößen für CAD-Zeichnungen;  Maße in mm
DIN-Format-
Kurzzeichen
Mercedes-Benz-spezifisches
Kurzzeichen für Überlängen
beschnittenes Format
Höhe
Länge
A4
-
297
210
A3
-
297
420
A2
-
420
594
A2x  694
694
A2x  794
794
A2x  894
894
A2x1094
1094
A2x1294
1294
A1
-
594
841
A1x1050
1050
A1x1260
1260
A1x1470
1470
A1x1730
1730
A0
-
841
1189
A0x1400
1400
A0x1610
1610
A0x1820
1820
A0x2030
2030
A0x2240
2240
A0x2450
2450
A0x3367
3367
A0x4456
4456
R2x1582
1189
1582
R2x2323
2323
R2x3064
3064
R2x3805
3805
R2x4546
4546
R2x5287
5287
R2x6028
6028
R2x6769
6769
R3x1582
1486
1582
R3x2323
2323
R3x3064
3064
R3x3805
3805
R3x4546
4546
R3x5287
5287
R3x6028
6028
R3x6769
6769
4.2 
Bereitstellung von Konstruktionsrahmen
4.2.1 
Konstruktionsrahmen im CAD-System „CATIA“
die Bereitstellung der Konstruktionsrahmen steht zur Verfügung
-
für CATIA Version V4 in der Detail-Library „CATIA.MB#ZNORM.P#019.MAP“ in der Family
 
„Konstruktionsrahmen“
-
für CATIA Version V5 im Katalog „#Catalog.MBZNORM_19“ mit dem Namen
 
„KONSTRUKTIONSRAHMEN.catalog“
4.2.2 
Konstruktionsrahmen im PC-System Winword
Tabelle 2 enthält Konstruktionsrahmen, die zur Anwendung freigegeben sind. Sie stehen im System
„Standards Information System“ (SIS) unter der Normnummer MBN 31 001 zur Verfügung.
Konstruktionsrahmen für weitere PC-Systeme stehen nicht zur Verfügung.
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 6 页
MBN 31 001, 2004-05, Seite 6
Copyright DaimlerChrysler 2004
Tabelle 2
Word-Konstruktionsrahmen zur PC-Anwendung
Dokument
Titel
MBN 31 001 Vorlage
Konstruktionsrahmen    DIN A4
MBN 31 001 Vorlage
Konstruktionsrahmen  -Deckblatt (Blatt 0) -    DIN A4
MBN 31 001 Vorlage
Konstruktionsrahmen  -Schriftzeichnung -      DIN A4
MBN 31 001 Vorlage
Konstruktionsrahmen                DIN A3
MBN 31 001 Vorlage
Schriftrahmen allgemein  neu   DIN A4
MBN 31 001 Vorlage
Schriftrahmen Funktionsvorschrift   -Titelseite -  DIN A4
MBN 31 001 Vorlage
Schriftrahmen Funktionsvorschrift   -Textseite -  DIN A4
4.3 
Elemente für den Konstruktionsrahmen
Jeder Konstruktionsrahmen beinhaltet als festen Bestandteil ein Basis-Zeichnungsschriftfeld nach Werk-
norm MBN 31 020-1.
Nach Bedarf sind ein Zusatzschriftfeld (siehe Abschnitt 4.3.2) und verschiedenartige Zusatzfelder (siehe
Abbschnitt 4.3.3) dieser Norm anzuwenden.
Aufgebracht ist das Basis-Zeichnungsschriftfeld auf den Konstruktionsrahmen der DIN-Größen A0 bis DIN
A3 und Übergrößen in der unteren rechten Ecke des beschnittenen Formates. Das Format DIN A4 enthält
das Basis-Zeichnungsschriftfeld in der unteren Hälfte des Formates.
4.3.1 
Zeichnungsschriftfelder für Konstruktionsrahmen und ihre Bereitstellung
Alle Arten von Zeichnungsschriftfelder sind in Werknorm MBN 31 020-1 dargestellt und beschrieben. Als
eigenes Element stehen die Schriftfelder sowohl in CATIA als auch im PC-System Winword  nicht zur
Verfügung.
4.3.2 
Zusatzschriftfelder für Konstruktionsrahmen und ihre Bereitstellung
Das Anwendungsgebiet von Zusatzschriftfeldern sind die Tabellenzeichnungen und die Teiledarstellung
ohne eigene Zeichnung.
Ein Zusatzschriftfeld steht in seiner Gesamtheit aller Datenfelder nicht zur Verfügung. Alle erforderlichen
Datenfelder sind  einzeln verfügbar und sind im Bedarfsfall vom Anwender zusammenzustellen.
Folgende Datenfelder sind verfügbar:
Position / Sach.-Nr. / ZGS / CAD / Benennung / Werkstoff / Allgemeintoleranz / Oberflächenschutz / ge-
setzliches Merkmal / Bemerkung / Oberfläche / Masse / Dokumentationspflicht / Signierkennung für Kfz-
Steuergeräte; bereitgestellt sind sie in "CATIA"
-
für CATIA Version V4 in der Detail-Library „CATIA.MB#ZNORM.P#019.MAP“ in der Family
 
„Zusatzfelder"
-
für CATIA Version V5 in „#Catalog.MBZNORM_19“ unter dem Namen  „ZUSATZFELDER.catalog“.
Für das Beschriften von Zusatzschriftfeldern gilt die Werknorm MBN 31 002.
4.3.3 
Zusatzfelder für Konstruktionsrahmen und ihre Bereitstellung
Zusatzfelder werden dort verwendet, wo weitere Daten zu einem Teil / Baugruppe benötigt werden.
Tabelle 3 zeigt eine Auswahl an Zusatzfeldern.
Für das Beschriften von Zusatzfeldern gilt die Werknorm MBN 31 002.
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 7 页
MBN 31 001, 2004-05, Seite 7
Copyright DaimlerChrysler 2004
Tabelle 3
Zusatzfelder für CAD-Zeichnungen; Tabelle nicht vollständig
Titel
Inhalt
Darstellungsangaben
letzter Schnitt/letzte Ansicht....
Änderungsindex
letzter Änderungsindex
Passmaße
Paßmaß / Abmaße
Gußteile
nicht bemaßte Wanddicken/Radien/Formschräge
ungültige Zeichnung Ausführung Nr. 1
Ungültig! ersetzt durch Zeichnung mit gleicher Sachnummer
ungültige Zeichnung Ausführung Nr. 2
Ungültig! wird KZ siehe..
Ausschnitts-Zeichnung
Alle weiteren Maße und Angaben siehe..
Dokumentationspflicht
Zusatzfeld  zur Kennzeichnung dokumentationspflichtiger Teile
Symbol "Dokumentationspflicht"
Symbol für einzelne Merkmale zur Dokumentationspflicht
Summe Merkmale -Dokumentationspflicht
Zusatzfeld zur Aufsummierung der Anzahl von Merkmalen
Signatur-Kennzeichnung
Nachweisführung zur Signatur-Kennzeichnung von flashbaren elektroni-
schen Kfz- Steuergeräten
weitere Zusatzfelder, die durch Werknormen definiert sind
z. B. Beschichtungs- und Verbindungsstellen im Karrosseriebau nach MBN 10 187, Allgemeintoleranzen nach MBN 36 012
usw.
Bereitgestellt sind sie in CATIA
-für CATIA Version V4 in der Detail-Library "CATIA.MB#ZNORM.P#019.MAP“ in der Family "Zusatzfel-
der"
-für CATIA Version V5 in „#Catalog.MBZNORM_19“ unter dem Namen  „ZUSATZFELDER.catalog“.
Für das PC-System “Winword” stehen die in Tabelle 4 aufgeführten Zusatzfelder im System „Standards
Information System“ (SIS) ) unter der Normnummer MBN 31 001 zur Verfügung.
Tabelle 4
Zusatzfelder für PC-Anwendung Winword
Dokument
Titel
MBN 31 001   Vorlage
Ausschnittszeichnung
MBN 31 001   Vorlage
Schriftfeld-Lieferantenzeichnung
MBN 31 001   Vorlage
Änderungsschriftfeld -Lieferantenzeichnung
MBN 31 001   Vorlage
Passmaße
MBN 31 001   Vorlage
ungültige Zeichnung Ausführung Nr. 1
MBN 31 001   Vorlage
ungültige Zeichnung Ausführung Nr. 2
5 
Darstellen der Teile in CAD-Zeichnungen
Die in Abschnitt 5 aufgeführten Regeln, bilden eine Basis für die Teile-Darstellung in CAD-Zeichnungen.
5.1 
Darstellung allgemein und Anordnung der Ansicht
Das Darstellen von Teilen in CAD-Zeichnungen erfolgt gemäß den Regeln der DIN ISO 128.
Die Anordnung der Ansichten erfolgt nach DIN ISO-5456-2 Projektionsmethode 1.
Das Symbol der Projektionsmethode 1 wird bereits in den Zeichnungsrahmen vorgegeben, siehe Bild 2.
Bild 2: Symbol der Projektionsmethode 1
Werkspezifisch gilt zusätzlich für die Systematik und die Bezeichnungsvarianten von Ansichten, Einzel-
heiten und Schnitten die Werknorm MBN 31 008, siehe hierzu Abschnitt 5.6 und 5.7.
Allgemeine Begriffe für Projektionsmethoden in der Technischen Produktdokumentation werden in DIN
ISO 10209-2 aufgeführt.
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 8 页
MBN 31 001, 2004-05, Seite 8
Copyright DaimlerChrysler 2004
5.2 
Ansichten im Karosseriebau
Die Anordnung der Ansichten einer Karosserie und deren Teile erfolgt grundsätzlich durch Zeichnen der
linken Fahrzeughälfte.
Rückansicht
Seitenansicht
Vorderansicht
Draufsicht
Bild 3:    Anordnung der Ansichten im Fahrzeugbau
5.3 
Netzliniendarstellung
Fahrzeuge und ihre Aufbauteile werden im Koordinatennetz dokumentiert, siehe Bild 4; d.h. die Darstel-
lungen des gesamten Fahrzeuges, der Karosserie oder des Fahrgestells als Einzelteil bzw. Zusammen-
bauzeichnung erfolgen mit Hilfe von Netzlinien in den verschiedenen Ansichten.
Die Netzlinien werden mit den Buchstaben   X , Y , Z    gekennzeichnet.
Die Höhen- und Längennetzlinien sind so angeordnet, daß sich die Nullinien der Höhe und der Länge in
der Mitte des Vorderrades der Seitenansicht schneiden (Nullpunkt). Die Nullinie der Breite verläuft durch
die Mitte des Fahrzeugs in der Vorderansicht und in der Draufsicht. Netzlinien unter Z0 bzw. links von X0
werden mit negativem Vorzeichen versehen, das gleiche gilt auch für rechts von Y0 in der Ansicht.
Die Netzlinienabstände betragen im natürlichen Maßstab (1:1) 100 mm.
Sämtliche Maße eines dargestellten Teiles werden auf den Maßebenen X0, Y0 und Z0 aufgebaut.
Bild 4: Koordinatennetz im PKW-Bereich
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 9 页
MBN 31 001, 2004-05, Seite 9
Copyright DaimlerChrysler 2004
5.4 
Schrägbilder auf Zeichnungen
Schrägbilder können zur anschaulicheren Darstellung des Bauteils verwendet werden; die Anzahl ist nicht
eingeschränkt. Folgende Kriterien müssen eingehalten werden:
- deutliche Abgrenzung zu den produktdefinierenden Ansichten
- Parallelprojektionen sind erlaubt; die Richtungen sind nicht eingeschränkt
- Angaben über Maßstab / Blickrichtung / Überschrift erfolgen nicht.
Bemaßung von Schrägbildern siehe Abschnitt 5.12.
5.5 
Symmetrische Teile und spiegelbildlich gleiche Teile
Bei größeren Teilen, die symetrisch sind, wird nur die linke Hälfte dargestellt.
Bei spiegelbildlich gleichen Teilen ist nur das linke Teil zu zeichnen. Geringfügige Abweichungen des
rechten Teiles werden so dargestellt, als wenn die Abweichungen im linken Teil vorhanden wären.
Für die Sachnummernanschreibung des rechten Spiegelbild-Teiles gilt die interne Rahmenrichtlinie EDR
001.
5.6 
Ansichten
Darstellung von Ansichten ist in DIN ISO 128-30 und DIN ISO 128-34 geregelt.
Werkspezifisch ist für die Systematik und die Bezeichnungsvarianten die Werknorm MBN 31 008 anzu-
wenden.
5.7 
Schnitte
Darstellung von Schnitten ist in DIN ISO 128-40 und DIN ISO 128-44 geregelt.
Werkspezifisch ist für die Systematik und die Bezeichnungsvarianten die Werknorm MBN 31 008 anzu-
wenden
5.8 
Linien
Für die allgemeine Anwendung der Linien gilt DIN ISO 128-24, Linien in Zeichnungen der mechanischen
Technik. Es sind die Liniengruppen 0,35 und 0,5 zu bevorzugen. Im Karosseriebau können schmalere
Vollinien zur Anwendung kommen.
5.9 
Schraffuren
Schraffurangaben siehe DIN ISO 128-50
5.10 Beschriftung und Schriftgrößen
Für die Beschriftung von CAD-Zeichnungen ist die Werknorm MBN 31 002 anzuwenden.
Die Werknorm MBN 31 002 gilt für die Beschriftungsangaben in deutscher als auch in einer anderen
Sprache.
5.11 Maßstäbe zum Darstellen der Teile und Abbildungsmaßstäbe von CAD-Zeichnungen
bei der Erzeugung
Die empfohlenen Maßstäbe zum Darstellen der Teile auf CAD-Zeichnungen und deren Angaben sind DIN
ISO 5455 zu entnehmen.
Der verwendete Abbildungs-Maßstab einer CAD-Zeichnung kann von den Maßstab-Werten für die Zeich-
nungserstellung nach DIN ISO 5455 abweichen.
Es gilt folgende Regelung für die Abbildung:
-  Maßstabbereich  1 bis 10           0,5 er Stufensprung        z.B.  3,5:1 oder  1:3,5
-  Maßstabbereich  10 bis 100       5,0 er Stufenbereich       z.B.  15:1 oder  1:15
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 10 页
MBN 31 001, 2004-05, Seite 10
Copyright DaimlerChrysler 2004
5.12 Maßeintragungen und Bemaßung in Schrägbildern auf CAD-Zeichnungen
- Maßeintragungen
Das Eintragen von Maßen in CAD-Zeichnungen ist gemäß den Festlegungen der Werknorm
MBN DIN 406 vorzunehmen. Für die Eintragung von Toleranzen für Längen- und Winkelmaße gilt die
Werknorm MBN DIN 406-12. Maßeintragungen der Maße und Toleranzen für Kegel siehe DIN ISO 3040.
- Bemaßung in Schrägbildern auf Zeichnungen
Da die Maßebene in aufgebrachten Schrägbildern in der Regel nicht die Zeichenebene ist, wird eine Be-
maßung in Schrägbildern in der Regel nicht zugelassen.
Als Ausnahme wird festgelegt:
1.  es sind nur Abstandsmaße in den Hauptachsen X, Y, Z zulässig
2.  Radien sind in den Hauptebenen XY, XZ, YZ zulässig
3.  das Hauptachsensystem ist auf der Zeichnung darzustellen.
5.13 Toleranzeintragungen
DIN ISO 1101 regelt die Zeichnungseintragungen von Form- und Lagetoleranzen. Normenangaben zu
Allgemeintoleranzen erscheinen im Zeichnungsschriftfeld.
5.14 Oberflächenbeschaffenheit
Die Rauheit der Werkstückoberflächen wird nach der Rauheitsmeßgröße Rz ( gemittelte Rauhtiefe ) an-
gegeben.
Freigegebene Oberflächen-Kennwerte und Angaben der Oberflächenbeschaffenheit (Rauheit, Rillenrich-
tung usw.) sind der Werknorm MBN 31 007-0 zu entnehmen.
Extern nicht definierte Kennwerte wie z.B. „Wirksamer Mikro-Profiltraganteil twi in %“ oder "Freilegetiefe
Rf" ,  oder „drallreduzierte dynamische Dichtflächen“ sind in weiteren Teilen der Werknorm MBN 31 007
festgelegt. Neu im Jahr 2002 ist die Kenngröße „dominante Welligkeit“ (VDA 2007) definiert worden und
zur Anwendung freigegeben worden.
Neueste Erfahrungen der Regeln und Verfahren zur Beurteilung der Oberflächenbeschaffenheit sowie
eine Angabe der Oberflächenbeschaffenheit in Zeichnungen werden in Normmitteilung Nr. 2002/01 er-
läutert und vorgeschrieben. Die Normmitteilung verweist auf die VDA-Empfehlungen VDA 2005 und VDA
2006.
5.15 Oberflächenschutzangaben
Oberflächenschutzangaben sind gemäß den Werk-Normen „DBL“ für den Endzustand der Oberflächen-
schutzbehandlung eines Teiles oder eines Zusammenbaus anzugeben. Für den Eintrag dieser Angaben
steht im Zeichnungsschriftfeld ein eigenes Datenfeld zur Verfügung
(siehe hierzu die Werknorm MBN 31 020-1, Schriftfelder für CAD-Zeichnungen).
Eine allgemein gültige Norm für galvanische und chemische Überzüge ist die DIN 50960-2.
5.16 Wärmebehandlungsangaben
Die für Wärmebehandlung (Härten, Härten, Anlassen, Vergüten, Randschichthärten, Einsatzhärten, Nitrie-
ren) erforderlichen Angaben und Darstellungen in CAD-Zeichnungen sind DIN 6773 zu entnehmen.
Verbindliche Ergänzungen oder Abweichungen zu DIN 6773 siehe Werknorm MBN 40 011.
5.17 Schweiß- und Lötangaben
Die symbolische Darstellung von Schweiß- und Lötnähten ist nach DIN EN 22553 auszuführen.
DIN EN 22553 entspricht der internationalen Norm ISO 2553.
Schweißen und verwandte Prozeße, Liste der Prozeße und Ordnungsnummern siehe DIN EN ISO 4063.
5.18 Gewinde und Gewindeeinsätze
Die Angaben von Gewinde und Gewindeeinsätzen sind in DIN ISO 6410 geregelt.
5.19 Werkstückkanten
Werkstückkanten in CAD-Zeichnungen sind nach DIN ISO 13715 anzugeben.
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 11 页
MBN 31 001, 2004-05, Seite 11
Copyright DaimlerChrysler 2004
5.20 Zentrierbohrungen
Werden Zentrierbohrungen in CAD-Zeichnungen dargestellt, so ist die DIN ISO 6411 anzuwenden.
5.21 Freistiche
Freistiche sind nach DIN 509 anzugeben.
5.22 Radien
Vorzugswerte für Radien sind DIN 250 zu entnehmen.
5.23 Wortangaben im Zeichnungsfeld einer CAD-Zeichnung
Werden Wortangaben ( sowohl Einzelangaben als auch Wortangaben im Satzverband ) in CAD-
Zeichnungen benötigt, so ist DIN 6790 anzuwenden.
5.24 Benennungsgebung für das Dargestellte in einer CAD-Zeichnung
Eine Benennung ist der Name für das Dargestellte in einer CAD-Zeichnung.
Sie dient der Verständigung und beschreibt das/die Teil/Baugruppe in der Regel nach seiner Funktion
oder nach seiner Bauform.
Für die Eingabe der Benennungen in CAD-Zeichnungen sind die jeweils federführenden Konstruktionsbe-
reiche zuständig. Die Informationsquelle einer Benennung ist generell der Rumpfsachnummernkatalog
(RSK) im System SRM. Festgelegt wird eine Benennung bei der Sachnummern-Vergabe für A/H-
Sachnummern durch die Nummernstellen.
Eine Benennung, ein sogenannter Benennungstext,  setzt sich aus folgenden Textbausteinen zusammen:
1 einem optionalen Benennungsvorsatz (Präfix)
2 einer zwingenden Hauptbenennung
3 einer optionalen Benennungsergänzung aus einem oder mehreren Zeichenketten
4 einem optionalen Text (freiformulierbar).
Textbaustein  1, beinhaltet die gebräuchlichsten Benennungsvorsätze mit ihren Definitionen siehe Anlage
A.
Textbaustein 2, beinhaltet Hauptbenennungen des Fahrzeugbaus. Sie beschreiben die Bauform
und/oder die Funktion des dargestellten Bauteils bzw. der Baugruppe.
Textbaustein 3 beinhaltet Abkürzungen von  Benennungsergänzungen, sowie funktionale Ergänzun-
gen,wie z.B. vst (vollständig), li (links), re (rechts), vo (vorn), hi (hinten) ob (oben), ut (unten) , Auslaß,
Einlaß, mechanisch, Oberteil, Rücklauf, Unterteil, Vorlauf.
Textbaustein 4 ist ein optionaler (freiformulierbarer) Text, er kann ergänzende technische Merkmale ent-
halten, Ortsangaben (wie zum Beispiel -unter Fondsitz-) sind nicht einzutragen; Muster hierzu siehe  An-
lage B.
Die Textbausteine 1 bis 3 werden  im Rumpfsachnummernkatalog (RSK) in deutscher Fassung , sowie in
übersetzter Form, bereitgestellt. Der Textbaustein 4, ein optionaler Text, wird zur Übersetzung systemsei-
tig nicht unterstützt.
Ein Bedarf an neuen Benennungen bei einer technischen Weiterentwicklung wird durch die Normung,
Team Sachstammdaten-Dokumentation, nach Vorschlag und in Abstimmung mit der Entwicklung, ent-
schieden. Die Normung hinterlegt diese neuen Benennungen im Rumpfsachnummernkatalog.
5.25 Einheiten, Formelzeichen, mathematische Zeichen und Begriffe
Einheitennamen und Zeichen DIN 1301
Formelzeichen DIN 1304
Mathematische Zeichen und Begriffe DIN 1302
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 12 页
MBN 31 001, 2004-05, Seite 12
Copyright DaimlerChrysler 2004
6 
Kennzeichnen von Teilen in CAD-Zeichnungen
6.1 
Kennzeichnung von  Erzeugnisteilen
Die Kennzeichnung der Erzeugnisteile ist nach Werknorm MBN 33 015 auszuführen.
6.2 
Kennzeichnung der Werkstoffe bei Bauteilen von Kraftfahrzeugen
Die Werknorm MBN 33 035 legt die Anwendung der VDA-Empfehlung 260, Bauteile von Kraftfahrzeugen,
Kennzeichnung der Werkstoffe, für Teile von Mercedes-Benz-Fahrzeugen fest.
6.3 
Kennzeichnung von Aufnahme-, Fixier-,Justierlöcher-/konturen-, Lackierungs- und
Schweißlöcher
Im Karosseriebereich Geschäftsfeld Pkw sind verschiedenartige Lochangaben auf CAD-Zeichnungen für
die Fertigung  erforderlich.
Neben dem Bedarf nach Fixier- und Aufnahmelöcher werden auch Lackierungslöcher für die Tauchlackie-
rung  erforderlich. Schweißlöcher, hierbei handelt es sich sowohl um Löcher, die bei der Verbindung von
zwei Teilen in einem Teil vorhanden sind und wieder zugeschweißt werden, als auch um Löcher, die zum
Durchführen der Schweißelektrode benützt werden.
Roboter-Einsatz beim Nahtabdichten oder Unterbodenschutz macht eine Justierstellen-Angabe erforder-
lich. Ausgewählte Löcher und/oder Konturen werden im Roboterprogramm als Nullpunkte deklariert.
Folgende Symbole (Kurzbezeichnungen) stehen zur Verfügung:
F = für Fixierloch oder Fixierkontur
A = für Aufnahmeloch
L = für Lackierungsloch
S = für Schweißloch
W = für Wachsloch
J = für Justierloch oder Justierkontur
Die Kennzeichnung auf der CAD-Zeichnung erfolgt mit dem Symbol an einer Hinweislinie auf das ent-
sprechende Loch.
Bei Löchern mit mehreren Funktionen können auch 2 oder 3 Symbole erscheinen.
Eine Erläuterung der in der Zeichnung verwendeten Symbole erfolgt in der Nähe des Zeichnungs-
Schriftfeldes.
Die Bereitstellung der Loch-Symbole erfolgt im CAD-System "CATIA"
-
für CATIA Version V4 in der Detail-Library "CATIA.MB#ZNORM.P#019.MAP“ in der Family
„Kanten- und Lochsymbole"
-
für CATIA Version V5 im Katalog „#Catalog.MBZNORM_19“ unter
„KANTEN-UND LOCHSYMBOLE.catalog, Untergruppe Funktion-Loecher“.
6.4 
Kennzeichnung dokumentationspflichtiger Teile in CAD-Zeichnungen
Anmerkung: Der Anwendungsbereich gilt nur für  die Mercedes Car Group.
Die Dokumentationspflicht mit den zwei Nachweisführungen Sicherheitsrelevanz und Zertifizierungsrele-
vanz ist nach der Werknorm MBN 10 317 verbindlich auszuführen.
6.5 
Signatur-Kenner  für elektronische Kraftfahrzeug-Steuergeräte in CAD-Zeichnungen
Die Signierpflicht wird in drei möglichen Nachweisführungen vorgeschrieben. Sie ist gemäß der Werknorm
MBN 10 320 verbindlich auszuführen.
Ende des Hauptdokuments
# # # # #
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 13 页
MBN 31 001, 2004-05, Seite 13
Copyright DaimlerChrysler 2004
Anhang A (informativ)
Die gebräuchlichsten Benennungsvorsätze (Textbaustein 1) mit ihren
Definitionen eines Benennungstextes
Die gebräuchlichsten Benennungvorsätze sind in der Regel zweistellige Abkürzungen, sie sind firmenspe-
zifisch und deshalb nicht übersetzt.
Anordnung -AO-
Zeichnung, die konstruktionsgruppen-übergreifende Zusammenhänge oder die Lage von Teilen zueinan-
der aufzeigt (z. B. Auspuffanlage). Sie wird als Kontrollzeichnung verwendet und enthält nur die hierfür
notwendigen Zusammenhangmaße (also keine vollständige Bemaßung) und evtl. Prüf- und Einstellvor-
schriften.
Sie dient nicht der Darstellung von Einzelteilen und Zusammenbauten (diese sind auf eigenen Zeichnun-
gen dargestellt und bemaßt).
Dichtungssatz ‘DS’ (für ET)
Dichtungen, die nach einer Demontage eines oder mehrerer Teile erneuert werden müssen, sind in einem
Dichtungssatz zusammengefasst. Dichtungssätze enthalten generell nur Abdichtelemente.
Einbau ‘EB’
Durch eine Einbauzeichnung wird die Montage bzw. das Einbauen von Einzelteilen und/oder Zusammen-
bauten in einem übergeordneten Zusammenbau dargestellt, ohne dass dieser übergeordnete Zusam-
menbau mit einer neuen bzw. eigenen ZB-Nummer dokumentiert wird. Voraussetzung für einen Einbau
ist, daß
� 
keine Zwischenlagerung
� 
keine ET-Bevorratung
� 
kein Auswärtsbezug
der durch ihn veränderten Zusammenbau/Einzelteile notwendig ist, da im allgemeinen mit gleicher Sach-
nummer keine unterschiedlichen Teile identifiziert werden können. Im Gegensatz zum Zusammenbau ist
der Einbau außerhalb des Endobjektes keine eigenständige physische Einheit.
Lieferumfang ‘LU’
Ein Lieferumfang (LU) ist ein Zusammenbau (ZB), der nicht vollständig strukturiert ist oder eine lose Zu-
sammenstellung von Teilen.
Nacharbeit ‘NA’
Aufarbeiten eines Teiles auf den neuesten Zeichnungsstand, wenn es einem überholten Zeichnungsstand
entspricht, bzw. Aufarbeiten eines Ausschussteiles auf einen wieder verwendbaren Zustand.
Reparatursatz ‘RS’ (für ET)
Der Inhalt von Reparatursätzen besteht aus Teilen, die für die Reparatur eines "ZB" bzw. einer Baugruppe
erforderlich sind.
Tabelle ‘TB’
Zeichnung, auf der mehrere gleichartige Einzelteile, Anordnungen, Einbauten und Zusammenbauten, die
nur in wenigen Maßen voneinander abweichen, aufgeführt sein können. Tabellen sind reine Hinweis-
zeichnungen, wobei die Benennung einer Tabelle stets als ersten Bestandteil das Wort ‘Tabelle’ oder ‘TB’
enthält.
Die Sachnummer der Tabelle kann nicht gleichzeitig für eines der darauf befindlichen Teile verwendet
werden. Sie muss für alle diejenigen Verwendungen freigegeben werden, für welche die in der Tabelle
aufgeführten einzelnen Sachnummern gültig sind. Im allgemeinen gilt der Gesamtumfang einer Tabelle (z.
B. als Zeichnung mit mehrern Blättern) für alle aufgeführten Teile. Abweichungen davon sind besonders
zu kennzeichnen (z. B. Blatt 3 gilt nur für Teil X).
Teilesatz ‘TS’ (für ET)
Ein Teilesatz besteht aus dem Hauptteil plus Kleinteile oder aus mehreren gleichen Hauptteilen.
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 14 页
MBN 31 001, 2004-05, Seite 14
Copyright DaimlerChrysler 2004
Umarbeit ‘UA’
Durch zusätzliche Arbeitsgänge wird aus einem Fertigteil (Mutterteil) ein anderes Fertigteil (Tochterteil)
hergestellt. Eine Umarbeit wird nur von der Produktion festgelegt. Hinweismöglichkeit durch die Konstruk-
tion im Datenfeld ‘Referenznummer’ der Zeichnung.
Zusammenbau ‘ZB’
Ein Zusammenbau besteht aus mindestens 2 Teilen (Einzelteile und/oder Unterzusammenbau und stellt
einen abgeschlossenen Fertigungs-/Montageumfang dar, für den eine eigene Teilnummer festgelegt wird
(dargestellt in der Regel in der Stückliste mittels Strukturstufen).
Zusatzarbeit ‘ZS’
Mit einer ‘Zusatzarbeit werden gleiche zusätzliche Arbeitsgänge an unterschiedlichen Einzelteilen bzw.
Zusammenbauten dargestellt, ohne dass die durch Zusatzarbeit veränderten Einzelteile bzw. Zusammen-
bauten mit einer neuen Sachnummer identifiziert werden. Voraussetzung für eine Zusatzarbeit ist, daß
� 
keine Zwischenlagerung
� 
keine ET-Bevorratung
� 
kein Auswärtsbezug
der durch sie veränderten Zusammenbauten/Einzelteile erfolgt. Dies ist erforderlich, da im allgemeinen mit
gleicher Sachnummer keine unterschiedlichen Teile identifiziert werden können. Die Abwicklung mit Zu-
satzarbeit erspart somit das Festlegen von Zeichnungen und Sachnummern für neue unterschiedliche
Ende von Anhang A
# # # # #
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)


### 第 15 页
MBN 31 001, 2004-05, Seite 15
Copyright DaimlerChrysler 2004
Anhang B (informativ)
Textbaustein 4 des Benennungstextes; Muster ergänzender techni-
scher Merkmale zu Textbaustein 2
Muster 1:
Hinterachsübersetzung als ergänzendes technisches Merkmal zu der Bennung -ZB Hinterachse-
i = 2,65 RG 107/198 ABS
Muster 2:
Rad- und Reifengröße zu der Benennung -ZB Rad mit Reifen-
8Jx19 H2 275/50 ZR 19   Reinforced
Muster 3:
Übersetzungsverhältnis / Zähnezahl zu der Benennung -Antriebskegelrad-
i = 3,89 Z = 35
Ende von Anhang B
# # # # #
Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-22)

