# MBN_LV_124-2_EN_2013-08_3.5t及以下乘用车电气电子零部件环境测试标准.pdf

## 文档信息
- 标题：Microsoft Word - Deckblatt MBN_LV_124-2_2013-04 - deutsch_ENU.docx
- 作者：mareime
- 页数：112

## 文档内容
### 第 1 页
Mercedes-Benz 
MBN LV 124-2 
Company Standard 
Date published:  2013-08
 
Supersedes: MBN LV 124-2: 2009-11
 
Total no. of pages: 1 and 110 pages excerpt from LV 124 
 
Person in charge: Dr. <PERSON><PERSON>to
 
Plant: 059; Dept.: RD/EDF
Date of translation: 2014-03 
Phone: +49 7031 4389 413
 
NOTE: This translation is for information purposes only. 
The German version shall prevail above all others. 
 
 
Copyright Daimler AG 2013 
Electric and Electronic Components in Motor  
Vehicles up to 3,5t – General Requirements, 
Test Conditions and Tests 
Part 2: Environmental Requirements 
Foreword 
This edition of this Standard is based on the document LV 124 which has been established by rep-
resentatives of the automotive manufacturers AUDI AG, BMW AG, Daimler AG, Porsche AG and 
Volkswagen Aktiengesellschaft within Working Group 4.9 "Process Validation Hardware Quality of 
Electronic Components by Suppliers". 
Deviations from LV 124 are listed in the cover sheet of this Standard. 
If in individual cases modifications to individual test sections are required, such modifications shall 
be agreed separately between the departments responsible of the automotive manufacturer and the 
supplier. 
 
The contents of LV 124, Version 2.2, Edition 2013-02-28, have been adopted unchanged, but divid-
ed into two parts in the set of standards of Mercedes-Benz with the exception of the test parameters 
in test E-05 in accordance with the following Table: 
 
MBN standard number 
LV number 
Content 
Pages of 
LV 124 
MBN LV 124-1 
LV 124 
Part 1 – Electrical Requirements 
and Tests – 12 V On-Board Electri-
cal System 
2-3; 6-54; 160 
MBN LV 124-2 
LV 124 
Part 2 – Environmental Require-
ments  
2-5; 55-160 
 
 
General requirements: 
For safety requirements, homologation (in particular, exhaust emissions) and quality, the existing 
statutory requirements and laws shall be complied with. In addition, the relevant requirements of the 
Daimler Group apply. 
All materials, procedures, processes, components, and systems shall conform to the current regula-
tory (governmental) requirements regarding regulated substances and recyclability. 
 
Deviations from LV 124: 
With regard to this edition of Standard MBN LV 124-2 of Daimler AG, for test "Damp heat, steady 
state", severity 2 according to Section 14.14.2 shall be applied; the application of test "Damp heat, 
steady state", severity 1 according to Section 14.14.1 is not permissible. 
 
Changes 
In comparison with edition MBN LV 124-2: 2009-11, the following changes have been made: 
- 
Cover sheet updated 
- 
For other changes, refer to Section "Change history" of LV 124 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 2 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 2 of 160
 
Change history 
 
 
 
 
 
 
Edition 
 
2013-02 
Editorial changes integrated.
 
Part I: Electrical Requirements and Tests 12 V On-Board Electrical System: 
Complete revision - each test adjusted to latest requirements. 
 
Part II – Environmental Requirements and Tests:
Extension to components which are described in several operating modes, 
components connected to coolant circuits, and revision of life tests.  
 
 
 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 3 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 3 of 160
 
 
Contents 
Part I: Electrical Requirements and Tests 12 V On-Board Electrical System ........... 6�
1�
Scope....................................................................................................................... 6�
2�
Normative references ............................................................................................. 6�
3�
Terms and definitions ............................................................................................ 6�
3.1�
Terms and abbreviations ....................................................................................... 6�
3.2�
Voltages and currents ............................................................................................ 7�
3.3�
Temperatures ........................................................................................................ 7�
3.4�
Times/durations ..................................................................................................... 8�
3.5�
Internal resistance, terminal designations, frequency ............................................ 8�
4�
General requirements ............................................................................................ 8�
4.1�
Voltages and currents ............................................................................................ 8�
4.2�
Temperatures ........................................................................................................ 9�
4.3�
Standard tolerances ............................................................................................... 9�
4.4�
Standard values ..................................................................................................... 9�
4.5�
Sampling rates and measured value resolutions ................................................... 9�
4.6�
Test voltages ......................................................................................................... 9�
4.7�
Operating voltage ranges and coding .................................................................. 10�
4.8�
Functional status classification ............................................................................ 10�
4.9�
Operating modes ................................................................................................. 12�
4.10� Interface description ............................................................................................ 13�
4.11� Procedural limitations .......................................................................................... 13�
4.12� Electrical tests ...................................................................................................... 14�
5�
Test selection table .............................................................................................. 16�
6�
Electrical tests and requirements ....................................................................... 18�
6.1�
E-01 Long-term overvoltage ................................................................................ 18�
6.2�
E-02 Transient overvoltage .................................................................................. 19�
6.3�
E-03 Transient undervoltage................................................................................ 21�
6.4�
E-04 Jump start ................................................................................................... 22�
6.5�
E-05 Load dump .................................................................................................. 23�
6.6�
E-06 Superimposed alternating voltage ............................................................... 24�
6.7�
E-07 Slow decrease and increase of the supply voltage ..................................... 26�
6.8�
E-08 Slow decrease, abrupt increase of the supply voltage ................................ 28�
6.9�
E-09 Reset behavior ............................................................................................ 30�
6.10� E-10 Short interruptions ....................................................................................... 32�
6.11� E-11 Start pulses ................................................................................................. 35�
6.12� E-12 Voltage profile for on-board electrical system control .................................. 39�
6.13� E-13 Pin interruption ............................................................................................ 40�
6.14� E-14 Connector interruption ................................................................................. 42�
6.15� E-15 Reverse polarity .......................................................................................... 43�
6.16� E-16 Ground offset .............................................................................................. 46�
6.17� E-17 Short circuit in signal circuit and load circuits .............................................. 47�
6.18� E-18 Insulation resistance ................................................................................... 49�
6.19� E-19 Closed-circuit current .................................................................................. 50�
6.20� E-20 Dielectric strength ....................................................................................... 51�
6.21� E-21 Backfeeds ................................................................................................... 52�
6.22� E-22 Overcurrents ............................................................................................... 54�
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 4 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 4 of 160
 
Part II – Environmental Requirements and Tests ..................................................... 55�
7�
Scope..................................................................................................................... 55�
8�
Normative references........................................................................................... 55�
9�
Terms and definitions .......................................................................................... 56�
9.1�
Terms and abbreviations ..................................................................................... 56�
9.2�
Voltages ............................................................................................................... 57�
9.3�
Temperatures ...................................................................................................... 57�
9.4�
Times/durations ................................................................................................... 57�
9.5�
Standard tolerances ............................................................................................. 57�
9.6�
Standard values ................................................................................................... 58�
10�
General ............................................................................................................... 58�
10.1� Working conditions .............................................................................................. 58�
10.2� Operating modes ................................................................................................. 59�
10.3� Attainment of complete thermal equilibrium ......................................................... 61�
10.4� Parameter test ..................................................................................................... 62�
10.5� Continuous parameter monitoring with drift analysis ........................................... 63�
10.6� Leakage test ........................................................................................................ 64�
10.7� Sampling rates and measured value resolutions ................................................. 64�
11�
Mission profile ................................................................................................... 65�
11.1� Service life specification ...................................................................................... 65�
11.2� Temperature distribution profiles ......................................................................... 65�
12�
Test selection ..................................................................................................... 67�
12.1� Test selection table .............................................................................................. 67�
12.2� Test sequence ..................................................................................................... 69�
13�
Mechanical requirements and tests ................................................................. 70�
13.1� M-01 Free fall ....................................................................................................... 70�
13.2� M-02 Stone chip test ............................................................................................ 71�
13.3� M-03 Dust test ..................................................................................................... 72�
13.4� M-04 Vibration test ............................................................................................... 74�
13.5� M-05 Mechanical shock ....................................................................................... 85�
13.6� M-06 Mechanical shock endurance ..................................................................... 86�
14�
Climatic requirements and tests ...................................................................... 87�
14.1� K-01 High/low temperature storage ..................................................................... 87�
14.2� K-02 Temperature step test ................................................................................. 88�
14.3� K-03 Low temperature operation ......................................................................... 89�
14.4� K-04 Repainting temperature ............................................................................... 90�
14.5� K-05 Thermal shock (component) ........................................................................ 91�
14.6� K-06 Salt spray test, operating, exterior ............................................................... 92�
14.7� K-07 Salt spray test, operating, interior ................................................................ 94�
14.8� K-08 Damp heat, cyclic ........................................................................................ 96�
14.9� K-09 Damp heat, cyclic (with frost) ...................................................................... 97�
14.10�K-10 Protection against water – IPX0 to IPX6K ................................................... 98�
14.11�K-11 High-pressure/steam-jet cleaning ................................................................ 99�
14.12�K-12 Thermal shock with splash water .............................................................. 100�
14.13�K-13 Thermal shock immersion ......................................................................... 103�
14.14�K-14 Damp heat, steady state ........................................................................... 104�
14.15�K-15 Condensation and climate test .................................................................. 108�
14.16�K-16 Thermal shock (without housing) .............................................................. 115�
14.17�K-17 Solar radiation ........................................................................................... 116�
14.18�K-18 Corrosion test with flow of mixed gas ........................................................ 117�
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 5 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 5 of 160
 
15�
Chemical requirements and tests .................................................................. 118�
15.1� C-01 Chemical tests .......................................................................................... 118�
16�
Life tests ........................................................................................................... 121�
16.1� L-01 Life test: mechanical/hydraulic endurance test .......................................... 121�
16.2� L-02 Life test: high-temperature endurance test ................................................ 122�
16.3� L-03 Life test: temperature cycle endurance test ............................................... 125�
Annex A (normative) Test sequence ....................................................................... 129�
A.1�
Test sequence plan ........................................................................................... 129�
A.2�
Sequential tests ................................................................................................. 130�
A.3�
Tests outside the sequence (parallel tests) ....................................................... 132�
A.4�
Life tests ............................................................................................................ 133�
Annex B (normative)  Typical temperature distribution profiles for different 
installation areas ....................................................................................................... 134�
B.1�
Temperature distribution profile 1 ...................................................................... 135�
B.2�
Temperature distribution profile 2 ...................................................................... 135�
B.3�
Temperature distribution profile 3 ...................................................................... 135�
B.4�
Temperature distribution profile 4 ...................................................................... 135�
Annex C (normative) Calculation models for the life test 'High-temperature 
endurance test' .......................................................................................................... 136�
C.1�
Arrhenius model ................................................................................................. 136�
C.2�
Example Arrhenius model: ................................................................................. 137�
C.3�
Arrhenius model for use with components with reduced performance at high 
temperatures ............................................................................................................... 138�
C.4�
Example Arrhenius model for use with components with reduced  
 
performance at high temperatures: .................................................................... 139�
C.5�
Arrhenius model for use with components connected to coolant circuits ........... 141�
C.6�
Example Arrhenius model for use with components connected to  
 
coolant circuits ................................................................................................... 145�
Annex D (normative)  Calculation models for the life test 'Temperature cycle 
endurance test' .......................................................................................................... 148�
D.1�
Coffin-Manson model ......................................................................................... 148�
D.2�
Example: ............................................................................................................ 149�
D.3�
Coffin-Manson model for use with components connected to coolant circuits ... 150�
D.4�
Example Coffin-Manson model for use with components connected to  
 
coolant circuits ................................................................................................... 153�
Annex E (normative)  Calculation models for test  'Damp heat, steady state - 
severity 2' ................................................................................................................... 155�
E.1�
Lawson model .................................................................................................... 155�
E.2�
Example: ............................................................................................................ 156�
Annex F (informative)  Condensation test, chamber programming and  
diagrams..................................................................................................................... 157�
Annex G (informative)  Examples of examination methods for physical  
analysis ...................................................................................................................... 160�
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 6 页
MBN LV 124-2 2013-08 
 
 
 
Copyright Daimler AG 2013 
 
 
 
 
 
Pages 6 to 54 of LV 124 contain Part I of LV 124 
which has been adopted into the Mercedes-Benz set of standards as MBN 
LV 124-1 (also refer to cover sheet of MBN LV 124-2) 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 7 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 55 of 160
 
Part II – Environmental Requirements and Tests 
7 Scope 
This document specifies requirements, test conditions and tests for electric, 
electronic and mechatronic components and systems for use in motor vehicles up to 
3,5 t. 
 
Any additional or deviating requirements, test conditions and tests shall be defined in 
the respective Component Requirement Specifications. 
 
Note: The represented tests are not intended for component qualification or a 
qualification of the manufacturing process. 
8 Normative references 
Table 38: Normative references 
DIN 75220 
Ageing of Automotive Components in Solar Simulation Units 
DIN EN 13018 
Non-Destructive Testing – Visual Testing – General Principles 
DIN EN 60068-2-1 
Environmental Testing - Part 2-1: Tests – Test A: Cold 
DIN EN 60068-2-2 
Environmental Testing - Part 2-2: Tests – Test B: Dry Heat 
DIN EN 60068-2-6 
Environmental Testing – Part 2-6: Tests – Test Fc: Vibration 
(Sinusoidal) 
DIN EN 60068-2-11 
Environmental Testing - Part 2: Tests – Test Ka: Salt Mist 
DIN EN 60068-2-14 
Environmental Testing - Part 2: Tests – Test N: Change of 
Temperature 
DIN EN 60068-2-27 
Environmental Testing – Part 2-27: Tests – Test Ea and 
Guidance: Shock 
DIN EN 60068-2-29 
Environmental Testing - Part 2: Tests – Test Eb and Guidance: 
Bump 
DIN EN 60068-2-30 
Environmental Testing - Part 2-30: Tests – Test Db: Damp Heat, 
Cyclic (12 + 12 Hours) 
DIN EN 60068-2-38 
Environmental Testing – Part 2: Tests –– Test Z/AD:  Composite 
Temperature/Humidity Cyclic Test 
DIN EN 60068-2-60 
Environmental Testing – Part 2: Tests –– Test Ke:  Flowing Mixed 
Gas Corrosion Test 
DIN EN 60068-2-64 
Environmental Testing – Part 2: Tests –– Test Fh: Vibration, 
Broadband Random (Digital Control) and Guidance 
DIN EN 60068-2-78 
Environmental Testing – Part 2-78: Tests –– Test Cab:  Damp 
Heat, Steady State 
DIN EN ISO 11124 
- 2 
Preparation of Steel Substrates before Application of Paints and 
Related Products - Specifications for Metallic Blast-Cleaning 
Abrasives - Part 2: Chilled-Iron Grit 
DIN EN ISO 20567-
1 
Paints and Varnishes – Determination of Stone-Chip Resistance 
of Coatings – Part 1: Multi-Impact Testing 
DIN EN ISO 6270-2 
Paints and Varnishes – Determination of Resistance to Humidity – 
Part 2: Procedure for Exposing Test Specimens in Condensation-
Water Atmospheres 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 8 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 56 of 160
 
ISO 12103-1 
Road Vehicles — Test Dust for Filter Evaluation — Part 1: 
Arizona Test Dust 
ISO 16750-3  
  
Road Vehicles – Environmental Conditions and Testing for 
Electrical and Electronic Equipment – Part 3: Mechanical Loads 
ISO 16750 - 5 
Road Vehicles – Environmental Conditions and Testing for 
Electrical and Electronic Equipment – Part 5: Chemical Loads 
ISO 20653 
Road Vehicles — Degrees of Protection (IP-Code) — Protection 
of Electrical Equipment against Foreign Objects, Water and 
Access. 
 
9 Terms and definitions 
9.1 Terms and abbreviations 
Table 39: Abbreviations environmental requirements and tests 
Electronic assembly
Circuit board (without housing) with mounted electronic 
devices 
Modules/devices 
Electric, electronic or mechatronic device 
(e.g. resistor, capacitor, transistor, IC, relay) 
DUT 
Device Under Test – the system or component to be tested 
Functions 
Comprises system-specific functions and diagnostic functions 
Hardware freeze 
The point during development at which a modification of the 
hardware is not possible any more. 
ICT 
In Circuit Test 
Climatic chamber 
with condensation 
option 
A specially controlled water bath in the climatic chamber which 
converts the required water quantity into water vapor. 
The intensity of the condensation film on the PCB depends on 
the thermal mass, the relative humidity and the temperature 
gradient of the water bath. 
The climate control of the climatic chamber is switched off 
during the condensation phase. The test room temperature is 
controlled by means of the temperature-controlled water bath. 
Component 
Complete unit, control unit or mechatronic system (with 
housing) 
Test piece 
The system or component to be tested (device under test). 
PTB 
Physikalisch-Technische Bundesanstalt (German national 
metrology institute providing scientific and technical services) 
PSD 
Power Spectral Density 
Circuit board 
Unmounted interconnect device (PCB, ceramic, leadframe, 
flexband etc. without mounted parts) 
System 
Functionally linked components, e.g. brake control system 
(control unit, hydraulic, sensors) 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 9 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 57 of 160
 
9.2 Voltages 
Table 40: Abbreviations for voltage definitions 
UBmin 
Lower operating voltage limit 
UB 
Operating voltage 
UBmax 
Upper operating voltage limit 
9.2.1 Voltages for components with extended requirements 
 
UBmin,HV 
Lower operating voltage limit HV – lower DC operating voltage 
UB,HV 
Operating voltage HV – DC operating voltage 
UBmax,HV 
Upper operating voltage limit HV - upper DC operating voltage 
9.3 Temperatures 
Table 41: Temperature definitions 
Tmin 
Minimum operating temperature 
TRT 
Room temperature 
Tmax 
Maximum operating temperature 
Top,min 
Minimum operating temperature for components with overload 
protection/low-temperature protection 
Top, max 
Maximum operating temperature for components with overload 
protection/over-temperature protection 
TTest 
Test temperature 
9.3.1 Temperatures for components connected to coolant circuits 
Tcool,nom 
Nominal cooling temperature coolant circuit 
Tcool,min 
Minimum cooling temperature coolant circuit 
Tcool, max 
Maximum cooling temperature coolant circuit 
9.4 Times/durations 
Table 42: Time definitions 
ttest 
Test duration 
top 
Operating hours over service life 
9.5 Standard tolerances 
Unless otherwise indicated, the tolerances according to Table 43 apply. 
The tolerances refer to the required measured value. 
Table 43: Standard tolerances 
Frequencies 
± 1 % 
Temperatures 
± 2 °C 
Humidity 
± 5 % 
Times/durations 
+ 5 %; 0 % 
Voltages 
± 2 % 
Currents 
± 2 % 
Vibrations 
± 3 dB 
Vibration PSD 
± 5 % 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 10 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 58 of 160
 
9.6 Standard values 
Unless otherwise indicated, the standard values according to Table 44 apply.
Table 44: Standard values 
Room temperature 
TRT = 23 °C ± 5 °C  
Humidity 
Frel = 25 % to 75 % relative humidity  
Test temperature 
Ttest = TRT 
Operating voltage 
(for test) 
UB = 14 V 
10 General 
10.1 Working conditions 
For vehicles with pure combustion-engined drive the operating status of the vehicle 
during its service life can normally be divided in the following two working conditions: 
� Driving 
� Parking 
 
For vehicles with alternative drives additional working conditions shall be considered 
if necessary (see Table 45). 
 
For components with several relevant working conditions (see Figure 22), the 
operating modes (see Section 10.2) shall be specified for every working condition if 
necessary. 
 
Table 45: Description General 
Working conditions 
Working condition 
Vehicle parked 
Charging cable 
plugged in 
Charging 
drive battery 
Powerline 
communication 
active (if 
existing)
Driving 
no 
no 
yes/no 
no 
Charging 
yes 
yes 
yes 
yes 
Preconditioning 
yes 
yes/no 
yes/no 
yes/no 
ON-grid parking 
yes 
yes 
no 
yes 
OFF-grid parking or 
parking 
yes 
no 
no 
no 
 
All relevant working conditions for the component according to (Figure 22) shall be 
considered to derive the testing requirements.  
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 11 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 59 of 160
 
 
 
Figure 22: Classification of load spectrum according to working conditions 
 
 
10.2 Operating modes 
10.2.1 
General 
The electric, electronic and mechatronic components and systems will be operated in 
different operating modes during their service life, which shall be simulated 
correspondingly during the tests. Details concerning the operating modes, operating 
loads (e.g. actuation, bus activity, bus messages, original sensors, original actuators 
or replacement circuitry) and the necessary boundary conditions shall be coordinated 
between the buyer and supplier and documented.  
10.2.2 
Operating mode I - DUT not electrically connected 
10.2.2.1 Operating mode I.a 
The DUT is without power; connector and harness are not connected. 
Any existing coolant circuit is unfilled, and the connections are sealed. 
10.2.2.2 Operating mode I.b 
The DUT is without power; but the connector and harness are connected. 
Any existing coolant circuit is filled, and the coolant hoses are connected. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 12 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 60 of 160
 
10.2.3 
Operating mode II – DUT electrically connected 
10.2.3.1 Operating mode II.a 
The DUT shall be operated without operating load. 
Any existing coolant circuit shall be filled, and the coolant hoses shall be 
connected. If required, the flow rate and temperature of the coolant shall be 
adjusted - as specified in the Component Requirement Specifications. 
10.2.3.2 Operating mode II.b 
The DUT shall be operated with minimal operating load. 
The DUT shall be operated such that minimal self-heating occurs (e.g. by 
means of a reduction of continuous output power or through infrequent 
activation of external loads). 
Any existing coolant circuit shall be filled, and the coolant hoses shall be 
connected. If required, the flow rate and temperature of the coolant shall be 
adjusted - as specified in the Component Requirement Specifications. 
10.2.3.3 Operating mode II.c 
The DUT shall be operated with maximum operating load (power user, but no 
misuse). 
The DUT shall be operated such that maximum self-heating occurs (for 
example by means of a realistic maximization of a continuous output power or 
frequent activation of external loads). 
Any existing coolant circuit shall be filled, and the coolant hoses shall be 
connected. If required, the flow rate and temperature of the coolant shall be 
adjusted - as specified in the Component Requirement Specifications. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 13 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 61 of 160
 
 
10.2.3.4 Examples of operating modes 
Table 46: Examples of operating modes 
Example of 
component 
Operating 
mode II.a 
Operating mode II.b 
Operating mode II.c 
Car radio with 
navigation 
Component 
as in parked 
vehicle 
(sleep). 
Follow-on 
current 
stopped 
Terminal 30 
"ON" 
Component in 
running vehicle. 
Component 
switched off by 
driver, BUS/�C’s 
active, Terminal 15 
"ON" 
Component in running 
vehicle. Component 
switched on (CD, 
navigation system, 
output stage), 
BUS/navigation 
computer active 
Anti-theft alarm 
system 
No operation 
when vehicle 
is running 
Vehicle interior is monitored while vehicle is 
parked 
Brake control 
system 
Component 
as in parked 
vehicle. 
Follow-on 
current 
stopped 
Driving without 
brake actuation 
Driving with frequent 
brake cycles (no 
misuse, such as 
uninterrupted brake 
control operation) 
On-board charger 
Off-grid 
parking 
or  
driving 
 
On-grid parking 
(power line 
communication 
only, no charging) 
Vehicle conditioning 
Charging 
HV battery (battery 
management 
system) 
Off-grid 
parking 
On-grid parking with 
power line 
communication 
Driving, charging 
 
10.3 Attainment of complete thermal equilibrium 
A component that is kept at a constant ambient temperature under defined operating 
conditions is deemed to have attained complete thermal equilibrium at that point in 
time at which the temperature will not change by more than ±3 °C at any point of the 
component over the further course of time. 
 
The time until complete thermal equilibrium is achieved shall be determined by the 
supplier through experiments and indicated in the test documentation. 
 
For temperature cycle tests, the DUTs, after having attained complete thermal 
equilibrium, shall additionally be kept at the specified temperature basic values for a 
defined time, so that stress in the component can turn into strain. This additional 
holding time shall be indicated for the respective tests. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 14 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 62 of 160
 
10.4 Parameter test  
A set of sensitive parameters, so-called key parameters, e.g. closed-circuit current 
consumption, operating currents, output voltages, contact resistances, input 
impedances, signal rates (rise/fall times) and bus specifications, shall be defined in 
the Component Requirement Specifications. These parameters shall be checked for 
their compliance with the specifications before the start and after the end of each 
test. 
For components connected to the coolant circuit parameter tests shall be carried out 
at TRT with Tcool,nom, at Tmax with Tcool,max and at Tmin with Tcool,min. 
If not specified otherwise in the Component Requirement Specifications, for 
components with HV supply the parameter tests shall be carried out at UBmin with 
UBmin,HV, at UB with UB,HV and at UBmax with UBmax,HV. 
 
10.4.1 
Parameter test (small)  
The key parameters shall be measured and the functional behavior of the 
components checked at TRT and UB.  For components with fault memory, the fault 
memory shall be read out. The components shall be checked for external 
damage/changes such as cracks, chipping/peeling, discoloration, deformation etc. by 
visual testing according to DIN EN 13018, without opening the DUT. 
Changes in the values of the key parameters, the functional behavior or the fault 
memory entries as well as irregularities found during the visual test shall be 
evaluated against the new condition with regard to the previous test exposures. 
 
All results shall be documented in the test report. 
 
10.4.2 
Parameter test (large) 
The key parameters shall be measured and the functional behavior of the 
components measured at temperatures Tmax, TRT and Tmin at each of the voltages 
UBmin, UB and UBmax. 
For components with fault memory, the content of the fault memory shall be read out. 
The components shall be checked for external damage/changes such as cracks, 
chipping/peeling, discoloration, deformation etc. by visual testing according to DIN 
EN 13018. 
 
Changes in the values of the key parameters, the functional behavior or the fault 
memory entries as well as irregularities found during the visual test shall be 
evaluated against the new condition with regard to the previous test exposures. 
 
All results shall be documented in the test report. 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 15 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 63 of 160
 
10.4.3 
Parameter test (function test)  
The key parameters shall be measured at one specified temperature and at each of 
the voltage levels UBmin, UB and UBmax. 
The basic functionalities of the components shall be measured. 
For components with fault memory, the content of the fault memory shall be read out. 
 
Changes in the values of the key parameters, the basic functionality of the 
component or the fault memory entries shall be evaluated with regard to the previous 
test loads. 
The results shall be documented in the test report. 
 
10.4.4 
Physical analysis 
For the physical analysis, the DUT shall be opened, and a visual test shall be 
performed according to DIN EN 13018. 
Additional analyses shall be agreed between the buyer and the supplier. 
Examples of examinations are given in Annex G. 
Changes of the component compared to the new condition shall be evaluated. 
If a DUT demonstrates irregularities, the additional analysis shall be agreed with the 
buyer, if appropriate by adding additional DUTs or using additional analytical 
methods. 
The results shall be documented and evaluated in the test report.  
 
10.5 Continuous parameter monitoring with drift analysis 
The key parameters to be monitored shall be recorded throughout the test.  
 
For components with fault memory, the fault memory shall be monitored continuously 
and the entries shall be documented. 
 
The data resulting from the continuous parameter monitoring shall be examined for 
trends and drifting to detect abnormalities, aging or malfunctions of the component. 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 16 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 64 of 160
 
10.6 Leakage test 
For components through which coolant flows, evidence of the leakage shall be 
provided by means of appropriate tests; the specific construction of the component 
and the specification of the coolant circuit shall be taken into account in this process. 
Unless specified otherwise in the Component Requirement Specifications, at least 
the following tests shall be carried out to provide evidence of leakage: 
 
� Pressure pulsation test with 100�000 pressure changes between minimum and 
maximum specified pressure of the coolant circuit at Tmax and Tcool,max and with 
50�000 pressure changes between minimum and maximum specified pressure 
of the coolant circuit at Tmin and Tcool,min. 
� Static leakage test at minimal, maximal and nominal specified pressure of the 
coolant circuit each at TRT with Tcool,nom, at Tmax with Tcool,max and at Tmin with 
Tcool,min. 
� Vacuum pressure test with test pressure less than 20 mbar at TRT, if the 
component is filled using a vacuum process. Unless specified otherwise in the 
Component Requirement Specifications, each pressure change from ambient 
pressure to test pressure and back shall occur in <5s. The holding time at test 
pressure shall be at least 30s. 
� Test of the coolant flow rate at minimum, maximum and nominal specified 
pressure of the coolant circuit each at TRT with Tcool,nom, at Tmax with Tcool,max 
and at Tmin with Tcool,min. 
Unless specified in the Component Requirement Specifications, details of the 
leakage test shall be agreed between the buyer and the supplier. 
 
The leakage test shall be carried out after the first and the last "parameter test 
(large)" during the sequential tests (A.2), tests outside the sequence (parallel tests) 
(A.3) and life tests (A.4). 
 
 
10.7 Sampling rates and measured value resolutions 
The sampling rate and bandwidth of the measuring system shall be adapted to the 
respective test. 
 
It shall be ensured that functionally relevant peaks (temporary positive/negative 
deviation) are detected and recorded. 
 
The resolution of the measured values shall be adapted to the respective test. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 17 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 65 of 160
 
11 Mission profile 
11.1 Service life specification 
Table 47 shows the typical parameters for the service life specification: 
Table 47: Service life specification 
Service life 
15 years 
Operating hours 
driving 
8�000 h 
Mileage 
300�000 km 
 
For vehicles with alternative drives additional working conditions shall be considered 
if necessary (see Table 45).  
 
The operating hours for the additional working conditions (see Table 45)  
� operating hours charging,  
� operating hours preconditioning,  
� operating hours ON-Grid parking 
shall be specified specific to each component in the Component Requirement 
Specifications. 
 
11.2 Temperature distribution profiles 
In order to describe the thermal stress comprehensively to which a component is 
exposed at its installation location in the vehicle, in addition to the specification of the 
minimum operating temperature Tmin and the maximum operating temperature Tmax, 
the distribution is required, which indicates for how long a component is exposed to 
the different temperatures between Tmin and Tmax. 
 
For vehicles with alternative drives, a distinction shall be drawn between the working 
conditions “driving”, “charging”, “preconditioning” and “ON-Grid parking” and the 
respective temperature distribution profiles shall be specified for both the ambient 
temperature and the coolant circuit temperature (see Figure 22). 
 
In principle, this temperature distribution is a continuous distribution, as the ambient 
temperature of the component may adopt any value between Tmin and Tmax. 
For the design of the component and for a simple calculation of the test times by 
means of the accelerated life-stress model by Arrhenius (see Annex C), this 
continuous distribution can be described sufficiently by means of several discrete 
temperature nodes Tfield, i. For each temperature node, the percentage pi of the 
operating time shall be indicated for which the component is exposed to the node 
temperature. 
The respective temperature distribution profile therefore has the following general 
form: 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 18 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 66 of 160
 
 
Table 48: Temperature distribution profile 
Temperature in °C 
Distribution 
Tfield.1 = Tmin 
p1 
Tfield.2 
p2 
… 
… 
Tfield.n = Tmax 
pn 
 
It is based mainly on field measurements and technical experiences. 
 
Typical temperature distribution profiles in driving mode for different installation 
locations are indicated in Annex B. 
The usability of these typical temperature distribution profiles for a specific 
component shall be verified e.g. by means of vehicle measurement, simulation or 
experience. In the case of deviations, the temperature distribution profile shall be 
adapted to the relevant component. 
For special installation locations and mounting situations (e.g. a location near a heat 
source), a component-specific temperature distribution profile shall always be 
defined. 
The valid temperature distribution profile shall be documented in the Component 
Requirement Specifications. 
In addition to the typical temperature distribution profiles, Annex B indicates typical 
values for an average temperature delta, which a component within a vehicle in 
driving mode may experience. 
For temperature distribution profiles defined or adopted on a component-specific 
basis, this value shall also be documented on a component-specific basis and in the 
Component Requirement Specification.  
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 19 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 67 of 160
 
12 Test selection 
12.1 Test selection table 
Table 49: Test selection table 
Test
Applicable to 
Required 
specifications 
M-01 Free fall 
All components. 
For components that will obviously be 
damaged in this test (e.g. glass bodies, highly 
sensitive sensors), this test may be omitted 
following discussions with the buyer. This shall 
be documented. 
 None 
M-02 Stone chip test 
Components mounted in areas that may be 
affected by stone impact. 
 None 
M-03 Dust test 
All components 
  
None 
 
Degree of protection 
IP6KX 
For components for which the ingress of dust 
is not allowed 
 
Degree of protection 
IP5KX 
For components for which the ingress of dust 
is allowed as long as functionality and safety 
are not impaired 
M-04 Vibration test 
All components 
None 
 
- as per vibration profile A For components mounted on the engine 
 
- as per vibration profile B For gear box mounted components 
 
- as per vibration profile C For components mounted on flexible plenum 
chamber but not rigidly attached 
 
- as per vibration profile D For components mounted on sprung masses 
(vehicle body) 
 
- as per vibration profile E For components mounted on unsprung 
masses (wheel, wheel suspension) 
M-05 Mechanical shock 
All components 
 None 
M-06 Mechanical shock 
endurance 
Components mounted in or on doors and 
hoods 
Number of shocks 
K-01 High/low temperature 
storage 
All components 
None 
K-02 Temperature step test 
All components 
None 
K-03 Low temperature operation All components 
None 
K-04 Repainting temperature 
Components mounted on the vehicle exterior, 
which could be exposed to increased 
temperatures during repainting. 
None 
K-05 Thermal shock 
(component) 
All components 
-   
Test method (Na 
or Nc), 
if Nc: test medium 
 
as per  
 
DIN EN 60068-2-14 Na 
(air-air) 
For components that are not permanently 
operated in a fluid 
 
 
as per  
 
DIN EN 60068-2-14 Nc 
 
(medium-medium) 
For components that are permanently operated 
in a fluid (IP X8) 
K-06 Salt spray test, operating, 
exterior 
Components mounted on the vehicle exterior, 
underbody or in the engine compartment 
None 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 20 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 68 of 160
 
Test
Applicable to 
Required 
specifications 
K-07 Salt spray test, operating, 
interior 
Components mounted in exposed positions in 
the interior (e.g. side pockets in the trunk, door 
wet space, spare wheel well) 
None 
K-08 Damp heat, cyclic  
All components 
None 
K-09 Damp heat, cyclic  
(with frost) 
All components 
None 
K-10 Water protection – IPX0 to 
IPX6K 
All components 
None 
 
- Degree of protection IPX0 
For components that do not require water 
protection 
 
- Degree of protection IPX1 
For components on which vertically falling 
drips must not cause any damage 
 
- Degree of protection IPX2 
For components with an inclination of up to 15° 
in installation position on which vertically falling 
drips must not cause any damage 
 
- Degree of protection IPX3 
For components on which spray water must 
not cause any damage 
 
- Degree of protection IPX4K For components on which splash water with 
increased pressure must not cause any 
damage 
 
- Degree of protection IPX5 
For components on which water jets must not 
cause any damage 
 
- Degree of protection IPX6K For components on which strong water jets 
with increased pressure must not cause any 
damage 
K-11 High-pressure/steam-jet 
cleaning 
Components that may be directly exposed to 
high-pressure/steam-jet cleaning or underbody 
cleaning 
None 
K-12 Thermal shock with splash 
water 
Components mounted on the vehicle exterior 
or in the engine compartment which are 
expected to be exposed to splash water (e.g. 
when driving through puddles)  
None 
K-13 Thermal shock immersion 
Components mounted below the fording line 
for which the temporary immersion into (salt) 
water is to be expected (e.g. when driving 
through waters) (IPX7) 
None 
K-14 Damp heat, steady state 
All components 
Severity 
K-15 Condensation and climate 
test 
The necessity of this test shall be evaluated 
specifically for each component. If required, 
the test shall be specified in the Requirement 
Specifications. 
 
If the test is listed in the Requirement 
Specifications, the test for components with 
waterproof housings may alternatively be 
carried out as test K-15 a Condensation test 
with electronic assemblies or as test K-15 b 
Climate test for components with waterproof 
housings; for components without waterproof 
housings the test shall be carried out as test K-
15 a Condensation test with electronic 
assemblies. 
None 
K-16 Thermal shock (without 
housing) 
Electronic assemblies of all components. 
 None 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 21 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 69 of 160
 
Test
Applicable to 
Required 
specifications 
K-17 Solar radiation 
Components exposed to direct solar radiation 
in installation location. 
 None 
K-18 Corrosion test with flow of 
mixed gas 
Components with switching contacts that are 
not gas tight  
 None 
C Chemical requirements and 
tests 
All components 
Chemicals 
Operating mode 
L-01 Life test: 
mechanical/hydraulic endurance 
test 
Components with mechanical/hydraulic 
actuation/operating cycles, e.g. brake 
actuation, seat adjustment cycles, 
switch/pushbutton actuations 
Number of 
actuation/operating 
cycles 
L-02 Life test: high-temperature 
endurance test 
All components 
Test duration 
L-03 Life test: temperature cycle 
endurance test 
All components 
Number of test 
cycles 
12.2 Test sequence 
A component-specific test sequence plan shall be defined in the Component 
Requirement Specification. 
 
A test sequence plan is contained in Annex A as basis for discussions relating to 
cooperation projects between several OEMs (e.g. Industrial Assembly (IBK)). 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 22 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 70 of 160
 
13 Mechanical requirements and tests 
13.1 M-01 Free fall 
13.1.1 
Purpose 
This test simulates the free fall of a component to the floor, as it may occur during the 
complete process chain until the intended mounting of the component. 
The test is intended to verify that a part that does not show any visible damage as a 
result of a fall and therefore is mounted in the vehicle, does not have any hidden 
damage or pre-damage, e.g. internal part displacement or cracks. 
13.1.2 
Test 
Table 50: Test parameters M-01 Free fall 
Operating mode of the DUT 
Operating mode I.a  
Drop height 
1 m 
Impact surface 
Concrete ground 
Test cycle 
For each of the 3 DUTs one drop in both directions 
of each dimensional axis (1st DUT: ±X,  
2nd DUT: ±Y, 3rd DUT: ±Z)  
Number of DUTs 
3 
13.1.3 
Requirement 
The DUTs shall be visually inspected with the naked eye and shaken to check for 
loose or rattling parts. 
 
- 
If the DUT is visibly damaged, all incidents of damage shall be documented in 
the test report.  
 
- 
If the DUT is not visibly damaged, it shall be fully functional after the test, and 
all the parameters shall meet the specifications. Verification is done by means 
of a parameter test (large) as per Section 10.4. Hidden damage is not 
permitted. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 23 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 71 of 160
 
13.2 M-02 Stone chip test 
13.2.1 
Purpose 
This test simulates the mechanical exposure of the component to grit impact. 
The test is intended to verify the resistance of the component to faults such as 
deformation and cracks. 
13.2.2 
Test 
The test shall be applied in analogy with DIN EN ISO 20567-1, test method B, with 
the following parameters: 
Table 51: Test parameters M-02 Stone chip test 
Operating mode of the DUT 
Operating mode I.b 
Mass of grit 
500 g 
Test pressure 
2 bar 
Blasting material 
Chilled-iron grit in accordance with DIN EN ISO 
11124-2, particle size 4 to 5 mm 
Test surface on DUT 
All surfaces that are freely accessible on the vehicle 
Impact angle 
54° to blasting direction 
Apparatus 
Multi-impact tester in accordance with 
DIN EN ISO 20567-1 
Number of cycles 
2 
Number of DUTs 
6 
13.2.3 
Requirement 
The DUT shall be fully functional before and after the test and all parameters shall 
meet the specifications. Verification is done by means of a parameter test (small) as 
per Section 10.4. 
 
In addition, the DUT shall be evaluated visually with the naked eye and shaken to 
check for loose or rattling parts. 
Changes/damage shall be documented in the test report and evaluated with the 
buyer. 
 
An evaluation according to the ratings given in DIN EN ISO 20567-1 is not required.
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 24 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 72 of 160
 
13.3 M-03 Dust test 
13.3.1 
Purpose 
This test simulates the dust load of the component during vehicle operation. 
The test is intended to verify the resistance of the component to electrical and 
mechanical faults. 
13.3.2 
Test 
This test shall be carried out in accordance with ISO-20653 with the following 
parameters: 
 
Table 52: Test parameters M-03 Dust test 
Operating 
mode of the 
DUT 
For electric/electronic components: Operating mode II.a 
 
For mechatronic components  
(e.g. for components with fan):  
Intermitting between operating mode II.c and operating mode II.a as 
per Figure 23. 
 
If several working conditions are relevant for the component in 
operating mode II.c (see Section 10) during the times in which 
operating mode II.c is required in this test, the component shall be 
operated in each relevant working condition in equal lengths of 
time. 
Test set-up 
Vertical flow according to ISO-20653:2006 Figure 1 
Degree of 
protection to 
be achieved 
As specified in the Component Requirement Specification 
Test duration 
20 cycles of 20 minutes each 
Number of 
DUTs 
6 
 
 
Figure 23: Test sequence M-03 Dust test 
When performing the test, the installation position of the component in the vehicle 
shall be simulated. The test setup (installation position, covers, trim, situation during 
operation) shall be recommended by the supplier, coordinated with the buyer, and 
documented. 
13.3.3 
Requirement 
The degree of protection specified in the Component Requirement Specification in 
accordance with ISO 20653 shall be achieved. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 25 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 73 of 160
 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of a parameter test 
(small) as per Section 10.4. 
 
In addition, the DUT shall be evaluated visually with the naked eye and shaken to 
check for loose or rattling parts. 
Changes/damage shall be documented in the test report and evaluated with the 
buyer. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 26 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 74 of 160
 
13.4 M-04 Vibration test 
13.4.1 
Purpose 
These tests simulate the exposure of the component to vibrations during driving 
operation. 
The test is intended to verify the resistance of the component to faults such as 
component displacement or material fatigue. 
13.4.2 
Test 
This test shall be carried out in accordance with ISO 16750 Part 3.  
Execution of the test in accordance with DIN EN 60068-2-6 for sinusoidal excitation, 
and DIN EN 60068-2-64 for broadband excitation with the following parameters: 
 
Table 53: Test parameters for general vibration 
Operating mode of the DUT  
If the component is not operated with operating 
load in driving mode:  
II.a during the entire test 
 
If the component is operated with operating 
load in driving mode:  
Intermitting II.a and II.c during working 
condition driving (see Figure 24) 
Superimposed temperature profile 
Repeating profile as per Figure 24: 
Temperature profile - vibration and Table 54: 
Temperature profile - vibration 
Frequency sweep time for 
sinusoidal excitation 
1 octave/min, logarithmic 
Vibration profile A 
(Equipment mounted directly on 
the engine) 
Sinusoidal excitation as per Figure 25 and 
Table 55 
 
Broadband excitation 
as per Figure 26 and Table 56 
Vibration profile B  
(Gear box mounted equipment) 
Sinusoidal excitation as per  
Figure 27 and Table 57 
Broadband excitation as per  
Figure 28 and Table 58 
Vibration profile C 
(Equipment mounted on flexible 
plenum chamber but not rigidly 
attached) 
Sinusoidal excitation as per Figure 29 and 
Table 59 
Vibration profile D 
(Equipment mounted on sprung 
masses (vehicle body)) 
Broadband excitation 
as per Figure 30 and Table 60 
Vibration profile E 
Equipment mounted on unsprung 
masses (wheel, wheel suspension) 
Broadband excitation as per  
Figure 31 and Table 61 
Number of DUTs 
6 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 27 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 75 of 160
 
Components which are mounted on an e-machine shall be tested according to 
vibration profile D at least. However, this test profile does not include special vibration 
loads generated by an e-machine. In practice, however, these special vibration loads 
may occur and stress the component. Therefore the special vibration loads 
originating from an e-machine shall be included in the test. Measurements on the 
respective e-machine are necessary for that purpose. 
 
The test shall be carried out without brackets or attached parts. The mounting of 
connected lines (e.g. electrical wires, coolant hoses, hydraulic lines, …) in the test 
setup shall be defined in the Component Requirement Specifications. 
 
For components that are mounted on the bracket or vehicle through vibration 
isolators, the Component Requirement Specifications shall specify whether 
 
- 
all DUTs with vibration isolators, 
- 
all DUTs without vibration isolators, 
- 
three DUTs with vibration isolators and three DUTs without vibration isolators 
 
are required to be tested. 
 
The sampling rate shall be selected such that open circuits and short circuits will be 
detected with absolute certainty. 
 
Any additional tests for protection of the strength of the complete system that 
includes an assembly of component, brackets and attached parts shall be 
coordinated with the buyer. 
 
 
Figure 24: Temperature profile - vibration 
 
II.c from 135 min to 420 min
Temperature 
time [min]
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 28 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 76 of 160
 
Table 54: Temperature profile - vibration 
Time in min 
Temperature in °C 
0 
TRT 
60 
Tmin 
150 
Tmin 
300 
Tmax 
410 
Tmax 
480 
TRT 
 
For components attached to a coolant circuit the coolant temperature shall follow the 
relevant test temperature between Tcool,min and Tcool,max . Outside the coolant 
temperature limits, only the ambient temperature shall be varied. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 29 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 77 of 160
 
******** Vibration profile A (for equipment mounted directly on the engine) 
Table 55: Test parameters vibration, sinusoidal for equipment  
mounted directly on the engine  
Excitation 
Sinusoidal 
Test duration for each 
dimensional axis 
22 h 
Vibration profile 
Curve 1 for DUTs intended for mounting on engines 
with 5 cylinders or fewer 
Curve 2 for DUTs intended for mounting on engines 
with 6 or more cylinders 
 
Combine the curves for components that can be used 
in both cases. 
Curve 1 in Figure 25 
 
Frequency in Hz 
Amplitude of  
acceleration in m/s² 
100 
100 
200 
200 
240 
200 
270 
100 
440 
100 
Curve 2 in Figure 25 
Frequency in Hz 
Amplitude of  
acceleration in m/s² 
100 
100 
150 
150 
440 
150 
Combination  
Frequency in Hz 
Amplitude of  
acceleration in m/s² 
100 
100 
150 
150 
200 
200 
240 
200 
255 
150 
440 
150 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 30 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 78 of 160
 
 
 
Figure 25: Vibration profile, sinusoidal for equipment mounted directly on the engine 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 31 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 79 of 160
 
Table 56: Test parameters vibration, broadband random vibration for equipment mounted 
directly on the engine  
Excitation 
Broadband random vibration 
Test duration for each dimensional axis
22 h 
Acceleration rms value 
181 m/s² 
Vibration profile Figure 26 
Frequency in Hz
Power spectral density
in (m/s²)²/Hz 
10 
10 
100 
10 
300 
0,51 
500 
20 
2�000 
20 
 
 
Figure 26: Vibration profile, broadband random vibration for equipment mounted on the engine 
 
 
0,1
1
10
100
10
100
1000
10000
Leistungsdichtespektrum�in�(m/s²)²/Hz
Frequenz�in�Hz
Frequency [Hz] 
Power spectral densitiy [(m/s2)2/Hz]
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 32 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 80 of 160
 
******** Vibration profile B (for gear box mounted equipment) 
Table 57: Test parameters vibration, sinusoidal for gear box mounted equipment 
Excitation 
Sinusoidal 
Test duration for each dimensional axis
22 h 
Vibration profile Figure 27 
Frequency in Hz
Amplitude of  
acceleration in m/s²
100 
30 
200 
60 
440 
60 
 
 
Figure 27: Vibration profile, sinusoidal for gear box mounted equipment 
 
 
Frequency [Hz] 
Acceleration amplitude [m/s2]
0
10
20
30
40
50
60
70
100
1000
Amplitude�der�Beschleunigung�in�m/s²
Frequenz�in�Hz
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 33 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 81 of 160
 
 
Table 58: Test parameters vibration, broadband random vibration for  
gear box mounted equipment  
Excitation 
Broadband random vibration 
Test duration for each dimensional axis
22 h 
Acceleration rms value 
96,6 m/s² 
Vibration profile Figure 28 
Frequency in Hz
Power spectral density
in (m/s²)²/Hz 
10 
10 
100 
10 
300 
0,51 
500 
5 
2�000 
5 
 
 
 
Figure 28: Vibration profile, broadband random vibration for gear box mounted equipment 
 
 
0,1
1
10
10
100
1000
10000
Leistungsdichtespektrum�in�(m/s²)²/Hz
Frequenz�in�Hz
Frequency [Hz] 
Power spectral densitiy [(m/s2)2/Hz]
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 34 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 82 of 160
 
******** Vibration profile C (for equipment mounted on flexible plenum 
chamber but not rigidly attached) 
Table 59: Test parameters vibration, sinusoidal for equipment mounted on flexible plenum 
chamber but not rigidly attached 
Excitation 
Sinusoidal 
Test duration for each dimensional axis
22 h 
Vibration profile Figure 29 
Frequency in Hz
Amplitude of  
acceleration in m/s²
100 
90 
200 
180 
325 
180 
500 
80 
1�500 
80 
 
 
Figure 29: Vibration profile, sinusoidal for equipment mounted on flexible plenum chamber but 
not rigidly attached 
 
 
0
20
40
60
80
100
120
140
160
180
200
100
1000
10000
Amplitude�der�Beschleunigung�in�m/s²
Frequenz�in�Hz
Frequency [Hz] 
Acceleration amplitude [m/s2]
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 35 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 83 of 160
 
******** Vibration profile D (for equipment mounted on sprung masses 
(vehicle body)) 
Table 60: Test parameters, broadband random vibration for equipment mounted on sprung 
masses (vehicle body) 
Excitation 
Broadband random vibration 
Test duration for each dimensional axis
8 h 
Acceleration rms value 
30,8 m/s² 
Vibration profile Figure 30 
Frequency in Hz
Power spectral density
in (m/s²)²/Hz 
5 
0,884 
10 
20 
55 
6,5 
180 
0,25 
300 
0,25 
360 
0,14 
1�000 
0,14 
2�000 
0,14 
 
 
Figure 30: Vibration profile, broadband random vibration for equipment mounted on sprung 
masses (vehicle body) 
 
 
0,1
1
10
100
1
10
100
1000
10000
Leistungsdichtespektrum�in�(m/s²)²/Hz
Frequenz�in�Hz
Frequency [Hz] 
Power spectral densitiy [(m/s2)2/Hz]
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 36 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 84 of 160
 
******** Vibration profile E (for equipment mounted on unsprung masses 
(wheel, wheel suspension)) 
Table 61: Test parameters, broadband random vibration for equipment mounted on unsprung 
masses (wheel, wheel suspension) 
Excitation 
Broadband random vibration 
Test duration for each dimensional axis
8 h 
Acceleration rms value 
107,3 m/s² 
Vibration profile Figure 31 
 
Frequency in Hz
Power spectral density
in (m/s²)²/Hz 
20 
200 
40 
200 
300 
0,5 
800 
0,5 
1�000 
3 
2�000 
3 
 
 
Figure 31: Vibration profile, broadband random vibration for equipment mounted on unsprung 
masses (wheel, wheel suspension) 
13.4.3 
Requirement 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (large) as per Section 10.4. 
0,1
1
10
100
1000
10
100
1000
10000
Leistungsdichtespektrum�in�(m/s²)²/Hz
Frequenz�in�Hz
Frequency [Hz] 
Power spectral densitiy [(m/s2)2/Hz]
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 37 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 85 of 160
 
13.5 M-05 Mechanical shock 
13.5.1 
Purpose 
This test simulates the mechanical exposure of components, e.g. when driving over 
curbs or in accidents. 
The test is intended to verify the resistance of the component to faults such as cracks 
or component displacements. 
13.5.2 
Test 
This test shall be carried out in accordance with DIN EN 60068-2-27 with the 
following parameters: 
 
Table 62: Test parameters M-05 Mechanical shock 
Operating mode of the DUT 
If the component is operated with 
operating load in driving mode:  
II.c during working condition 
driving 
 
If the component is not operated 
with operating load in driving 
mode:  
II.a 
Peak acceleration 
500 m/s2 
Duration of pulse 
6 ms 
Pulse shape 
Half-sine 
Number of shocks per direction (±X, ±Y, ±Z) 
10 
Number of DUTs 
6 
13.5.3 
Requirement 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (small) as per Section 10.4. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 38 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 86 of 160
 
13.6 M-06 Mechanical shock endurance 
13.6.1 
Purpose 
This test simulates the acceleration forces of components that are mounted in doors 
or flaps and are subjected to high accelerations during opening and closing. 
The test is intended to verify the resistance of the component to faults such as 
component displacement or material fatigue. 
13.6.2 
Test 
This test shall be carried out in accordance with DIN EN 60068-2-29 with the 
following parameters: 
 
Table 63: Test parameters M-06 Mechanical shock endurance 
Operating mode of the DUT 
Operating mode II.c 
 
If several working conditions are relevant for the 
component in operating mode II.c (see Section 10), 
during the times in which operating mode II.c is 
required in this test, the mechanical shocks shall 
be carried out in each relevant working condition in 
equal numbers of shocks. 
Peak acceleration 
300 m/s² 
Duration of pulse 
6 ms 
Pulse shape 
Half-sine 
Number of shocks 
 
Installation area 
Number of shocks 
Driver's door 
100�000 
Front passenger and 
rear doors 
50�000 
Trunk lid/tailgate 
30�000 
Engine hood 
3�000 
If the component is mounted in several installation 
areas, the highest number of shocks shall be 
applied. 
Installation position 
The DUT shall be fixed on the test fixture in the 
appropriate installation position in the vehicle. 
Number of DUTs 
6 
13.6.3 
Requirement 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (small) as per Section 10.4. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 39 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 87 of 160
 
14 Climatic requirements and tests 
14.1 K-01 High/low temperature storage 
14.1.1 
Purpose 
This test simulates the thermal exposure of the component during storage and 
transport. 
The test is intended to verify the resistance to storage at high or low temperatures, 
e.g. during the transport of the component (plane, ship container). 
If the test is carried out at the beginning of a test sequence, it is also intended to 
adjust all components to the same initial conditions. 
14.1.2 
Test 
Table 64: Test parameters K-01 High/low temperature storage 
Operating mode of the DUT 
Operating mode I.a 
Test duration and test 
temperature 
2 cycles of 24 h (consisting of 12 h storage at Tmin 
and 12 h storage at Tmax) 
Number of DUTs 
As specified in the test sequence plan in the 
Component Requirement Specification. 
14.1.3 
Requirement 
The DUT shall be fully functional before and after the test and all parameters shall 
meet the specifications. Verification is done by means of a parameter test (large) as 
per Section 10.4. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 40 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 88 of 160
 
14.2 K-02 Temperature step test 
14.2.1 
Purpose 
This test simulates the operation of the component at different ambient temperatures. 
The test is intended to verify the resistance of the component to malfunctions that 
may occur within a small interval of the operating temperature range. 
14.2.2 
Test 
Table 65: Test parameters K-02 Temperature step test 
Operating mode of 
the DUT 
During the parameter test (function test) operating mode II.c, 
otherwise operating mode II.a 
Test temperature 
The DUTs shall be subjected to the temperature profile shown 
in Figure 32. 
Temperature change of 5 °C for each step.  
Test sequence 
 
The DUT shall be maintained at each temperature step until 
the specified temperature is attained throughout (see Section 
10.4). 
This shall be followed by a parameter test (function test) as 
per Section "Parameter test" (see Section 10.4). 
Number of DUTs 
6 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Figure 32: Temperature profile for temperature step test 
For components attached to a coolant circuit the coolant temperature shall follow the 
relevant test temperature between Tcool,min and Tcool,max . Outside the coolant 
temperature limits, only the ambient temperature shall be varied. 
14.2.3 
Requirement 
All parameters of the DUT shall lie within the specification during each parameter test 
(function test). 
time
Temperature 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 41 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 89 of 160
 
14.3 K-03 Low temperature operation 
14.3.1 
Purpose 
This test simulates the exposure of the component to low temperatures. 
The test is intended to verify the function of the component after a long parking time 
or operation time at extremely low temperatures. 
14.3.2 
Test 
This test shall be carried out in accordance with DIN EN 60068-2-1, test Ab, with the 
following parameters: 
 
Table 66: Test parameters K-03 Low temperature operation 
Operating mode of DUT 
12 h operating mode II.a 
12 h operating mode II.c at UBmin 
12 h operating mode II.a 
12 h operating mode II.c at UB 
 
 
If several working conditions are relevant for the 
component in operating mode II.c (see Section 10) during 
the times in which operating mode II.c is required in this 
test, the component shall be operated in each relevant 
working condition in equal lengths of time. 
Test duration 
48 h 
Test temperature 
Tmin 
 
 
Number of DUTs  
6 
 
For components dissipating heat, the test shall also be carried out in accordance with 
DIN EN 60068-2-1, test Ab. 
For components attached to a coolant circuit the minimum coolant temperature 
Tcool,min shall be adjusted. 
 
For components with high power dissipation a rise of the test chamber temperature 
by means of self-heating above Tmin is allowed for this test in operating mode II.c, in 
agreement between supplier and buyer. 
 
14.3.3 
Requirement 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (small) as per Section 10.4. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 42 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 90 of 160
 
14.4 K-04 Repainting temperature 
14.4.1 
Purpose 
This test simulates the exposure of the component during repainting. 
The test is intended to verify the resistance of the component to faults that occur due 
to thermal exposure, e.g. cracking in soldered joints, adhesive joints and welded 
joints, in bond connections as well as in seals and housings. 
14.4.2 
Test 
Table 67: Test parameters K-04 Repainting temperature 
Operating mode of DUT 
Operating mode II.a 
Test duration and test temperature  
15 min at 130 °C and 1 h at 110 °C 
Number of DUTs 
6 
 
For components attached to a coolant circuit, the temperature of the stationary 
coolant shall be set to TRT. 
14.4.3 
Requirement 
The DUT shall be fully functional before and after the test and all parameters shall 
meet the specifications. Verification is done by means of a parameter test (small) as 
per Section 10.4.  
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 43 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 91 of 160
 
14.5 K-05 Thermal shock (component)  
14.5.1 
Purpose 
This test simulates the thermal exposure of a component to rapid temperature 
changes during vehicle operation. 
The test is intended to verify the resistance of the component to faults that occur due 
to thermal exposure, e.g. cracking in soldered joints, adhesive joints and welded 
joints, in bond connections as well as in seals and housings. 
14.5.2 
Test 
This test shall be carried out in accordance with DIN EN 60068-2-14 with the 
following parameters: 
 
Table 68: Test parameters K-05 Thermal shock (component) 
Operating mode of DUT 
Operating mode I.b 
Lower temperature / 
temperature of cold test 
bath 
Tmin 
Upper temperature / 
temperature of the warm 
test bath 
Tmax 
Holding time at 
upper/lower temperature 
15 min following attainment of complete thermal 
equilibrium (see Section 0) 
Transition time 
(air - air, medium - 
medium) 
� 30 s 
Test fluid for Nc test 
Fluid in which the component is operated in the vehicle 
Test 
In accordance with DIN EN 60068-2-14 Na for 
components that are not permanently operated in a fluid. 
 
In accordance with DIN EN 60068-2-14 Nc for 
components that are permanently operated in a fluid  
(IP X8). 
The DUT shall be immersed such that all sides of the 
DUT are surrounded by at least 25 mm of test fluid. 
Number of cycles 
100 
Number of DUTs 
6 
14.5.3 
Requirement 
The DUT shall be fully functional before and after the test and all parameters shall 
meet the specifications. Verification is done by means of a parameter test (large) as 
per Section 10.4. 
 
For medium - medium test additionally: 
There shall be no ingress of fluid. The DUT shall not be opened until after completion 
of the entire test sequence as given in the test sequence plan (Section 12.2). 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 44 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 92 of 160
 
14.6 K-06 Salt spray test, operating, exterior 
14.6.1 
Purpose 
This test simulates the exposure of the component to air and water containing salt, a 
situation that may occur in certain areas of the world and in wintry road conditions. 
The test is intended to verify the resistance of the component to malfunction when 
exposed to salt, e.g. due to short circuits and leakage currents caused by the ingress 
of salt into the component. 
14.6.2 
Test 
This test shall be carried out in accordance with DIN EN 60068-2-11 with the 
following parameters: 
 
Table 69: Test parameters K-06 Salt spray test, operating, exterior 
Operating mode of 
DUT 
 
During spray phase: Intermitting between 1 h operating mode 
II.a and 1 h operating mode II.c. 
 
During rest phase: Operating mode II.a 
Test temperature 
35 °C 
Test cycle 
Each test cycle consists of an 8 h spray phase and a 4 h rest 
phase as per Figure 33 
Number of test 
cycles 
For components in the underbody/engine compartment: 12 
cycles 
For other components: 8 cycles 
Number of DUTs 
6 
 
When performing the test, the installation position of the component in the vehicle 
shall be simulated.  
 
For components connected to a coolant circuit the coolant temperature shall be 
adjusted to the test temperature. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 45 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 93 of 160
 
 
Figure 33: Salt spray test, operating, exterior - spray phases 
14.6.3 
Requirement 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (small) as per Section 10.4. 
Spray phase 
Rest phase 
Electrical operation 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 46 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 94 of 160
 
K-07 Salt spray test, operating, interior 
14.6.4 
Purpose 
This test simulates the exposure of the component to air containing salt, as it may 
occur in certain areas of the world. 
The test is intended to verify the resistance of the component to malfunction when 
exposed to salt, e.g. due to short circuits and leakage currents caused by the ingress 
of salt into the components. 
14.6.5 
Test 
This test shall be carried out in accordance with DIN EN 60068-2-11 Ka with the 
following parameters: 
 
Table 70: Test parameters K-07 Salt spray test, operating, interior 
Operating mode of DUT 
 
During spray phase: intermitting between 55 min 
operating mode II.a and 5 min. operating mode II.c 
 
If several working conditions are relevant for the 
component in operating mode II.c (see Section 10) during 
the times in which operating mode II.c is required in this 
test, the component shall be operated in each relevant 
working condition in equal lengths of time. 
 
During rest phase: Operating mode II.a 
Test temperature 
35 °C 
Test cycle 
Each test cycle consists of an 8 h spray phase and a 4 h 
rest phase as per Figure 34 
Number of test cycles 
2 
Number of DUTs 
6 
 
When performing the test, the installation position of the component in the vehicle 
shall be simulated. The test setup (installation position, covers, trim, situation during 
operation) shall be recommended by the supplier, coordinated with the buyer, and 
documented. 
For components connected to a coolant circuit the coolant temperature shall be 
adjusted to the test temperature. 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 47 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 95 of 160
 
 
 
4
8
12
1 cycle
Spraying phase
Rest time
t (h)
Electrical operation
 
Figure 34: Salt spray test, operating, interior - spray phases 
14.6.6 
Requirement 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (large) as per Section 10.4. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 48 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 96 of 160
 
14.7 K-08 Damp heat, cyclic 
14.7.1 
Purpose 
This test simulates the thermal exposure of a component to cyclic temperature 
changes at high humidity during vehicle operation. 
The test is intended to verify the function of the component when exposed to damp 
heat. 
14.7.2 
Test 
This test shall be carried out in accordance with DIN EN 60068-2-30 with the 
following parameters: 
 
Table 71: Test parameters K-08 Damp heat, cyclic 
Operating mode of DUT 
Operating mode II.a 
Total test duration 
144 h 
Test variant 
Variant 1 
Upper test temperature 
55 °C 
Number of cycles 
6 
Number of DUTs 
6 
 
For components attached to a coolant circuit the coolant temperature shall follow the 
relevant test temperature between Tcool,min and Tcool,max . Outside the coolant 
temperature limits, only the ambient temperature shall be varied. 
14.7.3 
Requirement 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (large) as per Section 10.4. 
 
In addition, a parameter test (function test) shall be performed each time the upper 
test temperature is reached and each time the lower test temperature is reached. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 49 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 97 of 160
 
14.8 K-09 Damp heat, cyclic (with frost) 
14.8.1 
Purpose 
This test simulates the thermal exposure of a component to cyclic temperature 
changes (including frost)  at high humidity during vehicle operation. 
The test is intended to verify the function of the component when exposed to damp 
heat. 
14.8.2 
Test 
This test shall be carried out in accordance with DIN EN 60068-2-38 with the 
following parameters: 
 
Table 72: Test parameters K-09 Damp heat, cyclic (with frost) 
Operating mode of DUT 
Intermitting between 40 min operating mode II.a and 10 
min operating mode II.c 
 
If several working conditions are relevant for the 
component in operating mode II.c (see Section 10) during 
the times in which operating mode II.c is required in this 
test, the component shall be operated in each relevant 
working condition in equal lengths of time. 
Total test duration 
240 h 
Number of cycles 
10 
Test cycle sequence 
The first five cycles shall include a cold subcycle and the 
remaining cycles shall be carried out without a cold 
subcycle. 
Number of DUTs 
6 
 
For components attached to a coolant circuit the coolant temperature shall follow the 
relevant test temperature between Tcool,min and Tcool,max . Above the coolant 
temperature limits, only the ambient temperature shall be varied. 
 
14.8.3 
Requirement 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (large) as per Section 10.4. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 50 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 98 of 160
 
14.9 K-10 Protection against water – IPX0 to IPX6K 
14.9.1 
Purpose 
This test simulates the exposure of the component to water. 
The test is intended to verify the function of the component, e.g. when exposed to 
condensation water, rain or spray water. 
14.9.2 
Test 
This test shall be carried out in accordance with ISO 20653 with the following 
parameters: 
Table 73: Test parameters K-10 Protection against water – IPX0 to IPX6K 
Operating 
mode of DUT 
Intermitting between 1 min operating mode II.a and 1 min operating 
mode II.c 
 
If several working conditions are relevant for the component in 
operating mode II.c (see Section 10) during the times in which 
operating mode II.c is required in this test, the component shall be 
operated in each relevant working condition in equal lengths of time. 
Required 
degree of 
protection  
As specified in the Component Requirement Specifications 
Number of 
DUTs 
6 
14.9.3 
Requirement 
The degree of protection specified in the Component Requirement Specifications in 
accordance with ISO 20653 shall be achieved. 
 
There shall be no water ingress. The DUT shall not be opened until completion of the 
entire test sequence. 
 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (small) as per Section 10.4. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 51 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 99 of 160
 
14.10 
K-11 High-pressure/steam-jet cleaning 
14.10.1 
Purpose 
This test simulates the exposure of the component to water during vehicle cleaning. 
The test is intended to verify the function of the component when exposed to high-
pressure/steam-jet cleaning. 
14.10.2 
Test 
This test shall be carried out in accordance with ISO 20653 with the following 
parameters: 
Table 74: Test parameters 
Operating mode of 
DUT 
Operating mode II.a 
Required degree of 
protection 
IP X9K 
Water pressure 
The steam jet shall have a minimum pressure of 10�000 kPa 
(100 bar) as measured directly at the nozzle. 
Water temperature 
80 °C 
Test procedure 
The DUT shall be subjected to the water jet from every freely 
accessible direction around the vehicle. 
Number of DUTs 
6 
14.10.3 
Requirement 
The degree of protection IP X9K in accordance with ISO 20653 shall be achieved. 
 
There shall be no water ingress. The DUT shall not be opened until completion of the 
entire test sequence (Section 12.2). 
 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (small) as per Section 10.4. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 52 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 100 of 160
 
14.11 
K-12 Thermal shock with splash water 
14.11.1 
Purpose 
This test simulates the exposure of the component to splash water as it occurs when 
driving through puddles. 
The test is intended to verify the function of the component when exposed to abrupt 
cooling by means of water. 
14.11.2 
Test 
Table 75: Test parameters K-12 Thermal shock with splash water 
Operating mode of 
DUT 
If the component is not operated with operating load in driving 
mode:  
II.a during the entire test 
 
If the component is operated with operating load in driving 
mode:  
Intermitting II.a and II.c during working condition driving (see 
Figure 35) 
Test procedure 
Heating of DUT to test temperature. 
This is followed by the cyclic splashing of the DUT as per Figure 
35. The DUT shall be splashed over its entire width. 
Cycle duration 
30 min 
Test temperature 
Tmax
Test medium for 
splashing 
Tap water containing 3 % Arizona dust by weight, fine, in 
accordance with ISO 12103-1. Permanent mixing shall be 
ensured. 
Splash water 
temperature 
0 to +4 °C
Splash nozzle 
See Figure 36 
Splash duration 
3 s 
Water flow 
3 to 4 liters per splash/nozzle 
Distance of nozzle 
to DUT 
300 to 350 mm 
Number of cycles 
100 
Number of DUTs 
6 
 
When performing the test, the installation position of the component in the vehicle 
shall be simulated. 
The test setup (installation position, covers, trim, situation during operation) shall be 
recommended by the supplier, coordinated with the buyer, and documented. 
 
For components attached to a coolant circuit the coolant temperature shall follow the 
relevant test temperature up to the limit Tcool,max. Above the coolant temperature 
limits, only the ambient temperature shall be varied. 
 
Test setup as per Figure 37. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 53 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 101 of 160
 
 
Figure 35: Splash water test, splashing times 
 
Dimensions in mm 
 
Figure 36: Splash water test - splash nozzle 
t1 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 54 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 102 of 160
 
 
Figure 37: Splash water test setup 
14.11.3 
Requirement 
There shall be no water ingress. The DUT shall not be opened until completion of the 
entire test sequence. 
 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (small) as per Section 10.4. 
Splash nozzle 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 55 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 103 of 160
 
14.12 
K-13 Thermal shock immersion  
14.12.1 
Purpose 
This test simulates the exposure of the component when immersed in water. 
The test is intended to verify the function of the component when subjected to 
immediate cooling by means of immersion of the heated component. 
14.12.2 
Test 
This test shall be carried out in accordance with ISO 20653 with the following 
parameters: 
 
Table 76: Test parameters K-13 Thermal shock immersion  
Operating mode 
of DUT 
If the component is operated with operating load in driving 
mode:  
II.c during working condition driving 
 
If the component is not operated with operating load in driving 
mode:  
II.a 
Required degree 
of protection 
IP X7 
Test procedure 
Heating of DUT to Top,max 
Hold at Top,max until complete thermal equilibrium is reached (see 
Section 10.3) and then for 15 min more. 
Fully immerse the DUT within five seconds into the test fluid 
such that all sides of the DUT are surrounded by at least 25 mm 
of the test fluid. 
Test medium 
0 °C cold, 5 % salt water solution 
Immersion time 
5 min 
Number of cycles 
20 
Number of DUTs 
6 
 
For components attached to a coolant circuit the coolant temperature shall follow the 
relevant test temperature up to the limit Tcool,max. Above the coolant temperature 
limits, only the ambient temperature shall be varied. 
14.12.3 
Requirement 
There shall be no water ingress. The DUT shall not be opened until completion of the 
entire test sequence (Section 12.2). 
 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (small) as per Section 10.4.  
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 56 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 104 of 160
 
14.13 
K-14 Damp heat, steady state 
14.13.1 
Damp heat, steady state – severity 1 
14.13.1.1 Purpose 
This test simulates the exposure of the component to damp heat. 
The test is intended to verify the resistance of the component to faults caused by 
damp heat, e.g. corrosion, migration/dendritic growth, swelling and degradation of 
plastics, sealing and grouting compounds. 
14.13.1.2 Test 
This test shall be carried out in accordance with DIN EN 60068-2-78 with the 
following parameters: 
 
Table 77: Test parameters K-14 Damp heat, steady state – severity 1 
Operating mode of DUT 
Operating mode II.a 
Test temperature 
40 °C 
Humidity 
93 % relative humidity 
Test duration 
21 days 
Number of DUTs 
6 
14.13.1.3 Requirement 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (large) as per Section 10.4. 
 
In addition, a parameter test (function test) shall be carried out every seven days. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)
(#)
(#) With regard to MBN LV124-2  
of Daimler AG, the application of this test  
with severity 1 is not permissible.


### 第 57 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 105 of 160
 
14.13.2 
Damp heat, steady state – severity 2 
14.13.2.1 Purpose 
This accelerated test simulates the exposure of the component to damp heat during 
vehicle service life. 
The test is intended to verify the quality and reliability of the component with respect 
to faults caused by damp heat, e.g. corrosion, migration/dendritic growth, swelling 
and degradation of plastics, sealing and grouting compounds. 
14.13.2.2 Test 
This test shall be carried out in accordance with DIN EN 60068-2-78 with the 
following parameters: 
 
Table 78: Test parameters K-14 Damp heat, steady state – severity 2 
Operating mode of DUT 
Intermitting operation between 47 h operating mode II.a 
and  
1 h operating mode II.c  
repeating until end of test duration 
 
If several working conditions are relevant for the 
component in operating mode II.c (see Section 10) during 
the times in which operating mode II.c is required in this 
test, the component shall be operated in each relevant 
working condition in equal lengths of time. 
Test duration 
As specified in the Component Requirement Specification 
as per Section E.1 (Lawson model) 
Test temperature 
65 °C 
Test humidity 
93 % relative humidity 
Number of DUTs 
6 
 
For components attached to a coolant circuit the coolant temperature shall follow the 
relevant test temperature up to the limit Tcool,max. Above the coolant temperature 
limits, only the ambient temperature shall be varied. 
 
Prior to the execution of this service life test, a check shall be conducted to ascertain 
whether the high test acceleration using the test parameters 65 °C and 93 % r.h. 
exceeds the physical limits of the materials used in the components (e.g. hydrolysis 
of plastics). Where applicable, the supplier and buyer shall agree to changes in the 
test temperature and test humidity (e.g. to 55 °C and 93 % relative humidity) while 
increasing the test duration as per the Lawson model such that the physical limits of 
the materials used are not exceeded during the test. The overall severity of the 
testing shall, however, remain unchanged. The test humidity shall not exceed the 
level of 93 % relative humidity. 
 
Care shall be taken to ensure that no condensation occurs on the DUT during the 
test (including local condensation).  
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 58 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 106 of 160
 
14.13.2.3 Deviating test sequence for components with reduced 
performance at high temperatures
For components with reduced performance (e.g. reduction of backlight of LCDs) at 
high temperatures starting at Top,max (Top, max < 65 °C), the test shall not - deviating 
from Table 78 - be carried out at a constant test temperature of 65 °C, but with the 
following parameters (see Table 79). 
 
Table 79: Test parameters K-14 Damp heat, steady state for components with reduced 
performance at high temperatures 
Operating mode of 
DUT 
Intermitting operation as per Figure 38 
Test duration 
As specified in the Component Requirement Specification as 
per Section E.1 (Lawson model). 
The ramp times between 65 °C and Top, max shall not be 
included in the test duration. 
Test temperature 
As per Figure 38 
The temperature gradient shall be selected such that no 
condensation occurs at the DUT. 
Test humidity 
93 % relative humidity 
Interval time t1 
47 h 
Interval time t2 
1 h 
Number of DUTs 
6 
 
For components attached to a coolant circuit the coolant temperature shall follow the 
relevant test temperature up to the limit Tcool,max . Above the coolant temperature 
limits, only the ambient temperature shall be varied. 
II.c
II.c
II.a
II.a
II.a
t1
t2
65°C
Top,max
 
Figure 38: Temperature profile for testing components with reduced performance at high 
temperatures above Top,max 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 59 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 107 of 160
 
14.13.2.4 Requirement 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (large) as per Section 10.4. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 60 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 108 of 160
 
14.14 
K-15 Condensation and climate test 
14.14.1 
K-15 a Condensation test with electronic assemblies 
14.14.1.1 Purpose 
This test simulates the condensation on electronic assemblies in motor vehicles. 
It serves to evaluate the robustness of the electronic assembly with respect to 
condensation. 
14.14.1.2 Test 
Performance of the test with electronic assemblies without housing with the following 
parameters: 
 
Table 80: Test parameters K-15 a Condensation test with electronic assemblies 
Operating mode 
of DUT 
Operating mode II.a 
In addition, parameter tests (function tests) as described in row 
"Test procedure" shall be carried out.  
Apparatus 
Climatic chamber with condensation option (specially controlled 
water bath which converts the required quantity of water to water 
vapor). 
The climate control is switched off during the condensation phase. 
The test chamber temperature is controlled by means of the 
temperature-controlled water bath. 
Test procedure 
1. The climatic chamber remains at the initial temperature for 
60 min to ensure that the DUT has attained complete 
thermal equilibrium. Then the condensation phase begins. 
2. In the period between 30 min after the start until 30 min 
before the end of the condensation phase (as per Figure 
41), a parameter test (function test), shall be performed at 
each 10 K increase in the water bath temperature, however 
only at voltage UB,. 
The parameter test (function test) shall be performed with a 
minimum of power loss for max. 2 min since otherwise the 
DUT will heat up too much and condensation will no longer 
take place. 
Test 
temperature  
See Figure 41 
Relative test 
chamber 
humidity 
See Figure 41 
During the condensation phase the relative test chamber humidity 
shall be 100 % r.h. (0 %, -5 %). 
Test duration 
32,5 h (5 cycles of 6,5 h) 
Test medium 
Distilled water with a maximum conductivity of 5 �S/cm 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 61 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 109 of 160
 
Position of DUT 
Installation position as in vehicle.  
Plastic brackets shall be used to ensure that the electronic 
assembly remains in its installation position in the test chamber. 
If the assembly is used in different installation positions, the DUTs 
shall also be positioned in these different installation positions in 
the test chamber. 
Test set-up 
See Figure 39 
During the test a plastic hood as per Figure 40 shall be used to 
eliminate any undesired effects caused by variations in air speed. 
The hood shall be aligned such that the bevel points to the test 
chamber door. 
The dimensions of the plastic hood shall be adapted to the size of 
the test chamber. 
The distance between the plastic hood and the test chamber wall 
shall be 10 % of the test chamber width/depth, however, at least 8 
cm.  
In accordance with DIN EN ISO 6270-2 an alpha angle of � 12° 
shall be used for the top slope of the plastic hood. 
Condition for 
testing 
The condensation test shall be performed initially prior to the final 
definition of the circuit layout (hardware freeze), but shall be done 
on assemblies manufactured under near-production-level 
conditions so that any sensitivities to condensation found during 
the test can be remedied, e.g. through changes to the layout and 
the circuit. 
If changes to the assembly manufacturing process are introduced 
(e.g. circuit board, soldering agent, flux, soldering process, layout, 
relocation or electronic devices), the test shall be repeated. 
Number of 
cycles 
5 
Number of 
DUTs 
6 electronic assemblies 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 62 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 110 of 160
 
 
 
Figure 39: Test setup K-15 a Condensation test with electronic assemblies 
 
Figure 40: Plastic hood 
Test chamber 
) 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 63 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 111 of 160
 
10
20
30
40
50
60
70
80
90
100
0
°C
%
�
1 cycle
Condensation phase  **)
30
30
30
30
30
150
75
Time (t) in 
min
1)
2)
Parameter test (function test) at UB
Water bath temperature 
1 K
Test chamber temperature 
3 K
3)
Humidity curve not defined
Test chamber humidity
Water bath temperature < 20°C
**)     Recording of test chamber humidity and temperature, temperature difference < 15 °C
1)  Start of drying phase after reaching
75 °C air temperature
2)  DUT must be dry   Frel < 50 %
3)  Transition from climate chamber control to 
water bath control
Drying phase
15
 
Figure 41: Test sequence K-15 a Condensation test with electronic assemblies 
14.14.1.3 Requirement 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (large) as per Section 10.4.  
In addition, the assembly shall be examined with respect to electrochemical migration 
(e.g. signs of silver and tin migration) and dendritic growth.  
Any occurrence of electrochemical migration/dendritic growth is not permissible. 
Other changes to the assembly (e.g. corrosion, contamination) shall be documented 
in the test report and evaluated with the buyer. 
The following documentation shall be included with the test report: 
 
  1. Programming of the test chamber 
  2. Parameters (specified/actual) of one cycle  
  3. Parameters (specified/actual) of all five cycles 
For examples, see Annex F. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 64 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 112 of 160
 
14.14.2 
K-15 b Climate test for components with waterproof housings 
14.14.2.1 Purpose 
This accelerated test simulates the thermal exposure of the component to damp heat 
taking into account the protective effect of waterproof housings during the vehicle 
service life. 
The test is intended to verify the quality and reliability of the component with respect 
to faults caused by damp heat, e.g. corrosion, migration/dendritic growth, swelling 
and degradation of plastics, sealing and grouting compounds. 
 
14.14.2.2 Test 
The test shall be carried out with complete components (device, control unit, 
mechatronics, … with housings). 
 
The test shall be carried out as a sequence of five test blocks according to Figure 42: 
 
Test block 1
Damp heat, 
steady state 
Test block 3
Damp heat, 
steady state 
Test block 4
Damp heat, 
cyclic
(with frost)
Test block 2
Damp heat, 
cyclic 
(with frost)
Test block 5
Damp heat, 
steady state 
t
 
Figure 42: Test sequence K-15 b Climate test for components with waterproof housings 
 
Test blocks 1, 3 and 5: 
This test shall be carried out in accordance with DIN EN 60068-2-78 with the 
following parameters: 
 
Table 81: Test parameters K-15 b Climate test for components with waterproof housings  
test blocks 1, 3 and 5 
Operating mode 
of DUT 
Operating mode II.a 
12 hours after beginning of the test block and afterwards every 24 
hours a parameter test (function test) shall be carried out. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 65 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 113 of 160
 
Test duration 
per test block 
As specified in the Component Requirement Specifications 
Note: 
The total test duration of test K-15 b (test blocks 1 to 5) complies 
with the test duration of test K-14 Damp heat, steady state – 
severity 2. 
This total test duration consists of two times 240 hours test 
duration for each of the test blocks 2 and 4. 
The remaining test duration is divided into one third for each of the 
test blocks 1, 3 and 5: 
Test durationTest block 1 = Test durationTest block 3 =  
Test durationTest block 5 
= 1/3 (total test duration - 2*240 hours).  
Test 
temperature 
65 °C
Test humidity 
93 % relative humidity 
Number of 
DUTs
6
 
For components attached to a coolant circuit the coolant temperature shall follow the 
relevant test temperature up to the limit Tcool,max . Above the coolant temperature 
limits, only the ambient temperature shall be varied. 
 
Test blocks 2 and 4: 
This test shall be carried out in accordance with DIN EN 60068-2-38 with the 
following parameters: 
 
Table 82: Test parameters K-15 b Climate test for components with waterproof housings  
test blocks 2 and 4 
Operating mode 
of DUT 
Operating mode II.a 
12 hours after beginning of the test block and afterwards every 24 
hours a parameter test for environmental tests (function test) shall 
be carried out. 
Test duration 
per test block 
240 h 
Number of 
cycles 
10
Test cycle 
sequence 
The first five cycles shall include a cold subcycle and the 
remaining cycles shall be carried out without a cold subcycle.
Number of 
DUTs 
6 
 
For components attached to a coolant circuit the coolant temperature shall follow the 
relevant test temperature between Tcool,min and Tcool,max . Outside the coolant 
temperature limits, only the ambient temperature shall be varied. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 66 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 114 of 160
 
14.14.2.3 Requirement 
The DUT shall be fully functional before, during and after the test and all parameters 
shall meet the specifications. Verification is done by means of continuous parameter 
monitoring and a parameter test (large) as per Section 10.4.2. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 67 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 115 of 160
 
14.15 
K-16 Thermal shock (without housing) 
14.15.1 
Purpose 
This technology test does not simulate any real exposure. 
Instead, it is intended to detect weak points in the mechanical properties of 
interconnect technologies on electronic assemblies, such as soldering points. 
 
The test shall be performed exclusively with the electronic assembly of the 
component without housing and mechanical parts. 
14.15.2 
Test 
This test shall be carried out in accordance with DIN EN 60068-2-14 with the 
following parameters: 
 
Table 83: Test parameters K-16 Thermal shock (without housing) 
Operating mode of DUT 
Operating mode I.a 
Lower temperature 
Tmin 
Upper temperature 
Tmax 
Holding time at upper/lower 
temperature 
15 min following attainment of complete thermal 
equilibrium (see Section 0) 
Transition time 
� 10 s 
Number of cycles 
300 
Number of DUTs 
6 electronic assemblies 
14.15.3 
Requirement 
The DUT shall be fully functional before and after the test and all parameters shall 
meet the specifications. Verification is done by means of a parameter test (large) as 
per Section 10.4. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 68 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 116 of 160
 
14.16 
K-17 Solar radiation 
14.16.1 
Purpose 
This test simulates the influence of solar radiation and UV light on the component. 
The test is intended to verify the resistance of the component to damage caused by 
material fatigue, such as cracks or discolorations. 
14.16.2 
Test 
This test shall be carried out in accordance with DIN 75220 with the following 
parameters: 
 
Table 84: Test parameters K-17 Solar radiation 
Operating mode of 
DUT 
Operating mode I.a 
Applied test profiles 
The test profiles according to DIN 75220 shall be applied 
depending on the installation space for the component. 
Components in the 
exterior 
Use of the Z-OUT profile as per Table 2 and Table 5 of DIN 
75220 
Components in the 
interior 
Use of the Z-IN1 profile as per DIN 75220  
Test duration 
25 days (15 days dry, 10 days humid) 
Number of cycles 
1 
Number of DUTs 
6 
14.16.3 
Requirement 
The DUT shall be fully functional before and after the test and all parameters shall 
meet the specifications. Verification is done by means of a parameter test (large) as 
per Section 10.4. 
 
In addition, the DUT shall be visually inspected with the naked eye. 
Changes or damage shall be documented in the test report and evaluated with the 
buyer. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 69 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 117 of 160
 
14.17 
K-18 Corrosion test with flow of mixed gas 
14.17.1 
Purpose 
This test simulates the influence of corrosive gases on the component, particularly its 
plug contacts and switches. 
The test is intended to verify the resistance of the component to faults such as 
corrosion and component damages. 
14.17.2 
Test 
This test shall be carried out in accordance with DIN EN 60068-2-60 Method 4 with 
the following parameters: 
Table 85: Test parameters K-18 Corrosion test with flow of mixed gas 
Operating mode of DUT 
Operating mode I.b 
Temperature 
TRT 
Humidity 
75 % relative humidity 
Corrosive gas concentration 
SO2 
0,2 ppm 
H2S 
0,01 ppm 
NO2 
0,2 ppm 
Cl2 
0,01 ppm 
Test duration 
21 days 
Number of DUTs 
6 
14.17.3 
Requirement 
The DUT shall be fully functional before and after the test and all parameters shall 
meet the specifications. Verification is done by means of a parameter test (large) as 
per Section 10.4. 
 
In addition, the contact resistance of switches and contacts shall be measured. The 
measured values shall meet the specifications. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 70 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 118 of 160
 
15 Chemical requirements and tests 
15.1 C-01 Chemical tests 
15.1.1 
Purpose 
This test simulates the exposure of the component to different chemicals. 
The test is intended to verify the resistance of the component to chemical changes on 
the housing and impairment of functioning due to chemical reactions. 
15.1.2 
Test 
Table 86: Test parameters - chemical tests 
Operating mode of DUT 
As specified in the Component Requirement 
Specifications 
Chemicals 
As specified in the Component Requirement 
Specifications 
Typical chemicals for different installation locations 
are indicated in Table 87. 
Conditioning 
Unless otherwise specified, the DUT and the 
chemical shall be stored in standard atmosphere. 
Test procedure 
The test shall be carried out in analogy with  
ISO 16750 Part 5: 
1. The chemical shall be applied to the DUT at 
TRT . Unless otherwise specified in the 
Component Requirement Specifications, an 
appropriate application method shall be 
selected for each chemical as per Table 88. 
The selected application method shall be 
documented in the test report. Care shall be 
taken to ensure that the DUT is covered with a 
sufficient amount of the chemical. 
2. The DUT shall then be stored at the 
temperature indicated in Table 87 for the 
specified exposure time. 
Number of DUTs 
1 DUT per chemical. 
A DUT may be used multiple times for multiple 
chemicals subject to an agreement with the buyer. 
 
Safety instructions and warnings for the chemicals shall be observed. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 71 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 119 of 160
 
******** Chemicals 
Table 87: Overview of chemicals (see also ISO 16750- 5)  
ID�
Chemical�agent�
Temperature�of�
DUT�
Exposure�time�
Description�/�
reference�
1�
Diesel�fuel�
Tmax�
22�h
EN 590
2�
Biodiesel�
Tmax�
22�h
EN 14214
3�
Petrol/gasoline,�unleaded�
TRT�
10�min
EN 228
4�
Kerosene�
TRT�
10�min
ASTM�1655
5�
Methanol�
TRT�
10�min
CAS�67�56�1
6�
Engine�oil�
Tmax�
22�h
Multigrade�oil�SAE�0W40,�API�SL/CF
7�
Differential�oil�
Tmax�
22�h
Hypoid�gear�oil�SAE�75W140,�API�GL�5
8�
Transmission�fluid�
Tmax�
22�h
ATF�Dexron�III
9�
Hydraulic�fluid�
Tmax�
22�h
DIN�51�524�3�(HVLP�ISO�VG�46)
10�
Grease�
Tmax�
22�h
DIN�51�502�(KP2K�30)
11�
Silicone�oil�
Tmax�
22�h
CAS�63148�58�3�(AP�100)
12�
Battery�fluid�
TRT�
22�h
37�%�H2SO4
13�
Brake�fluid�
Tmax�
22�h
ISO�4926
14�
Antifreeze�fluid�
Tmax�
22�h
Ethylene�glycol�(C2H6O2)�– water�(mixture�ratio�1:1)�
15�
Urea�
Tmax�
22�h
ISO 22241�1
16�
Cavity�protection�
TRT�
22�h
e.g.�underbody�protection,�by�Teroson�
1
17�
Preservative�
TRT�
22�h
e.g.�W550�(by�Pfinder)�
1
18�
Preservative�remover�
Tmax�
22�h
e.g.�Friapol�750�(by�Pfinder)�
1
19�
Windscreen�washer�fluid�
TRT��
�
2�h
5�%�anionic�tensides,�distilled�water
�
20�
Vehicle�washing�chemicals
TRT�
2�h
CAS�25155�30�0
CAS�9004�82�4�
21�
Interior�cleaner�/�cockpit�spray�
TRT�
2�h
e.g.�Cockpit�spray�(by�Motip)�
1
22�
Glass�cleaner�
TRT�
2�h
CAS�111�76�2
23�
Wheel�cleaner�
TRT�
2�h
e.g.�Xtreme�(Sonax)�
1
24�
Cold�cleaning�agent�
TRT�
22�h
e.g.�P3�Solvclean�AK�(by�Henkel)�
1
25�
Acetone�
TRT�
10�min
CAS�67�64�1
26�
Cleaning�solvent�
TRT�
10�min
DIN�51�635
27�
Ammonia�cleaner�
TRT�
22�h
e.g.�Ajax�(by�Henkel)�
1
�
28�
Denatured�alcohol�
TRT�
10�min
CAS�64�17�5�(ethanol)
29�
Contact�spray�
Tmax�
22�h
e.g.�WD�40�
1
30�
Sweat�
TRT�
22�h
DIN�53�160
31�
Cosmetic�products,�e.g.�creams�
TRT�
22�h
e.g.�Nivea,�Kenzo�
1
32�
Beverages�containing�caffeine�and�sugar�
TRT�
22�h
Cola
�
33�
Runway�de�icer�
TRT�
2�h
SAE�AMS�1435A
34�
E85�fuel��
TRT�
10 min
DIN�51625
�
Other�chemicals�
�
�
1)�Manufacturer�intended�as�example,�exact�chemicals�shall�be�agreed�with�the�responsible�department�
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 72 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 120 of 160
 
 
Table 88: Types of application 
Code number 
Application method
I  
Spraying 
II  
Brushing 
III  
Wiping (e.g. with cotton cloth)
IV  
Pouring 
V  
Dipping 
VI 
Immersing 
15.1.3 
Requirement 
The DUT shall be fully functional before and after the test and all parameters shall 
meet the specifications. Verification is done by means of a parameter test (large) as 
per Section 10.4. 
 
Changes to labels and markings shall be documented in the test report and 
coordinated with the buyer. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 73 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 121 of 160
 
16 Life tests 
16.1 L-01 Life test: mechanical/hydraulic endurance test 
16.1.1 
Purpose 
This test simulates the actuation/operating cycles of the component during the 
vehicle service life.  
The test is intended to verify the quality and reliability of the component with respect 
to operating/actuation cycles, e.g. brake actuation, seat adjustment cycles, 
switch/pushbutton actuation.  
16.1.2 
Test 
Test details shall be defined in the Component Requirement Specifications in line 
with the operating/actuation cycle. 
Table 89: Test parameters L-01 Life test: mechanical/hydraulic endurance test 
Operating mode of 
DUT 
Operating mode II.c corresponding to operating/actuation 
cycle 
Test temperature 
The operating/actuation cycles shall be performed at the 
temperatures specified in the temperature distribution 
profile, the number of cycles at each temperature 
corresponding to the percentage share associated with that 
temperature. 
Number of 
actuation/operating 
cycles 
As specified in the Component Requirement Specifications 
Number of DUTs 
6 
 
For components attached to a coolant circuit the coolant temperature shall follow the 
relevant test temperature between Tcool,min and Tcool,max . Outside the coolant 
temperature limits, only the ambient temperature shall be varied. 
16.1.3 
Requirement 
The DUT shall be fully functional before, during and after the test and all key 
parameters shall meet the specifications. Verification shall be done using continuous 
parameter monitoring. Intermediate measurements at 25 %, 50 % and 75 % of the 
test duration and parameter tests as per the test sequence plan shall only be carried 
out if the functions of the component cannot be sufficiently monitored during the test. 
 
The intermediate measurements shall be carried out as a parameter test (large). 
 
The data acquired from continuous parameter monitoring shall be assessed for drifts, 
trends and conspicuous behavior or anomalies. 
 
For components connected to a coolant circuit: 
For components with coated copper parts in the coolant path these copper parts shall 
be investigated after the test by means of a stereomicroscope using 20x 
magnification. Identifiable defects and copper corrosion are not permitted. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 74 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 122 of 160
 
16.2 L-02 Life test: high-temperature endurance test 
16.2.1 
Purpose 
This accelerated test simulates the thermal exposure of the component during 
vehicle service life. 
The test is intended to verify the quality and reliability of the component with respect 
to faults that occur due to thermal exposure such as diffusion, migration and 
oxidation. 
16.2.2 
Test 
16.2.2.1 Test for components not connected to a coolant circuit and 
without reduced performance at high temperatures 
This test shall be carried out in accordance with DIN EN 60068 -2-2 with the following 
parameters: 
Table 90: Test parameters L-02 Life test: high-temperature endurance test - Test for 
components not connected to a coolant circuit and without reduced performance at high 
temperatures 
Operating mode of 
DUT 
Intermitting between 47 h operating mode II.c and 1 h  
operating mode II.a 
Test duration 
For each relevant working condition according to Section 10.1, 
the respective part test duration shall be calculated according 
to Annex C.1 (Arrhenius model); the working conditions 
parking / off-grid parking shall not normally be considered in 
this process. 
The total test duration is the sum of all part test durations and 
specified in the Component Requirement Specifications. 
Test temperature 
Tmax 
Number of DUTs 
6  
 
16.2.2.2 Test for components not connected to a coolant circuit and with 
reduced performance at high temperatures 
For components with reduced performance (e.g. reduction of backlight of LCDs) at 
high temperatures starting at Top,max, the test according to Table 91 shall not be 
carried out at a constant test temperature of Tmax, but with a temperature profile with 
the following parameters: 
 
This test shall be carried out in accordance with DIN EN 60068 -2-2 with the following 
parameters: 
 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 75 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 123 of 160
 
Table 91: Test parameters L-02 Life test: high-temperature endurance test - Test for 
components not connected to a coolant circuit and with reduced performance at high 
temperatures 
Operating mode of 
DUT 
As per Figure 43
Test duration 
For each relevant working condition according to Section 10.1, 
the respective part test duration shall be calculated according 
to Annex C.3 (Arrhenius model for use with components with 
reduced performance at high temperatures); the working 
conditions parking / off-grid parking shall not normally be 
considered in this process. 
The total test duration is the sum of all part test durations and 
specified in the Component Requirement Specifications. 
The ramp times between Tmax and Top, max shall not be included 
in the test duration. 
Test temperature 
As per Figure 43 
Interval time t1 
To be calculated in accordance with Annex C.3 and to be 
specified in the Component Requirement Specifications. 
Interval time t2 
To be calculated in accordance with Annex C.3  and to be 
specified in the Component Requirement Specifications. 
Number of DUTs 
6 
 
 
Figure 43: Temperature profile for testing components with reduced performance at high 
temperatures.  
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 76 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 124 of 160
 
16.2.2.3 Test for components connected to a coolant circuit 
This test shall be carried out in accordance with DIN EN 60068 -2-2 with the following 
parameters: 
 Table 92: Test parameters L-02 Life test: high-temperature endurance test - Test for 
components connected to a coolant circuit 
Operating mode of 
DUT 
Intermitting between 47 h operating mode II.c and 1 h  
operating mode II.a 
Test duration 
For each relevant working condition according to Section 10.1, 
the respective part test duration shall be calculated according 
to Annex C.5 (Arrhenius model for use with components 
connected to coolant circuits); the working conditions parking / 
off-grid parking shall not normally be considered in this 
process. 
The total test duration is the sum of all part test durations and 
specified in the Component Requirement Specifications. 
Ambient test  
temperature 
According to Annex C.5 (Arrhenius model for use with 
components connected to coolant circuits) 
Test temperature 
coolant 
According to Annex C.5 (Arrhenius model for use with 
components connected to coolant circuits) 
Number of DUTs 
6  
 
16.2.3 
Requirement 
The DUT shall be fully functional before, during and after the test and all key 
parameters shall meet the specifications. Verification shall be done using continuous 
parameter monitoring. Intermediate measurements at 25 %, 50 % and 75 % of the 
test duration and parameter tests as per the test sequence plan shall only be carried 
out if the functions of the component cannot be sufficiently monitored during the test. 
 
The intermediate measurements shall be carried out as a parameter test (large). 
 
The data acquired from continuous parameter monitoring shall be assessed for drifts, 
trends and conspicuous behavior or anomalies. 
 
For components connected to a coolant circuit: 
For components with coated copper parts in the coolant path these copper parts shall 
be investigated after the test by means of a stereomicroscope using 20x 
magnification. Identifiable defects and copper corrosion are not permitted.  
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 77 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 125 of 160
 
16.3 L-03 Life test: temperature cycle endurance test 
16.3.1 
Purpose 
This accelerated test simulates the thermomechanical exposure of the component as 
a result of temperature changes that occur during vehicle service life. 
The test is intended to verify the quality and reliability of the component with respect 
to faults that occur due to thermomechanical exposure such as aging and cracking in 
soldered joints, adhesive joints and welded joints, in bond connections as well as in 
seals or housings. 
16.3.2 
Test 
This test shall be carried out in accordance with DIN EN 60068-2-14 with the 
following parameters: 
 
16.3.2.1 Test for components not connected to a coolant circuit and 
without reduced performance at low or high temperatures 
 
Table 93: Test parameters L-03 Life test: temperature cycle endurance test - Test for 
components not connected to a coolant circuit and without reduced performance at low or 
high temperatures 
Operating mode 
of DUT 
Intermitting between operating mode II.a and operating mode II.c 
as per Figure 44. 
Temperature 
profile 
As per Figure 44. 
Minimum test 
temperature 
Tmin  
Maximum test 
temperature 
Tmax  
Temperature 
gradient 
4 °C/min 
If the temperature gradient cannot be realized in the test 
equipment, it may be reduced to a minimum of 2 °C/min following 
discussions with the buyer. 
Holding times at 
Tmin and Tmax 
15 min following attainment of complete thermal equilibrium (see 
Section 0) 
Number of 
cycles 
The total number of test cycles shall be calculated considering all 
relevant working conditions (Section 10.1) according to Annex D.1 
(Coffin-Manson model) and specified in the Component 
Requirement Specifications. 
Number of 
DUTs 
6 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 78 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 126 of 160
 
 
Figure 44: Temperature profile L-03 Life test: temperature cycle endurance test for components 
not connected to a coolant circuit and without reduced performance at low or high 
temperatures 
 
******** Test for components not connected to a coolant circuit and with 
reduced performance at low or high temperatures 
For components with reduced performance (e.g. reduction of backlight of LCDs) at 
low or high temperatures below Top,min and above Top,max, the test shall be performed 
with the following parameters: 
 
Table 94: Test parameters L-03 Life test: temperature cycle endurance test – Test for 
components not connected to a coolant circuit and with reduced performance at low or high 
temperatures 
Operating mode of 
DUT 
Operating mode II.a and operating mode II.c as per Figure 45 
Temperature profile 
As per Figure 45 
Minimum test 
temperature 
Tmin 
Maximum test 
temperature 
Tmax  
Temperature 
gradient 
4 °C/min 
  
Holding times at 
Tmin, Tmax, Top,min and 
Top,max  
15 min following attainment of complete thermal equilibrium 
(see Section 0) 
Number of cycles 
The total number of test cycles shall be calculated considering 
all relevant working conditions (Section 10.1) according to 
Annex D.1 (Coffin-Manson model) and specified in the 
Component Requirement Specifications. 
Number of DUTs 
6 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 79 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 127 of 160
 
 
Figure 45: Temperature profile – Test for components with reduced performance at low or high 
temperatures  
 
16.3.2.3 Test for components connected to a coolant circuit 
The test for components connected to coolant circuits shall be carried out with the 
following parameters: 
 
Table 95: Test parameters L-03 Life test: temperature cycle endurance test – Test for 
components connected to a coolant circuit 
Operating mode of 
DUT 
Operating mode II.a and operating mode II.c as per Figure 44 / 
Figure 45 
Temperature 
profile 
As per Figure 44 / Figure 45 
Minimum test 
temperature 
Tmin and Tcool, min 
Maximum test 
temperature 
Tmax and Tcool, max 
Temperature 
gradient 
4 °C/min 
  
Holding times at 
Tmin, Tmax, Top,min 
and Top,max  
15 min following attainment of complete thermal equilibrium 
(see Section 0) 
Number of cycles 
 The total number of test cycles shall be calculated considering 
all relevant working conditions (Section 10.1) according to 
Annex D.3 (Coffin-Manson model for use for components 
connected to coolant circuits) and specified in the Component 
Requirement Specifications. 
Number of DUTs 
6 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 80 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 128 of 160
 
16.3.3 
Requirement 
The DUT shall be fully functional before, during and after the test and all key 
parameters shall meet the specifications. Verification shall be done using continuous 
parameter monitoring. Intermediate measurements at 25 %, 50 % and 75 % of the 
test duration and parameter tests as per the test sequence plan shall only be carried 
out if the functions of the component cannot be sufficiently monitored during the test. 
 
The intermediate measurements shall be carried out as a parameter test (large). 
 
The data acquired from continuous parameter monitoring shall be assessed for drifts, 
trends and conspicuous behavior or anomalies. 
 
For components connected to a coolant circuit: 
For components with coated copper parts in the coolant path these copper parts shall 
be investigated after the test by means of a stereomicroscope using 20x 
magnification. Identifiable defects and copper corrosion are not permitted. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 81 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 129 of 160
 
Annex A (normative) 
Test sequence 
 
A.1 Test sequence plan 
 
A component-specific test sequence plan shall be defined in the Component 
Requirement Specifications. 
 
Tests that are not required for a component according to the test selection table shall 
be deleted from the test sequence plan. 
 
If a component-specific adaptation of the test sequence is required, the test 
sequence plan may be adapted. 
 
If, during sequential testing, the test Damp heat, cyclic (with frost) is performed in 
place of the test Damp heat, cyclic, the test Damp heat, cyclic (with frost) may, in 
consultation with the buyer, be omitted from the parallel tests. 
 
All components shall be tested with original connector or adapter starting with the test 
M-01 Free fall. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 82 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 130 of 160
 
A.2 Sequential tests 
 
Numerical data on the arrows denote the number of DUTs to be used. 
 
Figure 46: Test sequence plan – sequential tests 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 83 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 131 of 160
 
 
If the DUTs have not been damaged in the test M-01 Free fall, two DUTs shall be 
used for the further sequence test. Otherwise, the spare DUTs shall be used. 
 
If, during sequential testing, the test Damp heat, cyclic (with frost) is performed in 
place of the test Damp heat, cyclic, the test Damp heat, cyclic (with frost) may, in 
consultation with the buyer, be omitted from the parallel tests. 
 
All components shall be tested with original connector or adapter starting with the test 
M-01 Free fall.  
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 84 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 132 of 160
 
A.3 Tests outside the sequence (parallel tests) 
 
Numerical data on the arrows denote the number of DUTs to be used. 
 
Figure 47: Test sequence plan - parallel tests 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 85 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 133 of 160
 
A.4 Life tests 
 
 
L2
High-temperature endurance 
test
L3
Temperature cycle endurance 
test
L1
Mechanical/hydraulic endurance 
test
K-02
Temperature step test
K-02
Temperature step test
Physical analysis
6
6
6
25%
50%
75 %
Parameter test 
25%
50%
75 %
Parameter test
25%
50%
75 %
Parameter test
K-01
High/low temperature 
storage
Parameter test for 
environmental tests (large)
Parameter test for 
environmental tests (large)
Leakage Test
Leakage Test
 
Numerical data on the arrows denote the number of DUTs to be used. 
Figure 48: Test sequence plan - service life  
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 86 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 134 of 160
 
Annex B (normative) 
Typical temperature distribution profiles for different installation 
areas
 
Table 96: Overview of installation locations, typical distribution profiles and temperature deltas 
Installation location of the component 
Profile no. 
Temperature delta in K 
Interior, without special requirement 
1 
36 
Body-mounted part, without special 
requirements 
1 
36 
Interior exposed to solar radiation 
2 
46 
Body-mounted part, roof 
2 
46 
Engine compartment, but not on the engine 
3 
60 
On the radiator 
3 
60 
Engine-mounted parts 
4 
75 
Gearbox-mounted parts 
4 
75 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 87 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 135 of 160
 
B.1 
Temperature distribution profile 1 
Table 97: Temperature distribution profile 1  
Temperature in °C 
Distribution in % 
-40 °C  
6 %  
23 °C  
20 % 
40 °C  
65 % 
75 °C  
8 % 
80 °C  
1 % 
B.2 
Temperature distribution profile 2 
Table 98: Temperature distribution profile 2  
Temperature in °C Distribution in % 
-40 °C  
6 %  
23 °C 
20 % 
50 °C 
65 % 
100 °C 
8 % 
105 °C 
1 % 
B.3 
Temperature distribution profile 3 
Table 99: Temperature distribution profile 3 
Temperature in °C Distribution in % 
-40 °C  
6 %  
23 °C  
20 % 
65 °C  
65 % 
115 °C  
8 % 
120 °C  
1 % 
B.4 
Temperature distribution profile 4 
Table 100: Temperature distribution profile 4 
Temperature in °C  Distribution in % 
-40 °C  
6 %  
23 °C 
20 % 
85 °C 
65 % 
135 °C 
8 % 
140 °C 
1 % 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 88 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 136 of 160
 
Annex C (normative) 
 
Calculation models for the life test
'High-temperature endurance test' 
 
C.1 
Arrhenius model 
The calculation of the test duration of the high-temperature endurance test life test is 
based on the temperature distribution profile according to the mission profile in the 
Component Requirement Specifications. 
Table 101: Temperature distribution profile 
Temperature in °C 
Distribution in % 
Tfield.1 
p1 
Tfield.2 
p2 
… 
… 
Tfield.n 
pn 
 
and the operating hours top of the vehicle in the field.  
 
For each temperature Tfield,1 … Tfield,n an acceleration factor AT,1 … AT,n is calculated 
on the basis of the following equation: 
 
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
���
�
�
�
��
�
�
� �
�
�
�
273,15
T
1
273,15
T
1
k
E
e
A
field,i
test
A
T,i
 
(1) 
where: 
AT,i 
Acceleration factor of the Arrhenius model 
EA 
Activation energy EA = 0,45 eV 
k 
Boltzmann constant (k = 8,617 x 10-5 eV/K) 
Ttest 
Test temperature in [°C], normally Tmax
Tfield,i 
Field temperature in [°C] according to temperature distribution profile 
based on mission profile 
-273,15 °C Absolute zero of the temperature 
 
The total test duration for the high-temperature endurance test results from the 
acceleration factor according to 
 
� �
�
i
i
T,
i
op
test
A
p
t
t
  
 
(2) 
where: 
ttest Test duration (hours) of the high-temperature endurance test life test 
top 
Operating time (hours) in the field 
pi 
Percentage of the operating time for which the component is operated at the 
temperature Tfield,i in the field. 
AT,i Acceleration factor for temperature Tfield,i 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 89 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 137 of 160
 
C.2 
Example Arrhenius model:  
For a control unit with the temperature distribution profile indicated in the following 
table 
 Table 102: Sample temperature distribution profile 
Temperature in °C Distribution in % 
-40 
6 
23 
20 
60 
65 
100 
8 
105 
1 
 
and an operating time of 8�000 h, the test duration for the life test high-temperature 
endurance test is calculated as follows: 
 
Using equation (1) and TTest = Tmax = 105 °C, the acceleration factors AT,i for all five 
temperatures (see Table 102) of the temperature distribution profile indicated above 
are calculated: 
 
 
AT,1 = 5369
AT,2 = 45,8 
AT,3 = 6,46 
AT.4 = 1,20 
AT,5 = 1,00 
 
The operating time of the component is top = 8�000 h. 
 
The total test duration of the life test high-temperature endurance test results from 
equation (2) as: 
 
 
 1452 hours
1,00
0,01
1,20
0,08
6,46
0,65
45,8
0,20
5369
0,06
8000  hours 
t test
� �
�
�
�
�
�
�
�
�
�
�
�
. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 90 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 138 of 160
 
C.3 
Arrhenius model for use with components with reduced performance at 
high temperatures 
To calculate the test duration for the life test high-temperature endurance for 
components with reduced performance at high temperatures starting with Top,max, the 
temperature distribution profile according to the mission profile in the Component 
Requirement Specification is divided into the two temperature ranges T � Top,max and 
T>Top,max: 
  
Table 103: Temperature distribution profile for T � Top,max with test temperature Top,max
Temperature in °C 
Distribution in % 
Tfield.1 
p1 
Tfield.2 
p2 
… 
… 
Tfield.m (� Top,max) 
pm 
 
Table 104: Temperature distribution profile for Top,max < T � Tmax with test temperature Tmax
Temperature in °C 
Distribution in % 
Tfield.m+1(> Top,max) 
pm+1 
Tfield.m+2 
pm+2 
… 
… 
Tfield.n 
pn 
 
For each temperature Tfield,1 … Tfield,m … Tfield,n, an acceleration factor AT,1 … AT,m … 
AT,n is calculated by means of equation (1), where for temperature range T � Top,max a 
test temperature of TTest=Top, max and for temperature range T>Top,max a test 
temperature of TTest=Tmax is assumed. 
 
The required test duration top, max at test temperature Top, max results from equation (2) 
with i=1 …m. 
 
The required test duration tmax at test temperature Tmax results from equation (2) with 
i=m+1 …n. 
 
The total test duration ttot is the sum of top, max and tmax. 
 
To ensure a test close to reality, the test is carried out intermittently at the test 
temperatures Top, max and Tmax (see Figure 43). 
The typical interval of 48 h is divided using the ratio of the part test durations top, max 
and tmax. 
 
 
m < n
m < n
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 91 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 139 of 160
 
C.4 
Example Arrhenius model for use with components with reduced 
performance at high temperatures:
The temperature distribution profile according to Table 105 and Table 106 applies to 
the control unit. The test duration for the life test high-temperature endurance test for 
components with reduced performance starting with a temperature of Top, max = 90 °C 
for an operating time of 8�000 h is calculated as follows: 
The percentage temperature distribution profile according to the mission profile is 
divided into the two ranges T � Top,max and T>Top,max: 
 
Table 105: Sample temperature distribution profile for T � 90 °C 
Temperature in °C 
Distribution in % 
-40 
6 
23 
20 
60 
65 
 
Table 106: Sample temperature distribution profile for T > 90 °C 
Temperature in °C 
Distribution in % 
100 
8 
105 
1 
 
By means of equation (1) and TTest = 90 °C, the acceleration factors AT,i for all 
temperatures T � 90 °C (see Table 105) of the first part of the temperature 
distribution profile are calculated: 
AT,1 = 3060,78
AT,2 = 25,95 
AT,3 = 3,65 
 
This results in a test duration top, max at a test temperature of Top,max = 90 °C of 
 
 
 1485 hours
3,65
0,65
25,95
0,2
3060,78
0,06
8000 hours 
90 C)
(T
t
test
op,max
� �
�
�
�
�
�
�
�
�
�
�
�
 
 
By means of equation (1) and TTest = 105 °C, the acceleration factors AT,i for all 
temperatures T >90 °C (see Table 106) of the second part of the temperature 
distribution profile are calculated: 
AT.4 = 1,20
AT,5 = 1,00
 
This results in a test duration tmax at a test temperature of Tmax = 105 °C of 
 
 
 612 hours
1,00
0,01
1,20
0,08
8000 hours 
105 C)
(T
t
test
max
� �
�
�
�
�
�
�
�
�
�
�
 
 
The total test duration for the life test high-temperature endurance test is the sum of 
the two test durations 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 92 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 140 of 160
 
 
  hours
   2097
  hours
   612
  hours
1485
t
t
t
max
op, max
tot
�
�
�
�
�
 
 
The test shall be carried out as per Figure 43 intermittently at the test temperatures 
Top, max / Tmax with the intervals 
 
 
 
t1 = 48 h * top,max / ttot = 48 h * 1485/2097 = 34 h 
 
t2 = 48 h * tmax / ttot = 48 h * 612/2097 = 14 h. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 93 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 141 of 160
 
C.5 Arrhenius model for use with components connected to coolant circuits 
 
For components connected to the coolant circuit all relevant working conditions i (see 
Figure 22; i corresponds to the consecutive number of the working conditions) with 
their corresponding temperature distributions for ambient temperature and coolant 
circuit shall be considered. 
As described below, for the life test high-temperature endurance the test durations 
and test temperatures for the ambient temperature and the coolant circuit shall be 
calculated for each relevant working condition i; the total test duration is the sum of 
all part test durations for each relevant working condition i. 
 
To calculate the test duration for each relevant working condition i, the test duration 
for the ambient temperature and the coolant circuit shall initially be calculated 
separately according to the Arrhenius model as per Annex C.1 / C.3. 
Given that the resulting test durations ttest,ambient and ttest, CC , normally differ, an 
equalization of the test durations between ambient temperature and coolant circuit  is 
necessary, because the component can only be tested at a consistent test duration 
for the each working condition i. 
 
In doing so the shorter of the two test durations ttest, ambient and ttest, CC shall be adapted 
to the longer test duration by separating the test into at least two partial tests and by 
decreasing the test temperatures in all partial tests except for one, according to the 
following iteration method. 
 
Case A: ttest, ambient < ttest, CC
 
Test duration: 
For ttest, ambient < ttest, CC , the test duration for working condition i is 
ttest, condition i = ttest, CC. 
 
Test temperature coolant: 
The test temperature shall be selected according to the Arrhenius model as per 
Annex C.1 (normally Tcool,max). 
Test temperatures ambient temperature: 
The test temperatures shall be calculated iteratively according to the following 
algorithm based on the temperature distribution profile of the ambient temperature of 
the working condition i considered (Table 107). 
 
Table 107: Temperature distribution profile ambient 
Temperature in °C 
Distribution in % 
Tfield.1 
p1 
Tfield.2 
p2 
… 
… 
Tfield.n 
pn 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 94 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 142 of 160
 
1. Start of iteration (m = 0): 
The first partial test shall be carried out at test temperature Tfield, n with the test 
duration ttest, T_field, n = toperation* pn (where toperation corresponds to the field 
operating time of the considered working condition i in hours). 
 
2. First iteration (m = 1): 
The 1st partial test covers a part of the test duration of working condition i ttest, 
condition i, so that a remaining test duration results, which shall be covered by the 
further partial tests: 
tRest, 1 = ttest, condition i – ttest, T_field, n. 
 
Additionally the first partial test covers the portion pn of the temperature 
distribution of the ambient temperature. Therefore this portion pn shall be set 
to pn = 0 for further calculation. 
To define the test temperature for the 2nd partial test (m = 1) first of all the test 
temperature Tadapted shall be determined by means of the Arrhenius model 
according to C.1 / C.3 in such a way that for the ambient temperature 
distribution (adapted with pn = 0) a test duration equal to the remaining test 
duration of tRest, 1 results. 
If the resulting adapted test temperature is Tadapted < Tfield, n-1, the 2nd partial 
test shall be carried out at the test temperature Tfield, n-1 for the test duration 
ttest, T_field, n-1 = toperation * pn-1 
and at least one additional iteration step shall be carried out. 
However, if the resulting adapted test temperature is Tadapted > Tfield, n-1, the 2nd 
partial test shall be carried out at the test temperature Tadapted for the test 
duration 
ttest, T_field, n-1 = trest, 1 
and no further iteration step needs to be carried out (end of iteration). 
 
3. Further iterations (m = 2, 3, …) 
The first m partial tests cover a part of the test duration for the working 
condition i ttest, condition i, so that a remaining test duration results, which shall be 
covered by the further partial tests: 
�
�
�
�
�
�
1
0
,
,
,
,
,
m
k
test T
prüf condition i
m
rest
field n k
t
t
t
 
Additionally the first m partial tests cover the portions pn-k with 
k = 0, 1, …, (m-1) of the temperature distribution of the ambient temperature. 
Therefore these portions pn-k shall be set to  
pn-k = 0 for the further calculations. 
To define the test temperature for the (m+1)-th partial test, first of all the test 
temperature Tadapted shall be determined by means of the Arrhenius model 
according to C.1 / C.3 in such a way that for the ambient temperature 
distribution (adapted with pn-k = 0) a test duration equal to the remaining test 
duration of trest, m results. 
If the resulting adapted test temperature is Tadapted < Tfield, n-m, the (m+1)-th 
partial test shall be carried out at the test temperature Tfield, n-m for the test 
duration 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 95 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 143 of 160
 
ttest, T_field, n-m = toperation * pn-m 
and at least one additional iteration step shall be carried out. 
However, if the resulting adapted test temperature is Tadapted > Tfield, n-m, the 
(m+1)-th partial test shall be carried out at the test temperature Tadapted for the 
test duration 
ttest, T_field, n-m = trest, m 
and no further iteration step needs to be carried out (end of iteration). 
 
 
Case B: ttest, ambient > ttest, CC
 
Test duration: 
For ttest, ambient > ttest, CC , the test duration for working condition i is 
ttest, condition i = ttest, ambient. 
 
Ambient test temperature: 
The test temperature shall be selected according to the Arrhenius model as per 
Annex C.1 / C.3 (normally Tmax / Tmax and Top, max). 
 
Test temperature coolant: 
The test temperatures shall be calculated iteratively based on the temperature 
distribution profile of the coolant temperature of the considered working condition i 
(Table 108). 
 
Table 108: Temperature distribution profile coolant temperature 
Temperature in °C 
Distribution in %  
Tfield.1 
p1 
Tfield.2 
p2 
… 
… 
Tfield.n 
pn 
 
1. Start of iteration (m = 0): 
The first partial test shall be carried out at test temperature Tfield, n with the test 
duration ttest, T_field, n = toperation* pn (where toperation corresponds to the field 
operating time of the considered working condition i in hours). 
 
2. First iteration (m = 1): 
The first partial test covers a part of the test duration of working condition i ttest, 
condition i, so that a remaining test duration results, which shall be covered by the 
further partial tests: 
trest, 1 = ttest, condition i – ttest, T_field, n. 
 
Additionally the first partial test covers the portion pn of the temperature 
distribution of the coolant temperature. Therefore this portion pn shall be set to 
pn = 0 for further calculation. 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 96 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 144 of 160
 
To define the test temperature for the second partial test (m = 1) first of all the 
test temperature Tadapted shall be determined by means of the Arrhenius model 
according to C.1 in such a way that for the coolant temperature distribution 
(adapted with pn = 0) a test duration equal to the remaining test duration of trest, 
1 results. 
If the resulting adapted test temperature is Tadapted < Tfield, n-1, the second partial 
test shall be carried out at the test temperature Tfield, n-1 for the test duration 
ttest, T_field, n-1 = toperation * pn-1 
and at least one additional iteration step shall be carried out. 
However, if the resulting adapted test temperature is Tadapted > Tfield, n-1, the 2nd 
partial test shall be carried out at the test temperature Tadapted for the test 
duration 
ttest, T_field, n-1 = tRest, 1 
and no further iteration step needs to be carried out (end of iteration). 
 
 
3. Further iterations (m = 2, 3, …) 
The first m partial tests cover a part of the test duration for the working 
condition i ttest, condition i, so that a remaining test duration results, which shall be 
covered by the further partial tests: 
�
�
�
�
�
�
1
0
,
,
,
,
m
k
test T
test operation i
m
rest
field n k
t
t
t
 
Additionally the first m partial tests cover the portions pn-k with 
k = 0, 1, …, (m-1) of the temperature distribution of the ambient temperature. 
Therefore these portions pn-k shall be set to  
pn-k = 0 for the further calculations. 
To define the test temperature for the (m+1)-th partial test, first of all the test 
temperature Tadapted shall be determined by means of the Arrhenius model 
according to C.1 in such a way that for the coolant temperature distribution 
(adapted with pn-k = 0) a test duration equal to the remaining test duration of 
trest, m results. 
If the resulting adapted test temperature is Tadapted < Tfield, n-m, the (m+1)-th 
partial test shall be carried out at the test temperature Tfield, n-m for the test 
duration 
ttest, T_field, n-m = toperation * pn-m 
and at least one additional iteration step shall be carried out. 
However, if the resulting adapted test temperature is Tadapted > Tfield, n-m, the 
(m+1)-th partial test shall be carried out at the test temperature Tadapted for the 
test duration 
ttest, T_field, n-m = trest, m 
and no further iteration step needs to be carried out (end of iteration). 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 97 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 145 of 160
 
C.6 
Example Arrhenius model for use with components connected to coolant 
circuits
 
For a control unit attached to the coolant circuit with the following temperature 
distribution profile for the ambient temperature and the coolant temperature 
Table 109: Sample temperature distribution profile ambient temperature 
Temperature in °C Distribution in % 
-40 
6 
23 
20 
50 
65 
100 
8 
105 
1 
 
Table 110: Sample temperature distribution profile coolant temperature 
Temperature in °C Distribution in % 
-40 
6 
23 
20 
40 
65 
75 
8 
80 
1 
 
and an operating time of 8�000 h, the test duration for the life test high-temperature 
endurance test is calculated as follows: 
 
Test duration: 
Calculation of the test duration for ambient temperature and coolant temperature 
using the Arrhenius model:  
ttest, ambient = 1143 h 
ttest, CC = 2009 h 
As ttest, ambient <  ttest, CC the calculation is carried out according to case A of Annex C.5. 
The test duration for the ambient temperature shall be adapted to ttest, condition,i = ttest, CC 
= 2009 h. 
 
Test temperature coolant: 
According to the temperature distribution profile, the test temperature for the coolant 
is TCC, max = Tfield, 5 = 80°C. 
 
Iterative calculation of test temperatures ambient temperature: 
1. Start of iteration: 
The first partial test is carried out at Tfield, 5 = 105 °C. The test duration is 
ttest, T_field, 5 = toperation*p5 = 8000 h * 1 % = 80 h. 
 
2. First iteration: 
The first partial test already covered a part of the test duration of 
working condition i ttest, condition i; the remaining test duration shall 
therefore be recalculated as follows: trest, 1 = ttest, condition i – ttest, T_field, 5 = 
2009 h – 80 h = 1929 h. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 98 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 146 of 160
 
As the first partial test covers the portion p5 of the temperature 
distribution, p5 is set to p5 = 0 in the following calculations according to 
Arrhenius, as per the following table. 
 
Table 111: Adapted ambient temperature distribution profile after the first partial test 
Temperature in °C 
Distribution in % 
-40 
6 
23 
20 
50 
65 
100 
8 
105 
0 
 
To define the test temperature for the 2nd partial test, the test 
temperature Tadapted shall be calculated by means of the Arrhenius 
model according to C.1 in such a way that a test duration equal to the 
remaining test duration of trest, 1 = 1929 h results. Taking into account 
the adapted temperature distribution of the ambient temperature the 
necessary test duration results of 1929 h at a temperature of Tadapted = 
89,5°C (exact value: 89,46 °C). 
However, as Tadapted < Tfield, 4 (i.e. 89,5 °C < 100 °C), the 2nd partial test 
shall be carried out at test temperature Tfield, 4 = 100 °C. 
The test duration for the 2nd partial test is ttest, T_field, 4 = toperation*p4 = 
8000 h * 8 % = 640 h. 
 
3. Second iteration 
The 2nd partial test covered another part of the test duration of working 
condition i ttest, condition i, the remaining test duration therefore results as: 
trest, 2 = ttest, condition i – (ttest, T_field, 5 + ttest, T_field, 4) = 2009 h – 80 h - 640 h= 
1289 h. 
The first and second partial tests covered the portions p5 and p4 of the 
ambient temperature distribution profile. Therefore these portions are 
set to p4 = p5 = 0 for the further iteration, as per the following table. 
 
Table 112: Adapted ambient temperature distribution profile after the 1st and 2nd partial test 
Temperature in °C 
Distribution in % 
-40 
6 
23 
20 
50 
65 
100 
0 
105 
0 
 
To define the test temperature for the 3rd partial test, the test 
temperature Tadapted shall be calculated by means of the Arrhenius 
model according to C.1 in such a way that a test duration equal to the 
remaining test duration of trest, 2 = 1289 h results. Taking into account 
the adapted temperature distribution of the ambient temperature the 
necessary test duration results of 1289 h at a temperature of Tadapted = 
82 °C (exact value: 82,17 °C). 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 99 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 147 of 160
 
As Tadapted > Tfield, 3 (i.e. 82 °C > 50 °C), no further iteration is necessary. 
The 3rd and last partial test shall therefore be carried out at  
Tadapted = 82 °C for the test duration ttest, T_field, 3 = tRest, 3 = 1289 h. 
 
Altogether 80 h shall be tested at 105 °C ambient temperature, 640 h at 100°C 
ambient temperature and 1289 h at 82 °C ambient temperature. 
In this example the coolant temperature is constantly 80 °C over the entire test 
duration. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 100 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 148 of 160
 
Annex D (normative) 
Calculation models for the life test 'Temperature cycle endurance test' 
 
D.1 
Coffin-Manson model  
The calculation of the test duration for the life test temperature cycle endurance test is 
based on the average temperature change of the component in the field �Tfield (refer to 
Table 96) and the number of temperature cycles during service life in the field 
NTempCyclesfield.  
Typically 2 temperature changes per day can be assumed for the number of 
temperature cycles in the field. This results in: 
 
NTempCyclesfield = 2 * 365 * 15 (years) = 10 950 cycles 
 
Depending on the average temperature change in the field, the acceleration factor of 
the Coffin-Manson model is calculated as follows: 
 
c
�T
�T
A
field
test
CM
��
�
�
��
�
�
�
(3) 
where: 
 
ACM 
Acceleration factor of the Coffin-Manson model 
�Ttest Temperature difference during a test cycle (�Ttest = Tmax - Tmin) 
�Tfield Average temperature difference during service life in the field 
c 
Parameter of the Coffin-Manson model 
In this standard a fixed value of 2,5 is used for c 
 
 
The total number of test cycles is calculated according to 
 
CM
field
TempCycles
test
A
N
N
�
   
(4) 
 
where: 
Ntest 
Required number of test cycles 
NTempCyclesfield Number of temperature cycles during service life in the field 
ACM 
Acceleration factor of the Coffin-Manson model according to equation (3)
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 101 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 149 of 160
 
D.2 
Example:  
For a control unit with Tmin = -40 °C and Tmax = 105 °C, a service life of 15 years in the 
field and an average temperature difference in the field of �Tfield = 40 °C, the number of 
test cycles (Ntest) is calculated as described below: 
 
1. The number of temperature cycles in the field:  
 
NTempCyclesfield = 2 * 365 * 15 (years) = 10 950 cycles 
 
2. Temperature difference during a test cycle: 
 
�Ttest = 105 °C – (-40 °C)= 145 °C. 
 
3. The acceleration factor of the Coffin-Manson model is calculated as ACM = 25,02 
by means of equation (3). 
 
4. This results in a number of test cycles by means of equation (4) of: 
 438  cycles
25,02
10950  cycles
Ntest
�
�
 
 
5. The soak time is the time until  the component attains complete thermal 
equilibrium plus 15 min. Assuming that the component attains complete thermal 
equilibrium after 20 min, the soak time is 35 min.  
 
6. This results in a cycle time: 
����������� ������ � �����
��������
� ������������
 
7. In the example: 
����������� �
�����������������
�������
� ������ � ��������� 
8. For 438 cycles the total test duration is therefore 1040 h. 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 102 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 150 of 160
 
D.3 
Coffin-Manson model for use with components connected to coolant 
circuits
 
For components connected to the coolant circuit all relevant working conditions i (see 
Figure 22; i corresponds to the consecutive number of the working conditions) with its 
corresponding temperature deltas for ambient temperature and coolant circuit shall be 
considered. 
As described below, for the life test temperature cycle endurance the minimum and 
maximum temperatures and the number of test cycles shall be calculated for each 
relevant working condition i; the total number of test cycles is the sum of all part test 
cycles for each relevant working condition i. 
 
To calculate the test duration for each relevant working condition i, the number of test 
cycles for the ambient temperature and the coolant circuit shall initially be calculated 
separately according to the Coffin-Manson model as per Annex C.7. 
Given that the resulting numbers of test cycles Ntest, ambient and Ntest, CC normally differ, an 
equalization of the number of test cycles between ambient temperature and coolant 
circuit is necessary, because the component can only be tested at a consistent number 
of test cycles for the each working condition i. 
 
In doing so the smaller of the two numbers of test cycles Ntest, ambient and Ntest, CC shall be 
adapted to the larger number of test cycles as the test is separated into three partial 
tests. During this process, one partial test shall take place at full temperature delta 
between Tmin and Tmax; the two other tests shall take place at reduced temperature delta 
between Tmin and TRT / between TRT and Tmax. 
 
 
Case A: Ntest, ambient > Ntest, CC 
 
Number of test cycles: 
For Ntest, ambient > Ntest, CC, the number of test cycles for working condition i is 
Ntest, condition i = Ntest, ambient. 
 
 
Number of test cycles for coolant: 
 
The number of test cycles for the coolant Ntest, CC shall be adapted to the larger number 
of ambient test cycles Ntest, ambient. The test cycles shall be carried out at the following 
three temperature ranges: 
 
1. xCC test cycles shall be carried out between TCC, min and TCC, max. 
The acceleration factor ACM, CC, 1 shall be calculated according to the Coffin-
Manson model using �Ttest, 1 = TCC, max - TCC, min 
2. ½ * (Ntest, condition i - xCC) test cycles shall be carried out between TCC, min and TRT . 
The acceleration factor ACM, CC, 2 shall be calculated according to the Coffin-
Manson model using �Ttest, 2 = TRT - TCC, min. 
3. ½ * (Ntest, condition i - xCC) test cycles shall be carried out between TRT and TCC, max. 
The acceleration factor ACM, CC, 3 shall be calculated according to the Coffin-
Manson model using �Ttest, 3 = TCC, max – TRT. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 103 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 151 of 160
 
From 1 to 3, Ntest, condition i temperature cycles result in total. 
 
In analogy with equation (4) in Annex D.1, this results in:  
�
�
�
�
, 3
,
,
, 2
,
,
, 1
,
2
1
2
1
CM CC
CC
test condition i
CM CC
CC
test condition i
CM CC
CC
TempCyclesfield
A
x
N
A
x
N
A
x
N
�
�
�
�
�
�
�
�
�
�
 
From this equation, the number of test cycles xCC can be calculated as follows: 
 
�
�
�
, 3 �
,
, 2
,
, 1
,
, 3
,
, 2
,
,
2
1
2
CM CC
CM CC
CC
CM
CM CC
CM CC
test condition i
field
TempCycles
CC
A
A
A
A
A
N
N
x
�
�
�
�
�
�
�
 
 
 
Inserting xCC into points 1 to 3 above results in the number of test cycles for the three 
partial tests. 
 
If TCC, op, max < TCC, max or TCC, op,min > TCC, min or Tambient, op, max < Tambient, max or Tambient, op,min 
> Tambient, min, additional holding times at the respective temperatures shall be taken into 
account according to Figure 45 in Section 16.3.2.1.  
During a test the temperature cycles for the ambient temperature and the cooling circuit 
run synchronously. 
 
 
 
 
 
Case B: Ntest, ambient < Ntest, CC 
 
Number of test cycles: 
For Ntest, ambient < Ntest, CC , the number of test cycles for working condition i is 
Ntest, condition i = Ntest, CC. 
 
 
Number of ambient test cycles: 
 
The number of ambient test cycles Ntest, ambient shall be adapted to the larger number of 
test cycles for the coolant Ntest, CC. In this process the test cycles shall be carried out at 
the following three temperature ranges: 
 
1. xambient test cycles shall be carried out between Tambient, min and Tambient, max. The 
acceleration factor ACM, ambient , 1 shall be calculated according to the Coffin-
Manson model using �Ttest, 1 = Tambient, max – Tambient, min. 
2. ½ * (Ntest, condition i – xambient) test cycles shall be carried out between Tambient, min 
and TRT. The acceleration factor ACM, ambient, 2 shall be calculated according to the 
Coffin-Manson model using �Ttest, 2 = TRT – Tambient, min. 
3. ½ * (Ntest, condition i – xambient) test cycles shall be carried out between TRT and 
Tambient, max. The acceleration factor ACM, ambient, 3 shall be calculated according to 
the Coffin-Manson model using �Ttest, 3 = Tambient, max – TRT. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 104 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 152 of 160
 
From 1 to 3, Ntest, condition i temperature cycles result in total. 
 
In analogy with equation (4) in Annex D.1, this results in:  
�
�
�
�
, 3
,
,
, 2
,
,
, 1
,
2
1
2
1
CM ambient
ambient
condition i
test
CM ambient
ambient
test condition i
CM ambient
ambient
field
TempCycles
A
x
N
A
x
N
A
x
N
�
�
�
�
�
�
�
�
�
�
 
 
From this equation, the number of test cycles xambient can be calculated as follows: 
�
�
�
, 3 �
,
, 2
,
, 1
,
, 3
,
, 2
,
,
2
1
2
CM ambient
CM ambient
ambient
CM
CM ambient
CM ambient
test condition i
field
TempCycles
ambient
A
A
A
A
A
N
N
x
�
�
�
�
�
�
�
 
 
Inserting xambient into points 1 to 3 above results in the number of test cycles for the three 
partial tests. 
 
If Tambient, op, max < Tambient, max or Tambient, op,min > Tambient, min or TCC, op, max < TCC, max or TCC, 
op,min > TCC, min, additional holding times at the respective temperatures shall be 
considered, according to Figure 45 in Section 16.3.2.1. 
During a test the temperature cycles for the ambient temperature and the cooling circuit 
run synchronously. 
 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 105 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 153 of 160
 
D.4 
Example Coffin-Manson model for use with components connected to 
coolant circuits 
 
For a control unit connected to a coolant circuit having an ambient temperature range 
from Tambient, min = -40 °C to Tambient, max = 120 °C and a coolant temperature range from 
TCC, min = -40 °C to TCC, max = 80 °C, a service life in the field of 15 years, an average 
ambient temperature delta in the field of �Tfield, ambient = 60 K and an average coolant 
temperature delta in the field of �Tfield, CC = 36 K, the number of test cycles for working 
condition i can be calculated as follows: 
 
Number of ambient and coolant test cycles: 
The calculation of the numbers of ambient and coolant test cycles according to the 
Coffin-Manson model as per Annex D.1 results in the following values: 
 
Ntest, ambient = 943 cycles 
Ntest, CC = 540 cycles 
 
As Ntest, ambient > Ntest, CC, the number of test cycles for working condition i is Ntest, condition i = 
Ntest, ambient = 943 cycles. The number of test cycles for the coolant shall be adapted. 
 
Adaptation number of cycles coolant: 
Adapting the number of test cycles of the coolant to Ntest, condition i = 943 cycles is carried 
out in three parts: 
1. xCC test cycles shall be carried out between TCC, min = -40 °C and TCC, max = 80 °C. 
The acceleration factor ACM, CC, 1 calculated according to the Coffin-Manson 
model results in = 
5,2
36
)
( 40
80
�
�
�
�
�
�
�
�
� �
�
C
C
C
= 20,29. 
2. ½ * (943 – xCC) test cycles shall be carried out between TCC, min = -40 °C and TRT 
= 23 °C. The acceleration factor ACM, CC, 2 calculated according to the Coffin-
Manson model results in = 
5,2
36
)
( 40
23
�
�
�
�
�
�
�
�
� �
�
C
C
C
= 4,05. 
3. ½ * (943 – xCC) test cycles shall be carried out between TRT = 23 °C and TCC, max 
= 80 °C. The acceleration factor ACM, CC, 3 calculated according to the Coffin-
Manson model results in = 
5,2
36
23
80
�
�
�
�
�
�
�
�
�
�
C
C
C
= 3,15. 
 
For xCC the following number of cycles therefore results: 
 
�
�
�
, 3 �
,
, 2
,
, 1
,
, 3
,
, 2
,
,
2
1
2
CM CC
CM CC
CC
CM
CM CC
CM CC
test condition i
field
TempCycles
CC
A
A
A
A
A
N
N
x
�
�
�
�
�
�
�
 = 
�
�
�
315�
4 05
2
1
29
20
315
4 05
2
943
10950
,
,
,
,
,
�
�
�
�
�
�
 = 453 
cycles 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 106 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 154 of 160
 
The following numbers of test cycles therefore result for the three temperature ranges, 
calculated according to points 1 to 3: 
 
1. 453 test cycles shall be carried out between TCC, min = -40 °C and TCC, max = 80 °C 
2. 245 test cycles shall be carried out between TCC, min = -40 °C and TRT = 23 °C 
3. 245 test cycles shall be carried out between TRT = 23 °C and TCC, max = 80 °C 
 
Adding the partial test cycles results in the total number of test cycles for working 
condition i Ntest, condition i = 943 cycles. 
 
During a test the temperature cycles for the ambient temperature and the cooling circuit 
run synchronously. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 107 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 155 of 160
 
 
Annex E (normative) 
Calculation models for test  
'Damp heat, steady state - severity 2' 
 
E.1 
Lawson model 
The calculation of the test duration of the damp heat, steady state - severity 2 test is 
based on the average ambient humidity RHParking, field and the average temperature 
TParking, field of the component in the parked vehicle. 
Unless otherwise specified in the Component Requirement Specifications, the following 
values shall be assumed for the calculation: 
 
Table 113: Average ambient humidity and temperature in the parked vehicle 
Installation location 
Average ambient humidity 
in the parked vehicle 
RHParking, field 
Average temperature in the 
parked vehicle TParking, field 
In the passenger 
compartment/trunk 
60 % relative humidity 
23 °C 
Outside the passenger 
compartment/trunk 
65 % relative humidity 
23 °C 
  
Depending on the average ambient humidity and temperature in the field, the 
acceleration factor of the Lawson model is calculated as follows:  
 
 
��
�
�
�
��
�
�
�
�
�
�
�
�
�
��
�
��
� �
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
���
�
�
�
��
�
�
��
�
�
�
�
2
Parking, Field
2
Test
Parking,field
Test
A
T/RH
RH
b RH
 273,15
T
1
 273,15
T
1
k
E
e
A
  
(5) 
 
 
where: 
 
AT/RH 
Acceleration factor of the Lawson model 
b 
Constant ( b = 5,57 x 10-4) 
EA 
Activation energy (EA = 0,4 eV) 
k 
Boltzmann constant (k = 8,617 x 10-5 eV/K) 
TTest 
Test temperature in [°C] 
TParking, field 
Average temperature in [°C] in the parked vehicle 
RHTest  
Relative humidity in % during the test 
RHParking, field Average relative humidity in % in the parked vehicle
-273,15 °C 
Absolute zero of the temperature 
 
The test duration for the damp heat, steady state test - severity 2 - is calculated by 
means of: 
 
T/RH
Parking,field
Test
A
t
t
�
  
 
(6) 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 108 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 156 of 160
 
where: 
 
 
tTest 
Test duration in [h] 
TParking, field 
Non-operating time (parking time) in [h] during the service life in the field 
(131 400 h, in the most unfavorable case if the vehicle is not used) 
AT/RH  
Acceleration factor of the Lawson model according to equation (5) 
 
E.2 
Example:  
 
For a control unit mounted in the engine compartment, the test duration is calculated as 
follows: 
 
1. An average temperature of TParking, field = 23 °C  and a relative humidity of 
RHParking, field = 65 % is assumed for the component in the parked vehicle. 
The test conditions are Ttest = 65 °C and RHTest = 93 %. 
 
By means of equation (5), these values result in a combined acceleration factor 
of the Lawson model of AT/RH = 82,5 
 
2. The parking time in the field is tParking, field = 131 400 h. 
This results in a total test duration by means of equation (6) of: 
 
 
1593 hours
82,5
131400 hours
tTest
�
�
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 109 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 157 of 160
 
 
Annex F (informative) 
Condensation test, chamber programming and diagrams 
 
 
Figure 49: Programming of the test chamber 
During the temperature increase, the temperature of the water bath is used as control 
variable. When 80 °C is reached, the climatic chamber is switched over to temperature 
control (standard operation). 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 110 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 158 of 160
 
 
Figure 50: Sequence of the condensation test, 1 cycle 
1. 
Controlled water bath temperature 
2. 
Resulting test room temperature 
3. 
Actual humidity in the test chamber 
 
1 
2 
3
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 111 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 159 of 160
 
 
Figure 51: Sequence of the condensation test, 5 cycles 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 112 页
MBN LV 124-2 2013-08 
Copyright Daimler AG 2013 
Version 2.2 
LV 124 
Edition: 2013-02-28
 
 
 
 
Page 160 of 160
 
Annex G (informative)
Examples of examination methods for physical analysis 
 
• 
Screw loosening torques (e.g. housing screw connection, screws for fastening the 
component, ...) 
• 
Soldering spot failures/defects  
• 
Device / circuit board discolorations (in particular if thermally caused)  
• 
Stiffness/ease of operation, sliding, slackness (where parts are moved 
mechanically)  
• 
Signs of abrasion  
• 
Breaks, cracks, deformation of materials (in particular of casting and sealing 
materials). A suitable test method (X-ray, CT, metallographic sections,…) shall be 
selected by agreement  
• 
Opacity (in particular of parts of optical sensor systems) 
• 
Condition of latch and clip locking mechanisms  
• 
Signs of corrosion and migration, in particular silver and tin migration 
• 
Assessment of plastics for their resistance to hydrolysis (in particular in the case of 
components with inlaid lead frames and Terminal 30 switch circuits)  
• 
Damage to PCB vias, in particular thermal vias  
• 
Damage to internal connection (paddles) of large electrolytic capacitors after 
mechanical load (vibration, mechanical shock, free fall)  
• 
Damage to connector pins (e.g. resulting from current, temperature, rubbing, 
oxidation)   
• 
Other irregularities  
• 
ICT result (where possible) 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)

