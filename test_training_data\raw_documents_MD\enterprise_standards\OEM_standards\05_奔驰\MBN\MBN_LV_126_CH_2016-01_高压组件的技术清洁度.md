# MBN_LV_126_CH_2016-01_高压组件的技术清洁度.pdf

## 文档信息
- 标题：
- 作者：chenxiaoling
- 页数：12

## 文档内容
### 第 1 页
1 
 
高压组件的技术清洁度 
前言 
    本标准的版本由汽车制造商 AUDI AG，BMW AG，Daimler AG，Porsche AG 和 Volkswagen 
Aktiengesellschaft 的代表在工作组 4.9.6“高压组件技术清洁指南”中制作。 
    此 LV 作为 Word 文件存储在 AUDI AG 的标准化部门。 
    这个 LV 没有声称是完整的。汽车制造商可随时要求进行额外的最先进的测试。 
由于个别汽车制造商可能进行更改，只有基于本 LV 制定的各个汽车制造商的公司标准才适
用。 
    任何与本 LV 的偏差都列在公司标准的封面上（在合理的例外情况下，偏差可以用斜体
表示在标准的正文中）。如果在个别情况下需要修改单个测试部分，则这些修改应在汽车制
造商和供应商的负责部门之间单独商定。 
    在汽车制造商的共同开发项目的框架内，如果测试是由经过 DIN EN ISO / IEC 17025 认证
的独立机构进行的，则测试报告应被认可。批准不应从接受测试后自动进行报告。其他测试
报告应由客户自行决定。 
    本文档作为 VDA 19 卷的补充，旨在为电气移动性的高压（HV）组件定义一种特定的方
法。根据 VDA 第 19 卷第 1 部分和第 2 部分（以下简称 VDA19.1 和 VDA19.2），这些文件中
描述的整体方法应适用于整个汽车行业的技术清洁，包括其组件。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 2 页
2 
 
目录 
1 简介 ........................................................................................................................................ 3 
1.1 一般 ................................................................................................................................. 3 
1.2 风险监测的方法 ............................................................................................................. 3 
2 适用范围和有效性 ................................................................................................................ 4 
3 正式参考 ................................................................................................................................ 4 
4Holistic 方法 ............................................................................................................................ 4 
4.1 轮盘评估 ......................................................................................................................... 4 
4.2 开发 ................................................................................................................................. 5 
4.3 子供应商 ......................................................................................................................... 6 
4.4 物流 ................................................................................................................................. 6 
4.5 生产/装配计划 ............................................................................................................... 6 
4.6 人员 ................................................................................................................................. 7 
5 组件的文件证据和清洁度测试方法的过程 ........................................................................ 7 
5.1 一般 ................................................................................................................................. 7 
5.2 提取过程 ......................................................................................................................... 7 
5.3 测试流体 ......................................................................................................................... 7 
5.4 分析过滤器 ..................................................................................................................... 7 
5.5 分析过程与评估系统 ..................................................................................................... 8 
5.6 验证程序的适用性 ......................................................................................................... 8 
5.7 限定范围 ......................................................................................................................... 8 
5.8 通过故障确定极限 ......................................................................................................... 8 
5.9 分析过程 ......................................................................................................................... 8 
5.10 有文件证据的频率 ....................................................................................................... 8 
5.11 其他提取和分析过程 ................................................................................................... 9 
5.12 文件 ............................................................................................................................... 9 
6 术语和定义 ............................................................................................................................ 9 
7 缩写列表 .............................................................................................................................. 10 
8 修订文档 .............................................................................................................................. 11 
附件 A 资料性 ......................................................................................................................... 11 


### 第 3 页
3 
 
A.1 以电力电子器件为例的应用说明 ............................................................................... 11 
A.1.1 功率电子学应用实例的描述 ................................................................................ 11 
A.1.2 限定销间隙和粒径之间的差异 ............................................................................ 11 
A.1.3 使用电力电子器件的部件的粒子极限作为示例 ................................................ 12 
 
 
1 简介 
1.1 一般 
    由于在电动车辆中使用高压部件并且高压部件的复杂性日益增加，因此技术清洁对于汽
车工业特别重要。 
    作为高压环境中的高压部件的开发和工业化的一部分，焦点尤其在于气隙和爬电路径的
关键特性，特别是在高场强下以及与机械和机电组件元件结合。特别地，导电颗粒可能导致
HV 起弧和短路，这可能由于大量的金属部件元件和单独的组装过程而对系统产生负面影响。 
    颗粒污染一般不能排除。但是，组件或系统的残留污染应保持最小，以防止其导致功能
损坏或系统故障。本文件的实施应在经济上可行，并基于技术可行性。 
    通常在该概念方法中，防止颗粒优先于在原产地的移除并且需要在下游工艺步骤中清洁
组分。 
1.2 风险监测的方法 
    VDA 第 19.2 卷提供了在装配中规划和实施技术清洁的支持。它涉及环境，物流，人员
和装配设备的领域。 
    根据颗粒源来识别和分析关键颗粒，作为高电压组分对颗粒污染的灵敏度的系统分析的
一部分。 
    为了实现对技术清洁的要求的最佳和有效的实施，应当执行部件或系统设计与生产概念
的早期比较。设计对颗粒的抵抗力和在生产中实施清洁要求的可行性应当协调一致。 
    根据 VDA 19.2 的潜在清洁度分析或过程评估（根据 ZVEI 技术清洁指南）应用作工具。
该分析的结果应纳入 DFMEA 和 PFMEA，其中应特别处理由于技术清洁可能发生的故障。 
    该方法应在项目内实施为 QFL（见图 1），从提议阶段期间的一般清洁概念到开发阶段
期间的实施和验证以及系列阶段期间的监测。在此过程中，清洁度要求应在组件，组件和部
件的规格（如果需要的话）中定义。 


### 第 4 页
4 
 
 
 
2 适用范围和有效性 
本文件描述了根据 VDA 19.1 和 VDA 19.2 确定功能相关部件（清洁度测试）中颗粒污染
的方法的应用和文档修订。 
在这个过程中，包括从开发到生产用于电动移动的 HV 组件的供应链的整个价值链都被
考虑在内。 
本文件主要适用于通过直接桥接或缩短气隙和爬电路径可能导致短路或电弧的导电颗
粒。通常，非金属颗粒和纤维也应包括在分析中，只要它们引起故障，例如电绝缘故障或机
械阻塞接触，视觉障碍或光障/光纤的中断，以及显示元件或泄漏。 
此质量管理标准不是特定类型的，通常适用于所有电气/电子部件，即使它们是从次级
供应商处获得，或者供应商依赖分包商。 
3 标准参考 
    以下参考文件对于本文件的应用是必不可少的。对于注日期的引用文件，仅引用的版本
适用。对于未注明日期的引用，引用文件的最新版本（包括任何更改）适用。 
•VDA 第 19 卷第 1 部分技术清洁检查（VDA 19.1） 
•VDA 第 19 卷第 2 部分装配中的技术清洁度（VDA 19.2） 
•ZVEI 指南电气工程中的技术清洁度 
•IPC-A-610 电子组件的可接受性 
4 整体方法 
4.1 风险评估 
沿着被分析组件的整个价值链的污染（包括其尺寸，数量，形状，材料，来源和组件安
装位置）应按照潜在故障序列进行加权和优先级排序（见图 2），目的是设计概念和残留污
染，从而不会出现其他损害。 
诸如 D，P 和 Pin FMEA 和 FTA（故障树分析）的质量方法用于此目的。 
这些评估应在设计，组件，元件，接口和生产过程方面进行和比较。 
分析颗粒源，预
防 措 施• 提高 稳
健 性• 适 应残 留
污染的干预限制 
坚固的设计•残留污
染值和颗粒源•测量
方法•设计的功能安
全性/预期故障概率 
测量方法的适用性
•残留污染的测量
和与干预限值的比
较•监控生产质量 
实施：设计，组成
部分，物流，过程
管理，人员，环境，
测量系统 


### 第 5 页
5 
 
这也适用于被评估部件中和被评估部件上的功能接口，例如压力补偿装置，密封件，接
合，冷却表面，接触销和插头上的电功能表面以及螺钉附接表面。 
在 HV 部件内的所有气隙和爬电路径应根据由颗粒可能引起的短路和电弧进行评估。 
应考虑部件的安装位置。 
上述权重和优先级应与相应 OEM 的开发和供应商质量管理职能的组件经理协调。 这是
因为不同的车辆可能对相同的部件故障做出不同的响应。 
预防措施可包括例如清洁，去毛刺，保护漆和包封。 
 
图 2：风险评估方法 
4.2 开发 
为了确保在新项目中规划技术清洁，应将其作为流程的主要组成部分纳入开发过程。 
为了实施这一点，至少应考虑以下活动： 
•特定项目要求定义： 
在项目开始时，应确定技术清洁的要求。应考虑所有可用的信息，如标准，组件和生产
公差，以及组件元件和装配过程中可能的颗粒尺寸。这些要求应用于得出描述最大允许残留
污染的项目目标。 
•检查机电组件的间隙： 
应进行测试机电部件中的气隙和爬电距离的系统过程（例如部件间比较，间隙矩阵）。
应对所产生的任何风险进行评估，并规定必要的措施。 
对于气隙和爬电路径，应考虑到导电颗粒的尺寸，遵守规定的最小间隙（电气安全间隙）。 
•检查电气/电子部件中的间隙： 
应存在用于检查电气和电子部件中的气隙和爬电路径的系统过程。应使用适当的设计规
则和工具。例子包括： 
- 根据布局设计规则验证 PCB 上导电结构之间的特定间隙的符合性 
- 使用销 FMEA 验证是否符合特定的间隙 
- 确认符合防焊漆中未涵盖的 PCB 区域的特定间隙。 
污染源在整个价值
链上的权重和优先
级 • FMEA ， FTA ，
Ishikawa 等•与产品，
接口，客户进行比较 
故障相关污染的加
权和优先级•FMEA，
FTA，Ishikawa 等•与
过程，接口，客户的
比较 
比较过程，产品和界面
的加权和优先级，以及
针对车辆特定的故障
反应•比较 FMEA，FTA，
Ishikawa 等。 
故障相关污染的加权
和 优 先 级 • FMEA ，
FTA，Ishikawa 等•与过
程，产品，客户的比
较 
加工 
产 品 /
设计 
顾客 
系统界
面 


### 第 6 页
6 
 
对于电气间隙，导电颗粒的最大膨胀不应超过两个导电区域（IPC-A-610）之间的最小电
气间隙的一半。 
•作为 D-FMEA 一部分的技术清洁度评估： 
规定的间隙应在 D-FMEA 中评估。预防措施应根据风险分类进行定义和实施。 
在此过程中，在项目里程碑方面应考虑以下要求： 
•提供演示 
清洁度概念应由供应商规定，包括责任，部件对颗粒的阻力评估，考虑气隙和爬电路径
的设计规则和可能的接触覆盖，临界颗粒尺寸和颗粒量的初始极限估计，粗糙具有关键颗粒
和过程经验的风险评估，生产现场的现有清洁区域 
•B 样品 
验证风险评估，气隙和爬电路径的设计规则，工具和过程要求，组件元件的清洁度要求，
根据 VDA 19.2 的清洁潜力分析的初始自我评估 
C 样本 
在近系列生产和批量生产工具中实施清洁区域，根据 VDA 19.2 的自我评估状态 
•D 样本 
按照 VDA 19.2 的规定完成批量生产以及系列组件的审核，完整的 TecSa 文档 
4.3 子供应商 
为了能够满足项目特定的技术清洁要求，这些要求应指定给组件元件供应商。 
在技术文件中，如技术文件，如图纸和要求规范中，应描述各个部件在技术清洁方面的
要求。 
符合要求应记录在初始样品检验报告中，并在过程审核框架内进行验证。 
此外，组件部件的清洁度要求应通过极限表，提取和分析过程以及测量频率进行定义，
并记录在单个组件图纸中。如果组分元素的总颗粒数超过组分限制值，则应由供应商分析。
如果超过极限值，供应商应采取附加措施。 
对于批量生产，应同意对随机样品进行测试，以确保所需的清洁度。随机样本的范围和
频率应与相应客户协商确定。 
4.4 物流 
物流是特别重要的，对应于技术清洁措施的有效性所需的供应链中的概念的一致性。物
流应包括关于内部和外部运输，储存和货物提供（包括所需包装）的物料流的规划，绩效和
监测。 
早在生产概念规划的试验阶段，应根据清洁度水平（SaS），通过清洁潜力分析（VDA 19.2）
和适用性来分析供应链内环境的适用性。必要时应采取措施，例如运输和气锁概念及随后的
清洁措施。 
关于根据 VDA 第 19.1 条和 ZVEI 技术清洁指南的技术清洁，包装概念应设计并与客户协
调，以符合以下标准和要求： 
•与组件接触的包装符合要求 
与部件本身相同的清洁度要求 
•包装防止颗粒从环境中进入 
•包装不产生其他颗粒，例如。由于磨损 
•处理包装可防止反向污染 
•存在一种清洁概念，以确保符合可重复使用包装的要求 
•应定义目标值，测量方法和测量频率 
•符合清洁要求应通过运输试验验证 
4.5 生产/装配计划 


### 第 7 页
7 
 
要计划符合清洁要求的装配和组装环境，应进行过程评估，并确定与清洁度有关的风险
过程步骤（如 ZVEI 指南中所述或通过 VDA 19.2 中的清洁度潜力分析） 
在该过程中，所有关键颗粒源应根据现有技术在工艺顺序内并考虑潜在的环境因素进行
评估。如有必要，关键工艺步骤应配备清洁步骤，例如连接组件，钻孔，拧紧和焊接时。 
还可以通过合适的设计实现预防措施。 
应根据工艺清洁度（VDA 19.2）选择生产环境的适当清洁度（SaS）。 
对于生产环境，应存在适当方法的清洁概念。 
应定期检查是否符合清洁要求。监测的频率应与客户商定。应对每个工艺步骤检查颗粒
的类型和数量，并且根据颗粒减少的连续改进过程，主要影响因素应指定改进措施。 
4.6 人员 
在过程评估中还应考虑人们作为颗粒或其转移的原因的作用。 
应规定对洁净室区域，衣服，培训措施和工作站的清洁措施的适当进入限制（VDA 19.2）。 
5 组件的文件证据和清洁度测试方法的过程 
5.1 一般 
使用取样设备和测试流体的提取方法应定义为根据 VDA 19.1 的不同方法验证清洁度的
测试方法。该方法应通过分析方法作为补充，作为其中的一部分，粒子被过滤和评估。 
5.2 提取过程 
所选择的提取过程应根据 VDA 19.1 适用，并应提供其适用性的证据。根据 ZVEI 指南，
建议将压力冲洗作为选择的萃取过程。 
与 VDA 19.1 中命名的其他工艺（例如搅拌，冲洗和超声）相比，压力冲洗更适合于特
别是具有高度结构化表面和凹陷的控制单元的内部几何形状。 
冲洗过程中的主要影响参数应由供应商确定，并与承包商协调（见 VDA 19.1）。 应验证
主要影响因素，如试验流体，压力冲洗过程（压力，数量，喷嘴，距离，角度，排水）和暴
露时间，并定义适当的规格。 
应定义压力冲洗过程，以便根据设备的结构实现任何颗粒的最大分离和冲洗。压力冲洗
过程应包括随后冲洗取样设备。建议使用压力喷枪到达不能直接接触的区域。取样设备应包
括一个集成流量监测器。不允许使用喷雾瓶。 
应使用合适的提取室作为提取过程的取样设备。应排除外部污染物的进入。 
当使用其他萃取方法，如空气萃取（参见 VDA 19.1）时，应测量与压力冲洗相同的量和
类型。适用性应验证。 
5.3 测试流体 
对于所选择的测试流体，应根据 VDA 19.1 提供清洁剂与单个组件的兼容性的证据。测
试流体不得引起溶解或损坏。 
作为测试流体，ZVEI 指南建议基于非芳族脂族或非环状烃的溶剂，其中萜烯含量<30％。 
如果组件在关闭时不能冲洗，则开放组件的元件应从生产过程中移除，并在最后的工艺
步骤之后和组件密封之前进行检查。对于所有后续过程，应提供在密封或检查期间没有颗粒
进入组件的证据。 
5.4 分析过滤器 
    过滤器应根据 VDA 19.1 中规定的标准进行选择和使用。应根据污染物选择分析过滤器
及其孔的尺寸，以实现均匀分布，理想情况下不会接触或重叠颗粒，从而便于自动视觉计数。
分析滤波器的表面结构应适合于显微图像处理系统和自动视觉计数。 
优选直接过滤的方法，在取样设备的出口处直接过滤测试流体。 
过滤系统应与供应链中的 OEM 或相应客户协调，并根据风险分析和相应的临界粒度进
行记录。 


### 第 8 页
8 
 
5.5 分析过程与评估系统 
光学显微镜应用于分析过滤器上的颗粒类型，尺寸和数量的评估方法。为了特别地评估
短路的风险，导电颗粒及其尺寸和数量的识别是至关重要的。 
应根据 VDA 19.1 进行粒度和数量的自动目视评估。 
除了颗粒的评估之外，如果需要，应使用扫描电子显微镜和元素分析方法来确定材料类
型和来源。 
5.6 验证程序的适用性 
为了验证提取过程，不能确定测量仪器的能力，即颗粒分离和冲洗的程度。这是由于缺
乏确定组分上存在的颗粒的确切量的绝对选择，以及缺乏具有相同颗粒的参考组分或测试标
准。 
应根据 VDA 19.1 中规定的过程描述和标准，通过所谓的衰减试验和达到空白值，提供
提取过程和取样设备的适宜性的证据。 
应对所有具有指定颗粒量> 20 的功能关键尺寸等级的衰变行为进行检查。这可能需要考
虑低于功能关键尺寸的附加尺寸类别。 
如果在 6 个样品内部件清洁度值小于先前取样品的清洁度值的总和的 10％，则认为过
程的衰减测试已成功。 
空值 
应通过空白值提供取样设备的适宜性的证据，该数值应在没有部件的情况下，在系列试
验和分析条件下确定。 
5.7 限定范围 
每个组件（在打开状态下，每个主要组分）的限值应以表格的形式显示，其中包括尺寸
类别和数量（根据 VDA 19.1 的清洁度值）的明文文本指示。可以为被分离为防尘的部件的
区域定义不同的限制。 
尺寸类别的数量限制很大程度上取决于具有接触间隙和覆盖层的设计，由于功能故障导
致的可接受的残余风险以及测量和分析方法，因此应在项目开始时估计（前辈的实际状态或
类似组件），并在系列过程实施过程中（样品和测试对象）进行验证。作为一个基本规则，
非金属颗粒和纤维也应进行评估和必要时定义的限制。 
对于不防尘的部件，应在客户图纸中注明提取过程，分析过程，测试流体和极限表。 
5.8 通过故障确定极限 
如果在操作期间由于残留污染而发生故障，则可能需要调整现有限制，或如果没有限制
则引入限制。 
应确定打开分析装置的过程，防止颗粒的产生和进入。 
5.9 分析过程 
如果超过一个限值（每个尺寸等级的数量），则应增加测量频率。为此，供应链中的相
应客户应定义并同意升级流程。因此，承包商和次级供应商之间的升级流程也应同意。 
5.10 有文件证据的频率 
应定义随机抽样的频率，每个随机样本的测量次数和每次测量的测试单位数，以确保高
质量水平。结果应在正在进行的图表中记录和监测。除非另有约定，供应商有义务每月提供
所提供组件的组件质量和清洁度结果的证据，以符合限制。 
证据记录应从最初生产 C-样本开始，以验证组件元件，物流和近乎生产过程对系列生
产工具的影响，包括清洁和维护周期 
在采样和斜坡上升阶段，应实现更高的测试频率，直到达到稳定的串联状态。样品，斜
升和串联相的测量频率应与客户协调。 
对于技术清洁度的文件证明，应在这些分析中，特别是从振动测试中纳入额外的验证和


### 第 9 页
9 
 
重新鉴定样品。首先，对安装位置特定的颗粒汇应进行污染检查。 
5.11 其他提取和分析过程 
为了定位粒子源和用于故障排除，可以使用粒子陷阱或粒子戳记。 
在装配站的集成提取系统（紧固，预清洁单个零件，连接设备等）的过滤器内容应进行
分析，作为定期维护（更换或清洁）的一部分进行系列监测，以识别额外的颗粒源。 
5.12 文件 
由供应商验证和指定的过程和限制应记录并与供应链中的相应客户达成一致，并根据
VDA 19.1 中的样品模板向 OEM 提供证明。 
这些文件包含（测试规范，VDA 19.2，附录）： 
•组件信息（名称，体积，表面，重量，结构，密封前测试的主要部件，功能关键的颗
粒材料和尺寸） 
•测试对象的选择，挑选，处理，运输和存储的过程和责任 
•提取过程（采样设备，喷嘴，压力，流量，冲洗过程的工作指令（位置，间隙，角度，
持续时间，可能的行程距离，再冲洗程序）等） 
•过滤系统的规格 
•过滤器预处理的微观分析方法的规范 
•表格形式的尺寸类的衰减测试结果 
•每个组件的限制，每个大小类的最大粒子数，以表格的形式显示空白值 
•证据记录的频率，每次测试的测试和组件数 
在超过限制或运行平均值的情况下升级过程 
•每个尺寸等级的金属和非金属颗粒数量的时间进展，包括移动平均值（识别趋势），以
及最大颗粒的尺寸和材料 
•对极限表，提取和分析方法以及测量频率的组分元素的清洁度要求 
•打开设备进行分析的过程 
6 术语和定义 
衰减测量：作为其中一部分，通过在相同的试样上同样重复提取 6 次来验证提取参数的
有效性和适用性。 
清洁度值：表示组件部件的清洁度的个别值，例如。残余重量，最长颗粒或颗粒数。 
分析过滤器：薄，编织或泡沫隔膜，其特征在于在过滤期间保留具有确定尺寸的颗粒的
特定分离行为（也称为过滤器隔膜）。 
分析流体：用于提取和/或冲洗的流体，其包含用于分析的颗粒。 
项目阶段：供应阶段，开发阶段，抽样阶段，提升阶段，串联阶段。 
组件元素：安装在组件中的来自内部或外部生产的单位/子元素。 
取样：作为其一部分的过程，使用提取流体或空气从测试样品中提取颗粒残余物。 
取样设备用于提取的设备，室，支架，收集箱等 
空白值：在洁净度测试期间产生的颗粒，不是由测试对象创建的，而是由提取设备，工
具，环境和人员创建的。 
直接过滤：直接在提取设备处过滤：过滤器单元直接布置在分析流体收集箱的出口处。 
提取方法：从测试对象中分离颗粒负载的方法 VDA 19.1 中定义的用于流体提取的方法
包括压力冲洗，超声波，冲洗和振动。还描述了使用空气的提取。 
提取室提取设备的一部分，以防止环境影响并收集提取液和提取的颗粒 
限值表：具有粒度类别的表格和不得超过的最大允许粒度值。 
粒度等级：粒度范围，每个等级的上限和下限 
高压（HV）：电压> 60VDC 或 30VAC（根据 ECE R 100） 


### 第 10 页
10 
 
气隙和爬电距离：通过空气或载体材料表面作为开口通电触点之间的绝缘间隙 
组件：单元交付给 OEM，例如逆变器，充电器，电动机，电池，线束等。 
颗粒：颗粒是固体组分，例如。由金属，塑料，陶瓷，矿物，橡胶制成 
光纤：由开发线和最大内部电路之间的关系定义的长而薄的结构，大于 20 和宽度，使
用低于/等于 50μm 的最大内部电路测量。通常，它用于分类纺织纤维（柔性的，由有机材
料制成）并区别于致密颗粒。重要的是，通过纤维标准也考虑硬的纤维状结构，例如玻璃纤
维。 
临界颗粒：根据最近的发现，具有特定特性的颗粒，其存在损害了部件的质量 
颗粒负载表面或流体内所有可用颗粒的总和 
粒子源：产生或发射粒子的对象或过程。 
颗粒沉降（临界）：沉降是颗粒可能聚集的部件上的区域。如果它们如此靠近功能元件
（例如开放接触），则由于振动和引起故障（例如电弧或短路）而释放颗粒，则认为它们是
关键的。 
颗粒捕集器：用于收集和固定能够在环境空气中沉淀的颗粒的粘合剂表面。 
粒子印记：粘合剂表面以从表面吸收颗粒并固定颗粒用于下游分析 
过程评估：列出和评估组件生产中的过程步骤，以识别临界颗粒的来源 
引脚 FMEA：FMEA，用于评估组件的开路引脚之间的短路和电弧 
清洁区域：用于生产，组装和存储组件和系统的房间内的房间或禁区，为此采取相应的
措施以符合并保持表面清洁度。清洁区域包括包装和外壳。 
清洁等级（SaS）：洁净区和洁净室的清洁区域的分类。 
清洁度潜力分析：在组装中实施清洁度要求的问题列表，包括对部件清洁度的影响因素
的评估 
技术清洁度（TecSa）：测试表面/功能表面上的区域，没有因其制造和环境引起的功能
的关键污染 
TecSa 文档：关于技术清洁的总 PPA 文档。 （PPA：生产工艺和产品批准） 
污染（颗粒）可能对产品或过程产生负面影响的任何颗粒，分子，非颗粒或生物单位[DIN 
EN ISO 14644] 
再次污染：降低先前清洁的部件或组件的清洁度。 
装配：包括环境（清洁区），物流，人员和装配设备 
物流：关于内部和外部运输，存储和货物提供的物料流的规划，绩效和监测，包括所需
的包装 
人员：管理，清洁专家和来自项目特定单位的操作员工（LOG，流程规划，运营员工的
LOG，装配和质量） 
7 缩写列表 
D-FMEA：设计失效模式和效应分析 
EDXEnergy 分散 X 射线光谱学 
FMEA：故障模式和效应分析 
FTA：故障树分析，如果适用，不进行风险分析 
IPC：印刷电路研究所（现：连接电子工业协会） 
P-FMEA：过程失效模式和效应分析 
SaS：根据 VDA 19.2 的清洁度水平 
TecSa：技术清洁 
VDAGerman 汽车工业协会（Verband Deutscher Automobilindustrie） 
ZVEI：德国电气和电子制造商协会（Zentralverband der Elektrotechnik- und 


### 第 11 页
11 
 
Elektronikindustrie） 
8 修订文档 
 
修订版本 
 
 
版本 
简介 
日期 
1.0 
第一版本 
 
 
 
 
 
附件 A 资料性 
A.1 以电力电子器件为例的应用说明 
本附件应描述如何处理使用电力电子设备的标准。在这个过程中，我们正在分析一个纯
虚构的例子，即使它具有显着的实践特性。本示例的内容不代表完整的标准文档。 
A.1.1 功率电子学应用实例的描述 
•用于混合动力车的逆变器，大约 3 升体积，安装在电池安装空间内 
•铝压铸外壳，水密信号线连接，防尘 HV 电缆连接，冷却液连接 
•内部布局包括凝胶电源模块（焊接和焊接功率半导体），电流导轨，配备的 PCB（具有
SMT 和 THT 焊点的控制器和连接电路），中间电路箔电容器，螺钉和附加密封。 
描述由导电颗粒引起的潜在损伤机理 
损坏机构 1：开路触点的短路，例如，组件元件焊点 
    （参考 LV126，第 4.2 节） 
 
图 3：导电结构之间的间隙 
损坏机构 2：打开的 HV 触头之间的 HV 电弧，例如， 电缆或电流导轨 
 
图 4：气隙和爬电距离 
A.1.2 限定销间隙和粒径之间的差异 
典型的机械和电子生产工艺产生高达 1500μm 的颗粒尺寸（例如，来自冲压工艺的金
属屑）。 这些可以通过合适的措施（例如在冲孔刀具上的提取）限于颗粒尺寸，例如 400μ
m。 
例如，如果 PCB 以具有<400μm（细间距）的针间距的电子部件元件为特征，则这可能
导致在部件元件的打开的接触针之间的不希望的导电连接（例如由金属芯片引起）。 
如果粒径不能进一步减小，则应采取适当的耐久性措施作为 PCB 上电子电路设计的一


### 第 12 页
12 
 
部分。 这可以通过包括附加盖（例如关键部件上的保护漆）的元件来实现。 
 
图 5：临界接触间隙（间隙问题） 
A.1.3 使用电力电子器件的部件的粒子极限作为示例 
供应商应允许为本示例中的电力电子设备规定以下颗粒尺寸和数量的极限值。 它们应
记录在表格中，包括根据 VDA19.1 的粒度等级和数量。 本规范应基于设计的耐久性，部件
中剩余的颗粒的类型和尺寸，以及由此产生的故障概率和故障后果。 
表 1：使用功率电子器件作为示例的粒子极限 
组件的清洁技术 
尺寸等级 
颗粒直径 
最大允许颗粒
数量 
C 
15-25μm 
未指定 
D 
25-50μm 
未指定 
E 
50-100μm 
300 
F 
100-150μm 
120 
G 
150-200μm 
20 
H 
200-400μm 
3 
I 
400-600μm 
0 
J 
600-1000μm 
0 
K 
>1000μm 
0 
文件证明进程 
摘取方法 
压力清洗法 
分析方法 
见澄清表 
 

