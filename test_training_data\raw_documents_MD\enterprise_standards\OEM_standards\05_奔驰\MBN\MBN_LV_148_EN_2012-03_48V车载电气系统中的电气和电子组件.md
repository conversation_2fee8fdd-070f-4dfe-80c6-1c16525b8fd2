# MBN_LV_148_EN_2012-03_48V车载电气系统中的电气和电子组件.pdf

## 文档信息
- 标题：
- 作者：walmaye
- 页数：50

## 文档内容
### 第 1 页
Mercedes-Benz 
MBN LV 148
Company Standard 
Date published:  2012-03
 
First edition
 
Total no. of pages: 1 and 48 pages LV 148
 
Person in charge: Dr. <PERSON>
 
Plant: 059; Dept.: GR/AKS
Date of translation: 2012-06 
Phone: +49 *********** 50
 
NOTE: This translation is for information purposes only. 
The German version shall prevail above all others. 
 
Copyright Daimler AG 2012 
 
Electric and Electronic Components in Motor Vehicles 
48V On-Board Electrical System 
 
Foreword 
This edition of this Standard is based on the document LV 148 which has been established by representa-
tives of the automotive manufacturers AUDI AG, BMW AG, Daimler AG, Porsche AG and Volkswagen 
Aktiengesellschaft within Working Group AK 4.14, AG1 "14V On-Board Electrical System". 
Any deviations from LV 148 are listed on the cover sheet of this Standard (in justified exceptional cases, 
deviations may be represented in the body of the standard in italics). If in individual cases modifications to 
individual test sections are required, such modifications shall be agreed separately between the depart-
ments responsible of the automotive manufacturer and the supplier. 
Within the framework of common development projects of the automotive manufacturers, test reports will 
be recognized provided that the tests have been performed by an independent institute accredited in ac-
cordance with DIN EN ISO/IEC 17025. Approval does not automatically follow from acceptance of the test 
reports. Other test reports may be recognized at the discretion of the customer. 
 
LV 148 edition 2011-08-29 has been adopted into the set of Mercedes-Benz standards with the amend-
ments listed below. MBN LV 148 specifies component requirements for the 48V on-board electrical sys-
tem. AK-LV documents cited in this MBN LV are usually adopted into the set of Mercedes-Benz standards 
as MBN LV documents with identical number. 
 
Preamble: 
Implementation of the valid version of this Standard is binding for new vehicle projects or components for 
which no concept/basic specifications or component requirement specifications have been approved yet at 
the date of issue. 
 
The implementation of this Standard for the supplier shall be by referencing in the applicable contractual 
documents. 
Deviations from LV 148 
MBN LV 148 contains the following deviations in comparison with document LV 148: 
 
New wording in Section 2.1 Premises regarding components with BN48 connection: 
• 
A single fault condition in a harness shall not result in the short circuiting of the BN48 to the 12V 
on-board electrical system (BN12)/24V on-board electrical system (BN24) area. 
These deviations are indicated in italics in the document LV 148. 
General requirements 
For safety requirements, homologation (in particular, exhaust emissions) and quality, the existing statutory 
requirements and laws shall be complied with. In addition, the relevant requirements of the Daimler Group 
apply. 
 
All materials, procedures, processes, components, or systems shall conform to the current regulatory 
(governmental) requirements regarding regulated substances and recyclability. 
Changes 
New edition 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 2 页
 
LV 148 
2011-08-29 
 
 
Page 1 of 48 
 
Dr.-Ing. T. Dörsam, Dr.-Ing. D. Grohmann (Daimler AG), Dr.-Ing. S. Kehl (Porsche AG), Dipl.-Ing. A. Klinkig (Volkswagen Aktienge-
sellschaft), Dipl.-Ing. A. Mai, Dipl.-Ing. O. Sirch (BMW Group), Dipl.-Ing. A. Radon, Dipl.-Ing. J. Winkler (Audi AG)
 
 
Electric and Electronic Components in Motor Vehicles  
 
48V On-Board Electrical System 
Requirements and Tests 
 
Foreword 
This edition of this LV has been produced by representatives of the automotive manufacturers AU-
DI AG, BMW AG, Daimler AG, Porsche AG and Volkswagen Aktiengesellschaft in Working Group 
4.14, AG1, "14V / 48V On-Board Electrical System and Electrical Energy Management". 
This LV is stored as a Word file in the Standardization Department of AUDI AG. 
This LV does not claim to be complete. Automotive manufacturers are free to request additional 
state-of-the-art tests at any time. 
As the individual automotive manufacturers may make changes, only the company standards of the 
respective automotive manufacturers created on the basis of this LV shall apply. 
Any deviations from this LV are listed on the cover sheet of the company standards (in justified 
exceptional cases, deviations may be represented in the body of the standard in italics). If in indi-
vidual cases modifications to individual test sections are required, such modifications shall be 
agreed separately between the departments responsible of the automotive manufacturer and the 
supplier. 
Within the framework of common development projects of the automotive manufacturers, test re-
ports will be recognized provided that the tests have been performed by an independent institute 
accredited in accordance with DIN EN ISO/IEC 17025. Approval does not automatically follow from 
acceptance of the test reports. Other test reports may be recognized at the discretion of the cus-
tomer. 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 3 页
Page 2 
LV 148 
29.08.2011 
 
Contents 
 
Page 
1 
Normative references .......................................................................................................... 6 
2 
General ................................................................................................................................ 7 
2.1 
Premises regarding components with BN48 connection ..................................................... 7 
2.2 
Requirements for components with BN48 connection ........................................................ 7 
2.3 
Terms and definitions .......................................................................................................... 7 
2.4 
Sampling rates and measured value resolutions .............................................................. 12 
2.5 
Functional status classification .......................................................................................... 12 
2.6 
Operating modes ............................................................................................................... 13 
2.7 
Parameter test ................................................................................................................... 13 
2.8 
Continuous parameter monitoring with drift analysis ......................................................... 14 
2.9 
Physical analysis ............................................................................................................... 14 
2.10 
Interface description .......................................................................................................... 14 
3 
Electrical tests and requirements ...................................................................................... 15 
3.1 
E48-01a Long-term overvoltage for non-voltage-limiting components .............................. 15 
3.2 
E48-01b Overvoltage for voltage-limiting components ...................................................... 16 
3.3 
E48-02 Transient overvoltage, load dump ........................................................................ 17 
3.4 
E48-03 Transient event in lower operating range with functional limitation....................... 19 
3.5 
E48-04 Recuperation ........................................................................................................ 20 
3.6 
E48-05 Superimposed alternating voltage ........................................................................ 21 
3.7 
E48-06 Slow decrease and increase of the supply voltage ............................................... 22 
3.8 
E48-07 Slow decrease, abrupt increase of the supply voltage ......................................... 26 
3.9 
E48-08 Reset behavior ...................................................................................................... 27 
3.10 
E48-09 Short interruptions ................................................................................................ 28 
3.11 
E48-10 Start pulses ........................................................................................................... 30 
3.12 
E48-11 Loss of ground BN48 ............................................................................................ 32 
3.13 
E48-12 Ground offset ........................................................................................................ 34 
3.14 
E48-13 Internal withstand voltage ..................................................................................... 36 
3.15 
E48-14 Closed-circuit current ............................................................................................ 37 
3.16 
E48-15 Operation in range without functional limitation .................................................... 37 
3.17 
E48-16 Operation in upper range with functional limitation ............................................... 38 
3.18 
E48-17 Operation in lower range with functional limitation................................................ 40 
3.19 
E48-18 Overvoltage range ................................................................................................ 42 
3.20 
E48-19 Undervoltage range .............................................................................................. 43 
3.21 
E48-20 Fault current part 1 ............................................................................................... 46 
3.22 
E48-21 Fault current part 2 ............................................................................................... 46 
4 
Glossary, abbreviations ..................................................................................................... 48 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 4 页
Page 3 
LV 148 
29.08.2011 
 
List of figures 
 
Page 
Figure 1: Definitions of steady-state voltage ranges .......................................................................... 8 
Figure 2: Components with two voltage supplies ............................................................................. 10 
Figure 3: Test pulse E48-01b Overvoltage for voltage-limiting components .................................... 17 
Figure 4: Test pulse E48-02 Transient overvoltage, load dump ...................................................... 18 
Figure 5: Test pulse E48-03 Transient event in lower limited range ................................................ 19 
Figure 6: Test pulse E48-04 Recuperation ...................................................................................... 20 
Figure 7: Test pulse E48-05 Superimposed alternating voltage ...................................................... 22 
Figure 8: Test pulse E48-06 Slow decrease and increase of the supply voltage ............................. 23 
Figure 9: Test pulse E48-06 for operation with storage part 1 ......................................................... 24 
Figure 10: Test pulse E48-06 for operation with storage part 2 ....................................................... 25 
Figure 11: Test pulse E48-07 Slow decrease, abrupt increase of the supply voltage ..................... 27 
Figure 12: Test pulse E48-08 Reset behavior .................................................................................. 28 
Figure 13: Test pulses E48-09 Short interruptions ........................................................................... 30 
Figure 14: Schematic circuit diagram E48-09 Short interruptions .................................................... 30 
Figure 15: Test pulse: E48-10 Start pulses ...................................................................................... 31 
Figure 16: Schematic circuit diagram E48-11 Loss of ground BN48 ............................................... 33 
Figure 17: Schematic circuit diagram E48-12 Ground offset (UB48) ................................................. 35 
Figure 18: Schematic circuit diagram E48-13 Internal withstand voltage ........................................ 36 
Figure 19: Test pulse E48-15 Operation in range without functional limitation ................................ 38 
Figure 20: Test pulse E48-16 Operation in upper range with functional limitation ........................... 39 
Figure 21: Test pulse E48-17 Operation in lower range with functional limitation............................ 41 
Figure 22: Test pulse E48-18 Overvoltage range ............................................................................ 43 
Figure 23: Test pulse E48-19 Undervoltage range .......................................................................... 45 
Figure 24: Schematic circuit diagram: E48-20 Fault current part 1 .................................................. 46 
Figure 25: Schematic circuit diagram E48-21 Fault current part 2 ................................................... 47 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 5 页
Page 4 
LV 148 
29.08.2011 
 
List of tables 
 
Page 
Table 1: Normative references ........................................................................................................... 6 
Table 2: Abbreviations for voltages and currents ............................................................................... 9 
Table 3: Abbreviations for temperatures .......................................................................................... 10 
Table 4: Abbreviations for times/durations ....................................................................................... 11 
Table 5: Definition of standard tolerances ........................................................................................ 11 
Table 6: Definitions of standard values ............................................................................................ 11 
Table 7: Test parameters E48-01a Long-term overvoltage .............................................................. 15 
Table 8: Test parameters E48-01b Overvoltage for voltage-limiting components ........................... 16 
Table 9: Test parameters E48-02 Transient overvoltage ................................................................. 18 
Table 10: Test parameters E48-03 Transient event in lower limited range ...................................... 19 
Table 11: Test parameters E448-04 Recuperation .......................................................................... 20 
Table 12: Test parameters E48-05 Superimposed alternating voltage ............................................ 21 
Table 13: Test parameters E48-05 Superimposed alternating voltage ............................................ 21 
Table 14: Test parameters E48-06 Slow decrease and increase of the supply voltage .................. 23 
Table 15: Test parameters E48-06 for operation with storage part 1 ............................................... 24 
Table 16: Test parameters E48-06 for operation with storage part 2 ............................................... 25 
Table 17: Test parameters E48-07 Slow decrease, abrupt increase of the supply voltage ............. 26 
Table 18: Test parameters E48-08 Reset behavior ......................................................................... 28 
Table 19: Test parameters E48-09 Short interruptions .................................................................... 29 
Table 20: Test parameters E48-10 Start pulses ............................................................................... 31 
Table 21: Test parameters E48-11 Loss of ground BN48 ................................................................ 33 
Table 22: Test parameters E48-12 Ground offset ............................................................................ 35 
Table 23: Test parameters E48-13 Internal withstand voltage ......................................................... 36 
Table 24: Test parameters E48-14 Closed-circuit current ................................................................ 37 
Table 25: Test parameters E48-15 Operation in range without functional limitation ........................ 38 
Table 26: Test parameters E48-16 Operation in upper range with functional limitation ................... 39 
Table 27: Test parameters E48-17 Operation in lower range with functional limitation ................... 41 
Table 28: Test parameters E48-18 Overvoltage range .................................................................... 42 
Table 29: Test parameters E48-19 Undervoltage range .................................................................. 44 
Table 30: Test parameters E48-20 Fault current part 1 ................................................................... 46 
Table 31: Test parameters E48-21 Fault current part 2 ................................................................... 47 
Table 32: Abbreviations .................................................................................................................... 48 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 6 页
Page 5 
LV 148 
29.08.2011 
 
Scope 
This document specifies requirements, test conditions and tests for electric, electronic and mecha-
tronic components and systems for the use in motor vehicles. 
Any additional or deviating requirements, test conditions and tests shall be defined in the respec-
tive Component Requirement Specifications. 
The represented tests are not intended for component qualification or a qualification of the manu-
facturing process. 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 7 页
Page 6 
LV 148 
29.08.2011 
 
Part I - Electrical requirements 
1 
Normative references 
The following referenced documents, see Table 1, are indispensable for the application of this doc-
ument. For dated references, only the edition cited applies. For undated references, the latest edi-
tion of the referenced document (including any amendments) applies. 
Table 1: Normative references 
DIN 72552 
Terminal Markings for Motor Vehicles: Examples for Application on Circuit 
Diagrams 
DIN IEC 60038 
IEC Standard Voltages 
DIN EN ISO/IEC 17025 General Requirements for the Competence of Testing and Calibration La-
boratories 
ISO 6469-3 
Electric Road Vehicles - Safety Specifications - Part 3: Protection of Per-
sons Against Electric Hazards 
ECE-R100  
Uniform Provisions Concerning the Approval of Battery Electric Vehicles 
with Regard to Specific Requirements for the Construction and Functional 
Safety 
LV 124 
Electric and Electronic Components in Motor Vehicles up to 3,5 t - General 
Requirements, Test Conditions and Tests  
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 8 页
Page 7 
LV 148 
29.08.2011 
 
2 
General 
• 
LV 148 is intended as a supplement to LV 124 and includes the additional electrical tests of 
the 48V on-board electrical system (BN48). 
• 
LV 148 applies to all components used in the 48V on-board electrical system. 
2.1 
Premises regarding components with BN48 connection 
• 
No protection against electric shock is required for direct voltage < 60 V. This applies to di-
rect voltages with an on-board electrical system ripple of 10 % rms max. 
For alternating voltages Urms ≤ 30 V no protection against electric shock is required (ISO 
6469-3). 
• 
A single fault condition in a harness shall not result in the short circuiting of the BN48 on the 
12V on-board electrical system (BN12)/24V on-board electrical system (BN24) area. 
• 
A joint ground exists between BN12/ BN24 and BN48 which are connected by means of 
spatially separated ground studs/connections. 
• 
All voltage and current indications refer to the component (terminal voltage). 
• 
Appropriate measures shall be taken to prevent polarity reversal of the BN48 system in the 
vehicle. 
• 
Appropriate measures shall be taken to prevent a BN48 jump start in the vehicle. 
2.2 
Requirements for components with BN48 connection 
• 
A single fault condition shall not result in a short circuit between BN48 and BN12 / BN24. 
• 
Components with simultaneous BN48 supply and BN12/ BN24 supply or BN12-based inter-
faces require their own ground connection for both supply ranges. These ground connec-
tions shall be spatially separated. 
• 
The loss of ground of a BN48 component (T. 31 and/or T. 41) shall not result in the disturb-
ance or destruction of communication networks or electrical systems.  
• 
No component shall result in an entry into the overvoltage range (e.g. in the event of a load 
dump or due to resonance rises). 
2.3 
Terms and definitions 
2.3.1 
Terminal marking 
Terminal 40 is the positive line of the 48 volt supply. 
Terminal 41 is the ground line of the 48 volt supply. 
Terminal markings are defined in DIN 72552. 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 9 页
Page 8 
LV 148 
29.08.2011 
 
2.3.2 
Voltages and currents 
 
Figure 1: Definitions of steady-state voltage ranges 
 
Electric shock protection range 
In this range, protection against electric shock is required for direct voltages in accordance with 
ECE-R 100. 
Overvoltage range 
The overvoltage range including all tolerances lies between U48max,high,limited and U48r. In this range, 
the overvoltage protection should be active, and voltages greater than U48max,high,limited shall be rec-
orded by means of an entry in the fault memory. 
The range between U48r and U48shprotect includes the safety reserve. 
Upper operating voltage range with functional limitation 
The range between U48max,unlimited and U48max,high,limited is intended for the calibration of the storage and 
the acceptance of the recuperation energy. 
Operating voltage range without functional limitation 
The range between U48min,unlimited and U48max,unlimited allows the components to be operated without 
functional limitation. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 10 页
Page 9 
LV 148 
29.08.2011 
 
Lower operating voltage range with functional limitation 
Operation in the range from U48min,low,limited to U48min,unlimited is only permissible temporarily. Counter-
measures shall be taken in order to return to the operating voltage range without functional limita-
tion. 
Undervoltage range 
All voltages below U48min,low,limited are defined as undervoltage and shall be recorded as fault memory 
entries. The storage protection voltage is U48stoprotect. 
 
Table 2: Abbreviations for voltages and currents 
Abbreviation 
Designation 
Value 
U48shprotect 
Electric shock protection voltage. Derived from the requirement 
regarding compliance with the limit value for protection against 
electric shock with direct voltages according to ECE-R 100 
60 V 
U48r 
2 V safety reserve to electric shock protection voltage 
58 V 
U48max,high,limited 
Maximum voltage of the upper operating range with functional 
limitation 
54 V 
U48max,unlimited 
Maximum voltage of the operating range without functional 
limitation 
52 V 
U48n 
BN48 nominal voltage (the nominal voltage is based on stand-
ard DIN IEC 60038) 
48 V 
U48min,unlimited 
Minimum voltage of the operating range without functional limi-
tation 
36 V 
U48min,low,limited 
Minimum voltage of the lower operating range with functional 
limitation 
24 V 
U48stoprotect 
Storage protection voltage 
20 V 
U48pp 
Peak-to-peak voltage 
 
U48rms 
rms value of a voltage 
 
U48max 
Maximum voltage that may occur during a test 
 
U48min 
Minimum voltage that may occur during a test 
 
U48test 
BN48 test voltage 
 
U12test 
BN12 test voltage 
 
GND48 
Device ground (T. 41) 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 11 页
Page 10 
LV 148 
29.08.2011 
 
2.3.3 
Test set-up 
The general requirements of DIN EN ISO/IEC 17025 apply. 
Unless otherwise specified explicitly, the voltage profiles indicated refer to the terminal voltages of 
the DUT and shall be measured and documented at the terminals.  
The voltage thresholds indicated under U48shprotect shall be maintained by the component with an 
accuracy of ± 1 % (measured at the connector / terminal of the control unit) 
For the U48shprotect threshold, a tolerance band from 0 % to -1 % applies. 
 
The fault memory shall be deleted before each test. 
After each test, the fault memory shall be read out and documented. 
 
All BN48 components with an interface to the BN12 supply or which have communication interfac-
es shall additionally comply with LV 124: 
- 
BN12 shall comply with the requirements of LV 124. During the LV 124 tests, the voltage is 
U48n. 
- 
During the tests on BN48, the BN12 voltage shall be 14,0 V, provided that it is not specified 
otherwise for individual tests. 
 
In addition, care shall be taken to ensure and verify with appropriate tests that any voltage or any 
voltage profile within the defined limits does not result in the destruction or functional failure of the 
component on the other side of the on-board electrical system. 
 
See Figure 2: Components with two voltage supplies. 
DUT
14V
48V
BN12
BN48
 
Figure 2: Components with two voltage supplies 
2.3.4 
Temperatures 
Table 3: Abbreviations for temperatures 
Abbreviation Description 
Unit 
Tmin 
Minimum operating temperature 
°C 
TRT 
Room temperature 
°C 
Tmax 
Maximum operating temperature 
°C 
Ttest 
Test temperature 
°C 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 12 页
Page 11 
LV 148 
29.08.2011 
 
2.3.5 
Times/durations 
Table 4: Abbreviations for times/durations 
Abbreviation Description 
Unit 
tr 
Rise time (e.g. of a voltage profile) 
ms 
tf 
Fall time (e.g. of a voltage profile) 
ms 
Ttest 
Test duration 
s, min, h 
 
All edge descriptions refer to the 10% or 90% voltage values. 
2.3.6 
Standard tolerances 
Unless otherwise indicated, the tolerances according to Table 5: Definition of standard tolerances 
apply. 
Tolerances of envelopes must always be considered unilaterally as otherwise the requirement is 
mitigated. 
Tolerances refer to the required setting value. 
Table 5: Definition of standard tolerances 
Abbreviation Description 
Tolerance limits 
f 
Frequencies 
± 1 % 
T 
Temperatures 
± 2 °C 
Frel 
Relative humidity 
± 5 % 
t 
Times/durations 
+ 5 % to 0 % 
U 
Voltages 
± 0,5 % 
I 
Currents 
± 2 % 
2.3.7 
Standard values 
Unless otherwise indicated, the standard values according to Table 6: Definitions of standard val-
ues apply. 
Table 6: Definitions of standard values 
Abbreviation Description 
Value 
TRT 
Room temperature 
23 °C ± 5 °C 
Frel 
Relative humidity 
25 % (+ 5 % to 0 %) 
to 
75 % (- 5 % to 0 %) 
Ttest 
Test temperature 
TRT 
Ri 
Internal source resistance 
10 mΩ ≤ Ri ≤ 100 
mΩ 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 13 页
Page 12 
LV 148 
29.08.2011 
 
2.4 
Sampling rates and measured value resolutions 
The sampling rate and bandwidth of the measuring system shall be adapted to the respective test. 
All measured values with all maximum values (peaks) shall be recorded. 
The resolution of the measured values shall be adapted to the respective test. Care shall be taken 
to  ensure that occurring voltage peaks do not lead to an overflow or cannot be measured in the 
case of insufficient resolution. 
2.5 
Functional status classification 
This Section describes the functional status of the DUT during and after the test. The functional 
status of the DUT shall be indicated for each test. Both the precise functional requirements for the 
DUT in the operating modes and additional requirements shall be defined and documented in the 
Component Performance Specifications. 
Functional status A poses the most stringent requirements for the component. 
Storage functions shall always remain in functional status A. 
The integrity (not up-to-dateness) of the non-volatile memories shall be ensured at any time. 
2.5.1 
Functional status A 
The components are fully operational, and provide the intended function. 
2.5.2 
Functional status B 
The components are still fully operational. A function of the components is performed within the 
deviations permissible for functional status B. When the components revert to functional status A, 
they  shall automatically provide their intended function. 
2.5.3 
Functional status C 
The components are still operational, shall not assume any undefined states and, in particular, 
shall not cause any malfunctions in other components. The components may reduce their electrical 
output for self-protection purposes. When the components revert to functional status A or B, they  
shall automatically provide their intended function. 
2.5.4 
Functional status D 
The components are still operational and shall not assume any undefined states. The components 
may reduce their electric performance to zero. At the end of a reset process (e.g. ignition state 
transition, vehicle restart) the DUT shall revert to functional status A. 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 14 页
Page 13 
LV 148 
29.08.2011 
 
2.6 
Operating modes 
The electric, electronic and mechatronic components and systems will be operated in different op-
erating modes during their service life, which shall be simulated correspondingly during the tests. 
Details concerning the operating modes, operating loads (e.g. actuation, original sensors, original 
actuators or replacement circuitry) and the necessary boundary conditions shall be coordinated 
between the buyer and supplier and documented. 
The operating mode required during the tests should represent the most stringent requirement for 
the component. Any deviations shall be recorded in the Component Requirement Specifications 
with their value ranges.  
2.6.1 
Operating mode I - DUT not electrically connected 
******* 
Operating mode I.a 
The DUT is without power; connector and harness are not connected. 
******* 
Operating mode I.b 
The DUT is without power; but the connector and harness are connected. 
2.6.2 
Operating mode II – DUT electrically connected 
Components which from an electrical point of view are source and sink, shall be tested in both op-
erating modes. 
2.6.2.1 
Operating mode II.a 
The DUT shall be operated without operating load. 
2.6.2.2 
Operating mode II.b 
The DUT shall be operated with minimal operating load. 
The DUT shall be operated such that minimal self-heating occurs (e.g. by means of a reduction of 
continuous output power or through infrequent activation of external loads). 
******* 
Operating mode II.c 
The DUT shall be operated with maximum operating load. 
The DUT shall be operated such that maximum self-heating occurs (e.g. by means of a realistic 
maximization of a continuous output power or frequent activation of external loads). 
2.7 
Parameter test 
All parameters to be monitored separately shall be defined in the Component Requirement Specifi-
cations. 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 15 页
Page 14 
LV 148 
29.08.2011 
 
2.8 
Continuous parameter monitoring with drift analysis 
The key parameters to be monitored shall be recorded throughout the test.  
For components with fault memory, the fault memory shall be monitored continuously and the en-
tries shall be documented. 
The data resulting from the continuous parameter monitoring shall be examined for trends and drift-
ing to detect abnormalities, aging or malfunctions of the component. 
2.9 
Physical analysis 
For the physical analysis, the DUT shall be opened and evaluated for damage visually. 
Additional analyses (e.g. X-ray examinations and metallographic examinations of the assembly and 
joining technology) shall be coordinated between the buyer and the supplier. 
2.10 
Interface description 
The states and electrical properties of all interfaces shall be described completely. This description 
is intended as a basis for the evaluation of the test results and shall therefore be available in detail.  
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 16 页
Page 15 
LV 148 
29.08.2011 
 
3 
Electrical tests and requirements 
Each DUT shall be systematically subjected to all tests. 
After each individual test, the DUT shall be tested for external soundness and unrestricted func-
tionality before the next test is performed. The test for unrestricted functionality shall be defined in 
the Component Requirement Specifications. 
Damaged DUTs shall be eliminated from the test cycle and documented. If required, the test shall 
be repeated with a new DUT or the following test performed with a new DUT. The procedure shall 
be agreed with the buyer. 
The test plan shall be agreed with the buyer. 
The functional status indicated for the test represents the minimum requirement for that test.  
For each test, the permissible fault memory entries and the functional statuses of the component 
shall be specified. 
3.1 
E48-01a Long-term overvoltage for non-voltage-limiting components 
3.1.1 
Purpose 
The resistance of the component to long-term overvoltages is tested. 
The test does not apply to voltage-limiting components. 
3.1.2 
Test 
Table 7: Test parameters E48-01a Long-term overvoltage 
Operating mode of the DUT 
Operating modes II.a, II.b and II.c 
ttest 
60 min 
U48test 
U48shprotect 
Ttest 
Tmax - 20 °C 
Number of cycles 
1 
Number of DUTs 
6 
3.1.3 
Requirement 
The assessment of the test result depends on the application of the component. A distinction is 
made between 
a) Non-regenerative components which are able to convert  
electrical energy to heat (e.g. ohmic loads):  
 
functional status B 
b) Regenerative components   
 
 
 
functional status B 
c) Functions required for the driving mode: 
 
 
functional status B 
d) All other components:  
 
 
 
 
functional status C 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 17 页
Page 16 
LV 148 
29.08.2011 
 
3.2 
E48-01b Overvoltage for voltage-limiting components 
3.2.1 
Purpose 
This test applies to all components equipped with a voltage-limiting function to ensure protection 
against electric shock. A case is simulated where a source supplies energy to the BN48 which 
cannot be accepted by the on-board electrical system and which thus results in a voltage increase. 
3.2.2 
Test 
Connect DUT to a high-capacity source. 
Run through the specified current profile. Measure the current on the supply line of the component. 
Table 8: Test parameters E48-01b Overvoltage for voltage-limiting components 
Operating mode of the DUT 
Operating mode II.c 
Ttest 
TRT 
I0 
1 A 
I1 
30 A 
I2 
2 A 
t0 
1 s 
tr1 
10 ms 
t1 
2 s 
tf1 
2 s 
t2 
10 min 
Number of cycles 
3 
Number of DUTs 
6 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 18 页
Page 17 
LV 148 
29.08.2011 
 
 
Figure 3: Test pulse E48-01b Overvoltage for voltage-limiting components 
3.2.3 
Requirements 
Functional status A 
The voltage at the component shall be < U48r throughout the test. 
3.3 
E48-02 Transient overvoltage, load dump 
3.3.1 
Purpose 
Transient overvoltages in BN48 may occur due to the switching off of loads and due to short accel-
erator tip-ins. These overvoltages are simulated by means of this test. 
 
Current 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 19 页
Page 18 
LV 148 
29.08.2011 
 
3.3.2 
Test 
Table 9: Test parameters E48-02 Transient overvoltage 
Operating mode of the DUT 
Operating mode II.c  
U0 
U48n 
U1 
70 V 
U2 
U48r 
t0 
100 ms 
tr 
1 ms 
t1 
100 ms 
tf 
1 ms 
t2 
600 ms  
t3a 
2,5 s 
t3b 
9 s 
Ri 
10 mΩ ≤ Ri ≤ 100 mΩ 
Number of cycles 
1. Short test: 3x with t3a 
2. Endurance test: 1000x with t3b 
Both tests shall be carried out in succession.  
Number of DUTs 
6 
 
 
Figure 4: Test pulse E48-02 Transient overvoltage, load dump 
3.3.3 
Requirements 
Voltage 
Time 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 20 页
Page 19 
LV 148 
29.08.2011 
 
Functional status A 
3.4 
E48-03 Transient event in lower operating range with functional limitation 
3.4.1 
Purpose 
Transient undervoltages in the on-board electric system may occur due to switching on of loads. 
This test is intended to simulate such undervoltages. 
3.4.2 
Test 
Table 10: Test parameters E48-03 Transient event in lower limited range  
Operating mode of the DUT 
Operating mode II.c  
U0 
U48min,unlimited 
U1 
U48min,low,limited 
t0 
60 s 
tf 
1,8 ms 
t1 
500 ms 
tr 
1,8 ms 
t2 
500 ms 
Number of cycles 
1 
Number of DUTs 
6 
 
 
Figure 5: Test pulse E48-03 Transient event in lower limited range  
3.4.3 
Requirement 
Voltage 
Time
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 21 页
Page 20 
LV 148 
29.08.2011 
 
Functional status B 
3.5 
E48-04 Recuperation 
3.5.1 
Purpose 
This test simulates a prolonged recuperation phase. 
3.5.2 
Test 
Table 11: Test parameters E448-04 Recuperation 
Operating mode of the DUT 
Operating mode II.c  
U0 
U48min,unlimited 
U1 
U48max,high,limited 
t0 
60 s 
tr 
100 ms 
t1 
60 s 
tf 
100 ms 
t2 
60 s 
Number of cycles 
1 
Number of DUTs 
6 
 
 
 
Figure 6: Test pulse E48-04 Recuperation 
 
Voltage 
Time 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 22 页
Page 21 
LV 148 
29.08.2011 
 
3.5.3 
Requirements 
The assessment of the test result depends on the application of the component. A distinction is 
made between 
a) Components relevant to recuperation and driving mode: 
functional status A 
b) All other components:  
 
 
 
 
functional status B 
3.6 
E48-05 Superimposed alternating voltage 
3.6.1 
Purpose 
Voltages may be superimposed to the on-board electrical system. The superimposed alternating 
voltage may be applied during the entire generation-based operation. This test is intended to simu-
late such a situation. 
3.6.2 
Test part 1 
Table 12: Test parameters E48-05 Superimposed alternating voltage 
Operating mode of the DUT 
Operating mode II.c  
Ri 
10 mΩ ≤ Ri ≤ 100 mΩ 
U48test 
U48min,unlimited 
ttest 
30 min 
f 
F1: 15 Hz to 30 kHz 
F2: 30 kHz to 200 kHz 
Frequency sweep duration 
2 min 
Type of frequency sweep 
triangular, logarithmic 
U48pp 
for F1: 6 V 
for F2: 2 V 
Number of DUTs 
6 
3.6.3 
Test part 2 
Table 13: Test parameters E48-05 Superimposed alternating voltage 
Operating mode of the DUT 
Operating mode II.c  
Ri 
10 mΩ ≤ Ri ≤100 mΩ  
U48test 
U48max,unlimited 
ttest 
30 min 
f 
F1: 15 Hz to 30 kHz 
F2: 30 kHz to 200 kHz 
Frequency sweep duration 
2 min 
Type of frequency sweep 
triangular, logarithmic 
U48pp, 
for F1: 6 V 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 23 页
Page 22 
LV 148 
29.08.2011 
 
for F2: 2 V 
Number of DUTs 
6 
 
Figure 7: Test pulse E48-05 Superimposed alternating voltage 
3.6.4 
Requirements for part 1 and part 2 
Functional status A 
All outputs shall remain within the defined limits during the test - this shall be verified throughout 
the test duration. 
3.7 
E48-06 Slow decrease and increase of the supply voltage 
3.7.1 
E48-06 for storage free operation 
******* 
Purpose 
The slow decrease and increase of the supply voltage is simulated as it occurs during the slow 
discharging and recharging processes. 
 
Time 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 24 页
Page 23 
LV 148 
29.08.2011 
 
******* 
Test 
Table 14: Test parameters E48-06 Slow decrease and increase of the supply voltage 
Operating mode of the DUT 
At the beginning of the test, the DUT is in operating 
mode A. 
U0 
U48max,unlimited 
Voltage gradient 
± 2 V/min 
U1 
U48min,unlimited 
U2 
0 V 
t1 
Until the fault memory is completely read out 
Number of cycles 
1 cycle in operating mode II.c 
1 cycle in operating mode II.a 
Number of DUTs 
6 
 
Figure 8: Test pulse E48-06 Slow decrease and increase of the supply voltage 
 
 
3.7.2 
E48-06 for operation with storage part 1 
******* 
Purpose 
The slow switch-off of the supply voltage is simulated as it occurs during a storage disconnection. 
******* 
Test 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 25 页
Page 24 
LV 148 
29.08.2011 
 
Table 15: Test parameters E48-06 for operation with storage part 1 
Operating mode of the DUT 
Operating mode II.a 
U0 
U48max,unlimited 
U1 
U48min,unlimited 
U2 
U48stoprotect 
U3 
0 V 
t0 
100 ms 
tf1 
8 min  
t1 
≥ 60 s (the fault memory is read during this phase) 
tf2 
8 min 
t2 
60 s 
tf3 
3 s 
t3 
60 s 
Number of cycles 
1 cycle 
Number of DUTs 
6 
 
Figure 9: Test pulse E48-06 for operation with storage part 1 
Slow decrease of the supply voltage with storage switch-off 
3.7.3 
E48-06 for operation with storage part 2 
******* 
Purpose 
The activation of the generator while the storage is disconnected with subsequent switch-on of the 
discharged storage is simulated. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 26 页
Page 25 
LV 148 
29.08.2011 
 
******* 
Test 
Table 16: Test parameters E48-06 for operation with storage part 2 
Operating mode of the DUT 
Operating mode II.b after final voltage has been 
reached 
U0 
0 V 
U1 
U48n 
U48pp 
6 V at 10 kHz 
U2 
U48stoprotect 
t0 
100 ms 
tr1 
300 ms 
t1 
≥ 60 s (the fault memory is read during this phase) 
tf1 
1 ms  
tr2 
14 min 
t3 
100 ms 
Number of cycles 
1 cycle 
Number of DUTs 
6 
 
Figure 10: Test pulse E48-06 for operation with storage part 2 
 
First the generator is switched on, then the storage. 
3.7.4 
Requirement 
The assessment of the test result depends on the voltage range applied to the component during 
the test.  
A distinction is made between 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 27 页
Page 26 
LV 148 
29.08.2011 
 
a) within the defined operating voltage of the component: 
functional status A 
No fault memory entries shall occur. 
b) outside the defined operating voltage of the component: 
functional status B 
3.8 
E48-07 Slow decrease, abrupt increase of the supply voltage 
3.8.1 
Purpose 
This test simulates the slow decrease of the voltage of the on-board electrical system to the stor-
age protection voltage with subsequent switch-off to 0 V, followed by the abrupt reconnection of the 
storage voltage from a charged or new storage battery. 
NOTE: The storage battery is not activated until it has been connected to the on-board electrical 
system (no bouncing during contacting). 
3.8.2 
Test 
Table 17: Test parameters E48-07 Slow decrease, abrupt increase of the supply voltage 
Operating mode of the DUT 
Operating mode II.a 
U0 
U48max,unlimited 
U1 
U48min,unlimited 
U2 
U48stoprotect 
U3 
0 V 
U4 
U48n 
t0 
100 ms 
tf1 
8 min 
t1 
≥ 60 s (the fault memory is read during this phase) 
tf2 
8 min 
t2 
60 s 
tf3 
3 s 
t3 
300 s 
tr1 
1 ms 
t4 
100 ms 
Number of cycles 
1 cycle in operating mode II.a 
Number of DUTs 
6 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 28 页
Page 27 
LV 148 
29.08.2011 
 
 
Figure 11: Test pulse E48-07 Slow decrease, abrupt increase of the supply voltage 
3.8.3 
Requirement 
At the end of the test, the system shall be in functional status A. 
3.9 
E48-08 Reset behavior 
3.9.1 
Purpose 
The reset behavior of a component (control unit logic is supplied by BN48) in its environment is 
simulated and tested. Test boundary conditions (e.g. assembly, terminal, system) shall be de-
scribed in detail. 
During operation, an arbitrary sequence of repeated switching-on/off processes occurs; this shall 
not lead to an undefined behavior of the component. 
The reset behavior is reflected by a voltage variance and a time variance. Two different test se-
quences are required to simulate different switch-off times. A component shall always be subjected 
to both sequences. 
The test applies to components with logic supply from BN48. 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 29 页
Page 28 
LV 148 
29.08.2011 
 
3.9.2 
Test 
Table 18: Test parameters E48-08 Reset behavior 
Operating mode of the DUT 
Operating mode II.c  
U0 
U48min,unlimited 
ΔU1 (range from U0 to U1 
2 V 
U1 
U48min,low,limited 
ΔU2 (range U48min,low,limited to 0 V) 
0,5 V 
U2 
0 V 
t0 
at least 10 s and until the DUT has returned to 100% 
serviceability (all systems rebooted without error) 
t1a 
5 s 
t1b 
100 ms  
tf/r 
≤ 100 ms 
Number of cycles 
1 
Number of DUTs 
6 
 
Figure 12: Test pulse E48-08 Reset behavior 
3.9.3 
Requirement 
- Functional status A when U48min,unlimited is reached 
- Functional status B to U48min,low,limited 
- Functional status C below U48min,low,limited 
Undefined operating states shall not occur under any circumstances. 
Evidence of compliance with the specified threshold, at which the component leaves the functional 
status A for the first time, shall be provided and documented. 
3.10 
E48-09 Short interruptions 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 30 页
Page 29 
LV 148 
29.08.2011 
 
3.10.1 
Purpose 
The behavior of the component at short interruptions of different durations is simulated. 
3.10.2 
Test 
Table 19: Test parameters E48-09 Short interruptions 
Operating mode of the DUT 
Operating mode II.c  
Test set-up 
Schematic circuit according to Figure 14. The model-
ing of the on-board electrical system shall be coordi-
nated with the department responsible.  
The total resistance R incl. line routing is ≤ 100 mΩ 
In condition "On",  S1 is closed and S2 open. 
In condition "Off",  S1 is open and S2 closed. 
U48test 
U48n 
t1 
The supply voltage is interrupted by U48test at varying 
intervals. The following sequence shall be complied 
with for this purpose: 
100 µs to 1 ms 
100µs steps 
1 ms to 10 ms 
1ms steps 
10 ms to 100 ms 
10ms steps 
100 ms to 2 s 
100ms steps 
DUT On - function On 
> 10 s 
t2 
The test voltage U48test shall be held at least until the 
DUT has achieved 100% serviceability (all systems 
rebooted without error). 
Number of cycles 
1 
Number of DUTs 
6  
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 31 页
Page 30 
LV 148 
29.08.2011 
 
The duration of the voltage dip increases at the intervals specified in Table 19. This results in a 
diagram as shown in 
 
Figure 13: Test pulses E48-09 Short interruptions. 
 
Figure 13: Test pulses E48-09 Short interruptions 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 32 页
Page 31 
LV 148 
29.08.2011 
 
 
Figure 14: Schematic circuit diagram E48-09 Short interruptions 
3.10.3 
Requirements 
The time value t1 at which the DUT leaves functional status A for the first time shall be recorded. 
The test is passed if the DUT achieves functional status A for t1 ≤ 100 µs, otherwise functional sta-
tus C. A deviating value for the permissibility of functional status C shall be defined in the Compo-
nent Requirement Specifications. 
3.11 
E48-10 Start pulses 
3.11.1 
Purpose 
When starting the engine, the storage voltage drops to a lower value for a short period and then 
rises again.  
3.11.2 
Test 
Table 20: Test parameters E48-10 Start pulses 
Operating mode of the DUT 
For components relevant for starting: 
Operating mode II.c  
For components not relevant for starting: 
Operating mode II.b 
Test pulse 
For components relevant for starting: 
Cold start: test pulse "normal" and "severe" 
For components not relevant for starting: 
Cold start: test pulse "normal"  
U0 
U48n for cold start normal 
40 V for cold start severe 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 33 页
Page 32 
LV 148 
29.08.2011 
 
U1 
U48min,low,limited 
t0 
2 s 
tf 
1 ms 
t1 
1 s 
tr 
1 ms 
t2 
2 s 
Number of cycles 
10 
Number of DUTs 
6 
 
 
Figure 15: Test pulse: E48-10 Cranking pulses 
In  the case of warm start, the operating range without limitation is maintained. 
 
3.11.3 
Requirement 
******** 
Components relevant for starting 
No fault memory entry shall occur. 
The vehicle shall always be capable of being started. 
Test 1 - Cold start 
Test pulse "normal": Functional status A 
Test pulse "severe": Functional status A 
******** 
Components not relevant for starting 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 34 页
Page 33 
LV 148 
29.08.2011 
 
Test 1 - Cold start 
Test pulse "normal": Functional status B 
3.12 
E48-11 Loss of ground BN48 
3.12.1 
Purpose 
This test simulates a loss of ground of a BN48 component which is supplied exclusively by BN48 
and has interfaces to BN12 components (e.g. CAN/LIN/FlexRay bus or other analog or digital sig-
naling lines). 
Care shall be taken to ensure that the loss of ground of the BN48 component does not cause a 
malfunction in the other communication participants of the  BN12 (e.g. due to excessive voltages or 
polarity reversal). In addition, care shall be taken to ensure that the loss of ground does not result 
in the destruction of any components. 
3.12.2 
Test - general 
- 
Connect the BN48 component to be tested (DUT) on a test bench to the test bench compo-
nents partial on-board electrical system 1 (TB1) and partial on-board electrical system 2 
(TB2) (see Figure 16). 
- 
Connect all signaling and bus lines of the DUT to TB1 and TB2: 
o Distribution of the transmission signals of the DUT to TB1 and TB2. 
o Transmission of receive signals of the DUT from TB1. 
- 
TB1 and TB2 simulate the restbus and with their interface modules fulfill the requirements 
of the Component Requirement Specifications (automotive qualified and approved interface 
modules). 
- 
Between TB1 and TB2, monitored bus communication which is  
o high-performing,  
o bidirectional,  
o with message counter and CRC protected  
o is set up (any faults occurring are immediately detected and recorded). 
- 
The signaling lines between DUT and TB1/TB2 are  
o monitored in TB1 and TB2 (signal content);  
o any faults occurring are detected in TB1/TB2 and recorded. 
- 
The voltages and currents of all communication lines (bus and signaling line) shall be moni-
tored in or on TB1 and TB2 for any overshooting of the specification limits. 
Detailed coordination shall be carried out with the person responsible for the Component Require-
ment Specifications. 
Table 21: Test parameters E48-11 Loss of ground BN48 
Operating mode of the DUT 
Operating mode II.c  
ttest 
refer to tests 
U48test 
U48n 
U12test 
14 V 
Ttest 
Tmax - 20 °C 
Number of cycles for each test 
1 
Number of DUTs 
6 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 35 页
Page 34 
LV 148 
29.08.2011 
 
 
Figure 16: Schematic circuit diagram E48-11 Loss of ground BN48 
3.12.3 
Test 1 
S1 closed 
S2 closed 
All components  DUT/TB1/TB2 operating properly. 
S2 is opened. 
3.12.4 
Requirements 1 
Faults shall be stored in TB1 and TB2: 
- 
TB1: fault in bus communication with TB2  
- 
TB2: fault in bus communication with TB1 
- 
TB2: fault in signaling lines 
No malfunction in DUT – functional status A. 
3.12.5 
Test 2 
S1 closed 
S2 closed 
All components  DUT/TB1/TB2 operating properly. 
S1 is opened. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 36 页
Page 35 
LV 148 
29.08.2011 
 
The test duration is 30 minutes from the time of opening of S1. 
3.12.6 
Requirement 2 
In TB1 and TB2, no voltages shall occur above the defined interface voltages, and no currents 
above the defined interface current. This applies to all bus and signaling lines. 
Bus communication: The bus communication between TB1 and TB2 operates properly - no faults in 
fault memory. 
Signaling line: 
Distinction between cases 
a) DUT reads this line, i.e. TB1 is the transmitter 
 
Requirement: no fault entry in TB1 and TB2 
b) DUT is the transmitter. 
 
Requirement: fault entry in TB1 and TB2 due to loss of signal  
3.13 
E48-12 Ground offset 
3.13.1 
Purpose 
In components with several subsystems, potential differences between the individual supply inputs 
may occur. Care shall be taken to ensure that the functionality of the component is not influenced 
in the case of a potential difference to ground of up to ± 1,0 V. 
3.13.2 
Test 
If the DUT has several voltage and ground connections for the BN48, the test shall be performed 
separately for each connection point. 
A ground offset ± 1,0 V shall always be provided for in the interface dimensioning between two 
components. 
The component shall be connected as shown in Figure 17. 
Table 22: Test parameters E48-12 Ground offset 
Operating mode of the DUT 
Operating mode II.c  
U48test 
U48n 
U0 
1,0 V 
Number of cycles 
both switching positions 
Number of DUTs 
at least 6 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 37 页
Page 36 
LV 148 
29.08.2011 
 
 
Figure 17: Schematic circuit diagram E48-12 Ground offset (UB48) 
3.13.3 
Requirement 
Functional status A 
 
Component 1 
Component 2 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 38 页
Page 37 
LV 148 
29.08.2011 
 
3.14 
E48-13 Internal withstand voltage 
3.14.1 
Purpose 
The internal withstand voltage between BN48 pins and BN12 pins is determined in cases where 
both voltages are used in one component. 
3.14.2 
Test 
Table 23: Test parameters E48-13 Internal withstand voltage 
Operating mode of the DUT 
Operating mode I.a  
U48test 
U48stoprotect 
ttest 
60 min 
Frel 
50 % 
Ttest 
35 °C 
Test points 
Connection of test voltage between  
─ 
both supply terminals 
─ 
additional test points agreed with the relevant depart-
ment responsible 
see Figure 18 
Number of cycles 
1 
Number of DUTs 
6 
 
Figure 18: Schematic circuit diagram E48-13 Internal withstand voltage 
3.14.3 
Requirement 
The insulation resistance shall be at least 1 MΩ. Evidence shall be provided showing that the DUT 
has not been damaged. 
Functional status A 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 39 页
Page 38 
LV 148 
29.08.2011 
 
3.15 
E48-14 Closed-circuit current 
3.15.1 
Purpose 
The closed-circuit current consumption of components relevant to closed-circuit currents is deter-
mined. 
3.15.2 
Test 
For components with follow-on current (e.g. fan), the closed-circuit current consumption shall be 
determined after the follow-on current has stopped. 
Table 24: Test parameters E48-14 Closed-circuit current 
Operating mode of the DUT 
Operating mode II.a 
U48test 
U48n 
Test  
Temperature range 
Max. closed-circuit current 
Tmin to 40 °C 
0,1 mA  
40 °C to Tmax 
0,2 mA  
Number of DUTs 
6 
3.15.3 
Requirement 
For all DUTs, a closed-circuit consumption target of 0 mA applies. 
Follow-on current functions shall be approved by the department responsible for closed-circuit cur-
rent management. 
3.16 
E48-15 Operation in range without functional limitation 
3.16.1 
Purpose 
The operating behavior at the range boundaries is tested. 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 40 页
Page 39 
LV 148 
29.08.2011 
 
3.16.2 
Test 
Table 25: Test parameters E48-15 Operation in range without functional limitation 
Operating mode of the DUT 
Operating mode II.c  in B1 
U0 
U48n 
U1 
U48min,unlimited 
U2 
U48max,unlimited 
t0 
100 ms 
tf1 
1 ms 
t1 
1 s 
tr 
1 s 
t2 
10 s 
tf2 
1 s 
t3 
100 ms 
Ttest 
Tmax, TRT and Tmin 
Number of cycles 
10 
Number of DUTs 
6 
 
Figure 19: Test pulse E48-15 Operation in range without functional limitation 
 
3.16.3 
Requirement 
Functional status A 
3.17 
E48-16 Operation in upper range with functional limitation 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 41 页
Page 40 
LV 148 
29.08.2011 
 
3.17.1 
Purpose 
The operating behavior with change and at the range boundaries is tested. 
3.17.2 
Test 
Table 26: Test parameters E48-16 Operation in upper range with functional limitation 
Operating mode of the DUT 
Operating mode II.c in B1 
U0 
U48n 
U1 
U48max,high,limited 
U2 
U48max,unlimited 
U3 
U48max,unlimited + 1 V 
t0 
100 ms 
tr1 
4 s  
t1  
10 s 
tf1 
2 s  
t2 
10 s 
tr2 
2 s  
t3 
10 s 
tf2 
2 s  
t4 
100 ms 
Ttest 
Tmax, TRT and Tmin 
Number of cycles 
10 
Number of DUTs 
6 
 
Figure 20: Test pulse E48-16 Operation in upper range with functional limitation  
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 42 页
Page 41 
LV 148 
29.08.2011 
 
3.17.3 
Requirement 
See 
 
Figure 20: No fault is registered in the fault memory. 
3.18 
E48-17 Operation in lower range with functional limitation 
3.18.1 
Purpose 
The operating behavior with change and at the range boundaries is tested. 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 43 页
Page 42 
LV 148 
29.08.2011 
 
3.18.2 
Test 
Table 27: Test parameters E48-17 Operation in lower range with functional limitation 
Operating mode of the DUT 
Operating mode II.c in B1 
U0 
U48n 
U1 
U48min,low,limited 
U2 
U48min,unlimited 
U3 
U48min,low,limited + 1V 
t0 
100 ms 
tf1 
2 s 
t1  
10 s 
tr1 
4 s 
t2 
10 s 
tf2 
2 s 
t3 
10 s 
tr2 
2 s 
t4 
100 ms 
Ttest 
Tmax, TRT and Tmin 
Number of cycles 
10 
Number of DUTs 
6 
 
Figure 21: Test pulse E48-17 Operation in lower range with functional limitation 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 44 页
Page 43 
LV 148 
29.08.2011 
 
3.18.3 
Requirement 
See 
 
Figure 21: No fault is registered in the fault memory. 
3.19 
E48-18 Overvoltage range 
3.19.1 
Purpose 
This test is intended to simulate load switch-off during storage charging and examine the operating 
behavior into the overvoltage range. 
3.19.2 
Test 
Table 28: Test parameters E48-18 Overvoltage range 
Operating mode of the DUT 
Operating mode II.c  
U0 
U48n 
U1 
U48r 
U2 
U48max,unlimited + 1 V 
t0 
100 ms 
tr1 
10 ms 
t1 
1 s 
tf1 
1 s 
t2 
10 s 
tr2 
1 ms 
t3 
2 s 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 45 页
Page 44 
LV 148 
29.08.2011 
 
tf2 
1 s 
t4 
5 s 
tr3 
10 s 
t5 
2 s 
tf3 
10 s 
t6 
100 ms 
Ttest 
Tmax, TRT and Tmin 
Number of cycles 
10 
Number of DUTs 
6 
 
Figure 22: Test pulse E48-18 Overvoltage range 
3.19.3 
Requirements 
See Figure 22: Test pulse E48-18 Overvoltage range. 
In the fault memory, only the overvoltage fault shall be registered. 
3.20 
E48-19 Undervoltage range 
3.20.1 
Purpose 
The change of the operating behavior into the undervoltage range is tested. 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 46 页
Page 45 
LV 148 
29.08.2011 
 
3.20.2 
Test 
Table 29: Test parameters E48-19 Undervoltage range 
Operating mode of the DUT 
Operating mode II.c  
U0 
U48n 
U1 
U48stoprotect 
U2 
U48min,low,limited + 6V 
t0 
100 ms 
tf1 
1 s 
t1 
1 s 
tr1 
10 ms 
t2 
10 s 
tf2 
1 s 
t3 
2 s 
tr2 
1 ms 
t4 
5 s 
tf3 
10 s 
t5 
2 s 
tr3 
10 s 
t6 
100 ms 
Ttest 
Tmax, TRT and Tmin 
Number of cycles 
10 
Number of DUTs 
6 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 47 页
Page 46 
LV 148 
29.08.2011 
 
 
Figure 23: Test pulse E48-19 Undervoltage range 
3.20.3 
Requirement 
See Figure 23: Test pulse E48-19 Undervoltage range. 
In the fault memory, only the undervoltage fault shall be registered. 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 48 页
Page 47 
LV 148 
29.08.2011 
 
3.21 
E48-20 Fault current part 1 
3.21.1 
Purpose 
The fault current capacity of a component connected with both BNs (BN12 and BN48) is tested. 
3.21.2 
Test  
Connect the component to be tested (DUT) on a test bench according to Figure 24: Schematic cir-
cuit diagram: E48-20 Fault current part 1. 
Switch S1 is open (T.41 is separated). 
T. 40 is supplied (the behavior in the presence of two different voltages is examined). 
The BN12 part of the component is supplied. 
Measure the current at T.40 of the component. 
Table 30: Test parameters E48-20 Fault current part 1 
Operating mode of the DUT 
II.a 
Test set-up 
see Figure 24 
U48test 
a) U48n 
b) U48shprotect 
U12test 
14 V 
ttest 
10 min 
Ttest 
TRT 
Number of cycles 
1 
Number of DUTs 
6 
 
Figure 24: Schematic circuit diagram: E48-20 Fault current part 1 
3.21.3 
Requirement 
The following requirement applies to the current at T.40: 
I ≤ │1µA│ 
3.22 
E48-21 Fault current part 2 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 49 页
Page 48 
LV 148 
29.08.2011 
 
3.22.1 
Purpose 
The fault current capacity of a component supplied by both BNs (BN12 and BN48) is tested. 
3.22.2 
Test 
Connect the component to be tested (DUT) on a test bench according to Figure 25: Schematic cir-
cuit diagram E48-21 Fault current part 2.  
Switches S1, S2, S3 and S4 are open (the DUT is not supplied). 
All BN12 contacts (supply and communication) are connected with each other (short-circuited). 
All BN48 contacts (supply) are connected with each other (short-circuited). 
Apply a voltage of 70,0 V between BN48 and BN12, whereby: BN48 = 70 V and BN12 = 0 V. 
Measure the current flowing through this 70 V supply and therefore through the component. 
Table 31: Test parameters E48-21 Fault current part 2 
Test set-up 
see Figure 25 
U48test 
U48n 
U12test 
14 V 
ttest 
10 min 
Ttest 
TRT 
Number of cycles 
1 
Number of DUTs 
6 
 
Figure 25: Schematic circuit diagram E48-21 Fault current part 2 
3.22.3 
Requirement 
The following requirement applies to the current between BN12 and BN48:  
I ≤ │1µA│ 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 50 页
Page 49 
LV 148 
29.08.2011 
 
4 
Glossary, abbreviations 
Table 32: Abbreviations 
Abbreviation 
Definition  
Explanation 
BN12 
12V vehicle power supply 
12V on-board electrical system 
BN24 
24V vehicle power supply 
24V on-board electrical system 
BN48 
48V vehicle power supply 
48V on-board electrical system 
DUT 
Device Under Test 
Test sample 
FS 
Fehlerspeicher 
Fault memory 
GND 
Ground 
Electrical ground 
TB 
Partial vehicle power supply 
Partial on-board vehicle elec-
trical system 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)

