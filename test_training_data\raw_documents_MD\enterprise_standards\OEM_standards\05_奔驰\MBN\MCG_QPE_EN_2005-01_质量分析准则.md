# MCG_QPE_EN_2005-01_质量分析准则.pdf

## 文档信息
- 标题：(Microsoft Word - DaimlerChrysler Guideline Defective Parts Analysis MCG Kom\205)
- 作者：hroesel
- 页数：22

## 文档内容
### 第 1 页
 
 
 
 
 
 
 
 
 
 
Guideline regarding Defective Parts Analysis 
of Reclaimed Vehicle Parts from the Field 
(Quality Analysis Specification) 
 
 
Version date: 01/2005 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
DaimlerChrysler AG 
Quality Engineering Center (MCG/QPE) 
HPC G163 
71059 Sindelfingen
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 2 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 2/22 
 
Table of Contents 
 
0 Scope...............................................................................................................................................................5 
1 Purpose............................................................................................................................................................5 
2 Introduction......................................................................................................................................................5 
3 General information..........................................................................................................................................5 
3.1 Definition of terms....................................................................................................................................5 
3.1.1 Defective part................................................................................................................................5 
3.1.2 Defective part analysis...................................................................................................................5 
3.1.3 Diagnostic findings ........................................................................................................................5 
3.1.4 Quality analysis report...................................................................................................................5 
3.1.5 Test report statement....................................................................................................................6 
3.1.6 NTF and NG ...................................................................................................................................6 
3.2 Goal of the guideline.................................................................................................................................6 
3.2.1 Goal of the defective part analysis.................................................................................................6 
3.2.2 Goal of the quality analysis report..................................................................................................6 
3.3 Supplier's responsibility............................................................................................................................7 
3.4 Communication ........................................................................................................................................7 
3.5 Auditing the defective part analysis at the supplier...................................................................................7 
4 Defective part analysis .....................................................................................................................................7 
4.1 Defective part analysis concept................................................................................................................7 
4.2 Principles of the defective part analysis....................................................................................................7 
4.3 Vehicle-realistic part tests........................................................................................................................8 
4.4 Testing procedure.....................................................................................................................................8 
4.5 Process stability .......................................................................................................................................9 
4.6 Physical damages.....................................................................................................................................9 
5 Conducting the defective part analysis...........................................................................................................10 
5.1 Defective part analysis procedure ..........................................................................................................10 
5.2 Description of the test procedure...........................................................................................................11 
5.2.1 Supplier's acceptance of incoming goods (1)...............................................................................11 
5.2.2 Recording and registration (2) .....................................................................................................11 
5.2.3 Standard test (3)..........................................................................................................................11 
5.2.4 Standard test results (9)+(15)......................................................................................................12 
5.2.5 Standard test results (4)..............................................................................................................12 
5.2.6 Complaint-oriented defect reproduction test (5)..........................................................................12 
5.2.7 Environmental test (6) .................................................................................................................12 
5.2.8 Vehicle simulation test (7) ...........................................................................................................13 
5.2.9 Long-term test (8)........................................................................................................................13 
5.2.10 Marking parts (10+14).................................................................................................................13 
5.2.11 Cause analysis (11)......................................................................................................................13 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 3 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 3/22 
 
5.2.12 Definition of corrective actions (12).............................................................................................14 
5.2.13 Quality analysis report (13)..........................................................................................................14 
5.2.14 Complete statement provided to QEC (16) ..................................................................................15 
5.2.15 Reclaimed part provided to QEC (17)...........................................................................................15 
5.2.16 Time limits...................................................................................................................................15 
******** Overview.......................................................................................................................15 
******** Delay ............................................................................................................................15 
5.3 Documentation and reporting.................................................................................................................16 
5.3.1 Document and report types.........................................................................................................16 
5.3.2 General minimum requirements...................................................................................................16 
5.3.3 Analyzability.................................................................................................................................16 
5.3.4 Format 16 
5.3.5 Basic information.........................................................................................................................16 
5.3.6 Test protocol ...............................................................................................................................17 
5.3.7 Test report statement..................................................................................................................17 
5.3.8 General reporting requirements...................................................................................................17 
5.3.9 Data retention .............................................................................................................................18 
6 Appendix ........................................................................................................................................................19 
6.1 Index of illustrations...............................................................................................................................19 
6.2 Index of tables........................................................................................................................................19 
6.3 Index of abbreviations ............................................................................................................................19 
6.4 Specification interface for data export/import .......................................................................................20 
6.4.1 Table set-up.................................................................................................................................20 
6.4.2 Rules for file construction............................................................................................................20 
6.4.3 Data formats................................................................................................................................21 
6.4.4 Header construction for the test report header............................................................................21 
6.4.5 Data record construction for the test report header ....................................................................21 
6.4.6 Header construction for the test report items..............................................................................21 
6.4.7 Data record construction for the test report items (damaged part)..............................................22 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 4 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 4/22 
 
Change index 
 
 
Date 
Version 
Revised by 
Change 
DaimlerChrysler AG 
authorization 
14.01.2005 
1.0 
Martin Westhof 
First edition 
Dr. Paul Starker 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 5 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 5/22 
 
0 Scope 
This document applies to vehicle parts that were provided to the supplier by the DaimlerChrysler Quality Engineering 
Center (QEC) for analysis.  
1 Purpose 
This document is a binding description of the general procedure for supplier testing of reclaimed vehicle parts from 
the field as well as the contents and form of the quality analysis report.  
The following items are described: 
• Logistics (incoming and outgoing defective parts) 
• Defective parts analysis (tests, analyses) 
• Documentation (quality analysis report, etc.) 
2 Introduction 
The customers for our premium and luxury brands Mercedes-Benz and Maybach place the highest demands and 
expectations on our automobile products. Any deviations from these expectations lead to disproportional customer 
dissatisfaction, costs and loss of image. The purchased products represent an elementary component of our 
products. If these products are determined to be defective by our external organizations and then exchanged, then 
the cause of the complaint must be determined as fast as possible. A quick, capable state-of-the-art defective parts 
analysis is an important part of the corrective action process at DaimlerChrysler and at the suppliers. Weak points in 
ongoing developments can only be recognized and fundamentally corrected with the help of a closed feedback loop. 
The same thing applies to management of sub-suppliers on the part of the supplier. 
3 General information 
3.1 
Definition of terms 
3.1.1 
Defective part 
"Defective part" refers to the vehicle part that was rejected by the customer and/or the authorized MB repair shop. It 
will be viewed as damaged and/or defective (NG) until complete functionality can be proven. 
3.1.2 
Defective part analysis 
"Defective part analysis" refers to any tests and analyses that are conducted on-site at the supplier to determine the 
defect and its cause. 
The term “defective part analysis” corresponds the common term “quality analysis” as well.  
3.1.3 
Diagnostic findings 
“Diagnostic findings” refers to the results of the defective part analysis. 
3.1.4 
Quality analysis report 
“Quality analysis report” refers to the specified documentation of the results of the defective part analysis (see 
Chapter 5 .3) for a defective part. 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 6 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 6/22 
 
3.1.5 
Test report statement 
The test report statement contains the quality analysis reports for every defective part listed in the test report. 
3.1.6 
NTF and NG 
Two options for the results of the defective part analysis are defined. 
1. NTF (no trouble found): This is assigned when a part does not show any defects and all tolerances are 
maintained within the scope of the defective part analysis. In addition, the customer’s complaints do not point 
to development or production defects. 
2. NG (no good): This is assigned when a defect was found or a defect pattern appears.  
NG is assigned when: 
• 
Sporadic, non-reproducible defects occur in an analysis step 
• 
There are defective memory entries that point to an internal defect 
• 
The complaint involves known defect patterns (in conjunction with SW / HW versions) 
3.2 
Goal of the guideline 
The goal of this guideline is to describe a mandatory examination concept that ensures uniform and/or comparable 
quality when examining defective parts with the various suppliers and the various components.  
The implementation of this guideline should provide the following results: 
1. Fast recognition of the causes of failure in the field and incorporation of corrective actions 
2. Improvements in product and process quality 
3. Provision of evaluations and data regarding failures in the field 
4. Reduction in warranty and goodwill costs and possible buybacks. 
5. Secure data for obtaining acceptance rates for recovery processing 
3.2.1 
Goal of the defective part analysis 
The goals of the defective part analysis are: 
1. Determining the precise cause of the defect 
2. Determining the basic weak points in the vehicle part that led to the defect 
3. Reproducing the defect under field-approximate vehicle conditions (if necessary, with simulation of the vehicle 
parts, the environmental conditions, etc.) 
The following framework conditions need to be fulfilled to reach these goals: 
• The completeness of the individual tests must be ensured 
• The reproducibility of the tests must also be ensured (including at the various test stations) 
• The defective part tests must be conducted independently of the tester 
• The preciseness of the test must be ensured 
3.2.2 
Goal of the quality analysis report 
The goal of the quality analysis report is to provide complete, meaningful and traceable information regarding: 
1. Cause of the defect 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 7 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 7/22 
 
2. Basic weak points in the vehicle part 
3. Analysis steps conducted 
To do this, the following is imperative: 
• The uniformity of the suppliers’ quality analysis reports is ensured 
• The consistency of the quality of the reports is indicated 
• The tests conducted for recording the results are traceable 
• The quality of the analysis is independent of the tester 
3.3 
Supplier's responsibility 
The supplier is responsible for implementing the guideline specific to the component. The supplier ensures that the 
requirements of this guideline are fulfilled with respect to the QEC even upon transfer of the defective part to a 
subcontractor. 
The QEC always has the option of being present during the defective part analysis. 
3.4 
Communication 
To ensure a smooth process, both sides will name a contact person as well as a substitute in the event that the first 
contact is absent. 
3.5 
Auditing the defective part analysis at the supplier 
The QEC reserves the right to audit the defective part analysis process for creating a quality analysis report at any 
time upon prior notification. 
4 Defective part analysis 
4.1 
Defective part analysis concept 
It is important that all significant tests for reproducing the cause of the complaint is conducted and that the test 
results are included in the statement. 
The following basic procedure is used when analyzing defective parts: 
1. Each defective part is initially subjected to a standard test. The general status of the defective part with regard 
to function and quality is determined in the standard test. 
2. Attempts are made to reproduce the defect in the subsequent defect reproduction test. 
3. If this is not successful, the test scope is expanded successively ("expanded test scope") until either a defect 
can be reproduced or the absence of a defect is determined for the rejected part.  
4. If a defect can be reproduced, the defective part is marked as "NG". 
5. Subsequently, the defect pattern and the cause of the defect are analyzed, and corrective actions are defined. 
6. All tests and test results as well as a summary of the results with the reasoning (findings) must then be 
documented. 
4.2 
Principles of the defective part analysis 
• To verify sporadic or as-of-yet unknown defect patterns, the following information sources, among others, 
must be used for the defective part analysis: 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 8 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 8/22 
 
o Parts history 
o Defective part analysis database 
o Information with respect to known defect patterns (model-based) 
o Defect statistics 
o Defect catalog 
o Analysis sheets (with special actions) 
o Process documentation from the production schedule 
o Component specification 
o Miscellaneous documented agreements with DaimlerChrysler employees 
• The defective parts must be tested in an unchanged condition 
• No SW or HW adjustments can be made prior to the defective part analysis 
• The test and measuring equipment used must be calibrated regularly (in accordance with VDA guidelines) 
• The individual test steps must be determined specific to the component and must have limit and tolerance 
values 
• Qualified personnel, corresponding to the requirements of a qualification matrix and, if necessary, trained 
according to the training plan, must be used for the defective part analysis 
• Continuous improvement in the defective part analysis must be ensured and documented 
4.3 
Vehicle-realistic part tests 
The part tests must be adapted to the actual requirements of the component-specific usage profile in the vehicle. 
Variables that can influence the reliability of components are: 
• Vehicle environment (temperature, climate, and vibration profiles; system integration; etc.) 
• Stress (active operational condition, wear, aging, etc.) 
• Design (design elements, known weak points, etc.) 
• Material (selection, material changes with temperature, over time, etc.) 
• Production processes (process stability of the production processes, logistics processes, quality capabilities of 
sub-suppliers, etc.) 
4.4 
Testing procedure 
• Detailed work instructions with regard to the testing procedure at the worksite must be created 
• The testing procedure should be automated to the extent possible for reasons of process stability 
• Follow-up adjustments in the defective part analysis procedure may be required due to special field 
requirements 
• Any changes or modifications in the defective part analysis process must be agreed between the supplier and 
QEC in advance. In addition to changes in the process and data collection, this also includes any changes in 
test software and test equipment in particular 
• An expansion in the defective part analysis for testing known defect patterns must be implemented 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 9 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 9/22 
 
• Particular care must be taken for ensuring that the defective part analysis starts with SOP (Start of Production) 
as early as the product development process 
4.5 
Process stability 
In general, the quality analysis process must be clearly documented with the following: 
• Process description, 
• Procedural diagram, 
• Workplace instructions, 
• Evidence for inspection and test equipment capability, 
• Document that accompanies the test (checklists) 
To ensure lasting process stability throughout, automated functional test processes that ensure an automatic 
procedure must be implemented.  
4.6 
Physical damages 
A complete defective part analysis and reporting of the results--as generally described herein-- must be conducted, 
even for components with visible damage (e.g. scratches) that does not affect basic functionality.  
A component can only be rejected as having physical damage once it has been determined that the external damage 
effect is also the clear cause of the complaint. 
  
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 10 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 10/22 
 
5 Conducting the defective part analysis 
5.1 
Defective part analysis procedure 
 
Recording and registration
Supplier’s acceptance 
of incoming goods
Long-term test
Vehicle simulation test
Compliant-oriented 
defect reproduction 
test
Cause analysis
Reclaimed part provided to QEC
Environmental test
1
2
3
5
7
8
17
6
Standard test results
Failure 
occurred
No failure
Standard test
Documentation of 
Standard test results
9
Marking parts n.i.O.
Marking parts NTF
15
14
11
10
t0
t2
Quality analysis report
Complete statement to QEC
13
16
4
Failure
Failure
Failure
Failure
No failure
No failure
No failure
No failure occurred
Standard test results 
to QEC (optional)
Analysis
Logistic
Data transfer
Reporting
LEGEND
tx
Time limit (see Table 1)
t1
Definition of corrective actions
12
Report of Standard test
Recording and registration
Supplier’s acceptance 
of incoming goods
Long-term test
Vehicle simulation test
Compliant-oriented 
defect reproduction 
test
Cause analysis
Reclaimed part provided to QEC
Environmental test
1
2
3
5
7
8
17
6
Standard test results
Failure 
occurred
No failure
Standard test
Documentation of 
Standard test results
9
Marking parts n.i.O.
Marking parts NTF
15
14
11
10
t0
t2
Quality analysis report
Complete statement to QEC
13
16
4
Failure
Failure
Failure
Failure
No failure
No failure
No failure
No failure occurred
Standard test results 
to QEC (optional)
Analysis
Logistic
Data transfer
Reporting
LEGEND
tx
Time limit (see Table 1)
Analysis
Logistic
Data transfer
Reporting
LEGEND
tx
Time limit (see Table 1)
t1
Definition of corrective actions
12
Report of Standard test
 
Illustration 1: Defective part analysis procedure 
 
Note: 
The tester can change the sequence of the expanded tests (defect reproduction test, environmental test, vehicle-
simulation test and long-term test) if that would accelerate reproducing the defect and/or determining the cause of 
the defect. 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 11 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 11/22 
 
5.2 
Description of the test procedure 
The individual procedural steps for the supplier’s quality analysis process are described in the following.  
The numbers in parentheses after the section headings correspond to the respective number of the process step in 
Illustration 2. 
5.2.1 
Supplier's acceptance of incoming goods (1) 
The QEC will provide the supplier with the defective part.  
A defective part examination outside the scope of the warranty is requested by the QEC in the form of a special 
examination. 
5.2.2 
Recording and registration (2) 
Clear recording of the system, unit, and test-specific data and procedures is required. This enables identification and 
reproducibility of the quality analysis results and the history starting with the time of failure up to the conclusion of 
reporting the results.  
The time the goods are received must be documented with a delivery note or upon acceptance in the QEC. The 
delivery must be checked for completeness, and this must be documented.  
5.2.3 
Standard test (3) 
The standard test must be conducted completely for every reclaimed vehicle part, and this must be documented. 
Thus, this test cannot be interrupted even if the cause of the defect has already been determined unless the test is no 
longer possible due to the defect. 
The standard test fulfills the following purpose: 
• Documentation of the general condition of the defective part upon receipt 
• Documentation of component-specific index data (e.g. error memory contents) 
• Standard test for determining defects without reference to the complaint 
The scope of the standard test must correspond to a complete quality check of production at a minimum and includes 
the following types of tests: 
1. Visual check for (to the extent applicable to the component)... 
• ... Damages 
• ... Dirt (control elements) 
• ... Corrosion (connectors) 
• ... Traces of moisture 
• ... Completeness (control parts, fuses, etc.). 
Notes: 
Limit values and patterns must be specified in the work instructions. 
A visual check of the display elements must be conducted (e.g. with displays via test dot matrix). 
2. Maintain general index data such as: 
• Dimensional accuracy 
• Luster 
• Color comparison 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 12 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 12/22 
 
• Sleep current and active current consumption 
• Error memory contents 
• DC coding 
3. Function test: 
• All functions and control elements 
Requirements: 
All known technical requirements must be noted (current technology). 
5.2.4 
Standard test results (9)+(15) 
The implementation and results of the standard test must be documented completely. 
The standard test results contain the following items at a minimum: 
• General report information (basic data) as listed in Chapter 5.3  
• Recording of the test results as listed in Chapter 5.3  
5.2.5 
Standard test results (4) 
In the event that the defect can be shown using the standard test, the defective part must be marked as "NG" (10), 
and the cause of the defect must be analyzed extensively (11).  
5.2.6 
Complaint-oriented defect reproduction test (5) 
The defect reproduction test must be conducted if the preceding standard test did not result in reproducing the 
defect or in obtaining the (exact) cause of the defect. 
The complaint-oriented defect reproduction test fulfills the following purpose: 
• Targeted defect reproduction as quickly as possible 
The type and scope of the defect reproduction test is initially determined by the supplier specifically for the 
component and the defect. 
5.2.7 
Environmental test (6) 
The environmental test must be conducted if the preceding test did not result in reproducing the defect or in 
obtaining the (exact) cause of the defect. 
The environmental test fulfills the following purpose: 
• Reproduction of the defect behavior under realistic environmental conditions (climate, mechanical stress, etc.) 
The scope of the environmental test is component- but not defect-specific, and the test is conducted according to the 
component specification, e.g.: 
1) Temperature test 
2) Moisture test 
3) Mechanical test 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 13 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 13/22 
 
5.2.8 
Vehicle simulation test (7) 
The vehicle simulation test must be conducted if the preceding test did not result in reproducing the defect or in 
obtaining the (exact) cause of the defect. 
The vehicle simulation test fulfills the following purpose: 
• Reproduction of the defect behavior with realistic vehicle simulation with respect to the function to be tested 
(e.g. re-creation of the design environment; mechanical stress; CAN, control unit and load simulations; 
fluctuations in operational voltage) 
• Re-creation of the original configuration in the vehicle 
The vehicle simulation test is initially specified by the supplier specifically for the component and the defect. 
With a batch of multiple defective parts of the same type (same item number or Q status) with the same complaint, 
the quantity of defective parts to be tested can be reduced, for economical and technical reasons, upon agreement 
with the QEC. 
5.2.9 
Long-term test (8) 
The long-term test must be conducted if the preceding test did not result in reproducing the defect or in obtaining the 
(exact) cause of the defect. 
The long-term test fulfills the following purpose: 
• Reproduction of sporadic defects (e.g. loose contact, “random events”) 
The long-term test is specific to the component and the defect. 
The long-term test is conducted with the test set-up of the vehicle simulation and the environmental test and is 
initially determined by the supplier specifically for the component and the defect. 
With a batch of multiple defective parts of the same type (same item number or Q status) with the same complaint, 
the quantity of defective parts to be tested can be reduced, for economical and technical reasons, upon agreement 
with the QEC. 
5.2.10 
Marking parts (10+14) 
As soon as the defect can be found, a clear "NG” marking must be placed on the defective part so it is recognizable. 
A “NTF” marking can only be placed on the returned part once all tests have been conducted, and the supplier has 
determined that the tested part is free of defects.  
5.2.11 
Cause analysis (11) 
If the reclaimed vehicle part was found to be NG, the supplier must conduct a detailed defect-cause analysis. 
In this case, the following examinations must be conducted: 
• Defect pattern analysis (description of the symptoms) 
• Defect-cause analysis 
• Defect-consequences analysis (effects of the defect on other components, production, etc.) 
• Defect-root analysis (determining the defects and/or weaknesses of the product that can lead to this or other 
defects if applicable) 
• Risk analysis 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 14 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 14/22 
 
5.2.12 
Definition of corrective actions (12) 
After the defect-cause analysis has been conducted, the supplier must implement steps for remedying the defect 
within the scope of the corrective action process; this must meet the following criteria: 
• Assurance of immediate measures and series-production measures 
• Systematic procedure with definition and implementation of the corrective action 
• Indication of implementation deadlines for the corrective action 
• Clear specification of persons responsible for the corrective action 
• Proof of the effectiveness of the corrective action 
• Closed feedback loop (lessons learned) 
5.2.13 
Quality analysis report (13) 
After all necessary (follow-up) tests and defect analyses are concluded, the quality analysis report with the final 
statement must be created for each reclaimed vehicle part. This report contains the expanded test report and, in the 
event of a defect (NG), the 8D report as well. 
The quality analysis report must at least contain the following items for each reclaimed vehicle part (see Chapter 5.3): 
• General report information (basic information) 
• Test protocol for the standard test 
• Test protocol of the expanded tests if conducted 
• Results: 
- 
NG (new defect or known defect) 
- 
NTF (no defect) 
- 
Defect acknowledged or rejected 
- 
Customer caused defect (e.g. handling or configuration errors, external components defective) 
- 
Defect code (for statistical evaluation) 
• Clustering of the defect at the component level 
• Final statement: Summary of the findings results with reasoning if necessary 
• In the event of a defect (NG) 
o 
8D report in the attachment + reference to the 8D report (8D report no., date) 
o 
For previously known defects reference to the existing 8D report is sufficient 
• In the event of NTF 
- 
Best guess as to the cause of the defect (e.g. external components1, handling errors, etc.)     
- 
Return date for the “NTF” device 
                                                           
1 The supplier is required to provide DC technical help when analyzing the defect behavior even if the defect is caused by external 
events (customer caused fault). 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 15 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 15/22 
 
5.2.14 
Complete statement provided to QEC (16) 
The complete supplier’s statement contains the quality analysis reports for each reclaimed vehicle part in the test 
report.  
The complete statement must be sent to QEC after the completion of all necessary (follow-up) tests, defect analyses 
and documentation, within the prescribed time limit (t1) (see Chap. 0).  
5.2.15 
Reclaimed part provided to QEC (17) 
If the reclaimed vehicle part was found to be “NTF” (no defect) or "NG” but the supplier is not at fault, then the part 
must be returned to QEC within the t2 time limit (see Chap. 0).  
The supplier or a named provider is responsible for returning the part to the QEC.  
5.2.16 
Time limits 
******** Overview 
The following time limits (Table 1) are applicable for the defective part analysis (times are in business days).   
The deadline for providing the complete statement is specified by the QEC in the test report.  
 
Time 
Time frame 
Action 
to 
Start point 
Supplier’s receipt of goods 
t1 
Deadline as required in the test report Complete test report statement available 
t2 
t1  + 5 days 
Return of rejected parts to the QEC 
t3 
20 days after receipt of defective part 
Rejection based on terms of business when no statement at 
hand 
Table 1: Time limits 
******** Delay  
If it becomes necessary to extend the time limits due to long-term tests for reproducing defect patterns or because of 
a request by sub-contractors to analyze the defect behavior, this can be requested with the QEC on a case-by-case 
basis.  
 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 16 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 16/22 
 
5.3 
Documentation and reporting 
5.3.1 
Document and report types 
The supplier must create the following types of reports for each defective part: 
• Standard test report consisting of: 
o Test protocol for the standard test 
• Quality analysis report consisting of: 
o Test protocol for the expanded test scope including the defect reproduction test 
o Final statement for each defective part (findings, reasoning) 
o 8D report (for NG in the event of a defect) 
5.3.2 
General minimum requirements 
The documentation of results must satisfy the following minimum requirements:  
• Component-specific  
• Based on the series number 
• Results-oriented (complete text) 
• Statistically analyzable   
5.3.3 
Analyzability  
The documentation must enable analysis of... 
• ...a clustering of individual defects for defect emphases,  
• ...projecting a defect pattern or cause of the defect onto customer complaints 
• ...and an individual defective part. 
5.3.4 
Format 
Every report and every statement must be created in Excel format until the QEC supplier portal is implemented.  Once 
the supplier portal is implemented (planned for the end of May 2005), the supplier will be required to enter the results 
into the supplier portal. The interface described in the appendix can be used for this. 
5.3.5 
Basic information 
The report and the statement must contain the following basic data at a minimum:   
• Report information 
- 
Type of report (standard test report, expanded test report, final statement, 8D report) 
- 
Test report no. 
- 
Test report version 
- 
Contact person (name, first name, company, scope of responsibility, department, address, phone 
no., e-mail address) 
- 
Statement deadline 
• Defective part information 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 17 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 17/22 
 
- 
DaimlerChrysler reference number 
- 
Name of the defective part 
- 
DaimlerChrysler item number for the defective part 
- 
Supplier’s item number for the defective part 
- 
Q status (if available) 
- 
Software version (if available) 
- 
Hardware version (if available) 
- 
DaimlerChrysler mechanic's findings and/or basis of the complaint 
- 
Refurbished unit yes/no 
- 
Repair date (if available) 
- 
Date of manufacture (production date and/or end-of-line test) 
- 
Series number of the defective part 
• Vehicle information 
- 
Vehicle identification number (FIN) 
- 
Engine number 
- 
Initial registration 
- 
Mileage 
5.3.6 
Test protocol 
Every test protocol must contain the following items at a minimum: 
• Reference to the defective part 
• List of the tests conducted 
• Date tests conducted 
• Test set-up and environment (including SW and H W versions for the test equipment during the test) 
• Test parameters 
• Test results 
• Supplementary test protocols e.g. from independent testing institutes 
5.3.7 
Test report statement 
The statement for the test report contains complete documentation for each reclaimed part in the test report as 
described in Chapter 5.3. 
5.3.8 
General reporting requirements 
The supplier must provide the following general information: 
• Parts histories including the development phase 
• Changes in the defective part analysis process 
• Changes in the production process 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 18 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 18/22 
 
• Risk analysis with defect rate estimate for known production defects (timeline and quantity) 
5.3.9 
Data retention 
The following documents and data must be retained and kept available for at least five years: 
• Standard test report 
• Quality analysis report 
• 8D report 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 19 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 19/22 
 
6 Appendix 
6.1 
Index of illustrations 
Illustration 1: Defective part analysis procedure...........................................................................................................10 
 
6.2 
Index of tables 
Table 1: Time limits.....................................................................................................................................................15 
Table 2: Data record construction for the test report header ......................................................................................21 
Table 3: Data record construction for the test report items ........................................................................................22 
6.3 
Index of abbreviations  
 
DC 
DaimlerChrysler AG 
HW 
Hardware 
Chap. 
Chapter 
MCG/Q 
Mercedes-Benz Car Group, Quality Management 
NG 
No Good (defective) 
NTF 
No trouble found 
QEC 
Quality Engineering Center (DaimlerChrysler, Dept.: MCG/QPE) 
SOP 
Start of production  
SW 
Software 
VDA 
Association of German Automobile Manufacturers  
 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 20 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 20/22 
 
6.4 
Specification interface for data export/import 
This specification contains the definition and description of the interface file construction for exporting and importing 
test report data in the QEC supplier portal.  
The interface file is a text file in CSV format. 
The exported data can be processed by the supplier outside the QEC supplier portal system. Plausibility tests take 
place during the import of changed data. The tests include field lengths, field formats, validity ranges and mandatory 
entries.  
6.4.1 
Table set-up 
Data is exported/imported in CSV format. The interface file contains the information for one test report. This file is 
composed of: 
• The test report header data represented by a data line in the interface file, 
• The item data (information on the individual damaged parts) contained in the test report; 
Each damaged part is represented by a separate data line in the interface file.  
To provide a better overview, both types of data records are given a header record in the interface file. This header 
record is where the technical reference to the contents of the data records is generated.  
This results in the following interface file construction: 
• Block for the test report header 
o 
Interface file line 1: Test report header 
o 
Interface file line 2: Test report header data 
• Block for the test report items (damaged parts) 
o 
Interface file line 3: Damaged part header 
o 
Interface file line for line 4 to line n: Damaged part data 
The files for importing and exporting have the same record construction. The description of the record construction 
characterizes what fields are tested during import and the fields from which changes can be imported and put into 
the QEC supplier portal. 
6.4.2 
Rules for file construction 
The following basic rules apply to file construction: 
• The separator for the data in one data record is the semicolon (character 59). 
• The end of the line is coded in Windows format, i.e. carriage return + line feed (characters 10 + 13) 
• Line breaks within one field are coded as a line feed (character 10) 
• The character coding is ISO-8859-1 (Latin 1) 
• The length of the individual fields is variable 
• Fields that contain line breaks, a semicolon, or quote marks (character 34) are placed in quote marks. Quote 
marks in the text are represented by two quote marks. 
• The first item of each data line contains a two-digit record identifier.  
Columns the supplier can edit are characterized by an asterisk in the column header.  The asterisk is added at the end 
of the particular text.  
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 21 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 21/22 
 
6.4.3 
Data formats 
There are 4 types of data formats: 
Integer:  
Numeric values without decimal points 
String:  
Alphanumeric values 
Date: 
 
Format according to ISO-8601: YYYY-MM-DD 
Boolean:  
Numeric value with length of 1, only 0 (=false) and 1 (=true) permitted as values 
  
6.4.4 
Header construction for the test report header 
The header for the test report contains the contents of the field description column from Table 2: Data record 
construction for the test report header. The first attribute deviates from this however; it doesn't contain "record 
identifier" but instead has "HD" as the record identifier for the header record. 
6.4.5 
Data record construction for the test report header 
  
  
  
  
  
  
During import 
# 
Field description 
Attribute 
Type 
Maximum 
length 
Tested 
Modifiable 
1 Record identifier  
„PB“ 
String 
2 
X 
  
2 Test report identifier PBK_ID 
Integer 
 
X 
 
3 Test report number 
PBK_Prüfberichtsnummer 
Integer 
12 
X 
  
4 Test report version 
PBK_Prüfberichtsversion 
Integer  
2 
  
  
5 Contact person 
PBK_FachlicherAnsprechpartner 
String 
30 
  
  
6 Text 
PBK_Text 
String 
2000 
  
  
7 Release date 
PBK_Freigabedatum 
Date 
- 
  
  
8 Goods Receipt Date 
PBK_Datum_Wareneingang_Lieferant 
Date 
- 
  
  
9 Statement up to 
PBK_Stellungsnahmedatum 
Date 
- 
  
  
10 Closed on 
PBK_Datum_Abgeschlossen_Lieferant 
Date 
- 
  
  
11 Closed 
PBK_Kennz_Abgeschlossen_Lieferant 
Boolean 
1 
  
  
Table 2: Data record construction for the test report header 
6.4.6 
Header construction for the test report items 
The header for the test report items contains the contents of the field description column from Table 3: Data record 
construction for the test report items. The first attribute deviates from this however; it doesn't contain "record 
identifier" but instead has "HD" as the record identifier for the header record. 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)


### 第 22 页
 
Guideline for Defective Parts Analysis 
(Quality Analysis Specification) 
Mercedes Car Group 
Quality Engineering Center 
 
Version date 01/2005 
 © DaimlerChrysler AG 
Page 22/22 
 
6.4.7 
Data record construction for the test report items (damaged part) 
  
 
  
  
  
  
During import 
# 
Field description 
Attribute 
Type 
Maximum 
length 
Tested 
Modifiable 
1 Record identifier  
„ST“ 
String 
2 
X 
  
2 Reference number 
Referenznummer 
String 
12 
X 
  
3 Status 
Status 
Integer 
 
 
 
4 Part’s item number 
SachnummerTeil 
String 
16 
  
X 
5 Replacement part 
SachnummerARS 
String 
16 
  
  
6 Mechanic’s findings 
Monteurbefund 
String 
300 
  
  
7 Feedback to the supplier 
Rueckmeldung_an_Lieferant 
String 
300 
  
  
8 FIN 
FIN 
String 
14 
  
  
9 VIN 
VIN 
String 
17 
  
  
10 Engine number 
Motornummer 
String 
14 
  
  
11 Registration date 
Erstzulassung 
Date 
- 
  
  
12 Repair date 
Reparaturdatum 
Date 
- 
  
  
13 Mileage [km] 
Laufleistung 
String 
7 
  
  
14 Q status 
Q-Stand 
Integer 
2 
  
X 
15 Date of manufacture 
Lieferant_Herstelldatum 
Date 
- 
  
X 
16 Series number 
Lieferant_Seriennummer 
String 
20 
  
X 
17 Supplier’s item number 
Lieferant_Sachnummer 
String 
20 
  
X 
18 Software version 
Lieferant_SW 
String 
8 
  
X 
19 Hardware version 
Lieferant_HW 
String 
8 
  
X 
20 Analysis 
Lieferant_Analyse 
String 
300 
  
X 
21 Cause 
Lieferant_Ursache 
String 
300 
  
X 
22 Immediate measures 
Lieferant_Massnahme  
String 
300 
  
X 
23 File name_8D  
Lieferant_Dateiname_8D  
String 
50 
  
X 
24 Statement 
Lieferant_anerkannt 
Integer 
1 
  
X 
25 DC statement 
Lieferant_anerkannt_Aenderung
DC 
Integer 
1 
  
  
26 Part returned to DCAG 
Lieferant_Teil_zurueck 
Boolean 
1 
  
X 
27 Field 1 
Lieferant_Feld1 
String 
50 
  
X 
28 Field 2 
Lieferant_Feld2 
String 
50 
  
X 
29 Field 3 
Lieferant_Feld3 
String 
50 
  
X 
30 Field 4 
Lieferant_Feld4 
String 
50 
  
X 
31 Field 5 
Lieferant_Feld5 
String 
50 
  
X 
Note:  
#23: Radio (Value 0 – ntf, 1- customer at fault, 2 - acknowledged) 
 
 
#24: Radio (Value 0 - ntf, 1- customer at fault, 2 - acknowledged, 9 – delayed)  
Table 3: Data record construction for the test report items 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-06)

