# MSS_20206_EN_2018-08_FlexRay网络性能规范_214ff型号系列.pdf

## 文档信息
- 标题：FlexRay Networking Performance Specification for Model Series 214ff
- 作者：<PERSON><PERSON><PERSON>, Dietmar (059)
- 页数：72

## 文档内容
### 第 1 页
 
 
Mercedes-Benz 
MSS 20206, V2.0 
Company Standard 
Date published: 2018-08 
 
Total no. of pages (incl. Annex): 72  
 
Person in charge: <PERSON><PERSON>stowski 
 
Email: <EMAIL> 
 
Plant: 059, Dept.: RD/UPT 
  
  
 
 
 
 
 
Copyright Daimler AG 2018 
FlexRay Networking Performance Specification 
for Model Series 214ff 
 
 
 
 
Foreword 
 
 
This document defines the FlexRay networking performance specification for vehicle multiplexing 
communication systems and the electronic control modules using FlexRay on certain Mercedes-
Benz vehicle model series. 
 
 
 
 
 
 
Application note:  
Application of the present version of this Standard is binding for new vehicle projects or components 
of this scope, for which no concept/basic specifications or component requirement specifications 
have been approved yet at the date of issue of this version.  
The respective contract documents regulate the mandatory application of the present version of this 
Standard by the supplier. 
 
 
 
Changes 
 
 
In comparison with previous versions, the following major changes have been made: 
 
Refer to Annex B - revision history 
 
 
Uncontrolled copy when printed
RD/UBF: <PERSON>, 2018-11-23


### 第 2 页
MSS 20206, V2.0, 2018-08, page 2 
Copyright Daimler AG 2018 
Contents 
 
1 
Scope ............................................................................................................................................. 4 
1.1 
Deviations ................................................................................................................................... 4 
2 
Normative References.................................................................................................................... 5 
3 
Abbreviations, Acronyms, Definitions & Symbols .......................................................................... 6 
4 
General design requirements ......................................................................................................... 7 
4.1 
Voltage ranges ............................................................................................................................ 7 
5 
Network Communication Description ............................................................................................. 8 
6 
Communication behavior ............................................................................................................... 9 
6.1 
AUTOSAR FlexRay NM Parametrization ................................................................................... 9 
6.2 
Frame transmission mode ........................................................................................................ 10 
6.3 
Cycle time tolerances ............................................................................................................... 10 
6.4 
Cycle Multiplexing ..................................................................................................................... 11 
6.5 
Receiving Behavior ................................................................................................................... 12 
6.6 
Wake up during operation ........................................................................................................ 12 
6.7 
Influence of PNCs on communication behavior ....................................................................... 12 
6.8 
Uninterrupted Communication .................................................................................................. 12 
6.9 
Reset Operation ........................................................................................................................ 12 
6.10 
Short circuit of bus lines ........................................................................................................ 13 
6.11 
Start Delay Time ................................................................................................................... 13 
6.12 
PDU-Header on FlexRay ...................................................................................................... 13 
6.13 
Wake-up ................................................................................................................................ 13 
7 
Network startup and power down ................................................................................................. 14 
7.1 
Network wake up / startup ........................................................................................................ 14 
7.1.1 
Network wake up for ECUs with permanent power supply ........................................... 14 
7.1.2 
Passive wake up for ECUs with permanent power supply ............................................ 15 
7.1.3 
Network startup for ECUs with switched power supply ................................................. 16 
7.1.4 
Network startup timing ................................................................................................... 16 
7.2 
Network sleep / power down .................................................................................................... 19 
7.2.1 
Network sleep for ECUs with permanent power supply ................................................ 19 
7.2.2 
Network power down procedure for ECUs with switched power supply ....................... 19 
7.3 
Network Startup and Error handling ......................................................................................... 20 
7.3.1 
Wake up ......................................................................................................................... 20 
7.3.2 
Startup ........................................................................................................................... 21 
7.3.3 
Consecutive Startup ...................................................................................................... 21 
7.3.4 
Halt ................................................................................................................................. 23 
7.3.5 
Limp Home ..................................................................................................................... 24 
7.3.6 
Parametrization .............................................................................................................. 25 
8 
Testing .......................................................................................................................................... 26 
8.1 
FlexRay Node Information ........................................................................................................ 26 
8.2 
FlexRay Configuration Information ........................................................................................... 26 
9 
FlexRay Parameter Set ................................................................................................................ 28 
9.1 
FlexRay High-Level Parameters ............................................................................................... 28 
9.2 
FlexRay Low-Level Parameters ............................................................................................... 29 
9.2.1 
Cycle Parameters .......................................................................................................... 29 
9.2.2 
Wake up and Startup Parameters ................................................................................. 30 
9.2.3 
Clock Correction Parameters ......................................................................................... 31 
9.2.4 
pLatestTx Parameter ..................................................................................................... 32 
9.3 
Dynamic frame duration ........................................................................................................... 34 
9.4 
Additional Constraints ............................................................................................................... 35 
10 
ECU Physical Layer Circuitry ................................................................................................... 36 
10.1 
Supported Baudrates ............................................................................................................ 36 
10.2 
Oscillator Requirements ........................................................................................................ 36 
10.3 
FlexRay Transceivers ........................................................................................................... 37 
10.4 
Common Mode Chokes ........................................................................................................ 38 
10.5 
Bus Termination .................................................................................................................... 39 
10.5.1 
General hints on Bus Terminations ............................................................................... 39 
10.5.2 
Termination of End-nodes ............................................................................................. 39 
10.5.3 
Termination of In-between Nodes.................................................................................. 40 
10.5.4 
Termination of Active Star ECUs ................................................................................... 41 
10.5.5 
Universal Termination for optional end or in-between nodes ........................................ 41 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 3 页
MSS 20206, V2.0, 2018-08, page 3 
Copyright Daimler AG 2018 
10.6 
Circuit Diagrams .................................................................................................................... 43 
10.6.1 
Basic circuit diagram ...................................................................................................... 43 
10.6.2 
Exemplary circuit diagram for ECUs with permanent power supply using 16 pin 
transceivers .................................................................................................................................. 45 
10.6.3 
Exemplary circuit diagram for ECUs with switched power supply using 16 pin 
transceivers .................................................................................................................................. 47 
10.6.4 
Exemplary circuit diagram for ECUs using 14 pin transceivers ..................................... 49 
10.6.5 
Exemplary circuit diagram for active star ECUs using integrated active star devices ... 50 
10.7 
General ECU layout requirements ........................................................................................ 54 
10.8 
Interface between Communication Controller and Transceiver ............................................ 56 
11 
ECU Classification .................................................................................................................... 58 
12 
Wiring and Connectors ............................................................................................................. 60 
12.1 
FlexRay Cables ..................................................................................................................... 60 
12.1.1 
Cable Requirements ...................................................................................................... 60 
12.1.2 
Approved Cables ........................................................................................................... 60 
12.2 
Cable and Connector Assembly ........................................................................................... 60 
12.3 
Connectors for FlexRay ........................................................................................................ 62 
12.3.1 
Connector Requirements and Approved Connectors .................................................... 62 
12.3.2 
Pin Allocation within the Connector ............................................................................... 62 
12.3.3 
Connecting Circuitry for Cable Shields .......................................................................... 65 
12.3.4 
Shielded FlexRay interconnection in EMC sensitive areas ........................................... 66 
12.4 
Topology Layout Design Rules ............................................................................................. 68 
13 
Annex A (informative) References ............................................................................................ 70 
13.1 
Company standards .............................................................................................................. 70 
13.2 
International standards ......................................................................................................... 71 
14 
Annex B (informative) Revision History .................................................................................... 72 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 4 页
MSS 20206, V2.0, 2018-08, page 4 
Copyright Daimler AG 2018 
1 
Scope 
MSS 20206-37: 
This network specification document - together with [MSS 20200] - contains 
implementation requirements for ECUs connected to the FlexRay network in 
Mercedes-Benz Cars vehicles. This specification is valid for single channel networks 
only. The Vehicle Networking Group of Mercedes-Benz Cars will determine the 
respective vehicle lines that this specification applies. This document is subject to 
changes. 
MSS 20206-38: 
This specification is intended for Daimler internal use and Daimler supplier internal 
use only. 
MSS 20206-39: 
Refer all questions to the Vehicle Networking Group. 
1.1 
Deviations 
MSS 20206-41: 
In the current document, the following defined terminology prescription applies.  
The usage of 
MSS 20206-42: 
 
- “Shall” expresses in the text a mandatory requirement. 
MSS 20206-43: 
 
- “Should” expresses in the text an optional requirement 
MSS 20206-44: 
 
- “Can” expresses in the text a permitted practice or method. 
MSS 20206-45: 
Text segments written in italic letters have information character. 
MSS 20206-46: 
Deviations from the requirements contained in this standard are only allowed if 
agreed to explicitly and documented between the supplier, the Vehicle Networking 
Group and the appropriate responsible person for the relevant component within 
Mercedes-Benz. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 5 页
MSS 20206, V2.0, 2018-08, page 5 
Copyright Daimler AG 2018 
2 
Normative References 
MSS 20206-48: 
The following referenced documents are indispensable for the application of this 
document. For dated references, only the edition cited applies. For undated 
references, the latest edition of the referenced document (including any 
amendments) applies. 
 
Document Number 
Document Title 
[DSUDS] 
Daimler Diagnostic Specification - Supplement to ISO 14229 
DDS S-ISO14229, refer to [CANDELA_TEMPLATE] 
[CANDELA_TEMPLATE] 
Candela Template contains definition of diagnostic services 
Diagnose Portal, Daimler AG 
[ISO 17458-4] 
FlexRay communications system - Part 4: Electrical physical layer specification, see 
Annex A of this document 
[ISO 17458-5] 
FlexRay communications system - Part 5: Electrical physical layer conformance test 
specification, see Annex A of this document 
[MBN 10567] 
Electric and Electronic Components in Motor Vehicles - 12 V On-Board Electrical 
System - Requirements and Tests 
[MSS 20200] 
General Networking Performance Specification 
[VDA320] 
Electric and Electronic Components in Motor Vehicles - 48 V On-Board Power Sup-
ply, see Annex A of this document 
[AUTOSAR_INT] 
Integration Requirements AUTOSAR 4.x 
Diagnose Portal, Daimler AG 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 6 页
MSS 20206, V2.0, 2018-08, page 6 
Copyright Daimler AG 2018 
3 
Abbreviations, Acronyms, Definitions & Symbols 
 
Term 
Definition 
AUTOSAR 
Automotive Open System Architecture (www.autosar.org) 
µT 
Microtick 
BD 
Bus Driver 
BM 
Bus Minus 
BP 
Bus Plus 
CAS 
Collision Avoidance Symbol 
CC 
Communication Controller 
CMC 
Common Mode Choke 
ECU 
Electronic Control Unit 
EMC 
Electromagnetic Compatibility = immunity and emission 
EPL 
FlexRay Electrical Physical Layer Specification 
ESD 
Electro Static Discharge 
FR 
FlexRay 
GND 
Ground 
IOD 
Ignition Off Draw 
MT 
Macrotick 
NCD 
Network Communication Description (formerly "VMM - Vehicle Message Matrix") 
NM 
Network Management 
PCB 
Printed Circuit Board 
PDU 
Protocol Data Unit 
permanent power 
supply 
permanent battery fed, e.g. clamp 30 or 30T 
PNC 
Partial Network Cluster 
POC 
Protocol Operation Control 
sheathed cable 
German: “gemantelte Leitung” 
shielded cable 
German: “geschirmte Leitung” 
switched power 
supply 
supply of the ECU is switched, e.g. ignition fed, clamp 15 or 87 
TP 
Test Plane 
TP1_BD 
Test Plane at the transmitting Transceiver TxD pin 
TP4_CC 
Test Plane at the receiving FlexRay communication controller RxD pin 
TSS 
Transmission Start Sequence 
twisted pair cable 
German: “verdrillte Zweidrahtleitung” 
Vehicle Networ-
king Group 
Subdivision of Mercedes-Benz Cars responsible for in-vehicle-networking 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 7 页
MSS 20206, V2.0, 2018-08, page 7 
Copyright Daimler AG 2018 
4 
General design requirements 
MSS 20206-51: 
For safety requirements, homologation (in particular, exhaust emissions) and quality, 
the existing statutory requirements and laws shall be complied with. In addition, the 
relevant requirements of the Daimler Group apply. 
MSS 20206-52: 
All materials, procedures, processes, components, and systems shall conform to the 
current regulatory (governmental) requirements regarding regulated substances and 
recyclability. 
MSS 20206-53: 
The standards and specifications listed in chapter 2 "Normative References" and in 
"Annex A References" shall be followed when designing the FlexRay ECUs and the 
FlexRay network systems. 
MSS 20206-54: 
All tolerance values mentioned in this specification shall include initial tolerance, 
aging and temperature effects according to the application profile (service life, 
temperature profile) as specified in the “Component Requirement Specifications” of 
the affected ECU. 
MSS 20206-55: 
Several timings in this specification depend on the length of the FlexRay cycle TCycle. 
For all these timings the calculation formula is given. In this document the resulting 
timings are specified for TCycle = 5 ms. 
4.1 
Voltage ranges 
MSS 20206-57: 
All ECUs shall minimally guarantee proper FlexRay communication with a supply 
voltage required  for coding "a" components, refer to [MBN 10567]. 
MSS 20206-58: 
If FlexRay communication has to be continued below or above these limits because 
of functional requirements it shall run without errors. 
MSS 20206-59: 
In order to ensure proper FlexRay communication of critical vehicle components at 
specified under and over voltages, it is required that none of the FlexRay ECUs shall 
interfere with or disrupt the network traffic even if the battery voltage is outside its 
operating range. 
MSS 20206-60: 
This may require that bus drivers and other electrical loads are deactivated 
temporarily or completely at certain voltage conditions. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 8 页
MSS 20206, V2.0, 2018-08, page 8 
Copyright Daimler AG 2018 
5 
Network Communication Description 
MSS 20206-62: 
Refer to [MSS 20200] for general network communication description informations 
and requirements. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 9 页
MSS 20206, V2.0, 2018-08, page 9 
Copyright Daimler AG 2018 
6 
Communication behavior 
6.1 
AUTOSAR FlexRay NM Parametrization 
MSS 20206-65: 
Refer to [MSS 20200] for general Network Management informations and 
requirements. 
MSS 20206-66: 
The AUTOSAR Network Management schedule and parametrization is based on 
following constraints: 
MSS 20206-67: 
- Maximum number of nodes with NM:  
 
24 NM-nodes 
MSS 20206-68: 
- NM repetition cycle:  
 
 
 
2   NM voting cycles 
MSS 20206-69: 
- Maximum length of NM repetition cycle: 
 
TRepetitionCycle ≤ TFRInitMax 
 
MSS 20206-70: 
Rationale: Within the last voting cycle before cluster shutdown it is possible that an 
ECU cannot indicate the necessity of bus communication. In this case the ECU has 
to wake up the cluster again after the shutdown. The overall timing of this procedure 
shall correlate with the normal wake up timing. 
MSS 20206-71: 
This results in following calculations and schedule: 
MSS 20206-72: 
    TRepetitionCycle = FRNM_REPETITION_CYCLE ∙ TCycle = 8 cycles ∙ 5 ms = 40 ms 
MSS 20206-73: 
    NM-Nodes per slotMax = TRepetitionCycle / TCycle / NM repetition cycle 
    = 40ms / 5ms / 2 = 4 NM-Nodes/Slot 
MSS 20206-74: 
    Number of NM-Slots = 24 NM-Nodes / 4 NM-Nodes per Slot = 6 Slots 
MSS 20206-75: 
 
NM_ECU00
Cycle 
Counter
0
1
2
3
4
5
6
7
8
63
62
61
Cycle
Slot 1
Slot 2
Slot M+3
Slot M
NM_ECU01
NM_ECU02
NM_ECU03
NM_ECU00
NM_ECU01
NM_ECU02
NM_ECU03
NM_ECU01
NM_ECU02
NM_ECU03
Slot M+1
Slot M+4
Static Segment
Dynamic Segment
NM_ECU00
NM_ECU01
NM_ECU02
NM_ECU03
NM_ECU00
NM_ECU01
NM_ECU02
NM_ECU03
NM_ECU00
NM_ECU01
NM_ECU02
NM_ECU04
NM_ECU05
NM_ECU06
NM_ECU07
Slot M+2
NM_ECU05
NM_ECU06
NM_ECU07
Reserved for NM ECU 08-11
Reserved for NM ECU 12-15
9
10
11
12
13
14
15
16
17
18
NM_ECU04
NM_ECU05
NM_ECU06
NM_ECU07
NM_ECU04
NM_ECU05
NM_ECU06
NM_ECU07
NM_ECU04
NM_ECU05
NM_ECU06
NM_ECU07
NM_ECU04
NM_ECU05
NM_ECU06
Reserved for NM ECU 16-19
Reserved for NM ECU 20-23
Slot M+5
Slot M+6
Voting Cycle
Data Cycle
Data Cycle
Repetition 
Cycle
Voting Cycle
Figure F6.1a: NM schedule in the dynamic segment 
 
MSS 20206-481: 
The ECU Extract of an ECU connected to a FlexRay Network contains a receive NM 
frame which is scheduled with the NM Slot range depicted in figure F6.1a. In case 
this frame shall be transmitted (e.g. in a test environment) it is scheduled with the 
last Slot of the NM Slot range, Base Cycle 3, Repetition 4. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 10 页
MSS 20206, V2.0, 2018-08, page 10 
Copyright Daimler AG 2018 
MSS 20206-76: 
The parametrization and configuration requirements for the AUTOSAR Network 
Management in the following tables shall be used: 
MSS 20206-77: 
 
NM Parameters 
[AUTOSAR name] 
Description 
Value 
FRNM_VOTING_CYCLE 
Number of FlexRay cycles needed to transmit the NM vote of all 
ECUs 
4 Cycles 
FRNM_REPETITION_CYCLE 
Number of FlexRay cycles used to repeat the transmission of the NM 
vote of all ECUs in the FlexRay cluster 
8 Cycles 
FRNM_DATA_CYCLE 
Number of FlexRay cycles needed to transmit the NM Data of all 
ECUs in the FlexRay cluster 
4 Cycles 
FRNM_REPEAT_MESSAGE_TIME Time after “Repeat Message” state is left 
3000 ms 
FRNM_READY_SLEEP_TIME 
Time with no NM vote after which the transition from “Ready Sleep 
State” to “Bus Sleep Mode” is triggered 
2480 ms 
FRNM_REMOTE_SLEEP_IND_TIME Time after a node is notified when the node is the only one  in the 
network that requests bus communication 
40 ms 1 
1 Applicable for gateways 
 
Table T6.1a: AUTOSAR FlexRay Network Management parametrization 
requirements 
 
MSS 20206-78: 
Network specific changes may be defined in the NCD. The NCD values shall be 
applied. 
6.2 
Frame transmission mode 
MSS 20206-81: 
The frame transmission mode of the FlexRay communication controller shall be 
configured in the "single shot" mode to send updated frame buffers only once to 
ensure functionality of the update bits. From this it follows that the buffers shall be 
updated before the scheduled transmission. 
6.3 
Cycle time tolerances 
MSS 20206-83: 
In the static segment a null frame shall not be sent after finishing startup if updated 
content is available for the frame. Gateways may send null frames if no data from 
the source network is available for PDU routing. 
MSS 20206-84: 
The cycle time of frames in the dynamic segment shall be met. Gateways may omit 
the transmission in case of PDU routing if no data was received from the source 
network. 
MSS 20206-483: 
The average cycle time (tc and tc2) of all PDUs for both, a standalone ECU and 
ECUs in the network, shall be within ± 2% of the specified PDU cycle time in the 
NCD. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 11 页
MSS 20206, V2.0, 2018-08, page 11 
Copyright Daimler AG 2018 
MSS 20206-484: 
The absolute deviation of the cycle time shall not exceed the values stipulated in the 
following table: 
MSS 20206-485: 
 
Container PDU 
Tolerance 
First Contained 
PDU Triggers 
Cycletime > 10ms: 
[Cycletime - 10ms ; Cycletime + 10ms] 
Cycletime  10ms: 
 [Cycletime - 5ms ; Cycletime + 5ms] 
 
Table T6.3a: Absolute deviation of the cycle time 
 
6.4 
Cycle Multiplexing 
MSS 20206-86: 
By the use of cycle multiplexing frames can be transmitted in multiples of the 
communication cycle time. With in-cycle repetition frames can be transmitted in half 
of the cycle time. 
MSS 20206-87: 
 
Cycle repetition 
Effective frame rate 
In-cycle repetition 
approx. 2.5 ms 
1 
5 ms 
2 
10 ms 
4 
20 ms 
8 
40 ms 
16 
80 ms 
32 
160 ms 
64 
320 ms 
 
Table T6.4a: Effective frame rates for 5 ms cycle time 
 
MSS 20206-88: 
Some examples of cycle multiplexing can be found in the following figure: 
MSS 20206-89: 
 
N
I
T
Cycle 
Counter
0
1
2
3
4
5
6
7
8
63
62
61
Cycle
Slot 1
Slot 2
Slot M+1
Slot M
Slot 3
Cycle Repetition 2
Cycle Repetition 2
Cycle Repetition 4
Slot M+2
Base Cycle 0
Base Cycle 1
Base Cycle 2
In-Cycle Repetition
Cycle Repetition 1
Base Cycle 0
Static Segment
Dynamic Segment
Figure F6.4a: Examples of cycle multiplexing 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 12 页
MSS 20206, V2.0, 2018-08, page 12 
Copyright Daimler AG 2018 
6.5 
Receiving Behavior 
MSS 20206-91: 
Due to the use of PDU Header every ECU should be able to receive all slots which 
may contain a PDU Container, i.e. which are part of a PDU Container Rx Slot 
Range.  
6.6 
Wake up during operation 
MSS 20206-93: 
The dedicated data frames called WUDOP in the NCD emulate a wake up pattern to 
ensure wake up for the FlexRay bus drivers. 
MSS 20206-94: 
Each WUDOP frame shall consist of 36 bytes with following data pattern: 
 
     FFh, FFh, FFh, FFh, FFh, 00h, 00h, 00h, 00h, 00h, 
     FFh, FFh, FFh, FFh, FFh, 00h, 00h, 00h, 00h, 00h, 
     FFh, FFh, FFh, FFh, FFh, 00h, 00h, 00h, 00h, 00h, 
     FFh, FFh, FFh, FFh, FFh, FFh 
 
MSS 20206-95: 
All startup and sync nodes shall transmit a WUDOP frame cyclic. 
MSS 20206-96: 
These frames shall be scheduled at least every 16 cycles. 
MSS 20206-97: 
The NCD defines no receivers for this frame. 
6.7 
Influence of PNCs on communication behavior 
MSS 20206-99: 
All ECUs are part of at least one PNC. 
MSS 20206-100: 
One ECU can be a member of more than one PNC. 
MSS 20206-101: 
An ECU only sends PDUs if at least one of its associated PNCs is active. ECUs with 
no active PNC on this network will stay in a non-sending mode. 
MSS 20206-102: 
Thus it is possible to have network communication where only a subset of all ECUs 
will take part. 
MSS 20206-103: 
For further details see AUTOSAR ComM specification. 
6.8 
Uninterrupted Communication 
MSS 20206-105: 
Between the completion of the network startup phase and the beginning of prepare 
bus-sleep state an ECU shall not perform any activities e.g. reset, resulting in: 
- missing frames or null frames 
- reintegration into the FlexRay network 
- restarting Network Management 
MSS 20206-106: 
However, the above requirements do not apply to the ECU in the following events: 
- Activities which are explicitly triggered by the execution of diagnostic services, e.g. 
ECU reset 
- Detection of an error or fault which requires the above mentioned behavior. Refer 
to relevant ECU or system software requirement specification. 
6.9 
Reset Operation 
MSS 20206-108: 
ECUs shall not corrupt frames on the network while going into reset, during reset or 
coming out of reset. 
MSS 20206-109: 
ECUs shall be capable of coming out of reset and begin normal operation without 
requiring power or ignition reset. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 13 页
MSS 20206, V2.0, 2018-08, page 13 
Copyright Daimler AG 2018 
6.10 Short circuit of bus lines 
MSS 20206-111: 
An ECU shall be able to handle a short circuit on the buslines. After release of the 
short circuit ECUs shall be able to continue normal operation without requiring power 
or ignition reset. 
6.11 Start Delay Time 
MSS 20206-452: 
To avoid bursts, the NCD defines start delay times for all cyclic PDUs (cylic, cyclic 
on change and dual cycle). 
MSS 20206-453: 
ECUs shall follow the start delay times at every network wake up/startup. 
MSS 20206-454: 
If the defined start delays are contradictory to functional requirements the Vehicle 
Networking Group shall be contacted. 
MSS 20206-455: 
In case a new transmit PDU is added all start delay times for the ECU may be 
changed. 
6.12 PDU-Header on FlexRay 
MSS 20206-457: 
To support use of PDU-Header on FlexRay AUTOSAR ECU Configuration 
parameters of the IPDU-Multiplexer shall be configured according to the following 
table : 
MSS 20206-458: 
 
ECU Configuration Parameters 
[AUTOSAR name] 
Value 
IpduMContainerTxTriggerMode 1 
IPDUM_TRIGGERTRANSMIT 
1 All frames with PDU-Header which are sent on FlexRay 
 
Table T6.12a: ECU Configuration parameter requirements 
6.13 Wake-up 
MSS 20206-487: 
In a sleeping ECU (no communication interface active) a remote wake-up of a 
communication interface shall lead to a wake-up of all communication interfaces 
connected to that ECU. 
MSS 20206-488: 
A deviation from this requirement has to be agreed by the Vehicle Networking Group 
at project start and if changes occur. 
MSS 20206-489: 
This behavior is defined by AUTOSAR with parameter 
“ComMSynchronousWakeUp”, refer to “Integration Requirements AUTOSAR 4.x“ 
[AUTOSAR_INT]. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 14 页
MSS 20206, V2.0, 2018-08, page 14 
Copyright Daimler AG 2018 
7 
Network startup and power down 
7.1 
Network wake up / startup 
MSS 20206-114: 
An ECU with permanent power supply shall only start communication after a valid 
wake up event has been detected and verified. 
7.1.1 
Network wake up for ECUs with permanent power supply 
MSS 20206-116: 
For the realization of distributed functions in a vehicle it must be possible for an ECU 
to wake up the FlexRay network. 
MSS 20206-117: 
To keep the power consumption to a minimum, an ECU shall only wake up the 
FlexRay network if FR communication is necessary for the processing of 
functionality. Otherwise functions shall be handled locally without any FR 
communication. 
MSS 20206-118: 
Any local wake up event shall be checked for plausibility before the connected 
network is woken up. Local wake up events may be based on internal timers, 
interrupts, sensors, etc. The plausibility check shall be done independently of the 
type of connection, including sub buses like CAN and LIN. 
MSS 20206-119: 
The application shall transmit the wake up reason as required in [MSS 20200]. 
MSS 20206-120: 
In the following the network wake up is described. Refer to the chapter 7.1.4 
"Network startup timing" for details. 
MSS 20206-121: 
TWakeup 
Time from start of wake up listen to the end of transmission of the 
wake up pattern. 
MSS 20206-122: 
TWaitIntegration 
Time from start of integration listen to begin of cycle 0. 
MSS 20206-123: 
TIntegrationCS 
Time within a following coldstarter integrates into a FlexRay network 
and changes to POC:normal active. 
MSS 20206-124: 
TIntegration 
Time within a non coldstarter integrates into a FlexRay network and 
changes to POC:normal active. 
MSS 20206-125: 
TNetworkStartup 
Time within the whole network startup is finished and all ECUs have 
completed one cycle with POC:normal active. From the point of view of the wake up 
ECU TWakeup has to be added to TNetworkStartup. 
MSS 20206-126: 
TSignalValue 
Plausible signal values shall be transmitted as soon as possible, 
typically prior to the expiration of TSignalValue. For details see ECU specific software 
requirements. TSignalValue starts with the expiration of TFRInit. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 15 页
MSS 20206, V2.0, 2018-08, page 15 
Copyright Daimler AG 2018 
7.1.2 
Passive wake up for ECUs with permanent power supply 
MSS 20206-128: 
A voltage level change or bit interferences on the network lines shall not cause the 
start of FR communication. Only a valid wake up pattern or valid FR frames shall be 
able to trigger a wake up. 
MSS 20206-129: 
In the following the wake up behavior of an ECU is described if the wake up is 
caused by the network (passive wake up). Refer to the chapter 7.1.4 "Network 
startup timing" for details. 
MSS 20206-130: 
TFRInit   If the physical layer signals a wake up event on the network lines while the 
FR hardware is in sleep mode, the FR hardware (physical layer and FR 
microcontroller) and the communication software modules shall be set to the 
operational mode. The Initialization shall be executed as fast as possible within 
TFRInit. TFRInit begins with the end of the first wake up pattern on the network signalled 
through the physical layer and ends after the ECU is initialized and ready for the 
coldstart / integration procedure (POC:ready).  
MSS 20206-131: 
TColdstartPrepare 
Time from start of coldstart listen to begin of cycle 0. 
MSS 20206-132: 
TWaitIntegration 
Time from start of integration listen to begin of cycle 0. 
MSS 20206-133: 
TColdstart 
Time within a leading coldstarter performs the coldstart procedure 
and changes to POC:normal active.  
MSS 20206-134: 
TIntegrationCS 
Time within a following coldstarter integrates into a FlexRay network 
and changes to POC:normal active. 
MSS 20206-135: 
TIntegration 
Time within a non coldstarter integrates into a FlexRay network and 
changes to POC:normal active. 
MSS 20206-136: 
TNetworkStartup 
Time within the whole network startup is finished and all ECUs have 
completed one cycle with POC:normal active. 
MSS 20206-137: 
TSignalValue 
Plausible signal values shall be transmitted as soon as possible, 
typically prior to the expiration of TSignalValue. For details see ECU specific software 
requirements. TSignalValue starts with the expiration of TFRInit. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 16 页
MSS 20206, V2.0, 2018-08, page 16 
Copyright Daimler AG 2018 
7.1.3 
Network startup for ECUs with switched power supply 
MSS 20206-139: 
The power supply and FR interface of ECUs with switched power supply are 
switched on by a clamp 15r/15/87 input. FR communication is only allowed if the 
clamp 15r/15/87 is switched on. 
MSS 20206-140: 
In the following the startup behavior of an ECU with switched power supply is 
described. Refer to the chapter 7.1.4 "Network startup timing" for details. 
MSS 20206-141: 
TFRInit  The FR hardware (physical layer and FR microcontroller) and the 
communication software modules shall be set to the operational mode. The 
Initialization shall be executed as fast as possible within TFRInit. TFRInit begins when 
the power supply is switched to system voltage and ends after the ECU is initialized 
and ready for the integration procedure (POC:ready). 
MSS 20206-142: 
TIntegration 
 Time within the ECU integrates into the FlexRay network and 
changes to POC:normal active. 
MSS 20206-143: 
TNetworkStartup 
  Time within the startup of the part of the cluster that is fed by a 
switched power supply is finished and all ECUs have completed one cycle with 
POC:normal active. 
MSS 20206-144: 
TSignalValue  
  Plausible signal values shall be transmitted as soon as possible, 
typically prior to the expiration of TSignalValue. For details see ECU specific software 
requirements. TSignalValue starts with the expiration of TFRInit. 
 
MSS 20206-145: 
In clusters without nodes that are fed by permanent power supply the cluster has to 
perform a coldstart after the power supply is switched on. In this case coldstart 
nodes with switched power supply are necessary and the following timings also 
apply: 
MSS 20206-146: 
TWaitIntegration 
Time from start of integration listen to begin of cycle 0. 
MSS 20206-147: 
TColdstartPrepare 
Time from start of coldstart listen to the begin of cycle 0. 
MSS 20206-148: 
TColdstart 
Time within a leading coldstarter performs the coldstart procedure 
and changes to POC:normal active. 
MSS 20206-149: 
TIntegrationCS 
Time within a following coldstarter integrates into a FlexRay network 
and changes to POC:normal active. 
7.1.4 
Network startup timing 
MSS 20206-151: 
The following figures illustrate the startup procedure from the perspective of the 
protocol operation control (POC): 
 
 
 
 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 17 页
MSS 20206, V2.0, 2018-08, page 17 
Copyright Daimler AG 2018 
MSS 20206-152: 
 
TNetworkStartup
normal active
no schedule
config
ready
wakeup
listen
wakeup
send
power
off/reset
A
local wakeup 
event
wakeup
channel A
coldstart
listen
coldstart consistency 
check
normal active
initialize 
schedule
integration
coldstart check
coldstart join
normal active
cycle 0
cycle 1
cycle 2
cycle 3
cycle 4
cycle 5
cycle 6
cycle 7
cycle 8
power
off/reset
config
initialize 
schedule
integration
consistency check
node becomes leading 
coldstarter
Legend
.. wakeup pattern
X
.. undefined state
.. defined POC state
schedule cycle
init
init
B
CAS
WUP
X
WUP
X
CAS
X
.. collision avoidance symbol
S
X
.. startup/sync frame
F
X
.. frame
X
A
.. send by node A
config
ready
S
B
S
B
S
B
S
B
S
B
S
B
S
B
S
B
S
B
S
A
S
A
S
A
S
A
S
A
F
C
init
ready
TFRInit’
TWakeup
TIntegrationCS
TFRInit’’
TColdstart
TFRInit’’’
TIntegration
cycle 9
S
B
S
A
F
C
TCycle
Wakeup by bus driver
(ECUs with permanent power supply)
clamp 15/87 switched on
(ECUs with switched power supply)
or
TWaitIntegration
TColdstartPrepare
TWaitIntegration
integration
listen
coldstart collision resolution
integration
listen
ready
power
off/reset
Node B
coldstart node
channel A
Channel A
Node C
non
coldstart node
channel A
Node A
wakeup/coldstart 
node
channel A
 
Figure F7.1.4a: Startup procedure timing 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 18 页
MSS 20206, V2.0, 2018-08, page 18 
Copyright Daimler AG 2018 
MSS 20206-153: 
The following figure depicts the timing of TSignalValue: 
MSS 20206-154: 
 
wake up/startup/integration
power off/
reset
FR node
config ready
normal active
wakeup from 
bus driver
or
clamp 15/87 
switched on
TFRInit
TSignalValue
(ECUs with permanent power supply)
(ECUs with switched power supply)
all signals are 
transmitted 
have plausible 
values
init
Figure F7.1.4b: Startup procedure timing - TSignalValue 
 
MSS 20206-155: 
ECUs shall follow the startup timing1 stipulated in the following table: 
MSS 20206-156: 
 
Name 
Definition 
Typ  
[ms] 
Max2 
[ms] 
TFRInit 
Time from indication of a wakeup event or ECU power 
supply switched on to the completed FR initialization of an 
ECU (POC:ready) 
80 
80 
TWakeup 
Time from start of wakeup listen to the transmission of the 
wakeup pattern 
10 
20 
TWaitIntegration 
Time from start of integration listen to begin of cycle 0 
- 
- 
TColdstartPrepare 
Time from start of coldstart listen and transmission of the 
CAS symbol to the begin of cycle 0 
15 
25 
TColdstart 
Time within a leading coldstarter performs the coldstart 
procedure and enters POC:normal active 
303 
303 
TIntegrationCS 
Time within a following coldstarter integrates into a FlexRay 
network and enters POC:normal active 
353 
353 
TIntegration 
Time within a non coldstarter integrates into a FlexRay 
network and enters POC:normal active 
403 
403 
TNetworkStartup 
Time from the indication of a wakeup event to all ECUs have 
completed one cycle with POC:normal active 
1403 
1503 
TSignalValue 
Time period within which typically4 all signals shall have 
plausible values. In principle the signals shall have plausible 
values as soon as possible. TSignalValue starts after the 
expiration of TFRInit 
600 
600 
1 ECU or system software requirement spec may require more stringent timing for initialization 
2 Timings shall be as fast as possible 
3 Only valid at a failure free startup 
4 For details see ECU specific software requirements. 
 
Table T7.1.4a: Startup timing1 
MSS 20206-157: 
Calculation of the parameters: 
MSS 20206-158: 
TWakeupTyp ≈ 2 · TCycle 
MSS 20206-159: 
TWakeupMax ≈ gListenNoise · 2 · TCycle 
MSS 20206-160: 
TColdstartPrepareTyp ≈ (2+1) · TCycle 
MSS 20206-161: 
TColdstartPrepareMax ≈ ((gListenNoise · 2) +1) · TCycle 
MSS 20206-162: 
TColdstart = 6 · TCycle  
MSS 20206-163: 
TIntegrationCS = 7 · TCycle 
MSS 20206-164: 
TIntegration = 8 · TCycle 
MSS 20206-165: 
TNetworkStartupTyp = TFRInit + TColdstartPrepareTyp + TIntegration + TCycle  
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 19 页
MSS 20206, V2.0, 2018-08, page 19 
Copyright Daimler AG 2018 
7.2 
Network sleep / power down 
7.2.1 
Network sleep for ECUs with permanent power supply 
7.2.1.1 
Sleep Strategy 
MSS 20206-169: 
Generally ECUs shall enable network sleep as soon as possible. 
MSS 20206-170: 
Network sleep is an application decision. Each ECU shall determine the criteria 
under which it will be “ready for network sleep”. 
MSS 20206-171: 
After startup of the networks ECUs shall prevent network sleep for not less than 
TStartupSleepDelayMin and transmit the stay awake reason "Awake_NwSt".  
MSS 20206-172: 
Subsequently ECUs without local stay awake reason shall enable network sleep 
within TStartupSleepDelayMax. 
MSS 20206-173: 
ECUs shall transmit the reasons which prevent enabling network sleep (stay awake 
reasons). 
MSS 20206-174: 
ECU limp home modes shall not result in preventing network sleep infinitely. 
MSS 20206-175: 
 
Name 
Definition 
Min [ms] 
Max [ms] 
TStartupSleepDelay 
Time period within the application shall enable 
network sleep after the startup of the network, if the 
application doesn’t require FlexRay communication. 
The time starts with the successful 
startup/integration in the FlexRay cluster 
3000 
6000 
 
Table T7.2.1.1a: Startup sleep delay 
 
7.2.1.2 
Sleep Procedure 
MSS 20206-177: 
If the Network Management performs a transition to Bus-Sleep Mode the shutdown 
of the cluster cannot be aborted. To provide the possibility for a fast re-wake up in 
case a node needs FlexRay communication, but could not signalize it in the last NM 
repetition cycle, every ECU shall stay prepared for a fast wake up until TRevokeBusSleep 
is expired. 
MSS 20206-178: 
 
Name 
Definition 
Min [ms] 
Max [ms] 
TRevokeBusSleep 
Time period within an ECU shall stay prepared 
for a fast wakeup after network sleep 
50 
100 
 
Table T7.2.1.2a: Sleep procedure timing 
 
7.2.2 
Network power down procedure for ECUs with switched power supply 
MSS 20206-180: 
The FR communication of ECUs with switched power supply shall end within 500ms 
after switching off the power supply. 
MSS 20206-181: 
If an ECU with switched power supply has stopped the FR communication because 
it is powered down, it shall have no effect on other ECUs communicating on the FR 
cluster. This may be done by disabling the FR bus driver before powering down. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 20 页
MSS 20206, V2.0, 2018-08, page 20 
Copyright Daimler AG 2018 
7.3 
Network Startup and Error handling 
7.3.1 
Wake up 
MSS 20206-184: 
After transmitting the wake up pattern the wake up ECU waits for a coldstart attempt 
of another ECU. If this wake up doesn’t lead to a coldstart attempt by another ECU 
within TWakeUpTimeOut, the wake up ECU shall perform a coldstart itself (Coldstart 
nodes only). If the startup cannot be completed successfully within TStartupTimeOut the 
ECU shall proceed with a consecutive startup (refer to chapter 7.3.3 "Consecutive 
Startup"). 
MSS 20206-185: 
 
Set Coldstart inhibit mode
Set StartupCounter := 1
Wake up
Wake up 
pWakeupChannel
SetTimer TWakeUpTimeOut
Startup
Enter 
normal operation
Startup successful
TWakeUpTimeOut
Clear
Coldstart inhibit mode
SetTimer
TStartupTimeOut
Enter 
normal operation
Startup successful
TStartupTimeOut
Consecutive
Startup
 
 
Figure F7.3.1a Wake up 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 21 页
MSS 20206, V2.0, 2018-08, page 21 
Copyright Daimler AG 2018 
7.3.2 
Startup 
MSS 20206-187: 
ECUs woken up by the network (passive wake up) and ECUs with switched power 
supply shall commence to startup the network immediately; Coldstart ECUs are 
instantly allowed to perform a coldstart. If the startup cannot be completed 
successfully within TStartupTimeOut the ECU shall proceed with a consecutive startup 
(refer to chapter 7.3.3 "Consecutive Startup"). 
MSS 20206-188: 
 
Enter 
normal operation
Startup successful
TStartupTimeOut
Consecutive
Startup
Startup
Startup
SetTimer TStartupTimeOut
Set StartupCounter := 1
 
 
Figure F7.3.2a: Startup 
 
7.3.3 
Consecutive Startup 
MSS 20206-190: 
If the first wake up/startup attempt (refer to chapter 7.3.1 "Wake up" and 7.3.2 
"Startup") cannot be completed successfully within TStartupTimeOut, the consecutive 
startup shall be performed. 
MSS 20206-191: 
If the startup counter does not exceed NStartupWithWakeupAttempts the ECU shall perform 
a Wake up succeeded by a Startup. ECUs woken up by the network shall not 
perform a Wake up, in this case NStartupWithWakeupAttempts equals 0. 
MSS 20206-192: 
If the startup counter exceeds NStartupWithWakeupAttempts but not NStartupAttempts the ECU 
shall perform a Startup. 
MSS 20206-193: 
If the startup counter exceeds NStartupAttempts the ECU shall enter a local limp home 
mode (refer to chapter 7.3.5 "Limp Home") 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 22 页
MSS 20206, V2.0, 2018-08, page 22 
Copyright Daimler AG 2018 
MSS 20206-194: 
 
Consecutive 
Startup
StartupCounter 
NStartupAttempts
No
StartupCounter 
NStartupWithWakeupAttempts
Yes
Wakeup 
pWakeupChannel
Yes
Config
Local limp home mode
No
Enter 
normal operation
Startup successful
TStartupTimeOut
Consecutive
Startup
Startup
Set StartupCounter = 
StartupCounter+1
Clear
Coldstart inhibit mode
 
 
Figure F7.3.3a: Consecutive Startup 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 23 页
MSS 20206, V2.0, 2018-08, page 23 
Copyright Daimler AG 2018 
7.3.4 
Halt 
MSS 20206-196: 
If the POC enters POC:halt due to an error condition, the ECU shall reconfigure the 
FlexRay CC. Subsequently the ECU shall perform a startup without allowing a 
coldstart. If the startup attempt cannot be successfully completed within 
TStartupTimeOut, the consecutive startup shall be performed. 
MSS 20206-197: 
 
Halt
Config
Set
Coldstart inhibit mode
Enter 
normal operation
Startup successful
TStartupTimeOut
Consecutive
Startup
Startup
SetTimer TStartupTimeOut
Set StartupCounter := 1
 
 
Figure F7.3.4a: Halt 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 24 页
MSS 20206, V2.0, 2018-08, page 24 
Copyright Daimler AG 2018 
7.3.5 
Limp Home 
MSS 20206-199: 
After an ECU exceeds NStartupAttempts the ECU will remain indefinitely in Startup 
without initiating further coldstart attempts. 
MSS 20206-200: 
The application for an ECU with permanent power supply shall decide if it requires 
FR communication or if it can switch to a local mode/powerdown. 
MSS 20206-201: 
 
Local limp home mode
FR communication 
necessary?
Enter 
local mode
Powerdown
Yes
No
Continue Startup Attempt
Request „No 
Communication“
Enter 
normal operation
Local limp home mode
Startup successful?
No
Yes
 
 
Figure F7.3.5a: Limp Home 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 25 页
MSS 20206, V2.0, 2018-08, page 25 
Copyright Daimler AG 2018 
7.3.6 
Parametrization 
MSS 20206-203: 
The parameters describe the behavior on level of the communication software. The 
FlexRay low level parameters gListenNoise and gColdstartAttempts are defined in 
the chapter 9.2.2 "Wake up and Startup Parameters". 
 
MSS 20206-204: 
 
Name 
Definition 
Value 
TWakeUpTimeOut 
Time from the first wakeup attempt until a coldstart 
attempt by the wakeup ECU is allowed 
130 ms 
TStartupTimeOut 
Time from the beginning of a startup attempt to 
abandonment of the attempt if the CC POC:normal active 
has not been reached 
270 ms 
NStartupWithWakeupAttempts 
Number of allowed attempts to startup the cluster with a 
wakeup  
81 
Number of allowed attempts to startup the cluster with a 
wakeup for ECUs woken up by the network 
0 
NStartupAttempts 
Number of allowed attempts to startup the cluster.  
161 
1  For dedicated ECUs, e.g. central gateways,  different values may be defined after consulting the 
Vehicle Networking Group 
 
Table T7.3.6a: Network startup parameters 
 
MSS 20206-205: 
Calculation of the parameters TWakeUpTimeOut  and  TStartupTimeOut: 
MSS 20206-206: 
TWakeUpTimeOut = TFRInit + TListenNoise + 1 · TColdstartAttempt 
MSS 20206-207: 
TStartupTimeOut  = TListenNoise + (gColdstartAttempts-1) · TColdstartAttempt + TSuccessfulColdstart 
MSS 20206-208: 
TFRInit  
Initialization time of a FR ECU 
 
 
(Refer to chapter 7.1.4 "Network startup timing") 
MSS 20206-209: 
TListenNoise 
Maximal time for integration listen 
MSS 20206-210: 
 
 
TListenNoise ≈ gListenNoise · 2 · TCycle 
 
MSS 20206-211: 
TColdstartAttempt 
Time for a coldstart attempt 
 
 
(1· CAS or coldstart gap + 5 startup frames) 
MSS 20206-212: 
 
 
TColdstartAttempt = 6 · TCycle 
 
MSS 20206-213: 
TSuccessfulColdstart Time for a successful coldstart until POC:normal active is reached 
MSS 20206-214: 
 
 
TSuccessfulColdstart = 8 · TCycle 
 
MSS 20206-215: 
Rationale: 
In the calculation of TWakeUpTimeOut the time for a coldstart attempt 
TColdstartAttempt is only accounted by the factor 1 instead of gColdstartAttempts to 
avoid a long timeout if the first wake up attempt is lost. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 26 页
MSS 20206, V2.0, 2018-08, page 26 
Copyright Daimler AG 2018 
8 
Testing 
8.1 
FlexRay Node Information 
MSS 20206-218: 
Every ECU connected to the FlexRay shall provide the active FlexRay 
parametrization via the service "FlexRay Node Information (0xF1A6)". Refer to 
[DSUDS] and [CANDELA_TEMPLATE] respectively for details of the service and 
information regarding the required data encoding. 
8.2 
FlexRay Configuration Information 
MSS 20206-220: 
Every ECU connected to the FlexRay shall provide the active FlexRay 
parametrization via the service "FlexRay Configuration Information (0xF10E)". Refer 
to [DSUDS] and [CANDELA_TEMPLATE] respectively for details of the service and 
information regarding the required data encoding. Refer to table T8.2a in this 
specification for its data record definition. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 27 页
MSS 20206, V2.0, 2018-08, page 27 
Copyright Daimler AG 2018 
MSS 20206-221: 
 
Data 
Byte No. 
Parameter Name / Description 
Hex Range 
(Big Endian) 
Unit /  
Resolution 
0 
Network ID 
33 Hex 
 
1 
pSamplesPerMicrotick 
00-FF 
 
2 
gNumberOfStaticSlots 
00-FF 
 
3-4 
gdStaticSlot 
0000-FFFF 
MT=Macrotick 
5 
gdActionPointOffset 
00-FF 
MT 
6 
gdTSSTransmitter 
00-FF 
gdBit 
7 
gPayloadLengthStatic 
00-FF 
word 
8 
gdMinislot 
00-FF 
MT 
9 
gdMinislotActionPointOffset 
00-FF 
MT 
10-11 
gdNIT 
0000-FFFF 
MT 
12 
gNetworkManagementVectorLength 
00-FF 
byte 
13 
pMacroInitialOffsetA 
00-FF 
MT 
14 
pMacroInitialOffsetB 
00-FF 
MT 
15 
pMicroInitialOffsetA 
00-FF 
µT= microtick 
16 
pMicroInitialOffsetB 
00-FF 
µT 
17 
gColdstartAttempts 
00-FF 
 
18 
gListenNoise 
00-FF 
 
19 
gdCASRxLowMax 
00-FF 
gdBit 
20 
gdWakeupSymbolRxIdle 
00-FF 
gdBit 
21 
gdWakeupSymbolRxLow 
00-FF 
gdBit 
22 
gdWakeupSymbolTxIdle 
00-FF 
gdBit 
23 
gdWakeupSymbolTxLow 
00-FF 
gdBit 
24 
pWakeupChannel 
00-FF 
0: Ch A / 1: Ch B 
25 
pWakeupPattern 
00-FF 
number of WUP 
26-27 
pdMaxDrift 
0000-FFFF 
µT 
28-29 
pOffsetCorrectionOut 
0000-FFFF 
µT 
30-31 
pRateCorrectionOut 
0000-FFFF 
µT 
32-33 
pKeySlotId 
0000-FFFF 
 
34 
gMaxWithoutClockCorrectionFatal 
00-FF 
cycle pairs 
35 
gMaxWithoutClockCorrectionPassive 
00-FF 
cycle pairs 
36 
pAllowPassiveToActive 
00-FF 
cycle pairs 
37 
pClusterDriftDamping 
00-FF 
µT 
38 
pDelayCompensationA 
00-FF 
µT 
39 
pDelayCompensationB 
00-FF 
µT 
40 
pDecodingCorrection 
00-FF 
µT 
41-42 
gdSampleClockPeriod 
0000-FFFF 
0,1 ns = 1/10 ns 
43-44 
gdBit 
0000-FFFF 
ns 
45-46 
gMacroPerCycle 
0000-FFFF 
MT/Cycle 
47-48 
gNumberOfMinislots 
0000-FFFF 
Minislot 
49-50 
gdWakeupSymbolRxWindow 
0000-FFFF 
gdBit 
51-52 
pdAcceptedStartupRange 
0000-FFFF 
µT 
53-54 
gOffsetCorrectionStart 
0000-FFFF 
MT 
55-56 
pLatestTx 
0000-FFFF 
Minislot 
57-60 
pMicroPerCycle 
0000000-
FFFFFFFF 
µT 
61-64 
pdListenTimeout 
0000000-
FFFFFFFF 
µT 
65 
pChannelsA 
00-01 
used = true: 1, false: 0 
66 
pChannelsB 
00-01 
used = true: 1, false: 0 
67 
pKeySlotUsedForStartup 
00-01 
0: false / 1: true 
68 
pKeySlotUsedForSync 
00-01 
0: false / 1: true 
69 
pAllowHaltDueToClock 
00-01 
0: false / 1: true 
70- 
(reserved) 
00-FF 
 
 
Table T8.2a: Data Record Definition 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 28 页
MSS 20206, V2.0, 2018-08, page 28 
Copyright Daimler AG 2018 
9 
FlexRay Parameter Set 
9.1 
FlexRay High-Level Parameters 
MSS 20206-224: 
This section shows the values for the high-level parameters from which most of the 
low-level parameters are derived. 
MSS 20206-225: 
 
Parameter 
Value 
Unit 
Bit rate 
10 
Mbit/s 
Communication cycle length 
5 
ms 
Duration of static segment 
3.109 
ms 
Nominal microtick length 
25 
ns 
Oscillator quality 
200 
ppm 
Maximum physical layer propagation delay without CC 
delays 
526 
ns 
Maximum propagation delay including CC delays 
664 
ns 
Minimum physical layer propagation delay without CC 
delays 
24 
ns 
Minimum propagation delay including CC delays 
111 
ns 
Assumed Precision 
3.082 
µs 
Static payload length 
64 
byte 
Maximum number of stars 
1 
 
Connected channels 
A (+B) 
 
Duration of ringing after transmission 
0 
ns 
 
Table T9.1a: High Level Parameters 
 
MSS 20206-226: 
Physical layer parameters: 
MSS 20206-227: 
 
Parameter 
Value 
Unit 
Active star delay 
170 
ns 
Bus driver delay (transmission) 
75 
ns 
Bus driver delay (reception) 
80 
ns 
Maximum line length between any two nodes 
24 
m 
Maximum cable propagation speed 
6 
ns/m 
Overall Frame TSS Truncation 
1100 
ns 
 
Table T9.1b: Physical Layer Parameters 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 29 页
MSS 20206, V2.0, 2018-08, page 29 
Copyright Daimler AG 2018 
9.2 
FlexRay Low-Level Parameters 
MSS 20206-229: 
This section shows the various low-level parameters. Most of them were derived 
from the High-Level Parameters. 
9.2.1 
Cycle Parameters 
MSS 20206-231: 
 
Parameter 
Value 
Unit 
gdSampleClockPeriod 
0.0125 
µs 
pSamplesPerMicrotick 
2 
 
gdBit 
0.1 
µs 
gdMacrotick 
1.48765251 
µs 
gMacroPerCycle 
3361 
MT 
pMicroPerCycle 
200000 
µT 
gNumberOfStaticSlots 
38 
 
gdStaticSlot 
55 
MT 
gdActionPointOffset 
2 
MT 
gdTSSTransmitter 
13 
gdBit 
gPayloadLengthStatic 
32 
word 
gNumberOfMinislots 
211 
Minislot 
gdMinislot 
6 
MT 
gdMinislotActionPointOffset 
2 
MT 
gdDynamicSlotIdlePhase 
0 
Minislot 
gdSymbolWindow 
0 
MT 
gdNIT 
5 
MT 
gSyncNodeMax 
15 
 
gNetworkManagementVectorLength 
1 
byte 
pChannels (= gChannels) 
A (+B) 
 
1 Cycle length divided by number of macroticks 
 
 Table T9.2.1a: Cycle Parameters 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 30 页
MSS 20206, V2.0, 2018-08, page 30 
Copyright Daimler AG 2018 
9.2.2 
Wake up and Startup Parameters 
MSS 20206-233: 
 
Parameter 
Value 
Unit 
pMacroInitialOffset 
4 
MT 
pMicroInitialOffset 
55 
 
 µT 
gColdstartAttempts 
8 
 
gListenNoise 
2 
 
gdCASRxLowMax 
97 
gdBit 
gdWakeupSymbolRxIdle 
40 
gdBit 
gdWakeupSymbolRxLow 
40 
gdBit 
gdWakeupSymbolRxWindow 
301 
gdBit 
gdWakeupSymbolTxIdle 
180 
gdBit 
gdWakeupSymbolTxLow 
60 
gdBit 
pWakeupChannel 
A (+B) 
 
pWakeupPattern 
33 
 
pdAcceptedStartupRange 
269 
µT 
gdMaxInitializationError 
3.635 
µs 
pdListenTimeout 
400402 
µT 
pdMaxDrift 
201 
µT 
 
Table T9.2.2a: Wake up and Startup Parameters 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 31 页
MSS 20206, V2.0, 2018-08, page 31 
Copyright Daimler AG 2018 
9.2.3 
Clock Correction Parameters 
MSS 20206-235: 
 
Parameter 
Value 
Unit 
gOffsetCorrectionMax 
3.635 
µs 
pOffsetCorrectionOut 
146 
µT 
pRateCorrectionOut 
201 
µT 
pKeySlotId 
appl. defined 
 
pKeySlotUsedForStartup 
appl. defined 
Boolean 
pKeySlotUsedForSync 
appl. defined 
Boolean 
pSingleSlotEnabled 
False 
Boolean 
gOffsetCorrectionStart 
3357 
MT 
gMaxWithoutClockCorrectionFatal 
2 
even/odd 
cycle pairs 
gMaxWithoutClockCorrectionPassive 
2 
even/odd 
cycle pairs 
pAllowHaltDueToClock 
True 
Boolean 
pAllowPassiveToActive 
0 
even/odd 
cycle pairs 
pClusterDriftDamping (= gClusterDriftDamping) 
2 
µT 
pDelayCompensation 
5 
µT 
pDecodingCorrection 
60 
µT 
pExternOffsetCorrection 
0 
µT 
pExternRateCorrection 
0 
µT 
 
Table T9.2.3a: Clock Correction Parameters 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 32 页
MSS 20206, V2.0, 2018-08, page 32 
Copyright Daimler AG 2018 
9.2.4 
pLatestTx Parameter 
MSS 20206-237: 
The parameter pLatestTx denotes the last minislot in which a dynamic frame 
transmission is allowed to start and therefore depends on the dynamic payload 
length. 
MSS 20206-238: 
 
Max. dynamic 
payload [byte] 
pLatestTx 
[Minislot] 
 
Max. dynamic 
payload [byte] 
pLatestTx 
[Minislot] 
2 
 
209 
 
 
 
66 
202 
4 
209 
 
68 
202 
6 
209 
 
70 
202 
8 
209 
 
72 
201 
10 
208 
 
74 
201 
12 
208 
 
76 
201 
14 
208 
 
78 
201 
16 
208 
 
80 
200 
18 
207 
 
82 
200 
20 
207 
 
84 
200 
22 
207 
 
86 
200 
24 
207 
 
88 
200 
26 
206 
 
90 
199 
28 
206 
 
92 
199 
30 
206 
 
94 
199 
32 
206 
 
96 
199 
34 
206 
 
98 
198 
36 
205 
 
100 
198 
38 
205 
 
102 
198 
40 
205 
 
104 
198 
42 
205 
 
106 
198 
44 
204 
 
108 
197 
46 
204 
 
110 
197 
48 
204 
 
112 
197 
50 
204 
 
114 
197 
52 
204 
 
116 
196 
54 
203 
 
118 
196 
56 
203 
 
120 
196 
58 
203 
 
122 
196 
60 
203 
 
124 
196 
62 
202 
 
126 
195 
64 
202 
 
128 
195 
 
Table T9.2.4a: pLatestTx Parameter 1 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 33 页
MSS 20206, V2.0, 2018-08, page 33 
Copyright Daimler AG 2018 
MSS 20206-239: 
 
Max. dynamic 
payload [byte] 
pLatestTx 
[Minislot] 
 
Max. dynamic 
payload [byte] 
pLatestTx 
[Minislot] 
130 
195 
 
194 
188 
132 
195 
 
196 
187 
134 
194 
 
198 
187 
136 
194 
 
200 
187 
138 
194 
 
202 
187 
140 
194 
 
204 
187 
142 
193 
 
206 
186 
144 
193 
 
208 
186 
146 
193 
 
210 
186 
148 
193 
 
212 
186 
150 
193 
 
214 
185 
152 
192 
 
216 
185 
154 
192 
 
218 
185 
156 
192 
 
220 
185 
158 
192 
 
222 
185 
160 
191 
 
224 
184 
162 
191 
 
226 
184 
164 
191 
 
228 
184 
166 
191 
 
230 
184 
168 
191 
 
232 
183 
170 
190 
 
234 
183 
172 
190 
 
236 
183 
174 
190 
 
238 
183 
176 
190 
 
240 
182 
178 
189 
 
242 
182 
180 
189 
 
244 
182 
182 
189 
 
246 
182 
184 
189 
 
248 
182 
186 
189 
 
250 
181 
188 
188 
 
252 
181 
190 
188 
 
254 
181 
192 
188 
 
 
 
 
Table T9.2.4b: pLatestTx Parameter 2 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 34 页
MSS 20206, V2.0, 2018-08, page 34 
Copyright Daimler AG 2018 
9.3 
Dynamic frame duration 
MSS 20206-241: 
The table below shows the required number of minislots for a given dynamic 
payload. It allows optimizing the payload usage and shows if additional payload can 
be transmitted ‘for free’. 
MSS 20206-242: 
 
Dynamic payload 
[byte] 
Number of Minislots 
[Minislot] 
 
Dynamic payload 
[byte] 
Number of Minislots 
[Minislot] 
2 
3 
 
66 
10 
4 
3 
 
68 
10 
6 
3 
 
70 
10 
8 
3 
 
72 
11 
10 
4 
 
74 
11 
12 
4 
 
76 
11 
14 
4 
 
78 
11 
16 
4 
 
80 
12 
18 
5 
 
82 
12 
20 
5 
 
84 
12 
22 
5 
 
86 
12 
24 
5 
 
88 
12 
26 
6 
 
90 
13 
28 
6 
 
92 
13 
30 
6 
 
94 
13 
32 
6 
 
96 
13 
34 
6 
 
98 
14 
36 
7 
 
100 
14 
38 
7 
 
102 
14 
40 
7 
 
104 
14 
42 
7 
 
106 
14 
44 
8 
 
108 
15 
46 
8 
 
110 
15 
48 
8 
 
112 
15 
50 
8 
 
114 
15 
52 
8 
 
116 
16 
54 
9 
 
118 
16 
56 
9 
 
120 
16 
58 
9 
 
122 
16 
60 
9 
 
124 
16 
62 
10 
 
126 
17 
64 
10 
 
128 
17 
 
Table T9.3a: Dynamic frame duration 1 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 35 页
MSS 20206, V2.0, 2018-08, page 35 
Copyright Daimler AG 2018 
MSS 20206-243: 
 
Dynamic payload 
[byte] 
Number of Minislots 
[Minislot] 
 
Dynamic payload 
[byte] 
Number of Minislots 
[Minislot] 
130 
17 
 
194 
24 
132 
17 
 
196 
25 
134 
18 
 
198 
25 
136 
18 
 
200 
25 
138 
18 
 
202 
25 
140 
18 
 
204 
25 
142 
19 
 
206 
26 
144 
19 
 
208 
26 
146 
19 
 
210 
26 
148 
19 
 
212 
26 
150 
19 
 
214 
27 
152 
20 
 
216 
27 
154 
20 
 
218 
27 
156 
20 
 
220 
27 
158 
20 
 
222 
27 
160 
21 
 
224 
28 
162 
21 
 
226 
28 
164 
21 
 
228 
28 
166 
21 
 
230 
28 
168 
21 
 
232 
29 
170 
22 
 
234 
29 
172 
22 
 
236 
29 
174 
22 
 
238 
29 
176 
22 
 
240 
30 
178 
23 
 
242 
30 
180 
23 
 
244 
30 
182 
23 
 
246 
30 
184 
23 
 
248 
30 
186 
23 
 
250 
31 
188 
24 
 
252 
31 
190 
24 
 
254 
31 
192 
24 
 
 
 
 
Table T9.3b: Dynamic frame duration 2 
 
9.4 
Additional Constraints 
MSS 20206-245: 
The values for pKeySlotId, pKeySlotUsedForStartup and pKeySlotUsedForSync 
depend on the schedule design and the topology. 
MSS 20206-246: 
It shall be observed that the slot IDs for sync frames are not adjacent to slot IDs for 
other sync frames. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 36 页
MSS 20206, V2.0, 2018-08, page 36 
Copyright Daimler AG 2018 
10 ECU Physical Layer Circuitry 
MSS 20206-248: 
All values listed in this document shall be valid within the specified temperature 
range (-40°C … +85°C / 105°C / 125°C) of the ECU (refer to the Component 
Specification of the affected ECU).  
10.1 Supported Baudrates 
MSS 20206-250: 
The FlexRay interface of a FlexRay ECU shall support physically the baud rate of 
10MBaud. 
10.2 Oscillator Requirements 
MSS 20206-252: 
The tolerance of the oscillator that is clocking the FlexRay communication controller 
shall be ≤ 200ppm (0.02% or better). This ≤ 200ppm requirement shall be fulfilled 
including initial tolerance, aging and temperature effects (refer also to chapter 4 
"General design requirements"). 
MSS 20206-253: 
If an integrated communication controller inside a host controller is used, the 
clocking of the FlexRay communication controller is sometimes derived from an 
internal PLL clock generation. In that case the ECU designer shall ensure that the 
oscillator tolerance of the internal FlexRay communication controller within the host 
controller chip fulfills a requested oscillator tolerance of ≤ 200ppm. Thus, the 
oscillator tolerance of the external oscillator device that is clocking the host controller 
shall be adapted to the capabilities of the host controller’s internal clock generation 
in order to assure the required oscillator tolerance of ≤ 200ppm for the internal 
FlexRay communication controller. 
Please consider also [ISO 17458-4] - chapter A.******* "Sampling clock accuracy". 
MSS 20206-254: 
To avoid additional Jitter the usage of Spread Spectrum Oscillators (Spread 
Spectrum Clocking) shall not be used to generate the FlexRay clock. 
MSS 20206-255: 
According [ISO 17458-4] - Table 20 "Asymmetric delay budget (TP3 to TP5)" and 
Figure 27 the asymmetric delay of the communication controller shall be ≤5ns 
(TP4_CCi to TP5_CC). This value includes also the asymmetry and jitter caused by 
the external clock source (crystal based oscillator) plus the host controller’s internal 
clock generation (PLL), but excludes asymmetry due to the clock tolerance. It shall 
be verified that this requirement is kept. 
MSS 20206-256: 
Measurements shall be performed to ensure compliance to the timing requirements 
since FlexRay is sensitive regarding asymmetric delays. Please refer to [ISO 17458-
4] or the [EPL V3.0.1] and the associated Application Notes for details. 
MSS 20206-257: 
Refer to chapter 10.8 "Interface between Communication Controller and 
Transceiver" regarding further asymmetric delay requirements. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 37 页
MSS 20206, V2.0, 2018-08, page 37 
Copyright Daimler AG 2018 
10.3 FlexRay Transceivers 
MSS 20206-259: 
 
Supplier: 
Type: 
Category: 
Elmos 
E981.56 
Active Star 
Infineon 
TLE9221SX 
Transceiver 
TLE9222 
NXP 
TJA1081G 
TJA1083G 
TJA1085G 
Active Star 
TJA1086G 
OnSemi 
NCV7381 
Transceiver 
NCV7383 
 
Table T10.3a: FlexRay transceivers recommended for new ECU designs 
 
MSS 20206-260: 
Refer to the corresponding datasheets and application hints and check if the chosen 
transceiver satisfies the component requirements. 
MSS 20206-261: 
The different clock timing to readout the status register shall be considered. 
MSS 20206-262: 
New unlisted Transceivers shall successfully pass the FlexRay Conformance Test 
according the [Electrical Physical Layer Conformance Test Specification Version 
3.0.1] or [ISO 17458-5] performed by an external independent testing institute. 
MSS 20206-263: 
Furthermore, a passed EMC Test according the [FlexRay Physical Layer EMC 
Measurement Specification V3.0.1] performed by an external independent testing 
institute is required. 
MSS 20206-264: 
The corresponding reports and certificates shall be provided to Daimler by the 
supplier. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 38 页
MSS 20206, V2.0, 2018-08, page 38 
Copyright Daimler AG 2018 
10.4 Common Mode Chokes 
MSS 20206-266: 
Only chokes with bifilar winding shall be used. Therefore, it is not allowed to use 
chokes with sector winding. 
MSS 20206-267: 
 
Supplier: 
Type: 
Murata 
DLW43SH101XK2 
TDK 
ACT45R-101-2P 
ACT1210-101-2P 
Epcos(1) 
B82789C0104N001(1) 
B82789C0104N002(1) 
B82789C0104H001(1) 
B82789C0104H002(1) 
B82789C0104H052(1) 
 
Table T10.4a: Recommended(2) common mode chokes 
 
MSS 20206-268: 
(2) Recommended concerning their electrical parameters only; qualification regarding 
mechanical and temperature requirements shall be done by the supplier. 
MSS 20206-269: 
(1) The choke manufacturer does not recommend to use the common mode chokes 
Epcos B82789 for new ECU designs due to limited production capacities. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 39 页
MSS 20206, V2.0, 2018-08, page 39 
Copyright Daimler AG 2018 
10.5 Bus Termination 
10.5.1 General hints on Bus Terminations 
MSS 20206-272: 
Every FlexRay physical layer interface within an ECU shall be supplied with an 
adequate termination circuitry. 
MSS 20206-273: 
This termination circuitry generally performs two functions: 
-  signal integrity:   prevents reflections of the differential signal mode at the ends of 
the bus lines (effective for end-nodes only) 
-  electromagnetic compatibility:   terminates common mode interference signals on 
the bus lines (effective for both: end-nodes and in-between nodes) 
 
Therefore the termination circuitry of an ECU depends on the position of the ECU 
within the network topology. 
Refer to chapter 11 "ECU Classification" to determine the requested node type 
assignment and the dedicated implementation type of the bus termination. 
 
10.5.2 Termination of End-nodes 
MSS 20206-275: 
The termination of a FlexRay end-node provides an adequate termination for the 
differential FlexRay data signal, as well as a termination of common mode 
interference signals. An applicable end-node termination circuitry is shown in Figure 
F10.5.2a. The termination resistors (Sum of R1, R2, R3, R4) match the transmission 
line’s characteristic impedance. 
MSS 20206-276: 
A low tolerance of 1% of the resistors R1, R2, R3, R4 is required to prevent 
asymmetries in the network. 
MSS 20206-277: 
The minimum nominal power rating of each resistor R1, R2, R3, R4 is 125mW to 
guarantee fault-free electromagnetic immunity. 
MSS 20206-278: 
The capacitor Cs conducts common mode interference signals to ground. 
MSS 20206-279: 
 
BM
BP
Transceiver
transmission lines
CMC
R1
R3
Rs
R2
R4
Cs
Basic circuitry of end node and in-between node
are identical. Component values vary!
BP
BP
BM
BM
09 / 2015
necessary if ECU 
shall have an equipping 
option as in-between node
 
 
Figure F10.5.2a: termination circuitry for FlexRay end-nodes 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 40 页
MSS 20206, V2.0, 2018-08, page 40 
Copyright Daimler AG 2018 
MSS 20206-280: 
 
Part 
Value 
Tolerance 
Dimension 
Remark 
R1, R2, 
R3, R4 
25.5 
  1 % 
SMD 0805 
min. power rating 125mW IEC_60063-E96 se-
ries, consider the temperature profile 
Rs 
- 
- 
SMD 0805 
omit Rs, provide unpopulated pad for SMD 0805 
Cs 
4.7nF 
  20 % 
SMD 0805 
or less 
voltage rating in accordance with [VDA 320] and 
[MBN 10567] requirements to avoid damage (at 
least  50V required), ceramic type with small 
inductance (ESL) 
CMC 
chapter 10.4 
- 
- 
see chapter 10.4 “Common Mode Chokes” 
 
Table T10.5.2a: components of termination circuitry for FlexRay end-nodes 
 
MSS 20206-281: 
Pads for ESD protection parts located between the termination network and the ECU 
connector shall be present but shall be left unpopulated. Refer to chapter 10.6 
"Circuit Diagrams" for more information. 
MSS 20206-282: 
The position of the common mode choke and the termination circuitry with regard to 
bus line and transceiver shall not be changed. 
MSS 20206-283: 
A single termination resistor of 100Ω without center tapped split capacitor to ground 
is insufficient and is not allowed. 
MSS 20206-284: 
Unpopulated pads shall be present in the layout. 
 
10.5.3 Termination of In-between Nodes 
MSS 20206-286: 
The termination of a FlexRay in-between node provides a grounding of common 
mode interference signals without affecting the differential FlexRay data signal. The 
requested termination circuitry for in-between nodes is shown in Figure F10.5.3a. 
MSS 20206-287: 
 
BM
BP
Transceiver
transmission lines
CMC
R1
R3
Rs
R2
R4
Cs
Basic circuitry of end node and in-between node
are identical. Component values vary!
BP
BP
BM
BM
09 / 2015
 
 
Figure F10.5.3a: termination circuitry for FlexRay in-between nodes 
 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 41 页
MSS 20206, V2.0, 2018-08, page 41 
Copyright Daimler AG 2018 
MSS 20206-288: 
 
Part 
Value 
Tolerance 
Dimension 
Remark 
R1, R2, 
R3, R4 
649 
  1 % 
SMD 0805 
min. power rating 125mW IEC_60063-E96 se-
ries, consider the temperature profile 
Rs 
- 
- 
SMD 0805 
omit Rs, provide unpopulated pad for SMD 0805 
Cs 
4.7nF 
  20 % 
SMD 0805 
or less 
voltage rating in accordance with [VDA 320] and 
[MBN 10567] requirements to avoid damage (at 
least  50V required), ceramic type with small 
inductance (ESL) 
CMC 
chapter 10.4 
- 
- 
see chapter 10.4 “Common Mode Chokes” 
 
Table T10.5.3a: components of termination circuitry for FlexRay in-between nodes 
 
MSS 20206-289: 
Pads for ESD protection parts located between the termination network and the ECU 
connector shall be present but shall be left unpopulated. Refer to chapter 10.6 
"Circuit Diagrams" for more information. 
MSS 20206-290: 
The position of the common mode choke and the termination circuitry with regard to 
bus line and transceiver shall not be changed. 
MSS 20206-291: 
A single termination resistor without center tapped split capacitor to ground is 
insufficient and is not allowed. 
MSS 20206-292: 
In-between nodes without common mode termination circuitry (e.g. equipped with a 
common mode choke only) are not allowed due to their poor EMC performance. 
MSS 20206-293: 
Unpopulated pads shall be present in the layout. 
 
10.5.4 Termination of Active Star ECUs 
MSS 20206-295: 
The branch connections of an active star ECU are typically end-node 
implementations. 
See chapter 10.5.2 "Termination of End-nodes" and chapter 11 "ECU Classification". 
10.5.5 Universal Termination for optional end or in-between nodes 
MSS 20206-297: 
Sometimes it may be necessary that one ECU can be configured as an end node or 
an in-between node depending on topology. In this case end termination circuitry 
given in Figure F10.5.5a is recommended. 
A termination circuitry for in-between nodes as described in chapter 10.5.3 
"Termination of In-between Nodes" with four connector pins is enhanced by an 
additional end node termination with two additional pins at the ECU connector. 
This configuration may be used as  
a) an in-between node (in this case the end node termination remains unused) or  
b) an end node termination if two pins of the in-between node termination are 
connected to the end node termination by means of a short transmission line loop in 
the cable harness. 
MSS 20206-298: 
The transmission line loop shall be kept strictly symmetrical and the twisting of the 
cables shall be maintained. 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 42 页
MSS 20206, V2.0, 2018-08, page 42 
Copyright Daimler AG 2018 
MSS 20206-299: 
 
BM
BP
Transceiver
CMC
R7
R5
R8
R6
CT
BP
BP
BP
BM
BM
BM
in-between node termination
end node termination
09 / 2015
R1
R3
Rs
R2
R4
Cs
transmission lines
   
 
Figure F10.5.5a: termination circuitry for an ECU which can be configured as end 
node or in-between node 
 
MSS 20206-300: 
 
transmission lines
transmission lines
BP
BP
BP
BP
BP
BP
BM
BM
BM
BM
BM
BM
(b) twisted pair transmission 
line loop may transform 
in-between node optionally 
into an end node
a) normal in-between node
configuration
from other node / star
from other node / star
to other node
from in-between 
termination to 
end termination
09/2015
 
 
Figure F10.5.5b: ECU with termination loop 
 
MSS 20206-301: 
 
Part 
Value 
Tolerance 
Dimension 
Remark 
R1, R2, 
R3, R4 
649  
  1 % 
SMD 0805 
min. power rating 125mW IEC_60063-E96 se-
ries, consider the temperature profile 
R5, R6, 
R7, R8 
25.5  
  1 % 
SMD 0805 
min. power rating 125mW IEC_60063-E96 se-
ries, consider the temperature profile 
Rs 
- 
- 
SMD 0805 
omit Rs, provide unpopulated pad for SMD 0805 
Cs, CT 
4.7nF 
  20 % 
SMD 0805 
or less 
voltage rating in accordance with [VDA 320] and 
[MBN 10567] requirements to avoid damage (at 
least  50V required), ceramic type with small 
inductance (ESL) 
CMC 
chapter 10.4 
- 
- 
see chapter 10.4 “Common Mode Chokes” 
 
Table T10.5.5a: components of termination circuitry for FlexRay optional end or in-
between nodes 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 43 页
MSS 20206, V2.0, 2018-08, page 43 
Copyright Daimler AG 2018 
10.6 Circuit Diagrams 
MSS 20206-303: 
The circuit diagrams given in the following sections are exemplary circuit diagrams 
for a specific transceiver. The circuitry shall be adapted to the specific requirements 
of the implemented transceiver and the specific ECU requirements in general. Other 
ECU components not being part of the FlexRay physical layer such as host 
controller, power supply, ECU specific circuitry, etc. are diagrammed as simplified 
block diagrams as far as they are related to the physical layer. 
MSS 20206-304: 
The circuit diagrams given in the following sections are pure schematics. They are 
not intended as a layout recommendation in terms of placement of the components 
on the PCB, routing of signal lines on the PCB or circuit grounding concept. In 
particular these schematics do not distinguish between different ground areas, only 
one common ground symbol is used. General layout requirements corresponding to 
ground concept, placement of components and signal line routing on PCB for 
FlexRay ECU designs are given in chapter 10.7 "General ECU layout requirements" 
and chapter 10.8 "Interface between Communication Controller and Transceiver". 
These are applicable to all following circuit diagrams. 
MSS 20206-305: 
The ECU shall be designed to fulfill the requirements given in [MBN 10567] 
according to the associated Component Requirement Specification. ECUs with 48V 
supply shall fulfill  [VDA320]. 
MSS 20206-306: 
Because of the coexistence of 12V as well as 48V supplied ECUs in the vehicle: 
ECUs which are supplied solely from the 48 V system must not damage other ECUs 
of the FlexRay network in case of ground loss according to [VDA320]. 
 
10.6.1 Basic circuit diagram 
MSS 20206-308: 
Figure F10.6.1a shows the essential and obligatory part of a FlexRay node interface 
circuitry. 
MSS 20206-309: 
The pads shall be present within the PCB layout for all parts that are included in 
Figure F10.6.1a. 
MSS 20206-310: 
For the other pins of IC1 / IC2 that are not mentioned in Figure F10.6.1a refer to the 
following chapters 10.6.2 to 10.6.5 as well as to the requirements given within the 
data sheets and application notes of the semiconductor manufacturers. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 44 页
MSS 20206, V2.0, 2018-08, page 44 
Copyright Daimler AG 2018 
MSS 20206-311: 
 
IC1
FlexRay
Controller
(µC)
FlexRay_A1_BP
FlexRay_A1_BM
FlexRay_A2_BP
FlexRay_A2_BM
BM
BP
TXD
TXEN
RXD
TX
TXEN
RX
IC2
ESD 1
ESD 2
ESD 3
R1
R2
R3
R4
Rs
Cs
RTXEN
RTX
RRX
Oscillator
CMC
Common
Mode
Choke
GND
FlexRay
Transceiver
ERRN
INT / IO
(a) The connection to an interrupt pin of the µC might be necessary to reliably recognize 
the transceiver wake up.
ERRN is capable to signal the wake up if the transceiver is compliant to ISO 17458-4 or 
the FlexRay Electrical Physical Layer Specification V 2.1 Rev. B or later versions.
(b) Layout: The pads for the not populated dashed parts shall be present .
(a) 
08 / 2015
(c) 4 FlexRay Pins to enable the usage as inbetween node
(c) 
(c) 
Quartz
(b) 
 
Figure F10.6.1a: basic physical layer circuitry 
 
MSS 20206-312: 
Refer also to chapter 10.7 "General ECU layout requirements" and chapter 10.8 
"Interface between Communication Controller and Transceiver" as well as chapters 
10.6.2 to 10.6.5 for more specific details. 
MSS 20206-313: 
 
Part 
Value 
Tolerance 
Dimension 
Remark 
IC1 
micro- 
controller 
 
 
includes FlexRay Communication Controller 
IC2 
chapter 10.3 
 
 
FlexRay Transceiver. If the wake pin is not used 
connect with 0 to GND. Otherwise the wake pin 
shall be connected according to the requirements 
given within the transceiver data sheet or applica-
tion note. Use ERRN to indicate the wake-up to 
the µC. 
R1, R2, 
R3, R4 
25.5 or 
649 
  1 % 
SMD 0805 
Details see chapter 10.5 “Bus Termination”, 
value depends on chapter 11 “ECU classification” 
Rs 
- 
- 
SMD 0805 
Details see chapter 10.5 “Bus Termination”. 
Do not populate, pad shall be present. 
RTX, 
RTXEN, 
RRX 
0 
- 
n/a 
Optional. Populate with 0 (< 33 if indispensa-
ble to fulfill EMC requirements otherwise), refer to 
chapter 10.8 "Interface between Communication 
Controller and Transceiver", place RTX, RTXEN 
close to the CC and RRX close to the transceiver. 
Cs 
4.7nF 
  20 % 
SMD 0805 
or less 
details see chapter 10.5 “Bus Termination” 
CMC 
chapter 10.4 
n/a 
n/a 
details see chapter 10.4 “Common Mode Chokes” 
ESD1 
do not 
populate 
n/a 
SOT23 
pads shall be present, populate only if required 
and with a written permission of Daimler available 
ESD2/3 
do not 
populate 
n/a 
SMD 0805 
or 
SMD 0603 
These pads are required for alternative ESD pro-
tection parts, populate only if required and with a 
written permission of Daimler available. The max-
imum value of the parasitic capacitance shall be 
Cmax=17pF. The matching deviation of parasitic 
capacitance shall be lower than 2%. 
 
Table T10.6.1a: component details for basic circuitry 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 45 页
MSS 20206, V2.0, 2018-08, page 45 
Copyright Daimler AG 2018 
10.6.2 Exemplary circuit diagram for ECUs with permanent power supply using 16 pin trans-
ceivers 
MSS 20206-315: 
The physical layer circuitry given in Figure F10.6.2a represents the physical layer 
configuration of a FlexRay node ECU that allows wake-up and sleep functionality as 
well as error diagnosis of the transceiver. The wake pin may be used optionally if 
required in the ECU. In that case the connection of the wake pin shall be realized 
according to the requirements given within the transceiver data sheet or application 
note. 
 
MSS 20206-316: 
 
1
16
2
15
3
14
4
13
5
12
6
11
7
10
8
9
INH
INH
Vcc
Vbat
EN
Vio
TXD
TXEN
RXD
BGE
STBN
RXEN
ERRN
VBAT
WAKE
GND
BM
BP
Vcc
16 pin transceiver
communication controller /
host controller
I/O Port
VccµC
I/O Port
(7)
INT / I/O Port
TX_A
RX_A
TXEN_A
CMC
R6
R9
R8
R7
C4
C2
C1
ESD1
connector (1)
Details on all components see respective table
(1) Exemplary assignment, details see connector / pin definition, may include other ECU pins
(2) Diagrammed in brief, power supply details are ECU specific (regulators etc.)
(3) Wake pin can be used optionally if required in ECU.
(4) Depending on network topology the ECU will be an end-node or mid-node.
(5) If the hostcontroller has no internal pullup resistors external pullup resistors of 10k  to VccµC have to be added to the control pins.
Ω
(6) Transceiver: please refer to chapter FlexRay transceivers
(7) T
. The wake up event is signalled at the RxEN 
he connection to an interrupt pin of the µC might be necessary to recognize the transceiver wake up reliably
     pin if available and also at the ERRN pin in case transceivers are used that are compliant to ISO 17458-4 or 
 V
B or more recent.
EPL
2.1 Rev.
termination (4)
(end / in-between node) ESD protection
Vbat
Vbat
5V
3.3V
Vcc
VccµC
Vbat
ECU power supply (2)
INH
INH
supply 12V
inverse-polary
protection diode
VccµC
VccµC
BP
BP
BM
BM
HOLD
HOLD
wake (3)
C3
(5)
08 / 2015
R1
R3
R5
R2
ESD2
R4
ESD3
C5
SSOP16
(6)
 
 
Figure F10.6.2a: exemplary physical layer circuitry for ECU swith permanent power 
supply 
 
MSS 20206-317: 
Refer also to chapter 10.7 "General ECU layout requirements" and chapter 10.8 
"Interface between Communication Controller and Transceiver". 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 46 页
MSS 20206, V2.0, 2018-08, page 46 
Copyright Daimler AG 2018 
MSS 20206-318: 
 
Part 
Value 
Tolerance 
Dimension 
Remark 
transceiver chapter 10.3 
 
 
FlexRay Transceiver. Use ERRN to indicate the 
wake-up to the µC. 
R1, R2, 
R3, R4 
25.5  or 
649  
  1 % 
SMD 0805 
Details see chapter 10.5 “Bus Termination”, 
value depends on chapter 11 “ECU classification” 
R5 
- 
- 
SMD 0805 
Details see chapter 10.5 “Bus Termination”. 
Do not populate, pad shall be present. 
R6 
0  
- 
n/a 
If the wake pin is not used connect with 0  to 
GND. Otherwise the wake pin shall be connected 
according to the requirements given within the 
transceiver data sheet or application note. 
R7, R8, 
R9 
0  
- 
n/a 
Optional. Populate with 0  (< 33  if indispen-
sable to fulfill EMC requirements otherwise), refer 
to chapter 10.8 "Interface between Communica-
tion Controller and Transceiver", place R7, R8 
close to the CC and R9 close to the transceiver. 
C1 
100nF 
(10nF) 
  10 % 
n/a 
connect capacitor close to transceiver pin and the 
corresponding ground 
C2 
100nF 
  10 % 
n/a 
connect capacitor close to transceiver pin and the 
corresponding ground 
C3 
100nF 
(10nF) 
  10 % 
n/a 
connect capacitor close to transceiver pin and the 
corresponding ground 
C4 
470pF 
  10 % 
n/a 
required in usage with some former NXP trans-
ceivers (TJA1081TS), alternatively 440pF 
C5 
4.7nF 
  20 % 
SMD 0805 
or less 
details see chapter 10.5 “Bus Termination” 
CMC 
chapter 10.4 
n/a 
n/a 
details see chapter 10.4 “Common Mode Chokes” 
ESD1 
do not 
populate 
n/a 
SOT23 
pads shall be present, populate only if required 
and with a written permission of Daimler available 
ESD2/3 
do not 
populate 
n/a 
SMD 0805 
or 
SMD 0603 
These pads are required for alternative ESD pro-
tection parts, populate only if required and with a 
written permission of Daimler available. The max-
imum value of the parasitic capacitance shall be 
Cmax=17pF. The matching deviation of parasitic 
capacitance shall be lower than 2%. 
 
Table T10.6.2a: component details for ECUs with permanent power supply 
 
MSS 20206-319: 
Please refer also to the corresponding application hints and datasheets of the 
transceiver manufacturer and to [ISO 17458-4] or [FlexRay Electrical Physical Layer 
Application Notes V3.0.1]. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 47 页
MSS 20206, V2.0, 2018-08, page 47 
Copyright Daimler AG 2018 
10.6.3 Exemplary circuit diagram for ECUs with switched power supply using 16 pin trans-
ceivers 
MSS 20206-321: 
The physical layer circuitry given in Figure F10.6.3a represents the physical layer 
configuration of a FlexRay node ECU with fundamental functionality. Wake-up and 
sleep functionality as well as error diagnosis of the transceiver are not supported by 
this circuitry. If error diagnosis of the transceiver is requested it can be implemented 
by connecting EN, STBN and ERRN to the host controller (see also the circuitry 
described in the previous chapter). 
MSS 20206-322: 
 
1
16
2
15
3
14
4
13
5
12
6
11
7
10
8
9
INH
Vcc
Vbat
EN
Vio
TXD
TXEN
RXD
BGE
STBN
RXEN
ERRN
VBAT
WAKE
GND
BM
BP
Vcc
VccµC
TX_A
RX_A
TXEN_A
n.c.
R11
R10
C3
C2
C1
Details on all components see respective table
(1) Exemplary assignment, details see connector / pin definition may include other ECU pins
(2) Diagrammed in brief, power supply details are ECU specific (regulators etc.)
(3) Depending on network topology the ECU will be an end-node or in-between node
4 T
please refer to chapter „FlexRay Transceivers“
( ) ransceiver: 
Vbat
Vbat
5V
3.3V
Vcc
VccµC
Vbat
ECU power supply (2)
supply 12V
inverse-polary
protection diode
VccµC
VccµC
CMC
R6
R9
R7
connector (1)
termination (3)
(end / in-between node) ESD protection
BP
BP
BM
BM
08 / 2015
R8
R1
R3
R5
R2
R4
C4
16 pin transceiver
communication controller /
host controller
(4)
SSOP16
ESD1
ESD2
ESD3
n.c.
n.c.
 
 
Figure F10.6.3a: exemplary physical layer circuitry for ECUs with switched power 
supply using 16 pin transceivers 
 
 
MSS 20206-323: 
Refer also to chapter 10.7 "General ECU layout requirements" and chapter 10.8 
"Interface between Communication Controller and Transceiver". 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 48 页
MSS 20206, V2.0, 2018-08, page 48 
Copyright Daimler AG 2018 
MSS 20206-324: 
 
Part 
Value 
Tolerance 
Dimension 
Remark 
transceiver 
chapter 10.3 
 
 
FlexRay Transceiver 
R1, R2, 
R3, R4 
25.5  or 
649  
  1 % 
SMD 0805 
Details see chapter 10.5 “Bus Termination”, 
value depends on chapter 11 “ECU classification”  
R5 
- 
- 
SMD 0805 
Details see chapter 10.5 “Bus Termination”. 
Do not populate, pad shall be present. 
R6 
0  
- 
n/a 
connect with 0  to GND 
R7, R8, 
R9 
0  
- 
n/a 
Optional. Populate with 0  (< 33  if indispensa-
ble to fulfill EMC requirements otherwise), refer to 
chapter 10.8 "Interface between Communication 
Controller and Transceiver", place R7, R8 close to 
the CC and R9 close to the transceiver. 
R10 
10k 
  5 % 
SMD 0805 
if only normal mode is required (take care of 
initial controller values at TxD, TxEN) 
R11 
10k 
  5 % 
SMD 0805 
if only normal mode is required (take care of 
initial controller values at TxD, TxEN) 
C1 
100nF 
(10nF) 
  10 % 
n/a 
connect capacitor close to transceiver pin and the 
corresponding ground 
C2 
100nF 
  10 % 
n/a 
connect capacitor close to transceiver pin and the 
corresponding ground 
C3 
100nF 
(10nF) 
  10 % 
n/a 
connect capacitor close to transceiver pin and the 
corresponding ground 
C4 
4.7nF 
  20 % 
SMD 0805 
or less 
details see chapter 10.5 “Bus Termination” 
CMC 
chapter 10.4 
n/a 
n/a 
details see chapter 10.4 “Common Mode Chokes” 
ESD1 
do not popu-
late 
n/a 
SOT23 
pads shall be present, populate only if required 
and with a written permission of Daimler available 
ESD2/3 
do not popu-
late 
n/a 
SMD 0805 
or 
SMD 0603 
These pads are required for alternative ESD pro-
tection parts, populate only if required and with a 
written permission of Daimler available. The maxi-
mum value of the parasitic capacitance shall be 
Cmax=17pF. The matching deviation of parasitic 
capacitance shall be lower than 2%. 
 
Table T10.6.3a: component details for ECUs with switched power supply using 16 
pin transceivers 
 
MSS 20206-325: 
Please refer also to the corresponding application hints and datasheets of the 
transceiver manufacturer and to [ISO 17458-4] or [FlexRay Electrical Physical Layer 
Application Notes V3.0.1]. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 49 页
MSS 20206, V2.0, 2018-08, page 49 
Copyright Daimler AG 2018 
10.6.4 Exemplary circuit diagram for ECUs using 14 pin transceivers 
MSS 20206-327: 
The physical layer circuitry given in Figure F10.6.4a represents the physical layer 
configuration of a FlexRay node ECU that allows standby functionality as well as 
error diagnosis of the transceiver. 14 pin transceivers do not have the functionality to 
switch directly the voltage regulators on or off. 
MSS 20206-328: 
 
1
14
2
13
3
12
4
11
5
10
6
9
7
8
Vio
TXD
Vcc
TXEN
RXD
BGE
STBN/SCSN
SCLK
SDO
SCSN/SDI
ERRN
GND
VccµC
TX_A
RX_A
TXEN_A
C2
C1
Details on all components see respective table
(1) Exemplary assignment, details see connector / pin definition may include other ECU pins
(2) Diagrammed in brief, power supply details are ECU specific (regulators etc.)
(3) Depending on network topology the ECU will be an end-node or in-between node
(4) If the hostcontroller has no internal pullup resistors external pullup resistors of 10k  to VccµC have to be added to the transceiver control pins
Ω
(5) NXP, Infineon: STBN / ELMOS SCSN
(6) NXP, Infineon: SCSN / ELMOS SDI
Vbat
Vbat
5V
3.3V
Vcc
VccµC
Vbat
ECU power supply (2)
supply 12V
inverse-polary
protection diode
VccµC
VccµC
(4)
CMC
R8
R6
connector (1)
termination (3)
(end / in-between node) ESD protection
BP
BP
BM
BM
10 / 2015
R7
R1
R3
R5
R2
R4
C3
14 pin transceiver
communication controller /
host controller
SSOP14
ESD1
ESD2
ESD3
INT / I/O Port
I/O Port
I/O Port
I/O Port
I/O Port
(5)
(6)
 
 
Figure F10.6.4a: exemplary physical layer circuitry using 14 pin transceivers 
 
MSS 20206-329: 
Refer also to chapter 10.7 "General ECU layout requirements" and chapter 10.8 
"Interface between Communication Controller and Transceiver". 
 
 
 
 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 50 页
MSS 20206, V2.0, 2018-08, page 50 
Copyright Daimler AG 2018 
MSS 20206-330: 
 
Part 
Value 
Tolerance 
Dimension 
Remark 
transceiver 
chapter 10.3 
 
 
FlexRay Transceiver. In case a low power mode of 
the transceiver is used: Use ERRN to indicate the 
wake-up to the µC. 
R1, R2, 
R3, R4 
25.5  or 
649  
  1 % 
SMD 0805 
Details see chapter 10.5 “Bus Termination”, 
value depends on chapter 11 “ECU classification”  
R5 
- 
- 
SMD 0805 
Details see chapter 10.5 “Bus Termination”. 
Do not populate, pad shall be present. 
R6, R7, 
R8 
0  
- 
n/a 
Optional. Populate with 0  (< 33  if indispensa-
ble to fulfill EMC requirements otherwise), refer to 
chapter 10.8 "Interface between Communication 
Controller and Transceiver", place R6, R7 close to 
the CC and R8 close to the transceiver. 
C1 
100nF 
  10 % 
n/a 
connect capacitor close to transceiver pin and the 
corresponding ground 
C2 
100nF 
  10 % 
n/a 
connect capacitor close to transceiver pin and the 
corresponding ground 
C3 
4.7nF 
  20 % 
SMD 0805 
or less 
details see chapter 10.5 “Bus Termination” 
CMC 
chapter 10.4 
n/a 
n/a 
details see chapter 10.4 “Common Mode Chokes” 
ESD1 
do not popu-
late 
n/a 
SOT23 
pads shall be present, populate only if required 
and with a written permission of Daimler available 
ESD2/3 
do not popu-
late 
n/a 
SMD 0805 
or 
SMD 0603 
These pads are required for alternative ESD pro-
tection parts, populate only if required and with a 
written permission of Daimler available. The maxi-
mum value of the parasitic capacitance shall be 
Cmax=17pF. The matching deviation of parasitic 
capacitance shall be lower than 2%. 
 
Table T10.6.4a: component details for ECUs using 14 pin transceivers 
 
MSS 20206-331: 
Please refer also to the corresponding application hints and datasheets of the 
transceiver manufacturer and to [ISO 17458-4] or [FlexRay Electrical Physical Layer 
Application Notes V3.0.1]. 
10.6.5 Exemplary circuit diagram for active star ECUs using integrated active star devices 
MSS 20206-333: 
The circuit shown in Figure F10.6.5a shows an ECU based on integrated active star 
devices. Using one integrated active star device the circuitry is a monolithic active 
star implementation with four branches. It can be extended to eight branches if two 
integrated active star devices are interconnected by means of the intra star interface, 
as shown in the lower dashed area in Figure F10.6.5a. Please note that the pull-up 
resistors on the internal TRXD0 and TRXD1 bus lines are device and layout 
dependent. The values given here are typical and should be adjusted for a specific 
ECU layout. 
MSS 20206-334: 
The layout shall be designed to enable the usage of star devices by at least two 
different semiconductor manufacturers (second source) as shown in the circuitry 
example below. This makes it possible to change to another star device if necessary. 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 51 页
MSS 20206, V2.0, 2018-08, page 51 
Copyright Daimler AG 2018 
MSS 20206-335: 
 
I/O Port
INT / I/O
INT / I/O
I/O Port
I/O Port
I/O Port
VccµC
I/O Port
I/O Port
TX_A
RX_A
TXEN_A
R1
R3
R1
R3
R2
R4
R1
R3
R1
R3
C27
C28
connector (1)
Vbat
Vbat
5V
3.3V
Vcc
Vcc
Vcc
Vcc
Vcc
Vbat
Vbat
VccµC
VccµC
VccµC
VccµC
Vbat
ECU power supply
INH
INH
supply 12V
inverse-polary
protection diode
BP
BP
BM
BM
HOLD
HOLD
33
33
32
32
31
31
30
30
29
29
28
28
27
27
26
26
25
25
24
24
23
23
BP1
BP1
n.c.
n.c.
n.c.
BP2
BP2
BM2
BM2
BP3
BP3
BM3
BM3
n.c.
n.c.
BP4
BP4
BM4
BM4
BM1
BM1
22
22
34
34
Vcc2
Vcc2
Vcc1
Vcc1
21
21
35
35
Vbuf2
Vbuf2
Vbuf1
Vbuf1
20
20
36
36
INH2/n.c.
INH2/n.c.
n.c.
n.c.
19
19
37
37
gnd2
gnd2
gnd1
gnd1
active star device
QFN44 9x9
active star device
QFN44 9x9
18
18
38
38
V33/n.c.
V33/n.c.
n.c.
n.c.
17
17
39
39
Vbat
Vbat
n.c.
n.c.
16
16
40
40
LWU
LWU
n.c.
n.c.
15
15
41
41
INH
INH
n.c.
n.c.
14
14
42
42
n.c.
n.c.
13
13
43
43
TRXD1
TRXD1
CFG
CFG
GNDD
GNDD
12
12
44
44
SCSN
SCSN
INTN
INTN
TXEN
TXEN
TRXD0
TRXD0
RSTN
RSTN
SCK
SCK
SDO
SDO
VIO
VIO
BGE
BGE
TXD
TXD
RXD
RXD
SDI
SDI
10
10
1
1
2
2
3
3
4
4
5
5
6
6
7
7
8
8
9
9
11
11
C11
C24
C12
C26
C13
C25
C6
C20
C9
C22
C2
C15
C8
C29
C30
C23
C1
C14
C4
C18
C7
C19
C10
C21
C3
C16
C5
C17
R7
R11
R13
R14
R9
R8
R34
R35
R23
R12
R24
R26
R25
R28
R27
R29
R30
R6
R10
termination circuitry identical for other branches
termination circuitry identical for other branches
optional aditional 4 branches
with second monolithic active star device
CMC
CMC
die pad
die pad
exemplary for a
3.3V device
exemplary for a
3.3V device
R17
R18
R20
R22
R33
R31
R32
R19
R21
R16
R15
06 / 2016
communication controller /
host controller
ESD1
ESD2
ESD3
ESD1
ESD2
ESD3
n.c./RxEN
n.c./RxEN
dig gnd/n.c.
dig gnd/n.c.
route TRXD transmission lines
on PCB strictly parallel and symmetrical
INH
INH
 
 
Figure F10.6.5a: exemplary physical layer circuitry using integrated active star 
devices 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 52 页
MSS 20206, V2.0, 2018-08, page 52 
Copyright Daimler AG 2018 
MSS 20206-336: 
Refer also to chapter 10.7 "General ECU layout requirements" and chapter 10.8 
"Interface between Communication Controller and Transceiver". 
 
MSS 20206-337: 
 
Part 
Value 
(E981.56) 
Tolerance 
Dimension 
Remark /  
Values for alternative transceivers 
active star 
device 
(transceiver) 
chapter 10.3 
n/a 
 
it is not allowed to combine integrated star de-
vices or transceivers of different manufacturers 
R1, R3* 
25.5 
  1 % 
SMD 0805 
see chapter 10.5.4 “Termination of Active Star 
ECUs” 
R2, R4* 
do not 
populate 
n/a 
SMD 0805 
see chapter 10.5.4 “Termination of Active Star 
ECUs” 
R9* 
0 
n/a 

E981.56 : R9 can be populated with 0 in case 
of TxD of this E981.56 is not used 
R6, R7 
220 
  1 % 
 
if only one monolithic implementation of an active 
star is used / 
AS8223: do not populate 
TJA1085: 930 
R6, R7, 
R10*, R11* 
470 
  1 % 
 
if two integrated (monolithic) active star devices 
are banked / 
AS8223: do not populate 
TJA1085: 930 
R8, R12* 
1M 
  5 % 
 
AS8223: 1M 
TJA1085: 1M 
R13, R14* 
 10k 
n/a 
 
AS8223: = 6.2k  
TJA1085: = 10k 
R15, R16, 
R17 
0 
n/a 
 
Optional. Populate with 0(< 33 if indispensa-
ble to fulfill EMC requirements otherwise), refer 
to chapter 10.8 "Interface between Communica-
tion Controller and Transceiver", place R15, R16 
close to the CC and R17 close to the transceiv-
er./ 
AS8223: 0  
TJA1085: 0 
R18, R20* 
do not 
populate 
n/a 
 
pads shall be present / 
AS8223: 0  
TJA1085: 0 
R19, R21* 
0 
n/a 
 
pads shall be present /  
AS8223: do not populate 
TJA1085: do not populate 
R22, R33* 
0
n/a 
 
pads shall be present / 
AS8223: do not populate 
TJA1085: 0 
R23, R24*, 
R25, R26*, 
R27, R28, 
R29*, R30*, 
R34, R35* 
0
n/a 
 
pads shall be present / 
AS8223: do not populate 
TJA1085: 0 
R31, R32* 
do not 
populate
n/a 
 
pads shall be present / 
AS8223: 0 
TJA1085: do not populate
 
Table T10.6.5a part1: component details for physical layer circuitry using integrated 
active star devices 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 53 页
MSS 20206, V2.0, 2018-08, page 53 
Copyright Daimler AG 2018 
MSS 20206-338: 
 
Part 
Value 
Tolerance 
Dimension 
Remark 
C1, C8, 
C14*,C23* 
470pF 
  10 % 

AS8223: do not populate 
TJA1085: 100nF 
C2, C9, 
C15*,C22* 
47µF 
n/a 
 
AS8223: 1µF 
TJA1085: 33µF 
C3, C10, 
C16*,C21* 
47µF 
n/a 
 
AS8223: do not populate 
TJA1085: do not populate 
C4, C6, 
C18*,C20* 
470pF 
  10 % 
 
 
C5, C7, 
C17*,C19* 
1.0µF 
n/a 
 
dependent on voltage regulator 
C11, C24* 
470nF 
  10 % 
 
 
C12, C25* 
470pF 
  10 % 
 
 
C13, C26* 
1.0µF 
n/a 
 
dependent on voltage regulator 
C27, C28* 
4.7nF 
  20 % 
SMD 0805 
or less 
See chapter 10.5.4 “Termination of Active Star 
ECUs” 
C29, C30* 
do not 
populate 
  10 % 
 
ceramic multilayer capacitor 
AS8223: 1µF 
TJA1085: do not populate 
CMC 
chapter 10.4 
n/a 
 
details see chapter 10.4 
“Common Mode Chokes” 
ESD1 
do not 
populate 
n/a 
SOT23 
pads shall be present, populate only if required 
and with a written permission of Daimler available 
ESD2/3 
do not 
populate 
n/a 
SMD 0805 
or 
SMD 0603 
These pads are required for alternative ESD pro-
tection parts, populate only if required and with a 
written permission of Daimler available. The max-
imum value of the parasitic capacitance shall be 
Cmax=17pF. The matching deviation of parasitic 
capacitance shall be lower than 2%. 
 
Table T10.6.5a part2: component details for physical layer circuitry using integrated 
active star devices 
 
MSS 20206-339: 
◊ Component size may be chosen by supplier (e.g. 0805, 0603, 0402 etc.). 
MSS 20206-340: 
* All parts marked with a star (*) are to be omitted if only one integrated active star 
device is used (monolithic active star, lower dashed area is omitted in Figure 
F10.6.5a). 
Please refer to the related application notes for more details. 
MSS 20206-341: 
#) These capacitance values presuppose the following requirements concerning 
maximum switch-on delay of the supply voltage: 
 
E981.56: The power supply unit of the device with integrated active star shall provide 
a stable supply voltage (Vcc) within < 600ms after INH signal was switched by the 
active star device. Slower power supply units are not allowed. 
 
AS8223: The power supply unit of the device with integrated active star shall provide 
a stable supply voltage (Vcc) within < 100ms after INH signal was switched by the 
active star device. Slower power supply units are not allowed. 
 
TJA1085: The power supply unit of the device with integrated active star shall 
provide a stable supply voltage (Vcc) within < 100ms after INH signal was switched 
by the active star device. In case a slower power supply unit is used, the 
capacitance on C2, C9, C15*, C22* has to be increased according to the active star 
manufacturer's application notes. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 54 页
MSS 20206, V2.0, 2018-08, page 54 
Copyright Daimler AG 2018 
MSS 20206-342: 
Please refer also to the corresponding application hints and datasheets of the active 
star device manufacturer and to [ISO 17458-4] or [FlexRay Electrical Physical Layer 
Application Notes V3.0.1] (e.g. chapter 2.17 Application Note for further information 
regarding wake up reaction and power supply design). 
 
10.7 General ECU layout requirements 
MSS 20206-344: 
General layout requirements concerning the FlexRay physical layer components on 
an ECU PCB are given in Figure F10.7a. A recommendation of an exemplary 
schematic ground concept for a FlexRay ECU is given in Figure F10.7b. 
MSS 20206-345: 
PCB: 
MSS 20206-346: 
-  A multilayer PCB with at least 4 layers is recommended for FlexRay ECUs. 
 
MSS 20206-347: 
Component placement and signal line routing on PCB: 
MSS 20206-348: 
-  Place the following parts close to the FlexRay pins of the ECU connector in the 
following sequence: 
connector - ESD protection pads – termination circuitry – common mode choke – 
transceiver - controller. 
MSS 20206-349: 
-  Termination circuitry, transceiver and controller shall be placed close together (< 
50mm) to maintain the required EMC and ESD performance see figure F10.7a. 
MSS 20206-350: 
-  Avoid long transmission lines for sensitive FlexRay signals (e.g. TXD, RXD, TXEN, 
BP and BM) on the PCB. See corresponding chapters (e.g. chapter 10.8 "Interface 
between Communication Controller and Transceiver" ). Furthermore avoid stub lines 
for these signals. 
MSS 20206-351: 
-  The FlexRay signal lines BP and BM shall be decoupled from disturbances on the 
ECU board. 
MSS 20206-352: 
-  BP and BM shall be routed closely parallel to each other. Always maintain 
geometrical symmetry between BP and BM transmission lines and components 
connected to BP and BM (e.g. termination circuitry). BP and BM signals shall be 
conducted by means of impedance matched transmission lines on the PCB (i.e. 
100Ω differential mode characteristic impedance, low common mode characteristic 
impedance). 
MSS 20206-353: 
-  In case of an in-between node ECU interconnect both FlexRay signal lines right in 
front of the termination-circuitry and conduct them with separate transmission lines 
up to this point. 
MSS 20206-354: 
-  The routing of the FlexRay lines TX and TXEN shall be symmetric with minimized 
distance between them. 
 
MSS 20206-355: 
-  Avoid using wire straps, jumpers, plug-and-socket connections to conduct 
sensitive FlexRay signals (e.g. TXD, RXD, TXEN, BP, BM, BGE) on the PCB.  
MSS 20206-356: 
-  Please refer to chapter 12.3.2 "Pin Allocation within the Connector" concerning 
allocation of the FlexRay pins. 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 55 页
MSS 20206, V2.0, 2018-08, page 55 
Copyright Daimler AG 2018 
MSS 20206-357: 
 
connector
1
16
2
15
3
14
4
13
5
12
6
11
7
10
8
9
BM
BP
transceiver
BP2
BP1
BM2
BM1
gnd
transceiver circuitry
termination circuitry
cc / µc with interface
interconnection between termination
and connector on PCB-board
separate transmission lines
for BP1, BM1 and BP2, BM2
connect both lines directly
at the termination circuitry
place termination circuitry
directly in front of the CMC
keep transmission lines on PCB short!
keep transmission lines of
cc / transceiver interface short
avoid reflections or ringing
connect common mode termination
with low impedance to ground cable
place transceiver and termination circuitry
close to the connector
always maintain symmetry between 
BP and BM signal
a multilayer PCB with at least 4 layers is recommended (signal1 - ground - Vcc - signal2)
ground connection
to chassis
Z =100
DM
Ω
Z =100
DM
Ω
CC / µC
CMC
exemplary
termination circuitry
08/2015
RTx
RRx
RTxEN
!
!
!
!
!
 
 
Figure F10.7a: layout recommendations for ECU design  
(exemplarily for an in-between node) 
 
MSS 20206-358: 
An ECU ground concept recommendation for a FlexRay ECU is given in Figure 
F10.7b.  
It is intended to point out the principle of ECU ground design and should be adopted 
to the requirements of any specific ECU. 
MSS 20206-359: 
-  Use a concept of separate ground areas on a PCB to minimize mutual interference 
of the functional units within the ECU exemplarily given in Figure F10.7b. 
MSS 20206-360: 
-  Connect the common mode grounding of termination circuitries with low 
impedance and short lines directly to the ground cable pin of the ECU supply. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 56 页
MSS 20206, V2.0, 2018-08, page 56 
Copyright Daimler AG 2018 
MSS 20206-361: 
 
U1
U2
mains filter
power supply
bus driver section
analogue section
vehicle chassis
ground bolt
cable harness
FlexRay ECU
interface ground
transceiver
digital bus signals
(RXD, TXD, TXEN)
digital signals
analogue signals
ADC
i/o filter
termination & ESD protection
Vbat
Vcc
bus line
sheathed cable
main power supply
Vbat
ground cable
ground cable
sensitive digital circuitry
power supply / drivers / 
converters
i/o filtering
termination / ESD protection
interface
ground
connector
µC / CC section
digital circuitry
Vµc / Vcc
digital ground plane
central ground point
µC / CC
BP
BM
ECU housing
CMC
08 / 2015
CMC
 
 
Figure F10.7b: exemplary schematic ground concept for FlexRay ECU 
 
10.8 Interface between Communication Controller and Transceiver 
MSS 20206-363: 
The FlexRay interface between communication controller and transceiver (i.e. RXD, 
TXD and TXEN) conducts time sensitive signals. Distortions of these signals can 
have a negative impact on the timing properties of the FlexRay network and the 
EMC of the ECU. Thus the following design rules shall be regarded: 
MSS 20206-364: 
-  Avoid long transmission lines for sensitive FlexRay signals on the PCB. 
MSS 20206-365: 
Hint: In this context a transmission line is to be considered long if its parasitic 
capacitive and inductive components cause relevant changes of the data signal 
shape such as ringing, overshooting or changes in rise- and fall times of signal 
slopes. The maximum applicable line length is dependent on the PCB substrate 
used and the transmission line geometry. 
MSS 20206-366: 
-  If long transmission lines are inevitable (e.g. due to other eminent requirements on 
the ECU layout) the impedance of the PCB line shall be chosen suitable to the used 
CC and BD. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 57 页
MSS 20206, V2.0, 2018-08, page 57 
Copyright Daimler AG 2018 
MSS 20206-367: 
-  It is not allowed to connect any additional components (e.g. capacitors, ferrite 
beads, logic circuitry, decoupling devices etc.) to the RXD, TXD and TXEN 
interconnection lines e.g. as an EMC measure, signal level shifting or signal 
amplification. 
MSS 20206-368: 
In order to optimize EMC and the asymmetric delay parameters the permitted range 
of resistors between CC and transceiver is 0Ω to 33Ω  (circuitry: refer to chapter 10.6 
"Circuit Diagrams"). Please refer to [ISO 17458-4] - chapter A.2.23. The influence of 
these resistors on the asymmetric delay shall be verified by simulation and 
measurement. 
MSS 20206-369: 
-  To guarantee the reqirements regarding asymmetric delay and propagation delay 
simulations and measurements at the BD / CC interface of the TxD, TxEN and RxD 
signals shall be performed by the ECU supplier and the results shall be delivered to 
Vehicle Networking Group on request. 
Details about the requested values of the TxD and TxEN signals are described in 
[ISO 17458-4] - chapter 14 "Interface definitions". 
 
MSS 20206-370: 
TxD shall be investigated at TP1_BD: 
TxD signal sum of rise and fall time at TP1_BD shall be lower than 9ns according 
[ISO 17458-4] - Table 100 and Figure 65. 
TxD signal asymmetric delay at TP1_BD shall be lower than 2.45ns according [ISO 
17458-4] - Table 99 and Figure 65. This value does not include the asymmetry due 
to the oscillator tolerance (causing additional asymmetry of max. 0.02 * number of 
bits of the regarded time interval), but it does include jitter caused by the oscillator. 
 
MSS 20206-371: 
TxEN shall be investigated at TP1_BD: 
Rise time and fall time of TxEN at TP1_BD shall be lower than 9ns each according 
[ISO 17458-4] - Table 98. 
Details about the requested values of the RxD signals are described in [ISO 17458-
4] - chapter 12.9.6 "Receiver timing characteristics". 
 
MSS 20206-372: 
RxD shall be investigated at TP4_CC: 
The input signal at the transceiver bus pins is described in [ISO 17458-4] - Table 51 
and Figure 43. 
The requested maximum transceiver delay of the receive path can be found in  [ISO 
17458-4] - Table 52 (<75ns each) as well as the max. asymmetric delay (< 5ns). 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 58 页
MSS 20206, V2.0, 2018-08, page 58 
Copyright Daimler AG 2018 
11 ECU Classification 
MSS 20206-374: 
The following table T11a specifies the required number of FlexRay bus pins of a 
component and assigns the requested bus termination. 
MSS 20206-375: 
 
ECU 
classification 
(see Table T11b) 
number of bus pins 
(BP, BM) 
comment 
CPC 
universal node 
6 
 
DRVU 
universal node 
6 
 
EBB 
end node 
2 or 4 1) 
2 connected bus pins 1) 
EIS 
Active star with n=8 branches 
and variant with n=4 branch-
es. All branches shall be 
implemented with end node 
termination with the excep-
tion of one branch (no. 4) at 
the active star device which 
is connected to the EIS con-
troller; this one shall be im-
plemented with universal 
node termination 
2 per branch at (n-1) 
branches, 
6 at one branch no. 4 
 
EPS 
end node 
2 or 4 1) 
2 connected bus pins 1) 
ESP 
in-between node and hard-
ware variant as end node 
4 1) 
 
FOWD / AWD 
universal node 
6 
 
MPC 
end node 
2 or 4 1) 
2 connected bus pins 1), 
refer to chapter 12:  12.1 / 
12.3 and RTX, RTXEN, RRX in 
chapter 10.6.1 
MRR 
end node 
2 or 4 1) 
2 connected bus pins 1) 
ORC 
in-between node 
4 1) 
end node termination has 
to be a possible option 
PARK 
universal node 
6 
 
RAS 
end node 
2 or 4 1) 
2 connected bus pins 1) 
RAS_L / RAS _R 
universal node 
6 
 
SCCM 
in-between node 
4 
 
SDTR / FCW 
end node 
2 or 4 1) 
2 connected bus pins 1) 
SPC 
universal node 
6 
 
SPC_Vx 
universal node 
6 
 
V2X (V2V / C2X) 
universal node 
6 
 
Any other ECU 
than listed above 
universal node 
6 
 
 
1) An OEM wide usage of FlexRay ECUs requires 4 FlexRay pins whereas at end nodes only 2 pins will be 
connected. In this case the two unused pins should be separated by unmounted 0Ω resistors close to the 
termination in order to avoid stub line resonance and electromagnetic susceptibility. 
Table T11a: ECU classification 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 59 页
MSS 20206, V2.0, 2018-08, page 59 
Copyright Daimler AG 2018 
MSS 20206-376: 
Classification types: 
MSS 20206-377: 
 
classification 
bus termination 
end node 
chapter 10.5.2 
in-between node 
chapter 10.5.3 
universal node 
chapter 10.5.5 
active star 
chapter 10.5.4 
 
Table T11b: Classification types 
 
MSS 20206-378: 
Please find circuitry diagrams in chapter 10.6 "Circuit Diagrams". 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 60 页
MSS 20206, V2.0, 2018-08, page 60 
Copyright Daimler AG 2018 
12 Wiring and Connectors 
12.1 FlexRay Cables 
12.1.1 Cable Requirements 
MSS 20206-382: 
FlexRay networks shall be implemented with (unshielded) sheathed twisted pair 
cables or (unshielded) unsheathed twisted pair cables. 
MSS 20206-383: 
In dry installation space (e.g. inside the car body) both cable types can be used 
(sheathed or unsheathed). 
MSS 20206-384: 
In humid or wet installation space (e.g. engine compartment, outside the car body) 
only sheathed cables are allowed. 
MSS 20206-385: 
Furthermore shielded cables might be necessary for EMC sensitive applications, e.g. 
FlexRay transmission lines close to vehicle antennas. 
Depending on the connector system shielded twisted pair cables with stranded filter 
wire (unshielded connectors, low frequency range only) or completely shielded 
cable- and connector systems such as Rosenberger / Leoni HSD-System using 
shielded twisted pair or shielded star quad cable can be used (shielded connectors, 
reduction of emissions in VHF / UHF range). Please refer also to chapter 12.3.3 
"Connecting Circuitry for Cable Shields". 
MSS 20206-386: 
Shielded cables without galvanic shield connection (floating shield) are not allowed. 
12.1.2 Approved Cables 
MSS 20206-388: 
 
Cable Type – CAD number H35/(1..N) 
FLR09YS-YW 2x0.35 mm²-SN 
(unshielded sheathed twisted pair) 
FLR2X 2x0.35 mm² 
(unshielded unsheathed twisted pair) 
FLR9Y 2x0.35 mm²-SN 
(unshielded unsheathed twisted pair) 
FL21X11Y 2x0.35 mm²-SN 
(unshielded sheathed twisted pair) 
FL91X 91X 2x0.35 mm²-SN-A 
(unshielded sheathed twisted pair) 
 
Table T12.1.2a: Approved FlexRay cables 
 
MSS 20206-389: 
Other cable types might only be used on request. 
12.2 Cable and Connector Assembly 
MSS 20206-391: 
The assembly of ECU connector and cable is shown in Figure F12.2a, an inline 
connector assembly is shown in Figure F12.2b. The untwisted cable segment has to 
be regarded electrically as part of the connector and thus influences the connector’s 
characteristics. 
MSS 20206-392: 
In order to meet signal integrity and EMC requirements the following dimensions of 
the cable assembly are required (for both cases: ECU and inline connector): 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 61 页
MSS 20206, V2.0, 2018-08, page 61 
Copyright Daimler AG 2018 
MSS 20206-393: 
 
type of cable 
lmax, untwisted, unsheathed 
sheathed and unshielded cable 
 40mm 
sheathed and shielded cable 
 20mm 
twisted and unsheathed cable 
 40mm 
 
Table T12.2a 
 
MSS 20206-394: 
 
BP
BP
BP
BM
BM
BM
shield
busline BM
cable shield (stranded filter wire)
busline BP
connector
untwisted and unsheathed
cable segment
twisted but unsheathed
cable segment
    sheathed cable:  twisted and sheathed cable segment
unsheathed cable:  twisted cable segment
sheathed cable
twisted and unsheathed cable
sheathed and shielded cable
lmax, untwisted, unsheathed
pin connector socket
PCB
PCB
crimped
female connector
male connectors
(pins)
contact
09 / 2010
 
 
Figure F12.2a: pin assembly of ECU connector and FlexRay cable, green (BP), pink 
(BM) 
 
MSS 20206-395: 
 
BP
BP
BP
BM
BM
BM
shield
busline BM
cable shield (stranded filter wire)
busline BP
inline connector
untwisted and unsheathed
cable segment
untwisted and unsheathed
cable segment
twisted but unsheathed
cable segment
twisted but unsheathed
cable segment
twisted / sheathed
cable segment
twisted / sheathed
cable segment
sheathed cable
twisted and unsheathed cable
sheathed and shielded cable
lmax, untwisted, unsheathed
lmax, untwisted, unsheathed
pin connector socket
female connector
male connector
contact end
09 / 2010
 
 
Figure F12.2b: pin assembly of FlexRay inline connector and cable 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 62 页
MSS 20206, V2.0, 2018-08, page 62 
Copyright Daimler AG 2018 
12.3 Connectors for FlexRay 
12.3.1 Connector Requirements and Approved Connectors 
MSS 20206-398: 
The connector systems shown in Table T12.3.1a are approved to be used for 
FlexRay data communication systems if the positioning of the pins (pin allocation 
within the connector) and the assembly of the cables (attachment of the pins to the 
cable) comply with the requirements specified in chapters 12.2 and 12.3.2 to 12.3.4. 
MSS 20206-399: 
Other connector systems might be used on request only. 
MSS 20206-400: 
 
Connector Type 
MICRO QUADLOCK SYSTEM (MQS) 
2.54 x 2.54 mm centerline, matrix alignment 
2.54 mm centerline, line alignment 
NanoMQS  compatible with wire size 0.35mm² 
HSD Connector System a) (e.g. Rosenberger or comparable) 
 
a) If EME reduction is required 
 
Table T12.3.1a: approved connector system 
 
12.3.2 Pin Allocation within the Connector 
MSS 20206-402: 
FlexRay ECUs may use connectors with one or more rows of pins. In the following 
the pin allocation is specified exemplarily for a connector having two rows of multiple 
pins. The principle however is also applicable to single row connectors or multiple 
row connectors. 
MSS 20206-403: 
The possible pin allocation within one connector for ECUs in topologies with cable 
shield is shown in Figure F12.3.2a. 
MSS 20206-404: 
All three contacts needed for one FlexRay signal shall use directly adjacent pins 
within one row of pins. 
MSS 20206-405: 
There shall not be any other signal pins in-between the BP, shield_ground and BM 
pin. 
MSS 20206-406: 
This pin allocation can also be used without connecting a cable shield e.g. if the 
cable shield becomes dispensable during the development process. 
MSS 20206-407: 
The proper connection of the shield_ground to the ECU ground and the proper 
connection of the BP and BM pins to the termination and transceiver are crucial for 
EMC and signal integrity. If multiple rows are used the pins (BP1, BM1) may be 
located in line (same row) with the pins (BP2, BM2) as well as located on the 
opposite as shown in Figure F12.3.2a and Figure F12.3.2b. Please refer to chapter 
10.7 "General ECU layout requirements" for details. 
MSS 20206-408: 
The preferred pin allocation within one connector for ECUs in topologies without 
cable shield is shown in Figure F12.3.2b. 
MSS 20206-409: 
Both contacts needed for one FlexRay signal shall use directly adjacent pins. 
MSS 20206-410: 
There shall not be any other signal pins in-between BP and BM. 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 63 页
MSS 20206, V2.0, 2018-08, page 63 
Copyright Daimler AG 2018 
MSS 20206-411: 
 
BP2
BP1
BP
BP_B
BP_D
BP_A
BP_C
BM2
BM1
BM
BM_B
BM_D
BM_A
BM_C
shield_gnd2
shield_gnd1
shield_gnd
shield_gnd_B
shield_gnd_D
shield_gnd_A
shield_gnd_C
end node with shield
(one FlexRay channel)
end node with shield
(multiple FlexRay channels)
in-between node with shield
(one FlexRay channel)
BP1, BP2, shield_gnd1, 
shield_gnd2 and BM1, BM2
are interconnected inside the ECU
e.g. ECU with active star device
A, B, C, D denote connectors for separate branches of 
an active star device or separate FlexRay channels
pin used for FlexRay signal
pin used for other signal
caption:
continuation of connector
 
 
Figure F12.3.2a: pin allocation for ECUs in topologies with cable shield 
 
MSS 20206-412: 
 
BP2
BP2
BP2
BP1
BP1
BP1
BP
BP
BP_B
BP_B
BP_D
BP_D
BP_A
BP_A
BP_C
BP_C
BM2
BM2
BM2
BM1
BM1
BM1
BM
BM
BM_B
BM_B
BM_D
BM_D
BM_A
BM_A
BM_C
BM_C
pin used for FlexRay signal
pin used for other signal
end node without shield
(one FlexRay channel)
end node without shield
(multiple FlexRay channels)
in-between node without shield
(one FlexRay channel)
or
caption:
 BP1, BP2 and 
 BM1, BM2
are interconnected 
inside the ECU
BP1, BP2 and BM1, BM2
are interconnected inside the ECU
e.g. ECU with active star device 
A, B, C, D denote connectors for separate branches of 
an active star device or separate FlexRay channels
A, B, C, D denote connectors for separate branches of 
an active star device or separate FlexRay channels
alternative solution:
alternative solution:
alternative solution:
continuation of connector
08/2015
only applicable with 
uprightly mounted connectors!
only applicable with 
uprightly mounted connectors!
only applicable with 
uprightly mounted connectors!
 
Figure F12.3.2b: pin allocation for ECUs in topologies without cable shield 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 64 页
MSS 20206, V2.0, 2018-08, page 64 
Copyright Daimler AG 2018 
MSS 20206-413: 
If the connector is mounted upright onto the PCB (as shown in Figure F12.3.2c_a) 
with all pins having the same length it is possible to align the FlexRay signal pins 
vertically or horizontally within the connector matrix (see alternative solution in 
Figure F12.3.2b). 
MSS 20206-414: 
However if the connector is mounted horizontally on the PCB with pins bend by 90° 
to the PCB (as shown in Figure F12.3.2c_b) there is a length difference between the 
pins of the 1st and the 2nd row of pins. 
MSS 20206-415: 
In this case it is requested to place all FlexRay signals within the row with the 
shortest pin length (i.e. 2nd row in Figure F12.3.2c_b). In this case it is not allowed 
to use pins of different lengths for one FlexRay signal (e.g. BP 1st row and BM 2nd 
row in Figure F12.3.2c_b). This might yield in asymmetry and increase EM 
emissions. 
MSS 20206-416: 
 
PCB
pin connector
socket
pin connector
socket
1  row of pins
st
1  row of pins
st
2  row of pins
nd
2  row of pins
nd
bend
a) upright connector mounting
b) 90°-bend connector mounting
 
 
Figure F12.3.2c: upright and 90°-bend connector mounting on PCB 
 
MSS 20206-417: 
It is recommended to keep contacts that are used for FlexRay away from other 
contacts conducting fast transient or pulsed signals (e.g. PWM signals, switched 
inductive loads, etc.) within one connector. 
MSS 20206-418: 
If Rosenberger HSD connector system is used, refer to the respective Rosenberger 
application notes. The HSD connector system is a star quad configuration.  
MSS 20206-419: 
BP and BM signals shall be connected crosswise, as illustrated in figure F12.3.2d.  
MSS 20206-420: 
The remaining second pair is requested to be kept floating. Do not connect these 
unused wires to ground in order to prevent ground loops. 
MSS 20206-421: 
 
BP
BP
BM
BM
 
 
Figure F12.3.2d: exemplary pin configuration of FlexRay signals in HSD connector 
system 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 65 页
MSS 20206, V2.0, 2018-08, page 65 
Copyright Daimler AG 2018 
12.3.3 Connecting Circuitry for Cable Shields 
MSS 20206-423: 
Shielded cables might be required in case of EMC sensitive applications, e.g. 
FlexRay transmission lines close to vehicle antennas. If shielded cables are used in 
combination with unshielded connectors (e.g. EMC improvement in low frequency 
range), shielded twisted pair cables are required to have a stranded filter wire 
directly contacted to the shield inside the cable. This stranded filter wire shall be 
connected to ground (shield_ground, refer also to chapter 12.3.2 "Pin Allocation 
within the Connector") at the ECUs whereas the shield is cut off with the cable 
sheath during assembly of the connector. 
MSS 20206-424: 
Completely shielded housing-, connector- and cable systems might be required to 
improve emissions in the VHF / UHF frequency range such as the Rosenberger / 
Leoni HSD connection system (shielded star quad system). Please refer to the 
specifications and application notes of the respective cable-, connector system. 
MSS 20206-425: 
A cable shield will only provide an improvement of the electromagnetic compatibility 
of the FlexRay network when the shield is connected on both sides to ground. 
Ground connections with low impedance (i.e. short and direct wires to the chassis) 
are requested. Additionally it has to be avoided that equalizing DC currents 
originating from ground shifts within the vehicle’s main power supply will propagate 
on the cable shield. This could result in a damage of the FlexRay data cables and 
the connectors. 
MSS 20206-426: 
Thus a cable shield circuitry has to be included at one side of each cable shield, 
blocking DC currents and allowing for RF currents to propagate. 
MSS 20206-427: 
The fundamental interconnection concept of shielded FlexRay cables is shown in 
Figure F12.3.3a, Figure F12.3.3b shows the respective circuitry for interconnecting 
the cable shield within an ECU (all other lines but the cable shield are omitted). 
MSS 20206-428: 
In order to avoid charging effects of the cable it has to be ensured that each shield is 
directly grounded at one side; connecting a shield via a capacitor on both sides is 
not allowed. This principle is applicable to systems with shielded cables and 
unshielded connectors as well as to systems such as Rosenberger HSD. 
MSS 20206-429: 
 
chassis
chassis
chassis
chassis
end-node/
active star device
in-between node
in-between node
end-node
shielded cable
shielded cable
shielded cable
RF shield 
current
RF shield 
current
RF shield 
current
DC block
DC block
DC block
 
 
Figure F12.3.3a: fundamental interconnection concept of shielded FlexRay cables 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 66 页
MSS 20206, V2.0, 2018-08, page 66 
Copyright Daimler AG 2018 
MSS 20206-430: 
 
C1
C1
BP1
BP1
BP_A
BP_C
BP_B
BP_D
BP2
BM1
BM1
BM_A
BM_C
BM_B
BM_D
BM2
gnd1
gnd1
gnd_A
gnd_C
gnd_B
gnd_D
gnd2
end-node
in-between node
active star device
depending on topology
ECU interface ground
 
 
Figure F12.3.3b: interconnection circuitry for cable shields inside a FlexRay ECU 
 
MSS 20206-431: 
 
Part 
Value 
Tolerance 
Dimension 
Remark 
C1 
100nF 
  10 % 
SMD 0805 
or less 
voltage rating in accord-
ance with [VDA 320] and 
[MBN 10567] require-
ments to avoid damage 
(at least  63V required) 
 
Table T12.3.3a: DC blocking capacitor characteristics 
 
MSS 20206-432: 
Hint: When connecting the cable shield within an ECU to chassis it should be 
ensured that the RF currents propagating on the shield do not affect other 
components of the ECU, i.e. a direct low impedance connection to the 
ground/chassis cable of the ECU is recommended (interface ground). 
 
12.3.4 Shielded FlexRay interconnection in EMC sensitive areas 
MSS 20206-434: 
In EMC sensitive areas, e.g. FlexRay transmission lines close to vehicle antennas, 
shielded cables and connectors might be required in order to fulfill the EMC 
requirements. Figure F12.3.4a shows the exemplary configuration of a FlexRay ECU 
that is connected to an unshielded FlexRay cluster with active star. 
MSS 20206-435: 
Improvement of the EMC behavior will be achieved if the following measures are 
implemented: The FlexRay ECU is fully shielded. The Rosenberger HSD connector 
is connected directly and continuously to the metal ECU housing with low 
interconnection impedance. The power supply lines of the ECU are adequately 
filtered. 
MSS 20206-436: 
The shielded FlexRay ECU is connected to an HSD to MQS adapter board by 
means of shielded star quad cable. In this configuration the shielded FlexRay ECU 
and the shielded transmission line can be integrated into EMC sensitive areas of the 
vehicle, e.g. vehicle antennas. The HSD to MQS adapter board should be placed in 
sufficient distance to the EMC sensitive area. 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 67 页
MSS 20206, V2.0, 2018-08, page 67 
Copyright Daimler AG 2018 
MSS 20206-437: 
 
FlexRay ECU
shielded housing
HSD to MQS
adapter board
direct continuous connection 
of connecor to housing
Rosenberger HSD
Rosenberger HSD
ground plane
decoupling capacitor
X7R, 100nF, 50V
direct connection with screw
to car body / ground bolt
shielded star quad cable
EMC sensitive area, 
e.g. vehicle aerials
low impedance ground connection
pig tail connections are forbidden!
BP
BM
FlexRay active star
connection to other 
FlexRay ECUs
keep unshielded FlexRay lines
away from shielded FlexRay line
and EMC sensitive area!
FlexRay ECU
FlexRay ECU
MQS connector
to termination and
transceiver circuitry
Schreiner 09/2010
 
Figure F12.3.4a: exemplary configuration of a partly shielded FlexRay cluster 
 
MSS 20206-438: 
The HSD to MQS adapter board provides a MQS connection to the unshielded part 
of the FlexRay cluster. 
MSS 20206-439: 
In order to prevent common mode interference from spreading over the cable shield 
the HSD to MQS adapter board shall be connected directly to the vehicle body by 
means of bolted fastening. Pig tail interconnections to the vehicle body are not 
allowed.  
MSS 20206-440: 
The ground connection shall be decoupled for DC currents by means of a ceramic 
capacitor, 100nF,  X7R, voltage rating in accordance with [VDA320] and [MBN 
10567] requirements to avoid damage (at least > 50V required). 
MSS 20206-441: 
The HSD to MQS adapter board shall be designed in accordance with [ISO 17458-4] 
- (chapter 8.3) and chapter 10.7 "General ECU layout requirements". 
MSS 20206-442: 
The unshielded parts of the FlexRay cluster shall be separated from the shielded 
transmission line. Especially installing transmission lines parallel to the shielded 
transmission line crossing the HSD to MQS adapter shall be avoided. Otherwise 
common mode interference might be coupled to the cable shield. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 68 页
MSS 20206, V2.0, 2018-08, page 68 
Copyright Daimler AG 2018 
12.4 Topology Layout Design Rules 
MSS 20206-460: 
The FlexRay topology shown below in Figure F12.4a is an example of an active star 
topology with 6 branches. 
Information about the implementation of FlexRay end node, in-between node and 
active star is described in chapter 10.5 "Bus Termination". 
 
MSS 20206-461: 
 
single 
end-nodes
end-node
end-node
in-between node
in-between node
in-between node
in-between node
active
star
ECU
open star branches
lmax,star
lmax,star
Schreiner 12/2007
 
 
Figure F12.4a: exemplary topology with active star ECU and branches with in-
between nodes 
 
MSS 20206-462: 
In-between nodes must not be connected to the bus line via stubs of the wiring 
harness. The transmission lines are interconnected inside the ECU. 
MSS 20206-463: 
Double or multiple crimping at the ECU connector is not allowed. 
MSS 20206-464: 
The maximum total transmission line length of each branch is limited to 
lmax,star=10m if the topology contains an active star. 
In case the topology does not contain an active star, the total transmission line 
length might be increased to about lmax,nostar=15m. The maximum total length 
depends on the number of nodes and the length ratio of the transmission line 
segments. Examination of the topology quality is required. 
MSS 20206-465: 
The limitation of the branch length lmax,star is implied by the FlexRay parameter set. 
MSS 20206-466: 
A maximum number of three nodes can be connected to each branch of an active 
star ECU. Consequently the number of nodes connected to each branch is allowed 
to vary between zero (nothing connected) and three. 
MSS 20206-467: 
In the case of a FlexRay topology not containing an active star, the number of nodes 
within the one existing branch might be increased up to a total number of maximally 
six nodes. The implementation of more than four nodes requires an elaborately 
examination of the topology quality. Every additional node will decrease the possible 
total line length. 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 69 页
MSS 20206, V2.0, 2018-08, page 69 
Copyright Daimler AG 2018 
MSS 20206-468: 
Cascading two or more active star devices via the BM and BP bus lines is not 
allowed, neither locally inside one ECU nor distributed over the network. Only 
physical networks with up to one single active star ECU are allowed. 
MSS 20206-469: 
Other bus topologies such as passive stars, bus topologies with stub lines and all 
combinations including one of these topologies do not achieve adequate signal 
integrity at 10Mbit/s. Hence these  topologies are not allowed. 
MSS 20206-470: 
Either an end node or an universal node with transmission line loop shall be 
connected at the end of each branch. 
MSS 20206-471: 
The total number of nodes with coldstart configuration in a FlexRay network shall be 
three [ISO 17458-2]. The number of sync nodes (whithout being a coldstart node) 
shall be one. 
MSS 20206-472: 
The active star ECU shall be configured as a coldstart node. If the topology does not 
contain an active star, then the ECU with gateway functionality to the vehicle 
network shall be a coldstart node. It is strongly recommended that the other two 
coldstart nodes are located at different branches of the active star if possible. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 70 页
MSS 20206, V2.0, 2018-08, page 70 
Copyright Daimler AG 2018 
13 Annex A (informative) References 
13.1 Company standards 
MSS 20206-445: 
Mercedes-Benz specification documents are available via DocMaster / Daimler 
Supplier Portal  https://daimler.portal.covisint.com. 
 
MSS 20206-446: 
 
Reference 
Title 
[AUTOSAR_INT] 
Integration Requirements AUTOSAR 4.x 
Diagnose Portal, Daimler AG 
[DDS] 
Daimler Diagnostic Specifications 
[DSUDS] 
Daimler Diagnostic Specifications - Supplement to ISO 14229 
DDS S-ISO14229, refer to [CANDELA_TEMPLATE] 
[MBN 10284-1] 
EMV-Anforderungen – Fahrzeugprüfungen 
“EMC Performance Requirements – Vehicle Tests” 
[MBN 10284-2] 
EMV-Anforderungen – Komponentenprüfungen 
“EMC Performance Requirements – Component Test” 
[MSS 10446] 
Microcontroller Requirements (MGU00000778) 
[MBN 10567] 
Electric and Electronic Components in Motor Vehicles - 12 V On-
Board Electrical System - Requirements and Tests 
[MSS 10730] 
Electric and electronic (E/E) components – Standard software for 
electronic control units (ECU) 
[MSS 20200] 
General Networking Performance Specification 
[MSS 20220] 
Networking Test Suite Specification 
 
Table T13.1a: relevant company standards 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 71 页
MSS 20206, V2.0, 2018-08, page 71 
Copyright Daimler AG 2018 
13.2 International standards 
MSS 20206-448: 
 
Reference material 
Source 
FlexRay Communications System - Protocol 
Specification V3.0.1 and V2.1 Rev. A 
FlexRay consortium. To obtain this 
documents refer to the 
microcontroller manufacturer. 
This specifications are replaced by 
[ISO 17458-2] 
FlexRay Communications System - Protocol 
Specification Errata Sheet V2.1 Rev. A V1 
FlexRay Communications System - Electrical 
Physical Layer Specification EPL V3.0.1 
FlexRay consortium. To obtain this 
documents refer to the transceiver 
manufacturer. 
This specifications are replaced by 
[ISO 17458-4] 
FlexRay Communications System - Electrical 
Physical Layer Application Notes V3.0.1 
Electrical Physical Layer Conformance Test 
Specification Version 3.0.1 
FlexRay consortium. To obtain this 
documents refer to the transceiver 
manufacturer. This specifications are 
replaced by [ISO 17458-5] 
FlexRay Physical Layer EMC Measurement 
Specification V3.0.1 
FlexRay consortium. To obtain this 
documents refer to the transceiver 
manufacturer or a testing institute. 
ISO 17458-1 Road vehicles -  
FlexRay communications system -  
Part 1: General information and use case definition 
International Organization for 
Standardization 
URL: www.iso.org 
ISO 17458-2 Road vehicles -  
FlexRay communications system -  
Part 2: Data link layer specification 
ISO 17458-3 Road vehicles -  
FlexRay communications system -  
Part 3: Data link layer conformance test specification 
ISO 17458-4 Road vehicles -  
FlexRay communications system -  
Part 4: Electrical physical layer specification 
ISO 17458-5 Road vehicles -  
FlexRay communications system - 
Part 5: Electrical physical layer conformance test 
specification 
AEC-Q100 Stress Qualification For Integrated 
Circuits 
Automotive Electronics Council 
URL: www.aecouncil.com 
AEC-Q200 Stress Test Qualification For Passive 
Components 
VDA320 Electric and Electronic Components in 
Motor Vehicles - 48 V On-Board Power Supply 
Verband der Automobilindustrie 
URL: www.vda.de 
 
Table T13.2a: international standards 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 72 页
MSS 20206, V2.0, 2018-08, page 72 
Copyright Daimler AG 2018 
14 Annex B (informative) Revision History 
MSS 20206-450: 
 
Date 
Description 
 
Author 
2015-10 
Initial Version 1.0 of MSS 20206 based on MSS 10417 by OG and on 
A2220001599 by MS, MK and DO 
DO, UT 
2015-12 
V1.1 Corrected parameter name in MSS 20206-171 and MSS 20206-172 
UT 
2015-12 
V1.1 Added MSS 20206-451 to MSS 20206-455 chapter 6.11 
UT 
2015-12 
V1.1 Added MSS 20206-456 to MSS 20206-458 chapter 6.12 
UT 
2016-07 
V1.2 MSS 20206-220: Corrected reference to table T8.2a 
DO 
2016-07 
V1.2 MSS 20206-375 Table T11a:  
 
added the ECU SCCM 
 
added V2X 
 
changed the name of EBS_aux to EBB 
 
changed the classification of FOWD from end node to universal node 
 
added description of EIS branch with universal node termination 
 
changed the classification of EBB and of RAS from universal node to 
end node 
DO 
2016-07 
V1.2 MSS 20206-400 Table T12.3.1a: NanoMQS added 
DO 
2016-07 
V1.2 MSS 20206-460 to MSS 20206-472: Added Chapter 12.4 “Topology 
Layout Design Rules” 
DO 
2018-08 
 
V2.0 MSS 20206-259 Table T10.3a : Replaced FlexRay-Transceiver NXP 
TJA1081BTS by NXP TJA1081G, added TJA1085G and TJA1086G 
DO 
2018-08 
 
V2.0 MSS 20206-259 Table T10.3a : Removed discontinued ELMOS 
E981.57, added NXP TJA1083G 
RH 
2018-08 
V2.0 MSS 20206-375 Table T11a : changed the name of CPA to MRR,  
added ECUs AWD, RAS_L and RAS_R 
DO 
2018-08 
V2.0 MSS 20206-221 Table T8.2a : changed the name of Parameter 
pKeyUsedForSynch (old) to pKeySlotUsedForSync (new, Byte 68) 
DO 
2018-08 
V2.0 MSS 20206-221 Table T8.2a : replaced column Encoding by Unit 
DO 
2018-08 
V2.0 MSS 20206-218 and MSS 20206-220 added reference [CANDELA 
TEMPLATE] 
DO 
2018-08 
V2.0 [MBN LV124-1] superseeded by [MBN 10567] 
DO 
2018-08 
V2.0 Added  MSS 20206-486 to -489 chapter 6.13 Wake-up 
DO 
2018-08 
V2.0 MSS 20206-77 Table T6.1a: changed the NM Parameter 
FRNM_READY_SLEEP_CNT to FRNM_READY_SLEEP_TIME 
UT 
2018-08 
V2.0 Added MSS 20206-481 
UT 
2018-08 
V2.0 MSS 20206-91: Changed text 
UT 
2018-08 
V2.0 Removed MSS 20206-79 
UT 
2018-08 
V2.0 Added MSS 20206-483 to -485 
UT 
 
 
 
Author 
Information 
DO 
Dietmar Ostowski 
MK 
Matthias Kuehlewein 
MS 
Marc Schreiner 
OG 
Oliver Glodd 
RH 
Robert Holfelder 
UT 
Uli Teufel 
 
Table T14a: Revision History 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23

