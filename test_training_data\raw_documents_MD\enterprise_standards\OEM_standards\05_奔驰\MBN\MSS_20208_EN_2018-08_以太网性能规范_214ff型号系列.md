# MSS_20208_EN_2018-08_以太网性能规范_214ff型号系列.pdf

## 文档信息
- 标题：<TITEL>
- 作者：<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (059)
- 页数：85

## 文档内容
### 第 1 页
 
 
Mercedes-Benz 
MSS 20208, V2.0 
Company Standard 
Date published: 2018-08 
 
Total no. of pages (incl. Annex): 85  
 
Person in charge: Hartmut Günther 
 
Email: <EMAIL> 
 
Plant: 059, Dept.: RD/UPT 
 
 
 
 
 
Copyright Daimler AG 2018 
Ethernet Networking Performance Specification 
for Model Series 214ff 
 
 
 
 
Foreword 
 
 
This document defines the Ethernet networking performance specification and the interface 
implementation requirements for vehicle multiplexing communication system and the elec-
tronic control modules using Ethernet on certain Mercedes-Benz Cars vehicle models. 
 
This document covers the network layer, the data link layer and the physical layer (100BASE-
T1, 1000BASE-T1 and 100BASE-TX). 
 
Refer all questions to the Mercedes-Benz Cars Networking Group. 
 
Application note:  
Application of the present version of this Standard is binding for new vehicle projects or components 
of this scope, for which no concept/basic specifications or component requirement specifications 
have been approved yet at the date of issue of this version.  
The respective contract documents regulate the mandatory application of the present version of this 
Standard by the supplier. 
 
 
 
Changes 
 
 
Refer to annex A - revision history 
 
 
 
Uncontrolled copy when printed
RD/UBF: <PERSON>, 2018-11-23


### 第 2 页
MSS 20208, V2.0, 2018-08, page 2 
Copyright Daimler AG 2018 
Contents 
 
Ethernet Networking Performance Specification for Model Series 214ff .............................................. 1 
1 
Scope ............................................................................................................................................. 4 
1.1 
Deviations ................................................................................................................................... 4 
2 
Normative references ..................................................................................................................... 5 
3 
Terms and Definitions .................................................................................................................... 8 
4 
General requirements................................................................................................................... 10 
5 
Ethernet Network Structure .......................................................................................................... 11 
5.1 
Preface ..................................................................................................................................... 11 
5.2 
Network Topology ..................................................................................................................... 11 
6 
Ethernet Networking Section ........................................................................................................ 12 
6.1 
Addressing ................................................................................................................................ 12 
6.1.1 
MAC addressing ............................................................................................................ 12 
6.1.2 
VLAN and priority classes .............................................................................................. 16 
6.1.3 
IP addressing ................................................................................................................. 18 
6.2 
Transport Protocols .................................................................................................................. 20 
6.2.1 
UDP ............................................................................................................................... 20 
6.2.2 
UDP with IP Fragmentation ........................................................................................... 20 
6.2.3 
TCP ................................................................................................................................ 20 
6.2.4 
IEEE 1722 ...................................................................................................................... 20 
6.3 
Switches ................................................................................................................................... 21 
6.3.1 
Switch categories and description ................................................................................. 21 
6.3.2 
Requirements for ECUs with internal communication via switches ............................... 23 
6.3.3 
General switch requirements ......................................................................................... 23 
6.4 
Communication behavior .......................................................................................................... 25 
6.4.1 
AUTOSAR Ethernet UDP NM parametrization .............................................................. 25 
6.4.2 
Wake-up/sleep mode for ECUs with permanent power supply ..................................... 25 
6.4.3 
Startup/shutdown for ECUs with switched power supply .............................................. 27 
6.4.4 
Service Discovery .......................................................................................................... 28 
6.4.5 
Network Operation ......................................................................................................... 29 
7 
Ethernet MAC Layer ..................................................................................................................... 32 
7.1 
Switch hardware requirements ................................................................................................. 32 
7.1.1 
General Requirements ................................................................................................... 32 
7.1.2 
Address Resolution Requirements ................................................................................ 32 
7.1.3 
VLAN Requirements ...................................................................................................... 32 
7.1.4 
Quality of Service Requirements ................................................................................... 32 
7.1.5 
Ingress Filtering Requirements ...................................................................................... 32 
7.1.6 
Diagnostics Requirements ............................................................................................. 32 
7.1.7 
Interface Requirements ................................................................................................. 32 
7.1.8 
Configuration Requirements .......................................................................................... 32 
7.1.9 
Time Synchronization Requirements ............................................................................. 32 
7.1.10 
Audio Video Bridging Requirements .............................................................................. 33 
7.1.11 
Additional Requirements ................................................................................................ 33 
7.2 
Switch test requirements .......................................................................................................... 33 
8 
Ethernet Physical Layer implementations .................................................................................... 34 
8.1 
100BASE-T1 ECU Interface ..................................................................................................... 34 
8.1.1 
Physical Layer Interface ................................................................................................ 34 
8.1.2 
100BASE-T1 Transceivers (standalone or integrated in switch) ................................... 37 
8.1.3 
Common Mode Choke for 100BASE-T1 interfaces ....................................................... 40 
8.1.4 
ECU connectors for 100BASE-T1 interfaces (MDI interface) ........................................ 41 
8.1.5 
ESD Elements................................................................................................................ 41 
8.2 
1000BASE-T1 ECU Interface ................................................................................................... 41 
8.2.1 
Physical Layer Interface ................................................................................................ 41 
8.2.2 
1000BASE-T1 Transceivers (standalone or integrated in switch) ................................. 45 
8.2.3 
Common Mode Choke for 1000BASE-T1 interfaces ..................................................... 48 
8.2.4 
ECU connectors for 1000BASE-T1 interfaces (MDI interface) ...................................... 49 
8.2.5 
ESD Elements................................................................................................................ 49 
8.3 
100BASE-TX (Fast Ethernet) ECU Interface ........................................................................... 49 
8.3.1 
Physical Layer Interface ................................................................................................ 49 
8.3.2 
Requirements for 100BASE-TX Transceivers ............................................................... 50 
8.3.3 
Requirements for Transformers for 100BASE-TX ......................................................... 50 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 3 页
MSS 20208, V2.0, 2018-08, page 3 
Copyright Daimler AG 2018 
8.3.4 
Recommended ECU connectors for 100BASE-TX ....................................................... 50 
8.3.5 
ESD Elements................................................................................................................ 50 
8.3.6 
Activation Line Signaling for 100BASE-TX interfaces ................................................... 50 
8.4 
Required ECU documentation for 100BASE-T1 / 1000BASE-T1 interfaces............................ 50 
8.4.1 
Documentation of 100BASE-T1 / 1000BASE-T1 interface (including interface to 
µController) ................................................................................................................................... 50 
8.4.2 
Documentation of ECU supply interface ........................................................................ 51 
8.4.3 
Documentation of wake-up line interface ...................................................................... 52 
8.4.4 
Test reports for 100BASE-T1 / 1000BASE-T1 interface ............................................... 52 
8.4.5 
Test reports for wake-up line ......................................................................................... 52 
8.5 
Wake-up Line Requirements for 100BASE-T1 / 1000BASE-T1 Links ..................................... 52 
8.5.1 
Wake-up Line ................................................................................................................. 52 
8.5.2 
Wake-up Line Interface .................................................................................................. 53 
8.5.3 
Wake-up topology .......................................................................................................... 54 
8.5.4 
Timing of wake-up Line .................................................................................................. 56 
8.6 
Cable harness for 100BASE-T1 Links ...................................................................................... 60 
8.6.1 
General requirements on cables.................................................................................... 60 
8.6.2 
Exemplary implementation hints on connectors ............................................................ 61 
8.7 
Cable harness for 1000BASE-T1 Links .................................................................................... 62 
8.7.1 
General requirements on cables.................................................................................... 62 
8.7.2 
Exemplary implementation hints on connectors ............................................................ 62 
9 
Ethernet ECU Diagnostics ........................................................................................................... 64 
9.1 
Diagnostics Services ................................................................................................................ 64 
9.1.1 
Link Information ............................................................................................................. 64 
9.1.2 
Test Modes .................................................................................................................... 65 
9.1.3 
Wake-up Line Information .............................................................................................. 67 
9.1.4 
Communication Statistics .............................................................................................. 67 
9.1.5 
Configuration.................................................................................................................. 69 
9.1.6 
Identification ................................................................................................................... 69 
9.1.7 
Addressing ..................................................................................................................... 69 
9.1.8 
Mirroring ......................................................................................................................... 70 
9.2 
Failure monitoring ..................................................................................................................... 71 
9.2.1 
Hardware link failures .................................................................................................... 71 
9.2.2 
Wake-up line failures ..................................................................................................... 71 
10 
Supplier qualification tests for 100BASE-T1 / 1000BASE-T1 ECUs ........................................ 73 
10.1 
Qualification Tests for ECUs with 100BASE-T1 / 1000BASE-T1 PHY or Switch ................. 73 
10.2 
100BASE-T1 / 1000BASE-T1 Wake-up Line Functional Evaluation .................................... 73 
10.2.1 
100BASE-T1 / 1000BASE-T1 Worst Case Load Tests ................................................. 73 
10.2.2 
100BASE-T1 / 1000BASE-T1 Short circuit Load Test................................................... 75 
10.2.3 
100BASE-T1 / 1000BASE-T1 Wake-up Signal Corner Cases ...................................... 76 
11 
Supplier qualification tests for 100BASE-TX (Fast Ethernet) ECU interfaces.......................... 81 
11.1 
100BASE-TX Conformance Test .......................................................................................... 81 
11.1.1 
Test Reports for Transceiver ......................................................................................... 81 
11.1.2 
Tests to be done by the ECU Supplier .......................................................................... 81 
11.2 
100BASE-TX (FastEthernet) Activation Line ........................................................................ 82 
12 
Design hints for ECUs with 100BASE-T1 / 1000BASE-T1 interface(s) ................................... 83 
13 
Annex A (informative) ............................................................................................................... 84 
13.1 
Revision History .................................................................................................................... 84 
13.2 
Author information ................................................................................................................. 85 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 4 页
MSS 20208, V2.0, 2018-08, page 4 
Copyright Daimler AG 2018 
1 
Scope 
MSS 20208-37: 
This document defines the Ethernet networking performance specification and the 
interface implementation requirements for vehicle multiplexing communication 
system and the electronic control modules using Ethernet on certain Mercedes-Benz 
Cars vehicle models. 
MSS 20208-38: 
This document covers the Ethernet networking section, the data link layer and the 
physical layer (100BASE-T1 / 1000BASE-T1 and 100BASE-TX). 
MSS 20208-39: 
The term "100BASE-T1" is used in this specification for the interface according to 
IEEE 802.3 - clause 96 (refer to [IEEE802.3bw], which is defined in IEEE 802.3bw 
and based on the OPEN Alliance BroadR-Reach (OABR) Physical Layer 
Transceiver Specification For Automotive Applications. 
MSS 20208-1018: The term "1000BASE-T1" is used in this specification for the interface according to 
IEEE 802.3 - clause 97 [IEEE802.3bp]. 
MSS 20208-40: 
This specification is intended for Daimler internal use and Daimler supplier internal 
use only. 
MSS 20208-41: 
Refer all questions to the Vehicle Networking Group. 
1.1 
Deviations 
MSS 20208-43: 
In the current document the following defined terminology prescription applies. The 
usage of 
MSS 20208-44: 
 
- “Shall” expresses in the text a mandatory requirement. 
MSS 20208-45: 
 
- “Should” expresses in the text an optional requirement. 
MSS 20208-46: 
 
- “Must” expresses in the text a normative requirement. 
MSS 20208-47: 
 
- “Can” expresses in the text a permitted practice or method. 
 
MSS 20208-48: 
Text segments written in italic letters have information character. 
 
MSS 20208-49: 
Deviations from the requirements contained in this standard are only allowed if 
agreed to explicitly and documented between the supplier, Vehicle Networking 
Group and the appropriate responsible person for the relevant component within 
Mercedes-Benz. 
MSS 20208-50: 
All tolerance values mentioned in this specification include initial tolerance, aging 
and temperature effects according to the application profile (service life, temperature 
profile) as specified in the Component Requirement Specifications of the affected 
components. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 5 页
MSS 20208, V2.0, 2018-08, page 5 
Copyright Daimler AG 2018 
2 
Normative references 
MSS 20208-52: 
The following referenced documents are indispensable for the application of this 
document. For dated references, only the edition cited applies. For undated 
references, the latest edition of the referenced document (including any 
amendments) applies. 
MSS 20208-53: 
The referenced documents listed below shall be followed when developing ECUs 
with Ethernet / Gigabit Ethernet interface or when designing an Ethernet / Gigabit 
Ethernet network. 
MSS 20208-54: 
Mercedes-Benz specification documents are available via DocMaster and Diagnose 
Portal from Daimler Supplier Portal https://daimler.portal.covisint.com. 
MSS 20208-55: 
OPEN Alliance documents are available from http://www.opensig.org/Automotive-
Ethernet-Specifications/ 
MSS 20208-56: 
IEEE documents are available from www.ieee.org 
MSS 20208-57: 
Further test specifications are available from test houses directly (e.g. FTZ, UNH, 
C&S Group, Ruetz System Solutions, TÜV Nord). 
 
Document Number 
Document Title 
[OPEN_EMC] 
OPEN Alliance, TC1: IEEE 100BASE-T1 EMC Test Specification for Transceivers, Ver-
sion 1.0 
[OPEN_EMC_1G] 
OPEN Alliance, TC12: IEEE 100BASE-T1 EMC Test Specification for Transceivers, 
Version 1.0 
[OPEN_CMC] 
OPEN Alliance. TC1: IEEE 1000BASE-T1 EMC Test Specification for Common Mode 
Chokes, Version 1.0 
[OPEN_CMC_1G] 
OPEN Alliance. TC3: IEEE 1000BASE-T1 EMC Test Specification for Common Mode 
Chokes, Version 1.0 
[OPEN_PCS] 
OPEN Alliance, TC1: IEEE 100BASE-T1 Physical Coding Sublayer Test Suite, Version 
1.1 
[OPEN_PCS_1G] 
OPEN Alliance, TC12: IEEE 1000BASE-T1 Physical Coding Sublayer Test Suite, Ver-
sion 1.0 
[OPEN_PMA] 
OPEN Alliance, TC1: IEEE 100BASE-T1 Physical Media Attachment Test Suite, Ver-
sion 1.0 
[OPEN_PMA_1G] 
 
OPEN Alliance, TC12: IEEE 1000BASE-T1 Physical Media Attachment Test Suite, Ver-
sion 1.0 
[OPEN_PHYCTS] 
OPEN Alliance, TC1: IEEE 100BASE-T1 PHY Control Test Suite, Version 1.1 
[OPEN_PHYCTS_1G] 
OPEN Alliance, TC12: IEEE 1000BASE-T1 PHY Control Test Suite, Version 1.0 
[OPEN_IOP] 
OPEN Alliance, TC1: 100BASE-T1 Interoperability Test Suite Interoperability Test 
Suite Specification, Version 1 
[OPEN_IOP_1G] 
OPEN Alliance, TC12: 1000BASE-T1 Interoperability Test Suite Interoperability Test 
Suite Specification, Version 1.0 
[OPEN_CC] 
OPEN Alliance, TC2: IEEE 100BASE-T1 Definitions for Communication Channel, Ver-
sion 1.0 
[OPEN_CC_1G] 
OPEN Alliance, TC9: Channel and Components Requirements for 1000BASE-T1 Link 
Segment Type A, Version 2.0 
[OPEN_SWITCH] 
OPEN Alliance, TC11: Requirements for Ethernet Switch Semiconductors, Version 
1.0 
[OPEN_SWITCH_TEST] 
OPEN Alliance, TC11: Switch Semiconductor Test Specification, Version 1.0 
[OPEN_ECU] 
OPEN Alliance, TC8: Ethernet ECU Test Specification TC8 ECU Test, Version 2.0 
[OPEN_DIAG] 
OPEN Alliance, TC1: Advanced diagnostic features for 100BASE-T1 automotive 
Ethernet PHYs 
[OPEN_ESD] 
OPEN Alliance, TC1: IEEE 100BASE-T1 EMC Test Specification for ESD suppression 
devices, Version 1.1 
[OPEN_ESD_1G] 
OPEN Alliance, TC1: IEEE 1000BASE-T1 EMC Test Specification for ESD suppression 
devices, Version 1.1 
[OPEN_SIS] 
OPEN Alliance, TC1: IEEE 100BASE-T1 System Implementation Specification, Version 
1.0 
[OPEN_WODL] 
OPEN Alliance, TC10: Sleep/Wake-up Specification, Version 2.1 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 6 页
MSS 20208, V2.0, 2018-08, page 6 
Copyright Daimler AG 2018 
Document Number 
Document Title 
 
[IEEE802.3bw] 
IEEE802.3(bw), clause 96 (100BASE-T1 physical layer specification) 
[IEEE802.3] 
IEEE802.3-2015 
[IEEE802.1Q] 
Bridges and Bridged Networks 
[IEEE1588] 
IEEE Standard for a Precision Clock Synchronization Protocol for Networked Measu-
rement and Control Systems 
[IEEE1722] 
Layer 2 Transport Protocol for Time Sensitive Applications in Bridged Local Area Net-
works 
[ISO_DoIP] 
ISO13400-3 Road Vehicles – Diagnostic communication over internet Protocol- 
Part3: Wired vehicle interface based on IEEE802.3 
[FTZ FE EMC] 
Fast Ethernet Physical Layer EMC Measurement Specification for Transceivers, 
FTZ EMC Test Spec for 100BASE-TX 
 
Remark: FTZ EMC Measurement Specification for Transceivers will be superseded by 
IEC62228-5 when the document is finished. 
[100BASE_TX_IOP] 
UNH Interoperability Test Spec for 100BASE-TX, consisting of the following 
documents: 
- UNH IOL MAU Test Suite, IEEE802.3, Clause 14 
(https://www.iol.unh.edu/sites/default/files/testsuites/ether-
net/CL14_MAU/MAU_Test_Suite_v5.4.pdf) 
- UNH IOL PMD Test Suite, IEEE802.3, Clause 25 
(https://www.iol.unh.edu/sites/default/files/testsuites/ether-
net/CL25_PMD/PMD_Test_Suite_v3.5.pdf) 
- UNH IOL PMD Test Suite Autonegotiation, IEEE802.3, Clause 28 ( 
* https://www.iol.unh.edu/sites/default/files/testsuites/ether-
net/Management_Register_Test_Suite/Management_Regis-
ter_Test_Suite_v4.1.pdf 
* https://www.iol.unh.edu/sites/default/files/testsuites/ether-
net/Management_System_Suite/Management_Sys-
tem_Test_Suite_v3.0.pdf 
* https://www.iol.unh.edu/sites/default/files/testsuites/ether-
net/Next_Page_Test_Suite/next_page_exchange_test_suite_rev._2.6
.pdf 
* https://www.iol.unh.edu/sites/default/files/testsuites/ether-
net/State_Machine_Suite/State_Ma-
chine_Base_Page_Test_Suite_v6.3.pdf) 
[SWS_EthSwt_Drv] 
AUTOSAR Specification of Ethernet Switch Driver 
[SWS_EthTrcv_Drv] 
AUTOSAR Specification of Ethernet Transceiver Driver 
[AUTOSAR_INT] 
Integration Requirements AUTOSAR 4.x 
Diagnose Portal, Daimler AG 
[MSS 20200] 
General Vehicle Networking Performance Specification 
[MSS 20208-DH] 
Design Hints for Ethernet Interfaces 
[MSS 20220] 
Networking Test Suite Specification 
[MSS 20228] 
Ethernet Networking Test Suite Specification 
[MBN LV124-1] 
Electric and Electronic Components in Passenger Cars up to 3.5 t - General Require-
ments, Test Conditions and Tests; Part 1: Electrical Requirements (former MBN 
10615) 
obsolete, superseeded by [MBN10567] 
[MBN 10567] 
Elektrische und elektronische Komponenten im Kraftfahrzeug – 12 V Bordnetz – An-
forderungen und Prüfungen 
[MBN LV213-2] 
Hochfrequenzleitungen für Kraftfahrzeuge die keine einzelnen Koaxialleitungen sind 
[DIAG_ISO] 
DDS S-ISO 14229 (DSUDS) 
[DIAG_COMSYS] 
DDS S-COMSYS Part II (DSCS) 
[DIAG_R01_01] 
DDS R01-01 (DRECU) 
[DIAG_R01_02] 
DDS R01-02 (DRAC) 
[CANDELA_TEMPLATE] 
Candela Template contains definition of diagnostic services 
Diagnose Portal, Daimler AG 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 7 页
MSS 20208, V2.0, 2018-08, page 7 
Copyright Daimler AG 2018 
Document Number 
Document Title 
[MBN 10284-1] 
EMC Performance Requirements – Vehicle Tests 
[MBN 10284-2] 
EMC Performance Requirements – Component Test 
[MSS 21001] 
Global Time Synchronization Specification 
[MSS 10730] 
Electric and electronic (E/E) components - Standard software for electronic control 
units (ECU) 
[VDA320] 
Electric and Electronic Components in Motor Vehicles - 48 V On-Board Power Supply 
[RFC1643] 
Definitions of Managed Objects for the Ethernet-like Interface Types 
[RFC1493] 
Definitions of Managed Objects for Bridges 
[RFC1213] 
Management Information Base for Network Management of TCP/IP-based internets: 
MIB-II 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 8 页
MSS 20208, V2.0, 2018-08, page 8 
Copyright Daimler AG 2018 
3 
Terms and Definitions 
 
Term 
Definition 
AUTOSAR 
Automotive Open System Architecture 
BER 
Bit Error Rate 
BIN 
Bus Interface Network (consisting of ESD-protection, CMT, DC-block capacitors and CMC) 
CMC 
Common Mode Choke 
CMT 
Common Mode Termination 
CPU 
Central Processing Unit 
CRC 
Cyclic Redundancy Check 
DC 
Direct Current 
DGND 
Digital Ground (filtered ground potential) 
DTC 
Diagnostic Trouble Code 
DID 
Diagnostic Identifier 
ECU 
Electronic Control Unit 
EMC 
Electromagnetic Compatibility = immunity and emission 
ESD 
Electro Static Discharge 
FCS 
Frame Check Sequence 
FTZ 
Forschungstransferzentrum Zwickau - EMC Lab 
GMAC 
Gigabit Media Access Control 
GMII 
Gigabit Media Independent Interface 
GND 
Ground 
IC 
Integrated Circuit 
IEEE 
Institute of Electrical and Electronics Engineers 
IEEE 1722 
Transport protocol for A/V streaming 
IETF 
Internet Engineering Taskforce 
IOP 
Interoperability 
IP 
Internet Protocol 
LP 
Link Partner 
LPF 
Low Pass Filter 
MAC 
Media Access Control – Interface between OA BR Transceiver and Host according to 
IEEE802.3 
MAC-Switch 
Switch without PHY-ports, but multiple MAC-interfaces 
MBC 
Mercedes-Benz Cars 
MBN 
Mercedes-Benz Norm 
MDI 
Media Dependent Interface (IEEE terminology for ECU connector) 
MIB 
Management Information Base 
MII 
Media Independent Interface 
MSE 
Mean Square Error 
NCD 
Network Communication Description (formerly "VMM - Vehicle Message Matrix") 
NM 
Network Management 
OABR 
Open Alliance BroadR-Reach®, former naming of 100BASE-T1 
OBD 
On-Board Diagnostics 
OPEN-SIG 
One-Pair Ether-Net Special Interest Group (www.opensig.org) 
OSC 
Oscillator 
OUI 
Organizationally Unique Identifier 
PCB 
Printed Circuit Board 
Permanent power 
supply 
Battery fed, e.g. clamp 30, clamp 30T 
PGND 
Power Ground (i.e. clamp 31) 
PHY 
Physical Layer Transceiver, including single OABR Port and MAC-interface 
PMA 
Physical Media Attachment 
PPAP 
Production Part Approval Process 
PWM 
Pulse Width Modulation 
RF 
Radio Frequency 
RFC 
Request For Comments 
RGMII 
Reduced Gigabit Media Independent Interface 
RMII 
ReducedMedia Independent Interface 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 9 页
MSS 20208, V2.0, 2018-08, page 9 
Copyright Daimler AG 2018 
Term 
Definition 
RMON 
Remote Monitoring 
SD 
Service Discovery 
SGMII 
Serial Gigabit Media Independent Interface 
sheathed cable 
German: “gemantelte Leitung” 
shielded cable 
German: “geschirmte Leitung” 
SNA 
Signal not available 
SNR 
Signal to Noise Ratio 
SQI 
Signal Quality Index 
Switch 
Network component with multiple PHY-ports and capability to forward Ethernet frames 
between PHY-ports 
switched power 
supply 
Ignition fed, e.g. clamp 15, clamp 87 
TC 
Transceiver 
TCP 
Transmission Control Protocol 
TP 
Twisted Pair (cable), German: “verdrillte Zweidrahtleitung” 
uC 
Microcontroller 
UDP 
User Datagram Protocol 
UNH 
University of New Hampshire - Interoperability Lab 
UTP  
Unshielded TP, German: “ungeschirmte verdrillte Zweidrahtleitung” 
WL 
Wakeup Line 
WoDL 
Wake-up over Dataline 
WU ECU 
wake-up capable ECU 
WUP 
wake-up 
Vehicle Networ-
king Group 
Subdivision of Mercedes-Benz Cars responsible for in-vehicle networking 
VLAN 
Virtual Local Area Network 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 10 页
MSS 20208, V2.0, 2018-08, page 10 
Copyright Daimler AG 2018 
4 
General requirements 
MSS 20208-60: 
For safety requirements, homologation (in particular, exhaust emissions) and quality, 
the existing statutory requirements and laws shall be complied with. In addition, the 
relevant requirements of the Daimler Group apply. 
MSS 20208-61: 
All materials, procedures, processes, components, and systems shall conform to the 
current regulatory (governmental) requirements regarding regulated substances and 
recyclability. 
MSS 20208-810: 
An Ethernet ECU shall be able to handle and service the worst case continuous and 
burst frame loads by design without any functional restrictions on every connected 
Ethernet Link.  Unless otherwise stated in the Daimler component specification a 
worst case continuous frame load of 100% at maximum link speed has to be 
assumed. 
MSS 20208-819: 
The ECU supplier shall provide a holistic performance calculation to proof 
compliance with the above requirement. The performance calculation has to  be 
agreed by Daimler component responsible engineer and Daimler Networking Group 
at project start. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 11 页
MSS 20208, V2.0, 2018-08, page 11 
Copyright Daimler AG 2018 
5 
Ethernet Network Structure 
5.1 
Preface 
MSS 20208-64: 
Automotive Ethernet is – in opposite to classical automotive bus systems – not a 
shared medium communication system, but a switched network. This means the 
network consists of multiple point-to-point links which are concatenated to a network 
within a switch. There are different constraints to network topologies and the 
individual links which have to be taken into account. 
As a consequence the 100BASE-T1 / 1000BASE-T1 Networking Details focuses on 
the requirements of an individual link of the 100BASE-T1 / 1000BASE-T1 network. 
The requirements however are valid for all links of the 100BASE-T1 / 1000BASE-T1 
network. 
5.2 
Network Topology 
MSS 20208-66: 
Each network node with more than one Ethernet port is called a “switch” and is 
internally responsible for forwarding frames received at one port to the other ports. 
The following figure F5.2a shows a simple exemplary 100BASE-T1 network topology 
which consists of a 4-port Switch (S1), including 4 individual PHY-ports, a 2-port 
Switch (S2), including 2 individual PHY-ports and 4 nodes (T1-T4) including only one 
single PHY. 
MSS 20208-67: 
 
 
 
Figure F5.2a: Exemplary 100BASE-T1 Network Topology 
 
MSS 20208-68: 
Every link of this topology is an individual physical layer link. For the network 
topology a frame from node (T1) to node (T3) has to be transmitted over 3 individual 
physical layer links (called “hops”): From (T1) to (S1), then from (S1) to (S2)  and 
finally from (S2) to (T3). 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 12 页
MSS 20208, V2.0, 2018-08, page 12 
Copyright Daimler AG 2018 
6 
Ethernet Networking Section 
6.1 
Addressing 
6.1.1 
MAC addressing 
******* 
Daimler MAC address range 
MSS 20208-73: 
Daimler has registered a MAC address range at the IEEE. The OUI (Organizationally 
Unique Identifier) and the range of the EUI (Extended Unique Identifier) is 
documented in table T*******a. 
MSS 20208-74: 
 
OUI  
EUI-48 Address Block 
3C-CE-15 
00-00-00 through FF-FF-FF 
 
Table T*******a: Daimler MAC address range 
 
MSS 20208-75: 
MAC addresses of the Daimler MAC address range shall only be used under 
instruction of the Vehicle Networking Group. 
******* 
MAC addresses for vehicle internal Ethernet interfaces 
MSS 20208-77: 
Vehicle internal Ethernet interfaces shall be assigned one static MAC address, i.e. 
each particular ECU including ECU variants will have the same MAC address. 
MSS 20208-78: 
The MAC address for vehicle internal interfaces is provided with the ECU Extract. 
MSS 20208-79: 
Vehicles with direct access to the internal network via a switch (e.g. configuration in 
figure F*******a) shall not be connected, e.g. by an external switch (e.g. 
configuration in figure F*******b) due to resulting MAC address and IP address 
conflicts. 
MSS 20208-80: 
 
Vehicle
Switch 1
ECU 1
B1
C1
D1
ECU 2
ECU 3
I3
I2
A1
External 
Device
I1
 
 
Figure F*******a: MAC addressing - direct external access 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 13 页
MSS 20208, V2.0, 2018-08, page 13 
Copyright Daimler AG 2018 
MSS 20208-81: 
 
Vehicle A
Switch 1
ECU 1
B1
C1
D1
ECU 2
ECU 3
I3
I2
A1
I1
Vehicle B
Switch 1
ECU 1
B1
C1
D1
ECU 2
ECU 3
I3
I2
A1
External 
Switch
I1
Switch
BE
CE
DE
AE
Same MAC 
and 
IP addresses! 
 
 
Figure F*******b: MAC addressing - conflicts with external switch 
 
******* 
MAC addresses for ECU internal communication 
MSS 20208-83: 
Additional MAC addresses for ECUs with multiple internal interfaces (e.g. 
configuration in figure F*******a) shall be obtained at the Vehicle Networking Group. 
MSS 20208-84: 
Additional MAC addresses are not part of the provided ECU Extract. 
MSS 20208-85: 
 
Switch
ECU 1
A1
I2
I1
µC1
µC2
Vehicle 
communication
MAC address not 
in ECU extract
ECU 1
I1
µC1
MAC address 
in ECU extract
ECU description
ECU 
implementation
ECU internal 
communication
 
Figure F*******a: MAC addressing for multiple internal interfaces 
 
******* 
MAC addresses for dedicated vehicle external Ethernet interfaces 
MSS 20208-87: 
Dedicated vehicle external Ethernet interfaces (e.g. Port E5 at ECU 5 in figure 
F*******a) shall have a globally unique MAC address. 
MSS 20208-88: 
MAC addresses for dedicated vehicle external Ethernet interfaces are not provided 
by the Vehicle Networking Group and are not part of the ECU Extract. 
MSS 20208-89: 
MAC addresses for dedicated vehicle external Ethernet interfaces shall not be from 
the MAC address range in table T*******a. 
MSS 20208-90: 
MAC addresses for dedicated vehicle external Ethernet interfaces shall be registered 
at the IEEE. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 14 页
MSS 20208, V2.0, 2018-08, page 14 
Copyright Daimler AG 2018 
MSS 20208-91: 
 
Vehicle
Switch 1
A1
ECU 1
B1
C1
D1
ECU 2
ECU 3
ECU 5
I3
I2
I5
E5
External 
Device
I1
 
 
Figure F*******a: MAC addressing for external interfaces 
 
******* 
MAC addresses for internal interfaces for multiple ECUs 
MSS 20208-93: 
ECUs which are installed multiple times with the same interface shall support a 
reconfigurable MAC address. 
MSS 20208-94: 
Unconfigured ECUs shall use a MAC address differing the configured ECUs. 
MSS 20208-95: 
The MAC addresses for the different ECU instances are assigned by the Vehicle 
Networking Group and are provided with the ECU Extracts of the clients. 
MSS 20208-96: 
The MAC addresses for the unconfigured ECUs shall be obtained at the Vehicle 
Networking Group. 
MSS 20208-97: 
From network point of view there are basically two options for the MAC address 
assignment: 
MSS 20208-98: 
- Configuration independent to the Ethernet link, e.g. by Pin coding 
MSS 20208-99: 
- Configuration by a configuration server, an example is depicted in F*******a 
MSS 20208-100: 
 
ECU 3
Config Server
VLAN
ECU 3
Switch 3
B3
C3
D3
A3
I3
ECU 31.3
ECU 31.1
Vehicle Network
ECU 31.2
I31.0
I31.0
I31.0
Config Server
VLAN
Switch 3
B3
C3
D3
A3
I3
Vehicle Network
I31.2
I31.1
I31.3
Config Client
Config Client
Config Client
ECU 31.3
ECU 31.1
ECU 31.2
Config Client
Config Client
Config Client
 
Figure F*******a: MAC address assignment for multiple ECUs 
 
MSS 20208-101: 
The mechanism for assignment of a new MAC address shall not affect the vehicle 
network. 
MSS 20208-102: 
Note: This can be achieved by using a VLAN which is limited to the affected ECUs 
(Refer to figure F*******a). For VLANs refer to section 6.1.2. 
MSS 20208-103: 
The newly assigned MAC address shall be stored persistent. 
MSS 20208-104: 
The MAC address assignment shall also include the assignment of the 
corresponding IP addresses, refer to section "IP addresses for multiple ECUs". 
MSS 20208-105: 
A possible address configuration strategy is depicted in figure F*******b. 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 15 页
MSS 20208, V2.0, 2018-08, page 15 
Copyright Daimler AG 2018 
MSS 20208-106: 
 
VLANConfig
Deactivate ports C3 and D3
ECU 3
Switch 3
B3
C3
D3
A3
I3
ECU 31.1
ECU 31.3
ECU 31.2
Config Server
Config Client
Config Client
Config Client
Trigger by Diagnostic 
Request
Vehicle Network
Config Offer:
Source: MACServer, IPServer, VLANConfig
Target: MACMulticast, IPMulticast, VLANConfig
Payload: Configuration ECU 31.1
Store assigned 
configuration persistent 
and restart Ethernet-link
Config Acknowledgement:
Source: MAC31.1, IP31.1, VLANConfig
Target: MACServer, IPServer, VLANConfig
Activate port C3
Deactivate ports B3 and D3
Config Offer:
Source: MACServer, IPServer, VLANConfig
Target: MACMulticast, IPMulticast, VLANConfig
Payload: Configuration ECU 31.2
Store assigned 
configuration persistent 
and restart Ethernet-link
Config Acknowledgement:
Source: MAC31.3, IP31.3, VLANConfig
Target: MACServer, IPServer, VLANConfig
Config Offer:
Source: MACServer, IPServer, VLANConfig
Target: MACMulticast, IPMulticast, VLANConfig
Payload: Configuration ECU 31.3
Activate port D3
Deactivate ports B3 and C3
Activate all ports
Store assigned 
configuration persistent 
and restart Ethernet-link
Config Acknowledgement:
Source: MAC31.2, IP31.2, VLANConfig
Target: MACServer, IPServer, VLANConfig
I31.x
I31.x
I31.x
 
Figure F*******b: Address configuration strategy for multiple ECUs 
 
******* 
Multicast MAC addresses 
MSS 20208-108: 
Multicast MAC addresses shall only be assigned by the Vehicle Networking Group. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 16 页
MSS 20208, V2.0, 2018-08, page 16 
Copyright Daimler AG 2018 
6.1.2 
VLAN and priority classes 
******* 
VLAN 
MSS 20208-111: 
Single Tagged VLAN according to IEEE 802.1Q shall be supported. Refer to 
[IEEE802.1Q] for more information. 
MSS 20208-112: 
All vehicle communication shall be assigned to VLAN-IDs. 
MSS 20208-113: 
VLAN-IDs shall only be assigned by the Vehicle Networking Group. 
MSS 20208-114: 
VLAN IDs shall not be retagged. 
MSS 20208-115: 
VLAN IDs shall not be untagged, except the egress port to the external vehicle 
access. 
MSS 20208-116: 
Figure F*******a depicts a VLAN example 
 
VLAN 
Membership 
VLAN External 
ECU 1, ECU 2, ECU 3, ECU 31, ECU 32 
Ports A1, B1, C1, D1 and A3, B3, C3, D3 
VLAN Internal 1 
ECU 1, ECU 2, ECU 3 
Ports B1, C1, D1 and A3 
VLAN Internal 3 
ECU 3, ECU 31, ECU 32 
Ports B3, C3, D3 
 
Table T*******: VLAN Memberships 
 
MSS 20208-117: 
 
Unknown
VLAN External
VLAN Internal 1
VLAN Internal 3
Switch 1
ECU 1
B1
C1
D1
ECU 2
I
A1
ECU 3
I
Switch 3
B3
C3
D3
A3
I
ECU 32
ECU 31
I
I
External
Access
Tag
Untag
 
Figure F*******a: VLAN Example 
 
******* 
Double tagging 
MSS 20208-119: 
Double tagging shall not be used for vehicle internal communication. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 17 页
MSS 20208, V2.0, 2018-08, page 17 
Copyright Daimler AG 2018 
******* 
VLAN handling in switches 
MSS 20208-124: 
The VLAN handling configuration of switches is defined in the NCD and is provided 
with the ECU Extract of the switch host. 
MSS 20208-125: 
Untagged frames at vehicle internal ports shall be dropped. 
MSS 20208-126: 
Requirements for handling of a port for external access (refer also to the example in 
figure F*******a): 
MSS 20208-127: 
 
Ingress Handling: Untagged frames shall be tagged with a dedicated VLAN  
 
("VLAN External") 
MSS 20208-128: 
 
Ingress Handling: Tagged frames shall be dropped (e.g. with EtherTypes:   
 
0x8100, 0x88A8, 0x9100, 0x9200) 
MSS 20208-129: 
 
Egress Handling: Frames tagged with a dedicated VLAN  
 
("VLAN External") shall be untagged 
MSS 20208-130: 
 
Egress Handling: Frames from other VLANs shall not be switched to the port  
 
for external access 
******* 
Priority classes 
MSS 20208-132: 
All vehicle communication shall be assigned to priority classes, refer to table 
T*******a. Priority classes are assigned in the NCD and provided via the ECU 
Extract. 
MSS 20208-133: 
For communication originating outside the vehicle networks the according priority 
(priority class "0", refer to table T*******a) shall be assigned at the ingress port of the 
first switch (dedicated port for external access, refer to figure *******a). 
MSS 20208-134: 
The priority classes are mapped to the priority field (PCP field) of the IEEE 802.1Q 
header. Refer to table T*******a for the typical use of the priority classes. 
MSS 20208-1241: For switches the priority shall be assigned directly to traffic classes which are 
representing traffic queues. 
MSS 20208-135: 
 
Priority Class 
Communication Class 
Priority 
7 
Global Time Synchronization 
Network Control 
6 
Reserved 
- 
5 
Inter ECU communication 
High priority 
4 
Streaming data (Audio/Video) 
High priority 
3 
Inter-/Intra ECU communication 
Medium priority 
2 
Inter-/Intra ECU communication 
Low priority 
1 
Best effort traffic 
Best effort 
0 
External Access / Diagnostic / Measurement 
Best effort 
 
 
Table T*******a: Priority classes 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 18 页
MSS 20208, V2.0, 2018-08, page 18 
Copyright Daimler AG 2018 
6.1.3 
IP addressing 
******* 
IP addresses for vehicle internal communication 
MSS 20208-138: 
IPv4 shall be supported for vehicle internal communication. 
MSS 20208-139: 
IP addresses for vehicle internal communication shall be assigned statically. 
MSS 20208-140: 
IP addresses for vehicle internal communication are provided with the ECU Extract. 
******* 
IP addresses for vehicle external communication  
MSS 20208-142: 
The IP address assignment strategy for external communication is stipulated in 
[DIAG_COMSYS] and [DIAG_R01_02]. 
MSS 20208-143: 
The IP address assignment methods, priorities and, if applicable, IP addresses, are 
provided with the ECU Extract. 
******* 
IP addresses for ECU internal communication 
MSS 20208-145: 
Additional IP addresses for ECUs with multiple internal interfaces (e.g. configuration 
in figure F*******a) shall be obtained at the Vehicle Networking Group. 
MSS 20208-146: 
Additional IP addresses are not part of the provided ECU Extract. 
MSS 20208-147: 
 
Switch
ECU 1
A1
I2
I1
µC1
µC2
Vehicle 
communication
IP address not in 
ECU extract
ECU 1
I1
µC1
IP address in 
ECU extract
ECU description
ECU 
implementation
ECU internal 
communication
 
Figure F*******a: IP addressing for multiple interfaces 
 
******* 
IP addresses for multiple ECUs 
MSS 20208-149: 
The IP addresses for the different ECU instances (e.g. configuration in figure 
F*******a) are assigned by the Vehicle Networking Group and are provided with the 
ECU Extracts of the clients. 
MSS 20208-150: 
IP addresses for vehicle internal and, if applicable, vehicle external communication, 
for multiple ECUs shall be assigned during the MAC address configuration process. 
Refer to the section ******* "MAC addresses for internal interfaces for multiple 
ECUs". 
******* 
Address Resolution 
MSS 20208-794: 
At link init time the MAC destination address for a frame is not yet known by the 
sender. 
There are different possibilities to establish the Address resolution table, e.g. 
sending an ARP-Request or by using the source addresses of incoming Service 
Discovery messages. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 19 页
MSS 20208, V2.0, 2018-08, page 19 
Copyright Daimler AG 2018 
MSS 20208-927: 
The ARP section gives advice relating to the ARP behaviour (ARP = Address 
resolution protocol) in addition to the AUTOSAR 4.x standard and IETF RFC 826, 
IETF RFC 3927 section 2.5. 
MSS 20208-949: 
For AUTOSAR Integration Requirments in this section refer to [AUTOSAR_INT]. 
*******.1 
ARP-Request 
*******.1.1 General 
MSS 20208-930: 
If the ARP table of a ECU does not contain the necessary IP/MAC-Address pair 
entry, an ARP request shall be sent by the ECU. 
MSS 20208-931: 
An entry in the ARP table expires after <TcpIpArpTableEntryTimeout> and gets 
removed. To avoid further ARP requests/replies the value of 
<TcpIpArpTableEntryTimeout> shall be set to max/infinite. 
MSS 20208-932: 
In case of a IP-Address change (e.g. startup, reset, VLAN change) the ECU shall 
send not more than one Gratuitous ARP reply. The corresponding AUTOSAR 
parameter TcpIpArpNumGratuitousARPonStartup shall be set to at most one (0 or 
1).  
MSS 20208-933: 
The Gratuitous ARP reply shall contain the sender´s MAC/IP-Address as target 
hardware and protocol address. 
MSS 20208-934: 
The VLAN-PCP is derived from the configured default value of the corresponding 
VLAN, see ECU extract   
MSS 20208-935: 
Optional: The ARP table should be extended with a new IP/MAC-Address pair entry 
by analyzing Service Discovery-Frames for their Source IP/MAC-Adress, too.  
*******.1.2 Timing 
MSS 20208-937: 
Different ARP requests coming from one ECU may be sent in one burst without a 
dedicated distance in time, i.e. back-to-back is allowed within one ARP burst. 
MSS 20208-938: 
If not specified in  [AUTOSAR_INT] the bursts shall have a cycle time of TArpCycleTime 
= 100ms, see figure F*******.1.2. 
MSS 20208-940: 
The parameter TArpCycleTime shall be configurable. 
MSS 20208-941: 
 
ARP-Req
für IP-Adr 1
ARP-Req
für IP-Adr 2
…
ARP-Req
für IP-Adr x
ARP-Burst 1
…
ARP-Req
für IP-Adr 1
ARP-Req
für IP-Adr 2
…
ARP-Req
für IP-Adr x
ARP-Burst 2
…
TArpCycleTime
ARP-Req
für IP-Adr 1
ARP-Req
für IP-Adr 2
…
ARP-Req
für IP-Adr x
ARP-Burst n
 
Figure F*******.1.2: Timing of ARP requests 
*******.2 
ARP-Reply 
*******.2.1 General 
MSS 20208-944: 
Every Ethernet ECU shall respond on incoming ARP-Requests with an appropriate 
ARP reply. 
MSS 20208-945: 
The ARP reply shall contain the same VLAN tag as the request has (i.e. same 
VLAN-ID and VLAN-PCP). 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 20 页
MSS 20208, V2.0, 2018-08, page 20 
Copyright Daimler AG 2018 
*******.2.2 Timing 
MSS 20208-947: 
The ARP reply shall be sent back to requester not later than TArpResponseMax = 15ms. 
MSS 20208-948: 
The ARP reply shall be sent as  
a) broadcast for a IPv4 Link- Local address (***********/16) , see IETF RFC 3927 
section 2.5 
b) unicast for all other IPv4 addresses 
******* 
IPv6 
MSS 20208-152: 
If IPv6 should be used for dedicated use cases within the vehicle network, the 
Vehicle Networking Group shall be contacted. 
6.2 
Transport Protocols 
MSS 20208-154: 
The following transport protocols are available for vehicle communication. Other 
transport protocols shall only be used with the permission of the Vehicle Networking 
Group. 
6.2.1 
UDP 
MSS 20208-156: 
The User Datagram Protocol (UDP) is generally used for in-vehicle-communication. 
MSS 20208-157: 
UDP port definitions for vehicle internal communication are provided with the ECU 
Extract. 
MSS 20208-158: 
UDP ports for ECU internal communication shall be obtained at the Vehicle 
Networking Group. 
6.2.2 
UDP with IP Fragmentation 
MSS 20208-160: 
For dedicated use cases (e.g. transmission of large arrays) IP fragmentation can be 
used for the transmission of UDP frames where the resulting frame size would 
exceed the size of an Ethernet frame. In this case the UDP frame is transmitted 
within two or more IP frames. This handling is transparent to the upper layers. 
6.2.3 
TCP 
MSS 20208-162: 
The transmission control protocol (TCP) is used for segmented unicast vehicle 
communication. 
MSS 20208-163: 
TCP port definitions for vehicle internal communication are provided with the ECU 
Extract. 
MSS 20208-164: 
TCP ports for ECU internal communication shall be obtained at the Vehicle 
Networking Group. 
6.2.4 
IEEE 1722 
MSS 20208-166: 
IEEE 1722 can be used for the streaming of audio/video data. Refer to [IEEE1722] 
for more information. 
MSS 20208-167: 
The use of IEEE 1722 shall be agreed with the Vehicle Networking Group 
MSS 20208-168: 
The use of IEEE 1722 is not directly supported by the Vehicle Networking Group and 
the Network Communication Description. 
MSS 20208-169: 
In case IEEE 1722 is used, following information shall be provided to the Vehicle 
Networking Group: 
 
- communication endpoints 
 
- communication bandwidth 
 
- periodicity, length and timing of frames 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 21 页
MSS 20208, V2.0, 2018-08, page 21 
Copyright Daimler AG 2018 
MSS 20208-170: 
IEEE 1722 communication shall use the VLAN-ID and priority class assigned by the 
Vehicle Networking Group. 
6.3 
Switches 
6.3.1 
Switch categories and description 
MSS 20208-173: 
From the networking description point of view there are three different categories of 
switches: 
******* 
Switches for vehicle communication 
MSS 20208-175: 
Switches for vehicle communication and one port for the hosting ECU (Example 
depicted in figure F*******a). 
MSS 20208-176: 
 
Switch 1
ECU 1
B1
C1
D1
A1
I
 
 
Figure F*******a: Switches for vehicle communication 
 
MSS 20208-177: 
The switch configuration is part of the provided ECU Extract for the hosting ECU. 
******* 
Switches for vehicle communication and ECU internal communication 
MSS 20208-179: 
Switches for vehicle communication and multiple ports for inter-processor 
communication within one ECU (Example depicted in figure F*******a). 
MSS 20208-180: 
 
Switch
ECU 1
A1
I2
I1
µC1
µC2
Vehicle 
communication
Switch
ECU 1
A1
I2
I1
µC1
µC2
Vehicle 
communication
B1
C1
D1
ECU internal 
communication
 
 
Figure F*******a: Switches for vehicle communication and ECU internal 
communication 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 22 页
MSS 20208, V2.0, 2018-08, page 22 
Copyright Daimler AG 2018 
 
 
MSS 20208-181: 
For the communication description the ECU is decomposed to one ECU which hosts 
the switch and additional ECUs connected to the switch (Example depicted in figure 
F*******b and F*******c). 
MSS 20208-182: 
 
Switch 1
ECU 1
I2
I1
µC1
µC2
B1
C1
D1
A1
Switch 1
ECU 1
A1
I2
I1
µC1
µC2
 
 
Figure F*******b: ECU implementation (1 port/multiple ports to the vehicle 
network) 
 
MSS 20208-183: 
 
Switch 1
ECU 1/1
I1
µC1
B1
C1
D1
A1
ECU 1/2
I2
µC2
I2'
 
 
Figure F*******c: Resulting ECU descriptions after decomposition 
 
MSS 20208-184: 
The switch configuration is part of the provided ECU Extract of the switch host. 
MSS 20208-185: 
For the ECU internal communication following information shall be provided to the 
Vehicle Networking Group: 
 
- communication endpoints 
 
- communication bandwidth 
 
- periodicity, length and timing of frames 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 23 页
MSS 20208, V2.0, 2018-08, page 23 
Copyright Daimler AG 2018 
******* 
Switches for ECU internal communication 
MSS 20208-187: 
Switches only used for inter-processor communication within one ECU (Example 
depicted in figure F*******a). 
MSS 20208-188: 
 
Switch
ECU 1
A1
I2
I1
µC1
µC2
ECU 1
I1
µC1
Switch 
configuration not 
part of the ECU 
extract
ECU description
ECU 
implementation
Vehicle 
communication
ECU internal 
communication
 
 
Figure F*******a: Switches for ECU internal communication 
 
MSS 20208-189: 
The switch is not part of the communication design, therefore the switch 
configuration is not part of the provided ECU Extract. 
6.3.2 
Requirements for ECUs with internal communication via switches 
MSS 20208-191: 
ECU outbound vehicle communication shall originate only from the predefined MAC 
addresses. 
MSS 20208-192: 
The vehicle communication shall not be disturbed by the ECU internal 
communication. 
MSS 20208-193: 
ECU internal communication shall not be visible on the vehicle network by using a 
VLAN. The VLAN-ID for internal communication shall be obtained at the Vehicle 
Networking Group. 
6.3.3 
General switch requirements 
******* 
Switch configuration handling 
MSS 20208-196: 
An unconfigured switch shall not switch any frames. 
MSS 20208-197: 
The switch configuration shall only be performed or changed via the Host-CPU 
Interface or a switch-attached nonvolatile memory.  
MSS 20208-198: 
The switch configuration shall not be changed via a switch chip-internal CPU. 
MSS 20208-199: 
A change of the switch configuration shall be protected by an authorized diagnostic 
routine. 
MSS 20208-200: 
Switch configuration parameters, which are not provided via the ECU Extract (e.g. 
chip manufacturer dependent parameters), shall be derived from high level 
requirements in this specification. 
MSS 20208-201: 
Other switch configuration parameters, which are not provided via the ECU Extract 
and are not derived from this specification, have to be agreed by the Vehicle 
Networking Group at project start and if changes occur. 
MSS 20208-202: 
All switch configuration parameters shall be sent to the Vehicle Networking Group for 
the first release or if changes occur. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 24 页
MSS 20208, V2.0, 2018-08, page 24 
Copyright Daimler AG 2018 
******* 
Switch configuration requirements 
MSS 20208-204: 
The aging time for address table entries in switches shall be set to 
EthSwtArlTableEntryTimeout (refer to [SWS_EthSwt_Drv] and [AUTOSAR_INT]). 
 
MSS 20208-205: 
Rate limitations for dedicated switch ports shall be configured if required from the 
Vehicle Networking Group. 
MSS 20208-798: 
Jumbo frames shall be dropped by the switch, refer also to [MSS21001-R115]. 
 
******* 
Switch communication behavior 
MSS 20208-207: 
An ECU with integrated switch shall meet the wake-up/startup timing requirements 
stipulated in the section [Communication behavior]. 
MSS 20208-208: 
Remark: The startup of the switch can be parallelized to the ECU startup/initialization 
by loading the switch configuration from a switch-attached nonvolatile memory (if 
supported by the switch). 
******* 
Global Time Synchronization for switch ECUs 
MSS 20208-210: 
The Global Time Synchronization shall be implemented according to [MSS21001]. 
******* 
Port mirroring 
MSS 20208-212: 
ECUs containing switches for vehicle communication shall support the feature Port 
mirroring with filtering depending on ports, source MAC address, destination MAC 
address and VLAN membership. Refer also to section 7.1. 
MSS 20208-213: 
The activation, configuration and deactivation of the Port mirroring shall be possible 
during normal operation and shall not disturb the vehicle communication. 
MSS 20208-214: 
The activation, configuration and deactivation of the Port mirroring shall only be 
possible by using an authorized diagnostic routine. 
MSS 20208-215: 
It shall be possible to define the uplink port for mirroring. 
MSS 20208-216: 
The mirroring traffic shall be tagged with a dedicated mirroring VLAN-ID and priority. 
The VLAN-ID and priority is assigned by the Vehicle Networking Group. 
MSS 20208-217: 
If double tagging is available for Port mirroring, an outer tag with the dedicated 
mirroring VLAN-ID and priority shall be added to mirroring traffic (refer also to 
section *******.). 
MSS 20208-218: 
If double tagging is not available for Port mirroring, the VLAN-ID and priority of 
mirroring traffic shall be changed to the dedicated mirroring VLAN-ID and priority. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 25 页
MSS 20208, V2.0, 2018-08, page 25 
Copyright Daimler AG 2018 
6.4 
Communication behavior 
6.4.1 
AUTOSAR Ethernet UDP NM parametrization 
MSS 20208-221: 
Refer to [MSS 20200] for general AUTOSAR Network Management (NM) 
requirements. 
MSS 20208-222: 
The AUTOSAR Network Management schedule and parametrization is based on 
following constraints: 
MSS 20208-223: 
 
UDPNM_TIMEOUT_TIME   
= 2.5 ⋅ UDPNM_MSG_CYCLE_TIME 
MSS 20208-224: 
 
UDPNM_REPEAT_MESSAGE_TIME  = 3 ⋅ UDPNM_MSG_CYCLE_TIME 
MSS 20208-225: 
The parametrization and configuration requirements for the AUTOSAR Ethernet 
Network Management in the table T6.4.1.1a shall be used. The NCD can define 
differing values which supersede the predefined values. 
MSS 20208-226: 
 
NM Parameters 
[AUTOSAR name] 
Description 
Value 
UDPNM_MSG_CYCLE_TIME 
Transmission period for NM frames 
1000 ms 
UDPNM_MSG_CYCLE_OFFSET 
Node specific offset for the transmission of NM frames 
0ms 
UDPNM_REPEAT_MESSAGE_TIME 
Time after “Repeat Message” state is left 
3000 ms 
UDPNM_TIMEOUT_TIME 
Time after “Prepare Bus Sleep” is entered 
2500 ms 
UDPNM_WAIT_BUS_SLEEP_TIME 
Time after “Bus Sleep” is entered 
1500 ms 
UDPNM_REMOTE_SLEEP_IND_TIME 
Time after a node is notified when the node is the only one in 
the network that requests bus communication. 
2500 ms1 
UDPNM_IMMEDIATE_NM_TRANSMISSIONS Number of immediate NM transmissions for updated PNC data 
6 2 
UDPNM_IMMEDIATE_NM_CYCLETIME 
Cycle time for immediate NM transmissions 
20 ms2 
1 Applicable for gateways 
2 Applicable for ECUs using PNC functionality 
 
 
Table T6.4.1.1a: Network Management parametrization requirements 
 
6.4.2 
Wake-up/sleep mode for ECUs with permanent power supply 
******* 
Active wake-up for ECUs with permanent power supply 
MSS 20208-230: 
An ECU shall only wake-up the Ethernet network if Ethernet communication is 
necessary for the processing of functionality. Otherwise functions shall be handled 
locally without any Ethernet communication. 
MSS 20208-231: 
Any local wake-up event shall be checked for plausibility before the connected 
networks are woken up.  
MSS 20208-232: 
An activation of the power supply shall not trigger a wake-up pulse. Exceptions shall 
be agreed with the Vehicle Networking Group. 
MSS 20208-233: 
The application shall transmit the wake-up reason in the ECU-State PDU, refer to 
[MSS 20200]. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 26 页
MSS 20208, V2.0, 2018-08, page 26 
Copyright Daimler AG 2018 
6.4.2.2 
Passive wake-up for ECUs with permanent power supply 
MSS 20208-235: 
Only a valid wake-up pulse on the wake-up line shall trigger a wake-up. 
MSS 20208-236: 
After wake-up an ECU shall initialize the Ethernet interface as soon as possible. 
Refer to the section ******* "Timing requirements for ECUs with permanent power 
supply" 
6.4.2.3 
Network sleep mode for ECUs with permanent power supply 
MSS 20208-238: 
Generally ECUs shall enable network sleep as soon as possible. 
MSS 20208-239: 
After startup of the networks ECUs shall prevent network sleep for not less than 
TEthernetStartupSleepDelayMin and transmit the stay awake reason "Awake_NwSt".  
MSS 20208-240: 
Subsequently ECUs without local stay awake reason shall enable network sleep 
within TEthernetStartupSleepDelayMax. 
MSS 20208-241: 
ECUs shall transmit the reasons which prevent enabling network sleep (stay awake 
reasons). 
MSS 20208-242: 
ECU limp home modes shall not result in preventing network sleep infinitely. 
MSS 20208-243: 
ECU internal communication (e.g. interprocessor communication) via an internal 
switch shall not affect network sleep. 
******* 
Timing requirements for ECUs with permanent power supply 
MSS 20208-245: 
ECUs shall follow the sleep timing stipulated in figure F*******a and table T*******a. 
MSS 20208-246: 
 
TEthernetLinkInit
powerdown/reset/local mode
Ethernet 
node
init
config
link init 
TEthernetInit
TEthernetNetworkStartup
TEthernetLinkInit
powerdown/
reset
Ethernet 
node
Ethernet link operational
local wakeup 
event
config
link init 
link init
(wait for link partner )
init
Ethernet link operational
trigger wakeup 
line
 
Figure F*******a: Timing requirements for ECUs with permanent power supply 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 27 页
MSS 20208, V2.0, 2018-08, page 27 
Copyright Daimler AG 2018 
MSS 20208-247: 
 
Name 
Definition 
Min  
[ms] 
Max1 
[ms] 
TEthernetInit 
Time from the triggering of a wakeup event/power on to 
the completed Ethernet initialization of an ECU so that the 
link training can be initiated 
- 
1202 
TEthernetLinkInit 
Time for establishing the Ethernet link (link training). Not 
applicable for 100BASE-TX links. 
- 
1002 
TEthernetNetworkStartup 
Time from the triggering of a wakeup event/power on to 
all Ethernet links (not applicable for 100BASE-TX links) 
have been established. The communication stack has to 
be initialized so that Network Management, Service 
Discovery and functional communication can be 
transmitted and processed 
- 
2202 
TEthernetStartupSleepDelay 
Time period within the application shall enable network 
sleep after the startup of the network, if the application 
doesn’t require Ethernet communication. 
The time starts with the end of TEthernetNetworkStartup 
3000 
6000 
1 ECU or system software requirement spec can require more stringent timing for initialization 
2 Timings shall be as fast as possible 
 
 
Table T*******a: Timing requirements for ECUs with permanent power supply 
6.4.3 
Startup/shutdown for ECUs with switched power supply 
6.4.3.1 
Network startup for ECUs with switched power supply 
MSS 20208-250: 
After power on an ECU with switched power supply shall start the Ethernet 
communication as soon as possible. Refer to the section ******* "Timing 
requirements for ECUs with switched power supply". 
MSS 20208-251: 
During and after power on an ECU with switched power supply shall not trigger the 
wake-up line. 
******* 
Network shutdown for ECUs with switched power supply 
MSS 20208-253: 
The Ethernet communication shall end within TEthernetAfterrun after the power supply is 
switched off. Refer to the section ******* "Timing requirements for ECUs with 
switched power supply". 
MSS 20208-254: 
The wake-up line shall not be triggered during and after the power supply is switched 
off. 
******* 
Timing requirements for ECUs with switched power supply 
MSS 20208-256: 
ECUs shall follow the startup timing stipulated in figure F*******a and table 
T*******a. 
MSS 20208-257: 
 
TEthernetLinkInit
power off/
reset
Ethernet 
node
Ethernet link operational
power 
switched on
init
config
link init 
TEthernetInit
TEthernetNetworkStartup
power 
switched off
link down
afterrun
TEthernetAfterrun
 
Figure F*******a: Timing requirements for ECUs with switched power supply 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 28 页
MSS 20208, V2.0, 2018-08, page 28 
Copyright Daimler AG 2018 
MSS 20208-258: 
 
Name 
Definition 
Max1 
[ms] 
TEthernetInit 
Time from the triggering of a wakeup event/power on to the 
completed Ethernet initialization of an ECU so that the link training 
can be initiated 
1202 
TEthernetLinkInit 
Time for establishing the Ethernet link (link training). Not applicable 
for 100BASE-TX links. 
1002 
TEthernetNetworkStartup 
Time from the triggering of a wakeup event to all Ethernet links (not 
applicable for 100BASE-TX links) have been established. The 
communication stack has to be initialized so that Network 
Management, Service Discovery and functional communication can 
be transmitted and processed 
2202 
TEthernetAfterrun 
Time from the power is switched off to the end of Ethernet 
communication and shutting down the Ethernet link3 
5002 
1 ECU or system software requirement spec can require more stringent timing 
2 Timings shall be as fast as possible 
3 Rationale: ECUs can be capable of sustaining their power supply 
 
 
Table T*******a: Timing requirements for ECUs with switched power supply 
 
6.4.4 
Service Discovery 
******* 
Service Discovery configuration 
MSS 20208-261: 
Service Discovery (SD) is used to establish communication relationships for the 
functional communication between ECUs during runtime. 
MSS 20208-262: 
ECUs shall support Service Discovery if configured in the Network Communication 
Description. 
MSS 20208-263: 
By default services should be configured "auto available" on server side and "auto 
required" on client side. 
MSS 20208-264: 
For services controlled by the application following information shall be provided to 
the Vehicle Networking Group: 
 
- Conditions and timing of service start 
 
- Conditions and timing of service stop 
******* 
Service Discovery behavior for auto available services 
MSS 20208-266: 
Services which are configured "auto available" shall be offered within TSD_InitialOffer 
after the network startup is completed. Refer to table T*******a. 
MSS 20208-267: 
 
Name 
Definition 
Max1 [ms] 
TSD_InitialOffer 
Time from the end of TEthernetNetworkStartup to the transmission of the 
first SD offer for a dedicated service  
202 
1 ECU or system software requirement spec can require more stringent timing, see ARXML of ECU 
2 Timings shall be as fast as possible 
 
 
Table T*******a: Service Discovery timing of auto available services 
 
******* 
Service Discovery behavior for application controlled services 
MSS 20208-269: 
Services controlled by the application should be offered as fast as possible. 
MSS 20208-270: 
If services controlled by the application get unavailable the service shall be properly 
stopped. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 29 页
MSS 20208, V2.0, 2018-08, page 29 
Copyright Daimler AG 2018 
6.4.5 
Network Operation 
******* 
Cycle time tolerances 
MSS 20208-1232: The average cycle time (tc and tc2) of all PDUs for both, a standalone ECU and 
ECUs in the network, shall be within ± 2% of the specified PDU cycle time in the 
NCD 
MSS 20208-1233: The absolute deviation of the cycle time shall not exceed the values stipulated in 
table T *******a 
MSS 20208-1234:  
 
 
PDU 
 
Container PDU 
Always 
Never 
Default Trigger 
Cycletime > 10ms: 
[Cycletime - 10ms ; Cycletime + 10ms] 
Cycletime ≤ 10ms: 
[Cycletime - 5ms ; Cycletime + 5ms] 
Cycletime > 10ms: 
[Cycletime - 10ms ; Cycletime + TPDU_Collection + 10ms]* 
Cycletime ≤ 10ms: 
 [Cycletime - 5ms ; Cycletime +  TPDU_Collection + 5ms]* 
First Contained 
PDU Triggers 
Cycletime > 10ms: 
[Cycletime - 10ms ; Cycletime + 10ms] 
Cycletime ≤ 10ms: 
 [Cycletime - 5ms ; Cycletime + 5ms] 
Cycletime > 10ms: 
[Cycletime - 10ms ; Cycletime + 10ms] 
Cycletime ≤ 10ms: 
 [Cycletime - 5ms ; Cycletime + 5ms] 
*TPDU_Collection = Min(TMaxTxDelayFrame, TMaxTxDelayPDU)  
 
Table T*******a: Absolute deviation of the cycle time 
******* 
Influence of PNCs on communication behavior 
MSS 20208-804: 
All ECUs are part of at least one PNC. 
MSS 20208-805: 
One ECU can be a member of more than one PNC. 
MSS 20208-806: 
An ECU only sends PDUs if at least one of its associated PNCs is active. ECUs with 
no active PNC on this network will stay in a non-sending mode. 
MSS 20208-807: 
Thus it is possible to have network communication where only a subset of all ECUs 
will take part. 
MSS 20208-808: 
For further details see AUTOSAR ComM specification. 
6.4.5.3 
Uninterrupted Communication 
MSS 20208-273: 
Between the completion of the network startup phase and the beginning of prepare 
bus-sleep state an ECU shall not perform any activities e.g. reset, resulting in 
− 
missing frames 
− 
restart of the link training 
− 
restart of the Network Management 
MSS 20208-274: 
However, the above requirements do not apply to the ECU in the following events: 
− 
Activities which are explicitly triggered by the execution of diagnostic services,  
 
e.g. ECU reset 
− 
Detection of an error or fault which requires the above mentioned behavior.  
 
Refer to relevant ECU or system software requirement specification 
6.4.5.4 
Reset 
MSS 20208-276: 
ECUs shall be capable of coming out of reset and resume the current operation 
without requiring power on reset or a wake-up pulse. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 30 页
MSS 20208, V2.0, 2018-08, page 30 
Copyright Daimler AG 2018 
MSS 20208-796: 
According to [MBN 10567] ECUs shall be capable to resume the current operation 
out of a short power supply disconnection without requiring additional actions, e.g. 
wake-up pulse. 
MSS 20208-277: 
An ECU shall not trigger a wake-up pulse because of a reset. 
MSS 20208-278: 
A watchdog/soft reset at a switch host shall not influence the switch communication 
behavior, i.e. a running switch shall only be reconfigured if necessary. 
******* 
Communication loss handling 
MSS 20208-280: 
In case of a communication loss an ECU shall attempt to re-establish communication 
for at least TEthernetComLossRecoveryMin. Refer to figure F*******a and table T*******a.  
MSS 20208-281: 
If the communication can´t be re-established within TEthernetComLossRecoveryMin, the 
application of an ECU with permanent power supply shall decide if it requires 
Ethernet communication or if it can switch to a local mode or power down. 
MSS 20208-282: 
If TEthernetComLossRecoveryMax has expired, ECUs shall stop the attempts to re-establish 
communication. 
MSS 20208-283: 
 
 
 
Figure F*******a: Communication loss handling 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 31 页
MSS 20208, V2.0, 2018-08, page 31 
Copyright Daimler AG 2018 
MSS 20208-284: 
 
Name 
Definition 
Value [s] 
TEthernetComLossRecoveryMin 
Minimal time an ECU attempts to re-establish 
communication after a detected communication loss 
31 
TEthernetComLossRecoveryMax 
Maximal time an ECU attempts to re-establish 
communication after a detected communication loss 
3001 
1 ECU or system software requirement spec can require differing timing 
 
 
Table T*******a: Communication loss timing 
 
6.4.5.6 
Voltage ranges 
MSS 20208-286: 
 For coding "A" (Funktionsklasse A) ECUs a proper Ethernet communication shall be 
guaranteed within the defined supply voltage ranges defined in [MBN 10567]. 
MSS 20208-287: 
If Ethernet communication has to be continued below or above these limits because 
of functional requirements it shall run without errors. 
6.4.5.7 
Wake-Up 
MSS 20208-1245: A local or remote wake-up of a communication interface shall lead to a wake-up of 
all communication interfaces connected to that ECU. 
MSS 20208-1246: A deviation from this requirement has to be agreed by the Vehicle Networking Group 
at project start and if changes occur. 
MSS 20208-1247: This behavior is defined by AUTOSAR with parameter 
“ComMSynchronousWakeUp”, refer to “Integration Requirements AUTOSAR 4.x“ 
[AUTOSAR_INT]. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 32 页
MSS 20208, V2.0, 2018-08, page 32 
Copyright Daimler AG 2018 
7 
Ethernet MAC Layer 
7.1 
Switch hardware requirements 
7.1.1 
General Requirements 
MSS 20208-291: 
The switch shall fulfill the requirements according to section "General Requirements 
(GEN)" in [OPEN_SWITCH]. 
 
7.1.2 
Address Resolution Requirements 
MSS 20208-294: 
The switch shall fulfill the requirements according to section "Address Resolution 
(ADDR)" in [OPEN_SWITCH]. 
7.1.3 
VLAN Requirements 
MSS 20208-297: 
The switch shall fulfill the requirements according to section "Virtual LANs (VLAN)" in 
[OPEN_SWITCH]. 
 
7.1.4 
Quality of Service Requirements 
MSS 20208-300: 
The switch shall fulfill the requirements according to section "Quality of Service and 
Audio/Video Bridging (QOS)" in [OPEN_SWITCH]. 
 
7.1.5 
Ingress Filtering Requirements 
MSS 20208-303: 
The switch shall fulfill the requirements according to section "Filtering of Incoming 
Frames (FILT)" in [OPEN_SWITCH]. 
 
7.1.6 
Diagnostics Requirements 
MSS 20208-306: 
The switch shall fulfill the requirements according to section "Diagnostics (DIAG)" in 
[OPEN_SWITCH]. 
 
7.1.7 
Interface Requirements 
MSS 20208-309: 
The switch shall fulfill the requirements according to section "Interfaces (INTF)" in 
[OPEN_SWITCH]. 
 
7.1.8 
Configuration Requirements 
MSS 20208-312: 
The switch shall fulfill the requirements according to section "Configuration (CONF)" 
in [OPEN_SWITCH]. 
 
7.1.9 
Time Synchronization Requirements 
MSS 20208-315: 
The switch shall fulfill the requirements according to section "Time Synchronization 
(TIME)" in [OPEN_SWITCH]. 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 33 页
MSS 20208, V2.0, 2018-08, page 33 
Copyright Daimler AG 2018 
7.1.10 Audio Video Bridging Requirements 
MSS 20208-318: 
If the ECU has to support AVB features (refer to component requirements 
specification) the following specifications have to be fulfilled: 
- IEEE 802.1AS: Timing and Synchronization for Time-Sensitive Applications 
(gPTP), 
- IEEE 802.1Qat: Stream Reservation Protocol (SRP), 
- IEEE 802.1Qav: Forwarding and Queuing for Time-Sensitive Streams, und 
- IEEE 802.1BA: Audio Video Bridging Systems 
- IEEE 802.1Q: Media Access Control (MAC) Bridges and Virtual Bridge Local Area 
Networks 
 
For detailed requirements on AVB features refer to component requirements 
specification. 
 
7.1.11 Additional Requirements 
MSS 20208-321: 
The switch shall fulfill the following additional requirements: 
MSS 20208-322: 
It shall be possible to read out the switch configuration. 
 
7.2 
Switch test requirements 
MSS 20208-324: 
All switches (standalone or µC-integrated) used in combination with a 100BASE-T1 / 
1000BASE-T1 interface shall be tested by an independent test house according to 
the OPEN Alliance Switch Requirements [OPEN_SWITCH] and their test 
specification [OPEN_SWITCH_TEST]. 
MSS 20208-325: 
The above mentioned tests shall be fulfilled and the test results shall be provided to 
the Vehicle Networking Group. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 34 页
MSS 20208, V2.0, 2018-08, page 34 
Copyright Daimler AG 2018 
8 
Ethernet Physical Layer implementations 
MSS 20208-327: 
For Automotive Ethernet the physical layer implementations shall be used according 
to table T8a. 
MSS 20208-328: 
 
Use case 
Physical layer implementation 
Diagnostic access via OBD connector 
100BASE-TX1 
In vehicle networking 
100BASE-T12, 1000BASE-T13 
1 A 100BASE-TX interface includes the 100BASE-TX Pins and the activation line pin. 
2 A 100BASE-T1 interface includes the 100BASE-T1 pins and the wake-up line pin. 
3 A 1000BASE-T1 interface includes the 1000BASE-T1 pins and the wake-up line pin. 
 
 
Table T8a: Use cases for Ethernet physical layer implementations 
 
MSS 20208-329: 
Cable harness requirements for 100BASE-T1 Links are described in section "Cable 
harness for 100BASE-T1 Links" and  "Cable harness for 1000BASE-T1 Links". 
MSS 20208-1019: Cable harness requirements for 1000BASE-T1 Links are described in section "Cable 
harness for 1000BASE-T1 Links". 
MSS 20208-952: 
Wake-up Line requirements  for 100BASE-T1 / 1000BASE-T1 Links are described in 
section "Wake-up Line Requirements for 100BASE-T1 / 1000BASE-T1 Links" 
8.1 
100BASE-T1 ECU Interface 
MSS 20208-331: 
The ECU shall be designed to fulfill the requirements given in [MBN 10567] 
according to the associated Component Requirement Specification. ECUs with 48V 
supply shall fulfill [VDA320]. 
MSS 20208-332: 
Because of the coexistence of 12V as well as 48V supplied ECUs in the vehicle: 
ECUs which are supplied solely from the 48V system shall not damage other ECUs 
of the Ethernet network in case of ground loss according to [VDA320]. 
8.1.1 
Physical Layer Interface 
MSS 20208-334: 
In this section the requirements for the interfacing of the 100BASE-T1 Transceiver in 
the ECU are specified.  
MSS 20208-335: 
For all points that are not completely described in this document (e.g. supply 
voltages), the definitions of data sheets and application hints of the respective 
semiconductor manufacturers shall be fulfilled. 
MSS 20208-336: 
All values listed in this document shall be valid within the specified temperature 
range (e.g. -40°C … +85°C / 105°C / 125°C) of the ECU (refer to the Component 
Specification of the affected ECU).  
8.1.1.1 
Evaluation/Testing Capabilities 
MSS 20208-338: 
ECUs implementing a 100BASE-T1 interface shall fulfill the requirements of [MBN 
10567] (resp. [VDA320] if applicable). 
MSS 20208-339: 
ECUs implementing a 100BASE-T1 interface shall fulfill the requirements of [MBN 
10284-1] and [MBN 10284-2]. 
MSS 20208-340: 
To allow evaluation, 100BASE-T1 Test Mode functionality with Test Modes 1 to 5 
(refer to [IEEE802.3bw]) shall be implemented for each 100BASE-T1 interface of the 
ECU.  
MSS 20208-341: 
The ECU shall be able to deactivate data transmission (e.g. “TX off” or “Scrambler 
off”) of each 100BASE-T1 interface to allow MDI return loss and MDI Mode 
conversion reflection test with proper termination inside the Transceiver (i.e. 100 Ω). 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 35 页
MSS 20208, V2.0, 2018-08, page 35 
Copyright Daimler AG 2018 
8.1.1.2 
100BASE-T1 Interface Circuit 
MSS 20208-343: 
A 100BASE-T1 interface shall be implemented according to [IEEE802.3bw]. 
MSS 20208-344: 
The interface consists of the Transceiver block (which includes the Transceiver, 
optional ESD protection devices and supply filtering all according to the datasheet 
and application notes of the Transceiver manufacturer), common mode choke, DC 
block capacitors, common mode termination network and the ECU connector. 
MSS 20208-345: 
All components of the 100BASE-T1 interface shall be implemented as required in 
table T8.1.1.3a. 
MSS 20208-346: 
For layout design information, the application notes of semiconductor manufacturers 
shall be considered. 
MSS 20208-347: 
For short start up time it may be necessary to use hardware strap-in pins for phy 
configuration. 
MSS 20208-348: 
A review of the interface layout by the semiconductor manufacturer shall be 
conducted. Review results shall be documented and reported to the Vehicle 
Networking Group. 
MSS 20208-1222: For new ECU developments only PHYs / Switches without requiring an external low 
pass filter are allowed, see section ******* Transceiver Requirements. 
MSS 20208-350: 
Further implementation recommendations for 100BASE-T1 interfaces and ECUs are 
referenced in section "Design hints for ECUs with 100BASE-T1 / 1000BASE-T1 
interface(s)". 
MSS 20208-351: 
 
ETH_P
ETH_N
PGND
R1
R2
C1
C2
C3
L1
VBat
GND
PGND
CMT
DC-
Block
CMC
Transceiver Block
DGND
VDDX
CMC
Common Mode Choke
CMT
Common Mode Termination
BIN
Bus Interface Network
ESD
ESD suppression device
TC
100BASE-T1 Transceiver (PHY/Switch)
Filter
Supply Filtering
OSC
Oscillator/Crystal
MAC
Media Access Control
MII
Media Independent Interface
VDDX
is representing all necessary Voltage supplies of 
the transceiver including their necessary filtering 
according to datasheet and application notes
Supply
ESD_2
Filter
Filter
µController
MAC
OSC
TC
R3
ESD_1
PGND
ESD-
Protection
MII
BIN – Bus Interface Network
DGND
 
Figure F8.1.1.2a: Interface circuit for 100BASE-T1 interface 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 36 页
MSS 20208, V2.0, 2018-08, page 36 
Copyright Daimler AG 2018 
8.1.1.3 
Common 100BASE-T1 Transceiver Interface Component Data 
MSS 20208-354: 
 
Ref 
Part 
Remarks 
 
Transceiver Block 
The Transceiver block (Transceiver including ESD and LPF 
according to application notes) has to fulfill the requirements 
of the OPEN Alliance specifications (refer to 
[IEEE802.3bw]). Validated and released recommended 
Transceivers are listed in section “100BASE-T1 
Transceivers”.2 
L1 
common mode choke 
Has to fulfill the requirements according to OPEN Alliance 
[OPEN CMC]. Validated and recommended common mode 
chokes are listed in section “Common Mode Choke for 
100BASE-T1 Interfaces”. 
C1, C2 
capacitor 100nF 
tolerance <= 10% 
voltage range1 >= 50V 
 
R1, R2 
 
 
resistor 1kOhm 
matching tolerance 
<= 1% (even after ESD-Test) 
absolute tolerance <= 10% 
power rating >= 0.4W 
ESD resistance 
refer to 3 
Termination circuitry for common mode RF currents on the 
data line. Therefore, resistors need sufficient power loss and 
capacitors need sufficient electrical strength. 
 
Remark: To fulfill the requirement e.g. a series circuitry of 
two 510 ohm resistors at each line can be used. 
C3 
capacitor 4.7 nF 
tolerance <= 10% 
voltage range1 >= 50V 
ESD resistance 
refer to 3 
To avoid DC current flow (e.g. due to GND-shift). 
 
 
 
R3 
resistor 100kOhm 
tolerance <= 10% 
power rating >= 0.05W 
voltage range1 >= 50V  
ESD resistance 
refer to 3  
To avoid static charge of the cable harness. 
 
ECU connector 
Has to fulfill the requirements according to OPEN Alliance 
[OPEN_CC], refer also to section “ECU connectors 
for100BASE-T1 interfaces (MDI Interface)”. 
 
1 According to [VDA320] >=56V voltage range for 48V electrical systems, typically 100V min. electric strength 
recommended for ceramic caps. For 12V systems typ. 50V electrical strength is sufficient. 
  
2 It is required to follow the definitions of semiconductor manufacturer for external passive components and 
circuit, software configuration (script) and software flow definitions, which are defined in datasheet and 
application notes. The implementation of ESD protection elements is optional, but according layout is 
mandatory in any case. The implementation of Low Pass Filters for voltage supply is mandatory according to 
semiconductor manufacturer definition. 
 
3 BIN (R1, R2, R3, C3) components shall be chosen in order to fulfill the ESD requirements on MDI pins (BIN) 
according to table *******  
 
 
 
Table T8.1.1.3a: Mandatory 100BASE-T1 interface component and their 
requirements 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 37 页
MSS 20208, V2.0, 2018-08, page 37 
Copyright Daimler AG 2018 
8.1.2 
100BASE-T1 Transceivers (standalone or integrated in switch) 
MSS 20208-356: 
All Transceivers (PHYs or Switches) used in a 100BASE-T1 interface shall comply to 
IEEE 100BASE-T1 specification [IEEE802.3bw]. 
******* 
Transceiver requirements 
MSS 20208-357: 
All Transceivers (PHYs or Switches) used in a 100BASE-T1 interface shall fulfill the 
following requirements: 
MSS 20208-1223: - 
The PHY/switches shall have an integrated low pass filter, external low pass 
 
filters are not allowed. 
MSS 20208-358: 
-  
The PHY/switch shall support test modes acc. to 100BASE-T1 specification  
 
[IEEE802.3bw]. 
MSS 20208-359: 
-  
The PHY/switch shall support a mode which deactivates all signaling on the  
 
front-end, while the front-end termination is still active (e.g. called "TX_off" or  
 
"scrambler_off"). 
MSS 20208-360: 
-  
The PHY/switch shall provide a link status information via a register, according  
 
to [IEEE802.3]. 
MSS 20208-362: 
-  
The PHY/switch shall provide an information on CRC or symbol failures on  
 
physical layer level via a register. 
MSS 20208-838: 
- 
The PHY/switch shall provide diagnostic capabilities according to the 
 
definitions in OPEN Alliance, TC1: Advanced diagnostic features for 
 
100BASE-T1 automotive Ethernet PHYs [OPEN_DIAG]. 
MSS 20208-366: 
-  
Automatic polarity correction shall be supported (although only optional 
 
declaration in [OPEN_DIAG]). 
MSS 20208-1210: - 
Automatic polarity correction shall be enabled by default. 
MSS 20208-816: 
The following hardware configuration shall be configurable by hardware strap in pins: 
MSS 20208-1251: - 
Master / Slave configuration 
MSS 20208-1252: - 
Autonomous start of link training / host activated link training 
 
MSS 20208-1253: - 
Autonomous start of forwarding / host activated forwarding (applies only to 
switches) 
MSS 20208-867: 
All Transceivers (PHYs or Switches) used in a 100BASE-T1 interface shall fulfill the 
ESD-requirements listed in the following table T*******. 
MSS 20208-1224: These values shall be guaranteed in the datasheet of the respective semiconductor 
manufacturer. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 38 页
MSS 20208, V2.0, 2018-08, page 38 
Copyright Daimler AG 2018 
MSS 20208-874: 
 
Use-Case/Reference 
MDI  
(IC) 6 
Global Pins 
(ECU)  
[e.g. WAKE, 
VBAT] 
Local Supply 
Pins (IC) 
Local Control 
Pins /other 
local Pins 
(IC) 
Damage (unpowered) 
IEC61000-4-2 1,7 
+-6kV 
+-6kV 
- 
- 
HBM JESD22-A114 2 
+-4kV 
+-4kV 
+-2kV 
+-2kV 
CDM JESD22-C101 3 
+-500V 
+-500V 
+-500V 
+-500V 
SAEJ2962-2… 4 
- 
- 
- 
- 
Damage (powered) 
IEC61000-4-2 1,7 
+-6kV 
- 
- 
- 
SAEJ2962-2… 4 
- 
- 
- 
- 
SAEJ2962-2… 5 
- 
- 
- 
- 
No unwanted wake-up 
(powered) 8 
IEC61000-4-2 1,7 
+-3kV 
- 
- 
- 
No malfunction / no link loss 
(powered) 
IEC61000-4-2 1,7 
+-3kV 
- 
- 
- 
 
 
 
 
 
 
1 IEC61000-4-2 (330Ohm/150pF) - direct galvanic coupling 
2 Human Body Model JESD22-A114 / AEC-Q100-002 
3 Charge Device Model JESD22-C101 /AEC-Q100-011 
4 SAEJ2962-2…contact discharge 
5 SAEJ2962-2…air discharge 
6  MDI = Media Dependent Interface (Bus pins) : IEC61000-4-2 is tested with BIN (bus interface network) circuitry, all 
other tests directly at the pin. 
7 Tested at external Test house according to OPEN TC1 IEEE 100BASE-T1 EMC Test specification for transceivers version 
1.0 (FTZ Zwickau) 
The semiconductor is tested in combination with its external circuitry (e.g. bus interface network) 
The other semiconductor requirements have to be guaranteed by datasheet 
8 Only if wakeup function is available 
 
 
 
Table T*******: ESD Requirements 
 
MSS 20208-367: 
All Transceivers (PHYs or Switches) used in a 100BASE-T1 interface shall be tested 
by an independent test house according to the following test specifications. 
MSS 20208-368: 
- OPEN Alliance, TC1: IEEE 100BASE-T1 PHY Control Test Suite, Version 1.1 
[OPEN_PHYCTS] 
MSS 20208-369: 
- OPEN Alliance, TC1: IEEE 100BASE-T1 Physical Media Attachment Test Suite, 
Version 1.0 [OPEN_PMA] 
MSS 20208-370: 
- OPEN Alliance, TC1: IEEE 100BASE-T1 Physical Coding Sublayer Test Suite, 
Version 1.1 [OPEN_PCS] 
MSS 20208-371: 
- OPEN Alliance, TC1: 100BASE-T1 Interoperability Test Suite Interoperability Test 
Suite Specification, Version 1 [OPEN_IOP] 
MSS 20208-372: 
- OPEN Alliance, TC1: IEEE 100BASE-T1 EMC Test Specification for Transceivers, 
Version 1.0 [OPEN_EMC] 
 
MSS 20208-818: 
- OPEN Alliance, TC1: IEEE 100BASE-T1 System Implementation Specification, 
Version 1.0 [OPEN_SIS] 
MSS 20208-373: 
The requirements of the above Test Specifications (e.g. for EMC test specification 
recommended values in the appendix C) are valid for a bus interface network 
according to figure F8.1.1.2a. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 39 页
MSS 20208, V2.0, 2018-08, page 39 
Copyright Daimler AG 2018 
MSS 20208-1235: All tests shall be conducted in the identical configuration: 
- Same BIN, same die revision, no change of undocumented registers (no firmware 
patches or other patchcode). 
- Tested BIN shall be identical to the BIN defined in Figure F8.1.1.2a. 
MSS 20208-375: 
A Transceiver used in a 100BASE-T1 interface shall fulfill the entire above listed test 
specifications and the test results shall be provided to the Vehicle Networking Group. 
8.1.2.2 
Additional recommended transceiver features 
MSS 20208-866: 
The PHY/switch should provide wakeup capabilities according to the definitions in 
OPEN Alliance, TC10: Sleep/Wake-up Specification, Version 2.0 [OPEN_WODL]. 
8.1.2.3 
Recommended Single-PHY´s and switches for 100BASE-T1 interfaces 
MSS 20208-376: 
To be listed as recommended 100BASE-T1 component (table T8.1.2.3a and table 
T8.1.2.3b) the semiconductor manufacturer has to provide the following 
documentation to Vehicle Networking Group: 
MSS 20208-377: 
-  
complete test results of the OPEN Alliance Transceiver test specifications 
 
listed above (not in case of a PHY-less switch) 
MSS 20208-378: 
-  
datasheet 
MSS 20208-379: 
-  
additional information (referenced Application Notes, Software, etc.) 
MSS 20208-380: 
-  
validation of switch requirements [OPEN_SWITCH] and 
 
[OPEN_SWITCH_TEST], see section 7.2 "Ethernet MAC Layer" 
MSS 20208-381: 
-  
proof of successfully completed PPAP 
 
MSS 20208-385: 
The Single PHY Transceivers and switches listed in Tables T8.1.2.3a and T8.1.2.3b 
are recommended for use in 100BASE-T1 interfaces. 
MSS 20208-386: 
 
Supplier 
Type 
Comment 
BroadCom 
BCM89811 
 
Marvell 
88Q1010 
  
NXP 
TJA1101 (*) 
 
NXP 
TJA1102 (*) 
Dual Phy 
Realtek 
RTL9000AA/AN 
 
TI 
DP83TC811R/S 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
(*) only allowed for usage in ECUs which are not exposed to 
high ESD pulses during operation 
 
 
Table T8.1.2.3a: Recommended 100BASE-T1 Single-PHY Transceivers 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 40 页
MSS 20208, V2.0, 2018-08, page 40 
Copyright Daimler AG 2018 
MSS 20208-387: 
 
Supplier 
Type 
Comment 
BroadCom 
BCM89231 
 
BroadCom 
BCM89530 
 
BroadCom 
BCM89531 
 
BroadCom 
BCM89535 
 
Marvell 
88Q5050 
 
NXP 
SJA1105P/Q/R/S 
 
Realtek 
RTL9047AA 
 
Realtek 
RTL9044AA/AB 
 
Realtek 
RTL9043AA 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Table T8.1.2.3b: Recommended 100BASE-T1 Switches 
 
8.1.3 
Common Mode Choke for 100BASE-T1 interfaces 
MSS 20208-389: 
All Common Mode Chokes used in an 100BASE-T1 interface shall be tested by an 
independent test house according to the following test specifications: 
MSS 20208-390: 
- 
OPEN Alliance. TC1: IEEE 100BASE-T1 EMC Test Specification for Common 
 
Mode Chokes, Version 1.0 [OPEN_CMC] 
MSS 20208-391: 
The requirements of the EMC Test Specification (recommended values in the 
appendix B of [OPEN_CMC]) are mandatory. 
8.1.3.1 
Recommended Common Mode Choke for 100BASE-T1 interfaces 
MSS 20208-1225: To be listed as recommended 100BASE-T1 CMC (table T8.1.3.1a) the CMC vendor 
has to provide the following documentation to Vehicle Networking Group: 
 
-  
complete test results of the OPEN Alliance CMC test 
 
 
specification [OPEN_CMC] 
 
-  
datasheet 
 
-  
additional information (referenced Application Notes, etc.) 
 
-  
proof of successfully completed ppap 
MSS 20208-393: 
The Common Mode Chokes listed in table T8.1.3.1a are recommended for use in 
100BASE-T1 interfaces. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 41 页
MSS 20208, V2.0, 2018-08, page 41 
Copyright Daimler AG 2018 
MSS 20208-394: 
 
Supplier 
Type 
TDK 
ACT45L-201-2P 
TDK 
ACT1210L-201-2P 
Murata 
DLW43MH201XK2L 
 
 
 
 
 
 
Table T8.1.3.1a: Recommended 100BASE-T1 Data line Common Mode Chokes 
 
8.1.4 
ECU connectors for 100BASE-T1 interfaces (MDI interface) 
MSS 20208-396: 
The complete channel as well as all individual components (cables and connectors 
as well as assembled cable harness) of a 100BASE-T1 link shall fulfill the channel 
requirements according to [OPEN_CC]. 
MSS 20208-397: 
Therefore all components of the link shall be designed and assembled to ensure the 
required RF parameters. 
MSS 20208-398: 
Further design guideline references can be found in section "Design hints for ECUs 
with 100BASE-T1 / 1000BASE-T1 interface(s)". 
MSS 20208-399: 
The ECU connector implementation shall fulfill the connector requirements of OPEN 
Alliance, TC2: IEEE 100BASE-T1 Definitions for Communication Channel, Version 
1.0 [OPEN_CC]. 
MSS 20208-400: 
For specific configurations of the 100BASE-T1 channel local impedance deviations 
in the connector region can be tolerated if agreed with Vehicle Networking Group. 
 
8.1.5 
ESD Elements 
MSS 20208-402: 
A landing pad according to the transceivers application note for an optional ESD 
element shall be on the interface layout.  
MSS 20208-403: 
The ESD element can either be placed in the transceiver block or directly in front of 
the common mode choke – as shown in Figure F8.1.1.2a.  
MSS 20208-817: 
ESD elements placed at ESD_1 position shall fulfill the requirements according to 
[OPEN_ESD], Appendix B. 
 
8.2 
1000BASE-T1 ECU Interface 
MSS 20208-1021: The ECU shall be designed to fulfill the requirements given in [MBN 10567] 
according to the associated Component Requirement Specification. ECUs with 48V 
supply shall fulfill [VDA320]. 
MSS 20208-1022: Because of the coexistence of 12V as well as 48V supplied ECUs in the vehicle: 
ECUs which are supplied solely from the 48 V system shall not damage other ECUs 
of the Ethernet network in case of ground loss according to [VDA320]. 
8.2.1 
Physical Layer Interface 
MSS 20208-1024: In this section the requirements for the interfacing of the 1000BASE-T1 Transceiver 
in the ECU are specified.  
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 42 页
MSS 20208, V2.0, 2018-08, page 42 
Copyright Daimler AG 2018 
MSS 20208-1025: For all points that are not completely described in this document (e.g. supply 
voltages), the definitions of data sheets and application hints of the respective 
semiconductor manufacturers shall be fulfilled. 
MSS 20208-1026: All values listed in this document shall be valid within the specified temperature 
range (e.g. -40°C … +85°C / 105°C / 125°C) of the ECU (refer to the Component 
Specification of the affected ECU).  
8.2.1.1 
Evaluation/Testing Capabilities 
MSS 20208-1028: ECUs implementing a 1000BASE-T1 interface shall fulfill the requirements of [MBN 
10567] (resp. [VDA320] if applicable). 
MSS 20208-1029: ECUs implementing a 1000BASE-T1 interface shall fulfill the requirements of [MBN 
10284-1] and [MBN 10284-2]. 
MSS 20208-1030: To allow evaluation, 1000BASE-T1 Test Mode functionality with Test Modes 1 to 7 
(according to [IEEE802.3bp]) shall be implemented for each 1000BASE-T1 interface 
of the ECU.  
MSS 20208-1031: The ECU shall be able to deactivate data transmission (e.g. “TX off” or “Scrambler 
off”) of each 1000BASE-T1 interface to allow MDI return loss and MDI Mode 
conversion reflection test with proper termination inside the Transceiver (i.e. 100 Ω 
differential). 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 43 页
MSS 20208, V2.0, 2018-08, page 43 
Copyright Daimler AG 2018 
8.2.1.2 
1000BASE-T1 Interface Circuit 
MSS 20208-1033: A 1000BASE-T1 interface shall be implemented according to [IEEE802.3bp]. 
MSS 20208-1034: The interface consists of the Transceiver block (which includes the Transceiver, 
optional ESD protection devices and supply filtering all according to the datasheet 
and application notes of the Transceiver manufacturer), common mode choke, DC 
block capacitors, common mode termination network and the ECU connector. 
MSS 20208-1035: All components of the 1000BASE-T1 interface shall be implemented as required in 
tables T8.2.1.3a and T8.2.1.3b. 
MSS 20208-1036: For layout design information, the application notes of semiconductor manufacturers 
shall be considered. 
MSS 20208-1037: For short start up time it can be necessary to use hardware strap-in pins for phy 
configuration. 
MSS 20208-1038: A review of the interface layout by the semiconductor manufacturer shall be 
conducted. Review results shall be documented and reported to the Vehicle 
Networking Group. 
MSS 20208-1040: Further implementation recommendations for 1000BASE-T1 interfaces and ECUs 
are referenced in section "Design hints for ECUs with 100BASE-T1 / 1000BASE-T1 
interface(s)". 
MSS 20208-1041:  
G_ETH_P
G_ETH_N
PGND
R1
R2
C1
C2
C3
L1
VBat
GND
PGND
CMT
DC-Block
CMC
Transceiver Block
DGND
VDDX
CMC
Common Mode Choke
CMT
Common Mode Termination
BIN
Bus Interface Network
ESD
ESD suppression device
TC
1000BASE-T1 Transceiver (PHY or Switch)
Filter
Supply Filtering
OSC
Oscillator/Crystal
MAC
Media Access Control
GMII
Gigabit Media Independent Interface
(e.g. RGMII or SGMII)
VDDX
is representing all necessary Voltage supplies of 
the transceiver including their necessary filtering 
according to datasheet and application notes
Supply
ESD_2
Filter
Filter
µController
MAC
OSC
TC
GMII
R3
ESD_1
PGND
PGND
G_ETH_SHIELD
CST2 RST2
ESD Protection
PGND
CST1 RST1
BIN – Bus Interface Network
Shield 
termination
DGND
 
Figure F8.2.1.2a: Interface circuit for 1000BASE-T1 interface 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 44 页
MSS 20208, V2.0, 2018-08, page 44 
Copyright Daimler AG 2018 
8.2.1.3 
Common 1000BASE-T1 Transceiver Interface Component Data 
MSS 20208-1142:  
Ref 
Part 
Remarks 
 
Transceiver Block 
The Transceiver block (Transceiver including ESD and 
LPF according to application notes of the semiconductor 
manufacturer) has to fulfill the requirements of the OPEN 
Alliance specifications (refer to [IEEE802.3bp]). Validated 
and released recommended Transceivers are listed in 
section “1000BASE-T1 Transceivers”.2 
L1 
common mode choke 
Has to fulfill the requirements according to OPEN 
Alliance [OPEN_CMC_1G]. Validated and recommended 
common mode chokes are listed in section “Common 
Mode Choke for 1000BASE-T1 Interfaces”. 
C1, C2 
capacitor 100 nF 
tolerance <= 10% 
voltage range1 >= 50V 
 
R1, R2 
 
 
resistor 1 kOhm 
matching tolerance 
<= 1% (even after ESD-Test) 
absolute tolerance <= 10% 
power rating >= 0.4W 
ESD resistance 
refer to 3 
Termination circuitry for common mode RF currents on 
the data line. Therefore, resistors need sufficient power 
loss and capacitors need sufficient electrical strength. 
 
Remark: To fulfill the requirement e.g. a series circuitry of 
two 510 ohm resistors at each line can be used. 
C3 
capacitor 4.7 nF 
tolerance <= 10% 
voltage range1 >= 50V 
ESD resistance 
refer to 3 
To avoid DC current flow (e.g. due to GND-shift). 
R3 
resistor 100 kOhm 
tolerance <= 10% 
power rating >= 0.05W 
voltage range >= 50V 
ESD resistance 
refer to 3 
To avoid static charge of the cable harness. 
1 According to LV148 >=56V voltage range for 48V electrical systems, typically 100V min. electric strength 
recommended for ceramic caps. For 12V systems typ. 50V electrical strength is sufficient. 
  
2 It is required to follow the definitions of semiconductor manufacturer for external passive components and 
circuit, software configuration (script) and software flow definitions, which are defined in datasheet and 
application notes. The implementation of ESD protection elements is optional, but according layout is 
mandatory in any case. The implementation of Low Pass Filters for voltage supply is mandatory according to 
semiconductor manufacturer definition. 
 
3 BIN (R1, R2, R3, C3) components shall be chosen in order to fulfill the ESD requirements on MDI pins (BIN) 
according to table ******* 
 
 
 
Table T8.2.1.3a: Mandatory 1000BASE-T1 interface components and their 
requirements 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 45 页
MSS 20208, V2.0, 2018-08, page 45 
Copyright Daimler AG 2018 
MSS 20208-1143:  
Ref 
Part 
Remarks 
RST1 
resistor, not populated initially,  
100 kOhm 
tolerance <= 10% 
power rating >= 0.05W 
voltage range >= 100V 
 
ESD resistance 
refer to 3 
 
To avoid static charge of the cable harness. 
RST2 
resistor, not populated initially,  
0 Ohm 
tolerance <= 5 milliohm 
power rating >= 0.8W 
voltage range >= 100V 
 
ESD resistance 
refer to 3 
 
To short circuit shield on one side in case of EMC issues. 
CST1,CST2 
capacitor  
each 4,7µF 
tolerance <= 10% 
voltage range1 >= 50V 
ESD resistance 
refer to 3 
At least two landing pads shall be provided. 
To avoid DC current flow (e.g. due to GND-shift). 
 
 
 
ECU connector 
Has to fulfill the requirements according to OPEN 
Alliance [OPEN_CC_1G], refer also to section “ECU 
connectors for1000BASE-T1 interfaces (MDI Interface)”. 
 
1According to LV148 >=56V voltage range for 48V electrical systems, typically 100V min. electric strength 
recommended for ceramic caps. For 12V systems typ. 50V electrical strength is sufficient.  
 
 
3 Shield connection components (RST1, RST2, CST1, CST2) shall be chosen in order to fulfill the ESD 
requirements on housing/shield: 
   * 8kV contact discharge, acc. to [MBN10284] (ISO10605/330ohm/150pF) 
   * 15kV air discharge, acc. to [MBN10284] (ISO10605/330ohm/150pF) 
 
 
 
Table T8.2.1.3b: Mandatory 1000BASE-T1 interface components and their 
requirements 
8.2.2 
1000BASE-T1 Transceivers (standalone or integrated in switch) 
MSS 20208-1046: All Transceivers (PHYs or Switches) used in a 1000BASE-T1 interface shall comply 
to IEEE 1000BASE-T1 specification [IEEE802.3bp]. 
******* 
Transceiver requirements 
MSS 20208-1048: All Transceivers (PHYs or Switches) used in a 1000BASE-T1 interface shall fulfill the 
following requirements: 
MSS 20208-1049: -  
The PHY/switch shall support test modes acc. to 1000BASE-T1 specification  
 
[IEEE802.3bp]. 
MSS 20208-1050: -  
The PHY/switch shall support a mode which deactivates all signaling on the  
 
front-end, while the front-end termination is still active (e.g. called "TX_off" or  
 
"scrambler_off"). 
MSS 20208-1051: -  
The PHY/switch shall provide a link status information via a register, according  
 
to [IEEE802.3]. 
MSS 20208-1052: -  
The PHY/switch shall provide an information on CRC or symbol failures on  
 
physical layer level via a register. 
MSS 20208-1053: -  
The PHY/switch shall be able to detect a short between bus lines and provide  
 
this information via a register. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 46 页
MSS 20208, V2.0, 2018-08, page 46 
Copyright Daimler AG 2018 
MSS 20208-1054: -  
The PHY/switch shall be able to detect interruptions of bus lines and provide  
 
this information via a register. 
MSS 20208-1055: -  
The PHY/switch shall be able to detect wrong polarity and provide  
 
this information via a register. 
MSS 20208-1056: -  
The PHY/switch shall be able to correct the polarity by default 
MSS 20208-1057: -  
The PHY/switch shall provide a signal quality information based on the 
definitions in [OPEN_DIAG]. 
 
MSS 20208-1211: The following hardware configuration shall be configurable by hardware strap in pins: 
- Master / Slave configuration 
- Autonomous start of link training / host activated link training 
- Autonomous start of forwarding / host activated forwarding (applies only to 
switches) 
MSS 20208-1212: All Transceivers (PHYs or Switches) used in a 1000BASE-T1 interface shall fullfill 
the ESD-requirements listed in the following table *******. 
MSS 20208-1226: These values shall be guaranteed in the datasheet of the respective semiconductor 
manufacturer. 
MSS 20208-1213:  
Use-Case/Reference 
MDI  
(IC) 6 
Global Pins 
(ECU)  
[e.g. WAKE, 
VBAT] 
Local Supply 
Pins (IC) 
Local Control 
Pins /other 
local Pins 
(IC) 
Damage (unpowered) 
IEC61000-4-2 1,7 
+-6kV 
+-6kV 
- 
- 
HBM JESD22-A114 2 
+-4kV 
+-4kV 
+-2kV 
+-2kV 
CDM JESD22-C101 3 
+-500V 
+-500V 
+-500V 
+-500V 
SAEJ2962-2… 4 
- 
- 
- 
- 
Damage (powered) 
IEC61000-4-2 1,7 
+-6kV 
- 
- 
- 
SAEJ2962-2… 4 
- 
- 
- 
- 
SAEJ2962-2… 5 
- 
- 
- 
- 
No unwanted wake-up 
(powered) 8 
IEC61000-4-2 1,7 
+-3kV 
- 
- 
- 
No malfunction / no link loss 
(powered) 
IEC61000-4-2 1,7 
+-3kV 
- 
- 
- 
 
 
 
 
 
 
1 IEC61000-4-2 (330Ohm/150pF) - direct galvanic coupling 
2 Human Body Model JESD22-A114 / AEC-Q100-002 
3 Charge Device Model JESD22-C101 /AEC-Q100-011 
4 SAEJ2962-2…contact discharge 
5 SAEJ2962-2…air discharge 
6  MDI = Media Dependent Interface (Bus pins) : IEC61000-4-2 is tested with BIN (bus interface network) circuitry, all 
other tests directly at the pin. 
7 Tested at external Test house according to OPEN TC1 IEEE 100BASE-T1 EMC Test specification for transceivers version 
1.0 (FTZ Zwickau) 
The semiconductor is tested in combination with its external circuitry (e.g. bus interface network) 
The other semiconductor requirements have to be guaranteed by datasheet 
8 Only if wakeup function is available 
 
 
Table T*******: ESD Requirements 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 47 页
MSS 20208, V2.0, 2018-08, page 47 
Copyright Daimler AG 2018 
MSS 20208-1136: All Transceivers (PHYs or Switches) used in a 1000BASE-T1 interface shall be 
tested by an independent test house according to the following test specifications. 
MSS 20208-1060: -  
OPEN Alliance, TC12: 1000BASE-T1 PHY Control Test Suite 
 
[OPEN_PHYCTS_1G]. 
MSS 20208-1061: -  
OPEN Alliance, TC12: 1000BASE-T1 Physical Media Attachment Test Suite 
 
[OPEN_PMA_1G] 
MSS 20208-1062: -  
OPEN Alliance, TC12: 1000BASE-T1 Physical Coding Sublayer Test Suite 
 
[OPEN_PCS_1G] 
MSS 20208-1063: -  
OPEN Alliance, TC12: 1000BASE-T1 Interoperability Test Suite 
 
[OPEN_IOP_1G] 
MSS 20208-1064: -  
OPEN Alliance, TC12: EMC Test Specification for 1000BASE-T1 Transceivers 
 
[OPEN_EMC_1G]1 
 
MSS 20208-1065: 1The requirements of the EMC Test Specification (recommended values in the 
appendix C) are valid for a bus interface network according to figure F8.2.1.2a with 
the following changes: 
- As this specification is based on a fully shielded system, the limit for conducted 
emission (Appendix C.1 “Emission of RF disturbances”) between 76MHz and 
960MHz can be relaxed to 23dB (76MHz-960MHz) instead of 15dB/23dB. 
MSS 20208-1059: All tests shall be conducted in the identical configuration: 
Same BIN, same die revision, no change of undocumented registers (no firmware 
patches or other patchcode). 
Tested BIN shall be identical to the BIN defined in  Figure F8.2.1.2a. 
MSS 20208-1068: A Transceiver used in a 1000BASE-T1 interface shall fulfill the entire above listed 
test specifications and the test results shall be provided to the Vehicle Networking 
Group. 
8.2.2.2 
Additional recommended transceiver features 
MSS 20208-1138: This section is intended for future releases of this specification. 
8.2.2.3 
Recommended Single-PHY´s and switches for 1000BASE-T1 interfaces 
MSS 20208-1070: To be listed as recommended 1000BASE-T1 component (table T8.2.2.4a and table 
T8.2.2.4b) the semiconductor manufacturer has to provide the following 
documentation to Vehicle Networking Group: 
MSS 20208-1071: -  
complete test results of the OPEN Alliance Transceiver test specifications  
 
listed above (not in case of a PHY-less switch) 
MSS 20208-1072: -  
datasheet 
MSS 20208-1073: -  
additional information (referenced Application Notes, Software, etc.) 
MSS 20208-1074: -  
validation of switch requirements [OPEN_SWITCH] and 
 
[OPEN_SWITCH_TEST], see section 7.2 "Ethernet MAC Layer" 
MSS 20208-1075: -  
proof of successfully completed PPAP 
 
MSS 20208-1076: The Single PHY Transceivers and switches listed in Tables T8.2.2.4a and T8.2.2.4b 
are recommended for use in 1000BASE-T1 interfaces. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 48 页
MSS 20208, V2.0, 2018-08, page 48 
Copyright Daimler AG 2018 
MSS 20208-1077:  
Supplier 
Type 
Package 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Table T8.2.2.4a: Recommended 1000BASE-T1 Single-PHY Transceivers 
 
Please contact Vehicle Networking Group for update on this table. 
 
MSS 20208-1078:  
Supplier 
Type 
Package 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Table T8.2.2.4b: Recommended 1000BASE-T1 Switches 
 
Please contact Vehicle Networking Group for update on this table. 
 
8.2.3 
Common Mode Choke for 1000BASE-T1 interfaces 
MSS 20208-1080: All Common Mode Chokes used in an 1000BASE-T1 interface shall be tested by an 
independent test house according to the following test specifications: 
MSS 20208-1081: - 
OPEN Alliance, TC3: EMC Test Specification for 1000BASE-T1 Common  
 
Mode Chokes [OPEN_CMC_1G] 
MSS 20208-1082: The requirements of the EMC Test Specification (recommended values in the 
appendix C of [OPEN_CMC_1G]) are mandatory. 
8.2.3.1 
Recommended Common Mode Choke for 1000BASE-T1 interfaces 
MSS 20208-1227: To be listed as recommended 1000BASE-T1 CMC (table T8.1.3.1a) the CMC 
vendor has to provide the following documentation to Vehicle Networking Group: 
        -         complete test results of the OPEN Alliance CMC test specification 
[OPEN_CMC_1G] 
        -         datasheet 
        -         additional information (referenced Application Notes, etc.) 
        -         proof of successfully completed ppap 
MSS 20208-1084: The Common Mode Chokes listed in table T8.2.3.1a are recommended for use in 
1000BASE-T1 interfaces. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 49 页
MSS 20208, V2.0, 2018-08, page 49 
Copyright Daimler AG 2018 
MSS 20208-1085:  
Supplier 
Type 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Table T8.2.3.1a: Recommended 1000BASE-T1 Data line Common Mode Chokes 
 
Please contact Vehicle Networking Group for update on this table. 
 
8.2.4 
ECU connectors for 1000BASE-T1 interfaces (MDI interface) 
MSS 20208-1240: The 1000BASE-T1 channel shall be fully shielded system. Therefore cables and 
connectors shall be fully shielded. 
MSS 20208-1087: The complete channel as well as all individual components (cables and connectors 
as well as assembled cable harness) of a 1000BASE-T1 link shall fulfill the channel 
requirements according to according to the section of fully shielded cables of 
[OPEN_CC_1G]. 
MSS 20208-1088: Therefore all components of the link shall be designed and assembled to ensure the 
required RF parameters. 
MSS 20208-1089: Further design guideline references can be found in section "Design hints for ECUs 
with 100BASE-T1 / 1000BASE-T1 interface(s)". 
MSS 20208-1090: The ECU connector implementation shall fulfill the connector requirements of OPEN 
Alliance, TC9: 1000BASE-T1 Physical Layer EMC definitions for communication 
channel [OPEN_CC_1G]. 
8.2.5 
ESD Elements 
MSS 20208-1093: A landing pad according to the transceivers application note for an optional ESD 
element shall be on the interface layout.  
MSS 20208-1094: The ESD element can either be placed in the transceiver block or directly in front of 
the common mode choke – as shown in Figure F8.2.1.2a.  
MSS 20208-1249: ESD elements placed at ESD_1 position shall fulfill the requirements according to 
[OPEN_ESD_1G]. 
8.3 
100BASE-TX (Fast Ethernet) ECU Interface 
MSS 20208-507: 
The 100BASE-TX interface shall only be used for vehicle diagnostic purposes. 
8.3.1 
Physical Layer Interface 
MSS 20208-509: 
The 100BASE-TX physical layer interface shall be implemented according to 
[ISO_DoIP]. 
MSS 20208-510: 
The two pairs of the 100BASE-TX  link (TxD-pair and RxD-pair) shall have two 
different lay lengths to avoid crosstalk. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 50 页
MSS 20208, V2.0, 2018-08, page 50 
Copyright Daimler AG 2018 
MSS 20208-511: 
The overall 100BASE-TX link (from ECU to tester) shall fulfill the channel 
requirements of [IEEE802.3], clause 25.4.9 “Cable plant” and [ISO_DoIP] section 7.8 
"Cable definitions". 
8.3.2 
Requirements for 100BASE-TX Transceivers 
MSS 20208-513: 
AEC-Q100 qualified Transceivers (Single-PHYs or Switches) shall be used for 
100BASE-TX interfaces. 
MSS 20208-823: 
The transceivers used in the 100BASE-TX OBD interface shall least the fulfill the 
tests listed in [100BASE_TX_IOP]. 
8.3.3 
Requirements for Transformers for 100BASE-TX 
MSS 20208-515: 
AEC-Q200 qualified Transformers shall be used for 100BASE-TX interfaces. 
MSS 20208-516: 
Solutions with separate transformers and CMCs are also allowed. 
8.3.4 
Recommended ECU connectors for 100BASE-TX 
MSS 20208-518: 
The connector system shall have a maximum interspace of d =2,54mm. 
MSS 20208-519: 
Adjacent pins of the same length (same row) shall be used for each pair. 
MSS 20208-520: 
A suggested connector system is the Tyco / AMP - MICRO QUADLOCK SYSTEM 
(MQS) with an interspace of d =2,54mm.  
MSS 20208-521: 
For guidance refer to the appropriate recommendations of the "Design Hints for 
Ethernet Interfaces" [MSS 20208-DH]. 
8.3.5 
ESD Elements 
MSS 20208-523: 
No dedicated recommendations. 
8.3.6 
Activation Line Signaling for 100BASE-TX interfaces 
MSS 20208-525: 
The Activation Line shall be implemented according to [ISO_DoIP]. 
8.4 
Required ECU documentation for 100BASE-T1 / 1000BASE-T1 interfaces 
MSS 20208-1146: It is not intended to have further phy configuration parameters beyond NCD and 
MSS20208. 
However, in case PHY configuration parameters which are not available in the NCD 
and MSS20208 are present, they have to be agreed by the vehicle networking group 
at project start or if changes occur. 
MSS 20208-1147: All PHY configuration parameters shall be sent to the vehicle networking group for 
the first release or if changes occur. 
MSS 20208-1148: For review activities and documentation of the 100BASE-T1 / 1000BASE-T1 
interface of each ECU, the following information shall be provided to Vehicle 
Networking Group at the dates given below. 
8.4.1 
Documentation of 100BASE-T1 / 1000BASE-T1 interface (including interface to µCon-
troller) 
MSS 20208-1150: The following information/documents shall be provided for every sample release with 
changed hardware: 
MSS 20208-1151: -  
Schematic of 100BASE-T1 / 1000BASE-T1 interface 
MSS 20208-1152: -  
Layout of 100BASE-T1 interface (incl. MII-µC) / Layout of 1000BASE-T1 
 
interface (incl. RGMII/SGMII-µC) and layer stack-up of PCB 
MSS 20208-1153: -  
Component information of all 100BASE-T1 / 1000BASE-T1 interface 
 
components: 
MSS 20208-1154:  
* µController (vendor/manufacturer, type, revision) 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 51 页
MSS 20208, V2.0, 2018-08, page 51 
Copyright Daimler AG 2018 
MSS 20208-1155:  
* Transceiver (vendor/manufacturer, type, revision, software configuration) 
MSS 20208-1156:  
* Crystal (vendor/manufacturer, type, revision, tolerances) 
MSS 20208-1157:  
* Common Mode Choke (vendor/manufacturer, type, revision, tech. data) 
MSS 20208-1158:  
* Resistors (nominal values and tolerances, voltage and power ratings) 
MSS 20208-1159:  
* Capacitors (nominal values and tolerances as well as voltage ratings) 
MSS 20208-1160:  
* Inductors (nominal values/tolerances, voltage ratings, ESR, DC-resistance) 
MSS 20208-1161:  
* ESD component (vendor/manufacturer, type, revision) if used 
MSS 20208-1162:  
* MDI Connector (connector system with geometry and pin assignment) 
 
8.4.2 
Documentation of ECU supply interface 
MSS 20208-1164: The following information/documents shall be provided for every sample release with 
changed hardware: 
MSS 20208-1165: -  
Schematic of supply interface 
MSS 20208-1166: -  
Layout of supply interface 
MSS 20208-1167: -  
Component information of all supply interface components: 
MSS 20208-1168:  
* Voltage regulators2 
MSS 20208-1169:  
* Filter elements including e.g. inductors, ferrite beads, capacitors1 
MSS 20208-1170:  
* Common Mode Choke2 
MSS 20208-1171: Information to be included: 
1vendor/manufacturer, nominal value, tolerances, voltage and power ratings 
2vendor/manufacturer, type, revision and technical data 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 52 页
MSS 20208, V2.0, 2018-08, page 52 
Copyright Daimler AG 2018 
8.4.3 
Documentation of wake-up line interface 
MSS 20208-1173: The following information/documents shall be provided for every sample release with 
changed hardware: 
MSS 20208-1174: -  
Schematic of wake-up line interface 
MSS 20208-1175: -  
Component information of all wake-up line interface components: 
MSS 20208-1176:  
* Voltage regulators2 
MSS 20208-1177:  
* Resistors/inductors/capacitors/diodes/ferrite beads/IC’s1 
MSS 20208-1178: Information to be included: 
1vendor/manufacturer, nominal value, tolerances, voltage and power ratings 
2vendor/manufacturer, type, revision and technical data 
8.4.4 
Test reports for 100BASE-T1 / 1000BASE-T1 interface 
MSS 20208-1180: The following information/documents shall be provided for every sample release with 
new/changed hardware: 
MSS 20208-1181: -  
Test report according to Transmitter Test (Timing Jitter and Clock frequency) 
 
at MDI interface  
MSS 20208-1182: -  
Test report according to MDI return loss at MDI interface 
MSS 20208-1183: -  
Test report according to ModeConversion Reflection at MDI interface 
MSS 20208-1184: -  
Test report according to Common mode emission at MDI interface 
8.4.5 
Test reports for wake-up line 
MSS 20208-1186: The following information/documents shall be provided for every sample release with 
new/changed hardware. 
MSS 20208-1187: Wake-up Line test report (see section 10) with test results for: 
MSS 20208-1188: -  
Wake-up line driver test into worst-case load (100BASE-T1 / 1000BASE-T1 
 
worst case load test) 
MSS 20208-1189: -  
Wake-up line functional verification (100BASE-T1 / 1000BASE-T1 wake-up 
 
signal corner cases) 
8.5 
Wake-up Line Requirements for 100BASE-T1 / 1000BASE-T1 Links 
8.5.1 
Wake-up Line 
MSS 20208-955: 
Each ECU with 100BASE-T1 or 1000BASE-T1 interface and permanent power 
supply shall include a wake-up line interface. 
MSS 20208-956: 
For ECUs with 100BASE-T1 or 1000BASE-T1 interface and switched power supply 
the necessity to implement a wake-up line interface shall be agreed with the Vehicle 
Networking Group at project start. 
MSS 20208-957: 
Whether an ECU with wake-up line interface can actively wake up or not shall either 
be defined in the ECU specification or shall be agreed with vehicle networking group 
at project start. 
MSS 20208-958: 
The amount of wake-up line interfaces an ECU needs shall be defined in the ECU 
specification.  
MSS 20208-959: 
In case an ECU implements more than one wake-up line interface to handle 
separate wake-up line domains, every wake-up line interface shall be active wake-up 
capable. 
MSS 20208-960: 
ECUs with multiple wake-up lines shall implement each wake-up line interface 
independently. 
MSS 20208-961: 
The independent wake-up lines are used to realize separate failure domains on the 
wake-up network (e.g. for separating crash sensitive areas). 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 53 页
MSS 20208, V2.0, 2018-08, page 53 
Copyright Daimler AG 2018 
MSS 20208-963: 
Details have to be agreed with the component responsible persons within Mercedes-
Benz for the affected ECUs (e.g. EIS, HU, IC). 
MSS 20208-964: 
The 100BASE-T1 wake-up interface shall fulfill the requirements of [MBN 10567] 
(resp. [VDA320] if applicable). 
MSS 20208-965: 
The 100BASE-T1 wake-up interface shall fulfill the requirements of [MBN 10284-1] 
and [MBN 10284-2]. 
MSS 20208-966: 
For dedicated vehicle states cyclic wakeup pulses will occur. The handling of cyclic 
wakeup pulses in the ECU´s SW shall be considered accordingly. 
8.5.2 
Wake-up Line Interface 
MSS 20208-968: 
For understanding, simplified exemplary circuit diagrams of wake-up line interfaces 
(single/dual wake-up line) are shown in Figure F8.5.2a/F8.5.2b. 
MSS 20208-969: 
ECUs which need to be capable of also generating a wake-up pulse (active WU 
ECUs, refer to table T8.5.3a) shall consider the functionality represented by the line 
shaded part of the circuitry of Figure F8.5.2a. 
MSS 20208-970: 
A Switch-ECU which currently does not need to be active WU capable, shall 
implement landing pads for an appropriate circuitry anyway. 
MSS 20208-971: 
For non Switch-ECUs which currently do not need to be active WU capable, the 
landing pads for an appropriate circuitry shall be implemented anyway. Deviation 
from this requirement shall be agreed with the vehicle networking group. 
MSS 20208-972: 
It is strongly recommended to implement landing pads for the wake-up-pulse 
generation circuitry, even if the ECU is currently not planned as active WU capable 
ECU. 
MSS 20208-973: 
 
WL
Cin
RPD
µController
WL_WUP_detect
WL_WUP_generate
Supply_hold
VCC
GND
Vin
Vout
EN
LIM
RX
RX
H(X)
Filter
PGND
ESD-protection
UBat
 
 
Figure F8.5.2a: Exemplary interface circuit for 100BASE-T1 wake-up line 
interface (WL) 
 
 
MSS 20208-974: 
The filter element is used to avoid wake-up on very short pulses (avoid activation of 
the voltage regulator) and to protect the µController input pin. LIM is the current 
limitation circuitry which limits the driver current of the wake-up line. RX and the 
corresponding diodes are used to limit currents and to avoid reverse currents. Cin is 
the overall input capacitance of the wake-up line input pin (refer to table T8.5.3c) and 
RPD represents the overall input resistance of the wake-up line. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 54 页
MSS 20208, V2.0, 2018-08, page 54 
Copyright Daimler AG 2018 
MSS 20208-975: 
 
UBat
WL1
µController
WL_WUP_detect_1
WL_WUP_generate_1
Supply_hold
VCC
GND
UB
Vin
Vout
EN
LIM1
RX
RX
H(X)
Filter 1
PGND
ESD-Prot 1
ESD-Prot 2
WL2
H(X)
Filter 2
WL_WUP_detect_2
WL_WUP_generate_2
LIM2
RX
Cin
RPD
Cin
RPD
 
 
Figure F8.5.2b: Exemplary interface circuit for multiple wake-up line interfaces 
(WL) 
 
8.5.3 
Wake-up topology 
MSS 20208-977: 
Depending on the architecture of the networks there are 2 different types of ECUs: 
active and passive WU ECUs (refer to definition in table T8.5.3a). 
MSS 20208-978: 
 
ECU WU-Type 
Wake-up capability 
Active WU ECUs 
ECU’s capable of generating a wake-up pulse 
Passive WU ECUs 
ECUs able to wake-up upon a wake-up pulse, but not 
capable of generating a wake-up pulse 
 
 
 
Table T8.5.3a: Wake-up type definition 
 
MSS 20208-979: 
The signal source of a wake-up pulse shall be an ECU with an electronic switch to 
UB, with current limitation and a pull-down resistor RPD that corresponds to the input 
resistance of the control unit on the wake-up line. The signal is not synchronous with 
other signals. 
MSS 20208-980: 
The maximum number of ECUs in a 100BASE-T1 wake-up network is 26.  
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 55 页
MSS 20208, V2.0, 2018-08, page 55 
Copyright Daimler AG 2018 
MSS 20208-981: 
 
 
Wake-up line
RPD1
RLim
UB
RPD*
ECUs capable of generating 
a wake-up pulse 
(active wake-up  ECUs)
ECUs not capable of generating 
a wake-up pulse 
(passive wake-up  ECUs)
max. 26 ECUs
 
Figure F8.5.3a: Wake-up line topology constraints 
 
MSS 20208-982: 
The correct value of the pull-down resistor RPD for each ECU shall be chosen in 
regard to table T8.5.3a.  
MSS 20208-983: 
 
ECU WU-Type 
Pull-down Resistor 
Active WU ECUs 
RPD1 
Passive WU ECUs 
RPD* = RPD1 
 
 
 
Table T8.5.3a: Pull-down Resistor requirements 
 
MSS 20208-984: 
The wake-up line interface shall be able to withstand short circuit to battery voltage 
(including jump start and load dump test pulses) and short circuit to GND. 
MSS 20208-985: 
In case that the supply voltage of the control unit can be deactivated (e.g. for 
lowering the closed-circuit current), the deactivated ECU shall not influence the 
wake-up network. 
 
MSS 20208-986: 
This can be achieved, e.g. by using an isolating diode in the activation path of the 
wake-up line. 
MSS 20208-987: 
The wake-up line interface shall fulfill the requirements of the following Table. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 56 页
MSS 20208, V2.0, 2018-08, page 56 
Copyright Daimler AG 2018 
MSS 20208-988: 
 
 
Ref 
Min 
Typ 
Max 
Unit 
Remark 
Operating voltage 
UBat 
6,0 
 
16,0 
V 
A, B 
Wake-up line signal level 
UWL 
-3,0 
 
UB,max+3 
V 
B 
Transmitter “low” 
UTL 
 
 
0,4 
V 
 
Transmitter “high” 
UTh 
4,5 
14 
 
V 
 
Receiver “low” 
URL 
 
 
1,4 
V 
 
Receiver “high” 
URh 
3,5 
 
 
V 
 
Input capacitance 
Cin 
1,0 
 
2,7 
nF 
 
Pull-Down resistor #1 
RPD1 
9 
10 
11 
kOhm 
 
Wakeup Line leakage (driver not 
active) current 
IWL,leak 
 
 
100 
µA 
D 
Wakeup Line driver (active) current 
IWL,driver 
15 
 
1001 
mA 
C, E 
A 
Operating voltage is in accordance to [MBN 10567] requirements. ECUs with operating 
voltage 48V also shall supply the wake-up line driver according to the values in this 
table. If the operating voltage drops below the min-level, the ECU has to be neutral to 
the wake-up line, this means there should not be an unintended wake-up nor should a 
wake-up of other ECUs be shortcut.  
B 
valid at ECU Pin; eg „Jumpstart, Load Dump“ test pulses shall not lead to damage when 
applied to the wake-up line pin, refer to [MBN 10567] 
C 
Only for ECUs that can actively wake. 
D 
This requirement is valid for a powered ECU (active) which does not drive the wake-up 
line. The overall quiescent current requirements of the ECU have to be respected. 
E 
Driving capability starting at the minimal UBat  for which the ECU should still be able to 
wake others to reach the required voltage level Uhigh, at least while driving the activation 
line. To reduce power loss in the circuit assuming worst case conditions (maximum 
number of ECUs connected to the wake-up line, and high operating voltage 
(Jumpstart)), a driving capability of more than 25 mA is advised. 
1  
in case of a short circuit 
 
 
Table T8.5.3c: Wake-up line parameters 
 
8.5.4 
Timing of wake-up Line 
MSS 20208-990: 
Active wake-up ECUs shall regard the wake-up pulse restrictions according to Figure 
F8.5.4a and Table T8.5.4a for generation of wake-up pulses on the wake-up line. 
MSS 20208-991: 
A wake-up pulse shall be repeated immediately by the waking ECU in case the ECU 
identifies the generated wake-up pulse as corrupted or in case at least one of the 
following conditions is met while generating the pulse: 
- 
Operating voltage drops below UBat, Min (refer to table T8.5.3c) 
- 
Operating voltage exceeds UBat, Max (refer to table T8.5.3c) 
- 
Recognition of cold crank operation 
MSS 20208-992: 
The repetition of the wake-up pulse shall only be done after the disturbing condition 
has vanished if communication is still needed. 
MSS 20208-993: 
A single wake-up pulse shall not be repeated more than 3 times. 
MSS 20208-994: 
Remark: According to the repetition of corrupted pulses, a continuous high level of 
4s could occur on the wake-up line. In addition, cyclic repetitions of wake-up pulses 
can occur in specific vehicle states.  
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 57 页
MSS 20208, V2.0, 2018-08, page 57 
Copyright Daimler AG 2018 
MSS 20208-995: 
For dedicated vehicle states cyclic wake-up pulses will occur. The handling of cyclic 
wake-up pulses in the ECU´s SW shall be considered accordingly. 
MSS 20208-996: 
Remark: Cyclic pulses will be generated only by the ECU which has interfaces to 
both wake-up line clusters (which is typically the ECU EIS). The generation cycle 
time can be coded at this ECU to a value which is longer or equal to 1 second. 
MSS 20208-997: 
 
wake-up line pulse too long
(wake-up line duration failure)
wake-up line too low\
wake-up line too short
UWL,min= 4,5 V
UWake-upLine 
UWL[V]
t [ms]
UB,max= 16 V
UWL,typ= 14 V
UGND = 0 V
tWL Pulse, min
tWL Pulse, min = 500ms
tWL Pulse, max = 1000ms
tWL Pulse, max
wake-up line transmit timing
wake-up line low during 
ECU’s own WL pulse 
(e.g. short to GND failure)
UB,min= 6 V
UB,max,Jumpstart= 27 V
 
Figure F8.5.4a: Wake-up line timing on transmitter side. 
 
MSS 20208-998: 
The signal on the wake-up line shall fulfill the minimum and maximum time values 
according to Table T8.5.4a. 
MSS 20208-999: 
 
Parameter 
Ref 
Min 
[ms] 
Typ 
[ms] 
Max 
[ms] 
Remark 
Transmitter wake-
up pulse duration 
tWL_Pulse_Transmitter 
500 
500 
1000 
active waking ECU 
Receiver minimum 
valid wake-up 
pulse length 
tWL_Pulse_Receiver 
10-201 
 
 
wake up receiving ECU 
1 To avoid disturbing pulses to wake up ECUs, pulses shorter than 10ms shall be ignored (ECU 
internal power supply shall not be activated). Minimum valid wake-up pulse length depends on ECU 
state when triggering wake-up line. 
 
 
Table T8.5.4a: Wake-up line timing parameters 
 
MSS 20208-1000: Every connected wake-up line shall be monitored in an appropriate way to be able to 
detect a valid wake-up pulse according to tables T8.5.4b/T8.5.4c. 
MSS 20208-1001: For ECUs which are already active and are monitoring the wake-up line at the time 
the wake-up line is triggered, wake-up pulse validation shall meet the conditions 
according to table T8.5.4b. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 58 页
MSS 20208, V2.0, 2018-08, page 58 
Copyright Daimler AG 2018 
MSS 20208-1002:  
Pulse detection time 
Validation result 
tWL_Pulse_detection < 10ms 
Invalid wake-up pulse 
10ms <= tWL_Pulse_detection < 20ms 
Wake-up pulse can be treated as invalid or valid wake-up 
pulse 
tWL_Pulse_detection >= 20ms 
Valid wake-up pulse 
 
 
Table T8.5.4b: Wake-up pulse validation for active ECUs 
 
MSS 20208-1003: ECUs that are already activated when they receive a wake-up signal shall be able to 
forward it to higher layers (e.g. as an interrupt). 
MSS 20208-1004: ECUs which are activated from unpowered state because of a wake-up pulse, can 
use either the wake-up validation conditions according to table T8.5.4b or table 
T8.5.4c.  
 
MSS 20208-1005:  
Pulse detection time 
Validation result 
tWL_Pulse_detection < 10ms 
Invalid wake-up pulse 
10ms <= tWL_Pulse_detection < tWL_Pulse_Mon_Start
1,2 
Wake-up pulse can be treated as invalid or valid 
tWL_Pulse_detection >= tWL_Pulse_Mon_Start
2 
Valid wake-up pulse 
1 tWL_Pulse_Mon_Start represents the time from triggering the wake-up line to the time when wake-up line 
monitoring and evaluation is available at the receiving ECU 
2 limitation of tWL_Pulse_Mon_Start due to timing requirements according to tables T*******a/T*******a 
shall be considered; timing shall be as fast as possible 
 
 
Table T8.5.4c: Wake-up pulse validation for ECUs woken from Powerdown 
 
MSS 20208-1006: To simplify the wake-up pulse validation for the startup of an unpowered ECU there 
is no need of a length validation above 10ms due to the fact that the startup of the 
software stack typically lasts more than 20 ms. 
The wake-up reason can be evaluated as long as the WL is active (i.e. typ. 500ms). 
MSS 20208-1007: In general, the overall timing requirements for the activation of ECU/Ethernet 
communication stipulated in figures F*******a/F*******a and in tables 
T*******a/T*******a shall be met. 
MSS 20208-1008: ECUs implementing multiple wake-up lines shall forward an incoming valid wake-up 
pulse on all other wake-up lines within 10ms (time from when a pulse is validated 
until other wake-up line is generating a pulse).  
MSS 20208-1009: Wake-up pulses that last longer than 1000ms shall not affect the wake-up 
procedure. 
MSS 20208-1010: Detected wake-up pulses shall not be retriggered on the wake-up line carrying the 
initiating wake-up pulse. 
MSS 20208-1011: In case a valid wake-up pulse was detected, the ECU shall be activated (if not 
already active) and the actions stipulated in tables T8.5.4d (single port ECU) and 
T8.5.4e (switch ECU) for the 100BASE-T1 / 1000BASE-T1 Ethernet ports shall be 
executed. 
MSS 20208-1012: Invalid wake-up pulses shall not lead to an activation (if not already active) of the 
ECU or Ethernet communication or influence the ECU function in any other way than 
for validation of the wake-up pulse needed. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 59 页
MSS 20208, V2.0, 2018-08, page 59 
Copyright Daimler AG 2018 
MSS 20208-1013:  
State of single port ECU (no 
switch with external connections) 
Activities after detection of valid wake-up pulse 
ECU in Power Down State 
• 
ECU is activated and Ethernet communication is set 
to normal operation mode for the 100BASE-T1 / 
1000BASE-T1 port 
• 
ECUs with more than one wake-up line: generation 
of wake-up pulse on the other wake-up lines1 
ECU active and 100BASE-T1 / 
1000BASE-T1 Ethernet 
communication deactivated 
• 
Ethernet communication is set to normal operation 
mode for the 100BASE-T1 / 1000BASE-T1 port 
• 
ECUs with more than one wake-up line: generation 
of wake-up pulse on the other wake-up lines1 
ECU active and 100BASE-T1 / 
1000BASE-T1 Ethernet interface in 
test mode 
• 
Ethernet communication is set to normal operation 
mode for the 100BASE-T1 / 1000BASE-T1 port 
• 
ECUs with more than one wake-up line: generation 
of wake-up pulse on the other wake-up lines1 
ECU active and 100BASE-T1 / 
1000BASE-T1 Ethernet 
communication in normal operation 
mode 
• 
No action on the 100BASE-T1 / 1000BASE-T1 
Ethernet interface 
• 
ECUs with more than one wake-up line: generation 
of wake-up pulse on the other wake-up lines1 
1triggering of the wake-up line carrying the initiating wake-up pulse shall be omitted 
 
 
Table T8.5.4d: Activities after detection of valid wake-up pulse for single port 
ECU 
 
MSS 20208-1014: Some ports on a switch may be not connected, if a switch has more ports than 
needed by the current topology. Such ports shall never be switched on.  
MSS 20208-1015:  
State of switch ECU 
Activities after detection of valid wake-up pulse 
ECU in Power Down State 
• 
ECU is activated and ethernet communication is set to 
normal operation mode for all needed ports1 
• 
ECUs with more than one wake-up line: generation of 
wake-up pulse on the other wake-up lines2 
ECU active and ethernet 
communication deactivated for at 
least one needed port1 
• 
Ethernet communication is set to normal operation 
mode for the deactivated but needed ports1  
• 
ECUs with more than one wake-up line: generation of 
wake-up pulse on the other wake-up lines2 
ECU active and ethernet interface 
in test mode for at least one 
needed port1 
• 
Ethernet communication is set to normal operation 
mode for the needed ports that are in test mode1  
• 
ECUs with more than one wake-up line: generation of 
wake-up pulse on the other wake-up lines2 
ECU active and ethernet 
communication in normal operation 
mode for all needed ports1 
• 
No action on ethernet interfaces 
• 
ECUs with more than one wake-up line: generation of 
wake-up pulse on the other wake-up lines2 
1“needed ports” are 100BASE-T1 / 1000BASE-T1 ports that are connected according to current 
topology 
2 triggering of the wake-up line carrying the initiating wake-up pulse shall be omitted 
 
 
Table T8.5.4e: Activities after detection of valid wake-up pulse for switch ECU 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 60 页
MSS 20208, V2.0, 2018-08, page 60 
Copyright Daimler AG 2018 
8.6 
Cable harness for 100BASE-T1 Links 
MSS 20208-529: 
All 100BASE-T1 links within a cable harness assembly implementation shall fulfill the 
channel requirements of OPEN Alliance, TC2: IEEE 100BASE-T1 Definitions for 
Communication Channel, Version 1.0 [OPEN_CC]. 
MSS 20208-530: 
Therefore all components of the link shall be designed and assembled to ensure the 
required RF parameters. 
MSS 20208-531: 
For specific configurations of the 100BASE-T1 channel local impedance deviations 
in the connector region can be tolerated if agreed with Vehicle Networking Group. 
 
8.6.1 
General requirements on cables 
MSS 20208-533: 
In general 100BASE-T1 links are implemented with unshielded twisted pair cables. 
MSS 20208-534: 
Each unshielded 100BASE-T1 link shall be implemented with jacketed/sheathed 
twisted pair cables. 
MSS 20208-535: 
Strongly temperature dependent insulation materials (e.g. PVC,..) are not allowed for 
jacketed/sheathed cables of unshielded 100BASE-T1 links. 
MSS 20208-536: 
Cables used for 100BASE-T1 links shall fulfill the requirements of OPEN Alliance, 
TC2: IEEE 100BASE-T1 Definitions for Communication Channel, Version 1.0 
[OPEN_CC]. 
MSS 20208-537: 
Each 100BASE-T1 link shall be realized with qualified cables according to [MBN 
LV213-2]. 
MSS 20208-538: 
Each 100BASE-T1 link shall have a maximum length of 10m. 
MSS 20208-539: 
For 100BASE-T1 links with a length between 10m and 15m dedicated cables with 
reduced insertion loss have to be used. The Vehicle Networking Group shall be 
contacted in case more than 10m are necessary. 
MSS 20208-540: 
Furthermore, shielded cables might be necessary for EMC sensitive applications, 
e.g. 100BASE-T1 transmission lines close to vehicle antennas or in very noisy 
environments. 
MSS 20208-541: 
Shielded cables without galvanic shield connection (floating shield) shall not be 
used. 
 
MSS 20208-811: 
The unshielded, jacketed cables listed in table T8.6.1a fulfill the requirements of 
[OPEN_CC] and [MBN LV213-2]. 
MSS 20208-815: 
The shielded cables listed in table T8.6.1b fulfill the requirements of [OPEN_CC] and 
[MBN LV213-2]. 
MSS 20208-814: 
If shielded cables are used, both ECUs of the shielded links shall be equipped with 
connectors providing the connection from cable shield to ECU ground. 
MSS 20208-812: 
 
 
Daimler CAD-no. 
naming 
H35/20 
FLCuSn03 9Y-9Y 2x0,13 QMM-
A_SL23_T105 
H35/21 
FLCuSn03 9Y-31Y 2x0,13 QMM-
A_SL23_T125 
 
 
 
Table T8.6.1a: Unshielded cables for 100BASE-T1 wiring 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 61 页
MSS 20208, V2.0, 2018-08, page 61 
Copyright Daimler AG 2018 
MSS 20208-813: 
 
 
Daimler CAD-no. 
naming 
H35/23 
FLCuSn03 9Y-9YBCY 2x0,13 QMM-
A_SL30_T105 
 
 
 
 
 
Table T8.6.1b: Shielded cables for 100BASE-T1 wiring 
 
8.6.2 
Exemplary implementation hints on connectors 
MSS 20208-825: 
Connectors used for 100BASE-T1 links shall fulfill the requirements of OPEN 
Alliance, TC2: IEEE 100BASE-T1 Definitions for Communication Channel, Version 
1.0 [OPEN_CC]. 
MSS 20208-865: 
The following pin numbering on connector and color scheme shall be used: 
MSS 20208-1220:  
Pin number 
Description 
Wire Color 
Remark 
1 
ETH_P_ECU 
White 
 
2 
ETH_N_ECU 
Green 
 
 
 
 
 
 
 
Table T8.6.2a: Pin numbering and color scheme 
MSS 20208-827: 
The connectors listed in table T8.6.2b fulfill the requirements of [OPEN_CC]. 
MSS 20208-826: 
 
 
Daimler CAD-no. 
naming 
A009 000 18 99 
AV Ethernet-Modul (Buchse) 
A009 000 23 99 
AV Ethernet-Modul (Stift) 
A009 000 19 99 
AV 1 Port 
A009 000 49 99 
AV 2 Port 
A009 000 20 99 
AV 5 Port 
A009 000 21 99 
AV 6 Port 
A009 000 22 99 
AV 1 Port Wasserdicht 
 
 
 
Table T8.6.2b: Connectors for 100BASE-T1 wiring 
 
MSS 20208-543: 
The connector systems shown in table T8.6.2b have been investigated and it has 
been shown that these connector systems can achieve the required RF-parameters 
if appropriate manufacturing measures are taken into account. 
MSS 20208-544: 
The applicability of these connector systems for a specific Ethernet connection 
depends on the constraints according to: 
MSS 20208-545: 
-  
Channel configuration 
MSS 20208-546: 
-  
ECU connector/pinning/package requirements 
MSS 20208-547: 
-  
Requirements according to location of ECU/link in the vehicle 
MSS 20208-548: 
The selection of connector systems carrying Ethernet signals has to be agreed with 
Mercedes-Benz Connection Systems Department. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 62 页
MSS 20208, V2.0, 2018-08, page 62 
Copyright Daimler AG 2018 
8.7 
Cable harness for 1000BASE-T1 Links 
MSS 20208-1192: All 1000BASE-T1 links within a cable harness assembly implementation shall fulfill 
the channel requirements of OPEN Alliance, TC9, refer to [OPEN_CC_1G]. 
MSS 20208-1193: Therefore all components of the link shall be designed and assembled to ensure the 
required RF parameters. 
MSS 20208-1194: For specific configurations of the 1000BASE-T1 channel local impedance deviations 
in the connector region can be tolerated if agreed with Vehicle Networking Group. 
 
8.7.1 
General requirements on cables 
MSS 20208-1196: In general 1000BASE-T1 links are implemented with shielded twisted pair cables. 
MSS 20208-1197: Strongly temperature dependent insulation materials (e.g. PVC,..) are not allowed for 
1000BASE-T1 links. 
MSS 20208-1198: Cables used for 1000BASE-T1 links shall fulfill the requirements of OPEN Alliance, 
TC9: Channel and Components Requirements for 1000BASE-T1 Automotive 
Ethernet [OPEN_CC_1G] and MBN LV213 [MBN LV213-2]. 
MSS 20208-1248: The limits from [LV213-2] (Beiblatt 1000BASE-T1 HMTD/STP) are valid (e.g. cable 
type H35/29 can be used). 
MSS 20208-1199: Each 1000BASE-T1 link shall have a maximum length of 10m. 
MSS 20208-1200: The Vehicle Networking Group shall be contacted in case more than 10m are 
necessary. 
MSS 20208-1201: Shielded cables with floating shield shall not be used. 
 
MSS 20208-1242: The shielded cables listed in table T8.7.1a fulfill the requirements of  [LV213-2] 
(Beiblatt 1000BASE-T1 HMTD/STP). 
MSS 20208-1243:  
 
Daimler CAD-no. 
naming 
H35/29 
FL09YBCY 2x0,14sn-A /T105 
 
 
 
 
 
Table T8.7.1a: Shielded cables for 1000BASE-T1 wiring 
 
8.7.2 
Exemplary implementation hints on connectors 
MSS 20208-1216: The following pin numbering on connector and color scheme shall be used: 
MSS 20208-1218:  
Pin number 
Description 
Wire Color 
Remark 
1 
G_ETH_P_ECU 
White 
 
2 
G_ETH_N_ECU 
Green 
 
 
G_ETH_SHIELD 
Black 
 
 
 
Table T8.7.2a: Pin numbering and color scheme 
MSS 20208-1203: The connector systems shown in table T8.7.2b have been investigated and it has 
been shown that these connector systems can achieve the required RF-parameters 
if appropriate manufacturing measures are taken into account. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 63 页
MSS 20208, V2.0, 2018-08, page 63 
Copyright Daimler AG 2018 
MSS 20208-1204: The applicability of these connector systems for a specific Ethernet connection 
depends on the constraints according to: 
MSS 20208-1205: -  
Channel configuration 
MSS 20208-1206: -  
ECU connector/pinning/package requirements 
MSS 20208-1207: -  
Requirements according to location of ECU/link in the vehicle 
MSS 20208-1208: The selection of connector systems carrying Ethernet signals has to be agreed with 
Mercedes-Benz Connection Systems Department. 
MSS 20208-1209:  
 
 
 
 
 
 
 
 
 
 
 
 
Daimler CAD-no. 
naming 
A007 002 67 99 
AV-Kammer HMTD Buchse-Kontakt 
A007 002 68 99 
AV-Kammer HMTD Stift-Kontakt 
A007 002 69 99 
AV HMTD 1 Port 
A007 002 70 99 
AV  HMTD 2 Port 
A007 002 71 99 
AV  HMTD 4 Port 
A007 002 72 99 
AV  HMTD 1 Port Wasserdicht 
 
 
 
 
 
Table T8.7.2b: Connector system capable of fulfilling RF requirements of 
1000BASE-T1 links 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 64 页
MSS 20208, V2.0, 2018-08, page 64 
Copyright Daimler AG 2018 
9 
Ethernet ECU Diagnostics 
9.1 
Diagnostics Services 
MSS 20208-583: 
ECUs with at least one 100BASE-T1 / 1000BASE-T1 interface shall implement 
Ethernet diagnostics capabilities using the underlying mechanisms described in the 
following sections. 
MSS 20208-584: 
Additional requirements according to [DIAG_ISO] and [CANDELA_TEMPLATE] 
have to be considered for the diagnosis capabilities defined in this section.  
9.1.1 
Link Information 
9.1.1.1 
Link Quality 
MSS 20208-587: 
Ethernet Link Quality shall be accessible via ECU diagnostics, refer to 
[CANDELA_TEMPLATE], DID 0x0160. 
The DID reports the signal quality (often called SNR or SQI) of the Ethernet Links 
calculated and stored by the ECU. It reports the current link quality as well as the 
lowest link quality per port since last clear.  
MSS 20208-589: 
The worst case link quality information consists of a value for the lowest link quality 
since last clear. The value shall be stored nonvolatile and shall be clearable by a 
diagnostic service. 
MSS 20208-822: 
For PHYs/Switches compliant with the OPEN Alliance, TC1: Advanced diagnostic 
features for 100BASE-T1 automotive Ethernet PHYs [OPEN_DIAG] the defined 
signal quality index information shall be used.  
MSS 20208-595: 
For transceivers that provide the link quality value in different resolution a linear up-
/downscaling according to the scaling defined in [OPEN_DIAG] shall be used. 
MSS 20208-590: 
The worst case link quality information shall be read out from the register for 
[OPEN_DIAG] compliant PHYs/Switches or shall be updated for non-compliant 
PHYs/Switches with a cycle time of at least 1 sec while the link is active.  
MSS 20208-591: 
The actual link quality information shall be provided immediately if requested. 
MSS 20208-592: 
In case the link is inactive, the worst case link quality information shall not be 
updated. 
MSS 20208-593: 
The link quality information shall be updated for every active link with a cycle time of 
at least 1 sec or on demand of the diagnostic service in case the link is active. 
MSS 20208-594: 
The output value of the diagnostic service shall indicate the link quality and the worst 
case link quality by a relative integer value with 8 different quantities with range 0...7 
(0=lowest quality, 7=highest quality) for every port implemented in the ECU.  
In case no value can be evaluated for the actual or worst case signal quality a 
special value SNA=$F shall be returned indicating that the value is not available. 
MSS 20208-596: 
The actual and worst case Link Quality Information shall be clearable by diagnostics 
(Clear Ethernet Link Quality), refer to [CANDELA_TEMPLATE], DID 0x0250. 
9.1.1.2 
Link Status 
MSS 20208-598: 
The Ethernet Link Status information shall be accessible via ECU diagnostics, refer 
to [CANDELA_TEMPLATE], DID 0x0165. 
The DID shall report whether the Link of a Port is established / not established. 
MSS 20208-599: 
This information shall be gathered from the transceiver’s or switch’s link status 
register. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 65 页
MSS 20208, V2.0, 2018-08, page 65 
Copyright Daimler AG 2018 
9.1.1.3 
Link Training 
MSS 20208-601: 
The Ethernet Link Training Duration time information shall be accessible via ECU 
diagnostics, refer to [CANDELA_TEMPLATE], DID 0x0174. 
MSS 20208-602: 
The ECU shall calculate the Link Training Duration time information as the difference 
between the time when the link is signaled active to the application (application gets 
an indication from the ComM with communication mode “FULL_COM”) and a 
reference time. The reference time shall be determined as follows: 
-  
in case the application is active and the command to start link training is given 
by the application (FULL_COM Request from the application) then the reference 
time represents the time the command is given. 
-  
in case the application is in starting process while link training starts 
(autonomous start of PHY) then the reference time represents the time the wake-up 
was recognized. 
MSS 20208-872: 
Remark: The measurement method leads to a known inaccuracy which still is 
accepted as the inaccuracy is lower than relevant failure time (for DTC). 
 
******* 
Cable Diagnostics 
MSS 20208-854: 
The Ethernet cable diagnostics information shall be accessible via ECU diagnostics, 
refer to [CANDELA_TEMPLATE], DID 0x0259. 
MSS 20208-855: 
The following cable diagnostics information is provided: 
 
MSS 20208-1221:  
Diagnostic Information 
Description 
OK 
Link is OK and correctly terminated (Link Partner is sending SEND_Z) 
OPEN 
OPEN of one bus wire or of both bus wires 
SHORT 
SHORT of both bus wires to each other or to supply line 
(GND/battery) 
 
 
Table T*******: Connector system capable of fulfilling RF requirements of 
1000BASE-T1 links 
 
MSS 20208-871: 
Remark: See AUTOSAR 
EthTrcv_RunCableDiagnostic/EthSwt_RunPortCableDiagnostic and 
EthTrcv_GetCableDiagnosticsResult/EthSwt_GetPortCableDiagnosticsResult (refer 
to [SWS_EthTrcv_Drv] and [SWS_EthSwt_Drv]) 
 
 
9.1.2 
Test Modes 
******* 
Port Test Modes 
MSS 20208-605: 
The Ethernet Test Modes for 100BASE-T1 / 1000BASE-T1 devices shall be 
accessible via ECU diagnostics, refer to [CANDELA_TEMPLATE], DID 0x0251 for 
100BASE-T1 and DID 0x0252 for 1000BASE-T1 interfaces. 
MSS 20208-606: 
To allow evaluation, it shall be possible to activate/deactivate 100BASE-T1 Test 
Mode functionality with Test Modes 1 to 5 (refer to [IEEE802.3bw]). 
MSS 20208-1237: To allow evaluation, it shall be possible to activate/deactivate 1000BASE-T1 Test 
Mode functionality with Test Modes 1 to 7 (refer to [IEEE802.3bp]). 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 66 页
MSS 20208, V2.0, 2018-08, page 66 
Copyright Daimler AG 2018 
9.1.2.2 
Port Control 
MSS 20208-610: 
Ethernet transmit and scrambler functionality shall be accessible via ECU 
diagnostics, refer to [CANDELA_TEMPLATE], DID 0x0254. 
MSS 20208-611: 
To allow evaluation, it shall be possible to activate/deactivate the TX path and 
scrambler by the DID (refer to [IEEE802.3bw]). 
MSS 20208-800: 
Remark: Alternative solutions to force the phy to go to tx_mode=SEND_Z (refer to 
[IEEE802.3bw]) are allowed as well. 
MSS 20208-612: 
The activation/deactivation shall be done via the appropriate transceiver register. 
MSS 20208-613: 
Ethernet Port Activation/Deactivation shall be possible via ECU diagnostics, refer to 
[CANDELA_TEMPLATE], DID 0x0257. 
MSS 20208-614: 
The activation/deactivation shall be done by the DID separately for every port via the 
appropriate transceiver’s or switch’s control register. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 67 页
MSS 20208, V2.0, 2018-08, page 67 
Copyright Daimler AG 2018 
9.1.3 
Wake-up Line Information 
9.1.3.1 
Wake-up Line Status 
MSS 20208-617: 
The Ethernet Wake-up Line Status shall be accessible via ECU diagnostics, refer to 
[CANDELA_TEMPLATE], DID 0x0167. 
MSS 20208-618: 
The DID shall report the current status of the Wake-up Lines of the ECU. If no 
second Wake-up Line is connected, the ECU shall report 0 for the second Wake-up 
line. 
9.1.3.2 
Wake-up Line Activation 
MSS 20208-620: 
Ethernet Wake-up Line Activation shall be possible via ECU diagnostics, refer to 
[CANDELA_TEMPLATE], DID 0x0168. 
MSS 20208-621: 
The DID is used to activate / deactivate all Ethernet Wake-up Lines connected to the 
ECU. 
9.1.3.3 
Wake-up Line Pulse Counter 
MSS 20208-623: 
The amount of wake-up events since last clear shall be accessible by ECU 
diagnostics, refer to [CANDELA_TEMPLATE], DID 0x0169. 
MSS 20208-624: 
The DID shall report the number of valid wake-up pulses and the number of wake-up 
pulse length failures (wake-up pulse longer than maximum allowed transmitter pulse 
length) since last clear. In case the counter reaches the maximum count value, 
counting shall be stopped and the maximum value shall be held. 
MSS 20208-625: 
Clearing of the Ethernet wake-up line pulse counter (DID 0x169) shall be possible 
via ECU diagnostics, refer to [CANDELA_TEMPLATE], DID 0x0253. 
9.1.4 
Communication Statistics 
9.1.4.1 
Link Statistics 
MSS 20208-628: 
Ethernet link statistics information shall be accessible via ECU diagnostics, refer to 
[CANDELA_TEMPLATE], DID 0x0164. 
MSS 20208-629: 
The following link statistics values according to Management Information Base for 
Network Management shall be accessible for each active link by the DID.  For 
switches the MAC-Layer link is also included in the active links. 
MSS 20208-630: 
-  
data bytes received 
MSS 20208-631: 
-  
number of received frames 
MSS 20208-632: 
-  
data bytes transmitted 
MSS 20208-633: 
-  
RX data bytes dropped 
MSS 20208-634: 
-  
TX data bytes dropped 
MSS 20208-635: 
-  
number of dropped frames after reception 
MSS 20208-636: 
-  
number of sent frames 
MSS 20208-637: 
-  
number of unsuccessful sent frames 
MSS 20208-638: 
-  
number of CRC errors on reception 
MSS 20208-639: 
-  
RX Error Packets 
MSS 20208-640: 
-  
number of unexpected link losses 
MSS 20208-641: 
-  
CRC or Symbol Failure on physical layer level 
MSS 20208-642: 
RX Error Packets indicates the sum of all faulty packets (e.g. alignment errors, CRC 
errors, FCS errors, fragments, oversized packets, jabbers). 
MSS 20208-643: 
For single port PHYs the above statistics information typically is derived from the 
MAC of the µC, while switches provide this information internally.  
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 68 页
MSS 20208, V2.0, 2018-08, page 68 
Copyright Daimler AG 2018 
9.1.4.2 
MIB Counters 
MSS 20208-645: 
Ethernet MIB Counters shall be accessible via ECU diagnostics, refer to 
[CANDELA_TEMPLATE], DID 0x0163. 
MSS 20208-646: 
The DID shall report link statistics of the Management Information Base. Receive 
and Transmit Counters shall be reported according to table T9.1.4.2a/T9.1.4.2b. 
MSS 20208-647: 
 
Size (bit) 
Receive Counter per Port 
RFC1 
1643 
RFC2 
1493 
RFC3 
1213 
32 Dropped Pakets 
X 
X 
X 
32 Unicast Frames 
  
  
X 
32 Alignment Errors 
X 
  
  
32 FCS Errors 
X 
  
  
1 refer to [RFC1643] 
2 refer to [RFC1493] 
3 refer to [RFC1213] 
 
 
Table T9.1.4.2a: MIB Receive Counter 
 
MSS 20208-648: 
 
Size (bit) 
Transmit Counter per Port 
RFC1 
1643 
RFC2 
1493 
RFC3 
1213 
32 Dropped Pakets 
X 
  
X 
64 Bytes of data 
  
  
X 
32 Broadcast Pakets 
  
  
X 
32 Multicast Pakets 
  
  
X 
32 Unicast Pakets 
  
  
X 
32 Single Collision 
X 
  
  
32 Multiple Collision 
X 
  
  
32 Deferred Transmit 
X 
  
  
32 Late Collision 
X 
  
  
32 Excessive Collision 
X 
  
  
1 refer to [RFC1643] 
2 refer to [RFC1493] 
3 refer to [RFC1213] 
 
 
Table T9.1.4.2b: MIB Transmit Counter 
 
******* 
Switch Counters 
MSS 20208-650: 
Ethernet Switch Counters shall be accessible via ECU diagnostics, refer to 
[CANDELA_TEMPLATE], DID 0x0161. 
MSS 20208-651: 
The DID shall report the data of the AUTOSAR EthSwt_GetRxStats and 
EthSwt_GetTxStats function (refer to [SWS_EthSwt_Drv]) containing information on 
data traffic since the last power-cycle as well as faults/events during data 
communication.  
******* 
Drop Counters 
MSS 20208-653: 
Ethernet Drop Counters shall be accessible via ECU diagnostics, refer to 
[CANDELA_TEMPLATE], DID 0x0162. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 69 页
MSS 20208, V2.0, 2018-08, page 69 
Copyright Daimler AG 2018 
MSS 20208-654: 
The DID shall report the data of the AUTOSAR EthSwt_GetTxErrorCounterValues 
function (refer to [SWS_EthSwt_Drv]) containing information on data traffic since the 
last power-cycle as well as faults/events during data communication. 
9.1.4.5 
Port Status 
MSS 20208-849: 
The Ethernet Port Status shall be accessible via ECU diagnostics, refer to 
[CANDELA_TEMPLATE], DID 0x0166. 
 
MSS 20208-661: 
The DID shall provide information on the port settings and queue status. Reported is 
information on Master/Slave configuration and maximum fill level of the queues of 
each egress port since clearing the counter. Please refer to AUTOSAR 
EthSwt_MaxBufferlevel. 
9.1.4.6 
Switch/Port Measurement 
MSS 20208-852: 
This service starts / stops the measurement for the max. fill level of the queues, refer 
to [CANDELA_TEMPLATE], DID 0x0258. On start all counters are reset to "0". 
 
9.1.5 
Configuration 
9.1.5.1 
Hardware Configuration 
MSS 20208-847: 
This section is obsolete because the information is documented in EPDM. 
 
9.1.5.2 
Port Configuration Information 
MSS 20208-850: 
This section is obsolete because one part of it moved to section 9.1.4.5 Port status 
and other part was deleted. 
******* 
Switch Configuration 
MSS 20208-666: 
The Ethernet Switch Configuration shall be accessible via ECU diagnostics, refer to 
[CANDELA_TEMPLATE], DID 0x0173. 
MSS 20208-667: 
The DID shall report the actual configuration information that represents the switch 
configuration (configuration data that was used to configure the switch at startup). 
9.1.6 
Identification 
MSS 20208-820: 
This section is obsolete because the information is documented in EPDM. 
9.1.7 
Addressing 
******* 
MAC/IP Addresses 
MSS 20208-677: 
The Ethernet MAC and IP addresses shall be accessible via ECU diagnostics, refer 
to [CANDELA_TEMPLATE], DID 0x016F. 
MSS 20208-678: 
The DID shall report all configured MAC and IP addresses of the ECU including the 
corresponding VLAN IDs. 
MSS 20208-679: 
The ECU shall report all MAC addresses including addresses not externally visible 
(e.g. inter process communication). 
******* 
Switch Addresses 
MSS 20208-681: 
The Ethernet switch address table shall be accessible via ECU diagnostics, refer to 
[CANDELA_TEMPLATE], DID 0x0170. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 70 页
MSS 20208, V2.0, 2018-08, page 70 
Copyright Daimler AG 2018 
MSS 20208-682: 
The DID shall report the address tables of the switch containing MAC-Addresses, 
Hash-Values and the corresponding port numbers. 
MSS 20208-683: 
For unassigned addresses, the ECU shall report the corresponding Address Data 
with 0x0. 
9.1.8 
Mirroring 
******* 
Mirroring Configuration 
MSS 20208-686: 
The Ethernet port mirroring configuration shall be accessible for write access via 
ECU diagnostics, refer to [CANDELA_TEMPLATE], DID 0x016C. 
MSS 20208-687: 
The DID is used to change the Port Mirroring Configuration. The parameters 
according to table T*******a shall be set per link. 
MSS 20208-688: 
 
Parameter 
Selection type 
Traffic direction 
Filter Switch number/Port Mask 
Source MAC Address Filter 
Destination MAC Address Filter 
VLAN ID Filter 
Mirroring packet divider 
Mirroring Mode 
Copy Switch and Port Number 
Port Mirroring Timeout 
 
 
Table T*******a: Parameter for mirroring configuration 
 
MSS 20208-689: 
The Ethernet port mirroring configuration shall be accessible for read access via 
ECU diagnostics, refer to [CANDELA_TEMPLATE], DID 0x016D. 
MSS 20208-690: 
The DID is used to read the Port Mirroring Configuration. Reported shall be the 
information according to Table T*******a 
MSS 20208-691: 
The Ethernet port mirroring configuration shall be accessible for delete access via 
ECU diagnostics, refer to [CANDELA_TEMPLATE], DID 0x0255. 
MSS 20208-692: 
The DID is used to delete the Port Mirroring Configuration.  
******* 
Mirroring Status 
MSS 20208-694: 
The Ethernet Port Mirroring shall be accessible for activation/deactivation via ECU 
diagnostics, refer to [CANDELA_TEMPLATE], DID 0x0256. 
MSS 20208-695: 
The DID is used to activate/deactivate the port mirroring configuration which was 
written before (and not deleted in the meantime) via DID 0x016C. 
MSS 20208-696: 
The Ethernet Port Mirroring Status information shall be accessible via ECU 
diagnostics, refer to [CANDELA_TEMPLATE], DID 0x016E. 
MSS 20208-697: 
The DID reports the current status (port mirroring enabled/disabled) of Port Mirroring 
for every port. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 71 页
MSS 20208, V2.0, 2018-08, page 71 
Copyright Daimler AG 2018 
9.2 
Failure monitoring 
9.2.1 
Hardware link failures 
MSS 20208-700: 
The link failures according to table T9.2.1a shall be detected within 3000ms after 
reception of the Diagnostic command, refer to section ******* 
MSS 20208-701: 
 
Failure 
Condition/Action 
Short detection 
 
The ECU shall be able to detect a short circuit between "ETH_P" and "ETH_N". 
In this case a specific DTC shall be stored. 
Open detection 
The ECU shall be able to detect interruptions in the "ETH_P" or  "ETH_N" line. 
In this case a specific DTC shall be stored. 
Wrong 
polarity 
detection 
The ECU shall be able to detect connections with wrong polarity according to 
the "ETH_P" or  "ETH_N" lines. In this case a specific DTC shall be stored. 
 
 
Table T9.2.1a: Hardware link failures for 100BASE-T1 
 
MSS 20208-1239:  
Failure 
Condition/Action 
Short detection 
 
The ECU shall be able to detect a short circuit between "G_ETH_P" and 
"G_ETH_N". In this case a specific DTC shall be stored. 
Open detection 
The ECU shall be able to detect interruptions in the "G_ETH_P" or  "G_ETH_N" 
line. In this case a specific DTC shall be stored. 
Wrong 
polarity 
detection 
The ECU shall be able to detect connections with wrong polarity according to 
the "G_ETH_P" or  "G_ETH_N" lines. In this case a specific DTC shall be 
stored. 
 
 
Table T9.2.1b: Hardware link failures for 1000BASE-T1 
 
MSS 20208-704: 
Link failures of every 100BASE-T1 / 1000BASE-T1 link shall be stored in specific 
DTCs separately, refer to [DIAG_R01_01]. 
9.2.2 
Wake-up line failures 
MSS 20208-706: 
The wake-up line failures according to table T9.2.1b shall be detected. 
MSS 20208-707: 
 
Failure 
Condition/Action 
Short detection WL 
to ground 
The ECU shall be able to detect a short circuit that lasts more than 5 
seconds between WL and ground. In this case a specific DTC shall be 
stored. 
Short detection WL 
to UBat 
The ECU shall be able to detect a short circuit that lasts more than 5 
seconds between WL and Battery. In this case a specific DTC shall be 
stored. 
Detection of general 
wake-up failure 
The ECU shall be able to detect non successful wake-up trials (e.g. 
because of interruption in WL). In this case a wake-up pulse is generated 
by the ECU, but no communication detected. A specific DTC shall be 
stored. 
 
 
Table T9.2.1b: Wake-up line failures 
 
MSS 20208-710: 
Failures of every Ethernet wake-up line of the ECU shall be stored in specific DTCs 
separately, refer to [DIAG_R01_01]. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 72 页
MSS 20208, V2.0, 2018-08, page 72 
Copyright Daimler AG 2018 
MSS 20208-711: 
Short detection of WL to ground can be realized either by monitoring the current 
limitation circuitry or by reading back the wake-up line while actively transmitting a 
wake-up pulse. 
MSS 20208-712: 
Short detection of WL to UBat can be realized by cyclic monitoring of the wake-up 
line. In case the wake-up line stays high for at least 5 seconds, a short to UBat can be 
assumed. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 73 页
MSS 20208, V2.0, 2018-08, page 73 
Copyright Daimler AG 2018 
10 Supplier qualification tests for 100BASE-T1 / 1000BASE-T1 ECUs 
10.1 Qualification Tests for ECUs with 100BASE-T1 / 1000BASE-T1 PHY or Switch 
MSS 20208-715: 
Each ECU sample 1,2 used in an EE-Release shall be tested by an independent test 
house according to [OPEN_ECU] before delivery to DAG. The test report shall be 
delivered to Vehicle Networking Department for every EE-Release by the ECU 
supplier. 
 
1 HW relevant test cases of [OPEN_ECU] can be omitted if ECU sample contains 
only SW relevant changes. 
2 For a 1000BASE-T1 Interface the PMA test cases in [OPEN_ECU] have to be 
provided by the test house manually, because [OPEN_ECU] does include 100BASE-
T1 only. 
MSS 20208-1254: Therefore it is necessary to include the AUTOSAR test modul "Testability Protocol 
and Service Primitives" (v1.2.0 or higher) in the ECU, which is used for the test 
according to [OPEN_ECU]. 
Please refer also to [MSS 10730], section 7.7.14 Protocol compatibility. 
MSS 20208-1255: The functionality of Ethernet ECUs shall be tested and verified according to [MSS 
20228] "MSS20228 Ethernet Networking Test Suite Specification". The supplier shall 
verify conformity for each ECU sample before it is delivered to Daimler. This shall be 
done by executing the Networking Test Suite, see [MSS 20220] "MSS20220 
Networking Test Suite Specification". 
10.2 100BASE-T1 / 1000BASE-T1 Wake-up Line Functional Evaluation 
MSS 20208-718: 
To ensure the function of the 100BASE-T1 / 1000BASE-T1 wake-up line the 
following functional test procedures for the ECU (DuT = Device under Test) shall be 
conducted for every EE-Release within the regular ECU component tests and the 
test results shall be provided to Vehicle Networking Group.  
10.2.1 100BASE-T1 / 1000BASE-T1 Worst Case Load Tests 
MSS 20208-720: 
The worst case load test is to ensure that the ECU is capable of generating a valid 
wake-up signal under all valid load conditions. This test is valid only for active wake-
up ECUs. The timing and content of messages is not in the scope of this test. 
MSS 20208-721: 
For the Wake-up Line worst case load test the ECU shall be supplied by the 
minimum acceptable battery voltage (UBat = 6.0V), a typical battery voltage (UBat = 
14.0V) and a maximum specified battery voltage (UBat = 16.0V). For all of these 
supply cases the ECU then shall be forced to generate a wake-up pulse while its WL 
pin is connected to a worst case load (ZLoad  =  300Ω parallel 100nF). The resulting 
wake up signal on the WL shall be recorded with a high impedance probe and an 
oscilloscope. The signal on the wake-up line has to be documented and it has to be 
verified that it fulfills the signal requirements for the wake-up pulse described in 
section "Wake-up signaling for 100BASE-T1 interfaces" (see e.g. Figure F8.1.7.4a). 
MSS 20208-722: 
The procedure according to table T10.2.1a shall be processed to evaluate the wake-
up line worst case load test. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 74 页
MSS 20208, V2.0, 2018-08, page 74 
Copyright Daimler AG 2018 
MSS 20208-723: 
 
 
Step 
Step Description 
Step Executed Result 
01_Precondition_01 DUT is connected to clamp 31 and to power 
supply (UBat = 14V +- 0,1V) with all respective 
ground and power pins 
 
01_Precondition_02 DUTs Wake-up Line pin is connected to 
ground via a 300Ohm (+/-1%) resistor and a 
100nF capacitor in parallel. The Wake-up Line 
pin is connected to an high impedance input of 
an scope (U_wakeup) and an current 
measurement input of an scope (I_wakeup, 
either by means of an current clamp (DC) or 
an shunt resistor (300Ohm requirement 
includes the shunt resistor) 
 
01_Precondition_03 All network interfaces of the DUT are 
connected and terminated correctly. * 
 
01_Precondition_04 DUT is in sleep mode (i.e. in its lowest power 
consumption mode). 
 
02_Action_01 
Measure the voltage U_Wakeup on the DUTs 
Wake-up Line and the current I_wakeup 
drawn into the wake-up line 
U_Wakeup < 0,4V 
Document current consumption of 
ECU on supply and document 
current drawn into wake-up line. 
02_Action_02 
Activate any DUT specific condition for waking 
up the bus except the ethernet wake-up line. 
 
 
Measure the voltage U_Wakeup and the 
current I_wakeup during and after the wake-
up pulse on the Wake-up Line. 
 
Measure the duration of the Wake-up pulse on 
the Wake-up Line (t_Wakeup). 
 
DUT shall create a wake-up pulse 
with 
4,5V<=U_Wakeup<=UBat, Max 
and 
500ms<=t_Wakeup<=1000ms 
After the wake-up pulse the 
voltage shall drop below  
U_Wakeup < 0,4V and the 
leakage current shall be below 
100uA. 
Document the wake-up current 
during the wake-up pulse as well. 
02_Action_03 
Deactivate all DUT specific conditions for 
keeping the bus awake. 
Wait for at least 10 seconds. 
DUT must go to sleep mode. 
02_Action_04 
Set UBAT = 6.0V +- 0,1V and repeat 
02_Action_01 to 02_Action_03. 
Same expected result as for 
02_Action_02 
02_Action_05 
Set UBAT = 16.0V +- 0,1V and repeat 
02_Action_01 to 02_Action_03. 
Same expected result as for 
02_Action_02 
*If the DUT is connected to a CAN network then at least one (simulated) communication partner is 
available (to avoid ACK errors). If the DUT is connected to a FlexRay network then at least two 
(simulated) coldstart nodes are available and FlexRay is synchron. If the DUT is connected to an Ethernet 
network then a (simulated) link partner is available. 
 
 
Table T10.2.1a: Active Wake-Up Test Execution 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 75 页
MSS 20208, V2.0, 2018-08, page 75 
Copyright Daimler AG 2018 
10.2.2 100BASE-T1 / 1000BASE-T1 Short circuit Load Test 
MSS 20208-725: 
This test is to ensure that the ECU is capable of handling a short circuit on the wake-
up line. This test is valid only for active wake-up ECUs. The timing and content of 
messages is not in the scope of this test. 
MSS 20208-726: 
For the short-cut load test the ECU shall be supplied by the maximum specified 
battery voltage (UBat = 16.0V) and then shall be forced to generate a wake-up pulse 
while its WL pin is connected to a short circuit to supply ground (ZLoad < 0.1Ω). The 
resulting current into the wake-up line shall be measured. It shall be verified that the 
ECU is able to withstand a short circuit of the wake-up line. 
MSS 20208-727: 
The procedure according to table T10.2.2a shall be processed to evaluate the wake-
up line short circuit load test. 
MSS 20208-728: 
 
Step 
Step Description 
Step Executed Result 
01_Precondition_01 
DUT is connected to clamp 31 and to 
power supply (UBat = 16V +- 0,1V) with all 
respective ground and power pins 
 
01_Precondition_02 
DUTs Wake-up Line pin is connected to 
ground 
via 
a 
short 
cut 
(<0,1Ohm 
resistance). The Wake-up Line pin is 
connected to an current measurement 
input of an scope (I_Wakeup, either by 
means of an current clamp (DC) or an 
shunt 
resistor 
(0,1Ohm 
requirement 
includes the shunt resistor) 
 
01_Precondition_03 
All network interfaces of the DUT are 
connected and terminated correctly.*  
 
01_Precondition_04 
DUT is in sleep mode (i.e. in its lowest 
power consumption mode). 
 
02_Action_01 
Activate any DUT specific condition for 
waking up the bus except the ethernet 
wake-up line. 
 
Measure the current I_shortcut and the 
duration of the current pulse on the Wake-
up Line (t_I,shortcut) 
DUT shall not be damaged.  
Document 
the 
wake-up 
pulse current and time for 
information purposes. 
Document 
if 
ECU 
has 
stored the wake-up line 
shortcut internally. 
02_Action_02 
Reset the DUT and deactivate all DUT 
specific conditions for keeping the bus 
awake. Wait for 10 seconds. 
DUT must go to sleep 
mode. 
02_Action_03 
Set UBAT = 6.0V +- 0,1V and repeat 
02_Action_01 to 02_Action_02. 
Document the current for 
information purposes as for 
02_Action_01 
*If the DUT is connected to a CAN network then at least one (simulated) communication partner 
is available (to avoid ACK errors). If the DUT is connected to a FlexRay network then at least 
two (simulated) coldstart nodes are available and FlexRay is synchronous. If the DUT is 
connected to an Ethernet network then a (simulated) link partner is available. 
 
 
Table T10.2.2a: Short Circuit Load Test Execution 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 76 页
MSS 20208, V2.0, 2018-08, page 76 
Copyright Daimler AG 2018 
10.2.3 100BASE-T1 / 1000BASE-T1 Wake-up Signal Corner Cases 
MSS 20208-730: 
The 100BASE-T1 / 1000BASE-T1 wake-up signal corner cases test is to ensure a 
stable wake-up even for all corner case signals shapes and to ensure no wake-up or 
failure indication for all signals out of range. The timing and content of messages is 
not in the scope of this test. 
MSS 20208-731: 
For wake-up corner case tests the ECU shall be supplied by the minimum 
acceptable battery voltage (UBat =6.0V), a typical battery voltage (UBat = 14.0V) and a 
maximum specified battery voltage (UBat = 16.0V). For all of these supply cases the 
ECU shall be switched to sleep mode/stays active by software and the reaction of 
the ECU to dedicated wake-up line signals shall be evaluated. 
MSS 20208-732: 
The procedure according to T10.2.3a and F10.2.3a shall be processed to evaluate 
the wake-up signal corner cases test for an ECU already active. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 77 页
MSS 20208, V2.0, 2018-08, page 77 
Copyright Daimler AG 2018 
MSS 20208-733: 
 
Step 
Step Description 
Step Executed Result 
01_Precon_01 
DUT is connected to clamp 31 and to power supply 
(UBat = 14,0V +- 0,1V). with all respective ground and 
power pins. 
 
01_Precon_02 
DUT is connected to a pulse or arbitrary signal 
generator with its Wake-up Line Pin.* 
 
01_Precon_03 
All network interfaces of the DUT are connected and 
terminated correctly.**  
 
01_Precon_04 
DUT 
is 
in 
active 
local 
mode 
with 
Ethernet 
communication deactivated. 
 
02_Action_01 
Measure 
the 
voltage 
(U_wakeup) 
and 
current 
(I_wakeup) on the DUTs Wake-up Line 
 
02_Action_02 
Generate a pulse with length = 750ms and level = 1,2V 
to the DUTs Wake-up Line and measure U_wakeup 
and I_wakeup. Wait for 5sec for DUTs reaction. *** 
DUT is not allowed to activate 
Ethernet communication. 
02_Action_03 
Generate a pulse with length = 9ms and level = 16,0V 
to the DUTs Wake-up Line and wait for 5sec for DUTs 
reaction. *** 
DUT is not allowed to activate 
ethernet communication. 
02_Action_04 
Generate a pulse with length = 750ms and level = 3,7V 
to the DUTs Wake-up Line and wait for 5sec for DUTs 
reaction. *** 
DUT 
shall 
activate 
Ethernet 
communication 
and 
transmit 
messages (if appropriate). 
02_Action_05 
Deactivate all DUT specific conditions for keeping the 
bus awake. Wait for 10 seconds. 
DUT must go to local mode. 
02_Action_06 
Generate a pulse with length = 21ms and level = 3,7V 
to the DUTs Wake-up Line and wait for 5sec for DUTs 
reaction. *** 
DUT 
shall 
activate 
Ethernet 
communication 
and 
transmit 
messages (if appropriate). 
02_Action_07 
Deactivate all DUT specific conditions for keeping the 
bus awake. Wait for 10 seconds. 
DUT must go to local mode. 
02_Action_08 
Generate a pulse with length = 750ms and level = 
14,0V to the DUTs Wake-up Line and wait for 5 
seconds for DUTs reaction. *** 
Calculate R_PD=U_wakeup/I_wakeup. 
DUT 
shall 
activate 
Ethernet 
communication 
and 
transmit 
messages (if appropriate). 
Document and verify R_PD. 
02_Action_09 
Deactivate all DUT specific conditions for keeping the 
bus awake. Wait for 10 seconds. 
DUT must go to local mode. 
02_Action_10 
Generate a pulse with length = 21ms and level = 16,0V 
to the DUTs Wake-up Line and wait for 5 seconds for 
DUTs reaction. 
DUT 
shall 
activate 
Ethernet 
communication 
and 
transmit 
messages (if appropriate). 
02_Action_11 
Deactivate all DUT specific conditions for keeping the 
bus awake. Wait for 10 seconds. 
DUT must go to local mode. 
02_Action_12 
Set UBat = 6,0V +- 0,1V and repeat 02_Action_01 to 
02_Action_11. 
The expected result for each action 
remains constant. 
02_Action_13 
Set UBat = 16,0V +- 0,1V  and repeat 02_Action_01 to 
02_Action_11. 
The expected result for each action 
remains constant. 
*The Wake-up Line pin is connected to an high impedance input of an scope (U_wakeup) and an current 
measurement input of an scope (I_wakeup, either by means of an current clamp (DC) or an shunt resistor 
(<0,1Ohm requirement for the shunt resistor). 
**If the DUT is connected to a CAN network then at least one (simulated) communication partner is available (to 
avoid ACK errors). If the DUT is connected to a FlexRay network then at least two (simulated) coldstart nodes are 
available and FlexRay is synchron. If the DUT is connected to an Ethernet network then a (simulated) link partner 
is available. 
*** Refer to Figure F8.1.7.4a. 
 
 
Table T10.2.3a: Passive Wake-up Test Execution for active ECU 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 78 页
MSS 20208, V2.0, 2018-08, page 78 
Copyright Daimler AG 2018 
MSS 20208-734: 
 
3,5 V
UWake-upLine 
UWL[V]
pulslength [ms]
16 V
14 V
UGND = 0 V
wake-up corner case testing
3,7 V
1,2 V
1,4 V
9ms 10ms
20ms 21ms
750ms
Pull-down 
calculation
ECU shall 
wake-up
ECU shall not 
wake-up
 
 
Figure F10.2.3a: Wake-up corner case testing for active ECU 
 
MSS 20208-735: 
The procedure according to T10.2.3b and F10.2.3b shall be processed to evaluate 
the wake-up signal corner cases test for an ECU woken from powerdown. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 79 页
MSS 20208, V2.0, 2018-08, page 79 
Copyright Daimler AG 2018 
MSS 20208-736: 
 
Step 
Step Description 
Step Executed Result 
01_Precon_01 
DUT is connected to clamp 31 and to power supply (UBat 
= 14,0V +- 0,1V) with all respective ground and power 
pins 
 
01_Precon_02 
DUT is connected to a pulse or arbitrary signal generator 
with its Wake-up Line Pin.* 
 
01_Precon_03 
All network interfaces of the DUT are connected and 
terminated correctly.**  
 
01_Precon_04 
DUT is in sleep mode (i.e. in its lowest power 
consumption mode). 
 
02_Action_01 
Measure U_wakeup and I_wakeup on the DUTs Wake-
up Line 
 
02_Action_02 
Generate a pulse with length = 750ms and level = 1,2V 
to the DUTs Wake-up Line and measure U_wakeup and 
I_wakeup on the DUTs Wake-up Line. Wait for 5sec for 
DUTs reaction. *** 
DUT is not allowed to wake up. 
02_Action_03 
Generate a pulse with length = 9ms and level = 16,0V to 
the DUTs Wake-up Line and wait for 5sec for DUTs 
reaction. *** 
DUT is not allowed to wake up. 
Remark: supply_hold shall not be 
activated within this time. 
02_Action_04 
Generate a pulse with length = 750ms and level = 3,7V 
to the DUTs Wake-up Line and wait for 5sec for DUTs 
reaction. *** 
DUT shall wakeup and transmit 
messages (if appropriate). 
 
02_Action_05 
Deactivate all DUT specific conditions for keeping the 
bus awake. Wait for 10 seconds. 
DUT must go to sleep mode. 
02_Action_06 
Generate a pulse with length = 120ms and level = 3,7V 
to the DUTs Wake-up Line and wait for 5sec for DUTs 
reaction. *** 
DUT shall wakeup and transmit 
messages (if appropriate). 
 
02_Action_07 
Deactivate all DUT specific conditions for keeping the 
bus awake. Wait for 10 seconds. 
DUT must go to sleep mode. 
02_Action_08 
Generate a pulse with length = 750ms and level = 14,0V 
to the DUTs Wake-up Line and wait for 5 seconds for 
DUTs reaction. *** 
Calculate R_PD=U_wakeup/I_wakeup. 
DUT shall wakeup and transmit 
messages (if appropriate). 
Document R_PD and verify if it is in 
the correct range. 
02_Action_09 
Deactivate all DUT specific conditions for keeping the 
bus awake. Wait for 10 seconds. 
DUT must go to sleep mode. 
02_Action_10 
Generate a pulse with length = 120ms and level = 16,0V 
to the DUTs Wake-up Line and wait for 5 seconds for 
DUTs reaction. 
DUT shall wakeup and transmit 
messages (if appropriate). 
 
02_Action_11 
Deactivate all DUT specific conditions for keeping the 
bus awake. Wait for 10 seconds. 
DUT must go to sleep mode. 
02_Action_12 
Set UBat = 6,0V +- 0,1V and repeat 02_Action_01 to 
02_Action_11. 
The expected result for each action 
remains constant. 
02_Action_13 
Set UBat = 16,0V +- 0,1V  and repeat 02_Action_01 to 
02_Action_11. 
The expected result for each action 
remains constant. 
*The Wake-up Line pin is connected to an high impedance input of an scope (U_wakeup) and an current 
measurement input of an scope (I_wakeup, either by means of an current clamp (DC) or an shunt resistor 
(<0,1Ohm requirement for the shunt resistor). 
**If the DUT is connected to a CAN network then at least one (simulated) communication partner is available (to 
avoid ACK errors). If the DUT is connected to a FlexRay network then at least two (simulated) coldstart nodes are 
available and FlexRay is synchron. If the DUT is connected to an Ethernet network then a (simulated) link partner 
is available. 
*** Refer to Figure F8.1.7.4a. 
 
 
Table T10.2.3b: Passive Wake-up Test Execution for ECU woken from powerdown 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 80 页
MSS 20208, V2.0, 2018-08, page 80 
Copyright Daimler AG 2018 
MSS 20208-737: 
 
3,5 V
UWake-upLine 
UWL[V]
pulslength [ms]
16 V
14 V
UGND = 0 V
wake-up corner case testing
3,7 V
1,2 V
1,4 V
9ms 10ms
20ms
120ms
750ms
Pull-down 
calculation
ECU shall 
wake-up
ECU shall not 
wake-up
 
 
Figure F10.2.3b: Wake-up corner case testing for ECU woken from powerdown 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 81 页
MSS 20208, V2.0, 2018-08, page 81 
Copyright Daimler AG 2018 
11 Supplier qualification tests for 100BASE-TX (Fast Ethernet) ECU inter-
faces 
11.1 100BASE-TX Conformance Test 
MSS 20208-741: 
To ensure the basic functionality of the 100BASE-TX interface the following test 
reports shall be provided to Vehicle Networking Group by the ECU supplier. 
11.1.1 Test Reports for Transceiver 
MSS 20208-829: 
According to ISO13400-3:DoEth-004/005/006 the diagnostic interface shall be 
qualified according to the test specifications listed in [100BASE_TX_IOP]. Therefore 
these test reports shall be provided to Vehicle Networking Group. 
MSS 20208-840: 
In addition to ISO13400-3 a testreport for "FTZ e.V. an der Westfälischen 
Hochschule Zwickau: Fast Ethernet Physical Layer; EMC Measurement 
Specification for Transceivers; Version 1.0 [FTZ_FE_EMC]" shall be provided to 
Vehicle Networking Group. 
MSS 20208-841: 
Remark: FTZ EMC Measurement Specification for Transceivers will be superseded 
by IEC62228-5 when the document is finished. 
11.1.2 Tests to be done by the ECU Supplier 
MSS 20208-833: 
Following Tests shall be performed by the ECU supplier and Test Reports shall be 
provided to Vehicle Networking Group: 
 
- UNH IOL CL25-PMD_Test_Suite_v3.5.pdf; only these test cases:  
* 25.1.4 Transmit Jitter 
* 25.1.6 Return Loss TX 
* 25.1.8 Transmit Clock Frequency 
* 25.2.1 Differential Input Impedance (Return Loss RX) 
 
- Eye-diagram according to Ansi X3.263:1995 Annex J with these testloads: 
* [specified test load] measured at ECU-Connector 
and additional automotive test loads: 
* [2m UTP - OBD - 1,5m CAT5 - specified test load] measured at OBD-Connector on 
connector side 
* [3m UTP - OBD - 15m CAT5 - specified test load] measured at OBD-Connector on 
connector side 
* [5m UTP - OBD - 50m CAT5 - specified test load] measured at OBD-Connector on 
connector side 
 
For the UTP-OBD connection between ECU and OBD-Connector the cables listed in 
table T11.1.2a shall be used. 
 
MSS 20208-834: 
 
Pair@ECU-Connector 
Daimler CAD-No. of cable 
Remarks 
TX-Pair@ECU 
H35/3 
100Ohm, twist length 20mm 
RX-Pair@ECU 
H35/5 
100Ohm, twist length 18mm 
 
 
Table T8.1.1.3a: Cables to be used for OBD 100BASE-TX ECU supplier tests  
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 82 页
MSS 20208, V2.0, 2018-08, page 82 
Copyright Daimler AG 2018 
11.2 100BASE-TX (FastEthernet) Activation Line 
MSS 20208-743: 
Functional testing of the activation line required. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 83 页
MSS 20208, V2.0, 2018-08, page 83 
Copyright Daimler AG 2018 
12 Design hints for ECUs with 100BASE-T1 / 1000BASE-T1 interface(s) 
MSS 20208-745: 
A document containing design hints for ECUs with 100BASE-T1 / 1000BASE-T1 
Interfaces [MSS20208-DH] can be obtained from Vehicle Networking Group upon 
request. 
MSS 20208-746: 
 
End of Main Document 
# # # # # 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 84 页
MSS 20208, V2.0, 2018-08, page 84 
Copyright Daimler AG 2018 
13 Annex A (informative) 
13.1 Revision History 
MSS 20208-749: 
 
Version 
Date 
Description 
Rel 
Author 
1.0 
2015-10-30 
Initial Release 
X 
AK, HG, MR, 
OG, SB, WW 
1.1 
2016-01-13 
- 
T*******a, T8.1.7.3a, T8.1.7.3b changed 
- 
IDs 788,789,790 added 
- 
minor corrections and clarifications 
Y 
AK, HG, MR, 
OG, SB, WW 
1.2 
2016-07-19 
- 
T6.4.1.1a : added Immediate NM transmissions 
- 
T6.4.1.1b : disabled Node Detect./Immed. Restart 
- 
T8.1.3.1a : added new CMC 
- 
T*******   : added Port Mirroring Timeout 
- 
ID 421, 422, 423 : changed text for clarification 
- 
ID 794, 795 : inserted for ARP support 
- 
ID 276, 796 : rephrased Handling of Resets 
- 
ID 250 : clarified “Start Communication” 
- 
ID 425, 427 : drawings changed for clarification 
- 
ID 426, 431, 436, 788, 445, 250, 232, 276 : changed text 
for clarification 
- 
ID 440 : footnote text changed for clarification,  
table numbering changed from 8.1.7.3a to 8.1.7.3c 
- 
ID 796, 797, 799 : added information 
- 
ID 338, 417 : MBN LV148-1 replaced by VDA320 
- 
ID 798: added according to requirement in MSS 21001 
- 
ID 803 - 808 inserted for informative purpose 
Y 
AK, HG, MR, 
OG, SB, WW 
2.0 
2018-08 
New Requirements: 
- 
Added section ******* Cycle time tolerances 
- 
Added section 6.4.5.7 Wake-up 
- 
Added section 8.2 and 8.7 for 1000BASE-T1 
- 
ID 1223 : Low Pass Filter for PHY 
- 
Added section ******* Cable Diagnostics 
- 
ID1237 : Enhanced Diagnostic service: 0x0251 for 
1000BASE-T1 Test Modes 
- 
ID1254 : AUTOSAR Testability Protocol and Service 
Primitives 
- 
ID1255 : Networking Test Suite 
 
Changed Requirements: 
- 
Section 7.1 : Reference on OPEN Switch requirements 
replaces former switch requirements 
- 
T8.1.2.3a: Recommended PHY table updated 
- 
Added [CANDELTA_TEMPLATE] for Diagnostics ser-
vices reference 
- 
Update of section normative references (e.g. [MBN 
LV124-1] was superseded by [MBN 10567]) 
- 
ID 226 : footnote changed 
- 
minor corrections and clarifications 
 
Deleted Requirements: 
- 
ID 120,12,122 : Double Tagging 
- 
ID 227 : Values defined in ECU Extract 
- 
ID 962 : No message in Instrument Cluster planed 
- 
Section 9.1 : Diagnostic services obsolete: 0x016A, 
0x016B, 0x0171, 0x0172, 0x0252 
- 
Section 9.1.6 Transceiver Identification 
- 
Section 8.1.6 Crystal Requirements 
X 
AK, HG, MRA, 
MRO, OG, SB, 
WW 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 85 页
MSS 20208, V2.0, 2018-08, page 85 
Copyright Daimler AG 2018 
MSS 20208-750: 
Release Format: X/Y/Z 
Z = internal draft releases of physical layer working team 
Y = internal releases to Ethernet/Ethernet project team or minor changes in public 
releases 
X= public releases/major changes 
13.2 Author information 
MSS 20208-752: 
 
Name 
Department 
EMail 
Initials 
Stefan Buntz 
RD/UBE  
<EMAIL> 
SB 
Werner Winter 
RD/UBE 
<EMAIL> 
WW 
Anika Kaschner 
RD/UPT 
<EMAIL> 
AK 
Oliver Glodd 
RD/UPT 
<EMAIL> 
OG 
Hartmut Günther 
RD/UPT 
<EMAIL> 
HG 
Martin Rahfeld 
RD/UPT 
<EMAIL> 
MRA 
Matthias Roos 
RD/UPT 
<EMAIL> 
MRO 
 
 
MSS 20208-753: 
End of Annex A 
# # # # # 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23

