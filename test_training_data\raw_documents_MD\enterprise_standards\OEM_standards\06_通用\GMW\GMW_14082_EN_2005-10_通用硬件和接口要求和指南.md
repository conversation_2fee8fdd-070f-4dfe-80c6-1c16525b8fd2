# GMW_14082_EN_2005-10_通用硬件和接口要求和指南.pdf

## 文档信息
- 标题：Microsoft Word - GMW14082Oct2005.doc
- 作者：zz0kjt
- 页数：74

## 文档内容
### 第 1 页
 
 
 
 
 
 
 
 
 
 
WORLDWIDE 
ENGINEERING 
STANDARDS 
General Specification 
Electrical Function 
GMW14082 
 
 
 
 
 
 
 
 
 
General Hardware and Interface Requirements and Guidelines 
 
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Originating Department: North American Engineering Standards 
Page 1 of 74
 
1 Introduction 
1.1 Scope. This document is the top document for 
all general electrical hardware requirements and is 
a part of the documents the Supplier receives with 
the request for Quotation for the sourcing of 
Electrical/Electronic components (E/E Devices) for 
automotive use. This document is valid for all 
components with Electrical/Electronic content and 
shall be referenced in each SSTS/CTS. 
1.2 Mission/Theme. This specification describes 
the hardware requirements which apply to most 
ECU’s. 
This 
specification 
also 
provides 
descriptions, 
recommended 
circuits, 
and 
parameters to be specified for most standard 
inputs and outputs. These definitions are intended 
to be used for Body, Interior and Exterior ECU’s 
1.3 Requirement Wording Conventions. Within 
this document the following conventions are 
applied: 
The word “Shall” shall be used in the following 
ways: 
a. To state a binding requirement on the ECU or 
the interfaces which comprise the ECU, which 
is verifiable by external manipulation and/or 
observation of an input or output. 
b. To state a binding requirement upon an ECU’s 
requirements document that is verifiable 
through a review of the document. 
The word “Must” shall be used to state a binding 
requirement on components/devices which are 
outside the scope of this specification. 
The word “Will” shall be used to state an 
immutable law of physics. 
The word “Should” denotes a preference or 
desired conformance. 
Note: In the event of a conflict between the text of 
this specification and the documents cited herein, 
the text of this specification takes precedence. 
Note: Nothing in this specification supersedes 
applicable laws and regulations unless a specific 
exemption has been obtained. 
2 References 
Note: Only the latest approved standards are 
applicable unless otherwise specified. 
2.1 External Standards/Specifications. 
ANSI/IPC-J-STD-001D 
IPC-7351 
IPC-6012 
 
2.2 GM Standards/Specifications. 
GMW3001 
GMW3097 
GMW3059 
GMW3103 
GMW3091 
GMW3172 
3 Requirements 
3.1 System / Subsystem / Component / Part 
Definition. 
3.1.1 Appearance. Not applicable. 
3.1.2 Content. Not applicable. 
3.1.3 Ambient Environment. Electronic and 
Electrical 
subsystems/components 
shall 
pass 
environmental and durability validation when 
subjected to loads according to GMW3172. 
Requirements are defined in the CTS/SSTS via 
Code lettering according to GMW3172. 
******* Functional Classification. Functional 
classification is a general method for defining the 
functional performance status classification (FSC) 
for the functions of automotive Electrical/Electronic 
devices [E/E device] upon exposure to test 
conditions or real world operating conditions. 
*******.1 Class Definition. 
FSC_A: All functions of an E/E device/system 
perform as designed during and after exposure to 
a disturbance. 
FSC_B: All functions of an E/E device/system 
perform as designed during exposure; however, 
one or more function may go beyond the specified 
tolerance. All functions automatically return to 
within normal limits after exposure is removed. 
Memory functions shall remain class A. The E/E 
device/system shall not output any false actuation 
signals, erroneous serial data messages, false 
fault codes, or other erroneous I/O commands or 


### 第 2 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 2 of 74 
October 2005
 
states when the voltage is outside of the range 
defined for FSC_A. 
FSC_C: A function of an E/E device/system does 
not perform as designed during exposure but 
automatically returns to normal operation after 
exposure is removed. The E/E device/system shall 
not output any false actuation signals, erroneous 
serial data messages, false fault codes, or other 
erroneous I/O commands or states when the 
voltage is outside of the range defined for FSC_A. 
FSC_D: A function of an E/E device/system does 
not perform as designed during exposure and does 
not return to normal operation until exposure is 
removed and the device/system is reset by simple 
“operator/use” action. 
FSC_E: One or more functions of an E/E 
device/system do not perform as designed during 
and after exposure and cannot be returned to 
normal operation without repairing or replacing the 
E/E device/system. 
3.1.4 Interfaces. 
3.1.4.1 Electrical Interface Definitions. 
3.1.4.1.1 General Naming Of Electrical Signals. 
Vs: Voltage supply, general voltage source derived 
from the vehicle battery. 
Vs_prot: Voltage supply, general voltage source, 
reverse polarity protected. 
Vs_sw: Switched voltage supply, controlled on/off 
by software and reverse polarity protected. 
Vcc: Regulated power supply voltage, normally 
5V. 
Vcc_sw: 
Switched 
regulated 
power 
supply 
voltage, normally 5V and controlled on/off by 
software. 
Vbatt: Vehicle battery voltage, permanent supply 
from the vehicle battery. 
Vbatt_prot: Vehicle battery voltage, permanent 
supply from the vehicle battery, reverse polarity 
protected. 
Vrun/crank: Ignition key controlled battery from 
Vbatt source. 
Vacc: Ignition key controlled battery from Vbatt 
source. 
Vign: Ignition key controlled supply, general type 
(Vrun/crank or Vacc), reverse polarity protected. 
Gnd: Ground, unspecified type or grounding 
location. 
Signal_gnd: signal ground local to ECU. 
Power_gnd: power ground local to ECU. 
Battery_minus: 0 V in the vehicle, global 0V. 
Body_gnd: Unspecified ground bolt grounding. 
BIW_gnd: Vehicle body in white ground. Electrical 
point on body for connection of ground wire to 
battery minus. 
Engine_gnd: Unpainted common electrical 
connection point on engine block. 
Ui: Interface signal, input voltage. 
Uo: Interface signal: output voltage. 
U_Pn: Voltage drop between positive battery pole 
and ECU power input pin. 
Usat: Voltage drop over fully switched on 
transistor. 
U_GNDn: Voltage drop between negative battery 
pole and ECU ground input pin. 
Is: supply current, general. 
Io: Interface signal output current. 
Ii: Interface signal input current. 
3.1.4.1.2 Voltage and Current Directions. 


### 第 3 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 3 of 74
 
Ci
Rpd*
Rpu*
Co
Ii
Io
Is_1
Is_2
-
+
-
+
Vs_2
Vs_1
Gnd_1
Gnd_2
Uo
Ui
Output
Input
HSD*
LSD*
*=One of or both
 
Figure 1: Definitions of Input and Output Voltages and Current Directions Used 
3.1.4.1.3 Voltage Supply and Signal Noise 
Model. The design limits for the electrical power 
and signal distribution system are: 
Values given are for steady state conditions 
(>500ms) excluding the crank sequence. 
a. U_P voltage offset between any platform or 
powertrain ECU: ± 1.0V. 
b. U_GND voltage offset between any powertrain 
ECU grounded to the engine block: ± 0.5V. 
c. U_GND voltage offset between any platform 
ECU: ± 0.8V. 
d. U_GND voltage offset between any platform 
and powertrain ECU: ± 1.0V. 
VB
UB
+ Un -
U_GND3=0 to +0.8V
U_GND4=0 to +0.8V
U_P3=0 to +1.0V
U_P4=0 to +1.0V
U_GND2=0 to +0.5V
U_P2=0 to +1.0V
-0.2V to +0.5V
0 to +0.2V
U_GND1=0 to +0.5V
U_P1=0 to +1.0V
0 to +0.2V
Starter
Motor
+
-
Generator
+
-
-
+
-
+
-
+
-
+
V_gnd_ref= 0 V
+
-
+
-
+
+
Uo3
Ui4
-
-
Platform Ground Signal Distribution
ECU3
ECU4
Battery minus
+
+
-
ECU2
Ui2
Including ground -wire, -bolts and -sheet metal voltage drops
Vehicle Battery
+
-
-
-
Power Train Power Signal Distribution
Platform Power Signal Distribution
+1.0V@Crank
+
-
+
+
-
Ui1
-
ECU1
signal_gnd_3
signal_gnd_4
signal_gnd_2
signal_gnd_1
+
-
BIW_gnd
GMW8763
Power Train Ground Signal Distribution
* = DC current directions
*
*
*
*
*
ground cable
voltage drop
Engine_gnd
Engine block ground
Body In White ground
*
ground cable
voltage drop
*
Figure 2: Voltage Supply and Signal Noise Model 
 


### 第 4 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 4 of 74 
October 2005
 
******* Supply Lines. 
*******.1 System Voltage Requirements. All 
values, ranges, and tolerances stated in this 
section shall apply over the entire operating 
environment (e.g., voltage, temperature, humidity, 
etc.) They shall also apply over all normal 
environmental conditions applied to the attached 
components. 
All voltages are measured with respect to the 
Power Ground Electrical Interface input connection 
on the ECU. 
The voltage sources providing power to the ECU 
(i.e., the vehicle battery, generator, or jump-start) 
will be collectively referred to as system voltage 
within this document. 
System voltage may vary over the range as 
defined in GMW3172. 
System voltage may be derived, for use within the 
ECU, from either Battery (Vbatt) or Ignition 
switched connections (Vign). It may also be 
applied to ECU interfaces through an external load 
component. 
The specific path by which system voltage is 
applied will be defined in specific electrical 
interfaces or in the general requirements sections 
below. 
All 
External 
Electrical 
Interfaces 
shall 
fulfill 
electrical requirements according to GMW3172 
and the following conditions: 
a. Depending on the nature of a battery supplied 
system with a generator, any voltage in this 
range of +0 to +Vmax can exist on the car for a 
long time. The actual rise and fall times and 
durations of voltage variations on the system 
are random within this range. 
b. An ECU that has supply sections or MOSFET 
gate drivers which are automatically switched 
off by hardware in the +16 to +26.5V range 
shall be tested with a slowly varying supply 
voltage of 1 Volt/minute around the actual 
switch off voltage level.  
Note: The purpose of this test is to avoid problems 
caused by running switch transistors in the analog 
range mode with switch losses in a catastrophic 
state. 
c. The ECU shall withstand, without damage, 
simultaneous driving of system voltage, within 
the Normal Operating Voltage Range (Vmin to 
Vmax), to both Battery and Ignition inputs and 
any I/O signals pulled up through external 
loads to system voltage. This driving of system 
voltage 
may 
include 
multiple 
“bounce” 
transitions in the signals (e.g., due to a battery 
cable being connected with the ignition switch 
in the Run position), GMW3172. 
d. Each 
interface 
shall 
withstand, 
without 
damage, application of voltages within the 
Vehicle Ground Voltage Range (± 1V) to any of 
its input or output connections. 
*******.2 Normal Operating Voltage Range. A 
GMW3172 Operating Voltage range Code shall be 
defined 
in 
each 
Component 
Technical 
Specification (CTS) or Subsystem Technical 
Specification (SSTS). 
Subsystems that are necessary or essential for the 
starting procedure shall have electrical load 
requirements according to code letter A or B. 
Other electronic subsystems/components used on 
the vehicles shall have electrical Code Letter B 
(+6V V min) regarding micro controller and 
memory functions with FSC_A performance. 
Signal conditioning of all sensors based on analog 
voltage/current values or pulse counting (including 
electronic circuits/chips), by default, shall also 
continue to operate within spec down to a system 
voltage of +6.0V (GMW3172 electrical load code 
letter B) or shall be switched off below FSC_A 
system voltage and be blocked from being treated 
as a valid signal. 
Electronic subsystems/component shall not output 
any false actuation signals, erroneous serial data 
messages without setting DTC, or other erroneous 
I/O commands or states when the voltage is 
outside of the range defined for FSC_A. 
 


### 第 5 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 5 of 74
 
 
Table 1: Operating Voltage Range Codes 
Vnom 
14.5 
V 
 
 
 
 
 
 
 
 
Steady 
State 
Supply 
Voltage 
-13 < 0 
[V] 
0 < 4.5 
[V] 
4.5 < 6 
[V] 
6 < 9 
[V] 
9 < 10 
[V] 
10 < 12 
[V] 
12 < 16 
[V] 
16 < 18 
[V] 
Vmin [V]
Vmax 
[V] 
Code 
GMW 
3172 
↓ 
FSC_ 
FSC_ 
FSC_ 
FSC_
FSC_ 
FSC_ 
FSC_ 
FSC_ 
 
 
A 
C 
C 
A 
A 
A 
A 
A 
C 
4.5 
16 
B 
C 
C 
C 
A 
A 
A 
A 
C 
6 
16 
C 
C 
C 
C 
C 
A 
A 
A 
C 
9 
16 
D 
C 
C 
C 
C 
A 
A 
A 
A 
9 
18 
E 
C 
C 
C 
C 
C 
A 
A 
C 
10 
16 
F 
C 
C 
C 
C 
C 
C 
A 
C 
12 
16 
Z 
C 
C 
Defined 
by 
SSTS 
or 
CTS 
in 
detail 
 
 
*******.2.1 Normal Battery Voltage During Crank. 
5
1 2 V
6 V
5 V
1 5 -40
5 0
1 0 ,0 00
1 00
T im e in m illiseco nd s ®
-
S ystem
V o ltage
 
Figure 3: Normal Battery Voltage During Crank 
 
The normal crank operating voltage range of the 
ECU is defined by GMW3097 test pulse 4. Unless 
otherwise specified in the CTS/SSTS the default 
dwell time at +5V shall be 15 ms. 
Figure 3 shows the maximum voltage drop during 
each portion of the crank sequence.  
Any voltage waveform regarding time and voltage 
sequences can exist above this envelope drawn 
during crank, depending on a large number of 
factors like ambient temperature, oil temperature, 
time since last crank, battery state of charge, driver 
key closure time and so on.  
The GMW3097 pulse 4 test cases cover a limited 
number of situations to show correct functionality. 
The real hardware design must be closely 
analyzed and simulated to find any weak points 
caused by design that can lead to unintended 
behavior and/or setting unintended permanent 
trouble codes during crank. 
*******.3 Battery Input Electrical Interface. The 
ECU shall provide at least one (1) Battery input. 
The voltage driven on the Battery input signal will 
be referred to as Vbatt throughout this document. 
The Battery input signal shall be used as the power 
source for software controlled power moding and 


### 第 6 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 6 of 74 
October 2005
 
High Side Driver Electrical Interface if specifically 
required. It may be used for Ignition Independent 
Non-Volatile Data Memory retention. It may also be 
used by various internal circuits within the ECU.  
The Battery input signal will be driven from an 
automotive generator/battery voltage source in the 
range from reverse battery condition to max Vbatt, 
jump start condition. Due to the three-phase 
generator operation, the Battery input signal may 
have non-sinusoidal ripple GMW3172. 
The reverse polarity protection mechanization of 
Vbatt to the ECU internal Vbatt_prot is dependent 
on the Vmin requirement. The following can be 
used and are listed in order of decreasing voltage 
drop: a Si diode, a Schottky diode or a P-channel 
MOSFET can be used. 
Power_gnd
Vbatt
Vbatt_prot
* = Complete transient protection will depend on GMW3097 results
*
*
Alternative reverse protection schemes
 
Figure 4: Battery Input Electrical Interface 
*******.4 Ignition Input Electrical Interfaces. The 
ECU shall provide Run/Crank (Vrun/crank) and 
Accessory 
(Vacc) 
Ignition 
Input 
Electrical 
Interfaces if required by the ECU application. 
The Run/Crank and Accessory input signals shall 
be monitored as described in the analog inputs 
section. 
Each ignition input electrical interface shall have a 
resistive load to ground to have a defined voltage 
state in all operating conditions. The resistive load 
to ground shall be chosen to provide a maximum 
time constant of 1 second. (See Figure 5) 
Since both the Run/Crank and Accessory input 
signals are mechanically switched versions of 
Battery (Vbatt), they will both be driven over 
identical voltage ranges to Vbatt and have identical 
frequency (ripple) content.  
Note: The Battery, Run/Crank, and Accessory 
input signals may not be driven to identical 
voltages at all points in time. 
Power_gnd
Vacc
Vrun/crank
Vrun/crank_prot
Vacc_prot
* = Complete transient protection will depend on GMW3097 results
Signal_gnd
*
*
Figure 5: Ignition Input Electrical Interface 
*******.5 Sneak Circuits, Supply Path. Sneak 
circuits are paths through which unintended current 
can flow under certain conditions. Sneak circuits 
resulting from ECU Supply voltage interfaces that 
are sourced by two or more external supply signals 
shall not cause damage or undesired behavior. 
To protect against unintended current flows 
between Electronic Control Units due to sneak 
circuits, all power supply lines routed from 
Vrun_crank, Vacc or other Ignition Input Electrical 
interfaces shall be decoupled by means of a diode 
(Vr>250V) or equivalent device whose power is 
suitable for the current involved (See Figure 5) 
*******.6 Power Supply Trace Requirements. 
Power supply traces shall be able to continuously 
withstand 1.5 times the sum of all specified 
currents for supplied loads. These traces shall 
survive short circuit tests to battery and ground 
according to GMW3172 without damage. 
*******.7 
Immunity 
to 
Power 
Supply 
Interruptions. Precautions shall be taken to meet 
requirements of FSC_C under any interruption 
conditions on any ignition input interface controlled 
supply line (Vign). 
Components that perform a shutdown procedure 
when the ignition line is switched off must filter the 
ignition line state with a time duration of at least 30 
ms by the means of hardware and/or software 
(sampling).  
Any interruption on the Vrun/crank or Vacc supply 
lines of less than 30 ms shall not cause the internal 
state to alter, unless a valid state change is 
recognized (key turn). 
*******.8 Power Ground Electrical Interface. The 
ECU shall provide a Power Ground Electrical 
Interface (Power_gnd) that satisfies the following 
requirements: 
a. The voltage at the Power Ground Electrical 
Interface external connection shall serve as the 
ground reference for all ECU electrical 
measurements. 
b. It shall be capable of sinking, without damage, 
the sum of all of the ECU return currents. 


### 第 7 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 7 of 74
 
*******.9 Signal Return Electrical Interface. The 
ECU shall provide a Signal Return Electrical 
Interface (Signal_rtn) that satisfies the following 
requirements: 
a. The signal return interface shall be separated 
inside the ECU from the digital and power 
ground. Digital, power and signal ground shall 
be connected together at a single point as 
close as possible to the connector of the ECU. 
b. It shall provide the number of external 
connections defined by the CTS Application 
I/O tables (the actual number used may vary 
with the application). 
c. It shall be capable of sinking, without damage, 
the sum of all of the currents driven by all of 
the Power Output Electrical Interfaces. 
d. It shall be capable of sinking the minimum 
required current driven by all of the Power 
Output Electrical Interfaces with a maximum 
voltage drop of 0.01V relative to Power_gnd. 
e. In case of multiple ground interfaces outside of 
the ECU, the electrical ground concept shall be 
approved by the responsible GM electrical 
architecture engineer. 
*******.10 Sneak Circuits, Ground Path. Sneak 
circuits that result when the Power Ground 
Electrical interface of an ECU is connected to two 
or more external ground signals shall not cause 
any damage or undesired behavior. Additionally 
efforts shall be taken to prevent sneak circuits that 
occur through intrinsic diodes in MOSFETs and 
similar components. 
The design of an ECU Ground Electrical Interface 
shall be evaluated for potential sneak circuits and 
approved by the appropriate GM Engineer before it 
is finalized. 
*******.11 Power Output Electrical Interfaces. 
The ECU shall provide these types of buffered 
power outputs if needed to supply external slave 
units or sensors. 
Each power output shall meet the following general 
requirements in addition to any output specific 
requirements defined by the CTS: 
a. Power outputs shall meet their operational 
requirements whenever the “on” condition 
holds and Vs is in the range Vmin to Vmax. 
b. Power outputs shall be separately buffered. A 
fault (open, ground short, power short) on any 
power output external connection shall not 
affect operation of other power outputs or any 
internal ECU voltage reference (e.g., Battery, 
Ignition, micro-processor power, or the A/D 
converter reference). 
c. Power 
outputs 
shall 
withstand, 
without 
damage, short circuits to any voltage source in 
the range -1.0V to the maximum normal 
operating voltage (Vmax), and recover normal 
operation, without software action, once the 
fault condition is removed. 
Table 2 shows the Generic power output interface 
requirements for single voltage types (multiple 
channels can exist in one ECU depending on 
need). 
 
Table 2: Power Output Interface Requirements 
Power 
Output 
Name 
Type 
Tolerance 
+-% 
Current 
Capacity 
Fault 
trigger 
limit 
Transient 
Time [us] 
Current limit 
threshold 
Inrush delay 
Vref_5V 
Buffered 5V 2 
100mA 
0.3V 
200 
>100mA 
Load 
dependent 
Vref_12V 
Buffered 
12V 
N/A 
100mA 
N/A 
200 
>100mA 
Load 
dependent 
 
*******.11.1 
Buffered 
5V 
Output 
Electrical 
Interface. The Buffered 5V Electrical Interface 
output shall meet the following requirements 
unless otherwise specified in the applicable 
CTS/SSTS: 
a. It shall be regulated to 5.0 V ± 0.1 V while 
driving a maximum electrical current, I_out, of 
100mA and while Vbatt is in the normal 
operating voltage range of Vmin to Vmax. 
b. It shall be monitored and report a fault to the 
ECU software if the voltage leaves the required 
regulation range. The fault shall be reported no 
later than when the voltage equals 5.0 V ± 
0.3V. 
c. It shall include filtering to prevent reporting a 
fault condition unless the output is outside of 
regulation limits for at least 200µs. 
d. It 
shall 
drive 
the 
number 
of 
external 
connections 
defined 
by 
the 
CTS/SSTS 
Application I/O table. 
e. It shall be capable of continued operation after 
all Ignition Voltage is removed (Ignition Off) 


### 第 8 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 8 of 74 
October 2005
 
until the Power-Down Procedure has been 
completed. 
The inrush current generated by switching on 
connected sensors shall not lead to a fault 
condition. Actual sensor loads must be analyzed if 
digital 
short 
circuit 
protection 
switch 
off 
mechanisms are used. A nominal capacitive load 
of 10uF shall be tolerated. 
The buffered 5V output shall track the A/D 
converter reference to give the best possible 
conversion accuracy. 
D*
I_out
C_out
Vcc
Vref_5V
Vs
Power_gnd
+
-
uC
signal_gnd
5V LDO
Voltage
Regulator
Short to Gnd & Vbatt 
Protection
Vout
D* = Reverse polarity protection mechanization Vmin dependent
Figure 6: Buffered 5V Supply 
*******.11.2 Buffered 12 V Output Electrical 
Interfaces. 
D*
I_out
C_out
Vcc
Vs
Power_gnd
Vref_12V
+
-
uC
signal_gnd
5V LDO
Voltage
Regulator
Short to Gnd & Vbatt 
Protection
Vz
Vout
D* = Reverse polarity protection mechanization Vmin dependent
 
Figure 7: Buffered 12V Supply 
The Buffered 12V Electrical Interface output shall 
meet the following requirements: 
a. The output (Vout) shall be buffered and 
unregulated 
but 
with 
the 
electrical 
characteristics listed in Table 3. 
b. The output shall be monitored and report a 
fault to the ECU software if the requirements 
are not met. 
c. The output shall include filtering to prevent 
reporting of a fault condition unless the output 
is out of regulation limits for at least 200µs.  
d. The output shall drive the number of external 
connections 
defined 
by 
the 
CTS/SSTS 
Application I/O table. 
Table 3: Buffered 12V Supply Characteristics 
Parameter 
Min 
Limit 
Max 
Limit 
Units 
Test 
Conditions 
Vout= 
Vs-1 
Vs 
V 
Vs=Vmin to 
Vmax 
Vout= 
 
Vmax 
V 
Vs=18 
to 
26.5V 
1 
minute 
Vout= 
-0.5 
 
V 
Vs=-13.5V 
2minutes 
Vout= 
 
40 
V 
Vs at any 
voltage, 
including 
high voltage 
(EMC) 
transients 
3.1.4.3 
General 
Electrical 
Interface 
Requirements. This document describes the most 
common interfaces to be used on the platform. 
Voltage and current values given in the tables shall 
be handled as general interface requirements. 
Equivalent 
schematics 
shall 
be 
treated 
as 
guidelines for standard circuit solutions using off 
the 
shelf 
components, 
circuits 
and/or 
microcontrollers. Full protection mechanizations to 
reach GMW3097/GMW3172 requirements are not 
shown in detail for output drivers. 
By using dedicated I/O circuits, the same 
functionality 
can 
be 
realized 
with 
fewer 
components/lower cost, but the basic requirements 
regarding voltage/current levels and immunity to 
GMW3097/GMW3172 requirements shall be kept.  
The supplier always has full responsibility to 
implement the supplies and I/O protection needed 
in 
order 
to 
meet 
the 
requirements 
in 
GMW3097/GMW3172. 
CAN and LIN type serial interfaces are described 
by other GMW documents. 
3.1.4.3.1 
Interface 
Active 
State 
Currents. 
Preferred active state interface currents for digital 
signals and switches are in the range of 4 to 20 mA 
at the normal operating supply voltage.  
This is the basis for the general guidelines given 
and detailed interfaces documented in Appendix A.  
Analog signal inputs have higher impedance levels 
in the range of <50kΩ (0 to 0.1mA interface signal 
current).  
Note: depending on the sensor output impedance, 
analog interface signal handling can be more 
sensitive to radiated immunity requirements. 


### 第 9 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 9 of 74
 
3.1.4.3.2 Short Circuit Protection. All inputs and 
outputs of the ECU (except power and power 
ground interfaces) must be permanently short 
circuit protected against shorts to battery or ground 
potentials over the entire normal operating voltage 
and temperature range. 
It is preferred that grounded sensor returns be 
protected against shorts to battery. The current 
through the ECU that occurs with a short circuit 
has to be reduced with appropriate means such 
that no irreversible damage occurs in the ECU. 
3.1.4.3.3 Digital Input Electrical Interfaces. 
Figure 8 shows a generic schematic applicable to 
digital signal receivers or switch input interfaces. 
Driving side can be pullup/pulldown switch designs 
or high/low side transistor drivers. The input can 
handle ground offsets of up to ± 1.0V. This means 
that the signal driver or switch side can be 
grounded locally to body ground bolts or the 
engine block and not brought back separately to 
the receiver signal_gnd node. 
Requirements on PWM_type and Frequency_type 
control 
signals 
are 
described 
in 
separate 
paragraphs. 
R1
R2
C1
Rp
+
-
D*
I_inj
Ii
Io
Rp_source
Vcc
Vs
Vth
Power_gnd
Vign_prot
Vs_sw
+
-
+
-
10nF@Input_pin
Vth/L/H: common values: 30/70% or 35/65% of Vcc for CMOS
5V* LDO
Voltage
Regulator
uC
Signal_gnd
5V*: Lower Vcc supply levels than 5V can be used
Rpu
Rpd
I_inj: Injection current into IC-input when Vin > Vcc
Signal source
or switch
Gnd
D* = Reverse polarity protection mechanization Vmin dependent
Ui
Uo
transmitter
receiver
 
Figure 8: Digital Input Electrical Interfaces 
3.1.4.3.4 Input Signal Clamping. As external 
input pin voltages can/will be higher than the active 
interface circuit supply Vcc and lower than Gnd, 
the CMOS IC or uP input pin design shall limit the 
internal pin voltage/current to a safe level (IC 
dependent) with the help of an external series 
resistor R1 and internal diode clamps. If this is not 
possible due to IC pin injection current limitations 
(I_inj_max), external clamping to the IC shall be 
used. If external clamp diodes are used they shall 
be schottky type to guaranty that they clamp the 
input current before the IC internal silicon diodes 
start to conduct. In all conditions where an IC-pin 
current injection takes place with current flowing 
back to the Vcc node via input protection clamp 
diodes, the Vcc level shall be limited to safe levels 
by the voltage regulator itself or an additional 
voltage limit device on the Vcc net. 
3.1.4.3.5 Digital Input Signal Threshold Voltage 
Levels at the Input Pin. Vmin, Vmax and Vnom 
are defined by GMW3172 Electrical Load coding. 
Note: With the voltage divider R1-R2 shown in 
figure 8, the commonly used logic H/L thresholds 
(30/70% or 35/65%) can be tailored to meet the 
±1V ground shift requirement referenced to the 
input pin. By also using a 5V LDO regulator and a 
Schottky reverse protection diode, the correct 
signal interface voltages can be maintained down 
to a battery voltage of Vs=Vmin=6V.  
Table 4: Digital Input Thresholds 
Parameter 
Condition Min. Typ. Max 
Unit
Ui Input 
Low_state 
Uil 
Vmin<Vs<
Vmax 
-1 
0 
2 
V 
Ui Input 
High_state 
Uih 
Vmin<Vs<
Vmax 
5.5 
Vnom Vmax
+1 
V 
3.1.4.3.6 Time Constant, General Interface. The 
default input electrical time constant (seen at the 
IC input node Vth) shall be 1ms (excluding PWM 
and frequency type inputs). This time constant is 
set by R1, R2 and C1 (see Figure 8). 
3.1.4.3.7 Floating Interface Inputs. Due to the 
emphasis on reducing the quiescent current on all 
E/E devices in the car, there shall never be any 
interface left floating in any power state, on, off or 


### 第 10 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 10 of 74 
October 2005
 
sleep (Figure 8) connected to an ECU that is Vbatt 
supplied. 
a. In the case where Rp is a pulldown resistor 
(Rpd) the external input voltage to the input pin 
shall be zero in sleep mode. 
b. In the case where Rp is a pullup resistor (Rpu) 
and Vs_sw or Vign is switched off, there shall 
be a defined resistance to ground in the 
interface circuit (like R2 in figure 8). 
3.1.4.3.8 Transient Protection. The default input 
protection on all external connector pins shall be a 
ceramic 
100V 
DCWV 
capacitor 
(see 
the 
10nF@input_pin placeholder on the General Input 
Interface 
Schematic). 
Regardless 
of 
actual 
capacitor value chosen based on GMW3097 
results, the physical PCB copper pad design shall 
make it possible to mount a 10nF 0805 size X7R 
ceramic capacitor. If speed requirements disqualify 
a 10nF component the capacitor shall be 
replaced/reduced and the transient protection shall 
be designed with the help of an active device 
(varistor, TVS or similar). 
Switch input interfaces shall create a sufficient 
contact cleaning current. Note: this can easily be 
achieved by using a 10nF capacitor on the input. 
3.1.4.3.9 Switch Input Software Debounce Time. 
Each mechanical switch input signal activates a 
specific function. To make this activation secure a 
default software debounce time of at least 30 ms 
shall be used at both activation and deactivation of 
all switch signals. During this 30ms, the switch 
status must be sampled a number of times (greater 
than or equal to 3) and the same logical level must 
be 
recognized 
during 
each 
sample 
before 
changing the functional state. Edge triggering can 
be used for wakeup or interrupts but shall not be 
used in a switch state recognition algorithm. 
3.1.4.3.10 Influence Of Environmental Factors 
On ON-OFF Switch/Sensor Acquisition: During 
normal operation, control unit acquisition of these 
commands shall not be influenced by the following 
events and conditions: 
The status of an individual sensor and/or switch 
shall not remain undetermined or change even if 
environmental conditions are such that a 50 ohm 
resistance is formed in series with the switch 
connections as a result of oxidation, and a 
resistance as indicated below is formed in parallel 
with the switch connections as a result of moisture: 
a. 5 kOhm for occupant compartment sensors 
and switches when using Rp=3.0 kΩ. 
b. 2 kOhm for all other exterior sensors and 
switches when using Rp=1.2 kΩ. 
3.1.4.3.11 Digital Input Interface Tables. The 
ECU shall provide digital input types chosen from 
the Appendix A Digital or switch input interface 
paragraphs. The specific number of each type of 
each interface (which may be zero) shall be 
defined by an Application I/O Table in the CTS or 
SSTS. 
The Input Electrical Interface provides conditioning 
of the input voltage (electrical or mechanical 
switch) and, based on the predefined thresholds, it 
recognizes the input as one of two logical states, 
defined to be A and P. An associated I/O function 
reports this logical state directly or measures 
defined timing properties of the state transitions. 
A = Active State 
P = Passive State 
3.1.4.3.12 Analog Input Interfaces. 
R1**
51kOhm
C1
Rpu*
D*
Ii
Io
I_inj
R_no_float
Vcc
sensor_supply*
Vs
Power_gnd
Vcc_sw
+
-
signal_rtn
+
-
10nF@Input_pin
A/D Converter Input
Resolution 8 or 10 Bit of A/D_Vref
A
*Rpu and need for sensor_supply application dependent
Vref+
Vref-
uC
In
D
signal_gnd
Sensor
5V LDO
Voltage
Regulator
**A/D input protection and accuracy dependent on R1
Gnd
D* = Reverse polarity protection mechanization Vmin dependent
Ui
Uo
receiver
transmitter
One common R_no_float load for each separate Vcc_sw net
Figure 9: Analog Input Interface (Vin < Vref) 


### 第 11 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 11 of 74
 
It is important that measurements made by an 
analog interface be as accurate and sensitive as 
possible to ensure that a given function can be 
performed in full. Whether an 8-bit or 10-bit 
converter is used will depend on the required 
measurement accuracy. Measurements shall be 
ratiometric when possible, supplying the sensor 
with the same positive and negative voltage 
reference as the ECU A/D-converter reference 
inputs unless otherwise specified in the applicable 
CTS/SSTS.  
If all analog sensor/measuring channels used are 
supplied from the same Vcc_sw switched supply, 
the A/D Vref can be connected to the Vcc_sw net 
to minimize the pass transistor influence on the 
measuring accuracy. 
Active sensors shall have a resistive pull down 
(Rpd) on the receiving side. The default pulldown 
value shall be 51 kΩ. Pullup (Rpu) resistor 
mounting and value depend on the sensor 
interface required. 
Passive 
sensors 
like 
NTC/PTC 
thermistors, 
reostats or potentiometers shall have a pullup 
(Rpu) value matched to the sensor type (<50kΩ).  
Where input signal reduction is necessary, as in 
the case of supply voltage (Vbatt, Vrun/crank or 
Vacc) measurements, the resistances used shall 
have a tolerance and stability over time that 
matches 
the 
overall 
measurement 
accuracy 
needed. 
A Filter shall be included on all analog inputs with a 
default time constant of 1ms (R1*C1). 
R1**
10k
C1
Rpu*
D*
I_inj
R2*/**
Ii
Io
R_no_float
Vcc
sensor_supply*
Vs
Power_gnd
Vs_sw
+
-
signal_rtn
+
-
10nF@Input_pin
A/D Converter Input
Resolution 8 or 10 Bit of A/D_Vref
A
*Rpu, R2 values and need for sensor_supply application dependent
Vref+
Vref-
uC
In
D
signal_gnd
Sensor
5V LDO
Voltage
Regulator
**A/D input protection and accuracy dependent on R1//R2
Gnd
D* = Reverse polarity protection mechanization Vmin dependent
Ui
Uo
receiver
transmitter
One common R_no_float load for each separate Vs_sw net
 
Figure 10: Analog Input Interface (Vin > Vref) 
 
3.1.4.3.12.1 
A/D 
Converter 
Input 
Resistor 
Selection (Figures 9, 10 and 11: R1). The input 
protection resistor, R1, will influence different 
requirements set up by the microcontroller supplier 
(with integrated A/D converters): 
a. R1 is needed for A/D circuit input overvoltage 
protection (current limiter in series with internal 
A/D pin clamp diodes). 
b. A/D input injection current limits, I_inj, are 
based on: 
• 
Destruction of the A/D input circuit. 
• 
Crosstalk to adjacent A/D channels. 
c. A/D accuracy due to IC pin leakage, I_leak, 
current flow through the sensor source resistor 
(Rsensor+R1//R2). 
Typical design values for a microcontroller based 
A/D converter are: 
a. I_leak =  1uA from A/D pin 
b. I_inj = < 2.5mA for maintained function 
c. I_inj => 25mA destructive 
d. 8bit 1/2LSB error: 10mV 
e. 10bit 1/2LSB error: 2.5mV 
These values combined with a 10nF input pin 
capacitor require that R1 be chosen to be 10kOhm 
in order to survive GMW3097 ESD and transients 
on the signal lines. 
In practice, the accuracy of the A/D converter will 
be limited to 8 bits due to the I_leak error current 
flowing through R1 (10mV error with R1=10k). 
3.1.4.3.13 Analog Sensor Signal Return. Analog 
sensor signal returns (signal_rtn) shall be provided 
with individual input pins for each analog sensor. If 
splicing and summation of sensor return signals is 
done in the wire harness this shall be approved by 
a 
GM 
electrical 
architecture 
engineer. 
No 
grounding of sensor signals shall be done to body 


### 第 12 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 12 of 74 
October 2005
 
grounds or to the engine block in parallel to or 
instead of the ECU signal_gnd return. 
3.1.4.3.14 Analog Switch Input Interface With 
Diagnostic Functionality. The schematic in 
Figure 11 has resistor values chosen to handle 
ground offsets of up to 0.8V. This means that the 
switch return can be connected to local body 
ground bolts, and need not be routed back to a 
separate switch input pin on the ECU. 
States that can be recognized: 
a. Short to ground error 
b. Open switch signal 
c. Closed switch signal 
d. Open circuit or short to battery error 
R1**
51k
C1
680 +/-1%
D*
390 +/-1%
1500 +/-1%
390 +/-1%
1500 +/-1%
Ii
I_inj
R_no_float
Vcc
Vs
Power_gnd
Vcc_sw
+
-
signal_rtn
+
-
+
-
10nF@Input_pin
A/D Converter Input
Resolution 8 or 10 Bit of A/D_Vref
A
Vref+
Vref-
uC
In
D
signal_gnd
5V LDO
Voltage
Regulator
OR
Gnd
Alternative switch 
grounding
**A/D input protection dependent on R1
D* = Reverse polarity protection mechanization Vmin dependent
Ui
receiver
switch
Uo
Uo
One common R_no_float load for each separate Vcc_sw net
 
Figure 11: Analog Switch Input Interface with Diagnostics 
 
3.1.4.3.15 ON-OFF Commands Acquired Via an 
Analog Two Wire Resistor Coding Interface: A 
parallel resistive ladder switch with up to 11 On/Off 
switch states can be achieved if the switches 
involved feature a closed-contact resistance which 
is less than or equal to 5 ohm at all times. In 
practice this requires a gold to gold switch element. 
When fewer switches are used in the resistor 
ladder, alternative contact materials can be used. 
In Figure 12, switch S1 has the highest priority, 
overriding all others and switch Sn has the lowest 
priority. 
Rin
C1
Rpu
D*
S1
S2
R2
S3
R3
Sn
Rn
Rn+1
R_no_float
Ii
Rpd
Rcomp
Vcc
Vs
Power_gnd
Vcc_sw
+
-
signal_rtn
10nF@Input_pin
A/D Converter Input
Resolution 8 or 10 Bit of A/D_Vref
A
Vref+
Vref-
uC
In
D
signal_gnd
5V LDO
Voltage
Regulator
...
D* = Reverse polarity protection mechanization Vmin dependent
One common R_no_float load for each separate Vcc_sw net
Ui
receiver
keypad
 
Figure 12: Resistive Ladder Switch Input Interface 
 


### 第 13 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 13 of 74
 
Table 5: Resistor Ladder Resistor Values 
Resistor code table 
Note: All resistors are 1% except for Rpd and Rcomp which are 5%. 
 
Table 6: Resistor Ladder Voltage Thresholds 
Number 
of 
functions, (max 
closed 
switch 
impedance) 
SW1 
< x 
Volts 
SW2 < 
x 
> 
SW1 
Volts 
SW3 < 
x 
> 
SW2 
Volts 
SW4 < 
x 
> 
SW3 
Volts 
SW5 < 
x 
> 
SW4 
Volts 
SW6 < 
x 
> 
SW5 
Volts 
SW7 < 
x 
> 
SW6 
Volts 
SW8 < 
x 
> 
SW7 
Volts 
SW9 < 
x 
> 
SW8 
Volts 
SW10 < 
x > SW9 
Volts 
SW11 < 
x 
> 
SW10 
Volts 
3 (20 Ω) 
1.05 
2.31 
3.5 
 
 
 
 
 
 
 
 
4 (20 Ω) 
0.95 
2.02 
3.04 
3.99 
 
 
 
 
 
 
 
5 (20 Ω) 
0.85 
1.75 
2.59 
3.38 
4.11 
 
 
 
 
 
 
6 (20 Ω) 
0.87 
1.51 
2.21 
2.84 
3.5 
4.08 
 
 
 
 
 
7 (20 Ω) 
0.73 
1.4 
2.02 
2.61 
3.16 
3.69 
4.17 
 
 
 
 
8 (20 Ω) 
0.7 
1.32 
1.89 
2.44 
2.95 
3.42 
3.87 
4.3 
 
 
 
9 (5 Ω) 
0.44 
0.88 
1.33 
1.8 
2.28 
2.74 
3.21 
3.67 
4.1 
 
 
10 (5 Ω) 
0.39 
0.85 
1.28 
1.72 
2.16 
2.62 
3.07 
3.5 
3.93 
4.35 
 
11 (5 Ω ) 
0.41 
0.78 
1.17 
1.58 
2.0 
2.42 
2.84 
3.24 
3.64 
4.03 
4.41 
Note: An open state is any voltage greater than SWn. 
3 switch example for 5.1 ≤ ADC supply ≤ 4.9: 
• 
SW1 active � Ui ≤ 1.05 volts 
• 
SW2 active � 1.05 < Ui ≤ 2.31 volts 
• 
SW3 active � 2.31 < Ui ≤ 3.5 volts 
• 
Open (switch disconnected) � Ui > 3.5 volts 
 
3.1.4.3.16 
PWM 
Type 
Control 
Signals. 
GMW3097 has guidelines regarding slew rates for 
current and voltage signals routed in the vehicle 
wire harness. This will affect the mechanization of 
PWM type control signals. At the same time PWM 
signals carry information in the pulse width that 
shall keep a defined accuracy of the information 
passed in the interface. With limits on the voltage 
slew rate of the PWM signal, the ground offset 
between ECU´s not grounded at the same point 
will also create a pulse width variation seen at the 
receiver end of the interface. 
Combining PWM signal information resolution 
requirements with ground offset affects and limits 
on voltage signal slew rates gives the following 
PWM 
interface 
equivalent 
schematic 
and 
parameter table. Depending on whether the PWM 
interface is supplied by Vs (other ECU) or Vcc_sw 
(sensor/slave with local signal return) two sets of 
Number of 
functions, 
(max closed 
switch 
impedance) 
R2 
Ohm 
R3 
Ohm 
R4 
Ohm 
R5 
Ohm 
R6 
Ohm 
R7 
Ohm 
R8 
Ohm 
R9 
Ohm 
R10 
Ohm 
R11 
Ohm 
R12 
Ohm 
Rpu 
Ohm 
Rpd 
Ohm 
Rcomp 
Ohm 
3 (20 Ω) 
105 
226 
845 
 
 
 
 
 
 
 
 
237 
10k 
10 
4 (20 Ω) 
82.5 
154 
365 
2000 
 
 
 
 
 
 
 
237 
10k 
10 
5 (20 Ω) 
64.9 
105 
187 
422 
2000 
 
 
 
 
 
 
237 
10k 
10 
6 (20 Ω) 
52.3 
73.2 
113 
196 
402 
1240 
 
 
 
 
 
237 
10k 
10 
7 (20 Ω) 
45.3 
61.9 
86.6 
130 
221 
453 
1370 
 
 
 
 
237 
10k 
10 
8 (20 Ω) 
41.2 
53.6 
73.2 
105 
158 
261 
576 
1870 
 
 
 
237 
10k 
10 
9 (5 Ω) 
24.3 
32.4 
42.2 
57.6 
82.5 
121 
205 
374 
953 
 
 
237 
10k 
10 
10 (5 Ω) 
23.2 
30.1 
39.2 
51.1 
71.5 
105 
162 
274 
634 
2150 
 
237 
10k 
10 
11 (5 Ω ) 
20 
26.1 
33.2 
44.2 
57.6 
82.5 
115 
182 
309 
715 
2740 
237 
10k 
10 


### 第 14 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 14 of 74 
October 2005
 
requirements apply regarding logic level thresholds 
and maximum PWM frequency. 
Table 7: PWM Signal Characteristics 
Interface 
Type 
 
Rp 
Interface 
Supply 
Vs=12V 
Vcc=5V 
Interface 
Ground 
f_pwm_max
[Hz] 
PWM 
Valid 
signal 
range 
% 
Rise/Fall 
time 
0.005/f_pwm
[us] 
Ground 
shift 
Tolerated 
[V] 
Vil 
Input 
logic 
level 
low 
[V] 
Vih 
Input 
logic 
level 
high 
[V] 
A 
1.2kOhm 
Vs 
Body/Engine_block 
400 
5-95 
12 
± 1.0 
-1 to 
+2.0 
+5.5 to 
Vmax+1
B 
1.2kOhm 
Vcc 
Signal return 
1000 
5-95 
5 
0 
0 to 
+1.5 
+3.5 to 
+5.0 
 
R1
R2
C1
Rp
+
-
D*
I_inj
R1
Rp
+
-
D*
I_inj
C1
Irx
Itx
Irx
Itx
Rp_source
Vcc
Vs
Vth
Power_gnd
Vs_sw
Rp_source
Vcc
Vs
Vth
Power_gnd
Vcc_sw
slave_supply
Vs_prot
Vs_prot
+
-
+
-
signal_rtn
+
-
+
-
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
signal_gnd
Rpu
Rpd
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
signal_gnd
PWM transmitter
PWM out
Slave unit
PWM out
Type A, General PWM interface. Vs supplied
Type B, slave unit PWM interface with Vcc <= 5V supply
Rpu
Rpd
f_pwm < 400 Hz
f_pwm < 1kHz
Gnd
D* = Reverse polarity protection mechanization Vmin dependent
Utx
Urx
Utx
Urx
transmitter
receiver
 
Figure 13: PWM Type Interfaces 
 
3.1.4.3.16.1 Time Constant, PWM Receiver. The 
default PWM receiver input electrical time constant 
is dependent on the interface pullup source 
voltage. 
a. For a 12V supplied PWM control signal 
receiver, the nominal input time constant 
(defined by R1, R2 and C1) seen at node Vth 
shall be 5µs for a maximum PWM frequency of 
400Hz (t_rise/2.2). 


### 第 15 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 15 of 74
 
b. For a 5V supplied PWM control signal receiver, 
the nominal time constant (defined by R1 and 
C1) seen at node Vth shall be 2 µs for a 
maximum PWM frequency of 1kHz.(t_rise/2.2). 
3.1.4.3.17 Frequency Type Control Signals. 
3.1.4.3.17.1 Definition. Frequency modulation 
(FM) means that transmitted information is 
assigned to the frequency of a current or voltage 
signal. Either two frequencies (Frequency shift 
keying FSK) for transmitting binary information or 
continuous frequency interval for transmitting 
analog signals should be used. In any case, the 
duty cycle contains no information. 
3.1.4.3.17.2 Physical Interface. The frequency 
information shall be transmitted either by a current 
interface or a voltage interface. 
3.1.4.3.17.2.1 Current Interface. The current 
interface consists of a Current Transmitter CTx and 
Current Receiver CRx. 
The 
current 
receiver 
supplies 
the 
current 
transmitter with operating voltage. 
Rsense
G
Itx
D*
Itx
Irx
R_no_float
signal_rtn
signal_supply
Vs_prot
+
-
Vs
Power_gnd
+
-
Vs_sw
Utx
Urx
D* = Reverse polarity protection mechanization Vmin dependent
Sensor or
Slave unit
signal_gnd
transmitter
receiver
Current Interface
One common R_no_float load for each separate Vs_sw net
SC-protection
 
Figure 14: Frequency Type Control Signals 
3.1.4.3.17.2.1.1 Current Transmitter Tx. The 
Current transmitter supplied by the current receiver 
switches between two current values (low current 
and high current). The dominant state is indicated 
by the active high current. 
 


### 第 16 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 16 of 74 
October 2005
 
Table 8: Frequency Signal – Current Transmitter Characteristics 
 Absolute maximum Ratings 
Symbol 
Parameter 
Conditions 
Min 
Typ. 
Max 
Unit 
Vs 
Supply voltage 
GMW3172 Temperature requirement 
-13.5 
14 
26.5 
V 
Operating range 
Symbol 
Parameter 
Conditions 
Min 
Typ. 
Max 
Unit 
Vs 
Supply voltage 
Operating (Tmin-
Tmax) 
Vmin 
14 
Vmax 
V 
Itxl 
Transmitting current 
Low state 
Min Load and 
GMW3172 
Temperature 
requirement 
5.6 
7 
8.4 
mA 
Itxh 
Transmitting current 
High state 
Min Load and 
GMW3172 
Temperature 
requirement 
11.2 
14 
16.8 
mA 
Itxh/Itxl 
Current ratio 
Min Load and 
GMW3172 
Temperature 
requirement 
2 
 
3 
 
dI/dt 
Current slew rate 
0.1*(Itxh-Itxl)+Itxl to 
0.9*(Itxh-Itxl)+Itxl 
falling edge reverse 
at Itxh/Itxl max. 
10 
 
100 
mA/ µs 
F 
Frequency range 
 
0 
 
15000 
Hz 
 
3.1.4.3.17.2.1.2 Current Interface Receiver. The 
Current receiver supplies the current transmitter 
with operational voltage and detects the two 
current states. Dominant state is active high 
current. Current interface receiver Rx 


### 第 17 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 17 of 74
 
Table 9: Frequency Signal – Current Receiver Characteristics 
Absolute maximum Ratings 
Symbol 
Parameter 
Conditions 
Min 
Typ. 
Max 
Unit 
Vs 
Supply voltage 
GMW3172 Temperature requirement 
-13.5 
14 
26.5 
V 
Operating range 
Symbol 
Parameter 
Conditions 
Min 
Typ. 
Max 
Unit 
Vs 
Supply voltage 
GMW3172 Temperature requirement 
Vmin 
14 
Vmax 
V 
Irxl 
Receiving current 
Low state 
Min Load and GMW3172 
Temperature requirement 
5.4 
7 
8.6 
mA 
Irxh 
Receiving current 
High state 
Min Load and GMW3172 
Temperature requirement 
11 
14 
17 
mA 
Irxh/Irxl 
Current ratio 
Min Load and GMW3172 
Temperature requirement 
1.8 
 
3.2 
 
dI/dt 
Current slew rate 
0.1*(Itxh-Itxl)+Itxl to 0.9*(Itxh-Itxl)+Itxl 
falling edge reverse 
10 
 
100 
mA/ 
µs 
f 
Frequency range 
 
0 
 
15000 
Hz 
*) Voltage depends on the operational Voltage definition of the device 
 
3.1.4.3.17.2.1.3 Current Interface Resistances. 
Due to signal processing inside the receiver a load 
resistance has to be defined e.g. 115 Ω ± 1%. 
Based on an unintended leakage of signal current 
the corresponding leakage resistor should not be 
less than 100 times the load resistance. 
 
Resistances 
Symbol 
Parameter 
Conditions 
Min 
Typ. 
Max 
Unit 
Rsense 
Load Resistor 
Operation voltage range 
Rsense 
-1% 
Rsense
Rsense
+1% 
Ω 
Rleakage 
Leakage Resistor 
Operation voltage range 
100 
* 
Rsense 
 
 
Ω 
 
3.1.4.3.17.2.1.4 Duty Cycle. 
Symbol 
Parameter 
Conditions 
Min 
Typ. 
Max 
Unit 
tPuls/T 
Duty cycle 
Trigger level 0.5*(Itxh-Itxl)+Itxl  
0.33 
0.5 
0.66 
 
t jitter 
Duty 
cycle 
time 
Stability 
Trigger level 0.5*(Itxh-Itxl)+ 
1 
 
0.005/f_max 
µs 
 
3.1.4.3.17.3 Voltage Interface. The Voltage 
interface consists of a Voltage Transmitter (VTx) 
and Voltage Receiver (VRx). 
The voltage receiver and voltage transmitter are 
independently supplied if Vs=12V. The transmitter 
is supplied from the receiver if transmitter supply is 
5V and a signal return (signal_rtn) to the receiver is 
required. The dominant state can be defined to be 
either active high voltage or active low voltage. 
The signal interface carries its information in the 
frequency value that shall keep a defined accuracy 
of the information passed. With limits on the 
voltage slew rate of the signal the ground offset 
between ECU´s not grounded at the same point 
will also create a frequency variation seen at the 
receiver end of the interface. 
Combining 
signal 
information 
resolution 
requirements with ground offset effects and limits 


### 第 18 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 18 of 74 
October 2005
 
on voltage signal slew rates gives the frequency 
interface equivalent schematic shown in figure 15 
and the parameters given in table10.  
Depending on whether the frequency interface is 
supplied 
by 
Vs 
(other 
ECU) 
or 
Vcc_sw 
(sensor/slave with local signal return) two sets of 
requirements apply to the logic level thresholds. 
R1
R2
C1
Rp
+
-
D*
I_inj
R1
Rp
+
-
D*
I_inj
C1
Input_pin+
Rp_source
Vcc
Vs
Vth
Vin
Power_gnd
Vs_sw
Input_pin+
Rp_source
Vcc
Vs
Vth
signal_rtn
Vin
Power_gnd
Vcc_sw
slave_supply
Vs
Vs_prot
+
-
+
-
+
-
+
-
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
Signal_gnd
Rpu
Rpd
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
Signal_gnd
frequency out
Slave unit
Type A, General frequency type voltage interface. Vs supplied
Type B, slave unit, frequency type voltage interface with 5V supply
Rpu
Rpd
f < 15 kHz
f < 15 kHz
Gnd
D* = Reverse polarity protection mechanization Vmin dependent
frequency out
V_tx
V_rx
V_tx
V_rx
Frequency transmitter
Frequency receiver
 
Figure 15: Voltage Interface 
 
Table 10: Frequency Signal – Voltage Interface Characteristics 
 
3.1.4.3.17.3.1.1 Duty Cycle. 
Interface 
Type 
 
Rp 
Interface 
Supply 
Vs=12V 
Vcc=5V 
Interface 
Ground 
f_max
[kHz] 
Duty 
cycle
% 
Rise/Fall 
time 
0.005/f_max
 
[us] 
Ground 
shift 
Tolerated
[V] 
Uil 
Input 
logic 
level 
low 
[V] 
Uih 
Input logic level 
high 
[V] 
A 
1.2kOhm 
Vs 
Body/Engine_block 
15 
33-
66 
>12 
± 1.0 
-1 to 
+2.0 
+5.5 to 
Vmax+1 
B 
1.2kOhm 
Vcc 
Signal return 
15 
33-
66 
>5 
0 
0 to 
+1.5 
+3.5 to +5.0 


### 第 19 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 19 of 74
 
Table 11: Frequency Signal – Voltage Interface Duty Cycle 
Symbol 
Parameter 
Conditions 
Min 
Typ. 
Max 
Unit 
tPuls/T 
Duty cycle 
Trigger level 0.5*(Utxh-Utxl)+Utxl 
0.33 
0.5 
0.66 
 
t jitter 
Duty 
cycle 
time 
stability 
Trigger level 0.5*(Utxh-Utxl)+Utxl 
1 
 
0.005/f_max 
µs 
 
3.1.4.3.18 
Frequency 
Modulated 
signals 
(Physical Layer). 
3.1.4.3.18.1 Frequency shift keying (FSK). The 
physical layer for transmitting FSK signals could 
either be a Voltage interface or a current interface 
as described above. The logic high frequency 
should be approximately twice the logic low 
frequency e.g. low state � 100 Hz; logic high � 
200 Hz. 
Table 12: FSK Requirements 
Symbol 
Parameter 
Conditions 
Min 
Typ. 
Max 
Unit 
f_low 
Low state frequency 
 
100 
+-1% 
 
1000 
+-1% 
Hz 
f_high 
High state frequency 
 
200 
+-1% 
 
2000 
+-1% 
Hz 
f_high/flow 
Frequency ratio 
 
1.8 
2 
2.2 
 
t_delay 
Transmitting delay 
time 
 
 
 
1 
ms 
t_recogn 
Receiving 
recognition time 
 
 
 
10 
ms 
 
3.1.4.3.18.2 Continuous Frequency. The physical 
layer for transmitting continuous frequency signals 
could be either a Voltage interface or a current 
interface as described above. 
Table 13: Continuous Frequency Requirements 
Symbol 
Parameter 
Conditions 
Min 
Typ. 
Max 
Unit 
f_range 
Frequency range 
 
0 
 
15000 
+-1% 
Hz 
t_delay 
Transmitting 
delay 
time 
 
 
 
1 
ms 
t_recogn 
Receiving 
recognition time 
 
 
 
10 
ms 
 
******* Output Driver Interfaces. To avoid RFI 
problems, all power output drivers shall have a 
controlled rise and fall time while keeping the pass 
element power dissipation at a safe level (SOAR). 
PWM type power drivers that have loads 
connected through the vehicle wire harness shall 
have slew rates limited to less than 1V/µs and 
100mA/µs. This requirement limits the maximum 
f_pwm to 400Hz for a Vs supplied output driver 
(high or low side switching) and Vcc supplied 
slave/sensor PWM outputs to f_pwm_max=1kHz..  
When higher PWM frequencies are used (400Hz < 
f_pwm <25kHz) the power driver shall be 
assembled on the load itself (example: integrated 
PWM driver on fan motor ), and only the PWM 
command signal interface shall be routed through 
the wire harness. The power driver/load supply 
lines shall be filtered with a pi-filter (LC-type)). 
Regardless of the output load type (low/high side 
connected loads resistors, coils, relays or motors) 
the design of the avalanche energy absorption 
from the inductive load at switch off shall always be 
handled by the electronic output driver stage. 


### 第 20 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 20 of 74 
October 2005
 
In the vehicle electrical system including the wire 
harness there are no loads seen by power drivers 
that are only resistive. All power drivers shall 
handle their full current load value in series with a 
minimum inductance value of 20µH if an inductive 
load is not specified. 
Freewheel diodes shall never be used in parallel 
with coil loads for relay drivers or actuators (like 
injectors) where fast turn off time is critical for 
correct function and/or contact durability. 
Interfaces with inductive or incandescent lamp 
loads shall always be validated for reliability over 
the full operating temperature and voltage range 
with accurate load values. The Supplier is 
responsible to correlate equivalent loads (if used) 
during validation with the real loads in the car when 
available to the program. 
*******.1 Low Side Drivers. Figure 16 shows both 
an equivalent low side driver circuit including 
avalanche energy protection realizations for fast 
switch off loads and one low side driver for PWM 
type loads with free wheel diode connected in 
parallel with the coil.  
(Short circuit protection and diagnostics are not 
shown): 
R_load
L_load
Io
B
A
10n
R_load
L_load
Io
10n
-
+
Vs
Vs_prot
+
-
Power_gnd
Vs_prot
V_load
Uo
Uo
on/off control
pwm control
Type A: Fast switch off loads
Type B: PWM controlled loads
 
Figure 16: Low Side Driver 
 
Table 14: Figure 16 Key 
Symbol 
Parameter 
Io 
Output Current 
Uo 
Output Voltage 
Vs 
Source Voltage of Controller 
Vs_load 
Source Voltage of Load 
Vgnd 
Ground Voltage 
Tmax 
Maximum Ambient Operating Temperature 
 (GMW3172 coding) 
Tmin 
Minimum Ambient Operating Temperature 
 (GMW3172 coding) 
 
Table 15: Low Side Driver Characteristics  
Parameter 
Value/Conditions 
Uo @Io_max 
<1.0V @Tmin to Tmax 
Io_max 
Maximum 
current 
according 
to 
CTS/SSTS @Vs_load_max 
Load 
Resistance: 
R_load 
Defined by CTS/SSTS 
Load 
Inductance: 
L_load 
Defined by CTS/SSTS (20uH if not 
specified) 
Load Inductive Energy 
E = ½ * (L_load) * (Io_max)
2 
Output Capacitance 
10 nF default value 
Actuation 
Frequency: 
AF 
Defined by CTS/SSTS (< 400Hz for 
PWM 
drive 
loads 
through 
wire 
harness) 
Rise time 
<1V/µs. 
Nominal value 0.005 
* 
(1/(AF)) 
Shorts protection 
To Vbatt and Gnd 
 


### 第 21 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 21 of 74
 
Depending on the load type and actuation 
frequency, different avalanche energy handling 
schemes may be implemented. For a normal 
avalanche rated MOSFET the output drain to 
source zener (protection B in above figure) can 
handle a large single pulse absorption. This 
capability is very dependent on the junction 
temperature as is shown in the graph in Figure 17. 
For high energy loads or repetitive load switching a 
more robust design like the drain to gate zener 
(protection A in above figure) shall be used. 
 
Figure 17: Typical MOSFET Avalanche Energy 
 
Rs
Ls
Rp
10n
Rs
Ls
Rp
D2
10n
Vs
relay_output
Vs
relay_output
ECU
Typical coil values @25C:
Rs=72 Ohm (>50 Ohm @ -40C)
Ls= 300mH
Rp=680 Ohm
ECU
Equivalent Circuit
Relay Coil
Loads with reverse
polarity protection
Loads without reverse
polarity protection
 
Figure 18: Relay Driver Output 
*******.2 Relay Drivers. Standard GM relays 
normally have a resistor (Rp) in parallel with the 
coil in the range of 500 to 700 Ohms (10x Rs). 
Relay drivers for Vbatt connected relays feeding 
E/E devices not protected for reverse voltage shall 
have a series diode to prevent coil activation under 
reverse voltage conditions due to the MOSFET 
substrate diode. 
*******.3 Table 16: Figure 18 Key 
Symbol 
Parameter 
Lo 
Output Current 
Uo 
Output Voltage 
Vs 
Source Voltage of Controller and Load 
Tmax 
Maximum Ambient Operating Temperature 
 (GMW3172 coding) 
Tmin 
Minimum Ambient Operating Temperature 
 (GMW3172 coding) 
Table 17: High Side Driver Characteristics 
Parameter 
Value/Conditions 
Uo @Io_max 
Vs - 1.0V @Tmin to Tmax 
Io_max 
Maximum 
current 
according 
to 
CTS/SSTS @Vs_load_max 
Load 
Resistance: 
R_load 
Defined by CTS/SSTS 
Load 
Inductance: 
L_load 
Defined 
by 
CTS/SSTS 
(20uH 
minimum if not specified) 
Load Inductive Energy 
E = ½ * (L_load) * (Io_max)
2 
Output Capacitance 
10 nF default value 
Actuation 
Frequency: 
AF 
Defined by CTS/SSTS (< 400Hz for 
PWM drive through wire harness) 
Rise time 
<1V/µs in wire harness power lines. 
Nominal value 0.005 * (1/(AF)) 
Shorts protection 
To Vbatt and Gnd 
 


### 第 22 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 22 of 74 
October 2005
 
R_load
L_load
10n
B
A
R_load
L_load
10n
D1*
Io
Io
Vs
-
+
+
-
-
+
+
-
Power_gnd
Uo
Usat
Uo
Usat
* For high current loads
D1 is normally replaced by a
syncronous rectifier
Typ A: Fast switch off loads
Typ B: PWM controlled loads
pwm control
on/off control
 
Figure 19: High Side Driver Output 
 
*******.4 H-Bridge Driver Precautions. The 
normal application for a H-bridge driver is actuator 
and motor control. General use of half H-bridge 
drivers as configurable low/high side drivers are 
not recommended. Glitches on Vbatt or from other 
drivers connected to Vbatt_prot supply can trigger 
the IC-driver OVP function. If this happens while L1 
is energized all loads to Vbatt_prot are switched off 
and the L1 avalanche energy can charge C1 to 
destructive 
voltage 
levels 
through 
the 
HS_Transistor free wheel diode. Countermeasures 
to this risk are a voltage clamp D1 and/or choosing 
C1 with a large capacitance value. 
 
U1a
U1b
U2a
U2b
L1
C1
D1
U1a
U1b
L1
C1
D1
Vs
Vs
Vs_prot
Vs_prot
Power_gnd
Power_gnd
Half/Full H-Bridge IC-driver with over voltage protection (OVP) for coil load
OVP
OVP
OVP
Full Bridge
Half Bridge
signal_gnd
 
Figure 20: H-Bridge Driver Outputs 
 
*******.5 Drivers Diagnosis. Output drivers which 
include diagnosis shall generate a Diagnostic 
Trouble Code according to Table 18: 


### 第 23 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 23 of 74
 
Table 18: Recommended Output Driver Diagnosis 
Loads Driven 
Driver Type 
Ground 
S.C. 
Supply 
S.C. 
Open 
Load 
Strategy 
 
BRIDGE 
ACTUATOR 
RELAY 
 With current sensing (e.g.: 
window lifter) 
Relay diverter 
YES when activated NO 
YES when 
activated 
 -load current sensing  
-check overload 
 -check relay switching after activation 
(contacts molding, coil breaking) 
BRIDGE 
ACTUATOR 
RELAY  
Without current sensing (e.g.: 
door locking, mirror tipping) 
Relay diverter 
YES when activated NO 
NO 
 - check relay switching after activation 
(contacts molding, coil breaking) 
 
RELAY 
 Without 
current 
sensing 
(e.g.: Passive Entry) 
High 
Side 
Relay 
switch 
YES when activated NO 
YES, 
but 
not 
when 
activated 
 check relay switching after activation 
(contacts molding ) 
RELAY 
 Without current sensing  
Low side Relay switch NO  
YES  
when 
activated
YES,  
but 
not 
when 
activated 
 check relay switching after activation 
(contacts molding, coil breaking)  
HALF 
BRIDGE 
RELAY 
Without current sensing (e.g.: 
backlight cleaner) 
Relay diverter 
YES when activated NO 
NO 
check relay switching after activation 
(contacts molding, coil breaking) 
SSD 
 ( e.g. : door light or mirror 
light) 
High side driver 
YES  
YES  
YES  
complete check of the load 
LED Driver 
High side driver 
- 
- 
YES  
 
SSD 
( E.G. : rear overhead light , 
trunk, bonnet) 
Low side driver 
YES  
YES  
YES  
complete check of the load 
LED Driver 
Low side driver 
- 
- 
YES  
 
HALF BRIDGE SSD 
(e.g. door lock) 
Half-bridge 
YES  
YES  
YES  
complete check of the load 
STATIC 
BRIDGE 
ACTUATOR     (Eg: external 
mirror adjustment up/down, 
left/right) 
Bridge 
YES  
YES  
YES  
complete check of the load 
 
3.1.5 Usage Definition. GMW3172 describes 
environmental, durability and capability tests for 
E/E equipment for passenger vehicles and light 
duty trucks. These procedures are optimized for 
testing complete E/E devices. They are not 
optimized for validating that the E/E device internal 
components, materials and material combinations 
are suitable for a specific application. The device 
supplier is responsible for definition and validation 
of internal component and material requirements 
that 
incorporate 
the 
application 
specific 
performance, usage, and environmental durability 
needs. The GMW3172 test flow is focused on 
environmental loads put on the E/E device during 
operation of the vehicle during its life. The E/E 
device supplier is responsible for choosing 
components, materials and material combinations 
that do not have failure rate growth correlated to 
the outdoor storage time (calendar time) of the 
unused car in different climate markets. 
3.2 Product Characteristics. Not applicable. 
 
3.2.1 
Performance 
Requirements. 
Not 
applicable. 
3.2.2 Physical Characteristics. Not applicable. 
3.2.2.1 Dimensions and Capacity. Not applicable. 
3.2.2.2 Mass Properties. Not applicable. 
3.2.3 Dependability. Not applicable. 
3.2.3.1 Target Life. Not applicable. 
3.2.3.2 Class 1 and Class 2 Type Problems. Not 
applicable. 
3.2.3.2.1 Class 1 Type. Not applicable. 
3.2.3.2.2 Class 2 Type. Not applicable. 
3.2.3.3 Reliability Requirements. 
3.2.3.3.1 Electrical System Reliability. 
3.2.******* 
Reliability 
in 
the 
Development 
Phases. The design and development phases 
shall include reliability activities such as Design 
reviews, FMEA, FTA, Reliability Analysis, and 
Design of Experiments. A Reliability Program Plan 
shall be presented within 60 days of contract 
signing and must be approved by the GM 
responsible engineer. 


### 第 24 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 24 of 74 
October 2005
 
3.2.3.3.1.2 Reliability in Verification Phases. The 
reliability requirements can be divided into two 
groups, long term and short term requirements.  
The short term requirements concern the warranty 
outcome during the first 12 and 36 months of 
usage. 
The long term requirements consider the reliability 
of 10 years or 160 900 km, whichever comes first. 
The short term requirements are usually expressed 
in terms of RPTV, repairs per thousand vehicles. 
Long term reliability could be expressed in terms of 
a survival probability (=reliability). 
3.2.3.3.1.2.1 Short Term Requirements. The 
short term requirement is to be defined in each 
SSTS/CTS. It shall initially be demonstrated by a 
comparison 
method 
that 
the 
short 
term 
requirement is fulfilled. Later on the comparison 
method shall be complemented by testing. 
3.2.3.3.1.2.2 Long Term Requirements. The 
SSTS and/or CTS shall define the requirement for 
long term reliability. If the SSTS and/or CTS do not 
define any long term reliability requirements, the 
requirements in GMW3172 shall apply. The 
validation procedure for long term reliability shall 
be defined in the subsystem specification, the 
component specification or GMW3172. 
3.2.3.4 Electrical System Safety. Qualitative 
system safety analyses such as FMEA, FTA, and 
ETA shall be carried out for systems where 
potential hazardous events and conditions may 
occur. The choice of analysis method shall be 
discussed with, and approved by GM. 
3.2.4 Serviceability. Not applicable. 
3.2.5 User System/Subsystem/Component/Part 
Interface. Not applicable. 
3.3 Design and Construction. 
3.3.1 Materials, Processes and Parts Selection 
Guidelines. 
******* Material Guidelines. 
*******.1 Substrate Selection. 
Acceptable substrates are: 
• 
FR4 substrates with glass temperature Tg 
chosen from the operating temperature range 
requirements. 
• 
CEM3 substrates on low population density 
products. 
• 
Polyamide flexible substrates. 
• 
Ceramic hybrid substrates. 
• 
Combinations of the above mentioned (for 
example Rigid-flex). 
• 
New substrate types with a proven durability 
for Automotive use. 
*******.2 Materials to be Used. All materials used 
for design and production of electronic assemblies 
shall be qualified to the requirements given by the 
chosen J-STD-001D classification. This includes 
material property requirements/qualification of the 
following: 
a. Substrate material 
b. Solder resist materials 
c. Conformal coat (if used) 
d. Solder Alloys 
e. Fluxes 
f. 
Solderability (components, fluxes, pastes) 
Material selection is the responsibility of the 
supplier and material combinations that can lead to 
failure modes that grow over time during the car 
storage time shall be eliminated. 
*******.3 Materials to be Avoided. In addition to 
general requirements in GMW3059: 
*******.3.1 Lead Free Requirement. Electronic 
designs and mechanizations shall be lead free.  
This shall include, but is not limited to: 
a. solder used to connect the components to the 
circuit board. 
b. coating on connector terminals and "presolder" 
of wires and components prior to soldering, 
crimping etc. 
c. protective coatings on the circuit board. 
d. shrink hoses for components on the circuit 
board. 
Please see GMW3172 for additional information on 
lead free design. 
*******.3.2 Rubber Materials. No sulfur cured 
rubber materials of any kind shall be used inside of 
enclosures 
of 
electrical/electronic 
modules/components, 
due 
to 
components 
sensitive to gaseous exposure of sulfur vapors.  
This sensitivity to gaseous corrosion includes chip 
resistors, silver/epoxy screen printed PCB via 
holes and LCD heat seal carpets. 
Sealing material in wet electrolytic capacitors shall 
have a proven sulfur free design (peroxide cured). 
3.3.2 Process Guidelines.  
******* 
Assembly 
and 
Solder 
Processes. 
Acceptable processes are: 
• 
Single or Double sided assembly of SMD 
components with Reflow soldering. 
• 
Leaded components on top side with pin in 
paste (preferred) or selective soldering. 


### 第 25 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 25 of 74
 
• 
Glued SMD components and a Wave solder 
process shall be avoided. 
Masked solder pallets for selective wave soldering 
of leaded components shall be avoided due to non 
heated flux residues on masked areas after 
soldering. Residues enter the masked areas from 
direct mask leakage or by contamination from the 
mask itself. 
******* Multiple Board De-paneling and Break-
out Tabs. If multiple boards are handled in the 
production process as panels, or single boards 
have break-out tabs or parts the singulation must 
be done by a machine. 
Preferred method is a router milling the last parts 
of a pre routed panel (mouse bites). 
If a panel design has components outside the 
single board contour a scored board (V-scoring) 
are not allowed along that side. 
Bending or snapping out any parts from a 
board/panel by hand or by machine shall be strictly 
forbidden. 
*******.1 Static Mechanical Forces on Solder 
Joints. All external connectors and internal heavier 
components (Al-capacitors, cooling fins..) shall not 
rely on the solder joints for their mechanical 
strength. This type of component shall have other 
mechanical fixations present (rivets, snaps, RTV...) 
as the mechanical support. 
******* Parts Guidelines. Components shall be 
used within its published ratings. 
No component (such as discrete components, 
integrated circuits, power supplies, micro-controller 
etc.) shall be used outside of its specification 
(regarding clock frequency, temperature range, 
voltage range, power rating etc). 
*******.1 Package Selection. Surface Mounted 
Devices should be used where possible to reduce 
weight and size. 
*******.2 
Reset 
Circuits 
and 
Watchdog 
Functions. Microcontrollers with Vbatt connected 
power supplies shall have a separate (from the µC) 
reset circuit. Field experience shows this need 
since power glitches, crank voltage transients and 
battery rundown situations have led to code 
runaway and the only possible way to restart 
ECU’s 
are 
battery 
pole 
reconnects. 
This 
requirement is also one part of the requirement to 
reduce the number of warranty returns with “no 
fault found” analysis results. 
*******.3 Memory Concept. The ECU memory 
shall be permanently affixed (non-plugable) to the 
circuit board. 
The ECU shall contain the following memory types 
which can be categorized by retention capability 
and write capability. The quantities of each 
memory type shall be specified in the applicable 
CTS/SSTS. 
*******.3.1 Type 1 Memory (Typically Dynamic 
RAM). 
a. The ECU shall be able to write to Type 1 
memory until the microprocessor resets due to 
a low voltage condition. 
b. The ECU shall be able to read from Type 1 
memory until the microprocessor resets due to 
a low voltage condition. 
c. The ECU shall retain the contents of Type 1 
memory until the microprocessor resets due to 
a low voltage condition. 
*******.3.2 Type 2 Memory (Typically Static 
RAM). 
a. The ECU shall be able to write to Type 2 
memory until the microprocessor resets due to 
a low voltage condition. 
b. The ECU shall be able to read from Type 2 
memory until the microprocessor resets due to 
a low voltage condition. 
c. The ECU shall retain the contents of Type 2 
memory through a low voltage crank condition 
(4.5 
V 
in 
operating 
voltage 
range 
requirements). 
*******.3.3 Type 3 Memory (Typically EEPROM). 
a. The ECU shall be able to write to Type 3 
memory until the microprocessor resets due to 
a low voltage condition. 
b. The ECU shall be able to read from Type 3 
memory until the microprocessor resets due to 
a low voltage condition. 
c. The ECU shall retain the contents of Type 3 
memory in all operating voltage ranges for the 
life of the vehicle. 
d. The ECU shall prevent writes to the Type 3 
memory locations identified in the Functional 
(Software) Spec. 
e. The number of times that the ECU Type 3 
memory can be written shall meet the usage 
requirements for the life of the vehicle. 
Note: For ECU’s that have parameters that need 
to be stored once (or more) every Ignition_15 
cycle, special considerations need to be taken by 
the supplier when selecting the appropriate Non-
Volatile Re-Writable memory concept in which to 
store these parameters. The supplier shall, in this 
case, assume a minimum of 50000 Ignition_15 
cycles. 


### 第 26 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 26 of 74 
October 2005
 
*******.3.4 Type 4 Memory (Typically Flash). 
a. The ECU shall be able to write to Type 4 
memory in operating voltage range consistent 
with Electrical Loads Code Letter C. 
b. The ECU shall be able to read from Type 4 
memory until the microprocessor resets due to 
a low voltage condition. 
c. The ECU shall retain the contents of Type 4 
memory in all operating voltage ranges for the 
life of the vehicle, typically 15 years. 
d. The ECU shall prevent writes to the Type 4 
memory locations identified in the Functional 
(Software) Spec. 
e. The number of times that the ECU Type 4 
memory can be written shall be at least 100. 
f. 
The ECU supplier shall provide a guaranteed 
maximum Type 4 memory erase time under 
worst 
case 
conditions 
based 
on 
the 
programming algorithm used. 
g. The ECU supplier shall provide a guaranteed 
maximum Type 4 memory write time under 
worst 
case 
conditions 
based 
on 
the 
programming algorithm used. 
h. Type 4 memory shall be used to store 
operational software and calibrations. 
i. 
The 
ECU 
shall 
be 
capable 
of 
writing 
operational software and calibrations in Type 4 
memory in the following combinations: 
i. 
Calibrations only 
ii. 
Operational software and calibrations 
Note: 
The 
component 
average 
lifecycle 
temperature applies when data retention and 
minimum number of programming cycles are 
estimated. The supplier shall get an approval from 
the chosen microcontroller manufacturer as to 
whether 
these 
requirements 
can 
be 
met 
considering the ECUs average temperature. 
*******.3.5 Type 5 Memory (Typically the Boot 
Block of the Flash). 
a. The ECU shall be able to write to Type 5 
memory in operating voltage range consistent 
with Electrical Loads Code Letter C. At some 
point in time during development, the ability to 
write to the ECU Type 5 memory shall be 
restricted to the supplier. 
b. The ECU shall be able to read from Type 5 
memory in operating voltage range VR2. At 
some point in time during development, the 
ability to read from the ECU Type 5 memory 
shall be restricted to the supplier. 
c. The ECU shall retain the contents of Type 5 
memory in all operating voltage ranges for the 
life of the vehicle, typically 15 years. 
d. The number of times that the ECU Type 5 
memory can be written shall be at least 100. 
e. Type 5 memory shall be used to store boot 
software. 
*******.4 
Leadless 
Chip 
Components. 
No 
leadless components larger than 1210 size shall 
be used on organic substrates (FR4, polyimide, 
CEM3). If larger components are needed SMD 
leaded parts shall be chosen. These requirements 
are based on thermal expansion mismatch failures. 
*******.5 Ceramic Chip Capacitor Materials. The 
dielectric of ceramic chip capacitors shall be COG 
(NPO) or X7R. 
Y5V materials shall not be used. 
*******.6 Radial Electrolytic. Al-capacitors. When 
this type of component is used always choose a 
version with a vented rubber standoff sealing. The 
capacitor outer sleeve is not guaranteed to be an 
insulator and the rubber standoff also protects 
against encapsulating of leakage products from the 
capacitor seal vent. 
3.3.3 
Design 
Guidelines 
and 
Constraints. 
Guidelines stated are general. Deviation from them 
shall be discussed and evaluated with the 
appropriate GM DRE before the final design is 
selected. 
******* Component and Subsystem Level. 
*******.1 I/O Filtering. 
a. Voltage and current slew rate must be 
minimized on all I/O signals, particularly 
repetitive signals. Guidelines: dv/dt < 1V/us, 
di/dt < 100mA/µs. 
b. RF rectifying shall be avoided. Attention must 
be paid to diodes on inputs. 
c. Low pass filtering must be implemented on 
each port. A trade-off between bandwidth and 
ESD protection/RF filtering may be needed. 
This is particularly important on ports for serial 
communication. 
d. Bulk storage capacitors, with low ESR values 
at -40°C, must be provided at power inputs. 
e. Each external I/O pin must have a bypass 
capacitor connected directly to the ground 
plane. Recommended values are 1nF to 10nF 
100V DCWV X7R capacitors. The placement 
of the capacitor must prevent cross talk 
between the filtered and not filtered side. 


### 第 27 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 27 of 74
 
*******.2 Internal Circuit Design Guidelines. 
a. A series impedance, or provisions for adding 
one, shall be included on all repetitive lines, 
e.g. clock lines, address lines, data lines. The 
impedance must be placed near the source 
and matched to the characteristic impedance 
of the line. 
b. Bipolar and FET transistors shall be equipped 
with a base/gate to emitter/source resistor and 
optionally a filter capacitor in parallel to this 
resistor. This requirement applies both to 
NPN/PNP and N-channel/P-channel types. 
Microcontroller I/O pins often start as floating 
inputs during and after Reset before initializing 
and this base/emitter or gate/source resistor 
will prevent unknown states on the output 
signal. 
Vs_prot
in
out
in
out
Vs_prot
*
*
* = C optional
 
Figure 21: Base/Gate Resistors 
 
a. Rise and fall times of FETs must be controlled. 
b. Bulk capacitors must be provided. The value 
must be at least 10x the total individual 
decoupling value of the whole PCB. 
c. Decoupling capacitors must be provided on all 
ICs. The values must be calculated from the 
needed charge at transitions and the allowed 
voltage drop. Recommended values are in the 
10-100 nF range. For maximum resonance 
frequency the value must not be too high and 
the physical component size must be as small 
as possible (maximum size 0805). Micro 
controllers and power drivers may need 
multiple and distributed decoupling capacitors. 
*******.3 Connector Selection/Application. The 
connector must have a pin-out configuration 
corresponding to the logic areas of the PCB. 
*******.4 EMC Case Design 
a. A conductive case is strongly recommended if 
parallel bus connected memories, external to 
the µC, are used. 
b. The longest cross section length of a slot in a 
conductive case must not exceed λ/20, 
(λ=300/f, f [MHz]), where f is the highest 
frequency of interest. 
c. If a conductive case is used the grounding 
philosophy must be carefully considered. As a 
general rule, to avoid a DC current path 
through the PCB, the ground of the PCB must 
be connected to the conductive case and the 
vehicle body via a capacitor. If a direct 
connection is preferred - due to use of the 
connection for cooling, EMC or other reason - 
it shall be discussed and evaluated with the 
EMC responsible engineer at GM. 
******* PCB HW Design Requirements and 
Guidelines. 
*******.1 Substrate Layer Structure. 
a. All ECU quotations and designs shall be based 
on a multi layer design concept unless 
fulfillment of EMC requirements according to 
GMW3097 and GMW3091 is proven with fewer 
layers. 
b. All leaded component pads/holes and vias 
must be through plated. 
c. No leaded components or assemblies shall be 
mounted in non through plated holes. 
d. A solid ground plane is preferred. 
e. Internal power plane/planes shall be used as 
much as possible, in combination with other 
supply lines. 
*******.2 EMC PCB Design. 
a. Zoning. 
High 
frequency 
areas 
shall 
be 
separated from low frequency areas. Analog 
circuits 
must 
be 
grouped 
together 
and 
separated from digital circuits. 
b. Clock lines shall be short and located between 
ground and/or supply planes. 
c. Lines for external, serial, communications shall 
be short, i.e. the transceivers must be placed 
close to the connector. 
d. Digital and analogue traces shall not be routed 
in parallel. 
3.******* SMD Land Pattern Design. The land 
pattern design of surface mount components shall 
follow the guidelines given by IPC-7351. 
a. This standard provides guidelines for size, 
shape and tolerance on SMD land patterns to 
give correct solder fillets and allow for 
inspection and testing of those solder joints. 
b. Components with metallic tabs in contact with 
PCB surfaces shall not overlap foreign 


### 第 28 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 28 of 74 
October 2005
 
electrical nets and ground planes (risk of burrs 
short circuiting through solder resist layers).  
c. Metallic mechanical attachments to the PCB 
shall not be in contact with foreign electrical or 
solder resist insulated nets other than ground. 
d. Solder resist material shall never be treated as 
an insulation layer. 
e. Ground and power plane connected leads and 
SMD pads shall always have a thermal relief 
(wagon wheel). 
*******.4 Leaded and Via Pad Design. Used 
pad/drill design of leaded components and vias 
shall guaranty that annular ring breakout of drilled 
and plated hole walls does not occur. The 
minimum annular ring requirement shall be 
according to IPC class 3 (IPC-6012) that states 
minimum 25 µm. Breakout of hole walls are not 
accepted as this can easily be in conflict with inner 
and outer layer isolation clearances to conductors 
and long term reliability of inner and outer layer 
conductor to land interfaces. 
*******.5 
Special 
Routing 
Requirement 
Concerning Electrolytic Al-Capacitors. For PCB 
mounted radial Al-capacitors with rubber sealing 
against the board surface never locate any copper 
traces or ground plane under the seal side. Always 
route the tracks on the PCB backside or inner 
layers. 
3.3.4 Workmanship. General requirements of 
workmanship on assembled printed circuit boards 
(PCB) shall be according to J-STD-001D. 
This standard includes requirements on the total 
production process, practices, used materials, 
workmanship and acceptance criteria. 
This standard will be used as the acceptance 
criteria of electronic products in all development 
phases/deliveries during a car project, from 
prototypes to serial deliveries.  
GM and Supplier shall agree on the terms of 
classification according to §1.3 of the J-STD-001D. 
The default classification shall be Class 2. 
Safety related modules shall have requirement 
Class 3. 
3.3.5 Interchangeability. Not applicable. 
3.4 Documentation. The supplier shall provide the 
following documents: 
a. Circuit Schematics 
b. Component part list (BOM) 
c. Performance/Functional specification 
d. Component Fault Detection specification 
e. Interface specification 
f. 
Worst Case Analysis, Sneak Circuits, electrical 
and thermal analysis 
g. PCB layout 
h. Component placement drawing 
i. 
Engineering 
Validation 
Plan 
(including 
Reliability, and System Safety activities) 
j. 
Design Validation Plan 
k. Product Validation Plan 
3.5 
Support 
of 
System/Subsystem/Component/Part After Sale. 
Not applicable. 
3.6 
System/Subsystem/Component/Part 
Operator Training. Not applicable. 
3.7 
System/Subsystem/Component/Part 
Characteristics. Not applicable. 
4 Validation 
4.1 General. The supplier is responsible for 
validation of all component requirements as stated 
in the applicable SSTS/CTS. 
4.1.1 Supplier Validation Plan. The supplier shall 
create a Component Validation Plan/Subsystem 
Validation Plan (CVP/SSVP) containing a detailed 
description 
of 
relevant 
tests, 
references 
to 
applicable test procedures, detailed acceptance 
criteria, and a time schedule compliant with the 
master timing chart. This plan shall be submitted at 
least 60 days prior to the start of testing and shall 
be reviewed and approved by GM engineering 
prior to implementation. 
The CVP/SSVP shall contain the following items 
related to Hardware and EMC performance and 
durability characteristics: 
• 
Environmental and Durability Test Procedures 
and number of test samples according to 
Design Validation Chart for appropriate vehicle 
mounting location specified in GMW3172. 
• 
EMC Test Procedures and number of test 
samples according to GMW 3103. 
The Validation Plans shall also comprehend 
verification of the reliability requirements and the 
supplier shall assure that personnel with adequate 
statistical competence are assigned to these 
verification activities. 
4.1.2 Testing Required by Component Change. 
Any change in the design and manufacture of a 
component (e.g. part value and/or type, function, 
manufacturing 
process, 
etc.) 
shall 
be 
communicated to GM and must be approved by 
GM DRE and may then require re-testing of the 
component and subsystem. 


### 第 29 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 29 of 74
 
4.1.3 EMC Validation Process and Test Plan. 
Supplier shall conform to process requirements 
stated GMW 3103. 
4.2 Validation Cross Reference Index. Defined 
for each CTS/SSTS Project. 
4.3 Supporting Paragraphs. Not applicable. 
5 Provisions for Shipping 
6 Notes 
6.1 Glossary. Not applicable. 
6.2 Acronyms, Abbreviations, and Symbols. 
CAN 
Controller Area Network 
CPU 
Central Processing Unit 
CTS 
Component Technical Specification 
CVP 
Component Validation Plan 
DRE 
Design Release Engineer 
DTC 
Diagnostic Trouble Code 
ECU 
Electronic Control Unit 
E/E 
Electrical/Electronic 
EEPROM 
Electrical Erasable Programmable 
Read Only Memory 
EMC 
Electro Magnetic Compatibility  
ESR 
Equivalent Series Resistance 
FET 
Field Effect Transistor 
FMEA 
Failure Mode Effect Analysis 
FSC_ 
Functional Status Classification 
LDO 
Low Drop Out (Voltage Regulator) 
NTC 
Negative Temperature Coefficient 
OVP 
Over Voltage Protection 
PCB 
Printed Circuit Board 
PTC 
Positive Temperature Coefficient 
PTH 
Plated Through Hole 
PWM 
Pulse Width Modulated 
RAM 
Random Access Memory 
ROM 
Read Only Memory 
SC 
Short Circuit to Gnd or Vbatt 
SMD 
Surface Mounted Device 
SOAR 
Safe Operating Area  
SSTS 
Sub System Technical Specification 
 
7 Additional Paragraphs 
7.1 All materials supplied to this specification must 
comply with the requirements of GMW3001, Rules 
and Regulations for Materials Specifications. 
7.2 All materials supplied to this specification must 
comply with the requirements of GMW3059, 
Restricted and Reportable Substances for 
Parts. 
8 Coding System 
This specification shall be referenced in other 
documents, drawings, VTS, CTS, etc. as follows: 
 
GMW14082 
9 Release and Revisions 
9.1 Release. This general specification originated 
in December 2004. It was first approved in October 
2005 by the Global Body Interior/Exterior team. It 
was first published October 2005. 


### 第 30 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 30 of 74 
October 2005
 
Appendix A Electrical Interfaces 
Detailed Interface Specifications 
 
_x suffixes stands for a number to sort different parameters for the same type of interface. 
 
Parameters given by tables shall be used by CTS/SSTS documents for all generic type of interfaces. 
 
Initial tolerances for used components in schematics are: 
R: ± 5% 
C: ± 10% 
If not else specified. 
 
Where ± 1 % resistor values are shown, they shall have a total tolerance of max ± 2.5% over temperature 
after a lifetime aging including temperature cycling, high temp storage and high humidity. This requires in 
practice a thin film design type if ceramic chip resistors are chosen.  
 
A1 Power Circuits 
A1.1 Vbatt_x 
Power_gnd
Vbatt
Vbatt_prot
* = Complete transient protection will depend on GMW3097 results
*
*
Alternative reverse protection schemes
Signal_gnd
 
Name(s) 
Vbatt_x 
Type 
Power, Permanent supply from vehicle battery  
Voltage range, Max rating ( 1s < t < 1 minute) 
-13.5 to +26.5 V 
Voltage range, Normal 
0 to Vmax 
Voltage range, Normal operating 
Vmin to Vmax 
Maximum Load Current 
E/E device dependent, >3A 
Maximum Inrush Current 
Max load x 10, duration < 1s 
Reverse polarity protection mechanization 
Diode/Shottky/MOSFET, Vmin dependent 
 


### 第 31 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 31 of 74
 
A1.2 Vrun/crank_x and Vacc_x 
Power_gnd
Vacc
Vrun/crank
Vrun/crank_prot
Vacc_prot
* = Complete transient protection will depend on GMW3097 results
Signal_gnd
*
*
 
Name(s) 
Vrun/crank_x and/or Vacc_x 
Type 
Power or Signal. Ignition key state controlled  
Voltage range, Max rating ( 1s < t < 1 minute) 
-13.5 to +26.5 V 
Voltage range, Normal 
0 to Vmax 
Voltage range, Normal operating 
Vmin to Vmax 
Maximum Load Current 
E/E device dependent,  
Maximum Inrush Current 
Max load x 10, duration < 1s 
Reverse polarity protection mechanization 
Diode/Shottky 
Power switch off, voltage discharge time constant 
Approx 1s (resistive path shall exist to Power_gnd) 
 


### 第 32 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 32 of 74 
October 2005
 
A1.3 Vref_5V_x 
D*
I_out
C_out
Vcc
Vref_5V
Vs
Power_gnd
+
-
uC
signal_gnd
5V LDO
Voltage
Regulator
Short to Gnd & Vbatt 
Protection
Vout
D* = Reverse polarity protection mechanization Vmin dependent
 
Name(s) 
Vref_5V_x 
Type 
Regulated 5V output, switched 
Vs range, Max rating (1s<t<1 minute) 
-13.5V to +26.5 V 
Vs range, Normal 
0 to Vmax 
Vs range, Normal operating 
Vmin to Vmax 
Vout, Voltage output with Vs = Vmin to Vmax 
5.0V +-2% 
I_out, Maximum Load Current 
100mA  
C_out, Capacitive load without inrush shutdown 
10µF 
Reverse polarity protection mechanization 
Si Diode, Shottky or MOS depending on Vmin 
Power switch off, voltage discharge time constant 
Approx 1s (resistive path shall exist to Power_gnd) 
 


### 第 33 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 33 of 74
 
A1.4 Vref_12V_x 
D*
I_out
C_out
Vcc
Vs
Power_gnd
Vref_12V
+
-
uC
signal_gnd
5V LDO
Voltage
Regulator
Short to Gnd & Vbatt 
Protection
Vz
Vout
D* = Reverse polarity protection mechanization Vmin dependent
 
Name(s) 
Vref_12V_x 
Type 
Unregulated 12V output, switched 
Vs range, Max rating (1s<t<1 minute) 
-13.5 to +26.5 V 
Vs range, Normal 
0 to Vmax 
Vs range, Normal operating 
Vmin to Vmax 
Vout, Voltage output 
12.0V approx, Vs-1V, Limited to max +40V transients 
I_out, Maximum Load Current 
100mA  
C_out, Capacitive load without inrush shutdown 
10µF 
Reverse polarity protection mechanization 
Si Diode, Shottky or MOS depending on Vmin 
Power switch off, voltage discharge time constant 
Approx 1s (resistive path shall exist to Power_gnd) 
 


### 第 34 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 34 of 74 
October 2005
 
A2 Ground Circuits 
Power_gnd_01
Vbatt_01
Power_gnd_02*
Signal_rtn_01
Signal_rtn_02
Signal_02
Signal_01
Vref_12V_01
Vbatt_02*
Signal_gnd
I/O
Sensor/Slave
Sensor/Slave
out1
out2
* = Multiple Vbatt_ and Power_gnd_  connections used where
maximum single connector pin currents are exceeded
 
A2.1 Power_gnd_x 
Name(s) 
Power_gnd_x 
Type 
Power, Ground return to Body_gnd 
Maximum Return Current. Sum of internal and 
external load return currents 
E/E device dependent, >3A 
Maximum Return Inrush Current 
Max load x 10, duration < 1s 
Reverse polarity protection mechanization 
N/A 
Maximum ground voltage gradients within the same 
E/E device. Ground lines or planes to Power_gnd, 
Signal_gnd and Signal_rtn 
10mV 
 


### 第 35 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 35 of 74
 
A2.2 Signal_Rtn_x 
Name(s) 
Signal_rtn_x 
Type 
Signal return, sensor or slave unit 
Summation point of external sensor return signals. 
 
Maximum Return Current. Sum of internal and 
external load return currents 
E/E device dependent, >3A 
Maximum Return Inrush Current 
Max load x 10, duration < 1s 
Reverse polarity protection mechanization 
N/A 
Maximum ground voltage gradients within the same 
E/E device. Ground lines or planes to Power_gnd, 
Signal_gnd and Signal_rtn 
10mV 
 


### 第 36 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 36 of 74 
October 2005
 
A2.3 Power_Gnd_Stall_x 
Power_gnd_01*
Vbatt_01
Signal_rtn_01
Signal_01
Vref_12V_01
Vbatt_02
M_supply
Power_gnd_stall*
M_rtn
Signal_gnd
I/O
Sensor/Slave
out1
* = Multiple Power_gnd_  connections used with seperate ground wire returns back to 
common body ground bolt. Ground diode D* to prevent floating conditions if
Power_gnd_01 is lost. (UV = Under Voltage, SC = Short Circuit)
uC
5V LDO
Regulator
Body_gnd
D*
Stalling
Motor Load
R
20*R
20*R
SC-protection
SC-protection
UV-protection
 
 
Name(s) 
Power_gnd_stall_x 
Type 
Power, Ground return to body_gnd for stalling loads 
Maximum Return Current. Sum of external load return 
currents 
E/E device dependent, >30A at motor stall before 
controlled load switch off. 
Maximum Return Inrush Current 
Max load x 10, duration < 1s 
Reverse polarity protection mechanization 
N/A 
Maximum ground voltage gradients within the same 
E/E device. Ground lines or planes to Power_gnd, 
Signal_gnd and Signal_rtn 
Used for stalling high current loads where ground 
offset requirement of ± 1V can not be met. 
 


### 第 37 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 37 of 74
 
A3 Discrete Digital and Switch Interfaces 
R1
R2
C1
Rp
+
-
D*
I_inj
Ii
Io
Rp_source
Vcc
Vs
Vth
Power_gnd
Vign_prot
Vs_sw
+
-
+
-
10nF@Input_pin
Vth/L/H: common values: 30/70% or 35/65% of Vcc for CMOS
5V* LDO
Voltage
Regulator
uC
Signal_gnd
5V*: Lower Vcc supply levels than 5V can be used
Rpu
Rpd
I_inj: Injection current into IC-input when Vin > Vcc
Signal source
or switch
Gnd
D* = Reverse polarity protection mechanization Vmin dependent
Ui
Uo
transmitter
receiver
 
Electrical Characteristics 
Parameter 
Symbol 
Condition 
min 
typ 
max 
Unit 
Remarks 
Supply voltage 
Vs 
Maximum rating 
-13.5 
 
+26.5 
V 
No damage 
Supply voltage 
Vs 
Operating range with valid 
logic states 
Vmin 
14 
Vmax 
V 
 
Input voltage range 
Ui 
Maximum rating 
-1.0 
 
+26.5 
V 
 
Input voltage 
Ui 
Operating range 
-1.0 
 
Vmax+1 
V 
 
Input logic low state 
Uil 
Voltage range 
-1.0 
 
+2.0 
V 
Logic 0 
Input logic high state 
Uih 
Voltage range 
+5.5 
 
Vmax+1 
V 
Logic 1 
Output voltage low 
Uol 
Voltage range @ Io = 100mA 
0 
0.3 
1.0 
V 
Logic 0 
Output voltage high 
Uoh 
Voltage range @ Io = -100mA 
Vs-1 
 
Vs 
V 
Logic 1 
Output voltage dVdt 
tr, tf 
 0.1 Uoh to 0.9 Uoh, Type A 
0.1 
 
1.0 
V/µs 
 
 
R1 
 
 
33 
 
kΩ 
 
 
R2 
 
 
100 
 
kΩ 
Vth =30/70% 
 
R2 
 
 
220 
 
kΩ 
Vth = 35/65% 
 
Rp 
 
680 
1.2k 
3k 
Ω 
 
 
C1 
 
 
33 
 
nF 
R1*C1=1ms 
Vmin, Vnom and Vmax defined by electrical code letter from the GMW3172 code string. 


### 第 38 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 38 of 74 
October 2005
 
A3.1 Discrete Digital Input, Active Low 
R1
R2
C1
Rpu
+
-
D*
I_inj
R_no_float
Ii
Io
Rpu_source
Vcc
Vs
Vth
Power_gnd
Vign_prot
Vs_sw
+
-
+
-
10nF@Input_pin
Vth/L/H: common values: 30/70% or 35/65% of Vcc for CMOS
5V* LDO
Voltage
Regulator
uC
signal_gnd
5V*: Lower Vcc supply levels than 5V can be used
I_inj: Injection current into IC-input when Vin > Vcc
D* = Reverse polarity protection mechanization Vmin dependent
Alternative Voltage
pullup sources
LS-driver or
mechanical switch
......
......
Gnd
One common R_no_float load for each separate Vs_sw/Vign_prot net
Ui
Uo
transmitter
receiver
 
IDL_x 
Name(s) 
IDL_x 
Type 
Digital Input, Active Low 
Rpu pullup voltage source 
Vs_sw or Vign 
Wakeup Type 
N/A 
Iil_min , min current required in the active state 
-4 mA* 
Iil_max, max current allowed in the active state 
-20 mA* 
Ground offset voltage 
0 to ±1.0 V 
Low pass filter – Time constant 
1ms 
Tmin - Minimum input recognition time  
30 ms 
Tmax - Maximum input recognition time 
40 ms 
Nmin - Minimum number of same state samples 
3 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
*Min and max active state current given for Vs in the operating voltage range from Vnom to Vmax 


### 第 39 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 39 of 74
 
ODL_x 
Name(s) 
ODL_x 
Type 
Digital output, Active Low 
Output power source 
Gnd  
Uol_max , max saturation voltage in active state  
1.0V @ 100mA 
Iol_min ,min sink current required in active state 
100 mA @ Vmin to Vmax 
Io_leak ,max leakage current in inactive state 
10 µA max @ Vnom 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
 


### 第 40 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 40 of 74 
October 2005
 
A3.2 Discrete Digital Interface, Active High 
R1
R2
C1
Rpd
+
-
D*
I_inj
Ii
Io
Vcc
Vs
Vth
Power_gnd
Vs_prot
+
-
+
-
10nF@Input_pin
Vth/L/H: common values: 30/70% or 35/65% of Vcc for CMOS
5V* LDO
Voltage
Regulator
uC
signal_gnd
5V*: Lower Vcc supply levels than 5V can be used
I_inj: Injection current into IC-input when Vin > Vcc
HS-driver or momentary
mechanical switch
Gnd
D* = Reverse polarity protection mechanization Vmin dependent
......
......
Vs_prot > 5.5V
Ui
Uo
transmitter
receiver
 
IDH_x 
Name(s) 
IDH_x 
Type 
Digital Input, Active High 
Rpd pulldown voltage source 
signal_gnd 
Wakeup Type 
N/A 
Iih_min , min current required in the active state 
4 mA* 
Iih_max, max current allowed in the active state 
20 mA* 
Ground offset voltage 
0 to ±1.0 V 
Low pass filter – Time constant 
1ms 
Tmin - Minimum input recognition time  
30 ms 
Tmax - Maximum input recognition time 
40 ms 
Nmin - Minimum number of same state samples 
3 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
*Min and max active state current given for Vs in the operating voltage range from Vnom to Vmax 


### 第 41 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 41 of 74
 
IDH_WUR_x 
Name(s) 
IDH_WUR_x 
Type 
Digital Input, Active High with wake up 
Rpd pulldown voltage source 
signal_gnd 
Wakeup Type 
Wake up on input voltage rising edge 
Iih_min , min current required in the active state 
4 mA* 
Iih_max, max current allowed in the active state 
20 mA* 
Low pass filter – Time constant 
1ms 
Tmin - Minimum input recognition time  
30 ms 
Tmax - Maximum input recognition time 
40 ms 
Nmin - Minimum number of same state samples 
3 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
*Min and max active state current given for Vs in the operating voltage range from Vnom to Vmax 
 
ODH_x 
Name(s) 
ODH_x 
Type 
Digital output, Active High 
Output power source 
Vs_prot 
Uoh_min , min output voltage in active state  
Vs-1.0V @ 100mA 
Ioh_min ,min source current required in active state 
-100 mA @ Vmin to Vmax 
Io_leak ,max leakage current in inactive state 
-10 µA max @ Vnom 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
 


### 第 42 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 42 of 74 
October 2005
 
A4 PWM Type Voltage Interface 
R1
R2
C1
Rp
+
-
D*
I_inj
R1
Rp
+
-
D*
I_inj
C1
Irx
Itx
Irx
Itx
Rp_source
Vcc
Vs
Vth
Power_gnd
Vs_sw
Rp_source
Vcc
Vs
Vth
Power_gnd
Vcc_sw
slave_supply
Vs_prot
Vs_prot
+
-
+
-
signal_rtn
+
-
+
-
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
signal_gnd
Rpu
Rpd
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
signal_gnd
PWM transmitter
PWM out
Slave unit
PWM out
Type A, General PWM interface. Vs supplied
Type B, slave unit PWM interface with Vcc <= 5V supply
Rpu
Rpd
f_pwm < 400 Hz
f_pwm < 1kHz
Gnd
D* = Reverse polarity protection mechanization Vmin dependent
Utx
Urx
Utx
Urx
transmitter
receiver
 
 
Electrical Characteristics 
Parameter 
Symbol 
Condition 
min 
typ 
max 
Unit 
Remarks 
Supply voltage 
Vs 
Maximum rating 
-13.5 
 
+26.5 
V 
No damage 
Supply voltage 
Vs 
Operating range with valid logic states 
Vmin 
14 
Vmax 
V 
 
Input voltage range 
Urx 
Maximum rating 
-1.0 
 
+26.5 
V 
 
 
 
 
 
 
 
 
 
Input voltage 
Urx 
Operating range Type A, Vs supplied 
-1.0 
 
Vmax+1 
V 
Ground offset 
allowed +- 1V 
Input logic low state 
Urxl 
Voltage range, Typ A, Vs supplied 
-1.0 
 
+2.0 
V 
 
Input logic high state 
Urxh 
Voltage range, Typ A, Vs supplied 
+5.5 
 
Vmax+1 
V 
 
Output voltage 
Utx 
Operating range Type A, Vs supplied 
0 
 
Vmax 
 
f < 400Hz 
Output voltage low 
Utxl 
Voltage range @ Io = 100mA, Type A 
0 
0.3 
1.0 
V 
Logic 0 
Output voltage high 
Utxh 
Voltage range @ Io = -100mA, Type A 
Vs-1 
 
Vs 
V 
Logic 1 
Output voltage dVdt 
tr, tf 
 0.1 Utxh to 0.9 Utxh, Type A 
0.1 
 
1.0 
V/µs 
 
Input voltage 
Urx 
Operating range Type B, Vcc supplied 
0 
 
Vcc 
V 
Ground offset 
allowed 0V 
Input logic low state 
Urxl 
Voltage range, Typ B, Vcc supplied 
0 
 
0.3Vcc 
V 
Signal_rtn ref. 
Input logic high state 
Urxh 
Voltage range, Typ B, Vcc supplied 
0.7Vcc 
 
Vcc 
V 
Signal_rtn ref. 
Output voltage 
Utx 
Operating range Type B, Vcc supplied 
0 
 
Vcc 
 
f < 1 kHz 
Output voltage low 
Utxl 
Voltage range @ Io = 25mA, Type B 
0 
0.3 
1.0 
V 
Logic 0 
Output voltage high 
Utxh 
Voltage range @ Io = -25mA, Type B 
Vcc-1 
 
Vcc 
V 
Logic 1 


### 第 43 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 43 of 74
 
Parameter 
Symbol 
Condition 
min 
typ 
max 
Unit 
Remarks 
Output voltage dVdt 
tr, tf 
 0.1 Utxh to 0.9 Utxh, Type B 
0.1 
 
1.0 
V/µs 
 
 
R1 
 
 
33 
 
kΩ 
 
 
R2 
 
 
100 
 
kΩ 
Vth=30/70% 
 
R2 
 
 
220 
 
kΩ 
Vth=35/65% 
 
Rp 
 
680 
1.2 
3.3 
kΩ 
 
 
C1 
 
 
180 
 
pF 
 
Vmin, Vnom and Vmax defined by electrical code letter from the GMW3172 code string. Vcc normally 5.0V 
 


### 第 44 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 44 of 74 
October 2005
 
A4.1 Active Low PWM Interface, Vs Supplied 
R1
R2
C1
Rpu
+
-
D*
I_inj
R_no_float
Itx
Irx
Vcc
Vs
Vth
Power_gnd
Vs_sw
Vs_prot
+
-
+
-
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
signal_gnd
transmitter
Type A, Vs supplied, Active low PWM signal.
f_pwm < 400 Hz
D* = Reverse polarity protection mechanization Vmin dependent
Gnd
LS_driver
One common R_no_float load for each separate Vcc_sw net
Urx
Utx
receiver
 
 
IPL_Vs_x 
Name(s) 
IPL_Vs_x 
Type 
Digital pwm signal receiver, active low 
Rpu pullup voltage source 
Vs_sw 
Wake up type 
N/A 
Irxl_min min current required in active state 
-4 mA* 
Irxl_max max current allowed in active state 
-20 mA* 
Ground Offset Voltage 
0 to ±1.0 V 
Low Pass Filter – Time Constant 
5 µs 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
• 
Min and Max active state current given for Vs in the full operating voltage range from Vnom to Vmax 
OPL_Vs_x 
Name(s) 
OPL_Vs_x 
Type 
Digital pwm signal transmitter, active low 
Output Power Source 
Gnd 
Utxl_max - Max saturation voltage in active state  
1.0 V @ 100mA Itx 
Itxl_min – Min sink current in active state  
100mA @ Vmin to Vmax 
Ileak_max - Max leakage current in inactive state 
10 µA  
Output Frequency 
x Hz +-0.5% (< 400Hz, f-jitter<0.25%))  
Normal output signal PWM duty cycle range 
5-95% 
Normal output signal PWM duty cycle resolution 
0.5% 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection - Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
 


### 第 45 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 45 of 74
 
A4.2 Active Low PWM Interface, Vcc Supplied 
R1
C1
Rpu
+
-
D*
I_inj
R_no_float
Itx
Irx
Vcc
Vs
Vth
Power_gnd
Vcc_sw
slave_supply
signal_rtn
+
-
+
-
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
signal_gnd
transmitter
Type B, Vcc supplied. Active low PWM signal.
f_pwm < 1 kHz
D* = Reverse polarity protection mechanization Vmin dependent.
LS_driver
One common R_no_float load for each separate Vcc_sw net
Urx
Utx
receiver
 
IPL_Vcc_x 
Name(s) 
IPL_Vcc_x 
Type 
Digital pwm signal receiver, active low 
Rpu pullup voltage source 
Vcc_sw (Normally 5V) 
Wake up type 
N/A 
Irxl_min min current required in active state 
-4 mA* 
Irxl_max max current allowed in active state 
-20 mA* 
Ground Offset Voltage 
0 V 
Low Pass Filter – Time Constant 
2.2 µs 
Protected for shorts to (including Vcc_sw output): 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
• 
Min and Max active state current given for Vs in the full operating voltage range from Vmin to Vmax 
 
OPL_Vcc_x 
Name(s) 
OPL_Vcc_x 
Type 
Digital pwm signal transmitter, active low 
Output Power Source 
Signal_rtn wire from receiver signal_gnd 
Utxl_max - Max saturation voltage in active state  
1.0 V @ 25 mA Itx 
Itxl_min – Min sink current in active state  
25 mA @ Vcc=5V  
Ileak_max - Max leakage current in inactive state 
10 µA  
Output Frequency 
x Hz +-0.5% (< 1 kHz, f-jitter<0.25%))  
Normal output signal PWM duty cycle range 
5-95% 
Normal output signal PWM duty cycle resolution 
0.5% 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection - Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
 


### 第 46 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 46 of 74 
October 2005
 
A5 Active High PWM Interface 
A5.1 Active High PWM Interface, Vs Supplied 
R1
R2
C1
+
-
D*
I_inj
Rpd
Itx
Irx
Vcc
Vs
Vth
Power_gnd
Vs_prot
+
-
+
-
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
Signal_gnd
transmitter
f_pwm < 400 Hz
D* = Reverse polarity protection mechanization Vmin dependent
Gnd
HS_driver
Type A, Vs supplied. Active high PWM signal.
Urx
Utx
receiver
 
IPH_Vs_x 
Name(s) 
IPH_Vs_x 
Type 
Digital pwm signal receiver, active high 
Rpd pulldown voltage source 
Signal_gnd 
Wake up type 
N/A 
Irxh_min min current required in active state 
-4 mA* 
Irxh_max max current allowed in active state 
-20 mA* 
Ground Offset Voltage 
0 to ±1.0 V 
Low Pass Filter – Time Constant 
5 µs 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
• 
Min and Max active state current given for Vs in the full operating voltage range from Vnom to Vmax 
OPH_Vs_x 
Name(s) 
OPH_Vs_x 
Type 
Digital pwm signal transmitter, active high 
Output Power Source 
Vs_prot 
Utxh_min - Min output voltage in active state  
Vs-1.0 V @ 100mA Itx 
Itxh_min – Min source current in active state  
-100mA @ Vmin to Vmax 
Ileak_max - Max leakage current in inactive state 
10 µA  
Output Frequency 
x Hz +-0.5% (< 400Hz, f-jitter<0.25%))  
Normal output signal PWM duty cycle range 
5-95% 
Normal output signal PWM duty cycle resolution 
0.5% 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection - Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 


### 第 47 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 47 of 74
 
A5.2 Active High PWM Interface, Vcc Supplied 
R1
C1
Rpd
+
-
D*
I_inj
Itx
Irx
R_no_float
Input_pin+
Vcc
Vs
Vth
Power_gnd
Vcc_sw
slave_supply
signal_rtn
+
-
+
-
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
signal_gnd
transmitter
Type B, Vcc:_sw supplied. Active high PWM signal.
f_pwm < 1 kHz
D* = Reverse polarity protection mechanization Vmin dependent.
HS_driver
Urx
Utx
receiver
One common R_no_float load for each separate Vcc_sw net
 
 
IPH_Vcc_x 
Name(s) 
IPH_Vcc_x 
Type 
Digital pwm signal receiver, active high 
Rpd pulldown voltage source 
Signal_gnd 
Wake up type 
N/A 
Irxl_min min current required in active state 
-4 mA* 
Irxl_max max current allowed in active state 
-20 mA* 
Ground Offset Voltage 
0 V 
Low Pass Filter – Time Constant 
2.2 µs 
Protected for shorts to (including Vcc_sw output): 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
• 
Min and Max active state current given for Vs in the full operating voltage range from Vmin to Vmax. Vcc normally 5.0V 
OPH_Vcc_x 
Name(s) 
OPH_Vcc_x 
Type 
Digital pwm signal transmitter, active high 
Output Power Source 
Vcc_sw wire from receiver 
Utxh_min – Min output voltage in active state  
1.0 V @ 25 mA Itx 
Itxh_min – Min source current in active state  
-25 mA @ Vcc=5V  
Ileak_max - Max leakage current in inactive state 
10 µA  
Output Frequency 
x Hz +-0.5% (< 1 kHz, f-jitter<0.25%))  
Normal output signal PWM duty cycle range 
5-95% 
Normal output signal PWM duty cycle resolution 
0.5% 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection - Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
 


### 第 48 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 48 of 74 
October 2005
 
A6 Frequency Type Control Signals. 
A6.1 Frequency Type Voltage Interfaces 
R1
R2
C1
Rp
+
-
D*
I_inj
R1
Rp
+
-
D*
I_inj
C1
Irx
Irx
Itx
Itx
Rp_source
Vcc
Vs
Vth
Power_gnd
Vs_sw
Rp_source
Vcc
Vs
Vth
signal_rtn
Power_gnd
Vcc_sw
slave_supply
Vs
Vs_prot
+
-
+
-
+
-
+
-
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
Signal_gnd
Rpu
Rpd
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
Signal_gnd
frequency out
Slave unit
Type A, General frequency type voltage interface. Vs supplied
Type B, slave unit, frequency type voltage interface with Vcc <= 5V supply
Rpu
Rpd
f < 15 kHz
f < 15 kHz
Gnd
D* = Reverse polarity protection mechanization Vmin dependent
frequency out
Utx
Urx
Utx
Urx
Frequency transmitter
Frequency receiver
 
 


### 第 49 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 49 of 74
 
Electrical Characteristics 
Parameter 
Symbol 
Condition 
min 
typ 
max 
Unit 
Remarks 
Supply voltage 
Vs 
Maximum rating 
-13.5 
 
+26.5 
V 
No damage 
Supply voltage 
Vs 
Operating range with valid logic states 
Vmin 
14 
Vmax 
V 
 
Input voltage range 
Urx 
Maximum rating 
-1.0 
 
+26.5 
V 
 
Input voltage 
Urx 
Operating range Type A, Vs supplied 
-1.0 
 
Vmax+1 
V 
Ground offset 
allowed +- 1V 
Input logic low state 
Urxl 
Voltage range, Typ A, Vs supplied 
-1.0 
 
+2.0 
V 
 
Input logic high state 
Urxh 
Voltage range, Typ A, Vs supplied 
+5.5 
 
Vmax+1 
V 
 
Output voltage 
Utx 
Operating range Type A, Vs supplied 
0 
 
Vmax 
 
f < 15 kHz 
Output voltage low 
Utxl 
Voltage range @ Io = 100mA, Type A 
0 
0.3 
1.0 
V 
Logic 0 
Output voltage high 
Utxh 
Voltage range @ Io = -100mA, Type A 
Vs-1 
 
Vs 
V 
Logic 1 
Output voltage dVdt 
tr, tf 
 0.1 Utxh to 0.9 Utxh, Type A 
0.1 
 
1.0 
V/µs 
 
Input voltage 
Urx 
Operating range Type B, Vcc supplied 
0 
 
Vcc 
V 
Ground offset 
allowed 0V 
Input logic low state 
Urxl 
Voltage range, Typ B, Vcc supplied 
0 
 
0.3Vcc 
V 
Signal_rtn ref. 
Input logic high state 
Urxh 
Voltage range, Typ B, Vcc supplied 
0.7Vcc 
 
Vcc 
V 
Signal_rtn ref. 
Output voltage 
Utx 
Operating range Type B, Vcc supplied 
0 
 
Vcc 
 
f < 15 kHz 
Output voltage low 
Utxl 
Voltage range @ Io = 25mA, Type B 
0 
0.3 
1.0 
V 
Logic 0 
Output voltage high 
Utxh 
Voltage range @ Io = -25mA, Type B 
Vcc-1 
 
Vcc 
V 
Logic 1 
Output voltage dVdt 
tr, tf 
 0.1 Utxh to 0.9 Utxh, Type A 
0.1 
 
1.0 
V/µs 
 
 
R1 
 
 
33 
 
kΩ 
 
 
R2 
 
 
100 
 
kΩ 
Vth=30/70% 
 
R2 
 
 
220 
 
kΩ 
Vth=35/65% 
 
Rp 
 
680 
1.2 
3.3 
kΩ 
 
 
C1 
 
 
180 
 
pF 
 
Vmin, Vnom and Vmax defined by electrical code letter from the GMW3172 code string. Vcc normally 5.0V 


### 第 50 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 50 of 74 
October 2005
 
A6.1.1 Active Low Frequency Interface, Vs Supplied 
R1
R2
C1
Rpu
+
-
D*
I_inj
R_no_float
Itx
Irx
Vcc
Vs
Vth
Power_gnd
Vs_sw
Vs_prot
+
-
+
-
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
signal_gnd
Vs supplied, Active low frequency type voltage interface.
f < 15kHz
D* = Reverse polarity protection mechanization Vmin dependent
Gnd
LS_driver
One common R_no_float load for each separate Vs_sw net
Utx
Urx
transmitter
receiver
 
IFL_Vs_x 
Name(s) 
IFL_Vs_x 
Type 
Digital frequency signal receiver, active low 
Rpu pullup voltage source 
Vs_sw 
Wake up type 
N/A 
Irxl_min min current required in active state 
-4 mA* 
Irxl_max max current allowed in active state 
-20 mA* 
Ground Offset Voltage 
0 to ±1.0 V 
Low Pass Filter – Time Constant 
5 µs 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
Min and Max active state current given for Vs in the full operating voltage range from Vnom to Vmax 
OFL_Vs_x 
Name(s) 
OFL_Vs_x 
Type 
Digital frequency signal transmitter, active low 
Output Power Source 
Gnd 
Utxl_max - Max saturation voltage in active state 
1.0 V @ 100mA Itx 
Itxl_min – Min sink current in active state 
100mA @ Vmin to Vmax 
Ileak_max - Max leakage current in inactive state 
10 µA  
Output Frequency range: fmin to fmax 
f = x to y Hz (0< f < 15 kHz) 
Frequency signal period time jitter 
1µs < tjitter < 0.005/fmax 
Normal output signal fixed duty cycle 
0.33 to 0.66 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection - Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 


### 第 51 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 51 of 74
 
A6.1.2 Active Low Frequency Interface, Vcc Supplied 
R1
C1
Rpu
+
-
D*
I_inj
R_no_float
Itx
Irx
Vcc
Vs
Vth
Power_gnd
Vcc_sw
slave_supply
signal_rtn
+
-
+
-
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
signal_gnd
Vcc_sw supplied. Active low, frequency type voltage interface.
f < 15 kHz
D* = Reverse polarity protection mechanization Vmin dependent.
LS_driver
One common R_no_float load for each separate Vcc_sw net
Urx
Utx
transmitter
receiver
 
IFL_Vcc_x 
Name(s) 
IFL_Vcc_x 
Type 
Digital frequency signal receiver, active low 
Rpu pullup voltage source 
Vcc_sw (Normally 5V) 
Wake up type 
N/A 
Irxl_min min current required in active state 
-4 mA* 
Irxl_max max current allowed in active state 
-20 mA* 
Ground Offset Voltage 
0 V 
Low Pass Filter – Time Constant 
2.2 µs 
Protected for shorts to (including Vcc_sw output): 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
• 
Min and Max active state current given for Vs in the full operating voltage range from Vmin to Vmax 
OFL_Vcc_x 
Name(s) 
OFL_Vcc_x 
Type 
Digital frequency signal transmitter, active low 
Output Power Source 
Signal_rtn wire from receiver signal_gnd 
Utxl_max - Max saturation voltage in active state  
1.0 V @ 25 mA Itx 
Itxl_min – Min sink current in active state  
25 mA @ Vcc=5V  
Ileak_max - Max leakage current in inactive state 
10 µA  
Output Frequency range: fmin to fmax 
f = x to y Hz (0< f < 15 kHz) 
Frequency signal period time jitter 
1µs < tjitter < 0.005/fmax 
Normal output signal fixed duty cycle 
0.33 to 0.66 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection - Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 


### 第 52 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 52 of 74 
October 2005
 
A7 Active High Frequency Interface 
A7.1 Active High Frequency Interface, Vs Supplied 
R1
R2
C1
+
-
D*
I_inj
Rpd
Itx
Irx
Vcc
Vs
Vth
Power_gnd
Vs_prot
+
-
+
-
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
signal_gnd
transmitter
f < 15 kHz
D* = Reverse polarity protection mechanization Vmin dependent
Gnd
HS_driver
Type A, Vs_prot supplied. Active high frequency type voltage interface.
receiver
Urx
Utx
 
IFH_Vs_x 
Name(s) 
IFH_Vs_x 
Type 
Digital frequency signal receiver, active high 
Rpd pulldown voltage source 
Signal_gnd 
Wake up type 
N/A 
Irxh_min min current required in active state 
-4 mA* 
Irxh_max max current allowed in active state 
-20 mA* 
Ground Offset Voltage 
0 to ±1.0 V 
Low Pass Filter – Time Constant 
5 µs 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
• 
Min and Max active state current given for Vs in the full operating voltage range from Vnom to Vmax 
OFH_Vs_x 
Name(s) 
OFH_Vs_x 
Type 
Digital frequency signal transmitter, active high 
Output Power Source 
Vs_prot 
Utxh_min - Min output voltage in active state  
Vs-1.0 V @ 100mA Itx 
Itxh_min – Min source current in active state  
-100mA @ Vmin to Vmax 
Ileak_max - Max leakage current in inactive state 
10 µA  
Output Frequency range: fmin to fmax 
f = x to y Hz (0< f < 15 kHz) 
Frequency signal period time jitter 
1µs < tjitter < 0.005/fmax 
Normal output signal fixed duty cycle 
0.33 to 0.66 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection - Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 


### 第 53 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 53 of 74
 
A7.1.2 Active High Frequency Interface, Vcc Supplied 
R1
C1
Rpd
+
-
D*
I_inj
Itx
Irx
R_no_float
Vcc
Vs
Vth
Power_gnd
Vcc_sw
slave_supply
signal_rtn
+
-
+
-
1nF@Input_pin
5V LDO
Voltage
Regulator
uC
signal_gnd
transmitter
Vcc_sw supplied. Active high, frequency type voltage interface.
D* = Reverse polarity protection mechanization Vmin dependent.
HS_driver
f < 15 kHz
Urx
Utx
receiver
One common R_no_float load for each separate Vcc_sw net
 


### 第 54 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 54 of 74 
October 2005
 
IFH_Vcc_x 
Name(s) 
IFH_Vcc_x 
Type 
Digital frequency signal receiver, active high 
Rpd pulldown voltage source 
Signal_gnd 
Wake up type 
N/A 
Irxl_min min current required in active state 
-4 mA* 
Irxl_max max current allowed in active state 
-20 mA* 
Ground Offset Voltage 
0 V 
Low Pass Filter – Time Constant 
2.2 µs 
Protected for shorts to (including Vcc_sw output): 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
• 
Min and Max active state current given for Vs in the full operating voltage range from Vmin to Vmax. Vcc normally 5.0V 
 
OFH_Vcc_x 
Name(s) 
OFH_Vcc_x 
Type 
Digital frequency signal transmitter, active high 
Output Power Source 
Vcc_sw wire from receiver 
Utxh_min – Min output voltage in active state  
1.0 V @ 25 mA Itx 
Itxh_min – Min source current in active state  
-25 mA @ Vcc=5V  
Ileak_max - Max leakage current in inactive state 
10 µA  
Output Frequency range: fmin to fmax 
f = x to y Hz (0< f < 15 kHz) 
Frequency signal period time jitter 
1µs < tjitter < 0.005/fmax 
Normal output signal fixed duty cycle 
0.33 to 0.66 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection - Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 


### 第 55 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 55 of 74
 
A8 Frequency Type Current Interfaces 
Rsense
G
Itx
D*
Itx
Irx
R_no_float
signal_rtn
signal_supply
Vs_prot
+
-
Vs
Power_gnd
+
-
Vs_sw
Utx
Urx
D* = Reverse polarity protection mechanization Vmin dependent
Sensor or
Slave unit
signal_gnd
transmitter
receiver
Current Interface
One common R_no_float load for each separate Vs_sw net
SC-protection
 
Electrical Characteristics 
Parameter 
Symbol 
Condition 
min 
typ 
max 
Unit 
Remarks 
Supply voltage 
Vs 
Maximum rating 
-13.5 
 
+26.5 
V 
No damage 
Supply voltage 
Vs 
Operating range with valid current 
interface signals 
Vmin 
14 
Vmax 
V 
 
Input voltage range 
Urx, Utx 
Maximum rating 
0 
 
+26.5 
V 
 
Input voltage 
Urx 
Normal operation 
0 
Irxmax x 
Rsense 
2.0 
V 
Ground 
offset 
allowed = 
0V 
Input logic low state 
Irxl 
 
5.4 
7.0 
8.6 
mA 
 
Input logic high 
state 
Irxh 
 
11.0 
14.0 
17.0 
mA 
 
Output voltage 
Utx 
Normal operation 
 
 
Vmax 
V 
 
Output current low 
Itxl 
 
5.6 
7 
8.4 
V 
Logic 0 
Output current high 
Itxh 
 
11.2 
14 
16.8 
V 
Logic 1 
Output current dI/dt 
 
 
10 
 
100 
mA/µs 
 
 
Rsense 
 
Rsense-
1% 
115 
Rsense+1% 
Ω 
 
Vmin, Vnom and Vmax defined by electrical code letter from the GMW3172 code string. 


### 第 56 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 56 of 74 
October 2005
 
IFC_Vs_x 
Name(s) 
IFC_Vs_x 
Type 
Digital frequency signal current receiver, active 
high 
Rsense pulldown voltage source 
Signal_gnd 
Wake up type 
N/A 
Ground Offset Voltage 
0 V 
Low Pass Filter – Time Constant 
5 µs 
Protected for shorts to: 
Gnd and Vbatt 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
• 
Min and Max active state current given for Vs in the full operating voltage range from Vmin to Vmax 
 
OFC_Vs_x 
Name(s) 
OFC_Vs_x 
Type 
Digital frequency signal current transmitter, 
active high 
Output Power Source 
Vs_sw 
Output Frequency range: fmin to fmax 
f = x to y Hz (0< f < 15 kHz) 
Frequency signal period time jitter 
1µs < tjitter < 0.005/fmax 
Normal output signal fixed duty cycle 
0.33 to 0.66 
Protected for shorts to (including Vs_sw output): 
Gnd and Vbatt 
ESD Protection - Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
 


### 第 57 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 57 of 74
 
A9 Analog Inputs. 
A9.1 Analog Inputs Ratiometric. 
R1**
51kOhm
C1
Rpu*
D*
Ii
Io
I_inj
R_no_float
Vcc
sensor_supply*
Vs
Power_gnd
Vcc_sw
+
-
signal_rtn
+
-
10nF@Input_pin
A/D Converter Input
Resolution 8 or 10 Bit of A/D_Vref
A
*Rpu and need for sensor_supply application dependent
Vref+
Vref-
uC
In
D
signal_gnd
Sensor
5V LDO
Voltage
Regulator
**A/D input protection and accuracy dependent on R1
Gnd
D* = Reverse polarity protection mechanization Vmin dependent
Ui
Uo
receiver
transmitter
One common R_no_float load for each separate Vcc_sw net
 
Electrical Characteristics 
Parameter 
Symbol 
Condition 
min 
typ 
max 
Unit 
Remarks 
Supply voltage 
Vs 
Maximum rating 
-13.5 
 
+26.5 
V 
No damage 
Supply voltage 
Vs 
Operating range with correct 
accuracy 
Vmin 
14 
Vmax 
V 
 
A/D input voltage 
Ui 
Operating range 
Vref- 
0-5 
Vref+ 
V 
 
A/D input voltage range 
Ui 
Maximum rating 
-1.0 
 
+Vmax 
V 
 
Input time constant 
Tad 
R1xC1 
 
1 
 
ms 
 
R1 Input Protection 
R1* 
 
 
10 
 
kΩ 
 
Filter C1 
C1** 
 
 
100 
 
nF 
 
Pullup resistor 
Rpu 
Application dependent 
200 
 
Open 
Ω 
 
Vmin, Vnom and Vmax defined by electrical code letter from the GMW3172 code string. 
*Input protection shall pass GMW3097 validation requirements and have a 0805 size ceramic capacitor land pad available at the 
connector pin 
*Minimum value of R1 depends on allowed injection current for AD input pin, Maximum value depend on A/D accuracy. 
**Minimum value of C1 depends on allowed AD input source capacitance requirement. 


### 第 58 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 58 of 74 
October 2005
 
Electrical I/O Characteristics 
Analog I/O name 
Description 
Rpu 
[Ohm] 
Sensor supply 
[V] 
Tol. 
[%] 
A/D 
Resolution 
[Bits] 
mV 
Ui 
Range 
[V] 
Remarks 
Specific 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 59 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 59 of 74
 
A10 Analog Switch Input with Diagnosis. With chosen component values a ground offset of 0.8V can be 
tolerated and the switch can be grounded to the vehicle body. 
State 
Ui_low 
Ui_high 
Unit 
Remarks 
Short to Gnd 
-1 
0.6 
V 
Ui range 
Closed switch 
0.6 
2.5 
V 
Ui range 
Open switch 
2.5 
4.4 
V 
Ui range 
Short to Vs 
4.4 
Vs 
V 
Ui range 
 
R1**
51k
C1
680 +/-1%
D*
390 +/-1%
1500 +/-1%
390 +/-1%
1500 +/-1%
Ii
I_inj
R_no_float
Vcc
Vs
Power_gnd
Vcc_sw
+
-
signal_rtn
+
-
+
-
10nF@Input_pin
A/D Converter Input
Resolution 8 or 10 Bit of A/D_Vref
A
Vref+
Vref-
uC
In
D
signal_gnd
5V LDO
Voltage
Regulator
OR
Gnd
Alternative switch 
grounding
**A/D input protection dependent on R1
D* = Reverse polarity protection mechanization Vmin dependent
Ui
receiver
switch
Uo
Uo
One common R_no_float load for each separate Vcc_sw net
 
Electrical Characteristics 
Parameter 
Symbol 
Condition 
min 
typ 
max 
Unit 
Remarks 
Supply voltage 
Vs 
Maximum rating 
-13.5 
 
+26.5 
V 
No damage 
Supply voltage 
Vs 
Operating range with correct 
accuracy 
Vmin 
14 
Vmax 
V 
 
A/D input voltage 
Ui 
Operating range 
Vref- 
0-5 
Vref+ 
V 
 
A/D input voltage range 
Ui 
Maximum rating 
-1.0 
 
+Vmax 
V 
 
Input time constant 
Tad 
R1xC1 
 
1 
 
ms 
 
R1 Input Protection 
R1* 
 
 
10 
 
kΩ 
 
Filter C1 
C1** 
 
 
100 
 
nF 
 
Pullup resistor 
Rpu 
 
 
680 
 
Ω 
± 1%  
Vmin, Vnom and Vmax defined by electrical code letter from the GMW3172 code string. 
*Input protection shall pass GMW3097 validation requirements and have a 0805 size ceramic capacitor land pad available at the 
connector pin 
*Minimum value of R1 depends on allowed injection current for AD input pin, Maximum value depend on A/D accuracy. 
**Minimum value of C1 depends on allowed AD input source capacitance requirement. 


### 第 60 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 60 of 74 
October 2005
 
A10.1 Analog Inputs 5V Reference Resistor Ladder 
A10.1.1 Type A. 
Rin
C1
Rpu
D*
SW1
SW2
SW3
SW4
SW5
SWn
R1
R2
R3
R4
Ropen
Rn
R5
R_no_float
Ii
Vcc
Vs
Power_gnd
Vcc_sw
+
-
signal_rtn
10nF@Input_pin
A/D Converter Input
Resolution 8 or 10 Bit of A/D_Vref
A
Vref+
Vref-
uC
In
D
signal_gnd
5V LDO
Voltage
Regulator
...
D* = Reverse polarity protection mechanization Vmin dependent
One common R_no_float load for each separate Vcc_sw net
Ui
receiver
keypad
 
Note: All resistors are 1%.  
Note: Reopen can be deleted. In this case there is no difference between a disconnected switch assembly and 
no switch pressed. 
Number 
of 
functions, 
(max closed 
switch 
impedancel) 
R1 
Ω 
R2 
Ω 
R3 
Ω 
R4 
Ω 
R5 
Ω 
R6 
Ω 
R7 
Ω 
R8 
Ω 
R9 
Ω 
R10 
Ω 
R11 
Ω 
Rpu 
Ω 
Ropen 
Ω 
3 (20 Ω) 
604 
255 
113 
 
 
 
 
 
 
 
 
499 
4640 
4 (20 Ω) 
806 
340 
178 
84.5 
 
 
 
 
 
 
 
499 
4640 
5 (20 Ω) 
845 
374 
205 
127 
57.6 
 
 
 
 
 
 
499 
4640 
6 (20 Ω) 
768 
374 
215 
140 
97.6 
46.4 
 
 
 
 
 
499 
4640 
7 (20 Ω) 
1070 
487 
280 
178 
121 
88.7 
46.4 
 
 
 
 
499 
4640 
8 (20 Ω) 
1070 
523 
309 
196 
133 
100 
75 
46.4 
 
 
 
499 
4640 
9 (5 Ω) 
1150 
549 
309 
200 
137 
97.6 
73.2 
53.6 
46.4 
 
 
499 
4640 
10 (5 Ω) 
1370 
634 
348 
226 
150 
110 
84.5 
60.4 
47.5 
46.4 
 
499 
4640 
11 (5 Ω ) 
1650 
715 
402 
249 
174 
121 
88.7 
68.1 
53.6 
41.2 
33.2 
499 
6980 


### 第 61 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 61 of 74
 
 
 
3 switch example for 5.1 ≤ ADC supply ≤ 4.9: 
• 
Short to Batt � Ui ≥ 4.75 volts 
• 
Open (switch disconnected) � 4.75 > Ui ≥ 3.95 volts 
• 
SW1 active � 3.95 > Ui ≥ 2.79 volts 
• 
SW2 active � 2.79 > Ui ≥ 1.53 volts 
• 
SW3 active � 1.53 > Ui ≥0.4 volts 
• 
Short to Gnd � Ui < 0.40 volts 
Number of 
functions, 
(max closed 
switch 
impedancel) 
Number of 
functions, 
(material) 
Short 
to Batt 
> 
x 
Volts 
Open 
> x < 
Short 
Volts 
SW1 
> x < 
Open 
Volts 
SW2 
> x < 
SW1 
Volts 
SW3 
> x < 
SW2 
Volts 
SW4 
> x < 
SW3 
Volts 
SW5 
> x < 
SW4 
Volts 
SW6 
> x < 
SW5 
Volts 
SW7 
> x < 
SW6 
Volts 
SW8 
> x < 
SW7 
Volts 
SW9 
> x < 
SW8 
Volts 
SW10 
> x < 
SW9 
Volts 
SW11 
> x < 
SW10 
Volts 
3 (20 Ω) 
3 
4.75 
3.95 
2.79 
1.53 
0.4 
 
 
 
 
 
 
 
 
4 (20 Ω) 
4 
4.77 
4.13 
3.20 
2.31 
1.25 
0.33 
 
 
 
 
 
 
 
5 (20 Ω) 
5 
4.77 
4.20 
3.40 
2.60 
1.80 
0.96 
0.24 
 
 
 
 
 
 
6 (20 Ω) 
6 
4.77 
4.20 
3.48 
2.83 
2.16 
1.49 
0.83 
0.20 
 
 
 
 
 
7 (20 Ω) 
7 (gold) 
4.79 
4.4 
3.85 
3.3 
2.65 
2.05 
1.45 
0.80 
0.20 
 
 
 
 
8 (20 Ω) 
8 (gold) 
4.79 
4.38 
3.88 
3.40 
2.88 
2.36 
1.82 
1.30 
0.76 
0.20 
 
 
 
9 (5 Ω) 
9 (gold) 
4.79 
4.40 
3.92 
3.45 
2.96 
2.47 
1.94 
1.51 
1.06 
0.64 
0.20 
 
 
10 (5 Ω) 
10 (gold) 
4.81 
4.45 
4.05 
3.61 
3.14 
2.71 
2.23 
1.89 
1.45 
1.05 
0.65 
0.20 
 
11 (5 Ω ) 
11 (gold) 
4.9 
4.54 
4.14 
3.73 
3.31 
2.88 
2.45 
2.01 
1.61 
1.21 
0.84 
0.48 
0.15 


### 第 62 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 62 of 74 
October 2005
 
A10.1.2 Type B. 
Rin
C1
Rpu
D*
S1
S2
R2
S3
R3
Sn
Rn
Rn+1
R_no_float
Ii
Rpd
Rcomp
Vcc
Vs
Power_gnd
Vcc_sw
+
-
signal_rtn
10nF@Input_pin
A/D Converter Input
Resolution 8 or 10 Bit of A/D_Vref
A
Vref+
Vref-
uC
In
D
signal_gnd
5V LDO
Voltage
Regulator
...
D* = Reverse polarity protection mechanization Vmin dependent
One common R_no_float load for each separate Vcc_sw net
Ui
receiver
keypad
 
Resistor code table 
 
Note: All resistors are 1% except for Rpd and Rcomp which are 5%. 
Number of 
functions, 
(max closed 
switch 
impedancel) 
R2 
Ω 
R3 
Ω 
R4 
Ω 
R5 
Ω 
R6 
Ω 
R7 
Ω 
R8 
Ω 
R9 
Ω 
R10 
Ω 
R11 
Ω 
R12 
Ω 
Rpu 
Ω 
Rpd 
Ω 
Rcomp 
Ω 
3 (20 Ω) 
105 
226 
845 
 
 
 
 
 
 
 
 
237 
10k 
10 
4 (20 Ω) 
82.5 
154 
365 
2000 
 
 
 
 
 
 
 
237 
10k 
10 
5 (20 Ω) 
64.9 
105 
187 
422 
2000 
 
 
 
 
 
 
237 
10k 
10 
6 (20 Ω) 
52.3 
73.2 
113 
196 
402 
1240 
 
 
 
 
 
237 
10k 
10 
7 (20 Ω) 
45.3 
61.9 
86.6 
130 
221 
453 
1370 
 
 
 
 
237 
10k 
10 
8 (20 Ω) 
41.2 
53.6 
73.2 
105 
158 
261 
576 
1870 
 
 
 
237 
10k 
10 
9 (5 Ω) 
24.3 
32.4 
42.2 
57.6 
82.5 
121 
205 
374 
953 
 
 
237 
10k 
10 
10 (5 Ω) 
23.2 
30.1 
39.2 
51.1 
71.5 
105 
162 
274 
634 
2150 
 
237 
10k 
10 
11 (5 Ω ) 
20 
26.1 
33.2 
44.2 
57.6 
82.5 
115 
182 
309 
715 
2740 
237 
10k 
10 


### 第 63 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 63 of 74
 
 
Number 
of 
functions, (max 
closed 
switch 
impedancel) 
SW1 
< x 
Volts 
SW2 < 
x 
> 
SW1 
Volts 
SW3 < 
x 
> 
SW2 
Volts 
SW4 < 
x 
> 
SW3 
Volts 
SW5 < 
x 
> 
SW4 
Volts 
SW6 < 
x 
> 
SW5 
Volts 
SW7 < 
x 
> 
SW6 
Volts 
SW8 < 
x 
> 
SW7 
Volts 
SW9 < 
x 
> 
SW8 
Volts 
SW10 < 
x > SW9 
Volts 
SW11 < 
x 
> 
SW10 
Volts 
3 (20 Ω) 
1.05 
2.31 
3.5 
 
 
 
 
 
 
 
 
4 (20 Ω) 
0.95 
2.02 
3.04 
3.99 
 
 
 
 
 
 
 
5 (20 Ω) 
0.85 
1.75 
2.59 
3.38 
4.11 
 
 
 
 
 
 
6 (20 Ω) 
0.87 
1.51 
2.21 
2.84 
3.5 
4.08 
 
 
 
 
 
7 (20 Ω) 
0.73 
1.4 
2.02 
2.61 
3.16 
3.69 
4.17 
 
 
 
 
8 (20 Ω) 
0.7 
1.32 
1.89 
2.44 
2.95 
3.42 
3.87 
4.3 
 
 
 
9 (5 Ω) 
0.44 
0.88 
1.33 
1.8 
2.28 
2.74 
3.21 
3.67 
4.1 
 
 
10 (5 Ω) 
0.39 
0.85 
1.28 
1.72 
2.16 
2.62 
3.07 
3.5 
3.93 
4.35 
 
11 (5 Ω ) 
0.41 
0.78 
1.17 
1.58 
2.0 
2.42 
2.84 
3.24 
3.64 
4.03 
4.41 
Note: An open state is any voltage greater than SWn. 
• 
3 switch example for 5.1 ≤ ADC supply ≤ 4.9: 
• 
SW1 active � Ui ≤ 1.05 volts 
• 
SW2 active � 1.05 < Ui ≤ 2.31 volts 
• 
SW3 active � 2.31 < Ui ≤ 3.5 volts 
• 
Open (switch disconnected) � Ui > 3.5 volts 


### 第 64 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 64 of 74 
October 2005
 
A10.2 Analog Inputs Absolute (AD+input > Vref) 
R1**
10k
C1
Rpu*
D*
I_inj
R2*/**
Ii
Io
R_no_float
Vcc
sensor_supply*
Vs
Power_gnd
Vs_sw
+
-
signal_rtn
+
-
10nF@Input_pin
A/D Converter Input
Resolution 8 or 10 Bit of A/D_Vref
A
*Rpu, R2 values and need for sensor_supply application dependent
Vref+
Vref-
uC
In
D
signal_gnd
Sensor
5V LDO
Voltage
Regulator
**A/D input protection and accuracy dependent on R1//R2
Gnd
D* = Reverse polarity protection mechanization Vmin dependent
Ui
Uo
receiver
transmitter
One common R_no_float load for each separate Vs_sw net
 
Electrical Characteristics 
Parameter 
Symbol 
Condition 
min 
typ 
max 
Unit 
Remarks 
Supply voltage 
Vs 
Operating range with correct 
accuracy 
Vmin 
14 
Vmax 
V 
 
Supply voltage 
Vs_max 
Maximum rating 
-13.5 
 
+26.5 
V 
No damage 
Input voltage 
Vad+ 
Operating range 
Vref- 
0-16 
+26.5 
V 
 
Input voltage range 
Vad+ 
Maximum rating 
-1.0V 
 
+26.5 
V 
 
Input time constant 
Tad 
R1xC1 
 
1 
 
ms 
 
R1 Input Protection 
R1* 
 
 
10 
 
kΩ 
 
Filter C1 
C1** 
 
 
100 
 
nF 
 
R2 voltage divider 
R2 
Depends on AD+input voltage 
range 
 
2.2 
 
kΩ 
 
Vmin, Vnom and Vmax defined by electrical code letter from the GMW3172 code string. 
*Input protection shall pass GMW3097 validation requirements and have a 0805 size ceramic capacitor land pad available at the 
connector pin 
*Minimum value of R1 depends on allowed injection current for AD input pin, Maximum value depend on A/D accuracy. 
**Minimum value of C1 depends on allowed AD input source capacitance requirement. 
 
Electrical I/O Characteristics 
Analog I/O name 
Sensor 
Supply 
Used 
Rpu 
[kΩ]  
±1% 
R1 
[Ω] 
±1% 
R2 
[Ω] 
±1% 
Ui  
linear input 
range 
[V] 
Resolution 
[Bits] 
mV 
C1 
[nF] 
Remarks 
Specific 
 
 
 
 
 
 
 
 
Vbatt monitor 
Internal 
Vs_sw 
0k (short) 
10.0k 
2.26k 
6-27.1 
[8] 
105 
100 
Shottky polarity 
protection 
Vbatt monitor 
Internal 
Vs_sw 
0k (short) 
10.0k 
4.42k 
6-16.3 
[8] 
63 
100 
Shottky polarity 
protection 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 65 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 65 of 74
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 66 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 66 of 74 
October 2005
 
A11 Low Side Drivers. 
R_load
L_load
Io
B
A
10n
R_load
L_load
Io
10n
-
+
Vs
Vs_prot
+
-
Power_gnd
Vs_prot
V_load
Uo
Uo
on/off control
pwm control
Type A: Fast switch off loads
Type B: PWM controlled loads
 
Electrical Characteristics 
Parameter 
Symbol 
Condition 
min 
typ 
max 
Unit 
Remarks 
Supply voltage 
Vs 
Operating range  
Vmin 
14 
Vmax 
V 
 
Supply voltage 
Vs_max 
Maximum rating 
-13.5 
 
+26.5 
V 
No damage 
Uo(Vsat) 
 
Tmin to Tmax, Vmin to Vmax 
0 
 
1.0 
V 
 
Resistive load 
R_load 
Tmin to Tmax 
 
 
 
Ω 
 
Inductive load 
I_load 
Tmin to Tmax  
 
 
 
H 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Vmin, Vnom and Vmax defined by electrical code letter from the GMW3172 code string. 
*Input protection shall pass GMW3097 validation requirements and have a 0805 size ceramic capacitor land pad available at the 
connector pin 


### 第 67 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 67 of 74
 
A11.1 Low Side Drive Discrete Outputs. 
R_load
L_load
B
A
10n
D*
Io
-
+
Vs
Power_gnd
Uo
on/off control
Type A: Repetitive fast switch off loads
*
*
*
*
* = Complete transient protection will depend on GMW3097 results
D* = Reverse polarity protection mechanization Vmin dependent
 
ODLS_x 
Name(s) 
ODLS_x 
Type 
Low side drive discrete output 
Output Power Source 
Power_gnd 
Uol_max 
1.0 V 
Iol_min - Min current required in the active state 
x A  
Iol_max - Max current allowed in the active state 
y A 
f_act - Maximum actuation frequency 
x Hz 
t_on_max – Maximum on time in active state for repetitive 
signals per cycle (if not continuous) 
x ms 
Ileak_max - Max leakage current allowed in the Inactive state 
10 µA 
Isurge - Max surge current allowed in the active state 
10 * Iol_max 
R_load_min @ -40 C 
x Ω 
L_load_max @ -40 C (activated if mech. actuator) 
y mH 
Avalanche energy handling:  
E=1/2*(Iol_max * L_load_max) 
mJ 
Ground offset voltage 
1.0 V 
Output protected for shorts to: 
Gnd and Vbatt 
Over current shut-off 
Allowed 
ESD protection – handling  
Shall meet GMW3097 @ ≤ 8 kV 
ESD protection – powered 
Shall meet GMW3097 @ ≤ 25 kV 
 


### 第 68 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 68 of 74 
October 2005
 
A11.2 Low Side Drive PWM Outputs. 
R_load
L_load
10n
D*
Io
+
-
Vs
Power_gnd
Uo
pwm control
*
*
*
*
Free Wheel Diode
PWM controlled load
* = Complete transient protection will depend on GMW3097 results
D* = Reverse polarity protection mechanization Vmin dependent
 
OPLS_x 
Name(s) 
OPLS_x 
Type 
Low side drive PWM output 
Output power source 
Power_gnd 
Uol_max - Max voltage allowed in the active state  
1.0 V @ Iol_max 
Iol_min - Min current required in the active state  
x A 
Iol_max - Max current required in the active state 
y A  
Ileak_max - Max leakage current allowed in the Inactive state 
10 µA  
Isurge - Max surge current allowed in the active state 
10 * Iol_max 
R_load_min @ -40 C 
x Ω 
L_load_max @ -40 C (activated if mech. actuator) 
y mH 
Avalanche energy handling:  
E=1/2*(Iol_max * L_load_max) 
mJ 
Output frequency 
x Hz +/-1% (<400Hz load via harness, <25kHz 
driver integrated with load) 
Duty cycle 
0-100% 
Output protected for shorts to: 
Gnd and Vbatt 
Over current shut-off 
Allowed 
ESD Protection - Handling  
Shall meet GMW3097 @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097 @ ≤ 25 kV 
 


### 第 69 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 69 of 74
 
A12 High Side Drivers. 
R_load
L_load
10n
B
A
R_load
L_load
10n
D1*
Io
Io
Vs
-
+
+
-
-
+
+
-
Power_gnd
Uo
Usat
Uo
Usat
* For high current loads
D1 is normally replaced by a
syncronous rectifier
Typ A: Fast switch off loads
Typ B: PWM controlled loads
pwm control
on/off control
 
Electrical Characteristics 
Parameter 
Symbol 
Condition 
min 
typ 
max 
Unit 
Remarks 
Supply voltage 
Vs 
Operating range  
6.0 
14 
Vmax 
V 
 
Supply voltage 
Vs_max 
Maximum rating 
-13.5 
 
+26.5 
V 
No damage 
Uoh_min 
 
Tmin to Tmax,  
Vmin to Vmax 
Vs - 1 
 
Vs 
V 
 
Resistive load 
R_load 
Tmin to Tmax 
 
 
 
Ω 
 
Inductive load 
I_load 
Tmin to Tmax  
 
 
 
H 
 
Load type A or B 
 
 
 
 
 
 
 
Actuation frequency 
 
 
 
128 
400 
Hz 
Load 
connected 
via wire 
harness 
Actuation frequency 
 
 
 
 
25 
kHz 
Driver 
integrated to 
load 
Vmin, Vnom and Vmax defined by electrical code letter from the GMW3172 code string. 
*Input protection shall pass GMW3097 validation requirements and have a 0805 size ceramic capacitor land pad available at the 
connector pin 


### 第 70 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 70 of 74 
October 2005
 
A12.1 High Side Drive Discrete Outputs. 
R_load
L_load
10n
B
A
D*
Io
Vs
-
+
+
-
Power_gnd
Uo
Usat
Typ A: Repetitive fast switch off loads
on/off control
*
*
* = Complete transient protection will depend on GMW3097 results
D* = Reverse polarity protection mechanization Uoh_min dependent
*
*
 
ODHS_x 
Name(s) 
ODHS_x 
Type 
High Side Drive Discrete Output 
Output Power Source 
Vs: Vbatt or Vign 
Uoh_min 
Vs – 1 V 
Ioh_min - Min Current Required in the Active State 
-x A 
Ioh_max - Max Current Allowed in the Active State 
-y A 
f_act - Maximum actuation frequency 
x Hz 
t_on_max – Maximum On time in Active state for repetitive 
signals per cycle (if not continuous) 
x ms 
Ileak_max - Max Leakage Current Allowed in the Inactive State 
10 µA @ 12.8 V 
Isurge - Max Surge Current Allowed in the Active State 
10 * Ioh_min 
R_load_min @ -40°C 
x Ω 
L_load_max @ -40°C (activated if mech. actuator) 
y mH 
Avalanche energy handling:  
E=1/2*(Ioh_max * L_load_max) 
mJ 
Ground Offset Voltage 
1.0 V 
Output protected for shorts to: 
Gnd and Vbatt 
Over Current Shut-off 
Allowed 
ESD Protection – Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
 


### 第 71 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 71 of 74
 
A12.2 High Side Drive PWM Outputs. 
R_load
L_load
10n
D1*
D*
Io
-
+
+
-
Vs
Power_gnd
Uo
Usat
* For high current loads
D1 is normally replaced by a
syncronous rectifier
PWM controlled load
pwm control
*
*
*
*
* = Complete transient protection will depend on GMW3097 results
D* = Reverse polarity protection mechanization Uoh_min dependent
 
 
OPHS_x 
Name(s) 
OPHS_x 
Type 
High side drive PWM output 
Output power source 
Vs: Vbatt or Vign 
Uoh_min - Max voltage allowed in the active state  
Vs - 1.0 V @ Iol_max 
Ioh_min - Min current required in the active state  
-x A 
Ioh_max - Max current allowed in the active state 
-y A  
Ileak_max - Max leakage current allowed in the Inactive state 
10 µA  
Isurge - Max surge current allowed in the active state 
10 * Ioh_max 
R_load_min @ -40°C 
x Ω 
L_load_max @ -40°C (activated if mech. actuator) 
y mH 
Avalanche energy handling:  
E=1/2*(Ioh_max * L_load_max) 
mJ 
Output frequency 
x Hz +/-1% (<400Hz load via harness, <25kHz 
driver integrated with load) 
Duty cycle 
0-100%* 
Output protected for shorts to: 
Gnd and Vbatt 
Over Current Shut-off 
Allowed 
ESD Protection - Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
* The output duty cycle of 0 to 100% excludes use of N-channel high side transistor with boot-strap gate 
drivers. To use this type of high side N channel transistor and gate drive a duty cycle limit of 5 to 95% applies. 


### 第 72 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 72 of 74 
October 2005
 
A13 H-Bridge Drivers. 
U1_h
U1_l
U2_h
U2_l
L_load
C1
D1
R_load
Io
Io
D*
Co
Co
Vs
Vs_prot
Power_gnd
+
-
+
-
signal_gnd
Uo
Uo
Load
D* = Reverse polarity protection mechanization Vmin dependent
D1/C1 Designed to prevent Vs_prot overvoltage in any drive condition on/off/brake
*
 
Electrical Characteristics 
Parameter 
Symbol 
Condition 
min 
typ 
max 
Unit 
Remarks 
Supply voltage 
Vs_max 
Maximum rating 
-13.5 
 
+26.5 
V 
No damage 
Supply voltage 
Vs 
Operating range  
Vmin 
Vnom 
Vmax 
V 
 
 
 
 
 
 
 
 
 
Uoh_min 
 
Tmin to Tmax,  
Vmin to Vmax 
Vs - 1 
 
Vs 
V 
@Ioh_max 
Uol_max 
 
Tmin to Tmax,  
Vmin to Vmax 
0 
 
1.0 
V 
@Iol_max 
 
 
 
 
 
 
 
 
Actuation frequency 
f_act 
 
0 
128 
400 
Hz 
Load 
connected 
via wire 
harness 
Actuation frequency 
f_act 
 
0 
 
25 
kHz 
Driver 
integrated to 
load 
Vmin, Vnom and Vmax defined by electrical code letter from the GMW3172 code string. 
*Input protection shall pass GMW3097 validation requirements and have a 0805 size ceramic capacitor land pad available at the 
connector pin 


### 第 73 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14082
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
October 2005 
Page 73 of 74
 
ODHB_x 
Name(s) 
ODHB_x 
Type 
H-bridge discrete output driver 
Output power source 
(Vs: Vbatt or Vign) / (Power_gnd) 
Uoh_min -Min voltage output required in the active high state  
Vs - 1.0 V @ Ioh_max 
Uol_max -Max voltage output required in the active low state  
1.0 V @ Iol_max 
Ioh_min - Min current required in the active state  
-x A 
Ioh_max - Max current allowed in the active state 
-y A  
Iol_min - Min current required in the active state  
-x A 
Iol_max - Max current allowed in the active state 
-y A  
Ileak_max - Max leakage current allowed in the Inactive state, 
Vs_prot to signal_gnd through the H-bridge 
10 µA  
Isurge - Max surge current allowed in the active state 
10 * Io_max , duration < 1s 
R_load_min @ -40 C 
x Ω 
L_load_max @ -40 C (activated if mech. actuator) 
y mH 
Avalanche energy handling:  
E=1/2*(Ioh_max * L_load_max) 
mJ 
Actuation frequency, max 
x Hz 
Actuation time, max if not continuous allowed 
y ms 
Outputs protected for shorts to: 
Gnd and Vbatt 
Over Current Shut-off 
Allowed 
ESD Protection - Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 
 


### 第 74 页
GMW14082 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 74 of 74 
October 2005
 
OPHB_x 
 
 
 
 
 
Name(s) 
OPHB_x 
Type 
H-bridge PWM output driver 
Output power source 
(Vs: Vbatt or Vign) / (Power_gnd) 
Uoh_min -Min voltage output required in the active high state  
Vs - 1.0 V @ Ioh_max 
Uol_max -Max voltage output required in the active low state  
1.0 V @ Iol_max 
Ioh_min - Min current required in the active state  
-x A 
Ioh_max - Max current allowed in the active state 
-y A  
Iol_min - Min current required in the active state  
-x A 
Iol_max - Max current allowed in the active state 
-y A  
Ileak_max - Max leakage current allowed in the Inactive state, 
Vs_prot to signal_gnd through the H-bridge 
10 µA  
Isurge - Max surge current allowed in the active state 
10 * Io_max 
R_load_min @ -40°C 
x Ω 
L_load_max @ -40°C (activated if mech. actuator) 
y mH 
Avalanche energy handling:  
E=1/2*(Ioh_max * L_load_max) 
mJ 
Output frequency 
x Hz ±1% (<400Hz load via harness, <25kHz 
driver integrated with load) 
Duty cycle 
0 - 100% 
Outputs protected for shorts to: 
Gnd and Vbatt 
Over Current Shut-off 
Allowed 
ESD Protection - Handling  
Shall meet GMW3097GS @ ≤ 8 kV 
ESD Protection – Powered 
Shall meet GMW3097GS @ ≤ 25 kV 

