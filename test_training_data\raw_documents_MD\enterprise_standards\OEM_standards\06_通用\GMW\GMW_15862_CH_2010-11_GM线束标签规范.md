# GMW_15862_CH_2010-11_GM线束标签规范.pdf

## 文档信息
- 标题：
- 作者：
- 页数：60

## 文档内容
### 第 1 页
通用全球工程标准 
GMW15862 
 
 
 
零件识别，验证和可追溯性的条形码内容，格式和标签要求  
 
 
目录 
1 引言 ......................................................................................................................................................................................3 
2 参考 ......................................................................................................................................................................................3 
2.3 附件参考文献。 .......................................................................................................................................................4 
3 需要条形码的零件，部件，组件或模块。 ......................................................................................................................4 
3.2 可追溯性。 ...............................................................................................................................................................4 
3.2.1 .........................................................................................................................................................................6 
3.2.2 跟踪记录。 ....................................................................................................................................................6 
3.2.4 数据字段。 ....................................................................................................................................................7 
3.3 产品标识。 ...............................................................................................................................................................8 
4 条形码符号和编码 ..............................................................................................................................................................9 
4.1.1 2D 的案例。 .................................................................................................................................................9 
4.1.3 数据字段和数据标识符。 ......................................................................................................................... 10 
4.3.2 ...................................................................................................................................................................... 12 
4.4 纠错级别。 ............................................................................................................................................................ 12 
4.4.2 QR 码纠错（EC）级别。 ........................................................................................................................... 13 
4.5 数据矩阵和 QR 码数据格式。 ............................................................................................................................. 13 
4.5.1 数据语法 ISO/IEC 15434. ............................................................................................................................ 13 
4.5.2 合规标题. .................................................................................................................................................... 13 
4.5.3 数据矩阵标题和追踪 06 宏 BIG RULE：只要需要编码多个数据字段，就使用 06 宏。 06 宏在 ISO / 
IEC 15434 数据语法标准的编码中保存了 8 个字母数字字符。 ..................................................................... 13 
4.6 单数据字段编码。 ................................................................................................................................................ 15 
4.7 矩形数据矩阵。 .................................................................................................................................................... 15 
4.7.1 曲线曲面。 ................................................................................................................................................. 15 
4.8 远程扫描。 ............................................................................................................................................................ 16 
 
4.9 VIN。 ............................................................................................................................................................... 16 
4.11.3 轮胎锥度。 ............................................................................................................................................... 18 
4.11.4 数据语法要求。 ....................................................................................................................................... 18 
4.11.5 标签布局。 ............................................................................................................................................... 18 
5 标签要求 ........................................................................................................................................................................... 20 
5.3 标签尺寸。 ............................................................................................................................................................ 22 
5.4 颜色。 .................................................................................................................................................................... 22 
5.5 验证/错误校对. ..................................................................................................................................................... 23 
5.6 打印。 .................................................................................................................................................................... 23 
5.8 供应商标识或商标。 ............................................................................................................................................ 27 
6.1.1 代码 128 和代码 39 的代码密度和尺寸。 ............................................................................................... 29 
6.5 代码 128 和代码 39 校验位。 .............................................................................................................................. 31 
6.7.2 数据容量。 ................................................................................................................................................. 31 
 
 
内部通用规范 
 
 
 
GMW15862 
全球 
工程 
标准 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 


### 第 2 页
通用全球工程标准 
GMW15862 
 
7 直接零件标记（DPM） ................................................................................................................................................... 33 
7.4.1 划线。 ......................................................................................................................................................... 33 
7.4.2 激光蚀刻和点刻。 ..................................................................................................................................... 34 
7.4.3 激光。 ......................................................................................................................................................... 35 
7.4.4 喷墨。 ......................................................................................................................................................... 37 
9 其他产品特性 ................................................................................................................................................................... 40 
9.3 竣工标签。 ............................................................................................................................................................ 42 
10 射频识别（RFID） ....................................................................................................................................................... 42 
13 编码系统 ......................................................................................................................................................................... 49 
14 版本和修订 .................................................................................................................................................................... 49 
附录 B: 儒略历 ............................................................................................................................................................. 51 
 
 ....................................................................................................................... 60 
 


### 第 3 页
通用全球工程标准 
GMW15862 
 
 
1 引言 
注意：本标准中没有任何内容可取代适用的法律法规。 
注意：如果英语和母语之间存在冲突，则应优先使用英语。 
注意：在本文档中，单词 shall 是要求，单词 should 是推荐。 
1.1 范围。 
该标准定义了 GM 车辆制造中使用的零件，部件，组件和模块的条形码符号，数据内容和标签/标
记布局。该标准还定义了印刷条形码标签和直接部件标记（DPM）的性能标准。本文件取代了
GM1737 和 EDS-A-2404，所有区域，所有工厂，以及所有供应商的独特要求。 
1.2 带线性条形码的新的或修订的零件，部件，组件和模块。 
第 6 节线性一维（1D）条形码不得用于新的/修订的扫描零件，部件，组件或模块。 
1.3 新的源零件，部件，组件和模块。 
所有新零件，部件，组件或模块均应符合本文档的第 4 节和第 5 节（请参阅可追溯性行动通知
（NOA））。 
1.4 残留零件，部件，组件和模块。 
第 6 节，线性 1D 条形码允许在残留扫描的零件，部件，组件或模块上使用（参见可追溯性
NOA）。 
1.5 控制模块软件 
必须符合 GMW4710。新的数据结构将从全球 B 架构开始生效（参见可追溯性 NOA）。标签或
DPM 应符合本文档。 
1.6 条形码扫描的原因。 
1.6.1 可追溯性。 
主要目的是精确识别泄漏或潜在现场行动中涉及的车辆（VIN /跟踪号）。产品工程负责定义哪些
零件，部件，组件和模块需要可追溯性。 
1.6.2 验证/错误校对。 
提供验证正确零件/部件/模块/组件的功能。 
1.6.3 部件识别。 
提供识别零件/部件/模块/组件的功能。 
2 参考 
注意：除非另有说明，否则仅适用最新批准的标准。 
2.1 外部标准/规范。 
AIAG-B-2 
AIAG B-4 
ANSI MH10.8.2 
ANSI X12.3 
ISO/IEC 15416 
ISO/IEC 15417 
ISO/IEC 16388 
ISO/IEC 18000-6C 
AIAG B-7 
FMVSR 49 CFR § 574.5 
ISO/IEC 15418 
ISO/IEC 18004 
AIAG B-11 
ISO/IEC 646 
ISO/IEC 15434 
NASA-STD-6002 
AIAG B-17 
ISO/IEC 15415 
ISO/IEC 16022 
 
注意：AIAG 标准可从 www.aiag.org 获取。 


### 第 4 页
通用全球工程标准 
GMW15862 
 
注意：ANSI MH10.8.2 标准经常更新，并在 www.autoid.org 上作为 DRAFT 文档进行维护。 
2.2 GM 标准/规范。 
GMW4710 
GMW14574 
GMW14089 
GMW15049 
GMW14573 
GMW16331 
 
2.3 附件参考文献。 
AIM DPM 质量指南（可从 www.aimglobal.org 获取） 
CG2503 
Dun 和 Bradstreet; 可从 http://www.dnb.com 获得 
全球质量要求（GQR）120.57 
标签和文献网站：http：//gmna1.gm.com/eng/labels/barcodes.html。 
可追溯性 NOA QLT / ELT 196A 
 
 
车辆分区和产品结构（VPPS）管理的压缩代码；可从
http://gmna1.gm.com/eng/grc/vpps/index.html 获取 
3 需要条形码的零件，部件，组件或模块。 
3.1 根据 GMW15049 关键特性指定系统（KCDS）确定为需要可追溯性或验证的零件，部件，组件
和模块应编码为数据矩阵或可选的快速响应（QR）代码二维（2D）符号（条形码）。 
3.1.1 流程所有者对条形码要求沟通的责任。 见表 1 中的指南。 
表 1：沟通责任指南 
 
所有者 
触发器 
方法 
可追溯性 
KCDS 
模板 
注释 1 
注释 2 
TRA 
代码在 GPDS 
验证-工程 
KCDS 
模板 
注释 3 
VER 
代码在 GPDS 
验证-防错 
注释 4 
ME 
注释 5 
PFMEA 
注释 6 
PRTS 
零件 ID 
注释 7 
PE 
注释 8 
DFMEA 
注释 9 
SOR 
注释 10 
VPPS 
KCDS 
模板 
SOR 
注 1：TRA - 可追溯性 
注 2：GPDS - 全球产品描述系统 
注 3：VER - 验证 
注 4：ME - 制造工程 
注 5：PFMEA - 过程失效模式和影响分析 
注 6：PRTS - 问题报告和跟踪系统 
注 7：PE - 过程工程 
注 8：DFMEA - 设计失效模式影响分析 
注 9：SOR - 要求声明 
注 10：VPPS - 车辆分区和产品结构。 见附录 C. 
 
3.2 可追溯性。 有时也被称为家谱，历史或出生记录。 可追溯性必须回答以下四个最低限度
问题： 
什么？ 
谁？ 
哪个？ 
什么时候？ 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 


### 第 5 页
通用全球工程标准 
GMW15862 
 
请参见图 1 带有序列号的可追溯性示例和图 2 带有标签/标记的批量/批次 ID 的可追溯性示例。 
 
图 1：序列化可追溯性标签/标记的图示 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
6/ 59 


### 第 6 页
通用全球工程标准 
GMW15862 
 
 
 
 
图 2：批量/批次可追溯性标签/标记的图示 
3.2.1 在本标准中，这四个问题的答案如下： 
“what”是 GM 分配的 8 位数零件号。 
“who”是制造或装配现场的数据通用编号系统（DUNS）标识。 
“which”是 3.2.3 中定义的 GM 定义的可跟踪性代码结构。 
“when”是制造或装配的实际年份和儒略日期（附录 B），并包含在 GM 定义的可追溯性代码
结构中。 
3.2.2 跟踪记录。 要构成跟踪记录，可追溯性要求 GM 分配 14 个字符的字符压缩 VPPS 代
码，8 个字符的部件号，制造或汇编源站点的 9 个字符的 DUNS ID，以及 16 个字符的 GM 定义的
跟踪代码。 
3.2.3 GM 定义的可追溯性代码结构。 GM 定义的可追溯性代码结构（附录 E）是
16 个数据字符，不包括数据标识符（DI），如图 3 所示。有关常见 DI 的列表，请参阅附录 A. 
 
图 3：GM 定义的跟踪序列号或批量/批次识别结构 
此处： 
L 是供应商分配的生产线/机器/试验台标识。 
S 是供应商分配的班次识别。 
YY 是实际生产/装配年度的最后两位数（例如 2007 = 07），而不是型号年份。 
DDD 是生产/组装年度的儒略实际日期（例子 282 = 09OCT）（见附录 B）。 
对于序列号 - A2B4C6000 是供应商分配的唯一序列号（带有零的 9 个字符右键），或 
对于批次/批次 - @ 2B4C6000 是“@”是固定字符的格式，“2B4CD68E”是供应商分配
批次/批次代码（8 个字符右边用零填充）。 
3.2.3.1 对于动力总成发布部件，最后九个字符是根据附录 J 中的布局定义的。 
©版权所有 2010 通用汽车公司保留所有权利 
 
 
2010 年 11 月 
7/59 


### 第 7 页
通用全球工程标准 
GMW15862 
 
 
3.2.4 数据字段。 条形码应包含四个数据字段（表 2）及其关联的数据标识符（DI），
如图所示。 
表 2：条形码数据字段
注释 1
 
 
数据 
定义 
数据 
特征 
 
DI 
 
编码（带 DI） 
GM 分配了 VPPS 压缩代码 
14 字母数字 
Y 
Y0000000000000X 
GM 部
件号 
 
8 数字 
 
P 
 
P12345678 
制造或装配现场 DUNS 
 
9 数字 
 
12V 
 
12V987654321 
 
 
 
TLSYYDDDA2B4C6D8E or 
通用汽车定义 
跟踪代码或批量/批次代码 
 
16 字母数字 
 
T 
TLSYYDDD@2B4C6000 
or 
有关动力总成发布的部件，请参见附录 
附录 J. 
 
 
 
J. 
注 1：制造或装配儒略日期包含在 GM 定义的跟踪代码中。见附录 B 儒略历。 
3.2.5 BIG RULE：应使用数据标识符。 
3.2.5.1 对于所有条形码，包括那些不包含可能在零件，组件或模块上的 GM 数据的条形码。如果
条形码可见，则可能会错误地扫描。 
3.2.5.2 要识别 1D 或 2D 条形码中包含的单个数据元素，不得使用 ISO / IEC 15434 数据语法标准。 
3.2.5.3 要识别 2D 符号中包含的多个单独数据字段，应使用 ISO / IEC 15434 数据语法标准。 
3.3 验证/错误校对。由制造工程师通过过程失效模式和影响分析（PFMEA）识别的对选择
错误敏感的零件，部件，组件和模块应该需要条形码。错误校对制造工程师将启动变更请求（CR），
以便与产品工程部门沟通添加条形码的需要。图 4 展示了验证/错误校对标签。 
 
图 4：验证/错误校对标签/标记的图示 
注意：如果未使用 GM 定义的可追溯性，则应使用制造或装配儒略日期，如表 3 所示。 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
8/59 


### 第 8 页
通用全球工程标准 
GMW15862 
 
 
 
表 3：验证/错误校对
注释 1
 
 
数据定义 
数据特征 
DI 
编码（带 DI） 
GM 分配了 VPPS 压缩
代码 
14 字母数字 
Y 
Y0000000000000X 
GM 部件
号 
 
8 数字 
 
P 
 
P12345678 
制造或装配现场 DUNS 
9 数字 
12V 
12V987654321 
制造或装配的儒略日期 
5 数字 
4D 
4DYYDDD 
 
可选的 
通用定义 
跟踪代码或批量/批次代码 
 
 
16 字母数字 
 
 
T 
TLSYYDDDA2B4C6D8E or 
TLSYYDDD@2B4C6000 or 
有关动力总成发布的组件，请参
见附录 J. 
 注释 1：儒略制造日期或装配日期包含在 GM 定义的跟踪代码中。见附录 B 儒略日历。 
3.3 产品标识。产品标识标签或 DPM 由与可追溯性相同的数据字段（表 4）组成，GM 定义
的可追溯性数据是可选的。图 5 示出了产品标识标签。 
表 4：产品标识
注释 1
 
 
数据定义 
数据特性 
DI 
编码（带 DI） 
GM 分配了
VPPS 压缩代码 
14 字母数字 
Y 
Y0000000000000X 
GM 部件号 
8 数字 
P 
P12345678 
制造或装配现场
DUNS 
 
9 数字 
 
12V 
 
12V987654321 
制造或装配的儒略日期 
 
5 数字 
 
4D 
 
4DYYDDD 
 
 
 
TLSYYDDDA2B4C6D8E or 
 
可选的 GM 定义的
跟踪代码 
 
16 字母数字 
 
T 
TLSYYDDD@2B4C6000 
or 
有关动力总成发布的组件，请参见附录
J. 
 
 
 
J. 
注 1：制造或装配的儒略日期包含在 GM 定义的跟踪代码中。 见附录 B 儒略历。 如果未使用 GM 定
义的可追溯性，则应使用制造或装配儒略日期。 
 
图 5：产品标识标签/标记的图示 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
   9/59 
 


### 第 9 页
通用全球工程标准 
GMW15862 
 
 
4 条形码符号和编码 
4.1 条形码符号。 本节中的信息适用于本标准中推荐的二维（2D）条形码符号。 数据矩阵（首选）
ISO / IEC 16022，符号规范（附录 D）或与贸易伙伴/ GM 批准 QR 码。 应使用 ISO / IEC 18004，符号规范（图
6）。 
 
图 6：二维（2D）符号数据矩阵（首选）和 QR 码 
4.1.1 2D 的案例。 
消耗更少的空间（降低标签成本）。 
内置纠错（100％数据恢复，符号损坏 15％至 20％）。 
大数据容量（可能有数百个数据字符）。 
可印在标签上或直接标记上（激光蚀刻，带图章，喷墨等） 
扫描设备是一个成像仪（基于摄像头的技术）没有移动部件。 
成像仪比基于激光的技术成本更低。 
与 ISO / IEC 18000-6C 无源射频识别（RFID）技术的互换性，具有用户存储器，相同的数据语法结构。 使用
AIAG B-11，数据看起来与信息技术（IT）系统相同。 
4.1.2 允许的数据字符。 本标准的 ISO / IEC 646 美国信息交换标准码（ASCII）字符集应包括
以下内容： 
大写字母字符 
数字 0 到 9 
短划线（ - ） 
期间（。） 
下划线（_） 
空间（） 
注意：不推荐 ASCII 字符$符号（$）、前斜杠（/）、加号（+）和百分比（%）与代码 39 一起使用，因此在
数据字段中应该避免，这些数据字段可以被编码在线性 1D 和 2D 符号中。此建议基于这些特定字符的代码 
39 字符替换错误的可能性。 
4.1.1.1 完整的 ASCII 字符集不能用于数据。 
4.1.1.2 消息标题，消息预告片和字段分隔符中允许使用完整的 ASCII 字符集，如 ISO / IEC 15434 针对高容
量介质（数据语法）所定义。 这些特定的 ASCII 字符被称为“不可打印的控制字符”，并且需要不同的编码
技术，这取决于所使用的软件和打印机（表 5）。 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
10/59 


### 第 10 页
通用全球工程标准 
GMW15862 
 
 
表 5：ISO / IEC 15434 中使用的 ASCII ISO / IEC 646 字符 - 数据语法结构 
被称为 
ASCII ISO/IEC 646 特性 
十进制 
Hex 
[（左括号） 
[ 
91 
5B 
）（右括号） 
) 
41 
29 
>（大于） 
> 
62 
3E 
以下是不可打印的 ASCII 控制字符 
传输结束 
EOT 
04 
04 
组分隔符（数据
字段分隔符） 
G 
S 
29 
1D 
记录分隔符 
R 
S 
30 
1E 
 
4.1.3 数据字段和数据标识符。 数据字段应由数据标识符（DI）和相关数据组成。 应
使用符合 ISO / IEC 15418（ANSI MH 10.8.2）的数据标识符（DI）。 除非受此标准的限制，否则所有
数据均可变长。 使用时，表 6 中的字段不应超过显示的长度。 
表 6：受限长度数据字段 
数据标识符 DI 
说明 
最大数据长度 
最大总磁场长度 
12V 
制造或装配现场 DUNS 
9 
12 
4D 
以 YYDDD 形式的制造或装配儒略日期 
 
5 
 
7 
I 
车辆识别号码（VIN） 
17 
18 
P 
GM-指派的零件编号 
8 
9 
S 
产品序列号 
9 
10 
T 
GM 指派的可追溯性代码 
16 
17 
20P 
GM 指派的验证/防错代码 
6 
9 
20T 
GM 指派的的验证和可追溯性代码 
16 
19 
21T 
GM 指派的的验证和可追溯性代码 
16 
19 
Y 
通用压缩 VPPS 编码 
14 
15 
 
4.1.3.1 2D 符号系统，数据矩阵或 QR 码，可能包含多个数据字段。当表 6 中的字段以 2D 符号系
统编码时，它们不应超过最大字符长度。 
4.2 数据矩阵和 QR 码密度以及尺寸。 2D 符号密度（大小）由许多因素决定，包
括可用的标记区域，用于创建标记的方法，表面类型，环境和使用的成像装置。 
4.2.1 特定符号大小取决于编码数据的数量和类型，元素/单元大小和纠错级别。 
4.2.2 在可用标记区域的限制范围内，GM 应与供应商同意使用的 2D 元素/单元尺寸。 
4.2.3 为了获得最佳的成像器性能，请使用适合可用区域的最大实际尺寸元件/单元尺寸。 
4.2.4 BIG RULE：使条形码（1D 或 2D）符号尽可能大而实用，而非
尽可能小。随着符号元素/单元尺寸的减小，打印/标记和扫描/成像问题呈指数增长。 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
11/59 
 


### 第 11 页
通用全球工程标准 
GMW15862 
 
 
4.2.4.1 根据可用面积，印刷标签的元件/单元尺寸应为 0.51 毫米（0.02 英寸）或更大，并且不得小于 0.381 毫
米（0.015 英寸）。 
注意：如果空间限制要求较小的元素/单元格大小，则必须由 GM 和供应商同意并使用 GM 指定的成像器进行测试
4.3 数据矩阵和 QR 码静区。 应包括整个周边（四边）的静区。 不遵守最低要求可能导致不可解码
的符号。 
4.3.1 数据矩阵静区等于符号单元维的两倍（图 7）。 
 
图 7：数据矩阵的静区要求 
4.3.2 QR 码静区等于符号单元尺寸的四（4）倍（图 8）。 
 
图 8：QR 码的静区要求 
4.4 纠错级别。 
4.4.1 数据矩阵纠错码（ECC）级别。 ECC 200 应用于打印标签和直接部件标记（DPM）。 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
       12/59 


### 第 12 页
通用全球工程标准 
GMW15862 
 
S 
 
 
 
4.4.2 QR 码纠错（EC）级别。 纠错级别 M 建议在此标准中使用（表 7）。 
表 7：QR 码的纠错级别 
EC 水平 
% EC 
说明 
L 
7% 
最小的符号大小。 需要高水平的打印/标记质量 
M 
15% 
推荐的 
小尺寸和误差修正（EC）水平之间的良好折衷 
Q 
25% 
适用于提供高水平 EC 的关键或差的印刷/标记质量应用 
H 
30% 
最大 
 
4.5 数据矩阵和 QR 码数据格式。 
BIG RULE：当要在 2D 符号中编码多个数据字段时，它们应按照 ISO / IEC 15434（数据语法）的定
义进行格式化，并使用格式 06（ANSI MH 10.8.2 数据标识符）。 当要在 2D 符号中编码单个数据
字段时，它应仅使用 ANSI MH 10.8.2 数据标识符，并且 SHALL 将编码数据作为前缀。 
4.5.1 数据语法 ISO/IEC 15434. 
合规标题[）> R S后面必须是格式标题 06G
S. 
除最后一个数据字段外，每个数据字段必须由 G
S。 
格式包络中的最后一个数据字段应为格式追踪 R
S. 
消息中的最后一个格式包络应该是消息追踪 EOT。 
 编码数据格式如下所示： 
[)>
R  06
G  Y0000000000000X
G  P12345678
G  12V987654321
G  TLSYYDDDA2B4C6000 
EO 
S 
S 
S 
S 
S 
S 
T. 
4.5.2 合规标题. 合规性格式标题 06 要求为每个数据字段使用数据标识符（DI）。将数
据字段分隔符定义为 G
S（表 8）。 
表 8：数据语法格式和 DI 
注释 1
 
 
标题 
DI 
信息内容 
数据字段
分隔符 
追踪者 
[)>R  06G 
S 
S 
Y 
GM 定义的 VPPS 
0000000000000X 
G 
S 
 
 
P 
GM 定义的零件号
12345678 
G 
S 
 
 
 
12V 
制造或装配现场 DUNS 编号 987654321  
G 
S 
 
 
 
T 
LSYYDDDA2B4C6D8E or 
LSYYDDD@2B4C6000 or 
有关动力总成发布的组件，请参见
附录 J. 
 
 
R  EO 
S 
T 
注 1：单个数据字段应使用适当的数据标识符，后跟其数据。 
 
4.5.3 数据矩阵标题和追踪 06 宏 BIG RULE：只要需要编码多个数据
字段，就使用 06 宏。 06 宏在 ISO / IEC 15434 数据语法标准的编码
中保存了 8 个字母数字字符。数据矩阵 ISO / IEC 18004 提供了将标题和尾部缩写为一
个字符的方法。 创建此功能是为了减少使用 ISO / IEC 15434 数据语法标准对符号中的数据进行编码
所需的符号字符数。 06 宏字符仅在第一个符号字符位置时适用。 标题将作为前缀发送到数据流，
并且追踪将作为后缀发送到数据流（表 9）。 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
13/59 


### 第 13 页
通用全球工程标准 
GMW15862 
 
 
表 9：数据矩阵的宏功能 
宏代 
码字 
 
名称 
 
解释 
 
 
标题 
追踪者 
237 
06 宏 
[)>
R  06
G
 
S 
S 
R  EO 
S 
T 
 
4.6 单数据字段编码。 当仅在数据矩阵或 QR 码中编码单个数据元素（例如，车辆识别号（VIN））
时，应使用 ANSI MH 10.8.2 数据标识符（图 9）。 
DI 应该是数据之前的第一个字符。 没有标题或追踪。 
如果人类可读信息（HRI）要包含 DI，则应将其括在括号（DI）xxxdataxxx 中。 
注意：括号不应在 2D 符号中编码。 
 
图 9：带有和不带数据标识符（HRI）的单个数据元素编码示例（17 个字符 VIN） 
4.7 矩形数据矩阵。 虽然方形符号更有效，但是当可用空间不能容纳正方形时，可能会生成矩形符
号，特别是当零件是圆柱形时（图 10）。 
 
 
图 10：矩形数据矩阵的示例 
4.7.1 曲线曲面。 对于标签/标记和读取，平面表面优于弯曲表面。 项目的曲率可能会禁止正确的标
签或标记，并可能使代码失真到无法解码的程度。 如果标签或标记位于圆形/曲面上，则符号高度应小于零
件直径的 16％（图 11）。 
 
 
图 11：曲面上的标签或 DPM 指南 
 
©2010 版权所有通用汽车公司保留所有权利 
 
2010 年 11 月 
       14/59 


### 第 14 页
通用全球工程标准 
GMW15862 
 
 
4.8 远程扫描。 数据矩阵和 QR 码代码是可扩展的。 通过增加单元/元件尺寸并使用适当的成
像器配置，可以获得 3 米的距离。 图 12 是使用当前全球制造和质量（GM＆Q）标准手持式成像仪
在 1.4 米（4.5 英尺）处扫描使用 3 毫米（0.120 英寸）元件/单元测量 70.3×70.3 毫米的数据矩阵符
号的示例。 
 
注意：此数据矩阵符号使用 GM 标准手持式成像仪在 1.4 米处扫描。 
图 12：缩放单元尺寸（3 mm） 
 4.9 VIN。参考 GMW14574 用于 VIN 规范，AIAG B-7 车辆排放配置标签标准。标签，AIAG 
B-2 车辆 ID 号（VIN）标签应用标准和 DPM 条形码符号应符合 GMW15862 中的要求。 VIN 通常
是独立的条形码，例如 VIN 板或发射标签。 （有关示例，请参见图 9）。 
4.10 标签电子模块。参考 GMW4710，用于编写具有可追溯性信息的电子模块的方法。
电子模块的外部标签应符合 3.2 中详述的可追溯性结构。 
4.11 轮胎标签要求。 AIAG B-11 标准为轮胎和车轮识别条形码标签的打印和放置以及
读/写射频识别（RFID）标签提供了指导。该标准旨在帮助轮胎和车轮信息的自动收集以及轮胎
和车轮在 GM 环境中的安装和装配过程。该标准提供了有关制造商，轮胎和车轮尺寸，类型以
及本标准中概述的以及供应商和 GM 同意的其他可选信息的信息。 
4.11.1 该轮胎和车轮应用标准基于 AIAG B-4 零件识别和跟踪标准，附加信息特定于轮胎和车
轮识别条形码标签和 RFID 标签的打印，编程和放置。 
4.11.2 轮胎批次可追溯性识别。识别轮胎时，数据字段应包括数据标识符“21S”，后跟完
整的运输部（DOT）代码（联邦机动车辆安全规则（FMVSR）49CFR§574.5），这是一个 12 字
符的编码结构由 DOT 定义如下（图 13）： 
前两个字符按工厂定义制造商。 
字符 3 和 4 表示轮胎尺寸。 字符 3 和 4 也可以由轮胎制造商定义。 
对于轮胎制造商，字符 5,6,7 和 8 是可选的。 如果轮胎制造商使用 3 位选项代码，则应使用前
导“下划线”字符（5F HEX 或 95 DEC）填充。 选项代码的定义由轮胎原始设备制造商（OEM）
和 GM 决定。 
字符 9,10,11 和 12 是制造日期（2 位数周/ 2 位数年份）。 
©2010 通用汽车公司版权所有。保留所有权利 
 
2010 年 11 月 
15/59 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 


### 第 15 页
通用全球工程标准 
GMW15862 
 
 
 
 
 
图 13：DOT 轮胎批次可追溯性数据结构 
4.11.3 轮胎锥度。 GM 轮胎工程集团可能会根据称为“锥度”的工程值对轮胎进行分类（表 10）。 分配
给锥度并由轮胎工程选择的 DI 为 5N01。 “5Nxx”DI 组分配给 AIAG，并在 http://www.aiag.org 上进行管理和发
布。 
表 10：与 DI 5N01 一起使用的锥度值 
合格者 
定义 
A 
无分裂 
B 
+ 
C 
- 
D 
低 + 
E 
Hi + 
F 
低 - 
G 
Hi - 
H 
暂无 
 
4.11.4 数据语法要求。轮胎 2D 条形码中编码的数据应如表 11 所示。 
表 11：数据语法条形码加密注释 1 
 
标题 
DI 
信息内容 
数据字段
分隔符 
追踪 
[)>
R  06
G
 
S 
S 
Y 
GM 定义的 VPPS 
00000000000000 
 
G 
S 
 
 
P 
GM 定义的零件编号 12345678 
G 
S 
 
 
12V 
制造或装配现场 DUN 编号
987654321 
G 
S 
 
 
21S 
DOT 定义的跟踪代码示例
W2CU_XLT2508 
G 
S 
 
 
5N01 
锥度值示例 B 
G 
S 
R EOT 
S 
注 1：编码数据格式如下：[）> RS06G P12345678GS12V987654321GS21SW2CU_XLT2508GS5N01B RS 
EOT 
 
4.11.5 标签布局。 轮胎标签应符合第 5 节中详述的打印质量和字体规则。条形码单元/元件
应至少为 0.51 毫米（0.02 英寸）。 标签布局应该符合图 14。 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
16/59 
 
 


### 第 16 页
通用全球工程标准 
GMW15862 
 
 
 
 
图 14：轮胎标签布局（标签尺寸约为 27.9 x 27.9 毫米（1.1 x 1.1 英寸） 
 
5 标签要求 
5.1 标签要求。 本节定义仅包含条形码信息的零件/组件标签的标签要求。 对于包含其他信
息的标签，除了本标准中的标签外，标签设计还需要满足 GMW14089 的设计要求。 
组件/组件安装后，客户不应看到标签。 
5.2 标签详解。 图 15 显示了可在 Traceability Label / Mark 上显示的功能示例。 
 
图 15：标签/标记的详解 
特征： 
白色标签上的黑色打印。 
2D 条形码（数据矩阵或 QR 码） 
HRI 应为大写（大写字母）Arial Narrow BOLD 或 Helvetica Condensed 或等效。 
使用较大字体 12345678 强调最后四位数的部件号。 
GM 定义了车辆分区和产品结构（VPPS）。 
DUNS 识别制造或装配现场。 
制造或装配儒略日期。 
GM 定义了跟踪代码。 
规定使用图形指标; 例如，左/右手部分，颜色替代，例如，¥= RED; T =黄色; ¥=绿色; 等等 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
17/59 


### 第 17 页
通用全球工程标准 
GMW15862 
 
 
5.3 标签尺寸。 标签尺寸由可用区域和形状（例如，曲面）决定。 标签的可用区域将决定条
形码的大小，标签和 HRI 字体大小。 
注意：本文档中的标签示例仅供参考（图 16 至 20）。 
 
图 16：具有多种语言的转向柱安全气囊模切标签示例 
 
注意：多个数据矩阵符号指出了零件上每个条形码为何符合 DI 和数据语法标准的原因。 
图 17：车顶安全气囊传感器 
5.4 颜色。 使用白色背景和黑色打印或黑色背景和白色打印（反转图像）实现最佳成像器/扫描
仪性能。 
颜色问题： 
颜色增加了成本。 
10％以上的男性人口患有色觉障碍，大多数人都没有意识到这一点。 
降低条形码对比度，最大限度地减少扫描距离，或者更差地扫描。 
文本对比度可读性降低 - 使用户感到沮丧。 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
18/59 
 


### 第 18 页
通用全球工程标准 
GMW15862 
 
 
易于破碎的过程。 如果正确的颜色标签不可用，你不发货吗？ 
需要更改打印机库存或设置多台打印机。 
增加库存物料的成本。 
引入可能的错误（例如，选择了错误的标签颜色库存）。 
颜色规格。 你想要什么样的绿色，蓝色，黄色，白色或黑色？ 
5.4.1 颜色的替代方案是使用图形，例如钻石，心形，铲形，条纹等。在某些情况下（例如，电
缆），可能需要使用颜色，并且应该使用颜色条纹与防洪涂料，以避免产生扫描和人类阅读问题。 
 
图 18：使用图形（Sunbursts）代替颜色 
 
图 19：使用图形（三叶草）和标记上的位置以防止色彩错误 
 
 
图 20：背景对条形码和 HRI 的影响图示（不允许颜色） 
5.5 验证/错误校对. 如果操作员存在错误选择的风险，则需要对具有相似外观的部件进
行特殊识别（例如，L，R，
T，¥，¥）。 为了避免语言翻译的需要，不应该使用单词（图
21）。 注意以前对颜色的关注（5.4）。 
 
图 21：使用图形进行防错的示例（左手部分） 
5.6 打印。 使用热转印打印机进行打印时应使用树脂或树脂/蜡化合物基带。 应避免使用蜡基色
带，原因包括涂抹，耐刮擦和溶剂。 印刷标签应符合 GM 扫描点的最低 C 级，详见第 8 节。 
5.7 人类可读内容。 
5.7.1 数据矩阵和 QR 码的人类可读信息。 因为 2D 符号能够编码数百个数据字符，
所以数据字符的 HRI 可能不实用。 作为替代方案，符号可以伴随描述性文本而不是文字文本。 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
19/59 


### 第 19 页
通用全球工程标准 
GMW15862 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
5.7.1.1 消息的 HRI 可以打印在符号周围的任何区域，但不应干扰符号或静区。 
5.7.1.2 数据标识符不应出现在 HRI 中。数据标识符应在条形码中编码。 
5.7.1.3 消息标题，数据字段分隔符和消息预告字符不应出现在 HRI 中。 
5.7.1.4 HRI 应与 2D 符号相邻，并且应在任何零件或单元包上保持一致。 
5.7.1.5 数据矩阵和 QR 码的符号布局。 GM 和供应商应该构建一个最适合零件，部件，装配或模
块的布局。但是，应该注意的是，对于单个零件标记，符号的位置和方向对于使用自动固定安装扫
描仪的应用可能是至关重要的。本文件中所示的实例仅用于说明而不应被解释为说明书。 
5.7.1.6 字体规范。 
5.7.1.6.1 BIG RULE：使字体尽可能大而实用;而非尽可能小。随着字体大小的减小，
打印/标记和人类阅读问题呈指数级增长。 
5.7.1.6.2 字体应为大写字母（大写字母）Arial Narrow BOLD 或 Helvetica Condensed 或等效字母。
所有对字体类型和大小的引用都基于 MS Office 字体，仅供参考。字体大小基于称为点（pt）的系统。 
5.7.1.6.3 根据可读性和空间效率选择这些参考字体。各种打印技术和 DPM 标记设备使用的实际字
体差异很大。 
注意：表 12 和表 13 是指南，受打印/标记的可用区域和用于创建打印/标记的技术的影响。 
表 12：字体大小的图示 
MS Office 
Pt Size 
样例结果 
Arial Narrow Bold 
8 
A2B4C6D8 
10 
A2B4C6D8 
12 
A2B4C6D8 
14 
A2B4C6D8 
16 
A2B4C6D8 
18 
A2B4C68 
20 
A2B4C6D8 
24 
A2B4C6D8 
5.7.1.7 增强的 GM 部件号文本。 为了便于快速进行 HRI，产品识别和/或防错，GM 分配的
部件号的后四位应以更大的字体大小打印/标记，如表 13 所示。 
 
 
 
 
 
 
 
 
 
 
© 版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
20/59 


### 第 20 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
Licensee=TRW loc 14 Dusseldorf Germany/**********
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
表 13：增强型 GM 零件编号字体指南 
MS Office 
Pt Size 
前 4 数字/后 4 数字 
 
样例 
Arial Narrow Bold 
8/14 
12345678 
12/18 
12345678 
14/24 
12345678 
 
18/28 
12345678 
 
5.8 供应商标识或商标。 供应商应参考 GMW16331 了解品牌要求和政策。 
5.9 扫描仪/成像仪。 转基因工厂中使用的手持式或固定式条形码扫描仪/成像仪应满足 GM＆
Q IT 标准中的要求并将其物料清单（BOM）包含在内。 
5.9.1 成像器能够通过使用可编码为特殊 2D 编程符号的脚本来编辑设备内的数据。 脚本可以添加，
删除，解析和/或修改数据，并且可以包括前缀/后缀字符。 
5.9.2 以下是一个简单脚本的示例（图 22）。 
注意：此编程与接口无关。 在扫描此配置条形码之前，必须为特定接口编程扫描仪：串行，通用串行
总线（USB）等。 此脚本基于 Honeywell Products 4800i / 4820i 成像器。 
 
图 22：示例 RS232 脚本 
这是编码的脚本，如下所示：SUFBK2990D; DFMBK30099999999FE32FE30FE54F7F503F100; 
DFMBK30099999999FE54F7F501F100。 
5.9.2.1 从代码 128，代码 3/9，数据矩阵和 QR 条形码中删除数据标识符，同时为所有符号添加 CR 后
缀。 
 5.9.2.1.1 这适用于 RS232 串口，键盘楔或 USB 键盘接口。 
6 线性 1D 条形码 
注意：通用汽车正在逐步淘汰一维条形码，并逐步推出新的零件版本。 见可追溯性 NOA。 
6.1 代码 128 和代码 39（图 23）。 对于线性（1D）符号，应使用 ISO / IEC 15417 条
形码符号规范 - 代码 128 或 ISO / IEC 16388 条形码符号规范 - 代码 39。 参考 AIAG B4 获取额外的线性 1D
条形码详细信息。 UCC / EAN Code 128 符号系统不得使用。 代码 128 优于代码 39，主要是因为空间效
率和内置校验位。 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
21/59 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 


### 第 21 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
Licensee=TRW loc 14 Dusseldorf Germany/**********
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
 
 
 
 
注 1：代码 128 和代码 39 不得用于直接部件标记。 
注 2：在给定相同数据和 X 维度的情况下，代码 128 通常比代码 39 短 25％。 
图 23：代码 128 和 39 
6.1.1 代码 128 和代码 39 的代码密度和尺寸。两种符号的条形高度可以变化，以适
应特定的应用要求。最小钢筋高度应为 6.4 毫米（0.25 英寸）或条形码长度的 15％，以较大者为准，包
括静区，并且不应超过 13 毫米（0.5 英寸）。 
6.2 代码 128 是一个四比率条形码，它是通过符号标准自动确定的。每
个代码 128 数据字符由 1X，2X，3X 和 4X 宽度（条形和空格）元素组成。对于每个代码 128 符号，1X 窄
元件的平均宽度应该在 0.191 mm（0.0075 in）到 0.382 mm（0.0150 in）的范围内。代码 128 有三种模式;
标签软件或打印机应确定使用哪种模式以及何时切换模式。 
从历史上看，人工干预会导致代码 128 空间效率进行次优化。 代码 128 的基本规范： 
X 尺寸（窄条）。 
符号高度。 
6.3 代码 39 是双比率条形码，并且应指定比率。代码 39 符号的重要参数是窄元
素（条形和间隔）的平均宽度以及宽元素与窄元素的平均比率。对于每个代码 39 符号，窄元件的平均宽
度应在 0.191 mm（0.0075 in）至 0.382 mm（0.0150 in）的范围内。宽元素与窄元素的比例应该是 3：1。
测得的比例应在 2.8：1 和 3：1 之间。 
注意：比率是最常见的规格误差。如果比率低于 2.8：1，扫描仪可能会错误地解码数据，从而导致字符
替换错误。 
代码 39 的基本规范： 
X 尺寸（窄条）。 
比例应在 2.8：1 到 3：1 的范围内。 
符号高度。 
6.4 代码 128 和代码 39 安静区域。 代码 128 和代码 39 符号的每个前导和尾随静区应该
是 6.4 毫米（0.25 英寸），并且应该是窄元件宽度的至少十（10）倍（图 24）。 
 
图 24：1D 安静区域和高度要求 
 
 
 
 
©2010 通用汽车公司版权所有。保留所有权利 
 
2010 年 11 月 
22/59 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 


### 第 22 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
Licensee=TRW loc 14 Dusseldorf Germany/**********
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
6.5 代码 128 和代码 39 校验位。 
6.5.1 代码 128.根据符号标准，包括内置校验位，作为停止字符前的
最后一个字符。校验位不会显示在 HRI 中，并且通常不由解码器/阅读器传输。 
6.5.2 代码 39.校验位不应用于代码 39 符号。 
6.6 代码 128 和代码 39 打印质量。 ISO / IEC 15416 条形码打印质量测试规范 - 线性
符号应用于确定代码 128 和代码 39 符号打印质量。在 GM 扫描点，最小符号等级应为 2.0 / 05/660，
其中： 
最低打印质量等级= 2.0（C）。 
测量孔径= 0.12 mm（0.005 in）。 
检查波长= 660nm（纳米）+ 10nm。 
上述符号质量和测量参数可确保在各种扫描环境中的可扫描性。 
注意：以前的 AIAG 标准规定了 900 nm 的检测波长，以适应现有的红外扫描仪。在大多数情况下，
900 nm 的顺应性是 660 nm 处的顺应性的指标。当出现差异时，测量应在 660 nm 处进行。 
6.7 Code 128 和 Code 39 数据格式和数据长度。 
6.7.1 数据格式。兼容符号中的数据应该包含适当的 ANSI MH10.8.2 数据标识符，后跟用户数
据。图 25 与在 GMI 零件，部件，组件或模块标签的 HRI 中不显示 DI 一致。 
 
图 25：VPPS 代码，部件号，DUNS 和 GM 定义的跟踪代码示例（代码 128） 
6.7.2 数据容量。 代码 128 或代码 39 符号不应超过 20 个字符，包括数据标识符。 但是，
可用的标记空间可能会将可能的数据长度限制为较少的数据字符。 
6.8 代码 128 和代码 39 的人类可读信息。应该打印（代码 128 或代码 39 符号）
中的 HRI。 印刷时，HRI： 
SHALL 表示所有编码信息。 
应始终直接放在代码 128 或代码 39 符号的上方或下方。 
当 DI 是 HRI 的一部分时，应该在括号（）中显示数据标识符。 
不显示开始或停止字符或校验位。 
应为大写字母数字 Arial Narrow Bold，Helvetica Condensed 或同等产品。 
HRI 中用于将数据标识符与用户信息分开的括号不应编码在符号中。 
 
 
© 版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
23/59 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 


### 第 23 页
通用全球工程标准 
GMW15862 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company 
No reproduction or networking permitted without license from IHS 
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST 
 
 
 
7 直接零件标记（DPM） 
标准的这一部分描述了直接部件标记（DPM）的一般准则，要考虑的因素，以及如何为给定的应用
选择最合适的 DPM 技术。 DPM 的符号应为数据矩阵或 QR 码。参考 AIAG B-17 了解更多详情和
NASA-STD-6002C - 在航空航天零件上应用数据矩阵识别符号。 
注意：所有 DPM 系统在创建标记后都应立即要求条形码验证（标记质量测量），以保持符号质量和
GM 下游可扫描性。 
7.1 注意事项。以下是使用 DPM 的典型标准。 
部件太小，无法使用传统的条形码标签。 
部件受环境条件限制，不允许使用标签。 
DPM 可能比单个项目标签更具成本效益。 
由于上述原因，零件的生命周期需要标识，并且标签不可接受。 
DPM 作为制造过程的一部分而不是二级或手动过程集成。 
7.2 零件上的直接标记。条形码内容或部件上的直接标记应遵循第 3 节的编码方案。 
7.3 DPM 人类可读信息。标记和/或处理周期时间的可用区域可以消除或减少所需的人类可读信息量。
供应商和通用汽车之间必须达成相互协议。 
7.4 标记方法。表 14 中的指南确定了不同材料的建议标记方法。 
表 14：材料标记过程指南 
材料 
金属的 
非金属的 
 
 
 
 
 
标记过程 
 
铝 
 
亚铁 
 
镁 
 
钛 
 
陶瓷 
 
玻璃 
 
玻璃纤维 
 
塑料 
 
橡胶 
刻划 
X 
X 
X 
X 
X 
X 
X 
X 
X 
激光蚀刻和点刻 
X 
X 
X 
X 
 
 
 
 
 
激光 
X 
X 
X 
X 
X 
X 
X 
X 
X 
喷墨 
X 
X 
X 
X 
X 
X 
X 
X 
X 
 
7.4.1 划线。划线标记技术提供了通过移动材料在零件表面上划线或绘制图像的能力。该过程
使用气动或机电驱动的触针。标记数据矩阵符号可以使用 ISO / IEC 16022 中允许的三种方法来划线，
这些方法是方形，圆形或八边形。通常使用方形模块，因为它们更容易解码或读取。方形模块的外
观受标记力和材料硬度的影响。与点喷法相比，环境噪声通常会降低。标记噪声取决于零件几何形
状和夹具工具。 
7.4.1.1 通常，划线时最好使用方形模块。划线标记使用可以在表面上创建方形元素/单元格的触
控笔。它在方形的起点处用尖头触笔敲击表面，然后继续形成四条连接的直线，勾勒出方形元素/单
元格。可以调整元素/单元格大小。典型的填充率为 80％。 
7.4.1.2 划线标记比激光蚀刻和点刻慢。 
7.4.2 激光蚀刻和点刻。 点式打标技术通常在零件表面产生圆形压痕，带有气动或机电驱
动的触针，也称为销。 点对点的可读性至关重要，标记符号是缩进点的形状，大小和间距。 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
24/59 


### 第 24 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
点的大小和外观主要由触针锥角，标记力和材料硬度决定。 创建的缩进点应该适合于捕获或反
射光，并且足够大以便可以与零件的表面粗糙度区分开来。 它应该还有足够宽的间距，以适应
不同的模块尺寸，位置和照明。标记和阅读金属上的圆点标记符号所涉及的问题与印在纸上的
符号不同。 第一个根本区别在于暗场和光场之间的对比是通过符号的人工照明产生的。 因此，
模块的形状，尺寸，间距和零件表面光洁度都会影响符号的可读性。 
7.4.2.1 成功点对点标记和阅读项目的关键是严格控制影响过程一致性的变量。 符号读取验证
系统可在一定程度上提供过程参数的反馈。 应建立并遵循标记系统操作和维护程序和时间表，
以帮助确保一致的符号质量。 
7.4.2.2 点式标记比激光标记慢，并且具有密度限制（图 26）。 
 
图 26：激光蚀刻和点刻说明照明的重要性 
7.4.3 激光。激光可用于在某些材料上创建标记。这是通过将一束相干，准直，聚焦的光能引导
到物品表面上来完成的。通常，当激光束与物品接触时，其光能被转换成热能，通过熔化，消融，碳
迁移或化学反应产生标记。各种材料可以对每种类型的激光和/或激光标记技术作出不同的反应。所
有激光器都不会在所有基板上产生可读标记。 
7.4.3.1 在考虑激光打标系统时，应考虑以下因素： 
要标记的材料类型。 
激光类型和打标处理类型。 
激光功率。 
周期时间。 
要标记的信息（数据量）。 
激光安全。 
7.4.3.2 不同的材料以不同的速率吸收或反射特定的激光波长。吸收量与激光加热材料并引起材料
变化的能力成正比出现。激光介质的类型将决定激光的光波长。激光标记系统通常从其激光介质中获
得其名称。例如，CO2 激光器使用二氧化碳气体作为介质。 
7.4.3.3 激光标记通常产生最快的标记周期（图 27）。 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
25/59 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 


### 第 25 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
 
 
图 27：塑料上的激光蚀刻 
7.4.4 喷墨。 喷墨技术是一种非侵入式标记技术，可以通过空气喷射精确控制的墨滴，其图案能够产生符号
这些液滴由悬浮在液体中的颜料制成，蒸发，使有色染料留在物品表面。 
7.4.4.1 生成这些滴定的主要方法有两种：按需滴定和连续滴定。 Drop-on-Demand 方法使用阀门或压电技术迫
使墨水通过孔口。 与连续方法相比，该方法具有显着的打印分辨率优势。 墨水可以“喷射”的距离通常限制在
不超过 3.1 毫米（1/8 英寸）。 这限制了 Drop-on-Demand 在工业 DPM 应用中的使用（图 28）。 
 
图 28：带有矩形数据矩阵的喷墨示例扬声器 
7.4.4.2 标记和读取直接放在零件上的喷墨符号所涉及的问题与印在纸上的符号略有不同。 必须特别注意要在
其上沉积墨水的基材的状态。 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
26/59 


### 第 26 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
在使用研磨垫进行标记之前清洁零件表面以去除涂层，生锈和变色，或使用气刀吹掉多余的加
工液，碎屑或油可以提高标记和粘附可靠性。 
7.4.4.3 喷墨打标不应被视为永久性打标方法，通常仅限于不会暴露在恶劣制造条件下的部件。特别
是，它不应该用于电火花加工（EDM），喷砂，机加工和喷丸处理的表面。这些条件中的许多条件改
变了表面特性和/或颜色，并且可能需要重新施加标记。此外，必须注意确保零件不会经过任何涂料溶
解液。对喷墨标记的另一个限制是，在标记过程中，通常一部分必须在一个方向上以一致的速度移动
经过标记头。可以使用标记头移动并且标记的部分保持静止的系统。 
 7.4.4.4 喷墨标记适合使用需要特殊照明的紫外线（UV）油墨来要求安全的应用。 
8 符号（条形码）质量验证 
8.1 概述。验证设备是质量控制工具，用于验证印刷线性条形码符号的可读性和标准符合性。扫描
不被视为验证。 
8.1.1 验证测试应在标签和直接标记部件上进行。 
8.2 直接部件标记验证。DPM 验证器是由照明，光学，相机（成像器）验证软件和校准参考
组成的系统。验证系统的分辨率应至少是成像仪（阅读器）的两倍。这可以用更高放大率的光学器件
或具有两倍于读取器分辨率的成像装置来实现。AIAG B-17 应作为使用激光，喷丸或喷墨直接标记的工
艺指南。直接部件标记应由 GM 和供应商共同商定。直接标记的成像（扫描）要求可能需要特殊照明
和专业成像仪。 
注意：DPM 系统应在创建标记后立即进行验证，以保持质量和下游可扫描性。 
8.3 标签性能测试。必须对位于使用生产过程安装并在使用点扫描的生产意图零件，部件，组
件或模块的生产意图标签进行测试。 
8.3.1 KCDS VER / TRA。包含要求 KCDS 验证（VER）或可追溯性（TRA）的部件的条形码的标
签必须符合 GMW14573 A，B，C，D，E 或 G 的永久性标签测试要求。 
8.3.2 其他条形码。标签最低限度必须符合 GMW14573 F 的要求。 
8.4 标签上的条形码打印质量。在 GM 扫描点，条形码打印质量应为 ANSI C 级或更高级
别。使用 ISO / IEC 15415 或 AIM DPM 质量指南进行评估。 
8.4.1 数据矩阵和 QR 码打印标签质量。 ISO / IEC 15415（打印质量测试规范 - 二维符
号），ISO / IEC 16022（数据矩阵）和 ISO / IEC 18004（QR 码）应用于确定标签上的数据矩阵和 QR 码打
印质量。 
8.4.1.1 打印质量应在相互达成一致的 GM 扫描点测量。 
8.4.1.2 符号质量参数确保在广泛的环境中的可读性。此外，建议在一致的条件下进行质
量测量;例如，使用相同的照明并在同一表面上贴上标签。 
8.4.1.3 等级是根据 AIM 国际符号规范文件质量定义进行的特定测量的结果： 
符号解码。 
符号对比。 
符号打印。 
符号轴向不均匀。 
符号纠错。 
8.5 标签放置。必须在零件图纸/UG 数学上标识标签应用位置。在制造/装配地点使用固定安装
扫描仪时，必须放置一致的标签。 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
        27/59 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 


### 第 27 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
9 其他产品特性 
9.1 概述。本节介绍如何添加产品特征的其他信息。 （附录 F）产品特性的
例子是散热器压力试验的试验台结果;照明模块的电流消耗;测量紧固件的扭矩;设备的射频（RF）或
确定对质量或保修有重要意义的任何其他重要测量数据。在某些情况下，工艺需要产品特性，例如
活塞尺寸与气缸孔匹配。 
注意：轮胎应遵循 4.11。 
9.2 数据标识符 7Q。数据标识符 7Q 应与适当的附加度量单位限定符 ANSI X12.3 数据元素
编号 355 计量单位一起使用。 ANSI X12.3 表的摘录将发布到 AIAG 网站，网址如下：http：
//www.autoid.org/ANSI_MH10/ansi_mh10sc8_wg2.htm。如果合适，数据可能包含所需精度的小数点。 
14.7 电压直流（VDC）的电压测量示例为 7Q14.72H，其中 2H 是 VDC 的限定符。 
9.2.1 7Q 的编码。参考 4.5 用于数据语法编码方法。使用 4.5 中的示例（图 29）。
[)>
R  06
G  Y0000000000000X
G  P12345678
G  12V987654321
G TLSYYDDDA2B4C6D8E
R
 OT, 
the 
field 


### 第 28 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
S 
S 
S 
S 
S 
S 
SE 
SHOULD  be  added  after  the  trace  code  field  T  and  before  the  Record  Separator  
R  .  The Encodation 
would look like this: 
[)>R  06G  Y0000000000000XG  P12345678G  12V987654321G  TLSYYDDDA2B4C000EG 7Q14.72HRSEOT. 
S 
S 
S 
S 
S 
S 
在二维条码内编码的是一个 7Q14.72H 的字段，转换为 14.7 VDC。 
9.2.2 解析算法。 对于 DI 7Q，数据字段的最后两个字符应为 ANSI X12.3 数据元素编号 355 度
量单位（限定符）。 参见表 15. DI 7Q 和两个字符限定符之间的数据元素构成该值。 综上所述： 
DI 7Q 表示最后两个字符包含度量单位 X12.3 限定符。 
转到数据字段的末尾。 
返回两个字符。 
这两个字符是限定符代码。 
在限定符表中查找代码以确定度量单位。 
表 15：ANSI X12.3 示例 355 数据元素编号 355 计量单位（限定符） 
合格者 
定义 
 
合格者 
定义 
2G 
伏特 (AC) 
 
68 
安培 
2H 
伏特 (DC) 
 
CE 
摄氏 
2N 
分贝 
 
DN 
Deci 牛顿流量计 
2P 
千字节 
 
FA 
华氏 
2Z 
毫伏 
 
G9 
千兆字节 
4K 
毫安 
 
HJ 
马力 
4L 
兆字节 
 
HP 
毫米 H20 
4S 
帕斯卡尔 
 
HZ 
赫兹 
70 
伏特 
 
NU 
牛顿流量计 
 
9.3 竣工标签。 “竣工”标签/标记提供了一种捕获跟踪数据的方法，作为外部装配过程的一
部分以及增值装配器（VAA）。 2D 条形码的结构包括每个需要可追溯性的组件的单独跟踪记录。附
录 G. 
 
 
 
10 射频识别（RFID） 
10.1 当 RFID 标签对于识别部件、组件、组件和模块变得具有成本效益时，数据语法和数据字段将在包含
用户存储器的被动或主动 RFID 标签中编码。用于将数据编码为 2D 符号的标准已并入 AIAG B-11RFID 标准，
使得从数据/IT 角度来看，这两种技术可互换。AIAG-B-11 完全符合 ISO/IEC 标准。 
11 验证 
11.1 责任。设计发布工程师负责发布符合此标准的条形码。 
11.2 形式。 CG2503 - 条形码格式验证表用于确保正确的条形码内容，条形码扫描质量和工厂准备就
绪。 
11.3 过程。 CG2503 存储在 GDM 常规表单和模板文件夹中。要遵循的 CG2503 流程和 RASIC 记录
在标签和文献网站的条形码页面上：http：//gmna1.gm.com/eng/labels/barcodes.html。 
 


### 第 29 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
12 注意事项 
12.1 词汇表。 
2D（二维）符号：必须垂直和水平读取整个消息的光学可读符号。二维符号可以是两种类型之一：矩阵符号和
多行符号。二维符号具有错误检测并且可以包括错误校正特征。 （见矩阵符号。） 
AIAG：汽车工业行动组织 www.aiag.org。 
目的：自动识别制造商协会 www.aimglobal.org。 
字母数字：字符集包含字母字符（字母）和数字（数字）以及通常的其他字符，如标点符号。 
ANSI 或 ANS：美国国家标准协会。 
自动识别：代码识别代码（Code 128，Code 39，Data Matrix 和 QR Code）。 
条形码（即条形码）：光学机器可读的数据表示。传统上，条形码表示宽度（线）中的数据和平行线的间距，
并且可以称为线性或一维（1D）条形码或符号。但它们在二维（2D）矩阵码或符号体系的图像中也具有正方
形，点，六边形和其他几何图案的图案。重要的是要注意两种模式（线，正方形，点等）和间距构成数据编码
方案。 
条形码标签：印有 1D 和/或 2D 条形码符号，带有或不带有人类可读数据。 
批量：批量生产是一种制造方法，用于批量生产或加工任何产品，而不是连续生产过程或一次性生产。批次的
例子是铸件（基于浇注），涂料（基于单一成分混合物），粘合剂，钢等。 
单元格：参见模块。 
部件：作为更高级别组件的组成部分的零件，组件或原材料。 
数据字段：由数据标识符及其关联数据组成的消息。 
数据格式：引用数据字段中使用的字母和数字，以及数据字段中允许的数据总量。 
数据格式示例： 
“an.6”表示允许最多六个字符的字母数字数据。 “n.12”表示允许最多 12 个字符的数字数据。 
指定字符或字符串，用于定义后面的数据元素的预期用途。 出于自动数据捕获技术的目的，数据标识符表示字
母数字标识符，        
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
    29/59 
 
 
 


### 第 30 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
如 ISO / IEC 15418，UCC / EAN 应用标识符和 FACT 数据标识符和维护以及 ANSI MH10.8.2 中所定义。
数据矩阵：特定的二维条形码符号系统。 
数据语法代码：ISO / IEC 15434 中使用的 ASCII 字符，用于表示数据包络结构中的特定功能。 
十进制：基数为 10 的编号系统，其数字由 X10 表示，当十进制数需要用十六进制数表示时
（X16）。 
直接零件标记：使用侵入式或非侵入式标记技术直接应用于表面的标记。 
交通部：交通部（美国）。 
DUNS：由 Dun and Bradstreet www.dnb.com 分配的九位数的特定于站点的贸易伙伴识别码。 
纠错：用于重建丢失或损坏数据的数学技术。 
十六进制：十六进制描述了一个 16 进制数字系统。十六进制数是 0 到 9，然后是字母 A 到 F，其
数字由 X16 表示，此时需要从十进制数(X10)表示十六进制数。 
人类可读信息：可能出现并与机器可读介质相关联的信息，通常位于旨在向人传递信息的标签(例
如，条形码、2D 符号、RF 标签)上。 
IEC：国际电工委员会。所有电气，电子和相关技术的政府，企业和社会的国际标准和合格评定。 
ISO：国际标准化组织。 ISO 是由 156 个国家的国家标准机构组成的网络，每个国家有一名成员，
瑞士日内瓦的中央秘书处负责协调该系统。 
ISO/IEC：代表 ISO 和 IEC 组织完成和/或支持的工作。 
成像器（参见扫描仪）：用于读取使用光学成像技术的线性条码和二维符号 A 型条码扫描器的
（通常是基于相机矩阵阵列或线性阵列光学传感器技术）。 
单个零件：购买，制造和/或分发的单个部件，项目或材料。 
侵入式标记：任何设计为材料表面以形成人或机器可读符号的设备。该标记类别包括，但不限于：
设备没有磨损，烧伤，腐蚀，切割，变形，溶解，蚀刻，熔化，氧化或汽化的材料的表面。 
儒略日期：“儒略日期”是特定年份的日期数，因此 1 月 1 日=第 1 天，2 月 28 日=第 59 天，依此
类推。这是制造/组装的实际日期。 
标签：以任何方式在纸、布、聚合物、金属或其他材料上产生，通过压敏背衬或热应用粘贴在某
物上，在白色背景上或黑色背景上使用黑色图像（反转图像）以指示其内容、目的地。或其他信
息。 
激光：通过受激发射的辐射进行光放大。 
线性条形码符号（1D）：提供光学可读的平行矩形条阵列和不同厚度和间隔的空间。线性条形码
符号包含前导静区，起始字符，数据字符，停止字符和尾随静区，并且仅在一个轴中读取。 
批量：参见批次。 
制造商：物品的实际生产者或制造者，不一定是交易中的供应商。 
制造或装配现场 DUNS 编号：用于标识特定位置的数字 DUNS ID 代码。 
矩阵符号：常规图案中的多边形或圆形元素的集合，用于表示视觉扫描系统检索的数据。 
模块：在线性或多行条形码符号系统中，符号字符中的标称度量单位。在某些符号体系中，元素
宽度可以指定为一个模块的倍数。相当于 X 维度。 
 在矩阵符号系统中，用于编码代码字的一位的单个单元或元素。 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
30/59 
 
 


### 第 31 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
多行符号系统（也称为堆叠符号系统），条形码符号系统，其中符号由两个或多个垂直相邻的符号字符行组
成。 
共同定义：交易各方商定的含义。 
NASA：美国国家航空航天局（美国）。 
非侵入式标记：通过向表面添加材料来形成标记的方法。非侵入式方法包括喷墨，激光粘合，液态金属喷射，
丝网印刷和薄膜沉积。 
零件：具有分配给它的唯一名称和/或编号的可识别项。 
QR 码：特定的二维条码符号。 
修订级别：指定为工程变更级别，修订或编辑或软件版本的代码。 
RS232：串行二进制数据信号的标准。它通常用于计算机串行端口。 
扫描仪（参见成像仪）：一种输入设备，它将与符号的每个连续元素（线性或 2D）的反射率成比例的信号
发送到解码器。 
序列号：为实体的生命周期分配给实体的唯一代码，例如安全气囊模块，引擎或传输组件，用于区分该特定
实体与任何其他实体。 
供应商/经销商：在交易中，生产、提供或提供产品或服务的一方。 
供应商/经销商 ID：用于标识供应商/供应商的数字 DUNS ID 代码。 
符号体系：以光学可读形式表示数据的标准方法。每个符号系统规范都规定了其特定的组成规则或符号结构。 
二维符号（2D）：光学可读符号，必须垂直和水平读取整个消息。二维符号与线性条形码的不同之处在于它
们由与像线性条形码中的条相当的“像素元素”组成。 
车辆分区和产品结构（VPPS）：表示描述车辆内容的全局一致性方法。VPPS 是跨主要车辆区域（Powert.、
Chassis 等）具有一致性的分层结构。VPPS 是一种允许跨系统（GMNA、GME、GMLAAM、GMAP 等）全
局数据共享/比较的机制。（NOA 002）和 GADVC（NOA 012）。变更是通过全局过程来管理的。 
X 维度：条形码符号中窄元素的指定宽度或二维符号中单个元素/单元格的指定宽度。 
年份：在可追溯性的背景下，年份是制造/组装的实际年份，而不是“模型年”。 
缩略语，缩写和符号。 
1D 
一维或也称为线性条形码符号 
2D 
二维 
AIAG 
汽车工业行动集团 
AIDC 
自动识别数据采集技术 
AIM 
自动识别制造商协会 
ANS 
美国国家标准 
ANSI 
美国国家标准协会 
ASCII 
美国信息交换标准代码 
BOM 
物料清单 
CI 
组件标识符 
CO
2 
二氧化碳 
CR 
变更请求 
DFMEA 
设计失效模式效应分析 
DI 
数据标识符 
DNB 
邓白氏 
DOT 
交通运输部 
DPM 
直接部件标记 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 10 月 
31/59 
 
 
 


### 第 32 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
DUNS 数据通用编号系统 
DRE 设计发布工程师 
EC 错误纠正 
ECC 纠错码 
EDM 电火花加工 
EOT 传输结束 
FMVSR 联邦机动车安全法规 
GM＆Q 全球制造和质量 
GPDS 全球产品描述系统 
GQR 全球质量要求 
HRI 人类可读信息 
IEC 国际电工委员会 
in 英寸 
ISO 国际标准化组织 
IT 信息技术 
KCDS 关键特性指定系统 
m 米 
ME 制造工程 
mm 毫米 
NASA  国家航空航天局（美国） 
NOA 行动通知 
nm 纳米 
OEM 初始设备制造商 
PE 过程工程 
PFMEA 过程失效模式和影响分析 
PRTS 问题报告和跟踪系统 
pt 点 
QR 快速响应 
RF 无线电频率 
RFI 无线电频率识别 
SOR 要求声明 
TRA 可追溯性 
UCC / EAN 统一代码委员会/欧洲物品编号 
USB 通用串行总线 
ULD 单元加载设备 
UV 紫外线 
V 供应商/供应商标识符 
VAA 增值汇编程序 
VDC 电压直流电 
VER 验证 
VIN 车辆识别号码 
VPPS 车辆分区和产品结构 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
32/59 
 
 


### 第 33 页
通用全球工程标准 
GMW15862 
 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
13 编码系统 
本标准应在其他文件，图纸等中引用，如下：GMW15862 
 
14 版本和修订 
这个标准起源于 2008 年 3 月。它在 2008 年 11 月首次被国际工程技术公司批准。它首次发表
于 2008 年 12 月。 
发布 
出版日期 
描述（组织） 
1 
2008 年 12 月 
初始发布. 
2 
2009 年 10 月 
修改后将 VPPS 代码添加到条形码内容中。 确定批次
/批次可追溯性的结构。 添加了示例。 第 2.1,2.3,9,11
节 
更新; 主要重写为第 3,4,5,6,7 节。修改附录序列以增
强可用性。（内饰） 
3 
2010 年 11 月 
删除了过渡标签格式。 用动力总成格式替换了附录 J. 
添加了对动力总成可追溯性格式的参考。 添加了需要
CG2503 的验证部分。 
修改表 C2。 更正了条形码数字。 （标签和文献 GSSLT） 
 
附录 A：典型数据标识符 
ANSI MH10.8.2 为许多行业中的许多用途定义了 100 多个数据标识符。 GM 需要使用数据标识符。 
下表包括汽车行业中经常使用的 ANSI MH10.8.2 中的一些典型 DI。 由于对 ANSI MH10.8.2 的频繁更
新，草稿副本保存在 www.autoid.org，网址为：
http//www.autoid.org/ANSI_MH10/ansi_mh10sc8_wg2.htm。 
表 A1：汽车行业中使用的典型数据标识符 
DI 
说明 
B 
容器类型（内部分配或相互定义） 
1B 
可回收的容器识别码（例如，金属桶，篮子，卷轴，单元装载装置（ULD），追踪，油箱或联运容器）（见“2B”）） 
2B 
气体容器识别代码由制造商根据美国专利 No. 交通运输部 DOT 标准 
D 
日期，格式为 YYMMDD（相互定义的重要性） 
1D 
日期格式为 DDMMYY（相互定义的重要性） 
2D 
格式为 MMDDYY 的日期（相互定义的重要性） 
3D 
YDDD 格式的日期（儒略相互定义的重要性） 
4D 
YYDDD 格式的日期（儒略相互定义的重要性） 
5D 
ISO 格式的日期 YYMMDD 后跟 X12.3 数据元素编号 374 限定符提供指定日期类型的代码（例如，发货日期，制造日期） 


### 第 34 页
通用全球工程标准 
GMW15862 
 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
1E 
气压以帕斯卡表示为标准国际尺度 
I 
车辆识别号码（VIN） 
2I 
缩写的 VIN 代码（例如：PVI，订单 ID，序列 ID） 
5N 
符合 AIAG 建议的编码结构和格式。 完整数据标识符的格式为 5Nxx，其中 xx 位于完整代码列表中，可在 www.aiag.org 上找到。 
P 
GM 指定的物品识别码 
1P 
供应商分配的物品识别代码 
2P 
分配给零件修订级别的代码（例如，工程更改级别，修订版或版本，软件修订级别） 
20P 
传统 GM1737 验证/错误校对代码/结构由 GM 定义 
Q 
数量、件数或数量（仅数值）（测量单位和意义相互定义） 
1Q 
理论长度/重量（仅数值）（历史上用于原金属装运） 
2Q 
实际重量（仅数值） 
7Q 
格式中的数量和度量单位：数量，后面跟着 ANSI X12.3 数据元素字典标准中数据元素编号 355 中定义的两个字符的度量单位代码 
S 
供应商分配给实体的序列号 
10S 
机器、工作单元或工具 ID 代码 
11S 
固定资产 ID 代码 
T 
GM 定义的可追溯性代码/结构 
1T 
供应商/制造商指派的可追溯数量 
20T 
遗留 GM1737 可追溯代码/结构由通用汽车公司定义 
21T 
遗留 GM1737 增强了通用汽车公司定义的可追溯性代码/结构。 
12V 
识别制造/装配现场的 Duns 数 
14V 
识别特定 GM 站点的 Duns 数作为定制器 
Y 
GM 内部应用程序-分配给 VPPS 压缩代码。 
Z 
GM 与供应商之间的相互定义（相互反映含义的标题） 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
34/59 
 
 
 
 


### 第 35 页
通用全球工程标准 
GMW15862 
 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
附录 B: 儒略历 
 
表 B1：永久儒略日期日历 
天 
一月 
二月 
三月 
四月 
五月 
六月 
七月 
八月 
九月 
十月 十一月 十二月 
天 
1 
001 
032 
060 
091 
121 
152 
182 
213 
244 
274 
305 
335 
1 
2 
002 
033 
061 
092 
122 
153 
183 
214 
245 
275 
306 
336 
2 
3 
003 
034 
062 
093 
123 
154 
184 
215 
246 
276 
307 
337 
3 
4 
004 
035 
063 
094 
124 
155 
185 
216 
247 
277 
308 
338 
4 
5 
005 
036 
064 
095 
125 
156 
186 
217 
248 
278 
309 
339 
5 
6 
006 
037 
065 
096 
126 
157 
187 
218 
249 
279 
310 
340 
6 
7 
007 
038 
066 
097 
127 
158 
188 
219 
250 
280 
311 
341 
7 
8 
008 
039 
067 
098 
128 
159 
189 
220 
251 
281 
312 
342 
8 
9 
009 
040 
068 
099 
129 
160 
190 
221 
252 
282 
313 
343 
9 
10 
010 
041 
069 
100 
130 
161 
191 
222 
253 
283 
314 
344 
10 
11 
011 
042 
070 
101 
131 
162 
192 
223 
254 
284 
315 
345 
11 
12 
012 
043 
071 
102 
132 
163 
193 
224 
255 
285 
316 
346 
12 
13 
013 
044 
072 
103 
133 
164 
194 
225 
256 
286 
317 
347 
13 
14 
014 
045 
073 
104 
134 
165 
195 
226 
257 
287 
318 
348 
14 
15 
015 
046 
074 
105 
135 
166 
196 
227 
258 
288 
319 
349 
15 
16 
016 
047 
075 
106 
136 
167 
197 
228 
259 
289 
320 
350 
16 
17 
017 
048 
076 
107 
137 
168 
198 
229 
260 
290 
321 
351 
17 
18 
018 
049 
077 
108 
138 
169 
199 
230 
261 
291 
322 
352 
18 
19 
019 
050 
078 
109 
139 
170 
200 
231 
262 
292 
323 
353 
19 
20 
020 
051 
079 
110 
140 
171 
201 
232 
263 
293 
324 
354 
20 
21 
021 
052 
080 
111 
141 
172 
202 
233 
264 
294 
325 
355 
21 
22 
022 
053 
081 
112 
142 
173 
203 
234 
265 
295 
326 
356 
22 
23 
023 
054 
082 
113 
143 
174 
204 
235 
266 
296 
327 
357 
23 
24 
024 
055 
083 
114 
144 
175 
205 
236 
267 
297 
328 
358 
24 
25 
025 
056 
084 
115 
145 
176 
206 
237 
268 
298 
329 
359 
25 
26 
026 
057 
085 
116 
146 
177 
207 
238 
269 
299 
330 
360 
26 
27 
027 
058 
086 
117 
147 
178 
208 
239 
270 
300 
331 
361 
27 
28 
028 
059 
087 
118 
148 
179 
209 
240 
271 
301 
332 
362 
28 
29 
029  
088 
119 
149 
180 
210 
241 
272 
302 
333 
363 
29 
30 
030  
089 
120 
150 
181 
211 
242 
273 
303 
334 
364 
30 
31 
031  
090  
151  
212 
243  
304  
365 
31 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
35/59 
 
 


### 第 36 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
表 B2：闰年儒略日期日历 
天 
一月 
二月 
三月 
四月 
五月 
六月 
七月 
八月 
九月 
十月 十一月 十二月 
天 
1 
001 
032 
061 
092 
122 
153 
183 
214 
245 
275 
306 
336 
1 
2 
002 
033 
062 
093 
123 
154 
184 
215 
246 
276 
307 
337 
2 
3 
003 
034 
063 
094 
124 
155 
185 
216 
247 
277 
308 
338 
3 
4 
004 
035 
064 
095 
125 
156 
186 
217 
248 
278 
309 
339 
4 
5 
005 
036 
065 
096 
126 
157 
187 
218 
249 
279 
310 
340 
5 
6 
006 
037 
066 
097 
127 
158 
188 
219 
250 
280 
311 
341 
6 
7 
007 
038 
067 
098 
128 
159 
189 
220 
251 
281 
312 
342 
7 
8 
008 
039 
068 
099 
129 
160 
190 
221 
252 
282 
313 
343 
8 
9 
009 
040 
069 
100 
130 
161 
191 
222 
253 
283 
314 
344 
9 
10 
010 
041 
070 
101 
131 
162 
192 
223 
254 
284 
315 
345 
10 
11 
011 
042 
071 
102 
132 
163 
193 
224 
255 
285 
316 
346 
11 
12 
012 
043 
072 
103 
133 
164 
194 
225 
256 
286 
317 
347 
12 
13 
013 
044 
073 
104 
134 
165 
195 
226 
257 
287 
318 
348 
13 
14 
014 
045 
074 
105 
135 
166 
196 
227 
258 
288 
319 
349 
14 
15 
015 
046 
075 
106 
136 
167 
197 
228 
259 
289 
320 
350 
15 
16 
016 
047 
076 
107 
137 
168 
198 
229 
260 
290 
321 
351 
16 
17 
017 
048 
077 
108 
138 
169 
199 
230 
261 
291 
322 
352 
17 
18 
018 
049 
078 
109 
139 
170 
200 
231 
262 
292 
323 
353 
18 
19 
019 
050 
079 
110 
140 
171 
201 
232 
263 
293 
324 
354 
19 
20 
020 
051 
080 
111 
141 
172 
202 
233 
264 
294 
325 
355 
20 
21 
021 
052 
081 
112 
142 
173 
203 
234 
265 
295 
326 
356 
21 
22 
022 
053 
082 
113 
143 
174 
204 
235 
266 
296 
327 
357 
22 
23 
023 
054 
083 
114 
144 
175 
205 
236 
267 
297 
328 
358 
23 
24 
024 
055 
084 
115 
145 
176 
206 
237 
268 
298 
329 
359 
24 
25 
025 
056 
085 
116 
146 
177 
207 
238 
269 
299 
330 
360 
25 
26 
026 
057 
086 
117 
147 
178 
208 
239 
270 
300 
331 
361 
26 
27 
027 
058 
087 
118 
148 
179 
209 
240 
271 
301 
332 
362 
27 
28 
028 
059 
088 
119 
149 
180 
210 
241 
272 
302 
333 
363 
28 
29 
029 
060 
089 
120 
150 
181 
211 
242 
273 
303 
334 
364 
29 
30 
030  
090 
121 
151 
182 
212 
243 
274 
304 
335 
365 
30 
31 
031  
091  
152  
213 
244  
305  
366 
31 
 
 
 
 
 
© 版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
36/59 
 


### 第 37 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
附录 C：车辆分区和产品结构（VPPS） 
车辆分区和产品结构（VPPS）是描述车辆内容的全球一致方式
（http://gmna1.gm.com/eng/grc/vpps/index.html）。 VPPS 是一种分层结构，在主要车辆区域（动力
总成，底盘等）具有一致性.VPPS 是一种标准的全球技术（GMNA，GME，GMLAAM，GMAP 等） 产
品分解结构由 GEDOC（NOA 002）和 GADVC（NOA 012）批准。 通过全球流程管理变更。 联系 KCDS
寻求帮助。 
GMW15862 将数据标识符 Y 分配给压缩的 VPPS 代码，并使用零（0）将数据填充到剩余的级别，以
使总共 14 个数据字符。 小数是隐含的（未编码）。 参见表 C1 中的示例。 
有关 VPPS 代码的第 14 个字符的说明，请参阅附录 C2。 
表 C1：GMW15862 VPPS 数据编码的示例 
 
压缩的 VPPS 
VPPS 说明 
隐含的
十进制 
用 DI Y 编
码 
951.98 
燃料箱和罐 - 附件/组件 
 
 
952.97 
燃油泵和发送器-模块/组件 
 
 
952.98 
燃油泵和发送器附件/部件 
 
 
953.98 
燃料管道和五金附件/部件 
 
 
954 
减排液（尿素系统） 
 
 
954.01 
箱体装配 
95401 
Y9540100000000X 
954.01.01 
箱体 
 
 
954.01.02 
箱体定位桩 
 
 
954.01.03 
液位传感器 
 
 
954.01.03.01 
液位传感器连接器 
 
 
954.01.04 
压力传感器 
 
 
954.01.04.01 
压力传感器连接器 
 
 
954.01.05 
温度传感器 
 
 
954.01.05.01 
温度传感器连接器 
 
 
954.01.06 
泵 
 
 
954.01.06.01 
泵连接器 
 
 
954.01.07 
滤波器 
 
 
954.01.08 
油罐/油泵组 
 
 
954.01.09 
加热器 
 
 
954.01.10 
排气阀 
 
 
954.01.11 
通风软管 
 
 
954.01.12 
功能模块环 
 
 
954.02 
填充管组件 
 
 
954.02.01 
填充软管 
 
 
954.02.02 
填充管 
 
 
954.02.03 
软管夹 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
37/59 
 
 


### 第 38 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
VPPS 代码的最后一个字符（第 14 个字符）可用于引用部件安装位置。 X.当在车辆上安装相同的部
件号时，扫描操作将根据表 C2 对最终字符进行编码。 位置代码位于扫描站或电子模块传输到数据
库时。 
Y9540100000000X（隐含的小数，右边用 0 填充）例如，使用燃料箱组件，压缩的 VPPS 格式为
954.01 并在数据字段内编码。 如果该组件是右侧安装的油箱，那么通过表 C2 传输的编码将是
Y9540100000000R。 
表 C2：用于表示车辆安装位置的第 14 个字符 VPPS 的代码 - 默认值为 X（将使用大写字符） 
特征 
定义 
 
 
特征 
定义 
A 
左后侧 
 
N 
均不 
B 
左前侧 
 
O 
不要使用 
C 
右后方 
 
P 
保留的 
D 
右前方 
 
Q 
不要使用 
E 
左后车顶 
 
R 
右 
F 
左前车顶 
 
S 
保留的 
G 
右后车顶 
 
T 
左后方 
H 
右前方 
 
U 
左前方 
I 
不要使用 
 
V 
右后方 
J 
保留的 
 
W 
右前方 
K 
车顶后部 
 
X 
默认 
L 
左 
 
Y 
前方 
M 
车顶前部 
 
Z 
后方 
注意：DRE 应指定扫描站是否需要安装位置字符。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
38/59 
 


### 第 39 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
附录 D：数据矩阵参考信息 
下面是理解数据矩阵符号学的一些特征的助手。包含用于估计数据矩阵符号所需区域的符号大小的
过程。 
 
图 D1：数据矩阵符号的剖析以及每个字节的八位如何分布在 10×10 数据矩阵符号中 
 
 
表 D1：数据矩阵数据容量（正方形符号） 
符号大小 
行 
10 
12 
14 
16 
18 
20 
22 
24 
26 
32 
36 
40 
44 
48 
52 
64 
72 
80 
88 
96 
104 
120 
132 
144 
列 
10 
12 
14 
16 
18 
20 
22 
24 
26 
32 
36 
40 
44 
48 
52 
64 
72 
80 
88 
96 
104 
120 
132 
144 
数据容量 
数字 
6 
10 
16 
24 
36 
44 
60 
72 
88 
124 
172 
228 
288 
348 
408 
560 
736 
912 
1152 
1392 
1632 
2100 
2608 
3116 
字母数字 
3 
6 
10 
16 
25 
31 
43 
52 
64 
91 
127 
169 
214 
259 
304 
418 
550 
682 
862 
1024 
1222 
1573 
1954 
2335 
字节 
1 
3 
6 
10 
16 
20 
28 
34 
42 
60 
84 
112 
142 
172 
202 
278 
366 
454 
574 
694 
814 
1048 
1302 
1556 
 
 
 
 
 
 
© 版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
39/59 


### 第 40 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
表 D2：数据矩阵数据容量（矩形符号） 
符号大小 
行 
8 
8 
12 
12 
16 
16 
列 
18 
32 
26 
36 
36 
48 
数据容量 
数字 
10 
20 
32 
44 
64 
98 
字母数字 
6 
13 
22 
31 
46 
72 
字节 
3 
8 
14 
20 
30 
47 
 
要估计数据矩阵符号大小（长度 x 高度），请使用以下过程。 实际结果取决于所使用的打印/标记系
统。 
 
计算要编码的数据字符数____________。 
转到表 D1 获取正方形，或转到 D2 作为矩形数据矩阵符号。 
找到等于或大于字符数的字母数字。 
行= ____________。 
列= ____________。 
单元格/元素大小= ____________。 
乘以行数（d）的单元格/元素大小= ____________宽度。 
将单元数乘以单元格/元素大小= ____________高度。 
静区= 4 x 细胞/元素大小= ____________。 
将安静区域（i）添加到宽度（g）= ____________估计的总宽度。 
将安静区域（i）添加到高度（h）= ____________估计的总高度。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
© 版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
40/59 


### 第 41 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
表 D3 显示了以下参考编码和字符数： 
1.序列号的可追溯性 
2.批次或批次识别的可追溯性 
3.验证（错误证明） 
4.产品标识 
5. VIN（车辆识别号码） 
使用表 D3 来完成示例 1 到 4 以确定数据矩阵符号大小。 
表 D3：参考数据编码 
 
功能 
数据内容 
编码数据语法 
字符计数字母数字
（AN） 
1 
可追溯性 
序列号 
压缩 VPPS GM 零
件编号 
制造或组装场地 
通用定义跟踪码 
 
[)>
R  06
G  Y0000000000000X
G  P123456 
S 
S 
S 
78G 
G 
S12V987654321 S 
TLSYYDDDA2B4C6000
R EO 
S 
T 
 
06 宏＝57an 
没有 06 个宏= 65an 
2 
可追溯批次
或批量 
压缩 VPPS GM 零
件编号 
制造或组装场地 
通用定义跟踪码 
 
[)>R  06G  Y0000000000000XG  P123456 
S 
S 
S 
78G 
G 
S12V987654321 S 
TLSYYDDD@2B4C6000
R EO 
S 
T 
 
06 宏＝57an 
没有 06 个宏= 65an 
3 
验证（防
错） 
压缩 VPPS GM 零
件编号 
制造或组装场地 
儒略日期制造或装配日期 
 
[)>R 
G 
G 
S06 SY0000000000000X SP123456 
78
G  2V987654321
G  4DYYDDD 
R  EO 
S 
S 
S 
T 
 
06 宏＝47an 
没有 06 个宏= 55an 
4 
用儒略制造
日期或装配
日期进行产
品识别 
 
压缩 VPPS GM 零
件编号 
制造或组装场地 
儒略制造或装配日期 
 
[)>
R  06
G  Y0000000000000X
G  P123456 
S 
S 
S 
78
G  2V987654321
G  4DYYDDD 
R  EO 
S 
S 
S 
T 
06 宏 = 47an  
没有 06 宏 = 55 an 
5 
车 辆 识 别 号
码（VIN） 
17 字符车辆识别号 
IA2B4C6D8E0F2G4H6I 
18 an 
 
示例 1：使用 06 宏和方形数据矩阵符号进行零件识别。 
计算要编码的数据字符数：47。 
转到表 D1 获取正方形，或转到 D2 作为矩形数据矩阵符号。 
找到等于或大于字符数的字母数字。 
行= 24。 
列= 24。 
电池/元件尺寸= 0.5 毫米。 
将单元格数/行数（d）乘以单元格/元素大小= 12 毫米宽度。 
按单元格/单元尺寸= 12 毫米高度乘以列数。 
静区= 4 x 电池/元件尺寸= 2 mm。 
将安静区域（i）添加到宽度（g）= 14 mm 估计总宽度。 
将安静区域（i）添加到高度（h）= 14 mm 估计总高度。 
注意：在此示例中，使用 06 宏允许较小的整体图标大小。 如果没有 06 宏，符号将为 26 行 x 26 列，符号大小为 15 x 
15 mm。                          ©版权所有 2010 通用汽车公司保留所有权利 


### 第 42 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
 
 
 
图 D2：使用 06 宏导致小符号大小 
示例 2：带有儒略日期的产品标识，其中曲面为 06 宏矩形数据矩阵。 
计算要编码的数据字符数：47。 
转到表 D1 获取正方形，或转到 D2 作为矩形数据矩阵符号。 
找到等于或大于字符数的字母数字。 
行= 16。 
列= 48。 
电池/元件尺寸= 0.38 毫米。 
将单元格数/行数（d）乘以单元格/元素大小= 6.08 mm 宽度。 
按单元格/单元尺寸= 18.24 mm 高度乘以列数。 
静区= 4 x 电池/元件尺寸= 1.52 mm。 
将安静区域（i）添加到宽度（g）= 7.6 mm 估计总宽度。 
将安静区域（i）添加到高度（h）= 19.76 mm 估计总高度。 
注意：如果没有 06 宏，则符号大小相同，因为 55 个字符需要相同的 16 行 x 48 列。 但是，最佳做法是使用 06 宏
 
 
 
 
 
注意：使用或不使用 06 宏不会影响 16 行 x 48 列的数据矩阵图标大小， 
7.6 x 19.8 毫米。 但是，BIG RULE 是使用 06 宏。 
图 D3：带和不带宏的数据矩阵图标大小 
 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
42/59 


### 第 43 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
示例 3：使用 06 宏和方形数据矩阵符号的可追溯性。 
计算要编码的数据字符数：57。 
转到表 D1 获取正方形，或转到 D2 作为矩形数据矩阵符号。 
找到等于或大于字符数的字母数字。 
行= 26。 
列= 26。 
电池/元件尺寸= 0.5 毫米。 
将单元格数/行数（d）乘以单元格/元素大小= 13 毫米宽度。 
按单元格/单元尺寸= 13 mm 高度乘以列数。 
静区= 4 x 电池/元件尺寸= 2 mm。 
将安静区域（i）添加到宽度（g）= 15 mm 估计总宽度。 
将安静区域（i）添加到高度（h）= 15 mm 估计总高度。 
注意：在此示例中，使用 06 Macro 允许较小的整体图标大小。 没有 06 Macro 32 行 x 32 列，符号大
小为 18 x 18 mm。 
 
图 D4：不使用 06 宏对数据矩阵符号大小有显着影响 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
43/59 
 
 


### 第 44 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
示例 4：车辆识别号（VIN），其限制不超过 14 x 14 mm 
计算要编码的数据字符数= 18 
转到表 D1 中的方形符号：18 行 x 18 列 
总体尺寸= 4 x 单元尺寸+（18 个字符 x 单元尺寸的行数/列数） 
14mm = 4x +（18x）其中 x =单元尺寸 
14mm = 22x f。 x = 0.6363 毫米 
电池尺寸不得超过 0.6363 毫米 
 
转到表 D2 获取矩形符号：12 行 x 26 列 
总宽度尺寸= 4 x 单元尺寸+（18 个字符 x 单元尺寸的数字列） 
14mm = 4x +（26x）其中 x =单元尺寸 
14 毫米= 30 倍 e。 x = 0.4666 毫米 
电池尺寸不得超过 0.4666 毫米 
不需要进行高度计算，因为宽度是限制因素。 
在两次计算之间选择较大的单元格大小，方形符号的单元格大小为 0.6363 mm。 
注意：在此示例中，只有一个数据字段，因此不需要使用 ISO / IEC 15434 数据语法标准或 06 宏。此
外，BIG RULE 使符号尽可能小到实用，方形数据矩阵符号是一个合理的选择。另外，方形符号对于
扫描目的是优选的，并且所得到的单元/元件尺寸的增加改善了读取距离。 
 
 
注意：如果适用，数据矩阵方形符号是可读性的符号。 在该示例中，与用于改善可读性和读取距离的可用区域
的矩形符号（0.4mm）相比，方形符号的单元/元件尺寸更大（0.6mm）。 
图 D5：数据矩阵平方图标 v。 数据矩阵矩形符号 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
44/59 
 
 


### 第 45 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
S 
S 
S 
S 
S 
S 
S 
S 
S 
S 
S 
S 
 
附录 E：GMW15862 可追溯性，验证和部件识别代码结构和内容 
应对订单数据字段进行编码的一般原则是固定长度数据字段，首先是可变长度数据字段。例子：
可追溯性: 宏 06 (Y) VPPS  
G
 
(7Q or Q) 产品特征 
G
 
(P) GM 零件编号  
G
 
(DI or DIs) 供应商数据 
(12V) DUNS 
G
 (T) GM 跟踪结构
G
 
验证:  宏  06  (Y)  VPPS G 
(P) GM  零件编号 G 
(12V)  DUNS  G (4D)   
生产/装配 儒略日期G 
(7Q) 产品特征 G 
(DI or DIs)供应商数据 
 
产品 ID: 宏 06 (P) GM 零件编号 
G
 
(12V) DUNS 
G
 (4D) 生产/装配儒略日期 
G
 
(7Qor Q) 产品特征 
G
 
(DI or DIs) 供应商数据 


### 第 46 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
S 
S 
S 
S 
S 
S 
 
完整 GMW15862 跟踪记录所需的 E1 数据 
GM 分配的零件，部件，模块或组件的零件号（8 位数）。 
制造/组装零件，部件，模块或组件的站点的 DUNS ID。 GMW15862 定义了跟踪代码（图 E1 和 E2） 
编码数据如下所示：合规标题= 
[)>R 
[)>
R
 
格式标题 = 06
G
 
[)>R  06G 
S 
S 
VPPS DI = Y 
[)>RS06GSY 
VPPS 数据 = 0000000000000X 
[)>RS06GSY0000000000000X 
零件编号 DI = P 
[)>
R  06
G  Y0000000000000X
G  P 
S 
S 
S 
零件编号数据 = 12345678 
[)>R  06G  Y0000000000000XG  P12345678 
S 
S 
S 
数据分隔符= 
G
 
[)>
R  06
G  Y0000000000000X
G  P12345678
G
 
S 
S 
S 
S 
DUNS DI =12V 
[)>
R  06
G  Y0000000000000X
G  P12345678
G  12V 
S 
S 
S 
S 
DUNS 数据 =987654321 
[)>
R  06
G  Y0000000000000X
G  P12345678
G  12V987654321 
S 
S 
S 
S 
数据分隔符= 
G
 
[)>R  06G  Y0000000000000XG  P12345678G  12V987654321G 
S 
S 
S 
S 
S 
GM 跟踪代码 DI = T 
[)>
R  06
G  Y0000000000000X
G  P12345678
G  12V987654321
G  T 
S 
S 
S 
S 
S 
GM 跟踪数据= LSYYDDDA2B4C6000 
[)>
R  06
G  Y0000000000000X
G  P12345678
G  12V987654321
G TLSYYDDDA2B4C6000 
S 
S 
S 
S 
S 
记录分隔符= R 
[)>
R  06
G  Y0000000000000X
G  P12345678
G  12V987654321
G TLSYYDDDA2B4C6000
R
 
S 
S 
S 
S 
S 
S 
传输结束= 
EOT 
[)>
R  06
G  Y0000000000000X
G  P12345678
G  12V987654321
G  TLSYYDDDA2B4C6000
R  EO 
S 
S 
S 
S 
S 
S     T 
 
 
 
 
©2010 通用汽车公司版权所有。保留所有权利 
 
 
 
 
图 E1:二维符号合并（验证/产品标识示例） 
- 


### 第 47 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
GM 定义的跟踪结构: 以下是 GM 定义的可跟踪性结构，该跟踪结构将在可追溯性 NOA 中按日期用于所有新零
件，部件，装配体和模块（请参阅标签和文献网页）。 GM 定义的跟踪结构加上 GM 指定的 8 字符部件号加上
制造商/装配现场特定的 DUNS ID 构成完整的可追溯性记录（图 E2）。 
 
图 E2：GMW15862 为所有新程序定义的可追溯性并通过可追溯性分阶段实现现有零件，组件，组件和模块 
 
图 E3：GMW15862 序列号可追溯性要求标签/标记 
 
 
 
 
 
© 版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
46/59 
 
 


### 第 48 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
 
 
图 E4：GMW15862 批次/批次可追溯性要求标签/标记 
 
 
 
图 E5：具有修订级别要求标签/标记的 GMW15862 可追溯性 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
47/59 


### 第 49 页
通用全球工程标准 
GMW15862 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
E2 GMW15862 验证/错误校对和产品 ID 结构 
以下是用于新零件，组件和组件的验证/防错结构。 
 
图 E6：GMW15862 验证/错误校对数据结构 
 
 
 
图 E7：GMW15862 验证/错误校对或产品 ID 标签/标记示例（编码数据[)>R  06G  
Y0000000000000XG  P12345678G  12V987654321G  4DYYDDDR  EO  ) 
S 
S 
S 
S 
S 
S     T 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
48/59 


### 第 50 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
S 
G 
[)>
R
S06
G
 
G 
G 
 
附录 F：在 2D 条形码中附加/添加附加数据 
应遵循以下步骤（示例 1 和 2）将数据附加到 2D 条形码。 或供应商特定数据（供应商部件号，供
应商内部可追溯性代码等）编码遵循 ISO / IEC 15434 高语法容量 AIDC 媒体和 ISO / IEC 15418 信息技
术 - UCC / EAN 应用标识符和事实数据标识符和维护。 
例 1：附加产品特性 - 压力最终测试结果 
场景：供应商和通用汽车发布工程师一致认为，具有最终测试台压力将支持产品匹配申请，并加强保修问题。补
充日期：14.7 帕斯卡。 
数据标识符选择：7Q（见表 A1）。 
在 ANSI X12.3 数据元素字典标准的数据元素编号 355 中定义的两个字符的度量代码单元：4S。 
附加到附录 E 中使用的编码数据结构： 
[)>R  06
G  Y0000000000000XG  P12345678
G   12V987654321
G  TLSYYDDDA2B4C6000
R    EO 
S 
S 
S 
S 
S 
S     T. 
Step 1. Insert data separator 
G
 after the GM defined trace code. 
[)>
R
S06
G
 
SP12345678
G
 12V987654321
G
 TLSYYDDDA2B4C6000
G    . 
SY0000000000000X 
S 
S 
S 
Step 2. Insert Data Identifier 7Q. 
G 
G 
G 
G 
SY0000000000000X SP12345678 S12V987654321 STLSYYDDDA2B4C6000 S7Q. 
Step 3. Insert data value including decimal 14.7. 
SY0000000000000XG 
S 
S 
S 
[)>R
S06G 
SP12345678G   12V987654321G   TLSYYDDDA2B4C6000G   7Q14.7. 
Step 4. Insert qualifier from ANSI X12.3 4S. 
[)>
R
S06
G
 
SP12345678
G
 12V987654321
G
 TLSYYDDDA2B4C6000
G
 7Q14.74S. 
SY0000000000000X 
S 
S 
S 
Step 5. Since this is the last data field, it is closed by the Format Trailer. 
[)>
R
S06
G
 
SP12345678
G
 12V987654321
G
 TLSYYDDDA2B4C6000
G
 7Q14.74S 
R  EO 
SY0000000000000X 
S 
S 
S 
S     T. 
 
 
图 F1：使用嵌入 2D 条形码而不是人类可读信息中的 7Q 数据标识符的产品特征数据的示例标签/标记 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
49/59 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 


### 第 51 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
示例 2：附加供应商数据 - 供应商部件号和序列号 
场景：为了帮助支持其流程，供应商已要求将其部件号和序列号添加到产品 ID 标签中。 由于 GM 政策不是标
签上的供应商数据，因此允许将数据放入 2D 条形码中。 
注意: 对于此示例, 这是产品 ID 标签，2D 条形码中的数据由 GM 分配的部件号，制造/装配站点的 DUNS ID 和
儒略制造日期组成。 
附加的供应商部件号和序列号数据。 选择的数据标识符： 1P and S (参见表 A1). 产品标签 2D 条形码具有以下
数据编码。 
 [)>
R  06
G  Y0000000000000X
G  P12345678
G  12V987654321
G  4DYYDDD
R  EO 
S 
S 
S 
S 
S 
S     T 
 
Step 1. Insert data separator GS after the Julian date field 
[)>RS 06GSY0000000000000XGSP12345678GS12V987654321GS4DYYDDDGS 
Step 2. Insert Data Identifier for supplier part number 1P 
[)>RS  06GSY0000000000000XGSP12345678GS12V987654321GS4DYYDDDGS1P 
Step 3. Insert supplier part number 1A2B3C4D5E6F7G8H9 
[)>RS 06GSY0000000000000XGSP12345678GS12V987654321GS4DYYDDDGS1P1A2B3C4D5E6F7G8H9 
Step 4. Insert data separator GS 
[)>RS 06GSY0000000000000XGSP12345678GS12V987654321GS4DYYDDDGS1P1A2B3C4D5E6F7G8H9GS 
Step 5: Insert Data Identifier for supplier serial number S 
[)>RS 06GSY0000000000000XGSP12345678GS12V987654321GS4DYYDDDGS1P1A2B3C4D5E6F7G8H9GSS 
Step 6. Insert supplier serial number A2B4C000E0 
[)>RS 06GSY0000000000000XGSP12345678GS12V987654321GS4DYYDDDGS1P1A2B3C4D5E6F7G8H9GSSA2B4C000E0 
Step 7. Since this is the last data field it is closed by the Format Trailer 
[)>RS  06GSY0000000000000XGSP12345678GS12V987654321GS4DYYDDDGS1P1A2B3C4D5E6F7G8H9GSSA2B4C000E0RSEOT 
(See Figure F2) 
 
 
注意：供应商数据不会在人类可读信息中打印，而只能在二维条形码中编码。 
图 F2：带有供应商数据的标签/标记示例附加到 2D 条形码数据 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
50/59 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 


### 第 52 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
 
附录 G：“竣工”标签/标记 
 
                      “竣工”标签/标记提供了一种捕获跟踪数据的方法，作为外部装配过程的一部分以及增值装配器（VAA）。 2D 条  
形码被构造为具有需要可追溯性的每个组件的单独跟踪记录。 燃料箱组件由五（5）个可追踪组件组成（图
G1），旨在用作如何使用人类可读信息创建“竣工”2D 标签/标记的示例。 
 
图 G1：由五个可追踪组件组成的燃料箱组件的图示 
燃料箱是与其余四个部件相关联的主要连接。 数据采集系统在组装到油箱时捕获每个组件（表 G1）。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
51/59 


### 第 53 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
 
表 G1：以燃料箱为主链路的每个组件捕获的数据 
 
 
使用捕获的数据，数据被编码为 2D 符号，遵循 ISO / IEC 15434 数据语法标准和记录分隔符字符
R（ASCII ISO / IEC 646 字符十进制 30,1Eh）（图 G2）。 
 
注意：ASCII 不可打印字符（30 十进制，1Eh）用于分隔每条记录。 
图 G2：遵循 ISO / IEC 15434 数据语法标准的 2D 符号的数据编码 
“竣工”标签/标记将附在燃油箱上。 工厂系统将扫描 2D 条形码，并且数据将被组装为组装到
燃料箱的每个部件的完整可追溯性记录。 净效应是在扫描站组装（图 G3）。 
 
 
 
 
 
 
 
 
 
 
 
 
 
© 版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
52/59 
 
 


### 第 54 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
 
 
图 G3：完成“竣工”标签/标记贴在油箱上 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
53/59 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 


### 第 55 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
 
图 G3：完成“竣工”标签/标记贴在油箱上的应用程序 Hix：GM1737 定义的可追溯性和验证/错误打样代码结构..
 
H1 GM1737 可追溯性结构。 （淘汰 - 见可追溯性 NOA） 
以下是可追溯性结构，以前在 GM1737 中定义，用于现有零件，部件，组件和模块。 供应商应该计划根据可追
溯性 NOA 中的时间表更改为第 3 节中详述的 GM 定义跟踪结构。 电子模块将继续使用 GMW4710 的这种结构，
直到电气通用架构发布（图 H1 和 H2）。 
 
 
Figure H1: GM1737 Traceability Structure to be Phased Out per Traceability NOA 
 
 
图 H2：GM1737 20T 可追溯性标签/标记示例（编码数据 20TCI5678VA2B4C6D8E） 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
54/59 
--```,```````````,`,,,,``,```,,-`-


### 第 56 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
H2 GM1737 增强的可追溯性结构。 （淘汰 - 见可追溯性 NOA） 
以下是增强的可追溯性结构，以前在 GM1737 中定义，用于现有零件，组件，组件和模块。 供应商
应根据可追溯性 NOA 中的时间表计划更改为第 3 节中详述的 GM 定义跟踪结构。 电子模块将继续
使用 GMW4710 的这种结构，直到电气通用架构发布（图 H3 和 H4。） 
 
 
图 H3：通过可追溯性 NOA 逐步淘汰的增强型 GM1737 定义的可追溯性结构 
 
图 H4：GM1737 21T 可追溯性标签/标记示例（编码数据 21TCI5678VLS7282A2B） 
 
 
 
 
 
 
© 版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
55/59 


### 第 57 页
通用全球工程标准 
GMW15862 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
H3 GM1737 验证/错误校对结构。 （淘汰 - 见可追溯性 NOA） 
以下是以前在 GM1737 中定义的验证/防错结构，用于现有零件，组件和组件。 （见图 H5 和 H6。）
供应商应根据可追溯性 NOA 中的时间表，计划更改为第 3 节中详述的 GM 定义验证/防错结构。 
 
Figure H5: GM1737 20P Verification/Error Proofing Data Structure 
 
 
图 H6：GM1737 20P 验证/错误校对标签/标记示例（编码数据 20P5678） 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
56/59 


### 第 58 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
附录 J：动力总成可追溯性格式 
 
                         动力总成发布的部件在车辆制造厂或 VAA 上扫描，使用附录 J1（表 J1 至 J3）中所示的格式。 跟踪代码        
定义是与标准 GM 定义跟踪代码格式的唯一区别。 
如果空间允许，在动力总成制造地点扫描的动力总成部件可以在附录 J1（表 J1 至 J3）中示出。 附录 J2
（表 J4 至 J6）中所示的格式应在附录 J1（表 J1 至 J3）中使用，格式不适合该组件。 
 
J1 可追溯性格式，用于由车辆制造工厂或 VAA 扫描的动力总成释放组件 
动力总成跟踪代码定义是与标准 GM 可追溯性内容的唯一区别。 
表 J1：车辆制造厂或 VAA 扫描的动力传动系释放部件的定义 
 
数据定义 
数据特性 
 
DI 
 
编码（使用 DI） 
GM 分配了 VPPS 压缩代码 
14 字母数字 
Y 
Y0000000000000X 
GM 零件编号 
8 数字 
P 
P12345678 
制造或装配现场 DUNS 
9 数字 
12V 
12V987654321 
GM 定义的跟踪代码（用于在制造工厂或 VAA 扫描的动力系统发布的
组件） 
 
16 字母数字 
 
T 
 
TLSYYDDD9AAKX1234 
 
 
表 J2：编码布局显示在车辆制造工厂或 VAA 扫描的动力总成的控制特性和位置 
 
 
 
 
表 J3：由车辆制造工厂或 VAA 扫描的动力系统的 GM 定义跟踪代码
 
位置 14 的默认字符为“X”。 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
57/59 


### 第 59 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
 
 
图 J1: 车辆制造厂或 VAA（ATK95040）扫描的动力总成标签布局 
 
 
图 2：符合标准的传输标签示例 
 
 
 
 
 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
58/59 
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`--- 


### 第 60 页
通用全球工程标准 
GMW15862 
通用汽车公司版权所有 
由通用汽车公司授权的 IHS 公司提供 
未经 IHS 许可不得复制或联网 
 
被许可人= Trw LOC 14 德国杜塞尔多夫/ ********** 不
转售，01 / 06 / 2011 01:57∶43 MST 
 
 
 
J2 动力总成发布组件的 J2 可追溯性格式在动力总成制造工厂中扫描 
表 J4：动力总成工厂扫描的动力总成编码定义 
数据定
义 
数据特征 
 
DI 
 
编码 
GM 定义跟踪代码（用于动力系统发布的动
力总成工厂扫描的组件） 
 
16 字母数字 
 
T 
 
PCLL936512340000 
 
表 J5：编码布局显示动力总成工厂扫描的动力总成释放组件的 DI 放置和数据 
 
 
 
 
表 J6：动力总成工厂扫描的动力总成释放部件的 GM 定义跟踪代码 
 
 
J2.1 跟踪数据字段。 动力总成跟踪代码中的七个数据字段标识如下： 
程序 ID，组件类型和站点 ID 应由动力总成发布工程师提供。 动力总成工程师可以参考标准 ID 列表的全球
质量要求（GQR）120.57。 
构建年份是日历年的最后一位数 
附录 B 的儒略日期 
序列号为四位数，左边用零填充。 序列在每天开始时以零（0）开始。 
对于程序中的所有站点，可选字符的格式应相同。 如果未使用可选字符，则在字段 13 到 16 中放置零。 
J2.2 人类可读内容。 对于格式 J2，只要人类可读字符清晰可读，人类可读内容的布局就是灵活的。 
 
 
 
 
 
 
 
©版权所有 2010 通用汽车公司保留所有权利 
 
2010 年 11 月 
59/59 

