# GMW_3122_EN_2005-11_具有双线连接的控制器和物理层接口的子模块技术说明.pdf

## 文档信息
- 标题：Microsoft Word - GMW3122Nov2005.doc
- 作者：zz0kjt
- 页数：42

## 文档内容
### 第 1 页
 
 
 
 
 
 
 
 
 
 
WORDLWIDE 
ENGINEERING 
STANDARDS 
General Specification 
Electrical/Electronic 
GMW3122 
 
 
 
 
 
 
 
 
 
Dual Wire CAN Physical Layer and Data Link Layer Specification 
 
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Originating Department: North American Engineering Standards 
Page 1 of 42
 
1 Introduction 
1.1 Scope. This document specifies the physical 
layer requirements for a Carrier Sense Multiple 
Access/Collision Resolution (CSMA/CR) data link 
which operates on a dual wire medium to 
communicate among Electronic Control Units 
(ECU) on road vehicles at normal transmission 
rates of: 
High Speed Bus 
500 kbit/sec 
Medium Speed Bus 95.24 and 125 kbit/sec 
This document is to be referenced by the particular 
Component Technical Specification (CTS) which 
describes any given ECU in which the dual wire 
data link controller and physical layer interface is 
located. The performance of the data link layer and 
physical layer is specified in this document. ECU 
environmental and other requirements shall be 
provided 
in 
the 
Component 
Technical 
Specification. Functional as well as parameter 
requirements in this document generally apply over 
the applicable operating conditions and aging. This 
includes, 
for 
example, 
operating 
ambient 
temperature, operating supply voltage and age drift 
over component life time unless otherwise noted. 
The intended audience includes, but is not limited 
to, ECU suppliers, component release engineers, 
platform system engineers and semiconductor 
manufacturers 
of 
CAN 
controller 
and 
CAN 
transceiver ICs. 
1.2 Mission/Theme. This specification describes 
the physical layer requirements for a dual wire data 
link capable of operating with CSMA/CR protocols 
such as CAN version 2.0A (standard frame 
format). All ECUs shall tolerate CAN version 2.0B 
(29 bit identifier extended frame format) messages, 
i.e. ECUs may not disturb such messages unless 
bit errors were detected. This serial data link 
network is intended for use in applications where a 
high data rate is required. 
1.3 Requirement Wording Conventions. Within 
this document the following conventions are 
applied: 
The word “Shall” shall be used in the following 
ways: 
a. To state a binding requirement on the network 
or the nodes which comprise the network, 
which is verifiable by external manipulation 
and/or observation of a node or the network. 
b. To state a binding requirement upon a node’s 
requirements document that is verifiable 
through a review of the document. 
The word “Must” shall be used to state a binding 
requirement 
upon 
nodes 
on 
the 
network 
components which will have a corresponding ECU 
or component requirements document. These 
requirements will be verified as part of the 
component verification. 
The word “Should” denotes a preference or 
desired conformance. 
Note: In the event of a conflict between the text of 
this specification and the documents cited herein, 
the text of this specification takes precedence. 
Note: Nothing in this specification supersedes 
applicable laws and regulations unless a specific 
exemption has been obtained. 
2 References 
Note: Only the latest approved standards are 
applicable unless otherwise specified. 
2.1 External Standards/Specifications. 
ISO 11898-1 
ISO 21848-2 
ISO 11898-2 
SAE J2284-3 
ISO 16845 
SAE J2284-1 
2.2 GM Standards/Specifications. 
GME6718 
GMW3097 
GME14010 
GMW3172 
GMW3001 
GMW3173 
GMW3059 
GMW3191 
GMW3091 
GMW8763 
3 Requirements 
3.1 Physical and Data Link Layer 
Characteristics. 
3.1.1 Data link layer and two-wire physical layer 
according to ISO 11898 (high-speed CAN physical 
layer). 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 2 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 2 of 42 
November 2005
 
3.1.2 Capable of operating with CAN 2.0A protocol 
messages and tolerates CAN 2.0B protocol 
messages. If platform-specific documents or 
SSTS/CTS require conformance to CAN 2.0B, then 
the applicable interfaces shall be capable to 
transmit and receive standard as well as extended 
frame format messages at any time during 
operation, that is support the protocol CAN 2.0B 
running in mixed-mode operation. 
3.1.3 Supports the enhanced protocol for extended 
clock tolerance 
3.1.4 Only performs bit re-synchronization on 
recessive to dominant bus signal edges 
3.1.5 Meets GMW3173 GMLAN architecture and 
bus wiring requirements 
3.1.6 
Complies 
to 
GMW3097GS 
and 
GMW3091GS EMC requirements 
3.1.7 Intended to operate at ground offset voltages 
of up to 2V temporarily 
3.2 Bus Operation. 
3.2.1 General Requirements on Bus Operation 
for 12V-Powered Devices. Per default the bus 
network when awake shall be fully functional - i.e. 
nodes shall be able to transmit and receive data - 
when the supply voltage at the ECU power input 
pins is within a range as specified in GMW3172 
and/or the applicable CTS and/or SSTS document. 
Note: In any case bus communication shall be 
supported down to (at least) 9.0 V as measured at 
the ECU power/ground input pins.  
Note: 
The 
requirement 
“shall 
support 
communication” does not imply that valid sensor 
data or actuator function must be supported at this 
voltage.  
Note: Those devices which are involved in the 
vehicle immobilization function in some fashion 
typically need to support bus communication down 
to a supply voltage of 6.0 V. 
If local ECU supply voltage had decreased below 
the specified minimum value, then the ECU shall 
resume bus receive and transmit capability within a 
time as specified in the ECU Component Technical 
Specification. The time is measured from the point 
in time when the voltage increases to a level of 
0.5 V higher than the specified minimum supply 
voltage until bus receive and transmit capability is 
resumed. If no time is specified in the CTS, then 
the ECU shall resume bus receive and transmit 
capability within trsm (See section 3.10). In 
addition, if local ECU supply voltage decreases 
below the specified minimum value, then the ECU 
shall continue to support bus receive and transmit 
capability for a time of t > 2 ms.  
Note: Operation at battery voltages of less than 
approximately 6.5 V typically implies usage of a 
low-dropout voltage regulator. 
The bus network shall support communication 
between powered nodes even when only two of 
the nodes on each network are sufficiently 
powered and the rest of the nodes are non-
powered or under powered. Under powered is 
when battery voltage at the ECU power input pins 
is within the range of 0 V to 6.0 V unless otherwise 
specified, see applicable SSTS and/or CTS. 
Nodes that are not able to communicate without 
disturbing the communication between other 
nodes, e.g. due to non-powered or under powered 
conditions, shall not be allowed to communicate. 
Nodes shall, under these conditions, close down 
communication circuits in a controlled manner. 
Nodes that are in sleep condition shall not disturb 
communication between other nodes.  
In addition, ECU’s that transmit and receive 
message frames which are required to support 
engine start such as immobilizer data or Crank 
command shall support communication in the 
range of 16 V to 18 V for 1 hour and 18 V to 26.5 V 
for 1 minute (jump start condition) unless otherwise 
specified (see, e.g., GMW3172GS). 
3.2.2 General Requirements on Bus Operation 
for 42V-Powered Devices. If an ECU is supplied 
exclusively by 42V nominal power as main supply, 
then it shall comply to the following requirements 
on bus operation: 
3.2.2.1 The bus network when awake shall be fully 
functional (i.e., device shall be able to transmit and 
receive data) when the battery voltage at the ECU 
power input pins is within the range of 21.0 V to 
50.0 V. 
3.2.2.2 Devices that are relevant for vehicle 
functions at crank and/or which are not permitted 
to perform a reset or re-initialization during an 
engine Start & Stop cycle, shall be fully functional 
(i.e., devices shall be able to transmit and receive 
data) when a supply voltage starting profile 
according to ISO 21848-2 is applied. The ECU 
shall support error-free bus communication during 
and after the complete ISO 21848-2 starting 
profile.  
Devices, that are not relevant to vehicle functions 
during crank and/or that are allowed to perform 
reset or re-initialization during an engine Start & 
Stop cycle, shall not cause error conditions to 
occur on the bus, i.e., there shall be no error 
frames during and after the application of the crank 
pulse. The device shall resume bus communication 
within t ≤ trsm after the supply voltage has reached 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 3 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 3 of 42
 
a level of 21 V unless otherwise specified in the 
applicable SSTS or CTS. 
3.2.2.3 High Speed Bus networks shall operate if 
only two of the nodes on each network are 
sufficiently powered and the rest of the nodes are 
not powered or are in a low supply voltage 
condition. Low supply voltage in this context 
means the battery voltage at the ECU power input 
pins is less than 21.0 V. 
3.2.2.4 High Speed Bus Nodes that are not able to 
communicate without disturbing the communication 
of other nodes while in low supply voltage 
condition 
shall 
automatically 
disable 
communication under that condition. Such nodes 
shall close down their communication function in a 
controlled manner while in low supply voltage 
condition. 
3.2.2.5 Nodes that are in sleep condition shall not 
disturb communication of other nodes. 
3.2.2.6 Physical interface drivers which are not 
powered or under-powered shall not disturb the 
communication on the network. 
3.2.3 Bus Operation During Crank. Table 1 
describes the bus network functionality during 
crank. Please refer to the applicable CTS, SSTS or 
platform-specific technical document to determine 
which devices are required to support this function 
during crank.  
Note: Each ECU that is supposed to contribute 
data for engine start purposes (e.g., immobilizer 
data) needs to support bus transmission function 
during Crank, depending on platform- or vehicle-
specific requirements. 
Voltage levels and timing of the Crank pulse are 
specified in GMW3097GS and GMW8763 (PPEI). 
Table 1: ECU Functional Status at Crank 
Period 
Description 
Run/Crank Transition 
The bus network shall be fully functional, nodes shall be able to transmit and receive 
data 
Crank1 
Bus functionality during this period shall be stated in the Component Technical 
Specification. Under no circumstances shall any node disturb ongoing bus 
communication. Ongoing message transmissions should be concluded normally, e.g. 
without causing error conditions on the bus. 
Crank2 
See Crank1 
Crank3 
Upon return of power nodes shall resume data receive and transmit function within a 
time period being specified in the Component Technical Specification. If such time is 
not specified in the CTS and/or SSTS, then the node shall resume receive/transmit 
function within t ≤ trsm (See section 3.10). Under no circumstances shall any node 
disturb ongoing bus communication, e.g. due to start-up initiation.  
Crank/Run Transition 
The bus network shall be fully functional, nodes shall be able to transmit and receive 
data 
 
******* Transferred 42V Crank Pulse To 14V 
Side (e.g., via 42V/14V DC/DC converter). All 
designated ECU’s shall be fully functional (i.e., 
device shall be able to transmit and receive data 
on high-speed CAN) when a test pulse according 
to the table below is applied. Designated ECUs 
shall support error-free bus communication and are 
not allowed to perform reset or re-initialization 
during and after the complete test pulse. 
Note: See the CTS, SSTS and or platform-specific 
technical document to determine which devices 
shall support operation while a transferred crank 
pulse is present. 
 
Nominal Voltage [V] 
Minimum Pulse Voltage [V] 
Pulse Duration [Seconds] 
Rise and Fall Time [ms] 
13.8 
7 
1 
5 
 
3.2.4 Tolerance of the CAN Bit Time. Any CAN 
interface of an ECU must be compliant to an 
overall tolerance of the CAN bit time length as 
specified in 4.1 (5.1). That is, CAN bits being 
transmitted by an ECU shall meet this tolerance 
requirement and each ECU shall be capable to 
receive messages from other ECUs which meet 
this requirement. The tolerance value is applicable 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 4 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 4 of 42 
November 2005
 
over 
operating 
conditions 
and 
aging, 
e.g., 
temperature, supply voltage and age drift over 
specified vehicle temperature including component 
life time. This is to ensure proper operation of the 
network, e.g., with respect to the CAN bus 
resynchronization function. 
Careful analysis of the bit time tolerance is 
recommended when ceramic resonators and/or 
PLL 
clocks 
are 
considered. 
The 
permitted 
tolerance of the oscillator circuit is reduced when a 
PLL clock is used for the CAN data link layer 
controller. For example, the suitable oscillator 
tolerance would be 0.1%, for the case where the 
PLL circuit would exhibit an add-on tolerance of 
0.35% (at 125 or 95.2 kbit/s: 0.4%). 
When a ceramic resonator with a maximum 
tolerance of 0.3% is used with a PLL, the add-on 
clock tolerance of the PLL is limited to 0.15% (at 
125 or 95.2 kbit/s:  0.2%) over a single bit time. At 
a bus speed of 500 bit/s, this is equivalent to 
0.15% of 2 µs, or 3 ns maximum jitter over 2 µs. At 
a bus speed of 125 kbit/s, this is equivalent to 
0.2% of 8 µs, or 16 ns max jitter over 8 µs. At a 
bus speed of 95.2 kbit/s, this is equivalent to 0.2% 
of 10.5 µs, or 21 ns max jitter over 10.5 µs. 
3.3 Wake Up Techniques. Dual wire CAN allows 
three types of ECU awake/sleep techniques. If a 
wakeup technique is used then it must conform to 
GMW3097GS EMC requirements. 
a. Selective awake by an awake pulse. When 
used, it will be possible to communicate on the 
dual wire CAN network without forcing nodes 
that are not needed to stay awake. For details 
see section 3.3.1 
b. Non-selective wakeup on presence of CAN 
bus communication. When used, it will force 
nodes to stay awake as long as ongoing 
communication 
occurs. 
This 
is 
the 
recommended concept for new designs. For 
details see section 3.3.2. 
Attention: this concept requires to employ CAN 
transceiver products which support node wakeup 
upon bus traffic, see 3.11.1.2.11. 
c. Non-selective awake by a continuous high 
level 
discrete 
signal, 
typically 
called 
“Communication Enable”. When used, it will 
force nodes to stay awake as long as the 
discrete signal is at a high level (example: 
Ignition signal). For details see section 3.3.3. 
Usage of awake/sleep techniques are optional. 
Whether and how the above approaches shall be 
used is to be defined in the applicable platform-
specific data bus implementation document. Note 
more than one of the above concepts can apply to 
a particular ECU or network. For example, it may 
be necessary in a subnet for an ECU to be present 
which supports wakeup on bus traffic (technique 2) 
and in addition to control the discrete wakeup line 
(technique 3). 
3.3.1 ECU Selective Awake Using a Wake Up 
Wire. ECU selective awake is accomplished by a 
dedicated wire, a wake up wire. 
The wake up wire is a common wire for all ECU’s 
connected on the same bus. Each ECU needs one 
pin assigned for this function. The below parameter 
specifications of the wake up wire concept support 
connection of up to 22 ECU’s. The wake up wire 
signal is an input/output interface, i.e., the ECU 
can send and receive the wake up signal on the 
same wire. 
******* Concept Description. 
*******.1 The wake up output requirements are: 
*******.1.1 The wake up voltage Vtwu shall be 
applied on the wake up wire for a time ttwuo. 
*******.1.2 Wake up is generated as a hardware 
signal on the wake up wire. The ECU generating 
this signal shall wake up or notify all ECU’s 
connected on the same bus. 
*******.2 The wake up input requirements are: 
*******.2.1 An ECU currently in sleep mode, that 
detects a voltage in excess of Vrwu for a time 
longer than trwui applied on the wake up wire, shall 
switch to active mode. 
*******.2.2 The wake up pulse shall also affect an 
ECU in active mode, not only an ECU that is in 
sleep mode, i.e., an ECU that is in active mode 
shall be able to detect the wake up pulse. 
*******.2.3 Each ECU shall be able to generate 
wake up pulses and detect wake up pulses on the 
wake up wire. 
******* Wake Up Wire Basic Requirements. 
*******.1 An ECU should not change power modes 
when subjected to GMW3097GS EMC conditions. 
For 
example, 
the 
ECU 
shall 
not 
take 
conducted/coupled immunity test conditions as 
valid wake up or go to sleep events. 
*******.2 Fault tolerant modes: 
*******.2.1 ECU power loss. An ECU shall not 
interfere with wake up function among other ECUs 
during a loss of power or low supply voltage 
condition. Upon return of power, normal wake up 
operation shall resume without any operator 
intervention within a time period being specified by 
the ECU Component Technical Specification. If a 
time period is not specified in the CTS, then the 
ECU shall resume operation within t ≤ trsm. (See 
Section 3.10). 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 5 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 5 of 42
 
*******.2.2 Wake up wire short to ground. Wake up 
function may be interrupted but there shall be no 
damage to any ECU when the wake up wire is 
shorted to ground or to a negative voltage down to 
Vwu = –5 V. Upon removal of the wiring fault, 
normal wake up operation shall resume without 
any operator intervention within a time period being 
specified by the ECU Component Technical 
Specification. If a time period is not specified in the 
CTS, then the ECU shall resume normal operation 
within t ≤ trsm. 
*******.2.3 Wake up wire shorted to battery 
voltage. Wake up function may be interrupted but 
there shall be no damage to any ECU when the 
wake up wire is shorted to a positive battery 
voltage of Vwu = 16 V without time limit, 18 V for 
1 h and 26.5 V for 1 minute. Upon removal of the 
wiring fault, normal wake up operation shall 
resume without any operator intervention within a 
time 
period 
being 
specified 
by 
the 
ECU 
Component Technical Specification. If a time 
period is not specified in the CTS, then the ECU 
shall resume normal operation within t ≤ trsm. 
*******.3 The ECU´s wake up input/output function 
shall be fully functional when the ECU is powered 
with a supply voltage of Vs = 6.5 V to 16.0 V. 
When the ECU supply voltage has decreased 
below Vs = 6.5 V, then the wake up function shall 
resume operation when the supply voltage reaches 
a level of Vs = 7 V or higher.  
Note: This operating supply voltage range for the 
wake up wire function only applies if not otherwise 
specified in the applicable SSTS or CTS. 
 
 
n <= 22
ECU1
ECUn
ECU3
ECU2
Wake up wire
 
Figure 1: Wake Up Wire 
 
3.3.1.3 Wake Up Output Requirements. Note, 
parameter specifications apply over operating 
conditions and aging, e.g. temperature, supply 
voltage and age drift over specified vehicle 
temperature and component life time unless 
otherwise noted. 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 6 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 6 of 42 
November 2005
 
 
Table 2: Wake Up Wire Output Parameter Specifications 
Conditions: 6.0 V ≤ Vs ≤ 18 V, -40oC < Tamb < TambMax unless otherwise noted. TambMax shall be specified in the 
CTS. 
Parameter 
Conditions 
Symbol 
Min. 
Nom. 
Max. 
Unit 
Low 
Level 
Output 
Voltage 
RL > 100 kΩ, 
Vwuol 
- 0.3 
0 
+0.3 
V 
High 
Level 
Output 
Voltage 
 
RL = 240 Ω 
Vwuoh 
Min {9, (Vs 
– 1.7)} 
Vs 
Max 
{16,(Vs + 
0.3)} 
V 
Short-circuit 
output 
current 
Vwu = 0V 
Vwuohsc 
25 
 
250 
mA 
Transmit Wake up pulse 
length  
See 
Verification 
of 
Wake 
up 
pulse, 
section ******* 
twuo 
400 
500 
600 
ms 
Input capacitance 
 
CLWU 
 
 
20 
nF 
 
3.3.1.4 Wake Up Input Requirements. 
 
Table 3: Wake-Up Wire Input Parameter Specifications 
Conditions: 6.0 V ≤ Vs ≤ 18 V, -40oC < Tamb < TambMax unless otherwise noted. TambMax shall be specified in the 
CTS. 
Parameter 
Conditions 
Symbol 
Min. 
Nom. 
Max. 
Unit 
Low level wake up input 
voltage 
 
Vwuil 
-2.0 
0 
+2.0 
V 
High level wake up input 
voltage  
 
Vwuih 
4.5 
VS 
16 
V 
Wake up input filter time  See Verification, section 
6.2.2.2 
twui 
0.1 
 
2.0 
ms 
Low Level Input Current 
Vwu = VwuilMax = 2.0 V 
Iil 
0.1 
0.24 
0.35 
mA 
High Level Input Current Vwu = VbattNom = 12 V, Note 1 Iih 
1.2 
1.4 
2.0 
mA 
Input capacitance  
 
Ci 
0 
 
1 
nF 
Note 1: This requirement can be implemented, for example, using a pull down resistance to ECU ground potential, see Appendix C. 
 
3.3.2 Wake Up on Bus Activity. Dual wire CAN 
transceivers which support this wake up method 
have to provide a low power standby/power down 
mode. The µC sets the transceiver to standby 
before it enters standby /power down mode. Wake 
up shall be performed by an ECU upon detection 
of any activity on the bus. In standby mode the 
transceivers 
of 
the 
involved 
ECU’s 
detect 
recessive to dominant transitions and generate a 
wake up signal to the µC. Note it is necessary to 
apply appropriate bus input signal filtering against 
inadvertent wake-up’s. Specifically, devices shall. 
a. Devices shall not wake up when transient bus 
dominant conditions occur with a duration of 
less or equal to tfwu_min = 0.75 µs. This means 
the device shall continue to meet the quiescent 
current limit for sleep mode. 
b. Devices shall wakeup if a valid bus traffic 
detection condition is present, that is the bus 
has been in “dominant” condition for a duration 
of at least tfwu_max = 5 µs. It is acceptable to 
utilize devices that require multiple transitions 
from recessive to dominant to recognize a valid 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 7 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 7 of 42
 
wakeup event within a single bus frame; 
however, the device shall not require more 
than two recessive to dominant or dominant to 
recessive transitions. 
c. When the bus is in dominant state at the point 
in time when the ECU attempts to enter a low 
power mode, then the ECU shall enter the low 
power mode and shall remain in the low power 
mode until there is a valid wakeup condition. In 
this case a valid wakeup condition requires at 
least one recessive to dominant transition on 
the bus, unless otherwise explicitly specified 
for a particular ECU. 
3.3.2.1 Requirements On Valid Wakeup Request 
Messages. It is important to note a bus frame must 
meet certain characteristics in order to enable 
reliable wakeup request functionality. The frame 
that is used for the purpose of bus wakeup request 
must contain the pattern as specified below: 
3.3.2.1.1 The frame must contain at least 2 
instances consisting of at least 3 consecutive 
dominant bits separated by a pattern that includes 
at least one phase consisting of at least 3 
consecutive recessive bits. At data rates higher 
than 500 kbit/s the specified phases have to 
contain more than the above described number of 
consecutive bits. 
3.3.2.1.2 The frame must not contain any data 
bytes. Exception: If an ECU-specific frame header 
is utilized for the purpose to facilitate bus wakeup 
requests, then presence of data bytes is permitted. 
Note: The above described pattern may be located 
anywhere in the transmitted bus frame, e.g., 
including ID field, DLC, fixed form bits, CRC, SOF 
and stuff bits. 
3.3.3 Wake Up on Continuous High Level 
Discrete Input. This approach foresees that ECUs 
shall enable/disable the communication function of 
the primary HS-GMLAN interface based on the 
voltage level of a dedicated input. There is (at 
least) one ECU which controls the voltage on this 
dedicated wake up line. Devices which provide 
output control capability for the dedicated wakeup 
line shall also provide the input function specified 
in this section. Note it is possible that only a subset 
of ECUs of a subnet is connected to the wake up 
line. The Component Technical Specification 
specifies whether the ECU shall have a wake up 
output function and/or whether it shall have a wake 
up detection input function. For ESD protection 
requirements refer to GMW3097. Wake up outputs 
and inputs shall be proof against short circuits to 
any voltage between –3 V and +26.5 V. 
ECUs which shall be able to request wake up of 
the network shall meet the following requirements 
at the particular output: 
In principle the wake up wire output shall provide 
open collector characteristic. When asserted the 
ECU shall typically output battery voltage level. 
When not asserted the ECU shall exhibit a weak 
low state. 
Note: 
Parameter 
specifications 
apply 
over 
operating conditions and aging, e.g. temperature, 
supply voltage and age drift over specified vehicle 
temperature and component life time unless 
otherwise noted. 
ECUs which shall provide wake up input detection 
capability shall meet the following requirements at 
this particular input: 
The ECU shall have a logic input that connects to 
the continuous high level signal. This input shall be 
used for detecting presence of a wake up request. 
In principle the wake up wire input shall provide 
weak pull down to ground potential characteristic. 
 
Table 4: Wake up line output requirements 
Parameter 
Conditions 
Symbol 
Min. 
Nom. 
Max. 
Unit 
Active 
State 
Output 
Voltage 
RL = 240 Ω 
Vwuoh 
Vs – 1.0 
Vs 
Max {26.5,
(Vs + 0.3)}
V 
Active State Output 
Current 
Vs ≥ 9 V 
IOh 
50 
 
200 
mA 
Active State Short-Circuit 
Output Current 
Vwu 
= 
0 V 
and/or 
Vwu = 26.5 V 
Vwuohsc 
- 
 
250 
mA 
Inactive 
State 
Output 
Leakage Current 
Vwu = +12 V 
ILEAKmax 
-10 
0 
+10 
uA 
Conditions: 6.0 V ≤ Vs ≤ 26.5 V, -40
oC < Tamb < TambMax unless otherwise noted. TambMax shall be specified in the CTS. 
Vwu corresponds to the voltage at the Communication Enable I/O pin of the ECU. 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 8 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 8 of 42 
November 2005
 
Table 5: Wake up line input requirements 
Parameter 
Conditions 
Symbol 
Min. 
Nom. 
Max. 
Unit 
High Level Input Current Vwu = VsNom = 12V 
Iih 
1.2 
1.4 
2.0 
mA 
Low Level Input Current 
Vwu = 2.0V 
Iil 
0.1 
0.24 
0.35 
mA 
High Level Wake Up 
Input Voltage  
 
Vwuih 
 
4.0 
Vs  
26.5 
V 
Low Level Wake Up 
Input Voltage 
 
Vwuil 
-2.0 
0 
2.0 
V 
Edge Input Recognition 
Time  
See 
Verification, 
section 6.2.2.2  
twui 
1 
- 
40 
ms 
Conditions: 6.0 V ≤ Vs ≤ 26.5 V, -40
oC < Tamb < TambMax unless otherwise noted. TambMax shall be specified in the CTS. 
For an example circuit see appendix C. 
3.4 ECU Requirements. ECU requirements for 
high speed CAN shall be according to SAE J2284-
3. ECU requirements for medium speed CAN shall 
be according to ISO 11898 (part 1 and part 2, 
High-Speed Medium Access Unit).  
In addition, a medium speed ECU designated for 
operation at 125 kbit/s shall comply with SAE 
J2284-1 
unless 
otherwise 
specified 
in 
this 
document. 
All ECUs shall be proof against the following 
conditions being applied to the bus lines, CANH 
and CANL, unless an explicit exemption was made 
per applicable subsystem technical specification or 
platform-specific technical document. 
Table 6: ECU Absolute Maximum Ratings Note 1, Note 2 
Parameter 
Symbol 
Min 
Max 
Unit 
Conditions/Comment 
CANH and CANL DC voltage VCAN_DC 
-5 
+18 
V 
No deviations allowed after removal of 
condition No time limit, all modes (e.g., 
normal and sleep), recessive and dominant 
bus output states Note 3 
CANH and CANL voltage for  
1 minute 
VCAN_1m 
- 
+26.5 
V 
No deviations after removal of condition. 
Time limit 1 min., all modes, recessive and 
dominant bus output states. 
CANH and CANL voltage for  
10 milliseconds 
VCAN_10ms - 
+40 
V 
No deviations after removal of condition. 
Time limit 10 ms, all modes, recessive and 
dominant bus output states. 
Source voltage at coupling of 
conducted 
transients 
to 
CANH and CANL lines, see 
GMW3097GS, 
section 
3.2.1.3.3 
VCAN_trn 
-150 
+100 
V 
1.No deviations allowed after removal of 
condition.  
2. No functional deviations allowed during 
exposure. 
All operating modes, recessive and 
dominant bus output states. 
ESD protection for CANH 
and CANL 
VCAN_ESD 
-4 
+4 
kV 
HBM contact discharge, powered and 
unpowered ; additional requirements may 
apply, e.g., per GMW3097GS. Note 4 
Note 1: In addition, the device shall survive the interruption of its connection to ground without time limit at battery voltages up to 16V. 
Note in that case a (locally) negative voltage is present at the bus pins relative to the locally disconnected ECU ground pin. 
Note 2: When the ECU is in low-power mode, the bus outputs CAN_H and CAN_L shall output a voltage of 0V nominal to the bus via an 
output resistance as specified in ISO 11898-2 (see parameter internal resistance of CAN_H and CAN_L). 
Note 3: If the physical bus interface was subjected to an overtemperature shutdown condition, then it shall resume regular operation within 
10 s unless a component-specific exception were made per CTS. 
Note 4: For more detailed EMC & ESD requirements and test conditions applicable to the CAN bus interface of an ECU see GMW3097GS 
specifications. Note there are requirements on ESD for packaging and handling in section *******.3 and ESD during operation of the 
device (power-on mode) in sections *******.1 and *******.2. 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 9 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 9 of 42
 
3.5 Bus Termination. Each network needs to be 
equipped with two bus termination units. For high-
speed CAN buses crossing the PPEI border per 
default one bus termination shall be implemented 
on the platform side and one termination on the 
powertrain side unless otherwise specified. Per 
default this circuit shall be implemented in every 
ECM/PCM component unless explicitly otherwise 
specified. Bus termination characteristics shall 
meet the SAE J2284-3 specification unless 
otherwise specified in this document. All electronic 
control units shall package protect for a bus 
termination circuit on the PCB unless otherwise 
specified per CTS and/or SSTS. Whether the bus 
termination components (R1, R2, C1) are to be 
populated in a certain control unit shall be specified 
in 
the 
applicable 
Sub-System 
Technical 
Specification (SSTS), CTS or platform-specific 
technical document. 
When the ECU is in low power mode, the bus 
termination unit shall not drive a certain common 
mode voltage onto the bus, i.e. the output Vsplit 
shall be in high impedance state with an output 
leakage of less than ± 100 uA for –3V < V_CAN_H/L < 
+16V. Note the differential resistance as measured 
between CANH and CANL shall always be 
present, also in low-power mode. 
The bus interface and termination circuit shall be 
applied as shown below.  
Important note: If a bus termination shall be 
placed outside of a control unit (e.g. in the wiring 
harness), then a different circuit applies, see 
section “Bus Termination in the Wiring Harness” 
below. 
CANH
CANL
EMC Caps
L1
R1
R2
C1
Bus Termination 
Unit
Two termination units
present in a subnet
Common-
mode
Choke
CAN-
Transceiver
(ISO 11898)
C2
C3
GND
D1
D2
ESD prot.
(if needed)
 
Figure 2: CAN Bus Application Circuit 
Note: The length of the traces CANH and CANL 
should be as short as feasible, e.g., l< 10 cm to 
minimize EMC hazard. 
Note: For a device there are 3 bus termination 
choices including regular, secondary, and no 
termination. Please refer to the applicable platform-
specific technical document which devices shall 
perform with kind of bus termination function. 
The requirements on the Common Mode Choke L1 
are specified in section 3.9.1. The center tap of the 
termination unit shall be connected to the 
reference voltage output of the bus transceiver IC if 
such 
mechanization 
is 
suggested 
by 
the 
semiconductor manufacturer for the particular 
transceiver product. Note, this path may need to be 
interrupted 
if 
the 
used 
transceiver 
product 
changes. 
Capacitors C2 and C3 shall be chosen as follows: 
40 pF < Cin_nom < 100 pF, tolerance less or equal to 
10%, working voltage 100 V or higher. The actual 
capacitance value of both parts shall be close to 
each other to optimize EMC behaviors (see 
Section 4.1). If a special ESD protection circuit is 
used (see Section 3.7), then C2 and C3 might 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 10 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 10 of 42 
November 2005
 
have to be chosen with a lower value or be 
omitted, depending on the capacitance of the 
protection circuit. Both capacitors shall be placed 
close to the bus connector with minimum trace 
length. 
If ESD protection elements are needed acceptable 
parts are PESD24VS2UAT or MMBZ27VCLT1 or 
equivalent. The capacitance of the ESD protection 
elements shall be less than or equal to 50 pF. 
3.5.1 Regular Bus Termination.  
3.5.1.1 The value of the resistor configuration R1 + 
R2 shall be as specified in SAE J2284-3. The 
nominal value of R1 and R2 shall be between 60 Ω 
and 62 Ω (regular bus termination). Note, this 
allows the following IEC 60063 1% resistance 
values: 60.4, 61.9 Ohms. Note each of resistances 
R1 and R2 is equivalent to half of the bus 
termination resistance RL being specified in SAE 
J2284-3. 
******* The bus termination resistors R1 and R2 
shall meet an initial tolerance of the resistance 
value of ± 1%. In addition, the resistance of R1 
shall be equal to the resistance of R2 with a 
tolerance of 3% over vehicle lifetime. The power 
dissipation rating of R1 and R2 shall be at least P = 
250 mW over the ECU’s operating ambient 
temperature 
range. 
For 
applications 
in 
the 
passenger 
compartment 
this 
is 
typically 
implemented by using a resistor type with 
P = 400 mW nominal rating. At higher ambient 
temperatures (e.g., underhood etc.) the nominal 
power rating shall be adapted accordingly. The 
electronic components R1, R2 and C1 shall be 
suited for high frequency applications. (E.g., type 
MMA 0204 HF (BC Components) or similar parts.) 
******* The nominal capacitance value for C1 is 
100 nF with a working voltage of 50 V or higher. 
C1 shall be suited for high frequency applications, 
e.g., type ‘NP0’ or similar. C1 shall be connected to 
the GND terminal via a low-inductance trace. This 
trace shall not be used for supplying noisy parts 
such as microcontrollers, PWM mode units and 
oscillator circuits. Its length should be less than 
3 cm as measured from the device’s GND terminal 
to the corresponding pins of C1, C2 and C3. 
3.5.2 Secondary Bus Termination. The primary 
purpose of secondary bus termination is to 
minimize signal reflection effects when devices are 
connected to the bus via a longer stub line. There 
may be optionally up to 4 secondary bus 
termination units in a subsystem network.  
Note: A secondary termination is only suitable if all 
devices comply to the extra bus output drive 
capability requirement (e.g., 45 Ohms load), see 
section 4.1. 
Devices that are not assigned to provide primary 
bus termination shall implement a secondary bus 
termination function, if that is called out per SSTS, 
CTS or platform-specific technical document. 
Secondary bus termination units shall meet the 
same requirements as the regular termination, 
except that the nominal values of R1 and R2 are 
different. 
The nominal resistance of R1 and R2 shall be 
between 619 Ω < Rnom < 620 Ω, 1% 250 mW; 
C1 = 100 nF, 50V. The component C1 may be left 
unpopulated if it is not needed to meet component-
level and/or vehicle-level EMC requirements. 
3.5.3 Central Bus Termination Concept. For 
medium speed bus applications at 95.238 kbit/s, 
the bus termination can be located centrally, if 
called out per SSTS, CTS or platform-specific 
technical document. This means only one device in 
the network performs bus termination function. 
Note using this approach implies the sub network 
function is down in case one of the bus connector 
pins of the central termination device becomes 
disconnected from the bus. 
The parameter values for the central termination 
unit shall meet the specifications for regular bus 
termination see section 3.5.1. with the following 
exceptions.  
Differential internal resistance: 
58 Ohms < RdiffC < 64 Ohms 
Nominal resistance of R1 and R2: 
30 Ohms < R12nomC < 32 Ohms, 1%, 250 mW 
Note: Since R1 and R2 are supposed to be 
connected in series, the resulting bus termination 
resistance shall be twice the value of R12nomC. 
3.5.4 No Bus Termination. All devices which are 
not 
specifically 
assigned 
to 
perform 
bus 
termination (e.g., regular or secondary) shall not 
populate R1, R2 and C1. 
3.5.5 Bus Termination in the Wiring Harness. If 
a bus termination shall be placed outside of a 
control unit, e.g., in the wiring harness, then the 
following specifications apply. The resistors R1 and 
R2 should be implemented as a single resistor 
part. This single resistor part shall meet the 
following specifications: 
Nominal resistance = 121 Ohms, 1% 
The resistor shall support a power dissipation 
rating of 0.5 W at the applicable maximum ambient 
temperature. 
Note: TambMax depends on the location in the 
vehicle.  
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 11 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 11 of 42
 
Attention: It is highly recommended to locate this 
termination in a dry environment, e.g., in the 
passenger compartment. 
3.6 Bus Wiring Requirements. The bus wiring 
harness and bus connector implementation shall 
meet 
GMW3173 
requirements. 
For 
network 
topology requirements see sections 4.2 and 5.2 of 
this document. 
3.6.1 Bus Wiring Configurations. 
******* Each ECU shall provide two terminals per 
bus signal that shall be shorted within the ECU, 
see GMW3173. 
3.6.1.2 The internal connection between, for 
instance CANH_in and CANH_out, shall be 
implemented with a trace width that corresponds to 
the CAN physical media characteristic impedance. 
3.6.1.3 The internal connection shall be as short as 
possible and the loop area of the internal 
connection shall be as small as possible. The area 
between CANH and CANL on the PCB-board shall 
be kept as small as possible. 
3.6.1.4 CANH and CANL shall also be placed as 
close as possible in the ECU connector to keep 
loop area in the vicinity of the connector to a 
minimum. 
******* The overall CANH and CANL wiring/trace 
length inside of an ECU shall be less than 10 cm 
each. 
3.6.2 
Connector 
Parameters. 
Connector 
parameters shall be according to ISO 11898, 
GMW3191 and GMW3173. Note bus connectors 
need to provide uninterrupted contact even at 
presence of maximum vibration. There shall be no 
repetitive temporary interruption even for times up 
to 0.2 times the nominal bit time which equals 
t < 0.4 us for the high-speed CAN bus. Typically, 
multiple contact points/fingers are necessary to 
meet this requirement, e.g., 4 or more. 
3.7 ESD Protection of CAN Bus Pins. 
3.7.1 General Requirements for Every ECU. 
Each ECU shall meet the applicable ESD 
requirements at the CAN bus input pins, see 
GMW3097GS. Note this includes ESD during 
operation (power-on mode) and ESD protection for 
packaging & handling. See the GMW3097 sections 
“Remote 
Inputs/Outputs” 
and 
“Handling 
of 
Devices” for details. 
Additional requirements which apply to CAN bus 
interfaces which are in scope of this document: 
******* The ESD transient suppressor technique 
shall be compliant to the common mode bus 
voltage range. Therefore the ESD protection circuit 
shall exhibit a high impedance in this voltage 
range, e.g.: 
Rin_ESD > 100 kΩ for –2V < VCAN_H/L < +7V 
(preferred: –7V < VCAN_H/L < +12V). 
******* The ESD protection circuit shall not 
degrade the bus input resistance and bus input 
capacitance matching of the CAN interface. 
3.7.1.3 Possible ESD protection components 
include e.g. back-to-back Zener diodes, MLE, MOV 
or other suppression parts that do not exceed the 
capacitance limits specified in SAE J2284-3. 
3.7.1.4 The ESD transient suppressor components 
should provide a breakdown voltage which is 
higher than the maximum battery voltage at jump 
start condition, e.g., higher than 26.5 V. 
3.7.2 Specific Requirements for ECUs with 
Central ESD Protection Function. Usage of 
central ESD protection is not foreseen by this 
document. Each device shall protect itself against 
ESD. 
3.8 Radiated Emission and Immunity. ECU’s 
shall conform to EMC requirements according to 
the 
latest 
revisions 
of 
GMW3097GS 
and 
GMW3091GS when built into a vehicle. 
 
Table 7: Common Mode Choke Asymmetrical Insertion Loss (Common Mode) 
Frequency 
Min 
Nom 
Max 
Unit 
Conditions/Comment
150 to 500 kHz 
0 
 
 
dB 
 
500 kHz to 5 MHz 
5 
 
 
dB 
 
5 to 110 MHz 
20 
 
 
dB 
 
110 to 300 MHz 
10 
 
 
dB 
 
300 to 1000 MHz 
t.b.f. 
 
 
dB 
 
 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 12 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 12 of 42 
November 2005
 
Table 8: Common Mode Choke Asymmetrical Insertion Loss (Differential Mode) 
Frequency 
Min 
Nom 
Max 
Unit 
Conditions/Comment
150 to 500 kHz 
 
 
1 
dB 
 
500 kHz to 5 MHz 
 
 
10 
dB 
 
30 to 110 MHz 
6 
 
 
dB 
 
110 to 300 MHz 
t.b.f. 
 
 
dB 
 
300 to 1000 MHz 
t.b.f. 
 
 
dB 
 
 
3.9 Bus Protection and Transient Suppression. 
The following components shall be accommodated 
in the PCB layout to support an ECU meeting 
GMW3097GS EMC/ESD requirements. 
3.9.1 Line Common Mode Choke. The ECU shall 
provide the provision for a common-mode choke 
for FM- & AM-band. The common mode choke, if 
used, should provide an insertion loss as specified 
in Table 7 and Table 8: Asymmetrical insertion loss 
of line common-mode choke with both coil 
branches connected in parallel (common-mode). 
Each PCB layout shall accommodate footprints for 
placing a bus EMC inductor. The footprint shall 
support at least all the inductor products being 
mentioned in this section. The footprint provision is 
also required when all EMC tests have been 
successfully passed without inductor. If a bus 
inductor were not necessary for a certain 
component, then the footprints shall be populated 
with zero Ohms resistors. 
The typical inductance of the coil is approximately 
51 µH. Example implementations include types 
B82793-S0513-N201, 
ZJYS 
81R5-2PL(T)-G, 
ACT45B-510-2P and/or equivalent products. The 
potential usage of other parts is to be evaluated 
upon supplier request. 
3.10 Fault Tolerant Modes. The network shall 
meet requirements on Communication/Survivability 
Under Fault Conditions according to SAE J2284-3. 
Specifically each ECU shall meet the specified 
fault case behavior in 3.10.1 through 3.10.9, where 
the 
default 
time 
for 
resuming 
normal bus 
communication operation (i.e., capability to receive 
and transmit messages) is trsm ≤ 300 ms unless 
otherwise specified per Subsystem Technical 
Specification 
or 
Component 
Technical 
Specification.  
3.10.1 ECU loses power. ECU shall not interfere 
with the communication function among other 
ECUs during a loss of power or low supply voltage 
condition. Ongoing CAN frame transmissions 
should be concluded normally, e.g. without causing 
error conditions on the bus. The affected ECU 
should not degrade the EMC performance of the 
network (e.g., bus signal symmetry). Upon return 
of power, normal operation shall resume without 
any operator intervention within a time period being 
specified by the ECU Component Technical 
Specification. If a time period is not specified in the 
SSTS/CTS, then the ECU shall resume bus 
communication operation within t < trsm. An ECU 
shall not disturb ongoing transmissions upon return 
of power: generation of dominant bus conditions 
(disturbance) and/or sending of error frames is not 
allowed during ECU supply voltage ramp up and 
initialization. 
3.10.2 ECU loses ground. ECU shall not interfere 
with the communication function among other 
ECUs during a loss of ground condition. Upon 
return of ground connection, normal operation shall 
resume without any operator intervention within a 
time 
period 
being 
specified 
by 
the 
ECU 
Component Technical Specification. If a time 
period is not specified in the CSTS / CTS, then the 
ECU shall resume bus communication operation 
within t < trsm. 
3.10.3 Any ECU that is attached to the network 
by means of a stub connection and does not 
provide 
bus 
termination 
becomes 
disconnected from the bus wires. All remaining 
ECUs continue communication with no degradation 
of 
the 
communication 
function 
and/or 
electromagnetic compatibility. Upon removal of the 
wiring fault, normal operation shall resume without 
any operator intervention within a time period being 
specified by the ECU Component Technical 
Specification. If such time period is not specified in 
the SSTS / CTS, then the ECU shall resume 
normal 
bus 
communication 
operation 
within 
t < trsm. 
3.10.4 An ECU that provides bus termination 
becomes disconnected from the bus wires. All 
remaining ECUs should continue communication; 
however, there may be degradation of e.g. the 
electromagnetic compatibility. Upon removal of the 
wiring fault, normal operation shall resume without 
any operator intervention within a time period being 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 13 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 13 of 42
 
specified by the ECU Component Technical 
Specification. If such time period is not specified in 
the SSTS/CTS, then the ECU shall resume normal 
bus communication operation within t < trsm. 
3.10.5 Interruption of CANH and/or CANL wire 
at any location. There shall be no damage to any 
ECU. Data communication between ECUs on 
opposite sides of the interruption is not required. 
Data communication capability between ECUs on 
the same side of an interruption is desired. Upon 
removal of the wiring fault, normal operation shall 
resume without any operator intervention within a 
time 
period 
being 
specified 
by 
the 
ECU 
Component Technical Specification. If such time 
period is not specified in the SCTS/CTC, then the 
ECU shall resume normal bus communication 
operation within t < trsm. 
3.10.6 CANH and/or CANL wire shorted to 
ground. Communication may be interrupted but 
there shall be no damage to any ECU when a bus 
wire is shorted to ground potential. Upon removal 
of the wiring fault, normal operation shall resume 
without any operator intervention within a time 
period being specified by the ECU Component 
Technical Specification. If such time period is not 
specified in the SSTC / CTS, then the ECU shall 
resume normal bus communication operation 
within t < trsm. 
3.10.7 CANH and/or CANL wire shorted to 
battery 
voltage. 
Communication 
may 
be 
interrupted but there shall be no damage to any 
ECU when a bus wire is shorted to positive 
voltages of up to 18 V. It is highly desirable that 
there be no damage to any ECU when a bus wire 
is shorted to a voltage of 26.5 V for 1 minute. Upon 
removal of the wiring fault, normal operation shall 
resume without any operator intervention within a 
time 
period 
being 
specified 
by 
the 
ECU 
Component Technical Specification. If such time 
period is not specified in the SSTS / CTS, then the 
ECU shall resume normal bus communication 
operation within t < trsm. 
3.10.8 CANH wire shorted to CANL wire. 
Communication may be interrupted but there shall 
be no damage to any ECU. Upon removal of the 
wiring fault, normal operation shall resume without 
any operator intervention within a time period being 
specified by the ECU Component Technical 
Specification. If such time period is not specified in 
the SSTS/CTS, then the ECU shall resume normal 
bus communication operation within t < trsm. 
3.10.9 
Presence 
of 
above 
wiring 
fault 
conditions should not prevent an ECU from 
entering low power standby mode. E.g., an 
edge-triggered wakeup function is preferred. Also, 
a pending transmission request, where an ECU is 
waiting for in-frame acknowledgement from other 
ECUs, should not prevent an ECU from entering 
standby mode unless otherwise specified per CTS. 
3.11 Requirements On The CAN Transceiver 
and CAN Protocol Controller. This section 
specifies requirements that apply to dual wire CAN 
transceivers and CAN protocol controllers (stand-
alone and/or up-integrated) when used with the 
dual wire CAN interface described in this 
document. For information about device-specific 
reported problems see Appendix A and Appendix 
B. 
3.11.1 CAN Transceiver Requirements. The 
CAN transceiver IC (integrated circuit) is the 
interface between the CAN protocol controller and 
the physical bus line. 
3.11.1.1 CAN Transceiver Requirements. 
3.11.1.1.1 The CAN transceiver shall comply to 
ISO 11898 part 1 and part 2 (High-Speed Medium 
Access Unit). 
3.11.1.1.2 
CAN 
bus 
pin 
protection 
requirements. These requirements apply to all 
modes and conditions unless explicitly otherwise 
noted, e.g. including recessive state, dominant 
state, sleep mode, low-battery, not powered. 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 14 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 14 of 42 
November 2005
 
Table 9: Transceiver Absolute Maximum Ratings Requirements 
Parameter for CAN bus 
pins of integrated circuit 
(e.g., transceiver IC) 
Conditions 
Min 
Max 
Unit 
Extra requirement if protection 
against 42V supply is required 
per CTS or platform-specific 
technical document 
DC Voltage 
No time limit 
-5 
+18 
V 
+50 V 
Short-Term Voltage 
t = 1 min 
- 
+26.5 
V 
n/a 
Sporadic Overshoot 
Voltage  
t = 0.5 s 
- 
+40 
V 
+58 V 
Transient Voltage 
(ISO 7637-1, pulses 3a 
and 3b) 
t = 100 ns, 
R = 50 Ohms,  
C = 1 nF 
-150 
+100 
V 
n/a 
ESD protection 
in powered and 
unpowered state 
HBM contact 
discharge,  
10 pulses each 
-4 
+4 
kV 
n/a 
 
In addition, the transceiver shall survive loss of 
ground conditions without time limit at battery 
voltages of up to 16 V. Note in that case the bus 
pins assume the lowest voltage and all other 
transceiver pins may be biased to a voltage of up 
to 16 V higher than the bus pins (in case of a 12V 
nominal supply). 
3.11.1.1.3 The transceiver shall be self-protected 
against bus overload conditions, e.g. through 
thermal shutdown function. 
3.11.1.1.4 The implementation of the CAN-
transceiver shall support an ECU complying to the 
GMW3097GS 
EMC 
requirements 
and 
when 
implemented in a vehicle shall support complying 
with GMW3091GS. Note the IC shall support 
successful data transmission while the bus signal 
line is subjected to coupling of conducted 
transients, see GMW3097GS section 3.2.1.3.3  
Note: This test condition is similar to ISO 7637 part 
3, pulses 3a and 3b. 
3.11.1.1.5 When not powered, the transceiver shall 
not exhibit a bus input resistance less than 
specified in ISO 11898 for the high-speed CAN 
physical layer.  
Additional bus leakage requirement if the device is 
not foreseen for permanent battery supply: The 
bus input leakage current shall be less than 
0.25 mA when a voltage of 5 V is applied at the 
bus input while the transceiver supply voltage is 
0 V (applies to inputs CAN_H and CAN_L). 
3.11.1.1.6 The transceiver shall support a low-
power standby mode, if such function is required 
for an ECU per the Component Technical 
Specification or per platform-specific technical 
document. 
3.11.1.1.7 The CAN transceiver product shall not 
be a customer-specific integrated circuit (CSIC). 
That means the CAN transceiver core shall be 
intended and released for general automotive 
usage, specifically across vehicle manufacturers. It 
shall be an industry available part. 
3.11.1.1.8 When the transmit input (TxD) is not 
driven, then the bus pins of the CAN transceiver 
product shall exhibit high-impedance bus output 
behavior during supply voltage ramp-up and ramp-
down. Especially any turning on of bus output 
driver stages is not permitted when the TxD input 
is not driven, also during supply voltage on/off 
cycling. 
3.11.1.1.9 In case of the logic transmit control input 
(e.g., TxD) being erroneously locked in dominant 
state, then the transceiver IC shall automatically 
disable the bus driver, e.g., turn back to 
transmission of a recessive bus condition, after a 
timeout of 150 µs < tTxD_timeout < 10 ms. 
3.11.1.1.10 The transceiver device must support 
transmission of frames with 50% bit duty cycle (at 
a frame duty cycle of 100% forever). At supply 
voltages greater than 18 V, the device must 
transmit forever with a frame duty cycle of 50%. 
Note: The bit duty cycle is the percentage of bits 
which are dominant in any given message and is 
equivalent to the percentage of time the transmit 
data line (see, e.g., figure 3) is dominant during 
any given message. 
3.11.1.1.11 The transceiver device must support 
transmission of frames with 75% bit duty cycle at a 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 15 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 15 of 42
 
frame duty cycle of 100% forever when the 
operating ambient temperature is less or equal to 
85°C (Celsius). 
3.11.1.1.12 
This 
requirement 
applies 
to 
transceivers which remain powered and are placed 
into a standby or sleep mode when the ECU is in 
the low power state. This requirement does not 
apply to transceivers which are unpowered when 
the ECU is in the low power state.  
When the bus is in a dominant state and the 
transceiver is requested to change to a low power 
mode, the transceiver shall not assert the RxD 
output line prior to the bus changing its logic state. 
Note: This is necessary so that the device can 
enter the low power state when the bus is shorted 
to battery. 
3.11.1.1.13 The transceiver shall automatically limit 
the bus output current to less or equal to 200 mA 
when the CANH or CANL line is shorted to any 
voltage between –3 V and +26.5 V. 
3.11.1.2 
CAN 
Transceiver 
Preferred 
Conformance. 
Note: Preferred conformance items are typically 
candidates for being qualified as requirements in 
one of the following revisions of this document. 
3.11.1.2.1 Successful reception of data should be 
provided 
within 
a 
bus 
voltage 
range 
of 
-7V < VCAN_H/L < +12V. 
3.11.1.2.2 The transceiver should provide a 
common 
mode 
bus 
output 
voltage 
of 
2.25 V < VCM < 2.75 V in the recessive state and 
2.1 V < VCM < 2.9 V in the dominant state over 
operating conditions, e.g., Vcc = 5 V ± 5%, where 
VCM = 0.5 * (VCANH + VCANL). 
3.11.1.2.3 The transceiver shall be prepared for 
driving bus cable with a line impedance down to 
95 Ω see updated ISO 11898-2. Specifically it shall 
be capable of providing a differential bus output 
voltage level of Vdiff > 1.4V at a load resistance of 
RL = 45 Ω if called out per platform-specific 
technical document.  
Note: This is to provide proper drive capability for 
2 regular and 4 secondary bus termination loads. 
The requirement can be established per platform-
specific technical document if longer bus cable 
stubs shall be supported in a certain vehicle 
platform. 
3.11.1.2.4 The sum of the bus output delay and 
bus input delay of the transceiver should be less 
than 280 ns (250 ns preferred) on the bus signal 
rising and falling edge at a bus load condition of 
60 Ω, 50 pF. 
3.11.1.2.5 In standby mode the transceiver should 
consume less than 80 µA over the temperature 
range that corresponds to the ignition-off state, e.g. 
-40 to +85°C (preferred: +105°C). 
3.11.1.2.6 When the transceiver is not powered the 
leakage current into its logic level inputs (e.g., TxD, 
RxD) should be less than 50 µA when a voltage of 
+5 V is applied to any of the logic level inputs. 
3.11.1.2.7 The transceiver should support wakeup 
interrupt notification upon detection of bus traffic.  
Attention: This specification item is mandatory, if 
the applicable SSTS and/or CTS calls out for 
wakeup on bus traffic functionality. This is typically 
the case for specific ECUs in an electrical 
architecture. For a specification of the wakeup filter 
function refer to section 3.3.2. 
3.11.1.2.8 The transceiver shall not generate a bus 
wakeup notification when noise occurs on the bus 
line or when the bus line is clamped. For details 
refer to section 3.3.2. 
3.11.1.2.9 
The 
transceiver 
should 
support 
operation with differential input voltages in the 
range 
of 
–2 V < Vdiff < +7 V, 
where 
Vdiff = VCANH - VCANL. 
3.11.1.2.10 The transceiver should provide a 
notification flag output to indicate whether its 
operation is degraded or interrupted. This flag shall 
be set to the representation of the state “out of 
regular 
operation” 
for 
example 
when 
the 
transceiver detects a low supply voltage condition 
(e.g., Vcc < Vcc_min) and/or while it is in self 
protection mode (e.g., overtemperature shutdown) 
and/or while it has disabled transmitting due to a 
timeout condition for the TxD input. 
3.11.1.2.11 In low-power mode the transceiver 
should output a bias voltage of 0 V nominal to the 
bus with an internal resistance as specified in ISO 
11898-2 (see “internal resistance of CANH and 
CAN_L”). 
3.11.1.2.12 The transceiver should provide a 
suitable voltage output for attachment to the center 
tap of the bus termination resistors. This output 
shall assume high-impedance state when the 
transceiver is not powered, e.g., Vcc < 1.5 V and/or 
when the transceiver is in low-power mode. The 
bus input leakage current shall be less than 
± 100 µA for –3V < V_CAN_H/L < +16V. 
3.11.2 CAN Controller Requirements. 
3.11.2.1 The CAN controller shall support the 
protocol specification CAN 2.0A (standard format) 
and CAN 2.0B passive (29 bit ID extended format) 
and shall be fully compatible to ISO 11898-1. For 
example, the enhanced protocol for higher clock 
tolerance must be supported (e.g., tolerate 2 bit 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 16 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 16 of 42 
November 2005
 
message 
intermission) 
and 
extended 
frame 
messages shall not be disturbed unless bit errors 
are being detected. 
Alternatively the CAN controller shall be capable of 
transmitting and receiving messages with both 
standard frame format, i.e., 11 bit CAN identifiers, 
and extended frame format, i.e., 29 bit CAN 
identifiers (CAN 2.0B active) if explicitly called out 
per 
SSTS, 
CTS 
or 
platform-specific 
bus 
implementation specification. 
3.11.2.2 Compliance to ISO 11898 shall be verified 
through CAN conformance testing according to the 
ISO 16845 test plan. Successful passing of the 
complete conformance test shall be documented 
through provision of a written statement of the 
semiconductor 
manufacturer. 
The 
test 
plan 
conformance declaration shall indicate which 
product version was tested and which version of 
the CAN conformance test plan was used as 
reference. 
3.11.2.3 The controller must support the complete 
range of resynchronization jump width settings 
specified in ISO 11898-1. In particular, a bit timing 
parameter setting of SJW = 2 time quanta = 
PHASE_SEG2 must be supported. Also, the 
resynchronization function must be operational 
when a synchronization edge is detected outside 
the range set by the SJW parameter, i.e. when 
then phase error is greater than the SJW, the CAN 
controller shall adjust the timing of the current bit 
by SJW time quanta. 
3.11.2.4 The controller must perform at most one 
hard- and/or re-synchronization action between 
two consecutive bit sampling actions. The earliest 
point in time when the next re-synchronization is 
permitted is when the configured number of time 
quanta per bit time minus the length of TSEG2 has 
elapsed as counted from the bit start time as 
determined 
by 
the 
most 
recent 
bit 
resynchronization action. This means after any bit 
resynchronization action (hard- and/or re-sync) the 
resynchronization function must be disabled until 
the currently pending bit sampling has been 
concluded. 
This 
would 
allow 
the 
next 
resynchronization (at the earliest) in the TSEG2 
segment of the current bit, in case the bit sample 
result is “recessive”. This case might occur due to 
a glitch on the bus. Note this requirement applies 
to both the hard-synchronization function at the 
start of a frame as well as to the re-synchronization 
function while frame transmission/reception is 
ongoing. 
3.11.2.5 When the bus is idle and a recessive to 
dominant signal edge has occurred then the 
controller shall behave in the following way:  
3.11.2.5.1 The controller must sample the bit value 
at the specified time after this signal edge and 
must disregard any consecutive edges until 
sampling of the current bit has been concluded 
(e.g., hard sync function must be disabled 
immediately upon detection of the first recessive to 
dominant edge). 
3.11.2.5.2 If the sampling result is “dominant”, then 
this event shall be taken as a valid start of frame 
bit. 
3.11.2.5.3 Otherwise if the sampling result is 
“recessive”, then the edge shall be considered as a 
glitch and shall be disregarded. In particular there 
must not be any error frame transmission due to a 
glitch while the bus is idle. 
3.11.2.6 It is not recommended to feed CAN 
controllers with a PLL-generated clock signal. If a 
CAN controller is operated with a PLL clock, then 
careful analysis of the implications of the additional 
clock jitter is required. As a preventive measure 
extra accuracy requirements on the oscillator clock 
apply in this case, see 3.2.4. 
3.11.2.7 The information processing time of the 
protocol controller must be equal to 2 time quanta 
or less. 
3.11.2.8 The CAN controller may employ a 
message buffer concept with or without Dual 
Ported RAM (DPRAM). 
3.11.2.9 The CAN protocol controller shall provide 
at least 2 transmit message buffers and 2 receive 
message buffers. The transmit buffers shall be 
able to be independently loaded. The CAN 
controller shall have the capability of determining 
the higher priority message and attempt to transmit 
this message first. The CAN controller shall 
recheck the priority each time a message 
transmission is attempted. Note: Two (2) transmit 
buffers are required to be able to queue a higher 
priority message while a low priority message is 
waiting for bus access. Two (2) receive buffers are 
required to be able to receive new data while the 
host is still reading the current content of the 
particular receive buffer. 
********* Any time when more than one transmit 
buffer is armed for transmission, then the protocol 
controller shall automatically (e.g., without any 
CPU-support) transmit the message with the 
lowest CAN identifier first. 
********* When two or more messages with the 
same CAN identifier are armed for transmission, 
then the protocol controller shall transmit these 
messages in a FIFO fashion, i.e., in the sequence 
how they were armed. 
********* The controller product shall support a 
low-power sleep mode with wake-up capability via 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 17 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 17 of 42
 
interrupt signal from the CAN bus line and/or 
application-level interrupt and/or power-on reset.  
Note: 
The 
controller 
product 
data 
sheets 
sometimes refer to “stop mode” when specifying 
the function called “sleep mode” in this document. 
3.11.2.13 In sleep mode the power consumption of 
the controller should be less than approximately 
Iq = 50 µA. This is typically implemented by 
stopping the controller clock or turning it to a lower 
frequency. 
3.11.2.14 The controller product shall provide 
dedicated indication flags for each wakeup event 
source since ECUs need to take specific action 
depending on which source triggered the wakeup 
event, e.g., bus wakeup event vs. local wakeup 
event vs. reset event and so on. 
3.11.2.15 Deleted. 
3.11.2.16 The controller must properly respond to 
(e.g., latch) any activation conditions at any time, 
also while a goto sleep command is in progress. 
3.11.2.17 The controller shall not provide any 
means to suppress/ignore a pending bus wakeup 
interrupt. Exception: The controller may ignore 
pending wakeup interrupts as long as it is in 
regular operation and no goto sleep command is 
present or pending. 
3.11.2.18 The controller product shall support fast 
startup following a detection of a wakeup condition. 
Startup includes supply voltage ramp up (if 
applicable), oscillator start up, memory test, 
controller 
initialization 
and 
transceiver 
mode 
change. Note, in some applications the startup 
time for an ECU from detection of a wakeup 
condition 
until 
the 
controller 
is 
ready 
for 
reception/transmission of CAN messages needs to 
be less than approximately 70 ms. The required 
startup time for the ECU can be found in the 
applicable CTS or SSTS or platform-specific bus 
implementation document. 
3.11.2.19 The CAN controller shall support the 
implementation 
of 
the 
GMW3097GS 
EMC 
requirements for an ECU. Therefore, it is 
recommended 
that 
the 
CAN 
controller 
be 
integrated with the corresponding CPU in a single 
package. 
3.11.2.20 The following control and indication 
functions shall be supported by the CAN controller, 
e.g. by accessing the appropriate register in the 
CAN controller or by interrupt notification: 
3.11.2.20.1 
Abort 
transmission 
function 
i.e. 
capability 
to 
suspend 
transmission 
attempts 
without causing error condition on bus and without 
interrupting bus reception function. 
3.11.2.20.2 Bus-off condition notification flag. 
3.11.2.20.3 Bus error warning notification flag that 
becomes present earliest when the transmit error 
counter reaches a level of 96 and latest when the 
CAN bus error passive condition is reached. The 
error warning notification shall become absent no 
later than after the transmit error counter has 
decreased below a level of 32. 
3.11.2.21 It is preferred the CAN controller 
supporting specific notification of (i.e. differentiation 
between) the following fault cases: 
3.11.2.21.1 CAN receive input is locked in the 
dominant state. Note, the bus shall be considered 
as locked dominant when N_lock consecutive 
dominant 
bits 
have 
been 
detected, 
where 
32 < N_lock < 256. 
3.11.2.21.2 Transmission attempt failed because 
CAN receive input is locked in the recessive state. 
3.11.2.21.3 Transmission attempt failed because of 
missing in-frame acknowledge response. Note, this 
denotes the case where the ACK slot was detected 
to be recessive rather than dominant. 
3.11.2.21.4 Transmission attempt failed due to 
detection of a dominant bus condition in the 
acknowledge delimiter slot. 
3.11.2.22 The CAN controller function shall cover 
the complete range of standard frame format 
message identifiers from 0 through 2047 dec. 
Specifically it shall also be capable to receive and 
transmit messages with CAN identifiers 2032 
through 2047 dec ($7F0 through $7FF). 
3.11.2.23 
The 
microcontroller 
shall 
support 
protection mechanisms against use of corrupted 
memory data e.g. checksum and/or CRC. It shall 
support checking (e.g., CRC check or equivalent) 
of proper function and correct data content of the 
complete memory including volatile (e.g. RAM) and 
non-volatile 
memory 
(e.g., 
NV-program 
and 
-configuration data) within a default time of less or 
equal to 250 ms unless otherwise specified in the 
applicable CTS/SSTS. Note the memory check 
could be performed immediately at microcontroller 
startup and/or as a background task during 
operation. The CTS/SSTS shall state whether 
testing prior communication startup is required. 
3.11.2.24 The microcontroller product shall support 
retaining certain RAM memory data while in low 
supply voltage condition. RAM memory data shall 
be retained as long as the controller’s supply 
voltage is greater or equal to 2.5 V unless an 
exemption is made per SSTS or CTS. RAM 
memory data must be automatically marked as 
invalid, when the content must be regarded as 
unreliable due to presence of a low supply voltage 
condition. This capability is required, for example, 
to retain vehicle configuration data during crank. 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 18 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 18 of 42 
November 2005
 
Note: The minimum device supply voltage at crank 
can be as low as 4.5 V. 
3.11.2.25 It is preferred that the CAN controller 
product 
support 
logic 
operation 
(specifically 
capability to receive and transmit bus messages) 
within a supply voltage range of 3 V < VuC_op < 
5.25 V.  
Note: This does not imply support of sensor data 
A/D conversion and/or actuator control. 
3.11.2.26 The CAN controller product shall be 
capable of supporting uninterrupted bus data 
reception for a bus utilization of up to 100% on all 
CAN interfaces using the applicable nominal data 
rate(s) unless an explicit exemption is made for a 
certain device. 
********* The CAN controller product shall not be 
a customer-specific integrated circuit (CSIC). That 
means the CAN controller core shall be intended 
and released for general automotive usage, 
specifically across vehicle manufacturers. It shall 
be an industry available part. 
********* The CAN controller product and/or the 
host microcontroller shall be capable of detecting 
the presence of frames on the CAN bus and 
subsequently initializing the start-up sequence 
when in a low power mode. The detection of the 
presence 
of 
frames 
shall 
be 
supported 
independent of the current frame acceptance filter 
settings. I.e., the device shall be able to wake-up 
on any frame independent of the identifier or the 
format of the frame. 
3.11.3 
CAN 
Controller 
Set-up. 
The 
CAN 
controller’s CAN transmit output should be 
programmed to active low push-pull operation, e.g. 
set the CAN transmit output control register 
accordingly. 
See 
sections 
4.1 
and 
5.1 
for 
bit 
timing 
requirements. 
4 High Speed Bus Requirements 
An ECU being designated for application in a high-
speed 
network 
shall 
meet 
the 
general 
requirements as specified in the ISO 11898 part 1 
and part 2 (High-Speed Medium Access Unit) and 
SAE J2284-3 standards unless otherwise specified 
in this document. 
4.1 ECU Parameter Specifications. All parameter 
specifications shall be met over the applicable 
ECU operating conditions and life time. This 
includes for example the temperature range, 
supply voltage, network load conditions and 
lifetime degradation unless otherwise noted. All 
requirements do apply to test tools as well unless 
an explicit exemption were made. 
In particular cases bit timing settings other than the 
ones specified in Table 10 can be used depending 
on the size of the network and its topology. If this is 
desirable then contact a member of the GMLAN 
Hardware Team for  more information. 
 
Table 10: High-Speed CAN Parameter Specifications 
Parameter / Function 
Symbol 
Min 
Nom 
Max 
Unit 
Conditions / Comment 
Number of in-vehicle ECUs 
 
- 
- 
21 
 
Pay attention to the 
relation between node 
count, 
wiring 
length 
and clock tolerance 
Number of off-board ECUs 
 
0 
- 
1 
 
Tester or CAN tool 
Bus speed 
fB 
 
500 
 
kbit/s 
 
Tolerance of bus speed for vehicle 
ECUs 
∆fB 
0 
- 
± 0.45 
% 
over 
operating 
conditions 
and 
component lifetime  
Preferred tolerance of bus speed 
for off board tools 
∆fB_Tool 
0 
- 
± 0.15 
% 
Over 
operating 
conditions 
and 
tool 
lifetime 
Bit time 
tB 
1991 
2000 
2009 
ns 
± 0.45 % (incl. aging) 
Nominal 
time 
quantum 
length, 
option 1 
tQ_1 
- 
125 
- 
ns 
16 tQ per bit 
Nominal 
time 
quantum 
length, 
option 2 
tQ_2 
- 
100 
- 
ns 
20 tQ per bit 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 19 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 19 of 42
 
Parameter / Function 
Symbol 
Min 
Nom 
Max 
Unit 
Conditions / Comment 
Resynchronization mode Note 1 
RSM 
- 
R to D 
only 
- 
 
resync on recessive to 
dominant edges only 
Number of samples per bit 
SM 
1 
1 
1 
 
single sample mode 
Nominal sample point position Note 2 SP 
0.785 
0.8 
0.834 
tB 
78.5% to 83.4% of bit 
time 
Bit resynchronization jump width 
with 16 time quanta per bit 
tSJW 
3 
3 
3 
tQ 
Typically 
corresponds 
to SJW = 10bin 
Bit resynchronization jump width 
with 20 time quanta per bit 
tSJW 
4 
4 
4 
tQ 
Typically 
corresponds 
to SJW = 11bin 
ECU internal delay Note 3 
tECU 
30 
- 
350 
ns 
Sum of transmit output 
plus receive input delay
Differential voltage bus output rise 
time 
trdiff 
20 
- 
200 
ns 
Figure 3 
Differential voltage bus output fall 
time 
tfdiff 
20 
- 
400 
ns 
Figure 3 
Differential bus voltage at RL = 
45 Ω Note 4 
Vdiff45 
1.4 
- 
3.0 
V 
note this requirement is 
not 
covered 
by 
ISO 11898 to date 
CANH and CANL bus output level 
when signal value changed and 
t < 1 µs 
V0u 
0.81 
Vcsdy 
 
1.5 Vcsdy  V 
overshoot relative to 
the 
steady 
state 
voltage 
CANH and CANL bus output level 
when 1 µs has elapsed after a 
signal value change 
V1u 
0.95 
Vcsdy 
 
1.05 
Vcsdy 
V 
relative to the steady 
state voltage 
Common mode voltage when 0.2us 
has elapsed after a signal value 
change Note 5 
Vcm 
0.95 
Vcmavg 
 
1.05 
Vcmavg 
V 
relative to the average 
common mode voltage 
ECU CANH input capacitance to 
ground Note 6 
CinCANH 
40 
- 
150 
pF 
ftest = 1 MHz  
ECU CANL input capacitance to 
ground 
CinCANL 
40 
- 
150 
pF 
ftest = 1 MHz  
ECU 
CANH 
to 
CANL 
input 
capacitance 
Cindiff 
0 
- 
90 
pF 
ftest = 1 MHz  
Deviation 
between 
CANH 
and 
CANL bus input capacitance 
∆ Cin 
0 
- 
20 
% 
ftest = 1 MHz 
Note 1: The CAN resynchronization mode shall be set to resynchronization on recessive-to-dominant edges only and shall meet the 
enhanced protocol for extended clock tolerance. 
Note 2: The nominal sample point position shall be located at the specified time after the start of a bit cell. Other settings may be possible 
after careful analysis, see Appendix D. 
Note 3: The ECU internal delay includes the bus output delay and the bus input delay of the physical medium attachment (e.g. CAN 
transceiver IC, common-mode coil) plus the CAN controller delay. 
Note 4: This requirement is not covered by ISO 11898 and SAE J2284 to date. See the platform-specific technical document whether it 
applies to a certain device. Note secondary bus terminations are recommended if longer bus cable stubs shall be supported. 
Note 5: This signal symmetry requirement can be waived for a certain vehicle type, if component-level and vehicle-level radiated emission 
tests have been successfully passed. 
Note 6: Attention: See chapter 3.5 for more details on which bus input capacitors are permitted. 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 20 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 20 of 42 
November 2005
 
0.7V
Vil
Transceiver internal delay
Transmit data from
CAN-controller
(e.g. TX0)
Receive data of
CAN-controller
(e.g. RX0)
Differential output
voltage on BUS
Vdiff = VCANH - VCANL
Vih
90%
10%
trdiff
tfdiff
 
Figure 3: AC Parameters of Physical Medium Attachment 
 
4.2 Wiring and Network Topology.  
Physical media parameters (e.g., cable and 
connectors) shall meet GMW3173 and GME14010 
requirements unless otherwise specified, e.g., per 
vehicle platform-specific document.  
Note: The overall resistance of a CAN bus wire 
between any two ECUs must not exceed the value 
of Rwh < 4 Ohms over operating ambient 
temperature and vehicle lifetime. For clarification 
this includes the resistance of all connectors and 
conductors between any two ECUs in the network. 
Additional hints: 
The bus cable shall be twisted. Twisting is required 
regardless of presence or absence of a cable 
sheath. Per default the term “twist” refers to a full 
period turn meaning a turn of 360 degrees. 
A bus cable without and/or with sheath can be 
employed, 
see 
the 
vehicle 
platform-specific 
technical document. 
If wiring splices are used, then they should be 
placed in dry environment, e.g., passenger 
compartment. 
 
 
ECU
1
ECU
3
ECU
4
ECU
n
ECU
2
Bus
termination
Bus
termination
ECU
n-1
DLC
J1962
d
L1
L3
L2
n < 22
Tester
L4
L4
 
Figure 4: AC Parameters of Physical Medium Attachment 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 21 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 21 of 42
 
Table 11: High-Speed CAN Topology Specifications (Figure 4) 
Parameter 
Symbol 
Minimum 
Nominal 
Maximum 
Unit 
Comment 
Cumulative 
In-
Vehicle 
Cable 
Length 
 
LΣ 
 
0.1 
 
- 
 
25 Note 1 
 
meter 
accumulated 
total 
cable length including 
trunk line and stubs 
ECU 
Cable 
Stub 
length 
 
L1 
 
0 
 
- 
 
1 Note 2 
 
meter 
Minimum of 0 allows 
for 
daisy 
chain 
configurations. 
In-Vehicle 
DLC 
Cable Stub Length 
 
L2 
 
0 
 
- 
 
1 
 
meter 
Minimum of 0 allows 
for 
DLC 
direct 
mounting on PCB. 
Off-Board 
DLC 
Cable Stub Length 
 
L3 
 
0 
 
- 
 
5 
 
meter 
 
Tester cable 
 
Ground wire length 
for 
ECUs 
with 
regular 
bus 
termination 
 
L4 
 
0 
 
- 
 
1 
 
meter 
 
Length from split bus 
termination to vehicle 
chassis 
Note 1: The maximum overall length per network is 30m. If the architecture of a vehicle foresees the high-speed CAN in-vehicle network is 
not directly connected to the DLC (e.g. using a signal converter device), then the length budget for in-vehicle usage is equal to the overall 
budget, e.g. 30 m. 
Note 2: In general longer stubs are not suitable. Nevertheless a single stub with L >1m might be acceptable under certain conditions after 
careful analysis and verification in a particular vehicle type on a case-by-case basis. Secondary bus termination is recommended to be 
placed at the end of long stubs. 
 
Additional Requirements: 
a. To minimize standing waves ECUs should not 
be placed equally spaced on the network and 
cable tail lengths should not all be the same 
length. 
b. The bus line terminations may be placed within 
modules. 
Terminations 
shall 
be 
placed 
adjacent to or within the two On-Board ECUs 
which are typically located at the largest bus 
cable distance from each other. 
c. ECUs that do not provide bus line termination 
function can be optional devices. 
For the purpose of mounting of bus connectors the 
cable may be untwisted for a length of less than 
50 mm at a connector (less than 25 mm preferred). 
For more information about the relation between 
maximum cable length, bit timing and number of 
ECUs contact a member of the GMLAN Hardware 
Team. 
5 Medium Speed Bus Requirements 
An ECU being designated for application in a 
medium speed network shall meet the general 
requirements as specified in the ISO 11898 
standard part 1 and part 2 (High-Speed Medium 
Access Unit) unless otherwise specified in this 
document. In addition, an ECU designated for 
operation at 125 kbit/s shall comply with SAE 
J2284-1 
unless 
otherwise 
specified 
in 
this 
document. 
5.1 ECU Parameter Specifications. All parameter 
specifications have to be met over the applicable 
ECU operating conditions and life time. This 
includes for example the temperature range, 
supply voltage, network load conditions and 
lifetime degradation unless otherwise noted. 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 22 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 22 of 42 
November 2005
 
Table 12: 125 kbit/s Medium-Speed CAN Parameter Specifications 
Parameter/Function 
Symbol Min 
Nom 
Max 
Unit 
Conditions/Comment
Number of in-vehicle ECUs 
 
- 
- 
31 
 
 
Number of off-board ECUs 
 
0 
- 
1 
 
Tester or CAN tool 
Bus speed 
fB 
 
125 
 
kbit/s 
 
Tolerance of bus speed 
∆fB 
0 
- 
± 0.5 
% 
over 
operating 
conditions 
and 
component lifetime 
Bit time 
tB 
7960 
8000 
8040 
ns 
± 0.5% incl. aging 
Nominal time quantum length 
tQ 
380.95 
- 
666.67 
ns 
12 to 21 tQ per bit 
Resynchonization mode Note 1 
RSM 
- 
R to D 
only 
- 
 
resync on recessive to 
dominant edges only 
Nominal resynchronization jump width tSJW 
0.15 
- 
0.25 
tB 
15% to 25% of bit time
Number of samples per bit 
SM 
1 
1 
1 
 
single sample mode  
Nominal sample point position Note 2 
SP 
0.75 
0.775 
0.85 
tB 
75% to 85% of bit time
ECU internal delay Note 3 
tECU 
30 
- 
1000 
ns 
 
Differential voltage bus output rise 
time 
trdiff 
20 
- 
1000 
ns 
Figure 3 
Differential voltage bus output fall 
time 
tfdiff 
20 
- 
2000 
ns 
Figure 3 
ECU CANH input capacitance to 
ground  
CinCANH 
40 
- 
140 
pF 
f = 10 MHz 
ECU CANL input capacitance to 
ground 
CinCANL 
40 
- 
140 
pF 
f = 10 MHz 
ECU 
CANH 
to 
CANL 
input 
capacitance 
Cindiff 
0 
- 
50 
pF 
f = 10 MHz 
Note1: The CAN resynchronization mode shall be set to resynchronization on recessive-to-dominant edges only and shall meet the 
enhanced protocol for extended clock tolerance. 
Note 2: The nominal sample point position shall be located at the specified time after the start of a bit cell.  
Note 3: The ECU internal delay includes bus output delay and bus input delay of the physical medium attachment (e.g., CAN transceiver 
IC, common-mode coil) plus the CAN controller delay. 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 23 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 23 of 42
 
Table 13: 95.238 kbit/s Medium-Speed CAN Parameter Specifications 
Parameter/Function 
Symbol Min 
Nom 
Max 
Unit 
Conditions/Comment
Number of in-vehicle ECUs 
 
- 
- 
15 
 
 
Number of off-board ECUs 
 
0 
- 
1 
 
Tester or CAN tool 
Bus speed 
fB 
 
95.238 
 
kbit/s 
 
Tolerance of bus speed 
∆fB 
0 
- 
± 0.5 
% 
over 
operating 
conditions 
and 
component lifetime 
Bit time 
tB 
10447.5 10500 
10552.5 ns 
± 0.5% incl. aging 
Nominal time quantum length 
tQ 
500 
- 
750 
ns 
14 to 21 tQ per bit 
Resynchonization mode Note 1 
RSM 
- 
R to D 
only 
- 
 
resync on recessive to 
dominant edges only 
Nominal resynchronization jump width tSJW 
0.157 
- 
0.22 
tB 
15.7% to 22% of bit 
time 
Number of samples per bit 
SM 
1 
1 
3 
 
single sample mode or 
triple sample mode 
Nominal sample point position Note 2 
SP 
0.75 
0.775 
0.80 
tB 
75% to 80% of bit time
ECU internal delay Note 3 
tECU 
30 
- 
1000 
ns 
 
Differential voltage bus output rise 
time 
trdiff 
20 
- 
1000 
ns 
Figure 3 
Differential voltage bus output fall 
time 
tfdiff 
20 
- 
2000 
ns 
Figure 3 
ECU CANH input capacitance to 
ground  
CinCANH 
40 
- 
500 
pF 
f = 10 MHz 
ECU CANL input capacitance to 
ground 
CinCANL 
40 
- 
500 
pF 
f = 10 MHz 
ECU 
CANH 
to 
CANL 
input 
capacitance 
Cindiff 
0 
- 
300 
pF 
f = 10 MHz 
Note1: The CAN resynchronization mode shall be set to resynchronization on recessive-to-dominant edges only and shall meet the 
enhanced protocol for extended clock tolerance. 
Note 2: The nominal sample point position shall be located at the specified time after the start of a bit cell. In case of triple sample mode 
the sample point position denotes the location of the last sample being taken. 
Note 3: The ECU internal delay includes bus output delay and bus input delay of the physical medium attachment (e.g., CAN transceiver 
IC, common-mode coil) plus the CAN controller delay. 
 
5.2 Wiring and Network Topology. Physical 
media parameters (wiring) shall meet GME14010 
requirements unless otherwise specified. The bus 
cable shall be twisted. Twisting is required 
regardless of presence or absence of a cable 
sheath. Per default the term “twist” refers to a full 
period turn meaning a turn of 360 degrees. The 
bus cable typically shall come without sheath 
unless 
otherwise 
called 
out 
per 
applicable 
platform-specific technical document. Attention if 
wiring splices are used, then they should be placed 
in dry environment, e.g. passenger compartment. 
The overall resistance of a CAN bus wire between 
any two ECUs must not exceed the value of Rwh < 
4 Ohms over operating ambient temperature and 
vehicle lifetime. For clarification this includes the 
resistance of all connectors and conductors 
between any two ECUs in the network. 
The 
topology 
requirements 
for 
a 
network 
containing more than one ECU on-board the 
vehicle and a single off-board tool are specified 
below: 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 24 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 24 of 42 
November 2005
 
 
Table 14: 125 kbit/s Medium-Speed CAN Topology Specifications (See Figure 4) 
Parameter 
Symbol 
Minimum 
Nominal 
Maximum 
Unit 
Comment 
Cumulative in-
vehicle cable 
length 
 
LΣ 
 
0.1 
 
 
50 
 
meter 
Accumulated total cable 
length when 2 regular 
terminations are 
present.  
ECU Cable 
Stub length 
 
L1 
 
0 
 
 
1 
 
meter 
Minimum of 0 allows for 
daisy chain 
configurations. 
In-Vehicle 
DLC Cable 
Stub Length 
 
L2 
 
0 
 
 
1 
 
meter 
Minimum of 0 allows for 
DLC direct mounting on 
PCB. 
Off-Board DLC 
Cable Stub 
Length 
 
L3 
 
0 
 
 
5 
 
meter 
 
Tester cable 
 
 
Table 15: 95.238 kbit/s Medium-Speed CAN Topology Specifications (See Figure 4) 
Parameter 
Symbol 
Minimum 
Nominal 
Maximum 
Unit 
Comment 
Cumulative in-
vehicle cable 
length 
 
LΣ 
 
0.1 
 
 
35  
 
(20) 
 
meter 
Accumulated total cable 
length when 2 regular 
terminations are 
present.  
In parenthesis: When 
central termination 
approach employed 
ECU Cable 
Stub length 
 
L1 
 
0 
 
 
6 
 
meter 
Minimum of 0 allows for 
daisy chain 
configurations. 
In-Vehicle 
DLC Cable 
Stub Length 
 
L2 
 
0 
 
 
3 
 
meter 
Minimum of 0 allows for 
DLC direct mounting on 
PCB. 
Off-Board DLC 
Cable Stub 
Length 
 
L3 
 
0 
 
 
5 
 
meter 
 
Tester cable 
 
 
6 Verification 
All parameter specifications have to be met over 
the applicable ECU operating conditions and life 
time. This includes for example the temperature 
range, supply voltage, network load conditions and 
lifetime 
degradation 
unless 
otherwise noted. 
Please refer to the latest revision of the GME6718 
GMLAN Device Test Specification or follower 
document for applicable test procedures. 
6.1 Production Testing at Supplier’s End of 
Line. Suppliers shall take appropriate measures to 
ensure that each delivered device meets the 
specified properties. Specifically suppliers shall 
verify each individual dual wire CAN interface by 
applying (at least) the following tests at the end of 
production line prior to delivery to the automaker: 
Functional communication test with tool (Functional 
as well as diagnostic messages shall be covered 
using an appropriate tool e.g., CANoe or similar.) 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 25 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 25 of 42
 
• 
CANH recessive state output level when 
device is powered (see GME6718) 
• 
CANL recessive state output level when device 
is powered (see GME6718) 
• 
CANH output current at VCANH = -7 V when 
device is powered (see GME6718) 
• 
CANL output current at VCANL = +16 V when 
device is powered (see GME6718) 
Each test value shall meet the specified min/max 
parameter limits. These tests shall be performed 
with automatic test equipment unless an explicit 
exception were made. 
6.2 Development Testing. 
6.2.1 CAN Bus In-/Outputs. For specifications 
how to test the CAN bus in-/outputs refer to the 
latest version of the document GME6718. 
6.2.2 Wake Up Pulse. The tests in this section are 
only applicable in case the device supports a 
dedicated wakeup input terminal. 
******* Transmit Wake Up Pulse. 
ECU
RLOAD
Wake up wire
V
Vbatt
Vtwu
U
Us
 
Test Cases: 
Parameter 
Min 
Nom 
Max 
Conditions 
Vtwuoh 
6.5 V 
- 
16 V 
Vs = 9 V and 16V, RLOAD = 240 Ω,  
Wake up wire active 
Vtwuoh_lo batt 
4.8 V 
- 
16 V 
Vs = 6.5 V, RLOAD = 240 Ω,  
Wake up wire active 
Vtwuol 
-0.3 V 
- 
+0.3 V 
Vs = 6.5V and 16 V, RLOAD > 100 kΩ, 
Wake up wire inactive 
ttwuo 
400 ms 
500 ms 
600 ms 
Vs = 8 V, RLOAD = 240 Ω,  
Wake up wire active 
The wake up voltage Vtwu and wake up time ttwu shall be measured with an oscilloscope. 
Figure 5: Test Setup for Wake up Wire Output Function 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 26 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 26 of 42 
November 2005
 
6.2.2.2 Receive Wake Up Pulse. 
ECU
Wake up wire
V
Vbatt
Vrwu
U
Us
Pulse Generator
 
Figure 6: Test Setup Wake Up Input Function 
The wake up receive threshold shall be determined 
by varying the amplitude of a test wake up pulse 
applied by the pulse generator. The test pulse 
duration shall be 2 ms. 
The wake up pulse sensitivity shall be determined 
by applying a test pulse with duration of the 
maximum and the minimum trwu length. 
Test Cases: 
Parameter 
Min 
Nom 
Max 
Conditions 
Vrwu input 
threshold 
2.0 V 
 
4.5 V 
6.5 V ≤ Vs ≤ 16 V 
ECU wake up 
detection 
function 
 
Successful 
wake up 
detection 
 
6.5 V ≤ Vs ≤ 16 V 
apply single pulse of Vrwu = 4.5 V for 
trwu = 2 ms  
ECU wake up 
filter function 
 
No wake up 
detection 
permitted 
 
6.5 V ≤ Vs ≤ 16 V 
trwu = 100 µs ; apply square wave 
signal of 1 kHz, 10% duty cycle, 
0 V/8 V levels 
Iih 
1.2 mA 
1.4 mA 
2.0 mA 
Vrwu = 12 V 
 Vs ≤ 16 V 
 
6.2.3 Communication Enable I/Os. For test 
specifications see GME6718. 
7 Change Management 
Suppliers shall report any pending significant 
change of the hardware and/or software to the 
vehicle manufacturer prior to execution of the 
change. A significant change includes but is not 
limited to a change of the CAN transceiver and/or 
CAN data link controller core and/or microcontroller 
product. 
Any modification of a device’s bus termination 
behavior (e.g., removal or inclusion of regular bus 
termination in a device, change from regular to 
secondary termination, etc.) shall be documented 
though 
a 
change 
of 
appropriate 
device 
identification codes of the affected device, e.g. 
change of the hardware number/suffix. The device 
identification code that indicates the type of bus 
termination used shall be readable by test 
equipment via standardized diagnostic request 
services (see GMW3110).  
Further requirements may apply per applicable 
automaker- 
or 
vehicle-line-specific 
change 
management document. 
8 Notes 
8.1 Glossary. Not applicable. 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 27 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 27 of 42
 
8.2 Acronyms, Abbreviations, and Symbols. 
ECU 
 
Electronic Control Unit 
Node 
A device that is connected to a 
network and is capable of operating 
according to the relevant protocol 
specifications  
CAN 
 
Controller Area Network 
CTS 
 
Component Technical Specification 
SSTS  
Sub-System Technical Specification 
EMC 
 
Electromagnetic Compatibility 
ESD 
 
Electrostatic Discharge 
IC  
 
Integrated circuit 
µC  
 
Microcontroller 
VbattNom: 
Nominal 
vehicle 
system 
battery 
voltage 
Vs: 
Voltage between the ECU´s battery 
supply and ground inputs 
tB:  
 
CAN bit time length 
tQ: 
Time quantum (Note a bit time 
consists of an integer number of time 
quanta) 
n/a  
 
not applicable 
trsm: 
time it takes for an ECU to resume bus 
communications after an interruption of 
power or after wiring fault is removed 
9 Additional Paragraphs 
9.1 All materials supplied to this specification must 
comply with the requirements of GMW3001, Rules 
and Regulations for Materials Specifications. 
9.2 All materials supplied to this specification must 
comply with the requirements of GMW3059, 
Restricted and Reportable Substances for 
Parts. 
10 Coding System 
This specification shall be referenced in other 
documents, drawings, VTS, CTS, etc. as follows: 
GMW3122 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 28 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 28 of 42 
November 2005
 
 
11 Release and Revisions 
 
11.1 Release. This general specification originated in June 1998. It was first published in January 1999. 
11.2 Revisions. 
Rev 
Approval 
Date 
Description (Organization) 
C 
FEB 2005 
Maximum number of HS-CAN nodes changed to 22 (including off-board tool). 
All section numbers have changed as follows (previous � current) 
§1 � §11.2; §2 � §1; §2.1 �§1.1; §2.2 � §1.2; §2.3 � §1.3; §3 � §2; §3.1 � §2; §3.2 � §2.2: 
References introduced: GME6718, GME14010. References eliminated: GMW3094, GMW3100, 
GMW3098, GMW3104, GMW3128; §3.3 � §2.1; §3.4 � §8.1; §4 � §3; §4.1 � §3.1; §4.2 � §3.2; 
§4.2.1 � §3.2.1: Specifications for minimum operating voltage modified. Specification for resuming 
operation after power dropout changed; §4.2.2 � §3.2.2; §4.2.3 � §3.2.3; §4.2.4 � §3.2.4: 
Considerations for using PLL changed; §4.3 � §3.3: Some introductory statements modified. “t” and 
“r” in symbol subscripts eliminated; §4.3.1 � §3.3.1; §4.3.2 � §3.3.2: Specifications modified. 
Clause 2 modified to limit the number of transitions required for a wake-up. Clause 3 introduced; 
§4.3.3 � §3.3.3: Electrical specifications for Communication Enable I/Os introduced; §4.4 � §3.4; 
§4.5 � §3.5: Reference to ECM/PCM appended. Figure 2 modified. Maximum resistance changed 
to 32 Ohms with central termination concept. Package protect on the PCB made dependent on 
CTS/SSTS. New section §4.5.4 introduced dealing with termination in the harness; §4.5.1 � §3.5.1; 
§4.5.2 � §3.5.2; §4.5.3 � §3.5.3; §4.5.4 � §3.5.4; §4.6 � §3.6; §4.6.1 � §3.6.1: Clause 5 
introduced; §4.6.2 � §3.6.2; §4.7 � §3.7; §4.7.1 � §3.7.1: Clause 4 introduced. Reference to 
sections “Remote Inputs/ Outputs” and “Handling of Devices” introduced; §4.7.2 � §3.7.2: 
requirement eliminated.; §4.8 � §3.8; §4.9 � §3.9; §4.9.1 � §3.9.1: Wording corrected, population 
of coil when necessary; §4.10 � §3.10; §4.11 � §3.11: Reference to appendices A and B 
introduced; §4.11.1 � §3.11.1: Added specification of transmit duty cycles. Requirement “industry 
available part” introduced in clause 7; §4.11.2 � §3.11.2: New requirement on not asserting RxD 
when locked dominant. New requirement on bus output short circuit current. Clause 9 changed to 
define behavior of transmit buffers. Clause 15 for controller devices eliminated. Clause 16 dealing 
with wakeup on bus traffic changed. Changed wording of clause 23, microcontroller support for 
detection of corrupted memory. Requirement “industry available part” introduced in clause 27. 
Clause 28 added. 
§3.11.3: New section specifying set up of the CAN Controller TxD line; §5 � §4; §5.1 � §4.1: Bit 
time tolerance changed to 0.45%. Tolerance at component delivery eliminated. Tolerance statement 
for GM tools introduced. All bit timing options except of 16 and/or 20 time quanta per bit eliminated; 
§5.2 � §4.2 and §5.2: Reference to GME14010 introduced. References to SAE and ISO eliminated. 
Upper limit for resistance of the wiring harness introduced. Twist rate and specific line delay specs 
eliminated. Clarification to the term twist introduced. Note on splices introduced; §6 � §5; §6.1 � 
§5.1; §6.2 � §5.2; §7 � §6: CAN I/O test specifications replaced by a reference to GME6718; §7.1 
� §6.1; §7.2 � §6.2; §7.2.1 � §6.2.1; §7.2.2 � §6.2.2; §7.2.3 � §6.2.3; §8 � §7; §9 New 
Section; §10 New Section; §11 New Section; Appendix A, Appendix B: List of known device-specific 
issues replaced by a reference to the GMLAN web page; Appendix C: Reference circuit for 
Communication Enable I/Os introduced. 
D 
NOV 2005 
§1.1, §2.1, §3.2.4, §5, §5.1, §5.2 � Added requirements for a 125 kbit/s mid-speed bus. 
§3.5.3 � Information on Central Bus Termination moved here from section 5.1. 
§3.11.1.1.12 � Clarified when this requirement applies. 
§3.2.3 � Changed PPEI reference from GMW3119 to GMW8763. 
§3.3.3 � Modified Input thresholds and output voltages for Wake Up Line. 
§3.5.3 � Modified wake up line requirements to be compatible with micro I/O pin. 
Note: At the time of publication, GME6718 was in the process of being made inactive, replaced by 
GMW14241. 
 
 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 29 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 29 of 42
 
Appendix A 
Reported Transceiver Component Problems . 
Note: CAN transceiver requirements have been 
relocated to section 3.11.1. 
Please refer to the GMLAN website for the latest 
list of known CAN transceiver issues and 
precautions. 
The 
GMLAN 
website 
is: 
http://ived.gm.com/electrical/warren/ssltexpt/gmlan/
index.html 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 30 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 30 of 42 
November 2005
 
Appendix B 
Reported CAN Controller Component Problems  
Note: CAN controller requirements have been 
relocated to section 3.11.2. 
Please refer to the GMLAN website for the latest 
list of known CAN controller issues, resolutions 
and precautions. 
The 
GMLAN 
website 
is: 
http://ived.gm.com/electrical/warren/ssltexpt/gmlan/
index.html 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 31 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 31 of 42
 
Appendix C 
 
Implementation Guidelines WAKE UP WIRE 
Example Mechanization for Wakeup Technique 3
Voltage
Regulator
Comm 
Enable
Line Pin
Detecting
Circuits
Generating
Circuits
Vbatt
uC
Vcc
CAN-
Transceiver
IRQ
Vbatt
CAN-
Controller
Comm Enable 
Control Output
(typically only present on 
one ECU in the subnet)
Inhibit
Hold
Signal 
condition-
ing
Comm Enable
Line Input 
(all ECUs)
 
Figure C1: Implementation Guideline Wake Up Wire 
 
Note that applicable sleep/standby mode power 
consumption ratings are to be specified in the 
component technical specification. 
The wakeup input circuit provides a resistive pull-
down behavior to ECU ground. This can be 
implemented for example with a resistance of R = 
8.2 kΩ nominal, ± 10% tolerance, P = 50 mW no 
time limit and P =100 mW for 1 minute over the 
ECU operating ambient temperature range.  
It is recommended the wake up input filter function 
be implemented using passive filter components. 
If an ECU has several interrupt sources (wake up 
wire, door open) it is recommended these interrupt 
sources be polled on an I/O-port in order to quickly 
identify the signal source that initiated the wake up 
event. 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 32 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 32 of 42 
November 2005
 
Appendix D 
 
Propagation Delay (tPROP) 
Because CAN is a protocol employing bit by bit arbitration, the network propagation delay is equal to the 
complete round trip signal delay time for one CAN controller to another one and back. This translates to the 
following equation: 
tPROP = 2(tTX + tRX + tLOGIC + tCHK + tBUS) 
or 
tPROP = 2(tECU + tBUS) 
 
with Test tool: 
 
tPROP = tECU + tTtool + 2*tBUS 
Bit Timing Requirements  
The following table specifies the CAN bit timing requirements. Coordinated bit timing settings are required to 
maintain synchronization between modules during both normal and error conditions. 
 
Parameter 
Min (ns) 
Nominal (ns) 
Max (ns) 
tBIT * 
1991 
2000 
2009 
tBUS** 
0 
----- 
165 
tECU 
30 
----- 
350 
tTtool 
----- 
----- 
390 
tQNom 
- 
125 or 100 
- 
tSEG1 
*** 
*** 
*** 
* The nominal bit time typically is a programmable, integer multiple of the controller IC´s oscillator clock periods. 
** tBUS considers one trip through 30 meters using a bus cable with a specific line delay of less than 5.5 ns/m 
(5 ns/m nominal). 
*** tSEG1 = tBIT - tSEG2 - tQ 
 
The following table specifies all acceptable bit timing settings. 
tQ/tB  
(number of time 
quanta per bit) 
tQNom (ns)  
tB tolerance (%) 
tTSEG2 
tSJW  
BTR0 
(hex) Note 1 
BTR1 
(hex) 
16 
125 
0.5 
3 tQ 
3 tQ 
$80 
$2B 
20 
100 
0.5 
4 tQ 
4 tQ 
$C0 
$3E 
Note 1: BTR0 and BTR1 values apply to the SJA1000 controller when clocked with 16 MHz. Refer to data sheet and/or contact the 
semiconductor manufacturer for other controller products or clock speeds. 
 
The above bit timing register settings BTR0 and BTR1 are typical values. Please refer to the particular product 
data sheet for exact information.  
Bit settings for above time quanta has been calculated from the following equations: 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 33 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 33 of 42
 
 
Note: tBIT is always set to 2000 ns. If the ECU is unable to be programmed such as to allow tBIT nominal to be 
equal to 2000 ns, then the offset shall be taken into account in the ∆f term and not in the tBIT term . 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
20tBIT∆f 
 
∆f(20tBIT - tQ) + tQ - tPROP min 
tSJW 
≥ 
maximum of 
1 - ∆f 
or 
1 + ∆f 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
tSEG2min 
≥ 
maximum of 
tSJW 
or 
2 tQ 
 
 
 
 
 
 
 
 
 
 
or 
∆f(24tBIT - tQ) + tQ - tPROP min 
 
 
 
 
 
1 + ∆f 
 
 
 
 
 
 
 
 
 
tBIT(1 - 25∆f) - tPROP max  
tBIT - tPROP max - tQ - ∆f(25tBIT - tQ) + tPROPmin / 2 
tSEG2max 
< 
minimum of 
1 - ∆f 
or 
1 - ∆f 
 
 
 
 
 
 
Definition: ∆f equals the maximum deviation (either maximum or minimum) from the specified nominal bit rate 
divided by the specified nominal bit rate for specified values. 
Implementation Example  
Note: This example applies to the 82527. Other controller products may employ different clock division 
mechanisms and need different bus timing register settings relative to this example. See the applicable data 
sheets and application info of the semiconductor manufacturer. 
Implementation example for CAN bit timing parameters on a High Speed Bus. 
 
Reference circuit used in the example:  Intel 527 with an oscillator frequency of 16 MHz. 
The system clock (SCLK) is set to:  0.5 * 16 MHz 
= 8 MHz ± 0.45%  (DSC = 1) 
Prescaler value (BRP) is set to:   
0 
 
Nominal time quantum length 
 
tQ = tSCLK * (BRP + 1) = 2 * (BRP + 1) / fXTAL = 125 ns 
If the number of time quanta per bit is set to 16. That gives: 
Nominal bit time:  
 
 
 
 
tBIT = 2000 ns 
Nominal baudrate:  
 
 
 
f = 500 kbit/s 
 
tBIT = tSYNC_SEG + tSEG1 + tSEG2  
tSYNC_SEG = 1 tQ 
 
With tSEG1 and tSEG2 set to:  
 
 
tSEG1 = (TSEG1 + 1) * tQ = 12 tQ  
tSEG2 = (TSEG2 + 1) * tQ = 3 tQ 
That gives: 
Nominal sample point position: (tSEG1 + tSYNC_SEG) / tBIT = (12 tQ + 1 tQ) / 16 tQ = 0.8125 
equals 81.25 % 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 34 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 34 of 42 
November 2005
 
Resynchronization Jump Width is set to:  
 
tSJW = (SJW + 1) * tQ = 3 tQ  
That gives: 
SJW:   
 
 
 
 
 
 
 
 
 
tSJW /tBIT = 3 tQ / 16 tQ = 0.1875 equals 18.75 % 
Several suitable clock frequency examples with their associated calculated CAN bit settings are shown in the 
table below: (Note: The values TSEG1, TSEG2, DSC, BRP, BTR0 and BTR1 apply to the 82527 controller. 
Refer to data sheets and application info for other controller products or clock frequencies. 
 
fxtal  
(MHz) 
TSEG1 
TSEG2 
SJW 
DSC 
BRP 
BTR0 
BTR1 
CANbus 
speed 
(kb/s) 
tQ (ns) 
SJW  
(%) 
Sample 
point 
(%) 
16 
11 
2 
2 
1 
0 
$80 
$2B 
500 
125 
18.75 
81.25 
20 
14 
3 
3 
1 
0 
$C0 
$3E 
500 
100 
20 
80 
The above bit timing register settings BTR0 and BTR1 are typical values. Please refer to the particular product 
data sheet for exact information. Note the operation mode of the controller IC´s CAN transmit output (e.g., 
TX0) shall be set to push-pull operation with active low polarity. 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 35 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 35 of 42
 
Appendix E 
125 kbit/s Medium Speed Bit Timing Example 
Propagation Delay (tPROP) 
Because CAN is an arbitrating protocol, the propagation delay must take into account the time required for a 
signal to make a complete round trip from one ECU to another and back. This translates to the following 
equation: 
 
tPROP = 2(tTX + tRX + tLOGIC + tCHK + tBUS) 
or 
tPROP = 2(tECU + tBUS) 
Bit Timing Requirements  
The following table defines the CAN bit timing requirements. Coordinated bit timing settings are required to 
maintain synchronization between modules during both normal and error conditions. 
 
Parameter 
Min (ns) 
Nominal (ns) 
Max (ns) 
tBIT * 
7960 
8000 
8040 
tBUS** 
0 
----- 
400 
tECU 
30 
----- 
1000 
tQNom 
3200 
----- 
1000 
tSEG1 
*** 
*** 
*** 
* The nominal bit time is typically a programmable, integer multiple of the CAN controller oscillator clock periods. 
** tBUS considers one trip through 40 meters using a bus cable with a specific line delay of less than 8 ns/m. 
*** tSEG1 = tBIT - tSEG2 - tQ 
 
The following table shows compliant bit timing settings for 125 kbit/s medium-speed CAN:  
 
tQ/tB  
(number 
of 
time 
quanta 
per bit) 
tQNom  
Sampling 
Mode 
tTSEG2 
tSJW  
BTR0 
(hex) Note 1 
BTR1 
(hex) 
12 
666.67 ns 
(1.5 MHz) 
Single 
3 tQ 
3 tQ 
$8… 
$27 
13 
615.38 ns 
(1.625 MHz) 
Single 
3 tQ 
3 tQ 
$8… 
$28 
14 
571.43 ns 
(1.75 MHz) 
Single 
3 tQ 
3 tQ 
$8… 
$29 
15 
533.33 ns 
(1.875 MHz) 
Single 
3 tQ 
3 tQ 
$8… 
$2A 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 36 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 36 of 42 
November 2005
 
tQ/tB  
(number 
of 
time 
quanta 
per bit) 
tQNom  
Sampling 
Mode 
tTSEG2 
tSJW  
BTR0 
(hex) Note 1 
BTR1 
(hex) 
16 
500 ns 
(2.0 MHz) 
Single 
3 tQ 
3 tQ 
$8… 
$2B 
17 
470.59 ns 
(2.125 MHz) 
Single 
3 tQ 
3 tQ 
$8… 
$2C 
18 
444.44 ns 
(2.25 MHz) 
Single 
3 tQ 
3 tQ 
$8… 
$2D 
19 
421.05 ns 
(2.375 MHz) 
Single 
3 tQ 
3 tQ 
$8… 
$2E 
20 
400 ns 
(2.5 MHz) 
Single 
4 tQ 
4 tQ 
$C… 
$3E 
21 
380.95 ns 
(2.625) 
Single 
4 tQ 
4 tQ 
$C… 
$3F 
Note 1: BTR0 and BTR1 values apply to the SJA1000 controller. The BTR0 setting depends on the particular oscillator frequency used. 
Refer to applicable data sheets for other controller products. 
 
The above bit timing register settings BTR0 and BTR1 are typical values. Please refer to the particular product 
data sheet for exact information.  
Bit settings for above time quanta has been calculated from the following equations: 
 
Note: tBIT is always set to 8000 ns. If the ECU is unable to be programmed to allow tBIT nominal to be equal to 
8000 ns, then the offset shall be taken into account in the ∆f term not in the tBIT term . 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
20tBIT∆f 
 
∆f(20tBIT - tQ) + tQ - tPROP min 
tSJW 
≥ 
maximum of 
1 - ∆f 
or 
1 + ∆f 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
tSEG2min 
≥ 
maximum of 
tSJW 
or 
2 tQ 
 
 
 
 
 
 
 
 
 
 
or 
∆f(24tBIT - tQ) + tQ - tPROP min 
 
 
 
 
 
1 + ∆f 
 
 
 
 
 
 
 
 
 
tBIT(1 - 25∆f) - tPROP max 
 
tBIT - tPROP max - tQ - ∆f(25tBIT - tQ) + tPROPmin / 2 
tSEG2max 
< 
minimum of 
1 - ∆f 
or 
1 - ∆f 
 
 
 
 
 
 
Definition: ∆f equals the maximum allowable deviation (either maximum or minimum) from the specified 
nominal bit rate divided by the specified nominal bit rate for specified values. 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 37 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 37 of 42
 
Implementation Example 
Implementation example of CAN bit time parameters on an Infotainment Bus. 
Reference circuit used in the example:   
Intel 527 
The system clock (SCLK) is set to:   
 
8 MHz ± 0.5% 
Prescaler value (BRP) is set to:   
 
 
4 
 
Nominal time quantum length: 
 
 
 
tQ = tSCLK * (BRP + 1) 
 
 
 
 
 
 
 
 
 
 
 
tQ = 500 ns 
If the number of tQ (N) is set to 21. That gives: 
Nominal bit time:  
 
 
 
 
 
 
tBIT = 8 µs. 
Nominal baudrate:  
 
 
 
 
 
f = 125 kbit/s 
tBIT = tSYNC_SEG + tSEG1 + tSEG2  
tSYNC_SEG = 1 tQ 
With tSEG1 and tSEG2 is set to:  
 
 
 
tSEG1 = (TSEG1 + 1) * tQ = 12 tQ  
tSEG2 = (TSEG2 + 1) * tQ = 3 tQ 
That gives: 
Sample Point: (tSYNC_SEG + tSEG1)/ tBIT 
 
81.25 % 
reSyncronisation Jump Width is set to:  
tSJW = (SJW + 1) * tQ = 3 tQ  
That gives: 
SJW: (tSJW/ tBIT)  
 
 
 
 
 
 
18.75 % 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 38 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 38 of 42 
November 2005
 
Several suitable clock frequency examples with their associated calculated CAN bit settings are shown in the 
table below: 
 
fxtal 
(MHz) 
TSEG1 TSEG2 
SJW 
DSC 
BRP Note 1
CANbus 
speed 
(kb/s) 
tQ (ns) 
SJW 
(%) 
Sample point 
(%) 
4 
11 
2 
2 
0 
1 
125 
500 
18.75 
81.25 
8 
11 
2 
2 
0 
3 
125 
500 
18.75 
81.25 
12 
11 
2 
2 
0 
5 
125 
500 
18.75 
81.25 
16 
11 
2 
2 
0 
7 
125 
500 
18.75 
81.25 
20 
11 
2 
2 
0 
9 
125 
500 
18.75 
81.25 
Note the operation mode of the controller IC´s CAN transmit output (e.g. TX0) shall be set to push-pull operation with active low polarity. 
Note 1: DSC and BRP values apply to the 82527 controller. See controller data sheets and application info for other products or clock 
frequencies. 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 39 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 39 of 42
 
95.238 kbit/s Medium Speed Bit Timing Example 
Propagation Delay (tPROP) 
Because CAN is an arbitrating protocol, the propagation delay must take into account the time required for a 
signal to make a complete round trip from one ECU to another and back. This translates to the following 
equation: 
 
tPROP = 2(tTX + tRX + tLOGIC + tCHK + tBUS) 
or 
tPROP = 2(tECU + tBUS) 
Bit Timing Requirements  
The following table defines the CAN bit timing requirements. Coordinated bit timing settings are required to 
maintain synchronization between modules during both normal and error conditions. 
 
Parameter 
Min (ns) 
Nominal (ns) 
Max (ns) 
tBIT * 
10447.5 
10500 
10552.5 
tBUS** 
0 
----- 
320 
tECU 
30 
----- 
1000 
tQNom 
500 
----- 
750 
tSEG1 
*** 
*** 
*** 
* The nominal bit time is typically a programmable, integer multiple of the CAN controller oscillator clock periods. 
** tBUS considers one trip through 40 meters using a bus cable with a specific line delay of less than 8 ns/m. 
*** tSEG1 = tBIT - tSEG2 - tQ 
 
The following table shows compliant bit timing settings for medium-speed CAN:  
tQ/tB  
(number of time 
quanta per bit) 
tQNom  
Sampling 
Mode 
tTSEG2 
tSJW  
BTR0 
(hex)Note 1 
BTR1 
(hex) 
14 
750 
ns 
(1.333 MHz) 
Single 
3 tQ 
3 tQ 
$8… 
$29 
15 
700 
ns 
(1.4286 MHz) 
Single 
3 tQ 
3 tQ 
$8… 
$2A 
16 
656.25 
ns 
(1.5238 MHz) 
Single 
4 tQ 
3 tQ 
$8… 
$3A 
Note 1: BTR0 and BTR1 values apply to the SJA1000 controller. The BTR0 setting depends on the particular oscillator frequency used. 
Refer to applicable data sheets for other controller products. 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 40 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 40 of 42 
November 2005
 
The following table shows compliant bit timing settings for medium-speed CAN: 
tQ/tB  
(number 
of 
time 
quanta 
per bit) 
tQNom  
Sampling 
Mode 
tTSEG2 
tSJW  
BTR0 
(hex) Note 1 
BTR1 
(hex) 
14 
750 
ns 
(1.333 MHz) 
Single 
3 tQ 
3 tQ 
$8… 
$29 
15 
700 
ns 
(1.4286 MHz) 
Single 
3 tQ 
3 tQ 
$8… 
$2A 
16 
656.25 
ns 
(1.5238 MHz) 
Single 
4 tQ 
3 tQ 
$8… 
$3A 
17 
617.65 
ns 
(1.619 MHz) 
Single 
4 tQ 
3 tQ 
$8… 
$3B 
18 
583.33 
ns 
(1.7143 MHz) 
Single 
4 tQ 
3 tQ 
$8… 
$3C 
19 
522.63 
ns 
(1.8095 MHz) 
Single 
4 tQ 
3 tQ 
$8… 
$3D 
20 
525 
ns 
(1.9048 MHz) 
Single 
5 tQ 
4 tQ 
$C… 
$4D 
21 
500 
ns 
(2 MHz) 
Single 
5 tQ 
4 tQ 
$C… 
$4E 
18 
583.33 
ns 
(1.7143 MHz) 
3x 
3 tQ 
3 tQ 
$8… 
$AC 
19 
522.63 
ns 
(1.8095 MHz) 
3x 
3 tQ 
3 tQ 
$8… 
$AD 
20 
525 
ns 
(1.9048 MHz) 
3x 
4 tQ 
4 tQ 
$C… 
$BD 
21 
500 
ns 
(2 MHz) 
3x 
4 tQ 
4 tQ 
$C… 
$BE 
Note 1: BTR0 and BTR1 values apply to the SJA1000 controller. The BTR0 setting depends on the particular oscillator frequency used. 
Refer to applicable data sheets for other controller products. 
 
The above bit timing register settings BTR0 and BTR1 are typical values. Please refer to the particular product 
data sheet for exact information.  
Bit settings for above time quanta has been calculated from the following equations: 
 
Note: tBIT is always set to 10500 ns. If the ECU is unable to be programmed to allow tBIT nominal to be equal to 
10500 ns, then the offset shall be taken into account in the ∆f term not in the tBIT term . 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
20tBIT∆f 
 
∆f(20tBIT - tQ) + tQ - tPROP min 
tSJW 
≥ 
maximum of 
1 - ∆f 
or 
1 + ∆f 
 
 
 
 
 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 41 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3122
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
November 2005 
Page 41 of 42
 
 
 
 
 
 
 
 
 
 
 
 
 
tSEG2min 
≥ 
maximum of 
tSJW 
or 
2 tQ 
 
 
 
 
 
 
 
 
 
 
or 
∆f(24tBIT - tQ) + tQ - tPROP min 
 
 
 
 
 
1 + ∆f 
 
 
 
 
 
 
 
 
 
tBIT(1 - 25∆f) - tPROP max 
 
tBIT - tPROP max - tQ - ∆f(25tBIT - tQ) + tPROPmin / 2 
tSEG2max 
< 
minimum of 
1 - ∆f 
or 
1 - ∆f 
 
 
 
 
 
 
Definition: ∆f equals the maximum allowable deviation (either maximum or minimum) from the specified 
nominal bit rate divided by the specified nominal bit rate for specified values. 
Implementation Example 
Implementation example of CAN bit time parameters on an Infotainment Bus. 
Reference circuit used in the example:   
Intel 527 
The system clock (SCLK) is set to:   
 
8 MHz ± 0.5% 
Prescaler value (BRP) is set to:   
 
 
3 
 
Nominal time quantum length: 
 
 
 
tQ = tSCLK * (BRP + 1) 
 
 
 
 
 
 
 
 
 
 
 
tQ = 500 ns 
If the number of tQ (N) is set to 21. That gives: 
Nominal bit time:  
 
 
 
 
 
 
tBIT = 10.5 µs. 
Nominal baudrate:  
 
 
 
 
 
f = 95.238 kbit/s 
tBIT = tSYNC_SEG + tSEG1 + tSEG2  
tSYNC_SEG = 1 tQ 
With tSEG1 and tSEG2 is set to:  
 
 
 
tSEG1 = (TSEG1 + 1) * tQ = 15 tQ  
tSEG2 = (TSEG2 + 1) * tQ = 5 tQ 
That gives: 
Sample Point: (tSYNC_SEG + tSEG1)/ tBIT 
 
76.19 % 
reSyncronisation Jump Width is set to:  
tSJW = (SJW + 1) * tQ = 4 tQ  
That gives: 
SJW: (tSJW/ tBIT)  
 
 
 
 
 
 
19 % 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---


### 第 42 页
GMW3122 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
Page 42 of 42 
November 2005
 
Several suitable clock frequency examples with their associated calculated CAN bit settings are shown in the 
table below: 
 
fxtal 
(MHz) 
TSEG1 TSEG2 
SJW 
DSC 
BRP Note 1
CANbus 
speed 
(kb/s) 
tQ (ns) 
SJW 
(%) 
Sample point 
(%) 
4 
14 
4 
3 
0 
1 
95.238 
500 
19 
76.19 
8 
14 
4 
3 
0 
3 
95.238 
500 
19 
76.19 
12 
14 
4 
3 
0 
5 
95.238 
500 
19 
76.19 
16 
14 
4 
3 
0 
7 
95.238 
500 
19 
76.19 
20 
14 
4 
3 
0 
9 
95.238 
500 
19 
76.19 
Note the operation mode of the controller IC´s CAN transmit output (e.g. TX0) shall be set to push-pull operation with active low polarity. 
Note 1: DSC and BRP values apply to the 82527 controller. See controller data sheets and application info for other products or clock 
frequencies. 
 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:IHS Standards Store Purchase, 358112
Not for Resale,08/24/2006 11:37:59 MDT
No reproduction or networking permitted without license from IHS
--`,,,,,``,````,,,``,``,`,````,`,,`,`,,`,`,,,,```,``,-`-`,,`,,`,`,,`---

