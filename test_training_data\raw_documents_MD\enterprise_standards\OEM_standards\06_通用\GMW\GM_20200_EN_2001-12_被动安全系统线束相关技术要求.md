# GM_20200_EN_2001-12_被动安全系统线束相关技术要求.pdf

## 文档信息
- 标题：CM20200 Resraint Systems Connector and Wiring Technical Specification
- 作者：<PERSON>
- 页数：20

## 文档内容
### 第 1 页
 
 
 
 
 
 
  
 
 
 
General Motors 
 
 
 
Restraint Systems Connector and Wiring 
 Technical Specification 
December 13, 2001 
Release  
 
 
 
 
DOCUMENT  NUMBER 
REVISION 
CM  20200 
2.7 
 
 
This document and the information contained in it are considered sensitive to General Motors Corporation.  It is 
recognized that cases will exist where organizations outside of GM will require use of this document.  The personnel 
in these organizations are expected to treat this document and its contents as they treat their own company sensitive 
material. Under no circumstances shall any organization outside of  General Motors  disclose this document, its 
contents, or any sensitive information derived from the document to any third party.  Any organization that feels it 
cannot honor the above commitment is expected to return this document and all copies immediately. 
 
 
 
  GM Number      20200 
Last Page: 
14 
Date: 
December 13, 2001 


### 第 2 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200  Rev. 2.7  Release      
 
12/1301 
 
i 
Preface 
 
This Technical Specification was developed by the members of the RSEE (Restraint System 
Electrical Engineering) team and the Electrical Center Wiring SSLT, and committees of NAO 
Vehicle Engineering, Interior, and Electrical Centers. The specification is available from and will 
be maintained by the NAO Electrical Center. 
 
This specification is under configuration management by the NAO Electrical Center and changes 
to the document must be acted on by the EC Wiring SSLT with approval from the electrical 
DREs in the Safety Electronics organization.   
 
 


### 第 3 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200  Rev. 2.7  Release      
 
12/1301 
 
ii 
 
 
REVISION UPDATES - COMMENTS 
 
The Revision Updates table indicates all significant changes made to this document since 
revision 2.0 was released.   Errors associated with punctuation, spelling, or minor clarification 
have not been documented in the table for the sake of brevity.  All changes that affect the design 
or functionality of the interfacing units have been included. 
 
Revision Updates 
 
Revision # 
Date 
Description 
2.0 
11/11/98 
Baseline - developed by RSEE and approved by ABSSLT 
2.1 
2/16/99 
Proposed additions/changes to sections 3.2.1, 3.2.7, 3.2.8, 
3.2.12, 3.3.1 and section 6. 
2.2 
9/7/99 
Clarification of wording,  added redundant ground, 
requirements on SDM connector.  Sections affected: 1.3, 2.3, 
3.2.1, 3.2.2, 3.2.6, 3.2.7, 3.2.11, 3.2.14, 3.3.1, 3.3.3., 3.3.5-
3.3.7. 
2.3 
11/18/99 
Additional requirements for fusing for clockspring coil, 
shorting clips.  3.1.2, 3.2.1, 3.2.3, 3.2.8, 3.2.11, 3.3.1, 3.3.5, 
5.1, 6.1 . 
2.4 
3/9/00 
Add requirements for Ergonomic guidelines 
2.5 
4/28/00 
Section 3.2.11 amended to expand from SDM only to other 
SIR connectors.  Section 3.1 lower temperature range 
redefined.  SO (Service Operations) name change. 
2.6 
9/5/00 
Added ergonomic guidelines reference. 
 
9/5/00 
Add the word ‘connector’ to the end of the sentence in 3.2.12, 
item b. 
 
9/5/00 
3.2.11.  Add requirement that mating female half of SDM 
connector needs to be marked with part number, etc. 
 
9/5/00 
Changed title from “SIR” to “Restraint Systems” 
1.1 Expand scope to include seat belt pretensioners. 
3.2.1 Modified requirement so that yellow connectors used 
in pretensioners are not be used in non-restraint 
systems. 
 
Added item b. that a CPA is required.  The spec 
implied this but not explicitly. 
 
Removed item h.  Redundant with section 3.2.7 
 
3.2.3  Added Rollover sensors to the requirement 
3.2.6  Specify 20 or 22 gauge wire as a requirement 
3.2.8 Added “pretensioner” to the requirement. 
More clearly defined the max number of connectors 


### 第 4 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200  Rev. 2.7  Release      
 
12/1301 
 
iii 
Revision # 
Date 
Description 
used in SIR and pretensioner deployment loop circuits. 
3.2.9 Add pretensioner circuits to the requirement. 
3.2.12 Replaced “TBD” for in-line connector gold interface 
with more precise requirement. 
3.2.14  No sealing required for Rollover Sensors 
2.7 
8/20/01 
2.5  Added Definition section and mech. Drawing 
 
8/20/01 
3.2.1 Added requirement to secure in-line connectors to body. 
 
8/20/01 
3.2.3   Item f and g. added.  
Require ROS connector to be yellow.  
 
8/20/01 
3.2.6.  Clarified wire packing density for multi-pin connectors, 
item b. 
 
8/20/01 
3.2.7. Corrected web URL for ergonomic guidelines. 
 
8/20/01 
3.2.8.  Deleted phrase “under low energy conditions.” 
 
8/20/01 
3.2.9.  Deleted requirement that terminal crimps must be 
capable of being soldered. (tin core obviates this need)  
 
8/20/01 
3.2.11.  Added requirement that a common in-line connector is 
allowable for front seat airbags, suppression and other circuits. 
 
8/20/01 
3.2.12 Clarify when gold must be used for shorting clips. 
Clarify that shorting clips “may” be used on pretensioner in-
line connectors. 
 
8/20/01 
******* b. was added to allow sonic welding for clockspring 
coils. 
 
8/20/01 
*******  Clarify that twisted pair is required for deployment 
loops based on Lessons Learned. 
 
8/20/01 
3.3.7.  Remove language for RFQ options using twisted pair. 
 
8/20/01 
Added numerous comments as supporting rationale. 
 
9/11/01 
2.5  Add note to figure 2.5-1 that Class 2 systems do not use 
battery for the SDM feed. 


### 第 5 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200  Rev. 2.7  Release      
 
12/1301 
 
iv 
 
Table of Contents 
 
 
1 
INTRODUCTION .............................................................................................................................................. 1 
1.1 
SCOPE OF DOCUMENT ...................................................................................................................................... 1 
1.2 
MISSION/THEME .............................................................................................................................................. 1 
1.3 
CLASSIFICATION .............................................................................................................................................. 1 
2 
APPLICABLE DOCUMENTS ......................................................................................................................... 1 
2.1 
ORDER OF PRECEDENCE ................................................................................................................................... 1 
2.2 
GOVERNMENT DOCUMENTS ............................................................................................................................. 1 
2.3 
GENERAL MOTORS DOCUMENTS ..................................................................................................................... 1 
2.4 
INDUSTRY DOCUMENTS ................................................................................................................................... 1 
2.5 
DEFINITIONS .................................................................................................................................................... 1 
3 
REQUIREMENTS ............................................................................................................................................. 4 
3.1 
AMBIENT CONDITIONS ..................................................................................................................................... 4 
3.2 
CONNECTORS ................................................................................................................................................... 4 
3.2.1 
General Requirements ........................................................................................................................... 4 
3.2.2 
Connector Keys ...................................................................................................................................... 5 
3.2.3 
Connector Colors ................................................................................................................................... 5 
3.2.4 
Target Life ............................................................................................................................................. 5 
3.2.5 
Mate/Unmate Cycles .............................................................................................................................. 6 
3.2.6 
Wire Gauge Capability .......................................................................................................................... 6 
3.2.7 
Connection Engage/Disengage Force ................................................................................................... 6 
3.2.8 
Interconnect Resistance ......................................................................................................................... 6 
3.2.9 
Terminals ............................................................................................................................................... 7 
3.2.10 
Drop Impacts ......................................................................................................................................... 7 
3.2.11 
SDM and Other Connectors................................................................................................................... 7 
*******.3 Figure 3.2.11-1 Dual Cavity Connector ....................................................................................... 8 
3.2.12 
Shorting Clips ........................................................................................................................................ 8 
3.2.13 
Satellite Sensors ..................................................................................................................................... 9 
3.2.14 
Sealing ................................................................................................................................................... 9 
******** 
Modules.................................................................................................................................................................. 9 
******** 
In-Line Connectors ................................................................................................................................................ 9 
3.2.15 
Crush Strength ....................................................................................................................................... 9 
3.3 
WIRING ............................................................................................................................................................ 9 
3.3.1 
Cable Type/Insulation .......................................................................................................................... 10 
3.3.2 
Splicing ................................................................................................................................................ 10 
3.3.3 
Grounding ............................................................................................................................................ 10 
3.3.4 
Caution Labels ..................................................................................................................................... 10 
3.3.5 
Fusing .................................................................................................................................................. 10 
3.3.6 
Deployment Loop ................................................................................................................................. 10 
******* Crimp Interface ........................................................................................................................................................ 10 
3.3.6.2 Deployment Loop Polarity ...................................................................................................................................... 11 
******* Deployment Loop Wiring........................................................................................................................................ 11 
3.3.7 
Satellite Sensors ................................................................................................................................... 11 
3.3.8 
SIR Content Inclusion into Vehicle Wiring/Yellow Tape ..................................................................... 11 
3.3.9 
Wiring Harness Retention .................................................................................................................... 11 
4 
VALIDATION .................................................................................................................................................. 12 
4.1 
WATER SUBMERSION ..................................................................................................................................... 12 
5 
PROVISIONS FOR SHIPPING ..................................................................................................................... 12 


### 第 6 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200  Rev. 2.7  Release      
 
12/1301 
 
v 
5.1 
ASSEMBLY PLANT PREPARATION. .................................................................................................................. 12 
6 
NOTES .............................................................................................................................................................. 12 
6.1 
GLOSSARY ..................................................................................................................................................... 12 
6.2 
ACRONYMS, ABBREVIATIONS, AND SYMBOLS ............................................................................................... 13 


### 第 7 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
1 
1 
INTRODUCTION 
 
1.1 Scope of Document 
This specification establishes the NAO technical requirements for both halves of connection 
systems used with all SIR (Supplemental Inflatable Restraint) electrical modules and seat belt 
pretensioners.  It does not include the inflator module's integral connector which has its 
requirements defined in GMW-3109GS-5.  This includes for example, the SDM, the SIS (Side 
Impact Sensor), EFS (Electronic Front Sensor), AOS (Automatic Occupant Sensor for 
suppression), ROS (Rollover Sensor).  It also includes requirements for SIR wiring and in-line 
connections. 
 
1.2 Mission/Theme 
The SDM, SIS, EFS, ADS, ROS, and AOS are components of an occupant restraint system for 
making decisions on airbag and belt pretensioner deployments during vehicle crash conditions.  
 
1.3 Classification 
NAO-produced vehicles and new connection system designs beginning with the MY2004 shall 
comply with this document.   Existing, off-the-shelf connection systems - specifically, those, 
which were designed prior to MY2001, do not have to be redesigned to comply with this 
document. 
 
2 
APPLICABLE DOCUMENTS 
 
2.1 Order of Precedence 
In the event of a conflict between the text of this specification and the documents cited herein, 
the text of this specification takes precedence. 
 
2.2 Government Documents 
None cited. 
 
2.3 General Motors Documents 
 
 
Document 
Title 
Available From 
Date 
EICC-80.201.01 
Power and Signal Distribution Subsystem 
Technical Specification 
Electrical Center 
10/15/98 
CM-20239 
Water Submersion Test Procedure 
Electrical Center 
7/1/96 
GMW-3109 GS-5 
General Specification for Airbag Module 
Components 
Interior Center 
8/26/98 
GMW3109GS-5-1-R-1 
Frontal Air Bag Module Specification 
Interior Center 
5/25/01 
GMW3115GS-5-2-R-1 
Side  Air Bag Module Specification 
Interior Center 
 
 
 
2.4 Industry Documents 
 
Document  
Title 
Available From 
Date 
SAE/USCAR-3 
Standard for Automotive Electrical Connection 
Systems 
USCAR 
3/2/01 
Definitions 


### 第 8 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
2 
 
2.5.1 Terminology 
The word “shall” is used to state binding requirements of the component defined by this document.  
These requirements will be verifiable. 
The word “must” states requirements of other components or the supplier. 
The words “are” and “is” defines facts that do not require verification. 
The term “withstand” is defined as: 
 
“Maintains design intended functional and structural integrity while being 
 
 
subjected to the specified conditions.” 
 
The word “should” is used to define a best practice that is not a binding requirement.   
 
2.5.2 Occupant Protection Overview 
The Occupant Protection System consists of three types of circuits. 
 
1. Pretensioners 
2. Airbags 
3. Sensors 
 
Each seat belt pretensioner and airbag circuit consists of wiring, connectors, and an 
electroexplosive squib used to activate the particular airbag or seat belt pretensioner.  Each of 
these circuits is routed from the SDM (Sensing and Diagnostic Module) to the pretensioner or 
airbag with a return to the SDM. This path is oftentimes referred to as a deployment loop. 
Airbags are also referred to as Supplemental Inflatable Restraints (SIR).  Figure 2.5-1 shows a 
simplified block diagram for GMLAN-based SDMs.  Class 2 SDMs are not directly powered 
through battery but through the Ignition switch to battery. 
 
Sensors include crash sensors, seat belt and seat position sensors, and suppression sensors.  
Crash sensors include Side Impact Sensors (SIS), Electronic Front Sensors (EFS), and Rollover 
Sensors (ROS).  Earlier airbag systems used an Auxiliary Discriminating Sensor (ADS) which 
was an electromechanical version of the EFS. These devices are used to provide early detection 
of a crash and feed this information to the SDM where further processing occurs.  Sensors are 
never referred to as deployment loops (they don’t directly control the deployment of anything). 
 
Suppression sensors are either manual cutoff switches used to disable the driver and/or passenger 
frontal airbag or an automatic suppression (passive) sensor to disable the passenger airbag if a 
small child or other occupant is at risk from a deploying airbag. 
 
Seat belt sensors indicate if an occupant’s seat belt is fastened and seat position sensors indicate 
if the seat is forward or rearward of a reference point.  This information can be used to reduce 
inflation rates to minimize potential injuries to occupants who may be too close to the steering 
wheel or instrument panel in a crash.   
 


### 第 9 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
3 
More information on basic airbag operation is available on the web at 
http://home.gm.com/occupant_protection and provides additional restraint system information and 
background for the interested reader. 
 
 
 
Figure 2.5-1 Simplified Block Diagram 
 
Note:  SDMs that use the Class 2 serial data link do not operate off battery.  IGN1 (battery or 
crank) feeds the SIR fuse which in turn feeds the SDM, the Suppression and rollover sensors. 
 
Simplified Occupant Protection
SDM
(500 ma)
Electronic
Front Sensors
(3 max)
(40 ma max)
Airbag
Deployment
Loops
(1.2 A for 2 ms)
Rollover
Sensor
(100 ma)
 Side Impact
Sensors
(6 Max)
(40 ma max)
Manual Cutoff
Switch
SIR Fuse
D/P Seat Belt
Switch
Hall
Sensor
D/P Seat
Position
Sensor
L
`
Suppression
(150 ma)
Battery
IGN1
=  denotes twisted pair
Seatbelt Pretensioner
Deployment Loops
(1.2 A for 2 ms)


### 第 10 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
4 
3 
REQUIREMENTS 
3.1 Ambient Conditions 
The ambient environment for SIR electronic modules is defined below and is applicable to the 
Restraint System wiring and connectors.  All requirements within this document are to be met 
under these ambient conditions throughout the target life defined herein. 
 
Temperature (operating) 
-40 C to +85 C,  
-40 C to +105 C,  for underhood components 
Temperature (storage) 
-40 C to +90 C,  -57 C during air transport 
-40 C to +105 C,  for underhood components 
Relative Humidity 
  0 % to 100  % 
 
Barometric Pressure (operating) 
  47 kPa to 105 kPa 
Barometric Pressure (Storage) 
 12 kPa to 105 kPa 
Ozone 
    0.4 ppm 
Electromagnetic Fields 
    100 V/m electric field strength, 0.1 Mhz to 11 Ghz 
    80  Teslas magnetic field strength, 50/60 Hz. 
 
3.2 Connectors 
The requirements for Restraint System header connectors, mating-half connectors, and in-line 
connectors are defined in the following paragraphs.   
 
3.2.1 General Requirements 
The following are general requirements of connectors: 
a. Connectors, including CPAs1, TPAs, and wiring shall comply with EICC-80.201.01. 
b. CPAs shall be used on all Restraint System Connectors.  For further CPA 
requirements see the Electrical Center’s PASD spec (Power and Signal Distribution), 
EICC 80.201.01. 
c. Electrical contact shall not be made until the female-half of the connection is mated. 
d. Both halves of the connection system must be supplied by the same supplier. 
e. Any connector used in an airbag deployment loop must not be used in non-SIR 
applications.   
f. Yellow connectors used in a seat belt pretensioner deployment loop shall not be used 
in non-restraint system applications.   
g. All airbag deployment loop in-line connections (frontal airbags, side airbags, etc.) 
must be in locations in the vehicle that are accessible to service technicians as agreed 
upon by an appropriate SO (Service Operations) representative.  
h. As a SIR enhancement, in-line connectors used for deployment circuits should not be 
shared with other non-occupant protection circuits.  
                                                           
1 Ideally, a CPA is to be engaged by someone other than the individual who mated the connector, to ensure the 
connection is actually mated. 
批注 [G1]: Page: 2 
 Ozone is specified here because of potential degradation of 
some silicone or rubber sealant 
批注 [G2]: Page: 2 
  This is not a useful spec for the connector alone but is 
contained herein for completeness so the connector supplier is 
aware of the field strengths to which the connector could be 
exposed. 
批注 [G3]: Page: 2 
  This the baseline Connector and Wiring Spec that describes 
other significant requirements.  CM20200 contains additional 
requirements for restraint systems. 
批注 [G4]: Page: 2 
  CPA-capable is the requirement driven by the PASD 
document (EC Power and Signal Distribution).  While 
somewhat redundant here, we don’t want to lose this as a 
requirement. 
批注 [G5]: Page: 2 
  This seems like an obvious requirement but the intent is that 
electrical contact should not be made unless the connector is 
fully engaged (mated). 
批注 [G6]: Page: 2 
  Because airbag deployment loop connectors must be yellow 
GM wants to avoid confusion to service personnel if these 
connectors are used in non-SIR applications. 
批注 [G7]:  Yellow connectors are used to denote airbag 
deployment loop circuits.  They can also be used in seatbelt 
pretensioner circuits.  They are used to convey to service that 
special precautions should be taken when working in the 
vicinity of these circuits. 
批注 [G8]: Page: 2 
  Yellow is not a requirement for pretensioner loops as is 
specified later in this document but if it is chosen by the 
platform it should not be used in other vehicle circuits. 
批注 [G9]: Page: 2 
  Ideally, deployment circuits should have their own connectors 
to avoid service damage to a deployment loop circuit when 
servicing other circuits.  But it is recognized that for wiring 
efficiency some platforms may opt to share a deployment loop 
circuit for a side airbag circuit with a seat belt, seat position 
sensor, or suppression circuit.  Motor circuits using the same 
connector should be avoided if at all possible.  


### 第 11 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
5 
i. In-line connectors shall include a fastening mechanism (rosebuds, clips) that allow 
them to be secured to the vehicle body or other stable member. 
 
3.2.2 Connector Keys  
The requirements for connector keying are: 
a. A component that is calibration sensitive shall be capable of having a unique 
connector key (index) to insure electrical and/or mechanical incompatibility with 
devices of different calibrations.  (This applies to SDM, SIS and EFS connectors and 
may apply to ROS and AOS devices). 
b. A component that is not capable of providing electronic keys (electronic identifiers 
related to calibration) shall use a connector with a unique connector key to provide 
mechanical incompatibility with devices of different calibrations.  The number of 
keys required for each module will be dictated by the module CTS.  The number of 
keys required for an in-line connector will be determined by the appropriate SSLT.  
c. When a key is used, a unique, connector key shall be reserved for development, as 
specified by the platform.  This prototype key shall not be compatible with any 
production key.  
d. In-line connectors shall be keyed if the module is part of an assembly that can easily 
be installed in another vehicle without being visually detected that it is the wrong 
assembly.  For example, a clockspring-coil downlead in-line connector shall be 
keyed if the SIR inflator module uses a high energy squib and is part of the steering 
column assembly which could be installed in another vehicle whose SDM can’t 
provide enough initiation energy. 
e. Additional keying requirements defined in EICC-80.201.01, paragraphs 1.4.2.1.2 and 
1.4.2.2 shall also be met. 
 
3.2.3 Connector Colors 
The requirements for connector colors are: 
a. The SDM connector shall be yellow. 
b. The SIS/EFS and ADS connectors shall be integrated with the SIS, EFS, and ADS 
body and the color shall be that of the SIS, EFS, or ADS respectively. 
c. In-line connectors for airbag deployment loops shall be yellow.   
d. In-line connectors for pretensioner deployment loops may be but are not required to 
be yellow . 
e. The AOS connector shall not be yellow. 
f. The ROS connector shall be yellow. 
g. The ESS mating connector color shall not be yellow. 
1. The SIS-A connector should be yellow if in the supplier’s opinion dropping it 
while powered could result in an airbag deployment. 
 
3.2.4 Target Life 
批注 [G10]:   Front sensors should not use contrasting 
connectors colors that may be visible through the front grill of a 
vehicle leading to less than pleasing aesthetics. 
批注 [G11]:   Yellow has been dedicated to those connectors 
carrying deployment level current.  However, in the case of a 
rollover sensor, because it is possible that if a ROS is 
connected and powered and accidentally dropped a 
deployment could result it seems prudent to extend the yellow 
connector to sensors that can singularly cause a deployment.  
SIS-A devices while totally self-contained are low weight (75 g) 
and with a connected 2-wire harness it would be impossible to 
drop them in such a way to cause a deployment. 


### 第 12 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
6 
Target life is defined as the lesser of 15 years or 150,000 miles.  Operating life with full power 
applied is 8550 hours. 
 
3.2.5 Mate/Unmate Cycles 
The connectors used shall accommodate 10 mate/unmate cycles without: 
a. exceeding 20 milliohms contact resistance  
b. exhibiting damage to the harness 
c. failure of the connector body or parts thereof 
d. degradation of function including that of the shorting clips. 
 
3.2.6 Wire Gauge Capability 
a. Connector cavities shall be sized to accommodate up to 0.50 mm2 (20 gauge) SAE 
J1128 thin-wall type cable plus terminals.  
b. Wire gauge used for all restraint systems shall be 0.5 mm2 (20 gauge) or 0.35 mm2 
(22 gauge).  For SDM or other multi-pin connectors the supplier should assume wire 
density using SAE thin-wall type cable cross-linked polyethylene elastomer (TXL).  
GXL may be used where a slightly larger insulation can be accommodated in the 
connector. 
 
3.2.7 Connection Engage/Disengage Force 
The connector engage/disengage force requirements are: 
a. As a design enhancement, the maximum force to engage a connector should not 
exceed 75 Newtons.  
b. If the engage force in a. can't be met, the connector shall use a mechanical assist 
(e.g., force transmission slider or lever lock) to assist with engaging the connector. 
These features shall have a distinct detent in the fully latched position. 
c. The force required to unlatch the connector latch shall not exceed 25 N. 
d. The minimum disengagement force for  a fully latched connection with or without 
CPA shall be 110  Newtons.    
e. The connector latch-release feature shall be accessible such that the wires leading 
into the connector do not have to be pulled to separate the connector halves. 
f. No tools shall be required to remove CPAs, and unmate the connector halves 
including the latch. 
g. The connector shall meet ergonomic requirements specified in documents located at URL   
http://trucktown.gm.com/prd/iecm_ergo/wall.htm.  The supplier is to complete the 
ergonomic worksheet contained therein. 
 
3.2.8 Interconnect Resistance 
The connector interconnect-resistance requirements are: 
a. The resistance at the crimp-to-crimp interface of a SIR or pretensioner terminal 
system shall not exceed 20 milliohms at an open circuit potential of 20 mv. 


### 第 13 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
7 
b. The inter-pin isolation resistance between adjacent cavities shall be not less than 20 
megohms at 500 VDC. 
c. Requirement a. and b. shall be met during and after a sealed module or in-line 
connector that is to be sealed is submerged in water as specified in paragraph 4.1.  
d. A maximum of 3 connections/connector pairs ( 6 interconnectors) shall be used in a 
frontal airbag deployment circuit. 
e. A maximum of 4 connections/connector pairs (8 interconnectors) shall be used in a 
side airbag deployment circuit or safety belt `pretensioner circuit.    
 
As a design guideline, satellite sensors (SIS, EFS, ROS, suppression) should minimize the 
number of connections/connector pairs and no more than 3 should be used. 
 
3.2.9 Terminals 
a. Modules will be designed such that individual connector pins will carry less than 
1.0-Ampere continuous current. 
b. Connectors using fewer than 8 pins shall use tin plating. 
 
3.2.10 Drop Impacts 
All SIR connectors shall not be mechanically or electrically affected when SIR or belt 
pretensioner modules are dropped on a concrete floor from a height of 1.0 meter. 
 
3.2.11 SDM and Other Connectors 
Additional requirements specifically for SDM, AOS, Rollover Sensors and other connection 
systems, which may consist of one or more, connection systems with a minimum of 8 pins, are: 
 
a. Terminal material shall use CuSn or CuZn and be gold plated. 
b. Terminals (pins) not needed shall be omitted per platform direction which shall not 
violate the sealing capability of the connection system. 
c. It shall be impossible to mate the two halves of the connector at an angle, which 
would result in damaged terminals. 
d. The SDM connection system design shall allow unmating the connection without 
requiring an operator to place fingers beneath the connector.  This allows the 
female connector to be unmated while the SDM is installed in the vehicle. 
e. A common, multi-pin, in-line connector may be used to route a front seat side 
airbag, seat belt pretensioner, seat belt sensor or switch, seat position sensor, and 
suppression sensor.  This common connector shall be yellow. 
f. For dual cavity (dual pocket) SDMs, it may not be possible to unmate the 
connector by placing fingers on both sides of the connector due to proximity to the 
other connector pocket.  See figure 3.2.11-1.  If such a constraint exists, the 
connector design shall allow unmating the connector without requiring removal of 
the SDM from the vehicle. 
 
批注 [G12]:   At this point there is little benefit to sealing in-
line connectors unless the connector is mounted to the surface 
of the floor. 
批注 [G13]: Page: 4 
  With connectors the guideline ‘the fewer the better’ should be 
followed.  Latitude is granted to side airbag and belt 
pretensioner circuits due to some routing methods that may be 
necessary. 
批注 [G15]: Page: 5 
  Usually, connectors with few pins have high contact forces 
and GM’s experience has been that after 10 mate/unmate 
cycles, gold may wear off leading to a poorer connection than if 
tin had been used in the first place. 
批注 [G14]:   8 pins is somewhat arbitrary.  The intent here is 
with higher normal forces commonly used in smaller pin count 
connectors gold plating may be less effective than tin plating.  
With a few connect/disconnect cycles a thin gold plating (50 
microns) could wear off faster with high normal forces resulting 
in a worse connection than if tin were used originally. 
批注 [G16]:   Gold is specified so that the combination of 
multi-pin, sealed connectors will allow ergonomic forces to be 
met.  While this requirement is stated strongly as “shall” wiring 
designers may consider using tin if the connector is not sealed, 
or the connector can be easily engaged using other than the 
thumb and index finger. 


### 第 14 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
8 
 
 
 
 
 
 
 
 
 
 
 
 
*******.3 Figure 3.2.11-1 Dual Cavity Connector 
 
 
g. The SDM connector shall accommodate a left-hand dress, a right-hand dress, or a 
straight-out dress configuration, dependent on platform direction. 
h. The mating connector shall contain a legible part number marking or label as an 
aid in identifying its intended use. 
i. The connection system design shall prevent a mating connector from being 
installed upside down. 
 
3.2.12 Shorting Clips 
The requirements for shorting clips are: 
 
a. Shorting clips shall be used on all airbag deployment-loop in-line connectors.  
b. Shorting clips may be used on pretensioner deployment-loop in-line connectors but 
are not required.  Shorting clips shall be used on the pretensioner pigtail or jumper 
harness connectors.   
c. Shorting clips shall be used on the inflator-side of a connector pair. 
d. Shorting clips used on the SDM harness-side shall be allowed if it enhances 
Corporate commonality. 
e. A gold-plated shorting clip/terminal interface shall be used on SDM connectors. 
f. A gold-plated shorting clip/terminal interface is allowable on in-line connectors 
provided the gold interface meeting the electrical requirements herein is not 
degraded over life, engage/disengage cycles, and environment.  Generally, gold-on-
gold shall be used if the terminal normal force is less than 3 N.  Tin-on-tin shall be 
used if the normal force is greater than 5 N. 
g. The shorting clip shall short across two terminals when the connector is disengaged 
and shall be deactivated only when the two halves of the connection have reached 
their final engaged position. 
h. The shorting clip and terminal interface (including terminal/wire crimp) shall have a 
crimp-to-crimp total resistance such that no more than 150 ma flows through a 1.7 
 
SDM 
As an example: 
 
Due to the overall 
package size and  the 
number of connector 
I/O, there may be no 
room for placing 
fingers within a narrow 
gap, which may only 
be 2-3 mm. 
批注 [G17]: Page: 6 
  Shorting clip use at the body of the pretensioner or pigtail is 
required to minimize in-plant repairs.     
批注 [G18]:   Historically, tin plating has been used on 2-way 
connector shorting clips.  Why?  High normal forces used 
would be detrimental to a thin gold plating.  However, a design 
that uses gold plating with low normal forces would certainly be 
acceptable. 


### 第 15 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
9 
ohm resistor when 6 amps flows to the shunt combination of the shorting clip and 
the 1.7 ohm resistor.  Compliance will be determined by the Test Method for 
Temperature/Humidity Cycling defined in SAE/USCAR-2 Standard for Automotive 
Electrical Connection Systems. 
i. The retention force of the shorting clip in the connector shall be greater than the 
connector disengage force.  The shorting clip shall be designed such that no 
functional degradation occurs after the connection system has been mated and 
unmated 10 times.  
j. Shorting clips shall function as intended under worst-case connector mating 
conditions and insertion orientations. 
k. Shorting clips shall not require assembly assist tools for harness repair in service or 
at the vehicle assembly plant. 
 
3.2.13 Satellite Sensors 
Satellite sensors such as the ADS, SIS, and EFS shall use an integral two-way connector 
specified jointly by GM and the supplier. 
 
3.2.14 Sealing 
The requirements for connector sealing are given in the following paragraphs. 
 
******** 
Modules 
The SDM, SIS, EFS, and ADS shall use sealed connectors.  The connection shall be sealed when 
the mating connector-half is fully engaged.   The AOS connector, seat position, and safety belt 
connectors and Rollover Sensor connectors need not be sealed. 
 
******** 
In-Line Connectors 
In-line connectors shall be sealed under the following conditions: 
 
a. The connector is used underhood. 
b. The connector is located within 50 mm of the floorpan and the module cannot be 
diagnosed as having faulted if water ingress causes an unacceptable connector resistance.  
This resistance level is specified in the governing CTS. 
c. The connector is used for an SDM, SIS, or EFS. 
 
3.2.15 Crush Strength 
All vehicle interior floor-mounted mated connectors shall maintain electrical conductivity as 
specified herein and the connectors shall remain latched in position when a simulated "foot load" 
of 890 Newtons of a vertically distributed force is applied to the connectors through a rigid steel 
plate that is 50 mm by 50 mm for one minute. 
 
3.3 Wiring 
The requirements for Restraint System  wiring are defined in the following paragraphs. 
 


### 第 16 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
10 
3.3.1 Cable Type/Insulation 
The requirements for cable type/insulation are: 
a. SAE J1128 cable or equivalent shall be used. 
b. Copper or tinned copper-core cable shall be used. 
c. Cable insulation shall be either thin-wall cross-linked polyethylene elastomer 
(XLPE) rated for a continuous operating temperature of 135o C or  thin-wall 
polyvinyl chloride (PVC) for a continuous operating  temperature of 85o C.   
d. Crash sensor wiring shall be routed to the SDM so it is not crushed or damaged in 
laboratory crash tests.  Routing must also consider real-world crashes including 
offset impacts and pole impacts. 
 
3.3.2 Splicing 
There shall be no splices in a deployment loop.  Splices are allowed in power and ground circuits.  
 
3.3.3 Grounding 
The SDM should be grounded through a connector pin (primary) and a redundant chassis ground 
(secondary). 
 
3.3.4 Caution Labels 
SIR caution labels at each service disconnect location provide no service benefit, are not required, 
and shall not be used. 
 
3.3.5 Fusing 
The SDM power feed must contain a dedicated fuse with a 10-ampere rating to protect the wires 
within the subsystem.  This fuse shall not be shared with any other non-SIR module or circuit.  
Note:  a fuse of other than 10 amps may be specified by the platform. 
a. The AOS, if used, shall use the fuse dedicated to the SDM. 
b. The rollover sensor, if used, shall use the fuse dedicated to the SDM. 
c. The clockspring coil shall be fused or protected in a manner such that simultaneous 
shorts to power and ground of any non-SIR coil leads do not lead to coil failures and 
unintended deployments.  This requirement is further defined the Steering Column 
CTS available from GM’s Interior Center. 
d. The AOS display and Airbag Readiness Indicator shall not use the SDM dedicated 
fuse and should use the Cluster fuse. 
 
3.3.6 Deployment Loop  
 
******* Crimp Interface 
a. Deployment loop circuits (including SIR subsystem power and ground) shall use a 
crimp on tinned core cable interface.  A reflow soldered crimp interface or crimp 
only on copper wire shall not be used. 
批注 [G19]: Page: 7 
  XLPE is cross-linked polyethylene insulation.  It has a higher 
melting point than other insulation types and is less prone to 
abrasion and cut-through.  XLPE and TXL (Thin-walled Cross 
linked) is referenced in SAE J1128 and produced by a number 
of suppliers including Delphi Packard, Judd Wire 
(www.Juddwire.com), and Prestolite (www.prestolitewire.com).   
批注 [G20]:   PVC is an acceptable insulation type in the U.S. 
but not in Europe. 
批注 [G21]:   10 amp fuses have been used since the earliest 
SIR systems were introduced but the wiring engineer may 
choose other fuse values. 
批注 [G22]:   Early experience with a solder and crimp 
process led to cracking of some terminals even under 
controlled manufacturing conditions.  Crimp only on copper 
wire resulted in high warranty costs in the late 80’s. 


### 第 17 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
11 
b. Deployment loop circuits passing through a clockspring coil that have historically 
and successfully used sonic welding to copper wire need not adopt tinned core cable. 
 
3.3.6.2 Deployment Loop Polarity 
The requirements for maintaining wiring polarity are the following: 
a. Deployment loops using coaxial initiators (squibs) shall maintain correct wiring 
polarity from the initiator to the SDM (central controller).   
b. Wire colors shall be as defined in the frontal air bag module specification 
GMW3109GS-5-1-R-1, the side air bag module specification GMW3115GS-5-2-R-1 
and the safety belt pretensioner specification TBD. 
 
******* Deployment Loop Wiring 
a. Wiring between the SDM and deployment loops shall use twisted pair.   
1. Twisted pair for the clockspring coil downlead is to be determined by the using 
platform. 
b. The number of twists shall be a minimum of 6 twists per foot. 
 
3.3.7 Satellite Sensors 
 
a. Wiring between satellite sensors (“SIS-type” devices and EFS) and the SDM shall use 
twisted pair.   
b. The number of twists shall be a minimum of 6 twists per foot. 
c. Satellite sensors shall not be routed through a BEC (Bussed Electrical Center). 
 
3.3.8 SIR Content Inclusion into Vehicle Wiring/Yellow Tape 
 
a. SIR circuit wiring can be included as part of other vehicle-wiring harnesses.   
b. Applying yellow tape over SIR wires must not be practiced so that vehicle-system-
design and service uniformity can be maintained.   
c. SIR wires shall not be encapsulated in yellow conduit or sheathing for vehicle wiring 
harnesses.   
d. SIR coil uplead and downlead wires shall be encapsulated in black conduit, shrink 
tubing, and or sheathing. This requirement is further defined in the Steering Column 
CTS available from GM’s Interior Center. 
 
3.3.9 Wiring Harness Retention 
Assembled SDM, SIS, EFS, and AOS connectors shall withstand 70 Newtons of longitudinal 
force on the wire harness for 10 minutes on the harness side without damage or exceeding the 
contact resistance of  20 milliohms. 
 
批注 [G23]:   Coaxial connectors have one lead of the squib 
tied to the metal squib enclosure.  If wiring polarity is not 
maintained certain fault conditions can lead to a non-
deployment where one is desired.  
批注 [G24]:   To see the GM Common Circuit Color list got to 
http://ived.GM.com/IVED/ and select the link “GM Common 
Circuit Number/Color/Description Database Search.”  Then 
search the database using the words “SIR” then “PRETEN.” 
批注 [G25]:   Non-twisted pair has resulted in false 
illumination of the airbag lamp due to magnetic coupling from 
PWM and ignition module circuits.  This could lead to customer 
dissatisfaction. 
批注 [G26]:   See Lessons Learned on GM web site.  
Magnetic coupling from motor currents (seat motors, windows) 
can interfere with SIS communications.  Underhood transients 
during crash can interfere with EFS devices. 
批注 [G27]: Page: 9 
  Can’t implement twisted cable through a BEC 


### 第 18 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
12 
4 
VALIDATION 
Validation to the requirements of Section 3 are TBD (except for paragraph 4.1). 
 
4.1 Water Submersion 
See specification CM-20329 for water submersion test conditions. 
 
5 
PROVISIONS FOR SHIPPING 
5.1 Assembly Plant Preparation. 
For SDM connectors that use a mechanical assist to engage the connector, the mating connector 
with attached harness must be shipped to the vehicle assembly plant with the mechanical assist 
fully retracted.  This eliminates assembly plant personnel from having to retract the slide 
mechanism at the plant prior to engaging the connector.  
 
6 
NOTES 
 
6.1 Glossary 
 
Connector.  One half of an electrical connection. 
 
Deployment Loop  A circuit carrying deployment-level current to initiate devices such as 
airbags or safety belt pretensioners. 
 
EC  Electrical Center, formerly called the EICC (Electrical Information and Control Center), 
which is part of the GM Engineering Center headquartered at the GM Technical Center. 
 
Interior Center  A group of the GM Engineering Center that is responsible for airbag 
system and other interior item specifications. 
 
Jumper   Wire(s) with a connector on both ends generally, with one end mating to an 
electrical device and the other end mating to a vehicle wiring harness. 
 
Latch   Sometimes referred to as a lock.  A connector feature that provides a secure 
connection for the mated connector.  This is not the same as a CPA. 
 
Mated  Refers to the condition when the two halves of the connector are fully engaged and 
ideally an audible ‘click’ is heard providing feedback of the engagement. 
 
Module   Components including the AOS, ADS, EFS, SIS, and SDM. 
 
Must/Will   Implies other parts of the vehicles systems are to perform specific functions that 
the connector/wiring supplier can't control. 
 
Occupant Protection  The term refers to all electrical components that can affect occupant 
protection systems including the SDM, seat belts, etc.  It is also referred to as Restraint 


### 第 19 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
13 
systems. 
 
Pigtail    Wire(s) with a connector on one end mating to vehicle wiring harness but whose 
other end is hard-wired to an electrical device. 
 
Platform  A platform is a General Motors entity assigned to designing and developing a 
vehicle. 
 
Retention Force:  The force that is provided to maintain the connection against inadvertent 
unmating or abuse environments. 
 
Shall   Denotes a binding provision that must be met. 
 
Should  Denotes a preference or desired conformance which if not met must be documented 
and disclosed to General Motors. 
 
Target Life  Target life is the number of miles and/or years through which the requirements 
specified herein apply. 
 
6.2 Acronyms, Abbreviations, and Symbols 
 
ABSSLT   Airbag Sub-System Leadership Team 
 
ADS   Auxiliary Discriminating Sensor 
 
AOS   Automatic Occupant Sensor 
 
CPA  Connector Position Assurance.   
 
CTS  Component Technical Specification 
 
CuSn   Bronze, an alloy of Copper and Tin 
 
CuZn  Brass, an alloy of Copper and Zinc 
 
EC   Electrical Center 
 
EFS.  Electronic Front Sensor 
 
ROS.  Rollover Sensor 
 
RSEE  Restraint System Electrical Engineering committee 
 
SDM  Sensing and Diagnostic Module 
 
SIR  Supplemental Inflatable Restraint (airbag) 


### 第 20 页
Occupant Protection Connector and Wiring 
Requirements 
 
GM 20200, Rev. 2.7 
 
CM-20200 Rev. 2.7 Release 
 
12/13/01 
 
14 
 
SIS  Side Impact Sensor 
 
SSLT   Sub-System Leadership Team 
 
TPA  Terminal Position Assurance 
 
 
 
END OF DOCUMENT 

