# GM_MOST50_EN_2012-02_MOST线束技术要求.pdf

## 文档信息
- 标题：
- 作者：hzdw13
- 页数：4

## 文档内容
### 第 1 页
General Motors 
Wiring Requirements for the MOST50 Serial Data Bus 
 
1 
01FEB2012 
1 Scope 
This document defines the preliminary wire harness requirements for implementation of the 
electrical physical layer of the Media Oriented Systems Transport (MOST) serial data bus.  This 
bus is often referred to as MOST50 (or occasionally as eMOST) to differentiate it from the 
“optical” version of MOST that utilizes fiber optic cables. 
 
Note:  When requirements development is complete, the wire harness requirements for the 
MOST50 bus will be documented in GMW3173, at which time that document will replace this 
document in its entirety. 
 
2 Characteristic Impedance 
The MOST50 bus wires shall be selected and designed to provide a characteristic impedance of 
130 Ω at 50 MHz (100 Ω minimum, 140 Ω maximum).  Meeting this requirement may place 
restrictions on the type and thickness of cable insulation that may be used as well as the twist rate.  
Many of the following requirements are designed to ensure proper characteristic impedance.  It is 
desirable that the wiring supplier be able to measure and confirm characteristic impedance 
during the wire harness assembly process. 
 
3 Wire (Cable) Requirements 
 
3.1 Cable Type 
All cable used for the MOST50 bus shall meet the requirements of GMW15626.   
 
3.2 Wire Sizes 
The cross sectional area of MOST50 bus circuits shall be either 0.35 mm² or 0.50 mm² as 
defined in GMW15626. 
 
3.3 Cable Insulation 
Insulation thickness shall be “thin wall” as defined in GMW15626 unless additional abrasion 
resistance is required, in which case “thick wall” may be used.  Use of “ultra-thin wall” cable is 
prohibited.  The cable insulation material shall be selected to meet MOST50 bus characteristic 
impedance requirements.  GM recommends that cross linked polyethylene (XLPE) cable be used 
for all MOST50 circuits. 
 
3.4 Sheath Requirements 
Sheathed, twisted cable may be used for the MOST50 bus, but is not required.  It is acceptable to 
manually twist MOST50 bus cables if characteristic impedance requirements are met. 
 
3.5 Shield Requirements 
MOST50 bus circuits shall not be electrically shielded unless electro-magnetic compatibility 
testing demonstrates that shielding is required to resolve a noise coupling issue. 
 
3.6 Circuit Numbers and Wire Colors 
MOST50 circuit numbers and wire colors shall be as defined in GMW3176 revision 2 if cables 
are manually twisted.  Color deviations may be allowed with pre-approval for sheathed cable. 


### 第 2 页
General Motors 
Wiring Requirements for the MOST50 Serial Data Bus 
 
2 
01FEB2012 
 
4 Bus Construction Requirements 
 
4.1 Twist Length (Twist Rate) 
The (+) and (-) circuits of each MOST50 bus pair shall be twisted together.   The length of each 
360 degree twist shall be 40 mm/twist maximum. Stated alternatively, the MOST50 bus wire 
shall be twisted at a rate of 25 twists/meter minimum. 
 
If specifying a nominal twist length with a tolerance, specify a shorter nominal twist length, such 
that the 40mm/twist maximum is always met (e.g. 35+/-5 mm/twist).  Or alternatively, specify a 
higher nominal twist rate such that the 25/twists/meter minimum is always met (e.g. 30+/-5 
twists/meter). 
 
4.2 Connector Pin Assignments 
Each pair of MOST50 circuits shall occupy adjacent cavities within each connector assembly to 
reduce loop area and minimize the impact on characteristic impedance. 
 
4.3 Untwisted Length at Connector 
The untwisted length of MOST50 bus circuits at each wire harness connector shall be 50mm 
maximum (25mm preferred). 
 
Note:  MOST50 circuits that loop back to a common wire harness connector shall be twisted.  
Looping MOST50 wires at a connector without twisting is not allowed, even if they the wires are 
taped together. 
 
4.4 Splices 
Splices are not allowed in MOST50 bus circuits. 
 
4.5 Bus Topology 
MOST50 bus circuits shall be connected in a unidirectional ring with no stub connections 
allowed.  Within the ring, the “transmit” (Tx) pins of each module must be connected to the 
“receive” (Rx) pins of the next module.  These Tx and Rx connections are not interchangeable. 
 
4.6 Bus Length 
At the current time there is no maximum length requirement for the entire MOST50 bus loop, 
but the maximum bus length between any two nodes (i.e. modules) in 10m.  GM reserves the 
right to later define a maximum overall loop length. 
 
4.7 Inline Connections 
At the current time there is no maximum number of inline connections for the entire MOST50 
bus loop, but the maximum number of inline connections between any two nodes (i.e. modules) 
is eight (8).  GM reserves the right to later define a maximum number of overall inline 
connections for the entire MOST50 loop. 
 


### 第 3 页
General Motors 
Wiring Requirements for the MOST50 Serial Data Bus 
 
3 
01FEB2012 
4.8 Service Inline Connection 
A mated 2-pin inline connector pair shall be provided in the MOST50 bus loop for diagnostic 
and programming purposes.  Typical locations for this connector are in the Instrument Panel near 
the DLC connector or on the right hand side of the trunk.   The “service” inline connector pair 
shall be defined as follows: 
Tx Connector 
Part Number 
******** 
(F) 
The MOST50 circuits in this connector route to the Tx pins of 
a module installed in the vehicle (and will connect to the Rx 
pins of the Service Tool) 
Rx Connector 
Part Number 
******** 
(M) 
The MOST50 circuits in this connector route to the Rx pins of 
a module installed in the vehicle (and will connect to the Tx 
pins of the Service Tool) 
(+) Connector Pin 
1 
This is typically circuit number 3998 
(-) Connector Pin 
2 
This is typically circuit number 3997 
 
4.9 Harness Routing 
MOST50 bus circuits shall not be routed within wire harnesses in “high damageability” areas 
and shall not be routed within wire harness bundles with other sensitive and noisy circuits. 
 
4.10 Bussed Electrical Center Routing 
MOST50 bus circuits shall not be routed through a Bussed Electrical Centers (BECs) unless no 
alternative exists.  If MOST50 circuits are routed through a BEC, it is only acceptable to utilize 
adjacent “though pins” from a connector on one side of the BEC to a connector on the other side 
of the BEC.  No routing of MOST50 circuits on the BEC printed circuit board (pcb) is allowed.  
The MOST50 “through pins” shall not be adjacent to pins for other sensitive and noisy circuits. 
 
5.0 Revision History 
Date 
Change 
26Jan2012 
Initial release for D2UX Wire Harness SOR 
01Feb2012 Added Section 5.0 Revision History 
01Feb2012 Added Appendix A – Example MOST50 Diagram 
01Feb2012 Added note in Section 4.3 prohibiting untwisted “looped” wires at a connector. 
 
 
 
 
 
 
 
 
 
 
 


### 第 4 页
General Motors 
Wiring Requirements for the MOST50 Serial Data Bus 
 
4 
01FEB2012 
Appendix A – Example MOST50 Diagram 
 
 
 

