# EDS_EN_2003-08_福特线束设计指南.pdf

## 文档信息
- 标题：Electrical Wiring Design Guide
- 作者：<PERSON><PERSON>
- 页数：122

## 文档内容
### 第 1 页
� � � � � � � � � � � � � � � � � � � � �
� � � � � � � �
� � � �
� � � � � � � � � � � � � � � � � � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � � � � � � � � � � � �
� �
� � � � � � � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � � �
� � � � � � � �
� � � � � � � � � � � � � �


### 第 2 页
 
 
 
Originator:  <PERSON>/RREINI 
Page 2 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
 
PUBLISHING HISTORY 
 
 
As the Design Guide: 
 
1st edition - November 1988 
 
Latest revision - March 1992 
 
 
As the Engineer's Guide: 
 
1st edition 
January 1993 
 
Revisions 
1993 - 2 (Aug, Nov) 
 
 
 
1994 - 3 (Feb, May, Nov) 
 
 
 
1995 - 2 (May, Nov) 
 
 
 
1996 - 2 (Feb, Aug) 
 
 
 
1997 - 6 (Mar, May, Jun, Jul, Oct, Nov) 
 
 
 
1998 - 4 (Feb, May, Jul, Aug) 
 
 
 
1999 - 3 (Mar, Aug, Dec) 
 
 
 
2000 - 3 (Apr, Aug, Dec) 
 
 
 
2001 – 6 (Mar, Mar[2], Jun, Jul, Aug, Oct) 
 
 
 
2002 – 4 (Feb, Apr, Aug, Sept) 
 
 
 
2003 – 4 (Mar, Apr, May, Aug) 
 


### 第 3 页
 
 
 
Originator:  <PERSON>/RREINI 
Page 3 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
ACKNOWLEDGEMENTS 
 
I want to thank everyone who has ever been part of the Ford wiring community, whether in the wiring 
departments, the connector section, at Body & Assembly/Vehicle Operations, or in the supply base.  You taught 
me how to design wiring systems for automobiles, and you suggested important items for inclusion in this guide. 
 Without your instruction, I could never have authored this guide; without your suggestions, it would not be 
nearly as useful as it is.  There are far too many of you to name here, but you know who you are. 
 
Roger Reini 
August 2003 
 
 
WHAT'S NEW IN THIS EDITION 
 
These are the major changes since the last publication of the guide. 
 
 
GATHER TOGETHER ALL NECESSARY INFORMATION 
• Updated information about the Electrical Distribution Library 
 
DESIGN AND ENGINEER THE WIRING SUBSYSTEM 
• Updated reference to new ES-4C34-14401-AA spec similar to USCAR 23 but using SAE wire sizes 
• Added caution against overzealous use of thermoplastic insulation in the engine compartment 
• Added information about how far away from the bundle eyelet takeouts must extend 
• Noted that other technologies that can assure an eyelet crimp seal are permissible 
• Added guidance on avoiding wire chafes from the cut ends of convolute 
• Revised wire sizing guidelines to add exception for circuits protected by slow-blow fuses that blow 
within 1 second on a direct short 
 
SERVICE RECOMMENDATIONS FOR WIRING REPAIR 
• Added statement that these repair procedures must be used with vehicle personalization work done by 
dealers 
• Added questions and answers about gold and tin terminals 
 
 
Most changes in this edition are denoted in italics. 
 
 


### 第 4 页
 
 
 
Originator:  Roger Reini/RREINI 
Page 4 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
 
PREFACE  
 
 
This is the latest edition of the Engineer's Handbook and Guide for the Electrical Distribution System, also 
known as the Wiring Design Guide.  As always, it describes the total wiring design process and provides tips on 
wiring design that are not included in the SDS.  The SDS tells you how the wiring system must perform; this 
guide gives you pointers on how to design the system so that it will perform to expectations.  It captures the 
experiences and lessons learned over the course of many years by the wiring community at Ford and its suppliers. 
Taken together, the SDS and this guide serve as tools to educate the new, inexperienced wiring engineer yet 
function as references for experienced engineers. 
 
The scope of this document is the entire electrical distribution system, including component pigtails or flyleads.  
It does not cover ignition wires (spark plug cables).  Note that this guide was written for 12-volt electrical 
systems.  Much of the advice here will likely be applicable to 42-volt systems, and some of it may be applicable 
to high-voltage systems (i.e., over 60 volts, based on OSHA or UL definitions). 
 
This guide is available on the Ford Intranet.  The official location is the EDS Core Subsystems eRoom, where it 
can be accessed by eRoom members, including supplier members via Covisint/Ford Supplier Network.  The 
document is mirrored at <http://www.eds.ford.com/>, the home page for the Electrical Distribution 
Clearinghouse (alternate URL: <http://www.eese.ford.com/eds/>.  At this location, it can be viewed by anyone 
in the Company with a World Wide Web browser.  It cannot be reached via the public Internet, but suppliers can 
access it via the Ford network while on Ford property, as well as via the eRoom when they're off-site.  
Alternatively, they can request a copy of the guide in electronic format, as well as a copy of the Clearinghouse on 
CD-ROM. 
 
For purposes of ISO9000 compliance, the eRoom version of the guide is the official copy.  All printed copies are 
unofficial and uncontrolled. 
 
Requests for additional copies of this guide, as well as comments and suggestions for revision, may be directed 
to: 
 
 
 
Roger Reini (RREINI) 
 
 
MD5014, Bldg. 5 
 
 
Ph. (313) 39-04358 
 
 
Fax (313) 32-25964 or 39-04358 
 
 
E-mail address:  <EMAIL> 
 
 
Homepage:  http://www.eese.ford.com/~rreini/ (internal to Ford only) 
 
 
ATO®, MINI®, MAXI™, JCASE™ and MEGA® are trademarks of Littelfuse, Inc. for their fuse products. 


### 第 5 页
 
 
 
Originator:  Roger Reini/RREINI 
Page 5 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
 
TABLE OF CONTENTS 
 
 
This guide is composed of several sections, each of which can be updated independently of the others.  In order 
to be certain that you have the latest version of the guide, you should have these sections with these dates: 
 
THE CONTINUOUS DESIGN PROCESS  
 
 
August 2000 
 
GATHER TOGETHER ALL NECESSARY INFORMATION 
August 2003 
 
DESIGN & ENGINEER THE WIRING SYSTEM  
 
August 2003 
 
(including List of Pitfalls to Avoid, 
              Fuse links, Service Considerations, etc.) 
 
SATISFY SYSTEM REQUIREMENTS/TIMING 
 
 
October 2001 
 
VERIFY THAT THE PARTS WORK  
 
 
 
June 2001 
 
FEEDBACK ON YOUR DESIGNS  
 
 
 
June 2001 
 
APPENDICES: 
 
 
GLOSSARY  
 
 
 
 
 
June 2001 
 
 
HOW TO SUCCESSFULLY LAUNCH . . .  
 
March 1997 
 


### 第 6 页
 
 
 
Originator:  Roger Reini/RREINI 
Page 6 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
THIS PAGE DELIBERATELY LEFT BLANK 


### 第 7 页
 
 
 
Originator:  Roger Reini/RREINI 
Page 7 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 8 页
Originator:  Roger Reini/RREINI 
Page 8 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
THIS PAGE DELIBERATELY LEFT BLANK 


### 第 9 页
 
EESE - EDS                                                THE CONTINUOUS DESIGN PROCESS                                  August 2000 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 9 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
Credits:  Unknown 
 
 
PURPOSES OF THIS GUIDE  
 
 
� 
To provide a systematic approach to designing wiring, with greater attention to the use of formal engineering 
disciplines and design verification 
 
� 
To supplement the System Design Specification (SDS) for the Electrical Distribution System (EDS) 
 
� 
To educate new engineers at Ford and its full-service suppliers in wiring design, and to preserve practical knowledge 
that the Ford EESE-EDS organization has learned through experience despite personnel changes. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Designing a wiring system doesn't have to be this bad! 


### 第 10 页
 
EESE - EDS                                                THE CONTINUOUS DESIGN PROCESS                                  August 2000 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 10 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
 
 
THE CONTINUOUS DESIGN PROCESS  
 
AUTOMOTIVE WIRING  
 
 
The process of designing wiring can best be described as continuous because it never really ends.  Harness design is 
evolutionary, adapting to ever-changing conditions, with room for continuous improvement.  The process is applicable to 
all stages of harness design, from the first iteration on a brand new vehicle to an emergency change at the assembly plant. 
 
The process can be summarized in five major steps: 
 
 
� 
GATHER TOGETHER ALL NECESSARY INFORMATION 
 
• 
Product Assumptions (including option rates) 
• 
Product Letters 
• 
Cost, weight targets/objectives set by PMT and Program Management 
• 
Subsystem schematics 
• 
Design Transmittals 
• 
Design of Sheet Metal 
• 
PEO Engineers, Designers 
• 
Requirements of other PMTs 
• 
Packaging of Major Components (drawings or on bucks, physical or electronic) 
• 
System Constraints (design freezes, change deadline has passed, etc.) 
• 
QFD Studies -- Market Requirements, Wiring Characteristics 
• 
Generic FMEA 
• 
Service, Warranty, TGW, TGR Issues 
• 
Quality Improvement Objectives (for continuous improvement) 
• 
Electrical Details of Components (regular load, max load, abnormal stall load, etc.) 
• 
Feedback from previous designs, including Concerns (VO) 
 
� 
DESIGN AND ENGINEER THE WIRING SUB-SYSTEM 
 
• 
Partition the wiring system for vehicle assembly needs (primary), service, architecture, salvageability, etc. 
• 
Develop the parts list, parts usage 
• 
Refer to SDS, other design guidelines, List of Pitfalls to Avoid, Lessons Learned 
• 
Provide Sufficient Protection, Retention 
• 
Keep Priorities in Mind: 
o Durability 
o Assembly Feasibility/Reliability 
o Manufacturing Feasibility/Reliability 
o Component Quality 
o Cost 
o Serviceability (Diagnostics & Accessibility) 
o Simple Concepts - Minimize Complexity 
o Engineered Appearance 
o Consistent Supply Capacity 
• 
Rough Circuit Analysis - power feeds 
• 
Develop DVP&R (prototype included with guide, on computer) 
• 
Use QFD, DFA, FMA, FMEA disciplines as needed to design the wiring 
 
� 
SATISFY SYSTEM REQUIREMENTS/TIMING 


### 第 11 页
 
EESE - EDS                                                THE CONTINUOUS DESIGN PROCESS                                  August 2000 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 11 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
• 
Initial Release - satisfy EPR/WERS dates 
• 
Alert (PPM, Deviation) action 
• 
Process notices 
• 
Ok-To-Tool drawings 
• 
Prototype signoffs 
• 
Durability Reviews 
• 
Reliability Reviews 
• 
Prototype and Pre-Launch build support 
• 
Campaign Prevention Reviews 
• 
8D Write-ups 
• 
Key Program Dates 
• 
Ford Production Development System (FPDS) events 
• 
Total Program Work Plan (TPWP) 
 
� 
VERIFY THAT THE PARTS WORK 
 
• 
Drawing Review 
• 
Compatibility Reviews 
• 
Design Aid bucks, Digital Buck, package drawings 
• 
Prototype Vehicles 
• 
Production Validation testing 
• 
Key Life Tests (where applicable) 
• 
Functional Build Vehicles 
• 
FEU, Constrained/Integrated Build (4P) Vehicles 
• 
Production Vehicles (in-plant trials for running changes) 
 
� 
FEEDBACK ON YOUR DESIGNS (returns to step 1) 
 
• 
Concerns 
• 
Test Results 
• 
Issues from Design Aid meetings and compatibility reviews 
• 
Plant quality indicators (NOVA-C Audits, first run, etc.) 
• 
 Senior Management Concerns 
• 
Customer satisfaction indicators (TGW, TGR, Warranty Issues) 
• 
Comparative Vehicle Studies (teardowns, benchmarking, etc.) 
 
 
Cycle repeats until the design is "right" -- ready for production with no concerns.  If major program assumptions change, 
however, the process starts over at the very first cycle. 


### 第 12 页
 
EESE - EDS                                                THE CONTINUOUS DESIGN PROCESS                                  August 2000 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 12 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 


### 第 13 页
 
 
Originator:  Roger Reini/RREINI 
Page 13 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 14 页
 
 
Originator:  Roger Reini/RREINI 
Page 14 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 15 页
EESE-EDS                                GATHER TOGETHER ALL NECESSARY INFORMATION                             August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 15 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
 
 
GATHER TOGETHER ALL NECESSARY INFORMATION  
 
 
Before an electrical distribution system (or any part thereof) can be designed, you need to know several pieces of information. 
 Some of this information is so vital that without it, you cannot design the system at all.  Other information, while not 
essential, is important for a properly functioning, effective, and economical system. 
 
 
Information You Absolutely Must Have  
 
 
Here are five key questions for which you must have answers: 
 
• 
What components do you connect to (standard and/or optional)? 
• 
How do you plug into them, both electrically and mechanically? 
• 
What are the characteristics of the signals/loads? 
• 
Where are the components packaged in the vehicle?  How are they oriented? 
• 
What accommodations does the sheet metal provide for packaging the wiring? 
 
Where can you find the answers to these questions? 
 
• 
Program Assumptions/Product Direction Letters 
• 
Design Transmittals and System Design Specifications (SDS's) 
• 
Subsystem Schematics 
• 
Package Drawings and Layouts; Design Aid Bucks, including digital bucks 
• 
Electrical Breadboards 
• 
Engineers, designers, etc., for other systems and components 
 
Program Assumptions/Product Direction Letters:  These describe what will be available on the vehicle, whether it will be 
standard, RPO (regular production option), LPO (limited production option) or DSO (dealer special order), and authorize 
expenditures on particular programs.  Related information includes the option rates, projected volumes, and the hourly build 
rates at the assembly plant (use estimates, if necessary). 
 
Design Transmittals and SDS's:  Product direction letters do not go into detail about each component.  Those details can be 
found in system design specifications (SDS's) and design transmittals.  They provide in-depth information about subsystem 
requirements -- the inputs and outputs, their electrical characteristics such as steady-state and peak/stall currents, and other 
useful information.  SDS requirements involve interactions and interfaces between systems.  Departments send out 
transmittals describing their parts to other departments who need to know how the parts work (what signals do they put out, 
how do you interface to it, what connector does it take, etc.) in order to design their parts.  SDS's and transmittals by 
themselves cannot add significant cost to the system; only product letters can authorize such costs. 
 
Generic SDS's for all commodities, including electrical distribution, can be found online on the Ford intranet at the 
Requirements.ford.com site (http://www.requirements.ford.com/).  Note that the EDS SDS, like all SDS's, contains not 
only requirements written by the EDS organization but also the appropriate requirements imposed upon the EDS by other 
systems. Each vehicle team will adapt the SDS to its needs. 
 
Subsystem Schematics:  One of the items regularly sent by transmittal is a subsystem schematic for a major control system, 
such as the powertrain.  These provide information on what sensors and actuators are required, how to connect them to the 
control module, whether any special shielding is required, and any other requirements.  Nowadays, they are likely to be 
provided as CAE models.  NOTE:  Schematics have been known to be in conflict with applicable SDS's, EMC guidelines or 
other such documents.  When in conflict, notify the schematic owner of the conflict so that it can be resolved. 
 


### 第 16 页
EESE-EDS                                GATHER TOGETHER ALL NECESSARY INFORMATION                             August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 16 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Package Drawings, Design Aid Bucks, Digital Bucks:  These provide information on where the components are packaged 
and where the wiring must go.  They are also used to resolve interference issues.  Bucks (mockups of various parts of the 
vehicle -- engine compartment, instrument panel, door, body, etc.) are easier to use than package drawings because the parts 
are actually there, making it easier to visualize routing and to see interferences.  Also, having the actual sheet metal makes it 
easier to determine the need for retention and location of the wiring.  Digital bucks using CAD models of the parts are now 
becoming available.  Whatever you use, make sure that everything is updated to reflect the latest wire routing. 
 
Electrical Breadboards:  These are laboratory or "bench" mockups of the entire electrical system of a vehicle (powertrain 
generally excepted).  They are not installed in a buck or in vehicle position, but they function just like they would in an actual 
vehicle.  They are used for various testing purposes, not only by EDS but also by other electrical systems.  Typically, 
breadboards can be found at both EESE and at the wiring FSS. 
 
Other Engineers & Designers:  Talk to the responsible engineers and designers (both Ford and supplier), including your 
PMT and co-located team members.   Make them a part of your team.  Keep the flow of information a two-way street. 
 
 
OTHER IMPORTANT INFORMATION YOU SHOULD HAVE  
 
Here are some other important questions to answer: 
 
1.  What cost and weight objectives are there?  These are set by the PMT (see the Timing section for definition), of which 
you are a key member. Ford and supplier engineers are involved in setting targets and objectives as part of the PMT.  Cost 
and weight data are available to Ford engineers (but not suppliers) via the WERS PCW system. 
 
2.  What do indicators such as warranty or Things-Gone-Wrong say?  What are the Things Gone Right?  These help in 
determining the upgrades that must be made. 
 
3.  What quality improvement objectives are there?  There is usually a quality road map that lists the desired quality levels 
and the actions needed to get there.  It covers three to five years in the future. 
 
4.  Are there any system constraints that could delay or prevent design revisions from being implemented?  These restraints 
include rejection of changes due to high cost, design freezes, and deadlines for implementing changes in the current year. 
 
5.  What about feedback from previous design levels?  See the section FEEDBACK ON YOUR DESIGNS 
 
6.  Are there any concerns written against your parts?  This is a prime motivator to the design process.  Responses to the 
concerns must be made in a timely manner, which means that any investigation must be conducted rapidly.  Have these 
concerns been addressed in your new design? 
 
7.  What do QFD and FMA/FMEA studies indicate?  QFD (Quality Function Deployment) and FMA/FMEA (Failure 
Mode Analysis/Failure Mode and Effect Analysis) studies can provide valuable information -- what design factors are most 
important, where should extra effort be spent, etc.  Discussion of these disciplines is beyond the scope of this guide; classes in 
QFD are offered regularly, and FMEA classes can be arranged.  There are booklets available on these processes, and special 
software is available for completing QFD Houses of Quality and FMEA forms (QFDPlus and FMEAPlus). 
 
8.  What do comparisons against comparator vehicles show?  Used mainly on new vehicles or major programs, 
comparators are the vehicles that Program Management sees as the main competition for our vehicle. Comparisons between 
the vehicles are made on the basis of cost, weight, and image.  There is a benchmarking activity that carries out several studies 
a year.  In addition, the wiring FSS's conduct benchmarking studies of their own. 
 
9.  What does the campaign history show?  This list of campaign actions (recalls, owner notifications, etc.) tells you what 
has caused problems in the past and where you should put special emphasis during the design process.  It will be very useful 
for the campaign prevention review (covered elsewhere in the guide). It is available on the Ford web; the history for EDS can 
be found at the EDS Clearinghouse. 
 


### 第 17 页
EESE-EDS                                GATHER TOGETHER ALL NECESSARY INFORMATION                             August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 17 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
WHAT IF YOU CAN'T GET THE INFORMATION? 
 
You may be unable to obtain all the information you require.  This can jeopardize support for prototype and other pre-launch 
builds.  To reduce this risk, you or your PMT should consider publishing, on a regular basis, a list of those systems and 
components for which you lack information.  This list should also include the responsible engineers, which has led some to 
call it a "fink list" (i.e., if you're on the list, you're a fink).  This list should be published regularly (say, once a week) well 
before the freeze dates for the build.  Copies should go to the affected engineers as well as Program Management.  If freeze 
dates pass and you still lack information, DO NOT HESITATE to issue a program alert informing management that wiring 
availability is delayed and slipping because of incomplete information.  This technique has proven very effective in obtaining 
design direction; it was first used by Alcoa Fujikura, Ltd. (AFL) on the 1993 FN10 program. 
 
 
 
REFERENCE SECTION  
 
These are documents you should have or know where to find: 
 
Connector Systems Catalog:  Compilation of the connectors and terminals in the Ford library.  The most recent version of 
the catalog as of April 2000 was published in June 1999 with updates in February 2000.  It is available on the Web and in 
hardcopy format 
 
Electrical Distribution Library: 
This is located in the offices of Electrical Distribution Subsystems and Components Engineering (currently located in building 
5, room 2C017).  It has grown smaller in size over the years, as many of the documents that used to be there can now be 
found on the Web.  Some of the items still found there include the following: 
 
Worldwide Customer Requirements (WCR): 
This is a historical copy of a compilation of official standards for components used in vehicles.  The official version, as well 
as other historical copies, is located on the Web.  The requirements have, for the most part, been rolled into the appropriate 
SDS's. 
 
Wiring Diagrams (formerly called Electrical-Vacuum Troubleshooting Manuals, or EVTM's) for the past several years 
 
These items are no longer found there but are available elsewhere: 
 
List of Part Numbers, Names:  This tells you how to designate your parts (VERY IMPORTANT).  This can be generated 
from the MPNR Web site. 
 
Acronym Lists: 
These are lists of commonly used acronyms; essential if you wish to understand what's being discussed.  
These can be found on the Ford Web Hub under "Ford Speak." 
 
Full Service Supplier (FSS) Relationship Guide: 
This is a document that specifies and defines the relationship between Ford and its full service suppliers.  It spells out what is 
expected of the EDS department(s), EESE, Vehicle Operations, and the supplier(s).   
 
ISO Area: 
Items important in complying with ISO9000 requirements.  Note that the EESE Web site has its own ISO 
compliance area (see below).  Many departments have ISO areas in their eRooms. 
 
Websites (Ford internal unless otherwise stated) 
 
Electrical Distribution Clearinghouse (http://www.eds.ford.com/) 
Connectors and Terminals Website(s) (http://www.connectors.ford.com/) 
 
These are internal Ford Websites of special interest to the wiring community, covering wiring, connectors and terminals.  
They are accessible by all Ford engineers and by supplier engineers while on Ford property.  The EDS Clearinghouse is 


### 第 18 页
EESE-EDS                                GATHER TOGETHER ALL NECESSARY INFORMATION                             August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 18 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
available on CD-ROM to suppliers wishing to access it from their offices, and most sections of the Connectors Website can 
be provided on CD-ROM.   
 
EESE Website (http://www.eese.ford.com/ -- ISO page, http://www.eese.ford.com/iso/) 
 
This is the Website for the Electrical and Electronic Systems Engineering (EESE) organization.  Of special interest is the ISO 
page, which contains pointers to information and lists necessary for maintaining compliance to ISO 9000.  Note that these 
pages are generally available to Ford engineers but may not be available to supplier engineers. 
 
my.ford.com (http://my.ford.com/) – the main homepage within Ford. 
 
eRooms 
 
These are virtual work areas that are extensively used within Ford and EESE.  Suppliers can request their own dedicated 
eRooms, and they can also request access to some EESE eRooms.  For the wiring community, the EDS Core Subsystems 
eRoom (https://f1.ford.com/eRoom/EESE/EDSCoreSubsystems) will be useful. 
 


### 第 19 页
 
Originator:  Roger Reini/RREINI 
Page 19 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 20 页
 
Originator:  Roger Reini/RREINI 
Page 20 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 21 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 21 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 DEVELOP PARTS LIST, USAGE ..................................................24 
DETERMINE LOAD REQUIREMENTS.................................................24 
DETERMINE AND DEFINE THE WIRES..............................................25 
Assign Circuit Numbers and Color Codes................................25 
Determine the proper size of the wires................................25 
Pick the proper insulation for the wires..............................26 
Determine the Preliminary Length, Routing.............................28 
Resistance Wire Considerations........................................28 
FOLLOW STANDARD SCHEMATICS AND PINOUTS......................................29 
CHOOSE TERMINALS AND CONNECTORS.............................................30 
PROVIDE CIRCUIT PROTECTION..................................................37 
FUSES.................................................................37 
CIRCUIT BREAKERS......................................................40 
HIGH CURRENT FUSES....................................................40 
DETERMINE THE ROUTING, OPTIMUM PARTITIONING OF CONNECTORS...................42 
PIA (Purchase In Assembly) Wiring.....................................42 
Optimum Partitioning of Connectors....................................42 
Routing of harnesses..................................................42 
BEST-IN-CLASS WIRING GUIDELINES.......................................47 
MAKE SURE THE CONNECTORS ARE "CONNECTION-CAPABLE.\..........................49 
 
HOW TO REDUCE CONNECTION RISK.........................................49 
DETERMINE, POSITION RETAINING DEVICES.......................................50 
Types of Retainers....................................................50 
Deciding Where to Put Retention.......................................50 
Other Guidelines......................................................51 
SPLICING....................................................................53 
Determining the best location.........................................53 
Protecting the splice.................................................53 
Other guidelines......................................................54 
JUNCTION BLOCKS.......................................................54 
GROUNDING...................................................................55 
Standards for grounding...............................................55 
Grounding to sheet metal..............................................55 


### 第 22 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 22 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
PROTECTING THE WIRES........................................................57 
Electrical Protection.................................................57 
Mechanical/Environmental Protection...................................58 
Rules of thumb........................................................60 
PREVENT SQUEAKS AND RATTLES.................................................62 
PREVENT WATER LEAKS.........................................................62 
MISCELLANEOUS GUIDELINES....................................................66 
Manufacturing Practices...............................................66 
Electrical Components.................................................66 
Fuse Panels...........................................................67 
ENSURE CONNECTIONS ARE ACCESSIBLE FOR ASSEMBLY, SERVICE.....................67 
PERFORM A CIRCUIT ANALYSIS, ESPECIALLY ON POWER/GROUND CIRCUITS.............67 
DETERMINE SIGNIFICANT CHARACTERISTICS.......................................68 
DEVELOP A DVP&R.............................................................68 
ENSURE THAT DESIGNS COMPLY WITH FEDERAL REGULATIONS/CORPORATE REQUIREMENTS..68 
 


### 第 23 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 23 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
 
 
 
DESIGN AND ENGINEER THE WIRING SUB-SYSTEM  
 
Now that you have begun to assemble the necessary information, you move on to the task of actually designing the wiring 
system.  There are many engineering requirements to consider throughout the design phase: 
 
 
- 
Priorities to Keep in Mind: 
 
 
Quality/Customer Wants (All customers, not just the ultimate buyer) 
 
Durability 
 
Assembly Feasibility/Reliability 
 
Manufacturing Feasibility/Reliability 
 
Component Quality 
  
Cost 
 
Serviceability (Diagnostics & Accessibility) 
 
Salvageability 
 
Simple Concepts - Minimize Complexity  
 
Engineered Appearance 
 
Consistent Supply Capacity 
 
- 
Strive to attain Best-In-Class wiring by following the B-I-C Guidelines (included in this guide) 
- 
Always keep in mind the List of Pitfalls to Avoid (attached at the end of this section), a list of items based on 
experience which is intended to prevent customer concerns from recurring and to avoid electrical campaigns (i.e., 
recalls).  Look over this list as a final step; make sure that your wiring takes everything into account. 
- 
Consider ergonomics; think in terms as if you had to install your harness for 8-10 hours a day. 
 
Engineers are encouraged to contact the Core EDS section for consultation on issues related to wire harness design. The 
engineers, supervisors and technical specialists are generally very experienced wiring engineers and can provide valuable 
insight into the various issues of wiring design and packaging. 
 


### 第 24 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 24 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
1. 
DEVELOP PARTS LIST, USAGE 
 
Before you can design any parts, you have to determine what parts you will design.  You'll generally know which major 
harnesses you need (14290, 14401, 14405, etc.), but you'll have to determine how many different versions of each harness in 
order to account for the build variations and option content. The program direction letters may give you some guidance, but 
the ultimate decision belongs to you and the Electrical PMT. 
 
Things to keep in mind 
 
Can any parts carryover from prior model years or other vehicle lines? This is efficient because the parts have already been 
proven out; additional major engineering effort is not required.  Also, engine harnesses can sometimes be used on several 
vehicle lines using the same engine. Cross-platform usage is extremely uncommon due to the differences between platforms 
 
Try to strike a balance between reducing complexity and reducing the control model cost of the wiring system. 
 
REDUCE BUILDABLE COMBINATIONS.  It is imperative that you control the number of buildable combinations in the 
wiring system.  As the number of harnesses (i.e., complexity) increases, the number of potential buildable combinations 
increases.  This has a tremendous impact on the assembly plant and on component manufacturing plants handling wiring; they 
must dedicate valuable line space for these parts. In-line Vehicle Sequencing (ILVS) may reduce the need for line space, but it 
does not eliminate it.  The vast majority of these combinations will never actually be built. This number must be controlled by 
optimizing the option combinations and selectively giving away circuits.  EESE has a modeling program that can determine 
the optimal buildable combinations for a given set of options. 
 
Advantages: 
Reduced inventory of different end items 
Simplifies operator's job -- has fewer harnesses to keep track of 
Simplifies engineer's job -- same reason 
Parts take up less line space at assembly plant 
Economies of scale come into play 
Reduces obsolescence 
 
Disadvantages: 
Total control model cost increases 
Giving away wires, connectors -- corrosion can attack unused connectors, causing shorts (risk can be minimized with proper 
routing and locating and with proper protection of the unused connectors) 
Giving away some option content 
Possible squeaks, rattles from unused connectors (risk can be minimized with proper routing and locating) 
 
You will need cooperation from Program Management, because the average cost and/or control model cost of the system will 
increase.  Marketing may also need to be involved, as the option groupings can affect complexity.  However, there will be an 
overall benefit to the program. 
 
Be careful when grouping harness combinations together.  Unused connections might act as antennas, bring noise into the 
EEC/PCM or other modules.  Consult with the module owners about any potential EMC issues. 
 
Power feeds over 20A should not be given away on harnesses to reduce complexity.  If this cannot be avoided, however, 
the unused feed must be securely retained in an area known and confirmed to be dry. 
 
2. 
DETERMINE LOAD REQUIREMENTS 
 
This is part of the necessary information you should have gathered (or attempted to gather) earlier.  Load requirements for a 
particular component, such as steady-state and peak/stall, should be given in a logical or physical schematic, a design 
transmittal or an engineering specification (ES or SDS) for that component.  This information is crucial in determining wire 
gage and fuse size. 


### 第 25 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 25 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
3. 
DETERMINE AND DEFINE THE WIRES 
 
Assign Circuit Numbers and Color Codes 
 
With the schematics and other information in your hands, you know where the wires must go electrically.  You now have to 
identify the wires in a clean and consistent manner.  Per the Wiring SDS, circuits are identified and coded per specification 
ES-F0TB-1274-AA (i.e., the old North American system, for 2005 and earlier programs) or specification ES-5C3T-1274-AA 
(the new PSF spec, for 2006 and beyond programs).  Copies of the F0TB specification are available from DocMan, the EDGS 
librarians, and the EESE CAE eRoom (eRoom subject to change), while reference copies are available at the EDS 
Clearinghouse Web site, http://www.eds.ford.com/ [Ford internal only].  The 5C3T spec should also be available at the 
EESE CAE eRoom.  The EDS Core Subsystems eRoom links to these documents in their current location; in the future, they 
may reside in the eRoom. 
 
Determine the function of each wire.  Is it regular ground, a special ground, B+ at all times, key-on power only, etc.? Relate 
that function to an official function in the spec. 
Use official function to determine circuit number, color coding.  Review the spec to see if there is a circuit description 
matching or nearly matching your intended function for the circuit.  If there isn't one, consult with the engineer who assigns 
new circuit numbers (as of August 2002, for the F0TB spec, this is now Roger Reini; for the 5C3T spec, it is TBD). 
Don't have 2 or more circuits with the same number or color code in the same connector unless they can be interchanged 
(meaning they can handle the same voltage and current requirements). 
Don't use the same circuit number on both sides of a fuse, switch, or load. 
 
Circuit Classification 
 
Circuits can be classified based on their voltage level and on their (actually, their connected components') sensitivity to 
changes in resistance or impedance.  The Connector group has proposed a classification method based on one used at General 
Motors.  Power (P) circuits have open circuit voltages greater than 5 volts.  Low energy (LE) circuits have open circuit 
voltages less than or equal to 5 volts.  Low impedance or resistance (LI) circuits can have any open circuit voltage but are 
very sensitive to changes in impedance or resistance.  These parameters (open circuit voltage and resistance/impedance) are 
critical in selecting terminal and crimp parameters.  It is anticipated that all circuits in the various circuit identification 
specifications, both present and future, will eventually be rated and classified using this system. 
 
Circuits can also be classified based on the functional importance specification defined in ARL 09-0445 and ES-1W7T-
F407K00-AA (the class A, B or C circuits). 
 
Determine the proper size of the wires 
 
In general, the greater the current, the smaller the wire size, or gage, number must be (the larger the wire).  This is a list of 
resistances for common wire sizes: (derived from ASTM B 3 and ESF-M2L1-A specs) 
 
 
 
 
 
 
 
 
Bare Stranding 
 
 
 
 
 
 
 
Max. Resistance 
 
 
 
 
 
 
 
Milliohms per meter 
 
Wire Size 
 
Stranding 
 
20  °C        
 
 
22 
 
 
7/30  
 
50.6 
 
20 
 
 
7/28  
 
31.7 
 
18 
 
 
16/30  
 
22.1 
 
16 
 
 
19/29  
 
14.6 
 
14 
 
 
19/27  
 
9.19 
 
12 
 
 
19/25  
 
5.83 
 
10 
 
 
19/23  
 
3.64 
 
 8 
 
 
19/21  
 
2.30 
 
 6 
 
 
N/A 
 
 
1.18 
 


### 第 26 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 26 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
Chart based on R = 17.241/mm2 milliohms per meter 
 
Coefficient of Resistivity (copper):  .00383 /°C at 20 °C 
 
Some wire specifications may have their own resistance values.  Those values take precedence over these for any calculations. 
 
The use of 20 and 22 gage wire is encouraged in order to realize maximum bundle size and weight benefits.  At this time, 22 
gage wire is approved for use in unsealed connectors and selected sealed connectors.  Whether a particular connector has 
been approved for use with 22 gage wire is governed by the EDGS library and/or the EESE Component Database.  In general, 
do not use 22 gage wire in mat-seal connectors or in one-way connectors (connectors with only one circuit, whether sealed or 
not).   
 
Ground circuits that handle multiple loads need to be sized appropriately for the maximum combined loads of those systems.  
It may happen that the combined loads, especially if they are not on the same fuse, may send excessive current through the 
common ground, causing it to exceed its rated temperature.  See the notes on fuse and wire sizing in the section Provide 
Circuit Protection. 
 
These are some additional considerations when sizing wires: 
 
Physical Stress -- Can the wire stand rough handling during manufacturing, packing, and installation?  Will it be subject to 
flexing in service? Will it withstand connector unplugging for service?  If it's an eyelet connection, especially if the eyelet 
does not have anti-rotation features, is the wire small in diameter? 
 
Voltage Drops -- Does the wire feed a component which cannot tolerate a large voltage drop?  This is especially important 
during engine cranking.  Wire sizes larger than those dictated by current handling capacity may be required -- test to verify. 
 
Extreme Ambient Temperatures -- Remember, wire insulation temperature is dependent not only on current flow but also 
on ambient temperature.  A wire that is adequately sized for normal conditions may be inadequate under extreme conditions -- 
size the wire and choose the insulation appropriately. 
 
Per former WCR 00.00EA-D11 (now EY-0128), wiring systems in the vehicle interior are expected to function over the 
temperature ranges of –40 °C to 85 °C (exposure) and –40 °C to 75 °C (function).  For engine compartment and underbody, 
the minimum temperature is the same (-40 °C), while the maximum exposure and operational temperatures vary depending on 
the location (worst case exposure is 125 °C, best case operational is 105 °C).  If a circuit test fails at high ambients, consider 
the likely duty cycle when evaluating the necessity of action.  For example, it's unlikely that the heated backlite would be 
operated when the temperature was 75 °C. 
 
Consult with the alternator engineer for alternator sense circuit resistance. 
 
Pick the proper insulation for the wires 
 
Types of Insulation 
 
Worldwide metric wire specifications 
 
The new standard wire for use on Ford products for 2006 and beyond will be SAE/USCAR-23, the USCAR Global Wire 
Specification.  Currently available for use is WSS-M22P7-Ax, where the x designates the temperature rating of the wire (see 
below).  Note that –A5 through –A8 are nickel-plated.  All are considered to be thin wall, and they are usable for voltages 
under 60 volts AC RMS. 
 
WSS-M22P7-A1  
85 °C 
WSS-M22P7-A2  
100 °C 
WSS-M22P7-A3  
125 °C 
WSS-M22P7-A4  
150 °C 
WSS-M22P7-A5  
175 °C 
WSS-M22P7-A6  
200 °C 


### 第 27 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 27 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
WSS-M22P7-A7  
225 °C 
WSS-M22P7-A8  
250 °C 
 
For the benefit of programs that need wires with different steady-state temperatures than our current legacy wire specifications 
but can't wait for USCAR 23 wire, EDS Core has released a spec that generally follows USCAR 23 but uses SAE (AWG) wire 
rather than metric wire sizes, and it also includes some additional tests, like those cited in the M22P7 spec.  ES-4C34-14401-
AA was released in June 2003. 
 
Carryover legacy wire specifications (North America) 
 
During the transition to the worldwide wire spec, existing legacy wire specifications will continue to be used in both Europe 
and North America.  Note that some of these insulation materials will likely qualify for use under the worldwide wire spec as 
well; if it passes the worldwide tests, it meets the spec.  Here are the legacy wire specifications for North America: 
 
PVC (23 mil) -- Spec ESF-M1L56-A, Drafting Code D [REFERENCE ONLY - DO NOT USE] 
PVC (16 mil thinwall)-- Spec ESB-M1L120-A, Drafting Code AH 
PVC (Hi-Flex) -- Spec ESA-M1L77-A 
PVC (10 mil) – Spec WSB-M1L134-A1, Drafting Code BC 
 
This is the standard insulation for use in I/P and body wiring.  Historically, it was deemed not suitable for engine 
compartment use (in North America, at least) because of a low service temperature (85 °C or 185 °F).  However, improved 
heat mapping underhood and higher-temperature PVC may allow its use underhood.  Also, PVC was historically deemed 
unsuitable for use in sealed connectors because of cold flow concerns.  However, this is less of a risk nowadays.  See the 
section on PVC and cold-flow in the CHOOSE TERMINALS AND CONNECTORS section of this guide for more 
information. 
 
Regular PVC is a thermoplastic insulation, meaning that it will melt and flow. 
 
NOTE:  In some regions, PVC is being phased out, a decision that is driven by governmental regulations, especially in 
Europe.  The original phase-out period was to last for five years, after which PVC would no longer be used on Ford vehicles. 
As of July 2003, though, this is not being emphasized in North America, meaning that PVC is still OK for use.  This 
Corporate direction is subject to change; consult with EDS Core for the latest developments. 
 
Hypalon -- Spec ESB-M1L54-A, Drafting Code B [REFERENCE ONLY - DO NOT USE] 
 
Hypalon, the former insulation for wire-type fuse links, is no longer to be used.  Instead, use materials that meet the SAE J156 
performance specification for fuse links. 
 
XLPE (Cross-Linked Polyethylene, 23 mil) -- Spec ESB-M1L85-B, Drafting Code AB [REFERENCE ONLY – DO NOT 
USE] 
XLPE (16 mil thinwall)-- Spec ESB-M1L123-A, Drafting Code AZ 
XLPE (10 mil) -- Spec WSB-M1L135-A1, Drafting Code BD 
 
This is effective at resisting high temperatures (maximum continuous temperature 257 °F, or 125 °C).  It is the historical 
preferred choice in North America for use in the engine compartment. It is also to be used in I/P and body applications that 
require sealed connectors but can't use PVC insulation. 
 
XLPE is a thermoset insulation, meaning that it will not melt as the temperature rises.  It is the crosslinking process that 
makes a material thermoset; crosslinked PVC is also a thermoset insulation. 
 
Teflon -- Spec ESF-M1L111-A 
 
This insulation is used in very high temperature areas (up to 250 °C). Its primary use is in HEGO pigtails.  Note that dual-wall 
heat shrink tubing has difficulty sealing Teflon-insulated wires, as the slippery nature of the insulation makes it hard for the 


### 第 28 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 28 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
adhesive to bond to it.  Also, any splices to Teflon wires need to be located in areas where the temperature is less than 125 °C; 
otherwise, the adhesive may melt and reflow, compromising the seal and eventually causing it to fail.  NOTE: previous 
references to ESB-M1L93-A were incorrect; M1L111 is the correct spec number. 
 
 
Some additional guidelines: 
 
CHOOSE INSULATION THAT CAN WITHSTAND THE ENVIRONMENT.  Make sure that its maximum allowable 
temperature (ambient temp. plus the electrical load temp. rise under both steady-state and 135% overload conditions) is not 
exceeded.  See the section Provide Circuit Protection. 
 
Make sure that the insulation is compatible with any fluids it may encounter -- gasoline, oil, antifreeze, brake fluid, 
transmission fluid, power steering fluid, etc. Wires meeting the worldwide compatibility specification will do this.  
HISTORICAL NOTE:  no insulation is compatible with ESB-M1C158-A electrical grease.  WSB-M1C239-A grease does 
not attack XLPE insulation like M1C158 does. 
 
Whenever there is motion between two components such that the wiring connecting them is bending or flexing, there is a risk 
of failure (open/intermittent circuit, increased resistance, etc.) due to strand breakage.  That wiring must pass cycle testing: it 
must withstand the specified number of cycles of bending or flexing without strand breakage.  The exact number of cycles 
must be specified by each component or system (doors, seats, liftgates, brake and accelerator pedals, etc.), preferably in their 
SDS's.  If that information is not contained in their SDS's, then work with them to develop a requirement, or alert program 
management if that fails. 
 
ENGINE COMPARTMENT: Traditional North American design practice has been to use exclusively XLPE insulation 
(rated for 125 °C) in the engine compartment.  However, with improved heat profiling of the engine compartment, it may be 
possible to use lower-rated temperature insulations in certain areas underhood.  For example, if an area along the left-hand 
fender or shock tower never sees temperatures higher than 90 °C, then the wire insulation in that area would not need to be 
rated for over 100 °C.  Accurate heat profiling is the key, though; without it, wire insulation optimization is not possible.  
Also important is the availability of sealed connectors that have been tested with the proposed insulation at those 
temperatures. 
 
Don't forget the thermoplastic vs. thermoset considerations, either; thermoplastic-insulated wires can melt and short 
together, but thermoset wires can't.  Be very sure that the risk of extended temperature excursions is low, for otherwise, 
thermoplastic insulation will melt.  Tubing around wires will exacerbate the problem. 
 
With higher-temperature wire specifications becoming available, the temptation will be great to use these insulations with 
aggressively downsized wires.  Over-aggressive downsizing may cause difficulties, however.  The wires may run so hot that 
they pose a hazard to other nearby components – trim, carpeting, etc.  When designing circuits, design them such that not only 
will the wire handle the expected temperatures but also will not heat neighboring components excessively. 
 
Determine the Preliminary Length, Routing 
 
There are times when you may need to know the approximate length of certain circuits before the routing is complete -- 
performing rough circuit analysis, for example.  If so, estimate how long the circuits are, using the physical and digital design 
aid bucks and your experience to guide you. 
 
Resistance Wire Considerations 
 
DETERMINE POWER DISSIPATION when resistance wire is used in a circuit.  DO NOT EXCEED a resistance wire power 
dissipation of 5 watts/ft. (16.4 w/m). to avoid damage to adjacent wires.  If the resistance wire is doubled back on itself under 
the harness covering, the maximum is reduced to 2.5 w/ft (8.2 w/m).  The preferred design when resistance wire must be 
doubled back on itself is to place the double-back portion outside the harness covering.  This isolates the resistance wire from 
the main bundle.  Ground-side placement of resistance wire for voltage dropping is desirable for overload protection. 
 
Special considerations with sensors that "breathe" 


### 第 29 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 29 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
There are several sensors and switches (listed below) that must "breathe" as the temperature changes so that their calibrations 
and functions are unaffected.  If the device is sealed or uses a sealed connector (as it would underhood), then the breathing 
has typically been through the wire.  Component engineers may choose to use the wire for their components' breathing, but 
they must test to see if it will function properly for them.  The wire must not be relied upon for a controlled breathing rate, as 
this is impossible to obtain with wire; the function of the wire is to conduct electrons, not air molecules.  Any SDS 
requirements mandating specific airflow rates should be rejected for this reason. 
 
These are some of the components which must "breathe":  oil pressure switch; A/C pressure switch; A/C pressure sensor; 
power steering switch and sensor; natural gas fuel rail and tank sensor; turbo boost pressure; brake pressure sensors and 
switches; and heated and non-heated exhaust gas oxygen sensors.  Note that this is not an exhaustive list (no pun intended). 
 
If it becomes necessary to use the wire to allow the device to breathe, then there are important things to consider.  First, use 
the minimum wire size that has been tested with the device for this breathing.  This minimum wire size, or a larger one, must 
be used.  Do not use a smaller wire size, even if circuit analyses say you can; doing so may restrict airflow.  Be sure to use the 
same wire construction, too; changing to a different construction (different stranding, compressed core, etc.) could affect 
airflow.  In addition, no grease, solder or other contaminants must be allowed to plug up the wires.  Even splices and splice 
seals can interfere with the pathway.  Splices may need to be located a minimum distance away from the device.  However, do 
not remove splice sealing to accommodate device breathing. 
 
4. 
FOLLOW STANDARD SCHEMATICS AND PINOUTS 
 
Because of concerns about EMC issues and system interactions, and because of a desire to standardize engineering practice, it 
is desirable to connect to certain systems in a standardized manner.  If a standard schematic exists, follow it. 
 
Note about stoplamp wiring: It has been the traditional practice in North America and Japan to feed the stoplamp switch from 
a hot-at-all-times circuit, while Europe and Australia have fed it from a run-only circuit.  These practices are not mandated by 
regulations; they've simply evolved over the years.  They should be followed unless technical considerations preclude it 
(derived from former SDS requirement ED-0113) or other compelling reasons arise.  
 
If a standard pinout has been defined for an inline connector, use that pinout.  A prime example is the 42-way connector used 
on several engines for connecting the engine wiring to the PCM harness.  Some plants have defined this pinout so that all the 
engines in the plant have the same connection to the hot test stand.  In particular, follow the pinout specified below for the 12-
way on the engine harness: 
 
PIN 
CIRCUIT        NUMBER
1 
20 
       COP PWR 
2 
1140              V PWR (MAF/IAT)
3 
1138              Inj/IAC PWR
4 
391                HEGO/EGR PWR
5 
967                MAF to PCM
6 
968 
        MAF RETURN
7 
321                A/C CLUTCH RELAY
8 
48                  SHIELD GROUND 
9 
--- 
        OPEN FOR FUTURE ASSIGNMENT
10 
743                IAT to PCM
11 
31 
        OIL PRESSURE
12 
57                  A/C CLUTCH GROUND(Also type 4 Gnd)
12-WAY Engine Harness
Male, looking into harness
Pin 1
Pin 9
Pin 4
Pin 12
 
 


### 第 30 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 30 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
5. 
CHOOSE TERMINALS AND CONNECTORS 
 
Note: this section will be drastically revised in the future, based in part upon input from the connector engineering 
section.  It will emphasize process-related items and application-related items. 
 
The development and release of connectors and terminals is the responsibility of the Connector Engineering Section(s).  Each 
vehicle platform will have a connector system engineer assigned to it.  His/her job will be to review new connector 
applications on that vehicle.  Every connector and terminal system must be approved by the connector system engineer prior 
to release on a vehicle.  The goal of this policy is to ensure that the connector/terminal systems are in compliance with the 
Electrical Connector SDS. 
 
The Ford wiring platform engineer, the full-service supplier engineer and the Ford connector system engineer will work 
together to select appropriate connector/terminal systems. 
 
TERMINAL SELECTION TIPS 
 
Base your decision on: 
 
 
Wire gage size 
 
Current carrying capacity (normal and 135% overcurrent) 
 
Connector type 
 
Insulation type 
 
Ambient operating temperature 
 
The EDGS design system will select the appropriate terminal for you under most conditions.  With the phase-out of EDGS in 
progress, the C3P tools will assume this function.  Remember, the general design principle is to use terminals that have a 
maximum temperature rating that's higher than the maximum temperature it would see under worst-case conditions (i.e., the 
circuit is carrying 135% of the rated fuse current for the fuse protecting that circuit).  Terminals are not to exceed their rated 
temperatures. 
 
Select terminals for low-energy applications (open circuit voltage <= 5 volts and operating current <= 100 milliamps) in 
accordance with SDS requirement EL-0167.  It will tell when to use precious metal plating on the terminals. 
 
Expanding upon SDS requirement ED-1443: When connecting to heat-generating components (ignition and headlamp 
switches, blower motor switches, cooling fan motors, circuit breakers, fuses, etc) where currents exceed 15 amps, use high-
temperature (C7025 or beryllium-copper or equivalent) terminals and 10 gage wire unless the component engineer provides 
data proving that high-temperature terminals are not required.  Note that C7025 terminals perform much like beryllium-
copper terminals, but at a lower cost.  EXCEPTION:  the use of high-temperature terminals and upgaged wiring is mandatory 
on high-current ignition switches and high-current headlamp switches.  This is to conduct heat from the terminal interfaces, 
ensuring a more robust system. 
 
Keep in mind that as the ambient or environmental temperature changes, terminals will generally not be able to conduct as 
much current as they can at standard temperatures.  You must take this into account when selecting terminals for new 
applications or when substituting one terminal for another.  Failure to do this can lead to erratic system operation at the higher 
ambient temperatures, which can lead to customer dissatisfaction, increased warranty and even campaigns or recalls.  Re-
rating information for a given terminal will be available from the terminal supplier. 
 
The wire chosen for use in electrical connectors and terminals must have a core cross-section and outer diameter that is 
compatible with the terminal grip size (English for English, metric for metric). (From SDS requirement EL-0043) 
 
When terminals require bending at the crimping station, the specific bend angle must be specified on the wire harness detailed 
print/drawing/CAD-file. (From former SDS requirement EL-0160) 
 


### 第 31 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 31 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Side note: testing methods for verifying circuit continuity must not engage or deform the terminals.  This causes them to be 
less effective in engaging the proper mating terminals. 
 
 
CONNECTION SELECTION AND PACKAGING GUIDELINES: 
 
Process Considerations 
 
Before using a connector, make sure that it is in fact one of the preferred connectors.  You can verify this with Connector 
Engineering.  Any connector that is not preferred is not to be used in a vehicle and is not to be released on a harness.  
Electrical PMTs need to convey this to the engineers of all components to which the wiring interfaces.  The purpose of this 
rule is to enforce the removal of substandard connection systems from our products.  If a component engineer objects, then 
direct him/her to take up the issue with Connector Engineering. 
 
Use only terminals and connectors based on designs found in the most recent version of the Connector and Terminal 
Catalogs.  The designs in the catalogs represent preferred designs.  Terminals and connectors released after the catalog 
publishing date are, for the most part, based on these designs. 
 
Request via the Connector Engineering Section(s) any new connectors or terminals which are required for new programs and 
applications.  Use the application form found in the Connector System Selection, Development & Release Process manual 
(now online at the EDS Clearinghouse and/or the Connector Web sites). 
 
Use only those connectors that have secondary locks to prevent terminal pushouts.  Use of non-secondary-lock connectors in 
any application can only be approved by Connector Engineering. 
 
All connectors must be positive locking! 
 
Connectors should have positive snap locking devices that allow easy installation with low insertion efforts and easy removal. 
 Snap should be easily felt and heard (tactile and audible feedback). 
 
Application Considerations 
 
Follow the procedure specified in SDS requirement EL-0167 for selecting the proper base material and plating material to use 
on terminals. 
 
When selecting in-line connectors (especially bulkhead connectors), consider the likelihood of additional circuits being 
required in future model years.  To allow for added content in the future, select connectors such that there is some extra 
capacity.  If you don't do this, then you may need to choose larger connectors or use additional ones in the future, and 
packaging them may be difficult. 
 
Select connectors in accordance with SDS requirement ED-0031.   
 
If a connector will be located in a hostile environment or wet area (locations where the risk of water entry and corrosion is 
high), use a sealed or submergible (the terms are synonymous) connector.  If not, you can use an open one. "Hostile 
environment" areas include the engine compartment, wheel wells, underbody, doors, etc.  Use sealed connectors in areas 
prone to spillage from customer use, such as cupholders, consoles, etc. (see also section 7). This guidance will be revised in 
light of ED-0031. 
 
Sealed connectors may also be needed in certain cases of non-fluid contamination.  For example, one side air bag terminal and 
connector system became contaminated from the seat foam, eliminating the effectiveness of the gold terminals. Alternatively, 
pursue other changes that eliminate the source(s) of the contamination.  The need for sealed connectors in these applications 
will be driven by requirements from other subsystems. 
 
If a connector will be located in a direct splash area, it must be protected by some sort of barrier or fluid diverter, unless the 
connector has passed a high-pressure spray test (i.e., it's certified as a "super-sealed" connector).  Simply using a submergible 


### 第 32 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 32 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
connector is not enough, for given sufficient pressure, water and other contaminants can be forced past the connector's seals.  
Examples of  "diverters" can be a stand-alone shield, a boot covering the connector, or even an additional sealing method on 
the connector (such as Raychem gel).  It may be possible to eliminate the need for a barrier or diverter if the mating 
component is packaged and oriented such that the connector points rearward or away from the direct splash (ref. former SDS 
requirement ED-0040, now part of ED-0031). 
 
Use sealed connectors in footwells and other areas where the driver's and passenger's feet will rest.  These areas collect excess 
moisture and salt. Better yet, locate these connectors on side rails, away from moisture. 
 
Special note for trucks:  Some customers may wish to power wash the interiors of their truck cabs after a day's worth of mud 
and dirt (vinyl interiors only, obviously).  Keep this in mind when selecting connectors for these vehicles.  Unused connectors 
should either be capped or removed altogether.  Submergible connectors will be needed for these areas that would normally 
be dry in other vehicles.  Additional water barriers may be necessary, as submergible connectors cannot withstand high-
pressure streams. 
 
Locate connectors to facilitate installation and service.   
 
PVC and cold-flow:  In the past, PVC was not acceptable for use in submergible connectors because it cold flows at elevated 
pressure and, to some extent, temperature; this allows it to set in a deformed pattern, compromising the integrity of the seal. 
The pressure of the wire seal alone, especially with a mat seal, is frequently sufficient to induce cold flow.  With smaller 
insulation thicknesses (12 mil, 10 mil) and individual wire seals becoming more common, cold flow becomes less likely.  All 
of the connector suppliers to Ford have certified that their connectors will function with 16 mil and 10 mil PVC wire and will 
maintain seal integrity in passenger compartment usage (i.e., up to 85 °C).  What does this mean to the engineer?  It means 
that PVC can be used in sealed connectors located in the doors and wet areas other than the engine compartment.  
Engine compartment usage would require PVC insulation rated for at least 100 °C and would also require that the connector 
seals be validated for those higher temperatures.  Check with the Connector section for information on connectors that have 
been validated for use with PVC at temperatures above 85 °C. 
 
Be sure to use the correct submergible connector for standard wall or thinwall wire.  The sealing grommet hole diameters are 
different, and sealing integrity will not be maintained unless the correct insulation type is specified.  Consult with the 
appropriate connector engineer. 
 
Sometimes it is necessary to fill open connectors with electrical grease in order to resist water and corrosion.  Use WSB-
M1C239-A grease for this purpose.  It does not react with XLPE insulation and eat it away, unlike the traditional ESB-
M1C158-A.  Historical Note: Do NOT use ESB-M1C158-A grease for filling connectors.  
 
Retain or clip all connectors which are not used 100% to prevent rattles, sight problems, etc.  This is especially applicable to 
the instrument panel area. Use portable holes rather than tape ties, and use foam donuts on retainers where necessary. This is 
to help prevent rattles. 
 
Consult with Vehicle Operations for assembly sequencing issues that will affect which half of the connector pair will go on 
the wiring. 
 
Do not tape all the way to the back of connectors.  Doing so causes the terminals to deflect, leading to high mating forces and 
terminal pushouts.  Instead, tape to a distance corresponding to the maximum dimension between the terminal cavities used on 
that connector. Example:  if the maximum spread between terminals on a given connector is 60 mm, then stop the taping 60 
mm back from the connector.  Tape wrap that stops at this distance is considered to be fully taped all the way to the back of 
the connector.  See the section on tape wrap for more information on this, including applicable SDS requirements. 
  
Cavity plugs may be used if and only if they engage the terminal locking features of the connector and if their presence can be 
detected by manufacturing fixtures (from former SDS requirement EL-0060).  This insures that the plugs cannot fall out in 
shipment or in use, causing a breach in the connector seal.  The use of any plugs which do not engage the terminal locking 
features is prohibited.  Do not use dummy wires, either; they can wick water into the connector.  Dummy wires may be used 
for pre-CP prototype builds only, not for production.  If dummy wires are used, then the non-terminated ends of the wires 
must be sealed with blind dual wall heat shrink tubing. NOTE:  This applies to mat-seal connectors only.  Connectors using 


### 第 33 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 33 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
individual wire seals have little choice but to use wire seals without holes to seal empty cavities, unless the cavity in the 
hardshell has been molded over or a suitable cavity plug exists.  
 
Wires entering submergible connectors must not side-stress the connector sealing grommet.  This breaks the wire seal and can 
allow water to enter the connector.  Remove the side stress by re-tailoring the takeout or by using a connector with a strain-
relief cap. In general, wires exiting from connectors should extend 25 mm from the rear face of the connector parallel to the 
insulation grip before being bent or formed into the routing path (exception: connectors with routing or dress covers) (from 
former SDS requirement ED-0095).  For additional considerations, see the section on strain relief. 
 
Some components must use pigtails because of accessibility issues and/or a severe environment. Examples include the fuel 
sender, the HEGO sensors, the MAF sensor, and the TPS sensor. 
 
Avoid using similar types and colors of connectors close together when possible. If this is unavoidable, tailor the takeout 
lengths to eliminate the risk of crossed connections. 
 
The use of edgeboard, tang-type, and molded-over connectors should be eliminated.  The use of blade-type weather resistant 
connectors is restricted to high current applications which cannot be handled by submergible connectors. 
 
The use of multi-terminal bulkhead connectors in the dash panel is recommended in order to eliminate water leaks and wire 
difficulties associated with pulling harnesses through holes in the dash panel. 
 
If you need to specify any kind of push test on connectors or terminals, you must put a note to that effect on the wire assembly 
drawing. This applies to tests conducted by the harness manufacturer. 
 
CONNECTOR PINOUT PRACTICES 
 
When designing a harness-to-component connection, it is best to put the female terminals in the harness-side connection and 
the male terminals in the component side.  Male terminals in harness-side connectors are more likely to get bent or damaged 
in handling than if they're used on the component side. 
 
When determining connector pinouts, make sure that power and ground circuits are not in close proximity (i.e., adjacent) to 
one another.  This is especially important for B+ hot-at-all-times circuits.  This is to reduce the risk of short circuits caused by 
a connector failure such as salt bridges due to corrosion or misaligned or damaged terminals. Consider using a sealed 
connector to reduce the chances that a salt bridge will form.  This is covered in SDS requirement ED-0117, which calls for a 
minimum separation of 5 mm between power and ground circuits.  Note that the effective separation distance can be increased 
by placing dividing walls between circuits. 
 
In particular, make sure that the crank circuit is kept away from hot-at-all-times feeds. 
 
Use standard connector pinouts for the engine interface connectors where possible.  This is to promote ease of hot testing at 
the engine plants.  If, for whatever reason, this is not feasible, then meet with EPME and other involved parties and come up 
with an acceptable resolution. 
 
EYELETS 
 
It is a commonly held belief that serrations in ground eyelets help to form the ground path by cutting through the paint on the 
sheet metal, but this is untrue.  The ground path actually goes from the eyelet to the head of the bolt to the shaft (or through 
the nut and bolt combination), then to the weld nut.  Serrations in eyelets for cutting through paint actually promote corrosion. 
They also reduce the clamp load of the joint. 
 
Eyelets are to be dip-soldered for these reasons: 
• 
to protect old grip designs and crimps without approved standards 
• 
to protect 260 brass material 
• 
to protect bare edge corrosion of the eyelet terminal (pre-plated material which is then stamped, leaving the edges 
unplated), or to protect unplated terminals 


### 第 34 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 34 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
• 
as required by vehicle packaging constraints (corrosion, water wicking, etc.) 
 
If an eyelet terminal does not have an approved crimp standard, it must be dip soldered.  However, there may be alternate 
eyelet designs which do have approved crimp standards and, thus, do not need to be dip soldered.  Use of such alternate 
designs is encouraged. 
 
If an eyelet is going to be carrying high currents (15 A or greater), it may be advantageous to dip solder the crimp, as that 
would add conductivity to the wire-terminal interface 
 
In some circumstances, it may be advisable to dip solder eyelet crimps even if approved crimp standards exist for them.  This 
would be done to improve corrosion resistance (as well as improve conductivity, as mentioned above). 
 
All ground eyelets need to have a provision, either on the terminal itself or on the grounding surface, to prevent rotation (from 
SDS requirement ED-0116 – revival is pending in May 2003).  However, if a non-torsional fastening system is used, then 
anti-rotation features are not required.  It's not only ground eyelets that have to be concerned about rotation.  Consider using 
anti-rotation features (asymmetric B+ terminals and eyelets, non-conducting walls or ridges to stop rotation, etc.) in 
applications where B+ eyelet rotation would put the harness or cable in jeopardy. Also, consider making this connection a 
significant or critical characteristic. 
 
More on anti-rotation:  Anti-rotation features are especially needed when the wire going to the eyelet is of small diameter.  
Such circuits are more prone to damage if the eyelet spins and twists or pulls on the wire.  As for anti-rotation features in the 
sheet metal, they must be able to catch and restrain the eyelet crimp to be effective.  If they are too small, they won't catch the 
eyelet crimp. 
 
B+ eyelet shrouds/insulation must provide socket clearance per Vehicle Operations Power Tool guidelines (from former SDS 
requirement ED-0104). 
 
Eyelets need to extend out a minimum of 25 mm from the bundle, per SDS requirement ED-1479.  If they don't, then the 
eyelet could contact the bundle, chafe the circuits and cause a short circuit.  This contributed to a recall in 2002. 
 
EYELET CRIMP SEALING 
 
Under special circumstances, it may be necessary to seal the eyelet crimp to prevent water or other fluids from entering the 
crimp, being sucked up the wire and entering and contaminating a connector or component.  Those circumstances are when 
vacuum action is involved (i.e., a component or module is venting pressure via the wires) and when the eyelet circuit goes 
right to that module, especially when the eyelet has water falling right on top of it.  This situation was present on a 1996 
vehicle; water was falling right on the eyelet, and it was being sucked through the open crimp and into the wire until it got into 
the module connector, leading to a recall.  While dip solder alone and dual wall heat shrink alone will block much of the 
water, testing in 1997 found that the only totally effective means of sealing eyelets is the combination of dip solder with dual 
wall heat shrink.  This method is not mandated, though; other means of sealing are permissible and may soon be preferable, 
as dip soldering carries some environmental risks. 
 
New SDS requirement ED-1477 (release was imminent at press time), governing crimp sealing, mandates that eyelet crimps 
be sealed when either of these conditions apply: the circuit(s) leading from the eyelet leads directly to a component that can 
breathe through the wiring, without any splice nugget in between, or they pass through a sealed connector before going to a 
component that can breathe through the wiring, without any splice nugget in between.  Note that when there is a sealed splice 
upstream of the eyelet, it should not be necessary to also seal the eyelet crimp, as the heat-shrink tubing and sealant covering 
the splice should greatly restrict, if not prevent, any water that's wicking up from the eyelet from getting past.  Since there is a 
slight risk of water getting through, you may want to consider dip soldering the splice nugget. 
 
Excessive solder buildup may cause installation problems with the eyelets; the excess solder may cold-flow under pressure, 
thus relaxing the clamp load. 
 


### 第 35 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 35 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Eyelets that are sealed with heat shrink need to have a "long neck", which provides needed sealing surface for the tubing.  
Terminals without such a neck cannot be sealed properly.  Eyelets that have double crimps may also have difficulties being 
sealed. 
 
CONNECTOR POSITION ASSURANCE 
 
A Connector Position Assurance (CPA) device is used to provide an additional level of assurance that a connector mating pair 
are, in fact, mated and won't come apart.  Not all connectors use CPA's; not all connectors need to use them.  Here are some 
things to consider when deciding whether or not to use CPA's.  Note that some of them apply more to Connector Engineering 
than the wiring engineer. 
 
1. 
Robustness 
 
Before Connector Engineering releases a CPA for a connector family, it should evaluate the overall robustness of the 
connector system.  Note that Engineering and Vehicle Operations view CPA's differently:  while Engineering views them as 
assembly aids to insure positive latching, V.O. tends to view them as so-called "band-aids" which mask problems with the 
basic connector design.  As they see it, the connector design needs to be robust enough to provide the operator with adequate 
feedback that the connection is completed, and it needs to be packaged such that the operator has no issues with making the 
connection.  These are the typical factors in problematic connections. 
 
2. 
Cost 
 
CPA's add piece cost and assembly direct labor cost.  These costs will need to be offset in order to not adversely affect the 
vehicle's cost. 
 
3. 
Commonality 
 
CPA designs should follow a common architecture and generic design, as proliferation of designs and types adds to unique 
assembly methods.  This must be considered when a connector or connector system is different between two vehicles built on 
the same assembly line. 
 
4. 
Package 
 
You must consider these package issues:  hand access, fingertip access, line of sight, CPA orientation relative to the operator, 
application within an assembly plant on different vehicle lines, etc.  The connection and the CPA should be evaluated through 
the ECCR process and should obtain a favorable rating.  Otherwise, V.O. advises against using a CPA. 
 
Vehicle Operations requirements for using CPA's 
 
1. 
Hand access 
 
For adequate hand access to a connector package, a 75 mm (3 in.) radial clearance must be provided around the connector. 
 
2. 
Fingertip access 
 
For adequate fingertip access to a CPA package, a 25 mm (1 in.) radial clearance must provided around the CPA.  If this is 
not possible, then the CPA must be positioned to maximize clearance and must not be near any components which would 
affect the operator's ability to secure the CPA. 
 
3. 
CPA efforts 
 
The effort to seat the CPA must be less than 45 newtons (10 lb.) for fingertip CPA slide-into-place applications. 
 
4. 
Line of sight 
 


### 第 36 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 36 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
The location of the CPA must be visible by the operator when the connection is being made. 
 
5. 
Application 
 
The application and use of CPA's should be limited to critical safety-related components or systems.  They should not be used 
to mask design or process problems with poorly performing connectors or connector systems. 
 
6. 
Retention 
 
The CPA must be adequately retained to the connector to prevent its being knocked off during shipping and handling. 
 
Is a CPA required? 
 
Audit the safety-related connection capability on at least 100 vehicles.  Any partially mated connector is deemed failure and 
the CPA should be required.  Critical safety-related components/systems may require sporadic audits to justify the ongoing 
elimination of the CPA.  An audit tool can be made which is a simple metal blade that should slide under the connector 
primary lock.  This tool will only slide under the primary lock if the connection is fully seated.


### 第 37 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 37 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
6. 
PROVIDE CIRCUIT PROTECTION 
 
It is essential that the wiring be protected from overloads and short circuits.  The corporate generic fusing strategy is to be 
used as the basis for the fusing of a given vehicle's electrical distribution system.  Some modifications are permissible for 
accommodating unique option content, but major changes are not allowed without the concurrence of the strategy owners, the 
EDS core group and Electrical/Electronic System Engineering management. 
 
The primary function of circuit protection devices is to protect the wiring from damage due to overloads and short circuits.  
While they can help protect downstream devices, this is only an incidental benefit.  Engineers of downstream devices must 
NOT rely on circuit protection devices to protect their components.  There are some exceptions to this policy:  EDS circuit 
protection devices will protect high-current, continuous duty loads which do not see stall currents in normal operation.  Also, 
the rheostat on the headlamp switch dimmer is fused for 5 amps, although the wire can take a higher current.  Additional 
exceptions may arise, especially if the fusing strategy is revised with an eye to fusing to protect components. 
 
There are seven types of circuit protection devices.  Of these, three are in common use in our vehicles: 
• 
Fast-blow fuses (low-current) – ATO®, MINI® (Littelfuse products); medium-tab or miniature-tab fuses (ISO 8820) 
• 
Slow-blow fuses (high-current) – MAXI™, MEGA®, JCASE™ (Littelfuse products); high-current tab fuses (ISO 
8820) 
• 
Circuit Breakers 
 
 
And these three types are likely to become more common in our vehicles: 
• 
In-line fuse elements (slow-blow, high-current), such as the CABLE PRO® products from Littelfuse 
• 
PTC devices – these solid state devices sense when there is an overcurrent and restrict current flow until power is 
removed, functioning like a non-cycling circuit breaker 
• 
Smart FET's – when combined with intelligence inside smart junction boxes, these also sense overcurrent conditions 
and restrict current flow, which keeps the wires from overheating much, if at all 
 
The last type of protection device, the wire-type fuse link, is no longer in general use.  However, it may be used under special 
circumstances (i.e., protecting battery-to-PDB and battery-to-alternator feeds). See the back of this section, as well as the EDS 
SDS, for information on fuse links. 
 
FUSES 
 
Fuses are the most frequently used circuit protectors and are the most sensitive to load current variations.  Fuses are chosen to 
prevent wire temperatures from exceeding the maximum continuous operating temperature rating of the wire insulation when: 
 
 
The wiring is used in maximum ambient temperature areas; 
 
The wiring is carrying the maximum load current. 
 
Per SDS requirement ED-0062, design the circuits such that the maximum temperature they see under maximum steady-state 
conditions (i.e., the sum of the ambient temperature and the temperature rise caused by the maximum steady-state current 
flow) do not exceed the temperature rating of the wire specification used for the circuit.  For example, circuits using wire 
meeting WSS-M22P7-A1 must not exceed 85 °C under maximum steady-state conditions.  If the wire specification does not 
have an official rating (i.e., legacy wire specifications), then use the following values: 
 
 
 
PVC (M1L120, M1L134, etc.) 
 
  85 °C     185 °F 
 
 
XLPE (M1L123, M1L135, etc.) 
 
 125 °C     257 °F 
 
 
Teflon 
 
 
 
 
 TBD 
 
If a wire spec can be documented as meeting a higher steady-state rating than those shown here (i.e., PVC rated for 100 °C), 
then you may design to that higher temperature rating.  Any such documentation must be reviewed and approved by EDS 
Core. 
 


### 第 38 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 38 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Wires can tolerate short-term (30 minutes or less) excursions above the rated temperatures under fault conditions, such as 
135% fault current (i.e., where the current in the wire is at 135% of the actual, as opposed to derated, fuse size for a maximum 
of 30 minutes) or 200% or 350% fault currents.  Regardless of the criteria used, the wires must be able to withstand a specific 
fault current for a given period of time (more details in the Wire Sizing section) without exceeding its maximum excursion 
temperature.  For legacy wire specifications, these are the permitted excursion temperatures: 
 
 
 
PVC 
 
130 °C (16 mil M1L120) 110 °C (10 mil M1L134) 
 
 
XLPE  
185 °C (16 mil M1L123) 150 °C (10 mil M1L135) 
 
 
Teflon 
250 °C 
 
If a wire spec can be documented as meeting a higher excursion temperature (or fault temperature) rating than those shown 
here (i.e., PVC rated for 140 °C), then you may design to that higher temperature rating.  Any such documentation must be 
reviewed and approved by EDS Core. 
 
For materials meeting the USCAR-23 or the M22P7 worldwide wire spec, follow the procedure stated in detail 20666, which 
is attached to ED-1459.  Basically, if the material also meets a legacy spec, do one of the following: use the appropriate 
excursion (i.e., failure) temperature for that legacy spec; conduct tests on the material to determine the failure temperature, or 
assume the failure temperature to be equal to the steady-state temperature rating.  The last option is not preferred, as it would 
compel the use of larger wires. 
 
The maximum allowable temperature rise (steady state) is defined as the maximum continuous operating temperature rating 
minus the ambient temperature. 
 
Circuits carrying intermittent loads may be added to existing fuses as long as the total load does not exceed the rating of the 
fuse. 
 
FUSE DERATING 
 
Fuses of all types need to be derated when used at ambient temperatures greater than 20 °C.  This means that as the ambient 
temperature increases, it will take less current to blow the fuse.  This is the procedure: 
 
1. 
Look up the fuse re-rating factor, from published data, at the fuse ambient temperature.  This temperature is the 
temperature the fuse will see for the 95th percentile customer.  If this information is unavailable, use 40 °C for fuses 
located in the interior of the vehicle and 105 °C for fuses located in the engine compartment that are *not* cooled by 
interior air flow. 
 
2. 
Multiply this re-rating factor by .88 (88 percent) to eliminate nuisance failures. 
 
EXAMPLE:  An engineer wants to find the fuse re-rating factor for a 30 A Maxi-fuse in an ambient of 120 °C.  The value 
from the 30 A Maxi fuse curve gives 75%.  Multiplying by .88 gives 66%.  Therefore, the maximum steady state load through 
that fuse under those conditions is 19.8 A. 
 
Fuse blow characteristic and re-rating curves for Littelfuse products are now available on the EDS Clearinghouse Website.  
These are valid only for Littelfuse products; products from other suppliers may behave differently.  Contact those suppliers 
for the characteristic curves for their products. 
 
WIRE SIZING 
 
Wires should be sized in accordance with the maximum insulation temperatures mentioned above.  There are three SDS 
requirements that apply: 
• 
ED-0062, for sizing under steady-state current conditions – the wire must not exceed its maximum steady-state 
temperature rating 
• 
ED-0063, for sizing under overload conditions – the wire must be sized so that under a direct short circuit, the 
overcurrent protection device (OCPD) operates within either 5 seconds (for fuses) or 10 seconds (circuit breakers or 
wire-type fuse links) (exceptions noted below) 


### 第 39 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 39 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
• 
ED-1459, also for sizing under overload conditions – the wire must be sized so that it can endure 135% fault current 
(current equal to 135% of the actual, not derated, OCPD value) for 30 minutes without exceeding its maximum fault 
temperature (exceptions noted below) 
 
The exceptions to ED-0063 and –1459 are as follows: both requirements do not apply if the OCPD being used never allows 
the wires to reach an overload condition.  ED-1459 does not apply if the OCPD responds to an overcurrent condition within 
0.5 seconds (if the OCPD is a fast-blow fuse) or 1 second (if the OCPD is a slow-blow fuse). However, this exception does 
not apply to certain kinds of circuits such as motors, cigar lighters and solenoids; those still need to meet ED-1459, no matter 
what. 
 
Note that the permissible blow time for fuses under ED-0063, 5 seconds, corresponds to a current of 200% of the fuse value.  
Similarly, the 0.5-second blow time condition that's one of the exceptions for ED-1459 corresponds to a current of 350% of 
the fuse value, and the 1-second blow time condition corresponds to a current of 600% of the fuse value.  All of these values 
derive from the ISO 8820 fuse specification.  
 
Rationale for wire sizing under overload conditions: the intent of ED-0063 is to have the OCPD (specifically, the fuse) 
operate in a more favorable region of its operating characteristics.  If it responds to a hard short within 5 seconds, the wire 
never has a chance to heat up significantly.  This can be done by sizing the wire so that it passes 200% of the fuse value under 
hard short conditions.  At the same time, we must protect against low-grade overload conditions or intermittent or resistive 
shorts, which is why we have ED-1459.  Unfortunately, the 135% region is not a favorable one for the fuse, as it takes up to 
30 minutes to blow under those conditions.  This does allow the wire to heat up significantly.  However, if the wire is sized so 
that it can pass currents of 350% or more of the fuse value, or 600% or more if protected by a slow-blow fuse, then the fuse 
will blow within 0.5 seconds or within 1 second, respectively.  Once again, the wire never has a chance to heat up.  This 0.5-
second or 1-second blow time is significant because it can capture many intermittent short circuits, thus never permitting the 
fuse to see the 135% region of operation.  That's why the 135% test is waived if the OCPD/fuse can respond to a hard short 
within 0.5 seconds or 1 second. 
 
Note about sizing to 110%:  some competitors are known to use the 110% rule when sizing wires.  This also derives from the 
ISO 8820 spec; at 110% of its rating, a fuse is never supposed to open.  However, the maximum temperature cannot be the 
elevated fault temperature used for the other values; rather, it must be the regular steady-state temperature rating, for the wire 
would effectively be operating forever at those temperatures (the elevated fault temperature is for short durations only). 
 
For European-designed programs:  At the nominal current of the associated fuse, the temperature increase of the wire over 
the ambient temperature must be a maximum of 60 °C.   Otherwise, the wire must be increased in size. 
 
NOTES ON FUSE AND WIRE SIZING: 
 
The FuseTool program (developed by Paul Vegh of the EDS department) should be used for initial fuse and wire 
identification.  It is currently available on the EDS Web site (http://www.eds.ford.com).  NOTE: it is only useful for AWG 
wire and for PVC and XLPE insulations.  An approximation for the M22P7 spec is available (August 2002). 
 
When sizing wires for 135% overcurrent conditions, be sure to choose terminals that will withstand those overcurrent 
conditions.  It would not do to have the wire pass but the terminal fail.  This may be difficult to do with component 
connections, where the component controls the terminals being used. 
 
Watch out for ground circuits that handle the return currents for multiple loads (example: a common ground for the cigar 
lighter and the power point), especially if those loads are on separate fuses.  The common ground circuits need to be sized to 
handle the combined loads from simultaneous operation of those circuits (except, of course, if the loads can never be operated 
simultaneously).  Otherwise, they may exceed their temperature ratings and be damaged, even though the fuses aren't seeing 
overload conditions.  Monitor this condition in CAE evaluations, being sure to account for the normal conditions of fuse 
derating, ambient temperatures, battery voltage and load profiles.  If you can accurately model the effects of the other wires in 
the bundle on the wire temperature, good; otherwise, assume it's in free air.  If CAE analysis yields a temperature that's within 
5 degrees of spec, then you should do a physical test. 
  


### 第 40 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 40 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Of special interest to PVT engineers and engineers on launch:  If the fusing is being changed on a circuit, check with the 
engineers who are most familiar with the affected subsystems.  If the fuse is being changed to a higher rating, circuit analyses 
may need to be repeated.  This will avoid situations like switches sized for one current seeing a much higher current due to a 
larger fuse and failing as a result. 
 
Short circuit and load survey analysis must be completed prior to implementation -- either by breadboard, vehicle test or CAE 
analysis. 
 
Special circumstances must be clearly identified.  Issues affecting requirements for other subsystems or components must be 
signed off by the affected activities (e.g., blower circuits, voltage drop issues for windows or headlamps, etc.) 
 
Special dispensation to deviate from these requirements must be reviewed and approved in writing by the EDS Core sections.  
 
Do not reduce wire gages without checking the circuit protector for the wire. 
 
CIRCUIT BREAKERS 
 
There are two types of circuit breakers, cycling and non-cycling.  They differ drastically in their methods of operation and 
their effect on protected wiring. 
 
The cycling circuit breaker is used for the headlamp circuit and the windshield wiper circuit.  When a short or overload 
occurs, the breaker goes into a cycle of opening and closing.  As a result, the breaker will pass currents averaging 125% of the 
circuit breaker rating.  The high current surge during the "on" time will heat the wires; if the breaker contacts stick closed, the 
wiring may fail.  Make sure the wiring is sufficiently protected by high-current fuses in case of circuit breaker failure. 
 
Non-cycling circuit breakers share some characteristics of fuses and cycling-type breakers.  Non-cycling breakers will not 
open when subjected to nominal intermittent overloads.  When they do open, though, they will not reset until the load/short is 
completely removed.  Per specification, the breaker shall carry 100% rated load indefinitely and shall open at 135% rated 
load within one hour.  The wiring must be designed to carry 135% of the rated circuit breaker load under all ambient 
conditions. 
 
Recent design practice (late 1990's) has been to reduce or eliminate the use of circuit breakers.  However, they are likely to 
make a comeback for the power window circuits, protecting for the condition when all windows are operated simultaneously. 
 
 
HIGH CURRENT FUSES 
 
High current fuses have replaced fuse links for protecting the wiring.  This process was motivated by the desire to prevent 
electrical campaigns (recalls).  The high current fuses are easier to service and behave more predictably and reliably 
than fuse links. NOTE:  Yazaki calls its high current fuses "fuse links"; do not confuse these with the traditional fuse links. 
 
High current power distribution circuits carrying 30 amperes or more shall be protected by high current fuses to and through 
their switching or distribution component (e.g., headlamp switch, ignition switch, low current fuse panel, etc.). 
 
A special type of high current fuse is a MEGA® fuse, so named because of the large currents it can handle. It has been used to 
protect battery cable and alternator output feeds.  Use of the MEGA® fuse is optional; engineers may choose to use either 
MEGA® fuses or fuse links to protect battery-to-PDB and battery-to-alternator feeds.  Consult with EDS Core Technology for 
the latest information.  For very high current motor feeds, use the MEGA® fuse, as wire-type fuse links are not permissible. 
 
SMART FET'S 
 
Smart FET's (field effect transistors) are a fairly recent innovation in the area of circuit protection.  They are used instead of 
fast-blow fuses to protect the wiring and are typically packaged inside smart junction boxes (SJB's).  They have the ability to 
detect an overload and clamp the current at a level below which the wiring overheats.  This detection is either via thermal 
sensing or by software control.  It is even possible for a smart FET to completely shut off the current, like a fuse or a non-
cycling circuit breaker, or it can try to supply current after a specific interval has passed, like a cycling circuit breaker. 


### 第 41 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 41 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
This behavior of smart FET's has important implications for the wiring.  If the wires will never see overload conditions, then it 
will not be necessary to size the wires for those conditions.  All that will be necessary is for the wires to be sized to maximum 
steady-state current conditions for their environments.  This will lead to smaller diameter wires, with all of the benefits that go 
along with them.  However, the wires might still see overload conditions, depending on the retry strategy for the smart FET. 
 
Knowing how the smart FET's will behave under overcurrent conditions, whether it will completely shut off the current or 
clamp it off at a certain level, is important in determining the impact on the vehicle.  If the residual current is at a sufficiently 
high level, it can draw down the battery's state of charge.  If this current persists when the vehicle is off (i.e., there's a short on 
a hot-at-all-times circuit), it could conceivably drain the battery to the point where the vehicle won't start.  This would violate 
Trustmark requirement PS-0555, which says that the vehicle must start after sitting for 31 days with no more than a 25% loss 
of charge.  Then again, if the FET's shut down completely after sensing an overcurrent (i.e., they behave more like a fuse or 
circuit breaker), then there is no risk of a drained battery. 
 
Efforts are currently (February 2003) under way to determine the need for new requirements governing smart FET behavior.  
New requirements are likely, though their content and ownership is not yet known. 
 
How to avoid discharged batteries – here are some potential solutions: 
• 
Restrict smart FET's from being used on B+ feeds; use them only on run/accessory or battery-saver feeds.  
This would reduce (battery-saver) or eliminate (run/accessory) the key-off loads.  However, during vehicle operation, 
the battery's state of charge would still be drawn down, resulting in longer times to recharge, or possibly to a slowly 
degrading state of charge, if the recharge time is insufficient.  Also, this restriction would limit the usefulness of 
smart FET's, as systems needing key-off power and not on battery-saver could not use them. 
• 
Have the SJB detect the overload and shut it off after a given period.  This would need to be done by the SJB 
engineering activity. 
• 
Warn the driver of a clamped overload situation.  This, too, would need to be done by the SJB owners.  While 
this would not necessarily avoid discharged batteries, it would alert the driver that something is wrong, that the 
vehicle needed to be serviced for an electrical problem. 
 
FUSE PACKAGING 
 
With rare exceptions, all fuses are packaged in fuse panels.  One is typically located in the instrument panel; the other is found 
underhood, where it is normally called a power distribution box (PDB).  Sometimes, there are rear-mounted PDB's, especially 
when there is a rear-mounted battery.  PDB's will have regular fuses, high current fuses, and relays. 
 
When locating fuses and relays in fuse panels or PDB's, the heat distribution must be equalized.  Remember, these fuses and 
relays are carrying different amounts of current at different duty cycles.  Clustering high-current and/or high-duty cycle 
devices together will cause hot spots.  To avoid this, stagger the different kinds of loads.  Don't cluster the steady-state 
devices together; interleave them with intermittent-load devices.  Don't cluster the higher-current fuses together (like the 30 A 
fuses); intermix them with lower current ones.  Refer to component validation studies for more information.


### 第 42 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 42 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
  
7. 
DETERMINE THE ROUTING, OPTIMUM PARTITIONING OF CONNECTORS 
 
A. 
PIA (Purchase In Assembly) Wiring 
 
Can any wiring go PIA other major assemblies?  That is, would it be worthwhile for the harness to be installed in a major 
assembly (the engine, instrument panel, door panel, etc.), and then shipped to the assembly plant?  There are many reasons for 
doing this: 
 
 
Reduces end-item complexity at assembly plant 
 
Some components can't be put on easily at plant (such as fuel injector harnesses or instrument panel wiring [on main 
line as opposed to an I/P build-up line]) 
 
Can reduce costs due to labor savings. 
 
B. 
Optimum Partitioning of Connectors 
 
Which connections go into what harnesses?  For some connections (say the headlamps), the answer is obvious (14290).  The 
situation gets more complicated when a major subsystem's connections are divided among 2 or more harnesses.  Take, for 
example, the engine control subsystem.  The components that comprise the subsystem plug into one of several harnesses -- 
12A581, 9D930, 12A690, etc. Determining which harnesses handle what components can be difficult.  Things to consider 
when developing the optimum grouping include: 
 
Assembly issues -- what's the easiest way to make a connection?  On what harness should that connection be? (determined 
with the help and concurrence of V.O. and Design Aid buck reviews) 
 
Cost issues -- same as for PIA wiring:  complexity reduction, labor savings, etc. 
 
Manufacturing/Test issues -- In the case of the engine, it frequently is beneficial to have as many connections as possible be 
on the engine harness.  This allows for a better test procedure (no slave test harnesses), a dynamic test of the engine control 
components, and a better proveout of the actual wiring. 
 
C. 
Routing of harnesses 
 
With vehicles continuing to become more complex, the packaging and routing of the wiring has become more important than 
ever.  Here are a few things to consider when developing the routing: 
 
KEEP IT SIMPLE.  Harness routing should be simple and direct, avoiding confusing routing and mis-routings. 
 
Make the assembly sequence foolproof.  Design the component so that there is only one way it can be assembled into the 
vehicle.  If the operator can install the component in multiple positions, he/she will do so.  Some of these positions can cause 
problems.  Example: if the eyelet on a cable is installed in one position, the cable routing is free and clear of other 
components.  But if the eyelet can be installed at a different angle, the cable routing changes and is now forced into contact 
with another component that can chafe the cable. 
 
Refer to the Wire Harness Packaging Web site (http://www.ctis.ford.com/ppckb/cpscwire.htm, Ford internal), which is one of 
the manufacturing design rule manuals in the Product and Process Compatibility site. 
 
MAKE SURE THAT DESIGN AID BUCKS, PHYSICAL AND DIGITAL, ARE TRULY REPRESENTATIVE.  It is not 
your responsibility to maintain the entire buck (just your wiring), but you should know how representative of production the 
buck actually is.  If the components are not in their proper places, any routing you develop is useless.  Takeouts can end up on 
the opposite side of the vehicle from where they should be; a clear run on the buck may be heavily interfered with in the 
actual vehicle, and so forth.  If you have any doubts, talk with your vehicle engineer or the part engineers.  Ask to see 
drawings or CAD information, if necessary. 
 


### 第 43 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 43 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
MAKE SURE PACKAGE LAYOUTS, PHYSICAL AND ELECTRONIC, ARE REPRESENTATIVE.  Traditionally, wire 
routing has been developed on bucks rather than by formal design studies. This has meant that the wire routing either does not 
get into the package drawings or, if it does get into them, is not representative. This is no longer acceptable. The physical wire 
routing must be transmitted to the proper designers. This is made difficult by the EDGS system, which does not contain a true 
physical representation of the harness. However, the C3P system will enable one to design the physical harness layout in a 3D 
environment and link it to the electrical content generated by other tools. 
 
RESPECT EMC CONSIDERATIONS.  Take into account all EMC considerations contained in the WCR, subsystem design 
specifications (SDS) and other such documents.  These include the ignition system, PCM, electronic instruments, alternator, 
radio, etc.  In particular, try to keep at least the minimum specified distance away from RFI/EMI sources.  Distance is an 
inexpensive but effective way to reduce/eliminate EMC issues. 
 
An excellent reference for EMC requirements can be found on the EESE Web site (http://www_eese.ford.com/, Ford 
internal only).  Among the documents here are the Total Vehicle EMC Specifications and Design Requirements, AV-EESE-
EMC-03-500. 
 
PROTECT TAKEOUTS.  Diaper wrap takeout points to eliminate the possibility of exposed wires which could be pinched.  
Specify on your harness drawings that a minimum of three (3) full wraps are to be used. 
 
All connections must be positively located. 
 
USE SECTIONS LIBERALLY.  Wiring is designed in three dimensions but built in two, so the takeout orientation may need 
to change.  Control the takeout orientation in the design stage through the use of sections on the print to avoid short circuits. 
Explicitly show how a takeout is to branch from the main trunk, and specify retention (50-100 mm away) to force the takeout 
to be routed properly.  This has several advantages: 
 
Manufacturing 
The manufacturer no longer has to guess at design intent; orientation is now well-defined, so it can be 
well-controlled. 
 
Assembly 
When the harness is laid in the vehicle, the takeouts and retainers will be oriented in the correct directions.  
The harness will fall into place, making it easier to assemble; little, if any, twisting or severe bending will be 
required.  This is true in theory but not always true in practice. 
 
Durability 
Takeouts can be oriented away from sharp edges, corners, bolts or other potential sources of damage. 
 
ROUTE "NATURALLY".  Utilize natural routing provisions in sheet metal (mandated by the EDS SDS) whenever feasible.  
Design and use troughs and depressions that do not require covers, when feasible. 
 
TAILOR THE LENGTHS.  Ensure that wire lengths are long enough and that prints are dimensioned properly (not allowing 
tolerance stack-ups -- refer to ES-F65B-14A121-AA spec for more information) to promote full, complete connections. 
 
Tailoring of wire lengths is especially important at fuse panels, PDB's, junction blocks, bulkhead connectors, etc.  Due to 
harness design standards and the nature of these components, precise wire lengths for each cut lead cannot be specified and 
must be developed by the supplier.  Any excess length must be kept away from attachment points or from any situation where 
they could be trapped.  Proper quality control at the supplier can eliminate this risk.  Add a note to the print saying no excess 
length loops are allowed in this area. 
 
PROVIDE SUFFICIENT PROTECTION when routing near sharp edges, screws, fasteners, or other surfaces and when 
routing in areas subject to substantial vibration, such as engine roll (see the section on wire protection for more).  Minimize 
friction's effects. 
 
Do not route wires near weld points or weld flash.  The EDS SDS calls for a minimum of 15 mm clearance to any sheet metal 
welds under static and dynamic conditions (requirement ED-0005, for taped-only wiring).  However, it is best to avoid routing 
near weld points or weld flash at all.  Weld flash is dependent on the size of the weld -- the larger the weld, the greater the 


### 第 44 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 44 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
likelihood of flash, and the greater the likelihood that that flash will be large.  Not even convolute can protect wires from a 
sufficiently large piece of weld flash. 
 
Make sure that wire routing in the luggage compartment is not in proximity to the spare tire and jack to preclude any damage. 
 
Ensure that all wiring is routed and retained in such a way as to prevent damage by power tools and other components to be 
installed after the harness has been installed (partially or fully) in the vehicle. 
 
In general, the distance between retention points for wiring not contained in a rigid shield should be less than 300 mm.  For 
long straight runs in fairly safe areas, this distance can be increased (from former SDS requirement ED-0007). 
 
Make sure that the routing gives adequate clearance to nearby components, lines, tubes, etc.  In general, maintain a minimum 
clearance of 6 mm to all adjacent components unrelated to the EDS, unless the wiring is retained to those components.  
Maintain the same clearance from moving components where there is a barrier between the moving component and the wiring 
(from SDS requirement ED-0006).  Keep 19 mm or more away from moving components under maximum tolerance 
conditions (from SDS requirement ED-0002).   
 
In the case of fluid lines, keep at least 25 mm away (from SDS requirement ED-0118).  In fact, you may want to increase that 
distance, as both wiring and fluid lines can flex, which can lead to interference/contact, chafing, and a potential short circuit.  
Many of the fluids will burn if ignited by a spark from a short, so the best advice is to stay away from them. 
 
Keep clear of braided ground straps, as the braid can chafe through wires. Treat them like any other object and maintain the 
appropriate clearance distance.  If this is not possible, then encourage the owner of the ground strap to put protective 
shielding on the strap.  It may only need to be a localized shield rather than one that runs the full length of the strap.  
Typically, braided ground straps are not released by EDS.  If you ever do release one, then make sure it's shielded so that it 
can't chafe any nearby objects. 
 
Keep at least 100 mm away from normal customer maintenance items (dipsticks, fluid filler tubes, etc.).  This ensures free 
access to the maintenance items, and it prevents customers from disturbing the wiring or its components when the 
maintenance items are serviced (from former SDS requirement ED-0013). 
 
The EDS department recommends maintaining a minimum 25 mm clearance from the sharp edges of the parking brake 
assembly and a minimum 35 mm clearance from all moving parts of the parking brake assembly.  If these clearances can't be 
met, protect the wires with convolute or other appropriate means, or call for a shield over the parking brake assembly. 
 
Flange or roll wire routing and grommet hole edges in the direction of wire routing so that the wires can pass through 
unchafed.  In areas where wire bundles cross sheet metal edges, make certain that those edges are rolled away from the 
wiring.  Note that this won't be possible where the wire bundle passes through the hole obliquely rather than perpendicularly, 
as the edges can't be rolled in two different directions.  In that case, stay away from the edges, place additional protection on 
the edges or on the harness bundle, or use a grommet in the hole.  Note #2: This advice about rolling and flanging hole edges 
is based on the holes being stamped or punched; it may not be totally applicable to laser-cut holes. 
 
This may be stating the obvious, but if any portion of the EDS is exposed to stone impacts (tire kick-ups, etc.), the wiring 
must be physically protected so that it shall remain functionally operative (from former SDS requirement ED-0030). 
 
Taping over of convolute:  It is no longer necessary to tape over all convolute on the engine as a matter of course.  Use your 
engineering judgment to determine where convolute should be taped, mainly to prevent wires from splaying out of the tubing. 
 Note that program direction (underhood beautification efforts, etc.) may still dictate that on-engine wires be fully taped over; 
this is left to the discretion of the program team. 
 
STRAIN RELIEF.  When you are connecting or retaining to components which move, you must provide adequate strain relief 
to protect the connection.  Otherwise, the connector could disengage, seals could be deformed and compromised, wires could 
break, etc.  The movement can also be transmitted to the terminal interfaces; the induced micro-motion between the terminals 
can cause fretting corrosion.  Strain relief ensures that the relieving device (frequently a strategically placed locator) will see 
the strain rather than the connector or the terminal interface. 


### 第 45 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 45 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
Takeouts must not be designed to be so tight (under all tolerance conditions) that they pull on the components to which they 
connect.  This puts an axial load on the component.  In addition to deformed connector seals, the component itself can be 
pulled out of alignment or even be damaged (example: vehicle speed sensor shaft breakage).  This can be avoided by 
designing some slack (at least 25 mm at minimum wire length conditions) into the takeout.  But don't add too much slack, or 
the wire will be loose and liable to be trapped. 
 
Be careful when routing low energy circuits.  Make sure they are strain-relieved before they enter the connector.  This strain-
relief point must be vibrating in unison with the connector.  This will prevent relative motion of the harness terminal to the 
device terminal; such motion is known to erode and wear through terminal contact platings, exposing the substrate (this can 
happen no matter what energy level the circuit has).  There should be enough slack between the device strain relief point and 
other circuit retention points to allow for engine roll and other vibration. 
 
SERVICE CONSIDERATIONS.  Provide sufficient wire length to facilitate bulb servicing (headlamps, parking lamps, 
radios, instrument clusters, etc.) 
 
PROTECT AGAINST MISROUTING.  If a wire can be misrouted, it will be. Project where the wire is likely to be misrouted 
and provide additional protection, if necessary. 
 
Make sure that variation in components and sheet metal does not prevent successful routing of the harness. Follow the 
guidelines in the section MAKE SURE THE CONNECTORS ARE "CONNECTION-CAPABLE." 
 
FORCE A PARTICULAR ROUTING.  Specify adequate connector and wiring retention to ensure repeatable build sequences 
to avoid trapped or pinched wires. 
 
For the engine:  Wires should go onto the engine parallel to the centerline of the engine crankshaft whenever feasible – FWD 
vehicles from the side, RWD vehicles from the rear.  This reduces the likelihood of relative motion issues, which are made 
worse by routing perpendicular to the crankshaft. 
 
When processing a notice that affects PIA engine wiring, follow the Engine Wiring Packaging Process and Sign-Off 
Procedure (available at the EDS Clearinghouse Web site). 
 
Consider routing wire bundles between the fender inner and outer panels when feasible.  This may be necessary when engine 
compartment space is at a premium.  Extra protection for the bundles may be necessary.  Make sure that the fender liner does 
its job. 
 
In order to avoid attack and corrosion by battery acid spillage or outgassing, as well as the wiring warranty which will result, 
do not route wires under or near battery tray.  The EDS SDS calls for 25 mm clearance to the battery (excluding the 
connections to the battery itself, of course).  Note that this will be difficult to achieve in the engine compartment, given the 
tight packaging underhood. 
 
For information on battery cable retention, see the Special Cases section under Determine, Position Retaining Devices. 
 
Pre-form engine wiring to prevent takeouts straying into moving parts or high temperature components.  Consider using 
convolute fittings, tees, and elbows. 
 
The TASE Website (http://www-tase.ford.com/, Ford internal) has some guidance on packaging near high-heat areas.  It 
contains, among other things, a utility for estimating temperatures based on distance from a heat source.  The utility takes into 
account radiation heating and simple convection heating but doesn't consider forced air heating or cooling.  Still, it provides 
basic guidance on how far away we should stay from heat sources, in lieu of using special shielding. 
 
Obtain the surface temperature for wiring assemblies which are close to known high-heat areas (exhaust manifolds, EGR 
tubes, and other "Hot Spots") for the specific vehicle/engine line you are working on.  Route away from known high-heat 
areas (temperatures greater than 300 ºF or 149 °C) – suggested clearance is 250 mm. 
 


### 第 46 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 46 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Assist the Heat Protection engineers in locating thermocouples in areas of potential "hot spots." 
 
Provide routing through the grille for in-block heater cords whenever feasible (and when a grille exists).  If not feasible, 
provide enough length so the customer can bring it out.  NOTE: for luxury cars, Wixom Assembly Plant requires routing 
through the grille. 
 
For the I/P and console area:  Provide positive routing or pre-forms to steering column component wiring to prevent damage 
during shroud installation or by moving column parts (tilt/telescoping wheel). 
 
If the 14401 routes through the dash panel and into the engine compartment, then the engine compartment leads should be 
minimzed in length to prevent damage during routing through dash panel.  Engine compartment wiring should meet 14401 at 
or close to the dash.  Bulkhead connectors should be utilized whenever possible, as they eliminate the routing through the 
dash. 
 
When pulling complex harnesses through the firewall, consider using some sort of wrap instead of tear tape to hold the 
takeouts and connectors together.  With a wrap, the harness is totally covered; connectors, wire loops, etc. cannot hang up on 
sharp edges, reducing the risk of damage.  The harness is easier to control, also.  The wrap can be made of any effective 
material, such as clear plastic shrinkwrap or some kind of fabric.  It can be reusable or disposable, based on engineering 
discretion. 
 
Where feasible, locate and retain 14401 interface connectors low to cowl sides to permit connection to be made after I/P 
decking.  This also improves repair capability. 
 
Give special attention to the routing and retention of takeouts connected AFTER the I/P is decked (stoplamp switch, clutch 
switch, parking brake switch, etc.). 
 
Pre-form or retain (control) I/P wiring at cowl sides to allow hush panel installation.  Use brackets to retain interface 
connectors, if necessary. 
 
The EDS team needs to monitor the instrument panel key life test, specifically how the wiring performs on the test.  Any 
chafes to bare copper need to be eliminated, as should significant insulation chafes.  Make sure that the wiring used on the 
panel(s) being tested is representative. 
 
Be alert about routing and packaging connectors in so-called "spill" areas (i.e., areas likely to get wet from spilled drinks).  
Connectors should be sealed in these areas, or they should be packaged elsewhere. 
 
Check the packaging and orientation of the cigar lighter and power point sockets such that coins can't fall into them, leading 
to short circuits. 
 
For the Body:  Door wiring should be on trim panel and/or outside door inner panel and not inside door -- pre-form at 
sensitive areas. 
 
During assembly, doors and liftgates are opened and closed 9 times on a typical trim line.  Door wiring can be damaged by 
this if not properly retained or stowed until it is to be installed.  Ensure that takeouts do not fall down or drag or become 
vulnerable to door slams.  This is true throughout the vehicle, as well; takeouts that hang down can be damaged, so they need 
to be properly stowed until needed. 
 
It's typical for wiring going from the body to the door or decklid or other closure to pass through a grommet.  Guidance on 
these closure grommets and routing through them can be found in the section on water leaks and grommets. 
 
If you have an unused male connector (one with male terminals), evaluate its location and orientation for debris-catching 
potential.  Under certain circumstances, debris can fall through gaps between components and find its way into open 
connectors.  For example, something might fall from the I/P and work its way into an unmated connector located in the cowl 
side. If the debris is metallic, such as a coin or paper clip, it could cause a short circuit.  There are several ways to avoid this: 
orient the connector so that it won't catch debris; release the mating half of the connector as a cap for blocking debris entry; 


### 第 47 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 47 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
use the other gender connector (the one with the female terminals), delete the unused connector; etc.  Each method has 
advantages and disadvantages – re-orienting the connector is perhaps the least costly, but it may hurt assembly feasibility; 
releasing the mating half as a cap adds piece cost and requires an additional operation at the plant to install needed caps (if 
released as end item) or remove unneeded ones (if released PIA wiring); deleting the unused connector would probably 
require a new level or levels of wiring, adding to program and plant complexity; the connector containing the female terminals 
is far less likely to catch debris, but it may not have provisions for accepting a locator. 
 
All wiring routed in the passenger compartment of vehicles intended primarily for passenger transportation must be hidden 
from the vehicle occupants' line of sight (from former SDS requirement ED-0073).  This is generally done; the only vehicles 
on which it isn't are commercial trucks. 
 
Floor pan shields must be fully enclosed or retained so that the wiring cannot roll out of them.  By using a combination of 
shields and sheet metal troughs, the routing path should be fully enclosed. See section 11 for more on shields. 
 
All power seat leads routed on the floor pan (crossmember) must be positively secured (mechanically fastened) to prevent 
damage during seat installation. 
 
When wire is routing near the edge of a trim piece (such as the angel wings near the heated backlite connections), the routing 
path is in special need of control.  Use anti-rotation retainers or shielding to force the routing path.  This way, the wire will 
not be trapped by the trim piece. 
 
Kick areas deserve special consideration – use robust connectors that resist abuse, keep takeouts away from the carpet, make 
sure there's sufficient strain relief on the takeouts, etc. 
 
For the Seat:  Wiring attached to under-seat environments must be protected with sleeving such as nylon convolute.  Seat 
wiring designs must be verified dynamically by adjusting the seat through all possible phases of operation (both 
occupant-loaded and unloaded) and examining for potential wiring damage. Testing is to be coordinated with the seat 
engineer; it may be possible to piggyback onto the seat cycling test(s).  NOTE:  pay close attention to floor mats, especially 
rear floor mats; make sure they don't rub and chafe the seat wiring. 
 
From a design standpoint, it is preferable to have additional levels of harnesses (increased complexity) to support power and 
manual seats.  This is to prevent issues caused by the corrosion of an unused, hot-at-all-times connector.  If this cannot be 
achieved, however, then the unused power seat connectors must be retained in the seat, off of the floor pan, away from water. 
 
Do not under any circumstances strap wires to the seat strainer. 
 
For the Heated Backlight and/or Glass-Mounted Antenna.  Use spiral cord to connect to heated backlight and/or antenna 
grids located on movable glass (liftgates, lift glass) to ensure low pull forces on the grid terminals.  This will keep the 
connectors from disengaging from the glass. 
 
For the Frame:  Whenever a wire is routed along the outside of the frame rail, consider a dual tape wrap to the harness: wrap 
the wire bundle in tape, apply convoluted tubing, then tape wrap the convolute.  The inner tape wrap keeps the wires 
confined, reducing the risk of their being chafed by the edges of the slit in the tubing, while the outer tape wrap blocks dirt, 
grit and sand from getting into the bundle and creating more opportunities for chafing. 
 
For wiring that is in or leads to connections in high-splash areas: Slit convolute can allow water to enter the tubing, where it 
can be channeled down to connectors and pool on the sealing surfaces.  It can even bypass the shields or boots on the back of 
the connectors by sneaking underneath them.  This defeats the intent of former requirement ED-0040 (now part of ED-0031), 
which requires connectors to be oriented and shielded so that doesn't happen.  To reduce this risk, consider fully taping over 
the convolute, using non-slit convolute, using convolute that can hold itself closed, etc. 
 
FOLLOW THE BEST-IN-CLASS APPROACH.  This is a list of practices to follow when striving to achieve B-I-C wiring: 
 
 
 
BEST-IN-CLASS WIRING GUIDELINES  
 


### 第 48 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 48 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
Achieve a Predictable, Controlled Routing  
 
"MAP OUT" THE ROUTING, keeping the harness and locators visible 
 
LIMIT ROUTING VARIABILITY by retaining connectors and securing harnesses frequently -- 6-12" 
 
TAILOR HARNESS LENGTH to limit routing deviation between locators 
 
AVOID LONG UNSUPPORTED SPANS between apron and engine 
 
SPECIFY THE LOCATION AND DIRECTION OF TAKEOUTS away from risk areas 
 
SECURE THE HARNESS AWAY FROM WELD POINTS or add harness protection (covered in SDS) 
 
MINIMIZE HARNESS STIFFNESS by minimizing bundle diameter 
 
 
Protect the Harness from Abrasion and Cut Through  
 
PROTECT ALL CIRCUITS ROUTING ON THE ENGINE 
 
EXTEND PROTECTION TO THE BACK OF THE CONNECTOR when connector is not directly retained 
 
ALLOW EXPOSED CIRCUITS ONLY WHEN THE ROUTING SURFACE OR DISTANCE IS GUARANTEED 
HARMLESS 
 
MINIMIZE THE GAP IN HARNESS PROTECTION at branch takeouts 
 
TAPE OVER SPLIT CONVOLUTE AT BEND POINTS to prevent escape of circuits 
 
 
Promote Complete Connections  
 
MINIMIZE THE NUMBER OF CONNECTIONS 
 
PACKAGE CONNECTIONS IN AN ACCESSIBLE AREA 
 
PACKAGE CONNECTIONS IN A VISIBLE AREA (DURING VEHICLE ASSEMBLY) 
 
PACKAGE CONNECTIONS AWAY FROM SHARP EDGES 
 
USE LOW MATING EFFORT, LOCKING CONNECTORS 
 
USE COLORS TO HIGHLIGHT CONNECTORS AND LOCATORS 


### 第 49 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 49 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
 
8. 
MAKE SURE THE CONNECTORS ARE "CONNECTION-CAPABLE." 
 
You've defined the connectors, their mounting locations, and the wire routing. Now you need to determine if each connector 
is "connection-capable."  That is, is the connection at high risk for failure due to disconnects in the assembly plant and/or in 
the field? 
 
In order to reduce the number of disconnects, ensure that each connection is either properly accessible or of the proper 
operation type, that the connector has a sufficiently low mating effort, and that the connection operation is ergonomically 
desirable. Accessibility, operation type and ergonomics are rated according to the ECAR (Electrical Connector Acceptability 
Rating), which has replaced the old ECCR (Electrical Connection Capability Rating) procedure developed in 1993.  The main 
difference is the inclusion of ergonomic factors such as the type of grip and the wrist posture.  
 
Instructions on how to evaluate connectors, which were previously included in this section, have been removed, as have been 
the ECCR flowcharts.  Those instructions and charts, which were for ECCR, have been made obsolete by the updated ECAR 
process.  The ECAR instructions are available in a more complete form in the ECAR User Guide and Reference Document.  
This document can be found on the Ford Web (search for "ECAR"), and it is also available on the EDS Clearinghouse CD-
ROM. 
 
HOW TO REDUCE CONNECTION RISK 
 
If the ECAR analysis indicates a problem connection, there are several steps you can take to reduce the connection risk. They 
include design revisions, process revisions, or a combination of the two. 
 
• 
Change connectors 
• 
Resequence the operation 
• 
Reposition the connector 
• 
Reroute the wire 
• 
Adjust the takeout length, etc. 


### 第 50 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 50 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
9. 
DETERMINE, POSITION RETAINING DEVICES 
 
One of the keys to the Best-In-Class approach is achieving a predictable, controlled routing.  Once the routing is defined, the 
wiring must be retained so that it stays in position and does not interfere with other components or with assembly processes. 
 
Types of Retainers 
 
The term "retainer" covers a wide range of parts, from plastic or metal brackets to clips that wrap around the wiring bundle.  
There are so many types of retainers available that there is no room to discuss them all in this guide.  Two important 
categories of retainers are: 
 
Tie Straps 
 
This includes such items as bundling straps and "chicken band" but does not include retainers where the strap is an integral 
part of the retainer.  Bundling straps are usually of a two-loop design; one loop attaches the strap to the harness (so the strap 
can come into the plant already on the harness), and the other attaches the harness to the desired component.  Chicken band 
has only one loop, so it must be installed as an end item at assembly time. It is also used as an interim fix, to provide better 
retention while permanent design revisions are being made. 
 
Vehicle Operations (VO) does not like using tie straps, as they are rather labor-intensive and operator-dependent.  Straps are 
to be used only as an interim containment action, not as a permanent corrective action. Use tie straps only if no 
alternative is available.  Again, this does not apply to retainers which have straps as an integral part used to attach the retainer 
to the wire bundle. 
 
"Christmas tree" locators 
 
So named because of the shape of the locating pin.  Several types are available:  straight-on, offset (relative to the wire 
bundle), piggyback (has a hole which accommodates other locators), strap-on (these are secured to the bundle with a strap 
rather than tape) and locators that snap into connectors, allowing them to be positively retained. The latest versions of these 
locators have tabs that allow tape to grip better, holding the locator on the bundle better. 
 
NOTE:  The "arrowhead" style of locator (a predecessor of the "Christmas tree" style) is not a preferred design.  It is a go/no-
go design, while "Christmas tree" (Xmas tree) locators allow partial engagement at a minimum. Its use is discouraged, not just 
within Electrical but for other components affecting Electrical as well.  PMT's and engineers should communicate this to other 
activities and VC's and encourage them to switch to "Christmas tree" style locators.  It's better to be assured of partial 
engagement than to risk no engagement at all.  In some instances, such as an inability to control hole size on the low end, 
"arrowhead" retainers may be necessary.  Confer with the retainer expert from the EDS Core activity before using any 
"arrowhead" retainers. 
 
Details on these and other retainers can be found in the Wiring Reference Library, the EDS Clearinghouse website and other 
sources. 
 
Deciding Where to Put Retention 
 
1. 
Obtain CAD 3D-model data or hardcopy drawings of the parts you wish to attach to (the instrument panel, a sheet 
metal panel, etc.). If you can work with a Design Aid buck (either physical or virtual), so much the better. 
 
2. 
Overlay the harness/mockup 3D-model data or the actual part onto the model/drawing, part, or buck. Remember, you 
cannot pick up points from a traditional, 2D harness hardcopy drawing and put them on another drawing because 
harness prints are not drawn to scale. You can get true points from the CAD data. 
 
3. 
Decide where retention and/or support are needed.  Consider the following: 
 
 
Size, weight of wire bundle 


### 第 51 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 51 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
Accessibility of holes during assembly; if it's too hard to install a locator, it won't be installed 
 
Clearance to other components 
 
4. 
Note where the retainers are on the harness, then transfer these dimensions to the print.  If modifications to the other 
part are needed (holes need to be put in, for example), ask the design engineer of that part to make them, giving 
him/her all necessary information.  Follow up to make sure that sheet metal changes have been or will be 
incorporated. 
 
5. 
Adjust dimensions as required.  Get feedback from the usual sources -- buck trials, prototype builds, etc.  (also see 
the guide section FEEDBACK ON YOUR DESIGNS) 
 
If your EDS retention points are in close proximity to other EDS retention points (say, two connectors or bundles retained 
side by side) or to non-EDS retention or attachment points, and if mis-installing the bundles or connectors would cause 
difficulties (say, a bundle would contact a sharp edge as a result), then you need to devise a way to avoid this situation.  Here 
are some actions to consider: 
 
• 
Use a bracket or shield to force the routing in a certain way 
• 
Make that bracket/shield PIA to the harness, if feasible, so that the orientation is already defined when the 
harness is installed at the assembly plant 
• 
Adjust bundle lengths so that crossing up the retention points does not cause difficulty 
• 
Use different retainers or retention schemes which prevent mis-installation of the retainers 
• 
Stagger retainers so that they can't be crossed up 
• 
Use retainers with different nib sizes (say, an 8.0 mm as opposed to the standard 6.5 mm) 
• 
Combine connectors so only one retention point is needed (if feasible) 
• 
Promote heavy use of visual aids at the assembly plant (do this only as an adjunct to other design actions) 
 
Harness retention points must not be able to move or rotate if doing so would put the retained harness in violation of 
clearance requirements or too close to moving parts, sharp edges, or high heat areas.  This generally rules out portable holes 
and tie-on straps in these areas. 
 
If locator dimensions need to be held to very tight tolerances, use spline, which is a hard plastic strip. 
 
Consider putting locator and retention points near sheet metal screw points.  The retainer, and more specifically the tape 
holding the retainer to the bundle, will keep the wires together.  They won't be able to splay out and possibly be trapped by 
the screws.  If this is not feasible or desirable, then you should take other steps to keep the wires from being trapped.  For 
instance, you can route the bundle away from the attachments. If you're candy-striping the bundle, you can specify a short 
length of full taping near the attachment in order to keep the wires in place. 
 
This may be stating the obvious, but do not put retention points where they will cause the wiring to interfere with other 
components or block air vents. 
 
Per former SDS requirement ED-1440 (now contained in details for ED-0002 and ED-0006), the failure or removal of any 
given retainer shall not cause the harness bundle to violate clearance requirements to other components, especially moving 
components.  This is to avoid damage to the wiring. 
 
Other Guidelines 
 
Here are some additional tips to consider on using fasteners, retainers, and locators: 
 
Use only approved standard parts. 
 
Fasteners should be an integral part of the wire assembly whenever possible. 
 


### 第 52 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 52 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
All new locators should be designed in accordance with ES-E1AB-14A163-AA and with a 6.5 mm diameter Christmas-tree 
feature only.  Designs with medium length are preferred. Consider using the F8VB-13A506-AA retainer, which was designed 
with the application of Taguchi methods (hence the name "Taguchi retainer"). 
 
The preferred hole size for "Christmas Tree" locators is 6.7 +.1/-0.2 mm (flat surface); the preferred slot size is 6 mm x 12 
mm.  For the F8VB retainer, the preferred size is 6.6 +/- 0.1 mm; note that it cannot be used in slotted holes. NOTE:  Holes 
punched in a flat surface, which is then formed and shaped, will require a larger hole.  Also, this size will differ from one 
material to another. 
 
Use CRLC steel for any metal fasteners less than 0.071" thick. 
 
Do not use pointed screws for attachments, and review that all screws used in the vicinity of your wiring are blunt 
ended. 
 
The use of drill point screws is forbidden, in order to prevent wiring from being shorted.  Trim components (including 
wiring shields) should use pushpin attachments instead of screws. 
 
Consider specifying slotted holes when two or more locators are used in a row to avoid tolerance stackup conditions.  This 
also aids operators in differentiating between wiring holes and non-wiring holes.  Be careful:  slotted holes do not adequately 
retain large wiring bundles.  Also, the F8VB-13A506-AA retainer can't be used in slotted holes due to its own slots. 
 
Ensure that holes and brackets for mounting/securing connectors are adequately visible and accessible to enable full retention 
of connectors. 
 
Do not put locators in areas where possible sealer contamination exists (ex.:  seat locators into plastic wiring shields). 
 
Ensure that harness brackets (end items) can be installed properly without interfering with other processes. 
 
If metal brackets are to be crimped onto the wiring, the crimp dimensions (height and width) should be specified as significant 
characteristics on the harness drawing.  This crimp must be controlled to avoid damaging the cable. 
 
Retainers used on B+ feeds passing near moving items, especially the battery cable near the engine accessory drive 
components (alternators, A/C compressors, the belt, etc.), need to be declared significant or critical characteristics if failure to 
install them would cause the wire to contact these components.  Work closely with V.O. systems engineers and assembly plant 
personnel to make sure that the retainers are installable at full line speed. 
 
Ensure proper tool clearance to install harness/connector mounting brackets. 
 
Ensure that all harness locators/retainers are visible and accessible to enable all locators/retainers to be fully installed into 
retaining holes or mounting positions. 
 
Ensure that sheet metal or component assembly variations do not prevent installation of locators/retainers. 
 
Ensure that sheet metal holes are punched in the correct direction, which is from the side that the locator installation takes 
place. 
 
Consider using noon-black tape on both sides of locators if requested by Vehicle Ops so that the operator is less likely to 
overlook installation.  This will work only for locators not visible to the customer. 
 
Special Cases 
 
Coax connectors:  The standard connectors on coaxial cables are metal, which means that the shield portion can be grounded 
if it contacts sheet metal.  In several, perhaps most cases, this is desired and/or required for proper EMC shielding.  But when 
such contact is not desired, make sure that the connectors can't accidentally contact sheet metal.  Use standard retention 


### 第 53 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 53 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
techniques to assure this.  If necessary, have the owner of the coaxial cable provide an insulating boot or a non-conductive 
connector, but keep in mind these will incur cost penalties. 
 
 
10. 
SPLICING 
 
A. 
Determining the best location 
 
If subsystem guidelines mandate a specific location for a particular splice, use that location.  This requirement takes 
precedence over all other splice location guidelines. 
 
Splices involving HEGO circuits are not to be placed in wet areas.  They should be located inside the vehicle. If this is not 
possible, then the splices must be located in areas that do not get continuously splashed or doused. The sealing capability of 
these splices needs to be continually monitored by the supplier.  Consider making this a significant characteristic (SC's are 
covered later in this document). 
 
Try to balance the splice.  The best splice is one which has the same number of wires on both sides of the splice.  Try to 
position the splice so that it is as balanced as possible.  NOTE:  The practice of folding wires in a splice back to improve the 
balance ratio (for example, making a 5-to-3 splice into a 4-to-4 one) is not recommended and should not be used except as a 
last resort.  This is because of the many variables involved.  Exceptions must be reviewed by Ford on a case-by-case basis. 
 
The Electrical Distribution SDS does not mandate specific splice spacings.  However, the EDS core section suggests keeping 
splices 50 mm from takeouts and spacing splices a minimum of 100 mm apart when practical.  Keeping splices at least 50 mm 
from takeouts protects against wire breakage caused by the stresses on the takeout. 
 
Do not splice within 1 meter of a circuit entering a submergible connector unless the splice is hermetically sealed. 
 
Don't put splices too close to closure grommets (grommets going to doors, decklids, etc.).  Keep them at least 50 or 75 mm 
away from the grommet so that they aren't flexed to the point of breakage. 
 
Evaluate the location for moisture retention.  If there is a high probability of moisture retention, relocate the splice or seal it 
with heat shrink tubing. 
 
Splices should not be placed in an area where they will be continuously splashed or doused. There is no point in stressing the 
splice seal more than is absolutely necessary. 
 
B. 
Protecting the splice 
 
Splices are to be sealed in accordance with SDS requirement ED-0071.  If other systems mandate a sealed splice, or if the 
splice can get wet under normal circumstances or is in a high-corrosion area, then the splice is to be sealed.  This applies no 
matter what priority the circuits have (A, B or C, critical or non-critical).  There are three ways to hermetically seal splices: 
 
Heat Shrinkable Tubing (Heat Shrink)  Spec. ESB-M99D56-A2 
Tubing available in various diameters for different splice sizes and configurations.  When shrunken, forms a small, flexible 
hermetic seal. 
 
Mastic Patch -- Spec. ESB-M4G312-A 
A gummy tape which is also heat shrinkable.  Useful when the splice cannot be sealed by heat shrink.  Does not have to be 
heat-shrank in order to provide an effective seal.  This material is rarely used nowadays. 
 
Spec WSB-M4G352-A 
This is the newest sealing material. 
 
Older methods (tape, PVC mold, etc.) do not provide a hermetic seal and are not recommended.  However, they may be used 
in non-hostile environments.  NOTE:  this discussion also applies to eyelet crimps. 


### 第 54 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 54 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
Sealed splices must conform to ES-F0EB-1A263-AA using any of these methods to seal splices. NOTE TO 
MANUFACTURERS: process control is critical during the manufacturing of the splice seal. Failure to maintain control can 
lead to splices that are not sealed.  A useful reference on splice sealing is the Ford of Europe spec S91AG-14488-AA. 
 
Heat shrink tubing is also to be used for sealing resistors, diodes, and other such components in the wire assembly.  Besides 
providing protection from moisture, the tubing will stiffen the component, increasing its resistance to breakage during 
handling. (NOTE:  in-line components such as these should not be incorporated into the wiring unless absolutely necessary) 
 
Splice balancing is more critical with heat shrink than in the past.  If the splice is extremely unbalanced (more circuits on one 
side than the other), heat shrink will not seal it.  Similarly, if there are too many cut leads, the splice can't be sealed.  
Guidelines for usage are available for heat shrink and mastic patch; they can be found in the Reference Library. 
 
If splice sealing is required, use only those splice configurations which have a demonstrated process capability.  Exceptions 
must be approved by the EDS core technology group. 
 
C. 
Other guidelines 
 
Evaluate the use of double terminations instead of splices where practical.  However, keep supplier capability in mind when 
considering a multiple termination, as some suppliers prefer not to do them. 
 
Welding of 20 gage or smaller wires is not authorized unless a supplier control plan has been approved by Ford Engineering.  
Use a splice clip.  Welding of wires with significant differences in wire sizes may result in unsatisfactory welds. 
 
Seal any splices in the air bag firing circuit, no matter where the splices are located in the vehicle. 
 
Do not splice clip any splices containing 10 ga. or larger wire; they are welded only (EXCEPTION:  the splice contains 20 ga. 
or smaller wire -- this can be either welded or clipped). 
 
Low energy circuit splices are to be welded only. 
 
Use splice clips for resistance wire, diodes, resistors and capacitors to avoid heat damage that would be encountered if they 
were welded.  (NOTE:  in-line components such as these should not be incorporated into the wiring unless absolutely 
necessary) 
 
JUNCTION BLOCKS 
 
Junction blocks are a method for "splicing" wires together, a method which eliminates most, if not all, of the need for splices. 
 The block is a printed circuit board which can accommodate several connectors. These connectors hold the circuits which are 
to be "spliced" together. When plugged into the block, the wires are joined by the paths on the circuit board.  A junction block 
can replace several splices, thus avoiding the problems associated with splicing like sealing, wicking, crosstalk, increased 
bundle size, etc.  They promote increased automation of the harness manufacturing process in such areas as connector stuffing 
and subassembly buildup.  It is possible to add options by using a differently configured junction block (the wiring already 
accommodates the options), thus reducing wiring complexity. 
 


### 第 55 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 55 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
11. 
GROUNDING 
 
The guidance in this section applies to steel bodies only and do not necessarily apply to aluminum body programs. 
 
A. 
Standards for grounding 
 
The standard for grounding is the generic grounding strategy, developed by EESE.  It specifies the basic ground partitioning 
of the vehicle, classifies grounds based on their characteristics (clean vs. noisy, high-current vs. low-current, etc.), specifies 
grounding methods based on those characteristics (to a weldnut in sheet metal, to a special clean ground, back to the battery, 
etc.), specifies attachment methods, etc.  Vehicle teams will take the generic strategy and adapt it to the needs of their 
programs. 
 
Sneak Paths 
 
Grounding architectures must be designed such that sneak paths (unintended, alternate circuit connections) to ground do not 
occur. For example, if the blower motor ground is disconnected, the blower motor should not be able to sense ground via 
another path, such as through a control module.  If there is a sneak path, the components that are part of it may not have been 
designed to carry those kinds of currents and are likely to be damaged. This can be true even for non-electrical but conductive 
parts, such as throttle cables.  Similarly, revisions to the architecture must not cause sneak paths to develop. 
 
Pigtail and component wiring engineers need to check with the electrical PMT before altering their grounding schemes or 
strategies so that sneak path issues can be avoided. 
 
Ground attachments should be under our control, even if we may be using someone else's attachment.  We should specify the 
finish, making sure that it's electrically compatible, and specify the torque and anything else that's necessary.  This may lead to 
our attachments being unique, not being shared with other components.  It's best to make key attachments be critical or 
inverted-delta items, which requires certain levels of torque monitoring by the assembly plant. 
 
B. 
Grounding to sheet metal 
 
NOTE:  The preferred grounding method is currently being reviewed.  Changes are anticipated within the next few months.  
Both the EDS SDS and this design guide will be revised to reflect those changes. 
 
Per SDS requirement ED-0052, ground connections are to use weldnuts.  The requirement used to mandate threadless 
weldnuts, but this has been revised to permit both threaded and threadless weldnuts.  Both threaded and threadless weldnuts, 
when used with the appropriate fasteners, provide a good electrical ground. 
 
Here are the preferred part numbers for the threaded weldnut and its bolt (as used in Europe): 
• 
Weldnut: W520720-S 
• 
Bolt: 98AG-10N006-AA (torque 10.5 +/- 1.6 Nm) 
 
Here are the preferred part numbers for the threadless weldnut method: 
• 
Weldnut: W520712-S (S300) (replaced N806285-S) (use a sheet metal clearance hole of 9.0 +/- 0.4 mm) 
• 
Bolts (self-tapping, thread-forming): 
o Floating washer (for terminals without anti-rotation features): W706287-S900 (standard length), W705572-
S900 (20 mm longer) (torque 9.0 +/- 1.4 Nm) 
o Non-floating washer (for terminals with anti-rotation features): W706281-S900 (torque 12.0 +/- 1.8 Nm) 
 
“S900”, as called out on the bolt prints, refers to finish spec WSE-M1P93-A (yellow).  Torque values for the bolts for 
threadless weldnuts have been recommended by the Global Core Fastener Office. 
 
All studs (present and future) must have positive hex drive features so that installation torque can be controlled. 
 


### 第 56 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 56 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
This may be stating the obvious, but any fastener that's used for grounds must have a conductive finish.  Otherwise, it will 
break the ground path. 
 
Information on the old "green ground screw" that used to reside here has been removed, for the S36 finish has been banned 
because it contains hexavalent chromium. 
 
Drill-point screws are not to be used for any ground attachments. 
 
Do not ground to moving structures (doors, decklids, tailgates, etc.) as the ground return path through the hinges is not 
reliable (Teflon bushings disrupt the path). 
 
Do not use drill-at-assembly holes for ground attachments on a production basis. 
 
For steel-body applications: Do not ground to nonferrous metals.  Use of a nut on a stud or a bolt in aluminum castings is an 
exception. 
 
Refer to the guideline supplement for ground screw hole design requirements (available in the EDS library). 
 
See the section on Eyelets elsewhere in this guide for guidance on eyelets and ring terminals used for grounding. 
 
Per SDS requirement ED-0053, do not place more than 2 eyelet terminals under a single ground screw.  This comes from 
long-standing design practice about eyelet stacking (the gaps between eyelets reduce clamp load) and spinning (the uppermost 
eyelets are more likely to spin when they are torqued, as anti-rotation features won't be as effective).  Also, assembly 
operators are more likely to miss installing an eyelet when more than two are involved.  This applies to interlocking terminal 
systems as well, though ED-0053 permits interlocking terminals to be considered as either one eyelet or multiple eyelets, 
based on consultation with the connector group. 
 
Do not put any parts in between the ground eyelet(s) and the sheet metal or weldnut.  Testing has not been done for this 
condition (as of October 1997).  A plastic part, for sure, will lessen the clamp load and degrade the ground performance.  This 
also holds true when there's another layer of sheet metal above the layer that contains the weldnut.  This alters the joint 
characteristics and may invalidate our recommendations for fasteners and torques. 
 
Do not place ground attachments under component attaching screws. 
 
Make sure all ground locations are readily accessible for installation, service and verification. 
 
Do not place electrical component attachments or ground screws adjacent to vehicle fuel tanks or fuel lines. 
 
Do not place ground attachments in high-splash areas. 
 
When weld nuts are used in the vehicle interior, they are to be placed in dry areas to avoid water leaks. 
 
The PCM (EEC) module case should be grounded directly to sheet metal (not through a wire in the harness, although a wire 
ground is acceptable as a backup). 
 
Grounding accomplished directly through the mechanical tie down of the instrument panel to the body must be verified on all 
new vehicle lines.  The I/P fasteners must have a conductive finish so that the integrity of the ground path is established and 
maintained.  Ideally, the fasteners would be driven into weldnuts on the sheet metal. 


### 第 57 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 57 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
12. 
PROTECTING THE WIRES 
 
If you were to stop at this point, you would have a lot of wires running all over the place because they're not contained, at risk 
of being burned, melted, chafed, pinched, and abused in general.  They would not last long in the harsh environment of the 
vehicle, and neither would the components they plug into because of induced voltages, spikes and other EMC issues.  Clearly, 
the wires need to be protected, both electrically and mechanically/environmentally. 
 
A. 
Electrical Protection 
 
By properly routing wires away from noise-generating wires or components, you can reduce EMC concerns.  But sometimes 
you have no choice except to route near noisy wires.  And no routing will reduce noise generated by noisy components 
plugged into the harness.  In situations like these, additional protection has to be designed into the harness. 
 
Land Rover and Jaguar have developed some good design guidelines for minimizing EMC issues with wiring.  There is a link 
to this document on the EDS Clearinghouse, and it's also available on the Clearinghouse CD-ROM. 
 
Shielding (EMI) -- Wrap sensitive circuits in foil and ground one side of it.  This is done regularly for critical ignition 
circuits.  Consider shielding if you must route closer than is desirable to high-current or noisy circuits. 
 
Seal all splices in any wire assembly that uses bare coaxial shielding (braid or tape) for EMI suppression, and insulate or tape 
over all shielding ends that terminate near any open connectors.  This is to prevent splice and terminal shorts to the shielding. 
 
Minimize the length of conductors which extend beyond the shield. Failure to do this reduces the effectiveness of the shield. 
 
In order to be truly effective, electrical protection must be systemwide.  The sole burden of protection cannot be placed on the 
wiring; the modules must be shielded, too.  A discussion of module shielding techniques is, however, beyond the scope of this 
guide (also, not a Wiring responsibility). 
 
Twisted pairs -- An effective way to reduce magnetically induced noise is to twist sensitive, low-level leads together in pairs. 
 This causes the magnetic fields in each wire to cancel each other out.  However, twisted pairs add cost to the wiring, so any 
request for twisted pairs must be supported by test data. 
 
Per SDS requirement ED-0763, wires are to be twisted such that the pitch, or the distance between wire crossover points, is 
15 mm +/- 20% (a range from 12 to 18 mm per twist).  This pitch must be maintained to within 50 mm of the back of a device 
or inline connection (formerly ED-0764, now part of ED-0763).  The former twist rate of 33 to 55 twists/meter (equivalent to 
a pitch of 24 mm +/- 23%) can no longer guarantee the uniformity required by newer electrical systems, which have increased 
sensitivity to in-band noise.  The new pitch represents an increased twist rate, which could not be used in previous years due 
to the possibility of corrupting multiplex bus signals.  However, that multiplex system is being phased out in favor of more 
robust systems, thus allowing for a higher twist rate.  As before, twists must not unravel for more than 50 mm; otherwise, the 
effectiveness of the twisting is lost. If connector pinouts do not permit this, repin the connector. 
 
Spike suppression -- Several components, like the fuel pump and A/C clutch, are guilty of producing large voltage spikes as 
they are cycled on and off.  These components usually lack built-in spike suppression; that function has been historically left 
to the wiring, although the responsibility really belongs with the component engineer.  Strive to have suppression devices 
incorporated into components whenever possible, as they will be more reliable than harness-installed devices. 
 
In-line suppression devices may not be incorporated into wiring on a production basis unless they have an approved 
connection system, per SDS requirement ED-0025.  This is to avoid hiding the devices in the wiring bundles, where they can 
easily be damaged.  Suppression devices may be incorporated into wiring harnesses on a temporary basis only (not to exceed 
one model year); this would represent a deviation from the SDS. 
 
In general, spike suppression is accomplished by connecting a diode or resistor-diode combination across the terminals of the 
noisy component. The diode should be sufficiently close to the component (both electrically and physically) so that most 
inductive spikes are clamped off.  If possible, run or obtain from the responsible component engineer a transient electrical 


### 第 58 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 58 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
analysis on the system as the component turns off to confirm that any spike is sufficiently suppressed.  Make sure the diode is 
connected with the proper polarity. 
 
B. 
Mechanical/Environmental Protection 
 
Here, too, proper routing and retention will reduce the likelihood of chafing, pinching, and so forth.  But that routing is only 
the theoretical ideal; the actual routing, as done at the assembly plant, can and does differ substantially.  Additional protection 
is needed.  The historical amount of recommended protection is 95% coverage for frame, engine compartment and seat 
wiring, and it is 60% for all other areas, where coverage includes not only convoluted tubing or scroll but also sheet metal 
troughs, hard plastic shields, etc. (from former SDS requirement. ED-0001).  But don't use any unnecessary protection. 
 
Tape -- Tape contains the wires in a loose bundle and provides very limited environmental protection.  It does not protect 
against chafing and pinching. Tape is applied in three major styles: 
 
Spot tape:  Small pieces of tape are used to hold wire bundles together.  Also used to hold locators to the wiring trunk. 
 
Candy stripe:  Here, tape is applied loosely, in a continuous wrap, with wraps spaced 25 to 75 mm (1 to 3 in.) apart.  In terms 
of appearance, it is unacceptable, so it is done only in non-visible areas (underneath convolute or shields, carpet and trim 
panels). This provides more flexibility for the bundle than convolute or full taping. 
 
Full taping:  In this method, the tape is applied with a significant amount of overlap so that no wires show through.  This is 
superior to candy striping for appearance, but at the cost of reduced flexibility (which may be advantageous -- harness 
movement  is confined).  It contains the wires better -- it keeps them in their proper place (critical when screws are to be 
driven near wires).  If non-adhesive tape is used, the tails (ends) of the tape must be secured with an adhesive tape wrap.  This 
tape may be a standard tape tie, an ID tag, or any other appropriate tape. 
 
TYPES OF TAPE: 
 
All tape used in the vehicle must conform to performance specification ES-XU5T-1A303-AA.  This specification 
replaces any and all earlier tape specifications.  The older specification numbers have been removed but can be found in older 
versions of this guide.  If there is sufficient interest, there may be a cross-reference table showing the old specs and the 
replacements for them. 
 
Tear tape -- This tape is designed to tear away very easily, unlike the other tapes.  Its purpose is to temporarily hold takeouts 
and connectors against the main bundle until they are needed, either to prevent damage during other assembly operations or to 
allow the harness to be pushed through a hole in sheet metal.   
 
When tear tape is applied to electrical connectors, it must not interfere with their locking or mating features or otherwise 
degrade the mechanical and electrical performance of the connection system (from former SDS requirement EL-0047). 
 
Tape wrap -- While several tapes can be used to wrap wire bundles, "tape wrap" refers to a particular kind of tape.  The 
primary function of this tape is to wrap the wire bundle and keep wires from poking out and flopping around.  There are two 
kinds of tape wrap in use: non-adhesive (also called dry vinyl) and adhesive.   
 
Special note for tape wrap:  when tape wrap is specified to be applied all the way to the back of a connector, it should stop at 
a distance back from the connector corresponding to the maximum dimension between the used terminal cavities on that 
connector.  See the section on connectors and terminals for more information.  This will be addressed in SDS requirement 
ED-1475 (in development as of July 2003). 
 
Caution on dry vinyl tape:  Using dry vinyl tape to wrap wire bundles that are to be hot-glued to a headliner or other trim 
piece is not advisable.  If cut or damaged during the hot glue process, the tape can unravel and expose the circuits.  Use a tape 
with adhesive instead. 
 


### 第 59 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 59 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Woven tape -- Use this tape as a more rugged tape wrap; use it for extra protection of takeout points, for holding convolute in 
place and for holding locators on harness bundles. Again, it must be noted that this tape does not provide any significant 
cut-through or abrasion protection.  This was historically known as "cloth tape", as one version of it was made out of cloth. 
 
Sound-absorption or sound-deadening tape – as the name suggests, it's intended for use in areas where sound control is 
critical, such as eliminating squeaks and rattles.  Some have suggested using this type of tape for abrasion control in lieu of 
convolute or other shielding, but tests have found it unsuitable for that purpose – at least, not as a direct substitute for 
convolute or other coverings.  But could it be used in low-abrasion-risk areas?  Perhaps; some test results show that this tape 
can handle more abrasion test cycles than our other tapes, but nowhere near as many as convolute. More study will be 
required.  Until then, our guidance remains: tape, in any form, does not protect against chafing, abrasion and pinching. 
 
High heat tape ("Fiberglass tape")  -- Use this tape in high temperature (above 275 ºF) areas.  It's less expensive to use than a 
fiberglass sock, it's flame resistant, and it has a high breaking strength (120 lb) (comments are specific to historic specification 
ESB-M3G38-A) 
 
Convolute -- Use convolute when increased temperature and abrasion or pinch resistance is required.  Convoluted tubing 
comes in different diameters and materials to accommodate different temperature ranges and harness sizes.  The release 
specification is ES-XU5T-14A099-AA. 
 
Keep in mind that convolute, while providing increased abrasion resistance, will still wear through in an abrasive situation.  
It'll just take longer.  The key is to route and retain so that contact under normal circumstances is avoided.  Don't design in an 
area of contact and count on the convolute to protect the wire; over the long haul, it will fail.  It protects against inadvertent 
contact and pinching.  But abrasion resistance has not been and will not be quantified in the convolute performance 
specification. 
 
It is advantageous to have convolute butt up against the back of connectors for maximum protection of the wires, except 
where this would deform the wires or connector seals or cause terminal misalignment.  To assure this, specify on your harness 
drawings that the actual length of convolute used on a takeout be developed by the vendor (the decision to use this note is the 
engineer's).  If this note is used, the convolute will be undimensioned on the print. NOTE:  if butting the convolute against the 
back of the connectors will compress the wires and break the connector seals, then don't do it. 
 
Convolute must be used on all engine wiring except where flexibility is an issue.  The intent is that all engine wiring be 
protected in some form; Campaign Prevention will fault it if it's not there. 
 
If a wire bundle covered with slit convolute is bent too severely, or if the slit becomes excessively wide, the wires may loop 
out of the convolute, where they may be easily damaged.  The solution is to add tape wrap, either over the convolute itself or 
over the wires but under the convolute.  Places to consider adding tape include: 
 
 
Engine-mounted wiring 
 
Critical takeouts, such as air bag sensor takeouts and takeouts going to the engine 
 
Areas where the bundle can bend. 
 
Bear in mind, though, that as convolute ages, the slit can widen and allow wires to pop out, even in straight runs.  If this puts 
the wire in jeopardy of being cut or abraded, use candy striping or full taping, either over or under the convolute. 
 
It's not just the edges of the slits that can chafe the wires.  The cut ends of the tubing can also chafe them, especially if the 
takeout moves or vibrates such that the wires can repeatedly contact the tubing edges.  The convolute spec has an abrasion 
test, but it addresses single contacts during assembly, not multiple contacts during use.  To avoid this contact, spot tape the 
ends of the convolute to the wires, reposition the convolute so that the edges are away from a high wire-to-tubing relative 
motion area, use a different material for the tubing, or change to another type of tubing. 
 
Styles and types of taping include: 
 
Over Convolute:  Full taping  


### 第 60 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 60 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Over Wires:  Candy-striping with any suitable tape (depending on location) at a rate between 20 to 40 wraps per meter (exact 
rate to be specified by engineer; ranges are acceptable). 
 
Convolute junctions must be taped. 
 
Scroll -- Similar to convolute, but without the ridges.  Scroll is used where rigidity of the harness is required. 
 
Fiberglass sleeving -- This provides superior temperature resistance (over 350 ºF or 176 °C). Use when required to route 
close to extremely hot components, such as exhaust manifolds and EGR tubes. 
 
Woven nylon or fiberglass tubing -- The nylon tubing provides better abrasion resistance than fiberglass, but both are less 
resistant to high temperatures than fiberglass sleeving; use for environments less than 300 ºF (148 °C). 
 
Wiring shields (plastic or metal) -- Use these when the other methods of protection are insufficient, or when a rigid routing 
path is required.  Here are some things to consider: 
 
Sheet metal mounted wiring shields should cover the wiring 360 degrees and provide holes to accommodate RPO (regular 
production option) wiring. 
 
Use wire forms for harnesses when routing over or around crossmembers, etc., or when severe directional routings are 
required. 
 
Utilize PIA wiring shields whenever possible.  Design features to these shields to retain overlays, also. 
 
When a new or substantially modified shield is to be used on a vehicle, it should be flexed and bent to ensure there are no 
brittle or weak areas.  If any are found, take appropriate action (a design change or a manufacturing process change, as 
required). 
 
NOTE:  This is not meant to be an all-inclusive list of methods for physically protecting the wires.  Other means of protection 
include vinyl sheathes. 
 
Changing protection methods:  Any changes in harness protection, including those suggested in VA/VE proposals, which 
would either put a harness in violation of the SDS (especially clearance requirements) or keep it in violation must be proven 
out on durability testing.  It's recommended that all changes in harness protection be proven out on durability as well. 
 
Ground wires must be protected from abrasion.  You may ask, why? Isn't it impossible for them to short to ground? That 
is correct, but the wires must be protected for two reasons.  One, if the ground wire is cut totally, the ground is lost, and the 
system will act erratically or fail.  It could also reveal a hidden sneak path (i.e., unintended path) to ground, which can also 
cause erratic system operation. Two, water can get in between the insulation and the wire strands at the abrasion point, 
enabling it to wick into connectors or the interior of the vehicle.  More information on water leaks can be found in the section 
entitled PREVENT WATER LEAKS. 
 
C. 
Rules of thumb 
 
1. 
ANTICIPATE PROBLEMS AND DESIGN ACCORDINGLY.  The main thing to consider is Murphy's Law:  If 
anything can go wrong, it will.  Try to anticipate what can reasonably go wrong and modify your designs to minimize 
any adverse impact. 
 
2. 
ERR ON THE SIDE OF OVERPROTECTION.  If you have any doubts, assume the worst and protect against it. 
 
3. 
Make sure that B+ battery circuits are isolated in their own connectors in the engine compartment.  Use sealed 
connectors. 
 
4. 
Make sure that key-on circuits are isolated from ground potential circuits in connectors in the engine compartment.  
Don't put them adjacent to each other where they can be shorted by a salt bridge. Use sealed connectors. 


### 第 61 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 61 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 


### 第 62 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 62 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
 
13. 
PREVENT SQUEAKS AND RATTLES 
 
Squeaks and rattles can be very annoying to the customer, so it is imperative that you design your parts so that they will not 
rattle when properly assembled.  It is your responsibility to prevent design-caused squeaks and rattles at the early stages of the 
design process.  The earliest time where squeak and rattle prevention will be meaningful is at the Confirmation Prototype 
(CP) stage, where all parts are supposed to be at or very close to Job #1 intent.  Design-related squeak and rattle issues should 
be resolved by 1PP or earlier. 
 
Design Philosophies to Follow 
 
Positively retain the wiring every 150 to 250 mm (6 to 10 inches). 
 
All connectors must be positively retained.  In addition, per the SDS, all connectors that are given away through the 
complexity reduction program are to be positively retained against the main body of the wiring.  Use a "Christmas tree" 
locator plugged into a portable hole on the wiring trunk.  Do not tape the connector to the bundle. 
 
Use tapes which do not squeak against metal or plastic. 
 
Verification 
 
There are many ways to verify that your designs don't cause squeaks and rattles.  They include the following: 
 
Instrument panel bucks 
Full body bucks 
Engineering prototypes tested on the Burke/Porter or four-poster machines 
Testing at the assembly plant 
Fleet drive evaluation of units 
 
The verification program must involve the V.O. Electrical Systems engineer and the Squeak and Rattle engineer for your 
vehicle. 
 
 
14. 
PREVENT WATER LEAKS (AND OTHER GROMMET TIPS) 
 
Per the electrical distribution SDS, make sure that drip loops or other means are provided to prevent water leakage into the 
vehicle interior (passenger and luggage compartments) via wiring assemblies that pass from outside into the vehicle interior 
and to prevent water leakage into components packaged in exposed areas (i.e., engine compartment).  The drip loop or other 
sealing method must be capable of passing Vehicle Operations's 20-minute water test (a high-pressure spray, commonly called 
"soak") with no leakage. Submergible connectors should not be oriented vertically such that water might leak or be sucked 
inside the hardshell, passing through the seals (this is more of an issue with components that undergo a heating/cooling cycle). 
 Orient the connectors so they point sideways. 
 
The best way to prevent water leaks into the vehicle interior is to use bulkhead connectors at sheet metal crossings.  For a 
variety of reasons (package space, cost, the location of the pass-through, number of circuits, etc.), this is not always feasible, 
so grommet pass-throughs must be used instead. 
 
Two ways to reduce water leakage are drip loops and proper taping.  Drip loops (dips in the harness) trap water, forcing it 
below the level of the grommet where it can safely drip out.  Good taping techniques reduce the likelihood of water getting 
into the harness and allow it to drip out if it does get in.  Some of the techniques are diaper wrapping all takeouts, lapping the 
tape with a 50% overlap, etc.  If an effective drip loop is guaranteed, candy-stripe taping may permit water to drip out, 
providing a "siphon break."  These techniques are not 100% effective. 
 


### 第 63 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 63 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
When wiring passes from a door into the passenger compartment, it is best to design the transition such that the door portion 
is below the passenger compartment portion.  This creates a type of drip loop, where water that finds its way down the door 
wiring would not be able to rise up the transition and get into the passenger compartment.  Implementation of this requires the 
cooperation of the body-in-white and closures activities, so work with them as required. 
 
Conduct a 20-minute soak test functionally equivalent to the V.O. test, using 10 to 20 samples, to determine the need for 
additional sealing methods.  If there are no leaks, additional sealing is not required.  If there are leaks, use an appropriate 
bundle sealing material between the wires at the grommet.  In addition, conduct the tests specified in the new grommet sealing 
specification (availability TBD) 
 
One troublesome cause of water leaks has been water actually getting inside the insulation on one or more wires and wicking 
along the wire strands.  This allows water to enter sealed connectors and bypass drip loops.  Great care must be taken to 
prevent water from getting between the wire strands and the insulation.  This includes using only submergible connectors and 
sealing all splices in environmentally hostile areas.  If wires must be cut for any service operation, the cut end must be 
completely sealed if it is left in the vehicle and it is located in a hostile area.  Options for sealing eyelet terminations are 
discussed in the Eyelet section of this guide (under section 5, CHOOSE TERMINALS AND CONNECTORS). 
 
Additional grommet tips: 
 
From Engineering Design Standard DM 17.01-351, paragraph 5.1.3, dated August 14, 1981:  "Wiring may be routed through 
metal panels or castings when the hole edges are rolled or flanged in the same direction in which the wire is installed or when 
a protective grommet is used and hole edges in sheet metal are covered by an eyelet or punched in the same direction in which 
the wire is installed. 
 
Make sure that only approved grommets are used. 
 
Design new molded grommets using the design guide (in the Reference Library) whenever a new grommet is required. 
 
The use of 2-piece grommets (hard metal or plastic inserts with rubber outers) is recommended to facilitate installation and 
retention. 
 
Do not use non-round grommets unless forced to by packaging constraints. 
 
Locate grommets so they can be pulled to seat them.  Ensure they are accessible to assure full installation to the sheet metal 
hole. 
 
When bundles with grommets are seated, the operator will pull on the bundle, sometimes rather forcefully.  Secure the 
grommet to the harness bundle in such a way, such as longer taped areas of attachment, that the stress of the operator's pulling 
is taken into the grommet rather than transmitted to takeouts or connectors on the other side of the grommet.  This reduces the 
risk of connector/terminal damage and terminal pushouts.  It also keeps the length of the takeouts at design intent, rather than 
being shortened by the wire bundle sliding relative to the grommet.  This will be of most importance with smaller bundles 
combined with large grommets (example: door wiring with low content, but a grommet and hole designed for a high-content 
harness). 
 
Do not use grommets designed for punched holes in drill-at-assembly holes and vice versa. 
 
The direction of the hole punch should be in the direction of grommet seating. 
 
Make sure the grommet molding compound will adhere to the harness to prevent slippage.  Applying tape over the wire 
bundle before molding the grommet can improve retention. 
 
Make sure the grommet will withstand the environment, temperature, splash, etc. 
 
All vanity mirror wiring grommets at the header area should be hollow to provide ease of seating, stopping operators from 
pulling on the mirror connectors. 


### 第 64 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 64 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
Ensure that all holes are sufficiently large to allow the installation of the harness without causing circuit damage.  Holes 
should be large enough to allow for future growth. 
 
Use adhesive tape with at least a 50% overlap on any main trunk or branches that can have water runoff to prevent wicking 
through grommets.  Be certain to diaper-wrap the takeouts. 
 
Grommets going between the body and closures (doors, decklid, etc.) need to be a one-piece design with no hard inserts.  
Wires passing through the flexing area need to be loose, not taped or confined in any way.  Obviously, no splices are to be 
located inside the grommet; they'd never survive the flexing.  Don't put splices just outside of the grommets, either; locate 
them at least 50 to 75 mm away from the grommet.  This is to prevent wires from binding or being bent inside or just outside 
the grommet to such an extent that they'll break.  Verify the design with the appropriate cycle testing. 
 
Don't retain wires just outside of closure grommets or force them into sharp bends; this will cause strain on the wires and may 
cause them to break.  Put retention points or bends starting at 50 to 75 mm away from the grommets.  Again, verify the design 
with the appropriate cycle testing. 
 
GROMMETS VERSUS BULKHEAD CONNECTORS FOR WIRING PASS-THROUGHS 
 
There are two ways to pass wiring through sheet metal.  One way is to apply a grommet to the wiring to seal the hole in the 
sheet metal, leaving a wire bundle with one or more interface connectors on the other side of the grommet.  The other way is 
to use a large bulkhead connector at the sheet metal hole as the sole interface connector.  The connector itself seals the hole.  
Ford has used both approaches over the years.  Each has advantages and disadvantages. 
 
Advantages of grommets: 
 
• 
Fewer interfaces and interfacing terminals, leading to improved reliability (some takeouts can go right to components 
rather than through an additional interface) 
• 
Smaller sheet metal hole can be utilized, though size will depend on the connectors that are passed through 
 
Disadvantages of grommets: 
 
• 
Multiple interface connectors instead of one large one; these additional connectors are more difficult to package than 
one large connector 
• 
Bundles difficult to pull through sheet metal holes 
• 
Bundles and connectors more likely to be damaged by sheet metal edges while being pulled through holes 
• 
Takeouts and connectors must be retained by tape ties or by a wrap (either disposable or reusable) 
• 
Operators must make more connections (see first bullet point) 
• 
Water can leak through the wire bundle and into interior (though substances are available to block the gaps between 
the wires in the grommet) 
• 
Pass-through portion of wiring is vulnerable to damage during assembly process if connections are not made right 
away or if improperly stowed 
 
Advantages of bulkhead connectors: 
 
• 
Only one connection to make 
• 
No wiring passes through the sheet metal hole 
• 
Less likely to have a water leak 
 
Disadvantages of bulkhead connectors: 
 
• 
Must be mated with mechanical assistance (bolt, cam, etc.) rather than by hand 
• 
Terminals more likely to be damaged by rough handling because of the more open interface area (though caps can be 
used to reduce this risk; still, smaller connectors are less likely to need caps) 


### 第 65 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 65 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
• 
Wires, if improperly dressed, can strain the seals in the connector, leading to water leaks 
 
SUMMARY:  Neither method is inherently superior over the other; each has benefits and drawbacks.  The decision as to 
which method to use should be made by each program team after weighing all of the advantages and disadvantages and 
consulting with Vehicle Operations.  Vehicle and component packaging in conjunction with vehicle assembly sequences are 
the most critical criteria to be evaluated and controlled.


### 第 66 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 66 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
15. 
MISCELLANEOUS GUIDELINES 
 
Harness ID tags should be placed such that they can be found relatively easily by service technicians yet are not prominently 
visible to the average customer.  A good rule of thumb is to try and position the tag so that it's six inches from a major 
connector.   
 
Cutting off unused wires as part of a rework procedure is NOT permitted. 
 
Vehicle-level requirement 18-0034 and DVM-0161-18 specify the amount of corrosion resistance that each system in the 
vehicle must endure, in terms of cycles of the R-311 accelerated corrosion test.  EDS must pass 100 cycles for function and 
serviceability. 
 
Jump Starting:  It may be worthwhile to assist in the process for developing good locations for making jump start 
connections -- specifically, the ground connection on the engine.  The location for the connection should be far away from 
fuel lines and brake lines.  This will keep any arcing and sparking that may occur away from the lines. 
 
Per SDS requirement PS-0025, B+ covers, such as battery terminal covers or PDB stud covers, must not have holes or slots 
exposing the terminal such that it could be unintentionally grounded. 
 
Here are some other guidelines to keep in mind: 
 
Manufacturing Practices 
 
Drain wires are not to be wrapped taut around insulated wire for the purpose of defining takeouts.  This technique is known to 
cause short circuits.  Instead, use tape to define the takeouts.  For that matter, be careful with any takeout having contact with 
a drain wire, as pulling on the takeout may induce tautness and wire cutting.  Consider taping these takeouts as well. 
 
For harnesses that are PIA other components, advise the owners of those components not to use box cutters when opening the 
boxes of harnesses at their manufacturing facilities.  Box cutters end up cutting the harnesses and wires in addition to the 
boxes, leading to latent failures. 
 
Electrical Components 
 
Do not concur with component locations that do not facilitate making wiring connections or are in a splash area without 
adequate protection. 
 
All components packaged near the battery should maintain a minimum of 19 mm clearance from the battery to prevent 
possible damage from battery acid. Components packaged near the battery should be designed to resist acid or be properly 
shielded. 
 
It is not desirable to mount modules and relays under seats; nevertheless, they do get mounted there.  Protect the wiring and 
any of your components accordingly, using the rules for packaging in water entry zones specified in the EDS SDS. 
 
Modules, relays and other components that mount on the instrument panel should not be mounted on brackets that extend 
beyond the edges of the I/P. Otherwise, they are vulnerable to damage during I/P decking, shipping, etc. 
 
Make sure that concurrence is obtained on all exterior lamp socket usage. Refer to Lighting-issued socket usage transmittals. 
 
Make sure that takeouts for multiple bulb lamp assemblies are tailored and/or polarized to prevent misconnections. 
 
Verify interior lamp voltages when they can be activated from several sources (keyless entry, illuminated entry, door jamb 
switches, headlamp switch, etc.). 
 


### 第 67 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 67 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Use direct connects to modules, relays, etc.  Avoid pigtails, unless direct connection would expose the connector to a 
hazardous environment. 
 
Where feasible, package relays in junction boxes/high-current fuse panels. 
 
Components requiring self-grounding must be located on a compatible surface. Ground should not be accomplished through 
attachment points. 
 
All components (relays, circuit breakers, sensors, modules, etc.) in the engine compartment or other wet area must have 
terminals oriented to forestall water entry. 
 
All similar components in the same area must be designed/keyed to prevent misconnection. 
 
In general, components must NOT rely on wiring over-current protection devices to provide protection from overloads, short 
circuits, etc.  They must either provide their own protection or be robust enough to tolerate the overload.  For exceptions, see 
the section on fusing. 
 
Fuse Panels 
 
Put a reference dimension equivalent to the average length of wires in the fuse panel on the released drawing.  Do not 
dimension each individual wire from the edge of the fuse panel to buss bars, fuse clips, etc.  These will be developed by the 
manufacturer. 
 
Locate the fuse panel so fuses are readily accessible.  Do this with the Instrument Panel PMT. 
 
Do not make any tooling changes to fuse panels, buss bars or fuse clips without EDS Component concurrence.  Do not change 
fuse panel usage from year to year without EDS Component concurrence. 
 
Heating Harnesses 
 
This is not a design engineering issue, but the wiring design team needs to be aware of it, even though Engineering has little 
or no control over it.  It is common for assembly plants to heat the wiring prior to installation.  At higher temperatures 
(typically around 65 °C), the harnesses become more flexible, which permits them to be installed more easily.  Heating is also 
useful to bring harnesses that have just been unloaded from a cold truck up to installation temperature. 
 
There are two main ways to heat harnesses.  The first way is radiant heating, where heat lamps are placed above the harnesses. 
 This method is inexpensive, but it has two side effects.  First, the heating is not equally distributed throughout the harness 
box.  Harnesses at the top of the box are baked, while harnesses in the middle or at the bottom are not sufficiently warmed.  
Second, the components on the harnesses have been melted or otherwise damaged as a result of excessive heat.  This leads to 
in-process repairs at best and scrappage at worst.  The other way to heat harnesses involves passing the harnesses through a 
heat tunnel, where they are brought to the desired temperature.  This method is more costly due to the additional equipment, 
but it is more easily and precisely controlled.  Also, all of the harnesses see the same heat distribution, so they are heated to 
the same extent. 
 
Whichever method is used, care must be taken by plant personnel to prevent harness damage during breaks and other times 
when the line is shut down. 
 
16. 
ENSURE CONNECTIONS ARE ACCESSIBLE FOR ASSEMBLY, SERVICE 
 
The design is nearly complete.  At this stage, make sure that the connections are easy to make.  Difficult connections are 
likely to become unmade connections, with all the problems that entails.  Review with your V.O. Systems engineer and with 
the appropriate FCSD engineer. 
 
17. 
PERFORM A CIRCUIT ANALYSIS, ESPECIALLY ON POWER/GROUND CIRCUITS 
 


### 第 68 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 68 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Several systems and components have stringent requirements for voltage drops, resistances, inductances and the like.  To 
make sure these requirements are met, you will need to perform a circuit analysis on all of the circuits.  Perform a theoretical 
analysis at design time using CAE tools, and verify questionable or borderline results later on the breadboard and on actual 
vehicles. 
 
18.  DETERMINE SIGNIFICANT CHARACTERISTICS 
 
Significant characteristics are defined as key quality features of a process or a component on which variability data should be 
collected.  This data provides the means to control parameters that contribute significantly to quality characteristics.  
Significant characteristics are identified by the responsible Program Module Team (Engineering, VO, Supplier, Purchasing, 
CBG Program Management).  They are incorporated into the master control plan for that part, which specifies tolerances, 
monitoring requirements, etc. It is not recommended that significant characteristics be labeled as such on the drawing; if they 
are to be labeled, they should be called Control Plan Values instead. 
 
Criteria for Determining Significant Characteristics for Wiring and Related Parts 
 
1. 
If the wiring is out of spec, will that affect a subsequent operation?  That is, can a subsequent part (such as a trim panel) be 
installed? 
 
2. 
If the subsequent part can be installed, can the harness get pinched or trapped or damaged? 
 
3. 
If the wiring is out of spec, can it be damaged by exposure to high heat (near exhaust manifold, for example) or by interfering 
with moving parts (accessory drive, for example)? 
 
4. 
If the wiring is out of spec, can it interfere with the operation of moving parts, thus degrading other systems' operation (climate 
control, for example)? 
 
Some wiring characteristics which could be classified as significant include locator dimensions, takeout lengths and 
orientations, terminal pushouts, etc. 
 
These attachments are to be denoted as critical characteristics: electrical wire to alternator stud; battery cable positive terminal; battery 
cable negative terminal; battery positive feed to power distribution box; and battery to starter solenoid (B+).  In general, all high-current 
fasteners, be they power or ground, are to be designated as critical characteristics.  All other ground fasteners are to be designated as 
significant characteristics, as are the bolts on bolt-driven connectors. 
 
If metal brackets are to be crimped onto the wiring, the crimp dimensions (height and width) should be specified as significant 
characteristics on the harness drawing.  This crimp must be controlled to avoid damaging the cable. 
 
19. 
DEVELOP A DVP&R 
 
Now that the design is complete, it is time to develop a plan for testing it:  the Design Verification Plan and Report (DVP&R). 
 The DVP&R can cover the electrical distribution system in full or in part, rather than just the individual harnesses, because 
tests on the system of harnesses are usually more valid than tests on just a single harness.  The basis for the DVP&R is found 
in the EDS SDS: the design verification methods (DVM's).   Feel free to add, delete or modify these as necessary to make 
them fit your situation. 
 
20. 
ENSURE THAT DESIGNS COMPLY WITH GOVERNMENT REGULATIONS/CORPORATE 
REQUIREMENTS 
 
By connecting components together, your wiring becomes an integral part of several systems that are regulated by government regulations, 
such as the Federal/Canadian Motor Vehicle Safety Standards (F/CMVSS) in the US and Canada, respectively.  There are penalties for 
failure to comply with these standards.  Compliance should be no problem, though, as corporate design requirements meet or exceed 
regulatory stipulations.  Also, be aware of any state or local requirements that may be applicable, such as California emissions 
requirements, special lighting requirements, etc.  Make sure that your initial releases and any changes you make or are directed to make do 
not violate corporate requirements  -- if in doubt, consult with your supervisor and with the Safety Office. 
 


### 第 69 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 69 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Table 1 (see last page) lists some of the standards that apply to wiring. For more information, or for any specific questions about 
compliance, consult with the Safety Office. 
 
The same holds true for homologated parts (parts released for use in other countries).  Your designs must comply with the regulations of 
each country where the vehicle is sold.  These regulations can differ greatly from one country to the next. 


### 第 70 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 70 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
SUMMARY OF F/CMVSS PASSENGER CAR REGULATIONS WHICH IMPACT WIRING (REFERENCE ONLY) 
 
FMVSS 101 
Controls and Displays 
 
Instrument panel illumination must be able to provide at least 2 levels of brightness (usually accomplished with a dimmer rheostat in the 
main light switch). 
 
FMVSS 105 
Hydraulic Brake Systems 
 
Brake warning light must be illuminated under these conditions: 
 
Low pressure or low fluid in master cylinder reservoir 
 
Total electrical failure in anti-lock braking system 
 
Parking brake is Set 
 
Ignition switch is turned to "Start" Position (Check Lamp) 
 
FMVSS 108 
Lamps, Reflective Devices, and Associated Equipment 
 
Show power and circuitry to the following lamps: 
 
Backup Lamps 
 
Front Park Lamps 
 
High Beams 
 
Low Beams 
 
Side Markers (front/rear Park Lamps) 
 
Stop Lamps (high-mount) 
 
Stop Lamps (rear) 
 
Tail Lamps 
 
Turn Signals 
 
Daytime Running Lamps (Canada only) 
 
FMVSS 112 
Headlamp Concealment Devices 
 
Gives requirements for headlamp doors 
Allows use of an automatic lamp activation system -- show the circuitry for it 
 
FMVSS 114 
Theft Protection 
 
The ignition system must be keyed such that the engine cannot be started (no power supplied to ignition coil) without the key activating the 
switch. There must be a warning chime/buzzer which sounds when the driver's door is open and the key is in the switch 
 
FMVSS 118 
Power Operated Window Systems 
 
Power windows may operate only: 
 
When the ignition key is in the ON, START, or ACCESSORY position 
 
By a key-locking system on the exterior of the vehicle 
There are other conditions, but they do not involve wiring 
 
FMVSS 208 
Occupant Crash Protection 
 
Passive restraint system is required -- sign off on the circuitry for it  Seat belt warning circuitry must illuminate the instrument panel 
warning light for 4-8 second duration after the ignition switch is turned on.  Audible warning must sound if driver's seat belt is not buckled. 
 Show the circuitry for these components: Audible chime warning; Seat belt warning light; Air bag system; Air bag warning light 


### 第 71 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 71 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
BATTERY CABLE INFORMATION 
 
 
Until early 1995, the responsibility for battery and starter cables did not belong to the EDS department.  With the Ford 2000 
reorganization, that changed.  Along with that change came a need to incorporate cable design information into the Wiring 
Design Guide.  While many aspects of cable design and wiring design are similar, there are special requirements for the 
cables that deserved to be discussed in their own section of the guide. 
 
This section will be structured like the overall design guide.  Items that are substantially different from, or are in addition to, 
the regular wiring requirements will be mentioned here. 
 
DESIGN AND ENGINEER THE SYSTEM 
 
Wire Size, Insulation Types 
 
Here are the insulations used on battery cables: 
 
ESF-M1L28-A 
 
an Elastomer, used on 4 ga. wires 
ESF-M1L34-A 
 
stranded insulation, used on 6 gage wire 
ESF-M1L58-A   
vinyl, with a service temperature of 105 °C 
ESF-M1L59-A   
similar to M1L58 
ESF-M1L64-A 
 
Stranded insulation, used on 2 gage wire 
ESB-M1L85-A/B Standard wall XLPE; service temperature 150 °C 
ESB-M1L92-A/A2 
Irradiated standard wall XLPE, more ozone- and acid-resistant than M1L85.  Service temperatures 
are 125 °C and 135 °C, respectively 
ESB-M1L123-A  
Thinwall (16 mill) XLPE, used on 12 gage wire 
 
Efforts are underway to reduce the number of insulation types. 
 
(chart for resistance of battery cable size wires goes here) (future revision of section) 
 
Color coding for power cables has traditionally been very basic:  red for hot circuits, black for grounds.  This may change in 
the future, however, if battery and starter cable functions are incorporated into the main wiring color code spec. 
 
The insulation used on the battery cable must be acid-resistant, ozone-resistant and temperature-resistant.. 
 
CHOOSE TERMINALS AND CONNECTORS 
 
Battery Cable Terminals 
 
Battery cable terminals are of two types, die cast and stamped.  The die cast terminals are the traditional North American 
terminals.  They are still used for 00-gage and larger wire.  The advantages of this terminal are that it's made out of the same 
material as the battery post (no galvanic corrosion) and that it looks robust.  The disadvantages are that it's made from molten 
lead, a toxic and environmental hazard, and that it can corrode.  Because of this, die cast terminals are being replaced by 
stamped terminals. 
 
Stamped terminals are typically made from heavily plated 260 brass.  They are used in Europe and North America.  
Traditionally, insulation grips are not used on these terminals; tests are needed to determine whether they should be used. 
 
Battery cable terminals must conform to ES-F3LU-14450-AA and subsequent revisions.  Battery cable terminal covers must 
conform to ES-F3LU-14277-AA. 
 
Battery cable terminal clamps must be designed such that they have an interference fit to the battery post  When torqued to 
specifications, they cannot be higher than 3 mm (1.5 mm?) above the battery post. 
 


### 第 72 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 72 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
The eyelets used on the battery cable must be capable of sustaining the maximum torque value for each component to which 
the cable attaches, such as the PDB, the starter, etc. 
 
Several starter cable terminals have anti-rotation features.  Use these types of terminals, if possible. 
 
PROVIDE CIRCUIT PROTECTION 
 
Circuit protection requirements above and beyond those for regular wiring are TBD. 
 
CABLE ROUTING AND ATTACHING 
 
The battery cable connection to the starter must be covered to prevent shorting out. 
 
The negative battery cable cannot be secured underneath the starter mounting bolt.  A stud bolt attachment is permissible, 
however. 
 
Do not route any cable carrying starter motor current closer than 180 mm to the crankshaft position sensor or the distributor 
pick-up assembly inside the distributor bowl.  Requirements for distributorless ignition systems are TBD. 
 
Cables must be strain-relieved as they route from the body or frame to the engine.  Brackets are to be used on the engine and 
frame (if a frame vehicle); see the section on brackets for more information.  There must be enough slack in the cable to avoid 
overstressed connections and attachments due to engine roll and vibration, but not so much that the cable can be caught or 
trapped anywhere. 
  
CONNECTION CAPABILITY 
 
Battery cable connections must be accessible for service without having to remove the engine.  Service must not require any 
tools other than standard hand tools.  The battery cable connections to the starter and the starter motor relay must be 
accessible such that either component can be replaced without removing the other. 
 
Clearances 
 
Starter relay eyelet connections must have a minimum clearance of 15 mm to any grounded component. 
 
The battery cable clamps must have a minimum of 13 mm clearance to adjacent fixed chassis or body components (battery 
supporting components excepted) and a minimum of 19 mm clearance to moving components or to powertrain components. 
 
The top of the battery post or terminal (whichever is higher) must have a minimum of 32 mm clearance from the hood in the 
absence of a hood insulator.  If there is a hood insulator, this requirement is reduced to 15 mm. 
 
Brackets 
 
Brackets are a very important tool in controlling the routing of battery and starter cables.  As of August 1995, the vast 
majority of starter cable brackets are done by P-K Tool, a full service supplier.  Here are some basic guidelines for the design 
and application of brackets: 
 
At a minimum, a bracket should be used on the engine at the point where the cable routes over to the body or frame.  
Additional engine brackets may be required to prevent trapping or pinching situations.  If the cable routes to a frame, then 
there should also be a frame bracket. 
 
Brackets must have a positive location or anti-rotation feature.  There are four main ways to achieve this: 
 
Anti-rotation finger which goes into a hole in the mounting surface 
 
Anti-rotation finger that butts against an engine boss or other component 
 
Frame bracket rotation flange where a second hole for the finger is unavailable 
 
Dual mounting hole bracket 


### 第 73 页
EESE-EDS                          DESIGN AND ENGINEER THE WIRING SUBSYSTEM                                       August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 73 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
GROUNDING 
 
The battery ground cable must be terminated such that no stray currents are induced in the ignition system or carried by other 
non-electrical components, such as the throttle linkage or the speedometer cable. 
 
Perform a torque angle study to determine the minimum torque required for grounding attachment points. 
 
The preferred place to mount the engine ground is on the starter motor with a double-ended stud.  However, some programs 
have requested that it be mounted on a stud on the side of the engine block.  If this is done, then the mounting boss must be 
machined smooth to allow good electrical contact.  It should also have an anti-rotation feature. 
 
PROTECTING THE WIRES 
 
Electrical (EMC) Protection 
 
Mechanical Protection 
 
Whenever the starter motor cable passes through a metal compartment, the cable must be protected against chafing and 
grounding by an acid-resistant and waterproof insulating bushing. 
 
Convolute 
 
Convolute used on battery cables has been of 3 types: ES-E3TF-14A099-CA, ES-D523-14A099-AA, and ES-E90B-14A099-
AA.  Efforts are under way to develop tougher wire insulation to reduce or eliminate the need for convolute on battery cables 
in many instances. 
 
Tape 
 
Tapes used on battery and starter cables are the same kinds used on regular wiring. 
 
Heat Shrink Tubing 
 
Heat shrink tubing used on battery and starter cables is the same kind used on regular wiring. 
 
ENVIRONMENTAL ISSUES 
 
The alternator housing skin temperature can reach a temperature of 190 °C.  Choose materials that can withstand this 
temperature should they come into contact with the alternator housing. 
 


### 第 74 页
EESE-EDS                                                   LIST OF PITFALLS TO AVOID                                                       May 1997 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 74 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
List of Pitfalls to Avoid  
 
 
These tips come from field experience.  Nearly all of them have been incorporated into the main body of the design guide, but 
it never hurts to see them repeated. 
 
1. 
Provide protective covering in all areas that could be damaged during normal production sequences. 
 
2. 
Specify adequate wire gage and circuit protection for load requirements. 
 
3. 
Ensure that applications needing clean power (engine control, ABS, etc.) get it.  Power feeds to these applications 
should not be tied into noisy power feeds (such as fuel pumps).  Following the generic fusing strategy will ensure 
this. 
 
4. 
Insulate and separate splices to meet corrosion and current leakage requirements. 
 
5. 
Ensure that submergible connectors do not lose their seals under extreme assembly conditions, such as wires bending 
90° immediately after coming out of the back of the connector. 
 
6. 
Specify adequate connector and wire retention to ensure wiring position. 
 
7. 
Provide adequate retention for harness trunks so that they are clear of any bolts, corners, edges, etc., which could 
abrade the wires during normal operation (engine roll, for example). 
 
8. 
Review engine operating movement characteristics to avoid wiring contact and abrasion. 
 
9. 
Ensure that harness construction in general, and takeout orientation in particular, is controlled by design and by 
supplier so that the threat of shorting is minimized.  Avoid takeouts in areas subject to abrasion and pinching during 
assembly, if possible. 
 
10. 
Specify wire lengths to maintain clearances under minimum and maximum variation conditions.  Evaluate the 
acceptability of tolerances as given in ES-F65B-14A121-AA. 
 
11. 
Control VO build sequence to provide minimum wiring damage potential. 
 
12. 
Ensure that VO has Engineering concurrence when revising build sequence from design intent. 
 
13. 
Anticipate probable misrouted wiring situations and protect from penetration by screws and raw edges. 
 
14. 
Verify that design aid buck sheet metal is representative of production intent -- that locator holes are in the right 
places, that brackets and attachments are the latest level.  This will minimize surprises at Job #1. 
 
15. 
Verify that all modules and components which connect to your wiring are truly representative of Job #1 level 
components.  if they are not, find out how they differ from the current level.  This may impact on your designs. 
 
16. 
Use materials for wiring shields which will withstand the stresses of rough handling during assembly -- not too brittle 
so that it won't crack. 
 
17. 
Avoid using exposed supports (such as gas cylinder hood supports) or other such parts as conductors; they can be 
easily shorted during service. 
 
18. 
For EEC:  Ensure that the EEC schematic does not violate any of the EEC EMC Guidelines. 
 
 
General:  Ensure that a subsystem schematic is in agreement with any applicable design guidelines. 
 


### 第 75 页
EESE-EDS                                                   LIST OF PITFALLS TO AVOID                                                       May 1997 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 75 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
19. 
When two or more circuits with the same number and/or function must be present in the same connector and cannot 
be distinguished by standard functional checks, then the wires must have the same gage size.  This will enable them 
to be interchanged in the connector with no chance of damage to the wiring or component. 


### 第 76 页
EESE-EDS                                                               FUSE LINKS                                                                             March 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 76 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
 
FUSE LINKS 
 
 
A fuse link (fuselink, fusible link) is a short piece of wire several gage sizes smaller (gage number larger) than the 
other wires in the circuit.  Its job is to provide short-circuit protection for wiring.  It is NEVER to be used to protect 
devices and components; low-current fuses are to be used instead.  Fuse links have been largely eliminated from the 
vehicle, except for circuits containing too much current for a high-current fuse. Per SDS requirement ED-0094, there 
are only three types of feeds which may be protected by either a megafuse or a fuse link:  battery-to-PDB, battery-to-
alternator feeds, and diesel glow plug circuits. 
 
 
Use a fuse link at least 4 to 6 gage sizes smaller than the smallest wire in the circuit to be protected.  The total circuit 
resistance (including fuse link) determines the protected wire gage.  This chart shows the protective capability of 
various gages of fuse links: 
 
          Fuse         Minimum(1)        Maximum (2) 
        Link Gauge     Current           Resistance 
 
           20 
 
 80A                .112 ohms 
           18 
 
120A                .075 
           16 
 
150A                .060 
           14 
 
220A                .041 
           12 
 
320A                .028 
 
 
Fuse link length is to be 100-150 mm.  Leave a minimum of 50.0 mm and a maximum of 75.0 mm of fuse link 
exposed. 
 
 
Use this chart to determine the protected circuit length: 
 
                                                       Length to (5) 
                       Protected(3)                     Subtract     Length to (6) 
                         Circuit        Length to(4)   per Switch      Subtract 
Fuse         Protected   Maximum       Subtract per     or Relay     per Ignition 
Link Gauge  Ckt. Gauge   Length         Connection      Contract       Switch   
 
 20          16          7100 mm        130 mm           330 mm         670 mm  
 18          14          7700 mm        220 mm           540 mm        1080 mm  
 16          12          9800 mm        340 mm           850 mm        1700 mm  
 14          10         10800 mm        540 mm          1360 mm        2720 mm  
 12           8         11700 mm        860 mm          2160 mm        4310 mm  
  
 
 
(1) 
Minimum rupture current required to prevent circuit damage. 
 
(2) 
This value includes both the resistance of the fuse link and the downstream protected circuitry, and will ensure that 
minimum rupture current is met under short circuit conditions. 
 
(3) 
Calculated using attached resistance chart and assuming 150 mm fuse link.  Note:  Values calculated at 20 C.  
Lengths must be adjusted for ambient temperature differences. 
 
(4) 
Assumes .002 V/A per connection. 
 
(5) 
Assumes .005 V/A per switch (inertia) or relay contacts. 
 
(6) 
Assumes .010 V/A per ignition switch. 
 
 
Use insulation material complying with SAE J156.  Hypalon insulation is no longer to be used. 
 


### 第 77 页
EESE-EDS                                                               FUSE LINKS                                                                             March 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 77 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
Use heat shrink tubing (part no. E9DB-14454-**) for fuse link splice sealing and identification.  If one end of the 
fuse link ends in an eyelet, seal that termination with heat shrink.  If neither end of the fuse link is terminated with an 
eyelet, choose one end to be sealed with the special heat shrink.  Leave that end out of the wire bundle. 
 
 
If the alternator output wire is protected with fuse links, then based on historic experience, these are the 
recommended protection schemes: 
 
 
 
For alternators rated at less than 100 A, use a single 12-gage fuse link.  This requires an 8-gage feed wire. 
 
 
 
For higher-rated alternators, use two (2) 12-gage fuse links connected in parallel, with a total cut length of 
100 mm, 65 mm of exposed insulation, terminated at an eyelet.  This requires a 6-gage feed wire. 
 
 
In all cases, perform a load study to verify that the feed is sufficiently protected.  Average electrical loads may be 
such that parallel fuse links are not required. 
 
 
Do "weld only" or "splice clip" fuse link splices, but do not weld 20 ga. fuse links -- "splice clip" only. 
 
 
Do not tape wrap over a fuse link, including the weld splice insulator.  However, per the SDS, they may be 
encapsulated in a flame-resistant covering. 
 
 
Refer to the Guidelines Supplement for fuse link design information (should be in Wiring Reference Library). 
 
 
Do not locate fuse links in the passenger compartment; the only acceptable location is the engine 
compartment.  
 
 
Keep fuse links away from the battery (150 to 300 mm) in order to minimize splashing battery acid on them, as this 
will attack the insulation, as well as to minimize splashing the battery with droplets of molten copper if/when the fuse 
links blow.  Avoid routing under the battery whenever possible. 
 
 
Maintain wire gage size for fuse link protection on unfused key-on non-current limited circuits (ignition module feed, 
alternator regulator -- ammeter vehicle) until the circuit exits the interior of the vehicle. Gage size may then be 
reduced to suit the voltage/current requirements of the circuit. 
 
 
When using fuse links, you must ensure that, under worst case short circuit conditions, the wires permit at least the 
minimum current flow needed to blow the link.  Perform a circuit analysis on the circuit in question, and test if 
necessary, to verify this requirement. 
 
 
Minimize fuse link length and optimize the packaging to avoid direct contact of the fuse link with combustible 
materials. 
 
 
 Fuse Link                      Circuit Number           Color Code 
                             (North American Spec) 
 
 12 ga.                             290                   Gray 
 14 ga.                             299                   Dark Green 
 16 ga.                             291                   Black 
 18 ga.                             292                   Brown 
 20 ga.                             302                   Dark Blue 
 


### 第 78 页
EESE-EDS                                                 SERVICE RECOMMENDATIONS                                                         August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 78 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
SERVICE RECOMMENDATIONS FOR WIRING REPAIR 
 
Note: The repair procedures cited here can and must be applied to vehicle personalization work performed by the dealer. 
 
 
Your goal in designing the wiring system is to minimize the risk of damage to the system that can cause other systems in the 
vehicle to malfunction or fail.  Still, some damage is bound to occur, whether it's caused by an accident, a weakness in 
product design or some random noise factor.  The system will need to be serviced (parts must be available for up to 10 years 
after the last production date), and part of your job is to see that the wiring can be easily serviced. 
 
The decision on whether or not to release a repair kit for a given connection or terminal will generally be based on business 
decisions.  In certain cases, it's not feasible or desirable to repair a connector with a repair kit.  For example, the connector 
could have too many circuits, such as a bulkhead connector or the powertrain control module connector(s).  Others may be 
used in safety-related applications.  In addition, the cost of the kit might approach the cost of the harness, or the harness may 
be easier to replace than it would be to repair a connection. 
 
In general, the only connectors that should not be repaired are: (1) restraints connectors that are used in stand-alone restraints 
wiring harnesses (i.e., not contained inside the main vehicle harnesses), and (2) connectors that have circuits utilizing 8 AWG 
(or the metric equivalent, 6 sq. mm) or larger-diameter wire (these are too large to effectively splice in the field).   
 
Restraints-related connectors:  If restraints connectors and circuits are contained in a stand-alone harness, then do not repair 
them; rather, replace the restraints harness.  If, however, they are contained in the main vehicle wiring (14401, 14A005, etc.), 
then they may be repaired, provided that only the solder and heat-shrink repair procedure specified elsewhere in this 
document is followed. 
 
Component location has a large impact in determining the need for a repair kit.  Here is a list of key areas that are more likely 
to need repair kits: 
 
1. Connection points with high incidence of crash damage (lamp sockets, etc.) 
2. High current flow or high heat connection points (e.g. headlamp switch, ignition switch, blower motor, etc.) 
3. Connection points with high incidence of corrosion 
4. High theft rate areas (e.g. radio, etc.) 
5. Connection points with high incidence of damage during repair operations (based on field experience) 
6. Cases where hardshell service part sales or warranty are high 
 
Of course, a repair kit can be released for any connection in the vehicle, if it makes good business sense to do so. 
 
TYPES OF SERVICE PARTS FOR WIRING 
 
SERVICE HARNESSES 
 
Service harnesses are used when it's necessary to replace the entire harness, such as when it's been damaged in a crash or a 
vehicle fire.  It's generally not necessary to release every production wire harness for service; doing so incurs major inventory 
costs at Ford FCSD parts distribution centers.  Where content allows, release harnesses for service that can be used for 
multiple production harnesses.  For example, if 5Y2T-14290-Dx contains all the content found in 5Y2T-14290-Ax, Bx and 
Cx, then release the Dx part to service all four parts.  This reduces the number of part levels that FCSD must carry.  
Alternatively, you can design a special service-only harness that can be used to repair multiple production levels.  Again, this 
reduces the number of part levels that FCSD must carry.  In either case, any extra content on the harnesses must be either 
stowed away or cut off; the proper procedure to do these is listed elsewhere in this section. 
 
PIGTAIL REPAIR KITS 
 
Pigtail repair kits consist of a connector, including all necessary sealing devices, and a number of cut leads or "repair pigtails" 
(the service term).  These kits are used to repair and replace a damaged connection without having to remove and replace the 
entire harness.  Information about the repair kits, both pigtail and terminal, can be found at 


### 第 79 页
EESE-EDS                                                 SERVICE RECOMMENDATIONS                                                         August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 79 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
http://cms.fmcdealer.dealerconnection.com/Parts/Maintenance+and+Light+Repair/Electrical+and+Ignition/default.ht
m.  
 
 
 
TERMINAL REPAIR KITS 
 
A number of terminal repair kits have been released over the years, containing the then-latest terminal designs.  Each kit has 
up to 60 terminal types, and each terminal comes crimped to a 100-mm length of wire, pre-stripped, meeting the following 
color convention:  white for gold-plated terminals, green for silver-plated terminals, and black for tin-plated or unplated 
terminals.  Information about the terminal repair kits can be found at 
https://web.fordcomponents.dealerconnection.com/wire/index.jsp. 
 
As new terminal designs and usages are released, they'll need to be added to future terminal repair kits.  Work with the 
connector group to see that new terminal designs are captured. 
 
TECHNICAL CONSIDERATIONS FOR REPAIR KITS 
 
These are general recommendations for repair kit design as well as the methods used to perform the repair.  These apply 
mainly to pigtail repair kits, though some of them can apply to terminal repair kits as well.  Alternate repair techniques are 
allowable if they have been approved by EDS Core and FCSD. 
 
Should the kits be designed and released so that the parts are loose (i.e., cut leads not loaded into the connector, dress covers 
not installed, etc.) or assembled?  Either method is acceptable, depending on the circumstances.  Insulation-displacement 
connectors should come as complete pigtails, for example.  However, service technicians have expressed a preference for 
loose.  Generally, repair kits for molded connectors will come pre-loaded, while others should have the cut leads loose.  If 
special materials are required for the repair, such as an upgraded solder, include those in the kit as well. 
 
Having the proper tools (terminal/pin removal devices, high-quality heat sources for soldering, etc.) is vital to making a 
quality repair.  The service technicians must be able to obtain these tools, whether from Ford or another tool source.  The 
wiring and/or connector full-service suppliers need to work with the appropriate Ford service personnel to insure that the 
proper repair tools are made available to the technicians.  One idea: release an inexpensive, throwaway terminal removal tool 
or tools for inclusion in each kit.  This will increase the likelihood that the proper tools will be used for the repair.  
Information on a preferred crimp tool is shown at the end of this guide. 
 
Inline connector repair:  It is permissible to eliminate the inline connector by splicing wire to wire.  This method is preferred 
to splicing on all new terminals to one half or both sides of the connection, as it increases reliability by eliminating the 
terminal interfaces. 
 
In general, pigtail repair kits should be released as a 14A411, W/A - Main Jumper.  This corresponds to existing FCSD 
releases. 
 
Wire 
 
The cut leads supplied with the pigtail repair kits should be approximately 250 to 300 mm long (versus 100 mm for cut leads 
in the terminal repair kits).  This will allow sufficient room for the staggering of splices.  If the mating component is 
considered critical or safety-related, then the color of the cut leads must match those in the original vehicle wiring.  
Otherwise, they are to be specified as follows:  white for gold-plated terminals, green for silver-plated terminals, and black for 
tin-plated or unplated terminals.  However, if the cut leads are for a repair kit/pigtail created as an emergency field fix, they 
may match the colors of the original vehicle wiring. Their insulation must be compatible with the connector seals, not only in 
terms of material but also in terms of size.  For example, if a sealed connector is designed to use 16 mil insulation, do not 
supply pigtails made with 10 mil insulation.  Under most circumstances, use the wire insulation that can withstand the highest 
temperature. 
 


### 第 80 页
EESE-EDS                                                 SERVICE RECOMMENDATIONS                                                         August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 80 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Consider using tinned wire for the cut leads.  The tinned wire improves the capability of both the solder and crimp splicing 
methods. However, it will add cost to the pigtails. 
 
Sizing the wires 
 
In order to reduce the number of pigtail kits that are released, it is desirable to maximize the wire size used for a given circuit, 
so that it can be used in multiple applications.  For example, if a particular circuit in a connector is 18-gage on one vehicle but 
16- on another, then the pigtail kit circuit should be 16-gage, as it can be used for both applications.  This works best for wires 
that are close to each other in size.  If there's a large differential, though – say, an 18-gage wire to a 10- or 12-gage wire – 
special crimp techniques need to be employed.  The stripped end of the smaller wire needs to be folded a number of times in 
order to make the effective diameter match more closely the diameter of the larger wire (or, to be more specific, the diameter 
of the ferrule used as part of the crimp/heat shrink splice method).   
 
The solder/heat shrink procedure can be used for any combination of 16-gage and smaller wire.  Be careful, though, with 
soldering wires with large size disparities because the smaller wire will heat up much faster than the larger wire, which could 
lead to a cold weld, a poor-quality solder joint. 
 
Twisted Pairs 
 
When twisted circuits are repaired, the repair technique must cause a disruption in the twisting of no more than 50 mm.  
Otherwise, the benefits of the twisting will be completely negated.  It is desirable for wires that are going to be twisted to be 
supplied loose in the kit.  But if that's not feasible, then the technician must twist the wires before doing the repair, using the 
original twist rate and direction that's in the vehicle.  When doing the repair, the technician will splice these circuits to the 
vehicle wiring, staggering the splices if necessary.  If the wires came loose in the kit, he'll then twist the repaired wires in the 
same direction and with the same general twist rate as the original wiring, and then he'll insert the terminated ends into the 
connector.  Make sure that the repair kit instructions cite this repair procedure or include a pointer to where it can be found. 
 
Connectors 
 
If a connector is released in multiple colors but with the same keying (say, for appearance reasons), then use the version that 
will cause the fewest appearance issues.  This is a rare occurrence, though.  Most times, there will be only one color per 
keying. 
 
Terminals (also applies to terminal repair kits) 
 
Use the highest-temperature-rated terminal available for the specific plating being used in that application.  In other words, if 
the application is for a tin-plated terminal, but there's a gold-plated terminal that has a higher temperature rating, stick with the 
tin. 
 
If a terminal is available in both greased and non-greased forms, then use the greased form for service. 
 
Make sure that you specify special-plated terminals (i.e., gold, silver, etc.) where appropriate so that contact between 
dissimilar metals does not occur in the vehicle. 
 
Stowing unused connections or circuits (also applies to service harnesses) 
 
It is possible that after the repair is completed, there are unused circuits attached to the connector.  In the case of service 
harnesses, there may be unused connections for content not being used in the repair.  These unused circuits and connections 
need to be stowed and sealed, if necessary.  The approved procedure for that will be shown separately in a future revision of 
this document. 
 
Instruction Sheets (applies to all) 
 


### 第 81 页
EESE-EDS                                                 SERVICE RECOMMENDATIONS                                                         August 2003 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 81 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Without instructions for use, the best repair kits in the world will be useless.  The instruction sheets need to convey basic 
information about terminal/pin removal and installation, wire splicing and sealing, splice staggering, etc.  The information 
should be shown graphically where possible, to allow for worldwide use of the instruction sheets. 
 
If a repair involves twisted wires, then the instruction sheet should point to the detailed instructions for repairing twisted pairs 
in the Wiring Diagrams. 
 
WIRE SPLICING PROCEDURES 
 
Repair considerations: In order to feasibly repair wires without causing a huge knot in the bundle, the locations of the splices 
need to be staggered.  The proper technique for doing this needs to be shown in the instructions that accompany the repair kit. 
 
There are two approved wire splicing procedures available: the solder-and-heat-shrink procedure, and the crimp-and-heat-
shrink procedure (both shown separately).  The solder-and-heat-shrink procedure is the only one approved for use in repairing 
restraints-related circuits. 
 
Heat shrink tubing or other sealing device 
 
Splices are to be sealed with either dual-wall heat shrink tubing or mastic patch.  The tubing is the preferred method, but the 
mastic may be used in situations where applying heat to the tubing is difficult to impossible, or where there's a large mismatch 
between the two wires being spliced together (i.e., 12 gage to 18 gage).  Don't use single-wall (no sealant) heat shrink tubing 
in repair kits.  The sealant in dual-wall heat shrink improves the mechanical integrity of the splice, even if its water-sealing 
capabilities are not needed. 
 
Dual-wall heat shrink tubing is available in bulk kits, but it should also be included in the repair kits as well.  If mastic patch 
is used instead of heat shrink tubing, it should be included in the repair kits. 
 
SOME QUESTIONS AND ANSWERS 
 
1) How can the technicians in the field identify the difference between gold and tin terminals? 
The best technique to determine the type of terminal plating is to look at the male terminal, for the plating will be easily 
visible. 
 
Distinguishing between female terminals is much more difficult, since the terminal plating of interest is inside the terminal.  
Some gold female terminals have a strip of gold flash around the barrel of the terminal.  Others identify the plating with "Sn" 
or "Au" stamped on the terminal, and the micropin terminal has a tin plated barrel for gold and unplated (brass-colored) 
barrel for tin plated terminals. 
 
As for terminals coming from the Terminal Repair Kit or Pigtail Repair Kit, gold plated terminals will have white wire, and 
tin-plated or unplated terminals will have black wire.  
 
2) Why do we need to take care not to mix gold and tin terminals when making repairs? 
The contact physics of mating gold and tin terminals can create a high resistance interface.  This can cause vehicle systems 
to quit operating or to operate in unexpected ways.  This high resistance is dependent on factors such as plating thickness, 
terminal geometry and which terminal (male or female) is tin. In general, though, we discourage the mixing of tin and gold 
plated terminals. 
 
3) Why can't we crimp terminals onto wires ourselves? 
We do not allow any hand crimping of terminals on production vehicles.  What looks like a good mechanical crimp may 
actually not perform well electrically.  For this reason, Ford's crimps are validated and produced on production equipment 
to exacting standards.  When it comes to vehicle repair, we permit only the splicing of machine crimped terminated leads, 
such as that provided in the Terminal Repair Kit or Pigtail Repair Kit. 
 
 


### 第 82 页
� � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � �
� � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � �
� � � � � � � � � �
� � � � � � � � � � � � � � � � � � � � � � � � � � � �
� � � �
� � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � �
� � � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � � �
� � � � � � � � � �
� � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � �
�
� � � � � � � � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � �
� � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � �
� � � �
� � � � � � � � � � � � � � � � �
� � � � � � � � � �
� � � � � � � � � � � � � � �


### 第 83 页
CRIMP/HEAT SHRINK WIRE SPLICE PROCEDURE
SERVICE PROCEDURE:
Installation Procedure
Read these instructions thoroughly before crimping or repairing any wire splice.
1.  Disconnect battery ground.
2. Select appropriate wire splice for the wire size. Refer to guide at bottom of procedure.
3. Use heat shrinkable tubing marked with ES-1 to seal wire splice. Refer to guide at bottom of procedure.
4. Strip 1/4" (6.35mm) of insulation from wire end, taking care not to nick or cut wire strands.
5. Slide heat shrink tubing onto one (1) of the wire ends to be crimped, must be a least 1" away from 
stripped end.
6. Identify the appropriate crimping chamber by matching the wire size on the dies with the wire size stamped 
on the wire splice.
7. Hold the crimping tool so the identified wire sizes are facing you. Squeeze tool handles together until the 
ratchet releases, then allow the jaws of the tool to open fully.
8.  Center one (1) end of the wire splice on the appropriate crimping chamber. If visible, be sure to place the 
brazed seam of the wire splice toward the indenter.
9. Hold the wire splice in place and squeeze the tool handles together until ratchet engages sufficiently to hold 
the wire splice in position. (Typically one or two clicks!) DO NOT deform the wire barrel.
10. Insert stripped wire into the wire barrel, making sure the insulation on wire does not enter the wire barrel.
11. Holding the wire in place, squeeze tool handles together until ratchet releases. Allow tool handles to 
open, then remove crimped wire splice.
12. To crimp the other half of the splice, reposition the uncrimped wire barrel in the same crimping chamber, 
and repeat steps 6-11.
CPR   2003 FORD MOTOR COMPANY
DEARBORN, MICHIGAN 48121
7-03
SK 3U2J-14293-AA
SHEET 1 OF 2
CRIMP/HEAT SHRINK WIRE SPLICE PROCEDURE
KIT CONTENTS
Part Number
Description
Quantity
Kit Specific                 Butt Splice
5
Kit Specific 
Shrink Tube 
5
SK 3U2J-14293-AA     Instruction Sheet
1
STRIP 1/4" (6.35mm)
1" (25.4mm)
CRIMPING CHAMBER
CAVITY
INDENTER
INDENTER
BRAZED SEAM
WIRE STOPPER


### 第 84 页
NOTE: If splice cannot be turned for crimping the other half, turn the tool around.
13. Perform a visual inspection of crimped wire splice.
Criteria for an acceptable crimp
1. Crimp is centered on each end of the butt slice. It is acceptable for crimp to be slightly off center, but NOT 
OFF THE END of the wire barrel.
2. Wire insulation does not enter wire barrel.
3. Wire is visible through inspection hole of splices.
Applying Heat Shrinkable Tubing to Seal and Protect the Wire Splice
1. Slide the heat shrink tubing over the crimped splice, ensure the tubing is centered over the wire splice.
2. Follow instructions for proper operation of the flameless heat gun.
3. Using the flameless heat gun with the shrink tubing deflector attachment, apply heat evenly to the middle of the
tubing working your way out to the ends. Rotate wire assembly to ensure complete shrinkage and heat until the
tubing shrinks into place or will shrink no further. Adhesive lining will melt and flow and should be visible at 
both ends of the tubing.
NOTE: Keep the heat source moving and at least 1" away from the tubing to prevent charring or burning.
4.
AIIow repair to cool before handling.
CPR   2003 FORD MOTOR COMPANY
DEARBORN, MICHIGAN 48121
7-03
SK 3U2J-14293-AA
SHEET 2 OF 2
CRIMP/HEAT SHRINK WIRE SPLICE PROCEDURE
SHRINK TUBING
ADHESIVE LINING
HEAT
REPLACEMENT COMPONENTS
Motorcraft Wire Splice/Heat Shrinkable Combination Packages
Motorcraft 
Wire   
Quantity 
Description
Markings on Tubing
Part Number
Gage Size
WT-56814
22-18  
5  
22-18 Uninsulated Wire Splices
ES-1 ES-1 repeated
5  
2" pieces of Dual Wall 
Heat Shrinkable Tubing
WT-56815
16-14    
5
6-14 Uninsulated Wire Splices     
ES-1  ES-1  repeated
5
2" pieces of Dual Wall
Heat Shrinkable Tubing
WT-56816
12-10 
5
12-10 Uninsulated Wire Splices   
ES-1  ES-1  repeated
5   
2" pieces of Dual Wall 
Heat Shrinkable Tubing
Recommended Installation Tools:
Part Number
Rotunda 164-R5901  
Ratchet Action Crimp Tool
Rotunda 164-R5902  
Shielded Flameless Heat Gun with Heat Deflector
Rotunda 164-R5903 
Wire Splice Tool Kit 
(kit contains one (1) crimp tool, one (1) heat gun and an assortment of all 
splices and tubing) 
Refer to Technical Service Bulletin Number  03-11-6


### 第 85 页
10
12
14
16
18
20
22
24
22-18
1/4” 
strip, 
cut 9 
strands
1/4” 
strip, 
cut 2 
strands
1/4” 
strip, no 
fold
1/4” 
strip, no 
fold
1/4” 
strip, no 
fold
5/8” 
strip, 
fold 2x 
dia.
1” strip, 
fold 3x 
dia.
16-14
1/4” 
strip, 
cut 7 
strands
1/4” 
strip, no 
fold
1/4” 
strip, no 
fold
1/4” 
strip, no 
fold
5/8” 
strip, 
fold 2x 
dia.
1” strip, 
fold 3x 
dia.
1 1/4” 
strip, 
fold 4x 
dia.
12-10
1/4” 
strip, no 
fold
1/4” 
strip, no 
fold
5/8” 
strip, 
fold 2x 
dia.
1” strip, 
fold 3x 
dia.
1 1/4” 
strip, 
fold 4x 
dia.
Pigtail Only
Both Pigtail and Wire Harness
Wire Harness Only
Butt Splice as stamped
Wire Gauge Size


### 第 86 页
Rotunda Part Number: 164-R5901
Ford Approved Crimp Tool 
22-10 AWG Wire Ranges
Heat Deflector
Spare Orifice Assembly
Rotunda Part Number: 164-R5901
Uninsulated Butt Splices
Material:  Copper (annealed) / Tin Plated
Maximum Recommended Operating 
Temperature:  338° F (170° C)
Recommended Crimping Tool:
Rotunda Part Number: 164-R5901
Use with solid or stranded wire 
Storage Container for Heat Shrinkable Tubing
Dual-wall heat shrinkable tubing is designed for insulating and sealing splices in an automotive 
environment. Specialty formulated hot-melt adhesive forms an outstanding barrier against fluids and 
moisture, helping to protect the harness from the effects of corrosion and water wicking.
Shrink ratio: 
Approximately 4:1
Minimum Shrink Temperature: 
275° F (135° C)
Operating Temperature: 
-40° F to 266° F 
-40° C to 130° C
ES-1
Rotunda Part Number: 164-R5902
Shielded Flameless Heat Gun
With Heat Deflector
Specifications: 
Approximate Temperature 250° - 500° C (480° - 930° F)
Gas Container Capacity: 20 ml
Operating Capacity: 120 minutes at mid setting
Actual inside diameter 
before shrinking
Actual inside 
diameter
22-18 AWG                 Insulate with ES-1
Motorcraft Part #:
WT-56814
Replenishment package contains:
5 splices / 5 heat shrinkable tubing
.225" 
22-18
ES-1
16-14
Actual inside diameter 
before shrinking
Actual inside 
diameter
16-14 AWG                 Insulate with ES-1
Motorcraft Part #:
WT-56815
Replenishment package contains:
5 splices / 5 heat shrinkable tubing
.225" 
ES-1
12-10
Actual inside diameter 
before shrinking
Actual inside 
diameter
12-10 AWG                 Insulate with ES-1
Motorcraft Part #:
WT-56816
Replenishment package contains:
5 splices / 5 heat shrinkable tubing
.225" 
ES-1
Refer to Crimp/Heat Shrink Wire Splice Procedure: 
SK 3U2J-14293-AA 
Technical Service Bulletin: 03-11-6


### 第 87 页
EESE-EDS                                                              FIBER OPTICS                                                              December 1999 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 87 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
FIBER OPTICS 
 
In many ways, a fiber optic distribution system is like an electrical distribution system.  Both distribute energy and signals 
from one point to another.  Both use long conductors that snake throughout the vehicle; both use connectors and terminals to 
interface to other components.  The major difference between the two is that the optical fibers carry light rather than 
electricity.  There are other differences that will be addressed later on. 
 
Because of this basic similarity, most of the guidance for designing electrical distribution systems can be easily applied to 
fiber optic systems.  This section will deal with the unique requirements of fiber optics.  Much of the guidance is based on 
information provided by AMP, Delphi and Yazaki.  NOTE:  the guidance here is applicable mainly to optical fiber used for 
carrying data.  Fiber used for light pipes may have requirements of its own. 
 
 
Typical optical fibers are rated for up to 85 °C.  Therefore, by our standard practices, their use is limited to the passenger 
compartment. 
 
When designing a fiber optic system, you must pay careful attention to the power budget.  The optical power generated at the 
transmitter must be greater than the losses generated by the optical fibers themselves and by the connector interfaces, along 
with the optical receiver sensitivity and a safety factor (typically 3 dB). 
 
CONNECTOR CONSIDERATIONS 
 
Per the EDS SDS, fiber optic connectors will need to be protected with a cap of some sort.  This will prevent the interface and 
terminals from being damaged or contaminated during manufacturing, shipping and vehicle assembly.  If the cap is to remain 
in the vehicle after assembly (say, on an unused takeout or one intended for aftermarket use), and if the vehicle architecture 
requires a continuous, unbroken light path, then the cap will need to function like an optical shorting bar. 
 
Do not use dielectric grease in a hybrid opto-electric connector.  This is in the sense of filling the cavity of the connector with 
grease to serve as a barrier to moisture, not in the sense of using greased electrical terminals in the connectors (this is 
allowed).  The grease in the cavity will contaminate the optical interface.  Note that it's very uncommon to use grease in this 
manner nowadays, as sealed connectors have become more prevalent.  Note also that this does not forbid any substance used 
to enhance the properties of the optical interface (i.e., optical gel). 
 
ROUTING AND RETENTION 
 
In general, optical fiber should be routed with the regular wiring, preferably as part of the same bundle.  This helps protect the 
fiber from damage.  Obviously, the fiber will have to route by itself for takeouts; it should be protected by convoluted tubing 
or other protective sleeving.  Of course, make sure that the fiber takeouts are clear of sharp edges, weld burrs or anything else 
that could kink, cut or otherwise damage the fiber.  Doing this, along with capping the connectors, should eliminate the need 
for any special devices or measures to protect the fiber in transit or during vehicle assembly. 
 
Minimize the twisting that can take place during harness manufacture and vehicle assembly.  Twisting of the fiber can distort 
the geometry of the core, leading to excessive attenuation of the optical signal. 
 
Minimize the amount of folding back of the fiber that occurs due to excess length during harness manufacture.  Otherwise, 
excessive attenuation of the signal may result. 
 
Design the system such that it's not necessary to disassemble any optical connectors (not counting the removal of optical 
shorting bars) if installing any optional or aftermarket modules that connect to the fiber. 
 
Bend radii:  Unlike copper wire, optical fiber is much more sensitive to being bent.  If the fiber is bent such that the bend 
radius is 25 mm or less, light loss will become excessive.  More light will be refracted out of the core and thus lost.  This will 
degrade the operation of the system and cause other systems to operate erratically.  Fortunately, this effect is temporary and 
will disappear if the fiber is straightened out.  However, if the bend radius is 5 mm or less, the fiber will be damaged beyond 


### 第 88 页
EESE-EDS                                                              FIBER OPTICS                                                              December 1999 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 88 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
repair.  The light losses will be permanent.  Per the EDS SDS, design the system such that the fiber is never bent to a radius of 
5 mm or less at any time, nor is it installed in the vehicle with a bend radius of 25 mm or less.  If possible, design it to a 10 
mm bend radius rather than 5 mm, which will provide an extra margin of safety.  This applies for all conditions: static, 
dynamic, and relative motion. 
 
If the power budget has sufficient excess optical power, then the system will be able to tolerate some excess bending, where 
the bend radius is smaller than 25 mm but greater than 5 mm.  However, it is best to design such that no excess bending 
occurs. 
 
Optical fibers exiting the back of connectors should extend out a minimum of 30 mm before being bent or formed into the 
routing path.  This is to avoid the excessive bend radius issue.  Naturally, if it's a hybrid connector, the wire bundle should 
follow the lead of the fiber and extend at least 30 mm before being formed into its path.  Similarly, any excess length that's 
provided for ease of service must be controlled such that the bend radius does not go below 25 mm (5 mm for sure). 
 
EMC issues:  Optical fiber does not conduct electricity, which means that it cannot conduct any electromagnetic interference. 
 Note, though, that the optical transceivers are still susceptible to EMI and RFI. 
 
SERVICE CONSIDERATIONS 
 
Servicing of the optical fiber and its connections will, in many cases, require special equipment to prepare the ends of the 
fiber to be spliced together.  The fiber manufacturer and/or the harness supplier will recommend what equipment service 
technicians will need.  It may include a device to terminate optical fibers and/or splice them together.  It may also include a 
metering device to rate signal strength, either with a true readout or a simple go/no go readout.  This equipment should be as 
small and inexpensive as possible.  It must not require laboratory-type "clean room" conditions to work properly; it must be 
usable in the assembly plant and the garage.  Work with FCSD to make the equipment available to the dealer community. 
 
If the fiber is damaged or broken due to cutting, chafing or excessive bending, it can either be repaired by splicing or by 
replacing.  If the fiber is to be replaced, it'll need to be retained to the existing harness bundles, either by taping or by tie 
straps.  The original fiber would remain in the harness but be bypassed by the replacement fiber.  If the fiber will be spliced, 
minimize the excess length, and make sure that that excess is not folded back on itself too sharply. 
 
What are the criteria for repairing by splicing and by replacing?  If the repair location is easily accessible, and if the power 
budget permits a splice, then it should be spliced.  But if the repair location is in a difficult area to access, if the power budget 
is tight, or if there have already been splices, then it should be replaced.   Since the service technicians will have no idea of 
the power budget, you'll need to evaluate your specific design and issue service recommendations accordingly. 
 


### 第 89 页
EESE-EDS                                                     42-VOLT CONSIDERATIONS                                                  October 2001 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 89 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
 
42-VOLT SYSTEMS: DESIGN CONSIDERATIONS 
 
Due to ever-increasing demand for electrical energy, the current 12-volt electrical system (more properly, a 14-volt system) is 
being overtaxed.  It cannot supply the energy needed by new systems.  To meet the demands, vehicles will now use a 42-volt 
electrical system.  It will not replace the standard 14-volt system but will co-exist alongside it, at least initially.  One of the 
benefits of the 42-volt system is that, for a given current, the available power is tripled (triple the voltage times the same 
current gives triple the power). 
 
Designing wiring for 42-volt systems presents some unique challenges to the EDS engineer, compared with the 14-volt 
system.  The higher energy in 42-volt systems requires that more care be given to routing, retention and protection of the wire 
bundles.  Connectors need to be more robust.  More details will be provided in a future edition of this design guide. 
 


### 第 90 页
 
Originator:  Roger Reini/RREINI 
Page 90 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 91 页
 
Originator:  Roger Reini/RREINI 
Page 91 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 


### 第 92 页
 
Originator:  Roger Reini/RREINI 
Page 92 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 93 页
EESE-EDS                                      SATISFY SYSTEM REQUIREMENTS & TIMING                                  October 2001 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 93 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
SATISFY SYSTEM REQUIREMENTS AND TIMING 
 
 
The previous section of the guide covered the technical requirements in designing an electrical distribution system. This 
section covers the non-technical requirements, such as program timing, the prototype build process, the program team 
process, etc.  These requirements are just as important as the technical requirements.  You can design the best wiring system 
in the world, but it's no good unless it satisfies cost, weight and reliability objectives and meets all timing requirements. 
 
DIVISION OF RESPONSIBILITIES BETWEEN FORD AND THE FULL-SERVICE SUPPLIER 
 
Responsibility for wiring system design engineering for a given vehicle belongs to the appropriate consumer business group, 
or CBG (also called "platform").  Under current business plans, the actual design work is contracted out to a "full-service" 
wiring supplier ("full service" means they can engineer, develop, and prove out their system).  This means that the design 
responsibility is split between the Ford design and release ("D&R") engineer(s) and the supplier engineers.  The electrical 
distribution D&R engineers are sometimes referred to as "platform engineers" to distinguish them from the "core engineers", 
whose work is cross-vehicle in nature. 
 
A sidenote: It is important to distinguish between the Ford D&R engineer and the Ford Vehicle Operations (formerly Body & 
Assembly) electrical system engineer.  The term "system engineer" should be reserved for the VO engineer, who is 
responsible for reviewing the assembly feasibility and developing the installation process for the wiring system at the 
assembly plant.  The D&R engineer, on the other hand, as the title implies, has official design & release responsibility for the 
wiring system. 
 
The Ford engineer(s) and supplier have distinct roles and responsibilities in the design process including, but not limited to, 
the following: 
 
Ford D&R Engineer 
 
• 
Heads up the wiring sub-team of the Electrical PMT 
• 
Facilitates FSS drawing releases 
• 
Develops and updates the EDS workplan 
• 
Leads the effort to adapt the generic EDS SDS into a program-specific SDS 
• 
Reviews and concurs with FSS-pulled WERS concerns, alerts and notices 
• 
Attends Change Control, other PMT and PAT meetings as required 
• 
Prepares program alerts when called for 
• 
Assists and facilitates preparation for technical design reviews (TDR's) and gives presentation in conjunction with 
the FSS 
• 
If on launch team, represents Wiring and Electrical Components (the old Body Electrical) 
 
Full Service Supplier 
 
• 
Does the detailed design work on the EDS 
• 
Does the detailed wire assembly drawings with their own CAD installation 
• 
Pulls WERS notices, concerns and alerts; prepares marked prints to accompany these as required 
• 
Makes entries in FDVS, the Ford Design Verification System 
• 
Performs DVP&R on the EDS -- circuit analysis, fuse blow tests, short circuit tests, breadboard tests, etc. 
• 
Attends Change Control, other PMT and PAT meetings as required 
• 
Supports the process of adapting the generic EDS SDS to a program-specific SDS 
• 
Prepares material for TDR's and gives presentation in conjunction with Ford platform engineer 
• 
Supports the launch with an appropriately sized team 
 
A more complete description of the roles and responsibilities of the EDS program teams is available in the lists of Roles and 
Responsibilities, which can be found online at the EESE ISO Web site (http://www.eese.ford.com/iso/, Ford internal). 
 
FORD PRODUCT DEVELOPMENT SYSTEM (FPDS) 


### 第 94 页
EESE-EDS                                      SATISFY SYSTEM REQUIREMENTS & TIMING                                  October 2001 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 94 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
World Class Timing, which had been the standard product development process at Ford, has been supplanted by the Ford 
Product Development System. Under both processes, a vehicle program is managed by dedicated, collocated, cross-functional 
teams to accomplish simultaneous engineering by using new tools and processes.  The focus of FPDS is on using time as a 
strategic resource by increasing the rate of information flow, which will allow more actions to proceed simultaneously.  This 
lets Ford respond more quickly to changes in market demand. 
 
A detailed explanation of the FPDS process is beyond the scope of this guide. For more information about FPDS, consult the 
FPDS Page on the Ford Web (http://www.fpds.ford.com/, Ford internal).  However, there are certain aspects of the 
processes that you should be aware of: 
 
Team Structure 
 
With few exceptions, all of the engineers working on a particular program will be assigned to a CBG, and they will be in the 
same office.  This ensures that they are dedicated to that program and will have no problems contacting and working with the 
other team members.  The wiring D&R engineer will be collocated with the team and will officially belong to the CBG, but 
his/her management will have a dotted-line relationship with EESE.  Connector engineers supporting the CBG's will be 
located with the connector section(s). 
 
Cross-functional teams manage different aspects of the product development process for each program.  There are three types 
of these cross-functional teams: 
 
Program Steering Team -- consists of the Program Manager and the project managers from the various engineering activities, 
Purchasing, VO, etc.  This team is in charge of the whole program.  It establishes total program direction; it resolves conflicts 
among PMTs and PATs; and it provides assistance to teams who are attempting but failing to get necessary information. The 
PST also establishes procedures and teams for managing change control. 
 
Program Module Teams -- These teams have responsibility for the various systems and components of the vehicle.  For 
electrical system teams, a management-level engineer from EESE (Electrical/Electronic System Engingering) will be the 
chairperson, with the Ford D&R wiring engineer heading up the wiring sub-team.  As a wiring engineer, you'll participate in 
the Electrical System PMT and are likely to be involved with other PMT's such as the Instrument Panel, the Seat system, etc. 
 
Key PMT tasks involve establishing the targets and objectives (piece cost, investment, weight, and reliability) for the systems 
and components it controls. These are done with the parts on the control model (base vehicle plus options with a 33% or 
greater take rate). Another task is ensuring parts are binned to the proper team. In the case of the Electrical team, PIA 
harnesses are binned to the team with the end item; thus, the 14401 "belongs" to the Instrument Panel team, the 12B637 to the 
Engine team, etc. Each program will determine how costs for its parts, both end item and PIA, are binned and tracked. 
 
The PMT, in conjunction with the Ford platform engineer, approves all requests for information from the full service 
suppliers that would cause an increase in workload and might jeopardize meeting key deadlines.  This is required by Wiring 
management. 
 
Sometimes it is desirable to hold meetings with subsets of the PMT.  Key submeetings involving wiring are the Wiring sub-
team (also called a PAT) meeting and finance meetings.  The Wiring sub-team meeting allows the discussion of wiring issues 
that the total PMT may not be interested in.  This meeting is important for keeping year-to-year continuity on a program.  
Finance meetings are useful when developing targets and objectives for the wiring system.  While the PMT is very interested 
in the ultimate targets/objectives, it is not interested in the discussions that lead to those numbers.  Remember, the electrical 
PMT encompasses more than just wiring. 
 
Program Activity Teams -- These special teams are formed for actions which cut across PMT boundaries (examples are Clay 
Development Team, Design Aid, special engineering issues such as cowl side packaging, etc.).  The term also applies to sub-
teams of a PMT, such as the wiring PAT. 
 
Early Sourcing 


### 第 95 页
EESE-EDS                                      SATISFY SYSTEM REQUIREMENTS & TIMING                                  October 2001 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 95 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
A key concept of both processes is Early Sourcing.  This is when a full service supplier is awarded the business for a 
particular system on the vehicle.  For wiring, suppliers for new programs are picked several years in advance.  Under FPDS, 
suppliers join the PMT shortly after <SI>. 
 
Key Program Documents 
 
Vehicle Program Plan (VPP) 
 
This is the complete plan for total program execution.  It is used by the Program Steering Team to monitor a program's 
progress and to identify and deal with problem areas as they occur.  Under WCT, this was known as the Total Program Work 
Plan, or TPWP. 
 
Program Parts List 
 
This provides a uniform method for acquiring and distributing part-specific data such as weight, cost, timing, etc.  These data 
are used to forecast workload, define program intent clearly, and provide necessary information for program control and 
successful prototype builds. 
 
Other Important Documents 
 
Part Cost & Weight Report (PCW) 
 
This document ties in closely with the PPL.  It is WERS-based, and it keeps track of the program end-item parts' cost (piece 
and investment) and weight.  PIA parts are not formally tracked; their costs are contained within their final assembly. 
 
KEY EVENTS FOR THE ELECTRICAL DISTRIBUTION SUBSYSTEM:  Consult the EDS workplan for a listing of 
key events for EDS. 
 
SYSTEM REQUIREMENTS:  What must you do in order to properly communicate your design actions to the Ford world? 
 Here is a short list: 
 
Prototype Signoffs   
 
Conducted before durability and crash testing; certify that you or your representative have inspected the vehicles prior to the 
test, that the parts are of the correct design level, and that the test can go forward 
 
Prototype Test Reviews 
 
These are conducted after testing; they certify that you and/or the FSS engineers have inspected the vehicle and give you the 
opportunity to have your parts returned for analysis. 
 
Technical Design Reviews 
 
These internal EESE reviews take place at key points in the program.  They follow a well-defined format consisting of FMA 
and FMEA reviews, build issues, materials approvals, quality of event summaries, etc.  Detailed proformas specifying what is 
required for each review will be available on the EESE Web site (http://www.eese.ford.com/, Ford internal). 
 
Program Alerts 
 
These are issued in order to "alert" the Program Team when you are unable to meet critical dates, usually an MRD or IPD.  
The most frequent reasons for issuing an alert are late program direction or a lack of design information at a freeze date.  See 
the GATHER TOGETHER ALL NECESSARY INFORMATION section for an example of when to issue a program 
alert. 
 
F/CMVSS Signoffs 


### 第 96 页
EESE-EDS                                      SATISFY SYSTEM REQUIREMENTS & TIMING                                  October 2001 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 96 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
These consist of schematic circuit diagrams or sketches which are submitted to the Safety Engineering office as proof of 
compliance with the applicable F/CMVSS requirements.  You must verify that your wiring meets those requirements, that 
your drawings are identical to the schematics/sketches. 
 
WERS (Worldwide Engineering Release System) 
 
This is the master release and change control system, which attempts to bring more discipline to the change and release 
process.  As an engineer, you will become very familiar with WERS, as you'll use it to respond to concerns, process changes, 
obtain listings of all your parts, etc.  A detailed description of WERS is beyond the scope of this guide; for more information, 
contact your department's project specialist. 
 
Concerns 
These are issues which may adversely affect your parts in some way (Example:  a harness takeout interferes with a subsequent 
assembly operation).  They can be written by almost everyone, but most frequently are written by VO engineers, assembly 
plant personnel, PVTs, or even by yourself. 
 
Notices 
Formerly called Product Change Requests (PCR's), these are used to close out concerns.  They release new part design levels 
which should reduce or eliminate the issues with the prior levels. 
 
Advance PCI 
The process of writing a change, revising the drawings in drafting, and processing their release ("PCI") can be a slow one.  
For concerns which must be resolved quickly, the process can be abbreviated by going "Advance PCI."  Your marked-up 
drawings become the officially released drawings, which Purchasing and Pre-Production use to obtain new parts.  With this 
procedure, incorporation of a change happens much faster than with the standard procedure. 
 
PM Alerts 
Used to implement temporary action immediately, without having to wait for the system to finish processing the permanent 
action.  Also used to allow temporary substitution of parts, temporary use of parts not meeting specs, and use of tooling out of 
spec in noncritical areas 
 
8D or 14D Reports 
Short for "8-Discipline" or "14-Discipline," these are written to summarize the handling of a concern, usually at the request of 
assembly plant management.  The reports summarize what the concern is, what the root cause is, what actions have been taken 
to eliminate the concern, verification of those actions, and steps taken to ensure the concern never repeats itself. 
 
 


### 第 97 页
 
Originator:  Roger Reini/RREINI 
Page 97 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 98 页
 
Originator:  Roger Reini/RREINI 
Page 98 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 99 页
EESE-EDS                                              VERIFY THAT THE PARTS WORK                                                    June 2001 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 99 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
VERIFY THAT THE PARTS WORK  
 
 
 
Once the subsystem design (either original or revised) is completed, it must be verified.  Its effectiveness and robustness must 
be demonstrated before it can be approved for production.  Insufficient verification has led to many difficulties in the past, 
from build problems at the assembly plants to warranty problems in the field.  These difficulties cost time, money, and 
goodwill, none of which the Company can afford to waste. 
 
Where can the design be verified? 
 
• 
In the tube or on the table (i.e., before hardware is available) 
• 
In the lab 
• 
In the vehicle 
 
In the Tube or On the Table 
 
This is the best place to verify the design.  Parts have not been made; tools have not been cut; and significant money has not 
been spent.  With the increased utilization of CAD and CAE, it is now possible to evaluate many of the electrical and physical 
characteristics of the system without making up test samples.  You can evaluate the fusing and short circuit characteristics.  
You can determine if there are sneak paths to power or ground which could cause unpredictable operation.  You can see if 
your parts will package in the electronic design aid buck.  You can verify that the retainer holes you asked for are present in 
the sheet metal. 
 
The former VC2 EDS section developed a print review process, which is to be conducted before every vehicle build.  Details 
are below. 
 
In the Lab 
 
CAE evaluation is useful, but it will never completely eliminate the need for physical testing.  The parts need to pass the 
applicable design validation tests (breadboard tests, short circuit tests, pull tests, etc.).  These tests, formerly associated with 
the EDS PVS, or Production Validation Specification, are now Design Verification Methods (DVM's) in the EDS SDS. The 
DVM's are the tests used to verify EDS system integrity.  Each SDS requirement will have at least one DVM associated with 
it. 
 
Applicable tests can also be found in the appropriate component specifications. 
 
In the Vehicle 
 
The parts may pass all lab tests, but if they don't fit in the vehicle, they are useless.  They must function electrically in the 
vehicle.  They must be able to be installed in the vehicle at production line rates.  They must survive the vehicle environment 
(temperature, humidity, corrosion, fluid compatibility, etc.)  They must not introduce other problems, such as squeaks and 
rattles or water leaks. 
 
The in-vehicle tests are also mentioned in the PVS and the SDS/DVC.   Some of the tests deal with the assembly and 
packaging process, such as design aid buck trials and build evaluations.  Others deal with the performance of the system in the 
vehicle environment (combined durability, accelerated corrosion test, etc.).  Note that changes affecting harness protection 
where SDS requirements are not being met must be proven out on durability. 
 
EDS Print Review Process 
 
As mentioned above, the former VC2 EDS section developed a process for ensuring print reviews before every vehicle build. 
 This was in response to the results of the CPF build for an unnamed 1998 vehicle, where 200 errors of various types were 
found.  This process is NOT an evaluation of the design of the system; it is used to ensure that there are no incompatibilities 
among the system design, the subsystem designs and the wiring design. 


### 第 100 页
EESE-EDS                                              VERIFY THAT THE PARTS WORK                                                    June 2001 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 100 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
Thanks to Steve Schondorf for providing this information. 
 
1. 
System schematic signoff from every subsystem engineer 
 
The system schematics are the primary means of design direction for the EDS.  If they are wrong, the electrical system will 
not function correctly, and harnesses will need to be reworked.  At the time of the wiring design freeze (typically 3 months 
before wiring MRD), the system schematics should be signed by all affected parties -- system engineers, subsystem engineers, 
and wiring engineers.  This will ensure that if the wiring agrees with the system schematic, the system will work properly.  
EESE system schematics should be used for the signoff; if they are unavailable, the wiring full service supplier's schematics 
can be used instead. 
 
2. 
Review wiring prints against the system schematics 
 
To ensure a good build, the prints need to be compared to the signed system schematics, to make sure they agree.  If the 
number of changes is high, or it's a new design, then every circuit shown on the schematics needs to be checked.  If the 
number of changes is low then, if you're confident that carryover circuits weren't inadvertently changed, the checks can be 
confined to just those circuits.  If you're not confident, then take no chances and verify everything.  It may be a tedious 
process, but better to catch errors at this point than on the vehicle. 
 
3. 
Packaging compatibility signoff from every sheet metal and trim engineer 
 
It is a given that retention devices on the wiring must align with their mating holes or studs on the sheet metal or trim.  At the 
time of the wiring design freeze, obtain the appropriate package drawings or CAD models from the appropriate engineers.  
These drawings or models need to show the holes, studs, tabs, slots, etc. needed for wire retention.  Coordinates of all these 
features should be included, and the drawings should be signed as the released parts. 
 
4. 
Review wiring prints against the package drawings or CAD models 
 
Now that the retention features have been verified, review the harness designs against the package drawings or CAD models.  
Check all of the retainer dimensions to ensure that they line up with their corresponding holes or studs.  Again, it's better to 
catch errors at this point than on the vehicle. 
 
Issues found in the review process should be documented and tracked through the EDS PAT until closed. 
 
Statistical Significance 
 
Verifications must be statistically significant, where feasible.  The sample of trial parts must be sufficiently large to ensure a 
high degree of confidence in the results.  How large is sufficiently large?  That topic is beyond the scope of this guide.  
Consult with someone familiar with statistical methods. 
 
Key Life Testing 
 
There is no single system-level key life test for the EDS.  Existing tests which serve as a surrogate key life test process include 
the ECAR connection capability review, the combined durability test, and the accelerated corrosion test. In addition, the 
wiring participates in the key life tests of other components, such as the seat, doors, I/P, etc. There is a key life test for 
connectors and terminals. 
 
Now for some notes about the durability tests.  Due to the limited sample size, a lack of problems found in durability does not 
guarantee a lack of problems in the field.  The durability test proves out design intent rather than proving out the design.  It'll 
catch gross errors or other major problems but may miss the subtle stuff. 


### 第 101 页
 
Originator:  Roger Reini/RREINI 
Page 101 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 102 页
 
Originator:  Roger Reini/RREINI 
Page 102 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 103 页
EDS - EESE                                                  FEEDBACK ON YOUR DESIGNS                                                 June 2001 
___________________________________________________________________________________________________ 
Originator:  Roger Reini/RREINI 
Page 103 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
 
FEEDBACK ON YOUR DESIGNS  
 
 
 
Feedback is the step that closes the cycle, that allows it to begin again.  As the accompanying figure (see next page) shows, 
you can receive meaningful feedback from over 30 different quality indicators.  Here is a list of those indicators: 
 
 
Key Indicators 
 
AWS (formerly SE-II) - a data base of dealer warranty repairs set up by the (WCC) warranty classification code. 
 
CQIS - Short for Common Quality Indicator System, this is a new clearinghouse for early repair information, where dealers 
can write down the complete description and solution of a concern, along with its effectiveness. 
 
SAQ – Single Agenda for Quality; measures the effects of vehicle problems on customer satisfaction. 
 
FSE - Field Service Engineer; FSE's within a district visit Ford/Mercury dealers on a regular basis to obtain further warranty 
definition and/or experience first time failure. Detail report and picture is forwarded to Engineering. 
 
Durability Testing - Vehicle Engineering's formal testing of prototypes vehicles, with a systematic matrix for follow up on 
each concern. 
 
First Run - Assembly Plant first run is a system of data collection and reporting to eliminate defects in the assembly plant 
prior to vehicle completion. 
 
SMAC - Service Manager Advisory Committee - Formal program to review product issues submitted from all dealers 
throughout the USA. Culminates with review of selected issues by Engineering Management in front of National Advisory 
Committee Members. 
 
SIR - Service Investigation Reports - Concerns write up provided by dealer technician from actual repairs in dealerships 
 
Dealer Visitation - Visitation of key dealership to engage in discussion of current product concerns by engineering 
personnel.  
 
Dealer Call in Program - A dealer program which requires dealers to call in to engineering prior to claim acceptance, to 
further understand dealer claims and repairs. 
 
Dealer Mail in Program - Dealers mark up specific concerns on pictorial representations of the vehicle, which are then 
mailed (at Ford expense) to FCSD.  This helps to further identify issues and speed up their resolution. 
 
 
Other Indicators 
 
Lease Car Survey Program - Lease vehicle evaluation reports are compiled for identification of specific concerns. BE 
warranty section has an engineer monitoring concerns from the lease car garage. 
 
TGW -  Things Gone Wrong, a marketing survey which uses questionnaires mailed to new car owners after a specified period 
of time, typically 3 months and 3 years.  Note that, because wiring is invisible to the end customer, few if any TGW's are 
reported against it.  They typically are reported against the affected systems.  For example, a problem with the wiring 
associated with the radio will be reported against the radio.  It is possible to calculate the percentage of TGW's associated 
with wiring for a given system by using the percentage of warranty claims for that system that involve wiring.  So if 17% of 
radio system warranty could be traced to wiring, then 17% of radio TGW's could be allocated to wiring. 
 


### 第 104 页
EDS - EESE                                                  FEEDBACK ON YOUR DESIGNS                                                 June 2001 
___________________________________________________________________________________________________ 
Originator:  Roger Reini/RREINI 
Page 104 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
Inspection Studies - Done to improve understanding of concern areas of product dissatisfaction reported by owners. Perform 
actual inspection of owner's vehicles, discuss directly with owners their concerns, inspect and drive owners' vehicle. 
  
Probe Studies - Used to obtain volunteered perceptions of product quality directly from owners, expressed in their own 
words. 
 
NVQ - New Vehicle Quality Study, two month old vehicles - surveyed quarterly 
 
CNVQ - Competitive New Vehicle Quality Study, three month old vehicles - surveyed semi-annually, Ford and competitors' 
cars and light trucks.  
 
DTS - Durability Tracking Study - one to three year old vehicles, surveyed semi-annually, Ford and competitors' cars and 
light trucks.  
 
QTS - Quality Tracking Study - eight to 48 months old vehicles - surveyed annually, Ford and competitors' cars. 
 
M-10 Reports - Resident Engineers for each PEO assigned to the Assembly Plant drive and evaluate production vehicles 
built that day at their respective assembly plant; report is forwarded to VEO. 
 
Dual Dealership Study - Selected dealerships who sell both Ford and competitive vehicles are surveyed by Ford Personnel. 
Competitive claims are reviewed and coded similarly to the Ford warranty system to provide direct repair rate comparison. 
 
Fleet Evaluation Programs - Gelco/GRI - Vehicles are assigned to a particular fleet service; their performance is monitored 
over extended periods to identify product concerns. 
 
Top Five Supplier - SQI program with data from the assembly plant that identifies the five worst suppliers from each 
assembly plant. 
 
Railhead Audits - Specific areas throughout the USA and Canada where all vehicles are shipped by train and stored until 
transfered to delivery trucks for final shipment to the dealer. Ford vehicles are audited and sometimes arrangements can be 
made for competitive vehicle audits. 
 
NOVA - North American Overall Vehicle Audit, audit teams visit the assembly plant, review production vehicle quality for 
major subsystems deficiencies. A numerical ranking of the audit is developed. 
 
WRAP - Assembly Plant Warranty Reduction Audit Program, is an electrical audit after the vehicle is built which includes 
100% functional/visual evaluation of the electrical system of the vehicle and serves to protect the customer beyond Trim, 
Chassis and FAI inspections. Defects are identified and driven back into the plant system. 
 
500 Vehicle Audit - a program controlled by B&A SQA in the assembly plant to audit three areas, with SQA and Suppliers. 
In addition, assembly concerns and design concerns are also noted and reported. 
 
IQ Inspection - Assembly plant incoming inspection program, parts are inspected for critical dimensions prior to installation. 
 
Supplier Manufacturing Concerns - concerns that affect fixture build and eliminates historical defects.  
 
Buck Reviews - partial vehicles or engines are used to route and display wiring and other devices for eventual management 
reviews and sign-off. 
 
FMEA - Potential Failure Mode and Effects Analysis; all potential failure modes and causes associated with designing, 
manufacturing and processing are analyzed and corrected prior to production release. 
 
High Mileage - High Mileage Durability Performance includes inspection studies of high mileage vehicles. Items are 
generally durability failures. 
 


### 第 105 页
EDS - EESE                                                  FEEDBACK ON YOUR DESIGNS                                                 June 2001 
___________________________________________________________________________________________________ 
Originator:  Roger Reini/RREINI 
Page 105 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
These sources can provide useful information for improving and enhancing your part designs.  You'll still need to investigate 
the issues these indicators point out, but you'll have direction on how to proceed. 
 
 
This is NOT the end of the design process cycle; in fact, it can be described as a new beginning, because the information 
obtained here will be used to produce better quality designs.  The underlying philosophy is one of continuous improvement, 
which means that the cycle continues forever -- a never-ending design process.


### 第 106 页
 
Originator:  Roger Reini/RREINI 
Page 106 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 107 页
 
Originator:  Roger Reini/RREINI 
Page 107 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
 
APPENDICES 
 
• GLOSSARY 
• LAUNCH 
• ILLUSTRATIONS FROM COMPONENT 
WIRING DESIGN GUIDE 
 
 
 


### 第 108 页
 
Originator:  Roger Reini/RREINI 
Page 108 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 109 页
EESE-EDS                                                                GLOSSARY                                                                       June 2001 
___________________________________________________________________________________________________ 
Originator:  Roger Reini/RREINI 
Page 109 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
GLOSSARY 
 
Here is a glossary of common terms and expressions used in the wiring community. 
 
Term 
Definition 
A/C 
Air conditioner; air conditioning 
ABS 
Antilock brakes 
AWG 
American Wire Gage; the traditional wire sizing method in North America 
B+ 
Battery positive potential 
B+ Feed 
An unswitched, hot-at-all-times power circuit 
Buck 
Mockup of a vehicle or a section of a vehicle; may be either physical or virtual; used for packaging 
components 
Crimp 
Attachment of a terminal to the end of a circuit 
C0AF 
Refers to ES-C0AF-14A121-A, the old North American harness manufacturing spec.  It has been supplanted 
by ES-F65B-14A121-AA. 
DFA 
Design For Assembly 
DVP&R 
Design Verification Plan and Report 
ECAR 
Electrical Connector Acceptability Rating; a method for determining the ease of mating electrical connectors 
EDS 
Electrical Distribution System; the vehicle wiring, including pigtails/flyleads 
EESE 
Electrical and Electronic System Engineering 
EMC 
Electromagnetic Compatibility; encompasses all types of electromagnetic interference 
End Item 
A part that is installed on the vehicle at the main assembly plant, as opposed to being installed on another part 
that is then installed on the vehicle 
F/CMVSS 
(U.S.) Federal/Canadian Motor Vehicle Safety Standards 
FCSD 
Ford Customer Service Division 
Final 
Assembly 
See End Item 
Flyleads 
See Pigtails 
FMA 
Failure Mode Analysis 
FMEA 
Failure Mode and Effects Analysis 
FPDS 
Ford Production Development System; the standard for vehicle program timing 
FSS 
Full-Service Supplier 
FWD 
Front-Wheel Drive 
F65B 
Refers to ES-F65B-14A121-AA, the main manufacturing specification for North America.  The European 
manufacturing spec is ES-XS71-14A121-AA. 
Heated 
Backlight 
Electric rear-window defroster 
HEGO 
Heated Exhaust Gas Oxygen; a type of engine control sensor 
Locator 
Synonym for Retainer.  Historically, a locator had a male retention feature (Christmas tree or arrowhead). 
MAF 
Mass Air Flow; a type of engine control sensor 
PAT 
Program Activity Team; applies to activities that cut across PMT's or to sub-teams of a PMT (the EDS PAT) 
PCW 
Part Cost and Weight System; part of WERS; tallies these values for all components on a vehicle 
PDB 
Power Distribution Box; home to high-current and low-current fuses, relays, etc. 
PEO 
Product Engineering Office; an older term for referring to engineering activities 
PIA 
Purchased In Assembly; refers to parts that arrive at the assembly plant already attached to another part, such 
as the engine wiring 
Pigtails 
Wiring that's released as part of an electrical component, that interfaces directly to the component rather than 
by a connector.  Generally called flyleads in Europe.  They are not released by the wiring team but by the 
component owner. 
PMT 
"Program Module Team; the team that's responsible for the design of a specific chunk of the vehicle content 
(i.e., the Electrical PMT)" 
PSF 
Refers to wiring identification and color code specification ES-5C3T-1274-AA 
PVC 
Polyvinyl chloride; one of the legacy wire insulation materials 


### 第 110 页
EESE-EDS                                                                GLOSSARY                                                                       June 2001 
___________________________________________________________________________________________________ 
Originator:  Roger Reini/RREINI 
Page 110 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
QFD 
Quality Function Deployment 
Retainer 
Component used to retain the wiring in the vehicle, on a part, etc.  See also Locator.  
RWD 
Rear-Wheel Drive 
SDS 
System Design Specification 
Splice 
Junction of 2 or more circuits where the ends of the circuit are clipped or welded together 
Takeout 
A branch of a harness that carries the circuits going to one or more components 
TGR 
Things Gone Right; similar to TGW, but measures customer satisfaction 
TGW 
Things Gone Wrong; a measure of customer dissatisfaction with his/her vehicle 
TPS 
Throttle Position Sensor 
Trustmark 
The highest-level corporate requirements; formerly known as WCR (Worldwide Customer Requirements) 
USCAR 
Pronounced "U-S-CAR"; US Council on Automotive Research 
V.O. 
Vehicle Operations 
WCR 
See Trustmark 
WERS 
Worldwide Engineering Releasing System; the system by which parts and specifications are released for use 
in vehicle production 
XLPE 
Crosslinked polyethylene; one of the legacy wire insulation materials 
 


### 第 111 页
EESE-EDS                                  HOW TO SUCCESSFULLY LAUNCH A VEHICLE                                      March 1997 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 111 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
HOW TO SUCCESSFULLY LAUNCH A VEHICLE 
 
One of the most important stages of a vehicle program is the launch, the process to get the vehicle ready for full production at 
the assembly plant.  Like the overall program, launch involves all activities in the company.  However, some areas become 
more important during the launch, notably B&A, including Systems, the plant, and Pre-Production.  All parties will need to 
work with your team to ensure a successful launch.  "Successful" means that your parts should be available on-time with very 
few concerns. 
 
Here are some things to do and things to avoid during launch.  This was compiled from lists provided by engineers from 
Wiring, B&A, and full-service suppliers, presenting experiences from past launches.  Many of these items are simple for your 
team to implement; others will require cooperation from Launch Team management. 
 
 
Things To Do During Launch 
 
PROCESS 
 
The Wiring and/or Electrical System PMTs must be established very early in the program (preferably at <PD> or before).  
This allows the system architecture to be defined by the Electrical team, not by the PST or other PMTs. 
 
Members of the Electrical PMT should be active in other PMTs and PATs to make sure the integrity of the EDS is not 
compromised. 
 
There needs to be a good working relationship between the PMT and the Change Control Committee.  This makes it easier to 
get changes incorporated faster and allows the PMT to address more important issues, such as Best-In-Class designs and EDS 
optimization. 
 
There needs to be early involvement by the assembly plant core team of hourly and salaried personnel.  They need to 
participate in the early builds (EP, VP, CPF, and Emphasis builds) at the Pilot Plant and help to identify concerns that would 
otherwise not be found until the assembly plant builds.  If the vehicle is to be built at more than one plant, then each plant 
should send a team. 
 
The FSS needs to provide excellent support throughout the launch, whether on call or on site. 
 
There needs to be a good working relationship between the core team members. 
 
The Wiring PMT must hold firm to wiring freeze dates, making good on promises to force delays in builds due to lack of 
necessary information.  This strategy proved successful on the 1993 FN10 launch.  The team had to slip the EP builds back 
several weeks due to incomplete information; the PST didn't like it, but they made sure the information for the VP build was 
provided on time. 
 
There must be an aggressive effort to obtain the necessary design information before the freeze dates.  AFL's Subsystem 
Status Summary for the 1993 FN10 (unofficially known as the "fink list") was an excellent tool to alert the design engineers to 
their responsibilities and to inform Program Management about roadblocks to a quality event. 
 
Try to minimize the number of issue matrices and tracking meetings.  Frequently, the same issue will be tracked on several 
matrices simultaneously, which is inefficient and very time consuming.  Ideally, there should only be one matrix the team 
follows, and all issues should be rolled into it.  This will require cooperation from Launch Team management. 
 
To minimize suffix bumps, issues affecting the same harness should be combined into one concern.  It should not be sent to 
Change Control until the issues are reviewed by all affected activities and resolution is discussed. 
 
In this electronic age, the ability to access WERS and PROFS is a must.  There needs to be sufficient computers and terminals 
for everyone to avoid jam-ups.  This applies to the Full Service Suppliers, as well.  They should be able to tie into their 
company's E-mail network (which most likely interconnects with PROFS).  If this is not feasible, then Launch Team 
Management should seriously consider reserving some PROFS IDs for supplier use. 


### 第 112 页
EESE-EDS                                  HOW TO SUCCESSFULLY LAUNCH A VEHICLE                                      March 1997 
____________________________________________________________________________________________________ 
 
Originator:  Roger Reini/RREINI 
Page 112 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 
Customarily, concerns relating to build issues should be raised by B&A.  Concerns related to usage changes, release changes 
and other procedural matters should be raised by the Ford engineer or the supplier.  If the B&A engineer requests it, the 
Ford/supplier engineer may also raise build concerns; this is done to ease the workload on the system engineer. 
 
PLANT 
 
The functional test developed by B&A and AFL for the 1993 FN10 launch was an excellent tool for functioning the vehicle 
and generating concerns (cited by Gavin Haag in his list of what went right).  This list should be adopted by B&A as a 
standard. 
 
Assembly plant salaried and hourly personnel should participate in Engine Compartment assembly tryouts before the EP and 
VP/CPF  builds.  This proves invaluable for early concern investigation. 
 
Plant personnel need to attend the main team concern matrix meetings.  And those personnel who do attend need to have 
sufficient authority to speak to assembly plant issues (tooling, sequence, process, material handling. etc.).  They must be 
prepared to accept assignments as well as give them out.  If this is not done, then assembly plant issues will not be resolved. 
 
The plant must provide sufficient physical facilities for both the Ford engineer and the supplier engineers.  This will usually 
happen when the supplier has or will have a permanent on-site representative at the plant.  It should happen all of the time. 
 
Review each work station that installs wiring.  Review each part installation if time and scheduling permit.  Compare the 
illustrations and process sheets with the real world process; adjust if necessary. 
 
FORD ENGINEER 
 
When you go out on launch, you not only represent the Wiring group, you also represent the Components group.  Because of 
FSS involvement, the most true engineering and actual involvement with physical parts you have will be with Components.  
You interface with the particular component engineer or with their designated program contact, briefing them on the problem 
and receiving direction on how to proceed.  Because this is launch, you will be processing their changes, routing concerns, 
following the pink count, prodding engineers into doing what they need to do, etc. 
 
FULL-SERVICE SUPPLIER 
 
During the launch, you will frequently be asked for cost implications and timing on possible actions.  You may not be able to 
produce these numbers immediately, in which case you'll need to check with the home office.  This should be satisfactory, 
provided the turnaround is fast enough (usually 48 hours or less). 


### 第 113 页
 
Originator:  Roger Reini/RREINI 
Page 113 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 114 页
 
Originator:  Roger Reini/RREINI 
Page 114 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 115 页
 
Originator:  Roger Reini/RREINI 
Page 115 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 116 页
 
Originator:  Roger Reini/RREINI 
Page 116 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 


### 第 117 页
 
Originator:  Roger Reini/RREINI 
Page 117 of 122 
Date Issued: Nov. 1, 1988 
guide.doc 
 
Date Revised:  08/08/03 
 
 


### 第 118 页


### 第 119 页


### 第 120 页


### 第 121 页


### 第 122 页

