# Q-FPT_2800001_CH_2015-01_整车电子电器零部件一般技术标准-环境耐久.pdf

## 文档信息
- 标题：<4D6963726F736F667420576F7264202D20512D46505420323830303030315F43685F323031352D30315FD5FBB3B5B5E7D7D3B5E7C6F7C1E3B2BFBCFED2BBB0E3BCBCCAF5B1EAD7BC2DBBB7BEB3C4CDBEC32E646F6378>
- 作者：yanyinfeng
- 页数：104

## 文档内容
### 第 1 页
Q/FPT 
北汽福田汽车股份有限公司企业标准 
Q/FPT 2800001—2015 
取 代  
Q/FPT 
2800001‐2011 
 
 
 
 
 
 
 
 
 
 
 
整车电子电器零部件一般技术标准‐环境/耐久 
 
 
 
批准 
（完成日期：2015‐01‐05） 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
2015‐01‐05
颁
布                            
2015‐01‐05 执行 
 
北汽福田汽车股份有限公司      发行 
目录 


### 第 2 页
前言……………………………………………………………………………………………………………………….III 
1  应用范围………………………………………………………………………………………………………………1 
2  参考资料………………………………………………………………………………………………………………1 
3  总则……………………………………………………………………………………………………………………..2 
    3.1  术语和定义…………………………………………………………………………………………………..2 
    3.2  工作电压范围……………………………………………………………………………………………….6 
    3.3  性能验证……………………………………………………………………………………………………….6 
4  电气要求和测试…………………………………………………………………………………………………10 
    4.1 E1 寄生电流………………………………………………………………………………………………….13 
    4.2 E2 电源中断………………………………………………………………………………………………….13 
    4.3 E3 电池电压变化………………………………………………………………………………………….14 
    4.4 E4 正弦叠加电压………………………………………………………………………………………….15 
    4.5 E5 脉冲叠加电压………………………………………………………………………………………….16 
    4.6 E6 信号电路和负载电路中的短路……………………………………………………………….18 
    4.7 E7 开路—单线中断………………………………………………………………………………………19 
    4.8 E8 开路—多线中断………………………………………………………………………………………21 
    4.9 E9 接地偏移………………………………………………………………………………………………….21 
    4.10 E10 不连续输入电压………………………………………………………………………………….22 
    4.11 E11 过载—所有电路………………………………………………………………………………….25 
    4.12 E12 绝缘电阻……………………………………………………………………………………………..25 
    4.13 E13 过电压………………………………………………………………………………………………….26 
    4.14 E14 瞬时过电压………………………………………………………………………………………….27 
    4.15 E15 瞬时低电压………………………………………………………………………………………….28 
    4.16 E16 跳线跨接启动………………………………………………………………………………………29 
    4.17 E17 抛负载………………………………………………………………………………………………….31 
    4.18 E18 短时中断………………………………………………………………………………………………32 
    4.19 E19 启动脉冲……………………………………………………………………………………………..33 
    4.20 E20 智能发电机控制电压曲线………………………………………………………………….36 
    4.21 E21 反极性…………………………………………………………………………………………………38 
    4.22 E22 绝缘强度…………………………………………………………………………………………….40 
    4.23 E23 反馈………………………………………………………………………………………………………41 
    4.24 E24正常和最坏情况性能分析……………………………………………………………………42 
    4.25 E25 短路/开路分析…………………………………………………………………………………..42 
5  机械要求和测试……………………………………………………………………………………………….43 
    5.1 M1 振动测试…………………………………………………………………………………………….…43 
    5.2 M2 机械冲击……………………………………………………………………………………………….52 
    5.3 M3 耐久冲击测试……………………………………………………………………………………….53 
    5.4 M4 外壳撞击—肘部负荷……………………………………………………………………………54 
    5.5 M5 外壳撞击—脚部负荷……………………………………………………………………………55 
    5.6 M6 跌落测试……………………………………………………………………………………………….56 
    5.7 M7 共振频率分析……………………………………………………………………………………….56 
    5.8 M8 高海拔运输压力效应分析………………………………….……….………………………..57 
    5.9 M9 塑料卡扣固定件分析……………………………………………….……………………………57 
    5.10 M10 碾压测试……………………………………………………………………………………………58 


### 第 3 页
    5.11 M11 振动噪声……………………………………………………………………………………………58 
    5.12 M12 接插件测试………………………………………………………………………………………..58 
6  环境要求和测试………………………………………………………………………………………………..59 
    6.1 C1 高低温存放……………………………………………………………………………………………..59 
    6.2 C2 低温工作…………………………………………………………………………………………………59 
    6.3 C3 温度梯度测试…………………………………………………………………………………………60 
    6.4 C4 重新喷漆温度…………………………………………………………………………………………61 
    6.5 C5 温度冲击（零部件，无外壳）……………………………………………………………..61 
    6.6 C6 溅水温度冲击………………………………………………………………………………………..62 
    6.7 C7 浸水温度冲击………………………………………………………………………………………..65 
    6.8 C8 循环湿热测试………………………………………………………………………………………..66 
    6.9 C9 稳态湿热测试………………………………………………………………………………………..67 
    6.10 C10 工作时盐雾测试，内部…………………………………………………………………….68 
    6.11 C11 工作时盐雾测试，外部…………………………………………………………………….69 
    6.12 C12 防水测试……………………………………………………………………………………………70 
    6.13 C13 冰冻测试……………………………………………………………………………………………71 
    6.14 C14 含电气总成的冷凝测试…………………………………………………………………….71 
    6.15 C15 防尘测试……………………………………………………………………………………………73 
    6.16 C16 日照……………………………………………………………………………………………………74 
    6.17 C17 化学测试和要求………………………………………………………………………………..74 
    6.18 C18 有害气体测试……………………………………………………………………………………76 
    6.19 C19 高空工作过热分析……………………………………………………………………………77 
    6.20 C20 热疲劳分析……………………………………………………………………………………….78 
    6.21 C21 无铅焊分析……………………………………………………………………………………….78 
7  使用寿命测试………………………………………………………………………………………………….79 
    7.1 L‐01 寿命测试—机械/液压耐久测试………………………………………………………..79 
    7.2 L‐02 寿命测试—高温耐久测试…………………………………………………………………80 
    7.3 L‐03 寿命测试—温度循环测试…………………………………………………………………83 
8  分析…………………………………………………………………………………………………………………86 
    8.1  分析任务…………………………………………………………………………………………………..86 
    8.2  开发任务…………………………………………………………………………………………………..86 
    8.3  设计验证（DV）任务………………………………………………………………………………86 
    8.4  产品验证（PV）………………………………………………………………………………………86 
9  附录…………………………………………………………………………………………………………………88 
    9.1  安装区域………………………………………………………………………………………………….88 
    9.2  流程…………………………………………………………………………………………………………94 
    9.3 A/D/V 活动总结……………………………………………………………………………………….99 
    9.4  测试顺序计划…………………………………………………………………………………………102 
    9.5  高低温耐久测试计算模型……………………………………………………………………..108 
    9.6  温度循环耐久测试计算模型………………………………………………………………….113 
    9.7 运行模式………………………………………………………………………………………………….116 
 
 
 


### 第 4 页
前言 
本标准根据国家和行业标准制定。 
本标准由北汽福田汽车股份有限公司工程研究院提出。 
本标准由北汽福田汽车股份有限公司乘用车电器电子部门工程研究院起草。 
本标准起草人：Wang Qingping; Zhu Chuangang 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 5 页
1  应用范围 
 
本标准规定了乘用车的电气/电子零部件以及机动车辆电气电子、机电一体化零
部件/系统的特殊要求、测试条件和测试内容。其他附加偏差要求、测试条件和
测试内容见零部件设计任务书。 
注意：本标准中的测试不能代替零部件认可或制造过程认可。 
注意：本标准不能代替法律法规。 
注意：以中文版为准。 
 
2  参考资料 
 
如无其他规定，采用最新版本的标准 
ASTM D4728  集装箱随机振动测试的标准测试方法 
IEC 60068‐2‐1  环境测试—第 2‐1 部分：测试—测试 A：低温 
IEC 60068‐2‐2  环境测试—第 2‐2 部分：测试—测试 B：干热 
IEC 60068‐2‐11 环境测试—第 2‐11 部分：测试—测试 Ka：盐雾 
IEC 60068‐2‐14 环境测试—第 2‐14 部分：测试—测试 N：温度变化 
IEC 60068‐2‐27 环境测试—第 2‐27 部分：测试—测试 Ea：冲击 
IEC 60068‐2‐29 环境测试—第 2‐29 部分：测试—测试 Eb：碰撞 
IEC 60068‐2‐30 环境测试—第 2‐30 部分：测试—测试 Db：循环湿热 
IEC  60068‐2‐38 环境测试—第 2‐38 部分：测试—测试 Z/AD：组合温度/湿度循环
测试 
IEC 60068‐2‐52 环境测试—第 2‐52 部分：测试—测试 Kb：盐雾循环 
IEC 60068‐2‐60 环境测试—第 2‐60 部分：测试—测试 Ke：混合气体腐蚀测试 
IEC 60068‐2‐64 环境测试—第 2‐64 部分：测试—测试 L：粉尘测试 
IEC 60068‐2‐78 环境测试—第 2‐78 部分：测试—测试 Cab：稳态湿热 
ISO  11124‐2  使用油漆和相关产品前的钢基板准备—喷砂清理用金属磨料规范—
第 2 部分：冷淬铁砂粒 
ISO 12103‐1  道路车辆—过滤器评估用测试粉尘 
ISO 16750‐1  道路车辆—电气电子设备的环境条件和测试—第 1 部分：总则 
ISO 16750‐2  道路车辆—电气电子设备的环境条件和测试—第 2 部分：电气负载 
ISO 16750‐3  道路车辆—电气电子设备的环境条件和测试—第 3 部分：机械负载 
ISO 16750‐4  道路车辆—电气电子设备的环境条件和测试—第 4 部分：气候负载 
ISO 20567‐1  涂料和清漆—涂层的耐石片划痕的测定—第 1 部分：多冲击测试 
ISO 20653  道路车辆—IP 防护等级—电气设备防尘防水等级 
ISO 6270‐2 涂料和清漆—耐湿性测定—第 2 部分：冷凝水环境下的测试样品方法 
ISO 8820  标准 ATO/ATC 保险丝 
DIN 72552  汽车接线柱标识 
DIN EN ISO 17025  测试能力和校准实验室的基本要求 
UL94  用电器中零件的塑性材料的阻燃性测试 
 
3  总则 
 
3.1  术语和定义 


### 第 6 页
3.1.1  电压和温度定义 
表 1：电压电流缩写 
UN 
测试过程中零部件工作而发电机不工作时的额定电源电压（也就是蓄电
池电压） 
UBmin 
最小工作电压 
UB 
测试过程中零部件工作且发电机工作时的额定电源电压（也就是交流发
电机电压 
UBmax 
最大工作电压 
Umax 
测试过程中可能会出现的最大电压 
Umin 
测试过程中可能会出现的最小电压 
UPP 
峰峰电压 
Ueff 
电压有效值 
Utest 
测试电压 
IN 
额定电流 
GND 
器件接地 
Tmin 
最低工作温度 
Tmax 
最高工作温度 
TPH 
后热温度，汽车停止后以及零部件短期工作后可能会出现的最高环境温
度，例如发动机或周边环境温度 
TRPS 
重新喷漆&存放温度，重新喷漆但零部件不工作时的最高温度 
Troom 
室温 
Ttest 
测试温度 
Top,min 
有过载/超温保护的零部件的最低工作温度 
Top,max 
有过载/超温保护的零部件的最高工作温度 
Tmin_S 
最低存放温度 
Tmax_S  最高存放温度 
Tcool,min 
冷却管路中的最低冷却液温度 
Tcool,max 
冷却管路中的最高冷却液温度 
 
3.1.2  时间和时长 
表 2：时间和时长缩写 
tr 
（例电压曲线的）上升时间 
tf 
（例电压曲线的）下降时间 
ttest 
测试时长 
所有边缘描述参考电压值的 10%或 90%。 
 
3.1.3  其他 
表 3：其他定义 
Ri 
电压电源的内阻 
接线柱标记 
DIN 72552 
f 
频率 
3.1.4  参数公差 
 


### 第 7 页
如无其他规定，所有验证测试采用下列测试环境参数和公差（见表 4）： 
表 4：参数和公差 
参数 
公差 
最高温度 
标准值（2℃，0） 
最低温度 
标准值（0，‐2℃） 
室温 
（+23±5）℃ 
测试时间 
标准值±0.5% 
室内环境相对湿度 
（30~70）% 
测试箱相对湿度 
标准值±5% 
最大电压 
标准值（+4%，0%） 
最小电压 
标准值（0%，‐4%） 
电流 
标准值±2% 
电阻 
标准值±10% 
随机加速度（GRMS） 
±20% 
加速度 
标准值±20% 
频率 
±1% 
力值 
±10% 
距离 
标准值±5% 
备注： 
公差值只和额定值一起使用，若无福田乘用车验证工程师的许可，设置好的额定
值不能在公差范围内进行调整。 
 
3.1.5  标准值 
 
如无其他规定，以下参数适用于所有验证测试（见表 5）： 
表 5：参数定义 
室温 
Troom=23℃±5℃ 
湿度 
45%~75% 
测试温度 
Ttest= Troom 
额定电压 
UN=12V 
工作电压（测试用） 
UB=14V 
 
3.1.6  功能状态等级（FSC） 
FSC 是指零部件的功能性能。每项测试都需规定样件的功能状态。其他要求需注
明在零部件技术规范中。 
记忆功能必须一直保持在 A 级功能状态，任何时候都需保证非易失性存储器的完
整性（非最新的）。零部件技术规范需规定功能状态的时间顺序。 
如果有事件日志记录，需与采购方确认并作出相应规定。 
 
 
 
 
 
 


### 第 8 页
表 6：等级定义 
等级 
FSC 等级定义 
A 
测试期间以及测试结束后，零部件的所有功能均按照设计进行运
转。 
B 
测试期间零部件的所有功能按照设计一样进行运转；然而这些功
能中可能有 1 个或多个功能超出公差。测试结束后所有功能都能
自动回到正常值内。记忆功能应维持在 A 级。 
功能状态等级为 B 的零部件也可以为 A 级。 
C 
测试期间零部件的一个或多个功能不能按照设计值进行运转，但
测试结束后会自动恢复到正常运转。 
功能状态等级为 C 的零部件也可以为 A 级或 B 级。 
D 
测试期间零部件的一个或多个功能不能按照设计值进行运转，但
测试结束后通过简单的”操作者/用户”复位操作对零部件进行重
置就会自动恢复到正常运转。 
功能状态等级为 D 的零部件也可以为 A 级/B 级/C 级。 
E 
测试期间零部件的一个或多个功能不能按照设计值进行运转，测
试结束后，如果没有维修或更换零部件就不能自动恢复到正常运
转。 
功能状态等级为 E 的零部件也可以为 A 级/B 级/C 级/D 级。 
 
3.1.7  工作模式 
表 7：工作模式电气状态 
工作模式 
电气状态 
1 
零部件未电气连接 
 
1.1 
零部件未电气连接，无插头和线束 
 
1.2 
零部件未电气连接，但已插上插头和线束 
2 
零部件以额定电源电压 UN 电气连接 
 
2.1 
未激活零部件功能（如处于睡眠模式，关闭模式） 
 
2.2 
零部件以最小工作负载运行 
 
2.3 
零部件以最大工作负载运行 
3 
零部件以工作电源电压 UB 电气连接（发动机/发电机运转） 
 
3.1 
未激活零部件功能（如处于睡眠模式，关闭模式） 
 
3.2 
正常运转和控制零部件 
 
3.2  工作电压范围 
表 8：工作电压范围 
字母代码 
UBmin 
UBmax 
说明 
A 
 
 
保留 
B 
6V 
16V 
启动发动机期间必须保留性能的功能 
C 
8V 
16V 
启动发动机期间无需保留性能的功能 
注意：只有当零部件无法归类到 a/b/d/e
时才使用该代码。 
D（最常见） 
9V 
16V 
发动机不运转时必须保留性能的功能 


### 第 9 页
E 
10V 
16V 
发动机不运转时无需保留性能的功能 
Z 
协商确定 
注意：在给定字母代码范围内：功能状态等级必须为 A 级。 
注意：在（‐13.5V 到 Umin）和（Umax 到+26V），功能状态等级应为 C 级。 
 
3.3  性能验证 
供应商负责对零部件进行 5 点功能/参数检测，1 点功能/参数检测，持续监测和
功能循环检测。测试步骤见零部件环境测试计划。 
 
3.3.1 F‐1 1 点功能/参数测试 
目标：该项测试应验证零部件技术规范（CTS）中规定的温度和电压条件下的零
部件的完整功能性。 
测试： 
表 9：F‐1 1 点功能/参数测试 
工作模式 
2.3 和 2.1 
电压和温度 
Troom 和 UB
应用 
按照零部件环境测试计划进行测试，该计划必须获得福田验
证工程师的认可和批准。 
测试方法 
测试前温度应至少稳定 0.5h,测试步骤如下： 
1. 验证功能性：监测和记录所有输出（硬件和车载数据总
线通信）或零部件环境测试计划定义的子集在规定条件
下处于正确状态。 
2. 测量参数值：监测和记录所有输入和输出以及零部件环
境测试计划定义的子集的电压、电流和时间级，以验证
这些数据满足 CTS/SSTS 要求和公差要求 
3. 测量非电气参数如 LED 亮度、电机扭矩等：监测和记录
一些特定值，以验证这些数据满足 CTS/SSTS 要求和公差
要求。这些参数应定义在零部件环境测试计划内。 
要求： 
必须对零部件的基本功能进行测量，并注明在测试报告中。 
电源应保证能供足够的电流以避免高电流条件下的限流。测试期间以及测试结束
后功能状态等级应为 A 级。 
 
3.3.2 F‐2 5 点功能/参数测试 
目标：该项测试应验证零部件技术规范（CTS）中规定的温度和电压条件下的零
部件的完整功能性（见表 10）。 
测试： 
表 10：F‐2 5 点功能/参数测试 
工作模式 
2.3 和 2.1 
电压和温度 
(Tmin,UBmin), (Tmin,UBmax), (Troom,UB), (Tmax,UBmin), (Tmax,UBmax) 
应用 
所有测试组开始和最后的所有零部件 
测试方法 
测试前温度应至少稳定 0.5h（温度达到设定值后）,测试步
骤如下： 
1.验证功能性：监测和记录所有输出（硬件和车载数据总线


### 第 10 页
通信）或零部件环境测试计划定义的子集在规定条件下处于
正确状态。 
2.测量参数值：监测和记录所有输入和输出以及零部件环境
测试计划定义的子集的电压、电流和时间级，以验证这些数
据满足 CTS/SSTS 要求和公差要求 
3.测量非电气参数如 LED 亮度、电机扭矩等：监测和记录一
些特定值，以验证这些数据满足 CTS/SSTS 要求和公差要求。
这些参数应定义在零部件环境测试计划内。 
要求： 
必须对零部件的基本功能进行测量，并注明在测试报告中。 
测试期间以及测试结束后功能状态等级应为 A 级。 
 
3.3.3 F‐3  持续参数监测 
目标：持续监测应能检测出零部件在测试期间和测试结束后的功能状态，应能检
测出错误的触发信号、串行数据信息、故障码或其他错误的 I/O 命令以及状态。
这些应详细注明在零部件环境测试计划中。 
测试： 
表 11：F‐3  采用迁移分析法的持续参数监测 
工作模式 
根据具体测试而定 
电压和温度 
根据具体测试而定 
应用 
按照零部件环境测试计划在测试期间进行持续参数测试。 
测试方法 
1. 检测功能性：监测和记录所有输出（硬件和车载数据总
线通信）或零部件环境测试计划定义的子集在规定条件
下处于正确状态。监测取样率应定义在零部件环境测试
计划中。 
2. 监测和记录内部诊断代码（如需要） 
3. 对特定零部件功能定期观察，例如测试图形的光学监测
要求： 
持续监测装置在精确度和综合数据存储方面需得到验证，测试期间以及测试结束
后功能状态等级应为 A 级。 
 
3.3.4 F‐4  功能循环 
目标：功能循环应能模拟测试期间和测试结束后的用户使用情况，这些应详细注
明在零部件环境测试计划中。 
测试： 
表 12：F‐4  功能循环 
应用 
按照零部件环境测试计划在测试期间进行功能循环测试。 
工作模式 
2.1/2.2/3.1/3.2 
监测 
见下面测试方法 
测试方法 
当零部件暴露于测试环境中时，所有零部件的输入/输出、
显示器、人机界面和机械驱动都需进行功能运行的循环监
测。这可能会包含电源模式（如果适用）。功能循环率应注
明在零部件环境测试计划中。 
任何包含工作模式 3.2 的测试期间都需进行功能循环测试。


### 第 11 页
特殊情况应注明在零部件环境测试计划中。功能循环测试计
划应能检测零部件以及检测出退化和缺陷。电气/机械负载
应能反映装车环境中的正常使用情况。 
输入/输出循环和检测必须自动化。 
要求： 
功能循环测试装置在精确度和零部件状态变更方面需得到验证。 
 
3.3.5 F‐5  物理分析 
目标：该项测试应能识别出由环境测试造成的结构缺陷、材料/零部件退化或残
余物以及接近缺陷的条件和状态。 
测试： 
所有样件都需进行物理分析。一经福田要求，所有样件都能供福田审核。如果福
田工程师要求，供应商应将下列样件提交给福田进行物理分析：机械疲劳测试过
的样件 1 件，热疲劳测试过的样件 2 件（一件恒湿，另一件循环湿热），以及防
腐蚀测试过的样件 1 件。对零部件外壳进行外部检验，然后进行拆解检验。该测
试需借助辅助工具（如放大镜、显微镜、染料等）。 
以下是检验项举例： 
— 机械和结构完整性： 
是否有老化、裂纹、融化、磨损、松散迹象等 
— 焊接/引线疲劳、裂纹或凸起等： 
重点在大的集成电路或边角定位上（尤其在 Pin 脚末端），电路板上弯曲区域
处的零件也需注意 
— 大的零件和附件： 
电解电容漏电、继电器脏污等 
— 材料退化、霉菌生长或腐蚀残余： 
熔塑零件、敷型涂料、焊接掩膜或密封件退化、电路板限定、电路板痕迹上
升、腐蚀如由灰尘、盐分和潮湿造成的黑色的硫化银斑点、霉菌生长或环境
残余物。所有外来物都需进行材料成分和导电性分析。 
— 其他异常： 
外观或气味变化，糟糕的制造工艺、有令人不舒服的异响，尤其在振动测试
后出现的异响 
— 采用锡、锌、银形成的晶须： 
本标准提供的零部件环境测试计划会有效促进晶须的形成。所有零部件都需
用放大装置仔细检验电路板，尤其是经过 PTC 的零部件。环境测试过程中晶
须外观会表明该区域出现类似晶须的概率。晶须可能会造成零部件倾斜以及
零部件短路。 
— 无枝状生长： 
电路板及所有部件不能出现枝状生长。 
— 焊点孔洞 
焊点孔洞应降至可接受的最低水平上。 
要求： 
每个零部件的条件总结需注明并报告给福田设计认可工程师或福田零部件验证
工程师。福田有权要求供应商进行更多调研以确定退化类型等级，福田工程部会
根据纠正措施的必要性作出决定。 


### 第 12 页
4  电气要求和测试 
 
表 13：测试选择表 
测试项 
测试对象 
客 户 特 殊 要
求 
E1 寄生电流 
直接和汽车蓄电池连接的零部件 
 
E2 电源中断 
可能会受到电压下降影响的所有零
部件。包括由其他电器提供稳定电压
的零部件 
 
E3 电池电压变化 
由 12V 布线系统电池供电的所有零
部件 
 
E4 正弦叠加电压 
由 12V 布线系统电池供电的所有零
部件 
 
E5 脉冲叠加电压 
由 12V 布线系统电池供电的所有零
部件 
 
E6 信号和负载电路中的短
路 
所有零部件 
 
E7 开路—单线中断 
所有零部件 
 
E8 开路—多线中断 
所有零部件 
 
E9 接地偏移 
无信号返回线的零部件上的所有
I/O，这是指接地线发生电压偏移的
零部件上的所有 I/O。由于接地线中
的电压损耗，汽车内不同零部件之间
会产生接地偏移。线束的电阻特性
（如导线电阻、导线长度、连接器材
料等）会造成零部件上 I/O 的接地偏
移。 
 
E10 不连续输入电压 
带信号传送器的不连续的数字输入
和开关输入接口，无信号返回到接收
器，也可以在硬件设计评审时分析取
代该测试 
 
E11 过载—所有电路 
所有含或不含内部过电流保护的零
部件。这也适用于含保险丝的零部
件，如总线电子中心。 
 
E12 绝缘电阻 
所有能产生＞30V 电压的零部件 
 
E13 过电压 
由 12V 布线系统电池供电的所有零
部件 
 
E14 瞬态过电压 
通过 12V 电气系统供电的零部件 
 
E15 瞬态低电压 
通过 12V 电气系统供电的零部件 
 
E16 跳线跨接启动 
由 12V 布线系统电池供电的所有零
部件 
 
E17 抛负载 
通过 12V 电气系统供电的零部件 
 
E18 短时中断 
所有零部件 
 
E19 启动脉冲 
通过 12V 电气系统供电的零部件 
 


### 第 13 页
E20 智能发电机控制电压
曲线 
通过 12V 电气系统供电的零部件 
 
E21 反极性 
由 12V 布线系统电池供电的所有零
部件。该项测试不适用于设计任务书
中有特殊规定的发电机或零部件。 
 
E22 绝缘强度 
含感应元件的零部件（如发动机、继
电器、线圈） 
 
E23 反馈 
接到 KL15 电的零部件或接到其他有
唤醒功能的 KL 电的零部件 
 
 
4.1 E1 寄生电流 
 
目标： 
该项测试是为验证零部件的功率消耗符合点火 OFF 状态的标准要求。这是用于支
持在经过长期存储/停车条件下的电源管理和发动机启动能力。 
测试： 
表 14：E1 寄生电流测试参数 
工作模式 
2.1 
测试电压 
12.5V 
测试 1 
T 
Tmin 
测试 2 
T 
Troom 
测试 3 
T 
Tmax 
适用范围 
与蓄电池直接连接的所有零部件 
测试方法 
关闭 KL15 电后，启动零部件，根据要
求进行寄生电流消耗 
测试样件数量 
6 
要求： 
原则上任一测试样件的寄生电流为 0mA.供应商需要就接受标准和福田验证工程
师协商确定，默认的接受标准为低于 0.1mA（平均 12 小时内）；1.2mAh（超过+40℃
＜0.2mA）也可适用。测量装置的取样频率必须 10 倍高于零部件产生的最短的
电流峰值时长。 
 
4.2 E2 电源中断 
 
目标： 
该项测试是为验证零部件的复位能力。它主要适用于含稳压电源或稳压器的零部
件。它也可适用于含微处理器的零部件来测定能够承受短时低电压停留的强度。 
测试： 
表 15：E2 电源中断测试参数 
工作模式 
2.3 
Uth 
6V 


### 第 14 页
△U1(UBmin 到 6V) 
0.5V 
△U2(6V 到 0V) 
0.2V 
t0—零部件开启（On） 
至少≥10s,一直保持到零部件恢复到
100%可用性（所有系统正常重新启动）
t1—测试顺序 1 
5s 
t1—测试顺序 2 
100ms 
tr 
≤10ms
tf 
≤10ms
测试方法 
向所有电源电压输入（见图 1）同步施
加测试脉冲 
循环次数 
1 
测试样件数量 
3 
 
图 1：电源中断 
 
要求： 
当电压恢复到 UBmin 时，功能状态等级应为 A 级。任何情况下都不能出现未定义
的操作运行状态。当电压多少时零部件首次不在 A 级状态，这个数据需得到验证
并记录存档。 
 
4.3 E3 电池电压变化 
 
目标： 
该项测试是为验证汽车蓄电池放电和充电期间零部件耐电压升降性。 
 
测试： 


### 第 15 页
 
图 2：电池电压变化曲线 
 
表 16：E3 电池电压变化测试参数 
工作模式 
2.3 和 2.1 
测试方法 
见图 2，分两种情况： 
1. 缓慢降低和缓慢上升电源电压 
2. 缓慢降低和快速上升电源电压 
序
号 
I 
II 
III 
IV 
V 
VI 
VII 
1 
0.5V/min  * 
0.5V/min
** 
0.5V/min
* 
0.5V/min
2 
0.5V/min  * 
0.5V/min
** 
32V/s 
*** 
32V/s 
*在 UBmin 时停留直到完全读取故障记录。 
**在 0V 时至少保持 1 分钟，但只要内部电量全部放出就行。 
***无需停留 
循环次数 
工作模式 2.3:1 次循环 
工作模式 2.1:1 次循环 
样件数量 
3 
要求： 
根据测试期间施加给零部件的电压范围来评估测试结果。区别如下： 
a) 在零部件工作电压范围以内的： 
功能状态等级必须为 A 级。不允许有事件日志记录。 
b) 超出零部件工作电压范围的： 
功能状态等级 C 级可以接受。 
 
4.4 E4 正弦叠加电压 
 
目标：该项测试是为验证零部件耐由于调整正弦发电机电压引起的发电机输出纹
波电压性。 
测试： 
电压 
时间 
A 级 
C 级 
A 级 


### 第 16 页
表 17：E4 正弦叠加电压测试参数 
工作模式 
2.3 
Ri 
≤100mΩ 
测试时长 
30 分钟 
频率范围 
15Hz—30kHz 
波动时长 
2分钟（包括从15Hz到30kHz再到15Hz）
波动类型 
三角，对数 
UPP 
4V 
测试方法 
必须模拟实车环境，用原装汽车线束，
见图 3 
测试样件数量 
3 
 
图 3：E4 正弦叠加电压 
要求： 
功能状态等级为 A 级。 
 
4.5 E5 脉冲叠加电压 
 
目标：该项测试是为验证在标准工作电压范围内零部件耐电源电压产生的电压脉
冲性。这些电压脉冲会模拟电源线路突然有高电流负载变化，在开关时会引起电
压下降或上升。该项测试模拟了带浪涌电流特性的负载情况，如发动机、白炽灯
泡或长线束的电压降。 
 
测试： 
表 18：E5 脉冲叠加电压测试参数 
工作模式 
2.3 
适用范围 
由 12V 布线系统供电的所有零部件 
监测 
持续监测 


### 第 17 页
测试方法 
参见图 4 
1 将零部件连接到 UO 输出 
2 将环境温度升至 Tmax,在此温度保持零
部件至少 0.5 小时 
3 设置 US=Umax‐2V 
4 当持续监测间歇性失效时进行 5 次连
续频率扫描循环 
5 将 US 降低 1V 
6 重复步骤（4）和（5）直至（US=Umin+2V）
7 在常温下重复步骤（3）至步骤（6）
8 在 Tmin 重复步骤（3）至步骤（6） 
测试设置定义/参数 
� U0=US+UP 
� US=(Umin+2V)到(Umax‐2V)直流电压 
� UP= 方 波 ‐1V
到 +1V50% 占 空 比
（2VP‐P） 
� UP 频率扫描范围：1Hz 到 4kHz 
� 频率扫描类型：对数 
� 一次循环的频率扫描时长：1Hz 到
4kHz 再到 1Hz:120 秒 
注意 
� 含功率输出驱动的零部件：每次 US
设置测试应在实际环境或接到等量
负载（Load_1 到 Load_n）以及输出
电流在 I_load_min 到 I_load_max 之间进行
� U0 波形应根据频率而定。图 5 频率
范 围 示 例 ： 1Hz,100Hz 和 4kHz
（US=+14V,UP=+1V/‐1V） 
IO 输出电流能力 50A;2V 步骤的上升时间＜10s,RC=3.18ms(50Hz f_low) 
 
图 4：脉冲叠加电压测试装置 
要求： 
功能状态等级应为 A 级。 


### 第 18 页
 
图 5：脉冲波形示例 
 
4.6 E6 信号和负载电路中的短路 
 
目标：模拟所有装置输入和输出中的短路以及负载电路中的短路。 
所有输入和输出对+UB 和 GND 时可以耐短路（对处于工作状态和非工作状态的输
出端，也要考虑供电电源丢失和地丢失的情况） 
零部件上应有短路保护措施。 
零部件连接见图 6. 
 
测试： 
表 19：E6 信号和负载电路中的短路测试参数 
工作模式 
2.3 
测试时长 
每个 Pin 脚分别对 UB 和地短路 60 秒 
测试电压 
UBmin 和 UBmax
测试装置 
测试所使用的电源装置必须能够通过
零部件提供所预期的短路电流。如果不
可行，可以采用汽车蓄电池来代替供电
装置（在这种情况下 UBmax 为最大充电
电压） 
循环次数 
每个 Pin 脚对地和电源 UB 短路 1 次 
测试样件数量 
3 


### 第 19 页
 
图 6：E6 信号和负载电路中的短路电路图 
要求： 
输入和输出（E 和 A 见图 6）：功能状态等级为 C 级。 
 
4.7 E7 开路—单线中断 
 
目标：模拟每个 Pin 脚的线路中断，分成两个操作模式进行测试。由于中断时长
可能不同，可以采用不同的脉冲波形（从接触不良到永久中断）。 
 
测试： 
表 20：E7 开路—单线中断测试参数 
工作模式 
测试 1：KL30 ON 和 KL15 ON 
测试 2：KL30 ON 
测试 1 
移开每个 Pin 脚 10 秒钟再重新复位（缓慢间隔）
测试 2 
移开每个 Pin 脚 1ms 再重新复位，含继电器的电
路 100μs 
测试 3 
为模拟接触不良对每个 Pin 脚施加脉冲群。以下
标准适用： 
接触不良：与继电器开关触点相连（继电器触点
的反弹） 
 
 
测试 3 中的脉冲定义 
接触不良
t=0.1ms 
t1=1ms 
t2=4s 
t3=10s 
tr 
≤（0.1*t） 
tf 
≤（0.1*t） 
循环次数 
三种测试情况的每一种在所有定义的工作模式
下都必须进行。每种测试都要分别评估。 
测试样件数量 
3 


### 第 20 页
 
图 7：E7Pin 脚中断测试脉冲 
 
要求： 
测试 1：功能状态等级为 C 
测试 2：功能状态等级为 C 
测试 3：功能状态等级为 A 
 
4.8 E8 开路—多线中断 
 
目标：该项测试是为验证零部件耐多线开路性。这种情况会在汽车线束连接器断
开时发生。 
 
测试： 
表 21：E8 开路—多线中断测试参数 
工作模式 
2.3 和 2.1 
测试顺序 
每个连接器都要进行两种测试。每个连接器都要
被拔掉 10s 然后复位。如果被测样件有多个连接
器，每个连接器都要分别测试。测试顺序必须变
化。 
循环次数 
每个连接器必须被拔掉 1 次 
测试样件数量 
3 
 
要求： 
连接器重新连接之后，功能状态等级为 C 级。 
 


### 第 21 页
4.9 E9 接地偏移 
 
目标：如果零部件有几组电源输入，不同的电源之间可能会出现潜在的差异。在
各电源的地之间出现+/‐1V 偏差的情况下，必须确保零部件的功能正常。 
 
测试： 
如果被测样件有多个电压和接地连接，必须在每个引脚上分别进行测试。测试中
的零部件连接如图 8 所示。 
 
表 22：E9 接地偏移测试参数 
工作模式 
2.3 
源电压 
+/‐1V 
循环次数 
所有开关位置的排列 
测试 1 
U 
UBmin 
测试 2 
U 
UBmax 
测试样件数量 
3 
 
S1：2Pin（a/b）转换开关 
TR：测试参考，例如测试台，模拟电子控制单元、执行器、传感器或负载 
图 8：E9 接地偏移电路图 
 
要求： 
在两点间电压偏差+/‐1V 时，被测样件必须达到功能状态 A 级。 
 
4.10 E10 不连续输入电压 
 
目标：该项测试是为验证不连续输入接口的性能（含开关接口）。 
 
测试： 
总线系统 
信号电路


### 第 22 页
表 23：E10 不连续输入电压测试参数 
工作模式 
2.3 
适用范围 
带信号传送器的不连续的数字输入和开关输入
接口，无信号返回到接收器，也可以在硬件设计
评审时分析取代该测试 
监测 
持续监测，此外可以读取逻辑状态和电流 
测试方法 
将零部件接到 US 和电源接地，见图 9 
 
图 9：测试装置 
1  在常温下 Troom 进行下列测试（见图 10） 
2  将 US 调整到 Umin 
3  将 Ustep 调整到 0V 
4 将 Usin 调整到正弦 50Hz，200mV 峰值‐峰值 
5  读取逻辑状态并保存 
6  在读取输出状态时设置 Ustep 增量为 250mV：从 0 到 Umin 每一步保持 5 秒，并
以 100ms 的重复速率进行样值输出，直到一排中的所有记录的 50 个逻辑状态
变为新值。 
7  当出现 Uih_rise,保存 Ustep 的输入值，直到达到 Umin 时结束操作 
8  在 Umin 时，开始以 250mV 的速率降低 Ustep 从 Umin 到 0V.重复步骤 6 以检测是
否有逻辑状态变更。 
9  保存 Ustep 的输入值，命名为 Uil_fall,然后继续直到达到 0V。 
10  将 Ustep 升到（Uil_rise+Uil_fall）/2,并记录下 5 秒中内状态变更次数 Nth_Umin（见
图 11）。 
11  用 Umax 代替 Umin 重复步骤（2）至步骤（10），保存新的 Uil_rise 和 Uil_fall。 
12  在 Tmax 和 Tmin 时重复步骤（2）至步骤（11）。 
13  撰写报告，报告内容包括： 
� 在 Troom 和 Umin 时：Uil_fall 和 Nth_Umin 
输出逻辑状态 


### 第 23 页
� 在 Troom 和 Umax 时：Uil_fall 和 Nth_Umin 
� 在 Tmax 和 Umin 时：Uil_rise  和 Uil_fall 
� 在 Tmax 和 Umax 时：Uil_rise  和 Uil_fall 
� 在 Tmin 和 Umin 时：Uil_rise  和 Uil_fall 
� 在 Tmin 和 Umax 时：Uil_rise  和 Uil_fall 
 
图 10：阙值电压波形—所有步骤 
 
图 11：阙值电压波形—步骤（10） 
要求： 
功能状态等级不适用于该项测试。所有不连续的数字输入接口应能正确探测出逻
辑水平： 
低逻辑：‐1V＜Uil＜2V  高逻辑：4.5V＜Uih＜Umax+1V 
然后要求在整个工作温度和工作电压之间 Uil_rise 和 Uil_fall 都降到（2.0~4.5）V.
接口滞后也包含在该要求之内。在（Uil_rise+Uil_fall）/2 范围内电流不能增加（＞
整个测试可能会要求比图中更多的
Ustep 增量/减量


### 第 24 页
5mA）。 
 
4.11 E11 过载‐所有电路 
目标：该项测试是为了测试机械开关、电子输出和触点的过流保护。同时必须考
虑施加大于正常负载情况的过高电流（如电机的最大堵转电流）。 
 
测试： 
表 24：E11 过载—所有电路测试参数 
工作模式 
2.3 
温度 
Tmax 
电子输出的测试条件 
输出端必须至少承受 3 倍的正常负载下的电流，
且无损坏。负载持续时间 30 分钟。 
开关量输出的测试条件 
如果最大堵转电流大于 3 倍额定电流 IN,就采用
最大堵转电流。 
对于 IN≤10A 的零部件：3 X IN 
对于 IN＞10A 的零部件：2 X IN,但至少 30A，最大
150A（在带载情况下不停“开”和“关”） 
如果继电器或开关有多个触点，必须分别测试每
个触点 
测试样件数量 
3 
要求： 
无保险丝的机械零部件必须达到功能状态等级 A 级。如果负载电路中有保险元件，
保险元件可能会被触发。 
带过载检测（电流、电压、温度）的电子输出端必须达到功能状态等级 C 级。 
此外，外观检查时零部件不允许出现影响功能或使用寿命的变化（外观和电气特
性）。 
 
4.12 E12 绝缘电阻 
目标：该项测试是为验证零部件的耐绝缘损耗性。它可能会造成电气性能降低和
信号干扰。 
 
测试： 
表 25：E12 绝缘电阻测试参数 
工作模式 
1.1 
温度 
Troom 
测试电压 
500V DC 
测试时长 
60s 
相对湿度 
50%,Troom 
测试顺序 
准备工作：经采购方同意，测试样件必须进行循
环湿热测试，测量之前，必须将测试样件干燥
30 分钟。 
测试点 
在如下描述的两点间施加测试电压 
—接插件端子之间无电气连接 
—连接器 Pin 脚与外壳之间（非金属外壳） 


### 第 25 页
—连接器 Pin 脚与电极之间，当外壳不导电 
—其他测试点需与相关工程部门协商确定 
循环次数 
1 
测试样件数量 
3 
要求： 
绝缘电阻至少为 10MΩ。样件在测试中不得损坏。测试结束后功能状态等级为 A
级。 
4.13 E13 过电压 
目标：测试零部件在长时间过电压下的耐受性。模拟在行驶过程中发电机控制模
块失效导致过电压的情况。 
 
测试： 
表 26：E13 过电压测试参数 
工作模式 
2.3 
温度 
Tmax—20C 
t1 
60 分钟 
Umax 
17V（+4%，0%） 
Umin 
13.5V 
tr 
＜10ms 
tf 
＜10ms 
连续测试电压 
17V   
循环次数 
1 
测试样件数量 
3 
 
图 12：过电压 
要求： 
依据零部件的用途来评估测试结果。区分条件如下： 
a) 对于包含正常行驶所必须功能的零部件 
功能状态等级 B 级 
如果需要，需定义紧急模式。相应的降额战略需在零部件设计任务书定义。 


### 第 26 页
b) 对于其他的零部件： 
功能状态等级 C 级 
 
4.14 E14 瞬时过电压 
目标：短时的过电压可能在关掉大功率负载或者短时急加速的情况下发生。这种
情况通过该项测试来模拟。该项测试可以用于电气寿命测试。 
 
测试： 
表 27：E14 瞬时过电压测试参数 
工作模式 
2.3 
Umin 
16V 
U1 
17V 
Umax 
18V 
tr 
1ms 
tf 
1ms 
t1 
400ms 
t2 
600ms 
测试 1 
Ttest 
Tmax 
循环次数 
3 
t3 
2 秒 
测试 2 
Ttest 
Tmin 
循环次数 
3 
t3 
2 秒 
测试 3 
Ttest 
Troom 
循环次数 
100 
t3 
8 秒 
测试样件数量 
6 


### 第 27 页
 
图 13：E14 瞬时过电压测试脉冲 
要求： 
功能状态等级 A 级 
在测试过程中所有相关的输出必须保持在定义的范围内，该要求必须在整个测试
过程中验证。 
 
4.15 E15 瞬时低电压 
目标：瞬时的低电压可能在打开大功率负载的情况下发生。该情况下的欠电压可
通过本项测试来模拟。 
 
测试： 
表 28：E15 瞬时低电压测试参数 
工作模式 
2.3 
Umin 
9V 
Umax 
10.8V 
tr 
1.8ms 
tf 
1.8ms 
Ttest 
500ms 
测试 1 
Ttest 
Tmax 
循环次数 
3 
测试 2 
Ttest 
Tmin 
循环次数 
3 
测试样件数量 
3 
 


### 第 28 页
 
图 14：E15 瞬时低电压测试脉冲 
要求： 
功能状态等级 A 
 
4.16 E16  跳线跨接启动 
目标：模拟外接电源启动车辆的情况。最大测试电压可来自于外接商用车电源启
动。 
 
测试： 
表 29—E16 跳线跨接启动测试参数 
工作模式 
2.3 
Umin 
10.8V 
Umax 
26V 
tvor 
60s 
Ttest 
60s 
tr 
＜10ms 
tf 
＜10ms 
循环次数 
1 
测试样件数量 
3 


### 第 29 页
 
图 15：E16 跳线跨接启动测试脉冲 
要求： 
依据零部件的用途来评估测试结果。区分条件如下： 
a) 对于启动相关的零部件（如起动机）： 
功能状态等级 B 级 
传感器必须在测试过程中能发送有效的信号。 
b) 对于其他的零部件： 
功能状态等级 C 级 
 
4.17 E17 抛负载 
目标：由于发电机的内在特性，在较大的电气负载被关断之后，会导致过电压脉
冲产生。本测试的波形用来模拟此种情况。 
 
测试： 
表 30：E17 抛负载测试参数 
 
 
工作模式 
2.3 
Umin 
13.5V 
Umax 
27V 
tr 
≤2ms 
ts 
300ms 
tf 
≤30ms 
间隔时间 
1 分钟 
循环次数 
10 
测试样件数量 
3 


### 第 30 页
 
图 16：E17 抛负载测试脉冲 
要求： 
区别如下： 
a) 安全相关零部件： 
功能状态等级 B 级 
b) 其他零部件： 
功能状态等级 C 级 
 
4.18 E18 短时中断 
目标：模拟不同时长内零部件在短时中断时的特性。 
 
测试： 
表 31：E18 短时中断测试参数 
工作模式 
2.3 
测试装置 
电路图见图 18 
必须和工程部门协商确定人工汽车网络。 
测试 1 
S1 switched,打开 S2,R=100kΩ 
S1 switched,打开 S2,R=0.1Ω（电气系统） 
测试 2 
S1 switched,S2 负极连接到 S1,R≥10kΩ 
Utest 
11V 
测试顺序如下： 
t1 
间隔时间 
10μs 至 100μs 
10μs 
100μs 至 1ms 
100μs 
1ms 至 10ms 
1ms 
10ms 至 100ms 
10ms 
100ms 至 2s 
100ms 
测试样件 ON—功能 ON  ＞10s 
t2 
必须保持测试电压 Utest 直到测试样件达到 100%可用性
（所有系统重新正常启动）。 


### 第 31 页
循环次数 
1 
测试件数量 
3 
电压断路时间按照表 30 的步骤逐渐延长，生成的曲线图见图 17. 
 
 
图 17：E18 短时中断测试脉冲 
 
图 18：E18 短时中断电路图 
要求： 
必须确定测试件从 t1 的哪个时间点起第一次偏离功能状态 A。 
t1≤100μs 的：功能状态等级 A 
t1＞100μs 的：功能状态等级 C 
允许功能状态等级 C 级的偏差值需定义在零部件设计任务书中。 
 
4.19 E19 启动脉冲 
目标：在发动机启动的过程中，电池电压会短暂地跌落到很低的水平，然后轻微


### 第 32 页
地上升。多数零部件在启动前处于激活状态，在启动过程中处于非活动状态，在
启动成功后再次处于活动状态。本项测试即为了验证零部件在启动发动机情况下
是否满足要求。 
整车会处于不同的条件下启动，如冷启动和热启动（起停的自动重启）。为了满
足不同的情况，需要使用两个不同的测试序列对零部件进行测试。零部件必须满
足所有的测试序列。 
测试： 
表 32：E19 启动脉冲测试参数 
工作模式 
2.3 
测试脉冲 
—冷启动：测试脉冲“普通”和“严重”，见表
33 
—热启动：测试脉冲“短”和“长”，见表 34 
测试样件数量 
6 
 
4.19.1  测试 1—冷启动 
表 33—E19 冷启动脉冲测试参数 
参数 
测试脉冲“普通” 
测试脉冲“严重” 
UD 
11.0V 
11.0V 
UT 
4.5V（0%，‐4%） 
3.2V+0.2V 
US 
4.5V（0%，‐4%） 
5.0V（0%，‐4%） 
UA 
6.5V（0%，‐4%） 
6.0V（0%，‐4%） 
UR 
2V 
2V 
tf 
≤1ms 
≤1ms 
t4 
0ms 
19ms 
t5 
0ms 
≤1ms 
t6 
19ms 
329ms 
t7 
50ms 
50ms 
t8 
10s 
10s 
tr 
100ms 
100ms 
f 
2Hz 
2Hz 
两次循环间隔时间 
2s 
2s 
循环次数 
10 
10 
 
 
 
 
 
 


### 第 33 页
 
图 19：冷启动测试脉冲 
 
4.19.2  测试 2—热启动 
 
表 34：E19 热启动脉冲测试参数 
参数 
测试顺序“短” 
测试顺序“长” 
UD 
11.0V 
UT 
7.0V 
US 
8.0V 
UA 
9.0V 
t50 
≥10ms 
tf 
≤1ms 
t4 
15ms 
t5 
70ms 
t6 
240ms 
t7 
70ms 
t8 
600ms 
tr 
≤1ms 
两次循环间隔时间 
5s 
20s 
循环次数 
10 
100 


### 第 34 页
 
图 20：热启动测试脉冲 
要求： 
启动相关的零部件： 
不能产生故障记录。 
车辆应能保持正常启动。 
测试 1—冷启动 
测试脉冲“普通”：功能状态等级 A 级 
测试脉冲“严重”：功能状态等级 B 级 
测试 2—热启动 
测试顺序“长”：功能状态等级 A 级 
测试顺序“短”：功能状态等级 A 级 
非启动相关性零部件： 
测试 1—冷启动 
测试脉冲“普通”：功能状态等级 C 级 
测试脉冲“严重”：功能状态等级 C 级 
测试 2—热启动 
测试顺序“长”：功能状态等级 A 级 
测试顺序“短”：功能状态等级 A 级 
 
4.20 E20 智能发电机控制电压曲线 
目标：模拟电压控制的电气系统特性，例如使用智能发电机控制或直流—直流转
换器控制。通过这种控制，根据表 35 中的测试来设置恒定电压到永久电压波动
之间的电压曲线。 
 
这涉及到零部件在发动机启动或汽车准备运转时的所有负载情况。 
 
测试： 
 


### 第 35 页
表 35：E20 智能发电机控制电压曲线测试参数 
工作模式 
2.3 
Umin 
（11.8V‐△V）（0%，‐4%） 
Umax 
（15V‐△V）（+4%，0%） 
t1 
2s 
tr 
≥300ms 
tf 
≥300ms 
循环次数 
10 
测试样件数量 
6 
测试 1 
△U 
0V 
测试 2 
△U 
0.7V 
测试 3 
△U 
2V 
 
图 21：E20 智能发电机控制测试脉冲 
 
要求： 
功能状态等级 A 级 
 
4.21 E21 反极性 
目标：模拟在跳线跨接启动期间零部件耐受反极性电池连接的特性。反极性可能
会多次出现，不应对零部件造成影响。电压低于最小测试电压的零部件都必须进
行反极性保护。汽车保险丝不是反极性保护方案的一部分。 
 
 


### 第 36 页
测试：所有电路的相关连接都必须进行测试。测试样件布局必须和实际汽车电路
一致。必须在表 37 和表 38 中 0V 到最大电压之间的不同电压点进行测试。 
 
表 36：E21 反极性测试参数 
工作模式 
2.1 
循环次数 
见表 37 和表 38 
测试样件数量 
6 
 
4.21.1  静态反极性 
该项测试是为验证在不同反极性电压时的零部件的耐受性。 
表 37：E21 静态反极性测试参数 
△U1 
‐1V 
严重度 1 
Ri＜100mΩ 
严重度 2 
Ri＜30mΩ 
t1 
60s 
当出现反极性时，由继电器关闭工作电压的零部
件则适用：8ms 
t2 
≥60s,但零部件至少要达到开始测试时的热状
态 
tr 
≤10ms 
tf 
≤10ms 
Umax 
0V 
Umin 
‐14.0V 
循环次数 
1 
 
图 22：E21 静态反极性测试脉冲 
 
4.21.2  动态反极性 
该项测试是为验证运行期间汽车无法再启动时的零部件对动态反极性的耐受性。 
表 38：E21 动态反极性测试参数 


### 第 37 页
 
严重度 1 
Ri＜100mΩ 
严重度 2 
Ri＜30mΩ 
Umax 
10.8V 
Umin 
‐4.0V 
t1 
60s 
当出现反极性时，由继电器关闭工作电压的零部
件则适用：8ms 
t2 
≤5 分钟 
tr 
≤10ms 
tf 
≤10ms 
循环次数 
3 
 
图 23：E21 动态反极性测试脉冲 
 
要求： 
当施加反极性时，不能触发安全相关功能如电动车窗、电动天窗、起动机等。 
反极性期间，任何零部件都不能超出数据表（电气和温度方面）中的允许值。 
测试过程中不能超过汽车保险丝的额定电流值。 
反极性不能对零部件造成与损坏和隐藏性损坏。 
耐反极性也适用于 0V 到最大测试电压之间的任一电压。 
耐反极性测试功能状态等级为 C 级。 
测试过程中需记录电流消耗情况。 
 
4.22 E22 绝缘强度 
目标：模拟测试样件相互之间绝缘的构件的绝缘强度，如连接器 Pin 脚、继电器、
线圈、回路等。所有包含或控制感应元件的零部件都需进行测试。 
 
测试： 
 


### 第 38 页
 
表 39：E22 绝缘强度测试参数 
工作模式 
2.1 
测试电压 
Ueff=500V AC,50Hz 
测试时长 
60s 
测试顺序 
准备工作：经采购方同意，测试样件必须进行循
环湿热测试，测量之前，必须将测试样件干燥
30 分钟。 
测试点 
在如下描述的两点间施加测试电压 
—接插件端子之间无电气连接 
—连接器 Pin 脚与外壳之间（非金属外壳） 
—连接器 Pin 脚与电极之间，当外壳不导电 
—其他测试点需与相关工程部门协商确定 
相对湿度 
50% 
温度 
Troom 
循环次数 
1 次循环，上述定义的每个测试点至少要测试 1
次 
测试样件数量 
6 
 
要求： 
功能状态等级 C 级 
不允许出现介质击穿和电弧现象。 
 
4.23 E23 反馈 
目标：模拟连接到 KL15 的零部件的性能，所有接到 KL15 上的零部件都必须进行
测试。其他有“唤醒”功能的电源也需进行该项测试。 
 
测试： 
表 40：E23 反馈测试参数 
工作模式 
2.3 
Utest 
UBmax—0.2V 
测试温度 
Tmax,Troom 和 Tmin
测试样件数量 
3 
测试顺序： 
按照汽车布线环境在汽车内连接测试样件（包括传感器、执行器等）并正常运行。
关闭时测量 KL15 上的电压曲线。通过继电器或开关（Ropen_switch→∞）来关闭电
源。测试过程中不允许中断或关闭其他可能出现的电源如 KL30（按照汽车特性
要求）。测试过程中 KL15 上不允许使用电阻，通过接到 KL31 上不低于 10MΩ的
外接电阻（如示波器）来评估 KL15 上的电压曲线。 


### 第 39 页
 
图 24：E23 反馈测试电路图 
要求： 
KL15 反馈只允许最高到 1.0V 的电压水平。关闭电源后必须在 t=20ms 之内达到该
电压范围。从关闭开始为接通的 KL15 电压必须在 t=20ms 之内低于 Ukl.15=+1V。
电压曲线需体现连续降低功能。由于正脉冲导致的曲线不连续是不允许的。 
 
4.24 E24  正常和最坏情况性能分析 
目标：验证电路的设计是否满足产品的功能要求。 
 
测试： 
表 41：E24 正常和最坏情况性能分析 
工作模式 
N/A 
适用范围 
所有零部件 
监测 
N/A 
测试方法 
对每个零部件在遍及工作温度、电源和 I/O 电压
范围内，用系统分析程序测定电压、电流和功率
消耗 
要求： 
验证电路的设计，在所有的条件下，是否满足产品的功能要求，零部件满足
SSTS/CTS 的要求。 
 
4.25 E25 短路/开路分析 
目标：确定零部件对蓄电池/电源、对地的间歇短路、连续短路以及开路条件下
的能力。 
 
测试： 
表 42：E25 短路/开路分析测试参数 
工作模式 
N/A 
适用范围 
所有零部件 
监测 
N/A 
测试方法 
用一个电路分析程序进行模拟间歇（默认：1Hz
到 100Hz 的方波）和连续的短路和开路，然后在
示波器 


### 第 40 页
整个工作温度、电源和输出电流范围内，对每个
零部件检测电压、电流和功率消耗。 
要求： 
验证零部件对间歇和连续短路和开路的承受能力，这项分析是为了验证零部件的
工作参数不应该超过零部件数据表中定义的参数。 
 
5  机械要求和测试 
表 43：机械测试表 
测试 
M1 振动测试 
M4 外壳撞击—肘部负荷 
M2 机械冲击 
M5 外壳撞击—脚部负荷 
M3 耐久冲击测试 
M6 跌落测试 
M7 共振频率分析 
M8 高空运输压力效应分析 
M9 塑料卡扣紧固件分析 
M10 碾压分析 
M11 振动噪声 
M12 接插件测试 
 
5.1  振动测试 
目标：模拟汽车运行时元件的振动负荷。目的是确保测试零部件能承受组件松动
和材料疲劳的能力。 
测试： 
测试按照正弦波振动 DIN EN 60068‐2‐6 和宽频振动 DIN EN 60068‐2‐64 执行，参数
如下： 
 
表 44：基本振动测试参数 
样件运行方式/工作模式 
按照图 25：振动温度要求重复测试 
 
叠加的温度曲线 
按照图 25 振动温度要求进行重复测试：
表 511：振动温度曲线 
正弦振动的频率通过时间 
1 Oktave/min，对数的 
振动曲线 A（安装在发动机上的元件）
依据图 26 和表格 45：正弦波振动 
依据图 27 和表格 46：宽带随机振动 
正弦波和宽带随机振动可以一起测试，
也可以分开测试 
振动曲线 B（安装在变速箱上的元件）
依据图 28 和表格 47：正弦波振动 
依据图 29 和表格 48：宽带随机振动 
正弦波和宽带随机振动可以一起测试，
也可以分开测试 
振动曲线 C 
（安装在进气歧管但不固定连接的元
件） 
依据图 30 和表格 49：正弦波振动 
振动曲线 D（安装在车身上的元件） 
依据图 31 和表格 50：宽带随机振动 
振动曲线 E（安装在底盘上的元件） 
依据图 32 和表格 51：宽带随机振动 
样件数量 
3 
 


### 第 41 页
测试时无需支架或者附加组件。如有需要，带支架或附加组件的附加测试需与福
田验证工程师协商确定。 
通过防振器安装在支架或底盘上的元件必须在零部件设计任务书中规定：是否 
—所有含防振器的样件 
—所有不含防振器的样件  或 
—3 个含防振器的样件和 3 个不含防振器的样件 
要进行测试。 
 
采样率的选择必须能够识别断路和短路。 
 
图 25：温度要求—振动 
表 511：振动温度曲线 
时间：min 
温度：℃ 
0 
Troom 
60 
Tmin 
150 
Tmin 
300 
Tmax 
410 
Tmax 
480 
Troom 
 
如果有冷却液管路，冷却液温度不能超过 Tcool,max。只有环境温度可以超出上述
冷却液温度上限值. 
 
5.5.1 振动曲线 A（安装在发动机上的元件） 
表 45：发动机安装件正弦波振动测试参数 
振动激发 
正弦波 
每个轴向的测试时间 
22h 
振动曲线 
曲线 1：安装在 5 缸及以下发动机上的元件 
从 135 到 420min：2.3   


### 第 42 页
曲线 2：安装在 6 缸及以上发动机上的元件 
 
两种情况都有的元件使用组合曲线 
图 23 中的曲线 1 
频率 Hz 
加速振幅 m/s2 
100 
100 
200 
200 
240 
200 
270 
100 
440 
100 
图 23 中的曲线 2 
频率 Hz 
加速振幅 m/s2 
100 
100 
150 
150 
440 
150 
组合 
频率 Hz 
加速振幅 m/s2 
100 
100 
150 
150 
200 
200 
240 
200 
255 
150 
440 
150 
 
图 26：安装在发动机上元件的正弦波振动曲线 
 
表 46：安装在发动机上元件的宽带随机振动测试参数 
振动激发 
宽频随机振动 
每个轴向的测试时间 
22h 
加速有效值 
181m/s2 
图 27 中的振动曲线 
频率 Hz 
功率谱密度(m/s2)2/Hz 
10 
10 
频率 Hz
加
速
振
幅 


### 第 43 页
100 
10 
300 
0.51 
500 
20 
2000 
20 
 
 
图 27：安装在发动机上元件的宽带随机振动曲线 
 
5.1.2 振动曲线 B（安装在变速箱上的元件） 
表 47：安装在变速箱上的元件的正弦波振动测试参数 
振动激发 
正弦波 
每个轴向的测试时间 
22h 
图 28 中的振动曲线 
频率 Hz 
加速振幅 m/s2 
100 
30 
200 
60 
440 
60 
功率谱密度 
频率 


### 第 44 页
 
图 28：安装在变速箱上的元件的正弦波振动曲线 
表 48：安装在变速箱上元件的宽带随机振动测试参数 
振动激发 
宽带随机振动 
每个轴向的测试时间 
22h 
加速有效值 
96.6m/s2 
图 29 中的振动曲线 
频率 Hz 
功率谱密度(m/s2)2/Hz 
10 
10 
100 
10 
300 
0.51 
500 
5 
2000 
5 
 
图 29：安装在变速箱上元件的宽带随机振动曲线 
 
5.1.3 振动曲线 C（分离的进气总管安装件） 


### 第 45 页
表 49：安装在进气歧管但不固定连接的元件正弦测试参数 
振动激发 
正弦波 
每个轴向的测试时间 
22h 
图 30 中的振动曲线 
频率 Hz 
加速振幅 m/s2 
100 
90 
200 
180 
325 
180 
500 
80 
1500 
80 
 
图 30：安装在进气歧管但不固定连接的元件正弦振动曲线 
 
5.1.4 振动曲线 D（安装在车身上的元件） 
表 50：安装在车身上的元件宽带噪声测试参数 
振动激发 
宽带随机振动 
每个轴向的测试时间 
8h 
加速有效值 
3.07 
图 31 中的振动曲线 
频率 Hz 
功率谱密度(m/s2)2/Hz 
5 
0.884 
10 
20 
55 
6.5 
180 
0.25 
300 
0.25 
360 
0.14 
1000 
0.14 
2000 
0.14 


### 第 46 页
 
图 31：安装在车身上的元件宽带随机振动曲线 
 
5.1.5 振动曲线 E（安装在底盘上的元件） 
表 51：安装在底盘上的元件宽带随机振动测试参数 
振动激发 
宽带随机振动 
每个轴向的测试时间 
8h 
加速有效值 
107.3m/s2 
图 32 中的振动曲线 
频率 Hz 
功率谱密度(m/s2)2/Hz 
20 
200 
40 
200 
300 
0.5 
800 
0.5 
1000 
3 
2000 
3 


### 第 47 页
 
图 32：安装在底盘上的元件宽带随机振动曲线 
要求： 
样件必须在测试前、测试期间和测试后功能完好，功能状态等级为 A 级，并且所
有的参数必须在规定范围内，按照章节 3.3 通过持续的参数监控和 1 点测试进行
验证。 
 
5.2  机械冲击 
目标：该项测试是为验证零部件耐由坑槽和小碰撞（＜16英里/小时或25.7km/h）
造成的机械冲击性。 
 
测试： 
测试标准见 IEC 60068‐2‐29,Eb，Bump（撞击）和 IEC 60068‐2‐27 Ea,Shock（冲击），
参数如下： 
表 52：M2 机械冲击测试参数 
工作模式 
2.3 
机械冲击—坑槽 
零部件安装位置 
峰值 
时长 
冲击次数 
非簧载质量 
100G 
11ms 
20 X 6=120 
簧载质量（乘客舱） 
12G 
20ms 
400 x 6=2400 
簧载质量（其他安装区域，含框架） 
25G 
10ms 
400 x 6=2400 
冲击形式 
半正弦 
机械冲击—撞击 
说明 
数值 
加速度 
500G 
额定冲击时长 
11ms 
额定冲击形式 
半正弦 
所有方向（±X，±Y，±Z）的总冲击次
数 
3 X 6 个方向=18 
样件数量 
3 


### 第 48 页
要求： 
样件必须在测试前、测试期间和测试后功能完好，功能状态等级为 A 级，并且所
有的参数必须在规定范围内，按照章节 3.3 通过持续的参数监控和 1 点测试进行
验证。 
 
5.3 M3 耐久冲击测试 
目标：该测试是模拟安装在车门或盖板处的元件以及开关时有高加速的元件的加
速力测试。 
测试零部件耐松动和材料疲劳性。 
 
测试： 
测试标准见 DIN EN 60068‐2‐29，参数如下： 
 
表 53：M3 耐久冲击测试参数 
工作模式 
2.3 
峰值加速度 
300m/s2 
冲击时长 
6ms 
冲击形式 
半正弦 
冲击次数 
安装区域 
冲击次数 
驾驶侧门 
100000 
副驾驶和后座侧门 
50000 
后盖/尾门 
30000 
发动机舱盖 
3000 
如果元件在多个区域安装，则选择最大冲击测试 
安装位置 
测试设备上的样件位置必须和车辆上的安装位置符
合一致 
样件数量 
3 
 
要求： 
样件必须在测试前、测试期间和测试后功能完好，功能状态等级为 A 级，并且所
有的参数必须在规定范围内，按照章节 3.3 通过持续的参数监控和 1 点测试进行
验证。 
 
5.4 M4 外壳撞击—肘部负荷 
目标：该项测试是为验证零部件外壳及里面的组件不受由肘部负荷造成的影响。 
测试： 
表 54：外壳撞击—肘部负荷测试参数 
工作模式 
1.1 
适用范围 
所有零部件 
监测 
测量负荷期间内的外壳偏移挠度以确保电路板上零
件的间隙 
测试方法 
零部件的设置应能允许在 13.0mm 或更大直径的区域
的所有外表进行测试，如图33所示，使零部件13.0mm
直径大小的区域均匀受力 110N 一秒钟（这相当于一


### 第 49 页
个人肘部施加的力），所有面都测试完后进行 1 点功
能/参数检验。 
 
 
图 33：零部件外壳上施加肘部力 
要求： 
1. 零部件应无电气退化现象或永久物理损坏现象（功能状态等级为 C 级）。 
2. 受力期间的外壳偏移不会碰到电路板，接插件除外。 
3. 偏向力不会造成罩盖分离或“打开”。 
 
5.5 M5 外壳撞击—脚部负荷 
目标：该项测试是为验证零部件外壳及里面的组件不受由脚部负荷造成的影响。 
测试： 
表 55：外壳撞击—脚部负荷 
工作模式 
1.1 
适用范围 
装车或汽车使用时可能会被脚踩的所有零部件 
监测 
测量负荷期间内的外壳偏移挠度以确保电路板上零
件的间隙 
测试方法 
将（50X50）mm 的硬钢板置于零部件上面，如图 34
所示，通过钢板对零部件顶部施加均匀的力 890N 一
分钟（这相当于一个人肘部施加的力），测试完后进
行 1 点功能/参数检验。 
测试棒直径：13.0mm 
固定在支撑结构上的零部件外
壳 
用支撑结构代表底盘或支架 
注意：向外壳中心或任何直径大于
13.0mm 的区域施力，该力可能会造
成外壳表面偏移。 


### 第 50 页
 
图 34：零部件外壳上施加脚部负荷 
要求： 
1. 零部件应无电气退化现象或永久物理损坏现象（功能状态等级为 C 级）。 
2. 受力期间的外壳偏移不会碰到电路板，接插件除外。 
3. 偏向力不会造成罩盖分离或“打开”。 
 
5.6 M6 跌落测试 
目标：该项测试模拟了底座上元件从整个过程链到安装到车上时的可能会出现的
自由落体活动。该测试是为确保零件跌落时没有受损，从而在装车时没有出现隐
蔽性损坏，如零件内部松动或出现裂纹。 
 
测试 
表 56：M6 跌落测试参数 
工作模式 
1.1 
跌落高度 
1m 
碰撞面 
混凝土地面 
测试循环 
3 个样件每个都要进行朝两个方向（第 1 个样件：±
X，第 2 个样件：±Y，第 3 个样件：±Z）进行跌落
测试 
样件数量 
3 
 
要求： 
必须用肉眼对样件进行检查，通过摇动来检查样件是否松动。 
 
—如果样件外表有损坏，必须将这个损坏记录在测试报告中。 
 
对钢板中心施加压力 
将 50mmX50mm 的钢板放置在零部件顶端 
用支撑结构代表底盘或支架 
接插件，仅供参考 
固定在支撑
结构上的零
部件外壳 
 


### 第 51 页
—如果样件外表完好的，则样件在测试结束后必须功能完好，功能状态等级为 A，
所有参数必须在规定范围内。按照章节 3.3 通过 5 点测试进行验证，内部不允许
有隐藏性损伤。 
 
5.7 M7 共振频率分析 
目标：这项分析是为了确认共振频率，以便于发现可能导致机械疲劳的结构弱化。 
 
测试： 
表 57：共振频率分析测试参数 
工作模式 
N/A 
适用范围 
有电路板的所有零部件 
监测 
N/A 
测试方法 
考虑电路板安装的结构，用有限元分析等软件来计
算电路板的共振频率 
要求： 
电路板的共振频率应大于 150Hz,当共振频率低于 150Hz 时，供应商必须提供合理
的纠正措施。福田验证工程师负责对纠正措施进行审核。 
 
5.8 M8 高海拔运输压力效应分析 
目标：验证在海拔高度为 15240m 没有增压的飞机上，运输期间可能导致的机械
破坏。 
 
测试： 
表 58：高海拔运输压力效应分析测试参数 
工作模式 
N/A 
适用范围 
密封的或者可能用于高海拔运输的零部件 
监测 
N/A 
测试方法 
采用下面一系列的步骤确保由于空运时低压应力影
响下结构的设计有足够的鲁棒性。 
1. 用有限元分析或对比法量化零部件（或者构件）
在内部压力下所产生的破裂的应力。考虑到材料
参数（如最小的料厚）的多样性和与温度相关的
材料弱化的影响（玻璃传热）用一个最坏的情况
分析处理。玻璃导热温度是材料的一种属性，像
硬度一样，可以改变其特性。 
2. 采用下列等式进行分析：Pburst
3（Passembly—Paltitude）
X DM,Pburst:零部件破裂应力，Passembly:在装配期间
零部件内部的压力（这可能是由于装配地点的环
境气压，或者是由于制造过程的调整产生的压
力），Paltitude:空运时在 15240m 的高度产生的压
力，取 11kPa,设计余量 DM:4 
要求： 
零部件的破裂应力必须超过运输过程中产生的内应力的 4 倍。 
 


### 第 52 页
5.9 M9 塑料卡扣紧固件分析 
目的：这项分析是确保充分设计卡扣的固定方式，同时也确认塑料卡扣结构鲁棒
性的设计原理，包括： 
� 有足够的保持力 
� 在总装和维修拆卸期间人机工程可以接受的拆卸力和安装力 
� 机械结构合理，防止发出咔咔声 
� 有足够的设计余量，确保在装配期间挠曲力度不超过塑料的弹性极限 
 
测试： 
表 59：塑料卡扣紧固件分析测试参数 
工作模式 
N/A 
适用范围 
有塑料卡扣的所有零部件 
监测 
N/A 
测试方法 
对卡扣进行有限元分析或者其他等同效果的分析来
证明设计元件的性能，包括： 
1. 有足够的保持力 
2. 在总装和维修拆卸期间人机工程可以接受的拆
卸力和安装力 
3. 机械结构合理，防止发出咔咔声 
4. 有足够的设计余量，确保在装配期间挠曲力度不
超过塑料的弹性极限 
要求： 
证明设计满足以上规定的四个设计要素。 
 
5.10 M10 碾压分析 
目标：这项分析是确认外壳的结构缺陷，可能导致零部件内部或者外壳本身有集
中应力。 
 
测试： 
表 60：碾压分析测试参数 
工作模式 
N/A 
适用范围 
在总装或维修时，可能会遭受肘负荷或脚负荷的零
部件，也包括在其他零部件的装配操作过程中可能
用到的支撑面 
监测 
N/A 
测试方法 
采用有限元分析确保零部件满足实际测试中规定的
碾压测试要求。测试负荷根据测试部分关于这一点
对人的肘或脚负荷的描述来定义 
要求： 
当施加力时，应该证明零部件的组成部分和外壳之间有足够的间隙，零部件外壳
的变形不能对零部件内部或者电路板产生力的作用。 
 
5.11 M11 振动噪声 
参照振动噪声测试 


### 第 53 页
 
5.12 M12 接插件测试 
参照接插件测试 
 
6  环境要求和测试 
表 61：环境测试概要 
测试 
C1  高低温存放测试 
C10  工作时盐雾测试，内部 
C2  低温工作 
C11  工作时盐雾测试，外部 
C3  温度梯度测试 
C12  防水测试 
C4  重新喷漆温度 
C13  冰冻测试 
C5  温度冲击（零部件，无外壳） 
C14  冷凝测试 
C6  溅水温度冲击测试 
C15  粉尘测试 
C7  浸水温度冲击测试 
C16  日照 
C8  湿热循环测试 
C17  化学要求和测试 
C9  稳态湿热测试 
C18  有害气体测试 
C19  高空工作过热分析 
C20  热疲劳分析 
C21  无铅焊分析 
 
 
6.1 C1  高低温存放测试 
目标：模拟零部件存放和运输时的热负载。该项测试是为验证零部件存储过程中
的耐高低温性，例如零部件在运输过程中（飞机，集装箱内）。如果该测试已在
测试计划开始时完成，它有助于将所有零部件调整至相同的初始条件上。 
测试： 
表 62：C1  高低温存放测试参数 
工作/运行模式 
1.1 
测试时长和测试温
度 
2 次循环，每次各 24 小时（在 Tmin 存放 12 小时，在 Tmax 存放
12 小时） 
样件数量 
见设计任务书中的测试顺序计划 
要求： 
样件测试前后必须功能完好，满足 A 级的要求，以及所有参数需满足标准要求。
按照 3.3 章通过 5 点测试进行验证。 
 
6.2 C2  低温工作 
目标：模拟零部件低温负载情况。该项测试是为验证在极端低温环境下长期停放
或工作后的零部件的运行功能。 
测试： 
测试标准见 DIN EN 60068‐2‐1,测试方法 Ab,参数如下： 
表 63：C2  低温工作测试参数 
工作/运行模式 
工作模式 2.1:12 小时 
工作模式 2.3，UBmin:12 小时 
工作模式 2.1:12 小时 
工作模式 2.3，UB:12 小时 
如果零部件在工作模式 2.3 状态下涉及到多种工作模式（见章


### 第 54 页
节 9.7），则零部件在要求工作模式 2.3 的期间内，所有与工
作模式 2.3 相关的运行模式的运行时间要相同。 
测试时长 
48 小时 
测试温度 
Tmin 
样件数量 
3 
注意：散热性零部件也需按照 DIN EN 60068‐2‐1 测试方法 Ab 进行测试。 
 
要求： 
测试前、测试期间以及测试后样件必须功能完好，功能状态等级为 A 级，所有参
数必须满足标准要求。采用持续参数监测和 1 点测试进行验证。 
 
6.3 C3  温度梯度测试 
目标：模拟零部件在不同环境温度下的工作运行情况。该项测试是为验证零部件
在间隔较短的工作温度范围内可能会出现的耐失效性。 
测试： 
表 64：C3  温度梯度测试参数 
工作/运行模式 
参数测试过程中 2.3，其他情形 2.1 
测试温度 
见图 35，每次温度变化上升 5℃
测试顺序 
每次温升，必须保持样件直到温度完全稳定（见 3.3.4 章），
然后按照参数测试一章（见 3.3.2 章）进行参数测试（功能测
试）。 
样件数量 
3 
 
图 35：温度梯度测试曲线 
要求： 
参数测试（功能测试）期间内所有样件参数必须满足标准要求，功能状态等级为
A 级。 
 
6.4 C4  重新喷漆温度 
目标：模拟零部件在再次喷漆期间的负荷。是为了零部件对由于热负荷导致的故
障的抵抗能力。例如：在钎焊连接、粘贴连接、耦合连接和焊接连接处产生裂纹


### 第 55 页
和在密封衬垫和外壳上产生裂纹。 
 
测试： 
表 65：C4 重新喷漆温度测试参数 
工作/运行模式 
2.1 
测试时长和测试温
度 
在 130℃时保持 15 分钟和在 110℃时保持 1 小时 
样件数量 
3 
要求： 
测试前和测试后测试样件必须功能完好，满足功能状态等级 A 级，所有参数必须
满足标准要求。按照 3.3 章中的 1 点测试法进行验证。 
 
6.5 C5  温度冲击（零部件，无外壳） 
目标 1（零部件）：模拟汽车运行时温度冲击变化时的零部件的热负载。该项测
试是为验证零部件耐由热负载引起的失效性，例如焊点、粘接处裂纹，以及密封
件或外壳裂纹。 
目标 2（无外壳）：该项测试不是模拟任何实际负载，它是用于检测电气总成上
的机械接头的弱点，如焊点。只在无外壳和机械部件的零部件电气总成上进行测
试。 
测试： 
测试标准见 DIN EN 60068‐2‐14（零部件）和 DIN EN 60068‐2‐14 Na（无外壳），参
数如下： 
表 66：C5  温度冲击测试参数（零部件，无外壳） 
工作/运行模式 
1.2（零部件），1.1（无外壳） 
最低温度 
Tmin 
最高温度 
Tmax 
高低温停留时间 
零部件达到指定温度后停留 15 分钟（见 3.3.4 章） 
转移时间（空气‐空
气，介质‐介质） 
≤30s（零部件），≤10s（无外壳） 
Nc 测试方法的测试
液体（零部件） 
汽车零部件运行时的液体 
测试（零部件） 
不是长期在液体中工作的零部件：DIN EN 60068‐2‐14NA,长期
在 液 体 中 工 作 的 零 部 件 ： DIN  EN  60068‐2‐14Nc  (IP  X8 
ISO20653) 
被测样件浸入液体中至少 25mm. 
循环次数 
100（零部件），300（无外壳） 
样件数量 
3 
要求： 
测试前以及测试后样件必须功能完好，功能状态等级为 A 级，所有参数必须满足
标准要求。按照 3.3 章采用 5 点测试进行验证。 
介质‐介质测试（零部件）附加要求： 
液体不能进入样件内。只有在所有测试计划都结束后才能打开样件（9.4 章）。 
 
6.6 C6  溅水温度冲击测试 


### 第 56 页
目标：该项测试应验证零部件在遇到溅水引起的突然温度变化的功能性。 
测试： 
表 67：C6 溅水温度冲击测试参数 
工作/运行模式 
间歇性，见图 36 
适用范围 
安装在汽车行驶中可能会发生溅水的区域处的零部件（例如
安装在发动机上） 
测试方法 
对样件进行加热来进行温度测试。然后按照图 36 进行溅水循
环测试，样件总宽度都应溅水。 
循环时长 
30 分钟 
测试温度 
Tmax 
测试介质 
含有 3%亚利桑那尘土的自来水，见标准 ISO  12103‐1,必须确
保持续混合。 
溅水温度 
0 到+4℃ 
溅水喷嘴 
0 到+4℃ 
喷嘴 
见图 37 
溅水时间 
3 秒 
流速 
每喷嘴 3 到 4 升 
喷嘴与样件之间的
距离 
300 到 350mm 
循环次数 
100 
样件数量 
3 
测试时，零部件必须模拟汽车内装车位置。测试装置由供应商和采购方协商确定
并记录存档。测试装置见图 38. 
 
 
图 36：溅水测试时间 
 
操
作
模
式 


### 第 57 页
 
图 37：溅水测试—喷嘴 
 
 
图 38：溅水测试装置 
 
要求： 
不允许水渗入，只有在所有测试都结束后才能打开样件。测试前、测试期间以及
测试后样件必须功能完好，功能状态等级为 A 级，所有参数必须满足标准要求。
按照 3.3 章采用持续参数监测和 1 点测试进行验证。 
 
喷水 
溅水 


### 第 58 页
6.7 C7  浸水温度冲击测试 
目标：模拟零部件浸入水中时的负载情况。该项测试是通过浸渍热的零部件来验
证突然遭遇冷却后的零部件的功能性。 
 
测试： 
测试标准见 ISO 20653，参数要求如下： 
表 68：C7 浸水温度冲击测试参数 
工作/运行模式 
如果零部件是在驾驶操作时运行： 
2.3 
如果零部件不是在驾驶操作时运行： 
2.1 
防护等级 
IP X7 
测试方法 
对样件进行加热来进行温度测试。保持测试温度直到样件温
度完全稳定下来（见 3.3.4 章）+15 分钟。然后在 5 秒之内将
样件完全浸入到测试介质中，测试样件浸入液体中至少
25mm。 
测试介质 
含 5%盐分的 0℃的冷水 
浸渍时长 
5 分钟 
循环次数 
20 
样件数量 
3 
 
如果有冷却液管路，冷却液温度不能超过 Tcool,max。只有环境温度可以超出上述
冷却液温度上限值。 
 
要求： 
不允许水渗入，只有在所有测试计划都结束后才能打开样件（8.2 章）。测试前、
测试期间以及测试后样件必须功能完好，功能状态等级为 A 级，所有参数必须满
足标准要求。按照 3.3 章采用持续参数监测和 1 点测试进行验证。 
 
6.8 C8 循环湿热测试 
目标：该项测试是为验证零部件抗内结露性，因为内结露可能会造成功能和材料
退化。由湿度变化生成的喘息效应、温度快速变化引起的冷凝，以及水冰冻膨胀
引起的裂纹均是该项测试的显著特征。 
 
测试： 
测试设备见标准 IEC 60068‐2‐38, Test Z/AD,温湿度循环测试： 
表 69：C8 湿热循环测试参数 
工作/运行模式 
零部件在 ON（将功率损耗降至最低）和睡眠模式之间通电和
循环以避免本地干燥：3.2。 
如果零部件没有睡眠功能，则旋转到 OFF 上。通常情况下，
零部件默认连续在 ON 状态 1 小时，睡眠/OFF 模式 1 小时，
共持续 10 天。 
电源电压采用 Unom。
总测试时长 
两天 1 次循环，共持续 5 次（2 X 5=10 天） 


### 第 59 页
监测 
ON 状态时，必须采用持续监测。睡眠/OFF 模式时，则持续
对寄生电流进行监测和记录以监测处测试过程中的失效问
题。 
测试温度 
高温：+65℃；中温：+25℃；低温：‐10℃ 
湿度循环特征 
见图 39 
适用范围 
所有零部件 
样件数量 
3 
 
图 39：循环湿度特征 
高温时的湿度为（93±3）%，从+65℃降到+25℃期间，湿度降到 80%。+25℃时，
湿度应上升至（93±3）%。当温度低于+25℃时，湿度无要求。 
 
要求： 
功能状态等级为 A 级。寄生电流增加不可超过 50%。检验时不能出现电性迁移或
枝状生长。 
 
6.9 C9 稳态湿热测试 
目标：该项测试是为验证零部件抗高湿度性，因为高湿度可能会造成功能和材料
退化。 
测试： 
测试设备见标准 IEC 60068‐2‐78, Test Z/AD,稳态湿热测试： 
表 70：C9 稳态湿热测试参数—严重度 1 
工作/运行模式 
零部件在 ON（将功率损耗降至最低）和睡眠模式之间通电和
循环以避免本地干燥：3.2。 
如果零部件没有睡眠功能，则旋转到 OFF 上。通常情况下，
零部件默认连续在 ON 状态 1 小时，睡眠/OFF 模式 1 小时，
共持续 10 天。 
电源电压采用 Unom。
监测 
ON 状态时，必须采用持续监测。睡眠/OFF 模式时，则持续
对寄生电流进行监测和记录以监测处测试过程中的失效问


### 第 60 页
题。 
测试温度 
（+65±3）℃ 
湿度 
（90±5）% 
测试时长 
10 天 
适用范围 
所有零部件 
样件数量 
3 
要求： 
功能状态等级为 A 级。寄生电流增加不可超过 50%。检验时不能出现电性迁移或
枝状生长。 
 
6.10 C10 工作时盐雾测试，内部 
目标：该项测试是为验证零部件在沿海地区和道路盐分飞溅环境下的耐盐雾性，
测试样件应置于装车环境下。 
 
测试： 
测试标准见 DIN EN 60068‐2‐11Ka,参数要求如下： 
表 71：C10 工作时盐雾测试参数，内部 
工作/运行模式 
喷射阶段：间歇性，工作模式 2.1:55 分钟，工作模式 2.3:5 分
钟；静止阶段：工作模式 2.1
测试循环 
每个测试循环由 8 小时的喷射阶段和 4 小时的静止阶段组成，
见图 40；静止阶段应在盐雾测试箱内维持。 
测试温度 
35℃ 
循环次数 
2 次 
样件数量 
3 
测试时，零部件必须模拟汽车内装车位置。测试装置由供应商和采购方协商确定
并记录存档。 
 
图 40：工作时盐雾喷射，内部—喷射阶段 
喷射阶段 
静止阶段 


### 第 61 页
要求： 
测试前、测试期间以及测试后样件必须功能完好，功能状态等级为 A 级，所有参
数必须满足标准要求。按照 3.3 章采用持续参数监测和 1 点测试进行验证。 
 
6.11 C11 工作时盐雾测试，外部 
目标：该项测试是为验证零部件在沿海地区和道路盐分飞溅环境下的耐盐雾性，
测试样件应置于装车环境下。 
 
测试： 
测试标准见 DIN EN 60068‐2‐11Ka,参数要求如下： 
 
表 71：C11 工作时盐雾测试参数，外部 
工作/运行模式 
喷射阶段：间歇性，工作模式 2.1:1 小时，工作模式 2.3:1 小
时；静止阶段：工作模式 2.1
测试循环 
每个测试循环由 8 小时的喷射阶段和 4 小时的静止阶段组成，
见图 41；静止阶段应在盐雾测试箱内维持。 
测试温度 
35℃ 
循环次数 
安装在底盘、发动机上的零部件：12 次；其他零部件：8 次
样件数量 
3 
测试时，零部件必须模拟汽车内装车位置。 
 
 
图 41：工作时盐雾喷射，外部—喷射阶段 
 
要求： 
测试前、测试期间以及测试后样件必须功能完好，功能状态等级为 A 级，所有参
数必须满足标准要求。按照 3.3 章采用持续参数监测和 1 点测试进行验证。 
 
6.12 C12 防水测试 
喷射阶段 
静止阶段 
电气运行 
1 次循环 


### 第 62 页
目标：模拟零部件遭受水（≤IPX6K）或汽车清洗（IPX9K）时的负载情况。该项
测试是为验证暴露在冷凝水、雨水和喷水（≤IPX6K）或高压清洗喷水（IPX9K）
时的功能性。 
测试： 
测试标准见 ISO 20653,参数要求如下： 
 
表 73：C12 防水测试参数 
工作/运行模式 
间 歇 性 ， 工 作 模 式 2.1:1 分 钟 ， 工 作 模 式 2.3:1 分 钟
（IPX6K）;2.1(IPX9K)
水压（IPX9K） 
高压清洗机的最低水压为 10000kPa(100bar),直接在喷嘴处测
得 
水温（IPX9K） 
80℃ 
测试方法 
从任一可接近的汽车空间方向向样件喷水 
样件数量 
3 
要求： 
必须满足根据 ISO 20653 制定的设计任务书中的防水等级。不允许水渗入。只有
在所有测试计划都结束后才能打开样件。测试前、测试期间以及测试后样件必须
功能完好，功能状态等级为 A 级，所有参数必须满足标准要求。按照 3.3 章采用
持续参数监测和 1 点测试进行验证。 
 
6.13 C13 冰冻测试 
目标：该项测试是为验证低温环境下的零部件性能，模拟零部件周围生成冰冻。 
 
测试： 
表 74：C13 冰冻测试参数 
工作/运行模式 
2.1；2.3 
适用范围 
外壳或机械部件允许有积水的所有零部件，安装在乘客舱的
零部件则除外 
监测 
工作模式 2.3 时持续监测 
测试方法 
如果测试的零部件会机械移动时，测试装置需模拟装车环境，
所有机械附件需安装到位。 
1. 默认在工作模式 2.3时根据防护等级在 Troom进行防水测试
2. 在 5 分钟内将零部件转移到低温箱内，并在工作模式 2.1
下 Tmin 温度浸泡 24 小时 
3. 24 小时浸泡结束后，且当温度还停留在 Tmin 时，零部件在
工作模式 2.3 时应能正常工作 1 小时。 
4. 重复步骤（1）到步骤（3）5 次 
样件数量 
3 
要求： 
功能状态等级应为 A 级。 
 
6.14 C14 含电气总成的冷凝测试 
目标：该项测试是为验证零部件在高湿度环境下耐快速温度变化性，快速温度变
化可能会导致间歇性失效，进而影响功能。 


### 第 63 页
测试： 
表 75：C14 含电气总成的冷凝测试参数 
工作/运行模式 
低温阶段：1.2；高湿度：3.2
监测 
在触发计时器期间进行持续监测 
适用范围 
所有密封型和封闭型零部件 
测试方法 
测试方法见 IEC 60068‐2‐30 Db,湿热循环测试，温湿度要求见
图 42 
样件数量 
3 
 
图 42：冰冻测试要求 
循环次数参考表 76 
 
表 76：冰冻测试要求 
零部件类型 
已进行过的循环次数
有或无压力转换薄
膜的密封零部件 
10 
无通风孔的非密封
型零部件 
1 
有通风孔的非密封
型零部件 
0 
要求： 
功能状态等级应为 A 级。检验时不能出现电性迁移或枝状生长。 
 
6.15 C15 防尘测试 
目标：该项测试是为验证零部件外罩的防尘等级，这些可以是风沙、道路灰尘或
形成冰冻 
形成露水 
在‐20℃储存 
在 45℃和 95~98%湿度期间进行通电和监测 
在‐20℃和温度转换时湿度无要求 


### 第 64 页
其他类型的灰尘。灰尘的积累会影响散热性以及电气和机械零部件。 
 
测试： 
表 77：C15 防尘测试参数 
工作模式 
电气电子零部件：工作模式 2.1 
机械零部件（如带风扇的零部件）： 
按照图 615 间歇性执行工作模式 2.3 和工作模式 2.1. 
如果零部件在工作模式 2.3 状态下涉及到多种工作模式（见章
节 9.7），则零部件在要求工作模式 2.3 的期间内，所有与工
作模式 2.3 相关的运行模式的运行时间要相同。 
适用范围 
所有零部件 
监测 
N/A 
测试方法 
1. 测试方法见 ISO 20653:2006,见图 43 
2. 进行 1 点功能/参数测试 
防尘等级 
见设计任务书 
测试时长 
20 次循环，每次 20 分钟 
样件数量 
6 
 
测试时，零部件必须模拟汽车内装车位置。测试装置由供应商和采购方协商确定
并记录存档。 
要求： 
必须满足根据 ISO 20653 制定的设计任务书中的防尘等级。测试前、测试期间以
及测试后样件必须功能完好，所有参数必须满足标准要求。此外，必须肉眼检查
零部件，摇晃零部件检查是否有松散和异响。 
供应商需将变化和损坏记录在检测报告中，并和采购方一起评估。 
 
6.16 C16 日照 
目标：模拟阳光和紫外线照射到零部件上，该项测试是为验证零部件耐由材料疲
劳引起的损坏性，如裂纹和变色。 
测试： 
测试标准见 DIN 75220,测试参数如下： 
表 78：C16 日照测试参数 
工作模式 
1.1 
测试选择 
根据零部件装车位置选择测试要求，测试标准见 DIN 75220 
外部零部件 
根据 DIN 75220 表 2 和表 5 选择 Z‐OUT 
内部零部件 
根据 DIN 75220 选择 Z‐IN 
测试时长 
25 天（15 天干燥，10 天潮湿） 
循环次数 
1 
样件数量 
3 
工作模式 II.c 
灰尘 
工作模式 II.a 


### 第 65 页
要求： 
测试前以及测试后样件必须功能完好，功能状态等级为 A 级，所有参数必须满足
标准要求。然后按照 3.3 章通过 5 点测试进行验证。此外，必须肉眼检查零部件。
供应商需将变化和损坏记录在检测报告中，并和采购方一起评估。 
 
6.17 C17 化学要求和测试 
目标：模拟零部件在不同化学试剂中的负载情况，该项测试是为验证零部件耐化
学试剂性。 
 
测试： 
表 79：C17 化学要求和测试参数 
工作模式 
见设计任务书
化学试剂 
见设计任务书，不同安装位置的常见化学试剂见表 80，应用
方法见表 81 
条件处理 
如无其他规定，在标准气候条件下存放样品和化学试剂。 
测试方法 
1. 在室温条件下施加化学试剂，如设计任务书无其他规定，
则根据表 81 为每种试剂选择合适的应用类型。所选的应
用类型需注明在检测报告内，必须确保样件被试剂浸泡
住。 
2. 然后在表 80 规定的温度下存放样件，存放时间见具体规
定。 
测试时长 
见表 80 
样件数量 
每种试剂 1 个样件 
如果采购方同意，允许多个试剂共用 1 个样件。 
注意：必须注意化学品的安全提示和警告。 
 
表 80：化学试剂 
ID 
化学试剂 
样 件
温 
度 
暴 露
时间 
化学试剂举例 
1 
柴油 
Tmax 
22h 
EN 590 
2 
生态柴油 
Tmax 
22h 
EN 14214 
3 
无铅汽油 
Troom 
10min
EN 228 
4 
煤油 
Troom 
10min
ASTM 1655 
5 
甲醇 
Troom 
10min
CAS 67‐56‐1 
6 
发动机机油 
Tmax 
22h 
多 级 通 用 机 油 SAE  0W40,API 
SL/CF 
7 
差速器油 
Tmax 
22h 
双曲面齿轮油 SAE  75W140,API 
GL‐5 
8 
变速箱油 
Tmax 
22h 
ATF Dexron III  
9 
液压油 
Tmax 
22h 
DIN 51 524‐3（HVLP ISO VG 46）
10  润滑脂 
Tmax 
22h 
DIN 51 502（KP2K‐30） 
11  硅油 
Tmax 
22h 
CAS 63148‐58‐3（AP 100） 
12  蓄电池液 
Troom 
22h 
37% H2SO4 


### 第 66 页
13  刹车液 
Tmax 
22h 
ISO 4926 
14  防冻液 
Tmax 
22h 
乙烯乙二醇（C2H6O2）‐水（混合
比例 1:1） 
15  尿素 
Tmax 
22h 
ISO 22241‐1 
16  空腔保护蜡 
Troom 
22h 
如：底盘保护，公司 Teroson1 
17  防腐剂 
Troom 
22h 
如：W550（公司 Pfinder）1 
18  去防腐剂 
Tmax 
22h 
如：Friapol750（公司 Pfinder）1
19  挡风玻璃清洗液 
Troom 
2h 
5%阴离子表面活性剂，蒸馏水 
20  自动化的汽车清洗液 
Troom 
2h 
CAS 25155‐30‐0 
CAS 9004‐82‐4 
21  内部清洗剂/驾驶舱喷雾
剂 
Troom 
2h 
如：驾驶舱喷雾剂（公司 Motip）
1 
22  玻璃清洗剂 
Troom 
2h 
CAS 111‐76‐2 
23  轮辋清洁剂 
Troom 
2h 
如：Xtreme（Sonax）1 
24  冷清洗剂 
Troom 
22h 
如 ： P3‐Solvclean  AK （ 公 司
Henkel）1 
25  丙酮 
Troom 
10min
CAS 67‐64‐1 
26  清洗汽油 
Troom 
10min
DIN 51635 
27  含氨气的清洗剂 
Troom 
22h 
如：Ajax（公司 Henkel）1 
28  提纯酒精 
Troom 
10min
CAS 64‐17‐5（Ethanol） 
29  接触喷雾剂 
Tmax 
22h 
如：WD 401 
30  汗水 
Troom 
22h 
DIN 53160 
31  化妆品如：润肤膏 
Troom 
22h 
如：Nivea，Kenzo1 
32  含咖啡因和糖分的清凉饮
料 
Troom 
22h 
Cola（可乐） 
33  除霜剂（航空） 
Troom 
2h 
SAE AMS 1435A 
 
1)生产商举例，具体化学试剂与专业部门确认。 
 
表 88：应用方式 
代码 
应用方法 
1 
喷雾 
2 
用毛刷涂 
3 
擦拭（如：用棉质擦布） 
4 
浇注 
5 
短暂浸没 
6 
浸没 
要求： 
测试前以及测试后样件必须功能完好，功能状态等级为 A 级，所有参数必须满足
标准要求。然后按照 3.3.2 章通过 5 点测试进行验证。标签字体如有变化需注明
在检测报告内并和采购方协调。 
 
6.18 C18 有害气体测试 
目标：模拟有害气体进入零部件尤其是接插件和开关内的影响，该项测试是为验


### 第 67 页
证零部件耐失效性，如腐蚀和零部件损坏。 
 
测试： 
测试标准见 DIN EN 60068‐2‐60,method 4,测试参数如下： 
表 82：C18 有害气体测试 
工作模式 
1.2 
温度 
Troom 
湿度 
75% 
有害气体浓度 
SO2 
0.2ppm 
H2S 
0.01ppm 
NO2 
0.2ppm 
Cl2 
0.01ppm 
测试时长 
21 天 
样件数量 
6 
要求： 
测试前以及测试后样件必须功能完好，功能状态等级为 A 级，所有参数必须满足
标准要求。然后按照 3.3.2 章通过 5 点测试进行验证。此外必须对开关和端子的
接触电阻进行测量，测量值必须符合标准要求。 
 
6.19 C19  高空工作过热分析 
目标：该项分析应能识别出冷却缺陷，该缺陷可能会引起由于高空空气密度低引
起的部件过热现象。电路板上包含重要发热元件以及由气流（也就是对流）冷却
的所有零部件都需进行高空分析。高空空气密度减少会减少对流热传递，进而可
能会引起过热现象。 
 
测试： 
表 83：高空工作热分析参数 
工作模式 
N/A 
适用范围 
由于高空空气密度低导致散热降低的所有零部件 
监测 
N/A 
测试方法 
采用下列等式计算零部件在高空时的最大工作温度： 
Tmax_part□□Taltitude=Tpart_oper X Multiplieraltitude +Tambient +10℃ 
Tmax_part：零件所允许的最大温度 
Taltitude:计算得到的高空工作温度 
Tambient：高空环境温度，默认+35℃ 
Tpart_oper：由于操作运行引起的温度上升（零件在海平面时的
温差） 
Multiplieraltitude（系数）：基于高空和气流，采用表 85 中的
系数级来调整等式中的高空温升 
+10℃：安全界线 
 
 
 


### 第 68 页
表 84：过热分析参数 
高度 
系数 
冷却风扇（低功率
风扇） 
冷却风扇（高功率
风扇） 
自然冷却对流（无
风扇） 
0m 
1 
1 
1 
4572m 
1.77 
1.58 
1.33 
注意： 
FM 计算时系数 1.33 最常用。 
要求： 
Tmax_part 必须≥Taltitude，Tmax_part 是基于发热元件以及周围其他受到影响的零件的操
作运行规范。 
 
6.20 C20 热疲劳分析 
目标：当不同热膨胀系数（CTE）的材料组合在一起时，该项分析应能识别出由
温度循环变化引起的热疲劳缺陷。例如，电路板部件不同的热膨胀系数会造成接
头的疲劳应力，这些接头是将零件接到电路板上（如焊料和导线）。电路板总成
上不同材料的膨胀率（如灌封材料）可能会造成接头和/或零部件结构变形，进
而导致电气或机械问题。 
 
测试： 
表 85：热疲劳分析测试参数 
工作模式 
N/A 
适用范围 
所有零部件
监测 
N/A 
测试方法 
确认零部件所有子件的包装类型，确定每个包装类型的热膨
胀系数（CTE），以及包装里面的所有零部件型号。确定每个
包装类型的 CTE 差异以及所连接部件的 CTE 差异，确定用来
测定疲劳寿命的分析方法以及提供分析方法验证的证据。然
后通过温度循环测试中零部件的伸缩率进行分析来量化部件
接头的疲劳寿命。 
要求： 
计算得到的疲劳寿命至少为零部件必需寿命的 3 倍。 
 
6.21 C21  无铅焊分析 
目标：该项分析应能识别出由于使用无铅焊料而引起的焊点缺陷。该项分析支持
硬件设计评审（Hardware Design Review）来评估焊接流程。 
 
测试： 
表 86：C21 无铅焊分析参数 
工作模式 
N/A 
适用范围 
所有用无铅焊料加工的零部件
监测 
N/A 
测试方法 
参考附录 A（无铅焊注意事项）来分析产品设计和制造过程
中的潜在影响。根据失效模式编制设计评审（DRBFM）。使用
无铅焊料时在整个  供应链期间需提供风险信息，通过设计评


### 第 69 页
审和 DRBFM 流程说明和降低风险。将 DRBFM 提交给福田。
 
要求： 
该项分析应能证明无铅焊已评审过，且在测试计划、设计和过程中已作出相应更
改。产品数据表应能支持所有无铅焊过程。 
 
7  使用寿命测试 
表 87：使用寿命要求 
使用寿命 
通用件：10 年；关键零部件：12 年
驾驶操作时的工作时
数 
5400 小时 
里程数 
200000km 
测试时长由温度循环次数得出，公式如下： 
 
 
 
 
 
 
 
其中 
N1 test:测试中所需温度循环次数 
Nprac:实际所需温度循环次数：7300（10 年） 
R:存活率：0.9（标准）； 
PA:置信级（假设）：0.75； 
ß:威布尔系数：2.0（实验中测得，用于导线断裂）； 
n:样件数量：6（短试样） 
 
7.1 L‐01 寿命测试—机械/液压耐久测试 
目标：模拟汽车使用寿命期间内的零部件运转/执行循环，该项测试是为验证零
部件的质量和可靠性如制动器启动、座椅调整循环、开关次数。 
测试：测试细节按照运行/执行循环次数在设计任务书中进行定义。 
 
表 88：L‐01 寿命测试—机械/液压耐久测试参数 
 
9  附录 
工作模式 
按照运行/执行循环次数，工作模式：2.3
测试温度 
按照温度谱规定的温度进行运行功能/执行循环测试，测试时
长根据百分比来定。 
运行功能/执行循环
次数 
见设计任务书 
样件数量 
6 
如果有冷却液管路，冷却液温度不能超过 Tcool,max。只有环境温度可以超出上述
冷却液温度上限值。 


### 第 70 页
要求： 
测试件必须在测试前、测试中和测试后都是功能齐全的，并且所有的关键参数必
须在规定范围内，通过持续的参数监控进行确认。 
只有在测试过程中无法对元件功能进行完整监测时，才要在中间测试时间和参数
测试的 25%，50%和 75%时再测量。 
中间测量必须进行参数测试（大）验证。 
持续参数监控的数据必须关于漂移、趋势和突出特性或异常进行评估。 
冷却液管路上的元件： 
冷却液管路上含有铜件覆盖的元件，再测试结束后用 20 倍放大显微镜对铜件进
行观察，不允许出现明显缺陷和铜腐蚀。 
 
7.2 L‐02 寿命测试—高温耐久测试 
目标：模拟汽车使用寿命期间内的零部件热负载影响。该项测试是为验证零部件
在热作用下耐缺陷的质量和可靠性，如：扩散，迁移和氧化。 
测试： 
 
7.2.1 不和冷却液管路连接的元件且在高温下性能不会降低的元件测试 
测试标准见 DIN EN 60068‐2‐2，参数如下： 
表 89：L‐02 高温耐久测试参数—不和冷却液管路连接的元件且在高温下性能不
会降低的元件测试 
工作模式 
工作模式 2.3：47h,工作模式 2.1:1h（间歇性） 
测试时间 
章节 9.7 中的相关运行模式：按照附录 9.5.1（Arrhenius
模式）计算测试各部分测试时间；停车或离网停车运行模
式一般不用考虑。 
各部分测试时间的总和即是总测试时长，并规定在设计任
务书中 
测试温度 
Tmax 
样件数量 
6 
 
7.2.2 不和冷却液管路连接的元件但在高温下性能会降低的元件测试 
从高温时 Top,max 起性能降低的零部件（如 LCD 背光照明降低）：表 90 中的测试不
是按照 Tmax 测试温度，而是按照下列温度参数实施： 
 
测试方法见标准 DIN EN 60068‐2‐2,参数如下： 
表 90：L‐02 高温耐久测试参数—不和冷却液管路连接的元件但在高温下性能会
降低的元件测试 
 
样件运行方式 
见图 44 
测试时间 
章节 9.7 中的相关运行模式：按照附录 9.5.2(高温下性能
降低的 Arrhenius‐模型应用)计算测试各部分测试时间；
停车或离网停车运行模式一般不用考虑。 
各部分测试时间的总和即是总测试时长，并规定在设计
任务书中 Tmax 和 Top,max 之间的各自的爬坡时间不计算在
测试时间内。 


### 第 71 页
测试温度 
见图 44 
间隔时间 t1 
按照附录 9.5.2 进行计算，并规定在设计任务书中。 
间隔时间 t2 
按照附录 9.5.2 进行计算，并规定在设计任务书中。 
样件数量 
6 
 
*）当 T＞Top,max 时，允许性能降低。 
图 44：高温时性能降低的元件温度测试曲线 
 
7.2.3 连接到冷却液管路的元件测试 
测试方法见标准 DIN EN 60068‐2‐2,参数如下： 
 
表 91：L‐02 高温耐久测试参数—和冷却液管路连接的元件测试 
 
工作模式 
工作模式 2.3：47h,工作模式 2.1:1h（间歇性） 
测试时间 
章节 9.7 中的相关运行模式：按照附录 9.5.3(冷却液管路
元件的 Arrhenius‐模型应用)计算测试各部分测试时间；
停车或离网停车运行模式一般不用考虑。 
各部分测试时间的总和即是总测试时长，并规定在设计
任务书中 
环境测试温度 
按照附录 9.5.3（冷却液管路元件的 Arrhenius‐模型应用）
冷却液测试温度 
按照附录 9.5.3（冷却液管路元件的 Arrhenius‐模型应用）
样件数量 
6 
 
要求： 
测试件必须在测试前、测试中和测试后都是功能齐全的，并且所有的关键参数必
须在规定范围内，通过持续的参数监控进行确认。只有在测试过程中无法对元件
功能进行完整监测时，才要在中间测试时间和参数测试的 25%，50%和 75%时再


### 第 72 页
测量。 
中间测量必须进行参数测试（大）验证。 
持续参数监控的数据必须关于漂移、趋势和突出特性或异常进行评估。 
冷却液管路上的元件： 
冷却液管路上含有铜件覆盖的元件，再测试结束后用 20 倍放大显微镜对铜件进
行观察，不允许出现明显缺陷和铜腐蚀。 
 
7.3 L‐03 寿命测试—温度循环耐久测试 
目标：该测试通过温度变化测试模拟车辆使用寿命内对元件的热负荷要求。 
用来测试元件在热负荷作用下抑制缺陷形成的质量和可靠性，如：焊接、粘接、
压焊、焊接连接部位，密封件和护套的老化和裂纹。 
 
测试 
测试标准见 DIN EN 60068‐2‐14，参数如下： 
7.3.1 不和冷却液管路连接的元件且在高低温下性能不会降低的元件测试 
 
表 92：L‐03 温度变化耐久测试参数—不和冷却液管路连接的元件且在高低温下
性能不会降低的元件测试 
工作模式 
按照图 45：运行方式 2.3 和运行方式 2.1（间歇执行） 
温度曲线 
按照图 45 
最低测试温度 
Tmin 
最高测试温度 
Tmax 
温度梯度 
4℃/min 
如果这个温度梯度在测试仪中无法实现，则温度梯度可以
与委托方协商，将数值降低至最小 2℃/min。 
Tmin 和 Tmax 停留时
间 
待温度完全稳定后 15min 
循环次数 
总测试循环次数需考虑相关运行模式（章节 9.7），按照附
录 9.6.1(Coffin‐Manson 模型)进行计算，且规定在零部件设
计任务书中。 
样件数量 
6 
 
图 45：L‐03 温度变化耐久测试温度曲线—不和冷却液管路连接的元件且在高低


### 第 73 页
温下性能不会降低的元件测试 
 
7.3.2 不和冷却液管路连接的元件但在高低温下性能会降低的元件测试 
Top,min 以下温度和 Top,max 以上温度起性能降低的零部件（如 LCD 背光照明降低），
测试参数如下： 
 
表格 93：L‐03 温度循环耐久测试参数  –不和冷却液管路连接的元件但在高低温
下性能会降低的元件测试 
工作模式 
依据图 46 工作模式 2.1 和 2.3 
温度曲线 
依据图 46 
最低测试温度 
Tmin 
最高测试温度 
Tmax 
温度梯度 
4℃/min 
Tmin，Tmax，Top,min 和 Top,max 停留
时间 
待温度完全稳定后 15min 
循环次数 
总测试循环次数需考虑相关运行模式（章节
9.7），按照附录 9.6.1(Coffin‐Manson 模型)进行
计算，且规定在零部件设计任务书中。 
样件数量 
6 
 
图 46：温度曲线—低温或者高温下性能降低的元件测试 
 
7.3.3 冷却液管路元件 
冷却液管路元件测试参数如下： 
 
表 94：L‐03 温度循环耐久测试—冷却液管路元件测试 
工作模式 
依据图 45 和 46 工作模式 2.1 和 2.3 
温度曲线 
依据图 45 和 46 
最低测试温度 
Tmin 和 Tcool,min
最高测试温度 
Tmax 和 Tcool,max 
温度梯度 
4℃/min 
Tmin，Tmax，Top,min 和 Top,max 停留
待温度完全稳定后 15min 
只有当 Top,min＞Tmin 才需要 
只有当 Top,max＜Tmax 才需要 


### 第 74 页
时间 
循环次数 
总测试循环次数需考虑相关运行模式（章节
9.7 ）， 按 照 附 录 9.6.2( 冷 却 液 管 路 元 件 的
Coffin‐Manson‐模型应用)进行计算，且规定在
零部件设计任务书中。 
样件数量 
6 
 
要求: 
测试件必须在测试前、测试中和测试后都是功能齐全的，并且所有的关键参数必
须在规定范围内，通过持续的参数监控进行确认。只有在测试过程中无法对元件
功能进行完整监测时，才要在中间测试时间和参数测试的 25%，50%和 75%时再
测量。 
中间测量必须进行参数测试（大）验证。 
持续参数监控的数据必须关于漂移、趋势和突出特性或异常进行评估。 
冷却液管路上的元件： 
冷却液管路上含有铜件覆盖的元件，再测试结束后用 20 倍放大显微镜对铜件进
行观察，不允许出现明显缺陷和铜腐蚀。 
 
8  分析 
 
8.1  分析任务 
 
当物理组件尚未可用时，应采用分析法来辅助零部件设计的可靠性。分析是
A/D/V 流程中最早开始进行的活动，它可以提供早期零部件设计学习和改善机会。
所有分析活动需注明在零部件环境测试计划中。设计冻结前需完成分析活动。 
 
8.2  开发任务 
开发活动是用来发现分析过程中未包含的或不能评估的缺陷和设计疏忽。首样应
进行开发活动以提高最早的机会来定性评估和改善物理零部件。HALT 就属于该
类型的开发活动。验证前应改进开发活动过程中发现的缺陷。所有开发活动包括
结果应注明在零部件环境测试计划中。 
 
8.3  设计验证（DV）任务 
设计验证是定量与定性的验证，零部件设计满足环境、耐久性和可靠性要求。用
于生产设计性的零部件（含生产性子件、材料和过程包括焊料和焊剂）都需进行
DV 活动。所有 DV 活动包括结果应注明在零部件环境测试计划中。 
 
8.4  产品验证（PV） 
 
8.4.1  任务 
产品验证是定量与定性的验证，零部件满足环境、耐久性和可靠性要求，包括生
产制造影响。利用采用生产工具和过程制造的零部件进行 PV 活动。所有 PV 活
动包括结果应注明在零部件环境测试计划中。 
 


### 第 75 页
8.4.2  运输振动 
目标：该项测试是为验证运输过程中包装好的零部件的耐受性。 
 
测试： 
表 95：运输振动测试参数 
工作模式 
1.1 
适用范围 
客户能看得见表面的所有零部件 
监测 
N/A 
测试方法 
使用一个测试夹具能将集装箱（包装箱）从振
动台上的纵轴自由移动。夹具由比集装箱稍大
的四个立柱的底盘组成，能从三个方向放置集
装箱。 
1. 三个相互垂直的方向中每个都要振动集装
箱 24 小时，方法见标准 ASTM  D4728：集
装箱随机振动测试标准测试方法。该项测
试在集装箱内零部件的一个包装箱上进
行。集装箱应包括所有内部包材。随机振
动参数要求见表 96 和图 47. 
2. 检查所有零部件是否有结构损坏和外观变
化。 
3. 从集装箱的最高应力区域选择零部件（默
认 3 个样件）进行 5 点功能/参数测试。 
 
表 96：运输随机振动参数要求 
频率 
功率谱密度 
10Hz 
0.096000(m/s2)2/Hz=0.00100G2/Hz 
12Hz 
0.962000(m/s2)2/Hz=0.01002G2/Hz 
100Hz 
0.962000(m/s2)2/Hz=0.01002G2/Hz 
200Hz 
0.000962(m/s2)2/Hz=0.00001G2/Hz 
300Hz 
0.000962(m/s2)2/Hz=0.00001G2/Hz 


### 第 76 页
 
有效加速度=9.81m/s2=1.0GRMS 
图 47：运输随机振动参数 
要求： 
功能状态等级为 C 级，零部件不应有看得见的损坏。 
 
9.1  安装区域 
福田图纸上需空一栏列出参考资料，该栏还需包括安装区域分类代码（见表 97）。 
 
安装区域如有重要变更，测试计划中的某些测试则需重新进行，这是因为环境参
数已变动。 


### 第 77 页
表 97：安装区域分类代码，温度范围和适用测试 
测试项目   
A 乘客舱 
A1 
A2 
A3 
A4 
A4a 
A4b 
无特殊要求 
直接日照 
热辐射（如加热
器） 
乘客舱车门
与乘客舱一侧连
接 
与车门孔侧连
接 
额定工作电压范围℃ 
‐40~+80 
‐40~+105 
‐40~+90 
‐40~+80 
‐40~+80 
‐40~+80 
额定存储温度范围℃ 
‐40~+90 
‐40~+115 
‐40~+120 
‐40~+100 
‐40~+100 
‐40~+100 
M1 振动测试 
√ 
M2 机械冲击 
√ 
M3 耐久冲击测试 
严重度根据安装位置而定 
M4 外壳撞击—肘部负荷 
严重度根据安装位置而定 
M5 外壳撞击—脚部负荷 
严重度根据安装位置而定 
M6 跌落测试 
√ 
C1 高低温存放 
√ 
C2 低温工作 
√ 
C3 温升测试 
√ 
C4 重新喷漆温度 
√ 
C5 温度冲击（零部件，无
外壳） 
√ 
C6 溅水温度冲击 
严重度根据安装位置而定 
C7 浸水温度冲击 
严重度根据安装位置而定 
C8 循环湿热测试 
√ 
C9 稳态湿热测试 
√ 
C10 工作时盐雾测试，内部 
√ 
C11 工作时盐雾测试，外部 
严重度根据安装位置而定 


### 第 78 页
C12 防水测试 
严重度根据安装位置而定 
C13 冰冻测试 
严重度根据安装位置而定 
C14 含电气总成的冷凝测
试 
严重度根据安装位置而定 
C15 防尘测试 
IP5KX 
C16 日照测试 
严重度根据安装位置而定 
C17 化学测试和要求 
√ 
C18 有害气体测试 
√ 
寿命耐久测试 
√ 
 
测试项目   
B 发动机舱 
B1 
B2 
B3 
B4 
B5 
与车身连接 
与进气歧管连接 
发动机上/里面 
变速箱/减速器上/
里 
散热器上/里面 
额定工作电压范围℃ 
‐40~+105 
‐40~+105 
‐40~+140 
‐40~+140 
‐40~+120 
额定存储温度范围℃ 
‐40~+120 
‐40~+120 
‐40~+140 
‐40~+140 
‐40~+140 
M1 振动测试 
8 小时/轴 
24 小时/轴 
8 小时/轴 
M2 机械冲击 
√ 
M3 耐久冲击测试 
√ 
M4 外壳撞击—肘部负荷 
√ 
M5 外壳撞击—脚部负荷 
√ 
M6 跌落测试 
√ 
C1 高低温存放 
√ 
C2 低温工作 
√ 
C3 温升测试 
√ 


### 第 79 页
C4 重新喷漆温度 
——— 
C5 温度冲击（零部件，无
外壳） 
100 次循环 
C6 溅水温度冲击 
100 次循环 
——— 
100 次循环 
C7 浸水温度冲击 
√ 
C8 循环湿热测试 
6 次循环 
C9 稳态湿热测试 
21 天 
C10 工作时盐雾测试，内部 
——— 
C11 工作时盐雾测试，外部 
√ 
C12 防水测试 
IPX4,48 次循环 
C13 冰冻测试 
√ 
C14 含电气总成的冷凝测
试 
严重度根据安装位置而定 
C15 防尘测试 
IP6KX 
C16 日照测试 
√ 
C17 化学测试和要求 
√ 
C18 有害气体测试 
21 天 
寿命耐久测试 
√ 
 
测试项目   
C 外部车身 
C1 
C2 
C3 
C4 
C5 
C6 
C7 
C8 
车身上
车身底部/
驾驶室下 
车轮/车轴
其他安装
区域 
发动机舱盖 
行李箱盖 
尾门 
A 柱和 C 柱之间 
额定工作电压范围℃ 
‐40~+80
‐40~+80 
‐40~+80 
‐40~+80 
‐40~+120 
‐40~+80 
‐40~+80
‐40~+80 
额定存储温度范围℃ 
‐40~+90
‐40~+90 
‐40~+90 
‐40~+90 
‐40~+120 
‐40~+90 
‐40~+90
‐40~+90 


### 第 80 页
M1 振动测试 
8 小时/轴 
M2 机械冲击 
严重度根据安装位置而定 
M3 耐久冲击测试 
√ 
M4 外壳撞击—肘部负荷 
√ 
M5 外壳撞击—脚部负荷 
√ 
M6 跌落测试 
√ 
C1 高低温存放 
√ 
C2 低温工作 
√ 
C3 温升测试 
1 次循环 
C4 重新喷漆温度 
+135:0.25 小时；+110:1 小时 
+135:0.25 小
时 
+135:0.25 小时；+110:1 小时 
C5 温度冲击（零部件，无
外壳） 
100 次循环 
C6 溅水温度冲击 
100 次循环 
—— 
100 次循环 
C7 浸水温度冲击 
30 次循环 
C8 循环湿热测试 
6 次循环 
C9 稳态湿热测试 
21 天 
C10 工作时盐雾测试，内
部 
√ 
C11 工作时盐雾测试，外
部 
√ 
C12 防水测试 
IPX9K 
C13 冰冻测试 
√ 
C14 含电气总成的冷凝测
试 
√ 
—— 
√ 
—— 


### 第 81 页
C15 防尘测试 
IP6KX 
C16 日照测试 
√ 
C17 化学测试和要求 
√ 
C18 有害气体测试 
21 天 
寿命耐久测试 
√ 
 
 
 
 
 
 
测试项目   
D 腔 
E 行李箱 
D1 
D2 
E 
通到外部的腔，孔 
通到内部的腔，孔 
行李箱 
额定工作电压范围℃ 
‐40~+80 
‐40~+80 
‐40~+80 
额定存储温度范围℃ 
‐40~+90 
‐40~+90 
‐40~+90 
M1 振动测试 
8 小时/轴 
M2 机械冲击 
严重度根据安装位置而定 
M3 耐久冲击测试 
严重度根据安装位置而定 
M4 外壳撞击—肘部负荷 
严重度根据安装位置而定 
M5 外壳撞击—脚部负荷 
严重度根据安装位置而定 
M6 跌落测试 
√ 
C1 高低温存放 
√ 
C2 低温工作 
√ 
C3 温升测试 
1 次循环 


### 第 82 页
C4 重新喷漆温度 
+110:0.5 小时 
——— 
C5 温度冲击（零部件，无
外壳） 
100 次循环 
C6 溅水温度冲击 
严重度根据安装位置而定 
C7 浸水温度冲击 
严重度根据安装位置而定 
C8 循环湿热测试 
6 次循环 
9 次循环 
C9 稳态湿热测试 
21 天 
C10 工作时盐雾测试，内部 
√ 
C11 工作时盐雾测试，外部 
——— 
C12 防水测试 
——— 
C13 冰冻测试 
——— 
C14 含电气总成的冷凝测
试 
严重度根据安装位置而定 
C15 防尘测试 
IP5KX 
C16 日照测试 
严重度根据安装位置而定 
C17 化学测试和要求 
√ 
C18 有害气体测试 
21 天 
10 天 
寿命耐久测试 
√ 
 
 
 
 
 
 
 
 


### 第 83 页
9.2  流程 
 
9.2.1  电气电子零部件 A/D/V 流程 
所有含电气电子器件的零部件都要遵守本标准定义的全球环境零部件分析/开发
/验证（A/D/V）流程。这包括但不限于动力总成、底盘、空调系统、内饰、车身、
封闭、汽车外部和电气系统。 
自 2008 年 11 月份起，所有和环境/耐久性 A/D/V 活动相关的测试计划、测试结
果、记录文件以及供应商和福田之间的数据交换都需通过福田供应商系统
（Supply Power）上传至福田全球 EMC/环境/耐久性数据库中。 
 
9.2.2 A/D/V 流程图 
A/D/V 流程图见图 48. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 84 页
                       
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
图 48：A/D/V 流程图 


### 第 85 页
9.2.2.1  需求评审 
福 田 内 部 进 行 需 求 评 审 ， 以 便 定 义 Q/FTP2800004‐2011 代 码 字 母 。 该
Q/FTP2800004‐2011 代码应注明在零部件设计任务书（CTS）中“验证”一章。
福田环境/耐久性专家（ENV/DUR）应和福田设计认可工程师（DRE）以及零部件
验证工程师一起定义该信息。福田环境/耐久性专家（ENV/DUR）在采购前对零
部件设计任务书进行评审以确认 Q/FTP2800004‐2011。同时福田材料工程师需提
供材料测试要求。Q/FTP2800004‐2011 供应商提出的例外需获得福田环境/耐久性
专家（ENV/DUR）的批准。如果零部件设计任务书不可行，请参考 SSTS 中验证
一章进行 Q/FTP2800004‐2011 编码。 
 
9.2.2.2  测试计划启动/硬件设计评审 
供应商定点之后 2 周由供应商和福田一起召开测试计划启动/硬件评审会议。该
会议的目的是对设计进行评审以对环境鲁棒性提出建议方案并讨论零部件环境
测试计划的期望值。详见本标准中的“硬件设计评审”一章。 
 
9.2.2.3  制定和评审测试计划 
供应商负责完成附录 B 中的零部件环境测试计划，并在供应商定点之后 6 周向福
田提交电子档可编辑的测试计划。供应商定点之后 10 周应获得批准。福田环境/
耐久性专家（ENV/DUR）有权索要可编辑的版本。 
 
9.2.2.4  执行 A/D/V 任务 
A/D/V 任务包括分析、开发和设计验证（DV）应通过 IV MRD 顺利完成以协助整
车验证。 
零部件环境测试计划的每项条款下都需注明测试结果，并将此文件提交给福田零
部件验证工程师（或福田环境/耐久性专家）供评估和认可。 
如有不一致之处，福田零部件验证工程师或福田环境/耐久性专家需和福田供应
商质量工程师（SQE）以及福田设计认可工程师（DRE）一起评审测试结果并确
定是否需要重新进行 DV 验证。然后福田零部件验证工程师或福田环境/耐久性专
家进行硬件设计评审。根据纠正措施修改零部件环境测试计划。 
 
9.2.2.5  执行产品验证（PV）任务 
PV 任务通过 VTC（Validation  Test  Complete）顺利完成。应对零部件环境测试计
划进行评审，并根据综合生产流程/变更和 DV 测试结果进行调整。 
零部件环境测试计划的每项条款下都需注明测试结果，并将此文件提交给福田零
部件验证工程师（或福田环境/耐久性专家）供评估和认可。 
如有不一致之处，福田零部件验证工程师或福田环境/耐久性专家需和福田供应
商质量工程师（SQE）以及福田设计认可工程师（DRE）一起评审测试结果并确
定是否需要重新进行 PV 验证。然后福田零部件验证工程师或福田环境/耐久性专
家进行硬件设计评审。根据纠正措施修改零部件环境测试计划。 
 
9.2.3  测试计划协商 
供应商在定点后 6 周向福田环境/耐久性专家提交完整的零部件环境测试计划电
子档（MS 格式）。 
 


### 第 86 页
9.2.4  硬件设计评审 
如图 49 所示，每个新的零部件以及有变更的正在生产的零部件都需进行硬件设
计评审，以确保设计满足环境/耐久性要求。这包括硬件相关的更改（内部构件
变更或材料变更）和制造过程变更（如模具、焊接过程或产地）。同时也需考虑
可能会影响 Q/FTP2800004‐2011 相关要求的软件变更。硬件设计评审由福田零部
件验证工程师安排和主导。建议硬件设计评审和 EMC 设计评审会议一起进行。 
硬件设计评审的目标： 
� 评审零部件的原理图设计和电路板设计 
� 评审零部件装配和机械结构 
� 评审电气/电子设计方案、机械设计方案和选材的技术合理性 
� 检查以前相关的分析、计算和测试结果 
� 评估零部件设计的潜在变更 
� 提出问题解决方案以及重新验证的方案 
� 验证电路板和装配设计满足零部件环境/耐久性要求 
� 评估制造过程和变更 
� 评估可能会影响 Q/FTP2800004‐2011 相关要求的软件变更 
� 进行热疲劳分析 
供应商提交内容： 
供应商至少在会议前的 10 个工作日向福田环境/耐久性专家和/或福田验证工程
师提交以下资料： 
� 功能描述 
� 装车位置 
� 零部件内外部的接口描述 
� 硬件原理图 
� 电气部件清单和相关数据表 
� 所有零件的材料数据表（如 PCB 所用到的材料，焊料、焊剂、总成件、连接
器等），包括热膨胀系数 
� 零件布置图 
� PCB Layout 
� 焊接过程描述（焊锡、焊接温度曲线、清洗材料、过程等） 
� 装配机械（装配图纸、安装/支撑位置、外壳开口、冷却方案等） 
注意：如果可行，可以提供实物样品或模型进行外观检查。 
出席人员： 
福田： 
� Q/FTP2800004‐2011 环境/耐久性专家 
� 零部件验证工程师 
� 设计认可工程师 
� EMC 专家（可选） 
� 供应商质量工程师（可选） 
供应商： 
� 硬件设计工程师 
� 电气系统工程师 
� 环境/耐久性专家 
� 验证/测试工程师 


### 第 87 页
� 项目经理（可选） 
 
9.2.5  分析活动 
供应商应按照批准的零部件环境测试计划进行分析活动。供应商按照零部件环境
测试计划向福田提交分析模型和假设。零部件环境测试计划的每项条款下都需注
明分析结果，并将此文件提交给福田零部件验证工程师（或福田环境/耐久性专
家）供评估和认可。 
 
9.2.6  开发活动 
供应商应按照批准的零部件环境测试计划进行开发活动。供应商按照零部件环境
测试计划向福田提供测试样品，这包括测试前和测试后的零件。零部件环境测试
计划的每项条款下都需注明测试结果，并将此文件提交给福田零部件验证工程师
（或福田环境/耐久性专家）供评估和认可。 
测试不通过的零部件应由供应商立即进行分析。该零部件不可维修，也不可用于
后续的测试。供应商应立即联系福田零部件验证工程师（或福田环境/耐久性专
家）以确定后续行动措施。 
 
9.2.7  设计验证活动 
供应商应按照批准的零部件环境测试计划进行设计验证（DV）。供应商按照零部
件环境测试计划向福田提供测试样品，这包括测试前和测试后的零件。零部件环
境测试计划的每项条款下都需注明测试结果，并将此文件提交给福田零部件验证
工程师（或福田环境/耐久性专家）供评估和认可。 
测试不通过的零部件应由供应商立即进行分析。该零部件不可维修，也不可用于
后续的测试。供应商应立即联系福田零部件验证工程师（或福田环境/耐久性专
家）以确定后续行动措施。 
 
9.2.8  产品验证活动 
供应商应按照批准的零部件环境测试计划进行产品验证（PV）。供应商按照零部
件环境测试计划向福田提供测试样品，这包括测试前和测试后的零件。零部件环
境测试计划的每项条款下都需注明测试结果，并将此文件提交给福田零部件验证
工程师（或福田环境/耐久性专家）供评估和认可。 
测试不通过的零部件应由供应商立即进行分析。该零部件不可维修，也不可用于
后续的测试。供应商应立即联系福田零部件验证工程师（或福田环境/耐久性专
家）以确定后续行动措施。 
 
9.3 A/D/V 活动总结 
默认的 A/D/V 活动见表 98，这些需注明在零部件环境测试计划中，并获得福田
的认可。只能根据福田认可的零部件环境测试计划进行 A/D/V 活动。 
 
 
 
 
 
 


### 第 88 页
表 98：A/D/V 活动总结 
活动 
阶段 
FSC 
分析 
电气 
正常和最坏情况性能分析 
A 
N/A 
短路/开路分析 
A 
N/A 
机械 
共振频率分析 
A 
N/A 
高海拔运输压力效应分析 
A 
N/A 
塑料卡扣紧固件分析 
A 
N/A 
碾压分析 
A 
N/A 
温度 
高海拔工作过热分析 
A 
N/A 
热疲劳分析 
A 
N/A 
无铅焊分析 
A 
N/A 
设计验证（DV） 
电气 
E1 寄生电流 
DV 
N/A 
E2 电源中断 
DV 
A，C 
E3 电池电压变化 
DV 
A，C 
E4 正弦叠加电压 
DV 
A 
E5 脉冲叠加电压 
DV 
A 
E6 信号和负载电路中的短路 
DV 
A 
E7 开路—单线中断 
DV 
C 
E8 开路—多线中断 
DV 
C 
E9 接地偏移 
DV 
A 
E10 不连续数字输入电压 
DV 
N/A 
E11 过载—所有电路 
DV 
D，E 
E12 绝缘电阻 
DV 
C 
E13 过电压 
D,DV 
C 
E14 瞬时过电压 
DV 
D,E 
E15 瞬时低电压 
DV 
D,E 
E16 跳线跨接启动 
D,DV 
C 
E17 抛负载 
DV 
/ 
E18 短时中断 
DV 
/ 
E19 启动脉冲 
DV 
/ 
E20 职能发电机控制电压曲线 
DV 
/ 
E21 反极性 
D,DV 
C 
E22 绝缘强度 
DV 
/ 
E23 反馈 
DV 
/ 
机械 
M1 振动测试 
DV,PV 
A 
M2 机械冲击 
DV 
A 


### 第 89 页
M3 耐久冲击测试 
DV 
C 
M4 外壳撞击—肘负荷 
DV 
C 
M5 外壳撞击—脚负荷 
DV 
C 
M6 跌落测试 
DV 
N/A 
气候 
C1 高低温存放 
DV 
A 
C2 低温工作 
DV 
A 
C3 温度梯度测试 
DV 
A 
C4 重新喷漆温度 
DV 
A 
C5 温度冲击（零部件，无外壳） DV 
A 
C6 溅水温度冲击 
DV 
A 
C7 浸水温度冲击 
DV 
A 
C8 湿热循环 
DV,PV 
A 
C9 稳态湿热 
DV,PV 
A 
C10 工作时盐雾测试，内部 
DV 
A 
C11 工作时盐雾测试，外部 
DV 
A 
C12 防水测试 
DV 
A 
C13 冰冻测试 
DV 
A 
C14 含电气总成件的冷凝测试 
DV 
A 
C15 防尘测试 
DV 
A 
C16 日照 
DV 
A 
C17 化学要求和测试 
DV 
A 
C18 有害气体测试 
DV 
A 
产品验证（PV） 
运输振动 
PV 
C 
 
9.4  测试顺序计划 
零部件的测试顺序计划需定义在零部件设计任务书中。 
根据测试表不需要的测试需从测试顺序计划中删除。如果需要重新调整测试顺序
计划，则可以对其进行调整。如果测试样件在进行 M01 跌落测试时没有受到损
坏，则必须将两个测试样件用于后续的顺序测试中。否则需采用备用件。 
 
9.4.1  顺序测试 
 
图 49：顺序测试 
 
 
 
 
 
 
 
 


### 第 90 页
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
C1 高低温存放 
5 点功能参数测试 
C3 温度梯度测试 
5 点功能参数测试 
C2 低温工作 
1 点功能参数测试 
C4 重新喷漆温度 
1 点功能参数测试 
C5 温度冲击（零部件） 
5 点功能参数测试 
C11 工作时 
盐雾测试，外部
1 点功能参数测试 
C15 防尘测试 
1 点功能参数测试 
C8 湿热循环 
5 点功能参数测试 
M3 耐久冲击测试
1 点功能参数测试 
M1 振动测试 
5 点功能参数测试 
C12 防水测试
1 点功能参数测试 
C6 溅水温度冲击 
1 点功能参数测试 
C7 溅水温度冲击 
1 点功能参数测试 
C3 温度梯度测试 
5 点功能参数测试 
完成 
M2 机械冲击测试
1 点功能参数测试 


### 第 91 页
9.4.2  顺序外测试（平行测试） 
图 50：顺序外测试 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
C1 高低温存放 
5 点功能参数测试 
 
M5 外壳撞击—
脚负荷 
M4 外壳撞击—
肘负荷 
M6 跌落测试 
C10 工作时盐雾
测试，内部 
C9 稳态湿热 
C14 含电气总成
件的冷凝测试 
C5 温度冲击（无
外壳） 
C16 日照 
C18 有害气体测
试 
C17 化学要求和
测试 
C13 冰冻测试 
振动噪音 
接插件测试 
5 点功能参数测试 
完成 


### 第 92 页
9.4.3  寿命顺序测试 
图 51：寿命顺序测试 
 
 
 
 
 
 
 
 
 
 
 
 
 
                                                                6 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
25% 
50% 
75% 
25% 
50% 
75% 
25% 
50% 
75% 
C1 高低温存放
5 点功能参数测试 
C3 温度梯度测试 
L01 寿命测试—机械/
液压耐久测试 
参数测试 
L02 寿命测试—高低
温耐久测试 
参数测试
L02 寿命测试—温度
循环测试 
参数测试
6
6
C3 温度梯度测试 
5 点功能参数测试 
物理分析 


### 第 93 页
9.4.4  电气顺序测试 
图 52：电气顺序测试 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
C1 高低温存放
5 点功能参数测试 
E2 电源中断
E3 电池电压变化 
E1 寄生电流 
E4 正弦叠加电压 
E5 脉冲叠加电压 
E6 信号和负载电路中
的短路
E7 开路—单线中断 
E8 开路—多线中断
E9 接地偏移 
E10 不连续数字输
入电压 
E11 过载—所有电
路
E12 绝缘电阻 
E13 过电压 
E14 瞬时过电压 
E15 瞬时低电压 
E16 跳线跨接启动 
E17 抛负载 
E18 短时中断 
E19 启动脉冲 
E20 智能发电机控
制电压曲线 
E21 反极性 
E22 绝缘强度 
E23 反馈 
5 点功能参数测试 
外观检查和拆解 
完成 


### 第 94 页
9.4.5  分析顺序测试 
图 53：分析顺序测试 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
C1 高低温存放 
5 点功能参数测试 
E24 正常和最坏
情况性能分析 
E25 短路/开路分
析
M7 共振频率分
析 
M8 高海拔运输
压力效应分析 
M9 塑料卡扣紧
固件分析 
M10 碾压分析 
C19 高海拔工作
过热分析 
C20 热疲劳分析 
C21 无铅焊分析 
5 点功能参数测试 
完成 


### 第 95 页
9.4.6 PV 顺序测试 
图 54：PV 顺序测试 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
C1 高低温存放
5 点功能参数测试 
运输振动 
5 点功能参数测试 
外观检查和拆解 
完成


### 第 96 页
9.5  高温耐久测试计算模型 
9.5.1 Arrhenius 模型 
针对高温耐久测试时长的计算：温度集百分比是依据设计任务书中的使用特征。 
表 99：温度集 
温度（℃） 
分配百分比（%） 
TFeld.1 
P1 
TFeld.2 
P2 
… 
… 
TFeld.n 
Pn 
工作时间 tBetrieb 也参见设计任务书。 
每个温度 TFeld.1…TFeld.n 使用下面的公式加速系数 AT,1…AT,n 进行计算： 
 
其中： 
AT,I    Arrhenius 模型加速系数 
EA    活化能 EA=0.45eV 
K      波茨曼常数（k=8.617*10‐5eV/K） 
Ttest  测试温度（℃），一般是 Tmax 
TFeld,i    依据使用曲线的温度集区域温度（℃） 
‐273.15℃    绝对温度 0 度 
高温耐久测试的总测试时长是通过下面的加速系数得出： 
 
其中： 
tPruf      高温耐久测试的测试时间（小时） 
tBetrieb      区域运行时间（小时） 
Pi      运行时间百分比，元件在区域以温度 TFeld,i 运行 
AT,i      温度 TFeld,i 加速系数 
9.5.2 用于高温下性能降低的元件 Arrhenius 模型 
Top,max以内高温下性能降低的元件的高温耐久测试时间计算的温度集依据设计任务书中的使
用特征，在温度范围 T≤Top,max 和 T＞Top,max 进行分配： 
 
表 100：T≤Top,max 测试温度 Top,max 的温度集 
 
 
表 101：Top,max＜T≤Tmax 测试温度 Tmax 的温度集 


### 第 97 页
 
每个温度 TFeld,1…TFeld,m…TFeld,n 使用等式（1）的加速因数 AT,1…AT,m…AT,n 进行计算，其中测试温
度范围 T≤Top,max:Ttest=Top,max，温度范围 T＞Top,max 的，测试温度为 Ttest=Tmax 
 
测试温度为 Top,max 的测试时间 top,max 依据等式（2），i=1…m 
 
测试温度为 Tmax 的测试时间 tmax 依据等式（2），i=m+1…n 
 
总测试时间 tGes 是 top,max 和 tmax 的总和。 
 
接近实际的测试是按照间歇性的测试温度为 Top,max 或者 Tmax（见图 43） 
零件测试时间 top,max 与 tmax 间隔为 48h，按照比例进行分配。 
 
9.5.3 冷却液管路上零件应用的 Arrhenius 模型 
与冷却液管路连接的元件：必须考虑环境和冷却液相关温度分配的所有相关运行模式 i(见图
22，i 代表运行模式编号)。 
每个相关运行模式 i 的高温耐久测试：环境和冷却液测试时间和测试温度都要计算，计算公
式如下。总测试时间等于各自相关运行模式 i 的测试时间的总和。 
 
每个相关运行模式 i 的测试时间计算时首先计算环境温度的测试时间，然后计算冷却液温度
的测试时间，计算方式按照 9.5.1 甚至 9.5.2 的 Arrhenius 模型。 
得出的测试时间 tprüf,umgebung 和 tprüf,KKL 一般是不一样的，每个运行模式 i 的元件测试时间都要
统一。环境温度和冷却液管路之间的测试时间也要一致。 
 
通过下面的迭代法可以将测试时间 tprüf,umgebung 和 tprüf,KKL 延长，将测试分成 2 部分，除一段测
试外，其他段测试温度都要降低。 
 
案例 A: tprüf,umgebung＜tprüf,KKL 
 
测试时间： 
运行模式 i 的测试时间计算公式： 
 
冷却液测试温度： 
按照附录 9.5.1Arrhenius 模型选择测试温度（一般为 Tkühl,max） 
环境温度的测试温度： 
测试温度按照下列演算法（在环境温度集基础上）进行计算，（见表 107）： 
 
表 101：环境温度集 


### 第 98 页
 
1. 迭代开始（m=0）: 
第一个测试部分测试温度需为 TFeld,n,分段测试时间 tprüf,T_Feld,n=tBetrieb*pn(其中运行时间
tBetrieb 单位为 h) 
 
2. 首次迭代（m=1）: 
通过第 1 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，因此通过更多测试可
以得出剩下的测试时间： 
 
通过第 1 段测试还可以得出环境温度分配百分比 pn.Pn 用于后面的计算时必须调整为
pn=0. 
为确定第二段测试的测试温度（m=1）,首先按照附录 C.1 或 C.3Arrhenius 模型计算
Tangepasst(调整后)的测试温度，这样可以得出环境温度分配（pn=0 调整后的）剩下的测试时间
tRest,1. 
当计算得出的调整后的测试温度 Tangepasst(调整后)≤TFeld,n‐1 时，则第 2 段测试要在测试温度
TFeld,n‐1 进行，测试时间公式： 
 
至少要再进行一次迭代步骤。 
如果计算得出的调整后的测试温度 Tangepasst(调整后)＞TFeld,n‐1 时，则第 2 段测试要在测试温度
Tangepasst(调整后)进行，测试时间公式： 
 
这就不用再进行迭代步骤了。 
 
3. 继续迭代（m=2,3...） 
4. 通过首次 m 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，因此通过更多测试
可以得出剩下的测试时间： 
 
通过首批 m 段测试还可以得出环境温度分配百分比 pn‐k,k=0,1,...(m‐1).Pn‐k 用于后面的计
算时必须调整为 pn‐k=0. 
为确定(m+1)段测试的测试温度,首先按照附录 C.1 或 C.3Arrhenius 模型计算 Tangepasst(调整后)
的测试温度，这样可以得出环境温度分配（pn‐k=0 调整后的）剩下的测试时间 tRest,m. 
当计算得出的调整后的测试温度 Tangepasst(调整后),≤TFeld,n‐m 时，则第 m+1 段测试要在测试温
度 TFeld,n‐m 进行，测试时间公式： 


### 第 99 页
 
至少要再进行一次迭代步骤。 
如果计算得出的调整后的测试温度 Tangepasst(调整后)＞TFeld,n‐m 时，则第 m+1 段测试要在测试
温度 Tangepasst(调整后)进行，测试时间公式： 
 
这就不用再进行迭代步骤了(迭代结束)。 
 
案例 B: tprüf,umgebung＞tprüf,KKL 
测试时间： 
运行模式 i 的测试时间计算公式： 
 
 
环境温度的测试温度： 
按照附录 C.1 或 C.3Arrhenius 模型选择测试温度（一般为 Tmax 或 Tmax 和 Top,max） 
 
冷却液的测试温度： 
测试温度按照下列演算法（在环境温度集基础上）进行计算，（见表 102）： 
 
表 102：冷却液温度集 
 
1. 迭代开始（m=0）: 
第一个测试部分测试温度需为 TFeld,n,分段测试时间 tprüf,T_Feld,n=tBetrieb*pn(其中运行时间
tBetrieb 单位为 h) 
 
2. 首次迭代（m=1）: 
通过第 1 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，因此通过更多测试可
以得出剩下的测试时间： 
 
通过第 1 段测试还可以得出冷却液温度分配百分比 pn.Pn 用于后面的计算时必须调整为
pn=0. 
为确定第二段测试的测试温度（m=1）,首先按照附录 C.1 Arrhenius 模型计算 Tangepasst(调整
后)的测试温度，这样可以得出冷却液温度分配（pn=0 调整后的）剩下的测试时间 tRest,1. 


### 第 100 页
当计算得出的调整后的测试温度 Tangepasst(调整后)≤TFeld,n‐1 时，则第 2 段测试要在测试温度
TFeld,n‐1 进行，测试时间公式： 
 
至少要再进行一次迭代步骤。 
如果计算得出的调整后的测试温度 Tangepasst(调整后)＞TFeld,n‐1 时，则第 2 段测试要在测试温度
Tangepasst(调整后)进行，测试时间公式： 
 
这就不用再进行迭代步骤了（迭代结束）。 
 
3. 继续迭代（m=2,3...） 
4. 通过首次 m 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，因此通过更多测试
可以得出剩下的测试时间： 
 
通过首批 m 段测试还可以得出冷却液温度分配百分比 pn‐k,k=0,1,...(m‐1).Pn‐k 用于后面的
计算时必须调整为 pn‐k=0. 
为确定(m+1)段测试的测试温度,首先按照附录 C.1Arrhenius 模型计算 Tangepasst(调整后)的测试
温度，这样可以得出冷却液温度分配（pn‐k=0 调整后的）剩下的测试时间 tRest,m. 
当计算得出的调整后的测试温度 Tangepasst(调整后),≤TFeld,n‐m 时，则第 m+1 段测试要在测试温
度 TFeld,n‐m 进行，测试时间公式： 
 
 
至少要再进行一次迭代步骤。 
如果计算得出的调整后的测试温度 Tangepasst(调整后)＞TFeld,n‐m 时，则第 m+1 段测试要在测试
温度 Tangepasst(调整后)进行，测试时间公式： 
 
这就不用再进行迭代步骤了(迭代结束)。 
 
9.6 温度变化耐久测试计算模型 
9.6.1 Coffin‐Manson‐模型 
计算温度变化的耐久测试时间需要给出元件在△TFeld 区域（参见表 96）的平均温度变化以及
区域的温度耐久测试的循环次数 NTempZyklenFeld。 
 
每天 2 次温度变化的温度循环次数可以从下列公式推断出： 
NTempZyklenFeld=2*365*10（年）=7300 次循环 
与区域的平均温度变化有关，Coffin‐Manson‐模型的加速因数计算如下： 


### 第 101 页
 
其中： 
ACM                        Coffin‐Manson‐模型的加速因数 
△TTest                    一个测试循环期间的温度差（△TTest=Tmax‐Tmin） 
△TFeld                    区域耐久测试期间的平均温度差 
C                            Coffin‐Manson‐模型的参数 
                              在此标准中，规定 c 为固定值 2.5 
 
测试循环的总次数计算如下： 
 
其中： 
NPruf                  要求的循环次数 
NTempZyklenFeld      区域耐久测试期间的温度循环次数 
ACM                    Coffin‐Manson‐模型的加速因数，见等式（3） 
表 103：安装位置温度集和温升 
零部件安装位置 
温度集编号 
温升 K 
无特殊要求的内部 
1 
36 
无特殊要求的 hang‐on part 
1 
36 
暴露于日照的内饰 
2 
46 
天窗零部件 
2 
46 
发动机舱，但不在发动机上 
3 
60 
散热器上 
3 
60 
安装在发动机上的零部件 
4 
75 
安装在变速箱上的零部件 
4 
75 
 
9.6.2 冷却液循环系统元件的 Coffin‐Manson‐模型应用 
与冷却液管路连接的元件：必须考虑环境和冷却液相关温度分配的所有相关运行模式 i(见图
55，i 代表运行模式编号)。 
每个相关运行模式 i 的高温耐久测试：环境和冷却液最高温度和测试循环次数都要计算，计
算公式如下。总测试循环次数等于各自相关运行模式 i 的测试循环次数的总和。 
 
每个相关运行模式 i 的测试循环次数计算时首先计算环境温度的测试循环次数，然后计算冷
却液温度的测试循环次数，计算方式按照 Coffin‐Manson 模型。 
得出的测试循环次数 Nprüf,umgebung 和 Nprüf,KKL 一般是不一样的，每个运行模式 i 的元件测试循
环次数都要统一。环境温度和冷却液管路之间的测试循环次数也要一致。 
 
通过下面的迭代法可以将测试循环次数 Nprüf,umgebung 和 Nprüf,KKL 延长，将测试分成 3 部分，其
中一段测试温度在 Tmax 和 Tmin 之间进行，其他段测试温度冲程都要降低，在 Tmin 和 TRT 之间
甚至在 TRT 和 Tmax 之间进行。 
 


### 第 102 页
案例 A: Nprüf,umgebung＞Nprüf,KKL 
测试循环次数： 
运行模式 i 下的测试循环次数 Nprüf,Mode i=Nprüf,umgebung 
 
冷却液测试循环次数： 
冷却液的测试循环次数 Nprüf,KKL 必须调整到和环境测试循环次数 Nprüf,umgebung（比冷却液次数
多）一致，然后按照下面三个温度范围进行测试循环： 
 
1. XKKL 测试循环必须在 TKKL,min 和 TKKL,max 之间进行。根据 Coffin‐Manson 模型计算加速因数
ACM,KKL,1:△TTest,1=TKKL，max‐TKKL,min 
2. 1/2*(Nprüf,Mode i‐XKKL)测试循环必须在 TKKL,min 和 TRT 之间进行。 
根据 Coffin‐Manson 模型计算加速因数 ACM,KKL,2:△TTest,2=TRT‐TKKL,min 
3. 1/2*(Nprüf,Mode i‐XKKL)测试循环必须在 TRT 和 TKKL,max 之间进行。 
根据 Coffin‐Manson 模型计算加速因数 ACM,KKL,3:△TTest,3=TKKL,max‐TRT 
 
1 至 3 的总和即为温度循环总次数 Nprüf,Mode i。 
 
根据附录 D.1 中的等式 4 可以得出： 
 
测试循环次数 XKKL 计算如下： 
 
将 XKKL 值代入上述 1‐3 处可以计算出三段测试的循环次数。 
 
如果 TKKL,op,max＜TKKL,max,或者 TKKL,op,min＞TKKL,min 或者 TUmgebung,op,max＜TUmgebung,max ，或者
TUmgebung,op,min＞TUmgebung,min,则还需考虑相应温度（章节 16.3.2.1 中图 45）增加停留时间。 
测试过程中环境温度和冷却液管路的温度变化循环测试同步进行。 
 
案例 B: Nprüf,umgebung＜Nprüf,KKL 
测试循环次数： 
运行模式 i 下的测试循环次数 Nprüf,Mode i=Nprüf,KKL 
 
环境测试循环次数： 
环境的测试循环次数 Nprüf,Umgebung 必须调整到和冷却液测试循环次数 Nprüf,KKL（比环境测试次
数多）一致，然后按照下面三个温度范围进行测试循环： 
 
1. XUmgebung 测试循环必须在 TUmgebung,min 和 TUmgebung,max 之间进行。根据 Coffin‐Manson 模型
计算加速因数 ACM,Umgebung,1:△TTest,1=T Umgebung，max‐T Umgebung,min 


### 第 103 页
2. 1/2*(Nprüf,Mode i‐X Umgebung)测试循环必须在 T Umgebung,min 和 TRT 之间进行。 
根据 Coffin‐Manson 模型计算加速因数 ACM, Umgebung,2:△TTest,2=TRT‐T Umgebung,min 
3. 1/2*(Nprüf,Mode i‐X Umgebung)测试循环必须在 TRT 和 T Umgebung,max 之间进行。 
根据 Coffin‐Manson 模型计算加速因数 ACM, Umgebung,3:△TTest,3=T Umgebung max‐TRT 
 
1 至 3 的总和即为温度循环总次数 Nprüf,Mode i。 
 
根据附录 D.1 中的等式 4 可以得出： 
 
Xumgebung 测试循环次数计算如下： 
 
将 XKKL 值代入上述 1‐3 处可以计算出三段测试的循环次数。 
 
如果 T Umgebung op,max＜T Umgebung,max,或者 T Umgebung,op,min＞T Umgebung,min 或者 TKKL,op,max＜TKKL,max，或
者 TKKL,op,min＞TKKL,min,则还需考虑相应温度（7.3.2 中图 46）增加停留时间。 
测试过程中环境温度和冷却液管路的温度变化循环测试同步进行。 
 
9.7 运行模式 
纯粹靠燃烧发动驱动的汽车，其使用寿命期间内的运行状态一般分为两种运行模式： 
� 
驾驶状态 
� 
停车状态 
 
其他驱动的汽车也可以考虑其他运行模式（见表 104）。 
 
与多种运行模式相关的元件（图 55），如有必要，每种运行模式的运行方式需一一进行确定。 
 
表 104：运行模式基本要求 
运行模式 
停车 
插上充电线 
给驱动电池充电
启动电力线通信
（如有） 
驾驶中 
否 
否 
是/否 
否 
充电状态 
是 
是 
是 
是 
预处理 
是 
是/否 
是/否 
是/否 
入网停车 
是 
是 
否 
是 
离网停车或停车 
是 
否 
否 
否 
 
测试中需考虑（图 55 中）元件相关的所有运行模式。 


### 第 104 页
 
图 55：不同运行模式的载荷谱分类 
 

