# Q-FPT_2800001_EN_2015-01_整车电子电器零部件一般技术标准-环境耐久.pdf

## 文档信息
- 标题：标准名称
- 作者：CNIS
- 页数：121

## 文档内容
### 第 1 页
 
 
Q/FPT 
BEIQI FOTON MOTOR CO.,LTD ENTERPRISE STANDARD 
Q/FPT 2800001—2015 
REPLACE Q/ FPT 2800001-2011 
 
 
General Specification for 
Electrical/Electronic Components 
–Environmental/Durability 
整车电子电器零部件一般技术标准-环境/耐久 
 
 
（FINISHED DATE：2015-01-05） 
2015 – 01 – 05 Issue 
2015 – 01 –05 Implement 
BEIQI FOTON MOTOR CO.,LTD   I S S U E  


### 第 2 页
GB/T 2800001—2015 
 
INDEX 
FOREWORD ........................................................................... III 
1 Scope .............................................................................. 1 
2 References ......................................................................... 1 
3 General ............................................................................ 2 
3.1 Terms and Definitions .......................................................... 2 
3.2 Operation Voltage Ranges ....................................................... 6 
3.3 Performance Verification ....................................................... 6 
4 Electrical Requirements And Tests ................................................. 10 
4.1 E1 Parasitic Current .......................................................... 13 
4.2 E2 Power Supply Interruption .................................................. 13 
4.3 E3 Power Battery Voltage Change ............................................... 14 
4.4 E4 Sinusoidal Superimposed Voltage ............................................ 15 
4.5 E5 Pulse Superimposed Voltage ................................................. 16 
4.6 E6 Short Circuit In signal Circuit And Load Circuits .......................... 18 
4.7 E7 Open Circuit – Single Line Interruption .................................... 19 
4.8 E8 Open Circuit – Multiple Line Interruption .................................. 21 
4.9 E9 Ground Offset .............................................................. 21 
4.10 E10 Discrete Digital Input Threshold Voltage ................................. 22 
4.11 E11 Over Load – All Circuits ................................................. 25 
4.12 E12 Isolation Resistance ..................................................... 25 
4.13 E13 Over Voltage ............................................................. 26 
4.14 E14 Transient Over Voltage ................................................... 27 
4.15 E15 Transient Under Voltage .................................................. 28 
4.16 E16 Jump Start ............................................................... 29 
4.17 E17 Load Dump ................................................................ 31 
4.18 E18 Short Interruptions ...................................................... 32 
4.19 E19 Start Pulse .............................................................. 33 
4.20 E20 Voltage Curve With Intelligent Generator Control ......................... 36 
4.21 E21 Reverse Polarity ......................................................... 38 
4.22 E22 Dielectric Strength ...................................................... 40 
4.23 E23 Back Feeds ............................................................... 41 
4.24 E24 Nominal And Worst Case Performance Analysis .............................. 42 
4.25 E25 Short/Open Circuit Analysis .............................................. 42 
5 Mechanical Requirements And Tests ................................................. 43 
5.1 Vibration Test ................................................................ 43 
5.2 M2 Mechanical Shock ........................................................... 52 
5.3 M3 Endurance Shock Test ....................................................... 53 
5.4 M4 Crush For Housing-Elbow Load ............................................... 54 
5.5 M5 Crush For Housing-Foot Load ................................................ 55 
5.6 M6 Free Fall .................................................................. 56 
5.7 M7 Resonant Frequency Analysis ................................................ 56 


### 第 3 页
GB/T 2800001—2015 
 
5.8 M8 High Altitude Shipping Pressure Effect Analysis ............................ 57 
5.9 M9 Plastic Snap Fit Fastener Analysis ......................................... 57 
5.10 M10 Crush Analysis ........................................................... 58 
5.11 M11 Vibration Noise(Squeak & Rattle) ......................................... 58 
5.12 M12 Connector Test ........................................................... 58 
6 Climatic Requirements And Tests ................................................... 59 
6.1 C1 High-/low-temperature Storage .............................................. 59 
6.2 C2 Low-temperature Operation .................................................. 59 
6.3 C3 Incremental Temperature Test ............................................... 60 
6.4 C4 Repainting Temperature ..................................................... 61 
6.5 C5 Temperature Shock (component、Without Housing) ............................. 61 
6.6 C6 Temperature Shock With Splash Water ........................................ 62 
6.7 C7 Temperature Shock- immersion ............................................... 65 
6.8 C8 Humid Heat, Cyclic ......................................................... 66 
6.9 C9 Humid Heat,Constant ........................................................ 67 
6.10 C10 Salt Spray Test With Operation, Interior ................................. 68 
6.11 C11 Salt Spray Test With Operation, Exterior ................................. 69 
6.12 C12 Water Protection ......................................................... 70 
6.13 C13 Water Freeze ............................................................. 71 
6.14 C14 Condensation Test With Electr. Assemblies ................................ 71 
6.15 C15 Dust ..................................................................... 73 
6.16 C16 Sun Radiation ............................................................ 74 
6.17 C17 Chemical Requirements And Tests .......................................... 74 
6.18 C18 Harmful Gas Test ......................................................... 76 
6.19 C19 High Altitude Operation Overheating Analysis ............................. 77 
6.20 C20 Thermal Fatigue Analysis ................................................. 78 
6.21 C21 Lead-Free Solder Analysis ................................................ 78 
7 Service-life tests ................................................................ 79 
7.1 L-01 Life test - mechanical/hydraulic endurance test .......................... 79 
7.2 L-02 Life test – high-temperature endurance test .............................. 80 
7.3 L-03 Life test - temperature cycle test ....................................... 83 
8 Analysis .......................................................................... 86 
8.1 Analysis Mission .............................................................. 86 
8.2 Development Mission ........................................................... 86 
8.3 Design Validation (DV) Mission ................................................ 86 
8.4 Product Validation (PV) ....................................................... 86 
9 Appendix .......................................................................... 88 
9.1 Installation Area ............................................................. 88 
9.2 Process ....................................................................... 94 
9.3 Summary of A/D/V Activities ................................................... 99 
9.4 Test Sequence Plan ........................................................... 102 
9.5 Computation models for the life test "High-temperature endurance test"........ 108 
9.6 Computation models for the "temperature cycle test" life test ................ 113 


### 第 4 页
GB/T 2800001—2015 
 
9.7 Operating situations ......................................................... 116 
FORE  WORD 
 
The standard is edited according as national and industry standard. 
The standard is advanced by engineering research institute of BEIQI foton motor CO.,LTD. 
The standard is draft out by: engineering research institute electrical & electrics Dept. 
for passenger vehicle of BEIQI foton motor CO.,LTD. 
The standard is draft out by: Wang Qingping;Zhu Chuangang


### 第 5 页
GB/T 2800001—2015 
1 
1 Scope 
This standard applies to Electrical/Electronic (E/E)components for passenger vehicles and 
specifies requirements, test conditions and tests for electric, electronic and mechatronic 
components and systems for the use in motor vehicles.Additional or deviating requirements, 
test conditions and tests must be defined in the respective Component Technical Specifications. 
Note: The represented tests do not serve for component qualification or a qualification of 
the manufacturing process. 
Note: Nothing in this standard supersedes applicable laws and regulations. 
Note: In the event of a conflict between the Chinese and the domestic language, the Chinese 
language shall take precedence. 
2 References 
Only the latest approved standards are applicable unless otherwise specified 
ASTM D4728      Standard Test Method for Random Vibration Testing of Shipping Containers 
IEC 60068-2-1   Environmental testing-Part 2-1:Tests – Test A: Cold 
IEC 60068-2-2   Environmental testing – Part 2-2: Tests – Test B: Dry heat 
IEC 60068-2-11  Environmental testing procedures. Part 2-11 : Tests-Test Ka: Salt mist 
IEC 60068-2-14  Environmental testing-Part 2-14 Tests-Test N Change of temperature 
IEC 60068-2-27  Environmental testing – Part 2-27: Tests – Test Ea and guidance: Shock 
IEC 60068-2-29  Environmental testing. Part 2-29: Tests. Test Eb and guidance: Bump 
IEC 60068-2-30  Environmental testing - Part 2-30: Tests - Test Db: Damp heat, cyclic  
IEC 60068-2-38  Environmental testing - Part 2-38: Tests - Test Z/AD: Composite 
temperature/humidity cyclic test 
IEC 60068-2-52  Environmental Testing - Part 2-52: Tests - Test Kb: Salt Mist, Cyclic  
IEC 60068-2-60  Environmental testing - Part 2-60: Tests - Test Ke: Flowing mixed gas 
corrosion test 
IEC 60068-2-64  Environmental testing - Part 2-64: Tests- Test L: Dust and sand 
IEC 60068-2-78  Environmental testing – Part 2-78: Tests-Test Cab:Damp heat,steady state 
ISO 11124-2     Preparation of steel substrates before application of paints and related 
products - Specifications for metallic blast-cleaning abrasives - Part 2: 
Chilled-iron grit 
ISO 12103-1     Road Vehicles - Test Dust for Filter Evaluation 
ISO 16750-1     Road vehicles -- Environmental conditions and testing for electrical 
and electronic equipment -- Part 1: General 
ISO 16750-2     Road vehicles -- Environmental conditions and testing for electrical 
and electronic equipment -- Part 2: Electrical loads 
ISO 16750-3     Road vehicles - Environmental conditions and testing for electrical and 
electronic equipment - Part 3 Mechanical loads 
ISO 16750-4     Road vehicles -- Environmental conditions and testing for electrical 


### 第 6 页
GB/T 2800001—2015 
2 
and electronic equipment -- Part 4: Climatic loads 
ISO 20567-1    Paints and varnishes -- Determination of stone-chip resistance of 
coatings -- Part 1: Multi-impact testing 
ISO 20653      Road vehicles -- Degrees of protection (IP-Code) -- Protection of 
electrical equipment against foreign objects, water and access 
ISO 6270-2      Paints and varnishes -- Determination of resistance to humidity –    
Part 2: Procedure for exposing test specimens in condensation-water 
atmospheres 
ISO 8820        Standard ATO/ATC Blade Fuses 
DIN 72552       Automobile electric terminal numbers 
DIN EN ISO 17025  General requirements for the competence of testing and calibration 
laboratories 
UL94            Test for Flammability of Plastic Materials for Parts in Devices and 
Appliances 
3 General 
3.1 Terms and Definitions 
3.1.1 Voltage and Temperature Definitions 
Table 1 Abbreviations for voltages and currents 
UN 
Nominal supply voltage with generator not operating (i.e.,battery 
voltage) at which the component is operated during the test. 
UBmin 
Lower operating voltage limit 
UB 
Nominal supply voltage with generator operating (i.e.,alternator 
voltage) at which the component is operated during the test. 
UBmax 
Upper operating voltage limit 
Umax 
Maximum voltage that may occur during a test 
Umin 
Minimum voltage that may occur during a test 
UPP 
Peak-peak voltage 
Ueff 
RMS value of a voltage 
Utest 
Test voltage 
IN 
Nominal current 
GND 
Device ground 
Tmin 
Minimum operate Temperature 
Tmax 
Maximum operate Temperature 
TPH 
Post Heating Temperature, Maximum limit value of the ambient 
temperature which may temporarily occur after vehicle cut-off and at 
which the component may be operated for a brief period, i.e., on the 


### 第 7 页
GB/T 2800001—2015 
3 
engine and in its environment. 
TRPS 
Repaint & Storage Temperature, Maximum temperature which can occur 
during repainting, but at which the component is not operated. 
Accounts for high temperature storage and paint booth exposure. 
Troom 
Room Temperature 
Ttest 
Test Temperature 
Top,min 
Minimum operating temperature for components with overload 
protection/low-temperature protection 
Top,max 
Maximum operating temperature for components with overload 
protection/over-temperature protection 
Tmin_S 
Minimum storage temperature 
Tmax_S 
Maximum storage temperature 
Tcool,min 
Minimum coolant temperature of the coolant circuit 
Tcool,max 
Maximum coolant temperature of the coolant circuit 
3.1.2 Time and Duration 
   Table 2 Abbreviations for Time and Durations 
tr  
Rise time (e.g. of a voltage curve) 
tf  
Fall time (e.g. of a voltage curve) 
tTest 
Test duration 
All edge descriptions refer to the 10% or 90% voltage values. 
3.1.3 Others 
                       Table 3 Other definition 
Ri 
Internal resistance of the voltage source 
Terminal designations 
DIN 72552  
f 
Frequency 
 
3.1.4 Parameter Tolerances 
Unless stated otherwise, the following shall define the test environment parameters and 
tolerances to be used for all validation testing (see Table 4): 
Table 4: Parameters and Tolerances 
Parameter 
Tolerances 
Maximum Temperature 
Spec.(2℃,0) 
Minimum Temperature 
Spec.(0,-2℃) 
Room Temperature 
(+23 ± 5) ℃ 
Test Time 
Spec.± 0.5 % 
Room Ambient Relative Humidity 
(30...70) % 
Chamber Humidity 
Spec. ± 5 % 
Maximum Voltage 
Spec. ( +4 %,0%) 
Minimum Voltage 
Spec. ( 0%,-4%) 


### 第 8 页
GB/T 2800001—2015 
4 
Current 
Spec. ± 2 % 
Resistance 
Spec. ± 10 % 
Random Acceleration (GRMS) 
± 20 %  
Acceleration 
Spec. ± 20 % 
Frequency 
± 1 % 
Force Spec. 
± 10 % 
Distance 
Spec. ± 5 % 
Remarks: 
Tolerance value is only used with nominal value. The set nominal value could not be adjusted 
within tolerance without formal approval from FOTON Passenger Car Validation Engineer. 
3.1.5 Standard Value 
Unless stated otherwise, the following shall define parameters to be used for all 
validation testing (see Table 5): 
Table 5: Parameters definitions 
Room temperature 
Troom = 23°C ±5°C 
Humidity 
45% - 75%  
Test temperature 
Ttest = Troom 
Nominal voltage 
UN = 12 V 
Operating voltage(for test) 
UB = 14 V 
3.1.6 Functional Status Classification (FSC) 
FSC defines the functional performance of the component. The functional status of the DUT 
is to be specified for each test. Additional requirements must be defined and documented in 
the Component Technical Specifications. 
Memory functions must always remain in functional status A. The integrity (not 
up-to-dateness) of the non-volatile memories must be ensured at any time. The time sequences 
of the functional statuses must be specified in the Component Technical Specifications. 
Permissible event log entries must be coordinated with the purchaser and must be 
stipulated. 


### 第 9 页
GB/T 2800001—2015 
5 
Table 6: Class Definition  
Class Definition of FSC Class 
A 
All functions of the component perform as designed during and after the test. 
B 
All functions of the component perform as designed during the test. However, 
one or more of them may go beyond the specified tolerance. All functions return 
automatically to within normal limits after the test. Memory functions shall 
remain FSC A. 
FSC A is also acceptable for components that are classified as FSC B. 
C 
One or more functions of the component do not perform as designed during the 
test but return automatically to normal operation after the test. 
FSC A or B are also acceptable for components that are classified as FSC C. 
D 
One or more functions of the component do not perform as designed during the 
test and do not return to normal operation after the test until the component 
is reset by any “operator/use” action. 
FSC A, B, or C are also acceptable for components that are classified as FSC 
D. 
E 
One or more functions of the component do not perform as designed during and 
after the test and cannot be returned to proper operation without repairing 
or replacing the component. 
FSC A, B, C, or D are also acceptable for components that are classified as 
FSC E. 
3.1.7 Operating Types 
Table 7: Operating Type Electrical State 
Operating Types 
 Electrical State 
1 
Component not electrically connected 
 
1.1 
The Component is not electrically connected, without plug 
and harness 
 
1.2 
The Component is not electrically connected, but with 
connected plugs and harness 
2 
The component is electrically operated with supply voltage UN . 
 
2.1 
Component functions are not activated (e.g., sleep mode, 
OFF mode) 
 
2.2 
The Component must be operated with minimal operating 
load. 
 
2.3 
The Component must be operated with maximum load  
3 
The component is electrically operated with supply voltage UB  
(engine/generator active) with all electrical connections made. 
 
3.1 
Component functions are not activated (e.g., sleep mode, 
OFF mode). 
 
3.2 
Components with electric operation and control in typical 
operating type. 


### 第 10 页
GB/T 2800001—2015 
6 
 
3.2 Operation Voltage Ranges 
Table 8: Operating voltage ranges 
Code Letter 
UBmin  
UBmax 
Description 
A 
 
 
Reserved 
B 
6 
16V 
For functions that must retain their performance 
during starting of the engine 
C 
8V 
16V 
For functions that do not have to retain their 
performance during starting of the engine. 
Note: This encoding must only be used if the 
component cannot be classified in the encoding a, 
b or d,e. 
D 
(most common) 
9 
16V 
For functions that must retain their performance 
when the engine is not running 
E 
10V 
16V 
For functions that must retain their performance 
when the engine is running 
Z 
As agree upon 
Note: In the range of the given code letter, the Functional Status Classification shall 
be A. 
Note: In the range of (-13.5 V to Umin) and (Umax to +26 V), the Functional Status 
Classification shall be C. 
3.3 Performance Verification 
The supplier is responsible for developing 5-Point Functional/Parametric Check, 1-Point 
Functional/ Parametric Check, Continuous Monitoring, and Functional Cycling for the component. 
These procedures shall be defined in the Component Environmental Test Plan. 
3.3.1 F-1 1-Point Functional/Parametric Test 
Goal：This Test shall verify full functionality of the component as defined in the CTS at a 
single temperature and single voltage. 
Test： 
Table 9:F-1 1-Point Functional/Parametric Test 
Operating Types 
2.3 and 2.1 
Voltage and Temperature Troom and UB 
Application 
The test should be done according to the Component Environmental Test Plan. The 
Plan shall be approved by FOTON Validation Engineer. 
Test Procedure 
The temperature shall be stabilized for at least 0.5 h before the test，The test 
should be done as following 
1 Validate functionality by monitoring and recording that all outputs (both 
hardwired and on-vehicle data bus communications), or a subset thereof as defined 
in the Component Environmental Test Plan, are in the correct state for a given 


### 第 11 页
GB/T 2800001—2015 
7 
set of inputs and timing conditions. 
2 Measure parametric values by monitoring and recording the specific voltage, 
current, and timing levels for all inputs and outputs, or a subset thereof as 
defined in the Component Environmental Test Plan, in order to verify that these 
levels meet the CTS/SSTS requirements, including tolerances. 
3 Measure non-electrical parameters such as LED brightness, motor torque, etc. 
by monitoring and recording the appropriate specific values in order to verify 
that these levels meet the 
CTS/SSTS requirements, including tolerances. These parameters shall be defined 
in the Component Environmental Test Plan. 
 
Requirement：  
The basic functionalities of the components must be measured and documented in the test 
report. 
The power supply shall be capable of supplying sufficient current to avoid current limiting 
under high in-rush conditions.Function Class shall be class A during and after test. 
3.3.2 F-2 5-Point Functional/Parametric Test 
Goal：This Test shall verify full functionality of the component as defined in the CTS at a 
single temperature and single voltage according to table 10.  
Test： 
Table 10:F-2 5-Point Functional/Parametric Test 
Operating Types 
2.3 and 2.1 
Voltage and Temperature (Tmin, UBmin),(Tmin, UBmax), (Troom, UB), (Tmax, UBmin), (Tmax, UBmax) 
Application 
All components at the beginning and at the end of all test groups 
Test Procedure 
The temperature shall be stabilized for at least 0.5 h (after temperature 
reaching set point) before the test，The test should be done as following 
1、Validate functionality by monitoring and recording that all outputs (both 
hardwired and on-vehicle data bus communications), or a subset thereof as 
defined in the Component Environmental Test Plan, are in the correct state 
for a given set of inputs and timing conditions. 
2、Measure parametric values by monitoring and recording the specific voltage, 
current, and timing levels for all inputs and outputs, or a subset thereof 
as defined in the Component Environmental Test Plan, in order to verify that 
these levels meet the CTS/SSTS requirements, including tolerances. 
3、Measure non-electrical parameters such as LED brightness, motor torque, 
etc. by monitoring and recording the appropriate specific values in order to 
verify that these levels meet the CTS/SSTS requirements, including 
tolerances. These parameters shall be defined in the Component Environmental 
Test Plan. 
Requirement: 


### 第 12 页
GB/T 2800001—2015 
8 
The basic functionalities of the components must be measured and documented in the test 
report.Function Class shall be class A during and after test. 
3.3.3 F-3 Continuous Parameter Monitoring  
Goal: Continuous Monitoring shall detect the functional status of the component during and 
after exposure to the test environment. Continuous Monitoring shall detect false actuation 
signals, erroneous serial data messages, fault codes, or other erroneous I/O commands or states. 
This shall be documented in detail in the Component Environmental Test Plan. 
Test: 
Table 11 :F-3 Continuous parameter monitoring with drift analysis 
Operating Types 
As defined in each individual test 
Voltage and 
Temperature 
As defined in each individual test 
Application 
This check shall be performed during the tests as defined in 
the Component Environmental Test Plan 
Test Procedure 
1 Detect functionality by monitoring and recording that all 
outputs (both hardwired and on-vehicle data bus 
communications), or a subset thereof as defined in the Component 
Environmental Test Plan, are in the correct state for a given 
set of inputs and timing conditions. The monitoring sampling 
rate shall be defined in the Component Environmental Test Plan. 
2 Monitor and record internal diagnostic codes, if available. 
3 Include periodic observation of specific component 
functionality, such as optical monitoring of test patterns. 
Requirement: 
 Continuous Monitoring test setup shall be verified with respect to accurate and 
comprehensive data storage.Function Class shall be class A during and after test. 
3.3.4 F-4 Functional Cycling 
Goal: Functional Cycling shall simulate customer usage during and after exposure to the test 
environment. This shall be documented in detail in the Component Environmental Test Plan. 
Table 12: Functional Cycling 
Applicability 
Functional Cycling shall be performed during the tests as defined in 
the Component Environmental Test Plan. 
Operating Type 
2.1/2.2/3.1/3.2 
Monitoring 
As defined in the following procedure. 
Procedure 
While the component is exposed to the test environment, all component 
inputs/outputs, displays,human-interfaces, and mechanical 
actuations shall be cycled and monitored for proper functional 
operation. This may include power moding if applicable. The 
functional cycling rate shall be defined in the Component 


### 第 13 页
GB/T 2800001—2015 
9 
Environmental Test Plan. 
Functional Cycling shall occur during any test that includes 
Operating Type 3.2. Exceptions shall be included in the Component 
Environmental Test Plan. The functional cycling scheme shall exercise 
the component and allow for detection of degradation or failure. The 
electrical/mechanical loading shall reflect the normal usage in the 
vehicle application. 
The input/output cycling and monitoring shall be automatic. 
 
Requirement: 
     Functional Cycling test setup shall be verified with respect to accurate and comprehensive 
component state changes. 
3.3.5 F-5 Physical Analysis 
Goal:  
This activity shall identify any structural faults, material/component degradations or 
residues, and near-to-failure conditions caused by environmental testing.  
Test: 
Physical analysis should be performed on all samples. All samples shall be available for 
FOTON review upon request. The following samples shall be submitted to FOTON Engineering upon 
request for physical analysis: one sample from Mechanical Fatigue, two samples from Thermal 
Fatigue (one from Constant Humidity and one from Cyclic Humidity), and one sample from Corrosion. 
Perform an external inspection of the component housing. Then perform a dissection of the 
component followed by an internal inspection. The inspection shall use visual aids (e.g., 
magnifiers, microscopes, dyes, etc.) as necessary. 
The following are examples of items the inspection shall examine for: 
- Mechanical and Structural Integrity:  
Signs of degradation, cracks, melting, wear, fastener failures, etc. 
- Solder/Part Lead Fatigue Cracks or Creep or Pad-Lift: 
 Emphasis on large integrated circuits, large massive parts or connector terminations 
(Especially at the end or corner lead pins). Also, parts in high flexure areas of the circuit 
board. 
- Damaged Surface Mount Parts:  
Emphasis on surface mounted parts near circuit board edges, supports or carrier tabs. Also, 
surface mounted parts located in high flexure areas of the circuit board and near connector 
terminations. 
- Large Part Integrity and Attachment: 
 Leaky electrolytic capacitors, contaminated relays, heat, sink/rail attachments, etc. 
- Material Degradation, Growth, or Residues of Corrosion:  
Melted plastic parts; degraded conformal coatings, solder masks or seals; circuit board 
delimitations, lifted circuit board traces, corrosion such as black silver sulfide spots, 
organic growths, or environmental residues due to dust, salt, moisture, etc. All foreign 
residues shall be analyzed for material composition and conductivity. 


### 第 14 页
GB/T 2800001—2015 
10 
- Other Abnormal or Unexpected Conditions: 
Changes in appearance or smell. Indicators of poor manufacturing processes. Objectionable 
squeak and rattles, especially after vibration fatigue. 
-The Formation of Whiskers When Tin, Zinc, or Silver is used: The Component Environmental 
Test Plan provided in this document will effectively precipitate the formation of whiskers. 
A close examination of the circuit boards with a magnifying device shall occur on all components, 
particularly on the components that experienced PTC. The appearance of whiskers during 
environmental testing will indicate the probability of similar whisker formations occurring 
in the field. The formation of whiskers poses a risk to close-pitched parts, and may result 
in a short-circuit situation of parts or components that are stored for service. 
-Absence of Dendritic Growth:  
The circuit board and all parts must be free of signs of dendritic growth. 
- Solder Joint Voids:  
Selective solder joints shall be sectioned to ensure that the formation of voids is kept 
to an acceptable minimum level. Solder joints most at risk include interfaces with large CTE 
differentials or corner pins of surface mounted parts with large diagonal lengths. 
Requirement:  
A summary of each component’s condition shall be documented and reported to the FOTON Design 
Release Engineer or FOTON Component Validation Engineer. The supplier may be required to 
perform further investigation to determine the degree or type of degradation. FOTON Engineering 
will decide as to the necessity of corrective action. 
4 Electrical Requirements And Tests 
Table 13 is Test selection table. 
Table 13: Test selection table 
Test 
Applicable to 
To be additionally defined by 
the purchaser 
E1 Parasitic Current 
All components directly connected to 
the vehicle battery. 
 
E2 Power Supply 
interruption 
All components that may be affected 
by a momentary drop in voltage. This 
includes components supplied by 
regulated voltage provided by other 
components 
 
E3 Power Battery 
Voltage Change 
All components that have power 
supplied by the vehicle battery 12 V 
wiring system 
 
E4 Sinusoidal 
Superimposed Voltage 
All components that have power 
supplied by the vehicle battery 12 V 
wiring system 
 
E5 Pulse Superimposed 
All components that have power 
 


### 第 15 页
GB/T 2800001—2015 
11 
Voltage 
supplied by the vehicle battery 12 V 
wiring system 
E6 Short circuit in 
signal circuit and load 
circuits 
All components 
 
E7 Open Circuit – 
Single Line 
Interruption 
All components 
 
E8 Open Circuit – 
Multiple Line 
Interruption 
All components 
 
E9 Ground Offset 
All I/Os on the component without 
signal return line. This means all 
I/Os on the component where voltage 
offsets occur in the ground line(s). 
Ground offsets will occur between 
different components within a 
vehicle due to voltage losses in the 
ground lines. The resistive nature of 
the wire harness 
(e.g., wire resistance, wire length, 
connector material, etc.) will 
result in different ground offsets at 
each I/O on the component. 
 
E10 Discrete Digital 
Input Threshold 
Voltage 
Discrete digital input and switch 
input interfaces with transmitters 
grounded locally without signal 
return to receiver. It can be 
replaced by Analysis at Hardware 
Design Review 
 
E11 Over Load – All 
Circuits 
All components with or without 
internal over-current protection. 
This also applies to components 
containing fuses, such as Bused 
Electrical Centers, for power 
outputs that are equipped with 
built-in internal overload 
protection 
 
E12 Isolation 
Resistance 
All components that generate 
voltages > 30 V 
 
E13 Over Voltage 
All components in the vehicle that 
have power supplied by the vehicle 
 


### 第 16 页
GB/T 2800001—2015 
12 
battery 12 V wiring system 
E14 Transient over 
voltage 
Components supplied via the 12 V 
electric system 
 
E15 Transient under 
voltage 
Components supplied via the 12 V 
electric system 
 
E16 Jump start 
All components that have power 
supplied by the vehicle battery 12 V 
wiring system 
 
E17 Load dump 
Components supplied via the 12 V 
electric system 
 
E18 short 
interruptions 
All  
components 
 
E19 Start pulse 
Components supplied via the 12 V 
electric system 
 
E20 Voltage curve with 
intelligent generator 
control 
Components supplied via the 12 V 
electric system 
 
E21 Reverse polarity 
 
All components that have power 
supplied by the vehicle battery 12 V 
wiring system. This test is not 
applicable to generators or 
components that have an exemption 
stated in the CTS. 
 
E22 Dielectric 
strength 
Components with inductive parts 
(e.g., motors, relays, coils) 
 
E23 Back feeds 
Components that are electrically 
connected to KL15 or other terminals 
with wake-up function 
 


### 第 17 页
GB/T 2800001—2015 
13 
 
4.1 E1 Parasitic Current 
Goal： 
This test shall verify that the component’s power consumption complies with the 
specification for ignition OFF state. This is to support power management and engine start 
ability following long term storage/parking conditions 
Test： 
Table 14:Test Parameters E1 Parasitic Current  
Operating Types 
2.1 
Test voltage 
12.5 V 
Test case 1 
T 
Tmin 
Test case 2 
T 
Troom 
Test case 3 
T 
Tmax 
Application  
All Components directly connected in Battery 
Test Procedure 
For Component operated after KL15 OFF, a Parasitic 
current consumption according to requirement 
Number of Test Samples 
6 
Requirement: 
Parasitic Current target for any DUT is 0mA in principle. The acceptance criteria shall be 
discussed with FOTON validation engineer, and the default criteria is less than 0.1mA(average 
over 12 h)；corresponding to 1.2mAh (above+40°C < 0.2mA) applies. The current measuring device 
must have a sampling rate that is ten times higher than the shortest current peak duration 
that the component creates. 
4.2 E2 Power Supply Interruption 
Goal: 
This test shall verify the proper reset behavior of the component. It is intended primarily 
for components with a regulated power supply or a voltage regulator. This test shall also be 
used for microprocessor-based components to quantify the robustness of the design to sustain 
short duration low voltage dwells. 
Test: 
Table 15: Test parameters for E2 Power Supply interruption  
Operating Types  
2.3 
Uth  
6 V 
∆U1 (UBmin to 6 V)  
0,5 V 
∆U2 (6 V to 0 V)  
0,2 V 
t0 – Component On  
At least ≥10 s and until the component has returned to 100% 
serviceability (all systems rebooted without error). 


### 第 18 页
GB/T 2800001—2015 
14 
t1 – Test sequence 1  
5 s 
t1 – Test sequence 2  
100 ms 
tr  
≤10ms  
tf  
≤10ms 
Test Procedure 
Apply the test pulse to all supply voltage inputs (Figure 
1) simultaneously. 
Number of cycles  
1 
Number of Test Samples  
3 
 
Figure 1: Power Supply interruption 
Requirement: 
Functional status A should be reached when the voltage returns to UBmin. Undefined operating 
statuses must not occur under any circumstances. It must be verified and documented that the 
specified threshold as of which voltage level the component leaves functional status A for 
the first time is complied with. 
4.3 E3 Power Battery Voltage Change 
Goal: 
This test shall verify the component’s immunity to voltage decrease and increase that 
occurs during discharge and charging of the vehicle battery. 
Test： 


### 第 19 页
GB/T 2800001—2015 
15 
Voltage
Time
UBmax
UBmin
I
II
III
IV
V
VI
VII
0V
Function Status A
Function Status C
Function Status A
 
Figure 2 Battery Voltage Change Curve 
 
Table 16: Test parameters for E3 Power Battery Voltage Dropout  
Operating Types 
2.3 and 2.1 
Test Procedure 
See the figure 2, there are two variants: 
1,Slow decrease and Slow increase of the supply voltage 
2,Slow decrease and Fast increase of the supply voltage 
Variants I 
II III 
IV V 
VI 
VII 
1 
0.5V/min * 
0.5V/min ** 0.5V/min * 
0.5V/min 
2 
0.5V/min * 
0.5V/min ** 32V/s 
*** 32V/s 
* Holding time at UBmin Until the event log is completely 
read.  
**Holding time at 0v  a t least 1 min; however, as long as 
internal capacity is completely discharged 
***No holding duration. 
 
Number of cycles 
1 cycle in operating type 2.3 
1 cycle in operating type 2.1 
Number of Test Samples  3 
Requirement: 
The evaluation of the test results depends on the voltage range that is applied to the 
component during the test. A distinction is made between: 
a) Within the defined operating voltage of the component: 
Functional status A must be achieved. Event log entries are not permitted. 
b) Outside of the defined operating voltage of the component: 
Functional status C can be accepted. 
4.4 E4 Sinusoidal Superimposed Voltage 


### 第 20 页
GB/T 2800001—2015 
16 
Goal: This test shall verify the component’s immunity to generator output ripple voltage due 
to rectified sinusoidal generator voltage. 
Test: 
Table 17: Test parameters for E4 Sinusoidal Superimposed Voltage  
Operating type  
2.3 
Ri  
<=100mΩ  
Test duration 
30 min 
Frequency range  
15Hz – 30kHz 
Wobble duration  
2 min（including from 15Hz to 30kHz to 
15Hz） 
Type of wobble  
Triangle, logarithmic 
UPP  
4V 
Test Procedure 
The real vehicle situation, ideally 
with the original vehicle harness, 
must be simulated. See the figure 3 
Number of Test Samples 
3 
 
Figure 3: E4 Sinusoidal Superimposed Voltage 
Requirement: 
Functional Status Classification shall be A. 
4.5 E5 Pulse Superimposed Voltage 
Goal: This test shall verify the component’s immunity to supply voltage pulses that occur on 
battery supply in the normal operating voltage range. These voltage pulses will simulate a 
sudden high current load change to the battery supply line, causing a voltage drop or voltage 
rise at switch on or off. This test simulates loads with inrush current behavior such as motors, 
incandescent bulbs, or long wire harness resistive voltage drops modulated by PWM controlled 
high loads. 
Test: 
 Table 18: Test parameters for E5 Pulse Superimposed Voltage  


### 第 21 页
GB/T 2800001—2015 
17 
Operating Type 
2.3 
Application 
All components that have power supplied by the vehicle battery 12 
V wiring system 
Monitoring 
Continuous Monitoring 
Test Procedure 
Refer to Figure 4. 
1 Connect the component to the Uo output. 
2 Raise ambient temperature to Tmax, then stabilize the component at 
the temperature for at least 0.5h when reaching the setting point. 
3 Set Us = Umax - 2V. 
4 Perform 5 continuous frequency sweep cycles while continuously 
monitoring for intermittent faults. 
5 Decrease Us by 1V. 
6 Repeat steps (4) and (5) until (Us = Umin + 2V). 
7 Repeat steps (3) to (6) at Troom. 
8 Repeat steps (3) to (6) at Tmin. 
Test Setup 
Definitions/Parameters 
• U0 = Us + Up 
• Us = (Umin + 2V) to (Umax - 2V) DC voltage 
• Up = Square wave -1V to +1V 50 % duty cycle (2Vp-p) 
• Up frequency sweep range: 1 Hz to 4 kHz 
• Frequency sweep type: Logarithmic 
• Frequency sweep duration for one cycle:120s for 1Hz to 4kHz to 1Hz 
Note 
• For components with power output drivers, the test shall be 
performed with real or equivalent loads connected (Load_1 to Load_n) 
and the output currents driven in the full range from I_load_min to I_load_max 
for each Us step. 
• The U0 waveform will depend on the frequency. Figure 5 shows 
examples of the following frequencies: 1 Hz, 100 Hz and 4 kHz (Us 
= +14 V, 
Up = +1 V / -1 V). 
  
Io output current capability 50 A; Rise time<10s for a 2 V step; RC=3.18ms(50 Hz f_low) 


### 第 22 页
GB/T 2800001—2015 
18 
  
 
Figure 4: Pulse Superimposed Voltage test setup 
Requirement: 
Functional Status Classification shall be A.  
 
 
                     Figure 5: Examples for Pulse Waveforms 
4.6 E6 Short Circuit In signal Circuit And Load Circuits 
Goal: Short circuits on all device inputs and outputs and in the load circuit are simulated. 


### 第 23 页
GB/T 2800001—2015 
19 
All inputs and outputs must be designed short-circuit-proof against +UB and GND (for 
activated and non-activated outputs as well as for missing voltage supply and missing ground). 
The component must be permanently short-circuit-proof. 
The component is connected as described in Figure 6. 
Test: 
Table 19: Test parameters for E6 Short circuit in signal circuit and load circuits  
Operating Type 2.3 
Test duration 
Short circuit of each pin individually for 60 s to ground and to UB. 
Test voltages 
UBmin and UBmax 
Test setup 
The power supply unit used for the test must be able to supply the 
short-circuit currents to be expected by the component. If this is not 
possible, buffering of the power supply unit by means of a car battery is 
permissible (UBmax is the maximum charging voltage in this case). 
Number of 
cycles 
Each pin once against ground and against UB. 
Number of Test 
Samples 
3 
  
 
            Figure 6: Schematic circuit E6 Short circuit in signal circuit and load circuits 
Requirement: 
To pass the test, the following functional statuses must be achieved: 
- For inputs and outputs (E and A according to Figure 6): Functional status C 
4.7 E7 Open Circuit – Single Line Interruption 
Goal: The supply line interruption of individual pins is simulated. Testing is carried out 
in two different operating statuses. Different pulse forms are used since the possible 
interruptions may differ greatly regarding their duration (from loose contacts to permanent 
interruption). 
Test: 
 Table 20: Test parameters for E7 Open Circuit–Single Line Interruption  


### 第 24 页
GB/T 2800001—2015 
20 
Operating Type Test 1:KL.30 ON and KL.15 ON 
Test 2:KL.30 ON 
Test case 1 
Each pin is to be removed for 10s and then replaced (slow interval). 
Test case 2 
Each pin is to be removed for 1ms and then replaced, for circuits with relays 
100μs.  
Test case 3 
Pulse package on each pin in order to simulate a loose contact. The following 
criteria apply: 
Loose contact:Only if component is switched by means of a relay (bouncing 
relay) 
Pulse 
definition for 
test case3 
(Figure 7) 
Loose contact  
t = 0.1ms 
t1 = 1ms 
t2 = 4s 
t3 = 10s 
tr 
≤(0,1 * t) 
tf 
≤(0,1 * t) 
Number of 
cycles 
Each of the three test cases must be tested in both operating statuses 
specified above. Each test must be evaluated separately. 
Number of Test 
Samples 
3 
  
 


### 第 25 页
GB/T 2800001—2015 
21 
                    Figure 7: Test pulse E7 Pin interruption 
Requirement: 
Test case 1: Functional status C 
Test case 2: Functional status C 
Test case 3: Functional status A 
4.8 E8 Open Circuit – Multiple Line Interruption 
Goal: This test shall verify that the component is immune to multiple line open circuit 
conditions. This will occur when a vehicle harness connector becomes disconnected. 
Test: 
Table 21: Test parameters for E8 Open Circuit – Multiple Line Interruption  
Operating Type 2.3、2.1 
Test sequence 
Both tests must be performed with each connector. Each connector must be 
removed from the DUT for 10 s and then replaced. If the DUT has several 
connectors, each connector must be tested individually. The test sequence 
must be variable. 
Number of 
cycles 
Each connector must be removed once. 
Number of Test 
Samples 
3 
Requirement: 
After the connector is replaced, functional status C must be achieved. 
4.9 E9 Ground Offset 
Goal: In components with several voltage inputs, potential differences between the individual 
supply inputs may occur. It must be ensured that the functionality of the component is not 
influenced for a potential difference to ground of up to +/-1V. 
Test: 
If the DUT has several voltage and ground connections, the test must be performed 
individually for each connection point. The component is connected as described in Figure 8. 
Table 22: Test parameters E9 Ground offset  
Operating Type 2.3 
Voltage source +/-1V 
Number of 
cycles 
Both switching positions 
Test case 1 
U 
UBmin 
Test case 2 
U 
UBmax 
Number of Test 
Samples 
3 


### 第 26 页
GB/T 2800001—2015 
22 
 
S1 Two-pin (a/b) change-over switch 
TR Test reference,e.g., test bed, simulation electronic control unit, 
   actuator, sensor, or load 
Figure 8: Schematic circuit E9 Ground offset 
Requirement: 
Functional status A must be achieved for a potential difference of +/-1V. 
4.10 E10 Discrete Digital Input Threshold Voltage 
Goal: This test shall verify the performance of discrete digital input interfaces (including 
switch interfaces). 
Test: 
 
Table 23: Test parameters for E10 Discrete Digital Input Threshold Voltage  
Operating Type 2.3 
Applicability 
Discrete digital input and switch input interfaces with transmitters 
grounded locally without signal return to receiver. It can be replaced by 
Analysis at Hardware Design Review. 
Monitoring 
Continuous Monitoring. Additionally, readout of logic state and current. 
Procedure 
Connect the component to the Us supply and power ground. Refer to Figure 9 
and follow the given sequence. 


### 第 27 页
GB/T 2800001—2015 
23 
 
Figure 9: Threshold Voltage Test Setup 
1 At Troom perform the following sequence (see Figure10). 
2 Adjust Us to Umin for the component. 
3 Adjust Ustep to 0V. 
4 Adjust Usin to 50Hz sinusoidal with an amplitude of 200mV peak-peak. 
5 Read logic output state and store. 
6 Step Ustep by 250mV increments from 0 to Umin while recording the output logic state. 
Keep each step for 5s and sample output with a 100ms repetition rate until all 50 recorded 
logic states in a row have changed to the new value. 
7 Store input value of Ustep when this occurs to the name Uih_rise. Continue until Umin 
is reached. 
8 While at Umin start to decrease Ustep by 250mV steps from Umin down to 0V. Use the same 
procedure as in step (6) to detect a logic state change. 
9 Store input value of Ustep when this occurs to the name Uil_fall. Continue until 0V is 
reached. 
10 Rise voltage Ustep to (Uih_rise+Uil_fall)/2 and record the number of state changes 
during a 5s time period as Nth_Umin (see Figure 11). 
11 Repeat steps (2) to (10) by replacing Umin with Umax and store the new Uih_rise and Uil_fall. 
12 Repeat steps (2) to (11) at Tmax and Tmin for the component. 
13 Write a report including: 
• At Troom and Umin: Uih_rise,Uil_fall and Nth_Umin 
• At Troom and Umax: Uih_rise,Uil_fall and Nth_Umin 


### 第 28 页
GB/T 2800001—2015 
24 
• At Tmax and Umin: Uih_rise,Uil_fall 
• At Tmax and Umax: Uih_rise,Uil_fall 
• At Tmin and Umin: Uih_rise,Uil_fall 
• At Tmin and Umax: Uih_rise,Uil_fall 
 
Figure 10: Threshold Voltage Waveform – All Steps 
-2 
 


### 第 29 页
GB/T 2800001—2015 
25 
Figure 11: Threshold Voltage Waveform – Step (10) 
Requirement: 
Functional Status Classification is not applicable to this test. All discrete digital input 
interfaces shall be able to correctly detect the logic levels: 
Logic Low: -1 V<Uil<2V Logic High: 4.5V<Uih<Umax +1V This then requires that the Uih_rise 
and Uil_fall both fall into the range (2.0...4.5)V over the full operating temperature and 
voltage range. The interface hysteresis is included in this requirement. The current shall 
not increase significantly (>5mA) during the (Uih_rise+Uil_fall)/2 range. 
4.11 E11 Over Load – All Circuits 
Goal: The over current protection of mechanical switches, electronic outputs and contacts is 
tested. Higher currents than in the normal load case (e.g. maximum blocking current of a motor) 
must also be considered. 
Test: 
Table 24: Test parameters for E11 Over Load – All Circuits  
Operating Type 2.3 
Temperature 
Tmax 
Test 
conditions for  
electronic 
outputs 
The output must withstand at least the triple value of the nominal load 
without damage. Load duration 30 min 
Test 
conditions for 
switched 
outputs 
If the maximum blocking current is larger than 3 x IN, the maximum blocking 
current must be used instead of IN. Load duration 10 min For components with 
IN ≤10A: 3 x IN。 For components with IN > 10A: 2 x IN, but at least 30A 
and max. 150A (switch "OFF", "ON" and "OFF" again under load).Each contact 
must be tested individually in the case of multiple-contact relays and 
multiple-contact switches. 
Number of Test 
Samples 
3 
Requirement: 
Functional status A for mechanical components without fuse must be reached. If fuse 
elements are available in the load circuit, these may be triggered. 
Functional status C for electronic outputs with overload detection (current, voltage, 
temperature) should be reached. 
In addition, no dangerous changes that impair the function or service life (visual and 
electrical characteristics), must occur in a visual evaluation of all components. 
4.12 E12 Isolation Resistance 
Goal: This test shall verify the component’s immunity to loss of insulation. This could result 
in electrical performance degradation and signal interference. 
Test: 
Table 25: Test parameters for E12 Isolation Resistance  


### 第 30 页
GB/T 2800001—2015 
26 
Operating Type 1.1 
Temperature 
Troom 
Test voltage 
500V DC 
Test duration 
60s 
Relative 
humidity 
50%、Troom 
Test sequence 
For preparation, the DUTs must undergo the "humid heat, cyclic" test, which 
must be agreed upon with the purchaser. Before the measurement, the DUTs must 
be allowed to dry for 30 minutes. 
Test points 
Application of the test voltage 
- To terminals without galvanic connection. 
- Between connection pins and conducting housing without galvanic 
connection. 
- Between connection pins and an electrode around the housing if the housing 
is nonconducting. 
– To further test points coordinated with the respective engineering 
department. 
Number of 
cycles 
1 
 
 
Number of Test 
Samples 
3 
Requirement: 
The insulation resistance must be at least 10M. It must be verified that the DUT has not 
been damaged. Function classification after test shall be Class A. 
4.13 E13 Over Voltage 
Goal: The component's resistance to long-term over voltage is tested. A generator control fault 
during driving operation is simulated.  
Test: 
Table 26: Test parameters for E13 Over Voltage  
Operating Type 2.3 
Temperature 
Tmax – 20C 
t1 
60min 
Umax 
17 V (+4%, 0%) 
Umin 
13,5 V 
tr 
<10 ms 
tf 
<10 ms 
Continuous 
test voltage 
17V 
Number of 
cycles 
1 
 
 
Number of Test 
3 


### 第 31 页
GB/T 2800001—2015 
27 
Samples 
 
Figure 12: Over Voltage 
 
Requirement: 
The evaluation of the test results depends on the use of the component. A distinction is 
made between: 
a) Functions required for driving operation: 
Functional status B 
If required, an emergency mode must be defined. The corresponding derating strategy must 
be described in the Component Performance Specifications. 
b) For all other components: 
Functional status C 
4.14 E14 Transient Over Voltage 
Goal: Transient over voltages may occur in the electric system due to the switching off of loads 
and due to short accelerator tip-ins. These over voltages are simulated by means of this test. 
This test may be used for the electrical life test.  
Test: 
Table 27: Test parameters for E14 Transient over voltage  
Operating Type 
2.3 
Umin 
16V 
U1 
17V 
Umax 
18V 
tr 
1ms 
 
tf 
1ms 


### 第 32 页
GB/T 2800001—2015 
28 
t1 
400ms 
t2 
600ms 
Test case 1 
Ttest 
Tmax 
Number of cycles 
3 
t3 
2s 
Test case 2 
Ttest 
Tmin 
Number of cycles 
3 
t3 
2s 
Test case 3 
Ttest 
Troom 
Number of cycles 
100 
t3 
8s 
Number of Test Samples 
6 
 
Figure 13: Test pulse E14 Transient over voltage 
Requirement: 
Functional status A 
All relevant outputs must remain within the defined limits during the test - this must 
be verified during the complete test duration. 
4.15 E15 Transient Under Voltage 
Goal: Transient under voltages in the electric system may occur due to switching on of loads. 
These under voltages are simulated by means of this test. 


### 第 33 页
GB/T 2800001—2015 
29 
Test: 
Table 28: Test parameters for E15 Transient under voltage  
Operating Type 2.3 
Umin 
9V 
Umax 
10.8V 
tr 
1.8ms 
 
tf 
1.8ms 
Ttest 
500ms 
Test case 1 
Ttest 
Tmax 
Number of 
cycles 
3 
Test case 2 
Ttest 
Tmin 
Number of 
cycles 
3 
Number of Test 
Samples 
3 
 
Figure 14: Test pulse E15 Transient under voltage 
Requirement: 
Functional status A 
4.16 E16 Jump Start 
Goal: External starting of the vehicle is simulated. The maximum test voltage results from 


### 第 34 页
GB/T 2800001—2015 
30 
commercial vehicle systems and their increased power supply voltage. 
Test: 
Table 29: Test parameters for E16 Jump start  
Operating Type 2.3 
Umin 
10.8V 
Umax 
26V 
tvor 
60s 
Ttest 
60s 
 
tr 
<10 ms 
tf 
<10 ms 
Number of 
cycles 
1 
Number of Test 
Samples 
3 
 
Figure 15: Test pulse E16 Jump start 
Requirement: 
The evaluation of the test results depends on the use of the component. A distinction is 
made between: 
c) For components relevant for starting (e.g. starter): 
Functional status B 
The sensors must deliver valid values over the whole time (or safeguarded by means of 
replacement tables in the components). 
b） All other components: 
Functional status C 


### 第 35 页
GB/T 2800001—2015 
31 
4.17 E17 Load Dump 
Goal: Dumping of an electric load, in combination with a battery with reduced buffering ability, 
results in an energy-rich over voltage pulse due to the generator characteristics. This pulse 
is simulated by means of this test. 
Test: 
Table 30: Test parameters for E17 Load dump  
Operating Type 2.3 
Umin 
13.5V 
Umax 
27V 
tr 
≤2 ms 
ts 
300ms 
 
tf 
≤30 ms 
Break between 
cycles 
1min 
Number of 
cycles 
10 
Number of Test 
Samples 
3 
 
Figure 16: Test pulse E17 Load dump  
Requirement: 
A distinction is made between: 
a) Safety-relevant components: 
Functional status B 
b) All other components: 


### 第 36 页
GB/T 2800001—2015 
32 
Functional status C 
4.18 E18 Short Interruptions 
Goal: The component's behavior at short interruptions of different durations is simulated. 
Test: 
Table 31: Test parameters for E18 short interruptions  
Operating Type 2.3 
Test setup 
Schematic circuit acc. to Figure 18. 
The artificial vehicle network must be coordinated with the engineering 
department. 
Test case 1 
S1 switched, S2 open, R = 100 kΩ 
S1 switched, S2 open, R = 0,1Ω(electric system) 
Test case 2 
S1 switched, S2 negates S1,R ≥ 10 kΩ 
Utest 
11V 
The supply 
voltage is 
interrupted by 
Utest at varying 
intervals. The 
following 
sequence must 
be complied 
with for this 
purpose: 
t1 
Intervals 
>10μs to 100μs 
10μs 
100μs to 1ms 
100μs 
1ms to 10ms 
1ms 
10ms to 100ms 
10ms 
100ms to 2s 
100ms 
DUT On - 
function On 
> 10s 
t2 
The test voltage Utest must be held at least until the DUT has achieved 100% 
serviceability (all systems rebooted without error). 
Number of 
cycles 
1 
Number of Test 
Samples 
3 
The duration of the voltage dip increases at the intervals specified in Table 30. This 
results in a diagram as shown in Figure 17. 


### 第 37 页
GB/T 2800001—2015 
33 
 
Figure 17: Test pulses E18 Short interruptions 
 
 
Figure 18: Schematic circuit E18 Short interruptions 
Requirement: 
It must be documented as of which time value t1 the DUT leaves functional status A for 
the first time. The test is passed if the DUT achieves functional status A in the range up 
to 100μs, otherwise functional status C. A deviating value for the permissibility of functional 
status C must be defined in the Component Performance Specifications. 
4.19 E19 Start Pulse 


### 第 38 页
GB/T 2800001—2015 
34 
Goal: When starting the engine, the battery voltage drops to a low value for a short period 
and then slightly rises again. Most components are activated directly before starting for a 
short period, then deactivated during starting and activated again after starting when the 
engine is running. In this test, the behavior of the component in the event of voltage 
dips caused by starting is examined. 
     The starting process may be performed under different vehicle starting conditions: cold 
start and hot start (automatic restart for start-stop). In order to cover both cases, two 
different test cases are required. A component must always undergo both sequences. 
Test: 
Table 32: Test parameters for E19 Start pulse  
Operating Type 2.3 
Test pulse 
- Cold start: "normal" and "severe" test pulse as per 
Table 33 
- Hot start: "short" and "long" test pulse as per 
Table 34 
Number of Test 
Samples 
6 
4.19.1 Test 1 - Cold Start 
Table 33: Test parameters E19 Start pulses for cold start 
Parameter 
Test pulse "normal" 
Test pulse "severe" 
UD 
11.0V 
11.0V 
UT 
4.5 V (0%, -4%) 
3.2V +0.2V 
US 
4.5 V (0%, -4%) 
5.0 V (0%, -4%) 
UA 
6.5 V (0%, -4%) 
6.0 V (0%, -4%) 
UR 
2V 
2V 
tf 
≤1ms 
≤1ms 
t4 
0ms 
19ms 
t5 
0ms 
≤ 1ms 
t6 
19ms 
329ms 
t7 
50ms 
50ms 
t8 
10s 
10s 
tr 
100ms 
100ms 
f 
2Hz 
2Hz 
Break between two cycles 
2s 
2s 
Test cycles 
10 
10 


### 第 39 页
GB/T 2800001—2015 
35 
 
Figure 19: Cold start test pulse 
4.19.2 Test 2 - Hot Start 
 
 
Table 34: Test parameters E19 Start pulses for hot start 
Parameter 
Test sequence"short" 
Test sequence"long" 
UD 
11.0V 
UT 
7.0V 
US 
8.0V 
UA 
9.0V 
t50 
≥10ms 
tf 
≤1ms 
t4 
15ms 
t5 
70ms 
t6 
240ms 
t7 
70ms 
t8 
600ms 
tr 
≤1ms 
Break between two cycles 
5s 
20s 
Test cycles 
10 
100 


### 第 40 页
GB/T 2800001—2015 
36 
 
Figure 20: Warm start test pulse 
 
 
Requirement: 
Components relevant for starting: 
Event log entries must not occur. It must always be possible to start the vehicle. 
Test 1 - Cold start 
Test pulse "normal": Functional status A 
Test pulse "severe": Functional status B 
Test 2 - Hot start: 
Test sequence "long": Functional status A 
Test sequence "short": Functional status A 
Components not relevant for starting: 
Test 1 - Cold start 
Test pulse "normal": Functional status C 
Test pulse "severe": Functional status C 
Test 2 - Hot start: 
Test sequence "long": Functional status A 
Test sequence "short": Functional status A 
4.20 E20 Voltage Curve With Intelligent Generator Control 
Goal: The behavior of the electric system with voltage controls, e.g., with the use of 
intelligent generator controls or DC-DC converter controls, is simulated. By means of the 
control,voltage curves can be set in the range from constant voltage to permanent voltage 
fluctuations according to the test cases as per Table 35. 


### 第 41 页
GB/T 2800001—2015 
37 
     This is relevant to all load cases that the component can assume with the engine 
running or the vehicle ready for operation. 
Test: 
Table 35: Test parameters for E20 Voltage curve with intelligent generator control  
Operating Type 
2.3  
Umin 
(11.8 V - ΔV) (0%, -4%) 
Umax 
(15 V - ΔV) (+4%, 0%) 
t1 
2s 
tr 
≥300 ms 
tf 
≥300 ms 
Number of cycles 
10 
Number of DUTs 
6 
Test case 1 
ΔU 
0 V 
Test case 2 
ΔU 
0.7 V 
Test case 3 
ΔU 
2 V 
 
Figure 21: Test pulse E20 Voltage curve with intelligent generator control 
Requirement: 
Functional status A 


### 第 42 页
GB/T 2800001—2015 
38 
4.21 E21 Reverse Polarity 
Goal: The resistance of the DUT against reverse-polarity battery connection during jump 
starting is simulated. Reverse polarity can occur several times and must not cause damage to 
the component. Reverse polarity protection must be ensured for any voltages down to the minimum 
test voltage. The vehicle fuse is not part of the reverse polarity protection concept. 
Test: 
     All relevant connections of the original circuitry must be tested.The DUT must be 
addressed in the same way as it is in the vehicle circuit.The test must be performed at various 
voltages between 0 V and the maximum values specified in Table 37 and Table 38. 
 
Table 36: Test parameters for E21 Reverse polarity  
Operating Type 2.1 
Number of 
cycles 
See Table 37 and Table 38 
Number of Test 
Samples 
6 
4.21.1 Static reverse polarity 
     This test case checks the robustness of the component at various reverse polarity voltages 
that can arise depending on the vehicle state. 
Table 37: Test parameters, E21 Reverse polarity, static 
ΔU1 
-1 V 
Severity 1 
Ri <100 mΩ 
Severity 2 
Ri <30 mΩ 
t1 
60s 
For a component for which the operating voltage is switched off 
by a relay in the event of reverse polarity, the following deviating 
value applies: 
8 ms 
t2 
≥60 s, but at least until the component has reached the same 
thermal state as at the beginning of the test 
tr 
≤10 ms 
tf 
≤10 ms 
Umax 
0 V 
Umin 
-14.0 V 
Number of 
cycles 
1 


### 第 43 页
GB/T 2800001—2015 
39 
Figure 22: Test pulse, E21 Reverse polarity – Static 
4.21.2 Dynamic reverse polarity 
      This test case checks the reverse polarity of the component during operation in a vehicle 
that is no longer capable of starting. 
Table 38: Test parameters, E21 Reverse polarity, dynamic 
Severity 1 
Ri <100 mΩ 
Severity 2 
Ri <30 mΩ 
Umax 
10,8 V 
Umin 
-4,0 V 
t1 
60 s 
For a component for which the operating voltage is switched off 
by a relay in the event of reverse polarity, the following deviating 
value applies: 
8 ms 
t2 
≤5 min 
tr 
≤10 ms 
tf 
≤10 ms 
Number of 
cycles 
3 


### 第 44 页
GB/T 2800001—2015 
40 
 
Figure 23: Test pulse, E21 Reverse polarity - Dynamic 
Requirement: 
When reverse polarity is applied, no safety-relevant functions must be triggered, e.g. 
for electric windows, electric sunroof, starter, etc. 
During reverse polarity, the permissible limit values specified in the data sheet 
(electrical and thermal) must not be exceeded for any of the parts. 
The fuse rated current of the vehicle fuse system must not be exceeded during the test. 
The reverse polarity must not cause pre-damage or hidden damage on the component. 
The reverse polarity resistance also applies to any voltage from 0V to maximum testing 
voltage. 
Reverse polarity resistance must be in compliance with functional status C. 
The current draw during the test must be documented. 
4.22 E22 Dielectric Strength 
Goal: The dielectric strength between parts of the DUT that are galvanically isolated from 
each other, e.g., connector pins, relays, windings, or lines, is simulated. The test must be 
performed on components that contain or control inductive parts. 
Test: 
Table 39: Test parameters for E22 Dielectric strength  
Operating Type 2.1 
Test voltage 
Ueff = 500V AC,50Hz 
Test duration 
60s 
Test sequence 
For preparation, the DUTs must undergo the "humid heat, cyclic" test, which 
must be agreed upon with the purchaser. Before the measurement, the DUTs must 
be allowed to dry for 30 minutes. 
Test points 
Application of the test voltage 


### 第 45 页
GB/T 2800001—2015 
41 
- To terminals without galvanic connection. 
- Between connection pins and conducting housing without galvanic 
connection. 
- Between connection pins and an electrode around the housing if the housing 
is nonconducting. 
- To further test points coordinated with the respective engineering 
department. 
Relative 
humidity 
50%  
Temperature 
Troom 
Number of 
cycles 
1 cycle must be performed, in which each of the points defined above must 
be tested at least once. 
Number of Test 
Samples 
6 
Requirement: 
    Functional status C 
Dielectric breakdowns and electric arcs are not permissible. 
4.23 E23 Back Feeds 
Goal: The behavior of the DUT connected to KL.15 is simulated. This test must be carried out 
with all components connected to terminal 15.For other terminals with "wake-up functions", 
the test must also be performed. 
Test: 
Table 40: Test parameters for E23 Back feeds 
Operating Type 2.3 
Utest 
UBmax – 0.2V 
Test 
temperatures 
Tmax,Troom and Tmin 
Number of Test 
Samples 
3 
Test sequence: 
The DUT is connected identical to the wiring situation in the vehicle (including sensors, 
actuators, etc.) and operated in normal operation. The voltage curve on KL.15 is measured when 
it is cut off. The terminal must be cut off by means of a relay or a switch (Ropen_switch→∞). 
Other possible voltage sources such as KL.30, for example, must not be interrupted or cut off 
during the test (acc. to the behavior in the vehicle). Other resistors on KL.15 are not permitted 
for this test.The voltage curve on KL.15 is evaluated with an external resistor of ≥10MΩ 
(e.g.,oscilloscope) to terminal 31. 


### 第 46 页
GB/T 2800001—2015 
42 
 
             Figure 24: Schematic circuit for test E23 Backfeeds  
Requirement: 
Backfeed to KL.15 is permissible up to a maximum voltage level of 1.0V only. This voltage 
range must be achieved within t = 20ms after cutoff. The voltage on the unconnected KL.15 must 
decrease below a voltage of Ukl.15 = +1V within t = 20 ms from the cutoff. A continuously decreasing 
function is required for the voltage curve. A discontinuity of the curve due to positive pulses 
is not permitted. 
4.24 E24 Nominal And Worst Case Performance Analysis 
Goal: This analysis shall identify that the design of the circuit is capable of producing the 
required functions. 
Test: 
Table 41: Test parameters for Nominal and Worst Case Performance Analysis 
Operating Type 
N/A 
Applicability 
All components 
Monitoring 
N/A 
Procedure 
Use a circuit analysis program to determine voltage, current and power 
dissipation for each part across the operation temperature and supply and 
I/O voltage range. 
Requirement: 
Verify that the design of the circuit is capable of producing the required functions under 
all conditions.The component shall meet the requirements according to SSTS/CTS  
 
4.25 E25 Short/Open Circuit Analysis 
Goal: This analysis shall identify that the component withstands intermittent and continuous 
shorts to battery/supply voltages and to ground, and open circuit conditions. 
Test: 


### 第 47 页
GB/T 2800001—2015 
43 
Table 42: Test parameters for Short/Open Circuit Analysis 
Operating Type 
N/A 
Applicability 
All components 
Monitoring 
N/A 
Procedure 
Use a circuit analysis program to perform intermittent (default: 1Hz to 
100Hz square wave) and continuous conditions for short and open circuits 
to determine voltage, current, and power dissipation for each part across 
the operation temperature, supply, and output current range. 
Requirement: 
Verify ability of parts to withstand intermittent and continuous short/open circuit 
conditions. The analysis shall verify that the part’s operating parameters as defined by the 
part’s data sheet shall not be exceeded. 
5 Mechanical Requirements And Tests 
Table 43 is the mechanical tests overview. 
Table 43: Mechanical Tests Overview 
Test 
M1 Vibration Test 
M4 Crush For Housing-Elbow Load 
M2 Mechanical Shock 
M5 Crush For Housing-Foot Load 
M3 Endurance Shock test 
M6 Free Fall 
M7 Resonant Frequency 
Analysis 
M8 High Altitude Shipping Pressure Effect Analysis 
M9 Plastic Snap Fit Fastener 
Analysis 
M10 Crush Analysis 
M11 Vibration Noise(Squeak & 
Rattle) 
M12 Connector Test 
5.1 Vibration Test 
Goal: This test simulated the vibrational load of the component during driving operation. The 
test serves to verify the resistance of the part to faults, such as component displacement 
or material fatigue. 
Test: 
The test is carried out acc. to DIN EN 60068-2-6 for sinusoidal vibration excitation and 
DIN EN 60068-2-64 for wide-band vibration excitation with the following parameters: 
Table 44: Test parameters for general vibration 
Operating Type 
Repeating, acc. to Figure 25: Temperature 
profile – vibration 
Superimposed 
temperature 
profile 
Repeated profile acc. to Figure 25: 
Temperature profile – vibration 
Table 511: Temperature curve for vibration 
Frequency sweep 1 octave/min, logarithmic 


### 第 48 页
GB/T 2800001—2015 
44 
time for 
sinusoidal 
excitation 
Vibration 
profile A(for 
engine-mounted 
parts) 
Vibration excitation, sinusoidal acc. to 
Figure 26 and Table 45 
Vibration excitation, wide-band random 
Vibration acc. to Figure 27 and Table 46 
Sinusoidal and random could be test together，or run independence 
vibration 
profile B(for 
gearbox-mounted 
parts) 
Vibration excitation, sinusoidal acc. To Figure 28 and Table 47 Vibration 
excitation, wide-band random Vibration acc. to Figure 29 and Table 48 
Sinusoidal and random could be test together，or run independence 
Vibration 
profile C (for 
components 
installed on the 
decoupled 
intake plenum) 
Vibration excitation, sinusoidal acc. To Figure 30 and Table 49 
Vibration 
profile D 
(body-mounted 
parts for 
components 
installed on 
sprung masses) 
Vibration excitation, wide-band random vibration acc. to Figure 31 and Table 
50 
Vibration 
profile E for 
unsprung masses 
(chassis) 
Vibration excitation, wide-band random Vibration acc. to Figure 32 and Table 
51 
Number of Test 
Samples 
3 
The test must be performed without bracket or add-on parts.Additional tests with bracket 
or add-on parts must be coordinated with the purchaser(FOTON Validation Engineer), if 
required.For components that are installed on the bracket or vehicle through damping elements, 
it must be specified in the Component Performance Specifications whether 
- all DUTs with damping elements 
- all Test Samples without damping elements or 
- three DUTs with damping elements and three DUTs without damping elements must be tested. 
The sampling rate must be selected so that interruptions or short circuits can be 
unambiguously detected. 


### 第 49 页
GB/T 2800001—2015 
45 
 
Figure 25: Temperature profile – vibration 
 
Table 511: Temperature curve for vibration 
Time (min) 
Temperature (°C) 
0 
Troom 
60 
Tmin 
150 
Tmin 
300 
Tmax 
410 
Tmax 
480 
Troom 
    If there is a coolant circuit, the coolant temperature must track the respective test 
temperature to the limits Tcool,min and Tcool,max. Only the ambient temperature is varied outside 
of the coolant temperature limits. 
5.1.1 Vibration Profile A (for engine-mounted parts) 
Table 45: Test parameters - vibration, sinusoidal for engine-mounted parts 
Vibration 
excitation 
Sinusoidal 
Test duration 
for each spatial 
axis 
22 h 
Vibration 
profile 
Characteristic 1 applies to components mounted on engines with maximum 5 
cylinders. 
Characteristic 2 applies to components mounted on engines with 6 or more 


### 第 50 页
GB/T 2800001—2015 
46 
cylinders. 
The characteristics must be combined for components that can be used in both 
cases. 
Characteristic 
1 in Figure23 
Frequency in Hz 
Amplitude of the acceleration in m/s² 
100 
100 
200 
200 
240 
200 
270 
100 
440 
100 
Characteristic 
2 in Figure23 
Frequency in Hz 
Amplitude of the acceleration in m/s² 
100 
100 
150 
150 
440 
150 
Combination 
Frequency in Hz 
Amplitude of the acceleration in m/s² 
100 
100 
150 
150 
200 
200 
240 
200 
255 
150 
440 
150 
 
Figure 26: Vibration profile, sinusoidal for engine-mounted parts 
Table 46: Test parameters - vibration, wide-band random vibration for engine-mounted parts 
Vibration 
excitation 
Wide-band random vibration 


### 第 51 页
GB/T 2800001—2015 
47 
Test duration 
for each 
spatial axis 
22h 
RMS value of 
acceleration 
181m/s² 
Vibration 
profile Figure 
27 
Frequency in Hz 
Power density spectrum in (m/s²)²/Hz 
10 
10 
100 
10 
300 
0.51 
500 
20 
2000 
20 
 
Figure 27: Vibration profile, wide-band random vibration for engine-mounted parts 
5.1.2 Vibration Profile B (for gearbox-mounted parts) 
Table 47: Test parameters - vibration, sinusoidal for gearbox-mounted parts 
Vibration 
excitation 
Sinusoidal 
Test duration 
for each spatial 
axis 
22h 
Vibration 
profile Figure 
28 
Frequency in Hz 
Amplitude of the acceleration in m/s² 
100 
30 
200 
60 
440 
60 


### 第 52 页
GB/T 2800001—2015 
48 
 
Figure 28: Vibration profile,sinusoidal for gearbox-mounted parts 
Table 48: Test parameters - vibration, wide-band random vibration for gearbox-mounted parts 
Vibration 
excitation 
Wide-band random vibration 
Test duration 
for each spatial 
axis 
22h 
RMS value of 
acceleration 
96.6m/s² 
Vibration 
profile Figure 
29 
Frequency in Hz 
Power density spectrum (m/s²)²/Hz 
10 
10 
100 
10 
300 
0.51 
500 
5 
2000 
5 


### 第 53 页
GB/T 2800001—2015 
49 
 
Figure 29: Vibration profile,wide-band random vibration for gearbox-mounted parts 
5.1.3 Vibration Profile C (for components installed on the intake plenum,decoupled) 
Table 49: Test parameters, sinusoidal for components on the intake plenum,decoupled. 
Vibration 
excitation 
Sinusoidal 
Test duration 
for each spatial 
axis 
22h 
 
Vibration 
profile Figure 
30 
Frequency in Hz 
Amplitude of the acceleration in m/s² 
100 
90 
200 
180 
325 
180 
500 
80 
1500 
80 


### 第 54 页
GB/T 2800001—2015 
50 
 
Figure 30: Vibration profile, sinusoidal for components on the intake plenum, decoupled 
5.1.4 Vibration Profile D (body-mounted parts (for components installed on sprung masses)) 
Table 50: Test parameters, wide-band random vibration for sprung masses 
Vibration 
excitation 
Wide-band random vibration 
Test duration 
for each spatial 
axis 
8h 
 
RMS value of 
acceleration 
3.07 
Vibration 
profile Figure 
31 
Frequency in Hz 
Power density spectrum in (m/s²)²/Hz 
5 
0.884 
10 
20 
55 
6.5 
180 
0.25 
300 
0.25 
360 
0.14 
1000 
0.14 
2000 
0.14 


### 第 55 页
GB/T 2800001—2015 
51 
 
Figure 31: Vibration profile, wide-band random vibration for sprung masses 
5.1.5 Vibration Profile E For Unsprung Masses (chassis) 
Table 51: Test parameters, wide-band random vibration for unsprung masses 
Vibration 
excitation 
Wide-band random vibration 
Test duration 
for each spatial 
axis 
8h 
RMS value of 
acceleration 
107.3m/s² 
Vibration 
profile Figure 
32 
Frequency in Hz 
Power density spectrum in (m/s²)²/Hz 
20 
200 
40 
200 
300 
0.5 
800 
0.5 
1000 
3 
2000 
3 


### 第 56 页
GB/T 2800001—2015 
52 
 
Figure 32: Vibration profile,wide-band random vibration for unsprung masses 
Requirement: 
The DUT must be fully functional before, during and after the test,shall be meet Class 
A and all parameters must meet the specifications. Verification takes place by means of 
continuous parameter monitoring and 1-point test acc. to Section 3.3. 
 
5.2 M2 Mechanical Shock 
Goal: This test shall verify the component’s immunity to mechanical shock events produced by 
potholes and minor collisions (less than 16 mile/h or 25.7 km/h). 
Test: 
The test is carried out acc. to IEC 60068-2-29, Test Eb, Bump and IEC 60068-2-27, Test 
Ea, Shock  with the following parameters: 
Table 52: Test parameters M2 Mechanical shock 
Operating Type 
2.3 
Mechanical Shock – Pothole 
Component 
Location 
Peak 
Duration 
Number of Impacts 
Unsprung Masses 100 G 
11 ms 
20 x 6= 120 
Sprung Masses 
(Passenger 
Compartment) 
12 G 
20 ms 
400 x 6= 2400 
Sprung Masses 
(All other 
areas, 
25 G 
10 ms 
400 x 6= 2400 


### 第 57 页
GB/T 2800001—2015 
53 
including 
Cradles and 
Frames) 
Shock form 
Half-sine 
Mechanical Shock - Collision 
Description 
Value 
Acceleration 
100 G 
Nominal shock duration 
11 ms 
Nominal shock shape 
half sine 
Total Number of shocks 
3 x 6 directions = 18 
Number of Test 
Samples 
3 
Requirement: 
The sample must be fully functional before, during and after the test,shall be meet Class 
A, and all parameters must meet the specifications. Verification takes place by means of 
continuous parameter monitoring and 1-point test acc. to Section 3.3. 
5.3 M3 Endurance Shock Test 
Goal: This test simulates the acceleration forces of components that are installed in doors, 
hoods or liftgates and are subjected to high accelerations during opening and closing. The 
test serves to verify the resistance of the part to faults, such as component displacement 
or material fatigue. 
Test: 
The test is carried out acc.to DIN EN 60068-2-29 with the following parameters: 
Table 53: Test parameters for M3 Endurance shock test 
Operating Type 
2.3 
Peak 
acceleration 
300m/s² 
Shock duration 
6ms 
Shock form 
Half-sine 
Number of shocks  Installation area 
Number of shocks 
Driver's door 
100000 
Front passenger's door/rear doors 
50000 
Liftgats/rear gates 
30000 
Engine hood 
3000 
If the component is installed in several installation areas, the highest 
number of shocks must be applied. 
Shock direction shall simulate in vehicle position when shock happens. 
Installation 
position 
The DUT must be mounted in the test device acc.to the installation conditions 
in the vehicle. 
Number of Test 
Samples 
3 


### 第 58 页
GB/T 2800001—2015 
54 
Requirement: 
The DUT must be fully functional before, during and after the test ,shall be meet Class 
A and all parameters must meet the specifications. Verification takes place by means of 
continuous parameter monitoring and 1-point test acc. to Section 3.3. 
5.4 M4 Crush For Housing-Elbow Load 
Goal: This test shall verify that the housing of the component and the parts inside of the 
housing are not affected by resting on the component by elbow loads. 
Test: 
Table 54: Test parameters for M4 Crush For Housing-Elbow Load 
Operating Type 
1.1 
Applicability 
All components. 
Monitoring 
Measure case deflection during loading to insure case clearances to parts 
on the circuit board. 
Procedure 
The component shall be set up to allow testing on all external surfaces with 
a 13.0mm or larger diameter area. Subject the component to an evenly 
distributed 110N force about any 13.0mm diameter area for 1.0s as shown in 
Figure 33 (this represents the force applied by a person’s elbow). Perform 
a 1-Point Functional/Parametric Check after all surfaces have been tested. 
 
Figure 33: Elbow Load Applied to Top of Component Housing 


### 第 59 页
GB/T 2800001—2015 
55 
Requirement： 
1 The component shall survive without electrical degradation or permanent physical damage 
(Functional Status Classification = C). 
2 The case deflection during loading cannot contact any parts on the circuit board, except 
the connector. 
3 The deflection forces must not cause the cover to detach or “open up”. 
5.5 M5 Crush For Housing-Foot Load 
Goal: This test shall verify that the housing of the component and the parts inside of the 
housing are not affected by resting on the component by foot loads. 
Test: 
Table 55: Test parameters for M5 Crush For Housing-Foot Load 
Operating Type 
1.1 
Applicability 
All components that may experience foot loads during vehicle assembly or 
servicing. 
Monitoring 
Measure case deflection during loading to insure case clearances to parts 
on the circuit board. 
Procedure 
Locate a (50 x 50)mm (or appropriately sized) rigid steel plate on top of 
the component.Subject the component to an evenly distributed 890N force 
applied normally to the top of the component through the steel plate for 
1 minute as shown in Figure31.Perform a 1-Point Functional/Parametric Check 
afterthe test. 
 


### 第 60 页
GB/T 2800001—2015 
56 
Figure 34: Foot Load Applied to Top of Component Housing 
Requirement： 
1 The component shall survive without electrical degradation or permanent physical 
damage(Functional Status Classification = C). 
2 The case deflection during loading cannot contact any parts on the circuit board, except 
the connector. 
3 The deflection forces must not cause the cover to detach or “open up”. 
5.6 M6 Free Fall 
Goal: This test simulates the free fall of a component to the floor, as it may occur during 
the complete process chain until the intended installation of the component.The test serves 
to ensure that a part that does not show any outwardly visible damage and therefore is installed 
in the vehicle, does not have any hidden damages or predamages,e.g. internal part displacement 
or cracks. 
 
Test: 
Table 56: Test parameters for M6 Free fall 
Operating Type 
1.1 
Drop height 
1m 
Impact surface 
Concrete floor 
Test cycle 
For each of the 3 DUTs one drop in both directions of a spatial axis (1st 
DUT: ±X,2nd DUT: ±Y, 3rd DUT: ±Z) 
Number of Test 
Samples 
3 
Requirement： 
The DUT must be evaluated visually with the naked eye and tested for loose or rattling 
parts by means of shaking. 
- If the DUT is damaged on the outside, this damages must be documented in the test report. 
- If the DUT is not damaged on the outside, it must be fully functional after the test,shall 
be meet Class A, and all parameters must meet the specifications. Verification takes place 
by means of 5-point test acc. to Section 3.3. Hidden damages are not permissible. 
5.7 M7 Resonant Frequency Analysis 
Goal: This analysis shall identify the resonant frequency to detect structural weaknesses that 
may lead to mechanical fatigue. 
Test: 
Table 57: Test parameters for Resonant Frequency Analysis 
Operating Type 
N/A 
Applicability 
All components with a circuit board 
Monitoring 
N/A 
Procedure 
Considering the circuit board mounting configuration, calculate the 
resonant frequency of the circuit board by using appropriate software such 


### 第 61 页
GB/T 2800001—2015 
57 
as Finite Element Analysis. 
Requirement： 
The resonant frequency of the circuit board shall be greater than 150 Hz. The supplier 
must provide evidence of appropriate corrective action when the resonant frequency is below 
150 Hz. The corrective action is to be reviewed with the FM Validation Engineer. 
5.8 M8 High Altitude Shipping Pressure Effect Analysis 
Goal: This analysis shall identify mechanical destruction that may occur during shipping in 
an un-pressurized aircraft up to an altitude of 15240m above sea level 
Test: 
Table 58: Test parameters for High Altitude Shipping Pressure Effect Analysis 
Operating Type 
N/A 
Applicability 
All components or parts that are hermetically sealed and may be shipped at 
high altitude 
Monitoring 
N/A 
Procedure 
The following series of steps shall be taken to ensure adequate robustness 
of the structure under pressure to the effects of the low pressure stress 
resulting from air shipment. 
1、Quantify the burst pressure (Pburst) of the component (or part) under 
internal pressure using finite element analysis or a comparable method. Use 
a worst case analysis process considering the variation of material 
parameters (such as the minimum wall thickness) and the effects of material 
weakening relative to temperature effects (glass transition temperatures). 
The glass transition temperature(Tg) is the temperature whereupon the 
material properties, such as stiffness, changes its characteristics. 
2、Use the following equation for the analysis: Pburst ³ (Passembly – Paltitude) x 
DM，Pburst : Component (or part) burst pressure；Passembly: Internal pressure of 
component (or part) during assembly (this should be either the ambient air 
pressure at the assembly location, or it may be a different pressure if 
modified by the manufacturing process) Paltitude: Pressure in the freight 
section of the airplane at 15 240 m, use 11 kPa. Design Margin (DM): 4 
Requirement： 
The component (or part) burst pressure must exceed the resulting internal pressure during 
air shipment by a factor of 4. 
5.9 M9 Plastic Snap Fit Fastener Analysis 
Goal: This analysis shall ensure that the snap fit is adequately designed. Also, this analysis 
shall identify structural robustness of plastic snap fit fastener design fundaments, 
including: 
• Adequate retention force. 
• Acceptable ergonomic forces during vehicle assembly and disassembly for serviceability. 
• Presence of compliance mechanisms to prevent rattles. 


### 第 62 页
GB/T 2800001—2015 
58 
• Adequate design margin to ensure that flexing during vehicle installation does not exceed 
the elastic limit of the plastic. 
Test: 
Table 59: Test parameters for Plastic Snap Fit Fastener Analysis 
Operating Type 
N/A 
Applicability 
All components that incorporate plastic snap fits 
Monitoring 
N/A 
Procedure 
Perform a Finite Element Analysis, or equivalent, of the snap fit to prove 
the capability of the design elements including: 
1 Adequate retention force. 
2 Acceptable ergonomic forces during vehicle assembly and disassembly for 
serviceability. 
3 Presence of compliance mechanisms to prevent rattles. 
4 Adequate design margin to ensure that flexing during vehicle installation 
does not exceed the elastic limit of the plastic. 
Requirement： 
Evidence that the design meets the four design elements stated in the procedure. 
 
5.10 M10 Crush Analysis 
Goal: This analysis shall identify structural weaknesses of the housing that could either lead 
to excessive stress to parts inside the component or to the housing itself. 
Test: 
Table 60: Test parameters for Crush Analysis 
Operating Type 
N/A 
Applicability 
All components where forces from elbow or foot loads from assembly or 
servicing are possible. This may include use as a supporting surface for 
other assembly operations. 
Monitoring 
N/A 
Procedure 
Use finite element analysis to insure that the requirements for crush test, 
as defined as a physical test, are met. The intended load must be identified 
as being produced by a person’s elbow or foot as described in the test portion 
for this concern. 
Requirement： 
Sufficient clearance between parts of the component and housing shall be demonstrated when 
the necessary forces are applied. The deflection of the component cover must not generate forces 
on parts within the component or on the circuit board. 
5.11 M11 Vibration Noise(Squeak & Rattle) 
According to Vibration Noise Test 
5.12 M12 Connector Test 


### 第 63 页
GB/T 2800001—2015 
59 
According to Connector Test 
6 Climatic Requirements And Tests 
Table 61 is the Climatic tests overview. 
Table 61: Climatic Tests Overview 
Test 
C1 High-/low-temperature storage 
C10 Salt spray test with operation, interior 
C2 Low-temperature operation 
C11 Salt spray test with operation, exterior 
C3 Incremental temperature test 
C12 Water protection  
C4 Repainting temperature 
C13 Water Freeze 
C5 Temperature shock (component、
Without Housing) 
C14 Condensation test with electr. Assemblies 
 
C6 Temperature shock with splash water C15 Dust 
C7 Temperature shock- immersion 
C16 Sun radiation 
C8 Humid heat, cyclic 
C17 Chemical requirements and tests 
C9 Humid heat,constant 
C18 Harmful gas test 
C19 High Altitude Operation 
Overheating Analysis 
C20 Thermal Fatigue Analysis 
C21 Lead-Free Solder Analysis 
 
6.1 C1 High-/low-temperature Storage 
Goal: This test simulates the thermal load of the component during storage and transport.The 
test serves to verify the resistance to storage at high or low temperatures, e.g.during the 
transport of the component (plane, ship container).If the test is carried out at the beginning 
of a test sequence, it also serves to adjust all components to the same initial conditions. 
Test: 
Table 62: Test parameters for C1 High-/low-temperature storage 
Operating Type 
1.1 
Test duration 
and test 
temperature 
2 cycles for 24h each (consisting of 12 h storage at Tmin and 12 h storage 
at Tmax each) 
Number of Test 
Samples 
As specified in the test sequence plan in the Component Performance 
Specifications 
Requirement： 
The DUT must be fully functional before and after the test,shall be meet Class A, and all 
parameters must meet the specifications. Verification takes place by means of 5-point test 
acc. to Section 3.3. 
6.2 C2 Low-temperature Operation 
Goal: This test simulates the load of the component at low temperatures.The test serves to 
verify functioning of the component after a long parking time or operation time at extremely 


### 第 64 页
GB/T 2800001—2015 
60 
low temperatures. 
Test: 
The test is carried out acc. to DIN EN 60068-2-1, test Ab, with the following parameters: 
Table 63: Test parameters for C2 Low-temperature operation 
Operating Type 
12 h of operating type 2.1 
12 h of operating type 2.3 at UBmin 
12 h of operating type 2.1 
12 h of operating type 2.3 at UB 
If several operating situations (see section 9.7) are relevant to the 
component in operating type 2.3, the component must be operated in all 
operating situations relevant to operating type 2.3 with equal time 
proportions in the time intervals in which operating type 2.3 is required 
in the test. 
Test duration 
48 h 
Test 
temperature 
Tmin 
Number of Test 
Samples 
3 
Note: For components dissipating heat, the test must also be carried out acc. to DIN EN 
60068-2-1, test Ab. 
Requirement： 
The DUT must be fully functional before, during and after the test,shall be meet Class 
A, and all parameters must meet the specifications. Verification takes place by means of 
continuous parameter monitoring and 1-point test. 
6.3 C3 Incremental Temperature Test 
Goal: This test simulates the operation of the component at different ambient temperatures.The 
test serves to verify the resistance of the component to malfunctions that may occur within 
a small interval of the operating temperature range. 
Test: 
Table 64: Test parameters for C3 Incremental temperature test 
Operating Type 
During the parameter test (functional test) operating type 2.3, otherwise 
operating type 2.1 
Test 
temperature 
A temperature profile acc. to Figure 35 must be applied to the Samples.The 
temperature change per increment is 5°C. 
Test sequence 
At every temperature increment, the Sample must be kept until it completely 
maintains the temperature (see Section3.3.4).Subsequently, a parameter 
test (functional test) acc. to the parameter test section (see Section 3.3.2) 
must be performed. 
Number of Test 
Samples 
3 


### 第 65 页
GB/T 2800001—2015 
61 
  
Figure 35: Temperature profile for incremental temperature test 
Requirement： 
All DUT parameters must meet the specifications during the parameter test (functional 
test). ,shall be meet Class A 
6.4 C4 Repainting Temperature 
Goal: This test simulates the load of the component during repainting.The test serves to verify 
the resistance of the component to faults that occur due to thermal load, e.g. cracking in 
soldered joints, adhesive joints and welded joints, in bond connections as well as in seals 
or housings. 
Test: 
Table 65: Test parameters for C4 Repainting temperature 
Operating Type 
2.1 
Test duration 
and test 
temperature 
Stay 15min at 130°C and stay 1h at 110°C 
 
Number of Test 
Samples 
3 
Requirement： 
The DUT must be fully functional before and after the test,shall be meet Class A, and all 
parameters must meet the specifications. Verification takes place by means of 1-point test  
acc. to Section 3.3. 
6.5 C5 Temperature Shock (component、Without Housing) 
Goal1（component）: This test simulates the thermal load of a component imposed through 
shock-type temperature changes during vehicle operation.The test serves to verify the 
resistance of the component to faults that occur due to thermal load, e.g. cracking in soldered 


### 第 66 页
GB/T 2800001—2015 
62 
joints, adhesive joints and welded joints, in bond connections as well as in seals or housings. 
Goal2（without housing）: This technology test does not simulate any real load.The test rather 
serves to detect weak spots in the field of mechanical joints on electr.assemblies, such as 
solder points.The test is to be performed exclusively with the electr.assembly of the component 
without housing and mechanical parts. 
Test: 
The test is carried out acc.to DIN EN 60068-2-14（component）and acc.to DIN EN 60068-2-14 
Na(without housing) with the following parameters: 
Table 66: Test parameters for C5 Temperature shock (component、Without Housing) 
Operating Type 
1.2(component)、1.1（without housing） 
Lower 
temperature/ 
Tmin 
Upper 
temperature 
Tmax 
Dwell time at 
upper/lower 
temperature 
15 min after the component has achieved the condition at which it maintains 
the temperature (see Section 3.3.4) 
Transfer 
duration(air - 
air, medium - 
medium) 
≤ 30s（component），≤ 10s（without housing） 
Test fluid for 
test Nc
（component） 
Fluid in which the component is operated in the vehicle 
Test（component） Acc.to DIN EN 60068-2-14NA for components that are not permanently operated 
in a fluid Acc.to DIN EN 60068-2-14Nc for components that are permanently 
operated in a fluid(IP X8 ISO20653). 
The DUT must be immersed so that all sides of the Sample are covered by at 
least 25 mm of the test fluid. 
Number of cycles 100（component），300（without housing） 
Number of Test 
Samples 
3 
Requirement： 
The DUT must be fully functional before and after the test ,shall be meet Class A, and 
all parameters must meet the specifications. Verification takes place by means of 5-point test 
acc.to Section 3.3. 
For medium - medium test additionally（component）: 
The fluid must not enter the Sample. The Sample must not be opened until after completion 
of the complete test sequence (chapter 9.4). 
6.6 C6 Temperature Shock With Splash Water 
Goal: This test shall verify the component’s functionality during and after exposure to sudden 


### 第 67 页
GB/T 2800001—2015 
63 
changes in temperature due to a water splash. 
Test: 
Table 67: Test parameters for C6 Temperature shock with splash water 
Operating Type 
Intermitting, acc. to Figure 36 
Applicability 
All components that are located in the area where a splash could occur while 
driving(e.g., low mounted on the engine). 
Test procedure 
Heating of the DUT to test temperature.Then cyclical exposure of the Sample 
with splash water acc.to Figure 36. The complete width of the Sample must 
be subjected to splash water. 
Cycle duration 
30 minutes 
Test 
temperature 
Tmax 
Test medium for 
splashing 
Tap water containing Arizona dust in a weight percentage of 3%, fine acc. 
to ISO 12103-1. Permanent mixing must be ensured. 
Splash water 
temperature 
0 to +4°C 
Splash nozzle 
0 to +4°C 
Splash nozzle 
See Figure 37 
Splashing time 
3s 
Water flow rate 3 to 4 liters per splash/nozzle 
Distance 
between 
nozzle and DUT 
300 to 350mm 
Number of cycles 100 
Number of Test 
Samples 
3 
When performing the test, the as-installed position of the component in the vehicle must 
be simulated.The test setup (as-installed position, covers, trims, situation during operation) 
must be recommended by the contractor, coordinated with the purchaser and documented.Test setup 
acc. to Figure 38 


### 第 68 页
GB/T 2800001—2015 
64 
 
Figure 36: Splash water test splashing times 
 
Figure 37: Splash water test - splash nozzle 


### 第 69 页
GB/T 2800001—2015 
65 
 
Figure 38: Splash water test setup 
 
Requirement： 
The ingress of water is not permissible. The sample must not be opened until after 
completion of the complete test sequence.The sample must be fully functional before, during 
and after the test ,shall be meet Class A, and all parameters must meet the specifications. 
Verification takes place by means of continuous parameter monitoring and 1-point test acc.to 
Section 3.3. 
6.7 C7 Temperature Shock- immersion 
Goal: This test simulates the load of the component when immersed into water.The test is to 
verify functioning of the component when subjected to immediate cooling by means of immersion 
of the heated component. 
Test: 
The test is carried out acc. to ISO 20653 with the following parameters: 
Table 68: Test parameters for C7 Temperature shock- immersion 
Operating Type 
If the component is operated with operating load during driving 
operation: 
2.3 in the "driving operation" operating situation 
If the component is not operated with operating load during 
driving operation: 
2.1 


### 第 70 页
GB/T 2800001—2015 
66 
Required degree 
of protection 
IP X7 
Test procedure 
Heating of the sample to test temperature.Maintaining test temperature 
until the sample completely maintains its temperature (see Section 3.3.4) 
plus 15 min.The sample is then completely immersed into the test medium 
within maximum five seconds so that all sides of the DUT are covered by at 
least 25mm with the test medium. 
Test medium 
0°C cold water with a salt content of 5% 
Immersion time 
5min 
Number of cycles 20 
Number of Test 
Samples 
3 
    If there is a coolant circuit, the coolant temperature must track the respective test 
temperature up to the limit Tcool,max. Only the ambient temperature is varied above the coolant 
temperature limit. 
Requirement： 
The ingress of water is not permissible. The sample must not be opened until after 
completion of the complete test sequence (chapter 8.2).The sample must be fully functional 
before, during and after the test ,shall be meet Class A,and all parameters must meet the 
specifications.Verification takes place by means of continuous parameter monitoring and 
1-point test acc.to Section 3.3. 
6.8 C8 Humid Heat, Cyclic 
Goal: This test shall verify the component’s immunity to internal condensation that may lead 
to electrical sneak paths as well as functional and material degradation. The breathing effect 
produced by changes in humidity, condensation resulting from rapid changes in temperature, 
and the expansion effect of freezing water in cracks and fissures are the essential features 
of this composite test. 
Test: 
Use test equipment according to IEC 60068-2-38, Test Z/AD, Composite temperature/humidity 
cyclic test： 
Table 69: Test parameters for C8 Humid heat, cyclic 
Operating Type 
3.2 with the component powered and cycled between ON (operated in such a 
manner to minimize power dissipation) and Sleep mode to avoid local drying. 
In case the component does not have Sleep capability, switch OFF instead. 
As a default, the component shall be cycled between ON for 1 h and Sleep/OFF 
for 1 h continuously during the 10 days test. 
A supply voltage of Unom shall be used. 
Total test 
duration 
a two days cycle that is to be repeated a total of five times (2 x 5 = 10 
days). 
Monitoring 
During the ON state, Continuous Monitoring is required. During the Sleep/OFF 
state, continuous parasitic current is monitored and recorded over the 10 


### 第 71 页
GB/T 2800001—2015 
67 
days test period to detect malfunctions during the test. 
Test 
temperature 
High Temperature +65℃;Middle Temperature +25℃;Low Temperature -10℃ 
Cyclic Humidity 
Profile 
Figure 39 
Applicability 
All components 
Number of Test 
Samples 
3 
Figure 39: Cyclic Humidity Profile  
   The humidity during high temperature is (93 ± 3) % and drops to 80 % during the transition 
from +65 ℃ to +25 ℃. At +25 ℃, the humidity shall increase to (93 ± 3) %. The humidity 
is uncontrolled when< +25 ℃. 
Requirement： 
Functional Status Classification shall be A. There shall be no increase greater than 50 % 
in parasitic current. An inspection shall show no signs of electro-migration or dendritic 
growth. 
6.9 C9 Humid Heat,Constant 
Goal: This test shall verify the component’s immunity to high humidity that may lead to 
functional and material degradation. 
Test: 
Use test equipment according to IEC 60068-2-78, Damp heat, steady state: 
Table 70: Test parameters for Constant humid heat - severity 1 
Operating Type 
3.2 with the component powered and cycled between ON (operated in such a 
manner to minimize power dissipation) and Sleep mode to avoid local drying. 


### 第 72 页
GB/T 2800001—2015 
68 
In case the component does not have Sleep capability, switch to OFF 
instead.As a default, the component shall be cycled between ON for 1 h and 
Sleep/OFF for 1 h continuously during the 10 days test. A supply voltage 
of Unom shall be used. 
Monitoring 
During the ON state, Continuous Monitoring is required. During the Sleep/OFF 
state, continuous parasitic current is monitored and recorded over the 10 
days test period to detect malfunctions during the test. 
Test 
temperature 
（+65±3）℃ 
Humidity 
（90±5）% 
Test duration 
10 days 
Applicability 
All components 
Number of Test 
Samples 
3 
Requirement： 
    Functional Status Classification shall be A. There shall be no increase greater than 50 % 
in parasitic current. An inspection shall show no signs of electro-migration or dendritic 
growth.  
6.10 C10 Salt Spray Test With Operation, Interior 
Goal: This test shall verify the component’s ability to withstand exposure to salt mist as 
experienced in coastal regions and salted road splash. DUTs shall be put in vechile position. 
Test: 
The test is carried out acc.to DIN EN 60068-2-11 Ka with the following parameters: 
Table 71: Test parameters for C10 Salt spray test with operation, interior 
Operating Type 
During spray phase: Intermitting 55min operating type 2.1 and 5min operating 
type 2.3 each.During rest phase: operating type 2.1 
Test cycle 
Each test cycle consists of a spray phase of 8h and a rest phase of 4h acc. 
to Figure 40. Rest phase shall be maintained in salt spray/mist chamber. 
Test 
temperature 
35°C 
Number of test 
cycles 
2 
Number of Test 
Samples 
3 
When performing the test, the as-installed position of the component in the vehicle must 
be simulated. The test setup (as-installed position, covers, trims, situation during operation) 
must be recommended by the contractor, coordinated with the purchaser and documented. 


### 第 73 页
GB/T 2800001—2015 
69 
 
Figure 40: Salt spray test with operation, interior - spray phases 
Requirement： 
The DUT must be fully functional before, during and after the test,shall be meet Class 
A, and all parameters must meet the specifications. Verification takes place by means of 
continuous parameter monitoring and 1-point test acc. to Section 3.3. 
6.11 C11 Salt Spray Test With Operation, Exterior 
Goal: This test shall verify the component’s ability to withstand exposure to salt spray as 
experienced in coastal regions and salted road splash. DUTs shall be put in vechile position. 
Test: 
The test is carried out acc. to DIN EN 60068-2-11 Ka with the following parameters: 
Table 72: Test parameters for C11 Salt spray test with operation, exterior 
Operating Type 
During spray phase: Intermitting 1h operating type 2.1 and 1h operating type 
2.3 each.During rest phase: Operating type 2.1 
Test cycle 
Each test cycle consists of a spray phase of 8 h and a rest phase of 4h acc. 
to Figure 41， Rest phase shall be maintained in salt spray/mist chamber. 
Test 
temperature 
35°C 
Number of test 
cycles 
For components on the underbody/in the engine compartment: 12 cycles.For 
other components: 8 cycles 
Number of Test 
Samples 
3 
When performing the test, the as-installed position of the component in the vehicle must 
be simulated. 


### 第 74 页
GB/T 2800001—2015 
70 
 
Figure 41: Salt spray test with operation, exterior - spray phases 
Requirement： 
The DUT must be fully functional before, during and after the test ,shall be meet Class 
A, and all parameters must meet the specifications. Verification takes place by means of 
continuous parameter monitoring and 1-point test acc. to Section 3.3. 
6.12 C12 Water Protection  
Goal: This test simulates the load of the component when subjected to water（≤IPX6K）or when 
subjected to water during vehicle cleaning（IPX9K）.The test serves to verify functioning of 
the component,e.g.when exposed to condensedwater, rain and spray water（≤IPX6K）or when exposed 
to high-pressure cleaning（IPX9K）. 
Test: 
The test is carried out acc.to ISO 20653 with the following parameters: 
Table 73: Test parameters for C12 Water protection 
Operating Type 
Intermitting 1min operating mode 2.1 and 1min operating mode 2.3 each
（≤IPX6K）；2.1（IPX9K） 
Water pressure
（IPX9K） 
The minimum pressure of the pressure washer is 10000 kPa (100 bar), measured 
directly at the nozzle. 
Water 
temperature
（IPX9K） 
80°C 
Procedure（IPX9K） The DUT must be subjected to the water jet from any freely accessible spatial 
direction of the vehicle. 
Number of Test 3 


### 第 75 页
GB/T 2800001—2015 
71 
Samples 
Requirement： 
The degree of protection specified in the Component Performance Specifications acc. to 
ISO 20653 must be achieved.The ingress of water is not permissible. The DUT must not be opened 
until after completion of the complete test sequence.The DUT must be fully functional before, 
during and after the test ,shall be meet Class A, and all parameters must meet the specifications. 
Verification takes place by means of continuous parameter monitoring and 1-point test acc. 
to Section 3.3. 
6.13 C13 Water Freeze 
Goal: This test shall verify component performance after water exposure followed by low 
temperature.This test simulates the effect of ice formation around the component. 
Test: 
Table 74: Test parameters for C13 Water Freeze 
Operating Type 
2.1、2.3 
Applicability 
All components that may allow water pooling on housings or mechanical 
linkages,with the exclusion of components located in the passenger 
compartment. 
Monitoring 
Continuous Monitoring during Operating type 2.3. 
Test Procedure 
If this test is applied to a component which provides mechanical movement, 
the test shall be set up in a simulated vehicle environment with all 
mechanical attachments in place. 
1、 Apply the Water test at Troom according to the IP Code in Operating Type 
2.3 as a default. 
2、 Within 5min, transfer the component to a cold chamber and soak at Tmin 
for 24h at Operating Type 2.1. 
3、At the end of 24h,and while still at Tmin, the component shall demonstrate 
proper function for 1h at Operating Type 2.3. 
4、 Repeat steps (1) to (3) five times. 
Number of Test 
Samples 
3 
Requirement： 
Functional Status Classification shall be A. 
 
6.14 C14 Condensation Test With Electr. Assemblies 
Goal: This test shall evaluate that the component is robust against rapid temperature change 
in a high humidity environment. This can produce intermittent failures and sneak path circuits 
that may affect function. 
Test: 
Table 75: Test parameters for C14 Condensation test with electr. Assemblies 
Operating Type 
1.2 during cold phase; 3.2 during exposure to high humidity. 


### 第 76 页
GB/T 2800001—2015 
72 
Monitoring 
Continuous Monitoring during the time it is energized. 
Applicability 
All sealed or closed components 
Procedure 
Use the test methods according to IEC 60068-2-30, Test Db, Damp heat, cyclic, 
with the temperature and humidity profile defined in Figure 42. 
Number of Test 
Samples 
3 
 
 
Figure 42: Frost Test Profile 
Reference to Table 76 for the number of cycles performed. 
 
Table 76: Frost Test Requirements 
Component Type 
Number of cycles performed 
Sealed components with or without a pressure 
exchange membrane 
10 
Non-Sealed components without vent openings 1 
Non-Sealed components with vent openings 
0 
 
Requirement： 


### 第 77 页
GB/T 2800001—2015 
73 
Functional Status Classification shall be A. An inspection shall show no signs of 
electro-migration and dendritic growth. 
6.15 C15 Dust 
Goal: This test shall verify that the component’s enclosure sufficiently protects against dust 
intrusion. This can be from windblown sand, road dust, or other dust types. The accumulation 
of dust affects heat dissipation and electro-mechanical components. 
Test: 
Table 77: Test parameters for C15 Dust 
Operating Type 
For electrical/electronic components: operating type 2.1 
For mechatronic components 
(e.g., for components with fans): 
Operating type 2.3 and operating type 2.1 intermittently as per Figure 615. 
If several operating situations (see section 9.7) are relevant to the 
component in operating type 2.3, the component must be operated in all 
operating situations relevant to operating type 2.3 with equal time 
proportions in the time intervals in which operating type 2.3 is required 
in the test. 
Applicability 
All components  
Monitoring 
N/A 
Test Procedure 
1.Use the test methods according to ISO 20653:2006,see figure 43 
2.Perform the 1-Point Functional/Parametric Check. 
Degree of 
protection to be 
achieved 
As specified in the Component Performance Specification 
Test duration 
20 cycles of 20 minutes each 
Number of Test 
Samples 
6 
 
Figure 43: Test sequence, C15 Dust test 
   When performing the test, the as-installed position of the component in the vehicle must 
be simulated. The test setup (as-installed position, covers, trims, situation during operation) 
must be recommended by the contractor, coordinated with the purchaser and documented. 
Requirement： 
   The degree of protection specified in the Component Performance Specification as per ISO 
20653 must be achieved. 
   The DUT must be fully functional before, during and after the test, and all parameters must 


### 第 78 页
GB/T 2800001—2015 
74 
meet the specifications.  
   In addition, the DUT must be evaluated visually with the naked eye and tested for loose 
or rattling parts by means of shaking. 
   Changes and damage must be documented in the test report and evaluated with the purchaser. 
 
6.16 C16 Sun Radiation 
Goal: This test simulates the influence of sun radiation and UV light onto the component.The 
test serves to verify the resistance of the component to damages caused by material fatigue, 
such as cracks or discolorations. 
Test: 
The test is carried out acc.to DIN 75220 with the following parameters: 
Table 78: Test parameters for C16 Sun radiation 
Operating Type 
1.1 
Used test 
profiles 
The test profiles acc. to DIN 75220 must be used depending on the 
installation location of the component. 
Components in 
the exterior 
Use of the Z-OUT profile acc. to Table 2 and Table 5 of DIN 75220 
Components in 
the interior 
Use of the Z-IN profile acc.to DIN 75220 
Test duration 
25 days (15 days dry, 10 days humid) 
Number of cycles 1 
Number of Test 
Samples 
3 
Requirement： 
The sample must be fully functional before and after the test ,shall be meet Class A, and 
all parameters must meet the specifications.Verification takes place by means of 5-point test 
acc.to Section 3.3. 
In addition, the DUT must be evaluated visually with the naked eye.Changes or damages must 
be documented in the test report and evaluated with the purchaser. 
6.17 C17 Chemical Requirements And Tests 
Goal: This test simulates the load of the component when subjected to different chemicals.The 
test serves to verify the resistance of the component to chemical changes on the housing and 
impairment of functioning due to chemical reactions. 
Test: 
Table 79: Test parameters for C17 Chemical requirements and tests 
Operating Type 
As specified in the Component Performance Specifications 
Chemicals 
As specified in the Component PerformanceSpecifications.Typical chemicals 
for different installation locations are indicated in Table 80，and the 
supply must advance application method（according to table 81） for per 
position in ADV plan) 


### 第 79 页
GB/T 2800001—2015 
75 
Conditioning 
Unless otherwise specified, the samples and the chemicals must be aged in 
standard climate. 
Test procedure 
1. The chemical must be applied to the sample at Troom. Unless otherwise 
defined in the Component Performance Specifications, an appropriate type 
of application must be selected for each chemical acc. to Table 81. The 
selected type of application must be documented in the test report. It must 
be ensured that the sample is sufficiently covered with the chemical. 
2. The sample must then be aged at the temperature indicated in Table 80 
for the specified exposure time. 
Test duration 
According to table 80 
Number of Test 
Samples 
1 sample per chemical 
Multiuse of a DUT for several chemicals is permitted in coordination with 
the purchaser 
Note: Safety instructions and warnings for the chemicals must be observed. 
Table 80: Overview of chemicals 
ID 
Chemical agent 
Sample 
temperature 
Exposure 
time 
Sample chemicals 
1 
Diesel 
Tmax 
22 h 
EN 590 
2 
FAME 
Tmax 
22 h 
EN 14214 
3 
Gasoline, unleaded 
Troom 
10 min 
EN 228 
4 
Kerosine 
Troom 
10 min 
ASTM 1655 
5 
Methanol 
Troom 
10 min 
CAS 67-56-1 
6 
Engine oil 
Tmax 
22 h 
Multigrade oil SAE 0W40, API 
SL/CF 
7 
Differential oil 
Tmax 
22 h 
Hypoid gear oil SAE 75W140, 
API GL-5 
8 
Gearbox oil 
Tmax 
22 h 
ATF Dexron III 
9 
Hydraulic fluid 
Tmax 
22 h 
DIN 51 524-3 (HVLP ISO VG 46) 
10 
Grease 
Tmax 
22 h 
DIN 51 502 (KP2K-30) 
11 
Silicone oil 
Tmax 
22 h 
CAS 63148-58-3 (AP 100) 
12 
Battery acid 
Troom 
22 h 
37% H2SO4 
13 
Brake fluid 
Tmax 
22 h 
ISO 4926 
14 
Antifreeze agent 
Tmax 
22 h 
Ethylene glycol (C2H6O2) - 
water (mixture ratio 1:1) 
15 
Urea 
Tmax 
22 h 
ISO 22241-1 
16 
Cavity sealing medium 
Troom 
22 h 
e.g. underbody protection, by 
Teroson1 
17 
Preservative 
Troom 
22 h 
e.g. W550 (by Pfinder) 1 
18 
Preservative remover 
Tmax 
22 h 
e.g. Friapol 750 (by Pfinder) 
1 
19 
Windshield cleaner 
Troom 
2 h 
5% anionic tensides, 
distilled water 


### 第 80 页
GB/T 2800001—2015 
76 
20 
Automotive washing 
chemicals 
Troom 
2 h 
CAS 25155-30-0 
CAS 9004-82-4 
21 
Interior cleaner/cockpit 
spray 
Troom 
2 h 
e.g. Cockpit-spray (by Motip) 
1 
22 
Glass cleaner 
Troom 
2 h 
CAS 111-76-2 
23 
Rim cleaner 
Troom 
2 h 
e.g. Xtreme (Sonax) 1 
24 
Cold-cleaning agent 
Troom 
22 h 
e.g. P3-Solvclean AK (by 
Henkel) 1 
25 
Acetone 
Troom 
10 min 
CAS 67-64-1 
26 
Petroleum ether 
Troom 
10 min 
DIN 51 635 
27 
Cleaner containing ammonia Troom 
22 h 
e.g. Ajax (by Henkel) 1 
28 
Methylated spirits 
Troom 
10 min 
CAS 64-17-5 (ethanol) 
29 
Contact spray 
Tmax 
22 h 
e.g. WD 40 1 
30 
Sweat 
Troom 
22 h 
DIN 53 160 
31 
Cosmetic products, e.g. 
creams 
Troom 
22 h 
e.g. Nivea, Kenzo 1 
32 
Refreshment containing 
caffeine and sugar 
Troom 
22 h 
Cola 
33 
De-icer (aviation) 
Troom 
2 h 
SAE AMS 1435A 
 
Table 81: Types of application 
Code number 
Application method 
1 
Spraying 
2 
Painting 
3 
Wiping (e.g. with cotton cloth) 
4 
Dousing 
5 
Short immersion 
6 
Immersion 
Requirement： 
The DUT must be fully functional before and after the test ,shall be meet Class A, and 
all parameters must meet the specifications. Verification takes place by means of 5-piont test 
acc. to Section 3.3.2.Changes to letterings or markings must be documented in the test report 
and coordinated with the purchaser. 
6.18 C18 Harmful Gas Test 
Goal: This test simulates the influence of harmful gases onto the component, particularly onto 
its plug contacts and switches.The test serves to verify the resistance of the part to faults, 
such as corrosion and component damages. 
Test: 
The test is carried out acc. to DIN EN 60068-2-60, method 4, with the following parameters: 
Table 82: Test parameters for C18 Harmful gas test 
Operating Type 
1.2 


### 第 81 页
GB/T 2800001—2015 
77 
Temperature 
Troom 
Humidity 
75% 
Harmful gas 
concentration 
SO2 
0.2ppm 
H2S 
0.01ppm 
NO2 
0.2ppm 
Cl2 
0.01ppm 
Test duration 
21days  
Number of Test 
Samples 
6 
Requirement： 
The sample must be fully functional before and after the test ,shall be meet Class A, and 
all parameters must meet the specifications. Verification takes place by means of 5-piont test 
acc. to Section 3.3.2.In addition, the contact resistances of switches and contacts must be 
measured. The measured values must meet the specifications. 
6.19 C19 High Altitude Operation Overheating Analysis 
Goal: This analysis shall identify cooling weaknesses that may lead to overheating on parts 
as a result of low air density at high altitude.High altitude analysis is to be performed on 
all components that contain significant heat generating elements on their circuit board and 
are cooled by air flow(i.e., convection). The reduced air density at high altitude will reduce 
convective heat transfer and may cause marginal designs to overheat while operating within 
the vehicle. 
Test: 
Table 83: Test parameters for High Altitude Operation Overheating Analysis 
Operating Type 
N/A 
Applicability 
All components where heat dissipation may be reduced due to low air density 
caused by high altitudes 
Monitoring 
N/A 
Procedure 
Use the following equation to determine the maximum component operating 
temperature at high altitude: 
Tmax_part Taltitude = Tpart_oper x Multiplieraltitude +Tambient+10℃ 
• Tmax_part: Maximum allowable temperature from part data sheet. 
• Taltitude:Calculated operating temperature of part at altitude. 
• Tambient: Ambient temperature at altitude. Use +35℃ as the default value. 
• Tpart_oper: Temperature increase of part due to operation (this is the 
temperature difference of the component when the part is at sea level). 
• Multiplieraltitude: The Multiplier value is based on altitude and air 
flow. The multipliers as noted in Table 85 are used to adjust the temperature 
rise for high altitude effects in the equation. 
• +10℃: This is the safety margin. 
 
 


### 第 82 页
GB/T 2800001—2015 
78 
Table 84: Parameters for Overheating Analysis 
Altitude 
Multiplier 
Fan Cooled(Low Power 
Fan) 
Fan Cooled (High Power 
Fan) 
Naturally Cooled 
Convection(No Fan) 
0 m 
1 
1 
1 
4572 m 
1.77 
1.58 
1.33 
Note 
The bolded value of (1.33) will be the most frequently used value in FM 
calculations. 
Requirement： 
Tmax_part must be greater than or equal to Taltitude. Tmax_part is based on the operating 
specifications for the part generating heat and other affected parts nearby. 
6.20 C20 Thermal Fatigue Analysis 
Goal: This analysis shall identify thermal fatigue weaknesses caused by cyclic temperature 
change when materials with different Coefficients of Thermal Expansion (CTE) are attached to 
each other. For example, the different CTEs of circuit board parts result in fatigue stress 
to the junctions used to attach those parts to the circuit board (e.g., solder and lead wires). 
The expansion rates of different materials added to a circuit board assembly (e.g., potting 
materials) may also result in the unacceptable deformation of the junctions and/or the 
structure of the component resulting in electrical or mechanical problems. 
Test: 
Table 85: Test parameters for Thermal Fatigue Analysis 
Operating Type 
N/A 
Applicability 
All components  
Monitoring 
N/A 
Procedure 
Identify the package types of all the parts of the component.Identify the 
Coefficient of Thermal Expansion (CTE) of each package type, and all 
variants within the package type, of the parts of the component.Identify 
the differences of the CTEs for each package type of the parts and for the 
part to which they are attached.Identify the analysis method used to 
determine the fatigue life and provide the evidence of the validation for 
that analysis method.Perform the analysis to quantify fatigue life of the 
part junctions from the expansion and contraction during temperature 
cycling of the component. 
Requirement： 
The calculated fatigue life shall be three times greater than the required life of the 
component. 
6.21 C21 Lead-Free Solder Analysis 
Goal: This analysis shall identify solder joint weaknesses that may occur due to the use of 
lead-free solder. This analysis supports the Hardware Design Review to evaluate the soldering 
process 


### 第 83 页
GB/T 2800001—2015 
79 
Test: 
Table 86: Test parameters for Lead-Free Solder Analysis 
Operating Type 
N/A 
Applicability 
All components manufactured with lead-free solder. 
Monitoring 
N/A 
Procedure 
Use the Appendix A (Lead-Free Solder Considerations) as a reference to 
discuss potential impacts to the product design and manufacturing process. 
Use this as a basis to develop the Design Review Based on Failure Modes 
(DRBFM).Provide full disclosure throughout the supply chain regarding risks 
when lead-free solder is used. All risks are to be addressed and mitigated 
through the design review and DRBFM process.Provide the DRBFM to FM. 
Requirement： 
The analysis shall show evidence that the lead-free solder effects are reviewed and 
adjustments made to the test plans, design, and process. The part’s data sheets shall support 
all lead-free solder processes. 
7 Service-life tests 
Table 87 shows the typical parameters for the service life requirements. 
Table 87: Service life requirements 
Service life 
Common components:10 years;Key components:12 years 
Operating hours in driving operation 5400 h 
Mileage 
200000 km 
The test duration results from the required number of temperature cycles in the test. 
using: 
 
where 
N1 test is the required number of temperature cycles in the test with in-practice temperature 
rise; 
Nprac is the number of temperature cycles occurring in practice: 7 300 (in 10 years); 
R is the survival probability: 0.9 (specification); 
PA is the confidence level (assumption): 0.75; 
β is the Weibull form factor: 2.0 (determined in experiment, for wire fracture); 
n is the number of DUT: 6 (small and simple DUT). 
7.1  L-01 Life test - mechanical/hydraulic endurance test 
Goal: This test simulates the functioning/actuation cycles of the component during vehicle 
service life.The test serves to verify the quality and reliability of the component with respect 
to functioning/actuation cycles, e.g. brake actuation, seat adjustment cycles, switch/tip 


### 第 84 页
GB/T 2800001—2015 
80 
switch actuation. 
Test: Test details must be defined in the Component Performance Specification as per the 
functioning/actuation cycle. 
Table 88: Test parameters, L-01 Life test - mechanical/hydraulic endurance test 
Operating Type 
Operating type 2.3 as per functioning/actuation cycle 
Test temperature 
The functioning/actuation cycles must be performed at the temperatures 
indicated in the temperature spectrum, the duration depending on its 
percentage. 
Number of 
functional/actuation 
cycles 
As specified in the Component Performance Specification 
Number of Test 
Samples 
6 
    If there is a coolant circuit, the coolant temperature must track the respective test 
temperature to the limits Tcool,min and Tcool,max. Only the ambient temperature is varied 
outside of the coolant temperature limits. 
Requirement： 
    The DUT must be fully functional before, during and after the test, and all key parameters 
must meet the specifications. This must be verified by means of continuous parameter monitoring. 
Intermediate measurements at 25%, 50%, and 75% of the test duration and parameter tests as 
per the test sequence plan must only be carried out if the functioning of the component cannot 
be sufficiently monitored during the test. 
    The intermediate measurements must be carried out as parameter test (large). 
    The data of the continuous parameter monitoring must be examined with respect to drifts, 
trends and irregularities. 
    For components on coolant circuits: 
    For components with coated copper parts in the coolant path, these copper parts must be 
examined with a stereo microscope at 20x magnification after the test. Flaws and copper 
corrosion that can be detected in the examination are not permissible. 
7.2  L-02 Life test – high-temperature endurance test 
Goal: This test simulates in compact form the thermal load of the component during vehicle 
service life. 
     The test serves to verify the quality and reliability of the component with respect to 
faults that occur due to thermal load, e.g diffusion, migration and oxidation. 
Test:  
7.2.1 Test for components without a connection to the coolant circuit without reduced 
performance at high temperatures 
   The test is carried out as per DIN EN 60068-2-2 with the following parameters: 
Table 89: Test parameters, L-02 Life test – high-temperature endurance test – Test for 
components without a connection to the coolant circuit without reduced performance at high 


### 第 85 页
GB/T 2800001—2015 
81 
temperatures 
Operating 
Type 
Intermitting, 47 h operating type 2.3 an 1 h Operating type 2.1 
Test duration For each relevant operating situation as per section 9.7, the partial test 
duration must be calculated as per appendix SECTION 9.5.1 (Arrhenius model); 
the "parking" and "off-grid parking" operating situations generally must not 
be taken into account. The total test duration is the sum of all partial test 
durations and is specified in the Component Performance Specification. 
Test 
temperature 
Tmax 
Number of Test 
Samples 
6 
7.2.2 Test for components without a connection to the coolant circuit with reduced performance 
at high temperatures 
For components with reduced performance (e.g., reduction of background lighting of LCDs) 
at high temperatures from Top,max, the test must not be performed at a constant test temperature 
of Tmax as per Table 90, but rather with a temperature profile with the following parameters: 
The test is carried out as per DIN EN 60068-2-2 with the following parameters:  
Table 90: Test parameters, L-02 Life test – high-temperature endurance test – Test for 
components without a connection to the coolant circuit with reduced performance at high 
temperatures 
Operating 
Type 
As per Figure 44 
Test duration For each relevant operating situation as per section 9.7, the partial test 
duration must be calculated as per appendix SECTION 9.5.2 (Arrhenius model 
for use with components with reduced performance at high temperatures); the 
"parking" and "off-grid parking" operating situations generally must not be 
taken into account. 
The total test duration is the sum of all partial test durations and is 
specified in the Component Performance Specification. The respective ramp 
times between Tmax and Top,max are not included in the test duration. 
Test 
temperature 
As per Figure 44 
Interval time 
t1 
Must be calculated as per appendix SECTION 9.5.2 and specified in the Component 
Performance Specification. 
Interval time 
t2 
Must be calculated as per appendix SECTION 9.5.2 and specified in the Component 
Performance Specification. 
Number of Test 
Samples 
6 


### 第 86 页
GB/T 2800001—2015 
82 
 
Figure 44: Temperature profile for testing components with reduced performance at high 
temperatures 
7.2.3 Test for components with a connection to a coolant circuit 
The test is carried out as per DIN EN 60068-2-2 with the following parameters: 
Table 91: Test parameters, L-02 Life test – high-temperature endurance test – Test for 
components with a connection to a coolant circuit 
Operating Type 
Intermittently 47 h operating type 2.3 and 1 h operating type 2.1 
Test duration 
For each relevant operating situation as per section 9.7, the 
partial test duration must be calculated as per appendix SECTION 9.5.3 
(Arrhenius model for use with components on coolant circuits); 
the "parking" and "off-grid parking" operating situations 
generally must not be taken into account. 
The total test duration is the sum of all partial test durations 
and is specified in the Component Performance Specification. 
Test temperature 
Environment 
As per appendix SECTION 9.5.3 (Arrhenius model for use with 
components on coolant circuits) 
Test temperature for 
coolant 
As per appendix SECTION 9.5.3 (Arrhenius model for use with 
components on coolant circuits) 
Number of Test Samples 6 
Requirement： 
     The DUT must be fully functional before, during and after the test, and all key parameters 
must meet the specifications. This must be verified by means of continuous parameter monitoring. 


### 第 87 页
GB/T 2800001—2015 
83 
Intermediate measurements at 25%, 50%, and 75% of the test duration and parameter tests as 
per the test sequence plan must only be carried out if the functioning of the component cannot 
be sufficiently monitored during the test. 
     The intermediate measurements must be carried out as parameter test (large). 
     The data of the continuous parameter monitoring must be examined with respect to drifts, 
trends and irregularities. 
     For components on coolant circuits: 
     For components with coated copper parts in the coolant path, these copper parts must be 
examined with a stereo microscope at 20x magnification after the test. Flaws and copper 
corrosion that can be detected in the examination are not permissible. 
7.3  L-03 Life test - temperature cycle test 
Goal: This test simulates in compact form the thermal load of the component during temperature 
changes that occur during vehicle service life. 
     The test serves to verify the quality and reliability of the component with respect to 
faults that occur due to thermomechanical load, e.g. aging and cracking in soldered joints, 
adhesive joints and welded joints, in bond connections as well as in seals or housings. 
Test:The test is carried out as per DIN EN 60068-2-14 with the following parameters: 
7.3.1 Test for components without a connection to the coolant circuit without reduced 
performance at low or high temperatures 
Table 92: Test parameters, L-03 Life test - temperature cycle test – Test for components 
without a connection to the coolant circuit without reduced performance at low or high 
temperatures 
Operating Type 
Intermitting operating type 2.3 and operating type 2.1 as per Figure 45. 
Temperature 
profile 
As per Figure 45 
Minimum 
test 
temperature 
Tmin 
Maximum 
test 
temperature 
Tmax 
Temperature 
gradient 
4 °C/min 
If the temperature gradient cannot be produced by the testing device, it can 
be reduced to values up to a minimum of 2 °C/min in coordination with the 
purchaser. 
Holding time at 
Tmin and Tmax 
15 min after the component has achieved the condition at which it maintains 
the temperature 
Number 
of 
cycles 
The total number of test cycles must be calculated while taking into account 
all relevant operating situations (section 9.7) as per appendix SECTION 9.6.1 
(Coffin-Manson model) and specified in the Component Performance 
Specification. 
Number of Test 6 


### 第 88 页
GB/T 2800001—2015 
84 
Samples 
 
Figure 45: Temperature profile, L-03 Life test - temperature cycle test, for components without 
reduced performance at low or high temperatures 
7.3.2 Test for components without a connection to the coolant circuit with reduced performance 
at low or high temperatures 
    For components with reduced performance (e.g., reduction of background lighting of LCDs) 
at low or high temperatures below Top,min and above Top,max, the test must be performed with the 
following parameters: 
Table 93: Test parameters, L-03 Life test - temperature cycle test – Test for components 
without a connection to the coolant circuit with reduced performance at low or high temperatures 
Operating Type 
Operating type 2.1 and operating type 2.3 as per Figure 46. 
Temperature profile 
As per Figure 46 
Minimum 
test 
temperature 
Tmin 
Maximum 
test 
temperature 
Tmax 
Temperature gradient 4 °C/min 
Holding time at Tmin , 
Tmax  , Top,min  and Top,max 
15 min after the component has achieved the condition at which it 
maintains the temperature 
Number of cycles 
The total number of test cycles must be calculated while taking into 
account all relevant operating situations (section 9.7) as per appendix 
SECTION 9.6.1 (Coffin-Manson model) and 
specified in the Component Performance Specification. 
Number 
of 
Test 
Samples 
6 


### 第 89 页
GB/T 2800001—2015 
85 
 
Figure 46: Temperature profile – test for components with reduced performance at low or high 
temperatures 
7.3.3 Test for components on the coolant circuit 
    For components on coolant circuits, the test must be performed with the following 
parameters: 
Table 94: Test parameters, L-03 Life test - temperature cycle test – Test for components on 
the coolant circuit 
Operating Type 
Operating type 2.1 and operating type 2.3 as per Figure 45 or Figure 46 
Temperature 
profile 
As per Figure 45 or Figure 46 
Minimum 
test 
temperature 
Tmin and Tcool,min 
Maximum 
test 
temperature 
Tmax and Tcool,max 
Temperature 
gradient 
4 °C/min 
Holding time at 
Tmin , Tmax  , Top,min  
and Top,max 
15 min after the component has achieved the condition at which it maintains 
the temperature 
Number of cycles 
The total number of test cycles must be calculated while taking into 
account all relevant operating situations (section 9.7) as per appendix 
SECTION 9.6.2 (Coffin-Manson model for use with components on coolant 
circuits) and specified in the Component Performance Specification. 
Number of Test 
Samples 
6 
Requirement： 
    The DUT must be fully functional before, during and after the test, and all key parameters 


### 第 90 页
GB/T 2800001—2015 
86 
must meet the specifications. This must be verified by means of continuous parameter monitoring. 
Intermediate measurements at 25%, 50%, and 75% of the test duration and parameter tests as 
per the test sequence plan must only be carried out if the functioning of the component cannot 
be sufficiently monitored during the test. 
    The intermediate measurements must be carried out as parameter test (large). 
    The data of the continuous parameter monitoring must be examined with respect to drifts, 
trends and irregularities. 
    For components on coolant circuits: 
    For components with coated copper parts in the coolant path, these copper parts must be 
examined with a stereo microscope at 20x magnification after the test. Flaws and copper 
corrosion that can be detected in the examination are not permissible. 
8 Analysis 
8.1 Analysis Mission 
The Analysis shall be used to aid in designing reliability into the component during the 
time when physical components are not yet available. Analysis should be the earliest activity 
in the A/D/V process and provides the earliest component design learning and improvement 
opportunity. All Analytical activities shall be documented in the Component Environmental Test 
Plan, including results. Analytical activities shall be completed prior to the design freeze 
for building development level hardware. 
8.2 Development Mission 
Development activities are designed to detect weaknesses or design oversights that were 
not comprehended, or could not be evaluated, during Analysis activities. The Development 
activities shall be performed on first samples to provide the earliest opportunity to 
qualitatively evaluate and improve physical components. HALT is a typical example of this type 
of activity.Weaknesses detected during Development activities shall be corrected prior to 
validation. All Development activities shall be documented in the Component Environmental Test 
Plan, including results. 
 
8.3 Design Validation (DV) Mission 
The Design Validation (DV) shall be a quantitative and qualitative verification that the 
component design meets the requirements for Environmental,Durability, and Reliability. The 
DV activities shall be executed on production design-intent components with production-intent 
parts, materials, and processes(including solder and fluxes). All DV activities shall be 
documented in the Component Environmental Test Plan, including results. 
8.4 Product Validation (PV)  
8.4.1 Mission 


### 第 91 页
GB/T 2800001—2015 
87 
The Product Validation (PV) shall be a quantitative and qualitative verification that the 
component meets the requirements for Environmental, Durability, and Reliability when including 
the effects of production manufacturing. The PV activities shall be executed using components 
from the production tooling and processes. All PV activities shall be documented in the 
Component Environmental Test Plan, including results. 
8.4.2 Shipping Vibration 
Goal: This test shall verify the robustness of the component together with the packaging during 
shipping. 
Test: 
Table 95: Test parameters for shipping vibration 
Operating Type 
1.1 
Applicabil
ity 
All components with surfaces visible to customers. 
Monitoring N/A 
Procedure 
Use a vibration test fixture that allows the shipping container (one box 
of components) to move freely in the vertical axis of the vibration table.One 
possible fixture consists of a base plate with four upright posts that are 
slightly larger than the shipping container.Provisions shall be made to 
allow placement of the shipping container in all three directions. 
1、Vibrate the shipping container for 24 h in each of the three mutually 
perpendicular directions using the methods outlined in ASTM D4728, Standard 
Test Method for Random Vibration Testing of Shipping Containers. This test 
is to be conducted on one box of components in their final shipping 
container.The shipping container shall include all interior packing 
material. Use the Random Vibration Profile shown in Table 96 and Figure 47. 
2、Inspect all components for structural damage and degradation of 
surfaces visible to customers. 
3、Select components (3 samples as a default) from the highest stress 
area of the shipping container to perform the 5-Point Functional/Parametric 
Check. 
Table 96: Random Vibration Profile for Shipping 
Frequency 
Power Spectral Density 
10 Hz 
0.096000 (m/s2)2/Hz = 0.00100G2/Hz 
12 Hz 
0.962000 (m/s2)2/Hz = 0.01002G2/Hz 
100 Hz 
0.962000 (m/s2)2/Hz = 0.01002G2/Hz 
200 Hz 
0.000962 (m/s2)2/Hz = 0.00001G2/Hz 
300 Hz 
0.000962 (m/s2)2/Hz = 0.00001G2/Hz 
 


### 第 92 页
GB/T 2800001—2015 
88 
 
Effective Acceleration = 9.81 m/s2 = 1.0 GRMS 
Figure 47: Random Vibration Profile for Shipping 
Requirement： 
Functional Status Classification shall be C. There shall be no visible damage to any of 
the components. 
9 Appendix 
9.1 Installation Area  
An entry shall be made on the FOTON drawing in the space allocated for the reference 
standards. The drawing entry shall include the installation area classification code (referred 
to Table 97) as shown. 
Any significant changes to the installation area of a component will entail repetition 
of at least some of the tests because the environmental parameters will be different. 
 
 
 
 
 
 
Table 97 Installation Area Classification Codes, Temperature Ranges and Applicable Tests 
                                                                   
A Vehicle interior Passenger compartment 


### 第 93 页
GB/T 2800001—2015 
89 
Part 
assembled 
area 
 
Test Item                                                       
A1 
A2 
A3 
A4 
A4a 
A4b 
Without particular 
requirement 
Direct Solar Radiation 
With Exposure to Heat 
Radiation (e.g. heater) 
Passenger Compartment 
Door 
Attached to passenger 
Compartment side 
Attached to door cavity 
side 
Normal operating Temp Range ℃  -40 to +80 
-40 to 
+ 105 
-40 to 
+90 
-40 to 
+80 
-40 to 
+80 
-40 to 
+80 
Normal  storage Temp Range ℃ 
-40 to +90 
-40 to 
+115 
-40 to 
+120 
-40 to 
+100 
-40 to 
+100 
-40 to 
+100 
M1 Vibration test 
√ 
M2 Mechanical shock 
√ 
M3 Endurance shock test 
Severity dependent on mounting position 
M4 Crush For Housing-Elbow Load 
Severity dependent on mounting position 
M5 Crush For Housing-Foot Load 
Severity dependent on mounting position 
M6 Free fall 
√ 
C1 High-/low-temperature 
storage 
√ 
C2 Low-temperature operation 
√ 
C3 Incremental temperature test 
√ 
C4 Repainting temperature 
√ 
C5 Temperature shock 
(component、Without Housing) 
√ 
C6 Temperature shock with 
splash water 
Severity dependent on mounting position 
C7 Temperature shock- immersion              Severity dependent on mounting position 
C8 Humid heat, cyclic 
√ 
C9 Humid heat,constant 
√ 
C10 Salt spray test with 
operation, interior 
√ 
C11 Salt spray test with 
operation, exterior 
Severity dependent on mounting position 
C12 Water protection  
Severity dependent on mounting position 


### 第 94 页
GB/T 2800001—2015 
90 
C13 Water Freeze 
Severity dependent on mounting position 
C14 Condensation test with 
electr. assemblies 
             Severity dependent on mounting position 
C15 Dust 
IP5KX 
C16 Sun radiation 
Severity dependent on mounting position 
C17 Chemical requirements and 
tests 
√ 
C18 Harmful gas test 
√ 
Life endurance Test 
√ 
 
 
                                                                      
Part assembled 
area 
 
 
 
Test Item                                                      
B Engine Compartment 
B1 
B2 
B3 
B4 
B5 
Attached 
to body 
Attached 
to intake 
Manifold 
On / in 
the 
Engine 
On/in the 
Transmiss
ion/ 
Retarder 
On / in 
the 
Radiator 
Normal operating Temp Range ℃  -40 to 
+ 105 
-40 to 
+ 105 
-40 to 
+ 140 
-40 to  
+ 140 
-40 to 
+120 
Normal  storage Temp Range ℃ 
-40 to 
+120 
-40 to 
+120 
-40 to 
+140 
-40 to 
+140 
-40 to 
+140 
M1 Vibration test 
8 h/axis 
24 hours/axis 
8 h/axis 
M2 Mechanical shock 
√ 
M3 Endurance shock test 
√ 
M4 Crush For Housing-Elbow Load 
√ 
M5 Crush For Housing-Foot Load 
√ 
M6 Free fall 
√ 
C1 High-/low-temperature 
storage 
√ 
C2 Low-temperature operation 
 √ 
C3 Incremental temperature 
test 
√ 
C4 Repainting temperature 
---- 
C5 Temperature shock 
(component、Without Housing) 
100 cycles 


### 第 95 页
GB/T 2800001—2015 
91 
C6 Temperature shock with 
splash water 
100 
cycles 
--- 
100 cycles 
C7 Temperature shock- 
immersion 
√ 
C8 Humid heat, cyclic 
6 cycles 
C9 Humid heat,constant 
21 days 
C10 Salt spray test with 
operation, interior 
-- 
C11 Salt spray test with 
operation, exterior 
√ 
C12 Water protection  
IPX4, 48 cycles 
C13 Water Freeze 
√ 
C14 Condensation test with 
electr. assemblies 
Severity dependent on mounting position 
C15 Dust 
IP6KX 
C16 Sun radiation 
√ 
C17 Chemical requirements and 
tests 
√ 
C18 Harmful gas test 
21 days 
Life endurance Test 
√ 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
                                                                                                  
Part 
C Vehicle Exterior Body 
C1 
C2 
C3 
C4 
C5 
C6 
C7 
C8 


### 第 96 页
GB/T 2800001—2015 
92 
assembled 
area 
 
 
Test Item                                                       
On the Body 
Under body / wheel 
houses 
  wheel / Axle 
Other installation 
Area 
Engine Compartment 
Bonnet 
Boot Lid 
Tail Gate 
Between A and C 
pillar’s 
Normal operating Temp 
Range ℃  
-40 
to 
+80 
-40 to 
+ 80 
 
-40 
to 
+80 
-40 to 
+80 
-40 to 
+120 
-40 
to 
+80 
-40 
to 
+80 
-40 to 
+80 
Normal  storage Temp 
Range 
℃ 
-40 
to 
+90 
-40 to 
+ 90 
-40 
to 
+ 90 
-40 to 
+ 90 
-40 to 
+120 
-40
to 
+90 
-40 
to 
+90 
-40 to 
+90 
M1 Vibration test 
8hours/axis 
M2 Mechanical shock 
Severity dependent on mounting position 
M3 Endurance shock test 
√ 
M4 Crush For Housing-Elbow 
Load 
√ 
M5 Crush For Housing-Foot 
Load 
√ 
M6 Free fall 
√ 
C1 High-/low-temperature 
storage 
√ 
C2 Low-temperature operation 
√ 
C3 Incremental temperature 
test 
1 cycle 
C4 Repainting temperature 
+130 for 0.25h and +110 for 
1h 
+130 
for 
0.25 h 
+130 for 0.25h and 
+110 for 1h 
C5 Temperature shock 
(component) 
100 cycles 
C6 Temperature shock with 
splash water 
100 cycles 
----- 
100 cycles 
C7 Temperature shock- 
immersion 
30 cycles 
C8 Humid heat, cyclic 
6 cycles 
C9 Humid heat,constant 
21 days 
C10 Salt spray test with 
operation, interior 
√ 


### 第 97 页
GB/T 2800001—2015 
93 
C11 Salt spray test with 
operation, exterior 
√ 
C12 Water protection  
IPX9K 
C13 Water Freeze 
√ 
C14 Condensation test with 
electr. assemblies 
√ 
—— 
√ 
—— 
C15 Dust 
IP6KX 
C16 Sun radiation 
√ 
C17 Chemical requirements and 
tests 
√ 
C18 Harmful gas test 
21 days 
Life endurance Test 
√ 
 
 
 
                                                                                                    
Part 
assembled 
area 
 
Test Item                                                       
D Cavity 
E Boot 
D1 
D2 
E 
Cavities Open to 
Exterior 
Cavities Open to 
Interior 
Boot / Luggage 
Compartment 
Normal operating Temp Range ℃  
-40 to 
+ 80 
-40 to 
+ 80 
-40 to 
+ 80 
Normal  storage Temp Range ℃ 
-40 to +90 
-40 to +90 
-40 to +90 
M1 Vibration test 
8hours/axis 
M2 Mechanical shock 
Severity dependent on mounting position 
M3 Endurance shock test 
Severity dependent on mounting position 
M4 Crush For Housing-Elbow Load 
Severity dependent on mounting position 
M5 Crush For Housing-Foot Load 
Severity dependent on mounting position 
M6 Free fall 
√ 
C1 High-/low-temperature 
storage 
√ 
C2 Low-temperature operation 
√ 
C3 Incremental temperature test 
1 cycle 
C4 Repainting temperature 
+110 for 0.5 h 
---- 


### 第 98 页
GB/T 2800001—2015 
94 
C5 Temperature shock 
(component) 
100 cycles 
C6 Temperature shock with 
splash water 
Severity dependent on mounting position 
C7 Temperature shock- immersion 
Severity dependent on mounting position 
C8 Humid heat, cyclic 
6cycles 
9cycles 
C9 Humid heat,constant 
21 days 
C10 Salt spray test with 
operation, interior 
√ 
C11 Salt spray test with 
operation, exterior 
---- 
C12 Water protection  
--- 
C13 Water Freeze 
---- 
C14 Condensation test with 
electr. assemblies 
Severity dependent on mounting position 
C15 Dust 
IP5KX 
C16 Sun radiation 
Severity dependent on mounting position 
C17 Chemical requirements and 
tests 
√ 
C18 Harmful gas test 
21days 
10 days 
Life endurance Test 
√ 
 
9.2 Process 
9.2.1 A/D/V Process for Electrical/Electronic Components. 
The Global Environmental Component Analysis/Development/Validation (A/D/V) Process 
defined within this document shall be followed for all components with electrical/electronic 
content. This includes, but is not limited to, Powertrain, Chassis,HVAC, Interior, Body, 
Closures, Exterior, and Electrical. 
Beginning in November 2008, all A/D/V test plans,test results, documentation, and data 
exchange between the supplier and FM related to Environment/ Durability A/D/V activities shall 
be uploaded to FM’s Global EMC/Environmental/Durability Database accessible through FM Supply 
Power. 
9.2.2 A/D/V Process Flow 
The A/D/V Process Flow shall be followed as shown in Figure 48. 


### 第 99 页
GB/T 2800001—2015 
95 
Requirements Review
Supplier Selection
Test Plan Kick-off
Hardware Design Review
Create Test Plan
Review Test Plan
Approve Test Plan
    Execute A/D/V Tasks
according to Test Plan
   Dev and DV DRBTRs
    Review PV Test Plan
Review Test Results
     Execute PV Tasks       
according to Test Plan
           PV DRBTR
        Prepare PPAP
       Review Test
Results
Perform HW 
Design   Review
Perform HW
Design Review
N
N
Y
Y
N
Y
FM DRE (Lead)\FM ENV/DUR Expert
FM Comp Val Engr\FM Materials Engr
FM DRE
FM ENV/DUR Expert
FM Comp Val Engr(Lead)
Supplier
            Supplier
FM ENV/DUR Expert
FM ENV/DUR Expert
FM Comp Val Engr 
(Lead) or
FM ENV/DUR Expert 
(Lead)
FM SQE, GM DRE
FM Comp Val Engr(Lead) or
FM ENV/DUR Expert (Lead)
Supplier
            Supplier
Supplier
FM Comp Val Engr or
FM ENV/DUR Expert
FM Comp Val Engr 
(Lead) or
FM ENV/DUR Expert 
(Lead)
FM SQE, FM DRE
            Supplier
Define Component Validation Q/FTP 2800004-2011 
Code letters in SSTS/CTS . Include Material Test 
Requirements.
Review the design to suggest solutions for 
Environmental robustness and discuss Test Plan 
expectations.
Present results from A/D/V Tasks.
Analysis: Present results from analytical procedures 
and simulation models.
Development: Present results from Development 
tasks and perform DRBTR on pre-DV samples.
Design Validation: Present results from DV tasks 
and perform DRBTR on DV samples. Inform SQE of 
issues.
Review Test Plan for PV and adapt according to DV 
results and production process.
Present results from PV tasks and perform DRBTR 
on PV samples. Inform SQE of issues.
Supplier
FM Comp Val Engr or
FM ENV/DUR Expert
            Supplier
A/D/V Tasks and
reviews are iterative
Before Sourcing
2 weeks after
supplier sourcing
Submitted 6 weeks
after supplier sourcing
10 weeks
after supplier sourcing
Milestone: DV complete 
by IV MRD
Milestone:
PV complete by VTC
Latest 3 weeks before
PPAP submission
Timing
Responsibility
Process Steps
Data to be provided
 
Figure 48: A/D/V Process Flow 
 
The A/D/V Process consists of the following major tasks: 
9.2.2.1 Requirements Review 
The Requirements Review shall be performed within Foton Motors(FM) to define the Q/FTP 
2800004-2011 Code letters. This Q/FTP 2800004-2011 Coding shall be documented in the Component 
Technical Specification (CTS) Section “Validation”. The FM Environmental/Durability(ENV/DUR) 
Expert shall define this information together with the FM Design Release Engineer(DRE) and 
FM Component Validation Engineer.The FM ENV/DUR Expert shall review the CTS for alignment to 
Q/FTP 2800004-2011 prior to sourcing. Also,the FM Materials Engineer needs to provide the 
Material test requirements. Supplier-proposed exemptions to the Q/FTP 2800004-2011 must be 


### 第 100 页
GB/T 2800001—2015 
96 
approved by the FM ENV/DUR Expert. In case no CTS is available, refer to the Validation section 
in the SSTS for Q/FTP 2800004-2011 Coding. 
9.2.2.2 Test Plan Kick-off/Hardware Design Review 
A Test Plan Kick-off/Hardware Design Review meeting shall be completed jointly by the 
supplier and FM 2 weeks after supplier sourcing. The purpose of this meeting is to review the 
design to suggest solutions for Environmental robustness and discuss the Component 
Environmental Test Plan expectations. Refer to the “Hardware Design Review” section of this 
document for more detailed instructions. 
9.2.2.3 Create and Review Test Plan 
Appendix B(Component Environmental Test Plan) shall be completed by the supplier and 
submitted in an electronic, editable format to FM 6 weeks after supplier sourcing. Approval 
shall occur 10 weeks after supplier sourcing.An editable version of Appendix B can be obtained 
by request from FM ENV/DUR Expert. 
9.2.2.4 Execute A/D/V Tasks 
A/D/V Tasks, including Analytical, Development, and Design Validation(DV), shall be 
successfully completed by Integration Vehicle Material Required Date (IV MRD) to support 
vehicle validation. 
Each test result shall be reported underneath each negotiated clause in the electronic 
editable Component Environmental Test Plan and submitted to the FM Component Validation 
Engineer (or FM ENV/DUR Expert) for evaluation and approval. 
In case of nonconformance, the FM Component Validation Engineer or FM ENV/DUR Expert,along 
with the FM Supplier Quality Engineer (SQE) and DRE, shall review the test results and determine 
if another iteration of DV is required.The FM Component Validation Engineer or FM ENV/DUR Expert 
shall then perform a Hardware Design Review. Based on the corrective action,the Component 
Environmental Test Plan shall be modified as required. 
9.2.2.5 Execute Product Validation (PV) Tasks 
PV Tasks shall be successfully completed by Validation Test Complete (VTC). The Component 
Environmental Test Plan shall be reviewed and adapted to comprehend production process 
variations/changes and DV test results accordingly. 
Each test result shall be reported underneath each negotiated clause in the electronic 
editable Component Environmental Test Plan and submitted to the FM Component Validation 
Engineer (or FM ENV/DUR Expert) for evaluation and approval. 
In case of nonconformance, the FM Component Validation Engineer or FM ENV/DUR Expert, along 
with the FM SQE and DRE, shall review the test results and determine if another iteration of 
PV is required. The FM Component Validation Engineer or FM ENV/DUR Expert shall then perform 
a Hardware Design Review. Based on the corrective action, the Component Environmental Test 
Plan shall be modified as required. 
9.2.3 Test Plan Negotiation 


### 第 101 页
GB/T 2800001—2015 
97 
The supplier shall submit the completed Component Environmental Test Plan in an 
electronic format editable with Microsoft Office to the FM ENV/DUR Expert for approval 6 weeks 
after supplier sourcing. 
9.2.4 Hardware Design Review 
A Hardware Design Review, as shown in Figure 49, shall be conducted for all new components, 
as well as for current production components with modifications, to ensure that the design 
meets Environmental/Durability compliance. This includes any hardware-related modifications 
(such as internal part swaps or material changes) and manufacturing process changes (such as 
tooling, solder process, or manufacturing location).Software changes that may affect Q/FTP 
2800004-2011-related requirements shall also be considered.Hardware Design Review(s) shall 
be scheduled and led by the FM Component Validation Engineer. It is recommended that this 
Hardware Design Review occurs at the same meeting as the EMC Design Review. 
The objectives of the Hardware Design Review shall be to: 
• Review component schematic design and circuit board layout 
• Review component assembly and mechanical construction 
• Review technical rational of Electrical/Electronic design concept, Mechanization design 
concept,and chosen materials 
• Examine any prior relevant analysis, calculations,and test results 
• Evaluate potential changes to the component design 
• Propose solutions to problems and appropriate re-validation 
• Verify that the proposed circuit board and assembly design satisfies component 
Environmental/Durability requirements 
• Evaluate manufacturing processes and changes 
• Evaluate software changes that may affect Q/FTP 2800004-2011-related requirements 
• Perform Thermal Fatigue Analysis 
Supplier Deliverables: 
The following documentation shall be delivered to the FM ENV/DUR Expert and/or FM Component 
Validation Engineer at least 10 working days ahead of the scheduled meeting: 
• Functional description 
• Vehicle location 
• Interface description(s), internal and external to the component 
• Hardware Schematic drawing 
• Electrical parts list and associated datasheets 
• Material datasheets for all parts (such as materials used for PCB, solder, flux, assembly, 
connections,etc.) including the Coefficients of Thermal Expansion 
• Part placement drawing 
• PCB Layout 
• Solder process description (solder alloy, solder temperature profile, cleaning material, 
process,etc.) 
• Assembly Mechanization (assembly drawings,mounting/support locations, openings in 
housing,cooling concept, etc.) 
Note: When available, actual hardware samples or physical mock-up for visual examination is 


### 第 102 页
GB/T 2800001—2015 
98 
required. 
Attendees: 
FM: 
• Q/FTP 2800004-2011 Environmental/Durability Expert 
• Component Validation Engineer 
• Design Release Engineer 
• EMC Expert (optional) 
• Supplier Quality Engineer (optional) Supplier: 
• Hardware Design Engineer 
• Electrical System Engineer 
• Environmental/Durability Expert 
• Validation/Test Engineer 
• Project Manager (optional) 
9.2.5 Analytical Activities 
The supplier shall conduct Analytical Activities according to the approved Component 
Environmental Test Plan. Analytical models and assumptions shall be provided to FM,according 
to the Component Environmental Test Plan. Each analytical result shall be reported underneath 
each negotiated clause in the electronic editable Component Environmental Test Plan and 
submitted to the FM Component Validation Engineer(or FM ENV/DUR Expert) for evaluation and 
approval. 
9.2.6 Development Activities 
The supplier shall conduct Development Activities according to the approved Component 
Environmental Test Plan.Test samples shall be provided to FM, according to the Component 
Environmental Test Plan, and this may include parts before testing and parts after testing. 
Each test result shall be reported underneath each negotiated clause in the electronic editable 
Component Environmental Test Plan and submitted to the FM Component Validation Engineer (or 
FM ENV/DUR Expert) for evaluation and approval.  
Components that have failed during a test shall be analyzed immediately by the supplier. 
The component shall not be repaired or further used in the Test Flow. The supplier shall contact 
the FM Component Validation Engineer (or FM ENV/DUR Expert) immediately to define further 
actions. 
9.2.7 Design Validation Activities 
The supplier shall conduct Design Validation (DV) according to the approved Component 
Environmental Test Plan.Test samples shall be provided to FM, according to the Component 
Environmental Test Plan, and this may include parts before testing and parts after testing. 
Each test result shall be reported underneath each negotiated clause in the electronic editable 
Component Environmental Test Plan and submitted to the FM Component Validation Engineer (or 
FM ENV/DUR Expert) for evaluation and approval. 
Components that have failed during a test shall be analyzed immediately by the supplier. 
The component shall not be repaired or further used in the Test Flow. The supplier shall contact 


### 第 103 页
GB/T 2800001—2015 
99 
the FM Component Validation Engineer (or FM ENV/DUR Expert) immediately to define further 
actions. 
9.2.8 Product Validation Activities 
The supplier shall conduct Product Validation (PV) according to the approved Component 
Environmental Test Plan.Test samples shall be provided to FM, according to the Component 
Environmental Test Plan, and this may include parts before testing and parts after testing. 
Each test result shall be reported underneath each negotiated clause in the electronic editable 
Component Environmental Test Plan and submitted to the FM Component Validation Engineer (or 
FM ENV/DUR Expert) for evaluation and approval.Components that have failed during a test shall 
be analyzed immediately by the supplier. The component shall not be repaired or further used 
in the Test Flow. The supplier shall contact the FM Component Validation Engineer (or FM ENV/DUR 
Expert) immediately to define further actions. 
9.3 Summary of A/D/V Activities 
Table 98 is a default summary of A/D/V activities that shall be used for defining tests 
that will be included in the Component Environmental Test Plan with FM approval. The FM-approved 
Component Environmental Test Plan is the only required set of activities. 
Table 98: Summary of A/D/V Activities 
Activity 
Phase 
FSC 
Analysis 
Electrical 
Nominal and Worst Case 
Performance Analysis 
A 
N/A 
Short/Open Circuit 
Analysis 
A 
N/A 
Mechanical 
Resonant Frequency 
Analysis 
A 
N/A 
High Altitude 
Shipping Pressure 
Effect Analysis 
A 
N/A 
Plastic Snap Fit 
Fastener Analysis 
A 
N/A 
Crush Analysis 
A 
N/A 
Temperature 
High Altitude 
Operation Overheating 
Analysis 
A 
N/A 
Thermal Fatigue 
Analysis 
A 
N/A 
Lead-Free Solder 
A 
N/A 


### 第 104 页
GB/T 2800001—2015 
100 
Analysis 
Design Validation (DV) 
Electrical 
E1 Parasitic Current 
DV 
N/A 
E2 Power Supply 
interruption 
DV 
A, C 
E3 Power Battery 
Voltage Change 
DV 
A, C 
E4 Sinusoidal 
Superimposed Voltage 
DV 
A 
E5 Pulse Superimposed 
Voltage 
DV 
A 
E6 Short circuit in 
signal circuit and 
load circuits 
DV 
A 
E7 Open Circuit – 
Single Line 
Interruption 
DV 
C 
E8 Open Circuit – 
Multiple Line 
Interruption 
DV 
C 
E9 Ground Offset 
DV 
A 
E10 Discrete Digital 
Input Threshold 
Voltage 
DV 
N/A 
E11 Over Load – All 
Circuits 
DV 
D,E 
E12 Isolation 
Resistance 
DV 
C 
E13 Over Voltage 
 
D, DV 
C 
E14 Transient over 
voltage 
DV 
D,E 
E15 Transient under 
voltage 
DV 
D,E 
E16 Jump start 
D, DV 
C 
E17 Load dump 
DV 
/ 
E18 short 
interruptions 
DV 
/ 
E19 Start pulse 
DV 
/ 
E20 Voltage curve with 
intelligent generator 
DV 
/ 


### 第 105 页
GB/T 2800001—2015 
101 
control 
E21 Reverse polarity 
 
D, DV 
C 
E22 Dielectric 
strength 
DV 
/ 
E23 Back feeds 
DV 
/ 
Mechanical 
M1 Vibration test 
DV, PV 
A 
M2 Mechanical shock 
DV 
A 
M3 Endurance shock 
test 
DV 
C 
M4 Crush For 
Housing-Elbow Load 
DV 
C 
M5 Crush For 
Housing-Foot Load 
DV 
C 
M6 Free fall 
DV 
N/A 
Climatic 
C1 
High-/low-temperature 
storage 
DV 
A 
C2 Low-temperature 
operation 
DV 
A 
C3 Incremental 
temperature test 
DV 
A 
C4 Repainting 
temperature 
DV 
A 
C5 Temperature shock 
(component、Without 
Housing) 
DV 
A 
C6 Temperature shock 
with splash water 
DV 
A 
C7 Temperature shock- 
immersion 
DV 
A 
C8 Humid heat, cyclic DV, PV 
A 
C9 Humid heat,constant DV, PV 
A 
C10 Salt spray test 
with operation, 
interior 
DV 
A 
C11 Salt spray test 
with operation, 
exterior 
DV 
A 
C12 Water protection  DV 
A 


### 第 106 页
GB/T 2800001—2015 
102 
C13 Water Freeze 
DV 
A 
C14 Condensation test 
with electr. 
assemblies 
DV 
A 
C15 Dust 
DV 
A 
C16 Sun radiation 
DV 
A 
C17 Chemical 
requirements and tests 
DV 
A 
C18 Harmful gas test 
DV 
A 
Product Validation (PV) 
Shipping Vibration 
PV 
C 
 
 
9.4 Test Sequence Plan 
A component-specific test sequence plan must be defined in the Component Performance 
Specifications. 
The tests that are not required for a component acc. to the test selection table must be 
deleted from the test sequence plan.If a component-specific adaptation of the test sequence 
is required, the test sequence plan may be adapted.If the DUTs have not been damaged in the 
test M-01 Free fall, two DUTs must be used for the further sequence test. Otherwise, the spare 
DUTs must be used. 
9.4.1 Sequence Tests 
 
 


### 第 107 页
GB/T 2800001—2015 
103 
Figure 49:Sequencetests 
C1 High-/low-
temperature 
storage
C3 Incremental 
temperature 
test
C2 Low-
temperature 
operation
C4 Repainting 
temperature
C5 
Temperature 
shock 
(component)
C11 Salt spray 
test with 
operation, 
exterior
C15 Dust
C8 Humid heat, 
cyclic
M3 Endurance 
shock test
M2 Mechanical 
shock
M1 Vibration 
test
C12 Water 
protection 
C6 
Temperature 
shock with 
splash water
C7 
Temperature 
shock- 
immersion
C3 Incremental 
temperature 
test
5-Point Functional/
Parametric Check 
5-Point Functional/
Parametric Check 
1-Point Functional/
Parametric Check 
1-Point Functional/
Parametric Check 
5-Point Functional/
Parametric Check 
1-Point Functional/
Parametric Check 
1-Point Functional/
Parametric Check 
5-Point Functional/
Parametric Check 
1-Point Functional/
Parametric Check 
1-Point Functional/
Parametric Check 
5-Point Functional/
Parametric Check 
1-Point Functional/
Parametric Check 
1-Point Functional/
Parametric Check 
1-Point Functional/
Parametric Check 
5-Point Functional/
Parametric Check 
Complete
 


### 第 108 页
GB/T 2800001—2015 
104 
9.4.2 Tests outside the sequence (parallel tests) 
Figure 50: Tests outside the sequence 
C10 Salt spray test 
with operation, 
interior
C1 High-/low-
temperature 
storage
C9 Humid 
heat,constant
C14 Condensation 
test with electr. 
assemblies
C5 Temperature 
shock (Without 
Housing)
C16 Sun radiation
C18 Harmful gas 
test
C17 Chemical 
requirements and 
tests
M6 Free fall
M4 Crush For 
Housing-Elbow 
Load
M5 Crush For 
Housing-Foot 
Load
5-Point Functional/
Parametric Check 
5-Point Functional/
Parametric Check 
C13 Water Freeze
Complete
Vibration Noise (Squeak 
& Rattle)
Connector Test
 
 
9.4.3 Life Sequence Test 


### 第 109 页
GB/T 2800001—2015 
105 
Figure 51 life sequence 
 
 
 
9.4.4 Electric Sequence Test 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 110 页
GB/T 2800001—2015 
106 
Figure 52 Electric sequence 
 
E16 Jump start
E21 Reverse 
polarity
E13 Over 
Voltage
E5 Pulse 
Superimposed 
Voltage
E7 Open Circuit 
– Single Line 
Interruption
E8 Open Circuit 
– Multiple Line 
Interruption
E1 Parasitic 
Current
E9 Ground 
Offset
E2 Power Supply 
interruption
E11 Over Load 
– All Circuits
E4 Sinusoidal 
Superimposed 
Voltage
E3 Power Battery 
Voltage Change
E6 Short circuit 
in signal circuit 
and load circuits
E10 Discrete Digital 
Input Threshold 
Voltage
E12 Isolation 
Resistance
E14 Transient 
over voltage
E15 Transient 
under voltage
E17 Load dump
E18 short 
interruptions
E19 Start pulse
E20 Voltage curve 
with intelligent 
generator control
E22 Dielectric 
strength
E23 Back feeds
5-Point Functional/
Parametric Check 
Visual Inspection and 
Dissection
Complete
5-Point Functional/
Parametric Check 
C1 High-/low-temperature 
storage
 
9.4.5 Analysis Sequence Test 
 
 
 
 
 
 
 
 


### 第 111 页
GB/T 2800001—2015 
107 
Figure 53 Analysis sequence 
 
E24 Nominal and Worst 
Case Performance 
Analysis
C1 High-/low-
temperature storage
E25 Short/Open Circuit 
Analysis
M7 Resonant Frequency 
Analysis
M8 High Altitude 
Shipping Pressure Effect 
Analysis
M9 Plastic Snap Fit 
Fastener Analysis
M10 Crush Analysis
C19 High Altitude 
Operation Overheating 
Analysis
C20 Thermal Fatigue 
Analysis
5-Point Functional/
Parametric Check 
5-Point Functional/
Parametric Check 
C21 Lead-Free Solder 
Analysis
Complete
 
9.4.6 PV Sequence Test 


### 第 112 页
GB/T 2800001—2015 
108 
Figure 54 PV sequence 
 
5-Point Functional/
Parametric Check 
Visual Inspection and Dissection
Complete
5-Point Functional/Parametric 
Check 
C1 High-/low-temperature 
storage
Shipping Vibration
 
 
9.5 Computation models for the life test "High-temperature endurance test" 
9.5.1 Arrhenius model 
 The calculation of the test duration of the high-temperature endurance test life test 
is based on the temperature spectrum percentage as per the use profile in the Component 
Performance Specification 
Table 99: Temperature spectrum 
Temperature (°C) 
Distribution (%) 
TField.1 
p1 
TField.2 
P2 
... 
... 
TField.n 
pn 
and the operating hours tBetrieb of the vehicle in the field. 
For each temperature TFeld,1 … TFeld,n, an acceleration factor AT,1 … AT,n is calculated on the 
basis of the following equation: 
 (1) 
Where: 
AT,i Acceleration factor of the Arrhenius model 
EA Activation energy EA = 0,45 eV 
k Boltzmann constant (k = 8,617 x 10-5 eV/K) 


### 第 113 页
GB/T 2800001—2015 
109 
TTest Test temperature (°C), generally Tmax 
TFeld,i Field temperature (°C) according to the temperature spectrum as per use profile 
-     Absolute zero of the temperature 
273,15 °C 
The total test duration for the high-temperature endurance test results from the 
acceleration factor as per 
  (2) 
Where: 
Ttest Test duration (hours) of the high-temperature endurance test life test 
toper Operating time (hours) in the field 
pi  Percentage of the operating time for which the component is operated at the  
    temperature TFeld,i in the field 
AT,i  Acceleration factor for temperature TFeld,i 
9.5.2 Arrhenius model for the use for components with reduced performance at high temperatures 
To calculate the test duration for the high-temperature endurance test life test for 
components with reduced performance at high temperatures starting with Top,max, the temperature 
spectrum as per the use profile in the Component Performance Specification is divided into 
the two temperature ranges T ≤ Top,max and T>Top,max. 
Table 100: Temperature spectrum for T ≤ Top,max with test temperature Top,max 
Temperature (°C)  
Distribution (%) 
TField.1 
p1 
TField.2  
p2 
 … 
… 
TField.m (≤ Top,max) 
pm 
( m < n ) 
Table 101: Temperature spectrum for Top,max < T ≤ Tmax with test temperature Tmax 
Temperature (°C)  
Distribution (%) 
TField.m+1(> Top,max)  
pm+1 
TField.m+2 
pm+2 
 … 
… 
TField.n 
pn 
( m < n ) 
For each temperature TFeld,1 … TFeld,m … TFeld,n, an acceleration factor AT,1 … AT,m… AT,n is 
calculated by means of equation (1), where for temperature range T ≤ Top,max a test temperature 
of TTest = Top, max and for temperature range T > Top,max a test temperature of TTest = Tmax is assumed. 
The required test duration top, max for test temperature Top, max results from the equation (2) 
with i = 1 ...m. 
The required test duration Tmax for test temperature Tmax results from the equation (2) with 
i=m+1 …n. 


### 第 114 页
GB/T 2800001—2015 
110 
The total test duration tGes is the sum of top, max and tmax. 
To ensure a test similar to the real conditions, the test is carried out intermittently 
at the test temperatures Top,max and Tmax (see Figure 44). 
The typical interval of 48 h is divided at the ratio of the part test durations top, max and 
Tmax. 
9.5.3 Arrhenius model for use with components on coolant circuits 
For components with a connection to the coolant circuit, all pertinent operating situations 
i (see Figure 55; i corresponds to the consecutive number of the operating situations) and 
the associated temperature distributions for the environment and the coolant circuit must be 
taken into account. 
For the "high-temperature endurance test" life test, the test durations and test 
temperatures for the environment and the coolant circuit must be calculated for each relevant 
operating situation i as described below; the total test duration is the sum of the test 
durations for each relevant operating situation i. 
For each relevant operating situation i, the test duration for the ambient temperature 
and the coolant circuit must first be calculated separately according to the Arrhenius model 
as per appendix SECTION 9.5.1 or SECTION 9.5.2 in order to calculate the test duration for 
operating situation i. 
Because the resulting test durations ttest,environment and ttest,CC (CC = coolant circuit) generally 
differ but the component can only be tested for the respective operating situation i with a 
uniform test duration, the test durations must be aligned between the ambient temperature and 
the coolant circuit. 
The shorter of the two test durations ttest, environment and ttest, CC must be adapted to the longer 
test duration as per the following iteration method by dividing the test into at least two 
partial tests and reducing the test temperatures for all but one partial test. 
Case A: ttest, environment < ttest, CC 
Test duration: 
For ttest,environment < ttest,CC, the test duration for operating situation i is 
ttest, situation i = ttest, CC. 
Test temperature for coolant: 
The test temperature must be selected according to the Arrhenius model as per appendix SECTION 
9.5.1 (generally Tcool,max). 
Test temperatures for ambient temperature: 
The test temperatures must be calculated iteratively as per the following algorithm on the 
basis of the temperature spectrum of the ambient temperature of the operating situation i under 
examination (Table 101). 
Table 101: Temperature spectrum for environment 
Temperature (°C) 
Distribution (%) 
TField.1 
p1 
TField.2 
P2 
... 
... 


### 第 115 页
GB/T 2800001—2015 
111 
TField.n 
pn 
1. Iteration start (m = 0): 
The first partial test must be performed at the test temperature TFeld, n for the partial 
test duration ttest, T_Field, n = toper * pn (where toper corresponds to the operating time in the field 
of the operating situation i under examination in hours). 
2. First iteration (m = 1): 
Part of the test duration for operating situation i ttest, situation i is covered by the 1st partial 
test, so a remaining test duration still to be covered by the additional partial tests results 
from 
tremaining, 1 = ttest, situation i – ttest, T_Field, n. 
In addition, the portion pn of the temperature distribution of the ambient temperature 
is covered by the first partial test. Therefore, this portion pn must be set to pn = 0 for the 
further calculation. 
To specify the test temperature for the 2nd partial test (m = 1), the test temperature 
Tadapted must first be determined using the Arrhenius model as per SECTION 9.5.1 or SECTION 9.5.2 
in such a way that a test duration with the magnitude of the remaining test duration tremaining, 
1 results for the distribution (adapted with pn = 0) of the ambient temperature. 
If the adapted test temperature Tadapted determined in such a way is less than TFeld, n-1, the 
2nd partial test must be performed at the test temperature TFeld, n-1 for the test duration 
ttest, T_Field, n-1 = tBetrieb * pn-1 
and at least one additional iteration step must be performed. 
If, however, the determined adapted test temperature Tadapted is greater than TField, n-1, the 
2nd partial test must be performed at the test temperature Tadapted for the test duration 
ttest, T_Field, n-1 = tremaining, 1 
and an additional iteration step does not have to be performed (iteration end). 
3. Additional iterations (m = 2, 3, …) 
A part of the test duration for operating situation i ttest,situation i is covered by the first 
m partial tests, so a remaining test duration still to be covered by the additional partial 
tests results from 
 
In addition, the portions pn-k with k = 0, 1, …, (m-1) of the temperature distribution 
of the ambient temperature are covered by the first m partial tests. Therefore, these portions 
pn-k must be set to pn-k = 0 for the further calculation. 
To specify the test temperature for the (m+1)th partial test, the test temperature Tadapted 
must first be determined using the Arrhenius model as per SECTION 9.5.1 or SECTION 9.5.2 in 
such a way that a test duration with the magnitude of the remaining test duration tremaining, 
m results for the distribution (adapted with pn-k = 0) of the ambient temperature. 
If the adapted test temperature Tadapted determined in such a way is less than TField, n-m, the 
(m+1)th partial test must be performed at the test temperature TField, n-m for the test duration 
ttest, T_Field, n-m = toper * pn-m 
and at least one additional iteration step must be performed. 


### 第 116 页
GB/T 2800001—2015 
112 
If, however, the determined adapted test temperature Tadapted is greater than TField, n-m, the 
(m+1)th partial test must be performed at the test temperature Tadapted for the test duration 
ttest, T_Field, n-m = tremaining, m  
and an additional iteration step does not have to be performed (iteration end). 
Case B: ttest,environment > ttest, CC 
Test duration: 
    For ttest, environment > ttest, CC, the test duration for operating situation i is 
ttest, situation i = ttest, environment. 
Test temperature for environment: 
The test temperature must be selected according to the Arrhenius model as per appendix 
SECTION 9.5.1 or SECTION 9.5.2 (generally Tmax or Tmax and Top, max). 
Test temperatures for coolant: 
The test temperatures must be calculated iteratively as per the following algorithm on 
the basis of the temperature spectrum for the coolant temperature of the operating situation 
i under examination (Table 102). 
Table 102: Temperature spectrum for coolant temperature 
Temperature (°C) 
Distribution (%) 
TField.1 
p1 
TField.2 
P2 
... 
... 
TField.n 
pn 
1. Iteration start (m = 0): 
The first partial test must be performed at the test temperature TFeld, n for the partial 
test duration ttest, T_Field, n = tOper * pn (where tOper corresponds to the operating time in the field 
of the operating situation i under examination in hours). 
2. First iteration (m = 1): 
Part of the test duration for operating situation i ttest, situation i is covered by the first 
partial test, so a remaining test duration still to be covered by the additional partial tests 
results from 
tremaining, 1 = ttest, situation i – ttest, T_Field, n. 
In addition, the portion pn of the temperature distribution of the coolant temperature 
is covered by the first partial test. Therefore, this portion pn must be set to pn = 0 for the 
further calculation. 
To specify the test temperature for the second partial test (m = 1), the test temperature 
Tadapted must first be determined using the Arrhenius model as per SECTION 9.5.1 in such a way 
that a test duration with the magnitude of the remaining test duration tremaining, 1 results for 
the distribution (adapted with pn = 0) of the coolant temperature. 
If the adapted test temperature Tadapted determined in such a way is less than TFeld, n-1, the 
second partial test must be performed at the test temperature TFeld, n-1 for the test duration 
ttest, T_Field, n-1 = tOper * pn-1 
and at least one additional iteration step must be performed. 


### 第 117 页
GB/T 2800001—2015 
113 
If, however, the determined adapted test temperature Tadapted is greater than TFeld, n-1, the 
2nd partial test must be performed at the test temperature Tadapted for the test duration 
ttest, T_Feld, n-1 = tremaining, 1 
and an additional iteration step does not have to be performed (iteration end). 
3. Additional iterations (m = 2, 3, …) 
A part of the test duration for operating situation i ttest,situation i is covered by the first 
m partial tests, so a remaining test duration still to be covered by the additional partial 
tests results from 
 
In addition, the portions pn-k with k = 0, 1, …, (m-1) of the temperature distribution 
of the coolant temperature are covered by the first m partial tests. Therefore, these portions 
pn-k must be set to pn-k = 0 for the further calculation. 
To specify the test temperature for the (m+1)th partial test, the test temperature Tadapted 
must first be determined using the Arrhenius model as per SECTION 9.5.1 in such a way that 
a test duration with the magnitude of the remaining test duration tremaining, m results for the 
distribution (adapted with pn-k = 0) of the coolant temperature. 
If the adapted test temperature Tadapted determined in such a way is less than TField, n-m, the 
(m+1)th partial test must be performed at the test temperature TField, n-m for the test duration 
ttest, T_Fild, n-m = tOper * pn-m 
and at least one additional iteration step must be performed. 
If, however, the determined adapted test temperature Tadapted is greater than TFeld, n-m, the 
(m+1)th partial test must be performed at the test temperature Tadapted for the test duration 
ttest, T_Field, n-m = tremaining, m  
and an additional iteration step does not have to be performed (iteration end). 
9.6 Computation models for the "temperature cycle test" life test 
9.6.1 Coffin-Manson model 
The calculation of the test duration for the "temperature cycle test" life test is based 
on the average temperature change of the component in the field ΔTField (see Table 103) and the 
number of temperature cycles during the service life in the field NTempCyclesField. Two temperature 
cycles per day can typically be assumed for the number of temperature cycles in the field. 
This results in: 
NTempCyclesField = 2 * 365 * 10 (years) = 7300 cycles 
Depending on the average temperature change in the field, the acceleration factor of the 
Coffin-Manson model is calculated as follows: 
 
Where: 
ACM Acceleration factor of the Coffin-Manson model 


### 第 118 页
GB/T 2800001—2015 
114 
ΔTTest Temperature difference during a test cycle (ΔTTest = Tmax - Tmin) 
ΔTFeld Average temperature difference during service life in the field 
c Parameter of the Coffin-Manson model 
  In this standard a fixed value of 2,5 is used for c. 
The total number of test cycles is calculated as per 
 
Where: 
NTest Required number of test cycles 
NTempCyclesField Number of temperature cycles during service life in the field 
ACM Acceleration factor of the Coffin-Manson model as per equation (3) 
Table 103: Overview of installation locations, typical spectrums, and temperature rises 
Installation location of the component 
Spectrum no. Temperature rise in K 
Interior, without special requirement 
1 
36 
Hang-on part, without special requirements 
1 
36 
Interior exposed to sun radiation 
2 
46 
Hang-on part, roof 
2 
46 
Engine compartment, but not on the engine 
3 
60 
On the radiator 
3 
60 
Engine-mounted parts 
4 
75 
Gearbox-mounted parts 
4 
75 
9.6.2 Coffin-Manson model for use with components on coolant circuits 
For components with a connection to the coolant circuit, all relevant operating situations 
i (see Figure 55; i corresponds to the consecutive number of the operating situations) and 
the associated temperature rises for the environment and the coolant circuit must be taken 
into account. 
For the "temperature cycle test" life test, the key temperatures and the number of test 
cycles must be calculated for each relevant operating situation i, as described below; the 
total number of test cycles results from the sum of the partial numbers of test cycles for 
each relevant operating situation i. 
For each relevant operating situation i, the numbers of test cycles for the ambient 
temperature and the coolant circuit must first be calculated separately according to the 
Coffin-Manson model in order to calculate the number of test cycles for operating situation 
i. 
Because the resulting numbers of test cycles Ntest, environment and Ntest, CC (CC = coolant circuit) 
generally differ but the component can only be tested for the respective operating situation 
i with a uniform number of test cycles, the numbers of test cycles must be aligned between 
the ambient temperature and the coolant circuit. 
The shorter of the two numbers of test cycles Ntest, environment and Ntest, CC must be adapted to 
the longer number of test cycles as per the following calculation by dividing the test into 


### 第 119 页
GB/T 2800001—2015 
115 
three partial tests. One partial test is performed with a full temperature rise between Tmin 
and Tmax; the other two partial tests are performed with a reduced temperature rise between 
Tmin and Troom or between Troom and Tmax. 
Case A: Ntest,environment > Ntest,CC 
Number of test cycles: 
For Ntest, environment > Ntest, CC, the number of test cycles for operating situation i is 
Ntest, situation i = Ntest, environment. 
Number of test cycles for coolant: 
The number of test cycles for the coolant Ntest, CC must be adapted to the larger number of 
test cycles for the environment Ntest, environment. The test cycles must be performed in the following 
three temperature ranges: 
1. xCC test cycles must be performed between TCC, min and TCC, max. 
The acceleration factor ACM, CC, 1 is calculated as per the Coffin-Manson model with          
ΔTTest, 1 = TCC, max - TCC, min 
2. ½ * (Ntest, situation i - xCC) test cycles must be performed between TCC, min and Troom. 
The acceleration factor ACM, CC, 2 is calculated as per the Coffin-Manson model with          
ΔTTest, 2 = Troom - TCC, min. 
3. ½ * (Ntest,situation i - xCC) test cycles must be performed between Troom and TCC, max. 
The acceleration factor ACM, CC, 3 is calculated as per the Coffin-Manson model with          
ΔTTest, 3 = TCC, max –Troom. 
In total, Ntest,situation i temperature cycles result from 1. to 3. 
The following results from equation (4) in appendix SECTION 9.6.1: 
 
The number of test cycles xCC is calculated from this as follows: 
 
The numbers of test cycles for the three partial tests are obtained by inserting xCC into 
points 1. to 3. listed above. 
If TCC, op, max < TC, max or TCC, op, min > TCC, min or Tenvironment, op, max < Tenvironment, max or Tenvironment, op, min > Tenvironment, 
min, additional holding times at the corresponding temperatures as per Figure 46 in section 
7.3.2 must be taken into account. The temperature cycles for the ambient temperature and for 
the coolant circuit proceed synchronously during a test. 
Case B: Ntest, environment < Ntest, CC 
Number of test cycles: 
For Ntest, environment < Ntest, CC, the number of test cycles for operating situation i is 
Ntest, situation i = Ntest, CC. 
Number of test cycles for environment: 


### 第 120 页
GB/T 2800001—2015 
116 
The number of test cycles for the environment Ntest, environment must be adapted to the larger 
number of test cycles for the coolant Ntest, CC. The test cycles must be performed in the following 
three temperature ranges: 
1. xenvironment test cycles must be performed between Tenvironment, min and Tenvironment, max. The acceleration 
factor ACM, environment, 1 is calculated as per the Coffin-Manson model, with ΔTTest, 1 = Tenvironment, max – 
Tenvironment, min. 
2. ½ * (Ntest,situation i – xenvironment) test cycles must be performed between Tenvironment, min and Troom. 
The acceleration factor ACM, environment, 2 is calculated as per the Coffin-Manson model, with  
ΔTTest, 2 = Troom – Tenvironment, min. 
3. ½ * (Ntest,situation i – xenvironment) test cycles must be performed between Troom and Tenvironment, max. 
The acceleration factor ACM, environment, 3 is calculated as per the Coffin-Manson model, with  
ΔTTest, 3 = Tenvironment, max –Troom. 
In total, Ntest,situation i temperature cycles result from 1. to 3. 
The following results from equation (4) in appendix SECTION 9.6.1: 
 
The number of test cycles xenvironment is calculated from this as follows: 
 
The numbers of test cycles for the three partial tests are obtained by inserting xenvironment 
into points 1. to 3. listed above. 
If Tenvironment, op, max < Tenvironment, max or Tenvironment, op,min > Tenvironment, min or TCC, op, max < TCC, max or TCC, op,min > TCC,min, 
additional holding times at the corresponding temperatures as per Figure 46 in section 7.3.2 
must be taken into account. 
The temperature cycles for the ambient temperature and for the coolant circuit proceed 
synchronously during a test. 
9.7 Operating situations 
For vehicles with a pure engine drive train, the operating state of the vehicle generally 
can be divided into the following two operating situations over its service life: 
• Driving operation 
• Parking 
For vehicles with alternative drive trains, it may be necessary to take additional 
operating situations into account (see Table 104). 
For components to which several operating situations are relevant (Figure 55), the 
operating types must be specifically defined for each operating situation if necessary. 
Table 104: Description General part Operating situations 
Operating 
situation 
Vehicle  
parked 
Charging 
cable 
High-voltage 
battery pack 
Power line 
communication active 


### 第 121 页
GB/T 2800001—2015 
117 
inserted 
charging 
(if available) 
Driving operation 
no 
no 
yes/no 
no 
Charging operation 
yes 
yes 
yes 
yes 
Preconditioning 
yes 
yes/no 
yes/no 
yes/no 
On-grid parking 
yes 
yes 
no 
yes 
Off-grid parking or 
parking 
yes 
no 
no 
no 
    All operating situations relevant to the component as per Figure 55 must be taken into 
account when deriving the test requirements. 
 
Figure 55: Division of the load spectrum according to Operating situations 
 
_________________________________ 

