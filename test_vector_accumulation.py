#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试向量累加功能
验证向量是否正确累加而不是覆盖
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
import pickle
from pathlib import Path

def test_vector_accumulation():
    """测试向量累加功能"""
    print("=" * 60)
    print("🧪 测试向量累加功能")
    print("=" * 60)
    
    try:
        from src.indexer.builder import IndexBuilder
        
        # 创建测试索引
        test_index_path = Path("data/indices/test_accumulation.idx")
        test_meta_path = Path("data/indices/test_accumulation.meta")
        
        # 清理之前的测试文件
        if test_index_path.exists():
            test_index_path.unlink()
        if test_meta_path.exists():
            test_meta_path.unlink()
        
        print("1. 创建初始索引...")
        
        # 创建索引构建器
        config = {
            'indexing': {
                'index_type': 'hnsw',
                'metric': 'cosine',
                'ef_construction': 400,
                'ef_search': 200,
                'M': 32
            }
        }
        
        builder = IndexBuilder(config)
        builder.create_index(768)  # 768维向量
        
        print(f"   初始向量数: {builder.total_vectors}")
        
        # 添加第一批向量
        print("\n2. 添加第一批向量 (5个)...")
        vectors1 = np.random.random((5, 768)).astype(np.float32)
        ids1 = np.arange(1000, 1005)
        
        success = builder.add_vectors(vectors1, ids1)
        print(f"   添加结果: {'成功' if success else '失败'}")
        print(f"   当前向量数: {builder.total_vectors}")
        
        # 保存索引
        if builder.save_index(test_index_path):
            print("   ✅ 第一次保存成功")
        else:
            print("   ❌ 第一次保存失败")
            return False
        
        # 验证保存的元数据
        with open(test_meta_path, 'rb') as f:
            meta1 = pickle.load(f)
        print(f"   保存的向量数: {meta1.get('total_vectors', 0)}")
        
        # 创建新的构建器实例，模拟重新加载
        print("\n3. 重新加载索引...")
        builder2 = IndexBuilder(config)
        
        if builder2.load_index(test_index_path):
            print("   ✅ 索引加载成功")
            print(f"   加载后向量数: {builder2.total_vectors}")
        else:
            print("   ❌ 索引加载失败")
            return False
        
        # 添加第二批向量
        print("\n4. 添加第二批向量 (3个)...")
        vectors2 = np.random.random((3, 768)).astype(np.float32)
        ids2 = np.arange(2000, 2003)
        
        success = builder2.add_vectors(vectors2, ids2)
        print(f"   添加结果: {'成功' if success else '失败'}")
        print(f"   当前向量数: {builder2.total_vectors}")
        
        # 再次保存
        if builder2.save_index(test_index_path):
            print("   ✅ 第二次保存成功")
        else:
            print("   ❌ 第二次保存失败")
            return False
        
        # 验证最终结果
        with open(test_meta_path, 'rb') as f:
            meta2 = pickle.load(f)
        final_count = meta2.get('total_vectors', 0)
        print(f"   最终保存的向量数: {final_count}")
        
        # 验证累加是否正确
        expected_count = 5 + 3  # 第一批5个 + 第二批3个
        if final_count == expected_count:
            print(f"\n✅ 向量累加测试通过！")
            print(f"   期望向量数: {expected_count}")
            print(f"   实际向量数: {final_count}")
            
            # 测试搜索功能
            print("\n5. 测试搜索功能...")
            query_vector = np.random.random((1, 768)).astype(np.float32)
            distances, indices = builder2.search(query_vector, k=3)
            print(f"   搜索返回结果数: {len(indices[0])}")
            print(f"   搜索结果索引: {indices[0]}")
            
            return True
        else:
            print(f"\n❌ 向量累加测试失败！")
            print(f"   期望向量数: {expected_count}")
            print(f"   实际向量数: {final_count}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试文件
        try:
            if test_index_path.exists():
                test_index_path.unlink()
            if test_meta_path.exists():
                test_meta_path.unlink()
            print("\n🧹 测试文件已清理")
        except:
            pass

def test_index_0002_recovery():
    """尝试恢复索引0002"""
    print("\n" + "=" * 60)
    print("🔧 尝试恢复索引0002")
    print("=" * 60)
    
    try:
        from src.indexer.builder import IndexBuilder
        
        index_path = Path("data/indices/0002.idx")
        meta_path = Path("data/indices/0002.meta")
        backup_idx = Path("data/indices/0002.idx.backup")
        backup_meta = Path("data/indices/0002.meta.backup")
        
        print(f"索引文件存在: {index_path.exists()}")
        print(f"元数据文件存在: {meta_path.exists()}")
        print(f"备份索引存在: {backup_idx.exists()}")
        print(f"备份元数据存在: {backup_meta.exists()}")
        
        # 检查当前状态
        if meta_path.exists():
            with open(meta_path, 'rb') as f:
                current_meta = pickle.load(f)
            print(f"当前向量数: {current_meta.get('total_vectors', 0)}")
            
            if current_meta.get('total_vectors', 0) == 0:
                print("⚠️  检测到向量数为0，可能需要恢复")
                
                # 如果有备份，尝试恢复
                if backup_idx.exists() and backup_meta.exists():
                    print("🔄 尝试从备份恢复...")
                    
                    # 读取备份元数据
                    with open(backup_meta, 'rb') as f:
                        backup_meta_data = pickle.load(f)
                    backup_count = backup_meta_data.get('total_vectors', 0)
                    
                    if backup_count > 0:
                        print(f"备份中的向量数: {backup_count}")
                        
                        # 恢复备份
                        import shutil
                        shutil.copy2(backup_idx, index_path)
                        shutil.copy2(backup_meta, meta_path)
                        
                        print("✅ 从备份恢复成功")
                        
                        # 验证恢复结果
                        with open(meta_path, 'rb') as f:
                            restored_meta = pickle.load(f)
                        print(f"恢复后向量数: {restored_meta.get('total_vectors', 0)}")
                        
                        return True
                    else:
                        print("❌ 备份中也没有向量数据")
                else:
                    print("❌ 没有找到备份文件")
            else:
                print("✅ 索引状态正常，无需恢复")
                return True
        
        return False
        
    except Exception as e:
        print(f"❌ 恢复过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始向量累加功能测试...")
    
    # 测试向量累加
    accumulation_ok = test_vector_accumulation()
    
    # 尝试恢复索引0002
    recovery_ok = test_index_0002_recovery()
    
    print("\n" + "=" * 60)
    print("🎯 测试结果总结")
    print("=" * 60)
    print(f"向量累加功能: {'✅ 通过' if accumulation_ok else '❌ 失败'}")
    print(f"索引0002恢复: {'✅ 成功' if recovery_ok else '❌ 失败'}")
    
    if accumulation_ok:
        print("\n✅ 向量累加修复成功！现在向量化不会覆盖现有数据")
    else:
        print("\n❌ 向量累加仍有问题，需要进一步调试")
    
    if recovery_ok:
        print("✅ 索引0002状态正常")
    else:
        print("❌ 索引0002可能需要重新向量化")

if __name__ == "__main__":
    main()
