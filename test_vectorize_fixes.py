#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试向量化组件修复
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试导入"""
    print("=" * 50)
    print("测试导入...")
    
    try:
        # 测试PyQt6导入
        print("1. 测试PyQt6导入...")
        try:
            from PyQt6.QtWidgets import QApplication, QWidget
            print("   ✓ PyQt6导入成功")
            pyqt_available = True
        except ImportError as e:
            print(f"   ✗ PyQt6导入失败: {e}")
            pyqt_available = False
        
        # 测试qt_material导入
        print("2. 测试qt_material导入...")
        try:
            import qt_material
            print("   ✓ qt_material导入成功")
        except ImportError as e:
            print(f"   ✗ qt_material导入失败: {e}")
        
        # 测试sentence_transformers导入
        print("3. 测试sentence_transformers导入...")
        try:
            import sentence_transformers
            print("   ✓ sentence_transformers导入成功")
        except ImportError as e:
            print(f"   ✗ sentence_transformers导入失败: {e}")
        
        # 测试向量化组件导入
        print("4. 测试向量化组件导入...")
        try:
            from src.gui.i18n import Translator
            from src.gui.widgets.vectorize import VectorizeWidget
            print("   ✓ 向量化组件导入成功")
            
            # 创建翻译器
            translator = Translator()
            
            # 如果PyQt6可用，创建应用和组件
            if pyqt_available:
                app = QApplication(sys.argv)
                widget = VectorizeWidget(translator)
                print("   ✓ 向量化组件创建成功")
                
                # 测试组件属性
                print("5. 测试组件属性...")
                if hasattr(widget, 'logger'):
                    print("   ✓ logger属性存在")
                else:
                    print("   ✗ logger属性缺失")
                
                if hasattr(widget, 'model_combo'):
                    print("   ✓ model_combo属性存在")
                else:
                    print("   ✗ model_combo属性缺失")
                
                if hasattr(widget, 'language_combo'):
                    print("   ✓ language_combo属性存在")
                else:
                    print("   ✗ language_combo属性缺失")
                
                # 测试方法
                print("6. 测试组件方法...")
                if hasattr(widget, '_split_text'):
                    print("   ✓ _split_text方法存在")
                    # 测试方法调用
                    try:
                        result = widget._split_text("这是一个测试文本。\n\n这是第二段。", "txt")
                        print(f"   ✓ _split_text方法调用成功，返回 {len(result)} 个块")
                    except Exception as e:
                        print(f"   ✗ _split_text方法调用失败: {e}")
                else:
                    print("   ✗ _split_text方法缺失")
                
                app.quit()
            else:
                print("   ⚠ PyQt6不可用，跳过GUI组件测试")
                
        except Exception as e:
            print(f"   ✗ 向量化组件导入失败: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vectorizer_components():
    """测试向量化器组件"""
    print("=" * 50)
    print("测试向量化器组件...")
    
    try:
        # 测试Ollama向量化器
        print("1. 测试Ollama向量化器...")
        try:
            from src.vectorizer.ollama import OllamaVectorizer
            vectorizer = OllamaVectorizer()
            print("   ✓ Ollama向量化器创建成功")
            
            # 测试服务检查
            if hasattr(vectorizer, '_check_service_availability'):
                print("   ✓ _check_service_availability方法存在")
            else:
                print("   ✗ _check_service_availability方法缺失")
                
        except Exception as e:
            print(f"   ✗ Ollama向量化器测试失败: {e}")
        
        # 测试TextEmbedding
        print("2. 测试TextEmbedding...")
        try:
            from src.vectorizer.embeddings import TextEmbedding
            embedding = TextEmbedding()
            print("   ✓ TextEmbedding创建成功")
        except Exception as e:
            print(f"   ✗ TextEmbedding测试失败: {e}")
        
        # 测试IndexBuilder
        print("3. 测试IndexBuilder...")
        try:
            from src.indexer.builder import IndexBuilder
            config = {
                'indexing': {
                    'index_type': 'flat',
                    'metric': 'cosine',
                    'quantization': 'none'
                }
            }
            builder = IndexBuilder(config)
            print("   ✓ IndexBuilder创建成功")
            
            # 测试维度适配方法
            if hasattr(builder, '_adapt_vector_dimensions'):
                print("   ✓ _adapt_vector_dimensions方法存在")
            else:
                print("   ✗ _adapt_vector_dimensions方法缺失")
                
        except Exception as e:
            print(f"   ✗ IndexBuilder测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"向量化器组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理"""
    print("=" * 50)
    print("测试错误处理...")
    
    try:
        # 测试PyQt6错误处理
        print("1. 测试PyQt6错误处理...")
        try:
            from src.gui.widgets.vectorize import _show_message_fallback
            _show_message_fallback("测试", "这是一个测试消息")
            print("   ✓ PyQt6错误处理正常")
        except Exception as e:
            print(f"   ✗ PyQt6错误处理失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"错误处理测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始测试向量化组件修复...")
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    success = True
    
    # 运行测试
    success &= test_imports()
    success &= test_vectorizer_components()
    success &= test_error_handling()
    
    print("=" * 50)
    if success:
        print("✓ 所有测试通过！")
    else:
        print("✗ 部分测试失败，请检查上述错误信息")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
