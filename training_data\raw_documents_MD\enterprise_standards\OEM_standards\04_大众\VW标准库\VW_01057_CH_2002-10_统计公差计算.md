# VW_01057_CH_2002-10_统计公差计算.pdf

## 文档信息
- 标题：
- 作者：zhangqun
- 页数：30

## 文档内容
### 第 1 页
                   
 
 
 
 
 
康采恩标准                                                                    VW 010 57 
                                                                               版本 2002-10 
 
 
类别编号：02 52 8 
关键词：公差计算，尺寸链，统计公差 
 
尺寸链统计公差计算 
目录 
1 应用范围 ............................................................................................................................................................. 2 
2 设计任务书 ......................................................................................................................................................... 2 
3 尺寸链的数学描述 ............................................................................................................................................. 2 
4 数学上的公差计算 ............................................................................................................................................ 4 
5 统计公差计算的原理 ........................................................................................................................................ 6 
6 数学基础用于统计公差计算 ............................................................................................................................ 7 
7 统计的理想条件 ................................................................................................................................................ 9 
8 与统计理想条件的偏差 ................................................................................................................................... 11 
9  公差计算 ..................................................................................................................................................... 19 
11 关键词清单 .................................................................................................................................................... 26 
10 
参考标准 .................................................................................................................................................. 29 
附录 А .................................................................................................................................................................. 30 
 
前言 
一个总成的函数质量和制造成本很大程度上被单独零件定义的公差所影响。由于持续提升的对质量和经济的
要求，尺寸链的公差计算变得越发重要。也就是说，设计者今后应更加重视函数质量和制造成本的优化。 
由于设计者负责函数质量并且担保产品的召回，因此设计者通常倾向于定义更紧密的公差。这种方式通常会
导出为了保证其公差在生产的过程中需要很大花费。因此对于设计者统计公差计算的方法的意义在于，一方
面证明满足设计要求的函数质量，另一方面尽可能的降低制造成本。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
标准使用前要检查是否为最新版。 
电子档标准没有签字也生效。 
专职负责人                                                              标准化（EZTN,1733） 
  
 
保留所有权利。未经大众汽车集团标准部门事先批准，不得进行披露或复制。 
© 大众汽车股份公司 
 


### 第 2 页
 
 
1 应用范围 
 
此标准用于 VW 康采恩公司所有范围尺寸链的统计公差计算。它适用于任何互相的可测量的制造特性或影响
变量。 
 
2  概念 
首先根据一个简单的例子描述公差计算的概念。 
 
图 1—变速器总成线性尺寸链的例子 
（根据 Smirnow, N.W.,“技术上的数学统计”） 
 
图 1 是一个变速器，一方面轴可以自由转动，另一方面不允许有较大的轴向间隙。这个函数品质可通过轴肩
和左边套筒之间的缝隙尺寸进行描述。这个尺寸是由组成总成的 5 个单独零件决定的。因此这 5 个单独零件
形成了一个尺寸链 1），尺寸链包含了这 5 个描述隐函数尺寸，因此称为封闭尺寸。 
 
设计者第一步的任务是，根据客户的要求为总成计算具有允许公差的封闭尺寸。之后定义单独零件的标称尺
寸和公差，尺寸和公差在尽可能少的制造成本下满足设计边界条件。因此必须确定所有影响封闭尺寸的标称
尺寸并且描述其互相之间的关系（尺寸链描述）。然后可以根据数学或统计方法确定尺寸链的公差。 
 
注释：尺寸链，单独尺寸和封闭尺寸的概念不只表示几何变量，还表示任意的其他物理的变量，例如：温度，
加权等。 
 
3 尺寸链的数学描述 
通常每个任意的尺寸链在数学上都可以通过隐函数来描述，其中封闭尺寸 Ms 作为非独立选项，单独尺寸 M1
到 Mn 作为独立选项： 
 
如果一个尺寸链通过隐函数进行描述，那么公差计算明显更容易： 
 
 
 
 
1） 位置度公差，例如：在这个例子中为了便于理解可以忽视孔的垂直度偏差。 
 
 


### 第 3 页
 
 
一个经常重复出现的隐函数的特殊例子 
 
其中， 
gi：单独尺寸 Mi 的加权系数，加权系数描述了对封闭尺寸（例如：在杠杆关系或投影中）的影响强度以及
影响的方向（关于计算计数方向的符号） 
n：尺寸链中单独尺寸的数量 
 
这种形式的尺寸链是一维线性尺寸链。 
一个平面或一个空间的尺寸链可以通过公式中的一个封闭的矢量标记来进行表示， 
 
因此这个命名为一个多维度的线性尺寸链 2）。在这种情况下因为一个尺寸的影响方向已经包含在矢量变量
中，因此只考虑加权系数影响的强度。因此可以通过坐标系将矢量定义在一个平面中。 
在等式（3.1）到（3.4）中，通过使用定义的单独标称尺寸 Ni，可以得到一个封闭尺寸 Ns。 
 
图 1 中的变速器表示一个一维线性尺寸链的例子。下面的 n=5 的单独尺寸组成了一个尺寸链。 
 
其中加权系数： 
 
根据下面的规则进行定义： 
- 正号，当单独尺寸变大影响封闭尺寸变大。 
- 负号，当单独尺寸变大影响封闭尺寸变小。 
 
图 2 展示了一个非线性尺寸链的例子 
 
图 2- 非线性尺寸链皮带传送的例子 
图 2 例子中的封闭尺寸指的是要求的皮带长度 Ms，它与单独尺寸齿轮间距 M1，大齿轮半径 M2 和小齿轮半径
M3 相关。由于几何关系导出以下的非线性函数。 
 


### 第 4 页
 
 
用于 
和弧度尺寸中的角度。 
在等式（3.5）中，通过使用单独标称尺寸导出的封闭标称尺寸： 
 
在非线性尺寸链中，为了计算单独尺寸对封闭尺寸的影响，在单独标称尺寸小的变动范围内，要求将描述尺
寸链的函数线性化。在此，假设由公差定义的变动范围足够的小，以致可以忽视线性错误。线性可以根据偏
导数方法或偏差商方法 3）实现。 
 
使用偏导法计算影响单独标称尺寸的加权系数 gi。其中将尺寸链函数微分成作为影响变量的单独尺寸，尺寸
链通过隐函数被差分 4）并且在这个导数中插入单独标称尺寸（Mi=Ni）的值： 
 
 
2）一个一维尺寸链也可以用矢量公式（3.4）描述。 
3）对于这些方法，只考虑显函数。 
4）在此假设在观察的范围内函数是可差分的，在实际中通常允许给出函数。 
 
下面的导数和加权系数通过函数（3.5）和例子的标称尺寸导出： 
 
因此例子计算的加权系数显示，大齿轮半径M2 对封闭尺寸，即皮带长度，有巨大的影响。标称尺寸偏差0.1mm
对封闭尺寸有 0.39mm 变化的影响。相反齿轮间距 M1 导致的影响是微小的，因此不需要较高的制造精度。 
 
差商方法是一个数字的方法，因此适用于计算机。通过这个方法，首先通过插入指定的单独标称尺寸（Mi=Ni）
到函数中，计算出封闭标称尺寸 Ns，例如：通过在函数（3.5）中插入标称尺寸进行计算。然后计算偏差的
封闭尺寸：对于每个考虑范围内单独尺寸 Mi 按步骤将指定的标称尺寸 Ni（固定值）加上一个小的偏差值△
Mi 后插入到函数中，对于所有其他单独尺寸只插入指定的标称尺寸。然后计算封闭标称尺寸中的 Ns 的差值
并且除以单独尺寸偏差△Mi。假设其数学表达是一个显函数： 
 
 
对于公差计算而言，严重影响封闭尺寸的所有元件的标称尺寸和加权系数以及封闭标称尺寸自身已经完全描
述一个尺寸链。因此，如果非线性尺寸链函数有错误，可以通过 CAD 系统根据（3.7）确定加权系数，通过
△Mi 使单独标称尺寸变化，并且计算对封闭尺寸△Ms 的影响。 
 
4 数学上的公差计算 
常规的最小最大方法（数学方法）也考虑最差的情况，一个尺寸链的所有单独尺寸显示公差范围内的最大偏
差的标称尺寸。 
通常，一个尺寸 M 通过 
                                                         （4.1） 
规定公差，其中 
N 是一个标称尺寸 
是上下偏差（极限偏差） 
从公差尺寸中获得的最大和最小的尺寸： 
 
公差 T 5）的计算公式： 
 


### 第 5 页
 
中心尺寸 Mc（平均公差）计算公式： 
                                             （4.4） 
对于有上下偏差的公差尺寸，目标尺寸 Mo 等于中心尺寸 Mc，因此
是目标尺寸和标称尺寸之间
的差值。因此，一般来说标称尺寸不应视为目标尺寸！只有对称的尺寸公差，例如：M=30±0.1，标称尺寸
和目标尺寸才是相等的。 
例如：一个尺寸公差规定如下 
 
然后根据（4.2）获得一个最大尺寸和一个最小尺寸
 
然后根据（4.3）获得一个
的公差 
并且根据（4.4）获得一个
的中心尺寸，与标称尺寸的差值是 0.025。 
在对封闭尺寸公差 Ts 进行数学计算时，简单的计算绝对加权单独尺寸公差 Ti。 
 
其中
是线性或非线性尺寸链加权系数的绝对值（也参见等式（3.3），（3.6）和（3.7））。 
 
 
 
5）这个定义不可以用于只有下极限公差的物理变量，例如：抗拉强度。 
 
极限偏差 Ao 和 Au 的计算根据： 
 
其中通过 
 
考虑关于标称尺寸的公差区域的偏移。 
表 1 和 2 各自展示了变速器（图 1）和皮带传送（图 2）尺寸链的数学公差计算的例子。 
 
表 1—变速器线性尺寸链数学公差的计算的例子 
 
 
表 2—皮带传送非线性尺寸链数学公差计算的例子 


### 第 6 页
 
 
 
数学公差计算的优点是简便性以及结果的高安全性。缺点是，为了遵守封闭尺寸公差，必须常常对单独尺寸
公差的精密性进行确定，这样不能再满足制造的经济性或根本不可能实现。而且尺寸链越大或设计要求越高，
这个缺点的影响就越大。 
 
5 统计公差计算的原理 
对于变速器的例子（图 1），假设要求一个封闭尺寸，其公差 Ms=（1±0.3）mm 作为设计的边界条件。再进
一步假设，单独零件只有保持在表 1 中给出的公差以内才能够满足合理的费用。然后根据数学计算得到一个
公差为±0.5mm 的封闭尺寸，这个公差超过了的边界条件。 
 
现在的问题是指定的单独尺寸公差如何影响总成的功能品质或如何使其使更精湛，以及超过定义的封闭尺寸
的风险性有多高。这个问题的答案只能由统计公差计算给出。 
 
统计公差计算基于以下想法： 
概率，例如：投掷骰子得到 6 点的概率显然是 1/6。2 个骰子，都得到 6 点的概率是 1/36。4 个骰子以上，
都出现 6 点的可能很小。这个事实与尺寸链的实际尺寸类似。一个尺寸链的所有实际尺寸在各自的计数方向
上在最大允许的标称尺寸偏差内的概率小到可以忽视。因此在承担小的功能风险（有可能是错误）但遵循数
学计算的前提下，独尺寸公差通常会延伸。 
 
实际上，一个特性所有尺寸的总体经常通过正态分布的数学模型来进行描述（图 3）。 
 
测量值 
图 3—3σ-极限内外不同 σ-区域的概率说明正态分布 
 
特性值正态分布整体由两个参数确定： 
- 平均值μ，说明分布的情况。 
- 方差 σ²，它的根数（标准偏差 σ）是分布曲线宽度的标称变量（1σ=密度函数最大点和转折点之间的横坐
标间距）。 
 
在观察的极限（例如：极限尺寸）之间的图 3 中曲线下方的区域可以理解为在参数μ和 σ 的这个范围中找到
测量值的概率。因此，例如，一个测量值将发生在概率为 99.73%的μ-3σ 和μ+3σ 之内。 
（正态分布的定义和其他属性可查询 Graf,U.,Henning,H.-J.,Stange,K.,Wilrich,P.-T.“使用数学统计的公式和表格”） 
 
在各自超出极限尺寸部分 0.135%（符合极限值μ±3σ）的正态分布的单独尺寸的验收下，在带有如表 1 中


### 第 7 页
 
的相同的单独尺寸公差的变速器的例子中，根据表 3 中的统计计算，对于±0.3mm 的要求的封闭尺寸公差会
各自出现 0.0075%（75ppm，每百万的零件数）的极大的出错的概率（OFW 和 UFW）。也就是说，在既定的
条件下，在制造的一百万的变速器中的数量只有 150，其中轴向间隙超过（1±0.3）mm 的预定极限值。 
 
表 3—变速器预定单独尺寸公差的统计公差计算 
 
 
6 数学基础用于统计公差计算 
 
统计公差计算的基础是根据 Gauss 的均方差之和定律： 
 
其中， 
σs，σi：封闭尺寸的标准偏差，或 i-ten 单独尺寸的标准偏差（用于变化 Streuung 的尺寸） 
gi：i-ten 单独尺寸的加权系数（也参见等式 3.6 或 3.7） 
n：单独尺寸的数量 
 
这个定理对于整体分布在单独尺寸中是有效的。对此前提只是单独尺寸的统计独立性。 
 
分布的参数μ和变量 σ²如下定义： 
 
其中
是分布的概率密度函数适用于
并且
用于特性的 
所有值 x                                                                  （6.4） 
正态分布概率密度的例子： 
 
使用转化 
 
从（6.5）中得出标准正态分布的概率密度 
 


### 第 8 页
 
使用标准偏差 σ=1 
 
分布函数，通过 
 
进行定义，说明了密度情况，在观察的标准极限值中使用值 z 出现分位数 Zg（符号 g 用于上下极限）。对于
带有分位数
的标准正态分布的分布函数
在附录 A1 中给出表格值。其他表格值在例如
Graf,U.,Henning,H.-J.,Stange,K.,Wilrich,P.-T.“使用的数学统计的公式和表格”中查询。 
 
特性的极限尺寸和公差与下面的分布参数相关。 
 
错误概率
和
（上下极限值超出部分）以下面的方式再次与分位数 Zo 和 Zu 相关。 
 
例子：对于带有正态分布值的特性，分位数是
和
。根据等式（6.11）
和附录 A1 中正态分布函数表格值得出错误概率： 
 
（参见图 3） 
相反在给出的正态分布的情况下，可从错误概率
中确定分位数
。 
 
其中，
是一个倒数正态分数函数 6） 
例如：对于带有正态分布值的特性，最大错误概率是
。根据等式（6.12）和
附录 A2 中正态分布函数表格倒数值得出分位数： 
 
6）在技术文件，例如：Graf,U.,Henning,H.-J.,Stange,K.,Wilrich,P.-T.“使用的数学统计的公式和表格” 中，也
经常通过
进行表示。 
通过等式（6.1）和（6.10）可以获得用于统计公差计算的一般等式： 
 
其中， 
Ts，Ti：封闭尺寸或单独尺寸的公差 
封闭尺寸或单独尺寸的上下偏差的分位数与固定的错误概率和给出的概率分布有关。 
极限偏差类似于数据计算根据： 
 
进行计算，其中关于标称尺寸的封闭尺寸位置公差的位移通过， 
 
具有单独尺寸位置公差的位移 
 


### 第 9 页
 
进行考虑。 
封闭尺寸和单独尺寸的平均值通过公式： 
获取。 
其中，封闭尺寸 Ns 根据公式（3.2）或（3.3）进行计算。 
对于为单独尺寸定义具有相同错误概率的一个对称的和中心正态分布类型特殊情况，使用： 
 
其中， 
 
因此等式（6.13）和（6.17）简化成： 
 
封闭尺寸
预先规定的极限偏差中错误概率
的反向计算由等式（6.15）转
换成以下进行： 
 
并且由此与封闭尺寸正态分布相关根据等式（6.11）。 
为了评估单独尺寸公差对封闭尺寸公差的影响，感性系数 Sfi 是非常重要的，它通过公式： 
 
进行定义，其中， 
 
因此感性系数说明，在某一部分一个单独尺寸公差
的一个相对的变更总计如封闭尺寸公差
的相对变化。感性系数根据定义和等式（6.13）和（6.14）得出 
 
例如：给出 2 个具有加权系数和公差 g1=2，T1=0.2mm 和 g2=1，T2=0.2mm 的单独尺寸。在采用正态分布单
件尺寸和
各自最大公差超出部分得出： 
 
因此第一个尺寸公差的相对的缩小相对于两个尺寸总计是封闭尺寸相应缩小的 80%。因此例如首个公差的
1/2 影响封闭公差 1/4 的缩小作为第二个公差的 1/2。 
 
 
7 统计的理想条件 
 
理想条件需要具有以下条件： 
-单独尺寸在统计上是独立的； 
-链的所有单独尺寸都是正态分布的； 
-单独尺寸正态分布的所有平均值位于公差中点的位置处。 


### 第 10 页
 
此外，如果我们接受与单独尺寸相同的错误概率，我们可以从等式（6.13）和（6.14）中得出统计公差计算，
平方公差计算，的理想极限情况。 
 
 
应注意，公差 T¡必须是平方而不是极限偏差 A0 和 AU。 
 
在单独尺寸公差和加权系数总数最有利的情况下，对比平方和数学计算，产生以下关系： 
 
在具有 n 个单独尺寸公差的链线性尺寸中数学封闭尺寸公差增加期间，统计封闭尺寸在理想情况下仅增加系
数
。具有最大系数
的单独尺寸公差在相对于单独尺寸公差的封闭尺寸公差中在计算下被扩展，
最大系数
： 
 
 
例如：对于具有 16 个单独尺寸的尺寸链，其中允许封闭尺寸公差最大是 1.6 mm，因此根据数学计算，平均
每个单独尺寸公差为 Ti = 0.1 mm。然而，在理想的统计条件下，单独尺寸公差，扩张到系数
，
，没有明显超过封闭尺寸公差。 
使用这种简单的关系，可以在遵守设计相关的边界条件时粗略估计，在由问题的情况下是否有可能根据统计
规律实现单独零件公差要求的扩展或是否必须设计公差补偿，这自然代表了额外的成本系数，因此应予以避
免。


### 第 11 页
 
8 与统计理想条件的偏差 
 
然而，在实践中，必须将与理想条件之间的偏差计算在内，这些偏差会使结果失真，使得
它们更加不确定。以下与理想条件的偏差可以显示实际过程的分布： 
- 均值偏移 
- 偏斜分布 
- 混合正态分布 
- 趋势 
 
过程平均值相对于平均公差（理论值）的偏移是在未经正确修正的过程中发生的系统错误
（图 4） 
 
测量值 
图 4 - 过程平均值的偏移 
 
偏斜分布典型的发生在只通过上限值限制的特性，例如，波形的循环偏差（图 5） 
 


### 第 12 页
 
 
测量值 
图 5 - 偏斜分布


### 第 13 页
 
例如当在不同使用的机器上生产零件时，会发生混合分布（图 6） 
 
 
测量值 
图 6 - 混合分布 
 
在具有趋势的过程中，平均值随时间变化。短期内分布特性的平均值的长期变化导出合成
的平坦的分布，其最接近于梯形的函数（图 7）。这种情况是由于轴上的车床凿磨损而发生
的，这种磨损导出直径的系统性增加。在切削加工制造过程中，工具磨损或多或少地引起
趋势。 
 
 
取样编号 
图 7 - 趋势过程


### 第 14 页
 
 
 
具有趋势过程的结果分布可以通过梯形分布很好的近似描述。对于这种分布，可以使等式(6.3)导出标准偏差
和公差之间的以下关系： 
                                                                    
 
其中 Sv：上下梯形边的比例，包括边界情况 Sv = 0 表示三角形，Sv = 1 表示矩形 
 
从等式（6.19）我们再次获得分位数 
                                                                
 
在实际条件下，通过使用纵横比 Sv = 2/3 的梯形分布，可以导出与统计理想条件的大多数偏差。根据等式（8.2），
对于该分布获得分位数
。 
当然，在实践中，也可能发生梯形分布未统计覆盖的分布。这些是在称为针分布的公差带边缘处的非常窄的
分布。例如，可以通过冲压零件观察到这种分布，冲削零件可以以高重复精度（高 Cp 值）生产。知道过程
在机器寿命期间缓慢移动，过程被置于一个公差极限附近，以便利用尽可能长的机器寿命。与切屑成形加工
工艺一样，这也是一个趋势的过程。 然而，与切屑形成加工工艺相比，机器寿命在此延长数周甚至数月，
因此在此期间不断地移除部件。 因此，部件产生一段时间，其表现出大约一半公差范围的系统偏差。 在这
种情况下，这些尺寸不应该在统计上与其他尺寸相关联，而是在数学上。 
为了确定分位数 Zos 和 Zus 或者相反地确定封闭测量的误差概率 Poes 和 pues，必须知道其分布函数 F（z，），
因为这不能与各个测量分布相对应地定义，而是由各个测量分布的统计组合得出。 如果存在正态分布的单
独度量，则结果是关闭度量的正态分布，这使得可以从定义的错误概率 Pes 容易地确定分位数 Zs = Us，反之
亦然（例如，使用附录 A1 和 A2 中的正态分布表）。如果没有正态分布的单独尺寸，则必须确定封闭尺寸的
分布函数。有 3 种概率： 
- 卷积分布 
- 中心极限定理 
- 蒙特卡罗方法 
 
卷积是一种数学运算，可用于确定具有任意分布的两个独立随机变量 xs = X1 + X2 之和的分布，从而确定两
个单独维度之和的分布。它的定义是： 
 
                                                                                                       
例如，两个相等宽度的矩形分布的卷积是三角形分布。对于两个以上的随机变量，每次都使用先前卷积的乘
积，经常重复卷积。这是用于获得封闭 - 分布分布的三种方法中最精确的。由于需要很高的计算量，因此
只能通过计算机支持来管理卷积（另请参阅 Kirschling，G.;质量保证和公差） 
 
利用中心极限定理，近似于封闭 - 分布比使用卷积更简单。中心极限定理指出随着随机变量数量的增加，
具有任意分布的独立随机变量之和接近正态分布。因此，在大多数实际情况中，只要标准偏差近似相等，封
闭尺寸分布接近±3 范围内的正态分布，具有少至六个单独的尺寸。然而，为了通过正态分布获得封闭尺寸
公差的良好近似，该正态分布将被确定为最大误差概率为 2 0.003％并且具有显着不同的标准偏差，将需要
相应更大数量的单独尺寸（参见以下示例）。 
 
使用蒙特卡罗方法，使用随机数发生器模拟总成特征的测量值，并根据尺寸链描述相互链接。 基于以这种
方式产生的足够大数量的封闭尺寸（至少 1,000），然后使用值范围的合适分区来确定相对频率的分布，从而
获得所需分布的近似值。 
 
表4 给出了使用卷积对各个维度进行统计公差计算的示例，其中基本梯形分布S = 2/3，误差概率（UPE和 LPE）
在每个极限处为 0.135％。最大误差概率为 2 0.003％，封闭尺寸公差为±0.384 mm。 因此可以说，即使工
艺明显偏离理想条件，也可以非常确定地达到±0.384mm 的公差。 个体尺寸公差只需要达到 99.73％的确定
性。 
与卷积计算相比，根据通过中心极限定理的近似计算，对于相同的尺寸链和相同错误概率可获得一个±0.456 
mm 的封闭尺寸公差。 


### 第 15 页
 
表 4 - 具有梯形分布（纵横比 2：3）的单独尺寸（通过卷积计算）的预定公差的齿轮例子的统计公差计算 
 
尺寸单位为 mm 
 
 
 
编号 
名称 
加 权 系
数 
标 称 尺
寸 
上偏差 
下偏差 
UPE% 
LPE% 
分布 
敏 感 性
（%）Sf 
1 
轴 套 废
料 （ 左
边） 
 
 
 
 
 
 
梯形 
 
2 
护 套 内
部 尺 寸
（左边） 
 
 
 
 
 
 
梯形 
 
3 
护 套 内
部 尺 寸
（右边） 
 
 
 
 
 
 
梯形 
 
4 
轴 套 废
料 （ 右
边） 
 
 
 
 
 
 
梯形 
 
5 
轴 
 
 
 
 
 
 
梯形 
 
 
封 闭 尺
寸 
 
 
 
 
 
 
 
 
 
7） 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 16 页
 
9  公差计算 
按图 8 程序图进行公差计算。 
确定封闭尺寸 
 
 
定义封闭尺寸公差 
 
 
记录单独尺寸 
 
 
确定尺寸链函数 
 
 
no 
线性尺寸链? 
 
 
 
no 
 
 
各单独尺
寸公 差可
否优 化? 
 
 
 
 
yes 
优化 各单独尺 寸 
公差 
yes 
临 时  定 义  单 独 公
差  
 
线性化 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
数学公差计算 
yes 
安全 相关 封 闭尺
寸? 
 
no 
 
 
 
 
系统性单独尺寸 
偏差或相关? 
 
 
 
 
统计公差计算 
评估结果 
 
 
 
 
 
 
 
 
yes 
 
no 
结果优良? 
 
yes 
 
 
 
图. 8 — 公差计算程序图 
存档 
产 品 概 念
及 设 计 变
更  
混合公差计算 


### 第 17 页
 
确定封闭尺寸： 
描述总成通过隐函数或外观的特性，例如在第一步中确定车身缝隙，轴承间隙
或与另一个总成间的接插尺寸。如有必要，必须将封闭尺寸细分至合适的总成，
然后对其进行公差计算。 
 
 
定义封闭尺寸公差： 基于客户要求，当封闭尺寸达到最大公差时，必须通过最大偏差
样本测试来将其确定，从而确保被测组 件的正常通过隐函数或外观。 
 
记录各尺 寸： 
考虑到参考标准（例如根据 VW 010 55 的 RPS）及安装顺序，必须对封闭尺寸
产生重大影响的所 有生产和安装特性进行记录。 除形位特性外，还可包括其他物
理变量，如以 kg 为单位的总成质量， 以  N 为单位的弹力和°C 为单位的温度
等。借助加权系数将其转换为封闭尺寸的单位。 
 
根据 VW 010 56 对尺寸链的函数和位置公差的考虑应取决于基本公差原则
和公差类型，方法如 下： 
表 5 –以基于 DIN  7167 的封包要求作为基本公差原则，考虑尺寸链的函数和位置公
差 
 
公差类型 
符号 
以标称值零作
为 占位符的单
独尺寸的分布 
目标尺寸分布 
不考
虑 
线性度 
 
 
 
 
 
平面度 
 
  
 
 
圆度 
 
  
 
 
圆柱度 
 
 
 
 
 


### 第 18 页
 
线轮廓度 
 
 
 
 
面轮廓度 
 
 
 
 
平行度 
 
 
 
 
垂直度 
 
 
 
 
倾斜度 
 
 
 
 
位置度 
 
 
 
 
同轴度 
 
 
 
 
对称性 
 
 
 
 
圆偏转度（圆跳
动） 
 
 
 
 
 
注：如果没有明确的其它规定，此包容原则自动作为 VW 汽车集团的基本公差原则。 


### 第 19 页
 
 
例如，对于图 1 所示的变速器，除长度尺寸外，在尺寸链中还必须包括标称值为零的占位符，以 考
虑护套孔的垂直度以及轴和衬套横向偏离的位置公差。 
另一方面，如果在图纸中明确表明某独立原则作为基本公差原则并标注有“ISO 8015 公差” 
（ISO 8015 技术图纸，基本公差原则），则其不同于表 5，必须通过标称值为零作为占位符的各
尺 寸分布来考虑尺寸链的以下函数和位置公差： 
—      线性度 
—      平面度 
—      圆度 
—     圆柱度 
根据 DIN ISO 2692 的最大材料原则，定义的函数和位置公差原则上不在使用额外占位符的尺寸 链
中考虑。 
 
确定尺寸链函数： 如果可能，确定的单独尺寸对封闭尺寸的影响应用数学函数描述。在
线性尺寸链的情况下，只需使用以下符号规则定义加权系数： 
—            如果某个相关的尺寸的增加导出封闭尺寸增加，则为正号 
—            如果某个相关的尺寸的增加导出封闭尺寸减少，则为负号 
在非线性尺寸链中，使用 CAD 系统通过单独尺寸偏差计算对封闭尺寸的影响，则无需通过尺寸链函数计算。 
 
线性化: 
在非线性尺寸链函数中，必须使用偏微分法（3.6）或差商法（3.7）来线性化尺寸链,即必须计算加
权系数。 
 
临时定义单独尺寸公差： 
一旦尺寸链完全由标称尺寸和加权系数描述，就必须确定初始单独尺寸公差以进行公差计算。 
如果尚未明确规定这些，例如供应零件或安装装置尚未明确规定的情况下，则必须对它们进行规 
定，以便能够以较为合理的制造费用满足这些要求。 
即使对于仅具有上偏差或下偏差的公差的特性，例如两个连接件之间的公差气隙，也必须始终说 
明上下偏差以便计算公差。基于 VW 010 56 的函数和位置公差必须根据其属性分为上下偏差。 因
此，举例来说，针对上偏差需规定位置公差值，并且针对变速器衬套的横向跳动公差的情况，对
其下偏差规定值为零。 
根据 DIN ISO 2692 的最大材料原则确定的函数和位置公差必须与指定的尺寸公差进行数学组合，以形 成有
效的公差。
 
 


### 第 20 页
 
 
根据 DIN 7186，图纸还可包含单独尺寸分布的规范。如果未指定，则应适用下述“统计公差计算”
和“混合公差计算”中列出的规则。 
 
数学公差计算： 如果封闭尺寸与安全相关，则必须使用数学公差计算。 
统计公差计算： 统计公差计算必须满足以下条件： 
- 封闭尺寸并非是安全相关的特性。 
- 各单独尺寸显示没有明显的系统偏差（针在极限值附近分布）。因此对于每个特性，所有值中的至少 
50％必须在公差的一半之内。 
-  各单独尺寸彼此独立; 因此，它们之间必须没有相互影响（没有相关性）。 
 
各单独尺寸必须遵循以下规则： 
-  通常，边长比为 2 比 3 的梯形分布应作为模型分布类型的基础，因为它涵盖了与理想统计条件可能
存在偏差的大多数情况。其他分布在合理的情况下仅用作基础，例如： 
     -  当根据规范 DIN 7186 定义单独尺寸分布，或 
     -  在现有生产分布的公差测试中。 
-  通常，上下偏差的不合格百分比（误差概率）应定义为 0.135％（对应于正态分布的
）。 这 些 
不 合 格 百分 比 对 应 于  Cpk = 1 的 过 程 能 力 指 数（ 参 见 DGO ， “SPC 2 Qualit tsregel- 
kartentechnik”; 或  Dietrich ， E. ， Schulze ， A .;“Statistische Vedahren zur Maschinen- und Pro- 
zessqualifikation”），该指数代表制造过程的最低要求（有限的过程能力）。对于梯形分布，分位数 
zui = -zui =2.04 也可以简单地根据（8.2）定义。 
 
还必须考虑以下事项： 
-  作为统计计算的理想特殊实例的二次容差计算可以仅用于可行性估计。 
-  使用中心极限定理进行统计近似计算至少需要六个单独尺寸。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
遗憾的是，该标准没有定义容差区域划分的外部范围所允许得不合格百分比。 因此，它们在此 定义为每
个 0. 135％。 


### 第 21 页
 
 
混合公差计算： 
只要封闭尺寸不是安全相关的特性，在下列情况下必须使用混合公差计算： 
-   封闭尺寸受到怀疑与目标尺寸存在明显系统偏差的尺寸或值的影响（例如，对于冲孔特性）。 
对此，应进行数学计算。应对其余各单独尺寸进行统计计算，然后必须将两个部分结果相加。 
-   各单独尺寸部分相互依赖（相关）。各相关的单独尺寸必须具备简单地数学相关，并且它们的部分结果
应与剩余各单独维度保持统计相关。 
 
评估结果： 
在数学公差计算的情况下，必须积极评估指定公差范围内的封闭尺寸公差。 
 
在统计或混合公差计算的情况下，以下必须积极评估： 
- 在指定的可接受超差百分比内的封闭尺寸公差在指定的公差内，或 
- 指定的封闭尺寸公差导出的超差百分比是可接受的。 
 
建议采用以下标准来确定在封闭尺寸下哪些超差百分比 Poe 和 Pue 仍然可以接受： 
- 外观尺寸（例如：门缝）：Poe＜0.135%且 Pue＜0.135% 
- 可能会导出通过隐函数限制的封闭尺寸公差超差，例如：连杆轴承间隙的情况：Pue≤0.003% 和 
Pue<0.003%。 
优化单独 尺寸公 差： 
如果公差计算的结果收到消极评估，则必须首先检查是否可以优化单独尺寸公差而不影响生产成本。 
必须通过逐步减少单独尺寸公差进行优化- 与规划部门协商 - 直到达成经济上可行的值。在每种 
情况下，必须首先减小其灵敏度系数产生最大值的单独尺寸公差。以下示例说明了此过程对统计 公
差计算的影响（另请参见第 6 节中的灵敏度系数示例）： 
 
给定两个单独尺寸，其中加权系数和公差 g，= 2，T1 = 0.2mm 并且 g2 = 1，T = 0.2mm。假设理想条 件，
我们根据等式（7.1）得到 T =0.45 mm  的封闭尺寸公差。 第一尺寸和第二尺寸的灵敏度系数分别 为 80％
和 20％。因此，举例来说，如果第一尺寸的公差减半，则封闭尺寸公差从 0.45mm 减小到 0.28mm。 但
是，如果第二尺寸的公差减半，则封闭尺寸公差仅从 0.45 mm  减小到 0.41 mm。 
因此,相反地，其灵敏度系数导出低值的单独尺寸公差可以显着扩展而不会显着增加封闭尺寸公 
差。 
合适的细分标准，例如根据 ISO ISO 286-1 的长度尺寸的 ISO 标准公差进行细分或根据相对成本 进
行细分，为优化单独尺寸公差提供了进一步的概率。 


### 第 22 页
 
产品概念 或设计 变更： 
如果已尽可能地优化单独尺寸公差，则产品概念或设计变更必须尽量注重成本节约，以获得积极的评
估结果。 
不影响生产成本的可能变更包括： 
— 
通过优化连接顺序和安装设备来变更总成概念。 
— 
通过仔细选择参考点来减少链式尺寸的数量。 
— 
减少较大加权系数。例如，对于皮带传动（部分 3），可以通过增加皮带轮间距来减小最大的加 权系
数。 
— 
减少客户相关的封闭尺寸影响（稳健设计），例如通过避免车身缝隙的视觉比较进行减少。 
— 
增加封闭尺寸公差（必须检查影响）。 
— 
可能增加生产成本的变更包括： 
— 
变更生产过程。 
— 
公差的补偿，如可通过调整设施或自动重新调整实现。 
— 
细分为尺寸类别并选择适合总成的组合。例如，细分为尺寸类别 A，B 和 C，组合为 AC，BB 和 CA，
用于装配两个轴承壳。 
 
文件编制 ： 
文件编织必须至少包含以下信息： 
标题数据必须明确设计分配： 
— 
指定总成或封闭尺寸 
— 
图纸或 ASSY 编号或设计数据名称 
— 
计算日期 
— 
设计工程师姓名 
— 
部门 
 
尺寸链数据（以表格形式）： 
— 
单独尺寸名称 
— 
带符号的加权系数 
— 
单独标称尺寸 
— 
上下偏差 
— 
分布类型 
— 
错误百分比（上限和下限值超出百分比） 
— 
灵敏度系数 
封闭尺寸的符号必须指示是否存在间隙（缝隙）或重叠。因此必须遵守以下规则： 
— 
正号表示间隙（缝隙） 
— 
负号表示重叠 
此外，所有文件都应包含相关总成或通过隐函数组的概述，同时也应包含链式标称尺寸（尺寸链概述），
如有必要，还应包含制造概念的简要说明。 
 
 
 
 
 
 
 
 


### 第 23 页
 
11 关键词清单 
 
A 
  
Arithmetic tolerance calculation 
算术容差计算 
C 
  
Center dimension Mc, 
中心尺寸 Mc, 
Central limit lheorem 
中心极限定理 
Closing dimension Ms 
封闭尺寸 Ms 
— Distribution 
分布 
— Nominal dimension Ns, 
标称尺寸 Ns, 
— Sign rule 
符号规则 
— Tolerance Ts, 
公差 Ts, 
Compensation of tolerances 
公差补偿  
Convolution 
卷积 
Correlation 
相关性 
D 
  
Design change 
设计变更 
— That does not affect costs 
对成本无影响 
— That increases costs 
会增加成本 
Deviation 
偏差 
upper and lower As and A 
上下 As 和 A 
Dimension chain 
尺寸链 
Multi-dimensional linear 
多尺寸线性 
— Non-linear 
非线性 
— One-dimensional linear 
单独尺寸线性 
Dimension M 
尺寸 M 
Distribution 
分布 
— Mixed 
混合 
— Needle 
指针 
— Normal 
常规 
— Skew 
倾斜 
— Trapezoidal 
梯形 
Distribution function F(z/)  
分布函数 F(z/) 
Documentation 
文件编制 
E 
  
Effective tolerance 
有效公差  
Envelope requirement 
封包要求 
F 
  
Function of the probability density 
I(x) 
概率密度函数 I(x) 
Fundamental tolerancing principle 
基本公差原则 
I 
  
Ideal conditions 
理想条件 
Independence principle 
独立原则 
Individual dimension Mi, 
单独维度 Mi, 
— Nominal dimension Ni, 
标称尺寸 Ni  
— Tolerance T, 
公差 Ti 
Inverse distribution function  F”
1(p) 
逆分布函数 F''1（p) 
L 
  
Law of error propagation 
误差传播定律 
Limit deviation 
限制偏差 
Linearization 
线性化 


### 第 24 页
 
— Difference-quotient method 
差商法 
— Partial-differentiation method 
偏微分法 
M 
  
Maximum expansion factor eve.  
最大扩展系数 emax 
Maximum dimension Gu,  
最大尺寸 Gu 
Maximum material principle 
最大材料原则 
Mean value p 
平均值 p 
Minimum dimension G1  
最小尺寸 G1  
Mixed distribution  
混合分布 
Monte Carlo method 
蒙特卡罗方法 
N 
  
Needle distribution 
指针分布 
Nominal dimension N 
标称尺寸 N 
Nonconforming percentage, 
不合格百分比 
upper Pue and lower and ple 
上下 Pue 和 Ple 
Normal distribution 
正态分布 
Distribution function (u ) 
分布函数 
Probability density function ‹p(u) 概率密度函数‹p(u) 
— Ouantiles uu and ui 
Ouantiles uu 及 ui 
— Standardized normal 
distribution 
标准化正态分布 
U transformation 
U 转化 
O 
  
Optimization 
优化 
P 
  
Position tolerance 
位置公差 
Probability density function f(x) 
概率密度函数 f(x) 
Probability of error, 
误差概率 
upper poe and lower pue 
上下 pue 及 ple 
Probability p  
概率 p 
Process 
过程 
— Capability index 
能力指数 
— Minimum requirement 
最低要求 
— With trend 
趋势 
Quantiles of a distribution zu and 
z 
分布 zu 和 z 的分位数 
R 
  
Robust design 
稳健设计 
S 
  
Sensitivity factor SFi 
灵敏度因子 SFi 
Shift in the mean value of the 
process 
过程平均值的漂移 
Shift in tolerance zone position △
Ns, 
公差带位置偏移△Ns, 
Side ratio Sv 
边长比 Sv 
Size categories 
尺寸分类 
Skew distribution 
倾斜分布 
Standard deviation  
标准偏差 
Statistical independence 
统计独立性 
Statistical tolerance calculation 
统计公差计算 
Subdivision criteria 
细分标准 
T 
  
Tolerance 
公差 


### 第 25 页
 
— Effective 
有效 
— Form and position 
函数和位置 
Tolerance calculation 
公差计算 
— Arithmetic 
算术 
— Mixed 
混合 
— Quadratic 
二次 
— Statistical  
统计 
Tolerance of form 
函数公差 
Trapezoidal distribution 
梯形分布 
V 
  
Variance o2 
方差 o2  
W 
  
Weighting coefficient gi, 
加权系数 gi 
Sign rule 
符号规则 
 


### 第 26 页
 
10 参考标准 
DIN 7167 
尺寸，形式和平行度公差的关系;没有图纸说明的包容原则 
DIN 7186-1 
统计公差，定义，使用指南和图纸规范 
DIN ISO 2692 
技术图纸;形位公差;最大材料原则 
DIN ISO 286-1 
ISO 限制和适用系统;公差，偏差和适用基础 
VW  01055 
参考点系统 -RPS- 
VW  01056 
图纸;形位公差 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 27 页
 
附录 А 
A1:标准正态分布函数的表值 р= Ф(u),此处
 
 
 
 
 
 
 
 


### 第 28 页
 
 
 
 
 
A2:  逆标准正态分布函数的表值 
，此处
 
 
 


### 第 29 页
 


### 第 30 页
 
 

