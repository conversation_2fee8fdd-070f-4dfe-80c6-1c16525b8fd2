# VW_01059_3_EN_2011-11_CAD&CAM数据要求_第3部分：汽车零件用CAD系统CATIA V4.pdf

## 文档信息
- 标题：VW01059-3.pdf
- 作者：echheil
- 页数：9

## 文档内容
### 第 1 页
Klass.-Nr./Class. No. 22 63 2 
January 2005 
Requirements for CAD/CAM Data 
CAD System CATIA V4 for Vehicle Parts 
VW 
010 59-3 
Konzernnorm 
 
 
Descriptors: 
CAD, CAM, CATIA, guideline, vehicle parts, vehicle, data 
 
Page 1 of 9 
Fachverantwortung/Responsibility 
Normung/Standards (EZTD, 1733) 
 
Dr<PERSON> <PERSON>, EZTI   Tel. -76892 
Fischer 
Tel.: +49-5361-9-27995 
<PERSON>banski 
 
 
Confidential. All rights reserved. No part of this document may be transmitted or reproduced without prior permission of a Standards Department of the Volkswagen Group. 
Parties to a contract can only obtain this standard via the B2B supplier platform “www.vwgroupsuppy.com”. 
� VOLKSWAGEN AG 
Norm vor Anwendung auf Aktualität prüfen / Check standard for current issue prior to usage. 
Form FE 41 - 01.05 
Changes 
The 
following 
changes 
have 
been 
made 
as 
compared 
to 
Volkswagen 
standard 
VW 010 59-3, 2002-03: 
� 
Internet address (for downloads) changed 
� 
Section 3.1.1 changed 
� 
Section 3.1.2: new product data type TMZ (part model and part drawing) added, file name now 
with underscore instead of blank; permissible characters supplemented 
� 
Section 3.2.2: reference to ENDCHK PLOT VW added 
� 
Section 3.2.3: reference to DoLittle supplemented 
� 
Section 3.2.4: COLOR BY TYPE added 
� 
Section 3.2.5: last sentence supplemented 
� 
Section 3.3 added 
� 
Section 4: window activation removed; COLOR=NONE removed; COLOR BY TYPE removed; 
layer 188 and layer 198 layer visibility supplemented 
Previous issues 
1997-05; 1999-03; 2002-03 
Contents 
Page 
1 
Scope.................................................................................................................................. 2 
2 
Definitions ........................................................................................................................... 2 
3 
Representation of technical characteristics......................................................................... 2 
3.1 
Administrative specifications ............................................................................................... 2 
3.1.1 
Basic model......................................................................................................................... 2 
3.1.2 
Part model/part drawing, model name ................................................................................ 3 
3.1.3 
Model structure ................................................................................................................... 3 
3.1.4 
Changes.............................................................................................................................. 4 
3.1.5 
Model dimension................................................................................................................. 5 
3.2 
Geometrical and technological specifications ..................................................................... 5 
3.2.1 
Coordinate systems ............................................................................................................ 5 
3.2.2 
Views, representation of views............................................................................................ 5 
3.2.3 
Drawing, dimension, line type, texts.................................................................................... 6 
3.2.4 
Design element properties, visibility.................................................................................... 7 
3.2.5 
SOLIDS ............................................................................................................................... 7 
3.3 
Requirements for CAD models concerning the vehicle electrics and electronics ............... 7 
4 
End check, storage status................................................................................................... 8 
5 
Data check .......................................................................................................................... 8 
5.1 
Check of organizational data quality ................................................................................... 8 
5.2 
Checking the geometrical and mathematical data quality................................................... 8 
6 
Referenced standards......................................................................................................... 9 
The English translation is believed to be accurate. 
In case of discrepancies the German version shall govern. 
NOVEMBER 2011
The following change has been
made as compared to issue
2005-01:
NOTE FOR RESTRICTED USAGE ADDED.
DO NOT USE FOR NEW DESIGNS AND
DRAWING CHANGES!!!
Standards Department
U. Fischer / M. Terlinden


### 第 2 页
Page 2 
VW 010 59-3: 2005-01 
 
1 
Scope 
This standard is applicable to the entire Volkswagen Group. 
This standard provides specifications for working with the CATIA CAD system (version 4). Should 
the need arise to transfer data to other CAD systems, agreements have to be made between those 
involved in the processing of the data, for instance according to the directives set up by the 
CAD interface laboratory (EZTI department). 
This standard shall be considered a system-specific extension to parts 1 and 2 of VW 010 59. The 
specifications are: 
� 
of a functional nature, 
� 
related to representations and/or 
� 
related to quality. 
They shall be valid for designing vehicle parts. 
2 
Definitions 
Functions, commands and terms that are specific to the CATIA system are capitalized. 
CATIA homepage: 
Intranet site where links to CATIA help menus and similar can 
be found. 
Digital Mock Up (DMU): 
DMU is the description of a product and its characteristics in virtual 
computer reality. Vehicles, operating equipment, assemblies or parts 
are defined by the following: 
� 
CAD data (surfaces, solids, other design models) 
� 
Attributes (material characteristics, structure information,...) 
� 
Documents (evaluation of test results) 
DMU supplies information for evaluation processes, such as for 
example crash test simulation or collision tests; functionalities for 
performing these evaluations are not part of DMU. DMU does not 
entail administration of all information made available. 
3 
Representation of technical characteristics 
3.1 
Administrative specifications 
3.1.1 
Basic model 
The valid CATIA basic model shall be used for designing vehicle parts in new CATIA models. This 
model contains various default settings which are necessary for the application of CATIA at 
Volkswagen AG.  
Volkswagen partners and affiliated companies can find the basic model for the construction of 
vehicle parts in the Internet (http://www.vwgroupsupply.com) under “R & D Services”, “CATIA” in 
“Current System Implementation V4”. The respectively newest version shall be used. 


### 第 3 页
Page 3 
VW 010 59-3: 2005-01 
 
3.1.2 
Part model/part drawing, model name 
Due to process organization, 3D models are made available earlier than drawings. The following is 
stored in the HyperKVS (design data administration system): 
1. Part model (TM): 
 contains the binding 3D data model (SPACE); drawing 
information may also be available, however, if so, it is 
not binding 
2. Part drawing (TZ) 
 contains the binding 2D drawing  
 
a) in transparency mode (3D design model visible is 
binding and shall be used) or  
b) as a 2D drawing (3D information contained is not 
binding in this case) 
3. Part model and part drawing (TMZ), mainly used by AUDI: 
 
 contains the binding 3D design model (SPACE) and 
the binding drawing (DRAW) 
The model name has the following structure: 
Product data type: 
3 characters (e.g. TM_, ELV) 
Part number:  
16 characters 
 
 
 
1 underscore used as separator 
Designation:  
26 characters 
 
 
 
1 underscore used as separator 
Date:  
 
10 characters (DD_MM_YYYY) 
 
 
 
1 underscore used as separator 
Coordinator:  
max. 8 characters 
Blanks in part number, designation and coordinator shall be replaced with underscores. 
Example: 
TZ_0_1J0_823_105_01_DECKEL_VORN________________01_01_0004_MEIERIDA 
123                1                          1          1         
 
   1234567890123456 12345678901234567890123456 1234567890 12345678 
 
Permissible characters in model and file names are: 
A to Z, 0 to 9, underscore and hyphen;  
 
in model names also period, semi-colon and colon as well as  
 
 
*  at the beginning of a workspace name. 
NOTE: Blanks and periods as separators and fillers have been replaced with underscores due 
to CATIA version 5 and the problems arising from the use of blanks and periods in file names when 
using the Windows operating system.  
3.1.3 
Model structure 
New: SPACE design model is located in the layers 001 – 040. 
The layer structure shown in Table 1 is prescribed for designing single vehicle parts as well as 
assemblies. Other applications (such as installation/collision inspections, method planning, 
NC programming, FEM calculations) demand layer structures which are tailored to the given task. 
In order to ensure DMU performance, the scope and content of the data must be coordinated with 
the progress of development work. 


### 第 4 页
Page 4 
VW 010 59-3: 2005-01 
 
The vehicle parts relevant for production, completely described by surface models or solids and 
completely filleted, must be assigned to a defined layer range. FACES shall be grouped to form 
a SKIN. It must be possible to offset the SKIN by the sheet thickness.  
NOTE: If a SKIN can be created from an open skin group, this is regarded as the simple 
verification of the construction’s “leak tightness”. In the process sequence, SKINS are required for 
creating solids. SKINS make it easier to remove superfluous elements from a group of FACES 
(function ERASE *spc-*ski).  
Trims, clearance cuts and hole contours shall be described using FACES. 
The designer is responsible for structuring within layer ranges. 
In DETAIL workspaces, the model structure shall also comply with Table 1. Additional specification 
for COMPACT-DITTOS: only those elements that are permitted on layers 001 to 080 shall be 
visible in the DETAIL workspace. 
SURFACES on which FACES are based shall be set to NOSHOW mode. 
Layer restrictions shall also be met for elements in NOSHOW mode. 
Table 1 – Layer structure for designing vehicle parts with CATIA 
Type 
Layer 
Contents 
DMU 
DRAW+SPACE 000 
Coordinate system 
 
SPACE 
001 - 040 Vehicle part design model, *SUR, *FAC, *SKI, *SOL, 
*DIT 
DMU 
SPACE 
041 - 080 Changes, *SUR, *FAC, *SKI, *SOL, *DIT 
DMU 
SPACE 
081 - 120 Design layers available without restriction, including 
auxiliary shapes necessary for generating a design 
 
DRAW+SPACE 121 - 180 Views, sections, details 
 
DRAW+SPACE 181 - 237 Reserved for process sequence-specific needs (see 
Supplement) 
 
DRAW+SPACE 238-240 
Vehicle grid:  
238 
 
XZ grid 
 
 
 
 
239 
 
XY grid 
 
 
 
 
240 
 
YZ grid 
 
DRAW 
241-251 
Specific DRAW elements, such as:  
dimension, hatching, texts,   
text macros (250), tables, 
 
legend for drawing frame and auxiliary products 
 
DRAW 
252 
Drawing frame, optionally auxiliary frame (drawing field) 
 
DRAW 
253 
Change block/changes made and auxiliary points 
 
DRAW 
254 
Layer structure list/table of contents (optional) 
 
3.1.4 
Changes 
Changes for SPACE design models shall be located in the layers 041 – 080. 
Design layer and change layer in combination completely describe the part. The following applies 
to the change procedure: 
� 
The changes made in the SPACE design model during the status of development last released 
shall be transferred to layers 001-040. Current changes shall always be located in the 
layers 041 - 080. 
� 
Changes made on drawings shall not be represented in separate layers. These changes are 
documented in the drawing change block. 


### 第 5 页
Page 5 
VW 010 59-3: 2005-01 
 
3.1.5 
Model dimension 
MODEL 
DIMENSION 
default 
setting 
for 
the 
basic 
model 
is 
2,000 mm 
(STANDARD-MODEL-GEOMETRIC-STANDARDS -> FWD). The default MODEL DIMENSION as 
well as all other model tolerances resulting therefrom must not be changed. 
NOTE: When generating model elements with CATIA, internal computing accuracy is determined 
by the model dimension setting. Setting 2,000 mm is necessary for complying with the accuracy 
required in VW 01059 parts 1 and 2. Setting the model dimension to 2,000 mm does not mean 
that only parts up to this dimension can be designed! The dimension available for design (default 
parameter “INFINITY”) is tenfold this value, going in every direction of the model dimension, 
i.e. ± 20,000 mm. The design model to be generated shall be generated with the correct default 
setting from the start! Changing the model dimension afterwards will not increase design model 
accuracy! 
3.2 
Geometrical and technological specifications 
3.2.1 
Coordinate systems 
Design is on principle based on the vehicle or engine coordinate system (according to VW 010 59 
part 1 or VW 010 52 respectively). *AXS1 is the vehicle or engine coordinate system. 
Multiple-axis systems shall be deleted in VIEWS before the model is passed on or stored in 
HyperKVS. In DRAW mode, each VIEW may have only one h-v-coordinate system. 
SPACE axis systems shall be set to NOSHOW mode. 
SPACE coordinate systems shall comply with the “right hand rule”. 
3.2.2 
Views, representation of views 
The following VIEWS are pre-defined in the basic model drawing: 
� 
VA XZ (front view) 
� 
VA FORMAT (drawing frame) 
The designations given for the pre-defined VIEWS of the basic model shall not be changed. 
Additional VIEWS shall be designated applying the following rules: 
� 
Letter 
 
V 
for VIEW 
� 
 
 
 
A 
View (from German “Ansicht”) 
� 
S 
Section 
� 
E 
Detail (from German “Einzelheit”) 
Further descriptions (examples): 
X1000  
Section at X1000 
� VS X1000 
A-A 
 
Section A-A 
 
� VS A-A 
XY 
 
View XY 
 
� VA XY 
FORMAT 
Drawing frame 
� VA FORMAT 
Z 
 
Detail Z 
 
� VE Z 
-XY 
 
Rear view XY  
� VA -XY 
All VIEWS shall be limited with a VIEW FRAME, meaning that no VIEW shall be in “INFINITE 
FRAME” mode. The VIEW-FRAME shall be as small as possible. 
All VIEWS, including their limiting VIEW FRAMES shall lie within the drawing frame (except for the 
VIEW FRAME of the drawing frame itself).  


### 第 6 页
Page 6 
VW 010 59-3: 2005-01 
 
While no Volkswagen drawing frames are being used, two diagonally opposite points shall form the 
lower left and upper right corner of an imaginary drawing frame. 
The VIEW “VA FORMAT” containing the drawing frame and the VIEW containing the plotting frame 
(the plotting points), must not be scaled or turned. ENDCHK PLOT VW shall be used to create a 
plotting frame. The creation of empty plotting frames (without 2D elements) shall be avoided. 
The AUXVIEW function shall be used for deriving drawings from skin designs. It is permissible to 
use AUXVIEW2 for solid designs. 
NOTE: For cutout enlargements, VIEWS with differing scale factors are defined as “SAME BGD”. 
As a result, CATIA will display a text visible in the cutout enlargement in the same height as in the 
original VIEW. Following an IGES conversion, however, these texts are scaled just like the 
represented design model in the receiving system. Consequence: this results in a different 
representation in the receiving system compared to CATIA. 
VIEWS devoid of content shall be deleted (exception: VIEWS of the basic model). 
Sections and views shall be derived from the SPACE mode. Every drawing view and every section 
shall be assigned its own specific VIEW. 
3.2.3 
Drawing, dimension, line type, texts 
Drawings shall only be changed in the CAD system in which they have been generated. 
When storing in the HyperKVS system, the CATIA model shall contain only one DRAFT, i.e. for 
each DRAFT a separate model file shall be created. 
Drawing 
frames 
and 
text 
macros 
are 
made 
available 
in 
the 
DETAIL-LIBRARY 
VW_AUDI_RAHMEN. Volkswagen partners can download the LIBRARY from the Internet, URL: 
http://www.vwgroupsupply.com  
The LIBRARY is exported and packed using gzip and must be imported into an empty LIBRARY. 
External CATIA users with access to the HyperKVS system are provided with an exported 
LIBRARY. 
Part number: “C.VW-.RAH.000” 
Maximum drawing height shall be 841 mm (A0). 
Dimensioning elements shall only be generated or changed with the designated dimensioning 
functions (DIMENS2). Afterwards, extension lines and arrowheads shall not be changed 
separately. 
Mark-up arrows generated using the MARKUP – ARROW function must always be TYPE 1, see 
Figure 1. 
 
Figure 1 – Permitted arrow type in MARKUP function 
No more than 4 line widths and 4 standard line types (line fonts) shall be used. Line types defined 
by the designer shall not be used. 
Only the default fonts of the basic model are permissible. Do not use German umlauts. Use “ss” 
instead of “ß” in German. Other fonts, especially those defined by the designer, shall not be used. 
TYPE 1 


### 第 7 页
Page 7 
VW 010 59-3: 2005-01 
 
For the Volkswagen and Volkswagen Commercial Vehicles brands, the following applies: texts in 
CAD drawings must be written in German and in English. Only the GII tool DoLittle shall be used 
for bilingual drawing note representation. DoLittle is a tool for creating multilingual CAD designs on 
the basis of a standardized, Volkswagen-specific text catalogue; within the Volkswagen Group 
DoLittle is part of the CATIA installation. Changes to DoLittle-generated texts must be performed 
only in consultation with the Technical Translations Team EZTD-6. Changes to these texts within 
the CAD model performed without using DoLittle are impermissible. 
3.2.4 
Design element properties, visibility 
Storing (in the HyperKVS system) is performed in COLOR=NONE and COLOR BY TYPE mode 
where possible in order to preserve model structure visualization with different colors. 
Prior to passing on the model or storing it in the HyperKVS system, the NOSHOW area shall be 
cleaned. Only the SPACE axes and the corresponding design models shall be found in the 
NOSHOW mode, e.g: 
� 
SURFACES of FACES 
� 
generating design models of SOLIDS 
The NOPICK area shall be emptied before storing. 
NOTE: During transfer using IGES, elements which have been protected against unintentional 
deletion with NOPICK are changed into NOSHOW mode which falsifies the content. 
3.2.5 
SOLIDS 
The SOLIDE – UPDATE function must always be executed before storing solids. 
NOTE: In DMU, simplified faceted models (LITE) and three-dimensional measurements of the 
parts (“boxes”) are calculated from the precise, fully described 3D models (TM). If no SOLIDE – 
UPDATE has been carried out, this may cause false calculations. 
Mock up solids (SOLIDM) lacking HISTORY shall not be used. Exception: standard parts. 
3.3 
Requirements for CAD models concerning the vehicle electrics and electronics 
CATIA models of components for vehicle electrics and vehicle electronics must include clear 
representations of the plug connections. Connector coding, joining direction, description of plug 
and socket, number and definition of pins or cavities as well as defined cable exits must be 
identifiable. Connector coding is modeled in a simplified manner. The following must be considered 
for a complete representation: 
� 
In order to define the joining direction, a line of 10 mm length is drawn along the symmetry axis 
of pin 1 or cavity 1 respectively. The line shall start at the contact surface. 
� 
In order to represent the pins and cavities, simplified 2D views are generated on the contact 
surface plane. 
� 
In order to define the pin numbering, a line of 10 mm length is generated on the contact 
surface plane, starting on the symmetry axis of pin 1 or cavity 1 respectively and running in the 
direction of pin 2 or cavity 2 respectively. 
� 
Cable exits shall be represented by lines measuring 10 mm in length that start on the model 
surface or the point of exit and run away from the model. 
� 
VW 010 59-3 Supplement 1 must be observed in regard to layer assignment.  


### 第 8 页
Page 8 
VW 010 59-3: 2005-01 
 
4 
End check, storage status 
The following inspections must be performed prior to passing the model on or storing it in the 
HyperKVS system: 
� 
Model “cleaning”! It is absolutely necessary for the performance of DMU that any superfluous 
design models that are not required for the vehicle part description are deleted from the model. 
� 
The layer structure shall be complied with. 
� 
Activate CATCLEAN in order to check for incorrect elements (enter:/CLN). 
� 
Activate MASTER-WORKSPACE. 
� 
Perform VIEW-REFRAME (necessary for automatic plotting). 
� 
Activate layer 000. 
� 
Activate coordinate system *AXS1, set SPACE axes to NOSHOW mode. 
� 
Delete UNUSED DETAILS. 
� 
Delete UNUSED SYMBOLS. 
� 
Delete empty SETS. 
� 
Delete empty VIEWS. 
� 
DR/SP switch: DR if drawing exists, otherwise SP. 
� 
Generate text positioning points in NOSHOW. 
� 
Only SPACE model exists: Activate 'DMU' general filter (layers 001 – 080, 188 and 198 visible, 
necessary for DMU). 
� 
Execute SOLID – UPDATE function. 
5 
Data check 
5.1 
Check of organizational data quality 
The check program ENDCHK checks for adherence to the points listed in Sections 1 to 4. The 
checking of individual points depends on the product data type. It is strongly recommended to 
perform this check regularly accompanying development. 
5.2 
Checking the geometrical and mathematical data quality 
Checking of geometrical and mathematical data quality is to be performed using a check program 
certified by the Association of German Automotive Industry (VDA). In the scope of this standard, 
the check is performed using the VALIDAT program. 
Checking criteria are defined according to VDA 4955 (see VW 010 59-2). 


### 第 9 页
Page 9 
VW 010 59-3: 2005-01 
 
6 
Referenced standards*) 
VDA 4955 
Umfang und Qualität von CAD/CAM-Daten (Scope and Quality of CAD/CAM 
Data – only available in German) 
VW 010 52 
Technical Drawings; Representations 
VW 010 59-1 
Requirements 
for 
CAD/CAM 
Data; 
Representation 
of 
Technical 
Characteristics 
VW 010 59-2 
Requirements for CAD/CAM Data; Geometrical and Mathematical Data 
Quality 
General conditions concerning Volkswagen AG partners involved in the product engineering 
process 
can 
be 
found 
at 
http://www.vwgroupsupply.com 
under 
R & D Services, Relevant Documentation, CAD/CAM Data exchange. 
                                                 
*) 
In this section, terminological inconsistencies may occur as the original titles are used. 

