# VW_01059_6_1_DE_2011-11_CAD&CAM数据要求_第6_1部分：CAD系统CATIA V5_术语.pdf

## 文档信息
- 标题：
- 作者：
- 页数：10

## 文档内容
### 第 1 页
Konzernnorm
VW 01059-6-1
Ausgabe 2015-11
Klass.-Nr.:
22632
Schlagwörter:
CAD, CAM, CATIA, Begriffe, Abkürzung
Anforderungen an CAD/CAM-Daten – CAD-System CATIA V5-6
Teil 1: Begriffe
Frühere Ausgaben
VW 01059-6 Beiblatt 1: 2005-07, 2006-12
Änderungen
Gegenüber der VW 01059-6 Beiblatt 1: 2006-12 wurden folgende Änderungen vorgenommen:
–
Status des Dokuments von Beiblatt zu Norm geändert
–
Version des CAD-Systems CATIA von V5 in V5-6 geändert
–
Fachverantwortung geändert
–
Abschnitt 2 auf 3 Unterabschnitte geteilt
–
Abschnitt 2.1 : bei folgenden Begriffen „CEG (CAD-Einsatz-Gruppe)“, „CEG-Level“, „DMU-
CATPart“, „Relational Design“ Begriffserklärungen angepasst; Begriffe „Feature Catalog“,
„GRC“, „VWGRCLite“, „OEM-Startpart“ und „PCA“ hinzugefügt; Begriff „CCP-Link “ entfernt;
Begriff „Verbindungs-Elemente- Management“ in Abschnitt 2.3 versetzt
–
Abschnitt 2.2 „Systeme rund um CATIA“ neu
–
Abschnitt 2.3 „Tools rund um CATIA“ neu
Anwendungsbereich
Diese Norm enthält Abkürzungen und Begriffe aus dem CAD-System CATIA V5-6 und dessen Um‐
feld. Es soll den Umgang mit und das Verständnis für die Norm VW 01059-6 erleichtern.
1  
Norm vor Anwendung auf Aktualität prüfen.
Die elektronisch erzeugte Norm ist authentisch und gilt ohne Unterschrift.
Seite 1 von 10
Fachverantwortung
Normung
K-SIPE-2/3
Stefan Biernoth
Tel.: +49 5361 9-48896
EKDV/4 Norbert Wisla
EKDV
Tel.: +49 5361 9-48869
Maik Gummert
Alle Rechte vorbehalten. Weitergabe oder Vervielfältigung ohne vorherige Zustimmung einer Normenabteilung des Volkswagen Konzerns nicht gestattet.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Seite 2
VW 01059-6-1: 2015-11
Begriffe und Abkürzungen
CATIA-spezifische Begriffe und Abkürzungen
Die Groß-/Kleinschreibung wurde meist an das Deutsche angelehnt.
 
Adapter
Der Adapter beinhaltet sowohl Originalgeometrie als auch Referenzen zu
führenden CATParts. Ein Adapter kann entweder die Eingangsinformation
(Input-Daten) für eine Konstruktion beinhalten oder Ausgangsdaten (Out‐
put-Daten) für nachfolgende Konstruktionen oder Prozesse (z. B. DMU-
CATPart) bereitstellen. Er dient damit als Schnittstelle im Konstruktionspro‐
zess und kann diverse vom Konstrukteur festzulegende Ausprägungen ha‐
ben. Bei der Verwendung von Adaptern ist die hierarchische Anordnung
der Referenzen zu beachten.
 
Advanced Part    
 Design
Als „Advanced Part Design“ bezeichnet man die Konstruktion eines Bau‐
teils innerhalb eines CATParts unter der Nutzung von Parametrik und As‐
soziativität, aber ohne Verwendung von externen, d. h. CATPart-übergreif‐
enden Referenzen.
 
Assoziativität
Als Assoziativität bezeichnet man Abhängigkeiten zwischen geometrischen
Elementen und/oder Parametern innerhalb eines CATParts oder über meh‐
rere CATParts hinweg mit Hilfe von gerichteten Beziehungen (Eltern-Kind-
Beziehungen). Die CAD-technische Ausprägung der CATPart-übergreifen‐
den Abhängigkeiten ist der Link bzw. die externe Referenz.
 
Boundary
Representation
(BRep)
Beschreibung eines Volumenkörpers (Solid), der durch seine Berandungs‐
elemente – Ecken, Kanten und Flächen (Vertex, Edge und Face) – definiert
ist. Neben der geometrischen Beschreibung sind auch die Nachbarschafts‐
beziehungen (Topologie) der Elemente Bestandteil der Darstellung. BRep
ist neben dem auf Bool‘schen Operationen von Primitivkörpern basieren‐
den CSG-Modell (constructive solid geometry) die heute vorwiegend einge‐
setzte Methode zur rechnerinternen Abbildung komplexer Volumenkörper.
 
CAD-Typ
Der CAD-Typ wird im Rahmen der Namenskonvention (Dokumentenbenen‐
nung) zur Kennzeichnung der Funktion innerhalb der Produktstruktur ver‐
wendet. Beispiele:
DMU-Adapter - CAD-Typ-Kennzeichnung: DMU
Adapter für Berechnung - CAD-Typ-Kennzeichnung: CAE
 
 
catalog
Der catalog (Katalog) ist ein eigenständiges CATIA V5-6 Dokument, wel‐
ches zur strukturierten Ablage von CATIA V5-6 Objekten und Dokumenten
genutzt werden kann. Der catalog kann 2D Standard-Geometrie wie Rah‐
men und Aufkleber verwalten. Im 3D dient der catalog z. B. zur Strukturie‐
rung von Power Copies, UDFs, CATParts und Knowledgeware Elementen.
 
2  
2.1  


### 第 3 页
Seite 3
VW 01059-6-1: 2015-11
CATAnalysis
Die CATAnalysis ist ein CATIA V5-6 Dokument, welches bei der Erstellung
von Finite-Elemente-Analysen entsteht. Es beinhaltet den Verweis auf die
zu analysierende Teile oder Produkte, die Konstruktionsverbindungen für
die Analyse und die Spezifikationen des Finite-Elemente-Modells.
 
CATDrawing
Die CATDrawing ist das CATIA V5-6 Dokument für die 2D Zeichnungser‐
stellung wie auch die Zeichnungsableitung aus 3D Bauteilen. Bei der Zeich‐
nungsableitung wird ein gerichteter Link von der CATDrawing zum Ur‐
sprungs-CATPart oder Ursprungs-CATProduct aufgebaut.
 
CATPart
Das CATPart ist das CATIA V5-6 Dokument, in dem die beschreibende 3D
Geometrie in Form von Drahtgeometrie, Solids und Flächen definiert und
erzeugt wird. Die Strukturierung der Geometrieelemente innerhalb des
CATParts erfolgt über den PartBody oder weitere Bodies und/oder Geo‐
metrical Sets.
 
CATProcess
Das CATProcess Dokument in CATIA V5-6 spiegelt die Fertigungssichtwei‐
se auf ein mechanisch zu bearbeitendes Bauteil wider. Der darin enhaltene
PPR-Baum referenziert ein oder mehrere CATParts oder CATProducts, die
zur Bearbeitung verwendeten Ressourcen (Maschinen, Werkzeuge) und
definiert die mechanischen Bearbeitungen als NC-Programmierung z. B. für
Bohren und Fräsen, Erodieren und Draht-, Laser- oder Wasserstrahlschnei‐
den. Aus einem CATProcess-Dokument können weitere neutrale oder ma‐
schinenspezifische Steuerdaten abgeleitet werden.
 
CATProduct
Das CATProduct ist das CATIA V5-6 Dokument, welches mehrere CAT‐
Parts, Components sowie weitere CATProducts als Unterstrukturen enthal‐
ten kann. Die in der CATProduct Struktur enthaltenen Komponenten kön‐
nen als CATPart- oder CATProduct-Instanz, mit zugehörigen gespeicherten
Dokumenten, oder als Component ohne repräsentierendes Dokument ein‐
gesetzt werden. Als CATProduct wird eine Baugruppe oder die Konstrukti‐
onsumgebung für ein Bauteil aufgebaut. Das CATProduct referenziert auf
CATParts und untergeordnete CATProducts und verwaltet deren räumliche
Lage.
 
CCP                       
(Connector Connecti‐
on Point)
Der Connector Connection Point (CCP) wird bei der Konstruktion elektri‐
scher Leitungen und Rohrleitungen verwendet. Er markiert den jeweiligen
Anfangs- und Endpunkt der Leitung.
 
 
 
CEG                       
(CAD-Einsatz-Grup‐
pe)
Die CEG ist eine Arbeitsgruppe der deutschen Automobil-Hersteller. Die
Arbeitsgruppe stimmt Systemeinstellungen, Release-Anforderungen und
die Release-Einsatztermine der einzelnen Automobil-Hersteller aufeinander
ab.
 
CEG-Level
Von der CEG in einer gemeinsamen Arbeitsgruppe definierte Einstellungen
für die Zeichnungserstellung. Die jeweiligen CEG-Level beinhalten unter‐
schiedliche Vorgaben bzgl. Symbole, Liniengruppen, Schriftgrößen, usw.


### 第 4 页
Seite 4
VW 01059-6-1: 2015-11
 
Component
Component ist eine CATIA V5-6-Bezeichnung für einen in der Produkt‐
struktur des CATProducts eingefügten Bauteilknoten.
 
Context
Ein Context entsteht, wenn innerhalb eines CATProducts von einem CAT‐
Part zu einem anderen CATPart Links aufgebaut werden. CATIA V5-6 geht
davon aus, dass weitere Links nur in dieser Produktstruktur erzeugt werden
sollen. Als sogenannter Root-Context wird der oberste im CATIA-Fenster
geladene Produktknoten bezeichnet.
 
Context-Verhalten
Als Context-Verhalten wird das unterschiedliche Verhalten eines CATParts
oder CATProducts bezeichnet, abhängig davon ob es sich im Context be‐
findet oder nicht. Im Context besitzt das Dokument die volle Funktionalität,
während außerhalb des Contexts das Synchronisieren vorhandener und
die Erzeugung neuer externer Links nicht möglich ist.
 
Design in Context
Der Begriff „Design in Context“ bezeichnet eine Arbeitsweise, bei der direkt
in der Konstruktionsumgebung gearbeitet wird, wobei die reale Lage be‐
nachbarter Bauteile berücksichtigt wird. So konstruierte Bauteile sind in der
Konstruktionsumgebung korrekt positioniert. Mit CATIA V5-6 werden die
Links (Referenzen) zwischen den Dokumenten in der Produktstruktur durch
Publication der Elemente und die Funktionen Copy und Paste special as
Result with Link erstellt, dabei entstehen Links des Typs Import und Con‐
text.
 
Design Table
Die „Design Table“ ist eine Wertetabelle zur Steuerung der Parameter in ei‐
nem CATIA V5-6 Dokument. Sie wird als separate Datei (im Excel- oder
Text-Format) erzeugt und gespeichert. Die Inhalte einer Design Table kön‐
nen in das Dokument eingelagert werden, um es von der externen Datei zu
entkoppeln. Dabei geht die Möglichkeit zur Modifikation der Design Table
verloren und kann erst mit Anbindung der externen Datei wieder hergestellt
werden.
 
DL-Name                 
(Dynamic Link Name)
Bei der Verwendung von Dynamic Link Names (DL-Names) werden die ab‐
soluten Pfadangaben in einem Dokument durch einen virtuellen Dateina‐
men (DL-Name) ersetzt. Innerhalb der Referenz wird dann nur dieser virtu‐
elle Name gespeichert. In CATIA V5-6 wird eine Konfigurationsdatei (DL-
Names.CATSettings) bereitgestellt, die ggf. vor der Verwendung von CA‐
TIA V5-6 Dokumenten angepasst werden muss. Die Verwendung von DL-
Names vereinfacht und beschleunigt den Zugriff auf Dokumentenverzeich‐
nisse. Außerdem erleichtert die Verwendung von DL-Names den Daten‐
austausch zwischen den unterschiedlichen CATIA V5-6 Betriebssystem-
Plattformen (Windows, UNIX).
 
DMU-CATPart
Das DMU-CATPart enthält die bauteil-beschreibende Geometrie für den
DMU-Prozess. Die Ausprägung der Geometrie wird durch den Konstrukteur
nach der VW 01059-6-3 bestimmt.
 


### 第 5 页
Seite 5
VW 01059-6-1: 2015-11
Explicit Design
Das „Explicit Design“ als Arbeitsmethode sieht die Konstruktion der Bauteil‐
geometrie in einem CATPart ohne die Verwendung von Parametrik und As‐
soziativität vor.
 
Feature
Als Feature wird ein geometrisches oder funktionales Objekt zur Erzeugung
der 3D Geometrie bezeichnet. Das Feature enthält eine Spezifikation aus
Elternelementen und Parametern sowie einen Operator als Vorschrift, wie
mit der Spezifikation verfahren werden soll. Das Ergebnis wird in Form von
Elementen und Subelementen dargestellt.
Features (Konstruktionselemente) sind Objekte, die neben der reinen Geo‐
metriebeschreibung auch zusätzliche Attribute, wie z. B. Fertigungsvor‐
schriften enthalten. Technische Standardelemente (Bohrungen, Fasen, Ta‐
schen, Nuten, etc.) können so als parametrisiertes Feature in eine Kon‐
struktion eingefügt und ggf. für den Fertigungsprozess ausgewertet
werden.
 
Feature Catalog
Eine Sammlung von Features zu einem bestimmten Thema.
 
Geometrical Set
Ein „Geometrical Set“ ist ein Strukturierungselement im Spezifikations‐
baum, unter dem Nicht-Solids (also Draht- und Flächenelemente) abgelegt
sind. Bis einschließlich CATIA V5R12 wurden diese als OpenBodies be‐
zeichnet.
 
Grundmodell
Der Begriff Grundmodell stammt aus CATIA V4 und sollte unter CATIA
V5-6 nicht mehr verwendet werden. Die im CATIA V4 Grundmodell defi‐
nierten Einstellungen und Vorgaben werden unter CATIA V5-6 über die
CATIA-Group-Referenz (GRC), die CATIA-Settings, ein Strukturdokument
und den CEG-Level festgelegt.
 
GRC
Gruppen Referenz CATIA, eine Zusammenstellung von aktualisierter Soft‐
ware von CATIA und CATIA betreffende Tools, die konzernweit verteilt wird
und so den Datenaustausch im Konzern stark erleichtert.
 
Import
Import bezeichnet den Link-Typ, der beim Arbeiten in einer Produktstruktur
(„Design in Context“) entsteht und auf das Ursprungs-Bauteil referenziert
 
Instanz
Als Instanz wird ein im CATProduct mehrfach verbautes Original-Bauteil
bezeichnet. Im CATProduct werden die Referenz auf das Original-Bauteil
und die jeweiligen Positionen und Orientierungen der Instanzen gespei‐
chert. Änderungen im Original-Bauteil wirken sich auf alle Instanzen aus.
Änderungen an den Instanzen sind dagegen nicht möglich. Jede Instanz
wird durch einen „Instance Name“ gekennzeichnet.
 
Konstruktions-       
CATPart
Das Konstruktions-CATPart ist das CATIA V5-6 Modell eines realen Bau‐
teils. Das CATPart enthält die kopierte Geometrie aus den Inputdaten und
die gesamte Geometrie der parametrisch-assoziativen Konstruktion eines
modellierten Bauteils.


### 第 6 页
Seite 6
VW 01059-6-1: 2015-11
Konstruktions-       
CATProduct
Das Konstruktions-CATProduct fasst das Konstruktions-CATPart, diverse
Eingangs- und Ausgangsdaten in den Adapter CATParts sowie den DMU-
CATPart zu einem Paket zusammen, welches sämtliche Geometrieinforma‐
tionen des realen Bauteils umfasst.
 
Master-Modell
Das Master-Modell kann sowohl ein CATPart wie auch ein CATProduct
sein, welches eine detaillierte parametrisch-assoziative Konstruktion für ein
Bauteil bzw. eine Baugruppe enthält (einschließlich Formeln und Regeln).
Aus dem Master-Modell wird durch Austausch und/oder Anpassung von
Elementen, durch Änderung von Parametern und durch Einsatz von Relati‐
onen eine neue Konstruktion erstellt.
 
Multi-Modell-Konzept
Das Multi-Modell-Konzept beschreibt die Aufteilung eines physischen Bau‐
teils auf mehrere CATParts. Es ermöglicht die Parallelisierung der Entwick‐
lung des Bauteils durch mehrere Konstrukteure („Concurrent Engineering“).
Die Links werden in einem übergeordneten CATProduct über Publikationen
verwaltet.
 
OEM-Startpart
Das OEM-Startpart ist ein von den deutschen Automobilherstellern (AUDI,
BMW, DAIMLER, Porsche und Volkswagen) gemeinsam entwickeltes
Struktur-CATPart
 
OpenBody
Ein OpenBody ist ein Strukturierungselement im Spezifikationsbaum, unter
dem Draht- und Flächengeometrie abgelegt wird, ab CATIA V5 Release 13
Geometrical Set genannt, siehe dort.
 
Parametrik
Parametrik heißt, die Gestalt und Position eines Objektes mittels variabler
Parameter zu beschreiben und bei Bedarf zu modifizieren. Es sind sowohl
Beziehungen innerhalb eines Körpers als auch zwischen verschiedenen
Körpern möglich. Die Parameter werden im CATPart definiert und gespei‐
chert. Sie können durch den Anwender nachträglich editiert und geändert
werden.
 
PartBody
Der PartBody ist ein Strukturierungselement im Spezifikationsbaum des
CATParts, unter welchem Solidfeatures und zugehörige Sketche abgelegt
werden. Ein CATPart beinhaltet im Spezifikationsbaum genau einen Part‐
Body, der immer an erster Stelle steht. Die zusätzlichen Strukturierungsele‐
mente tragen die Bezeichnung Body. Jeweils zwei Bodies können über bo‐
olesche Operationen mit einander verknüpft werden.
 
PCA                       
(Process Chain Adap‐
ter)
Der Prozesskettenadapter bündelt und strukturiert alle für die Datenabneh‐
mer notwendigen geometrischen und nichtgeometrischen Informationen.
 
 
 
 


### 第 7 页
Seite 7
VW 01059-6-1: 2015-11
PowerCopy
In einer PowerCopy wird mehrfach verwendbare Geometrie (z.B. Sicken,
Verprägungen) zunächst allgemeingültig aus einer Anzahl von geometri‐
schen Elementen, Formeln, Bedingungen usw., in einem eigenen CATPart
definiert. Beim Einsetzen der PowerCopy in ein anderes CATPart wird die
zuvor erzeugte Geometrie den neuen Randbedingungen angepasst.
 
Process Chain        
Integration
Unter „Process Chain Integration“ versteht man die Implementierung des
„Relational Design“ über die gesamte Prozesskette, d. h. von der Entwick‐
lung bis zur Produktion.
 
Publication              
(Publikation)
Als Publication wird die gezielte Veröffentlichung von Elementen verstan‐
den, auf die im Rahmen des Design in Context von anderen CATParts zu‐
gegriffen werden kann. Bei der Definition von Links und der Positionierung
über Assembly Constraints können nur diese definierten veröffentlichten
Elemente verwendet werden (entsprechende Einstellungen vorausgesetzt).
Durch die Verwendung von Publications wird der Verlauf der Links über die
Produktstruktur abgebildet und ist vom Dateisystem abgekoppelt.
 
Relational Design
Als „Relational Design“ beschreibt man die Arbeitsweise, bei der neben
Nutzung von Assoziativität und Parametrik innerhalb eines CATParts auch
CATPart-übergreifend mit externen Referenzen und/oder Constrains die
Bauteile definiert werden. Dazu wird in einer Produktstruktur gearbeitet, die
oftmals nicht der Stücklistenstruktur entspricht.
 
Skelett                       
(Skeleton)
Ein Skelett enthält Originalgeometrie und Referenzen für den weiteren Kon‐
struktionsprozess. Das Skelett (Skeleton) ist in der Regel ein CATPart. Es
wird verwendet, wenn zwei oder mehr Konstrukteure die geometrische
Ausprägung ihrer Bauteile in Teilbereichen gemeinsam definieren und nut‐
zen.
 
Solid
Als Solid wird zunächst ein mit der entsprechenden CATIA-Funktion erstell‐
ter Vollkörper bezeichnet.
Als Output für das DMU kann das auch eine durch eine Aufdickungsregel
ergänzte Fläche sein (thick-surface oder Fläche mit Materialvektor).
 
Startmodell
Der Begriff Startmodell stammt aus CATIA V4 und wird unter CATIA V5-6
nicht mehr verwendet. Sämtliche Einstellungen und Vorgaben werden unter
CATIA V5-6 über die CATIA-Referenz, die Settings, das Struktur-CATPart
und den CEG-Level festgelegt.
 
Struktur-CATPart
Das Struktur-CATPart enthält einen vordefinierten Spezifikationsbaum so‐
wie voreingestellte Standards für die Konstruktion. Die Struktur des Spezifi‐
kationsbaumes wird von den Fachbereichen vorgegeben und kann bei Be‐
darf durch den Konstrukteur erweitert werden.
 
 
 


### 第 8 页
Seite 8
VW 01059-6-1: 2015-11
Tolerant Modelling
Als „Tolerant Modelling“ wird in CATIA V5-6 die Möglichkeit bezeichnet, in‐
nerhalb verschiedener Feature-Funktionen (im Generative Shape Design)
von der vorgegebenen Standard-Genauigkeit abzuweichen. Dies verein‐
facht die Weiterverwendung von importierten CAD-Daten mit geringeren in‐
ternen Genauigkeiten. Außerdem werden mit dem Tolerant Modelling Tan‐
genten- und Krümmungsfehler minimiert, die durch aufeinander folgende
Operationen in CATIA V5-6 auftreten könnten.
 
Unique Universal
IDentifier                  
(UUID)
Der „Unique Universal IDentifier“ (UUID) ist eine eindeutige Kennung. CA‐
TIA V5-6 nutzt sie zur Kennzeichnung der Dokumente. Der UUID ist die
Grundlage für das Multi-Modell-Konzept und dient zur Vermeidung von Na‐
menskonflikten.
 
User Defined Feature
(UDF)
Die Erstellung eines User defined Features (UDF) ist identisch der einer
PowerCopy. Beim Einsetzen des UDF sind die Modifikationsmöglichkeiten
eingeschränkt, da Teile der Geometrie vom Ersteller gegen Änderungen
durch den Nutzer gesperrt wurden. Das UDF erscheint im Spezifikations‐
baum als ein Feature.
VWGRCLite
VWGRCLite ist eine Standardarbeitsumgebung auf Basis von CATIA V5-6
für Partnerfirmen der VOLKSWAGEN AG
Systeme rund um CATIA
CONNECT
CONNECT ist die für den VW-Konzern angepasste PLM-Standardsoftware
„Teamcenter" von SIEMENS PLM. CONNECT ist die zentrale Produktdaten
Plattform für den gesamten Konzern mit Schnittstellen zu TI-Syncro, CA‐
TIA, KVS u.a. Systemen. Ziel ist es, konzernweit durch CONNECT einen
gemeinsamen Datenpool aufzubauen, der durchgängig die Entwicklung
von Fahrzeugprojekten sowie markenübergreifende Modulentwicklungen
über alle Marken, Geschäftsbereiche und Standorte des VW Konzerns hin‐
weg unterstützt. CONNECT ist ein Client-Server-System, weshalb für den
im Konzern eingesetzten Teamcenter-eigene Client ebenfalls der Begriff
CONNECT geläufig ist.
 
KVS
Konstruktionsdaten-Verwaltungs-System
 
LCA
Life Cycle Assessment, ein TDM-System der Fa. Dassault.
 
GRICOS
Group Reference Installation COnversion Server, GRICOS ist die Arbeits‐
schicht eines zentralen weltweiten genutzten Konvertierservices an allen
Konzernstandorten mit Konvertierinstanzen.
 
GINA
GINA ist die Steuerschicht eines zentralen Konvertierservices, der an allen
Konzernstandorten mit eigenen Konvertierinstanzen Verwendung findet.
 
PDM
Produkt Daten Management, datenbankbasierendes Verwaltungs- und
Kommunikationssystem für Dokumenten (CAX, Office, Stammdaten,
etc…), die innerhalb des Produktentstehungsprozesses erstellt werden.
 
2.2  


### 第 9 页
Seite 9
VW 01059-6-1: 2015-11
TDM
Team Data Manager, ein System zur Verwaltung von CAD-Daten innerhalb
des Konstruktionsprozesse. Wird in kleineren Teams und abgegrenzten
Konstruktionsumfeld genutzt, vergleiche PDM-System. Bei Volkswagen
wird CONNECT als TDM-System genutzt.
 
VPM
Virtual Product Model (VPM) ist eine TDM-Anwendung. Wird zur Kollisions‐
untersuchung verwendet.
Tools rund um CATIA
KVS-Plugin
Das KVS-Plugin ist eine Applikation zum Datentransfer von CATIA-V5-6
Modellen zwischen Client und HyperKVS (sowohl Upload (direkt) als auch
Download (nur das Load in CATIA)), sowie zur Festlegung des hochzulad‐
enden Umfangs und der Konsistenzprüfung dieses definierten Umfangs.
 
LTA
Die Loch-Tool-Applikation (LTA) ist eine Anwendung zur Unterstützung des
Prozesses zur Erzeugung von LTA-Elementen in CATIA V5-6. Dazu
werden spezielle LTA-Features erzeugt und im CATIA-Modell gespeichert.
Diese können auch in Tabellenform angezeigt werden.
 
NTool
Das Namenstool unterstützt den CATIA-Anwender bei der Benennung von
CATIA Daten entsprechend der VW 01059-6 Punkt „3.2 Namenskonventi‐
on“. Es unterstützt darüber hinaus beim Datenmanagement und Umbenen‐
nen ganzer Produktstrukturen.
 
OutGen
Output Generation, Programm zur Deklaration und Erzeugung von Out-
Adaptern nach VW01059-6-3
 
Rahmenscript
Ein Hilfsprogramm, mit dem man sich einen passenden Zeichnungsrahmen
um seine Zeichnung erstellen kann. Bei nachträglicher Änderung der Rah‐
mengröße werden die bereits eingetragenen Daten automatisch in den
neuen Zeichnungsrahmen übertragen.
 
RPS
Referenz-Punkt-System, eine Applikation, die bei der Erstellung und Tabel‐
lierung von RPS-Punkten und -Flächen nach VW 01055 hilft.
 
Validat
Ein Prüftool, das die Qualität von CATIA Datensätzen auf Einhaltung der
Vorgaben von VW 01059-6 prüft.
 
VEM
Verbindungs-Elemente-Management, ein Programm zur Unterstützung
beim Einsatz von Fügeverfahren in einem Assembly. Beispiele für unter‐
stützte Fügetechniken sind: Schweißen, Löten, Kleben, Clinchen, Schwei‐
ßen von Normteilen (Schweißbolzen u. a.).
 
VTA
Verbindungs-Technik-Applikation, ein Programm zur Unterstützung beim
Einsatz von Fügeverfahren in einem Assembly. Beispiele für unterstützte
Fügetechniken sind: Schweißen, Löten, Kleben, Clinchen, Schweißen von
Normteilen (Schweißbolzen u. a.).
2.3  


### 第 10 页
Seite 10
VW 01059-6-1: 2015-11
Mitgeltende Unterlagen
Die folgenden in der Norm zitierten Dokumente sind zur Anwendung dieser Norm erforderlich:
VW 01055
Referenz-Punkt-Systematik (RPS); Angaben in Zeichnungen und 3D-
CAD-Modellen
VW 01059-6
Anforderungen an CAD/CAM-Daten - CAD-System CATIA V5-6
VW 01059-6-3
Anforderungen an CAD/CAM-Daten - CAD-System CATIA V5-6 - Teil 3:
Prozesskettenadapter (PCA), DMU-CATPart und optionalen Adaptern
3  

