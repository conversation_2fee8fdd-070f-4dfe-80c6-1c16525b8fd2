# VW_01059_6_1_EN_2011-11_CAD&CAM数据要求_第6_1部分：CAD系统CATIA V5_术语.pdf

## 文档信息
- 标题：
- 作者：
- 页数：9

## 文档内容
### 第 1 页
Group standard
VW 01059-6-1
Issue 2015-11
Class. No.:
22632
Descriptors:
CAD, CAM, CATIA, Terms, Abbreviation
Requirements for CAD/CAM Data – CATIA V5-6 CAD System
Part 1: Terms
Previous issues
VW 01059-6 Supplement 1: 2005-07, 2006-12
Changes
The following changes have been made to VW 01059-6 Supplement 1: 2006-12:
–
Status of the document changed from supplement to standard
–
CAD system changed from CATIA V5 to V5-6
–
Technical responsibility changed
–
Section 2 divided into 3 subsections
–
Section 2.1 : Explanation of following terms modified "CEG (CAD use group)", "CEG level",
"DMU CATPart", "relational design"; following terms added: "feature catalog", "GRC",
"VWGRCLite", "OEM start part," and "PCA"; term "CCP link " removed; term "connection ele‐
ment management" moved to Section 2.3
–
Section 2.2 "Systems associated with CATIA" new
–
Section 2.3 "Tools associated with CATIA" new
Scope
This standard explains abbreviations and terms used in the CATIA V5-6 CAD system and its envi‐
ronment. It aims to facilitate the use and the comprehension of VW 01059-6.
1  
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Page 1 of 9
Technical responsibility
The Standards department
K-SIPE-2/3
Stefan Biernoth
Tel.: +49 5361 9 48896
EKDV/4 Norbert Wisla
EKDV
Tel.: +49 5361 9 48869
Maik Gummert
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Page 2
VW 01059-6-1: 2015-11
Terms and abbreviations
CATIA-specific terms and abbreviations
Capitalization mostly is based on the German way of spelling.
 
Adapter
An adapter contains the original geometry and references to CATParts of
higher order. An adapter can either contain the input data for a design or
provide the output data for downstream designs or processes (e.g., DMU
CATParts). Thus, it serves as an interface in the design process and can
have various characteristics to be determined by the designer. The hier‐
archical order of references must be taken into account when adapters are
used.
 
Advanced part design "Advanced part design" means the design of a part within a CATPart using
parametrization and associativity, but not using external references beyond
the CATPart.
 
Associativity
Associativity means the establishment of dependencies between geometri‐
cal elements and/or parameters within a CATPart or over several CAT‐
Parts. These dependencies are determined by unidirectional relations (pa‐
rent/child relations). The CAD-specific features for the cross-CATPart de‐
pendencies are links and external references.
 
Boundary representa‐
tion (BRep)
Description of a solid that is defined by its bounding elements – vertices,
edges, faces. In addition to the geometric description, the topology of the
element is also part of the representation. BRep nowadays is the preferred
method for the computer representation of complex solids, besides the
CSG (constructive solid geometry) model based on Boolean operations of
primitive solids.
 
CAD type
The CAD type is used in the process of naming documents in order to de‐
fine the function within the product structure. Examples:
DMU adapter – CAD type marking: DMU
Adapter for numerical simulation – CAD type marking: CAE
 
 
Catalog
The catalog is an independent CATIA V5-6 document used for the struc‐
tured storage of CATIA V5-6 objects and documents. The catalog can man‐
age 2-D standard geometries like frames and labels. In the 3-D area, the
catalog is used for, e.g., structuring PowerCopies, user-defined features
(UDFs), CATParts, and knowledgeware elements
 
CATAnalysis
The CATAnalysis is a CATIA V5-6 document which is created in the course
of finite element analyses. It contains the reference to parts or products to
be analyzed, the design relations for the analysis, and the specifications of
the finite element model.
2  
2.1  


### 第 3 页
Page 3
VW 01059-6-1: 2015-11
 
CATDrawing
The CATDrawing is the CATIA V5-6 document used for 2-D drawing crea‐
tion and for drawing generation from 3-D parts. In drawing generation, a
unidirectional link from the CATDrawing to the original CATPart or original
CATProduct is established.
 
CATPart
The CATPart is a CATIA V5-6 document used for the definition and crea‐
tion of the descriptive 3-D geometry in the form of wire frames, solids, and
surfaces. The geometry elements within the CATPart are structured using
the PartBody or other Bodies and/or Geometrical Sets.
 
CATProcess
The CATIA V5-6 CATProcess document reflects the production perspective
on a part to be processed mechanically. The included PPR tree references
one or several CATParts or CATProducts and the resources used for pro‐
cessing (machines, tools). It defines the mechanical processes as numeri‐
cally controlled (NC) programming, e.g., for drilling, milling, electric dis‐
charge machining, wire cutting, laser cutting, or waterjet cutting. Other neu‐
tral or machine-specific control data can be derived from a CATProcess
document.
 
CATProduct
CATProduct is a CATIA V5-6 document which can contain several CAT‐
Parts, Components, and other CATProducts as sub-structures. The compo‐
nents included in the CATProduct structure can be applied as CATPart in‐
stance or CATProduct instance with related stored documents or as Com‐
ponent without a representing document. An assembly or the design envi‐
ronment for a part is constructed as CATProduct. The CATProduct referen‐
ces CATParts and CATProducts of lower order and manages their spatial
position.
 
CCP                       
(connector connec‐
tion point)
The connector connection point (CCP) is used during the design of electri‐
cal wiring and pipes. It defines the start and the end of a cable or pipe.
 
 
 
CEG (German acro‐
nym for CAD use
group)
CEG is a working group of the German Automobile Manufacturers. This
working group coordinates system settings, release requirements, and re‐
lease start dates of the individual automobile manufacturers.
 
CEG level
Drawing creation settings defined by the CEG in a joint working group. The
respective CEG levels comprise different specifications concerning sym‐
bols, line groups, font sizes, etc.
 
Component
Component is a CATIA V5-6 term for a part node integrated into the prod‐
uct structure of the CATProduct.
 


### 第 4 页
Page 4
VW 01059-6-1: 2015-11
Context
A context is created when links are constructed between two CATParts
within one CATProduct. CATIA V5-6 assumes that other links are to be cre‐
ated only in that same product structure. The product node loaded on the
top position in the CATIA window is called the root context.
 
Context behavior
The behavior of a CATPart or CATProduct is called context behavior. This
behavior can vary, depending on the part or product being within the con‐
text or not. Within the context, the document is fully functional. Outside the
context, the synchronization of existing external links and the creation of
new external links is not possible.
 
Design in context
The term "design in context" means the procedure of working directly in the
design environment. The as-installed position of adjacent parts is taken into
account during this process. Parts designed in such a way are correctly
positioned in the design environment. With CATIA V5-6, links (references)
between the documents in the product structure are created by publication
of the elements and by the functions Copy and Paste special as Result with
Link. In this process, import and context links are established.
 
Design table
The "design table" is a value table used to control the parameters in a CAT‐
IA V5-6 document. It is created and stored as a separate file (in Excel or
text format). The content of a design table can be integrated into the docu‐
ment in order to detach it from an external file. During this process, the pos‐
sibility of modifying the design table is lost and can only be restored if the
external file is attached again.
 
DL name (dynamic
link name)
When dynamic link names (DL names) are used, the absolute path specifi‐
cation in a document is replaced by a virtual file name (DL name). Only this
virtual name is stored within the reference. A configuration file (DL-
names.CATSettings) is provided in CATIA V5-6. This file may have to be
adapted before CATIA V5-6 documents are used. The application of DL
names simplifies and accelerates the access to document directories.
Moreover, using DL names simplifies data exchange between different
CATIA V5-6 operating systems (Windows, UNIX).
 
DMU CATPart
The DMU CATPart contains the geometry describing a part for the DMU
process. The designer determines the characteristics of the geometry as
per Volkswagen standard VW 01059-6-3.
 
Explicit design
"Explicit design" as an operating process involves part geometry design in
a CATPart without using parametrization and associativity.
 
Feature
A feature is a geometrical or functional object used to create a 3-D geome‐
try. The feature contains a specification consisting of parent elements and
parameters as well as an operator functioning as an instruction on how to
deal with the specification. The result is represented in the form of elements
and sub-elements.


### 第 5 页
Page 5
VW 01059-6-1: 2015-11
Features (design elements) are objects that, in addition to the pure geomet‐
ric description, also contain additional attributes such as production specifi‐
cations. Technical standard elements (bores, chamfers, pockets, grooves,
etc.) can be integrated into a design as parametrized features and evalu‐
ated for the manufacturing process, as necessary.
 
Feature catalog
A collection of features for a certain topic.
 
Geometrical set
Non-solids (wire frame and surface elements) are stored under a "geometri‐
cal set" which is a structuring element in the specification tree. These non-
solids were designated OpenBodies up to and including CATIA V5R12.
 
Basic model
The term basic model was used in CATIA V4 and must not be used further
in CATIA V5-6. The settings and specifications defined in the CATIA V4 ba‐
sic model are determined in CATIA V5-6 using the CATIA Group Reference
(GRC), the CATIA settings, a structure document, and the CEG level.
 
GRC
Group reference CATIA: a collection of updated CATIA software and rela‐
ted tools that is distributed throughout the Group, thus significantly simplify‐
ing data exchange within the Group.
 
Import
Import is the link type created during work in a product structure ("design in
context"); it references the original part.
 
Instance
An original part used several times in the CATProduct is called an instance.
The reference to the original part and the related positions and orientations
of the instances are stored in the CATProduct. Modifications of the original
part influence all instances. Modifications of the instances, however, are not
possible. Each instance is identified by an "instance name".
 
Design CATPart
The design CATPart is the CATIA V5-6 model of a real part. The CATPart
contains the geometry copied from the input data and the entire geometry
of the parametrized associative design of a modeled part.
Design CATProduct
The design CATProduct combines the design CATPart, various input and
output data in the adapter CATParts, and the DMU CATPart into a package
including all geometrical information of the real part.
 
Master model
The master model can be a CATPart or a CATProduct which contains a de‐
tailed parametric associative design for a part or assembly (including for‐
mulas and rules). A new design is created from the master model through
the exchange and/or adaptation of elements, modification of parameters,
and use of relations.
 
Multi-model concept
The Multi-model concept describes the distribution of a physical part to sev‐
eral CATParts. It allows the part to be developed in parallel by multiple de‐
signers ("concurrent engineering"). The links are managed in a higher-order
CATProduct using publications.


### 第 6 页
Page 6
VW 01059-6-1: 2015-11
 
OEM start part
OEM start part is a structure CATPart jointly developed by the German Au‐
tomobile Manufacturers (Audi, BMW, Daimler, Porsche, and Volkswagen).
 
OpenBody
An OpenBody is a structuring element in the specification tree. It serves to
store wire frame and surface geometries and is called a geometrical set
from CATIA V5 release 13 on.
 
Parametrization
Parametrization means the act of describing and modifying (as necessary)
the shape and position of an object using variable parameters. Relations
within a solid and between several solids are possible. The parameters are
defined and stored in the CATPart. They can later be edited and modified
by the user.
 
PartBody
The PartBody is a structuring element in the specification tree of the CAT‐
Part. Solid features and the related sketches are stored under the Part‐
Body. A CATPart contains exactly one PartBody which is always in the first
position in the specification tree. The additional structuring elements are
designated "body." Two bodies can be connected to each other using Boo‐
lean operations.
 
PCA (process chain
adapter)
The process chain adapter bundles and structures all geometric and non-
geometric information necessary for the data recipient.
 
 
 
 
PowerCopy
In a PowerCopy, a geometry used repeatedly (e.g., swages, embossments)
is defined in general in a separate CATPart using a number of geometrical
elements, formulas, conditions, etc. When the PowerCopy is integrated into
a different CATPart, the geometry created before is adapted to the new
boundary conditions.
 
Process chain inte‐
gration
"Process chain integration" is the implementation of "relational design" over
the entire process chain from development to production.
 
Publication
A publication is the specific publication of elements which can be accessed
from other CATParts during "design in context." In the process of defining
links and their positioning with the help of assembly constraints, only these
defined published elements can be used (corresponding settings provided).
Publications enable the representation of the link path via the product struc‐
ture and detached from the file system.
 
Relational design
"Relational design" is the procedure applied to define the parts beyond the
limits of one CATPart with external references and/or constraints, besides
using associativity and parametrization within a CATPart. This process is


### 第 7 页
Page 7
VW 01059-6-1: 2015-11
realized in a product structure which in many cases is not equivalent to the
BOM structure.
 
Skeleton
A skeleton contains the original geometry and references for further steps
in the design process. The skeleton is usually a CATPart. It is applied when
two or more designers define and use some of the geometrical characteris‐
tics of their parts together.
 
Solid
A solid is a solid body element created using the appropriate CATIA func‐
tion.
Used as output data for DMU purposes, this can also refer to a thickened
surface (thick surface or surface with material vector).
 
Start model
The term "start model" was used in CATIA V4 and is not longer used in
CATIA V5-6. All settings and specifications are determined in CATIA V5-6
using the CATIA reference, the settings, the structure CATPart, and the
CEG level.
 
Structure CATPart
The structure CATPart contains a predefined specification tree and default
standards for design. The specification tree structure is defined by the ap‐
propriate departments and can be extended by the designer, as necessary.
 
 
 
Tolerant modeling
"Tolerant modeling" in CATIA V5-6 means the possibility of deviating from
the default standard accuracy within different feature functions (in genera‐
tive shape design). This possibility simplifies the reuse of imported CAD da‐
ta of reduced internal accuracies. Additionally, tangent and curvature er‐
rors, which could result from subsequent operations in CATIA V5-6, are
minimized using tolerant modeling.
 
Unique universal
identifier (UUID)
The "unique universal identifier" (UUID) is an unambiguous identification. In
CATIA V5-6, it is used to identify documents. The UUID is the basis for the
multi-model concept and serves to prevent name conflicts.
 
User-defined feature
(UDF)
A user-defined feature (UDF) is created the same way as a PowerCopy.
Modification options are restricted if the UDF is used, since certain charac‐
teristics of the geometry are protected against modifications by the user.
The UDF is represented as a feature in the specification tree.
VWGRCLite
VWGRCLite is a CATIA-V5-6-based standard working environment for con‐
tractors of VOLKSWAGEN AG.
Systems associated with CATIA
CONNECT
Siemens' product lifecycle management (PLM) software "Teamcenter" has
been localized for the Volkswagen Group. The Volkswagen version is
called CONNECT. It is the central product data platform for the entire
2.2  


### 第 8 页
Page 8
VW 01059-6-1: 2015-11
Group with interfaces to TI-Syncro, CATIA, the engineering data manage‐
ment system (KVS), and other systems. The objective of CONNECT is to
create a common, Group-wide data pool that consistently facilitates the
development of vehicle projects and modules across all the brands, divi‐
sions, and locations of the Volkswagen Group. CONNECT is a client-server
system, so the Teamcenter client used in the Group is also often referred to
as CONNECT.
 
KVS
German acronym for the engineering data management system
 
LCA
Life cycle assessment: a team data management (TDM) system from Das‐
sault.
 
GRICOS
Group reference installation conversion server: GRICOS is the working lay‐
er of a central conversion service used worldwide at all Volkswagen loca‐
tions with conversion instances.
 
GINA
Geometric interface navigator: GINA is the control layer of a central conver‐
sion service that is used at all Volkswagen locations that have their own
conversion instances.
 
PDM
Product data management: database-based administration and communi‐
cation system for documents (CAX, MS Office, master data, etc.) that are
created within the product emergence process.
 
TDM
Team data manager: a system for administering CAD data within the de‐
sign process. TDM is used in smaller teams and limited design environ‐
ments; compare PDM system. CONNECT is used as a TDM system at
Volkswagen.
 
VPM
Virtual product model is a TDM application. VPM is used for collision ex‐
aminations.
Tools associated with CATIA
KVS plug-in
The KVS plug-in is an application for transferring the data of CATIA V5-6
models between the client and HyperKVS (both upload (direct) and down‐
load (only the load in CATIA)). It is also used to determine the package to
be uploaded and to test the consistency of the defined package.
 
LTA
LTA (German acronym for hole tool application) is an application that facili‐
tates the process of generating LTA elements in CATIA V5-6. In addition,
special LTA features are generated and saved in the CATIA model. They
can also be displayed in tabular form.
 
NTool
This naming tool helps the CATIA user to name CATIA data as per
VW 01059-6, section 3.2 "Naming convention." Furthermore, it facilitates
data management and the renaming of entire product structures.
 
2.3  


### 第 9 页
Page 9
VW 01059-6-1: 2015-11
OutGen
Output generation: a program for declaring and generating output adapters
as per VW 01059-6-3.
 
Frame script
An auxiliary program that can be used to create a suitable frame around a
drawing. If the frame size is later changed, the data already entered are au‐
tomatically transferred into the new drawing frame.
 
RPS
Reference Point System: an application that facilitates the creation and tab‐
ulation of RPS points and surfaces as per VW 01055.
 
Validat
A testing tool that checks whether the quality of CATIA data sets complies
with the specifications of VW 01059-6.
 
VEM
German acronym for "connection element management": a program that fa‐
cilitates the use of joining procedures in an assembly. Supported joining
techniques include welding, soldering, bonding, clenching, welding stand‐
ard parts (weld studs, etc.).
 
VTA
German acronym for "connection technique application": a program that fa‐
cilitates the use of joining procedures in an assembly. Supported joining
techniques include welding, soldering, bonding, clenching, welding stand‐
ard parts (weld studs, etc.).
Applicable documents
The following documents cited in this standard are necessary to its application.
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
VW 01055
Reference Point System (RPS); Specifications in Drawings and 3-D
CAD Models
VW 01059-6
Requirements for CAD/CAM Data - CAD System CATIA V5-6
VW 01059-6-3
Requirements for CAD/CAM Data – CATIA V5-6 CAD System – Part 3:
Process Chain Adapter (PCA), DMU CATPart, and Optional Adapters
3  

