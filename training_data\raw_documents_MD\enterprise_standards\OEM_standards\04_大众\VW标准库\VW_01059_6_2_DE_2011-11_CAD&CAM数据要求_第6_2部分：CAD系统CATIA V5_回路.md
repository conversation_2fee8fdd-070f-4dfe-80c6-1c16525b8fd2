# VW_01059_6_2_DE_2011-11_CAD&CAM数据要求_第6_2部分：CAD系统CATIA V5_回路.pdf

## 文档信息
- 标题：
- 作者：
- 页数：12

## 文档内容
### 第 1 页
Konzernnorm
VW 01059-6-2
Ausgabe 2015-11
Klass.-Nr.:
22632
Schlagwörter:
CAD, CATIA, Leitungsverlegung, Namenskonvention, Konvention, Leitfaden, Elektrik-Bauteile, Elektrik,
Bordnetz
Anforderungen an CAD/CAM-Daten – CAD-System CATIA V5-6
Teil 2: Elektrische Leitungsverlegung
Frühere Ausgaben
VW 01059-6 Beiblatt 2: 2005-07, 2006-12, 2008-03, 2008-12, 2011-08
Änderungen
Gegenüber der VW 01059-6 Beiblatt 2: 2011-08 wurden folgende Änderungen vorgenommen:
–
Status des Dokuments von Beiblatt zu Norm geändert
–
Normtitel ergänzt
–
Fachverantwortung geändert
–
Abschnitt 3.2.2.3, Bild 14 durch Austausch berichtigt
–
Mitgeltende Unterlagen aktualisiert
Inhalt
Seite
Anwendungsbereich ................................................................................................... 2
Begriffe ....................................................................................................................... 2
Anforderungen ........................................................................................................... 4
Elektrik-Bauteile ......................................................................................................... 4
Elektrische Leitungsverlegung ................................................................................... 5
Grundstrukturelemente .............................................................................................. 7
Root-Produkt .............................................................................................................. 7
UMGEBUNG .............................................................................................................. 8
VERLEGEBEREICH .................................................................................................. 9
SKELETON ................................................................................................................ 9
Baukastenmodul, Hutmodul und Referenz-Hutmodul ................................................ 9
CONNECTOR, SUPPORT und SPLICE .................................................................. 10
Geometrical Bundle .................................................................................................. 11
1
2
3
3.1
3.2
3.2.1
3.2.1.1
3.2.1.2
3.2.1.3
3.2.1.4
3.2.2
*******
3.2.2.2
Norm vor Anwendung auf Aktualität prüfen.
Die elektronisch erzeugte Norm ist authentisch und gilt ohne Unterschrift.
Seite 1 von 12
Fachverantwortung
Normung
EEXS/4
Heide Melchior
Tel.: +49 5361 9-38113
EKDV/4 Norbert Wisla
EKDV
Tel.: +49 5361 9-48869
Maik Gummert
Alle Rechte vorbehalten. Weitergabe oder Vervielfältigung ohne vorherige Zustimmung einer Normenabteilung des Volkswagen Konzerns nicht gestattet.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Seite 2
VW 01059-6-2: 2015-11
Multi-Branchable (bzw. Bundle Segmente) .............................................................. 11
Ablage der Daten im Datenbanksystem ................................................................... 12
Mitgeltende Unterlagen ............................................................................................ 12
3.2.2.3
3.3
4
Anwendungsbereich
Diese Norm gilt in Abweichung zur Norm VW 01059-6 bei der elektrischen Leitungsverlegung im
Volkswagen Konzern. Sie wird bis auf weiteres auf die Produktdatenarten (PDA) ELV, EKR, ELX
und EN angewendet.
VW 01059-6-3 kommt nicht zur Anwendung.
CATIA-spezifische Begriffe in dieser Norm sind kursiv geschrieben.
Folgende Unterlagen sind zusätzlich zu berücksichtigen:
–
Methodikleitfaden „Methodikleitfaden CATIA V5 für Leitungsstrangkonstruktion“
–
Steckerleitfaden „Leitfaden zur Erstellung von Elektrik-Bauteilen mit CATIA“
–
Vorgehensweise für CATIA V5 „Ergänzung der Volkswagen Standardumgebung für die elektri‐
sche Leitungsstrangverlegung“
–
Arbeitsanweisung 37_AA_EEK  „Zur Bereitstellung der Leitungsstrangdaten für DMU mit CA‐
TIA V5“
–
Arbeitsanweisung 57_AA_EEK  „Aggregateorientierte Triebsatzverkabelung“
Diese Unterlagen müssen, wenn notwendig, vom Auftraggeber beschafft werden.
Begriffe
BNS
Ein Bundle Segment ist die grafische 3D-Darstellung eines Leitungsstrang-
abschnittes.
 
EKR
Die Produktdatenart für Elektrik-Bauteile (Elektrikkomponente reduziert).
 
ELV
Die Produktdatenart für die in VOBESPlus genutzten 3D-Daten. Sie findet
Anwendung für die sog. 150% Modelle der Leitungsstränge und dient u. a.
der Zeichnungsableitung. Es dürfen deshalb keine nicht genehmigten Än‐
derungen enthalten sein.
 
ELX
Die Produktdatenart dient u. a. für die in DMU genutzten Daten, die im KVS
abgelegt werden (gilt nicht für Audi). Sie findet Anwendung:
1. für die 180% Modelle der Leitungsstränge
2. für die Modelle der geometrisch relevanten Module im DMU
 
GBN
Ein CATProduct, das mit CATIA V5 als Geometrical Bundle (GBN) definiert
ist (elektrifiziert).
 
VOBES
VOlkswagen Bordnetz Entwicklungs-System. VOBES wird im Volkswagen
Konzern zur Entwicklung der Bordnetze eingesetzt.
 
1  
2  


### 第 3 页
Seite 3
VW 01059-6-2: 2015-11
Leitungsstrang
Der Leitungsstrang ist ein Teilbereich des Bordnetzes und hat verschiede‐
ne Ausprägungen für die Prozesskette (150% und 180%). Der Leitungs‐
strang besteht aus einem oder mehreren Verlegebereichen.
 
150% Leitungsstrang
In der 150% Verlegung eines Leitungsstranges sind alle elektrisch logi‐
schen Varianten des Leitungsstranges und die Verlegevarianten (stücklis‐
tenrelevant) enthalten.
 
180% Leitungsstrang
In der 180% Verlegung eines Leitungsstranges sind, neben den elektrisch
logischen Verlegevarianten des Leitungsstranges und den Verlegevarian‐
ten, auch die Einbauvarianten (nicht stücklistenrelevant) enthalten.
 
Verlegebereich
Es wird unterschieden:
1. Der „geometrische“ Verlegebereich bezeichnet den gesamten Leitungs‐
strang des Bordnetztes oder einen Teilbereich.
2. Das CATProduct VERLEGEBEREICH ist ein Grundstrukturelement der
Leitungsstrangkonstruktion in CATIA V5.
 
Baukastenmodul
Enthält alle Verlegetrassen und Bauteile innerhalb einer Fahrzeugplattform,
die marken-/hutübergreifend gleich sind.
 
Hutmodul
Enthält alle Verlegetrassen und Bauteile des fahrzeugbezogenen Hutum‐
fanges.
 
Referenz-Hutmodul
Referenz-Hutmodule enthalten Verlegeanteile, die für sehr viele, aber nicht
alle Hutmodule als Referenz gelten. Nähere Informationen sind im „Metho‐
dikleitfaden CATIA V5 für Leitungsstrangkonstruktion“ zu finden.
 
Modul(familie)
Jeder modular aufgebaute Leitungsstrang ist in mehrere Modulfamilien
gegliedert. Eine Modulfamilie steht jeweils für eine bestimmte Funktion (z.
B. Scheinwerfer). Das Zusammenfassen bestimmter Modulfamilien ergibt
die vollständige Leitungsstrangkonfiguration. Innerhalb dieser Modulfamili‐
en sind zusätzlich noch Modulvarianten möglich.
 
Modulvarianten
Die Modulvarianten entstehen durch die Veränderung der Geometrie (z. B.
Längenvariante) oder durch Veränderung der elektrischen Verschaltung.
Sie sind in der Stückliste eines Leitungsstranges durch Indizierung der Mo‐
dulteilnummer abgebildet. Innerhalb einer Modulfamilie schließen sich die
Modulvarianten gegenseitig aus, da sie für die gleiche Funktion stehen.
 
Einbauvarianten
Sie entstehen durch eine veränderte geometrische Darstellung einer Mo‐
dulvariante, ohne dass dabei die elektrische Verschaltung oder zum Bei‐
spiel die Länge verändert wird. Da diese Varianten sich nur aus der DMU-
Betrachtung ergeben und keine neue Teilnummer erzeugt wird, sind sie
nicht in der Stückliste abgebildet.
Weitere Begriffe siehe VW 01059-6 und VW 01059-6-1.


### 第 4 页
Seite 4
VW 01059-6-2: 2015-11
Anforderungen
Elektrik-Bauteile
Die für die elektrische Leitungsverlegung zu verwendenden Elektrik-Bauteile werden im Volkswa‐
gen Elektrik-Katalog abgelegt und zur Verfügung gestellt. Alle Elektrik-Bauteile sind als EKR zu
verwenden. Bei der Verwendung der Elektrik-Bauteile müssen die Methoden und Vorgaben des
Fachbereichs eingehalten werden.
Elektrik-Bauteile, die nicht im Volkswagen Elektrik-Katalog zur Verfügung stehen, müssen gemäß
der Methodik und der Vorgaben des Fachbereichs „Bordnetz Entwicklungsprozess“ erstellt
werden.
Zusätzlich zu den generellen Festlegungen der Norm VW 01059-6 sind für Elektrik-Bauteile folgen‐
de Vorgaben zu beachten:
–
Die Part Number entspricht der Teilenummer (max. 14 Zeichen, wird nicht aufgefüllt).
–
Die Trennung der Teilenummer erfolgt durch Unterstriche „_“.
–
Normteile beginnen mit „N“ oder mit „N__“, siehe Bild 2. Dies ist mit dem Fachbereich „Bord‐
netz Entwicklungsprozess“ abzustimmen.
–
Sonderbauteile, wie zum Beispiel Splice, Kombibauteile oder Dummy-Bauteile, haben eine
spezielle Benennung. Diese Ausnahmen müssen mit dem Fachbereich „Bordnetz Entwick‐
lungsprozess“ abgestimmt werden.
–
Wird ein Elektrik-Bauteil in ein CATProduct (siehe Abschnitt *******) eingefügt, muss der CA‐
TIA V5 Instance name nach Vorgaben des VOBESPlus-Prozess vergeben werden.
–
Wird ein Leitungsschutzelement (Protective Covering) in die elektrische Leitungsverlegung
eingefügt, werden die CATIA V5-Namen automatisch generiert. Die Teilenummer wird um ei‐
nen Zeitstempel erweitert, siehe Bild 3. Diese Benennungen sind zu übernehmen und dürfen
nicht geändert werden.
Bild 1 – Beispiel zur Benennung von Elektrik-Bauteilen
3  
3.1  


### 第 5 页
Seite 5
VW 01059-6-2: 2015-11
Bild 2 – Beispiele zur Benennung von Normteilen
Bild 3 – Beispiel zur Benennung von Leitungsschutzelementen
Elektrische Leitungsverlegung
Die Konstruktion der elektrischen Leitungsverlegung erfolgt in einer Produktstruktur, siehe Bild 4.
Die Produktstruktur besteht aus Grundstrukturelementen und Strukturelementen.
Als Grundstrukturelemente werden die CATProducts Root-Produkt ELX bzw. ELV, ggf. UMGE‐
BUNG, VERLEGEBEREICH und das CATPart SKELETON bezeichnet.
Im VERLEGEBEREICH werden die Strukturelemente eingefügt. Die Strukturelemente Baukasten‐
modul, Hutmodul und Referenz-Hutmodul teilen die Leitungsverlegung in Bereiche auf.
Die Anordnung der CATParts und CATProducts gemäß Bild 4 ist einzuhalten.
3.2  


### 第 6 页
Seite 6
VW 01059-6-2: 2015-11
Bild 4 – Übersicht der Produktstruktur für elektrische Leitungsverlegung


### 第 7 页
Seite 7
VW 01059-6-2: 2015-11
Grundstrukturelemente
Zusätzlich zu den generellen Festlegungen der Norm VW 01059-6 gelten für die Grundstrukturele‐
mente Root-Produkt ELX und ELV, UMGEBUNG, VERLEGEBEREICH und SKELETON folgende
Festlegungen:
–
Teilenummer (KVS-Code) und Bezeichnung wird vom Verlegebereich vorgegeben. Die Tren‐
nung der Teilenummer erfolgt durch Unterstriche „_“.
–
Die Version und Alternative ist den Methodikunterlagen der Fachbereiche zu entnehmen.
–
Der Kommentar wird nicht mit Unterstrichen aufgefüllt.
–
Dateiname, Instance name und Part Number sind identisch.
Bild 5 – Beispiel zum Aufbau der Benennung der Grundstrukturelemente
Root-Produkt
Das Root-Produkt ist das oberste CATProduct der elektrischen Leitungsverlegung, beim Root-Pro‐
dukt wird zwischen ELX und ELV unterschieden.
ELX
Die ELX stellt einen Entwicklungsstand dar und beinhaltet die 180% Leitungsstrangverlegung mit
allen Einbauvarianten.
Für die Benennung ist folgendes zu beachten:
–
CAD-Typ „KPR“ (Konstruktionsprodukt)
–
PDA „ELX“
–
Im Kommentar ist eine Freizeichenkette von max. 20 Zeichen zulässig. Der Inhalt ist mit der
Fachabteilung abzustimmen.
Bild 6 – Beispiel zur Benennung des CATProducts ELX
3.2.1  
3.2.1.1  
3.2.1.1.1  


### 第 8 页
Seite 8
VW 01059-6-2: 2015-11
ELV
Die ELV stellt einen Serien-/Freigabestand bzw. einen Zeichnungsstand dar und beinhaltet die
150% Leitungsstrangverlegung.
Für die Benennung ist folgendes zu beachten:
–
CAD-Typ „KPR“ (Konstruktionsprodukt)
–
PDA „ELV“
–
Im Kommentar wird das Zeichnungsdatum YYYYMMDD (z. B. ********) gesetzt.
Bild 7 – Beispiel zur Benennung des CATProducts ELV
UMGEBUNG
Das CATProduct UMGEBUNG wird nur in dem Root-Produkt der ELX angelegt. Unter der UMGE‐
BUNG können die zur Konstruktion benötigten Umgebungsdaten hinzugeladen werden. Vor dem
Speichern ins Datenbanksystem müssen Umgebungsdaten aus dem Datensatz entfernt werden.
Für das CATProduct UMGEBUNG ist folgendes zu beachten:
–
CAD-Typ „INP“ (Inputgeometrie)
–
PDA „ELX“
–
Der Kommentar lautet „UMGEBUNG“ bzw. englisch „ENVIRONMENT“.
–
Die UMGEBUNG existiert maximal einmal.
Bild 8 – Beispiele zur Benennung des CATProducts UMGEBUNG
3.2.1.1.2  
3.2.1.2  


### 第 9 页
Seite 9
VW 01059-6-2: 2015-11
VERLEGEBEREICH
Für das CATProduct VERLEGEBEREICH ist folgendes zu beachten:
–
CAD-Typ „GEO“ (Geometrie)
–
PDA wie das Root-Produkt
–
Der Kommentar lautet „VERLEGEBEREICH“ bzw. englisch „SECTIONHARNESS“.
–
Der VERLEGEBEREICH existiert genau einmal.
Bild 9 – Beispiele zur Benennung des CATProducts VERLEGEBEREICH
SKELETON
Für das CATPart SKELETON ist folgendes zu beachten:
–
CAD-Typ „SKE“ (Skeleton)
–
PDA wie das Root-Produkt
–
Der Kommentar lautet „SKELETON“.
–
Das SKELETON existiert genau einmal.
Bild 10 – Beispiel zur Benennung des CATParts SKELETON
Baukastenmodul, Hutmodul und Referenz-Hutmodul
Die Strukturelemente Baukastenmodul, Hutmodul und Referenz-Hutmodul teilen die Leitungsverle‐
gung des Verlegebereiches in zwei oder mehrere Bereiche auf.
Unter dem VERLEGEBEREICH existieren die CATProducts Baukastenmodul und Hutmodul je‐
weils ein- oder mehrmals. Bei Bedarf können zusätzlich ein oder mehrere Referenz-Hutmodule im
VERLEGEBEREICH angelegt werden.
Zusätzlich zu den generellen Festlegungen der Norm VW 01059-6 ist folgendes zu beachten:
–
Die Benennung beginnt mit der festen Zeichenkette „BAUKST“, „HUT“ oder „REFHUT“.
–
Es folgt die Bezeichnung des Root-Produkts durch einen Unterstrich abgetrennt.
Diese Bezeichnung kann in Absprache mit der Fachabteilung und der für die Fachabteilung
zuständigen Systembetreuung von der Bezeichnung des Root-Produkts abweichen.
3.2.1.3  
3.2.1.4  
3.2.2  


### 第 10 页
Seite 10
VW 01059-6-2: 2015-11
Die Bezeichnung besteht aus max. 18 Zeichen und wird bei Benennungen, die kürzer als 18
Zeichen sind, nicht mit Unterstrichen aufgefüllt.
–
Werden in einem Datensatz mehrere Fahrzeugprojekte und ggf. deren Lenkvarianz darge‐
stellt, müssen die Strukturelemente über eine Kennzeichnung unterschieden werden. Die
Kennzeichnung darf aus max. 5 Zeichen bestehen und wird durch einen Unterstrich von der
restlichen Benennung getrennt.
Die Kennzeichnung muss gemäß der Methoden und Vorgaben des Fachbereichs gewählt
werden (z. B. V01VW, V03AU, V01, L0L) und in der Description des Root-Produkts des Verle‐
gebereiches aufgeschlüsselt werden.
–
Dateiname, Instance name und Part Number sind identisch.
Bild 11 – Beispiele zur Benennung der Strukturelemente (Baukastenmodul, Hutmodul und Refe‐
renz-Hutmodul)
CONNECTOR, SUPPORT und SPLICE
In den CATProducts CONNECTOR, SUPPORT und SPLICE werden die jeweiligen Elektrik-Bautei‐
le eingefügt.
Diese CATProducts werden unter den CATProducts Baukastenmodul, Hutmodul und Referenz-
Hutmodul in die Produktstruktur eingefügt. Hierzu ist folgendes zu beachten.
–
Die feste Zeichenkette lautet CONNECTOR, SUPPORT oder SPLICE.
–
Die nachfolgende Benennung entspricht der Benennung vom Baukastenmodul, Hutmodul
bzw. Referenz-Hutmodul. Zur Trennung der Benennung wird ebenfalls ein Unterstrich verwen‐
det.
–
Die CATProducts CONNECTOR, SUPPORT und SPLICE existieren jeweils einmal im Bau‐
kastenmodul, Hutmodul und Referenz-Hutmodul.
–
Dateiname, Instance name und Part Number sind identisch.
*******  


### 第 11 页
Seite 11
VW 01059-6-2: 2015-11
Bild 12 – Beispiele zur Benennung der CATProducts CONNECTOR, SUPPORT und SPLICE
Geometrical Bundle
Im Geometrical Bundle (GBN) werden Bereiche der Leitungsverlegung, d. h. die Multi-Branchable
bzw. Bundle Segmente sowie die Leitungsschutzelemente abgelegt.
Geometrical Bundle werden unter den CATProducts Baukastenmodul, Hutmodul und Referenz-
Hutmodul in die Produktstruktur eingefügt. Leere Geometrical Bundle müssen aus der Produkt‐
struktur entfernt werden.
Zusätzlich zu den generellen Festlegungen der Norm VW 01059-6 ist bei der Erstellung der Geo‐
metrical Bundle folgendes zu beachten.
–
Die CATProducts müssen mit CATIA V5 als Geometrical Bundle definiert werden.
–
Die einzelnen Bestandteile der Benennung werden durch Unterstriche getrennt.
–
Die einzelnen Bestandteile werden nicht mit Unterstrichen aufgefüllt.
–
Bestandteile der Benennung sind:
1.
eine kurze, sinnvolle Benennung des Geometrical Bundle
2.
die Benennung des Baukastenmoduls, Hutmoduls oder des Referenz-Hutmoduls.
–
Die Benennung beinhaltet maximal 49 Zeichen. Bei mehr als 49 Zeichen muss der erste Teil
entsprechend gekürzt werden.
–
Dateiname, Instance name und Part Number sind identisch.
Bild 13 – Beispiele zur Benennung der Geometrical Bundle
Multi-Branchable (bzw. Bundle Segmente)
Die Multi-Branchables und Bundle Segmente repräsentieren die Leitungen und müssen gemäß der
Methoden und Vorgaben des Fachbereichs erstellt werden.
Die Benennung des CATParts des Multi-Branchables bzw. Bundle Segmentes beinhaltet im ersten
Teil die Benennung des Geometrical Bundles.
3.2.2.2  
3.2.2.3  


### 第 12 页
Seite 12
VW 01059-6-2: 2015-11
Im zweiten Teil wird die Bezeichnung „Multi-Branchable“ bzw. „Bundle_Segment“ und eine Num‐
merierung angefügt. Die einzelnen Teile der Benennung werden durch einen Bindestrich getrennt.
Bei korrekter Einstellung in den CATIA V5-Settings erfolgt dies automatisch.
Für die Multi-Branchables und Bundle Segmente ist folgendes zu beachten.
–
Dateiname (FileName) und Part Number sind identisch, der Instance name kann ggf. abwei‐
chen.
–
Die Benennung des Multi-Branchables bzw. des Bundle Segmentes kann in Absprache mit
der Fachabteilung und der für die Fachabteilung zuständigen Systembetreuung abweichen.
Bild 14 – Beispiele zur Benennung der Multi-Branchable (bzw. Bundle Segmente)
Ablage der Daten im Datenbanksystem
Zur Ablage der elektrischen Leitungsverlegung werden die Root-Produkte der ELX [gilt nicht für
Audi] und der ELV mit der Produktstruktur ins KVS gespeichert. Die Ablage der Daten muss ge‐
mäß den Methoden und Vorgaben des Fachbereichs erfolgen.
Für den DMU-Prozess mit VPM wird zusätzlich aus dem CATProduct ELX ein CATPart bzw. meh‐
rere CATParts benötigt. Die Erstellung der CATParts und die Ablage im KVS ist den Methoden der
jeweiligen Fachabteilungen zu entnehmen.
Fahrzeugprojekte, die nur im System CONNECT dargestellt werden, benötigen kein zusätzliches
CATPart des CATProducts ELX.
Mitgeltende Unterlagen
Die folgenden in der Norm zitierten Dokumente sind zur Anwendung dieser Norm erforderlich:
37_AA_EEK
Zur Bereitstellung der Leitungsstrangdaten für DMU mit CATIA V5
57_AA_EEK
Aggregateorientierte Triebsatzverkabelung
Methodikleitfaden
Methodikleitfaden CATIA V5 für Leitungsstrangkonstruktion
Steckerleitfaden
Leitfaden zur Erstellung von Elektrik-Bauteilen mit CATIA
Vorgehensweise für
CATIA V5
Ergänzung der Volkswagen Standardumgebung für die elektrische Lei‐
tungsstrangverlegung
VW 01059-6
Anforderungen an CAD/CAM-Daten - CAD-System CATIA V5-6
VW 01059-6-1
Anforderungen an CAD/CAM-Daten - CAD-System CATIA V5-6 - Teil 1:
Begriffe
3.3  
4  

