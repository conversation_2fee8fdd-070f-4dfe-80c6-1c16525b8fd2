# VW_01059_6_2_EN_2011-11_CAD&CAM数据要求_第6_2部分：CAD系统CATIA V5_回路.pdf

## 文档信息
- 标题：
- 作者：
- 页数：13

## 文档内容
### 第 1 页
Group standard
VW 01059-6-2
Issue 2015-11
Class. No.:
22632
Descriptors:
CAD, CATIA, cable routing, naming convention, convention, guideline, electrical components, electrics,
electric system
Requirements for CAD/CAM Data – CATIA V5-6 CAD System
Part 2: Electric Cable Routing
Previous issues
VW 01059-6 Supplement 2: 2005-07, 2006-12, 2008-03, 2008-12, 2011-08
Changes
The following changes have been made to VW 01059-6 Supplement 2: 2011-08:
–
Status of the document changed from supplement to standard
–
Standard title expanded
–
Technical responsibility changed
–
Section *******, Figure 14 replaced with correct version
–
Section "Applicable documents" updated
Contents
Page
Scope ......................................................................................................................... 2
Terms ......................................................................................................................... 2
Requirements ............................................................................................................. 4
Electrical components ................................................................................................ 4
Electric cable routing .................................................................................................. 5
Basic structure elements ............................................................................................ 6
Root product ............................................................................................................... 7
ENVIRONMENT ......................................................................................................... 8
SECTIONHARNESS .................................................................................................. 9
SKELETON ................................................................................................................ 9
Assembly module, hat module, and reference hat module ........................................ 9
CONNECTOR, SUPPORT, and SPLICE ................................................................. 10
Geometrical bundle .................................................................................................. 11
1
2
3
3.1
3.2
3.2.1
3.2.1.1
3.2.1.2
3.2.1.3
3.2.1.4
3.2.2
*******
3.2.2.2
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Page 1 of 13
Technical responsibility
The Standards department
EEXS/4
Heide Melchior
Tel.: +49 5361 9 38113
EKDV/4 Norbert Wisla
EKDV
Tel.: +49 5361 9 48869
Maik Gummert
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Page 2
VW 01059-6-2: 2015-11
Multi-branchables (or bundle segments) .................................................................. 12
Storing the data in the database system .................................................................. 12
Applicable documents .............................................................................................. 13
*******
3.3
4
Scope
Deviating from VW 01059-6, this standard applies to electric cable routing within the Volks‐
wagen Group. Until further notice, it applies to product data types ELV, EKR, ELX, and EN.
VW 01059-6-3 does not apply.
In this standard, CATIA-specific terms are written in italics.
The following documents must also be taken into account:
–
Methodology Guideline "Methodology Guideline CATIA V5: Wiring Harness Design Engineer‐
ing"
–
Connector Guidelines "Guidelines for the Generation of Electrical Components in CATIA V5"
–
Procedure for CATIA V5 "Addition to the Volkswagen Standard Environment for Electric Wiring
Harness Routing"
–
Work Instruction 37_AA_EEK  "Regarding the Provision of Wiring Harness Data for DMU in
CATIA V5"
–
Work Instruction 57_AA_EEK  "Engine-Oriented Engine-Transmission Unit Wiring"
If necessary, these documents must be obtained from the purchaser.
Terms
BNS
A bundle segment is a graphical 3-D representation of a wiring harness
section.
 
EKR
The product data type for electrical components (German acronym for
"electrical component, reduced")
 
ELV
The product data type for the 3-D data used in VOBESPlus. It is used for
what are referred to as 150% wiring harness models, and is used in order
to derive drawings, among other purposes. Because of this, it must not con‐
tain any changes that have not been approved.
 
ELX
Among other things, this product data type is used for the data used in Digi‐
tal Mock-Up (DMU) that is stored in the engineering data management sys‐
tem (KVS) (does not apply to Audi). It is used:
1. For the 180% wiring harness models
2. For the models of geometrically relevant modules in the DMU
 
GBN
A CATProduct that is defined as a geometrical bundle (GBN) with CAT‐
IA V5 (electrified).
 
VOBES
German acronym for the system used in the Volkswagen Group for the
development of electric systems.
1  
2  


### 第 3 页
Page 3
VW 01059-6-2: 2015-11
 
Wiring harness
The wiring harness is a subsection of the electric system. There are differ‐
ent wiring harness versions (150% and 180%) for the process chain. The
wiring harness consists of one or multiple routing sections.
 
150% wiring harness
The 150% routing model of a wiring harness contains all the electrically log‐
ical variants of the wiring harness and the routing variants (BOM-relevant).
 
180 % wiring harness In addition to the electrically logical routing variants of the wiring harness
and the routing variants, the 180% routing model of a wiring harness also
contains the installation variants (not BOM-relevant).
 
Routing section
A distinction is drawn between the following:
1. The "geometrical" routing section refers to the wiring harness of the elec‐
tric system as a whole or a section of the wiring harness.
2. The SECTIONHARNESS CATProduct is a basic structure element of the
wiring harness design in CATIA V5.
 
Assembly module
Contains all cable routes and components within a vehicle platform that are
identical across brands/hats.
 
Hat module
Contains all cable routes and components of the vehicle-related hat parts.
 
Reference hat mod‐
ule
Reference hat modules contain routing sections that serve as a reference
for many, but not for all, hat modules. For more detailed information, con‐
sult the "Methodology Guideline CATIA V5: Wiring Harness Design Engi‐
neering".
 
Module (family)
Each modular harness is composed of several module families. Each mod‐
ule family stands for a specific function (e.g., headlamp). The complete har‐
ness configuration is obtained by combining specific module families. In ad‐
dition, these module families may have module variants.
 
Module variants
Module variants result from geometry modifications (e.g., length variant) or
from modification of the electric circuitry. In the BOM of a wiring harness,
they are specified by adding a suffix to the module part number. Module
variants are mutually exclusive within a module family, as they represent
the same function.
 
Installation variants
These variants are the result of modifications to a module variant's geomet‐
rical representation that do not involve changes to the electrical circuitry or,
e.g., the length. Since these variants only arise in the context of DMU pro‐
cesses, and since a new part number is not generated, they are not inclu‐
ded in the bill of materials.
For additional terms, refer to VW 01059-6 and VW 01059-6-1.


### 第 4 页
Page 4
VW 01059-6-2: 2015-11
Requirements
Electrical components
The electrical components to be used for electric cable routing are stored and made available in
the Volkswagen electrical catalog. All electrical components must be used as EKRs. When the
electrical components are used, the appropriate department's methods and specifications must be
adhered to.
Electrical components that are not available in the Volkswagen electrical catalog must be created
as per the methods and specifications of the "Electric System Development Process" department.
In addition to the general specifications in VW 01059-6, the following specifications must be com‐
plied with for electrical components:
–
The part number matches the actual part number (max. 14 digits – is not filled).
–
Underscores ("_") are used to separate the part number.
–
Standard parts begin with "N" or with "N__"; see figure 2. This must be coordinated with the
"Electric System Development Process" department.
–
Special components, such as splices, multipurpose components, and dummy components,
have a special name. These exceptions must be coordinated with the "Electric System
Development Process" department.
–
If an electrical component is added to a CATProduct (see section *******), the CATIA V5 in‐
stance name must be assigned as per the VOBESPlus process specifications.
–
If a cable protection element (protective covering) is added to the electric cable routing, the
CATIA V5 names are generated automatically. A time stamp will be added to the part number;
see figure 3. These names must be adopted and must not be changed.
Figure 1 – Sample showing how electrical components are named
3  
3.1  


### 第 5 页
Page 5
VW 01059-6-2: 2015-11
Figure 2 – Examples showing how standard parts are named
Figure 3 – Examples showing how cable protection elements are named
Electric cable routing
The electric cable routing is designed in a product structure; see figure 4. This product structure is
made up of basic structure elements and structure elements.
The following are the basic structure elements: the ELX/ELV root product, ENVIRONMENT (if ap‐
plicable), and SECTIONHARNESS CATProducts and the SKELETON CATPart.
The structure elements are inserted in the SECTIONHARNESS. The assembly module, hat mod‐
ule, and reference hat module structure elements are used to subdivide the cable routing into sec‐
tions.
The CATPart and CATProduct arrangement must be complied with as shown in figure 4.
3.2  


### 第 6 页
Page 6
VW 01059-6-2: 2015-11
Figure 4 – Overview of product structure for electric cable routing
Basic structure elements
In addition to the general specifications of VW 01059-6, the following specifications apply to the
ELX/ELV root product, ENVIRONMENT, SECTIONHARNESS, and SKELETON basic structure el‐
ements:
–
The part number (KVS code) and designation are defined based on the routing section. Under‐
scores ("_") are used to separate the part number.
–
The version and the alternative must be gathered from the appropriate department's docu‐
ments concerning methodologies.
3.2.1  


### 第 7 页
Page 7
VW 01059-6-2: 2015-11
–
The comment is not filled with underscores.
–
The file name, instance name, and part number are identical.
Figure 5 – Example showing the syntax used to name basic structure elements
Root product
The root product is the topmost electric cable routing CATProduct. The root product can either be
ELX or ELV.
ELX
ELX represents a development version and contains the 180% wiring harness routing model with
all installation variants.
The following must be observed when naming:
–
CAD type "KPR" (design product)
–
Product data type "ELX"
–
A user-defined string with a max. of 20 characters is permissible in the comment. The content
must be agreed upon with the appropriate department.
Figure 6 – Example showing how the ELX CATProduct is named
ELV
ELV represents a production/release version or a drawing version and contains the 150% wiring
harness routing model.
The following must be observed when naming:
3.2.1.1  
3.2.1.1.1  
3.2.1.1.2  


### 第 8 页
Page 8
VW 01059-6-2: 2015-11
–
CAD type "KPR" (design product)
–
Product data type "ELV"
–
The drawing date is set in the comment in YYYYMMDD format (e.g., ********).
Figure 7 – Example showing how the ELV CATProduct is named
ENVIRONMENT
The ENVIRONMENT CATProduct is only created in the ELX root product. The environment data
required for the design can be loaded under the ENVIRONMENT. Before being stored in the data‐
base system, environment data must be deleted from the data set.
The following must be adhered to for the ENVIRONMENT CATProduct:
–
CAD type "INP" (input geometry)
–
Product data type "ELX"
–
The comment is "ENVIRONMENT" or "UMGEBUNG" in German.
–
There is only one ENVIRONMENT at a time.
Figure 8 – Examples showing how the ENVIRONMENT CATProduct is named
3.2.1.2  


### 第 9 页
Page 9
VW 01059-6-2: 2015-11
SECTIONHARNESS
The following must be observed for the SECTIONHARNESS CATProduct:
–
CAD type "GEO" (geometry)
–
Same product data type as root product
–
The comment is "SECTIONHARNESS" or "VERLEGEBEREICH" in German.
–
There is only one SECTIONHARNESS at a time.
Figure 9 – Examples showing how the SECTIONHARNESS CATProduct is named
SKELETON
The following must be adhered to for the SKELETON CATPart:
–
CAD type "SKE" (skeleton)
–
Same product data type as root product
–
The comment is "SKELETON."
–
There is only one SKELETON at a time.
Figure 10 – Example showing how the SKELETON CATPart is named
Assembly module, hat module, and reference hat module
The assembly module, hat module, and reference hat module structure elements divide the routing
section's cable routing into two or more sections.
The assembly module and hat module CATProducts are found one or more times under the SEC‐
TIONHARNESS. If necessary, one or more reference hat modules can also be created under the
SECTIONHARNESS.
In addition to the general specifications in VW 01059-6, the following must be observed:
3.2.1.3  
3.2.1.4  
3.2.2  


### 第 10 页
Page 10
VW 01059-6-2: 2015-11
–
The name starts with the fixed character string "BAUKST" (for assembly modules), "HUT" (for
hat modules), or "REFHUT" (for reference hat modules).
–
The root product's designation follows, separated by an underscore.
In consultation with the appropriate department and with the system administrator responsible
for the department, this designation can deviate from the root product's designation.
The designation consists of a maximum of 18 characters and is not filled with underscores if
the name is shorter than 18 characters.
–
If multiple vehicle projects – and perhaps their left-hand drive/right-hand drive variance – are
included in a single data set, the corresponding structure elements must be differentiated by a
code. This code is limited to 5 characters and is separated from the rest of the name by an
underscore.
The code must be selected as per the methods and specifications of the appropriate depart‐
ment (e.g., V01VW, V03AU, V01, L0L) and broken down in the description of the root product
of the routing section.
–
The file name, instance name, and part number are identical.
Figure 11 – Examples showing how the structure elements are named (assembly module, hat
module, and reference hat module)
CONNECTOR, SUPPORT, and SPLICE
In the CONNECTOR, SUPPORT, and SPLICE CATProducts, the corresponding electrical compo‐
nents are added.
*******  


### 第 11 页
Page 11
VW 01059-6-2: 2015-11
These CATProducts are inserted into the product structure under the assembly module, hat mod‐
ule, and reference hat module CATProducts. The following must be observed here:
–
The fixed character string is CONNECTOR, SUPPORT, or SPLICE.
–
The name that follows matches the name of the assembly module, hat module, or reference
hat module. An underscore is used to separate the name in this case as well.
–
The CONNECTOR, SUPPORT, and SPLICE CATProducts are each found once in the assem‐
bly module, once in the hat module, and once in the reference hat module.
–
The file name, instance name, and part number are identical.
Figure 12 – Examples showing how the CONNECTOR, SUPPORT, and SPLICE CATProducts are
named
Geometrical bundle
The geometrical bundle (GBN) contains cable routing sections, i.e., the multi-branchables and bun‐
dle segments, as well as the cable protection elements.
Geometrical bundles are inserted into the product structure under the assembly module, hat mod‐
ule, and reference hat module CATProducts. Empty geometrical bundles must be removed from
the product structure.
In addition to the general specifications in VW 01059-6, the following must be observed when geo‐
metrical bundles are created:
–
The CATProducts must be defined as geometrical bundles in CATIA V5.
–
The individual elements of the name are separated by underscores.
–
The individual components are not filled with underscores.
–
Elements of the name are:
1.
A short, representative name for the geometrical bundle
2.
The name of the assembly module, hat module, or reference hat module
–
The name consists of a maximum of 49 characters. If there are more than 49 characters, the
first part must be shortened accordingly.
–
The file name, instance name, and part number are identical.
3.2.2.2  


### 第 12 页
Page 12
VW 01059-6-2: 2015-11
Figure 13 – Examples showing how to name geometrical bundles
Multi-branchables (or bundle segments)
Multi-branchables and bundle segments represent the cables and must be created as per the ap‐
propriate department's methods and specifications.
The name of the CATPart representing the multi-branchable or bundle segment contains, as its
first part, the geometrical bundle's name.
The second part is the designation "Multi-Branchable" or "Bundle_Segment" and a number. The in‐
dividual elements of the name are separated by a hyphen. The name is created automatically if the
CATIA V5 settings have been set correctly.
The following must be observed for multi-branchables and bundle segments:
–
The file name (FileName) and part number are identical; the instance name can be different if
necessary.
–
In consultation with the appropriate department and the system administrator responsible for
the department, the name of the multi-branchable or the bundle segment may be different.
Figure 14 – Examples showing how multi-branchables (and bundle segments) are named
Storing the data in the database system
In order to store the electric cable routing, the ELX [does not apply to Audi] and ELV root products
are stored in KVS together with the product structure. The data must be stored as per the appropri‐
ate department's methods and specifications.
*******  
3.3  


### 第 13 页
Page 13
VW 01059-6-2: 2015-11
In addition, for the DMU process with Virtual Product Model (VPM), one or more CATParts from
the ELX CATProduct are required. The procedure used to create CATParts and store data in KVS
must be gathered from the appropriate department's methods.
Vehicle projects that are only represented in the CONNECT system do not require an additional
CATPart from the ELX CATProduct.
Applicable documents
The following documents cited in this standard are necessary to its application.
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
37_AA_EEK
Regarding the Provision of Wiring Harness Data for DMU in CATIA V5
57_AA_EEK
Engine-Oriented Engine-Transmission Unit Wiring
Connector Guidelines
Guidelines for the Generation of Electrical Components in CATIA V5
Methodology Guideline
Methodology Guideline CATIA V5: Wiring Harness Design Engineering
Procedure for
CATIA V5
Addition to the Volkswagen Standard Environment for Electric Wiring
Harness Routing
VW 01059-6
Requirements for CAD/CAM Data - CAD System CATIA V5-6
VW 01059-6-1
Requirements for CAD/CAM Data – CATIA V5-6 CAD System – Part 1:
Terms
4  

