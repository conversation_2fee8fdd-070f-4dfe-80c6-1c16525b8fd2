# VW_01059_6_3_EN_2011-11_CAD&CAM数据要求_第6_3部分：CAD系统CATIA V5_过程链适配器.pdf

## 文档信息
- 标题：
- 作者：
- 页数：19

## 文档内容
### 第 1 页
Group standard
VW 01059-6-3
Issue 2015-11
Class. No.:
22632
Descriptors:
CAD, process chain, Adapter, Process chain adapter, PCA, CATIA, CAM, DMU
Requirements for CAD/CAM Data – CATIA V5-6 CAD System
Part 3: Process Chain Adapter (PCA), DMU CATPart, and Optional Adapters
Previous issues
VW 01059-6 Supplement 3: 2006-12, 2007-07, 2010-11, 2011-11, 2012-06
Changes
The following changes have been made to VW 01059-6 Supplement 3: 2012-06:
–
Status of the document changed from supplement to standard
–
Standard title expanded
–
CAD system changed from CATIA V5 to V5-6
–
Technical responsibility changed
–
Sections 1 to 6: revised and restructured; errors removed from tables and table formatting ad‐
justed; tables 1 to 6: footnotes 2 and 3 added
–
Section 5 "Contents of optional adapter parts": contents of optional adapter parts added
–
Section 6 "Generally valid tables" added: Tables divided and restructured
–
Section 7 "Applicable documents" updated
Contents
Page
Scope ......................................................................................................................... 2
Abbreviations and terms ............................................................................................ 2
Abbreviations ............................................................................................................. 2
Output adapters ......................................................................................................... 2
Content of a process chain adapter (PCA) ................................................................ 3
Sheet metal parts ....................................................................................................... 4
Cast metal parts ......................................................................................................... 6
1
2
2.1
2.2
3
3.1
3.2
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Page 1 of 19
Technical responsibility
The Standards department
K-SIPE-2/3
Stefan Biernoth
Tel.: +49 5361 9 48896
EKDV/4 Norbert Wisla
EKDV
Tel.: +49 5361 9 48869
Maik Gummert
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Page 2
VW 01059-6-3: 2015-11
Plastic parts ................................................................................................................ 7
Profile parts ................................................................................................................ 8
Other parts ............................................................................................................... 10
Content of a DMU CATPart ...................................................................................... 11
Contents of optional adapter parts ........................................................................... 12
Adapter "Oxx_TMT" for 3DZP .................................................................................. 12
Generally valid tables ............................................................................................... 13
Applicable documents .............................................................................................. 19
3.3
3.4
3.5
4
5
5.1
6
7
Scope
This standard is a supplement to Volkswagen standard VW 01059-6 for generating process chain
adapters (PCA) and Digital Mock-Up (DMU) CATParts for individual parts and other optional adapt‐
ers.
In the following tables, the elements to be provided are divided into three priority levels:
–
P – Compulsory element for all parts
–
S – Compulsory element for all parts, if present. In cases where such elements cannot exist for
topological reasons, they may be used as optional elements (possibility to switch to optional,
"Switch")
–
O – Optional element that must be provided, if available in the part. Optional elements can be‐
come compulsory elements in agreement between the data provider and data recipient.
Information specified as compulsory elements must be present in a released design status. It is not
necessary to generate optional elements if these are not created during the design process.
Abbreviations and terms
Abbreviations
LPS
Local positioning system; see VW 01055
 
RB
German acronym for reference processing: see VW 01055
 
3DZP
3-D drawing-free process; see intranet http://catia.wob.vw.vwg:8080, Meth‐
ods & Guidelines, 3D Drawingless Process (3DZP) or http://www.vwgrou‐
psupply.com, R&D Services, 3DZP.
For additional abbreviations and terms, see VW 01059-6-1.
Output adapters
Output adapters are documents that are used to transmit process-chain-relevant geometric and
non-geometric component information to data recipients. Output adapters are a summary of the
(preliminary) design results. Output adapters include the documents of CAD types OUT, DMU,
PCA, and O01 to O99. All output adapters comply with the naming convention specified in
VW 01059-6, section 3.2.2. Rules for naming and structuring output adapters are defined in
VW 01059-6-4.
For output adapters that are archived outside of a CAD structure, only the TEIVON number (TEI‐
VON = Part Number Assignment Online) and the version number need to agree with the root prod‐
ucts of the basic source document.
1  
2  
2.1  
2.2  


### 第 3 页
Page 3
VW 01059-6-3: 2015-11
The additional CATIA V5-6 application OutGen of Volkswagen AG, which has been harmonized
throughout the Group, must be used to declare the elements to be provided and to create the
adapter CATParts.
Output adapters can be:
PCA
The CATPart with the CAD type PCA (process chain adapter) is used to
bundle and structure all geometric and non-geometric information necessa‐
ry for the data recipient in the form of a process chain adapter. The geome‐
try and parameters included therein must be "published". The PCA must
not contain any links to other documents if it is archived alone or in an OUT
CATProduct.
If a PCA is archived as a single CATPart under a product data type relevant
to a Digital Mock-Up (DMU), then this also acts to assure the DMU process.
 
DMU
The CATPart with CAD type DMU provides the geometry representing the
vehicle component as a solid/volume to be used in the following DMU proc‐
ess, or if otherwise not technically possible, as a surface (with material vec‐
tor for a single-side surface).
 
O01 to O99
The CATParts with CAD type Oxx are special adapters that make available,
either in isolation or associatively, elements from the KPR geometry area or
from other output adapters. They must contain all geometric and non-geo‐
metric information necessary for the data recipient.
Content of a process chain adapter (PCA)
The process chain adapter is used for the structured transmission of data to all data recipients in
parallel and downstream processes.
The following parts are distinguished by the material used, its treatment (manufacturing process),
and the design method used:
–
Sheet metal parts, see table 1
–
Cast metal parts, see table 2
–
Plastic parts, see table 3
–
Profile parts, see table 4, and
–
Other parts, see table 5.
A "type_of_design" parameter is already contained in the design part (GEO, G01, etc.) with the
group structure part and must be set accordingly.
 
 
Furthermore, two time phases are differentiated in the tables:
–
Draft: work versions as part model or draft and
–
Release: data versions archived as part model by the prototype model phases, or by P-release
and B-release at the latest.
Accordingly, elements that are optional in an early phase become compulsory for a release-rele‐
vant version of the part.
3  


### 第 4 页
Page 4
VW 01059-6-3: 2015-11
Sheet metal parts
Table 1 – Elements for sheet metal parts
Element description
Structure/element name1)
Element
type
Draft
Re‐
lease
Hide/
show
Finished part solid
(if applicable, thickened sur‐
face model or combination of
the tailored blanks)
PartBody[_*]
Body with the property PartBody 2), 3)
 
Rule for ASSY PCAs:
The PartBody must be blank. The descriptive
bodies that contain the geometry then follow this
nomenclature.
 
Body[_*]  
 
Body
O
S
S
Finished part surface
(one-sided description of the
designed side)
Part_Geometry/Part_Geometry[_*]
or, if multiple parts are combined
Part_Geometry_"Descriptive_Name"
Surface
P
P
S
Material vector4)
Material_Vector/Material_Vector[_*]
Line
P
P
S
Part surface trimmed and non-
pierced
Part_Geometry_Trimmed_Non_Pierced/Part_Ge‐
ometry_Trimmed_Non_Pierced[_*]
Surface
O
S
H
RPS elements
See table 8 "RPS elements"
LPS elements
See table 9 "LPS elements"
RB elements
See table 10 "RB elements"
Hole tool application elements
(LTA elements)
See table 11 "LTA elements"
Parameter and property
See table 12 "Parameter and property"
Axis system
See table 13 "Axis system"
Contact and flange areas
Contact_Areas /Contact_Area__ "Contact Part
Name"
Geometrical
set,
curve,
surface
O
S
Curve
= S
Surface
= H
Part surface, untrimmed
(surface model of the sheet
metal part before trimming op‐
erations)
Part_Geometry_Untrimmed/Part_Geometry_Un‐
trimmed[_*]
(surfaces numbered consecutively _nn, if neces‐
sary)
Surface
O
O
H
Outer trim contour of the part
as curved element
Boundaries/Outer_Trim_Contour[_*]
Curve
O
O
H
Trim contours for inner trim
(several individual contours
possible, no holes)
Boundaries/Inner_Trim_Contour_"Descrip‐
tive_Name"
(numbered consecutively _nn, if necessary)
Curve
O
O
H
Part surface untrimmed and
pre-stressed
Part_Geometry_Untrimmed_Pre‐
stressed/Part_Geometry_Untrimmed_Pre‐
stressed[_*]
Surface
O
O
H
3.1  


### 第 5 页
Page 5
VW 01059-6-3: 2015-11
Element description
Structure/element name1)
Element
type
Draft
Re‐
lease
Hide/
show
Part surface trimmed, non-
pierced, and pre-stressed
Part_Geometry_Trimmed_Non_Pierced_Prestres
sed/Part_Geometry_Trimmed_Non_Pierced_Pre‐
stressed[_*]
Surface
O
O
H
Finished part surface pre-
stressed
Part_Geometry/Part_Geometry_Prestressed[_*]
Surface
O
O
H
Part surface unfolded
Part_Geometry_Unfolded/Part_Geometry_Unfol‐
ded[_*]
Surface
O
O
H
Tailored blank surfaces
Part_Geometry/Part_Geometry_n,
Part_Geometry/Material_Vector_n,
(where n is a consecutive number starting with 2)
Surface,
line
O
O
S
Tailored blank parameters
(parameters for material, mate‐
rial thickness, density, and
weight are similarly arranged
directly under the standard ele‐
ment in the structure tree)
Parameters/material_thickness_n,
Parameters/material_density_n,
Parameters/weight_n,
(where n is a consecutive number starting with 2)
Parameter
O
O
–
Main mold removal direction
Main_Tooling_Direction/Main_Tooling_Direc‐
tion[_*]
Line
O
O
H
1) The structure of the geometrical sets and parameter sets is shown using a slash "/" as a separator. The last entry is the
element name. The same name is used for publication in the PCA CATPart
2) Rule for ASSY PCAs: The PartBody must be blank. The descriptive bodies that contain the geometry then follow this
nomenclature.
3) The rule for the blank PartBody currently (Group Reference CATIA (GRC) 5.4.x.) does not apply to PCA ASSY adapters
generated with OutGen (for technical reasons).
4) The material vector begins on the zero surface and displays the material thickness in the offset direction. (Vector
length = 100 × material thickness.)


### 第 6 页
Page 6
VW 01059-6-3: 2015-11
Cast metal parts
Table 2 – Elements for cast metal parts
Element description
Structure/element name1)
Element type
Draft
Re‐
lease
Hide/
show
Finished part solid
PartBody[_*] 2), 3)
Body with the property PartBody
 
Rule for ASSY PCAs:
The PartBody must be blank. The descriptive
bodies that contain the geometry then follow this
nomenclature.
 
Body[_*]
Body
S
S
S
Finished part surface
Part_Geometry/Part_Geometry[_*]
or, if multiple parts are combined
Part_Geometry_"Descriptive_Name"
Surface
P
P
S
Mold removal direction
Information/Molding_Direction[_*]
Line
P
P
S
Mold parting line
Mold_Parting_Line/Mold_Parting_Line[_*]
Curve
O
P
S
Projected surface
(of mold in main mold removal
direction)
Projected_Surface/Projected_Surface[_*]
Surface
O
P
H
Component surfaces, inner
mold part without ribs
Inner_Surfaces/Inner_Surfaces_with‐
out_Ribs_nn[_*]
Surface
O
S
H
Component surfaces, inner
mold part with ribs
Inner_Surfaces/Inner_Surfaces_with_Ribs_nn
Surface[_*]
Surface
O
P
H
Component surfaces, outer
mold part without ribs
Outer_Surfaces/
Outer_Surfaces_without_Ribs_nn[_*]
Surface
O
S
H
Component surfaces, outer
mold part with ribs
Outer_Surfaces/Outer_Surfaces_with_Ribs_nn[_*]
Surface
O
P
H
RPS elements
See table 8 "RPS elements"
LPS elements
See table 9 "LPS elements"
RB elements
See table 10 "RB elements"
LTA elements
See table 11 "LTA elements"
Parameter and property
See table 12 "Parameter and property"
Axis system
See table 13 "Axis system"
Contact and flange areas
Contact_Areas /Contact_Area__ "Contact Part
Name"
Geometrical
set,
curve,
surface
O
S
Curve
= S
Sur‐
face
= H
Slide direction
Information/Slide_Direction[_*]
(if more than one slide direction: Slide_Direc‐
tion_01…n_[*])
Line
O
O
S
Component surfaces, slide
without ribs
Slide_Surfaces/Slide_Surfaces_with‐
out_Ribs_nn[_*]
Surface
O
O
H
3.2  


### 第 7 页
Page 7
VW 01059-6-3: 2015-11
Element description
Structure/element name1)
Element type
Draft
Re‐
lease
Hide/
show
Component surfaces, slide with
ribs
Slide_Surfaces/Slide_Surfaces_with_Ribs_nn[_*]
Surface
O
O
H
Component surfaces created
by machining
Machining/Feature name of the selected element
Surface,
curve,
geometrical
set
O
O
H
1) The structure of the geometrical sets and parameter sets is shown using a slash "/" as a separator. The last entry is the
element name. The same name is used for publication in the PCA CATPart
2) Rule for ASSY PCAs: The PartBody must be blank. The descriptive bodies that contain the geometry then follow this
nomenclature.
3) The rule for the blank PartBody currently (GRC 5.4.x.) does not apply to PCA ASSY adapters generated with OutGen
(for technical reasons).
Plastic parts
Table 3 – Elements for plastic parts
Element description
Structure/element name1)
Element type Draft
Re‐
lease
Hide/
show
Finished part solid
(thickened surface model, if
necessary)
PartBody[_*] 2), 3)
Body with the property PartBody
 
Rule for ASSY PCAs:
The PartBody must be blank. The descriptive
bodies that contain the geometry then follow this
nomenclature.
 
Body[_*]
 
Body
S4)
S4)
S
Finished part surface
(one-sided description of the
designed side; extract from sol‐
id possible)
Part_Geometry/Part_Geometry[_*]
or, if multiple parts are combined
Part_Geometry_"Descriptive_Name"
Surface
S4)
S4)
S
Mold parting line
Mold_Parting_Line/Mold_Parting_Line[_*]
Curve
O
S
S
Contact and flange areas
Contact_Areas/Contact_Area_"Contact compo‐
nent name"
Geometrical
set,
curve,
surface
O
O
Curve
= S
Surface
= H
RPS elements
See table 8 "RPS elements"
LPS elements
See table 9 "LPS elements"
RB elements
See table 10 "RB elements"
LTA elements
See table 11 "LTA elements"
Parameter and property
See table 12 "Parameter and property"
Axis system
See table 13 "Axis system"
Mold removal direction
Information/Direction_of_Mold_Separation[_*]
Line
O
O
S
3.3  


### 第 8 页
Page 8
VW 01059-6-3: 2015-11
Element description
Structure/element name1)
Element type Draft
Re‐
lease
Hide/
show
Slide direction(s)
Information/Slide_Direction (if more than one
slide direction: Slide_Direction_01…n_[*])
Line
O
O
S
Separated partial surfaces
Mold_Separation/Fixed_Side and Moving_Side
Surface
O
O
H
1) The structure of the geometrical sets and parameter sets is listed using a slash "/" as a separator. The last entry is the
element name. The same name is used for publication in the PCA CATPart
2) Rule for ASSY PCAs: The PartBody must be blank. The descriptive bodies that contain the geometry then follow this
nomenclature.
3) The rule for the blank PartBody currently (GRC 5.4.x.) does not apply to PCA ASSY adapters generated with OutGen
(for technical reasons).
4)Either a finished part solid or a finished part surface is obligatory, and the other element is thus optional.
Profile parts
Table 4 – Elements for profile parts
Element description
Structure/element name1)
Element
type
Draft
Re‐
lease
Hide/
show
Finished part solid
PartBody[_*] 2), 3)
Body with the property PartBody
 
Rule for ASSY PCAs:
The PartBody must be blank. The descriptive
bodies that contain the geometry then follow this
nomenclature.
 
Body[_*]
 
Body
O
S
S
Finished part surface
(extract from solid is possible)
Part_Geometry/Part_Geometry[_*]
or, if multiple parts are combined
Part_Geometry_"Descriptive_Name"
Surface
P
P
S
Trim contours at beginning and
end of profile part
Boundaries/Outer_Trim_Contour_01[_*]
and
Boundaries/Outer_Trim_Contour_02[_*]
(there are always 2 contours)
Curve
P
P
S
Part geometry trimmed but
non-pierced
(extract from solid is possible)
Part_Geometry_Trimmed_Non_Pierced/Part_Ge‐
ometry_Trimmed_Non_Pierced
Surface
O
S
H
Inner trim contours without
standard holes
Boundaries/Inner_Trim_Contour_01[_*]
and consecutive contours; where applicable
adopt feature name or attach description (if nee‐
ded, number consecutively _nn)
Curve
O
O
H
Contact and flange areas
Contact _Areas/Contact_Area_"Contact Part
Name"
Geometrical
set,
curve,
surface
O
O
Curve
= S
Surface
= H
RPS elements
See table 8 "RPS elements"
3.4  


### 第 9 页
Page 9
VW 01059-6-3: 2015-11
Element description
Structure/element name1)
Element
type
Draft
Re‐
lease
Hide/
show
LPS elements
See table 9 "LPS elements"
RB elements
See table 10 "RB elements"
LTA elements
See table 11 "LTA elements"
Parameter and property
See table 12 "Parameter and property"
Axis system
See table 13 "Axis system"
Parting line for
fluid-formed (IHU) parts
or
parting line
IHU_Mold_Parting/IHU_Mold_Parting[_*]
or
IHU_Mold_Parting/IHU_Mold_Parting_Line[_*]
Curve
O
O
S
1) The structure of the geometrical sets and parameter sets is listed using a slash "/" as a separator. The last entry is the
element name. The same name is used for publication in the PCA CATPart
2) Rule for ASSY PCAs: The PartBody must be blank. The descriptive bodies that contain the geometry then follow this
nomenclature.
3) The rule for the blank PartBody currently (GRC 5.4.x.) does not apply to PCA ASSY adapters generated with OutGen
(for technical reasons).


### 第 10 页
Page 10
VW 01059-6-3: 2015-11
Other parts
This part type is only permissible if the part cannot be assigned to any of the above-mentioned part
categories, e.g., glass parts, composite parts, etc.
All elements to be used in downstream processes must be agreed upon between the author of the
data and the recipient. These elements must be made available in the PCA.
Table 5 – Elements for other parts
Element description
Structure/element name1)
Element
type
Draft
Re‐
lease
Hide/
show
Finished part solid
(thickened surface model, if
necessary)
PartBody[_*] 2), 3)
Body with the property PartBody
 
Rule for ASSY PCAs:
The PartBody must be blank. The descriptive
bodies that contain the geometry then follow this
nomenclature.
 
Body[_*]
Body
S4)
S4)
S
Finished part surface
(one-sided description of the
designed side; extract from sol‐
id possible)
Part_Geometry/Part_Geometry[_*]
or, if multiple parts are combined
Part_Geometry_"Descriptive Name"
Surface
S4)
S4)
S
Material vector5)
Material_Vector/Material_Vector[_*]
Line
O
O
S
RPS elements
See table 8 "RPS elements"
 
 
 
 
LPS elements
See table 9 "LPS elements"
 
 
 
 
RB elements
See table 10 "RB elements"
 
 
 
 
LTA elements
See table 11 "LTA elements"
 
 
 
 
Parameter and property
See table 12 "Parameter and property"
 
 
 
 
Axis system
See table 13 "Axis system"
 
 
 
 
1) The structure of the geometrical sets and parameter sets is listed using a slash "/" as a separator. The last entry is the
element name. The same name is used for publication in the PCA CATPart
2) Rule for ASSY PCAs: The PartBody must be blank. The descriptive bodies that contain the geometry then follow this
nomenclature.
3) The rule for the blank PartBody currently (GRC 5.4.x.) does not apply to PCA ASSY adapters generated with OutGen
(for technical reasons).
4)Either a finished part solid or a finished part surface is obligatory, and the other element is thus optional.
5) The material vector begins on the zero surface and displays the material thickness in the offset direction. (Vector
length = 100 × material thickness.)
3.5  


### 第 11 页
Page 11
VW 01059-6-3: 2015-11
Content of a DMU CATPart
A DMU CATPart is used for structured data transfer to the DMU process.
A DMU CATPart must not contain more elements than those listed in table 6.
The elements of the DMU CATPart indicated in table 6 must be created uniformly, irrespective of
the material used, its processing (manufacturing process), and the design methodology applied.
The settings for the "type_of_design" parameter in the structure part have no effect on the content.
Table 6 – Elements of a DMU CATPart
Element description
Geometrical set structure/element name1)
Element type
Opt./
comp./
switch
Hide/
show
Finished part solid
(if applicable, thickened surface
model or combination of the
tailored blanks)
PartBody[_*] 2), 3)
Body with the property PartBody
 
Rule for ASSY DMUs:
The PartBody must be blank. The descriptive bod‐
ies that contain the geometry then follow this no‐
menclature.
Body[_*]
Body
S 4)
S
Finished part surface
(one-sided description of the
designed side, or perhaps more
than one with tailored blanks)
Part_Geometry/Part_Geometry[_*]
Surface
S 4)
S 5)
Axis system
(origin axis system 0;0;0)
See table 13 – Axis systems
Material vector6)
Material_Vector/Material_Vector[_*]
Line
S 4)
S
Type-of-design parameters
Parameters/type_of_design
Parameter
O
–
CAD_Roboter property
CAD_Roboter
Property
O
–
1) The structure of the geometrical sets and parameter sets is listed using a slash "/" as a separator. The last entry is the
element name.
2) Rule for ASSY DMUs: The PartBody must be blank. The descriptive bodies that contain the geometry then follow this
nomenclature.
3) The rule for the blank PartBody currently (GRC 5.4.x.) does not apply to DMU ASSY adapters generated with OutGen
(for technical reasons).
4)Either a finished part solid or a finished part surface is obligatory. The other element is then optional. If no solid geometry
can be delivered, then the material vector in the thickened direction must be submitted in addition to the finished part sur‐
face (if it is a one-sided surface derivation).
5) If a finished part solid is available, the associated finished part surface must be present but hidden
6) The material vector begins on the zero surface and displays the material thickness in the offset direction. (Vector
length = 100 × material thickness.) (Perhaps more than one with tailored blanks.)
4  


### 第 12 页
Page 12
VW 01059-6-3: 2015-11
Contents of optional adapter parts
Adapter "Oxx_TMT" for 3DZP
This adapter is created separately with the OutGen application and saved individually in the engi‐
neering data management system (KVS). It is used to create 3DZP documents in the KVS.
Table 7 – Elements of the 3DZP adapter
Element description
Structure/element name1)
Element type
Draft
Re‐
lease
Hide/
show
Auxiliary geometry
FTA_Auxiliary_Geometry
Geometrical
set
S
S
S
Product Manufacturing Infor‐
mation (PMI)
Annotation Set.1
Annotation
P
P
S
 
Captures
Annotation Set.1/Captures
Capture
P
P
S
Views
Annotation Set.1/Views
View
P
P
S
Reference element
Annotation Set.1/Datums
Datum
S
S
S
Reference frames
Annotation Set.1/Reference Frames
Reference
frame
S
S
S
Tolerances of shape and
positional tolerances
Annotation Set.1/Geometrical Tolerances
Geometrical
tolerance
S
S
S
Dimensions
Annotation Set.1/Dimensions
Dimension
S
S
S
Roughnesses
Annotation Set.1/Roughness
Roughness
S
S
S
Annotations
Annotation Set.1/Notes
Note
S
S
S
Restricted area
Annotation Set.1/Restricted Areas
Restricted
area
S
S
S
Construction geometry
(auxiliary geometry specif‐
ic to Functional Toleranc‐
ing & Annotation (FTA))
Annotation Set.1/Construction geometries
Construction
geometry
S
S
S
Additional elements for prod‐
uct documentation (early
drawing contents)
See table 1 through table 5, depending on component type2)
1) The structure of the geometrical sets and parameter sets is listed using a slash "/" as a separator. The last entry is the
element name. The same name is used for publication in the PCA CATPart.
2) So that the 3DZP process can maintain all information, all additional information must also be delivered per component
type as per PCA specifications.
5  
5.1  


### 第 13 页
Page 13
VW 01059-6-3: 2015-11
Generally valid tables
Table 8 – RPS elements
Element description
Structure/element name1)
Structure/example2)
Element type
Draft
Re‐
lease
Hide/
show
RPS elements are always governed by VW 01055. VW 01059-6-3 merely points out the structure elements to be used.
RPS elements
RPS_Elements/RPS_001_Hxy
Geometrical set with content defined below(all addi‐
tional RPS elements are numbered consecutively; the
mounting directions are indicated in the name of the
geometrical set or the element name [VW 01055 5)])
Geometrical
set
O
S
S
 
RPS point
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_Point
Point
– 4)
S
 
RPS plane
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_Plane
Plane
– 4)
H
 
RPS mounting direction
(in the RPS point in the
offset direction
(L = 10 x material thick‐
ness))
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_
Hole_Direction
Line
– 4)
S
 
RPS axes
(centerlines of the RPS
point or the RPS sur‐
face)
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_
Centerline_1
and
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_
Centerline_2
Line
– 4)
S
 
RPS surface
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_Surface
Surface
– 4)
S
 
RPS boundaries
RPS_Elements/RPS_001_Fy/RPS_001_Fy_
Boundary_Inner
and
RPS_Elements/RPS_001_Fy/RPS_001_Fy_
Boundary_Outer
and
RPS_Elements/RPS_001_Hxy/RPS_001_Hxy_
Boundary_Hole
Curve
– 4)
H
 
RPS annotations
Annotation Set.1/Notes/RPS_01_Hxy
Annotation
– 4)
 
RPS feature
RPS_Features/RPS_001_Hxy
Feature
(component
application
architecture
(CAA RPS))
O
O
S
RPS table
Tables/RPS_Table"index"
(index may be replaced by part number, if necessary)
Feature
(component
application
architecture
(CAA RPS))
O
O/P3)
–
6  


### 第 14 页
Page 14
VW 01059-6-3: 2015-11
Element description
Structure/element name1)
Structure/example2)
Element type
Draft
Re‐
lease
Hide/
show
RPS
RPS_Systems/*
Feature
(component
application
architecture
(CAA RPS))
O
O
H
Parameter
RPS dimension A
(or diameter)
RPS_Elements/RPS_001_Hxy/Size_A
Parameter
O
O
–
Parameter
RPS dimension B
(or thickness of the ring
surface)
RPS_Elements/RPS_001_Hxy/Size_B
Parameter
O
O
–
RPS description parame‐
ter
RPS_Elements/RPS_001_Hxy/Description
Parameter
O
O
–
1) The structure of the geometrical sets and parameter sets is listed using a slash "/" as a separator. The last entry is the
element name. The same name is used for publication in the PCA CATPart.
2) Structures/element names written in cursive are examples
3) Obligatory for use of 3DZP
4) Results from the control of the higher-order RPS element
5) The designation deviates in the current version of VW 01055 (version 2009-06); a change has been initiated to
VW 01055 to rectify this discrepancy. The naming convention shown here is used at one's own risk. VW 01055 is deci‐
sive.
Table 9 – LPS elements
Element description
Structure/element name1)
Structure/example2)
Element type
Draft
Re‐
lease
Hide/
show
LPS elements are always governed by VW 01055. VW 01059-6-3 merely points out the structure elements to be used.
LPS elements
LPS_Elements/LPS_001_Hxy
Geometrical set with content defined below(all addi‐
tional LPS elements are numbered consecutively;
the mounting directions are indicated in the name of
the geometrical set or the element name [VW 01055
5)])
Geometrical
set
O
S
S
 
LPS point
LPS_Elements/L04_001_Hxy/L04_001_Hxy_Point
Point
– 4)
S
 
LPS plane
LPS_Elements/L04_001_Hxy/L04_001_Hxy_Plane
Plane
– 4)
H
 
LPS mounting direction
(in the LPS point in the
offset direction
(L = 10 x material thick‐
ness))
LPS_Elements/L04_001_Hxy/L04_001_Hxy_
Hole_Direction
Line
– 4)
S
 
LPS axes
(centerlines of the LPS
point or the LPS surface)
LPS_Elements/L04_001_Hxy/L04_001_Hxy_
Centerline_1
and
LPS_Elements/L04_001_Hxy/L04_001_Hxy_
Centerline_2
Line
– 4)
S


### 第 15 页
Page 15
VW 01059-6-3: 2015-11
Element description
Structure/element name1)
Structure/example2)
Element type
Draft
Re‐
lease
Hide/
show
 
LPS surface
LPS_Elements/L04_001_Hxy/L04_001_Hxy_Surface
Surface
– 4)
S
 
LPS boundaries
LPS_Elements/L04_001_Fy/L04_001_Fy_
Boundary_Inner
and
LPS_Elements/L04_001_Fy/L04_001_Fy_
Boundary_Outer
and
LPS_Elements/L04_001_Hxy/L04_001_Fy_Hxy_
Boundary_Hole
Curve
– 4)
H
LPS annotations
Annotations Set.1/Notes/L04_01_Hxy
Annotation
O
S
S
LPS feature
LPS_Features/L04_01_Hxy
Feature (CAA
LPS)
O
O
S
LPS table
Tables/LPS_Table"index"
(index may be replaced by part number, if necessa‐
ry)
Feature (CAA
LPS)
O
O/P3)
–
LPS system
LPS_Systems/*
Feature (CAA
LPS)
O
O
H
Parameter
LPS dimension A
(or diameter)
LPS_Elements/L04_001_Hxy/Size_A
Parameter
O
O
–
Parameter
LPS dimension B
(or thickness of the ring sur‐
face)
LPS_Elements/L04_001_Hxy/Size_B
Parameter
O
O
–
LPS description parameter
LPS_Elements/L04_001_Hxy/Description
Parameter
O
O
–
1) The structure of the geometrical sets and parameter sets is listed using a slash "/" as a separator. The last entry is the
element name. The same name is used for publication in the PCA CATPart.
2) Structures/element names written in cursive are examples
3) Obligatory for use of 3DZP
4) Results from the control of the higher-order LPS element
5) See VW 01055


### 第 16 页
Page 16
VW 01059-6-3: 2015-11
Table 10 – RB elements
Element description
Structure/element name1)
Structure/example2)
Element type
Draft
Re‐
lease
Hide/
show
RB elements are always governed by VW 01055. VW 01059-6-3 merely points out the structure elements to be used.
RB elements
RB_Elements/RB_001_Hxy
Geometrical set with content defined below(all addi‐
tional RB elements are numbered consecutively; the
mounting directions are indicated in the name of the
geometrical set or the element name [VW 01055 5)])
Geometrical
set
O
S
S
 
RB point
RB_Elements/RB_001_Hxy/RB_001_Hxy_Point
Point
– 4)
S
 
RB plane
RB_Elements/RB_001_Hxy/RB_001_Hxy_Plane
Plane
– 4)
H
 
RB mounting direction
(in the RB point in the off‐
set direction
(L = 10 x material thick‐
ness))
RB_Elements/RB_001_Hxy/RB_001_Hxy_
Hole_Direction
Line
– 4)
S
 
RB axes
(centerlines of the RB
point or the RB surface)
RB_Elements/RB_001_Hxy/RB_001_Hxy_
Centerline_1
and
RB_Elements/RB_001_Hxy/RB_001_Hxy_
Centerline_2
Line
– 4)
S
 
RB surface
RB_Elements/RB_001_Hxy/RB_001_Hxy_Surface
Surface
– 4)
S
 
RB boundaries
RB_Elements/RB_001_Fy/RB_001_Fy_
Boundary_Inner
and
RB_Elements/RB_001_Fy/RB_001_Fy_
Boundary_Outer
and
RB_Elements/RB_001_Hxy/RB_001_Hxy_
Boundary_Hole
Curve
– 4)
H
RB annotations
Annotations Set.1/Notes/RB_01_Hxy
Annotation
O
S
S
RB feature
RB_Features/RB_001_Hxy
Feature (CAA
RB)
O
O
S
RB table
Tables/RB_Table"index"
(index may be replaced by part number, if necessa‐
ry)
Feature (CAA
RB)
O
O/P3)
–
RB system
RB_Systems/*
Feature (CAA
RB)
O
O
H
Parameter
RB dimension A
(or diameter)
RB_Elements/RB_001_Hxy/Size_A
Parameter
O
O
–
Parameter
RB dimension B
(or thickness of the ring sur‐
face)
RB_Elements/RB_001_Hxy/Size_B
Parameter
O
O
–


### 第 17 页
Page 17
VW 01059-6-3: 2015-11
Element description
Structure/element name1)
Structure/example2)
Element type
Draft
Re‐
lease
Hide/
show
RB description parameter
RB_Elements/RB_001_Hxy/Description
Parameter
O
O
–
1) The structure of the geometrical sets and parameter sets is listed using a slash "/" as a separator. The last entry is the
element name. The same name is used for publication in the PCA CATPart.
2) Structures/element names written in cursive are examples
3) Obligatory for use of 3DZP
4) Results from the control of the higher-order RB element
5) RB is not mentioned in the current version of VW 01055 (version 2009-06); a change has been initiated to VW 01055 to
rectify this discrepancy.
Table 11 – LTA elements
Element description
Structure/element name1)
Element type
Draft
Re‐
lease
Hide/
show
LTA elements
Hole_Elements /LTA_"Index"_"Descriptive_Name"
("Index" in the form of consecutive numbering of
holes/hole elements with the following syntax: lower
case letters + numbers)
Geometrical
set
O
O
S
Hole center point
Hole_Elements /LTA_"Index"_"Descrip‐
tive_Name" /LTA_"Index"_Point
Point
– 3)
S
Hole outlet point
Hole_Elements/LTA_"Index"_"Descrip‐
tive_Name"/LTA_"Index"_ThickPoint
Point
– 3)
H
Hole axes
Hole_Elements/LTA_"Index"_"Descrip‐
tive_Name"/LTA_"Index"_Centerline_1
and
Hole_Elements/LTA_"Index"_"Descrip‐
tive_Name"/LTA_"Index"_Centerline_2
Line
– 3)
S
Hole direction
(Hole generation direc‐
tion)
Hole_Elements/LTA_"Index"_"Descrip‐
tive_Name"/LTA_"Index"_Hole_
Direction
Line
– 3)
S
Hole boundary
Hole_Elements/LTA_"Index"_"Descrip‐
tive_Name"/LTA_"Index"_Boundary_Hole
Curve
– 3)
S
Hole annotations
Annotations Set.1/Notes/LTA_"Index"
Annotation
O
S
S
Hole feature
LTA_Features/LTA_"Index"
Feature (CAA
LTA)
O
O
S
Hole table
Tables/LTA_Table"Index"
(index can be replaced by part number if necessary)
Feature (CAA
LTA)
O
O/P2)
–
1) The structure of the geometrical sets and parameter sets is shown using a slash "/" as a separator. The last entry is the
element name. The same name is used for publication in the PCA CATPart.
2) Obligatory if LTA used for 3DZP use, provided no RPS table is available
3) Results from the control of the higher-order LTA element


### 第 18 页
Page 18
VW 01059-6-3: 2015-11
Table 12 – Parameter and property
Element description
Structure/element name1)
Structure/example2)
Element type
Draft
Re‐
lease
Hide/
show
Parameter and property
 
Materials parameters
Parameters/material
Parameter
P
P
–
 
Density parameters
Parameters/material_density
Parameter
O
P
–
 
Material thickness pa‐
rameters
Parameters/material_thickness
Parameters/material_thickness_"index" 3)
Parameter
P
P
–
 
Weight parameters
Parameters/weight
Parameter
O
P
–
 
Type-of-design parame‐
ters
Parameters/type_of_design
Parameter
P
P4)
–
 
Symmetry parameters
Parameters/symmetry
Parameter
P
P
–
 
Right-hand_part_number
parameters
Parameters/right-hand_part_number
Parameter
O
O
–
 
CAD_Roboter property
CAD_Roboter
Property
O
O
–
1) The structure of the geometrical sets and parameter sets is shown using a slash "/" as a separator. The last entry is the
element name. The same name is used for publication in the PCA CATPart.
2) Structures/element names written in cursive are examples.
3) Index begins with "2"
4) Omitted for cast metal parts
Table 13 – Axis system
Element description
Geometrical set structure/element name1)
Element type
Draft
Re‐
lease
Hide/
show
Axis system
 
Axis system
(origin axis system 0;0;0)
Axis systems/Absolute Axis System
Axis system
P
P
H
Additional axis systems
Axis systems/"Origin Name"[_*]
Axis system
O
P
H
1) The structure of the geometrical sets and parameter sets is shown using a slash "/" as a separator. The last entry is the
element name. The same name is used for publication in the PCA CATPart.


### 第 19 页
Page 19
VW 01059-6-3: 2015-11
Applicable documents
The following documents cited in this standard are necessary to its application.
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
VW 01055
Reference Point System (RPS); Specifications in Drawings and 3-D
CAD Models
VW 01059-6
Requirements for CAD/CAM Data - CAD System CATIA V5-6
VW 01059-6-1
Requirements for CAD/CAM Data – CATIA V5-6 CAD System – Part 1:
Terms
VW 01059-6-4
Requirements for CAD/CAM Data – CATIA V5-6 CAD System – Part 4:
Product Structures for Product Data Type TM
7  

