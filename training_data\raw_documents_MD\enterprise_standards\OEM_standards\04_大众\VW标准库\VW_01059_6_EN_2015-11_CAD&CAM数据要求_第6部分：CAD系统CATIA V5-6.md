# VW_01059_6_EN_2015-11_CAD&CAM数据要求_第6部分：CAD系统CATIA V5-6.pdf

## 文档信息
- 标题：
- 作者：
- 页数：15

## 文档内容
### 第 1 页
Group standard
VW 01059-6
Issue 2015-11
Class. No.:
22632
Descriptors:
CAD, CAM, CATIA, HyperKVS
Requirements for CAD/CAM Data – CATIA V5-6 CAD System
Previous issues
VW 01059-6: 2005-07, 2006-12, 2007-03, 2008-02, 2008-03, 2009-05, 2010-04, 2010-11, 2012-06
Changes
The following changes have been made to VW 01059-6: 2012-06:
–
CAD system changed from CATIA V5 to V5-6
–
Technical responsibility changed
–
Normative change: Status of the supplements for this standard changed from supplement to
standard
–
Section 2: Second sentence added
–
Section 3: Following text added to second paragraph, first sentence: "by using VWGRCLite;
see section 3.4."
–
Section 3.1: third and fourth bullet point: "optionally" replaced with "if necessary"; fifth bullet
point: texts "for the parametric scope" and "to the parametric scope" added
–
Section 3.2.2: last bullet point before Figure 1 added; Figure 1: "designation" replaced with
"name"; Table 1: for kinematics "KIN part (kinematic part)" replaced with "kinematics geome‐
try" and "special KIN parts" replaced with "kinematics geometry, differentiated"; for kinematics
→ KIN → CATProduct "-" changed to "+"; last paragraph removed, with the exception of
Table 1
–
Section *******: The sentence with the reference to the "contracting department" removed
–
Old section 3.3 removed, subsequent sections renumbered
–
Section 3.4 "Settings": Sentence with VWGRCLite obligation added; reference to the engineer‐
ing portal and the associated text removed; sentence about the "start package for the produc‐
tion equipment and method planning from Audi" removed
–
Section 3.5.1 "Individual vehicle parts": first two paragraphs new, paragraph about "vehicle
part models that are stored in HyperKVS as CATPart" removed; in the third paragraph about
"vehicle part models that are stored through direct archival or through IPP processes...": text
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Page 1 of 15
Technical responsibility
The Standards department
K-SIPE-2/3
Stefan Biernoth
Tel.: +49 5361 9 48896
EKDV/4 Norbert Wisla
EKDV
Tel.: +49 5361 9 48869
Maik Gummert
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5


### 第 2 页
Page 2
VW 01059-6: 2015-11
"product data type TM" replaced with "(target) product data type TM"; reference to
VW 01059-6-4 added to fifth paragraph; Figure 2 changed; last sentence in paragraph re‐
moved
–
Section 3.5.2 "Drawing documents (CATDrawing)": "the CATDrawings" added to the first sen‐
tence and "on which the CATDrawings are based" added to the second sentence
–
Section 3.6 "Accuracy": first paragraph about the accuracy specifications of VW 01059-2 re‐
moved; in the second paragraph: text "including with generative shape design" removed
–
Section 3.7 "LTA elements": Text reformulated
–
Section 3.11 "Drawing derivation": second bullet point: text "only data sets with update" re‐
placed with "only updated data sets"; fourth bullet point text "Volkswagen-Audi standard frame
as per VW 01014" replaced with the text "drawing frame for development as per VW 01014";
eighth bullet point: text "(applies to Volkswagen and Volkswagen Commercial Vehicles, other
brands can also use DoLittle)" removed
–
Section 4: Section "Applicable documents" updated
–
Section 5: Bibliography updated
Contents
Page
Scope ......................................................................................................................... 3
Abbreviations and terms ............................................................................................ 3
Requirements ............................................................................................................. 3
General notes ............................................................................................................. 3
Naming convention .................................................................................................... 4
General specifications ................................................................................................ 4
Naming conventions of Design Engineering .............................................................. 4
Naming convention of Production .............................................................................. 7
Infrastructure .............................................................................................................. 9
Settings ...................................................................................................................... 9
Storage in HyperKVS ................................................................................................. 9
Individual vehicle parts ............................................................................................. 10
Drawing documents (CATDrawing) .......................................................................... 10
Accuracy .................................................................................................................. 11
LTA elements ........................................................................................................... 11
RPS elements .......................................................................................................... 11
Link management ..................................................................................................... 11
Layer ........................................................................................................................ 12
Drawing derivation ................................................................................................... 12
Sketcher ................................................................................................................... 13
Power Copy and User Defined Feature (UDF) ........................................................ 13
Knowledge ware ....................................................................................................... 13
Component application architecture (CAA) applications .......................................... 13
Applicable documents .............................................................................................. 14
Bibliography ............................................................................................................. 14
1
2
3
3.1
3.2
3.2.1
3.2.2
3.2.3
3.3
3.4
3.5
3.5.1
3.5.2
3.6
3.7
3.8
3.9
3.10
3.11
3.12
3.13
3.14
3.15
4
5


### 第 3 页
Page 3
VW 01059-6: 2015-11
Scope
This standard provides specifications for working with the CATIA V5-6 computer-aided design
(CAD) system. Additional provisions are specified in the following standards:
–
VW 01059-6-1 contains specific terms for the CATIA V5-6 CAD system .
–
VW 01059-6-2 contains supplemental information and deviations for Electrical Engineering.
–
VW 01059-6-3 contains supplemental information on process chain adapters (PCA) and DMU
CATPart.
–
VW 01059-6-4 contains supplemental information on the product structures for the product da‐
ta type "part model."
–
VW 01059-6 supplement 5 "Requirements for CAD/CAM/PDM – CATIA Version 5 CAD Sys‐
tem – CONNECT" contains supplemental information on the CATIA V5 CAD system – CON‐
NECT.
Comment: successor standard VW 01059-6-5 Requirements for CAD/CAM/PDM – CAT‐
IA V5-6 CAD System – Part 5: CONNECT (in draft stage at time of publication).
Abbreviations and terms
For abbreviation and terms, see VW 01059-6-1.
CATIA-specific terms are written in italics.
Requirements
Volkswagen Group companies are obliged to use the current Volkswagen CATIA Group reference.
Contractors are obliged to use the standards and settings of the current CATIA/ENOVIA release
level of the Volkswagen Group by using VWGRCLite; see section 3.4. The licenses used must be
compatible with the license configuration used by Volkswagen. Other license configurations must
be agreed upon with the contracting department.
General notes
Designing requires careful planning before geometry creation can begin, as well as careful imple‐
mentation. The following must be taken into account:
–
The 3-D model must be designed in complete detail as a solid or surface model or as a combi‐
nation of both. It must be created in the parametric/associative CATIA V5-6 format. All of the
CATIA V5-6 parameters and design elements required to change or control the model must be
available. Deviations from these provisions require a justified written agreement. Operating
equipment must only be designed as solids, with the exception of integrated vehicle data,
adapters, control parts, and skeletons.
–
The following applies to design engineering:
–
The Group structure part must be used for in-house design scopes.
–
System suppliers may also use the OEM start part for their design scopes. In this case,
the system supplier must submit a process chain adapter (if necessary, a Digital Mock-Up
(DMU) CATPart) to the purchaser. The exact specifications are given in the Component
Performance Specification.
–
Development partners who are involved in the design process must use the Group struc‐
ture part by default. They may only use the OEM start part in agreement with the persons
1  
2  
3  
3.1  


### 第 4 页
Page 4
VW 01059-6: 2015-11
accepting the part/part owner for the parametric scope. In this case, the development part‐
ner must submit a process chain adapter (if necessary, a DMU CATPart) to the purchaser
in addition to the parametric scope. The exact specifications are given in the Component
Performance Specification.
–
The procedures of the appropriate departments and the standards, i.e., the parts of this stand‐
ard, must be observed.
–
Elements that are no longer needed must be deleted before the data set is stored permanently
in the engineering data management system (KVS). Links to catalogs and project folders must
be separated.
–
The size of CATIA V5-6 CATParts must not exceed 150 MBytes, as problems may otherwise
occur, for instance, when the data are converted to different formats.
–
The right-hand origin axis system (generated by CATIA or present in the released structure
parts or master models) must have its coordinate axes at the absolute origin of the CATPart
(X, Y, and Z = 0). Its position must not be changed, and it must not be renamed.
–
All axis systems must be hidden (hide) before the file is stored in HyperKVS.
EXCEPTION: Axis systems may remain visible (show) for documents of product data type BM
(production equipment model), MP (method plan), and TMP (part model position information).
–
The CATIA V5-6 settings must be such that CATParts cannot be created using the "Hybrid De‐
sign Mode."
Naming convention
General specifications
–
File names may only contain capital letters, numbers, hyphens, and underscores [A to Z, 0 to
9, -, _ ]. Spaces, umlauts, and special characters are not permissible. Underscores must be
used for spaces, and hyphens must be used for periods.
–
The file name without extension must not exceed 70 characters.
–
The file must not be renamed at the operating system level.
–
Missing characters are filled with underscores ("_").
–
The individual name elements are separated by underscores.
–
PartNumber and FileName must be identical; the InstanceName may deviate (deviations must
be agreed upon with the purchaser in advance).
–
Umlauts and special national characters are never permitted for file names.
NOTE 1: : NTool – the CATIA V5-6 supplementary application of Volkswagen AG – must be used
in order to ensure compliance with the naming conventions described below as well as other nam‐
ing conventions. It is available within the Volkswagen Group as well as for all suppliers and
development partners and provides an independent profile for each currently known and compul‐
sory naming convention.
NOTE 2: : Sheet names may only contain letters, numbers, hyphens, and underscores [A to Z,
a to z, 0 to 9, -, _ ]. Spaces, umlauts, and special characters are not permissible.
Naming conventions of Design Engineering
In addition to the general specifications, the following specifications must be observed when nam‐
ing (example in figure 1) CAD data to be released in Design Engineering (Electrical Engineering is
excepted; see VW 01059-6-2).
3.2  
3.2.1  
3.2.2  


### 第 5 页
Page 5
VW 01059-6: 2015-11
–
The part number is separated by an underscore ("_").
–
When the (KVS) substructure level 1 is used, the corresponding number (3 digits) must be en‐
tered in the file name, otherwise three underscores are entered in the file name.
–
Standard parts are only provided with the part number (13 or 14 digits). Examples:
N___123_456_78, N___023_456_7, 111_222_333_AA. They are specified exclusively by the
Standards department EKDV/3.
–
When file names are assigned, it must be ensured that the individual attribute values in the file
name correspond to the attribute values of the HyperKVS key assigned to the data set. Partic‐
ular attention must be paid to the following attributes:
–
Part number
–
Product data type
–
(KVS) document version and
–
(KVS) substructure (level 1: alternative, sheet)
–
If the date is specified in the name, it must have the format YYYY_MM_DD or YYMMDD and it
must be located from digits 61 to 70. Example:
0ZZ_000_000____GEO_TM__001_____SHEET_METAL_PART_____________2012_06_06
Figure 1 – Naming convention of Design Engineering


### 第 6 页
Page 6
VW 01059-6: 2015-11
NOTE 1 on table 1: The currently agreed CAD types (for CATPart, CATProduct, CATDrawing, and
CATAnalysis) are collected in table 1:
If a CATProduct structure is used, the CAD type "KPR" must be used for the component design
and the CAD type "ZSB" must be used for the assembly design in the root product.
NOTE 2 on table 1: The table provides an overview of the permissible CAD types. Precise instruc‐
tions on the use of the CAD types are given in the CAD manuals or procedure specifications of the
respective brands and departments.
Table 1 – CAD types and their application
Application
CAD type
Brief description
CATIA V5-6 document
CATProd
uct
CATPart
CATDra
wing
Part
KPR
Design product
+
–
– (+)
Input
INP
Input geometry, general
+
+
–
I01 to I99 Input geometry, differentiated
–
+
–
A01 to
A99
Special adapters
–
+
–
SKE
Skeleton model
+
+
–
S01 to
S99
Skeleton, differentiated
–
+
–
Geometry
GEO
Design data, general
+
+
– (+)
G01 to
G99
Design data, differentiated
–
+
– (+)
Joining tech‐
nology
VER
Root joining group
+
–
–
VEG
Joining group
+
–
–
VEE
Fastener/joining part
–
+
–
Output
OUT
Output geometry, general
+
–
– (+)
O01 to
O99
Output geometry, differentiated
–
+
– (+)
DMU
DMU adapter
–
+
– (+)
PCA
Process chain adapter
–
+
– (+)
Drawing
DRW
Part drawing
+
–
+
Assembly
ZSB
Root for assembly
+
–
– (+)
Assem‐
bly com‐
ponents
Z01 to
Z99
Pre-assembly of the ASSY
+
–
– (+)
ASSY in‐
fo part
(ZIN)
Adapter for additional ASSY informa‐
tion (e.g., reference point system
(RPS))
–
+
–


### 第 7 页
Page 7
VW 01059-6: 2015-11
Application
CAD type
Brief description
CATIA V5-6 document
CATProd
uct
CATPart
CATDra
wing
Kinematics
KIN
Kinematics geometry
+
+
–
K01 to
K99
Kinematics geometry, differentiated
–
+
–
R01 to
R99
Simplified geometry representation
–
+
–
Motion
group el‐
ements
M01 to
M99
Motion groups
+
–
–
Legend:
+ Assignment allowed
- Assignment prohibited
(+) reference to the document that is used as a source for the drawing.
Table 2 – Additional CAD types and their use
Application
CAD type
Brief description
CATIA V5-6 document
CATAnalysis
Finite element
method
(FEM)
CAE
Computer-aided engineering (CAE)
document
+
Legend:
+ Assignment allowed
- Assignment prohibited
(+) reference to the document that is used as a source for the drawing.
EXCEPTION upon agreement with the contracting department: If a complete data package (2-D
with 3-D) is stored under the product data type TMG (part model, protected), a combination of
DRW (CAD type) and TZ (product data type, TZ = part drawing) is permissible for the root file
CATDrawing.
NOTE 3: : The part geometry may only be created within the geometry structure. Details are pro‐
vided in the procedure specifications of the appropriate departments and brands.
NOTE 4: : CAD types other than DRW for a CATDrawing are permissible in exceptional cases.
The use of the CAD types marked with (+) indicates which document served as a source for the
drawing. Details are provided in the procedure specifications of the responsible department.
Naming convention of Production
Method planning
The naming convention for method plans is defined by the following guideline
–
39D 22000 [5] – "CAD Guideline for Creating Method Plans in CATIA V5".
3.2.3  
*******  


### 第 8 页
Page 8
VW 01059-6: 2015-11
This guideline is binding for the Volkswagen and Audi brands as well as for external partners con‐
tracted by these brands.
NOTE 5: : The guideline serves as a basis for a consistent layout of the method plan using the
CATIA V5-6 CAD system. It ensures that subsequent departments can use the data on a consis‐
tent basis. For method planning purposes, it supplements this standard and Audi guideline
1D300046 [7].
Production equipment design
The naming convention for the different fields of production equipment design is specific to the re‐
spective departments. The following guidelines must be obtained from the respective purchaser:
Design of facilities and fixtures:
–
1D3119 [2] – Supplementary Guideline for the Design of Facilities and Fixtures Using CAT‐
IA V5 (Audi)
–
39D 22001 [6] – Basic Guideline for the Design of Facilities and Fixtures Using CATIA V5
(CATIA V5 Facilities and Fixtures Design Team of the German Automotive Industry)
–
39D 22002 [1] – Supplementary Guideline for the Design of Facilities and Fixtures Using CAT‐
IA V5
*******  


### 第 9 页
Page 9
VW 01059-6: 2015-11
Press tool design:
–
39D 944 [3] – Basic Guideline for Press Tool Design Using CATIA V5 (CATIA V5 Production
Equipment Design Team of the German Automotive Industry)
–
39D 945 [4] – Supplementary Guideline for Press Tool Design Using CATIA V5 (Audi and
Volkswagen)
Infrastructure
Only the English language version of the CATIA V5-6 software must be used.
The operating system may remain in the respective national language environment.
CATIA V5-6 documents must only be copied and renamed in the CATIA environment with CATIA
functions (e.g., "Save Management,", "New from," "Send To," or "Save As," or comparable applica‐
tions). They must not be copied using Explorer/File Manager, or the copy command at the operat‐
ing system level.
Settings
The current standard environment of the Volkswagen Group must be used.
The following applies to contractors:
As of Group Reference CATIA 4.5.0, development partners must use VWGRCLite.
Source: http://www.vwgroupsupply.com.
If the settings are modified further for specific user groups, (e.g., departments or teams), the re‐
sponsible design partner must be informed.
Storage in HyperKVS
Data may only be exchanged via HyperKVS, the HyperKVS temporary file area, or the ODETTE
file transfer protocol (OFTP) (see HyperKVS CATIA V5 data exchange; notes for contractors).
VALIDAT checks for compliance with the requirements of this standard. Without an "OK" data qual‐
ity check with VALIDAT and a pertinent test profile, persistent storage in certain product data types
is not possible in KVS.
Persistent storage in substructures of the corresponding substructure type must be selected in
KVS and the number (as per the information in the file name) entered.
The (KVS) substructure type "Alternative" must be used for the product data type TM (part model).
The (KVS) substructure type "Blatt" (sheet) must be used for the product data type TZ (part draw‐
ing).
Deviations from this specification, as well as the use of combined (KVS) substructure levels (e.g.,
"Alternative" and "Sheet") require documented methodology from an appropriate department or a
Group organization unit.
3.3  
3.4  
3.5  


### 第 10 页
Page 10
VW 01059-6: 2015-11
Individual vehicle parts
For each individual vehicle part, the DMU adapter must be submitted as per Volkswagen standard
VW 01059-6-3 for each component that is to be released under product data type TM (part model).
However, the DMU adapter alone is usually not sufficient for release.
This obligation does not apply if the PCA adapter corresponds precisely to the description in
VW 01059-6-3.
Vehicle part models that remain in HyperKVS through direct archival or through intellectual proper‐
ty protection (IPP) processes as a CATProduct with (target) product data type TM must contain ex‐
actly one DMU adapter in the product structure and additional output adapters defined in
VW 01059-6 -3, in order to ensure the subsequent process (see figure 2).
Other adapters to be created or information in addition to the elements defined in VW 01059-6-3 is
subject to bilateral agreement.
Additional information on the content and use of output adapters must be taken from standards
VW 01059-6-3 and VW 01059-6-4.
Figure 2 – Example of a KPR structure
The product structure can be expanded according to the design specifications of the responsible
department (Design Engineering Methods). Additional sub-structure levels in the form of CATProd‐
ucts and CATParts may be created.
The links lead from top to bottom (e.g., from the design data to the output data). Cross references
are not permissible.
Drawing documents (CATDrawing)
If derived drawing documents (CATDrawing) are stored in HyperKVS, then the 3-D CAD docu‐
ments (CATPart or CATProduct) on which the CATDrawings are based must also be made availa‐
ble. It must be guaranteed that the documents can be used or reused in the subsequent process
steps.
Currently, providing multiple CATDrawings and the 3-D CAD data on which the CATDrawings are
based in only one TAR archive is only permitted if appropriate links to the CATDrawings are man‐
ually set by the user in advance. For the Audi brand group, only one CATDrawing is permissible
per TAR archive.
For details on drawing documents as such, see section 3.11.
3.5.1  
3.5.2  


### 第 11 页
Page 11
VW 01059-6: 2015-11
Accuracy
When surfaces are created, the preset accuracy is used:
Position deviation
≤ 0.001 mm
Angle deviation (tangent continuity)
≤ 0.5 degrees
"Tolerant modeling" must be applied in such a way that the tolerances for the finished part are met.
For topological quantities (e.g., adding surfaces with the Join function), the following accuracies
are permitted:
Position deviation
≤ 0.01 mm
Angle deviation (tangent continuity)
≤ 0.5 degrees
An accuracy of 0.02 mm for gaps and 1 degree tangent continuity is permitted for mixed data,
CATIA V5-6 data, and data that has been migrated from other formats (CATIA V4, initial graphics
exchange specification (IGES), standard for the exchange of product model data (STEP), etc.).
The use of geometries from other formats is always subject to bilateral agreement.
The values for the accuracy of representation (Tools, Options, General, Display, Performances)
specified and distributed with the Volkswagen reference must be complied with when data are stor‐
ed in HyperKVS:
3-D accuracy
Default = Fixed 0.20
Curves’ accuracy ratio
Default = 1.00 x 3-D accuracy
2-D accuracy
Default = Fixed 0.02
LTA elements
The application "LTA" (German acronym for "hole tool application") facilitates the generation of
function holes in the design.
RPS elements
RPS elements must be structured as per VW 01055. This is facilitated by the application "RPS."
Link management
Only published geometries and parameters are to be linked.
The following specifications apply to documents archived in HyperKVS.
The following links are permitted:
–
Links to Design Tables
–
Instance links to CATPart, CATProduct, cgr files
–
KWE links (knowledge links) to CATPart, CATProduct, CATDrawing, CATProcess,
CATAnalysis
–
Hyperlinks to CATIA V5-6 documents
–
Connector Connection Point (CCP) links to CATParts
–
Import Context links to CATParts/CATProducts
–
View link, Sub Catalog link
–
Components without substructure
3.6  
3.7  
3.8  
3.9  


### 第 12 页
Page 12
VW 01059-6: 2015-11
The following are not permissible:
–
All links to CATIA V4 model files.
EXCEPTION:The use of CATIA V4 models integrated into a CATIA V5-6 product structure
(CATProduct) is permissible only in the designer‘s own project environment. CATProducts ar‐
chived in HyperKVS must not contain any V4 models. Context links to a CATIA V4 model with‐
in a product structure are not permissible. The related links or CATParts must be isolated. The
derivation of V5-6 drawings from CATIA V4 models is only permissible if every View link or the
whole CATDrawing is subsequently isolated.
–
Material link
–
OLE link (drawing, OLE = object linking and embedding)
–
Components with subordinated physical documents or other components
–
Catalog File component links, except in catalogs.
Layer
The use of layers is not permissible. Exceptions are only permissible upon agreement with the ap‐
propriate department. If layers are used, the filter must be set so that all elements are visible when
saving.
Drawing derivation
–
The CATPart or CATProduct must be updated before the drawing is updated.
–
Only (updated) data sets may be saved in HyperKVS.
–
Before the CATDrawing is saved in HyperKVS, the updated Views must be locked.
–
Drawing frames must always be created in the background in 1:1 scale of the related Sheet.
Details (e.g., text macros) are created in a separate detail sheet and instanced in the related
drawing sheet (drawing frames for Design Engineering as per VW 01014 are provided in a
Catalog and per macro).
–
For drawings with supplier protection, see VW 01058.
–
There must not be any external links to catalog elements (Expose 2-D component).
–
Only the CEG1 or CEG2 configurations may be used. Most important differentiating feature:
CEG1 uses 0.35-mm visible part edges while CEG2 uses 0.5-mm visible part edges.
–
The format of each sheet used in the drawing must be chosen so that it is congruent with the
drawing frame used. The relevant drawing content must be arranged so that it is completely
contained within the sheet borders.
–
Bilingual CAD drawings in German and English must be completed with the "DoLittle" pro‐
gram. VW 01058 governs bilingual features of drawings. DoLittle is a tool that facilitates the
creation of multilingual CAD drawings, based on a standardized, Volkswagen-specific text cat‐
alog. The text elements created using DoLittle may only be changed by the Technical Transla‐
tions department, EKDD/5. It is not permitted to modify these text elements in the CAD model
without using DoLittle.
3.10  
3.11  


### 第 13 页
Page 13
VW 01059-6: 2015-11
If CATIA V4 drawings are changed in CATIA V5-6 after CATIA V4 has been shut down, the
changes must be implemented by means of the following procedures:
–
Migration of the 3-D data and creation of a new drawing in CATIA V5-6 or
–
Migration of the drawing. This drawing must be marked with the note "aus CATIA V4 migriert
durch GRICOS / migrated from CATIA V4 by GRICOS" in the field "CAD-System und Verwal‐
tungssystem-Schlüssel" (CAD system and management system code).
The relevant department must decide for the individual case, which workflow proves to be more
efficient.
The exact workflow for the migration of a drawing is specified in "V4 Migration Manual" [8].
Sketcher
Sketches must be completely defined. So-called "Positioned Sketches" must be used to detach
from the standard H and V directions.
Standard vehicle parts are not parameterized, and thus are an exception.
Power Copy and User Defined Feature (UDF)
The use of Power Copies is unrestricted. They are provided by the purchaser, as necessary.
The use of UDF must be agreed upon with the appropriate department. If they are used, all Fea‐
ture Definition Files must be delivered to Volkswagen AG during the data exchange process. Oth‐
erwise, the data are unusable and are rejected.
NOTE 6: : When a UDF is created, the interface definition is stored in the so-called CATGScript.
This is then stored in the "Directory of File Types".
The related CATParts must be completely exported using SendTo/Directory, so as to ensure fur‐
ther use of the data.
Knowledge ware
The use of Rules, Checks, and Formulas is unrestricted. The use of other knowledge ware ele‐
ments must be agreed upon with the appropriate department because of the additional licenses re‐
quired for further use.
Component application architecture (CAA) applications
CAA applications may only be used if the objects/object groups created with them can be pro‐
cessed with Volkswagen CATIA software (see also Volkswagen and Audi download sites,
section 3.4). Only CAA applications containing File Type Libraries1) approved by Volkswagen may
be used.
3.12  
3.13  
3.14  
3.15  
1)
Individual object types created in CAA applications are declared in so-called *.CATfct files. These must exist on computers that are
used to visualize these data or to access them. If this is not the case, the CAD data cannot be used.


### 第 14 页
Page 14
VW 01059-6: 2015-11
Applicable documents
The following documents cited in this standard are necessary to its application.
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
VW 01014
Engineering Drawings; Drawing Frames and Text Macros
VW 01055
Reference Point System (RPS); Specifications in Drawings and 3-D
CAD Models
VW 01058
Drawings; Lettering
VW 01059-6-1
Requirements for CAD/CAM Data – CATIA V5-6 CAD System – Part 1:
Terms
VW 01059-6-2
Requirements for CAD/CAM Data – CATIA V5-6 CAD System – Part 2:
Electric Cable Routing
VW 01059-6-3
Requirements for CAD/CAM Data – CATIA V5-6 CAD System – Part 3:
Process Chain Adapter (PCA), DMU CATPart, and Optional Adapters
VW 01059-6-4
Requirements for CAD/CAM Data – CATIA V5-6 CAD System – Part 4:
Product Structures for Product Data Type TM
Bibliography
[1]
39D 22002 – Supplementary Guideline for the Design of Facilities and Fixtures Using
CATIA V5. Contractors obtain all valid production equipment standards from the Group
Business Platform (http://www.vwgroupsupply.com) under Online-Normentexte/Betrieb‐
smittel (Online Standards Texts/Production Equipment).
[2]
1D3119 – Guideline for Creating Production Equipment without Drawings Using CAT‐
IA V5. Design and work guidelines are available directly from the contracting depart‐
ments via the Group Business Platform (http://www.vwgroupsupply.de).
[3]
39D 944 – Basic Guideline for Press Tool Design Using CATIA V5 (CATIA V5 Produc‐
tion Equipment Design Team of the German Automotive Industry). Contractors obtain all
valid production equipment standards from the Group Business Platform
(http://www.vwgroupsupply.com) under Online-Normentexte/Betriebsmittel (Online
Standards Texts/Production Equipment).
[4]
39D 945 – Supplement to the Basic Guideline for Press Tool Design Using CATIA V5.
Contractors obtain all valid production equipment standards from the Group Business
Platform (http://www.vwgroupsupply.com) under Online-Normentexte/Betriebsmittel (On‐
line Standards Texts/Production Equipment).
[5]
39D 22000 – CAD Guideline on Creating Method Plans in CATIA V5 (Expert Group
Method Planning with CATIA V5). Contractors obtain all valid production equipment
standards from the Group Business Platform (http://www.vwgroupsupply.com) under
Online-Normentexte/Betriebsmittel (Online Standards Texts/Production Equipment).
[6]
39D 22001 – Basic Guideline for the Design of Facilities and Fixtures Using CATIA V5
(CATIA V5 Facilities and Fixtures Design Team of the German Automotive Industry).
4  
5  


### 第 15 页
Page 15
VW 01059-6: 2015-11
Contractors obtain all valid production equipment standards from the Group Business
Platform (http://www.vwgroupsupply.com) under Online-Normentexte/Betriebsmittel (On‐
line Standards Texts/Production Equipment).
[7]
1D300046 – Guideline for Creating Method Plans (Audi). Design and work guidelines
are available directly from the contracting departments via the Group Business Platform
(http://www.vwgroupsupply.de).
[8]
Manual for converting from V4 to V5. This manual can be obtained directly from the
Volkswagen supplier portal at http://www.vwgroupsupply.com. The document can be
found on the Volkswagen Intranet at http://catia.wob.vw.vwg:8080/, section "Quality
Management", line "Interoperability".

