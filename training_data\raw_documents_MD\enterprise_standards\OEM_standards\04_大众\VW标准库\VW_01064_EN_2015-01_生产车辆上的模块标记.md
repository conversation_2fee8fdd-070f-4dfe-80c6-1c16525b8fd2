# VW_01064_EN_2015-01_生产车辆上的模块标记.pdf

## 文档信息
- 标题：
- 作者：
- 页数：28

## 文档内容
### 第 1 页
Group standard
VW 01064
Issue 2015-01
Class. No.:
01152
Descriptors:
marking, component marking, module, modules catalog, build status documentation, traceability,
installation check, serial number, barcode, Code 39, data matrix code, BG-Online, RFID
Module Marking on Production Vehicles
Build Status Documentation – Codes on Mechanical Vehicle Parts
Previous issues
VW 01064: 1996-09, 1999-05, 2001-06, 2001-11, 2002-03, 2002-09, 2003-04, 2003-12, 2007-11,
2008-05, 2010-09, 2011-07, 2013-11
Changes
The following changes have been made to VW 01064: 2013-11:
–
Section 1 "Scope" changed
–
Section 2.2 "Build status documentation (BZD)" expanded
–
Section 3.1 "General requirements" expanded
–
Section 3.3 "Data acquisition" changed
–
Section 4 "Build status documentation marking with 1-D code (barcode, Code 39) and 2-D
code (data matrix code)": 2nd paragraph added
–
Section 4.1.3 "Serial number": 1st paragraph changed, 3rd paragraph expanded
–
Section 4.2 "Implementation examples", Figure 5 changed
–
Section 5.1 "Data sequence of 2-D code for part installation checks and build status documen‐
tation": Note 5 added
–
Section 5.2.1 "Standard data content for part installation checks with build status documenta‐
tion": 1st paragraph corrected, rectangular DMC added and description changed, Figure 11
changed
–
Section 5.2.2 "Minimum data content for part installation checks": 1st paragraph expanded,
rectangular DMC added and description changed
–
Section 5.2.3 "Minimum data content for build status documentation": 1st paragraph expan‐
ded, rectangular DMC added and description changed, Figure 13 changed, note 9 changed
–
Section 5.2.4 "Maximum data content for part installation checks with build status documenta‐
tion": Rectangular DMC added and description changed, Figure 14 corrected
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Page 1 of 28
Technical responsibility
The Standards department
K-GQZ/I
Armin Witschi
Tel.: +49 5361 9 18168
EKDV/4 Uwe Stüber
EKDV
Tel.: +49 5361 9-29063
Maik Gummert
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2014-06a-patch5
QUELLE: NOLIS


### 第 2 页
Page 2
VW 01064: 2015-01
–
Section 6.2.1 "When used in plants that build vehicles" added, ISO/IEC TR 29158 instead of
AIM DPM, overall quality changed from "B" to "3"
–
New section 6.2.2 "When used in plants that build engines" added
–
Section 6.3 "Plain text information (1-D and 2-D codes)" changed
–
Section 9 "Applicable documents" updated
Contents
Page
Scope ......................................................................................................................... 3
General notes, definitions .......................................................................................... 3
Traceability ................................................................................................................. 3
Build status documentation (BZD) ............................................................................. 4
Module ....................................................................................................................... 4
Module data ............................................................................................................... 4
Part identification (installation check) ......................................................................... 5
Vehicle component ..................................................................................................... 5
Complex assembly ..................................................................................................... 5
General requirements for marking and technical requirements for data
processing .................................................................................................................. 5
General requirements ................................................................................................ 5
Marking types ............................................................................................................. 6
Data acquisition .......................................................................................................... 6
Standard configuration ............................................................................................... 7
Technical requirements for data processing using 1-D and 2-D codes ..................... 7
Modulo 43 check digit calculation .............................................................................. 8
Build status documentation marking with 1-D code (barcode, Code 39) and 2-D
code (data matrix code) ............................................................................................. 8
Build status documentation 1-D and 2-D code data sequence (module data) ........... 8
Implementation examples ........................................................................................ 10
Marking for part installation checks with build status documentation ....................... 11
Data sequence of 2-D code for part installation checks and build status
documentation .......................................................................................................... 11
2-D code examples .................................................................................................. 13
Label design and layout (1-D code and 2-D code) ................................................... 16
Code symbol for barcode (1-D code) ....................................................................... 16
Code symbol for matrix code (2-D code) ................................................................. 16
Plain text information (1-D and 2-D codes) .............................................................. 17
One-part label permanently attached to the component (1-D and 2-D codes) ........ 18
Labeling outside on the ASSY (group marking) ....................................................... 18
ASSY transfer ticket (group marking) ....................................................................... 19
Marking applied directly in the material (direct part mark (DPM)) ............................ 19
Verifications .............................................................................................................. 19
Group marking – Pre-recording data for complex assemblies ................................. 20
Build status documentation group marking .............................................................. 20
Part installation check group marking ...................................................................... 20
Data exchange ......................................................................................................... 21
Build status documentation marking and part identification with using RFID tags ... 21
Technical implementation ........................................................................................ 21
Examples of user memory data content ................................................................... 22
Applicable documents .............................................................................................. 25
1
2
2.1
2.2
2.3
2.4
2.5
2.6
2.7
3
3.1
3.2
3.3
3.4
3.5
3.6
4
4.1
4.2
5
5.1
5.2
6
6.1
6.2
6.3
6.4
6.5
6.6
6.7
6.8
7
7.1
7.2
7.3
8
8.1
8.2
9


### 第 3 页
Page 3
VW 01064: 2015-01
Bibliography ............................................................................................................. 25
.................................................................................................................................. 26
10
Appendix A
Scope
This standard describes the requirements for the exterior marking of vehicle components that are
subject to mandatory build status documentation (BZD) and are being used for production vehicles
(vehicles with a general operating license (ABE)), as well as for parts within an ASSY that are sub‐
ject to mandatory build status documentation (e.g., engines). The data coded in the marking is
used for documenting and tracing vehicle components ("build status documentation") and for iden‐
tifying parts ("installation check") within the Volkswagen Group.
Markings as per this standard do not substitute part markings as per Volkswagen standard
VW 10500.
This standard is intended for:
–
Developers in charge of specifying a marking subject to mandatory build status documentation
–
Quality assurance personnel in charge of sampling a marking, on the component, that is sub‐
ject to mandatory build status documentation
–
Vehicle part suppliers in charge of adding a marking subject to mandatory build status docu‐
mentation
–
Suppliers of complex assemblies (ASSYs) in charge of pre-recording module data if necessary
–
Production planning personnel in charge of establishing the procedure for recording data
Volkswagen AG build status documentation applies to the following Volkswagen Group brands:
Volkswagen, Audi, Skoda, SEAT, Volkswagen Commercial Vehicles, Bentley, Lamborghini.
NOTE 1: This marking does not apply to components that can be diagnosed. The markings for
electronic control units are described in workshop sketch WSK.013.290 E.
NOTE 2: The "build status documentation marking and part identification with RFID tags" descri‐
bed in this standard applies only to "part installation checks" and/or "build status documentation"
for production components. VW 01067 describes how prototype components are marked.
General notes, definitions
Traceability
Traceability means the ability to determine when, where, and by whom a product or marketable
good was obtained, produced, processed, stored, transported, used, or disposed of. This route and
process tracking is also called "tracing." A distinction is made between downstream tracing (from
the producer to the consumer) and upstream tracing (from the consumer to the producer).
1  
2  
2.1  


### 第 4 页
Page 4
VW 01064: 2015-01
Build status documentation (BZD)
Within the Volkswagen Group, build status documentation is subdivided into two main categories:
–
Individual build status documentation
–
Batch build status documentation (build status documentation for each container)
In the case of individual build status documentation, a marking must be added to each individual
component. This standard applies to said marking.
In contrast, batch build status documentation does not require for individual components to be
marked. Instead, it is based on the use of markings used in logistics processes and systems. Ac‐
cordingly, this standard does not apply to batch build status documentation.
The data coded in the marking is used to document and trace vehicle components ("build status
documentation") within Volkswagen AG. Build status documentation is used to establish a clear
link that makes it possible to identify the correspondence between a vehicle identification number
and certain characteristics of a component. These characteristics include manufacturer and serial
numbers, part numbers, and electronic control unit hardware and software versions. This makes it
possible, in the event of a warranty claim (recall), to precisely determine which vehicle identification
numbers are involved.
Figure 1 shows the three principal procedures, e.g., in the event of a recall:
Figure 1 – Basic recall procedure
Module
A vehicle component or assembly that is subject to marking as per this Group standard. Each mod‐
ule is identified by means of a unique module number.
Module data is defined in the "BG-Online" system. Instructions on mandatory marking can be
found in engineering drawings and TLD (Technical Guideline for Documentation) sheets.
Module data
Module data is the portion of the build status documentation marking data that is documented for
traceability purposes.
The following build status documentation data ("module data") is required in order to be able to
uniquely identify vehicle components:
Module number → Type of vehicle component
Manufacturer's code → Manufacturer and, if applicable, manufacturing location
2.2  
2.3  
2.4  


### 第 5 页
Page 5
VW 01064: 2015-01
Serial number → Vehicle component
Check digit → Modulo 43 algorithm
This data subject to mandatory build status documentation is used as a reference to specific vehi‐
cle components, is recorded in a vehicle-specific manner during production, and is stored in the
Volkswagen AG long-term vehicle archive. This makes it possible, in the event of a defect, to pre‐
cisely determine which vehicles are involved.
The syntax for the module data is described in the BG-Online Group Modules Catalog (contact via:
<EMAIL>).
Part identification (installation check)
In addition to the aforementioned module data, a marking may include additional information on the
vehicle component:
–
Group part number (VW 01098)
–
DUNS number (Data Universal Numbering System)
–
Date of manufacture
–
Part version
This data is not intended for archiving purposes. Instead, it is meant for use during the ongoing
manufacturing process, if necessary, in order to verify that a vehicle component has been installed
correctly (technical design, age, manufacturer).
Vehicle component
Vehicle component or assembly with a part number that references the exact corresponding tech‐
nical design. If applicable, the part number is the basis for an installation check.
Complex assembly
An assembly that contains multiple modules. For example, the seat ASSY, which includes the side
airbag, backrest trim cover airbag, sensor mat, and seat belt buckle with sensor. If the modules'
individual data will no longer be accessible in the finished ASSY, the data must be pre-recorded
(see section 7).
General requirements for marking and technical requirements for data processing
General requirements
The marking is a release-relevant component property and must be taken into account during the
sample inspection process. The following must be checked:
–
That the data can be recorded under production conditions
–
The contents of the applied data sequence
–
The type of marking on the component and its application
–
Retention period ≥15 years
The marking is checked during the sample inspection process. For this purpose, the supplier/man‐
ufacturer provides components that are marked the same way as they are during production.
2.5  
2.6  
2.7  
3  
3.1  


### 第 6 页
Page 6
VW 01064: 2015-01
The supplier/manufacturer ensures that the marking requirements described here are met during
ongoing production. This can be ensured with random sampling tests, for example.
The retention period is at least 15 years from the moment the data is created (equivalent to Classi‐
fication System for Documents (CSD) class 7.2) The supplier/manufacturer must ensure that the
data set remains unique for the entire retention period (≥15 years). It must document important,
quality-relevant individual information concerning the marked vehicle component (e.g., batch of raw
materials used, manufacturers of purchase parts used, testing and setting values, manufacturing
location and system), establish a clear link that makes it possible to identify the correspondence
between this information and the corresponding reference data, and archive the information. If nec‐
essary, this will then make it possible to obtain clear information concerning functional, manufactur‐
ing, and material qualities.
If the supplier manufactures an ASSY containing a component subject to mandatory build status
documentation (e.g., fuel pump inside the fuel tank ASSY), the supplier must record the data for
the component subject to mandatory build status documentation, add the data to the ASSY's data,
and retain this data in its documentation for at least 15 years. This also applies to ASSYs made up
of an electronic control unit and a mechanical component (e.g., headlamp with control module).
If the marking requirements are not met, the components in question will be deemed defective and,
if applicable, must not be installed.
Marking types
The goal is to record data quickly and in a cost-effective manner during the manufacturing process.
There are three marking types that can be used. They are as follows:
– 1-D code (barcode) → Code 39 is used; see section 4
– 2-D code (matrix code) → Data matrix code is used; see section 4
– RFID tag → See section 8
Using RFID tag markings is advisable if partially or fully automated data recording is required,
marking with labels is problematic, or vehicle components are already equipped with an RFID tag.
NOTE 3: The use of RFID tag markings must always be agreed upon with the relevant manufac‐
turing plants in terms of both technical and organizational requirements.
Data acquisition
Data is recorded during the vehicle assembly process or at the relevant pre-assembly areas. A dis‐
tinction must be drawn between the following data acquisition procedures:
Direct entry:
The data is entered immediately at the vehicle part's installation location. A
one-part label permanently attached to the component, or a direct marking,
is read directly by means of a scanner.
Transfer ticket:
The data is acquired at the vehicle part's installation location. Out of a label
with two sections, the detachable and self-adhesive section is affixed to a
designated field on the transfer ticket. The data is entered by using a scan‐
ner to read the transfer ticket at downstream sequence points.
Scanning:
The data is acquired and entered directly at the vehicle part's installation lo‐
cation or at sequence points that follow soon after by reading the RFID
tags.
3.2  
3.3  


### 第 7 页
Page 7
VW 01064: 2015-01
Standard configuration
The standard configuration for marking and data recording is as follows:
–
Marking with 2-D code on a label
–
Data recorded at installation location
In comparison to the 1-D code, the 2-D code has additional potential application options (installa‐
tion check); high reading accuracy; requires less space; and requires less label material – or none
in the case of a direct marking.
In justified cases, deviations from this standard configuration are permissible within the aforemen‐
tioned options. Combined markings are possible if required due to the technical conditions involved
in acquiring the data.
Technical requirements for data processing using 1-D and 2-D codes
Only the ASCII characters listed in figure 2 are permissible for the data sequences described be‐
low:
Legend
-
Excluded
(1)
Unrestricted
(2)
For check digit only
(3)
The "#" character is placed between the first five data fields in a data sequence (sepa‐
rator). Separators must always be used, even if there are no characters in the field.
(4)
The "&" character links two data sequences (continuation character, for group markings
only).
(5)
The "*" character is placed at the beginning and end of the module data in the data se‐
quence. If there is no module data, these special characters are not used either.
(6)
The "=" character terminates the data sequence (terminator).
Figure 2
When designing data acquisition systems, care must be taken to ensure that the technical interface
and the configuration of the reading device (stationary scanner, hand-held scanner) do not lead to
3.4  
3.5  


### 第 8 页
Page 8
VW 01064: 2015-01
character errors. Problem example: English keyboard ("Y" and "Z" characters swapped when
compared to a German keyboard layout).
Modulo 43 check digit calculation
The check digit is used to verify manual entries based on the plain text information in the marking.
43 different characters are defined for the single-character check digit (see figure 2 for the ASCII
characters corresponding to checksum values 00–42).
The value of the check digit is calculated based on the data sequence characters, excluding the
check digit. The calculation method is based on the assignment table (see figure 2).
The check digit is calculated for the module data only (see section 4.1). The data fields for the part
installation check (see section 5.1) are not used to calculate the check digit.
Calculating the check digit:
1) Using the assignment table, determine the checksum value for each character in the data se‐
quence
2) Calculate the total of all checksum values
3) Divide this total by 43
4) Use the division remainder and the assignment table to determine what the check digit is
Example:
Data sequence without check digit
065 KTO1234567
Total
0+6+5+38+20+29+24+1+2+3+4+5+6+7 = 150
Division
150 : 43 = 3 remainder 21
Check digit
21 ≙ L (see figure 2)
→ Data sequence
*065  KTO1234567L*
NOTE 4: The check digit is part of the data sequence and must not be confused with a check
digit in the code symbol.
Build status documentation marking with 1-D code (barcode, Code 39) and 2-D code
(data matrix code)
Build status documentation markings can use either a 1-D code or a 2-D code. In both cases, the
data sequences described below apply.
If the component needs to be marked for the part installation check and for build status documen‐
tation purposes, a 2-D code must be used.
The build status documentation marking does not replace the part marking in VW 10540-1 and
VW 10540-3. If applicable, both markings must be applied on the component independently from
each other.
Build status documentation 1-D and 2-D code data sequence (module data)
The data sequence for the barcode and matrix code contains four data fields and, in the standard
version, fifteen characters (exceptions include module no. 005 for engines and module no. 006 for
transmissions, which have 21 characters), see figure 3:
3.6  
4  
4.1  


### 第 9 页
Page 9
VW 01064: 2015-01
Figure 3
The data sequence described here is just an example. The structure of each individual module
must be taken from the "BG-Online" system.
Only characters 0 to 9 and A to Z are permissible for the "Module number" and "Serial number"
data fields. Special characters as per figure 2 are also permissible for the check digit.
Module number
The module number is used to identify the type of vehicle component. It is three characters long
and can be alphanumeric or numeric. Example: 671 = front passenger side airbag module.
Manufacturer’s code
The manufacturer's code is an in-house code used by Volkswagen AG to distinguish between dif‐
ferent manufacturers and their manufacturing locations. For a definition, see VW 10540-1. The field
is four characters long. In the data sequence, the alphabetical or alphanumeric three-character
manufacturer's codes must be right-aligned and begin with a blank character.
Example: Manufacturer: "ZFS" → Syntax in data sequence: "ZFS".
Serial number
The serial number is used to uniquely identify a manufacturer's vehicle components or assemblies
when they are identical in the sense of modules. The serial number is always alphanumeric (per‐
missible characters; 0 to 9, A to Z). The serial number's length for each individual component is
specified in the Group Modules Catalog.
Instead of individual parts, defined production batches can also be marked and distinguished from
each other. In this case, the serial number will correspond to a batch number.
The serial number sequence does not end when the part number is changed or depending on the
vehicle component's usage. In other words, the serial number must be independent from the part
number.
Example:
Fuel tank = module "065"
Supplier code = "KTO", written with blank character "KTO"
→ Data sequence in plain text: *065 KTOxxxxxxxP*
The module data must remain unique for the entire retention period (≥15 years) and for all fuel
tanks from the "KTO" supplier that are supplied to Volkswagen AG brands. In this context, the cor‐
responding technical design and usage are not relevant.
Unless otherwise specified, and provided this requirement is met, the supplier can choose the
composition for their serial numbers. Number ranges with different sizes will result depending on
how the number is counted (see figure 4).
4.1.1  
4.1.2  
4.1.3  


### 第 10 页
Page 10
VW 01064: 2015-01
Figure 4 – Examples of counting methods
The alphanumeric counting method corresponds to a 36-number system using numbers and capi‐
tal letters. Value comparison for decimal and alphanumeric counting methods:
(1) an = (1) dec, .., (9) an = (9) dec, (A) an = (10) dec, .., (Z) an = (35) dec, (10) an = (36) dec, ..,
(ZZ) an = (1295) dec
If smaller number ranges are used, the remaining free spaces in the serial numbers can be filled
out with useful codes as defined by the supplier (e.g., code for manufacturing system, part code).
The counting method chosen must safely exceed the anticipated total number of modules. The rec‐
ommended counting method is: alphanumeric, 7 characters.
Modulo 43 check digit calculation
See section 3.6.
Implementation examples
The standard data sequence for the module data consists of 15 characters; see figure 5. The exact
syntax must always be gathered from the "BG-Online" Group Modules Catalog. Information con‐
cerning data sequences can be requested from the Group Build Status Documentation (BZD) Of‐
fice (<EMAIL>).
Figure 5
4.1.4  
4.2  


### 第 11 页
Page 11
VW 01064: 2015-01
Marking for part installation checks with build status documentation
A 2-D code must be used for the marking for part installation checks with build status documenta‐
tion.
Implementation examples are described at the end of this section.
Data sequence of 2-D code for part installation checks and build status documentation
The data sequence for the matrix code consists of six data fields; see figure 6.
Figure 6
The order of the individual data fields must be strictly adhered to. Attempting to specify absolute
positions within the overall string would make no sense, as certain data fields may be optional
and/or have variable lengths depending on the use case in question.
NOTE 5: The elimination of the usage code in issue 2012-06 of this standard must only be taken
into account for components yet to be introduced.
Part number
The part number is a composite code containing identification and classification information. Its ba‐
sic structure consists of a nine to eleven-character code.
In the 2-D code for part installation checks and build status documentation, the 11-character part
number, followed by the three-character color code, is always used. The 14-character data field is
written without separating periods or blank spaces, left-aligned. Unused characters are filled out
with blank spaces; see figure 7.
Figure 7
NOTE 6: VW 01098 describes the part number structure used by Volkswagen AG.
Part code
The "Part code" data field is optional. If the use case in question does not require this information,
this information is omitted without being replaced and the data field has a length of 0. Separators
(#) must always be used, even if the field is blank.
The part code contains context-specific information on the vehicle component. Various formats and
definitions are permissible for the structure of this data field depending on the corresponding vehi‐
cle component category. For examples, see figure 8.
5  
5.1  
5.1.1  
5.1.2  


### 第 12 页
Page 12
VW 01064: 2015-01
Figure 8
The contents of this data field must be specified by the appropriate developer and documented in
the part drawing.
DUNS number
The "DUNS number" data field is optional. If the use case in question does not require this informa‐
tion, the DUNS number is omitted without being replaced and the data field has a length of 0. Sep‐
arators (#) must always be used, even if the field is blank.
The DUNS number is an internationally standardized supplier number. The data field has 9 charac‐
ters; see figure 9.
Figure 9
NOTE 7: The use of Volkswagen supplier numbers (KRIAS system) or other code numbers is im‐
permissible.
Date of manufacture
The "Date of manufacture" data field is optional. If the use case in question does not require this
information, the date of manufacture is omitted without being replaced and the data field has a
length of 0. Separators (#) must always be used, even if the field is blank.
The date of manufacture indicates the time at which the component was technically completed
(ready for installation or ready for delivery).
The "Date of manufacture" data field always has 6 digits and the DDMMYY (DayDayMonthMon‐
thYearYear) format; see figure 10. If the information for the specific date is not available, the first
workday of the week must always be entered.
Figure 10
Module data
If the marking is only intended for part identification purposes, the module data can be omitted
without substitution. The characters "* " are also omitted in this case.
Section 4.1 describes the module data's syntax.
5.1.3  
5.1.4  
5.1.5  


### 第 13 页
Page 13
VW 01064: 2015-01
NOTE 8: The check digit is calculated exclusively for the module data. The 2-D code's remaining
data fields are not used to calculate the check digit.
Additional information
Freely available to developers and suppliers.
Special characters in the data sequence
The "#" character is placed between the first four data fields of a data sequence (separator). Sepa‐
rators must always be used, even if there are no characters in the field.
The "*" character is placed at the beginning and end of the module data in the data sequence. If
there is no module data, these special characters are not used either.
The "=" character terminates the data sequence for build status documentation and installation
checks (terminator).
2-D code examples
Standard data content for part installation checks with build status documentation
The standard data for part installation checks with build status documentation contains the "Part
number with color code" and "Module data" data fields (with a standard 15-character syntax in the
example). The "Part code," DUNS number," and "Date of manufacture" data fields are blank.
Deviations from this standard data content (see figure 11) are only permissible in justified cases.
Every deviation must be discussed and agreed upon with all parties involved in the process.
Figure 11
The following code symbols result when using the conditions in section 6.2; see table 1.
Table 1
Type
Square code symbol
Rectangular code symbol
Code symbol
Size in dots
22 × 22
16 × 36
Size in mm
without quiet zone
11.22 × 11.22
8.16 × 24.48
Size in mm
with quiet zone
15.22 × 15.22
12.16 × 28.48
5.1.6  
5.1.7  
5.2  
5.2.1  


### 第 14 页
Page 14
VW 01064: 2015-01
Minimum data content for part installation checks
The standard data content may be deviated from if the component geometry does not allow for the
standard data content. This deviation from the standard data content must be discussed and
agreed upon with all parties involved in the process.
The minimum data content for part installation checks without build status documentation contains
the "Part number" data field, with 14 characters, only. The "Part code," "DUNS number," and "Date
of manufacture" data fields do not have any content, and the "Module data" data field is omitted;
see figure 12.
Figure 12
The following code symbols result when using the conditions in section 6.2; see table 2.
Table 2
Type
Square code symbol
Rectangular code symbol
Code symbol
Size in dots
18 × 18
12 × 36
Size in mm
without quiet zone
9.18 × 9.18
6.21 × 13.26
Size in mm
with quiet zone
13.18 × 13.18
10.21 × 17.26
Minimum data content for build status documentation
If the component geometry makes it absolutely necessary, the standard data content may be devi‐
ated from for the "build status documentation only" use case. This deviation from the standard data
content must be discussed and agreed upon with all parties involved in the process. In this case,
the build status documentation data sequence described in section 4.1 is written as a data matrix
code; see figure 13. The read result for this 2-D code is exactly the same as that for Code 39.
Figure 13
The following code symbols result when using the conditions in section 6.2; see table 3.
5.2.2  
5.2.3  


### 第 15 页
Page 15
VW 01064: 2015-01
Table 3
Type
Square code symbol
Rectangular code symbol
Code symbol
Size in dots
16 × 16
12 × 26
Size in mm
without quiet zone
8.16 × 8.16
6.21 × 13.26
Size in mm
with quiet zone
12.16 × 12.16
10.21 × 17.26
NOTE 9: The module data's syntax is specified in the Group Modules Catalog. The module data
has a four-character code (manufacturer's code) used to identify the manufacturer. The manufac‐
turer's code as per VW 10540-1 has three characters and must be used with a leading blank char‐
acter. Contact via: <EMAIL>
Maximum data content for part installation checks with build status documentation
Depending on the specific use case, the data content can be expanded all the way to the maxi‐
mum data content limit. In this case, all data fields, incl. additional data, are filled out. The length of
the "Part code" and "Additional data" data fields will depend on the specific use case; see
figure 14.
Figure 14
The size of the code symbol will depend on the specific data content. Using figure 14 above, which
has 65 characters, results in the following code symbols when using the conditions in section 6.2;
see table 4
Table 4
Type
Square code symbol
Rectangular code symbol
Code symbol
Size in dots
32 × 32
16 × 48
Size in mm
without quiet zone
16.32 × 16.32
8.16 × 24.48
Size in mm
with quiet zone
20.32 × 20.32
12.16 × 28.48
5.2.4  


### 第 16 页
Page 16
VW 01064: 2015-01
Label design and layout (1-D code and 2-D code)
Code symbol for barcode (1-D code)
The following requirements must be taken into consideration when generating the 1-D code sym‐
bol:
–
Code 39 as per ISO/IEC 16388 must be used
–
The overall symbol quality as per DIN EN ISO/IEC 15416 must be 3.5 or better
–
Module width x (see appendix A): Approx. 0.254 mm
–
Module width ratio: At least 1:2.5
–
Gap width ratio: Same as module width ratio
–
Print resolution: At least 300 dpi
–
Quiet zone: Approx. 3 mm per side
–
Bar height: Approx. 10 mm
–
Check digit calculation (automatic) does not apply
–
These requirements yield a symbol size of approx. 63 x 10 mm for a data sequence with
15 characters.
–
Deviations must be discussed and agreed upon with all parties involved
Code symbol for matrix code (2-D code)
The following requirements must be taken into consideration when generating the 2-D code sym‐
bol:
When used in plants that build vehicles
–
A data matrix code must be used
–
The overall symbol quality as per ISO/IEC TR 29158 (formerly AIM DPM-1 2006) must be 3 or
better. This symbol quality must be ensured for the entire process chain all the way to the in‐
stallation location
–
ECC 200 error correction
–
Module size x (see appendix A): At least 0.50 mm
–
Printer resolution: 300 dpi or higher
–
The quiet zone has a size of at least 2 mm per side; a small quiet zone is permissible only in
agreement with all parties involved.
–
Matrix size and character created with automatic function
–
These requirements result in a symbol size of approx. 20 mm x 20 mm for a completely filled-
out data sequence. Larger symbols will result for group markings
–
Deviations must be discussed and agreed upon with all parties involved
6  
6.1  
6.2  
6.2.1  


### 第 17 页
Page 17
VW 01064: 2015-01
When used in plants that build engines
–
A data matrix code must be used
–
The overall symbol quality as per ISO/IEC TR 29158 (formerly AIM DPM-2006) must be 3 or
better. This symbol quality must be ensured for the entire process chain all the way to the in‐
stallation location
–
ECC 200 error correction
–
Module size x (see appendix A): At least 0.50 mm
–
Printer resolution: 300 dpi or higher
–
The quiet zone has a size of at least four times the module size
–
Matrix size and character created with automatic function
–
These requirements, together with a size of 20 x 20 dots, result in a symbol size of approx.
10 mm x 10 mm for a standard string with 32 to 36 characters.
–
When identical components (across different suppliers) are involved, the exact same marking
method, size, and position must be chosen
–
The position must ensure that the code will always remain legible after the component is in‐
stalled
–
The data matrix code's (DMC's) manufacturer must provide documentation verifying that the
legibility quality requirement is being met
–
Deviations, e.g., resulting from a lack of space, must be expressly noted and must be dis‐
cussed and agreed upon with all parties involved
Plain text information (1-D and 2-D codes)
In addition to the code symbol, the marking must include plain text information. This information is
used for manual data entry if the code symbol is defective and illegible or in the event of system
malfunctions. The following requirements must be met:
–
1-D code: The plain text must show the entire data sequence, including the check digit
–
2-D code: If the 2-D code includes the "Module data" or "Part number" data field, they must be
shown as plain text without fail. Optionally, all data fields can be shown as plain text.
–
The "*" character is added at the beginning and end of the plain text data for the "Module data"
data field.
–
In order to make it easier to read, the part number plain text information must have a blank
space between the front number, middle group, end number, and suffix.
–
If possible, the font must be "Thesis TheSans". If not possible, "Arial" must be used
–
The font height must be at least 2 mm
–
Preferably, the plain text appears centered below the barcode (plain text may be positioned
differently in exceptional cases)
–
To distinguish between the number "0" and the letter "O," the zero must have a line through it
(e.g., ClearType font "Consolas"; ASCII value 048)
6.2.2  
6.3  


### 第 18 页
Page 18
VW 01064: 2015-01
One-part label permanently attached to the component (1-D and 2-D codes)
The following requirements must be taken into consideration for the label type and layout:
–
The label must be self-adhesive and must not become accidentally detached after being af‐
fixed
–
The printed image may be inverted if this is absolutely required (styling specification)
–
Print smear is not permissible
–
Substrate: Metalized film with chalk coating for thermal transfer printer; RAL 9010 (pure white
– including barcode field); alternatively, silver gray
–
Font: Jet black
The label must be applied to the vehicle component in such a way that the data will be visible and
technically legible after the component is installed. The position and orientation must be specified
in an engineering drawing.
Taking existing labels initially intended for other purposes and using them for the marking descri‐
bed here is permissible. The existing contents of these labels must not have a negative impact on
the usability of the marking described here (e.g., due to other similar code symbols).
Two-part or multiple-part labels (1-D and 2-D codes)
The one-part label permanently attached to the component must be supplemented with one or
more additional, detachable label portions. These are detached during the manufacturing process
and, for example, affixed to the transfer ticket. At least the last detachable part to be used contains
the code symbol. All other parts at least contain the plain text information. The requirements speci‐
fied above for the one-part label apply accordingly.
Detachable label portions must not be excessively wide or tall, as, for example, they must not be
larger than the fields on the transfer ticket reserved for affixing these portions. The label size to be
used is based on the size of the code symbol, including the quiet zone and plain text information.
Recommended sizes:
–
1-D code: 15-character data sequence (standard data sequence): Approx. 80 mm × 20 mm
–
1-D code: 21-character data sequence (modules 005 = engine, 006 = transmission): Approx.
100 mm × 20 mm
–
2-D code: The recommended size for a matrix code label is 20 mm × 30 mm
The labels' material and positioning must ensure that it is possible to quickly and easily detach the
appropriate portions without damaging them. The detachable portion must not become detached
unintentionally.
Labeling outside on the ASSY (group marking)
Vehicle components that will later be inaccessible require a multi-part label:
–
2 parts if the data is recorded with direct entry at the vehicle plant
–
3 parts if the data is recorded with a transfer at in the vehicle plant
During the manufacturing process for the complex ASSY, the detachable portion is then perma‐
nently affixed to the outside of the ASSY at an agreed location (e.g., seat: The seat cushion fra‐
me's outer panel).
6.4  
6.4.1  
6.5  


### 第 19 页
Page 19
VW 01064: 2015-01
ASSY transfer ticket (group marking)
The supplier provides an ASSY transfer ticket if labeling on the outside of the ASSY is not possi‐
ble. It must be supplied with the complex ASSY and read in downstream manufacturing stages.
The following content must be provided:
–
Supplier name and ASSY designation
–
In the case of vehicle-specific deliveries: Vehicle code number or vehicle identification number
–
If delivery is not vehicle-specific: Assignment number of ASSY to ASSY transfer ticket
–
All barcodes of modules installed in the ASSY and inaccessible as a result
–
Acceptance notice (stamp, etc.) of the supplier attesting to the correctness of the data provi‐
ded
The ASSY transfer ticket is thus part of the vehicle transfer ticket. It must be affixed to the ASSY in
such a way that it will not come off or be damaged during transportation. Its content, layout, and
processes must be agreed upon with the appropriate vehicle plant.
Marking applied directly in the material (direct part mark (DPM))1)
2-D codes can be inscribed directly in or on the component material if so required by technical or
other requirements (e.g., service life marking, styling requirements). The requirements for the plain
text information must also be considered and met accordingly.
Available marking techniques:
–
Laser marking
–
Micro-percussion
–
Electrochemical etching
–
Inkjet printing
The use of each one of these techniques is suitable for certain applications, depending on the
parts' life expectancy and material mix or production volume and wear caused by environmental
influences.
Verifications
Verifying barcodes
Barcodes must be verified as per DIN EN ISO/IEC 15416. The overall symbol classification as per
DIN EN ISO/IEC 15416 must have a value of 3.5 or better.
Verifying 2-D codes
ISO/IEC TR 29158 (corresponds to AIM DPM-2006) must be applied when verifying codes with
square cells. ISO/IEC TR 29158 refers to direct-marking 2-D codes; the specifications must be ap‐
plied to 2-D codes on labels as applicable. The overall symbol classification must be at least class
"B".
NOTE 10: The use of direct markings requires appropriate readers (DPM scanners). These mark‐
ings must be discussed and agreed upon with all departments involved (from Acquisition on the
assembly line to After-Sales Service – this is to ensure that the codes can be read wherever nec‐
essary anywhere in the Group).
6.6  
6.7  
6.8  
6.8.1  
6.8.2  
1)
Section 6.7 source: "Reading Direct Markings – 10 Important Aspects" by Cognex and "New Standards Allow for Reliable Verifica‐
tion of 2-D Data Matrix Codes" – white paper by Carl W. Gerst III, Cognex Corporation, Senior Director & Business Unit Manager, ID
Products.


### 第 20 页
Page 20
VW 01064: 2015-01
Group marking – Pre-recording data for complex assemblies
In the case of complex ASSYs, the data marked on the individual components will no longer be
accessible in certain cases (e.g., airbag module in seat). In these cases, the data must be pre-re‐
corded while the ASSY is being made in order to ensure that the data will be available at the instal‐
lation location for the ASSY.
Group markings can only be implemented using matrix code.
Build status documentation group marking
The supplier pre-records the data by reading the individual module data sets (see section 4.1).
These individual data sequences are combined to form a group data sequence (see figure 15).
Figure 15
Special characters in the data sequence
The "&" character links two data sequences (continuation character).
The "=" character terminates the data sequence for build status documentation (terminator).
Part installation check group marking
The supplier pre-records the data by reading the individual data sets (see section 5.1). These indi‐
vidual data sequences are combined to form a group data sequence (see figure 16).
Figure 16
For this group data sequence, a new marking is created and then permanently affixed to the out‐
side of the complex ASSY.
This kind of pre-recording is only permissible if none of the modules in the complex ASSY will be
separated from the ASSY in downstream processes. Changes (e.g., replacing a module) cannot be
integrated into the group marking after the fact.
The requirements specified for individual labels apply accordingly. The marking must meet the fol‐
lowing requirements:
–
Plain text information is required for every module in a group marking
–
"&" is used as separator between two data sets
–
The last data set contains terminator "="
7  
7.1  
7.1.1  
7.2  


### 第 21 页
Page 21
VW 01064: 2015-01
A square data matrix code can consist of a maximum of 144 rows x 144 columns. Accordingly,
1 982 ASCII characters can be represented. This results in a maximum of 23 data sets that can be
grouped together. This number is limited to 20 due to usability reasons.
Special characters in the data sequence
The "#" character is placed between the first four data fields of a data sequence (separator). Sepa‐
rators must always be used, even if there are no characters in the field.
The "*" character is placed at the beginning and end of the module data in the data sequence. If
there is no module data, these special characters are not used either.
The "&" character links two data sequences.
The "=" character terminates the data sequence for the installation check (terminator).
Data exchange
The supplier's manufacturing area is treated like an in-house upstream manufacturing process.
The supplier records the data for all modules contained in complex ASSYs and submits this data to
the appropriate vehicle plant.
This pre-recording is only permissible if it impossible for the data to be assigned to the wrong com‐
ponent or vehicle in downstream processes or if there are appropriate emergency procedures that
can be used to correct the data.
Data formats and the entire technical implementation are subject to bilateral agreement.
Build status documentation marking and part identification with using RFID tags
This standard describes the data structures for RFIDs for production component part installation
checks and/or build status documentation. VW 01067 describes the marking for prototype compo‐
nents. Each of these two use cases has been prepared with the other in mind.
Vehicle components with an RFID tag marking must show the marking data as plain text; this will
make it possible to enter the data manually in the event of a reading failure. The plain text shows,
at least, the content of the module data and, also, the most important production characteristics
(e.g., part number, generation, production time), and can be implemented, e.g., by means of a la‐
bel. The requirements for applying the plain text information are analogous to the requirements for
markings using a 1-D code.
RFID tag data must be recorded promptly at the installation location in order to ensure that it will be
possible to enter the data manually in the case of reading errors (it must be possible to view the
part or, if required, to remove it).
Technical implementation
VW 01067 describes the RFID technology used.
The permissible characters can be gathered from the ASCII-Character-to-6-Bit-Compaction Substi‐
tution Table; see ODETTE 5510, and JAIF Global RFID. The characters "$" and "%" used for the
check digit are already reserved in the 6-bit code as control characters and therefore cannot be
used for the check digits. Because of this, they must be converted to permissible special charac‐
ters "@" and "\" before the tags are written.
After the tags are read, the characters are converted back: "@" becomes "$" and "\" becomes "%".
7.2.1  
7.3  
8  
8.1  


### 第 22 页
Page 22
VW 01064: 2015-01
Unique Item Identifier (UII – bank 01)
VW 01067 and VW 01067 RFID Data Matrix Tutorial  describe the UII's content and syntax.
User memory (UM - bank 11)
The user memory contains the build status documentation data just like the data matrix code (see
section 5.2); see figure 17.
Examples of user memory data content
Following is a description of 4 data content variants with contents that have a syntax just like that
for 2-D code markings. The advantage is that this does not require any modifications to the sys‐
tems at the plants building the vehicles.
In exceptional cases, deviations from these defined data content variants are permissible. Part in‐
formation may also be written to the UII depending on the size of the available RFID tags. In this
case, the particular implementation must be discussed and agreed upon with all the parties in‐
volved.
Standard data content for part installation checks with build status documentation
The standard data for part installation checks with build status documentation contains the "Part
number with color code" and "Module data" data fields; see figure 17 (see section 5.2.1).
Figure 17
Data identifier (DI) used: Z; see ANSI MH 10.8.2
The data block for build status documentation data comprises 36 characters; in the case of 6-bit
coding, 240 bits (including control characters) are used.
8.1.1  
8.1.2  
8.2  
8.2.1  


### 第 23 页
Page 23
VW 01064: 2015-01
Minimum data content for part installation checks
The minimum data content for part installation checks without build status documentation contains
the "Part number with color code" data field only (see section 5.2.2); see figure 18.
Figure 18
Data identifier (DI) used: Z; see ANSI MH 10.8.2.
The data block for build status documentation data comprises 19 characters; in the case of 6-bit
coding, 138 bits (including control characters) are used.
Minimum data content for build status documentation
The minimum data content for build status documentation contains the "build status documenta‐
tion" data field only (see section 5.2.3); see figure 19.
Figure 19
Data identifier (DI) used: Z; see ANSI MH 10.8.2.
The data block for build status documentation data comprises 19 characters; in the case of 6-bit
coding, 138 bits (including control characters) are used.
NOTE 11: The module data's syntax is specified in the Group Modules Catalog. Normally, pur‐
chase parts have a three-character manufacturer's code (H) as per VW 10540-1 with a leading
blank space. Contact via: <EMAIL>
8.2.2  
8.2.3  


### 第 24 页
Page 24
VW 01064: 2015-01
Maximum data content for part installation checks with build status documentation
All data fields are filled out when the maximum data content for part installation checks with build
status documentation is used. (See section 5.2.4.) The length of the "Part code" and "Additional
data" data fields will depend on the specific use case; see figure 20.
Figure 20
Data identifier (DI) used: Z; see ANSI MH 10.8.2.
The data block for build status documentation data comprises 66 characters; in the case of 6-bit
coding, 396 bits (including control characters) are used.
8.2.4  


### 第 25 页
Page 25
VW 01064: 2015-01
Applicable documents
The following documents cited in this standard are necessary to its application.
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
VW 01067
Use of Auto ID for Unique Object Marking; Serialization Using Optical
Coding Methods and/or Radio Frequency Identification (RFID)
VW 01067 RFID Data
Matrix Tutorial
Auto-ID-Tutorial - Unique Object Identifications
VW 01098
Part Number System
VW 10500
Company Designation, Marking of Parts; Guidelines for Use
VW 10540-1
Manufacturer's Code; for Vehicle Parts
VW 10540-3
Manufacturer's Code; Issuing Ranges for Foreign Plants Issuing Manu‐
facturer’s Codes
WSK.013.290 E
Identification Plate for Electronic Control Units
ANSI MH 10.8.2
Data Identifier and Application Identifier Standard.
DIN EN ISO/
IEC 15416
Information technology - Automatic identification and data capture tech‐
niques - Bar code print quality test specification; Linear symbols
ISO/IEC 16388
Information technology - Automatic identification and data capture tech‐
niques - Code 39 bar code symbology specification
ISO/IEC TR 29158
Information technology - Automatic identification and data capture tech‐
niques - Direct Part Mark (DPM) Quality Guideline
JAIF Global RFID
Item Level Standard, 2011-08-10
ODETTE 5510
RFID for Tracking of Parts & Components in the Automotive Industry,
Table 2
Bibliography
[1]
VW 80115 Electronic Control Unit Identification with KWsP 2000 Services; Version 3.0
[2]
VW 80125 Identification of Electronic Vehicle Systems; Version 2.3
[3]
Data Matrix Symbol Specification, e.g., with: AIM DPM-2006, Association for Automatic
Identification and Mobility
[4]
Volkswagen Modules Catalog BG-Online System
[5]
Das Lesen von Direktmarkierungen – 10 wichtige Aspekte (Reading Direct Markings -
10 Important Aspects), Cognex
[6]
Neue Standards verifizieren 2-D-Data-Matrix-Codes zuverlässig (New Standards Allow
for Reliable Verification of 2-D Data Matrix Codes), Carl W. Gerst III, Cognex
9  
10  


### 第 26 页
Page 26
VW 01064: 2015-01
 
Figure A.1 – Module width/module size of code symbols (x)
Figure A.2 – Standard label
Figure A.3 – One-part labels with barcode
Appendix A (informative)  


### 第 27 页
Page 27
VW 01064: 2015-01
Figure A.4 – Multi-part labels
Figure A.5 – Special application types


### 第 28 页
Page 28
VW 01064: 2015-01
Figure A.6 – Group marking (example with 8 modules)

