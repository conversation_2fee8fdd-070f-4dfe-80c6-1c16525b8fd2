# VW_01067_CH_2017-11_使用Auto-ID标识唯一对象.pdf

## 文档信息
- 标题：20180320_VW_01067_CH_OK.pdf
- 作者：zhangqun
- 页数：36

## 文档内容
### 第 1 页
康采恩标准
���������
版本��������
类别编号：
关键词：
�����
编码��编码技术��标识��������零件标识��车辆标识��工具标识��可重用容器标识��包装标识
使用Auto-ID用于标识唯一对象
使用光学编码程序和�或射频识别��������进行序列化
前言
这个标准的目标是通过使用光学编码方法或射频识别 (RFID)实现对对象的唯一标识(序列化) 和自动
识别。为此, 定义了唯一的数据结构 (参考编号), 可以用通常的光学编码方法或 RFID 应答器来进行, 
因此, 可能使用混合的技术。在使用先进的RFID 技术时，对于既定程序也可以实现使用光学编码 
(条码, 2D 代码, 二维码) 和纯文本格式的备份。
之前的版本
�����������������������������������������������������
变更
对于�������������������有如下变更�
�
段落����缩写��增加������������������������和����
�
段落�����增加
�
段落��������编号范围的结构���最后一段有变更
�
段落��������编号范围的结构���添加警告提示。
请注意使用新版本的标准。
没有署名的电子标准是有效的并且适用。
页���������
专业负责
�����
人名 �����������������
����������������������
标准化工作
��������人名 ����������
����������������������
�����
人名 �����������
保留所有权利。未经大众集团的标准部门事先同意不允许披露或复制。�
��大众股份公司
��������������


### 第 2 页
页��
�����������������
内
内容
页
应用范围�������������������������������������������������������������������������������������������������������������������������
符号和缩写��������������������������������������������������������������������������������������������������������������������
唯一对象标识��序列化��的基础知识�������������������������������������������������������������������������������
����技术� ����������������������������������������������������������������������������������������������������������������������
光学编号和可辨识的记号��标签�� �����������������������������������������������������������������������������������
唯一对象标识的数据内容的插图������������������������������������������������������������������������������������
样件包括组件手册和图纸登记的标识
����������������������������������������������������������������������������������������������������������������������������������������
编号范围的结构� �������������������������������������������������������������������������������������������������������������
样件 �����标识�����������������������������������������������������������������������������������������������������������
样件光学标识� ���������������������������������������������������������������������������������������������������������������
量产施工文件中的�����技术� �������������������������������������������������������������������������������������
编号范围的结构� �������������������������������������������������������������������������������������������������������������
批量零件的�����标识������� ����������������������������������������������������������������������������������������
大批量零件光学标识�������������������������������������������������������������������������������������������������
工具标识� ����������������������������������������������������������������������������������������������������������������������
编号范围的结构� �����������������������������������������������������������������������������������������������������������
工具�����标识��������������������������������������������������������������������������������������������������������������
康采恩内部标识������������������������������������������������������������������������������������������������������������
工具光学标识� ���������������������������������������������������������������������������������������������������������������
可重用容器的标识��������������������������������������������������������������������������������������������������������
编号范围的结构� ������������������������������������������������������������������������������������������������������������
可重用容器的�����标识� �����������������������������������������������������������������������������������������������
可重用容器的光学标识���������������������������������������������������������������������������������������������������
包装标识� �����������������������������������������������������������������������������������������������������������������������
编号范围的结构� ������������������������������������������������������������������������������������������������������������
包装 �����标识��������������������������������������������������������������������������������������������������������������
包装光学标识�����������������������������������������������������������������������������������������������������������������
JIS包装标识��������������������������������������������������������������������������������������������������������������������
编号范围的结构� �����������������������������������������������������������������������������������������������������������
���包装�����标识� ��������������������������������������������������������������������������������������������������������
���包装光学标识������������������������������������������������������������������������������������������������������������
车辆标识�� ���������������������������������������������������������������������������������������������������������������������
编号范围的结构� �����������������������������������������������������������������������������������������������������������
车辆的 RFID-标识���������������������������������������������������������������������������������������������������������
车辆光学标识� ���������������������������������������������������������������������������������������������������������������
适用文档�����������������������������������������������������������������������������������������������������������������������
�字节标号�����������������������������������������������������������������������������������������������������������������������
例子��样件的参考编号�� �������������������������������������������������������������������������������������������������
�
�
�
���
���
�
���
�����
�����
�����
���
�����
�����
�����
���
�����
�����
�����
�����
���
�����
�����
�����
���
�����
�����
�����
���
�����
�����
�����
���
�����
�����
�����
�
附录��
附录��
应用范围
本标准描述了使用光学编码和/或 RFID 方法对对象进行唯一标识 (序列化)。这个方法用于零件、车
辆、工具、可重用容器和包装。�
本标准的标识不能代替VW�����和VW�����标准中对于零件标识的规定,以及VW 34022 标准中对
于工具、辅助工具、测试设备和仪表��铭牌��标识的规定。
���


### 第 3 页
页��
�����������������
注释���这个标识不适用于可诊断的零件。控制仪器的标识规定在����������������仪器的
铭牌��中。
本标准旨在：
�
开发人员，指定唯一对象标识
�
质量安全人员,检验唯一的对象标识
�
汽车零件供应商, 实现了一个唯一的对象标识 
此标准的内容基于���������、VDA �����、��������、VDA ����、����������并考虑
到���������标准的现状。
符号和缩写
应用家庭标识符
施工文档
公司编号
公司识别号
����������������
数据标识符
二维码
直接零件标识
数据结构格式标识符
数据通用编号系统
传输末端
车辆编号
组分隔符
全球运输标签
发行机构代码
国际电信联盟�
需要量
康采恩商业平台�
文件分类系统�
对象标识符
零件编号
零件生产序列号
射频识别技术
记录分隔符
生产序列号
技术指导文档
高频
唯一物品标识符
用户内存
独一无二的合作伙伴识别键
资源定位符
试生产认可车辆
车辆识别号
总装
字母数字的
数字的
���
���
���
��
���
�����
��
���
���
�����
����
���
���
��
���
���
���
���
���
���
���
��
���
����
��
��
���
���
���
��
����
���
���
���
���
��
�


### 第 4 页
页��
�����������������
唯
唯一对象标识��序列化��的基础知识
�����技术
本文介绍了使用无源超高频应答器��频率范围���������到���mhz ��的目的是为了实现对象的唯一标
识��序列化��和自动跟踪。
空气接口符合ISO/IEC 18000-63/EPC 级别1 第2代。
在选择应答器时考虑由国际电信联盟 (ITU) 确定的超高频通信的不同频带: 欧洲 (865 mhz 到
868mhz ), 美国 (902 mhz 到928mhz ) 和日本 (950 mhz 到960mhz )。
根据genuine ISO/IEC 18000-63/EPC 级别1第2代，RFID-应答器配备4个逻辑内存区域/内存条(MB) 。
����
����
�����和密码
唯一物品标识符 �����签
标识�������
用户内存�����
����
����
详细规范和内存区域的描述在相关的标准中。
MB01 和 MB11区域用于存储对象特定数据的 。在区域 MB01中, 除了需要的控制信息还需储存唯一的
物品标识符。 UII包含一个单独给出的唯一的参考编号。存贮区域 MB01 在初期描述以后被保护免受
进一步文字干预(锁定)。用户内存 (UM) 位于 MB11 区域内。在用户内存中可以选择存储其他用户数
据。但应该注意的是, 用户内存相对于 UII 配备少量的读/写性能。
各个区域的内存大小将因应用程序的用途而异。
表1显示了 UII和附加的UM的首选尺寸。额外的UM应总计128bit到512bit, 以确保足够的灵活性。额
定值被四舍五入且适应可用内存大小, 以便确保涵盖各应用特定的内存需求。
���
�����


### 第 5 页
页��
�����������������
表
表�����每个应用程序的内存占用量
应用的目的
最小内存占用量
标准样件
�����������（唯一物品标识符）
车辆� �分布�
�����������
车辆��试生产�
������������������������
工具
�����������
容器
�����������
包装
�����������
数据内容的结构和设计遵循 ISO/IEC 标准。遵循 ISO/IEC 标准基本特征的RFID 数据结构的转化与既
定条形码/二维码一致并且兼容。用这种方式保证了条形码/二维码和RFID的共存并且逐步向 RFID 
迁移。
在应答器的控制信息中储存一个可用于过滤数据的应用家庭识别符 (AFI) (ISO/IEC 15961-1)。
在ISO 17363、ISO 17364、ISO 17365、ISO 17366 和ISO 17367 中AFI 是专有名词。包括 AFI参数的控
制信息的完整结构在相关的标准和介绍中 (包括 VDA 5500, VDA 5501 , VDA 5509, VDA 5510, VDA 
5520), 因此在此没有进一步的解释。
在 UII 中输入一个唯一的参考编号。参考编号的语法和数据结构基于JAIF 全球射频识别 (RFID) 物品
水平标准 ISO/IEC 15418 和 ISO 17363, ISO 17364, ISO 17365, ISO 17366 和 ISO 17367 。应用专有结构在
4节中提供。UII 以传输末端 (EOT) 结束。如果 UII 的数据结构 (MB01)占据了整个可利用储存区
域，那么可以省去 EOT  。用填充字符填充当前的16 bit字符的空位。根据 ISO/IEC 15962 使用一个确
定的比特序列(单态) 。细节参见表2, 描述符合ISO / IEC标准的 RFID 数据结构 (MB01）和前置的日
志控制bit (个人计算机-标头)的一般结构。


### 第 6 页
页��
�����������������
表
表������������������数据结构示意图结构
����位置�
�����
数据类型
值
尺寸
描述
������������协议控制字
�������
������
硬件
发出
�������
循环冗余校验
�������
长度
变量
������
表示排除��字段和属性�
���字段的��bit字母的
数量。
��
�����������
����������
�����
����没有有效的用户数
据��或没有
����
��������中有效的用户
数据
��
�����������
���
�����
����没有使用�扩展的����
字��
��
�����������
���
�����
����基于���的数据解
释规则
�������
���
�� ��
�����������������
����
������
���应用家庭识别符根
据������������������
����������������������
���������������������
和����������
总计
�������
���������
从位置��开始，
到数据的末端/
可用内存的末
端。
唯一参考编号��占位符�。应用专有的数据内容见第4节。
传输末端
���
����
������
填充直到16bit
字母的最后一
位�
�����
��������
����������
������������
��������������
����������������
或�
����������������
�
�����������
���������
�������
填充根据���������
�������单态��
总计
变量
最多���������
总�����
�����
变量
最多���������
数据的内容被编码为�位。只可以使用在附件 A上标明的字母、数字和选定的特殊字符。


### 第 7 页
页��
�����������������
光
光学编码和纯文本��标签�
二维码用于标识唯一的对象。也可以使用与应用程序相关的代码 128 (如全球传输标签、可重用容
器)。条形码和二维码的数据内容符合 RFID 的原理。在1 D/2D-编码以及RFID应答器中输入相同的参
考 ID。该方法的目的是在混合使用光学和射频 AutoID -方法。
条码的结构根据�� ��������������，在此没有进一步的描述。二维码的结构遵循 ISO/ IEC 15418 和 
ISO/IEC 15434。语法由文件头组成, 后跟一个格式文件头用于命名嵌套数据结构，最后由格式符号作
为结束的符号。单个数据元素用 DI 进行识别, 并用组分隔符分隔。
表� 显示了符合标准的二维码的通用结构�
表� ����符合� ������标准的二维码的示意图结构
起始序列
���
记录分隔符
�
�
格式识别符
��
组分隔符
�
�
唯一的参数编号
��
组分隔符
�
�
其它的数据元素
��
记录分隔符
�
�
传输末端
���
语法结构，在唯一的参数ID旁还可以选择编辑一个附加的数据元素。前提是使用标准 DI (参见 ISO/
IEC 15418) 标识附加的使用数据, 以确保整个企业中对此附加数据内容的正确解释。通过这种方式也
应该避免或减少专有的解决方案。
数据的内容被编码为8bit。为了确保条码/二维码和 RFID 数据结构的同步, 在6bit编码中通常只可以
使用字母、数字和选定的特殊字符 (见附录 A)。
�����


### 第 8 页
页��
�����������������
条码�二维码打印在适当的标签上。编码的结构和标签的设计和他们的内容根据VW 01064和
VW10500 标准。在标签上,   尽可能长地以纯文本形式显示代码的数据内容。
如果除了光学代码还使用附加的RFID,那么建议用于光学的识别，根据 ISO/IEC 29160打印 RFID标
志。对此可以使用普遍的选项(参见图 1) 或使用下列与应用程序相关的一个选项 (请参阅 ISO/IEC 
29160):
�
����可重用容器�
�
����运输单位��包装�
�
����产品包装�
�
����产品�
�
����运费�
打印�����标志的前提是，标签具有适当的尺寸。
图 ���������标签��一般�
唯一对象标识的数据内容的插图
标准样件标识包括零件规格和图纸内容
电子识别对所有标准样件都是有效的根据这个标准 (VW 01067) , 直到 VFF开始。可能存在车辆项目
和/或零件特有的偏差。
本标准描述了二维码和/RFID的使用。标准零件统一使用二维码标识。RFID 标识适用于所有的标准
样件。由于车辆项目专有的必要性其他零件同样用 RFID进行标识。
RFID 参考列表位于康采恩商业平台 (KBP) http://vwgroup-supply.com 下，路径为： Informationen > 
Gesch�ftsbereiche > Forschung und Entwicklung >TE-Logistik > RFID。
直接访问:
内部
外部
射频识别
射频识别�
���
�����


### 第 9 页
页��
�����������������
标识量产零件的目的是为了检测建造，施工文档使用VW� �����。
元
元件规格和图纸内容
图纸和元件规格的�����标识的说明��参见图���重复文本是VW� ������中的NO-E4� �
表�����编号范围的结构
编号范围
字符数量
�
公司识别号������
��个字符�����
�
零件号������零件号��康采恩�
最多����个字符�����
�
零件生产序列号������
最多���个字符�����
字符的数量��总计�
最多����个字符�����
CIN, PN 和PSN 字符的数量不可超过总的� �个字符� ����。R FID 数据结构要求附加字符( 请参见
4.1.2 节), 生成一个最多40个字符（an）的数据字符串。这个长度可以用普通大小的内存 (240 位 
UII) 来覆盖, 这样零件的混合标识可以使用 RFID 和二维码进行保证。
���的设计取决于标准零件的内部或外部是否被标识��参见表� ��
表�����标识的种类
标识
公司识别号������
字符数量
�
外部
供应商的������������
��个字符����
�
内部
品牌代码���车间���成
本中心
��个字符����������个字符�����
� ��个字符����
����的所有者发出参考编号并保证唯一性。
根据VW 01098 建立零件号� �����零件号。零件号可能有前导空格。在实现基于光学和 基于RFID 的 
AutoID 系统时, 要特别考虑前导空格。它们是强制编码的, 以帮助确保在 IT 系统端引导的附加信息上
可以使用完整编码字符串或编码字符串的零件作为参考。
���只由大写字母和数字组成。�
图�����重复�������������标识�
�������
编号范围的结构
标准零件标识唯一参考编号的设计要确保3个编号范围的基础, 请参见表 4:


### 第 10 页
表�����典型的������数据结构��外部�
数据内容�����������
字符数量
值
�
数据标识符�����
��个字符�����
���
�
发行机构代码������
��个字符�����
���������
�
公司识别号������
��个字符����
���������
�
零件号������零件号
最多����个字符�����
�����������
�
分隔符
��个字符
�
�
零件生产序列号������
最多���个字符�����
���������
�
传输末端
��个字符�����
�������
字符的数量
最多����个字符�����
�������
字母数字描述�
�����������������������������������
品牌奥迪和保时捷要求额外的颜色代码� �如果可用� �的插图��以及组件特定的技术标准� �如果有
的话� 。相应数据结构的结构在附件 B。
注释2：颜色代码和技术级别的插图导致更长的零件号。同时 DUNS 编号、零件号和序列号的组合
必须包括 AutoID 特定的控制字符，共计最多为 40个 (an)。 即，用于系列编号插图的剩余的字符
最多限制在5或6个字符 。
页���
�����������������
�������
样件的����标识
在控制信息��������中使用以下的AFI������与产品相关�。
���������
供应商标识
表�显示了������������中唯一参考编号的结构�


### 第 11 页
页���
�����������������
表�����典型的����数据结构��内部�
数据内容�����������
字符数量
值
�
数据标识符�����
��个字符�����
���
�
发行机构代码������
��个字符�����
����公司内部标识�
�
公司标识号������
��个字符�����
���������
�
零件号����
最多����个字符�����
�����������
�
分隔符号
��个字符
�
�
零件系列号������
最多���个字符�����
���������
�
传输的末端
��个字符�����
�������
字符的数量
最多����个字符�����
�������
字母数字描述�
������������������������������������
品牌奥迪和保时捷要求额外的颜色代码（如果可用）的插图，以及组件特定的技术标准。（如果
有的话）。相应数据结构的结构在附件 B。
注释2：颜色代码和技术级别的插图导致更长的零件号。同时 DUNS 编号、零件号和序列号的组合
必须包括 AutoID 特定的控制字符，共计最多为 40个 (an)。 即，用于系列编号插图的剩余的字符
最多限制在5或6个字符 。
����������康采恩内部标识
表� ��展示了UII(MB01)内部唯一参考编号的结构：


### 第 12 页
描述
数据内容
起始序列
���
记录分隔符�����
�
���������
格式标识符
��
组分隔符�����
�
���������
数据标识符�����
���
发行机构代码������
���������
公司识别号������
���������
零件号������零件号��康采恩�
�����������
分隔符
�
零件生产序列号������
���������
记录分隔符�����
�
���������
传输末端������
�����������
例子������
����
�������������������������������������
�
���
图���展示了一个相应结构的，典型的二维码标签�
图�����典型的二维码标签
注释�����员工的示例标签在VW01067 -SampleLabels for Prototype Parts v1.0中，或对于外部供应商
示例标签在VW01067 - SampleLabels forPrototype Parts v1.0中。
表8中的示例将扩展到特定的技术状态 (请参见4.1.1 节), 然后数据内容如下设计:
初始状况������������
����
������������������������������������������
�
���
变更状况 �������������
����
�������������������������������������������
�
���
页���
�����������������
���������  样件的光学标识
在标准样件的标识框架内使用二维码。
����������供应商标记的标识
表�显示了符合���������标准的二维码内容��包括控制字符��的插图。
表�����标准样件的光学编码��外部�


### 第 13 页
页���
�����������������
表
表�����标准样件的光学编码（内部）�
描述
数据内容
起始序列
���
记录分隔符�����
�
���������
格式标识符
��
组分隔符�����
�
���������
数据标识符�����
���
发行机构代码������
����公司内部标识�
品牌代码���车间���成本中心
���������
零件号������零件号����������
����������
分隔符
�
零件生产序列号������
���������
记录分隔符�����
�
���������
传输末端������
�����������
例子� ������
����
�������������������������������������
�
����图� �
显示了一个相应结构的、典型的二维码标签�
图� ����典型的二维码标签
注释� � VW员工的示例标签在VW01067 -SampleLabels for Prototype Parts v1.0中，或对于外部供应
商示例标签在VW01067 - SampleLabels forPrototype Parts v1.0中。
表8中的示例将扩展到特定的技术状态 (请参见4.1.1 节), 然后数据内容如下设计:
初始状况� �����������
����
������������������������������������������
�
���
变更状况� ������������
����
�������������������������������������������
�
���
���������
康采恩内部标识
表�显示了符合���������标准的二维内容��包括控制字符��的插图。


### 第 14 页
页���
�����������������
量
量产阶段施工文件中的RFID 技术
量产零件电子标识通过二维码或� �����实现，标识的目的是实施施工文档 (BZD)的目的 和/或验证
安装。零件上标识的种类和标识的位置在零件图纸中确定，在总装（ZSB）中的在PDM 页中没有零
件号。
施工文件: 每个零件都附带标识 (二维码/RFID) 。因此制造过程中的零件都可以确定一个底盘编号。
通过这种方法实现了车辆零件的文档化和可追溯性, 以便在发生损害 (回调) 时可以限制受影响的车
辆的数量。
同时文档涉及到了组件。其中包括车辆零件或总装。这个标准的意义是对组件进行唯一的标识。组
件数据的定义在BG-ONLINE系统中。标识说明可以在技术图纸和 TLD 中找到。
每个车辆项目需要 根据BZD进行决定, 是在产品组/模型系列组根据过程标准 1. 4_K-GQZ/I_01_PS 
"产品组中 BZD 范围的定义和实施"。 BZD 的决定是通过KVS中的TLD页TLD. 011.XXX. B0 来发出。
标识是认可组件的一个属性��在取样时就要考虑。要检查的是:
�
模拟生产条件下的数据
�
应用数据字符串的内容
�
部分标识的设计与应用
�
保留≥15年
在取样的框架内进行标识检查。供应商或制造商为这一目的提供系列一致的标识零件。
供应商和制造商保证遵守在这里描述的在当前系列生产中对标识的要求 (例如, 通过抽样检测)。
从创建数据开始，保留期至少��年����相当于�����类�����。文件是重要的与质量有关的信息, 以标识
的部分汽车建设 (如批使用的原材料, 制造商使用的供应零件, 测试和调整值, 制造地点和设施, 等等), 
将他们归档到参考数据中。如有需要, 可以清楚说明功能、制造或材料的质量。
如果供应商制造一个�����，其中有一个����负责的零件��例如在ZSB罐中有一个燃料泵�，然后供应
商必须理解BZD负责的零件，�其数据添加到ZSB的数据中��并在其文档中储中至少��年。�这也适用
于控制器和机械零件的ZSBs��如控制设备的车灯控制单元�。如果没有满足标识的要求, 受影响的组件
被认为是错误的, 在适当的情况下不需要重新安装。
安装测试��每个组件附带一个标识��二维码�������。编码数据在制造过程中使用, 对汽车零件 (技术设
计, 车龄, 制造商) 的正确装配进行检查。
�����


### 第 15 页
页���
�����������������
表
表������编号范围的结构
编号范围
字符数量
�
公司识别号������
��个字符�����
�
零件编号������零件编号��康采恩�
最多����个字符�����
�
组件������
最多���个字符�����
�
零件生产序列号������
最多���个字符�����
字符的数量��总计�
最多����个字符�����
�������������和�����字符的数量总计不可以超过����个字符。包括附加字符，产生一个最多��个
字符的数据字符串。�该字符长度对应于常见的内存大小������位�������因此使用������和二维码可以
保证组件的混合标识。
����的所有者设置参考编号并保证唯一性。零件号 (PN)/零件号的构造根据VW01098。PSN只包含
大写字母和数字。
注��在��年的存档期内，���的编制必须独立于零件号，并且与 BGR唯一的结合。
另外其他的参数储存到RFID�应答器中，参见�表���。
表������编号范围的结构
编号范围
字符数量
�
厂商代码
最多���个字符�����
量产零件的����标识������
在控制信息��������中使用以下AFI的方法� �����与产品相关� 。表3显示了 UII (MB01) 中唯一参
考编号的结构。
注:  BZD-数据串的建造在康采恩-组件-条目 "BGOnline"中描述，参见VW 01064的4.1章节 (联系方
式： <EMAIL>) 。在本例中假定 BZD 数据的序列编号为7位数。
��������编
编号
号范围的结构
量产零件标识的唯一参考编号的结构是保证4  个编号范围的基础，参见表� ��。
�������


### 第 16 页
页���
�����������������
表
表������典型的������数据结构
数据内容�����������
字符数量
值
� 数据标识符�����
��个字符�����
���
� 发行机构代码������
��个字符�����
���������
� 公司识别号������
��个字符����
���������
� 零件编号�����零件编号包括目录��
颜色编号
最多����个字符�����
��������������
� 分隔符
��个字符
�
� 组件������
��个字符�����
��������
� 分隔符
��个字符
�
� 零件生产序列号������
最多���个字符�����
�������
字符的数量
最多����个字符�����
�������
字母数字描述�
����������������������������������������
注意��如果不使用颜色代码��则不会填充生成的空格��这意味着数据结构的内容如下示�
�������������������������������������
在取消颜色代码后，在����区域有足够的磁盘空间��即数据结构是以 EOT 结束的。
表����显示了UM（用户内存）中数据的结构�
表������数据内容和 ����结构
数据内容����������
字符数量
值
�
数据结构格式标识符��������
������
�������
�
预光标��压缩编码������������
������
�������
�� 字节计数指示开关
����
�� 以下字节数量
�����
�������������
数据内容����������
字符数量
值
�
数据标识符�����
��个字符�����
�
�
厂商代码
����个字符�����
���
�
传输末端������
��个字符�����
�������
字符的数量
最多���个字符�����
������


### 第 17 页
页���
�����������������
量
量产零件(BZD)的光学标识��
在量产零件标识的框架中� �根据VW� ����标准插入二维码。在经典的标签旁可以存在直接的零
件记号。二维码能也可以是正或长方形。
例子参见表14。
表���
结构
正方形
长方形
代码符号
点的尺寸
��������点
��������点
尺寸单位是���
无稳定区
�������������
������������
尺寸单位是���
有稳定区
�������������
�������������
二维码符合下面的的质量要求�
�
符号质量根据 ��������������等级���或�（标签）
�
符号质量根据 ISO/IEC�������������（DPM）
�
纠错��������
�
模块尺寸至少�����mm
�
打印分辨率 300 dpi 或更高
�
Ruhe区域至少每页2mm  
注��只有与所有过程人员协商后��才允许偏离上述的质量标准。遵守质量标准的证明以及二维码的可
读性是由供应商提供的。
表�������根据����������典型的二维码数据内容
零件号包括索引和颜色代码
��������������
����
���������
组件
���
厂商代码
���
编号
�������
校验码
���校验码�
基于VW �����的典型字符串的内容�
��������������������������������������������������������其他数据
上述例子的����编码�
���������������������������������������������
�������


### 第 18 页
页���
�����������������
典型的������标签�参见图���
图���������������标签带有纯文本和���代码
有关标签结构的更多详细信息��以及其他示例，请见文档�������������������������. Arbeitsstand. 
pptx "。
本文本从供应商平台������������������������������������������������������������
�������������������������������������������������������������������������中获得。
工具的标识
编号范围的结构
工具标识的唯一参考编号的结构根据VW��������根据������������确保2个编号范围的基础��
参见表����
表������编号范围的结构
编号范围
字符数量
�
公司身份编号������
��个字符�����
�
库存�工具编号
最多����个字符�����
����的设计取决于这些工具是在外部还是内部标识��请参阅表����
表������标识的选择
编号范围
字符数量
�
外部
供应商的�������������
��个字符����
�
内部
品牌代码���车间���成
本中心
��个字符����������个字符�����
� ��个字符����
库存/工具编号由 CIN 的所有者进行发出。所有者应确保参考编号的唯一性。
可以选择的使用用于存储其他用户数据的用户内存(UM), 请参见VW34022。
�����
�������


### 第 19 页
页���
�����������������
表������典型的�����数据结构��外部�
数据内容�����������
字符数量
值
�
数据标识符�����
��个字符�����
���
�
发行机构代码������
��个字符�����
���������
�
公司识别号������
��个字符����
���������
�
生产序列号�����
最多����个字符�����
������������������
�
传输末端
��个字符�����
�������
字符的数量
最多����个字符�����
�������
�
字母数字描述��
���������������������������������
表������典型的�����数据结构��内部�
数据内容�����������
字符数量
值
�
数据标识符�����
��个字符�����
���
�
发行机构代码������
��个字符�����
����公司内部标识）
�
公司识别号������
��个字符�����
���������
�
生产序列号�����
���个字符�����
������������������
�
传输末端
��个字符�����
�������
字符的数量
最多����个字符�����
�������
字母数字描述�
����������������������������������
�������
工具的�����标识
在控制信息内部（MB01）要使用下面的AFI：�����产品相关��
���������
供应商标记的标识
表��显示了������������中唯一参考编号的结构�
�������
康采恩内部标识�
表��显示了������������中唯一参考编号的结构�
�������
工具光学标识�
在工具标识的框架下，使用二维码。


### 第 20 页
表������工具(外部)光学编码
描述
数据内容
起始序列
���
记录分隔符�����
�
���������
格式标识符
��
组分隔符�����
�
���������
数据标识符�����
���
发行机构代码������
���������
公司识别号������
���������
生产序列号�����
������������������
记录分隔符�����
�
���������
传输末端������
�����������
例子������
����
����������������������������������
�
���
图���显示类似建造的，�典型的二维码标签�
表������工具�内部�光学编码
描述
数据内容
起始序列
���
记录分隔符�����
�
���������
格式标识符
��
组分隔符�����
�
���������
数据标识符�����
���
发行机构代码������
����公司内部标识�
公司识别号������
���������
生产序列号�����
������������������
记录分隔符�����
�
���������
传输末端������
�����������
页���
�����������������
���������
供应商标识
表��显示了符合���������标准的二维码内容��包括控制字符��的插图。
图�����典型的二维码标签
���������
康采恩内部标识
表����显示了符合���������标准的二维码内容��包括控制字符��的插图。


### 第 21 页
页���
�����������������
例子� �����
����
����������������������������������
�
����图� ��显
示了符合建造的，� 典型的二维码标签�
图�����典型的二维码标签
可重用容器
编号范围的结构
重用容器的唯一参考编号标识的结构确保基于3个编号范围��参见，表����
表������编号范围的结构
编号范围
字符数量
�
公司识别号������
��个字符�����
�
容器类型
��到���个字符�������
�
量产零件�����
最多���个字符�����
��� 连续文本字符串��不包含空格
����的设计取决于可重用的容器是否标识外部或内部��参见表����
表������标识种类
标识
公司识别号������
字符数量
�
外部
供应商的�������������
��个字符����
�
内部
品牌代码���车间���成
本中心
��个字符����������个字符�����
� ��个字符����
�����
�������
����的所有者发出参考编号并保证唯一性。
�������
可重用的容器的�����标识
在控制信息（�����）中使用下面的AFI������可重用的容器�。
���������
通过供应商标识
表��显示了������������中唯一参考编号的结构�


### 第 22 页
页���
�����������������
表
表������典型的�����数据结构��外部�
数据内容�����������
字符数量
值
�
数据标识符�����
��个字符�����
����到����
�
发行机构代码������
��个字符�����
���������
�
公司识别号������
��个字符����
���������
�
容器类型
��到���个字符�����
�������
�
分隔符
��个字符
�
�
生产序列号�����
最多���个字符�����
���������
�
传输末端
��个字符�����
�������
字符的数量
最多����个字符�����
�������
字母数字描述：
��������������������������������
数据标识符用于决定容器的类型，参见表����
表������可重用容器的数据标识符
数据标识符
容器类型
�
���
可重用容器的通用名称
�
���
大装载载体��托盘
�
���
小型负载载体��自支撑专用包装
�
���
辅助包装��盖子��中间位置��内部包装�
表������典型的������数据结构 �内部�
数据内容�����������
字符数量
值
�
数据标识符�����
��个字符�����
����到����
�
发行机构代码������
��个字符�����
����公司内部标
识�
�
公司识别号������
��个字符�����
���������
�
容器类型
��到���个字符�����
�������
�
分隔符
��个字符
�
�
生产序列号�����
最多���个字符�����
���������
�
传输末端
��个字符�����
�������
字符的数量
最多����个字符�����
�������
�
���������
康采恩内部标识
表����显示了������������中唯一参考编号的结构�


### 第 23 页
页���
�����������������
字
字母数字描述�
��������������������������������
数据标识符用于决定容器的类型，参见表����
表������可重用容器的数据标识符
数据标识符
容器类型
�
���
可重用容器的通用名称
�
���
大装载载体��托盘
�
���
小型负载载体��自支撑专用包装
�
���
辅助包装��盖子��中间位置��内部包装�
可重用容器的光学标识
可重用容器的光编码取决于特定的应用场景。使用代码128可以实现非常高的读取范围, 因此建议
采用可重用容器标识代码128。允许使用二维码进行标识。
表������可重用容器的光学编码�外部�
描述
数据内容
数据标识符�����
���
发行机构代码������
���������
公司识别号������
���������
容器类型
�������
分隔符
�
生产序列号�����
���������
例子���������������������������������
图���显示了相应结构，典型的条形码标签：
图�����典型的二维码标签
�������
���������
供应商标记的标识
表����显示了符合�������标准的编码内容的插图：�


### 第 24 页
页���
�����������������
表������可重复使用容器（内部）的光学编码
描述
数据内容
数据标识符�����
���
发行机构代码������
����公司内部标识�
公司识别号������
���������
容器类型
�������
分隔符
�
生产序列号�����
���������
例子�����������������������
�����������图���显示了典型的条形码标签：
图�����典型的条形码标签 
包装的标识
表������编号范围的结构
编号范围
字符数量
�
公司识别号������
��个字符�����
�
包装���
��个字符����
����的设计取决于包装是在外部还是内部标识��参见表��
���������
康采恩内部标识�
表����显示了符合���������标准的编码128的插图�
�����
�������
编号范围的结构
唯一的用于标识包装的参考编号的结构要保证在2个数字范围��参见表����


### 第 25 页
页���
�����������������
表
表������标识种类
标识
编号范围
字符数量
�
外部
供应商的������������
��个字符����
�
内部
品牌代码���车间���成
本中心
��个字符����������个字符�����
� ��个字符����
包装����将由�����的拥有人分发。包装 ID 采用000000001到999999999的数字范围不允许有重复。康
采恩内部通过添加交货日期来确保公司内部的唯一性。
包装的�����标识
在控制信息内部（MB01）,使用AFI:A2 (运输单位)。
根据 ISO 17365 在 MB01中包装的标识可以使用以下选项(参见 ISO/IEC 15418) :
�
����包装�
�
����混合容器�
�
����纯集装箱�
�
下面介绍一个简单包的例子实现。
表������典型的������数据结构��外部�
数据内容�����������
字符数量
值
�
数据标识符�����
��个字符�����
��
�
发行机构代码������
��个字符�����
���������
�
公司识别号������
��个字符����
���������
�
包装���
��个字符����
���������
�
传输末端
��个字符�����
�������
字符的数量
最多����个字符�����
�������
字母数字描述��
�����������������������
�������
���������
标识������������������
表����显示了UII  (MB01)内部的唯一参考编号的建造�


### 第 26 页
页���
�����������������
表������典型的������数据结构��内部�
数据内容�����������
字符数量
值
�
数据标识符�����
��个字符�����
��
�
发行机构代码������
��个字符�����
����公司内部标识�
�
品牌代码���车间���成
本中心
��个字符�����
��������
�
包装���
��个字符����
���������
�
传输末端
��个字符�����
�������
字符的数量
最多����个字符�����
�������
字母数字描述��
�����������������������
包装的光学标识
包装的光学标识使用编码128。
供应商建造的标识�
唯一参考编号的表示形式和附加数据内容的插图根据大众实施指导全球运输标签 (GTL) 。
表34显示了符合 ISO/IEC 的所谓许可证的代码内容板的编码内容的插图。
表������包装的光学编码��外部�
描述
数据内容
数据标识符�����
��
发行机构代码������
���������
公司识别号������
���������
包装���
���������
例子������������������������
���������
康采恩内的标识
表����显示了UII (MB01)内部唯一的参考编号的构造：
�������
���������


### 第 27 页
页���
�����������������
图� ���显示了一个符合建造的，典型的GTL-标识：
表������包装的光学编码（内部）
描述
数据内容
数据标识符�����
��
发行机构代码������
����公司内部�标识�
品牌代码���车间���成本中心
���������
������������
���������
例子� �����������������������
图� ���显示了一个符合建造的，内部条形码标签：
图������典型的条形码标签
图������典型的国际传输标签
���������
康采恩内部的标识
表����显示了符合�������标准的编码内容的插图：


### 第 28 页
页���
�����������������
JJIS包装标识
在顺序开发和车辆相关的供应过程中使用所谓的� ���包装，它区别于标准包装的标识。包装用
包装ID进行标识。另外，平行的把包装ID转到到Lieferavise（VDA 4987）中并且打印在装运文件 
(VDA 4939)上。
编号范围的结构
�����包装标识的唯一参考编号的构成保证以下标识，参见表����
表������编号范围的结构
编号范围
字符数量
�
公司识别号������
��个字符�����
�
包装�缩略语
��个字符�����
�
包装�安装线
��个字符����
�
包装�编号
��个字符����
CIN由�车辆结构定义的�����编号����&����组成。这个包装缩写是由汽车制造厂给出的。装配线在
生产同步呼叫 (DELJIT/Syncro 或 VDA 4986) 的框架内转移给供应商。包装编号将由供应商发出, 并
制造唯一的参考编号 (000001-999999)。到达999999后, 计数序列再次开始于000001。
�
��������包装��简单�
�
��������包装��容器�
下面介绍一个简单包装的例子。表37显示了 UII (MB01) 中唯一参考编号的结构:
�����
�������
�������
���包装的�����标识
根据�����������使用包装标识和下面的选项��参见����������������


### 第 29 页
页���
�����������������
表
表������典型的������数据结构��外部�
数据内容�����������
字符数量
值
�
数据标识符�����
��个字符�����
��
�
发行机构代码������
��个字符�����
���������
�
公司识别号������
��个字符�����
���������
�
包装�缩略语
��个字符�����
���
�
包装�安装线
��个字符�����
��
�
包装�编号
��个字符�����
������
�
传输末端
��个字符�����
�������
字符的数量
最多����个字符�����
�������
字母数字描述��
�������������������������
图������典型的条形码标签
车辆标识�
编号范围的结构
车辆标识的唯一参考编号的设计��将确保两个编号范围，参见表����
表������编号范围的结构
编号范围
字符数量
�
车辆识别号������
���个字符�����
�
车辆编号������
���个字符�����
�������
���包装的光学
包装的光学标识使用编码��������。�例子��
������������������������
图����显示相应结构的条形码标签：
�����
�������


### 第 30 页
页���
�����������������
参考编号的结构取决于车辆在初步系列跟踪框架内还是在车辆分布 (系列) 看见内被标识，请
参见，表����
表
表������标识种类
标识
车辆标识
字符数量
�
车辆分布
车辆识别号������
���个字符����
�
产前批量
车辆编号������
� 车辆识别号������
���个字符�����������个字符�����
车辆分布��系列��内部���标识的结构符合����������并且由车辆制造商给出。
警告指示
车辆标识号�������符合欧盟作为关于人员的信息并且进行数据保护和数据整理������������也就是
说，使用 VIN需要法律依据。
因此��法律或任何其他需要��例如从制造商和分销商的角度来看��描述汽车上的�����的电子读数��并与
负责保护数据的主管协调��以记录在案。
在试生产中��车辆��开发代理�用内部车辆编号�����进行标识。在试生产中不经常使用 VIN, 因此它
仅在车辆标识的框架内作为附加的可选属性。汽车制造商负责参考编号的唯一性。
下面是车辆标识的数据结构。
�������
车辆的 RFID-标识
在控制信息��������中使用以下AFI�����与车辆相关�。
���������
车辆的 RFID-标识���车辆分布�
表��显示了������������中唯一参考编号的结构。结构符合 VDA 5520。


### 第 31 页
页���
�����������������
表
表������典型的������数据结构��车辆分布�
数据内容�����������
字符数量
值
�
数据标识符�����
��个字符�����
�
�
车辆识别号������
���个字符�����
�����������������
�
传输末端
��个字符�����
�������
字符的数量
最多����个字符�����
�������
字母数字描述��
�������������������
表������典型的�����数据结构 �产前批量�
数据内容�����������
字符数量
值
�
数据标识符�����
��个字符�����
��
�
车辆编号������
���个字符�����
�����������
�
传输末端
��个字符�����
�������
字符的数量
最多����个字符�����
������
数据内容����������
字符数量
值
�
数据结构格式标识符��������
������
�������
�
预光标��压缩编码�����������
������
�������
�
字节计数指示开关
����
������
�
以下字节数量
����
����
数据内容����������
字符数量
值
�
数据标识符�����
��个字符�����
�
�
车辆识别号������
���个字符�����
�����������������
�
传输末端������
��个字符�����
�������
字符的数量
最多����个字符�����
�������
字母数字描述�
唯一的物品标识符����������������������
用户内存��������������������������
���������
标准车辆的�����标识����产前批量�
表����显示了UII(MB01)内唯一参考编号的结构和UM������的额外使用�


### 第 32 页
页���
�����������������
车
车辆的光学标识
车辆的光学标识��车辆分布�
描述
数据内容
起始序列
���
记录分隔符�����
�
���������
格式标识符
��
组分隔符�����
�
���������
数据标识符�����
��
车辆编号������
�����������
组分隔符�����
�
���������
数据标识符�����
�
车辆识别号������
�����������������
记录分隔符�����
�
���������
传输末端������
�����������
例子� �����
����
���������������
��������������������
�
����
图� ���显示了符合建造的��典型的二维码标签�
图������典型的二维码标签
�������
���������
车辆标识标签的光学代码和结构根据����������
���������
标准车辆的光学标识�产前批量�
表����显示了符合ISO/IEC标准的二维码内容（包括控制符号）的插图。�
表�������车辆的光学编码��产前批量�


### 第 33 页
页���
�����������������
��������
��������
��������
��������
��������
���������
�������������
�������������
�������������
�������������
���������������
�������������
����������������
��������������
���������������
��������
���������
���������
���������
图纸;图纸框和重复文字
系列车辆上建造组标识;汽车机械零件的 BZD 编码
零件编号系统
公司名称, 零件标识; 使用指南
标识工具、辅助工具、测试设备和仪表 (额定板);要求
信息技术-自动识别和数据收集程序。 电子铭牌
信息技术- 自动识别和数据收集程序。条形码打印质量的试验规范. 2D 符
号
信息技术 -自动识别和数据收集程序。条形码符号规范；代码128
信息技术 -自动数据采集和识别 GS1 数据标识符和 ASC MH10 数据标识符
及其维护
信息技术;自动识别和数据收集程序; 信息技术- 自动介质的传输语法
高容量数据采集
信息技术-用高频方法识别货物(RFID) 用于管理货物流量-数据协议- 第1部
分:应用程序接口 (API)
信息技术- 用高频方法识别货物(RFID) 用于管理货物流动-数据协议: 规则
用于对逻辑数据存储区的数据和函数进行编码
信息技术� �用高频方法识别货物(RFID) 对货物流动的管理- 第63部分: 参
数通信在860-960 兆赫频率类型 C
信息技术- 自动识别和数据捕获过程-RFID 标志
信息技术� �自动识别和数据收集程序��零件直接标签的质量政策� �����
货物集装箱物流链中的射频识别技术�������
射频识别技术��������在物流链中��可重复使用的衬垫������和RPI�
射频识别技术��������在物流链、运输单位
射频识别技术 (RFID) 在物流链-产品包装中的应用
���
适
适用文档
从标准中引用的下列文件必须适用于本标签的使用�


### 第 34 页
页���
�����������������
���������
��������
��������
��������
��������
��������
��������
��������
��������
��������
射频识别技术��������在物流链中��使用在产品上
道路车辆 - 车辆识别码- 内容和结构
装运单据; 版本3.1
从生产同步获取的过程描述中传输数据-转移生产同步交付轮询数据
通过 EDI 采用 EDIFACT 和 XML; 版本1.2
通过装运通知进行数据传输-程序-通过 EDI EDIFACT 和 XML (版本 1.3) 
了解汽车工业中 RFID 的使用情况; 版本1.2
RFID 在集装箱管理中的供应链; 版本2.2
AutoID/RFID 的使用和数据传输到跟踪车辆开发中的零件和零件; 版本2.4
RFID 跟踪汽车工业的零件和组件; 版本2.0
RFID 在生产、物流和车辆识别中的标识服务的实现;版本2.0


### 第 35 页
页���
�����������������
��字
字符�编码
表���������字符 编码表格
字符
二进制值
字符
二进制值
字符
二进制值
字符
二进制值
�����
������
�
������
�
������
�
������
�����
������
�
������
�
������
�
������
����������
������
�
������
�
������
�
������
����
������
�
������
�
������
�
������
����
������
�
������
�
������
�
������
����������
������
�
������
�
������
�
������
����������
������
�
������
�
������
�
������
����������
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
�
������
����
������
�
������
�
������
�
������
����
������
下面的字符可以用于数据内容的插图：
������������������������������������������������������������������������������������������������������������� 
使用�����空格� �����加号� �����减号� �����星号� �������������������等的附加符号需要按照规定。
附录����标准的���


### 第 36 页
页���
�����������������
例
例子
子� �标准零件的参考编号�
为了说明参考编号的结构，特别展示下面不同的零件号�����和零件生产序列号�������的不同型
号的例子，参见，图�����
图�������参考编号的结构
附录����信息���

