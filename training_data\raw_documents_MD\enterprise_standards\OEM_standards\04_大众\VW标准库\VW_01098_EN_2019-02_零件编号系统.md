# VW_01098_EN_2019-02_零件编号系统.pdf

## 文档信息
- 标题：
- 作者：
- 页数：12

## 文档内容
### 第 1 页
Group standard
VW 01098
Issue 2019-02
Class. No.:
02201
Descriptors:
drawing number, multi-use-part number, part number, part number system, standard-part number
Part Number System
Previous issues
VW 01098: 1976-08, 1995-08, 2001-05, 2007-04, 2007-12, 2011-06, 2013-02, 2013-10
Changes
The following changes have been made to VW 01098: 2013-10:
–
Section 2.3 "Structure of the part number"; 2nd paragraph added
–
Section 3 "Standard-part numbers, part numbers for parts similar to standard parts, and multi-
use-part numbers" restructured
–
Section 3.1.3 "Structure of the standard-part number" changed, Figure 4 adapted
–
Section 3.2.2 "Number structure"; last paragraph added
–
Section 3.3.2 "Number structure" added
–
Section 4 "Process material number" added
–
Figure 7, topmost line (right-hand bubble) changed
–
Standard edited
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature. A comma is used as the decimal sign.
The English translation is believed to be accurate. In case of discrepancies, the German version controls.
Page 1 of 12
Technical responsibility
The Standards department
EXD2/5
Andreas <PERSON>
Tel.: +49 5361 9 24933
K-ILI/5 Uwe Stüber
K-ILI
Tel.: +49 5361 9 29063
Uwe Wiesner
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards
departments.
© Volkswagen Aktiengesellschaft
VWNORM-2018-11


### 第 2 页
Page 2
VW 01098: 2019-02
Contents
Page
Scope ......................................................................................................................... 2
Part number (referred to as "material number" in SAP systems) ............................... 2
Numbering system ..................................................................................................... 2
Organization ............................................................................................................... 3
Structure of the part number ...................................................................................... 3
Structure of the front number, middle group, end number, and part number suffix .... 4
Front number and assignment rules .......................................................................... 4
Middle group .............................................................................................................. 5
End number ................................................................................................................ 5
Part number suffix ...................................................................................................... 5
Color code .................................................................................................................. 6
Assignment of part designations ................................................................................ 6
Standard-part numbers, part numbers for parts similar to standard parts, and
multi-use-part numbers .............................................................................................. 6
Standard-part number ................................................................................................ 6
Numbering system ..................................................................................................... 6
Assignment ................................................................................................................ 7
Structure of the standard-part number ....................................................................... 7
Parts similar to standard parts ................................................................................... 7
Numbering system ..................................................................................................... 7
Number structure ....................................................................................................... 7
Multi-use-part number ................................................................................................ 8
Numbering system ..................................................................................................... 8
Number structure ....................................................................................................... 8
Process material number ........................................................................................... 8
Numbering system ..................................................................................................... 8
Structure of the process material number .................................................................. 9
Front number and assignment rules .......................................................................... 9
Middle group .............................................................................................................. 9
End number ................................................................................................................ 9
Part number suffix ...................................................................................................... 9
Color code .................................................................................................................. 9
Part number formats – Examples ............................................................................. 10
Valid part number formats based on the PF944a test program ............................... 11
Applicable documents .............................................................................................. 12
1
2
2.1
2.2
2.3
2.4
2.4.1
2.4.2
2.4.3
2.4.4
2.4.5
2.5
3
3.1
3.1.1
3.1.2
3.1.3
3.2
3.2.1
3.2.2
3.3
3.3.1
3.3.2
4
4.1
4.2
4.2.1
4.2.2
4.2.3
4.2.4
4.2.5
5
6
7
Scope
This standard applies to all divisions of the Volkswagen Group and defines the general structure
and format of part numbers.
Part number (referred to as "material number" in SAP systems)
Numbering system
The part number is a composite code containing identifying and classifying information. Its basic
structure is a 9- to 11-character code; see figure 1, figure 2, and figure 3. The part number can also
be supplemented with additional information (characters) for distribution to systems outside of
Development (e.g., color codes, E + L suffix).
1  
2  
2.1  


### 第 3 页
Page 3
VW 01098: 2019-02
It can be part number, drawing number, and genuine-part number at the same time.
It is specified when a new design is begun (see Product Development Process (PDP) Manual) and
refers to the first use of the part. Therefore, it contains classifying information. Later uses of the
part at other locations will not result in a change to the part number and/or to the designation. In
these cases, the part number contains identifying information only.
Organization
The departments EXD2 (Product Data) and EXDV (see section 3) are responsible for the format-
ting, assignment, documentation, and dissemination of
–
the front numbers,
–
the main group and sub-groups of the part numbers,
–
the end numbers of the part numbers, and
–
all other types of numbers.
Structure of the part number
See figure 1.
Notation of the part number:
–
Between the individual groups of the part number (front number, middle group, end number,
part number suffix, and color code), there are no dots as separators; see also the examples in
figure 7.
–
Exceptions are permitted, if notation without dots is not permitted for system-specific rea-
sons/specifications.
–
If dots were used in the past, this may continue. For new projects and changes, the specifica-
tion of "no dots" must be adhered to.
Figure 1 – Part number structure1)2)
2.2  
2.3  
1)
n = numeric, a = alphabetic, d = alphanumeric
2)
The color code is not part of the title block. It can/may be used, however, in the part designation.


### 第 4 页
Page 4
VW 01098: 2019-02
Structure of the front number, middle group, end number, and part number suffix
Front number and assignment rules
Structure of the front number
See figure 2 and figure 3.
Figure 2 – Front number structure3)
Figure 3 – Front number structure (engine/motor/transmission)3)
Assignment rules for the front number
Due to the limited availability of front numbers, a decision was made on 2006-06-22 to use the re-
maining numbers by "filling in the gaps" (i.e., by using all unassigned front numbers). As a conse-
quence, the correlation between vehicle class and front number cannot be recognized at first
glance anymore. The only means by which a relation can be established is by using the FI.04.
screen in the TEIVON (Part Number Assignment Online) system. For future vehicle projects, this
means that each new front number must be considered individually. The formation of groups with
the 1st and 2nd character positions (e.g., "1J") and the differentiation with the 3rd character posi-
tion (e.g., "0" or "M") have thus lost their general significance for future use.
2.4  
2.4.1  
2.4.1.1  
2.4.1.2  
3)
n = numeric, a = alphabetic, d = alphanumeric


### 第 5 页
Page 5
VW 01098: 2019-02
Front numbers are assigned for:
–
New vehicle projects (with an engineering project (EA) no.)
–
Body styles (station wagon, coupe, box, double cab, etc.)
–
Steering layout (left-hand-drive (LHD)/right-hand drive (RHD) vehicle)
–
Door layout (2-door/4-door)
–
Country-specific adaptations4)
–
New engine/motor/transmission series
In general, front numbers are not assigned to/reserved for derivatives. Alternatives, such as part
identification using primary properties (PR) codes, must be used.
No front numbers are assigned for:
–
Model upgrade
–
Differentiation between checkpoints
–
Further body differentiations (derivative of a derivative)
–
Long/short wheelbase
The application form for new front numbers is available on the Volkswagen intranet: TEIVON
Middle group
The middle group is formed from the 1st position = main group and the 2nd and 3rd posi-
tions = sub-group. The organizational units mentioned in section 2.2 assign the middle group ac-
cording to the main and sub-group classification (see TEIVON).
End number
The end number always identifies the part within a middle group.
If possible, reflection symmetrical parts installed on the left- and right-hand side of the vehicle
should be given an end number ending in an odd digit for left-hand and an even digit for right-hand
parts.
Regardless of vehicle class or engine/motor/transmission, functionally equivalent parts are given
the same end number within a middle group. If there are several variants or model phases, these
are distinguished by the part number suffix (see section 2.4.4).
Part number suffix
The part number suffix consists of two characters and enables:
–
Identification of different model phases of a part (see R&D Division Directive 307)
–
Differentiation between multiple part variants, e.g., different materials
–
Differentiation between purchase parts of different internal design depending on supplier, but
having identical connecting dimensions and performance characteristics, e.g., shock absorb-
ers, brake cylinders, tires
–
Identification of CKD or OT requirements5)
The letters I and O must not be used because of the possibility of confusion.
The following rules apply:
2.4.2  
2.4.3  
2.4.4  
4)
(Produktprozess/2.1_K-ES_01_PS.pdf
5)
CKD = completely knocked down, OT = genuine part


### 第 6 页
Page 6
VW 01098: 2019-02
First character:
–
(BLANK) A through T (without I and O) for production parts
–
U for CKD parts
–
V for Brussels CKD parts, combo pack units
–
Z for parts that are designed by Volkswagen for subsidiary/associate companies. These com-
panies calculate how many of these parts are required or manufacture these parts themselves.
Second character:
–
(BLANK) A through T (without I and O) for production parts
–
U for non-remanufactured exchange packages
–
V for new parts with status "remanufactured"
–
W for returned parts
–
X for remanufactured parts
–
Y for genuine parts subject to mandatory sample inspection
–
Z for parts for bonded warehouses
The use of these special part number suffixes assumes that for certain sub-groups, only a 1-char-
acter suffix (1st suffix character) will be assigned, in order to keep the 2nd suffix character free for
the previously mentioned special suffixes.
Color code
The color code has 3 characters and identifies the color for color-relevant parts.
Assignment of part designations
The central TEIVON office is responsible for assigning part designations (see also Volkswagen
standard VW 01058 for notes on how to name parts correctly).
Standard-part numbers, part numbers for parts similar to standard parts, and
multi-use-part numbers
Standard-part number
Numbering system
The standard-part number – see figure 4 – has the same number of characters as the part number.
It serves as both order number and genuine part number. The standard part drawing number can
be valid for a number of standard parts and is written without a part number suffix. The suffix of a
part number smaller than N 100 000 contains 1 character; for example, N 020 222 2 (if suffix
is < 10). The suffix of a part number greater than N 100 000 contains 2 characters (e.g., N 110 222
02)
In some cases, the standard-part drawing number may be equivalent to a standard-part number
(e.g., N 110 222 02).
2.4.5  
2.5  
3  
3.1  
3.1.1  


### 第 7 页
Page 7
VW 01098: 2019-02
Assignment
The EXDV department (Standard and Multi-Use Parts subdepartment) is responsible for the as-
signment, documentation, and dissemination of standard-part numbers. The numbers are man-
aged in the standard parts management system (link on the intranet: NVS). Contractors have
search permissions in the Engineering Data Management System (KVS).
Structure of the standard-part number
See figure 4.
Figure 4 – Part-number structure for standard parts6)
The N (symbol for standard parts): The 2 characters after the N must be blank for some electronic
data processing systems (e.g., TEIVON, BESSY, and ESON). No zeros must be entered in these
spaces.
The consecutive number comprises 6 digits for the consecutive numbering of parts.
Normally, the standard-part number does not contain classifying information. Exceptions:
N 052 nnn dd, N 054 nnn dd (fuels, coolants, fluids, and lubricants with an in-house standard) and
N 053 nnn dd (fuels, coolants, fluids, and lubricants without an in-house standard).
Materials or property classes, surface protection, color, etc. are identified by the two-character part
number suffix for parts otherwise identical in dimension and shape. The part number suffixes are
assigned consecutively in the order of the request.
The letters I and O must not be used because of the possibility of confusion.
Parts similar to standard parts
Numbering system
All parts which, for design reasons, deviate from international, national, or in-house standards are
treated as standard parts as per section 3, but with the following exceptions. These parts are num-
bered consecutively beginning with N. 9nn nnn nn; see figure 5. Therefore, only the character 9
contains classifying information.
Number structure
See figure 5.
3.1.2  
3.1.3  
3.2  
3.2.1  
3.2.2  
6)
n = numeric, a = alphabetic, d = alphanumeric


### 第 8 页
Page 8
VW 01098: 2019-02
Figure 5 – Part-number structure of parts similar to standard parts7)
The drawing number is the same number, but without part number suffix.
The letters I and O must not be used because of the possibility of confusion.
Multi-use-part number
Numbering system
Multi-use parts are simple system parts (small parts such as washers, bolts, nuts, studs, and pins)
that are not standardized but which can be used in a variety of applications and products. These
parts are not defined in an existing standard, but can be the precursors to new standard parts.
The use of multi-use parts reduces the cost of new designs.
Multi-use parts must not contain confidential content because they are intended to be used for mul-
tiple purposes.
The design engineer creates the drawings and the 3-D model for multi-use parts.
Number structure
See figure 6.
Figure 6 – Part-number structure for multi-use parts7)
The letters I and O must not be used because of the possibility of confusion.
Process material number
Numbering system
For the assignment of numbers, a distinction is made between direct (remaining on the vehicle)
and indirect (auxiliary equipment) process materials. This standard describes only the structure of
direct process material numbers relevant to the bill-of-materials.
NOTE: Notes/descriptions for indirect process material numbers are available from PMP-L/G
(central management and assignment).
3.3  
3.3.1  
3.3.2  
4  
4.1  
7)
n = numeric, a = alphabetic, d = alphanumeric


### 第 9 页
Page 9
VW 01098: 2019-02
Structure of the process material number
Front number and assignment rules
The first 3 characters (marked in bold) define a certain type of material.
Examples:
ALD 092 A7W
Paint, top coat
AKR 303 F02
Corrosion protection
AMV 167 N60
Metal adhesive bond
Middle group
The middle group (marked in bold) describes the application, use, and quality.
Examples:
ALD 092 A7W
Number for a certain quality (e.g., metallic)
AKR 303 F02
Undercoating, fine-seam or coarse-seam sealing (PVC plastisols)
AMV 167 N60
Epoxy adhesive (body manufacture adhesive)
End number
The end number (marked in bold) describes the color code for paints and colors.
For other process materials, it is used for differentiation, e.g., for undercoating and fine-seam seal-
ing.
Examples:
ALD 092 A7W
Reflex silver (color code defined in color combination table)
AKR 303 F02
Fine-seam and coarse-seam sealing
AMV 167 N60
Hem-flange and weld bonding adhesive
Part number suffix
Undercoating and fine-seam sealing:
Two additional characters (01, 02, 03, …) are added to the 9-character process material number
for plant releases, so that the various formulations (rheology, color, etc.) are labeled (different rec-
ipe numbers).
The part number suffix consists of 2 characters and enables:
–
Identification of different model phases of a part (see R&D Division Directive 307)
–
Differentiation between multiple part variants, e.g., different materials
–
Differentiation between purchase parts of different internal design depending on supplier, but
having identical interface dimensions and performance characteristics, e.g., shock absorbers,
brake cylinders, tires
–
Identification of CKD or OT requirements.
Color code
See section 2.4.5 "Color code".
4.2  
4.2.1  
4.2.2  
4.2.3  
4.2.4  
4.2.5  


### 第 10 页
Page 10
VW 01098: 2019-02
Part number formats – Examples
See figure 7.
Figure 7 – Part number formats
5  


### 第 11 页
Page 11
VW 01098: 2019-02
Valid part number formats based on the PF944a test program
See figure 8.
Figure 8 – Valid part number formats
This table (figure 8) is only an excerpt. The system identifiers FI, ES, ON, PF, (ta), MH are internal-
ly used for control purposes. The complete table can be found at the following link: TEIVON.
6  


### 第 12 页
Page 12
VW 01098: 2019-02
 
Legend for figure 8:
a =
alphabetical (upper case only: A through Z, in the case of new entries
without I and O)
n =
numeric
x:
must contain a value (a/n)
n:
must contain a value (n)
a
must contain a value (a)
BLANK:
must be BLANK
d:
can be (BLANK, a, n)
t:
can be (BLANK, n)
u:
can be (BLANK, a)
Characters 11 to 12 (part number suffix) must be entered starting from the left, and for U – Z also
from the right.
Applicable documents
The following documents cited are necessary to the application of this document:
Some of the cited documents are translations from the German original. The translations of Ger-
man terms in such documents may differ from those used in this standard, resulting in terminologi-
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
VW 01058
Drawings; Lettering
7  

