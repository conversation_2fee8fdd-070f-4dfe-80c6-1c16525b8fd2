# VW_60306_3_EN_2011-12_汽车导线载流量测定.pdf

## 文档信息
- 标题：
- 作者：
- 页数：21

## 文档内容
### 第 1 页
Determining Current Capacity of Vehicle Cables
Preface
This Standard in the present issue is based on LV 112-3, which was drawn up by representatives of
automobile manufacturers Audi AG, BMW AG, Daimler AG, Porsche AG, and Volkswagen AG.
Deviations from the LV 112-3 are listed on the cover sheet of this Standard. If modifications to indi‐
vidual test sections become necessary in individual cases, these must be agreed upon separately
between the appropriate department and the relevant manufacturer.
Test reports will be accepted as long as the tests were performed by an independent testing institute
that is accredited as per DIN EN ISO/IEC 17025. Acceptance of the test reports does not automatically
result in a release.
NOTE 1 The LV numbers listed in this document will be converted as per table 1.
Table 1
LV
VW
LV 112-1
VW 60306-1
LV 112-2
VW 60306-2
Group Standard
VW 60306-3
Issue 2011-12
Class. No.:
8ME30
Descriptors:
single-wire vehicle cable, sheathed line, cable bundle, current capacity, voltage drop, derating, fuse design,
LV 112, LV 112-3
Verify that you have the latest issue of the Standard before relying on it.
This electronically generated Standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Numerical notation acc. to ISO/IEC Directives, Part 2.
Page 1 of 21
Technical responsibility
Standards Department
EEKK/2
Dr. Liane Wiegel
Tel.: +49 5361 9-36678
I/EE-23
Michael Pickl
Tel.: +49 841 89-34925
EKDV/4 Dirk Beinker
EKDV
Tel.: +49 5361 9-32438
Manfred Terlinden
Confidential. All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of the Standards Department of a Volkswagen Group
member.
This Standard is available to contracting parties solely via the B2B supplier platform www.vwgroupsupply.com.
© Volkswagen Aktiengesellschaft
VWNORM-2011-08g


### 第 2 页
Page 2 of 21 
LV 112-3: 2011-12 
 
 
Content 
Page 
 
1. 
Scope ................................................................................................................ 3 
2. 
General information ......................................................................................... 3 
3. 
Basics for single-wire cables .......................................................................... 4 
3.1. 
Current capacity ................................................................................................... 4 
3.2. 
Heating by current ............................................................................................... 4 
3.2.1 
Dynamic transition status ................................................................................. 4 
3.2.2 
Steady state ..................................................................................................... 4 
3.3. 
Maximum permissible conductor temperature .................................................. 4 
3.4. 
Derating ................................................................................................................ 4 
4. 
Simplified computational method ................................................................... 5 
4.1. 
Definition of parameters ...................................................................................... 5 
4.1.1 
Characteristic cable parameters ....................................................................... 5 
4.1.2 
Additional parameters used .............................................................................. 5 
4.2. 
Simplified equations ............................................................................................ 5 
4.2.1 
Conductor temperature difference ∆T in the steady case in K .......................... 6 
4.2.2 
Conductor temperature difference ∆T(t) in the dynamic transition status .......... 6 
4.2.3 
Conductor resistance R'(T) in Ω/m ................................................................... 6 
4.2.4 
Voltage drop per length E in V/m ...................................................................... 6 
5. 
Load cases for the cable comparison ............................................................ 6 
6. 
Measuring methods ......................................................................................... 7 
6.1. 
Recording the calibration curve R(T) ................................................................. 7 
6.1.1 
Sample length .................................................................................................. 7 
6.1.2 
Four-point measurement .................................................................................. 7 
6.1.3 
Temperatures to be adjusted............................................................................ 7 
6.1.4 
Evaluation, determination of R'20, αρ, and βρ ...................................................... 8 
6.2. 
Current-loading test with determination of conductor temperature T ............. 9 
6.2.1 
Measuring setup............................................................................................... 9 
6.2.2 
Measurement of room temperature and initial resistance ............................... 10 
6.2.3 
Measurement of the resistance change with various current supplies ............ 11 
6.2.4 
Determining the characteristic cable parameters a and b ............................... 12 
6.2.5 
Determining the time constant τ , dynamic heating characteristics ................. 12 
7. 
Documentation of the parameters to calculate the  current capacity........ 13 
8. 
Application of the simplified description model ......................................... 13 
8.1. 
Calculating the current capacity – steady behavior ........................................ 13 
8.1.1 
Temperature increase and voltage drop with specified current ....................... 14 
8.1.2 
Permissible current depending on ambient temperature – derating ................ 14 
8.2. 
Calculating the heating time – unsteady behavior .......................................... 16 
8.2.1 
Calculating the heating time with specified current ......................................... 16 
8.2.2 
Short-term behavior with load changes .......................................................... 17 
8.2.3 
Short-circuit derating ...................................................................................... 17 
8.2.4 
Selection of fuses ........................................................................................... 18 
9. 
Outlook............................................................................................................ 18 


### 第 3 页
Page 3 of 21 
LV 112-3: 2011-12 
 
10. 
Appendix ......................................................................................................... 19 
10.1. 
List of required measuring equipment (example) ............................................ 19 
11. 
Normative references .................................................................................... 21 
12. 
Literature ......................................................................................................... 21 
 
1. 
Scope 
This Supply Specification (LV) describes the procedure to be complied with for mathematically  
determining 
- 
the current capacity 
- 
the voltage drop 
- 
and the heating time 
of single-wire vehicle cables.  
The thermal behavior is modeled by a few separate, characteristic cable parameters. This ap-
proach has an advantage, in that thermal behavior can be done through simple calculation pro-
cesses when these parameters are known. This can be done in various ambient conditions. 
Starting from known physical parameters (e.g., specific heat capacity, specific electrical conductor 
resistance), the characteristic cable parameters can be calculated on the basis of known and 
documented procedures. The computation is not presented in detail in this Supply Specification. 
However, measuring methods will be indicated, which allow a verification with the aid of several 
measurements. 
Caution: The values calculated by means of this Standard are ideal data for cables installed freely 
in air. Practice-oriented adaptations for other use conditions must be conducted. 
Outlook: As a further consequence, the behavior of multi-wire cables (i.e., of bundles of single-
wire vehicle cables) with and without sheath insulation will be calculable. Therefore, it will be 
possible to dimension the cables in a cable set in a practice-oriented manner, without using 
complex mathematical models (e.g., finite elements). (Not part of this Supply Specification!) 
2. 
General information 
The following is described in this document: 
- 
The definition of the characteristic parameters of single-wire cables 
- 
The measuring methods to verify the calculated characteristic cable parameters of single-
wire cables 
- 
Application examples for using the characteristic parameters 


### 第 4 页
Page 4 of 21 
LV 112-3: 2011-12 
 
3. 
Basics for single-wire cables 
3.1. Current capacity 
The current capacity of single-wire vehicle cables depends on the following factors: 
- 
Conductor cross section 
- 
Conductor material 
- 
Conductor composition 
- 
Conductor diameter (inner diameter of insulation) 
- 
Insulation material 
- 
Conductor diameter (outer diameter of insulation) 
- 
Ambient temperature 
- 
Installation conditions 
- 
Heat dissipation 
3.2. Heating by current 
In the measurements described below, the application of a constant current is always assumed! 
If a cable has current flowing through it, heating takes place, the extent of which depends on 
conductor resistance, time, and on the square of the current. 
In principle when heating occurs, the dynamic transition status and the static condition must be 
distinguished after a long period of current supply. 
3.2.1 Dynamic transition status 
The rate of the heating is determined above all by the electrical power input in the conductor 
material, the specific heat capacity of the conductor and the insulation, and the heat dissipation 
ratios. A thermal time constant τ is characteristic over the course of time, which may range from 
several seconds (10 s to 20 s at 0,35 mm²) to several minutes in large battery cables, depending 
on nominal cross section. 
3.2.2 Steady state 
The resulting temperature increase leads to a heat flow through the insulation material to the 
surface of the cable, and from there by irradiation and convection to the ambient air. A thermal 
equilibrium state takes place over time when current is supplied. In the process, the conductor 
temperature converges asymptotically on an upper limit. The rate of heating is determined by the 
time constant τ described below. One can assume in good approximation that the temperature at 
the conductor no longer changes if the current supply lasts longer than ~5 τ. 
3.3. Maximum permissible conductor temperature 
The highest temperature occurs at the conductor. When a cable is in operation, care must be 
taken that the specific limits for temperature resistance of the insulation material are not exceeded, 
taking into account the ambient temperature and self-heating. 
3.4. Derating 
The connection between the ambient temperature and the permissible current load is described 
by derating. 


### 第 5 页
Page 5 of 21 
LV 112-3: 2011-12 
 
4. 
Simplified computational method 
The thermal behavior of a single-wire cable can be described using simplified equations. 
6 characteristic cable parameters are required in these equations. 
4.1. Definition of parameters 
4.1.1 Characteristic cable parameters 
a 
Linear current dependence of the conductor heating 
in the steady state 
in K/A 
b 
Quadratic current dependence of the conductor 
heating in the steady state 
in K/A2 
R'20 
Length-related conductor resistance at +20 °C 
in Ω/m 
αρ 
Linear temperature coefficient of the  
material-specific conductor resistance 
in 1/K 
βρ 
Quadratic temperature coefficient of the  
material-specific conductor resistance 
in 1/K² 
τ 
Heating time constant, characteristic value for the  
rate of heating of the conductor 
in s 
4.1.2 Additional parameters used 
I 
Current that leads to conductor heating 
in A 
TO 
Permissible long-term service temperature (3 000 h) 
of the cable class 
in °C 
TL 
Temperature of the conductor 
in °C 
Ta 
Temperature of the ambient air around the cable, 
excluding the convective layer,  
(a for ambient) 
in °C 
TL(t) 
Progress of the conductor temperature in the  
dynamic transition area after  
abrupt change of the load current 
in °C 
TLmin 
Beginning temperature of the conductor in the steady  
state before abrupt change of the  
load current 
in °C 
TLmax 
End temperature of the conductor after abrupt 
change of the load current at the end of the  
dynamic transition area 
in °C 
4.2. Simplified equations 
The following simplified equations are used to calculate the temperature increase as a result of 
current supply. 
Note: This simplified representation exactly describes only one definite area of application; how-
ever, it is sufficiently precise for the design of single-wire vehicle cables in practice. If, however, 
the expected ambient temperatures deviate heavily from the temperatures at which the measure-
ments described below were made, new parameters must be determined for this.  


### 第 6 页
Page 6 of 21 
LV 112-3: 2011-12 
 
4.2.1 Conductor temperature difference ∆T in the steady case 
in K 
2I
b
a I
T
⋅ + ⋅
=
∆
 
(eq. 1) 
a
L
T
T
T
−
=
∆
 
(eq. 2) 
ΔT in K; conductor temperature difference as compared to the ambient temperature in the steady 
state (after at least ~5 τ). 
4.2.2 Conductor temperature difference ∆T(t) in the dynamic transition status 
( )





 −
= ∆
∆
−τ
t
e
T
T t
1
max
 
(eq. 3) 
( )
min
( )
TL
T t
T t
−
=
∆
 
(eq. 4) 
min
max
max
L
L
T
T
T
−
=
∆
 
(eq. 5) 
ΔT(t) in K describes the chronological sequence of the difference in the latest conductor tempera-
ture as compared to the conductor temperature before the abrupt change of current supply. If the 
conductor was previously exposed to the ambient temperature Ta without current and for a suffi-
ciently long time (> ~5τ), the conductor temperature TLmin corresponds to the ambient temperature 
Ta. 
4.2.3 Conductor resistance R'(T) in Ω/m 
[
)²]
20
(
)
20
(
1
'
)
('
20
C
T
C
T
R
T
R
L
L
L
°
−
⋅
+
°
−
⋅
⋅ +
=
ρ
ρ
β
α
 
(eq. 6) 
4.2.4 Voltage drop per length E in V/m 
I
R T
E
L ⋅
=
)
('
 
(eq. 7) 
5. 
Load cases for the cable comparison 
The simplified computational procedure described in this Supply Specification enables the 
conductor heating to be calculated with great accuracy, depending on a different current load and 
ambient temperature of the cable. However, often not the evaluation of a specific loading situation 
is desired, but rather the comparison between different cables. 
For this comparison, currents in ambient conditions are defined as follows, with reference to the 
aging temperatures for short- and long-term aging designated in LV 112-1: 
ID 
Long-term use current (derating current), defined as the current  
which, in steady operation  
at an ambient temperature of TO – 50 K, results in a  
conductor temperature of TL = TO. 
in A  
The associated test in LV 112-1 is the  
long-term aging of 3 000 h at TO 
IKU 
Short-term current, defined as the current which, in steady 
operation at an ambient temperature of TO – 50 K, results in a  
conductor temperature of TL = TO + 25 K. 
in A 
The associated test in LV 112-1 is the  
short-term aging of 240 h at TO + 25 K 
IÜL 
Overload current, defined as the current which, in steady 
operation at an ambient temperature of TO – 50 K, results in a  
conductor temperature of TL = TO + 50 K. 
in A 


### 第 7 页
Page 7 of 21 
LV 112-3: 2011-12 
 
The associated test in LV 112-1 is the  
thermal overloading test of 6 h at TO + 50 K 
These three currents must be indicated by the cable manufacturer in addition to the characteristic 
parameters of the cable in supplement 1. 
6. 
Measuring methods 
Starting from known physical parameters (e.g., specific heat capacity, specific electrical conductor 
resistance), the characteristic cable parameters can be calculated on the basis of known and 
documented procedures. The computation is not presented in detail in this Supply Specification. 
However, measuring methods are indicated, which allow a verification with the aid of the example 
measurement at selected cables. 
6.1. Recording the calibration curve R(T) 
Because the resistance of the cable is used later to determine the temperature of the conductor, 
this calibration measurement is conducted first, provided the temperature coefficients of the 
conductor material used are not inherently known. 
The cable to be tested is heated to defined temperatures in a heatable bath with silicone oil. 
As an alternative, measurements are permissible in suitable heating ovens. 
6.1.1 Sample length 
Nominal cross section 
Length 
<2,5 mm² 
10 m 
≤2,5 mm² to 10 mm² 
5 m 
≥10mm² 
2 m 
At least 80% of the specimen must be immersed in the oil. 
6.1.2 Four-point measurement 
The injected current must be kept constant and selected such that the current does not 
considerably heat the conductor. 
Nominal cross section 
Max. permissible current 
<2,5 mm² 
10 mA 
≤2,5 mm² to 10 mm² 
50 mA 
≥10mm² 
200 mA 
Contacting for the voltage measurement takes place directly in the oil. A measuring instrument 
with sufficiently high internal resistance (>1 MΩ) must be provided to measure the voltage. The 
length between the voltage measuring points must be defined as ±2 mm exactly. 
The resistance of the conductor must be determined by measuring current and voltage for each 
adjusted temperature. 
6.1.3 Temperatures to be adjusted 
Beginning with room temperature, measuring points must be selected with an interval every 
approx. 20 K up to TO +50 K. 
Note: TO as per temperature class of the sample. 
The temperature of the oil bath must be measured and regulated. It must be ensured through 
suitable circulation that the bath has a uniform temperature distribution. After the temperature is 
increased to the next level, wait until the temperature measurement of the oil does not change for 
longer than 1 min by more than ±3 K and the resistance value not by more than 0,40/00. 


### 第 8 页
Page 8 of 21 
LV 112-3: 2011-12 
 
6.1.4 Evaluation, determination of R'20, αρ, and βρ 
A calibration curve T(R') must be determined from the measured values. 
T 
Temperature 
in °C 
R' 
Resistance 
in Ω/m 
ΔT = T – 20 °C  
in K 
The resistance temperature value pairs R'(ΔT) – ΔT obtained must then be "fitted" with the follo-
wing resistance curve, and the parameters c, d, and e must be determined. 
(
)
e
T
d
T
c
T
R
+
⋅ ∆
+
⋅ ∆
=
∆
2
'
 
(eq. 8) 
However, the representation as in (eq. 6) is common for the temperature dependence of the 
resistance. Rewriting (eq. 6) results in the following: 
(
)
20
20
2
20
'
'
'
'
R
T
R
T
R
T
R
+
⋅∆
⋅
+
⋅∆
⋅
=
∆
ρ
ρ
α
β
 
(eq. 9) 
R'20 
Length-related conductor resistance at +20 °C 
in Ω/m 
αρ 
Linear temperature coefficient of the  
material-specific conductor resistance 
in 1/K 
βρ 
Quadratic temperature coefficient of the  
material-specific conductor resistance 
in 1/K² 
The constants R'20, αρ, and βρ result from comparing coefficients with the following formulas: 
e
R
=
20'
 
(eq. 10) 
e
d
ρ =
α
 
(eq. 11) 
e
c
ρ =
β
 
(eq. 12) 
Note: The following approximate temperature coefficients can be assumed: 
 
Conductor 
material 
Temperature coefficients 1) 
αρ 
 
βρ 
 
Cu ETP1 
4,0 
10-3 1/K 
0,0 
1/K² 
Al 99,7 
4,1 
0,0 
CuMg 0,2 
3,2 
0,0 
CuAg 0,1 
3,8 
0,0 
CuSn 0,3 
2,9 
0,0 
1) 
Apply as average values for the temperature interval 
+20 °C to +200 °C 
 


### 第 9 页
Page 9 of 21 
LV 112-3: 2011-12 
 
 
6.2. Current-loading test with determination of conductor temperature T 
6.2.1 Measuring setup 
The cable to be tested must be suspended from non-metallic cords in a rectangular-shaped 
chamber to prevent a draft of air. The chamber must have the approximate (i.e., with ±50 mm 
tolerance in the main dimensions) dimensions of (figure 1) below. The number of required sus-
pension points may be decreased by tightening the straight conductor segments with horizontal 
cords. 
Heat dissipation by the suspension must be prevented as much as possible. The distance to the 
floor must be at least 300 mm, the distance of the cables to one another at least 200 mm, and the 
distance of the cable to the room walls at least 50 mm. If necessary, the cable may be laid in a 
meandering pattern. 
To achieve proper measuring accuracy, the length of the chosen cable to be tested must be as 
large as possible, particularly for large nominal cross sections. It is limited in practice by the 
dimensions and measuring chamber. For small nominal cross sections with a high length-related 
resistance, the largest possible length is determined by the dimensions of the test chamber as 
well as by the maximum possible supply voltage of the current source. 
Figure 1 
 
 


### 第 10 页
Page 10 of 21 
LV 112-3: 2011-12 
 
The required length of the sample may either be determined experimentally, or mathematically 
using a theoretical computation of the overload current IÜL to be expected as per the following 
formula. 
)
25
('
max
max
K
R T
I
U
l
O
ÜL
+
⋅
=
 
(eq. 13) 
Imax 
Largest possible length of the sample, including the  
required length up to the external supply points 
in m 
Umax 
Highest possible supply voltage of the current source in V 
R'
)
25
(
K
TO +
 Length-related resistance of the sample at  
TO + 25 K, calculated as per eq.  6 
in Ω/m 
The measurement length between the voltage tapping points must, however, not be less than 1 m, 
even with small nominal cross sections.  
The nominal cross section of both voltage measurement cables must be significantly smaller than 
that of the cable to be tested, in order to minimize thermal influence on the measuring point. A 
better thermal coupling is achieved e.g., by winding the measuring cable around the test line 
before voltage tapping. 
In order to retain a uniform temperature over this range, it is important that at least 1,5 m long 
cable segments for supplying the current are located on both sides up to the voltage tapping point. 
This results in a minimum possible cable length of 4 m. 
6.2.2 Measurement of room temperature and initial resistance 
Because the conductor temperature is determined using the resistance of the sample measured 
during current-loading and taking into account the temperature coefficients αρ and βρ of the 
electrical conductor resistance, the starting situation must be recorded exactly, in order to arrive at 
an exact temperature statement. 
After the measuring setup has been allowed to dwell at least 4 h without current, so that the 
conductor assumes room temperature, the initial resistance and the associated conductor tempe-
rature = room temperature is measured with a current/voltage measurement. 
When conducting the current-voltage measurement, do not select a current that is too low, in 
order to ensure the required accuracy of reading during the voltage measurement. Care must be 
taken to keep the measurement as short as possible to prevent a distortion of the measured value 
by heating the conductor too much. Tested values are, e.g., 1 A for 0,35 mm² with a reading time 
for voltage under 2 s. 
The initial resistance is converted using the known length between the voltage taps to the length-
related resistance. 
Sp
a
I l
U
R T
⋅
) =
('
 
(eq. 14) 
U 
Measured voltage 
in V 
I 
Measured current 
in A 
ISp 
Distance between the voltage taps 
in m 
R'(Ta) Measured length-related resistance at  
room temperature Ta 
in Ω/m 
Next, this value pair is used to calibrate the conductor temperature measurement, in which the 
latest R'20 value from the rewritten (eq. 6) is calculated as follows, with the known temperature 
coefficients αρ and βρ. 


### 第 11 页
Page 11 of 21 
LV 112-3: 2011-12 
 
)²
20
(
)
20
(
1
)
('
'20
C
T
C
T
R T
R
a
a
a
°
−
⋅
+
°
−
⋅
+
=
ρ
ρ
β
α
 
(eq. 15) 
As an alternative to this approach, the value for R'20 can also be determined with a temperature-
compensated resistance measuring bridge, which can compensate for the conductor material 
used. (Use caution with alloys!) 
6.2.3 Measurement of the resistance change with various current supplies 
The current to be injected is always kept constant, but is increased in a number of steps (at 
least 10) suitable for the expected bend of the current-temperature curve until the conductor 
temperature TO +50 K is reached. Subsequently, it is returned to the zero value with the same 
steps. 
The voltage drop between the voltage taps is measured for each current step, and its average 
value determined after the dynamic transition status. The steady state is deemed to be reached 
when the measured voltage does not change by more than ±1% over a period of at least ~5 τ. 
τ must therefore be estimated beforehand using a theoretical computation. 
As an alternative, one can analyze the measured chronological resistance process parallel to the 
measurement, preferably in graphic form. A decision can then be made from this, whether the 
steady state has already been reached in sufficient quantities. 
The room temperature Ta slightly below the level of the cable suspension is always documented 
as well. 
Direct current (DC) must be used for the measurement. As an alternative, the actual shape of 
signal must be taken into account in the computations by suitable corrective factors. 
From the quotient R(I), derived from voltage U(I) and current I, taking into account the length 
between the voltage taps ISp 
( )
( )
I lSp
U I
R I
⋅
=
'
 
(eq. 16) 
the conductor temperature TL is calculated by solving (eq. 9) for ΔT as follows. Here, in many 
cases (in small quadratic coefficients βρ), only the linear coefficient αρcan be taken into account, in 
order to simplify the calculation: 
- 
By neglecting the quadratic temperature coefficient βρ: 
( )






−
⋅
=
= ∆
°
−
1
'
'
1
20
R 20
R I
T
C
TL
ρ
α
 
(eq. 17) 
- 
By taking into account both temperature coefficients αρ and βρ: 
( )
ρ
ρ
ρ
ρ
β
β
α
α
⋅






−
⋅
+ ⋅
+
−
=
= ∆
°
−
2
1
'
'
4
20
20
2
R
I
R
T
C
TL
 
(eq. 18) 


### 第 12 页
Page 12 of 21 
LV 112-3: 2011-12 
 
Since (eq.  18) can be too highly influenced by measuring errors due to quotients near zero, the 
root is replaced by the first three terms of the Taylor series: 
8
2
1
1
x2
x
x
−
≈ +
+
 
(eq. 19) 
which leads to the following more precise result with a small βρ: 
( )
( )














−
−
⋅





−
≈
= ∆
°
−
1
'
'
1
1
'
'
1
20
20
2
20
R
R I
R
R I
T
C
TL
ρ
ρ
ρ
α
β
α
 
(eq. 20) 
6.2.4 Determining the characteristic cable parameters a and b 
A table with (at least 8) current values and the assigned conductor temperatures arises as a result 
of the measurement and subsequent computation in section 6.2.3. 
The characteristic cable parameters a and b must be determined using suitable methods by 
"fitting" from the determined values. Only measured values for a conductor temperature of 
TO -50 K to TO +25 K must be taken into account in the process. 
When using the computational tool, the same temperature range must be taken into account. 
The functional association between (eq. 1) and (eq. 2) (see section 4.2.1) must be observed here. 
After determining the constants a and b, it is possible to calculate the required load current Izu 
from (eq. 1) and (eq. 2) with the specification of the ambient temperature Ta and the permissible 
conductor temperature TLzu: 
[
])
(
4
²
2
1
a
Lzu
zu
T
T
b
a
a
b
I
−
⋅ ⋅
+
+
⋅ −
⋅
=
 
(eq. 21) 
Note: By solving the quadratic equation (eq. 1), the "+" before the root expression must be used. 
6.2.5 Determining the time constant τ , dynamic heating characteristics 
The time constant τis preferably determined mathematically by means of the computatio-
nal tool [6]. These values can be confirmed by the measuring method described below in 
cases of doubt. 
Note: This measuring method will be more optimized in the next revision of LV 112-3. 
The time constant τ is measured in the measuring chamber at room temperature, where the 
conductor temperatures TLmin and TLmax are adjusted to the following default values, by entering 
load currents that were previously calculated with the help of (eq. 21) (see section 6.2.4): 
Conductor tempe-
rature TLmin 
Required current at 
Ta = RT 
Room temperature 
Conductor tempe-
rature 
TLmax 
Required current at 
Ta = RT 
Room temperature 
TO –50 K 
ITo-50 
TO +25 K 
IKu = ITo+25 
6.2.5.1. 
Determining the time constant τ 
The temperature of the conductor is monitored and recorded in the measurements described 
below by measuring the current and voltage, and determining the conductor resistance as per 
equation (eq. 17) or (eq. 20) (see section 6.2.3) 
The cable that is built on a measuring setup as per section 6.2.1 in a room at room temperature of 
around 20 °C and at the start of the measurement is loaded with the current ITo-50 until no change 
to the measured conductor temperature is any longer discerned. The conductor should then 
exhibit a temperature approximate to TO –50 K. 


### 第 13 页
Page 13 of 21 
LV 112-3: 2011-12 
 
Next, the cable to be measured is heated with a jump to the current IKu = To+25 as long as necessa-
ry until any change to the measured conductor temperature is no longer discerned. By loading 
with ITo+25, the temperature at the conductor ultimately arrives at TO +25 K. 
A time constant is determined by this procedure, which corresponds to a continuous load current 
approximate to IKU at Ta = TO –50 K; however, the measurement can take place here at room 
temperature. 
The periodic intervals between the individual recorded measured values must be selected such 
that at least 20 measured values are available for the dynamic transition area. The following time 
intervals have proven useful: 
Nominal cross section 
Time intervals 
<0,5 mm² 
1 s 
≤4 mm² 
3 s 
6 mm² to 10 mm² 
10 s 
≥10 mm² 
20 s 
The time constant τfrom the values determined by "fitting" must be determined. The functional 
association in the process as per (eq. 3) (see section 4.2.2) must be observed. 
Example:  Unscreened low-voltage cable with thin-walled PVC insulation (FLRY) 2,5-B, a = 0,25, 
b = 0,041, Ta =20 °C, τ = 60,6 s 
ITo -50K = 30,2 A; IKu = ITo+25K = 48,8; TO = 105 °C; TO –50K = 65 °C, TO +25K = 130 °C 
 
7. 
Documentation of the parameters to calculate the current capacity 
The cable manufacturers must enter the parameters defined in LV 112-3, supplement 1 for each 
cable, and provide them to the appropriate design engineering department. 
8. 
Application of the simplified description model 
8.1. Calculating the current capacity – steady behavior 
The permissible current capacity of a cable depends on the following parameters: 
- 
the ambient temperature to be expected 
- 
the maximum permissible long-term service temperature TO, depending on the cable class 
used or on the insulation material 
- 
the characteristic cable parameters of the cable used 


### 第 14 页
Page 14 of 21 
LV 112-3: 2011-12 
 
8.1.1 Temperature increase and voltage drop with specified current 
The conductor temperature during continuous loading results from (eq. 1) and (eq. 2) (see sec-
tion 4.2.1) with the specified ambient temperature Ta and load current I: 
2
)
( ,
bI
aI
T
I T
T
a
a
L
+
+
=
 
(eq. 22) 
Note: This formula yields a correct result only with positive current values! 
The associated voltage drop per 1 m results from (eq. 6) (see section 4.2.3) and (eq. 7) (see 
section 4.2.4) 
[
2 ]
20
20)
(
20)
(
1
'
( )
−
⋅
+
−
⋅
⋅ +
⋅
=
L
L
T
T
I R
E I
ρ
ρ
β
α
 
(eq. 23) 
In order to take into account the worst-case in voltage drop, the maximum permissible resistance 
for R'20 is taken as a basis from LV 112-1 for the respective cable. 
Example:  
FLRY 2,5-B, a = 0,25, b = 0,041, Ta = 65 °C,  
R20 = 7,6 mΩ/m, αρ = 3,81∙10-3 1/K, βρ = 6∙10-7 1/K2 
 
8.1.2 Permissible current depending on ambient temperature – derating 
The maximum permissible current must be determined such that, under continuous loading, 
taking into account the ambient temperature to be expected, the temperature at the conductor 
remains lower than the long-term service temperature TO. 
At maintained TO and varied ambient temperature Ta, the desired functional association for the 
permissible current also results from (eq. 1) and (eq. 2) (see section 4.2.1) 
Two representations are possible, where the same information can be derived from both conse-
quent diagrams. However, (eq. 24) or (eq. 25) are better suited for calculating a specific load case, 
depending on the problem. 
(eq. 23) is used again to calculate the voltage drops. 


### 第 15 页
Page 15 of 21 
LV 112-3: 2011-12 
 
 
8.1.2.1. 
Permissible ambient temperature with specified current 
2
)
( ,
bI
aI
T
I T
T
o
O
a
−
−
=
 
(eq. 24) 
Note: This formula yields a correct result only with positive current values! 
Example:  
FLRY 2,5-B, a = 0,25, b = 0,041, TO = 105 °C,  
R20 = 7,6 mΩ/m, αρ = 3,81∙10-3 1/K, βρ = 6∙10-7 1/K2 
 
8.1.2.2. 
Permissible current with specified ambient temperature 
[
])
(
4
²
2
1
)
,
(
a
O
O
a
T
T
b
a
a
b
I T T
−
⋅ ⋅
+
+
⋅ −
⋅
=
 
(eq. 25) 
Note: 
By solving the quadratic equation, the "+" before the root expression must be used. 
Example:  
FLRY 2,5-B, a = 0,25, b = 0,041, TO = 105 °C,  
R20 = 7,6 mΩ/m, αρ = 3,81∙10-3 1/K, βρ = 6∙10-7 1/K2 
 


### 第 16 页
Page 16 of 21 
LV 112-3: 2011-12 
 
 
8.2. Calculating the heating time – unsteady behavior 
The size of the time constant τis very highly dependent on the ambient temperature Ta, 
where τ is lower with a higher ambient temperature. If conductor temperature are estimated 
taking the dynamic transition behavior as a basis, lower values for τ must be selected to en-
sure additional certainty. 
8.2.1 Calculating the heating time with specified current 
With specified ambient temperature and permissible conductor temperature, the permissible 
continuous load current I (Ta, TO) arises from (eq. 25), at which the long-term service temperature 
of the cable TO is not exceeded. This equation can also be used for calculating any limit currents 
IGr, if a different limit temperature TGr is used instead of TO. 
From (eq. 26), taking into account the time constant τ for currents that are higher than the limit 
current IGr, the heating time tE can be calculated, which is required after an abrupt switching-on of 
the current I. This is so that at the ambient temperature Ta, starting from the no-current status, the 
conductor temperature TGr can be reached. 
)
(
²
²
ln
a
Gr
E
T
T
b I
a I
b I
a I
t
−
−
⋅
+
⋅
⋅
+
⋅
= τ ⋅
 
(eq. 26) 
As an alternative, one can determine the limit current IGr with (eq. 25) and calculate it with the 
simplified equation (eq. 27). 
2
2
2
ln
IGr
I
I
t
−
≈τ ⋅
 
(eq. 27) 
In the following example, the heating times for various load currents are presented in a diagram, 
where the short-term current IKU defined in section 5 is used as the limit current IGr. 
Example:  
FLRY 2,5-B, τ = 68 s, heating of Ta = 65 °C to TL = 130 °C,  
IGr = IKU
 = 39,8 A, Compare tE as per (eq. 26) or t as per. (eq. 27) 
 
The peak current IKu is the current at which exactly the short-term temperature TKu is reached at 
the conductor, with specified ambient temperature Ta in the steady borderline case. The curve in 
the diagram thus approaches the steady current IKu asymptotically. 
One can recognize well in the example, that the error when using the simplified (eq. 27) is only 
very low. 


### 第 17 页
Page 17 of 21 
LV 112-3: 2011-12 
 
8.2.2 Short-term behavior with load changes 
The chronological sequence in load changes can be calculated with the equations (eq. 3) , (eq. 4) , 
and (eq. 5). 
Example:  
FLRY 2,5-B, τ = 68 s, Ta = To-50 K = 55 °C,  
TLmin = 105 °C, TLmax = 155 °C, ID = 32,0 A, IKu = 46,4 A 
 
 
This example demonstrates the time behavior during short-term overload. Initially, the conductor 
has the permissible class temperature of TLmin = TO = 105 °C. This is reached at the selected 
ambient temperature Ta = TO –50 K = 55 °C by the derating current ID = 32,0 A. With increase to 
the overload current IÜL = 46,4 A, the conductor temperature approaches the short-term tempera-
ture TÜL = TO +50 K = 155 °C with the time constant τ = 68 s. However, the conductor temperature 
does not quite reach the short-term temperature, since the lower current is again supplied after 
300 s, whereby cooling to TO takes place with the same time constant. 
Note: Both currents ID and IÜL can be calculated with (eq. 25) (see section 8.1.2.2). 
8.2.3 Short-circuit derating 
(eq. 27) (see section 8.2.1) can be rewritten as follows, where also IGr is replaced by IÜL: 
τ
t
ÜL
e
I
I t
− −
⋅
≈
1
1
)
(
 
(eq. 28) 
The overload current IÜL has been defined in the preceding example, at which the temperature for 
the thermal overload test TÜL = TO +50 K, defined in LV 213-1, is not exceeded in the steady case. 
All cables must withstand this temperature for 6 h without incurring damage. 
Starting with this overload current IÜL, it can be calculated using (eq. 28), how long short-circuit 
currents must be present at maximum at the initially current-free conductor, to ensure that the 
thermal overload temperature TÜL is not exceeded. 


### 第 18 页
Page 18 of 21 
LV 112-3: 2011-12 
 
 
Example:  
FLRY 2,5-B, τ = 68 s,  Ta = TO –50 K = 55 °C,  
TÜL = TO +50 K = 155 °C, IÜL = 46,4 A 
 
8.2.4 Selection of fuses 
The guaranteed triggering time of the fuse to be used at the specified short-circuit current strength 
must be less than or equal to the times calculated in section 8.2.3, to ensure that the conductor 
temperature remains less than TÜL. 
In practice, low short-circuit currents are problematic above all, such as those occurring in cree-
ping short circuits, because many fuses then require a very long time to trigger. This means that 
the guaranteed triggering times are generally far longer than the time constant τ of the cables. 
When selecting the fuse, therefore, the thermal overload current IÜL of the cable must be observed 
above all. 
9. 
Outlook 
In addition to the single-wire cables described in this Standard, the following components are also 
used for supplying current to the electrical components in vehicles: 
1. Multi-wire cables 
2. Harnesses 
3. Fuses 
4. Current distributor boxes 
By using and further developing the simplified mathematical approach described in this document, 
standardized computation procedures are planned to be prepared in the future for these areas.  
As described in the references (see section 0), it is also possible to arrive at the characteristic 
parameters by computation using a few practical control measurements, if the physical material 
constants of the materials used are known. There is a computational tool (software package) [6] 
for this purpose, which is offered for sale. 


### 第 19 页
Page 19 of 21 
LV 112-3: 2011-12 
 
 
10. 
Appendix 
10.1. List of required measuring equipment (example) 
1. Test chamber with approx. 1,8 m³ space volume (see Figure 1) 
2. Power supply unit for current injection, manufactured by Fug,  
Type "Low Voltage Power Supply NTN 700M – 65" 
Input 
 
Mains connection, single-phase 
230 V ±10% 
 
47 to 63 Hz 
 
max. 6 A 
or 
115 V ±10% 
 
47 to 63 Hz 
 
max. 12 A 
Output 
 
Output current 
0 to 10 A 
Output voltage 
0 to 65 V 
Accuracy 
 
All data related to maximum value 
 
Residual ripple 
<1 x 10-4 ss + 
10 mVss 
Normal deviation of current or voltage 
At ±10% mains power deviation at idle state 
<1 x 10-5 
At ±10% mains power deviation at full throttle 
<2 x 10-4 
Over 8 h constant conditions 
<±1 x 10-4 
With temperature change 
<±1 x 10-4/K 
Absolute precision 
 
For all rated voltages 
<±0,2% of 
nominal value 
For all rated currents >5 mA to <10 A 
<±0,2% of 
nominal value 
3. High-current device, manufactured by Jovyatlas, custom build without type designation 
Input 
 
Mains connection, three-phase 
230 V ±10% 
 
47 to 63 Hz 
 
Max. 7 A 
Output 
 
Output current 
0 to 1 200 A 
Output voltage 
0 to 5,1 V 
Limits 
 
Current ripple  
0,3% max. 
Leap in desired value, current 
 
Readjustment time 0 to 1 200 A 
635 ms typ. 
 


### 第 20 页
Page 20 of 21 
LV 112-3: 2011-12 
 
4. Multimeter for current measurement, manufactured by HP, type HP 34401A 
 
24 h (23 ±1) °C 
24 h (23 ±1) °C 
 
Accuracy of reading 
Measuring range accuracy 
DC V 
% 
% 
100 mV 
±0,0030 
±0,0030 
1 V 
±0,0020 
±0,0006 
10 V 
±0,0015 
±0,0004 
DC I 
 
 
10 mA 
±0,005 
±0,01 
100 mA 
±0,010 
±0,004 
1 A 
±0,050 
±0,006 
3 A 
±0,100 
±0,020 
5. Multimeter for current measurement, manufactured by Keithley, type 2700, 
with data recording by LabView programming 
 
24 h (23 ±1) °C 
24 h (23 ±1) °C 
 
Accuracy of reading 
Measuring range accuracy 
DC V 
% 
% 
100 mV 
±0,0015 
±0,0030 
1 V 
±0,0015 
±0,0006 
10 V 
±0,0010 
±0,0004 
DC I 
 
 
20 mA 
±0,0060 
±0,0030 
100 mA 
±0,0100 
±0,0300 
1 A 
±0,0200 
±0,0030 
3 A 
±0,1000 
±0,0015 
6. Temperature measurement with thermopair TE type K, Testo data logger T4 for recording 
room temperature in derating box and conductor temperature 
 
24 h (23 ±1) °C 
 
 
Measuring range 
accuracy 
 
 
% 
 
Measuring instrument (-100 
to 70) °C 
±0,3 °C 
 
Thermopair with  
TE plug, flexible, length 
1 500 mm, filament glass 
yarn,  
TE type K 
±2,5 °C 
±0,0075•δ  
(referenced to  
temperature in °C) 
Sensor deviation measured 
at +80 °C 
±0,2 °C 
 
 


### 第 21 页
Page 21 of 21 
LV 112-3: 2011-12 
 
11. 
Normative references 
The following cited documents are required for application of this document. For dated references, 
only the referenced issue is valid. For undated references, the most recent issue of the referenced 
document (including all changes) is valid. 
 
LV 112-1 
Electric wiring in motor vehicles, copper cable; single-wire, unscreened 
LV 112-2 
Electric wiring in motor vehicles, aluminum cables; single-wire, unscreened 
 
12. 
Literature 
[1] 
VDI Heat Atlas, issuing body: Association of German Engineers (VDI), Berlin (Springer 
Verlag), 10th edited and expanded edition, 2006, ISBN 3-540-25504-4 
[2] 
Kabel und Leitungen für Starkstrom (Cables and lines for heavy current – only available in 
German), part 1, issuing body Lothar Heinhold, Berlin, Munich (Siemens AG), 4th revised 
edition 1987, ISBN 3-8009-1472-7 
[3] 
Bestimmung der Strombelastbarkeit von Fahrzeugleitungen (Determining the current-
loading capacity of vehicle cables – only available in German), H.-D. Ließ, Universität der 
Bundeswehr, Munich, 2010 
[4] 
Berechnungswerkzeug (Softwaretool) (Calculation tool (Software tool) – only available in 
German), H.-D. Ließ, Universität der Bundeswehr, Munich 

