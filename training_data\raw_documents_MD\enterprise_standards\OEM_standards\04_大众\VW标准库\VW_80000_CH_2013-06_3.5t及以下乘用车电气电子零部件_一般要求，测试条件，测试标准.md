# VW_80000_CH_2013-06_3.5t及以下乘用车电气电子零部件_一般要求，测试条件，测试标准.pdf

## 文档信息
- 标题：<4D6963726F736F667420576F7264202D2056572038303030305F43685F323031332D30362D30315F332E3574BCB0D2D4CFC2B3CBD3C3B3B5B5E7C6F8B5E7D7D3C1E3B2BFBCFEB5E7B2E2CAD4B1EAD7BC2E646F6378>
- 作者：yanyinfeng
- 页数：158

## 文档内容
### 第 1 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
VOLKSWAGEN   
股份公司 
集团标准                                                                                                                              VW80000 
                                                                                                                                      版本：2013‐06 
分类号：8MA00 
 关键词：元件、电气元件、电子元件、组件、测试条件、LV 124 
3.5t 以下乘用车的电气和电子元件 
一般要求，测试条件和测试 
 
前言 
备注 1：零部件相关要求与测试定义见 BT_设计任务书测试模块。 
 
备注 2：零部件 EMV 要求与测试见 BT_设计任务书 EMV 模块。 
 
此标准以当前的 LV 124 为基础，由以下汽车制造商 AUDI AG，BMW AG，Daimler AG，Porsche 
AG，Volkswagen AG 进行制订。 
此标准的扉页已经列出与 LV124 的差异部分。如果某个测试内容有个别修改，那么主管专业
部门和相应的生产商之间要协商确认。 
只要测试是按照 DIN EN ISO/IEC 17025 要求，委托独立的研究机构进行的，那么测试报告即
是被认可的。检测报告的认可不以自动释放为条件。 
 
以前版本： 
VW 
80101:1987‐06,1988‐08,1992‐01,1993‐04,1994‐05,1995‐06,1998‐01,1999‐06,2000‐09,2001‐04,2
003‐05,2004‐07,2005‐06,2006‐10,2009‐03;VW 80000:2009‐10 
 
变更 
与 VW 80000:2009‐10 相比作出如下变更： 
—将 LV 124 收录进 VW 80000 中 
 
标准在使用前进行更新检查。 
电子生成的标准是有效的，无需签字。 
专门负责部门 
EEIP/1    Dr.Torsten Polte  电话：+49 5361 9‐36035
I/EE‐61    Uwe Girgsdies  电话：+49 841 89‐90836
EEH4      Achim Henne    电话：+49 711 911‐88782
标准化部门 
 
EKDV/4    Dirk Beinker          EKDV 
电话：+49 5361 9‐32438 Manfred Terlinden
保密。版权所有。未经大众汽车股份公司标准部门许可禁止转发或复制。 
©  大众汽车股份公司 
 
 


### 第 2 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
 
 
 
 
 
 
  
LV 124 
  
3.5t 以下乘用车的电气和电子元件 
一般要求，测试条件和测试 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 3 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
变更记录： 
颁布日期 
 
2013‐02 
编辑更改 
 
第 1 部分  ‐ 12V 整车电源的电气要求和测试： 
基本修改  –  修改各项测试要求 
 
第 2 部分  –  环境要求和测试 
增加多种运行模式下的元件、与冷却液管路相关的元件，修改耐
久性测试 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 4 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
LV124 
目录 
第 1 部分—12V 整车电源的电气要求和测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐6 
1  适用范围‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐6 
2  标准参考‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐6 
3  术语和定义‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐6 
3.1  术语和缩写‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐6 
3.2  电压和电流‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐7 
3.3  温度‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐7 
3.4  时间‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐8 
3.5  内阻，接线柱标记，频率‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐8 
4  基本要求‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐8 
4.1  电压和电流‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐8 
4.2  温度说明‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐9 
4.3  标准公差‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐9 
4.4  标准值‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐9 
4.5  采样率和测量值分辨率‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐9 
4.6  测试电压‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐9 
4.7  工作电压范围和编码‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐10 
4.8  功能参数‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐10 
4.9  运行方式‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐12 
4.10  接口说明‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐13 
4.11  实施限制‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐13 
4.12  电气测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐14 
5 测试选择表‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐16 
6  电气要求和测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐18 
6.1 E‐01 长时间过载电压‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐18 
6.2 E‐02 瞬间过载电压‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐19 
6.3 E‐03 瞬间低电压‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐21 
6.4 E‐04 跨接启动‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐22 
6.5 E‐05 抛负载‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐23 
6.6 E‐06 叠加的交流电‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐24 
6.7 E‐07 电源电压缓慢降低和上升‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐26 
6.8 E‐08 电源电压缓慢降低快速上升‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐28 
6.9 E‐09 复位特性‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐30 
6.10 E‐10 短暂断路‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐32 
6.11 E‐11 启动脉冲‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐35 
6.12 E‐12 整车电源调节的电压曲线‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐39 
6.13 E‐13 Pin 脚断开‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐40 
6.14 E‐14 插头断开‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐42 
6.15 E‐15 反极性‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐43 
6.16 E‐16 接地错位‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐46 
6.17 E‐17 信号线和用电回路短路‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐47 
6.18 E‐18 绝缘电阻‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐49 


### 第 5 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.19 E‐19 静态电流‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐50 
6.20 E‐20 击穿强度‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐51 
6.21 E‐21 反馈‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐52 
6.22 E‐22 过载电流‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐54 
第 2 部分–  环境要求和测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐55 
7  适用范围‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐55 
8  标准参考‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐55 
9  术语和定义‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐56 
9.1  术语和缩写‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐56 
9.2  电压‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐57 
9.3  温度‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐57 
9.4  时间‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐57 
9.5  标准公差‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐57 
9.6  标准值‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐58 
10  基本要求‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐58 
10.1  运行模式‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐58 
10.2  运行方式‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐59 
10.3  温度设置  ‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐61 
10.4  参数测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐62 
10.5  应用迁移分析法的参数持续监控‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐63 
10.6  气密测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐64 
10.7  采样率和测量值分辨率‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐64 
11  使用属性‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐65 
11.1  使用寿命参数‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐65 
11.2  温度分布概况‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐65 
12  测试选择‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐67 
12.1  测试选择表‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐67 
12.2  测试流程计划‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐69 
13  机械要求和测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐70 
13.1 M‐01 跌落测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐70 
13.2 M‐02 石击测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐71 
13.3 M‐03 粉尘测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐72 
13.4 M‐04 振动测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐74 
13.5 M‐05 机械冲击‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐85 
13.6 M‐06 机械耐久冲击测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐86 
14 气候要求和测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐87 
14.1 K‐01 高/低温老化‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐87 
14.2 K‐02 温度分级测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐88 
14.3 K‐03 低温运行‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐89 
14.4 K‐04 重新喷漆温度‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐90 
14.5 K‐05 温度冲击（元件）‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐91 
14.6 K‐06 运行时外舱盐雾测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐92 
14.7 K‐07 运行时内舱盐雾测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐94 
14.8 K‐08 湿热循环‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐96 


### 第 6 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.9 K‐09 湿热循环（带冰冻）‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐97 
14.10 K‐10 防水– IPX0 至 IPX6K‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐98 
14.11 K‐11 高压/蒸汽喷射清洗‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐99 
14.12 K‐12 溅水温度冲击‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐100 
14.13 K‐13 浸水温度冲击‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐103 
14.14 K‐14 恒湿恒温‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐104 
14.15 K‐15 冷凝和气候测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐108 
14.16 K‐16 温度冲击（不带外壳）‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐115 
14.17 K‐17 日照‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐116 
14.18 K‐18 有害气体测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐117 
15  化学要求和测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐118 
15.1 C‐01  化学测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐118 
16 耐久测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐121 
16.1 L‐01 机械/液压耐久测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐121 
16.2 L‐02 高温耐久测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐122 
16.3 L‐03 温度变化耐久测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐125 
附录 A（标准）测试流程‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐129 
A.1  测试流程计划‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐129 
A.2  顺序测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐130 
A.3  非顺序测试（平行测试）‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐132 
A.4  耐久测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐133 
附录 B（标准）不同安装区域的常用温度集‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐134 
B.1  温度集 1‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐135 
B.2  温度集 2‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐135 
B.3  温度集 3‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐135 
B.4  温度集 4‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐135 
附录 C（标准）高温耐久测试的计算模型‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐136 
C.1 Arrhenius‐模型‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐136 
C.2 Arrhenius‐模型举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐137 
C.3 高温下性能降低的 Arrhenius‐模型应用‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐138 
C.4 高温下性能降低的 Arrhenius‐模型应用举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐139 
C.5 冷却液循环系统元件的 Arrhenius‐模型应用‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐141 
C.6 冷却液循环系统元件的 Arrhenius‐模型应用举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐145 
附录 D（标准）温度变化耐久测试的计算模型‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐148 
D.1 Coffin‐Manson‐模型‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐148 
D.2 举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐149 
D.3 冷却液循环系统元件的 Coffin‐Manson‐模型应用‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐150 
D.4 冷却液循环系统元件的 Coffin‐Manson‐模型应用举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐153 
附录 E（标准）等级为 2 的恒湿恒温测试的计算模型‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐155 
E.1 Lawson‐模型‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐155 
E.2  举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐156 
附录 F（参考）冷凝测试，测试箱设置和图表‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐157 
附录 G（参考）物理分析方法举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐160 
 


### 第 7 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
第 1 部分  – 12V 整车电源的电气要求和测试 
1  适用范围 
此标准规定了 12V 整车电源的电气、电子、机械元件和系统的要求、测试条件和测试内容。
如无其他规定，不需要进行电气耐久测试。 
 
附加或者偏差要求、测试条件与测试见相应的设计任务书。 
 
注意：标准中涉及到的测试是用于对零件要求的特性进行测试，不能用来作为零件认可或生
产过程认可的证明。 
 
2  标准参考 
表 1：标准参考 
ANSI/UL94 
设备和器具上的塑料件易燃性安全测试标准 
DIN 72552‐2 
汽车上接线柱标记的含义 
DIN EN 13018 
无损伤测试—外观检查—基本要求 
DIN EN ISO/IEC 17025 
实验室测试和校准能力的基本要求 
 
3  术语和定义 
3.1  术语和缩写 
表 2：电气要求和测试的缩写 
术语/缩写 
含义 
元件/组件 
电气，电子或者机电一体元件（如：电阻，电容，晶体管 
IC，继电器） 
DUT 
被测器件—见试样 
功能 
包括系统相关的功能和诊断功能 
ICT 
电路内测试（线路内逐个元件测试） 
零部件 
完整的设备，控制器或者机电一体化（带塑壳） 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 8 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
短路 
负载输出的短路是指，低电阻的工作负载至电阻值为 0Ω，缓
慢的短路连接，即电流接近短路。 
短路可以长期存在（零部件运行/不运行） 
入网停车 
汽车运行可选模式，停车时与充电站/插座连接，但是不充电。
一般车辆是可以与充电站相连的 
离网停车 
汽车运行可选模式，停车时不与充电站/插座连接 
高级用户（Power‐User） 
实际运用情况最大化 
试样 
被测试的系统或者零件 
PTB 
德国联邦技术物理研究所 
启动相关 
发动机启动直接或间接相关的元件 
系统 
功能链接的元件，如：刹车系统（控制器，液压系统，传感器）
 
3.2  电压和电流 
表 3：电压和电流缩写 
UN 
额定电压 
UBmin 
最低工作电压 
UB 
工作电压 
UBmax 
最高工作电压 
Umax 
测试期间能够产生的最高电压 
Umin 
测试期间能够产生的最低电压 
UPP 
峰间电压 
Ueff 
电压有效值 
Utest 
测试电压 
IN 
额定电流 
GND 
设备接地 
UA UT US UR 
启动电压脉冲的电压等级 
 
3.3  温度 
表 4：温度缩写 
Tmin 
最低工作温度 
TRT 
室温 
Tmax 
最高工作温度 
Ttest 
测试温度 
 
 
 
 
 
 
 
 
 
 


### 第 9 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
3.4  时间 
表 5：时间缩写 
tr 
上升时间（如：电压曲线） 
tf 
下降时间（如：电压曲线） 
 
3.5  内阻，接线柱标记，频率 
表格：电阻，接线柱和频率的缩写 
Ri 
包括供电线束和电源的内阻（见图 1：内阻） 
接线柱标记 
依据 DIN 72552‐2 
F 
频率 
 
4  基本要求 
4.1  电压和电流 
电压曲线可以理解为包络曲线。实际的电压曲线为规定的测试和参考曲线之间的任意曲线。 
 
所有电压和电流说明都与元件（接线柱）有关。这不适用于内阻 Ri 要详细说明的测试。在
这种情况下，电压和电流数据与电源有关。（见图 1：内阻） 
 
插图说明 
Us      电源 
RL      线束和接插件电阻 
Ri      电源方向的元件接线柱内阻 
图 1：内阻 
 
所有边缘说明条件为电压值的 10%或 90%。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 10 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
4.2  温度说明 
如果没有其他说明，则所有温度参数参照试样的周围环境温度。 
 
4.3  标准公差 
如果没有其他说明，公差见表 7。 
公差适用于需要的测量值。 
表 7：标准公差 
频率 
±1% 
温度 
±2℃ 
空气湿度 
±5% 
时间 
＋5%；0% 
电压 
±2% 
电流 
±2% 
 
4.4  标准值 
如果没有其他说明，标准值见表 8。 
表 8：标准值 
室温 
TRT=23℃±5℃ 
空气湿度 
Frel=25% ‐ 75%相对湿度 
测试温度 
Ttest=TRT 
工作电压（用于测试） 
UB=14V 
 
4.5  采样率和测量值分辨率 
测量系统的采样率或者带宽视各自测试而定。所有测量值必须记录最大值（峰值）。 
 
测量值分辨率必须与各自的测试相符合。必须确保，产生的电压峰值不能超程，或者由于过
低不能测量。数据简化/概念（如：极限值监控，Bus 信息评估）不能抑制异常性。 
 
4.6  测试电压 
测试电压尤其是用于过载和过低电压测试的电压，可以与章节 4.7 的工作电压范围有偏差，
可以分别规定。 
元件适用的电压范围必须随时满足功能参数 A（见章节 4.8）的要求。 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 11 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
4.7  工作电压范围和编码 
表 9：工作电压范围 
编码 
UBmin 
UBmax 
描述 
a 
6V 
16V 
启动过程中必须保持的功能 
b 
8V 
16V 
启动过程中不需要保持的功能 
此编码仅用于当元件不能编入 a,c,d 编码时 
c 
9V 
16V 
“电机关闭”时必须保持的功能 
d 
9.8V 
16V 
“电机运行”时必须保持的功能 
 
4.8  功能参数 
4.8.1  概况 
此章节描述了测试过程中和测试完成后样件的功能状态，规定了每个测试中测试件的功能参
数。 
功能参数中元件的功能特性（含温度电压范围的降额）以及客户感觉（如：视觉，声觉，触
觉，热觉）是由委托方在图纸或者设计任务书中进行定义的。 
 
在所有情况下功能参数 A 必须随时保持储存功能。必须随时确保非瞬间存储的统一性。设
计任务书中规定了功能参数的时间过程。允许的故障存储记录必须与委托方协商确认。 
 
功能参数 A‐D 时不允许损坏测试件，测试件使用的电气/电子元件不允许超过数据库详细规
定的允许极限值，必须至少依据章节 4.12.2 的参数测试（小）进行证明。 
 
4.8.2  功能参数 A 
在测试参数的加载过程中和加载之后，试验件必须满足参数测试的所有功能要求。 
 
4.8.3  功能参数 B 
在测试参数的加载过程中，试验件必须满足参数测试的所有功能要求，然而允许一个或者多
个功能超出规定的公差。测试参数的加载结束后，试验件必须又自动达到功能参数 A。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 12 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
4.8.4  功能参数 C 
在测试参数的加载过程中，试验件有一个或多个功能要求不能满足。测试参数的加载结束后，
试验件必须又自动达到功能参数 A。未定义的功能在任何时刻都不允许出现。 
 
4.8.5  功能参数 D 
在测试参数的加载过程中，试验件有一个或多个功能要求不能满足。测试参数加载结束后，
试验件必须能够通过更换接线柱、重设或者简单的动作（如：更换损坏的保险丝）再次达到
功能参数 A。未定义的功能在任何时刻都不允许出现。 
 
4.8.6  功能参数 E 
在测试参数的加载过程中，试验件有一个或多个功能要求不能满足，则必须在测试参数加载
结束后进行修复或者更换。 
测试件必须满足 UL94‐v0 的阻燃性要求。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 13 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
4.9  运行方式 
4.9.1  概况 
电气、电子和机电一体的元件和系统在使用周期内以不同的工作方式运行，必须进行测试模
拟。工作方式，工作负荷（如：控制，总线驱动，总线报文，原始传感器，原始执行器或者
备用线路）和要求的边界条件等详细情况必须由受托方和委托方进行协商确认和记录。 
 
4.9.2  运行方式Ⅰ‐试验件不电气连接 
4.9.2.1  运行方式Ⅰ.a 
试验件无电流通过，无插头和线束。 
现有的冷却液循环未注满，接头处密封。 
 
4.9.2.2 运行方式Ⅰ.b 
试验件无电流通过，但是带有接通的插头和线束。 
现有的冷却液循环被注满，冷却液软管连接。 
 
4.9.3  运行方式Ⅱ  ‐试验件电气连接 
4.9.3.1  运行方式Ⅱ.a 
试验件无工作负载运行 
现有的冷却液循环注满，冷却液软管连接。冷却介质的流量和温度根据设计任务书的要求进
行确认和调节。 
4.9.3.2  运行方式Ⅱ.b 
试验件以最小的工作负载运行。 
试验件运行中必须产生最小的自热（如：通过持续减少功率输出或者通过减少外部负载触发）。 
现有的冷却液循环注满，冷却液软管连接。根据设计任务书的规定调节冷却介质的流量和温
度。 
4.9.3.3  运行方式Ⅱ.c 
试验件以最大的工作负载运行（高级用户 Power‐User，但是不允许不当行为）。 
试验件运行中必须产生最大的自热（如：通过持续的实际最大功率输出或者频繁的外部负载
触发） 
现有的冷却液循环注满，冷却液软管连接。冷却液的流量和温度根据需求如设计任务书进行
确认和调节。 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 14 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
4.9.3.4  运行方式举例 
表 10：运行方式举例 
元件举例 
运行方式Ⅱ.a 
运行方式Ⅱ.b 
运行方式Ⅱ.c 
带有导航的汽车
收音机 
停 车 状 态 的 元 件
（睡眠）空载结束，
打开 KL30   
汽车行驶状态的元件。
司 机 关 闭 元 件 ， 激 活
BUS/μC’s，打开 KL15   
汽车行驶状态的元件。
启动元件（CD，导航，
放大器），激活 BUS/导
航仪 
防盗报警装置 
汽车运行无此功能 
监视停车状态的汽车内部 
制动调节系统 
停车状态的元件，
空载结束 
行驶中不操作刹车系统 
行驶中多次循环使用刹
车系统（不允许不当行
为，如：不间断的刹车
操作） 
车载充电器 
离网（OFF‐Grid）停
车或者汽车运行 
入网（ON‐Grid）停车（仅
电力载波通信，不充电）
汽车调节 
充电 
高压‐蓄电池（蓄
电池管理系统） 
离网（OFF‐Grid）驻
车 
带有电力载波通信 
的入网（ON‐Grid）停车
汽车运行，充电 
 
4.10  接口说明 
所有接口必须有完整的状态和电气特性记录。这些记录作为测试结果评估的基础，并且必须
进行相应的细化。 
 
4.11  实施限制 
测试实验室必须依据 DIN EN ISO/IEC 17025 进行组织和运行。所有测量器材都必须依据 DIN 
EN ISO/IEC 17025 进行校准（或者通过生产商确认或建议），由 PTB 或者其他的等效的国家级
标准实验室进行测试。使用的检测仪器，生产设备，装配和测试过程不允许限制/歪曲测试
件特性（如：电流输入）。将这些和校准精确度和测试日期记录在检测报告中。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 15 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
4.12  电气测试 
4.12.1  测试过程 
当测试件完全启动并处于功能参数 A 时，开始电气测试。 
 
电气测试的顺序是可以自由选择的，每次测试需要确认允许的缺陷存储记录以及元件的功能
参数。 
 
如果章节 5 的测试选择表没有明确规定，则执行所有的测试。 
 
只要不违反电气测试要求且委托方同意，则允许在环境测试（见 LV124 第 2 部分）期间进行
电气测试。如果测试件在组合测试中出现异常的，则需单独重复这些测试。 
 
设计任务书中或者与委托方协商中会定义敏感参数，也叫关键参数，如：静态电流消耗，工
作电流，输出电压，过渡电阻，输入阻抗，信号速率（上升时间和下降时间），总线规范。
每次测试开始前和测试结束后，都要检测这些参数是否符合标准要求。 
 
每次测试期间，必须详细记录监控的关键参数。元件的重新设置应以合适的形式进行监控和
记录。 
 
每次测试前和测试后，应按照设计任务书章节 4.12.2 进行参数测试（小）。 
 
第一次电气测试前和最后一次测试后，应按照设计任务书章节 4.12.3 进行参数测试（大）。 
 
预先测试和事后测试的测量结果和数据的允许公差必须在规定的范围内。测量结果的变化大
于测量精度时要做标记。必须从测量结果来研究趋势和功能变化，以识别异常、老化或者元
件缺陷功能。 
 
所有测试结束后至少要在 1 个样件上进行章节 4.12.4 的物理分析。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 16 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
4.12.2  参数测试（小） 
测量关键参数，检查零件在 TRT 和 UB 的功能特性。带有故障存储功能的元件必须能够读取故
障存储内容。在不拆解测试件的情况下，依据 DIN EN 13018 对元件外部缺陷/变化如：裂纹，
开裂/脱落，褪色，变形等进行外观检查。 
测试人员必须将测试获得的关键参数值，功能特性或者故障存储记录的变化，包括外观检查
中的异常情况与新状态的参数进行对比评估。 
 
所有测试结果都必须在检测报告上进行记录。 
 
4.12.3  参数测试（大） 
测量关键参数，分别测量电压在 UBmin,UB,UBmax 时的 Tmax,TRT,Tmin 下的零件功能特性。 
带有故障存储功能的元件必须能够读取故障存储内容。在不拆解测试件的情况下，依据 DIN 
EN 13018 对元件外部缺陷/变化如：裂纹，开裂/脱落，褪色，变形等进行外观检查。 
 
测试人员必须将测试获得的关键参数值，功能特性或者故障存储记录的变化，包括外观检查
中的异常情况与新状态的参数进行对比评估。 
 
所有测试结果都必须在检测报告中进行记录。 
 
4.12.4  物理分析 
物理分析时，必须将试验件进行拆解，并依据 DIN EN 13018 进行外观检查。 
 
附加分析由委托方和受托方共同协商确认，分析案例参见附件 G。 
 
必须对元件与新状态之间的变化进行评估。若测试件显示异常时，后续必须尽可能增加更多
的测试件或者使用附加分析方法，与委托方一起进行分析确认。 
 
所有测试结果都必须在检测报告上进行记录。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 17 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
5 测试选择表 
表 11：测试选择表 
测试 
应用零件 
委托方附加规定 
E‐01 长时间过载电压 
系统提供大于 12V 电中的元件 
汽车运行时 
必须的元件 
E‐02 瞬间过载电压 
系统提供大于 12V 电中的元件 
无 
E‐03 瞬间低电压 
系统提供大于 12V 电中的元件 
无 
E‐04 迅速启动 
系统提供大于 12V 电中的元件 
启动相关/启动非相关
元件 
E‐05 抛负载 
系统提供大于 12V 电中的元件 
安全相关的元件 
E‐06 叠加交流电 
系统提供大于 12V 电中的元件 
整车电路连接为基础
的测试 
E‐07 供电电压缓慢下降和上升 
所有元件 
相关接线柱 KL 电状态
E‐08 供电电压缓慢下降和快速上升
所有元件 
相关接线柱 KL 电状态
E‐09 复位特性 
所有元件 
相关接线柱 KL 电状态，
测试边缘条件 
E‐10 短暂断路 
所有元件 
无 
E‐11 起动脉冲 
系统提供大于 12V 电中的元件 
起动相关/非相关元件 
E‐12 整车电源调节的电压曲线 
系统提供大于 12V 电中的元件 
无 
E‐13 Pin 脚断开 
所有元件 
相关接线柱 KL 电状态
E‐14 插头断开 
所有元件 
无 
E‐15 反极性 
车内能够产生反极性的元件 
锐度（清晰度），反极
性时切断元件 
E‐16 搭铁偏差 
所有元件 
无 
E‐17 信号线和电源线短路 
所有元件 
无 
E‐18 绝缘电阻 
电镀元件 
无 
E‐19 静态电流 
持续提供电压的元件（如：Kl.30, 
Kl.30f, Kl.30g…） 
无 
E‐20 击穿强度 
带有电感零件的元件，（如：发
动机，继电器，线圈） 
无 
E‐21 反馈 
电气上与 Kl.15 或者其他接线柱
连接的带有唤醒功能的元件 
无 
E‐22 过载电流 
带有输出端的元件 
无 
 
 
 
 
 
 
 
 
 
 
 


### 第 18 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6  电气要求和测试 
6.1 E‐01 长时间过载电压 
6.1.1  目的 
测试元件长时间过载电压下的耐久性，模拟汽车运行中发电机调节失效。 
6.1.2  测试 
表 12：E‐01 长时间过载电压测试参数 
试验件的运行方式 
运行方式Ⅱ.c 
Umax 
17V  （+4%，0%） 
Umin 
13.5V   
tr 
＜10ms 
tf 
＜10ms 
t1 
60min 
Ttest 
Tmax ‐ 20K 
循环次数 
1 
测试件数量 
至少 6 个 
 
图 2：E‐01 长时间过载电压测试脉冲 
6.1.3  要求 
测试结果的评估与使用的元件有关。区别是： 
a） 汽车运行所必需的元件： 
功能参数 B 
可能会定义应急方式，相应的“降额策略”在设计任务书中查找。 
b） 其他所有元件： 
功能参数 C 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 19 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.2 E‐02 瞬间过载电压 
6.2.1 目的 
由于关闭用电器和短时间的气体脉冲（挂档）可以在整车电路中产生瞬间过载电压。该过载
电压可以通过测试模拟实现。 
 
6.2.2 测试 
表 13：E‐02 瞬间过载电压测试参数 
测试件的运行方式 
运行方式Ⅱ.c 
Umin 
16V 
U1 
17V 
Umax 
18V（+4%，0%） 
tr 
1ms 
tf 
1ms 
t1 
400ms 
t2 
600ms 
测试件数量 
至少 6 个 
测试情况 1 
 
Ttest 
Tmax 
循环次数 
3 
t3 
2s 
测试情况 2 
 
Ttest 
Tmin 
循环次数 
3 
t3 
2s 
测试情况 3 
 
Ttest 
TRT 
循环次数 
100 
t3 
8s 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 20 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
 
 
6.2.3 要求 
功能参数 A 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
电
压 
图 3：E‐02 瞬间过载电压测试脉冲 
时间 


### 第 21 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.3 E‐03 瞬间低电压 
6.3.1 目的 
由于启动用电器就可以在整车电路中产生瞬间低电压。此低电压可以通过测试模拟实现。 
 
6.3.2 测试 
表 14：E‐03 瞬间低电压测试参数 
测试件的运行方式 
运行方式Ⅱ.c 
Umax 
10.8V（+4%，0%） 
Umin 
9V（0%，‐4%） 
tr 
1.8ms 
tf 
1.8ms 
t1 
500ms 
t2 
1s 
测试件数量 
至少 6 个 
测试情况 1 
 
Ttest 
Tmax 
循环次数 
3 
测试情况 2 
 
Ttest 
Tmin 
循环次数 
3 
 
 
 
 
 
6.3.3 要求 
功能状态参数 A 
 
 
 
 
 
电
压 
时间
图 4：E‐03 瞬间低电压测试脉冲 


### 第 22 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.4 E‐04 快速启动 
6.4.1 目的 
模拟汽车外部启动。最大测试电压由汽车系统和上升的整车电压产生的。 
 
6.4.2 测试 
表 15：E‐04 快速启动测试参数 
测试件的运行方式 
运行方式Ⅱ.c 
Umin 
10.8V 
Umax 
26V（+4%，0%） 
t1 
60s 
tr 
＜10ms 
tf 
＜10ms 
循环次数 
1 
测试件数量 
至少 6 个 
 
 
6.4.3 要求 
分成 2 类： 
a) 起动相关元件（如：起动机）： 
功能参数 B 
整个时间内传感器必须提供有效值（或通过元件中的替代表格来保障） 
 
b) 其他所有元件： 
功能参数 C 
 
 
 
 
 
 
 
 
 
电
压 
时间
图 5：E‐04 跨接启动测试脉冲


### 第 23 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.5 E‐05 抛负载 
6.5.1 目的 
有简化缓冲能力蓄电池连接的电气抛负载时，由于发电机特性会造成高能量的过载电压脉冲，
此脉冲可以通过测试进行模拟。 
 
6.5.2 测试 
表 16：E‐05 抛负载测试参数 
测试件的运行方式 
运行方式Ⅱ.c 
Umin 
13.5V 
Umax 
27V（+4%，0%）   
tr 
≤2ms 
t1 
300ms 
tf 
≤30ms 
循环间隔 
1min 
循环次数 
10 
测试件数量 
至少 6 个 
 
 
 
6.5.3 要求 
分成 2 类： 
a) 安全相关的元件： 
功能参数 B 
 
b) 其他所有元件： 
功能参数 C 
 
 
 
 
 
电
压 
时间
图 6：E‐05 抛负载测试脉冲 


### 第 24 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.6 E‐06 叠加交流电 
6.6.1 目的 
整车电路的交流电压可以叠加的。叠加的交流电压可以存在于整个发动机运转期间。这种情
况可以进行模拟测试。 
 
6.6.2 测试 
表 17：E‐06 叠加交流电测试参数 
测试件运行方式 
运行方式Ⅱ.c 
Umax 
UBmax 
Ri 
≤100mΩ 
频率范围 
15Hz – 30kHz 
摆 动 周 期 ( 频 率 扫 描 时
长)t1 
2min 
摆动方式（频率扫描方
式） 
三角形对数 
循环次数 
15 
测试件数量 
至少 6 个 
测试情况 1 
 
Upp 
2V（+4%，0%） 
测试情况 2 
 
Upp 
3V（+4%，0%） 
仅用于蓄电池和发电机间的元件，尤其是远离发电机端的蓄电池
连接 
测试情况 3 
 
Upp 
6V（+4%，0%） 
行车模式下的所有元件（不含蓄电池‐应急方式）或靠近发电机
端的连接 
 
 
 
 
 
 
电
压 
图 7：E‐06 叠加的交流电压测试脉冲 


### 第 25 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.6.2.1 测试装置 
整车电路性能需与专业部门协商一致。必须详细注明测试装置包括导线电感、容量和电阻。 
 
6.6.3  要求 
 
测试情况 1：功能参数 A 
测试情况 2：功能参数 A 
测试情况 3： 
a) 汽车运行所必需的元件： 
功能参数 A 
b) 其他所有元件： 
功能参数 B 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 26 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.7 E‐07 电源电压缓慢降低和上升 
6.7.1 目的 
模拟电源电压缓慢降低和提升，即车辆蓄电池缓慢充电和放电过程。 
 
6.7.2 测试 
表 18：E‐07 电压缓慢降低和上升测试参数 
测试件运行方式 
运行方式Ⅱ.a 和Ⅱ.c 
 
所有相关 KL 电状态（如 KL.15/KL.30/KL.87…）及其组合都要
进行测试。 
起动电压 
UBmax（+4%，0%） 
电压变化速度 
0.5V/min（+10%，‐10%） 
U1 
UBmin 
t1 
U1 时间要足够直到故障存储记录全部读取出来为止 
最小电压 
0V 
U2 
UBmin 
t2 
U2 时间要足够直到故障存储记录全部读取出来为止 
最终电压 
UBmax（+4%，0%） 
循环次数 
每个 KL 电及其组合： 
运行方式Ⅱ.a 循环一次 
运行方式Ⅱ.c 循环一次 
测试件数量 
至少 6 个 
 
 
图 8：E‐07 电压缓慢降低和上升的测试脉冲 
 
 
 
 
 
 
 


### 第 27 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.7.3 要求 
测试结果的评估与零部件测试电压范围有关。 
 
分成 2 类： 
a） 元件电压在规定的工作电压内的： 
功能参数 A 
 
b） 元件电压超出规定的工作电压外的： 
功能参数 C 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 28 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.8 E‐08 电源电压缓慢降低快速上升 
6.8.1 目的 
此测试模拟蓄电池电压缓慢降至 0V，然后通过外部启动突然上升蓄电池电压。 
 
6.8.2 测试 
表 19：E‐08 电源电压缓慢降低快速上升 
测试件运行方式 
运行方式Ⅱ.a 和Ⅱ.c 
 
所有相关 KL 电状态（如 KL.15/KL.30/KL.87…）及其组合都要
进行测试。 
起动电压 
UBmax（+4%，0%） 
电压降 
0.5V/min（+10%，‐10%） 
U1 
UBmin 
t1 
U1 时间要足够直到故障存储记录全部读取出来为止 
最小电压 
0V 
t2 
至少 1min,但要直至内部容量全部卸放掉 
最终电压 
UBmax（+4%，0%） 
tr 
≤0.5s 
循环次数 
每个 KL 电及其组合： 
运行方式Ⅱ.a 循环一次 
运行方式Ⅱ.c 循环一次 
测试件数量 
至少 6 个 
 
 
图 9：E‐08 电源电压缓慢降低快速上升的测试脉冲 
 
 
 
 
 
 
 


### 第 29 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.8.3 要求 
 
测试结果的评估与零部件测试电压范围有关。 
 
分成 2 类： 
c） 元件电压在规定的工作电压内的： 
功能参数 A 
 
d） 元件电压超出规定的工作电压外的： 
功能参数 C 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 30 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.9 E‐09 复位特性 
6.9.1 目的 
检查零部件在其使用环境下的复位性能，测试边界条件（如：连接，KL 电，系统）必须详
细描述。 
运行中如果出现任意时间的重复的接通/断开测试，不允许对元件造成未定义的特性。复位
特性反映了电压变化和时间变化。为了模拟不同的断开时间，要求进行两个不同的测试过程。
一个元件要进行两个测试流程。 
 
6.9.2 测试 
表 20：E‐09 复位特性测试参数 
测试件运行方式 
运行方式Ⅱ.a 和Ⅱ.c 
 
所有相关 KL 电状态（如 KL.15/KL.30/KL.87…）及其组合都要
进行测试。 
Umax 
UBmin（0%，‐4%） 
Uth 
6V 
△U1（范围 Umax 至 Uth） 
0.5V 
△U2（范围 Uth 至 0V） 
0.2V 
t2   
至少≥10s，直到测试件 100%达到运行能力（所有系统再次
无故障高负荷运转） 
tr 
≤10ms 
tf 
≤10ms 
循环次数 
每个 KL 电及其组合的各自测试流程： 
运行方式Ⅱ.a 循环一次 
运行方式Ⅱ.c 循环一次 
测试件数量 
至少 6 个 
测试情况 1 
t1 
5s 
测试情况 2 
t1 
100ms 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 31 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
图 10：E‐09 复位特性测试脉冲 
 
6.9.3 要求 
功能参数 A 再次达到 Umax 
 
不允许出现未定义的运行状态. 
 
需证明满足规定要求，并确定从哪个电压值开始零件首次偏离功能参数 A. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
   


### 第 32 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.10 E‐10 短暂断路 
6.10.1 目的 
模拟短暂断路时不同时间下的元件特性. 
测试情况 1 模拟中断零件电源电压。 
测试情况 2 模拟整车电源电压中断。 
电压中断可通过端子‐导线错误或继电器振动实现。 
 
6.10.2 测试 
表格 21：E‐10 短暂断路测试参数 
测试件运行方式 
运行方式Ⅱ.c 
Utest 
11V 
Z1 
关闭 S1 
Z2 
打开 S1 
tr 
≤（0.1*t1） 
tf 
≤（0.1*t1） 
S1 开关的开关顺序 
t1 
步骤 
10μs 至 100μs 
10μs 
100μs 至 1ms 
100μs 
1ms 至 10ms 
1ms 
10ms 至 100ms 
10ms 
100ms 至 2s 
100ms 
t2 
＞10s 
Utest  测试电压必须保持至测试件达到 100%运行能力 
循环次数 
1 
测试件数量 
至少 6 个 
测试情况 1 
接通 S1,静态打开 S2 
测试情况 2 
接通 S1,S2 负极连接到 S1(?) 
 
电压断路时间按照表 21 的步骤逐渐延长，生成的曲线图见图 11. 
 
测试件电压可以通过测试装置限制到 E‐05 抛负载测试（见章节 6.5）的最大电压。 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 33 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
图 11：S1 开关的 E‐10 短暂断路变化 
6.10.2.1  测试装置 
 
图 12：E‐10 短暂断路电路原理 
开关 S2 断开时，含必需导线的电阻需低于 100mΩ。 
 
6.10.2.2  测试流程 
用 100Ω（±5%）和 1Ω（±5%）作为测试替代件进行参照测量并记录存档。该测试是用于
验证陡度要求。低电感零件可以作为电阻使用。 
然后按照表 21 进行测试。 
 
 
 
 
 
 
 
测试件 


### 第 34 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.10.3 要求 
t1＜100μs 的：功能参数 A 
t1≥100μs 的：功能参数 C 
 
必须确定测试件从 t1 的哪个时间点起第一次偏离功能参数 A。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 35 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.11 E‐11 启动脉冲 
 
6.11.1 目的 
起动时（发动机启动），蓄电池电压在很短时间内下降至最低值，然后缓慢上升。大部分元
件在起动前已经运转，然后在起动期间停止运转，紧接着在起动结束后发动机运转期间再次
运行。通过该测试来模拟零部件在启动相关的电压干扰下的性能。 
起动过程分为两种类型，冷启动和热启动（启停后自动再启动）。为了覆盖这两种情况，需
进行两种测试。一个元件需进行两个测试流程。 
 
6.11.2 测试 
表 22：E‐11 起动脉冲测试参数 
测试件运行方式 
运行方式Ⅱ.a, Ⅱ.b 和Ⅱ.c 
有时每个运行方式还会规定其他运行负载。 
测试脉冲 
‐ 
冷启动：按照表 23，测试脉冲“正常”和“剧烈” 
‐ 
热启动：按照表 24，测试脉冲“短”和“长” 
测试件数量 
至少 6 个 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 36 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.11.2.1 测试情况 1‐冷启动 
表 23：E‐11 起动脉冲测试参数 
参数 
测试脉冲“正常” 
测试脉冲“剧烈” 
UB 
11.0V 
11.0V 
UT 
4.5V（0%，‐4%） 
3.2+0.2V 
US 
4.5V（0%，‐4%） 
5.0V（0%，‐4%） 
UA 
6.5V（0%，‐4%） 
6.0V（0%，‐4%） 
UR 
2V 
2V 
tf 
≤1ms 
≤1ms 
T4 
0ms 
19ms 
T5 
0ms 
≤1ms 
T6 
19ms 
329ms 
T7 
50ms 
50ms 
T8 
10s 
10s 
tr 
100ms 
100ms 
f 
2Hz 
2Hz 
循环间隔 
2s 
2s 
测试循环次数 
10 
10 
 
插图说明 
a  关闭 KL.50 
b  打开 KL.50 
c  关闭 KL.50 
ttest            循环 
图 13：冷启动测试脉冲 
 
 
 
 


### 第 37 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.11.2.2 测试情况 2 ‐热启动 
表 24：E‐11 热启动脉冲测试参数 
参数 
测试过程“短” 
测试过程“长” 
UB 
11.0V 
UT 
7.0V（0%，‐4%） 
US 
8.0V（0%，‐4%） 
UA 
9.0V（0%，‐4%） 
t50 
≥10ms 
tf 
≤1ms 
t4 
15ms 
t5 
70ms 
t6 
240ms 
t7 
70ms 
t8 
600ms 
tr 
≤1ms 
循环间隔 
5s 
20s 
测试循环次数 
10 
100 
 
插图说明 
a  关闭 KL.50 
b  打开 KL.50 
c  关闭 KL.50 
ttest            循环 
 
图 14：热启动测试脉冲 
 
 
 
 


### 第 38 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.11.3 要求 
不能出现故障存储记录。 
任何情况下汽车都能启动。 
 
6.11.3.1 启动相关元件： 
 
测试情况 1‐冷启动 
测试脉冲“正常”：功能参数 A 
测试脉冲“剧烈”：功能参数 B 
 
测试情况 2‐热启动 
测试过程“长”：功能参数 A 
测试过程“短”：功能参数 A 
 
6.11.3.2 启动不相关元件： 
 
测试情况 1‐冷启动 
测试脉冲“正常”：功能参数 C 
测试脉冲“剧烈”：功能参数 C 
 
测试情况 2‐热启动 
测试过程“长”：功能参数 A 
测试过程“短”：功能参数 A 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 39 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.12 E‐12 整车电路调节电压曲线 
 
6.12.1 目的 
通过采用智能发电机或 DC/DC 转换器调节来模拟整车电路特性。通过这一调节可以按照表
25 的测试情况在恒电压和固定电压波动之间调节电压曲线。 
与发动机运行相关的零件或汽车准备运行的零件都适用。 
 
6.12.2 测试 
表 25：E‐12 整车电路调节电压曲线测试参数 
测试件运行方式 
运行方式Ⅱ.c 
Umin 
（11.8V‐△U）(0%,‐4%) 
Umax 
(15V‐△U)（+4%，0%） 
t1 
2s 
tr 
≥300ms 
tf 
≥300ms 
循环次数 
10 
测试件数量 
至少 6 个 
测试情况 1 
△U   
0V 
测试情况 2 
△U 
0.7V 
测试情况 3 
△U 
2V 
 
图 15：E‐12 整车电路调节电压曲线测试脉冲 
6.12.3  要求 
功能参数 A 
 
 


### 第 40 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.13 E‐13 Pin 脚断开 
6.13.1 目的 
模拟每个 Pin 脚的线路断开，测试分两种不同的运行状态进行。Pin 脚断开（接触不良至长
时间断开）随着的时间推移会出现多种样式，因此会采用不同的脉冲形式。 
 
6.13.2 测试 
表 26：E‐13 Pin 脚断开测试参数 
测试件运行方式 
运行方式Ⅱ.a 和Ⅱ.c 
 
所有相关 KL 电状态（如 KL.15/KL.30/KL.87…）及其组合都要进行测试。
Z1 
状态 1：Pin 脚连接 
Z2 
状态 2：Pin 脚断开 
tr 
≤（0.1*t1） 
tf 
≤（0.1*t1） 
循环次数 
两种测试情况及相关 KL 电： 
运行方式Ⅱ.a:3 次循环 
运行方式Ⅱ.c: 3 次循环 
每个测试分开评估。 
测试件数量 
至少 6 个 
测试情况 1 
 
每个 Pin 脚先断开 t=10s，然后再插上（间隔慢些） 
测试情况 2 
 
对每个 Pin 脚进行脉冲包，模拟接触不良（图 16） 
脉冲包中脉冲 t2次数
4000 
a 
脉冲包 
t1 
0.1ms 
t2 
1ms 
t3 
10s 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 41 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
图 16：E‐13 测试情况 2 中 P in 脚断开测试脉冲 
6.13.2.1  测试流程 
将零件接上电压。 
该测试不用于供电 Pin 脚（如 KL.15,KL.30,KL87…）. 
该测试可用于接地 Pin 脚（KL.31）。 
 
Pin 脚电压可以限制到 E‐05 抛负载测试（见章节 6.5）中的最大电压。 
 
用 1kΩ（±5%）和 1Ω（±5%）作为测试替代件进行参照测量并记录存档。该测试是用于
验证陡度要求。低电感零件可以作为电阻使用。 
 
然后按照表 26 进行测试。 
 
6.13.3 要求 
 
所有测试情况：功能参数 C 
 
 
 
 
 
 
 
 
 


### 第 42 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.14 E‐14 插件断开 
 
6.14.1 目的 
模拟线束插件断开。 
 
6.14.2 测试 
表 27：E‐14 插件断开测试参数 
测试件运行方式 
运行方式Ⅱ.a 运行方式Ⅱ.c 
循环次数 
每个插件 2 种运行方式必各接插一次 
测试件数量 
至少 6 个 
 
6.14.2.1  测试流程 
将插件从测试件上拔下 10s 然后再插上。如果测试件有多个插件，则每个插件都要进行测试。
顺序可以变动。多个插件按照组合数学（分析）进行测试。 
 
6.14.3 要求 
功能参数 C 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 43 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.15 E‐15 反极性 
 
6.15.1 目的 
测试蓄电池外部启动辅助时反极性连接时测试件的稳定性。另外反极性可能出现多次，不允
许造成元件损坏。任一电压直至最小电压必须确保反极性安全。汽车保险不属于反极性安全
方案的一部分。 
 
6.15.2 测试 
必须对原始线路布置的所有相关的连接进行测试。 
测试件必须与汽车电路连接相一致。 
测试反极性的电压在 0V 和表  29 列出的最大值中任意电压值之间。 
 
测试过程中的输入电压需记录下来。 
表 28：E‐15 反极性测试参数 
测试件运行方式 
运行方式Ⅱ.a 
测试情况 1 
按照表 29 静态反极性 
测试情况 2 
按照表 30 动态反极性 
测试件数量 
至少 6 个 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 44 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.15.2.1 测试情况 1—静态反极性 
该项是测试元件在不同反极性电压下的稳定状况，电压根据汽车状态调整。 
 
表 29：E‐15 静态反极性测试参数 
Umax 
0V   
Umin 
‐14.0V 
△U1 
‐1V 
锐度 1 
Ri＜100mΩ 
锐度 2 
Ri＜30mΩ 
t1 
60s 
工作电压在反极性时通过继电器断开的元件,时间可以选择： 
8ms 
t2 
≥60s,但至少要达到元件在测试初的热状态 
tr 
≤10ms 
tf 
≤10ms 
循环次数 
1 
 
 
图 17：E‐15 反极性测试脉冲—静态反极性 
 
 
 
 
 
 
 
 
 
 
 


### 第 45 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.15.2.2 测试情况 2—动态反极性 
该项是测试无法再启动的汽车其元件在运转过程中的反极性状况。 
 
表 30：E‐15 动态反极性测试参数 
Umax 
10.8V   
Umin 
‐4.0V 
锐度 1 
Ri＜100mΩ 
锐度 2 
Ri＜30mΩ 
t1 
60s 
工作电压在反极性时通过继电器断开的元件,时间可以选择： 
8ms 
t2 
≤5min 
tr 
≤10ms 
tf 
≤10ms 
循环次数 
3 
 
 
图 18：E‐15 反极性测试脉冲—动态反极性 
 
6.15.3 要求 
反极性期间不允许触发安全相关的功能，如：电动车窗，电动天窗，起动机。 
 
功能参数 C 
 
 
 
 
 
 
 


### 第 46 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.16 E‐16 接地偏移 
6.16.1 目的 
不同接地连接位置的电位差会对接地点处的零件造成信号失真。必须确保接地点间的电位差
在电气连接时不超过静态±1V，且不能影响零件的正常功能。 
 
6.16.2 测试 
如果测试件有多个供电接头和接地点，则需要对每个连接点单独进行测试。 
 
元件连接如图 19 所示 
表 31：E‐16 接地偏移测试参数 
测试件运行方式 
运行方式Ⅱ.c 
U 
1V 
循环次数 
两个开关位置 
测试件数量 
至少 6 个 
 
插图说明 
B  总线系统 
S  信号线 
S1  两孔转换开关（a/b） 
TE  其他零件如参照物、测试台、模拟控制器、执行器、传感器或负载 
图 19：E‐16 接地偏移原理图 
 
6.16.3 要求 
功能参数 A 
 
 
 
 
 
 
 


### 第 47 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.17 E‐17 信号线和负载电路短路 
6.17.1 目的 
模拟所有设备输入和输出以及负荷电路的短路状况。 
所有输入和输出在+UB 和接地进行短路设置（在电源和接地有无情况下，输出激活和关闭，）。 
零件要能长时间经受短路。 
 
6.17.2 测试 
表 32：E‐17 信号线和负载电路短路测试参数 
测试件运行方式 
运行方式Ⅱ.c 
测试时间 
每个 Pin 脚接地和 UB 时短路各 60s 
测试电压 
UBmin 和 UBmax 
测试情况 1 
在电源供应和接地连接情况下 Pin 脚在 UB 和接地下交替进行测试 
测试情况 2 
在无电源供应和接地连接情况下 Pin 脚在 UB和接地下交替进行测试
测试情况 3 
有电源供应无接地连接情况下 Pin 脚在 UB 和接地下交替进行测试 
测试装置 
测试使用的稳压电源必须能够提供元件短路的电流，如果不可行，
允许稳压电源的缓冲使用汽车蓄电池（在这种情况下 UBmax 为最大
的充电电压）。 
测试件数量 
至少 6 个 
如果通过多个 Pin 脚进行电源连接和接地连接，则需考虑组合（多种可能性）测试。 
 
6.17.2.1  测试装置 
测试用的电源必须能提供相应的短路电流，如果不可行，允许用汽车电池对电源进行缓冲（这
种情况下 UBmax 为最大充电电压）。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 48 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
插图说明 
L  负载 
E  输入 
A  输出 
PWR UB 输出 
GND KL.31 输入/输出 
图 20：E‐17 信号线及负载电路短路原理图 
 
6.17.2.2  测试流程 
输入/输出：短路电流测试过程中记录和评估时间走向（曲线图）。 
记录下短路对功能的影响。 
 
6.17.3 要求 
输入和输出（E 和 A）：功能参数 C 
供电电压（PWR）：功能参数 D 
设备接地（GND）：功能参数 E 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 49 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.18 E‐18 绝缘电阻 
 
6.18.1 目的 
测量带电镀分离层的绝缘电阻，仅需注意在车上连接且需要进行绝缘的 Pin 脚。 
 
6.18.2 测试 
表 33：E‐18 绝缘电阻测试参数 
测试件运行方式 
运行方式Ⅰ.a 
测试电压 
500V DC 
测试时间 
60s 
测试点 
测试电压施加在： 
‐ 
没有电镀连接的接头 
‐ 
没有电镀连接的 Pin 脚和电气导电的护套之间 
‐ 
如果护套不导电，Pin 脚连接和护套环绕的电极之间 
‐ 
其他的测试点与相关专业部门协商确认 
循环次数 
1，上述几点至少测试一次 
测试件数量 
至少 6 个 
 
6.18.2.1  测试流程 
样品准备时必须进行“循环湿热测试”，测试细节与委托方协商确定，测量前样品需通风干
燥 30 分钟。 
 
6.18.3 要求 
绝缘电阻必须至少达到 10MΩ。 
测量结束后必须达到功能参数 A. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 50 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.19 E‐19 静态电流 
 
6.19.1 目的 
测量元件的静态电流消耗。 
 
6.19.2 测试 
有惯性运动功能的元件（如：风扇），只有此功能结束后才能测量静态电流消耗。 
测量时与外围设备和回路一起测量。 
 
表 34：E‐19 静态电流测试参数 
测试件运行方式 
运行方式Ⅱ.a 
测试电压 
12.5V（+4%，0%） 
测试件数量 
至少 6 个 
测试情况 1 
T 
Tmin 
测试情况 2 
T 
TRT 
测试情况 3 
T 
Tmax 
 
6.19.3 要求 
原则上所有测试件的静态电流消耗为 0mA。 
 
对于 KL15 断开后必须运行的测试件，静态期间的静态电流量（平均 12h）≤0.1mA，相当于
1.2mAh（+40℃以上时，≤0.2mA）。此状态必须在所有车辆静止状态和 12h 时间段的任一时
间里保持。其他特殊情况需要得到静态电流管理相关专业部门的批准许可。 
 
惯性运动功能同样也必须通过静态电流管理相关专业部门批准许可。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 51 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.20 E‐20 击穿强度 
 
6.20.1 目的 
模拟测试件的电镀零件之间的击穿强度，如：插头 Pin 脚，继电器，包扎或者线束。含有感
应零部件或控制感应零部件的元件都要进行测试。 
 
6.20.2 测试 
表 35：E‐20 击穿强度测试参数 
测试件运行方式 
运行方式Ⅱ.a 
测试电压 Ueff 
500V AC,50Hz，正弦曲线 
测试时间 
60s 
测试点 
测试电压施加在： 
‐ 
没有电镀连接的接头 
‐ 
没有电镀连接的 Pin 脚和电气导电的护套之间 
‐ 
如果护套不导电，Pin 脚连接和护套环绕的电极之间 
‐ 
其他的测试点与相关专业部门协商确认 
循环次数 
一个循环必须完成，上面列出的每个点至少测试一次 
测试件数量 
至少 6 个 
 
6.20.2.1  测试流程 
样品准备时必须进行“循环湿热测试”，测试细节与委托方协商确定，测量前样品需通风干
燥 30 分钟。 
 
6.20.3 要求 
功能参数 C 
不允许出现电压击穿和电弧现象。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 52 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.21 E‐21 反馈 
 
6.21.1 目的 
模拟 KL15 相关的测试件及其整车电路中用作唤醒功能的导线的特性。该测试适用于所有与
KL.15 和/或其他唤醒功能导线相关的元件。 
 
6.21.2 测试 
表 36：E‐21 反馈测试参数 
测试件运行方式 
运行方式Ⅱ.c 
Utest 
UBmax‐0.2V 
测试温度 
TBmax，TRT，TBmin 
测试件数量 
至少 6 个 
 
6.21.2.1 测试过程 
将测试件依据汽车电路进行连接（包括传感器，执行器等）并以正常运行方式运行，测量待
测试的 KL 电断路后的电压曲线，断路必须通过如继电器或者开关（R 开关_打开→∞）执行。其
他可能存在的电压电源如 KL30，在测试期间不允许断开（按照车辆特性）。此测试中待测试
的 KL 电不允许出现其他电阻。 
将外部电阻（如：示波器）≥10MΩ施加在 KL.31 上，测量待测试 KL 电的电压曲线。 
 
插图说明 
T  探测头 
Os  示波器 
K  待测试的 KL 电 
图 21：E‐21 反馈测试电路原理图 
 
 
 
 
 
 
 
 


### 第 53 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.21.3 要求 
 
待测试的 KL 电的电压反馈允许的电压最大为 1V，从断开的时间点开始在 t=20ms 之内必须
达到此电压范围。 
 
未接线的待测试的 KL 电的电压必须从断开的时间点开始在 t=20ms 之内降到 1V 以下。 
 
电压时间曲线要呈现持续下降趋势，由于正脉冲导致的不连续是允许出现在曲线中的。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 54 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
6.22 E‐22 过载电流 
 
6.22.1 目的 
测试机械开关、电气输出和接触连接的过载电流稳定性。同时也需要注意高于正常负载情况
的高电流（如：发动机最大堵转电流 IBlock）。 
 
6.22.2 测试 
表 37：E‐22 过载电流测试参数 
测试件运行方式 
运行方式Ⅱ.c 
温度 
Tmax 
电气输出测试条件 
输出必须至少能够承受未接通的额定负载的三倍负载。 
 
负载持续时间 30min 
接通的输出测试条件 
IN≤10A 的元件： 
Itest=3*lN 
IN>10A 的元件： 
Itest=2 *lN,但最低要达到 30A,最高不超过 150A 
IBlock＞3* lN 的元件： 
Itest= IBlock 
负载的情况下按一下“关”，“开”然后再“关”。 
 
负载持续时间 10min 
 
多触点继电器和开关的每个触点必须单独测试 
测试件数量 
至少 6 个 
 
6.22.3 要求 
不带保险的机械元件要达到功能参数 A，如果负荷电路中有保险，则此项不作要求。 
 
带有过载识别（电流，电压，温度）的电气输出达到功能参数 C。 
 
另外，在对元件进行外观检查时，不允许出现影响功能或减少使用寿命的损坏（外观和电气
特性）。   
 
 
 
 
 
 


### 第 55 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
第 2 部分–  环境要求和测试 
 
7  适用范围 
 
该标准规定了 3.5t 及以下车辆电气、电子及机械零部件和系统的测试条件要求和方法。 
 
其他测试条件及要求或偏差要求见相应的零部件设计任务书。 
 
注意：标准中涉及到的测试不能用来作为零件认可或生产过程认可的证明。 
 
8  标准参考 
表 38：标准参考 
DIN 75220 
阳光模拟元件老化测试 
DIN EN 13018 
非破坏性测试—外观检查—基本要求 
DIN EN 60068‐2‐1 
环境影响‐第 2‐1 部分：测试方法‐测试 A：冷 
DIN EN 60068‐2‐2 
环境影响‐第 2‐2 部分：测试方法‐测试 B：干热 
DIN EN 60068‐2‐6 
环境影响‐第 2‐6 部分：测试方法‐测试 Fc:振动（正弦） 
DIN EN 60068‐2‐11 
环境测试  第 2 部分：测试‐测试 Ka：盐雾 
DIN EN 60068‐2‐14 
环境测试  第 2 部分：测试‐测试 N：交变温度 
DIN EN 60068‐2‐27 
环境影响‐第 2‐27 部分：测试方法‐测试 Ea 和指南：冲击 
DIN EN 60068‐2‐29 
环境测试  第 2 部分：测试 Eb 和手册：机械耐久冲击实验 
DIN EN 60068‐2‐30 
环境影响‐第 2‐30 部分：测试流程‐测试 Db：湿热，循环（12+12
小时） 
DIN EN 60068‐2‐38 
环境测试  第 2 部分：测试‐测试 Z/AD：混合测试，温度/湿度，
循环 
DIN EN 60068‐2‐60 
环境测试  第 2 部分：测试‐测试 Ke：流动的混合气体防腐蚀
测试 
DIN EN 60068‐2‐64 
环境测试  第 2 部分：测试流程测试 Fh：振动，宽频带噪音
（电子调节）和手册 
DIN EN 60068‐2‐78 
环境测试  第 2‐78 部分：测试‐测试 Cab：恒温恒湿 
DIN EN ISO 11124‐2 
应用涂料和有关制品前钢基材的制备.金属喷砂清理研磨料
规范.第 2 部分:冷铁粗砂 
DIN EN ISO 20567‐1 
涂层材料‐涂层抗碎石击打强度‐第 1 部分：多种碎石击打测试
DIN EN ISO 6270‐2 
涂层材料‐耐潮性‐第 2 部分：冷凝水气候测试 
ISO 12103‐1 
道路车辆—粉尘过滤器测试‐第 1 部分：亚利桑那州粉尘 
ISO 16750‐3 
道路车辆—电气电子设备的环境条件和试验  第 3 部分:机械
负荷 
ISO 16750‐5 
道路车辆‐‐电气和电子设备的环境条件和试验‐‐第 5 部分:化
学负荷 
ISO 20653 
道路车辆‐IP‐Code 防护等级‐电气元件防杂质、水 
 
 
 
 


### 第 56 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
9  术语和定义 
9.1  术语和缩写 
表 39：环境要求和测试缩写表 
电子总成 
已装好电子零件的不带外壳的电路板 
模块/零部件 
电气、电子或机电零部件（如电阻、电容、晶体管、IC、继电器） 
DUT 
测试样品 
功能 
含系统功能和诊断功能 
硬件冻结 
开发过程中的一个节点，从这时起硬件不能再变更 
ICT   
逐个元件测试 
具有冷凝功能的
环境测试箱 
环境测试箱内有一个可以调控的水浴装置，它可以将规定的水量转化为
水蒸气。PCB 板上的冷凝薄膜强度视热质量、相对湿度以及水浴的温度
梯度而定。冷凝期间需关掉测试箱的温湿调控，通过水浴来调控测试的
室温。 
元件 
完整的仪器、控制器或机电设备（带外壳） 
样品 
待测试的系统或元件 
PTB 
德国联邦物理技术研究院 
PSD 
功率谱密度 
电路板 
未组装的内部器件（未组装的 PCB、陶瓷、引线框、弹力带等） 
系统 
由各功能元件组成，如制动控制系统（控制器、液压、传感器） 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 57 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
9.2 电压 
表 40：电压定义缩写 
UBmin 
最低工作电压 
UB 
工作电压 
UBmax 
最高工作电压 
 
9.2.2.1  含其他要求的元件电压 
 
UBmin，HV 
最低工作电压—HV 最低直流工作电压 
UB，HV 
工作电压—HV 直流工作电压 
UBmax，HV 
最高工作电压‐HV 最高直流工作电压 
 
9.3 温度 
表 41：温度定义 
Tmin 
最低工作温度 
TRT 
室内温度 
Tmax 
最高工作温度 
Top,min 
有过载保护/低温保护功能的元件的最低工作温度 
Top,max 
有过载保护/高温保护功能的元件的最高工作温度 
TTest 
测试温度 
 
9.3.1  冷却液管路元件的温度 
Tkühl,nom 
冷却液管路的冷却液额定温度 
Top,max 
冷却液管路的冷却液最低温度 
TTest 
冷却液管路的冷却液最高温度 
 
9.4 时间 
表 42：时间定义 
tPrüf 
测试时间 
tBetrieb 
使用寿命内的工作时间 
 
9.5 标准公差 
如果没有其他说明，则按照表 43 实施。 
公差涉及到要求的测量值。 
表 43：标准公差 
频率 
±1% 
温度 
±2℃ 
空气湿度 
±5% 
时间 
+5%，0% 
电压 
±2% 
电流 
±2% 
振动 
±3dB 
振动功率谱密度 PSD 
±5% 


### 第 58 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
9.6 标准值 
如果没有其他说明，则按照表 44 实施。 
表 44：标准值 
室内温度 
TRT=23℃±5℃ 
空气湿度 
Frel=相对湿度 25%‐75% 
测试温度 
TTest=TRT 
工作电压 
（用于测试） 
UB=14V 
 
10  基本要求 
10.1  运行模式 
纯粹靠燃烧发动驱动的汽车，其使用寿命期间内的运行状态一般分为两种运行模式： 
� 
驾驶状态 
� 
停车状态 
 
其他驱动的汽车也可以考虑其他运行模式（见表 45）。 
 
与多种运行模式相关的元件（图 22），如有必要，每种运行模式的运行方式（见章节 10.2）
需一一进行确定。 
 
表 45：运行模式基本要求 
运行模式 
关闭汽车 
插上充电线 
给驱动电池充电
启动电力线通信
（如有） 
驾驶中 
否 
否 
是/否 
否 
充电状态 
是 
是 
是 
是 
预处理 
是 
是/否 
是/否 
是/否 
入网停车 
是 
是 
否 
是 
离网停车或停车 
是 
否 
否 
否 
 
测试中需考虑（图 22 中）元件相关的所有运行模式。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 59 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
图 22：不同运行模式的载荷谱分类 
 
10.2  运行方式 
10.2.1  总则 
电气、电子、机电元件及系统在其寿命期间会有多种运行方式，这也是本标准中需要测试的。
运行方式和用电器（如控制器、总线消息、原始传感器、原始执行器或备用线路）的具体要
求及限制条件由委托方和承接方共同协商确定并记录存档。 
 
10.2.2  运行方式Ⅰ—样件没有电气连接 
10.2.2.1 运行方式Ⅰ.a 
样件没有电流通过，无接插件和线束。 
冷却液管路没有注入冷却液，且管路密封。 
 
 
 
 
 
 
 
 
 
 
 
载荷谱
分成多个运行模式
1.定义载荷谱 
2.确定各个运行模
式相关的规格尺寸 
3.模型应用 
4.定义各个运行模
式相关的规格尺寸 
驾驶状态 
充电状态
预处理
入网停车 
离网停车
运行时间 
冷却液温度分布 
环境温度分布 
运行时间 
冷却液温度分布 
环境温度分布 
运行时间 
冷却液温度分布 
环境温度分布 
运行时间 
冷却液温度分布 
环境温度分布 
运行时间 
冷却液温度分布 
环境温度分布 
寿命模型 
寿命模型 
寿命模型 
寿命模型 
寿命模型 
环境测试温度 
冷却液测试温度 
部分测试时长 
电负载 
环境测试温度 
冷却液测试温度 
部分测试时长 
电负载 
环境测试温度 
冷却液测试温度 
部分测试时长 
电负载 
环境测试温度 
冷却液测试温度 
部分测试时长 
电负载 
环境测试温度 
冷却液测试温度 
测试时间 
电负载 
环境测试温度 
冷却液测试温度
总测试时长
电负载 
元件各种运行模式下的测试要求


### 第 60 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
10.2.2.2 运行方式Ⅰ.b 
样件未通电流，但已连接接插件和电线束。 
如有冷却液管路，冷却液管路注入冷却液，且连接上冷却液管。 
 
10.2.3 运行方式Ⅱ  –  样件电气连接 
 
10.2.3.1 运行方式Ⅱ.a 
样件无负载运行。 
如有冷却液管路，冷却液管路注入冷却液，且连接上冷却液管。如有要求，冷却介质的流量
及温度按照零部件设计任务书中的要求进行调控。 
 
10.2.3.2 运行方式Ⅱ.b 
样件以最低负载运行。 
样件运行时必须只能产生最小的自身发热（如：通过减小持续的输出功率或者通过较少的外
部负载触发）。 
如有冷却液管路，冷却液管路注入冷却液，且连接上冷却液管。如有要求，冷却介质的流量
及温度按照零部件设计任务书中的要求进行调控。 
 
10.2.3.3 运行方式Ⅱ.c 
样件以最大负载运行（高级用户 Power user，但不能出现错误使用） 
样件运行时必须产生最大的自身发热（如：通过切合实际的最大持续的输出功率或者通过频
繁的外部负载触发）。 
如有冷却液管路，冷却液管路注入冷却液，且连接上冷却液管。如有要求，冷却介质的流量
及温度按照零部件设计任务书中的要求进行调控。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 61 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
10.2.3.4 运行方式举例 
表 46：运行方式举例 
元件举例 
运行方式Ⅱ.a 
运行方式Ⅱ.b 
运行方式Ⅱ.c 
带有导航的汽车收音
机 
停车状态的元件（睡
眠）空载结束，打开
KL30   
汽 车 行 驶 状 态 的 元
件。司机关闭元件，
激活 BUS/μC’s，打开
KL15   
汽车行驶状态的元件。
启动元件（CD，导航，
放大器），激活 BUS/
导航仪 
防盗报警装置 
汽车运行无此功能 
监视停车状态的汽车内部 
制动调节系统 
停车状态的元件，空
载结束 
行驶中不操作刹车系
统 
行驶中多次循环使用
刹车系统（不允许不当
行为，如：不间断的刹
车操作） 
车载充电器 
离网（OFF‐Grid）停
车或者汽车运行 
入网（ON‐Grid）停车
（仅电力载波通信，
不充电）汽车调节 
充电 
高压‐蓄电池（蓄电池
管理系统） 
离网（OFF‐Grid）驻
车 
带有电力载波通信 
的入网（ON‐Grid）停
车 
汽车运行，充电 
 
10.3  温度设置 
在规定的运行条件下，元件从恒温处理的时间点起保持稳定的环境温度，在这个时间点后面
的运行时间里元件温度变化不允许超过±3℃。 
 
受托方必须根据实验确认完整的恒温时间，并记录在测试文件中。 
 
温度变化测试中样件在达到规定的恒温后再停留一段时间，以便元件中的应力可以转化延伸。
停留时间见各项测试规定。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 62 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
10.4  参数测试 
设计任务书中或者与委托方协商中会定义敏感参数，也叫关键参数，如：静态电流消耗，工
作电流，输出电压，过渡电阻，输入阻抗，信号速率（上升时间和下降时间），总线规范。
每次测试开始前和测试结束后，都要检测这些参数是否符合标准要求。 
与冷却液管连接的元件：需在 TRT 用 Tkühl,nom,在 Tkühl,max 用 Tmax 用 Tkühl,min 进行参数测试。 
如果零部件设计任务书中没有其他规定，则高压（HV）电源的元件在 UBmin 下用 UBmin,HV;在
UB 下用 UB,HV;在 UBmax 下用 UBmax,HV 进行参数测试。 
 
10.4.1 参数测试（小） 
测量关键参数，检查元件在 TRT 和 UB 的功能特性。带有故障存储功能的元件必须能够读取故
障存储内容。在不拆解样件的情况下，依据 DIN EN 13018 对元件外部缺陷/变化如：裂纹，
开裂/脱落，褪色，变形等进行外观检查。 
测试人员必须将测试获得的关键参数值，功能特性或者故障存储记录的变化，包括外观检查
中的异常情况与新状态的参数进行对比评估。 
 
所有测试结果都必须在检测报告上进行记录。 
 
10.4.2 参数测试（大） 
测量关键参数，分别测量电压在 UBmin,UB,UBmax 时的 Tmax,TRT,Tmin 下的零件功能特性。 
带有故障存储功能的元件必须能够读取故障存储内容。在不拆解样件的情况下，依据 DIN EN 
13018 对元件外部缺陷/变化如：裂纹，开裂/脱落，褪色，变形等进行外观检查。 
 
测试人员必须将测试获得的关键参数值，功能特性或者故障存储记录的变化，包括外观检查
中的异常情况与新状态的参数进行对比评估。 
 
所有测试结果都必须在检测报告中进行记录。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 63 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
10.4.3 参数测试（功能测试） 
必须在规定的温度和三个不同的电压（UBmin,UB,UBmax）下测试关键参数。 
必须测试元件的基本功能，带有故障存储功能的元件必须能够读取故障存储内容。 
 
测试人员必须将测试获得的关键参数值，基本功能或者故障存储记录的变化与之前的测试负
荷进行对比评估。 
 
所有测试结果都必须在检测报告中进行记录。 
 
10.4.4  物理分析 
物理分析时，必须将试验件进行拆解，并依据 DIN EN 13018 进行外观检查。 
 
附加分析由委托方和受托方共同协商确认，分析案例参见附件 G。 
 
必须对元件与新状态之间的变化进行评估。若测试件显示异常时，后续必须尽可能增加更多
的测试件或者使用附加分析方法，与委托方一起进行分析确认。 
 
所有测试结果都必须在检测报告上进行记录。 
 
10.5 应用迁移分析法的参数持续监控 
整个测试期间必须记录监控的关键参数。 
 
有故障记录功能的元件必须持续监控故障存储并记录。 
 
通过持续参数监控获得的数据来分析其变化和趋势，以便识别元件的异常，老化或者故障。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 64 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
10.6  气密测试 
有冷却液流经的元件需进行气密测试，测试时需注意元件的特殊结构以及冷却液管路的规格
标准。 
如零部件设计任务书中无其他规定，则按照下列测试要求进行： 
 
� 
压力脉动测试：在 Tmax 和 Tkühl,max 温度下对冷却液管路在最低和最高压力之间进行
100000 次压力交变；在 Tmin 和 Tkühl,min 温度下对冷却液管路在最低和最高压力之间进行
50000 次压力交变。 
� 
分别在 TRT 采用 Tkühl,nom,Tmax 采用 Tkühl,max，Tmin 采用 Tkühl,min 温度在最小、最大和额定冷却
液管路的压力时进行静态气密测试。 
� 
如果元件要在低压方式下注入冷却液，则在 TRT 下施加 20mbar 以下的测试压力进行低
压测试。如零部件设计任务书无其他规定，从环境压力到测试压力的来回转变的转移时
间应＜5s。测试压力的停留时间至少为 30s。 
� 
分别在 TRT 采用 Tkühl,nom,Tmax 采用 Tkühl,max，Tmin 采用 Tkühl,min 温度在最小、最大和额定冷却
液管路的压力时进行冷却液流速测试。 
 
如零部件设计任务书无其他规定，气密测试具体细节由委托方和承接方协商确定。 
 
在进行顺序测试（A.2）、非顺序测试（平行测试）（A.3）以及耐久性测试（A.4）时，要分别
在首次参数测试（大）和最后的参数测试（大）结束后进行气密测试。 
 
10.7  采样率和测量值分辨率 
测量系统的采样率或者带宽视各自测试而定。 
 
必须识别和记录与功能相关的峰值（临时正/负偏差）。 
 
测量值分辨率必须与各自的测试相符合。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 65 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
11  使用属性 
11.1  使用寿命参数 
 
使用寿命常用参数见表 47. 
表 47：寿命要求 
使用寿命 
15 年 
运行状态下的工作时长 
8000h 
行驶里程 
300000km 
 
其他驱动的汽车如有必要也可以考虑其他运行模式（见表 45）。 
 
其他运行模式（见表 45） 
� 
充电状态下的工作时长 
� 
预处理状态下的工作时长 
� 
入网停车状态下的工作时长 
下的工作时长见零部件设计任务书。 
 
11.2 温度分布概况 
为了全面描述汽车安装范围内的元件的温度负载，除了规定最小工作温度 Tmin 和最大工作温
度 Tmax 之外，还需规定元件在 Tmin 和 Tmax 之间的不同温度分布的间隔时间。 
 
针对其他驱动的汽车：必须区分“驾驶状态”、“充电状态”、“预处理状态”和“入网停车状
态”这几个模式。且必须分别规定环境温度下和冷却液管路温度下的温度分布状况（见图
22）。 
 
一般情况下温度分布是连续的分布，因为元件的环境温度值可以是 Tmin 和 Tmax 间的任意数值。 
为简化测试时间的计算，可以借助 Arrhenius（参见附录 C）加快使用寿命模型的方法通过非
连续温度节点来表现持续分布状况。每个温度节点都必须给出元件工作时间里 pi 的百分比。 
 
相应的温度分布见下面的总表 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 66 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
表 48：温度分布 
温度℃ 
分布 
TFeld.1=Tmin 
p1 
TFeld.2 
p2 
… 
… 
TFeld.n=Tmax 
pn 
原则上依据现场测量和技术经验。 
 
不同安装区域的常见温度分布参见附录 B。 
不同元件的温度分布应用需通过如：车辆测量，模拟或者依据经验进行验证。如有偏差应根
据零部件进行调整。 
特殊的安装位置或者安装条件（如：安装位置靠近热源处）一般需要定义元件匹配的温度分
布情况。 
可行的温度分布情况需记录在零部件设计任务书中。 
附件 B 还补充了汽车运行状态中的元件的平均温升的常见数值。 
与元件相关的温度分布需在零部件设计任务书中进行规定。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 67 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
12  测试选择 
12.1  测试选择表 
表 49：测试选择表 
测试 
适用于 
要求说明 
M‐01 跌落测试 
所有元件 
测试时肯定会受损的元件（如：玻璃，高敏感
测量传感器），可以与委托方确认取消该项测
试。这些必须记录下来。 
无 
M‐02 石击测试 
安装区域可能会遭受石击的元件 
无 
M‐03 粉尘测试 
元件防护等级 IP6KX 
元件防护等级 IP5KX 
所有元件 
不允许有粉尘进入的元件 
只允许少量的不影响功能和安全的粉尘进入
的元件 
无 
M‐04 振动测试 
—按照振动属性 A 
—按照振动属性 B 
—按照振动属性 C 
—按照振动属性 D 
—按照振动属性 E 
所有元件 
安装在发动机上的元件 
安装在变速箱上的元件 
安装在进气歧管但不固定连接的元件 
安装在车身上的元件 
安装在非簧载质量（车轮、悬架）上的元件 
无 
M‐05 机械冲击 
所有元件 
无 
M‐06 机械耐久冲击 
安装在门或者罩盖上的元件 
冲击次数 
K‐01 高温/低温老化 
所有元件 
无 
K‐02 温度分级测试 
所有元件 
无 
K‐03 低温运行 
所有元件 
无 
K‐04 重新喷漆温度 
安装在外舱并在重新喷漆时会升温的零件 
无 
K‐05 温度冲击（元件） 
—根据 DIN EN 60068‐2‐14 
Na（空气‐空气） 
—根据 DIN EN 60068‐2‐14 
Nc（介质‐介质） 
所有元件 
非永久在液体中运行的元件 
 
永久在液体中运行的元件（IPX8） 
 
测试方法（Na
或 Nc），如果
Nc：测试介质 
K‐06 运行时的外舱盐雾
测试 
安装在外部，底盘或发动机舱的零件 
测试循环次数 
K‐07 运行时的内舱盐雾
测试 
安装在车内裸露位置的零件（如：行李箱，车
门湿区，备胎区的车门内侧壁小袋） 
无 
K‐08 湿热，循环 
所有元件 
无 
K‐09 湿热，循环（带有霜
冻） 
所有元件 
无 
K‐10 防 水 保 护 ‐IPX0 至
IPX6K 
—防护等级 IPX0 
—防护等级 IPX1 
—防护等级 IPX2 
 
—防护等级 IPX3 
所有元件 
 
不需要防水的元件 
水滴垂直落下时不允许产生损坏的元件 
距安装面倾斜角不超过 15°，水滴垂直落下时
不允许产生损坏的元件 
喷淋水时不允许产生损伤的元件 
无 


### 第 68 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
—防护等级 IPX4K 
—防护等级 IPX5 
—防护等级 IPX6K 
压力调高时溅水不允许产生损伤的元件 
喷水时不允许产生损伤的元件 
压力调高时强烈喷水不允许产生损伤的元件 
K‐11 高压/蒸汽喷射清洗 
直接受到高压、蒸汽喷射清洗或者底盘洗涤的
元件 
无 
K‐12 溅水温度冲击 
可能会遭受溅水的安装在外部或发动机舱的
元件（如：轮胎涉水） 
无 
K‐13 浸水温度冲击 
涉水下面安装的零件，会时不时地浸没在（盐）
水中（如：驶过积水）（IPX7） 
无 
K‐14 恒湿恒温 
所有元件 
等级 
K‐15 冷凝和气候测试 
是否需要进行该项测试视零部件而定。如果有
此项测试要求，参见设计任务书。 
 
如果该测试在设计任务书中有规定，则外壳防
水的零部件可以选择进行 K‐15a 的冷凝测试或
进行 K‐15b 的气候测试。外壳不防水的零部件
则进行 K‐15a 的冷凝测试。 
无 
K‐16 温度冲击 
（无外壳） 
所有元件的组件 
无 
K‐17 日照 
阳光能直接照射到的元件 
无 
K‐18 有害气体测试 
含有非气密的开关触点的元件 
无 
C  化学测试 
所有元件 
化学物质 
运行方式 
L‐01 机械/液压耐久测试 
带有机械/液压操作/功能循环的元件如：制动
控制，座椅调节循环，开关/按钮操作 
功能/操作循环
次数 
L‐02 高温耐久测试 
所有元件 
测试时间 
L‐03 温度变化耐久测试 
所有元件 
测试循环次数 
 
12.2  测试流程 
元件相关的测试流程参见设计任务书。 
 
不同 OEM（如：工业标准构件 IBK）间的合作项目讨论前提参见附件 A 的测试流程。 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 69 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
13  机械要求和测试 
 
13.1 M‐01 跌落测试 
 
13.1.1 目的 
该项测试模拟了底座上元件从整个过程链到安装到车上时的可能会出现的自由落体活动。 
该测试是为确保零件跌落时没有受损，从而在装车时没有出现隐蔽性损坏，如零件内部松动
或出现裂纹。 
 
13.1.2 测试 
表 50：M‐01 跌落测试参数 
样件运行方式 
运行方式Ⅰ.a 
跌落高度 
1m 
碰撞面 
混凝土地面 
测试循环 
3 个样件每个都要进行朝两个方向（第 1 个样件：±X，第 2
个样件：±Y，第 3 个样件：±Z）进行跌落测试 
样件数量 
3 
 
13.1.3 要求 
必须用肉眼对样件进行检查，通过摇动来检查样件是否松动。 
 
—如果样件外表有损坏，必须将这个损坏记录在测试报告中。 
 
—如果样件外表完好的，则样件在测试结束后必须功能完好，所有参数必须在规定范围内。
按照章节 10.4 进行参数测试（大）验证，内部不允许有隐藏性损伤。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 70 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
13.2 M‐02 石击测试 
 
13.2.1 目的 
通过碎石撞击测试模拟元件的机械负荷要求。 
测试元件防止缺陷形成（如：变形和裂纹）的稳定性。 
 
13.2.2 测试 
测试标准见 DIN EN ISO 20567‐1，测试方法 B,参数如下： 
表 51：M‐02 石击测试参数 
样件运行方式 
运行方式Ⅰ.b 
粗砂石重量 
500g 
测试压力 
2 bar 
喷砂材料 
硬模铸造铁砂粒，标准见 DIN EN ISO 11124‐2，颗粒大小 4‐5mm 
样件的测试面 
汽车上所有可接触到的表面 
撞击角度 
与喷砂方向呈 54° 
测试装置 
多冲击测试仪，标准见 DIN EN ISO 20567‐1 
循环次数 
2 
样件数量 
6 
 
13.2.3 要求 
测试件必须在测试前和测试后功能完好，所有参数必须在规定范围之内，按照章节 10.4 进
行参数测试（小）验证。 
 
此外还必须用肉眼对样件进行检查，通过摇动来检查样件是否松动。 
在检测报告中记录变化/损坏，并与委托方进行评估。 
 
无需按照 DIN EN ISO 20567‐1 的参数进行评估。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 71 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
13.3 M‐03 粉尘测试 
 
13.3.1 目的 
模拟汽车运行状态下元件的粉尘负荷。 
目的是确保元件能承受电气和机械缺陷。 
 
13.3.2 测试 
测试标准见 ISO‐20653，参数如下： 
 
表 52：M‐03 粉尘测试参数 
样件运行方式 
电气/电子元件：运行方式Ⅱ.a 
 
机电一体化元件（如：带有通风装置的元件）：依据图 23 间歇性
执行运行方式Ⅱ.c 和运行方式Ⅱ.a 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种运行模式（见章节
10），则元件在要求运行方式Ⅱ.c 的期间内，所有与运行方式Ⅱ.c
相关的运行模式的运行时间要相同。 
测试装置 
符合 ISO‐20653:2006 图 1 的竖直流向 
防护等级 
按照设计任务书规定 
测试时间 
共 20 次循环，每次循环各 20 分钟 
样件数量 
6 
 
 
图 23：M‐03 粉尘测试过程 
 
测试过程中，需要模仿车内元件的安装位置。受托方提出测试装置（安装位置，盖板，饰板，
运行情况），并与委托方协商确认和记录。 
 
13.3.3 要求 
必须达到零部件设计任务书中规定的防护等级（防护等级标准见 ISO 20653）要求。 
 
 
 
 
 
 
 
运行方式Ⅱ.c 
粉尘 
运行方式Ⅱ.a 
1 循环/20min 
5 分钟 


### 第 72 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
样件必须在测试前、测试期间以及测试后功能完好，并且所有的参数必须在规定范围内，按
照章节 10.4 进行参数测试（小）验证。 
 
此外还必须用肉眼对样件进行检查，通过摇动来检查样件是否松动。 
在检测报告中记录变化/损坏，并与委托方进行评估。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 73 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
13.4 M‐04 振动测试 
13.4.1 目的 
模拟汽车运行时元件的振动负荷。 
目的是确保测试元件能承受组件松动和材料疲劳的能力。 
 
13.4.2 测试 
测试标准见 ISO 16750 第 3 部分。 
测试按照正弦波振动 DIN EN 60068‐2‐6 和宽频振动 DIN EN 60068‐2‐64 执行，参数如下： 
 
表 53：基本振动测试参数 
样件运行方式 
如果元件是无负载运行：整个测试过程按照
Ⅱ.a 
 
如果元件时带负载运行：依据图 24 间歇性执
行运行方式Ⅱ.a 和运行方式Ⅱ.c 
 
叠加的温度曲线 
按照图 24 进行重复测试： 
振动温度曲线和表 54： 
振动温度曲线 
正弦振动的频率通过时间 
1 Oktave/min，对数的 
振动曲线 A（安装在发动机上的元件） 
依据图 25 和表格 55：正弦波振动 
 
依据图 26 和表格 56：宽带噪声激发振动 
振动曲线 B（安装在变速箱上的元件） 
依据图 27 和表格 57：正弦波振动 
 
依据图 28 和表格 58：宽带噪声激发振动 
振动曲线 C 
（安装在进气歧管但不固定连接的元件） 
依据图 29 和表格 59：正弦波振动 
振动曲线 D（安装在车身上的元件） 
依据图 30 和表格 60：宽带噪声激发振动 
振动曲线 E（安装在底盘上的元件） 
依据图 31 和表格 61：宽带噪声激发振动 
样件数量 
6 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 74 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
安装在电子机器上的元件至少要按照振动曲线 D 进行测试。但该测试曲线不是从电子机器
出发的振动负荷。实际情况下元件会受到该振动负荷。因此测试时需考虑从电子机器出发的
振动负荷。因此要对每个电子机器进行测量。 
 
测试时无需支架或者元件。零部件设计任务书中会规定如何固定已连接的线束和管路（如线
束、冷却液管、液压管路等）。 
 
通过防振器安装在支架或底盘上的元件必须在零部件设计任务书中规定：是否 
 
—所有含防振器的样件 
—所有不含防振器的样件  或 
—3 个含防振器的样件和 3 个不含防振器的样件 
 
要进行测试。 
 
采样率的选择必须能够识别断路和短路。 
 
为确保由元件、支架和零部件组成的整个系统的强度，是否需要进行其他测试，需要和委托
方协商确定。 
 
 
图 24：振动温度曲线 
 
 
 
 
 
 
 
 
时间：分钟
从 135 到 410min：Ⅱ.a 或Ⅱ.c 


### 第 75 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
表 54：振动温度曲线 
时间：min 
温度：℃ 
0 
TRT 
60 
Tmin 
150 
Tmin 
300 
Tmax 
410 
Tmax 
480 
TRT 
 
如果有冷却液管路，则测试温度下的冷却液温度则按照 Tkühl,min 和 Tkühl,max 实施。超出该温度
范围的，只有环境温度才能变化。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 76 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
13.4.2.1 振动曲线 A（安装在发动机上的元件） 
表 55：发动机安装件正弦波振动测试参数 
振动激发 
正弦波 
每个轴向的测试时间 
22h 
振动曲线 
曲线 1：安装在 5 缸及以下发动机上的元件 
曲线 2：安装在 6 缸及以上发动机上的元件 
 
两种情况都有的元件使用组合曲线 
图 25 中的曲线 1 
频率 Hz 
加速振幅 m/s2 
100 
100 
200 
200 
240 
200 
270 
100 
440 
100 
图 25 中的曲线 2 
频率 Hz 
加速振幅 m/s2 
100 
100 
150 
150 
440 
150 
组合 
频率 Hz 
加速振幅 m/s2 
100 
100 
150 
150 
200 
200 
240 
200 
255 
150 
440 
150 
 
 
 
 
 
 


### 第 77 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
 
图 25：安装在发动机上元件的正弦波振动曲线 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
频率 Hz
加
速
振
幅 
曲线 1           
曲线 2 


### 第 78 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
表 56：安装在发动机上元件的宽带噪音激发的振动测试参数 
振动激发 
宽频噪声激发 
每个轴向的测试时间 
22h 
加速有效值 
181m/s2 
图 26 中的振动曲线 
频率 Hz 
功率谱密度(m/s2)2/Hz 
10 
10 
100 
10 
300 
0.51 
500 
20 
2000 
20 
 
 
图 26：安装在发动机上元件的宽带噪音激发的振动曲线 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
频率 Hz
功率谱密度 


### 第 79 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
13.4.2.2 振动曲线 B（安装在变速箱上的元件） 
表 57：安装在变速箱上的元件的正弦波振动测试参数 
振动激发 
正弦波 
每个轴向的测试时间 
22h 
图 27 中的振动曲线 
频率 Hz 
加速振幅 m/s2 
100 
30 
200 
60 
440 
60 
 
图 22：安装在变速箱上的元件的正弦波振动曲线 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
加速振幅 
频率 Hz


### 第 80 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
表 58：安装在变速箱上元件的宽带噪声振动测试参数 
振动激发 
宽带噪声激发 
每个轴向的测试时间 
22h 
加速有效值 
96.6m/s2 
图 28 中的振动曲线 
频率 Hz 
功率谱密度(m/s2)2/Hz 
10 
10 
100 
10 
300 
0.51 
500 
5 
2000 
5 
 
 
图 28：安装在变速箱上元件的宽带噪声振动曲线 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
功率谱密度 
频率 Hz


### 第 81 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
13.4.2.3 振动曲线 C（分离的进气总管安装件） 
表 59：安装在进气歧管但不固定连接的元件正弦测试参数 
振动激发 
正弦波 
每个轴向的测试时间 
22h 
图 29 中的振动曲线 
频率 Hz 
加速振幅 m/s2 
100 
90 
200 
180 
325 
180 
500 
80 
1500 
80 
 
图 29：安装在进气歧管但不固定连接的元件正弦振动曲线 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
加速振幅 
频率 Hz


### 第 82 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
13.4.2.4 振动曲线 D（安装在车身上的元件） 
表 60：安装在车身上的元件宽带噪声测试参数 
振动激发 
宽带噪声 
每个轴向的测试时间 
8h 
加速有效值 
30.8m/s2 
图 30 中的振动曲线 
频率 Hz 
功率谱密度(m/s2)2/Hz 
5 
0.884 
10 
20 
55 
6.5 
180 
0.25 
300 
0.25 
360 
0.14 
1000 
0.14 
2000 
0.14 
 
 
图 30：安装在车身上的元件宽带噪声振动曲线 
 
 
 
 
 
 
 
 
 
 
 
 
 
功率谱密度 
频率 Hz


### 第 83 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
13.4.2.5 振动曲线 E（安装在底盘上的元件） 
表 61：安装在底盘上的元件宽带噪声振动测试参数 
振动激发 
宽带噪声 
每个轴向的测试时间 
8h 
加速有效值 
107.3m/s2 
图 31 中的振动曲线 
频率 Hz 
功率谱密度(m/s2)2/Hz 
20 
200 
40 
200 
300 
0.5 
800 
0.5 
1000 
3 
2000 
3 
 
图 31：安装在底盘上的元件宽带噪声振动曲线 
 
13.4.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
功率谱密度 
频率 Hz


### 第 84 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
13.5 M‐05 机械冲击 
 
13.5.1 目的 
此测试是模拟元件的机械负荷如：越过路面石头等或者事故。 
测试元件耐裂纹或零件松动性。 
 
13.5.2 测试 
测试标准见 DIN EN 60068‐2‐27，参数如下： 
表 62：M‐05 机械冲击测试参数 
样件运行方式 
如果元件是带负载运行：运行状态
的Ⅱ.c 
 
如果元件运行不带负载运行：运行
方式Ⅱ.a   
峰值加速 
500m/s2 
冲击时长 
6ms 
冲击形式 
半正弦 
每个方向（±X，±Y，±Z）冲击次数 
10 
样件数量 
6 
 
13.5.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 85 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
13.6 M‐06 机械耐久冲击测试 
 
13.6.1 目的 
该测试是模拟安装在车门或盖板处的元件以及开关时有高加速的元件的加速力测试。 
测试元件耐松动和材料疲劳性。 
 
13.6.2 测试 
测试标准见 DIN EN 60068‐2‐29，参数如下： 
 
表 63：M‐06 机械耐久冲击测试参数 
样件运行方式 
运行方式Ⅱ.c 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种运行模式（见章
节 10），则元件在要求运行方式Ⅱ.c 的期间内，所有与运行
方式Ⅱ.c 相关的机械冲击时间要相同。 
峰值加速 
300m/s2 
冲击时长 
6ms 
冲击形式 
半正弦 
冲击次数 
安装区域 
冲击次数 
驾驶侧门 
100000 
副驾驶和后座侧门 
50000 
后盖/尾门 
30000 
发动机舱盖 
3000 
如果元件在多个区域安装，则选择最大冲击测试 
安装位置 
测试设备上的样件位置必须和车辆上的安装位置符合一致 
样件数量 
6 
 
13.6.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 86 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14  气候要求和测试 
 
14.1 K‐01 高温/低温老化 
 
14.1.1 目的 
该测试是模拟元件储存和运输时的热负荷。 
测试元件在高温或者低温如运输时（飞机，轮船集装箱））的稳定性。 
如果该测试在顺序测试时进行，则所有元件的初始条件都要一致。 
 
14.1.2 测试 
表 64：K‐01 高温/低温老化测试参数 
样件运行方式 
运行方式Ⅰ.a 
测试时间和测试温度 
每 24h 进行 2 次循环（Tmin 老化 12 小时以及 Tmax 老化 12
小时） 
样件数量 
根据设计任务书中的测试流程规划确定 
 
14.1.3 要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 87 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.2 K‐02 温度分级测试 
 
14.2.1 目的 
 
该测试是模拟元件在不同环境温度下的运行状况。 
测试元件在工作温度范围的小区间内可能出现的功能失效时的稳定性。 
 
14.2.2 测试 
表 65：K‐02 温度分级测试参数 
样件运行方式 
参数测试（功能测试）过程中运行方式Ⅱ.c，其他情况运行方式Ⅱ.a
测试温度 
样件依据图 32 中的温度曲线进行测试，每级温度变化为 5℃ 
测试流程 
样件从每个温度级开始直到达到恒定状态（参见章节 10.4），接着
根据章节参数测试（参见章节 10.4）进行参数测试（功能测试） 
样件数量 
6 
 
图 32：温度分级测试温度曲线 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,min 和 Tkühl,max 温度范围进行。超出冷却液温度范
围的，只有环境温度才能变化。 
 
14.2.3 要求 
样件在每个参数测试（功能测试）中的所有参数必须在规定范围内。 
 
 
 
 
 
 
 
 
 
 
 
温度 
时间


### 第 88 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.3 K‐03 低温运行 
 
14.3.1 目的 
该测试是模拟元件低温负荷要求。 
测试元件在极度低温状态下长时间停车或者行驶之后的功能状况。 
 
14.3.2 测试 
测试标准见 DIN EN 60068‐2‐1，测试 Ab，参数如下： 
 
表 66：K‐03 低温运行测试参数 
样件运行方式 
运行方式Ⅱ.a                12h 
UBmin 时运行方式Ⅱ.c    12h 
运行方式Ⅱ.a                12h 
UB 时运行方式Ⅱ.c        12h 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种运行模式（见章节
10），则元件在要求运行方式Ⅱ.c 的期间内，所有与运行方式
Ⅱ.c 相关的运行时间要相同。 
测试时间 
48h 
测试温度 
Tmin 
样件数量 
6 
 
散热零件的测试标准也可以参见 DIN EN 60068‐2‐1，测试 Ab。 
如有冷却液管路，则最低冷却液温度调整到 Tkühl,min。 
 
输出功率高的元件在运行方式Ⅱ.c 测试时，在 Tmin 时因自身发热导致的测试箱温度升高是允
许的，但必须委托方和承接方协商一致。 
 
14.3.3 要求 
 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 89 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.4 K‐04 重新喷漆温度 
 
14.4.1 目的 
该测试是模拟重新喷漆时元件的负荷，是测试元件在热作用下钎焊、粘接、压焊、焊接时裂
纹形成以及密封件和塑壳裂纹的稳定性。 
14.4.2 测试 
表 67：K‐04 重新喷漆温度测试参数 
样件运行方式 
运行方式Ⅱ.a 
测试时间和测试温度 
130℃维持 15min，110℃维持 1h 
样件数量 
6 
 
如有冷却液管路，冷却液静止时的温度设置在 TRT. 
 
14.4.3 要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 90 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.5 K‐05 温度冲击（元件） 
 
14.5.1 目的 
该测试是模拟车辆运行时交变的温度对元件热负荷的要求。 
是测试元件在热作用下钎焊、粘接、压焊、焊接时裂纹形成以及密封件和塑壳裂纹的稳定性。 
 
14.5.2 测试 
测试标准见 DIN EN 60068‐2‐14，参数如下： 
 
表 68：K‐05 温度冲击（元件）测试参数 
样件运行方式 
运行方式Ⅰ.b 
低温/测试池温度 
Tmin 
高温/测试池温度 
Tmax 
高温/低温停留时间 
温度稳定后 15min（见章节 10.4） 
转化时间（空气‐空气，介质‐介质） ≤30s 
Nc  测试的测试溶液 
汽车运行时元件使用的液体 
测试 
依据 DIN  EN  60068‐2‐14Na，适用于非长期在液体中运
行的元件。 
 
依据 DIN  EN  60068‐2‐14Nc，适用于长期在液体中运行
的元件（IP X8）。 
样件的所有面至少浸入液体中 25mm 
循环次数 
100 
样件数量 
6 
 
14.5.3 要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（大）验证。 
 
介质‐介质测试时还需注意： 
不允许液体渗透，只有整个测试顺序（章节 12.2 中的测试流程计划）结束之后才允许对样
件进行拆解。 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 91 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.6 K‐06 运行时外舱盐雾测试 
 
14.6.1 目的 
该测试是模拟元件在特定区域或者冬季街道上遇到带盐分的空气和水的情况。 
是测试元件在盐分负荷下耐缺陷形成性，如：由于盐分侵蚀导致的元件短路和漏电。 
 
14.6.2 测试 
测试标准见 DIN EN 60068‐2‐11，参数如下： 
 
表 69：K‐06 汽车运行外舱盐雾测试参数 
样件运行方式 
在喷射期间：运行方式Ⅱ.a 运行 1h,运行方式Ⅱ.c 运行 1h(间歇性) 
 
静止期间：运行方式Ⅱ.a 
测试温度 
35℃ 
测试循环 
见图 33，每次测试循环由 8h 喷射时间和 4h 静止时间组成 
测试循环次数 
底盘和发动机舱元件：循环 12 次 
其他元件：循环 8 次 
样件数量 
6 
 
进行测试时仿照元件在汽车上的安装位置。 
 
如有冷却液管路，冷却液温度调整到测试温度。 
 
 
 
 
 
 
 
 
 


### 第 92 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
图 33：汽车运行时外舱盐雾测试  ‐  喷射阶段 
 
14.6.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
喷射阶段
静止阶段
关 
开 
电气运行 
循环 1 次


### 第 93 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.7 K‐07 运行时内舱盐雾测试 
 
14.7.1 目的 
该测试是模拟元件在特定区域遇到带盐分的空气的情况。 
是测试元件在盐分负荷下耐缺陷的形成，如：由于盐分侵蚀导致的元件短路和漏电。 
 
14.7.2 测试 
测试标准见 DIN EN 60068‐2‐11 Ka，参数如下： 
 
表 70：K‐07 汽车运行内舱盐雾测试参数 
样件运行方式 
在喷射期间：运行方式Ⅱ.a 运行 55min,运行方式Ⅱ.c 运行
5min(间歇性) 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种运行模式（见章
节 10），则元件在要求运行方式Ⅱ.c 的期间内，所有与运行方
式Ⅱ.c 相关的运行时间要相同。 
 
静止期间：运行方式Ⅱ.a 
测试温度 
35℃ 
测试循环 
见图 34，每次测试循环由 8h 喷射时间和 4h 静止时间组成 
循环次数 
2 
样件数量 
6 
进行测试时仿照元件在汽车上的安装位置，受托方提出测试装置（安装位置，盖板，挡板，
运行情况），并与委托方协商确认且记录存档。 
如有冷却液管路，冷却液温度调整到测试温度。 
 
 
 
 
 
 


### 第 94 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
图 34：汽车运行时内舱盐雾测试  –  喷射阶段 
 
14.7.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
开 
关 
电气运行 
静止阶段
喷射阶段
循环 1 次


### 第 95 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.8 K‐08 湿热循环 
 
14.8.1 目的 
该测试通过模拟汽车运行时高湿度的温度循环变化来测试元件热负荷大小，测定元件湿热稳
定性。 
 
14.8.2 测试 
测试标准见 DIN EN 60068‐2‐30，参数如下： 
 
表 71：K‐08 湿热循环测试参数 
样件运行方式 
运行方式Ⅱ.a 
总测试时间 
144h 
测试模式 
模式 1 
最高测试温度 
55℃ 
循环次数 
6 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,min 和 Tkühl,max 温度范围进行。超出冷却液温度范
围的，只有环境温度才能变化。 
 
14.8.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
另外在达到最高和最低测试温度后必须分别进行参数测试（功能测试）。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 96 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.9 K‐09 湿热循环（带冰冻） 
 
14.9.1 目的 
该测试通过模拟汽车运行时高湿度的温度循环交替变化来测试元件热负荷（包括冰冻）大小，
测定元件湿热稳定性。 
 
14.9.2 测试 
测试标准见 DIN EN 60068‐2‐38，参数如下： 
 
表 72：K‐09 湿热循环（带冰冻）测试参数 
样件运行方式 
运行方式Ⅱ.a 运行 40min,运行方式Ⅱ.c 运
行,10min(间歇性) 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种
运行模式（见章节 10），则元件在要求运行
方式Ⅱ.c 的期间内，所有与运行方式Ⅱ.c 相
关的运行时间要相同。 
总测试时间 
240h 
循环次数 
10 
循环测试顺序 
一开始的 5 个循环必须带有冷冻阶段，剩下
的循环执行则无需冷冻阶段 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,min 和 Tkühl,max 温度范围进行。超出冷却液温度范
围的，只有环境温度才能变化。 
 
14.9.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 97 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.10 K‐10 防水保护  – IPX0 至 IPX6K 
 
14.10.1 目的 
测试是模拟元件的防水性。 
测定对元件施加融冰水、雨水、喷水时的功能稳定性。 
 
14.10.2 测试 
测试标准见 ISO 20653 参数如下： 
 
表 73：K‐10 防水保护– IPX0 至 IPX6K 的测试参数 
样件运行方式 
运行方式Ⅱ.a 运行 1min,运行方式Ⅱ.c 运行 1min(间歇性) 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种运行模式（见章
节 10），则元件在要求运行方式Ⅱ.c 的期间内，所有与运行方
式Ⅱ.c 相关的运行时间要相同。 
要求的防护等级 
参照元件设计任务书规定 
样件数量 
6 
 
14.10.3 要求 
必须达到设计任务书中要求的防护等级（标准见 ISO 20653）。 
 
不允许渗水，只有整个测试顺序结束后才能打开样件。 
 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 98 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.11 K‐11 高压清洗/蒸汽喷射清洗 
 
14.11.1 目的 
测试模拟汽车清洗时元件的防水要求。 
测试元件高压/蒸汽清洗时的功能稳定性。 
 
14.11.2 测试 
测试标准见 ISO 20653，参数如下： 
表 74：测试参数 
样件运行方式 
运行方式Ⅱ.a 
要求的防护等级 
IP X9K 
水压 
蒸汽喷射时的最小压力为 10000kPa(100bar)，直接在喷嘴口测量 
水温 
80℃ 
执行 
从汽车各个可接触到的方向对样件进行喷射 
样件数量 
6 
 
14.11.3 要求 
必须达到防护等级 IP X9K（见标准 ISO 20653）. 
 
不允许渗水，只有章节 12.2 中测试流程计划中整个测试顺序结束后才能打开样件。 
 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 99 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.12 K‐12 溅水温度冲击 
 
14.12.1 目的 
该测试模拟汽车行驶经过积水区的元件防溅水要求。 
测试涉水时元件突然变冷的功能稳定性。 
 
14.12.2 测试 
表 75：K‐12 溅水温度冲击测试参数 
样件运行方式 
如果元件是无负载运行：整个测试过程按照Ⅱ.a 
 
如果元件时带负载运行：依据图 35 间歇性执行运行方式Ⅱ.a
和运行方式Ⅱ.c（运行状态下） 
测试过程 
样件加热至测试温度，然后依据图 35 进行循环溅水测试，样
件的整个外廓宽度必须溅到水。 
循环时间 
30min 
测试温度 
Tmax 
溅水测试介质 
自来水，含 3%  的亚利桑那沙，细度按照 ISO 12103‐1.必须确
保混合均匀。 
溅水温度 
0 至+4℃ 
溅水喷嘴 
见图 36 
溅水时间 
3s 
流经水量 
每次溅水/喷嘴 3‐4L 
样件与喷嘴间距 
300‐350mm 
循环次数 
100 
样件数量 
6 
 
进行测试时仿照元件在汽车上的安装位置，受托方提出测试装置（安装位置，盖板，挡板，
运行情况），并与委托方协商确认且记录存档。 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,min 和 Tkühl,max 温度范围进行。超出冷却液温度范
围的，只有环境温度才能变化。 
 
测试装置见图 37 
 
 


### 第 100 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
图 35：溅水测试溅水时间 
 
图 36：溅水测试—喷嘴 
 
关 
开 
循环 1 次 
14:57min
3s
30min
运
行
方
式 
溅
水 
尺寸单位：mm


### 第 101 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
图 37：溅水测试装置 
 
14.12.3 要求 
不允许渗水，只有整个测试顺序结束后才能打开样件。 
 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
溅水喷嘴
300mm 到 350mm
溅水 
样件 


### 第 102 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.13 K‐13 浸水温度冲击 
 
14.13.1 目的 
该测试模拟元件进入水中的负荷要求。 
测试加热后的元件在浸入水中温度骤冷情况下的功能稳定性。 
 
14.13.2 测试 
测试标准见 ISO 20653，参数如下： 
 
表 76：K‐13 浸水温度冲击测试参数 
样件运行方式 
如果元件是带负载运行：运行状态下的Ⅱ.c 
 
如果元件不带负载运行：运行方式Ⅱ.a   
要求的防护等级 
IP X7 
测试过程 
样件加热至 Top,max，在 Top,max 停留 15min 直到温度恒定下来（见章
节 10.3）. 
然后将样件完全浸没到测试介质中 5s，测试件所有面必须浸入到
测试介质至少 25mm 处。 
测试介质 
0℃，5%的冷盐水 
浸水时间 
5min 
循环次数 
20 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,max 温度范围进行。超出冷却液温度范围的，只
有环境温度才能变化。 
 
14.13.3 要求 
不允许渗水，只有章节 12.2 中测试流程计划中整个测试顺序结束后才能打开样件。 
 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 103 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.14 K‐14 恒湿恒温 
 
14.14.1 恒湿恒温‐等级 1（#） 
 
14.14.1.1 目的 
该测试是模拟元件经受湿热的负载情况，测定元件是否能承受由于湿热导致的缺陷如：腐蚀、
迁移/枝状结构生长，塑料、密封件和浇注材料的膨胀和退化。 
 
14.14.1.2 测试 
测试标准见 DIN EN 60068‐2‐78，参数如下： 
 
表 77：K‐14 恒湿恒温‐等级 1 测试参数 
样件运行方式 
运行方式Ⅱ.a 
测试温度 
40℃ 
空气湿度 
相对湿度 93% 
测试时间 
21 天 
样件数量 
6 
 
14.14.1.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
此外每隔 7 天必须进行一次参数测试（功能测试）。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 104 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.14.2 恒湿恒温‐等级 2 
 
14.14.2.1 目的 
该测试是模拟元件在使用寿命期间经受湿热的负载情况，测定元件在遭受湿热导致的缺陷如：
腐蚀、迁移/枝状结构生长，塑料、密封件和浇注材料的膨胀和退化时的质量及可靠性。 
 
14.14.2.2 测试 
测试标准见 DIN EN 60068‐2‐78，参数如下： 
 
表 78：K‐14 恒湿恒温‐等级 2 测试参数 
样件运行方式 
运行方式Ⅱ.a 运行 47h,运行方式Ⅱ.c 运行 1h(间歇
性),直至测试结束 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种运行模
式（见章节 10），则元件在要求运行方式Ⅱ.c 的期
间内，所有与运行方式Ⅱ.c 相关的运行时间要相同。
测试时间 
按照元件设计任务书，章节 E.1（Lawson 模型） 
测试温度 
65℃ 
测试湿度 
93%相对湿度 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,max 温度范围进行。超出冷却液温度范围的，只
有环境温度才能变化。 
 
测试前需检查确认该测试条件（测试温度 65℃和相对湿度 93%）是否超过元件子件材质的
物理极限（如塑料水解）。如有必要在延长“Lawson 模型”的测试时间时可以和委托方协商
调整测试温度及湿度，（如改为 55℃和相对湿度 93%），这样就不会超过子件的物理极限。
但总体上必须遵守测试等级要求。相对湿度不能超过 93%。 
 
测试期间必须确保样件上不能发生冷凝（局部冷凝也不允许）。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 105 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.14.2.3 高温时性能降低的元件偏差测试 
在高温时（温度从 Top,max＜65℃起）性能降低的零部件（如 LCD 背光照明降低）：测试则不
按照表 78 的恒温 65℃下执行，而是按照表 79 中的参数实施： 
 
表 79：K‐14 元件高温时性能降低的恒湿恒温测试参数 
样件运行方式 
按照图 38 
测试时间 
按照设计任务书章节 E.1 规定（Lawson‐模型）规定 
65℃至 Top,max 间的爬坡曲线时间不计算在测试时间里 
测试温度 
按照图 38 
温度梯度的选择要保证样件不会冷凝融化 
测试湿度 
93%相对湿度 
时间间隔 t1 
47h 
时间间隔 t2 
1h 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,max 温度范围进行。超出冷却液温度范围的，只
有环境温度才能变化。 
 
 
图 38：高温超过 Top,max 时元件性能降低的测试温度曲线 
 
 
 
 
 
 
 
 


### 第 106 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.14.2.4 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 107 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.15 K‐15 冷凝和气候测试 
 
14.15.1 目的 
该测试是模拟汽车上电气组件冷凝情况。 
它是用来评估电气元件冷凝稳定性。 
 
14.15.2 测试 
 
用不带外壳的电气组件进行测试，参数如下： 
 
表 80：K‐15 电气组件冷凝测试参数 
样件运行方式 
运行方式Ⅱ.a 
 
此外还需进行参数测试（功能测试），见“测试过程”一栏 
测试装置 
可以进行冷凝的测试箱（可以调节水浴，通过水浴可以将必要的水量
转化成水蒸气）。 
冷凝期间关掉温度箱控制调节器，通过水浴来调节测试温度。 
测试过程 
1. 将温度测试箱在初始温度下保持 60min,以确保样件温度稳定下
来。然后进行冷凝。 
2. 开始后 30 分钟内到冷凝结束前 30 分钟（见图 41），每次水浴—
温度上升 10K 都要进行参数测试（功能测试），但只能在电压 UB
状态下进行。 
参数测试（功能测试）的消耗功率要尽可能低，时间不超过 2min,
否则样件会很快变热，无法进行冷凝。 
测试温度 
见图 41 
实验室相对湿度 
见图 41 
冷凝期间相对湿度必须达到 100% (0%,‐5%)。 
测试时间 
32.5h(5 次循坏，每次循环 6.5h) 
测试介质 
蒸馏水，导电率不超过 5μS/cm 
样件位置 
和安装在汽车上的位置一样。 
为保持该位置可采用塑料支架。 
 
如果样件用在多处部位，则需在实验室按照多个位置进行模拟。 
测试装置 
见图 39 
 
测试期间如图 40 采用塑料罩，以将不同风速下的不良因素降至最低。
塑料罩斜面要指向实验室门一侧。塑料罩尺寸要与实验室尺寸相匹
配。塑料罩和实验室墙壁间距为实验室宽度/深度的 10%，但至少要
达到 8cm. 
根据标准 DIN EN ISO 6270‐2 塑料罩上方倾斜角α应≥12°。 
测试条件 
在最终确定电路板设计（硬件冻结）前进行一次冷凝测试，但要在接
近批产条件的电气组件上进行，以优化组件对冷凝的敏感性，如设计
变更或线路变更以达到优化目的。 
 


### 第 108 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
组件制造如有变更（如电路板、焊剂、焊料、焊接工序、设计、标准
化存储或子件）必须重新进行测试。 
循环次数 
5 
样件数量 
6 个组件 
 
 
图 39：K‐15a 组件冷凝测试装置 
 
 
图 40：塑料罩 
 
 
 
 
 
 
 
 
 
 
加热 
热交换器（冷却）
测试箱门 
样件 
实验室 
温度传感器/调节器
熔化阶段
干燥阶段


### 第 109 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
 
 
 
 
图 41：K‐15a 冷凝测试流程 
14.15.1.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
此外还必须检查电气元件是否有迁移（如银锡迁移）现象，是否有枝状结构产生。 
 
不允许产生电气化学迁移现象和枝状结构。 
 
其他变化（如腐蚀、脏污）需在测试报告中注明，并与委托方一起评估。 
 
测试报告中必须附上下列资料： 
1. 测试箱设置 
2. 一次循环参数对比（SOLL/IST） 
3. 所有 5 次循环的参数对比（SOLL/IST） 
示例见附录 F。 
1） 空气温度达到 75℃开始干燥阶段 
2） 样件必须干燥  Frel＜50%（相对湿度） 
3） 气候箱调节器转为水浴调节 
1 次循环 
冷凝阶段
干燥阶段
UB 时的参数测试（功
能测试）
实验室湿度 
水浴温度＜20℃ 
未定义的湿度曲线 
水浴温度±1K 
测试温度±3K 
**）实验室湿度和温度标记，温差＜15℃ 


### 第 110 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.15.2 K‐15b 带防水外壳的元件气候测试 
 
14.15.2.1 目的 
该测试是模拟元件在使用寿命期间采用防水外壳所经受湿热的负荷情况。 
测定元件在遭受湿热导致的缺陷如：腐蚀、迁移/枝状结构生长，塑料、密封件和浇注材料
的膨胀和退化时的质量及可靠性。 
 
14.15.2.2 测试 
必须用整个元件（带外壳的仪器、控制器、机电元件…）进行测试. 
 
测试顺序见图 42： 
 
图 42：K‐15b 带防水外壳的元件气候测试流程 
 
测试 1,3 和 5： 
测试标准见 DIN EN 60068‐2‐78,测试参数如下： 
 
表 81：K‐15b 带防水外壳的元件气候测试参数，测试 1,3 和 5 
样件运行方式 
运行方式Ⅱ.a 
测试开始后 12 小时进行一次参数测试（功能
测试）。，然后每 24 小时进行一次参数测试
（功能测试）。 
每个测试的测试时长 
见零部件设计任务书 
 
注意： 
K‐15b（测试 1 到 5）的总测试时长需与 K‐14
恒湿恒温—等级 2 的测试时长一致。 
其中测试 2 和测试 4 时长分别为 240 小时。
剩下的时长分摊到测试 1.3 和 5 上，各占三
分之一时间： 
测试时长 测 试 1=测试时长 测 试 3=测试时长 测 试
5=1/3(总测试时长—2*240 小时) 
测试温度 
65℃ 
测试湿度 
93%的相对湿度 
样件数量 
6 
测试 1 
 
恒湿恒温 
测试 2 
 
带冰冻的
湿热循环 
测试 3 
 
恒湿恒温 
测试 4 
 
带冰冻的
湿热循环 
测试 5 
 
恒湿恒温 


### 第 111 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
如果有冷却液管路，则冷却液温度按照 Tkühl,max 温度范围进行。超出冷却液温度范围的，只
有环境温度才能变化。 
 
测试 2 和 4： 
测试标准见 DIN EN 60068‐2‐38,测试参数如下： 
 
表 82：K‐15b 带防水外壳的元件气候测试参数，测试 2 和 4 
样件运行方式 
运行方式Ⅱ.a 
测试开始后 12 小时进行一次参数测试（功能
测试）。，然后每 24 小时在环境温度下进行一
次参数测试（功能测试）。 
每个测试的测试时长 
240h 
循环次数 
10 
测试顺序 
前 5 次循环需带有低温阶段，剩下的循环无
需低温。 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,min 和 Tkühl,max 温度范围进行。超出冷却液温度范
围的，只有环境温度才能变化。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 112 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.15.2.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4.2 通过持续的参数监控进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 113 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.16 K‐16 温度冲击（不带外壳） 
 
14.16.1  目的 
该测试模拟的不是实际负荷情况。 
它是用来发现组件机械连接范围如焊接点处的缺陷。 
 
该测试仅能在不带外壳的组件及机械零件上进行。 
 
14.16.2  测试 
测试标准见 DIN EN 60068‐2‐14,参数如下： 
 
表 83：K‐16 温度冲击（不带外壳）测试参数 
样件运行方式 
运行方式Ⅰ.a 
最低温度 
Tmin                                                                        
最高温度 
Tmax 
高低温停留时间 
待温度稳定后 15min 
转移时间 
≤10s 
循环次数 
300 
样件数量 
6 个组件 
 
14.16.3  要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 114 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.17 K‐17 日照 
 
14.17.1 目的 
该测试模拟太阳辐射和紫外线对元件的影响。 
用于测试元件抵抗材料疲劳损害的稳定性，如：裂纹和褪色。 
 
14.17.2 测试 
测试标准见 DIN 75220，参数如下： 
 
表 84：K‐17 日照测试参数 
样件运行方式 
运行方式Ⅰ.a 
使用的测试曲线 
测试曲线依据 DIN  75220 根据元件安装空间
进行应用                                                              
外舱元件 
曲线 Z‐Out 运用，见标准 DIN 75220 的表 2 和
表 5 
内舱元件 
曲线 Z‐IN1 运用，见标准 DIN 75220 
测试时间 
25 天（15 天干燥，10 天潮湿） 
循环次数 
1 
样件数量 
6 
 
14.17.3 要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（大）验证。 
 
此外还需用肉眼对样件进行外观检查，样件变化或损坏需注明在测试报告中，且与委托方一
起评估。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 115 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
14.18 K‐18 有害气体测试 
 
14.18.1 目的 
该测试是模拟有害气体对元件，尤其是对端子和开关的影响。 
用于测试元件耐缺陷形成的稳定性，如：腐蚀和元件损坏。 
 
14.18.2 测试 
测试标准见 DIN EN 60068‐2‐60 方法 4，参数如下： 
 
表 85：K‐18 有害气体测试参数 
样件运行方式 
运行方式Ⅰ.b 
温度 
TRT 
湿度 
75%的相对湿度 
有害气体浓度 
SO2 
0.2ppm 
H2S 
0.01ppm 
NO2 
0.2ppm 
Cl2 
0.01ppm 
测试时间 
21 天 
样件数量 
6 
 
14.18.3 要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（大）验证。 
 
此外必须测量开关和端子的接触电阻，测试结果必须在规定范围内。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 116 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
15  化学要求和测试 
 
15.1 C‐01  化学测试 
15.1.1 目的 
该测试是模拟元件对不同化学试剂的要求。 
用于测定元件的外壳受到化学影响和通过化学作用受到功能影响的稳定性。 
 
15.1.2 测试 
表 86：化学测试参数 
样件运行方式 
见零部件设计任务书 
化学试剂 
见零部件设计任务书 
常见化学试剂见表 87。 
温湿度处理 
如果没有其他特殊说明，样件和化学试剂放置在正常气候条件下 
测试过程 
测试标准见 ISO 16750 第 5 部分： 
1.化学试剂在样件达到 TRT 时进行应用，如果在元件设计任务书中没
有特殊说明，则必须按照表 88 选择一个适合的应用方式，并在测试
报告中记录选择的应用方式。必须确保样件被化学试剂完全覆盖。 
2.然后样件必须在表 87 给定的温度和规定的有效时间内进行存放。
样件数量 
每个化学试剂一个样件 
一个测试件用于不同试剂的多次使用必须与委托方确认 
 
必须注意化学试剂的安全和警告说明。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 117 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
15.1.2.1 化学试剂 
表 87：化学试剂一览表（也可参见 ISO 16750‐5） 
ID 
化学试剂 
样
件
温  度
有效时
间 
化学试剂举例 
1 
柴油 
Tmax 
22h 
EN 590 
2 
生态柴油 
Tmax 
22h 
EN 14214 
3 
无铅汽油 
TRT 
10min 
EN 228 
4 
煤油 
TRT 
10min 
ASTM 1655 
5 
甲醇 
TRT 
10min 
CAS 67‐56‐1 
6 
发动机机油 
Tmax 
22h 
多级通用机油 SAE 0W40,API SL/CF 
7 
差速器油 
Tmax 
22h 
双曲面齿轮油 SAE 75W140,API GL‐5 
8 
变速箱油 
Tmax 
22h 
ATF Dexron III  
9 
液压油 
Tmax 
22h 
DIN 51 524‐3（HVLP ISO VG 46） 
10 
润滑脂 
Tmax 
22h 
DIN 51 502（KP2K‐30） 
11 
硅油 
Tmax 
22h 
CAS 63148‐58‐3（AP 100） 
12 
蓄电池液 
TRT 
22h 
37% H2SO4 
13 
刹车液 
Tmax 
22h 
ISO 4926 
14 
防冻液 
Tmax 
22h 
乙烯乙二醇（C2H6O2）‐水（混合比例
1:1） 
15 
尿素 
Tmax 
22h 
ISO 22241‐1 
16 
空腔保护蜡 
TRT 
22h 
如：底盘保护，公司 Teroson1 
17 
防腐剂 
TRT 
22h 
如：W550（公司 Pfinder）1 
18 
去防腐剂 
Tmax 
22h 
如：Friapol750（公司 Pfinder）1 
19 
挡风玻璃清洗液 
TRT 
2h 
5%阴离子表面活性剂，蒸馏水 
20 
自动化的汽车清洗液 
TRT 
2h 
CAS 25155‐30‐0 
CAS 9004‐82‐4 
21 
内部清洗剂/驾驶舱喷雾剂 
TRT 
2h 
如：驾驶舱喷雾剂（公司 Motip）1 
22 
玻璃清洗剂 
TRT 
2h 
CAS 111‐76‐2 
23 
轮辋清洁剂 
TRT 
2h 
如：Xtreme（Sonax）1 
24 
冷清洗剂 
TRT 
22h 
如：P3‐Solvclean AK（公司 Henkel）1
25 
丙酮 
TRT 
10min 
CAS 67‐64‐1 
26 
清洗汽油 
TRT 
10min 
DIN 51635 
27 
含氨气的清洗剂 
TRT 
22h 
如：Ajax（公司 Henkel）1 
28 
提纯酒精 
TRT 
10min 
CAS 64‐17‐5（Ethanol） 
29 
接触喷雾剂 
Tmax 
22h 
如：WD 401 
30 
汗水 
TRT 
22h 
DIN 53160 
31 
化妆品如：润肤膏 
TRT 
22h 
如：Nivea，Kenzo1 
32 
含咖啡因和糖分的清凉饮料 
TRT 
22h 
Cola（可乐） 
33 
除霜剂（航空） 
TRT 
2h 
SAE AMS 1435A 
34 
E85 燃料 
TRT 
10min 
DIN 51625 
 
其他化学试剂 
 
 
 
 
1)生产商举例，具体化学试剂与专业部门确认。 
 


### 第 118 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
表 88：应用方式 
代码 
应用方法 
I  
喷雾 
II  
用毛刷涂 
III  
擦拭（如：用棉质擦布） 
IV  
浇注 
V  
短暂浸没 
VI 
浸没 
 
15.1.3 要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（大）验证。 
 
标签和标识如有变化需在测试报告中注明，并与委托方协商确认。   
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 119 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
16  耐久测试 
 
16.1 L‐01 机械/液压耐久测试 
 
16.1.1 目的 
该测试是模拟汽车使用寿命内功能/操作循环。 
用于测试元件功能操作循环的质量和可靠性，如：刹车操作，座椅调节循环，开关/按键操
作等等。 
 
16.1.2 测试 
测试详情必须符合零部件设计任务书中定义的功能/操作循环。 
表 89：L‐01 机械/液压耐久测试参数 
样件运行方式 
运行方式Ⅱ.c，符合功能/操作循环 
测试温度 
功能/操作循环就是在温度集中规定的温度下，时间按照它们
的百分比执行 
功能/操作循环次数 
参照设计任务书中规定 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,min 和 Tkühl,max 温度范围进行。超出冷却液温度范
围的，只有环境温度才能变化。 
 
11.1.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，必须
通过持续的参数监控进行验证。 
只有测试过程中的无法完整对元件功能进行监测时，才要在测试时长的 25%、50%和 75%时
再测量并根据测试流程计划进行参数测试。 
 
中间测试必须按照参数测试（大）进行验证。 
 
持续参数监控的数据必须关于就迁移、趋势和突出特性或异常进行评估。 
 
针对冷却液管路的元件： 
有铜镀层的元件，在测试结束后要对这些铜件用 20 倍放大的显微镜进行观察，不允许有缺
陷和铜腐蚀产生。 
 
 
 
 
 
 
 
 
 
 


### 第 120 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
16.2 L‐02 高温耐久测试 
 
16.2.1 目的 
该测试模拟车辆使用寿命内对元件热负荷的要求。 
用于测试元件在热作用下耐缺陷的质量和可靠性，如：扩散，迁移和氧化。 
 
16.2.2 测试 
 
16.2.2.1  不和冷却液管路连接的元件且在高温下性能不会降低的元件测试 
测试标准见 DIN EN 60068‐2‐2，参数如下： 
表 90：L‐02 高温耐久测试参数—不和冷却液管路连接的元件且在高温下性能不会降低的元
件测试 
样件运行方式 
运行方式Ⅱ.c：47h,运行方式Ⅱ.a:1h（间歇性） 
测试时间 
章节 10.1 中的相关运行模式：按照附录 C.1（Arrhenius 模式）计
算测试各部分测试时间；停车或离网停车运行模式一般不用考虑。
各部分测试时间的总和即是总测试时长，并规定在设计任务书中
测试温度 
Tmax 
样件数量 
6 
 
16.2.2.2 不和冷却液管路连接的元件但在高温下性能会降低的元件测试 
从高温时 Top,max 起性能降低的零部件（如 LCD 背光照明降低）：表 91 中的测试不是按照 Tmax
测试温度，而是按照下列温度参数实施： 
 
测试方法见标准 DIN EN 60068‐2‐2,参数如下： 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 121 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
表 91：L‐02 高温耐久测试参数—不和冷却液管路连接的元件但在高温下性能会降低的元件
测试 
 
样件运行方式 
见图 43 
测试时间 
章节 10.1 中的相关运行模式：按照附录 C.3(高温下性能降低的
Arrhenius‐模型应用)计算测试各部分测试时间；停车或离网停车
运行模式一般不用考虑。 
各部分测试时间的总和即是总测试时长，并规定在设计任务书中
Tmax 和 Top,max 之间的各自的爬坡时间不计算在测试时间内。 
测试温度 
见图 43 
间隔时间 t1 
按照附录 C.3 进行计算，并规定在设计任务书中。 
间隔时间 t2 
按照附录 C.3 进行计算，并规定在设计任务书中。 
样件数量 
6 
 
 
*）当 T＞Top,max 时，允许性能降低。 
图 43：高温时性能降低的元件温度测试曲线 
 
 
 
 
 
 
 
 
 
 


### 第 122 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
16.2.2.3  连接到冷却液管路的元件测试 
测试方法见标准 DIN EN 60068‐2‐2,参数如下： 
 
表 92：L‐02 高温耐久测试参数—和冷却液管路连接的元件测试 
 
样件运行方式 
运行方式Ⅱ.c：47h,运行方式Ⅱ.a:1h（间歇性） 
测试时间 
章节 10.1 中的相关运行模式：按照附录 C.5(冷却液管路元件的
Arrhenius‐模型应用)计算测试各部分测试时间；停车或离网停车
运行模式一般不用考虑。 
各部分测试时间的总和即是总测试时长，并规定在设计任务书中
环境测试温度 
按照附录 C.5（冷却液管路元件的 Arrhenius‐模型应用） 
冷却液测试温度 
按照附录 C.5（冷却液管路元件的 Arrhenius‐模型应用） 
样件数量 
6 
 
16.2.3 要求 
测试件必须在测试前、测试中和测试后都是功能齐全的，并且所有的关键参数必须在规定范
围内，通过持续的参数监控进行确认。 
只有在测试过程中无法对元件功能进行完整监测时，才要在中间测试时间和参数测试的 25%，
50%和 75%时再测量。 
 
中间测量必须进行参数测试（大）验证。 
 
持续参数监控的数据必须关于漂移、趋势和突出特性或异常进行评估。 
 
冷却液管路上的元件： 
冷却液管路上含有铜件覆盖的元件，再测试结束后用 20 倍放大显微镜对铜件进行观察，不
允许出现明显缺陷和铜腐蚀。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 123 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
16.3 L‐03 温度变化耐久测试 
 
16.3.1 目的 
该测试通过温度变化测试模拟车辆使用寿命内对元件的热负荷要求。 
用来测试元件在热负荷作用下抑制缺陷形成的质量和可靠性，如：焊接、粘接、压焊、焊接
连接部位，密封件和护套的老化和裂纹。 
 
16.3.2 测试 
测试标准见 DIN EN 60068‐2‐14，参数如下： 
16.3.2.1 不和冷却液管路连接的元件且在高低温下性能不会降低的元件测试 
 
表 93：L‐03 温度变化耐久测试参数—不和冷却液管路连接的元件且在高低温下性能不会降
低的元件测试 
样件运行方式 
按照图 44：运行方式Ⅱ.a 和运行方式Ⅱ.c（间歇执行） 
温度曲线 
按照图 44 
最小测试温度 
Tmin 
最大测试温度 
Tmax 
温度梯度 
4℃/min 
如果这个温度梯度在测试仪中无法实现，则温度梯度可以与委托方
协商，将数值降低至最小 2℃/min。 
Tmin 和 Tmax 停留时间 
待温度完全稳定后 15min 
循环次数 
总测试循环次数需考虑相关运行模式（章节 10.1），按照附录
D.1(Coffin‐Manson 模型)进行计算，且规定在零部件设计任务书中。
样件数量 
6 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 124 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
图 44：L‐03 温度变化耐久测试温度曲线—不和冷却液管路连接的元件且在高低温下性能不
会降低的元件测试 
 
16.3.2.2 不和冷却液管路连接的元件但在高低温下性能会降低的元件测试 
Top,min 以下温度和 Top,max 以上温度起性能降低的零部件（如 LCD 背光照明降低），测试参数如
下： 
 
表格 94：L‐03 温度变化耐久测试参数  –不和冷却液管路连接的元件但在高低温下性能会降
低的元件测试 
样件运行方式 
依据图 45 运行方式Ⅱ.a 和Ⅱ.c 
温度曲线 
依据图 45 
最小测试温度 
Tmin 
最大测试温度 
Tmax 
温度梯度 
4℃/min 
Tmin，Tmax，Top,min 和 Top,max 停留时间
待温度完全稳定后 15min 
循环次数 
总测试循环次数需考虑相关运行模式（章节 10.1），
按照附录 D.1(Coffin‐Manson 模型)进行计算，且规定
在零部件设计任务书中。 
样件数量 
6 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 125 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
图 45：温度曲线—低温或者高温下性能降低的元件测试 
 
16.3.2.3 冷却液管路元件 
冷却液管路元件测试参数如下： 
 
表 95：L‐03 温度变化耐久测试—冷却液管路元件测试 
样件运行方式 
依据图 44 和 45 运行方式Ⅱ.a 和Ⅱ.c 
温度曲线 
依据图 44 和 45 
最小测试温度 
Tmin 和 Tkühl,min 
最大测试温度 
Tmax 和 Tkühl,max 
温度梯度 
4℃/min 
Tmin，Tmax，Top,min 和 Top,max 停留时间
待温度完全稳定后 15min 
循环次数 
总测试循环次数需考虑相关运行模式（章节 10.1），
按照附录 D.3(冷却液管路元件的 Coffin‐Manson‐模型
应用)进行计算，且规定在零部件设计任务书中。 
样件数量 
6 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
只有当 Top,min＞Tmin 才需要 
只有当 Top,max＜Tmax 才需要 


### 第 126 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
16.3.3  要求 
测试件必须在测试前、测试中和测试后都是功能齐全的，并且所有的关键参数必须在规定范
围内，通过持续的参数监控进行确认。 
只有在测试过程中无法对元件功能进行完整监测时，才要在中间测试时间和参数测试的 25%，
50%和 75%时再测量。 
 
中间测量必须进行参数测试（大）验证。 
 
持续参数监控的数据必须关于漂移、趋势和突出特性或异常进行评估。 
 
冷却液管路上的元件： 
冷却液管路上含有铜件覆盖的元件，再测试结束后用 20 倍放大显微镜对铜件进行观察，不
允许出现明显缺陷和铜腐蚀。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 127 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
附录 A(标准) 
测试流程 
A.1  测试流程计划 
测试流程见零部件设计任务书。 
 
测试选择表中不必要的测试需从测试流程计划中删掉。 
 
如有必要对测试顺序进行调整，则可以修改测试流程计划。 
 
如果在顺序测试中用循环湿热测试（带霜冻）代替湿热测试，则在平行测试中可以取消湿热
循环测试（带霜冻）。 
 
所有元件从测试 M‐01”自由跌落测试”起用原始插件或适配器进行测试。 


### 第 128 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
K‐01 高温/低温老化 
K‐10 防水保护
IPX0 至 IPX6K 
K‐06 运行时盐雾测试，外舱 
M‐03 粉尘测试（IP5KX 和 IP6KX） 
参数测试（小）
参数测试（小） 
K‐08 湿热循环 
（增加 2 个备用件用于跌落测试） 
K‐02 温度梯级测试 
2 备用 
M‐01 跌落测试 
参数测试（大） 
梯级温度测试（最终测试）
固定 6 个测试件（备用件也可以） 
参数测试（大） 
参数测试（小） 
参数测试（大） 
参数测试（小） 
M‐06 机械耐久冲击 
M‐05 机械冲击 
参数测试（大） 
参数测试（小） 
K‐04 重新喷漆温度 
参数测试（小） 
K‐03 低温运行 
K‐05 温度冲击（元件） 
M‐02 石击测试 
M‐04 振动测试 
参数测试（大） 
参数测试（大） 
物理分析 
K‐13 温度冲击浸入（盖板 IPX7） 
参数测试（小） 
跌落测试 
参数测试（小）
K‐12 带溅水的温度冲击 
参数测试（小） 
K‐11 高压喷射测试（IPX9K） 
参数测试（小） 
K‐02 梯级温度测试 


### 第 129 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
箭头中的数字表示样件数量。 
图 46：测试流程‐顺序流程 
 
 
如果测试 M‐01“跌落测试”中样件没有受损，则将其中 2 个样件应用到接下来的顺序测试
中。其他情况必须使用备用样件。 
 
如果在顺序测试中用循环湿热测试（带霜冻）代替湿热测试，则在平行测试中可以取消湿热
循环测试（带霜冻）。 
 
所有元件从测试 M‐01”自由跌落测试”起用原始插件或适配器进行测试。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 130 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
A.3 顺序外的测试（平行测试） 
 
 
箭头中的数字表示样件数量。 
图 47：测试流程计划—平行测试 
 
 
 
 
 
 
 
 
 
 
 
 
 
K‐01  高低温老化
环境测试参数测试（大） 
气密测试 
K‐07 运行时内舱盐雾测试
K‐09 湿热循环（带霜冻）
K‐14 恒湿恒温
K‐15 冷凝测试（无外壳）
K‐16 温度冲击（无外壳）
K‐17 日照测试
K‐18 有害气体测试
C‐01 化学测试
环境测试参数测试（大）
气密测试
物理分析 
*）一个化学试剂对应一个样件 
用于多种化学试剂的样件需与
委托方协商确定 


### 第 131 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
A.4 耐久测试 
 
箭头中的数字表示样件数量。 
图 48：测试流程计划‐耐久测试 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
K‐01 高低温老化 
环境测试参数测试（大） 
气密测试 
K‐02  温度梯级测试 
L1  机械液压耐久测试 
L2  高温耐久测试
L3 温度变化耐久测试 
参数测试 
K‐02  温度梯级测试 
环境测试参数测试（大） 
气密测试 
物理分析 


### 第 132 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
附录 B(标准) 
不同安装区域的常见温度集 
 
表 96：安装区域、常见温度集和温度升程一览表 
元件安装区 
温度集 No. 
温度升程 k 
车内（内舱），没有特殊要求 
1 
36 
白车身，没有特殊要求 
1 
36 
车内（内舱），有日照 
2 
46 
车顶 
2 
46 
发动机舱，但不在发动机上 
3 
60 
冷却风扇上 
3 
60 
发动机安装 
4 
75 
变速箱安装 
4 
75 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 133 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
B.1 温度集 1 
表 97：温度集 1 
温度 
分配百分比 
‐40℃ 
6% 
23℃ 
20% 
40℃ 
65% 
75℃ 
8% 
80℃ 
1% 
B.2 温度集 2 
表 98：温度集 2 
温度 
分配百分比 
‐40℃ 
6% 
23℃ 
20% 
50℃ 
65% 
100℃ 
8% 
105℃ 
1% 
B.3 温度集 3 
表 99：温度集 3 
温度 
分配百分比 
‐40℃ 
6% 
23℃ 
20% 
65℃ 
65% 
115℃ 
8% 
120℃ 
1% 
B.4 温度集 4 
表 100：温度集 4 
温度 
分配百分比 
‐40℃ 
6% 
23℃ 
20% 
85℃ 
65% 
135℃ 
8% 
140℃ 
1% 
 
 
 
 
 
 
 
 
 
 
 


### 第 134 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
附录 C(标准) 
高温耐久测试计算模型 
C.1 Arrhenius 模型 
针对高温耐久测试时长的计算：温度集百分比是依据设计任务书中的使用特征。 
表 101：温度集 
温度 
分配百分比 
TFeld.1 
P1 
TFeld.2 
P2 
… 
… 
TFeld.n 
Pn 
工作时间 tBetrieb 也参见设计任务书。 
每个温度 TFeld.1…TFeld.n 使用下面的公式加速系数 AT,1…AT,n 进行计算： 
 
其中： 
AT,I    Arrhenius 模型加速系数 
EA    活化能 EA=0.45eV 
K      波茨曼常数（k=8.617*10‐5eV/K） 
Ttest  测试温度（℃），一般是 Tmax 
TFeld,i    依据使用曲线的温度集区域温度（℃） 
‐273.15℃    绝对温度 0 度 
高温耐久测试的总测试时长是通过下面的加速系数得出： 
 
其中： 
tPruf      高温耐久测试的测试时间（小时） 
tBetrieb      区域运行时间（小时） 
Pi      运行时间百分比，元件在区域以温度 TFeld,i 运行 
AT,i      温度 TFeld,i 加速系数 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 135 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
C.2 Arrhenius 模型举例 
控制器温度集见下表 
表 102：温度集举例 
温度（℃） 
分配百分比 
‐40 
6 
23 
20 
60 
65 
100 
8 
105 
1 
运行时间为 8000h 的高温耐久测试时间计算如下： 
 
通过等式（1）和 Ttest=Tmax=105℃上述温度集的所有 5 个温度（见表 102）的加速系数 AT,i
计算结果如下： 
 
 
元件运行时间为：tBetrieb=8000h 
 
高温耐久测试总测试时间计算见等式（2）： 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 136 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
C.3 用于高温下性能降低的元件 Arrhenius 模型 
Top,max 以内高温下性能降低的元件的高温耐久测试时间计算的温度集依据设计任务书中的
使用特征，在温度范围 T≤Top,max 和 T＞Top,max 进行分配： 
 
表 103：T≤Top,max 测试温度 Top,max 的温度集 
 
 
表 104：Top,max＜T≤Tmax 测试温度 Tmax 的温度集 
 
每个温度 TFeld,1…TFeld,m…TFeld,n 使用等式（1）的加速因数 AT,1…AT,m…AT,n 进行计算，其中测试温
度范围 T≤Top,max:Ttest=Top,max，温度范围 T＞Top,max 的，测试温度为 Ttest=Tmax 
 
测试温度为 Top,max 的测试时间 top,max 依据等式（2），i=1…m 
 
测试温度为 Tmax 的测试时间 tmax 依据等式（2），i=m+1…n 
 
总测试时间 tGes 是 top,max 和 tmax 的总和。 
 
接近实际的测试是按照间歇性的测试温度为 Top,max 或者 Tmax（见图 43） 
零件测试时间 top,max 与 tmax 间隔为 48h，按照比例进行分配。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 137 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
C.4 用于高温下性能降低的元件 Arrhenius 模型举例 
控制器适用的温度集依据表格 105 和 106，高温性能降低且最高温度小于 top,max=90℃的元件
在运行时间 8000h 的高温耐久测试时间按照如下计算： 
温度分配的百分比依据使用特征，分为下面 T≤Top,max 和 T＞Top,max 两个范围： 
 
表 105：T≤90℃的温度集举例 
温度（℃） 
分配 
‐40 
6 
23 
20 
60 
65 
 
表 106：T＞90℃的温度集举例 
温度（℃） 
分配 
100 
8 
105 
1 
使用等式（1）和 Ttest=90℃计算温度集首件温度 T≤90℃的（见表 105）的加速因数： 
 
 
从中可以计算出测试温度 Top,max=90℃的测试时间 top,max： 
 
使用等式（1）和 Ttest=105℃计算温度集第二个零件温度 T＞90℃的（见表 106）的加速因数： 
 
由此得出测试温度 Tmax=105℃的测试时间 tmax： 
 
高温耐久测试的总测试时间为两个测试时间之和： 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 138 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
按照图 43 间歇性的使用测试温度 Top,max 或者 Tmax 并采用下面的间隔时间： 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 139 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
C.5  冷却液管路上零件应用的 Arrhenius 模型 
与冷却液管路连接的元件：必须考虑环境和冷却液相关温度分配的所有相关运行模式 i(见图
22，i 代表运行模式编号)。 
每个相关运行模式 i 的高温耐久测试：环境和冷却液测试时间和测试温度都要计算，计算公
式如下。总测试时间等于各自相关运行模式 i 的测试时间的总和。 
 
每个相关运行模式 i 的测试时间计算时首先计算环境温度的测试时间，然后计算冷却液温度
的测试时间，计算方式按照 C.1 甚至 C.3 的 Arrhenius 模型。 
得出的测试时间 tprüf,umgebung 和 tprüf,KKL 一般是不一样的，每个运行模式 i 的元件测试时间都要
统一。环境温度和冷却液管路之间的测试时间也要一致。 
 
通过下面的迭代法可以将测试时间 tprüf,umgebung 和 tprüf,KKL 延长，将测试分成 2 部分，除一段测
试外，其他段测试温度都要降低。 
 
案例 A: tprüf,umgebung＜tprüf,KKL 
 
测试时间： 
运行模式 i 的测试时间计算公式： 
 
冷却液测试温度： 
按照附录 C.1Arrhenius 模型选择测试温度（一般为 Tkühl,max） 
环境温度的测试温度： 
测试温度按照下列演算法（在环境温度集基础上）进行计算，（见表 107）： 
 
表 107：环境温度集 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 140 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
1. 迭代开始（m=0）: 
第一个测试部分测试温度需为 TFeld,n,分段测试时间 tprüf,T_Feld,n=tBetrieb*pn(其中运行时间
tBetrieb 单位为 h) 
 
2. 首次迭代（m=1）: 
通过第 1 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，因此通过更多测试可
以得出剩下的测试时间： 
 
通过第 1 段测试还可以得出环境温度分配百分比 pn.Pn 用于后面的计算时必须调整为
pn=0. 
为确定第二段测试的测试温度（m=1）,首先按照附录 C.1 或 C.3Arrhenius 模型计算
Tangepasst(调整后)的测试温度，这样可以得出环境温度分配（pn=0 调整后的）剩下的测试时间
tRest,1. 
当计算得出的调整后的测试温度 Tangepasst(调整后)≤TFeld,n‐1 时，则第 2 段测试要在测试温度
TFeld,n‐1 进行，测试时间公式： 
 
至少要再进行一次迭代步骤。 
如果计算得出的调整后的测试温度 Tangepasst(调整后)＞TFeld,n‐1 时，则第 2 段测试要在测试温度
Tangepasst(调整后)进行，测试时间公式： 
 
这就不用再进行迭代步骤了。 
 
3. 继续迭代（m=2,3...） 
4. 通过首次 m 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，因此通过更多测试
可以得出剩下的测试时间： 
 
通过首批 m 段测试还可以得出环境温度分配百分比 pn‐k,k=0,1,...(m‐1).Pn‐k 用于后面的计
算时必须调整为 pn‐k=0. 
为确定(m+1)段测试的测试温度,首先按照附录 C.1 或 C.3Arrhenius 模型计算 Tangepasst(调整后)
的测试温度，这样可以得出环境温度分配（pn‐k=0 调整后的）剩下的测试时间 tRest,m. 
当计算得出的调整后的测试温度 Tangepasst(调整后),≤TFeld,n‐m 时，则第 m+1 段测试要在测试温
度 TFeld,n‐m 进行，测试时间公式： 
 
 
 
 
 


### 第 141 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
至少要再进行一次迭代步骤。 
如果计算得出的调整后的测试温度 Tangepasst(调整后)＞TFeld,n‐m 时，则第 m+1 段测试要在测试
温度 Tangepasst(调整后)进行，测试时间公式： 
 
这就不用再进行迭代步骤了(迭代结束)。 
 
案例 B: tprüf,umgebung＞tprüf,KKL 
测试时间： 
运行模式 i 的测试时间计算公式： 
 
 
环境温度的测试温度： 
按照附录 C.1 或 C.3Arrhenius 模型选择测试温度（一般为 Tmax 或 Tmax 和 Top,max） 
 
冷却液的测试温度： 
测试温度按照下列演算法（在环境温度集基础上）进行计算，（见表 108）： 
 
表 108：冷却液温度集 
 
1. 迭代开始（m=0）: 
第一个测试部分测试温度需为 TFeld,n,分段测试时间 tprüf,T_Feld,n=tBetrieb*pn(其中运行时间
tBetrieb 单位为 h) 
 
2. 首次迭代（m=1）: 
通过第 1 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，因此通过更多测试可
以得出剩下的测试时间： 
 
通过第 1 段测试还可以得出冷却液温度分配百分比 pn.Pn 用于后面的计算时必须调整为
pn=0. 
为确定第二段测试的测试温度（m=1）,首先按照附录 C.1 Arrhenius 模型计算 Tangepasst(调整
后)的测试温度，这样可以得出冷却液温度分配（pn=0 调整后的）剩下的测试时间 tRest,1. 
当计算得出的调整后的测试温度 Tangepasst(调整后)≤TFeld,n‐1 时，则第 2 段测试要在测试温度
TFeld,n‐1 进行，测试时间公式： 


### 第 142 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
至少要再进行一次迭代步骤。 
如果计算得出的调整后的测试温度 Tangepasst(调整后)＞TFeld,n‐1 时，则第 2 段测试要在测试温度
Tangepasst(调整后)进行，测试时间公式： 
 
这就不用再进行迭代步骤了（迭代结束）。 
 
3. 继续迭代（m=2,3...） 
4. 通过首次 m 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，因此通过更多测试
可以得出剩下的测试时间： 
 
通过首批 m 段测试还可以得出冷却液温度分配百分比 pn‐k,k=0,1,...(m‐1).Pn‐k 用于后面的
计算时必须调整为 pn‐k=0. 
为确定(m+1)段测试的测试温度,首先按照附录 C.1Arrhenius 模型计算 Tangepasst(调整后)的测试
温度，这样可以得出冷却液温度分配（pn‐k=0 调整后的）剩下的测试时间 tRest,m. 
当计算得出的调整后的测试温度 Tangepasst(调整后),≤TFeld,n‐m 时，则第 m+1 段测试要在测试温
度 TFeld,n‐m 进行，测试时间公式： 
 
 
至少要再进行一次迭代步骤。 
如果计算得出的调整后的测试温度 Tangepasst(调整后)＞TFeld,n‐m 时，则第 m+1 段测试要在测试
温度 Tangepasst(调整后)进行，测试时间公式： 
 
这就不用再进行迭代步骤了(迭代结束)。 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 143 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
C.6 冷却液管路上零件应用的 Arrhenius 模型举例 
与冷却液管路连接的控制器：环境温度和冷却液温度集见下表： 
 
表 109：环境温度集举例 
 
表 110：冷却液温度集 
 
工作时长为 8000h，使用寿命测试和高温耐久性测试的时间计算如下： 
 
测试时间： 
Arrhenius 模型环境温度和冷却液温度测试时间计算： 
tprüf,umgebung=1143h 
tprüf,KKL=2009h 
由于 tprüf,umgebung＜tprüf,KKL，则按照 C.5 中的案例 A 进行计算。环境温度的测试时间必须调整
为 tprüf,Mode,i= tprüf,KKL=2009h. 
 
冷却液测试温度： 
根据温度集冷却液测试温度 TKKL,max=TFeld,5=80℃ 
 
环境温度的测试温度的迭代计算： 
1.迭代开始： 
第一部分测试 TFeld,5=105℃，测试时间 tprüf,T_Feld,5=tBetrieb*p5=8000h*1%=80h。 
 
2.首次迭代： 
通过第 1 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，其余的测试时间必须重新
计算：tRest,1=tprüf.Mode,i‐tprüf,T_Feld,5=2009h‐80h=1929h. 


### 第 144 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
通过第 1 段测试还可以得出温度分配百分比 p5.P5 用于后面的计算时必须调整为 p5=0. 
 
表 111：第 1 段测试后环境温度集调整 
 
然后确定第二段测试的测试温度，必须按照 Arrehnius 模型 C.1 计算调整后的测试温度
Tangepasst 可以得出剩下的测试时间 tRest,1=1929h。考虑到环境温度调整后的温度分配情况，得
出在 Tangepasst=89.5℃（精确值：89.46℃）时的测试时间必须为 1929h. 
由于 Tangepasst＜TFeld,4(89.5℃＜100℃），则第 2 段测试必须在 TFeld,4=100℃时进行。 
第 2 段测试时间 tprüf,T_Feld,4=tBetrieb*p4=8000h*8%=640h. 
 
3.第 2 次迭代： 
通过第 2 段测试可以计算出模式 i 时的其他测试时间 tprüf.Mode,i,剩下的测试时间计算： 
 
通过第 2 段测试还可以得出环境温度分配百分比 p5和 p4.P5和 p4用于后面的计算时必须调整
为 p5=p4=0. 
表 112：第 1 段和第 2 段测试后环境温度集调整 
 
然后确定第三段测试的测试温度，必须按照 Arrehnius 模型 C.1 计算调整后的测试温度
Tangepasst 可以得出剩下的测试时间 tRest,2=1289h。考虑到环境温度调整后的温度分配情况，得
出在 Tangepasst=82℃（精确值：82.17℃）时的测试时间必须为 1289h. 
由于 Tangepasst＞TFeld,3(82℃＞50℃），则无需再进行迭代。 
 
 
 


### 第 145 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
第 3 段和最后一段测试在 Tangepasst=82℃时的测试时间 tprüf,T_Feld,3=tBetrieb*p3=1289h 
 
 
在环境温度 105℃时测试 80 小时，环境温度 100℃时测试 640 小时，以及在环境温度 82℃
时测试 1289 小时。此案例中的冷却液温度整个测试期间维持在恒定 80℃。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 146 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
附录 D(标准) 
温度变化耐久测试计算模型 
D.1 Coffin‐Manson‐模型 
计算温度变化的耐久测试时间需要给出元件在△TFeld 区域（参见表 96）的平均温度变化以及
区域的温度耐久测试的循环次数 NTempZyklenFeld。 
 
每天 2 次温度变化的温度循环次数可以从下列公式推断出： 
NTempZyklenFeld=2*365*15（年）=10950 次循环 
与区域的平均温度变化有关，Coffin‐Manson‐模型的加速因数计算如下： 
 
其中： 
ACM                        Coffin‐Manson‐模型的加速因数 
△TTest                    一个测试循环期间的温度差（△TTest=Tmax‐Tmin） 
△TFeld                    区域耐久测试期间的平均温度差 
C                            Coffin‐Manson‐模型的参数 
                              在此标准中，规定 c 为固定值 2.5 
 
测试循环的总次数计算如下： 
 
其中： 
NPruf                  要求的循环次数 
NTempZyklenFeld      区域耐久测试期间的温度循环次数 
ACM                    Coffin‐Manson‐模型的加速因数，见等式（3） 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 147 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
D.2 举例 
Tmin=‐40℃和Tmax=105℃的控制器，区域内使用周期为15年，区域内平均温度差为△TFeld=40℃，
测试循环（NPruf）次数计算如下： 
1. 区域温度循环次数： 
NTempZyklenFeld =2*365*15（年）=10950 次循环 
2. 一次测试循环的温度差： 
△TTest=105℃‐（‐40℃）=145℃ 
3. 按照等式（3）计算 Coffin‐Manson‐模型的加速因数 ACM=25.02 
4. 按照等式（4）计算循环测试次数： 
 
5. 停留时间 tHaltezeit 等于元件温度恒定所需的时间再加上 15min。假设元件 20 分钟温度恒
定，那么停留时间就是 35min。 
6. 因此循环一次的时间是： 
 
7. 举例： 
 
8. 438 次循环的总测试时间是 1040h。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 148 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
D.3 冷却液循环系统元件的 Coffin‐Manson‐模型应用 
与冷却液管路连接的元件：必须考虑环境和冷却液相关温度分配的所有相关运行模式 i(见图
22，i 代表运行模式编号)。 
每个相关运行模式 i 的高温耐久测试：环境和冷却液最高温度和测试循环次数都要计算，计
算公式如下。总测试循环次数等于各自相关运行模式 i 的测试循环次数的总和。 
 
每个相关运行模式 i 的测试循环次数计算时首先计算环境温度的测试循环次数，然后计算冷
却液温度的测试循环次数，计算方式按照附录 C.7 的 Coffin‐Manson 模型。 
得出的测试循环次数 Nprüf,umgebung 和 Nprüf,KKL 一般是不一样的，每个运行模式 i 的元件测试循
环次数都要统一。环境温度和冷却液管路之间的测试循环次数也要一致。 
 
通过下面的迭代法可以将测试循环次数 Nprüf,umgebung 和 Nprüf,KKL 延长，将测试分成 3 部分，其
中一段测试温度在 Tmax 和 Tmin 之间进行，其他段测试温度冲程都要降低，在 Tmin 和 TRT 之间
甚至在 TRT 和 Tmax 之间进行。 
 
案例 A: Nprüf,umgebung＞Nprüf,KKL 
测试循环次数： 
运行模式 i 下的测试循环次数 Nprüf,Mode i=Nprüf,umgebung 
 
冷却液测试循环次数： 
冷却液的测试循环次数 Nprüf,KKL 必须调整到和环境测试循环次数 Nprüf,umgebung（比冷却液次数
多）一致，然后按照下面三个温度范围进行测试循环： 
 
1. XKKL 测试循环必须在 TKKL,min 和 TKKL,max 之间进行。根据 Coffin‐Manson 模型计算加速因数
ACM,KKL,1:△TTest,1=TKKL，max‐TKKL,min 
2. 1/2*(Nprüf,Mode i‐XKKL)测试循环必须在 TKKL,min 和 TRT 之间进行。 
根据 Coffin‐Manson 模型计算加速因数 ACM,KKL,2:△TTest,2=TRT‐TKKL,min 
3. 1/2*(Nprüf,Mode i‐XKKL)测试循环必须在 TRT 和 TKKL,max 之间进行。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 149 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
根据 Coffin‐Manson 模型计算加速因数 ACM,KKL,3:△TTest,3=TKKL,max‐TRT 
 
1 至 3 的总和即为温度循环总次数 Nprüf,Mode i。 
 
根据附录 D.1 中的等式 4 可以得出： 
 
测试循环次数 XKKL 计算如下： 
 
将 XKKL 值代入上述 1‐3 处可以计算出三段测试的循环次数。 
 
如果 TKKL,op,max＜TKKL,max,或者 TKKL,op,min＞TKKL,min 或者 TUmgebung,op,max＜TUmgebung,max ，或者
TUmgebung,op,min＞TUmgebung,min,则还需考虑相应温度（章节 16.3.2.1 中图 45）增加停留时间。 
测试过程中环境温度和冷却液管路的温度变化循环测试同步进行。 
 
案例 B: Nprüf,umgebung＜Nprüf,KKL 
测试循环次数： 
运行模式 i 下的测试循环次数 Nprüf,Mode i=Nprüf,KKL 
 
环境测试循环次数： 
环境的测试循环次数 Nprüf,Umgebung 必须调整到和冷却液测试循环次数 Nprüf,KKL（比环境测试次
数多）一致，然后按照下面三个温度范围进行测试循环： 
 
1. XUmgebung 测试循环必须在 TUmgebung,min 和 TUmgebung,max 之间进行。根据 Coffin‐Manson 模型
计算加速因数 ACM,Umgebung,1:△TTest,1=T Umgebung，max‐T Umgebung,min 
2. 1/2*(Nprüf,Mode i‐X Umgebung)测试循环必须在 T Umgebung,min 和 TRT 之间进行。 
根据 Coffin‐Manson 模型计算加速因数 ACM, Umgebung,2:△TTest,2=TRT‐T Umgebung,min 
 
 
 
 
 
 
 
 
 
 
 


### 第 150 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
3. 1/2*(Nprüf,Mode i‐X Umgebung)测试循环必须在 TRT 和 T Umgebung,max 之间进行。 
根据 Coffin‐Manson 模型计算加速因数 ACM, Umgebung,3:△TTest,3=T Umgebung max‐TRT 
 
1 至 3 的总和即为温度循环总次数 Nprüf,Mode i。 
 
根据附录 D.1 中的等式 4 可以得出： 
 
Xumgebung 测试循环次数计算如下： 
 
将 XKKL 值代入上述 1‐3 处可以计算出三段测试的循环次数。 
 
如果 T Umgebung op,max＜T Umgebung,max,或者 T Umgebung,op,min＞T Umgebung,min 或者 TKKL,op,max＜TKKL,max，或
者 TKKL,op,min＞TKKL,min,则还需考虑相应温度（章节 16.3.2.1 中图 45）增加停留时间。 
测试过程中环境温度和冷却液管路的温度变化循环测试同步进行。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 151 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
D.4 冷却液循环系统元件的 Coffin‐Manson‐模型应用举例 
一个与冷却液管路连接的控制器，环境温度范围 TUmgebung,min=‐40℃，TUmgebung,max=120℃，冷
却液温度范围 TKKL,min=‐40℃，TKKL,max=80℃，使用寿命 15 年，环境平均温差△TFeld,Umgebung=60K
以及冷却液的平均温差△TFeld,KKL=36K，运行模式 i 下的测试循环次数计算如下： 
 
环境和冷却液的测试循环次数： 
环境和冷却液的测试循环次数按照附录 D.1Coffin‐Manson 模型计算，得出结果如下： 
NPrüf.Umgebung=943 次循环 
NPrüf.KKL=540 次循环 
由于 NPrüf.Umgebung＞NPrüf.KKL，运行模式 i 的测试循环次数 N Prüf.Mode i=NPrüf.Umgebung=943 次循环，
冷却液的循环次数也要相应调整。 
 
冷却液测试循环次数调整 
冷却液循环次数调整到 N Prüf.Mode i=943 次循环，分成 3 部分： 
1.    XKKL 测试循环必须在 TKKL,min=‐40℃和 TKKL,max=80℃之间进行。根据 Coffin‐Manson 模型计
算加速因数
 
2.    1/2*(943‐XKKL)测试循环必须在 TKKL,min=‐40℃和 TRT=23℃之间进行。 
根
据
Coffin‐Manson
模
型
计
算
加
速
因
数
 
3.1/2*(943‐XKKL)测试循环必须在 TRT=23℃和 TKKL,max=80℃之间进行。根据 Coffin‐Manson 模型
计算加速因数
 
由此得出 XKKL： 
 
 
 
 
 
 
 
 
 
 


### 第 152 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
1 至 3 处的 3 个温度范围的测试循环次数计算如下： 
1. TKKL,min=‐40℃和 TKKL,max=80℃之间必须进行 453 次循环。 
2. TKKL,min=‐40℃和 TRT=23℃之间必须进行 245 次循环。 
3. TRT=23℃和 TKKL,max=80℃之间必须进行 245 次循环。 
 
三部分测试循环次数累加起来即可得到运行模式 i 时的总测试循环次数 N  Prüf.Mode  i=943 次循
环。 
 
测试过程中环境温度和冷却液管路的温度变化循环测试同步进行。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 153 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
附录 E(标准) 
等级为 2 的恒湿恒温测试的计算模型 
E.1 Lawson‐模型 
该测试时间的计算需要获得停车状态下的平均环境湿度 RHFeldParken 以及元件平均温度
TFeldParken. 
如果设计任务书中没有特殊说明，则采用下表中的数值： 
 
表 113：停车状态下的平均环境湿度和温度 
安装位置 
停车平均环境湿度 RHFeldParken
停车平均环境温度 TFeldParken 
乘客车厢/行李箱内 
60%rH 
23℃ 
乘客车厢/行李箱外 
65%rH 
23℃ 
 
与环境平均湿度和温度有关，Lawson‐模型的加速因数计算如下： 
 
 
其中： 
AT/RH                    Lawson‐模型的加速因数 
b                          常量（b=5.57*10‐4） 
EA                          激活能量（EA=0.4eV） 
K                          波茨曼常量（k=8.617*10‐5eV/K） 
TPruf                      测试温度（℃） 
TFeldParken              停车时的平均温度（℃） 
RHPruf                    测试期间的相对湿度（%） 
RHFeldParken            停车时的平均相对温度（%） 
‐273.15℃            温度绝对零点 
 
等级为 2 的恒湿恒温测试的测试时间计算如下： 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 154 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
其中： 
tPruf                          测试时间（h） 
tFeldParken                区域内使用寿命周期内的非运行时间（停车时间）
（h）
（不利情况下131400h，
如果车辆未使用） 
AT/RH                      按照等式（5）计算的 Lawson‐模型的加速因数 
 
E.2 举例 
安装在发动机舱内的控制器测试时间计算如下： 
1. 停车时元件的平均温度 TFeldParken =23℃，相对湿度为 RHFeldParken =65%。 
测试条件为 TTest =65℃和 RHPruf =93% 
 
通过等式（5）得出 Lawson‐模型综合的加速因数 AT/RH =82.5 
 
2. 区域内的停车时间 tFeldParken =131400h 
通过等式（6）得出总测试时间： 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 155 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
附录 F(参考) 
冷凝测试，测试箱设置和图表 
 
图 49：测试箱设置 
 
温度上升期间的水浴温度可以控制调节，温度达到 80℃时换到温控上（正常运行）。 
 
 
 
 
开始启动 
相对湿度 
冷凝 
功能测试 


### 第 156 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
图 50：冷凝测试流程，一次循环 
1. 调节的水浴温度 
2. 得出的测试箱温度 
3. 测试箱中的实际空气湿度 
 
 
 
 
 
 
 
 


### 第 157 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
 
图 51：冷凝测试流程，5 次循环 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 158 页
版本 2.2                                                                          LV 124                                                            2013.02.28 
VW 80000:2013‐06 
附录 G(参考) 
物理分析方法举例 
 
� 
螺钉松动扭矩（例如塑壳螺钉连接、固定零件的螺钉……） 
� 
焊接点缺陷 
� 
（尤其是温度变化引起的）零件和/或电路板变色 
� 
（机械运动的零部件）操作灵活性、摩擦和运转 
� 
磨损痕迹 
� 
（尤其是注塑件、密封件的）材质出现裂缝、裂纹、变形，选择合适的测试方法（X 射
线、CT、剖面分析……）进行检测 
� 
不透明性（尤其是光学传感系统中的零件） 
� 
锁紧结构的状态 
� 
腐蚀及移动痕迹，尤其是银、锡的移动 
� 
评估塑料的耐水解性（尤其是内嵌网格的零件以及 KL.30 电连接的零件） 
� 
通孔电路板损坏，尤其是热过孔 
� 
机械负荷后（振动、机械冲击、坠落测试）的大电容内部连接损坏 
� 
（如电流、温度、摩擦、氧化引起的）连接器 Pin 脚损坏 
� 
其他异常现象 
� 
ICT  结果（在可能的情况下） 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 

