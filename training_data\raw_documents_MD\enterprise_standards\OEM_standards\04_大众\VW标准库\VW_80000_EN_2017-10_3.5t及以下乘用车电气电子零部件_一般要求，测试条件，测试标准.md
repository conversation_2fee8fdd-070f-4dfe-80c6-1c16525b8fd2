# VW_80000_EN_2017-10_3.5t及以下乘用车电气电子零部件_一般要求，测试条件，测试标准.pdf

## 文档信息
- 标题：
- 作者：<PERSON><PERSON><PERSON>, <PERSON> (EXTERN: Hoenigsberg)
- 页数：183

## 文档内容
### 第 1 页
 
 
 
 
Group Standard 
VW 80000 
Issue 2017-10 
Class. No. 
8MA00 
Descriptors: 
component, electric component, electronic component, module, test condition 
 
 
Electric and Electronic Components in Motor Vehicles up to 3.5 t  
General Requirements, Test Conditions, and Tests 
 
Previous issues 
VW 80101: 1987-06, 1988-08, 1992-01, 1993-04, 1994-05, 1995-06, 1998-01, 1999-06, 2000-09, 
2001-04, 2003-05, 2004-07, 2005-06, 2006-10, 2009-03; VW 80000: 2009-10, 2013-06 
 
Changes 
The following changes have been made compared to Volkswagen standard VW 80000, 2013-06: 
– 
See change history on page 2 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Always use the latest version of this standard. 
This electronically generated standard is authentic and valid without a signature. 
The English translation is believed to be accurate. In case of discrepancies, the German version controls. 
Technical responsibility 
 
Standards 
 
EEIP/1 
Dr. Torsten Polte 
Phone: +49 5361 9 36035 
 
 
I/EE-61 
Uwe Girgsdies 
Phone: +49 841 89 90836 
K-ILI/5 <PERSON>inker 
K-ILI 
EEG4 
Mark Martins 
Phone: +49 711 911 84847 
Phone: +49 5361 9 32438 
Uwe Wiesner 
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group's Standards departments. 
© Volkswagen AG 
VWNORM-2016-08a 
Page 1 of 165 


### 第 2 页
Page 2 
VW 80000: 2017-10 
 
 
 
 
 
 
Revision record 
 
Date of issue 
 
2013-02 
Editorial changes incorporated. 
 
Part I – Electrical requirements and tests on the 12-V electric 
system: 
Thoroughly revised – each test was adapted to current 
requirements. 
 
Part II – Environmental requirements and tests: 
Expanded to components that are described in several operating 
situations and components that are connected to coolant circuits; 
service life tests revised  
2017-05 
Fundamental revision 
 
General 
- 
Operating modes and their derivation and designation revised 
- 
General requirements for part I and part II harmonized and 
merged 
 
Part I – Electrical requirements and tests on the 12-V electric 
system: 
- 
Expanded to include function classes  
- 
Functional states reorganized 
- 
Vehicles with alternative drives taken into account 
- 
Parameter tests redefined 
- 
E-03b, E-07b, and E-23 added 
- 
General modifications and error corrections (consideration of 
all change requests (CRs) received) 
 
Part II – Environmental requirements and tests: 
- 
Expansions to test procedures in the individual tests 
- 
Leak tightness requirement expanded 
- 
Pressure pulsation test added to the mechanical tests 
 
 
 


### 第 3 页
 
  
Page 3 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Contents 
 
1 
Scope ................................................................................................................... 6 
2 
Referenced standards ........................................................................................ 7 
3 
Terms and definitions ......................................................................................... 9 
3.1 
Terms and abbreviations ....................................................................................... 9 
3.2 
Voltages and currents ......................................................................................... 11 
3.3 
Temperatures ...................................................................................................... 12 
3.4 
Times .................................................................................................................. 12 
3.5 
Internal resistance, terminal designation, frequency ........................................... 12 
3.6 
Standard tolerances ............................................................................................ 13 
3.7 
Standard values .................................................................................................. 13 
4 
General requirements ....................................................................................... 14 
4.1 
Sampling rates and measured-value resolutions ................................................ 14 
4.2 
Functional states ................................................................................................. 14 
4.3 
Operating situations ............................................................................................ 15 
4.4 
Operating states .................................................................................................. 15 
4.5 
Operating modes ................................................................................................ 16 
4.6 
Temperature stabilization .................................................................................... 19 
4.7 
Parameter test .................................................................................................... 20 
4.8 
Continuous parameter monitoring with drift analysis ........................................... 22 
4.9 
Leak tightness ..................................................................................................... 23 
Part I – Electrical requirements and tests on the 12-V electric system .................. 24 
5 
General requirements ....................................................................................... 24 
5.1 
Voltages and currents ......................................................................................... 24 
5.2 
Test voltages ....................................................................................................... 24 
5.3 
Function classes and operating voltage ranges .................................................. 25 
5.4 
Interface description ............................................................................................ 25 
5.5 
Procedure restrictions ......................................................................................... 25 
5.6 
Test sequence .................................................................................................... 26 
6 
Test selection table ........................................................................................... 28 
7 
Electrical requirements and tests .................................................................... 30 
7.1 
E-01 Long-term overvoltage ................................................................................ 30 
7.2 
E-02 Transient overvoltage ................................................................................. 32 
7.3 
E-03 Transient undervoltage ............................................................................... 34 
7.4 
E-04 Jump start ................................................................................................... 37 
7.5 
E-05 Load dump ................................................................................................. 38 
7.6 
E-06 Ripple voltage ............................................................................................. 39 
7.7 
E-07 Slow decrease and increase of the supply voltage ..................................... 41 
7.8 
E-08 Slow decrease, quick increase of the supply voltage ................................. 44 
7.9 
E-09 Reset behavior ........................................................................................... 46 
7.10 E-10 Brief interruptions ....................................................................................... 48 
7.11 E-11 Start pulses ................................................................................................ 51 
7.12 E-12 Voltage curve with electric system control .................................................. 55 
7.13 E-13 Pin interruption ........................................................................................... 56 
7.14 E-14 Connector interruption ................................................................................ 58 
7.15 E-15 Reverse polarity.......................................................................................... 59 
7.16 E-16 Ground potential difference ........................................................................ 62 
7.17 E-17 Short circuit in signal line and load circuits ................................................. 63 


### 第 4 页
Page 4 
VW 80000: 2017-10 
 
 
 
 
 
 
7.18 E-18 Insulation resistance ................................................................................... 65 
7.19 E-19 Quiescent current ....................................................................................... 66 
7.20 E-20 Dielectric strength ....................................................................................... 67 
7.21 E-21 Backfeeds ................................................................................................... 68 
7.22 E-22 Overcurrents ............................................................................................... 70 
7.23 E-23 Equalizing currents of multiple supply voltages .......................................... 71 
Part II – Environmental requirements and tests ....................................................... 73 
8 
Use profile ......................................................................................................... 73 
8.1 
Service life requirements ..................................................................................... 73 
8.2 
Temperature load spectra ................................................................................... 73 
9 
Test selection .................................................................................................... 75 
9.1 
Test selection table ............................................................................................. 75 
9.2 
Test sequence plan ............................................................................................. 77 
10 
Mechanical requirements and tests ................................................................ 78 
10.1 M-01 Free fall ...................................................................................................... 78 
10.2 M-02 Stone impact test ....................................................................................... 79 
10.3 M-03 Dust test ..................................................................................................... 80 
10.4 M-04 Vibration test .............................................................................................. 81 
10.5 M-05 Mechanical shock ...................................................................................... 92 
10.6 M-06 Continuous mechanical shock ................................................................... 93 
10.7 M-07 Coolant circuit pressure pulsation test ....................................................... 94 
11 
Climatic requirements and tests ...................................................................... 95 
11.1 K-01 High-/low-temperature aging ...................................................................... 95 
11.2 K-02 Incremental temperature test ...................................................................... 96 
11.3 K-03 Low-temperature operation ........................................................................ 98 
11.4 K-04 Repainting temperature .............................................................................. 99 
11.5 K-05 Thermal shock (component) ..................................................................... 100 
11.6 K-06 Salt spray test with operation, exterior ...................................................... 101 
11.7 K-07 Salt spray test with operation, interior ....................................................... 103 
11.8 K-08 Damp heat, cyclic ..................................................................................... 104 
11.9 K-09 Damp heat, cyclic (with frost) ................................................................... 105 
11.10 K-10 Water protection – IPX0 to IPX6K ............................................................ 106 
11.11 K-11 High-pressure cleaning/pressure washing ................................................ 107 
11.12 K-12 Thermal shock with splash water.............................................................. 108 
11.13 K-13 Thermal shock – immersion ..................................................................... 111 
11.14 K-14 Damp heat, constant ................................................................................ 111 
11.15 K-15 Condensation and climatic test ................................................................. 115 
11.16 K-16 Thermal shock (without housing) .............................................................. 123 
11.17 K-17 Solar radiation .......................................................................................... 124 
11.18 K-18 Harmful gas test ....................................................................................... 125 
12 
Chemical requirements and tests .................................................................. 126 
12.1 C-01 Chemical tests .......................................................................................... 126 
13 
Service life tests .............................................................................................. 129 
13.1 L-01 Service life test – Mechanical/hydraulic durability testing ......................... 129 
13.2 L-02 Service life test – High-temperature durability testing ............................... 131 
13.3 L-03 Service life test – Temperature cycle durability testing ............................. 135 
Appendix A (normative)  Test sequence ................................................................. 140 
A.1 
Test sequence plan ........................................................................................... 140 
A.2 
Sequence tests ................................................................................................. 141 


### 第 5 页
 
  
Page 5 
VW 80000: 2017-10 
 
 
 
 
 
 
 
A.3 
Tests outside of the sequence (parallel tests) ................................................... 143 
A.4 
Service life tests ................................................................................................ 145 
Appendix B (normative)  Typical temperature load spectra for different 
installation areas ............................................................................................. 146 
B.1 
Temperature load spectrum 1 ........................................................................... 147 
B.2 
Temperature load spectrum 2 ........................................................................... 147 
B.3 
Temperature load spectrum 3 ........................................................................... 147 
B.4 
Temperature load spectrum 4 ........................................................................... 147 
Appendix C (normative)   Calculation for the performance of the 
"Mechanical/hydraulic durability testing" service life test .......................... 148 
C.1 
Calculation ........................................................................................................ 148 
C.2 
Example calculation .......................................................................................... 150 
Appendix D (normative)  Calculation models for the "High-temperature 
durability testing" service life test ................................................................. 152 
D.1 
Adaptation of the test temperatures to reduce the test duration ........................ 152 
D.2 
Arrhenius model ................................................................................................ 152 
D.3 
Example Arrhenius model: ................................................................................ 154 
D.4 
Arrhenius model to be used for components with reduced performance at high 
temperatures ..................................................................................................... 155 
D.5 
Example Arrhenius model to be used for components with reduced performance at 
high temperatures: ............................................................................................ 156 
D.6 
Arrhenius model to be used for components on coolant circuits ....................... 158 
D.7 
Example Arrhenius model to be used for components on coolant circuits ........ 163 
Appendix E (normative)  Calculation models for the "Temperature cycle 
durability testing" service life test ................................................................. 166 
E.1 
Adaptation of the test temperatures to reduce the test cycles ........................... 166 
E.2 
Coffin-Manson model ........................................................................................ 166 
E.3 
Example: ........................................................................................................... 168 
E.4 
Coffin-Manson model to be used for components on coolant circuits ............... 169 
E.5 
Example Coffin-Manson model to be used for components on coolant circuits 172 
Appendix F (normative)  Calculation models for the "Damp heat, constant – 
severity 2" test ................................................................................................ 174 
F.1 
Lawson model ................................................................................................... 174 
F.2 
Example: ........................................................................................................... 175 
Appendix G (informative)  Condensation test, chamber programming, and 
graphs .............................................................................................................. 176 
Appendix H Examination methods for physical analysis ...................................... 179 
 
 


### 第 6 页
Page 6 
VW 80000: 2017-10 
 
 
 
 
 
 
1 Scope 
 
This document specifies requirements, test conditions, and tests for electric, electronic, 
and mechatronic components and systems for use in motor vehicles up to 3.5 t. 
 
Additional or deviating requirements, test conditions, and tests are defined in the 
corresponding Performance Specifications. 
 
The following additionally applies to part I: The requirements, test conditions, and tests 
refer to the 12-V electric system. Unless otherwise noted, the tests are not electrical 
service life tests. 
 
Note: The specified tests are used to check some of the required properties of the 
component and are not used for subcomponent qualification or qualification of the 
production process. 
 


### 第 7 页
 
  
Page 7 
VW 80000: 2017-10 
 
 
 
 
 
 
 
2 Referenced standards 
 
Table 1: Referenced standards 
ANSI/UL 94 
Standard for Tests for Flammability of Plastic Materials for Parts 
in Devices and Appliances 
DIN 75220 
Ageing of Automotive Components in Solar Simulation Units 
DIN 72552-2 
Terminal Markings for Vehicles – 
Part 2: Codes 
DIN EN 13018 
Non-Destructive Testing – Visual Testing – General Principles 
DIN EN ISO/IEC 1702
5 
General Requirements for the Competence of Testing and 
Calibration Laboratories 
DIN EN 60068-2-1 
Environmental Testing – Part 2-1: Tests – Test A: Cold 
DIN EN 60068-2-2 
Environmental Testing – Part 2-2: Tests – Test B: Dry Heat 
DIN EN 60068-2-6 
Environmental Testing – Part 2-6: Tests – Test Fc: Vibration 
(Sinusoidal) 
DIN EN 60068-2-11 
Environmental Testing – Part 2: Tests – Test Ka: Salt Mist 
DIN EN 60068-2-14 
Environmental Testing – Part 2-14: Tests – Test N: Change of 
Temperature 
DIN EN 60068-2-27 
Environmental Testing – Part 2-27: Tests – Test Ea and 
Guidance: Shock 
DIN EN 60068-2-29 
Basic Environmental Testing Procedures – Part 2: Tests; 
Test Eb and Guidance: Bump 
DIN EN 60068-2-30 
Environmental Testing – Part 2-30: Tests – 
Test Db: Damp Heat, Cyclic (12 h + 12 h Cycle) 
DIN EN 60068-2-38 
Environmental Testing – Part 2-38: Tests – Test Z/AD: 
Composite Temperature/Humidity Cyclic Test 
DIN EN 60068-2-60 
Environmental Testing – Part 2-60: Tests – Test Ke: Flowing 
Mixed Gas Corrosion Test 
DIN EN 60068-2-64 
Environmental Testing – Part 2-64: Tests – Test Fh: Vibration, 
Broadband Random and Guidance 
DIN EN 60068-2-78 
Environmental Testing – Part 2-78: Tests – Test Cab: Damp 
Heat, Steady State 
DIN EN ISO 11124-2 
Preparation of Steel Substrates Before Application of Paints 
and Related Products – Specifications for Metallic Blast-
Cleaning Abrasives – Part 2: Chilled-Iron Grit 
DIN EN ISO 20567-1 
Paints and Varnishes –  
Determination of Stone-Chip Resistance of Coatings –  
Part 1: Multi-Impact Testing 
DIN EN ISO 6270-2 
Paints and varnishes - Determination of resistance to humidity - 
Part 2: Condensation (in-cabinet exposure) 
ISO 12103-1 
Road Vehicles – Test Contaminants for Filter Evaluation – Part 
1: Arizona Test Dust 
ISO 16750-3  
Road Vehicles – Environmental Conditions and Testing for 
Electrical and Electronic Equipment – Part 3: Mechanical Loads 
ISO 16750-5 
Road Vehicles – Environmental Conditions and Testing for 
Electrical and Electronic Equipment – Part 5: Chemical Loads 


### 第 8 页
Page 8 
VW 80000: 2017-10 
 
 
 
 
 
 
ISO 20653 
Road Vehicles – Degrees of Protection (IP Code) – Protection 
of Electrical Equipment Against Foreign Objects, Water and 
Access 
ISO 26262 
Road Vehicles – Functional Safety 
 


### 第 9 页
 
  
Page 9 
VW 80000: 2017-10 
 
 
 
 
 
 
 
3 Terms and definitions 
3.1 
Terms and abbreviations 
Table 2: Abbreviations for electrical requirements and tests 
Term/abbreviation 
Definition 
Application software 
Relates to the behavior according to the function class as per this 
standard and the functional capabilities as per the Performance 
Specification, for example: 
 Startup behavior 
 Sleep behavior 
 Control systems 
 Overload protection, short-circuit protection, and fiddle-
proofing 
 Diagnostics 
Module 
Electronic circuit carrier populated with subcomponents (without 
the housing) 
Subcomponent/ 
subcomponents 
Electric, electronic, or mechatronic subcomponent 
(e.g., resistor, capacitor, transistor, integrated circuit (IC), relay) 
Operation (general) 
Out of all relevant situations, operating situation in which 
minimum or maximum self-heating is generated. This operating 
situation is additionally defined. 
DAE 
Venting element 
Derating 
Deliberately limited function, e.g., change in power consumption 
as a function of voltage and/or temperature 
DUT 
See "Device under test" 
Driving 
Operating situation of a vehicle that has been released and put 
into operation by the customer (terminal 15 (t.15) on). 
A vehicle with an electric drive is not connected to a charging 
station/socket-outlet. 
Assembly 
Operating situation that reflects the assembly process. A 
distinction is made at the component level between an 
disconnected connector ("not installed") and a connected connector 
("assembly"). In both cases, the component is free of voltage and 
current. 
Functions 
Includes system-specific functions and diagnostic functions 
Hardware freeze 
The point during development from which changes to the 
hardware are no longer possible 
ICT 
In-circuit test 
High availability 
For its function, the component needs a requirement of at least 
ASIL A as per ISO 26262 for the electrical power supply (ASIL – 
Automotive Safety Integrity Level). 


### 第 10 页
Page 10 
VW 80000: 2017-10 
 
 
 
 
 
 
Climatic chamber with 
condensation option 
A specially controlled water bath in the climatic chamber, by 
means of which the required water quantity is implemented in the 
form of water vapor. 
The intensity of the condensation film on the circuit carrier 
depends on the thermal capacity, the relative humidity, and the 
temperature gradient of the water bath. 
The climate control of the climatic chamber is switched off during 
the condensation phase. The test chamber temperature is 
controlled by means of the temperature-controlled water bath. 
Component 
Complete device, electronic control unit (ECU), or mechatronic 
system (including housing) 
Short circuit 
A short circuit of a load output is defined by a loading case with 
lower impedance than with the specified load, down to the limit 
case of 0 Ω. This includes a creeping short circuit, that is, with the 
current just below short-circuit detection. 
A short circuit may be permanently present (component in 
operation/not in operation). 
Charging 
Operating situation of a vehicle with an electric drive that is 
parked and connected to a charging station/socket-outlet. The 
high-voltage battery pack is charging. 
Performance 
Specification 
Used in this standard as a generic term for the following, for 
example: 
 Drawing 
 Component Performance Specification (BT-LAH) 
 Feature Performance Specification 
 General Project-Independent Performance Specification 
(Q-LAH) 
 System Performance Specification 
 Reliability Testing Performance Specification 
 Diagnostics Performance Specification  
 Electromagnetic Compatibility (EMC) Performance 
Specification  
 Quality Performance Specification 
On-grid parking 
Operating situation of a vehicle with an electric drive that is 
parked and connected to a charging station/socket-outlet. The 
high-voltage battery pack is not charging. The vehicle can 
communicate with the charging station; a charging process is 
complete or can be started at any time. Extended use of the high-
voltage battery pack is possible, e.g., as a buffer store by the 
power-grid operator. 
Off-grid parking 
Operating situation of a vehicle that is parked. A vehicle with an 
electric drive is not connected to a charging station/socket-outlet. 
Power user 
Actual use case with maximum conceivable utilization  
Device under test 
The system or component being tested 
PTB 
The National Metrology Institute of Germany  
(Physikalisch-Technische Bundesanstalt) 
PSD 
Power spectral density 


### 第 11 页
 
  
Page 11 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Circuit carrier 
Unpopulated substrate for electronics in general (unpopulated 
printed circuit board (PCB), ceramic, lead frame, flexible 
PCB/connector, etc.) 
Relevant to starting 
Components that are needed directly or indirectly for the starting 
process of an internal combustion engine 
System 
Functionally linked components, e.g.,  
brake control system (control module, hydraulic system, sensors) 
Preconditioning 
Operating situation of a vehicle with an electric drive that is 
parked and can be connected to a charging station/socket-outlet. 
The vehicle executes thermal conditioning. This typically involves 
vehicle-interior conditioning and/or high-voltage battery pack 
conditioning. 
 
3.2 
Voltages and currents 
Table 3: Abbreviations for voltages and currents 
VN 
Nominal voltage 
Vopmin 
Minimum operating voltage limit 
Vop 
Operating voltage 
Vopmax 
Maximum operating voltage limit 
Vmax 
Maximum voltage that can occur during a test 
Vmin 
Minimum voltage that can occur during a test 
VPP 
Peak-to-peak voltage 
VRMS 
Root-mean-square (RMS) value of a voltage 
Vtest 
Test voltage 
IN 
Nominal current 
GND 
Device ground 
VA, VT, VS, VR 
Voltage level of the starting voltage pulse 
 
3.2.1 
Voltages for components with extended requirements 
Table 4: Abbreviations for extended voltage definitions 
Vopmin,HV 
Minimum high-voltage (HV) operating voltage limit – minimum direct-
current (DC) operating voltage 
Vop,HV 
Operating voltage 
HV DC operating voltage 
Vopmax,HV 
Maximum HV operating voltage limit – maximum DC operating voltage 
 


### 第 12 页
Page 12 
VW 80000: 2017-10 
 
 
 
 
 
 
3.3 
Temperatures 
Table 5: Abbreviations for temperatures 
Tmin 
Minimum ambient temperature 
TRT 
Room temperature 
Tmax 
Maximum ambient temperature 
Top,min 
Minimum ambient temperature for components with overload 
protection/low-temperature protection at which full functionality is still 
required 
Top,max 
Maximum ambient temperature for components with overload 
protection/overtemperature protection at which full functionality is still 
required 
Ttest 
Test temperature 
3.3.1 
Temperatures for components of a coolant circuit 
Table 6: Coolant circuit temperature definitions 
Tcool,nom 
Nominal coolant temperature in the coolant circuit 
Tcool,min 
Minimum coolant temperature in the coolant circuit 
Tcool,max 
Maximum coolant temperature in the coolant circuit 
 
3.4 
Times 
Table 7: Abbreviations for times 
ttest 
Test duration 
toperation 
Operating duration over the service life 
tr 
Rise time (e.g., of a voltage curve) 
tf 
Fall time (e.g., of a voltage curve) 
 
3.5 
Internal resistance, terminal designation, frequency 
Table 8: Abbreviations for resistances, terminals, and frequencies 
Ri 
Internal resistance of the source, including the power supply 
wiring harness (see section 5.1) 
Terminal designations 
As per DIN 72552-2 
f 
Frequency 
 


### 第 13 页
 
  
Page 13 
VW 80000: 2017-10 
 
 
 
 
 
 
 
3.6 
Standard tolerances 
Unless otherwise specified, the tolerances specified in Table 9 apply. 
The tolerances have reference to the required measured value. 
Table 9: Standard tolerances 
Frequencies 
±1% 
Temperatures 
±2 °C 
Humidity 
±5% 
Times 
+5%; 0% 
Voltages 
±2% 
Currents 
±2% 
Vibrations 
±3 dB 
Vibration PSD 
±5% 
3.7 
Standard values 
Unless otherwise specified, the standard values specified in Table 10 apply. 
Table 10: Standard values 
Room temperature 
TRT = 23 °C ±5 °C  
Humidity 
Hrel = 25% to 75% relative humidity  
Test temperature 
Ttest = TRT 
Operating voltage 
(for test) 
Vop = 14 V 
 
 


### 第 14 页
Page 14 
VW 80000: 2017-10 
 
 
 
 
 
 
4 General requirements 
4.1 
Sampling rates and measured-value resolutions 
The sampling rate or bandwidth of the measuring system must be adapted to the DUT 
in question. All measured values must be recorded with all maximum values (peaks). 
 
The resolution of the measured values must be adapted to the test in question. It must 
be ensured that voltage peaks do not lead to an overflow and that there will not be 
cases in which they cannot be measured due to insufficient resolution. Data 
reduction/abstraction (e.g., limit monitoring, bus-message evaluation) must not 
suppress irregularities. 
4.2 
Functional states 
4.2.1 
General 
This section describes the functional state of the DUT before, during, and after the test. 
The purchaser must define the functional behavior (including derating, e.g., in terms of 
temperature and voltage) of the component in the functional states, as well as the 
customer's perception (e.g., visual, acoustic, tactile, thermal) in the drawing or in the 
Performance Specification. 
Memory functions must always remain in functional state A, in all cases. The integrity of 
non-volatile memories must be guaranteed at all times. The time sequences of the 
functional states must be indicated in the Performance Specification. Permitted event 
memory entries must be agreed upon with the purchaser and must be recorded in 
writing. 
Damage to the DUT is not permissible in functional states A to D. Undefined functions 
are not permissible at any time. The permissible limits (e.g., electrical, thermal, 
mechanical) for the electrical/electronic components installed in the DUT, as specified in 
the data sheets, must not be exceeded. This is verified at least by the P-02 Parameter 
test (small) specified in section 4.7.2. 
4.2.2 
Functional state A 
The DUT fulfills all functions as specified. 
4.2.3 
Functional state B 
Not used. 
4.2.4 
Functional state C 
The DUT does not fulfill one or more functions during exposure to the test parameters. 
After exposure to the test parameters, the DUT must immediately return to functional 
state A automatically or by means of the external triggers specified in the Performance 
Specification. Undefined functions are not permissible at any time. 


### 第 15 页
 
  
Page 15 
VW 80000: 2017-10 
 
 
 
 
 
 
 
4.2.5 
Functional state D1 
The DUT does not fulfill one or more functions during exposure to the test parameters. 
After exposure to the test parameters, the DUT must return to functional state A by 
means of terminal on/off switching (if necessary, with bus idle). 
4.2.6 
Functional state D2 
The DUT does not fulfill one or more functions during exposure to the test parameters. 
After exposure to the test parameters, the DUT must return to functional state A by 
means of a simple intervention (e.g., replacement of a faulty fuse). 
4.2.7 
Functional state E 
The DUT does not fulfill one or more functions during exposure to the test parameters 
and must be repaired or replaced after exposure to the test parameters. 
The DUT must fulfill flame-retardant classification V-0 as per UL 94 published by 
Underwriters Laboratories. 
4.3 
Operating situations 
For vehicles with a powertrain driven by an internal combustion engine only, vehicle 
operation can usually be divided into the following operating situations over its service 
life: 
 Driving 
 Parking (referred to as "off-grid parking" in this document)  
 Assembly 
 Operation (general) 
 
For vehicles with an electric drive, additional operating situations must be taken into 
account as needed: 
 Charging 
 Preconditioning 
 On-grid parking 
 
All operating situations relevant to the component must be taken into account when 
deriving the test requirements. 
4.4 
Operating states 
4.4.1 
General 
To facilitate a reproduction during the tests of the various component loads that occur in 
real-life situations, the following operating states are defined. 
If additional operating states are relevant to the component, they must be defined 
together with the purchaser. 
4.4.1.1 
Operating state – Not electrically connected 
The DUT is free of voltage and current. If a coolant circuit is present, it is not filled and 
its connections are sealed. 
A distinction is made between  


### 第 16 页
Page 16 
VW 80000: 2017-10 
 
 
 
 
 
 
- 
"not installed": DUT without connector and wiring harness 
- 
"assembly": DUT with connected connector and wiring harness 
4.4.1.2 
Operating state – Electrically connected and low operating load  
The DUT must be operated with the lowest possible real-life operating load for the 
operating situation in question. This may also mean that the DUT is operated without an 
operating load. 
The voltage supply of all voltage levels relevant to the component (12-V electric system, 
48-V electric system, HVAC, and HVDC) and, if applicable, the bus activity, must be 
recreated according to the actual situation in the vehicle for the operating situation in 
question. 
If a coolant circuit is present, it must be filled, and the coolant hoses must be connected. 
The flow rate and temperature of the coolant must be adjusted as required, as defined 
in the Performance Specification. 
 
This operating state is identified by "min" in this document. 
4.4.1.1 Operating state – Electrically connected and high operating load 
The DUT must be operated with the maximum operating load as per the design load 
profile (power user, but no misuse). 
The DUT must be operated in such a way that it generates a maximum power loss (e.g., 
by means of a realistic maximization of a continuous output power or by means of 
frequent activation of external loads). 
The voltage supply of all voltage levels relevant to the component (12-V electric system, 
48-V electric system, HVAC, and HVDC) and, if applicable, the bus activity, must be 
recreated according to the actual situation in the vehicle for the operating situation in 
question. 
If a coolant circuit is present, it must be filled, and the coolant hoses must be connected. 
The flow rate and temperature of the coolant must be adjusted as required, as defined 
in the Performance Specification. 
 
This operating states is identified by "max" in this document. 
4.5 
Operating modes 
4.5.1 
General 
The following applies in general: 
The operating mode of the component is yielded from the allocation of the operating 
states to the respective operating situations (Operating situationoperating state). 
 
Details concerning the operating modes, operating loads (e.g., activation, bus activity, 
bus messages, original sensors, original actuators, substitute circuits), and the required 
general conditions must be agreed upon between the purchaser and contractor, and 
documented. 


### 第 17 页
 
  
Page 17 
VW 80000: 2017-10 
 
 
 
 
 
 
 
4.5.2 
Operating modes for operating state "Not electrically connected"  
Operating state "Not electrically connected" is allocated to operating situation 
"Assembly." A distinction is made between the following operating modes of the 
component: 
 
- 
Assemblynot installed: 
 
Note: This operating mode was referred to as "I.a" in earlier versions of this document. 
 
- 
Assemblyassembly: 
 
Note: This operating mode was referred to as "I.b" in earlier versions of this document. 
4.5.3 
Operating modes for operating state "Electrically connected and 
low operating load" 
Operating state "Electrically connected and low operating load" is allocated to operating 
situations "Driving," "Charging," "Preconditioning," "On-grid parking," and "Off-grid 
parking." This yields the following operating modes: 
- 
Drivingmin  
- 
Chargingmin 
- 
Preconditioningmin  
- 
On-grid parkingmin  
- 
Off-grid parkingmin 
 
Out of these operating modes with low operating load, the operating mode during which 
the component generates the least power loss must be determined. This is defined in 
addition to the other operating modes, and referred to as: 
 
- 
Operationmin  
 
If there are multiple operating modes with low operating load during which the 
component generates power loss or has specific functions, the component must be 
operated in these operating modes intermittently; all functions of the relevant operating 
modes must be taken into account here. 
4.5.4 
Operating modes for operating state "Electrically connected and 
high operating load" 
Operating state "Electrically connected and high operating load" is allocated to 
operating situations "Driving," "Charging," "Preconditioning," "On-grid parking," and "Off-
grid parking." This yields the following operating modes: 
- 
Drivingmax 
- 
Chargingmax 
- 
Preconditioningmax  
- 
On-grid parkingmax 
- 
Off-grid parkingmax 
 


### 第 18 页
Page 18 
VW 80000: 2017-10 
 
 
 
 
 
 
Out of these operating modes with high operating load, the operating mode during 
which the component generates the greatest power loss must be determined. This is 
defined in addition to the other operating modes, and referred to as: 
 
- 
Operationmax 
 
If there are multiple operating modes with high operating load during which the 
component generates a significant power loss or has specific functions, the component 
must be operated in these operating modes intermittently; all functions of the relevant 
operating modes must be taken into account here. 
4.5.5 
Overview of operating modes 
Table 11: Classification and designation of operating modes 
Operating situation Operating mode:  
Not electrically connected 
Operating mode: 
Electrically connected 
and low operating load 
Operating mode: 
Electrically connected 
and high operating 
load 
Assembly 
 
Assemblynot installed (formerly I.a)  
 
Assemblyassembly (formerly I.b) 
 
 
Driving 
 
Drivingmin 
Drivingmax 
Charging 
 
Chargingmin 
Chargingmax 
Preconditioning 
 
Preconditioningmin 
Preconditioningmax 
On-grid parking 
 
On-grid parkingmin 
On-grid parkingmax 
Off-grid parking 
 
Off-grid parkingmin 
Off-grid parkingmax 
 
 
 
 
General 
 
Operationmin  
Operationmax  
 
4.5.6 
Application of the operating modes 
The operating modes for the component in question must be defined and agreed upon 
with the purchaser in line with Table 11, before the start of reliability testing. 
Table 12: Example operating modes – DUT electrically connected 
Operating mode 
Car stereo with 
navigation system 
Anti-theft alarm 
system 
On-board charger 
Drivingmin 
Component switched off 
by driver, 
bus/microcontrollers active 
No function 
No function, voltage supplies 
(12-V electric system and, if 
applicable, HVDC) applied 
Drivingmax 
Component switched on 
(data buses, electronic 
drives, navigation system, 
final stages), active 
No function 
No function, voltage supplies 
(12-V electric system and, if 
applicable, HVDC) applied 


### 第 19 页
 
  
Page 19 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Chargingmin 
Not relevant 
Not relevant 
Charging process with low 
charging power, voltage 
supplies (12-V electric system, 
HVAC, and HVDC) applied 
Chargingmax 
Charging process with high 
charging power, voltage 
supplies (12-V electric system, 
HVAC, and HVDC) applied 
Preconditioningmin 
No charging process, voltage 
supply to components 
necessary for preconditioning 
with low power, voltage supplies 
(12-V electric system, HVAC, 
and HVDC) applied 
Preconditioningmax 
No charging process, voltage 
supply to components 
necessary for preconditioning 
with high power, voltage 
supplies (12-V electric system, 
HVAC, and HVDC) applied 
On-grid parkingmin 
No charging process, power-
line communication active 
On-grid parkingmax 
Off-grid parkingmin 
Component in sleep mode, 
post-run ended 
Deactivated by 
customer, no 
function 
No charging process, voltage 
supply (only 12-V electric 
system) applied 
Off-grid parkingmax 
Component switched on 
(data buses, electronic 
drives, navigation system, 
final stages), active 
Function active 
Operationmin 
Component in sleep mode, 
post-run ended 
(corresponds to "Off-grid 
parkingmin") 
No function 
(corresponds to 
"Drivingmin") 
No charging process, voltage 
supply (only 12-V electric 
system) applied (corresponds to 
"Off-grid parkingmin") 
Operationmax 
Component switched on 
(data buses, electronic 
drives, navigation system, 
final stages), active 
(corresponds to 
"Drivingmax," "Off-grid 
parkingmax") 
Function active 
(corresponds to "Off-
grid parkingmax") 
Charging process with high 
charging power, voltage 
supplies (12-V electric system, 
HVAC, and HVDC) applied 
(corresponds to "Chargingmax") 
 
4.6 
Temperature stabilization 
A component exposed to a constant ambient temperature under defined operating 
conditions is said to be thermally stabilized from the point in time that the temperature 
does not vary by more than ±3 °C at any point of the component over the course of time 
going forward. 
 
The contractor must determine the time until complete temperature stabilization is 
achieved through experiments, and indicate it in the test documentation. 
For temperature cycle tests, after reaching a temperature-stabilized state, the DUTs 
must additionally be kept at the specified temperature reference values for a defined 
period, so that stresses in the component can be converted into elongations. This 
additional hold time must be indicated for the respective tests. 


### 第 20 页
Page 20 
VW 80000: 2017-10 
 
 
 
 
 
 
4.7 
Parameter test  
A set of sensitive parameters, or "key" parameters, such as the quiescent-current draw, 
operating currents, output voltages, contact resistances, input impedances, signal rates 
(rise and fall times), and bus specifications, must be defined in the Performance 
Specification. These parameters must be checked before the start of each test, and at 
the conclusion of each test, in terms of their compliance with specifications. 
For components connected to the coolant circuit, the parameter tests must be 
performed at TRT with Tcool,nom, at Tmax with Tcool,max, and at Tmin with Tcool,min. 
Unless otherwise specified in the Performance Specification, the parameter tests must 
be performed at Vopmin with Vopmin,HV, at Vop with Vop,HV, and at Vopmax with Vopmax,HV for 
components with an HV supply. 
 
The parameter test must be performed immediately upon completion of the test. 
The time between the end of the test and the performance of the parameter test must 
be documented in the test report. 
4.7.1 
P-01 Parameter test (function check)  
4.7.1.1 
Purpose 
The parameter test (function check) is meant to provide evidence of a DUT's flawless 
function at a specified temperature and at voltages Vopmin, Vop, and Vopmax. 
4.7.1.2 Test 
The key parameters must be measured and recorded. The functional behavior at a 
specified temperature and at each of the voltages Vopmin, Vop, and Vopmax must be 
checked. 
The basic functionalities of the components must be measured. 
For components with an event memory, the content of the event memory must be read 
out. 
4.7.1.3 
Requirement 
Functional state A. 
Changes in the values of the key parameters, the basic functionality of the component, 
or the event memory entries must be evaluated in terms of the preceding test loads as 
compared to the unused condition. 
The results must be documented in the test report. 
 
4.7.2 
P-02 Parameter test (small) 
4.7.2.1 
Purpose 
The parameter test (small) is meant to provide evidence of a DUT's flawless function at 
room temperature and operating voltage. 
4.7.2.2 
Test 
The key parameters must be measured and recorded. The functional behavior of the 
components at TRT and Vop must be checked. 


### 第 21 页
 
  
Page 21 
VW 80000: 2017-10 
 
 
 
 
 
 
 
The components must be checked in the context of a visual inspection as per 
DIN EN 13018. 
For components with an event memory, the content of the event memory must be read 
out and documented. The event memory entry must then be cleared. 
The test sequence and the results must be documented. 
4.7.2.3 
Requirement 
Functional state A. 
Changes in the values of the key parameters, the functional behavior, or the event 
memory entries, as well as irregularities in the visual inspection, must be evaluated in 
terms of the preceding test loads as compared to the unused condition. 
The visual inspection must evaluate any external damage or changes, such as cracks, 
ruptures, flaking, discoloration, and deformation. Visual irregularities are not 
permissible. 
There must not be any loose parts inside the device. 
 
4.7.3 
P-03 Parameter test (large) 
4.7.3.1 
Purpose 
The parameter test (large) is meant to provide evidence of flawless function at certain 
temperatures and voltages. 
4.7.3.2 
Test 
The key parameters must be measured, and the functional behavior of the components 
must be measured at each of the temperatures Tmax, TRT, and Tmin at voltages Vopmin, 
Vop, and Vopmax. 
Internal and external measurable parameters for evaluating the components’ accuracy 
and function must be recorded in this test. 
A reference must be created, against which changes to the DUTs caused by test loads 
can be determined by comparison. 
The recorded parameters must be documented and evaluated in the test report. This 
applies to the following data in particular: 
 All functional variables 
 Chromaticity coordinate, illumination, and contrast of light sources and displays 
 Characteristic curves (sensors, converters, motors) 
 Event memory entries 
 Reset and error counter counts 
 EEPROM content check 
 Curve of the recorded current over time in the transition from "Operationmin" to 
"Operationmax" (with the goal of determining the aging of electric subcomponents 
based on current-curve changes) 
 Tactile properties 
 Acoustics 
 Dimensional stability (deformation), gaps, function of clips 
 Actuation forces/torques 
 The leak tightness of leak-tight DUTs must be tested as per section 4.9. 


### 第 22 页
Page 22 
VW 80000: 2017-10 
 
 
 
 
 
 
The components must be checked in the context of a visual inspection as per 
DIN EN 13018. A manual shaking test must be carried out to identify any loose parts 
inside the device. 
For components with an event memory, the content of the event memory must be read 
out and documented. The event memory entry must then be cleared. 
The test sequence and the results must be documented. 
******* 
Requirement 
Functional state A 
The specified tolerances of form and functional tolerances must be adhered to. 
Changes in the values of the key parameters, the functional behavior, as well as 
irregularities in the visual inspection, must be evaluated in terms of the preceding test 
loads as compared to the unused condition. 
The visual inspection must evaluate any external damage or changes, such as cracks, 
ruptures, flaking, discoloration, and deformation. Visual irregularities are not 
permissible. 
There must not be any loose parts inside the device. 
Event memory entries and counter changes must reflect precisely those that should 
have been triggered by the test and the functional requirement. 
 
4.7.4 
P-04 Physical analysis 
4.7.4.1 
Purpose 
The physical analysis must be carried out after each reliability testing phase (B-sample, 
C-sample, etc.) to identify changes to the component as compared to the unused 
condition. 
4.7.4.2 
Test 
Examination methods as per appendix H that are required for physical analysis must be 
agreed upon between the purchaser and contractor, and documented. 
All DUTs must be opened and visually inspected as per DIN EN 13018. 
If a DUT has any irregularities, the further analysis, possibly with additional DUTs or the 
use of additional analysis methods, must be agreed upon with the purchaser. 
4.7.4.3 
Requirement 
The results must be documented and evaluated in the test report. 
 
4.8 
Continuous parameter monitoring with drift analysis 
The key parameters to be monitored must be recorded throughout the entire test. 
 
For components with an event memory, the event memory must be monitored 
continuously and the entries must be documented. 
 
The data obtained from continuous parameter monitoring must be examined for trends 
and drifts to identify any component irregularities, aging, or malfunctions. 


### 第 23 页
 
  
Page 23 
VW 80000: 2017-10 
 
 
 
 
 
 
 
4.9 
Leak tightness 
4.9.1 
Leak tightness requirement 
The required leak tightness of a component in terms of its self-contained electronics 
space relative to the environment or other spaces, such as the coolant duct of a coolant 
circuit, is described as a leak tightness requirement. The leak tightness requirement is 
defined as a limit leakage rate and must be verified by means of a leak tightness test. 
 
For the limit leakage rate in electronic control units (ECUs), an initial value of 
1 to 10 cm³/min at Δp = 300 mbar can be assumed for environmental loads in the form 
of water. This must be adapted on a component-specific basis depending on the 
surrounding medium, and verified by measurements. 
 
For components without a sealed housing, the definition and verification of a limit 
leakage rate is not required. 
 
4.9.2 
Leak tightness test 
Adherence to the defined component-specific limit leakage rate of the electronics space 
of a component is verified by means of a leak tightness test. 
When measuring on the DUT, the air leakage rate must be determined using common 
measurement methods (e.g., absolute pressure measurement, differential pressure 
measurement, mass airflow measurement, or volumetric flow rate measurement). 
For this purpose, the component is exposed to a defined test pressure via an access 
point (e.g., venting element); the air leakage rate is determined by measurement after a 
settling period. 
Because the design and application determine which pressure of a medium the 
component is exposed to in real-life operation, the test pressure corresponding to the 
most severe use case in the field must be selected. This may also be a negative 
pressure (vacuum). If the assumption is that the sealing system behaves differently on 
exposure to positive and negative pressure (e.g., sealing lips being pressed against a 
surface), the test must be performed with both positive and negative pressure. The test 
pressure to be applied must be agreed upon between the contractor and purchaser, and 
documented. 
 
The leak tightness test must be performed in the context of P-03 Parameter test (large) 
specified in section 4.7.3 at TRT. The DUT must not be exposed to any temperature 
fluctuations during the measurement. The measured air leakage rate must not exceed 
the defined, component-specific limit leakage rate, and must be documented in the test 
report. Changes in the air leakage rate must be evaluated and must be documented in 
the test report. 
 


### 第 24 页
Page 24 
VW 80000: 2017-10 
 
 
 
 
 
 
Part I – Electrical requirements and tests on the 12-V 
electric system 
5 General requirements 
5.1 
Voltages and currents 
The specified voltage curves must be interpreted as envelopes. Curves with any 
contour within the specified test and reference curves must be expected as the actual 
voltage curves. 
 
All voltage and current specifications have reference to the component (at its terminal). 
This does not apply to tests for which the internal resistance Ri is specified. In this case, 
the voltage and current specifications refer to the source (see Figure 1). 
 
 
US 
VS 
RL 
RL 
Ri 
Ri 
DUT 
DUT 
 
Legend 
VS 
Source 
RL 
Line resistance and contact resistance 
Ri 
Internal resistance observed at the terminals of 
the component in the direction of the source 
Figure 1: Internal resistance  
 
All edge descriptions refer to the 10% or 90% voltage values. 
5.2 
Test voltages 
Test voltages, particularly those for overvoltage and undervoltage tests, may deviate 
significantly from the operating voltage ranges in section 5.3 and will be specified 
separately. 
Functional state A (see section 4.2) must always be fulfilled within the voltage range 
applicable to the component. 
US
Ri
RL
DUT


### 第 25 页
 
  
Page 25 
VW 80000: 2017-10 
 
 
 
 
 
 
 
5.3 
Function classes and operating voltage ranges  
A DUT usually has several functions, which can each be allocated to different function 
classes. The following table lists the functional states (A or C; see section 4.2) required 
for each of the function classes. 
Table 13: Function classes and operating voltage ranges 
Voltage range [V] 
Test 
Duration 
Function class 
1 2 3 4 5  6 
17 – 26 
E-04 
≤ 60 s 
C C C C C  A 
16 – 17 
E-01 
≤ 1 h 
A A A C C  A 
 
 
 
 
 
 
 
 
 
 
18 – 27 
E-05 
≤ 300 ms A A A C C  A 
17 – 18 
E-02 
≤ 400 ms A A A A A  A 
16 – 17 
E-02 
≤ 600 ms A A A A A  A 
 
 
 
 
 
 
 
 
 
 
9.8 – 16 
 
Static 
A A A A A  A 
9 – 9.8 
 
A A C A C  C 
6 – 9 
 
A C C C C  C 
 
 
 
 
 
 
 
 
 
 
≥ 9 
E-03a 
500 ms 
A A A A A  A 
≥ 7 
E-11 
See E-11 A A A A A  A 
≥ 6 
E-03b 
See E-03b A A A C C  A 
≥ 4.5 
E-11, normal 
See E-11 A C C C C  C 
≥ 3.2 
E-11, severe 
See E-11 A C C C C  C 
≥ 0 
E-10 
≤ 100 µs A A A A A  A 
 
Minimum requirement for the use of the function classes: 
Function class 1:  
Functions that are necessary for ensuring and maintaining the 
power supply or are relevant to starting 
Function class 2:  
Functions with high availability (see Table 2) 
Function class 3:  
Functions necessary for driving operation 
Function class 4:  
Convenience functions that must be retained during "engine off" or 
"electric-system supply only from store" 
Function class 5:  
Convenience functions that must be available during engine 
operation ("engine on") or "electric-system supply active" 
Function class 6:  
Diagnostics and communication 
5.4 
Interface description 
All interfaces must be fully described with regard to their states and electrical properties. 
This description is used as the basis for evaluating the test results and must be detailed 
accordingly. 
5.5 
Procedure restrictions 
The test laboratory must be organized and operated as per DIN EN ISO/IEC 17025. All 
test equipment used for measurement must be calibrated as per 
DIN EN ISO/IEC 17025 (or as specified or recommended by the manufacturer), and 
must be traceable to PTB or an equivalent, national standards laboratory. The test 
devices, equipment, setups, and test methods must not limit or distort the behavior of 


### 第 26 页
Page 26 
VW 80000: 2017-10 
 
 
 
 
 
 
the DUT (e.g., current draw). They must be documented in the test report together with 
the accuracies and the expiration date of the calibration. 
5.6 
Test sequence 
An electrical test begins when the DUT is completely started up and is in functional 
state A. 
 
Unless otherwise specified, electrical loading must be realized and operated with 
original loads. 
 
Component function according to specifications must be tested in all relevant operating 
phases of the device. The following operating phases must be tested: 
 Startup phase/power-up 
 Operation in different functional states 
 Shutdown phase/power-down 
 Sleep mode 
Additional operating phases must be agreed upon with the purchaser. 
 
No later than from the C-sample version, the DUT must be operated with application 
software during the test. 
 
If the software package, application parameters, or processor utilization affects the test 
result, the affected tests must be repeated if these are changed. 
 
The software version and level of functionality must be indicated in the test report. 
 
The purchaser must approve the opening of DUTs, except for the physical analysis. 
 
The electrical tests can be performed in any order. The permissible event memory 
entries and the functional states of the component must be specified for each test. 
 
Unless otherwise specified in the test selection table as per section 6, all the test cases 
in a test must be performed. 
 
The electrical tests may be performed during an environmental test (see part II) if this 
does not contradict the test requirements of the electrical test and the purchaser has 
approved this approach. If the DUT demonstrates any irregularities during combined 
tests, the tests must be repeated individually. 
 
A set of sensitive parameters, or "key" parameters, such as the quiescent-current draw, 
operating currents, output voltages, contact resistances, input impedances, signal rates 
(rise and fall times), and bus specifications, must be defined in the Performance 
Specification or in agreement with the purchaser. These parameters must be checked 
before the start of each test, and at the conclusion of each test, in terms of their 
compliance with specifications. 
 
The key parameters to be monitored must be recorded during each test. Resets of the 
component must be monitored in a suitable manner and documented. 


### 第 27 页
 
  
Page 27 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Before an electrical test with a defined internal resistance, the test setup must be 
verified at the DUT connector by means of a reference measurement, and documented. 
Unless otherwise specified in the test, the reference measurement must be carried out 
with a dummy load equivalent to 150% of the load current for operating mode 
"Operationmax." 
 
Before and after each test, the DUTs must undergo a P-02 Parameter test (small) as 
per section 4.7.2 as defined in the Performance Specification. For climatic loads, the 
parameter test is performed within one hour after test completion. 
 
Before the first electrical test and after the last electrical test, the P-03 Parameter test 
(large) as per section 4.7.3 must be performed as defined in the Performance 
Specification. 
 
The measurement results and data from the before/after tests may differ from each 
other only within the specified permissible tolerances. Changes in the measured values 
greater than the measurement accuracies must be highlighted. The measurement 
results must be examined for trends and drifts to detect component irregularities, aging, 
or malfunctions. 
 
The physical analysis as per section 4.7.4 must be carried out after all electrical tests 
have been completed on at least one DUT. 
 


### 第 28 页
Page 28 
VW 80000: 2017-10 
 
 
 
 
 
 
6 Test selection table 
Table 14: Test selection table 
Test 
To be applied to 
To be additionally defined by 
the purchaser 
E-01 Long-term 
overvoltage  
Components supplied via the 
12-V electric system 
Component necessary for 
driving operation 
E-02 Transient 
overvoltage  
Components supplied via the 
12-V electric system 
None 
E-03a Transient 
undervoltage  
Components supplied via the 
12-V electric system 
None 
E-03b Transient 
undervoltage  
Components supplied via the 
12-V electric system 
Severity 
E-04 Jump start  
Components supplied via the 
12-V electric system 
Component relevant/not 
relevant to starting 
E-05 Load dump  
Components in vehicles with 
a 12-V alternator 
Safety-relevant component 
E-06 Ripple voltage  
Test case 1 for all 
components, test 
cases 2 and 3 only for 
vehicles with a 12-V 
alternator, test case 4 only for 
vehicles with a DC-DC 
converter 
Test cases based on 
connection in the electric 
system 
E-07 Slow decrease and 
increase of the supply 
voltage  
All components 
Relevant terminal status 
E-08 Slow decrease, 
quick increase of the 
supply voltage 
All components 
Relevant terminal status 
E-09 Reset behavior  
All components 
Relevant terminal status, 
general test conditions  
E-10 Brief interruptions  
All components 
None 
E-11 Start pulses  
Components supplied via the 
12-V electric system; may not 
be applicable to vehicles 
without a 12-V starter 
Component relevant/not 
relevant to starting 
E-12 Voltage curve with 
electric system control 
Components supplied via the 
12-V electric system 
None 
E-13 Pin interruption 
All components 
Relevant terminal status 
E-14 Connector 
interruption  
All components 
None 
E-15 Reverse polarity  
Components that can be 
exposed to reverse polarity in 
the vehicle 
Severity, component switch-
off in the event of reverse 
polarity 
E-16 Ground potential 
difference 
All components 
None 


### 第 29 页
 
  
Page 29 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Test 
To be applied to 
To be additionally defined by 
the purchaser 
E-17 Short circuit in 
signal line and load 
circuits  
All components 
None 
E-18 Insulation 
resistance  
Components with galvanically 
isolated portions 
None 
E-19 Quiescent current  
Components that are 
continuously supplied with 
voltage (e.g., t.30, t.30f, 
t.30g) 
None 
E-20 Dielectric strength  
Components with inductive 
parts (e.g., motors, relays, 
coils) 
None 
E-21 Backfeeds  
Components that are 
electrically connected to t.15 
or to other terminals with a 
wake-up function  
Severity 
E-22 Overcurrents 
Components that have an 
output 
None 
E-23 Equalizing currents 
of multiple supply 
voltages 
Components supplied via an 
independent t.30 
None 
 
 


### 第 30 页
Page 30 
VW 80000: 2017-10 
 
 
 
 
 
 
7 Electrical requirements and tests 
7.1 
E-01 Long-term overvoltage 
7.1.1 
Purpose 
This test examines component behavior and resistance in the event of long-term 
overvoltage. 
The overvoltage can be caused by a fault of the energy source generating the voltage 
and recreates a single fault in the power supply. 
7.1.2 
Test  
Table 15: Test parameters for E-01 Long-term overvoltage 
Operating mode of the DUT 
Operating mode "Operationmax" 
Vmax 
17 V (+4%, 0%) 
Vmin 
13.5 V 
tr 
< 10 ms 
tf 
< 10 ms 
t1 
60 min 
Ttest 
Tmax - 20 K 
Number of cycles 
1 
Number of DUTs 
At least 6 
 
 
U 
V 
Umax 
Vmax 
Umin 
Vmin 
Figure 2: Test pulse for E-01 Long-term overvoltage 
If no thermal steady state (< 1 K in 10 min) has been reached on the DUT after the 
minimum duration, the test must be prolonged until a thermal steady state is reached. 
t1
tf
tr
Umin
Umax
U
t


### 第 31 页
 
  
Page 31 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.1.3 
Requirement 
See Table 13: Function classes and operating voltage ranges. 


### 第 32 页
Page 32 
VW 80000: 2017-10 
 
 
 
 
 
 
7.2 
E-02 Transient overvoltage 
7.2.1 
Purpose 
Switching off loads and engine revving (tip-in) may result in transient overvoltages in the 
electric system. These overvoltages are simulated in this test. 
7.2.2 
Test  
Table 16: Test parameters for E-02 Transient overvoltage 
Operating mode of the DUT Operating mode "Drivingmax" 
Vmin 
16 V 
V1 
17 V 
Vmax 
18 V (+4%, 0%) 
tr 
1 ms 
tf 
1 ms 
t1 
400 ms 
t2 
600 ms 
Number of DUTs 
At least 6 
Test case 1 
Ttest 
Tmax 
Number of cycles 
3 
t3  
2 s 
Test case 2 
Ttest 
Tmin 
Number of cycles 
3 
t3 
2 s 
Test case 3 
Ttest 
TRT 
Number of cycles 
100 
t3 
8 s 
 


### 第 33 页
 
  
Page 33 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
U 
V 
Umax 
Vmax 
U1 
V1 
Umin 
Vmin 
 
Figure 3: Test pulse for E-02 Transient overvoltage 
7.2.3 
Requirement 
See Table 13: Function classes and operating voltage ranges. 
 
t2
tf
tf
Umin
Umax
U
t
t1
tr
t3
U1


### 第 34 页
Page 34 
VW 80000: 2017-10 
 
 
 
 
 
 
7.3 
E-03 Transient undervoltage 
7.3.1 
Purpose 
Switching on loads may result in transient undervoltages, depending on the state of the 
power electric system (e.g., availability of energy stores). 
7.3.2 
Test E-03a 
Table 17: Test parameters for E-03a Transient undervoltage 
Operating mode of the DUT Operating mode "Operationmax" 
Vmax 
10.8 V 
Vmin 
9 V 
tf 
1.8 ms 
t1 
500 ms 
tr 
1.8 ms 
t2 
1 s 
Number of cycles 
10 
Test case 1 
 
Ttest 
Tmax 
Test case 2 
 
Ttest 
Tmin 
 
Figure 4: Test pulse for E-03a Transient undervoltage 
U 
V 
Umax 
Vmax 
Umin 
Vmin 
 
 
t1
tf
Umin
Umax
U
t
tr
t2


### 第 35 页
 
  
Page 35 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.3.3 
Test E-03b 
Table 18: Test parameters for E-03b Transient undervoltage 
Parameter 
Severity 1 
Operating mode of the DUT 
Operating mode "Operationmax"  
V1 
10.8 V 
V2 
6 V 
V3 
8 V 
V4 
9 V 
t1 
5 ms 
t2  
20 ms 
t3 
2 ms 
t4 
180 ms 
t5 
1 ms 
t6 
300 ms 
t7 
2 ms 
t8 
1 s 
Number of cycles 
10 
Test case 1 
Ttest 
Tmax 
Test case 2 
Ttest 
Tmin 
 
Severity 1 must be applied for functions that are used to maintain driving readiness and 
for the power supply, as well as functions for which a higher degree of availability is 
required. 
 
Figure 5: Test pulse for E-03b Transient undervoltage 
U 
V 
U1 
V1 
U2 
V2 
U3 
V3 
U4 
V4 


### 第 36 页
Page 36 
VW 80000: 2017-10 
 
 
 
 
 
 
7.3.4 
Requirement 
See Table 13: Function classes and operating voltage ranges. 


### 第 37 页
 
  
Page 37 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.4 
E-04 Jump start 
7.4.1 
Purpose 
This test simulates an external power supply to a vehicle. The maximum test voltage is 
yielded from commercial vehicle systems and their increased electric-system voltages. 
7.4.2 
Test  
Table 19: Test parameters for E-04 Jump start 
Operating mode of the DUT Operating mode "Operationmax" 
V0 
0 V 
V1 
3 V (+0%, -15%) 
V2 
10.8 V 
V3 
26 V (+4%, 0%) 
t1 
1 s 
t2 
0.5 s 
t3 
5 s 
t4 
1 s 
t5 
60 s 
tr 
< 2 ms 
tf 
< 100 ms 
Number of cycles 
1 
Number of DUTs 
At least 6 
 
Figure 6: Test pulse for E-04 Jump start 
U 
V 
U1 
V1 
U2 
V2 
U3 
V3 
U0 
V0 
7.4.3 
Requirement  
See Table 13: Function classes and operating voltage ranges. 


### 第 38 页
Page 38 
VW 80000: 2017-10 
 
 
 
 
 
 
7.5 
E-05 Load dump 
7.5.1 
Purpose 
Dumping of an electric load, in conjunction with a battery with reduced buffering 
capacity, results in a high-energy overvoltage pulse due to generator properties. This 
test is meant to simulate this pulse. 
7.5.2 
Test 
Table 20: Test parameters for E-05 Load dump 
Operating mode of the DUT 
Operating mode "Drivingmax" 
Vmin 
13.5 V 
Vmax 
27 V (+4%, 0%) 
tr 
≤ 2 ms 
t1 
300 ms 
tf  
≤ 30 ms 
Break between cycles 
1 min 
Number of cycles 
10 
Number of DUTs 
At least 6 
 
 
Figure 7: Test pulse for E-05 Load dump 
U 
V 
Umax 
Vmax 
Umin 
Vmin 
 
7.5.3 
Requirement 
See Table 13: Function classes and operating voltage ranges. 
Umin
Umax
U
t
tr
t1
tf


### 第 39 页
 
  
Page 39 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.6 
E-06 Ripple voltage 
7.6.1 
Purpose 
Alternating-current (AC) voltages may be superimposed on the electric system. This 
ripple voltage may be present at any time during engine operation. These tests simulate 
this situation. 
7.6.2 
Test 
Table 21: Test parameters for E-06 Ripple voltage 
Operating mode of the DUT 
Operating mode "Drivingmax" 
Vmax 
Vopmax 
Ri 
≤ 100 m 
Type of wobble 
Triangle, logarithmic 
Number of cycles 
15 
Number of DUTs 
At least 6 
Test case 1  
VPP 
2 V (+4%, 0%) 
Frequency range 
15 Hz to 30 kHz 
Wobble period t1 
2 min 
Test case 2  
VPP 
3 V (+4%, 0%)  
for components between the battery and generator, 
particularly in the case of a battery connection far from 
the generator 
Frequency range 
15 Hz to 30 kHz 
Wobble period t1 
2 min 
Test case 3  
VPP 
6 V (+4%, 0%)  
for all components during drives without the battery 
(emergency mode) or in the case of a connection close to 
the generator 
Frequency range 
15 Hz to 30 kHz 
Wobble period t1 
2 min 
Test case 4 
VPP 
1 V (+4%, 0%) 
for components supplied from the DC-DC converter 
Frequency range 
30 kHz – 200 kHz 
Wobble period t1 
10 min 
 


### 第 40 页
Page 40 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Figure 8: Test pulse for E-06 Ripple voltage 
U 
V 
Umax 
Vmax 
UPP 
VPP 
 
******* 
Test setup 
The electric system conditions must be agreed upon with the appropriate departments. 
The test setup must be documented in detail, including line inductance values, line 
capacitance values, and line resistance values. 
 
7.6.3 
Requirement 
Test case 1: functional state A 
Test case 2: functional state A 
Test case 3: 
a) Components necessary for driving operation: 
Functional state A 
b) All other components: 
Functional state C 
Test case 4: functional state A 
 
Umax
U
t
UPP
t1


### 第 41 页
 
  
Page 41 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.7 
E-07 Slow decrease and increase of the supply voltage 
7.7.1 
Purpose 
The slow decrease and increase of the supply voltage is simulated as it occurs during 
the slow discharging and charging processes of the vehicle battery. 
7.7.2 
Test E-07a 
Table 22: Test parameters for E-07a Slow decrease and increase of the supply voltage 
Operating mode of the DUT  
Operating mode "Operationmin" and "Operationmax" 
Must be performed with all relevant states of the 
voltage supply terminals (e.g., t.15, t.30, t.87) and 
their combinations 
Start voltage 
Vopmax (+4%, 0%) 
Rate of voltage change 
0.5 V/min (+10%, -10%) 
V1 
Vopmin  
t1 
Hold time at V1 until event memory has been 
completely read out 
Minimum voltage 
0 V  
V2 
Vopmin  
t2 
Hold time at V2 until event memory has been 
completely read out 
End voltage 
Vopmax (+4%, 0%) 
Number of cycles 
For each relevant terminal state and their 
combinations: 
1 cycle with operating mode "Operationmin" 
1 cycle with operating mode "Operationmax" 
Number of DUTs 
3 
 
Figure 9: Test pulse for E-07a Slow decrease and increase of the supply voltage 
U 
V 
UBmax 
Vopmax 
UBmin 
Vopmin 
U1 
V1 
t1
t2
UBmin
UBmax
U
t
0 V
U1
U2


### 第 42 页
Page 42 
VW 80000: 2017-10 
 
 
 
 
 
 
U2 
V2 
7.7.3 
Test E-07b 
Table 23: Test parameters for E-07b Slow decrease and increase of the supply voltage 
Operating mode of the DUT  Operating mode "Operationmin" and "Operationmax" 
Must be performed with all relevant states of the voltage 
supply terminals (e.g., t.15, t.30, t.87) and their 
combinations 
Vmax 
14.5 V 
Vmin 
1.5 V 
VPP 
3 V 
V1 
Vopmin + 1.5 V 
V2 
Vopmin + 1.5 V 
Frequency 
Sinusoidal, 50 Hz 
Wobble period 
52 min 
Number of cycles 
For each relevant terminal state and their combinations: 
1 cycle with operating mode "Operationmin" 
1 cycle with operating mode "Operationmax" 
Number of DUTs 
3 
 
 
Figure 10: Test pulse for E-07b Slow decrease and increase of the supply voltage 
U 
V 
Umax 
Vmax 
Umin 
Vmin 
U1 
V1 
U2 
V2 
Funktionszustand A 
Functional state A 
Funktionszustand C 
Functional state C 
 


### 第 43 页
 
  
Page 43 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.7.4 
Requirement 
The evaluation of the test results depends on the voltage range that is applied to the 
component during the test. 
 
A distinction is made between the following: 
a) Within the defined operating voltage range of the component: 
Functional state A 
b) Outside of the defined operating voltage range of the component: 
Functional state C 


### 第 44 页
Page 44 
VW 80000: 2017-10 
 
 
 
 
 
 
7.8 
E-08 Slow decrease, quick increase of the supply voltage 
7.8.1 
Purpose 
This test simulates the slow decrease of the battery voltage to 0 V and the sudden re-
application of the battery voltage, e.g., from a jump start source. 
7.8.2 
Test 
Table 24: Test parameters for E-08 Slow decrease, quick increase of the supply voltage 
Operating mode of the DUT 
Operating mode "Operationmin" and "Operationmax" 
Must be performed with all relevant states of the voltage 
supply terminals (e.g., t.15, t.30, t.87) and their 
combinations 
Start voltage 
Vopmax (+4%, 0%) 
Voltage drop 
0.5 V/min (+10%, -10%)  
V1 
Vopmin  
t1 
Hold time at V1 until event memory has been completely 
read out 
Hold time at Vopmin 
Until the event memory has been completely read out 
Minimum voltage 
0 V  
t2 
At least 1 min, but until internal capacitors are fully 
discharged 
End voltage 
Vopmax (+4%, 0%) 
tr 
≤ 0.5 s  
Number of cycles 
For each relevant terminal state and their combinations: 
1 cycle with operating mode "Operationmin" 
1 cycle with operating mode "Operationmax" 
Number of DUTs 
At least 6 
 
 
Figure 11: Test pulse for E-08 Slow decrease, quick increase of the supply voltage 
U 
V 
UBmax 
Vopmax 
UBmin
UBmax
U
t
0 V
tr
t2
t1
U1


### 第 45 页
 
  
Page 45 
VW 80000: 2017-10 
 
 
 
 
 
 
 
UBmin 
Vopmin 
U1 
V1 
7.8.3 
Requirement 
The evaluation of the test results depends on the voltage range that is applied to the 
component during the test. 
 
A distinction is made between the following ranges: 
a) Within the defined operating voltage range of the component: 
Functional state A 
b) Outside of the defined operating voltage range of the component: 
Functional state C 
 


### 第 46 页
Page 46 
VW 80000: 2017-10 
 
 
 
 
 
 
7.9 
E-09 Reset behavior 
7.9.1 
Purpose 
The reset behavior of a component in its environment is recreated and tested. General 
test conditions (e.g., network, terminal, system) must be described in detail. 
An arbitrary chronological sequence of repeated switch-on/switch-off processes occurs 
during operation and must not result in undefined component behavior. 
The reset behavior is reflected in a voltage variance and in a time-based variance. Two 
different test sequences are required to simulate varying switch-off times. A component 
must always run through both sequences. 
7.9.2 
Test 
Table 25: Test parameters for E-09 Reset behavior 
Operating mode of the DUT 
Operating mode "Operationmin," "Drivingmin," and 
"Operationmax" 
Must be performed with all relevant states of the voltage 
supply terminals (e.g., t.15, t.30, t.87) and their 
combinations 
Vmax 
Vopmin (0%, -4%) 
Vth 
6 V 
V1 (range Vmax to Vth) 
0.5 V 
V2 (range Vth to 0 V) 
0.2 V 
t2 
At least 10 s and until the DUT has reached 100% 
operability again (all systems rebooted without errors). 
tr 
≤ 10 ms 
tf 
≤ 10 ms 
Number of cycles 
For each test sequence, for each relevant terminal state 
and their combinations: 
1 cycle with operating mode "Operationmin" 
1 cycle with operating mode "Drivingmin" 
1 cycle with operating mode "Operationmax" 
Number of DUTs 
At least 3 
Test case 1 
t1 
5 s 
Test case 2 
t1 
100 ms 


### 第 47 页
 
  
Page 47 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Figure 12: Test pulse for E-09 Reset behavior 
U 
V 
Umax 
Vmax 
Uth 
Vth 
ΔU1 
ΔV1 
ΔU2 
ΔV2 
t 
t 
 
7.9.3 
Requirement 
Functional state A once Vmax is reached again 
 
Undefined operating states must not occur under any circumstances. 
 
Proof of adherence to the specified threshold value must be provided. The voltage level 
above which the component leaves functional state A for the first time must be 
recorded. 
 
Umax
Uth
0V
U
t
tf
U2
U1
t1
t2
tr


### 第 48 页
Page 48 
VW 80000: 2017-10 
 
 
 
 
 
 
7.10 
E-10 Brief interruptions 
7.10.1 
Purpose 
This test simulates the behavior of the component in the event of brief interruptions of 
varying durations. 
Test case 1 represents a supply voltage interruption at the component. 
Test case 2 represents a supply voltage interruption in the electric system. 
Such interruptions can occur due to events such as contact faults and line faults or 
relay-contact bounce. 
7.10.2 
Test 
Table 26: Test parameters for E-10 Brief interruptions 
Operating mode of the DUT 
Operating mode "Operationmax" 
Vtest 
11 V 
State 1 (Z1) 
Switch 1 (S1) closed 
Z2 
S1 open 
tr 
≤ (0.1 * t1) 
tf  
≤ (0.1 * t1) 
Switch S1 must be switched with the 
following sequences: 
t1 
Increments 
Severity 1 
10 µs to 100 µs 
10 µs 
100 µs to 1 ms 
100 µs 
1 ms to 10 ms 
1 ms 
10 ms to 100 ms 
10 ms 
100 ms to 2 s 
100 ms 
Severity 2 
10 µs to 100 µs 
10 µs 
100 µs to 1 ms 
100 µs 
1 ms to 200 ms  
1 ms 
200 ms to 2 s 
100 ms 
t2 
> 10 s 
Test voltage Vtest must be held at least until the 
DUT and the peripherals have reached 100% 
operability again. 
Number of cycles 
1 
Number of DUTs 
At least 6 
Test case 1 
S1 switched, S2 statically open 
Test case 2 
S1 switched, S2 negated with respect to S1 
The duration of the voltage sag is increased by the increments specified in Table 26. 
This yields the diagram shown in Figure 13. 
 
The voltage on the DUT can be limited by the test setup to the maximum voltage of test  
E-05 Load dump (see section 7.5). 
 


### 第 49 页
 
  
Page 49 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Figure 13: Change in state of switch S1 for E-10 Brief interruptions 
 
******** 
Test setup 
 
Figure 14: Schematic circuit diagram for E-10 Brief interruptions 
Utest 
Vtest 
 
The closed switch S2, including the necessary cables, must be implemented with a 
series resistance of < 100 mΩ. 
 
******** 
Test sequence 
One reference measurement each with 100 Ω (±5%) and 1 Ω (±5%) as a DUT dummy 
must be carried out and documented. The slew rate must be verified with this test setup. 
Low-inductance components must be used as resistors. 
The tests as per Table 26 must then be performed. 
7.10.3 
Requirement  
For t1 < 100 µs: functional state A 
Z2
Z1
S1
t
tf
t1
t1
t2
tr
Utest
S1
S2
DUT


### 第 50 页
Page 50 
VW 80000: 2017-10 
 
 
 
 
 
 
For t1 ≥ 100 µs: functional state C 
 
The time value t1 beyond which the DUT leaves functional state A for the first time must 
be recorded. 


### 第 51 页
 
  
Page 51 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.11 
E-11 Start pulses 
7.11.1 
Purpose 
When the engine is started, the battery voltage drops to a low value for a short period in 
order to then rise again slightly. Most components are briefly activated immediately 
before the starting process, deactivated during the starting process, and then activated 
again after the starting process once the engine is running. This test examines the 
behavior of the component in the event of voltage sags caused by starting. 
The starting process may be carried out under varying vehicle starting conditions: cold 
start and hot start (automatic restart in the case of a start-stop system). In order to cover 
both cases, two different test cases are required. A component must always run through 
both sequences. 
7.11.2 
Test 
Table 27: Test parameters for E-11 Start pulses 
Operating mode of the DUT 
Operating mode "Operationmin," "Drivingmin," and 
"Operationmax" 
If necessary, additional operating loads must be 
defined in the respective operating mode. 
Test pulse  
- Cold start: "normal" and "severe" test pulse as per 
Table 28 
- Hot start: "short" and "long" test sequence as per 
Table 29 
Number of DUTs 
At least 6 
 
7.11.2.1 
Test case 1 – Cold start 
Table 28: Test parameters for E-11 Cold start 
Parameter 
"Normal" test pulse 
"Severe" test pulse 
Vop 
11.0 V  
11.0 V 
VT 
4.5 V (0%, -4%) 
3.2 V +0.2 V 
VS 
4.5 V (0%, -4%) 
5.0 V (0%, -4%) 
VA 
6.5 V (0%, -4%) 
6.0 V (0%, -4%) 
VR 
2 V 
2 V 
tf 
≤ 1 ms 
≤ 1 ms 
t4 
0 ms 
19 ms 
t5 
0 ms 
≤ 1 ms 
t6 
19 ms 
329 ms 
t7 
50 ms 
50 ms 
t8 
10 s 
10 s 
tr 
100 ms 
100 ms 
f 
2 Hz 
2 Hz 
Break between cycles 
2 s 
2 s 
Test cycles 
10 
10 


### 第 52 页
Page 52 
VW 80000: 2017-10 
 
 
 
 
 
 
 
U 
V 
UB 
Vop 
UA 
VA 
US 
VS 
UT 
VT 
UR 
VR 
 
Legend 
a 
t.50 off 
b 
t.50 on 
c 
t.50 off 
ttest 
Cycle 
Figure 15: Test pulse for E-11 Cold start 
7.11.2.2 
Test case 2 – Hot start 
Table 29: Test parameters for E-11 Hot start 
Parameter 
"Short" test sequence 
"Long" test sequence 
Vop 
 
11.0 V 
VT 
 
7.0 V (0%, -4%) 
VS 
 
8.0 V (0%, -4%) 
VA 
 
9.0 V (0%, -4%) 
t50 
 
≥ 10 ms 
tf 
 
≤ 1 ms 
t4 
 
15 ms 
t5 
 
70 ms 
t6 
 
240 ms 
t7 
 
70 ms 
t8 
 
600 ms 
tr 
 
≤ 1 ms 
Break between cycles 
5 s 
20 s 
Test cycles 
10 
100 
 
 
 
 
 
1/f 
U 
UB 
US 
UT 
tf 
t5 
t4 
t6 
t8 
t 
UA 
t7 
tr 
b 
c 
ttest 
UR 
a 


### 第 53 页
 
  
Page 53 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
U 
V 
UB 
Vop 
UA 
VA 
US 
VS 
UT 
VT 
 
Legend 
a 
t.50 off 
b 
t.50 on 
c 
t.50 off 
ttest 
Cycle 
Figure 16: Test pulse for E-11 Hot start 
7.11.3 
Requirement 
The test must not result in any event memory entries. 
The vehicle must be capable of starting in all cases. 
7.11.3.1 
Components relevant to starting: 
 
Test case 1 – Cold start: 
"Normal" test pulse: functional state A 
"Severe" test pulse: functional state A 
 
Test case 2 – Hot start: 
"Long" test sequence: functional state A 
"Short" test sequence: functional state A 
7.11.3.2 
Components not relevant to starting: 
Test case 1 – Cold start: 
"Normal" test pulse: functional state C 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
U 
UB 
US 
UT 
tf 
t5 
t4 
t6 
t8 
t 
UA 
t7 
tr 
b 
 
c 
 
ttest 
t50 
a 
 


### 第 54 页
Page 54 
VW 80000: 2017-10 
 
 
 
 
 
 
"Severe" test pulse: functional state C 
 
Test case 2 – Hot start: 
"Long" test sequence: functional state A 
"Short" test sequence: functional state A 


### 第 55 页
 
  
Page 55 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.12 
E-12 Voltage curve with electric system control 
7.12.1 
Purpose 
This test simulates the behavior of the electric system with voltage controls, e.g., with 
the use of intelligent generator or DC-DC converter controls. By means of the control, 
voltage curves can be set in the range between constant voltage to permanent voltage 
fluctuations according to the test cases. This is relevant to all load cases that the 
component can assume with the engine running or the vehicle ready for operation. 
7.12.2 
Test 
Table 30: Test parameters for E-12 Voltage curve with electric system control 
Operating mode of the DUT 
Operating mode "Drivingmax" 
Vmin 
(11.8 V - V) (0%, -4%) 
Vmax 
(16 V - V) (+4%, 0%) 
t1 
2 s 
tr 
400 ms 
tf 
400 ms 
Number of cycles 
10 
Number of DUTs 
At least 6 
Test case 1 
V 
0 V 
Test case 2 
V 
0.7 V 
Test case 3 
V 
2 V 
 
Figure 17: Test pulse for E-12 Voltage curve with electric system control 
U 
V 
Umax 
Vmax 
Umin 
Vmin 
Umin
Umax
U
t
tf
tr
t1
t1


### 第 56 页
Page 56 
VW 80000: 2017-10 
 
 
 
 
 
 
7.12.3 
Requirement 
Functional state A  
7.13 
E-13 Pin interruption 
7.13.1 
Purpose 
This test simulates the line interruption of individual pins. The test must be performed in 
two different operating states. Different pulse forms must be used, because the possible 
interruptions may differ greatly in terms of their duration (from loose contacts to 
permanent interruption). 
7.13.2 
Test 
Table 31: Test parameters for E-13 Pin interruption 
Operating mode of the DUT Operating mode "Operationmin" and "Operationmax" 
 
Must be performed with all relevant states of the voltage 
supply terminals (e.g., t.15, t.30, t.87) and their 
combinations 
Z1 
State 1: pin connected 
Z2 
State 2: pin interrupted 
tr 
≤ (0.1 * t1) 
tf 
≤ (0.1 * t1) 
Number of cycles 
The following applies to the two test cases and the 
relevant terminal state: 
3 cycles with operating mode "Operationmin" 
3 cycles with operating mode "Operationmax" 
 
Each test must be evaluated separately. 
Number of DUTs 
At least 6 
Test case 1  
 
Each pin must be removed for t = 10 s and then replaced 
(slow interval). 
Test case 2 
 
Burst on each pin to simulate a loose contact (figure 18) 
Number of pulses t2 in the 
burst 
4 000 
a 
Burst 
t1 
0.1 ms 
t2 
1 ms 
t3 
10 s 


### 第 57 页
 
  
Page 57 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Figure 18: Test pulse for E-13 Pin interruption, test case 2 
7.13.2.1 
Test sequence  
The component is connected to the voltage supply. 
The test must not be performed on the supply pins (e.g., t.15, t.30, t.87), unless one of 
these pins is used as a wake-up line. 
The test must also be performed on ground pins (t.31). 
 
The voltage on the pin can be limited to the maximum voltage of the test E-05 Load 
dump (see section 7.5). 
 
One reference measurement each with 1 kΩ (±5%) and 1 Ω (±5%) as a DUT dummy 
must be carried out and documented. The slew rate must be verified with this test setup. 
Low-inductance components must be used as resistors. 
 
The tests as per Table 31 must then be performed. 
7.13.3 
Requirement 
For all test cases: functional state C 
 
tr
t3
t2
t1
tf
Z2
Z1
Pin
t
a


### 第 58 页
Page 58 
VW 80000: 2017-10 
 
 
 
 
 
 
7.14 
E-14 Connector interruption 
7.14.1 
Purpose 
This test simulates the line interruption of connectors. 
7.14.2 
Test 
Table 32: Test parameters for E-14 Connector interruption 
Operating mode of the DUT 
Operating mode "Operationmin" and "Operationmax" 
Number of cycles 
Each connector must be removed once in both 
operating modes. 
Number of DUTs 
At least 6 
7.14.2.1 
Test sequence  
Each connector must be removed from the DUT for 10 s and then replaced. If the DUT 
has several connectors, each connector must be tested individually. The test sequence 
must be varied. If there are several connectors, their combinations must also be tested. 
7.14.3 
Requirement 
Functional state C 
 


### 第 59 页
 
  
Page 59 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.15 
E-15 Reverse polarity 
7.15.1 
Purpose 
The resistance of the DUT to reverse-polarity battery connection during jump starting is 
tested. Reverse polarity can occur several times and must not cause damage to the 
component. Reverse polarity protection must be ensured for any voltages down to the 
minimum test voltage. The vehicle fuse is not part of the reverse polarity protection 
strategy. 
7.15.2 
Test 
All relevant connections of the original circuitry must be tested. 
The DUT must be activated according to the circuitry in the vehicle. 
The test must be performed at various voltages between 0 V and the maximum values 
specified in Table 33. 
 
The current draw during the test must be documented. 
Table 33: Test parameters for E-15 Reverse polarity 
Operating mode of the DUT Operating mode "Operationmin" (static reverse polarity) 
Operating mode "Operationmax" (dynamic reverse polarity) 
Test case 1 
Static reverse polarity as per Table 34 
Test case 2 
Dynamic reverse polarity as per Table 35 
Number of DUTs 
At least 6 
 
7.15.2.1 
Test case 1 – Static reverse polarity 
This test case checks the robustness of the component at various reverse polarity 
voltages that can arise depending on the vehicle state. 
 
Table 34: Test parameters for E-15 Static reverse polarity 
Vmax 
0 V  
Vmin 
-14.0 V  
∆V1 
-1 V 
Severity 1 
Ri < 100 mΩ 
Severity 2 
Ri < 30 mΩ  
t1 
60 s 
 
For a component for which the operating voltage is switched off 
by a relay in the event of reverse polarity, the following deviating 
value applies: 
8 ms 
t2 
≥ 60 s, but at least until the component has reached the same 
thermal state as at the start of the test 
tr 
≤ 10 ms 
tf 
≤ 10 ms 
Number of cycles 
1 


### 第 60 页
Page 60 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Figure 19: Test pulse for E-15 Static reverse polarity 
U 
V 
Umax 
Vmax 
Umin 
Vmin 
ΔU1 
ΔV1 
 
7.15.2.2 
Test case 2 – Dynamic reverse polarity 
This test case checks the reverse polarity of the component during operation in a 
vehicle that is no longer capable of starting. 
 
Table 35: Test parameters for E-15 Dynamic reverse polarity 
Vmax 
10.8 V  
Vmin 
-4.0 V 
Severity 1 
Ri < 100 mΩ 
Severity 2 
Ri < 30 mΩ  
t1 
60 s 
 
For a component for which the operating voltage is switched off 
by a relay in the event of reverse polarity, the following deviating 
value applies: 
8 ms 
t2 
≤ 5 min 
tr 
≤ 10 ms 
tf 
≤ 10 ms 
Number of cycles 
3 
Umin
Umax
U
t
U1
t1
t2
tf
tr


### 第 61 页
 
  
Page 61 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Figure 20: Test case for E-15 Dynamic reverse polarity 
U 
V 
Umax 
Vmax 
Umin 
Vmin 
ΔU1 
ΔV1 
 
7.15.3 
Requirement 
Safety-relevant functions, e.g., of power windows, the power sliding sunroof, or the 
starter, must not be triggered during periods of reverse polarity. 
 
Functional state C 
Umin
Umax
U
t
0 V
t1
t2
tf
tr


### 第 62 页
Page 62 
VW 80000: 2017-10 
 
 
 
 
 
 
7.16 
E-16 Ground potential difference 
7.16.1 
Purpose 
Potential differences between various ground connection locations can cause signal 
distortions between components at these connection locations. It must be ensured that 
potential differences between ground points up to a magnitude of ±1 V (static) in the 
electrical assembly do not affect component functions. 
7.16.2 
Test 
If the DUT has several voltage and ground connections, the test must be performed 
separately for each connection point. 
 
The component is wired up as in Figure 21. 
Table 36: Test parameters for E-16 Ground potential difference 
Operating mode of the DUT 
Operating mode "Operationmax"  
Test duration 
≥ 60 s 
V 
1 V  
Number of cycles 
Both switching positions 
Number of DUTs 
At least 6 
 
UB 
Vop 
TE 
Terminal 
U 
V 
Kl.31 
t.31 
 
Legend 
B 
Bus system 
S 
Signal line 
S1 
Two-pin (a/b) change-over switch 
Terminal Other component, e.g., test reference, test bed, simulation control module, 
actuator, sensor, or load 
Figure 21: Schematic circuit diagram for E-16 Ground potential difference 
TE
DUT
B
S
UB
Kl.31
S1b
S1a
1
2
1
2
U


### 第 63 页
 
  
Page 63 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.16.3 
Requirement 
Functional state A  
7.17 
E-17 Short circuit in signal line and load circuits 
7.17.1 
Purpose 
This test simulates short circuits on all device inputs and outputs and in the load circuit. 
All inputs and outputs must be short-circuit-proof to t.30 and t.31 (for activated and non-
activated outputs, with and without voltage supply, and with and without ground 
connection). 
The component must be able to withstand a sustained short circuit. 
7.17.2 
Test 
Table 37: Test parameters for E-17 Short circuit in signal line and load circuits 
Operating mode of the DUT Operating mode "Operationmax"  
Test duration 
Each combination of test voltage and test case for 
60 s 
Test voltages 
Vopmin and Vopmax 
Test case 1 
Each pin alternately to t.30 and t.31 with voltage 
supply and with ground connection 
Test case 2 
Each pin alternately to t.30 and t.31 without voltage 
supply and with ground connection 
Test case 3 
Each pin alternately to t.30 and t.31 with voltage 
supply and without ground connection 
Number of DUTs 
At least 6 
 
If the voltage supply/ground supply is fed via several pins, these combinations must 
also be taken into account. 
7.17.2.1 
Test setup  
The power supply unit used for the test must be able to supply the short-circuit currents 
to be expected by the component. If this is not possible, buffering of the power supply 
unit by means of a car battery is permissible (Vopmax is the maximum charging voltage in 
this case). 


### 第 64 页
Page 64 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Figure 22: Schematic circuit diagram  
 
UB 
Vop 
UB DTU 
DUT Vop 
PWR 
PWR 
E 
I 
GND 
GND 
Kl. 31 DUT 
DUT t.31 
A 
O 
L 
L 
Kl. 31 
t.31 
 
Legend 
L  
 
Load 
I  
 
Input 
O  
 
Output 
PWR   
Output of DUT Vop 
GND   
Input/output of DUT t.31 
 
7.17.2.2 
Test sequence 
For inputs and outputs: record and evaluate the curve of the short-circuit current over 
time. 
The functional effects of the short circuits must be documented. 
 
7.17.3 
Requirement 
For inputs and outputs (I and O): functional state C 
For looped-through supply voltages (PWR): functional state D2 
For device ground (GND): functional state E. This also applies to the vehicle contact set 
and wiring harness, and must be guaranteed by the component. 
 


### 第 65 页
 
  
Page 65 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.18 
E-18 Insulation resistance 
7.18.1 
Purpose 
This test determines the insulation resistance between components with galvanic 
isolation. Only the galvanically isolated pins that are connected in the vehicle and that 
require isolation properties for their function are examined. 
7.18.2 
Test 
Table 38: Test parameters for E-18 Insulation resistance 
Operating mode of the DUT 
Operating mode "Assemblynot installed" 
Test voltage 
500 V DC 
Test duration 
60 s 
Test points 
Application of the test voltage  
- 
To terminals without a galvanic connection 
- 
Between connection pins and conductive 
housing without a galvanic connection 
- 
Between connection pins and an electrode 
around the housing if the housing is non-
conductive 
- 
To additional test points agreed upon with the 
appropriate department 
Number of cycles 
1 cycle must be performed, in which each of the 
points defined above must be tested at least once. 
Number of DUTs 
At least 6 
 
7.18.2.1 
Test sequence 
This test must be performed after the tests "Damp heat, constant" and "Damp heat, 
cyclic." 
After the "Damp heat, constant" test, the DUTs must be ventilated for 30 min before the 
measurement of the insulation resistance is carried out. 
The insulation resistance must be measured immediately after the "Damp heat, cyclic" 
test. 
7.18.3 
Requirement 
The insulation resistance must be at least 10 M. 
Functional state A must be verified after the test. 


### 第 66 页
Page 66 
VW 80000: 2017-10 
 
 
 
 
 
 
7.19 
E-19 Quiescent current 
7.19.1 
Purpose 
This test is meant to determine the quiescent-current draw of the component. 
7.19.2 
Test 
For components with a delayed cut-off function (e.g., fan), the quiescent-current draw 
must be determined after this function has stopped. 
The component must be measured with the associated peripherals and circuitry. 
 
Table 39: Test parameters for E-19 Quiescent current 
Operating mode of the DUT 
Operating mode "Operationmin"  
Test voltage 
12.5 V (+4%, 0%) 
Number of DUTs 
At least 6 
Test case 1 
T 
Tmin 
Test case 2 
T 
TRT 
Test case 3 
T 
Tmax 
7.19.3 
Requirement 
A target quiescent-current draw of 0 mA applies to all DUTs. 
 
For DUTs that must be operated after t.15 OFF, a quiescent-current draw equivalent 
(averaged over 12 h) of ≤ 0.1 mA corresponding to 1.2 mAh (above +40 °C, ≤ 0.2 mA) 
applies in the idle phase. This value must be adhered to under any conceivable idle 
conditions of the vehicle and over any 12-h period. Otherwise, a release by the 
department responsible for quiescent-current management is required. 
 
Post-run functions must also be released by the department responsible for quiescent-
current management. 


### 第 67 页
 
  
Page 67 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.20 
E-20 Dielectric strength 
7.20.1 
Purpose 
This test simulates the dielectric strength between components of the DUT that are 
galvanically isolated from each other, e.g., connector pins, relays, windings, or lines. 
The test must be performed on components that contain or control inductive 
subcomponents. 
7.20.2 
Test 
Table 40: Test parameters for E-20 Dielectric strength 
Operating mode of the DUT 
Operating mode "Operationmin" 
Test voltage VRMS 
500 V AC, 50 Hz, sinusoidal 
Test duration 
60 s 
Test points 
Application of the test voltage 
- 
To terminals without a galvanic connection 
- 
Between connection pins and conductive 
housing without a galvanic connection 
- 
Between connection pins and an electrode 
around the housing if the housing is non-
conductive 
- 
To additional test points agreed upon with 
the appropriate department 
Number of cycles 
1 cycle must be performed, in which each of the 
points defined above must be tested at least once. 
Number of DUTs 
At least 6 
 
7.20.2.1 
Test sequence 
This test must be performed after the tests "Damp heat, constant" and "Damp heat, 
cyclic." 
After the "Damp heat, constant" test, the DUTs must be ventilated for 30 min before the 
measurement of the insulation resistance is carried out. 
The insulation resistance must be measured immediately after the "Damp heat, cyclic" 
test. 
7.20.3 
Requirement 
Functional state C 
Dielectric breakdowns and electric arcs are not permissible. 


### 第 68 页
Page 68 
VW 80000: 2017-10 
 
 
 
 
 
 
7.21 
E-21 Backfeeds 
7.21.1 
Purpose 
The independence of switched terminals must be ensured. 
This test verifies that the DUT is free of backfeeds to switched terminals (t.15, t.87, 
t.30c, etc.). 
7.21.2 
Test 
Table 41: Test parameters for E-21 Backfeeds 
Operating mode of the DUT 
Operating mode "Operationmax"  
Vtest 
Vopmax - 0.2 V 
Test temperatures 
Tmax, TRT, and Tmin 
Test case 1 
Severity 1 
Severity 2 
R 
Not present 
≥ 10 kΩ 
S1 
Open 
Open 
S2 
Closed 
Closed 
Test case 2 
R 
≥ 10 kΩ 
S1 
Open 
S2 
Open 
Number of DUTs 
At least 6 
 
7.21.2.1 
Test sequence 
The DUT must be connected according to the circuitry in the vehicle (including sensors, 
actuators, etc.) and operated in normal operation. Switches S1 and S2 are closed. The 
voltage curve at the terminal being tested must be measured during switch-off of the 
terminal. To do this, the switches must be opened as per Table 41. 
The terminal must be switched off, e.g., by means of a relay or a switch 
(Rswitch_open → ∞). Other possible voltage sources, such as t.30, must not be 
disconnected or switched off during the test (according to the behavior in the vehicle). 
The voltage curve at the terminal being tested must be measured using a measuring 
device (V) with an input resistance of ≥ 10 MΩ (e.g., oscilloscope). 
 
 


### 第 69 页
 
  
Page 69 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Figure 23: Schematic circuit diagram of test   
Kl. 30 
t.30 
V 
V 
Kl. 30 DUT 
DUT t.30 
K 
t 
Kl. 31 DUT 
DUT t.31 
Kl. 31 
t.31 
 
Legend 
S1  
 
Switch 1 
S2  
 
Switch 2  
R  
 
Resistor 
t  
 
Terminal being tested 
V 
 
Measuring device 
 
7.21.3 
Requirement 
Voltage backfeeds to the terminal being tested are permissible only up to a maximum 
level of 1 V. This voltage range must be achieved within t = 20 ms from the time of the 
switch-off. 
 
The voltage at the non-wired terminal being tested must drop below a voltage of 1 V 
within t = 20 ms from the time of the switch-off. 
 
The voltage curve over time must continuously fall. Discontinuity of the curve due to 
positive pulses is not permitted. 


### 第 70 页
Page 70 
VW 80000: 2017-10 
 
 
 
 
 
 
7.22 
E-22 Overcurrents 
7.22.1 
Purpose 
This test examines the overcurrent strength of mechanical switches, electronic outputs, 
and contacts. Higher currents than in the normal load case (e.g., maximum stalling 
current Istall of a motor) must also be taken into account. 
7.22.2 
Test 
Table 42: Test parameters for E-22 Overcurrents 
Operating mode of the DUT 
Operating mode "Operationmax" 
Temperature 
Tmax 
Test condition for electronic 
outputs 
The output must be able to withstand at least 
three times the nominal load without damage. 
ttest 
30 min 
Test conditions for switched 
outputs 
For components with IN ≤ 10 A: 
 
Itest = 3 * IN 
For components with IN > 10 A: 
 
Itest = 2 * IN, but min. 30 A and max. 150 A 
For components with Istall > 3 * IN: 
 
Itest = Istall  
Under load, switch "OFF," "ON," and "OFF" again 
once. 
 
Load duration: 10 min 
 
In the case of multiple-contact relays and multiple-
contact switches, each contact must be tested 
individually. 
Number of DUTs 
At least 6 
 
7.22.3 
Requirement 
Functional state A for mechanical components without a fuse. If fuses are present in the 
load circuit, it is permissible for them to trip. 
 
Functional state C for electronic outputs with overload detection (current, voltage, 
temperature). 
 
In addition, no harmful changes that restrict the function or service life must be visible in 
a visual inspection of all components (visual and electrical characteristics). 
 


### 第 71 页
 
  
Page 71 
VW 80000: 2017-10 
 
 
 
 
 
 
 
7.23 
E-23 Equalizing currents of multiple supply voltages 
7.23.1 
Purpose 
For components with multiple supply voltage inputs independent of each other, e.g., if 
voltage is supplied by 12-V electric subsystems independent of each other, this test 
determines the internal independence of these supply branches. 
7.23.2 
Test 
Table 43: Test parameters for E-23 Equalizing currents 
Operating mode of the DUT 
Operating mode "Assemblyassembly" 
ttest 
60 s 
Test points 
Application of the test voltage between  
─ 
Both supply connections 
─ 
Additional test points agreed upon with the 
appropriate department 
See Figure 24. 
Number of cycles 
1 
Number of DUTs 
6 
Test case 1 
Vtest 
32 V 
Test case 2 
Vtest 
-32 V 
 
Figure 24: Schematic circuit diagram for E-23 Equalizing currents 
Utest 
Vtest 
Kl.30 
t.30 
Kl.30_2 
t.30_2 
BN1 
Electric system 1 
BN2 
Electric system 2 
Kl.31 
t.31 
Kl.31_2 
t.31_2 
 


### 第 72 页
Page 72 
VW 80000: 2017-10 
 
 
 
 
 
 
7.23.3 
Requirement 
The equalizing current measured in the test setup must not exceed 100 µA. A single 
fault must not override or jeopardize the independence of the supply branches. 
Functional state A must be verified after the test. 


### 第 73 页
 
  
Page 73 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Part II – Environmental requirements and tests 
8 Use profile 
8.1 
Service life requirements 
Table 44 shows the typical parameters for service life requirements. 
Table 44: Service life requirements 
Service life 
15 years 
Duration of driving operation 
8 000 h 
Mileage 
300 000 km 
 
For vehicles with an electric drive, it may be necessary to take additional operating 
situations into account (see section 4.3). 
 
The operating duration in the following additional operating situations (see 
section 4.3)  
 Duration of charging operation  
 Duration of preconditioning operation  
 Duration of on-grid parking operation 
must be defined in the Performance Specification on a component-specific basis. 
8.2 
Temperature load spectra 
In addition to the specification of minimum ambient temperature Tmin and maximum 
ambient temperature Tmax, the distribution indicating how long the component is 
exposed to the various temperatures between Tmin and Tmax must also be specified, 
in order to fully describe the temperature load to which the component is exposed at 
the point of use in the vehicle. 
 
For vehicles with an alternative powertrain, a distinction must be made between 
operating situations "Driving," "Charging," "Preconditioning," and "On-grid parking" 
and the respective temperature load spectra must be specified both for the ambient 
temperature and for the coolant circuit temperature. 
 
This temperature distribution is fundamentally a continuous distribution, because the 
ambient temperature of the component may be any value between Tmin and Tmax. 
For the design of the component and for the simplified calculation of test durations 
using the accelerated service-life model according to the Arrhenius equation (see 
Appendix D), this continuous distribution can be described well by several discrete 
temperature data points Tfield.i. For each temperature data point, the percentage 
share pi of the operating duration during which the component is exposed to the data-
point temperature must be specified. 
The corresponding temperature load spectrum therefore has the following general 
form: 
 
 


### 第 74 页
Page 74 
VW 80000: 2017-10 
 
 
 
 
 
 
Table 45: Temperature load spectrum 
Temperature in °C 
Distribution 
Tfield.1 = Tmin 
p1 
Tfield, 2 
p2 
… 
… 
Tfield.n = Tmax 
pn 
 
This is based mainly on field measurements and technical experience. 
 
Typical temperature load spectra for "Driving" operating situation in terms of different 
installation areas are indicated in Appendix B. 
The usability of these typical temperature load spectra for a specific component must 
be verified, e.g., by vehicle measurement, simulation, or experience. In case of 
deviations, the temperature load spectrum must be adapted to the particular 
component. 
A component-specific temperature load spectrum must be defined for special points 
of use or installed conditions (e.g., at a point of use near a heat source). 
The temperature load spectrum that applies must be documented in the Performance 
Specification. 
In addition to the typical temperature load spectra, typical values for the average 
temperature rise of a component in the vehicle during "Driving" operating situation 
are specified in Appendix B. 
For temperature load spectra that are defined or adapted on a component-specific 
basis, this value must also be defined on a component-specific basis and 
documented in the Performance Specification. 
 


### 第 75 页
 
  
Page 75 
VW 80000: 2017-10 
 
 
 
 
 
 
 
9 Test selection 
9.1 
Test selection table 
Table 46: Test selection table 
Test 
To be applied to 
Required specification 
M-01 Free fall 
All components 
For components that will obviously 
be damaged during the test (e.g., 
glass bodies, highly sensitive 
transducers), this test may be 
omitted in agreement with the 
purchaser. This must be 
documented. 
 None 
M-02 Stone impact test 
Components installed in areas that 
may be affected by stone impact 
 None 
M-03 Dust test 
All components 
 IP degree of protection 
 
Degree of protection IP6KX 
Components for which the ingress of 
dust is not permissible 
 
Degree of protection IP5KX 
Components for which the ingress of 
dust is permissible, but only as long 
as function and safety are not 
impaired 
M-04 Vibration test 
All components 
Vibration profile 
 
- As per vibration profile A 
Components installed on the engine 
 
- As per vibration profile B 
Components installed on the 
transmission 
 
- As per vibration profile C 
Components installed at the 
decoupled intake manifold 
 
- As per vibration profile D 
Components installed on sprung 
masses (body) 
 
- As per vibration profile E 
Components installed on unsprung 
masses (wheel, suspension) 
M-05 Mechanical shock 
All components 
 None 
M-06 Continuous mechanical shock 
Components mounted in or on 
doors, hoods, and tailgates/trunk lids 
Number of shocks 
K-01 High-/low-temperature aging 
All components 
None 
K-02 Incremental temperature test 
All components 
None 
K-03 Low-temperature operation 
All components 
None 
K-04 Repainting temperature 
Components installed in the exterior 
area which may be subjected to 
increased temperatures in the event 
of repainting 
None 
K-05 Thermal shock (component) 
All components 
-  
Test method (Na or Nc), 
if Nc: test medium 
 
As per 
DIN EN 60068-2-14 Na (air-
to-air) 
Components that are not 
permanently operated in a fluid 
 
As per 
DIN EN 60068-2-14 Nc 
(medium-to-medium) 
Components that are permanently 
operated in a fluid (IP X8) 
K-06 Salt spray test with operation, 
exterior 
Components mounted in the exterior 
area, underbody, or engine 
compartment 
None 


### 第 76 页
Page 76 
VW 80000: 2017-10 
 
 
 
 
 
 
Test 
To be applied to 
Required specification 
K-07 Salt spray test with operation, 
interior 
Components installed at exposed 
points in the vehicle interior (e.g., 
side pockets in the luggage 
compartment, door wet space, spare 
wheel well) 
None 
K-08 Damp heat, cyclic  
All components 
None 
K-09 Damp heat, cyclic  
(with frost) 
All components 
None 
K-10 Water protection – IPX0 to 
IPX6K 
All components 
IP degree of protection 
 
- Degree of protection IPX0 
Components that do not require 
water protection 
 
- Degree of protection IPX1 
Components for which vertically 
falling drops must have no harmful 
effect 
 
- Degree of protection IPX2 
Components with a tilt of up to 15° in 
the installation position, for which 
vertically falling drops must have no 
harmful effect 
 
- Degree of protection IPX3 
Components for which spray water 
must have no harmful effect 
 
- Degree of protection IPX4K Components for which splash water 
with increased pressure must have 
no harmful effect 
 
- Degree of protection IPX5 
Components for which water jets 
must have no harmful effect 
 
- Degree of protection IPX6K Components for which powerful 
water jets with increased pressure 
must have no harmful effect 
K-11 High-pressure 
cleaning/pressure washing 
Components that may be directly 
exposed to high-pressure 
cleaning/pressure washing or 
underbody washing 
None 
K-12 Thermal shock with splash 
water 
Components installed in the exterior 
area or engine compartment which 
are expected to be exposed to 
splash water (e.g., when driving 
through puddles).  
None 
K-13 Thermal shock – immersion 
Components installed below the 
fording depth for which temporary 
immersion in (salt) water is to be 
expected (e.g., when driving through 
water) (IPX7) 
None 
K-14 Damp heat, constant 
All components 
Severity 


### 第 77 页
 
  
Page 77 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Test 
To be applied to 
Required specification 
K-15 Condensation and climatic test 
The necessity of the test must be 
evaluated on a component-specific 
basis. If required, the necessity of 
the test must be indicated in the 
Performance Specification. 
 
If the test is indicated in the 
Performance Specification, the test 
can be performed as test K-15 a 
Condensation test with modules or 
as test K-15 b Climatic test for 
components with watertight housings 
for components with watertight 
housings; the test must be 
performed as test K-15 a 
Condensation test with modules for 
components without a watertight 
housing. 
None 
K-16 Thermal shock (without 
housing) 
Modules of all components 
 None 
K-17 Solar radiation 
Components exposed to direct solar 
radiation in the installation position 
Test profile 
K-18 Harmful gas test 
Components with open plug contacts 
and switching contacts  
 None 
C Chemical tests 
All components 
Chemicals 
Operating mode 
L-01 Service life test – 
Mechanical/hydraulic durability 
testing 
Components with 
mechanical/hydraulic 
actuation/function cycles, e.g., brake 
actuations, seat adjustment cycles, 
switch/button actuations 
Number of 
function/actuation 
cycles 
L-02 Service life test – High-
temperature durability testing 
All components 
Test duration 
L-03 Service life test – Temperature 
cycle durability testing 
All components 
Number of test cycles 
9.2 
Test sequence plan 
A component-specific test sequence plan must be defined in the Performance 
Specification. 
 
A test sequence plan is provided in Appendix A as a basis of discussion for 
collaborative projects between several original equipment manufacturers (OEMs) 
(e.g., industry modular assembly matrix or IBK). 
 
 


### 第 78 页
Page 78 
VW 80000: 2017-10 
 
 
 
 
 
 
10 Mechanical requirements and tests 
10.1 
M-01 Free fall 
10.1.1 
Purpose 
This test simulates the free fall of a component to the floor, as it may occur anytime 
throughout the process chain before the component is properly installed. 
 
It is meant to ensure that, in the event of a drop, a component that appears visibly 
undamaged and is therefore installed in the vehicle, does not have any concealed 
damage or premature damage, e.g., internal subcomponent detachments or cracks. 
10.1.2 
Test 
Table 47: Test parameters for M-01 Free fall 
Operating mode of the DUT 
Assemblynot installed  
Drop height 
1 m 
Impact surface 
Concrete floor 
Test cycle 
For each of the 3 DUTs, one drop in both directions 
of a spatial axis (1st DUT: ±X, 2nd DUT: ±Y, 3rd 
DUT: ±Z)  
Number of DUTs 
3 
10.1.3 
Requirement 
The DUT must be visually inspected with the naked eye and tested for loose or 
rattling parts by shaking. 
 
- 
If the DUT is visibly damaged, this damage must be documented in the test 
report. 
 
- 
If the DUT is not visibly damaged, it must be fully functional after the test, and 
all parameters must be within the specifications. This is verified by means of a 
P-03 Parameter test (large) as per section 4.7.3. 
 
- 
Concealed damage is not permissible. 
  
 


### 第 79 页
 
  
Page 79 
VW 80000: 2017-10 
 
 
 
 
 
 
 
10.2 
M-02 Stone impact test 
10.2.1 
Purpose 
This test simulates the mechanical load on the component due to stone impact. 
It is meant to verify the resistance of the component to flaw patterns, e.g., 
deformation or cracks. 
10.2.2 
Test 
The test is performed on the basis of DIN EN ISO 20567-1, test method B, with the 
following parameters: 
Table 48: Test parameters for M-02 Stone impact test 
Operating mode of the DUT 
Assemblyassembly 
Quantity of blasting medium 
500 g 
Test pressure 
2 bar 
Blasting material 
Chilled iron grit as per DIN EN ISO 11124-2, grain 
size 4 to 5 mm 
Test surface on DUT 
All surfaces that are freely accessible on the vehicle 
Impact angle 
54° relative to blasting direction 
Testing equipment 
Multiple stone-impact test device as per 
DIN EN ISO 20567-1 
Number of cycles 
2 
Number of DUTs 
6 
 
10.2.3 
Requirement 
The DUT must be fully functional before and after the test, and all parameters must 
meet the specifications. This is verified by means of a P-02 Parameter test (small) as 
per section 4.7.2. 
 
In addition, the DUT must be visually inspected with the naked eye and tested for 
loose or rattling parts by shaking. 
Changes/damage must be documented in the test report and evaluated together with 
the purchaser. 
 
An evaluation based on the characteristic values in DIN EN ISO 20567-1 is not 
required. 
 
 


### 第 80 页
Page 80 
VW 80000: 2017-10 
 
 
 
 
 
 
10.3 
M-03 Dust test 
10.3.1 
Purpose 
This test simulates the dust load of the component during vehicle operation. 
It is meant to verify the resistance of the component to electrical and mechanical 
error/flaw patterns. 
10.3.2 
Test 
The test is carried out as per ISO 20653 with the following parameters: 
Table 49: Test parameters for M-03 Dust test 
Operating mode of the 
DUT 
For electrical/electronic components: "Operationmin" 
For mechatronic components (e.g., for components with 
rotating parts, controls): 
"Operationmax" and "Operationmin" intermittently as per 
Figure 25. 
Test setup 
Vertical flow direction as per ISO 20653:2006, figure 1 
Degree of protection to 
be achieved 
As defined in the Performance Specification 
Test duration 
20 cycles of 20 minutes each 
Number of DUTs 
6 
 
 
 
Figure 25: Test sequence for M-03 Dust test 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. The test setup (as-installed position, covers, trim panels, situation 
during operation) must be suggested by the contractor, approved by the purchaser, 
and documented. 
10.3.3 
Requirement 
The required degree of protection defined in the Performance Specification as per 
ISO 20653 must be achieved. 
 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of a P-02 
Parameter test (small) as per section 4.7.2. 
 
In addition, the DUT must be visually inspected with the naked eye. Changes or 
damage must be documented in the test report and evaluated together with the 
purchaser. 
 
Operationmin 
5 s 
5 minutes 
1 cycle/20 minutes 
Dust 
Operationmax 


### 第 81 页
 
  
Page 81 
VW 80000: 2017-10 
 
 
 
 
 
 
 
10.4 
M-04 Vibration test 
10.4.1 
Purpose 
These tests simulate the vibrational load on the component during "Driving" operating 
situation. 
They are meant to verify the resistance of the component to flaw patterns, such as 
subcomponent detachments and material fatigue. 
10.4.2 
Test 
The test is performed on the basis of ISO 16750, part 3. 
The test is carried out as per DIN EN 60068-2-6 for sinusoidal vibration excitation 
and DIN EN 60068-2-64 for wide-band vibration excitation, with the following 
parameters: 
 
Table 50: General vibration test parameters 
Operating mode of the DUT  
"Drivingmin" and "Drivingmax" intermittently  
(see Figure 26) 
Superimposed temperature curve 
Repeating as per Figure 26 and Table 44 
Frequency sweep time for sinusoidal 
excitation 
1 octave/min, logarithmic 
Vibration profile A 
(for components installed on the engine) 
Vibration excitation, sinusoidal as per 
Figure27 and Table 52 
 
Vibration excitation, wide-band random 
vibration 
as per Figure 28 and Table 53 
Vibration profile B 
(for components installed on the 
transmission) 
Vibration excitation, sinusoidal as per 
Figure 29 and Table 54 
 
Vibration excitation, wide-band random 
vibration 
as per Figure 30 and Table 55 
Vibration profile C 
(for components installed at the 
decoupled intake manifold) 
Vibration excitation, sinusoidal as per 
Figure 31 and Table 56 
Vibration profile D 
(hang-on parts, for components installed 
on sprung masses) 
Vibration excitation, wide-band random 
vibration 
as per Figure 32 and Table 57 
Vibration profile E 
for unsprung masses (chassis) 
Vibration excitation, wide-band random 
vibration 
as per Figure 33 and Table 58 
Number of DUTs 
6 
 
Components that are installed on an electric machine must be tested at least as per 
vibration profile D. However, this test profile does not take into account the specific 
vibration loads that emanate from an electric machine. But in practice, these specific 
vibration loads can occur and act as a load on the component. Therefore, the specific 
vibration loads that emanate from an electric machine must be taken into account 


### 第 82 页
Page 82 
VW 80000: 2017-10 
 
 
 
 
 
 
during the test. For this purpose, measurements are required on the electric machine 
in question. 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
 
The manner of fastening connected lines (e.g., electric wiring, coolant hoses, 
hydraulic lines) in the test setup must be defined together with the purchaser. 
 
For components that are installed on the bracket or vehicle by means of damping 
elements, it must be specified with the purchaser whether 
 
- 
all DUTs must be tested with damping elements, 
- 
all DUTs must be tested without damping elements, or 
- 
three DUTs must be tested with damping elements and three DUTs must be 
tested without damping elements. 
 
The sampling rate must be selected in such a way that interruptions and short circuits 
can be unambiguously detected. 
Additional tests for verifying the strength of the whole system, consisting of the 
assembly of component, bracket, and add-on parts, must be agreed upon with the 
purchaser. 
 
 
Figure 26: Temperature curve for vibration 
Tmax 
Tmax 
Temperatur 
Temperature 
TRT 
TRT 
Tmin 
Tmin 
Fahrenmin 
Drivingmin 
Fahrenmax von 135min bis 410min 
Drivingmax from 135 min to 410 min 
Zeit in min. 
Time in min 
 
 


### 第 83 页
 
  
Page 83 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Table 51: Temperature curve for vibration 
Time in min 
Temperature in °C 
0 
TRT 
60 
Tmin 
150 
Tmin 
300 
Tmax 
410 
Tmax 
480 
TRT 
 
If a coolant circuit is present, the coolant temperature must track the respective test 
temperature to the limits Tcool,min and Tcool,max. Only the ambient temperature is varied 
outside of the coolant temperature limits. 


### 第 84 页
Page 84 
VW 80000: 2017-10 
 
 
 
 
 
 
10.4.2.1 
Vibration profile A (components installed on the engine) 
Table 52: Test parameters – Vibration, sinusoidal for engine-mounted parts  
Vibration excitation 
Sinusoidal 
Test duration for each 
spatial axis 
22 h 
Vibration profile 
Curve 1 applies to components mounted on engines with 
no more than 5 cylinders. 
Curve 2 applies to components mounted on engines with 
6 or more cylinders. 
 
For components that can be used in both scenarios, the 
curves are combined. 
Curve 1 in Figure27 
Frequency in Hz 
Amplitude of acceleration in m/s² 
100 
100 
200 
200 
240 
200 
270 
100 
440 
100 
Curve 2 in Figure27 
Frequency in Hz 
Amplitude of acceleration in m/s² 
100 
100 
150 
150 
440 
150 
Combination  
Frequency in Hz 
Amplitude of acceleration in m/s² 
100 
100 
150 
150 
200 
200 
240 
200 
255 
150 
440 
150 


### 第 85 页
 
  
Page 85 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
 
Figure27: Vibration profile, sinusoidal for engine-mounted parts 
Kurve 1 
Curve 1 
Kurve 2 
Curve 2 
Amplitude der Beschleunigung in m/s2 
Amplitude of acceleration in m/s2 
Frequenz in Hz 
Frequency in Hz 
 
Table 53: Test parameters – Vibration, wide-band random vibration for engine-mounted parts  
Vibration excitation 
Wide-band random vibration 
Test duration for each spatial axis 22 h 
RMS value of acceleration 
181 m/s² 
Vibration profile Figure 28 
Frequency in Hz Power density spectrum 
in (m/s²)²/Hz 
10 
10 
100 
10 
300 
0.51 
500 
20 
2 000 
20 
 
0
50
100
150
200
250
10
100
1000
Amplitude der Beschleunigung  in m/s²
Frequenz in Hz
Kurve 1
Kurve 2


### 第 86 页
Page 86 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Figure 28: Vibration profile, wide-band random vibration for engine-mounted parts 
Leistungsdichtespektrum in (m/s2)2/Hz 
Power density spectrum in (m/s2)2/Hz 
Frequenz in Hz 
Frequency in Hz 
 
0,1
1
10
100
10
100
1000
10000
Leistungsdichtespektrum in (m/s²)²/Hz
Frequenz in Hz


### 第 87 页
 
  
Page 87 
VW 80000: 2017-10 
 
 
 
 
 
 
 
10.4.2.2 
Vibration profile B (components installed on the transmission) 
Table 54: Test parameters – Vibration, sinusoidal for transmission-mounted parts 
Vibration excitation 
Sinusoidal 
Test duration for each spatial axis 22 h 
Vibration profile Figure 29 
Frequency in Hz Amplitude of acceleration in m/s² 
100 
30 
200 
60 
440 
60 
 
 
Figure 29: Vibration profile, sinusoidal for transmission-mounted parts 
Amplitude der Beschleunigung in m/s2 
Amplitude of acceleration in m/s2 
Frequenz in Hz 
Frequency in Hz 
 
Table 55: Test parameters – Vibration, wide-band random vibration for transmission-mounted 
parts  
Vibration excitation 
Wide-band random vibration 
Test duration for each spatial axis 22 h 
RMS value of acceleration 
96.6 m/s² 
Vibration profile Figure 30 
Frequency in Hz Power density spectrum in 
(m/s²)²/Hz 
10 
10 
100 
10 
300 
0.51 
500 
5 
2 000 
5 
 
0
10
20
30
40
50
60
70
100
1000
Amplitude der Beschleunigung in m/s²
Frequenz in Hz


### 第 88 页
Page 88 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Figure 30: Vibration profile, wide-band random vibration for transmission-mounted parts 
Leistungsdichtespektrum in (m/s2)2/Hz 
Power density spectrum in (m/s2)2/Hz 
Frequenz in Hz 
Frequency in Hz 
 
0,1
1
10
10
100
1000
10000
Leistungsdichtespektrum in (m/s²)²/Hz
Frequenz in Hz


### 第 89 页
 
  
Page 89 
VW 80000: 2017-10 
 
 
 
 
 
 
 
10.4.2.3 
Vibration profile C (components installed at the decoupled 
intake manifold) 
Table 56: Test parameters, sinusoidal for components at the decoupled intake manifold 
Vibration excitation 
Sinusoidal 
Test duration for each spatial axis 22 h 
Vibration profile Figure 31 
Frequency in Hz Amplitude of acceleration in m/s² 
100 
90 
200 
180 
325 
180 
500 
80 
1 500 
80 
 
Figure 31: Vibration profile, sinusoidal for components at the decoupled intake manifold 
Amplitude der Beschleunigung in m/s2 
Amplitude of acceleration in m/s2 
Frequenz in Hz 
Frequency in Hz 
 
0
20
40
60
80
100
120
140
160
180
200
100
1000
10000
Amplitude der Beschleunigung in m/s²
Frequenz in Hz


### 第 90 页
Page 90 
VW 80000: 2017-10 
 
 
 
 
 
 
10.4.2.4 
Vibration profile D (components installed on sprung masses 
(body)) 
Table 57: Test parameters, wide-band random vibration for sprung masses 
Vibration excitation 
Wide-band random vibration 
Test duration for each spatial axis 8 h 
RMS value of acceleration 
30.8 m/s² 
Vibration profile Figure 32 
Frequency in Hz Power density spectrum 
in (m/s²)²/Hz 
5 
0.884 
10 
20 
55 
6.5 
180 
0.25 
300 
0.25 
360 
0.14 
1 000 
0.14 
2 000 
0.14 
 
 
Figure 32: Vibration profile, wide-band random vibration for sprung masses 
Leistungsdichtespektrum in (m/s2)2/Hz 
Power density spectrum in (m/s2)2/Hz 
Frequenz in Hz 
Frequency in Hz 
 
 
0,1
1
10
100
1
10
100
1000
10000
Leistungsdichtespektrum in (m/s²)²/Hz
Frequenz in Hz


### 第 91 页
 
  
Page 91 
VW 80000: 2017-10 
 
 
 
 
 
 
 
10.4.2.5 
Vibration profile E (components installed on unsprung masses 
(wheel, suspension)) 
Table 58: Test parameters, wide-band random vibration for unsprung masses 
Vibration excitation 
Wide-band random vibration 
Test duration for each spatial axis 8 h 
RMS value of acceleration 
107.3 m/s² 
Vibration profile Figure 33 
Frequency 
in Hz 
Power density spectrum 
in (m/s²)²/Hz 
20 
200 
40 
200 
300 
0.5 
800 
0.5 
1 000 
3 
2 000 
3 
 
Figure 33: Vibration profile, wide-band random vibration for unsprung masses 
Leistungsdichtespektrum in (m/s2)2/Hz 
Power density spectrum in (m/s2)2/Hz 
Frequenz in Hz 
Frequency in Hz 
 
10.4.3 
Requirement 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-03 Parameter test (large) as per section 4.7.3. 
 
In addition, the DUT must be visually inspected with the naked eye and tested for 
loose or rattling parts by shaking. 
 
0,1
1
10
100
1000
10
100
1000
10000
Leistungsdichtespektrum in (m/s²)²/Hz
Frequenz in Hz


### 第 92 页
Page 92 
VW 80000: 2017-10 
 
 
 
 
 
 
10.5 
M-05 Mechanical shock 
10.5.1 
Purpose 
This test simulates the mechanical load on the component, e.g., when driving over 
curbs or in the case of car accidents. 
It is meant to verify the resistance of the component to flaw patterns, such as cracks 
and subcomponent detachments. 
10.5.2 
Test 
The test is carried out as per DIN EN 60068-2-27 with the following parameters: 
 
Table 59: Test parameters for M-05 Mechanical shock 
Operating mode of the DUT 
"Drivingmin" and "Drivingmax" 
intermittently  
Peak acceleration 
500 m/s2 
Shock duration 
6 ms 
Pulse shape 
Half-sine 
Number of shocks per direction (±X, ±Y, ±Z) 10 
Number of DUTs 
6 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
 
The test must be performed without brackets or add-on parts. The manner of 
fastening connected lines (e.g., electric wiring, coolant hoses, hydraulic lines) in the 
test setup must be defined together with the purchaser. 
 
For components that are installed on the bracket or vehicle by means of damping 
elements, it must be specified with the purchaser whether 
 
- 
all DUTs must be tested with damping elements, 
- 
all DUTs must be tested without damping elements, or 
- 
three DUTs must be tested with damping elements and three DUTs must be 
tested without damping elements. 
 
The time between the shock pulses must be long enough to ensure complete decay 
of the previous vibration. 
 
10.5.3 
Requirement 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-02 Parameter test (small) as per section 4.7.2. 
In addition, the DUT must be visually inspected with the naked eye and tested for 
loose or rattling parts by shaking. 


### 第 93 页
 
  
Page 93 
VW 80000: 2017-10 
 
 
 
 
 
 
 
10.6 
M-06 Continuous mechanical shock 
10.6.1 
Purpose 
This test simulates the acceleration forces of components that are installed in doors, 
hoods, or tailgates/trunk lids and are exposed to high accelerations during opening 
and closing. 
They are meant to verify the resistance of the component to flaw patterns, such as 
subcomponent detachments and material fatigue. 
10.6.2 
Test 
The test is carried out as per DIN EN 60068-2-29 with the following parameters: 
 
Table 60: Test parameters for M-06 Continuous mechanical shock 
Operating mode of the DUT 
Operationmax 
Peak acceleration 
300 m/s² 
Shock duration 
6 ms 
Pulse shape 
Half-sine 
Number of shocks 
 
Installation area 
Number of shocks 
Driver door 
100 000 
Front passenger door 
and rear doors 
50 000 
Trunk lid/tailgate 
30 000 
Hood 
3 000 
If the component is installed in several installation 
areas, the one with the highest number of shocks must 
be selected. 
As-installed position 
The DUT must be mounted on the test device 
according to its installed condition in the vehicle. 
Number of DUTs 
6 
 
10.6.3 
Requirement 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-02 Parameter test (small) as per section 4.7.2. 
In addition, the DUT must be visually inspected with the naked eye and tested for 
loose or rattling parts by shaking. 
 


### 第 94 页
Page 94 
VW 80000: 2017-10 
 
 
 
 
 
 
10.7 
M-07 Coolant circuit pressure pulsation test 
10.7.1 
Purpose 
This test simulates the load on the component due to fluctuations in coolant pressure, 
as well as the states during the post-heating phase and vacuum filling of the cooling 
system. It must be applied exclusively to components that are connected to a coolant 
circuit. 
It is meant to verify the mechanical strength of the components  affected by pressure 
fluctuations in the coolant circuit (e.g., cooling plates of the power module). 
10.7.2 
Test 
Table 61: Test parameters for M-07 Coolant circuit pressure pulsation test 
Operating mode of the DUT 
Assemblyassembly 
Test 
procedure 
Part 1 – Pressure pulsation test: 
Minimum test pressure Pmin: 0.5 (-0.1) barabs 
Maximum test pressure Pmax: 2.0 (+0.1) barabs 
Pressure pulsation frequency: 25-35 1/min 
Number of pressure pulsations: 100 000 
Test temperature: Tcool,max 
Part 2 – Overpressure test: 
Test pressure P: 4.0 (+0.1) barabs 
Test duration: 1 h 
Test temperature: Tcool,max 
Part 3 – Underpressure test (without test medium): 
Test pressure P: 0.01 (-0.01) barabs 
Test duration: 30 min 
Test temperature: TRT 
Test medium 
Must be agreed upon with the purchaser.  
Number of DUTs 
6 
 
10.7.3 
Requirement 
The DUT must be fully functional before and after the test, and all parameters must 
meet the specifications. This is verified by means of a P-03 Parameter test (large) as 
per section 4.7.3. 
 
 


### 第 95 页
 
  
Page 95 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11 Climatic requirements and tests 
11.1 
K-01 High-/low-temperature aging 
11.1.1 
Purpose 
This test simulates the thermal load on the component during storage and transport. 
It is meant to verify the resistance to storage at high or low temperatures, e.g., during 
component transport (by plane, shipping container). 
If the test is carried out at the start of a test sequence, it is also used to adjust all 
components to the same initial conditions. 
11.1.2 
Test 
Table 62: Test parameters for K-01 High-/low-temperature aging 
Operating mode of the DUT 
Assemblynot installed 
Test duration and test 
temperature 
2 cycles of 24 h each (each consisting of 12 h of 
aging at Tmin and 12 h of aging at Tmax) 
Number of DUTs 
As specified in the test sequence plan in the 
Performance Specification. 
11.1.3 
Requirement 
The DUT must be fully functional before and after the test, and all parameters must 
meet the specifications. This is verified by means of a P-03 Parameter test (large) as 
per section 4.7.3. 
 
In addition, the DUT must be visually inspected with the naked eye and tested for 
loose or rattling parts by shaking. 
 
 


### 第 96 页
Page 96 
VW 80000: 2017-10 
 
 
 
 
 
 
11.2 
K-02 Incremental temperature test 
11.2.1 
Purpose 
This test simulates operation of the component at different ambient temperatures. 
It is meant to safeguard the component against any malfunctions which may occur 
within a narrow interval of the ambient temperature range, as well as the startup 
behavior of the components over the entire ambient temperature range. 
11.2.2 
Test 
Table 63: Test parameters for K-02 Incremental temperature test 
Operating mode of 
the DUT 
"Operationmax" during the P-01 Parameter test (function 
check) as per section 4.7.1; otherwise, "Operationmin" 
Test temperature 
The DUTs must be exposed to a temperature profile as per 
Figure 34. 
The temperature change per increment is 5 °C.  
Test sequence 
 
The DUT must be held at every temperature increment until it 
has reached complete temperature stabilization (see 
section 4.6). 
A P-01 Parameter test (function check) as per section 4.7.1 
must then be performed. 
Number of DUTs 
As specified in the test sequence plan in the Performance 
Specification, but at least 6 
 
Figure 34: Temperature profile for incremental temperature test 
Tmax 
Tmax 
TRT 
TRT 
Tmin 
Tmin 
Temperatur 
Temperature 
Zeit 
Time 
 
If a coolant circuit is present, the coolant temperature must track the respective test 
temperature to the limits Tcool,min and Tcool,max. Only the ambient temperature is varied 
outside of the coolant temperature limits. 


### 第 97 页
 
  
Page 97 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.2.3 
Requirement 
All DUT parameters must be within the specifications during each P-01 Parameter 
test (function check) as per section 4.7.1. 


### 第 98 页
Page 98 
VW 80000: 2017-10 
 
 
 
 
 
 
11.3 
K-03 Low-temperature operation 
11.3.1 
Purpose 
This test simulates the load on the component at low temperatures. 
It is meant to verify the function of the component after a long parking time or driving 
time at extremely low temperatures. 
11.3.2 
Test 
The test is performed as per DIN EN 60068-2-1, test Ab, with the following 
parameters: 
 
Table 64: Test parameters for 


### 第 99 页
 
  
Page 99 
VW 80000: 2017-10 
 
 
 
 
 
 
 
K-03 Low-temperature operation 
Operating mode of the DUT 12 h "Off-grid parkingmin" (t.30 of component at Vopmin) 
12 h "Operationmax" at Vopmin 
12 h "Off-grid parkingmin" (t.30 of component at Vop) 
12 h "Operationmax" at Vop 
Test duration 
48 h 
Test temperature 
Tmin 
Number of DUTs  
6 
 
The test as per DIN EN 60068-2-1, test Ab, must also be performed for components 
that give off heat. 
If a coolant circuit is present, the minimum coolant temperature Tcool,min must be set. 
 
For components with high power loss, an increase in the test chamber temperature 
above Tmin as a result of self-heating is permissible during the test in operating mode 
"Operationmax," in agreement between the contractor and the purchaser. 
 
11.3.3 
Requirement 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-02 Parameter test (small) as per section 4.7.2. 
  
 


### 第 100 页
Page 100 
VW 80000: 2017-10 
 
 
 
 
 
 
11.4 
K-04 Repainting temperature 
11.4.1 
Purpose 
This test simulates the load on the component during repainting. 
It is meant to safeguard the component against thermally induced flaw patterns, e.g., 
cracking in solder joints, adhesive joints, bonded joints, and welded joints, and at 
seals and housings. 
11.4.2 
Test 
Table 65: Test parameters for K-04 Repainting temperature 
Operating mode of the DUT 
Off-grid parkingmin 
Test duration and test temperature  
15 min at 130 °C and 1 h at 110 °C 
Number of DUTs 
6 
 
If a coolant circuit is present, the temperature of the idle coolant must be specified as 
TRT. 
11.4.3 
Requirement 
The DUT must be fully functional before and after the test, and all parameters must 
meet the specifications. This is verified by means of a P-02 Parameter test (small) as 
per section 4.7.2. 
  
 


### 第 101 页
 
  
Page 101 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.5 
K-05 Thermal shock (component)  
11.5.1 
Purpose 
This test simulates the thermal load on the component as a result of abrupt changes 
in temperature during vehicle operation. 
It is meant to safeguard the component against thermally induced flaw patterns, e.g., 
cracking in solder joints, adhesive joints, bonded joints, and welded joints, and at 
seals and housings. 
11.5.2 
Test 
The test is carried out as per DIN EN 60068-2-14 with the following parameters: 
 
Table 66: Test parameters for K-05 Thermal shock (component) 
Operating mode of the 
DUT 
Assemblyassembly 
Minimum temperature/ 
temperature of the cold 
test bath 
Tmin 
Maximum temperature/ 
temperature of the hot 
test bath 
Tmax 
Hold time at 
minimum/maximum 
temperature 
15 min after complete temperature stabilization (see 
section 4.6) 
Transfer duration 
(air-to-air, medium-to-
medium) 
≤ 30 s 
Test fluid for test Nc 
Fluid in which the component is operated in the vehicle 
Test 
As per DIN EN 60068-2-14 Na for components that are 
not permanently operated in a fluid 
 
As per DIN EN 60068-2-14 Nc for components that are 
permanently operated in a fluid (IP X8). The DUT must be 
immersed so that all sides of the DUT are covered by at 
least 25 mm of the test fluid. 
Number of cycles 
100 
Number of DUTs 
6 
11.5.3 
Requirement 
The DUT must be fully functional before and after the test, and all parameters must 
meet the specifications. This is verified by means of a P-03 Parameter test (large) as 
per section 4.7.3. 
 
Additionally for the medium-to-medium test: 
The fluid must not penetrate the DUT. The DUT must not be opened until after the 
entire test sequence has been completed as per the test sequence plan (section 9.2). 


### 第 102 页
Page 102 
VW 80000: 2017-10 
 
 
 
 
 
 
11.6 
K-06 Salt spray test with operation, exterior 
11.6.1 
Purpose 
This test simulates the load on the component in a saline atmosphere with saline 
water, as it may occur in certain areas of the world or in winter road conditions. 
It is meant to safeguard the component against any malfunctions when exposed to a 
salt load, e.g., from short circuits and leakage currents due to the ingress of salt into 
the component. 
11.6.2 
Test 
The test is carried out as per DIN EN 60068-2-11 with the following parameters: 
 
Table 67: Test parameters for K-06 Salt spray test with operation, exterior 
Operating mode 
of the DUT 
 
During the spraying phase: 1 h "Off-grid parkingmin" and 1 h 
"Operationmax," intermittently 
 
During the resting phase: "Off-grid parkingmin" 
Test 
temperature 
35 °C 
Test cycle 
Each test cycle consists of an 8-h spraying phase and a 4-h 
resting period as per Figure 35. 
Number of test 
cycles 
12 cycles 
 
For components with increased requirements for leak tightness as 
per section 4.9.1 (e.g., exposure of parked vehicle to water), the 
number of cycles must be adapted on a component-specific basis. 
Number of 
DUTs 
6 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
 
It is not permissible to clean the DUTs after the test. 
 
If a coolant circuit is present, the coolant temperature must be set to the test 
temperature. 
 
 
 


### 第 103 页
 
  
Page 103 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Figure 35: Salt spray test with operation, exterior – spraying phases 
Ein 
On 
Aus 
Off 
Betriebmax 
Operationmax 
Off-Grid Parkenmin 
Off-grid parkingmin 
Sprühphase 
Spraying phase 
Ruhezeit 
Resting period 
t [h] 
t [h] 
Elektrischer Betrieb 
Electrical operation 
1 Zyklus 
1 cycle 
 
11.6.3 
Requirement 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-02 Parameter test (small) as per section 4.7.2. 
 
 


### 第 104 页
Page 104 
VW 80000: 2017-10 
 
 
 
 
 
 
11.7 
K-07 Salt spray test with operation, interior 
11.7.1 
Purpose 
This test simulates the load on the component in a saline atmosphere, as it may 
occur in certain areas of the world. It is meant to safeguard the component against 
any malfunctions when exposed to a salt load, e.g., from short circuits and leakage 
currents due to the ingress of salt into the component. 
11.7.2 
Test 
The test is carried out as per DIN EN 60068-2-11 with the following parameters: 
Table 68: Test parameters for K-07 Salt spray test with operation, interior 
Operating mode of the 
DUT 
 
During the spraying phase: 55 min "Off-grid parkingmin" 
and 5 min "Operationmax," intermittently 
During the resting phase: "Off-grid parkingmin" 
Test temperature 
35 °C 
Test cycle 
Each test cycle consists of an 8-h spraying phase and a 
4-h resting period as per Figure 36. 
Number of test cycles 
2 
Number of DUTs 
6 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. The test setup (as-installed position, covers, trim panels, situation 
during operation) must be suggested by the contractor, approved by the purchaser, 
and documented. 
If a coolant circuit is present, the coolant temperature must be set to the test 
temperature. 
 
 
Figure 36: Salt spray test with operation, interior – spraying phases 
 
11.7.3 
Requirement 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-03 Parameter test (large) as per section 4.7.3. 
 
4 
8 
12 
t [h] 
Resting period 
Spraying phase 
Electrical operation 
1 cycle 
On 
Off 
Operationmax 
Off-grid parkingmin 


### 第 105 页
 
  
Page 105 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.8 
K-08 Damp heat, cyclic 
11.8.1 
Purpose 
This test simulates the thermal load on the component by cyclic temperature changes 
with high humidity during vehicle operation. 
It is meant to verify the resistance of the component to damp heat. 
11.8.2 
Test 
The test is carried out as per DIN EN 60068-2-30 with the following parameters: 
 
Table 69: Test parameters for K-08 Damp heat, cyclic 
Operating mode of the DUT 
"Operationmax" during the P-01 Parameter test 
(function check); otherwise, "Operationmin" 
Total test duration 
144 h 
Test variant 
Variant 1 
Maximum test temperature 
55 °C 
Number of cycles 
6 
Number of DUTs 
6 
 
The P-01 Parameter test (function check) must be performed two times, once each 
after the maximum and minimum temperatures are reached. 
 
If a coolant circuit is present, the coolant temperature must track the respective test 
temperature to the limits Tcool,min and Tcool,max. Only the ambient temperature is varied 
outside of the coolant temperature limits. 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
11.8.3 
Requirement 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-03 Parameter test (large) as per section 4.7.3. 
 
 


### 第 106 页
Page 106 
VW 80000: 2017-10 
 
 
 
 
 
 
11.9 
K-09 Damp heat, cyclic (with frost) 
11.9.1 
Purpose 
This test simulates the thermal loading (including frost) of the component by cyclic 
temperature changes with high humidity during vehicle operation. 
It is meant to verify the resistance of the components to damp heat. 
11.9.2 
Test 
The test is carried out as per DIN EN 60068-2-38 with the following parameters: 
 
Table 70: Test parameters for K-09 Damp heat, cyclic (with frost) 
Operating mode of the 
DUT 
40 min "Operationmin" and 10 min "Operationmax," 
intermittently. 
 
For components with excessive self-heating, the 
contractor and the purchaser must agree on whether the 
duration in operating mode "Operationmax" is to be 
shortened to the duration required to test the overall 
functionality of the component. In this case, the cycle 
duration of 50 min must remain in place. 
Total test duration 
240 h 
Number of cycles 
10 
Test cycle sequence 
The first five cycles must be carried out with a cold phase 
and the remaining cycles must be carried out without a 
cold phase. 
Number of DUTs 
6 
 
If a coolant circuit is present, the coolant temperature must track the respective test 
temperature to the limits Tcool,min and Tcool,max. Only the ambient temperature is varied 
outside of the coolant temperature limits. 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
 
11.9.3 
Requirement 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-03 Parameter test (large) as per section 4.7.3. 
 
 


### 第 107 页
 
  
Page 107 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.10 K-10 Water protection – IPX0 to IPX6K 
11.10.1 Purpose 
This test simulates the load on the component when exposed to water. 
It is meant to verify the function of the component, e.g., when exposed to condensed 
water, rain, or splash water. 
11.10.2 Test 
The test is carried out as per ISO 20653 with the following parameters: 
Table 71: Test parameters for K-10 Water protection – IPX0 to IPX6K 
Operating mode of the DUT 1 min "Operationmin" and 1 min "Operationmax," 
intermittently 
Required degree of 
protection 
As defined in the Performance Specification 
Number of DUTs 
6 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
11.10.3 Requirement 
The required degree of protection defined in the Performance Specification as per 
ISO 20653 must be achieved. 
 
The ingress of water is not permissible. The DUT must not be opened until after 
completion of the entire test sequence. 
 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-02 Parameter test (small) as per section 4.7.2. 


### 第 108 页
Page 108 
VW 80000: 2017-10 
 
 
 
 
 
 
11.11 K-11 High-pressure cleaning/pressure washing 
11.11.1 Purpose 
This test simulates the load on the component when subjected to water during 
vehicle cleaning. 
It is meant to verify the function of the component when exposed to high-pressure 
cleaning/pressure washing. 
11.11.2 Test 
The test is carried out as per ISO 20653 with the following parameters: 
Table 72: Test parameters 
Operating mode of 
the DUT 
Off-grid parkingmin 
Required degree of 
protection 
IP X9K 
Water pressure 
The minimum pressure of the pressure washer is 10 000 kPa 
(100 bar), measured directly at the nozzle. 
Water temperature 
80 °C 
Procedure 
The DUT must be exposed to the water jet from every spatial 
direction freely accessible on the vehicle. 
Number of DUTs 
6 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
 
11.11.3 Requirement 
Degree of protection IP X9K as per ISO 20653 must be achieved. 
 
The ingress of water is not permissible. The DUT must not be opened until after the 
entire test sequence has been completed as per the test sequence plan (section 9.2). 
 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-02 Parameter test (small) as per section 4.7.2. 
 
 


### 第 109 页
 
  
Page 109 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.12 K-12 Thermal shock with splash water 
11.12.1 Purpose 
This test simulates the load on the component when exposed to splash water when 
driving through puddles. 
It is meant to verify the function of the component when subjected to abrupt cooling 
by water. 
11.12.2 Test 
Table 73: Test parameters for K-12 Thermal shock with splash water 
Operating mode of 
the DUT 
"Drivingmin" and "Drivingmax" intermittently  
(see Figure 37) 
Test procedure 
The DUT is heated up to test temperature. 
The DUT is then cyclically exposed to splash water as per 
Figure 37. The DUT must be exposed to splash water over its 
entire breadth. 
Cycle duration 
30 min 
Test temperature 
Tmax 
Test medium for 
splashing 
Tap water with 3 weight percent fine Arizona dust as per 
ISO 12103-1 Permanent mixing must be ensured. 
Splash water 
temperature 
0 to +4 °C 
Splash nozzle 
See Figure 38. 
Splash duration 
3 s 
Water discharge 
3 to 4 liters per splash/nozzle 
Distance between 
nozzle and DUT 
300 to 350 mm 
Number of cycles 
100 
Number of DUTs 
6 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
The test setup (as-installed position, covers, trim panels, situation during operation) 
must be suggested by the contractor, approved by the purchaser, and documented. 
 
If a coolant circuit is present, the coolant temperature must track the respective test 
temperature up to the limit Tcool,max. Only the ambient temperature is varied above the 
coolant temperature limit. 
 
Test setup as per Figure 39 
 


### 第 110 页
Page 110 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Figure 37: Splash water test – splashing times 
 
Dimensions in mm 
 
Figure 38: Splash water test – splash nozzle 
t1 (1 cycle) 
On 
Off 
Operationmax 
Off-grid parkingmin 
Operating mode 
Splashing 
t2 
t3 
t1 = 30 min 
t2 = 14:57 min 
t3 = 3 s 


### 第 111 页
 
  
Page 111 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Schwall
Prüfling
Schwalldüse
300 mm
bis
 350 mm
 
Figure 39: Splash water test setup 
300 mm bis 350 mm 
300 mm to 350 mm 
Prüfling 
DUT 
Schwall 
Splashing 
Schwalldüse 
Splash nozzle 
 
11.12.3 Requirement 
The ingress of water is not permissible. The DUT must not be opened until after 
completion of the entire test sequence. 
 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-02 Parameter test (small) as per section 4.7.2. 
 
 


### 第 112 页
Page 112 
VW 80000: 2017-10 
 
 
 
 
 
 
11.13 K-13 Thermal shock – immersion  
11.13.1 Purpose 
This test simulates the load on the component when immersed in water. 
The test is meant to verify the function of the component when subjected to 
immediate cooling due to immersion of the heated component. 
11.13.2 Test 
The test is carried out as per ISO 20653 with the following parameters: 
 
Table 74: Test parameters for K-13 Thermal shock – immersion 
Operating mode of 
the DUT 
Drivingmax 
Required degree of 
protection 
IP X7 
Test procedure 
The DUT is heated up to Top,max  
It is then hold at Top,max until the DUT reaches temperature 
stabilization (see section  4.6), plus an additional 15 min. 
The DUT is completely immersed in the test medium for ≤ 5 s.  
The DUT must be immersed so that all sides of the DUT are 
covered by at least 25 mm of the test medium. 
Test medium 
Cold water at 0 °C with a salt content of 5% 
Immersion duration 
5 min 
Number of cycles 
20 
Number of DUTs 
6 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
If a coolant circuit is present, the coolant temperature must track the respective test 
temperature up to the limit Tcool,max. Only the ambient temperature is varied above the 
coolant temperature limit. 
11.13.3 Requirement 
The ingress of water is not permissible. The DUT must not be opened until after the 
entire test sequence has been completed as per the test sequence plan (section 9.2). 
 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-02 Parameter test (small) as per section 4.7.2. 
 
11.14 K-14 Damp heat, constant 
11.14.1 K-14 a Damp heat, constant – severity 1 
11.14.1.1 
Purpose 
This test simulates the load on the component due to damp heat. 


### 第 113 页
 
  
Page 113 
VW 80000: 2017-10 
 
 
 
 
 
 
 
It is meant to verify the resistance of the component to flaw patterns caused by damp 
heat, e.g., corrosion, migration/dendrite growth, and swelling and degradation of 
plastics, sealing compounds, and potting compounds. 
11.14.1.2 
Test 
The test is carried out as per DIN EN 60068-2-78 with the following parameters: 
 
Table 75: Test parameters for K-14 Damp heat, constant – severity 1 
Operating mode of the DUT 
Off-grid parkingmin 
 
If "On-grid parking" operating situation is relevant to 
the component, then operating mode "On-grid 
parkingmin" must be tested instead of operating mode 
"Off-grid parkingmin." 
Test temperature 
40 °C 
Humidity 
93% relative humidity 
Test duration 
21 days 
Number of DUTs 
6 
11.14.1.3 
Requirement 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-03 Parameter test (large) as per section 4.7.3. 
 
In addition, a P-01 Parameter test (function check) as per section 4.7.1 must be 
performed every seven days. 
 
 


### 第 114 页
Page 114 
VW 80000: 2017-10 
 
 
 
 
 
 
11.14.2 K-14 b Damp heat, constant – severity 2 
11.14.2.1 
Purpose 
This test simulates the load on the component due to damp heat during the vehicle 
service life in the form of an accelerated test. 
It is meant to verify the quality and reliability of the component in terms of flaw 
patterns caused by damp heat, e.g., corrosion, migration/dendrite growth, and 
swelling and degradation of plastics, sealing compounds, and potting compounds. 
11.14.2.2 
Test 
The test is carried out as per DIN EN 60068-2-78 with the following parameters: 
 
Table 76: Test parameters for K-14 Damp heat, constant – severity 2 
Operating mode of the 
DUT 
Intermittent operation, 47 h "Off-grid parkingmin" and 1 h 
"Operationmax," repeating, until the end of the test 
duration. 
 
If "On-grid parking" operating situation is relevant to the 
component, then operating mode "On-grid parkingmin" 
must be tested instead of operating mode "Off-grid 
parkingmin." 
Test duration 
As defined in the Performance Specification as per 
section F.1 (Lawson model) 
Test temperature 
65 °C 
Test humidity 
93% relative humidity 
Number of DUTs 
6 
 
If a coolant circuit is present, the coolant temperature must track the respective test 
temperature up to the limit Tcool,max. Only the ambient temperature is varied above the 
coolant temperature limit. 
 
Before this service life test is performed, a check must be performed as to whether 
the test parameters of 65 °C and 93% relative humidity exceed the physical limits of 
the materials being used in the components (e.g., hydrolysis of plastics) as a result of 
the highly accelerated nature of the test. If required, the contractor and purchaser 
must agree upon the adaptation of the test temperature and test humidity while 
increasing the test duration according to the Lawson model (e.g., to 55 °C and 93% 
relative humidity) so that the physical limits of the materials being used are not 
exceeded during the test. However, the test severity must remain in place throughout 
the test. The test humidity must not exceed a value of 93% relative humidity. 
 
It must be ensured that no condensation occurs on the DUT during the test (also no 
localized condensation). 
11.14.2.3 
Deviating test for components with reduced performance at 
high temperatures  
For components with reduced performance (e.g., reduction of LCDs' backlighting) in 
the event of high temperatures at or above Top,max (Top,max < 65 °C), the test must not 


### 第 115 页
 
  
Page 115 
VW 80000: 2017-10 
 
 
 
 
 
 
 
be performed at a constant temperature of 65 °C – deviating from Table 76 – but 
rather with the following parameters (see Table 77). 
 
Table 77: Test parameters for K-14 Damp heat, constant, for components with reduced 
performance at high temperatures 
Operating mode of the DUT Intermittent operation as per Figure 40 
Test duration 
As defined in the Performance Specification as per 
section F.1 (Lawson model) 
The respective ramp times between 65 °C and Top, max 
are not included in the test duration. 
Test temperature 
As per Figure 40 
The temperature gradient must be selected such that 
no condensation occurs on the DUT. 
Test humidity 
93% relative humidity 
Interval time t1 
47 h 
Interval time t2 
1 h 
Number of DUTs 
6 
 
If a coolant circuit is present, the coolant temperature must track the respective test 
temperature up to the limit Tcool,max. Only the ambient temperature is varied above the 
coolant temperature limit. 
 
 
Figure 40: Temperature profile for testing of components with reduced performance at high 
temperatures greater than Top,max 
Betriebmax 
Operationmax 
Off-Grid Parkenmin 
Off-grid parkingmin 


### 第 116 页
Page 116 
VW 80000: 2017-10 
 
 
 
 
 
 
11.14.2.4 
Requirement 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-03 Parameter test (large) as per section 4.7.3. 
11.15 K-15 Condensation and climatic test 
11.15.1 K-15 a Condensation test with modules 
11.15.1.1 
Purpose 
This test simulates the condensation on electronic modules in motor vehicles. 
It is meant to evaluate the robustness of electronic modules with respect to 
condensation. 
********* 
Test 
The test is performed with modules without a housing, with the following parameters: 
 
Table 78: Test parameters for K-15 a Condensation test with modules 
Operating mode 
of the DUT 
Off-grid parkingmin 
In addition, P-01 Parameter tests (function checks) must be 
performed as described in the "Test procedure" row.  
Testing 
equipment 
Climatic chamber with condensation option (specifically controlled 
water bath by means of which the required water quantity is 
converted into water vapor). 
The climate control is switched off during the condensation phase. 
The test-chamber temperature is controlled by means of the 
temperature-controlled water bath. 
Test procedure 
1. The climatic chamber remains at the initial temperature for 
60 min to ensure that the DUT is thermally stabilized. Then 
the condensation phase begins. 
2. A P-01 Parameter test (function check) is performed upon 
every increase in water bath temperature by 10 K, in the 
range from 30 min after the start of the condensation phase 
(as per Figure 43) until 30 min before the end of the 
condensation phase, but only at a voltage of Vop. 
The P-01 Parameter test (function check) must be 
performed for max. 2 min with the lowest possible power 
loss. Otherwise, the DUT is heated too much and 
condensation is no longer possible as a result. 
Test 
temperature  
See Figure 43. 
Relative 
humidity of test 
chamber 
See Figure 43. 
The relative humidity of the test chamber must be 100% (0%, -5%) 
during the condensation phase. 


### 第 117 页
 
  
Page 117 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Test duration 
32.5 h (5 cycles of 6.5 h each) 
Test medium 
Distilled water with a maximum conductivity of 5 µS/cm 
DUT position 
As-installed position as in the vehicle  
Plastic brackets must be used to maintain the as-installed position 
of the module in the climatic chamber. 
If the module is used in different installation positions, the DUTs 
must also be positioned in different installation positions in the 
climatic chamber. 
Test setup 
See Figure 41 
During the test, a plastic hood as per Figure 42 must be used to 
eliminate undesired effects in the case of different air speeds. The 
hood must be oriented in such a way that its slope points toward 
the test chamber door. 
The dimensions of the plastic hood must be adapted to the size of 
the test chamber. 
The distance between the plastic hood and the test chamber wall 
must be 10% of the test room width/depth, but at least 8 cm.  
An angle α of  ≥12° must be used as the roof slope of the plastic 
hood as per DIN EN ISO 6270-2. 
Test condition 
For the first time, the condensation test must be performed prior to 
the final specification of the circuit layout (hardware freeze), but 
already with modules manufactured under conditions similar to 
production, in order to optimize any discovered condensation 
sensitivities, e.g., by means of layout and circuit modifications. 
If the manufacturing process of the module is changed (e.g., 
circuit carrier, solder, flux, soldering process, layout, location, or 
subcomponents), the test must be repeated. 
Number of 
cycles 
5 
Number of 
DUTs 
6 modules 
 


### 第 118 页
Page 118 
VW 80000: 2017-10 
 
 
 
 
 
 
Prüfliing
Heizung
Wärmetauscher
(Kühlung)
Temperaturfühler /
- regler
Prüfraum
Prüfschranktür
 
Figure 41: Test setup – K-15 a Condensation test with modules 
Prüfraum 
Test chamber 
Prüfliing 
DUT 
Heizung 
Heating system 
Wärmetauscher (Kühlung) 
Heat exchanger (cooling) 
Temperaturfühler / - regler 
Temperature sensor/controller 
Prüfschranktür 
Test chamber door 
 
α
 
Figure 42: Plastic hood 


### 第 119 页
 
  
Page 119 
VW 80000: 2017-10 
 
 
 
 
 
 
 
10
20
30
40
50
60
70
80
90
100
0
°C
%

1 Zyklus
Betauungsphase  **)
30
30
30
30
30
150
75
  Zeit (t) in min
1)
2)
Parametertest (Funktionstest) bei UB
Wasserbadtemperatur  1 K
Prüfraumtemperatur  3 K
3)
Feuchteverlauf nicht definiert
Prüfraumfeuchte
Wasserbadtemperatur < 20°C
**)     Aufzeichung der Prüfraumfeuchte und -temperatur, Temperaturdifferenz     < 15 °C
1)  Start der Trocknungsphase nach Erreichen
     von 75 °C Lufttemperatur
2)  Prüfling muss trocken sein   Frel  < 50 %
3)  Umschaltung von Klimaregelung auf 
     Wasserbadregelung
Trocknungsphase
15
 
Figure 43: Sequence – K-15 a Condensation test with modules 
Betauungsphase **) 
Condensation phase **) 
Trocknungsphase 
Drying phase 
Zeit (t) in min 
Time (t) in min 
1 Zyklus 
1 cycle 
1) Start der Trocknungsphase nach Erreichen von 75 °C 
Lufttemperatur 
 
2) Prüfling muss trocken sein Frel < 50 % 
 
3) Umschaltung von Klimaregelung auf 
Wasserbadregelung 
1) Start of the drying phase after reaching an air 
temperature of 75 °C 
 
2) The DUT must be dry; Hrel < 50% 
 
3) Climate control is switched to water-bath control 
Parametertest (Funktionstest) bei UB 
Parameter test (function check) at Vop 
Wasserbadtemperatur ± 1 K 
Water bath temperature ±1 K 
Prüfraumtemperatur ± 3 K 
Test room temperature ±3 K 
**) Aufzeichung der Prüfraumfeuchte und -temperatur, 
Temperaturdifferenz < 15 °C 
**) The test-chamber humidity and temperature is 
recorded; temperature difference < 15 °C 
Prüfraumfeuchte 
Test-chamber humidity 
Wasserbadtemperatur < 20°C 
Water bath temperature < 20 °C 
Feuchteverlauf nicht definiert 
Humidity curve not defined 
 


### 第 120 页
Page 120 
VW 80000: 2017-10 
 
 
 
 
 
 
11.15.1.3 
Requirement 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-03 Parameter test (large) as per section 4.7.3. 
In addition, the module must be examined for electrochemical migration (e.g., traces 
of silver or tin migration) and dendrite growth. 
Electrochemical migration and dendrite growth are not permissible. 
Other changes to the module (e.g., corrosion, contamination) must be documented in 
the test report and evaluated together with the purchaser. 
The following documentation must be enclosed with the test report: 
 
 1. Programming of the test chamber 
 2. Parameters (desired/actual) of a cycle 
 3. Parameters (desired/actual) of all five cycles 
See Appendix G for examples. 
 
 


### 第 121 页
 
  
Page 121 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.15.2 K-15 b Climatic test for components with watertight housings 
11.15.2.1 
Purpose 
This test simulates the load on the component due to damp heat during the vehicle 
service life in the form of an accelerated test, taking into account the protective effect 
of watertight housings. 
It is meant to verify the quality and reliability of the component in terms of flaw 
patterns caused by damp heat, e.g., corrosion, migration/dendrite growth, and 
swelling and degradation of plastics, sealing compounds, and potting compounds. 
11.15.2.2 
Test 
The test must be performed with complete components (device, ECU, mechatronic 
system, etc., with their housings). 
 
The test must be performed as a sequence of five test blocks as per Figure 44: 
 
Figure 44: Sequence – K-15 b Climatic test for components with watertight housings 
Prüfblock 1 
 
Feuchte Wärme 
konstant 
Test block 1 
 
Damp heat, 
constant 
Prüfblock 2 
 
Feuchte Wärme 
zyklisch, 
mit Frost 
Test block 2 
 
Damp heat, 
cyclic, 
with frost 
Prüfblock 3 
 
Feuchte Wärme 
konstant 
Test block 3 
 
Damp heat, 
constant 
Prüfblock 4 
 
Feuchte Wärme 
zyklisch, 
mit Frost 
Test block 4 
 
Damp heat, 
cyclic, 
with frost 
Prüfblock 5 
 
Feuchte Wärme 
Konstant 
Test block 5 
 
Damp heat, 
constant 
 
Prüfblock 1
Feuchte Wärme
konstant
Prüfblock 3
Feuchte Wärme
konstant
Prüfblock 4
Feuchte Wärme 
zyklisch,
mit Frost
Prüfblock 2
Feuchte Wärme 
zyklisch,
 mit Frost
Prüfblock 5
Feuchte Wärme
Konstant
t


### 第 122 页
Page 122 
VW 80000: 2017-10 
 
 
 
 
 
 
Test blocks 1, 3, and 5: 
The test is carried out as per DIN EN 60068-2-78 with the following parameters: 
 
Table 79: Test parameters for K-15 b Climatic test for components with watertight housings 
Test blocks 1, 3, and 5 
Operating mode 
of the DUT 
Off-grid parkingmin 
A P-01 Parameter test (function check) must be performed 12 h 
after the start of the test block, and every 24 hours thereafter. 
If "On-grid parking" operating situation is relevant to the 
component, then operating mode "On-grid parkingmin" must be 
tested instead of operating mode "Off-grid parkingmin." 
Test duration 
per test block 
As defined in the Performance Specification 
Note: 
The total test duration of the K-15 b test (test blocks 1 to 5) is 
equal to the test duration of the test K-14 b Damp heat, constant – 
severity 2. 
Of this total test duration, a test duration of 240 h each is allocated 
to test blocks 2 and 4. 
The remaining test duration is allocated equally in thirds to test 
blocks 1, 3, and 5: 
Test durationtest block 1 = test durationtest block 3 = test durationtest 
block 5 = 1/3 (total test duration - 2 * 240 h).  
Test 
temperature 
65 °C 
Test humidity 
93% relative humidity 
Number of 
DUTs 
6 
 
If a coolant circuit is present, the coolant temperature must track the respective test 
temperature up to the limit Tcool,max. Only the ambient temperature is varied above the 
coolant temperature limit. 
 


### 第 123 页
 
  
Page 123 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Test blocks 2 and 4: 
The test is carried out as per DIN EN 60068-2-38 with the following parameters: 
 
Table 80: Test parameters for K-15 b Climatic test for components with watertight housings 
Test blocks 2 and 4 
Operating mode 
of the DUT 
Off-grid parkingmin 
A P-01 Parameter test (function check) must be performed 12 h 
after the start of the test block, and every 24 hours thereafter. 
If "On-grid parking" operating situation is relevant to the 
component, then operating mode "On-grid parkingmin" must be 
tested instead of operating mode "Off-grid parkingmin." 
Test duration 
per test block 
240 h 
Number of 
cycles 
10 
Test cycle 
sequence 
The first five cycles must be carried out with a cold phase and the 
remaining cycles must be carried out without a cold phase. 
Number of 
DUTs 
6 
 
If a coolant circuit is present, the coolant temperature must track the respective test 
temperature to the limits Tcool,min and Tcool,max. Only the ambient temperature is varied 
outside of the coolant temperature limits. 
 
11.15.2.3 
Requirement 
The DUT must be fully functional before, during, and after the test, and all 
parameters must be within the specifications. This is verified by means of continuous 
parameter monitoring and a P-03 Parameter test (large) as per section 4.7.3. 
 
 


### 第 124 页
Page 124 
VW 80000: 2017-10 
 
 
 
 
 
 
11.16 K-16 Thermal shock (without housing) 
11.16.1 Purpose 
This technology test does not simulate any real loading. 
Rather, it is meant to detect weak spots in the area of mechanical joints on modules, 
such as solder joints. 
 
The test must be performed exclusively with the module of the component, without 
the housing and mechanical parts. Necessary supports must be set up in such a way 
that no additional mechanical stresses act on the module. 
11.16.2 Test 
The test is carried out as per DIN EN 60068-2-14 with the following parameters: 
 
Table 81: Test parameters for K-16 Thermal shock (without housing) 
Operating mode of the DUT 
Assemblynot installed 
Minimum temperature 
Tmin 
Maximum temperature 
Tmax 
Hold time at maximum and 
minimum temperature 
15 min after complete temperature 
stabilization (see section  4.6) 
Transfer duration 
≤ 10 s 
Number of cycles 
300 
Number of DUTs 
6 modules 
11.16.3 Requirement 
The DUT must be fully functional before and after the test, and all parameters must 
meet the specifications. This is verified by means of a P-03 Parameter test (large) as 
per section 4.7.3. 
 
 


### 第 125 页
 
  
Page 125 
VW 80000: 2017-10 
 
 
 
 
 
 
 
11.17 K-17 Solar radiation 
11.17.1 Purpose 
This test simulates the influence of solar radiation and ultraviolet (UV) light on the 
component. 
It is meant to verify the resistance of the component to damage caused by material 
fatigue, such as cracks or discolorations. 
11.17.2 Test 
The test is carried out as per DIN 75220 with the following parameters: 
 
Table 82: Test parameters for K-17 Solar radiation 
Operating mode of 
the DUT 
Assemblynot installed 
Test profiles used 
The test profiles in DIN 75220 are applied, depending on the 
installation area of the component. 
Components in the 
exterior 
Use of the Z-OUT profile as per table 2 and table 5 of 
DIN 75220 
Components in the 
interior 
Use of the Z-IN1 profile as per DIN 75220  
Test duration 
25 days (15 days dry, 10 days humid) 
Number of cycles 
1 
Number of DUTs 
6 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
11.17.3 Requirement 
The DUT must be fully functional before and after the test, and all parameters must 
meet the specifications. This is verified by means of a P-03 Parameter test (large) as 
per section 4.7.3. 
 
In addition, the DUT must be visually inspected with the naked eye. 
Changes or damage must be documented in the test report and evaluated together 
with the purchaser. 
 
 


### 第 126 页
Page 126 
VW 80000: 2017-10 
 
 
 
 
 
 
11.18 K-18 Harmful gas test 
11.18.1 Purpose 
This test simulates the influence of harmful gases on the component, primarily on its 
plug contacts and switches. 
It is meant to verify the resistance of the component to flaw patterns, such as 
corrosion and component damage. 
11.18.2 Test 
The test is carried out as per DIN EN 60068-2-60, method 4, with the following 
parameters: 
Table 83: Test parameters for K-18 Harmful gas test 
Operating mode of the DUT 
Assemblyassembly 
Temperature 
TRT 
Humidity 
75% relative humidity 
Harmful gas concentration 
SO2 
0.2 ppm 
H2S 
0.01 ppm 
NO2 
0.2 ppm 
Cl2 
0.01 ppm 
Test duration 
21 days 
Number of DUTs 
6 
11.18.3 Requirement 
The DUT must be fully functional before and after the test, and all parameters must 
meet the specifications. This is verified by means of a P-03 Parameter test (large) as 
per section 4.7.3. 
 
In addition, the contact resistances of switches and contacts must be measured. The 
measured values must be within the specifications. 


### 第 127 页
 
  
Page 127 
VW 80000: 2017-10 
 
 
 
 
 
 
 
12 Chemical requirements and tests 
12.1 
C-01 Chemical tests 
12.1.1 
Purpose 
This test simulates the load on the component by various chemicals. 
It is meant to verify the resistance of the component to chemical changes on the 
housing and functional impairment due to chemical reactions. 
12.1.2 
Test 
Table 84: Test parameters for chemical tests 
Operating mode of the DUT 
As defined in the Performance Specification 
Chemicals 
As defined in the Performance Specification 
Typical chemicals for different installation locations 
are indicated in Table 85. 
Conditioning 
Unless otherwise specified, the DUT and the 
chemicals must be aged in a standard atmosphere. 
 
Test procedure 
The test is performed on the basis of ISO 16750, 
part 5: 
1. The chemical must be applied on the DUT at 
TRT. Unless otherwise defined in the 
Performance Specification, a suitable 
application method as per Table 86 must be 
selected for each chemical. The selected 
application method must be documented in 
the test report. 
It must be ensured that the exterior of the 
DUT is adequately covered with the chemical 
on all materials, material interfaces (e.g., 
seals, material transitions), and labels being 
used. 
2. The DUT must then be aged at the 
temperature indicated in Table 85 for the 
specified exposure time. 
Number of DUTs 
1 DUT per chemical. 
It is possible to use the DUT multiple times for 
several chemicals, in consultation with the 
purchaser. 
 
Safety instructions and warning labels for the chemicals must be adhered to. 
 


### 第 128 页
Page 128 
VW 80000: 2017-10 
 
 
 
 
 
 
******** 
Chemicals 
Table 85: Overview of chemicals (see also ISO 16750-5)  
ID Chemical agents 
DUT temperature 
Exposure time 
Description/reference 
1 
Diesel fuel 
Tmax 22 h 
EN 590 
2 
"Bio" diesel 
Tmax 22 h 
EN 14214 
3 
Petrol/gasoline unleaded 
TRT 
10 min EN 228 
4 
Kerosene 
TRT 
10 min ASTM 1655 
5 
Methanol 
TRT 
10 min CAS 67-56-1 
6 
Engine oil 
Tmax 22 h 
Multigrade oil SAE 0W-40, API SL/CF 
7 
Differential oil 
Tmax 22 h 
Hypoid gear oil SAE 75W-140, API GL-5 
8 
Transmission fluid 
Tmax 22 h 
ATF Dexron III 
9 
Hydraulic fluid 
Tmax 22 h 
DIN 51524-3 (HVLP ISO VG 46) 
10 Greases 
Tmax 22 h 
DIN 51502 (KP2K-30) 
11 Silicone oil 
Tmax 22 h 
CAS 63148-58-3 (AP 100) 
12 Battery fluid 
TRT 
22 h 
37% H2SO4 
13 Brake fluid 
Tmax 22 h 
ISO 4926 
14 Antifreeze fluid 
Tmax 22 h 
Ethylene glycol (C2H6O2) – Water mixture 1:1 
15 Urea NOx (reduction agent) 
Tmax 22 h 
ISO 22241-1 
16 Cavity protection 
TRT 
22 h 
e.g., Teroson1 Underbody Coating Spray 
17 Protective lacquer 
TRT 
22 h 
e.g., W550 (supplied by Pfinder Chemie) 1 
18 Protective lacquer remover 
Tmax 22 h 
e.g., Friapol 750 (supplied by Pfinder Chemie) 1 
19 Windscreen washer fluid 
TRT  2 h 
5% anionic tenside, deionized water 
20 Vehicle washing chemicals 
TRT 
2 h 
CAS 25155-30-0 
CAS 9004-82-4 
21 Interior cleaner 
TRT 
2 h 
e.g., Motip1 Cockpit Spray 
22 Glass cleaner 
TRT 
2 h 
CAS 111-76-2 
23 Wheel cleaner 
TRT 
2 h 
e.g., Sonax1 Xtreme 
24 Cold cleaning agent 
TRT 
22 h 
e.g., P3-Solvclean AK (supplied by Henkel) 1 
25 Acetone 
TRT 
10 min CAS 67-64-1 
26 Cleaning solvent 
TRT 
10 min DIN 51635 
27 Ammonium-containing cleaner 
TRT 
22 h 
e.g., Ajax (supplied by Henkel) 1 
28 Denatured alcohol 
TRT 
10 min CAS 64-17-5 (ethanol) 
29 Contact spray 
Tmax 22 h 
e.g., WD 40 1 
30 Sweat/perspiration 
TRT 
22 h 
DIN 53160 
31 Cosmetic products such as creams 
TRT 
22 h 
e.g., Nivea, Kenzo 1 
32 Refreshment containing caffeine and sugar TRT 
22 h 
Cola 
33 Runway de-icer 
TRT 
2 h 
SAE AMS 1435A 
34 E85 fuel  
TRT 
10 min DIN 51625 
 
Additional agents 
 
 
 
 
1) Example manufacturer; the exact chemicals must be agreed upon with the appropriate department 
 
Table 86: Application methods 
Code 
Application method 
I  
Spraying 
II  
Brushing 
III  
Wiping (e.g., with a cotton cloth) 
IV  
Dousing 
V  
Brief immersion 
VI 
Immersion 


### 第 129 页
 
  
Page 129 
VW 80000: 2017-10 
 
 
 
 
 
 
 
12.1.3 
Requirement 
The DUT must be fully functional before and after the test, and all parameters must 
meet the specifications. This is verified by means of a P-03 Parameter test (large) as 
per section 4.7.3. 
 
Changes to lettering and markings must be documented in the test report and agreed 
upon with the purchaser. 


### 第 130 页
Page 130 
VW 80000: 2017-10 
 
 
 
 
 
 
13 Service life tests 
13.1 
L-01 Service life test – Mechanical/hydraulic durability testing 
13.1.1 
Purpose 
This test simulates the function/actuation cycles of the component during the vehicle 
service life. 
It is meant to verify the quality and reliability of the component with respect to 
function/actuation cycles, e.g., brake actuations, seat adjustment cycles, 
switch/button actuations. 
13.1.2 
Test 
Test details must be defined according to the function/actuation cycle in the 
Performance Specification. 
 
Table 87: Test parameters for L-01 Service life test – Mechanical/hydraulic durability testing 
Operating mode of 
the DUT 
"Operationmax" according to the function/actuation cycle 
Test temperature 
The function/actuation cycles must be performed at the 
temperatures indicated in the temperature load spectrum, 
the duration depending on their percentage share (see 
Appendix C). 
 
At least two temperature ramps as per Appendix C must be 
run through. 
Number of 
function/actuation 
cycles 
As defined in the Performance Specification 
Number of DUTs 
6 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
 
If a coolant circuit is present, the coolant temperature must track the respective test 
temperature to the limits Tcool,min and Tcool,max. Only the ambient temperature is varied 
outside of the coolant temperature limits. 
13.1.3 
Requirement 
The DUT must be fully functional before, during, and after the test. All key 
parameters must be within the specifications. This must be verified by continuous 
parameter monitoring. Intermediate measurements at 25%, 50%, and 75% of the test 
duration and parameter tests as per the test sequence plan must only be carried out 
if the component's functions cannot be adequately monitored during the test. 
 
The intermediate measurements must be carried out as a P-03 Parameter test 
(large). 


### 第 131 页
 
  
Page 131 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
The data from continuous parameter monitoring must be evaluated with respect to 
drifts, trends, and irregular behavior or anomalies. 
 
For components on coolant circuits: 
For components with coated copper parts in the coolant path, these copper parts 
must be examined with a stereo microscope at 20x magnification after the test. Flaws 
or copper corrosion perceptible during this examination are not permissible. 


### 第 132 页
Page 132 
VW 80000: 2017-10 
 
 
 
 
 
 
13.2 
L-02 Service life test – High-temperature durability testing 
13.2.1 
Purpose 
This test simulates the thermal load on the component during electrical operation 
over the vehicle service life in the form of an accelerated test. 
It is meant to verify the quality and reliability of the component with respect to 
thermally induced flaw patterns, such as diffusion, migration, and oxidation. 
13.2.2 
Test 
13.2.2.1 
Test for components without a connection to the coolant circuit, 
without reduced performance at high temperatures 
The test is carried out as per DIN EN 60068-2-2 with the following parameters: 
Table 88: Test parameters for 


### 第 133 页
 
  
Page 133 
VW 80000: 2017-10 
 
 
 
 
 
 
 
L-02 Service life test – High-temperature durability testing – Test for components without a 
connection to the coolant circuit, without reduced performance at high temperatures 
Operating mode of 
the DUT 
Intermittent, 47 h "Operationmax" and 1 h "Off-grid parkingmin" 
In operating mode "Operationmax," the component must be 
operated intermittently in all relevant operating modes with a 
high operating load. The ratio of time shares between these 
operating modes must correspond to the ratio of the 
respective partial test durations.  
Test duration 
For each relevant operating situation as per section 4.3, the 
partial test duration must be calculated as per appendix D.2 
(Arrhenius model). The total test duration is the sum of all 
partial test durations.  
Test temperature 
Tmax 
Number of DUTs 
6  
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
 


### 第 134 页
Page 134 
VW 80000: 2017-10 
 
 
 
 
 
 
13.2.2.2 
Test for components without a connection to the coolant circuit, 
with reduced performance at high temperatures 
For components with reduced performance (e.g., reduction of LCDs' backlighting) in 
the event of high temperatures at or above Top,max, the test as per Table 89 must not 
be performed at a constant temperature of Tmax, but rather with a temperature profile 
with the following parameters: 
 
The test is carried out as per DIN EN 60068-2-2 with the following parameters: 
Table 89: Test parameters for 


### 第 135 页
 
  
Page 135 
VW 80000: 2017-10 
 
 
 
 
 
 
 
L-02 Service life test – High-temperature durability testing –  
Operating mode of 
the DUT 
Intermittent as per Figure 45 
In operating mode "Operationmax" and "Operationmax*," the 
component must be operated intermittently in all relevant 
operating modes with a high operating load. The ratio of time 
shares between these operating modes must correspond to 
the ratio of the respective partial test durations. 
Test duration 
For each relevant operating situation as per section 10.1, the 
respective partial test duration must be calculated as per 
appendix D.4 (Arrhenius model). The total test duration is the 
sum of all partial test durations. 
The respective ramp times between Tmax and Top,max are not 
included in the test duration. 
Test temperature 
As per Figure 45 
Interval time t1 
Must be calculated as per appendix D.4 and defined in the 
Performance Specification 
Interval time t2 
Must be calculated as per appendix D.4 and defined in the 
Performance Specification 
Number of DUTs 
6 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
 
 
 
 
Figure 45: Temperature profile for testing of components with reduced performance at high 
temperatures 
Betriebmax 
Operationmax 


### 第 136 页
Page 136 
VW 80000: 2017-10 
 
 
 
 
 
 
Off-Grid Parkenmin 
Off-grid parkingmin 
*) Für T > Top,max, ist eine Reduktion der Performance 
erlaubt  
Die Anwendung der 1 h Off-Grid Parkenmin erfolgt bei 
Top,max 
*) For T > Top,max, a reduction in performance is permitted. 
1 h of "Off-grid parkingmin" is applied at Top,max 
  
 


### 第 137 页
 
  
Page 137 
VW 80000: 2017-10 
 
 
 
 
 
 
 
13.2.2.3 
Test for components with a connection to a coolant circuit 
 
The test is carried out as per DIN EN 60068-2-2 with the following parameters: 
 
Table 90: Test parameters for 


### 第 138 页
Page 138 
VW 80000: 2017-10 
 
 
 
 
 
 
L-02 Service life test – High-temperature durability testing – Test for components with a 
connection to a coolant circuit 
Operating mode of 
the DUT 
Intermittent, 47 h "Operationmax" and 1 h "Off-grid parkingmin" 
In operating mode "Operationmax," the component must be 
operated intermittently in all relevant operating modes with a 
high operating load. The ratio of time shares between these 
operating modes must correspond to the ratio of the 
respective partial test durations. 
Test duration 
For each relevant operating situation as per section 4.3, the 
partial test duration must be calculated as per appendix D.6 
(Arrhenius model). The total test duration is the sum of all 
partial test durations. 
Test temperature 
(ambient) 
As per appendix D.6 (Arrhenius model to be used for 
components on coolant circuits) 
Test temperature 
(coolant) 
As per appendix D.6 (Arrhenius model to be used for 
components on coolant circuits) 
Number of DUTs 
6  
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
13.2.3 
Requirement 
The DUT must be fully functional before, during, and after the test. All key 
parameters must be within the specifications. This must be verified by continuous 
parameter monitoring. Intermediate measurements at 25%, 50%, and 75% of the test 
duration and parameter tests as per the test sequence plan must only be carried out 
if the component's functions cannot be adequately monitored during the test. 
 
The intermediate measurements must be carried out as a P-03 Parameter test 
(large). 
 
The data from continuous parameter monitoring must be evaluated with respect to 
drifts, trends, and irregular behavior or anomalies. 
 
For components on coolant circuits: 
For components with coated copper parts in the coolant path, these copper parts 
must be examined with a stereo microscope at 20x magnification after the test. Flaws 
or copper corrosion perceptible during this examination are not permissible. 
 
13.3 
L-03 Service life test – Temperature cycle durability testing 
13.3.1 
Purpose 
This test simulates the thermomechanical load on the component due to temperature 
changes during the vehicle service life in the form of an accelerated test. 


### 第 139 页
 
  
Page 139 
VW 80000: 2017-10 
 
 
 
 
 
 
 
It is meant to verify the quality and reliability of the component with respect to 
thermomechanically induced flaw patterns, such as aging and cracking in solder 
joints, adhesive joints, bonded joints, and welded joints, and at seals and housings. 
13.3.2 
Test 
The test is carried out as per DIN EN 60068-2-14 with the following parameters: 
13.3.2.1 
Test for components without a connection to the coolant circuit, 
without reduced performance at low or high temperatures 
 
Table 91: Test parameters for L-03 Service life test – Temperature cycle durability testing – Test 
for components without a connection to the coolant circuit, without reduced performance at 
low or high temperatures 
Operating mode 
of the DUT 
Intermittently, "Operationmax" and "Off-grid parkingmin" as per 
Figure 46. 
Temperature 
profile 
As per Figure 46 
Minimum test 
temperature 
Tmin  
Maximum test 
temperature 
Tmax  
Temperature 
gradient 
4 °C/min 
If the temperature gradient cannot be realized by the test device, 
it can be reduced to values down to a minimum of 2 °C/min in 
agreement with the purchaser. 
Hold times at 
Tmin and Tmax 
15 min after complete temperature stabilization (see section 4.6)  
Number of cycles 
The total number of test cycles must be calculated, taking into 
account all relevant operating situations (section 4.3) as per 
appendix E.1 (Coffin-Manson model). 
Number of DUTs 
6 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
 


### 第 140 页
Page 140 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Figure 46: Temperature profile – L-03 Service life test – Temperature cycle durability testing for 
components without reduced performance at low or high temperatures 
Off-Grid Parkenmin 
Off-grid parkingmin 
Betriebmax 
Operationmax 
Zeit 
Time 
  
 


### 第 141 页
 
  
Page 141 
VW 80000: 2017-10 
 
 
 
 
 
 
 
13.3.2.2 
Test for components without a connection to the coolant circuit, 
with reduced performance at low or high temperatures 
For components with reduced performance (e.g., reduction of LCDs' backlighting) at 
low or high temperatures, less than Top,min or greater than Top,max, the test must be 
performed with the following parameters: 
 
Table 92: Test parameters for L-03 Service life test – Temperature cycle durability testing – Test 
for components without a connection to the coolant circuit, with reduced performance at low 
or high temperatures 
Operating mode of 
the DUT 
Intermittent, "Off-grid parkingmin" and "Operationmax" as per 
Figure 47 
Temperature profile 
As per Figure 47 
Minimum test 
temperature 
Tmin 
Maximum test 
temperature 
Tmax  
Temperature gradient 
4 °C/min 
  
Hold times at Tmin, 
Tmax, Top,min, and 
Top,max  
15 min after complete temperature stabilization 
(see section 4.6) 
Number of cycles 
The total number of test cycles must be calculated, taking 
into account all relevant operating situations (section 4.3) as 
per appendix D.3 (Coffin-Manson model). 
Number of DUTs 
6 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
 
 
 
Figure 47: Temperature profile – Test for components with reduced performance at low or high 
temperatures  


### 第 142 页
Page 142 
VW 80000: 2017-10 
 
 
 
 
 
 
Off-Grid Parkenmin 
Off-grid parkingmin 
Betriebmax 
Operationmax 
Zeit 
Time 
Nur notwendig, wenn Top,min > Tmin 
Only necessary if Top,min > Tmin 
Nur notwendig, wenn Top,max < Tmax 
Only necessary if Top,max < Tmax 
  
 


### 第 143 页
 
  
Page 143 
VW 80000: 2017-10 
 
 
 
 
 
 
 
13.3.2.3 
Test for components on the coolant circuit 
For components on coolant circuits, the test must be performed with the following 
parameters: 
 
Table 93: Test parameters for L-03 Service life test – Temperature cycle durability testing – Test 
for components on the coolant circuit 
Operating mode of 
the DUT 
Intermittent, "Off-grid parkingmin" and "Operationmax" as per 
Figure 46 and Figure 47, respectively 
Temperature profile 
As per Figure 46 and Figure 47, respectively 
Minimum test 
temperature 
Tmin and Tcool,min 
Maximum test 
temperature 
Tmax and Tcool,max 
Temperature 
gradient 
4 °C/min 
  
Hold times at Tmin, 
Tmax, Top,min, and 
Top,max  
15 min after complete temperature stabilization 
(see section  4.6) 
Number of cycles 
The total number of test cycles must be calculated, taking into 
account all relevant operating situations (section 4.3) as per 
appendix D.5 (Coffin-Manson model to be used for 
components on coolant circuits). 
Number of DUTs 
6 
 
When performing the test, the as-installed position of the component in the vehicle 
must be recreated. 
 
13.3.3 
Requirement 
The DUT must be fully functional before, during, and after the test. All key 
parameters must be within the specifications. This must be verified by continuous 
parameter monitoring. Intermediate measurements at 25%, 50%, and 75% of the test 
duration and parameter tests as per the test sequence plan must only be carried out 
if the component's functions cannot be adequately monitored during the test. 
 
The intermediate measurements must be carried out as a P-03 Parameter test 
(large). 
 
The data from continuous parameter monitoring must be evaluated with respect to 
drifts, trends, and irregular behavior or anomalies. 
 
For components on coolant circuits: 
For components with coated copper parts in the coolant path, these copper parts 
must be examined with a stereo microscope at 20x magnification after the test. Flaws 
or copper corrosion perceptible during this examination are not permissible. 
 
 


### 第 144 页
Page 144 
VW 80000: 2017-10 
 
 
 
 
 
 
Appendix A (normative) 
 
Test sequence 
 
A.1 Test sequence plan 
 
A component-specific test sequence plan must be defined in the Performance 
Specification. 
 
The tests that are not required for a component as per the test selection table must 
be crossed out from the test sequence plan. 
 
If a component-specific adaptation of the test sequence is required, the test 
sequence plan may be adapted. 
 
All components are tested with new, original contact systems. 
Original connectors/cables must be connected to the DUT before the start of the 
testing. Only after all tests required for the respective components have been 
performed can the connectors/cables be disconnected for physical analysis of the 
component. This requirement does not apply to tests with operating mode 
"Assemblynot installed." 
The wiring harness used with the contact system must be designed in such a way 
that the wiring harness extends out of the test chamber without any additional 
intermediate connections. 
 
 
 
 


### 第 145 页
 
  
Page 145 
VW 80000: 2017-10 
 
 
 
 
 
 
 
A.2 Sequence tests 
 
If the DUTs are not damaged from the M-01 "Free fall" test, two DUTs must be used 
for further sequence testing. Otherwise, the spare DUTs must be used. 
 
All components must be tested with the original connector or adapter starting from 
the M-01 "Free fall" test. 
 
Dichtheitsprüfung
Tieftemperaturbetrieb
Nachlackiertemperatur
Temperaturschock (Komponente)
Steinschlagprüfung
Salzsprühnebelprüfung mit Betrieb,Aussenraum
Staubprüfung (IP 5KX oder IP 6KX)
Feuchte Wärme, zyklisch (mit Frost)
Mechanischer Dauerschock
Mechanischer Schock
Vibrationsprüfung
Wasserschutz (IP X0 bis IP X6K)
Hochdruck-/Dampfstrahlreinigung IP X9K
Temperaturschock mit Schwallwasser
Temperaturschock Tauchen (IP X7)
+
+
4 (+2 Res.)
2
3
1
6
K-03
K-06
K-05
M-06
M-05
M-04
K-04
M-02
M-03
K-09
K-10
K-11
K-12
K-13
Stufentemperaturtest
K-02
Stufentemperaturtest
K-02
Dichtheitsprüfung
Freier Fall
M-01
Physikalische Analyse
P-04
Legende:
P-02 Parametertest (klein) nach der Prüfung
P-03 Parametertest (groß) nach der Prüfung
Hoch- / Tieftemperaturlagerung
K-01
7
 
Figure 48: Test sequence plan – Sequence 


### 第 146 页
Page 146 
VW 80000: 2017-10 
 
 
 
 
 
 
K-01 
K-01 
Hoch- / Tieftemperaturlagerung 
High/low-temperature aging 
Dichtheitsprüfung 
Leak tightness test 
Stufentemperaturtest 
Incremental temperature test 
4 (+2 Res.) 
4 (+2 spare DUTs) 
M-01 
M-01 
Freier Fall 
Free fall 
Tieftemperaturbetrieb 
Low-temperature operation 
Nachlackiertemperatur 
Repainting temperature 
Temperaturschock (Komponente) 
Thermal shock (component) 
Steinschlagprüfung 
Stone impact test 
Salzsprühnebelprüfung mit Betrieb, Aussenraum 
Salt spray test with operation, exterior 
Staubprüfung (IP 5KX oder IP 6KX) 
Dust test (IP 5KX or IP 6KX) 
Feuchte Wärme, zyklisch (mit Frost) 
Damp heat, cyclic (with frost) 
Mechanischer Dauerschock 
Continuous mechanical shock 
Mechanischer Schock 
Mechanical shock 
Vibrationsprüfung 
Vibration test 
Wasserschutz (IP X0 bis IP X6K) 
Water protection (IP X0 to IP X6K) 
Hochdruck-/Dampfstrahlreinigung IP X9K 
High-pressure cleaning/pressure washing IP X9K 
Temperaturschock mit Schwallwasser 
Thermal shock with splash water 
Temperaturschock Tauchen (IP X7) 
Thermal shock – immersion (IP X7) 
Stufentemperaturtest 
Incremental temperature test 
Dichtheitsprüfung 
Leak tightness test 
Physikalische Analyse 
Physical analysis 
P-04 
P-04 
Legende: 
Legend: 
P-02 Parametertest (klein) nach der Prüfung 
P-02 Parameter test (small) after the test 
P-03 Parametertest (groß) nach der Prüfung 
P-03 Parameter test (large) after the test 
 
 
 


### 第 147 页
 
  
Page 147 
VW 80000: 2017-10 
 
 
 
 
 
 
 
A.3 Tests outside of the sequence (parallel tests) 
 
Dichtheitsprüfung
Salzsprühnebelprüfung mit Betrieb, Innenraum
Feuchte Wärme, zyklisch
Feuchte Wärme konstant - Schärfegrad 1
Betauungsprüfung mit Baugruppen
Temperaturschock (ohne Gehäuse)
Sonnenbestrahlung
Schadgasprüfung
Chemische Anforderungen und Prüfungen
K-07
K-16
K-14 a
C-01
K-08
K-15 a
K-17
K-18
Parametertest (groß)
P-03
Dichtheitsprüfung
Parametertest (groß)
P-03
Physikalische Analyse
P-04
Feuchte Wärme konstant - Schärfegrad 2
K-14 b
Klimaprüfung
K-15 b
Druckwechselprüfung
M-07
 2
 6
 6
 6
 6
 6
 6
 6
 6
 6
 X*
*) 1 Prüfling pro Chemikalie
Mehrfachnutzung eines Prüflings für 
mehrere Chemikalien ist in Absprache 
mit dem Auftraggeber möglich.
Hoch- / Tieftemperaturlagerung
K-01
 
Figure 49: Test sequence plan – Parallel tests 
K-01 
K-01 
Hoch- / Tieftemperaturlagerung 
High/low-temperature aging 
P-03 
P-03 
Parametertest (groß) 
Parameter test (large) 
Dichtheitsprüfung 
Leak tightness test 
M-07 
M-07 
Druckwechselprüfung 
Pressure pulsation test 
Salzsprühnebelprüfung mit Betrieb, Innenraum 
Salt spray test with operation, interior 
Feuchte Wärme, zyklisch 
Damp heat, cyclic 
Feuchte Wärme konstant - Schärfegrad 1 
Damp heat, constant – severity 1 
Feuchte Wärme konstant - Schärfegrad 2 
Damp heat, constant – severity 2 
Betauungsprüfung mit Baugruppen 
Condensation test with modules 
Klimaprüfung 
Climatic test 
Temperaturschock (ohne Gehäuse) 
Thermal shock (without housing) 
Sonnenbestrahlung 
Solar radiation 
Schadgasprüfung 
Harmful gas test 
C-01 
C-01 
Chemische Anforderungen und Prüfungen 
Chemical requirements and tests 


### 第 148 页
Page 148 
VW 80000: 2017-10 
 
 
 
 
 
 
Parametertest (groß) 
Parameter test (large) 
Dichtheitsprüfung 
Leak tightness test 
Physikalische Analyse 
Physical analysis 
*) 1 Prüfling pro Chemikalie Mehrfachnutzung eines 
Prüflings für mehrere Chemikalien ist in Absprache mit 
dem Auftraggeber möglich. 
*) 1 DUT per chemical; it is possible to use the DUT 
multiple times for several chemicals, in consultation with 
the purchaser 
 
 
 


### 第 149 页
 
  
Page 149 
VW 80000: 2017-10 
 
 
 
 
 
 
 
A.4 Service life tests 
 
Dichtheitsprüfung
6
Stufentemperaturtest
K-02
Hoch- / Tieftemperaturlagerung
K-01
6
6
+
Stufentemperaturtest
K-02
Dichtheitsprüfung
Physikalische Analyse
P-04
Mechanisch / Hydraulischer 
Dauerlauf
L-01
25 %
50 %
75 %
P-03 Parametertest (groß)
Hochtemperaturdauerlauf
L-02
25 %
50 %
75 %
P-03 Parametertest (groß)
Temperaturwechseldauerlauf
L-03
25 %
50 %
75 %
P-03 Parametertest (groß)
 
Figure 50: Test sequence plan – Service life  
K-01 
K-01 
Hoch- / Tieftemperaturlagerung 
High/low-temperature aging 
Dichtheitsprüfung 
Leak tightness test 
Stufentemperaturtest 
Incremental temperature test 
P-03 Parametertest (groß) 
P-03 Parameter test (large) 
L-01 
L-01 
Mechanisch / Hydraulischer Dauerlauf 
Mechanical/hydraulic durability testing 
Hochtemperaturdauerlauf 
High-temperature durability testing 
Temperaturwechseldauerlauf 
Temperature cycle durability testing 
Stufentemperaturtest 
Incremental temperature test 
Dichtheitsprüfung 
Leak tightness test 
Physikalische Analyse 
Physical analysis 
 
 
 


### 第 150 页
Page 150 
VW 80000: 2017-10 
 
 
 
 
 
 
Appendix B (normative) 
 
Typical temperature load spectra for different installation areas 
 
Table 94: Overview of installation areas, typical spectra, and temperature rises 
Installation area of the component 
Spectrum no. 
Temperature rise in K 
Vehicle interior, without special requirement 
1 
36 
Body-mounted, without special requirements 
1 
36 
Vehicle interior, exposed to solar radiation 
2 
46 
Body-mounted, roof 
2 
46 
Engine compartment, but not on the engine 
3 
60 
On the radiator 
3 
60 
Engine-mounted 
4 
75 
Transmission-mounted 
4 
75 
 
 
 


### 第 151 页
 
  
Page 151 
VW 80000: 2017-10 
 
 
 
 
 
 
 
B.1 Temperature load spectrum 1 
Table 95: Temperature load spectrum 1  
Temperature in °C 
Distribution in % 
-40 
6 
23  
20 
40  
65  
75  
8  
80  
1 
 
B.2 Temperature load spectrum 2 
Table 96: Temperature load spectrum 2  
Temperature in °C 
Distribution in % 
-40  
6  
23 
20 
50 
65 
100 
8 
105 
1 
 
B.3 Temperature load spectrum 3 
Table 97: Temperature load spectrum 3 
Temperature in °C 
Distribution in % 
-40  
6  
23  
20  
65  
65  
115  
8  
120  
1 
 
B.4 Temperature load spectrum 4 
Table 98: Temperature load spectrum 4 
Temperature in °C  Distribution in % 
-40  
6  
23  
20  
85  
65  
135  
8  
140 
1  
 
 
 


### 第 152 页
Page 152 
VW 80000: 2017-10 
 
 
 
 
 
 
Appendix C(normative)  
 
Calculation for the performance of the "Mechanical/hydraulic 
durability testing" service life test 
C.1 Calculation 
 
The temperature load spectrum being applied is used to calculate the number of 
mechanical/hydraulic function/actuation cycles to be completed during testing. 
 
Table 99: Temperature load spectrum 
Temperature in °C 
Distribution in % 
Tfield, 1 
p1 
Tfield, 2 
p2 
Tfield, 3 
p3 
… 
… 
Tfield, n 
pn 
 
The mechanical/hydraulic function/actuation cycles to be tested are distributed to 
each temperature, Tfield, 1 … Tfield, n, from the temperature load spectrum being 
applied, using the following equation. 
 
 
i
Total
T
p
n
n
field,i


 (1) 
 
The mechanical/hydraulic function/actuation cycles must be tested as per 
appendix C.2, Figure 51: Temperature curve for mechanical/hydraulic durability 
testing. At least 2 temperature ramps must be carried out. For this, equation (1) must 
be expanded as follows: 
 
 
 e ramps
temperatur
p
n
n
i
Total
T
i
field,


 (2) 
 
where: 
nTfield,i 
 
 
Number of mechanical/hydraulic function/actuation 
 
 
 
 
cycles for temperature increment Tfield,i 
ntotal 
 
 
Number of mechanical/hydraulic function/actuation cycles 
 
 
 
to be tested 
pi 
 
 
Percentage share of the mechanical/hydraulic function 
 
 
 
/actuation cycles with which the component is operated 
 
 
 
in the field at temperature Tfield,i 
 
temperature ramps Number of temperature ramps At least two temperature  
 
 
 
ramps must be carried out. 
 
 


### 第 153 页
 
  
Page 153 
VW 80000: 2017-10 
 
 
 
 
 
 
 
The total test duration is yielded from the following equations: 
 
 
Cycle
T
test_T
*t
n
t
field,i
i
field, 
 (3) 
 
 


temperatur  e re-stabilization time *Temperatur  e ramps
... t
t
t
field,n
field,1
test_T
test_T
test_total




(4) 
 
where: 
tcycle 
 
 
Time for a mechanical/hydraulic function/actuation cycle 
 
 
 
to be tested 
ttest_Tfield,i  
 
Test duration for temperature value Tfield,i 
 
temperature re-stabilization time Duration for temperature re-stabilization between 
the  
 
 
 
 
temperature values for a temperature ramp at a
 
 
 
 
 
temperature gradient of 
min
2 C
 
 
If a coolant circuit is present, the coolant temperature must track the respective test 
temperature to the limits Tcool,min and Tcool,max. Only the ambient temperature is varied 
outside of the coolant temperature limits. 
 
 


### 第 154 页
Page 154 
VW 80000: 2017-10 
 
 
 
 
 
 
C.2 Example calculation 
 
For an ECU with the temperature load spectrum indicated in the following table: 
 Table 100: Example temperature load spectrum 
 
Temperature in °C 
Distribution in % 
-40 
6 
23 
20 
60 
65 
100 
8 
105 
1 
 
and 
 
Number of function/actuation cycles 
 
100 000 
Cycle duration tcycle  
 
 
 
8 s/cycle 
Temperature re-stabilization time  
 
240 min, 
the test duration for the "Mechanical/hydraulic durability testing" service life test is 
calculated as follows: 
 
The percentage shares of the function/actuation cycles of the component for all 
temperatures (see table XX) of the temperature load spectrum indicated above are 
calculated using equation (1). 
 
 
nTfield,1 = 
ntotal * p1 
= 
100 000 cycles * 0.06 = 
6 000 cycles 
 
nTfield,2 = 
ntotal * p2 
= 
100 000 cycles * 0.20 = 
20 000 cycles 
 
nTfield,3 = 
ntotal * p3 
= 
100 000 cycles * 0.65 = 
65 000 cycles 
 
nTfield,4 = 
ntotal * p4 
= 
100 000 cycles * 0.08 = 
8 000 cycles 
 
nTfield,5 = 
ntotal * p5 
= 
100 000 cycles * 0.01 = 
1 000 cycles 
 
The partial test durations for the respective temperatures are calculated using 
equation (2): 
 
ttest_Tfield,1 = 
nTfield1 * tcycle 
= 
 
6 000 cycles * 8  = 
48 000 s 
 
ttest_Tfield,2 = 
nTfield2 * tcycle 
= 
 
20 000 cycles * 8  = 
160 000 s 
 
ttest_Tfield,3 = 
nTfield3 * tcycle 
= 
 
65 000 cycles * 8  = 
520 000 s 
 
ttest_Tfield,4 = 
nTfield4 * tcycle 
= 
 
8 000 cycles * 8  = 
64 000 s 
 
ttest_Tfield,5 = 
nTfield5 * tcycle 
= 
 
1 000 cycles * 8  = 
8 000 s 
 
The total test duration for the "Mechanical/hydraulic durability testing" service life test 
is calculated using equation (3): 
 
ttest_total = 48 000 s + 160 000 s + 520 000 s + 64 000 s + 8 000 s + 240 min * 60 s 
ttest_total = 814 400 s = 13 573 min = 226 h 
The total test duration for the component is ttest_total = 226 h. 
 


### 第 155 页
 
  
Page 155 
VW 80000: 2017-10 
 
 
 
 
 
 
 
-40
23
60
100
105
T
[°C]
t
a
b
c
d
e
1 Temperaturrampe
 
Figure 51: Temperature curve for mechanical/hydraulic durability testing  
 
T 
[°C] 
T 
[°C] 
1 Temperaturrampe 
1 temperature ramp 
t 
t 
 
 


### 第 156 页
Page 156 
VW 80000: 2017-10 
 
 
 
 
 
 
Appendix D (normative) 
 
Calculation models for the "High-temperature durability testing" 
service life test 
D.1 Adaptation of the test temperatures to reduce the test duration 
 
To reduce the test duration for components that are actively operated in several 
operating situations, the test temperature Tmax (or TCC, max, where "CC" stands for 
coolant circuit) can be increased to test individual operating situations relevant to the 
component. For this purpose, the absolute maximum temperature Tmax (or TCC, max) 
can be applied as the test temperature for the operating situations relevant to the 
component. The component must remain fully functional here. In addition, it must be 
ensured that none of the parts, subcomponents, or materials belonging to the 
component are operated outside of their specified limits (temperature limits) in each 
case, taking into account any component self-heating that is generated according to 
the operating mode. 
 
The test temperature can be adapted for individual or multiple operating situations 
relevant to the component. The test durations in each case must be calculated as per 
appendix C. 
 
Details concerning an increase in the test temperature and the resulting test 
durations must be agreed upon between the purchaser and the contractor, and 
documented. 
 
D.2 Arrhenius model 
For the calculation of the test duration for the "High-temperature durability testing" 
service life test, the percentage temperature load spectrum according the use profile 
in the Performance Specification, 
Table 101: Temperature load spectrum 
Temperature in °C 
Distribution in % 
Tfield, 1 
p1 
Tfield, 2 
p2 
… 
… 
Tfield, n 
pn 
 
and the operating duration toperation of the vehicle in the field, must be used. 
 
For each temperature from Tfield, 1 … Tfield, n, an acceleration factor AT,1 … AT,n must 
be calculated using the following equation: 
 































273.15
T
1
273.15
T
1
k
E
e
A
field,i
test
A
T,i
 
(1) 
 
where: 
AT,i 
Acceleration factor of the Arrhenius model 


### 第 157 页
 
  
Page 157 
VW 80000: 2017-10 
 
 
 
 
 
 
 
EA 
Activation energy EA = 0.45 eV 
k 
Boltzmann constant (k = 8.617 * 10-5 eV/K) 
Ttest 
Test temperature in °C, usually Tmax 
Tfield,i 
Field temperature in °C, based on the temperature load spectrum 
according to the use profile 
-273.15 °C 
Absolute zero of temperature 
 
The total test duration for high-temperature durability testing is yielded from the 
acceleration factors as per: 


i
T,i
i
operation
test
A
p
t
t
   
(2) 
 
where: 
ttest 
Test duration (hours) for the "High-temperature durability testing" 
service life test 
toperation 
Operating duration (hours) in the field 
pi 
Percentage share in operating duration during which the component is 
operated at temperature Tfield,i in the field 
AT,i 
Acceleration factor for temperature Tfield,i 
 
 
 
 
 


### 第 158 页
Page 158 
VW 80000: 2017-10 
 
 
 
 
 
 
D.3 Example Arrhenius model: 
 
For an ECU with the temperature load spectrum indicated in the following table: 
Table 102: Example spectrum 
Temperature in °C 
Distribution in % 
-40 
6 
23 
20 
60 
65 
100 
8 
105 
1 
 
and an operating duration of 8 000 h, the test duration for the "High-temperature 
durability testing" service life test is calculated as follows: 
 
The acceleration factors AT,i for all five temperatures (see Table 102) of the 
temperature load spectrum indicated above are calculated using equation (1), where 
Ttest = Tmax = 105 °C. 
 
AT,1 = 5 369 
AT,2 = 45.8 
AT,3 = 6.46 
AT,4 = 1.20 
AT,5 = 1.00 
 
The operating duration of the component is toperation = 8 000 h. 
 
The total test duration for the "High-temperature durability testing" service life test is 
yielded from equation (2) as: 
 
 
 1 452 hours
1.00
0.01
1.20
0.08
6.46
0.65
45.8
0.20
5369
0.06
 8000 hours 
ttest











 
 
 
 


### 第 159 页
 
  
Page 159 
VW 80000: 2017-10 
 
 
 
 
 
 
 
D.4 Arrhenius model to be used for components with reduced performance at 
high temperatures 
 
For the calculation of the test duration of the "High-temperature durability testing" 
service life test for components with reduced performance at high temperatures at or 
above Top,max, the temperature load spectrum according to the use profile in the 
Performance Specification is divided into the two temperature ranges T ≤ Top,max and 
T > Top,max: 
 
Table 103: Temperature load spectrum for T ≤ Top,max with test temperature Top,max 
Temperature in °C 
Distribution in % 
Tfield, 1 
p1 
Tfield, 2 
p2 
… 
… 
Tfield,m (≤ Top,max) 
pm 
 
Table 104: Temperature load spectrum for Top,max < T ≤ Tmax with test temperature Tmax 
Temperature in °C 
Distribution in % 
Tfield,m+1 (> Top,max) 
pm+1 
Tfield,m+2 
pm+2 
… 
… 
Tfield, n 
pn 
 
For each temperature Tfield,1 … Tfield,m … Tfield, n, an acceleration factor AT,1 … AT,m … 
AT,n is calculated using equation (1), where for temperature range T ≤ Top,max a test 
temperature of Ttest = Top,max is assumed, and for temperature range T > Top,max a test 
temperature of Ttest = Tmax. 
 
The required test duration top,max at test temperature Top,max is yielded according to 
equation (2), where i = 1 … m. 
 
The required test duration tmax at test temperature Tmax is yielded according to 
equation (2), where i = m + 1 … n. 
 
The total test duration ttotal is the sum of top,max and tmax. 
 
For testing that is close to real-life conditions, the test is carried out intermittently at 
test temperatures Top,max and Tmax (see Figure 45). 
The interval duration, which is typically 48 h, is divided based on the ratio of partial 
test durations top,max to tmax. 
 
 
m < n 
m < n 


### 第 160 页
Page 160 
VW 80000: 2017-10 
 
 
 
 
 
 
D.5 Example Arrhenius model to be used for components with reduced 
performance at high temperatures: 
 
The temperature load spectrum as per Table 105 and Table 106 applies to the ECU. 
For an operating duration of 8 000 h, the test duration for the "High-temperature 
durability testing" service life test for components with reduced performance at or 
above Top,max = 90 °C is calculated as follows: 
The percentage temperature distribution according to the use profile is divided into 
the two ranges T ≤ Top,max and T > Top,max: 
 
Table 105: Example spectrum for T ≤ 90 °C 
Temperature in °C 
Distribution in % 
-40 
6 
23 
20 
60 
65 
 
Table 106: Example spectrum for T > 90 °C 
Temperature in °C 
Distribution in % 
100 
8 
105 
1 
 
Using equation (1) and Ttest = 90 °C, the acceleration factors AT,i are calculated for all 
temperatures T ≤ 90 °C (see Table 105) of the first part of the temperature load 
spectrum: 
 
AT,1 = 3 035.79 
AT,2 = 25.88 
AT,3 = 3.65 
 
This yields a test duration top,max at a test temperature of Top,max = 90 °C of 
 
 
 1 487 hours
3.65
0.65
25.88
0.2
3035.79
0.06
 8000 hours 
90 C)
(T
t
test
op,max











 
 
Using equation (1) and Ttest = 105 °C, the acceleration factors AT,i are calculated for 
all temperatures T > 90 °C (see Table 106) of the second part of the temperature 
load spectrum: 
AT,4 = 1.20 
AT,5 = 1.00 
 
This yields a test duration tmax at a test temperature of Tmax = 105 °C of 
 
 
 612 hours
1.00
0.01
1.20
0.08
 8000 hours 
105 C)
(T
t
test
max










 
 
The total test duration for the "High-temperature durability testing" service life test is 
yielded as the sum of the two test durations
 


### 第 161 页
 
  
Page 161 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
ttotal = top,max + tmax = 1 487 hours + 612 hours = 2 099 hours 
 
 
 
The test is performed as per Figure 45 intermittently at test temperatures Top,max and 
Tmax with the interval times 
 
 
t1 = 48 h * top,max / ttotal = 48 h * 1 487 / 2 099 = 34 h 
 
t2 = 48 h * tmax / ttotal = 48 h * 612 / 2 099 = 14 h. 
 
 
 


### 第 162 页
Page 162 
VW 80000: 2017-10 
 
 
 
 
 
 
D.6 Arrhenius model to be used for components on coolant circuits 
 
For components with a connection to the coolant circuit, all relevant operating 
situations i (see section 4.3; "i" corresponds to the consecutive number of the 
situations) with their associated temperature distributions for ambient temperature 
and coolant circuit temperature must be taken into account. 
For the "High-temperature durability testing" service life test, the test durations and 
test temperatures for the ambient temperature and the coolant circuit temperature 
must be calculated as described below for each relevant operating situation i; the 
total test duration is yielded from the sum of the test durations for each relevant 
operating situation i. 
 
For each relevant operating situation i, the test duration for the ambient temperature 
and the coolant circuit must first be calculated separately according to the Arrhenius 
model as per appendix D.1 or D.4, in order to calculate the test duration for operating 
situation i. 
Because the resulting test durations ttest, ambient and ttest, CC usually differ, but the 
component can only be tested with a uniform test duration for the respective 
operating situation i, the test durations must be aligned between the ambient 
temperature and the coolant circuit temperature. 
 
The shorter of the two test durations ttest, ambient and ttest, CC must be adapted to the 
longer test duration using the following iteration method, by dividing the test into at 
least two partial tests and reducing the test temperatures in all but one partial test. 
 
 
Case A: ttest, ambient < ttest, CC 
 
Test duration: 
For ttest, ambient < ttest, CC, the test duration for operating situation i is 
ttest, mode i = ttest, CC 
 
Test temperature for coolant: 
The test temperature must be selected according to the Arrhenius model as per 
appendix D.1 (usually Tcool,max). 
Test temperatures for ambient temperature: 
The test temperatures must be calculated iteratively according to the following 
algorithm on the basis of the temperature load spectrum of the ambient temperature 
of operating situation i being considered (Table 107). 
 
 
Table 107: Temperature load spectrum for ambient temperature 
Temperature in °C 
Distribution in % 
Tfield, 1 
p1 
Tfield, 2 
p2 
… 
… 
Tfield, n 
pn 


### 第 163 页
 
  
Page 163 
VW 80000: 2017-10 
 
 
 
 
 
 
 
1. Start of iteration (m = 0): 
The first partial test must be performed at the test temperature Tfield, n for the 
partial test duration ttest, T_field, n = toperation * pn (where toperation corresponds to the 
operating duration in the field of operating situation i being considered, in 
hours). 
 
2. First iteration (m = 1): 
Part of the test duration for operating situation i, ttest, situation i is covered by the 
1st partial test, so a remaining test duration yet to be covered by further partial 
tests is yielded from 
tremaining, 1 = ttest, mode i - ttest, T_field, n. 
 
In addition, the portion pn of the temperature distribution of the ambient 
temperature is covered by the first partial test. Therefore, this portion pn must 
be set to pn = 0 for the further calculation. 
To specify the test temperature for the second partial test (m = 1), the test 
temperature Tadapted must first be determined using the Arrhenius model as per 
D.1 or D.4 in such a way that a test duration equivalent to the remaining test 
duration tremaining, 1 is yielded for the distribution (adapted with pn = 0) of the 
ambient temperature. 
If the adapted test temperature Tadapted determined in such a way is less than 
Tfield, n-1, the second partial test must be performed at the test temperature 
Tfield, n-1 for the test duration 
ttest, T_field, n-1 = toperation * pn-1 
and at least one additional iteration step must be carried out. 
If, however, the determined adapted test temperature Tadapted is greater than 
Tfield, n-1, the second partial test must be performed at the test temperature 
Tadapted for the test duration 
ttest, T_field, n-1 = tremaining, 1 
and an additional iteration step does not have to be carried out (end of 
iteration). 
 
3. Additional iterations (m = 2, 3, …) 
A part of the test duration for operating situation i ttest, mode i is covered by the 
first m partial tests, so that a remaining test duration yet to be covered by the 
additional partial tests is yielded from 






1
m
0
k
test,T
test,modei 
 m
remaining,
field, n k
t
t
t
 
 
In addition, the portions pn-k with k = 0, 1, …, (m - 1) of the temperature 
distribution of the ambient temperature are covered by the first m partial tests. 
Therefore, these portions pn-k must be set to pn-k = 0 for the further calculation. 
To specify the test temperature for the (m + 1)th partial test, the test 
temperature Tadapted must first be determined using the Arrhenius model as per 
D.1 or D.4 in such a way that a test duration equivalent to the remaining test 
duration tremaining, m is yielded for the distribution (adapted with pn-k = 0) of the 
ambient temperature. 


### 第 164 页
Page 164 
VW 80000: 2017-10 
 
 
 
 
 
 
If the adapted test temperature Tadapted determined in such a way is less than 
Tfield, n-m, the (m + 1)th partial test must be performed at the test temperature 
Tfield, n-m for the test duration 
ttest, T_field, n-m = toperation * pn-m 
and at least one additional iteration step must be carried out. 
If, however, the determined adapted test temperature Tadapted is greater than 
Tfield, n-m, the (m + 1)th partial test must be performed at the test temperature 
Tadapted for the test duration 
ttest, T_field, n-m = tremaining, m 
and an additional iteration step does not have to be carried out (end of 
iteration). 
 
 
Case B: ttest, ambient > ttest, CC 
 
Test duration: 
For ttest, ambient > ttest, CC, the test duration for operating situation i is 
ttest, mode i = ttest, ambient 
 
Test temperature (ambient): 
The test temperature must be selected according to the Arrhenius model as per 
appendix D.1 or D.4 (generally Tmax or Tmax and Top, max). 
 
Test temperatures (coolant): 
The test temperatures must be calculated iteratively using the following algorithm, on 
the basis of the temperature load spectrum for the coolant temperature of operating 
situation i being considered (Table 108). 
 
Table 108: Temperature load spectrum for coolant temperature 
Temperature in °C 
Distribution in %  
Tfield, 1 
p1 
Tfield, 2 
p2 
… 
… 
Tfield, n 
pn 
 
1. Start of iteration (m = 0): 
The first partial test must be performed at the test temperature Tfield, n for the 
partial test duration ttest, T_field, n = toperation * pn (where toperation corresponds to the 
operating duration in the field of operating situation i being considered, in 
hours). 
 
2. First iteration (m = 1): 
Part of the test duration for operating situation i ttest, mode i is covered by the first 
partial test, so that a remaining test duration yet to be covered by the 
additional partial tests is yielded from 
tremaining, 1 = ttest, mode i - ttest, T_field, n. 
 


### 第 165 页
 
  
Page 165 
VW 80000: 2017-10 
 
 
 
 
 
 
 
In addition, the portion pn of the temperature distribution of the coolant 
temperature is covered by the first partial test. Therefore, this portion pn must 
be set to pn = 0 for the further calculation. 
To specify the test temperature for the second partial test (m = 1), the test 
temperature Tadapted must first be determined using the Arrhenius model as per 
D.1 in such a way that a test duration equivalent to the remaining test duration 
tremaining, 1 is yielded for the distribution (adapted with pn = 0) of the coolant 
temperature. 
If the adapted test temperature Tadapted determined in such a way is less than 
Tfield, n-1, the second partial test must be performed at the test temperature 
Tfield, n-1 for the test duration 
ttest, T_field, n-1 = toperation * pn-1 
and at least one additional iteration step must be carried out. 
If, however, the determined adapted test temperature Tadapted is greater than 
Tfield, n-1, the second partial test must be performed at the test temperature 
Tadapted for the test duration 
ttest, T_field, n-1 = tremaining, 1 
and an additional iteration step does not have to be carried out (end of 
iteration). 
 
 
3. Additional iterations (m = 2, 3, …) 
A part of the test duration for operating situation i ttest, mode i is covered by the 
first m partial tests, so that a remaining test duration yet to be covered by the 
additional partial tests is yielded from 
 






1
m
0
k
test,T
test,modei 
 m
remaining,
field, n k
t
t
t
 
 
In addition, the portions pn-k with k = 0, 1, …, (m - 1) of the temperature 
distribution of the coolant temperature are covered by the first m partial tests. 
Therefore, these portions pn-k must be set to pn-k = 0 for the further calculation. 
To specify the test temperature for the (m + 1)th partial test, the test 
temperature Tadapted must first be determined using the Arrhenius model as per 
D.1 in such a way that a test duration equal to the remaining test duration 
tremaining, m is yielded for the distribution (adapted with pn-k = 0) of the coolant 
temperature. 
If the adapted test temperature Tadapted determined in such a way is less than 
Tfield, n-m, the (m + 1)th partial test must be performed at the test temperature 
Tfield, n-m for the test duration 
ttest, T_field, n-m = toperation * pn-m 
and at least one additional iteration step must be carried out. 
If, however, the determined adapted test temperature Tadapted is greater than 
Tfield, n-m, the (m + 1)th partial test must be performed at the test temperature 
Tadapted for the test duration 
ttest, T_field, n-m = tremaining, m 
and an additional iteration step does not have to be carried out (end of 
iteration). 


### 第 166 页
Page 166 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 


### 第 167 页
 
  
Page 167 
VW 80000: 2017-10 
 
 
 
 
 
 
 
D.7 Example Arrhenius model to be used for components on coolant circuits 
 
For an ECU connected to the coolant circuit, with the temperature load spectrum 
specified in the following tables for the ambient temperature and the coolant 
temperature: 
Table 109: Example spectrum for ambient temperature 
Temperature in °C 
Distribution in % 
-40 
6 
23 
20 
50 
65 
100 
8 
105 
1 
 
Table 110: Example spectrum for coolant temperature 
Temperature in °C 
Distribution in % 
-40 
6 
23 
20 
50 
65 
75 
8 
80 
1 
 
and an operating duration of 8 000 h, the test duration for the "High-temperature 
durability testing" service life test is calculated as follows: 
 
Test duration: 
Calculation of the test durations for ambient temperature and coolant temperature 
using the Arrhenius model: 
ttest, ambient = 1 143 h 
ttest, CC = 2 009 h 
Because ttest, ambient is less than ttest, CC, the calculation is performed as per case A 
described in appendix D.6. The test duration for the ambient temperature must be 
adapted to ttest, mode i = ttest, CC = 2 009 h. 
 
Test temperature for coolant: 
The test temperature for the coolant is TCC, max = Tfield, 5 = 80 °C as per the 
temperature load spectrum. 
 
Iterative calculation of test temperatures for ambient temperature: 
1. Iteration start: 
The first partial test occurs at Tfield, 5 = 105 °C. The test duration is ttest, 
T_field, 5 = toperation * p5 = 8 000 h * 1% = 80 h. 
 
2. First iteration: 
A part of the test duration for operating situation i ttest, mode i was already 
covered by the first partial test. Therefore, the remaining test duration 
must be recalculated: tremaining, 1 = ttest, mode i - ttest, T_field, 5 = 2 009 h -
 80 h = 1 929 h. 


### 第 168 页
Page 168 
VW 80000: 2017-10 
 
 
 
 
 
 
Because the portion p5 of the temperature distribution is covered by the 
first partial test, p5 is set to p5 = 0 in the temperature distribution for the 
further calculation by the Arrhenius model, as per the following table. 
 
Table 111: Adapted temperature load spectrum for ambient temperature after first partial test 
Temperature in °C 
Distribution in % 
-40 
6 
23 
20 
50 
65 
100 
8 
 
In order to subsequently be able to determine the test temperature for 
the second partial test, the test temperature Tadapted must be calculated 
using the Arrhenius model as per appendix C.1, in such a way that a 
test duration equivalent to the remaining test duration tremaining, 
1 = 1 929 h is yielded. Taking into account the adapted temperature 
distribution of the ambient temperature, the required test duration of 
1 929 h is yielded at a temperature of Tadapted = 89.5 °C (exact value: 
89.46 °C). 
However, because Tadapted is less than Tfield, 4 (i.e., 89.5 °C < 100 °C), 
the second partial test must be performed at the test temperature Tfield, 
4 = 100 °C. 
The test duration for the second partial test is ttest, T_field, 
4 = toperation * p4 = 8 000 h * 8% = 640 h. 
 
3. Second iteration 
An additional part of the test duration for operating situation i ttest, mode i 
was covered by the second partial test, so the remaining test duration is 
yielded by: 
tremaining, 2 = ttest, mode i - (ttest, T_field, 5 + ttest, T_field, 4) = 2 009 h - 80 h - 640 h= 
1 289 h. 
The portions p5 and p4 of the temperature load spectrum for the ambient 
temperature were already covered by the first two partial tests. 
Therefore, the portions must be set to p4 = p5 = 0 for the further 
iteration, as per the following table. 
 
Table 112: Adapted temperature load spectrum for ambient temperature after first and second 
partial test 
Temperature in °C 
Distribution in % 
-40 
6 
23 
20 
50 
65 
 
To be able to subsequently determine the test temperature for the third 
partial test, the test temperature Tadapted must be calculated using the 
Arrhenius model as per appendix D.1, in such a way that a test duration 
equivalent to the remaining test duration tremaining, 2 = 1 289 h results. 
Taking into account the adapted temperature distribution of the ambient 


### 第 169 页
 
  
Page 169 
VW 80000: 2017-10 
 
 
 
 
 
 
 
temperature, the required test duration of 1 289 h is yielded for the 
temperature Tadapted = 82 °C (exact value: 82.17 °C). 
Because Tadapted is greater than Tfield, 3 (i.e., 82 °C > 50 °C), no further 
iteration is necessary. Therefore, the third and final partial test is 
performed at Tadapted = 82 °C for the test duration ttest, T_field, 3 = tremaining, 
3 = 1 289 h. 
 
In total, testing must be performed for 80 h at 105 °C ambient temperature, for 640 h 
at 100 °C ambient temperature, and for 1 289 h at 82 °C ambient temperature. 
In this example, the coolant temperature is constant at 80 °C during the entire test 
duration. 
 


### 第 170 页
Page 170 
VW 80000: 2017-10 
 
 
 
 
 
 
Appendix E (normative) 
 
Calculation models for the "Temperature cycle durability testing" 
service life test 
 
E.1 Adaptation of the test temperatures to reduce the test cycles 
 
To reduce the number of test cycles for components that are actively operated in 
several operating situations, the test temperature Tmax (or TCC, max) can be increased to 
test individual operating situations relevant to the component. For this purpose, the 
absolute maximum temperature Tmax (or TCC, max) can be applied as the upper test 
temperature for all operating situations relevant to the component. The component must 
remain fully functional here. In addition, it must be ensured that none of the parts, 
subcomponents, or materials belonging to the component are operated outside of their 
specified limits (temperature limits) in each case, taking into account any component 
self-heating that is generated according to the operating mode. 
The test temperature can be adapted for individual or multiple operating situations 
relevant to the component. The required numbers of cycles must be calculated as per 
appendix D in each case. 
Details concerning an increase in the test temperature and the resulting numbers of test 
cycles must be agreed upon between the purchaser and the contractor, and 
documented. 
 
E.2 Coffin-Manson model  
 
The calculation of the test duration for the "Temperature cycle durability testing" service 
life test is based on the average temperature change of the component in the field Tfield 
(see Table 94) and the number of temperature cycles during the service life in the field 
NTempCyclesField. 
2 temperature cycles per day are used for the number of temperature cycles in the field. 
This yields: 
 
NTempCyclesField = 2 * 365 * 15 (years) = 10 950 cycles 
 
Depending on the average temperature change in the field, the acceleration factor of 
the Coffin-Manson model is calculated as follows: 
 
c
ΔT
ΔT
A
field
test
CM







(3) 
 
where: 
ACM 
Acceleration factor of the Coffin-Manson model 
Ttest Temperature difference during a test cycle (Ttest = Tmax - Tmin) 
Tfield Average temperature difference of the ambient temperature of the component at 
its point of use during the service life in the field 
c 
Parameter of the Coffin-Manson model 
A fixed value of 2.5 is used for c in this standard. 
The total number of test cycles is calculated as per 


### 第 171 页
 
  
Page 171 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
CM
Field
TempCycles
test
A
N
N

   
(4) 
 
where: 
Ntest 
Required number of test cycles 
NTempCyclesField Number of temperature cycles during the service life in the field 
ACM 
Acceleration factor of the Coffin-Manson model as per equation (3) 
 
 
 
 
 


### 第 172 页
Page 172 
VW 80000: 2017-10 
 
 
 
 
 
 
E.3 Example: 
 
The number of test cycles (Ntest) is calculated as follows for an ECU with Tmin = -40 °C 
and Tmax = 105 °C, a service life of 15 years in the field, and an average temperature 
difference in the field of Tfield = 40 °C: 
 
1. The number of temperature cycles in the field:  
 
NTempCyclesField = 2 * 365 * 15 (years) = 10 950 cycles 
 
2. Temperature difference during a test cycle: 
 
Ttest = 105 °C - (-40 °C) = 145 °C 
 
3. The acceleration factor of the Coffin-Manson model is calculated as ACM = 25.02 
using equation (3). 
 
4. Using equation (4), this results in a number of test cycles of: 
 
 438 cycles
25.02
10 950 cycles
Ntest


 
 
5. The hold time is the time until the component achieves temperature stabilization 
plus 15 min. Assuming that the component is thermally stabilized after 20 min, 
the hold time is therefore 35 min.  
 
6. This yields the following duration for a cycle: 
t𝑍𝑦𝑘𝑙𝑢𝑠 = 2∙ (
(T𝑚𝑎𝑥 − T𝑚𝑖𝑛)
4 °𝐶/𝑚𝑖𝑛
+ t𝐻𝑎𝑙𝑡𝑒𝑧𝑒𝑖𝑡) 
tZyklus 
tcycle 
Tmax - Tmin 
Tmax - Tmin 
4° C/min 
4 °C/min 
tHaltezeit 
thold time 
 
7. In the example: 
  
tZyklus 
tcycle 
4° C/min 
4 °C/min 
142,5 min 
142.5 min 
 
8. For 438 cycles, the total test duration is therefore 1 040 h. 
 
 


### 第 173 页
 
  
Page 173 
VW 80000: 2017-10 
 
 
 
 
 
 
 
E.4 Coffin-Manson model to be used for components on coolant circuits 
 
For components with a connection to the coolant circuit, all relevant operating situations 
i (see section 4.3; "i" corresponds to the consecutive number of the situations) with their 
corresponding temperature rises for ambient temperature and coolant circuit 
temperature must be taken into account. 
For the "Temperature cycle durability testing" service life test, the key temperatures and 
the number of test cycles must be calculated for each relevant operating situation i, as 
described below; the total number of test cycles is yielded from the sum of the partial 
numbers of test cycles for each relevant operating situation i. 
 
For each relevant operating situation i, the numbers of test cycles for the ambient 
temperature and for the coolant circuit must first be calculated separately according to 
the Coffin-Manson model as per appendix C.7, in order to calculate the number of test 
cycles for operating situation i. 
Because the resulting numbers of test cycles Ntest, ambient and Ntest, CC generally differ but 
the component can only be tested for the respective operating situation i with a uniform 
number of test cycles, the numbers of test cycles must be aligned between the ambient 
temperature and the coolant circuit. 
 
The shorter of the two numbers of test cycles Ntest, ambient and Ntest, CC must be adapted to 
the longer number of test cycles using the following calculation, by dividing the test into 
three partial tests. One partial test is performed with a full temperature rise between Tmin 
and Tmax; the other two partial tests are performed with a reduced temperature rise 
between Tmin and TRT, and between TRT and Tmax. 
 
 
Case A: Ntest, ambient > Ntest, CC 
 
Number of test cycles: 
For Ntest, ambient > Ntest, CC, the number of test cycles for operating situation i is 
Ntest, mode i = Ntest, ambient. 
 
 
Number of test cycles for coolant: 
 
The number of test cycles for the coolant Ntest, CC must be adapted to the greater 
number of test cycles for the ambient temperature Ntest, ambient. The test cycles must be 
performed in the following three temperature ranges: 
 
1. xCC test cycles must be performed between TCC, min and TCC, max. 
The acceleration factor ACM, CC, 1 is calculated according to the Coffin-Manson 
model, with ∆Ttest, 1 = TCC, max - TCC, min 
2. ½ * (Ntest, mode i - xCC) test cycles must be performed between TCC, min and TRT. 
The acceleration factor ACM, CC, 2 is calculated according to the Coffin-Manson 
model, with ∆Ttest, 2 = TRT - TCC, min. 
3. ½ * (Ntest, mode i - xCC) test cycles must be performed between TRT and TCC, max. 
The acceleration factor ACM, CC, 3 is calculated according to the Coffin-Manson 
model, with ∆Ttest, 3 = TCC, max - TRT. 


### 第 174 页
Page 174 
VW 80000: 2017-10 
 
 
 
 
 
 
 
In total, Ntest, mode i temperature cycles are yielded from 1. to 3. 
 
The following is yielded using equation (4) from appendix E.1: 




, 3
,
, 2
,
,1
,
2
1
2
1
CM CC
CC
test,mode,i
CM CC
CC
test,mode,i
CM CC
CC
TempCyclesField
A
x
N
A
x
N
A
x
N










 
 
The number of test cycles xCC is calculated from this as follows: 
 



, 3 
,
, 2
,
,1
,
, 3
,
, 2
,
,
,
2
1
2
CM CC
CM KCC
CC
CM
CM CC
CC
CM
mode i
test
Field
TempCycles
CC
A
A
A
A
A
N
N
x







 
 
The numbers of test cycles for the three partial tests are obtained by introducing xCC 
into points 1. to 3. listed above. 
 
If TCC, op, max < TCC, max, or TCC, op, min > TCC, min, or Tambient, op, max < Tambient, max, or Tambient, op, 
min > Tambient, min, additional hold times at the corresponding temperatures as per 
Figure 47 in section 13.3.2.1 must be taken into account. 
The temperature cycles for the ambient temperature and for the coolant circuit proceed 
synchronously during a test. 
 
 
Case B: Ntest, ambient < Ntest, CC 
 
Number of test cycles: 
For Ntest, ambient < Ntest, CC, the number of test cycles for operating situation i is 
Ntest, mode i = Ntest, CC. 
 
 
Number of test cycles for ambient temperature: 
 
The number of test cycles for the ambient temperature Ntest, ambient must be adapted to 
the greater number of test cycles for the coolant Ntest, CC. The test cycles must be 
performed in the following three temperature ranges: 
 
1. xambient test cycles must be performed between Tambient, min and Tambient, max. The 
acceleration factor ACM, ambient, 1 is calculated according to the Coffin-Manson 
model, with ∆Ttest,1 = Tambient, max - Tambient,min. 
2. ½ * (Ntest, mode i - xambient) test cycles must be performed between Tambient, min and 
TRT. The acceleration factor ACM, ambient, 2 is calculated according to the Coffin-
Manson model, with ∆Ttest, 2 = TRT - Tambient, min. 
3. ½ * (Ntest, mode i - xambient) test cycles must be performed between TRT and Tambient, 
max. The acceleration factor ACM, ambient, 3 is calculated according to the Coffin-
Manson model, with ∆Ttest, 3 = Tambient, max - TRT. 
 
In total, Ntest, mode i temperature cycles are yielded from 1. to 3. 
 
 


### 第 175 页
 
  
Page 175 
VW 80000: 2017-10 
 
 
 
 
 
 
 
The following is yielded using equation (4) from appendix E.1: 




CM,ambient,3
ambient
 i
test, mode
CM,ambient,2
ambient
 i
test, mode
CM,ambient,1
ambient
Field
TempCycles
A
x
N
A
x
N
A
x
N










2
1
2
1
 
 
The number of test cycles xambient is calculated from this as follows: 



CM,ambient,3 
CM,ambient,2
ambient,1
CM,
CM,ambient,3
ambient,2
CM,
 i
test, mode
Field
TempCycles
ambient
A
A
A
A
A
N
N
x







2
1
2
 
 
The numbers of test cycles for the three partial tests are obtained by inserting xambient 
into points 1. to 3. listed above. 
 
If Tambient, op, max < Tambient, max, or Tambient, op, min > Tambient, min, or TCC, op, max < TCC, max, or 
TCC, op, min > TCC, min, additional hold times at the corresponding temperatures as per 
figure 45 in section 16.3.2.1 must be taken into account. 
The temperature cycles for the ambient temperature and for the coolant circuit proceed 
synchronously during a test. 
 
 


### 第 176 页
Page 176 
VW 80000: 2017-10 
 
 
 
 
 
 
E.5 Example Coffin-Manson model to be used for components on coolant 
circuits 
 
For an ECU connected to the coolant circuit, with the ambient temperature range 
Tambient, min = -40 °C to Tambient, max = 120 °C and the coolant temperature range TCC, 
min = -40 °C to TCC, max = 80 °C, a service life in the field of 15 years, an average 
temperature difference of the ambient in the field of ∆Tfield, ambient = 60 K, and an average 
temperature difference of the coolant in the field of ∆Tfield, CC = 36 K, the number of test 
cycles for an operating situation i is calculated as follows: 
 
Number of test cycles for ambient temperature and coolant temperature: 
The calculation of the numbers of test cycles for the ambient temperature and for the 
coolant according to the Coffin-Manson model as per appendix E.1 supplies the 
following values: 
 
Ntest, ambient = 943 cycles 
Ntest, CC = 540 cycles 
 
Because Ntest, ambient > Ntest, CC, the number of test cycles for operating situation i Ntest, 
mode i = Ntest, ambient = 943 cycles. The number of test cycles for the coolant must be 
adapted. 
 
Adapting the number of test cycles for the coolant: 
The number of test cycles for the coolant is adapted to Ntest, mode i = 943 cycles in three 
parts: 
1. xCC test cycles must be performed between TCC, min = -40 °C and TCC, max = 80 °C. 
According to the Coffin-Manson model, this yields 
 
calculated acceleration factor ACM, CC, 1 = 
5.2
36
)
( 40
80






 

C
C
C
 = 20.29. 
2. ½ * (943 - xCC) test cycles must be performed between TCC, min = -40 °C and 
TRT = 23 °C. According to the Coffin-Manson model, this yields  
 
calculated acceleration factor ACM, CC, 2 = 
5.2
36
)
( 40
23






 

C
C
C
 = 4.05. 
3. ½ * (943 - xCC) test cycles must be performed between TRT = 23 °C and TCC, 
max = 80 °C. According to the Coffin-Manson model, this yields  
 
calculated acceleration factor ACM, CC, 3 = 
5.2
36
23
80








C
C
C
 = 3.15. 


### 第 177 页
 
  
Page 177 
VW 80000: 2017-10 
 
 
 
 
 
 
 
This yields the following for xCC: 
 












, 3
,
, 2
,
,1
,
, 3
,
, 2
,
2
1
2
CM CC
CM CC
CC
CM
CM CC
CC
CM
 i
test, mode
Field
TempCycles
CC
A
A
A
A
A
N
N
x




cycles
453
.3 15
.4 05
2
1
.29
20
.3 15
.4 05
2
943
950
10







  
 
Therefore, the following numbers of test cycles calculated as per points 1. to 3. are 
yielded for the three temperature ranges: 
 
1. Between TCC, min = -40 °C and TCC, max = 80 °C, 453 cycles must be performed. 
2. Between TCC, min = -40 °C and TRT = 23 °C, 245 cycles must be performed. 
3. Between TRT = 23 °C and TCC, max = 80 °C, 245 cycles must be performed. 
 
Adding the partial test cycles again yields the total number of test cycles for operating 
situation i Ntest, mode i = 943 cycles. 
 
The temperature cycles for the ambient temperature and for the coolant circuit proceed 
synchronously during a test. 
 
 


### 第 178 页
Page 178 
VW 80000: 2017-10 
 
 
 
 
 
 
Appendix F (normative) 
 
Calculation models for the "Damp heat, constant – severity 2" test 
 
F.1 Lawson model 
 
The calculation of the test duration for the "Damp heat, constant – severity 2" test is 
based on the average ambient humidity RHFieldParking and the average ambient 
temperature TFieldParking of the component in the parked vehicle. 
Unless otherwise defined in the Performance Specification, the following values must be 
used for the calculation: 
 
Table 113: Average ambient humidity and ambient temperature in the parked vehicle 
Point of use 
Average ambient 
humidity RHFieldParking 
Average ambient 
temperature TFieldParking 
In the passenger 
compartment/luggage compartment 
60% relative humidity 
23 °C 
Outside the passenger 
compartment/luggage compartment 
65% relative humidity 
23 °C 
  
The acceleration factor of the Lawson model is calculated as follows, as a function of 
the average ambient humidity and ambient temperature in the field: 
 















 

































2
ng
FieldParki
2
test
FieldParking 
 
test
A
T/RH
RH
b RH
 273.15
T
1
 273.15
T
1
k
E
e
A
  (5) 
 
where: 
AT/RH 
Acceleration factor of the Lawson model 
b 
Constant (b = 5.57 * 10-4) 
EA 
Activation energy (EA = 0.4 eV) 
k 
Boltzmann constant (k = 8.617 x 10-5 eV/K) 
Ttest 
Test temperature in °C 
TFieldParking 
Average ambient temperature in °C  
RHtest  
Relative humidity in % during the test 
RHFieldParking Average relative humidity in %  
-273.15 °C 
Absolute zero of temperature 
 
The test duration for the "Damp heat, constant – severity 2" test is calculated using the 
following equation: 
T/RH
ng
FieldParki
test
A
t
t

  
 
(6) 
where: 
ttest 
Test duration in h 
tFieldParking Non-operating duration (parking duration) in h during the service life in the 
field (131 400 h in the worst-case scenario if the vehicle is not used) 
AT/RH  
Acceleration factor of the Lawson model as per equation (5) 
 


### 第 179 页
 
  
Page 179 
VW 80000: 2017-10 
 
 
 
 
 
 
 
F.2 Example: 
 
For an ECU installed in the engine compartment, the test duration is calculated as 
follows: 
 
1. For the component, an average temperature of 
TFieldParking = 23 °C and a relative humidity of RHFieldParking = 65%  
in the parked vehicle are assumed. 
The test conditions are Ttest = 65 °C and RHtest = 93%. 
 
Using equation (5), these values yield a combined acceleration factor of the 
Lawson model of AT/RH = 82.5. 
 
2. The parking duration in the field is tFieldParking = 131 400 h. 
Using equation (6), this yields a total test duration of: 
 
 
 1593 hours
82.5
131 400 hours
ttest


 
 
 
 


### 第 180 页
Page 180 
VW 80000: 2017-10 
 
 
 
 
 
 
Appendix G (informative) 
 
Condensation test, chamber programming, and graphs 
 
 
Figure 52: Programming of the test chamber 
 
During the temperature increase, the temperature of the water bath is used as the 
control variable. When 80 °C is reached, the climatic chamber is switched over to 
temperature control (standard operation). 
 


### 第 181 页
 
  
Page 181 
VW 80000: 2017-10 
 
 
 
 
 
 
 
 
Figure 53: Sequence of the condensation test, 1 cycle 
I: Temperatur 
°C 
S: Temperatur 
°C 
I: Rel. Feuchte 
%rF 
S: Rel. Feuchte 
%rF 
I: T Bad  
°C 
Z: Korr. T. 
Actual temperature  
°C 
Desired temperature 
°C 
Actual rel. humidity  
%RH 
Desired rel. humidity 
%RH 
Actual bath temp.  
°C 
Z: Corr. temp. 
 
1. 
Controlled water bath temperature 
2. 
Resulting test chamber temperature 
3. 
Actual humidity in the test chamber 
 
1 
2 
3 


### 第 182 页
Page 182 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Figure 54: Sequence of the condensation test, 5 cycles 
I: Temperatur 
°C 
S: Temperatur 
°C 
I: Rel. Feuchte 
%rF 
S: Rel. Feuchte 
%rF 
I: T Bad  
°C 
Z: Korr. T. 
Actual temperature  
°C 
Desired temperature 
°C 
Actual rel. humidity  
%RH 
Desired rel. humidity 
%RH 
Actual bath temp.  
°C 
Z: Corr. temp. 
 
 
 


### 第 183 页
 
  
Page 183 
VW 80000: 2017-10 
 
 
 
 
 
 
 
Appendix H 
 
Examination methods for physical analysis 
 
• 
Residual torques (e.g., housing threaded connection, fastening screws on the 
shaker table) 
• 
Solder joint flaws  
• 
Subcomponent/PCB discolorations (especially due to thermal causes)  
• 
Rough/smooth running, grinding, play (for mechanically moving parts)  
• 
Traces of abrasion  
• 
Jumps, cracks, deformation of materials (especially in potting and sealing 
compounds). A suitable test method (x-ray, computed tomography (CT), 
microsections, etc.) must be selected by mutual agreement.  
• 
Opacity (especially for parts in optical sensor systems) 
• 
State of latches and clips  
• 
Traces of corrosion and migration 
• 
Evaluation of plastics for hydrolysis resistance (especially for components with 
inserted lead frames and t.30 circuitry)  
• 
Damage to vias of PCBs, especially thermal vias  
• 
Damage to the internal connection (paddles) of large electrolytic capacitors after 
mechanical load (vibration, mechanical shock, drop test)  
• 
Connector pin damage (e.g., due to current, temperature, rubbing, oxidation)  
• 
Other irregularities  
• 
Result of in-circuit test (ICT) 
 
Evaluation of sealing surfaces for corrosion creepage: 
 
Corrosion creepage is generally impermissible. 
 
Corrosion creepage of ≤ 50% of the sealing surface must be jointly 
evaluated with the purchaser. 
 
  
Figure 55: Sealing geometries 
 
 

