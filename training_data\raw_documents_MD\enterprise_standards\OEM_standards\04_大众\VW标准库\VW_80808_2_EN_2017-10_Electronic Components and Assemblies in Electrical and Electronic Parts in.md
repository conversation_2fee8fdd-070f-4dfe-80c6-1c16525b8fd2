# VW_80808_2_EN_2017-10_Electronic Components and Assemblies in Electrical and Electronic Parts in.pdf

## 文档信息
- 标题：
- 作者：
- 页数：25

## 文档内容
### 第 1 页
Group standard
VW 80808-2
Issue 2017-01
Class. No.:
8MC0
Descriptors:
electronic component, electronic assembly, semiconductor, active component, passive component
Electronic Components and Assemblies in Electrical and Electronic Parts in
Motor Vehicles up to 3.5 t
Requirements and Use – Part 2
Preface
To provide for improved practical implementation in a project environment, and to orient the stand‐
ard toward the target groups, Volkswagen standard VW 80808 consists of two parts. The two parts
depend on each other and cannot be viewed individually.
The content and scope of VW 80808-2 are characterized by a high level of detail and supplement
those of VW 80808-1.
Previous issues
VW 80808-2: 2015-02
Changes
The following changes have been made to VW 80808-2: 2015-02:
–
Standard completely revised
Contents
Page
Scope ......................................................................................................................... 3
Definitions .................................................................................................................. 3
Symbols and abbreviations ........................................................................................ 3
Structure of the standard ............................................................................................ 4
General requirements ................................................................................................ 6
General requirements for the use of electronic components ..................................... 7
Quality management .................................................................................................. 7
1
2
3
4
5
6
6.1
Always use the latest version of this standard.
This electronically generated standard is authentic and valid without signature.
The English translation is believed to be accurate. In case of discrepancies, the German version is alone authoritative and controlling.
Page 1 of 25
Technical responsibility
The Standards department
EEIP/1
Dr. Torsten Polte 
Tel.: +49 5361 936035
I/EE-61
Berthold Hellenthal 
Tel.: +49 841 89 45578
EEG4
Achim Henne 
Tel.: +49 711 911 88782
K-IPP/5 Dirk Beinker 
K-IPP
Tel.: +49 5361 9-32438
Uwe Wiesner
All rights reserved. No part of this document may be provided to third parties or reproduced without the prior consent of one of the Volkswagen Group’s Standards departments.
© Volkswagen Aktiengesellschaft
VWNORM-2015-07d


### 第 2 页
Page 2
VW 80808-2: 2017-01
Quality assurance process ......................................................................................... 7
Automotive quality system ......................................................................................... 7
Zero-fault strategy ...................................................................................................... 7
Process audit ............................................................................................................. 7
Definition of the forecast failure rate .......................................................................... 8
Component requirements ........................................................................................... 8
Development of silicon technologies .......................................................................... 8
Housing development ................................................................................................ 8
Product design ........................................................................................................... 9
Characterization and testing ...................................................................................... 9
Requirements for the production process ................................................................ 11
Qualification requirements ....................................................................................... 12
Lifetime support requirements .................................................................................. 13
Ramp-up assurance ................................................................................................. 13
Engineering change management ........................................................................... 13
Fault analysis ........................................................................................................... 13
Termination .............................................................................................................. 13
Supply assurance and storage of components ........................................................ 13
Component chip technologies .................................................................................. 14
Bare-die applications ................................................................................................ 14
Active semiconductor components .......................................................................... 14
Microcontroller .......................................................................................................... 14
Embedded flash for microcontrollers ........................................................................ 14
Diodes (excluding LEDs) .......................................................................................... 15
Light emitting diodes (LEDs) .................................................................................... 15
Passive components ................................................................................................ 15
Resistors .................................................................................................................. 15
Capacitors ................................................................................................................ 15
Multilayer ceramic capacitors (MLCCs) ................................................................... 15
Tantalum capacitors ................................................................................................. 16
Electrolytic capacitors .............................................................................................. 16
Varistors ................................................................................................................... 16
Circuit carriers .......................................................................................................... 16
Basic requirements for organic circuit carriers ......................................................... 16
Rigid PCBs ............................................................................................................... 16
PCB component requirements ................................................................................. 16
Requirements for the PCB production process ........................................................ 16
PCB application requirements .................................................................................. 16
Flexible printed circuits (flexible film – FPC) ............................................................ 16
Requirements for the FPC component ..................................................................... 16
Requirements for the FPC production process ........................................................ 16
FPC application requirements .................................................................................. 16
Rigid flexible PCBs (Starrflex) .................................................................................. 17
Semi-flexible PCBs (Semiflex) ................................................................................. 17
3D-MID ..................................................................................................................... 17
PCBs with embedded components .......................................................................... 17
Assemblies ............................................................................................................... 17
Product requirements ............................................................................................... 17
Requirements for the manufacturing process .......................................................... 17
Soldering technology ................................................................................................ 17
Wire bonding technologies ....................................................................................... 17
Press-fit technology .................................................................................................. 17
6.1.1
6.1.2
6.1.3
6.1.4
6.1.5
6.2
6.2.1
6.2.2
6.2.3
6.2.4
6.3
6.4
6.5
6.5.1
6.5.2
6.5.3
6.5.4
6.5.5
7
7.1
8
8.1
8.1.1
8.2
8.3
9
9.1
9.2
9.2.1
9.2.2
9.2.3
9.3
10
10.1
10.2
10.2.1
10.2.2
10.2.3
10.3
10.3.1
10.3.2
10.3.3
10.4
10.5
10.6
10.7
11
11.1
11.2
11.2.1
11.2.2
11.2.3


### 第 3 页
Page 3
VW 80808-2: 2017-01
Rework and repair .................................................................................................... 17
Depaneling of assemblies ........................................................................................ 17
Cleanliness of assemblies ........................................................................................ 17
Coating (gel, protective coating, potting) of assemblies ........................................... 17
Power modules ........................................................................................................ 18
Applicable documents .............................................................................................. 18
Bibliography ............................................................................................................. 18
Qualification of MLCCs with soft termination ........................................................... 21
Verifying the service life of electrolytic capacitors .................................................... 23
Minimum Requirements for Parametric Tests (Pre- and Post-Stress) during
Product-Characterization ......................................................................................... 24
11.2.4
11.2.5
11.2.6
11.2.7
11.3
12
13
Appendix A
Appendix B
Appendix C
Scope
See VW 80808-1 ("Scope" section)
Definitions
See VW 80808-1 ("Definitions" section)
Symbols and abbreviations
Automotive Electronics Council
Automotive Industry Action Group
Advanced product quality planning
Automatic test equipment
Back end of line
Below minimum yield
Bias temperature instability
Chip-package interaction
Process capability index
Design for failure analysis
Design for manufacturing
Design failure modes and effects analysis
Design for reliability
Design for test
Design of Experiment
Dynamic part average testing
Error-correcting code
Electronic design automation
Electromigration
Electrostatic discharge
Front end of line
Failure modes and effects analysis
Flexible printed circuit
Good die bad cluster
Geographic part average testing
Highly accelerated stress test
Hot carrier instability
High-temperature operating life
1  
2  
3  
AEC
AIAG
APQP
ATE
BEOL
BMY
BTI
CPI
Cpk
DFFA
DFM
DFMEA
DFR
DFT
DOE
DPAT
ECC
EDA
EM
ESD
FEOL
FMEA
FPC
GDBC
GPAT
HAST
HCI
HTOL


### 第 4 页
Page 4
VW 80808-2: 2017-01
High-voltage stress test
International Automotive Task Force
Integrated circuit
Supply current (Idd) quiescent state
Inter-layer dielectric
Intellectual property
Light emitting diode
Last-time buy
Molded interconnect device
Multilayer ceramic capacitor
Moisture Sensitivity Level
Nearest neighbor residual
Nominal voltage
Non-volatile memory
Out-of-control action plan
Part average testing
Printed circuit board
Process control monitoring
Product change notification
Process failure modes and effects analysis
Production process and product approval
Production part approval process
Positive temperature coefficient
Product termination notification
Relative humidity
Scanning acoustic microscopy
Statistical bin limit
Soft error latch-up
Soft error rate
Short voltage elevation testing
Stress migration
Statistical process control
Statistical post processing
Statistical yield analysis
Time-dependent dielectric breakdown
Temperature humidity bias
Junction temperature T-junction
RMS value of a voltage
Structure of the standard
Table 1 provides an overview, stating which part of the standard (VW 80808-1 and/or VW 80808-2)
contains the individual sections.
Table 1 – Structure of the standard
Section
VW 80808-1
VW 80808-2
Section 1 "Scope"
X
 
Section 2 "Definitions"
X
 
Section 3 "Symbols and abbreviations"
X
X
HVST
IATF
IC
IDDQ
ILD
IP
LED
LTB
MID
MLCC
MSL
NNR
NV
NVM
OCAP
PAT
PCB
PCM
PCN
PFMEA
PPA
PPAP
PTC
PTN
RH
SAM
SBL
SEL
SER
SHOVE
SM
SPC
SPP
SYA
TDDB
THB
Tj
Vrms
4  


### 第 5 页
Page 5
VW 80808-2: 2017-01
Section
VW 80808-1
VW 80808-2
Section 4 "Structure of the standard"
X
X
Section 5 "General requirements"
X
 
Section 6 "General requirements for the use of electronic components"
X
X
Section 6.1 "Quality management"
 
X
Section 6.1.1 "Quality assurance process"
 
X
Section 6.1.2 "Automotive quality system"
 
X
Section 6.1.3 "Zero-fault strategy"
 
X
Section 6.1.4 "Process audit"
 
X
Section 6.1.5 "Definition of the forecast failure rate"
 
X
Section 6.2 "Component requirements"
X
 
Section 6.2.1 "Development of silicon technologies"
 
X
Section 6.2.2 "Housing development"
 
X
Section 6.2.3 "Product design"
 
X
Section 6.2.4 "Characterization and testing"
 
X
Section ******* "Characterization"
 
X
Section ******* "Test strategy"
 
X
Section ******* "Outlier screening"
 
X
Section ******* "Screening tests"
 
X
Section ******* "Test coverage"
 
X
Section 6.3 "Requirements for the production process"
 
X
Section 6.4 "Qualification requirements"
 
X
Section 6.5 "Lifetime support requirements"
 
X
Section 6.5.1 "Ramp-up assurance"
 
X
Section 6.5.2 "Engineering change management"
 
X
Section 6.5.3 "Fault analysis"
X
 
Section 6.5.4 "Termination"
 
X
Section 6.5.5 "Supply assurance and storage of components"
 
X
Section 7 "Component chip technologies"
 
X
Section 7.1 "Bare-die applications"
 
X
Section 8 "Active semiconductor components"
X
X
Section 8.1 "Microcontroller"
 
X
Section 8.1.1 "Embedded flash for microcontrollers"
 
X
Section 8.2 "Diodes (excluding LEDs)"
X
 
Section 8.3 "Light emitting diodes (LEDs)"
 
X
Section 9 "Passive components"
X
 
Section 9.1 "Resistors"
X
 
Section 9.2 "Capacitors"
X
 
Section 9.2.1 "Multilayer ceramic capacitors (MLCCs)"
X
 
Section ******* "Component requirements"
X
 
Section ******* "Qualification requirements"
X
 


### 第 6 页
Page 6
VW 80808-2: 2017-01
Section
VW 80808-1
VW 80808-2
Section ******* "Application requirements"
X
 
Section 9.2.2 "Tantalum capacitors"
X
 
Section 9.2.3 "Electrolytic capacitors"
X
 
Section 9.3 "Varistors"
X
 
Section 10 "Circuit carriers"
X
 
Section 10.1 "Basic requirements for organic circuit carriers"
X
 
Section 10.2 "Rigid PCBs"
X
 
Section 10.2.1 "PCB component requirements"
X
 
Section 10.2.2 "Requirements for the PCB production process"
X
 
Section 10.2.3 "PCB application requirements"
X
 
Section 10.3 "Flexible printed circuits (flexible film – FPC)"
X
 
Section 10.3.1 "Requirements for the FPC component"
X
 
Section 10.3.2 "Requirements for the FPC production process"
X
 
Section 10.3.3 "FPC application requirements"
X
 
Section 10.4 "Rigid flexible PCBs (Starrflex)"
X
 
Section 10.5 "Semi-flexible PCBs (Semiflex)"
X
 
Section 10.6 "3D-MID"
X
 
Section 11 "Assemblies"
X
 
Section 11.1 "Product requirements"
X
 
Section 11.2 "Requirements for the manufacturing process"
X
 
Section 11.2.1 "Soldering technology"
X
 
Section 11.2.2 "Wire bonding technologies"
X
 
Section 11.2.3 "Press-fit technology"
X
 
Section 11.2.4 "Rework and repair"
X
 
Section 11.2.5 "Depaneling of assemblies"
X
 
Section 11.2.6 "Cleanliness of assemblies"
X
 
Section 11.2.7 "Coating (gel, protective coating, potting) of assemblies"
X
 
Section ******** "Use of underfiller"
X
 
Section 11.3 "Power modules"
X
 
Section 12 "Applicable documents"
X
X
Section 13 "Bibliography"
X
X
Appendix A "Qualification of MLCCs with soft termination"
 
X
Appendix B "Verifying the service life of electrolytic capacitors"
 
X
Appendix C "Minimum Requirements for Parametric Tests (Pre- and Post-Stress) dur‐
ing Product-Characterization"
 
X
General requirements
See VW 80808-1 ("General requirements" section)
5  


### 第 7 页
Page 7
VW 80808-2: 2017-01
General requirements for the use of electronic components
Quality management
Quality assurance process
A suitable quality assurance process, such as Advanced Product Quality Planning (APQP) as per
AIAG [12], must be established for semiconductor elements still in the development phase. The
process must ensure transparency in terms of the quality, provision of samples in good time, and
production.
The requirements of this standard must be planned, implemented, and verified on the basis of
milestones within this quality assurance process.
If risks at semiconductor level are identified that could lead to an immediate risk for the purchaser,
then the purchaser must be actively involved in the quality assurance process. The contractor must
ensure that the semiconductor manufacturer supports a product release process in line with the
PPAP definition as per AIAG [13]. The PPAP documentation must be submitted to the purchaser
on request.
The design and process FMEAs required in line with PPAP must determine all potentially critical
risks with respect to function and reliability. Identified risks must be covered by measures and a
plan of action. The characteristics identified as critical must be taken into consideration in the con‐
trol plan (see section 6.3 "Requirements for the production process").
Automotive quality system
Production sites for components conforming to automotive requirements must at least be certified
as per IATF 16949 by an IATF-registered certifier or alternatively as per VDA Volume 6 Part 1.
Semiconductor manufacturers who do not have their own production lines must at least have a cer‐
tificate as per ISO 9001.
Zero-fault strategy
The contractor must ensure that the semiconductor manufacturer has established a zero-fault
strategy, for example, based on the Zero Defects Guideline AEC-Q004 [22]. Appropriate processes
must be established to ensure that problems are identified internally, and that no faulty semicon‐
ductor elements are delivered to the purchaser along the supply chain.
A pervasive continuous improvement process (CIP) must be implemented in order to actively iden‐
tify, reduce, and eliminate process and product weaknesses. Central key indicators such as yield,
fault density, and error rate in the field must be continuously monitored and optimized through im‐
provement programs. The implementation of these actions, and the results must be documented
and presented to the purchaser on request.
Process audit
The production process must be audited at least every two years by means of VDA Vol‐
ume 6 Part 3 process audits in the form of third-party audits or self-audits. The overall result of the
audit must be submitted to the purchaser on request.
The audit result must include the currently used technology at the current production location.
The supplier must ensure that audits at sub-contractors can also be supervised by the purchaser.
6  
6.1  
6.1.1  
6.1.2  
6.1.3  
6.1.4  


### 第 8 页
Page 8
VW 80808-2: 2017-01
Definition of the forecast failure rate
On request, the contractor must, in collaboration with the component manufacturer, provide a fore‐
cast of the failures in time (FIT) rate with a confidence interval of at least 60%.
The requirements of the standard JESD85 apply [23]. The operating temperature assumed for the
numerical simulation, the activation energy, and other required key indicators for numerical simula‐
tion (e.g., test duration, scope of random sampling, etc.) must be stated.
Component requirements
See VW 80808-1 ("Component requirements" section)
Development of silicon technologies
The technology must be developed in a documented process as per IATF 16949 ("Product realiza‐
tion" section) with suitable milestones and evaluations. The documented process must ensure a
PPAP- or PPA-ready (see VDA volume 2) technology at the time of product introduction. The docu‐
mentation of the technology development process must be submitted to the purchaser on request.
For all wafer-technology-related failure mechanisms from the FEOL and BEOL, at least for TDDB
(gate & metal dielectrics), HCI, BTI, EM, SM, SER, and SEL (see JEP122G [1], JESD89A [2],
JESD89-1A [3], JESD89-2A [4], JESD89-3A [5]), it must be demonstrated to the purchaser, on re‐
quest, how these failure mechanisms and their behavior over operating hours, operating voltage,
and operating temperature are taken into account during technology development and design.
Verification: For a given load profile, consisting of a voltage curve and temperature histogram, and
possibly a temperature change spectrum, the expected failure rate due to the aforementioned fail‐
ure mechanisms must be estimated for the purchaser on request, with a confidence interval of at
least 90% if technically possible. The method used to estimate this failure rate must also be pre‐
sented.
The wafer technology must be designed and qualified at least for the temperature range specified
for the product. The wafer technology must be qualified in accordance with the methodology speci‐
fied in JP001A [6].
Housing development
The housing must be developed in a documented process as per IATF 16949 ("Product realiza‐
tion" section) with suitable material selection, DFMEA, milestones, evaluations, and verifications.
The documented process must ensure a PPAP- or PPA-ready (VDA volume 2) package technolo‐
gy at the time of product introduction. The documentation of the housing development process
must be submitted to the purchaser on request.
A process-window evaluation must be performed for each new or changed individual process (die
attach, wire-bonding, molding, separation) by means of statistical test planning (Design of Experi‐
ment – DoE). The basis for test planning is at least the chemical and physical properties of the die,
of the mold compound, of the interconnects, and of the lead frame or substrate and the DFMEA.
For wafer technology nodes ≤45 nm, the influence of the housing design (CPI) or the processing
during the assembly of the electronic control unit (soldering, PCB bending, etc.) on the chip func‐
tionality (mechanically generated transistor drift) or chip integrity (ILD crack sensitivity) must be
evaluated and documented as per JEP156 [7] and JEP150.01 [8] or a comparable manufacturer-
specific procedure, and the results must be taken into account in the design of the component and
the assembly.
6.1.5  
6.2  
6.2.1  
6.2.2  


### 第 9 页
Page 9
VW 80808-2: 2017-01
The CPI investigation for the chip-package combination selected for the product must be submitted
to the purchaser on request.
If a wafer technology ≤100 nm is being used for automotive technologies for the first time, the
aforementioned requirements must be fulfilled.
Gel coating is not permitted for semiconductor components if the "die" (silicon) is completely en‐
closed by a molding compound, which has been applied through an injection molding process.
Product design
The product must be developed in a documented process as per IATF 16949 ("Product realization"
section) with suitable milestones and evaluations. The documented process must ensure PPAP- or
PPA-ready (VDA volume 2) products at the time of product introduction. The documentation of the
product development process must be submitted to the purchaser on request.
In the component development process, it must be ensured that there is a link between technology
models and IP or finished product designs. In particular, revision and errata management and for‐
ward and backward traceability are required. This must be ensured by using appropriate IT tools.
In addition to the functionality, the following must be take into account as targets in the product de‐
sign: test coverage, yield, reliability, product service life, and ease of analysis. For ICs, state-of-the-
art design methods such as design margining, design for test (DFT), design for reliability (DFR),
design for manufacturing (DFM), and design for failure analysis (DFFA) must be used.
The cited design methods (DFx) are technology-dependent. The contractor must present the strat‐
egies and methods used by the component manufacturer to the purchaser on request. Within the
framework of DFR, rules for the use of redundant VIAs must be defined and implemented in the
product design. The corresponding documents must be submitted to the purchaser on request.
The feasibility of testing requirements must be ensured by means of DFT (see section 6.2.4).
Variations in production processes, product aging processes, fluctuations in ambient conditions
(temperature, voltage, etc.), and inaccuracies of the EDA tools must be taken into account in the
digital and analog circuit simulations as far as technically possible.
Characterization and testing
Characterization
The electrical characterization of the integrated circuit must follow a defined and documented proc‐
ess, for example based on the current version of AEC-Q003 [9]. The documentation of the charac‐
terization must be submitted to the purchaser on request.
The verification and characterization of the integrated circuit must take into account the dispersion
of critical parameters in wafer production, such as threshold voltages, feature sizes, and the values
of integrated passive components, with a spread of ±3 sigma. Critical parameter fluctuations must
be realized by deliberately constructing boundary samples (corner lots). The characterization must
be conducted over the entire specified voltage and frequency range at a minimum of room temper‐
ature (as per VW 80000) and at the temperature limits of the product specification.
Test strategy
The test program must be developed in a documented process as per IATF 16949 ("Product reali‐
zation" section) with suitable milestones and evaluations. The documented process must ensure a
minimum of the required test coverage and a system of revision control at the time of product
6.2.3  
6.2.4  
*******  
*******  


### 第 10 页
Page 10
VW 80808-2: 2017-01
introduction. The documentation of the test program development process must be submitted to
the purchaser on request.
It must be ensured that a link and forward and backward traceability exist between the test revision
and the delivered products.
At the start of production of IC products, the electrical function check must be conducted at high
temperature (critical upper temperature), room temperature (as per VW 80000), and low tempera‐
ture (critical lower temperature). For new IC products, the three temperature levels must be cov‐
ered at least in the combination of all conducted production tests, including wafer test and end test
(ATE). Individual temperature levels can be omitted if it can be ensured, by means of guard-band‐
ing, that test coverage is provided over the complete specified temperature range.
The method for assuring the entire temperature range and documenting the results must be sub‐
mitted to the purchaser on request.
The aging processes of the product must be taken into account when defining the test specification
limits.
Outlier screening
A test strategy for avoiding potential early and reliability failures must be in place. It must contain at
least the following methods:
–
Selection of outliers in production testing (SPP) by means of the statistical methods BMY,
SBL, SYA, and PAT (as per AEC-Q001 [10], AEC-Q002 [11]) for all critical parameters. The
critical parameters must be derived from the design and process FMEAs.
–
For technology nodes ≤65 nm, advanced outlier techniques such as DPAT, GPAT, GDBC (re‐
moval of good dies in and around bad clusters), pattern recognition, linear regression analysis,
and nearest neighbor residual (NNR) must be applied. The applied outlier detection solution
must be submitted to the purchaser on request.
Screening tests
The use of stress screening tests to avoid early or reliability failures must be applied to reflect the
technology and product design. It includes at least:
–
Data retention bake for products with non-volatile memory (NVM).
–
Burn-in test or verification demonstrating that a burn-in test is not necessary, or not permissi‐
ble. For technology nodes ≤45 nm, the influence on the service life consumption must be eval‐
uated by means of a burn-in (e.g., due to "non-conducting channel hot carrier effects").
–
Stress tests by applying higher operating voltages (HVST, SHOVE).
Test coverage
The minimum requirements for the ATE test are:
–
Digital test coverage (stuck-at fault, without memory) >98%
–
Digital runtime analysis (at maximum frequency) with test coverage >85%
–
IDDQ test with test coverage >85%.
The documentation of the test coverage must be submitted to the purchaser on request.
Each component must be tested with the full test scope in accordance with the same test specifica‐
tion.
*******  
*******  
*******  


### 第 11 页
Page 11
VW 80808-2: 2017-01
Requirements for the production process
In the wafer and housing production, the process and machine stability must be monitored with
statistical process control (SPC) by applying suitable intervention rules (e.g., Western Electric
Rules, VDA Volume 6 Part 1). The control limits must be implemented with a value of at least
±3 sigma. Additionally, work instructions must exist that define the next steps (OCAP) in case of
control limit violations.
At the places of manufacture, a process for handling non-conforming material must be established
that includes at least the following rules:
–
Violation of control limit: no reliability-relevant faults permissible
–
Violation of specification limit: deliveries to customers not permissible
Stricter process checks with tighter control limits must be applied for critical process steps. Critical
process steps must be derived from the design and process FMEAs and marked in the production
control plan. This applies in particular for process steps with reliability-relevant features that cannot
be detected by means of electrical tests. The control plan and the PFMEA must be submitted to
the purchaser on request.
The process capability index Cpk (calculated as per AIAG "Statistical Process Control" [14]) must
be at least 1.67 for all SPC parameters.
In production planning, a method for selecting the currently most stable production systems (in
terms of tolerances, downtime, fault density) must be implemented for all automotive batches. The
method and the characteristics used must be presented to the purchaser on request.
In component production, process control monitoring (PCM) must be performed for wafer process
control. The implemented PCM solution must be submitted to the purchaser on request.
In the production of ICs, the PCM test must be conducted on 100% of wafers. The acceptance cri‐
teria of the PCM test and the number of measuring points per wafer must be submitted to the pur‐
chaser on request. For PCM parameters suitable for monitoring critical process steps, an adaptive
test method must be implemented in addition if the wafer has a result that is not 100% OK. In par‐
ticular, rules and a process for recording additional measuring points must be defined. The results
achieved from this and the wafer disposition must be documented. The documentation must be
submitted to the purchaser on request.
The continuous quality of the product production process must be assured and documented as per
IATF 16949 ("Monitoring and measurement" section to "Improvement" section) by means of an ap‐
propriate conformity monitoring process (random-sample analysis by means of selected device
and package qualification tests) at least twice a year with at least 30 parts. Conformity results from
IATF 16949 ("Manufacturing process design input" section). The documentation must be submitted
to the purchaser on request.
The optical flaw inspections in the manufacturing process (light- and dark-field inspections) must
be specified so as to detect flaw sizes in the dimension of the critical feature sizes of the process
technology. Control limits must be defined for all particle measurements (product measurement,
plant particles, and environmental particles).
It must be ensured, by means of optical outgoing checks on 100% of the wafers, that fundamental
faults, such as passivation flaws and corrosion, are reliably detected prior to delivery.
In component production, the electrical fault density of the entire process must be monitored and
documented.
6.3  


### 第 12 页
Page 12
VW 80808-2: 2017-01
Qualification requirements
Only automotive components qualified at least as per the current versions of AEC-Q100 [15], AEC-
Q101 [16], and AEC-Q200 [17]may be used.
The qualification of power electronics modules must be performed as per VW 82324.
For components whose qualification is performed as per AEC-Q100 [15], a drift analysis of the
specified parameters based on the loading in the HTOL qualification test must be conducted. For
each parameter, the maximum occurring parameter drift must be less than the distance of the dis‐
tribution before the HTOL from the respective specification limit. The use of generic data (as per
AEC-Q100 [15] section  "Use of Generic Data to Satisfy Qualification and Requalification Require‐
ments" and appendix  "Definition of a Qualification Family") is permissible in the drift analysis.
If the HTOL test does not represent the service life requirement as per VW 80808-1 ("Component
requirements" section) of this standard, a drift prediction for the target service life must also be de‐
rived from the drift analysis. The documentation of the drift analysis and, if applicable, the drift pre‐
diction must be submitted to the purchaser on request.
For discrete, active semiconductor components, the parameters listed in appendix C as per AEC-
Q101 [16] test 1 must be characterized at room temperature before and after the load tests, and as
per test 4 at limit temperatures. The contractor must specifically define other reliability-relevant pa‐
rameters for the semiconductor component.
After the temperature cycle test (as per AEC-Q100 [15], section "Temperature Cycling", and AEC-
Q101 [16], section "Temperature Cycling"), delamination is permissible only if it has no effect on
the reliability of the component. This is not the case, in particular:
–
if delamination occurs in the area of the wire-bonding connections;
–
if delamination affects >50% of the die attach (percentage calculated in relation to the total
area examined);
–
if delamination growth >20% occurs in the area of the die attach (percentage calculated in re‐
lation to the total area examined);
–
if delamination growth >10% occurs along a polymer film intended to span and electrically in‐
sulate metal areas (percentage calculated in relation to the total area examined, applicable to
metal lead frame packages);
–
if the electrical or thermal behavior of the component is affected by delamination of the die at‐
tach in such a way that violation of the specification limits is to be expected over the service
life;
–
or if delamination occurs at interfaces that connect the outside of the housing to the die.
Twenty-two randomly selected sample parts per batch from testing must be investigated for de‐
lamination using SAM as per J-STD-035 [18].
The results of the SAM examination must be confirmed by microsections of a random sample of at
least 5 components.
For wafer technologies ≤45 nm or first-use technologies ≤100 nm, in the case that AEC-Q100 [15]
load tests HTOL, THB/HAST, and PTC have been performed on the basis of test boards with a
socket receptacle, it must be verified in particular that this component loading is comparable to
loading of soldered components on a circuit carrier. The use of generic data is permissible
6.4  


### 第 13 页
Page 13
VW 80808-2: 2017-01
Lifetime support requirements
Ramp-up assurance
Upon request, the contractor, in agreement with component manufacturer, must present a plan for
reliable start of production and provide suitable resources for implementing this plan. In particular,
this plan contains methods for reliable ramping up of volume production, delivery plans for sample
parts, production capacity predictions for the ramp-up, risk analyses and risk management, and in‐
volvement in defining the faulty part analysis flow.
Engineering change management
The contractor needs a documented process for communicating, releasing, and reliably introducing
changes during the production phase. The contractor must ensure that a corresponding process is
implemented at the component manufacturer's facility.
The contractor must inform the purchaser at least 150 days before the planned first delivery of the
changed product by means of a product change notification (PCN).
The contractor must not introduce the change without the approval of the purchaser (basic appro‐
val obligation).
The product change notifications (PCNs) must be classified and documented according to the cat‐
egories specified in the ZVEI "Guideline for Customer Notifications of Product and/or Process
Changes (PCN) of Electronic Components specified for Automotive Applications" [19].
Furthermore, the requirements of the JESD46D standard [20] apply.
A change in the semiconductor technology (technology node/feature size) used for a product must
not be considered as a change, but as replacement of the product with a new product, with the
result that the specifications of section 6.5.4 apply to the existing product.
Fault analysis
See VW 80808-1 ("Fault analysis" section)
Termination
The contractor must ensure that the component manufacturer gives it sufficient notice of product
terminations (product termination notification – PTN) and offers a last-time buy (LTB) opportunity
as per specifications below.
Last order: The contractor must ensure that the component manufacturer gives it the opportunity to
place a last order within a time period of at least 12 months after announcement of the termination.
Last delivery: The contractor must ensure that the component manufacturer allows delivery of the
ordered product for at least 6 additional months after conclusion of the order period (at least
18 months after announcement of the product termination).
Furthermore, standard JESD48C [21] must be complied with.
Supply assurance and storage of components
The contractor must ensure supply assurance on the part of the semiconductor manufacturer in the
form of a business continuity plan (BCP).
6.5  
6.5.1  
6.5.2  
6.5.3  
6.5.4  
6.5.5  


### 第 14 页
Page 14
VW 80808-2: 2017-01
On request, the contractor must collaborate with the component manufacturer to show the impact
of potential interference within a risk assessment and cover this with suitable actions in the BCP.
The stock level must be designed and maintained during production such that the supply of genu‐
ine parts is ensured for a period of at least 12 weeks.
Deliveries to on-going series production of components from stock that do not originate directly
from the component manufacturer, and for which a storage period of 12 months has been excee‐
ded, must be reported.
In case of end-of-production inventory manufacturing, the last delivery date, and the forecast cov‐
erage for the vehicle projects in question, including a worst-case risk analysis must be indicated
prior to the last order date.
In case of end-of-production inventory manufacturing, storing of non-tested goods at wafer level is
only permissible if a required delivery coverage of 6 months is guaranteed by means of an appro‐
priate number of OK tested dies, assuming typical reject rates in assembly and final testing.
A retroactive change of the assembly or test location must be appropriately requalified.
The storage solution, and the strategy for ensuring supply assurance for the stocks in the period
between start and end of production, must be presented on request.
Component chip technologies
Bare-die applications
Bare-die components must be qualified analogously to AEC-Q100 [15] and AEC-Q101 [16]. In par‐
ticular, qualification must also take into account interactions between the assembly and the bare
die. The basic strategy and the qualification tests and criteria must be submitted to the purchaser
on request.
Active semiconductor components
Microcontroller
Embedded flash for microcontrollers
The timing and the execution of the programming and erasure routines of the flash module must be
controlled by structures within the chip. Programming voltages must be generated on the chip. The
objective is low sensitivity to variance in software, external voltage, and frequency during program‐
ming.
Embedded flash devices must have ECC error correction. In the final electrical tests the IC, the
ECC error correction must be switched off temporarily to select bit-flips that occur.
The test must be designed in such a way that abnormal bits with a significantly lower life expectan‐
cy are detected. Transient errors during program execution must be taken into account in the test.
For components that contain embedded flash, it must be possible to read out a threshold voltage
distribution (e.g., in the form of a frequency distribution) of the written memory on the finished com‐
ponent for analysis purposes.
7  
7.1  
8  
8.1  
8.1.1  


### 第 15 页
Page 15
VW 80808-2: 2017-01
Diodes (excluding LEDs)
See VW 80808-1 ("Diodes (excluding LEDs)" section)
Light emitting diodes (LEDs)
Light emitting diodes with a permissible maximum forward current in forward-bias direction greater
than or equal to 100 mA must be qualified as per IEC 60810. The qualification report must be sub‐
mitted to the purchaser on request.
Corrosion is not permissible at any position of the LEDs. This also applies to the H2S corrosive gas
test as per IEC 60810.
The degradation from forward voltage, or luminous flux/luminous intensity, due to operation at
maximum Tj and nominal current must be tested for a time of at least 10 000 h. The aging behavior
must be determined with a confidence interval of 90%. The corresponding boundary conditions
(test duration, number of samples) and data must be presented to the purchaser on request.
The aging life Bage of an LED is defined as the time the luminous flux or luminous intensity drops to
below 70% of the original luminous flux for an LED that has not failed (degradation, measured at
room temperature TRT = 23 °C ±5 °C). The aging life Bage as per L70/B10 may occur after 8 000 h
of operation at the earliest.
An ESD protective diode built into the LED must be designed to match the component service life.
Color drift: The color drift for the entire service life must be shown graphically.
The color drift across the beam angle must be shown graphically.
It must be possible to process soldering-capable LEDs using a reflow soldering process (lead-free)
as per J-STD-020D [24]; pre-treatment as per MSL level 4 as a minimum. Soldered LEDs are ex‐
clusively approved.
The contractor must observe the soldering profile recommended by the LED manufacturer. If devi‐
ations are necessary, they must be agreed upon with the LED manufacturer and evaluated in col‐
laboration with the purchaser.
The measuring procedures used, including the measuring equipment for all tests, must be dis‐
closed to the purchaser on request.
Passive components
Resistors
See VW 80808-1 ("Resistors" section)
Capacitors
Multilayer ceramic capacitors (MLCCs)
Component requirements
See VW 80808-1 ("Component requirements" section)
8.2  
8.3  
9  
9.1  
9.2  
9.2.1  
*******  


### 第 16 页
Page 16
VW 80808-2: 2017-01
Qualification requirements
See VW 80808-1 ("Qualification requirements" section)
Application requirements
See VW 80808-1 ("Application requirements" section)
Tantalum capacitors
See VW 80808-1 ("Tantalum capacitors" section)
Electrolytic capacitors
See VW 80808-1 ("Electrolytic capacitors" section)
Varistors
See VW 80808-1 ("Varistors" section)
Circuit carriers
Basic requirements for organic circuit carriers
Rigid PCBs
PCB component requirements
See VW 80808-1 ("PCB component requirements" section)
Requirements for the PCB production process
See VW 80808-1 ("Requirements for the PCB production process" section)
PCB application requirements
See VW 80808-1 ("PCB application requirements" section)
Flexible printed circuits (flexible film – FPC)
See VW 80808-1 ("Flexible printed circuits (flexible film – FPC)" section)
Requirements for the FPC component
See VW 80808-1 ("Requirements for the FPC component" section)
Requirements for the FPC production process
See VW 80808-1 ("Requirements for the FPC production process" section)
FPC application requirements
See VW 80808-1 ("FPC application requirements" section)
*******  
*******  
9.2.2  
9.2.3  
9.3  
10  
10.1  
10.2  
10.2.1  
10.2.2  
10.2.3  
10.3  
10.3.1  
10.3.2  
10.3.3  


### 第 17 页
Page 17
VW 80808-2: 2017-01
Rigid flexible PCBs (Starrflex)
See VW 80808-1 ("Rigid flexible PCBs (Starrflex)" section)
Semi-flexible PCBs (Semiflex)
See VW 80808-1 ("Semi-flexible PCBs (Semiflex)" section)
3D-MID
See VW 80808-1 ("3D-MID" section)
PCBs with embedded components
See VW 80808-1 ("PCBs with embedded components" section)
Assemblies
See VW 80808-1 ("Assemblies" section)
Product requirements
See VW 80808-1 ("Product requirements" section)
Requirements for the manufacturing process
Soldering technology
See VW 80808-1 ("Soldering technology" section)
Wire bonding technologies
See VW 80808-1 ("Wire-bonding technologies" section)
Press-fit technology
See VW 80808-1 ("Press-fit technology" section)
Rework and repair
See VW 80808-1 ("Rework and repair" section)
Depaneling of assemblies
See VW 80808-1 ("Depaneling of assemblies" section)
Cleanliness of assemblies
See VW 80808-1 ("Cleanliness of assemblies" section)
Coating (gel, protective coating, potting) of assemblies
See VW 80808-1 ("Coating (gel, protective coating, potting) of assemblies" section)
10.4  
10.5  
10.6  
10.7  
11  
11.1  
11.2  
11.2.1  
11.2.2  
11.2.3  
11.2.4  
11.2.5  
11.2.6  
11.2.7  


### 第 18 页
Page 18
VW 80808-2: 2017-01
Use of underfiller
See VW 80808-1 ("Use of underfiller" section)
Power modules
See VW 80808-1 ("Power modules" section)
Applicable documents
The following documents cited in this standard are necessary to its application.
Some of the cited documents are translations from the German original. The translations of Ger‐
man terms in such documents may differ from those used in this standard, resulting in terminologi‐
cal inconsistency.
Standards whose titles are given in German may be available only in German. Editions in other
languages may be available from the institution issuing the standard.
VW 80000
Electric and Electronic Components in Motor Vehicles up to 3,5 t - Gen‐
eral Component Requirements, Test Conditions and Tests
VW 80808-1
Electronic Components and Assemblies in Electrical and Electronic
Parts in Motor Vehicles up to 3.5 t; Requirements and Use - Part 1
VW 82324
Qualification of Power Electronics Modules for Use in Motor Vehicle
Components; General Requirements, Test Conditions and Tests
IATF 16949
Requirements for Quality Management Systems for Series Production
and Spare Parts Production in the Automotive Industry
IEC 60810
Lamps for road vehicles - Performance requirements
ISO 9001
Quality management systems - Requirements
VDA volume 2
Quality Management in the Automotive Industry - Quality Assurance for
Supplies - Production Process and Product Approval (PPA)
VDA Volume 6 Part 1
Quality Management in the Automotive Industry - Part 1: QM System
Audit; based on DIN EN ISO 9001 and DIN EN ISO 9004-1
VDA Volume 6 Part 3
Quality Management in the Automotive Industry - Process Audit - Prod‐
uct Emergence Process, Series Production, Service Development Proc‐
ess, Providing the Service
Bibliography
[1]
JEP122G "Failure Mechanisms and Models for Semiconductor Devices"; available at
http://www.jedec.org/standards-documents/results/122G
[2]
JESD89A "Measurement and Reporting of Alpha Particle and Terrestrial Cosmic Ray-
Induced Soft Errors in Semiconductor Devices"; available at http://www.jedec.org/stand‐
ards-documents/results/JESD89A
[3]
JESD89-1A "Test Method for Real-Time Soft Error Rate"; available at http://www.je‐
dec.org/standards-documents/results/JESD89-1
********  
11.3  
12  
13  


### 第 19 页
Page 19
VW 80808-2: 2017-01
[4]
JESD89-2A "Test Method for Real-Time Soft Error Rate"; available at http://www.je‐
dec.org/standards-documents/results/JESD89-2
[5]
JESD89-3A "Test Method for Beam Accelerated Soft Error Rate"; available at
http://www.jedec.org/standards-documents/results/JESD89-3
[6]
JP001A "Foundry Process Qualification Guidelines"; available at http://www.je‐
dec.org/standards-documents/results/JP001
[7]
JEP156 "Chip-Package Interaction Understanding, Identification and Evaluation"; availa‐
ble at http://www.jedec.org/standards-documents/results/JEP156
[8]
JEP150.01 "Stress-Test-Driven Qualification of and Failure Mechanisms Associated with
Assembled Solid State Surface-Mount Components"; available at http://www.je‐
dec.org/standards-documents/results/JEP150
[9]
AEC-Q003 "Guidelines for Characterizing the Electrical Performance of Integrated Cir‐
cuit Products"; available at http://www.aecouncil.com/AECDocuments.html
[10]
AEC-Q001 "Guidelines for Part Average Testing"; available at http://www.aecoun‐
cil.com/AECDocuments.html
[11]
AEC-Q002 "Guidelines for Statistical Yield Analysis"; available at http://www.aecoun‐
cil.com/AECDocuments.html
[12]
AIAG "Advanced Product Quality Planning and Control Plan"; available at
http://www.aiag.org/store/publications/details?ProductCode=APQP
[13]
AIAG "Production Part Approval Process"; available at http://www.aiag.org/store/publica‐
tions/details?ProductCode=PPAP-4
[14]
AIAG "Statistical Process Control"; available at http://www.aiag.org/source/Orders/prod‐
Detail.cfm?productDetail=SPC-3
[15]
AEC-Q100 "Stress Qualification For Integrated Circuits"; available at http://www.aecoun‐
cil.com/AECDocuments.html
[16]
AEC-Q101 "Stress Test Qualification For Discrete Semiconductors"; available at
http://www.aecouncil.com/AECDocuments.html
[17]
AEC-Q200 "Stress Test Qualification For Passive Components"; available at
http://www.aecouncil.com/AECDocuments.html
[18]
J-STD-035 "Joint IPC/JEDEC Standard for Acoustic Microscopy for Nonhermetic Encap‐
sulated Electronic Components"; available at http://www.jedec.org/standards-docu‐
ments/results/J-STD-035
[19]
"ZVEI Guideline for Customer Notifications of Product and/or Process Changes (PCN) of
Electronic Components specified for Automotive Applications"; available at
http://www.zvei.org/Verband/Publikationen/Seiten/default.aspx?k=ZVEI%20Guideline
%20for%20Customer%20Notifications%20PCN
[20]
JESD46D "Customer Notification of Product/ Process Changes by Solid-State Suppli‐
ers"; available at http://www.jedec.org/standards-documents/results/JESD46
[21]
JESD48C "Product Discontinuance"; available at http://www.jedec.org/standards-docu‐
ments/results/JESD48
[22]
AEC-Q004 "Zero Defects Guideline"; available at http://www.aecouncil.com/AECDocu‐
ments.html


### 第 20 页
Page 20
VW 80808-2: 2017-01
[23]
JESD85 "Methods for Calculating Failure Rates in Units of Fits"; available at
http://www.jedec.org/standards-documents/docs/jesd-85
[24]
J-STD-020D "Moisture/Reflow Sensitivity Classification for Nonhermetic Solid State Sur‐
face Mount Devices"; available at https://www.adestotech.com/wp-content/up‐
loads/jstd020d-01.pdf


### 第 21 页
Page 21
VW 80808-2: 2017-01
Qualification of MLCCs with soft termination
For the procedure and measurements accompanying testing, see table A.1 and table A.2.
Table A.1 – Qualification procedure for MLCCs with flexible termination
 
100 pieces per structural form in its
unfavorable geometry
Test
Explanation
1
Wave soldering at 280 °C, no pre‐
heating
 
Worst-case stress by means of sol‐
dering method
 
 
 
2
Bend test based on
AEC-Q200 [17] (board flex)
Analysis after bend test
Simulation of stress caused by man‐
ufacturing process
 
– Displacement amplitude of 5 mm
– Test speed: 1 mm/s
After the test, 10 specimens are ex‐
amined by means of microsections.
3
1 000 temperature cycles from
-55 °C to +125 °C
Measurements accompanying test‐
ing:
– Capacitance
– Dielectric loss factor
– Insulation resistance
Simulation of stress caused by man‐
ufacturing process
 
Aging time for the respective key
temperatures: 30 min
Acclimatization time: 3 min
Evaluation of min., max., mean,
% deviation (including standard devi‐
ation and Cpk)
3
Constant humidity/heat test
(85 °C/85% RH)
Measurements accompanying test‐
ing:
– Capacitance
– Dielectric loss factor
– Insulation resistance
Acceleration of failure mechanisms
 
Test voltage: 2 × RV
Duration: 1 000 h
Evaluation of min., max., mean,
% deviation (including standard devi‐
ation and Cpk)
4
Bend test based on
AEC-Q200 [17] (board flex)
Analysis after bend test
Simulation of stress caused by work‐
shop/repair
 
– Displacement amplitude of 5 mm
– Test speed: 1 mm/s
After the test, 10 specimens are ex‐
amined by means of microsections.
5
Constant humidity/heat test
(85 °C/85% RH)
Measurements accompanying test‐
ing:
– Capacitance
– Dielectric loss factor
– Insulation resistance
Acceleration of failure mechanisms
 
Test voltage: 2 × RV
Duration: 24 h
Evaluation of min., max., mean,
% deviation (including standard devi‐
ation and Cpk)
Appendix A (normative)  


### 第 22 页
Page 22
VW 80808-2: 2017-01
Table A.2 – Measurements accompanying testing for MLCCs
Parameters/measuring methods
Evaluation criteria
Capacitance
Measurement at Vrms = 1 V and 1 kHz
Evaluation of min., max., mean, % deviation (including
standard deviation and Cpk)
All parts must lie within their data sheet tolerances.
Dielectric loss factor
Measurement at Vrms = 1 V and 1 kHz
Evaluation of min., max., mean, % deviation (including
standard deviation and Cpk)
All parts must lie below the maximum dielectric factor speci‐
fied in the data sheet.
Insulation resistance
Measurement at NV (DC nominal voltage)
Evaluation of min., max., mean, % deviation (including
standard deviation and Cpk)
All parts must lie below the minimum specification of
1 GΩ/µF, but they must not exceed 100 GΩ.
The contractor must present the detailed qualification results to the purchaser before the electronic
components are used.
The documentation must include a minimum of the following contents:
–
General description of the component, component designation (meaning)
–
Table with design information (size, NV, dielectric thickness, casing thickness, number of lay‐
ers, total thickness)
–
Table of the tested parts that have passed the test (e.g., NV and structural form)
–
Individual presentation of the tested parts with the evaluation criteria, limits, measured values,
and microsections (detail "peel-off zone")


### 第 23 页
Page 23
VW 80808-2: 2017-01
Verifying the service life of electrolytic capacitors
The service life is verified on the basis of a temperature/load spectrum for typical, pertinent operat‐
ing points of the part while taking into account the associated operating parameters.
Using the manufacturer-specific estimation method, the service life theoretically achievable under
the specific operating condition is first estimated for each assumed individual temperature level. In
comparison with the operating time expected at this temperature, the proportion of service life "uti‐
lized" is then determined relatively from the nominal service life. These proportions are summed
over all individual temperature levels (for details, see also table B.1 below).
Table B.1 – Service life verification for electrolytic capacitors
Nominal capacitor data
Manufacturer
 
Nominal capacitance in μF
 
Order number
 
Nominal voltage in V
 
 
Operating voltage Vop in V
 
Nominal ripple current in mA
 
Nominal temperature in °C
 
Nominal service life LN in h
 
Calculation formula used
Index
Temperature
in °C
Operating time
(collective)
in h
Ripple current
in mA
Service life
(theoretical)
in h
... proportionate
utilization thereof
in h
1
T1
t1
ΔI1
Lth_1
Lu_1
2
T2
t2
ΔI2
Lth_2
Lu_2
...
...
...
...
...
...
i
Ti
ti
ΔIi
Lth_i
Lu_i
...
...
...
...
...
...
n
Tn
tn
ΔIn
Lth_n
Lu_n
 
Σti
 
ΣLu_i
Legend
i
Consecutive index of the examined temperature level
Ti
Temperature of the examined temperature level
ti
Dwell time within Ti over the total running time
ΔIi
Ripple current load at Ti
Lth_i
Theoretically achievable service life at Ti, ΔIi, if applicable Vop, ...
Lth_i = f(ti, ΔIi, Vop, ...)
Calculation as per manufacturer's specifications
Lu_i
Proportionately utilized service life at Ti
Lu_i = LN × ti / Lth_i
Σti is identical to the total operating time of the component. ΣLu_i must remain less than LN.
Appendix B (normative)  


### 第 24 页
Page 24
VW 80808-2: 2017-01
Minimum Requirements for Parametric Tests (Pre- and Post-Stress)
during Product-Characterization
See table C.1.
Table C.1
MOSFET  
 
 
Comments/accord‐
ing to data sheet
conditions
 
BVDSS
V
Drain-source breakdown voltage
 
 
IDSS
nA
Drain-source leakage current
 
 
IGSS
nA
Gate-source leakage current
 
 
RDS(ON)
Ohm
Drain-source on-state resistance
 
 
VGS(th)
V
Gate-source threshold voltage
 
 
(Gfs)
A/V
Transconductance
only if relevant
 
(Rth)
K/W
Thermal resistance
only if relevant
 
 
 
 
 
IGBT
 
 
 
 
 
V(BR)CES
V
Collector-emitter breakdown voltage
 
 
ICES
nA
Zero gate voltage collector current
 
 
Itotal
nA
Gate-emitter leakage current
 
 
VCE(SAT)
V
Collector-emitter saturation voltage
 
 
(hfe)
 
Common emitter current gain
only if relevant
 
VGE(th)
V
Gate-emitter threshold voltage
 
 
(Rth)
K/W
Thermal resistance
only if relevant
 
 
 
 
 
Bipolar
 
 
 
 
 
V(BR)CEX
V
Collector-emitter breakdown voltage
 
 
ICEX
A
Collector-emitter cut-off current
 
 
IEBX
A
Emitter-base cut-off current
 
 
VCE(SAT)
V
Collector-emitter saturation voltage
 
 
hFE
 
Common emitter current gain
 
 
 
 
 
 
Diodes
 
 
 
 
 
VR
V
Reverse voltage
 
 
IF
A
Forward current
 
 
IR
µA
Reverse current
 
 
 
 
 
 
LEDs
 
 
 
 
 
IF
A
Forward current
 
 
VF
V
Forward voltage
 
Appendix C (normative)  


### 第 25 页
Page 25
VW 80808-2: 2017-01
 
(IR)
nA
Reverse leakage current
only if relevant
 
(VR)
V
Reverse blocking voltage
only if relevant
 
λpeak
nm
Wavelength
 
 
λdom
nm
Dominant wavelength
 
 
Δλpeak
nm
Peak wavelength
 
 
(Cx, Cy)
 
Chromaticity coordinates
only for converted
devices
 
ФV(T)
lm
Luminous flux
Evaluation points to
be defined
 
lV
cd
Luminous intensity
 
 
Rth
K/W
Thermal resistance
 
 
 
 
 
 
Laser diodes
 
 
 
 
λ
nm
Wavelength
 
 
Δλ
nm
Spectral width (FWHM)
 
 
Pout
W
Optical output power
 
 
IF
A
Forward current
 
 
VF
V
Forward voltage
 
 
ITH
A
Threshold current
 
 
 
 
 
 
Photo diodes
 
 
 
 
ID
nA
Dark current
 
 
VBR
V
Breakdown voltage
 
 
RMIN
A/W
 
 

