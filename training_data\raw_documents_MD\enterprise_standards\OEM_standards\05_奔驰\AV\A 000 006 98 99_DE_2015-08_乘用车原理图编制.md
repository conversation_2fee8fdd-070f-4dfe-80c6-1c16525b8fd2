# A 000 006 98 99_DE_2015-08_乘用车原理图编制.pdf

## 文档信息
- 标题：Lastenheft zur Erstellung
- 作者：wadresc
- 页数：50

## 文档内容
### 第 1 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 1von 50 
 
 
Ausführungsvorschrift  
 
 
Erstellung  
von Schaltplänen  
für Mercedes-Benz PKW  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Version 7.0 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 2 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 2von 50 
 
 
Freigabe 
 
Fachbereich/Funktion 
Freigabe 
Name 
Datum 
Unterschrift 
Ersteller Lastenheft Schaltplan 
Daimler AG 
Redinger 
2010-07-28 
 
Fachbereich E3 
Daimler AG 
Dauner 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 3 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 3von 50 
 
Änderungsindex / Änderungsbeschreibung 
 
Änderungsdokumentation des Lebenslaufes Ausführungsvorschrift Schaltplanerstellung 
ZGS Beschreibung 
Bearbeitet 
(Datum) 
Genehmigt (Datum) 
Daimler AG 
Daimler AG 
001 Neuerstellung 
2007-08-10 
Drescher 
 
002 
- LCable entfernt 
- Anpassungen an E3.Cable 
- Datenversorgung aktualisiert 
- Schaltplanlayout zugefügt 
2009-11-20 
Redinger  
 
003 
- 
Kontaktform  von Schneid- Klemmverbinder berichtigt und 
Kontaktform für offene Kabelende zugefügt 
- 
Steckernamen und Steckerformen im Kapitel Referenzen  
aktualisiert und erweitert 
- 
Umbenamung des Kapitel Kontaktform in Steckerform  
- 
Kapitel  „Datenbereitstellung zu MBC“  das PDF- Projekt zugefügt  
- 
Kapitel „Änderungsmanagement / Versionierung“ und 
„Datenstand“ entfallen 
- 
Kapitel Zeichnungsbenennung umbenannt in Zeichnungskopf, 
engl. Benennung entfernt, Beschreibung Zeichnungskopf 
erweitert 
- 
Kapitel „Projektstrukturen“ um andere Bereiche erweitert 
- 
Im Kapitel „Leitungsnummern“ die  Cadds- Spezifika entfernt 
- 
Im Kapitel „Leitungsbenennung“  eine Detaillierung für 
Meterware und Sonderkabel zugefügt. 
- 
Im Kapitel „Bauteillegende“ wurde die Zugehörigkeit zur Baureihe 
zugefügt. 
- 
Im Kapitel „Optionale Attribute“ wurde der Verlegebereich 
entfernt und in neuem Kapitel beschrieben 
- 
Kapitel „Leitungscodierung“ um Seriencode erweitert 
- 
Im Kapitel „Sachnummer“ wurde das  freigaberelevante 
Sachnummernformat entfernt 
- 
Im Kapitel „Dateinamen Einzelschaltpläne“ und „Dateinamen 
Baureihenprojekte“ die Formate angepasst und das Datum 
berichtigt. 
 
2010-07-28 
Redinger 
 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 4 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 4von 50 
 
004 
Anpassungen in den Kapiteln: 
- Dateinamen Schaltplanexporte  
- Sachnummernformat 
- Zeichnungskopf  
- Bearbeitungsdatum und Datenstand  
- Referenzen von Trennstellen und Kabelschwanzkomponenten 
- Klemmen und Signalbezeichnung 
- Benennung der Baureihe, Ausführungsart und  Fahrzeugphase 
- Optionale Attribute 
- Datenbereitstellung zu MBC 
- Schaltplan- ELOG- Datei 
 
Neue Kapitel: 
- Externes Änderungsfeld  
- Generische Benennung der Sonderkabel   
- Pin- Festlegungen 
- Slotdarstellung  
- Nur zur Information 
- Verwendung in der Bauteillegende 
- Benennung der Baureihe, Ausführungsart und Fahrzeugphase 
- Datenbereitstellung Schaltplancontainer 
- Dateinamen Schaltplancontainer 
- Sicherungsdarstellung 
 
 
 
2012-02-17 
Redinger 
 
005 
Anpassungen in den Kapiteln: 
- 
Referenzen für SRS- Prüfung erweitert 
- 
Kapitel Steckerbezeichnung hieß Steckerform 
- 
Projektstrukturen erweitert 
- 
Klemmen- und Signalbezeichnung 
- 
Datenbereitstellung von MBC 
- 
Pin- Bezeichnung Kontaktgehäuse 
 
Neue Kapitel: 
- 
Pneumatische Leitungen 
- 
Nicht angeschlagene Schirme 
- 
Schirm an Kontaktgehäuse 
- 
Kontaktform 
- 
Ausführungsarten 
- 
Dateinamen Gesamtschaltplan 
- 
Schaltplanumfänge von Kleinlieferanten in einem Projekt 
2013-07-16 
Redinger 
 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 5 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 5von 50 
 
006 
Anpassungen in den Kapiteln: 
- 
Projektstrukturen 
- 
Referenzen 
- 
Steckernamen 
- 
Steckerbezeichnungen 
- 
Pinbezeichnungen am Kontaktgehäuse 
- 
„Nicht angeschlagenen Schirme“ umbenamt in „Nicht 
kontaktierte Schirmung“ 
- 
Datenbereitstellung von MBC 
- 
Schalplan ELOG-Datei 
- 
Abkürzungen und Begriffe 
 
Neue Kapitel: 
- 
Schlaufen 
- 
Kennzeichnung von 48V- Leitungen 
- 
iSPM (inclusive Unterkapitel) 
- 
Generischer Export 
2014-02-07 
Redinger 
 
007 
Anpassungen in den Kapiteln: 
- 
Nicht kontaktierte Schirmung 
- 
Schirmanschluss am Kontaktgehäuse  
- 
Bauteillegende: bilingualer Bezeichnung (DE/EN) 
- 
Festlegung der Syntax des Anlage- & Ortskennzeichen 
- 
Datenstand 
- 
Schaltplansachnummer 
- 
Schirmdarstellung anpassen 
- 
Datenbereitstellung an MBC 
 
Entfernen der Kapitel: 
- 
ELOG Exports 
- 
Generische Benennung der Sonderkabel 
- 
Datencontainers 
- 
 
2015-08-28 
Redinger 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 6 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 6von 50 
 
Inhaltsverzeichnis 
 
Änderungsindex / Änderungsbeschreibung 
3 
Inhaltsverzeichnis 
6 
1 
Rahmenbedingungen 
8 
2 
Gegenstand dieses Lastenhefts 
8 
2.1 
Allgemeines .......................................................................................................................................................... 8 
2.2 
Ziele  ...................................................................................................................................................................... 8 
2.3 
Zuständigkeiten ................................................................................................................................................... 9 
2.4 
Prozesssicherheit ................................................................................................................................................. 9 
2.5 
Technologie .......................................................................................................................................................... 9 
3 
Schaltplaninhalte 
10 
3.1 
Zeichnungsrahmen und Zeichnungsschriftfeld ................................................................................................10 
3.2 
Zeichnungskopf ..................................................................................................................................................10 
3.3 
Nur zur Information ............................................................................................................................................11 
3.4 
Projektstrukturen ...............................................................................................................................................12 
3.5 
Externes Änderungsfeld ....................................................................................................................................12 
3.6 
Schaltplansichten ..............................................................................................................................................13 
3.7 
Übersichten (Belegungssichten) .......................................................................................................................13 
3.8 
Zeichnerische Darstellung .................................................................................................................................13 
3.9 
Layout der Schaltplanzeichnung .......................................................................................................................14 
3.10 Graphische Darstellung .....................................................................................................................................15 
3.11 Ausführungsarten...............................................................................................................................................16 
3.12 Referenzen .........................................................................................................................................................17 
3.13 Steckernamen ....................................................................................................................................................19 
3.14 Steckerbezeichnung ..........................................................................................................................................20 
3.15 Kontaktform .......................................................................................................................................................20 
3.16 Pin- Bezeichnung Kontaktgehäuse ...................................................................................................................20 
3.17 Sicherungsdarstellung .......................................................................................................................................21 
3.18 Leitungsbezeichnungen .....................................................................................................................................21 
3.18.1 Leitungsfarben ...........................................................................................................................................22 
3.18.2 Leitungsnummern ......................................................................................................................................22 
3.18.3 Leitungsbenennung ...................................................................................................................................23 
3.18.4 Kennzeichnung von 48V- Leitungen .........................................................................................................23 
3.19 Leitungskodierung .............................................................................................................................................23 
3.20 Kabeldarstellung ................................................................................................................................................24 
3.21 Klemmen und Signalbezeichnungen .................................................................................................................25 
3.22 Bauteillegende....................................................................................................................................................25 
3.22.1 Referenzen .................................................................................................................................................25 
3.22.2 Code ...........................................................................................................................................................26 
3.22.3 Verwendung ...............................................................................................................................................26 
3.23 Ebenenkonzept ..................................................................................................................................................26 
3.24 Slotdarstellung ...................................................................................................................................................26 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 7 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 7von 50 
 
3.25 Nicht kontaktierte Schirmung ...........................................................................................................................27 
3.26 Schirmanschluss am Kontaktgehäuse .............................................................................................................28 
3.27 Hydraulische und pneumatische Umfänge ......................................................................................................28 
3.28 Schlaufen ............................................................................................................................................................29 
3.29 Schaltplanumfänge von Kleinlieferanten in einem Projekt .............................................................................29 
4 
Attribute 
29 
4.1 
Optionale Attribute ............................................................................................................................................29 
4.1.1 
Systemgruppe ............................................................................................................................................30 
4.2 
Attributanzeiger .................................................................................................................................................31 
5 
iSPM 
32 
5.1 
Definition ............................................................................................................................................................32 
5.2 
Serienprozess .....................................................................................................................................................32 
5.3 
Inhalt ...................................................................................................................................................................33 
5.3.1 
Projektstruktur ...........................................................................................................................................34 
5.3.2 
Verwendung von Anlage- und Ortskennzeichen ......................................................................................37 
5.3.3 
Nicht definierte Pinnamen .........................................................................................................................37 
5.3.4 
Massen, Sicherungen, Stützpunkte ..........................................................................................................37 
5.3.5 
Leitungsauslegung .....................................................................................................................................38 
5.3.6 
Code ...........................................................................................................................................................38 
5.3.7 
Signale ........................................................................................................................................................38 
6 
Datenbereitstellung 
39 
6.1 
Datenbereitstellung zu MBC .............................................................................................................................39 
6.2 
Datenbereitstellung von MBC ...........................................................................................................................41 
6.3 
Datenbereitstellung für Modulverkabelung ......................................................................................................41 
7 
Datenformate und Namenskonventionen 
42 
7.1 
Datenformate .....................................................................................................................................................42 
7.2 
Namenskonventionen ........................................................................................................................................42 
7.2.1 
Benennung der Baureihe, Ausführungsart und Fahrzeugphase ..............................................................42 
7.2.2 
Sachnummernformate ...............................................................................................................................43 
7.2.3 
Dateinamen Einzelschaltpläne ..................................................................................................................44 
7.2.4 
Dateinamen Baureihenprojekte ................................................................................................................45 
7.2.5 
Dateinamen Gesamtschaltplan .................................................................................................................45 
7.2.6 
Dateinamen Bibliotheken und  Softwareerweiterungen ..........................................................................45 
7.3 
Generischer Export ............................................................................................................................................46 
8 
Mitgeltende Unterlagen 
47 
8.1 
Normen und Vorschriften ..................................................................................................................................47 
8.2 
Abkürzungen und Begriffe .................................................................................................................................47 
8.3 
Abbildungsverzeichnis .......................................................................................................................................49 
9 
Anhang 
50 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 8 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 8von 50 
 
1 Rahmenbedingungen 
Dieses Lastenheft enthält die von der Daimler AG, Bereich MBC bzw. deren Nachfolgeorganisationen festgelegten 
Anforderungen an die Schaltplanerstellung. Es ist die verbindliche Vorgabe zur Erstellung von Schaltplänen für die  
Leitungssatzentwicklung für alle Fahrzeugbaureihen des Bereiches MBC. 
 
Der hier beschriebene Inhalt beschreibt die inhaltlichen und methodischen Anforderungen von MBC an einen 
Schaltplan für die Leitungssatzentwicklung. 
 
Dieses Lastenheft  beschreibt keine technische Inhalte sowie Sachverhalte welche durch Lastenhefte, Normen und 
Verfahrensanweisungen der MBC geregelt sind. Der Lieferant ist verpflichtet sämtliche Ausführungsvorschriften 
und Normen einzuhalten und die Schaltpläne stets dem aktuellen Stand anzupassen. 
 
Sind für eine einwandfreie Dokumentation erforderliche Randbedingungen in diesem Lastenheft nicht oder 
abweichend beschrieben, ist dies der MBC anzuzeigen.  
 
Alle Abweichungen von den Anforderungen dieses Lastenhefts bedürfen der schriftlichen Zustimmung der MBC. 
 
2 Gegenstand dieses Lastenhefts 
Gegenstand des Lastenhefts ist die Verwendung, den Inhalt und die Ausführung eines Schaltplans zur 
Produktentwicklung, dessen Inhaltsbeschreibung sowie die Ausführung einer digitalen ELOG- und PDF- Datei als 
Produktbeschreibung  sowie Anforderungen an die Prozesssicherheit in der Leitungssatzentwicklung zu 
beschreiben und verbindlich festzulegen. 
 
2.1 Allgemeines 
Die Schaltpläne und Schaltplanoriginaldaten sind Eigentum der MBC und für den internen Gebrauch entsprechend 
MBN 31 002 auszuführen. Zeichnungsrahmen und Schriftköpfe, Bibliotheken, Template – Dateien für Schriften und 
Ebenen, Bauteil-, Referenzen-, Code- und Leitungsdaten sowie die  notwendigen Zusatzfunktionen der Software 
werden von RD/EKL zur Verfügung gestellt und müssen verbindlich verwendet werden. Als Schaltplaneditor ist 
E3.Cable, nachfolgend auch Schaltplaneditor genannt, mit dem Plugin ConnyE zum Datenaustausch mit CONNECT 
zu verwenden. 
 
Die Inhalte der Schaltpläne sind im Kapitel 3 Änderungsindex / Änderungsbeschreibung beschrieben. 
Firmenspezifische Informationen sind nur in den vereinbarten und abgestimmten Attributen gestattet oder vor der 
Bereitstellung zur MBC zu entfernen. 
 
2.2 Ziele 
Ziel dieses Lastenhefts ist es Inhalte und Form der Entwicklungsschaltpläne für die Leitungssatzentwicklung 
festzulegen, die wesentlichen Inhalte zu beschreiben sowie ein einheitliches Erscheinungsbild der Schaltpläne 
unter Einhaltung der Dokumentationsrichtlinien der MBC sicherzustellen. 
 
Ein weiteres Ziel ist die Austauschbarkeit der Entwicklungsergebnisse innerhalb der Entwicklung sowie zwischen 
Montage, After Sales und externen Entwicklungspartnern sicherzustellen. 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 9 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 9von 50 
 
2.3 Zuständigkeiten  
Zuständig für die Ausführung der Schaltpläne und deren technische Inhalte ist innerhalb der MBC der 
beauftragende Bereich, im Bereich RD/EK Leitungssätze die Abteilung RD/EKL, RD/EKW, im Bereich Powertrain 
die Abteilung RD/PEW, im Bereich Hochvolt die Abteilung RD/EKS und bei AMG die Abteilung AMG/EBSE und 
Motorenabteilung AMG/ET14 sowie deren Nachfolgeorganisationen. 
 
2.4 Prozesssicherheit 
Schaltplannetzlisten bilden die logische Vorgabe für den Leitungssatzentwicklungsprozess und bestimmen die 
elektrischen Verbindungen des Leitungssatzes. 
 
Die Netzlisten müssen automatisch aus den Schaltplänen generiert und für den weiteren Entwicklungsprozess 
verwendet werden. 
 
Um eine eindeutige Zuordnung der Schaltpläne im System zu den daraus generierten Netzlisten und graphischen 
Dateien zu gewährleisten, müssen diese einer eindeutigen Namenskonvention und definierten Datenformaten 
entsprechen (siehe auch Kapitel 7 Datenformate und Namenskonventionen). 
 
Zur Gewährleistung der Prozesssicherheit werden die Lieferanten vor dem Ersteinsatz der Schaltplanwerkzeuge 
einem Review von RD/EKL unterzogen.   
 
2.5 Technologie 
Der Einsatz der Schaltplanwerkzeuge ist im Normalfall durch den Lieferantenentwicklungsvertrag geregelt, 
Abweichungen sind nur in Absprache mit dem zuständigen Leitungssatz- Baureihenteam möglich. 
 
Die einzusetzenden Softwareversionen, Softwareerweiterungen, Bibliotheken und Template müssen einen von MBC 
zertifiziert und freigegebenen Softwarestand haben. Die einzusetzende Software/ Versionen sowie der 
Einsatztermin, sind mit dem Team „Leitungssatz DMU und Tools“ der Abteilung RD/EKL abzustimmen. 
 
Erforderliche Funktionserweiterungen für die von RD/EKL freigegebenen Schaltplaneditoren werden über den 
zuständigen Cabling-Betrieb zur Verfügung gestellt. 
 
Die zur Verfügung gestellten Bibliotheken sind im Kapitel 7.2.6 Dateinamen Bibliotheken und  
Softwareerweiterungen zusammenfassend aufgeführt. 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 10 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 10von 50 
 
3 Schaltplaninhalte 
3.1 Zeichnungsrahmen und Zeichnungsschriftfeld 
Es sind die MBC Standard-Zeichnungsformulare, Zeichnungsrahmen und Zeichnungsschriftfeld nach den Regeln 
der MBN 31 020 zu verwenden. Eine Auswahl entsprechender Zeichnungsrahmen und Zeichnungsschriftfelder  
werden für die freigegebenen Schaltplaneditoren von RD/EKL über den zuständigen Cabling-Betreiber zur 
Verfügung gestellt.  
3.2 Zeichnungskopf  
Benennung 
Die Benennung des Schaltplans muss den Inhalt des System- oder Funktionsschaltplans beschreiben in Deutsch 
und Englisch. 
 
Beispiel:  
 
 
 
Schaltplan SAM/SRB hinten 
 
 
 
wiring diagram SAM /SRB back 
 
Schaltplannummer 
Jeder Schaltplan muss eine Schaltplansachnummer erhalten (siehe Kapitel 7.2.2 Sachnummernformate). Die 
Blattnummer darf hier nicht aufgeführt werden.  
 
Blattnummer 
Die Blattnummer muss ausgefüllt und ungleich 0 sein. 
 
Bearbeiter 
In den beiden Bearbeitungsfeldern ist das Datum und beim Namen das Firmenkürzel und Bearbeiter Kürzel 
einzutragen.  
 
Datum 
Jedes Datum auf dem Zeichnungskopf ist in der Form YYYY-MM—DD (Jahr-Monat-Tag) einzutragen. Hier ein 
Beispiel: 2010-07-20.  
 
Datenstand 
Beim Datenstand ist das Datum einzutragen zu welchem Tag die Änderungen vorgenommen wurden. Dieses Datum 
ist auch der Datenstand des Schaltplan auf der Leitungssatzzeichnung und in der Leitungssatz- KBL. Der 
Datenstand ist nur auf Blättern der Schaltpläne zu ändern, auf denen eine Änderung sattgefunden hat.  
  
Freigabeinfo: 
Bei den C1 / iSPM Projekten wird über dem Zeichnungskopf ein zusätzliches Attribut mit angezeigt von welchem 
C1 Projekt die Zeichnung stammt (work / public).  
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 11 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 11von 50 
 
 
 
 
Abbildung 1: Zeichnungskopf inkl. Hinweise 
 
3.3 Nur zur Information 
Alle Schaltpläne erhalten als Wasserzeichen in der Schaltplanlegende den Zusatztext „Nur zur Information Kein 
Änderungsdienst uncontrolled“. Damit soll verdeutlicht werden dass es sich um nicht freigegebene Dokumente 
handelt. 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 12 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 12von 50 
 
3.4 Projektstrukturen 
Die Baureihenprojekte sind analog der Vorgabe aus der iSPM- Struktur (siehe im iSPM- Kapitel:5.3.1 
Projektstruktur) aufzubauen. Die Hauptgruppe und Gruppe sind fest vorgegeben, die Untergruppen können nach 
Bedarf erweitert werden.  
 
Weitere Strukturstufen oder nicht benötigte Strukturen sind von den verwendeten Systemen abhängig und mit dem 
zuständigen Leitungssatz- Baureihenteam abzustimmen. Standardmäßig sollten aber aus Gründen der 
Übersichtlichkeit keine tieferen Unterebenen eingezogen werden und darunter die Schaltpläne stehen. 
 
Eine Ausnahme für das Zufügen einer Hauptgruppe zu einer Baureihe noch andere Bereiche/ Baureihen, wie z. B. 
AMG-Umfänge, mit dokumentiert werden müssen. Dieses macht Sinn wenn der allergrößte Teil der 
Schaltplanumfänge der Baureihe und von AMG identisch sind und nur wenige Schaltpläne sich unterscheiden. 
Dadurch kann man sicherstellen dass die Leitungsnummern in dem Projekt eindeutig sind und ohne 
Doppelbelegung in die Leitungssatzzeichnung überspielt werden können. Die sich unterscheidenden Schaltpläne 
für AMG werden unter das Verzeichnis AMG abgelegt. Es brauchen natürlich nur die Strukturen angelegt zu werden 
die auch Schaltpläne beinhalten. Es sind die gleichen Strukturierungsregeln gültig wie oben beschrieben.  
 
Benennung der Projektdateien: siehe Kapitel 7 Datenformate und Namenskonventionen. 
 
3.5 Externes Änderungsfeld 
Um eine lückenlose Änderungshistorie zu haben werden im externen Änderungsfeld formlos alle Änderungen für 
Zwischen- und Vorserienstände bzw. nicht freigegebene Schaltpläne beschrieben. Sollte es hierzu ein 
Änderungsvorhaben geben, sollte dieses gelistet werden.  
 
Das externe Änderungsfeld wird immer rechts neben dem Zeichnungskopf, außerhalb des Zeichnungsrahmens, 
platziert und besteht aus den Feldern Datum, Änderungstext und Bearbeiter. 
 
Zur Erstellung von Freigabezeichnungen wird dieses nicht offizielle Änderungsfeld vor dem Ausdruck abgeschnitten 
bzw. ausgeblendet. 
 
 
Abbildung 2: Externes Änderungsfeld 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 13 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 13von 50 
 
3.6 Schaltplansichten 
Wenn keine anders lautenden Absprachen mit dem zuständigen Leitungssatz-  Baureihenteam getroffen wurden 
sind die Schaltpläne als Systemschaltpläne anzulegen. Der Schaltplan soll in sich schlüssig sein und ein System 
vollständig beschreiben. 
 
Korrespondierende Informationen sind nach Möglichkeit ohne Signalquerverweise (Ports) auf andere 
Schaltplanblätter darzustellen. Sollten Signalquerverweise doch notwendig sein, ist die Vorgehensweise mit dem 
zuständigen Leitungssatz-  Baureihenteam abzustimmen bzw. zur Vermeidung dieser sind werkzeugspezifische 
Funktionalitäten wie z.B. Sichten, doppelte Pindarstellung  usw. zu verwenden. 
 
3.7 Übersichten (Belegungssichten) 
Zu den Themen Massen, CAN, Stützpunkte, Sicherungen und Trennstellen sind Übersichtspläne zu erstellen. 
Erforderliche Übersichten sind mit dem zuständigen Leitungssatz- Baureihenteam abzustimmen. Die Abstimmung 
mit dem Bereich GSP wird durch das zuständige Leitungssatz- Baureihenteam vorgenommen. 
 
 
3.8 Zeichnerische Darstellung 
Grundeinstellungen wie Layer, Grid, Textgröße, Textfont usw. für Schaltplan- und Datenbankeditor sind im E3-
Ebenenkonzept beschrieben und wird in einer  Template- Datei festgelegt. 
 
Die Projekteinstellungen (siehe Kapitel 7.2.6 Dateinamen Bibliotheken und  Softwareerweiterungen) werden als 
Datei im Austauschverzeichnis über den zuständigen Betreiber bereitgestellt. 
 
 
Beschriftung, Schriftarten und Schriftgrößen sind nach MBN 31 002 auszuführen. 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 14 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 14von 50 
 
3.9 Layout der Schaltplanzeichnung 
 
Abbildung 3: Schalplanlayout 
 
Das Schaltplanlayout teilt sich in drei Bereiche mit folgenden Inhalten: 
Oberer Bereich: 
 
Steuergeräte 
 
Systembestimmende Geräte 
Mittlerer Bereich: 
 
Trennstellen 
 
Stützpunkte 
 
Massestellen 
Unterer Bereich: 
 
Sensoren 
 
Aktoren 
 
Sicherungen 
 
Stützpunkte 
 
Massestellen 
 
Signalquerverweise (nur im Ausnahmefall benutzen) 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 15 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 15von 50 
 
Der logische Teil des Schaltplanes wird nach folgenden Gesichtspunkten erstellt: 
 
Die Leitungsdarstellung sollte möglichst kreuzungsfrei erfolgen.  
 
Ein Schaltplan kann entweder funktionsorientiert oder steuergeräteorientiert aufgebaut werden. Hierbei ist 
die Funktion komplett zu beschreiben, also auch inklusive Strom- und Masseversorgung und Busleitungen. 
 
Jeder Pin von Steuergeräten, Sensoren und Aktoren enthält eine lesbare Signal- oder Klemmenbezeichnung 
(Klemmenbezeichnung nach DIN 72552 ). 
 
Leitungen werden über maximal zwei Schaltpläne verschleift. 
 
Bei Sicherungen ist der Sicherungswert einzutragen. 
 
Signalquerverweise sind zu vermeiden da die Lesbarkeit des Schaltplanes stark darunter leidet.  
 
Die Innenverschaltung der Bauteile im Schaltplan wird nachvollziehbar dargestellt sein, eine 
Signalverfolgung muss weitestgehend gesichert sein. 
 
Steuergeräte benötigen keine ausführliche Darstellung der Innenverschaltung. Optional können auch 
externe Dokumente verlinkt werden. 
 
Bei Verbrauchern wird die Innenverschaltung dargestellt wenn dieses die Lesbarkeit des Schaltplanes 
verbessert. 
 
Verdrillte und geschirmte Leitungen sind als solche grafisch darzustellen. 
 
Zusammengehörige Pins eines Steckers sind mit einer Umrandung zu versehen um deren 
Zusammengehörigkeit darzustellen. 
 
Stecker sollten auf einem Schaltplan nicht mehrfach dargestellt werden. 
 
Die Inhalte des Schaltplanes und dessen Layout sind vor dem Projektstart mit dem beauftragenden Bereich in MBC 
abzustimmen und festzulegen. 
 
3.10 Graphische Darstellung 
Für die graphische Darstellung sind nur die Inhalte der von RD/EKL freigegebenen Bibliothek zu verwenden. 
In dieser Bibliothek werden Symbole, Blöcke, Teilschaltungen und Attributanzeiger entsprechend der in der 
Template- Datei vorgegebenen Festlegungen bereitgestellt. 
 
Die Attributierung der im Schaltplan verwendeten Bauteile und Schaltplaninhalte wie Legenden und 
Belegungssichten muss mit den Funktionen der ConnyE vorgenommen werden. 
 
Die Symbolbibliothek wird als Datenbankabzug im Austauschverzeichnis über den zuständigen Betreiber 
bereitgestellt. Die Namenskonvention der Bibliothek ist im Kapitel „7.2.6 Dateinamen Bibliotheken und  
Softwareerweiterungen“ beschrieben. 
 
Abbildung 4: Grafische Darstellung von Verdrillung und Schirmung 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 16 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 16von 50 
 
3.11 Ausführungsarten 
 
Abbildung 5: Grafische Unterscheidung von Ausführungsarten 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 17 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 17von 50 
 
Soll eine Fahrzeugfunktion, die in unterschiedlichen Ausführungsarten (siehe auch Kapitel 4.1.1 Systemgruppe) 
abweichend verschaltet wird, übersichtlich in einem Schaltplan  dargestellt werden, so kann sie für jede 
Ausführungsart durch einen Rahmen grafisch zusammengefasst werden. Zusätzlich ist das Kürzel der 
Ausführungsart in den Rahmen oben links darzustellen. Über die Verbindungseigenschaften kann in der 
Systemgruppe die Ausführungsart für die Steuerung des Leitungssatzes eingetragen werden.  
 
Abbildung 6: Ausführungsarten in den Verbindungseigenschaften 
 
3.12 Referenzen 
Die im Schaltplan verwendeten Bauteilreferenzen und deren Langbezeichnungen müssen den Vorgaben des VZK 
(VerwendungsZweckKatalog) entsprechen. Dieser Katalog wird als Datenexport der Datenbank CONNECT PARTS 
von MBC über den zuständigen Betreiber zur Verfügung gestellt. 
 
 
 
Abbildung 7: Stecker- Referenz 
 
Syntax der Bauteilreferenzen im Schaltplan:  
<ref><trenner1><steckername><trenner2><Steckerform><trenner3><AKZ><trenner4><OKZ> 
 
<ref> 
 
= Kurzbezeichnung des Verwendungszweck Katalogs 
<trenner1> 
= „*“  
<steckername> = Steckername (max. 3 Stellen) 
<trenner2> 
= „-„ 
<Steckerform > = Unterscheidung der Kontaktierungsseite, z. B. S=Steckerseite, B=Buchsenseite 
 
Ggf. sofern benötigt (genaueres siehe Kapitel 5.3.2 Verwendung von Anlage- und Ortskennzeichen) 
<trenner3> 
= „_“ 
<AKZ>  
= V[Zahl] 
<trenner4> 
= „_“ 
<OKZ>  
= O[Zahl] 
 
Beispiele: 
N10/1*2-B 
N10/1*2-S 
N10/1*2-B_V1_O3 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 18 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 18von 50 
 
Wenn kein Steckernamen eingetragen wurde wird automatisch der Stecker „1“ vergeben. 
 
Um die Konsistenz der Bauteil- und Steckerbezeichnungen sicherzustellen, muss die Modifikation der Referenzen 
und Langbezeichnungen (DE/EN) mit dem Werkzeug ConnyE vorgenommen werden.  
Die Referenzen bei einer Trennstelle sind im Bereich der REF und des Steckernamen identisch. Die 
Buchsengehäuse haben die Steckerform=B und die Steckergehäuse die Steckerform=S. Dadurch ist sichergestellt 
dass das Buchsengehäuse auf das entsprechend Steckergehäuse passt.  
 
Bei Komponenten mit Kabelschwanz hat die Kontaktgehäuseseite die gleiche REF wie die Komponente. Wie in 
untenstehendem Beispiel ist der Wegesensor Heckdeckel HFS mit der REF=B24/16 bezeichnet. Die Steckerseite 
hat die REF aber ergänzt um den Steckernamen und die Steckerform (B24/16*1-S).  
 
Für die Überprüfung von SRS- Leitungen müssen folgende Präfixe der Referenzen eingehalten werden: 
  
Zündpillen 
R12/*, auch wenn sie als Kabelschwanz-Komponenten vorliegen 
Airbag-  Steuergeräte 
N2/* 
Mantelrohschaltermodul 
N80 
Pyrofuses 
K88, F108 
 
 
 
 
 
Abbildung 8: Trennstelle 
  
 
Abbildung 9: Kabelschwanzkomponente 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 19 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 19von 50 
 
Die Referenzen aus dem iSPM sollten für die Baureihen übernommen werden und müssen gegeben falls nochmals 
freigeschaltet werden. Siehe hierzu auch das Kapitel 5 Hauptkapitel iSPM. 
 
Hinweis: Die Referenz-Langbezeichnung in DE/EN wird auf den Stecker vererbt und kann über 
Bauteileigenschaften eingesehen werden. Die Langbezeichnung ist für die NX-Bridge nötig und wird per Default 
nicht im Schaltplan am Stecker dargestellt. 
3.13 Steckernamen 
Da die Steckernamen sprachneutral sein müssen ist hier eine einfache Namensgebung gefordert. Die 
Steckernamen sind aus dem iSPM zu übernehmen. Sollten hierzu weitere Varianten notwendig sein, dann sollten 
diese durch einen „_“ erweitert werde und danach der Zusatz geschrieben werden.  
 
 
Hier ein Bespiel des Steckernamens für die Erweiterung: 
 
Abbildung 10: Variantenbehaftete Steckernamen 
 
Sollte der Steckername nicht über iSPM definiert sein, so muss er analog dem EPDM (Elektronik-
Produktdatenmanagement) gewählt werden. Für die Erweiterung der Steckervarianten gelten die obigen 
Grundsätze. 
Bei noch unterschiedlichen Architekturen oder Hardwareständen und Einbauorten wird der Steckername um das 
Anlage- und Ortskennzeichen erweitert (siehe Kapitel 5.3.2 Verwendung von Anlage- und Ortskennzeichen). 
Das Anlagekennzeichen wird hierbei zentral von iSPM vorgegeben. 
 
 
Folgende Zeichen sind erlaubt: 
- 
0-9 
- 
A-Z 
- 
_ 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 20 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 20von 50 
 
3.14 Steckerbezeichnung 
Die Steckerbezeichnung dient der Unterscheidung verschiedener Kontaktträger bzw. Kontaktierungsformen und 
steuert deren Bearbeitung in Folgewerkzeugen.  
 
Beispiele: 
Buchsengehäuse 
X10/1*2-B 
Steckergehäuse 
X10/1*2-S 
Kabelschuh 
W10*1-K 
Kabelschuh, blockseitig 
W10*1-KB 
Leitungsverbinder (z. B.Lötverbinder) 
Z10/1*1-L 
Leitungsverbinder blockseitig 
Z10/1*1-LB 
Schneid- Klemmverbinder 
Z20*1-T 
Kabelende 
Z30*1-C 
Kabelende blockseitig 
Z30*1-CB 
 
3.15 Kontaktform 
Die Kontaktform dient der Unterscheidung verschiedener Kontakte bzw. Kontaktierungsformen und steuert deren 
Bearbeitung in Folgewerkzeugen. 
 
Beispiele: 
LWL- Stecker 
X 
LWL- Buchse 
Y 
Flachbuchse 
F 
Flachstecker 
E 
Rundstecker 
O 
Rundbuchse 
R 
Pneumatik 
P 
Kabelschuh 
K 
Lötverbinder 
L 
3.16 Pin- Bezeichnung Kontaktgehäuse 
Bei der Pin- Bezeichnung von Kontaktgehäusen gilt die Regel dass die Bezeichnung aus der Freigabezeichnung der 
entsprechenden Teile übernommen wird. Zu dieser Regel gibt es folgende Ausnahmen: 
 
Einpolige Kontaktgehäuse, bei denen es in der Freigabezeichnung keine Pin- Bezeichnung gibt,  bekommen 
immer den  Pin- Namen = x. Beispiele für solche Komponenten sind Kabelschuhe, einfachbelegte 
Sicherungen oder Ultraschallschweißungen. 
 
Komponenten wie z.B. Sicherungsträger, Relais usw. ohne Pin- Bezeichnung in der Komponentenzeichnung 
werden mit Pin E = Eingang bzw. A = Ausgang versehen. Relais- Pins bekommen die Klemmenbezeichnung. 
 
Koaxialstecker die keine Pin- Bezeichnung haben werden mit Pin klein „x“ und groß „S“ bezeichnet, bei 
Steckern mit mehreren Kammern werden die Pins mit x1, S1 und x2 und S2 bezeichnet. Der Pin=x ist der 
Leitungsanschluss und der Pin=S ist der Schirm. 
 
HSD- Stecker erhalten sollten die Pins der stromtragenden Leitungen von 1-n durchnummeriert werden. Ist 
nur ein Schirm vorhanden, so wird dieser mit S gekennzeichnet. Sollten mehrere Schirme am zum Einsatz 
kommen, dann werden diese von S1-Sn durchnummeriert. 
 
Das Pining der Airbag- Stecker wird nach der Kammerbezeichnung aus der Freigabezeichnung des 
Steckers definiert.  
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 21 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 21von 50 
 
Bei der Schaltplanerstellung ist sicherzustellen dass der Komponenten-Pin und der Pin des angeschlossenen 
Leitungssatzsteckers richtig aufeinander gesteckt sind. Eine rein grafische Kontaktierung ist nicht ausreichend 
und kann beim Export der Schaltplandaten zu Fehlern führen.  
3.17 Sicherungsdarstellung 
Die Sicherungen sind mit dem unten dargestellten Bauteil aus der Bibliothek darzustellen.  Neben den allgemeinen 
Bezeichnungen für die  Referenz, Steckernummer, Steckerform und Pinbeschreibung sind noch die 
sicherungsspezifischen Merkmale wie Sicherungsplatz, Klemme und Sicherungswert darzustellen. Diese Inhalte 
werden auch exportiert. 
 
Abbildung 11: Sicherungsblock 
3.18 Leitungsbezeichnungen 
Um die Konsistenz der Leitungsbezeichnungen und der erforderlichen Attributen sicherzustellen, muss die 
Modifikation der Leitungen mit dem Werkzeug ConnyE vorgenommen werden. 
 
Firmenspezifische oder so genannte Dummy- Informationen wie z.B. Leitungstyp „YY, XX“ dürfen in der 
Leitungssatz- Entwicklungsphase verwendet werden wenn Leitungen oder Sonderkabel sich noch in der 
Entwicklung befinden und keine Freigabe besitzen. Bei der Blank- Freigabe des Leitungssatzes dürfen keine 
Dummy- Informationen mehr im Schaltplan vorhanden sein. Abweichungen bedürfen der Absprache und 
Genehmigung des zuständigen Leitungssatz- Baureihenteams.  
 
 
Bei Einsatz der Software E3.Cable ist der Leitungs- Attributträger „Attributträger“ der E3- Bibliothek zu verwenden. 
Die sichtbare Leitungsbezeichnung im Schaltplan enthält die Leitungsnummer, bei Sonderkabel die interne 
Leitungsnummer,  Leitungstyp, Querschnitt, Farbe und wenn notwendig noch die Vercodung. Sollten weitere 
Informationen notwendig werden so wird der der Leitungs- Attributträger entsprechend angepasst. 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 22 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 22von 50 
 
3.18.1 Leitungsfarben 
Die Leitungsfarben werden entsprechend IEC 60 757 (englische Kürzel in Großbuchstaben) ausgeführt. 
Ergänzend zur IEC 60 757 wurden für Schirme, Beilauflitzen, nicht isolierte und Naturfarbene Leitungen folgende 
Begriffe und Abkürzungen festgelegt: 
 
Deutsch 
Englisch 
Abkürzung 
Schirm 
Screen 
SC 
Beilauflitze 
drain wire 
DW 
Natur 
Nature 
NT 
Nicht isoliert 
not isolated 
NI 
 
3.18.2 Leitungsnummern 
Die Leitungsnummer besteht aus einer ganzzahligen Nummer. 
Um die Adern, Schirme oder Beilauflitzen eines Kabels direkt dem Kabel zuordnen zu können wird für diese bei 
mehradrigen Kabeln die Leitungsnummer um die 1-2-stelligen Adernkennzeichnung auf dem Adernanzeiger 
ergänzt.  
 
 
Beispiel : 
Leitungsnummer 
1000 
Kabelnummer 
1000 
Ader aus Kabel 
1000_10 (hier die zusammengesetzte Darstellung 
 
 
 
Abbildung 12: Leitungsnummern 
 
Die Leitungsnummer und die Adernkennzeichnung haben beim Export unterschiedliche Attribute.  
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 23 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 23von 50 
 
3.18.3 Leitungsbenennung 
Bei der eindeutigen Benennung (=Id) bei Leitungen ist nach Meterware und Sonderkabel auf dem Schaltplan zu 
unterscheiden.  
 
Der eindeutige Schlüssel für eine Meterware ist der zusammengesetzte Wert aus den Attributen Kurzzeichen, 
Querschnitt und Farbe. Die Farbe setzt sich aus der Grund- Zweit-  und Drittfarbe zusammen. Wobei die Drittfarbe 
normalerweise nicht eingesetzt werden soll.  
Das Kurzzeichen wird im normalen Sprachgebrauch auch als Leitungstyp, z. B. Iy, bezeichnet und ist eine 
Kombination aus dem Mantelisolationsmaterial und dem Leitungsaufbau.   
 
Syntax: 
 
<Kurzzeichen> -<Querschnitt>-<Farbe> 
 
Beispiel: 
 <Iy-0.75-GR/YEGN> 
 
Die Benennung des Sonderkabels setzt sich auch zusammen aus dem Kurzzeichen des Sonderkabels, dem 
Querschnitt und der Farbe des Leiters innerhalb des Sonderkabels zusammen. Der Unterschied zur Meterware ist, 
dass sich das Kurzzeichen vom Sonderkabel aus einem Kennbuchstaben für die Materialgruppierung und einer 
fortlaufenden Nummer zusammensetzt. Optional kann noch ein Zusatz für z. B. unterschiedliche Mantelfarben oder 
unterschiedliche Ausführungsarten dazugesetzt werden.  Als Beispiel sei die B48/3 genannt. 
 
Syntax: 
 
<Kurzzeichen> -<Querschnitt Einzelleiter 1 -n>-<Farbe Einzelleiter 1 -n > 
 
Beispiel: 
 <B48/3-0.75-GR/YE> 
 
3.18.4 Kennzeichnung von 48V- Leitungen 
Die Leitungen des 48Volt- Bordnetzes sind als solche zu kennzeichnen. Hierzu wird ein eigenes Attribut auf der 
Leitung zur Verfügung gestellt. 
 
3.19  Leitungskodierung 
Die Leitungen werden bei Bedarf, mit den aus Connect zur Verfügung gestellten Code versehen. Es sollen die IP- 
Code verwendet werden. Ist keine IP- Code aus Dialog für die benötigte Baureihe vorhanden, aber in einer anderen 
Baureihe im Einsatz, so kann diese IP- Code für die notwendige Baureihe in CONNECT freigeschaltet werden. Nur 
im Ausnahmefall sollen die U- Code verwendet werden.  
 
Die Verwendung bzw. die Neuvergabe der Code ist inhaltlich mit dem zuständigen Leitungssatz- Baureihenteam 
abzustimmen. 
 
Es können die booleschen Operationen nach Vorgabe aus der ConnyE verwendet werden um die Leitungskodierung 
vorzunehmen. Bei E3.Cable gibt es keine Begrenzung für die Anzahl der Code und deren booleschen Operatoren.  
 
In Absprache mit dem Baureihenteam kann man zum Kennzeichnen der Leitungen für Serienumfänge „U972“ 
verwenden. 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 24 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 24von 50 
 
3.20 Kabeldarstellung 
Kabel, Verdrillungen oder sonstige Sonderleitungen werden zusätzlich zu den verwendeten Einzeladern noch durch 
ein geeignetes graphisches Symbol gekennzeichnet. 
 
 
 
Abbildung 13: Symbol für Verdrillungen 
 
 
 
Abbildung 14: Symbol für Schirme 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 25 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 25von 50 
 
3.21 Klemmen und Signalbezeichnungen 
Dargestellte Anschlussklemmen sind mit den erforderlichen Klemmen- oder Signalbezeichnungen nach DIN 72552  
bzw. der Klemmen- und Signalliste aus dem EPDM zu beschreiben. 
Jeder Pin von Steuergeräten, Sensoren und Aktoren enthält eine lesbare Signal- oder Klemmenbezeichnung, 
gegebenenfalls muss das Innenschaltbild entsprechend angeordnet werden.  
 
Auf der elektrisch angeschlossenen Leitung ist bei den Verbindungseigenschaften das Signalattribut zu befüllen. 
 
 
Abbildung 15: Signal- und Klemmenbezeichnung 
 
3.22 Bauteillegende 
Die Bauteilreferenzen (Kapitel 3.12) sowie die verwendeten Code sind unter Verwendung der Langbezeichnung in 
Deutsch und Englisch des Verwendungszweckkatalogs in einer Legende aufzulösen. Sowohl die Code als auch die 
Referenzen müssen einen Eintrag für die Baureihe in CONNECT zugewiesen haben. Diese Bauteillegende ist vor 
jedem Export von Daten zu aktualisieren.  
 
Die Legende wird im rechten Zeichnungsteil oberhalb des Schriftfeldes platziert.  
 
Der Eintrag „**Kein Eintrag gefunden**“ bei der Langbezeichnung weist darauf hin dass Einträge im 
Verwendungskatalog oder Code in CONNECT nachgetragen werden müssen oder keine Baureihe zugeordnet 
wurde. Dieser Eintrag ist zu vermeiden. 
 
3.22.1 Referenzen 
Format: 
<Referenz>  
<Langbezeichnung_de>  
 
<Liste der Feldkenner> 
 
 
<Langbezeichnung_en> 
 
Beispiel: 
A1 
 
Kombiinstrument 
 
 
20N 
 
 
Instrument cluster 
X20/1   
Steckverbindung 3. Bremsleuchte 
3A,20B,30J  
 
 
Connection 3. Brake light 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 26 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 26von 50 
 
3.22.2 Code 
Format: 
<Code>  
<Langbezeichnung_de>  
 
 <Liste der Feldkenner> 
 
 
<Langbezeichnung_en> 
Beispiel: 
IP443  
Lenkrad heizbar  
 
 
30B,56C 
 
 
heated steering wheel 
U20 
 
Gültig für alle außer USA/Japan  
2A,16C 
 
 
applicable to all without USA/Japan 
3.22.3 Verwendung 
Unter Verwendung versteht man die Zuordnung eines Schaltplanes zur Baureihe, Ausführungsart und 
Fahrzeugphase (siehe hierzu auch Kapitel 7.2.1 Benennung der Baureihe, Ausführungsart und Fahrzeugphase) 
Format: 
<Code> <Baureihe><Ausführungsart><Fahrzeugphase Name> 
 
Beispiel: 
C231 FC AJ2016.1  
C205 FW EF2.BL2 
3.23 Ebenenkonzept 
Das Ebenenkonzept wird zentral von der Abteilung RD/EKL erstellt, bearbeitet und allgemein zur Verfügung 
gestellt. Durch diese Maßnahme entsteht ein einheitliches Template für alle Entwicklungspartner, keine 
firmenspezifischen Lösungen. 
 
3.24 Slotdarstellung 
Es gibt Stecker bei denen Kammernummern mehrfach vorhanden sind. 
 
Wie aus nebenstehender Abbildung zu ersehen gibt es einen sogenannten Slot A und B 
welche beide die Kammern 1 und 2 besitzen. Um die Kammern unterscheiden zu können 
setzt sich der Pinname aus dem Slot und der Kammernummer zusammen. Bei diesem 
Beispiel gibt es für den Slot A die Pinnamen A1, A2, A3, A4 und für den Slot B die 
Pinnamen B1 und B2.  
 
Abbildung 16: Slotdarstellung 
 
Bei neuen Komponenten mit Slotdarstellung sieht der Prozess so aus, dass der Leitungssatzlieferant der diese als 
erste einsetzt, das Bauteil den anderen Leitungssatzlieferanten vorstellt und das Symbol erstellt und verteilt. Alle 
Komponenten mit Slotdarstellung sind auf einer Übersichtszeichnung (siehe Abbildung 30: Übersicht der 
Slotkomponenten) darzustellen und an alle Leitungssatzlieferanten zu verteilen.  
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 27 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 27von 50 
 
E40/2
#9
1/4
E40/2*1-S
1
#10
2/4
2
#11
3/4
3
#12
4/4
4
N33/4
#12
1/4
N33/4*1-S
1
#11
2/4
2
#10
3/4
3
#9
4/4
4
4/4
1/4
E40/2*1-B
3/4
2/4
2/4
3/4
1/4
N33/4*1-B
4/4
Z20/36
/A-205-440-01-01.D9
#13
x/1
Z20/36*1-LB
x
x/1
Z20/36*1-L
Z20/36
/A-205-440-01-01.H9
#13
x/1
Z20/36*2-LB
x
x/1
Z20/36*2-L
0.50
2
H7
RD
35
0.50
3
H7
WH
35
0.50
4
H7
YE
35
0.50
5
H7
DW
35
1
H7
SC
35
3.25 Nicht kontaktierte Schirmung 
Bei geschirmten Sonderleitungen, deren Schirm nicht mit den Kontaktgehäusen verbunden ist, müssen im 
Schaltplan die offenen Enden des Schirmes mit Leitungsenden-Bauteil-Blöcken (E3.Cable Symbol: Bauteil  
Kabelende) dokumentiert werden. Diese können in der Leitungssatz- Zeichnung vermasst werden. Der Schirm wird 
als Leitung dargestellt. Das Schirmsymbol darf nicht über die Schirm- Leitung gezogen werden.  
 
Siehe Beispiel: 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Abbildung 17: Nicht angeschlagene Schirme  
 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 28 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 28von 50 
 
3.26 Schirmanschluss am Kontaktgehäuse  
Die Schirmleitung von HSD- und Fakra-Steckern werden am Kontaktgehäuse kontaktiert. Dieses wird im Schaltplan 
wie folgt dargestellt. Das Schirmsymbol darf nicht über die Schirm- Leitung gezogen werden.  
 
 
 
Abbildung 18: Schirmanschluss am Gehäuse 
3.27 Hydraulische und pneumatische Umfänge 
Es gibt Umfänge, wie beispielsweise Pneumatik- oder Hydraulikleitungen, die im Leitungssatz mit verlegt werden. 
Diese liegen nicht in dem Verantwortungsbereich der Leitungssatzentwicklung. 
 
Für Schaltpläne gilt folgende Regeln: 
- 
Kein Mix von elektrischen und pneumatisch/hydraulischen Verschaltungen in einem Schaltplan 
- 
Es darf nicht die Abteilungsbezeichnung der Leitungssatzentwicklung im Zeichnungskopf eines 
pneumatisch/hydraulischen Schaltplanes stehen. Es ist die Abteilungsbezeichung der verantwortlichen 
Fachabteilung einzutragen. 
- 
Pneumatisch/hydraulische Schaltpläne dürfen NICHT in Connect eingestellt werden. 
 
Die Leitungssatzentwicklungsabteilung lehnt jegliche Verantwortung für die Richtigkeit dieser pneumatisch/ 
hydraulischen Schaltpläne ab. 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 29 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 29von 50 
 
3.28 Schlaufen 
Leitungsschlaufen bzw. ein geändertes Routing in der Leitungssatzzeichnung sind für Sicherheits-relevante bzw. 
kundenspezifische Anforderungen zulässig. Dazu wird ein RoutingPoint der Leitung im Schaltplan hinzugefügt. 
Dieses Vorgehen darf nicht bei Produktions-relevanten Verlege-Wegen des Leitungssatzlieferanten verwendet 
werden. Bei Ringstrukturen (Doppel-H) muss der exakte Verlege-Weg manuell der Leitung zugewiesen werden, um 
die reale Leitungslänge im Leitungssatz (KBL) zu erhalten, d.h. auch hier darf kein RoutingPoint verwendet werden. 
 
 
Abbildung 19: Schlaufendarstellung 
3.29 Schaltplanumfänge von Kleinlieferanten in einem Projekt 
Bei der Vergabe des Leitungssatzumfanges für eine Baureihe kommen oftmals auch Kleinlieferanten für 
Spezialumfänge wie Parktronik- Verkabelung zum Einsatz. Da diese Umfänge auch in Masterleitungssätzen zum 
Einsatz kommen und um doppelte Leitungsnummern zu verhindern sind diese Umfänge in das Schaltplanprojekt 
der Baureihe zu integrieren. Die Art- und Weise ist mit dem Baureihenteam abzustimmen.  
    
4 Attribute 
Durch die Verwendung der MCG Softwareerweiterung ConnyE sind die Inhalte der zwingend erforderlichen 
Attribute wie Kurzzeichen, Kabel / Ader in Verbindung, Querschnitt, Farbe, Code usw.,  vorgegeben. 
 
4.1 Optionale Attribute 
Das optionale Attribute Feature ist  inhaltlich noch nicht festgelegt. Anforderungen für weitere optionale Attribute 
sind an das Team „Leitungssatz DMU und Tools“ in RD/EKL zu stellen.  Der Attributanzeiger wird dann 
gegebenenfalls erweitert und mit dem Update- Prozess allen externen Partnern zur Verfügung gestellt.  
 
Zurzeit gibt es folgende optionale Attribute: 
 
Kontaktoberflächen und PosNr bei Kontakten 
 
Sachnummer vom Kontaktgehäuse 
 
Systemgruppe bei Leitungen 
 
Die optionalen Attribute sind auf dem Schaltplan nicht sichtbar dargestellt. 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 30 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 30von 50 
 
4.1.1 Systemgruppe 
Das Attribut Systemgruppe dient dem Leitungssatzlieferanten für die Steuerung welche Leitungen in welche 
Masterleitungssätze gesteuert werden. Das Attribut Systemgruppe kann auf Leitungen vergeben werden und ist 
aber unsichtbar darzustellen.  Dabei kann es mit folgenden Inhalten belegt werden: 
 
Verlegebereich 
 
Ausführungsart 
 
Lenkungsart 
 
Benutzerdefinierte Inhalte. 
 
Es wurden Folgende Begriffe vereinheitlicht: 
Verlegebereich: 
Abkürzung 
Beschreibung 
COC 
Cockpit 
MRA 
Motorraum 
MOT 
Motor 
DACH 
Dach 
MIKO 
Mittelkonsole 
TUER 
Tür 
SITZ 
Sitz 
GET 
Getriebe 
HECK 
Heck 
HECKD 
Heckdeckel 
HECKK 
Heckklappe 
HA 
Hinterachse 
STO 
Stoßfänger 
BOD 
Boden 
VERD 
Verdeck 
RBA 
Rahmenbodenanlage 
 
Ausführungsarten: 
Abkürzung 
Beschreibung 
W 
Limousine 
EV 
 
Elektrofahrzeug 
S 
Kombi 
C 
Coupé 
A 
Cabrio 
X 
Sonderfahrzeuge 
V 
Verlängert 
 
Lenkungsarten:  
Abkürzung 
Beschreibung 
LL 
Linkslenker 
RL 
Rechtslenker 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 31 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 31von 50 
 
Benutzerdefinierte Inhalte: 
Am Ende der Systemgruppe können noch benutzerdefinierte Inhalte zugefügt werden.  
 
Sollten mehrere dieser Steuerungsmerkmale notwendig sein so sind diese einem „+“ zu konkatinieren. Bei leeren 
Bereichen braucht dieser nicht dargestellt werden. 
 
Syntax: 
<Verlegebereich>+<Ausführungsart>+<Lenkungsart>+<Benuterdefiniertes Steuerungsmerkmal> 
 
Beispiele: 
COC+W,V+LL+ICE 
COC,MRA+LL 
 
4.2 Attributanzeiger 
Im Attributanzeiger sind alle erforderlichen Attribute definiert.  Der Attributanzeiger wird als Teil der MCG – 
Bibliothek bereitgestellt. Änderungen im Attributanzeiger können mit einer Standartfunktion in E3.Cable als Update 
übernommen werden.  
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 32 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 32von 50 
 
5 iSPM 
5.1 Definition 
iSPM (integriertes SchaltPlan Management) ist der zukünftige systemorientierte Schaltplan Modulbaukasten im 
Bereich der Leitungssatzentwicklung im Bereich RD/EKL. 
Hier werden hardwareseitig alle leitungssatzrelevanten Schaltpläne für ein virtuelles Fahrzeug in E³.Cable C1 
abgelegt und für die Initialbefüllung einer Baureihe verwendet. 
Aufgrund der Systemsichtweise sehen wir hier Verknüpfungspunkte zur übergreifenden Systemliste (Hintergrund 
ISO 26262) und Potential für gemeinsame Lösungsansätze. 
Im virtuellen Fahrzeug C1 werden alle bekannten leitungssatzrelevanten Schaltpläne in E3.Cable abgelegt und für 
die Erstbefüllung einer Baureihe verwendet.  
 
 
Abbildung 20 Leitungssatzentwicklungs Prozess mit iSPM 
5.2 Serienprozess 
Im veröffentlichten Schaltplan Modulbaukasten des iSPM werden die geprüften systemorientierten 
Basisschaltpläne den Baureihenteams in der Leitungssatzentwicklung für die Erstbefüllung nach 
Vorentwicklungsübergabe (BMKL) zur Verfügung gestellt. Diese Ausleitung reduziert das 200%-ige iSPM Projekt auf 
100% Baureihenbasisprojekt. Die Baureihenbasisschaltpläne dienen als Grundlage für die Anpassung, Integration 
und Weiterentwicklung der Systeme für den Serienentwicklungsprozess. In den Baureihenschaltplänen werden 
beispielsweise die realen Stützpunkte und Massen dokumentiert und Trennstellen festgelegt. In gewissen 
Abständen sollen die Schaltpläne aus denen Leitungssätze erstellt wurden gegen das iSPM verglichen werden um 
mögliche Abweichungen feststellen zu können.   
Sollten Abweichungen an Leitungen gemacht werden, müssen diese seitens Lieferant beschrieben und 
dokumentiert werden, z.B. weshalb eine andere Leitung / Farbe / Querschnitt als vorgegeben verwendet wurden 
(siehe Kapitel 5.3.5Leitungsauslegung). 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 33 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 33von 50 
 
 
Abbildung 21: iSPM Prozess 
5.3 Inhalt 
Folgende leitungssatzrelevanten Informationen werden im iSPM festgelegt: 
 
Referenz der Bauteile inkl. Anlagenkennzeichen(siehe auch Kapitel 3.12Referenzen und 5.3.2.1 
Anlagekennzeichen) 
 
Stecker Benennung 
 
Leitungsquerschnitt (inkl. Auslegungskriterien), Farbe und Typ (kann bei Bedarf in einzelnen 
Zielbaureihen angepasst werden) 
 
Signalname 
 
Leitungssatzspezifische Komponenten wie z.B. CAN-Verteiler 
 
Sicherungswerte und Versorgungsklemmen (kann bei Bedarf in einzelnen Zielbaureihen angepasst 
werden) 
 
Nicht festgelegt werden fahrzeugspezifische Umfänge wie: 
 
Sicherungsdose aus dem das System versorgt wird 
 
Massebolzen 
 
Lötverbinder die BR abhängig sind 
 
Trennstellen 
 
Wenn sich der Informationsinhalt zu den Baureihenschaltplänen unterscheidet, werden diese unten genauer 
beschrieben. 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 34 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 34von 50 
 
 
Abbildung 22: Vergleich Basisschaltplan und BR- Schaltplan an KZL und 3. Bremsleuchte 
5.3.1 Projektstruktur 
Das iSPM-Schaltplanprojekt ist eine Sammlung aller aktuellen Systemschaltpläne in höchster  veröffentlichter 
Version  aus unterschiedlichen Architekturen, welche als Grundlage für Baureihen-spezifische Schaltpläne 
herangezogen werden können. 
 
Die Strukturierung der Schaltpläne erfolgt nach: 
 
Hauptgruppe 
o Gruppe 
 
Untergruppe 
 
Architektur / Hardware (HW) 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 35 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 35von 50 
 
 
Abbildung 23: Prinzip der Gruppierung der Systeme 
 
Unter der Untergruppe werden die verschiedenen Hardwarevarianten architekturabhängig dargestellt.  
Es sind werden die unten beschriebene Attribute in E3.Cable für die Strukturebenen befüllt. Hier ein Beispiel: 
 
iSPM_Hauptgruppe: 
 
01_Aussenaumsysteme 
 
iSPM_Gruppe:  
 
01_Beleuchtung 
 
iSPM_System:  
 
02_Heckleuchte Basis einteilig 
 
iSPM_Architektur_HW:  
Star2.3 
 
Aktuelle Hauptgruppen /Untergruppen sind in iSPM zu entnehmen. 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 36 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 36von 50 
 
 
Abbildung 24: Hardwarevarianten am Beispiel Heckleuchte 
Diese generelle Struktur ist auch im Baureihenprojekt weiterzuverwenden. 
5.3.1.1 Hauptgruppen 
Die Hauptgruppe sind die Gewerke aus der Modulgruppeneinteilung. 
00 Innenraumsysteme 
01 Aussenraumsysteme  
02 Regelsysteme 
03 UI (Telematik,BAK) 
04 Motor/Triebstrang 
05 Sonder-Fzg/Individual. 
5.3.1.2 Gruppen 
Die Gruppen orientieren sich an den Modulgruppen und sind mit der Systemliste abgestimmt.  
Im nachfolgenden sind Beispiele aufgeführt: 
01_Innenbeleuchtung 
02_Sitze 
03_Klimatisierung 
04_Bordnetz 
05_Diebstahlschutz 
06_passive Sicherheit 
07_Komfort 
08_Versorgung Geräte 
… 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 37 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 37von 50 
 
5.3.2 Verwendung von Anlage- und Ortskennzeichen 
Bei der Beschreibung von Systemen und deren EE- Komponenten im iSPM werden die bekannten Referenzen 
überprüft ob sie so für neue Baureihen noch gültig sind und dann ggf. der BR=C1 zugewiesen. Bei neuen Systemen 
werden neue Referenzen vergeben und diese sollten so auch im Baureihenschaltplan später verwendet werden.  
 
Zu den Referenzen gibt es bei iSPM noch weitere Attribute für die Unterscheidung der Architekturen und zur 
Unterscheidung geometrischer Lagen von EE- Komponenten mit Kontaktgehäusen.  
 
Alle Komponenten haben eine Anlagenkennzeichen. Ausnahme hiervon sind die, die nur in C1 gültig sind wie X ,Z, 
W, F. Für diese folgt direkt nach der Referenz (Kapitel 3.12) das Ortskennzeichen. 
5.3.2.1 Anlagekennzeichen 
Da für die verschiedenen Baureihen unterschiedliche Architekturen oder Hardware (HW) zum Einsatz kommen kann 
und alle Baureihen vom selben iSPM-Schaltplanprojekt ausgeleitet werden können sollen, müssen mehrere HWs 
gleichzeitig im iSPM-Schaltplanprojekt existieren können. Unterschiedliche HW wird zwar auf unterschiedlichen 
Schaltplanseiten gezeichnet, jedoch muss die REF über die verschiedenen HWs hinweg identisch sein können. Das 
Anlagekennzeichen wird für die unterschiedlichen HWs verwendet und steht nach der Bereitstellung in das 
Baureihenbasisprojekt auf den Komponenten. Damit eine Rückverfolgung der Komponenten zum iSPM möglich ist 
müssen die Anlagekennzeichen im Baureihenschaltplan erhalten bleiben. (Syntax siehe Kapitel 3.12 Referenzen) 
Bsp.: 
F152/1*1-KB_V1 
F152/1*1-KB_V1_O1 
 
5.3.2.2 Ortskennzeichen 
Wenn ein elektrisches Gerät mit Kontaktgehäusen an unterschiedlichen Orten im Gesamtfahrzeug platziert ist so 
sollten diese auch dort überall die gleiche REF besitzen. In Schaltplan wird hierfür das  Ortskennzeichen bei 
unterschiedlichen Geometrielagen(Unterschiedliche Einbauorte) eines Steckers verwendet. (Syntax siehe Kapitel 
3.12 Referenzen) 
 
Bsp.: 
F152/1*1-KB_O1 
F152/1*1-KB_V1_O1 
 
5.3.3 Nicht definierte Pinnamen 
Es kann der Zustand vorkommen dass Systeme von der logischen Seite her schon bekannt sind aber das konkrete 
Pining noch nicht feststeht da der Serienlieferant für die EE-Komponente noch nicht vergeben ist. Solche Systeme 
können auch im iSPM beschrieben sein. Hierzu bekommen dann die Pins einen Präfix „nd“ und danach eine 
Pinnummer. Dieser Pinname ist im Baureihenschaltplan dann entsprechend der EPDM- Angabe anzupassen.  
5.3.4 Massen, Sicherungen, Stützpunkte 
Massepunkte, Sicherungen und Stützpunkte werden im iSPM neutral beschrieben und müssen dann bei den 
baureihenspezifischen Schaltplänen konkret festgelegt werden.  
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 38 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 38von 50 
 
5.3.5 Leitungsauslegung 
Für neue BR kommt die Querschnittauslegung von der Konzeptentwicklung Leitungssätze und werden so in iSPM 
übernommen. Es wird das kritischste Kriterium im iSPM eingetragen, d. h. es muss mindestens einen Eintrag 
geben. Für die weitere Verarbeitung in den BR-Teams sind die Kriterien für die Auslegung wichtig und sollen 
deshalb mit dokumentiert werden. Sollten diese Vorgaben aus dem Baureihen Basisschaltplan geändert werden, ist 
dies zu begründen und zu dokumentieren. 
 
Hinweis: Die Auslegungskriterien im iSPM-Schaltplanprojekt sind lediglich Vorschläge. Verbindlich ist EPDM! 
 
Im folgenden Beispiel gibt der Basisschaltplan die Leitung ly-1.5-gn vor und der Lieferant hat die Leitung auf ly-2.5-
gn geändert: 
 
Abbildung 25: Leitungsauslegung 
Bedingung für einen durchgängigen Prozess ist, dass im Basisschaltplan mindestens ein Vorgabewert im iSPM 
definiert ist und dass alle Begründungen des Lieferanten leer sind. Nach der Übergabe der Basisschaltpläne an die 
Baureihe sind die Auslegungskriterien vom Leitungssatzlieferanten zu überprüfen und bei Abweichungen an der 
Leitung zu dokumentieren warum die Vorgaben geändert wurden. 
5.3.6 Code 
Standardmäßig werden keine U-Code und kein IP-Code im iSPM verwendet. Sollte dies doch im Ausnahmefall 
notwendig sein dann werden separate „C1-Code“ vergeben. 
Im Baureihenschaltplan sind die C1- Code durch die realen Baureihen- Code auszutauschen oder gegebenenfalls 
anzupassen. Beim Ausleiten mit ConnyE dürfen keine „C1-Code“ mehr enthalten sein. Falls doch, dann muss eine 
Fehlermeldung ausgegeben werden. 
5.3.7 Signale 
Alle Leitungen erhalten im iSPM einen Signalnamen. Diese sind im Baureihenschaltplan weiterzuverwenden.  
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 39 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 39von 50 
 
6 Datenbereitstellung 
Nach Fertigbearbeitung eines Schaltplans ist die Legende sowie der Datenstand automatisch zu generieren, und 
abzuspeichern. Die Dateinamen werden, entsprechend Kapitel 7.2 Namenskonventionen gebildet.  
 
6.1 Datenbereitstellung zu MBC 
Die Originaldaten des verwendeten Schaltplaneditors, bei E3.Cable die gesamten Projektdaten, die 
Zeichnungsdaten und die digitale Produktbeschreibung der Schaltpläne sind nach Fertigstellung eines 
Zeichnungsstandes und nach jeder leitungssatzrelevanten Änderung zu MBC zu übertragen.  
 
 
Nach jeder Leitungssatzrelevanten Schaltplanänderung wird über die ConnyE ein (Kapitel 0) Generischer 
Datenexport des geänderten Schaltplans veranlasst. Nach Abschluss einer Fahrzeugphase liegt der gesamte 
Datenbestand des Schaltplanprojektes MBC vor. 
 
 
Alle in Leitungssätzen referenzierten Schaltpläne bzw. –Stände und Blätter müssen an DAG übermittelt werden und 
sind im Multi-User Projekt auf dem DAG Server zu archivieren. Gleichzeitig müssen diese Stände über den 
Generischen Export an Connect übermittelt werden. Der Lieferant muss somit sicherstellen, dass für alle 
Leitungssatzzeichnungen die referenzierten Schaltpläne in E3.Cable Multi-User Projekt als auch in Connect zu 
jeder Leitungssatzversion der entsprechende Einzel- & Gesamt Schaltplan-PDFs sowie die XML Datei zur Verfügung 
stehen. 
 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 40 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 40von 50 
 
 
 
 
Abbildung 26: Datenbereitstellung Schaltpläne 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 41 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 41von 50 
 
Grafikformate:  
 
 
PDF aus E3.Cable  
 
Netzliste: 
 
Generischer Export aus E3.Cable für alle Baureihen 
 
Daten aktueller Schaltplandaten werden in das Briefkasten- Austauschverzeichnis eingestellt und via ENX zu MBC 
übertragen. Das Briefkastenverzeichnis ist mit dem Betreiber abzustimmen. 
Der Betreiber konfiguriert den ENX - Mechanismus so, dass die Daten im spezifizierten Projektverzeichnis auf 
einem Rechner bei RD/EKL bereitgestellt werden. 
 
Dateinamen siehe Kapitel 7.2 Namenskonventionen. 
 
6.2 Datenbereitstellung von MBC 
Daten aktueller Schaltplanbibliotheken, Template- Dateien, ConnyE, Leitungs- und Bauteildaten sowie die 
Exportdateien aus Connect werden vom Betreiber in das Briefkasten- Austauschverzeichnis der beteiligten Zuliefer- 
Firmen eingestellt und via ENX zum Zulieferer übertragen. Die Austauschverzeichnisse sind vordefiniert und 
können mit dem Team „Leitungssatz DMU und Tools“ (Mechanische Komponenten und Tools) erweitert werden. 
Dateinamen siehe Kapitel 7.2 Namenskonventionen. 
 
 
Das Briefkastenverzeichnis auf der Gegenseite (Zulieferer) ist mit dem Betreiber abzustimmen und die Zugriffe 
einzurichten. 
Der Betreiber konfiguriert den ENX - Mechanismus so, dass die Daten in dem spezifizierten Eingangsbriefkasten 
beim Zulieferer bereitgestellt werden. 
 
Die Dateinamen der Exportdateien aus Connect werden ohne Exportdatum im Dateinamen, jedoch mit Hinweis auf 
den Inhalt mit Schlüsselwort Full bei einem kompletten Update oder DIFF bei einem inkrementellen Update, 
bereitgestellt. Da das inkrementelle Update nur Neuerungen kennt, gelöschte Objekte werden dort nicht 
aufgeführt, ist der komplette Update mindestens einmal in der Woche auszuführen.  
 
Unter DIFF ist hier der zeitliche Abstand zum letzten FULL- Update zu verstehen. 
 
Die Versionen der E3.Cable Release, Patchlevel und der ConnyE müssen wegen Rückwärtskompatibilität und 
Datenaustausch auf Seite der externen Installation und MBC gleich sein. Die Umstellungstermine werden vom 
Team „Leitungssatz DMU und Tools“ koordiniert um eine gemeinsamen Umstellungszeitpunkt sicherzustellen.  
 
6.3 Datenbereitstellung für Modulverkabelung 
Für die Bearbeitung von Modulen wie Sitze, Türen, Stoßfänger usw. werden die für diese Umfänge notwendigen 
Einzelschaltpläne bereitgestellt.  
Die Bereitstellung wird vom zuständigen Leitungssatz- Baureihenteam veranlasst. 
 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 42 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 42von 50 
 
 
7 Datenformate und Namenskonventionen  
7.1 Datenformate 
Die Datenformate wie SVG, PDF und XML fließen in MGD/D interne Dokumentationssysteme ein und sind vor dem 
erstmaligen Einsatz bei MBC zu qualifizieren. 
 
7.2 Namenskonventionen 
Um die Lesbarkeit der Dateinamen zu erleichtern werden die Strukturelemente des Dateinamen durch einen 
Unterstrich voneinander getrennt dargestellt.  
Leerzeichen und Sonderzeichen wie z.B. Klammern und Umlaute in Dateinamen sind zu generell zu vermeiden. 
7.2.1 Benennung der Baureihe, Ausführungsart und Fahrzeugphase 
Jeder  Schaltplan ist mit seiner Verwendung (Baureihe, Ausführungsart und die Fahrzeugphase) zu versehen. Er 
kann mehrere Verwendungen haben, beispielsweise wenn in einem Schaltplanprojekt für eine Baureihe mehrere 
Ausführungsarten, wie z. B. Limousine und Kombi, besitzt oder verblockte Baureihen darin dokumentiert sind. In 
dem Beispiel unten wäre der Schaltplan für die Baureihe C205 gültig in den Ausführungsarten FS und FW.  
 
Die Verwendung wird in der Schaltplanlegende unter die Varianten geschrieben.  
 
Die Baureihe, die Ausführungsart und die Fahrzeugphase werden in CONNECT eingepflegt und nur diese 
Zuordnungen dürfen auf den Schaltplan geschrieben werden. Die Verwendung bzw. die Fahrzeugphase wird 
Sprachneutral dargestellt. 
 
 
 
Abbildung 27: Verwendung (Baureihe, Ausführungsart, Fahrzeugphase) 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 43 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 43von 50 
 
7.2.2 Sachnummernformate 
Die Schreibweise von Sachnummern innerhalb von Zeichnungen und Dokumenten kann in strukturierter oder 
unstrukturierter Form erfolgen, die strukturierte Form ist wegen der besseren Lesbarkeit vorzuziehen. Innerhalb 
der Exportdateien ist die unstrukturierte Schreibweise einzutragen. In der Sachnummer ist die Baureihe 
aufzuführen. 
 
Bei der Sachnummer sind folgende Zeichen zulässig: A-Z, 0-9, „-„ 
 
Beispiel der unstrukturierten Form innerhalb von Zeichnungen:  
A2045401005 
HBE2128201034 
212820X034 
16754001020301 
 
Beispiel der strukturierten Form innerhalb von Zeichnungen:  
A204-540-10-05 
HBE212-820-10-34 
212-820 X-034 
167-**********-01 
 
Als Strukturelement wird hier ein Leerzeichen verwendet. 
7.2.2.1 Schaltplansachnummer: 
Zukünftig soll die Schaltplansachnummer wie folgt aussehen: 
  
Abbildung 28 Schaltplansachnummer 
 
Syntax: 
<Baureihe> - <KGU> - <Struktur iSPM> - <Zähler>-<Blatt> 
<Baureihe> 
Baureihe von MBC festgelegte Nummer 
<KGU> 
Konstruktionsuntergruppe ist immer „540“ für Schaltpläne  
<Struktur iSPM> 
spiegelt wieder, wo der Schaltplan in der Struktur, die von der iSPM vorgegeben wird, 
eingeordnet ist 
<Zähler> 
Fortlaufende zweistellige Nummer 
 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 44 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 44von 50 
 
 
Bsp: 
167-**********-01 
222-**********-02 
7.2.3 Dateinamen Einzelschaltpläne 
Aus Schaltplänen abgeleitete Daten, werden nach der nachfolgend beschriebenen Dateinamenstruktur abgelegt. 
Die Grundlage sind die Felder aus dem Zeichnungskopf. 
  
Syntax :  
<sachnummer>_ <blatt>_< Datenstand >.<Dateiendung> 
< sachnummer> 
Sachnummer aus dem Zeichnungsschriftfeld (Sach-Nr.) 
< blatt> 
Blattnummer aus dem Zeichnungsschriftfeld (Blatt) 
< datenstand> 
Datenstand aus dem Zeichnungsschriftfeld in dem Format YYYY-MM-DD 
 
 
Beispiele: 
Einzel- Schaltplan PDF- Datei:  
212—**********-02_1_2010-09-22.pdf 
 
Alternative Dateinamen: 
Schaltplan PDF- Datei:  
 
21254001020302_1_2010-09-22.pdf 
 
Die alternativen Dateinamen sind nicht zu bevorzugen. 
 
Fehlerhaftes Beispiel der strukturierten Form als Dateiname:  
212_540_010203*02.01_1_2010-09-22.pdf  
 
Hinweis:  
In dem oben aufgeführten fehlerhaften Beispiel sind die rot dargestellten Zeichen fehlerhaft. Es dürfen in der 
Sachnummer keine Sonderzeichen wie „_“, „*“ oder „.“ Enthalten sein. Weiterhin ist die Blattnummer „01“ in der 
Sachnummer enthalten. Durch diese Strukturfehler können die Schaltplanfiles nicht in CONNECT HARNESS 
importiert werden. 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 45 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 45von 50 
 
7.2.4 Dateinamen Baureihenprojekte 
Die Strukturelemente der Projektbezeichnung werden durch einen Unterstrich voneinander getrennt dargestellt. 
 
Syntax :  
 
<baumuster>_<fahrzeugphase>_< Datenstand>.<Dateiendung> 
 
Beispiel für Baumusterbezeichnung: 
C212 = Baumusterbezeichnung der Fahrzeugbaureihe 212 
D166 = Baumusterbezeichnung des Motor 166 
 
Beispiele für Dateinamen: 
Baureihenprojekt: 
C212_E1A2_2000-06-30.e3s 
Änderungsjahr:  
C212_AEJ07X_2000-06-30.e3s 
C166_AEJ07X_2000-06-30.xml 
Aggregateprojekt: 
D166_AEJ07X_2000-06-30.e3p 
 
7.2.5 Dateinamen Gesamtschaltplan  
Der Dateinamen des Gesamtschaltplanes ist wie folgt aufgebaut. 
 
Syntax :  
<Baureihe 1>_<alle Ausführungsarten dieser Baureihe 1 mit gleicher Fahrzeugphase>-<Fahrzeugphase>_ 
<Baureihe n>_<alle Ausführungsarten Baureihe n mit gleicher Fahrzeugphase>-<Fahrzeugphase n>_<Datum>.pdf 
 
Beispiel: 
C231_FAS-FA-FFH_AJ2014_C204_FC_MOPF_C231_FFH_EF1_2011-11-10.pdf 
 
7.2.6 Dateinamen Bibliotheken und  Softwareerweiterungen 
Symbolbibliothek 
MBC-E3-<E3-Cable-Version>-DAG-Datenbank-<Datum in TT.MM.JJ>.mdb 
Konfigurationsdatei 
MBC-E3-< E3-Cable-Version >-Projekteinstellungen.e3t 
XML Datei aus CONNECT 
E3Export_complete.xml (Full- Export) 
E3Export_delta_of_delta.xml 
(Delta- Export) 
ELOG- Konfigurationsdatei 
MBC-E3-ELOGAttributMapping.xml 
Softwareerweiterungen 
ConnyE-<Version> 
< E3-Cable-Version > 
zeigt die Kompatibilität zur eingesetzten Software an 
mdb 
Access Datenbankformat 
 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 46 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 46von 50 
 
7.3 Generischer Export 
Die Schaltplan XML- Datei ist ein elektronisches Abbild des Schaltplans und beschreibt  die Verbindungen und 
Komponenten mit allen verfügbaren Attributen. Zusätzlich sind noch die Zeichnungskopfinformationen enthalten.  
 
Der Export wird als Grundlage des Leitungssatz-  Entwicklungsprozess sowie  zur Dokumentation und  für interne 
Recherchen in der Connect Harness- Datenbank bei MBC verwendet.  
 
Beim generischen Export werden alle Daten eines Schaltplanprojektes in ein definiertes XML- erzeugt inklusive 
aller Einzelschaltpläne sowie den Gesamtschaltplan als PDF. Im Nachgang können auf das XML-File  
unterschiedliche Transformationen angewendet werden die die unterschiedlichen Formate erstellen. Die 
Lieferanten können eigenverantwortlich ihre eigene Transformation einstellen. Somit ist für alle Parteien eine 
flexible Anpassung an neue fachliche Anforderungen gegeben.  
 
 
Abbildung 29: Generischer Export 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 47 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 47von 50 
 
8 Mitgeltende Unterlagen  
8.1 Normen und Vorschriften  
 
Unterlage/Dokument Inhalte/ Benennung 
MBN 31 001 
Grundlagen für Konstruktions-Zeichnungen 
MBN 31 002 
CAD-Zeichnungen; Beschriftung, Schriftarten und Schriftgrößen 
IEC   60 757 
Farbkennzeichnung von Leitungen 
DIN EN 28 601 
Datenelemente und Austauschformate Informationsaustausch, Darstellung von Datum und Uhrzeit 
VA 059 EI08 
VZK, Bauteilkurzbezeichnungen (Verwendungszweck) 
 
8.2 Abkürzungen und Begriffe 
 
Abkürzung 
Benennung / Bemerkung 
Ausführungsart 
 Beschreibt die Karosserieform der Fahrzeuge 
Baumuster 
Baureihen oder Aggregate- Kennzeichen der Fahrzeugstückliste 
Basisschaltplan 
Ein einzelner Systemschaltplan (z.B. Außenbeleuchtung vorne LED / STAR2.2), bestehend 
aus 1-n Blättern im iSPM-Schaltplanprojekt 
Baureihenbasisprojekt Die Baureihen-spezifische Ausleitung aus dem iSPM-Schaltplanprojekt, welche die 
Grundlage für das Baureihenprojekt bildet (mehrere Basisschaltpläne). 
Baureihenschaltplan 
Ein einzelner Systemschaltplan (z.B. Außenbeleuchtung vorne LED), bestehend aus 1-n 
Blättern im Baureihenprojekt 
Baureihenprojekt 
Die Weiterentwicklung des Baureihenbasisprojekts in der Serienentwicklung 
BMKL 
B-Muster Klausur 
CADDS 
CAD- System zur Leitungssatzentwicklung 
Datenstand 
Datum der maschinellen Zeichnungsbearbeitung im  Format DIN EN 28 601 , Siehe auch 
Kapitel Prozesssicherheit  
Connect  
Bauteiledatenbank der Leitungssatzentwicklung 
ConnyE 
Container für Softwareanpassungen der MBC 
E3.Cable 
Schaltplaneditor der Firma ZUKEN 
EPDM 
Elektronik-Produktdatenmanagement 
Fahrzeugphase 
Entwicklungsstufe eines Fahrzeugs (z.B. E- Fzg, Serie, Änderungsjahr, MOPF) 
Feldkenner 
Koordinatenfelder in Konstruktions-Zeichnungen, Beispiel YX = A-Z1-N 
Freigabezeichnung 
Zeichnung die in Smaragd mit Auftrag und ZGS freigegeben wird 
GSP 
Global Service Parts , Service Organisation der MBC 
IP Code  
Offizielle Verkaufscode der Daimler AG, Sparte PKW 
iSPM 
integriertes SchaltPlanManagement 
Kennbuchstabe 
Klassifiziert Sachnummern f. Teile, Code, Baumuster usw. 
KBL 
Kabelbaumliste nach ISO 10303- AP212 Spezifikation 
MBC 
Mercedes Benz Car / Development 
PDF 
Portable Document Format, plattformübergreifendes Dateiformat 
RD/EKL 
Abteilung „Leitungssätze und Digitale Entwicklung“ 
RSync 
Remote Synchronisation , Verfahren zum Synchronisieren von Bibliotheken 
SVG 
Scalable Vector Graphics, Skalierbare Vektorgrafiken 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 48 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 48von 50 
 
SRM 
Sachnummern Recherche Management: System in dem die Teilenummern vergeben 
werden 
Step AP212 
Step, Standard fort the exchange of Product Data ISO 10303-212 
AP212,  Application Protocol for Electro technical design and installation  
U-Code 
Bereichsinterne Code zur Steuerung von Leitungen im Schaltplan 
VZK 
Verwendungszweck, Katalog der Bauteilkurzbezeichnungen (REF) 
XML 
extensible mark-up language 
ZGS 
Zeichnungs- und Geometrie- Stand 
 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 49 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 49von 50 
 
 
8.3 Abbildungsverzeichnis 
Abbildung 1: Zeichnungskopf inkl. Hinweise .................................................................................................................. 11 
Abbildung 2: Externes Änderungsfeld ............................................................................................................................. 12 
Abbildung 3: Schalplanlayout ........................................................................................................................................... 14 
Abbildung 4: Grafische Darstellung von Verdrillung und Schirmung ............................................................................. 15 
Abbildung 5: Grafische Unterscheidung von Ausführungsarten .................................................................................... 16 
Abbildung 6: Ausführungsarten in den Verbindungseigenschaften ............................................................................... 17 
Abbildung 7: Stecker- Referenz ....................................................................................................................................... 17 
Abbildung 8: Trennstelle    Abbildung 9: Kabelschwanzkomponente ......................................................................... 18 
Abbildung 10: Variantenbehaftete Steckernamen.......................................................................................................... 19 
Abbildung 11: Sicherungsblock ....................................................................................................................................... 21 
Abbildung 12: Leitungsnummern ..................................................................................................................................... 22 
Abbildung 13: Symbol für Verdrillungen .......................................................................................................................... 24 
Abbildung 14: Symbol für Schirme .................................................................................................................................. 24 
Abbildung 15: Signal- und Klemmenbezeichnung........................................................................................................... 25 
Abbildung 16: Slotdarstellung .......................................................................................................................................... 26 
Abbildung 17: Nicht angeschlagene Schirme ................................................................................................................. 27 
Abbildung 18: Schirmanschluss am Gehäuse ................................................................................................................. 28 
Abbildung 19: Schlaufendarstellung ................................................................................................................................ 29 
Abbildung 20 Leitungssatzentwicklungs Prozess mit iSPM ........................................................................................... 32 
Abbildung 21: iSPM Prozess ............................................................................................................................................ 33 
Abbildung 22: Vergleich Basisschaltplan und BR- Schaltplan an KZL und 3. Bremsleuchte ....................................... 34 
Abbildung 23: Prinzip der Gruppierung der Systeme ..................................................................................................... 35 
Abbildung 24: Hardwarevarianten am Beispiel Heckleuchte ......................................................................................... 36 
Abbildung 25: Leitungsauslegung .................................................................................................................................... 38 
Abbildung 26: Datenbereitstellung Schaltpläne ............................................................................................................. 40 
Abbildung 27: Verwendung (Baureihe, Ausführungsart, Fahrzeugphase) ..................................................................... 42 
Abbildung 28 Schaltplansachnummer ............................................................................................................................. 43 
Abbildung 29: Generischer Export ................................................................................................................................... 46 
Abbildung 30: Übersicht der Slotkomponenten.............................................................................................................. 50 
 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)


### 第 50 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Schaltplanerstellung 
 
A 000 006 98 99 
 
Bearb./auth.: Redinger 
 
Abt./dep.: RD/ EKL 
 
Datum/date: 2015-08-28 
 
ZGS: 007 
Auftr.-Nr./order  no.: YAP37939/15 
 
Seite/page: 50von 50 
 
9 Anhang 
 
Abbildung 30: Übersicht der Slotkomponenten 
 
 
Unkontrollierte Kopie bei Ausdruck (: Yinfeng Yan, 2016-01-06)

