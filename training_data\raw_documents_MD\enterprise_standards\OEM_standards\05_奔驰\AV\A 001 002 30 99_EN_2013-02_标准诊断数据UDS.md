# A 001 002 30 99_EN_2013-02_标准诊断数据UDS.pdf

## 文档信息
- 标题：<TITEL>
- 作者：<PERSON><PERSON><PERSON>, <PERSON><PERSON> (059)
- 页数：112

## 文档内容
### 第 1 页
 
 
A0010023099: 2013-02, page 1 
 
 
Auftr.-Nr./order no.   
 
System / system 
 
fed.Abt./resp. 
dep.  
GSP/OVE 
ZGS   
007 
ED-KB   
PY 
CAD     
- 
Datum / date 
Name / name 
Benennung / title  
Standardized Diagnostic Data UDS  
Bearb./auth
 
2013-02-26 
Pfaff 
Pruef/check 2013-02-26 
Bortolus 
Norm/stand
 
2013-02 
 
Freig./rel. 
2013-02-26 
<PERSON><PERSON> <PERSON> 
 
DAIMLER  
Aktiengesellschaft 
 
© Daimler AG 
Schutzvermerk  DIN  34  beachten!  /  
Refer  to  protection  notice   DIN 34! 
Format / size 
A4 
Blatt / sh. 
 
Sach-Nr. / basic number 
A 001 002 30 99 
Keine   Aenderung   ohne   Zustimmung   der   federfuehrenden   Konstruktion.   /   Any   alterations   are   subject   to   the   approval   of   the   de-
sign   department. 
 
 
 
 
 
 
 
 
 
 
 
 
 
  
 
Ausführungsvorschrift 
 
 
Stand: 26.02.2013 
 
 
 
 
 
DAIMLER AG 
Aktiengesellschaft 
 
 
DIAGNOSTIC DEVELOPMENT 
 
 
Standardized Diagnostic Data 
 
UDS 
 
RELEASE 2.7 
 
Uncontrolled copy when printed (: Yiping <PERSON>, 2013-08-22)


### 第 2 页
A0010023099: 2013-02, page 1 
 
 
Copyright Daimler AG 2013  
Contents 
 
1 
Scope ............................................................................................................................................. 4 
2 
References ..................................................................................................................................... 5 
3 
Terms and Definitions .................................................................................................................... 6 
3.1 
Definitions ................................................................................................................................... 6 
3.2 
Abbreviations and Acronyms ...................................................................................................... 7 
4 
General information ........................................................................................................................ 8 
4.1 
Data encoding ............................................................................................................................. 8 
4.2 
Document conventions ............................................................................................................... 8 
4.2.2 
Definition of Parameter support ....................................................................................... 9 
5 
Standardized Data Identifiers ....................................................................................................... 10 
5.1 
Network Configuration Data ..................................................................................................... 10 
5.1.1 
Network Configuration [F0 10 - F0 14]  r/w .................................................................... 10 
5.1.2 
Removed ........................................................................................................................ 11 
5.1.3 
Activate Partial Networking  [ 01 30 ]   r/w ..................................................................... 11 
5.2 
ECU Identification ..................................................................................................................... 11 
5.2.1 
Active Diagnostic Information [F1 00 ]  r ........................................................................ 11 
5.2.2 
Removed ........................................................................................................................ 12 
5.2.3 
ECU Configuration [F1 0B ]  r/w .................................................................................... 12 
5.2.4 
Diagnostic Specification Information [ F1 0D ]  r ........................................................... 13 
5.2.5 
Diagnostic Specification Version Information - Document Specific [F1 A1 - F1 A5]  r .. 16 
5.2.6 
Hardware Part Number - Business Unit Specific [F1 11 - F1 1F]  r ............................... 19 
5.2.7 
Software Part Numbers - Business Unit Specific  [F1 21 - F1 2F ]  r ............................ 20 
5.2.8 
Removed ........................................................................................................................ 22 
5.2.9 
Hardware Version Information [ F1 50 ]  r ..................................................................... 22 
5.2.10 
Software Version Information [ F1 51 ]  r ....................................................................... 23 
5.2.11 
Boot software Version Information [ F1 53 ]   r .............................................................. 25 
5.2.12 
Hardware Supplier Identification [ F1 54 ]    r ................................................................ 26 
5.2.13 
Commercial Vehicle Engine Number  [ F1 57 ]   r/w...................................................... 26 
5.2.14 
Removed ........................................................................................................................ 27 
5.2.15 
ECU Model Type  [ F1 59 ]   r ........................................................................................ 28 
5.2.16 
Write Software Fingerprint  [ F1  5A ]  w ........................................................................ 28 
5.2.17 
Read Software Fingerprint(s)  [ F1 5B ]    r .................................................................... 29 
5.2.18 
Write Configuration Fingerprint  [ F1 5C ] w .................................................................. 32 
5.2.19 
Read Configuration Fingerprint [ F1 5D] r ..................................................................... 32 
5.2.20 
Write Routine I/O Fingerprint  [ F1 5E ] w ...................................................................... 33 
5.2.21 
Read Routine I/O Fingerprint  [ F1 5F ] r ....................................................................... 34 
5.2.22 
ECU Serial Number [ F1 8C ] r ...................................................................................... 35 
5.2.23 
VIN Original  [ F1 90 ] r .................................................................................................. 36 
5.2.24 
VIN Current  [ F1 A0 ]   r/w ............................................................................................ 36 
5.2.25 
Exhaust Regulation or Type Approval Number (EROTAN) [ F1 96 ] r/w....................... 37 
5.2.26 
Software Supplier Identification [ F1 55 ]    r .................................................................. 37 
5.2.27 
FINAS Number [ F1 01 ]    r/w ....................................................................................... 38 
5.2.28 
Technical Time Stamp  [F1 02 ]    r/w ............................................................................ 39 
5.2.29 
FDOK Safety Relevant Information  [F1 03 ]    r ............................................................ 39 
5.2.30 
FlexRay Node Information  [F1 A6]    r .......................................................................... 40 
5.3 
Development Data .................................................................................................................... 41 
5.3.1 
OSEK - Module information [ F1 0F ]   r ........................................................................ 41 
5.3.2 
SW Module Information  [F1 60 - F1 6F ]   r .................................................................. 41 
5.3.3 
Physical Layer Channel Configuration [F1 70 - F1 7F ]    r ........................................... 43 
5.3.4 
SW-Module Identification AUTOSAR/DAIMLER ........................................................... 44 
5.3.5 
Standard FlashBootLoader SW Package Information  [ EF 02 ]  r ................................ 46 
5.3.6 
Standard SW Package Information  [ EF 03 ]  r ............................................................ 46 
5.3.7 
FlexRay Configuration Information  [F1 0E ]    r ............................................................ 47 
5.3.8 
DMR_Timeout [ 01 2E ] r/w ........................................................................................... 50 
5.3.9 
Read VMM Version [EF 04] r ......................................................................................... 50 
5.3.10 
Read LIN Slope Mode [01 0F] r ..................................................................................... 50 
5.3.11 
Activate Supplier Specific Messages [01 2A] r/w .......................................................... 51 
5.4 
Supplier specific data................................................................................................................ 51 
5.4.1 
System Supplier ECU Hardware Part Number  [ F1 92 ]   r .......................................... 51 
5.4.2 
System Supplier ECU Hardware Version Number [ F1 93 ]    r ..................................... 52 
5.4.3 
System Supplier ECU Software Part Number  [ F1 94 ]    r ........................................... 52 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 3 页
A0010023099: 2013-02, page 2 
 
 
Copyright Daimler AG 2013  
5.4.4 
System Supplier ECU Software Version Number  [ F1 95 ]     r .................................... 52 
5.5 
Predefined Manufacturer Specific Data Identifier ..................................................................... 52 
5.5.1 
Reprogramming Attempt Counter  [ 01 00 ]    r ............................................................. 52 
5.5.2 
Historical Interrogation Record  [ 01 01 ]   r ................................................................... 54 
5.5.3 
Diagnostic Trace Memory  [ 01 02 ]    r/w ...................................................................... 54 
5.5.4 
VIN Odometer [ 01 03 ] r/w ............................................................................................ 55 
5.5.5 
VIN Odometer Limit  [ 01 04 ] r/w .................................................................................. 55 
5.5.6 
In-use Histogram  [ 01 05 ]   r ........................................................................................ 56 
5.5.7 
Network Mirroring Mode Configuration  [ 01 06 ]   r/w ................................................... 57 
5.5.8 
Removed ........................................................................................................................ 57 
5.5.9 
Removed ........................................................................................................................ 57 
5.5.10 
Configuration Write Counter  [ 01 09 ]   r ....................................................................... 57 
5.5.11 
Most Diagnostic Routing Table  [ 01 0A]    r .................................................................. 58 
5.5.12 
Adjust ISO 15765-2 Block Size and STmin Parameter [ 01 0B ]  r/w ............................ 59 
5.5.13 
Removed ........................................................................................................................ 59 
5.5.14 
DPM Status Information  [01 10 - 01 1F ]   r .................................................................. 59 
5.5.15 
Vehicle Configuration Programmed Status / Last Programmed Status [ 01 20 ] .......... 63 
5.5.16 
Vehicle Configuration Write Counter [ 01 21 ] ............................................................... 63 
5.5.17 
Vehicle Configuration [01 22 - 01 2B ] ........................................................................... 63 
5.5.18 
Moved to 5.6 .................................................................................................................. 63 
5.5.19 
Moved to 5.6.1 ............................................................................................................... 63 
5.5.20 
Moved to 5.6.2 ............................................................................................................... 63 
5.5.21 
Read Used EVC Config  [01 0D]    r .............................................................................. 63 
5.5.22 
Read Odometer value from Bus [ 01 0C ] r ................................................................... 64 
5.5.23 
High Voltage Lock  [ 01 2C ]   r/w .................................................................................. 64 
5.5.24 
Engine Style [ 01 2D ] r .................................................................................................. 65 
5.5.25 
Adjust ISO 10681-2 Bandwidth Control Parameter [ 01 2B ]  r/w .................................. 66 
5.5.26 
HighVoltageValue  [ 01 2F ]   r ....................................................................................... 67 
5.5.27 
Compatibility List (for Onboard Configuration) [ E0 00 ]   r ............................................ 67 
5.5.28 
GVC Update Time Stamp [ E0 01 ]   r ........................................................................... 68 
5.5.29 
Read Discrete Signals [ 1E xx ]   r ................................................................................. 68 
5.5.30 
Read Analog Signals [ 1D xx ]   r ................................................................................... 69 
5.6 
OBD Info Types ........................................................................................................................ 70 
5.6.1 
Calibration Identifications (CAL ID)  [ F8 04 ]   r/w ........................................................ 70 
5.6.2 
Calibration Verification Numbers (CVN)  [ F8 06 ]   r ..................................................... 71 
5.6.3 
RBM HEX Gasoline [ F8 08 ]   r ..................................................................................... 71 
5.6.4 
RBM HEX Diesel [ F8 0B ]   r ......................................................................................... 72 
6 
Standardized Routine Identifiers .................................................................................................. 73 
6.1 
Synchronous and Asynchronous Routines .............................................................................. 73 
6.1.1 
Synchronous Routine Operating Mode ......................................................................... 73 
6.1.2 
Asynchronous Routine Operating Mode ........................................................................ 73 
6.2 
Predefined Routine Identifiers .................................................................................................. 74 
6.2.1 
Erase Memory  [ FF 00 ] ................................................................................................ 74 
6.2.2 
Check Routine  [ FF 01 ] ................................................................................................ 75 
6.2.3 
Erase Mirror Memory DTCs  [ FF 02 ] ........................................................................... 78 
6.2.4 
Check Programming Preconditions  [ FF 03 ] ................................................................ 79 
6.2.5 
Check Memory  [ FF 04 ] ............................................................................................... 80 
6.2.6 
Control  Fail Safe Reactions  [ FF 05 ] .......................................................................... 83 
6.2.7 
CheckCompatibilityDependencies  [ FF 06 ] ................................................................. 84 
6.3 
Predefined Manufacturer Specific Routine Identifiers .............................................................. 85 
6.3.1 
Removed ........................................................................................................................ 85 
6.3.2 
Removed ........................................................................................................................ 85 
6.3.3 
Removed ........................................................................................................................ 85 
6.3.4 
Reset VIN Values [ 02 12 ] ............................................................................................ 85 
6.3.5 
Removed ........................................................................................................................ 86 
6.3.6 
Removed ........................................................................................................................ 86 
6.3.7 
Removed ........................................................................................................................ 86 
6.3.8 
Removed ........................................................................................................................ 86 
6.3.9 
EVC Protected Reset [02 01]......................................................................................... 86 
6.3.10 
Switch Test Mode  [ 02 18 ] ........................................................................................... 86 
6.3.11 
ECU self-test   [ 02 19 ] .................................................................................................. 87 
6.3.12 
FBS4 specific routines (Details defined in FBS4 Specification) [02 30 - 02 3F] ............ 88 
6.4 
Removed .................................................................................................................................. 88 
6.5 
Reserved Daimler Trucks Specific Routines  [ 02 20 - 02 2F ] ................................................ 88 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 4 页
A0010023099: 2013-02, page 3 
 
 
Copyright Daimler AG 2013  
7 
Read DTC Environmental Data .................................................................................................... 89 
7.1 
Report Chrono Stack Environmental Data ............................................................................... 89 
7.1.1 
Standardized Environmental Data ................................................................................. 89 
7.1.2 
Optional Environmental Data ......................................................................................... 90 
7.2 
Report Historical Stack Environmental Data ............................................................................ 92 
7.2.1 
Standardized Environmental Data ................................................................................. 92 
7.2.2 
Optional Environmental Data ......................................................................................... 92 
8 
DID and RID Implementation Conventions .................................................................................. 94 
8.1 
Overview - Data Identifiers ....................................................................................................... 94 
8.2 
Overview - Routine Identifiers .................................................................................................. 99 
9 
Vehicle Line Identification (removed) ......................................................................................... 103 
10 
Physical Layer Channel Configuration Encoding ................................................................... 104 
11 
Supplier Identification Table ................................................................................................... 107 
12 
AUTOSAR Modul Encoding ................................................................................................... 110 
13 
ANNEX A ................................................................................................................................ 111 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 5 页
A0010023099: 2013-02, page 4 
 
 
Copyright Daimler AG 2013  
1 
Scope 
The goal of this document is to standardize the data identifiers and the routine identifiers 
implemented by Electronic Control Units (ECUs) and is intended for use by electrical/electronic (E/E) 
engineering development teams and corporate suppliers in the design of Electronic Control Units 
ECUs designed for use in all Daimler applications when Unified Diagnostic Service (UDS) diagnostic 
protocol (as defined in MBN 10747) is implemented. 
The document is split into sections for Standardized Data Identifiers (including manufacturer specific, 
system supplier specific, and OBD specific data identifiers), Standardized Routine Identifiers, and 
Read DTC Environmental Data.   
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 6 页
A0010023099: 2013-02, page 5 
 
 
Copyright Daimler AG 2013  
2 
References 
MBN 10746 
Diagnostic Performance Requirements Standard 
MBN 10747 
Unified Diagnostic Services – Diagnostic Protocol 
MBN 10761 
Flash Re-programming Requirements Definition based on UDS 
MBN 10482 
Gateway Diagnostic Requirements 
MBN 10412 
LIN 2.1 Diagnostic Requirements 
ISO15765-2 
Road vehicles – Road vehicles – Diagnostics on Controller Area 
Networks (CAN) – Part 2: Network layer services 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 7 页
A0010023099: 2013-02, page 6 
 
 
Copyright Daimler AG 2013  
3 
Terms and Definitions 
3.1 
Definitions 
 
Detected Network 
Configuration 
The Detected Network Configuration of an individual network connected to a 
vehicle gateway represents the ECUs which are currently detected by the 
gateway as present on the network. 
 
External Test Tool 
The device that is used as an external or internal diagnostic test device 
performing the client functionality defined in this protocol standard. 
 
Least Significant 
Bit/Byte 
The bit/byte in a multiple bit/byte binary number that is farthest to the right, or 
the bit/byte transmitted last in a sequence. 
 
Most Significant 
Bit/Byte 
The bit/byte in a multiple bit/byte binary number that is farthest to the left, or 
the bit/byte transmitted first in a sequence. 
 
Programmed Network 
Configuration 
The programmed network configuration of an individual network connected 
to a vehicle gateway represents the ECUs which were installed on the 
vehicle and are expected to be present in the Detected Network 
Configuration. 
 
Vehicle Configuration 
Backup ECU 
The ECU which provides vehicle configuration to the Central Gateway ECU 
when the Central Gateway ECU is replaced. 
 
Vehicle Gateway 
A Vehicle Gateway is defined as the generic term for all gateway types 
independent from the specific networks which are connected. 
 
Supplier 
The producer of an ECU Software and/or ECU hardware. In case of tool 
supplier this also identifies the producer   
 
Manufacturer 
Shall be the term for the manufacturer of the vehicle. IIdentifies the 
responsible business unit of Daimler AG. 
 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 8 页
A0010023099: 2013-02, page 7 
 
 
Copyright Daimler AG 2013  
3.2 
Abbreviations and Acronyms 
 
 
CBD 
CANbedded delivery 
Daimler Trucks 
Includes DTNA, DDC, MB-Trucks,  and MFTBC  
DDC 
Detroit Diesel Corporation 
DID 
Data Identifier 
DPRS 
Diagnostic Requirements Performance Standard 
DTC 
Diagnostic Trouble Code 
DTNA 
Daimler Trucks North America 
ECU 
Electronic Control Unit 
EVC 
Extended Vehicle Configuration 
FBL 
FlashBootLoader 
FRFM 
Fault Retention & Fault Management 
LSB 
Least Significant Bit/Byte 
MBC 
Mercedes-Benz Cars 
MB-Truck 
Mercedes-Benz Trucks 
MFTBC 
Mitsubishi Fuso Truck & Bus Corporation 
MSB 
Most Significant Bit/Byte 
r 
Data Identifier is readable 
r/w 
Data Identifier is readable/writeable 
RID 
Routine Identifier 
SIP 
Software Integration Package 
SW 
Software 
UDS 
Unified Diagnostic Services – Diagnostic Protocol 
w 
Data Identifier is writeable 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 9 页
A0010023099: 2013-02, page 8 
 
 
Copyright Daimler AG 2013  
4 
General information 
This section provides information on how this document shall be read and how the individual values 
are encoded with regards to bit and byte positions. 
 
4.1 
Data encoding 
The numbering of data bytes associated with a specific data identifier, routine identifier or extended 
data record is counted from zero (0) in relative position to the end of the preceding protocol 
overhead. For example when starting to count from zero (0) the first data byte in a data identifier 
which was read via service [22 Hex] may have the following absolute byte positions depending on 
which protocol overhead is included: 
 
• 
Position two (2) when starting to count from zero (0) including the data identifier 
• 
Position three (3) when starting to count from zero (0) including the service identifier and the 
data identifier 
 
To prevent any confusion based on the point of view when counting, the first data byte shall always 
be counted from zero (0) excluding any protocol overhead and then be incremented as necessary. 
All bit encoded values are encoded in Motorola format: 
 
• 
Bit 0: 
Least significant bit 
• 
Bit 7 / 15 / 31: Most significant bit for 8 / 16 / 32 bit value 
 
The complete value range of any parameter defined in this document shall be considered to be 
reserved if not explicitly allowed to be used via either one of the columns "Parameter name / 
Description" or column "Hex range". Reserved values shall not be used for any purpose as they may 
be re-defined in future versions of this document. 
 
 
4.2 
Document conventions 
******* 
Definition of Service support 
For each individual Data Identifier, Routine Identifier, or Extended Data Record described in this 
document it is explicitly stated under which condition it shall be supported. Please refer to Table 89 
and Table 90  for details. 
  
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 10 页
A0010023099: 2013-02, page 9 
 
 
Copyright Daimler AG 2013  
Possible support types are: 
 
• 
Required (R):   
The respective Data Identifier, Routine Identifier or Extended Data 
Record shall be supported by all ECUs 
• 
Conditional (C): The respective Data Identifier, Routine Identifier or Extended Data 
Record shall only be supported if the described conditions apply 
• 
Optional (O):  
It is up to the ECU supplier's discretion whether to implement the 
specified data identifier, routine identifier or extended data record or 
not. However; if it is implemented, the message structure shall follow 
the definitions provided in this document. 
 
ECU designed for use in multiple business units shall implement all the Data Identifiers and Routine 
Identifiers defined for the respective business units. 
 
 
4.2.2 
Definition of Parameter support 
Each data identifier, routine identifier or extended data record is defined in a table. The column titled 
“Sup” (parameter support type) in this table determines under which conditions each message 
parameter is present in the message. Possible parameter support types are: 
 
• 
Mandatory (M):  The message parameter is always present in the message. 
 
• 
Conditional (C):  Specific conditions have to be fulfilled for the message parameter to be 
present. Each condition is described in greater detail at the bottom of 
each table. 
 
NOTE: The parameter support type is not to be confused with the support type. For example, a data 
identifier of support type "Required" may have parameters which are of parameter support type 
"Conditional" 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 11 页
A0010023099: 2013-02, page 10 
 
 
Copyright Daimler AG 2013  
5 
Standardized Data Identifiers 
This chapter describes predefined diagnostic data contents assigned to specific Data Identifiers in 
order to provide basic diagnostic functionality. 
 
5.1 
Network Configuration Data 
5.1.1 
Network Configuration [F0 10 - F0 14]  r/w 
DID [Hex]: F0 10 - F0 14 
Description: 
The data identifiers described in this section contain information about the ECUs on the various 
communication networks connected to a gateway ECU.  The content of the data records for these 
data identifiers are programmed at assembly and are not dynamically updated.  Refer to the 
Gateway Diagnostic Requirement specification for additional information regarding the specific 
usage of these data identifiers. Refer to Chapter 8 for information regarding which data identifier to 
use for the various connected networks. 
Message structure:  
Upon requesting a specific Network Configuration data identifier with the diagnostic service Read 
Data By Identifier [22 Hex], the ECU shall return the status of the Network Configuration for the 
specified network. Upon requesting specific Network Configuration data identifier with the diagnostic 
service Write Data by Identifier [2E Hex], the ECU shall write the Network Configuration information 
to the respective memory location. The data content associated with the data identifier shall match 
Table 1.  The assignment of ECUs to the various bit positions are controlled by the respective 
business unit diagnostic development team. 
 
Table 1 : Positive Response/ Request  Data Record Definition - Network Configuration 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex 
Range Encoding 
0 
Network Configuration Data Byte #0 
M 
00-FF 
Hex 
  
Bit 0 – ECU 1 (0=False, 1=True) 
  
  
  
Bit 1 – ECU 2 (0=False, 1=True) 
Bit 2 – ECU 3 (0=False, 1=True) 
: 
Bit 7 – ECU 8 (0=False, 1=True) 
: 
: 
 
 
 
n-1 
Network Configuration Data Byte #n-1 
C 
00-FF 
Hex 
  
Bit 0 – ECU 8(n-1) + 1 (0=False, 1=True) 
  
  
  
Bit 1 – ECU 8(n-1)  + 2  (0=False, 1=True) 
Bit 2 – ECU 8(n-1)  + 3 (0=False, 1=True) 
: 
Bit 7 – ECU 8(n-1) + 8 (0=False, 1=True) 
 
 
n: maximum number of Network Configuration bytes required. 
C: These message data parameters shall only be supported if more than one (1) byte is required to 
report the Network Configuration. 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 12 页
A0010023099: 2013-02, page 11 
 
 
Copyright Daimler AG 2013  
If the Network Configuration was not previously written to the ECU by the external testtool, the ECU 
shall report FFh in every "Network Configuration Data Byte" 
 
5.1.2 
Removed 
Removed  Detected Network Configuration [F0 80 - F0 84] 
 
5.1.3 
Activate Partial Networking  [ 01 30 ]   r/w 
DID [Hex]: 01 30 
Description: 
This data identifier is used to activate and deactivate the Partial Networking Mode  It is also used to 
return the current configuration of the Partial Networking Mode. 
Message structure:  
Upon requesting data identifier [01 30 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the current Partial Networking Mode state. Upon requesting  
[01 30 Hex] with the diagnostic service Write Data by Identifier [2E Hex], the ECU shall write the 
Partial Networking Mode state to the respective memory location in the non-volatile memory. The 
data content associated with the data identifier shall match Table 114. 
 
Table 114 : Request/Response Data Record Definition - Activate Partial Networking 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex value Encoding 
0 
 
 
Partial Networking Mode 
 
00: inactive 
 
01: active 
M 
 
 
00-01 
 
 
Enum 
 
 
 
 
 
5.2 
ECU Identification 
5.2.1 
Active Diagnostic Information [F1 00 ]  r 
DID [Hex]: F1 00 
Description:  
This data identifier contains information which is used by the diagnostic tool to determine in which 
diagnostic session an ECU is currently operating and whether it is operating in the normal application 
mode or in the boot loader. Furthermore the diagnostic variant and diagnostic version information is 
used for the diagnostic tool to use the associated diagnostic data description to correctly perform 
diagnostic communication with the ECU. 
Message structure:  
Upon requesting [F1 00 Hex] with diagnostic service Read Data by Identifier [22 Hex], the ECU shall 
report the Active Diagnostic Information needed by the diagnostic tool to uniquely identify the 
diagnostic status of the ECU. The data content associated with the data identifier shall match  
Table 3. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 13 页
A0010023099: 2013-02, page 12 
 
 
Copyright Daimler AG 2013  
Table 3 :  Positive Response Data Record Definition - Active Diagnostic Information 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Hex 
Range 
Encoding 
0 
Active Diagnostic Status Of ECU 
M 
00-01 
Hex 
 
Bit 0 – ECU Software Mode  
0=Application  
1=Boot 
Bit 1 – reserved (set to zero) 
: 
Bit 7 – reserved (set to zero) 
 
 
 
1 
Active Diagnostic Variant 
M 
00-FF 
Hex 
 
This byte represents the ECU Identification. 
These bits are used to distinguish ECU’s that have the same 
ECU address (e.g. diagnostic CAN identifier) and the same 
diagnostic protocol and the same supplier code. 
 
 
 
2 
Active Diagnostic Version 
M 
00-FF 
Hex 
 
This byte represents the Diagnostic Version of an ECU. 
 
The Diagnostic Version identifies the version of the current 
diagnostic implementation.  
The starting value is equal to zero [00 Hex] and has to be 
incremented with every diagnostics relevant software change in 
the ECU (i.e. diagnostic tool / diagnostic data description).  
 
Note: A starting value of zero [00 Hex] shall be used for both 
the boot software and the application software as Active 
Diagnostic Version value. The distinctive feature for boot and 
application is the ECU Software Mode. 
 
 
 
3 
Active Diagnostic Session 
M 
01-7F 
Hex 
 
This byte represents the diagnostic session type, which the 
ECU is currently running. The encoding of this session type 
shall comply with the definitions in MBN 10747 (e.g. after 
executing Diagnostic Session Control service requesting 
Diagnostic Session Type “Extended Diagnostic Session” the 
reported value shall be [03 Hex] 
 
Note: The Suppress Positive Response Bit (Bit No. 7) shall be 
suppressed when reporting the Active Diagnostic Session and 
shall always set to zero (0). 
 
 
 
 
 
 
5.2.2 
Removed 
Removed  ECU Origin [ F1 0A ] 
 
5.2.3 
ECU Configuration [F1 0B ]  r/w 
DID [Hex]: F1 0B 
Description: 
This data identifier and the associated data records shall be supported if multiple hardware or 
software configurations are provided by the ECU, to reflect which of the features or functions are 
enabled within the ECU’s software for the specific vehicle if the ECU’s software implements several 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 14 页
A0010023099: 2013-02, page 13 
 
 
Copyright Daimler AG 2013  
features or functions which can be enabled separately. This does not imply that an ECU must take 
specific actions based on the contents of this data identifier. This information is used by the 
diagnostic tool to determine which data items within the same data identifier are to be suppressed or 
not used by the diagnostic tool since they may not provide valid data due to features or functions that 
are disabled. 
Message structure:  
Upon requesting [F1 0B Hex] with the diagnostic service Read Data By Identifier [22 Hex], the ECU 
shall return the information about it’s configuration. The format of the Record Data shall follow the 
definition in Table 4. 
Upon requesting [F1 0B Hex] with the diagnostic service Write Data By Identifier [2E Hex], the ECU 
shall write the ECU Configuration information  to the respective memory location. The associated 
data content  shall match the data defined in Table 5 
 
Table 4 : Positive Response Data Record Definition - ECU Configuration 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Hex 
Range Encoding 
0 
ECU Configuration Data Byte #0 
M 
00-FF 
Hex 
Bit 0 – Configuration Data Item 1 (0=False, 1=True) 
Bit 1 – Configuration Data Item 2 (0=False, 1=True) 
Bit 2 – Configuration Data Item 3 (0=False, 1=True) 
: 
  
Bit 7 – Configuration Data Item 8 (0=False, 1=True) 
  
  
  
: 
: 
 
 
 
n-1 
ECU Configuration Data Byte #n-1 
C 
00-FF 
Hex 
Bit 0 – Configuration Data Item 8(n-1)+1 (0=False, 1=True) 
Bit 1 – Configuration Data Item 8(n-1)+2 (0=False, 1=True) 
Bit 2 – Configuration Data Item 8(n-1)+3 (0=False, 1=True) 
: 
  
Bit 7 – Configuration Data Item 8(n-1)+8 (0=False, 1=True) 
  
  
  
 
 
n: maximum number of required ECU configuration bytes 
C: These message data parameters shall only be supported if the ECU supports more than one ECU 
Configuration Data byte. 
 
 
5.2.4 
Diagnostic Specification Information [ F1 0D ]  r 
DID [Hex]: F1 0D 
Description: 
This data identifier and the associated data record reflect the diagnostic specification versions 
according to which the ECU diagnostic software was developed. Please also refer to section 5.2.5 
for additional Diagnostic Specification Version Information data identifiers. 
Message structure: 
Upon requesting [F1 0D Hex] with the diagnostic service Read Data By Identifier [22 Hex], the ECU 
shall return the information about the utilized diagnostic specification version. The data content 
associated with the data identifier shall match Table 5. 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 15 页
A0010023099: 2013-02, page 14 
 
 
Copyright Daimler AG 2013  
Table 5 :  Positive Response Data Record Definition - Diagnostic Specification Information 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
0 
Unified Diagnostic Services –  
Diagnostic Protocol Version  MBN 10747 
M 
00 – 5F 
Hex 
see 
Table 6 
1 
ECU Flash Reprogramming Requirements 
Definition Version  MBN 10761 
M 
60 – AF 
Hex 
see  
 Table 7 
2 
Diagnostic Performance Requirements Standard 
MBN 10746 
M 
B0 – FE 
Hex 
see  
Table 8 
 
 
 
 
Table 6 : Encoding MBN 10747  Version 
 
Protocol Version 
Encoding 
(Hex value) 
MBN 10747 / DC-10747  not applicable 
00 
MBN 10747 
01 
MBN 10747 Rev. A 
02 
MBN 10747 Rev. B 
03 
MBN 10747 Rev. C 
04 
MBN 10747 Rev. D 
05 
MBN 10747  2008-11 
06 
MBN 10747  2009-06 
07 
MBN 10747  2009-06 
08 
MBN 10747  2009-11 
09 
MBN 10747  2011-09 
0A 
MBN 10747  2012-06 
0B 
MBN 10747  2012-11 
0C 
MBN 10747  2013-02 
0D 
Reserved 
0E – 5F 
 
 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 16 页
A0010023099: 2013-02, page 15 
 
 
Copyright Daimler AG 2013  
Table 7 : Encoding MBN 10761 Version 
Protocol Version 
Encoding 
(Hex value) 
MBN 10761 / DC-10761 not applicable 
60 
MBN 10761 
61 
MBN 10761 Rev. A 
62 
MBN 10761 Rev. B 
63 
MBN 10761 Rev. C 
64 
MBN 10761 Rev. D 
65 
MBN 10761 Rev. E  2008-11 
66 
MBN 10761 2009-06 
67 
MBN 10761 2009-11 
68 
MBN 10761 2010-06 
69 
MBN 10761 2011-09 
6A 
MBN 10761 2012-06 
6B 
MBN 10761 2012-11 
6C 
MBN 10761 2013-02 
6D 
Reserved 
6E – AF 
 
 
Table 8 : Encoding MBN 10746 Version 
Protocol Version 
Encoding 
(Hex value) 
MBN 10746  / DC-70746 not applicable 
B0 
MBN 10746 
B1 
MBN 10746 Rev. A 
B2 
MBN 10746 Rev. B 
B3 
MBN 10746 Rev. C 
B4 
MBN 10746 Rev. D 
B5 
MBN 10746 2008-06 
B6 
MBN 10746  2008-11 
B7 
MBN 10746  2009-06 
B8 
MBN 10746 2009-11 
B9 
MBN 10746  2010-06 
BA 
MBN 10746 2011-09 
BB 
MBN 10746  2012-06 
BC 
MBN 10746  2012-11 
BD 
MBN 10746  2013-02 
BE 
Reserved 
BF - FE 
Not Supported 
FF 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 17 页
A0010023099: 2013-02, page 16 
 
 
Copyright Daimler AG 2013  
 
 
5.2.5 
Diagnostic Specification Version Information - Document Specific [F1 A1 - F1 A5]  r 
 
DID [Hex]: F1 A1 - F1 A5 
Description:  
These data identifiers are used to report the versions of each diagnostic specification implemented 
by an ECU.  
This is necessary to keep track of ECUs which are new developments and those which are carry 
over components from existing model-lines. Having each ECU report the version of each 
implemented diagnostic specification will aid in automatically adjusting diagnostic test tools to the 
specifics of these diagnostic specification versions. 
 
NOTE: For details regarding DID and its associated specification please refer to Table 9  and  
section 8.1 Overview - Data Identifiers. 
 
 
Table 9 :  Diagnostic Specification Version Information - DID and associated specification 
 
DID  
[Hex] 
Specification 
F1A1 
Diagnostic Specification Version Information - Gateway Diagnostic Requirements 
F1A2 
Diagnostic Specification Version Information - Standardized Diagnostic Data 
F1A3 
Diagnostic Specification Version Information - Response on Event - lite 
F1A4 
Diagnostic Specification Version Information - LIN 2.1 Diagnostic Requirements 
F1A5 
Diagnostic Specification Version Information - Diagnostics on FlexRay 
F1A6-F1AF 
Reserved for future Diagnostic Specification Version Information 
 
 
Message structure:  
Upon requesting a specific Diagnostic Specification Version Information data identifier with the 
diagnostic service Read Data By Identifier [22 Hex], the ECU shall return the associated version 
information of the requested diagnostic specification which was used to implement the diagnostic 
requirements of the ECU. The data content associated with the data identifier shall be formatted 
according to Table 10. 
 
Table 10 :  Positive Response Data Record Definition - Diagnostic Specification Version 
Information 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Range 
Encoding 
 
0 
1 
Specification Version 
 
Specification Version High Byte 
 
Specification Version Low Byte 
 
 
 
M 
 
0…65534 
 
(65535 
reserved) 
 
UINT16 
For details see 
Table 11 to 
Table 15 
 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 18 页
A0010023099: 2013-02, page 17 
 
 
Copyright Daimler AG 2013  
Table 11:  Encoding of  Gateway Diagnostic Requirements Version 
 
Specification 
Release 
Encoding 
[Specification 
Version] 
DC 11264   Gateway Diagnostic Requirements  
2007-01 
00 00 
DC 11264   Gateway Diagnostic Requirements  
2007-02 
00 01 
MBN 11264   Gateway Diagnostic Requirements  
2007-12 
00 02 
MBN 11264   Gateway Diagnostic Requirements  
2008-06 
00 03 
MBN 11264   Gateway Diagnostic Requirements  
2008-11 
00 04 
MBN 11264   Gateway Diagnostic Requirements  
2009-06 
00 05 
MBN 11264   Gateway Diagnostic Requirements  
2009-11 
00 06 
MBN 11264   Gateway Diagnostic Requirements  
2010-06 
00 07 
MBN 11264   Gateway Diagnostic Requirements  
2010-11 
00 08 
MBN 10482   Gateway Diagnostic Requirements  
2011-09 
00 09 
MBN 10482   Gateway Diagnostic Requirements  
2012-06 
00 0A 
MBN 10482   Gateway Diagnostic Requirements  
2012-11 
00 0B 
MBN 10482   Gateway Diagnostic Requirements  
2013-02 
00 0C 
 
 
 
Table 12:  Encoding of  Standardized Diagnostic Data UDS Version 
 
Specification 
Release 
Encoding 
[Specification 
Version] 
Standardized Diagnostic Data UDS 
1.3 
00 0D 
Standardized Diagnostic Data UDS 
1.6 
00 10 
Standardized Diagnostic Data UDS 
1.8 
00 12 
Standardized Diagnostic Data UDS 
1.9 
00 13 
Standardized Diagnostic Data UDS 
2.0 
00 14 
Standardized Diagnostic Data UDS 
2.1 
00 15 
A001 002 30 99   ZGS 001  Standardized Diagnostic Data UDS 
2.2 
00 16 
A001 002 30 99   ZGS 002  Standardized Diagnostic Data UDS 
2.3 
00 17 
A001 002 30 99   ZGS 003  Standardized Diagnostic Data UDS 
2.4 
00 18 
A001 002 30 99   ZGS 004  Standardized Diagnostic Data UDS 
2.5 
00 19 
A001 002 30 99   ZGS 005  Standardized Diagnostic Data UDS 
2.6 
00 1A 
A001 002 30 99   ZGS 006  Standardized Diagnostic Data UDS 
2.7 
00 1B 
 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 19 页
A0010023099: 2013-02, page 18 
 
 
Copyright Daimler AG 2013  
Table 13:  Encoding of  Gateway Diagnostic Requirements Version 
 
Specification 
Release 
Encoding 
[Specification 
Version] 
MBN 10399   Response on Event –Light 
2006-07 
00 00 
MBN 10399   Response on Event –Light Rev A 
2007-12 
00 01 
MBN 10399   Response on Event –Lite   Rev B 
2008-06 
00 02 
MBN 10399   Response on Event -Lite   Rev C 
2008-11 
00 03 
MBN 10399   Response on Event -Lite    
2009-06 
00 04 
MBN 10399   Response on Event -Lite     
2009-11 
00 05 
MBN 10399   Response on Event -Lite     
2010-06 
00 06 
MBN 10399   Response on Event -Lite     
2010-11 
00 07 
MBN 10399   Response on Event -Lite     
2011-09 
00 08 
MBN 10399   Response on Event -Lite     
2012-06 
00 09 
MBN 10399   Response on Event -Lite     
2012-11 
00 0A 
MBN 10399   Response on Event -Lite     
2013-02 
00 0B 
 
 
 
Table 14:  Encoding of  Gateway Diagnostic Requirements Version 
 
Specification 
Release 
Encoding 
[Specification 
Version] 
MBN 10412   LIN2.1 Diagnostic Requirements 
2007-12 
00 00 
MBN 10412   LIN2.1 Diagnostic Requirements 
2008-06 
00 01 
MBN 10412   LIN2.1 Diagnostic Requirements 
2008-11 
00 02 
MBN 10412   LIN2.1 Diagnostic Requirements 
2009-06 
00 03 
MBN 10412   LIN2.1 Diagnostic Requirements 
2009-11 
00 04 
MBN 10412   LIN2.1 Diagnostic Requirements 
2010-06 
00 05 
MBN 10412   LIN2.1 Diagnostic Requirements 
2010-11 
00 06 
MBN 10412   LIN2.1 Diagnostic Requirements 
2011-09 
00 07 
MBN 10412   LIN2.1 Diagnostic Requirements 
2012-06 
00 08 
MBN 10412   LIN2.1 Diagnostic Requirements 
2012-11 
00 09 
MBN 10412   LIN2.1 Diagnostic Requirements  
 
2013-02 
00 0A 
 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 20 页
A0010023099: 2013-02, page 19 
 
 
Copyright Daimler AG 2013  
Table 15:  Encoding of  Gateway Diagnostic Requirements Version 
 
Specification 
Release 
Encoding 
[Specification 
Version] 
MBN 10400  Diagnostics on FlexRay 
2007-03 
00 00 
MBN 10400  Diagnostics on FlexRay 
2007-12 
00 01 
MBN 10400  Diagnostics on FlexRay 
2008-06 
00 02 
MBN 10400  Diagnostics on FlexRay 
2008-11 
00 03 
MBN 10400  Diagnostics on FlexRay 
2009-06 
00 04 
MBN 10400  Diagnostics on FlexRay 
2009-11 
00 05 
MBN 10400  Diagnostics on FlexRay 
2010-06 
00 06 
MBN 10400  Diagnostics on FlexRay 
2010-11 
00 07 
MBN 10400  Diagnostics on FlexRay 
2011-09 
00 08 
MBN 10400  Diagnostics on FlexRay 
2012-06 
00 09 
MBN 10400  Diagnostics on FlexRay 
2012-11 
00 0A 
 
 
 
5.2.6 
Hardware Part Number - Business Unit Specific [F1 11 - F1 1F]  r 
DID [Hex]: F1 11 - F1 1F 
Description:  
These data identifiers are used to report the business unit specific hardware part number(s). 
Message structure:  
Upon requesting a specific hardware part number data identifier with the diagnostic service Read 
Data By Identifier [22 Hex], the ECU shall return the Business Unit Specific Hardware Part Number. 
The leading letter of the part number shall not be subject of the data record. The data content 
associated with the data identifier shall match Table 16. 
Refer to Chapter 8.1 for details regarding which data identifier to use for the various business units. 
In certain cases ECUs shall support several business unit specific part numbers. 
 
Table 16 :  Positive Response Data Record Definition - Hardware Part Number 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Encoding 
 
0 
: 
m-1 
Hardware Part Number 
 
Part Number High Byte #0 
 
 
: 
 
Part Number Low Byte #(m-1) 
 
M 
 
ASCII 
: 
ASCII 
 
 
m: length of part number used and varies by specific business unit. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 21 页
A0010023099: 2013-02, page 20 
 
 
Copyright Daimler AG 2013  
Table 17 :  Business Unit specific Part number definition and size: 
 
Brand 
Definition 
Format 
Size 
(Byte) 
MB Cars / 
MB VANs 
10 characters A part number 
The first byte of the data string contains the most left sided character 
of the part number followed by the next 9 characters. 
ASCII 
10 
MB Trucks / 
FUSO 
10 characters A part number + 3 characters geometric release (ZGS) 
The first byte of the data string contains the most left sided character 
of the part number followed by the next 9 characters. Bytes 11 to 13 
contain the ZGS. 
ASCII 
13 
Freightliner 
13 characters part number 
The first byte of the data string contains the most left sided character 
of the part number followed by the next used characters.  
Unused parts shall be padded with $20 == blank. 
 
Software part number shall be the same Format as defined for 
Mercedes-Benz Trucks 
ASCII 
13 
 
 
 
Response Example: 
Table 18 shows an example to report a MBC Hardware Part Number. In this example the reported 
Part Number is A 204 820 32 26 
 
Table 18 :  Positive Response Data Record Example - Hardware Part Number 
 
Data Byte 
No. 
Hex 
Value 
Parameter Name / Description 
Encoding 
0 
1 
2 
62 
F1 
11 
Read Data By Identifier Service Id 
Data Identifier – Read Hardware Part Number (byte #1) 
Data Identifier – Read Hardware Part Number (byte #2) 
Hex 
 
3 
4 
5 
6 
7 
8 
9 
10 
11 
12 
 
32 
30 
34 
38 
32 
30 
33 
32 
32 
36 
Hardware Part Number 
Hardware Part Number – byte #1   = 2 
Hardware Part Number – byte #2   = 0 
Hardware Part Number – byte #3   = 4 
Hardware Part Number – byte #4   = 8 
Hardware Part Number – byte #5   = 2 
Hardware Part Number – byte #6   = 0 
Hardware Part Number – byte #7   = 3 
Hardware Part Number – byte #8   = 2 
Hardware Part Number – byte #9   = 2 
Hardware Part Number – byte #10 = 6 
ASCII 
 
 
 
5.2.7 
Software Part Numbers - Business Unit Specific  [F1 21 - F1 2F ]  r 
DID [Hex]: F1 21 - F1 2F 
Description: 
The identification of Software Part Numbers is required for reprogrammable ECUs only  
(refer to MBN 10761 for more information). The individual data identifier to be used to report the 
Software Part Number is business unit specific. Refer to Chapter 8.1 for details regarding which data 
identifier to use for the various business units. In certain cases ECUs shall support several business 
unit specific part numbers. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 22 页
A0010023099: 2013-02, page 21 
 
 
Copyright Daimler AG 2013  
These data identifiers are used to report the business unit specific software part number. 
For more than one module, the data record (byte 0 to m-1) shall be repeated according to the 
number of programmable logical blocks. The logical block numbering of these logical blocks shall 
start with zero (0) and with every additional logical block the logical block number is increased by 
one. 
The order of the reported data items shall correspond to the order of data items of the Software 
Version Information (see 5.2.10) and Software Supplier Identification (see 5.2.26) data record. The 
software part number reported first (Software Logical Block #0 Part Number) is associated with the 
first reported version information (Software Logical Block #0 Version Information) and the first 
reported Software Supplier Identification #0. The second part number is associated with the second 
version information and the second supplier information etc. 
All bytes of the software logical block part number shall be set to 0xFF if an ECU has never been 
programmed (e.g. the ECU is delivered from the supplier, not programmed, or running in the boot 
software). 
All bytes of a software logical block part number shall be set to 0x30 (ASCII "0") if this specific logical 
block of an ECU is re-programmable in engineering only and will never be re-programmed in 
manufacturing or service. This is to allow for retaining the software logical block structure for 
reporting software version information without requiring additional part-numbers to be released and 
documented. 
Allocation of the specific logical blocks shall be assigned in coordination with the responsible Daimler 
reprogramming engineer. 
Message structure: 
Upon requesting a specific software part number data identifier with the diagnostic service Read 
Data By Identifier [22 Hex], the ECU shall return the Business Unit Specific Software Part Number. 
The data content associated with the data identifier shall match Table 19. 
 
Table 19 :  Positive Response Data Record Definition - Software Part Number(s) 
 
Data Byte No. Parameter Name / Description 
Sup 
Hex Range 
 
0 
: 
(m-1) 
Software Logical Block #0 Part Number 
 
Part Number High Byte 
 
 
: 
 
Part Number Low Byte 
M 
 
 
 
 
ASCII 
: 
ASCII 
: 
: 
 
 
 
(n-1)m 
: 
(n-1)m+(m-1) 
Software Logical Block #(n-1) Part Number 
 
Part Number High Byte 
 
 
: 
 
Part Number Low Byte 
C 
 
 
 
ASCII 
: 
ASCII 
 
 
m: length of part number used and varies by specific business unit. 
n: Number of logical blocks supported 
C: Parameter is only present if more than one (1) Software Logical Block is supported by the ECU 
 
For details regarding definition and size of part numbers refer to Table 17 
 
Response Example: 
Table 20 shows an example to report  MBC Software Part Numbers. In this example the ECU 
supports two re-programmable logical blocks (application software modules).  
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 23 页
A0010023099: 2013-02, page 22 
 
 
Copyright Daimler AG 2013  
The reported Part Numbers are: 
• 
application software module #0: A 204 442 01 32 
• 
application software module #1: A 204 447 42 53 
 
Table 20 :  Positive Response Data Record Example - Software Part Numbers 
 
Data Byte 
No. 
Hex 
Value Parameter Name / Description 
Encoding 
0 
1 
2 
62 
F1 
21 
Read Data By Identifier Service Id 
Data Identifier – Read Software Part Numbers (byte #1) 
Data Identifier – Read Software Part Numbers (byte #2) 
Hex 
 
3 
4 
5 
6 
7 
8 
9 
10 
11 
12 
 
32 
30 
34 
34 
34 
32 
30 
31 
33 
32 
Software Part Number Logical Block #0 
Software Part Number – byte #1   = 2 
Software Part Number – byte #2   = 0 
Software Part Number – byte #3   = 4 
Software Part Number – byte #4   = 4 
Software Part Number – byte #5   = 4 
Software Part Number – byte #6   = 2 
Software Part Number – byte #7   = 0 
Software Part Number – byte #8   = 1 
Software Part Number – byte #9   = 3 
Software Part Number – byte #10 = 2 
ASCII 
 
13 
14 
15 
16 
17 
18 
19 
20 
21 
22 
 
32 
30 
34 
34 
34 
37 
34 
32 
35 
33 
Software Part Number Logical Block #1 
Software Part Number – byte #1   = 2 
Software Part Number – byte #2   = 0 
Software Part Number – byte #3   = 4 
Software Part Number – byte #4   = 4 
Software Part Number – byte #5   = 4 
Software Part Number – byte #6   = 7 
Software Part Number – byte #7   = 4 
Software Part Number – byte #8   = 2 
Software Part Number – byte #9   = 5 
Software Part Number – byte #10 = 3 
ASCII 
 
 
 
5.2.8 
Removed 
Removed ECU Part Number  [F1 32 ] 
 
 
5.2.9 
Hardware Version Information [ F1 50 ]  r 
DID [Hex]: F1 50 
Description:  
The year parameter identifies the last two digits of the year when the hardware was designed. The 
week parameter identifies the week of the year when the hardware was designed. The patch level 
shall indicate all changes occurred in one calendar week. The numbering follows an ascending 
order. If no patch level is applicable [00 Hex] shall be reported as default. 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 24 页
A0010023099: 2013-02, page 23 
 
 
Copyright Daimler AG 2013  
Message structure: 
Upon requesting data identifier [F1 50 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Hardware Version Information needed by the tester to identify the 
development status of the hardware. The data content associated with the data identifier shall match 
Table 21. 
 
Table 21 :  Positive Response Data Record Definition - Hardware Version Information 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
 
0 
1 
2 
Hardware Version Information 
 
HW - year 
 
HW - week 
 
HW - patch level 
M 
 
 
 
 
00-63 
01-35 
00-63 
UINT 8 
 
 
 
Response Example: 
Table 22  shows an example to report a Hardware Version Information. In this example the reported 
Hardware Version is: 
• 
Year: 2005 
• 
Week: 42 
• 
Patch level: 00 
 
Table 22 :  Positive Response Data Record Example - Hardware Version Information 
 
Data Byte 
No. 
Hex 
Value 
Parameter Name / Description 
Encoding 
0 
1 
2 
62 
F1 
50 
Read Data By Identifier Service Id 
Data Identifier – Read Hardware Version Information (byte #1) 
Data Identifier – Read Hardware Version Information (byte #2) 
Hex 
 
3 
4 
5 
 
05 
2A 
00 
Hardware Version Information 
Year 
 
= 05 
Week  
= 42 
Patch level  
= 00 
UInt8 
 
 
 
5.2.10 Software Version Information [ F1 51 ]  r 
DID [Hex]: F1 51 
Description: 
The Software Version Information is required for programmable ECUs as well as for non-
programmable ECUs. Therefore, following rules apply: 
• 
Non-programmable ECUs shall only report one Software Version Information unit  
 
(Major Byte, Middle Byte, and Minor Byte) for the application software (Logical  
Block #0) currently running. 
• 
Programmable ECUs shall report as much Software Version Information units as there 
are programmable Logical Blocks within the respective ECU. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 25 页
A0010023099: 2013-02, page 24 
 
 
Copyright Daimler AG 2013  
The year parameter identifies the last two digits of the year when the software was built. The week 
parameter identifies the week of the year when the software was built. The patch level shall indicate 
all changes occurred in one calendar week. The numbering follows an ascending order. If no patch 
level is applicable, [00 Hex] shall be reported as default. 
The Software Version of a programmable logical block shall be set to [FF FF FF Hex] if the logical 
block has never been programmed (e.g. the ECU is delivered from the supplier, not programmed, 
and running in the boot software) or has been erased and the Software Version of this logical block 
has not been stored into separate 'non volatile memory' (e.g. EEPROM). 
Message structure: 
Upon requesting data identifier [F1 51 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Software Version Information needed by the tester to identify the 
development state of the software. The data content associated with the data identifier shall match 
Table 23. 
 
Table 23 :  Positive Response Data Record Definition - Software Version Information 
 
Data 
Byte No. Parameter Name / Description 
Sup Hex Range Encoding 
 
0 
1 
2 
Software Logical Block #0 Version Information 
 
SW - year 
 
SW - week 
 
SW - patch level 
M 
 
 
 
00-63 
01-35 
00-63 
UINT 8 
: 
: 
 
 
 
 
3(n-1) 
3(n-1)+1 
3(n-1)+2 
Software Logical Block #(n-1) Version Information 
 
Major Byte 
(year) 
 
Middle Byte (week) 
 
Minor Byte 
(patch level) 
C 
 
 
 
00-63 
01-35 
00-63 
UINT 8 
 
 
n:  Number of logical blocks respectively part numbers 
C: These message data parameters shall only be supported if the ECU is reprogrammable and more 
than one logical block can be programmed. 
 
Response Example: 
Table 24 is an example of how to report Software Version Information. In this example the ECU 
supports two re-programmable logical blocks (application software modules). The reported Software 
Version Information is 
• 
Application software module #0 
• 
Year: 2005 
• 
Week: 17 
• 
Patch level: 03 
• 
-Application software module #1 
• 
Year: 2006 
• 
Week: 12 
• 
Patch level: 01 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 26 页
A0010023099: 2013-02, page 25 
 
 
Copyright Daimler AG 2013  
Table 24 :  Positive Response Data Record Example - Software Version Information 
 
Data 
Byte 
No. 
Hex 
Value 
Parameter Name / Description 
Encoding 
0 
1 
2 
62 
F1 
51 
Read Data By Identifier Service Id 
Data Identifier – Read Software Version Information (byte #1) 
Data Identifier – Read Software Version Information (byte #2) 
Hex 
 
3 
4 
5 
 
05 
11 
03 
Software Version Information logical block #0 
Year 
 
= 05 
Week  
= 17 
Patch level  
= 03 
UInt8 
 
6 
7 
8 
 
06 
0C 
01 
Software Version Information logical block #1 
Year 
 
= 06 
Week  
= 12 
Patch level  
= 01 
UInt8 
 
 
 
5.2.11 Boot software Version Information [ F1 53 ]   r 
DID [Hex]: F1 53 
Description:  
This data identifier and the associated data record shall be supported by the boot software for 
reprogrammable ECUs (refer to MBN 10761 for more information) 
The boot software version identifies the software version of an ECU's boot loader. 
Message structure: 
Upon requesting data identifier [F1 53 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Software Version Information needed by the tester to identify the 
development state of the software. The data content associated with the data identifier shall match 
Table 25. 
 
Table 25 :  Positive Response Data Record Definition -Boot Software Version Information 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
 
0 
1 
2 
Boot Software Version Information 
 
Boot SW - year 
 
Boot SW - week 
 
Boot SW - patch level 
M 
 
 
 
00-63 
01-35 
00-63 
UINT 8 
 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 27 页
A0010023099: 2013-02, page 26 
 
 
Copyright Daimler AG 2013  
5.2.12 Hardware Supplier Identification [ F1 54 ]    r 
DID [Hex]: F1 54 
Description:  
The hardware supplier identification determines the manufacturer of an ECU's hardware. 
Message structure:  
Upon requesting data identifier [F1 54 Hex] with the diagnostic service Read Data By Identifier  
[22 Hex], the ECU shall return the Hardware Supplier Information needed by the tester to identify the 
Supplier of the hardware. The data content associated with the data identifier shall match Table 26. 
 
Table 26 :  Positive Response Data Record Definition -Hardware Supplier Identification 
 
Data 
byte 
no. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
 
0 
1 
Hardware Supplier Identification 
 
Hardware Supplier Identification High Byte 
 
Hardware Supplier Identification Low Byte 
M 
 
 
 
00-FF 
00-FF 
Refer to 
Chapter 11 
for further 
details. 
 
 
 
 
5.2.13 Commercial Vehicle Engine Number  [ F1 57 ]   r/w 
DID [Hex]: F1 57 
Description:  
This data identifier provides information about the vehicle’s engine make, engine model, and engine 
serial number. 
Message structure: 
Upon requesting data identifier [F1 57 Hex] with the diagnostic service Read Data By Identifier  
[22 Hex], the ECU shall report the Commercial Vehicle Engine Number Information data record. The 
data content associated with the data identifier shall match Table 27. The number of bytes listed for 
each parameter in Table 27 is the maximum number bytes allowable in this data identifier. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 28 页
A0010023099: 2013-02, page 27 
 
 
Copyright Daimler AG 2013  
Table 27 :  Positive Response Data Record Definition - Commercial Vehicle Engine Number 
 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range Encoding 
 
0 
: 
m-1 
Engine Make 
 
Character #1 
 
: 
 
Delimiter 
Delimiter for the Make string shall be “*” [2A 
Hex). 
If the Engine Make is supported the length of the 
Engine Make string is five bytes (m=5) otherwise 
the Engine Make incl. the delimiter are not present 
in this data record (m=0). 
 
C 
: 
C 
 
00-7F 
: 
'*' 
 
ASCII 
: 
ASCII 
 
m 
: 
m+n-1 
Engine Model 
 
Character #1 
 
: 
 
Delimiter 
Delimiter for the Model string shall be “*” [2A 
Hex). 
The maximum string length excluding delimiter 
shall not exceed 11 byte. 
 
C 
: 
C 
 
00-7F 
: 
'*' 
 
ASCII 
: 
ASCII 
 
m+n 
: 
m+n+p-1 
Engine Serial Number 
 
Character #1 
 
: 
 
Delimiter 
Delimiter for the Serial Number string shall be “*” 
[2A Hex) 
The maximum string length excluding delimiter 
shall not exceed 11 byte. 
 
C 
: 
C 
 
00-7F 
: 
'*' 
 
ASCII 
: 
ASCII 
 
 
m: Number of characters in "Engine Make" (including delimiter). 
n: Number of characters in "Engine Model" (including delimiter). 
p: Number of characters in "Engine Serial Number"  (including delimiter). 
C: Depending on the number of characters of each of the data items defined in Table 27, the total 
number of bytes in the data record may vary. As not all parameters have to be supported, the 
subsequent bytes of the data record shift correspondently. 
 
 
5.2.14 Removed 
Removed Vehicle Information [ F1 58 ] 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 29 页
A0010023099: 2013-02, page 28 
 
 
Copyright Daimler AG 2013  
5.2.15 ECU Model Type  [ F1 59 ]   r 
DID [Hex]: F1 59 
Description:  
This information shall be used to identify differently equipped hardware versions of the same base 
ECU (e.g. an instrument cluster which has different displays although the base ECU is identical). 
Message structure:  
Upon requesting data identifier [F1 59 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the ECU Model information. The data content associated with the data 
identifier shall match Table 28. 
 
Table 28 :  Positive Response Data Record Definition - ECU Model Type 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
0 
ECU Model Type 
M 
01-FF 
Hex 
 
00: reserved 
01 – FF: ECU model types  
are individually described per ECU  
 
 
 
 
 
 
 
5.2.16 Write Software Fingerprint  [ F1  5A ]  w 
DID [Hex]: F1 5A 
Description:  
This write service is used to select the respective logical block intended to be re-programmed. 
Additionally it writes identification data to the ECU. Programming date represents the current date 
when this data record is written to the ECU. 
NOTE: Supplier Identification shall not identify the creator of the respective software, but the user 
downloading the software to the ECUs memory. Therefore the Supplier Identification shall reflect 
either the manufacturer of the diagnostic tool hard/software or the owner of the diagnostic tool 
hard/software. For details regarding encoding see section  11. 
Message structure:  
Upon requesting [F1 5A Hex] with diagnostic service Write Data by Identifier [2E Hex], the ECU shall 
write the data transferred in the request message to the associated memory block. The data content 
shall match Table29/Table 30  associated to the valid revision of MBN 10761 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 30 页
A0010023099: 2013-02, page 29 
 
 
Copyright Daimler AG 2013  
******** Data Content associated with MBN 10761 Rev C and previous Revisions 
 
Table 29 :  Request Data Record Definition - Write Software Fingerprint 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
0 
Active Logical Block #. 
M 
00-FF 
Hex 
 
1 
2 
Supplier Identification 
 
Supplier Identification High Byte 
 
Supplier Identification Low Byte 
M 
 
 
 
00-FF 
00-FF 
 
See Chapter 
11 for details 
 
3 
4 
5 
Programming Date 
 
SW Programming - Year 
 
SW Programming - Month 
 
SW Programming - Day 
M 
 
 
 
 
00-63 
01-0C 
01-1F 
Uint8 
 
6 
7 
8 
9 
Diagnostic Tool Serial Number 
 
High Byte 
 
: 
 
: 
 
Low Byte 
M 
 
 
 
 
 
00 – FF 
00 – FF 
00 – FF 
00 – FF 
Hex 
 
 
 
******** Data Content associated with MBN 10761 Rev D  and later 
 
Table 30 :  Request Data Record Definition - Write Software Fingerprint 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
 
0 
1 
Supplier Identification 
 
Supplier Identification High Byte 
 
Supplier Identification Low Byte 
M 
 
 
 
00-FF 
00-FF 
 
See 
Chapter 11 
for details 
 
2 
3 
4 
Programming Date 
 
SW Programming - Year 
 
SW Programming - Month 
 
SW Programming - Day 
M 
 
 
 
 
00-63 
01-0C 
01-1F 
Uint8 
 
5 
6 
7 
8 
Diagnostic Tool Serial Number 
 
High Byte 
 
: 
 
: 
 
Low Byte 
M 
 
 
 
 
 
00 – FF 
00 – FF 
00 – FF 
00 – FF 
Hex 
 
 
 
5.2.17 Read Software Fingerprint(s)  [ F1 5B ]    r 
DID [Hex]: F1 5B 
Description:  
This data record shall uniquely identify the last external test tool which reprogrammed the ECU. The 
Supplier Identification data identifies the supplier or the Daimler business unit that has programmed 
the specific logical block. 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 31 页
A0010023099: 2013-02, page 30 
 
 
Copyright Daimler AG 2013  
The ECU shall report the following data if a logical block has not been programmed by the DC 
download sequence (e.g. the ECU is preprogrammed by the supplier manufacturing line) 
• 
Supplier Identification: The Supplier Identification according to section 6.4 
• 
Programming Date = [00 00 00 Hex] 
• 
Diagnostic Tool Serial Number = [00 00 00 00 Hex] 
The ECU shall report the following data if a logical block has never been programmed (e.g. the ECU 
is delivered from the supplier, not programmed, and running in the boot software) 
• 
Supplier Identification = [FF FF Hex] 
• 
Programming Date = [FF FF FF Hex] 
• 
Diagnostic Tool Serial Number = [FF FF FF FF Hex] 
Message structure:  
Upon requesting data identifier [F1 5B Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Software Fingerprint Information. The data content associated 
with the data identifier shall match Table 31. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 32 页
A0010023099: 2013-02, page 31 
 
 
Copyright Daimler AG 2013  
Table 31 :  Positive Response Data Record Definition - Read Software Fingerprint 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
 
Fingerprint Logical Block #0 
 
 
 
0 
Status Information – Logical Block #0 
M 
 
 Bit 0 – Software Programmed And Valid 
 (software valid = 1    (CRC and Signature valid),  
 software invalid = 0   (CRC and/or Signature error)) 
M 
 
 Bit 1 – Software – Software Mismatch 
 (Mismatch occurred = 1, no Mismatch = 0; 
 if this Bit is not supported report no Mismatch) 
C1 
 
 Bit 2 – Software – Hardware Mismatch 
 (Mismatch occurred = 1, no Mismatch = 0; 
 if this Bit is not supported report no Mismatch) 
M 
 
 Bit 3 – Memory Status Information 
 (Memory Erased = 1, Memory Programmed = 0) 
M 
 
 Bit 4 – Reserved (set to zero) 
M 
 
 Bit 5 – Reserved (set to zero) 
M 
 
 Bit 6 – Reserved (set to zero) 
M 
 
 Bit 7 – Reserved (set to zero) 
M 
00-0F 
Hex 
 
1 
2 
Supplier Identification - Logical Block #0 
 Supplier Identification High Byte 
 Supplier Identification Low Byte 
M 
 
 
 
00-FF 
00-FF 
See Chapter 
11 for details 
 
3 
4 
5 
Programming Date - Logical Block #0 
 SW Programming - Year 
 SW Programming - Month 
 SW Programming - Day 
M 
 
 
 
 
00, 00-63, FF 
00, 01-0C, FF 
00, 01-1F, FF 
Uint8 
 
6 
7 
8 
9 
Diagnostic Tool Serial Number - Logical Block #0 
 High Byte 
 : 
 : 
 Low Byte 
M 
 
 
 
 
 
00 – FF 
00 – FF 
00 – FF 
00 – FF 
Hex 
: 
: 
: 
: 
: 
 
Fingerprint Logical Block #(n-1) 
 
 
 
 
 
10(n-1) Status Information – Logical Block #(n-1) 
C2 
00-0F 
Hex 
 
10(n-1)+1 
10(n-1)+2 
Supplier Identification - Logical Block #(n-1) 
 Supplier Identification High Byte 
 Supplier Identification Low Byte 
C2 
 
 
 
00-FF 
00-FF 
See Chapter 
11 for details 
 
10(n-1)+3 
10(n-1)+4 
10(n-1)+5 
Programming Date - Logical Block #(n-1) 
 Year 
 Month 
 Day 
C2 
 
 
 
 
00, 00-63, FF 
00, 01-0C, FF 
00, 01-1F, FF 
Uint8 
 
10(n-1)+6 
10(n-1)+7 
10(n-1)+8 
10(n-1)+9 
Diagnostic Tool Serial Number - Logical Block #(n-1) 
 High Byte 
 : 
 : 
 Low Byte 
C2 
 
 
 
 
 
00 – FF 
00 – FF 
00 – FF 
00 – FF 
Hex 
 
 
 
n:    Number of logical blocks 
C1:  This bit depends on the ECU architecture (number of programmable logical blocks). 
C2: These response message parameters shall only be supported, if more than one logical block in 
an ECU is programmable. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 33 页
A0010023099: 2013-02, page 32 
 
 
Copyright Daimler AG 2013  
5.2.18 Write Configuration Fingerprint  [ F1 5C ] w 
DID [Hex]: F1 5C 
Description:  
This write service is used to select the respective configuration block intended to be parameterized. 
Additionally it writes identification data to the ECU. Please refer to model line specific requirements 
for details. 
Message structure:  
Upon requesting [F1 5C Hex] with diagnostic service Write Data by Identifier [2E Hex], the ECU shall 
write the data transferred in the request message to the associated memory block. The data content 
associated with the data identifier shall match Table 32. 
 
Table 32 :  Request Data Record Definition - Write Configuration Fingerprint 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
0 
Active Configuration Block # 
M 
00-FF 
Hex 
 
1 
2 
Supplier Identification 
 
Supplier Identification High Byte 
 
Supplier Identification Low Byte 
M 
 
 
 
00-FF 
00-FF 
 
See 
Chapter 11 
for details 
 
3 
4 
5 
Programming Date 
 
Year 
 
Month 
 
Day 
M 
 
 
 
 
00-63 
01-0C 
01-1F 
 
 
Uint8 
 
 
6 
7 
8 
9 
Diagnostic Tool Serial Number 
 
High Byte 
 
: 
 
: 
 
Low Byte 
M 
 
 
 
 
 
00 – FF 
00 – FF 
00 – FF 
00 – FF 
Hex 
 
 
 
5.2.19 Read Configuration Fingerprint [ F1 5D] r 
DID [Hex]: F1 5D 
Description:  
This data record shall uniquely identify the external test tool that last parameterized the ECU. The 
Supplier Identification data identifies the supplier or the Daimler business unit that has parameterized 
the specific configuration block. 
 
The ECU shall report the following data if a configuration block has never been written to the ECU’s 
non-volatile memory or no information is available (e.g. basic configuration included in ECU 
software): 
• 
Supplier Identification = [FF FF Hex] 
• 
Programming Date = [FF FF FF Hex] 
• 
Diagnostic Tool Serial Number = [FF FF FF FF Hex] 
NOTE: The Supplier Identification, Programming Date and the Diagnostic Tool Serial Number are 
written by the diagnostic service Write Data By Identifier - Write Configuration Fingerprint  
[2E F1 5C Hex] described in section 5.2.18. 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 34 页
A0010023099: 2013-02, page 33 
 
 
Copyright Daimler AG 2013  
Message structure: 
Upon requesting data identifier [F1 5D Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Configuration Fingerprint Information. The data content associated 
with the data identifier shall match Table 33 
 
Table 33 : Positive Response Data Record Definition - Read Configuration Fingerprint 
 
Data Byte 
No. 
Parameter Name / Description 
Sup Hex Range Encoding 
0 
Configuration Block #0 
M 
00-FF 
Hex 
 
1 
2 
Supplier Identification - Configuration Block #0 
 
Supplier Identification High Byte 
 
Supplier Identification Low Byte 
M 
 
 
 
00-FF 
00-FF 
See 
Chapter 11 
for details 
 
3 
4 
5 
Programming Date - Configuration Block #0 
 
Year 
 
Month 
 
Day 
M 
 
 
 
 
00-63 
01-0C 
01-1F 
Uint8 
 
6 
7 
8 
9 
Diagnostic Tool Serial Number-Configuration Block #0 
 
High Byte 
 
: 
 
: 
 
Low Byte 
M 
 
 
 
 
 
00 – FF 
00 – FF 
00 – FF 
00 – FF 
Hex 
: 
: 
 
 
 
10 (n-1) 
Configuration Block #n-1 
C 
00 - FF 
Hex 
 
10(n-1)+1 
10(n-1)+2 
Supplier Identification - Configuration Block #n 
 
Supplier Identification High Byte 
 
Supplier Identification Low Byte 
C 
 
 
 
00-FF 
00-FF 
See 
Chapter 11 
for details 
 
10(n-1)+3 
10(n-1)+4 
10(n-1)+5 
Programming Date - Configuration Block #n 
 
Year 
 
Month 
 
Day 
C 
 
 
 
 
00-63 
01-0C 
01-1F 
Uint8 
 
10(n-1)+6 
10(n-1)+7 
10(n-1)+8 
10(n-1)+9 
Diagnostic Tool Serial Number - Configuration Block #n 
 
High Byte 
 
: 
 
: 
 
Low Byte 
C 
 
 
 
 
 
00 – FF 
00 – FF 
00 – FF 
00 – FF 
Hex 
 
 
n:  Number of configuration blocks reported. 
C: These response message parameters are only reported if more than one configuration block is 
supported by a specific ECU. 
 
5.2.20 Write Routine I/O Fingerprint  [ F1 5E ] w 
DID [Hex]: F1 5E 
Description:  
This write service is used to select the respective group of Routine or I/O Control services intended 
to be used. Additionally it writes identification data to the ECU. (Please refer to model line specific 
requirements for details). 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 35 页
A0010023099: 2013-02, page 34 
 
 
Copyright Daimler AG 2013  
Message structure:  
Upon requesting [F1 5E Hex] with diagnostic service Write Data by Identifier [2E Hex], the ECU shall 
write the data transferred in the request message to the associated memory block. The data content 
associated with the data identifier shall match Table 34 
 
Table 34 :  Request Data Record Definition - Write Routine/I/O Fingerprint 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
0 
Active Routine /  I/O Group #0 
M 
00-FF 
Hex 
 
1 
2 
Supplier Identification 
 
Supplier Identification High Byte 
 
Supplier Identification Low Byte 
M 
 
 
 
00-FF 
00-FF 
 
See Chapter 11 
for details 
 
3 
4 
5 
Programming Date 
 
Year 
 
Month 
 
Day 
M 
 
 
 
 
00-63 
01-0C 
01-1F 
 
 
Uint8 
 
 
6 
7 
8 
9 
Diagnostic Tool Serial Number 
 
High Byte 
 
: 
 
: 
 
Low Byte 
M 
 
 
 
 
 
00 – FF 
00 – FF 
00 – FF 
00 – FF 
Hex 
 
 
 
5.2.21 Read Routine I/O Fingerprint  [ F1 5F ] r 
DID [Hex]: F1 5F 
Description:  
This data record shall uniquely identify the entity that executed Routine or I/O control services of 
specifically secured group. The Supplier Identification data identifies the supplier or the Daimler 
business unit that has used the Routine or I/O Control service of a specific group. 
 
The ECU shall report the following data if a Routine / I/O block has never been accessed or no 
information is available: 
• 
Supplier Identification = [FF FF Hex] 
• 
Programming Date = [FF FF FF Hex] 
• 
Diagnostic Tool Serial Number = [FF FF FF FF Hex] 
 
NOTE: The Supplier Identification, Programming Date and the Diagnostic Tool Serial Number are 
written by the diagnostic service Write Data By Identifier - Write Routine / I/O Fingerprint  
[2E F1 5E Hex] described in section 5.2.20. 
 
Message structure:  
Upon requesting data identifier [F1 5F Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Routine I/O Fingerprint Information. The data content associated 
with the data identifier shall match Table 35. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 36 页
A0010023099: 2013-02, page 35 
 
 
Copyright Daimler AG 2013  
Table 35 : Positive Response Data Record Definition - Read Routine / I/O Fingerprint 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex 
Range 
Encoding 
0 
Routine/I/O Group #0 
M 
00-FF 
Hex 
 
1 
2 
Supplier Identification - Routine/I/O Group #0 
 
Supplier Identification High Byte 
 
Supplier Identification Low Byte 
M 
 
 
 
00-FF 
00-FF 
See 
Chapter 11 
for details 
 
3 
4 
5 
Programming Date - Routine/I/O Group # 0 
 
Year 
 
Month 
 
Day 
M 
 
 
 
 
00-63 
01-0C 
01-1F 
Uint8 
 
6 
7 
8 
9 
Diagnostic Tool Serial Number- Routine/I/O Group #0 
 
High Byte 
 
: 
 
: 
 
Low Byte 
M 
 
 
 
 
 
00 – FF 
00 – FF 
00 – FF 
00 – FF 
Hex 
: 
: 
 
 
 
10(n-1) 
Routine/I/O Group #n-1 
C 
00 - FF 
Hex 
 
10(n-1)+1 
10(n-1)+2 
Supplier Identification - Routine/I/O Group #n 
 
Supplier Identification High Byte 
 
Supplier Identification Low Byte 
C 
 
 
 
00-FF 
00-FF 
See 
Chapter 11 
for details 
 
10(n-1)+3 
10(n-1)+4 
10(n-1)+5 
Programming Date - Routine/I/O Group #n 
 
Year 
 
Month 
 
Day 
C 
 
 
 
 
00-63 
01-0C 
01-1F 
Uint8 
 
10(n-1)+6 
10(n-1)+7 
10(n-1)+8 
10(n-1)+9 
Diagnostic Tool Serial Number - Routine/I/O Group #n 
 
High Byte 
 
: 
 
: 
 
Low Byte 
C 
 
 
 
 
 
00 – FF 
00 – FF 
00 – FF 
00 – FF 
Hex 
 
 
 
n:  Number of Routine/I/O blocks reported. 
C: These response message parameters are only reported, if more than one Routine/I/O block is 
supported by a specific ECU. 
 
 
5.2.22 ECU Serial Number [ F1 8C ] r 
DID [Hex]: F1 8C 
Description:  
This record shall be used to uniquely identify a specific ECU hardware to be able to identify ECUs of 
a specific batch.  This number is also referred to as a Traceability Number. 
Message structure:  
Upon requesting data identifier [F1 8C Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the ECU Serial Number. The data content associated with the data 
identifier shall match Table 36. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 37 页
A0010023099: 2013-02, page 36 
 
 
Copyright Daimler AG 2013  
Table 36 : Positive Response Data record Definition - ECU Serial Number 
 
Data 
Byte 
No. 
Parameter Name 
Description 
Sup Encoding 
0 
ECU Serial Number (MSB) 
M 
: 
: 
: 
n 
ECU Serial Number (LSB) 
 
MBC: The ECU serial number shall 
uniquely identify an individual ECU. Any 
format fulfilling this requirement is 
allowed. 
 
Daimler Trucks: The ECU serial number 
shall uniquely identify an individual ECU. 
Any format fulfilling this requirement is 
allowed. The size of this data record shall 
be 10 bytes. 
M 
ASCII 
 
 
 
5.2.23 VIN Original  [ F1 90 ] r 
DID [Hex]: F1 90 
Description:  
This data record reflects the Vehicle Identification Number of the vehicle an ECU was originally 
installed.. 
Message structure: 
Upon requesting data identifier [F1 90 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the VIN Original. The data content associated with the data identifier 
shall match Table 37. 
 
Table 37 : Positive Response Data Record Definition - VIN Original 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex Range 
Encoding 
0 
VIN (MSB) 
M 
ASCII 
: 
: 
: 
: 
16 
VIN (LSB) 
M 
30-39; 
41-5A  
without  
49,4F,51 
ASCII 
 
 
NOTE: See business unit specific requirements for default value definition. 
 
 
5.2.24 VIN Current  [ F1 A0 ]   r/w 
DID [Hex]: F1 A0 
Description: 
This data record reflects the Vehicle Identification Number of the vehicle an ECU is currently 
installed. 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 38 页
A0010023099: 2013-02, page 37 
 
 
Copyright Daimler AG 2013  
Message structure: 
Upon requesting data identifier [F1 A0 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the VIN Current. The data content associated with the data identifier 
shall match Table 38. 
 
Table 38 : Positive Response Data record Definition - VIN Current 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex Range 
Encoding 
0 
VIN (MSB) 
M 
ASCII 
: 
: 
: 
: 
16 
VIN (LSB) 
M 
30-39; 
41-5A  
without  
49,4F,51 
ASCII 
 
 
NOTE: See business unit specific requirements for Default value definition. 
 
 
5.2.25 Exhaust Regulation or Type Approval Number (EROTAN) [ F1 96 ] r/w 
DID [Hex]: F1 96 
Description:  
The Exhaust Regulation or Type Approval Number (EROTAN) is a certificate/approval for a specific 
vehicle type considering: 
• 
Vehicle type 
• 
Engine displacement 
• 
Number of cylinders 
• 
Nominal engine power 
• 
Approval of emissions 
The EROTAN is defined by the vehicle manufacturer in accordance with a registration office. The 
EROTAN has to be available from the vehicle itself. 
Message structure: 
For details refer to specification “Exhaust Regulation or Type Approval Number - Requirement 
Specification“. Upon requesting data identifier [F1 96 Hex] with the diagnostic service Read Data By 
Identifier [22 Hex], the ECU shall return the EROTAN. 
 
 
5.2.26 Software Supplier Identification [ F1 55 ]    r 
DID [Hex]: F1 55 
Description: 
The Software Supplier Information is required for reprogrammable ECUs as well as for non-
reprogrammable ECUs. Therefore the following rules apply: 
- 
Non -reprogrammable ECUs shall only report one Software Supplier Information unit  
(High Byte, Low Byte) for the application software (Logical Block #0) currently running. 
- 
Reprogrammable ECUs shall report as much Software Supplier Information units as there are 
programmable Logical Blocks within the respective ECU 
The software supplier identification identifies the developer who designed and built the ECU's 
software. 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 39 页
A0010023099: 2013-02, page 38 
 
 
Copyright Daimler AG 2013  
Message structure:  
Upon requesting data identifier [F1 55 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Software Supplier Information needed by the tester to identify the 
Supplier of the software. The Software Supplier Information of a programmable logical block shall be 
set to [FF FF Hex] if the logical block has never been programmed (e.g. the ECU is delivered from 
the supplier, not programmed, and running in the boot software). The data content associated with 
the data identifier shall match Table 100 
 
 
Table 100  :  Positive Response Data Record Definition - Software Supplier Identification 
 
Data byte 
no. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
 
0 
1 
Software Logical Block #0 Supplier Identification 
 
Software Supplier Information High Byte 
 
Software Supplier Information Low Byte 
M 
 
 
 
00-FF 
00-FF 
: 
: 
 
 
 
2(n-1) 
2(n-1)+1 
Software Logical Block #(n-1) Supplier Identification 
Software Supplier Information High Byte 
Software Supplier Information Low Byte 
C 
 
 
 
00-FF 
00-FF 
Refer to 
Chapter 11 
for further 
details. 
 
 
n:  Number of logical blocks respectively part numbers 
C:  Dependent on the number of the programmable logical blocks in a specific ECU.  
 
 
5.2.27 FINAS Number [ F1 01 ]    r/w 
DID [Hex]: F1 01 
Description:  
This Data Record is used to ensure the complete documentation of  electronic components during 
testing and try out phase. 
Message structure:  
Upon requesting [F1 01 Hex] with diagnostic service Read Data by Identifier [22 Hex], the ECU shall 
return the FINAS number.  
Upon requesting [F1 01 Hex] with diagnostic service Write Data by Identifier [2E Hex], the ECU shall 
write the data transferred in the request message to the associated memory block.  
The data content associated with the data identifier shall match Table 104. 
 
Table 104 :  Request / Response Data Record Definition - FINAS Number 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
0 -18 FINAS Number 
M 
 
ASCII 
19 
Null Termination 
M 
00 
Hex 
 
 
Unused bytes shall be padded with blanks  ($20). 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 40 页
A0010023099: 2013-02, page 39 
 
 
Copyright Daimler AG 2013  
5.2.28 Technical Time Stamp  [F1 02 ]    r/w 
DID [Hex]: F1 02 
Description:  
This Data Record provides information regarding the release version of the vehicle (TTZ - 
Termintechnischer Zustand). The value is written to the main vehicle gateway by the external testtool 
during manufacturing process. 
Message structure:  
Upon requesting [F1 02 Hex] with diagnostic service Read Data by Identifier [22 Hex], the ECU shall 
return the Technical Time Stamp Information.  
Upon requesting [F1 02 Hex] with diagnostic service Write Data by Identifier [2E Hex], the ECU shall 
write the data transferred in the request message to the associated memory block.  
The data content associated with the data identifier shall match Table 103. 
 
Table 103 :  Request / Response Data Record Definition - Technical Time Stamp 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
 
 
0 
1 
2 
3 
 
Technical Time Stamp 
 
Year  (Highbyte) 
 
Year  (Lowbyte) 
 
Month 
 
Day 
 
 
M 
M 
M 
M 
 
 
 
00-FF 
00-FF 
01-0C 
01-1F 
 
 
 
Dec 
 
 
 
Technical Time Stamp = JJJJMMTT,  e.g  2009 07 11  which is coded as  07D9 07 0B Hex 
 
 
5.2.29 FDOK Safety Relevant Information  [F1 03 ]    r 
DID [Hex]: F1 03 
Description:  
This Data Record provides all information required for documentation of safety relevant data in the 
Daimler documentation system FDOK. This Data Record represents a compilation of the MB 
hardware partnumber, hardware supplier identification, manufacturing date and the daily production 
number which starts with 1 every day and is incremented for each produced ECU.  
Message structure:  
Upon requesting [F1 03 Hex] with diagnostic service Read Data by Identifier [22 Hex], the ECU shall 
return the FDOK Safety Relevant Information.  
 
The data content associated with the data identifier shall match Table 105. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 41 页
A0010023099: 2013-02, page 40 
 
 
Copyright Daimler AG 2013  
Table 105 :  Response Data Record Definition - FDOK Safety Relevant Information 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
 
Hex Range Encoding 
 
0 
: 
9 
Hardware Part Number 
 
Part Number High Byte #0 
 
 
: 
 
Part Number Low Byte #(9) 
 
M 
 
 
ASCII 
: 
ASCII 
 
10 
11 
Hardware Supplier Identification 
 
Hardware Supplier Identification High Byte 
 
Hardware Supplier Identification Low Byte 
M 
 
 
 
00-FF 
00-FF 
Refer to 
Chapter 11 
for further 
details. 
 
12 
13 
14 
Manufacturing Date 
 
Year 
 
Month 
 
Day 
M 
 
 
 
 
00-63 
01-0C 
01-1F 
 
 
Dec 
 
15 - 18 
 
Daily Production Number 
 
 
M 
 
 
 
00000000-
FFFFFFFF 
Dec 
 
 
 
 
 
 
5.2.30 FlexRay Node Information  [F1 A6]    r 
DID [Hex]: F1 A6 
Description:  
This Data Record provides information regarding the sync and cold start cababilities of a Flexray 
node..  
Message structure:  
Upon requesting [F1 A6 Hex] with diagnostic service Read Data by Identifier [22 Hex], the ECU shall 
return the FlexRay Node Information.  
The data content associated with the data identifier shall match Table 113. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 42 页
A0010023099: 2013-02, page 41 
 
 
Copyright Daimler AG 2013  
Table 113 : Positive Response Data Record Definition - FlexRay Node Information 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range Encoding 
0 
FlexRay Node Informnation 
M 
 
 Bit 0 – IsColdStartNode 
 
(True = 1 
 
False = 0 ) 
M 
 
 Bit 1 – IsSyncNode 
 
(True = 1 
 
False = 0 ) 
M 
 
 Bit 2 – Reserved (set to zero) 
M 
 
 Bit 3 – Reserved (set to zero) 
M 
 
 Bit 4 – Reserved (set to zero) 
M 
 
 Bit 5 – Reserved (set to zero) 
M 
 
 Bit 6 – Reserved (set to zero) 
M 
 
 Bit 7 – Reserved (set to zero) 
M 
00-03 
Hex 
 
 
 
5.3 
Development Data 
5.3.1 
OSEK - Module information [ F1 0F ]   r 
Removed 
 
5.3.2 
SW Module Information  [F1 60 - F1 6F ]   r  
DID [Hex]: F1 60 - F1 6F 
Description:  
The Software Module Information data record shall report the version information of standard 
software modules implemented in the respective ECU.  Table 40 and Table 41 show the symbolic 
name used in the standard softwarefor each data element to be reported. If no standard  software 
module is implemented      at least the information related to the Communication Matrix  shall be 
reported. 
Message structure:  
When requesting data identifier [F1 60 Hex] the response shall include the number of available 
channels and the SW Module Information associated with the last channel. The highest number of 
available channels is assigned to the last channel. The complete data content of the response 
message is described in Table 40. 
 
NOTE: If kSipxxxVersion exceeds the decimal value 99 the dedicated value shall be set to 99 
(0x99). 
 
When requesting information of one specific channel the request shall use the following identifier:  
[F1 6<selected channel number> Hex]. The numbering of channels starts with one (1). The complete 
data content of the response message is described in Table 41 
 
NOTE: SW-Modules that are not implemented shall be indicated with [00 Hex] 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 43 页
A0010023099: 2013-02, page 42 
 
 
Copyright Daimler AG 2013  
Table 40 : 0x62 0xF160 Positive Response Data Record Definition - SW Module Information - 
Number of Channels / Last Channel Development State 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex 
Range 
 
Encoding 
0 
Number of Channels (kCanNumberOfChannels) 
M 
01 – 0F 
Hex 
1 
HIS Supplier Identification Last Channel  
(None = 00,  Vector = 30   ) 
http://www.automotive-his.de 
M 
00,  
1E 
Hex 
2 
Software Integration Package Version Last Channel 
MSB (kSipMainVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
3 
Software Integration Package Version Last Channel 
LSB (kSipSubVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
4 
Software Integration Package Patch Version Last 
Channel (kSipBugFixVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
5 
Communication Matrix Last Channel MSB 
(kDBCMainVersion) 
VMM Calendar Year (YY) 
M 
HN: 0-9 
LN: 0-9 
BCD 
6 
Communication Matrix Last Channel LSB 
(kDBCSubVersion) 
VMM Calendar Week (WW) 
M 
HN: 0-9 
LN: 0-9 
BCD 
7 
Communication Matrix Patch Version Last Channel 
(kDBCReleaseVersion) 
M 
00 - FF 
Hex 
8 
Physical Layer Driver Version Last Channel MSB 
(kCanMainVersion or kLinMainVersion or 
kFRMainVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
9 
Physical Layer Driver Version Last Channel LSB 
(kCanSubVersion or kLinSubVersion or 
kFRSubVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
10 
Physical Layer Driver Bug fix Version Last Channel 
(kCanBugFixVersion or kLinBugFixVersion or 
kFRBugFixVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
11 
NM Version Last Channel MSB (kNmMainVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
12 
NM Version Last Channel LSB (kNmSubVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
13 
NM Bug fix Version Last Channel 
(kNmBugfixVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
14 
UDS Module Version Last Channel MSB  
(g_descMainVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
15 
UDS Module Version Last Channel LSB  
(g_descSubVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
16 
UDS Bug Fix Version Last Channel  
(g_descBugFixVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
17 
Transport Layer Version Last Channel MSB  
(kTpMainVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
18 
Transport Layer Version Last Channel LSB  
(kTpSubVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
19 
Transport Layer Bug Fix Version Last Channel 
(kTpBugFixVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
 
 
 
HN: High Nibble 
LN: Low Nibble 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 44 页
A0010023099: 2013-02, page 43 
 
 
Copyright Daimler AG 2013  
Table 41 : 0x62 0xF161-0xF16F Positive Response Data Record Definition - 
SW Module Information - Selected Channel Development State 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex 
Range 
Encoding 
0 
Selected Channel 
M 
01 – 0F 
Hex 
1 
HIS Supplier Identification Selected Channel  
( None = 00 ,  Vector = 30 ) 
http://www.automotive-his.de 
M 
00, 
1E 
Hex 
2 
Software Integration Package Version Selected Channel 
MSB (kSipMainVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
3 
Software Integration Package Version Selected Channel LSB 
(kSipSubVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
4 
Software Integration Package Patch Version Selected 
Channel (kSipBugFixVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
5 
Communication Matrix Selected Channel MSB 
(kDBCMainVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
6 
Communication Matrix Selected Channel LSB 
(kDBCSubVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
7 
Communication Matrix Patch Version Selected Channel 
(kDBCReleaseVersion) 
M 
00 - FF 
Hex 
8 
Physical Layer Driver Version Selected Channel MSB 
(kCanMainVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
9 
Physical Layer Driver Version Selected Channel LSB 
(kCanSubVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
10 
Physical Layer Driver Bug fix Version Selected Channel 
(kCanBugFixVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
11 
NM Version Selected Channel MSB (kNmMainVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
12 
NM Version Selected Channel LSB (kNmSubVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
13 
NM Bug fix Version Selected Channel (kNmBugfixVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
14 
UDS Module Version Selected Channel MSB 
(g_descMainVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
15 
UDS Module Version Selected Channel LSB  
(g_descSubVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
16 
UDS Bug Fix Version Selected Channel  
(g_descBugFixVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
17 
Transport Layer Version Selected Channel MSB 
(kTpMainVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
18 
Transport Layer Version Selected Channel LSB  
(kTpSubVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
19 
Transport Layer Bug Fix Version Selected Channel 
(kTpBugFixVersion) 
M 
HN: 0-9 
LN: 0-9 
BCD 
 
 
HN: High Nibble 
LN: Low Nibble 
 
NOTE: Physical Layer Channel Configuration data which is not available shall be indicated  
with [FF Hex] 
 
 
5.3.3 
Physical Layer Channel Configuration [F1 70 - F1 7F ]    r 
Removed 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 45 页
A0010023099: 2013-02, page 44 
 
 
Copyright Daimler AG 2013  
5.3.4 
SW-Module Identification AUTOSAR/DAIMLER  
******* 
SW-Module Identification - Application - AUTOSAR  [ EF 00]    r 
DID [Hex]: EF 00 
Description: 
This routine is used to retrieve the identification of AUTOSAR SW-modules and DAIMLER Standard 
SW-Modules implemented in the Application of the respective ECU.   
Message structure: 
Upon receiving the request " Report SW-module Identification - Application - AUTOSAR " [22 EF 00 
Hex]) the ECU shall report the identification information of all AUTOSAR SW-modules or  DAIMLER 
standard SW-modules implemented within the Application Software according to the format defined 
in Table 44 
 
******* 
SW-Module Identification - Flashloader - AUTOSAR  [ EF 01]    r 
Removed 
 
******* 
Positive Response Definition-  SW-Module Identification AUTOSAR/DAIMLER 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 46 页
A0010023099: 2013-02, page 45 
 
 
Copyright Daimler AG 2013  
Table 44 : Positive Response Data Record Definition - SW-Module Identification AUTOSAR 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
0 
Ecu Extract Of System Description 
Major Version 
M 
HN: 0-9 
LN: 0-9 
BCD 
1 
Ecu Extract Of System Description 
Minor Version 
M 
HN: 0-9 
LN: 0-9 
BCD 
2 
Ecu Extract Of System Description 
Patch Version 
M 
HN: 0-9 
LN: 0-9 
BCD 
3 
Number of included AUTOSAR SW-modules 
M 
01-FF 
Hex 
4 
Standard Identification #1 
 
00 = AUTOSAR Standard Module 
  
01 = DAI- Standard Module 
M 
00-01 
Hex 
5 
 
6 
AUTOSAR SW-Module Identification # 1 
 
AUTOSAR Module ID 
 
 
(XX_MODULE_ID) 
 
AUTOSAR Supplier ID 
  
 
(XX_VENDOR_ID) 
M 
 
M 
00-FF 
 
00-FF 
Hex 
 
Hex 
7 
AUTOSAR SW Specification # 1 
Major Version 
(XX_AR_MAJOR_VERSION) 
M 
HN: 0-9 
LN: 0-9 
BCD 
8 
AUTOSAR SW Specification # 1 
Minor Version 
(XX_AR_MINOR_VERSION) 
M 
HN: 0-9 
LN: 0-9 
BCD 
9 
AUTOSAR SW Specification # 1 
Patch Version 
(XX_AR_PATCH_VERSION) 
M 
HN: 0-9 
LN: 0-9 
BCD 
10 
AUTOSAR SW Module Version # 1 
Major version 
(XX_SW_MAJOR_VERSION) 
M 
HN: 0-9 
LN: 0-9 
BCD 
11 
AUTOSAR SW Module Version # 1 
Minor version 
(XX_SW_MINOR_VERSION) 
M 
HN: 0-9 
LN: 0-9 
BCD 
12 
AUTOSAR SW Module Version # 1 
Patch Version 
(XX _ SW_PATCH_VERSION) 
M 
HN: 0-9 
LN: 0-9 
BCD 
: 
: 
: 
: 
: 
: 
: 
: 
: 
: 
12(n-1) 
Standard Identification # n 
 
00 = AUTOSAR Standard Module 
  
01 = DAI- Standard Module 
C 
00-01 
Hex 
12(n-1)+1 
 
12(n-1)+2 
AUTOSAR SW-Module Identification # n 
 
AUTOSAR Module ID 
 
 
(XX_MODULE_ID) 
 
AUTOSAR Supplier ID 
  
 
(XX_VENDOR_ID) 
C 
 
C 
00-FF 
 
00-FF 
Hex 
 
Hex 
12(n-1)+3 AUTOSAR SW Specification # n 
Major Version 
(XX_AR_MAJOR_VERSION) 
C 
HN: 0-9 
LN: 0-9 
BCD 
12(n-1)+4 AUTOSAR SW Specification # n 
Minor Version 
(XX_AR_MINOR_VERSION) 
C 
HN: 0-9 
LN: 0-9 
BCD 
12(n-1)+5 AUTOSAR SW Specification # n 
Patch Version 
(XX_AR_PATCH_VERSION) 
C 
HN: 0-9 
LN: 0-9 
BCD 
12(n-1)+6 AUTOSAR SW Module Version # n 
Major version 
(XX_SW_MAJOR_VERSION) 
C 
HN: 0-9 
LN: 0-9 
BCD 
12(n-1)+7 AUTOSAR SW Module Version # n 
Minor version 
(XX_SW_MINOR_VERSION) 
C 
HN: 0-9 
LN: 0-9 
BCD 
12(n-1)+8 AUTOSAR SW Module Version # n 
Patch Version 
(XX _ SW_PATCH_VERSION) 
C 
HN: 0-9 
LN: 0-9 
BCD 
 
 
 
n:  Number of AUTOSAR SW-Module Identification blocks reported. 
C: These response message parameters are only reported, if more than one AUTOSAR SW-Module 
Identification block is supported by a specific ECU. 
HN: High Nibble 
LN: Low Nibble 
 
NOTE: Please refer to http://www.automotive-his.de/standarde.htm  “HIS Software Supplier 
Identifications v.xx”  for latest definition of  AUTOSAR Supplier ID. 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 47 页
A0010023099: 2013-02, page 46 
 
 
Copyright Daimler AG 2013  
5.3.5 
Standard FlashBootLoader SW Package Information  [ EF 02 ]  r 
DID [Hex]: EF 02 
Description: 
This data identifier and associated data record is used to report information regarding the SW 
Integration Package, the build version and the software licence number of the FlashBootLoader . 
Message structure:  
Upon requesting data identifier [EF 02 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Standard FlashBootLoader  SW Package information. The data 
content associated with this data identifier shall match Table 97 . 
        
Table 97 : Positive Response Data Record Definition - Standard FlashBootLoader SW 
Package Information 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex Range 
Encoding 
 
 
 
0 
 
1 
 
2 
 
3 
 
FlashBootLoader Software Integration 
Package  ID 
 
SIP – Major Version 
 
 
SIP – Minor Version  
 
 
SIP – Patch Level Version 
 
 
SIP – Build Version 
 
 
 
 
M 
 
M 
 
M 
 
M 
 
 
 
 
HN: 0-9 
LN: 0-9 
HN: 0-9 
LN: 0-9 
HN: 0-9 
LN: 0-9 
HN: 0-9 
LN: 0-9 
 
 
 
 
BCD 
 
BCD 
 
BCD 
 
BCD 
 
 
4 
 
5 
 
6 
 
7 
FlashBootLoader Software Licence Number 
 
 
CBD Number Byte3  (MSB) 
 
 
 
CBD Number Byte2 
 
 
CBD Number Byte1 
 
 
CBD Number Byte0  (LSB) 
 
 
M 
 
M 
 
M 
 
M 
 
 
HN: 0-9 
LN: 0-9 
HN: 0-9 
LN: 0-9 
HN: 0-9 
LN: 0-9 
HN: 0-9 
LN: 0-9 
 
 
BCD 
 
BCD 
 
BCD 
 
BCD 
 
 
HN: High Nibble 
LN: Low Nibble 
 
5.3.6 
Standard SW Package Information  [ EF 03 ]  r 
DID [Hex]: EF 03 
Description: 
This data identifier and associated data record is used to report information regarding the SW 
Integration Package, the build version and the software licence number. 
Message structure:  
Upon requesting data identifier [EF 03 Hex] with the diagnostic service Read Data By Identifier  
[22 Hex], the ECU shall return the Standard  SW Package information. The data content associated 
with this data identifier shall match Table 99 . 
        
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 48 页
A0010023099: 2013-02, page 47 
 
 
Copyright Daimler AG 2013  
Table 99 : Positive Response Data Record Definition - Standard SW Package Information 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex 
Range 
Encoding 
 
 
 
0 
 
1 
 
2 
 
3 
 
SW Integration Package ID 
 
 
SIP – Major Version 
 
 
(static value = 0x10) 
 
SIP – Minor Version  
 
 
(VGEN_DELIVERY_VERSION_BYTE_0) 
 
SIP – Patch Level Version 
 
 
(VGEN_DELIVERY_VERSION_BYTE_1) 
 
SIP – Build Version 
 
 
(VGEN_DELIVERY_VERSION_BYTE_2) 
 
 
 
M 
 
M 
 
M 
 
M 
 
 
 
 
HN: 1 
LN: 0 
HN: 0-9 
LN: 0-9 
HN: 0-9 
LN: 0-9 
00-FF 
 
 
 
 
BCD 
 
BCD 
 
BCD 
 
Hex 
 
 
4 
 
5 
 
6 
 
7 
 Software Licence Number 
 
 
CBD Number Byte3  (MSB) 
 
 
(VGEN_DELIVERY_VERSION_BYTE_3) 
 
CBD Number Byte2 
 
 
(VGEN_DELIVERY_VERSION_BYTE_4) 
 
CBD Number Byte1 
 
 
(VGEN_DELIVERY_VERSION_BYTE_5) 
 
CBD Number Byte0  (LSB) 
 
 
(VGEN_DELIVERY_VERSION_BYTE_6) 
 
 
M 
 
M 
 
M 
 
M 
 
 
HN: 0-9 
LN: 0-9 
HN: 0-9 
LN: 0-9 
HN: 0-9 
LN: 0-9 
HN: 0-9 
LN: 0-9 
 
 
BCD 
 
BCD 
 
BCD 
 
BCD 
 
 
HN: High Nibble 
LN: Low Nibble 
 
For values of VGEN_DELIVERY_VERSION_BYTE_x see #defines in the headerfile  v_par.h of the 
generated software. 
 
5.3.7 
FlexRay Configuration Information  [F1 0E ]    r 
DID [Hex]: F1 0E 
Description:  
This Data Record provides information regarding configuration of the FlexRay controller registers.  
Message structure:  
Upon requesting [F1 0E Hex] with diagnostic service Read Data by Identifier [22 Hex], the ECU shall 
return the FlexRay Configuration Information.  
The data content associated with the data identifier shall match Table 106. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 49 页
A0010023099: 2013-02, page 48 
 
 
Copyright Daimler AG 2013  
Table 106 : Positive Response Data Record Definition - FlexRay Configuration Information 
 
Data 
Byte No. 
Parameter Name / Description 
Sup 
Hex Range  
Encoding 
0 
Network ID 
M 
33 
Hex 
1 
pSamplesPerMicrotick 
M 
00-FF 
Dec 
2 
gNumberOfStaticSlots 
M 
00-FF 
Dec 
3-4 
gdStaticSlot 
M 
0000-FFFF 
Dec 
5 
gdActionPointOffset 
M 
00-FF 
Dec 
6 
gdTSSTransmitter 
M 
00-FF 
Dec 
7 
gPayloadLengthStatic 
M 
00-FF 
Dec 
8 
gdMinislot 
M 
00-FF 
Dec 
9 
gdMinislotActionPointOffset 
M 
00-FF 
Dec 
10-11 
gdNIT 
M 
0000-FFFF 
Dec 
12 
gNetworkManagementVectorLength 
M 
00-FF 
Dec 
13 
pMacroInitialOffsetA 
M 
00-FF 
Dec 
14 
pMacroInitialOffsetB 
M 
00-FF 
Dec 
15 
pMicroInitialOffsetA 
M 
00-FF 
Dec 
16 
pMicroInitialOffsetB 
M 
00-FF 
Dec 
17 
gColdstartAttempts 
M 
00-FF 
Dec 
18 
gListenNoise 
M 
00-FF 
Dec 
19 
gdCASRxLowMax 
M 
00-FF 
Dec 
20 
gdWakeupSymbolRxIdle 
M 
00-FF 
Dec 
21 
gdWakeupSymbolRxLow 
M 
00-FF 
Dec 
22 
gdWakeupSymbolTxIdle 
M 
00-FF 
Dec 
23 
gdWakeupSymbolTxLow 
M 
00-FF 
Dec 
24 
pWakeupChannel 
M 
00-FF 
Dec 
25 
pWakeupPattern 
M 
00-FF 
Dec 
26-27 
pdMaxDrift 
M 
0000-FFFF 
Dec 
28-29 
pOffsetCorrectionOut 
M 
0000-FFFF 
Dec 
30-31 
pRateCorrectionOut 
M 
0000-FFFF 
Dec 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 50 页
A0010023099: 2013-02, page 49 
 
 
Copyright Daimler AG 2013  
 
Table 106 : Positive Response Data Record Definition - FlexRay Configuration Information 
 
Data 
Byte No. 
Parameter Name / Description 
Sup 
Hex Range  
Encoding 
32-33 pKeySlotId 
M 
0000-FFFF 
Dec 
34 
gMaxWithoutClockCorrectionFatal 
M 
00-FF 
Dec 
35 
gMaxWithoutClockCorrectionPassive 
M 
00-FF 
Dec 
36 
pAllowPassiveToActive 
M 
00-FF 
Dec 
37 
pClusterDriftDamping 
M 
00-FF 
Dec 
38 
pDelayCompensationA 
M 
00-FF 
Dec 
39 
pDelayCompensationB 
M 
00-FF 
Dec 
40 
pDecodingCorrection 
M 
00-FF 
Dec 
41-42 gdSampleClockPeriod 
M 
0000-FFFF 
Dec 
43-44 gdBit 
M 
0000-FFFF 
Dec 
45-46 gMacroPerCycle 
M 
0000-FFFF 
Dec 
47-48 gNumberOfMinislots 
M 
0000-FFFF 
Dec 
49-50 gdWakeupSymbolRxWindow 
M 
0000-FFFF 
Dec 
51-52 pdAcceptedStartupRange 
M 
0000-FFFF 
Dec 
53-54 gOffsetCorrectionStart 
M 
0000-FFFF 
Dec 
55-56 pLatestTx 
M 
0000-FFFF 
Dec 
57-60 pMicroPerCycle 
M 
0000000 -
FFFFFFFF 
Dec 
61-64 pdListenTimeout 
M 
0000000 -
FFFFFFFF 
Dec 
65 
pChannelsA 
M 
00-01 
Dec 
66 
pChannelsB 
M 
00-01 
Dec 
67 
pKeySlotUsedForStartup 
M 
00-01 
Dec 
68 
pKeyUsedForSynch 
M 
00-01 
Dec 
69 
pAllowHaltDueToClock 
M 
00-01 
Dec 
70 
(reserved) 
M 
00-FF 
Hex 
71 
(reserved) 
M 
00-FF 
Hex 
72 
(reserved) 
M 
00-FF 
Hex 
73 
(reserved) 
M 
00-FF 
Hex 
74 
(reserved) 
M 
00-FF 
Hex 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 51 页
A0010023099: 2013-02, page 50 
 
 
Copyright Daimler AG 2013  
 
5.3.8 
DMR_Timeout [ 01 2E ] r/w 
DID [Hex]: 01 2E 
Description: 
The DMR_Timeout value is used to control the diagnostic gateway routing behaviour. For details 
refer to MBN 10482 . 
Message structure: 
Upon requesting data identifier [01 2E Hex] with the diagnostic service Read Data By Identifier  
[22 Hex], the ECU shall return the current setting of the DMR_Timeout. The data content associated 
with the data identifier shall match Table 109. 
Upon requesting [01 2E Hex] with diagnostic service Write Data By Identifier [2E Hex], the ECU shall 
write the DMR_Timeout to the associated memory. The data content associated with the data 
identifier shall match Table 109. 
 
Table 109 : Request / Response Data Record Definition - DMR_Timeout 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Hex 
Range 
Encoding 
0 
DMR_Timeout 
          Resolution  1s/Bit   
M 
00 - FF 
Dec 
 
 
 
5.3.9 
Read VMM Version [EF 04] r 
Removed 
 
5.3.10 Read LIN Slope Mode [01 0F] r 
DID [Hex]: 01 0F 
Description: 
This data identifier provides information about the configured slope mode of a LIN component. 
Message structure: 
Upon requesting data identifier [01 0F Hex] with the diagnostic service Read Data By Identifier  
[22Hex], the ECU shall return the configured LIN slope mode. The data content associated with the 
data identifier shall match Table 116. 
 
 
Table 116 : Response Data Record Definition - LIN Slope Mode 
 
Data 
Byte  
No. 
Parameter Name 
Sup 
Hex 
Range 
Encoding 
 
0 
LIN Slope Mode 
 
0 - not defined / reserved 
1 - Low Slope Mode 
2 - Normal Slope Mode 
3 - Fast Slope Mode 
4 - Flash Slope Mode 
5..255 - not defined / reserved 
 
M 
 
01-04 
Hex 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 52 页
A0010023099: 2013-02, page 51 
 
 
Copyright Daimler AG 2013  
 
5.3.11 Activate Supplier Specific Messages [01 2A] r/w 
DID [Hex]: 01 2A 
Description: 
This data identifier is used to activate and deactivate the transmission of supplier specific application 
messages. It is also used to return the current configuration of the transmission of supplier specific 
application messages. For further information refer to the Networking Performance Specification. 
 
Message structure: 
Upon requesting data identifier [01 2A Hex] with the diagnostic service Read Data By Identifier  
[22 
Hex], the ECU shall return the current configuration of the transmission of supplier specific 
application messages.  
Upon requesting [01 2A Hex] with the diagnostic service Write Data by Identifier [2E Hex], the ECU 
shall write the configuration of the transmission of supplier specific application messages to the 
respective memory location in the non-volatile memory. The data content associated with the data 
identifier shall match Table 125. 
 
Table 125 : Request/Response Data Record Definition - Supplier Specific Message Mode. 
 
Data 
Byte  
No. 
Parameter Name 
Sup 
Hex 
Range 
Encoding 
 
0 
Supplier Specific Message Mode 
 
0 – Transmission deactivated 
 
      (Deactivate transmission) 
1 - Transmission activated 
      (Activate transmission) 
2 .. 255  reserved 
 
M 
00-01 
Hex 
 
 
 
 
5.4 
Supplier specific data 
5.4.1 
System Supplier ECU Hardware Part Number  [ F1 92 ]   r 
DID [Hex]: F1 92 
Description: 
This data identifier and associated data records are used to report the system supplies specific ECU 
Hardware Part Number. 
Message structure:  
Upon requesting data identifier [F1 92 Hex] with the diagnostic service Read Data By Identifier  
[22 Hex], the ECU shall return the System Supplier ECU Hardware Number. The data content 
associated with this data identifier shall be defined by supplier. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 53 页
A0010023099: 2013-02, page 52 
 
 
Copyright Daimler AG 2013  
5.4.2 
System Supplier ECU Hardware Version Number [ F1 93 ]    r 
DID [Hex]: F1 93 
Description: 
This data identifier and associated data records are used to report the system supplies specific ECU 
Hardware Version Number. 
Message structure:  
Upon requesting data identifier [F1 93 Hex] with the diagnostic service Read Data By Identifier  
[22 Hex], the ECU shall return the System Supplier ECU Hardware Version Number. The data 
content associated with this data identifier shall be defined by supplier. 
 
5.4.3 
System Supplier ECU Software Part Number  [ F1 94 ]    r 
DID [Hex]: F1 94 
Description: 
This data identifier and associated data records are used to report the system supplies specific ECU 
Software Part Number. 
Message structure: 
Upon requesting data identifier [F1 94 Hex] with the diagnostic service Read Data By Identifier  
[22 Hex], the ECU shall return the System Supplier ECU Software Number. The data content 
associated with this data identifier shall be defined by supplier. 
 
5.4.4 
System Supplier ECU Software Version Number  [ F1 95 ]     r 
DID [Hex]: F1 95 
Description: 
This data identifier and associated data records are used to report the system supplier’s specific 
ECU Software Version Number. 
Message structure:  
Upon requesting data identifier [F1 95 Hex] with the diagnostic service Read Data By Identifier  
[22 Hex], the ECU shall return the System Supplier ECU Software Version Number. The data 
content associated with this data identifier shall be defined by supplier. 
 
 
5.5 
Predefined Manufacturer Specific Data Identifier 
5.5.1 
Reprogramming Attempt Counter  [ 01 00 ]    r 
DID [Hex]: 01 00 
Description:  
The reprogramming attempt counter tracks the number of reprogramming attempts. It also displays 
the maximum number of reprogramming attempts. An ECU which has never been reprogrammed by 
a diagnostic tool will report a programming attempt counter of zero. The reprogramming attempt 
counter shall be incremented by each reprogramming attempt. The non-volatile memory of an ECU 
can be composed of several memory devices with different maximum numbers of reprogramming 
cycles. It is required to track the reprogramming attempts individually for each device or logical block 
(Counter 1 to n). 
Message structure:  
Upon requesting data identifier [01 00 Hex] with the diagnostic service Read Data By Identifier  
[22 Hex], the ECU shall return the Reprogramming Attempt Counter data record. The data content 
associated with the data identifier shall match Table 45. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 54 页
A0010023099: 2013-02, page 53 
 
 
Copyright Daimler AG 2013  
 
Table 45 :  Positive Response Data Record Definition - Reprogramming Attempt Counter 
 
Data 
Byte No. Parameter Name / Description 
Sup Hex value Encoding 
 
0 
1 
Number of Reprogramming Attempts – Counter #0 
 
Number of Reprogramming Attempts High Byte 
 
Number of Reprogramming Attempts Low Byte 
 
M 
M 
 
00-FF 
00-FF 
 
2 
3 
Max. Number of Reprogramming Attempts – Counter #0 
 
Max. Number of Reprogramming Attempts High Byte 
 
Max. Number of Reprogramming Attempts Low Byte 
 
M 
M 
 
00-FF 
00-FF 
Hex 
: 
: 
 
 
 
 
4(n-1) 
4(n-1)+1 
Number of Reprogramming Attempts – Counter #n 
 
Number of Reprogramming Attempts High Byte 
 
Number of Reprogramming Attempts Low Byte 
 
C 
C 
 
00-FF 
00-FF 
 
4(n-1)+2 
4(n-1)+3 
Max Number of Reprogramming Attempts – Counter #n 
 
Max. Number of Reprogramming Attempts High Byte 
 
Max. Number of Reprogramming Attempts Low Byte 
 
C 
C 
 
00-FF 
00-FF 
Hex 
 
 
 
n: maximum number of reprogramming attempt counters supported 
C: The number of Reprogramming Attempt Counters depends on the number of memory blocks with 
a different maximum number of allowed programming cycles. The ECU shall only report a 
Reprogramming Counter value if there is an actual assignment for a counter to a specific memory 
block. 
 
Default value:  
Default value of the programming counter for a logical block depends on the delivery state of the 
ECU Application software:  
 
Table 46 : Delivery State  Definition for Number of Reprogramming Attempts 
 
Delivery State 
 
Number of  Re-
programming 
Attempts  
- Default value [hex] 
Logical block erased and Programmed by the ECU supplier  
- High-Byte 
- Low-Byte 
00  
00  
Logical block erased by the ECU supplier and programmed by 
the vehicle manufacturer 
- High-Byte 
- Low-Byte 
00 
01  
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 55 页
A0010023099: 2013-02, page 54 
 
 
Copyright Daimler AG 2013  
5.5.2 
Historical Interrogation Record  [ 01 01 ]   r 
DID [Hex]: 01 01 
Description:  
This data identifier reports the content of the Historical Interrogation Record as defined in MBN 
10746. 
Message structure: 
Upon requesting data identifier [01 01 Hex] with the diagnostic service Read Data By Identifier  
[22 Hex], the ECU shall return the Historical Interrogation Record. The data content associated with 
the data identifier shall match Table 47. 
 
Table 47 : Positive Response Data record Definition - Historical Interrogation Record 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex Range 
Encoding 
0 
DTC Read Counter 
M 
00 - FF 
Hex 
 
Interrogation Record Odometer 
M 
 
Hex 
1 
 
High Byte 
 
00 - FF 
Hex 
2 
 
Low Byte 
 
00 - FF 
Hex 
 
 
 
5.5.3 
Diagnostic Trace Memory  [ 01 02 ]    r/w 
DID [Hex]: 01 02 
Description:  
The diagnostic trace memory is used by the service diagnostic tool to check e.g. the proper 
realization of test sequence steps performed by the service technician. This memory is used  by „PF-
Team“ and Warranty Return Center for analysis  (e. g. checking whether the defined repair 
procedure has been completely performed ).  
Analysis process and utilization of trace memory must be defined and fixed by  „PF-Team“ and 
Warrenty Return Center before implementation in the service diagnostic tool. 
The Diagnostic trace memory is defined in the first „Diagnostic Definition Meeting“  or at any other 
point in time if there are requirements of „PF-Team“ and Warrenty Return Center“. 
The diagnostic trace memory is a raw bit field which is interpreted by the service diagnostic 
application based on the individual use-case for an ECU. Therefore no specific interpretation/scaling 
is defined for this DID. 
Message structure: 
Upon requesting data identifier [01 02 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Diagnostic Trace Memory information. The data content 
associated with the data identifier shall match Table 48. 
 
NOTE: The ECU shall report 0x00 in data byte 0 to n if the diagnostic tool has never written test 
sequence data to the ECU. 
Upon requesting [01 02 Hex] with diagnostic service Write Data by Identifier [2E Hex], the ECU shall 
write the data transferred in the request message to the associated memory block. The data content 
associated with the data identifier shall match Table 48. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 56 页
A0010023099: 2013-02, page 55 
 
 
Copyright Daimler AG 2013  
Table 48 : Request / Response Data record Definition - Diagnostic Trace Memory 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex Range 
Encoding 
0 
Diagnostic Trace Memory Data Byte #1 
M 
00 - FF 
Hex 
: 
: 
: 
: 
: 
11 
Diagnostic Trace Memory Data Byte #12 
M 
00 - FF 
Hex 
12 
Diagnostic Trace Memory Data Byte #13 
C 
00 - FF 
Hex 
: 
: 
: 
: 
: 
15 
Diagnostic Trace Memory Data Byte #16 
C 
00 - FF 
Hex 
 
 
 
C: These data bytes shall be supported from ECUs engineered for BU MB VAN 
 
 
5.5.4 
VIN Odometer [ 01 03 ] r/w 
DID [Hex]: 01 03 
Description: 
The VIN Odometer value is used to accumulate the number of kilometers driven since the VIN 
Current has been received for the first time. Once the VIN Odometer value has reached or exceeded 
the VIN Odometer  Limit value set in Data Identifier [01 04 Hex] the VIN Original of an ECU is locked.  
This mechanism prevents the VIN Original from being updated after the vehicle has left the 
production/manufacturing environment. 
Message structure: 
Upon requesting data identifier [01 03 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the VIN Odometer. The data content associated with the data 
identifier shall match Table 49. 
Upon requesting [01 03 Hex] with diagnostic service Write Data By Identifier [2E Hex], the ECU shall 
write the data transferred in the request message to the associated memory block. The data content 
associated with the data identifier shall match Table 49. 
 
Table 49 : Request / Response Data Record Definition - VIN Odometer 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Hex 
Range 
Encoding 
0 
VIN Odometer 
M 
00 – FA 
Hex 
 
 
 
5.5.5 
VIN Odometer Limit  [ 01 04 ] r/w 
DID [Hex]: 01 04 
Description: 
The VIN Odometer Limit value is used to specify the maximum value of the VIN Odometer. As soon 
as the VIN Odometer value equals this specified or programmed value the VIN Original can no 
longer be changed. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 57 页
A0010023099: 2013-02, page 56 
 
 
Copyright Daimler AG 2013  
Message structure: 
Upon requesting data identifier [01 04 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the VIN Odometer Limit. The data content associated with the data 
identifier shall match Table 50. 
Upon requesting [01 04 Hex] with diagnostic service Write Data by Identifier [2E Hex], the ECU shall 
write the data transferred in the request message to the associated memory block. The data content 
associated with the data identifier shall match Table 50. 
As soon as the VIN Odometer value equals the configurable max value (default 250km) the ECU 
shall prevent the VIN Odometer Limit value from being changed at any time. 
 
Table 50: Request / Response Data Record Definition - VIN Odometer Limit 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Hex 
Range 
Encoding 
0 
VIN Odometer Limit 
M 
00 - FA 
Hex 
 
 
 
 
5.5.6 
In-use Histogram  [ 01 05 ]   r 
DID [Hex]: 01 05 
Description:  
This data identifier is used to retrieve logs from the ECU for in-use performance data (e.g. rpm, 
requested torque, vehicle speed etc). to provide a histogram of the overall usage over the lifetime of 
a vehicle. As the data is intended to be used for engineering purposes only the data is provided as a 
Hex dump to prevent the data from being analyzed in the dealership. 
Message structure:  
Upon requesting data identifier [01 05 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Usage Histogram information. The data content associated with 
the data identifier shall match Table 51. 
 
Table 51 : Positive Response Data Record Definition - Usage Histogram 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup 
Hex 
Range 
Encoding 
 
Usage Histogram data 
 
 
 
0 
 
Log Data Byte #0 
M 
00 - FF 
Hex 
: 
 
… 
… 
… 
n 
 
Log Data Byte #n 
C 
00 - FF 
Hex 
 
 
 
n: maximum number of Log Data Bytes supported. 
C: The maximum number of Log Data Bytes depends on the ECU type and memory size. 
Removed Network Mirroring Mode Configuration  [ 01 06 ] 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 58 页
A0010023099: 2013-02, page 57 
 
 
Copyright Daimler AG 2013  
5.5.7 
Network Mirroring Mode Configuration  [ 01 06 ]   r/w  
Removed  
 
5.5.8 
Removed 
Removed Response on Event-light Activation State  [ 01 07 ]   R/W 
 
5.5.9 
Removed 
Removed Ignition Status Enable Condition Calibration State [ 01 08 ] 
 
5.5.10 Configuration Write Counter  [ 01 09 ]   r 
DID [Hex]: 01 09 
Description:  
Each configuration data block (parameter block) shall contain a 2 byte Configuration Write Counter 
reflecting how often the configuration data of the respective block was changed on request. Initial 
value of the Configuration Write Counter shall be 0000 Hex. If the counter reaches its maximum 
value of FFFF Hex counting of configuration procedures shall be discontinued and the maximum 
value shall be reported upon request.  Please refer to model line specific requirements for details. 
Message structure: 
Upon requesting data identifier [01 09 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Configuration Write Counter data record. The complete data 
content of the response message is described in Table 53. 
 
Table 53 : Positive Response Data Record Definition - Configuration Write Counter 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex 
Range 
Encoding 
0 
Configuration Block #0 
M 
00-FF 
Hex 
 
1 
2 
Configuration Write Counter (Configuration Block #0) 
 
High Byte 
 
Low Byte 
M 
 
 
0000-
FFFF 
UINT 16 
 
 
3 
Configuration Block #1 
C 
00-FF 
Hex 
 
4 
5 
Configuration Write Counter (Configuration Block #1) 
 
High Byte 
 
Low Byte 
C 
 
 
0000- 
FFFF 
UINT 16 
 
 
6 
Configuration Block #2 
C 
00-FF 
Hex 
 
7 
8 
Configuration Write Counter (Configuration Block #2) 
 
High Byte 
 
Low Byte 
C 
 
 
0000- 
FFFF 
UINT 16 
 
 
9 
Configuration Block #3 
C 
00-FF 
Hex 
 
10 
11 
Configuration Write Counter (Configuration Block #3) 
 
High Byte 
 
Low Byte 
C 
 
 
0000- 
FFFF 
UINT 16 
 
 
 
 
C: The number of configuration data blocks depends on the number of configuration data blocks 
supported by the ECU (maximum 4 blocks). 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 59 页
A0010023099: 2013-02, page 58 
 
 
Copyright Daimler AG 2013  
5.5.11 Most Diagnostic Routing Table  [ 01 0A]    r 
DID [Hex]: 01 0A 
Description: 
This data identifier shall be used to retrieve Diagnostic Message Routing (DMR) information from a 
vehicle gateway. In order to perform DMR between a CAN network and a MOST network the vehicle 
gateway has to perform network address translation using a routing table which consists of a static 
part preconfigured in the vehicle gateway, and a dynamic part generated by the vehicle gateway 
during runtime. For further details refer to MBN 10482 Gateway Diagnostic Requirements . 
Message structure: 
Upon requesting data identifier [01 0A Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Diagnostic Routing Table information as defined in Table 54 
 
Table 54: Format of the MOST Diagnostic Routing Table 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex 
Range 
Encoding 
 
0 
1 
Diagnostic Request CAN-ID of MOST device #1 
 
CAN-ID – high byte 
 
CAN-ID – low byte  
M 
0000-
07FF 
Hex 
 
2 
3 
Diagnostic Response CAN-ID of MOST device #1 
 
CAN-ID – high byte 
 
CAN-ID – low byte  
M 
0000- 
07FF 
Hex 
 
4 
5 
RoE –light CAN-ID of MOST device #1 
 
CAN-ID – high byte 
 
CAN-ID – low byte  
M 
0000-
07FF 
Hex 
 
6 
7 
Unique device ID of MOST device #1 
 
Uni-ID – high byte 
 
Uni-ID – low byte  
M 
0000- 
FFFF 
Hex 
 
8 
9 
10 
Logical MOST slave device address #1 
 
Device address 
 
Main FBlock 
 
InstID 
 
M 
M 
M 
 
00-FF 
00-FF 
00-FF 
 
Hex 
Hex 
Hex 
: 
: 
- 
- 
 
 
m*11 – 11 
m*11 – 10 
Diagnostic Request CAN-ID of MOST device #m 
 
CAN-ID – high byte 
 
CAN-ID – low byte  
M 
 
 
0000-
07FF 
 
Hex 
 
 
m*11 – 9 
m*11 – 8 
Diagnostic Response CAN-ID of MOST device #m 
 
CAN-ID – high byte 
 
CAN-ID – low byte  
M 
 
 
0000-
07FF 
 
Hex 
 
 
m*11 – 7 
m*11 – 6 
RoE –light CAN-ID of MOST device #m 
 
CAN-ID – high byte 
 
CAN-ID – low byte  
M 
 
 
0000-
07FF 
 
Hex 
 
 
m*11 – 5 
m*11 – 4 
Unique device ID of MOST device #m 
 
Uni-ID – high byte 
 
Uni-ID – low byte  
M 
 
 
0000-
FFFF 
 
Hex 
 
 
m*11 – 3 
m*11 – 2 
m*11 -  1 
Logical MOST slave device address #m 
 
Device address 
 
Main FBlock 
 
InstID 
 
M 
M 
M 
 
00-FF 
00-FF 
00-FF 
 
Hex 
Hex 
Hex 
 
 
m: Number of MOST devices connected to the MOST network of the vehicle gateway. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 60 页
A0010023099: 2013-02, page 59 
 
 
Copyright Daimler AG 2013  
5.5.12 Adjust ISO 15765-2 Block Size and STmin Parameter [ 01 0B ]  r/w 
DID [Hex]: 01 0B 
Description: 
This service shall be used to aid in the reprogramming process when gateways implement varying 
buffer sizes and which may result in lost messages when the wrong BS and STmin parameters are 
used. 
It allows for the adjustment of the values which are reported by an ECU in the flow control frame 
upon reception of the first frame. 
Message structure: 
Upon requesting data identifier [01 0B Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the BS and STmin parameters as defined in Table 55. 
Upon requesting [01 0B Hex] with the diagnostic service Write Data by Identifier [2E Hex], the ECU 
shall write the BS and STmin values. The data content associated with the data identifier shall match 
Table 55. 
 
Table 55: Format of the Adjust ISO 15765-2 Block Size and STmin Parameter 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup Hex value Encoding 
0 
Block Size Value as defined in ISO15765-2 
M 
00-FF 
UINT 8 
1 
STmin Value as defined in ISO15765-2 
M 
00-7F 
F1–F9 
UINT 8 
 
 
 
5.5.13 Removed 
Removed Re-programming Error Status [ 01 0E ] 
 
5.5.14 DPM Status Information  [01 10 - 01 1F ]   r 
DID [Hex]: 01 10 - 01 1F 
Description:  
The DPM status information provides information about the status of the Decentralized Power 
Management module of the respective ECU. The ECU shall support this data record when the 
standard core module DPM is used. Refer to the Vector Technicalreference_DPM.pdf as part of the 
standard software documentation for further details. 
The DPM status information contains the following information: 
• 
an unsigned 16 bit counter (15 bits for counter value, MSB indicates an overflow) for the 
number of requests of all users for the external mode (bus communication send mode) when 
at the same time the power net controller function limits the ECU to local mode (no bus 
communication send mode, only receive mode) or the sleep mode 
• 
an unsigned 16 bit counter (15 bits for counter value, MSB indicates an overflow) for the 
number of events when the power net controller function requires a mandatory bus sleep 
• 
the mode requests of the first 32 DPM-users. 
• 
the maximum mode requests of the users 32 to 255 and the corresponding user number for 
the maximum mode requests 
• 
the actual activity mode of the ECU and the requests of the system user 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 61 页
A0010023099: 2013-02, page 60 
 
 
Copyright Daimler AG 2013  
Message structure:  
When requesting data identifier [01 10 Hex] the response shall include the number of available 
channels and the DPM status information associated with the last channel. The complete data 
content of the response message is described in Table 56. 
When requesting information of one specific channel the request shall use the following identifier:  
[01 1<selected channel number> Hex]. The channel numbering starts with one (1). The data content 
of the response message is described in Table 57. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 62 页
A0010023099: 2013-02, page 61 
 
 
Copyright Daimler AG 2013  
Table 56 : Positive Response Data Record Definition - DPM Status Information - Number of 
Channels / Last Channel 
 
Data Byte 
No. 
Parameter Name 
Sup 
Hex 
Range Encoding 
0 
Number of Channels 
M 
00 – 
FF 
Hex 
1 
Counter for inhibited external mode and/or local mode 
attempts (MSB) – Last Channel 
M 
00 – 
FF 
Hex 
2 
Counter for inhibited external mode and/or local mode 
attempts (LSB) – Last Channel 
M 
00 – 
FF 
Hex 
3 
Counter for forced bus sleep events (MSB) –  
Last Channel 
M 
00 – 
FF 
Hex 
4 
Counter for forced bus sleep events (LSB) –  
Last Channel 
M 
00 – 
FF 
Hex 
5 
Mode request of users 0...3  – Last Channel 
M 
00 – 
FF 
Hex 
 
Mode request coding for one user : 
00b: request sleep mode (default value) 
01b: request for local mode (only reception of mes-
sages allowed) 
10b: request for external mode (sending and receiv-
ing messages allowed) 
11b: reserved 
Note:  one byte holds the mode request values for 4 us-
ers,  
 
 that means for each user 2 bits are reserved 
 
 
 
6 
Mode request of users 4...7 – Last Channel 
M 
00 – 
FF 
Hex 
7 
Mode request of users 8...11 – Last Channel 
M 
00 – 
FF 
Hex 
8 
Mode request of users 12...15 – Last Channel 
M 
00 – 
FF 
Hex 
9 
Mode request of users 16...19 – Last Channel 
M 
00 - FF 
Hex 
10 
Mode request of users 20...23 – Last Channel 
M 
00 – 
FF 
Hex 
11 
Mode request of users 24...27 – Last Channel 
M 
00 – 
FF 
Hex 
12 
Mode request of users 28...31 – Last Channel 
M 
00 – 
FF 
Hex 
13 
User number for maximum mode request of users 
32...255 Last Channel 
M 
00 – 
FF 
Hex 
14 
Maximum requested mode of users 32...255 –  
Last Channel 
M 
00 - 02 
Hex 
 
Bit 0..1: Maximum mode requested by users 32...255 (en-
coding see description byte 5) 
Bit 2..7: reserved 
 
 
 
15 
Actual activity mode of ECU + system user mode 
(MSB) – Last Channel 
M 
00 - FF 
Hex 
16 
Actual activity mode of ECU + system user mode (LSB) 
– Last Channel 
M 
00 - FF 
Hex 
17 
reserved 
M 
FF 
Hex 
18 
reserved 
M 
FF 
Hex 
19 
reserved 
M 
FF 
Hex 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 63 页
A0010023099: 2013-02, page 62 
 
 
Copyright Daimler AG 2013  
 
 
Table 57 :  Positive Response Data Record Definition - DPM Status Information - Selected 
Channel 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex Range 
Encoding 
0 
Number of Selected Channel  
M 
00 – FF 
Hex 
1 
Counter for inhibited external mode and/or local mode 
attempts (MSB) – Selected Channel 
M 
00 – FF 
Hex 
2 
Counter for inhibited external mode and/or local mode 
attempts (LSB) – Selected Channel 
M 
00 – FF 
Hex 
3 
Counter for forced bus sleep events (MSB) –  
Selected Channel 
M 
00 – FF 
Hex 
4 
Counter for forced bus sleep events (LSB) –  
Selected Channel 
M 
00 – FF 
Hex 
5 
Mode request of users 0...3  – Selected Channel 
M 
00 – FF 
Hex 
 
Mode request coding for one user : 
00b:  request sleep mode (default value) 
01b:  request for local mode (only reception of 
 
messages allowed) 
10b:  request for external mode (sending and receiving 
 
messages allowed) 
11b:  reserved 
 
Note: one byte holds the mode request values for 4 users,  
          that means for each user 2 bits are reserved 
 
 
 
6 
Mode request of users 4...7 – Selected Channel 
M 
00 – FF 
Hex 
7 
Mode request of users 8...11 – Selected Channel 
M 
00 – FF 
Hex 
8 
Mode request of users 12...15 – Selected Channel 
M 
00 – FF 
Hex 
9 
Mode request of users 16...19 – Selected Channel 
M 
00 - FF 
Hex 
10 
Mode request of users 20...23 – Selected Channel 
M 
00 – FF 
Hex 
11 
Mode request of users 24...27 – Selected Channel 
M 
00 – FF 
Hex 
12 
Mode request of users 28...31 – Selected Channel 
M 
00 – FF 
Hex 
13 
User number for maximum mode request of users 32...255 
Selected Channel 
M 
00 – FF 
Hex 
14 
Maximum requested mode of users 32...255 –  
Selected Channel 
Bit 0..1:  Maximum mode requested by users 32...255 
 (encoding see description byte 5) 
Bit 2..7:  reserved 
M 
00 - 02 
Hex 
15 
Actual activity mode of ECU + System user mode (MSB) – 
Selected Channel 
M 
00 - FF 
Hex 
16 
Actual activity mode of ECU + System user mode (LSB) – 
Selected Channel 
M 
00 – FF 
Hex 
17 
reserved 
M 
FF 
Hex 
18 
reserved 
M 
FF 
Hex 
19 
reserved 
M 
FF 
Hex 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 64 页
A0010023099: 2013-02, page 63 
 
 
Copyright Daimler AG 2013  
 
5.5.15 Vehicle Configuration Programmed Status / Last Programmed Status [ 01 20 ] 
Removed 
 
5.5.16 Vehicle Configuration Write Counter [ 01 21 ] 
Removed 
 
5.5.17 Vehicle Configuration [01 22 - 01 2B ] 
Removed 
 
5.5.18 Moved to 5.6 
5.5.19 Moved to 5.6.1 
5.5.20 Moved to 5.6.2 
 
5.5.21 Read Used EVC Config  [01 0D]    r 
DID [Hex]: 01 0D 
Description:  
This data identifier reports the currently used configuration data a specific ECU has read from the 
EVC messages. 
Message structure: 
Upon requesting data identifier [01 0D Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return a counter representing the number of detected EVC configuration 
changes and the content of the used EVC configuration messages (detailed content defined in 
Candela file of the respective ECU). 
 The data content associated with the data identifier shall match Table 63. 
The 8-byte EVC Configuration data record #x shall have the same layout as the corresponding vmm 
message (see vehicle message matrix).  
Parts of data content within the message not used by the ECU shall be defined as “reserved” but the 
structure (bit and byte positions) shall be identical with the structure of the related vmm message. 
 
Table 63 : Positive Response Data Record Definition - Read Used EVC Config 
 
Data 
Byte No. 
Parameter Name  
Sup 
Hex 
Range 
Encoding 
0 
Number of detected EVC Config changes 
M 
00 - FF 
Uint8 
 
1 
: 
8 
EVC Configuration data record #1 
EVC Configuration data byte #1 
: 
EVC Configuration data byte #8 
 
 
M 
: 
M 
 
00 - FF 
: 
00 - FF 
 
Hex 
: 
Hex 
: 
: 
 
 
 
 
8(n-1)+1 
: 
8(n-1)+8 
 
EVC Configuration data record #n 
EVC Configuration data byte 1 
: 
EVC Configuration data byte 8 
 
 
C 
: 
C 
 
00 - FF 
: 
00 - FF 
 
Hex 
: 
Hex 
 
 
C: If ECU utilizes more than one EVC message  
For details regarding EVC refer to EVC Specification (A000 006 95 99)  
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 65 页
A0010023099: 2013-02, page 64 
 
 
Copyright Daimler AG 2013  
5.5.22 Read Odometer value from Bus [ 01 0C ] r 
DID [Hex]: 01 0C 
Description:  
This data record reflects the current ODO-Information read out from the vehicle´s Bus. 
Message structure: 
Upon requesting data identifier [01 0C] with the diagnostic service Read Data By Identifier [22 Hex], 
the ECU shall return the vehicles current ODO information as sent (or received) from the vehicles 
bus. The data content associated with the data identifier shall match Table 98. 
 
Table 98 : Positive Response Data Record Definition - Odometer value from Bus 
 
Data Byte 
No. 
Parameter Name 
Sup 
Range 
Encoding 
0  
Odometer value (MSB)  
M  
1 
Odometer value 
M 
2 
Odometer value (LSB) 
M 
000000-
FFFFFF 
Dec 
 
 
 
 
5.5.23 High Voltage Lock  [ 01 2C ]   r/w 
DID [Hex]: 01 2C 
Description: 
The “High Voltage Lock” (Transportprotection) prevents each active high voltage component to 
generate/supply high voltage even if commanded (e.g. “Close contactor in HV-Battery” ). Delivery 
status is locked. 
Message structure: 
Upon requesting data identifier [01 2C Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the status of the HV-Lock. The data content associated with the data 
identifier shall match Table 99 
Upon requesting data identifier [01 2C Hex] with the diagnostic service Write Data By Identifier 
[2E Hex], the ECU shall set the status of the HV-Lock to the value included in the Request message. 
The data content of the request parameter shall match Table 99. 
 
Table 99 : Request/ Positive Response Data record Definition - HV-Lock Status 
 
Data Byte 
No. 
Parameter Name 
Sup 
Hex 
Range 
Encoding 
0 
 
 
HV-Lock Status 
 
00: unlocked 
 
01: locked 
M 
 
 
00-01 
 
 
Enum 
 
 
 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 66 页
A0010023099: 2013-02, page 65 
 
 
Copyright Daimler AG 2013  
5.5.24 Engine Style [ 01 2D ] r 
DID [Hex]: 01 2D 
Description:  
This data record reports the type of engine mounted  in the respective vehicle.  
Message structure: 
Upon requesting data identifier [01 2D] with the diagnostic service Read Data By Identifier [22 Hex], 
the ECU shall return the engine style as coded in VMM signal name "EngStyle". The data content 
associated with the data identifier shall match Table 107. 
 
Table 107 : Positive Response Data Record Definition - Engine Style 
 
 
Data Byte 
No. 
Parameter Name 
Sup 
Range 
Encoding 
0  
Engine Style 
M  
00-FF 
See table 
108 
Encoding 
of Engine 
Style 
 
 
Table 108 : Encoding of Engine Style 
 
Dec 
Engine Style 
0 
M275 E55 [ME2.7.2] 
1 
M273 E55 [ME9.7] 
2 
M273 E46 [ME9.7] 
3 
M272 E35 [ME9.7] 
4 
M272 E30 [ME9.7] 
5 
M272 E25 [ME9.7] 
9 
M272 E35 DE [MED9.7] 
12 
M271 E18 ML attrac. (135 kW) [SIM271KE] 
13 
M271 E16/E18 ML attrac. (115 kW) [SIM271KE] 
14 
M272 E35 (224 kW) [ME9.7] 
15 
M276 E35 DEH LA [MED17.7] 
16 
M276 E35 DES [MED17.7] 
17 
M276 E30 DES [MED17.7] 
18 
M278 E46 DEH LA [MED17.7] 
19 
M271 E18 LA Evo (115kW) [SIM271DE] 
20 
M271 E18 LA Evo (150kW) [SIM271DE] 
21 
M271 E18 LA Evo (135kW) [SIM271DE] 
22 
M270 DE16 (90 kW) [] 
23 
M270 DE16 LA (115 kW) [] 
24 
M270 DE20 LA (150 kW) [] 
25 
M276 E35 DEH [MED17.7] 
26 
M276 E30 DEH [MED17.7] 
120 
AMG M159 E63 [ME9.7] 
121 
AMG M278 E55 DE [MED17.7] 
122 
AMG M278 E55 DEH LA [MED17.7] 
123 
AMG M156 E63 HP [ME9.7] 
124 
AMG M275 E60 LA [ME2.7.2] 
125 
AMG M157 E60 LA [ME9.7] 
126 
AMG M156 E63 [ME9.7] 
129 
OM642 DE30 LA (155/160 kW) [CR5/CR6/CR60] 
130 
OM629 DE40 LA [CR5] 
131 
OM642 DE30 LA red. (140 kW) [CR6] 
132 
OM646EVO DE22 LA (120/125 kW) [CRD] 
133 
OM646EVO DE22 LA red. (100 kW) [CRD] 
134 
OM646EVO DE22 LA (85 kW) [CRD] 
135 
OM651 DE22 LA (150 kW) [CRD2] 
136 
OM651 DE22 LA (120 kW) [CRD2] 
137 
OM651 DE22 LA (100 kW) [CRD2] 
138 
OM651 DE22 LA (80 kW) [CRD2] 
139 
OM642 DE30 LA (180/185kW) [CR60] 
140 
OM645 DE18 LA (80 kW) [EDC17] 
141 
OM645 DE18 LA (100 kW) [EDC17] 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 67 页
A0010023099: 2013-02, page 66 
 
 
Copyright Daimler AG 2013  
5.5.25 Adjust ISO 10681-2 Bandwidth Control Parameter [ 01 2B ]  r/w 
DID [Hex]: 01 2B 
Description:  
This services shall be used to change the Bandwidth Control Parameter used by the FlexRay 
transport layer during runtime 
Message structure: 
Upon requesting data identifier [01 2B Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Bandwidth Control Parameter as defined in Table 110. 
Upon requesting [01 2B Hex] with the diagnostic service Write Data by Identifier [2E Hex], the ECU 
shall write the Bandwidth Control Parameter value. The data content associated with the data 
identifier shall match Table 110. 
 
Table 110 : Format of the Adjust ISO 10681-2 Bandwidth Control Parameter 
 
 
Data Byte 
No. 
Parameter Name / Description 
Sup 
Hex 
value 
Encoding 
0 
Bandwidth Control Parameter 
value as defined in ISO10681-2 
 
SCexp 
The sub-parameter 'Separation Cycle 
Exponent' represents the exponent to 
calculate the minimum number of 
'Separation Cycles' (SC) the sender 
has to wait for the next transmission 
of a C_PDU.  
Formula: SC = (2
n
)-1 (with n = 
SCexp)" which results in the 
following separation cycles: 0, 1, 3, 
7, 15, 31, 63, 127 
 
MNPC 
Bit 7-3 
 
MNPC (Maximum Number of PDUs 
per Cycle 
Bit 0-2 
 
SCexp (Separation Cycle Exponent) 
 
The sub-parameter 'Maximum 
Number of PDUs per Cycle' limits 
the number of C_PDUs the sender is 
allowed to transmit within a FlexRay 
cycle either immediately following 
FC C_PDU or following SC cycles 
after the sender has sent the previous 
C_PDU of the message. The 
adherence of NMPC prevents an 
overflow on the receiver side (e.g. 
due to Rx-buffer restraints in the 
receiver).  
Note: Both parameter can be set 
independently  
M 
00-FF 
 
Bit 7-3: 0 
Bit 2-0: X 
 
 no 
Bandwidth 
Control 
 
 
Valid 
values: 
 
Bit 7-3: 1-
1F 
Bit 2-0: 0-7 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 68 页
A0010023099: 2013-02, page 67 
 
 
Copyright Daimler AG 2013  
5.5.26 HighVoltageValue  [ 01 2F ]   r 
DID [Hex]: 01 2F 
Description: 
This service shall be used to read out the current value of the high voltage unit. 
Message structure: 
Upon requesting data identifier [01 2F Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the value of the high voltage in Volt with no conversion. A value of FF 
FF shall state that the signal is currently not available.  
The data content associated with the data identifier shall match Table 111. 
 
Table 111 :  Positive Response Data record Definition - HighVoltageValue 
 
Data Byte 
No. 
Parameter Name 
Sup 
Range 
Encoding 
0  
HighVoltage value (MSB)  
M  
0000-
FFFF 
Dec 
1 
HighVoltage value (LSB) 
M 
 
 
e.g  250 Volt  =  00 FA 
       s.n.a        =  FF FF  
 
5.5.27 Compatibility List (for Onboard Configuration) [ E0 00 ]   r 
DID [Hex]: E0 00 
Description:  
The Compatibility List data record gives information about backward compatibility of configuration 
values and services for a configurable ECU. All ECU diagnostic versions (see also Active Diagnostic 
Information [F1 00 ]) which are fully compatible shall be listed. Compatibility defines the usage of the 
same configuration service, PIDs and values as used by the current diagnostic version of the 
software. 
Message structure: 
Upon requesting data identifier [E0 00 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the Compatibility List data record. The complete data content of the 
response message is described in Table 118. 
 
Table 118 : Positive Response Data Record Definition – Compatibility List 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex 
Range 
Encoding 
0 
Active Diagnostic Status #1 
M 
00-01 
Hex 
1 
Active Diagnostic Variant #1 
M 
00-FF 
Hex 
2 
Active Diagnostic Version #1 
M 
00-FF 
Hex 
.. 
… 
.. 
.. 
.. 
n-2 
Active Diagnostic Status #n 
C 
00-01 
Hex 
n-1 
Active Diagnostic Variant #n 
C 
00-FF 
Hex 
n 
Active Diagnostic Version #n 
C 
00-FF 
Hex 
 
 
C: If more than one diagnostic version is compatible to the parameterization format 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 69 页
A0010023099: 2013-02, page 68 
 
 
Copyright Daimler AG 2013  
5.5.28 GVC Update Time Stamp [ E0 01 ]   r 
DID [Hex]: E0 01 
Description:  
The GVC Update Time Stamp data record contains information regarding the most recent point in 
time when an ECU successfully had adopted the values of its respective GVC configuration 
messages.  
Initial value of the GVC update time stamp shall be $00, $00,$00. 
If no valid date signal is available the ECU shall store $FF, $FF, $FF. 
Message structure: 
Upon requesting data identifier [E0 01 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the GVC Update Time Stamp data record. The complete data content 
of the response message is described in Table 119. 
 
Table 119 : Positive Response Data Record Definition – GVC update time stamp 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex 
Range 
Encoding 
0 
Year 
M 
00-01 
Hex 
1 
Month 
M 
00-FF 
Hex 
2 
Day 
M 
00-FF 
Hex 
 
 
5.5.29 Read Discrete Signals [ 1E xx ]   r 
DID [Hex]: 1E xx 
Description:  
The Read Discrete Signals data record contains binary coded data as specified below for On-Board 
displaying purpose.  
The response shall only contain 1 Byte with maximum 4 binary coded signals. 
The the state encoding of a discrete signal is shown in Table 120. 
 
Table 120 : Standardized state encoding of a binary coded signal 
 
 
 
state 
definition 
00 
not activated (/ Off / not pressed) 
01 
activated (/ On / pressed) 
10 
intermittent (/flashing / undefined state) 
11 
signal not available (physically available but not configured or not available in general ) 
 
Message structure: 
Upon requesting data identifier [1E 00 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the number of available discrete signal bytes. The complete data 
content of the response message is described in Table 121. 
Upon requesting data identifier [1E xx Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the requested binary coded signal byte. The complete data content of 
the response message is described in Table 122. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 70 页
A0010023099: 2013-02, page 69 
 
 
Copyright Daimler AG 2013  
Table 121 : Positive Response Data Record Definition – Number of available discrete signal 
bytes 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex 
Range 
Encoding 
0 
Number of discete signal bytes 
M 
00-FF 
dec 
 
 
Table 122 : Positive Response Data Record Definition – Discrete signal byte number xx 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex 
Range 
Encoding 
0 
Discrete signal byte number xx 
 
7,6 
discrete signal d 
 
5,4 
discrete signal c 
 
3,2 
discrete signal b 
 
1,0 
discrete signal a 
M 
00-FF 
Hex 
 
 
5.5.30 Read Analog Signals [ 1D xx ]   r 
DID [Hex]: 1D xx 
Description:  
The Read Analog Signals data record contains present analog values as specified below for On-
Board displaying purpose.  
The data record shall contain 1 analog signal coded as a 4 byte single precision float according to 
IEEE 754 or as a 4 byte signed integer (complement of two). 
Message structure: 
Upon requesting data identifier [1D 00 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the number of available analog signals. The complete data content of 
the response message is described in Table 123. 
Upon requesting data identifier [1D xx Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the requested analog signal. The complete data content of the 
response message is described in Table 124. 
 
Table 123 : Positive Response Data Record Definition – Number of available analog signals 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex 
Range 
Encoding 
0 
Number of analog signals 
M 
00-FF 
dec 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 71 页
A0010023099: 2013-02, page 70 
 
 
Copyright Daimler AG 2013  
Table 124 : Positive Response Data Record Definition – Analog signal number xx 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex 
Range 
Encoding 
0 
MSB of  analog  signal (number  xx) 
M 
00-FF 
Hex 
1 
analog  signal (number  xx) 
M 
00-FF 
Hex 
2 
analog  signal (number  xx) 
M 
00-FF 
Hex 
4 
LSB of  analog  signal (number  xx) 
M 
00-FF 
Hex 
 
 
5.6 
OBD Info Types 
5.6.1 
Calibration Identifications (CAL ID)  [ F8 04 ]   r/w 
DID [Hex]: F8 04 
Description: 
Calibration identifications (CAL ID) shall uniquely identify the software installed in the ECU. A unique 
CAL ID shall be used for each emission-related calibration and/or software set having at least one bit 
of different data from any other emission-related calibration and/or software set.  Calibration 
identifications can include a maximum of sixteen (16) characters. Calibration identification shall 
contain only printable ASCII characters, and will be reported as ASCII values. Any unused data 
bytes shall be reported as [00 Hex] and filled at the end of the calibration identification. 
Message structure:  
Upon requesting data identifier [F8 04 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the CAL ID. The complete data content of the response message is 
described in Table 61. Upon requesting [F8 04 Hex] with the diagnostic service Write Data by 
Identifier [2E Hex], the ECU shall write the Calibration Identification to the respective memory 
location in the non-volatile memory. The data content associated with the data identifier shall match 
Table 61. 
 
Table 61 : Positive Response Data Record Definition - Calibration Identifications 
 
Data 
Byte 
No. 
Parameter Name  
Sup 
Hex 
Range 
Encoding 
 
Calibration Identification 
 
 
 
0 
: 
15 
 
Byte 1 
 
: 
 
Byte 16 
M 
: 
M 
00 – FF 
: 
00 - FF 
ASCII 
: 
ASCII 
 
 
 
NOTE: If the total number of bytes is lower than 16, all remaining bytes shall be padded with [00 
Hex]. 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 72 页
A0010023099: 2013-02, page 71 
 
 
Copyright Daimler AG 2013  
5.6.2 
Calibration Verification Numbers (CVN)  [ F8 06 ]   r 
DID [Hex]: F8 06 
Description:  
The Calibration Verification Number (CVN) is a checksum which verifies the integrity of the ECUs 
software. It shall be capable of being used to determine if software and calibration data are valid and 
applicable for a certain vehicle and CAL ID. 
Message structure:  
Upon requesting data identifier [F8 06 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the list of CVNs. The complete data content of the response message 
is described in Table 62 
 
Table 62 : Positive Response Data Record Definition - Calibration Verification Numbers 
 
Data 
Byte No. 
Parameter Name  
Sup 
Hex 
Range 
Encoding 
0 
Message Count Calibration Verification Number 
(Number of supported CVNs) 
M 
00 - FF 
Hex 
 
CVN List 
 
 
 
1 
: 
4 
 
CVN #1 Byte 1 
 
 
: 
 
CVN #1 Byte 4 
M 
: 
M 
00 – FF 
: 
00 - FF 
Hex 
: 
Hex 
: 
 
 
: 
: 
: 
: 
4(n-1) + 1 
: 
4(n-1) + 4 
 
CVN #n Byte 1 
 
 
: 
 
CVN #n Byte 4 
C 
: 
C 
00 – FF 
: 
00 - FF 
Hex 
: 
Hex 
 
 
 
n: number of Calibration Verification Numbers supported 
C: if more than one (1) CVN is supported 
 
 
5.6.3 
RBM HEX Gasoline [ F8 08 ]   r 
DID [Hex]: F8 08 
Description:  
The RBM HEX Gasoline service reports the rate based monitoring values required by legislation 
from OBDII related gasoline engine ECU. The data content of the data record shall be identical to the 
content of the OBD Mode 0x09 PID 0x08. 
Message structure:  
Upon requesting data identifier [F8 08 Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the RBM values. The complete data content of the response message 
is described in Table 117 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 73 页
A0010023099: 2013-02, page 72 
 
 
Copyright Daimler AG 2013  
Table 117 : Positive Response Data Record Definition -RBM HEX 
 
Data 
Byte No. 
Parameter Name  
Sup 
Hex 
Range 
Encoding 
0 
Number of data items 
M 
0 - 255 
Dec 
 
General Denominator 
 
 
 
1 
2 
 
 
MSB 
 
 
LSB 
M 
M 
00 – FF 
00 - FF 
Dec 
 
Ignition Cycle Counter 
 
 
 
3 
4 
 
 
 
MSB 
 
 
LSB 
M 
M 
00 – FF 
00 - FF 
Dec 
: 
Hex Dump according to ISO 15031-5  Mode 0x09 
PID 0x08 (for gasoline engines) or PID 0x0B (for 
Diesel engines) 
M 
 
M 
00 – FF 
: 
00 - FF 
Hex 
: 
Hex 
 
 
 
 
 
5.6.4 
RBM HEX Diesel [ F8 0B ]   r 
DID [Hex]: F8 0B 
Description:  
The RBM HEX Diesel service reports the rate based monitoring values required by legislation from 
OBDII related Diesel engine ECU. The data content of the data record shall be identical to the 
content of the OBD Mode 0x09 PID 0x0B. 
Message structure:  
Upon requesting data identifier [F8 0B Hex] with the diagnostic service Read Data By Identifier 
[22 Hex], the ECU shall return the RBM values. The complete data content of the response message 
is described in Table 117 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 74 页
A0010023099: 2013-02, page 73 
 
 
Copyright Daimler AG 2013  
6 
Standardized Routine Identifiers 
This chapter describes predefined routines assigned to specific Routine Identifiers in order to provide 
basic diagnostic functionality. 
 
6.1 
Synchronous and Asynchronous Routines 
According to MBN 10746 there are two different ECU routine operating modes implementing various 
scenarios. For each of the ECU routine operating modes different response behavior needs to be 
implemented. 
6.1.1 
Synchronous Routine Operating Mode 
In the synchronous routine operating mode the diagnostic tool expects only one final response which 
includes the test results. Depending on the specific routine the positive response may or may not 
include additional result data in the Routine Status Record. 
6.1.2 
Asynchronous Routine Operating Mode 
In the asynchronous routine operating mode the diagnostic tool expects one final response which 
may include test results in the Routine Status Record after starting the routine. In addition to the first 
response the current result of the execution of a routine shall be made available by an ECU 
according to Table 64 when receiving requests for Routine Results. Depending on the specific 
routine the positive response [71 03 xx xx]may or may not include additional result data in the 
Routine Status Record - Optional Data. 
 
Table 64 : Positive Response Routine Status Record - Sub-function Request Routine Results 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex Range 
Encoding 
0 
Routine Status 
M 
00 - 02 
Hex 
 
Successfully run to Completion 
 
00 
 
 
In process 
 
01 
 
 
Stopped without results 
 
02 
 
1 
Optional Data #1 
C 
00 - FF 
Hex 
: 
 
 
: 
: 
: 
: 
n 
Optional Data #n 
C 
00 - FF 
Hex 
 
 
C: These message parameters are only present when an ECU reports the result from the execution 
of a requested routine. 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 75 页
A0010023099: 2013-02, page 74 
 
 
Copyright Daimler AG 2013  
6.2 
Predefined Routine Identifiers 
6.2.1 
Erase Memory  [ FF 00 ] 
RID [Hex]: FF 00 
Type:  Synchron 
Description: 
The Routine Control Diagnostic Request initiates the Erase Memory step of the reprogramming 
sequence.  The positive response to the Routine Control Diagnostic Request provides status of the 
erase memory step of the reprogramming sequence. 
 
******* 
Data Content associated with MBN 10761 Rev C  and Previous 
Message structure: 
Upon receiving diagnostic service Routine Control [31 01 FF 00 Hex] the ECU shall start the Erase 
Memory routine.  The complete data content of the positive response message is described in  
Table 66. 
 
******* 
Data Content associated with MBN 10761 Rev D  and Later 
Message structure: 
Upon receiving diagnostic service Routine Control [31 01 FF 00 Hex]  with RoutineControlOption  
[01 xx Hex] the ECU shall start the Erase Memory routine for the associated memory section defined 
by the memory address index. The complete data content of the positive response message is 
described in Table 66. 
 
Table 65 : Request Routine Record Definition -EraseMemory 
 
Data Byte No. Parameter Name / Description 
Sup 
Hex Range 
Encoding 
 
0 
RoutineControlOption 
 
addressFormatIdentifier 
M 
 
 
01 
 
Hex 
 
1 
RoutineControlOption 
 
memoryAddressIndex (= logical Block) 
M 
 
 
00-FF 
 
Hex 
 
 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 76 页
A0010023099: 2013-02, page 75 
 
 
Copyright Daimler AG 2013  
Table 66 : Positive Response Routine Status Record - EraseMemory 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup Hex value Encoding 
0 
Erase Verification 
M 
00-FF 
Hex 
 
Erase Completed Successfully 
M 
00 
 
Erase Failed (general failure) 
M 
01 
Erase Failed – voltage too high 
C 
02 
Erase Failed – voltage too low 
C 
03 
Erase Failed – microprocessor temperature too high 
C 
04 
Erase Failed – microprocessor temperature too low 
C 
05 
Reserved 
M 
06-FF 
 
 
C: If an ECU can detect any of the errors listed above, it shall support the specific report value. 
 
6.2.2 
Check Routine  [ FF 01 ] 
RID [Hex]: FF 01 
Type:  Synchron 
Description: 
The Check Routine Request is used after a completed download sequence to verify the software 
downloaded. The Routine Status Record of the positive response includes several status flags (each 
represented by a bit) indicating the failure status of the routine. Depending on the ECU capabilities 
certain flags shall be supported. Depending on the implementation several flags may be set to one 
(1) in conjunction with others. For the specific usage of each flag refer to conditions described with 
each bit. 
 
NOTE:  This service is only assigned to MBN 10761 Rev C and Previous 
 
Message structure: 
Upon receiving diagnostic service Routine Control [31 01 FF 01 Hex] the ECU shall start the Check 
Routine. The complete data content of the positive response message is described in Table 67.  If 
additional Check Values are required the Request Routine Record shall match Table 68 for 32bit 
CRC or Table 70 for 32bit CRC with additional Signature bytes. Refer to MBN 10761 for more 
information. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 77 页
A0010023099: 2013-02, page 76 
 
 
Copyright Daimler AG 2013  
Table 67 : Positive Response Routine Status Record - Check Routine 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup Hex Range Encoding 
0 
Check Verification Result 
 
 
Bit 0 – Check Verification Failed - General Failure 
(failed = 1, passed = 0) 
C1 
 
Bit 1 – CRC Check Failed 
(failed = 1, passed = 0) 
C2 
 
Bit 2 – Signature Check Failed 
(failed = 1, passed = 0) 
C3 
 
Bit 3 – Reserved (set to zero) 
M 
 
Bit 4 – Reserved (set to zero) 
M 
 
Bit 5 – Reserved (set to zero) 
M 
 
Bit 6 – Reserved (set to zero) 
M 
 
Bit 7 – Read / Write Memory Failure 
(failed = 1, passed = 0) 
C4 
 00 – 07, 
80 – 87 
 
Hex 
 
 
C1: This flag shall be set to one (1) if a verification check has failed for any reason which is not 
covered by any of the other bits. Otherwise this flag shall be set to zero (0). 
C2:  This flag shall be set to one (1) if the CRC verification has failed. Otherwise this flag shall be set 
to zero (0). 
C3: This flag shall be set to one (1) if the ECU implements Signature Check functionality and the 
signature verification has failed. Otherwise this flag shall be set to zero (0). 
C4:  This flag shall be set to one (1) if the ECU is capable of detecting failures during read/write 
memory access (flash memory, EEPROM, RAM, etc.) and an error has occurred when trying to 
access memory. Otherwise this flag shall be set to zero (0). 
 
 
6.2.2.1 
Check Routine without additional CRC and without Additional Signature 
ECUs that provide reprogramming capability which use no additional CRC (CRC will be transmitted 
within the reprogramming data) and no additional Signature have no additional Request Routine 
Record (Security-Class NONE). 
 
******* 
Check Routine with additional CRC and without Additional Signature 
ECUs that provide reprogramming capability which use an additional CRC and no additional 
Signature shall implement the following Request Routine Record (Security-Class NONE): 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 78 页
A0010023099: 2013-02, page 77 
 
 
Copyright Daimler AG 2013  
Table 68 :  Request Routine Record Definition - Check Routine with an additional 32 Bit CRC 
 
Data byte No. Parameter Name / Description 
Sup 
Hex Value 
Encoding 
 
0 
1 
No. Of CRC Bytes #n 
 
No. Of CRC Bytes - High Byte 
 
No. Of CRC Bytes - Low Byte 
 
M 
M 
 
00 
04 
 
Hex 
Hex 
 
2 
: 
5 
CRC Values  
 
CRC Value High Byte 
 
: 
 
CRC Value Low Byte 
 
M 
: 
M 
 
00-FF 
: 
00-FF 
 
Hex 
: 
Hex 
 
 
 
Table 68 shows the Check Routine record with an additional 32 Bit CRC value. This record is 
required for Security-Class NONE for a Check Routine record using an additional 32 Bit CRC value. 
 
 
******* 
Check Routine without additional CRC and with Additional Signature 
ECUs that provide reprogramming capability which use no additional CRC (CRC will be transmitted 
within the reprogrammed data) and an additional Signature shall implement the following Request 
Routine Record (Security-Class C and CCC): 
 
Table 69 :  Request Routine Record Definition -Check Routine with an additional Signature 
 
Data byte No. Parameter Name / Description 
Sup 
Hex Range 
Encoding 
 
0 
1 
No. Of Signature Bytes #n 
 
No. Of Signature Bytes - High Byte 
 
No. Of Signature Bytes - Low Byte 
 
M 
M 
 
00-FF 
00-FF 
 
Hex 
Hex 
 
2 
: 
n+2 
Signature Values  
 
Signature Value High Byte 
 
: 
 
Signature Value Low Byte 
 
M 
: 
M 
 
00-FF 
: 
00-FF 
 
Hex 
: 
Hex 
 
 
n: maximum number of Signature Value bytes supported. 
 
Table 69 shows the Check Routine with an additional signature. This record is required for Security-
Class C and CCC for a Check Routine record using an additional signature 
• 
An ECU supporting Security Class C requires a 20 byte signature (n=20). 
• 
An ECU supporting Security Class CCC requires a 128 byte signature (n=128). 
 
 
******* 
Check Routine with additional CRC and with Additional Signature 
ECUs that provide reprogramming capability which use an additional CRC and an additional 
Signature shall implement the following Request Routine Record (Security-Class C and CCC): 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 79 页
A0010023099: 2013-02, page 78 
 
 
Copyright Daimler AG 2013  
Table 70 :  Request Routine Record Definition - Check Routine with an additional 32 Bit CRC 
and an additional Signature 
 
Data 
byte 
No. 
Parameter Name / Description 
Sup 
Hex 
Range 
Encoding 
 
0 
1 
No. Of CRC Bytes 
 
No. Of CRC Bytes - High Byte 
 
No. Of CRC Bytes - Low Byte 
 
M 
M 
 
00 
04 
 
Hex 
: 
Hex 
 
2 
: 
5 
CRC Values  
 
CRC Value High Byte 
 
: 
 
CRC Value Low Byte 
 
M 
: 
M 
 
00-FF 
: 
00-FF 
 
Hex 
: 
Hex 
 
6 
7 
No. Of Signature Bytes #n 
 
No. Of Signature Bytes - High Byte 
 
No. Of Signature Bytes - Low Byte 
 
M 
M 
 
00-FF 
00-FF 
 
Hex 
Hex 
 
8 
: 
n+8 
Signature Values  
 
Signature Value High Byte 
 
: 
 
Signature Value Low Byte 
 
M 
: 
M 
 
00-FF 
: 
00-FF 
 
Hex 
: 
Hex 
 
 
 
n: maximum number of Signature Value bytes supported. 
 
Table 70 shows the Check Routine with an additional 32 Bit CRC value and an additional signature. 
This record is required for Security-Class C and CCC for a Check Routine record using an additional 
32 Bit CRC value and an additional signature 
• 
An ECU supporting Security Class C requires a 20 bytes signature (n=20). 
• 
An ECU supporting Security Class CCC requires a 128 bytes signature (n=128). 
 
6.2.3 
Erase Mirror Memory DTCs  [ FF 02 ] 
RID [Hex]: FF 02 
Type:  Synchron 
Description: 
This routine identifier is used to clear the Mirror Memory (also referred to as Historical Stack as 
defined in MBN 10746) as well as the Historical Interrogation Record. Please refer to MBN 10746 - 
section “Historical Data Retention” for further details. 
Message structure: 
Upon receiving diagnostic service Routine Control [31 01 FF 02 Hex], the ECU shall erase all 
historical fault records and reset the elements of Historical Interrogation Record. This routine 
identifier does not support any routine specific parameters therefore the request message as well as 
the response message for this routine identifier only contains the routine identifier and no additional 
parameters. 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 80 页
A0010023099: 2013-02, page 79 
 
 
Copyright Daimler AG 2013  
6.2.4 
Check Programming Preconditions  [ FF 03 ] 
RID [Hex]: FF 03 
Type:  Synchron 
Description: 
A Routine Control Service for checking programming preconditions is sent before a download 
sequence is started in order to verify if any system conditions or ECU conditions are not met  
(e.g. vehicle speed, low voltage, high micro-temperature). There are all conditions listed which are 
not met (for each condition a value, which is not met). 
Message structure:  
Upon receiving diagnostic service Routine Control [31 01 FF 03 Hex], the ECU shall check for any 
programming preconditions. This routine identifier does not support any additional data for the Start 
Routine sub-function. The positive response message structure is described in Table 71 and the 
available Programming Preconditions are defined in Table 72.   
If multiple preconditions are not fulfilled the ECU shall return all preconditions that were not fulfilled in 
the positive response message. 
If the list is empty (Number of Programming Conditions = 0), all conditions to start the download 
sequence are fulfilled. 
The ECU shall send the positive response (including Number of Programming Conditions = 0), if no 
preconditions have to be fulfilled. 
 
Table 71 : Positive Response Routine - Check Programming Preconditions 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup Hex value Encoding 
0 
Number of Programming Conditions 
M 
00-FF 
Hex 
 
1 
: 
n 
Programming Precondition List 
 
Programming Condition No. 1 
 
: 
 
Programming Condition No. m 
 
C 
: 
C 
 
00-0C 
: 
00-0C 
see  
Table 72 
 
 
 
n:   maximum number of programming precondition bytes required. 
C:  If one or more preconditions are not fulfilled an individual message parameter needs to be 
added for each unfulfilled precondition. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 81 页
A0010023099: 2013-02, page 80 
 
 
Copyright Daimler AG 2013  
Table 72 : List of Programming Conditions 
 
Hex 
Value 
Description 
00 
Reserved 
01 
Engine Speed <> 0 
02 
Immobilizer System is not activated 
03 
Input shaft rpm <> 0 
04 
Output shaft rpm <> 0 
05 
Vehicle speed > 0 
06 
Control is active 
07 
Ignition off/on required 
08 
No programming voltage 
09 
Ignition off 
0A 
Low battery voltage 
0B 
High temperature 
0C 
Low temperature 
0D - 7F 
This range of values shall be reserved by this document for future definition. 
80 - BF 
This range of values shall be used for vehicle manufacturer specific data. 
C0 - FF 
This range of values shall be used for supplier specific data. 
 
 
 
 
6.2.5 
Check Memory  [ FF 04 ] 
RID [Hex]: FF 04 
Type:  Synchron 
Description: 
The Check Memory Request is used after a completed download sequence to verify the software 
downloaded. The Routine Status Record of the positive response includes several status flags (each 
represented by a bit) indicating the failure status of the routine. Depending on the ECU capabilities 
certain flags shall be supported. Depending on the implementation several flags may be set to one 
(1) in conjunction with others. For the specific usage of each flag refer to conditions described with 
each bit. 
NOTE:   This service is only assigned to MBN 10761 Rev D and later 
Message structure: 
Upon receiving the diagnostic service Routine Control [31 01 FF 04 Hex] the ECU shall start the 
Check Memory Routine. The complete data content of the positive response message is described 
in Table 73.  If additional Check Values are required the Request Routine Record shall match Table 
74 for 32bit CRC or Table 76 for 32bit CRC with additional Signature bytes. Refer to MBN 10761 for 
more information. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 82 页
A0010023099: 2013-02, page 81 
 
 
Copyright Daimler AG 2013  
Table 73 : Positive Response Routine Status Record - Check Momory 
 
Data 
Byte No. Parameter Name / Description 
Sup 
Hex Range 
Encoding 
0 
Check Verification Result 
 
 
Bit 0 – Check Verification Failed - General Failure 
(failed = 1, passed = 0) 
C1 
 
Bit 1 – CRC Check Failed 
(failed = 1, passed = 0) 
C2 
 
Bit 2 – Signature Check Failed 
(failed = 1, passed = 0) 
C3 
 
Bit 3 – Reserved (set to zero) 
M 
 
Bit 4 – Reserved (set to zero) 
M 
 
Bit 5 – Reserved (set to zero) 
M 
 
Bit 6 – Reserved (set to zero) 
M 
 
Bit 7 – Read / Write Memory Failure 
(failed = 1, passed = 0) 
C4 
 00-07; 
80-87 
Hex 
 
 
C1: This flag shall be set to one (1) if a verification check has failed for any reason which is not 
covered by any of the other bits. Otherwise this flag shall be set to zero (0). 
C2:  This flag shall be set to one (1) if the CRC verification has failed. Otherwise this flag shall be set 
to zero (0). 
C3:  This flag shall be set to one (1) if the ECU implements Signature Check functionality and the 
signature verification has failed. Otherwise this flag shall be set to zero (0). 
C4:  This flag shall be set to one (1) if the ECU is capable of detecting failures during read/write 
memory access (flash memory, EEPROM, RAM, etc.) and an error has occurred when trying to 
access memory. Otherwise this flag shall be set to zero (0). 
 
 
6.2.5.1 
Check Memory without additional CRC and without Additional Signature 
ECUs that provide reprogramming capability which use no additional CRC (CRC will be transmitted 
within the reprogramming data) and no additional Signature have no additional Request Routine 
Record (Security-Class NONE). 
 
 
******* 
Check Memory with additional CRC and without Additional Signature 
ECUs that provide reprogramming capability which use an additional CRC and no additional 
Signature shall implement the following Request Routine Record (Security-Class NONE): 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 83 页
A0010023099: 2013-02, page 82 
 
 
Copyright Daimler AG 2013  
Table 74 :  Request Routine Record Definition - Check Memory with an additional 32 Bit CRC 
 
Data 
byte 
No. 
Parameter Name / Description 
Sup 
Hex 
Value 
Encoding 
 
0 
1 
No. Of CRC Bytes #n 
 
No. Of CRC Bytes - High Byte 
 
No. Of CRC Bytes - Low Byte 
 
M 
M 
 
00 
04 
 
Hex 
Hex 
 
2 
: 
5 
CRC Values  
 
CRC Value High Byte 
 
: 
 
CRC Value Low Byte 
 
M 
: 
M 
 
00-FF 
: 
00-FF 
 
Hex 
: 
Hex 
 
 
 
Table 74 shows the Check Memory record with an additional 32 Bit CRC value. This record is 
required for Security-Class NONE for a Check Memory record using an additional 32 Bit CRC value. 
 
 
******* 
Check Memory without additional CRC and with Additional Signature 
ECUs that provide reprogramming capability which use no additional CRC (CRC will be transmitted 
within the reprogrammed data) and an additional Signature shall implement the following Request 
Routine Record (Security-Class C and CCC): 
 
Table 75 :  Request Routine Record Definition - Check Memory with an additional Signature 
 
Data byte No. Parameter Name / Description 
Sup 
Hex Range 
Encoding 
 
0 
1 
No. Of Signature Bytes #n 
 
No. Of Signature Bytes - High Byte 
 
No. Of Signature Bytes - Low Byte 
 
M 
M 
 
00-FF 
00-FF 
 
Hex 
Hex 
 
2 
: 
n+2 
Signature Values  
 
Signature Value High Byte 
 
: 
 
Signature Value Low Byte 
 
M 
: 
M 
 
00-FF 
: 
00-FF 
 
Hex 
: 
Hex 
 
 
n: maximum number of Signature Value bytes supported. 
 
Table 75 shows the Check Memory with an additional signature. This record is required for Security-
Class C and CCC for a Check Memory record using an additional signature 
• 
An ECU supporting Security Class C requires a 20 byte signature (n=20). 
• 
An ECU supporting Security Class CCC requires a 128 byte signature (n=128). 
 
 
******* 
Check Memory with additional CRC and with Additional Signature 
ECUs that provide reprogramming capability which use an additional CRC and an additional 
Signature shall implement the following Request Routine Record (Security-Class C and CCC): 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 84 页
A0010023099: 2013-02, page 83 
 
 
Copyright Daimler AG 2013  
Table 76 :  Request Routine Record Definition - Check Memory with an additional 32 Bit CRC 
and an additional Signature 
 
Data byte No. Parameter Name / Description 
Sup 
Hex Range 
Encoding 
 
0 
1 
No. Of CRC Bytes 
 
No. Of CRC Bytes - High Byte 
 
No. Of CRC Bytes - Low Byte 
 
M 
M 
 
00 
04 
 
Hex 
: 
Hex 
 
2 
: 
5 
CRC Values  
 
CRC Value High Byte 
 
: 
 
CRC Value Low Byte 
 
M 
: 
M 
 
00-FF 
: 
00-FF 
 
Hex 
: 
Hex 
 
6 
7 
No. Of Signature Bytes #n 
 
No. Of Signature Bytes - High Byte 
 
No. Of Signature Bytes - Low Byte 
 
M 
M 
 
00-FF 
00-FF 
 
Hex 
Hex 
 
8 
: 
n+8 
Signature Values  
 
Signature Value High Byte 
 
: 
 
Signature Value Low Byte 
 
M 
: 
M 
 
00-FF 
: 
00-FF 
 
Hex 
: 
Hex 
 
 
 
n: maximum number of Signature Value bytes supported. 
 
Table 76 shows the Check Memory with an additional 32 Bit CRC value and an additional signature. 
This record is required for Security-Class C and CCC for a Check Memory record using an additional 
32 Bit CRC value and an additional signature 
• 
An ECU supporting Security Class C requires a 20 bytes signature (n=20). 
• 
An ECU supporting Security Class CCC requires a 128 bytes signature (n=128). 
 
 
6.2.6 
Control  Fail Safe Reactions  [ FF 05 ] 
RID [Hex]: FF 05 
Type:  Asynchron 
Description:  
This Routine Identifier shall be used to deactivate all Fail Safe Reaction software routines which 
would run as part of the ECU application software if a loss of communication event (no message 
received from remote ECU) occurs. 
Message structure:  
Upon receiving the diagnostic service Routine Control [31 01 FF 05 xx Hex], the ECU shall disable 
any Fail Safe Reaction modes which would be activated if a loss of communication event occurred 
and positively respond with [71 01 FF 05 xx ]. The format of the request and the response message 
shall be according to Table 115. 
Upon requesting the diagnostic service Routine Control [31 02 FF 05 xx  Hex], the ECU shall no 
longer disable any Fail Safe Reaction modes and positively respond with [71 02 FF 05 xx ]. The 
format of the request and the response message shall be according to Table 115. 
 
Upon receiving the request [31 03 FF 05 Hex] the ECU shall report the current state of the Control 
Fail Safe Reactions routine. The format of the response message shall be according to Table 77. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 85 页
A0010023099: 2013-02, page 84 
 
 
Copyright Daimler AG 2013  
 
Table 115 : Routine Start /Stop - Request Routine Control Option Record Definition &  
Positive Response Routine Status Record Definition 
 
Data 
Byte 
No. 
Parameter Name / Description 
Sup Hex value Encoding 
0 
 
 
Fail Safe Reaction 
 
00:              inactive 
 
01 – FF :    Reserved 
M 
 
 
00 
 
 
Enum 
 
 
 
 
 
 
Table 77 : Routine Results Positive Response - Routine Status Record Definition 
 
Data 
Byte 
No. 
Parameter Name 
Sup Hex Range 
Encoding 
0 
Routine Status 
M 
00 - 02 
Hex 
 
Successfully run to Completion (inactive) 
 
00 
 
 
In process  (active) 
 
01 
 
 
Stopped without results (inactive) 
 
02 
 
 
 
 
 
6.2.7 
CheckCompatibilityDependencies  [ FF 06 ] 
RID [Hex]: FF 06 
Type:  Synchron 
Description:  
This Routine Identifier shall be used to verify the consistency of a downloaded software 
configuration. After software download to at least one logical block the software consistency shall be 
checked to determine whether no hardware/software mismatch or software/software mismatches are 
available. 
Message structure:  
Upon receiving diagnostic service Routine Control [31 01 FF 06 Hex], the ECU shall check the 
hardware/software and software/software dependencies and respond positively with  
[71 01 FF 06 hex].   
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 86 页
A0010023099: 2013-02, page 85 
 
 
Copyright Daimler AG 2013  
Table78 : Response Routine Record Definition -  CheckCompatibilityDependencies 
 
Data Byte No. Parameter Name / Description 
Sup 
Hex Range 
Encoding 
0 
Check Verification Result 
 
 
 
Dependencies Check passed - No Failure 
M 
00 
 
Dependencies Check failed  
– internal check routine error  
M 
01 
 
Dependencies Check failed  
– HW/SW mismatch detected 
M 
02 
 
Dependencies Check failed  
– SW/SW mismatch detected 
M 
03 
 
Dependencies Check failed  
– at least one logical block not valid 
M 
04 
 
Reserved 
 
05-FF 
Hex 
 
 
 
Check sequence: 
For details regarding the check sequence please refer to MBN 10761 
 
 
Removed 
 
Figure 1:  FlowChart  CheckCompatibilityDependencies 
 
 
6.3 
Predefined Manufacturer Specific Routine Identifiers 
6.3.1 
Removed 
Removed LIN Slave Node Data Routing  [02 00] 
 
6.3.2 
Removed 
Removed Clear Diagnostic Information - Not Tested DTC Stack  [ 02 10 ] 
 
6.3.3 
Removed 
Removed  Report DTC Information from Not Tested DTC Stack [ 02 11 ] 
 
 
6.3.4 
Reset VIN Values [ 02 12 ] 
RID [Hex]: 02 12 
Type:  Synchron 
Description: 
This Routine Identifier shall be use for development purposes only and therefore only be supported 
during development. Testing the correct implementation of the VIN handling necessitates the 
possibility to reset all VIN related data records (VIN Current, VIN Original, VIN Odometer and VIN 
Odometer Limit). 
This routine shall be removed from the the ECUs application software at a latest together with the 
last planned software change (e.g. quality gate B) 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 87 页
A0010023099: 2013-02, page 86 
 
 
Copyright Daimler AG 2013  
Message structure: 
Upon receiving diagnostic service Routine Control  [31 01 02 12 Hex], the ECU shall reset VIN 
Original, VIN Current, VIN Odometer and VIN Odometer Limit to the respective default value. 
The diagnostic request and response message of this particular Routine Control request do not 
contain any optional data records. 
 
6.3.5 
Removed 
Removed Set Fault Status [ 02 13 ] 
6.3.6 
Removed 
Removed Request Echo Routine  [02 14] 
6.3.7 
Removed 
Removed Control Network Mirroring Mode[02 15] 
6.3.8 
Removed 
Removed Fast Routing Mode [02 16] 
 
6.3.9 
EVC Protected Reset [02 01] 
RID [Hex]: 02 01 
Type:  Synchron 
Description: 
This routine is used to reset all EVC related data to the respective default value. 
For details refer to EVC Specification (A000 006 95 99)  
Message structure: 
Upon receiving diagnostic service Routine Control  [31 01 02 01 Hex], the ECU shall reset 
the data described in EVC Specification to the respective default value. 
The diagnostic request and response message of this particular Routine Control request do not 
contain any 
optional data records. 
 
 
6.3.10 Switch Test Mode  [ 02 18 ] 
RID [Hex]: 02 18 
Type:  Asynchron 
Description: 
This asynchronous routine is used to disable the function during switch actuation (e.g. horn, wiper). 
Message structure: 
Upon receiving the request [31 01 02 18 Hex] the ECU shall disable the execution of the function 
which is normally initiated by switch actuation.  
Upon receiving diagnostic service Routine Control  [31 02 02 18 Hex] the ECU shall stop the Switch 
Test  Mode and return to normal message routine behaviour. In addition the ECU shall reset the 
status and signals of each connected button to "OFF" and send the related bussed signals where 
appropriate. 
Upon receiving diagnostic service Routine Control  [31 03 02 18 Hex] the ECU shall report the 
current state of the Switch Test  Mode. The format of the response message shall be according to 
Table 101. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 88 页
A0010023099: 2013-02, page 87 
 
 
Copyright Daimler AG 2013  
 
Table 101 : Routine Status Record Definition - Switch Test  Mode 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex 
Range 
Encoding 
0 
Routine Status 
M 
00 - 01 
Hex 
 
Successfully run to Completion (inactive) 
 
00 
 
 
In process  (active) 
 
01 
 
 
 
 
Execution of this routine shall only be allowed in the extended session and shall be terminated after 
leaving this session.   
 
 
6.3.11 ECU self-test   [ 02 19 ] 
RID [Hex]: 02 19 
Type:  Asynchron 
Description: 
This asynchronous routine is used to activate all I/Os of an ECU which can be executed without 
manual interaction (automatic testing) to ensure checking for all DTCs only testable with functionality 
turned on. The I/Os shall be activated until the mature criteria is set and afterwards automatically 
deactivated. 
Message structure: 
Upon receiving diagnostic service Routine Control  [31 01 02 19  Hex] the ECU shall activate all I/Os 
which can be executed without manual interaction, until the mature criteria is set and afterwards 
automatically deactivate the respective I/O. 
UUpon receiving diagnostic service Routine Control [31 02 02 19 Hex] the ECU shall stop the ECU 
Self Test Routine deactivate all I/Os.  
Upon receiving diagnostic service Routine Control [31 03 02 19 Hex] the ECU shall report the current 
state of the ECU Self Test Routine. The format of the response message shall be according to Table 
102. 
 
Table 102 : Routine Status Record Definition -ECU self-test 
 
Data 
Byte 
No. 
Parameter Name 
Sup 
Hex 
Range 
Encoding 
0 
Routine Status 
M 
00 - 01 
Hex 
 
Successfully run to Completion (inactive) 
 
00 
 
 
In process  (active) 
 
01 
 
 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 89 页
A0010023099: 2013-02, page 88 
 
 
Copyright Daimler AG 2013  
6.3.12 FBS4 specific routines (Details defined in FBS4 Specification) [02 30 - 02 3F] 
RID [Hex]: 02 30 - 02 3F 
Type:  Synchron 
Description: 
These routines are used to personalize, activate and retrieve FBS4 specific datasets. 
For details refer to FBS4 Specification which will be provided for FBS4 relevant control units.    
Message structure: 
Upon receiving diagnostic service Routine Control  [31 01 02 3x  xx ... xx Hex] and  [31 03 02 3x  xx 
... xx Hex] the ECU shall provide the functionality described in the FBS4 Specification and 
implemented in the FBS4 software module. 
The data content of the diagnostic request and response messages of these particular Routine 
Control requests are confidential and described in the FBS4 Specification . 
 
6.4 
Removed 
Removed Configure DTC Suppression Flag  [02 17] 
 
 
6.5 
Reserved Daimler Trucks Specific Routines  [ 02 20 - 02 2F ] 
RID [Hex]: 02 20 - 02 2F 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 90 页
A0010023099: 2013-02, page 89 
 
 
Copyright Daimler AG 2013  
7 
Read DTC Environmental Data 
According to MBN 10746 each individual DTC stored in the chrono stack or the historical stack shall 
support a specific set of standardized environmental data and may support optional environmental 
data. This chapter describes how environmental data (standardized and optional) shall be reported 
via extended data records as defined in UDS. Refer to MBN 10746 for further details on DTC 
environmental data. 
 
7.1 
Report Chrono Stack Environmental Data 
7.1.1 
Standardized Environmental Data 
Support: Mandatory 
Description: 
Upon requesting [19 06 DTC1 DTC2 DTC3 01] the ECU shall report the standardized environmental 
data record stored for one specific DTC. 
DTCX = A DTC consisting of 3 Bytes → DTC High Byte, DTC Middle Byte, DTC Low Byte 
Message structure: 
The format of the response shall match  Table 85. 
 
Table 85 :  Response Definition Read DTC Information - Report Standardized Environmental 
Data 
 
Data 
byte 
no. 
Parameter name 
Sup 
Hex Value 
Encoding 
 
DTC And Status Record 
 
 
 
 
0 
1 
2 
DTC number 
DTC – high byte 
 
DTC – middle byte 
 
DTC – low byte  
 
M 
M 
M 
 
00-FF 
00-FF 
00-FF 
 
Hex 
Hex 
Hex 
3 
Status Of DTC  
M 
00-FF 
Hex 
4 
DTC Extended Data Record Number – Record #1 
C 
01 
UINT 8 
 
DTC Extended Data Record – 
Standardized Environmental Data 
 
 
 
5 
Enhanced DTC Information :  
Bit 0 : Occurrence Flag  
(0 = Fault / 1 = Occurrence) 
Bit 1: External Tester Present Flag  
(0 = Not Present / 1 = Present) 
Bit 2–7 : reserved  (set to 0) 
C 
00-03 
Enum 
 
6 
7 
Original Odometer Value 
High Byte 
Low Byte 
C 
 
0000-FFFF  
UINT 16 
 
8 
9 
Most Recent Odometer Value 
High Byte 
Low Byte 
C 
C 
0000-FFFF 
UINT 16 
10 
Frequency Counter 
C 
00-FF 
UINT 8 
11 
Ignition Cycle Counter 
C 
00-FF 
UINT 8 
 
 
 
C: These message parameters are only present if the requested DTC was stored in the chrono-
stack or has status pending 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 91 页
A0010023099: 2013-02, page 90 
 
 
Copyright Daimler AG 2013  
7.1.2 
Optional Environmental Data 
Support: Conditional 
An ECU may support Optional Environmental Data when additional data is necessary to be stored 
with a DTC e.g. to do further fault analysis. 
Description: 
Upon requesting [19 06 DTC1 DTC2 DTC3 FF] the ECU shall report all environmental data stored for 
one specific DTC. 
DTCX = A DTC consisting of 3 Bytes → DTC High Byte, DTC Middle Byte, DTC Low Byte 
Message structure: 
The format of the response shall match Table 86. 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 92 页
A0010023099: 2013-02, page 91 
 
 
Copyright Daimler AG 2013  
Table 86 :  Response Definition Read DTC Information - Report All Environmental Data 
 
Data byte 
no. 
Parameter name 
Sup 
Hex 
Value 
Encoding 
 
DTC And Status Record 
 
 
 
 
0 
1 
2 
 
DTC number 
 
 
DTC – high byte 
 
 
DTC – middle byte 
 
 
DTC – low byte  
 
M 
M 
M 
 
00-FF 
00-FF 
00-FF 
 
Hex 
Hex 
Hex 
3 
Status Of DTC  
M 
00-FF 
Hex 
4 
DTC Extended Data Record Number – Record #1 
C1 
01 
UINT 8 
 
DTC Extended Data Record – 
Standardized Environmental Data 
 
 
 
5 
Enhanced DTC Information :  
Bit 0 : Occurrence Flag  
(0 = Fault / 1 = Occurrence) 
Bit 1: External Tester Present Flag  
(0 = Not Present / 1 = Present) 
Bit 2–7 : reserved  (set to 0) 
C1 
00-03 
Enum 
 
6 
7 
Original Odometer Value 
High Byte 
Low Byte 
C1 
0000-
FFFF 
UINT 16 
 
8 
9 
Most Recent Odometer Value 
High Byte 
Low Byte 
C1 
0000-
FFFF 
UINT 16 
10 
Frequency Counter 
C1 
00-FF 
UINT 8 
11 
Ignition Cycle Counter 
C1 
00-FF 
UINT 8 
12 
DTC Extended Data Record Number – Record #2 
C2 
02 
UINT 8 
 
DTC Extended Data Record – 
Environmental Data Record #2 
 
 
 
13 
: 
13 + (k-1) 
Environmental Data Record # 2 - Byte #1 
: 
Environmental Data Record # 2 - Byte #k 
C2 
: 
C2 
00-FF 
: 
00-FF 
HEX 
: 
HEX 
: 
: 
: 
: 
: 
q-p-1 
DTC Extended Data Record Number – Record #n 
C2 
03-FF 
UINT 8 
 
DTC Extended Data Record – 
Environmental Data Record # n 
 
 
 
q-p 
: 
q 
Environmental Data Record # n - Byte #1 
: 
Environmental Data Record # n - Byte #p 
C2 
: 
C2 
00-FF 
: 
00-FF 
HEX 
: 
HEX 
 
 
 
n:  Number of Extended Data Records stored for a specific DTC 
k, p: Number of Bytes assigned to a certain Extended Data Record 
C1:  These message parameters are only present if the requested DTC was stored in the chrono-
stack or has status pending 
C2:  These message parameters are only present if optional environmental data for a certain DTC is 
supported and if the requested DTC was stored in the chrono-stack or has status pending 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 93 页
A0010023099: 2013-02, page 92 
 
 
Copyright Daimler AG 2013  
7.2 
Report Historical Stack Environmental Data 
7.2.1 
Standardized Environmental Data 
Support: Mandatory 
Description: 
Upon requesting [19 10 DTC1 DTC2 DTC3 01] the ECU shall report the standardized environmental 
data record stored for the requested DTC from the historical stack. 
DTCX = A DTC consisting of 3 Bytes → DTC High Byte, DTC Middle Byte, DTC Low Byte 
Message structure: 
The format of the response shall match Table 87.   
If the requested DTC is supported but not stored in the Historical Stack at the time of request the 
positive response shall contain the requested DTC number and shall set the associated Status of 
DTC byte to [00 hex]. 
 
Table 87 :  Response Definition Read DTC Information - Report Historical Stack Standardized 
Environmental Data 
 
Data 
byte 
no. 
Parameter name 
Sup 
Hex Value 
Encoding 
 
DTC And Status Record 
 
 
 
 
0 
1 
2 
 
DTC number 
 
 
DTC – high byte 
 
 
DTC – middle byte 
 
 
DTC – low byte  
 
M 
M 
M 
 
00-FF 
00-FF 
00-FF 
 
Hex 
Hex 
Hex 
3 
Status Of DTC  
M 
00-FF 
Hex 
4 
DTC Extended Data Record Number – Record No. 1 
C 
01 
UINT 8 
 
DTC Extended Data Record – 
Standardized Environmental Data 
 
 
 
5 
Enhanced DTC Information :  
Bit 0 : Occurrence Flag  
(0 = Fault / 1 = Occurrence)  
Bit 1: External Tester Present Flag  
(0 = Not Present / 1 = Present) 
Bit 2–7 : reserved (set to 0) 
C 
00-03 
Enum 
 
6 
7 
Original Odometer Value 
High Byte 
Low Byte 
 
 
C 
0000-FFFF 
UINT 16 
 
 
C: These message parameters are only present if the requested DTC was stored in the historical 
stack. 
 
7.2.2 
Optional Environmental Data 
Support: Conditional 
An ECU may support Optional Environmental Data when additional data is necessary to be stored 
with a DTC e.g. to do further fault analysis. 
Description: 
Upon requesting [19 10 DTC1 DTC2 DTC3 FF] the ECU shall report all environmental data stored for 
the requested DTC from the historical stack. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 94 页
A0010023099: 2013-02, page 93 
 
 
Copyright Daimler AG 2013  
 
 
DTCX = A DTC consisting of 3 Bytes → DTC High Byte, DTC Middle Byte, DTC Low Byte 
Message structure: 
The format of the response shall match Table 88. 
If the requested DTC is supported but not stored in the Historical Stack at the time of request the 
positive response shall contain the requested DTC number and shall set the associated Status of 
DTC byte to [00 hex]. 
 
Table 88 :  Response Definition Read DTC Information - Report All Historical Stack 
Environmental Data 
 
Data byte 
no. 
Parameter name 
Sup Hex value Encoding 
 
DTC And Status Record 
 
 
 
 
0 
1 
2 
 
DTC number 
 
 
DTC – high byte 
 
 
DTC – middle byte 
 
 
DTC – low byte  
 
M 
M 
M 
 
00-FF 
00-FF 
00-FF 
 
Hex 
Hex 
Hex 
3 
Status Of DTC  
M 
00-FF 
Hex 
4 
DTC Extended Data Record Number – Record #1 
C1 
01 
UINT 8 
 
DTC Extended Data Record – 
Standardized Environmental Data 
 
 
 
5 
Enhanced DTC Information :  
Bit 0 : Occurrence Flag  
(0 = Fault / 1 = Occurrence) 
Bit 1: External Tester Present Flag  
(0 = Not Present / 1 = Present) 
Bit 2–7 : reserved 
C1 
00-03 
Enum 
 
6 
7 
Original Odometer Value 
High Byte 
Low Byte 
 
 
C1 
0000-
FFFF 
UINT 16 
8 
DTC Extended Data Record Number – Record #2 
C2 
02 
UINT 8 
 
DTC Extended Data Record – 
Environmental Data Record #2 
 
 
 
9 
: 
9 + (k-1) 
Environmental Data Record #2 – Byte #1 
: 
Environmental Data Record #2 – Byte #k 
C2 
: 
C2 
00 - FF 
: 
00 - FF 
HEX 
: 
HEX 
: 
: 
: 
: 
: 
q-p-1 
DTC Extended Data Record Number – Record #n 
C2 
03-FF 
UINT 8 
 
DTC Extended Data Record – 
Environmental Data Record #n 
 
 
 
q-p 
: 
Q 
Environmental Data Record #n – Byte #1 
: 
Environmental Data Record #n – Byte #p 
C2 
: 
C2 
00 - FF 
: 
00 - FF 
HEX 
: 
HEX 
 
 
n:     Number of Extended Data Records stored for a specific DTC 
k, p: Number of Bytes assigned to a certain Extended Data Record 
q:     Last byte position in response 
C1: These message parameters are only present if the requested DTC was stored in the historical 
stack 
C2: These data parameters are only available if optional environmental data for a certain DTC is 
supported. 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 95 页
A0010023099: 2013-02, page 94 
 
 
 
A0010023099: 2013-02, page 94 
 
8 
DID and RID Implementation Conventions 
8.1 
Overview - Data Identifiers 
This section defines the applicable Data Identifier ranges allocated for the respective use case. The development engineer and the supplier shall adhere to the 
definitions given inTable 89 below to ensure diagnostic and ISO conformity.  Descriptions of the support conditions are defined in Table 91 
 
Table 89: Data Identifier Overview 
 
Description 
DID type 
DID code 
[hex] 
Status 
MBC_ 
Supp-
Cond 
Trucks_
Supp-
Cond 
VAN_ 
Supp-
Cond 
Reserved for future ISO/SAE definition 
Reserved ISO/SAE 
0000 - 00FF 
Reserved 
n/a 
n/a 
n/a 
Reprogramming Attempt Counter 
Predefined Man Specific Data Records 
0100 
Defined 
C3 
C3 
C3 
Historical Interrogation Record 
Predefined Man Specific Data Records 
0101 
Defined 
R 
R 
R 
Diagnostic Trace Memory 
Predefined Man Specific Data Records 
0102 
Defined 
R 
O 
R 
VIN Odometer (Counter) 
Predefined Man Specific Data Records 
0103 
Defined 
O 
R 
O 
VIN Odometer (Counter) Limit 
Predefined Man Specific Data Records 
0104 
Defined 
O 
R 
O 
Engine In-use Histogram 
Predefined Man Specific Data Records 
0105 
Defined 
C14 
C14 
O 
(Originally reserved for Network Mirroring Mode Configuration) 
Predefined Man Specific Data Records 
0106 
Reserved 
n/a 
n/a 
n/a 
(Originally reserved for Response on Event - light Activation State) 
Predefined Man Specific Data Records 
0107 
Reserved 
n/a 
n/a 
n/a 
(Originally reserved for Ignition Status Enable Condition Calibration State) 
Predefined Man Specific Data Records 
0108 
Reserved 
n/a 
n/a 
n/a 
Configuration Write Counter 
Predefined Man Specific Data Records 
0109 
Defined 
O 
C23 
O 
Most Diagnostic Routing Table 
Predefined Man Specific Data Records 
010A 
Defined 
C15 
C15 
n/a 
Adjust ISO 15765-2 Block Size and STmin Parameter 
Predefined Man Specific Data Records 
010B 
Defined 
C18 
C18 
C18 
Read Odometer value from Bus 
Predefined Man Specific Data Records 
010C 
Defined 
O 
n/a 
O 
Read Used EVC Config  
Predefined Man Specific Data Records 
010D 
Defined 
C28 
n/a 
n/a 
(Originally reserved for Reprogramming Error Status) 
Predefined Man Specific Data Records 
010E 
Reserved 
n/a 
n/a 
n/a 
LIN Slope Mode 
Predefined Man Specific Data Records 
010F 
Defined 
C38 
n/a 
n/a 
DPM Status Information 
Predefined Man Specific Data Records 
0110 - 011F 
Defined 
C9 
n/a 
C9 
Reserved for future Vehicle Configuration Data Records 
Predefined Man Specific Data Records 
0120  - 0129 
Reserved 
n/a 
n/a 
n/a 
Activate Supplier specific messages 
Predefined Man Specific Data Records 
012A 
Defined 
C39 
n/a 
n/a 
Adjust ISO 10681-2 Bandwidth Control Parameter 
Predefined Man Specific Data Records 
012B 
Defined 
C33 
n/a 
C33 
High Voltage Lock 
Predefined Man Specific Data Records 
012C 
Defined 
C35 
n/a 
C35 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 96 页
A0010023099: 2013-02, page 95 
 
 
Copyright Daimler AG 2013  
Description 
DID type 
DID code 
[hex] 
Status 
MBC_ 
Supp-
Cond 
Trucks_
Supp-
Cond 
VAN_ 
Supp-
Cond 
Engine Style 
Predefined Man Specific Data Records 
012D 
Defined 
C13 
n/a 
C13 
DMR_Timeout 
Predefined Man Specific Data Records 
012E 
Defined 
C13 
n/a 
C13 
High Voltage Value 
Predefined Man Specific Data Records 
012F 
Defined 
C35 
n/a 
n/a 
Activate Partial Networking 
Predefined Man Specific Data Records 
0130 
Defined 
C37 
n/a 
n/a 
Reserved for future Predefined Man Specific Data Records assigned by 
diagnostic development teams. 
Predefined Man Specific Data Records 
0131 - 01FF 
Reserved 
n/a 
n/a 
n/a 
Used to define ECU specific Data Records (Static, Dynamic Data) 
Manufacturer Specific Data Records 
0200 - 1CFF 
Available 
O 
O 
O 
Number of available analog values 
Number of available Onboard analog 
values 
1D00 
Defined 
n/a 
R 
n/a 
Read analog value number xx 
Daimler Trucks ManSpec Data Records 
for analog values 
1D01 - 1DFF 
Available 
n/a 
C21 
n/a 
Number of available discrete value bytes  
Number of available Onboard discrete 
values 
1E00 
Defined 
n/a 
R 
n/a 
Read discrete value number xx 
Daimler Trucks ManSpec Data Records 
for digital values 
1E01 - 1EFF 
Available 
n/a 
C21 
n/a 
Used to define ECU specific Data Records (Static, Dynamic Data) 
Manufacturer Specific Data Records 
1F00 - A5FF 
Available 
O 
O 
O 
Reserved for future ISO/SAE definition 
Reserved ISO/SAE 
A600 - A7FF 
Reserved 
n/a 
n/a 
n/a 
Used to define ECU specific Data Records (Static, Dynamic Data) 
Manufacturer Specific Data Records 
A800 - ACFF 
Available 
O 
O 
O 
Reserved for future ISO/SAE definition 
Reserved ISO/SAE 
AD00 - AFFF 
Reserved 
n/a 
n/a 
n/a 
Used to define ECU specific Data Records (Static, Dynamic Data) 
Manufacturer Specific Data Records 
B000 - B1FF 
Available 
O 
O 
O 
Reserved for future ISO/SAE definition 
Reserved ISO/SAE 
B200 - BFFF 
Reserved 
n/a 
n/a 
n/a 
Used to define ECU specific Data Records (Static, Dynamic Data) 
Manufacturer Specific Data Records 
C000 - C2FF 
Available 
O 
O 
O 
Reserved for future ISO/SAE definition 
Reserved ISO/SAE 
C300 - CEFF 
Reserved 
n/a 
n/a 
n/a 
Used to define ECU specific Data Records (Static, Dynamic Data) 
Manufacturer Specific Data Records 
CF00 - CFFF 
Available 
O 
O 
O 
This range shall be used to define ECU specific Input Output Control Pa-
rameters or Parameter Groups (used with the I/O Control service  [2F hex] 
as well as the Read Data by DID service [22 hex] ) 
Manufacturer Specific I/O Controls 
D000 - DFFF 
Available 
O 
O 
O 
Compatibility List  
Predefined Man Specific Data Records 
E000 
Defined 
n/a 
R 
n/a 
GVC update time stamp 
Predefined Man Specific Data Records 
E001 
Defined 
O 
C27 
O 
Reserved for future Predefined Man Specific Data Records assigned by 
diagnostic development teams 
Predefined Man Specific Data Records 
E002 - EEFF 
Reserved 
n/a 
n/a 
n/a 
SW-Module Identification - Application - AUTOSAR 
Development Data 
EF00 
Defined 
C25 
C25 
C25 
(Originally reserved for  SW-Module Identification - FlashLoader - AU-
TOSAR) 
Development Data 
EF01 
Reserved 
n/a 
n/a 
n/a 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 97 页
A0010023099: 2013-02, page 96 
 
 
Copyright Daimler AG 2013  
Description 
DID type 
DID code 
[hex] 
Status 
MBC_ 
Supp-
Cond 
Trucks_
Supp-
Cond 
VAN_ 
Supp-
Cond 
Standard FBL SW Package information 
Development Data 
EF02 
Defined 
C30 
C30 
C30 
Standard SW Package information 
Development Data 
EF03 
Defined 
C2 
C2 
C2 
(Originally reserved for  Read VMM Version) 
Development Data 
EF04 
Reserved 
n/a 
n/a 
n/a 
Reserved for future Development Data assigned by diagnostic develop-
ment teams. 
Development Data 
EF05 - EFFF 
Reserved 
n/a 
n/a 
n/a 
Reserved for Network Configuration Data For Tractor Trailer Application 
Reserved ISO/SAE 
F000 - F00F 
Reserved 
n/a 
n/a 
n/a 
Network Configuration - CAN Cabin Network 
Network Configuration Data 
F010 
Defined 
C4 
n/a 
C4 
Network Configuration - CAN Powertrain/Chassis Network 
Network Configuration Data 
F011 
Defined 
C4 
n/a 
C4 
Network Configuration - LIN Network 
Network Configuration Data 
F012 
Defined 
C4 
n/a 
C4 
Network Configuration - MOST Network 
Network Configuration Data 
F013 
Defined 
C4 
n/a 
C4 
Network Configuration - FlexRay Network 
Network Configuration Data 
F014 
Defined 
C4 
n/a 
C4 
Reserved for future Network Configuration 
Network Configuration Data 
F015 - F07F 
Reserved 
n/a 
n/a 
n/a 
Reserved for future Network Configuration 
Network Configuration Data 
F080 - F0FF 
Reserved 
n/a 
n/a 
n/a 
Active Diagnostic Information 
ECU Identification 
F100 
Defined 
R 
R 
R 
FINAS Number 
ECU Identification 
F101 
Defined 
O 
O 
O 
Technical Time Stamp (TTZ - Termintechnischer Zustand) 
ECU Identification 
F102 
Defined 
O 
n/a 
C13 
FDOK Safety Relevant Information  
ECU Identification 
F103 
Defined 
O 
n/a 
O 
Reserved for future ECU Identification Data Records 
ECU Identification 
F104 - F109 
Reserved 
n/a 
n/a 
n/a 
(Originally reserved for ECU Origin) 
ECU Identification 
F10A 
Reserved 
n/a 
n/a 
n/a 
ECU Configuration 
ECU Identification 
F10B 
Defined 
C26 
n/a 
C26 
(Originally reserved for MMC diagnostic variant information) 
ECU Identification 
F10C 
Reserved 
n/a 
n/a 
n/a 
Diagnostic Specification Information 
ECU Identification 
F10D 
Defined 
R 
R 
O 
FlexRay Configuration Information 
ECU Identification 
F10E 
Defined 
C33 
n/a 
C33 
OSEK - Module Information 
Development Data 
F10F 
Defined 
n/a 
C2 
n/a 
(Originally reserved for Common ECU Identification - Hardware Part Num-
ber) 
ECU Identification 
F110 
Reserved 
n/a 
n/a 
n/a 
MBC ECU Identification - Hardware Part Number 
ECU Identification 
F111 
Defined 
R 
n/a 
R 
(Originally reserved for DCA ECU Identification - Hardware Part Number ) 
ECU Identification 
F112 
Reserved 
n/a 
n/a 
n/a 
Reserved for future ECU Identification Data Records 
ECU Identification 
F113 
Reserved 
n/a 
n/a 
n/a 
(Originally reserved for MMC hardware part number)F 
ECU Identification 
F114 
Reserved 
n/a 
n/a 
n/a 
SMART ECU Identification - Hardware Part Number 
ECU Identification 
F115 
Defined 
n/a 
n/a 
n/a 
MBTruck ECU Identification - Hardware Part Number 
ECU Identification 
F116 
Defined 
n/a 
R 
n/a 
Freightliner ECU Identification - Hardware Part Number 
ECU Identification 
F117 
Defined 
n/a 
R 
n/a 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 98 页
A0010023099: 2013-02, page 97 
 
 
Copyright Daimler AG 2013  
Description 
DID type 
DID code 
[hex] 
Status 
MBC_ 
Supp-
Cond 
Trucks_
Supp-
Cond 
VAN_ 
Supp-
Cond 
MFTBC ECU Identification - Hardware Part Number 
ECU Identification 
F118 
Defined 
n/a 
R 
n/a 
(Originally reserved for DDC hardware part number)n 
ECU Identification 
F119 
Reserved 
n/a 
n/a 
n/a 
Reserved for future part number assignment 
ECU Identification 
F11A - F11F 
Reserved 
n/a 
n/a 
n/a 
(Originally reserved for Common ECU Identification - Hardware Part Num-
ber) 
ECU Identification 
F120 
Reserved 
n/a 
n/a 
n/a 
MBC ECU Identification - Software Logical Block Part Number(s) 
ECU Identification 
F121 
Defined 
C3 
n/a 
C3 
(Originally reserved for DCA ECU Identification - Software Logical Block 
Part Number(s)) 
ECU Identification 
F122 
Reserved 
n/a 
n/a 
n/a 
Reserved for future ECU Identification Data Records 
ECU Identification 
F123 
Reserved 
n/a 
n/a 
n/a 
(Originally reserved for MMC Software Logical Block Part Number(s)) 
ECU Identification 
F124 
Reserved 
n/a 
n/a 
n/a 
SMART ECU Identification - Software Logical Block Part Number(s) 
ECU Identification 
F125 
Defined 
n/a 
n/a 
n/a 
MBTruck ECU Identification - Software Logical Block Part Number(s) 
ECU Identification 
F126 
Defined 
n/a 
C3 
n/a 
Freightliner ECU Identification - Software Logical Block Part Number(s) 
ECU Identification 
F127 
Defined 
n/a 
C3 
n/a 
MFTBC ECU Identification - Software Logical Block Part Number(s) 
ECU Identification 
F128 
Defined 
n/a 
C3 
n/a 
(Originally reserved for DDC software part number) 
ECU Identification 
F129 
Reserved 
n/a 
n/a 
n/a 
Reserved for future ECU Identification Data Records 
ECU Identification 
F12A - F12F 
Reserved 
n/a 
n/a 
n/a 
(Originally reserved for Common ECU Identification - ECU Part Number) 
ECU Identification 
F130 
Reserved 
n/a 
n/a 
n/a 
Reserved for future ECU Identification Data Records 
ECU Identification 
F131 
Reserved 
n/a 
n/a 
n/a 
(Originally reserved for DCA ECU Identification - ECU Part Number 
ECU Identification 
F132 
Reserved 
n/a 
n/a 
n/a 
Reserved for future ECU Identification Data Records 
ECU Identification 
F133 - F14F 
Reserved 
n/a 
n/a 
n/a 
Hardware Version Information 
ECU Identification 
F150 
Defined 
R 
R 
R 
Software Version Information 
ECU Identification 
F151 
Defined 
R 
R 
R 
Reserved for future ECU Identification Data Records 
ECU Identification 
F152 
Reserved 
n/a 
n/a 
n/a 
Boot Software Version Information 
ECU Identification 
F153 
Defined 
C3 
C3 
C3 
Hardware Supplier Identification 
ECU Identification 
F154 
Defined 
R 
R 
R 
Software Supplier Identification 
ECU Identification 
F155 
Defined 
R 
R 
R 
(Originally reserved for Layered Network Information) 
ECU Identification 
F156 
Reserved 
n/a 
n/a 
n/a 
Commercial Vehicle Engine Number 
ECU Identification 
F157 
Defined 
n/a 
C16 
n/a 
(Originally reserved for Vehicle Information) 
ECU Identification 
F158 
Reserved 
n/a 
n/a 
n/a 
ECU Model Type 
ECU Identification 
F159 
Defined 
C26 
C26 
n/a 
Write Software Fingerprint 
ECU Identification 
F15A 
Defined 
C18 
C18 
C18 
Read Software Fingerprint(s) 
ECU Identification 
F15B 
Defined 
C3 
C3 
C3 
Write Configuration Fingerprint 
ECU Identification 
F15C 
Defined 
n/a 
C23 
n/a 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 99 页
A0010023099: 2013-02, page 98 
 
 
Copyright Daimler AG 2013  
Description 
DID type 
DID code 
[hex] 
Status 
MBC_ 
Supp-
Cond 
Trucks_
Supp-
Cond 
VAN_ 
Supp-
Cond 
Read Configuration Fingerprint(s) 
ECU Identification 
F15D 
Defined 
n/a 
C23 
n/a 
Write Routine I/O Fingerprint 
ECU Identification 
F15E 
Defined 
n/a 
C5 
n/a 
Read Routine I/O Fingerprint 
ECU Identification 
F15F 
Defined 
n/a 
C5 
n/a 
SW Module Information  
Development Data 
F160 - F16F 
Defined 
R 
R 
n/a 
Physical Layer Channel Configuration 
Development Data 
F170 - F17F 
Defined 
n/a 
R 
n/a 
Reserved for future ISO/SAE definition 
Reserved ISO/SAE 
F180 - F18B 
Reserved 
n/a 
n/a 
n/a 
ECU Serial Number 
ECU Identification - ISO 
F18C 
Defined 
R 
R 
R 
Reserved for future ISO/SAE definition 
Reserved ISO/SAE 
F18D - F18F 
Reserved 
n/a 
n/a 
n/a 
VIN Original 
ECU Identification 
F190 
Defined 
O 
R 
O 
Reserved for future ISO/SAE definition 
Reserved ISO/SAE 
F191 
Reserved 
n/a 
n/a 
n/a 
System Supplier ECU Hardware Part Number 
System Supplier Specific Identification 
F192 
Defined 
n/a 
R 
n/a 
System Supplier ECU Hardware Version Number 
System Supplier Specific Identification 
F193 
Defined 
n/a 
R 
n/a 
System Supplier ECU Software Part Number 
System Supplier Specific Identification 
F194 
Defined 
n/a 
R 
n/a 
System Supplier ECU Software Version Number 
System Supplier Specific Identification 
F195 
Defined 
n/a 
R 
n/a 
Exhaust Regulation or Type Approval Number (EROTAN) 
System Supplier Specific Identification 
F196 
Defined 
C6 
C6 
C6 
Reserved for future ISO/SAE definition 
Reserved ISO/SAE 
F197 - F19F 
Reserved 
n/a 
n/a 
n/a 
VIN Current 
ECU Identification 
F1A0 
Defined 
O 
R 
O 
Diagnostic Specification Version Information - Gateway Diagnostic Re-
quirements 
ECU Identification 
F1A1 
Defined 
C20 
C20 
O 
Diagnostic Specification Version Information - Standardized Diagnostic 
Data 
ECU Identification 
F1A2 
Defined 
R 
R 
O 
Diagnostic Specification Version Information - Response on Event - lite 
ECU Identification 
F1A3 
Defined 
R 
n/a 
O 
Diagnostic Specification Version Information - LIN 2.1 Diagnostic Re-
quirements 
ECU Identification 
F1A4 
Defined 
C20 
n/a 
O 
Diagnostic Specification Version Information - Diagnostics on FlexRay 
ECU Identification 
F1A5 
Defined 
C20 
n/a 
n/a 
FlexRay Node Information 
ECU Identification 
F1A6 
Defined 
C33 
n/a 
n/a 
Reserved for future Diagnostic Specification Version Information 
ECU Identification 
F1A7 - F1AF 
Reserved 
n/a 
n/a 
n/a 
Reserved for future ECU Identification Data Records 
ECU Identification 
F1B0 - F1EF 
Reserved 
n/a 
n/a 
n/a 
Used by supplier to define Supplier specific Identification Data Records 
System Supplier Specific Identification 
F1F0 - F1FF 
Available 
O 
O 
O 
Reserved for future ECU Identification Data Records 
Reserved ISO/SAE 
F200 - F2FF 
Reserved 
n/a 
n/a 
n/a 
Used to define ECU specific Dynamically Defined Data Records 
Manufacturer Specific Data Records 
F300 - F3FF 
Available 
O 
O 
O 
OBD PID     (definition see  ISO 15031-5) 
Reserved ISO/SAE 
F400 - F4FF 
Defined 
C17 
n/a 
n/a 
OBD PID - Future Use 
Reserved ISO/SAE 
F500 - F5FF 
Reserved 
n/a 
n/a 
n/a 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 100 页
A0010023099: 2013-02, page 99 
 
 
Copyright Daimler AG 2013  
Description 
DID type 
DID code 
[hex] 
Status 
MBC_ 
Supp-
Cond 
Trucks_
Supp-
Cond 
VAN_ 
Supp-
Cond 
OBD Monitor ID 
Reserved ISO/SAE 
F600 - F6FF 
Reserved 
n/a 
n/a 
n/a 
OBD Monitor DID - Future Use 
Reserved ISO/SAE 
F700 - F7FF 
Reserved 
n/a 
n/a 
n/a 
Reserved for  OBD Info Type Data DID 
Reserved ISO/SAE 
F800 - F803 
Reserved 
n/a 
n/a 
n/a 
Calibration Identifications (CAL ID) 
Reserved ISO/SAE 
F804 
Defined 
C17 
C17 
C17 
Reserved for OBD Info Type Data DID 
Reserved ISO/SAE 
F805 
Reserved 
n/a 
n/a 
n/a 
Calibration Verification Numbers (CVN) 
Reserved ISO/SAE 
F806 
Defined 
C17 
C17 
C17 
RBM HEX Gasoline   
Reserved ISO/SAE 
F808 
Reserved 
C17 
n/a 
n/a 
Reserved for OBD Info Type Data DID 
Reserved ISO/SAE 
F809 - F80A 
Reserved 
n/a 
n/a 
n/a 
RBM HEX Diesel 
Reserved ISO/SAE 
F80B 
Reserved 
C17 
n/a 
n/a 
Reserved for OBD Info Type Data DID 
Reserved ISO/SAE 
F80C - F8FF 
Reserved 
n/a 
n/a 
n/a 
These DID shall be used according to ISO 16844-6 (Tachograph Systems) 
Reserved ISO/SAE 
F900 - F9FF 
Reserved 
n/a 
C22 
n/a 
These DID shall be used according to ISO/WD 26021 (Airbag Deployment) Reserved ISO/SAE 
FA00 - FA0F 
Reserved 
C22 
C22 
n/a 
Safety System DID 
Reserved ISO/SAE 
FA10 - FAFF 
Reserved 
n/a 
n/a 
n/a 
Reserved for future legislative use 
Reserved ISO/SAE 
FB00 - FCFF 
Reserved 
n/a 
n/a 
n/a 
Used by the supplier to define System Supplier specific Data Records. 
System Supplier Specific Identification 
FD00 - FEFF 
Available 
O 
O 
O 
Reserved for future ISO/SAE definition 
Reserved ISO/SAE 
FF00 - FFFF 
Reserved 
n/a 
n/a 
n/a 
 
 
8.2 
Overview - Routine Identifiers 
This section defines the applicable Data Identifier ranges allocated for the respective use case. The development engineer and the supplier shall adhere to the 
definitions given in Table 90 to ensure diagnostic uniformity and ISO conformity. Descriptions of the support conditions are defined in Table 91 
 
Table 90: Routine Identifier Overview 
 
Description 
RID type 
RID Code 
[hex] 
Status 
MBC_ 
Supp-
Cond 
Trucks_
Supp-
Cond 
VAN_ 
Supp-
Cond 
Reserved for future ISO/SAE definition 
Reserved ISO/SAE 
0000 - 00FF 
Reserved 
n/a 
n/a 
n/a 
Reserved for future Tachograph Test Ids 
Reserved for Tachograph Test IDs 
0100 - 01FF 
Reserved 
n/a 
n/a 
n/a 
LIN Slave Node Data Routing 
Predefined Man Specific Routines 
0200 
Reserved 
n/a 
C10 
n/a 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 101 页
A0010023099: 2013-02, page 100 
 
 
Copyright Daimler AG 2013  
Description 
RID type 
RID Code 
[hex] 
Status 
MBC_ 
Supp-
Cond 
Trucks_
Supp-
Cond 
VAN_ 
Supp-
Cond 
EVC Protected Reset  
Predefined Man Specific Routines 
0201 
Defined 
C28 
n/a 
n/a 
Reserved for future Predefined Man Specific Routine IDs assigned by diag-
nostic development teams 
Predefined Man Specific Routines 
0202 - 020F 
Reserved 
n/a 
n/a 
n/a 
Clear Diagnostic Information - Not Tested DTC Stack 
Predefined Man Specific Routines 
0210 
Reserved 
n/a 
O 
n/a 
Report DTC Information from Not Tested DTC Stack 
Predefined Man Specific Routines 
0211 
Reserved 
n/a 
O 
n/a 
Reset VIN Values 
Predefined Man Specific Routines 
0212 
Defined 
O 
R 
O 
(Originally reserved for Set Fault Status ) 
Predefined Man Specific Routines 
0213 
Reserved 
n/a 
n/a 
n/a 
(Originally reserved for Request Echo Routine) 
Predefined Man Specific Routines 
0214 
Reserved 
n/a 
n/a 
n/a 
(Originally reserved for Control Network Mirroring) 
Predefined Man Specific Routines 
0215 
Reserved 
n/a 
n/a 
n/a 
Fast Routing Mode 
Predefined Man Specific Routines 
0216 
Reserved 
n/a 
C12 
n/a 
(Originally reserved for  Configure DTC Suppression Flag)   
Predefined Man Specific Routines 
0217 
Reserved 
n/a 
n/a 
n/a 
Reserved for future Predefined Man Specific Routine IDs assigned by diag-
nostic development teams. 
Predefined Man Specific Routines 
021A - 021F 
Reserved 
n/a 
n/a 
n/a 
Switch Test Mode 
Predefined Man Specific Routines 
0218 
Defined 
C32 
O 
C32 
ECU self-test  
Predefined Man Specific Routines 
0219 
Defined 
O 
O 
O 
Reserved for future Daimler Trucks Specific Routine IDs. 
Daimler Trucks Specific Routines 
0220 - 0222 
Reserved 
n/a 
n/a 
n/a 
Reload Original Supplier Configuration(see definition in Model line specific 
diagnostic specification for S3P/P3/F2T2)  
Daimler Trucks Specific Routines 
0223 
Defined 
n/a 
C23 
n/a 
Reload Supplier Delivery State (see definition in Model line specific diag-
nostic specification for S3P/P3/F2T2) 
Daimler Trucks Specific Routines 
0224 
Defined 
n/a 
R 
n/a 
Reserved for future Daimler Trucks Specific Routine IDs. 
Daimler Trucks Specific Routines 
0225 - 0227 
Reserved 
n/a 
n/a 
n/a 
Auto Configuration (see definition in Model line specific diagnostic specifi-
cation for S3P/P3/F2T2) 
Daimler Trucks Specific Routines 
0228 
Defined 
n/a 
O 
n/a 
Activate sensitized mature criteria (see definition in Model line specific 
diagnostic specification for S3P/P3/F2T2) 
Daimler Trucks Specific Routines 
0229 
Defined 
n/a 
C24 
n/a 
Calculate Checksum for configuration data  (see definition in Model line 
specific diagnostic specification for S3P/P3/F2T2) 
Daimler Trucks Specific Routines 
022A 
Defined 
n/a 
C23 
n/a 
Deactivate Limp home functionality (see definition in Model line specific 
diagnostic specification for S3P/P3/F2T2) 
Daimler Trucks Specific Routines 
022B 
Defined 
n/a 
R 
n/a 
Reserved for future Daimler Trucks Specific Routine IDs. 
Daimler Trucks Specific Routines 
022C - 022F 
Reserved 
n/a 
n/a 
n/a 
FBS4 Personalization 
Predefined Man Specific Routines 
0230 
Defined 
C34 
n/a 
C34 
FBS4 Activation 
Predefined Man Specific Routines 
0231 
Defined 
C34 
n/a 
C34 
FBS4 Data00 
Predefined Man Specific Routines 
0232 
Defined 
C34 
n/a 
C34 
FBS4 Data01 
Predefined Man Specific Routines 
0233 
Defined 
C34 
n/a 
C34 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 102 页
A0010023099: 2013-02, page 101 
 
 
Copyright Daimler AG 2013  
Description 
RID type 
RID Code 
[hex] 
Status 
MBC_ 
Supp-
Cond 
Trucks_
Supp-
Cond 
VAN_ 
Supp-
Cond 
FBS4 Data03 
Predefined Man Specific Routines 
0234 
Defined 
C34 
n/a 
C34 
FBS4 Data04 
Predefined Man Specific Routines 
0235 
Defined 
C34 
n/a 
C34 
FBS4 Data05 
Predefined Man Specific Routines 
0236 
Defined 
C34 
n/a 
C34 
FBS4 Data06 
Predefined Man Specific Routines 
0237 
Defined 
C34 
n/a 
C34 
FBS4 Data07 
Predefined Man Specific Routines 
0238 
Defined 
C34 
n/a 
C34 
FBS4 DataD0 
Predefined Man Specific Routines 
0239 
Defined 
C34 
n/a 
C34 
Resreved for  FBS4 specific routines 
Predefined Man Specific Routines 
023A - 023F 
Reserved 
C34 
n/a 
C34 
Reserved for future Predefined Man Specific Routine IDs assigned  by 
diagnostic development teams. 
Predefined Man Specific Routines 
0240 - 02FF 
Reserved 
n/a 
n/a 
n/a 
Reserved for future Predefined Man Specific Routine IDs assigned  by 
diagnostic development teams. 
Predefined Man Specific Routines 
0241 - 02FF 
Reserved 
n/a 
n/a 
n/a 
This range shall be used to define ECU specific Routines. 
Manufacturer Specific Routines 
0300 - DFFF 
Available 
O 
O 
O 
OBD Test ID 
Reserved ISO/SAE 
E000 - E1FF 
Reserved 
n/a 
n/a 
n/a 
DeployLoopRoutine  (This RID shall be used according to ISO/WD 26021) 
Reserved ISO/SAE 
E200 
Reserved 
C22 
C22 
n/a 
Safety System Specificn 
Reserved ISO/SAE 
E201 - E2FF 
Reserved 
n/a 
n/a 
n/a 
Reserved for future ISO/SAE definition 
Reserved ISO/SAE 
E300 - EFFF 
Reserved 
n/a 
n/a 
n/a 
This range may be used by the supplier to define System Supplier specific 
Routines. 
System Supplier Specific Routines 
F000 - FEFF 
Available 
O 
O 
O 
Erase Memory 
ECU Reprogramming 
FF00 
Defined 
C18 
C18 
C18 
Check Routine 
ECU Reprogramming 
FF01 
Defined 
C18 
C18 
C18 
Erase Mirror Memory DTCs 
ECU Reprogramming 
FF02 
Defined 
C31 
R 
C31 
Check Programming Preconditions 
ECU Reprogramming 
FF03 
Defined 
n/a 
O 
n/a 
CheckMemory 
ECU Reprogramming 
FF04 
Defined 
C18 
C18 
C18 
Control Fail Safe Reactions 
ECU Reprogramming 
FF05 
Defined 
C36 
n/a 
O 
CheckCompatibilityDependencies 
ECU Reprogramming 
FF06 
Defined 
C3 
n/a 
C3 
Reserved for future ISO/SAE definition 
Reserved ISO/SAE 
FF07 - FFFF 
Reserved 
n/a 
n/a 
n/a 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 103 页
A0010023099: 2013-02, page 102 
 
 
 
A0010023099: 2013-02, page 102 
 
Table 91: DID and RID Support Conditions 
 
Condition Description 
R 
Required 
O 
Optional 
n/a 
Not Applicable 
C1 
Shall be implemented when RoE-lite is implemented. 
C2 
Shall be implemented if ECU supports the Daimler standard software stack. 
C3 
Shall be implemented for flash reprogrammable ECUs only. 
C4 
Shall be implemented for gateway ECUs only. 
C5 
Shall be implemented when specific routine identifier or I/O control identifier shall be protected 
through Security Access and Fingerprint 
C6 
Shall be implemented for emissions-related ECUs certified for E-OBD. 
C7 
Shall be implemented if ECU is intended for use at MBC. 
C8 
Shall be implemented if Daimler standard software boot loader is used. 
C9 
Shall be implemented if Decentralized Power Management is implemented. 
C10 
Shall be implemented if ECU is the LIN master-node based on specification LIN 2.0 
C11 
Shall be implemented for CAN to CAN or CAN to Diag CAN Gateway ECUs only. 
C12 
Shall be implemented for Vehicle Gateway ECUs operating polling mode routing. 
C13 
Shall be implemented for Main vehicle gateways only. 
C14 
Shall be implemented for Engine Control Modules only. 
C15 
Shall be implemented for MOST gateways only. 
C16 
Shall be implemented for Daimler Trucks Engine controller only. 
C17 
Shall be implemented for emissions-related ECUs certified for OBD II. 
C18 
Shall be implemented in boot loader only. 
C19 
Shall be implemented when ECU supports the standard software module FRFM. 
C20 
Shall be implemented when  the associated Diagnostic Specification is supported by an ECU. 
C21 
Shall be implemented when ECU supports values for On-Board display puspose. 
C22 
Shall be implemented when required by legislation. 
C23 
Shall be implemented when ECU is configurable (changing parameters). 
C24 
Shall be implemented when ECU supports very slow DTC maturing criteria. 
C25 
Shall be implemented if ECU supports the  AUTOSAR standard software stack. 
C26 
Shall be implemented if ECU SW supports multiple different HW variant 
C27 
Shall be implemented if ECU supports Global Variant Coding 
C28 
Shall be implemented if ECU supports enhanced variant coding according to EVC Specification 
A000 006 95 99  
C29 
Shall be implemented for flash reprogrammable ECUs providing flash security class CCC (Asym-
metric Signature) only 
C30 
Shall be implemented if ECU supports the Daimler Standard Software Flash Boot Loader 
C31 
Shall be implemented if ECU supports a mirror memory for DTCs (Historical Stack) 
C32 
Shall be implemented if ECU is equiped with switches or buttons or has hardwired switches or 
buttons 
C33 
Shall be implemented if ECU is connected to a FlexRay bus 
C34 
Shall be implemented if ECU is a part of the drive authorization system FBS4 
C35 
Shall be implemented if ECU is a high voltage powertrain component which is able to supply or 
generate high voltage 
C36 
Shall be implemented if ECU has implemented fail safe reactions in case of not receiving messages 
containing signals necessary for proper ECU behaviour  
C37 
Shall be implemented if ECU supports partial networking 
C38 
Shall be implemented if ECU is connected to a LIN network (master or slave) 
C39 
Shall be implemented if supplier specific messages as defined in the VMM are assigned to the ECU 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 104 页
A0010023099: 2013-02, page 103 
 
 
Copyright Daimler AG 2013  
9 
Vehicle Line Identification (removed) 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 105 页
A0010023099: 2013-02, page 104 
 
 
Copyright Daimler AG 2013  
10 Physical Layer Channel Configuration Encoding 
 
Table 92: Physical Layer Channel Configuration Encoding 
 
Data Element 
Data 
Value 
[hex] 
CAN 
LIN 
FlexRay 
Transceiver Type 
 
00 
Unknown 
Unknown 
Unknown 
01 
NXP TJA 1040 
Elmos VR 910 18 
NXP TJA 1080 
02 
NXP TJA 1041A 
Elmos 910.45 
NXP TJA 1080A 
03 
Freescale MC 33742 
Infineon TLE6258 
Reserved 
04 
NXP TJA 1054 
Melexis TH 8080 
Reserved 
05 
Infineon TLE 6251 G 
Melexis TH 8082 
Reserved 
06 
Infineon TLE 6251DS 
Freescale MC33689 
Reserved 
07 
Bosch CY320 
Freescale MM908E624 
Reserved 
08 
AMIS 42665AGA 
NXP TJA1020 
Reserved 
09 
Infineon TLE 7263E 
ST L9638 
Reserved 
0A 
NXP UJA1065 
ST L9952 
Reserved 
0B 
TI SN65HVD1040 
NXP UJA1069 
Reserved 
0C 
NXP TJA1055 
NXP UJA1065  
Reserved 
0D 
Infineon TLE6251-2G 
NXP TJA1021 
Reserved 
0E 
Reserved 
Infineon TLE7259G 
Reserved 
0F 
Reserved 
Infineon TLE 7263E 
Reserved 
10 
Reserved 
Bosch BGWA-ASIC 
Reserved 
11 
Reserved 
Atmel ATA6662 
Reserved 
12 
Reserved 
Atmel ATA6624 
Reserved 
13 
Reserved 
Atmel ATA6625 
Reserved 
14 
Reserved 
Infineon TLE 7269G 
Reserved 
15 
Reserved 
Infineon TLE 7259-2GE 
Reserved 
16 
Reserved 
Reserved 
Reserved 
17 
Reserved 
Elmos 910.48A 
Reserved 
18 
Reserved 
Atmel ATA6622 
Reserved 
Common Mode Choke  
 
00 
Unknown 
Unknown 
Unknown 
01 
Epcos B82790-S0513-
N201 
n/a 
Reserved 
02 
Epcos B82793-S0513-
N201 
n/a 
Reserved 
03 
Epcos B82799-S0333-N1 
n/a 
Reserved 
04 
Epcos B82789-S0223-N1 
n/a 
Reserved 
05 
Epcos B82789-S0223-N2 
n/a 
Reserved 
06 
Vogt DK2x50µH/0,5A 
n/a 
Reserved 
07 
Valor ST2001 
n/a 
Reserved 
08 
Bourns DR 332-513 
n/a 
Reserved 
09 
TDK ZJYS81R5-2PL51T-G 
n/a 
Reserved 
0A 
TDK ACT4532-102-2PT 
n/a 
Reserved 
0B 
Magnetron TF0513S 
n/a 
Reserved 
0C 
Pulse TX8111T 
n/a 
Reserved 
0D 
Pulse M3001 
n/a 
Reserved 
0E 
TTelectronics HM67-
10510TR 
n/a 
Reserved 
0F 
TDK ACT45S-220-2P 
n/a 
Reserved 
10 
Epcos B82789-S0223-H 
n/a 
Reserved 
11 
Bourns DR 331-513 
n/a 
Reserved 
Termination Resistor Value 
 
XX 
MBS 
$00 
MSB (default 
$00) 
XX 
LSB 
$00 
LSB (default 
$00) 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 106 页
A0010023099: 2013-02, page 105 
 
 
Copyright Daimler AG 2013  
Data Element 
Data 
Value 
[hex] 
CAN 
LIN 
FlexRay 
Baud Rate 0 
 
XX 
Register 0 (MSB) 
MSB 
MSB (default 
$00) 
XX 
Register 0 (LSB) 
LSB 
LSB (default 
$00) 
Baud Rate 1 
 
XX 
Register 1 (MSB) 
$00 
MSB (default 
$00) 
XX 
Register 1 (LSB) 
$00 
LSB (default 
$00) 
Channel Oscillator Fre-
quency 
 
XX 
MSB 
MSB 
MSB (default 
$00) 
XX 
LSB 
LSB 
LSB (default 
$00) 
Channel Oscillator Type 
 
00 
Unknown 
Unknown 
Unknown 
01 
Crystal 
Crystal 
Crystal 
02 
Resonator 
Resonator 
Resonator 
Channel Type 
 
00 
Undefined 
Undefined 
Undefined 
01 
CAN 
CAN 
CAN 
02 
LIN 
LIN 
LIN 
03 
Most 
Most 
Most 
04 
FlexRay 
FlexRay 
FlexRay 
 
Table 93: Network ID hardware 
 
Network ID 
Mercedes-Benz Cars 
Daimler Trucks 
00 
Unknown 
Unknown 
01 
Reserved 
DIAGNOSTIC_CAN 
02 
Reserved 
CABIN_CAN 
03 
Reserved 
CHASSIS_CAN 
04 
CAN-BACKBONE 
EXTERIOR_CAN 
05 
CAN-DIAGNOSTICS 
J1939_CAN 
06 
CAN-BODY 
ASSISTANCE_CAN 
07 
CAN-CHASSIS 
POWERTRAIN_CAN 
08 
CAN-POWERTRAIN 
TELEMATIC_CAN 
09 
CAN-PT-SENSOR 
HVAC_CAN 
0A 
Reserved 
Reserved 
0B 
CAN-DYNAMICS 
Reserved 
0C 
Reserved 
Reserved 
0D 
Reserved 
SENS_ACT_CAN1 
0E 
CAN-HEADUNIT 
SENS_ACT_CAN2 
0F 
CAN-IMPACT 
Reserved 
10 
CAN-MULTIPURPOSE 
Reserved 
11 
LIN-PT_ACTUATOR 
Reserved 
12 
LIN-DOOR_FRONT 
Reserved 
13 
LIN-STEERING_WHEEL 
Reserved 
14 
LIN-WIPER_ROOF 
IES-CAN 
15 
LIN-VTA 
Reserved 
16 
LIN-HVAC 
Reserved 
17 
LIN-IBS 
Reserved 
18 
LIN-DASHBOARD 
Reserved 
19 
LIN-DOOR_REAR 
Reserved 
1A 
LIN-HEADLAMP 
Reserved 
1B 
LIN-MCMS (Multi cushion memory seat) 
Reserved 
1C 
LIN-OAS (Optional Accessory Switches LIN) 
Reserved 
1D 
LIN-ORC 
Reserved 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 107 页
A0010023099: 2013-02, page 106 
 
 
Copyright Daimler AG 2013  
Network ID 
Mercedes-Benz Cars 
Daimler Trucks 
1E 
Reserved 
Reserved 
1F 
Reserved 
Reserved 
20 
LIN-DOOR FRONT DRIVER 
Reserved 
21 
LIN-DOOR FRONT PASSENGER 
Reserved 
22 
Reserved 
Reserved 
23 
Reserved 
Reserved 
24 
Reserved 
Reserved 
25 
LIN- SEAT DRIVER 
Reserved 
26 
LIN- SEAT PASSENGER 
Reserved 
27 
Reserved 
Reserved 
28 
Reserved 
Reserved 
29 
LIN- HEADLAMP FRONT LEFT 
Reserved 
2A 
HYBRID CAN 
Reserved 
2B 
Reserved 
Reserved 
2C 
Reserved 
Reserved 
2D 
Reserved 
Reserved 
2E 
Reserved 
Reserved 
2F 
Reserved 
Reserved 
30 
LIN- HEADLAMP FRONT RIGHT 
Reserved 
31 
CAN-CHASSIS 1 
Reserved 
32 
CAN-CHASSIS 2 
SCA_LIN 
33 
FlexRay-CHASSIS 
MSF_LIN 
34 
ENGINE CAN 
ICUC_LIN 
35 
Reserved 
DCMD_LIN 
36 
Reserved 
CPC_LIN 
37 
Reserved 
CPC_LIN2 
38 
SCA_LIN2 
Reserved 
39 
Reserved 
SSAM_LIN 
3A 
Reserved 
SSAM_LIN2 
3B - FF 
Reserved 
Reserved 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 108 页
A0010023099: 2013-02, page 107 
 
 
Copyright Daimler AG 2013  
11 Supplier Identification Table 
If a specific supplier code is not available, please contact Diagnostic Department. 
Table 94 specifies the state encoded values for the supplier information to be provided for the ECU. 
 
Table 94: Supplier Identification 
 
Supplier ID 
[hex] 
Supplier name 
0000 
Reserved 
0001 
Becker 
0002 
Blaupunkt 
0003 
Bosch 
0004 
MB 
0005 
HuF 
0006 
Kammerer 
0007 
Kostal 
0008 
Siemens 
0009 
Stribel 
000A 
MicroHeat 
000B 
JATCO 
000C 
Cummins 
000D 
ZF Lenksysteme 
000E 
Nidec Motors & Actuators 
000F 
S&T Daewoo (Science & Technology 
Daewoo) 
0010 
SWF 
0011 
VDO 
0012 
Webasto 
0013 
Dornier 
0014 
TEG 
0015 
Hella 
0016 
Lucas 
0017 
GKR 
0018 
MBB 
0019 
Motometer 
001A 
Daimler 
001B 
Sanden 
001C 
IEE 
001D 
ASK 
001E 
U-Shin 
001F 
Volkswagen 
0020 
Borg 
0021 
Temic 
0022 
Teves 
0023 
BorgWarner 
0024 
MED S.P.A 
0025 
DENSO 
0026 
ZF 
0027 
TRW 
0028 
Dunlop 
0029 
LuK 
002A 
Hyundai Autonet 
002B 
Freightliner 
002C 
TAKATA-PETRI 
002D 
Haldes 
Supplier ID 
[hex] 
Supplier name 
002E 
Hirschmann 
002F 
e2v Technology 
0030 
Magneti Marelli 
0031 
DODUCO 
0032 
Alpine 
0033 
AMC (AEG Mobile COM.) 
0034 
Bose 
0035 
DASA 
0036 
Motorola 
0037 
Nokia 
0038 
Panasonic 
0039 
APAG 
003A 
Rialtosoft 
003B 
Applicom 
003C 
Conti Temic 
003D 
Cherry 
003E 
TI Automotive 
003F 
Kongsberg Automotive 
0040 
Delphi 
0041 
Alfmeier 
0042 
Sidler 
0043 
Marquardt 
0044 
Wehrle 
0045 
megamos 
0046 
ADC 
0047 
BERU 
0048 
Valeo 
0049 
Magna 
004A 
Allison 
004B 
Isringshausen 
004C 
Grammer 
004D 
Funkwerk Dabendorf 
004E 
Hella-Behr 
004F 
Pollak 
0050 
AKG 
0051 
Automotive Lighting 
0052 
TAG 
0053 
UNITED PARTS 
0054 
catem 
0055 
Alge 
0056 
Pierburg 
0057 
Brusa 
0058 
Ecostar 
0059 
NuCellSys 
005A 
Wabco Automotive 
005B 
Voith 
005C 
Knorr 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 109 页
A0010023099: 2013-02, page 108 
 
 
Copyright Daimler AG 2013  
Supplier ID 
[hex] 
Supplier name 
005D 
TVI 
005E 
Stoneridge 
005F 
Telma 
0060 
STW 
0061 
Koyo 
0062 
Eberspächer 
0063 
ADVICS 
0064 
OMRON 
0065 
Mitsubishi Heavy Industry 
0066 
Methode 
0067 
UNISIAJECS 
0068 
UNISIAJKC Steering Systems 
0069 
AISIN 
006A 
Zexel Valeo 
006B 
Schrader 
006C 
Ballard 
006D 
Alcoa Fujikura 
006E 
Transtron 
006F 
SFT 
0071 
Kieckert AG 
0072 
Behr 
0073 
MB Lenkugnen 
0074 
Sachs Automotive 
0075 
Peiker 
0076 
Petri 
0077 
Autoliv 
0078 
Thien electronic 
0079 
Siemens VDO 
007A 
Dornier Consulting GmbH 
007B 
Alps 
007C 
PREH 
077D 
Hitachi Unisia 
007E 
Hitachi 
007F 
Reserved 
0080 
Huntsville 
0081 
Yazaki 
0082 
Lear 
0083 
Johnson Controls 
0084 
Hamann / Becker 
0085 
Mitsubishi Electric 
0086 
Tokico USA Inc. 
0087 
Nippon Seiki (NS Int.) 
0088 
Inalfa 
0089 
Nippon Seiki (UK) 
008A 
GHSP 
008B 
Vector 
008C 
Gentex 
008D 
Visteon 
008E 
Tochigi Fuji 
008F 
Chrysler 
0090 
May and Scofield 
0091 
Mercedes-Benz Hamburg lant 
0092 
AIS IN AW 
0093 
TOYOTA MACHINE WORKS 
0094 
Solectron-Invotronics 
0095 
Kicker 
Supplier ID 
[hex] 
Supplier name 
0096 
American Axle Company 
0097 
GETRAG 
0098 
Promate 
0099 
ArvinMeritor 
009A 
Autometer 
009B 
Valeo Sylvania 
009C 
Cobasys 
009D 
Helbako 
009E 
Continental 
009F 
Reserved 
00A0 
Reserved by MMC 
00A1 
Reserved by MMC-SMART 
00A2 
FUSO 
00A3 
Autokabel 
00A4 
Hyundai Mobis 
00A5 
Festo 
00A6 
Schmidhauser 
00A7 
Sphere DesignGmbH 
00A8 
Deutsche Accumotive GmbH & Co KG 
00A9 
BRC Gas Equipment 
00AA 
Delta Energy Systems 
00AB 
A123 Systems 
00AC 
Mercedes AMG 
00AD 
Huber Automotive AG 
00AE 
Witte Velbert 
00AF 
MetaSystem 
00B0 
M/A-COM 
00B1 
TBK (Tokai Bussan Corp) 
00B2 
DDC (Detroit Diesel Corp) 
00B3 
3Soft 
00B4 
MB-Tech 
00B5 
E-T-A 
00B6 
Ssangyong 
00B7 
Paragon 
00B8 
ThyssenKrupp 
00B9 
Hoerbiger 
00BA 
Bang and Olufsen 
00BB 
Hughes 
00BC 
Flextronics 
00BD 
Spheros 
00BE 
Küster ACS 
00BF 
Kromberg und Schubert 
00C0 
SB LiMotive 
00C1 
MAGNA E-Car Systems GmbH & Co OG 
00C2 
SK innovation 
00C3 
Renault 
00C4 
Bury 
00C5 
Digades 
00C6 
Claas 
00C7 
Widmaier 
00C8 
Garmin 
00C9 
Liebherr 
00CA 
LAWO 
00CB 
Poclain Hydraulics Industry 
00CC 
Tesla 
00CD 
Daimler Plant Mannheim 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 110 页
A0010023099: 2013-02, page 109 
 
 
Copyright Daimler AG 2013  
Supplier ID 
[hex] 
Supplier name 
00CE - 
00FD 
Reserved 
00FE 
Reserved (After Market Supplier KWP 
2000) 
00FF 
Reserved (Unidentified KWP 2000) 
0100 - 
6FFD 
Reserved 
6FFE - 
6FFF 
Reserved for CVD CDS approach 
7000 - 
7FFF 
Reserved 
Supplier ID 
[hex] 
Supplier name 
8000 - 
EFFD 
Reserved for CVD CDS approach 
EFFE 
After Market Supplier 
EFFF 
Unidentified 
F000 - 
FFFF 
Reserved 
 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 111 页
A0010023099: 2013-02, page 110 
 
 
Copyright Daimler AG 2013  
12 AUTOSAR Modul Encoding 
 
Table 95 AUTOSAR module Encoding 
 
ID code 
Long Name 
001 
Operating System 
002 
RunTime Environment 
003 - 009 
Reserved 
010 
ECU State Manager 
011 
Function Inhibition Manager 
012 
Communication Manager 
013 
Watchdog Manager 
014 
Reserved 
015 
Development Error Tracer 
016 - 019 
Reserved 
020 
NVRAM Manager 
021 
Flash EEPROM Emulation 
022 
Memory Abstraction Interface 
023 - 028 
Reserved 
029 
Generic NM Interface 
030 
Reserved 
031 
CAN NM 
032 
FlexRay NM 
033 
UDP NM 
034 
Reserved 
035 
CAN Transport Layer 
036 
FlexRay Transport Layer 
037 
SAE J1939 Transport Layer 
038 - 039 
Reserved 
040 
EEPROM Abstraction 
041 
Reserved 
042 
BSW Mode Manager 
043 
Watchdog Interface 
044 - 049 
Reserved 
050 
AUTOSAR COM 
051 
PDU Router 
052 
IPDU Multiplexer 
053 
Diagnostic Communication Manager 
054 
Diagnostic Event Manager 
055 
Diagnostic Log and Trace 
056 
Socket Adaptor 
057 
Debugging 
058 - 059 
Reserved 
060 
CAN Interface 
061 
FlexRay Interface 
062 
LIN Interface 
063 
LIN NM 
064 
LIN Transceiver Driver 
065 
Ethernet Interface 
066 
TTCAN  Interface 
067 - 069 
Reserved 
070 
CAN Tranceiver Driver 
071 
FlexRay Tranceiver Driver 
072 
Reserved 
073 
Ethernet Tranceiver Driver 
ID code 
Long Name 
074 - 079 
Reserved 
080 
CAN Driver 
081 
FlexRay Driver 
082 
LIN Driver 
083 
SPI Handler Driver 
084 
TTCAN Driver 
085 - 087 
Reserved 
088 
Ethernet Driver 
089 
Reserved 
090 
Internal / External EEPROM Driver 
091 
Reserved 
092 
Internal / External Flash Driver 
093 
RAM Test 
094 - 099 
Reserved 
100 
GPT Driver 
101 
MCU Driver 
102 
Internal / external Watchdog Driver 
103 
Core Test 
104 
Flash Test 
105 - 109 
Reserved 
110 
Crypto Service Manager 
111 - 119 
Reserved 
120 
DIO Driver 
121 
PWM Driver 
122 
ICU Driver 
123 
ADC Driver 
124 
Port Driver 
125 - 129 
Reserved 
130 
BSW Scheduler Module 
131 - 139 
Reserved 
140 
CAN State Manager 
141 
LIN State Manager 
142 
FlexRay State Manager 
143 
Ethernet State Manager 
144 - 159 
Reserved 
160 
Syncronized Time-Base Manager 
161 - 211 
Reserved 
212 
XCP 
213 - 253 
Reserved 
254 
I/O Hardware Abstraction  
255 
Complex Drivers 
256 - 65535 
Reserved 
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 112 页
A0010023099: 2013-02, page 111 
 
 
Copyright Daimler AG 2013  
13 ANNEX A 
Table 96: Change log description 
 
Release date 
Release 
code 
Diagnostic A0010023099 Table of Change Log Description 
Author 
28 February 
2013 
2.7 
• 
Supplier list ´Table 94   Add  $00CA - LAWO, $00CB - Poclain Hy-
draulics Industry, $00CC - Tesla; $00CD - Daimler Plant Mannheim 
• 
Issue# 459:  Removed section 5.5.7 "Network Mirroring Mode Con-
figuration" and section 6.3.7 "Control Network Mirroring Mode "and 
modified tables 89 and 90 accordingly 
• 
Issue# 458: Modified range (0x00-0xFF to 0x00- 0xFA) in sections 
5.5.4 VIN Odometer Counter and 5.5.5 and VIN Odometer Counter 
Limit 
• 
Issue# 456: Modified names of "VIN Odometer" and "VIN Odometer 
Limit" instead of "VIN Odometer counter" and "VIN Odometer counter 
Limit".  
• 
Issue# 447 Removed section 6.3.8 Fast Routing Mode 
• 
Issue# 461: Replaced MBN 11264 with MBN 10482 in all references 
• 
Issue# 450: Added a note to section 6.2.2 Check Routine and 6.2.5 
Check Memory showing their relation to MBN 10761 revisions 
• 
Issue# 439: Replaced all ' no.' ,' no' and 'No' meaning number with  
'No.'  
• 
Issue# 437: Table 99: added names of #defines where SIP-ID and 
Software Licence Number can be found. 
• 
Issue# 462: Redefined F1 60 
• 
Issue# 471: Added DID 010F for LIN Slope Mode  
• 
Issue# 315: Added DID F808 and DID F80B for RBM HEX Gasoline 
and Diesel  
• 
Issue# 467: Corrected typo in table 66 
• 
Issue# 463: Delete specific partnumber format of FUSO and added 
FUSO to the definition of MB Trucks partnumber in table 17 
• 
Issue# 433: Replaced "allowed" with "required" in description of sec-
tion 5.5.1 "Reprogramming Attempt Counter" 
• 
Issue# 464: Added section 5.5.27 Compatibility List (for Onboard 
Configuration) 
• 
Issue# 125: Added section 5.5.28 GVC Update Time Stamp 
• 
Issue# 125: Added section 5.5.29 Read Discrete Signals 
• 
Issue# 125: Added section 5.5.30 Read Analog Signals 
RP
 
 
 
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)

