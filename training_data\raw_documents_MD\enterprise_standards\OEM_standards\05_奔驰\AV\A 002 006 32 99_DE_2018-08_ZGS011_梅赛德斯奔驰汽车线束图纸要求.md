# A 002 006 32 99_DE_2018-08_ZGS011_梅赛德斯奔驰汽车线束图纸要求.pdf

## 文档信息
- 标题：Lastenheft zur Erstellung
- 作者：wadresc
- 页数：112

## 文档内容
### 第 1 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: <PERSON><PERSON><PERSON>, <PERSON> 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 1 von 112 
 
Ausführungsvorschrift 
 
 
 
 
Leitungssatzdokumentation  
für Mercedes-Benz PKW  
 
 
 
 
 
 
 
 
 
 
Version 11.0 
 
 
 
 
 
 
 


### 第 2 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 2 von 112 
 
 Änderungsindex / Änderungsbeschreibung 
 
Änderungsdokumentation des Lebenslaufes Ausführungsvorschrift Leitungssatzzeichnung 
ZGS 
Nachtragsbeschreibung 
Bearbeitet (Datum) 
Daimler AG 
001 
Dokument neu erstellt aus den beiden gültigen AVs  
„Ausfg.Vorsch/Leitungssatzerstellung“ A0000069799 und  
„Ausfg.Vorsch/Leitungssatz Legende“  A0000026299.  
Juric 2011-12-20 
002 
Integration Änderungen aus Workshop 01/12 und 02/12 
Abbildungsverzeichnis eingefügt, Kapitel 8.2 „Normen und Vorschriften“ 
aktualisiert 
Rath 2012-04-25 und 
        2012-11-07 
003 
Kapitel 2.3.1 Änderungsindizes auf der Zeichnung und Kapitel 4.3 
Leitungsschlaufen bzw. geändertes Routing nach Abstimmung in 
Lieferantenreko aktualisiert. 
Rath 2013-09-25 
004 
Überarbeitung Kapitel 7; Integration Beschreibung des KBL- und HCV-
Datenformat (Kapitel 0 und Kapitel 8.6) 
Rath 2014-03-21 
005 
Kapitel 3 (Direktverkabelung von Masseleitungen auf Kabelschuh 
Massekonzept „ohne Splice“) hinzugefügt 
Juric 2014-06-13 
006 
komplette Überarbeitung, KBL 2.4  
MTC-Cabling-TP 
2015-08-
28 
007 
Kleine Korrekturen KBL 2.4, Überarbeitung Massekonzept „ohne Splice“ 
Juric 2016-07-31 
008 
Ergänzungen bei Abschnitten Referenzbezeichnungen, Dummy-POS-
Nummern, Lage Befestigungselement und Steckrichtung. Überarbeitung 
von Dokumentation von Leitungssatz-Stützpunkten, Darstellung von 
Textbändern, Darstellung von KSL-Steckern, Dokumentation von 
Schläuchen 
Neckenich (2016-11-25) 
 
 
 
 
 
 
 


### 第 3 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 3 von 112 
 
009 
 
Auslagerung von Kapitel 8.3 in eigenständige AV (A0060064699) 
 
Neu: Dokumentation von B7-Teilen, Trennung von Leitungsfarben, 
Darstellung von Umhüllungen, Darstellung nicht angeschlagene 
Schirme, Verwendung von TB-Sachnummern 
Neckenich (2017-07-03) 
010 
 
Kleinere Korrekturen und Überarbeitungen 
 
Änderung Tabelle DMU (2.3.4) 
 
Überarbeitung Darstellung Kabelschuhe 
 
Überarbeitung Lagedarstellung (Blickrichtung, etc.) 
 
Überarbeitung Bemaßung von Kabelschuhen & Kontaktgehäusen 
 
Erweiterung Kapitel „Überprüfung der Leitungssatzmaße“ 
 
Neues Kapitel: „Leitungsduplizierung“ 
Neckenich (2018-01-08) 
011 
 
Überarbeitung Kapitel „Darstellungen“ 
o Ausbindungen & Segmentdarstellung 
o KSL-Stecker, Splices 
o Befestigungs- & Zusatzteile 
o Geschlossene Kammern 
 
Referenzbemaßung 
 
Lage gedrehter Kabelschuhe 
 
Kleinere Überarbeitungen 
Dr. Neckenich (2018-08-24) 


### 第 4 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 4 von 112 
 
Inhaltsverzeichnis 
1 
Rahmenbedingungen – Gegenstand dieser Ausführungsvorschrift 
9 
1.1 
Ziele ......................................................................................................................................................... 10 
1.2 
Zuständigkeit .......................................................................................................................................... 10 
1.3 
Verwendete Begriffe .............................................................................................................................. 11 
2 
Dokumentation der Leitungssätze in 2D Zeichnungen 
14 
2.1 
Aufbau einer 2D Leitungssatzzeichnung .............................................................................................. 16 
2.2 
Besonderheiten bei der Darstellung von Leitungssätzen in Mehrblattzeichnung .............................. 17 
2.3 
Besonderheiten bei der Darstellung von Leitungssätzen in einer 2D Zeichnung .............................. 18 
2.3.1 
MB Schriftfeld/ CAD-Datum/ Änderungshistorie Zeichnung (Punkt 1, 2, 3) ............................ 20 
2.3.2 
Verweis auf die gültigen Ausführungsvorschriften (Punkt 4) ...................................................... 22 
2.3.3 
Tabelle Schaltpläne, Tabelle SP mit/ohne Steuercode (Punkt 5) ............................................... 22 
2.3.4 
Tabelle DMU (Punkt 6) ................................................................................................................... 23 
2.3.5 
Bemerkungen/Hinweise Lieferantenspezifisch (Punkt 7) ........................................................... 23 
2.3.6 
Kennzeichnung von Merkmalen zur besonderen Nachweisführung (Punkt 8) ........................... 24 
2.3.7 
Tabelle der Module in der Masterleitungssatzzeichnung (Punkt 9) ............................................ 24 
2.3.8 
Leitungssatztabellen pro Modul (Punkt 10) ................................................................................. 25 
2.3.9 
Teilestückliste (Punkt 11) .............................................................................................................. 26 
2.3.10 Dokumentation Leitungssatzfremde Umfänge in LS-Zeichnung ................................................. 27 
2.3.11 Dokumentation von 7er-Teilen in LS-Zeichnung .......................................................................... 27 
2.3.12 Verweis auf Abweichtabellen auf der Leitungssatzzeichnung ..................................................... 27 
3 
Prozessvorgaben 
28 
3.1 
Direktverkabelung von Masseleitungen auf Kabelschuh Massekonzept „ohne Splice“ .................... 28 
3.1.1 
Anforderungen ................................................................................................................................ 28 
3.1.2 
Verarbeitung ................................................................................................................................... 29 
3.1.3 
Grafische Vorgaben ....................................................................................................................... 30 
3.2 
Leitungsduplizierung .............................................................................................................................. 32 
3.2.1 
Leitungsduplizierung auf Grund topologischer Varianz der Steuergeräte (OKZ) ....................... 32 
3.2.1.1 
Anforderungen ...................................................................................................................... 32 
3.2.1.2 
Vorgaben für Leitungsduplizierung ...................................................................................... 32 
3.2.2 
Leitungsduplizierung auf Grund topologischer Verschiebung ..................................................... 33 
3.2.2.1 
Anforderungen ...................................................................................................................... 33 


### 第 5 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 5 von 112 
 
3.2.2.2 
Vorgaben für Leitungsduplizierung ...................................................................................... 33 
3.2.2.3 
Zeichnungsdarstellung .......................................................................................................... 34 
4 
Datenformate und Einheiten der Leitungssatzzeichnung 
35 
4.1 
Leitungslängen ....................................................................................................................................... 35 
4.2 
Leitungssatz-Gewicht ............................................................................................................................. 35 
4.3 
Angabe des Datums ............................................................................................................................... 35 
4.4 
Positionsnummern ................................................................................................................................. 35 
4.4.1 
Format der Positionsnummer ........................................................................................................ 36 
4.4.2 
Sonstige Positionsnummern .......................................................................................................... 36 
4.5 
Teilkennzeichnung .................................................................................................................................. 36 
4.6 
Allgemeine Anforderung Umweltschutz ............................................................................................... 37 
5 
Leitungssatzkomponenten in der 2D Zeichnung 
38 
5.1 
Darstellung der Kontaktierungsteile ..................................................................................................... 38 
5.1.1 
Kontaktierungsteil-Symbol ............................................................................................................ 38 
5.1.2 
Kontaktierungsteil-Tabelle ............................................................................................................. 39 
5.1.3 
Symbolbereitstellung ..................................................................................................................... 39 
5.1.4 
Referenzbezeichnungen (REF) ....................................................................................................... 39 
5.1.5 
Aufbau der Referenz ...................................................................................................................... 39 
5.1.6 
Steckername .................................................................................................................................. 40 
5.1.7 
Kontaktform.................................................................................................................................... 40 
5.1.8 
Kabelschuh ..................................................................................................................................... 40 
5.1.9 
Darstellung von KSL-Steckern....................................................................................................... 40 
5.2 
Dokumentation von Leitungssatz-Stützpunkten .................................................................................. 41 
5.2.1 
Gültige Varianten ........................................................................................................................... 41 
5.2.2 
Darstellung ..................................................................................................................................... 41 
5.3 
Schlüsselwörter ...................................................................................................................................... 42 
5.4 
Leitungsschlaufen bzw. geändertes Routing ........................................................................................ 42 
5.5 
Lagedarstellung ...................................................................................................................................... 42 
5.6 
Ausbindungsrichtung ............................................................................................................................. 43 
5.7 
Befestigungselemente ........................................................................................................................... 43 
5.7.1 
Darstellung ..................................................................................................................................... 43 
5.7.2 
Lage Befestigungselement zum Leitungssatz .............................................................................. 44 


### 第 6 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 6 von 112 
 
5.7.3 
Lage Befestigungselement und Steckrichtung zum Leitungssatz............................................... 44 
5.7.4 
Lage Kabelbinderschloss zum Leitungssatz ................................................................................. 45 
5.7.5 
Benennung Befestigungspunkt in der Zeichnung ........................................................................ 45 
5.7.6 
Bezeichnung Kabelkanal- & Tüllen-Ausgänge ............................................................................... 46 
5.8 
Darstellung Segmente ........................................................................................................................... 47 
5.8.1 
Darstellung Segmente mit Isolierung............................................................................................ 47 
5.8.2 
Darstellung nicht angeschlagene Schirme ................................................................................... 47 
5.9 
Dokumentation anderer Leitungssatzumfänge .................................................................................... 48 
5.9.1 
Längswasserdichtheit .................................................................................................................... 48 
5.9.2 
Sicherungen, Relais........................................................................................................................ 48 
5.9.3 
Zusatzteile ...................................................................................................................................... 49 
5.9.4 
Etiketten und Markierungen .......................................................................................................... 49 
5.10 Bemaßungen ........................................................................................................................................... 50 
5.10.1 Bemaßung von Segmenten ............................................................................................................ 50 
5.10.2 Bemaßung von Leitungsschutz-Elementen ................................................................................... 50 
5.10.3 Bemaßung von Kabelschuhen ....................................................................................................... 50 
******** Bemaßung von sternförmigen Massestellen ...................................................................... 50 
5.10.3.2 Bemaßung gewinkelter Kabelschuhe und Sonderformen ................................................. 51 
5.10.3.3 Lage gedrehter Kabelschuhe .............................................................................................. 51 
5.10.4 Bemaßung Befestigungselement .................................................................................................. 51 
5.10.5 Bemaßung von Kontaktgehäusen ................................................................................................. 52 
5.10.6 Referenzpunktbemaßung ............................................................................................................... 52 
5.11 Toleranzen .............................................................................................................................................. 53 
5.11.1 Toleranzen für Segmente ............................................................................................................... 53 
5.11.2 Toleranzen für Referenzpunktbemaßung von Segmenten........................................................... 53 
5.11.3 Toleranzen für Befestigungselemente .......................................................................................... 53 
5.11.4 Toleranzen für Tüllen ...................................................................................................................... 54 
5.12 Verwendung von Tabellenzeichnungs-Sachnummern ......................................................................... 55 
5.12.1 Dokumentation von Massestellen ................................................................................................. 55 
5.12.2 Schläuche in Bündeln mit variablem Durchmesser ..................................................................... 55 
6 
Überprüfung der Leitungssatzmaße 
56 
6.1 
Blick- und Lagerichtungen ..................................................................................................................... 56 


### 第 7 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 7 von 112 
 
6.2 
Referenz- / Maßbezugspunkte .............................................................................................................. 56 
6.3 
Vermessung von Kabelschuhen ............................................................................................................ 57 
6.3.1 
Vermessung von Kabelschuhsternen............................................................................................ 57 
6.3.2 
Vermessung von gewinkelten Kabelschuhen und Sonderformen ............................................... 57 
6.4 
Vermessung von Kontaktgehäusen ...................................................................................................... 58 
7 
Datenbereitstellungsprozess 
60 
7.1 
Leitungssatzdaten .................................................................................................................................. 60 
7.2 
Schaltplandaten ..................................................................................................................................... 61 
8 
Datenformate in der Leitungssatzentwicklung 
63 
8.1 
Einzelleitungs-, Modul- und Masterleitungssatz ................................................................................... 63 
8.1.1 
Einzelleitungssatz ........................................................................................................................... 63 
8.1.2 
Modulleitungssatz .......................................................................................................................... 63 
8.1.3 
Masterleitungssatz ......................................................................................................................... 63 
8.2 
Namenskonvention für KBL- und HCV-Dateien .................................................................................... 64 
8.3 
KBL (Kabelbaumliste) ............................................................................................................................. 65 
8.4 
Digitale Netzliste .................................................................................................................................... 65 
8.5 
TIFF.......................................................................................................................................................... 66 
8.6 
HCV ......................................................................................................................................................... 66 
8.6.1 
Aufbau des HCV-Datencontainer .................................................................................................. 66 
8.6.2 
KBL .................................................................................................................................................. 67 
8.6.3 
Index.xml ........................................................................................................................................ 67 
8.6.4 
SVG ................................................................................................................................................. 71 
******* 
Unterstützte SVG Elemente ................................................................................................. 72 
8.6.4.2 
Transformationen .................................................................................................................. 76 
******* 
Wichtige Hinweise ................................................................................................................. 78 
8.6.4.4 
Verlinkung zwischen SVG und KBL ...................................................................................... 78 
8.6.4.5 
Typ-Spezifizierer .................................................................................................................... 79 
8.6.4.6 
Objekte und ihre Darstellung ............................................................................................... 83 
8.6.4.7 
Besonderheiten und implizite Logik bei der Darstellung .................................................. 104 
9 
Mitgeltende Unterlagen 
105 
9.1 
Normative Hinweise ............................................................................................................................. 105 


### 第 8 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 8 von 112 
 
9.2 
Normen und Vorschriften .................................................................................................................... 106 
9.3 
Abkürzungen und Begriffe ................................................................................................................... 108 
9.4 
Abbildungsverzeichnis ......................................................................................................................... 110 
9.5 
Anhang .................................................................................................................................................. 112 
 
 


### 第 9 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 9 von 112 
 
1 Rahmenbedingungen – Gegenstand dieser Ausführungsvorschrift 
Diese Ausführungsvorschrift enthält die von der Daimler AG festgelegten Anforderungen an die 
Leitungssatzzeichnung. Sie stellt die verbindliche Vorgabe zur Erstellung von Dokumentationszeichnungen  
des Leitungssatzes für alle Fahrzeugbaureihen des Bereiches MBC dar. Der Leitungssatz soll mit den 
Bordmitteln des entsprechenden Entwicklungssystems dokumentiert werden können. Die Zeichnungen 
werden mit Zeichnungsrahmen und Zeichnungsschriftfeldern der Daimler AG gefertigt, die KBL/HCV-Datei ist 
mit einen Copyright der Daimler AG analog Zeichnungsschriftfelder zu versehen. 
Beispiel:  
Copyright reserved Daimler AG, Schutzvermerk nach DIN ISO 16016 beachten!  
 
Der hier beschriebene Inhalt beschreibt die inhaltlichen und darstellerischen Anforderungen von MBC an eine 
Dokumentationszeichnung für das Produkt Leitungssatz.  
 
Diese Ausführungsvorschrift beschreibt keine technischen Sachverhalte,  zu verwendende Materialien sowie 
Sachverhalte welche durch Lastenhefte, Normen und Verfahrensanweisungen der Daimler AG geregelt sind. 
Der Lieferant ist verpflichtet sämtliche Ausführungsvorschriften und Normen einzuhalten und die 
Dokumentation stets dem aktuellen Stand anzupassen. Sind für eine einwandfreie Dokumentation 
erforderliche Randbedingungen in dieser Ausführungsvorschrift nicht oder abweichend definiert, ist dies der 
Daimler AG anzuzeigen.  
 
Alle Abweichungen von den Anforderungen dieser Ausführungsvorschrift bedürfen der schriftlichen 
Zustimmung der Daimler AG.  
 
 


### 第 10 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 10 von 112 
 
 
1.1 Ziele 
Die wichtigsten Ziele sind:  
 
Inhalte der Dokumentationszeichnung als Produktdokumentation festzulegen 
sowie ein einheitliches Erscheinungsbild der Leitungssatz- Zeichnung sicherzustellen. 
 
Die Abbildung der Leitungssatz-Inhalte in einer KBL-/HCV-Datei zur Unterstützung der MBC 
internen Prozesse sowie Versorgung der Dokumentation mit archivierungsgerechten Zeichnungen. 
 
Die Abbildung der Leitungssatzzeichnung als Tabellensachverhalt (Master-Leitungssatzzeichnung). 
 
Die Absicherung des Entstehungsprozesses der Leitungssatz-Produktdokumentation. 
 
Versorgung der internen Entwicklungsprozesse mit digitalen Leitungssatzdaten. 
 
1.2 Zuständigkeit 
Zuständig für die Ausführung der Leitungssatzdokumentation, der technischen Inhalte sowie der zu 
verbauenden Einzelteile wie  Kontaktierungsteile, Leitungen, Sonderkabel und Leitungssatzumhüllungen im 
Bereich Fahrgestell und Aufbau der MBC ist die Abteilung Leitungssatzentwicklung.  
 
Die verwendeten Kontaktierungsumfänge sind zwischen den Bereichen Kontaktierung, Leitungssatz-
Entwicklung, den Systemfachabteilungen und dem zuständigen Baureihenteam in der Leitungssatzentwicklung 
abzustimmen! Nicht freigegebene Materialien und Teile bedürfen einer gesonderten Genehmigung des 
Baureihenprojektes, vertreten durch das zuständige Baureihenteam in der Leitungssatzentwicklung. 
 
 
 


### 第 11 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 11 von 112 
 
1.3 Verwendete Begriffe 
Bauraum Master 
Das Gesamtfahrzeug wird in Bauräume unterteilt. Für jeden Bauraum oder für jede größere Geometrievarianz 
wird eine Bauraum-Master 2D-Zeichnung erstellt. Der Bauraum Master definiert einen maximalen 
Gesamtumfang für einen bestimmten Fahrzeugbauraum / Variante. Der Umfang als solcher ist normalerweise 
nicht bestellbar.  
 
Abbildung 1: Bauraum Master 
Modul 
Ein Leitungssatz wird mit einer Sachnummer in einem Modul dokumentiert. Der Masterleitungssatz besteht 
aus einzelnen Modulzeichnungen. Ein Leitungssatzmodul spezifiziert dabei einen bestimmten 
Ausstattungsumfang. Die Abhängigkeiten der Module untereinander können unter Umständen komplex sein. 
Die Module müssen nicht per Default sinnvolle und vollständige Fahrzeugfeatures sein.  
 
 
Abbildung 2: Leitungssatzmodule 


### 第 12 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 12 von 112 
 
Die effizienteste Art der 2D Dokumentation von Leitungssätzen ist die Dokumentation in einer 2D Master-
Leitungssatzzeichnung. Alle Module eines Bauraumes werden auf einer Zeichnung zusammen erstellt und 
gepflegt. 
Somit 
können 
Änderungen, 
die 
mehrere 
Module 
betreffen, 
einmalig 
auf 
der 
Masterleitungssatzzeichnung dokumentiert werden. Diese Zeichnung kann je nach Absprache im Projekt mit 
dem verantwortlichen Sachbearbeiter freigegeben werden. Pro Masterleitungssatz entstehen dann 2 Files:  
- 
HCV File 
(siehe Kapitel HCV )  
- 
TIFF File 
(siehe Kapitel TIFF )  
 
Wird aus einem Masterleitungssatz ein Leitungssatzmodul abgeleitet, so entstehen als Ergebnis dieser 
Ableitung zwei Dateien:  
- 
KBL oder HCV File (siehe Kapitel KBL / Kapitel HCV )  
- 
TIFF File 
 
(siehe Kapitel TIFF)  
 
Segment 
Ein Segment kann am Anfangs- und Endpunkt mit einem schwarzen Kreis gekennzeichnet sein (z.B. Standard-
Bemaßung). Ein Segment wird nicht durch das Anbringen eines oder mehrerer Befestigungselemente 
aufgeteilt. Die Befestigungselemente müssen separat bemaßt werden. 
Knoten für Segmente dürfen nur an den Stellen verwendet werden, an denen auch eine fachliche Funktion 
ersichtlich ist (z.B. Ausbindung, Stecker, etc.). Eine Aufteilung in mehrere Segmente aus Sicht der Konstruktion 
darf in der 2D Zeichnung nicht sichtbar sein. 
 
Abbildung 3: Beispiel für den Aufbau von Segmenten 
 
Befestigungselement 
Befestigungselemente sind Kabelkanäle, Kabelschienen, Kabelführungen mit Dichtlippe, die über eine 
Leitungsfixierung verfügen, Clipse jeglicher Art und Halter die den Leitungssatz im Fahrzeug führen und/oder 
befestigen. 
 


### 第 13 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 13 von 112 
 
kZ-siehe-Teil (keine Zeichnung siehe Teil) 
Es ist die Abkürzung für den Hinweis, dass ein Teil (Sachnummer) keine eigene Zeichnung hat, sondern auf 
einer Tabellen-Zeichnung dargestellt ist. 
 
Tabellen-Zeichnung 
Eine Tabellen-Zeichnung ist eine technische Zeichnung in der die Teile zu einer Grundausführung mit einem 
oder mehreren unterschiedlichen Merkmalen in einer Tabelle erfasst sind. Die Sachnummer der Tabellen-
Zeichnung muss sich von den Sachnummern der Teile unterscheiden. 


### 第 14 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 14 von 112 
 
2 Dokumentation der Leitungssätze in 2D Zeichnungen 
Die Dokumentation eines Leitungssatzes in einer Leitungssatzzeichnung kann auf zwei unterschiedliche Arten 
erfolgen. Beide Dokumentationsmethoden sind zulässig. Der Leitungssatzlieferant muss mit dem zuständigen 
Daimler Sachbearbeiter bzw. Projekt die Entscheidung treffen, welche der Leitungssatzumfänge mit welcher 
Dokumentationsmethode dokumentiert werden. Dabei wird unterschieden in  
1. Dokumentation des Leitungssatzes in einer Mehrblattzeichnung 
2. Dokumentation des Leitungssatzes in einer Einzelblattzeichnung 
Beide Dokumentationsmethoden müssen nach folgenden MB Vorgaben aufgebaut sein: 
MBN 31 001   
Grundlagen der Produktdarstellung 
 
Die Norm dient als Basisinformation mit dem Ziel einer einheitlichen 
Produktdarstellung auf CAD- und MS – Office Zeichnungen der Technischen 
Produktdokumentation. 
MBN 31 020-1 
Schriftfelder in Zeichnungen 
 
Die Norm beschreibt Schriftfelder in Zeichnungen, die ein fest angeordneter 
Bestandteil eines Konstruktionsrahmens (Konstruktions-Zeichnungsvorlage) sind. 
MBN 31 020-2 
Zeichnungsfeld auf CAD-Zeichnungen 
 
Die Norm beschreibt die Darstellung und Angaben in den verschiedenen 
Zeichnungs-Arten für CAD-Zeichnungen, die in der Produktdokumentation der  
Geschäftsfelder Mercedes-Benz-Pkw und Mercedes–Benz–Truck zur Anwendung 
kommen. 
MBN 31 020-3 
Änderungen in Konstruktionszeichnungen 
 
Die 
vorliegende 
Werk 
Norm 
MBN 
31 
020-3, 
Änderungen 
in 
Konstruktionszeichnungen, regelt die Dokumentation von Änderungen in 
Konstruktionszeichnungen. 
MBN 10 317-0 
Kennzeichnung von Merkmalen zur besonderen Nachweisführung 
Grundlagen - Dokumentationspflicht von Bauteilen/ Baugruppen 
MBN 10 317-2 
Kennzeichnung von Merkmalen zur besonderen Nachweisführung 
Spezifische Vorgaben und Anwendungsfälle (MBC, VAN und Buses) 
 
Zur besseren Verständigung ist die Kennzeichnung gemäß MBN 10 317 
durchzuführen. Zusätzlich unterstützen die Beispielvorgaben bei der Festlegung 
von sicherheitsrelevanten Merkmalen. 
A 999 80 02 
Benennungsfestlegung 
für 
Abkürzungen 
im 
zweisprachigen 
(deutsch/englisch) Schriftfeld der MB-Zeichnungen 
 
Diese Arbeitsanweisung regelt die Anwendung von Begriffsabkürzungen, die im 
zweisprachigen Zeichnungsschriftfeld (dt./eng.) vor oder hinter der Benennung 


### 第 15 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 15 von 112 
 
eingetragen werden können. Beispiel: ZB für Zusammenbau. 
V 019 8029 
Änderungen in der Zeichnungsorganisation 
 
Die Verfahrensanweisung regelt die Abwicklung der Zeichnungen, die eine 
Veränderung erfahren, ungültig werden oder Nachfolgedokumente erfordern. 
 
Grundsätzlich gilt für die Leitungssatzzeichnung: 
Sind zusammenhängende, technische Informationen nicht auf einer CAD Zeichnung (auf einem 
Zeichnungsblatt) darstellbar, so können diese auf weiteren Format-Zeichnungsblätter angebracht werden. 
Hinweis: Eine Leitungssatz-Masterzeichnung (Tabellensachverhalt) kann auch mit einer Einzelblattzeichnung 
freigegeben werden, wenn der Umfang auf einer Einzelblattzeichnung darstellbar ist. Umgekehrt muss bei 
einem umfangreichen Leitungssatzmodul, das nicht auf einer Einzelblattzeichnung darstellbar ist, eine 
Mehrblattzeichnung erstellt werden. 
Im folgenden Kapitel werden die Vorgaben für die Struktur und den Inhalt der beiden Dokumentationsarten 
vorgegeben. Dabei ist zu beachten, dass bestimmte Tabellen nur bei einer Leitungssatz-Masterzeichnung auf 
der Zeichnung dargestellt werden müssen. 
 
 


### 第 16 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 16 von 112 
 
2.1 Aufbau einer 2D Leitungssatzzeichnung 
Grundsätzlich gilt für beide Dokumentationsarten, dass der Aufbau von 2D Zeichnungen nach der MBN 31 
020-1 zu erstellen ist. Der strukturelle Aufbau einer Mehrblattzeichnung ist im folgenden Bild dargestellt:  
 
 
Abbildung 4: Aufbau Mehrblattzeichnung 
Wenn eine Zeichnung als Einzelteil-Leitungssatzzeichnung angelegt wird, dann muss diese Zeichnung so 
dargestellt werden, dass sowohl die Zeichnung als auch die zugehörigen Tabellen auf diesem einzigen Blatt 
enthalten sind.  
 
Abbildung 5: Aufbau Einzelblattzeichnung 
 
 
 
 


### 第 17 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 17 von 112 
 
2.2 Besonderheiten 
bei 
der 
Darstellung 
von 
Leitungssätzen 
in 
Mehrblattzeichnung 
Eine Mehrblattzeichnung ist nach der MBN 31 020-1 aufzubauen.  
Besonderheiten der Mehrblattzeichnung auf die geachtet werden muss: 
- 
eine konsistente Blattverwaltung  
- 
Änderungshistorie mit den letzten Änderungsständen der Blätter im Deckblatt der Zeichnung. 
- 
konsistente Blattnummer jedes einzelnen Blattes. 
 
Abbildung 6: Besonderheiten auf Zeichnungskopf Mehrblattzeichnungen 
 
 
 


### 第 18 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 18 von 112 
 
2.3 Besonderheiten bei der Darstellung von Leitungssätzen in einer 2D 
Zeichnung 
Der prinzipielle Aufbau einer 2D Leitungssatzzeichnung ist in der Abbildung 7: Aufbau der 
Leitungssatzzeichnung (bei Mehrblattzeichnungen Blatt1) dargestellt. Im Zentrum des Blatts steht die 
Zeichnung. Die anzugebenden Informationen und Tabellen sind unter den Punkten 1. – 9. beschrieben.  
 
Abbildung 7: Aufbau der Leitungssatzzeichnung (bei Mehrblattzeichnungen Blatt1) 
Es gelten die folgenden, beschriebenen Aspekte und Referenzen: 
Punkt 
Beschreibung 
Kapitelverweis 
1.  
MB Schriftfelder in Zeichnungen nach MBN 31 020-1 
0 
2.  
CAD Datum (Version) 
0 
3.  
Änderungshistorie Zeichnung 
0 
4.  
Verweis auf die gültigen Ausführungsvorschriften (AV) 
2.3.2 
5.  
Tabelle Schaltpläne, Tabelle SP mit/ohne Steuercode (masterbezogen) 
2.3.3 
6.  
Tabelle DMU 
0 
7.  
Bemerkungen/Hinweise Lieferantenspezifisch 
2.3.5 
8.  
Kennzeichnung von Merkmalen zur besonderen Nachweisführung 
2.3.6 
9.  
Tabelle Module im Master 
2.3.7 


### 第 19 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 19 von 112 
 
Das Blatt „2.Blatt“ bis n ist schematisch in der Abbildung 8 dargestellt. Die Punkte 1.-4. und 8. von Abbildung 
7 sind ebenfalls auf diesem Blatt hinterlegt. Zusätzlich sind folgende Punkte darauf zu dokumentieren: 
Punkt 
Beschreibung 
Kapitelverweis 
Siehe 1 
die Tabelle ÄM pro Modul 
 
05 
die Tabelle des Schaltplans und der Steuercode pro Modul 
2.3.3 
1.  MB Schriftfelder in Zeichnungen nach MBN 31 020-1 
0 
2.  CAD Datum (Version) 
0 
3.  Änderungshistorie Zeichnung 
0 
4.  Verweis auf die gültigen Ausführungsvorschriften (AV) 
2.3.2 
8.  Kennzeichnung von Merkmalen zur besonderen Nachweisführung 
2.3.6 
10. 
die Leitungssatztabellen pro Modul 
2.3.8 
11. 
die Teilestückliste pro Modul 
2.3.9 
 
 
Abbildung 8: Blatt 2 (bei einer Mehrblattzeichnung) 
 
 
 


### 第 20 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 20 von 112 
 
2.3.1 MB Schriftfeld/ CAD-Datum/ Änderungshistorie Zeichnung (Punkt 1, 2, 3) 
Folgende Normen sind für die Befüllung des MB Schriftfeldes und der Änderungshistorie einzuhalten: 
MBN 31 001 
Grundlagen der Produktdarstellung 
MBN 31 020-1 
Schriftfelder in Zeichnungen 
MBN 31 020-2 
Zeichnungsfeld auf CAD-Zeichnungen 
MBN 31 020-3 
Änderungen in Konstruktionszeichnungen 
MBN 10 317-0 
Kennzeichnung 
von 
Merkmalen 
zur 
besonderen 
Nachweisführung 
Grundlagen - Dokumentationspflicht von Bauteilen/ Baugruppen 
MBN 10 317-2 
Kennzeichnung 
von 
Merkmalen 
zur 
besonderen 
Nachweisführung 
Spezifische Vorgaben und Anwendungsfälle (MBC, VAN und Buses) 
A 999 80 02 
Benennungsfestlegung für Abkürzungen im zweisprachigen 
(deutsch/englisch) Schriftfeld der MB-Zeichnungen 
 
Die Pflichtfelder inkl. Änderungshistorie eines Schriftkopfes sind in der Abbildung 9: Pflichtfelder des MB 
Schriftfeldes rot gekennzeichnet: 
 
Abbildung 9: Pflichtfelder des MB Schriftfeldes 
 
Hinweis: Sofern nicht das Format „R“ verwendet wird, so muss in das in Abbildung 9 gestrichelt dargestellte 
Feld Maßstab ein konkreter Maßstab eingetragen werden. 
 


### 第 21 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 21 von 112 
 
Wenn in der Zeichnung ein Tabellensachverhalt dargestellt wird (Masterleitungssatz), dann bezieht sich sowohl 
der Schriftkopf als auch der Änderungsindex auf die Sachnummer der Tabelle. Die freigaberelevanten Daten 
der Leitungssatzmodule sind in der „Tabelle Module im Master“ (siehe Abschnitt 2.3.7) darzustellen. Die 
Änderungsindizes der dargestellten Leitungssatzmodule sind pro Modul in einer Tabelle darzustellen. 
Bei sehr großen Tabellenzeichnungen (Innenraum, Cockpit …) ist vom zuständigen Daimler Sachbearbeiter zu 
entscheiden, ob diese Informationen auf dem Blatt 2 einer Mehrblattzeichnung dargestellt werden sollen. 
Zu empfehlen ist folgende Dokumentation im Änderungsschriftkopf: 
 
Vereinfachte Schreibweise der Änderungsmeldung aus ConnectCHANGE: 
Im Änderungsschriftkopf können die Änderungsmeldungen aus ConnectCHANGE in einer verkürzten 
Schreibweise wie folgt angegeben werden: 
<Baureihe><Lieferantennummer><Änderungsmeldung><Jahr> ggf. <Nachtrag> 
Beispiel: 
o Änderungsmeldung aus ConnectCHANGE:  
AEM 222-11-XXXX/13 
o Verkürzte Schreibweise: 
 
 
222-11-XXXX/13 
 
Im Änderungsschriftkopf werden die Indizes mit den zugeordneten Änderungsmeldungen aus 
ConnectCHANGE angegeben. 
 
Die Angabe des Planquadrates/der Planquadrate, in der die Änderung durchgeführt wurde kann dabei 
auch in Abstimmung mit dem Daimler-Verantwortlichen in einer separaten Tabelle auf der Zeichnung 
außerhalb des Zeichnungsschriftkopfes erfolgen. 
 
Beispiel: 
 
 
Abbildung 10: Beispiel Änderungsmeldungen 


### 第 22 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 22 von 112 
 
 
Reicht die Anzahl der freien Änderungszeilen für eine vollständige Dokumentation nicht mehr aus, so 
werden die Änderungsschriftköpfe vom Master und allen von der Änderung betroffenen Module „Mit 
Änderung neu gezeichnet“ bereinigt. Auch wenn eigentlich bei einem Teil der geänderten Module die 
Änderungsschriftköpfe noch nicht voll beschrieben sind. 
 
Beispiel:  
 
Abbildung 11: Beispiel eines Änderungsschriftkopfes "mit Änderung neu gezeichnet" 
Die Änderungsdokumentation auf dem Deckblatt der Mehrblattzeichnung ist analog MBN 31020-3 zu 
dokumentieren. Als Bearbeitungsdatum muss hier das Bearbeitungsdatum aus dem Zeichnungskopf des 
geänderten Blattes eingetragen werden. 
Die ESD-Kennzeichnung im Schriftfeld der Tabellenzeichnung ist analog A0598030 immer mit einem „N“ zu 
versehen. 
2.3.2 Verweis auf die gültigen Ausführungsvorschriften (Punkt 4) 
Der Verweis auf diese AV (A0020063299) muss auf der Leitungssatzzeichnung aufgeführt sein.  
2.3.3 Tabelle Schaltpläne, Tabelle SP mit/ohne Steuercode (Punkt 5) 
Für die lückenlose Dokumentation eines Leitungssatz ist es erforderlich die für die Erstellung des aktuellen 
Leitungssatz verwendeten Schaltplandatensätze in einer Tabelle aufzuführen. 
 
Pro Sachnummer ist eine Zeile in der folgenden Gliederung anzulegen:  
Spalte 1 Sachnummer der Schaltpläne aufgebaut nach der AV „Schaltplan“ – A 000 006 98 99 
Spalte 2 Blattnummer des Schaltplans 
Spalte 3 Benennung des Schaltplans 
Spalte 4 Datum ( Version ) des Schaltplans nach Vorgabe aus AV „Schaltplan“ – A 000 006 98 99 
Spalte 5 (Code) verwendeten Schaltplancode 
 


### 第 23 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 23 von 112 
 
Leitungssatz entstand aus folgenden Schaltplänen: 
Harness developed using following wiring diagram: 
Sachnummer 
Blattnummer 
Benennung 
Datum 
Code 
Part number 
Sheet number 
Title 
Date 
Code 
222-540-00-02 
1 
Schaltplan Diagnose 
2011-01-05 
 
222-540-00-01 
2 
Schaltplan Luftfeder 
2011-01-05 
 
Hinweis: Die oben aufgeführten Attribute müssen enthalten sein, das Layout der Tabelle kann von der 
dargestellten Form abweichen. 
2.3.4 Tabelle DMU (Punkt 6) 
Hinweis:  
 
 
Die Angabe der DMU-Tabelle und der Sachnummer der DMU-Modelle ist zwingend notwendig, unabhängig 
davon, ob die Zeichnung von einem 3D-Modell abgeleitet worden ist oder nicht. 
 
In der DMU-Tabelle müssen alle verwendeten 3D-Modelle, die zur Erstellung des Leitungssatzes verwendet 
worden sind, aufgelistet werden. Dabei sind folgende Attribute aufzuführen:  
- 
Sachnummer des 3D-Modells 
- 
Benennung des DMU-Modells  
- 
Version (Smaragd-Version) des Modells  
 
Leitungssatz entspricht den folgenden DMU-Modellen: 
Harness corresponds to following DMU Models: 
Sachnummer 
Benennung 
Version 
Part number 
Title 
Version 
HCA205540B001 
ZB EL.LTG DMU VORB IR LL RE 
0002.021 
HCA205540B162 
ZB EL.LTG DMU VORB MR W/S/A/C BATT RL 
0001.016 
2.3.5 Bemerkungen/Hinweise Lieferantenspezifisch (Punkt 7) 
Alle Vereinbarungen, die zu einer besseren Lesbarkeit der Zeichnung dienen und nicht im Lastenheft (LH) bzw. 
Ausführungsvorschrift (AV) erfasst sind, können im Bereich „Baureihenspezifische Unterschiede“ dargestellt 
werden. Dabei ist zu beachten, dass keiner der Vorgaben aus dem LH bzw. der AV widersprochen wird.  
 


### 第 24 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 24 von 112 
 
2.3.6 Kennzeichnung von Merkmalen zur besonderen Nachweisführung (Punkt 8) 
Das Merkmal „100% elektrische Prüfung“ muss immer auf allen Leitungssatzzeichnungen abgebildet sein. Es 
wird für alle erforderlichen Prozessschritte beim Leitungssatzlieferanten zur Sicherstellung sämtlicher 
Qualitätsmerkmale die Leitungssatzdokumentation vollumfänglich mit dem Merkmal DS1-S versehen. Des 
Weiteren sind, falls erforderlich, die Merkmale für Drehmoment und Drehwinkel zu dokumentieren (DS2-S, 
DS3-S). Die konkreten Drehmoment- und Drehwinkel-Werte sind auf der zugehörigen Bauteilzeichnung zu 
dokumentieren.  
Details zu der Kennzeichnung von besonderen Merkmalen sind aus der MBN 10 317 „Kennzeichnung von 
Merkmalen zur besonderen Nachweisführung“ zu entnehmen. 
2.3.7 Tabelle der Module in der Masterleitungssatzzeichnung (Punkt 9) 
Diese Tabelle muss immer dann auf der Zeichnung abgebildet sein, wenn die Zeichnung einen 
Tabellensachverhalt (Masterleitungssatz) abbildet.  
Jede Zeile der Tabelle enthält die Stammdaten eines Leitungssatzes, d.h. alle Informationen, die im Schriftkopf 
einer Modulzeichnung aufzufinden sind. 
Spaltennr. 
Spalten-Name 
Beschreibung 
1.  
Variante 
Kürzel für die Variante des gelisteten Leitungssatzmoduls 
2.  
DAG-Sachnummer 
Daimler Sachnummer des Leitungssatzes 
3.  
Benennung 
Eintragung der SRM - Benennung des Leitungssatzes 
4.  
Zeichnungsgeometriestand 
Eintragung ZGS zur Sachnummer 
5.  
Auftragsnummer 
Eintragung KEM (Freigabe Leitungssatz) 
6.  
Zeichnungsdatum 
Zeichnungsdatum [nach DIN ISO 8601 („JJJJ-MM-DD“)] 
7.  
Name 
Name des Leitungssatzverantwortlichen 
8.  
Leitungssatzgewicht 
Gewicht des Leitungssatz [kg] 
9.  
Datenstand/Version 
[Datum nach DIN ISO 8601 („JJJJ-MM-DD“)] 
10.  
Vorgängersachnummer 
Wenn vorhanden, die Sachnummer des Vorgänger - 
Leitungssatzes 
11.  
Anzahl/Merkmale 
nach Vorgabe MBN 31 020-1 und MBN 10 317 
auszufüllen 
Hinweis: Die Merkmalsspalte muss mit DSx-S beschriftet 
sein. 


### 第 25 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 25 von 112 
 
12.  
ESD Kennzeichnung 
nach Vorgabe MBN 31 020-1 und MBN 10 317 
auszufüllen 
13.  
Schaltplancodierung 
alle Code, die die Verwendung der einzelnen Module 
beschreiben. Bsp.: IP494 - für USA, Quelle: ist der 
Schaltplan (optional) 
2.3.8 Leitungssatztabellen pro Modul (Punkt 10) 
Die Leitungssatztabelle listet die verwendeten Leitungen mit ihren Anschlusspunkten auf und ist vorzugsweise 
am linken Zeichnungsrand anzuordnen. In den Tabellenzeichnungen (Masterleitungssatzzeichnung) ist in der 
Überschrift der Tabelle die Sachnummer und Variante des Leitungssatzmoduls aufzuführen (auf der 
Zeichnung/Schriftfeld wird die SRM–Benennung dargestellt, siehe Norm MBN 31 020-1). Bei einer 
Tabellenzeichnung ist diese Tabelle pro Modul aufzuführen. Bei sehr großen Tabellenzeichnungen (Innenraum, 
Cockpit …) ist vom zuständigen Daimler Sachbearbeiter zu entscheiden ob diese Informationen auf dem Blatt 
2 einer Mehrblattzeichnung dargestellt werden sollen. 
Pro Leitung ist eine Zeile in der folgenden Gliederung anzulegen: (4 
Spalten-Nr 
Beschreibung 
1.  
Leitungsnummer 
2.  
Typ der Leitung (4 
3.  
Angaben zu Querschnitt 
4.  
Angabe der Farben (1 
5.  
Bauteilbezeichnung REF (2  (Leitungsanfang), Steckername, Bauteil  (Leitungsanfang) 
6.  
Kontaktbezeichnung (Pin), Bauteil (Leitungsanfang) 
7.  
Feldkenner entsprechend der Zeichnungs-Koordinatenfelder, Bauteil (Leitungsanfang) 
8.  
Bauteilbezeichnung  REF (2 (Leitungsende), Steckername, Bauteil (Leitungsende) 
9.  
Kontaktbezeichnung (Pin), Bauteil (Leitungsende) 
10.  
Feldkenner entsprechend der Zeichnungs-Koordinatenfelder, Bauteil (Leitungsende) 
11.  
Sachnummer des Kontakt (Leitungsanfang) 
12.  
Sachnummer der am Kontakt verwendeten  Dichtungen  (Leitungsanfang) 
13.  
Sachnummer des Kontakt (Leitungsende) 
14.  
Sachnummer der am Kontakt verwendeten  Dichtungen  (Leitungsende) 


### 第 26 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 26 von 112 
 
15.  
Leitungslängen (ohne Zuschlag) (3 
16.  
Leitungslängen (mit Zuschlag, optionale Spalte) (3 
(1 Leitungsfarben nach DIN IEC 60 757, Trennung durch „/“ 
(2 Bauteilbezeichnung entsprechend Verwendungszweckkatalog nach VA 059 EI08 
   Referenz -    Langbezeichnung (deutsch)  
   Beispiel: A2 – Radio mit CD Player 
(3  je eine Spalte pro Variante bei Tabellenzeichnungen 
(4  Leitungsausführung nach AV Leitungen A 000 002 61 99 
2.3.9 Teilestückliste (Punkt 11) 
Die Tabelle listet alle Daimler-Teilesachnummern, die im Leitungssatzmodul verbaut sind, auf. Pro 
Sachnummer ist eine Zeile in der folgenden Gliederung anzulegen: 
 
Spalten-Nr 
Beschreibung 
1.  
POS-Nummer entsprechend der Vorgaben aus ConnectPARTS 
2.  
Sachnummer (Daimler AG) 
3.  
Benennung 
entsprechend 
der 
Vorgaben 
aus 
ConnectPARTS  
(auf der Zeichnung/Schriftfeld wird die SRM-Benennung dargestellt, siehe Norm MBN 
31 020-1) 
4.  
Zeichnungssachnummer bei KZ-siehe-Teil Sachverhalt 
5.  
Menge in Stück bzw. Gesamtlänge (je eine Spalte pro Variante bei Tabellenzeichnungen) 
 
Die Einträge in dieser Tabelle sind nach Spalte 2 (Sachnummer) aufsteigend zu sortieren. 
Existiert für eine Sachnummer keine Einzelteil-Zeichnung, so wird in der Teilestückliste auf der Zeichnung im 
Feld „Teilezeichnung“ die Sachnummer der Tabellen-Zeichnung aufgeführt in der diese Sachnummer 
beschrieben ist. 
Die Erweiterung der Sachnummer um das Attribut kZ ist technisch und fachlich nicht relevant und entfällt 
damit auf der Zeichnung. 
Gibt es weder eine Einzelteil-Zeichnung noch eine Tabellenzeichnung, so ist die gültige AV für die Sachnummer 
im Feld Teilezeichnung einzutragen. 


### 第 27 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 27 von 112 
 
In den Tabellenzeichnungen (Masterleitungssatzzeichnung)  ist in der Überschrift der Tabelle die Sachnummer 
und Variante des Leitungssatzmoduls aufzuführen. Bei einer Tabellenzeichnung ist diese Tabelle pro Modul 
aufzuführen.  
Bei sehr großen Tabellenzeichnungen (Innenraum, Cockpit …) ist vom zuständigen Daimler Sachbearbeiter zu 
entscheiden ob diese Informationen auf dem Blatt 2 einer Mehrblattzeichnung dargestellt werden sollen. 
2.3.10 Dokumentation Leitungssatzfremde Umfänge in LS-Zeichnung 
Es gibt Umfänge, die nicht von der Fachabteilung Leitungssatz verantwortet sind, aber zusammen mit dem 
Leitungssatz verlegt werden. Aus diesem Grund werden diese Umfänge in der LS-Zeichnung als reine 
Information dargestellt, d.h. diese sind nicht Bestandteil der KBL bzw. Leitungssatzstückliste. Beispiele hierfür 
sind Wasser- oder Pneumatik-/Hydraulikleitungen. 
2.3.11 Dokumentation von 7er-Teilen in LS-Zeichnung 
Einige Umfänge wie beispielsweise Sicherungsdosen werden als 7er-Teile direkt ans Band des Lieferanten 
geliefert, sollen jedoch gleichzeitig in der Leitungssatz-Zeichnung dargestellt werden. Diese 7er-Teile können 
auf der LS-Zeichnung abgebildet werden. Sie dürfen allerdings nicht modularisiert werden, also in der 
zugehörigen KBL-Datei nicht in den Controlled_components und damit auch nicht in der index.xml-Datei 
enthalten sein. Auf der Zeichnung darf am 7er-Teil entsprechend kein Verweis auf die Module sein. 
2.3.12 Verweis auf Abweichtabellen auf der Leitungssatzzeichnung 
Auf den Leitungssatzeichungen müssen lokale Abweichungen (AWT) der jeweiligen Länder dokumentiert 
werden. Dazu muss auf der jeweiligen betroffenen Leitungssatzeichung folgender Verweis eingetragen 
werden:  
Alternative Komponenten werden mit folgenden Abweichtabellen dokumentiert: 
<Liste der SNRs> 
 
Beispiel:  
Alternative Komponenten werden mit folgenden Abweichtabellen dokumentiert: 
A205 540 68 10, A205 540 69 10, A205 540 70 10 
 
 
 
 


### 第 28 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 28 von 112 
 
3 Prozessvorgaben 
3.1 Direktverkabelung von Masseleitungen auf Kabelschuh Massekonzept 
„ohne Splice“ 
3.1.1 Anforderungen 
1. Der dem Verbraucher nächstliegende Massebolzen, bezogen auf die effektive Leitungslänge, ist zu 
nutzen. Änderungen nur nach Absprache mit dem Auftraggeber.  
2. Der Masseanschluss für 48V erhält eigene Massebolzen, die hier zu verwendende Kabelfarbe lautet 
braun/violett. Der Anschluss weiterer Massen z.B. 12V an die 48V Masse ist unzulässig. Eine 
Verwechselung ist z.B. durch die Kabellänge auszuschließen. 
3. Bei der Verlegung der Masseleitungen sind spezielle Anforderungen der Steuergeräte zu 
berücksichtigen. Beispielsweise sind Elektronik-, CAN-, EMV-Massen autark auszuführen und werden 
nicht mit Lastströmen oder pulsierenden Strömen gemischt. Redundante Leitungen müssen auf 
separate Massen geführt werden. 
4. Alle Massekabelschuhe einer Massestelle sind vom Auftragnehmer miteinander vormontiert (verclipst) 
anzuliefern. Es ist dabei auf eine bauraumoptimierte Anordnung (kompakte Verclipsung) der 
Kabelschuhe zu achten.  
5. Bei der Auslieferung des Leitungssatzes muss sichergestellt werden, dass im Falle von M6-Bolzen jede 
Massestelle einen Kabelschuh mit lackschabender Mutter enthält, es sei denn es ist durch den 
Auftraggeber anders gewünscht. 
     
                          
                 
            
      
 
Abbildung 12: bauraumoptimierte Anordnung (kompakte Verclipsung) Kabelschuhe 
Die Art der Anordnung ist über das KBL-Attribut GND_TERMINAL_ARRANGEMENT zu beschreiben ( 
siehe AV Leitungssatz DMU Erstellung )  
 
 


### 第 29 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 29 von 112 
 
3.1.2 Verarbeitung 
1. Standardmäßig sind elektrische Kontakte in Crimptechnologie anzuschlagen, sofern durch die 
Bauteilvorgabe des elektrischen Kontakts keine andere Anschlagtechnologie vorgegeben wird (z.B. 
Schweißen). Bei der Verarbeitung sind generell neben den Vorgaben auf den Daimler-Zeichnungen 
auch alle Vorgaben der Bauteilhersteller zu beachten und umzusetzen. Bei eventuellen Widersprüchen 
in den Vorgaben muss der Auftragnehmer diese proaktiv mit dem Auftraggeber abstimmen. 
2. Leitungen (Ltg.) pro Crimp  
 
Es dürfen, unter Berücksichtigung von Punkt „3.2.1“ bis zu 6 Leitungen pro Crimp verwendet 
werden.  
 
Es muss die Summe aller Ltg. zum Crimp passen.  
 
Es muss der gesamte Bündelquerschnitt zum ISO - Crimp passen.  
 
Es müssen die Vorgaben zur Überlötung eingehalten werden. 
Alle Prozessschritte beim Anschlagprozess müssen durch jeweils geeignete und mit dem Auftraggeber 
abgestimmte Maßnahmen überwacht werden. 
3. Unabhängig von bestehenden Regelungen bzgl. Überlöten gilt hiermit als gesetzt, dass Kontakte ab 
einem angeschlagenen (Gesamt-) Leitungsquerschnitt von 6 mm² (oder größer) generell zu überlöten 
sind, sofern nicht eine explizite Freigabe des Kontaktes durch Daimler vorliegt, die den Einsatz einer 
unverlöteten Anschlagtechnologie genehmigt (z.B. Schweißkontakte). 
4. Die Stromlast über einen Kabelschuh und Bolzen ist abhängig von der Blechdicke des Rohbaus, dem 
Bolzendurchmesser, Bolzenmaterial bzw. der Bolzengeometrie, Blechdicke des Kabelschuhs und von 
der Leitung. Es ist nicht zulässig, die Strombelastbarkeit eines Kabelschuhs ausschließlich an dem 
Leitungsquerschnitt zu bestimmen, da einige Kabelschuhe-Leitungs-Paarungen ausschließlich zur 
Reduzierung des Spannungsabfalles auf der Leitung entwickelt wurden. 
Aus den hier erwähnten Gründen sind viele Kabelschuhe in ConnectPARTS gesperrt. Hier kann durch 
den Konfektionär eine Freischaltung beim Kontaktierungsteam des Auftraggebers erlangt werden, 
wenn eine derartige Prüfung vollzogen wurde und der hier gemessene Strom als I.O. bewertet wurde. 
Ansonsten ist eine Verwendung unzulässig.  
Der maximale Summenstrom (Dauer + Peak) an einem Massebolzen über alle aufgelegten Leitungen 
ist zu dokumentieren und dem Auftraggeber vor der Zeichnungsumsetzung vorzustellen. Dabei darf 
der Imax Bolzen und Imax Kabelschuh nicht überschritten werden.  
Beispiele:  
 
A003 546 22 40:  auch für große Leitungsquerschnitte sind sowohl der Bolzen M6 als auch 
die Kabelschuh-Blechdicke 0,8 mm die begrenzenden Elemente. 
 
A172 982 30 02: auch für große Leitungsquerschnitte sind sowohl der Bolzen M6 als auch 
die Kabelschuh-Blechdicke 0,8 mm die begrenzenden Elemente. 


### 第 30 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 30 von 112 
 
 
 
Abbildung 13: Auszug maximaler Summenstrom Massebolzen 
 
3.1.3 Grafische Vorgaben 
1. Grafik wie hier abgebildet in der Zeichnung als Masseanschluss verwenden 
 
 
Abbildung 14: Masseanschluss ohne/mit Schrumpfschlauch 
2. Dokumentiert wird im Falle von M6-Bolzen nur die Tabelle des Kabelschuhs mit gefangener Mutter. 
Im Falle von M8-Bolzen wird die Tabellensachnummer des Rastkabelschuhs verwendet (A 002 982 
54 02). 
Die Anzahl und die Sachnummern der restlichen Kabelschuhe (ohne gefangene Mutter)  
sind nicht dokumentiert und sind kostenirrelevant, da im Klima-Adernpreis enthalten.  


### 第 31 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 31 von 112 
 
 
Abbildung 15: Auszug Leitungssatzzeichnung 
3. Die Referenz (REF) der Masse bezeichnet nur den Masseanschluss, nicht die Nummer des 
Kabelschuhs. 
4. Für den Leitungsabgang am isolierten Crimp ist der vorgegebene Biegeradius sicherzustellen. Mit 
Einhaltung des Biegeradius durch Auswahl geeigneter Leitungslängen muss das Abknicken verhindert 
werden. Überlängen müssen auch weiterhin in Richtung Hauptstrang zurückgeführt werden. 
5. Zur Vermeidung von Überlängen wird bei Massestellen nach einem definierten Abstand (imaginäre 
Linie zu Lochmitte, vgl. Abschnitt ********) eine Fassung (z.B. Wickel, Befestigungselement, 
Ausbindung) benötigt. Für Anwendungen ohne Schrumpfschlauch (trockener Bereich) beträgt dieser 
Abstand 80 mm, bei Anwendungen mit Schrumpfschlauch 120 mm. 
6. Die Darstellung der genutzten Verclipsung-Variante erfolgt zentral durch die Variantengrafik in der 
Leitungssatzlegende, sodass die Darstellung der Verclipsung an der eigentlichen Massestelle entfällt. 
Für die Zeichnung ungültige Verclipsungsvarianten müssen dort durchgestrichen werden. Bei mehr als 
einer freigegebenen Variante pro Zeichnung muss an allen Kabelschuhen ein entsprechender 
Hinweistext („Variante xy“) angebracht werden. 
7. Die Anzahl maximal vorgegebener Kabelschuhe ist entsprechend Zeichnungsvorgaben an der 
jeweiligen Massestelle zwingend einzuhalten. 
 


### 第 32 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 32 von 112 
 
3.2 Leitungsduplizierung  
3.2.1 Leitungsduplizierung auf Grund topologischer Varianz der Steuergeräte (OKZ) 
In den Fällen, in denen an den Kontaktierungen das Ortskennzeichen (OKZ) verwendet wird, ist es notwendig 
die laut Schaltplan angeschlagenen Leitungen zu duplizieren, da diese im Schaltplan nicht eindeutig dargestellt 
werden (vgl. AV Schaltplanerstellung). 
3.2.1.1 Anforderungen 
Da im Schaltplan Leitungen auf Grund topologischer Varianz nicht mehr dupliziert werden, muss dies im 
Nachfolgeprozess 
geschehen. 
Hierzu 
muss 
entweder 
der 
Router 
innerhalb 
des 
Leitungsentwicklungswerkzeuges die Duplizierung vornehmen oder ein Vorprozess muss dies vor dem Import 
ins Entwicklungswerkzeug sicherstellen. 
3.2.1.2 Vorgaben für Leitungsduplizierung 
Abhängig vom OKZ von Start- und/oder End-Kontaktierung erhält die Leitungsnummer jeweils einen 
entsprechenden Suffix (vgl. AV Schaltplanerstellung). Dieser entspricht der Zählnummer des OKZ aus dem 
DMU/Connect und wird ohne den Präfix „O“ übernommen. Als Trennzeichen wird ein „.“ verwendet. Bei 
Sonderleitungsbestandteilen steht dieser Trenner vor der Untergliederung der Bestandteile an der 
Leitungsnummer der Sonderleitung. 
 
Abbildung 16: Leitungsduplizierung einfacher Fall 
Die Reihenfolge der Trenner bei doppelter Duplizierung (OKZ sowohl an Start- als auch an End-Kontaktierung) 
folgt der alphanumerischen Sortierung der Referenzen von Start- und End-Kontaktierung). 


### 第 33 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 33 von 112 
 
 
Abbildung 17: Leitungsduplizierung komplexer Fall 
Es wird dabei empfohlen jedem OKZ einen internen Code mitzugeben, der als Zusatzbedingung an die Code 
der Leitung geschrieben wird, um bei der nachfolgenden Modularisierung eine eindeutige Steuerung zu 
ermöglichen. 
Die duplizierten Leitungen müssen auf ihre Sinnhaftigkeit geprüft werden. Nicht benötigte Duplikate müssen 
wieder entfernt werden bzw. dürfen nicht erzeugt werden. 
3.2.2 Leitungsduplizierung auf Grund topologischer Verschiebung 
In einzelnen Sonderfällen unterscheiden sich die Leitungssätze verschiedener Derivate oder Ausführungsarten 
nur in der Länge eines einzelnen Segmentes, sollen aber in einem gemeinsamen Master dargestellt werden. 
Dies wiederum hat Auswirkung auf die Länge der einzelnen Leitungen, die durch diese betroffenen Segmente 
laufen (bspw. Innenraum-Leitungssatz bei Baureihe 223 zwischen Derivaten FW, FV, FZ). 
3.2.2.1 Anforderungen 
Da im Schaltplan Leitungen auf Grund topologischer Varianz nicht mehr dupliziert werden, muss dies im 
Nachfolgeprozess 
geschehen. 
Hierzu 
muss 
entweder 
der 
Router 
innerhalb 
des 
Leitungsentwicklungswerkzeuges die Duplizierung vornehmen oder ein Vorprozess muss dies vor dem Import 
ins Entwicklungswerkzeug sicherstellen. 
3.2.2.2 Vorgaben für Leitungsduplizierung 
Unabhängig vom zugehörigen Derivat erhält die Leitungsnummer jeweils einen entsprechenden Suffix (vgl. AV 
Schaltplanerstellung). Dieser ist ein einfacher Zähler und entspricht jeweils einem zugehörigen Derivat (bspw. 
„1“ für Derivat FW, „2“ für Derivat FV). Als Trennzeichen wird ein „#“ verwendet. Bei 
Sonderleitungsbestandteilen steht dieser Trenner nach der Untergliederung der Bestandteile an der 
Leitungsnummer der Sonderleitung. 


### 第 34 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 34 von 112 
 
Die duplizierten Leitungen müssen auf ihre Sinnhaftigkeit geprüft werden. Nicht benötigte Duplikate müssen 
wieder entfernt werden bzw. dürfen nicht erzeugt werden. Jede Leitung muss durch ihr zugehöriges Segment 
geroutet werden. 
3.2.2.3 Zeichnungsdarstellung 
Die Segmente mit den unterschiedlichen Längen werden übereinander dargestellt (vgl. Abbildung 18). Über 
ein Kommentarfeld soll die Zuordnung zum entsprechenden Derivat gekennzeichnet sein. 
 
Abbildung 18: Darstellung Segmente bei Leitungsduplizierung 
In den Tabellen der Kontaktierungen wird lediglich einmal die Grundform dargestellt, hier tauchen die 
duplizierten Leitungen nicht auf. Lediglich auf den zugehörigen Verbindungslisten der Module werden die 
einzelnen Leitungen mit den entsprechenden Leitungsnummern und den entsprechenden Leitungslängen 
dargestellt. 
 
 


### 第 35 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 35 von 112 
 
4 Datenformate und Einheiten der Leitungssatzzeichnung  
4.1 Leitungslängen 
Längenmaße sind in Millimeter und ohne Nachkommastellen aufzuführen. 
 
4.2 Leitungssatz-Gewicht 
Gewichtsangaben in Kilogramm sind auf drei Nachkommastellen zu begrenzen.  
Das gewogene Gewicht ist beim Archivieren von Leitungssatzdaten im PDM-System Smaragd / Sm@web 
einzutragen. Liegt dieses nicht vor, so muss ein Prognosegewicht angegeben werden. Masterleitungssätze 
enthalten keine Gewichtsangabe. 
Die Prognose-Gewichtsangabe bei Leitungssatzmodulen ist auf der Zeichnung des Masterleitungssatzes 
enthalten und in der daraus abgeleiteten KBL einzutragen. 
Bei Leitungssatzmodulen, die nicht über einem Masterleitungssatz zusammengefasst sind, ist die 
Gewichtsangabe auf der Zeichnung im Zeichnungskopf und der daraus abgeleiteten KBL einzutragen 
(vergleiche Abbildung 9). 
 
4.3 Angabe des Datums 
Nach der DIN ISO 8601 „Datenelemente und Austauschformate“ ist der Datenstand auf der Zeichnung und 
im KBL im erweiterten Format vollständig darzustellen: 
        JJJJ-MM-TT 
z.B.: 2009-11-23 
 
4.4 Positionsnummern 
Die Positionsnummer besteht aus einer drei- bis vierstelligen Zahl und einem Kennbuchstaben für die 
Produktgruppe. Die Positionsnummer wird für alle beschränkt oder unbeschränkt freigegebenen 
Sachnummernteile von ConnectPARTS vergeben. 
Fehlende Positionsnummern müssen beim Teileverantwortlichen eingefordert werden. 
 
Nur Teile, die in ConnectPARTS dokumentiert sind, dürfen eine POS-Nr. haben.  
A-Sachnummer und N-Sachnummer benötigen zwingend eine POS-Nummer. 


### 第 36 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 36 von 112 
 
4.4.1 Format der Positionsnummer 
Die Notation der Positionsnummer besteht aus einer drei- bis vierstelligen Zahl und einem Kennbuchstaben 
für die Klassifizierung der Produktgruppe: 
P 
PKW 
G 
G Klasse 
Z 
Zusatzteile 
… 
Bsp.: P698 
 
Die Kennbuchstaben für die Produktgruppe sind in ConnectPARTS nach den Regeln der VA 998027 
festgelegt.  
4.4.2 Sonstige Positionsnummern 
Werden Neuteile verwendet, für die noch keine Positionsnummer vergeben wird, ist die vorübergehende 
Positionsnummer Z000 zu verwenden und später gegen die Positionsnummer aus ConnectPARTS 
auszutauschen. 
B8 Teile, die in ConnectPARTS keine Positionsnummer bekommen, sind mit der Positionsnummer Z005 zu 
kennzeichnen. 
 
Erläuterung der Dummy POS- Nummern: 
Z000 = Bauteil noch nicht festgelegt 
Z001 = Bauteil noch nicht festgelegt Bitte Rücksprache 
Z002 = Versuchsteil siehe Zeichnung 
Z003 = Leitungen werden bei Endmontage gesteckt 
Z004 = kein Einzelteil / im ZB enthalten 
Z005 = Bauteil ohne POS – Nr. siehe Zeichnung 
Z006 = Bauteile variabel (KSL) siehe Zeichnung 
Z007 = Bauteile, die ein Schlüsselwort tragen 
 
4.5 Teilkennzeichnung 
Jeder Einzelleitungssatz muss eine Kennzeichnung nach MBN 10435 an einer im eingebauten Zustand 
einsehbaren Stelle erhalten. Bei manchen kundenspezifischen Leitungssätzen ist dies nur begrenzt möglich! 
Hierfür ist dann ein „Sammeletikett“ für die Variante zu verwenden. 
 


### 第 37 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 37 von 112 
 
4.6 Allgemeine Anforderung Umweltschutz 
Auf allen Leitungssätzen ist die Norm DBL8585 in der Legende aufzuführen.  
Der Lieferant ist verpflichtet, zu jeder Zeit alle gültigen gesetzlichen Regelungen, die seine Lieferung oder 
Leistung betreffen, hinsichtlich Umweltschutz, Gefahrstoffrecht, Gefahrgutrecht, Arbeitssicherheit und 
Transport in aktueller Fassung zu berücksichtigen. Er hat die Umwelt-Leitlinien des Daimler-Konzerns zur 
Kenntnis genommen und orientiert sich bei seinen Tätigkeiten an deren Inhalten. 
 
 
 


### 第 38 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 38 von 112 
 
5 Leitungssatzkomponenten in der 2D Zeichnung 
Auf der Leitungssatz-Zeichnung sind alle stücklistenrelevanten Teile, die Bestandteil des angelieferten 
Leitungssatzes sind, zu dokumentieren. Dies bezieht sich explizit auch auf notwendige Unterwicklungen an 
Clipsen. 
5.1 Darstellung der Kontaktierungsteile 
Ein Beispiel, welches in den folgenden Abschnitten detailliert beschrieben wird: 
 
Abbildung 19: Beispiel-Darstellung Kontaktierungsteil auf Zeichnung 
5.1.1 Kontaktierungsteil-Symbol 
Die verwendete Kontaktierung ist in klassischer Form, bestehend aus Seitenansicht und Sicht auf die 
Kontakt-Bestückungsseite, entsprechend der Zeichnungs-Darstellung nach CAD-Handbuch CS080 
darzustellen.  
Die Kontaktierungsteilsymbole müssen mit folgenden Informationen beschriftet sein: 
 
Sachnummer und sofern vorhanden die Positionsnummer des Kontaktierungsteils 
 
Farbe und Codierung des Kontaktierungsteils 
 
Modulzugehörigkeit des Kontaktierungsteils 
 
Bauteilbezeichnung (REF) und Langbezeichnung (fett geschrieben) in Deutsch 
und darunter 
Bauteilbezeichnung (REF) und Langbezeichnung (fett geschrieben) in Englisch 


### 第 39 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 39 von 112 
 
5.1.2 Kontaktierungsteil-Tabelle 
Ein Grafiksymbol der Kontaktierung ist neben der Kontaktliste mit Sicht auf die Bestückungsseite des 
Kontaktierungsteiles zu platzieren. Die Symboldarstellung muss nicht maßstäblich sein, muss aber den 
Bezug auf die Nummerierung der Kontakte herstellen. 
Inhalte der Kontaktliste am Graphiksymbol: 
Textspalte 
Titel 
Inhalt 
1 
Pin 
Kontaktnummer des Steckers 
2 
Ltg.Nr. 
Leitungsnummer 
3 
Typ 
Ausführungsart der Leitung 
4 
qmm 
Querschnitt der Leitung 
5 
Farbe 
Farbkodierung der Leitung, Trennung durch „/“ 
6 
Mod 
optional nur bei Master 
7 
Kontakt  
Sachnummer des angeschlagenen Kontakts 
8 
KontaktMat 
Material Kontaktoberfläche 
9 
Dichtung 
Sachnummer der Einzeladerabdichtung 
Gehäusefarben sind in Deutsch und Kleinschrift anzugeben. 
Leere und geschlossene Kammern müssen im Tabellenfeld dargestellt werden (vergleiche Abbildung 19). 
5.1.3 Symbolbereitstellung 
Neue Symbole werden vom Lieferanten des Steckers oder im Rahmen des Komponentenmanagement aus 
CAD-Modellen generiert und im Format SVG in Smaragd bereitgestellt (siehe auch CAD Handbuch CS080). 
Über den Datenaustausch erhält der LS-Lieferant diese Symbole auf Anfrage. Vom LS-Lieferanten neu 
erstellte Symbole werden über den Datenaustausch der DAG zur Verfügung gestellt und wiederum zentral 
verteilt.  
5.1.4 Referenzbezeichnungen (REF) 
Die dargestellten Kontaktierungsteile werden mit einer eindeutigen Bauteilbezeichnung (REF) 
aus der Komponentendatenbank versehen. Fehlende Referenzen sind anzufordern bzw. für die jeweilige 
Baureihe freizuschalten. Die Anforderung wird entsprechend Ausführungsvorschrift A0070049999  über die 
Leitungssatz-Entwicklungsabteilung abgewickelt. 
Die Referenzen der Kontaktgehäuse (bspw. „N17/7*1-B“) müssen innerhalb eines Masters eindeutig sein. 
5.1.5 Aufbau der Referenz 
Die Referenzen und deren Aufbau kommen über die Schnittstelle ConnyE aus ConnectPARTS und werden über 
den Schaltplan in den Leitungssatz durchgeschleust. Auf der Leitungssatzzeichnung ist die Langbezeichnung 
der REF aus ConnectPARTS zu verwenden.  


### 第 40 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 40 von 112 
 
Hinweis: Die Referenz kann zusätzlich Informationen zu Anlage- und Ortskennzeichen tragen. Sollte kein 
Anlagekennzeichen benötigt werden/verfügbar sein, so wird direkt nach der Referenz das Ortskennzeichen 
aufgeführt (Detailinformationen dazu sind der aktuellen AV Schaltplan sowie AV Leitungssatz DMU zu 
entnehmen). In Ausnahmefällen kann es vorkommen, dass durch die Verwendung eines 600%-Masters in der 
150%-Zeichnung nur ein OKZ auftritt. Dies muss jedoch mit dem zuständigen BTV abgestimmt werden. 
5.1.6 Steckername 
Der Steckername besteht aus max. 6 Zeichen.  
Hinweis: In EPDM werden aktuell nur 3-stellige Steckernamen unterstützt.  
5.1.7 Kontaktform 
Die Kontaktform der Pins wird in EPDM bzw. im Schaltplantool festgelegt. 
5.1.8 Kabelschuh 
Für Rastkabelschuhe sind die einzuhaltenden Anordnungen der Rastkabelschuhe der Firma Delphi bzw. Stocko 
der entsprechenden Vorschrift von Delphi bzw. Stocko zu entnehmen.  
 
Kabelschuh M6: Tabellenzeichnung  A 000 982 67 02 (Delphi) bzw. A 172 982 13 02 (Stocko) 
 
Kabelschuh M8: Tabellenzeichnung  A 002 982 54 02 (Delphi) 
Weitere Details zur grafischen Abbildung sind Abschnitt 3.1.3 zu entnehmen. 
 
5.1.9 Darstellung von KSL-Steckern 
Für den Fall, dass  
 
an einem Steuergerät/Komponente elektrisch identische Kontaktgehäuse verwendet werden, die sich 
lediglich in ihrer Codierung auf Grund einer Varianz des Steuergerätes/Komponente unterscheiden,   
 
keine Duplizierung der Leitungen im Schaltplan erfolgen soll und  
 
diese einzelnen Kontaktgehäuse in separaten sogenannten Gehäusemodulen (Module, welche nur 
dieses Kontaktgehäuse als Teil besitzen) gesteuert werden, 
werden diese Kontaktgehäuse als KSL-Stecker abgebildet (Beispiel siehe Abbildung 20).  
 


### 第 41 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 41 von 112 
 
 
Abbildung 20: Beispiel eines KSL-Steckers 
Die verschiedenen Gehäusemodule werden in einer separaten Tabelle mit folgenden Mindestinformationen 
dargestellt: 
 
Codierung des Kontaktgehäuses 
 
Sachnummer des Kontaktgehäuses 
 
Abkürzung des zugehörigen Gehäusemoduls 
 
5.2 Dokumentation von Leitungssatz-Stützpunkten 
Stützpunkte dienen dazu mehrere elektrische Leitungen miteinander zu verbinden. Durchgangs-Stützpunkte 
werden zum Mittelpunkt bemaßt. Man unterscheidet dichte und undichte Stützpunkte.  
5.2.1 Gültige Varianten 
Die verschiedenen, gültigen Varianten und ihre AV-Sachnummern sind der TB AV A 009 000 38 99 zu 
entnehmen. 
5.2.2 Darstellung 
Die verschiedenen Stützpunkte sind wie folgt in der Zeichnung zu dokumentieren: 
 
Abbildung 21: Symbole Stützpunkte 


### 第 42 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 42 von 112 
 
Die einzelnen Stützpunkte werden mit weißer Farbe hinterlegt. End-Verbinder werden mittels Bundle-Point an 
der kurzen Kante eingebunden, Durchgangs-Verbinder werden leicht versetzt zum Bündel angebunden (siehe 
Abbildung 22). 
 
Abbildung 22: Anbindung von Durchgangs- und Endverbindern 
5.3 Schlüsselwörter 
Alle erlaubten Schlüsselwörter sind in der CONNECT Datenbank dokumentiert und werden täglich über den 
Lieferanten-Export verteilt. 
 
5.4 Leitungsschlaufen bzw. geändertes Routing 
Leitungsschlaufen bzw. ein geändertes Routing in der Leitungssatzzeichnung sind für sicherheits-relevante 
bzw. kundenspezifische Anforderungen sowie für notwendige Leitungsschlaufen zur Vollbelegung von 
Stützpunkten zulässig. Dazu wird ein Routingpoint der Leitung im Schaltplan hinzugefügt, siehe AV-
Schaltplanerstellung A0000069899. Dieses Vorgehen darf nicht bei produktionsrelevanten Verlegewegen des 
Leitungssatzlieferanten verwendet werden. Bei Ringstrukturen (Doppel-H) muss der exakte Verlegeweg 
manuell der Leitung zugewiesen werden, um die reale Leitungslänge im Leitungssatz (KBL) zu erhalten, d.h. 
auch hier darf kein Routingpoint verwendet werden. 
 
5.5 Lagedarstellung 
Der Leitungssatz auf der Zeichnung ist flach ausgebreitet dargestellt. Die Draufsicht der Zeichnung ist in der 
Überprüfungsebene dargestellt. Diese Überprüfungsebene entspricht einer gedachten Ebene bzw. eines 
ebenen Tisches, auf der der Leitungssatz torsionsfrei ausgebreitet und vermessen wird.  
 
Abbildung 23: Darstellung der Uhrzeit mit Definition der Überprüfungsebene 
Hinweis: Die Legende zur Definition von Uhrzeiten muss auf der Zeichnung vermerkt sein. 


### 第 43 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 43 von 112 
 
Der Ausgangspunkt zur Bestimmung der Lagedarstellung und Blickrichtung soll auf der X-Achse des Fahrzeugs 
(möglichst Hauptverlegewege) liegen. 12 Uhr ist an diesem Punkt in Z+ zu sehen. Leitungssätze, die im 
Fahrzeug die Richtung ändern, müssen auf der Überprüfungsebene in ausgestreckter Lage betrachtet werden.  
Der Ausgangspunkt muss in der Zeichnung durch das entsprechende Symbol gekennzeichnet werden (siehe 
Abbildung 24): 
 
Abbildung 24: Ausgangspunkt der Blickrichtung 
Die Blickrichtung ist auf der Zeichnung am Hauptstrang anzugeben und im Verlauf des Leitungsstranges ggf. 
zu wiederholen. Die Blickrichtung startet am Ausgangspunkt (s.o.) und entspricht in der Regel der 
Verlegerichtung, vom großen Bündel zum kleinen Bündel. Sie führt immer vom Stamm zu den Kontaktierungen 
(in der Regel weg von den Kabelkanälen). 
Hinweis: Zur besseren Übersichtlichkeit ist nach Rücksprache mit dem zuständigen BTV Leitungssatz eine 
Grobübersicht der Blickrichtung im Legendenbereich der Zeichnung anzugeben. 
 
5.6 Ausbindungsrichtung 
Ausbindungen sind in der Zeichnung lagerichtig darzustellen.  
Die Richtung der Darstellung einer Ausbindung auf der Zeichnung muss der Abgangsrichtung des 
Leitungssatzes entsprechen. Bei Abweichung der Darstellung zur Produktionsrichtung ist eine 
Hinweiszeichnung für die tatsächliche Abgangsrichtung zum Hauptstrang darzustellen.  
 
5.7 Befestigungselemente 
Nicht gekennzeichnete Befestigungselemente haben grundsätzlich die Lage 6 Uhr zum Leitungssatz und 
zeigen zur Überprüfungsebene. Weicht die Lage hiervon ab, ist dieses mit der Uhr zu kennzeichnen.  
 
5.7.1 Darstellung 
Die Befestigungsteilsymbole müssen mit folgenden Informationen beschriftet sein: 
 
Id des Befestigungselements (fett geschrieben) 
 
Bauteilbenennung des Befestigungselements (fett geschrieben) 
 
Sachnummer des Befestigungselements 
 
Modulzugehörigkeit des Befestigungselements 
Die grafische Darstellung der Befestigungselemente hat keine Aussage über die Lage. 


### 第 44 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 44 von 112 
 
 
Abbildung 25: Darstellung von Befestigungselementen 
In Abhängigkeit des Befestigungstyps sind unterschiedliche Ansichtsvarianten bei einer Doppelreferenzierung 
möglich. Diese lassen sich an den Stammdaten über das KBL-Attribut <Fixing_type> unterscheiden: 
 
Mehrmalige Darstellung:  
Bei Kabelkanälen und Tüllen existieren in der Regel mehrere  
  
 
 
Referenzen 
auf 
dasselbe 
Bauteil. 
Hier 
müssen 
die  
  
 
 
entsprechenden 
Eintrittsflächen 
am 
Eintrittspunkt 
dargestellt  
  
 
 
werden, auch wenn datentechnisch nur ein Bauteil vorhanden ist.  
  
 
 
(<Fixing_type> = CABLE_DUCT, GROMMET, Kabelkanal, Tülle). 
 
Einmalige Darstellung: 
Bei Doppelhaltern oder Kabelbindern, die mehrere Segmente  
  
 
 
verbinden, darf das Bauteil nur ein einziges Mal dargestellt  
  
 
 
werden. 
  
 
 
(<Fixing_type> != CABLE_DUCT, GROMMET, Kabelkanal, Tülle) 
5.7.2 Lage Befestigungselement zum Leitungssatz 
Die Lage des Befestigungselements zum Leitungssatz wird durch die Darstellung des Dreiecks mit Angabe der 
Uhrzeit definiert. 
Die Definition der Lage durch die grafische Darstellung des Befestigungsteils wird nicht berücksichtigt.  
 
 
Abbildung 26: Befestigungselement zeigt in Überprüfungsebene 9 Uhr, auch wenn grafisch anders dargestellt 
5.7.3 Lage Befestigungselement und Steckrichtung zum Leitungssatz 
Unterscheidet sich die Lage vom Befestigungselement zu der Steckrichtung des Befestigungselements zum 
Leitungssatz, so ist zusätzlich das Symbol Quadrat mit der Uhrzeit für die Steckrichtung darzustellen. 
 


### 第 45 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 45 von 112 
 
 
Abbildung 27: Befestigungselement liegt zur Montagebrettebene 9 Uhr, die Steckrichtung liegt auf 6 Uhr 
 
= Lage des Befestigungselements zum Leitungssatz 
 
 
 = Steckrichtung zum Leitungssatz 
 
Für die Sonderfälle einer parallel zum Leitungsstrang liegenden Steckrichtung (siehe Abbildung 28) sind die 
Symbole • für die Steckrichtung entgegen der Blickrichtung und x für die Steckrichtung in Blickrichtung zu 
verwenden. 
 
 = Steckrichtung entgegen der Blickrichtung zum Leitungsstrang 
 
      
= Steckrichtung in Blickrichtung zum Leitungsstrang 
 
 
Abbildung 28: Beispiel einer Steckrichtung parallel zum Leitungsstrang 
5.7.4 Lage Kabelbinderschloss zum Leitungssatz 
Werden zur Darstellung eines Kabelbinderschlosses mehr Informationen benötigt, so ist eine 
Hinweiszeichnung anzugeben. 
 
5.7.5 Benennung Befestigungspunkt in der Zeichnung 
Die Benennung von Befestigungspunkten in der Zeichnung muss folgendem Format entsprechen und eindeutig 
sein: 
Präfix „FX“ + Trenner + <Bauraumkürzel> + Trenner + <Zähler> 


### 第 46 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 46 von 112 
 
Trenner = “.“ 
Beispiel: FX.IR.1000 
Die Bezeichnung des Bauraumkürzels muss dabei den in CONNECT spezifizierten und in den Schaltplan 
übernommenen Verlegebereichen entsprechen. Sie werden bereits im DMU definiert.  
Diese Benennungen sind für Kabelkanäle und Tüllen nicht notwendig, diese tragen stattdessen eine 
Kombination aus Bauraumkürzel und Zähler. 
5.7.6 Bezeichnung Kabelkanal- & Tüllen-Ausgänge 
Die Ausgänge von Kabelkanälen und Tüllen werden mit den Bauraumkürzeln des Leitungssatzes, der 
Kabelkanal-Nummer, einem Unterstrich („_“) als Trenner sowie der laufenden Ausgangsnummer bezeichnet:  
<Bauraumkürzel> + <Kabelkanal-Zähler> + Trenner + <Kabelkanal-Ausgangszähler> 
Trenner = „_“ 
Beispiel: COC1_5 


### 第 47 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 47 von 112 
 
5.8 Darstellung Segmente 
Segmente ohne Isolierung werden als Einzellinie dargestellt. Der Wert des Bündeldurchmessers ist für die 
Zeichnung nicht relevant und darf nicht dargestellt werden. 
5.8.1 Darstellung Segmente mit Isolierung 
Bei Segmenten mit Isolierung wird die entsprechende Darstellung angezeigt. 
 
DE 
EN 
Symboldarstellung 
Geflechtschlauch 
Braided sleeve 
 
Glasseidenschlauch 
Glass silk sleeve 
Schlauch allg. 
Sleeve 
Sparbandagierung 
Spiral space tape 
Vollbandagierung 
Full space tape 
Längsbandagierung 
Longitudinal space tape 
Wellrohre 
Corrugated tube 
 
Als Isolierungskennzeichnung sind die CAD-Kennzeichen aus der Komponentendatenbank zu verwenden und 
am jeweiligen Segment anzugeben.  
Die Spezifikation der Isolierung wird in einem Sechseck (z.B.: 
) dargestellt. 
Hinweis: Es soll immer möglichst der dünnste (am engsten anliegende) Schlauch verwendet werden. 
5.8.2 Darstellung nicht angeschlagene Schirme 
Geschirmte Leitungen, bei denen der Schirm nicht am Kontaktgehäuse angeschlagen wird, werden in der 
Zeichnung durch ein Segment dargestellt. Siehe hierzu auch die AV Schaltplanerstellung A0000069899, 
Kapitel Darstellung von nicht angeschlagenen Schirmen in Schaltplänen. 


### 第 48 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 48 von 112 
 
 
Abbildung 29: Darstellung von nicht-angeschlagenen Schirmen 
5.9 Dokumentation anderer Leitungssatzumfänge 
5.9.1 Längswasserdichtheit 
Die Längswasserdichtheit von Tüllen ist analog den Vorgaben des KLHs sicherzustellen. Zur Dokumentation 
auf der Zeichnung muss dazu die allgemeine Art der Dichtung herangezogen werden, beispielweise die 
Sachnummer des Butyls, nicht jedoch die konkrete Ausprägung inklusive Streifengröße. 
5.9.2 Sicherungen, Relais 
Sicherungen und Relais sind als Zubehörteile direkt unterhalb der Kontaktierungsteil-Tabelle auf der Zeichnung 
aufzuführen. 
 
 
 
 
 
 
 
 
 
Abbildung 30: Dokumentation Zubehörteil - Beispiel Sicherung 
 
Die Überschrift der Tabelle setzt sich aus dem Präfix „Zubehörteile zu“, der Bauteilbezeichnung (REF) und 
Langbezeichnung (fett geschrieben) zusammen. 
Die Tabelle selbst ist nach folgendem Format aufgebaut: 


### 第 49 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 49 von 112 
 
 
Benennung (Sicherung/Relais) 
 
Farbe 
 
Pin (bei Relais ggf. leer) 
 
Steckplatz 
 
Kundenteilenummer 
 
Modul 
 
5.9.3 Zusatzteile 
Zusatz- und Anbauteile sind mit den folgenden Informationen auf der Zeichnung darzustellen: 
 
Bauteilbenennung des Elements 
 
Sachnummer des Elements 
 
Modulzugehörigkeit des Elements 
5.9.4 Etiketten und Markierungen 
Der Bezugspunkt für die Bemaßung von Etiketten und Markierungen ist auf deren Anfangspunkt zu legen. Es 
gilt die Allgemeintoleranz. 
Folgende Etiketten können für den Leitungssatz relevant sein: 
 
KZ-Etikett: 
Bei Einzel- und Stufenleitungssätzen mit der konkreten Sachnummer der bestellten  
  
 
Stufe. 
 
KSL-Etikett: 
Bei KSL-Leitungssätzen mit den Sachnummern aller enthaltenen /bestellten   
  
 
Module. 
 
Prüf-Etikett: 
Zusätzliches Etikett bei allen Leitungssätzen, welches 100% elektrische  
  
 
Prüfung nachweist. 
 
 
 


### 第 50 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 50 von 112 
 
5.10 Bemaßungen 
Die Bemaßung erfolgt grundsätzlich in Blickrichtung. 
5.10.1 Bemaßung von Segmenten 
Ein Segment wird von Anfangs- bis Endknoten bemaßt. Die Bemaßung muss je Segment dargestellt werden. 
Bezugspunkt für die Bemaßung von Ausbindungen ist die neutrale Faser des Leitungsbündels. 
5.10.2 Bemaßung von Leitungsschutz-Elementen 
In vielen Fällen wird die Isolierung des Leitungsbündels nicht bis zum Steckereingang ausgeführt und endet 
30 mm vor dem Steckereingang. In diesem Fall kann die Vermaßung entfallen. Ein entsprechender Hinweis 
muss in der Legende der Zeichnung vermerkt sein. 
Abweichungen von dieser Regel müssen separat vermaßt werden. 
5.10.3 Bemaßung von Kabelschuhen 
Für die Bemaßung von Kabelschuhen wird ein Referenz- bzw. Übermaß verwendet. Dieses wird für gerade, 
gewinkelte Kabelschuhe und Sonderformen angewandt. Die Bemaßung wird vom Referenzpunkt bis zur 
Lochmitte des Kabelschuhs definiert (siehe auch Kapitel 5.10.6).  
Für die Bemaßung gelten die in Kapitel 5.11.1 beschriebenen Toleranzen. Weitere Besonderheiten wie 
beispielsweise Verarbeitungshinweise sind den Zeichnungen zu entnehmen und sind bei der Vermessung zu 
berücksichtigen. 
******** Bemaßung von sternförmigen Massestellen 
Bei sternförmigen Massestellen, bei denen in der Zeichnung nur eine Referenz als Potential dargestellt wird 
(vgl. Abschnitt 3.1), wird nicht die direkte Leitungslänge bemaßt, sondern die imaginäre Linie zur Lochmitte 
des Kabelschuhs (siehe auch Abschnitt 6.3.1). 
Die Überlängen bei zwei und mehr Kabelschuhen sind selbstständig zu ermitteln. Der Längenzuschlag soll vom 
Kabelschuh her kommend in den Hauptstrang zurückgeführt werden. Dort soll dieser sauber eingelegt oder 
eingebunden werden. 
 
Abbildung 31: Bemaßung von sternförmigen Massestellen 


### 第 51 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 51 von 112 
 
5.10.3.2 Bemaßung gewinkelter Kabelschuhe und Sonderformen 
Bei gewinkelten Kabelschuhen wird vom Referenzpunkt zur Maßhilfslinie gemessen. Diese Maßhilfslinie 
verläuft senkrecht (90°) zur Crimpachse und durch die Lochmitten-/Anschraubebene des Kabelschuhs. Das 
Maß läuft dabei entlang der ausgestreckten Leitung (siehe auch Abschnitt 6.1). 
 
Abbildung 32: Bemaßung von Kabelschuhen mit Referenzmaß 
Auf Grund des Längentoleranzausgleichs muss das "letzte" Segmentmaß vor dem Kabelschuh in Klammern 
geschrieben bzw. ausgeblendet werden. 
5.10.3.3 Lage gedrehter Kabelschuhe 
Für Leitungssätze mit größeren Querschnitten (ab 16 mm²), bei kurzen Längen (z.B. < 450 mm) oder kurzen 
Abständen zur letzten Ausbindung/Befestigung (z.B. < 250 mm) kann die Orientierung und Ausrichtung 
einzelner Kabelschuhe zueinander wichtig sein. Diese wird sofern notwendig nach Abstimmung zwischen BTV 
und Lieferant über Uhrzeiten in Bezug zur Lage des Kabelschuh-Loches beschrieben. 
Die Maßhilfslinie (die senkrechte Linie von Crimpachse zur Lochmitte) gibt die Lage (Uhrzeit) des Kabelschuhs 
wieder. Es werden Lagerichtung und Steckrichtung angegeben. 
Liegt die globale Sichtendefinition auf einem Kabelschuh (keine Ausbindungen, Befestigungselemente 
vorhanden), wird dieser Ausgangskabelschuh mit einer Ausgangsuhrzeit als Startpunkt gewählt. Die Lage des 
gegenüberliegenden Kabelschuhs wird dann relativ zu diesem angegeben. 
5.10.4 Bemaßung Befestigungselement 
Neben der Lagedarstellung muss auch die genaue Position eines Befestigungselementes auf der Zeichnung 
festgehalten werden. Diese ist von einem prominenten Bezugspunkt (z.B. Kontaktgehäuse, Ausbindung, etc.) 
ausgehend mit Hilfe eines geeigneten Maßes anzugeben. 
 
Abbildung 33: Beispielbemaßung Befestigungselement 


### 第 52 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 52 von 112 
 
5.10.5 Bemaßung von Kontaktgehäusen 
Bei Kontaktgehäusen dient allgemein die Gehäusekante am Leitungsabgang als Maßbezugskante. Sind 
richtungsweisende Kappen verbaut, so wird deren Kante als Maßbezugskante genutzt.  
Bei Sonderfällen wie sich in der Höhe unterscheidender Gehäusekanten ist ein Hinweistext mit den sich 
unterscheidenden Leitungslängen einzubringen. Bei Kontaktgehäusen mit variabler Abgangsrichtung ist ein 
Hinweistext zum Leitungsabgang zu ergänzen (siehe Abbildung 48). 
5.10.6 Referenzpunktbemaßung 
Toleranzeinschränkungen 
sowie 
Bemaßungen 
entgegen 
der 
Blickrichtung 
müssen 
über 
eine 
Referenzbemaßung unter Angabe der Toleranz eingebracht werden. Sollten innerhalb des Referenzmaßes die 
Allgemeintoleranzen 
gültig 
bleiben, 
dürfen 
diese 
nicht 
zusätzlich 
angegeben 
werden. 
Referenzpunktbemaßungen müssen zwischen Lieferant und DAG Baureihen-Team abgestimmt werden.  
Die Benennung des Referenzpunktes muss folgendem Format entsprechen: 
 
Abbildung 34: Benennung Referenzpunkt 
Der Referenzpunkt muss benannt und durch Maßlinie oder Maßpfeil gekennzeichnet sein (siehe beispielhaft 
Abbildung 32 und Abbildung 35). 
 
Abbildung 35: Referenzpunktbemaßung bei Befestigungselement 
 
 


### 第 53 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 53 von 112 
 
5.11 Toleranzen 
5.11.1 Toleranzen für Segmente 
Die Allgemeintoleranz gilt für Leitungslängen je Segment. Die Zwischenbemaßung von Segmenten durch das 
Anbringen von Befestigungsteilen hat keine Auswirkung auf die Allgemeintoleranz je Segment.  
Diese Standardtoleranzen sind zusätzlich im Datenformat KBL zu hinterlegen. 
Die Legende zur Allgemeintoleranz (siehe Abbildung 36) muss auf der Zeichnung vermerkt sein. 
 
 
Abbildung 36: Legende Allgemeintoleranz (DE/EN) 
5.11.2 Toleranzen für Referenzpunktbemaßung von Segmenten 
Die Toleranz eines Segments von Ausbindung bis Ausbindung/Ende ist die Allgemeintoleranz.  
Beim Bemaßen mehrerer Segmente bezogen auf einen Referenz-Punkt darf keine Verkürzung des Nennmaßes 
für eines der Segmente entstehen. 
5.11.3 Toleranzen für Befestigungselemente 
Bei Kettenbemaßung beträgt die Toleranz zwischen zwei Befestigungselementen +5mm (Befestigungselement-
Variante beachten). Die maximal zulässige Toleranz eines Segmentes beschränkt die Summe der Toleranzen 
aller auf diesem Segment angebrachten Befestigungselemente. 
Die Legende zur Toleranz für Befestigungselemente (siehe Abbildung 37) muss auf der Zeichnung vermerkt 
sein. 
 
 
Abbildung 37: Toleranz für Befestigungselemente (DE/EN) 
Sind abweichende Toleranzen nötig, so werden diese in Abstimmung mit dem DAG Baureihen-Team separat 
definiert.  


### 第 54 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 54 von 112 
 
5.11.4 Toleranzen für Tüllen 
Für Tüllen gilt die Allgemeintoleranz bezogen auf die Einknüpfebene.  
 
 
Abbildung 38: Einknüpfebene einer Tülle 
Sind abweichende Toleranzen oder Bemaßungspunkte nötig, so werden diese in Abstimmung mit dem DAG 
Baureihen-Team separat definiert.  
 
 
Einknüpfebene


### 第 55 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 55 von 112 
 
5.12 Verwendung von Tabellenzeichnungs-Sachnummern 
In Ausnahmefällen kann es notwendig sein, statt einer konkreten Teilesachnummer nur eine Variantenvorgabe 
durch die Dokumentation einer Tabellenzeichnungs-Sachnummer zu treffen.  
Dies ist in den in diesem Abschnitt dokumentierten Fällen zulässig. Die entsprechenden Tabellenzeichnungs-
Sachnummern sind mit dem Maximalgewicht zu dokumentieren. 
5.12.1 Dokumentation von Massestellen 
Bei einigen Massestellen wird lediglich die Tabellenzeichnungs-Sachnummer eines ausgewählten Kabelschuhs 
dokumentiert. Die genauen Vorgaben sind Kapitel 3.1 zu entnehmen. 
5.12.2 Schläuche in Bündeln mit variablem Durchmesser 
In Bündeln, bei denen auf Grund unterschiedlicher Modulzuordnungen der enthaltenen Leitungen der 
Durchmesser des Bündels variiert, muss der Außendurchmesser des Schlauches variieren. Da in diesem Fall 
keine eindeutige Teilenummern-Zuordnung möglich ist, wird lediglich die Tabellenzeichnung der zu 
verwendenden Schlauchfamilie dokumentiert. In der konkreten Verbauung soll in Rücksprache mit dem 
jeweiligen BTV der am engsten anliegende Schlauch der Familie verwendet werden.  
 
 


### 第 56 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 56 von 112 
 
6 Überprüfung der Leitungssatzmaße 
Für die einheitliche Sicherstellung der Maßangaben auf der Leitungssatzzeichnung ist für kritische Punkte eine 
Messdokumentation zum Leitungssatz-Produkt zu erstellen. (Messspezifikation der Baureihe) 
Die Bemaßungsmethoden dieser Messspezifikation dürfen dabei jedoch nicht den in dieser 
Ausführungsvorschrift beschriebenen Regeln widersprechen. 
6.1 Blick- und Lagerichtungen 
Leitungssätze werden grundsätzlich auf einer ebenen Fläche bzw. einem Tisch vermessen – der sogenannten 
Überprüfungsebene. Der Leitungssatz muss dabei torsionsfrei ausgebreitet sein. Leitungssätze, die im 
Fahrzeug ihre Richtung ändern, müssen auf der Überprüfungsebene in ausgestreckter Lage betrachtet werden. 
Die Uhrzeiten der Befestigungselemente müssen dazu ab Ausbindung in Blickrichtung gesehen in Relation zur 
Überprüfungsebene positioniert sein. 
6.2 Referenz- / Maßbezugspunkte 
Die Überprüfung der Maße erfolgt immer von einem Referenz- bzw. Maßbezugspunkt aus.  
An Kabelkanälen sind diese Maßbezugspunkte in der Regel durch eine oder mehrere Kerben gekennzeichnet. 
 
Abbildung 39: Maßbezugspunkt am Kabelkanal 
Die Bezugspunkte an Tüllen sind in der Regel mit einem eingespritzten Pfeil gekennzeichnet. Sollte kein Pfeil 
vorhanden sein, gilt die Einknüpfebene als Bezugspunkt. 


### 第 57 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 57 von 112 
 
 
Abbildung 40: Tülle mit eingespritztem Pfeil als Maßbezugspunkt 
6.3 Vermessung von Kabelschuhen 
Alle Kabelschuhe werden bis Lochmitte bzw. Schraubmittelpunkt vermessen.  
6.3.1 Vermessung von Kabelschuhsternen 
Bei sternförmigen Massestellen (Kabelschuhsterne) wird nicht die direkte Leitungslänge bemaßt, sondern die 
imaginäre Linie zur Lochmitte des Kabelschuhs.  
 
Abbildung 41: Vermessung von Kabelschuhsternen 
6.3.2 Vermessung von gewinkelten Kabelschuhen und Sonderformen 
Bei gewinkelten Kabelschuhen oder Sonderformen wird dabei bis zur Maßhilfslinie gemessen, welche 90° zur 
Crimpachse liegt und durch die Lochmitten-/Anschraubebene geht. 
 


### 第 58 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 58 von 112 
 
 
Abbildung 42: Allgemeine Vermessung Kabelschuhe 
 
Abbildung 43: Vermessung gewinkelter Kabelschuhe 
 
Abbildung 44: Vermessung von Kabelschuhen mit Anschraubpunkt 
 
6.4 Vermessung von Kontaktgehäusen 
Bei Kontaktgehäusen dient allgemein die Gehäusekante am Leitungsabgang als Maßbezugskante. Sind 
richtungsweisende Kappen verbaut, so wird deren Kante als Maßbezugskante genutzt.  


### 第 59 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 59 von 112 
 
 
Abbildung 45: Vermessung von Kontaktgehäusen 
 
 
Abbildung 46: Vermessung von HSD-/FAKRA-Winkelstecker 
 
Abbildung 47: Vermessung Spezial-Kontaktierung 
Bei Sonderfällen wie sich in der Höhe unterscheidender Gehäusekanten ist ein Hinweistext mit den sich 
unterscheidenden Leitungslängen einzubringen. Bei Kontaktgehäusen mit variabler Abgangsrichtung ist ein 
Hinweistext zum Leitungsabgang zu ergänzen. 
 
Abbildung 48: Zusatzinformationen bei Sonderfällen 


### 第 60 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 60 von 112 
 
7 Datenbereitstellungsprozess 
Es gelten die allgemeinen von Daimler vorgegebenen Richtlinien zur Datensicherheit und zur 
Datenbereitstellung bzw. zum Datenaustausch. 
Für die Datenbereitstellung in Smaragd (z.B. über Sm@Web) ist die Archivierungsrichtlinie Smaragd A0598004 
zu beachten. Es ist untersagt Entwicklungsdaten per Email zu versenden! Leitungssatzdaten sind 
ausschließlich über Smaragd bereitzustellen. 
7.1 Leitungssatzdaten 
Folgende Systeme werden bei der Datenbereitstellung der Leitungssatzdaten aktiv verwendet: 
 
Connect – (HARNESS, PARTS und CHANGE) 
 
ACM 
 
E3.Cable (ConnyE) 
 
Service – Dokumentation  
 
Smaragd bzw. Sm@Web 
 
EPDM 
 
DIALOG 
 
ZGDOK 
 
Siemens NX 
 
Leitungssatzentwicklungstool (kein vorgeschriebenes System) 
 
 
Abbildung 49: Daten/Tools 
Im folgenden Bild erkennt man die Toolabbildung von den Grundprozessen der Leitungssatzentwicklung:  


### 第 61 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 61 von 112 
 
- 
Component Management 
- 
Elektrologik Erstellung 
- 
3D Leitungssatzverlegung 
- 
2D Zeichnungserstellung  
- 
Offizielle Freigabe und Dokumentation 
- 
Change Management Prozess 
 
 
Abbildung 50: Abbildung Prozesse auf Toollandschaft 
Die Zeitpunkte der Datenbereitstellung sind mit dem Baureihenprojekt und dem verantwortlichen 
Sachbearbeiter abzustimmen. 
Aus ConnectPARTS kommen verschieden Datenpakete, die dann an die Folgeprozesse automatisch versorgt 
werden:  
- 
VZK Katalog (xSeparated Textfile, beinhaltet den Verwendungszweck-Katalog) 
- 
CLS Katalog (Library für LCable)  
- 
Teilekatalog 
- 
XML Teilekatalog 
- 
Verwendungskatalog 
- 
Schlüsselwortwerte 
 
 
7.2 Schaltplandaten 
Die Schaltpläne und Schaltplanoriginaldaten sind Eigentum der Daimler AG (MBC/D) und für den internen 


### 第 62 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 62 von 112 
 
Gebrauch entsprechend MBN 31 002 auszuführen. Zeichnungsrahmen und Schriftköpfe, Bibliotheken, 
Template-Dateien für Schriften und Ebenen, Bauteil-, Referenzen-, Code- und Leitungsdaten sowie die  
notwendigen Zusatzfunktionen der Software werden von RD/UBL zur Verfügung gestellt und müssen 
verbindlich verwendet werden. Als Schaltplaneditor ist E3.Cable, nachfolgend auch Schaltplaneditor genannt, 
mit dem Plug-In ConnyE zum Datenaustausch mit CONNECT zu verwenden.  
 
Die erste Strukturebene der Schaltpläne, System/ Funktionsbezug sind fest vorgegeben. Die zweite 
Strukturebene und der Sachnummernkreis usw. werden inhaltlich vom Leitungssatz-Baureihenteam 
vorgegeben bzw. muss vor Projektstart abgesprochen und festgelegt werden. 
 
Die Inhalte der Schaltpläne sind im Kapitel Schaltplaninhalte in der AV Schaltplanerstellung A0000069899 
beschrieben. 
Firmenspezifische Informationen sind nur in den vereinbarten und abgestimmten Attributen gestattet oder vor 
der Bereitstellung zur MBC/D zu entfernen. 
 
 


### 第 63 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 63 von 112 
 
8 Datenformate in der Leitungssatzentwicklung 
8.1 Einzelleitungs-, Modul- und Masterleitungssatz 
8.1.1 Einzelleitungssatz 
Bei einem Einzelleitungssatz handelt es sich um ein bestellbares Teil aus einem Baukasten mit mindestens 
einer elektrischen Leitung und Anbauteilen. Er wird als Einzelteil auf einer eigenen Zeichnung nach MBN 10317 
dokumentiert werden. 
 
Abbildung 51: Strukturierung KBL bei Einzelleitungssätzen 
8.1.2 Modulleitungssatz 
Bei einem Modulleitungssatz handelt es sich um ein bestellbares Teil aus einem Baukasten mit mindestens 
einer elektrischen Leitung und Anbauteilen. Er wird als Einzelteil nicht auf einer eigenen Zeichnung, sondern 
auf einer Tabellenzeichnung nach MBN 10317 dokumentiert werden. 
 
8.1.3 Masterleitungssatz 
Bei einem Masterleitungssatz handelt sich um eine Zeichnung vom Typ Tabelle mit mehreren bestellbaren 
Zusammenbauteilen vom Typ Modulleitungssatz (kZ-Teil). In der Regel handelt es sich hier um einen 
Bauraumleitungssatz mit mehreren dargestellten Varianten und / oder optionalen Add-On Leitungssätzen.  


### 第 64 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 64 von 112 
 
Der Masterleitungssatz bildet die Grundlage des Kundenspezifischen Leitungssatzes (KSL). Dieser enthält ein 
Grundmodul und kann mit den Varianten, optionalen Add-On Leitungssätzen, zu einem auftragsbezogenen, 
kundenspezifischen Leitungssatz zusammengestellt und fertig montiert werden. Ein Masterleitungssatz 
besteht i.d.R. somit aus einem Grundmodul und Modulleitungssätzen. Eine HCV bzw. KBL-Datei eines Masters 
beinhaltet immer alle zu diesem Master gehörenden Modulleitungssätze. 
Die Benennung eines Masterleitungssatzes beginnt mit TB ZB Elektr. Leitungssatz. Für jeden Modulleitungssatz 
wird zusätzlich eine laufende Variantennummer "(Bsp. V1, ...)" vergeben.  
Die Zeichnung muss einer Tabellenzeichnung nach MBN 10317 entsprechen. 
 
 
Abbildung 52: Strukturierung KBL bei Masterleitungssätzen 
Eine Tabellenzeichnung hat eine eigene Sachnummer, hinter der sich kein bestellbares Teil verbirgt. 
Bestellbare Teile sind mit ihrer Sachnummer auf der Tabellenzeichnung dargestellt. 
Übertragen auf die KBL stellt die Tabelle den Harness-Container dar, in der bestellbare Teile mit ihrer 
Sachnummer als Modul abgebildet sind. 
8.2 Namenskonvention für KBL- und HCV-Dateien 
Syntax:  
<MBC Sachnummer>_<ZGS>_<Datenstand>.kbl 
<MBC Sachnummer>_<ZGS>_<Datenstand>.hcv 


### 第 65 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 65 von 112 
 
 
Beispiele: 
ZGS = 001 
MBC Sachnummer = A1665406503 
Datenstand = 20131121 
Dateiendung für KBL Dateien = kbl 
 
Der Dateinamen setzt sich aus der Sachnummer ohne Leerzeichen/Sonderzeichen, dem ZGS (3stellig), dem 
Datenstand im Basisformat (JJJJMMTT) und der Dateiendung kbl/hcv zusammen. Als Trenner ist ein Unterstrich 
zu verwenden. Bei Masterleitungssätzen entspricht die MBC-Sachnummer der Masterleitungssatznummer/ 
Sachnummer der Tabellenzeichnung. 
 
Beispiele: 
A1665406503_001_20131121.kbl 
A1725408709_002_20100630.kbl 
A2225403908_012_20131203.hcv 
 
8.3 KBL (Kabelbaumliste) 
Die KBL ist ein elektronisches Abbild der Leitungssatzzeichnung und beschreibt Leitungen, Verbindungen, 
Einzelteile, die Leitungssatztopologie mit ihren Abzweigen und Segmenten, die theoretischen und die 
physikalischen Längen der Leitungen sowie die Quelldokumente (Schaltplan und 3D-DMU-Modelle), die 
diesem Leitungssatz zu Grunde liegen.  
Die KBL wird aus dem Entwicklungswerkzeug generiert und muss den Leitungssatz (topologisch und 
elektrisch) auch ohne Zeichnung vollständig dokumentieren sowie die zur Erstellung verwendeten 
Quelldokumente wie Schaltplan- und DMU-Topologie-Daten dokumentieren und referenzieren.  
Die KBL muss dem Standard ISO 10303-212 und dem XML Schema der vereinbarten KBL- Version 
entsprechen. Es sind die im Schema vereinbarten Zeichensatzdeklarationen zu verwenden. 
 
Die Daimler-Vorgaben zum Datenformat KBL sind der Ausführungsvorschrift Datenformat KBL (A 006 006 46 
99) in der jeweilig gültigen Version zu entnehmen. 
8.4 Digitale Netzliste 
Schaltplandaten sind nach der AV Schaltplanerstellung zu erstellen und in der Abstimmung mit den 
Baureihenprojekten in das DV System ConnectHARNESS abzuliefern. Diese bestehen aus einer PDF-Datei und 
einer von MBC geforderten digitalen Netzliste (generischer Export). 
 


### 第 66 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 66 von 112 
 
8.5 TIFF 
Die Zeichnungsdaten werden im Hause Daimler AG in den elektronischen Freigabeprozess über Smaragd 
eingespeist und für die Langzeitarchivierung in das System ZGDOK eingestellt.  
Die Daten sind entsprechend der Anforderungen des Dokumentationssystems ZGDOK zu erstellen und vor 
dem ersten Einsatz bei MBC zu qualifizieren. 
 
Anforderungen des ZGDOK: 
Tagged Image File Format G4 
Anordnung der Pixel: 
Striped (Streifenformat) 
Farbtiefe: 
 
 
1 Bit 
Auflösung: 
 
 
200 bis 400 DPI (dots per inch) 
Komprimierung:  
 
CCITT Gruppe 4 
 
8.6 HCV 
8.6.1 Aufbau des HCV-Datencontainer 
Der Harness Container for Viewing dient zum Transport aller notwendigen Dateien, die einen gesamten 
Leitungssatz inklusive der verschiedenen Varianten bzw. Module beschreiben. Es können aber auch einzelne 
Leitungssatzmodule damit transportiert werden. 
Er wird hauptsächlich zur Masterfreigabe verwendet, ist aber auch in vielen anderen Einsatzgebieten der 
Entwicklung oder Kalkulation, sowie des Musterbaus gut einsetzbar. Da sowohl die grafische Repräsentation 
der Leitungssatzzeichnung als auch alle technischen Informationen darin enthalten sind, können die meisten 
Fragestellungen bezüglich des Produktes selber als auch der Fertigung beantwortet werden. 
Das Softwareprodukt E3.HarnessAnalyzer kann diese HCV Container lesen und deren Inhalt entsprechend 
visualisieren, vergleichen oder auch analysieren. 
Ein HCV ist ein komprimierter (ZIP) Datencontainer, bestehend aus: 
 
genau einer KBL, die den gesamten Leitungssatz mit den Varianten und Modulen beinhaltet 
 
einer oder mehrerer SVG Dateien, die die grafische Repräsentation der Leitungssatzzeichnung auf 
gegebenenfalls mehreren Seiten darstellt. Die Namen der Dateien sollten den Inhalt kennzeichnen, 
damit man sie im Dokumentenbaum leichter wiederfinden kann, und müssen die Endung .svg haben. 
(Bsp.: Topologie.svg, Deckblatt.svg, Stückliste.svg…) 
 
genau einer Index-XML Datei, die eine Modulstückliste repräsentiert 
 
anderer optionaler Dateien, wie z.B. 3D Konturmodelle des Leitungssatzes 
 
optionaler „Redlining“ Informationen, die der E3.HarnessAnalyzer dort einstellen kann  
 


### 第 67 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 67 von 112 
 
Die Komprimierung muss nach ZIP Standard erfolgen, es werden keine RAR/TAR oder GZIP Archive 
unterstützt. 
Abhängig von Prozessanforderungen können in einem HCV Container auch weitere Dateien (z.B. HP-GL/2, 
TIFF, PDF) enthalten sein. Ein HCV Viewer muss die im Mindestumfang enthaltenen Dateien (KBL, SVG(s), 
Index XML) für das Viewing berücksichtigen. Die Zusätzlich enthaltenen Dateien können vom Viewing System 
berücksichtigt und entsprechend angeboten/ausgewertet werden. 
 
 
Abbildung 53: schematischer Aufbau eines HCV-Containers 
8.6.2 KBL 
Ein HCV Container muss genau eine KBL Datei beinhalten. Die KBL Datei muss nach dem KBL Schema 2.4 
(siehe Kapitel 0) aufgebaut sein. 
 
8.6.3 Index.xml 
Die Index XML ist eine Stückliste des Leitungssatzes mit entsprechender Zuordnung der Teile zu den jeweiligen 
Modulen. Sie wird in verschiedenen Prozessabläufen der Freigabe oder Auswertung benötigt.  
Damit man diese einfache Basisinformation nicht jedes Mal aus der relativ komplexen Datenstruktur der KBL 
auslesen muss, wird die Datei aus dem Datenmodell des jeweiligen Autorensystems bei der Erstellung des 
HCV Containers erzeugt, oder aber von anderen Systemen generiert und in den Container eingestellt. 
 
Die Daten der Index.xml müssen mit den Daten der KBL-Datei in Version und Inhalt übereinstimmen: 
 


### 第 68 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 68 von 112 
 
 
Abbildung 54: Darstellung Verbindung Index.xml und Harness.kbl 
Der E3.HarnessAnalyzer kann diese Datei, sofern vorhanden, lesen und deren Inhalt visualisieren. Die Funktion 
ist unter dem Menüpunkt BOM (Active Modules / All Modules) zu finden. 
 
Alle Attribute bzw. Attributknoten müssen geschrieben werden (die min und max Occurence sind in der 
entsprechenden Schemadatei auf 1 gesetzt) 
Das XML Schema der Index XML gliedert  sich in drei Teilbereiche: 
 
1. Die Metadaten des Harness-Containers: 
 
ID <string>: 
Identifier des Containers oder der Datei. Dieser wird zu Zeit nicht weiter ausgewertet und ist nur 
vorgehalten. 
 „id_08_15“ 
 
creationTimestamp<string>: 
Zeitstempel der Erstellung der Index XML. Hier wird aus Gründen der Kompatibilität zurzeit ein String 
Datentyp verwendet, wobei das Format YYYY-MM-DD HH:MM:SS vorzusehen ist: 


### 第 69 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 69 von 112 
 
„2014-06-01 20:07:34“ 
 
creatingSystem<string>: 
Name des Autorensystems von dem die Index XML generiert worden ist. 
 „HarnessDesigner 1.5“ 
 
supplier<string>: 
Lieferantenname aus dem CONNECT-Teilekatalog, der die Index XML generiert hat. 
„Automotive Systems“ 
 
harnessDrawing<string>: 
Sachnummer des Harness Masters. Die Sachnummer ist ohne Leerzeichen oder sonstige 
Sonderzeichen zu schreiben. 
„A2225403908“ 
 
2. Die Harness-Module: 
In diesem Abschnitt werden alle Module des Harness Masters aufgeführt. 
 
partNumber<string>: 
Sachnummer des Moduls. Die Sachnummer ist ohne Leerzeichen oder sonstige Sonderzeichen zu 
schreiben. 
„A2225404108“ 
 
version<string>: 
Datenstand des Moduls. Hier kann eine Versionsinformation eingetragen werden. Im Daimler Umfeld 
ist die Datumsinformation des Tagesstandes im Format YYYY-MM-DD zu verwenden. 
„2011-11-07“ 


### 第 70 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 70 von 112 
 
 
description<string>: 
Beschreibung bzw. Benennung des Moduls. 
„ZB EL.LTG.SATZ LL COCKPIT RDU“ 
 
code<string>: 
Logistik-Kontrollinformation in Form eines Optionscodes. 
„+LL+TY0+889“ 
 
kzflag<boolean> („keine Zeichnung Flag“): 
Wenn das Modul keine eigene Zeichnung hat, wird das Flag auf true gesetzt. Bei Masterumfängen ist 
das Flag automatisch auf true zu setzen, da alle Module als („kZ-siehe“) abgebildet sind. 
Mit folgender Vorschrift lässt sich das kzflag ermitteln: 
Wenn der Eintrag der harnessDrawing in der Sektion harnessContainer leer ist, wird das kzflag auf 
false gesetzt. Anderenfalls ist dieser Eintrag mit dem Eintrag der partNumber unter der Sektion 
harnessModule zu vergleichen (Case Insensitive). Bei Ungleichheit ist das Flag auf true zu setzten, 
sonst verbleibt es false. 
 
3. Die zu einem Modul gehörigen Teile: 
 
 
 
partNumber<string>: 
Sachnummer des Teils. Die Sachnummer ist ohne Leerzeichen oder sonstige Sonderzeichen zu 
schreiben. 
„A2129820126“ 
 


### 第 71 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 71 von 112 
 
drawing<string>: 
Zeichnungsnummer, auf der das Teil dargestellt ist. 
„A2129820126“ 
 
 
supplier<string>: 
Lieferantenname des Teils oder entsprechendes Kurzzeichen aus ConnectPARTS. 
 „Coroplast“ 
 
occurences<integer>: 
Anzahl der Teile. Im Falle von Bandierung wird hier die Anzahl der bandierten Segmente  angegeben. 
„5“ 
 
description<string>: 
Benennung des Teils. (auf der Zeichnung/Schriftfeld wird die SRM-Benennung dargestellt, siehe Norm 
MBN 31 020-1) 
„Buchsenkontakt (gerade) Au 0.14-0.14“ 
 
kZflag<boolean>(„keine Zeichnung Flag“): 
Einzelteile habe in der Regel keine eigene Zeichnung, daher ist das Flag hier immer auf true zu setzen 
(-> siehe Tabellenzeichnung). 
Ausschnitt aus einer Index XML: 
 
Abbildung 55: Ausschnitt einer Index-XML 
8.6.4 SVG 
Ein HCV Container muss mindestens eine SVG enthalten. Bei Autorensystemen mit Mehrblattfähigkeit soll für 
jedes Blatt eine einzelne SVG ausgeleitet und in den Container eingebracht werden. Die Dateinamen der SVGs 
entsprechen hierbei der Bezeichnung des jeweiligen Blattes (z.B. „Seite 1“, „Stücklisten“) mit der Dateiendung 


### 第 72 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 72 von 112 
 
„.svg“. Die SVG Dateien enthalten die grafische Repräsentation der Leitungssatzzeichnung, wie sie dem 
Anwender im Autorensystem dargestellt wird. 
 
Dieser Stand der Spezifikation beschreibt die Strukturen, die der E3.HarnessAnalyzer in der Version 6.1 
unterstützt. Als Referenz für Fragen der Darstellung kann der Microsoft Internet Explorer in der Version 9 
verwendet werden. 
Es sind in zukünftigen Versionen einige Änderungen nötig und sinnvoll, um besonders im Bereich der SVG 
Modellierung mehr Flexibilität und Dateneffizienz zu ermöglichen. In weiteren Entwicklungsschritten sollen die 
zusammengehörigen Leitungssätze eines gesamten Fahrzeuges mit in die Visualisierung einbezogen werden, 
um auch die Übergänge und Trennstellen zwischen den Leitungssätzen darstellen zu können. Damit steigen 
die Anforderungen an die zu verarbeitenden Datenmengen und Dateigrößen nochmals deutlich an. Daher ist 
bei der Generierung des HCV Containers und besonders der SVG Daten auf schlanke Strukturen und 
effiziente Darstellung zu achten. 
 
Die SVG orientiert sich an dem W3C Standard (http://www.w3.org/TR/SVG11), aber es werden in der 
aktuellen Version nur bestimmte Elemente unterstützt, und innerhalb des jeweiligen Elementes sind nur 
bestimmte Spezifizierer bzw. Attribute zugelassen. Diese werden in den nachfolgenden Abschnitten behandelt. 
 
Auch in zukünftigen Versionen werden bestimmte Elemente nicht unterstützt werden. Hierzu gehört in jedem 
Falle das „ClipPath“ Element. „ClipPathes“ werden bei der Verarbeitung der SVG und der Darstellung ignoriert. 
 
SVG Befehle und Attribute, die nicht unterstützt werden, dürfen trotzdem in der SVG vorkommen, sie werden 
vom Browser einfach ignoriert. 
 
In einer SVG werden zu Beginn die Breite und Höhe sowie die Größe der Viewbox angegeben. Damit die SVG 
z.B. auch im Internet Explorer oder anderen Programmen für sich alleine darstellbar ist, müssen diese Werte 
entsprechend bei der Generierung beschrieben werden. Der EE Browser verwendet diese Information nicht, 
er ermittelt die Koordinaten der umgebenden Begrenzung selbst und alle Anzeigen basieren auf dem World 
Koordinatensystem (WCS). Die Verwendung von User-Koordinatensystemen und verschiedenen Einheiten wird 
nicht unterstützt. 
 
Die Hintergrundfarbe der Dokumente ist fest auf weiß eingestellt und kann nicht vorgegeben werden. 
******* Unterstützte SVG Elemente 
Die grafischen Elemente und Gruppen können innerhalb des SVG Dokumentknotens in beliebiger Reihenfolge 
angeordnet werden, wobei eine strukturierte Auflistung die Lesbarkeit deutlich vereinfacht. 


### 第 73 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 73 von 112 
 
 
Gruppe 
<g transform(translate,rotate,scale)/> 
 
Das Gruppenelement wird zur Gruppierung von zusammengehörigen Basiselementen benötigt. Gruppen 
können in beliebiger Tiefe geschachtelt werden. Dieses Element wird für alle Zusammenfassungen von 
Business Objekten innerhalb des Browsers gebraucht. Z.B. Steckertabellen, Steckeransichten usw. 
Beispiel: 
<g> 
    <g transform="translate(8976.7,46.8) rotate(-0.97)"> 
      <line x1="4" y1="0" x2="117" y2="0" style="stroke:black; stroke-width:0.5"/> 
      <polygon points="0 0 ,4 -1 ,4 1" style="fill:black" /> 
    </g> 
 </g> 
 
Linie 
<line x1,y1,x2,y2,style(stroke;stroke-width)/>  
 
x1,y1: Startkoordinaten im WCS 
x2,y2: Endkoordinaten im WCS 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der Farbangabe ist sowohl die 
Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig.  
 
 
Beispiel: 
<line x1="25" y1="10" x2="25" y2="15" style="stroke:black; stroke-width:0.5 " /> 
 
 
 
 
Oder  
 
<line x1="25" y1="10" x2="25" y2="15" style="stroke: rgb(100,113,207); stroke-width:0.5 " /> 
 
 Polylinie 
<polyline points(list of points) style(fill; opacity; stroke; stroke-width) /> 
 
points: eine Liste von XY Paaren im WCS 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der Farbangabe ist sowohl die 
Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig. Das fill Attribut legt die 
Füllfarbe fest oder steht auf none. Ein opacity Wert von 0 setzt die Füllung auf „ungefüllt“.  


### 第 74 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 74 von 112 
 
 
Beispiel: 
<polyline points="8.453 -20.554,11.034 -8.973 ,8.41 -8.322 ,8.215 -9.189 " style="stroke:black; stroke-width:0.1; 
fill:none" /> 
 
 Polygon 
< polygon points(list of points) style(fill; opacity; stroke; stroke-width) /> 
 
points: eine Liste von XY Paaren 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der Farbangabe ist sowohl die 
Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig. Das fill Attribut legt die 
Füllfarbe fest oder steht auf none. Ein opacity Wert von 0 setzt die Füllung auf „ungefüllt“.  
 
Beispiel: 
<polygon points="-0.462 3.5 ,1.813 1.225 ,13.187 1.225" style="stroke:rgb(78,113,207); stroke-width:0.5; fill:none" /> 
 
 Path 
<path d(A, a, C, c, Q, q, S, s, T, t, H, h, L, l, M, m V, v) style(fill, opacity, stroke, stroke-width) /> 
 
Angaben in Großbuchstaben kennzeichnen absolute Koordinaten, mit Kleinbuchstaben sind relative 
Koordinaten bezeichnet. 
 
 
A, a: Arc-Definition 
 
C, c, Q, q, S, s, T, t: Curve-Definition 
 
H, h: Horizontal-Line-Definition  
 
L, l: Line-Definition 
 
M, m: MoveTo-Definition 
 
V, v: Vertical-Line-Definition 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der Farbangabe ist 
sowohl die Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig. Das 
fill Attribut legt die Füllfarbe fest oder steht auf none. Ein opacity Wert von 0 setzt die Füllung auf 
„ungefüllt“.  
 
Beispiel: 
<path d="M-8 -4 A17 17,0,0,1,8 -4" style="stroke:black; stroke-width:0.25; fill:none" /> 
 
 Circle 


### 第 75 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 75 von 112 
 
<circle cx,cy,r,style(fill, opacity, stroke, stroke-width)/> 
 
cx,cy: Ursprungskoordinaten in WCS 
r: Radius 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der Farbangabe ist sowohl die 
Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig. Das fill Attribut legt die 
Füllfarbe fest oder steht auf none. Ein opacity Wert von 0 setzt die Füllung auf „ungefüllt“.  
 
Beispiel: 
<circle cx="6545" cy="393" r="0.5" style="fill:rgb(78,113,207)" /> 
 
 Ellipse 
<ellipse cx,cy,rx,ry,style(fill, opacity, stroke, stroke-width)/> 
 
cx,cy: Ursprungskoordinaten in WCS 
rx,ry: X- und Y-Achsen Radius 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der Farbangabe ist sowohl die 
Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig. Das fill Attribut legt die 
Füllfarbe fest oder steht auf none. Ein opacity Wert von 0 setzt die Füllung auf „ungefüllt“.  
 
Beispiel: 
< ellipse cx="6545" cy="393" rx="0.5" ry="1" style=" fill:none  stroke:blue  stroke-width:20/> 
 
 Rect 
<rect x,y,width,height,style(fill, opacity, stroke, stroke-width) /> 
 
x,y: Ursprungskoordinaten in WCS 
width: Breite 
height: Höhe 
style: Es werden die stroke Attribute für Farbe und Linienbreite unterstützt. Bei der Farbangabe ist sowohl die 
Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein RGB Wert zulässig. Das fill Attribut legt die 
Füllfarbe fest oder steht auf none. Ein opacity Wert von 0 setzt die Füllung auf „ungefüllt“. 
Die Werte Rx und Ry für gerundete Ecken werden in der aktuellen Version nicht unterstützt. 
 
Beispiel: 
<rect x="0" y="0" width="15" height="7" style="fill:rgb(255,255,255); opacity:0" /> 
 
 Text 


### 第 76 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 76 von 112 
 
<text x,y, font-weight, style (fill, font-family, font-size, text-anchor, text-decoration)>Value</> 
 
x,y: Ursprungskoordinaten in WCS, eine Liste von Punkten wird hier nicht unterstützt 
font-weight: Bei diesem Attribut wird nur der Wert “bold” verwendet.  
 
style: Mit dem Attribut text-anchor kann die horizontale Ausrichtung angegeben werden. Die font-family 
erlaubt die Angabe des Fonts. Font-Verweise sind nicht möglich. Dieser muss auf dem Zielsystem verfügbar 
sein, ansonsten wird auf einen Default zurückgegriffen. Über das Attribut fill kann die Farbe des Textes gesetzt 
werden. Bei der Farbangabe ist sowohl die Verwendung des Farbnamens (siehe W3C Spezifikation) sowie ein 
RGB Wert zulässig. Mit der text-decoration können Unter-, Über-, und Durchstreichungen erstellt werden. Mit 
der font-size kann die Größe eingestellt werden. 
 
Beispiel: 
<text x="375" y="13.8"  font-weight="bold"  style="text-anchor:middle; font-family:Arial; font-size:3px; fill:black; 
text-decoration:underline">Dies ist ein Text</text> 
 Tspan 
<tspan x,y,font-weight,style(text-anchor, text-decoration)>Value</ tspan > 
 
x,y: Ursprungskoordinaten in WCS, eine Liste von Punkten wird hier nicht unterstützt  
font-weight: Bei diesem Attribut wird nur der Wert “bold” verwendet.  
 
style: Mit dem Attribut text-anchor kann die horizontale Ausrichtung angegeben werden. Mit der text-
decoration können Unter-, Über-, und Durchstreichungen erstellt werden. 
 
Der Tspan wird nur in Verbindung mit einem Text Element verwendet. Es werden die Angaben für die Größe, 
Farbe, Font vom Text Element übernommen. 
Beispiel: 
<text x="0" y="10" style="text-anchor:middle; font-family:Arial; font-size:12px; fill:black">X30/20<tspan x="0" 
y="3.214" style="text-anchor:middle">nach elektrischer Prüfung</tspan><tspan x="0" y="17.013" style="text-
anchor:middle">alle Abgriffe in CAN-Verteiler stecken</tspan></text> 
8.6.4.2 Transformationen 
 
 Matrix 
<matrix(a,b,c,d,e,f)> 
 
Es werden die Matrix Transformationen unterstützt. Dabei können die Matrixelemente 


### 第 77 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 77 von 112 
 
A00:a 
A01:c 
A10:b 
A11:d 
A03:e 
A13:f 
verwendet werden (siehe auch W3C). 
 
Anordnung der Matrix Elemente in der SVG Transformationsmatrix: 
 
 
 
Beispiel: 
<g transform="matrix(-1,0,0,-1,-4.4,39.6)"> 
 
 Rotate 
<rotate(angle)> 
Der Rotationswinkel ist in Grad anzugeben! 
 
Beispiel: 
<g transform="rotate(-90)" /> 
 
 
 


### 第 78 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 78 von 112 
 
 
Scale 
<scale(x,y)> 
Beispiel: 
<g transform="scale(1,2)"> 
 
 Translate 
<translate(x,y)> 
 
Beispiel: 
<g transform="translate(0,1990) rotate(-90)" /> 
 
Hinweis: die Skew Kommandos werden nicht unterstützt. 
******* Wichtige Hinweise 
Grundsätzlich gibt es zwei Möglichkeiten Texte in einer SVG zu transportieren. Zum einen können SVG Text 
Elemente verwendet werden (siehe oben), zum anderen können Texte als gefüllte Polygone gewandelt werden. 
Viele SVG Konverter nutzen die zweitgenannte Möglichkeit, da man hier auf dem Zielrechner keine Information 
über den verwendeten Font benötigt. In typische CAD Zeichnungen lässt sich dieser Ansatz prinzipiell auch 
verwenden, da man hier nur wenige Texte hat. 
  
Für den Einsatz im HCV Container ist nur der erstgenannte Weg über die Verwendung von Text Elementen 
sinnvoll, da die SVG Dateigröße sonst viel zu groß wird, speziell bei entsprechenden Innenraumleitungssätzen! 
 
Weiterhin sollte darauf geachtet werden, nicht unnötig viele Koordinatensätze zu produzieren. Dies passiert 
häufig bei der Darstellung von z.B. Steckerbildern, die aus 3D Modellen mittels bestimmter Konverter in 2D 
überführt werden. Hier werden dann oft unzählige Polylinien mit vielen Vertexes generiert, die für die grafische 
Darstellung eigentlich völlig überflüssig sind, in ihrer Anzahl aber auch signifikant zum Speicherverbrauch 
beitragen. 
 
Die Verwendung von Pattern-Definitionen und Dash-Arrays wird in der aktuellen Version noch nicht 
unterstützt, da die rückwärtige Umsetzung von dieser SVG Informationen innerhalb des Browser nicht zu der 
gleichen Darstellung führt. 
 
Textpathes (Texte, die beliebigen Verläufen folgen) werden nicht unterstützt. 
8.6.4.4 Verlinkung zwischen SVG und KBL 
Um die Navigation zwischen Grafik und physikalischen Elementen zu ermöglichen, wird von der SVG aus 


### 第 79 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 79 von 112 
 
innerhalb der Gruppenelemente auf Elemente der KBL über den internen KBL-Identifier (id) mittels definierter 
Kommentare verwiesen. 
Die in der KBL vergebenen Identifier für die verschieden Objekte sind nach folgendem Muster aufgebaut. 
Details zu den KBL vergebenen Identifier können im Kapitel Fehler! Verweisquelle konnte nicht gefunden 
erden. nachgelesen werden. 
 
Diese Vorgehensweise ist bei Daimler zu verwenden, da eine SVG Schemaerweiterung einen entsprechenden 
Aufwand auch in der Verwaltung und Bereitstellung des Schemas bedeutet hätte. Zudem ist diese Erweiterung 
einfacher in vorhandene Autorensysteme einzubringen. 
 
Ist das erste Element innerhalb einer Gruppe ein solcher Kommentar <!--kbl-id:xyz ……-->, so wird er 
entsprechend ausgewertet; anderenfalls dient das Gruppenelement rein der Strukturierung. 
 
Dieser Kommentar enthält ein oder mehrere Identifier von KBL Elementen, die zu dieser Gruppe gehören. 
Mehrere Identifier sind durch Leerzeichen zu separieren. 
Die damit beschriebene Verlinkung kann entweder eine „ist“ (z.B. Steckersymbol) oder eine „gehört zu“ (z.B. 
Steckertabelle) Beziehung darstellen. Im zweiten Fall kann der Kommentar um einen zusätzlichen Typ-
Spezifizierer (siehe folgender Abschnitt) erweitert werden, der es dem Browser ermöglicht, die verschiedenen 
Repräsentationen eines physikalischen Objektes gegebenenfalls auch unterscheiden zu können. 
Ein Steckersymbol (face view) wird z.B. wie folgt gekennzeichnet: 
 
<g> 
   <!--kbl-id:ID_CON167--> 
   …Grafikelemente des Steckersymbols… 
</g> 
8.6.4.5 Typ-Spezifizierer 
Folgende Typ-Spezifizierer sind bisher vereinbart: 
 
type:ref 
Der type:ref  bezeichnet ein allgemein referenzierendes Objekt ohne weitere Präzisierung. Es wird verwendet, 
wenn z.B. spezielle Zusatzgrafik wie Bezugspfeile oder Textfahnen mit Objekten verbunden werden soll. 
Elemente vom Typ ref werden nicht in die Selektion genommen und auch bei bestimmten Ansichten, wie z.B. 
der Start-End Connector Ansicht nicht dargestellt. 
 
<g> 
<!--kbl-id:ID_CON1;type:ref-->  


### 第 80 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 80 von 112 
 
…Irgendwelche grafischen Elemente zum Stecker … 
</g> 
 
type:table 
Der type:table bezeichnet die Repräsentation von Tabellen als Ganzes (z.B. Steckertabelle). Innerhalb dieser 
Struktur können Zeilen und Zellen angegeben werden. 
 
<g> 
<!--kbl-id:ID_CON1;type:table-->  
…Inhalte und grafische Elemente der Tabelle … 
</g> 
 
type:row 
Der Type:row bezeichnet die Repräsentation einer inhaltlichen Zeile (nicht Überschrift) innerhalb einer Tabelle. 
 
<g> 
<!--kbl-id:ID_CON1;type:table-->  
…Inhalte und grafische Elemente der Tabelle … 
<g>  
<!--kbl-id:ID_W1;type:row-->  
…Inhalt der Tabellenzeile mit einem Bezug zur Leitung ID_W1… 
</g>  
</g> 
 
type:cell 
Der type:cell bezeichnet die Repräsentation einer Zelle innerhalb einer Zeile in der Tabelle. 
 


### 第 81 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 81 von 112 
 
<g> 
<!--kbl-id:ID_CON1;type:table-->  
 …Inhalte und grafische Elemente der Tabelle … 
<g>  
<!--kbl-id:ID_W1;type:row-->  
…Inhalt der Tabellenzeile mit einem Bezug zur Leitung ID_W1… 
<g>  
<!--kbl-id:ID_SEA1;type:cell-->  
…Inhalt der Tabellenzelle mit einem Bezug zur Einzeldichtung SEA1…  
</g> 
</g>  
</g> 
 
type:dimension 
Der type:dimension  bezeichnet die Repräsentation eines Bemaßungsobjektes. Bemaßungsobjekte verweisen 
in der Regel auf die beiden Vertex-Identifier des entsprechenden Segments, für das die Bemaßung gilt (BNJ-
BundleJunction). Im nachfolgenden Beispiel sind der Bemaßungstext und der Maßpfeil voneinander getrennt 
geschrieben worden. Das ist nicht zwingend nötig, findet sich aber in einigen Beispieldateien; im dritten 
Gruppenabschnitt ist das zusammengefasst worden. 
Beispiel: 
Bemaßungstext 
<g> 
<!--kbl-id:ID_BNJ1 ID_BNJ109;type:dimension--> 
<g transform="translate(6739.5,3) rotate(0) translate(0,-10)"> 
<text 
x="0" 
y="1.3" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
text-
decoration:underline; fill:black">50</text> 
</g> 
</g> 
 
 


### 第 82 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 82 von 112 
 
Maßpfeil 
<g> 
<!--kbl-id:ID_BNJ1 ID_BNJ109;type:dimension--> 
<g transform="translate(6739.5,37) rotate(0)"> 
<line x1="-68.5" y1="-1" x2="-68" y2="-11" style="stroke:black; stroke-width:0.25" /> 
 <polygon points="-68.5 -10 ,-64 -11 ,-64.5 -9" style="fill:black" /> 
 <line x1="-3.5" y1="-10" x2="-64" y2="-10" style="stroke:black; stroke-width:0.25" /> 
 <line x1="68.5" y1="-1" x2="68" y2="-11" style="stroke:black; stroke-width:0.25 " /> 
 <polygon points="68.5 -10 ,64 -11 ,64.5 -9" style="fill:black" /> 
 <line x1="3.5" y1="-10" x2="64" y2="-10" style="stroke:black; stroke-width:0.25" /> 
 </g> 
</g> 
 
 
Zusammengefasst: 
<g> 
 <!--kbl-id:ID_BNJ1 ID_BNJ109;type:dimension--> 
 <g transform="translate(6739.5,3) rotate(0) translate(0,-10)"> 
<text 
x="0" 
y="1.3" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
text-
decoration:underline; fill:black">50</text> 
</g> 
<g transform="translate(6739.5,377) rotate(0)"> 
<line x1="-68.5" y1="-1" x2="-68" y2="-11" style="stroke:black; stroke-width:0.25" /> 
<polygon points="-68.5 -10 ,-64 -11 ,-64.5 -9" style="fill:black" /> 
<line x1="-3.5" y1="-10" x2="-64" y2="-10" style="stroke:black; stroke-width:0.25" /> 
<line x1="68.5" y1="-1" x2="68" y2="-11" style="stroke:black; stroke-width:0.25 " /> 
<polygon points="68.5 -10 ,64.5 -11 ,64.5 -9" style="fill:black" /> 
<line x1="3.5" y1="-10" x2="64" y2="-10" style="stroke:black; stroke-width:0.25" /> 
</g> 
</g> 
 
 
 
 
 
 
type:DocumentFrame 


### 第 83 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 83 von 112 
 
Der type:DocumentFrame  bezeichnet die Gruppe des Blattrahmens. In dieser Gruppe sind alle grafischen 
Objekte unterzubringen, die für den Blattrahmen benötigt werden (Planquadratangaben usw.) 
<g> 
<!--kbl-id:DocumentFrame--> 
<rect x="0" y="0" width="10289" height="841" style="fill:rgb(255,255,255)" /> 
<rect x="0" y="0" width="10289" height="841" style="stroke:black; stroke-width:0.01; fill:none" /> 
<rect x="20" y="10" width="10259" height="821" style="stroke:black; stroke-width:0.5; fill:none" /> 
…. 
</g> 
 
8.6.4.6 Objekte und ihre Darstellung 
 
 Steckersymbol 
Das Steckersymbol verweist auf das entsprechende Connector Objekt in der KBL. Hier ist es in der Gruppe 
der zugehörigen Vertex angelegt, sodass bei Selektion der Vertex das Symbol automatisch mit in die Selektion 
genommen wird. Die Vertex selbst hat keine Grafik - sie ist durch das Steckersymbol repräsentiert – hier ist 
nur der Verweis auf den zugehörigen node in der KBL eingetragen. (Kapitel Vertex). 
 
 
 
 
 
 
1 


### 第 84 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 84 von 112 
 
SVG Teilausschnitt: 
<g> 
<!--kbl-id:ID_BNJ79--> 
<g> 
<!--kbl-id:ID_CON54--> 
<g transform="translate(3757.08,212.09) rotate(257.26) translate(-18.54,-2.1)"> 
<g transform="translate(0,0) scale(1,1) translate(18,25)"> 
<g transform="matrix(0,1,-1,0,-17.599,-25.599)"> 
<text 
x="4" 
y="-21.599" 
style="text-anchor:start; 
font-family:Arial; 
font-size:2.74px; 
fill:black">10</text>  
    …und weitere Kammertexte… 
</g> 
 
<line x1="-3" y1="21" x2="-3" y2="19" style="stroke:black; stroke-width:0.25 " /> 
  …und weitere Grafik… 
</g> 
</g> 
</g> 
</g> 
 
 
 Steckertabelle 
Die Steckertabelle verweist auf das entsprechende Connector Objekt in der KBL. Sie wird über die Typ-Angabe 
spezifiziert. Zu Beginn werden die grafischen Elemente des Tabellenkopfes beschrieben. Nachfolgend kommt 
die Angabe der Spaltenüberschriften. 
Im Anschluss daran werden die einzelnen Zeilen jeweils als eine Gruppe geschrieben und auf das zugehörige 
Wire Objekt der KBL verwiesen, wobei diese Verweise vom Typ row zu setzen sind. Innerhalb einer Zeile werden 
Untergruppen verwendet, um die Zellinhalte für z.B. Kontaktsachnummer zu spezifizieren. Diese Untergruppen 
verweisen auf das Termination Objekt in der KBL und müssen auf den Typ cell gesetzt  werden. Im 
nachfolgenden Beispiel sind die Kontaktsachnummer und das Kontaktmaterial in einer solchen Untergruppe 
und können daher entsprechend ein- und ausgeblendet werden. 
Die Zellränder und Füllungen sind hier als gefüllte Rechtecke ausgeführt. Dabei ist zuerst der Zellhintergrund 
getrennt von der Umrandung angegeben. Idealerweise setzt man besser die Zellfüllung auf ungefüllt und 
beschreibt den Hintergrund der Zelle gar nicht weiter, denn der Rendering Prozess macht in bestimmten 
Ansichten aus explizit beschriebenen Farben einen grauen Hintergrund. 
1 
Vertex des Symbols 


### 第 85 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 85 von 112 
 
 
 
 
SVG Teilausschnitt: 
 
 
<g> 
    <!--kbl-id:ID_CON54;type:table--> 
    <g transform="translate(3810.0,149.09) rotate(0)"> 
      <g> 
        <rect x="0" y="0" width="33" height="6" style="fill:rgb(255,255,255); opacity:0" /> 
<rect x="0" y="0" width="33" height="6" style="stroke:rgb(255,255,255); opacity:0; stroke-width:0.25; 
fill:none" /> 
<text 
x="0.833" 
y="4.332" 
style="text-anchor:start; 
font-family:CorpoS; 
font-size:5px; 
fill:black">Kundenteilenr.:</text>   
…. 
      </g> 
 
      <g> 
 <rect x="0" y="25" width="8" height="6" style="stroke:black; stroke-width:0.25; fill:none" /> 
<text 
x="4" 
y="29.332" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
1 
2 
3 
5 
4 
6 
1 
Hintergrund und Zelle getrennt 
 Zelle mit „transparentem“ Hintergrund 


### 第 86 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 86 von 112 
 
fill:black">Pin</text>  
      … 
        <g> 
<!--kbl-id:ID_WIR1239;type:row--> 
<rect x="0" y="31" width="8" height="6" style="fill:rgb(255,255,255)" /> 
<rect x="0" y="31" width="8" height="6" style="stroke:black; stroke-width:0.25; fill:none" /> 
<text 
x="4" 
y="35.332" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
fill:black">1</text> 
 
<rect x="8" y="31" width="18" height="6" style="fill:rgb(255,255,255)" /> 
<rect x="8" y="31" width="18" height="6" style="stroke:black; stroke-width:0.25; fill:none"/> 
<text 
x="17" 
y="35.332" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
fill:black">57</text> 
         ….. 
          <g> 
<!--kbl-id:ID_TER1837;type:cell--> 
<rect x="83" y="31" width="32" height="6" style="fill:rgb(255,255,255)" /> 
<rect x="83" y="31" width="32" height="6" style="stroke:black; stroke-width:0.25; fill:none" /> 
<text 
x="99" 
y="35.332" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
fill:black">A0055457626</text> 
          </g> 
          <g> 
            <!--kbl-id:ID_TER1837;type:cell--> 
            <rect x="115" y="31" width="27" height="6" style="fill:rgb(255,255,255)" /> 
<rect x="115" y="31" width="27" height="6" style="stroke:black; stroke-width:0.25; fill:none" /> 
<text 
x="128.5" 
y="35.332" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
fill:black">Sn</text>  
          </g> 
        </g> 
      </g> 
    </g> 
</g> 
 
Zubehörteile 
Zubehörteile zum Stecker werden meist unterhalb der Steckertabellen dargestellt. Sie sind innerhalb der 
Gruppe der Steckertabelle angeordnet. Diese Teile verweisen in der Regel auf Accessory Objekte der KBL. 
Manchmal wird auch auf ein Component Objekt verwiesen (meist bei Sicherungen, wie hier im Beispiel). 
2 
3 
5 
4 
6 


### 第 87 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 87 von 112 
 
Im Grunde handelt es sich bei dieser Darstellung wieder um eine Tabelle, und die einzelnen Zeilen werden 
wieder entsprechend über den Typ als row gekennzeichnet. 
 
SVG Teilausschnitt: 
<g> 
   <!--kbl-id:ID_CON132;type:table--> 
…..Hier ist der gesamte Block der eigentlichen Steckertabelle zu finden (siehe auch Abschnitt Steckertabelle)… 
<g> 
<rect x="0" y="75" width="35" height="7" style="stroke:rgb(255,255,255); opacity:0; stroke-width:0.25; 
fill:none" /> 
<text x="0.833" y="79.756" font-weight="bold" style="text-anchor:start; font-family:CorpoS; font-size:5px; 
fill:black">Zubehörteile zu K40/6*S1-B SRB li.</text>  
 
<rect x="0" y="82" width="35" height="6" style="stroke:rgb(255,255,255); opacity:0; stroke-width:0.25; 
fill:none" /> 
<rect x="35" y="82" width="13" height="6" style="stroke:rgb(255,255,255); opacity:0; stroke-width:0.25; 
fill:none" /> 
<text 
x="35.833" 
y="86.332" 
style="text-anchor:start; 
font-family:CorpoS; 
font-size:5px; 
fill:black">Farbe</text> 
….. 
       <g> 
         <!--kbl-id:ID_ACC54;type:row--> 
<rect x="0" y="88" width="35" height="6" style="stroke:rgb(255,255,255); opacity:0; stroke-width:0.25; 
fill:none" /> 
<text 
x="0.833" 
y="92.5" 
style="text-anchor:start; 
font-family:CorpoS; 
font-size:5px; 
1 
2 
1 


### 第 88 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 88 von 112 
 
fill:black">Sicherung 5 A</text>    
…… 
       </g> 
    </g> 
</g> 
 
Zugehörige Beschreibung in der KBL bei Verweisen auf eine Komponente: 
<Component_occurrence id="ID_ACC54"> 
     <Id>ID_ACC54</Id> 
     <Mounting>ID_CON132 id_370_774</Mounting> 
     <Part>id_99902_1</Part> 
</Component_occurrence> 
 
<Component id="id_99902_1"> 
   <Part_number>N000000004202</Part_number> 
…. 
   <Version>20110329-ST30611</Version> 
   <Abbreviation>P0026</Abbreviation> 
   <Description>Sicherung 5 A</Description> 
    
   <Processing_information id="id_336_1"> 
     <Instruction_type>Type</Instruction_type> 
     <Instruction_value>Fuse</Instruction_value> 
   </Processing_information> 
 </Component> 
 
Zugehörige Beschreibung in der KBL bei Verweisen auf ein Accessory Objekt: 
<Accessory_occurrence id="ID_ACC65"> 
     <Id>ID_ACC65</Id> 
     <Placement id="id_345_197"> 
… 
     </Placement> 
     <Part>id_300_5</Part> 
     <Reference_element>ID_CON18</Reference_element> 
     <Installation_information id="id_329_1027"> 
       <Instruction_type>Colour</Instruction_type> 
2 


### 第 89 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 89 von 112 
 
       <Instruction_value>violett</Instruction_value> 
     </Installation_information> 
   </Accessory_occurrence> 
 
Bezugspfeile zur Tabelle: 
Der Bezugspfeil wird hier quasi wie ein Stück der Tabelle gesehen (type:table) und verweist dementsprechend 
auch wieder auf das Connector Objekt in der KBL. Dieser Bezugspfeil könnte prinzipiell auch direkt mit in die 
Definition der Steckertabelle aufgenommen werden. 
 
SVG Teilausschnitt: 
<g> 
 <!--kbl-id:ID_CON54;type:table--> 
 <g transform="translate(3757.0,212.0) rotate(19.7)"> 
 <line x1="4" y1="0" x2="56.303" y2="0" style="stroke:black; stroke-width:0.5" /> 
 <polygon points="0 0 ,4 -1 ,4 1" style="fill:black" /> 
 </g> 
 </g> 
 
Hinweistexte 
Hinweise zu z.B. Steckern oder auch andere Objekten verweisen auf das zugehörige KBL Objekt und werden 
mit dem Typ Spezifizierer type:ref gekennzeichnet. 
 
 
SVG Teilausschnitt: 


### 第 90 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 90 von 112 
 
<g> 
<!--kbl-id:ID_CON84;type:ref--> 
<g transform="translate(5491.5,82.5) rotate(0)"> 
<rect x="-59.5" y="-23.5" width="119" height="47" style="stroke:black; stroke-width:0.5; fill:none" /> 
<text x="0" y="-14.57" style="text-anchor:middle; font-family:Arial; font-size:10px; fill:red"> 
<tspan x="0" y="-3.071" style="text-anchor:middle">V6 = 8-fach = Cod.A</tspan> 
<tspan x="0" y="8.428" style="text-anchor:middle">V10 = 4-fach = Cod.B</tspan> 
</text> 
</g> 
</g> 
 
Fixings 
Befestigungselemente und Clips verweisen auf das entsprechende Fixing Objekt in der KBL. 
 
 
 
 
 
 
 
 
 
 
 
1 
2 


### 第 91 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 91 von 112 
 
Die Tabellendarstellung in der SVG: 
<g> 
   <!--kbl-id:ID_FIX18;type:table--> 
   <g transform="translate(3415.6,342.39) rotate(0) translate(-3415.6,-342.3)"> 
     <g> 
<rect x="3415.622" y="342.399" width="33" height="7" style="stroke:rgb(255,255,255); opacity:0; 
stroke-width:0.25; fill:none" /> 
<text x="3416.455" y="347.155" xml:space="preserve" font-weight="bold" style="text-anchor:start; font-
family:CorpoS; font-size:5px; fill:black">FX.COC.0017 </text> 
       ...  
     </g> 
   </g> 
 </g> 
 
Die grafische Darstellung des Fixings in der SVG: 
<g> 
   <!--kbl-id:ID_FIX18--> 
   <g transform="translate(3501.6,372.3) rotate(129.7) translate(-12,-12)"> 
     <g transform="translate(0,0) scale(1,1) translate(12,12)"> 
       <line x1="-10" y1="-2.5" x2="10" y2="-2.5" style="stroke:black; stroke-width:0.25" /> 
        <path d="M5 -7 A1 1,0,0,0,4 -8" style="stroke:black; stroke-width:0.25; fill:none" /> 
        ...  
     </g> 
  </g> 
</g> 
 
Das Fixingelement in der KBL: 
<Fixing_occurrence id="ID_FIX18"> 
     <Id>ID_FIX18</Id> 
     <Alias_id id="id_302_328"> 
       <Alias_id>FX.COC.0017</Alias_id> 
     </Alias_id> 
     … 
     <Part>id_323_9</Part> 
     <Installation_information id="id_329_1145"> 
       <Instruction_type>ADDITIONAL_ATTRIBUTE</Instruction_type> 
       <Instruction_value>Additional Attribute</Instruction_value> 
1 
2 


### 第 92 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 92 von 112 
 
     </Installation_information> 
     <Installation_information id="id_329_1146"> 
       <Instruction_type>CustomerDrawNo</Instruction_type> 
       <Instruction_value>A0009956290</Instruction_value> 
     </Installation_information> 
     <Installation_information id="id_329_1147"> 
       <Instruction_type>COMMENT_1</Instruction_type> 
       <Instruction_value>Kabelband ( Befestigungsclip)</Instruction_value> 
     </Installation_information> 
   </Fixing_occurrence> 
 
<Fixing id="id_323_9"> 
   <Part_number>A0009956190</Part_number> 
   <Version>20110811-NI</Version> 
   <Abbreviation>P3187</Abbreviation> 
   <Description>Kabelband ( Befestigungsclip)</Description> 
</Fixing> 
 
 


### 第 93 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 93 von 112 
 
 
Splices 
Splices verweisen auf das zugehörige Connector Objekt in der KBL und werden wie sonstige Stecker auch 
behandelt. 
 
 
SVG Teilausschnitt Symbol: 
<g> 
    <!--kbl-id:ID_BNJ3--> 
    <g> 
      <!--kbl-id:ID_CON128--> 
      <g transform="translate(2315,5) rotate(179.9) translate(-8.9,-8.0)"> 
        <g transform="translate(0,0) scale(1,1) translate(9,8)"> 
          <line x1="-2.5" y1="1.5" x2="-2.5" y2="-1.5" style="stroke:black; stroke-width:0.18" /> 
          …. 
        </g> 
      </g> 
    </g> 
</g> 
 
 
1 
2 
3 
4 
1 
Vertex 


### 第 94 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 94 von 112 
 
SVG Teilausschnitt Tabelle: 
<g> 
    <!--kbl-id:ID_CON128;type:table--> 
    <g transform="translate(2342,518) rotate(0)"> 
      <g> 
 
... 
        <rect x="0" y="12" width="53" height="7" style="stroke:rgb(255,255,255); opacity:0; stroke-width:0.25; 
fill:none" /> 
        <text x="0.833" y="16.756" font-weight="bold" style="text-anchor:start; font-family:CorpoS; font-
size:5px; fill:black">Z68/25*1-L Endh. Daten LIN3 Klima</text> 
      </g> 
      <g> 
<rect x="0" y="19" width="17" height="6" style="stroke:black; stroke-width:0.25; fill:none" /> 
<text x="8.5" y="23.332" style="text-anchor:middle; font-family:CorpoS; font-size:5px; fill:black">Ltg.-
Nr.</text> 
       ... 
        <g> 
          <!--kbl-id:ID_WIR1081;type:row--> 
          ... 
<text 
x="8.5" 
y="29.332" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
fill:black">2647</text> 
         ... 
        </g> 
      </g> 
 
 
2 


### 第 95 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 95 von 112 
 
SVG Teilausschnitt Zubehör: 
      <g> 
<rect x="0" y="45" width="47" height="7" style="stroke:rgb(255,255,255); opacity:0; stroke-width:0.25; 
fill:none" /> 
<text x="0.833" y="49.756" font-weight="bold" style="text-anchor:start; font-family:CorpoS; font-
size:5px; fill:black">Zubehörteile zu Z68/25*1-L Endh. Daten LIN3 Klima</text> 
 
....  
        <g> 
          <!--kbl-id:ID_ACC35;type:row--> 
<rect x="0" y="58" width="47" height="6" style="stroke:rgb(255,255,255); opacity:0; stroke-
width:0.25; fill:none" /> 
<text 
x="0.833" 
y="62.5" 
style="text-anchor:start; 
font-family:CorpoS; 
font-size:5px; 
fill:black">Gewebeband schwarz</text> 
<rect x="47" y="58" width="50" height="6" style="fill:rgb(255,255,255); opacity:0" /> 
         .... 
        </g> 
      </g> 
    </g> 
  </g> 
 
Zugehöriges KBL Element: 
<Connector_occurrence id="ID_CON128"> 
     <Id>Z68/25*1-L</Id> 
     <Alias_id id="id_302_298"> 
       <Alias_id>Z68/25*1-L</Alias_id> 
     </Alias_id> 
     <Description>Endh. Daten LIN3 Klima</Description> 
     <Usage>splice</Usage> 
      
     <Part>id_315_38</Part> 
     <Contact_points id="id_372_821"> 
       <Id>2877_0</Id> 
       <Contacted_cavity>id_370_954</Contacted_cavity> 
     </Contact_points> 
     <Contact_points id="id_372_822"> 
       <Id>2726_0</Id> 
       <Contacted_cavity>id_370_954</Contacted_cavity> 
4 
3 


### 第 96 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 96 von 112 
 
     </Contact_points> 
     <Contact_points id="id_372_823"> 
       <Id>2647_0</Id> 
       <Contacted_cavity>id_370_954</Contacted_cavity> 
     </Contact_points> 
…. 
     <Slots id="id_386_128"> 
       <Part>id_341_1</Part> 
       <Cavities id="id_370_954"> 
         <Part>id_308_1</Part> 
       </Cavities> 
     </Slots> 
 </Connector_occurrence> 
 
 
 
 
 
 


### 第 97 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 97 von 112 
 
 
Vertex 
Eine einzelne Vertex verweist auf das entsprechende Node Objekt in der KBL. 
 
<g> 
<!--kbl-id:ID_BNJ130--> 
<circle cx="7250.14" cy="528.65" r="0.5" style="fill:rgb(78,113,207)" /> 
</g> 
 
Zugehöriges KBL Element: 
<Node id="ID_BNJ130"> 
    
 
<Id>ID_BNJ130</Id> 
    
 
<Cartesian_point>id_307_117</Cartesian_point> 
 </Node> 
 
 
 


### 第 98 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 98 von 112 
 
 
Segment / Bandierung / Rohr 
Segmente werden eigentlich über den darauf liegenden Leitungsschutz dargestellt. Sie verweisen auf das 
Segment Objekt der KBL (Bundlesegment). Innerhalb der Segmentgruppe verweist der Leitungsschutz auf 
das entsprechende Wire-Protection Objekt in der KBL. Hier ist ein teilbandierter Abschnitt gezeigt. 
 
 
Der SVG Ausschnitt der Segmentdarstellung: 
<g> 
<!--kbl-id:ID_BNS6--> 
<path d="M5026.643 392.017, l4.837 0, ……" style="stroke:rgb(78,113,207); stroke-width:0.25;    
fill:none" /> 
<g> 
<!--kbl-id:ID_TAP63--> 
<path d="M5026.643 392.016, l33.861 0, .... "style="stroke:rgb(78,113,207); stroke-  width:0.25; 
fill:none" /> 
 </g> 
 </g> 
Der zugehörige KBL Abschnitt: 
<Segment id="ID_BNS6"> 
   <Id>ID_BNS6</Id> 
   <Virtual_length id="id_334_768"> 
     <Unit_component>id_346_1</Unit_component> 
     <Value_component>150</Value_component> 
   </Virtual_length> 
   <Physical_length id="id_334_767"> 
     <Unit_component>id_346_1</Unit_component> 
     <Value_component>150</Value_component> 
   </Physical_length> 
   <End_node>ID_BNJ113</End_node> 
1 
2 
1 
2 


### 第 99 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 99 von 112 
 
   <Start_node>ID_BNJ35</Start_node> 
   <Center_curve id="id_306_6"> 
     <Degree>1</Degree> 
     <Control_points>id_307_31 id_307_103</Control_points> 
   </Center_curve> 
…. 
   <Protection_area id="id_337_9"> 
     <Start_location>0.2</Start_location> 
     <End_location>1</End_location> 
     <Associated_protection>ID_TAP63</Associated_protection> 
   </Protection_area> 
 </Segment> 
 
 


### 第 100 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 100 von 112 
 
 Kabelkanäle 
Kabelkanäle und ähnlich Teile werden wie Fixings behandelt und verweisen daher auf das Fixing Objekt in 
der KBL. 
 
Der SVG Ausschnitt der Tabelle:  
<g> 
    <!--kbl-id:ID_FIX2;type:table--> 
    <g transform="translate(6839,264) rotate(0) translate(-6839,-264)"> 
<rect x="6839" y="264" width="33" height="7" style="stroke:rgb(255,255,255); opacity:0;   stroke-
width:0.25; fill:none" /> 
<text x="6839.833" y="268.756" xml:space="preserve" font-weight="bold" style="text-anchor:start; 
font-family:CorpoS; font-size:5px; fill:black">FX.COC.0001 </text> 
        …… 
    </g> 
  </g> 
 
 
1 
2 
1 


### 第 101 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 101 von 112 
 
Der SVG Ausschnitt der Kabelkanal Symbolgrafik: 
  <g> 
    <!--kbl-id:ID_FIX2--> 
    <g transform="translate(6808,377) rotate(-359.0122) translate(-1,-55)"> 
      <g transform="translate(0,0) scale(1,1) translate(221,60)"> 
        <polyline points="-117.305 22.474 ,63.927 22.474 ……./> 
 
…… 
      </g> 
    </g> 
  </g> 
 
 
2 


### 第 102 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 102 von 112 
 
 Uhrzeiten 
Die Symbole der Uhrzeiten (Ausbindungsrichtungen) können prinzipiell auf verschiedene Objekte verweisen. 
Zum Beispiel kann das Uhrzeitensymbol auf den dazugehörigen Clip verweisen oder mit einer Vertex bzw. 
einem Segment verbunden werden. Sie selber haben kein physikalisches Objekt in der KBL. 
 
SVG Teilausschnitt: 
<g> 
    <!--kbl-id:ID_FIX1--> 
    <g transform="translate(315.9,589.2) rotate(-89.3) translate(-14,-13)"> 
<line x1="11.238" y1="-8.838" x2="-0.013" y2="-8.838" style="stroke:black; stroke-width:0.25" />  
…. Uhrzeitengrafik… 
    </g> 
</g> 
 
 
1 
1 


### 第 103 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 103 von 112 
 
 Bemaßungen 
Bemaßungen verweisen auf die beiden zugehörigen Vertexes (Start- und Endpunkt) und werden mit dem Typ 
dimension gekennzeichnet.  
Im nachfolgenden Beispiel sind Text und Maßpfeil voneinander getrennt gruppiert worden, da hier 
unterschiedliche Transformationen angewendet werden. Die Bemaßung kann natürlich auch in einer einzigen 
Gruppe untergebracht werden. 
 
 
SVG Abschnitt einer Bemaßung: 
<g> 
    <!--kbl-id:ID_BNJ46 ID_BNJ125;type:dimension--> 
    <g transform="translate(7769.0,377.61) rotate(329.21) translate(0,-10)"> 
<text 
x="0" 
y="1.332" 
style="text-anchor:middle; 
font-family:CorpoS; 
font-size:5px; 
text-
decoration:underline; fill:black">100</text> 
    </g> 
  </g> 
  <g> 
    <!--kbl-id:ID_BNJ46 ID_BNJ125;type:dimension--> 
    <g transform="translate(7769.01,377.61) rotate(329.21)"> 
<line x1="104.748" y1="-1" x2="104.748" y2="-11" style="stroke:black; stroke-width:0.25" /> 
      <polygon points="104.748 -10 ,100.748 -11 ,100.748 -9" style="fill:red" /> 
<line x1="4.952" y1="-10" x2="100.748" y2="-10" style="stroke:black; stroke-width:0.25" /> 
    </g> 
  </g> 
 
 
2 
1 
1 
2 


### 第 104 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 104 von 112 
 
8.6.4.7 Besonderheiten und implizite Logik bei der Darstellung  
Um eine sinnvolle Darstellung des Routings machen zu können, muss der Browser bezüglich der 
Segmentdarstellung eine besondere Logik anwenden. Segmente, die eigentlich über ihre Modulzugehörigkeit 
bzw. der Modulzugehörigkeit des entsprechend dargestellten Leitungsschutzes in einer Konfiguration 
ausgeblendet würden, müssen trotzdem dargestellt werden, wenn es Leitungen gibt, die in diesem Segment 
laufen und entsprechend ihrer Modulzugehörigkeit aktiv sind. 
 
 


### 第 105 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 105 von 112 
 
9 Mitgeltende Unterlagen  
9.1 Normative Hinweise 
Das in dieser Ausführungsvorschrift abgebildete Zeichnungsschriftfeld, das Änderungsschriftfeld sowie 
normative Hinweise sind nur Beispielhaft, für die Aktualität und Ausführung der Zeichnung ist der 
Zeichnungsersteller verantwortlich. 
 
 


### 第 106 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 106 von 112 
 
9.2 Normen und Vorschriften  
Alle in der folgenden Tabelle aufgeführten Dokumente sind über DocMaster verfügbar. 
Unterlage/Dokument Inhalte/ Benennung 
A0000026199 
AV Leitungen 
A0000069899 
Ausführungsvorschrift Schaltplanerstellung 
A0040028599 
Ausführungsvorschrift Leitungssatz-DMU Erstellung 
A0060064699 
Ausführungsvorschrift Datenformat KBL 
A0070049999   
Ausführungsvorschrift Referenzen 
A0090000299 
Sachnummerzuordnung für Eigenschaften von Stützpunkten in elektrischen 
Leitungssätzen 
A999 8002 
Benennungsfestlegung 
für 
Abkürzungen 
im 
zweisprachigen 
(deutsch/englisch) Schriftfeld der MB-Zeichnungen 
A0598004 
Archivierungsrichtlinie Smaragd  
A0598030 
Dokumentation des ESD-Kenners in Smaragd 
A0598031 
Festlegung von Benennungen, Abkürzungen und Akronyme 
zu A- und H- Sachnummern für MBC und MB Vans 
CS080 
Leitfaden zur Erstellung von Leitungssatzkomponenten in Siemens NX 
DBL 8585 
Stoffnegativliste für die Werkstoffauswahl 
DIN 46234 
Kabelschuhe für lötfreie Verbindungen, Ringform ohne Isolierhülse für 
Kupferleiter 
DIN IEC 60757 
Code zur Farbkennzeichnung  
DIN ISO 16016 
Technische Produktdokumentation – Schutzvermerke zur Beschränkung der 
Nutzung von Dokumenten und Produkten (ISO 16016:2000) 
DIN ISO 8601 
Datenelemente und Austauschformate – Darstellung von Datum und Uhrzeit 
DIN 46225 
Gestanzte Krallenkabelschuhe mit Isolierumfassung für isolierte Leitungen 
DIN 46234 
Kabelschuhe für lötfreie Verbindungen, Ringform ohne Isolierhülse für 
Kupferleiter 
ISO 10303-212 
Industrielle 
Automatisierungssysteme 
und 
Integration 
– 
Produktdatendarstellung und –austausch – Teil 212: Anwendungsprotokoll: 
Elektrische/elektrotechnische Systeme und Anlagen 
MBN 10317 
Kennzeichnung von Merkmalen zur besonderen Nachweisführung 
Grundlagen - Dokumentationspflicht von Bauteilen / Baugruppen 
MBN 31001 
CAD-Zeichnungen: Grundlagen der Produktdarstellung  
Basis-Konstruktionsrahmen, die Darstellung und Kennzeichnung von Teilen 
MBN 31002 
CAD-Zeichnungen: Beschriftung Schriftarten und Schriftgröße 
MBN 31020-1 
Schriftfelder in Zeichnungen 


### 第 107 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 107 von 112 
 
MBN 31020-2 
Zeichnungsfeld auf CAD-Zeichnungen 
MBN 31020-3 
Änderungen in Konstruktionszeichnungen 
MBN 10435  
Kennzeichnung von Teilen mit Daimler-Warenzeichen, Sachnummer und 
Identmerkmalen 
******** 
Steuerungskennzeichen in den Systemen der Produktdokumentation 


### 第 108 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 108 von 112 
 
9.3 Abkürzungen und Begriffe 
Abkürzung 
Benennung/ Bemerkung 
ACM 
Agile Change Management (ehemals NCM  New Change Management) 
AV 
Ausführungsvorschrift 
ConnectPARTS 
Komponentendatenbank der Leitungssatzentwicklung 
ConnectHARNESS 
Leitungssatz- und Schaltplan- Recherche Datenbank 
DE 
deutsch 
Dialog 
Stücklistensystem der Daimler AG  
DS 
Dokumentation Sicherheitsrelevanter Teile 
DMU 
Digital Mock-Up. Computersimulation eines Produktes für die Unterstützung von 
Entwicklung, Herstellung und Service 
DZ 
Dokumentation Zertifizierung 
DocMaster 
Dokumenten-Management 
EAD/ ELA 
Einzeladerabdichtung/ Einzelleitungsabdichtung für elektrische Leitungen 
ELOG 
Schaltplandatenmodell nach ISO 10303- AP212 Spezifikation 
EN 
englisch 
EPDM 
Electronics Product Data Management 
ESD 
Empfindlichkeit eines Bauteils gegen elektrostatische Entladungen (electrostatic discharge) 
HCV 
Harness Container for Viewing  
KBL 
Leitungssatzdatenmodell (Kabelbaumliste) nach ISO 10303- AP212 Spezifikation 
KEM 
Konstruktions- Einsatz- Meldung 
KSL 
Kundenspezifischer Leitungssatz 
kZ 
Abkürzung für den Hinweis, dass ein Teil (Sachnummer) keine eigene Zeichnung hat, 
sondern, auf einer Tabellen-Zeichnung dargestellt ist. 
MBC 
Mercedes Benz Car / Development , PKW - Entwicklung 
REF 
Referenzbezeichnung nach VA 059 EI08  (siehe auch VZK) 
RD/UBL 
Leitungssatzentwicklung in der Entwicklung PKW /Elektrik.  
Smaragd  
in MBC verwendetes PDM- System 
SRM 
Sachstamm Recherche Modul 
SVG 
Skalierbare Vektor Graphik 
TIFF 
Tagged Image File Format   
VZK 
Verwendungszweckkatalog  
XML 
extensible mark-up language 
ZGS 
Zeichnungs- und Geometrie- Stand 
ZGDOK 
Zeichnungs-Geometrie-Dokumentation Datenmanagement 


### 第 109 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 109 von 112 
 
 
 
 


### 第 110 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 110 von 112 
 
9.4 Abbildungsverzeichnis 
Abbildung 1: Bauraum Master ...........................................................................................................................11 
Abbildung 2: Leitungssatzmodule ......................................................................................................................11 
Abbildung 3: Beispiel für den Aufbau von Segmenten .....................................................................................12 
Abbildung 4: Aufbau Mehrblattzeichnung ........................................................................................................16 
Abbildung 5: Aufbau Einzelblattzeichnung ......................................................................................................16 
Abbildung 6: Besonderheiten auf Zeichnungskopf Mehrblattzeichnungen ...................................................17 
Abbildung 7: Aufbau der Leitungssatzzeichnung (bei Mehrblattzeichnungen Blatt1) .................................18 
Abbildung 8: Blatt 2 (bei einer Mehrblattzeichnung) .......................................................................................19 
Abbildung 9: Pflichtfelder des MB Schriftfeldes ...............................................................................................20 
Abbildung 10: Beispiel Änderungsmeldungen ..................................................................................................21 
Abbildung 11: Beispiel eines Änderungsschriftkopfes "mit Änderung neu gezeichnet"...............................22 
Abbildung 12: bauraumoptimierte Anordnung (kompakte Verclipsung) Kabelschuhe ...............................28 
Abbildung 13: Auszug maximaler Summenstrom Massebolzen ......................................................................30 
Abbildung 14: Masseanschluss ohne/mit Schrumpfschlauch ...........................................................................30 
Abbildung 15: Auszug Leitungssatzzeichnung ..................................................................................................31 
Abbildung 16: Leitungsduplizierung einfacher Fall .........................................................................................32 
Abbildung 17: Leitungsduplizierung komplexer Fall .......................................................................................33 
Abbildung 18: Darstellung Segmente bei Leitungsduplizierung .....................................................................34 
Abbildung 19: Beispiel-Darstellung Kontaktierungsteil auf Zeichnung .........................................................38 
Abbildung 20: Beispiel eines KSL-Steckers .......................................................................................................41 
Abbildung 21: Symbole Stützpunkte ..................................................................................................................41 
Abbildung 22: Anbindung von Durchgangs- und Endverbindern ..................................................................42 
Abbildung 23: Darstellung der Uhrzeit mit Definition der Überprüfungsebene............................................42 
Abbildung 24: Ausgangspunkt der Blickrichtung ............................................................................................43 
Abbildung 25: Darstellung von Befestigungselementen ...................................................................................44 
Abbildung 26: Befestigungselement zeigt in Überprüfungsebene 9 Uhr, auch wenn grafisch anders dargestellt
 .......................................................................................................................................................................44 
Abbildung 27: Befestigungselement liegt zur Montagebrettebene 9 Uhr, die Steckrichtung liegt auf 6 Uhr45 
Abbildung 28: Beispiel einer Steckrichtung parallel zum Leitungsstrang ......................................................45 
Abbildung 29: Darstellung von nicht-angeschlagenen Schirmen ....................................................................48 
Abbildung 30: Dokumentation Zubehörteil - Beispiel Sicherung ....................................................................48 
Abbildung 31: Bemaßung von sternförmigen Massestellen .............................................................................50 
Abbildung 32: Bemaßung von Kabelschuhen mit Referenzmaß .....................................................................51 
Abbildung 33: Beispielbemaßung Befestigungselement ...................................................................................51 
Abbildung 34: Benennung Referenzpunkt ........................................................................................................52 
Abbildung 35: Referenzpunktbemaßung bei Befestigungselement .................................................................52 


### 第 111 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 111 von 112 
 
Abbildung 36: Legende Allgemeintoleranz (DE/EN) ........................................................................................53 
Abbildung 37: Toleranz für Befestigungselemente (DE/EN) ...........................................................................53 
Abbildung 38: Einknüpfebene einer Tülle .........................................................................................................54 
Abbildung 39: Maßbezugspunkt am Kabelkanal ..............................................................................................56 
Abbildung 40: Tülle mit eingespritztem Pfeil als Maßbezugspunkt ................................................................57 
Abbildung 41: Vermessung von Kabelschuhsternen ........................................................................................57 
Abbildung 42: Allgemeine Vermessung Kabelschuhe ......................................................................................58 
Abbildung 43: Vermessung gewinkelter Kabelschuhe .....................................................................................58 
Abbildung 44: Vermessung von Kabelschuhen mit Anschraubpunkt ............................................................58 
Abbildung 45: Vermessung von Kontaktgehäusen ...........................................................................................59 
Abbildung 46: Vermessung von HSD-/FAKRA-Winkelstecker ......................................................................59 
Abbildung 47: Vermessung Spezial-Kontaktierung..........................................................................................59 
Abbildung 48: Zusatzinformationen bei Sonderfällen ......................................................................................59 
Abbildung 49: Daten/Tools..................................................................................................................................60 
Abbildung 50: Abbildung Prozesse auf Toollandschaft....................................................................................61 
Abbildung 51: Strukturierung KBL bei Einzelleitungssätzen .........................................................................63 
Abbildung 52: Strukturierung KBL bei Masterleitungssätzen ........................................................................64 
Abbildung 53: schematischer Aufbau eines HCV-Containers .........................................................................67 
Abbildung 54: Darstellung Verbindung Index.xml und Harness.kbl ..............................................................68 
Abbildung 55: Ausschnitt einer Index-XML .....................................................................................................71 
 
 
 


### 第 112 页
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! Keine 
Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
Ausführungsvorschrift 
Leitungssatzdokumentation 
 
A 002 006 32 99 
 
Bearb./auth.: Neckenich, Jonas 
 
Abt./dep.: RD/ UBL 
 
Datum/date: 2018-08-24 
 
ZGS: 011 
 Auftr.-Nr./order no.:   YAP4043818 
 
Seite/page: 112 von 112 
 
9.5 Anhang 
Beispiel für Allgemeine Legende in DE/ EN 
 
 

