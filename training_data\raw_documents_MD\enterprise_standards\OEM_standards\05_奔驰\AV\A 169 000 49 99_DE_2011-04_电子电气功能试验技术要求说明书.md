# A 169 000 49 99_DE_2011-04_电子电气功能试验技术要求说明书.pdf

## 文档信息
- 标题：Microsoft Word - A1690004999.DOC
- 作者：petroth
- 页数：12

## 文档内容
### 第 1 页
MBC 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
AV E/E-Funktionstests 
A 169 000 49 99 
 
Bearb./auth.: Weimer 
 
Abt./dep.: EP/ EKE 
 
Datum/date: 11-27-04 
 
ZGS : 001 
Auftr.-Nr./order  no.: YAP28121/07 
 
Seite/page: 1 von 12 
 
Ausführungsvorschrift (AV) 
E/E-Funktionstests 
 
 
Stand 27.04.11 
Version 4.1 
 
 
Historisch, ersetzt durch MGU00000030 (2012-08)
Dokument historisch/Unkontrollierte Kopie bei Ausdruck (: <PERSON><PERSON>, 2013-08-23)


### 第 2 页
MBC 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
AV E/E-Funktionstests 
A 169 000 49 99 
 
Bearb./auth.: Weimer 
 
Abt./dep.: EP/ EKE 
 
Datum/date: 11-27-04 
 
ZGS : 001 
Auftr.-Nr./order  no.: YAP28121/07 
 
Seite/page: 2 von 12 
 
 
Aenderungsbeschreibung/ revision text 
 
Datum 
Seite, (ev. 
verschob
en) 
Kapitel 
Bearbeiter 
Änderung 
12.03.01 
Alle 
Alle 
Fromm 
Version 1.0 
01.03.02 
Alle 
Alle 
Gisy 
Version 2.0 
28.06.07 
Alle 
Alle 
Petke 
Version 3.0 
24.03.09 
Alle 
Alle 
Petke 
Version 3.1 Trennung von Anforderungen und 
Informationen, Angabe von Quality Gates wurden 
entfernt 
22.04.09 
9, 11 
5,6 
Petke 
Version 4.0: Verantwortlichkeiten Auftragnehmer 
und Bauteilberantwortlicher getrennt. 
05.10.09 
Alle 
Fußzeile 
Petke 
MBC eingefügt 
27.04.11 
Alle 
Fußzeile 
Weimer 
Version 4.1: Verantwortlichkeit geändert 
 
 
 
 
 
 
Rückfragen 
zur 
AV 
E/E-Funktionstests 
sind 
zu 
richten 
an 
die 
Abteilung 
EP/EKE. 
Hier wird auch die Aktualisierung der Ausführungsvorschrift bearbeitet. 
Ansprechpartner bei EP/EKE, HPC X944, sind: 
 
Dieter Serries,  
Tel. 
07031/90-41061, 
E-Mail: <EMAIL> 
 
Oliver Weimer 
Tel. 
07031/90-85958 
E-Mail: <EMAIL> 
 
Homepage 
http://smtcve0001.rd.corpintra.net/HIL_homepage/index.html;internal&action=noaction&Parameter=1
237892615389 
 
Historisch, ersetzt durch MGU00000030 (2012-08)
Dokument historisch/Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-23)


### 第 3 页
MBC 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
AV E/E-Funktionstests 
A 169 000 49 99 
 
Bearb./auth.: Weimer 
 
Abt./dep.: EP/ EKE 
 
Datum/date: 11-27-04 
 
ZGS : 001 
Auftr.-Nr./order  no.: YAP28121/07 
 
Seite/page: 3 von 12 
 
1 
Allgemeines 
 
 
Ziel der AV E/E-Funktionstests  
Die Ausführungsvorschrift E/E-Funktionstests enthält weitere Informationen zu den im Kapitel *******.3 des 
Standardlastenhefts formulierten Anforderungen an E/E-Komponenten, die für die Durchführung von 
Systemintegrations- und Fahrzeugintegrationstests an Hardware-in-the-Loop (HIL) Testsystemen im Labor (Daimler 
und Zulieferer) und an Prototypen während der Inbetriebnahme in der E/E-Werkstatt notwendig sind.  
Der Abschnitt „Design-for-Testability“ (DFT) enthält Beschreibungen zur Auslegung von Komponenten mit integrierten 
Sensoren und Aktoren mit Hinblick auf eine vollständige Automatisierung der Testabläufe. Besonderes Augenmerk 
wird auf Steuergeräte gerichtet, die Teil des MMIs sind und integrierte Anzeige- oder Bedienelemente beinhalten. 
 
 
Gebrauch der AV E/E-Funktionstests 
Die Anforderungen im Kapitel *******.3 des Standardlastenhefts sind bei der Entwicklung von E/E-Komponenten zu 
berücksichtigen. 
Den terminlichen Angaben liegt der Entwicklungsprozess der jeweiligen Baureihe zugrunde. Eventuelle Anpassungen 
bzgl. der Baureihenterminpläne sind in Abstimmung mit den E/E-Funktionsgruppen zulässig. 
 
 
 
 
Historisch, ersetzt durch MGU00000030 (2012-08)
Dokument historisch/Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-23)


### 第 4 页
MBC 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
AV E/E-Funktionstests 
A 169 000 49 99 
 
Bearb./auth.: Weimer 
 
Abt./dep.: EP/ EKE 
 
Datum/date: 11-27-04 
 
ZGS : 001 
Auftr.-Nr./order  no.: YAP28121/07 
 
Seite/page: 4 von 12 
 
2 
E/E-Funktionstests im Entwicklungsprozess 
 
2.1 
Übersicht 
 
Labor
Test
Bordnetz
Test
Sicherungen
E/E-Klausur
E/E-Erpro-
bungsfahrten
EMV-
Test
Gesamtfahrzeug
Klausuren
Fahrzeug
Inbetriebnahme
Referenz-Fahrzeug Elektrik/Elektronik
Fahrzeug
Integrationstest
System-
Integrationstest
Komponenten-
test
HIL
•Tests beim
Lieferanten
•Test bei DAI
•Test der
Kommunikation
•Automatisierter
Test der System-
funktionen
•Automatisierter
Test der Fahrzeug-
funktionen
•Test des
Bordnetzes
•Test der Diagnose
•Statische u.
dynamische
Funktionstests
•Sicherungs-
konzept
•Unterspannung
•Resetverhalten
•Ruhestrom
•Ladebilanz
•Systemtests
•E/E-Tests im
Fahrbetrieb
•EMV-
Messungen
•Störimpulse
•E/E-Test durch
Dauerlauffahrer
und Baureihen SB
Abbildung 1: Der integrale E/E-Testprozess 
 
2.2 
Zielsetzung 
 
Die Qualitätssicherung von E/E-Systemen ist durch deren steigende Komplexität immer mehr zu einem 
bereichsübergreifenden 
Thema 
geworden, 
dessen 
Behandlung 
ein 
abgestimmtes 
Vorgehen 
von 
Entwicklungsabteilungen, Systemintegratoren, Zulieferern und Prototypenwerkstätten erfordert. Der integrale E/E-
Testprozess (Abbildung 1) stellt den in der MCG PKW-Entwicklung definierten Ablauf der dafür notwendigen 
Aktivitäten in Labor und Werkstatt dar. 
Um den Reifegrad der E/E-Komponenten im Entwicklungsstadium zu erhöhen und die Inbetriebnahme von Prototypen 
zu beschleunigen, sind im Entwicklungsablauf gemäß Releasemanagement Testphasen vorgesehen. In diesen 
Testphasen werden mit automatisiert ablaufenden Funktionstests Fehler erkannt und Fehlerbeseitigungsmaßnahmen 
von den verantwortlichen Parteien der DAI und entsprechenden Zulieferern initiiert. Die dabei durchgeführten 
Testaktivitäten werden im weiteren Verlauf des Dokuments als E/E-Funktionstests bezeichnet. 
Zur Planung und Durchführung der Testphasen müssen die notwendigen Informationen bzgl. Steuergeräten und 
durchzuführenden Tests rechtzeitig zur Verfügung stehen (s. Kapitel 5, Inhalt des SG Datenblatts). 
In Regressionstests wird nach den jeweiligen Quality Gates mit Hilfe von reproduzierbaren Testfällen die 
Fehlerbeseitigung überprüft.  
 
Historisch, ersetzt durch MGU00000030 (2012-08)
Dokument historisch/Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-23)


### 第 5 页
MBC 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
AV E/E-Funktionstests 
A 169 000 49 99 
 
Bearb./auth.: Weimer 
 
Abt./dep.: EP/ EKE 
 
Datum/date: 11-27-04 
 
ZGS : 001 
Auftr.-Nr./order  no.: YAP28121/07 
 
Seite/page: 5 von 12 
 
3 
Design-for-Testability 
 
3.1 
Allgemeines 
 
Die ersten drei Phasen des E/E-Testprozesses (s. Abbildung 1) werden im Labor durchgeführt. Dazu werden die 
Steuergeräte an ein HIL-Testsystem adaptiert, d.h. über die elektrische Schnittstelle mit einem Simulationssystem 
verbunden. Die elektrische Adaption muß dabei so erfolgen, daß das HIL-Testsystem die Eingänge des Steuergerätes 
stimulieren und seine Ausgänge erfassen kann. 
Der E/E-Funktionstest wird mit derselben Steuergerätehardware und –software durchgeführt, die später auch bei der 
Fahrzeuginbetriebnahme verbaut wird. Dadurch soll die Aussagekraft der im Labortest erzielten Testergebnisse 
möglichst realitätsnah und mit dem am realen Fahrzeug auftretenden Verhalten vergleichbar sein. 
Bei manchen Steuergeräten ist es jedoch nicht möglich, die für den E/E-Funktionstest erforderlichen Prozeßgrößen 
an einer äußeren elektrischen Schnittstelle des Steuergerätes zu manipulieren oder zu beobachten. Dies ist zum 
einen dann der Fall, wenn sich Sensoren oder Aktoren innerhalb des Steuergerätes befinden, zum anderen wenn der 
Zugriff auf interne Prozeßgrößen wie z.B. Zwischenergebnisse des Steuergerätealgorithmus notwendig ist. 
Beispiele hierfür sind integrierte Temperatursensoren oder Taster, oder aber auch Anzeigeelemente wie Displays oder 
LEDs. In diesen Fällen ist es notwendig, das Steuergerät entsprechend zu modifizieren, um an die für den 
automatisierten Test notwendigen Prozeßgrößen zu gelangen. Die durch die Veränderung reduzierte Vergleichbarkeit 
der Testergebnisse im Bezug auf das unveränderte Steuergerät wird dabei gegenüber der Untestbarkeit bewußt in 
Kauf genommen. 
Der Begriff „Design-for-Testability“ (DFT) bezeichnet alle Maßnahmen und Vorkehrungen an einem Steuergerät, die 
notwendig sind, um es für einen automatisierten E/E-Funktionstest nutzbar zu machen. 
 
3.2 
Maßnahmen 
 
Es gibt unterschiedliche Arten ein Steuergerät für den Test an HIL-Testsystemen zu modifizieren. Grundsätzlich lassen 
sich zwei Arten unterscheiden: Software- und Hardwareänderungen. Das prinzipielle Vorgehen bei beiden Typen ist in 
Abbildung 2 graphisch dargestellt. 
 
 
Softwareänderungen 
Bei einer Softwareänderung wird das eigentliche Steuergeräteprogramm derart instrumentiert, daß die für den Test 
erforderlichen Prozeßgrößen über eine bereits am Steuergerät vorhandene Kommunikationsschnittstelle (z.B. CAN) 
mit dem HIL-Testsystem ausgetauscht werden. Das heißt, interne Prozeßgrößen, die nicht elektrisch erfaßt werden 
können, werden aus dem Steuergerät gesendet. Bei Prozeßgrößen, die nicht elektrisch von außen manipuliert werden 
können, muß eine interne Umschaltung des Datenflusses und die Vorgabe des Größenwertes über die 
Kommunikationsschnittstelle erfolgen können. Dabei ist zu gewährleisten, daß weder die reine Instrumentierung des 
Steuergeräteprogramms noch die Ausführung der instrumentierten Codeteile die eigentliche Funktion des 
Steuergerätes beeinflussen. Die interne Umschaltung des Datenflusses sorgt dafür, daß derselbe Softwarestand 
sowohl im Labor als auch im Fahrzeug benutzt werden kann. 
 
 
Hardwareänderungen 
Änderungen an der Steuergerätehardware sind grundsätzlich zu vermeiden, da dadurch die Möglichkeit genommen 
wird, das im Labor getestete Steuergerät in der nächsten Phase des E/E-Funktionstests im Fahrzeug zu verbauen. 
Hardwareänderungen zur elektrischen Adaption an ein HIL-Testsystem implizieren außerdem meist, dass das 
Steuergerät danach nicht mehr funktionstüchtig in ein Fahrzeug eingebaut werden kann. In manchen Fällen kann es 
aber durchaus sinnvoll sein, notwendige DFT-Maßnahmen nicht in der Software sondern in der Hardware zu treffen. 
Bei einer Hardwareänderung als DFT-Maßnahme wird die Hardwareverbindung innerhalb des Steuergerätes 
unterbrochen und die entstandene Schnittstelle über einen proprietären DFT-Stecker nach außen geführt. Über 
diesen Stecker kann dann das HIL-Testsystem elektrisch adaptiert werden. Welcher Stecker als DFT-Stecker zum 
Einsatz kommt wird im Einzellfall zwischen dem HIL-Ansprechpartner und dem Steuergeräteverantwortlichen 
abgestimmt. 
Historisch, ersetzt durch MGU00000030 (2012-08)
Dokument historisch/Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-23)


### 第 6 页
MBC 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
AV E/E-Funktionstests 
A 169 000 49 99 
 
Bearb./auth.: Weimer 
 
Abt./dep.: EP/ EKE 
 
Datum/date: 11-27-04 
 
ZGS : 001 
Auftr.-Nr./order  no.: YAP28121/07 
 
Seite/page: 6 von 12 
 
Betriebsystem und
Standard-SW-Module
(OSEK, DBKOM,...)
Applikation
(Steuergeräte-Funktion,
Diagnose-Funktion,...)
Interner Sensor
oder Aktor
(LED, Taster,...)
Hardware-Änderung:
Aufbrechen der elektrischen
Verbindung zwischen
Sensor/Aktor
und der IO-Hardware.
Die elektrische Adaption des
HIL-Testsystems erfolgt über
einen im Einzelfall zu
definierenden DFT-Stecker
(z.B. Sub-D9) 
I/O-Treiber
Steuergeräte-Stecker
DFT-Stecker
I/O-Hardware
(DIO, ADC/DAC, CAN-
Transceiver,...)
Hardwareverbindung
Steuergerät
Software-Änderung:
Instrumentierung des
Steuergerätecodes zur
Kontrolle des Datenflusses.
Die direkte Datenverbindung
zwischen der Applikation und
der Treiberschicht wird
unterbrochen. Eine Zusatz-
komponente in der Applikation
(DFT-Code) koordiniert den
Datenaustausch zwischen der
Applikation und dem externen
HIL-Testsystem. Die
Kommunikation der
Zusatzkomponente mit dem
HIL-Testsystem erfolgt über
die bereits im Steuergerät
vorhandenen IO-Treiber
(z.B. CAN).
Datenflussverbindung
DFT-
Code
 
Abbildung 2: Prinzipdarstellung eines Steuergerätes mit den Eingriffstellen der Design-for-Testability-Maßnahmen 
 
 
Es ist hilfreich, wenn auf die modifizierte Steuergerätehardware neue Softwarestände eingespielt werden können. 
Andernfalls muß jedes Steuergerät mit einem neuen Softwarestand erneut geändert werden. 
 
 
3.3 
Umsetzung 
 
Welche konkreten DFT-Maßnahmen für ein Steuergerät getroffen werden müssen, ist in jedem Fall zwischen dem 
Steuergeräteverantwortlichen und dem Ansprechpartner der HIL Systemintegration abzustimmen. Nach dieser 
Abstimmung sind die DFT-Maßnahmen im Steuergerätelastenheft zu dokumentieren. 
 
3.4 
DFT-Maßnahmen am Beispiel KOMBI169 
 
Im Folgenden soll nun am Beispiel des Steuergerätes Kombiinstrument der Baureihe 169 gezeigt werden, wie DFT-
Maßnahmen konkret aussehen können. 
Beim Kombiinstrument der Baureihe169 sollte beim automatisierten E/E-Funktionstest überprüft werden, ob der 
Steuergerätealgorithmus die Prozessgrößen richtig berechnet, die dann vom Kombi in Form von Zeigerstellungen, 
Warnmeldungen und Anzeigelampen dargestellt werden. Um eine aufwändige optische  Erfassung durch ein 
Kamerasystem mit anschließender Bildverarbeitung zu vermeiden, hat man sich in diesem Fall entschlossen, als DFT-
Maßnahme eine Softwareänderung vorzunehmen und die beim automatisierten E/E-Funktionstest relevanten 
Prozeßgrößen mit CAN-Botschaften dem HIL-Testsystem zugänglich zu machen. Abbildung 3 zeigt einen Ausschnitt 
aus der K-Matrix für den CAN-Class-B.  
Historisch, ersetzt durch MGU00000030 (2012-08)
Dokument historisch/Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-23)


### 第 7 页
MBC 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
AV E/E-Funktionstests 
A 169 000 49 99 
 
Bearb./auth.: Weimer 
 
Abt./dep.: EP/ EKE 
 
Datum/date: 11-27-04 
 
ZGS : 001 
Auftr.-Nr./order  no.: YAP28121/07 
 
Seite/page: 7 von 12 
 
 
Abbildung 3: Beispielhafte Umsetzung der DFT-Richtlinie für das Kombi BR169; Auszug aus der  
K-Matrix für CAN Class-B 
 
Die Botschaften 0x5ABh und 0x774h tragen Informationen, die in einem regulären Fahrzeug nicht auf dem CAN 
verfügbar sind. Der zusätzliche DFT-Code im Steuergerät verpackt die internen Prozeßgrößen in CAN-Botschaften und 
schickt sie über den CAN-Class B nach draußen zum HIL-Testsystem. Dort dienen sie zur Testauswertung. 
Die eigentliche Funktion des Steuergerätes wird nicht beeinträchtigt. Die Prozeßgrößen werden nach wie vor zur 
Ansteuerung der integrierten Ausgabeeinheiten wie z.B. Kontrollleuchten, Zeiger usw. benutzt. 
Wie häufig die CAN-Botschaften gesendet werden, hängt von den Testanforderungen ab. Im Beispiel Kombi169 
genügte es, wenn die Botschaften immer nur dann gesendet werden, wenn sich der Inhalt der Botschaft geändert hat. 
Deshalb ist die Sendeart „spontan“ vermerkt. 
Bei diesem Beispiel handelt es sich um eine reine Beobachtung interner Prozeßgrößen. Es findet keine Stimulation 
des Steuergerätes über DFT-Maßnahmen statt. 
SG
ID
ID-Name
Zykluszeit in ms
Sendeart
Sendeparameter
Byte-Nr.
Bit-Nr
Signalpaket-Name
Signalpaket-Funktion
Signalpaket-Länge [Bit]
Byte-Nr.
Bit-Nr
Signalname
Signal-Beschreibung
KOMBI
5ABh APPL_SG_KOMBI
spontan
1 0 WMB_HFKTNR
Warnmeldung und Bedienmenü Hauptfunktionsnummer
8
1 0 WMB_HFKTNR
Warnmeldung und Bedienmenü Hauptfunktionsnummer
2 0 WMB_UFKTNR
Warnmeldung und Bedienmenü Unterfunktionsnummer
16 2 0 WMB_UFKTNR
Warnmeldung und Bedienmenü Unterfunktionsnummer
4 0 WMB_LFDBNR
Warnmeldung und Bedienmenü laufende Bildnummer
8
4 0 WMB_LFDBNR
Warnmeldung und Bedienmenü laufende Bildnummer
5 0 Sprache
Eingestellte Menüsprache
8
5 0 Sprache
Eingestellte Menüsprache
6 0 Uhr_STD
Stunden
8
6 0 Uhr_STD
Stunden
7 0 Uhr_min
Minuten
8
7 0 Uhr_min
Minuten
8 0 ZS_Tank
Zeigerstand Tankanzeige
8
8 0 ZS_Tank
Zeigerstand Tankanzeige
KOMBI
774h SG_APPL_KOMBI
spontan
1 0 KTL_status1
Kontrollleuchten Status 1
8
8 0 KTL_tank
Kontrollleuchten Tankreserve
8 1 KTL_küwatp
Kontrollleuchten Kühlwasserübertemperatur
8 2 KTL_tank
Kontrollleuchten Blinker links
8 3 KTL_tank
Kontrollleuchten Blinker rechts
8 4 -
Reserve
2 0 KTL_status2
Kontrollleuchten Status 2
1
7 0 KTL_Diesel
Kontrolleuchte Dieselvorglühen
7 1 KTL_EU
Kontrolleuchte EU3/4-Symbol
7 2 KTL_Feststbr
Kontrolleuchte Feststellbremse
7 3 KTL_SRS
Kontrolleuchte SRS-Airbag
7 4 KTL_ABS
Kontrolleuchte ABS
7 5 KTL_Gurt
Kontrolleuchte Gurtwarnung
7 6 KTL_Fernli
Kontrolleuchte Fernlicht
7 7 KTL_ESP
Kontrolleuchte ESP-Funktion
3 0 KTL_status3
Kontrollleuchten Status 3
1
8 0 KTL_DTR
Kontrolleuchte Distronic
8 1 -
Reserve
4 0 TWS_Value
Tageswegstrecke
16 4 0 TWS_Value
Tageswegstrecke
6 0 GNG_Prog
Ganganzeige Fahrprogramm
8
6 0 GNG_Prog
Ganganzeige Fahrprogramm
7 0 GNG_Stufe
Ganganzeige Fahrstufe
8
7 0 GNG_Prog
Ganganzeige Fahrstufe
8 0 ATP_Value
Aussentemperatur
8
3 0 ATP_Value
Aussentemperatur
Historisch, ersetzt durch MGU00000030 (2012-08)
Dokument historisch/Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-23)


### 第 8 页
MBC 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
AV E/E-Funktionstests 
A 169 000 49 99 
 
Bearb./auth.: Weimer 
 
Abt./dep.: EP/ EKE 
 
Datum/date: 11-27-04 
 
ZGS : 001 
Auftr.-Nr./order  no.: YAP28121/07 
 
Seite/page: 8 von 12 
 
 
4 
Diagnoseanforderungen 
 
4.1 
Allgemein 
 
Die Diagnosefunktionalität ist über die im Hause Daimler standardisierte Diagnoseumgebung CAESAR/DIOGENES zu 
gewährleisten. Deshalb müssen bereits zum Steuergeräte-B-Muster die realisierten Diagnosefunktionalitäten in 
DIOGENES bedatet sein und die daraus resultierenden CBF-Dateien mit dem Steuergerät mitgeliefert werden. Die 
Erstellung der DIOGENES-Bedatung bzw. der CBF-Dateien ist zwischen dem Steuergeräteverantwortlichen und dem 
Zulieferer abzustimmen. 
 
4.2 
B-Musterstand 
 
Eine verbindliche schriftliche Beschreibung  der Diagnoseumfänge für das B-Muster in Form einer SG-spezifischen 
Diagnosespezifikation muß zeitgleich zum SG-Lastenheft, dem Ansprechpartner für HIL Systemintegration vorliegen. 
 
Die Implementierung allein der Umfänge Fehlerspeicher Lesen und Löschen reicht nicht aus, um die HIL-Testsysteme 
bzgl. der Fehlersimulation zu spezifizieren und entwickeln zu lassen. Angaben über Fehlersetz- und 
Rücksetzbedingungen sind unbedingt notwendig, um die Entwicklung der Fehlersimulationshardware auf den SG-
Interfaceschaltungen zu ermöglichen. Die Informationen sind zudem für die Entwicklung der Testprogramme für den 
automatisierten Test der SG-Diagnoseumfänge während der HIL-Testphase für B-Muster erforderlich. 
 
Die Input/Output Control Funktionalität ist für die elektrische Inbetriebnahme der Steuergeräte am HIL-
Systemintegrationstestsystem erforderlich. Hierzu gehört das Ansteuern von einzelnen Ausgängen zur Einstellung von 
elektronischen Lasten am HIL-Testsystem oder das Einlesen und Verifizieren von simulierten Sensorsignalen.  
 
 
4.3 
C-Musterstand 
 
Die Implementierung der Seriendiagnoseumfänge muss bis spätestens zum Meilenstein ‚Erste C-Muster gehen in 
Erprobung’ erfolgen. Dies ist erforderlich, um eventuelle Anpassungen an der Fehlersimulationshardware 
vorzunehmen und im Rahmen der HIL-Testphase für C-Muster einen automatisierten Test der SG-Diagnoseumfänge 
durchzuführen. 
Historisch, ersetzt durch MGU00000030 (2012-08)
Dokument historisch/Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-23)


### 第 9 页
MBC 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
AV E/E-Funktionstests 
A 169 000 49 99 
 
Bearb./auth.: Weimer 
 
Abt./dep.: EP/ EKE 
 
Datum/date: 11-27-04 
 
ZGS : 001 
Auftr.-Nr./order  no.: YAP28121/07 
 
Seite/page: 9 von 12 
 
 
5 
Informationsbedarf für die Steuergeräteinbetriebnahme 
 
Bei der Inbetriebnahme am HIL und am Fahrzeug werden zu jedem Steuergerät allgemeine Informationen sowie die 
Dokumentation der Hard- und Software benötigt, die der Auftragnehmer liefern muss. Die Auflistung der Inhalte findet 
sich ebenfalls im Kapitel *******.3 des Standardlastenhefts. 
Zu den allgemeinen Informationen gehören der Lieferant mit dem Ansprechpartner und Kontaktdaten, sowie die 
Angaben über HW- und SW-Stand, Diagnose-Version, K-Matrix Version und die SG-Nummer. 
Wichtigste Angabe zur Hardware ist das Pinning sowie alle Änderungen im Vergleich zum vorigen Stand.  
Die Dokumentation der Software muss die beseitigten Fehler enthalten. Hierin sind auch die vom Lieferanten selbst 
gefundenen und beseitigten Fehler enthalten. Für die weitere Testplanung werden zudem der realisierte Rollout der 
Funktionsbeiträge des SGs benötigt. Zu diesen Funktionsbeiträgen gehört auch die Diagnose. 
In diesem Kapitel ist ein Steuergerätedatenblatt mit Informationen am Beispiel des Kombi169 abgebildet.  
Für jedes neue Steuergerät bzw. jeden neuen Softwarestand, der am HIL oder am Fahrzeug in Betrieb genommen 
werden soll, sind vom Lieferanten über den Steuergeräteverantwortlichen die hier aufgeführten Informationen 
bereitzustellen. Dabei sind die Inhalte wichtig. Das Format muß nicht zwangsläufig mit dem hier gezeigten 
Steuergerätedatenblatt identisch sein. 
 
SG Datenblatt
Ort: SindelfingenDatum: 16.12.2001 13:44
SG:
Kombiinstrument
HW-Stand:
47/01
SW-Stand:
51/01
Lieferant:
Robert Bosch GmbH
Diagnose-Version:
$8b01
Ansprechpartner:
     
K-Matrix Version:
17/01
Telefon:
     
NM-Version:
..........
Fax:
     
SG-Nummer:
..........
eMail:
     
SG ist flashbar:
1. Pinning
Pin.-Nr.
Beschreibung
2
Kl_31
5
Kl_30
6
Kl_15g
7
Tank +
8
Tank –
9
Außentemperatur +
10
Außentemperatur –
12
CAN_C Low
13
CAN_C High
14
Kl_58k
16
Kl_58d
17
CAN_B Low
18
CAN_B High
     
 
 
 
 
 
 
2. Realisierte Funktionen 
 
Historisch, ersetzt durch MGU00000030 (2012-08)
Dokument historisch/Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-23)


### 第 10 页
MBC 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
AV E/E-Funktionstests 
A 169 000 49 99 
 
Bearb./auth.: Weimer 
 
Abt./dep.: EP/ EKE 
 
Datum/date: 11-27-04 
 
ZGS : 001 
Auftr.-Nr./order  no.: YAP28121/07 
 
Seite/page: 10 von 12 
 
Lfd.-Nr. 
Beschreibung 
1 
Drehzahlmesser 
2 
Tachometer 
3 
Tankanzeige MAPPS 
4 
Kühlmitteltemperaturanzeige 
5 
ESP Infoleuchte 
6 
Dieselvorglühlampe 
7 
Uhr 
 
 
 
 
3. Beseitigte Fehler 
 
 
 
a.) Beseitigte Fehler, die in der Fehlerdokumentation enthalten sind 
 
Fehler-ID 
Fehlertitel 
2 
Keine Fernlichtanzeige im KOMBI 
3 
Uhranzeige flackert 
10 
Tankanzeige im Bezug auf Sensor invertiert 
11 
Die Geschwindigkeit wird nicht korrekt nach dem 
Tachoalgorithmus angezeigt 
 
 
 
 
 
b.) Beseitigte Fehler, die in der Fehlerdokumentation nicht enthalten sind 
 
    
Lfd.-Nr. 
Fehlertitel 
1 
 
 
 
4. Realisierte Diagnosefunktionen 
 
Funktion 
SID/LID
Bemerkungen 
 
 
 
 Start Diagnostic Session 
$10 92 
      
 Tester Present 
$3E 02 
      
 Diagnostic Session beenden 
$10 81 
      
 ID-Block lesen 
$1A 86 
      
 Speicherblock lesen 
$21 xx 
      
 Speicherblock schreiben 
$3B xx 
      
 Fehler lesen 
$18 xx 
      
 Fehlerumgebungsdaten 
$17 
      
 Fehler löschen 
$14 xx 
      
 I/O Control 
$30 xx 
      
 Routine 
$31 xx 
      
 SG-Entwicklungsstand 
$21 E0 
      
 SG-Seriennummer 
$21 E1 
      
 Disable Normal Message Transmission 
$28 01 
      
 Enable Normal Message Transmission 
$29 01 
      
 Standby-Modus 
$10 89 
      
 
Historisch, ersetzt durch MGU00000030 (2012-08)
Dokument historisch/Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-23)


### 第 11 页
MBC 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
AV E/E-Funktionstests 
A 169 000 49 99 
 
Bearb./auth.: Weimer 
 
Abt./dep.: EP/ EKE 
 
Datum/date: 11-27-04 
 
ZGS : 001 
Auftr.-Nr./order  no.: YAP28121/07 
 
Seite/page: 11 von 12 
 
6 
Komponentenbereitstellung 
 
Inbetriebnahme/Pretesting: 
Die ersten Fahrzeugintegrationstests am HiL starten mit der Pretesting-Phase, der Terminplan wird für jede Baureihe 
vom HiL-Systemintagrationsteam erstellt, in die Releaseplanung eingebunden und an die Steuergeräteentwickler 
kommuniziert. Zu diesem Zeitpunkt sind erste Teile in B-Muster Qualität vom Auftragnehmer über den 
Bauteilverantwortlichen abzugeben. Vor der Pretesting-Phase findet die Inbetriebnahme des Prüfstandes statt.   Die 
hierfür erforderlichen Steuergeräte sind nach Abstimmung mit den Ansprechpartnern der HIL Systemintegration zur 
Verfügung zu stellen. 
 
Releasetaktung und Regressionstest: 
Ausgenommen die Pretesting-Phase richten sich die Anliefertermine der Steuergerätesätze nach der offiziellen 
Release-Taktung. Mit diesem Stand der Steuergeräte (mit dem für die jeweilige Phase vereinbarten Funktionsumfang) 
findet dann die vierwöchige  Regressionstest-Phase statt, um die Fehlerbeseitigungsmaßnahmen zu überprüfen und 
den Reifegrad der Steuergeräte vor dem Fahrzeugaufbau zu ermitteln. Der Steuergeräteverantwortliche muß 
sicherstellen, dass die erforderlichen Steuergeräte zu den festgelegten Abgabeterminen vom Auftragnehmer zur 
verfügbar gestellt werden. 
Nach den Regressionstests werden die Ergebnisse dem E/E-Paten präsentiert. 
 
 
Historisch, ersetzt durch MGU00000030 (2012-08)
Dokument historisch/Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-23)


### 第 12 页
MBC 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
AV E/E-Funktionstests 
A 169 000 49 99 
 
Bearb./auth.: Weimer 
 
Abt./dep.: EP/ EKE 
 
Datum/date: 11-27-04 
 
ZGS : 001 
Auftr.-Nr./order  no.: YAP28121/07 
 
Seite/page: 12 von 12 
 
 
 
 
7 
Abkürzungsverzeichnis 
 
ADC 
Analog Digital Converter 
CAN 
Controller Area Network 
DAC 
Digital Analog Converter 
DAI 
Daimler AG 
DBKOM Daimler-Benz Kommunikation 
DFT 
Design-for-Testability 
DIO 
Digital Input and Output 
E/E 
Elektrik / Elektronik  
EMV 
Elektromagnetische Verträglichkeit 
EP 
Entwicklung PKW 
HIL 
Hardware-in-the-Loop 
HW 
Hardware 
I/O 
Input/Output 
LCD 
Liquid Crystal Display 
LED 
Light Emitting Diode 
MBC 
Mercedes Benz Cars 
MDS 
Mercedes-Benz Development System 
OSEK 
Offene Systeme und deren Schnittstellen für die Elektronik im Kraftfahrzeug 
QG 
Quality Gate 
SG 
Steuergerät 
SIL 
Software-in-the-Loop 
SW 
Software 
 
 
 
Historisch, ersetzt durch MGU00000030 (2012-08)
Dokument historisch/Unkontrollierte Kopie bei Ausdruck (: Yiping Huang, 2013-08-23)

