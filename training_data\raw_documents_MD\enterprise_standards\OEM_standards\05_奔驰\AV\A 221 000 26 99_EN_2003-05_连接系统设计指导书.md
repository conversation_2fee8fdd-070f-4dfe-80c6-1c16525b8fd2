# A 221 000 26 99_EN_2003-05_连接系统设计指导书.pdf

## 文档信息
- 标题：Microsoft Word - common design guideline ver 2-1_3.doc
- 作者：marcos
- 页数：41

## 文档内容
### 第 1 页
 
DaimlerChrysler / Mitsubishi 
 
Design Guidelines for  
Connection Systems  
 
Version 2.1.3 
 
For information only, not replaced in case of revisions. 
 
Pages 1 to 40 
 
This product version specification (AV) is model-independent and component-
independent.  
This product version specification is released in the product documentation system 
DIALOG. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
State of revision / revision text 
CAD 
 
ZGS 
 
Order no. 
Author 
Date 
Check. 
Date 
Stand. 
Date 
Order no.   
YAPXXX 
System 
MS Word 97 
Resp. dep.  
EP/EKK 
ZGS   
- 
ED-KB   
PY 
CAD     
--- 
Date 
Name 
Auth. 
2003-05-03 Merget 
Check. 
2003-02-20 Eck 
Stand. 
 
 
Title  
Design Guidelines for Connection Systems  
Rel. 
2003- - 
 
 
 Mercedes-Benz 
© DaimlerChrysler AG 
Refer  to  protection  notice   DIN 34! 
Size 
A4 
Sheet 
1 
Basic number 
A 221 000 26 99 
Any   alterations   are   subject   to   the   approval   of   the   design   department. 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 2 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
1 of 1 
 
State of revision / revision text 
 
Date 
Page, 
(moved, if 
applicable) 
Section 
Author 
Change 
25.07.2002  
 
Merget 
First version for MB-cars 
12.2002 
 
 
Merget, 
Naomitsu, 
Zaverzence, 
Tabor 
First common version for DC / Mitsubishi 
05.2003 
 
 
Merget, 
Zaverzence, 
Tabor 
Rework of details  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Please address inquiries on the requirement specification to the EP/EKK department. 
This department also manages the updating of the requirement specification. 
The contact persons at DCS, Germany, EP/EKK, HPC X973, are:  
 
Mr. Eck 
 
Tel. 
+49 - 07031/90-41091  
jochen.eck@DC/MMCx.com 
 
Mr. Merget 
 
Tel.  
+49 - 07031/90-41096  
peter.merget@DC/MMCx.com 
 
 
 
 
 
The contact persons at DCA, USA, are:  
 
Mr. Bazzi 
 
Tel. 
+1  ************ 
 
aab5@DC/MMCx.com 
 
Mr. Zaverzence 
Tel. 
+1  ************ 
 
 <EMAIL> 
 
Mr. Tabor 
 
Tel. 
+1  ************ 
 
 <EMAIL> 
 
 
The contact persons at MMC, Japan, are:  
 
Mr. Kawai 
Tel. 
+81  564 32 5228 
 
 
 
 
 
 
  
 
<EMAIL> 
 
Mr. Adachi 
Tel. 
+81  564 32 5216 
 
 
 
 
 
 
  
 
<EMAIL> 
 
 
  
 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 3 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
2 of 2 
 
Table of contents 
 
 
State of revision / revision text................................................................................................................................1 
1 
Project data .........................................................................................................................................................4 
1.1 
Subject of this requirement specification................................................................................................... 4 
1.2 
Start of development ................................................................................................................................. 4 
1.3 
Goals.......................................................................................................................................................... 5 
2 
Design of connector parts .................................................................................................................................6 
2.1 
Definitions ................................................................................................................................................. 6 
2.1.1 
Function of the connector components.............................................................................................. 6 
2.1.2 
Scoop proof........................................................................................................................................ 7 
2.1.3 
"No insertion success" ....................................................................................................................... 7 
2.2 
General specifications................................................................................................................................ 7 
2.2.1 
Design concept .................................................................................................................................. 7 
2.2.2 
Families.............................................................................................................................................. 8 
2.2.3 
Pitch and Row dimensions.................................................................................................................. 9 
2.2.4 
Voltage levels..................................................................................................................................... 9 
2.2.5 
Second source (for feed material) ...................................................................................................... 9 
2.3 
Signal-conducting components (terminals) .............................................................................................. 10 
2.3.1 
Contact area (area I)......................................................................................................................... 11 
2.3.1.1 
"Insertion" type ...................................................................................................................... 12 
2.3.1.2 
"Screw" type (eyelet terminals) .............................................................................................. 13 
2.3.2 
Mech. fastening area (area II)........................................................................................................... 13 
2.3.2.1 
"Insertion" type ...................................................................................................................... 14 
******* 
"Screw" type........................................................................................................................... 14 
2.3.3 
Non-detachable area (area III) .......................................................................................................... 15 
******* 
Terminal to wire attachment................................................................................................... 15 
******* 
Multiple connections.............................................................................................................. 16 
******* 
Splices ................................................................................................................................... 17 
******* 
Cables .................................................................................................................................... 17 
******* 
Processing / processing tools................................................................................................ 17 
******* 
Terminal on PCB..................................................................................................................... 18 
2.3.4 
Labeling............................................................................................................................................ 18 
2.3.5 
Handling properties.......................................................................................................................... 19 
2.3.6 
Material properties........................................................................................................................... 19 
2.3.7 
Electrical and climatic properties ..................................................................................................... 19 
2.3.8 
Optical transmission properties........................................................................................................ 19 
2.4 
Connecting systems (housing) ................................................................................................................. 19 
2.4.1 
Removal/ mounting / assembly (e.g. for service) ............................................................................ 20 
2.4.2 
Locking mechanism (housing to housing)......................................................................................... 20 
2.4.2.1 
Basic requirements ................................................................................................................ 20 
2.4.2.2 
Frictional/positive connections.............................................................................................. 21 
******* 
Actuation................................................................................................................................ 22 
2.4.2.4 
Unintentional actuation protection / snag protection / expansion protection....................... 23 
2.4.2.5 
Clicking sound........................................................................................................................ 23 
2.4.2.6 
Insertion and withdrawal aids.............................................Fehler! Textmarke nicht definiert. 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 4 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
3 of 3 
 
2.4.2.7 
Primary interlock (PL)............................................................................................................. 24 
2.4.2.8 
Secondary interlock (SL) ........................................................................................................ 24 
2.4.3 
Coding / polarization ....................................................................................................................... 25 
2.4.4 
Contact protection of signal-conducting components ...................................................................... 26 
2.4.5 
Sealing systems ............................................................................................................................... 27 
******* 
Purpose.................................................................................................................................. 28 
2.4.5.2 
Color assignment (tbd)........................................................................................................... 29 
******* 
Protection for sealing elements.............................................................................................. 29 
******* 
interface seal.......................................................................................................................... 29 
******* 
Wire seal ................................................................................................................................ 29 
******* 
device sealing......................................................................................................................... 30 
2.4.6 
Labeling............................................................................................................................................ 30 
2.4.7 
Living hinges .................................................................................................................................... 31 
2.4.8 
Fastening concept (tbd).................................................................................................................... 31 
2.4.9 
dress cover....................................................................................................................................... 31 
2.4.10 Twist protectors (screw connections)............................................................................................... 32 
2.4.11 EMC ................................................................................................................................................. 32 
2.4.12 Geometrical properties..................................................................................................................... 32 
2.4.13 Electrical properties ......................................................................................................................... 33 
2.4.14 Material properties........................................................................................................................... 33 
2.4.15 Mechanical properties...................................................................................................................... 33 
2.4.16 Ergonomic properties ....................................................................................................................... 33 
2.4.17 Climatic properties........................................................................................................................... 33 
2.4.18 Chemical properties ......................................................................................................................... 33 
2.4.19 Visual properties .............................................................................................................................. 33 
2.4.20 Optical transmission properties........................................................................................................ 34 
2.5 
Additional parts........................................................................................................................................ 34 
2.5.1 
Shorting bar ..................................................................................................................................... 34 
2.5.2 
Transport protective caps................................................................................................................. 34 
2.5.3 
Auxiliary materials............................................................................................................................ 34 
3 
Documentation................................................................................................................................................. 35 
4 
Quality assurance............................................................................................................................................ 36 
4.1 
Product and process responsibility .......................................................................................................... 36 
4.2 
FMEA ....................................................................................................................................................... 36 
4.3 
(Re-)qualification...................................................................................................................................... 37 
4.4 
Controll of the series production.............................................................................................................. 37 
5 
Appendix ........................................................................................................................................................... 38 
5.1 
Definition of terms ................................................................................................................................... 38 
5.2 
Other applicable documents.................................................................................................................... 39 
5.3 
Overview of families (proposal)................................................................................................................ 39 
5.4 
Part history (proposal) ............................................................................................................................. 40 
 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 5 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
4 of 4 
 
1 
Project data 
1.1 
Subject of this requirement specification 
The subject of this requirement specification is:  
• 
Signal/energy-conducting components (terminals)  
• 
Connector housings  
• 
connector systems (housing and terminals)  
The components can be used with: 
• 
Device side (control units)     
• 
Wiring harnesses side 
• 
The development of one or more components is considered to be a project. 
 
The basic technical specifications on the development of connector parts are stated in this standard 
contacting system requirement specification. Product or project-specific details (schedules, quantities, 
etc.) and deviations from the present document are stated in separate project-specific contacting system 
requirement specifications. In the event of conflicting information from the standard contacting system 
requirement specification and the project-specific contacting system requirement specification, the 
specifications of the project-specific contacting system requirement specification apply. 
Ranking of the documents applicable in a project: 
1. Drawing (with cross reference to the product and processing specifications by the given 
manufacturer) 
2. Project-specific requirement specification 
3. Requirement Specification – Contacting Systems 
 
All rights reserved. Changes due to technical progress are at all times possible. 
1.2 
Start of development  
• 
The requesting source is DaimlerChrysler AG or Mitsubishi-Motors Company  (henceforth: DC/MMC).  
• 
The purchase department represents DC/MMC concerning cost-related specifications.  
• 
The engineering  team represents DC/MMC concerning all technical specifications for projects.  
• 
DC/MMC is represented by other departments or teams concerning wiring harness or contacting 
related components or device units.  
• 
The contractor is a manufacturer of terminal parts (henceforth: terminal manufacturer) or a 
manufacturer of housing parts (henceforth: contacting system manufacturer).  
• 
For manufacturers of wiring harnesses or wiring harness related components (henceforth: 
processors), this requirement specification is a co-applicable document. 
• 
All information, technical interim states, completion of open items (daily business) must be 
coordinated with the requesting source.  
• 
This requirement specification is the mandatory basis for all further developments. The specifications 
apply to the vehicles from DC/MMC. 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 6 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
5 of 5 
 
• 
Before beginning the new construction of components, the manufacturer must check whether 
equivalent components are already available (carry-over potential). All changes in carry-over parts 
must be coordinated with DC/MMC! 
• 
Orders must be sent immediately according to the applicable DC/MMC regulation on contract 
awarding to the relevant purchasing department of DC/MMC and to the contacting system 
development department of DC/MMC. 
• 
The purchasing department must be notified of the relinquishment of the right of usage or the sale 
from DC/MMC tools. 
1.3 
Goals 
The most important goals of the development of connector parts are: 
• 
Fulfillment of the technical prerequisites for wiring an on-board electrical system and integrating this 
system in the vehicle 
• 
Development of standard connector systems; if possible no application-specific connector systems 
(best way: shelf item parts) 
• 
Search for solutions optimal with respect to installation space 
• 
Search for cost-optimized solutions (in manufacture and processing) 
• 
Traceability of possible faults 
 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 7 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
6 of 6 
 
2 
Design of connector parts  
2.1 
Definitions 
2.1.1 
Function of the connector components 
The functions of the connector components are: 
• 
the signal and/or energy transfer properties,  
• 
the preservation of these properties after the integration of the components in the vehicle under the 
load conditions occurring there. 
In this regard there are 2 component groups: 
The functions of the signal conducting components (terminals) are: 
• 
the signal and/or energy transfer properties 
• 
the stability of these properties under ambient conditions given the corresponding operating 
parameters  
• 
the properties of the corresponding interfaces to lines, printed circuit boards, etc. (also regarding 
processing). 
The functions of the contacting systems (housing parts) are: 
• 
process-reliable fixing and holding of the signal-conducting components and preservation of their 
functioning or assurance of their nonfunctioning (e.g. in the case of a secondary lock that is not 
actuated) 
• 
process-reliable fixing and holding of the contacting system components relative to one another if 
necessary, including implementation of the protection against dust and water (e.g. leaktightness, 
contact protection)  
• 
process-reliable fixing and holding of the contacting system relative to the vehicle 
• 
according to corresponding environment parameters and operating parameters.  
 
The functions (separated according to properties and stability) shall be parameterized as is possible. The 
functional parameters must be verified in suitable form through measurements (according to DC/MMC 
test guideline).  
The functional parameters of a signal-conducting component are thereby defined in terms of the 
properties of the operating parameters (current carrying capability, voltage level with corresponding 
requirements, transition impedance, etc.), the stability with respect to the environment parameters 
(temperature, vibration profile, etc.) and the transfer reliability of the signals or energy (no interaction 
with other signals via current draw, EMI, etc.). 
The functional parameters of a contacting system ensure the assembly processes (e.g. scoop proof, 
clicking sound) and ensure - with optimal implementation - the full satisfaction of all functional 
parameters of the signal-conducting components. In general, the function (and therefore the functional 
parameters) of the signal-conducting components are restricted by the contacting system (e.g. derating 
curve with neighboring power pins or restriction of the transfer reliability through material-related current 
draws). 
DC/MMC's requirements on the functional parameters are defined in the following chapters. 
The manufacturer must explicitly document the particular guaranteed functional parameters (in the 
product specification and in the qualification report). 
Comment: 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 8 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
7 of 7 
 
The separation between the functions of the signal-conducting components and the functions of the 
housing parts is expressly desired. 
2.1.2 
Scoop proof  
All Interface drawing and all derived contacting systems must be scoop proofed! 
Design of  the connectors should to provide proper alignment of the mating connectors before terminal 
engagement. This provides good terminal alignment and makes the connector “scoop poof”. Design the 
female housing to come no closer than 1,0 mm to the male terminals during worst case mis-insertion 
angle (see drawing below). A combination of CAD, SLA and prototype part studies may be required to 
demonstrate fulfillment to this requirement. 
1.0 mm
 
2.1.3 
"No insertion success"  
Definition of "no insertion success" (occurs several times in the document; see e.g. key-coding):  
• 
No mechanical locking (no holding ability), no clicking sound of the lock  
and 
• 
for each contact transition / contact no (no sufficient) signal transfer and therefore reliable detection 
of the absence of a function (at the latest at the end of line in the vehicle). 
• 
With electrical plug connections: no contact overlay   
• 
With optical plug connections: high auxiliary damping ( >10 dB) 
2.2 
General specifications 
2.2.1 
Design concept 
All known boundary conditions of conventional contacting systems must be fulfilled even with new 
applications and new technologies (e.g. LWL, film technology)  
Properties to be avoided / impermissible designs: 
• 
No exposed contact areas (contamination) 
• 
No freely accessible seals 
• 
No pressure contacts that can be contacted through axial contact pressing (e.g. spring pins at the 
trunk lid → Reason: high mechanical stress) 
• 
No round contacts with new developments with standard stress 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 9 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
8 of 8 
 
• 
Housing latch / no unprotected snap arms (without rear hook protection) 
• 
No grease as water protection (filling of the contacting system with grease) 
• 
No locking according to the frictional connection principle 
Properties to be implemented: 
• 
Machine-capable design 
2.2.2 
Families 
Subject: housing parts on the wiring harness side and on the device side 
Purpose:   
• 
Carry-over potential:  
Goal of "standard connector systems" having the potential across all model series for high volumes 
(see the section on "Goals") 
• 
Development of connector system:  
Reduction of the developmental expenditure on the part of the manufacturer and DC/MMC through 
recycling of previously tested contacting features 
• 
Development of wiring harness and device units:  
Reduction of the developmental expenditure through takeover of existing components and tested 
concepts concerning male and female housing and headers.  
Features: 
All family members must have the same design features. 
The technical features of a family are: 
• 
same signal-conducting components (terminals) and correspondingly the same locking mode of the 
terminals (primary) 
• 
same secondary locking mode of the contacts in the coupling (e.g. side or rear-locking with separate 
piece or hinged) 
• 
same locking of the pin connector for the coupling (locking geometry, insertion and withdrawal aids, 
actuation during loosening, etc.) 
• 
same coding principle (A-, B-, ... , Z-coding) and identical polarization 
• 
same contact numbering (either in series or zigzagged)  
• 
same voltage level 
• 
if necessary same sealing concept 
• 
same fastening principle in the vehicle 
• 
same line types (round/film) or processes (reflow soldering) 
Example: The most important design features must therefore match within a family; i.e. in the simplest 
case: cut apart the drawing of a 4-pin part and insert a few contacts "in-between" to obtain a new 
connector with more pins. 
Family specification: 
Before the manufacturer can produce and update a family specification, the design must be discussed 
with DC/MMC in case of changes and provide the information as required. 
The family specification must contain 4 parts: 
1. Name of the family 
The families are given names.  
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 10 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
9 of 9 
 
2. Subject of the family 
The layout of the entire family must be planned from the start, even if at first not all family members 
are implemented (pin numbers, mechanical dimensional variations, coding variants, etc.). 
3. Implemented properties of the features 
The implemented properties must be explicitly documented. 
This documentation must allow expansion of the family (even after the change of an administrator) 
without increased design expenditure. 
All family-related special features must be described in a reproducible manner (such as decreased 
wall thickness, reinforcements, locking details, special features with the cable port, details of the seal 
design, rounding off of edges, special angle geometries, etc.). 
4. Overview of the family members  
The manufacturer must have a tabular overview of the entire family. Components not yet implemented 
must then be crossed out.  
This overview - separate from the specification - serves as the working document at DC/MMC 
(connector team only). 
A sample of the table is contained in the appendix. 
In case of changes of a current component from the family (e.g. due to a production problem), the other 
family members must be run through the same change, as soon as the change has been tested on the 
first component. At the latest the change must be phased in when the die is duplicated. The goal is to 
preclude recurring faults in similar components. 
With each new development of contacting systems, the manufacturer must ensure the expandability of 
the design into a family. Design features affecting (or even preventing) the expandability of a component 
into a family must be indicated to DC/MMC.  
2.2.3 
Pitch and Row dimensions 
The standard values of pitch and row dimensions must be maintained (e.g. 2.54 mm) or multiples thereof.  
2.2.4 
Voltage levels 
The usability of 42V must be ensured for new developments. The components must be checked with 
respect to pitch and row dimensions, tracking distances, etc. and documented according to the product 
specification. The following definitions apply (based on VDE 0100 and VDE 0110): 
1. Contamination class (depending on the housing design)  
 
sealed:  
Class 1 (no influence because of contamination) 
 
unsealed:  
Class 2 (no influence only dew-humidity) 
2. Measurement of impulse voltage: 0.8 kV 
3. Altitude correction factor:  
4000m NN  (altitude over sea-level) 
4. Neutral voltage:  
58 V  (maximum voltage) 
2.2.5 
Second source (for feed material) 
Material bottlenecks must be assured in advance (metal/plastic/nonmetallic coatings/auxiliary 
materials); for each material at least one second source must be technically assured by the manufacturer 
and if necessary demonstrated.  
In case of procurement problems with materials, alternative materials must be made available and 
presented to DC/MMC. In case of a long-term material changeover, the components must be requalified 
with the corresponding materials. 
 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 11 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
10 of 10 
 
2.3 
Signal-conducting components (terminals) 
The subject is therefore (e.g.): 
• 
the male or female terminal optionally on wiring harness or device unit side  
• 
fiber optic contacts 
• 
screens / shielding (e.g. from housing to housing with fully screened components) 
• 
FPC or FFC (flat wiring) line surface with direct contacting 
• 
short circuit jumpers (actuatable) between contacts 
• 
eyelet (with/without hole) and battery terminals   
Remark: A permanent connection in this regard is also a connection that must not be detached (e.g. 
soldered joint)! 
A signal-conducting component can be divided in 3 areas:  
• 
Area I : the contact area is the active connection between male and female; detachable connection  
• 
Area II: fastening of the contacts in mounts not relevant to signals (pin connector or wiring harness 
coupling) 
• 
Area III: contact area between contact and signal-related media (contact pcb or contact wiring 
harness cable); permanent connection  
In addition, the contacts are subdivided into the types "insert" "screw" and "clamp". The following figure 
illustrate the respective principles. 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 12 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
11 of 11 
 
 
Figure: Diagram of screw connection type, terminal points   
 
2.3.1 
Contact area (area I) 
Preliminary damage: 
Impermissible widening of the contact and all "preliminary damage" to the contact prior to the first 
insertion in the vehicle must be avoided. 
On the terminal and connector drawing provide access for the harness fixture continuity test probe in the 
connector housing assembly. Provide access for the probe through the front of the connector. Design the 
connector housing and probe so that the contact is not made with the terminal contact surface.  
Design the female and male terminal with sufficient lead-in, to guarantee the engagement without 
interference or “stubbing” at the endpoint. 
Design the female terminals with hoods, shrouds, or sleeves to protect the electrical contact(s). 
Surface coatings: 
Surface coatings of corresponding hardness must be firmly connected to the base material or the 
underlying layers. The coating must not become detached.   
Additional requirement (for all contact surfaces e.g. male, female, shorting bars):  
• 
"pore-free"/pore level  
• 
tbd: coat thickness: Gold>1 µm / Silver> 1-6 µm / Tin> 1-3 µm / Nickel (under Gold) > 0,5µm ?  
• 
description of the plating structures (e.g. intermetallic phase) 
• 
description of different production processes (electroplating, hot air level tin, etc.) 
• 
connection systems should use the same plating types on male and female terminals (tin, silver, gold) 
whenever possible.  
Contact overlap: 
Make connector and terminal tolerance stack-up studies during all stages of engage/disengage. Provide 
for a minimum 1,0 mm terminal engagement (excluding coined tip of male, see picture) in all positions 
between male and female terminals, when the housings are fully mated. 
I
I
I
AREA III
AREA I
AREA II
1
2
3
4
5
6
PV
SV
6
9
5
MA
MB
8
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 13 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
12 of 12 
 
Each supplier must perform a layout study of the mated assembly showing minimum /maximum terminal 
insertions tolerance stack-up, and worst case connector-to-connector alignment. 
The following information is required on all drawings: 
Interface-drawing of a male terminal (in relationship to surface coating):  
• 
specify the minimum (overlap)/maximum (insertion depth) contact point as of the coined tip 
Contact drawing:  
• 
Specify the contact point up to the primary interlock (PL) and secondary interlock (SL)  
• 
Maximum insertion depth of the blade to female 
Contact normal force: 
• 
The contact normal force in summary over all contact-points must be > 2N (during the entire 
lifetime). 
• 
Preferably the contact normal force should be generated exclusively through the interplay of metallic 
components. 
• 
The design must ensure that temporary changes in the contact normal force (e.g. through vibration 
profiles in the application) neither impermissibly limit the current carrying capability (or the transition 
impedance) of the two signal-conducting components, nor cause impermissible material damage (e.g. 
abrasion through the surface, sparks). 
• 
In case of deviations from the requirements, the contacting or contact manufacturer must state the 
special features, e.g. in the case of film direct contacting (the contact force via insulation material of 
metered goods), the contacting/contact manufacturer must specify verifiable requirements on the 
metered goods (e.g. thickness, flow characteristics, etc.). These requirements must be duly 
communicated to DC/MMC for informing the metered good manufacturers. 
 
Polarization: 
The interplay of the signal-conducting components must be designed as twist-proof.  
(Definition of "twist-proof": see the section on "Coding/polarization") 
2.3.1.1 "Insertion" type 
Insertion and withdrawal force: 
terminal to terminal (not housing - pin connector!) 
(min. table) requirement:  
< x N (tbd) 
Insertion cycles and surface design: 
The contact manufacturer must guarantee the following insertion cycles (included the electrical 
parameter after the insertion cycles): 
contact surface 
Min. insertion cycles  
(CCG, MMC) 
Insertion cycles 
(MGC) 
Tin 
10 
at least 20  
 
Silver 
10 
at least 50 
Gold 
10 
At least 100 
(Remark: see test-performance-spec; see also chapter 6) 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 14 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
13 of 13 
 
Chamfer insertion angle 
The contact manufacturer must specify in the product specification up to what maximum chamfer 
insertion angle the contact is suitable (see section on "Insertion safety"). After insertion in the housing, 
the chamfer insertion angle of the contact must be maintained by the design as a minimum. 
2.3.1.2 "Screw" type (eyelet terminals)  
different types: 
1) “weld stud” (practice in Germany and US) 
2) “weld nut”  (practice in Japan and US) 
3) “mechanical joint nut” (practice in Japan(for antenna) and US) 
4) “paint cutting screw” / “sheet metal screw” (practice in Japan and US) 
For application and performance criteria refer to the appropriate grounding specification.   
 
Special features regarding the contact normal force: 
Since the contact normal force generally also represents the final connection between the wiring harness 
and the opposing side (e.g. bolts on the device unit or vehicle side), the requirements on primary locking 
mechanism and secondary locking mechanism must also be fulfilled. 
The contact area is precisely the area on which the applied force of the screw acts. Signal-conducting 
connections outside this area are "accidental connections"; they must be avoided during testing (e.g. 
testing of the current carrying capability, vibrations). 
The signal transmission (current flow) must not be routed through the screw, since the screw surface is 
not uniquely defined (the connection through the screw counts as an "accidental connection"). The signal 
transmission therefore occurs only directly, via the screwed components (see figure: from the cable lug 
(2), through an additional cable lug (not shown), through an optional socket (3), onto the stamped grid 
(4)). 
Torque (primary interlock): 
See MBN 10 130 parts 1-3 
Surface design (rule): 
See grounding specification. 
2.3.2 
Mech. fastening area (area II) 
The fastening area describes the connection of the signal-conducting component to the housing with 
contacting on the wiring harness and device unit sides. 
Tolerances for movement: 
Between contact areas in  the terminals: When signal-conducting components are interconnected, 
minimize relative motions in all directions at the contact transition between the components may occur 
(e.g. when the entire temperature range is run through).  
Terminal to housing: The participating terminal must not be fixed in position from the outside (housing), 
but sufficiently large relative motions must allowed on at least one side.  
Remark: The lurch play is generally allowed in the contacting system on the wiring harness side (contact 
chamber); the contact chamber must permit the contact to perform all (e.g. thermal) motions. Any 
deviation from this rule (even in dimensional form) must be noted and defined in the relevant product 
version specification of the housing! 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 15 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
14 of 14 
 
Detachable interlock (male and female terminal to housing) 
Each tang terminal must have a primary and a true secondary lock in the final application. The primary 
(PV) and secondary locking mechanism (SV) must act independently of one another. 
Each tangless terminal must have a primary and a secondary lock in the final application. 
Primary interlock types: 
• 
Active primary interlock at the contact ("tang" contact): if possible 2 locking springs, symmetrical to 
the contact  
• 
Passive primary interlock ("tangless" contact): the primary interlock is implemented through the 
plastic housing  
Note: the component cost of the plastic housing might effect the decision of the locking type. 
 
Other requirements: see section on "Locking mechanism". 
  
Types of fastening (in header): 
The fastening of the contacts must ensure that no processing can affect the mechanical position 
(including tolerances) of the contacts according to the product version specification or affect the 
tightness. 
• 
Pressed in: used for unsealed applications and sealed applications with low pressure differential. 
Press-out strength (in both axial directions) > 24 N 
• 
Insert molded: must be used for sealed applications with increased pressure differentials. Press-out 
strength (in both axial directions) > 150 N  
Remark: the pressure differential refers to the inner pressure of the control unit relative to the ambient 
pressure.   
 
2.3.2.1 "Insertion" type  
No sharp edges (see DIN 6784; for future revisions refer to international standards) 
A sufficiently good protection against damage (before assembly), overexpansion or overstress (for tang-
terminal only) and hooking must be implemented. 
******* "Screw" type 
Only metric screw connections may be used.  
Primary interlock: thread 
Secondary interlock: optional screw locking ring (lock nut)  
Twist protector of the bolt / over-torque-protection: 
The twist protector must absorb the tightening torque when the screw connection is tightened.  
Requirement: The twist protector torque must be greater than the maximum torque that causes 
destruction of the screw connection (bolt or thread "turned off"). 
Twist protector of eyelets: 
The eyelets must be protected against twist; provide an anti-rotation feature.   
  
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 16 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
15 of 15 
 
2.3.3 
Non-detachable area (area III) 
Connection technologies must be designed for the corresponding cable structure; restrictions must be 
specified by the manufacturer. 
Specifications with contacts must apply for all cables used by DC/MMC. Restrictions on the usability of 
cables must be specified by the manufacturer and coordinated with DC/MMC. In this case, functional 
cables must be listed in the product specification.  
The specification of connection technology comprises all required components and the corresponding 
processes (molds and process parameters). For each connection technology the corresponding product 
specifications and processing specifications must be produced and appropriately distributed, in order to 
ensure the process reliability of the technology. 
 
******* Terminal to wire attachment 
 
- Crimp to wire:  
During the crimp process, no material weaknesses (e.g. cracks in the terminal material) and no material 
compression (e.g. sharp-edged burrs) must occur at the terminal. The crimp tool (e.g. crimp applicator) 
must be correspondingly dimensioned and defined. The corresponding processing specifications must be 
produced.  
 
The crimping must be "gas-tight" ("gas-tight": between the strands there must not be any air gap into 
which a harmful gas could penetrate. Verification with the environmental simulation test (see test-
specification) without vibration). 
In the transition from the crimp to the contact part, no cracks must occur (testing according to DIN IEC 
60512-2 with recommended enlargement: 100 x or larger). (to be moved to test-spec) 
The crimp features for a quality assessment of the wire crimp area (cross section evaluation), of the 
insulation crimp (visual) and of the sealing crimp (visual) must be defined and documented by the contact 
manufacturer.                                                                                      
 
- Crimp to insulation of the wire: 
The insulation crimp has the following tasks (overlap, passby or B-crimp?):  
• 
holds the insulation in place 
• 
mechanical protection of the strands (bending of the cables is absorbed by the insulation crimp and 
not by the contact crimp) 
Requirements: 
• 
Geometry: All cable strands must be held in conductor crimp for process reliability (no loose strands) 
• 
no systematic effect on cables (e.g. buckling of cables) 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 17 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
16 of 16 
 
 
- insulation displacement 
Owing to its disadvantageous characteristics with the use of stranded cables (due to the weakening of 
the materials at the joint, among other factors), this technology is not employed by DC in the wiring 
harness.  
Any application must insure positive strain relief of the wire (to prevent accidental pull off at conductor) 
and the wire must have a special compressed structure (CAVS). 
- additional soldering 
soldering must be lead-free.  
Individual applications may require soldering over the terminals (e.g. battery cables, eyelets (only DC), 
sometimes in the engine compartment). 
- welding 
For each joining technology, the process capability must always be verified and the manufactured 
products qualified. 
Permissible are resistance and laser welding and friction welding depending on the procedure. 
Not permissible is ultrasonic welding. 
 
******* Multiple connections 
Remark: All specifications of the respective technologies defined in the preceding sections (e.g. for 
crimping) apply. 
Double or multiple crimping entails a conditional, process-dependent quality risk. The contact 
manufacturer and the processor are therefore required to maintain the appropriate care with respect to 
the specifications for processing and testing and with respect to their implementation. 
Different cable technologies must not be interconnected: a detachable connection must not 
interconnect: 
• 
Cu conductors and Al conductors  
• 
round conductors and FPC/FFC 
At terminals single and double crimp are permissible (according to the following table); the crimp must be 
gas-tight.  
At eyelets single and double crimp are permissible (according to the following table); the crimp must be 
gas-tight and multiple crimps and deviating cable combinations must be soldered over. 
These requirements apply to solder-free joints with double crimps with the use of U- or V-shaped open 
crimp contacts (see DIN EN 60352 part 2 with stranded wire conductors of 0.35mm² to 4mm² in cross 
section). 
The cables can be used in combination, with at most one cross section jump (see table).  
The manufacturer's specification of the terminals must include: 
• 
cross sections (see table) of the paired cables (according to DC/MMC standard); here the 
specifications of the insulation diameters of the cables must be heeded!  
• 
Material pairs (e.g. copper to copper)  
• 
Crimp applicators to be used. The recommended crimp tooling geometry’s must be defined (e.g. 
applicator types) on the released drawing of the terminals 
• 
Dimension of the resulting crimp (height, width)  
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 18 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
17 of 17 
 
 
Cross section combinations of stranded wire conductors in mm² 
 
Double stop 
            With 
From 
0.35 0.5 0.75 
1 
1.5 
2.5 
4 
 
Remark 
0.35 
X 
X 
- 
- 
- 
- 
 
 
JASO : 0,30 mm² 
0.5 
X 
X 
X 
- 
- 
- 
 
 
 
0.75 
- 
X 
X 
X 
- 
- 
 
 
JASO : 0,85 mm² 
1 
- 
- 
X 
X 
X 
- 
 
 
 
1.5 
- 
- 
- 
X 
X 
X 
 
 
JASO : 1,25 mm² 
2.5 
- 
- 
- 
- 
X 
X 
X 
 
JASO : 2,0mm² / 3,0mm² 
4 
- 
- 
- 
- 
- 
X 
X 
 
 
 
- 
- 
- 
- 
- 
- 
 
 
 
The overall cross section must lie within the specified crimp cross section area! 
 
Not permitted is the use of double wire crimps for: 
• 
coaxial cables  
• 
shielded cables  
• 
sealed contact systems (single cable sealing system)  
• 
engine and transmission applications (increased acceleration forces). 
 
******* Splices  
Splices needs to be specially release, in reference to the model series. 
 
******* Cables 
Minimum requirement: thin wall cable 
For multiple connections (double crimp, splice, etc.) only insulation-reduced stranded wire conductors 
(thin walled wires) may be used, but not solid conductors or coaxial cables.  
Permissible cable combinations must be defined by the contact manufacturer (as a maximum, however, 
the table in the section on "Multiple connections" applies).  
******* Processing / processing tools 
The terminal manufacturer must define the tools, including the processing specifications and processing 
or quality characteristics (e.g. grinding patterns) (based on DIN EN 60352 part 2).  
The processor must conform to the contacting system manufacturer's processing specifications. 
The processor must implement the terminal manufacturer's specifications regarding the insulation 
lengths and the location of the conductors in the crimp. Insofar as the processing tool has no effect on 
the introduction of the cable in the crimp area of the contact prior to crimping, the positional correctness 
of the conductor feed and the correct positioning of the cable (or cables) in the crimp must be assured 
according to the contact manufacturer's specifications. 
The processing characteristics for quality assurance (on the part of the terminal manufacturer) must be 
implemented and documented by the processor. 
The reliability of the entire crimp process must be ensured.  
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 19 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
18 of 18 
 
The terminal manufacturer must define the process parameters (crimpheight/-width) necessary for the 
processing procedure in the processing specification for all occurring or released connection variants 
(cross sections, material, cable number, etc.). The process parameters (e.g. "crimp window") describes 
the relation between the conductor pullout strength, the transmission property (e.g. electrical resistance) 
and the process regulating variable (e.g. crimp height). 
In the processing, "state-of-the-art" crimp monitoring is required for the partially or fully automatic crimp 
production. With manual crimping, the process reliability must be assured at least by a crimp inspection. 
All single wires must be located within the crimp area. No strands must project (no loops either). The 
process reliability must be verified. 
 
******* Terminal on PCB 
- soldering  
soldering must be lead-free (because current a new law in Europe). 
When mounted on printed circuit boards (e.g. through soldering), the pin connector must withstand the 
corresponding process undamaged and without affecting the process reliability (e.g. floating of the pin 
connector through the solder must be prevented). The soldering process must not thermally affect the 
components, even if electronic components are contained in the component to be contacted.  
The maximum processing parameters (temperature, time) must be specified on the drawing or the 
processing procedure must be stated (e.g. reflow capable). 
Pin connectors must be designed for a reflow process, insofar as the wave solder capability is not 
restricted (with large pin connectors).  
A mechanical uncoupling of the insertion and withdrawal forces and the soldering joint must be planned. 
No effects on the contact position / contact area (bent pins) 
With soldered joints the dimension of the solder rising is a quality criterion  
Requirement: at least 2/3 solder rising, meniscus, gloss, no shrink holes 
 
- press in  
tbd 
 
2.3.4 
Labeling 
All signal-conducting components must be labeled with the following information: 
• 
Designation of the manufacturer 
• 
Modification level of the terminal (unique tractability to the basic design drawing) or the production 
date (week and year) 
• 
Surface version of the contact: Each variant must be clearly identifiable; the identifying 
characteristics must be described on the drawing. 
• 
Crimp size range / cable diameter (including insulation and seal fastening) 
• 
Cavity identification of the die, preferably numbering or, with acute space problems, alternatively 
small bore holes in the die (not larger than a center punch mark, max. 0.1mm deep) for distinguishing 
the cavities. 
All labeling must be visible after processing (e.g. crimping). 
 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 20 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
19 of 19 
 
2.3.5 
Handling properties 
Design the terminal to be robust or protected to withstand the normal packaging, shipping and handling 
of the product, especially insertion to connector housing. 
 
2.3.6 
Material properties 
All material information must be specific and shown on the drawing. Every change of material must be 
reported. 
2.3.7 
Electrical and climatic properties 
All requirements in section 2.3 refer to the required temperature range. The manufacturer must verify the 
properties and stability of the signal-conducting components in the temperature range and guarantee 
them in the product specification. 
The basic material together with the surface coatings must be stable with respect to all properties and 
types of resistance through the entire lifetime in the temperature range:  
Temperature range, tin 
 
-40°C to +125°C  
Temperature range, silver 
 
-40°C to +140°C 
Temperature range, gold 
  
-40°C to +150°C 
The current carrying capability must be guaranteed through the derating curve (free in air) in the product 
specification. In case of restrictions due to housing designs, assembly variants of the housings or surface 
coatings, the different exemplary derating curves must be specified. 
2.3.8 
Optical transmission properties 
The transmission properties of optical terminals (POF: plastic optical fibers) must by specified by the 
contact manufacturer for the entire spectrum of the transmitted wavelengths. 
If the optically effective contact area is not provided and defined by the contact itself (that is, the optical 
area is not the subject of the contact drawing), but the optical contact area is generated by a processing 
procedure (temporally) following the contact manufacturing process, then besides the processing 
procedures the contact manufacturer must also describe the mechanical properties of the contact area 
that lead to the specified optical properties. 
Reference to:  
• 
Sections in Technical Specification KIN / D2B 
• 
MOST specification 
• 
Test guideline for optical contacting 
Each optical property must be specified by the manufacturer and processor by name and with 
parameters! No supplier is allowed a reference to or entry in a “level reserve”, “system reserve” or the 
like! These system reserves are defined exclusively by the automotive manufacturer!  
 
2.4 
Connecting systems (housing) 
This section concerns the plastic-related aspects of a connecting system  
• 
Male and Female Housing on the wiring harness side  
• 
Male Housing  on the device side  
• 
Female Housing  on the device side (only special cases) 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 21 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
20 of 20 
 
• 
Relay base / lamp socket 
• 
Housing / cover / contact support 
• 
etc. 
These specifications apply to all separate parts and to all assembled components  
 
2.4.1 
Removal/ mounting / assembly (e.g. for service) 
A housing design must be used that is process-reliable and mountable. Assembly must involve little 
friction, be easily understandable and uncomplicated, and assembly of an overall contacting system must 
be possible, without destruction, damage or impact on the functioning of any component. The 
corresponding assembly and disassembly steps must be documented in the corresponding processing 
specifications.  
Design all terminal and housings with sufficient lead-in, to guarantee the engagement without 
interference or “stubbing” at the endpoint. 
Unavoidable damage through the action of force (> 220 N) must lead to the total failure of the main 
function (signal/energy transmission) in the final state (in the car).  
The same applies to the disassembly (e.g. contact removal, reference to crimp tool)  
Automatic assembly capability is always desirable and to be implemented for the assembly of housing 
parts with contacts. 
Minimum assembly cycles in the relevant product specification must be ensured without affecting the 
functions  
(1 cycle = 1 installation and 1 removal) 
Assembly cycles of terminals:  
min. 5 assembly cycles 
Assembly cycles of insertion and withdrawal aids:  
min. 20 assembly cycles 
2.4.2 
Locking mechanism (housing to housing) 
Locking mechanism are detachable, strictly mechanically acting connections between components. 
2.4.2.1 Basic requirements 
Geometrical allocation (where is the lock lug and lock arm?):  
• 
Lock lug: on device side (generally on male housing side) 
• 
Lock arm: on wiring harness side (generally on female housing side) 
Locking mechanism must be preferably developed in the pre-locked position. In the delivery condition of 
the housings, the terminals must be insertable without an additional operation. The pre-locked part must 
survive each transport to the processor and secured against loss. 
Scanning of the secondary interlock with insertion (e.g. at the processor's test board) should be provided 
for by the design. 
Maximum insertion force, one contacting process:  
75 N (full loaded with terminals) 
CPA: 
Provide for CPA capability in the design where feasible without adversely affecting size, complexity 
and/or cost of the part. Before proceeding with the design, the connector supplier should contact the 
responsible engineer at DC/MMC or harness supplier to confirm the need for a CPA.  Airbag squib 
connectors based on the 11mm interface always require a CPA. 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 22 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
21 of 21 
 
Design the CPA so that it cannot be engaged until the connector is completely mated. Design the CPA so 
that it is capable of being preloaded on the connector housing. Make the CPA of a contrasting color to 
the connector housing (e.g. red). 
 
2.4.2.2 Frictional/positive connections 
There are 4 types of locking mechanisms: 
Snap lock (always preferred): 
The locking plane is perpendicular to the force vector or is inclined relative to the acting force so that the 
lock automatically "pulls shut" under stress. 
Force for opening the lock: F > 150 N (without actuation of the lock) 
 
 
 
 
 
 
 
 
 
modified snap lock / modified Go-NoGo (permissible only with contact supports per agreement): 
 
Modified Inertia lock (to be avoided if possible): 
For reducing the mechanical tolerances (reduce movement  or optical connectors) 
The locking plane is perpendicular to the force vector or is inclined relative to the acting force so that the 
lock automatically opens under stress. 
Force for opening the lock: F > 80 N (without actuation of the lock) 
Inertia lock (to be avoided if possible): 
The locking plane is inclined relative to the acting force (or rounded off) so that the lock automatically 
opens under stress. Such constructions are not permitted with new applications. 
Force for closing the lock: 
60 N < F < 100 N 
Force for opening the lock: 60 N < F < 100 N 
 (If frictional locking mechanism are specifically desired for individual projects, then the lock must reliably 
open as of the maximum opening force) 
 
 
 
 
.
.
1. 2. 3.
1.
3.
.
F
F
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 23 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
22 of 22 
 
 
Interlock table (minimum requirement between directly interacting components) 
                  From 
To 
Male / Female 
Terminal 
(primary 
interlock and 
secondary 
interlock) 
Terminal housing 
Per-staged housing 
Male Main 
housing 
Female Main 
housing 
Terminal housing 
Per-staged housing 
Snap lock 
 
Mod. Inerta lock (1) 
 
 
Male Main housing Snap lock 
Mod. inerta lock or  
Snap lock 
Snap lock (2) 
 
Female Main housing 
Snap lock 
 
Mod. inerta lock or  
Snap lock 
 
Snap lock  
 
Snap lock (2) 
 
Insertion and 
withdrawal aid 
 
(3) 
(3) 
Snap lock (6) 
 
Snap lock (6) 
 
Strain relief  
(4) (5) 
(3) 
(3) 
Snap lock 
 
Snap lock 
 
Cable port (5) 
 
(3) 
Frictional connection 
 
Frictional 
connection 
 
Frictial 
connection 
 
 
The goal with each interlock is a positive connection between the participating components. 
With: 
(1) To be assessed in the individual case: with modular assemblies of terminal housing and main 
housing; actuation direction perpendicular to the positive secondary interlock plane  
(2) with modular assemblies (A168 000 79 99 is to be used for serial elements) 
(3) Combination not permitted 
(4) From the perspective of the wiring harness  
(5) Implementation as required 
(6) Insertion and withdrawal aid as separate part has positive connection to the device unit / wiring 
harness (trivial) as well as at the actuation end in the two interlock geometries 
******* Actuation 
 
Types of housing to housing locking mechanism:  
The positioning of the locking mechanism is subdivided in types: 
Type 1 
Frictional connection only without actuation (not permissible) 
Type 2 
Interlock on both narrow sides  
Type 3 
Interlock at one broad side 
Type 4 
Interlock on both broad sides 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 24 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
23 of 23 
 
Type 5 
Interlock at one narrow side 
Type 6 
Bayonet-type lock  (twist lock): not allowed 
Type 7 
Screw lock (bolt lock) : not allowed 
 
 
 
 
 
 
2.4.2.4 Unintentional actuation protection / snag protection / expansion protection 
Each interlock must be protected against unintentional actuation (e.g. slightly recessed actuating 
elements).  
Unintentional actuation can be caused by staff during handling/installation of connected device units or 
even through long-term pressure (e.g. from insulating material). 
 
Wiring harness components in fabricated states in the wiring harness must not hook the cables or during 
fabrication damage the cables (no hooks, no cutting geometry)  
The locking mechanism must be protected with expansion protection: 
• 
Male housing to female housing: expansion protection for the interlock hooks, limitation to the 
maximum deflection of the lock arm during locking operation.  
• 
Terminal housing to main housing: with the action of force by the cables on the terminal housing or 
on the housing (without actuation of the lock), it must not be possible to release the lock by 
expanding the housing. If necessary, the terminal housing must be frictionally connected to the main 
housing (in the direction normal to locking).  
• 
Check "cutting" geometry’s for damage to transverse cables / no sharpe edges  
2.4.2.5 Clicking sound 
locking mechanism must produce a visual, audible and tactile feedback. In addition the housing to 
housing locking mechanism must make a clicking sound during locking (min. 75dB(A)) which is clearly 
audible in the assembly shops. 
Each locking operation must produce a clicking sound once only, to avoid a false indication of lockup 
(also applies to contact primary interlock). 
2.4.2.6 Mate assist systems  
Mate assist systems are always at contacting systems on the wiring harness side. They are generally 
lever mechanisms or sliders with beveled guides.  
The direction of actuation must always be collinear with the pin axes  
Pre/final locking positions: 
• 
Each Mate assist system must have a pre-locking position (open lock) and a final locking position 
(closed lock). Both locks are positive connections 
• 
Both positions must produce the clicking sound 
• 
If the Mate assist system is not in the "open" position, then a contacting attempt must not lead to 
successful insertion. Only if the Mate assist system is in the "open" position, may the contacting 
attempt lead to successful insertion. 
                        4                                                     3    
 
 2                                     2                                                      5 
                
 
                         4       
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 25 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
24 of 24 
 
• 
Pressing by hand on the housing – without the use of the Mate assist system – must neither further 
close the connector system nor lead to damage. The contacting process must lead to successful 
insertion exclusively through the use of the insertion and withdrawal aid 
• 
The Mate assist system must be in a locked condition when delivered. The preferred position is 
"open". If required, the closed position can also be agreed on as the delivery condition. 
Predetermined fracture point: 
With excessive stress of the Mate assist system (housing incorrectly attached, a terminal without a 
complete closed secondary lock), a predetermined fracture point must occur at the Mate assist system 
and thereby protect the housing and especially the pins on the device unit side against destruction. 
According to scoop proof, the insertion and withdrawal aid must be destroyed before terminal overlay 
occurs. 
 
2.4.2.7 Primary interlock (PL) 
Each terminal must be positively and primarily locked in a housing unit. The operation of the primary 
interlock must be independent of the secondary interlock. 
The primary interlock is a component of the cavity drawing. 
• 
With tangless terminals, plastic lances of the surrounding housing engage in the corresponding 
recesses at the terminal (preferred version) 
• 
Two spring lances at the terminals (if possible on both sides); with small terminals, only one spring 
lance may also be used. 
2.4.2.8 Secondary interlock (SL) 
Each terminal must be positively and secondarily locked in a housing unit. The operation of the secondary 
interlock must be independent of the primary interlock. 
The secondary interlock has 2 functions:  
1. to check whether the terminal is correctly assembled (process reliability) 
2. to provide the retaining force if the primary interlock fails 
The secondary interlock may:  
• 
must be “side loaded” (detect but not correct a not correct positioned terminal) or “rear loaded” 
(detect and correct a terminal further in the direction of the contact zone without damage to the 
contact).  
• 
not “front loaded” (not acceptable, because the PL and SL are not independent)   
• 
be dispensed if with tang or tangless terminal a fully automatic assembly is assured and implemented 
with process reliability under all circumstances (assembly variants, with each supplier in the supplier 
chain up to DC/MMC). The process reliability must be verified by gently pulling on the locked 
terminal element (min. 10N) or by monitoring the insertion force.  
• 
be actuatable only if all inserted terminal are at the correct chamber position and primarily locked  
• 
be actuatable only if an inserted, not primarily locked terminal has been moved so far back in the 
housing that the secondary interlock passes by the terminal (free terminal). Here the design must 
ensure that a free terminal cannot make (e.g. an electrical) connection with the corresponding 
counterpart (e.g. pin connector) through the actuated secondary interlock; such a fault must be 
detected by the 100% inspection of a wiring harness (no insertion success). 
• 
NOT be actuated if a terminal is inserted in the chamber in an unlocked (primary interlock) 
intermediate position, leading to a (e.g. electrical) connection with the corresponding counterpart 
(e.g. pin connector). In this case, it must not be possible for the secondary interlock either to be 
brought to the final position or to damage the terminal.  
• 
not permit insertion between the male and female housing in an open state.  
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 26 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
25 of 25 
 
• 
NOT be accidentally actuatable during shipping or handling of the housing. 
• 
be lockable by hand (one handed), but not unlockable by one hand; the secondary interlock must be 
unlocked using two hands or a corresponding tool by force; the use of the tool (in the simplest case: 
screwdriver) must be defined on the drawing if necessary. 
• 
Easy to activate 
Pre/final locking position: 
• 
A secondary interlock not in the final locking position must be reliably detectable in the processing 
procedure (e.g. through a projecting secondary interlock, so that a coupling cannot be attached to a 
(test) plug). 
• 
Insertion force of the secondary interlock (from pre-) in final locking position: max. 80 N (with 
contacts)  
• 
Press-out force of the secondary interlock from the final locking position (to pre-locking position): 
min. 30 N , max. 50 N 
• 
With additional parts (e.g. separate lock sliders): force for separating the secondary interlock from 
the housing :  
 
 
 
min. 50 N , max. 100 N 
• 
With film hinges: see section on "Film hinges" (force for separation: min. 50 N , max. 100 N) 
Preferred version of the secondary interlock: 
• 
Hinge (Folding mechanism with permanent connection to the housing; see capture "Film hinge") 
• 
lock slider (uses housing as secondary lock)   
• 
lock slider (separate part in pre-locking position, not a loose piece)   
• 
lock slider (loose piece)   
2.4.3 
Coding / polarization 
Coding: 
Different coding variants must be coded mechanically and by color (see following table, important is the 
coluom “Coding” and RAL-no”) to protect against incorrect insertion.  
4 key-coding must be reserved for housing components (in the corresponding product version 
specifications). Implement light-colored codings for invisible connectors in the car. The first order shall is 
coding B (white). If nessesary use in addition coding C (light blue) and coding E (green). The mechanical 
coding : 
• 
may not lead either to insertion success nor to damage of participating components even with the 
application of force (>220N)  
• 
must be testable (e.g. through shift pins) at test adapters by the harness manufacturer 
• 
must not be implemented through symmetrical tool inserts (each key way-code must be unique)   
The Z-coding must not be installed in series wiring harnesses or series units.  
Visible Color 
(only for 
information) 
Coding 
Similar to RAL no. 
Remark 
Black 
A 
  9011 (graphite black) 
Preferred coding  
Off – White 
B 
  - 
Natural  (no color addition) 
Light Blue  
C 
  5012 (light blue) 
 
Dark Violet 
D 
  4004 (Bordeaux violet) 
 
Green 
E 
  6017 (May green) 
 
Dark Brown 
F 
  8011 (nut brown) 
 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 27 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
26 of 26 
 
Orange 
G 
  1028 (melon yellow) 
 
Light Pink 
H 
  3015 (light pink) 
 
Dark Gray 
I 
  7037 (dust gray) 
 
Blue/Green  
Z 
  5021 (water blue) 
Without codings, suitable for all codings 
 Dark Orange 
A 
(alternative) 
  1027 (curry yellow) 
Alternative A-coding  
(e.g when there are two different dress cover available) 
Light Gray 
B 
(alternative) 
  1014 (ivory) 
Alternative B-coding  
(e.g when there are two different dress cover available) 
Light Brown 
 
C 
(alternative) 
  1006 (corn yellow) 
Alternative C-coding  
(e.g when there are two different dress cover available) 
Light Yellow 
D 
(airbag) 
  1018 (zinc yellow) 
Preferred coding for safety systems (1
st coding);  
all coding possible 
Dark Yellow 
A 
(airbag) 
  1032 (ginster yellow) 
for safety systems (2
nd coding) 
Orange 
B 
(airbag) 
  1028 (melonen yellow) 
for safety systems (3
rd coding) 
Light Red 
C 
(airbag) 
  2004 (reinorange) 
for safety systems (4
th coding) 
Red 
EVT 
  3020 (traffic light red) 
Optionally series color or red 
Dark Green 
- 
  6001 (emerald green) 
Protective caps for plug-type sockets and the like 
The color batch must be coordinated with the basic material. The properties of the basic material must 
not change (ideally the color batch and the basic polymer have the same material basis). The goal is to 
have all color shades free of cadmium and chromium.  
Polarization: 
All component combinations must be designed to allowed to fit together in only one orientation 
indepentent of the key-coding. . If required, the twist-proofness must be achieved through suitable 
additional polarization’s. 
Definition of "twist-proof":  
• 
Only one correct insertion orientation is permissible  
• 
No insertion attempt in the twisted insertion orientation (also with an applied force >220N) may lead 
either to insertion success or to damage of the components; the component must be easily separable 
by hand  
• 
Codings may not be used as polarization’s (the Z-coding must also be twist-proof); if required the 
components must be additionally polarized  
Remark: "All component combinations" concerns the combinations plug/coupling-housing, 
housing/contact support, contact support/signal-conducting components, housing/protective caps, but 
also additional lock sliders, etc.  
2.4.4 
Contact protection of signal-conducting components 
Requirement in de-contacted state:   
IP 20 (not touchable with fingers; see DC-10612)  
(requirements in contacted state: see section on "Sealing systems") 
The contact protection serves: 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 28 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
27 of 27 
 
• 
the mechanical protection (against damage) of the signal-conducting components 
• 
if necessary electrical protection of personnel (as of the corresponding voltage level) 
• 
if necessary optical protection of personnel (e.g. eye safety with lasers); in addition, the regulations 
of the laser protection classes must be implemented.  
The contact protection should also be effected through components as directly connected as possible 
(e.g. the socket contacts in the wiring harness should be protected against contact by the contact 
support (1st processing step) and not by the surrounding housing (2nd processing step)). 
The contact protection must not be achieved through additional parts that can become lost.   
2.4.5 
Sealing systems 
The signal-conducting components must be protected in the contacted state against external stress for 
retaining their functions. With leaktight designs, besides the IP requirement other expanded requirements 
must be simultaneously fulfilled. 
In the area of the seals, no burrs, sharp edges or offset may occur at any of the participating components 
(plastic and metal parts). In these areas die separating planes should be avoided. 
Basic requirement:  
• 
With sealed designs:  
must meet the test-specification 
• 
With sealed designs with insertion and withdrawal aids (lever or slide lock):  
must meet all the 
test specification including protection against coarse sand grains (against jamming of the locking 
geometry in the inserted state) 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 29 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
28 of 28 
 
1 
Radial housing seal  
1a)   axial housing seal / compression seal (not permitted)  
2 
single wire seal  
3 
single wire seal plug 
4 
Cavity not required, sealed by manufacturer 
5 
Terminal with additional sealing crimp 
6 
Ventilation diaphragm in the outer wall of the device unit 
7 
Terminal in blade bar with insert molded (seal) 
8 
Wiring harness 
8a) Opposite wire end (e.g. splice) 
9 
Enclosed air volume  
10 Printed circuit board with contact pin 
Pi = Pressure inside the major assembly / control unit  
Pk = Pressure inside the contacting  
Pa = Ambient pressure 
******* Purpose 
Sealing systems must mainly be effective in the contacted state. The individual parts (e.g. pin strip) can 
also be tested separately for tightness)  
Sealing systems can be used for multiple purposes (functions): 
• 
Protection of the signal-conducting components against moisture (and therefore galvanic corrosion) 
• 
Protection of the housing parts against contamination (for achieving contamination class 1 with 
higher operating voltages and air and tracking distances minimized with respect to installation space) 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 30 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
29 of 29 
 
Contacting systems designed as sealable that are sealed with additional components (e.g. single wire 
seal and radial seal) must fulfill all requirements on a unsealed connector system, if the additional 
components are not installed. The drawing must indicate whether the given housing may be installed 
without sealing elements (possibly a restriction based on cable cross sections).  
2.4.5.2 Color assignment (tbd) 
Colors of the seals : 
Color 
Material 
Similar to 
RAL no. 
Remark 
Off – White 
 
  - 
Natural  (no color addition) 
On special requirement’s 
 
 
 
Withstands gasoline (e.g. on engine)  
 
 
 
 
 
 
 
 
 
******* Protection for sealing elements 
• 
Design connectors with shroud to completely protect seals and connector seal surface. Design the 
interface seal as part of the harness-side (usually the female connector) not on the device side.  
The seal protection must not be achieved through parts that can become lost or can separated.   
******* interface seal 
Axial seals (compression seal) must not be used with new designs (e.g. because of tolerance 
problems/seal functionality). 
A triple rib seal is to be preferred. When using a multiple rib seal, design so that shroud length and size 
incorporates full contact of all functional ribs in the in worst case statistic tolerances. 
Design to prevent “bunching” rollover or excessive movements of the interface seal during mating and 
un-mating of connectors. 
The position of the seal must be designed so that little air as possible is compressed during mating the 
connector (the internal pressure Pk unnecessarily increases the insertion force). 
 
******* Wire seal 
Purpose: Sealing of cable ports at contacting systems on the wiring harness side (male and female 
housings). 
2 sealing variants are permissible: 
• 
single wire seal / single wire seal plug 
• 
Mat seal: simultaneously closes several cavities 
General requirements: 
• 
A triple rib seal is to be preferred for contact with the cavity. When using a multiple rib seal, design 
so that cavity length and size incorporates full contact of all functional ribs in the in worst case 
statistic tolerances. 
• 
For contact with the wire: for single wire seals a triple rib seal is to be preferred / for mat seals a 
double rib seal is to be preferred. 
• 
The holding ability, including the sealing function of the sealing element at or in the cavity must 
withstand the pressure surges during the contacting process during opening or closing. 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 31 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
30 of 30 
 
• 
The fixations of the sealing elements relative to the cavity and to the wire or terminal must safely 
withstand the manufacturing process and the in- and out-pin processes (see "Assembly cycles of the 
contact"). If necessary, the connector system manufacturer must restrict the assembly cycles of the 
terminal regarding the sealing (in the processing specification).  
Cavity equipped with terminal and wire: 
• 
The single wire seal must be mounted at the terminal.  
• 
The Mat seal must seal off the wire (without touching the terminal). Per cavity, the permissible wire  
cross sections or wire diameter must be defined for the particular mat seal. 
Cavity not equipped with wire / terminal  
• 
Empty cavity must be sealed with suitable sealing elements. (e.g. single wire seal plug). Design the 
plug in a way that it can withstand the air pressure while mating of connector. 
• 
With the use of mat seals, the openings of the mat seals must be sealed by appropriate "sealing 
journals". The sealing journals can act individually or in combination for several cavities. 
• 
If a cavity is closed by manufacturer of the housing part the cavity must be closed on the wiring 
harness side (rear of connector) because of facilitation of the assembly process. 
The contacting system manufacturer must develop single wire seals as well as single wire seal plugs 
when developing tight housings with single wire seals. The single wire seals and single wire seal plugs 
must have the same external geometry’s with respect to the cavity.  
******* device sealing  
Purpose: To seal the contact area from the interior of the device.   
The sealing area generally represents the fastening area of the particular male terminal. 
2 fastening or sealing variants are permissible: 
• 
sealed: The fastening areas of the terminals are molded in.  
• 
not sealed: terminals are pressed in.  
2.4.6 
Labeling  
All components must be labeled with the following information: 
• 
Only for DCS-tools: part number (if possible on the same tool-insert for key-coding) with trademark 
(Mercedes star) 
• 
Supplier-number and supplier name (or trademark instead of the name)  
• 
Production date (date clock) 
• 
Material 
• 
Cavity identification of the mold, preferably numbering; with very small components and space 
problems (e.g. sealing elements, bolt, etc.), small holes can be alternatively drilled in the die (not 
larger than a center punch mark, max. depth 0.1mm) for differentiating the cavities.  
• 
Modification level (revision) of the drawings (unique traceability to the basic design drawing) 
• 
Special details on pins (e.g. light direction with optical fibers) 
• 
Pin numbering systems (each pin number may occur in a contacting system once only (e.g. not "Pin 
1" twice) 
The lettering must be slightly recessed, to avoid abrasion (see MBN 10 230 and MBN 33 015). 
The DC/MMC basic number should lie in the vicinity of die inserts (for codings), to ensure that the coding 
variant matches the DC/MMC basic number.  
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 32 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
31 of 31 
 
2.4.7 
Living hinges 
Subject: Living hinge with retaining function for single parts that are actuated or assembled in a later 
process step (e.g. with secondary interlock of contacts).  
Open state: 
• 
Angle to end position as small as possible (<<90°: means “very less than 90°”) 
• 
By design, no rear hook possibility with cable pieces (see section on "Rear hook protection") 
Closed state:  
• 
When locked, the main function of the component must be ensured, even if the hinges are broken; 
the broken ends must not protrude or have sharp edges (no risk of injury) � the additional part must 
therefore be locked or guided with the counterpart at a minimum of 3 non-collinear points 
Actuation: 
• 
Close hinge: pressing once on the attached part must securely close all locks (with a clicking sound); 
multiple actuation must not be necessary. 
• 
Open hinge: suitable actuation opening (e.g. with a small screwdriver) must be able to open the lock 
without impairing the functioning (e.g. secondary interlock) (at least 5 actuation cycles).  
• 
The hinge itself must withstand at least 5 actuation cycles without breakage. 
• 
Hinges that are open or not completely closed must, through connection to the test adapter (100% 
final inspection by the harness manufacturer) either be closed during insertion (process reliability) or 
must not lead to insertion success.  If not possible the check fixture must check the full installation. 
2.4.8 
Fastening concept (tbd) 
Subject: Fastening of housing part at / in the vehicle 
Definition of the slider : tbd  
All male-housings (inline-connectors) must have a slider for fixing in the car. 
With sheet metal penetrations:  
• 
Specification of punch burr (on vehicle side),  
• 
direction of punch must be direction of clip insertion 
• 
sheet thickness with paint coats must be taken into account when selecting clip. 
 
2.4.9 
dress cover (optional part) 
If required, a dress cover must fulfill the following functions: 
• 
wire guiding / wire relief: 
• 
Depending on the installation space, wire guides or fastenings at the housing part must be available; 
the secure fixation of the wires must be possible for any filling level (cross section of the wire 
bundle). 
• 
Depending on the installation space, the wire covers must protect the wires and terminals against 
moisture (e.g. road splash water) and deflect moisture away from the wire side of a connector and all 
sealing parts. 
• 
Per tensile and vibration requirement, the wire guide must also keep away from the wire forces 
applied by the signal-conducting components (terminals) ("strain relief"). 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 33 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
32 of 32 
 
• 
Bending radius limit: Depending on the requirement of the wire specifications, the minimum bending 
radii must be maintained ( e.g. POF, RF cables, etc.). These requirements must be fulfilled through 
design either in the housing part or in additional parts which can be connected to the housing part (at 
least positively). Limit value: measurement of the minimum radius with the maximum permissible 
pulling on the wire / wire bundle 
2.4.10 Twist protectors (screw connections) 
Screw locks (e.g. Fuses with screw connection) must not be stressed with torque when the nuts are 
tightened. For this purpose, spacing (e.g. shim) between the nut and lock connection as well as the 
twisting of the cable lug to be screwed on must be excluded or minimized as much as possible.   
2.4.11 EMC 
There are 3 types of EMC tight contacting systems (depending on the maximum frequency (rising edges) 
and the transmitted voltage: 
• 
> 10 MHz / in the µV range (e.g. antennas) 
• 
> 10 MHz / in the V range (e.g. bus systems) 
• 
contacting systems for transmitting power, representing (with respect to EMC) an undesired opening 
in the control unit wall. 
Depending on the type, the following requirements are posed: 
• 
Connection between the screening of the pin connector and the PCB: at least 4 ground pins 
distributed over the entire circumference. Preferred is a continuous connection over the entire 
circumference. 
• 
Connection between the pin connector and wiring harness coupling: permanent, solvable, no 
"accidental" connection" (e.g.) via locking geometry permitted / as many contact points possible 
along the entire plug collar / R<5mOhm (with DC voltage, over lifetime)  
• 
Connection between the wiring harness coupling and shielded cable: additional shield must be 
applied mechanically (“crimped”) to cover junction between shield of connector and shielded cable, 
screen must be fastened – during manufacture – with as many contact points as possible along the 
circumference (corresponding specification in the processing specification)/ R<5mOhm (at DC 
voltage, over lifetime)  
• 
Return loss: ≥ 20 dB in the range of 0 – 1 GHz above that –3 dB per octave (up to the target 
frequency) 
• 
Shielding effectiveness: ≥ 65 dB @ target Frequency 
• 
The electrical way on surface of the shielding of the housing should be as short as possible (e.g. from 
the wire shield over connector to PCB). The goal is that there is no time delay between the signal 
time over the terminals and the signal time over the shielded housing. 
• 
The connection between wire bundle and the connector should be rigid against torsion effects. 
 
2.4.12 Geometrical properties 
Mold mismatch < 0.1 mm 
Keep mold parting lines, steel match lines, and part decorations (e.g. part number) off all the sealing 
surfaces of the plastic connector housing. 
Mold flash < 0.1 mm 
Minimum external wall thickness: 1mm 
Edges / corners 
chamfer : 0.5 mm * 45° or round: r > 0.1 mm  
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 34 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
33 of 33 
 
2.4.13 Electrical properties 
When the function (and the functional parameters) of the terminals are restricted by the connecting 
system (e.g. derating curve with neighboring power pins) this must be documented in the product 
specification of the connecting system housing. 
When there are different terminal sizes in one connector (hybrid) make sure, that the potential power-
pins have as less as possible thermal (and therefor electrical) influence to each other.  
2.4.14 Material properties 
The connector manufacture must follow all the processing requirements as specified by the raw plastic 
material manufacturer (e.g. moisture of raw material while molding) . 
Certify that any used combination of regrind and raw material meets the plastic material manufacturers 
specification. Also certify that the “as molded” parts are not degraded beyond acceptable material limits 
(over the life of the part) and meets the connector test specification.  
2.4.15 Mechanical properties 
During the initial design of each new connector and terminal family, complete a layout study of the mated 
assembly showing minimum/maximum terminal insertion, seal compression tolerance stack-ups, and 
worst case connector-to-connector alignment at the point of initial terminal contact.  This guideline 
ensures that connector lock over-travel and minimum terminal engagement length requirements are met.  
The supplier retains this information and makes it available to the OEM or tier supplier upon request. 
Design male and female connectors with terminals so that there is no buzz, squeak, or rattle in either the 
mated or unmated condition. 
2.4.16 Ergonomic properties 
The assembled contacting system (housing including contact support) must have planar areas at wire end 
of connector allowing an assembly-friendly contacting process. The areas must not be bordered by sharp 
edges. The same applies to unlocking and lever/sliders. 
2.4.17 Climatic properties 
All requirements in section 2.4 refer to the required temperature range. The manufacturer must verify the 
properties and stability of the connecting systems in the temperature range and guarantee them in the 
product specification. 
The plastic material must be stable with respect to all properties and types of resistance through the 
entire lifetime in the temperature range specification (see temperature class).  
The housing design must not restrict the specified parameters of the terminals (e.g. current carrying 
capability) further than stated in the product specification of the terminals.  
 
2.4.18 Chemical properties 
Verify that all lubricants, cleaners, and mold release agents used in manufacture of the terminals and 
connectors are not harmful to the electrical performance for the life of the connection system. 
2.4.19 Visual properties 
The surfaces must appear level and smooth and comply in all details with the corresponding drawing. 
No scorch marks or shrink holes must occur at the components (these faults generally indicate deficient 
processing). 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 35 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
34 of 34 
 
2.4.20 Optical transmission properties 
The contacting system manufacturer must describe the following in the product specification: 
Attenuation terms of each interface (coupling to converter, fiber - fiber couplings)  
The possible and tested component combinations with components from other manufacturers (this can 
also occur on the given drawing). The combinations must comply with the required transmission 
parameters (test program: "Test guideline for optical contacting systems, part B"). 
The requirements regarding the laser protection classes must be followed and if necessary implemented.  
  
2.5 
Additional parts 
2.5.1 
Shorting bar 
Provide for shorting bars capability in the design of airbag connectors.  
TBD: In all connectors for the airbag squib signals must be a shorting bar (on airbag side). Before 
proceeding with the design, the connector supplier and the harness supplier should contact the 
responsible engineer at DC/MMC to confirm the need for a the shorting bar.    
The electrical parameters of the shorting bar must be maintained over lifetime of the vehicle. 
2.5.2 
Transport protective caps 
Try to design all connectors in a way that transport protected caps are not needed.  
• 
Protection during transport or storage of the signal-conducting components against damage or 
contamination. The cap must be designed to minimize accidental removal but allow easy removal by 
the manufacturer or worker.  
Protective caps are always Z-coded, i.e. they fit all housing variants. 
Protective caps must be designed as reusable, circulating parts. To this end, the manufacturer must 
indicate the increased costs (material, logistic, cleaning, etc.) for the alternative, once-only use and 
subsequent recycling. 
Do not use expensive material. 
 
2.5.3 
Auxiliary materials 
Auxiliary materials may be required to achieve required properties with the available materials. 
These auxiliary materials can contribute to the overall functioning of the component through its lifetime 
(e.g. optical jelly in the D2B contact) or guarantee temporary protection (surface protection on metal 
contacts). 
Requirements on necessary auxiliary materials  
• 
Nontoxic (according to DBL 8585) 
• 
Process reliability when integrated in the processing procedure 
• 
Reliable functional detection of absence (at the latest before delivery to DC, it must be verified that 
the necessary auxiliary material is available; reliable detection of defective parts must be ensured!)   
The properties of the material must be documented in a data sheet (e.g. from the manufacturer of the 
additional material) and fall within the scope of the contact manufacturer's product specification. Besides 
the special properties of the given auxiliary material, the following details must be documented 
(examples):  
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 36 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
35 of 35 
 
• 
Properties of the auxiliary material (e.g. color, lifetime, shelf life, viscosity, shrink characteristics)  
• 
Operating parameters (temperature, humidity)  
• 
Gas emission and migration (positional stability)  
• 
Chemical fingerprint  
• 
Compatibility with the neighboring components (e.g. stress corrosion cracking) 
• 
Resistance to decomposition (due to contamination, moisture or harmful gas) or oxidation (through 
oxygen or ozone) 
• 
Process capability (safety requirements)  
The functioning of the auxiliary material is tested along with the qualification inspection of the 
corresponding contacting parts. 
In addition, suitable life tests with corresponding property tests (if necessary in parallel with 
development) must be performed particularly with regard to the given auxiliary materials. 
The manufacturer must define these tests with respect to applications and materials and (before 
performing the tests) coordinate these definitions with DC. 
 
3 
Documentation 
TBD (will be added in Revision 2.1) 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 37 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
36 of 36 
 
4 
Quality assurance 
All suppliers are required to report quality issues to DC/MMC. The same applies to modifications and 
necessary compromises in the fulfillment of specifications. 
4.1 
Product and process responsibility 
Definition of "supervisor": 
• 
The supervisor is responsible for the technical (e.g. component implementation), logistic (e.g. 
prompt, sufficient component supplying of the customer) and legal (e.g. intellectual property 
searches) safeguards during the introduction of new components and technologies. 
• 
During the run-up phase and the series implementation the supervisor is responsible for reporting 
and verification in the event of faults and for submitting this information to the corresponding 
customers (through DC/MMC). 
Product responsibility (interface drawing relevant): 
Responsibility in reference to completeness and clarity of an interface drawing lies with the interface 
drawing-generating connector system manufacturer. This therefore also applies to the product 
responsibility for derived products. 
Product and process responsibility 
Product and process responsibility always lie with one source. 
A) 
During the normal process flow, the product and process responsibility for connector parts on the 
wiring harness side as well as connector parts on the device unit side lies with the particular 
manufacturer of the contacting parts; this includes the necessary definitions (product and 
processing specifications). 
B) 
In exceptional situations with the deliberate deviation or disregard of the manufacturer's 
specifications (such as deviating dies, processing procedures, other components, etc.), the product 
and process responsibility shifts to the processor.  
B1) With permanent deviations, a requalification of the product and the processing procedures on the 
part of the particular processor must be stipulated and carried out. The requalification results must 
be coordinated with DC/MMC. 
B2) With temporally limited, deliberate disregard of the manufacturer's specifications due to logistic 
problems (e.g. no sufficient storage of correct sealing elements by the processor), the affected 
customers must be notified about the situation directly, immediately and unrequested. The 
processor must point out the affected components (including the specification of the model series, 
basic numbers of affected wiring harnesses, delivery date, plant etc.), the foreseeable period and 
the possible risks and, if appropriate, the suitable temporary solutions. For this temporary solution, 
at least a functional verification of the solution for the affected property (properties) must be 
provided. 
4.2 
FMEA 
Design and process FMEA (failure mode and effect analysis) coordinated with the DC/MMC-FMEA. The 
manufacturer's FMEA must be concluded after the planning phase, at the latest however after a third of 
the specified development period. 
 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 38 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
37 of 37 
 
4.3 
(Re-)qualification 
The requalification tests generally comprise the qualification tests to their full extent. 
All components are always tested according to the test guidelines of DC/MMC. To this end the 
components must be tested according to all the corresponding test groups. In the individual case the 
scope of testing can be reduced following written approval by DC/MMC (e.g. with the release of a further 
coding variant). 
The testing and all related cost is the responsibility of the suppliers as part of the development of their 
product. Proof of lab studies and tests must be provided by the supplier. The results  must be 
documented and archived by the supplier and made available for DC/MMC on request. 
For components with special requirements, further tests can and shall be agreed on.  
4.4 
Control of the series production 
The drawing must identify critical dimensions that will effect the function of the design. The drawing must 
also identify dimensions that can be used for quality auditing / monitoring during manufacturing of the 
part. 
The supplier must control at least these parameters according to quality control process by each OEM 
and the supplier will be audited by the direct customers (harness manufacturer).  
 
 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 39 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
38 of 38 
 
5 
Appendix 
5.1 
Definition of terms 
 
English 
German 
Description 
 
 
 
Terminal housing  
 
Terminal housings can be male or female housings. 
Male housing 
 
Assembled with male contacts (pins, blades).  
Plugs exist in 2 variants:  
1. Variant: device unit connection with pins at control units (alternativ
names: pin connector, pin strip);  
2. Variant: wiring harness separation point (alternative name: flying 
separation point). The plug always contains contact pins or contact 
blades (male, pins, posts).  
The contacts of a plug are (generally) protected against contact only to 
limited extent (see section on "Contact protection"), but they must be 
protected against damage (see section on "Kojiri"). 
Female housing 
 
A coupling always contains female contacts (sockets or contact bushes
The contacts are always protected against contact, since non-inserted 
couplings on the wiring harness side in the vehicle can conduct voltage
Mixed assembly 
 
For the mixed assembly of pins and sockets in a contact housing only th
term "Terminal housing" is used; the terms "male housing" or "female 
housing" are not practical for mixed assemblies and are therefore not 
permitted. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
"Tang" Terminal 
 
Active locking element (locking lance) located at the contact 
"Tangless" Terminal  
Active locking element (locking lance) is located not at the contact, but 
the surrounding housing/contact support (e.g. CleanBody) 
 
 
 
 
 
 
Locking: 
 
 
slider 
Verriegelung-
schieber 
lock slider / housing slider 
CPA 
 
Connector Position Assurance (locking of the coupling in inserted 
condition):  
"true secondary 
lock" 
“echte” Sekundär-
verriegelung 
Side or rear loaded secondary lock; no front loaded lock. The locking 
mechanism is independent from the primary lock. 
Screw locking ring 
(lock nut) 
Schrauben 
sicherungsring 
Optional secondary lock for a nut 
Cross section 
evaluation 
Schliffbild 
For terminal evaluation of the crimp section 
Splices 
Stützstellen 
 
dress cover 
Leitungsabgang 
Wire routing out of the connector  
Seals: 
 
 
single wire seal 
Einzelader-
abdichtung 
 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 40 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
39 of 39 
 
single wire seal 
plug 
Blindstopfen 
 
Mat seal 
Matten Dichtung 
 
Interface seal 
 
seal between the two connector housings 
Cables: 
 
 
Strands 
Litze 
 
Conductor 
Innenleiter 
all strands of a wire 
wire 
Leiter / Ader 
A single wire includes the conductor with the wire insulation 
AV 
 
normal cable/standard cable 
AVS 
 
thin wall cable (insulation-reduced stranded wire) 
AVSS / CAVS 
 
ultra thin wall cable (no randam strands, only sturktured strands) 
 
 
 
Optical systems: 
 
 
system reserve 
System reserve 
In the calculation of optical systems the “system reserve” is a special 
term in the power budget. It’s not allowed for suppliers to use this term
( other wording: optical transmission loss, level reserve) 
 
 
 
 
 
 
 
 
 
 
 
 
Tooling:  
 
 
mold / tool 
Spritz-Werkzeug 
Tool for plastic-parts 
die 
Stanz-Werkzeug 
Tool for metal-parts (stamp-process) 
mold flash 
Spritzgrat 
is created when a there is a gap in the steel (in the mold tool) 
mold mismatch 
Formteilungsgrat 
is created when there is a mis alignent in the steel (in the mold tool) 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
5.2 
Other applicable documents 
The required Daimler-Benz standards must be requested from the Purchasing department. They are 
available there only.  
 
5.3 
Overview of families (proposal) 
See "Familie-Übersicht.xls" 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)


### 第 41 页
 
Confidential - All rights reserved. Distribution and duplication with the  
written approval of DaimlerChrysler AG only 
Design Guidelines for Connection Systems  
 
A 221 000 26 99 
Author: 
Merget 
Checked: 
  
Department: EP/EKK 
ZGS: - 
Date: 2002-07-25 
Page: 
40 of 40 
 
5.4 
Part history (proposal) 
 
Part history 
 
 
 
 
 
 
DC/MMC basic 
number: 
A 000 545 00 00 kZ 
Supplier: basic number: 0-123456-1 
 
 
DC/MMC drawing 
no.: 
A 000 545 00 01  
If applicable, project 
number: 
 
 
 
 
DC/MMC 
designation: 
Contact support, 2-pole 
 
 
 
 
Delivery 
date 
Drawing date / 
revision level 
Reason for change 
Implemented change 
Code 
Units Reason for issue 
02.11.65 
19.11.65 / A 
Stereo lithography sample 
 
A 
3 
DC/MMC internal 
04.02.66 
13.01.66 / A2 
 
 
E 
10 
First presentable 
part (EVT) 
06.04.66 
20.02.66 / B 
Interlock jams 
Snap arms shortened 
F 
20 
DC/MMC 
presentation 
20.04.66 
30.03.66 / B1 
 
 
F 
40 
DC/MMC 
warehouse 
28.07.66 
17.06.66 / C 
 
 
S 
10 
First presentable 
part (EVT) 
10.08.66 
17.06.66 / C 
 
 
S 
50 
Manufacturer, 
internal 
15.08.66 
17.06.66 / C 
 
 
S 
100 
DC/MMC 
warehouse 
19.08.66 
04.08.66 / D 
Clicking sound too faint 
Overexpansion protection 
area enlarged 
t 
10 
DC/MMC 
presentation 
30.08.66 
04.08.66 / D 
 
 
 
416 
Installation release 
(VBF) 
14.09.66 
04.08.66 / D 
 
 
 
16 
Initial Sample 
Inspection Report 
(EMPB) 
14.03.68 
04.08.66 / D 
New location: A- Dorf 
 
 
416 
Installation release 
(VBF) 
01.04.68 
04.08.66 / D  
 
 
 
16 
Initial Sample 
Inspection Report 
(EMPB) 
04.05.71 
04.08.66 / D 
Die wear 
Correction of the die 
 
416 
Installation release 
(VBF) 
07.06.71 
04.08.66 / D 
 
 
 
16 
Initial Sample 
Inspection Report 
(EMPB) 
A 000 545 00 00 Teilelebenslauf.xls 
Page 1 of 1 
 
Uncontrolled copy when printed (: qiushi sun, 2014-08-04)

