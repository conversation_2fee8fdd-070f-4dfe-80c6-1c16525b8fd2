# A 222 000 16 99_EN_2010-03_CAN网络特性规范.pdf

## 文档信息
- 标题：Änderungsverzeichnis
- 作者：olirich
- 页数：15

## 文档内容
### 第 1 页
 
 
Aenderungsbeschreibung/ revision text 
 
 
YAP2726809          Document completely revised  
 
YAP2696710       Document completely revised 
 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 1 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping <PERSON>, 2013-08-22)


### 第 2 页
 
Contents 
Change History ........................................................................................................................................... 3 
1 
Scope.................................................................................................................................................... 4 
1.1 
Deviations ......................................................................................................................................4 
2 
Concerned Vehicle Lines ................................................................................................................... 4 
3 
Physical Layer..................................................................................................................................... 5 
3.1 
Battery fed (Clamp 30)...................................................................................................................5 
3.1.1 
Transceiver Circuit Specifications...........................................................................................................................5 
3.1.2 
Bus voltage levels for battery fed ECUs ..................................................................................................................6 
3.2 
Ignition fed (Clamp 15/87)............................................................................................................7 
3.2.1 
Transceiver Circuit Specifications...........................................................................................................................7 
3.2.2 
Bus voltage levels for ignition fed ECUs..................................................................................................................8 
3.3 
Dimensioning .................................................................................................................................8 
3.4 
Galvanic separation between transceiver and microcontroller (CAN)...........................................9 
3.5 
System Basis Chip ...................................................................................................................... 10 
3.6 
Oscillator Tolerance .................................................................................................................... 10 
3.7 
Bit timing..................................................................................................................................... 11 
4 
Transceiver........................................................................................................................................12 
4.1 
Recommended Transceivers/ System Basis Chips.................................................................... 12 
5 
Common Mode Choke......................................................................................................................13 
6 
Termination.......................................................................................................................................14 
6.1 
Periphery-CAN............................................................................................................................. 14 
6.2 
Diagnostics-CAN ......................................................................................................................... 14 
6.3 
Body-CAN.................................................................................................................................... 14 
6.4 
PT-CAN........................................................................................................................................ 14 
6.5 
EPT CAN...................................................................................................................................... 14 
6.6 
FCS-CAN ..................................................................................................................................... 14 
6.7 
PT_Sensor-CAN........................................................................................................................... 14 
6.8 
Headunit-CAN ............................................................................................................................. 14 
6.9 
HMI-CAN ..................................................................................................................................... 14 
6.10 
Engine-CAN ................................................................................................................................. 15 
6.11 
ECM-Chassis CAN  (205) ............................................................................................................ 15 
6.12 
HY-CAN ....................................................................................................................................... 15 
6.13 
ESP_Sensor-CAN ........................................................................................................................ 15 
6.14 
H2 CAN ....................................................................................................................................... 15 
 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 2 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 3 页
 
Change History 
ZGS Changes 
Ch. 
Author 
 
001 Creation of document 
 
Weber 
 
 
 
 
002 1. Document completely revised 
 
 
Weber 
003 1. Add vehicle line 205 
2  
2. Delete vehicle body styles 
2
3. Changed Reference from MBN 10416 to MSS 10416 
1
4. Add Infineon SBCs TLE8261E, TLE8261-2E, TLE8262E, 
TLE8262-2E to recommended Transceiver/SBC list 
4.1
 
5. Add Freescale SBCs MC33904A5, MC33905S5, MC33905D5 
to recommended Transceiver/SBC list 
4.1
 
6. Remove Transceiver TJA1042 from recommended 
Transceiver/SBC list 
4.1
 
7. Changes regarding level adaptations 
3.2.1
8. Change Bus-name from Chassis-CAN to Periphery-CAN 
6.1
9. Change Engine-CAN termination  
6.10
10. Add termination for ECM-Chassis CAN   
6.11
2
3.4
 
6.6
6.14
6.5
Weber 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Mäusling 
11. Add vehicle line BR242ev/Rex/H2 
12. Add Chapter 3.4 “Galvanic separation between transceiver and 
microcontroller (CAN)” 
13. Add Termination for FCS-CAN, H2 CAN, EPT CAN  
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 3 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 4 页
 
1 
2 
Scope 
This CAN network specification document contains implementation requirements for ECUs used in 
Daimler vehicles. 
 
The Core Networking Group of Mercedes Car Group determines for which vehicle lines this specification 
applies to. 
 
Refer also to the specification “MSS 10416 – CAN Networking Performance Specification” in the latest 
version for the appropriate vehicle line. 
 
Refer all questions to the Core Networking Group. 
 
 
1.1  Deviations 
Deviations from the requirements contained in this standard are only allowed if agreed to explicitly 
and documented between the supplier, Core Networking and the appropriate Daimler design and release 
areas. 
 
 
 
Concerned Vehicle Lines 
This CAN network specification document applies to the following Mercedes-Benz vehicle lines 
 
 
Vehicle Line 
222 
205 BR242ev/Rex/H2 
 
 
 
� 
� 
� 
 
 
 
   
 
 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 4 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 5 页
 
 
3 
Physical Layer 
3.1 Battery fed (Clamp 30) 
 
3.1.1 Transceiver Circuit Specifications  
 
 
Split
CANHigh
CANLow
STB
RX
TX
RX
TX
µC
Transceiver
Port-Pin
Vcc
Vcc_µC
VBat
5V
Vcc
+
+
Clamp 30
VBat
3.3V
Vcc_µC
+
+
VBat
INH
ERR
EN
Port-Pin
Port-Pin
Vcc_µC
VBat
Vio
Vcc
VBat
INH
Wake
INH
CANHigh
CANLow
R1
R2
C1
CMC
R5
R3
R4
ESD2
ESD3
ESD1
U
U
1
2
3
 
Figure 1: Transceiver circuit of a battery fed ECU 
 
For EMI reasons, all transceiver circuit components shall be arranged within a distance of 50mm to the 
module connector on the circuit board. 
 
Resistors within the Rx and Tx lines are not allowed. 
 
Connect the Wake-Pin according to the requirements given within the transceiver datasheet respectively 
application notes. 
 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 5 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 6 页
 
 
Some CAN-transceivers in combination with modules use additional wake up sources, e.g. a second 
CAN-transceiver, a LIN-transceiver or if INH of the voltage supply is not only controlled by the CAN-
transceiver. To wake up the CAN, an additional circuit might be necessary in combination with a slow 
starting voltage supply. The implementation of this circuitry may vary. 
 
The necessity of the additional circuitry is based on the specific behavior of current CAN transceivers: 
undervoltage detection of the transceiver has higher priority than the mode control via the STB / EN – 
pins. Please refer to the application notes for details. 
 
If a controller with a voltage supply below 5V is used (e.g. 3.3V, see Figure 1), a separate voltage 
regulator with INH shall be used. INH inputs of both voltage regulators are controlled by the transceiver. 
The controller supply voltage is connected to the pin VI/O for “level adaptation.” 
Depending on the controller/software, a connection to an interrupt input ( ERR -INT) may be necessary. 
 
The maximum value of capacitance shall not exceed 47pF in total per CAN line (CANHigh resp. 
CANLow to GND). Calculation according to Figure 1 has to be done including all optional 
components, but excluding the parasitic capacitance of the CMC and the transceiver. 
 
Any deviation in the transceiver circuit design shall be coordinated with the Core Networking 
Group. 
 
For carryover ECUs from previous vehicle lines, the transceiver circuit has not to be modified if 
the required settings in section 3.7 “Bit timing” can be met. 
 
 
3.1.2 Bus voltage levels for battery fed ECUs 
 
 
 
Figure  2: CAN-Bus Voltage Level according to DIN ISO 11898 (Battery fed) 
 
All threshold voltages are specified in the respective transceiver device specification. 
 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 6 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 7 页
 
 
3.2 Ignition fed (Clamp 15/87) 
 
The ECU power supply and the CAN interface in these systems is switched on with a Clamp 15/87 
switch. CAN communication is only possible if clamp 15/87 is switched on. 
 
3.2.1 Transceiver Circuit Specifications 
 
 
 
Split
CANHigh
CANLow
STB
RX
TX
RX
TX
µC
Transceiver
Port
Vcc
Vcc_µC
VBat
5V
Vcc
Vcc_µC
+
+
Clamp
15/87
CANHigh
CANLow
R1
R2
C1
CMC
R5
R3
R4
ESD2
ESD3
ESD1
U
U
1
2
3
 
Figure 3: Transceiver Circuit of an ignition fed ECU 
 
For EMI reasons, all transceiver circuit components shall be arranged close together on the circuit board. 
 
Resistors within the Rx and Tx lines are not allowed.  
 
The maximum value of capacitance shall not exceed 47pF in total per CAN line (CANHigh resp. 
CANLow to GND). Calculation according to Figure 3 has to be done including all optional 
components, but excluding the parasitic capacitance of the CMC and the transceiver. 
 
Any deviation in the transceiver circuit design shall be coordinated with the Core Networking 
Group. 
 
For carryover ECUs from previous vehicle lines, the transceiver circuit has not to be modified if 
the required settings in section 3.7 “Bit timing” can be met. 
 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 7 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 8 页
 
 
3.2.2 Bus voltage levels for ignition fed ECUs 
 
 
 
Figure 4: CAN-Bus Voltage Level according to DIN ISO 11898 (Ignition fed) 
 
All threshold voltages are specified in the respective transceiver device specification. 
 
 
3.3 Dimensioning 
 
Device 
Value 
Rating 
Tolerance 
Package 
Mandatory 
footprint on 
PCB 
Populated 
R1, R2 
62R 
250mW 
≤ +/- 1% 
1206 
yes 
1)
R3, R4 
62R 
250mW 
≤ +/- 1% 
1206 
2)
2)
R5 
0R 
- 
- 
0603 
yes 
1)
C1 
1)
≥50V 
- 
0603 
yes 
1)
ESD1 
- 
- 
- 
SOT23 
yes 
optional 
ESD2, ESD3 
- 
- 
- 
0805 
yes 
optional 
CMC 
3)
- 
- 
- 
yes 
yes 
Transceiver 
4)
- 
- 
- 
yes 
yes 
 
1)      Affected ECUs which have to populate this device, are listed in Chapter 6 “Termination” 
2)      Affected ECUs which have to reserve the footprint and populate the devices, are listed in   
        Chapter 6 “Termination”. All other ECUs do not have to reserve the footprint. 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 8 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 9 页
 
3)      Refer to Chapter 5 “Common Mode Choke” for recommended common mode chokes 
4)      Refer to Chapter 4 “Transceiver” 
 
 
3.4 Galvanic separation between transceiver and microcontroller (CAN) 
ECUs which need a galvanic separation between transceiver and µController have to fulfill additional 
requirements. 
  
 
 
Figure 5:  CAN model with galvanic separation of RX and TX 
 
It is required that the following three parameters are fulfilled: 
 
Attribute 
Unit 
Value 
tr / tf
ns 
max.     3 
tPG [tMP1-MP2 + tMP3-MP4 ] 
ns 
max.   80 
PWD [tPLH - tPHL] 
ns 
max.     4 
 
The parameter tPG is the additional propagation delay of a signal caused by the device for galvanic 
separation (see Figure 5). 
This additional time, results from the summation of propagation delay between connection TX of 
controller and transceiver (duration from measuring point MP1 to MP2) and connection RX of transceiver 
and controller (duration from measuring point MP3 to MP4). The value of tPG is independent whether the 
ECU is fed by clamp 15 or 30. For dimensioning of CAN Networks it is essential to match this parameter.  
The parameter PWD is the difference between the propagation delays caused by rising and falling edges. 
This difference generates an asymmetric delay for signals on the bus (see Figure 6).  
 
It is up to the supplier which specific device is used for this isolation under the requirement that the 
three parameters tr/tf, tPG and PWD are matched. Furthermore, there is no definition how many channels 
such a device/devices shall have. 
 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 9 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 10 页
 
 
Figure 6: Description of used parameters 
 
Vin is the input voltage of the corresponding channel and Vout the output voltage of the corresponding 
channel. Vcc (if applicable) is the supply voltage of the galvanic separation device. For further details see 
the datasheet of the galvanic separation device. 
 
 
3.5 System Basis Chip 
For ECUs using a system basis chip (SBC) with combined CAN transceiver, voltage regulator and watch 
dog, the CAN circuit design shall also refer to section 3. 
 
 
3.6 Oscillator Tolerance 
The oscillator provides the reference timing for the CAN controller. The oscillator frequency shall be 
chosen as an integer multiple of the baudrate and the selected time quanta per bit to ensure proper CAN 
communication. The more precise the oscillator, the more stable the bit timing. The controller and the 
oscillator must be aligned to each other with an appropriate circuitry. 
 
A PLL is often included to generate the timing for the CAN controller in the manufacturer controller 
application notes. If a PLL is used, proof must be provided that the overall CAN timing meets the  
specification in the Chapter 3.7 ”Bit timing” including the effects of time, temperature and aging. 
 
Generally crystals should be used for clock generation. For clock generators, the tolerance must be < ± 
0.15 % across temperature and life cycle aging. The fulfillment of this requirement across temperature 
and aging must be confirmed with suitable documentation. It is up to the supplier to provide 
documentation, e.g. manufacturing process, sorting process etc., which proves that all resonators and 
crystals delivered to Mercedes-Benz Cars or their suppliers will �fulfill this requirement and guarantees 
availability for the life cycle of the vehicle. 
 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 10 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 11 页
 
 
3.7 Bit timing 
 
The Bit timing parameters of the ECU CAN channels shall be set according to the following requirements 
for all the CAN network speeds listed. 
 
For ECUs intended to work at 125kBaud, the oscillator shall carefully be chosen, to meet the required 
settings even if the Baudrate will be increased to 250kBd.  
The increased Baudrate shall be possible if software settings will be adapted without the necessity to 
change the oscillator.   
 
 
 
Parameter 
Value 
Baudrate 
125, 250, 500 kBaud (±0.15%) 
Synchronization 
recessive → dominant 
one Sample Point 
Configuration 1: 
Configuration 2: 
Configuration 3: 
Sample Point 
(% of bit time) 
81,25% 
80,00% 
80,00% 
Time quanta per Bit NBT 
16 
20 
15 
Synchronization Jump Width 
3 
4 
3 
 
 
Carryover ECUs from previous vehicle lines must also meet these requirements. 
 
 
 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 11 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 12 页
 
 
4 
Transceiver 
4.1 Recommended Transceivers/ System Basis Chips 
Supplier Type 
Category 
System 
Application 
SOP1)
AMIS 
42665AGA 
Transceiver 
CAN 
Cl.15 
Series 
Bosch 
CY320 
SBC 
CAN 
Cl.30/30G 
Series 
Freescale MC337422)
SBC 
CAN 
Cl.30/30G 
Series 
Freescale MC33904A53)
SBC 
CAN 
Cl.30/30G 
Series 
Freescale MC33905S53)
SBC 
CAN+LIN 
Cl.30/30G 
Series 
Freescale MC33905D53)
SBC 
CAN+LIN 
Cl.30/30G 
Series 
Infineon 
TLE6251 DS 
Transceiver 
CAN 
Cl.15 
Series 
Infineon 
TLE6251-2G 
Transceiver 
CAN 
Cl.30/30G 
Series 
Infineon 
TLE7263E 
SBC 
CAN+LIN 
Cl.30/30G 
Series 
Infineon 
TLE8261E 
SBC 
CAN 
Cl.30/30G 
Series 
Infineon 
TLE8261-2E 
SBC 
CAN 
Cl.30/30G 
Series 
Infineon 
TLE8262E 
SBC 
CAN+LIN 
Cl.30/30G 
Series 
Infineon 
TLE8262-2E 
SBC 
CAN+LIN 
Cl.30/30G 
Series 
Infineon 
TLE8263E 
SBC 
CAN+LIN 
Cl.30/30G 
Series 
Infineon 
TLE8263-2E 
SBC 
CAN+LIN 
Cl.30/30G 
Series 
Infineon 
TLE8264E 
SBC 
CAN+LIN 
Cl.30/30G 
Series 
Infineon 
TLE8264-2E 
SBC 
CAN+LIN 
Cl.30/30G 
Series 
NXP 
TJA1040 
Transceiver 
CAN 
Cl.15 
Series 
NXP 
TJA1041A 
Transceiver 
CAN 
Cl.30/30G 
Series 
NXP 
TJA1043  
Transceiver 
CAN 
Cl.30/30G 
Series 
NXP 
UJA 1065 
SBC 
CAN+LIN 
Cl.30/30G 
Series 
  Table 1: Recommended Transceivers and System Basis Chips 
 
1) Supplier Information 
2) The fastest Slew Rate setting in the CAN Register shall be used (Slew Rate 3). 
3) The fastest Slew Rate setting in the CAN Register shall be used (FAST) 
 
The transceivers listed, shall only be used for applications given in  Table 1, column “Application”. 
 
The Transceivers/SBCs used, shall comply with the Directive 2000/53/EC of the European Parliament 
and of the Council on end-of-life vehicles (ELV). These compliant types may vary in the denotation (suffix, 
prefix) compared with the recommended types. 
 
Passed conformance test is required for the used Transceivers/SBCs and CAN-Controllers. 
Refer also to ISO11898-5 for Cl.30/30G application Transceivers/SBCs and to ISO11898-2 for Cl.15 
application Transceivers/SBCs. 
 
If a supplier finds it necessary to deviate from this list, the Core Networking Group shall be contacted for 
transceiver qualification requirements. 
 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 12 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 13 页
 
 
5 
Common Mode Choke 
Recommended1) common mode chokes 
 
 
Supplier 
Type 
Epcos 
B82789-S0223-N/H 
Epcos 
B82789-C0513N/H 
Epcos 
B82789-C0104-H001/2 2) 
Epcos 
B82789-C0104-N001/2 2) 
Epcos 
B82799-S0333-N 
Epcos 
B82799-S0513N 
TDK 
ACT45B-510-2P 
TDK 
ACT45S-220-2P 
 
         1) Recommended only regarding high-frequency attenuation; qualification regarding mechanical and temperature 
           requirements shall be done by module supplier. 
         2) Shall not be used in combination with Infineon transceivers/SBCs  
 
For the required lead-free soldering, note the allowed lead-free soldering profiles supported by the CMC 
manufacturer.  
 
 
 
 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 13 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 14 页
 
 
6 
Termination 
 
6.1 Periphery-CAN  
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
 
no termination in any ECU 
 
6.2 Diagnostics-CAN  
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
EIS/CGW 
62R 
62R 
62R 
62R 
0R 
47nF 
 
6.3 Body-CAN  
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
 
no termination in any ECU 
 
6.4 PT-CAN  
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
 
no termination in any ECU 
 
6.5 EPT CAN 
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
 
no termination in any ECU 
 
6.6 FCS-CAN 
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
 
no termination in any ECU 
 
6.7 PT_Sensor-CAN 
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
 
no termination in any ECU 
 
6.8 Headunit-CAN 
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
 
no termination in any ECU 
 
6.9 HMI-CAN 
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 14 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 15 页
 
 
no termination in any ECU 
 
6.10  Engine-CAN   
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
CPC 
62R 
62R 
62R 
62R 
0R 
47nF 
 
6.11  ECM-Chassis CAN  (205) 
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
EIS/CGW 
62R 
62R 
62R 
62R 
0R 
47nF 
 
6.12  HY-CAN   
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
 
no termination in any ECU 
 
6.13  ESP_Sensor-CAN   
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
ESP 
62R 
62R 
- 
- 
0R 
22nF 
ESP_SC 
62R 
62R 
- 
- 
0R 
22nF 
 
6.14  H2 CAN  
ECU 
R1 
R2 
R3 
R4 
R5 
C1 
TCU 
62R 
62R 
62R 
62R 
0R 
47nF 
 
 
 
Mercedes-Benz 
© Daimler AG  Schutzvermerk DIN ISO 16016 beachten! / Refer to protection notice DIN ISO 16016! 
Keine Aenderung ohne Zustimmung der federfuehrenden Konstruktion / 
Any alterations are subject to the approval of the design department 
 
FUNKTIONSVORSCHRIFT 
 CAN NETWORKING DETAILS 
A 222 000 16 99 
 
Bearb./auth.: Weber 
 
Abt./dep.: EP/ EKA 
 
Datum/date: 2010—03-18 
 
ZGS / CAD: 003 / G 
Auftr.-Nr./order  no.: YAP2696710 
 
Seite/page: 15 von 15 
 
Not for new design
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)

