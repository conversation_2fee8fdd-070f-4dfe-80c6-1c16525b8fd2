# MBN_10384-2_EN_2007-12_接插件慢动作检测.pdf

## 文档信息
- 标题：Anwendungswarnvermerk
- 作者：ejustni
- 页数：18

## 文档内容
### 第 1 页
 
 
Mercedes-Benz 
 MBN 10 384-2 
Technical Standard 
Date published:  2007-12 
Category: 22 
Total no. of pages: 1 and 15 pages LV 214-2  
 
Person in charge: <PERSON>t 
Plant 059; Dept.: EP/EKL 
Date of translation: 2014-12 
Phone: +49(0)7031 90 – 4 10 96 
 
Road Vehicles 
Automotive Wire-to-Contact Connections 
Slow Motion Test  
Road Vehicles – Automotive Wire-to-Contact Connections – Slow Motion Test 
Foreword 
 
LV 214-2 edition 2007-10, Slow Motion Test for Automotive Wire-to-Contact Connections, has been 
adopted unchanged into the Mercedes-Benz standards collection. 
 
Changes 
Not applicable. 
 
 
 
 
 
 
 
 
 
NOTE: This translation is for information purposes only. 
The German version shall prevail above all others. 
 
 
Copyright Daimler AG 2007 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 2 页
 
October 2007 
 
Automotive Wire-to-Contact Connections 
Slow Motion Test 
 
LV 214-2 
 
 
 
Descriptors: 
Automotive connector, connector, contact, crimp, slow motion 
 
 
Continued on pages 2 to 17 
 
 
 
File stored at AUDI AG (Standardization), Mr. <PERSON>: +49-841-89-30965 
 
 
 
 
 
 
 
Foreword 
The present edition of this Standard has been produced by representatives of the automotive man-
ufacturers Audi AG, BMW AG, Daimler AG, Porsche AG and Volkswagen AG. 
This Standard is stored as a Word file in the Standardization department of Audi AG. 
This Standard does not claim to be complete. Automotive manufacturers are free to request 
additional state-of-the-art tests at any time. 
Since individual automotive manufacturers may make changes as they deem necessary, tests shall 
not be carried out according to this Standard. Instead, the supplier shall ensure that it has the valid 
relevant in-house standard of the corresponding automotive manufacturer available. Deviations 
from this Standard are to be indicated in italics in the in-house standards. Should modifications  
of any test sections become necessary in individual cases, these shall be agreed upon separately 
between the appropriate department and the manufacturer concerned. 
Test reports will be recognized provided that the tests have been performed by an independent 
institute accredited in accordance with DIN EN ISO/IEC 17025. Approval does not automatically 
follow from acceptance of the test reports. 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 3 页
Page 2 
LV 214-2: 2007-10 
 
Contents 
Page 
1 
General ............................................................................................................................. 2 
1.1 
Scope ................................................................................................................................ 2 
2 
Definitions.......................................................................................................................... 3 
2.1 
Climatic conditions at RT ................................................................................................... 3 
2.2 
Accuracy ........................................................................................................................... 3 
2.3 
Documentation of the measurement results ....................................................................... 3 
2.3.1 
Documents to be provided ................................................................................................. 3 
3 
Slow motion test ................................................................................................................ 4 
3.1 
Subject of test and batch sizes .......................................................................................... 4 
3.2 
Measuring resistance and saving data ............................................................................... 4 
3.2.1 
Measurement setup ........................................................................................................... 4 
3.2.2 
Data recording and data filtering ........................................................................................ 8 
3.2.3 
Saving the measurement data ........................................................................................... 8 
3.2.4 
Final measurement ............................................................................................................ 8 
3.3 
Preconditioning by thermal shock ...................................................................................... 8 
3.4 
Slow motion test ................................................................................................................ 9 
3.4.1 
Definition of the motion sequence ...................................................................................... 9 
3.4.2 
Definition of temperature change (TC) ............................................................................. 10 
3.5 
Limit values ..................................................................................................................... 11 
A.1 
Annex (informative) ......................................................................................................... 13 
A.1.1 
The "4-point measurement" ............................................................................................. 13 
A.1.2 
Description of a 4-point measurement ............................................................................. 13 
A.1.3 
Possible mistakes when performing 4-point measurements ............................................ 15 
A.1.3.1 Common U and I soldering point ..................................................................................... 15 
A.1.3.2 Resistance in voltage path too high ................................................................................. 16 
A.1.3.3 Resistance in the current path causes the constant current to "break down" ................... 17 
A.1.3.4 One-piece and two-piece contact tabs ............................................................................. 17 
1 
General 
It is hereby expressly stated that a vehicle manufacturer's adoption of this test guideline, regardless 
of legal grounds, shall not constitute explicit or implicit assumption of liability for the complete  
and adequate inspection and testing of products based on the following test guideline. No liability 
whatsoever is assumed for any product defects that may have gone unnoticed. 
1.1 
Scope 
The purpose of the following test is to determine the properties of a wire-to-contact (crimp) 
connection. Applying the measuring instrument parameters as currently defined, measurements 
can be performed on wires up to 4 mm². The wires shall comply with the relevant standards  
(for metric wires, see LV 112). 
This Standard applies to all electrical wire-to-contact connections found in automotive vehicles.  
It does not, however, replace existing test guidelines, which remain in force (e.g. LV 214: "Working 
Group, Test Guidelines for Automotive Connectors"). 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 4 页
Page 3 
LV 214-2: 2007-10 
 
2 
Definitions 
2.1 
Climatic conditions at RT 
The following applies to all measurements taken at room temperature (RT): 
Temperature: 
T = (23 ± 2) °C , 
Humidity: 
RH = 50 % or uncontrolled 
All measurements shall be performed and verified in certified warming cabinets or climatic 
chambers. Otherwise, all changes in temperature and humidity in the cabinets shall be recorded. 
2.2 
Accuracy 
The accuracy of the resistance measurement (or voltage measurement with current measurement) 
shall be at least 10 times greater than the resistances being measured on the units under test. 
2.3 
Documentation of the measurement results 
2.3.1 
Documents to be provided 
a) Documentation of the test setup (including measurement technology, direction of motion, 
sampling rate and pictures of the units under test) 
b) Product designation with object numbers 
c) Test result: "Pass" or "Fail" 
d) Identification of the contacting parts and wires of all manufacturers for whom the test  
reports are applicable (manufacturer, wire designation) 
e) Noticeable irregularities that have occurred 
f) Processing specifications (including crimp parameters) 
g) Precisely measured crimp data of contacts under test (height and width) 
h) Measurement results of crimp resistances in tabular and diagram form. The following data 
shall be provided in tables: maximum, mean, minimum, and standard deviation for initial 
and final resistance and for the change in resistance in each test, for all units under test. 
i) All results (diagrams and tables) shall: 
− 
Include the applied measurement, ambient, and device parameters 
− 
Use appropriately scaled diagram ordinates 
(Default value range is (0 to 50) mΩ) 
j) One micrograph per test group 
All resistance values shall always be indicated with the wire resistance included.  
The line or the effect of temperature shall not be computed from the results.  
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 5 页
Page 4 
LV 214-2: 2007-10 
 
3 
Slow motion test 
3.1 
Subject of test and batch sizes 
The subject of each measurement is the connection between the contact and the wire  
(as per LV 112). 
Here, measurements also include metallic transitions within the contact (spot welds or stampings 
between the contact body and the contact tabs). The transition from the contact to mating 
connectors or, for example, plating shall not influence the measurement result (see Section  3.2). 
Scope of testing: 
− 
Batch size: 10 units 
− 
All of the crimping forms described in the processing specifications 
− 
For wires: all cross sections, type B stranded wire design, all surfaces and all insulation 
materials (currently: PVC 105 °C; other wires will be specified in more detail) 
Test conditions: 
− 
Crimp heights at respective upper tolerance limit (minimum pressing) 
− 
Direction of wire motion for test: horizontal (see  Fig. 1) 
 
 
 
Fig. 1 
Definition of wire movements 
At least one reference component shall be measured during each measurement run to verify  
the measurement setup. The reference component shall consist of the same parts found  
in the units under test (same object numbers, connections, wire lengths, etc.). In addition,  
the connection between the wire and the contact shall be soldered. 
 
3.2 
Measuring resistance and saving data  
3.2.1 
Measurement setup 
It shall be ensured that the 4-point measurement principle is implemented (see Annex  A.1). Voltage 
tapping and current injection shall never be done at the same point on the units under test  
(e.g. a soldering point). 
For documentation purposes, the resistance value shall be indicated in mΩ. It shall be computed 
from the voltage drop and the measured current (via a measuring shunt).  
Vertical 
 
Horizontal 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 6 页
Page 5 
LV 214-2: 2007-10 
 
Setup 
The units under test shall be set up as shown in Figure 2 and Figure 3. The soldering points shall 
not be placed too close to the crimp. The first soldering point in the wire shall not be placed  
closer than the distance of l1 + l2. The soldering shall include all wire strands. The wire shall  
not be separated at no. 4 (remove conductor insulation without breaking the copper strands). 
 
 
Fig. 2 
4-point measurement (overview) 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 7 页
Page 6 
LV 214-2: 2007-10 
 
 
 
Fig. 3 
4-point measurement (detail) 
 
Legend for  Fig. 2 and  Fig. 3 
No. 1 
PCB contact strip with copper trace on top and bottom (see  Fig. 4) 
No. 2 
Soldering point on top side (for voltage tapping) 
No. 3 
Soldering point on bottom side (for current injection) 
No. 4 
Soldering point on wire (for voltage tapping) 
No. 5 
Soldering point on wire (for current injection) 
No. 6 
Slider with through hole serving as wire guide 
No. 7 
Lead for voltage measurement  
 
(use smallest possible cross section due to temperature effects; max. 0,35 mm²) 
No. 8 
Wire for constant current (max. 0,5 mm²) 
No. 9 
Contact to be tested in a housing (contacts may also be clamped instead) 
l1, l2, l3 
Wire lengths (see "Parameters") 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 8 页
Page 7 
LV 214-2: 2007-10 
 
Remarks: 
If a PCB contact strip cannot be used (e.g. due to round contact), the measurement lead  
to the voltage tap shall be adhesively and conductively bonded to the contact body. The lead shall 
not be soldered (soldering is not permitted in the vicinity of the crimp). In the case of bonded  
connections, the measurement described in Section  3.2.4 shall be performed at the beginning  
and end of the measurement procedure. 
Where pin contacts are employed (instead of the PCB contact strip) "Kelvin clamps" shall be used. 
 
 
Fig. 4 
PCB contact strip with surface coating on both sides 
 
Parameters for  Fig. 4 
Material:  
 
Commercially available circuit board (PCB) with Cu traces and non-porous 
Au surface 
PCB thickness:  
Shall 
match 
the 
pin 
dimension 
of 
the 
contact 
being 
tested  
(typical thicknesses: 0,6 mm or 0,8 mm). The tolerances given in the pin 
specification (for series production parts) do not need to be fulfilled.  
The nominal thickness, to include the thickness of the conductor trace  
in the Cu layer, shall be achieved. 
Grid size:   
 
The grid size shall correspond to the contact housing used for the test  
(e.g. 2,54 mm). Here it shall be taken into account that it might be required 
to populate only every second socket of the housing.  
 
 
 
(See dimensions of "Diameter D of the wire guide hole".) 
Note: 
See  A.1.3.4 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 9 页
Page 8 
LV 214-2: 2007-10 
 
3.2.2 
Data recording and data filtering 
When capturing data it shall be ensured that data is recorded (at RT without motion) for 30 minutes 
before and after the application of loads (both pre-aging and slow motion test). 
It shall be possible to determine the limit values using the initial and final constant resistance  
values. 
When recording data, differentiation shall be made between raw data and filtered data. 
─ 
Raw data: The resistance of the test unit shall be sampled continuously at 1 Hz.  
The measurement current shall flow constantly (without interruption, no clocking).  
This raw data shall be recorded. 
─ 
Filtered data (optional): It is further permitted to filter and save the raw data  
("MMM" data: maximum, mean, minimum). In this case the raw data (e.g. from recordings  
of 3 minutes each) is used to determine the following 3 values: 
 
Maximum value in these 3 minutes 
 
Mean value (geometric) in these 3 minutes 
 
Minimum value in these 3 minutes 
 
(Example: 9 minutes of raw data thus yield 9 values) 
3.2.3 
Saving the measurement data 
The data shall be saved during preconditioning and during the slow motion test. 
Ultimately only the MMM data is required. It is recommended, however, to keep the raw data until 
data preparation and the discussion of the results are complete. 
3.2.4 
Final measurement 
When the entire measurement is completed, all current and voltage circuits shall be re-measured 
using 2-point measurement and these measurements documented. To this end, all measuring  
instruments shall be removed and replaced one by one with ohmmeters. 
Remark: The corresponding contact resistances (at conductive bonding points, soldering points, 
PCB connections, etc.) shall not be of such a magnitude as to influence the 4-point measurement 
(see Annex  A.1). 
3.3 
Preconditioning by thermal shock 
The resistance values shall be obtained during preconditioning. 
Preconditioning shall be performed on fully assembled test units (i.e. contacts in housing,  
all measurement leads attached, slider in place, etc.). 
Parameters: 
Upper temperature 
Tupper = 125 °C 
Lower temperature 
Tlower = -40 °C 
Transfer time 
< 10 s 
Holding time 
tupper = tlower = ½ h 
Number of cycles 
100 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 10 页
Page 9 
LV 214-2: 2007-10 
 
3.4 
Slow motion test 
The motion cycles (slow motion) are superimposed by a temperature change. 
The measurement setup shall be designed such that it can be arranged in a climatic chamber  
to allow the motion cycles to take place under various climatic conditions. The measurement leads 
shall be routed from inside the climatic chamber to the outside. 
3.4.1 
Definition of the motion sequence 
The preconditioning of the assembled components and measurement leads can be done with  
or without motion apparatus. In all cases, installation shall be done as gently as possible  
(without applying any force on the contacts). The contacts shall not be taken out of the housing 
parts used. 
While the test is in progress, the components shall not be subjected to any additional mechanical 
load. It is not permitted to shake, joggle, wiggle or even touch the wires or contacts. 
 
Setup 
The entire test setup shall, to the greatest possible extent, consist of lightweight (low mass) materials 
so that equilibrium can be achieved quickly during the superimposed temperature change (TC). 
The contacts can be installed in a housing produced under series production conditions. 
Contacts with sealing elements (single-wire seals, etc.) shall be tested with matching single-wire 
seals in the series production housings. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Fig. 5 
Setup principle 
L1 
L2  
Motion: ± 25 mm 
Not firmly constrained 
U 
15 min 
 25 mm 
-25 mm 
Motion 
Time 
Constrained in series 
production housing 
Dual sided PCB 
I_const 
L3  
1 s 
1 s 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 11 页
Page 10 
LV 214-2: 2007-10 
 
Parameters: 
Length from crimp to slider 
L1 = 100 mm  
(length of wire strands in motion zero position) 
Length from slider to voltage tap 
L2 = 50 mm 
Length from voltage to current tap 
L3 = 50 mm 
Deviation of wire guide in slider from crimp axis 
( Fig. 2 and  Fig. 3, no. 6) 
3° 
Diameter D of wire guide hole  
( Fig. 2 and  Fig. 3, no. 6) 
D = (wire diameter + 20 %)  
or (max. 5 mm) 
Current (I_const) 
100 mA 
Sampling rate 
1 Hz 
Motion amplitude (effective) 
± 25 mm (within one second, sine) 
No. of motion cycles  
720 
3.4.2 
Definition of temperature change (TC) 
The following TC (see  Fig. 6) is superimposed on the motion sequence. 
Temperature change (TC) 
Tlower = RT, tlower = 1 h 
 
Tupper = 80 °C, tupper = 3,5 h 
Gradient 
1,33 K/min 
(= 45 min changeover time ⇒ 1 TC takes 6 h)  
Number of TC cycles  
30 
 
Temperature change (TC) over time 
10 
20 
30 
40 
50 
60 
70 
80 
90 
100 
0 
1 
2 
3 
4 
5 
6 
Time h 
Temperature °C 
 
Fig. 6 
Temperature change 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 12 页
Page 11 
LV 214-2: 2007-10 
 
3.5 
Limit values 
Three criteria apply for determining the limit values (see Figure 7 to 9): 
 
Determining limit values 
Time axis 
R mΩ 
 
Fig. 7 
Criterion 1: Maximum scatter of measurements (min/max difference)  
of all units under test at beginning of measurement  
(representation is only schematic) 
 
 
Determining limit values 
Time axis 
R mΩ 
 
Fig. 8 
Criterion 2: Maximum change of each test unit  
(representation is only schematic) 
 
ΔR2 
ΔR1 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 13 页
Page 12 
LV 214-2: 2007-10 
 
 
Determining limit values 
Time axis 
R mΩ 
 
Fig. 9 
Criterion 3: Maximum change (also during test) of each test unit  
(representation is only schematic) 
Maximum permitted change in resistance ΔR (applies to small wire cross sections of < 1 mm²) 
≤ 1 mΩ for ΔR1, scatter for all units under test (new condition) 
≤ 3 mΩ for ΔR2, ΔR3 for each test unit (from test start to test end) 
ΔR3 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 14 页
Page 13 
LV 214-2: 2007-10 
 
A.1 
Annex (informative) 
A.1.1 
The "4-point measurement" 
A.1.2 
Description of a 4-point measurement 
 
 
 
 
 
 
 
 
 
 
 
 
Fig. A.1.1   Schematic diagram of a 4-point measurement 
A 4-point measurement (also known as 4-wire or 4-terminal sensing) is used to obtain precise  
resistance measurements unaffected by lead or terminal resistance or changes in their temperature. 
The measurement setup is always as shown in Fig. A.1.2. 
A constant direct current is injected into the resistance to be measured (current path). One voltage 
tap is placed on either side of and as close as possible to the resistance to be measured  
(see 3.4.1) (voltage path). 
 
 
 
 
 
 
 
 
 
 
 
 
Fig. A.1.2   Equivalent circuit diagram of a 4-point measurement 
 
 
R 
High-impedance  
voltage measurement 
Constant current source 
I M ≈ 0 
 
 
I M ≈ 0 
R 
U Meas 
I K 
Equivalent resistance 
Connection (soldering point) 
Equivalent  
resistance 
Lead 
R V 
R L 
U loop 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 15 页
Page 14 
LV 214-2: 2007-10 
 
Here it shall be ensured that current injection and tapping of the voltage path do not take place  
at the same point (e.g. at the same soldering point). 
A high-impedance (megohm) voltmeter shall now be connected to the voltage path (the meter  
reduces current flow to just I << mA, or practically 0 when compared to the current injected into  
the current path). 
With a "voltage loop" (in the context of Kirchhoff's voltage law) in the voltage path we get: 
0 = -UMeas + 2 * (IM * RL) + 2 * (IM * RV) + (IK * R) 
Note here that no lead or contact resistance in the current path is included in this equation  
nd hence cannot influence the measurement result. 
With IM approaching 0, the measurement leads, including the contact resistance of the connectors 
in the current path, play no role, yielding: 
UMeas = IK * R 
or 
R = UMeas / IK 
Fig. A.1.3 and Fig. A.1.4 describe the 4-point measurement circuit with a PCB contact strip coated 
on both sides (yellow). 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Fig. A.1.3   Equivalent circuit diagram of 4-point measurement with PCB contact strip 
 
 
R Wire 
U Meas 
I K 
R Bottom 
R Top 
R Crimp 
R Contact 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 16 页
Page 15 
LV 214-2: 2007-10 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Fig. A.1.4   Equivalent circuit diagram of 4-point measurement with PCB contact strip 
A.1.3 
Possible mistakes when performing 4-point measurements 
The following mistakes can be made when setting up and performing 4-point measurements,  
and they can affect the measurement results. 
A.1.3.1 Common U and I soldering point 
The voltage loop now gives us: 
 
 
0 = -UMeas + 2 * (IM * RL) + 2 * (IK * RV) + (IK * R) 
Since IM = 0 it follows that:  
UMeas = IK * (R + 2 * RV) 
or 
R = UMeas / IK – 2 * RV 
 
This means that the connection of the measurement leads, whose resistance is not known, flows 
directly into the measurement result. 
⇒ The measurement result UMeas is too high and may vary during the time of measurement  
(the standard deviation increases).  
 
I K 
I M ≈ 0 
R Wire       R Crimp         R Contact 
U Meas 
R Bottom 
R L 
U loop 
R Top 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 17 页
Page 16 
LV 214-2: 2007-10 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Fig. A.1.5   Equivalent circuit diagram with common soldering point 
 
For Detail X and Detail Y, see Fig. A.1.6. The same applies for a "cut lead" to the resistance  
to be measured. 
 
 
 
 
 
 
 
 
 
 
 
Fig. A.1.6   Detail views from Fig. A.1.5 
A.1.3.2 Resistance in voltage path too high 
This normally pertains to the RV values (e.g. soldering points) for the voltage taps, which  
can change during the time of measurement. A serious measurement error occurs if RV  
approaches the same magnitude as the input resistance of the voltmeter (high-impedance  
voltage divider). 
When using voltmeters with an input resistance ≥ 1 MΩ, these effects are negligible for RV < 1 kΩ 
(see Section  3.2.4). 
 
I M ≈ 0 
R 
U Meas 
I K 
R V 
R L 
U loop 
Detail X 
Detail Y 
R V 
R V 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)


### 第 18 页
Page 17 
LV 214-2: 2007-10 
 
A.1.3.3 Resistance in the current path causes the constant current to "break down" 
This most often pertains to the RV values (e.g. soldering points) of the current taps or an abnormal 
test unit during the time of measurement. It is recommended to frequently monitor  
the measurement data of the injected constant current (I). If there is a discontinuity or a test unit 
cannot be controlled, it is still possible to "rescue" the series of measurements by connecting  
a suitable resistor (e.g. 1 Ω wire resistor) across the discontinuity or test unit at the proper point.  
A qualification test can be terminated immediately. 
A.1.3.4 One-piece and two-piece contact tabs 
 
Fig. A.1.7   One-piece and two-piece contact tabs 
 
When testing contacts whose upper and lower tabs are formed by a single strip of metal, current 
and voltage tapping can basically be done in any order. 
If, however, testing is being done on contacts whose contact normal force is produced  
by 2 different materials (possibly with different conductance), voltage tapping shall be done  
on the side formed by the contact body (normally the more conductive material). The current that  
is injected on the other side shall in this case not only be measured and monitored, but the voltage 
of the current source shall also be recorded in order to detect whether the current source  
is at the limit of its capacity. 
Using this setup it is possible to do a subsequent check of the current (it being a quasi unreliable 
factor) over the entire period of measurement, something that is not possible with the voltage. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2016-01-07)

