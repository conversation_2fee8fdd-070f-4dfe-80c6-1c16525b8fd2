# MBN_10385_DE_2015-08_VeDoc文件中的重要标识说明.pdf

## 文档信息
- 标题：Microsoft Word - MBN_10385.DOCX
- 作者：petroth
- 页数：17

## 文档内容
### 第 1 页
Mercedes-Benz  
MBN 10385 
Werknorm 
Ausgabe: 2015-08
 
Seiten insgesamt (inkl. <PERSON><PERSON>): 17 
 
Fachbetreuer: <PERSON>: <EMAIL> 
 
Werk 059; Abt.: RD/OES
 
Tel.: +49 (0) 151 586 130 26
 
 
Copyright Daimler AG 
 
 
 
CAD-Zeichnungen und 3D-CAD-Modelle 
VeDoc – Informationen auf einer Zeichnung 
relevanter Bauteile 
 
 
 
 
 
Vorwort 
 
Die Norm beschreibt die Dokumentation von VeDoc-Informationen auf einer Zeichnung.  
Die Information, dass ein Bauteil VeDoc-relevant ist, wird über die Zuordnung des VeDoc-Kenners in 
den Daimler PDM-Systemen erreicht. 
 
Diese Ausgabe ersetzt die vorherige Ausgabe 2012-12 dieser Norm. 
 
 
 
 
 
 
 
 
 
 
 
Änderungen 
 
Gegenüber der Ausgabe 2012-12 wurden folgende Änderungen vorgenommen: 
 
Durch Änderung der Dokumentationsmethode musste die MBN 10385 vollständig überarbeitet 
werden.  
 
1.) 
Zeichnung ist nicht mehr 
Quelle des VPD-Ident  
Die Daten des VPD-Ident werden nur dann auf der  Zeichnung 
dokumentiert wenn Etiketten beschrieben werden. 
2.) 
VeDoc-Etikett 
Neu: Abhängig von der VPD-Identnummer müssen die 
Formatvorgaben der Daten des Etiketts auf der Zeichnung 
dokumentiert werden. 
3.) 
VeDoc-
Lieferantennummer 
Neu: Die durch den BTV vergebenen Lieferantennummern müssen 
auf der Zeichnung dokumentiert werden. 
4.) 
Dokumentationsverfahren 
Vollständige Beschreibung der Dokumentationsverfahren eingefügt 
(Kapitel 6) 
5.)  
Titel geändert 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 2 页
MBN 10385:2015-08, Seite 2 
 
Copyright Daimler AG 
Inhaltsverzeichnis 
 
1 
Anwendungsbereich ............................................................................................................................ 3 
2 
Normative Verweisungen .................................................................................................................... 3 
3 
Begriffe und Definitionen ..................................................................................................................... 3 
4 
Allgemeine Anforderungen .................................................................................................................. 3 
5 
Kennzeichnung VeDoc-relevanter Bauteile ........................................................................................ 4 
5.1 
Allgemeines ......................................................................................................................................... 4 
5.2 
Datenfeld VeDoc – Relevanz im Zeichnungsschriftfeld ...................................................................... 5 
5.3 
Textfeld zur Kennzeichnung der Position eines VeDoc-Etikett ........................................................... 5 
5.4 
Hinweispfeil VeDoc-Etikett .................................................................................................................. 6 
5.5 
Tabelle „Datenformat VeDoc-Etikett“ .................................................................................................. 6 
5.6 
Tabelle „bauteilspezifische Lieferantennummer“ ................................................................................ 7 
5.7 
Tabellenerweiterung Sachnummerntabelle ........................................................................................ 7 
6 
Dokumentationsverfahren Zeichnung ................................................................................................. 8 
6.1 
Allgemeines ......................................................................................................................................... 8 
6.2 
Fall 1: Einblatt „Bauteil mit VeDoc-Etikett (Einzelteil oder ZB)“ .......................................................... 9 
6.3 
Fall 2: Mehrblatt „Bauteil mit VeDoc-Etikett (Einzelteil oder ZB)“ ..................................................... 10 
6.4 
Fall 3: Einblatt „ZB und kZ-siehe Bauteil mit VeDoc-Etikett“ ............................................................ 11 
6.5 
Fall 4 Mehrblatt „ZB und kZ-siehe Bauteil mit VeDoc-Etikett“ .......................................................... 12 
6.6 
Fall 5 Einblatt „kZ-siehe Bauteil mit VeDoc-Etikett in TB-/ ZB-Zeichnung“ ....................................... 13 
6.7 
Fall 6 Mehrblatt „kZ-siehe Bauteil mit VeDoc-Etikett in TB-/ ZB-Zeichnung“ .................................... 14 
6.8 
Fall 7: Einblatt „VeDoc Steuergerät oder Steuergeräte-Peripherie-Teil (Einzelteil oder ZB)“ ........... 15 
6.9 
Fall 8: Mehrblatt „VeDoc-Steuergerät oder Steuergeräte-Peripherie-Teil (Einzelteil oder ZB)“ ....... 15 
6.10 Fall 9: Einblatt „ZB und kZ-siehe Bauteil VeDoc Steuergerät oder Steuergeräte-Peripherie-
Teil“ ................................................................................................................................................... 15 
6.11 Fall 10: Mehrblatt „ZB und kZ-siehe Bauteil VeDoc Steuergerät oder Steuergeräte-Peripherie-
Teil“ ................................................................................................................................................... 16 
6.12 Fall 11: Einblatt „kZ-siehe Bauteil VeDoc Steuergerät oder Steuergeräte-Peripherie-Teil in TB-
/ ZB-Zeichnung“ ................................................................................................................................. 16 
6.13 Fall 12: Mehrblatt „kZ-siehe Bauteil VeDoc Steuergerät oder Steuergeräte-Peripherie-Teil in 
TB-/ ZB-Zeichnung“ ........................................................................................................................... 17 
6.14 Ausschnittszeichnung ....................................................................................................................... 17 
 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 3 页
MBN 10385:2015-08, Seite 3 
 
Copyright Daimler AG 
1 
Anwendungsbereich 
Die Norm beschreibt die Dokumentation von VeDoc-Informationen auf CAD-Zeichnungen, erzeugt 
aus CAD-Systemen und 3D-CAD-Modelle nach der 3D-Master Methode in der technischen 
Produktdokumentation für das Geschäftsfeld Mercedes-Benz Cars und Mercedes-Benz VAN, sowie 
2D-Zeichnungen aus anderen Systemen. 
2 
Normative Verweisungen 
Die folgenden zitierten Dokumente sind für die Anwendung dieses Dokuments erforderlich. Bei 
datierten Verweisungen gilt nur die in Bezug genommene Ausgabe. Bei undatierten Verweisungen 
gilt die letzte Ausgabe des in Bezug genommenen Dokuments (einschließlich aller Änderungen). 
 
ISO/IEC 15417 
Information technology — Automatic identification and data capture 
techniques — Code 128 bar code 
symbology specification — 
ISO/IEC 16022 
Information technology — Automatic identification and data capture 
techniques — Data Matrix bar code symbology specification 
DIN EN ISO 10209 
Technische Produktdokumentation – Vokabular – Begriffe für technische 
Zeichnungen, Produktdefinition und verwandte Dokumentation 
3 
Begriffe und Definitionen 
Es gelten die Begriffsdefinitionen nach DIN EN ISO 10209 und folgende: 
 
3D-Master 
 
3D-Master ist die Methode, bei der der 3D-Datensatz alle 
prozessrelevanten Informationen enthält. 
 
kZ-siehe – Bauteile 
 
"kZs - Teile" = keine Zeichnungs-Teile 
Diese Teile besitzen keine eigene Zeichnung, sie werden durch ein 2-D 
CAD-Modell unter der „siehe Sachnummer“ (z.B. ZB-Zeichnung, 
Tabellenzeichnung) dargestellt. 
 
VeDoc  
 
Vehicle Documentation System (Fahrzeugdokumentationssystem) / 
Mercedes-Benz  Dokumentationssystem zur 
Bauzustandsdokumentation. 
 
VPD-Ident-Nr 
Ein VPD-Ident ist eine eindeutige, baureihenübergreifende 
Kennnummer für ein variables Produktdatum, durch die  VeDoc-Daten 
einem konkreten Fahrzeug zugeordnet werden können. 
 
Zeichnungssachnummer 
Sachnummer unter der die Zeichnung archiviert ist (kann auch 
gleichzeitig eine Bauteilsachnummer sein). 
4 
Allgemeine Anforderungen 
Für Sicherheitsanforderungen, Zertifizierung (insbesondere Abgasemission) und Qualität sind alle 
relevanten rechtlichen Vorschriften und Gesetze zu erfüllen. Zusätzlich gelten die relevanten 
Anforderungen des Daimler Konzerns. 
 
In Bezug auf Inhaltsstoffe und Wiederverwertbarkeit müssen Materialien, Verfahrens- und 
Prozesstechnik, Bauteile und Systeme alle geltenden gesetzlichen Bestimmungen erfüllen. 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 4 页
MBN 10385:2015-08, Seite 4 
 
Copyright Daimler AG 
5 
Kennzeichnung VeDoc-relevanter Bauteile 
5.1 
Allgemeines 
Die in Abstimmung mit den Teileverantwortlichen festgelegten VeDoc-relevanten Bauteile sind zu  
kennzeichnen. Dabei erfolgt die VeDoc-Kennzeichnung der Bauteile in den PDM-Systemen (wie z.B.  
Smaragd und weiteren Systemen nachfolgender Bereiche). 
 
Muss ein VeDoc-relevantes Bauteil mit einer maschinell lesbaren Teilekennzeichnung (z.B. Barcode) 
gekennzeichnet werden, müssen folgende Angaben auf der Zeichnung dokumentiert werden: 
• 
Position des Datenträgers (VeDoc-Etikett, Label) am Bauteil 
• 
Barcodetyp,  
Hinweis: Es werden folgende Verfahren zur automatischen Identifikation und Datenerfassung 
verwendet: 
o 
Verfahren gemäß ISO/IEC 15417 zur Spezifikation des Strichcodes  
o 
Verfahren gemäß ISO/IEC 16022 zur Spezifikation der Daten Matrix 
 
Auf dem VeDoc-Etikett muss der maschinell lesbare Dateninhalt zusätzlich in Klarschrift 
dokumentiert werden. Hinweis: Datennutzer ist After Sales. 
 
Sobald ein VeDoc-Etikett und dessen Inhalt auf einer Zeichnung (Komponente, Montagevorschrift, 
…) hinzugefügt, entfernt oder verändert wird, müssen alle für die Darstellung des Teils relevanten 
Sachnummern mit ZGS-Erhöhung freigegeben werden (z.B. Darstellung Einzelteil mit eigener 
Sachnummer in ZB-Zeichnung, Einzelteilsachnummer).  
 
Datenfelder die zur Kennzeichnung und Dokumentation der VeDoc-Informationen benötigt werden, 
sind in Bild 1 dargestellt. 
 
 
Bild 1: Übersichtszeichnung Datenfelder VeDoc 
 
 
 
Tabellenerweiterung „Relevanz VeDoc“ 
Hinweispfeil VeDoc-
Etikett / Strichcode 
Hinweispfeil VeDoc-
Etikett / Daten Matrix 
Textfeld VeDoc-Etikett
Tabelle „bauteilspezifische Lieferantennummer“ 
Tabelle „Datenformat VeDoc-Etikett“ 
Datenfeld VeDoc Relevanz 
VeDoc-Etikett (fuer VPD-Ident: xxxx) 
Strichcode gemaess ISO/IEC 15417 
VeDoc-Etikett (fuer VPD-Ident:xxxx) 
Daten Matrix gemaess ISO/IEC 16022 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 5 页
MBN 10385:2015-08, Seite 5 
 
Copyright Daimler AG 
5.2 
Datenfeld VeDoc – Relevanz im Zeichnungsschriftfeld 
Zeichnungen werden durch die Angaben „X“ im Datenfeld „VeDoc-Relevanz“ und durch die Angabe 
des Kenners „999“  oder „888“ im Datenfeld „Anzahl  / number of VPD – Ident - Nr. / no“ im 
Zeichnungsschriftfeld gekennzeichnet. Die Anzahl der VPD-Identnummern wird im PDM System 
geführt. Zulässige Werte sind in Tabelle 1 aufgelistet. 
VeDoc – Relevanz / relevance MBN 10385 
 
Anzahl  / number of 
VPD – Ident - Nr. / no 
X 
999 
 
 
 
 
 
 
Bild 2: Ausschnitt Zeichnungsschriftfeld / Datenfeld VeDoc-Relevanz 
 
Tabelle 1: Zulässige Werte im Datenfeld VeDoc-Relevanz 
Spalte 
Wert 
Erläuterung 
VeDoc-Relevanz 
X 
Kennzeichnung der VeDoc-Relevanz 
Anzahl  / number of 
VPD – Ident - Nr. / no 
999 
Zusammen mit Kenner „X“ zeigt der Kenner „999“ an, dass das  
Bauteil ein VeDoc-relevantes Steuergerät oder Steuergeräte-
Peripherie-Teil ist, deren die VeDoc-Daten über die 
Diagnoseschnittstelle übertragen werden. 
Die Zuordnung des VPD-Ident zur Sachnummer erfolgt im PDM-
System. 
888 
Zusammen mit Kenner „X“ zeigt der Kenner „888“ an, dass zu dem 
Montageteil ein VeDoc-Etikett auf der Zeichnung dokumentiert ist. 
Die Zuordnung des VPD-Ident zur Sachnummer erfolgt im PDM-
System.  
900 
Werden auf einer Zeichnung VeDoc-Etiketten dokumentiert, die 
nicht zur Zeichnungssachnummer gehören wird der Kenner 900 
verwendet (z.B. TB-Zeichnung, Montagevorschrift VeDoc-Etikett, 
…).  
 
Hinweis: Sind auf einer TB-Zeichnung ausschließlich Steuergeräte 
oder Steuergeräte-Peripherie-Teile dokumentiert, deren VeDoc-
Daten über die Diagnoseschnittstelle übertragen werden, wird diese 
nicht mit dem Kenner „900“ gekennzeichnet. 
 
5.3 
Textfeld zur Kennzeichnung der Position eines VeDoc-Etikett  
Die Position des VeDoc-Etiketts wird durch ein Textfeld gekennzeichnet. 
  
 
 
Bild 3: Beispiel Textfeld VeDoc-Etikett 
 
VeDoc-Relevanz  
Anzahl  / number of VPD – 
Ident - Nr. / no
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 6 页
MBN 10385:2015-08, Seite 6 
 
Copyright Daimler AG 
5.4 
Hinweispfeil VeDoc-Etikett 
Die Kennzeichnung des VeDoc-Etiketts  erfolgt durch einen Hinweispfeil mit dem Text „VeDoc-
Etikett“. Zusätzlich wird im Textfeld des Hinweispfeiles die Norm, die das Verfahren der 
automatischen Identifikation und Datenerfassung festlegt dokumentiert.  
 
 
 
Bild 4: Beispiel Hinweispfeil VeDoc-Etikett für Strichcode 
 
 
 
Bild 5: Beispiel Hinweispfeil VeDoc-Etikett für Daten Matrix Code 
5.5 
Tabelle „Datenformat VeDoc-Etikett“ 
Die durch den VPD-Ident vorgegebenen Dateninhalte müssen auf der Zeichnung dokumentiert 
werden. Hierfür muss die Tabelle „Datenformat VeDoc-Etikett“ verwendet werden (sieheTabelle 2).  
 
Tabelle 2: Tabelle „Datenformat  VeDoc-Etikett“ 
Vorgaben VPD-Etikett für A-SNR: 
VPD-Ident Nr.: (xxxxx)  
Stellen 
Anzahl 
Inhalt 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Die Sachnummer, für die die Angaben des VeDoc-Etiketts gelten, muss nicht mit der Sachnummer 
der Zeichnung, auf der das Etikett dargestellt ist, übereinstimmen. 
 
Es muss für jede Variante des VeDoc-Ident, die auf der Zeichnung verwendet werden, eine Tabelle 
„Datenformat VeDoc-Etikett angelegt werden (siehe Beispiel Tabelle 3). 
 
Tabelle 3: Beispiel einer Tabelle „Datenformat VeDoc-Etikett“ für VPD-Ident 10008 
Vorgaben VPD-Etikett für A-SNR: A 123 456 78 90 
VPD-Ident Nr.: (10008) 
Stellen 
Anzahl 
Inhalt 
01-10 
10 
Sachnummer 
11-12 
2 
Lieferantennummer (teilespezifisch) 
13-17 
5 
Produktionsdatum (Jahr Jahr/Tag Tag Tag) 
18-21 
4 
Zählnummer/Chargennummer 
22-22 
1 
Prüfziffer (Modulo 10/Modulo11) 
 
VeDoc-Etikett (fuer VPD-Ident.: xxxxx) 
Strichcode gemaess ISO/IEC 15417 
VeDoc-Etikett (fuer VPD-Ident.: xxxxx) 
Daten Matrix Code gemaess ISO/IEC 16022 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 7 页
MBN 10385:2015-08, Seite 7 
 
Copyright Daimler AG 
Hinweis: Vorgehensweise für den BTV und Vorgabe der Stellen ist durch die Richtlinie C 4.2 
„Richtlinie Bauzustandsdokumentation in VeDoc“, abhängig vom VPD-Ident, verbindlich festgelegt.  
5.6 
Tabelle „bauteilspezifische Lieferantennummer“ 
Der Lieferantennummern des Einkaufs und der bauteilspezifischen Lieferantennummer werden 
Erläuterungen und zusätzliche Angaben durch den Bauteilverantwortlichen zugeordnet. Zum 
Beispiel können Produktionsstrukturen des jeweiligen Lieferanten erläutert werden. 
 
Für die Dokumentation auf der Zeichnung kann die Tabelle „bauteilspezifische Lieferantennummer“ 
verwendet werden (siehe Tabelle 4). In Tabelle 5 ist die Tabelle „bauteilspezifische 
Lieferantennummer“ mit Beispieldaten ausgefüllt dargestellt. 
 
Tabelle 4: Tabelle "bauteilspezifische Lieferantennummer" 
Vorgaben Lieferantennummer VPD-Etikett für SNR:  
Lieferantennummer Einkauf oder 
Werkskenner bei internen Lieferant 
Lieferanten-
nummer 
(teilespezifisch) 
Erläuterung 
Zusätzliche Angaben 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Hinweis: Vorgehensweise ist durch die Richtlinie C 4.2 „Richtlinie Bauzustandsdokumentation in 
VeDoc“ verbindlich festgelegt. 
 
Tabelle 5: Beispiel ausgefüllte Tabelle bauteilspezifische Lieferantennummer 
Vorgaben Lieferantennummer VPD-Etikett für SNR: A123456789 
Lieferantennummer Einkauf oder 
Werkskenner bei internen Lieferant 
Lieferanten-
nummer 
(teilespezifisch) 
Erläuterung 
Zusätzliche Angaben 
12345678 
01 
<Freitext - Externer Lieferant> 
<Freitext> 
W010 
02 
<Freitext - Interner Lieferant> 
<Freitext> 
 
 
 
 
 
 
 
 
 
5.7 
Tabellenerweiterung Sachnummerntabelle 
Werden in einer TB- bzw. ZB-Zeichnung kZ-siehe Bauteile dargestellt, müssen die VeDoc-Etikette 
dieser Bauteile in der TB- bzw. ZB-Zeichnung dokumentiert werden. Es werden die Position des 
VeDoc-Etiketts und der Dateninhalt dokumentiert.  
Die Bauteilsachnummer wird in der Sachnummerntabelle gekennzeichnet (siehe Tabelle 7). Hierfür 
muss eine zusätzliche Spalte in der Sachnummerntabelle hinzugefügt werden (siehe Tabelle 6) 
 
Tabellenerweiterung „Relevanz VeDoc“ 
 
Tabelle 6: Tabellenerweiterung Relevanz VeDoc 
 
 
 
 
 
 
 
Beispiel: Sachnummerntabelle mit Tabellenerweiterungen. 
 
Tabelle 7: Sachnummerntabelle mit Tabellenerweiterungen 
2 
A 999 456 78 90 kZ 
002 
G 
999 
1 
A 123 456 78 90 kZ 
001 
G 
999 
Pos. 
Sach-Nr. 
ZGS 
CAD 
VeDoc 
MBN 10385 
Basic number 
 
 
 
 
 
 
VeDoc 
MBN 10385 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 8 页
MBN 10385:2015-08, Seite 8 
 
Copyright Daimler AG 
6 
Dokumentationsverfahren Zeichnung 
6.1 
Allgemeines 
Bei VeDoc-Steuergeräte oder Steuergeräte-Peripherie-Teile zu beachten: 
• 
Die Kennzeichnung von Bauteilen deren VPD-Informationen über die Fahrzeugdiagnose 
ermittelt werden können, erfolgt ausschließlich im PDM-System. Hierbei handelt es sich um 
VeDoc-Steuergeräte oder Steuergeräte-Peripherie-Teile. In Tabelle 9 ist die Übersicht der 
Dokumentationsverfahren dokumentiert. 
• 
Handelt es sich um ein diagnosespezifisches VeDoc-Bauteil, z.B. um ein Steuergerät oder 
um ein Steuergeräte-Peripherie-Teil, wird im Schriftfeld der Zeichnung „999“ dokumentiert. 
• 
Hat das VeDoc-Steuergerät oder das Steuergeräte-Peripherie-Teil (VeDoc-Bauteil) keine 
eigene Zeichnung (kZ-siehe Bauteil), so wird die Bauteilsachnummer in der 
Sachnummerntabelle mit „999“ gekennzeichnet. 
 
Bei Kennzeichnung mit einem VeDoc-Etikett zu beachten: 
• 
Muss ein Bauteil mit einem VeDoc-Etikett gekennzeichnet werden, so muss dies auf einer 
Zeichnung dokumentiert werden. Im Schriftfeld der Zeichnung wird „888“ dokumentiert. In 
Tabelle 8 ist die Übersicht der Dokumentationsverfahren dokumentiert. 
• 
Der Dateninhalt des VeDoc-Etiketts muss auf der Zeichnung vorgegeben werden. 
• 
Teil des Dateninhaltes eines VeDoc-Etiketts ist die „bauteilspezifische Lieferantennummer“ 
(durch BTV vergeben). Die Definitionen zu diesen Lieferantennummern müssen auf der 
Zeichnung dokumentiert werden. 
• 
Wird für ein Bauteil ohne eigene Zeichnung ein VeDoc-Etikett benötigt, wird die 
Sachnummer im PDM-System mit 888 gekennzeichnet. Hinweis: Die Informationen für kZ-
siehe Bauteil sind auf der verknüpften ZB/TB-Zeichnung zu finden. 
• 
Werden auf einer Zeichnung VeDoc-Etiketten zur Zeichnungssachnummern und VeDoc-
Etiketten anderer Sachnummern dargestellt, wird die Zeichnungssachnummer mit 888 
gekennzeichnet. 
 
Zusätzlich ist zu beachten: 
• 
In einer ZB-/TB-Zeichnung wird eine VeDoc-relevante Bauteilsachnummer in der 
Sachnummerntabelle gekennzeichnet. Im Schriftfeld der ZB bzw. TB-Zeichnung wird die 
VeDoc-Relevanz mit „X“ und „900“ gekennzeichnet.  
• 
Hinweis: Die Anzahl der VPD-Identnummern wird nicht dokumentiert. 
 
Tabelle 8: Matrix Dokumentationsverfahren für VeDoc-Etikette auf Zeichnungen 
 
VeDoc-Etikett 
Zeichnungsarten 
 
 
Einzel
-teil 
Zusammen
-bau 
kZ-siehe 
Teil 
Einblatt 
Mehrblatt 
Dokumentations-
fall 
Einzelteil-
zeichnung 
X 
 
 
X 
 
Fall 1 
X 
 
 
 
X 
Fall 2 
Zusammen-
bau-
zeichnung 
(ZB) 
 
 
X 
 
X 
 
Fall 1 
 
X 
 
 
X 
Fall 2 
 
X 
X 
X 
 
Fall 3 
 
X 
X 
 
X 
Fall 4 
 
 
X 
X 
 
Fall 5 
 
 
X 
 
X 
Fall 6 
Tabellen-
zeichnung 
(TB) 
 
 
X 
X 
 
Fall 5 
 
 
X 
 
X 
Fall 6 
 
 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 9 页
MBN 10385:2015-08, Seite 9 
 
Copyright Daimler AG 
Tabelle 9: Matrix Dokumentationsverfahren für Steuergeräte und Steuergerät-Peripherie-Teile 
 
VeDoc-Kennzeichnung: 
Steuergerät oder Steuergeräte-
Peripherie-Teil 
Zeichnungsarten 
 
 
Einzel
-teil 
Zusammen
-bau 
kZ-siehe 
Teil 
Einblatt 
Mehrblatt 
Dokumentations-
fall 
Einzelteil-
zeichnung 
X 
 
 
X 
 
Fall 7 
X 
 
 
 
X 
Fall 8 
Zusammen-
bau-
zeichnung 
(ZB) 
 
 
X 
 
X 
 
Fall 7 
 
X 
 
 
X 
Fall 8 
 
X 
X 
X 
 
Fall 9 
 
X 
X 
 
X 
Fall 10 
 
 
X 
X 
 
Fall 11 
 
 
X 
 
X 
Fall 12 
Tabellen-
zeichnung 
(TB) 
 
 
X 
X 
 
Fall 11 
 
 
X 
 
X 
Fall 12 
Hinweis: 
TB-Zeichnungen, die kZ-siehe – Sachnummern von VeDoc relevanten Steuergeräten und 
Steuergerät-Peripherie-Teilen enthalten, dürfen nicht als VeDoc – relevant 
gekennzeichnet und nicht mit dem Kenner „900“ versehen werden, der Kenner entfällt. 
6.2 
Fall 1: Einblatt „Bauteil mit VeDoc-Etikett (Einzelteil oder ZB)“ 
Das Dokumentationsverfahren zu Fall 1 wird in Tabelle 10 beschrieben. 
 
Tabelle 10: Dokumentationsverfahren Fall 1 
Blatt 
Zeichnungs-
bereich 
zu verwendende Datenfelder
Beschreibung 
der Aufgabe 
Wert
Blatt 
Schriftfeld  
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
X 
888 
In Spalte „VeDoc-
Relevanz“ Relevanz 
kennzeichnen 
X 
Anzahl / number of 
VPD-Ident-Nr. / no 
888 
Zeichnungs-
feld 
Textfeld 
VeDoc-
Etikett 
 
Position des VeDoc-
Etikett festlegen 
Textfeld 
positioniert 
Hinweispfeil 
VeDoc-
Etikett 
Verfahren zur 
automatischen 
Identifikation und 
Datenerfassung 
festlegen 
ISO/IEC 
15417 
oder  
ISO/IEC 
16022 
Tabelle 
Datenformat 
VeDoc-
Etikett 
 
 
Vorgaben VPD-Etikett für A-SNR: A 123 456 78 90 
VPD-Ident Nr.: (10008) 
Stellen 
Anzahl 
Inhalt 
01-10 
10 
Sachnummer 
11-12 
2 
Lieferantennummer 
13-17 
5 
Produktionsdatum (Jahr Jahr/Tag 
Tag Tag) 
18-21 
4 
Zählnummer/Chargennummer 
22-22 
1 
Prüfziffer (Modulo 10/Modulo11) 
Vorgabewerte, 
abhängig von VPD-
Ident Nummer und 
Bauteilnummer in 
Tabelle 
dokumentieren. 
Tabelle ggf. 
erweitern 
Vorgaben 
VPD-Ident 
eingetragen 
Tabelle 
bauteil-
spezifische 
Lieferanten-
nummer 
(optional) 
 
 
 
Vorgaben Lieferantennummer VPD-Etikett für SNR: A123456789
Lieferantennummer Einkauf oder 
Werkskenner bei internen Lieferant 
Lieferantennummer 
(teilespezifisch) 
Erläuterung
Zusätzliche
 Angaben 
12345678 
01 
Erklärung 1 
Montagelinie 1 
88888888 
02 
Erklärung 2 
Montagelinie 2 
99999999 
10 
Erklärung 3 
Montagelinie 1 
555555555 
20 
Erklärung 4 
Montagelinie 2
Dokumentation der 
durch den BTV 
zugeordneten 
Lieferantennummer 
u.a. 
Lieferanten-
nummern + 
bauteil-
spezifische 
Lieferanten-
nummer +. 
Erläuterung 
VeDoc-Etikett (fuer VPD-Ident.: 10008) 
Strichcode gemaess ISO/IEC 15417
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 10 页
MBN 10385:2015-08, Seite 10 
 
Copyright Daimler AG 
 
6.3 
Fall 2: Mehrblatt „Bauteil mit VeDoc-Etikett (Einzelteil oder ZB)“ 
Das Dokumentationsverfahren zu Fall 2 wird in Tabelle 11 beschrieben. 
 
Tabelle 11: Dokumentationsverfahren Fall 2 
Blatt 
Zeichnungs-
bereich 
zu verwendende Datenfelder
Beschreibung 
der Aufgabe 
Wert
Blatt 0 
Schriftfeld  
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
X 
888 
In Spalte „VeDoc-
Relevanz“ Relevanz 
kennzeichnen 
X 
Anzahl / number of 
VPD-Ident-Nr. / no 
888 
Blatt n 
Schriftfeld 
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
 
 
Auf den 
Folgeblättern wird 
keine Anzahl und 
Relevanz 
eingetragen. 
„leer“ / „0“ 
Zeichnungs-
feld 
Textfeld 
VeDoc-
Etikett 
 
Position des VeDoc-
Etikett festlegen 
Textfeld 
positioniert 
Hinweispfeil 
VeDoc-
Etikett 
Verfahren zur 
automatischen 
Identifikation und 
Datenerfassung 
festlegen 
ISO/IEC 
15417 
oder  
ISO/IEC 
16022 
Tabelle 
Datenformat 
VeDoc-
Etikett 
 
 
Vorgaben VPD-Etikett für A-SNR: A 123 456 78 90 
VPD-Ident Nr.: (10008) 
Stellen 
Anzahl 
Inhalt 
01-10 
10 
Sachnummer 
11-12 
2 
Lieferantennummer 
13-17 
5 
Produktionsdatum (Jahr Jahr/Tag 
Tag Tag) 
18-21 
4 
Zählnummer/Chargennummer 
22-22 
1 
Prüfziffer (Modulo 10/Modulo11) 
Vorgabewerte, 
abhängig von VPD-
Ident Nummer und 
Bauteilnummer in 
Tabelle 
dokumentieren. 
Tabelle ggf. 
erweitern. 
Vorgaben 
VPD-Ident 
eingetragen 
Tabelle 
bauteil-
spezifische 
Lieferanten-
nummer 
 
 
 
 
Vorgaben Lieferantennummer VPD-Etikett für SNR: A123456789
Lieferantennummer Einkauf oder 
Werkskenner bei internen Lieferant 
Lieferantennummer 
(teilespezifisch) 
Erläuterung
Zusätzliche
 Angaben 
12345678 
01 
Erklärung 1 
Montagelinie 1 
88888888 
02 
Erklärung 2 
Montagelinie 2 
99999999 
10 
Erklärung 3 
Montagelinie 1 
555555555 
20 
Erklärung 4 
Montagelinie 2 
Dokumentation der 
durch den BTV 
zugeordneten 
Lieferantennummer 
u.a. 
Lieferanten-
nummern + 
bauteil-
spezifische 
Lieferanten-
nummer +. 
Erläuterung 
 
 
 
VeDoc-Etikett  (fuer VPD-Ident.: 10008) 
Strichcode gemaess ISO/IEC 15417
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 11 页
MBN 10385:2015-08, Seite 11 
 
Copyright Daimler AG 
6.4 
Fall 3: Einblatt „ZB und kZ-siehe Bauteil mit VeDoc-Etikett“ 
Das Dokumentationsverfahren zu Fall 3 wird in Tabelle 12 beschrieben. 
 
Tabelle 12: Dokumentationsverfahren Fall 3 
Blatt 
Zeichnungs-
bereich 
zu verwendende Datenfelder
Beschreibung 
der Aufgabe 
Wert
Blatt 
Schriftfeld  
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
X 
888 
In Spalte „VeDoc-
Relevanz“ Relevanz 
kennzeichnen 
X 
Anzahl / number of 
VPD-Ident-Nr. / no 
888 
Zeichnungs-
feld 
Textfeld 
VeDoc-
Etikett für ZB 
 
Position des VeDoc-
Etikett festlegen 
Textfeld 
positioniert 
Hinweispfeil 
VeDoc-
Etikett für ZB 
Verfahren zur 
automatischen 
Identifikation und 
Datenerfassung 
festlegen 
ISO/IEC 
15417 
oder  
ISO/IEC 
16022 
Tabelle 
Datenformat 
VeDoc-
Etikett für ZB 
Beispiel 
 
 
Vorgaben VPD-Etikett für A-SNR: A 123 456 78 90 
VPD-Ident Nr.: (10008) 
Stellen 
Anzahl 
Inhalt 
01-10 
10 
Sachnummer 
11-12 
2 
Lieferantennummer 
13-17 
5 
Produktionsdatum (Jahr Jahr/Tag 
Tag Tag) 
18-21 
4 
Zählnummer/Chargennummer 
22-22 
1 
Prüfziffer (Modulo 10/Modulo11) 
Vorgabewerte, 
abhängig von VPD-
Ident Nummer und 
Bauteilnummer in 
Tabelle 
dokumentieren. 
Tabelle ggf. 
erweitern 
Vorgaben 
VPD-Ident 
eingetragen 
Tabellen-
erweiterung 
Relevanz 
VeDoc 
 
888 
 
888 
VeDoc 
MBN 10385 
Tabellenerweiterung 
Anzahl Relevanz 
VeDoc an 
Sachnummern- 
tabelle hinzufügen 
Erweiterte 
Tabelle 
kZ-siehe Bauteil mit 
VeDoc-Etikett 
kennzeichnen 
888 
Textfeld 
VeDoc-
Etikett für 
kZ-siehe 
Bauteil 
 
Position des VeDoc-
Etikett festlegen 
Textfeld 
positioniert 
Hinweispfeil 
VeDoc-
Etikett für 
kZ-siehe 
Bauteil 
Verfahren zur 
automatischen 
Identifikation und 
Datenerfassung 
festlegen 
ISO/IEC 
15417 
oder  
ISO/IEC 
16022 
Tabelle 
Datenformat 
VeDoc-
Etikett für 
kZ-siehe 
Bauteil 
 
 
Vorgaben VPD-Etikett für A-SNR: A 123 456 78 88  
VPD-Ident Nr.: (12222) 
Stellen 
Anzahl 
Inhalt 
01-10 
10 
Sachnummer 
11-12 
2 
Lieferantennummer 
13-17 
5 
Produktionsdatum (Jahr Jahr/Tag 
Tag Tag) 
18-21 
4 
Zählnummer/Chargennummer 
22-22 
1 
Prüfziffer (Modulo 10/Modulo11) 
Vorgabewerte, 
abhängig von VPD-
Ident Nummer und 
Bauteilnummer in 
Tabelle 
dokumentieren. 
Tabelle ggf. 
erweitern 
Vorgaben 
VPD-Ident 
eingetragen 
Tabelle 
bauteil-
spezifische 
Lieferanten-
nummer für 
ZB und kZ-
siehe Bauteil 
 (optional) 
Vorgaben Lieferantennummer VPD-Etikett für SNR: A 123 456 78 88, A 123 456 78 90
Lieferantennummer Einkauf oder 
Werkskenner bei internen Lieferant 
Lieferantennummer 
(teilespezifisch) 
Erläuterung
Zusätzliche
 Angaben 
12345678 
01 
Erklärung 1 
Montagelinie 1 
88888888 
02 
Erklärung 2 
Montagelinie 2 
99999999 
10 
Erklärung 3 
Montagelinie 1 
W010 
20 
Erklärung 4 
Montagelinie 2
 
 
Dokumentation der 
durch den BTV 
zugeordneten 
Lieferantennummer 
u.a. 
Lieferanten-
nummern + 
bauteil-
spezifische 
Lieferanten-
nummer +. 
Erläuterung 
 
 
 
VeDoc-Etikett (fuer VPD-Ident.: 10008) 
Strichcode gemaess ISO/IEC 15417 
VeDoc-Etikett (fuer VPD-Ident.: 12222) 
Strichcode gemaess ISO/IEC 15417
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 12 页
MBN 10385:2015-08, Seite 12 
 
Copyright Daimler AG 
6.5 
Fall 4 Mehrblatt „ZB und kZ-siehe Bauteil mit VeDoc-Etikett“ 
Das Dokumentationsverfahren zu Fall 4 wird in Tabelle 13 beschrieben. 
 
Tabelle 13: Dokumentationsverfahren Fall 4 
Blatt 
Zeichnungs-
bereich 
zu verwendende Datenfelder
Beschreibung 
der Aufgabe 
Wert
Blatt 0 
Schriftfeld  
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
X 
888 
In Spalte „VeDoc-
Relevanz“ Relevanz 
kennzeichnen 
X 
Anzahl / number of 
VPD-Ident-Nr. / no 
888 
Blatt n 
Schriftfeld 
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
 
 
Auf den 
Folgeblättern wird 
keine Anzahl und 
Relevanz 
eingetragen. 
„leer“ / „0“ 
Zeichnungs-
feld 
Textfeld 
VeDoc-
Etikett für ZB 
 
Position des VeDoc-
Etikett festlegen 
Textfeld 
positioniert 
Hinweispfeil 
VeDoc-
Etikett für ZB 
Verfahren zur 
automatischen 
Identifikation und 
Datenerfassung 
festlegen 
ISO/IEC 
15417 
oder  
ISO/IEC 
16022 
Tabelle 
Datenformat 
VeDoc-
Etikett für ZB 
Beispiel
 
Vorgaben VPD-Etikett für A-SNR: A 123 456 78 90 
VPD-Ident Nr.: (10008) 
Stellen 
Anzahl 
Inhalt 
01-10 
10 
Sachnummer 
11-12 
2 
Lieferantennummer 
13-17 
5 
Produktionsdatum (Jahr Jahr/Tag 
Tag Tag) 
18-21 
4 
Zählnummer/Chargennummer 
22-22 
1 
Prüfziffer (Modulo 10/Modulo11) 
Vorgabewerte, 
abhängig von VPD-
Ident Nummer und 
Bauteilnummer in 
Tabelle dokumen-
tieren. Tabelle ggf. 
erweitern. 
Vorgaben 
VPD-Ident 
eingetragen 
Textfeld 
VeDoc-
Etikett für 
kZ-siehe 
Bauteil 
 
Position und Größe 
des VeDoc-Etikett 
festlegen 
Textfeld 
positioniert 
Hinweispfeil 
VeDoc-
Etikett für 
kZ-siehe 
Bauteil 
Verfahren zur 
automatischen 
Identifikation und 
Datenerfassung 
festlegen 
ISO/IEC 
15417 
oder  
ISO/IEC 
16022 
Tabelle 
Datenformat 
VeDoc-
Etikett für 
kZ-siehe 
Bauteil 
Beispiel
 
Vorgaben VPD-Etikett für A-SNR: A 123 456 78 88 
VPD-Ident Nr.: (12222) 
Stellen 
Anzahl 
Inhalt 
01-10 
10 
Sachnummer 
11-12 
2 
Lieferantennummer 
13-17 
5 
Produktionsdatum (Jahr Jahr/Tag 
Tag Tag) 
18-21 
4 
Zählnummer/Chargennummer 
22-22 
1 
Prüfziffer (Modulo 10/Modulo11) 
Vorgabewerte, 
abhängig von VPD-
Ident Nummer und 
Bauteilnummer in 
Tabelle 
dokumentieren. 
Tabelle ggf. 
erweitern 
Vorgaben 
VPD-Ident 
eingetragen 
Tabelle 
bauteil-
spezifische 
Lieferanten-
nummer für 
ZB und kZ-
siehe Bauteil 
Beispiel (optional) 
Vorgaben Lieferantennummer VPD-Etikett für SNR: A 123 456 78 88, A 123 456 78 90
Lieferantennummer Einkauf oder 
Werkskenner bei internen Lieferant 
Lieferantennummer 
(teilespezifisch) 
Erläuterung
Zusätzliche
 Angaben 
12345678 
01 
Erklärung 1 
Montagelinie 1 
88888888 
02 
Erklärung 2 
Montagelinie 2 
99999999 
10 
Erklärung 3 
Montagelinie 1 
555555555 
20 
Erklärung 4 
Montagelinie 2
 
 
Dokumentation der 
durch den BTV 
zugeordneten 
Lieferantennummer 
u.a. 
Lieferanten-
nummern + 
bauteil-
spezifische 
Lieferanten-
nummer +. 
Erläuterung 
Tabellen-
erweiterung 
Relevanz 
VeDoc 
 
888 
 
888 
VeDoc 
MBN 10385 
Tabellenerweiterung 
Anzahl Relevanz 
VeDoc an 
Sachnummern- 
tabelle hinzufügen. 
Erweiterte 
Tabelle 
kZ-siehe Bauteil mit 
VeDoc-Etikett 
kennzeichnen. 
888 
 
 
 
VeDoc-Etikett (fuer VPD-Ident.: 10008) 
Strichcode gemaess ISO/IEC 15417
VeDoc-Etikett (fuer VPD-Ident.: 12222) 
Strichcode gemaess ISO/IEC 15417
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 13 页
MBN 10385:2015-08, Seite 13 
 
Copyright Daimler AG 
6.6 
Fall 5 Einblatt „kZ-siehe Bauteil mit VeDoc-Etikett in TB-/ ZB-Zeichnung“ 
Das Dokumentationsverfahren zu Fall 5 wird in Tabelle 14 beschrieben. 
 
Tabelle 14: Dokumentationsverfahren Fall 5 
Blatt 
Zeichnungs-
bereich 
zu verwendende Datenfelder
Beschreibung 
der Aufgabe 
Wert
Blatt 
Schriftfeld  
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
X 
900 
In Spalte „VeDoc-
Relevanz“ Relevanz 
kennzeichnen 
X 
Anzahl / number of 
VPD-Ident-Nr. / no 
900 
Zeichnungs-
feld 
Textfeld 
VeDoc-
Etikett für 
kZ-siehe 
Bauteil 
 
Position des VeDoc-
Etikett festlegen 
Textfeld 
positioniert 
Hinweispfeil 
VeDoc-
Etikett für 
kZ-siehe 
Bauteil 
Verfahren zur 
automatischen 
Identifikation und 
Datenerfassung 
festlegen 
ISO/IEC 
15417 
oder  
ISO/IEC 
16022 
Tabelle 
Datenformat 
VeDoc-
Etikett für 
kZ-siehe 
Bauteil 
 
 
Vorgaben VPD-Etikett für A-SNR: A 123 456 78 88 
VPD-Ident Nr.: (10008) 
Stellen 
Anzahl 
Inhalt 
01-10 
10 
Sachnummer 
11-12 
2 
Lieferantennummer 
13-17 
5 
Produktionsdatum (Jahr Jahr/Tag 
Tag Tag) 
18-21 
4 
Zählnummer/Chargennummer 
22-22 
1 
Prüfziffer (Modulo 10/Modulo11) 
Vorgabewerte, 
abhängig von VPD-
Ident Nummer und 
Bauteilnummer in 
Tabelle 
dokumentieren. 
Tabelle ggf. 
erweitern 
Vorgaben 
VPD-Ident 
eingetragen 
Tabellen-
erweiterung 
Relevanz 
VeDoc 
 
888 
 
888 
VeDoc 
MBN 10385 
Tabellenerweiterung 
Anzahl Relevanz 
VeDoc an 
Sachnummern- 
tabelle hinzufügen. 
Erweiterte 
Tabelle 
kZ-siehe Bauteil mit 
VeDoc-Etikett 
kennzeichnen. 
888 
Tabelle 
bauteil-
spezifische 
Lieferanten-
nummer für 
kZ-siehe 
Bauteil 
(optional) 
 
 
 
Vorgaben Lieferantennummer VPD-Etikett für SNR: A123456789
Lieferantennummer Einkauf oder 
Werkskenner bei internen Lieferant 
Lieferantennummer 
(teilespezifisch) 
Erläuterung
Zusätzliche
 Angaben 
12345678 
01 
Erklärung 1 
Montagelinie 1 
88888888 
02 
Erklärung 2 
Montagelinie 2 
99999999 
10 
Erklärung 3 
Montagelinie 1 
555555555 
20 
Erklärung 4 
Montagelinie 2 
Dokumentation der 
durch den BTV 
zugeordneten 
Lieferantennummer 
u.a. 
Lieferanten-
nummern + 
bauteil-
spezifische 
Lieferanten-
nummer +. 
Erläuterung 
 
 
 
VeDoc-Etikett (fuer VPD-Ident.: 10008) 
Strichcode gemaess ISO/IEC 15417 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 14 页
MBN 10385:2015-08, Seite 14 
 
Copyright Daimler AG 
6.7 
Fall 6 Mehrblatt „kZ-siehe Bauteil mit VeDoc-Etikett in TB-/ ZB-Zeichnung“ 
Das Dokumentationsverfahren zu Fall 6 wird in Tabelle 15 beschrieben. 
 
Tabelle 15: Dokumentationsverfahren Fall 6 
Blatt 
Zeichnungs-
bereich 
zu verwendende Datenfelder
Beschreibung 
der Aufgabe 
Wert
Blatt 0 
Schriftfeld  
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
X 
900 
In Spalte „VeDoc-
Relevanz“ Relevanz 
kennzeichnen 
X 
Anzahl / number of 
VPD-Ident-Nr. / no 
900 
Blatt n 
Schriftfeld 
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
 
 
Auf den 
Folgeblättern wird 
keine Anzahl und 
Relevanz 
eingetragen. 
„leer“ / „0“ 
Zeichnungs-
feld 
Textfeld 
VeDoc-
Etikett für 
kZ-siehe 
Bauteil 
 
Position des VeDoc-
Etikett festlegen 
Textfeld 
positioniert 
Hinweispfeil 
VeDoc-
Etikett für 
kZ-siehe 
Bauteil 
Verfahren zur 
automatischen 
Identifikation und 
Datenerfassung 
festlegen 
ISO/IEC 
15417 
oder  
ISO/IEC 
16022 
Tabelle 
Datenformat 
VeDoc-
Etikett für 
kZ-siehe 
Bauteil 
 
 
Vorgaben VPD-Etikett für A-SNR: A 123 456 78 88 
VPD-Ident Nr.: (10008) 
Stellen 
Anzahl 
Inhalt 
01-10 
10 
Sachnummer 
11-12 
2 
Lieferantennummer 
13-17 
5 
Produktionsdatum (Jahr Jahr/Tag 
Tag Tag) 
18-21 
4 
Zählnummer/Chargennummer 
22-22 
1 
Prüfziffer (Modulo 10/Modulo11) 
Vorgabewerte, 
abhängig von VPD-
Ident Nummer und 
Bauteilnummer in 
Tabelle 
dokumentieren. 
Tabelle ggf. 
erweitern. 
Vorgaben 
VPD-Ident 
eingetragen 
Tabellen-
erweiterung 
Relevanz 
VeDoc 
 
888 
 
888 
VeDoc 
MBN 10385 
Tabellenerweiterung 
Anzahl Relevanz 
VeDoc an 
Sachnummern- 
tabelle hinzufügen. 
Erweiterte 
Tabelle 
kZ-siehe Bauteil mit 
VeDoc-Etikett 
kennzeichnen. 
888 
Tabelle 
bauteil-
spezifische 
Lieferanten-
nummer für 
kZ-siehe 
Bauteil 
(optional) 
 
 
 
Vorgaben Lieferantennummer VPD-Etikett für SNR: A123456789
Lieferantennummer Einkauf oder 
Werkskenner bei internen Lieferant 
Lieferantennummer 
(teilespezifisch) 
Erläuterung
Zusätzliche
 Angaben 
12345678 
01 
Erklärung 1 
Montagelinie 1 
88888888 
02 
Erklärung 2 
Montagelinie 2 
99999999 
10 
Erklärung 3 
Montagelinie 1 
555555555 
20 
Erklärung 4 
Montagelinie 2 
Dokumentation der 
durch den BTV 
zugeordneten 
Lieferantennummer 
u.a. 
Lieferanten-
nummern + 
bauteil-
spezifische 
Lieferanten-
nummer +. 
Erläuterung 
 
 
 
VeDoc-Etikett (fuer VPD-Ident.: 10008) 
Strichcode gemaess ISO/IEC 15417 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 15 页
MBN 10385:2015-08, Seite 15 
 
Copyright Daimler AG 
6.8 
Fall 7: Einblatt „VeDoc Steuergerät oder Steuergeräte-Peripherie-Teil (Einzelteil 
oder ZB)“ 
Das Dokumentationsverfahren zu Fall 7 wird in Tabelle 16 beschrieben. 
 
Tabelle 16: Dokumentationsverfahren Fall 7 
Blatt 
Zeichnungs-
bereich 
zu verwendende Datenfelder
Beschreibung 
der Aufgabe 
Wert
Blatt 
Schriftfeld  
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
X 
999 
In Spalte „VeDoc-
Relevanz“ Relevanz 
kennzeichnen 
X 
Anzahl / number of 
VPD-Ident-Nr. / no 
999 
6.9 
Fall 8: Mehrblatt „VeDoc-Steuergerät oder Steuergeräte-Peripherie-Teil 
(Einzelteil oder ZB)“ 
Das Dokumentationsverfahren zu Fall 8 wird in Tabelle 17 beschrieben. 
 
Tabelle 17: Dokumentationsverfahren Fall 8 
Blatt 
Zeichnungs-
bereich 
zu verwendende Datenfelder
Beschreibung 
der Aufgabe 
Wert
Blatt 0 
Schriftfeld  
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
X 
999 
In Spalte „VeDoc-
Relevanz“ Relevanz 
kennzeichnen 
X 
Anzahl / number of 
VPD-Ident-Nr. / no 
999 
Blatt n 
Schriftfeld 
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
 
 
Auf den 
Folgeblättern wird 
keine Anzahl und 
Relevanz 
eingetragen. 
„leer“ / „0“ 
6.10 Fall 9: Einblatt „ZB und kZ-siehe Bauteil VeDoc Steuergerät oder Steuergeräte-
Peripherie-Teil“ 
Das Dokumentationsverfahren zu Fall 9 wird in Tabelle 18 beschrieben. 
 
Tabelle 18: Dokumentationsverfahren Fall 9 
Blatt 
Zeichnungs-
bereich 
zu verwendende Datenfelder
Beschreibung 
der Aufgabe 
Wert
Blatt 
Schriftfeld  
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
X 
999 
In Spalte „VeDoc-
Relevanz“ Relevanz 
kennzeichnen 
X 
Anzahl / number of 
VPD-Ident-Nr. / no 
999 
Zeichnungs-
feld 
Tabellen-
erweiterung 
Relevanz 
VeDoc 
 
999 
 
999 
VeDoc 
MBN 10385 
Tabellenerweiterung 
Anzahl Relevanz 
VeDoc an 
Sachnummern- 
tabelle hinzufügen. 
Erweiterte 
Tabelle 
kZ-siehe Bauteil mit 
VeDoc-Etikett 
kennzeichnen. 
999 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 16 页
MBN 10385:2015-08, Seite 16 
 
Copyright Daimler AG 
6.11 Fall 10: Mehrblatt „ZB und kZ-siehe Bauteil VeDoc Steuergerät oder 
Steuergeräte-Peripherie-Teil“ 
Das Dokumentationsverfahren zu Fall 10 wird in Tabelle 19 beschrieben. 
 
Tabelle 19: Dokumentationsverfahren Fall 10 
Blatt 
Zeichnungs-
bereich 
zu verwendende Datenfelder
Beschreibung 
der Aufgabe 
Wert
Blatt 0 
Schriftfeld  
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
X 
999 
In Spalte „VeDoc-
Relevanz“ Relevanz 
kennzeichnen 
X 
Anzahl / number of 
VPD-Ident-Nr. / no 
999 
Blatt n 
Zeichnungs-
feld 
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
 
 
Auf den 
Folgeblättern wird 
keine Anzahl und 
Relevanz 
eingetragen. 
„leer“ / „0“ 
Tabellen-
erweiterung 
Relevanz 
VeDoc 
 
999 
 
999 
VeDoc 
MBN 10385 
Tabellenerweiterung 
Anzahl Relevanz 
VeDoc an 
Sachnummern- 
tabelle hinzufügen. 
Erweiterte 
Tabelle 
kZ-siehe Bauteil mit 
VeDoc-Etikett 
kennzeichnen. 
999 
6.12 Fall 11: Einblatt „kZ-siehe Bauteil VeDoc Steuergerät oder Steuergeräte-
Peripherie-Teil in TB-/ ZB-Zeichnung“ 
Das Dokumentationsverfahren zu Fall 11 wird in Tabelle 20 beschrieben. 
 
Tabelle 20: Dokumentationsverfahren Fall 11 
Blatt 
Zeichnungs-
bereich 
zu verwendende Datenfelder
Beschreibung 
der Aufgabe 
Wert
Blatt 
Schriftfeld  
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
 
 
In Spalte „VeDoc-
Relevanz“ Relevanz 
kennzeichnen 
„leer“ / „0“ 
Anzahl / number of 
VPD-Ident-Nr. / no 
„leer“ / „0“ 
Zeichnungs-
feld 
Tabellen-
erweiterung 
Relevanz 
VeDoc 
 
999 
 
999 
VeDoc 
MBN 10385 
Tabellenerweiterung 
Anzahl Relevanz 
VeDoc an 
Sachnummern- 
tabelle hinzufügen. 
Erweiterte 
Tabelle 
kZ-siehe Bauteil mit 
VeDoc-Etikett 
kennzeichnen. 
999 
 
 
 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25


### 第 17 页
MBN 10385:2015-08, Seite 17 
 
Copyright Daimler AG 
6.13 Fall 12: Mehrblatt „kZ-siehe Bauteil VeDoc Steuergerät oder Steuergeräte-
Peripherie-Teil in TB-/ ZB-Zeichnung“ 
Das Dokumentationsverfahren zu Fall 12 wird in Tabelle 21 beschrieben. 
 
Tabelle 21: Dokumentationsverfahren Fall 12 
Blatt 
Zeichnungs-
bereich 
zu verwendende Datenfelder
Beschreibung 
der Aufgabe 
Wert
Blatt 0 
Schriftfeld  
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
 
 
In Spalte „VeDoc-
Relevanz“ Relevanz 
kennzeichnen 
„leer“ / „0“ 
Anzahl / number of 
VPD-Ident-Nr. / no 
„leer“ / „0“ 
Blatt n 
Schriftfeld 
Datenfeld 
VeDoc-
Relevanz 
(integriert im 
Schriftfeld) 
VeDoc-Relevanz / relevance MBN10385
 
Anzahl / number of 
VPD-Ident-Nr. / no. 
 
 
Auf den 
Folgeblättern wird 
keine Anzahl und 
Relevanz 
eingetragen. 
„leer“ / „0“ 
Zeichnungs-
feld 
Tabellen-
erweiterung 
Relevanz 
VeDoc 
 
999 
 
999 
VeDoc 
MBN 10385 
Tabellenerweiterung 
Anzahl Relevanz 
VeDoc an 
Sachnummern- 
tabelle hinzufügen. 
Erweiterte 
Tabelle 
kZ-siehe Bauteil mit 
VeDoc-Etikett 
kennzeichnen. 
999 
 
 
6.14 Ausschnittszeichnung 
Für die vereinfachte Darstellung von Bauteilen werden Ausschnittszeichnungen verwendet. 
Ausschnittszeichnungen referenzieren für fehlende Angaben auf weitere Zeichnungen 
(Bezugszeichnungen).  
 
Ist die Sachnummer der Ausschnittszeichnung VeDoc-relevant (VPD-Kenner im PDM-System 
zugeordnet) muss das auf der Ausschnittszeichnung dokumentiert werden. Die Beschreibung des 
VeDoc-Etiketts kann auf der Bezugszeichnung dokumentiert sein. Ein entsprechender Verweis („Alle 
weiteren Masse und Angaben siehe <Sachnummer >“) muss auf der Ausschnittszeichnung 
dokumentiert werden. 
 
Hinweis: Vorgaben zur Referenz auf Bezugszeichnungen sind in der V 999 8011 geregelt. 
Unkontrollierte Kopie bei Ausdruck
RD/UBF: Stefan Kehrt, 2018-11-25

