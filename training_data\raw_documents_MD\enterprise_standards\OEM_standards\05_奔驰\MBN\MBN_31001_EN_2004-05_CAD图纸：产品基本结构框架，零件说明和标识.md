# MBN_31001_EN_2004-05_CAD图纸：产品基本结构框架，零件说明和标识.pdf

## 文档信息
- 标题：Microsoft Word - mbn 31001_2004-05_e.doc
- 作者：
- 页数：15

## 文档内容
### 第 1 页
Mercedes-Benz
MBN 31 001
Engineering Standard
Date Published:  2004-05
Category:  31
Total No. of Pages (Including Annex): 15
Person in Charge:Mogwitz / Schloz
Plant 19; Dept.:EP/QIN
Date of Translation:  2004-06
Phone: 3 49 05 / 3 49 81
CAD drawings
General principles of product presentation
Basic design limits
Presentation and identification of parts
CAD-Zeichnungen; Grundlage der Produktdarstellung; Basis-Konstruktionsrahmen, Darstellung und
Kennzeichnung von Teilen
Foreword
This standard supplies basic information with the aim of standardizing product presentation on
Technical Product Documentation CAD drawings.
All standards referenced in this standard are available within the "Standards Information System"
(SIS).
Changes
In comparison with edition 2001-12, the following changes have been made:
- Standard represented on the basis of new standard template; sequence number 31 051 deleted
- Title was "General principles of CAD drawings; drawing frames, frame sizes, presentation of parts,
identification of parts"
- New Section 3, Abbreviations, acronyms..., inserted; followed by all further Sections
- CATIA version V5 status included
- Number of cited standards corrected
NOTE: No guarantee can be given in respect of this translation. In all cases the latest
German-language version of this Standard shall be taken as authoritative.
Copyright DaimlerChrysler 2004
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 2 页
MBN 31 001, 2004-05, Page 2
Copyright DaimlerChrysler 2004
Contents
Basic design limits..........................................................................................................................................1
1
Scope ......................................................................................................................................................3
2
Normative References.............................................................................................................................3
3
Abbreviations, Acronyms, Definitions, & Symbols ..................................................................................3
4
Design frame...........................................................................................................................................3
4.1
Design frame types and sizes..........................................................................................................3
4.2
Provision of design frames...............................................................................................................5
4.3
Elements for the design frame.........................................................................................................6
5
Presentation of parts on CAD drawings..................................................................................................7
5.1
General presentation and layout of the view....................................................................................7
5.2
Views in body construction ..............................................................................................................8
5.3
Grid line representation....................................................................................................................8
5.4
Oblique views on drawings ..............................................................................................................9
5.5
Symmetrical and mirror image parts................................................................................................9
5.6
Views................................................................................................................................................9
5.7
Sections ...........................................................................................................................................9
5.8
Lines.................................................................................................................................................9
5.9
Hatching ...........................................................................................................................................9
5.10
Lettering and lettering sizes .............................................................................................................9
5.11
Scales for representing parts and drawing scales of CAD drawings during generation..................9
5.12
Entry of dimensions and dimensioning in oblique views on CAD drawings ..................................10
5.13
Tolerancing ....................................................................................................................................10
5.14
Surface quality ...............................................................................................................................10
5.15
Surface protection specifications ...................................................................................................10
5.16
Heat treatment specifications.........................................................................................................10
5.17
Specifications for welding, brazing and soldering..........................................................................10
5.18
Screw threads and screw thread inserts........................................................................................10
5.19
Workpiece edges ...........................................................................................................................10
5.20
Centre holes...................................................................................................................................11
5.21
Relief grooves ................................................................................................................................11
5.22
Radii ...............................................................................................................................................11
5.23
Wording used in the title block of a CAD drawing..........................................................................11
5.24
Designations for the represented object on CAD drawings...........................................................11
5.25
Units, letter symbols, mathematical symbols and concepts ..........................................................11
6
Identification of parts on CAD drawings................................................................................................12
6.1
Identification of product parts.........................................................................................................12
6.2
Identification of materials for motor vehicle components...............................................................12
6.3
Identification of mounting, locating, adjusting holes/contours, paint and weld holes ....................12
6.4
Identification of parts subject to obligatory documentation on CAD drawings...............................12
6.5
Signature identifier for electronic car control units on CAD drawings............................................12
Annex A (informative)  Most common used designation prefixes (text module 1) with their definitions of
destination texts............................................................................................................................................13
Annex B (normative)  Text module 4 of the designation text; specimens of supplementary technical
characteristics with regard to text module 2.................................................................................................15
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 3 页
MBN 31 001, 2004-05, Page 3
Copyright DaimlerChrysler 2004
1 
Scope
This standard does not define a separate normative content, but is designed as a reference work contain-
ing general rules for the generation of CAD drawings and for the graphical presentation of parts. The ob-
jective is a standardized generation of CAD drawings.
2 
Normative References
This Section does not contain any data, as this standard is a reference work.
3 
Abbreviations, Acronyms, Definitions, & Symbols
- CAD drawing
Design drawing generated by a computer program which is printed on a plotter or printer or displayed on a
screen.
- CATIA
The CAD system in the version for Mercedes-Benz
- Design frames
are standardized limits to drawing spaces for the generation of design drawings derived from 3D CAD
models.
- Design drawings
are technical drawings depicting an object in its intended final condition.
- SIS
SIS is the abbreviation for the "Standards Information System".
- SRM
SRM is the abbreviation for "Sachstamm-Recherche- und -Management-System“ ("Item Number Re-
search and Management System").
- Additional block
An additional block is the total of all data fields which represent a necessary supplement to a basic title
block.
4 
Design frame
Design frames are standardized limits to drawing spaces for the generation of drawings derived from 3D
CAD models.
4.1 
Design frame types and sizes
Design frames of all standardized DIN sizes and Mercedes-Benz-specific outsizes are available.
In addition, DIN A4 design frames for the PC system "Winword" are available.
The DIN A4 landscape size is not accceptable.
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 4 页
MBN 31 001, 2004-05, Page 4
Copyright DaimlerChrysler 2004
The layout of the design frame shall be in accordance with DIN EN ISO 5457. The following changes
against DIN EN ISO 5457 apply:
- Borders and frame from 5 to 8 mm, lower side size to 12 mm for sizes > DIN A4
- For sizes with grid reference systems (coordinate squares), the field length shall be 100 instead of 50
mm.
A drawing frame includes the drawing space (the drawing block) and the title block.
For a description of the drawing block, refer to MB standard MBN 31 020-2. The title blocks described in
MBN 31 020-1 are arranged in the lower right-hand corner of the drawing frame. The DIN A4 size is an
exception.
All outsizes specific to Mercedes-Benz also include a title block in the lower right-hand corner, but in addi-
tion also the microfilming layout in accordance with DIN 19052 Part 4. If required, the design engineer
shall mark the individual parts to be recorded conforming to the pattern in Figure 1.
The identification shall be marked 7 mm above the drawing space borders and in lettering size 7 mm.
Figure 1: Marking of microfilm identification of recorded parts in outsizes specific to Mercedes-Benz
Length
overlap
Complete item number, in
case of drawings with several
sheets the sheet number is
indicated in addition
Recorded part 2
Recorded part 1
Height
Recorded part 3
Number of re-
corded part
Total number of part
recordings appertain-
ing to a drawing
Title block
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 5 页
MBN 31 001, 2004-05, Page 5
Copyright DaimlerChrysler 2004
The sizes of the design frames in IT systems correspond to the trimmed paper size of the A series DIN EN
ISO 216. The approved sizes are indicated in Table 1.
Table 1   Dimensions (trimmed sizes) of standard DIN sizes and outsizes specific to Mercedes-Benz;
dimensions in mm
DIN size
designation
Mercedes-Benz specific designa-
tion for excess lengths
Trimmed size
Height
Length
A4
-
297
210
A3
-
297
420
A2
-
420
594
A2x  694
694
A2x  794
794
A2x  894
894
A2x1094
1094
A2x1294
1294
A1
-
594
841
A1x1050
1050
A1x1260
1260
A1x1470
1470
A1x1730
1730
A0
-
841
1189
A0x1400
1400
A0x1610
1610
A0x1820
1820
A0x2030
2030
A0x2240
2240
A0x2450
2450
A0x3367
3367
A0x4456
4456
R2x1582
1189
1582
R2x2323
2323
R2x3064
3064
R2x3805
3805
R2x4546
4546
R2x5287
5287
R2x6028
6028
R2x6769
6769
R3x1582
1486
1582
R3x2323
2323
R3x3064
3064
R3x3805
3805
R3x4546
4546
R3x5287
5287
R3x6028
6028
R3x6769
6769
4.2 
Provision of design frames
4.2.1 
Design frames in CAD system "CATIA"
Design frames are available
-
for CATIA version V4 in the Detail Library "CATIA.MB#ZNORM.P#019.MAP" within the
 
"Konstruktionsrahmen" family;
-
for CATIA version V5 in the Catalogue "#Catalog.MBZNORM_19" with the name
 
"KONSTRUKTIONSRAHMEN.catalog".
4.2.2 
Design frames in the PC system Winword
Table 2 includes design frames approved for application. They are available within the "Standards Infor-
mation System“ (SIS) under standard number MBN 31 001.
Design frames for other PC systems are not available.
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 6 页
MBN 31 001, 2004-05, Page 6
Copyright DaimlerChrysler 2004
Table 2
Word design frame for PC application
Document
Title
MBN 31 001 template
Design frame    DIN A4
MBN 31 001 template
Design frame    -   Cover sheet (sheet 0) -    DIN A4
MBN 31 001 template
Design frame    -  Written drawing  -      DIN A4
MBN 31 001 template
Design frame            DIN A3
MBN 31 001 template
Lettering frame general new   DIN A4
MBN 31 001 template
Lettering frame functional instructions   -Title page -  DIN A4
MBN 31 001 template
Lettering frame functional instructions   -Text page -  DIN A4
4.3 
Elements for the design frame
Each design frame includes a basic title block in accordance with MB standard MBN 31 020-1 as a per-
manent constituent.
Where required, an additional title block (see Section 4.3.2) and different additional blocks (see Section
4.3.3 of this standard) shall be used.
The basic title block is located on the design frames of DIN sizes A0 to DIN A3 and outsizes in the lower
right-hand corner of the trimmed size. The DIN A4 size includes the basic title block in the lower half of the
format.
4.3.1 
Title blocks and their provision
In MB standard MBN 31 020-1, all types of title blocks are depicted and described. The title blocks are not
available as separate elements either in CATIA or in the PC system Winword.
4.3.2 
Additional title blocks for design frames and their provision
Additional title blocks are used for tabular drawings and the parts presentation without own drawing.
An additional title block with the totality of its data fields is not available. All required fields are available
individually and shall be compiled by the user when and where required.
The following data fields are available:
Position / basic number / ZGS (drawing geometry technical level) / CAD / Designation / Material / General
tolerance / Surface protection / Statutory characteristic / Remark / Finish / Dimensions / Documentation
requirement / Signature code for electronic car control units; these are available in "CATIA"
-
for CATIA version V4 in the Detail Library "CATIA.MB#ZNORM.P#019.MAP" within the 
"Zusatzfelder" family
-
for CATIA version V5 in "#Catalog.MBZNORM_19" under the name "ZUSATZFELDER.catalog".
MB standard MBN 31 002 applies to the lettering of additional title blocks.
4.3.3 
Additional blocks for design frames and their provision
Additional blocks are used where further data are required for a part/subassembly.
Table 3 indicates a selection of additional blocks.
MB standard MBN 31 002 applies to the lettering of additional blocks.
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 7 页
MBN 31 001, 2004-05, Page 7
Copyright DaimlerChrysler 2004
Table 3 Additional blocks for CAD drawings; Table not comprehensive
Title
Content
Presentational information
Last section / last view....
Change index
Last change index
Fit sizes
Fit size / Deviations
Castings
Undefined wall thicknesses / radii / mould inclines
Invalid drawing version no. 1
Invalid! Superseded by drawing with identical basic number
Invalid drawing version no. 2
Invalid! Becoming no drawing, refer to ...
Sectional drawing
For all other dimensions and indications, refer to ..
Obligatory documentation requirement
Additional block for identifying parts subject to obligatory documentation
Symbol " Obligatory documentation require-
ment"
Symbol for individual characteristics subject to obligatory documentation
Total characteristics subject to obligatory
documentation
Additional block for adding up the number of characteristics
Signature code
Verification of signature code for flashable electronic car control units
Other additional blocks defined by MB standards,
e.g. coating and connecting points in body construction in accordance with MBN 10 187, general tolerances in accordance
with MBN 36 012 etc.
Available in CATIA
-for CATIA version V4 in Detail Library "CATIA.MB#ZNORM.P#019.MAP within the "Zusatzfelder" family;
-for CATIA version V5 in "#Catalog.MBZNORM_19" under the name "ZUSATZFELDER.catalog".
For the PC system “Winword”, the additional blocks listed in Table 4 are available in the "Standards Infor-
mation System“ (SIS) under the standard number MBN 31 001.
Table 4 Additional blocks for Winword PC application
Document
Title
MBN 31 001   template
Sectional drawing
MBN 31 001   template
Title block – supplier drawing
MBN 31 001   template
Change block – supplier drawing
MBN 31 001   template
Fit sizes
MBN 31 001   template
Invalid drawing version no. 1
MBN 31 001   template
Invalid drawing version no. 2
5 
Presentation of parts on CAD drawings
The rules specified in Section 5 shall form the basis for the presentation of parts in CAD drawings.
5.1 
General presentation and layout of the view
The presentation of parts on CAD drawings shall be in accordance with the rules of DIN ISO 128.
The views shall be arranged in accordance with DIN ISO-5456-2, first angle projection.
The symbol of first angle projection is already defined in the drawing frame; see Figure 2.
Figure 2: 
Symbol of first angle projection
For the methodology and designation variants of views, details and sections, MB standard MBN 31 008
shall apply in addition for specific plants; refer to Sections 5.6 and 5.7.
General terms relating to projection methods in Technical Product Documentation are listed in DIN ISO
10209-2.
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 8 页
MBN 31 001, 2004-05, Page 8
Copyright DaimlerChrysler 2004
5.2 
Views in body construction
The views of a body and its parts are always arranged by drawing the left half of the vehicle.
Rear view
Side view
Front view
Top view
Figure 3:
   Arrangement of views in body construction
5.3 
Grid line representation
Vehicles and their superstructures are documented in the coordinate grid (see Figure 4); i.e. the complete
vehicle, the body or the chassis are represented as individual component or assembly drawings using grid
lines in different views.
The grid lines are marked with the characters   X , Y , Z  .
The height and length grid lines are arranged so that the zero lines of the height and of the length intersect
in the centre of the front wheel (origin). The zero line of the width runs through the centre of the vehicle
when viewed from the front and from the top. Grid lines under Z0/left of X0 have a negative sign; the same
also applies to right of Y0 in the view.
The grid line spacing on a natural scale (1:1) is 100 mm.
All dimensions of a represented part build upon the planes X0, Y0 and Z0.
Figure 4:    Coordinate grid for passenger cars
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 9 页
MBN 31 001, 2004-05, Page 9
Copyright DaimlerChrysler 2004
5.4 
Oblique views on drawings
Oblique views may be used to illustrate the component better; the number of views is not limited. The fol-
lowing criteria shall be observed:
- clear delimitation to product-defining views
- parallel projections are permitted; the directions are not limited
- no specifications regarding scale / viewing direction / headline exist.
For the dimensioning of oblique views, refer to Section 5.12.
5.5 
Symmetrical and mirror image parts
In case of larger, symmetrical parts, the left half only shall be represented.
In case of mirror image parts, the left-hand part only shall be drawn. Minor deviations of the right-hand
part shall be represented as if these deviations were present in the left-hand part.
With regard to the item number indication of the right-hand mirror image part, the internal frame guideline
EDR 001 shall apply.
5.6 
Views
The presentation of views is governed by DIN ISO 128-30 and DIN ISO 128-34.
For the methodology and designation variants, MB standard MBN 31 008 shall apply for specific plants.
5.7 
Sections
The presentation of sections is governed by DIN ISO 128-40 and DIN ISO 128-44.
For the methodology and designation variants, MB standard MBN 31 008 shall apply for specific plants.
5.8 
Lines
The general application of lines is governed by DIN ISO 128-24, Lines on mechanical engineering draw-
ings. Line groups 0,35 and 0,5 shall be preferred. In body construction, narrower continuous lines may be
used.
5.9 
Hatching
For hatching specifications, refer to DIN ISO 128-50.
5.10 Lettering and lettering sizes
MB standard MBN 31 002 shall apply for the lettering on CAD drawings.
MB standard MBN 31 002 shall apply both for lettering in German and in a different language.
5.11 Scales for representing parts and drawing scales of CAD drawings during genera-
tion
For recommended scales for representing the parts on CAD drawings and their indications refer to DIN
ISO 5455.
The scale of a CAD drawing may deviate from the scale values for the generation of drawings indicated in
DIN ISO 5455.
The following specifications for the representation shall apply:
-  Scale range  1 to 10           0,5  gradation        e.g.  3,5:1 or  1:3,5
-  Scale range  10 to 100       5,0  gradation        e.g.  15:1 or  1:15
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 10 页
MBN 31 001, 2004-05, Page 10
Copyright DaimlerChrysler 2004
5.12 Entry of dimensions and dimensioning in oblique views on CAD drawings
- Dimensioning
The dimensioning of CAD drawings shall be governed by the specifications contained in the MB standard
MBN DIN 406. For the tolerancing of linear and angular dimensions, MBN DIN 406-12 shall apply. For the
dimensioning and tolerancing of cones, refer to DIN ISO 3040.
- Dimensioning in oblique views on drawings
As the measuring plane in oblique views is not normally identical with the drawing plane, dimensioning is
not normally allowed in oblique views.
The following exceptions have been defined:
1.  Only distance measurements in the main axes X, Y, Z are permissible.
2.  Radii are permissible in the main axes XY, XZ, YZ.
3.  The main axis system must be represented on the drawing.
5.13 Tolerancing
DIN ISO 1101 applies to the indication of geometrical tolerances on drawings. Standard indications re-
garding general tolerances are specified in the title block.
5.14 Surface quality
The roughness of workpiece surfaces is indicated using the roughness value Rz (ten-point height).
For the approved surface characteristics and indications of the surface quality (roughness, groove orienta-
tion etc.), refer to the MBN 31 007-0.
Characteristics not defined externally such as "effective micro-profile load-bearing proportion twi in %" or
"exposure depth Rf" or "twist-reduced dynamic sealing surfaces“ are largely defined in MB standard MBN
31 007. The characteristic "dominant waviness" (VDA 2007) has been newly defined in 2002 and ap-
proved for application.
The latest experiences with rules and methods for assessing the surface quality and indication of the sur-
face quality on drawings are explained and specified in Normmitteilung No. 2002/01. This standard noti-
fication refers to VDA Recommendations VDA 2005 and VDA 2006.
5.15 Surface protection specifications
Surface protection specifications shall be indicated in accordance with MB "DBL" specifications for the
final condition of the surface protection treatment of a part or a subassembly. For entering this data, a
separate field is available in the drawing title block (refer to MB standard MBN 31 020-1, Title blocks for
CAD drawings).
DIN 50960-2 is a generally applicable standard for electroplated and chemical coatings.
5.16 Heat treatment specifications
For the required specifications and representations on CAD drawings for heat treatment (hardening, tem-
pering, quenching and tempering, surface carburization, case hardening, nitriding), refer to DIN 6773.
For mandatory additions to or deviations from DIN 6773, refer to MBN 40 011.
5.17 Specifications for welding, brazing and soldering
Symbols for representing welded, brazed and soldered joints shall be selected in accordance with DIN EN
22553.
DIN EN 22553 corresponds to International Standard ISO 2553.
Welding and allied processes - Nomenclature of processes and reference numbers, refer to DIN EN ISO
4063.
5.18 Screw threads and screw thread inserts
DIN ISO 6410 applies to the specification of threads and screw thread inserts.
5.19 Workpiece edges
Workpiece edges in CAD drawings shall be indicated in accordance with DIN ISO 13715.
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 11 页
MBN 31 001, 2004-05, Page 11
Copyright DaimlerChrysler 2004
5.20 Centre holes
Where centre holes are represented on CAD drawings, DIN ISO 6411 shall apply.
5.21 Relief grooves
Relief grooves shall be indicated in accordance with DIN 509.
5.22 Radii
For preferred values for radii, refer to DIN 250.
5.23 Wording used in the title block of a CAD drawing
Where wording (both terms and sentence structures) is required on CAD drawings, DIN 6790 shall apply.
5.24 Designations for the represented object on CAD drawings
A designation is the name of the represented object of a CAD drawing.
It is intended to facilitate understanding and normally describes the part/subassembly by its function or
design.
The relevant leading design area is responsible for entering the designations on CAD drawings. Generally,
the information source for a designation is the basic item number catalogue (RSK) within the SRM system.
A designation is assigned during the basic number allocation for A/H basic numbers.
A designation, a so-called designation text, includes the following text modules:
1 an optional designation prefix
2 an obligatory main designation
3 an optional designation supplement consisting of one or more character strings
4 an optional text (freely definable).
Text module 1 contains the most commonly used designation prefixes with definitions; refer to Annex A.
Text module 2 contains main designations from automotive engineering. They describe the design and/or
function of the component/subassembly represented.
Text module 3 contains abbreviations of designation supplements, as well as functional supplements
such as cpl (complete), le (left), ri (right), fr (front), re (rear), tp (top), bt (bottom), inlet, outlet, mechanical,
upper part, return line, lower part, forward flow.
Text module 4 is an optional (freely definable) text and may contain supplementary technical features;
locations (such as "underneath rear seat") shall not be entered. For specimens refer to Annex B.
Text modules 1 to 3 are available in the basic item number catalogue (RSK) in German as well as in
translated form. The system does not support the translation of text module 4, an optional text.
In case of any requirements for new designations for technical advancements, Standards, Team Item
Master Data Documentation, will decide following submission of a suggestion and following consultation
with Development. Standards will then archive these new designations in the basic item number cata-
logue.
5.25 Units, letter symbols, mathematical symbols and concepts
Unit names and symbols DIN 1301
Letter symbols DIN 1304
Mathematical symbols and concepts DIN 1302
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 12 页
MBN 31 001, 2004-05, Page 12
Copyright DaimlerChrysler 2004
6 
Identification of parts on CAD drawings
6.1 
Identification of product parts
Product parts shall be marked in accordance with MB standard MBN 33 015.
6.2 
Identification of materials for motor vehicle components
MB standard MBN 33 035 specifies the application of VDA Recommendation VDA 260, Components of
motor vehicles, Identification of materials, for parts of Mercedes-Benz vehicles.
6.3 
Identification of mounting, locating, adjusting holes/contours, paint and weld holes
In the passenger car body division, different hole indications for production are required on CAD drawings.
Besides the requirement for locating and mounting holes, paint holes for dip coating are also required.
Weld holes may be either holes which are present during connection of two parts into one which are sub-
sequently welded shut or holes used for guiding through a welding electrode.
The use of robots for seam sealing or underbody protection requires indication of adjusting locations. Se-
lected holes and/or contours are defined as zero points in the robot program.
The following symbols (abbreviated designations) are available:
F = for locating hole or locating contour
A = for mounting hole
L = for paint hole
S = for weld hole
W = for wax hole
J = for adjusting hole or adjusting contour
The identification on the CAD drawing shall be made using the symbol on a reference line for the relevant
hole.
For holes serving several functions, 2 or 3 symbols may appear.
The symbols used on the drawing shall be explained near the drawing title block.
The hole symbols shall be provided in the CAD system "CATIA"
-
for CATIA version V4 in the Detail Library "CATIA.MB#ZNORM.P#019.MAP" within the
"Kanten- und Lochsymbole" family;
-
for CATIA version V5 within the catalogue "#Catalog.MBZNORM_19" under
"KANTEN-UND LOCHSYMBOLE.catalog, Untergruppe Funktion-Loecher".
6.4 
Identification of parts subject to obligatory documentation on CAD drawings
Note: The scope is restricted to the Mercedes Car Group.
The obligatory documentation with the two verification methods safety relevance and certification rele-
vance shall always be fulfilled in accordance with MBN 10 317.
6.5 
Signature identifier for electronic car control units on CAD drawings
The obligatory signature is specified with three possible verification methods. These shall always be per-
formed in accordance with MBN 10 320.
End of Main Document
# # # # #
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 13 页
MBN 31 001, 2004-05, Page 13
Copyright DaimlerChrysler 2004
Annex A (informative)
Most common used designation prefixes (text module 1) with their
definitions of destination texts
The most commonly used designation prefixes are usually two-character abbreviations which are com-
pany specific and therefore not translated.
Anordnung -AO- (arrangement)
Drawing showing the context beyond the design group or the relative position of parts to each other (e.g.
exhaust system). It is used as control drawing and contains only the configuration dimensions required
(i.e. 
no 
complete 
dimensioning) 
and 
testing 
and 
setting 
instructions, 
if 
applicable.
It is not intended for the representation of individual parts and assemblies. (These are represented and
dimensioned on their own drawings.)
Dichtungssatz ‘DS’ (seal set) (for ET)
Seals which must be replaced after dismantling one or several parts, are comprised in a seal set. In gen-
eral, seal sets only include sealing elements.
Einbau  ‘EB’ (installation)
An installation drawing represents the assembly or installation of individual parts and/or assemblies in a in
higher assembly; this higher assembly is not documented with a new or separate assembly number. It is a
condition for the installation that
� 
no temporary storage
� 
no part inventory
� 
no external procurement
of the assembly/individual part modified by it is required, as in general the same basic number cannot be
used to identify different parts. In contrast to an assembly, the installation outside the final object does not
constitute a separate physical entity.
Lieferumfang ‘LU’ (scope of delivery)
A scope of delivery (LU) is an assembly (ZB) which is not completely structured or which constitutes a
loose compilation of parts.
Nacharbeit ‘NA’ (rework)
Reconditioning a part to bring it up to the latest drawing date if it corresponds to an obsolete drawing date
or reworking of a reject to bring it back to a usable condition.
Reparatursatz ‘RS’ (repair set) (for ET)
Repair sets consist of parts required for the repair of a "ZB" or a subassembly.
Tabelle ‘TB’ (table)
Drawing on which several similar parts, arrangements, installations and assemblies only deviating from
each other with regard to few dimensions may be listed. Tables are mere reference drawings whereby the
designation 
of 
a 
table 
always 
includes 
the 
word 
"Table" 
or 
"TB" 
as 
first 
constituent.
The basic number of the table cannot at the same time be used for one of the parts indicated in it. It must
be approved for all those uses for which the individual basic numbers indicated in the Table are valid. In
general, the total scope of a table (e.g. as drawing with several sheets) applies to all parts listed. Any de-
viations shall be specifically marked (e.g. "Sheet 3 only applies to Part X").
Teilesatz (parts kit)‘TS’ (parts kit) (for ET)
A parts kit consists of the main part plus small parts or of several identical main parts.
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 14 页
MBN 31 001, 2004-05, Page 14
Copyright DaimlerChrysler 2004
Umarbeit ‘UA’ (conversion)
By means of additional work processes, another finished product is produced from a particular finished
product. Conversion is specified by Production only. Indication is possible by Design in the data field "ref-
erence number" on the drawing.
Zusammenbau ‘ZB’ (assembly)
An assembly consists of at least 2 parts (individual parts and/or subassemblies) and constitutes a com-
plete production/assembly scope for which a separate part number is assigned (normally represented in
the parts list by means of structural levels).
Zusatzarbeit ‘ZS’ (additional work)
Additional work represents identical additional work processes on different individual parts or assemblies,
whereby the individual parts/assemblies modified by additional work are not identified with a new basic
number. It is a condition for additional work that
� 
no temporary storage
� 
no part inventory
� 
no external procurement
of the individual parts/assemblies modified by it occurs. This is required as it is generally not possible to
identify different parts with the same basic number. The handling using additional work therefore elimi-
nates the need to specify drawings and basic numbers for new, different individual parts or assemblies.
End of Annex A
# # # # #
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)


### 第 15 页
MBN 31 001, 2004-05, Page 15
Copyright DaimlerChrysler 2004
Annex B (normative)
Text module 4 of the designation text; specimens of supplementary
technical characteristics with regard to text module 2
Specimen 1:
Rear axle ratio as supplementary technical characteristic for the designation - ZB rear axle -
i = 2,65 RG 107/198 ABS
Specimen 2:
Wheel and tire size for the designation - ZB wheel with tire -
8Jx19 H2 275/50 ZR 19   Reinforced
Specimen 3:
Gear ratio / number of teeth for the designation – drive pinion -
i = 3,89 Z = 35
End of Annex B
# # # # #
Uncontrolled copy when printed (: Yiping Huang, 2013-08-22)

