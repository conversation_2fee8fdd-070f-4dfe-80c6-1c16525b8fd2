# MBN_LV_124-1_EN_2013-03_3.5t及以下乘用车电气电子零部件电测试标准.pdf

## 文档信息
- 标题：Harmonized OEM hardware requirements for E/E components
- 作者：<PERSON><PERSON> (P3)
- 页数：57

## 文档内容
### 第 1 页
Mercedes-Benz  
MBN LV 124-1  
Company Standard 
Edition:  2013-03 
 
Supersedes: MBN LV 124-1: 2011-03 
 
Total no. of pages: 1 and 55 pages excerpt from LV 
  
 
Person in charge: Holger Müller 
 
Plant: 059; Dept.: RD/EEB 
Date of translation: 2013-06 
Phone: + 49(0)7031 90 60330 
 
NOTE: This translation is for information purposes only. 
The German version shall prevail above all others. 
 
Copyright Daimler AG 2013 
 
Electric and Electronic Components in Motor  
Vehicles up to 3,5t – General Requirements, 
Test Conditions and Tests 
Part I: Electrical Requirements and Tests  
12 V On-Board Electrical System 
Foreword 
This edition of this Standard is based on the document LV 124 which has been established by rep-
resentatives of the automotive manufacturers AUDI AG, BMW AG, Daimler AG, Porsche AG and 
Volkswagen Aktiengesellschaft within Working Group 4.9 "Process Assurance of Supplier Hardware 
Quality of Electronic Components". 
Any deviations from LV 124 are listed on the cover sheet of this Standard (in justified exceptional 
cases, deviations may be represented in the body of the standard in italics). If in individual cases 
modifications to individual test sections are required, such modifications shall be agreed separately 
between the departments responsible of the automotive manufacturer and the supplier. 
Within the framework of common development projects of the automotive manufacturers, test re-
ports will be recognized provided that the tests have been performed by an independent institute 
accredited in accordance with DIN EN ISO/IEC 17025. Approval does not automatically follow from 
acceptance of the test reports. Other test reports may be recognized at the discretion of the buyer. 
The contents of LV 124, version 2.2, edition 2013-02-28, have been adopted unchanged, but divid-
ed into two parts, into the set of standards of Mercedes-Benz with the exception of the test parame-
ters in test E-05 in accordance with the following Table: 
MBN standard number 
LV number 
Contents 
Pages of 
LV 124 
MBN LV 124-1 
LV 124 
Part I: Electrical Requirements 
and Tests - 12 V On-Board Elec-
trical System 
2-3; 6-54; 160 
MBN LV 124-2 
LV 124 
Part II – Environmental Require-
ments and Tests 
2; 4-5; 55-159 
 
Application note:  
Application of the present version of this Standard is binding for new vehicle projects or components 
of this scope, for which no concept/basic specifications or component requirement specifications 
have been approved yet at the date of issue of this version.  
The respective contract documents regulate the mandatory application of the present version of this 
Standard by the supplier. 
 
General requirements: 
For safety requirements, homologation (in particular, exhaust emissions) and quality, the existing 
statutory requirements and laws shall be complied with. In addition, the relevant requirements of the 
Daimler Group apply. 
All materials, procedures, processes, components, and systems shall conform to the current regula-
tory (governmental) requirements regarding regulated substances and recyclability. 
Changes 
In comparison with edition 2011-03, the following changes have been made: 
- 
Cover sheet updated 
- 
For other changes, refer to Section "Change history" of LV 124 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 2 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 1 of 160 
 
 
LV124 
 
 
 
 
 
 
Electric and Electronic Components in Motor Vehicles up to 
3,5 t - General Requirements, Test Conditions and Tests  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 3 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 2 of 160 
 
Change history 
 
 
 
 
 
 
Edition 
 
2013-02 
Editorial changes integrated. 
 
Part I: Electrical Requirements and Tests - 12 V On-Board 
Electrical System: 
Complete revision - each test adjusted to latest requirements. 
 
Part II – Environmental Requirements and Tests: 
Extension to components which are described in several operating 
modes, components connected to coolant circuits, and revision of life 
tests.  
 
 
 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 4 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 3 of 160 
 
 
Contents 
Part I: Electrical Requirements and Tests - 12 V On-Board Electrical System ........ 6 
1 
Scope ....................................................................................................................... 6 
2 
Normative references ............................................................................................. 6 
3 
Terms and definitions ............................................................................................ 6 
3.1 
Terms and abbreviations ....................................................................................... 6 
3.2 
Voltages and currents ............................................................................................ 7 
3.3 
Temperatures ........................................................................................................ 7 
3.4 
Times/durations ..................................................................................................... 8 
3.5 
Internal resistance, terminal designations, frequency ............................................ 8 
4 
General requirements ............................................................................................ 8 
4.1 
Voltages and currents ............................................................................................ 8 
4.2 
Temperatures ........................................................................................................ 9 
4.3 
Standard tolerances ............................................................................................... 9 
4.4 
Standard values ..................................................................................................... 9 
4.5 
Sampling rates and measured value resolutions ................................................... 9 
4.6 
Test voltages ......................................................................................................... 9 
4.7 
Operating voltage ranges and coding .................................................................. 10 
4.8 
Functional status classification ............................................................................ 10 
4.9 
Operating modes ................................................................................................. 12 
4.10 Interface description ............................................................................................ 13 
4.11 Procedural limitations .......................................................................................... 13 
4.12 Electrical tests ...................................................................................................... 14 
5 
Test selection table .............................................................................................. 16 
6 
Electrical tests and requirements ....................................................................... 18 
6.1 
E-01 Long-term overvoltage ................................................................................ 18 
6.2 
E-02 Transient overvoltage .................................................................................. 19 
6.3 
E-03 Transient undervoltage ................................................................................ 21 
6.4 
E-04 Jump start ................................................................................................... 22 
6.5 
E-05 Load dump .................................................................................................. 23 
6.6 
E-06 Superimposed alternating voltage ............................................................... 24 
6.7 
E-07 Slow decrease and increase of the supply voltage ..................................... 26 
6.8 
E-08 Slow decrease, abrupt increase of the supply voltage ................................ 28 
6.9 
E-09 Reset behavior ............................................................................................ 30 
6.10 E-10 Short interruptions ....................................................................................... 32 
6.11 E-11 Start pulses ................................................................................................. 35 
6.12 E-12 Voltage profile for on-board electrical system control .................................. 39 
6.13 E-13 Pin interruption ............................................................................................ 40 
6.14 E-14 Connector interruption ................................................................................. 42 
6.15 E-15 Reverse polarity .......................................................................................... 43 
6.16 E-16 Ground offset .............................................................................................. 46 
6.17 E-17 Short circuit in signal circuit and load circuits .............................................. 47 
6.18 E-18 Insulation resistance.................................................................................... 49 
6.19 E-19 Closed-circuit current .................................................................................. 50 
6.20 E-20 Dielectric strength ....................................................................................... 51 
6.21 E-21 Backfeeds ................................................................................................... 52 
6.22 E-22 Overcurrents ............................................................................................... 54 
Annex G (informative)  Examples of examination methods for physical analysis
 .................................................................................................................................... 160 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 5 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 4 of 160 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Pages 4 and 5 of LV 124 concern Part II Environmental Tests: see MBN LV 124-2 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 6 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 5 of 160 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 7 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 6 of 160 
 
Part I: Electrical Requirements and Tests - 12 V On-
Board Electrical System 
 
1 Scope 
This document specifies requirements, test conditions and tests for electric, electronic 
and mechatronic components and systems for use in motor vehicles with a 12 V on-
board electrical system. Unless otherwise indicated, the tests do not represent electric 
service life tests. 
 
Any additional or deviating requirements, test conditions and tests shall be defined in 
the respective Component Requirement Specifications. 
 
Note: The represented tests are intended for the examination of part of the required 
properties of the component; they are not intended for component qualification or a 
qualification of the manufacturing process. 
 
2 Normative references 
Table 1: Normative references 
ANSI/UL94 
Tests for Flammability of Plastic Materials for Parts in Devices 
and Appliances 
DIN 72552-2 
Terminal Markings for Motor Vehicles: Codes 
DIN EN 13018 
Non-Destructive Testing – Visual Testing - General Principles 
DIN EN ISO/IEC 
17025 
General Requirements for the Competence of Testing and  
Calibration Laboratories 
 
3 Terms and definitions 
3.1 Terms and abbreviations 
Table 2: Abbreviations for electrical requirements and tests 
Term / Abbreviation 
Meaning 
Modules/devices 
Electric, electronic or mechatronic device 
(e.g. resistor, capacitor, transistor, IC, relay) 
DUT 
Device Under Test – the system or component to be tested 
Functions 
Comprises system-specific functions and diagnostic functions 
ICT 
In Circuit Test 
Component 
Complete unit, control unit or mechatronic system (with 
housing) 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 8 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 7 of 160 
 
Term / Abbreviation 
Meaning 
Short circuit 
A short circuit of a load output is defined by a lower impedance 
load case than with specified load up to the limit case 0 Ω. 
Creeping short circuits i.e. current just below short circuit 
detection are also included. 
A short circuit may be permanently present (component in 
operation/not in operation). 
On-grid parking 
Operating mode of a vehicle with alternative drive which is 
connected to a charging station/socket during parking, but is not 
being charged. The vehicle can normally communicate with the 
charging station. 
Off-grid parking 
Operating mode of a vehicle with alternative drive which is not 
connected to a charging station/socket during parking.  
Power user 
Real application case with maximum conceivable usage.  
 
 
PTB 
Physikalisch-Technische Bundesanstalt (German national 
metrology institute providing scientific and technical services) 
Start-relevant 
Components which are directly or indirectly required for a 
combustion engine starting procedure 
System 
Functionally linked components, e.g. 
brake control system (control unit, hydraulic, sensors) 
3.2 Voltages and currents 
Table 3: Abbreviations for voltages and currents 
UN 
Nominal voltage 
UBmin 
Lower operating voltage limit 
UB 
Operating voltage 
UBmax 
Upper operating voltage limit 
Umax 
Maximum voltage that may occur during a test 
Umin 
Minimum voltage that may occur during a test 
UPP 
Peak-to-peak voltage 
Urms 
rms value of a voltage 
Utest 
Test voltage 
IN 
Nominal current 
GND 
Device ground 
UA, UT, US, UR 
Voltage level of the start voltage pulse 
3.3 Temperatures 
Table 4: Abbreviations for temperatures 
Tmin 
Minimum operating temperature 
TRT 
Room temperature 
Tmax 
Maximum operating temperature 
Ttest 
Test temperature 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 9 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 8 of 160 
 
3.4 Times/durations 
Table 5: Abbreviations for times/durations 
tr 
Rise time (e.g. of a voltage profile) 
tf 
Fall time (e.g. of a voltage profile) 
3.5 Internal resistance, terminal designations, frequency 
Table 6: Abbreviations for resistance, terminals and frequencies 
Ri 
Internal source resistance inclusive of the power supply 
wiring harness (see Figure 1: Internal resistance) 
Terminal designations 
According to DIN 72552-2 
f 
Frequency 
 
4 General requirements 
4.1 Voltages and currents 
The voltage curves indicated shall be interpreted as envelope. Real voltage curves are 
to be expected with random profile within the specified test and reference curves. 
 
All voltage and current indications refer to the component (at its terminal).This does not 
apply to tests for which the internal resistance Ri is specified. In this case, the voltage 
and current indications refer to the source (see Figure 1: Internal resistance). 
 
 
 
Key 
US 
Source 
RL 
Line and contact resistance 
Ri 
Internal resistance at the terminals of the 
component source direction 
 
Figure 1: Internal resistance  
 
All edge descriptions refer to the 10% or 90% voltage values. 
US
Ri
RL
DUT
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 10 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 9 of 160 
 
4.2 Temperatures 
Unless otherwise indicated, the temperature indications refer to the ambient air of the 
DUT. 
4.3 Standard tolerances 
Unless otherwise indicated, the tolerances according to Table 7 apply. 
The tolerances refer to the required measured value. 
Table 7: Standard tolerances 
Frequencies 
± 1 % 
Temperatures 
± 2 °C 
Humidity 
± 5 % 
Times/durations 
+ 5 %; 0 % 
Voltages 
± 2 % 
Currents 
± 2 % 
4.4 Standard values 
Unless otherwise indicated, the standard values according to Table 8 apply. 
Table 8: Standard values 
Room temperature 
TRT = 23 °C ± 5 °C  
Humidity 
Frel = 25 % to 75 % relative humidity  
Test temperature 
Ttest = TRT 
Operating voltage 
(for test) 
UB = 14 V 
4.5 Sampling rates and measured value resolutions 
The sampling rate and bandwidth of the measuring system shall be adapted to the 
respective test. All measured values with all maximum values (peaks) shall be recorded. 
 
The resolution of the measured values shall be adapted to the respective test. Care 
shall be taken to ensure that occurring voltage peaks do not lead to an overflow or 
cannot be measured in the case of insufficient resolution. Any data 
reduction/abstraction (e.g. limit value monitoring, bus message evaluation) shall not 
suppress irregularities. 
4.6 Test voltages 
Test voltages, especially for over- and undervoltage tests, may deviate significantly from 
the voltage ranges in Section 4.7 and will be specified individually. 
Functional status A (see Section 4.8) shall always be fulfilled within the voltage range 
applicable for the component. 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 11 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 10 of 160 
 
 
4.7 Operating voltage ranges and coding 
Table 9: Operating voltage ranges 
Code UBmin UBmax 
Description 
a 
6 V 
16 V For functions that must retain their performance during starting of 
the engine 
b 
8 V 
16 V For functions that do not have to retain their performance during 
starting of the engine 
 
This code shall only be used if the component cannot be 
classified under codes a, c or d. 
c 
9 V 
16 V For functions that must retain their performance 
when the engine is not running 
d 
9,8 V 
16 V For functions that must retain their performance 
when the engine is running 
 
4.8 Functional status classification 
4.8.1 General 
This Section describes the functional status of the DUT during and after the test. The 
functional status of the DUT shall be indicated for each test.  
The functional behavior (including derating, e.g. with regard to temperature and voltage) 
of the component in the functional statuses and the buyer perception (e.g. visual, 
acoustic, haptic, thermal) shall be defined by the buyer on the drawing or in the 
component requirement specifications. 
Memory functions shall always remain in functional status A. The integrity of the non-
volatile memories shall be ensured at any time. The time sequences of the functional 
statuses shall be specified in the component requirement specifications. Permissible 
fault memory entries shall be coordinated and specified in conjunction with the buyer. 
For functional statuses A to D no damage to the DUT is permissible. The permissible 
limit values specified in the data sheets (e.g. electric, thermal, mechanical) of the 
electric/electronic devices installed in the DUT shall not be exceeded. Evidence shall be 
provided at least by the parameter test (small) according to Section 4.12.2. 
4.8.2 Functional status A 
The DUT shall perform all functions during and after the exposure to the test 
parameters. 
4.8.3 Functional status B 
The DUT shall perform all functions during the exposure to the test parameters; 
however, one or more functions may lie outside the specified tolerance. After the 
exposure to the test parameters, the DUT shall automatically revert to functional status 
A. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 12 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 11 of 160 
 
4.8.4 Functional status C 
The DUT does not fulfill one or several functions during exposure to the test 
parameters. After the exposure to the test parameters, the DUT shall automatically 
revert to functional status A. Undefined functions are not permissible at any time. 
4.8.5 Functional status D 
The DUT does not fulfill one or several functions during exposure to the test 
parameters. After the exposure to the test parameters, the DUT shall automatically 
revert to functional status A by means of a change of terminal, a reset or a 
straightforward intervention (e.g. replacement of a defective fuse). Undefined functions 
are not permissible at any time. 
4.8.6 Functional status E 
The DUT does not fulfill one or several functions during exposure to the test parameters 
and must be repaired or replaced after the exposure to the test parameters. 
The DUT shall comply with the flammability requirement specified in UL94-v0. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 13 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 12 of 160 
 
4.9 Operating modes 
4.9.1 General 
The electric, electronic and mechatronic components and systems will be operated in 
different operating modes during their service life, which shall be simulated 
correspondingly during the tests. Details concerning the operating modes, operating 
loads (e.g. actuation, bus messages, original sensors, original actuators or replacement 
circuitry) and the necessary boundary conditions shall be coordinated between the 
buyer and supplier and documented. 
4.9.2 Operating mode I - DUT not electrically connected 
4.9.2.1 Operating mode I.a 
The DUT is without power; connector and harness are not connected. 
Any existing coolant circuit is unfilled, and the connections are sealed. 
4.9.2.2 Operating mode I.b 
The DUT is without power; but the connector and harness are connected. 
Any existing coolant circuit is filled, and the coolant hoses are connected. 
4.9.3 Operating mode II – DUT electrically connected 
4.9.3.1 Operating mode II.a 
The DUT shall be operated without operating load. 
Any existing coolant circuit shall be filled, and the coolant hoses shall be 
connected. If required, the flow rate and temperature of the coolant shall be 
adjusted - as specified in the component requirement specifications. 
4.9.3.2 Operating mode II.b 
The DUT shall be operated with minimal operating load. 
The DUT shall be operated such that minimal self-heating occurs (e.g. by means 
of a reduction of continuous output power or through infrequent activation of 
external loads). 
Any existing coolant circuit shall be filled, and the coolant hoses shall be 
connected. If required, the flow rate and temperature of the coolant shall be 
adjusted - as specified in the component requirement specifications. 
4.9.3.3 Operating mode II.c 
The DUT shall be operated with maximum operating load (power user, but no 
misuse). 
The DUT shall be operated such that maximum self-heating occurs (for example 
by means of a realistic maximization of a continuous output power or frequent 
activation of external loads). 
Any existing coolant circuit shall be filled, and the coolant hoses shall be 
connected. If required, the flow rate and temperature of the coolant shall be 
adjusted - as specified in the component requirement specifications. 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 14 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 13 of 160 
 
4.9.3.4 Examples of operating modes 
Table 10: Examples of operating modes 
Example of 
component 
Operating 
mode II.a 
Operating mode II.b 
Operating mode II.c 
Car radio with 
navigation 
Component as 
in parked 
vehicle 
(sleep). 
Follow-on 
current 
stopped 
Terminal 30 
"ON" 
Component in 
running vehicle. 
Component switched 
off by driver, 
BUS/µC’s active, 
Terminal 15 "ON" 
Component in running 
vehicle. Component 
switched on (CD, 
navigation system, 
output stage), 
BUS/navigation 
computer active 
Anti-theft alarm 
system 
No operation 
when vehicle 
is running 
Vehicle interior is monitored while vehicle is 
parked 
Brake control system Component as 
in parked 
vehicle. 
Follow-on 
current 
stopped 
Driving without brake 
actuation 
Driving with frequent 
brake cycles (no 
misuse, such as 
uninterrupted brake 
control operation) 
On-board charger 
Off-grid 
parking 
or  
driving 
 
On-grid parking 
(power line 
communication only, 
no charging) 
Vehicle conditioning 
Charging 
HV battery (battery 
management 
system) 
Off-grid 
parking 
On-grid parking with 
power line 
communication 
Driving, charging 
 
4.10  Interface description 
The states and electrical properties of all interfaces shall be described completely. This 
description is intended as a basis for the evaluation of the test results and shall 
therefore be available in detail.  
4.11  Procedural limitations 
The test laboratory shall be organized and operated according to DIN EN ISO/IEC 
17025. All test equipment used for measuring shall be calibrated according to DIN EN 
ISO/IEC 17025 (or as specified or recommended by the manufacturer) and be traceable 
to PTB or another equivalent national metrology laboratory. The test equipment, 
workshop equipment, set-ups and test methods used shall not limit/falsify the behavior 
of the DUT (e.g. current input). These shall be documented in the test report together 
with the accuracies and the expiry date of the calibration. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 15 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 14 of 160 
 
 
4.12  Electrical tests 
4.12.1 
Test sequence 
An electrical test shall commence when the DUT has started up completely and is in 
functional status A. 
 
The sequence of the electrical tests can be selected freely. For each test, the 
permissible fault memory entries and the functional statuses of the component shall be 
specified. 
 
All test cases of a test shall be executed unless specified in the test selection table 
according to Section 5. 
 
The electrical tests may be performed during an environmental test (see LV 124 Part II) 
unless this contradicts the test requirements of the electrical test and provided that the 
buyer has agreed to this procedure. If the DUT shows irregularities during combined 
tests, the tests shall be repeated individually. 
 
In the component requirement specifications or in agreement with the buyer, a set of 
sensitive parameters, so-called key parameters, such as closed-circuit current 
consumption, operating currents, output voltages, transition resistances, input 
impedances, signal rates (rise and fall times) and bus specifications shall be defined. 
These parameters shall be checked for their compliance with the specifications before 
the start and after the end of each test. 
 
During each test, the key parameters to be monitored shall be recorded. Resets of the 
components shall be monitored and documented in a suitable form. 
 
Before and after each test the DUTs shall be subjected to a parameter test (small) 
according to Section 4.12.2 in line with requirement specifications.  
 
Before the first and after the last electrical test, the parameter test (large) according to 
Section 4.12.3 shall be performed in line with requirement specifications. 
 
The measuring results and the data of the before/after tests shall only differ within the 
specified permissible tolerances. Changes in the measuring values which are larger 
than the measuring accuracies shall be marked. The measuring results from the 
continuous parameter monitoring shall be examined for trends and drifting to detect 
abnormalities, aging or malfunctions of the component. 
 
The physical analysis according to 4.12.4 shall be performed on at least one DUT after 
completion of all electrical tests. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 16 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 15 of 160 
 
4.12.2 
Parameter test (small)  
The key parameters shall be measured and the functional behavior of the components 
checked at TRT and UB.  For components with fault memory, the fault memory shall be 
read out. The components shall be checked for external damage/changes such as 
cracks, chipping/peeling, discoloration, deformation etc. by visual testing according to 
DIN EN 13018, without opening the DUT. 
Changes in the values of the key parameters, the functional behavior or the fault 
memory entries as well as irregularities found during the visual test shall be evaluated 
against the new condition with regard to the previous test exposures. 
 
All results shall be documented in the test report. 
 
4.12.3 
Parameter test (large) 
The key parameters shall be measured and the functional behavior of the components 
measured at temperatures Tmax, TRT and Tmin at each of the voltages UBmin, UB and 
UBmax. 
For components with fault memory, the content of the fault memory shall be read out. 
The components shall be checked for external damage/changes such as cracks, 
chipping/peeling, discoloration, deformation etc. by visual testing according to DIN EN 
13018. 
 
Changes in the values of the key parameters, the functional behavior or the fault 
memory entries as well as irregularities found during the visual test shall be evaluated 
against the new condition with regard to the previous test exposures. 
 
All results shall be documented in the test report. 
 
4.12.4 
Physical analysis 
For the physical analysis, the DUT shall be opened, and a visual test shall be performed 
according to DIN EN 13018. 
Additional analyses shall be agreed between the buyer and the supplier. 
Examples of examinations are given in Annex G. 
Changes of the component compared to the new condition shall be evaluated. 
If a DUT demonstrates irregularities, the additional analysis shall be agreed with the 
buyer, if appropriate by adding additional DUTs or using additional analytical methods. 
The results shall be documented and evaluated in the test report. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 17 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 16 of 160 
 
5 Test selection table 
 
Table 11: Test selection table 
Test 
Applicable to 
To be specified 
by buyer in 
addition 
E-01 Long-term overvoltage  
Components supplied by the 12 V 
on-board electrical system 
Component 
required for the 
driving mode 
E-02 Transient overvoltage  
Components supplied by the 12 V 
on-board electrical system 
None 
E-03 Transient undervoltage  
Components supplied by the 12 V 
on-board electrical system 
None 
E-04 Jump start  
Components supplied by the 12 V 
on-board electrical system 
Start-relevant/ 
not start-
relevant 
component 
E-05 Load dump  
Components supplied by the 12 V 
on-board electrical system 
Safety-related 
component 
E-06 Superimposed alternating 
voltage  
Components supplied by the 12 V 
on-board electrical system 
Test cases 
based on 
connection in 
on-board 
electrical 
system 
E-07 Slow decrease and increase 
of the supply voltage  
All components 
Relevant 
terminal status 
E-08 Slow decrease, abrupt 
increase of the supply voltage 
All components 
Relevant 
terminal status 
E-09 Reset behavior 
All components 
Relevant 
terminal status, 
test boundary 
conditions 
E-10 Short interruptions  
All components 
None 
E-11 Start pulses  
Components supplied by the 12 V 
on-board electrical system 
Start-relevant/ 
not start-
relevant 
component 
E-12 Voltage profile for on-board 
electrical system control 
Components supplied by the 12 V 
on-board electrical system 
None 
E-13 Pin interruption 
All components 
Relevant 
terminal status 
E-14 Connector interruption  
All components 
None 
E-15 Reverse polarity  
Components which may be 
subjected to reverse polarity in the 
vehicle 
Severity, 
shutdown of 
component in 
the case of 
reverse polarity 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 18 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 17 of 160 
 
Test 
Applicable to 
To be specified 
by buyer in 
addition 
E-16 Ground offset 
All components 
None 
E-17 Short circuit in signal circuit 
and load circuits  
All components 
None 
E-18 Insulation resistance  
Components with galvanically 
isolated parts 
None 
E-19 Closed-circuit current  
Components which are 
continuously supplied with voltage 
(e.g. Terminal 30, Terminal 30f, 
Terminal 30g,…) 
None 
E-20 Dielectric strength  
Components with inductive parts 
(e.g. motors, relays, coils) 
None 
E-21 Backfeeds  
Components which are electrically 
connected to Terminal 15 or other 
terminals with wake-up function  
None 
E-22 Overcurrents 
Components which have an 
output 
None 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 19 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 18 of 160 
 
6 Electrical tests and requirements 
6.1 E-01 Long-term overvoltage  
6.1.1 Purpose 
The resistance of the component to long-term overvoltages is tested. A generator 
control fault during driving operation is simulated. 
6.1.2 Test 
Table 12: Test parameters E-01 Long-term overvoltage  
Operating mode of the DUT 
Operating mode II.c 
Umax 
17 V (+4 %, 0 %) 
Umin 
13,5 V 
tr 
< 10 ms 
tf 
< 10 ms 
t1 
60 min 
Ttest 
Tmax – 20K 
Number of cycles 
1 
Number of DUTs 
at least 6 
 
 
Figure 2: Test pulse E-01 Long-term overvoltage   
6.1.3 Requirement 
The assessment of the test result depends on the application of the component. A 
distinction is made between: 
a) Functions required for the driving mode: 
functional status B 
If required, an emergency mode shall be defined. The corresponding "derating 
strategy" shall be described in the component requirement specifications. 
 
b) All other components:  
 
 
 
 
 
functional status C 
t1
tf
tr
Umin
Umax
U
t
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 20 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 19 of 160 
 
6.2 E-02 Transient overvoltage 
6.2.1 Purpose 
Transient overvoltages in the on-board electrical system may occur due to the switching 
off of loads and due to short accelerator tip-ins. These overvoltages are simulated by 
means of this test. 
 
6.2.2 Test 
Table 13: Test parameters E-02 Transient overvoltage 
Operating mode of the DUT Operating mode II.c  
Umin 
16 V 
U1 
17 V 
Umax 
18 V (+4 %, 0 %) 
tr 
1 ms 
tf 
1 ms 
t1 
400 ms 
t2 
600 ms 
Number of DUTs 
at least 6 
Test case 1 
Ttest 
Tmax 
Number of cycles 
3 
t3  
2 s 
Test case 2 
Ttest 
Tmin 
Number of cycles 
3 
t3 
2 s 
Test case 3 
Ttest 
TRT 
Number of cycles 
100 
t3 
8 s 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 21 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 20 of 160 
 
 
 
Figure 3: Test pulse E-02 Transient overvoltage 
6.2.3 Requirement 
Functional status A 
 
t2
tf
tf
Umin
Umax
U
t
t1
tr
t3
U1
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 22 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 21 of 160 
 
6.3 E-03 Transient undervoltage  
6.3.1 Purpose 
Transient undervoltages in the on-board electrical system may occur due to switching 
on of loads. This test is intended to simulate such undervoltages.  
6.3.2 Test 
Table 14: Test parameters E-03 Transient undervoltage  
Operating mode of the 
DUT 
Operating mode II.c 
Umax 
10,8 V (+4 %, 0 %) 
Umin 
9 V (0 %, -4 %) 
tr 
1,8 ms 
tf 
1,8 ms 
t1 
500 ms 
t2 
1 s 
Number of DUTs 
at least 6 
Test case 1 
Ttest 
Tmax 
Number of cycles 
3 
Test case 2 
Ttest 
Tmin 
Number of cycles 
3 
 
Figure 4: Test pulse E-03 Transient undervoltage   
 
6.3.3 Requirement 
Functional status A  
t1
tf
Umin
Umax
U
t
tr
t2
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 23 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 22 of 160 
 
6.4 E-04 Jump start 
6.4.1 Purpose 
External starting of the vehicle is simulated. The maximum test voltage originates from 
commercial vehicle systems and their increased on-board electrical system voltages. 
6.4.2 Test 
Table 15: Test parameters E-04 Jump start 
Operating mode of the DUT 
Operating mode II.c  
Umin 
10,8 V 
Umax 
26 V (+4 %, 0 %) 
t1 
60 s 
tr 
< 10 ms 
tf 
< 10 ms 
Number of cycles 
1 
Number of DUTs 
at least 6 
 
 
Figure 5: Test pulse E-04 Jump start  
6.4.3 Requirement 
A distinction is made between: 
 
a) For components relevant for starting (e.g. starter):  
functional status B  
The sensors shall deliver valid values over the whole time (or be safeguarded by 
means of replacement tables in the components). 
 
b) All other components: 
functional status C 
t1
tf
tr
Umin
Umax
U
t
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 24 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 23 of 160 
 
6.5 E-05 Load dump 
6.5.1 Purpose 
Dumping of an electric load, in combination with a battery with reduced buffering ability, 
results in an energy-rich overvoltage pulse due to the generator characteristics. This 
pulse is simulated by means of this test. 
6.5.2 Test 
Table 16: Test parameters E-05 Load dump 
Operating mode of the DUT 
Operating mode II.c  
Umin 
13,5 V 
Umax 
27 V (+4 %, 0 %) 32 V 
tr 
≤ 2 ms 
t1 
300 ms 
tf  
≤ 30 ms 
Break between cycles 
1 min. 
Number of cycles 
10 
Number of DUTs 
at least 6 
 
 
Figure 6: Test pulse E-05 Load dump 
6.5.3 Requirement 
A distinction is made between: 
a) Safety-related components:  
functional status B 
 
b) All other components: 
functional status C 
 
Umin
Umax
U
t
tr
t1
tf
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 25 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 24 of 160 
 
6.6 E-06 Superimposed alternating voltage  
6.6.1 Purpose 
Voltages may be superimposed on the on-board electrical system. The superimposed 
alternating voltage may be applied during the entire running time of the engine. These 
tests are intended to simulate such situations. 
 
6.6.2 Test 
 
Table 17: Test parameters E-06 Superimposed alternating voltage   
Operating mode of the DUT 
Operating mode II.c  
Umax 
UBmax 
Ri 
≤100 mΩ 
Frequency range 
15 Hz – 30 kHz 
Frequency sweep duration t1 
2 min 
Type of frequency sweep 
triangular, logarithmic 
Number of cycles 
15 
Number of DUTs 
at least 6 
Test case 1 
UPP 
2 V (+4 %, 0 %) 
Test case 2 
UPP 
3 V (+4 %, 0 %)  
only for components between battery and generator, 
in particular in the case of battery connection remote 
from generator 
Test case 3 
UPP 
6 V (+4 %, 0 %)  
for all components when driving without battery 
(emergency operation) or in the case of connection 
close to generator 
 
Figure 7: Test pulse E-06 Superimposed alternating voltage  
Umax
U
t
UPP
t1
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 26 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 25 of 160 
 
 
6.6.2.1 Test set-up 
The conditions for the on-board electrical system shall be agreed with the departments 
responsible. The test set-up shall be documented in detail, including line inductances, 
line capacities and line resistances. 
 
 
6.6.3 Requirement 
 
Test case 1: functional status A 
Test case 2: functional status A 
Test case 3: 
a) Components required for the driving mode: 
functional status A 
 
b) All other components:  
functional status B 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 27 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 26 of 160 
 
6.7 E-07 Slow decrease and increase of the supply voltage  
6.7.1 Purpose 
The slow decrease and increase of the supply voltage is simulated as it occurs during 
the slow discharging and recharging processes of the vehicle battery. 
6.7.2 Test 
Table 18: Test parameters E-07 Slow decrease and increase of the supply voltage  
Operating mode of the DUT  
Operating modes II.a and II.c  
 
To be performed for all relevant statuses of the 
voltage supply terminals (e.g. Terminal 15, 
Terminal 30, Terminal 87, ...) and their 
combinations. 
Start voltage 
UBmax  (+4 %, 0 %) 
Rate of voltage variation 
0,5 V/min (+10 %, -10 %) 
U1 
UBmin 
t1 
Holding time at U1 until fault memory has been 
completely read out 
Minimum voltage  
0 V  
U2 
UBmin 
t2 
Holding time at U2 until fault memory has been 
completely read out 
Final voltage 
UBmax (+4 %, 0 %) 
Number of cycles 
Depending on relevant terminal status and their 
combinations: 
1 cycle in operating mode II.a 
1 cycle in operating mode II.c 
Number of DUTs 
at least 6 
 
 
Figure 8: Test pulse E-07 Slow decrease and increase of the supply voltage  
t1
t2
UBmin
UBmax
U
t
0 V
U1
U2
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 28 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 27 of 160 
 
6.7.3 Requirement 
The assessment of the test result depends on the voltage range applied to the 
component during the test. 
 
A distinction is made between: 
a) within the defined operating voltage of the component: 
functional status A 
 
 
b) outside the defined operating voltage of the component: 
functional status C 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 29 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 28 of 160 
 
6.8 E-08 Slow decrease, abrupt increase of the supply voltage 
6.8.1 Purpose 
This test simulates the slow decrease of the battery voltage to 0 V, followed by the 
abrupt reconnection of the battery voltage, e.g. by means of applying a jump start 
source. 
6.8.2 Test 
Table 19: Test parameters E-08 Slow decrease, abrupt increase of the supply voltage 
Operating mode of the DUT 
Operating modes II.a and II.c  
 
To be performed for all relevant statuses of the voltage 
supply terminals (e.g. Terminal 15, Terminal 30, Terminal 
87, ...) and their combinations. 
Start voltage 
UBmax (+4 %, 0 %) 
Voltage fall 
0,5 V/min (+10 %, -10 %) 
U1 
UBmin 
t1 
Holding time at U1 until fault memory has been 
completely read out 
Holding time at UBmin 
Until the fault memory is completely read out 
Minimum voltage  
0 V  
t2 
At least 1 min; however, until internal capacities are 
completely discharged. 
Final voltage 
UBmax (+4%, 0 %) 
tr 
≤ 0,5 s  
Number of cycles 
Depending on relevant terminal status and their 
combinations: 
1 cycle in operating mode II.a 
1 cycle in operating mode II.c 
Number of DUTs 
at least 6 
 
 
Figure 9: Test pulse E-08 Slow decrease, abrupt increase of the supply voltage 
UBmin
UBmax
U
t
0 V
tr
t2
t1
U1
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 30 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 29 of 160 
 
6.8.3 Requirement 
The assessment of the test result depends on the voltage range applied to the 
component during the test. 
 
A distinction is made between the areas: 
a) within the defined operating voltage of the component: 
functional status A 
 
b) outside the defined operating voltage of the component: 
functional status C 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 31 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 30 of 160 
 
6.9 E-09 Reset behavior 
6.9.1 Purpose 
The reset behavior of a component in its environment is simulated and tested. Test 
boundary conditions (e.g. assembly, terminal, system) shall be described in detail. 
During operation, an arbitrary sequence of repeated switching-on/off processes occurs; 
this shall not lead to an undefined behavior of the component. 
The reset behavior is reflected by a voltage variance and a time variance. Two different 
test sequences are required to simulate different switch-off times. A component shall 
always be subjected to both sequences. 
6.9.2 Test 
Table 20: Test parameters E-09 Reset behavior 
Operating mode of the DUT 
Operating modes II.a and II.c  
 
To be performed for all relevant statuses of the voltage 
supply terminals (e.g. Terminal 15, Terminal 30, 
Terminal 87, ...) and their combinations. 
Umax 
UBmin (0%, -4 %) 
Uth 
6 V 
∆U1 (range Umax to Uth) 
0,5 V 
∆U2 (range Uth to 0 V) 
0,2 V 
t2 
At least ≥10 s and until the DUT has returned to 100% 
serviceability (all systems rebooted without error). 
tr 
≤ 10 ms 
tf 
≤ 10 ms 
Number of cycles 
For each test sequence per relevant terminal status and 
their combinations: 
1 cycle in operating mode II.a 
1 cycle in operating mode II.c 
Number of DUTs 
at least 6 
Test case 1 
t1 
5 s 
Test case 2 
t1 
100 ms 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 32 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 31 of 160 
 
 
Figure 10: Test pulse E-09 Reset behavior 
 
6.9.3 Requirement 
Functional status A when Umax is reached again. 
 
Undefined operating statuses shall not occur under any circumstances. 
 
Evidence of compliance with the specified threshold, at which the component leaves the 
functional status A for the first time, shall be provided and documented. 
Umax
Uth
0V
U
t
tf
∆U2
∆U1
t1
t2
tr
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 33 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 32 of 160 
 
6.10 E-10 Short interruptions 
6.10.1 
Purpose 
The behavior of the component in the case of short interruptions of different durations is 
simulated.  
Test case 1 models the supply voltage interruption at the component. 
Test case 2 models the supply voltage interruption in the on-board vehicle electrical 
system. 
Such interruptions may be caused by events such as contact and line defects or 
bouncing relays. 
6.10.2 
Test 
Table 21: Test parameters E-10 Short interruptions 
Operating mode of the DUT 
Operating mode II.c  
Utest 
11 V 
Z1 
S1 closed 
Z2 
S1 open 
tr 
≤ (0,1 * t1) 
tf  
≤ (0,1 * t1) 
The switch S1 shall be switched with 
the following sequences: 
t1 
Steps 
10 µs to 100 µs 
10 µs 
100 µs to 1 ms 
100 µs 
1 ms to 10 ms 
1 ms 
10 ms to 100 ms 
10 ms 
100 ms to 2 s 
100 ms 
t2 
> 10 s 
The test voltage Utest shall be held at least until 
the DUT and the periphery have regained 
100% serviceability. 
Number of cycles 
1 
Number of DUTs 
at least 6 
Test case 1 
S1 switched, S2 statically open 
Test case 2 
S1 switched, S2 negates S1 
 
The duration of the voltage dip increases at the intervals specified in Table 21. This 
results in a diagram as shown in Figure 11. 
 
The voltage at the DUT can be limited by the test set-up to the maximum voltage of test 
E-05 Load dump (see Section 6.5). 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 34 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 33 of 160 
 
 
Figure 11: Change in status switch S1 E-10 Short interruptions 
 
******** Test set-up 
 
Figure 12: Schematic circuit diagram E-10 Short interruptions 
 
The closed switch S2 including the required lines shall be realized with a series 
resistance of <100 mΩ. 
 
******** Test sequence 
One reference measurement each with 100 Ω (±5 %) and 1 Ω (±5 %) as DUT substitute 
shall be performed and documented. This test is intended to provide evidence of the 
edge steepness. Low-inductance modules shall be used as resistors. 
Then the tests according to Table 21 shall be performed. 
 
Z2
Z1
S1
t
tf
t1
t1
t2
tr
Utest
S1
S2
DUT
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 35 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 34 of 160 
 
6.10.3 
Requirement 
 
For t1 < 100 µs: functional status A 
For t1 ≥ 100 µs: functional status C 
 
The time value t1 at which the DUT leaves functional status A for the first time shall be 
recorded . 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 36 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 35 of 160 
 
6.11  E-11 Start pulses 
6.11.1 
Purpose 
When starting the engine, the battery voltage drops to a low value for a short period and 
then slightly rises again. Most components are briefly activated directly before starting, 
then deactivated during starting and activated again after starting when the engine is 
running. This test is intended to examine the behavior of the component under start-
related voltage dips. 
The starting process may be performed under different vehicle starting conditions, cold 
start and warm start (automatic restart for start/stop). In order to cover both cases, two 
different test cases are required. A component shall always be subjected to both 
sequences. 
6.11.2 
Test 
Table 22: Test parameters E-11 Start pulses 
Operating mode of the DUT 
Operating modes II.a, II.b and II.c  
If appropriate, additional operating loads shall be 
specified for the relevant operating mode. 
Test pulse  
- Cold start: test pulse "normal" and "severe" 
according to Table 23 
- Warm start: test pulse "short" and "long" according 
to Table 24  
Number of DUTs 
at least 6 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 37 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 36 of 160 
 
******** Test case 1 – cold start 
Table 23: Test parameters E-11 Start pulses 
Parameters 
Test pulse "normal" 
Test pulse "severe" 
UB 
11,0 V  
11,0 V 
UT 
4,5 V (0 %, -4 %) 
3,2 V +0,2V 
US 
4,5 V (0 %, -4 %) 
5,0 V (0 %, -4 %) 
UA 
6,5 V (0 %, -4 %) 
6,0 V (0 %, -4 %) 
UR 
2 V 
2 V 
tf 
≤ 1 ms 
≤ 1 ms 
t4 
0 ms 
19 ms 
t5 
0 ms 
≤ 1 ms 
t6 
19 ms 
329 ms 
t7 
50 ms 
50 ms 
t8 
10 s 
10 s 
tr 
100 ms 
100 ms 
f 
2 Hz 
2 Hz 
Break between cycles 
2 s 
2 s 
Test cycles 
10 
10 
 
Key 
a 
Terminal 50 off 
b 
Terminal 50 on 
c 
Terminal 50 off 
ttest 
Cycle 
Figure 13: Test pulse cold start 
 
 
 
 
 
1/f 
U 
UB 
US 
UT 
tf 
t5 
t4 
t6 
t8 
t 
UA 
t7 
tr 
b 
c 
ttest 
UR 
a 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 38 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 37 of 160 
 
6.11.2.2 Test case 2 – warm start 
Table 24: Test parameters E-11 Start pulses warm start 
Parameters 
Test sequence 
"short“ 
Test sequence 
"long" 
UB 
 
11,0 V 
UT 
 
7,0 V (0 %, -4 %) 
US 
 
8,0 V (0 %, -4 %) 
UA 
 
9,0 V (0 %, -4 %) 
t50 
 
≥ 10 ms 
tf 
 
≤ 1 ms 
t4 
 
15 ms 
t5 
 
70 ms 
t6 
 
240 ms 
t7 
 
70 ms 
t8 
 
600 ms 
tr 
 
≤ 1 ms 
Break between cycles 
5 s 
20 s 
Test cycles 
10 
100 
 
 
Key 
a 
Terminal 50 off 
b 
Terminal 50 on 
c 
Terminal 50 off 
ttest 
Cycle 
Figure 14: Test pulse warm start 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
U 
UB 
US 
UT 
tf 
t5 
t4 
t6 
t8 
t 
UA 
t7 
tr 
b 
 
c 
 
ttest 
t50 
a 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 39 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 38 of 160 
 
6.11.3 
Requirement 
No fault memory entry shall occur. 
The vehicle shall always be capable of being started. 
6.11.3.1 Components relevant for starting: 
 
Test case 1 – cold start 
Test pulse "normal": functional status A 
Test pulse "severe": functional status B 
 
Test case 2 – warm start: 
Test sequence "long": functional status A 
Test sequence "short": functional status A 
 
6.11.3.2 Components not relevant for starting: 
Test case 1 – cold start 
Test pulse "normal": functional status C 
Test pulse "severe": functional status C 
 
Test case 2 – warm start: 
Test sequence "long": functional status A 
Test sequence "short": functional status A 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 40 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 39 of 160 
 
6.12  E-12 Voltage profile for on-board electrical system control 
6.12.1 
Purpose 
The behavior of the on-board electrical system is simulated, for example when 
intelligent generator or DC-DC converter controls are used. The control allows voltage 
profiles to be set in the range between constant voltage and permanent voltage 
fluctuations in line with the test cases according to Table 25. 
This is relevant for all load cases which the component can assume for "engine running" 
/ "vehicle ready for operation". 
6.12.2 
Test 
Table 25: Test parameters E-12 Voltage profile for on-board electrical system control 
Operating mode of the DUT 
Operating mode II.c 
Umin 
(11,8 V - ∆U) (0 %, -4 %) 
Umax 
(15 V - ∆U) (+4 %, 0 %) 
t1 
2 s 
tr 
≥300 ms 
tf 
≥300 ms 
Number of cycles 
10 
Number of DUTs 
at least 6 
Test case 1 
∆U 
0 V 
Test case 2 
∆U 
0,7 V 
Test case 3 
∆U 
2 V 
 
Figure 15: Test pulse E-12 Voltage profile for on-board electrical system control 
6.12.3 
Requirement 
Functional status A 
 
Umin
Umax
U
t
tf
tr
t1
t1
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 41 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 40 of 160 
 
6.13  E-13 Pin interruption 
6.13.1 
Purpose 
The line interruption of individual pins is simulated. Testing shall be carried out in two 
different operating statuses. Different pulse forms shall be used since the possible 
interruptions may differ greatly regarding their duration (from loose contacts to 
permanent interruption). 
 
6.13.2 
Test 
Table 26: Test parameters E-13 Pin interruption 
Operating mode of the DUT Operating modes II.a and II.c  
 
To be performed for all relevant statuses of the voltage 
supply terminals (e.g. Terminal 15, Terminal 30, Terminal 
87, ...) and their combinations. 
Z1 
Status 1: pin connected 
Z2 
Status 2: pin interrupted 
tr 
≤ (0,1 * t1) 
tf 
≤ (0,1 * t1) 
Number of cycles 
The following applies to the two test cases and the 
relevant terminal status: 
3 cycles in operating mode II.a 
3 cycles in operating mode II.c 
 
Each test shall be assessed separately. 
Number of DUTs 
at least 6 
Test case 1 
 
Each pin shall be removed for t = 10s and then replaced 
(slow interval) 
Test case 2 
 
Pulse package on each pin in order to simulate a loose 
contact (Figure 16) 
Number of pulses t2 in 
pulse package 
4 000 
a 
Pulse package 
t1 
0,1 ms 
t2 
1 ms 
t3 
10 s 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 42 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 41 of 160 
 
 
Figure 16: Test pulse E-13 Pin interruption, Test case 2 
 
 
6.13.2.1 Test sequence 
 
The component is connected to the voltage supply. 
The test shall not be applied to the supply pins (e.g. Terminal 15, Terminal 30, Terminal 
87, ...). The test shall also be applied to the ground pins (Terminal 31). 
 
The voltage at the pin can be limited to the maximum voltage of test E-05 Load dump 
(see Section 6.5). 
 
One reference measurement each with 1 kΩ (±5 %) and 1 Ω (±5 %) as DUT substitute 
shall be performed and documented. This test is intended to provide evidence of the 
edge steepness. Low-inductance modules shall be used as resistors. 
 
Then the tests according to Table 26 shall be performed. 
 
6.13.3 
Requirement 
 
All other test cases functional status C. 
 
 
tr
t3
t2
t1
tf
Z2
Z1
Pin
t
a
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 43 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 42 of 160 
 
6.14  E-14 Connector interruption  
6.14.1 
Purpose 
The line interruption of connectors is simulated.  
6.14.2 
Test 
Table 27: Test parameters E-14 Connector interruption  
Operating mode of the DUT 
Operating modes II.a and II.c  
Number of cycles 
Each connector shall be removed once in both 
operating modes. 
Number of DUTs 
at least 6 
6.14.2.1 Test sequence 
Each connector shall be removed from the DUT for 10 s and then replaced. If the DUT 
has several connectors, each connector shall be tested individually. The sequence shall 
be varied. In the case of several connectors, their various combinations shall also be 
examined. 
6.14.3 
Requirement 
Functional status C 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 44 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 43 of 160 
 
6.15 E-15 Reverse polarity  
6.15.1 
Purpose 
This test is intended to examine the resistance of the DUT against reverse-polarity 
battery connection during jump starting. Reverse polarity may occur several times and 
shall not lead to component damage. The reverse polarity resistance shall be assured 
for any voltages up to the minimum test voltage. The vehicle fuse is not part of the 
reverse polarity protection concept. 
 
6.15.2 
Test 
All relevant connections of the original circuitry must be tested. 
The DUT shall be operated as connected in the vehicle circuit. 
The test shall be performed at different voltages between 0 V and the maximum values 
specified in Table 29. 
 
The current input during the test shall be recorded. 
Table 28: Test parameters E-15 Reverse polarity  
Operating mode of the DUT 
Operating mode II.a  
Test case 1 
Polarity static according to Table 29 
Test case 2 
Polarity dynamic according to Table 30 
Number of DUTs 
at least 6 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 45 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 44 of 160 
 
6.15.2.1 Test case 1 - polarity static 
This test case is intended to examine the robustness of the component at different 
polarity reversal voltages which might occur depending on the vehicle status. 
 
Table 29: Test parameters E-15 Reverse polarity static 
Umax 
0 V  
Umin 
-14,0 V  
∆U1 
-1 V 
Severity 1 
Ri < 100 mΩ 
Severity 2 
Ri < 30 mΩ  
t1 
60 s  
 
For a component in which the operating voltage is switched off 
by means of a relay in the case of polarity reversal, the following 
applies deviating from the value mentioned above: 
8 ms 
t2 
≥ 60 s, but at least until the component has reached the same 
thermal condition as at the beginning of the test 
tr 
≤ 10 ms 
tf 
≤ 10 ms 
Number of cycles 
1 
 
Figure 17: Test pulse E-15 Reverse polarity  - polarity reversal static 
 
 
 
 
 
Umin
Umax
U
t
∆U1
t1
t2
tf
tr
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 46 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 45 of 160 
 
6.15.2.2 Test case 2 - polarity dynamic 
This test case is intended to examine polarity reversal of the component during ongoing 
operation when the vehicle is no longer able to start. 
 
Table 30: Test parameters E-15 Reverse polarity  dynamic 
Umax 
10,8 V  
Umin 
- 4,0 V 
Severity 1 
Ri < 100 mΩ 
Severity 2 
Ri < 30 mΩ  
t1 
60 s 
 
For a component in which the operating voltage is switched off 
by means of a relay in the case of polarity reversal, the following 
applies deviating from the value mentioned above: 
8 ms 
t2 
≤ 5 min 
tr 
≤ 10 ms 
tf 
≤ 10 ms 
Number of cycles 
3 
 
Figure 18: Test case E-15 Reverse polarity  - polarity reversal dynamic 
 
 
6.15.3 
Requirement 
When reverse polarity is applied, no safety-relevant functions shall be triggered, e.g. for 
electric windows, electric sunroof, starter, etc. 
 
Functional status C 
 
Umin
Umax
U
t
0 V
t1
t2
tf
tr
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 47 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 46 of 160 
 
6.16 E-16 Ground offset 
6.16.1 
Purpose 
Potential differences between different ground connection locations may result in signal 
distortions between components and these connection locations. Care shall be taken to 
ensure that the functionality of the component is not influenced in the case of potential 
differences between ground points up to a height of static ± 1 V in the electrical network.  
 
6.16.2 
Test 
If the DUT has several voltage and ground connections, the test shall be performed 
separately for each connection point. 
 
The component shall be connected as shown in Figure 19. 
Table 31: Test parameters E-16 Ground offset 
Operating mode of the DUT 
Operating mode II.c  
U 
1 V  
Number of cycles 
both switching positions 
Number of DUTs 
at least 6 
 
 
Key 
B 
Bus system 
S 
Signaling line 
S1 
Two-pole (a/b) switch 
TE 
Additional component such as test reference, test bench, simulation control unit, 
actuator, sensor or load 
Figure 19: Schematic circuit diagram E-16 Ground offset 
6.16.3 
Requirement 
Functional status A 
 
TE
DUT
B
S
UB
Kl.31
S1b
S1a
1
2
1
2
U
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 48 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 47 of 160 
 
6.17  E-17 Short circuit in signal circuit and load circuits  
6.17.1 
Purpose 
This test simulates short circuits on all device inputs and outputs and in the load circuit. 
All inputs and outputs shall be designed short-circuit-proof against + UB and GND (for 
activated and non-activated outputs with and without voltage supply and with and 
without ground connection). 
The component shall be permanently short-circuit-proof. 
6.17.2 
Test 
Table 32: Test parameters E-17 Short circuit in signal circuit and load circuits  
Operating mode of the 
DUT 
Operating mode II.c  
Test duration 
Short circuit of each pin individually for 60 s to ground 
and to UB 
Test voltages 
UBmin and UBmax 
Test case 1 
Each pin alternating to UB and GND with voltage supply 
and with ground connection 
Test case 2 
Each pin alternating to UB and GND without voltage 
supply and with ground connection 
Test case 3 
Each pin alternating to UB and GND with voltage supply 
and without ground connection 
Number of DUTs 
at least 6 
 
If several pins are involved in the voltage supply/ground supply, the combination 
possibilities shall also be taken into account. 
6.17.2.1 Test set-up 
The power supply unit used for the test shall be capable of supplying the short-circuit 
currents expected by the component. If this is not possible, buffering of the power 
supply unit by means of a car battery is permissible (UBmax is the maximum charging 
voltage in this case). 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 49 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 48 of 160 
 
 
Key 
L 
Load 
E 
Input 
A 
Output 
PWR Output UB 
GND Input / output Terminal 31 
Figure 20: Schematic circuit diagram E-17 Short circuit in signal circuit and load circuits  
 
6.17.2.2 Test sequence 
For inputs, outputs: recording and evaluation of time profile of short-circuit current. 
The functional effects of the short circuits shall be documented. 
 
6.17.3 
Requirement 
For inputs and outputs (E and A): functional status C 
For looped-through supply voltages (PWR): functional status D 
For device mass (GND): functional status E 
 
 
E
PWR
A
UB
Kl.31
L
GND
UB
PWR
GND
E
E
E
Kl.31
L
L
A
A
A
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 50 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 49 of 160 
 
6.18  E-18 Insulation resistance  
6.18.1 
Purpose 
This test is intended to determine the insulation resistance between modules with 
galvanic isolation. Only the galvanically isolated pins shall be considered which are 
connected in the vehicle and which require insulation properties for their function.   
 
6.18.2 
Test 
Table 33: Test parameters E-18 Insulation resistance  
Operating mode of the DUT 
Operating mode I.a  
Test voltage 
500 V DC 
Test duration 
60 s 
Test points 
Application of the test voltage 
- 
to terminals without galvanic connection 
- 
between connection pins and conducting 
housings without galvanic connection 
- 
between connection pins and an electrode 
around the housing if the housing is non-
conducting 
- 
additional test points agreed with the relevant 
department responsible 
Number of cycles 
1 cycle shall be passed with each of the above points 
being tested at least once. 
Number of DUTs 
at least 6 
 
6.18.2.1 Test sequence 
As preparation for the test, the DUTs shall pass through the test "Damp heat, cyclic" 
which shall be agreed with the buyer. Before the measurement, the DUTs shall be 
allowed to dry for 30 minutes. 
 
6.18.3 
Requirement 
The insulation resistance shall be at least 10 MΩ.  
After the test, evidence of functional status A shall be provided. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 51 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 50 of 160 
 
6.19  E-19 Closed-circuit current  
6.19.1 
Purpose 
This test is intended to determine the closed-circuit current consumption of the 
component. 
6.19.2 
Test 
For components with follow-on current (e.g. fan), the closed-circuit current consumption 
shall be determined after the follow-on current has stopped.  
The component shall be measured with the related peripherals and circuits. 
 
Table 34: Test parameters E-19 Closed-circuit current  
Operating mode of the DUT 
Operating mode II.a  
Test voltage 
12,5 V (+4%, 0 %) 
Number of DUTs 
at least 6 
Test case 1 
T 
Tmin 
Test case 2 
T 
TRT 
Test case 3 
T 
Tmax 
6.19.3 
Requirement 
For all DUTs, a closed-circuit consumption target of 0 mA applies in principle. 
 
For DUTs required to be operated after Terminal 15 OFF, a closed-circuit current 
consumption equivalent (average over 12 h) of ≤ 0,1 mA, corresponding to 1,2 mAh 
(above +40 °C ≤ 0,2 mA) applies in the at-rest phase. This shall be complied with under 
any conceivable at-rest conditions of the vehicle and at any 12 h period. Otherwise, 
release by the department responsible for closed-circuit current management is 
required.  
 
Follow-on current functions shall also be approved by the department responsible for 
closed-circuit current management. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 52 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 51 of 160 
 
6.20  E-20 Dielectric strength 
6.20.1 
Purpose 
This test is intended to simulate the dielectric strength between galvanically isolated 
parts of the DUT, e.g. pins, relays, windings or cables. The test shall be applied to 
components containing or actuating inductive devices. 
6.20.2 
Test 
Table 35: Test parameters E-20 Dielectric strength 
Operating mode of the DUT 
Operating mode II.a 
Test voltage Ueff 
500 V AC, 50 Hz, sinusoidal 
Test duration 
60 s 
Test points 
Application of the test voltage 
- 
to terminals without galvanic connection 
- 
between connection pins and conducting 
housings without galvanic connection 
- 
between connection pins and an electrode 
around the housing if the housing is non-
conducting 
- 
additional test points agreed with the 
relevant department responsible 
Number of cycles 
1 cycle shall be passed with each of the above 
points being tested at least once. 
Number of DUTs 
at least 6 
 
6.20.2.1 Test sequence 
As preparation for the test, the DUTs shall pass through the test "Damp heat, cyclic". 
Before the measurement, the DUTs shall be allowed to dry for 30 minutes. 
 
6.20.3 
Requirement 
Functional status C 
Dielectric breakdowns and arcs are not permissible. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 53 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 52 of 160 
 
6.21  E-21 Backfeeds 
6.21.1 
Purpose 
This test is intended to simulate the behavior of the DUT at Terminal 15 and all lines 
which can be used as wake-up lines in the on-board electrical system. This test shall be 
performed for all components connected to Terminal 15 and/or other lines with "wake-
up" capability. 
6.21.2 
Test 
Table 36: Test parameters E-21 Backfeeds 
Operating mode of the DUT 
Operating mode II.c  
Utest 
UBmax – 0,2 V 
Test temperatures 
TBmax, TRT and TBmin 
Number of DUTs 
at least 6 
6.21.2.1 Test sequence 
The DUT shall be connected identical to the wiring situation in the vehicle (including 
sensors, actuators, etc.) and operated in normal operation. The voltage profile at the 
terminal to be tested shall be measured when it is switched off. The terminal shall be cut 
off by means of a relay or a switch (Rswitch_open→∞). Other possible voltage sources such 
as Terminal 30, shall not be interrupted or cut off during the test (according to the 
behavior in the vehicle). Other resistors at the terminal to be tested are not permissible 
for this test. 
The voltage profile at the Terminal to be tested shall be examined using an external 
resistor ≥10 MΩ (e.g. oscilloscope) to Terminal 31. 
 
 
 
Key 
T 
Probe 
Os 
Oscilloscope 
K 
Terminal to be tested 
 
Figure 21: Schematic circuit diagram E-21 Backfeeds  
 
DUT
Kl.30
K
Kl.31
T
Os
Utest
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 54 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 53 of 160 
 
6.21.3 
Requirement 
Voltage backfeeds to the terminal to be tested are only permissible up to a level of 1 V 
max. This voltage range shall be achieved within t = 20 ms after cutoff. 
 
The voltage on the unconnected terminal to be tested shall decrease below a voltage of 
1 V within t = 20 ms from the time of switch-off. 
 
The voltage time profile shall be continuously decreasing. A discontinuity of the profile 
due to positive pulses is not permitted. 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 55 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 54 of 160 
 
6.22  E-22 Overcurrents 
6.22.1 
Purpose 
The overcurrent protection of mechanical switches, electronic outputs and contacts is 
tested. Higher currents than in the normal load case (e.g. maximum blocking current 
Iblock of a motor) shall also be considered. 
6.22.2 
Test 
Table 37: Test parameters E-22 Overcurrents 
Operating mode of the DUT 
Operating mode II.c  
Temperature 
Tmax 
Test conditions for electronic 
outputs 
The output shall withstand at least the triple value 
of the nominal load without damage. 
 
Load duration 30 min 
Test conditions for switched 
outputs 
For components with IN ≤ 10 A:  
 
Itest = 3 x IN 
For components with IN > 10 A:  
 
Itest = 2 x IN, but at least 30 A and max.  
 
150 A 
For components with Iblock > 3 x IN:  
 
Itest = Iblock  
Switch "OFF", "ON" and "OFF" again under load. 
 
Load duration 10 min 
 
Each contact shall be tested individually in the case 
of multiple-contact relays and multiple-contact 
switches. 
Number of DUTs 
at least 6 
6.22.3 
Requirement 
Functional status A for mechanical components without fuse. If fuse elements are 
available in the load circuit, these may be triggered. 
 
Functional status C for electronic outputs with overload detection (current, voltage, 
temperature). 
 
In addition, no dangerous changes that impair the function or service life (visual and 
electrical characteristics) shall be visible during a visual inspection of all components. 
 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 56 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 55 of 160 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Pages 55 to 159 of LV 124: see MBN LV 124-2 
 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)


### 第 57 页
Version 2.2 
LV 124 
Edition: 2013-02-28 
 
 
 
 
 
Page 160 of 160 
 
Annex G (informative) 
 
Examples of examination methods for physical analysis 
 
• 
Screw loosening torques (e.g. housing screw connection, screws for fastening the 
component, ...) 
• 
Soldering spot defects 
• 
Device / circuit board discolorations (in particular if thermally caused)  
• 
Stiffness/ease of operation, rubbing, play (where parts are moved mechanically)  
• 
Traces of abrasion  
• 
Breaks, cracks, deformations of materials (in particular of casting and sealing 
materials). A suitable test method (X-ray, CT, metallographic sections,…) shall be 
selected by agreement  
• 
Opacity (in particular of parts of optical sensor systems) 
• 
Condition of latch and clip locking mechanisms  
• 
Traces of corrosion and migration, in particular silver and tin migration 
• 
Assessment of plastics for their resistance to hydrolysis (in particular in the case of 
components with inserted stamped grids and Terminal 30 switch circuits)  
• 
Damage to through-hole plated boards, in particular thermal vias  
• 
Damage to internal connection (paddles) of large electrolytic capacitors after 
mechanical load (vibration, mech. shock, drop test)  
• 
Damage to connector pins (e.g. resulting from current, temperature, rubbing, 
oxidation)   
• 
Other irregularities 
• 
ICT result (where possible) 
Uncontrolled copy when printed (: Yinfeng Yan, 2015-01-28)

