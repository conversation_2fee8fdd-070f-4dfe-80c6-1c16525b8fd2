# MBN_LV_124-2_CH_2013-08_3.5t及以下乘用车电气电子零部件环境测试标准.pdf

## 文档信息
- 标题：<4D6963726F736F667420576F7264202D204D424E5F4C565F3132342D325F43685F323031332D30385F332E3574BCB0D2D4CFC2B3CBD3C3B3B5B5E7C6F8B5E7D7D3C1E3B2BFBCFEBBB7BEB3B2E2CAD4B1EAD7BC2E646F6378>
- 作者：yanyinfeng
- 页数：110

## 文档内容
### 第 1 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
梅赛德斯奔驰                                                                                                            MBN LV 124‐2 
企业标准                                                                                                            颁布日期：2013‐08 
                                                                                                              取代：MBN LV124‐2:2009‐11 
                                                                                                                      总页数（包括附录）：111 
                                                                                                                专业负责人：Dr. Ralf Getto 
                                                                                                                    工厂 059；部门：RD/EDF 
                                                                                                                    电话：+49 7031 4389 413 
 
3.5t 以下乘用车的电气和电子元件 
一般要求，测试条件和测试 
第 2 部分：环境要求 
 
前言 
此标准以当前的 LV 124 为基础，由以下汽车制造商 AUDI AG，BMW AG，Daimler AG，Porsche 
AG，Volkswagen AG 进行制订。 
此标准的扉页已经列出与 LV124 的差异部分。如果某个测试内容有个别修改，那么主管专业
部门和相应的生产商之间要协商确认。 
只要测试是按照 DIN EN ISO/IEC 17025 要求，委托独立的研究机构进行的，那么测试报告即
是被认可的。检测报告的认可不以自动释放为条件。 
 
2013.02.28 编制的 2.2 版本的 LV 124 除了 E‐05 中的测试参数不一样，其他保持不变，奔驰将
其分成两部分收录在标准库中，具体见下表： 
MBN 标准号 
LV 标准号 
内容 
LV 124 中的页码 
MBN LV 124‐1 
LV 124 
第 1 部分：12V 电路
电气要求与电气测试
2‐3；6‐54；160 
MBN LV 124‐2 
LV 124 
第 2 部分：环境要求
与测试 
2；4‐5；55‐159 
 
应用附注： 
根据应用范围，本标准也适用于在本标准颁布日期尚未发布设计任务书的新车项目或新零件。 
供应商合同规定供应商必须遵守本标准。 
所有材料、步骤、过程、零件及系统在材质和可回收方面需遵守现行法律法规。 
 
与 LV 124 的偏差部分 
戴姆勒集团的 MBN LV 124‐2 标准规定，在“恒湿恒温”测试中按照章节 14.14.2 应采用等级
2，章节 14.14.1 中的“恒湿恒温—等级 1”测试是不允许的。 
 
变更 
与 MBN LV 124‐2，2009‐11 版相比作出如下变更： 
—更新扉页 
—其他变更见 LV 124  变更记录章节 
 
 
 


### 第 2 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
变更记录： 
颁布日期 
 
2013‐02 
编辑更改 
 
第 1 部分  ‐ 12V 整车电源的电气要求和测试： 
基本修改  –  修改各项测试要求 
 
第 2 部分  –  环境要求和测试 
增加多种运行模式下的元件、与冷却液相关的元件，修改耐久性
测试 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 3 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
LV124 
目录 
第 1 部分—12V 整车电源的电气要求和测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐6 
1  适用范围‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐6 
2  标准参考‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐6 
3  术语和定义‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐6 
3.1  术语和缩写‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐6 
3.2  电压和电流‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐7 
3.3  温度‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐7 
3.4  时间‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐8 
3.5  内阻，接线柱标记，频率‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐8 
4  基本要求‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐8 
4.1  电压和电流‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐8 
4.2  温度说明‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐9 
4.3  标准公差‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐9 
4.4  标准值‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐9 
4.5  采样率和测量值分辨率‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐9 
4.6  测试电压‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐9 
4.7  工作电压范围和编码‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐10 
4.8  功能参数‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐10 
4.9  运行方式‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐12 
4.10  接口说明‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐13 
4.11  实施限制‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐13 
4.12  电气测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐14 
5 测试选择表‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐16 
6  电气要求和测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐18 
6.1 E‐01 长时间过载电压‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐18 
6.2 E‐02 瞬间过载电压‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐19 
6.3 E‐03 瞬间低电压‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐21 
6.4 E‐04 跨接启动‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐22 
6.5 E‐05 抛负载‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐23 
6.6 E‐06 叠加的交流电‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐24 
6.7 E‐07 电源电压缓慢降低和上升‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐26 
6.8 E‐08 电源电压缓慢降低快速上升‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐28 
6.9 E‐09 复位特性‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐30 
6.10 E‐10 短暂断路‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐32 
6.11 E‐11 启动脉冲‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐35 
6.12 E‐12 整车电源调节的电压曲线‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐39 
6.13 E‐13 Pin 脚断开‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐40 
6.14 E‐14 插头断开‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐42 
6.15 E‐15 反极性‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐43 
6.16 E‐16 接地错位‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐46 
6.17 E‐17 信号线和用电回路短路‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐47 
6.18 E‐18 绝缘电阻‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐49 


### 第 4 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
6.19 E‐19 静态电流‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐50 
6.20 E‐20 击穿强度‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐51 
6.21 E‐21 反馈‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐52 
6.22 E‐22 过载电流‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐54 
第 2 部分–  环境要求和测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐55 
7  适用范围‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐55 
8  标准参考‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐55 
9  术语和定义‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐56 
9.1  术语和缩写‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐56 
9.2  电压‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐57 
9.3  温度‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐57 
9.4  时间‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐57 
9.5  标准公差‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐57 
9.6  标准值‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐58 
10  基本要求‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐58 
10.1  运行模式‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐58 
10.2  运行方式‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐59 
10.3  温度设置  ‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐61 
10.4  参数测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐62 
10.5  应用迁移分析法的参数持续监控‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐63 
10.6  气密测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐64 
10.7  采样率和测量值分辨率‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐64 
11  使用属性‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐65 
11.1  使用寿命参数‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐65 
11.2  温度分布概况‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐65 
12  测试选择‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐67 
12.1  测试选择表‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐67 
12.2  测试流程计划‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐69 
13  机械要求和测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐70 
13.1 M‐01 跌落测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐70 
13.2 M‐02 石击测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐71 
13.3 M‐03 粉尘测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐72 
13.4 M‐04 振动测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐74 
13.5 M‐05 机械冲击‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐85 
13.6 M‐06 机械耐久冲击测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐86 
14 气候要求和测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐87 
14.1 K‐01 高/低温老化‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐87 
14.2 K‐02 温度分级测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐88 
14.3 K‐03 低温运行‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐89 
14.4 K‐04 重新喷漆温度‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐90 
14.5 K‐05 温度冲击（元件）‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐91 
14.6 K‐06 运行时外舱盐雾测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐92 
14.7 K‐07 运行时内舱盐雾测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐94 
14.8 K‐08 湿热循环‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐96 


### 第 5 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.9 K‐09 湿热循环（带冰冻）‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐97 
14.10 K‐10 防水– IPX0 至 IPX6K‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐98 
14.11 K‐11 高压/蒸汽喷射清洗‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐99 
14.12 K‐12 溅水温度冲击‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐100 
14.13 K‐13 浸水温度冲击‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐103 
14.14 K‐14 恒湿恒温‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐104 
14.15 K‐15 冷凝和气候测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐108 
14.16 K‐16 温度冲击（不带外壳）‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐115 
14.17 K‐17 日照‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐116 
14.18 K‐18 有害气体测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐117 
15  化学要求和测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐118 
15.1 C‐01  化学测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐118 
16 耐久测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐121 
16.1 L‐01 机械/液压耐久测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐121 
16.2 L‐02 高温耐久测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐122 
16.3 L‐03 温度变化耐久测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐125 
附录 A（标准）测试流程‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐129 
A.1  测试流程计划‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐129 
A.2  顺序测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐130 
A.3  非顺序测试（平行测试）‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐132 
A.4  耐久测试‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐133 
附录 B（标准）不同安装区域的常用温度集‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐134 
B.1  温度集 1‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐135 
B.2  温度集 2‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐135 
B.3  温度集 3‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐135 
B.4  温度集 4‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐135 
附录 C（标准）高温耐久测试的计算模型‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐136 
C.1 Arrhenius‐模型‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐136 
C.2 Arrhenius‐模型举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐137 
C.3 高温下性能降低的 Arrhenius‐模型应用‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐138 
C.4 高温下性能降低的 Arrhenius‐模型应用举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐139 
C.5 冷却液循环系统元件的 Arrhenius‐模型应用‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐141 
C.6 冷却液循环系统元件的 Arrhenius‐模型应用举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐145 
附录 D（标准）温度变化耐久测试的计算模型‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐148 
D.1 Coffin‐Manson‐模型‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐148 
D.2 举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐149 
D.3 冷却液循环系统元件的 Coffin‐Manson‐模型应用‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐150 
D.4 冷却液循环系统元件的 Coffin‐Manson‐模型应用举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐153 
附录 E（标准）等级为 2 的恒湿恒温测试的计算模型‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐155 
E.1 Lawson‐模型‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐155 
E.2  举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐156 
附录 F（参考）冷凝测试，测试箱设置和图表‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐157 
附录 G（参考）物理分析方法举例‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐‐160 
 


### 第 6 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
 
LV 124 中的第 6 页至第 54 页是 LV 124 的第一部分，且收录在奔驰标准 MBN LV 124‐1 中（也
可参见 MBN LV 124‐2 的扉页） 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 7 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
第 2 部分–  环境要求和测试 
 
7  适用范围 
 
该标准规定了 3.5t 及以下车辆电气、电子及机械零部件和系统的测试条件要求和方法。 
 
其他测试条件及要求或偏差要求见相应的零部件设计任务书。 
 
注意：标准中涉及到的测试不能用来作为零件认可或生产过程认可的证明。 
 
8  标准参考 
表 38：标准参考 
DIN 75220 
阳光模拟元件老化测试 
DIN EN 13018 
非破坏性测试—外观检查—基本要求 
DIN EN 60068‐2‐1 
环境影响‐第 2‐1 部分：测试方法‐测试 A：冷 
DIN EN 60068‐2‐2 
环境影响‐第 2‐2 部分：测试方法‐测试 B：干热 
DIN EN 60068‐2‐6 
环境影响‐第 2‐6 部分：测试方法‐测试 Fc:振动（正弦） 
DIN EN 60068‐2‐11 
环境测试  第 2 部分：测试‐测试 Ka：盐雾 
DIN EN 60068‐2‐14 
环境测试  第 2 部分：测试‐测试 N：交变温度 
DIN EN 60068‐2‐27 
环境影响‐第 2‐27 部分：测试方法‐测试 Ea 和指南：冲击 
DIN EN 60068‐2‐29 
环境测试  第 2 部分：测试 Eb 和手册：机械耐久冲击实验 
DIN EN 60068‐2‐30 
环境影响‐第 2‐30 部分：测试流程‐测试 Db：湿热，循环（12+12
小时） 
DIN EN 60068‐2‐38 
环境测试  第 2 部分：测试‐测试 Z/AD：混合测试，温度/湿度，
循环 
DIN EN 60068‐2‐60 
环境测试  第 2 部分：测试‐测试 Ke：流动的混合气体防腐蚀
测试 
DIN EN 60068‐2‐64 
环境测试  第 2 部分：测试流程测试 Fh：振动，宽频带噪音
（电子调节）和手册 
DIN EN 60068‐2‐78 
环境测试  第 2‐78 部分：测试‐测试 Cab：恒温恒湿 
DIN EN ISO 11124‐2 
应用涂料和有关制品前钢基材的制备.金属喷砂清理研磨料
规范.第 2 部分:冷铁粗砂 
DIN EN ISO 20567‐1 
涂层材料‐涂层抗碎石击打强度‐第 1 部分：多种碎石击打测试
DIN EN ISO 6270‐2 
涂层材料‐耐潮性‐第 2 部分：冷凝水气候测试 
ISO 12103‐1 
道路车辆—粉尘过滤器测试‐第 1 部分：亚利桑那州粉尘 
ISO 16750‐3 
道路车辆—电气电子设备的环境条件和试验  第 3 部分:机械
负荷 
ISO 16750‐5 
道路车辆‐‐电气和电子设备的环境条件和试验‐‐第 5 部分:化
学负荷 
ISO 20653 
道路车辆‐IP‐Code 防护等级‐电气元件防杂质、水 
 
 
 
 


### 第 8 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
9  术语和定义 
9.1  术语和缩写 
表 39：环境要求和测试缩写表 
电子总成 
已装好电子零件的不带外壳的电路板 
模块/零部件 
电气、电子或机电零部件（如电阻、电容、晶体管、IC、继电器） 
DUT 
测试样品 
功能 
含系统功能和诊断功能 
硬件冻结 
开发过程中的一个节点，从这时起硬件不能再变更 
ICT   
逐个元件测试 
具有冷凝功能的
环境测试箱 
环境测试箱内有一个可以调控的水浴装置，它可以将规定的水量转化为
水蒸气。PCB 板上的冷凝薄膜强度视热质量、相对湿度以及水浴的温度
梯度而定。冷凝期间需关掉测试箱的温湿调控，通过水浴来调控测试的
室温。 
元件 
完整的仪器、控制器或机电设备（带外壳） 
样品 
待测试的系统或元件 
PTB 
德国联邦物理技术研究院 
PSD 
功率谱密度 
电路板 
未组装的内部器件（未组装的 PCB、陶瓷、引线框、弹力带等） 
系统 
由各功能元件组成，如制动控制系统（控制器、液压、传感器） 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 9 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
9.2 电压 
表 40：电压定义缩写 
UBmin 
最低工作电压 
UB 
工作电压 
UBmax 
最高工作电压 
 
9.2.2.1  含其他要求的元件电压 
 
UBmin，HV 
最低工作电压—HV 最低直流工作电压 
UB，HV 
工作电压—HV 直流工作电压 
UBmax，HV 
最高工作电压‐HV 最高直流工作电压 
 
9.3 温度 
表 41：温度定义 
Tmin 
最低工作温度 
TRT 
室内温度 
Tmax 
最高工作温度 
Top,min 
有过载保护/低温保护功能的元件的最低工作温度 
Top,max 
有过载保护/高温保护功能的元件的最高工作温度 
TTest 
测试温度 
 
9.3.1  冷却液管路元件的温度 
Tkühl,nom 
冷却液管路的冷却液额定温度 
Top,max 
冷却液管路的冷却液最低温度 
TTest 
冷却液管路的冷却液最高温度 
 
9.4 时间 
表 42：时间定义 
tPrüf 
测试时间 
tBetrieb 
使用寿命内的工作时间 
 
9.5 标准公差 
如果没有其他说明，则按照表 43 实施。 
公差涉及到要求的测量值。 
表 43：标准公差 
频率 
±1% 
温度 
±2℃ 
空气湿度 
±5% 
时间 
+5%，0% 
电压 
±2% 
电流 
±2% 
振动 
±3dB 
振动功率谱密度 PSD 
±5% 


### 第 10 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
9.6 标准值 
如果没有其他说明，则按照表 44 实施。 
表 44：标准值 
室内温度 
TRT=23℃±5℃ 
空气湿度 
Frel=相对湿度 25%‐75% 
测试温度 
TTest=TRT 
工作电压 
（用于测试） 
UB=14V 
 
10  基本要求 
10.1  运行模式 
纯粹靠燃烧发动驱动的汽车，其使用寿命期间内的运行状态一般分为两种运行模式： 
� 
驾驶状态 
� 
停车状态 
 
其他驱动的汽车也可以考虑其他运行模式（见表 45）。 
 
与多种运行模式相关的元件（图 22），如有必要，每种运行模式的运行方式（见章节 10.2）
需一一进行确定。 
 
表 45：运行模式基本要求 
运行模式 
关闭汽车 
插上充电线 
给驱动电池充电
启动电力线通信
（如有） 
驾驶中 
否 
否 
是/否 
否 
充电状态 
是 
是 
是 
是 
预处理 
是 
是/否 
是/否 
是/否 
入网停车 
是 
是 
否 
是 
离网停车或停车 
是 
否 
否 
否 
 
测试中需考虑（图 22 中）元件相关的所有运行模式。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 11 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
图 22：不同运行模式的载荷谱分类 
 
10.2  运行方式 
10.2.1  总则 
电气、电子、机电元件及系统在其寿命期间会有多种运行方式，这也是本标准中需要测试的。
运行方式和用电器（如控制器、总线消息、原始传感器、原始执行器或备用线路）的具体要
求及限制条件由委托方和承接方共同协商确定并记录存档。 
 
10.2.2  运行方式Ⅰ—样件没有电气连接 
10.2.2.1 运行方式Ⅰ.a 
样件没有电流通过，无接插件和线束。 
冷却液管路没有注入冷却液，且管路密封。 
 
 
 
 
 
 
 
 
 
 
 
载荷谱
分成多个运行模式
1.定义载荷谱 
2.确定各个运行模
式相关的规格尺寸 
3.模型应用 
4.定义各个运行模
式相关的规格尺寸 
驾驶状态 
充电状态
预处理
入网停车 
离网停车
运行时间 
冷却液温度分布 
环境温度分布 
运行时间 
冷却液温度分布 
环境温度分布 
运行时间 
冷却液温度分布 
环境温度分布 
运行时间 
冷却液温度分布 
环境温度分布 
运行时间 
冷却液温度分布 
环境温度分布 
寿命模型 
寿命模型 
寿命模型 
寿命模型 
寿命模型 
环境测试温度 
冷却液测试温度 
部分测试时长 
电负载 
环境测试温度 
冷却液测试温度 
部分测试时长 
电负载 
环境测试温度 
冷却液测试温度 
部分测试时长 
电负载 
环境测试温度 
冷却液测试温度 
部分测试时长 
电负载 
环境测试温度 
冷却液测试温度 
测试时间 
电负载 
环境测试温度 
冷却液测试温度
总测试时长
电负载 
元件各种运行模式下的测试要求


### 第 12 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
10.2.2.2 运行方式Ⅰ.b 
样件未通电流，但已连接接插件和电线束。 
如有冷却液管路，冷却液管路注入冷却液，且连接上冷却液管。 
 
10.2.3 运行方式Ⅱ  –  样件电气连接 
 
10.2.3.1 运行方式Ⅱ.a 
样件无负载运行。 
如有冷却液管路，冷却液管路注入冷却液，且连接上冷却液管。如有要求，冷却介质的流量
及温度按照零部件设计任务书中的要求进行调控。 
 
10.2.3.2 运行方式Ⅱ.b 
样件以最低负载运行。 
样件运行时必须只能产生最小的自身发热（如：通过减小持续的输出功率或者通过较少的外
部负载触发）。 
如有冷却液管路，冷却液管路注入冷却液，且连接上冷却液管。如有要求，冷却介质的流量
及温度按照零部件设计任务书中的要求进行调控。 
 
10.2.3.3 运行方式Ⅱ.c 
样件以最大负载运行（高级用户 Power user，但不能出现错误使用） 
样件运行时必须产生最大的自身发热（如：通过切合实际的最大持续的输出功率或者通过频
繁的外部负载触发）。 
如有冷却液管路，冷却液管路注入冷却液，且连接上冷却液管。如有要求，冷却介质的流量
及温度按照零部件设计任务书中的要求进行调控。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 13 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
10.2.3.4 运行方式举例 
表 46：运行方式举例 
元件举例 
运行方式Ⅱ.a 
运行方式Ⅱ.b 
运行方式Ⅱ.c 
带有导航的汽车收音
机 
停车状态的元件（睡
眠）空载结束，打开
KL30   
汽 车 行 驶 状 态 的 元
件。司机关闭元件，
激活 BUS/μC’s，打开
KL15   
汽车行驶状态的元件。
启动元件（CD，导航，
放大器），激活 BUS/
导航仪 
防盗报警装置 
汽车运行无此功能 
监视停车状态的汽车内部 
制动调节系统 
停车状态的元件，空
载结束 
行驶中不操作刹车系
统 
行驶中多次循环使用
刹车系统（不允许不当
行为，如：不间断的刹
车操作） 
车载充电器 
离网（OFF‐Grid）停
车或者汽车运行 
入网（ON‐Grid）停车
（仅电力载波通信，
不充电）汽车调节 
充电 
高压‐蓄电池（蓄电池
管理系统） 
离网（OFF‐Grid）驻
车 
带有电力载波通信 
的入网（ON‐Grid）停
车 
汽车运行，充电 
 
10.3  温度设置 
在规定的运行条件下，元件从恒温处理的时间点起保持稳定的环境温度，在这个时间点后面
的运行时间里元件温度变化不允许超过±3℃。 
 
受托方必须根据实验确认完整的恒温时间，并记录在测试文件中。 
 
温度变化测试中样件在达到规定的恒温后再停留一段时间，以便元件中的应力可以转化延伸。
停留时间见各项测试规定。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 14 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
10.4  参数测试 
设计任务书中或者与委托方协商中会定义敏感参数，也叫关键参数，如：静态电流消耗，工
作电流，输出电压，过渡电阻，输入阻抗，信号速率（上升时间和下降时间），总线规范。
每次测试开始前和测试结束后，都要检测这些参数是否符合标准要求。 
与冷却液管连接的元件：需在 TRT 用 Tkühl,nom,在 Tkühl,max 用 Tmax 用 Tkühl,min 进行参数测试。 
如果零部件设计任务书中没有其他规定，则高压（HV）电源的元件在 UBmin 下用 UBmin,HV;在
UB 下用 UB,HV;在 UBmax 下用 UBmax,HV 进行参数测试。 
 
10.4.1 参数测试（小） 
测量关键参数，检查元件在 TRT 和 UB 的功能特性。带有故障存储功能的元件必须能够读取故
障存储内容。在不拆解样件的情况下，依据 DIN EN 13018 对元件外部缺陷/变化如：裂纹，
开裂/脱落，褪色，变形等进行外观检查。 
测试人员必须将测试获得的关键参数值，功能特性或者故障存储记录的变化，包括外观检查
中的异常情况与新状态的参数进行对比评估。 
 
所有测试结果都必须在检测报告上进行记录。 
 
10.4.2 参数测试（大） 
测量关键参数，分别测量电压在 UBmin,UB,UBmax 时的 Tmax,TRT,Tmin 下的零件功能特性。 
带有故障存储功能的元件必须能够读取故障存储内容。在不拆解样件的情况下，依据 DIN EN 
13018 对元件外部缺陷/变化如：裂纹，开裂/脱落，褪色，变形等进行外观检查。 
 
测试人员必须将测试获得的关键参数值，功能特性或者故障存储记录的变化，包括外观检查
中的异常情况与新状态的参数进行对比评估。 
 
所有测试结果都必须在检测报告中进行记录。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 15 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
10.4.3 参数测试（功能测试） 
必须在规定的温度和三个不同的电压（UBmin,UB,UBmax）下测试关键参数。 
必须测试元件的基本功能，带有故障存储功能的元件必须能够读取故障存储内容。 
 
测试人员必须将测试获得的关键参数值，基本功能或者故障存储记录的变化与之前的测试负
荷进行对比评估。 
 
所有测试结果都必须在检测报告中进行记录。 
 
10.4.4  物理分析 
物理分析时，必须将试验件进行拆解，并依据 DIN EN 13018 进行外观检查。 
 
附加分析由委托方和受托方共同协商确认，分析案例参见附件 G。 
 
必须对元件与新状态之间的变化进行评估。若测试件显示异常时，后续必须尽可能增加更多
的测试件或者使用附加分析方法，与委托方一起进行分析确认。 
 
所有测试结果都必须在检测报告上进行记录。 
 
10.5 应用迁移分析法的参数持续监控 
整个测试期间必须记录监控的关键参数。 
 
有故障记录功能的元件必须持续监控故障存储并记录。 
 
通过持续参数监控获得的数据来分析其变化和趋势，以便识别元件的异常，老化或者故障。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 16 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
10.6  气密测试 
有冷却液流经的元件需进行气密测试，测试时需注意元件的特殊结构以及冷却液管路的规格
标准。 
如零部件设计任务书中无其他规定，则按照下列测试要求进行： 
 
� 
压力脉动测试：在 Tmax 和 Tkühl,max 温度下对冷却液管路在最低和最高压力之间进行
100000 次压力交变；在 Tmin 和 Tkühl,min 温度下对冷却液管路在最低和最高压力之间进行
50000 次压力交变。 
� 
分别在 TRT 采用 Tkühl,nom,Tmax 采用 Tkühl,max，Tmin 采用 Tkühl,min 温度在最小、最大和额定冷却
液管路的压力时进行静态气密测试。 
� 
如果元件要在低压方式下注入冷却液，则在 TRT 下施加 20mbar 以下的测试压力进行低
压测试。如零部件设计任务书无其他规定，从环境压力到测试压力的来回转变的转移时
间应＜5s。测试压力的停留时间至少为 30s。 
� 
分别在 TRT 采用 Tkühl,nom,Tmax 采用 Tkühl,max，Tmin 采用 Tkühl,min 温度在最小、最大和额定冷却
液管路的压力时进行冷却液流速测试。 
 
如零部件设计任务书无其他规定，气密测试具体细节由委托方和承接方协商确定。 
 
在进行顺序测试（A.2）、非顺序测试（平行测试）（A.3）以及耐久性测试（A.4）时，要分别
在首次参数测试（大）和最后的参数测试（大）结束后进行气密测试。 
 
10.7  采样率和测量值分辨率 
测量系统的采样率或者带宽视各自测试而定。 
 
必须识别和记录与功能相关的峰值（临时正/负偏差）。 
 
测量值分辨率必须与各自的测试相符合。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 17 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
11  使用属性 
11.1  使用寿命参数 
 
使用寿命常用参数见表 47. 
表 47：寿命要求 
使用寿命 
15 年 
运行状态下的工作时长 
8000h 
行驶里程 
300000km 
 
其他驱动的汽车如有必要也可以考虑其他运行模式（见表 45）。 
 
其他运行模式（见表 45） 
� 
充电状态下的工作时长 
� 
预处理状态下的工作时长 
� 
入网停车状态下的工作时长 
下的工作时长见零部件设计任务书。 
 
11.2 温度分布概况 
为了全面描述汽车安装范围内的元件的温度负载，除了规定最小工作温度 Tmin 和最大工作温
度 Tmax 之外，还需规定元件在 Tmin 和 Tmax 之间的不同温度分布的间隔时间。 
 
针对其他驱动的汽车：必须区分“驾驶状态”、“充电状态”、“预处理状态”和“入网停车状
态”这几个模式。且必须分别规定环境温度下和冷却液管路温度下的温度分布状况（见图
22）。 
 
一般情况下温度分布是连续的分布，因为元件的环境温度值可以是 Tmin 和 Tmax 间的任意数值。 
为简化测试时间的计算，可以借助 Arrhenius（参见附录 C）加快使用寿命模型的方法通过非
连续温度节点来表现持续分布状况。每个温度节点都必须给出元件工作时间里 pi 的百分比。 
 
相应的温度分布见下面的总表 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 18 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
表 48：温度分布 
温度℃ 
分布 
TFeld.1=Tmin 
p1 
TFeld.2 
p2 
… 
… 
TFeld.n=Tmax 
pn 
原则上依据现场测量和技术经验。 
 
不同安装区域的常见温度分布参见附录 B。 
不同元件的温度分布应用需通过如：车辆测量，模拟或者依据经验进行验证。如有偏差应根
据零部件进行调整。 
特殊的安装位置或者安装条件（如：安装位置靠近热源处）一般需要定义元件匹配的温度分
布情况。 
可行的温度分布情况需记录在零部件设计任务书中。 
附件 B 还补充了汽车运行状态中的元件的平均温升的常见数值。 
与元件相关的温度分布需在零部件设计任务书中进行规定。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 19 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
12  测试选择 
12.1  测试选择表 
表 49：测试选择表 
测试 
适用于 
要求说明 
M‐01 跌落测试 
所有元件 
测试时肯定会受损的元件（如：玻璃，高敏感
测量传感器），可以与委托方确认取消该项测
试。这些必须记录下来。 
无 
M‐02 石击测试 
安装区域可能会遭受石击的元件 
无 
M‐03 粉尘测试 
元件防护等级 IP6KX 
元件防护等级 IP5KX 
所有元件 
不允许有粉尘进入的元件 
只允许少量的不影响功能和安全的粉尘进入
的元件 
无 
M‐04 振动测试 
—按照振动属性 A 
—按照振动属性 B 
—按照振动属性 C 
—按照振动属性 D 
—按照振动属性 E 
所有元件 
安装在发动机上的元件 
安装在变速箱上的元件 
安装在进气歧管但不固定连接的元件 
安装在车身上的元件 
安装在非簧载质量（车轮、悬架）上的元件 
无 
M‐05 机械冲击 
所有元件 
无 
M‐06 机械耐久冲击 
安装在门或者罩盖上的元件 
冲击次数 
K‐01 高温/低温老化 
所有元件 
无 
K‐02 温度分级测试 
所有元件 
无 
K‐03 低温运行 
所有元件 
无 
K‐04 重新喷漆温度 
安装在外舱并在重新喷漆时会升温的零件 
无 
K‐05 温度冲击（元件） 
—根据 DIN EN 60068‐2‐14 
Na（空气‐空气） 
—根据 DIN EN 60068‐2‐14 
Nc（介质‐介质） 
所有元件 
非永久在液体中运行的元件 
 
永久在液体中运行的元件（IPX8） 
 
测试方法（Na
或 Nc），如果
Nc：测试介质 
K‐06 运行时的外舱盐雾
测试 
安装在外部，底盘或发动机舱的零件 
测试循环次数 
K‐07 运行时的内舱盐雾
测试 
安装在车内裸露位置的零件（如：行李箱，车
门湿区，备胎区的车门内侧壁小袋） 
无 
K‐08 湿热，循环 
所有元件 
无 
K‐09 湿热，循环（带有霜
冻） 
所有元件 
无 
K‐10 防 水 保 护 ‐IPX0 至
IPX6K 
—防护等级 IPX0 
—防护等级 IPX1 
—防护等级 IPX2 
 
—防护等级 IPX3 
所有元件 
 
不需要防水的元件 
水滴垂直落下时不允许产生损坏的元件 
距安装面倾斜角不超过 15°，水滴垂直落下时
不允许产生损坏的元件 
喷淋水时不允许产生损伤的元件 
无 


### 第 20 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
—防护等级 IPX4K 
—防护等级 IPX5 
—防护等级 IPX6K 
压力调高时溅水不允许产生损伤的元件 
喷水时不允许产生损伤的元件 
压力调高时强烈喷水不允许产生损伤的元件 
K‐11 高压/蒸汽喷射清洗 
直接受到高压、蒸汽喷射清洗或者底盘洗涤的
元件 
无 
K‐12 溅水温度冲击 
可能会遭受溅水的安装在外部或发动机舱的
元件（如：轮胎涉水） 
无 
K‐13 浸水温度冲击 
涉水下面安装的零件，会时不时地浸没在（盐）
水中（如：驶过积水）（IPX7） 
无 
K‐14 恒湿恒温 
所有元件 
等级 
K‐15 冷凝和气候测试 
是否需要进行该项测试视零部件而定。如果有
此项测试要求，参见设计任务书。 
 
如果该测试在设计任务书中有规定，则外壳防
水的零部件可以选择进行 K‐15a 的冷凝测试或
进行 K‐15b 的气候测试。外壳不防水的零部件
则进行 K‐15a 的冷凝测试。 
无 
K‐16 温度冲击 
（无外壳） 
所有元件的组件 
无 
K‐17 日照 
阳光能直接照射到的元件 
无 
K‐18 有害气体测试 
含有非气密的开关触点的元件 
无 
C  化学测试 
所有元件 
化学物质 
运行方式 
L‐01 机械/液压耐久测试 
带有机械/液压操作/功能循环的元件如：制动
控制，座椅调节循环，开关/按钮操作 
功能/操作循环
次数 
L‐02 高温耐久测试 
所有元件 
测试时间 
L‐03 温度变化耐久测试 
所有元件 
测试循环次数 
 
12.2  测试流程 
元件相关的测试流程参见设计任务书。 
 
不同 OEM（如：工业标准构件 IBK）间的合作项目讨论前提参见附件 A 的测试流程。 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 21 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
13  机械要求和测试 
 
13.1 M‐01 跌落测试 
 
13.1.1 目的 
该项测试模拟了底座上元件从整个过程链到安装到车上时的可能会出现的自由落体活动。 
该测试是为确保零件跌落时没有受损，从而在装车时没有出现隐蔽性损坏，如零件内部松动
或出现裂纹。 
 
13.1.2 测试 
表 50：M‐01 跌落测试参数 
样件运行方式 
运行方式Ⅰ.a 
跌落高度 
1m 
碰撞面 
混凝土地面 
测试循环 
3 个样件每个都要进行朝两个方向（第 1 个样件：±X，第 2
个样件：±Y，第 3 个样件：±Z）进行跌落测试 
样件数量 
3 
 
13.1.3 要求 
必须用肉眼对样件进行检查，通过摇动来检查样件是否松动。 
 
—如果样件外表有损坏，必须将这个损坏记录在测试报告中。 
 
—如果样件外表完好的，则样件在测试结束后必须功能完好，所有参数必须在规定范围内。
按照章节 10.4 进行参数测试（大）验证，内部不允许有隐藏性损伤。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 22 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
13.2 M‐02 石击测试 
 
13.2.1 目的 
通过碎石撞击测试模拟元件的机械负荷要求。 
测试元件防止缺陷形成（如：变形和裂纹）的稳定性。 
 
13.2.2 测试 
测试标准见 DIN EN ISO 20567‐1，测试方法 B,参数如下： 
表 51：M‐02 石击测试参数 
样件运行方式 
运行方式Ⅰ.b 
粗砂石重量 
500g 
测试压力 
2 bar 
喷砂材料 
硬模铸造铁砂粒，标准见 DIN EN ISO 11124‐2，颗粒大小 4‐5mm 
样件的测试面 
汽车上所有可接触到的表面 
撞击角度 
与喷砂方向呈 54° 
测试装置 
多冲击测试仪，标准见 DIN EN ISO 20567‐1 
循环次数 
2 
样件数量 
6 
 
13.2.3 要求 
测试件必须在测试前和测试后功能完好，所有参数必须在规定范围之内，按照章节 10.4 进
行参数测试（小）验证。 
 
此外还必须用肉眼对样件进行检查，通过摇动来检查样件是否松动。 
在检测报告中记录变化/损坏，并与委托方进行评估。 
 
无需按照 DIN EN ISO 20567‐1 的参数进行评估。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 23 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
13.3 M‐03 粉尘测试 
 
13.3.1 目的 
模拟汽车运行状态下元件的粉尘负荷。 
目的是确保元件能承受电气和机械缺陷。 
 
13.3.2 测试 
测试标准见 ISO‐20653，参数如下： 
 
表 52：M‐03 粉尘测试参数 
样件运行方式 
电气/电子元件：运行方式Ⅱ.a 
 
机电一体化元件（如：带有通风装置的元件）：依据图 23 间歇性
执行运行方式Ⅱ.c 和运行方式Ⅱ.a 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种运行模式（见章节
10），则元件在要求运行方式Ⅱ.c 的期间内，所有与运行方式Ⅱ.c
相关的运行模式的运行时间要相同。 
测试装置 
符合 ISO‐20653:2006 图 1 的竖直流向 
防护等级 
按照设计任务书规定 
测试时间 
共 20 次循环，每次循环各 20 分钟 
样件数量 
6 
 
 
图 23：M‐03 粉尘测试过程 
 
测试过程中，需要模仿车内元件的安装位置。受托方提出测试装置（安装位置，盖板，饰板，
运行情况），并与委托方协商确认和记录。 
 
13.3.3 要求 
必须达到零部件设计任务书中规定的防护等级（防护等级标准见 ISO 20653）要求。 
 
 
 
 
 
 
 
运行方式Ⅱ.c 
粉尘 
运行方式Ⅱ.a 
1 循环/20min 
5 分钟 


### 第 24 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
样件必须在测试前、测试期间以及测试后功能完好，并且所有的参数必须在规定范围内，按
照章节 10.4 进行参数测试（小）验证。 
 
此外还必须用肉眼对样件进行检查，通过摇动来检查样件是否松动。 
在检测报告中记录变化/损坏，并与委托方进行评估。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 25 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
13.4 M‐04 振动测试 
13.4.1 目的 
模拟汽车运行时元件的振动负荷。 
目的是确保测试元件能承受组件松动和材料疲劳的能力。 
 
13.4.2 测试 
测试标准见 ISO 16750 第 3 部分。 
测试按照正弦波振动 DIN EN 60068‐2‐6 和宽频振动 DIN EN 60068‐2‐64 执行，参数如下： 
 
表 53：基本振动测试参数 
样件运行方式 
如果元件是无负载运行：整个测试过程按照
Ⅱ.a 
 
如果元件时带负载运行：依据图 24 间歇性执
行运行方式Ⅱ.a 和运行方式Ⅱ.c 
 
叠加的温度曲线 
按照图 24 进行重复测试： 
振动温度曲线和表 54： 
振动温度曲线 
正弦振动的频率通过时间 
1 Oktave/min，对数的 
振动曲线 A（安装在发动机上的元件） 
依据图 25 和表格 55：正弦波振动 
 
依据图 26 和表格 56：宽带噪声激发振动 
振动曲线 B（安装在变速箱上的元件） 
依据图 27 和表格 57：正弦波振动 
 
依据图 28 和表格 58：宽带噪声激发振动 
振动曲线 C 
（安装在进气歧管但不固定连接的元件） 
依据图 29 和表格 59：正弦波振动 
振动曲线 D（安装在车身上的元件） 
依据图 30 和表格 60：宽带噪声激发振动 
振动曲线 E（安装在底盘上的元件） 
依据图 31 和表格 61：宽带噪声激发振动 
样件数量 
6 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 26 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
安装在电子机器上的元件至少要按照振动曲线 D 进行测试。但该测试曲线不是从电子机器
出发的振动负荷。实际情况下元件会受到该振动负荷。因此测试时需考虑从电子机器出发的
振动负荷。因此要对每个电子机器进行测量。 
 
测试时无需支架或者元件。零部件设计任务书中会规定如何固定已连接的线束和管路（如线
束、冷却液管、液压管路等）。 
 
通过防振器安装在支架或底盘上的元件必须在零部件设计任务书中规定：是否 
 
—所有含防振器的样件 
—所有不含防振器的样件  或 
—3 个含防振器的样件和 3 个不含防振器的样件 
 
要进行测试。 
 
采样率的选择必须能够识别断路和短路。 
 
为确保由元件、支架和零部件组成的整个系统的强度，是否需要进行其他测试，需要和委托
方协商确定。 
 
 
图 24：振动温度曲线 
 
 
 
 
 
 
 
 
时间：分钟
从 135 到 410min：Ⅱ.a 或Ⅱ.c 


### 第 27 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
表 54：振动温度曲线 
时间：min 
温度：℃ 
0 
TRT 
60 
Tmin 
150 
Tmin 
300 
Tmax 
410 
Tmax 
480 
TRT 
 
如果有冷却液管路，则测试温度下的冷却液温度则按照 Tkühl,min 和 Tkühl,max 实施。超出该温度
范围的，只有环境温度才能变化。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 28 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
13.4.2.1 振动曲线 A（安装在发动机上的元件） 
表 55：发动机安装件正弦波振动测试参数 
振动激发 
正弦波 
每个轴向的测试时间 
22h 
振动曲线 
曲线 1：安装在 5 缸及以下发动机上的元件 
曲线 2：安装在 6 缸及以上发动机上的元件 
 
两种情况都有的元件使用组合曲线 
图 25 中的曲线 1 
频率 Hz 
加速振幅 m/s2 
100 
100 
200 
200 
240 
200 
270 
100 
440 
100 
图 25 中的曲线 2 
频率 Hz 
加速振幅 m/s2 
100 
100 
150 
150 
440 
150 
组合 
频率 Hz 
加速振幅 m/s2 
100 
100 
150 
150 
200 
200 
240 
200 
255 
150 
440 
150 
 
 
 
 
 
 


### 第 29 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
 
图 25：安装在发动机上元件的正弦波振动曲线 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
频率 Hz
加
速
振
幅 
曲线 1           
曲线 2 


### 第 30 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
表 56：安装在发动机上元件的宽带噪音激发的振动测试参数 
振动激发 
宽频噪声激发 
每个轴向的测试时间 
22h 
加速有效值 
181m/s2 
图 26 中的振动曲线 
频率 Hz 
功率谱密度(m/s2)2/Hz 
10 
10 
100 
10 
300 
0.51 
500 
20 
2000 
20 
 
 
图 26：安装在发动机上元件的宽带噪音激发的振动曲线 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
频率 Hz
功率谱密度 


### 第 31 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
13.4.2.2 振动曲线 B（安装在变速箱上的元件） 
表 57：安装在变速箱上的元件的正弦波振动测试参数 
振动激发 
正弦波 
每个轴向的测试时间 
22h 
图 27 中的振动曲线 
频率 Hz 
加速振幅 m/s2 
100 
30 
200 
60 
440 
60 
 
图 22：安装在变速箱上的元件的正弦波振动曲线 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
加速振幅 
频率 Hz


### 第 32 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
表 58：安装在变速箱上元件的宽带噪声振动测试参数 
振动激发 
宽带噪声激发 
每个轴向的测试时间 
22h 
加速有效值 
96.6m/s2 
图 28 中的振动曲线 
频率 Hz 
功率谱密度(m/s2)2/Hz 
10 
10 
100 
10 
300 
0.51 
500 
5 
2000 
5 
 
 
图 28：安装在变速箱上元件的宽带噪声振动曲线 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
功率谱密度 
频率 Hz


### 第 33 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
13.4.2.3 振动曲线 C（分离的进气总管安装件） 
表 59：安装在进气歧管但不固定连接的元件正弦测试参数 
振动激发 
正弦波 
每个轴向的测试时间 
22h 
图 29 中的振动曲线 
频率 Hz 
加速振幅 m/s2 
100 
90 
200 
180 
325 
180 
500 
80 
1500 
80 
 
图 29：安装在进气歧管但不固定连接的元件正弦振动曲线 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
加速振幅 
频率 Hz


### 第 34 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
13.4.2.4 振动曲线 D（安装在车身上的元件） 
表 60：安装在车身上的元件宽带噪声测试参数 
振动激发 
宽带噪声 
每个轴向的测试时间 
8h 
加速有效值 
30.8m/s2 
图 30 中的振动曲线 
频率 Hz 
功率谱密度(m/s2)2/Hz 
5 
0.884 
10 
20 
55 
6.5 
180 
0.25 
300 
0.25 
360 
0.14 
1000 
0.14 
2000 
0.14 
 
 
图 30：安装在车身上的元件宽带噪声振动曲线 
 
 
 
 
 
 
 
 
 
 
 
 
 
功率谱密度 
频率 Hz


### 第 35 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
13.4.2.5 振动曲线 E（安装在底盘上的元件） 
表 61：安装在底盘上的元件宽带噪声振动测试参数 
振动激发 
宽带噪声 
每个轴向的测试时间 
8h 
加速有效值 
107.3m/s2 
图 31 中的振动曲线 
频率 Hz 
功率谱密度(m/s2)2/Hz 
20 
200 
40 
200 
300 
0.5 
800 
0.5 
1000 
3 
2000 
3 
 
图 31：安装在底盘上的元件宽带噪声振动曲线 
 
13.4.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
功率谱密度 
频率 Hz


### 第 36 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
13.5 M‐05 机械冲击 
 
13.5.1 目的 
此测试是模拟元件的机械负荷如：越过路面石头等或者事故。 
测试元件耐裂纹或零件松动性。 
 
13.5.2 测试 
测试标准见 DIN EN 60068‐2‐27，参数如下： 
表 62：M‐05 机械冲击测试参数 
样件运行方式 
如果元件是带负载运行：运行状态
的Ⅱ.c 
 
如果元件运行不带负载运行：运行
方式Ⅱ.a   
峰值加速 
500m/s2 
冲击时长 
6ms 
冲击形式 
半正弦 
每个方向（±X，±Y，±Z）冲击次数 
10 
样件数量 
6 
 
13.5.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 37 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
13.6 M‐06 机械耐久冲击测试 
 
13.6.1 目的 
该测试是模拟安装在车门或盖板处的元件以及开关时有高加速的元件的加速力测试。 
测试元件耐松动和材料疲劳性。 
 
13.6.2 测试 
测试标准见 DIN EN 60068‐2‐29，参数如下： 
 
表 63：M‐06 机械耐久冲击测试参数 
样件运行方式 
运行方式Ⅱ.c 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种运行模式（见章
节 10），则元件在要求运行方式Ⅱ.c 的期间内，所有与运行
方式Ⅱ.c 相关的机械冲击时间要相同。 
峰值加速 
300m/s2 
冲击时长 
6ms 
冲击形式 
半正弦 
冲击次数 
安装区域 
冲击次数 
驾驶侧门 
100000 
副驾驶和后座侧门 
50000 
后盖/尾门 
30000 
发动机舱盖 
3000 
如果元件在多个区域安装，则选择最大冲击测试 
安装位置 
测试设备上的样件位置必须和车辆上的安装位置符合一致 
样件数量 
6 
 
13.6.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 38 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14  气候要求和测试 
 
14.1 K‐01 高温/低温老化 
 
14.1.1 目的 
该测试是模拟元件储存和运输时的热负荷。 
测试元件在高温或者低温如运输时（飞机，轮船集装箱））的稳定性。 
如果该测试在顺序测试时进行，则所有元件的初始条件都要一致。 
 
14.1.2 测试 
表 64：K‐01 高温/低温老化测试参数 
样件运行方式 
运行方式Ⅰ.a 
测试时间和测试温度 
每 24h 进行 2 次循环（Tmin 老化 12 小时以及 Tmax 老化 12
小时） 
样件数量 
根据设计任务书中的测试流程规划确定 
 
14.1.3 要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 39 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.2 K‐02 温度分级测试 
 
14.2.1 目的 
 
该测试是模拟元件在不同环境温度下的运行状况。 
测试元件在工作温度范围的小区间内可能出现的功能失效时的稳定性。 
 
14.2.2 测试 
表 65：K‐02 温度分级测试参数 
样件运行方式 
参数测试（功能测试）过程中运行方式Ⅱ.c，其他情况运行方式Ⅱ.a
测试温度 
样件依据图 32 中的温度曲线进行测试，每级温度变化为 5℃ 
测试流程 
样件从每个温度级开始直到达到恒定状态（参见章节 10.4），接着
根据章节参数测试（参见章节 10.4）进行参数测试（功能测试） 
样件数量 
6 
 
图 32：温度分级测试温度曲线 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,min 和 Tkühl,max 温度范围进行。超出冷却液温度范
围的，只有环境温度才能变化。 
 
14.2.3 要求 
样件在每个参数测试（功能测试）中的所有参数必须在规定范围内。 
 
 
 
 
 
 
 
 
 
 
 
温度 
时间


### 第 40 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.3 K‐03 低温运行 
 
14.3.1 目的 
该测试是模拟元件低温负荷要求。 
测试元件在极度低温状态下长时间停车或者行驶之后的功能状况。 
 
14.3.2 测试 
测试标准见 DIN EN 60068‐2‐1，测试 Ab，参数如下： 
 
表 66：K‐03 低温运行测试参数 
样件运行方式 
运行方式Ⅱ.a                12h 
UBmin 时运行方式Ⅱ.c    12h 
运行方式Ⅱ.a                12h 
UB 时运行方式Ⅱ.c        12h 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种运行模式（见章节
10），则元件在要求运行方式Ⅱ.c 的期间内，所有与运行方式
Ⅱ.c 相关的运行时间要相同。 
测试时间 
48h 
测试温度 
Tmin 
样件数量 
6 
 
散热零件的测试标准也可以参见 DIN EN 60068‐2‐1，测试 Ab。 
如有冷却液管路，则最低冷却液温度调整到 Tkühl,min。 
 
输出功率高的元件在运行方式Ⅱ.c 测试时，在 Tmin 时因自身发热导致的测试箱温度升高是允
许的，但必须委托方和承接方协商一致。 
 
14.3.3 要求 
 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 41 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.4 K‐04 重新喷漆温度 
 
14.4.1 目的 
该测试是模拟重新喷漆时元件的负荷，是测试元件在热作用下钎焊、粘接、压焊、焊接时裂
纹形成以及密封件和塑壳裂纹的稳定性。 
14.4.2 测试 
表 67：K‐04 重新喷漆温度测试参数 
样件运行方式 
运行方式Ⅱ.a 
测试时间和测试温度 
130℃维持 15min，110℃维持 1h 
样件数量 
6 
 
如有冷却液管路，冷却液静止时的温度设置在 TRT. 
 
14.4.3 要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 42 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.5 K‐05 温度冲击（元件） 
 
14.5.1 目的 
该测试是模拟车辆运行时交变的温度对元件热负荷的要求。 
是测试元件在热作用下钎焊、粘接、压焊、焊接时裂纹形成以及密封件和塑壳裂纹的稳定性。 
 
14.5.2 测试 
测试标准见 DIN EN 60068‐2‐14，参数如下： 
 
表 68：K‐05 温度冲击（元件）测试参数 
样件运行方式 
运行方式Ⅰ.b 
低温/测试池温度 
Tmin 
高温/测试池温度 
Tmax 
高温/低温停留时间 
温度稳定后 15min（见章节 10.4） 
转化时间（空气‐空气，介质‐介质） ≤30s 
Nc  测试的测试溶液 
汽车运行时元件使用的液体 
测试 
依据 DIN  EN  60068‐2‐14Na，适用于非长期在液体中运
行的元件。 
 
依据 DIN  EN  60068‐2‐14Nc，适用于长期在液体中运行
的元件（IP X8）。 
样件的所有面至少浸入液体中 25mm 
循环次数 
100 
样件数量 
6 
 
14.5.3 要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（大）验证。 
 
介质‐介质测试时还需注意： 
不允许液体渗透，只有整个测试顺序（章节 12.2 中的测试流程计划）结束之后才允许对样
件进行拆解。 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 43 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.6 K‐06 运行时外舱盐雾测试 
 
14.6.1 目的 
该测试是模拟元件在特定区域或者冬季街道上遇到带盐分的空气和水的情况。 
是测试元件在盐分负荷下耐缺陷形成性，如：由于盐分侵蚀导致的元件短路和漏电。 
 
14.6.2 测试 
测试标准见 DIN EN 60068‐2‐11，参数如下： 
 
表 69：K‐06 汽车运行外舱盐雾测试参数 
样件运行方式 
在喷射期间：运行方式Ⅱ.a 运行 1h,运行方式Ⅱ.c 运行 1h(间歇性) 
 
静止期间：运行方式Ⅱ.a 
测试温度 
35℃ 
测试循环 
见图 33，每次测试循环由 8h 喷射时间和 4h 静止时间组成 
测试循环次数 
底盘和发动机舱元件：循环 12 次 
其他元件：循环 8 次 
样件数量 
6 
 
进行测试时仿照元件在汽车上的安装位置。 
 
如有冷却液管路，冷却液温度调整到测试温度。 
 
 
 
 
 
 
 
 
 


### 第 44 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
图 33：汽车运行时外舱盐雾测试  ‐  喷射阶段 
 
14.6.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
喷射阶段
静止阶段
关 
开 
电气运行 
循环 1 次


### 第 45 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.7 K‐07 运行时内舱盐雾测试 
 
14.7.1 目的 
该测试是模拟元件在特定区域遇到带盐分的空气的情况。 
是测试元件在盐分负荷下耐缺陷的形成，如：由于盐分侵蚀导致的元件短路和漏电。 
 
14.7.2 测试 
测试标准见 DIN EN 60068‐2‐11 Ka，参数如下： 
 
表 70：K‐07 汽车运行内舱盐雾测试参数 
样件运行方式 
在喷射期间：运行方式Ⅱ.a 运行 55min,运行方式Ⅱ.c 运行
5min(间歇性) 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种运行模式（见章
节 10），则元件在要求运行方式Ⅱ.c 的期间内，所有与运行方
式Ⅱ.c 相关的运行时间要相同。 
 
静止期间：运行方式Ⅱ.a 
测试温度 
35℃ 
测试循环 
见图 34，每次测试循环由 8h 喷射时间和 4h 静止时间组成 
循环次数 
2 
样件数量 
6 
进行测试时仿照元件在汽车上的安装位置，受托方提出测试装置（安装位置，盖板，挡板，
运行情况），并与委托方协商确认且记录存档。 
如有冷却液管路，冷却液温度调整到测试温度。 
 
 
 
 
 
 


### 第 46 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
图 34：汽车运行时内舱盐雾测试  –  喷射阶段 
 
14.7.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
开 
关 
电气运行 
静止阶段
喷射阶段
循环 1 次


### 第 47 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.8 K‐08 湿热循环 
 
14.8.1 目的 
该测试通过模拟汽车运行时高湿度的温度循环变化来测试元件热负荷大小，测定元件湿热稳
定性。 
 
14.8.2 测试 
测试标准见 DIN EN 60068‐2‐30，参数如下： 
 
表 71：K‐08 湿热循环测试参数 
样件运行方式 
运行方式Ⅱ.a 
总测试时间 
144h 
测试模式 
模式 1 
最高测试温度 
55℃ 
循环次数 
6 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,min 和 Tkühl,max 温度范围进行。超出冷却液温度范
围的，只有环境温度才能变化。 
 
14.8.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
另外在达到最高和最低测试温度后必须分别进行参数测试（功能测试）。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 48 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.9 K‐09 湿热循环（带冰冻） 
 
14.9.1 目的 
该测试通过模拟汽车运行时高湿度的温度循环交替变化来测试元件热负荷（包括冰冻）大小，
测定元件湿热稳定性。 
 
14.9.2 测试 
测试标准见 DIN EN 60068‐2‐38，参数如下： 
 
表 72：K‐09 湿热循环（带冰冻）测试参数 
样件运行方式 
运行方式Ⅱ.a 运行 40min,运行方式Ⅱ.c 运
行,10min(间歇性) 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种
运行模式（见章节 10），则元件在要求运行
方式Ⅱ.c 的期间内，所有与运行方式Ⅱ.c 相
关的运行时间要相同。 
总测试时间 
240h 
循环次数 
10 
循环测试顺序 
一开始的 5 个循环必须带有冷冻阶段，剩下
的循环执行则无需冷冻阶段 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,min 和 Tkühl,max 温度范围进行。超出冷却液温度范
围的，只有环境温度才能变化。 
 
14.9.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 49 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.10 K‐10 防水保护  – IPX0 至 IPX6K 
 
14.10.1 目的 
测试是模拟元件的防水性。 
测定对元件施加融冰水、雨水、喷水时的功能稳定性。 
 
14.10.2 测试 
测试标准见 ISO 20653 参数如下： 
 
表 73：K‐10 防水保护– IPX0 至 IPX6K 的测试参数 
样件运行方式 
运行方式Ⅱ.a 运行 1min,运行方式Ⅱ.c 运行 1min(间歇性) 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种运行模式（见章
节 10），则元件在要求运行方式Ⅱ.c 的期间内，所有与运行方
式Ⅱ.c 相关的运行时间要相同。 
要求的防护等级 
参照元件设计任务书规定 
样件数量 
6 
 
14.10.3 要求 
必须达到设计任务书中要求的防护等级（标准见 ISO 20653）。 
 
不允许渗水，只有整个测试顺序结束后才能打开样件。 
 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 50 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.11 K‐11 高压清洗/蒸汽喷射清洗 
 
14.11.1 目的 
测试模拟汽车清洗时元件的防水要求。 
测试元件高压/蒸汽清洗时的功能稳定性。 
 
14.11.2 测试 
测试标准见 ISO 20653，参数如下： 
表 74：测试参数 
样件运行方式 
运行方式Ⅱ.a 
要求的防护等级 
IP X9K 
水压 
蒸汽喷射时的最小压力为 10000kPa(100bar)，直接在喷嘴口测量 
水温 
80℃ 
执行 
从汽车各个可接触到的方向对样件进行喷射 
样件数量 
6 
 
14.11.3 要求 
必须达到防护等级 IP X9K（见标准 ISO 20653）. 
 
不允许渗水，只有章节 12.2 中测试流程计划中整个测试顺序结束后才能打开样件。 
 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 51 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.12 K‐12 溅水温度冲击 
 
14.12.1 目的 
该测试模拟汽车行驶经过积水区的元件防溅水要求。 
测试涉水时元件突然变冷的功能稳定性。 
 
14.12.2 测试 
表 75：K‐12 溅水温度冲击测试参数 
样件运行方式 
如果元件是无负载运行：整个测试过程按照Ⅱ.a 
 
如果元件时带负载运行：依据图 35 间歇性执行运行方式Ⅱ.a
和运行方式Ⅱ.c（运行状态下） 
测试过程 
样件加热至测试温度，然后依据图 35 进行循环溅水测试，样
件的整个外廓宽度必须溅到水。 
循环时间 
30min 
测试温度 
Tmax 
溅水测试介质 
自来水，含 3%  的亚利桑那沙，细度按照 ISO 12103‐1.必须确
保混合均匀。 
溅水温度 
0 至+4℃ 
溅水喷嘴 
见图 36 
溅水时间 
3s 
流经水量 
每次溅水/喷嘴 3‐4L 
样件与喷嘴间距 
300‐350mm 
循环次数 
100 
样件数量 
6 
 
进行测试时仿照元件在汽车上的安装位置，受托方提出测试装置（安装位置，盖板，挡板，
运行情况），并与委托方协商确认且记录存档。 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,min 和 Tkühl,max 温度范围进行。超出冷却液温度范
围的，只有环境温度才能变化。 
 
测试装置见图 37 
 
 


### 第 52 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
图 35：溅水测试溅水时间 
 
图 36：溅水测试—喷嘴 
 
关 
开 
循环 1 次 
14:57min
3s
30min
运
行
方
式 
溅
水 
尺寸单位：mm


### 第 53 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
图 37：溅水测试装置 
 
14.12.3 要求 
不允许渗水，只有整个测试顺序结束后才能打开样件。 
 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
溅水喷嘴
300mm 到 350mm
溅水 
样件 


### 第 54 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.13 K‐13 浸水温度冲击 
 
14.13.1 目的 
该测试模拟元件进入水中的负荷要求。 
测试加热后的元件在浸入水中温度骤冷情况下的功能稳定性。 
 
14.13.2 测试 
测试标准见 ISO 20653，参数如下： 
 
表 76：K‐13 浸水温度冲击测试参数 
样件运行方式 
如果元件是带负载运行：运行状态下的Ⅱ.c 
 
如果元件不带负载运行：运行方式Ⅱ.a   
要求的防护等级 
IP X7 
测试过程 
样件加热至 Top,max，在 Top,max 停留 15min 直到温度恒定下来（见章
节 10.3）. 
然后将样件完全浸没到测试介质中 5s，测试件所有面必须浸入到
测试介质至少 25mm 处。 
测试介质 
0℃，5%的冷盐水 
浸水时间 
5min 
循环次数 
20 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,max 温度范围进行。超出冷却液温度范围的，只
有环境温度才能变化。 
 
14.13.3 要求 
不允许渗水，只有章节 12.2 中测试流程计划中整个测试顺序结束后才能打开样件。 
 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（小）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 55 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.14 K‐14 恒湿恒温 
 
14.14.1 恒湿恒温‐等级 1（#） 
 
14.14.1.1 目的 
该测试是模拟元件经受湿热的负载情况，测定元件是否能承受由于湿热导致的缺陷如：腐蚀、
迁移/枝状结构生长，塑料、密封件和浇注材料的膨胀和退化。 
 
14.14.1.2 测试 
测试标准见 DIN EN 60068‐2‐78，参数如下： 
 
表 77：K‐14 恒湿恒温‐等级 1 测试参数 
样件运行方式 
运行方式Ⅱ.a 
测试温度 
40℃ 
空气湿度 
相对湿度 93% 
测试时间 
21 天 
样件数量 
6 
 
14.14.1.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
此外每隔 7 天必须进行一次参数测试（功能测试）。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 56 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.14.2 恒湿恒温‐等级 2 
 
14.14.2.1 目的 
该测试是模拟元件在使用寿命期间经受湿热的负载情况，测定元件在遭受湿热导致的缺陷如：
腐蚀、迁移/枝状结构生长，塑料、密封件和浇注材料的膨胀和退化时的质量及可靠性。 
 
14.14.2.2 测试 
测试标准见 DIN EN 60068‐2‐78，参数如下： 
 
表 78：K‐14 恒湿恒温‐等级 2 测试参数 
样件运行方式 
运行方式Ⅱ.a 运行 47h,运行方式Ⅱ.c 运行 1h(间歇
性),直至测试结束 
 
如果元件在运行方式Ⅱ.c 状态下涉及到多种运行模
式（见章节 10），则元件在要求运行方式Ⅱ.c 的期
间内，所有与运行方式Ⅱ.c 相关的运行时间要相同。
测试时间 
按照元件设计任务书，章节 E.1（Lawson 模型） 
测试温度 
65℃ 
测试湿度 
93%相对湿度 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,max 温度范围进行。超出冷却液温度范围的，只
有环境温度才能变化。 
 
测试前需检查确认该测试条件（测试温度 65℃和相对湿度 93%）是否超过元件子件材质的
物理极限（如塑料水解）。如有必要在延长“Lawson 模型”的测试时间时可以和委托方协商
调整测试温度及湿度，（如改为 55℃和相对湿度 93%），这样就不会超过子件的物理极限。
但总体上必须遵守测试等级要求。相对湿度不能超过 93%。 
 
测试期间必须确保样件上不能发生冷凝（局部冷凝也不允许）。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 57 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.14.2.3 高温时性能降低的元件偏差测试 
在高温时（温度从 Top,max＜65℃起）性能降低的零部件（如 LCD 背光照明降低）：测试则不
按照表 78 的恒温 65℃下执行，而是按照表 79 中的参数实施： 
 
表 79：K‐14 元件高温时性能降低的恒湿恒温测试参数 
样件运行方式 
按照图 38 
测试时间 
按照设计任务书章节 E.1 规定（Lawson‐模型）规定 
65℃至 Top,max 间的爬坡曲线时间不计算在测试时间里 
测试温度 
按照图 38 
温度梯度的选择要保证样件不会冷凝融化 
测试湿度 
93%相对湿度 
时间间隔 t1 
47h 
时间间隔 t2 
1h 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,max 温度范围进行。超出冷却液温度范围的，只
有环境温度才能变化。 
 
 
图 38：高温超过 Top,max 时元件性能降低的测试温度曲线 
 
 
 
 
 
 
 
 


### 第 58 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.14.2.4 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 59 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.15 K‐15 冷凝和气候测试 
 
14.15.1 目的 
该测试是模拟汽车上电气组件冷凝情况。 
它是用来评估电气元件冷凝稳定性。 
 
14.15.2 测试 
 
用不带外壳的电气组件进行测试，参数如下： 
 
表 80：K‐15 电气组件冷凝测试参数 
样件运行方式 
运行方式Ⅱ.a 
 
此外还需进行参数测试（功能测试），见“测试过程”一栏 
测试装置 
可以进行冷凝的测试箱（可以调节水浴，通过水浴可以将必要的水量
转化成水蒸气）。 
冷凝期间关掉温度箱控制调节器，通过水浴来调节测试温度。 
测试过程 
1. 将温度测试箱在初始温度下保持 60min,以确保样件温度稳定下
来。然后进行冷凝。 
2. 开始后 30 分钟内到冷凝结束前 30 分钟（见图 41），每次水浴—
温度上升 10K 都要进行参数测试（功能测试），但只能在电压 UB
状态下进行。 
参数测试（功能测试）的消耗功率要尽可能低，时间不超过 2min,
否则样件会很快变热，无法进行冷凝。 
测试温度 
见图 41 
实验室相对湿度 
见图 41 
冷凝期间相对湿度必须达到 100% (0%,‐5%)。 
测试时间 
32.5h(5 次循坏，每次循环 6.5h) 
测试介质 
蒸馏水，导电率不超过 5μS/cm 
样件位置 
和安装在汽车上的位置一样。 
为保持该位置可采用塑料支架。 
 
如果样件用在多处部位，则需在实验室按照多个位置进行模拟。 
测试装置 
见图 39 
 
测试期间如图 40 采用塑料罩，以将不同风速下的不良因素降至最低。
塑料罩斜面要指向实验室门一侧。塑料罩尺寸要与实验室尺寸相匹
配。塑料罩和实验室墙壁间距为实验室宽度/深度的 10%，但至少要
达到 8cm. 
根据标准 DIN EN ISO 6270‐2 塑料罩上方倾斜角α应≥12°。 
测试条件 
在最终确定电路板设计（硬件冻结）前进行一次冷凝测试，但要在接
近批产条件的电气组件上进行，以优化组件对冷凝的敏感性，如设计
变更或线路变更以达到优化目的。 
 


### 第 60 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
组件制造如有变更（如电路板、焊剂、焊料、焊接工序、设计、标准
化存储或子件）必须重新进行测试。 
循环次数 
5 
样件数量 
6 个组件 
 
 
图 39：K‐15a 组件冷凝测试装置 
 
 
图 40：塑料罩 
 
 
 
 
 
 
 
 
 
 
加热 
热交换器（冷却）
测试箱门 
样件 
实验室 
温度传感器/调节器
熔化阶段
干燥阶段


### 第 61 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
 
 
 
 
图 41：K‐15a 冷凝测试流程 
14.15.1.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4 通过持续的参数监控进行参数测试（大）验证。 
 
此外还必须检查电气元件是否有迁移（如银锡迁移）现象，是否有枝状结构产生。 
 
不允许产生电气化学迁移现象和枝状结构。 
 
其他变化（如腐蚀、脏污）需在测试报告中注明，并与委托方一起评估。 
 
测试报告中必须附上下列资料： 
1. 测试箱设置 
2. 一次循环参数对比（SOLL/IST） 
3. 所有 5 次循环的参数对比（SOLL/IST） 
示例见附录 F。 
1） 空气温度达到 75℃开始干燥阶段 
2） 样件必须干燥  Frel＜50%（相对湿度） 
3） 气候箱调节器转为水浴调节 
1 次循环 
冷凝阶段
干燥阶段
UB 时的参数测试（功
能测试）
实验室湿度 
水浴温度＜20℃ 
未定义的湿度曲线 
水浴温度±1K 
测试温度±3K 
**）实验室湿度和温度标记，温差＜15℃ 


### 第 62 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.15.2 K‐15b 带防水外壳的元件气候测试 
 
14.15.2.1 目的 
该测试是模拟元件在使用寿命期间采用防水外壳所经受湿热的负荷情况。 
测定元件在遭受湿热导致的缺陷如：腐蚀、迁移/枝状结构生长，塑料、密封件和浇注材料
的膨胀和退化时的质量及可靠性。 
 
14.15.2.2 测试 
必须用整个元件（带外壳的仪器、控制器、机电元件…）进行测试. 
 
测试顺序见图 42： 
 
图 42：K‐15b 带防水外壳的元件气候测试流程 
 
测试 1,3 和 5： 
测试标准见 DIN EN 60068‐2‐78,测试参数如下： 
 
表 81：K‐15b 带防水外壳的元件气候测试参数，测试 1,3 和 5 
样件运行方式 
运行方式Ⅱ.a 
测试开始后 12 小时进行一次参数测试（功能
测试）。，然后每 24 小时进行一次参数测试
（功能测试）。 
每个测试的测试时长 
见零部件设计任务书 
 
注意： 
K‐15b（测试 1 到 5）的总测试时长需与 K‐14
恒湿恒温—等级 2 的测试时长一致。 
其中测试 2 和测试 4 时长分别为 240 小时。
剩下的时长分摊到测试 1.3 和 5 上，各占三
分之一时间： 
测试时长 测 试 1=测试时长 测 试 3=测试时长 测 试
5=1/3(总测试时长—2*240 小时) 
测试温度 
65℃ 
测试湿度 
93%的相对湿度 
样件数量 
6 
测试 1 
 
恒湿恒温 
测试 2 
 
带冰冻的
湿热循环 
测试 3 
 
恒湿恒温 
测试 4 
 
带冰冻的
湿热循环 
测试 5 
 
恒湿恒温 


### 第 63 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,max 温度范围进行。超出冷却液温度范围的，只
有环境温度才能变化。 
 
测试 2 和 4： 
测试标准见 DIN EN 60068‐2‐38,测试参数如下： 
 
表 82：K‐15b 带防水外壳的元件气候测试参数，测试 2 和 4 
样件运行方式 
运行方式Ⅱ.a 
测试开始后 12 小时进行一次参数测试（功能
测试）。，然后每 24 小时在环境温度下进行一
次参数测试（功能测试）。 
每个测试的测试时长 
240h 
循环次数 
10 
测试顺序 
前 5 次循环需带有低温阶段，剩下的循环无
需低温。 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,min 和 Tkühl,max 温度范围进行。超出冷却液温度范
围的，只有环境温度才能变化。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 64 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.15.2.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，按照
章节 10.4.2 通过持续的参数监控进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 65 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.16 K‐16 温度冲击（不带外壳） 
 
14.16.1  目的 
该测试模拟的不是实际负荷情况。 
它是用来发现组件机械连接范围如焊接点处的缺陷。 
 
该测试仅能在不带外壳的组件及机械零件上进行。 
 
14.16.2  测试 
测试标准见 DIN EN 60068‐2‐14,参数如下： 
 
表 83：K‐16 温度冲击（不带外壳）测试参数 
样件运行方式 
运行方式Ⅰ.a 
最低温度 
Tmin                                                                        
最高温度 
Tmax 
高低温停留时间 
待温度稳定后 15min 
转移时间 
≤10s 
循环次数 
300 
样件数量 
6 个组件 
 
14.16.3  要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（大）验证。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 66 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.17 K‐17 日照 
 
14.17.1 目的 
该测试模拟太阳辐射和紫外线对元件的影响。 
用于测试元件抵抗材料疲劳损害的稳定性，如：裂纹和褪色。 
 
14.17.2 测试 
测试标准见 DIN 75220，参数如下： 
 
表 84：K‐17 日照测试参数 
样件运行方式 
运行方式Ⅰ.a 
使用的测试曲线 
测试曲线依据 DIN  75220 根据元件安装空间
进行应用                                                              
外舱元件 
曲线 Z‐Out 运用，见标准 DIN 75220 的表 2 和
表 5 
内舱元件 
曲线 Z‐IN1 运用，见标准 DIN 75220 
测试时间 
25 天（15 天干燥，10 天潮湿） 
循环次数 
1 
样件数量 
6 
 
14.17.3 要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（大）验证。 
 
此外还需用肉眼对样件进行外观检查，样件变化或损坏需注明在测试报告中，且与委托方一
起评估。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 67 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
14.18 K‐18 有害气体测试 
 
14.18.1 目的 
该测试是模拟有害气体对元件，尤其是对端子和开关的影响。 
用于测试元件耐缺陷形成的稳定性，如：腐蚀和元件损坏。 
 
14.18.2 测试 
测试标准见 DIN EN 60068‐2‐60 方法 4，参数如下： 
 
表 85：K‐18 有害气体测试参数 
样件运行方式 
运行方式Ⅰ.b 
温度 
TRT 
湿度 
75%的相对湿度 
有害气体浓度 
SO2 
0.2ppm 
H2S 
0.01ppm 
NO2 
0.2ppm 
Cl2 
0.01ppm 
测试时间 
21 天 
样件数量 
6 
 
14.18.3 要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（大）验证。 
 
此外必须测量开关和端子的接触电阻，测试结果必须在规定范围内。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 68 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
15  化学要求和测试 
 
15.1 C‐01  化学测试 
15.1.1 目的 
该测试是模拟元件对不同化学试剂的要求。 
用于测定元件的外壳受到化学影响和通过化学作用受到功能影响的稳定性。 
 
15.1.2 测试 
表 86：化学测试参数 
样件运行方式 
见零部件设计任务书 
化学试剂 
见零部件设计任务书 
常见化学试剂见表 87。 
温湿度处理 
如果没有其他特殊说明，样件和化学试剂放置在正常气候条件下 
测试过程 
测试标准见 ISO 16750 第 5 部分： 
1.化学试剂在样件达到 TRT 时进行应用，如果在元件设计任务书中没
有特殊说明，则必须按照表 88 选择一个适合的应用方式，并在测试
报告中记录选择的应用方式。必须确保样件被化学试剂完全覆盖。 
2.然后样件必须在表 87 给定的温度和规定的有效时间内进行存放。
样件数量 
每个化学试剂一个样件 
一个测试件用于不同试剂的多次使用必须与委托方确认 
 
必须注意化学试剂的安全和警告说明。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 69 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
15.1.2.1 化学试剂 
表 87：化学试剂一览表（也可参见 ISO 16750‐5） 
ID 
化学试剂 
样
件
温  度
有效时
间 
化学试剂举例 
1 
柴油 
Tmax 
22h 
EN 590 
2 
生态柴油 
Tmax 
22h 
EN 14214 
3 
无铅汽油 
TRT 
10min 
EN 228 
4 
煤油 
TRT 
10min 
ASTM 1655 
5 
甲醇 
TRT 
10min 
CAS 67‐56‐1 
6 
发动机机油 
Tmax 
22h 
多级通用机油 SAE 0W40,API SL/CF 
7 
差速器油 
Tmax 
22h 
双曲面齿轮油 SAE 75W140,API GL‐5 
8 
变速箱油 
Tmax 
22h 
ATF Dexron III  
9 
液压油 
Tmax 
22h 
DIN 51 524‐3（HVLP ISO VG 46） 
10 
润滑脂 
Tmax 
22h 
DIN 51 502（KP2K‐30） 
11 
硅油 
Tmax 
22h 
CAS 63148‐58‐3（AP 100） 
12 
蓄电池液 
TRT 
22h 
37% H2SO4 
13 
刹车液 
Tmax 
22h 
ISO 4926 
14 
防冻液 
Tmax 
22h 
乙烯乙二醇（C2H6O2）‐水（混合比例
1:1） 
15 
尿素 
Tmax 
22h 
ISO 22241‐1 
16 
空腔保护蜡 
TRT 
22h 
如：底盘保护，公司 Teroson1 
17 
防腐剂 
TRT 
22h 
如：W550（公司 Pfinder）1 
18 
去防腐剂 
Tmax 
22h 
如：Friapol750（公司 Pfinder）1 
19 
挡风玻璃清洗液 
TRT 
2h 
5%阴离子表面活性剂，蒸馏水 
20 
自动化的汽车清洗液 
TRT 
2h 
CAS 25155‐30‐0 
CAS 9004‐82‐4 
21 
内部清洗剂/驾驶舱喷雾剂 
TRT 
2h 
如：驾驶舱喷雾剂（公司 Motip）1 
22 
玻璃清洗剂 
TRT 
2h 
CAS 111‐76‐2 
23 
轮辋清洁剂 
TRT 
2h 
如：Xtreme（Sonax）1 
24 
冷清洗剂 
TRT 
22h 
如：P3‐Solvclean AK（公司 Henkel）1
25 
丙酮 
TRT 
10min 
CAS 67‐64‐1 
26 
清洗汽油 
TRT 
10min 
DIN 51635 
27 
含氨气的清洗剂 
TRT 
22h 
如：Ajax（公司 Henkel）1 
28 
提纯酒精 
TRT 
10min 
CAS 64‐17‐5（Ethanol） 
29 
接触喷雾剂 
Tmax 
22h 
如：WD 401 
30 
汗水 
TRT 
22h 
DIN 53160 
31 
化妆品如：润肤膏 
TRT 
22h 
如：Nivea，Kenzo1 
32 
含咖啡因和糖分的清凉饮料 
TRT 
22h 
Cola（可乐） 
33 
除霜剂（航空） 
TRT 
2h 
SAE AMS 1435A 
34 
E85 燃料 
TRT 
10min 
DIN 51625 
 
其他化学试剂 
 
 
 
 
1)生产商举例，具体化学试剂与专业部门确认。 
 


### 第 70 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
表 88：应用方式 
代码 
应用方法 
I  
喷雾 
II  
用毛刷涂 
III  
擦拭（如：用棉质擦布） 
IV  
浇注 
V  
短暂浸没 
VI 
浸没 
 
15.1.3 要求 
样件必须在测试前和测试后功能完好，并且所有的参数必须在规定范围内，按照章节 10.4
进行参数测试（大）验证。 
 
标签和标识如有变化需在测试报告中注明，并与委托方协商确认。   
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 71 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
16  耐久测试 
 
16.1 L‐01 机械/液压耐久测试 
 
16.1.1 目的 
该测试是模拟汽车使用寿命内功能/操作循环。 
用于测试元件功能操作循环的质量和可靠性，如：刹车操作，座椅调节循环，开关/按键操
作等等。 
 
16.1.2 测试 
测试详情必须符合零部件设计任务书中定义的功能/操作循环。 
表 89：L‐01 机械/液压耐久测试参数 
样件运行方式 
运行方式Ⅱ.c，符合功能/操作循环 
测试温度 
功能/操作循环就是在温度集中规定的温度下，时间按照它们
的百分比执行 
功能/操作循环次数 
参照设计任务书中规定 
样件数量 
6 
 
如果有冷却液管路，则冷却液温度按照 Tkühl,min 和 Tkühl,max 温度范围进行。超出冷却液温度范
围的，只有环境温度才能变化。 
 
11.1.3 要求 
样件必须在测试前、测试期间和测试后功能完好，并且所有的参数必须在规定范围内，必须
通过持续的参数监控进行验证。 
只有测试过程中的无法完整对元件功能进行监测时，才要在测试时长的 25%、50%和 75%时
再测量并根据测试流程计划进行参数测试。 
 
中间测试必须按照参数测试（大）进行验证。 
 
持续参数监控的数据必须关于就迁移、趋势和突出特性或异常进行评估。 
 
针对冷却液管路的元件： 
有铜镀层的元件，在测试结束后要对这些铜件用 20 倍放大的显微镜进行观察，不允许有缺
陷和铜腐蚀产生。 
 
 
 
 
 
 
 
 
 
 


### 第 72 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
16.2 L‐02 高温耐久测试 
 
16.2.1 目的 
该测试模拟车辆使用寿命内对元件热负荷的要求。 
用于测试元件在热作用下耐缺陷的质量和可靠性，如：扩散，迁移和氧化。 
 
16.2.2 测试 
 
16.2.2.1  不和冷却液管路连接的元件且在高温下性能不会降低的元件测试 
测试标准见 DIN EN 60068‐2‐2，参数如下： 
表 90：L‐02 高温耐久测试参数—不和冷却液管路连接的元件且在高温下性能不会降低的元
件测试 
样件运行方式 
运行方式Ⅱ.c：47h,运行方式Ⅱ.a:1h（间歇性） 
测试时间 
章节 10.1 中的相关运行模式：按照附录 C.1（Arrhenius 模式）计
算测试各部分测试时间；停车或离网停车运行模式一般不用考虑。
各部分测试时间的总和即是总测试时长，并规定在设计任务书中
测试温度 
Tmax 
样件数量 
6 
 
16.2.2.2 不和冷却液管路连接的元件但在高温下性能会降低的元件测试 
从高温时 Top,max 起性能降低的零部件（如 LCD 背光照明降低）：表 91 中的测试不是按照 Tmax
测试温度，而是按照下列温度参数实施： 
 
测试方法见标准 DIN EN 60068‐2‐2,参数如下： 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 73 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
表 91：L‐02 高温耐久测试参数—不和冷却液管路连接的元件但在高温下性能会降低的元件
测试 
 
样件运行方式 
见图 43 
测试时间 
章节 10.1 中的相关运行模式：按照附录 C.3(高温下性能降低的
Arrhenius‐模型应用)计算测试各部分测试时间；停车或离网停车
运行模式一般不用考虑。 
各部分测试时间的总和即是总测试时长，并规定在设计任务书中
Tmax 和 Top,max 之间的各自的爬坡时间不计算在测试时间内。 
测试温度 
见图 43 
间隔时间 t1 
按照附录 C.3 进行计算，并规定在设计任务书中。 
间隔时间 t2 
按照附录 C.3 进行计算，并规定在设计任务书中。 
样件数量 
6 
 
 
*）当 T＞Top,max 时，允许性能降低。 
图 43：高温时性能降低的元件温度测试曲线 
 
 
 
 
 
 
 
 
 
 


### 第 74 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
16.2.2.3  连接到冷却液管路的元件测试 
测试方法见标准 DIN EN 60068‐2‐2,参数如下： 
 
表 92：L‐02 高温耐久测试参数—和冷却液管路连接的元件测试 
 
样件运行方式 
运行方式Ⅱ.c：47h,运行方式Ⅱ.a:1h（间歇性） 
测试时间 
章节 10.1 中的相关运行模式：按照附录 C.5(冷却液管路元件的
Arrhenius‐模型应用)计算测试各部分测试时间；停车或离网停车
运行模式一般不用考虑。 
各部分测试时间的总和即是总测试时长，并规定在设计任务书中
环境测试温度 
按照附录 C.5（冷却液管路元件的 Arrhenius‐模型应用） 
冷却液测试温度 
按照附录 C.5（冷却液管路元件的 Arrhenius‐模型应用） 
样件数量 
6 
 
16.2.3 要求 
测试件必须在测试前、测试中和测试后都是功能齐全的，并且所有的关键参数必须在规定范
围内，通过持续的参数监控进行确认。 
只有在测试过程中无法对元件功能进行完整监测时，才要在中间测试时间和参数测试的 25%，
50%和 75%时再测量。 
 
中间测量必须进行参数测试（大）验证。 
 
持续参数监控的数据必须关于漂移、趋势和突出特性或异常进行评估。 
 
冷却液管路上的元件： 
冷却液管路上含有铜件覆盖的元件，再测试结束后用 20 倍放大显微镜对铜件进行观察，不
允许出现明显缺陷和铜腐蚀。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 75 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
16.3 L‐03 温度变化耐久测试 
 
16.3.1 目的 
该测试通过温度变化测试模拟车辆使用寿命内对元件的热负荷要求。 
用来测试元件在热负荷作用下抑制缺陷形成的质量和可靠性，如：焊接、粘接、压焊、焊接
连接部位，密封件和护套的老化和裂纹。 
 
16.3.2 测试 
测试标准见 DIN EN 60068‐2‐14，参数如下： 
16.3.2.1 不和冷却液管路连接的元件且在高低温下性能不会降低的元件测试 
 
表 93：L‐03 温度变化耐久测试参数—不和冷却液管路连接的元件且在高低温下性能不会降
低的元件测试 
样件运行方式 
按照图 44：运行方式Ⅱ.a 和运行方式Ⅱ.c（间歇执行） 
温度曲线 
按照图 44 
最小测试温度 
Tmin 
最大测试温度 
Tmax 
温度梯度 
4℃/min 
如果这个温度梯度在测试仪中无法实现，则温度梯度可以与委托方
协商，将数值降低至最小 2℃/min。 
Tmin 和 Tmax 停留时间 
待温度完全稳定后 15min 
循环次数 
总测试循环次数需考虑相关运行模式（章节 10.1），按照附录
D.1(Coffin‐Manson 模型)进行计算，且规定在零部件设计任务书中。
样件数量 
6 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 76 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
图 44：L‐03 温度变化耐久测试温度曲线—不和冷却液管路连接的元件且在高低温下性能不
会降低的元件测试 
 
16.3.2.2 不和冷却液管路连接的元件但在高低温下性能会降低的元件测试 
Top,min 以下温度和 Top,max 以上温度起性能降低的零部件（如 LCD 背光照明降低），测试参数如
下： 
 
表格 94：L‐03 温度变化耐久测试参数  –不和冷却液管路连接的元件但在高低温下性能会降
低的元件测试 
样件运行方式 
依据图 45 运行方式Ⅱ.a 和Ⅱ.c 
温度曲线 
依据图 45 
最小测试温度 
Tmin 
最大测试温度 
Tmax 
温度梯度 
4℃/min 
Tmin，Tmax，Top,min 和 Top,max 停留时间
待温度完全稳定后 15min 
循环次数 
总测试循环次数需考虑相关运行模式（章节 10.1），
按照附录 D.1(Coffin‐Manson 模型)进行计算，且规定
在零部件设计任务书中。 
样件数量 
6 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 77 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
图 45：温度曲线—低温或者高温下性能降低的元件测试 
 
16.3.2.3 冷却液管路元件 
冷却液管路元件测试参数如下： 
 
表 95：L‐03 温度变化耐久测试—冷却液管路元件测试 
样件运行方式 
依据图 44 和 45 运行方式Ⅱ.a 和Ⅱ.c 
温度曲线 
依据图 44 和 45 
最小测试温度 
Tmin 和 Tkühl,min 
最大测试温度 
Tmax 和 Tkühl,max 
温度梯度 
4℃/min 
Tmin，Tmax，Top,min 和 Top,max 停留时间
待温度完全稳定后 15min 
循环次数 
总测试循环次数需考虑相关运行模式（章节 10.1），
按照附录 D.3(冷却液管路元件的 Coffin‐Manson‐模型
应用)进行计算，且规定在零部件设计任务书中。 
样件数量 
6 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
只有当 Top,min＞Tmin 才需要 
只有当 Top,max＜Tmax 才需要 


### 第 78 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
16.3.3  要求 
测试件必须在测试前、测试中和测试后都是功能齐全的，并且所有的关键参数必须在规定范
围内，通过持续的参数监控进行确认。 
只有在测试过程中无法对元件功能进行完整监测时，才要在中间测试时间和参数测试的 25%，
50%和 75%时再测量。 
 
中间测量必须进行参数测试（大）验证。 
 
持续参数监控的数据必须关于漂移、趋势和突出特性或异常进行评估。 
 
冷却液管路上的元件： 
冷却液管路上含有铜件覆盖的元件，再测试结束后用 20 倍放大显微镜对铜件进行观察，不
允许出现明显缺陷和铜腐蚀。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 79 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
附录 A(标准) 
测试流程 
A.1  测试流程计划 
测试流程见零部件设计任务书。 
 
测试选择表中不必要的测试需从测试流程计划中删掉。 
 
如有必要对测试顺序进行调整，则可以修改测试流程计划。 
 
如果在顺序测试中用循环湿热测试（带霜冻）代替湿热测试，则在平行测试中可以取消湿热
循环测试（带霜冻）。 
 
所有元件从测试 M‐01”自由跌落测试”起用原始插件或适配器进行测试。 


### 第 80 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
K‐01 高温/低温老化 
K‐10 防水保护
IPX0 至 IPX6K 
K‐06 运行时盐雾测试，外舱 
M‐03 粉尘测试（IP5KX 和 IP6KX） 
参数测试（小）
参数测试（小） 
K‐08 湿热循环 
（增加 2 个备用件用于跌落测试） 
K‐02 温度梯级测试 
2 备用 
M‐01 跌落测试 
参数测试（大） 
梯级温度测试（最终测试）
固定 6 个测试件（备用件也可以） 
参数测试（大） 
参数测试（小） 
参数测试（大） 
参数测试（小） 
M‐06 机械耐久冲击 
M‐05 机械冲击 
参数测试（大） 
参数测试（小） 
K‐04 重新喷漆温度 
参数测试（小） 
K‐03 低温运行 
K‐05 温度冲击（元件） 
M‐02 石击测试 
M‐04 振动测试 
参数测试（大） 
参数测试（大） 
物理分析 
K‐13 温度冲击浸入（盖板 IPX7） 
参数测试（小） 
跌落测试 
参数测试（小）
K‐12 带溅水的温度冲击 
参数测试（小） 
K‐11 高压喷射测试（IPX9K） 
参数测试（小） 
K‐02 梯级温度测试 


### 第 81 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
箭头中的数字表示样件数量。 
图 46：测试流程‐顺序流程 
 
 
如果测试 M‐01“跌落测试”中样件没有受损，则将其中 2 个样件应用到接下来的顺序测试
中。其他情况必须使用备用样件。 
 
如果在顺序测试中用循环湿热测试（带霜冻）代替湿热测试，则在平行测试中可以取消湿热
循环测试（带霜冻）。 
 
所有元件从测试 M‐01”自由跌落测试”起用原始插件或适配器进行测试。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 82 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
A.3 顺序外的测试（平行测试） 
 
 
箭头中的数字表示样件数量。 
图 47：测试流程计划—平行测试 
 
 
 
 
 
 
 
 
 
 
 
 
 
K‐01  高低温老化
环境测试参数测试（大） 
气密测试 
K‐07 运行时内舱盐雾测试
K‐09 湿热循环（带霜冻）
K‐14 恒湿恒温
K‐15 冷凝测试（无外壳）
K‐16 温度冲击（无外壳）
K‐17 日照测试
K‐18 有害气体测试
C‐01 化学测试
环境测试参数测试（大）
气密测试
物理分析 
*）一个化学试剂对应一个样件 
用于多种化学试剂的样件需与
委托方协商确定 


### 第 83 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
A.4 耐久测试 
 
箭头中的数字表示样件数量。 
图 48：测试流程计划‐耐久测试 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
K‐01 高低温老化 
环境测试参数测试（大） 
气密测试 
K‐02  温度梯级测试 
L1  机械液压耐久测试 
L2  高温耐久测试
L3 温度变化耐久测试 
参数测试 
K‐02  温度梯级测试 
环境测试参数测试（大） 
气密测试 
物理分析 


### 第 84 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
附录 B(标准) 
不同安装区域的常见温度集 
 
表 96：安装区域、常见温度集和温度升程一览表 
元件安装区 
温度集 No. 
温度升程 k 
车内（内舱），没有特殊要求 
1 
36 
白车身，没有特殊要求 
1 
36 
车内（内舱），有日照 
2 
46 
车顶 
2 
46 
发动机舱，但不在发动机上 
3 
60 
冷却风扇上 
3 
60 
发动机安装 
4 
75 
变速箱安装 
4 
75 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 85 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
B.1 温度集 1 
表 97：温度集 1 
温度 
分配百分比 
‐40℃ 
6% 
23℃ 
20% 
40℃ 
65% 
75℃ 
8% 
80℃ 
1% 
B.2 温度集 2 
表 98：温度集 2 
温度 
分配百分比 
‐40℃ 
6% 
23℃ 
20% 
50℃ 
65% 
100℃ 
8% 
105℃ 
1% 
B.3 温度集 3 
表 99：温度集 3 
温度 
分配百分比 
‐40℃ 
6% 
23℃ 
20% 
65℃ 
65% 
115℃ 
8% 
120℃ 
1% 
B.4 温度集 4 
表 100：温度集 4 
温度 
分配百分比 
‐40℃ 
6% 
23℃ 
20% 
85℃ 
65% 
135℃ 
8% 
140℃ 
1% 
 
 
 
 
 
 
 
 
 
 
 


### 第 86 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
附录 C(标准) 
高温耐久测试计算模型 
C.1 Arrhenius 模型 
针对高温耐久测试时长的计算：温度集百分比是依据设计任务书中的使用特征。 
表 101：温度集 
温度 
分配百分比 
TFeld.1 
P1 
TFeld.2 
P2 
… 
… 
TFeld.n 
Pn 
工作时间 tBetrieb 也参见设计任务书。 
每个温度 TFeld.1…TFeld.n 使用下面的公式加速系数 AT,1…AT,n 进行计算： 
 
其中： 
AT,I    Arrhenius 模型加速系数 
EA    活化能 EA=0.45eV 
K      波茨曼常数（k=8.617*10‐5eV/K） 
Ttest  测试温度（℃），一般是 Tmax 
TFeld,i    依据使用曲线的温度集区域温度（℃） 
‐273.15℃    绝对温度 0 度 
高温耐久测试的总测试时长是通过下面的加速系数得出： 
 
其中： 
tPruf      高温耐久测试的测试时间（小时） 
tBetrieb      区域运行时间（小时） 
Pi      运行时间百分比，元件在区域以温度 TFeld,i 运行 
AT,i      温度 TFeld,i 加速系数 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 87 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
C.2 Arrhenius 模型举例 
控制器温度集见下表 
表 102：温度集举例 
温度（℃） 
分配百分比 
‐40 
6 
23 
20 
60 
65 
100 
8 
105 
1 
运行时间为 8000h 的高温耐久测试时间计算如下： 
 
通过等式（1）和 Ttest=Tmax=105℃上述温度集的所有 5 个温度（见表 102）的加速系数 AT,i
计算结果如下： 
 
 
元件运行时间为：tBetrieb=8000h 
 
高温耐久测试总测试时间计算见等式（2）： 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 88 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
C.3 用于高温下性能降低的元件 Arrhenius 模型 
Top,max 以内高温下性能降低的元件的高温耐久测试时间计算的温度集依据设计任务书中的
使用特征，在温度范围 T≤Top,max 和 T＞Top,max 进行分配： 
 
表 103：T≤Top,max 测试温度 Top,max 的温度集 
 
 
表 104：Top,max＜T≤Tmax 测试温度 Tmax 的温度集 
 
每个温度 TFeld,1…TFeld,m…TFeld,n 使用等式（1）的加速因数 AT,1…AT,m…AT,n 进行计算，其中测试温
度范围 T≤Top,max:Ttest=Top,max，温度范围 T＞Top,max 的，测试温度为 Ttest=Tmax 
 
测试温度为 Top,max 的测试时间 top,max 依据等式（2），i=1…m 
 
测试温度为 Tmax 的测试时间 tmax 依据等式（2），i=m+1…n 
 
总测试时间 tGes 是 top,max 和 tmax 的总和。 
 
接近实际的测试是按照间歇性的测试温度为 Top,max 或者 Tmax（见图 43） 
零件测试时间 top,max 与 tmax 间隔为 48h，按照比例进行分配。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 89 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
C.4 用于高温下性能降低的元件 Arrhenius 模型举例 
控制器适用的温度集依据表格 105 和 106，高温性能降低且最高温度小于 top,max=90℃的元件
在运行时间 8000h 的高温耐久测试时间按照如下计算： 
温度分配的百分比依据使用特征，分为下面 T≤Top,max 和 T＞Top,max 两个范围： 
 
表 105：T≤90℃的温度集举例 
温度（℃） 
分配 
‐40 
6 
23 
20 
60 
65 
 
表 106：T＞90℃的温度集举例 
温度（℃） 
分配 
100 
8 
105 
1 
使用等式（1）和 Ttest=90℃计算温度集首件温度 T≤90℃的（见表 105）的加速因数： 
 
 
从中可以计算出测试温度 Top,max=90℃的测试时间 top,max： 
 
使用等式（1）和 Ttest=105℃计算温度集第二个零件温度 T＞90℃的（见表 106）的加速因数： 
 
由此得出测试温度 Tmax=105℃的测试时间 tmax： 
 
高温耐久测试的总测试时间为两个测试时间之和： 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 90 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
按照图 43 间歇性的使用测试温度 Top,max 或者 Tmax 并采用下面的间隔时间： 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 91 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
C.5  冷却液管路上零件应用的 Arrhenius 模型 
与冷却液管路连接的元件：必须考虑环境和冷却液相关温度分配的所有相关运行模式 i(见图
22，i 代表运行模式编号)。 
每个相关运行模式 i 的高温耐久测试：环境和冷却液测试时间和测试温度都要计算，计算公
式如下。总测试时间等于各自相关运行模式 i 的测试时间的总和。 
 
每个相关运行模式 i 的测试时间计算时首先计算环境温度的测试时间，然后计算冷却液温度
的测试时间，计算方式按照 C.1 甚至 C.3 的 Arrhenius 模型。 
得出的测试时间 tprüf,umgebung 和 tprüf,KKL 一般是不一样的，每个运行模式 i 的元件测试时间都要
统一。环境温度和冷却液管路之间的测试时间也要一致。 
 
通过下面的迭代法可以将测试时间 tprüf,umgebung 和 tprüf,KKL 延长，将测试分成 2 部分，除一段测
试外，其他段测试温度都要降低。 
 
案例 A: tprüf,umgebung＜tprüf,KKL 
 
测试时间： 
运行模式 i 的测试时间计算公式： 
 
冷却液测试温度： 
按照附录 C.1Arrhenius 模型选择测试温度（一般为 Tkühl,max） 
环境温度的测试温度： 
测试温度按照下列演算法（在环境温度集基础上）进行计算，（见表 107）： 
 
表 107：环境温度集 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 92 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
1. 迭代开始（m=0）: 
第一个测试部分测试温度需为 TFeld,n,分段测试时间 tprüf,T_Feld,n=tBetrieb*pn(其中运行时间
tBetrieb 单位为 h) 
 
2. 首次迭代（m=1）: 
通过第 1 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，因此通过更多测试可
以得出剩下的测试时间： 
 
通过第 1 段测试还可以得出环境温度分配百分比 pn.Pn 用于后面的计算时必须调整为
pn=0. 
为确定第二段测试的测试温度（m=1）,首先按照附录 C.1 或 C.3Arrhenius 模型计算
Tangepasst(调整后)的测试温度，这样可以得出环境温度分配（pn=0 调整后的）剩下的测试时间
tRest,1. 
当计算得出的调整后的测试温度 Tangepasst(调整后)≤TFeld,n‐1 时，则第 2 段测试要在测试温度
TFeld,n‐1 进行，测试时间公式： 
 
至少要再进行一次迭代步骤。 
如果计算得出的调整后的测试温度 Tangepasst(调整后)＞TFeld,n‐1 时，则第 2 段测试要在测试温度
Tangepasst(调整后)进行，测试时间公式： 
 
这就不用再进行迭代步骤了。 
 
3. 继续迭代（m=2,3...） 
4. 通过首次 m 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，因此通过更多测试
可以得出剩下的测试时间： 
 
通过首批 m 段测试还可以得出环境温度分配百分比 pn‐k,k=0,1,...(m‐1).Pn‐k 用于后面的计
算时必须调整为 pn‐k=0. 
为确定(m+1)段测试的测试温度,首先按照附录 C.1 或 C.3Arrhenius 模型计算 Tangepasst(调整后)
的测试温度，这样可以得出环境温度分配（pn‐k=0 调整后的）剩下的测试时间 tRest,m. 
当计算得出的调整后的测试温度 Tangepasst(调整后),≤TFeld,n‐m 时，则第 m+1 段测试要在测试温
度 TFeld,n‐m 进行，测试时间公式： 
 
 
 
 
 


### 第 93 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
至少要再进行一次迭代步骤。 
如果计算得出的调整后的测试温度 Tangepasst(调整后)＞TFeld,n‐m 时，则第 m+1 段测试要在测试
温度 Tangepasst(调整后)进行，测试时间公式： 
 
这就不用再进行迭代步骤了(迭代结束)。 
 
案例 B: tprüf,umgebung＞tprüf,KKL 
测试时间： 
运行模式 i 的测试时间计算公式： 
 
 
环境温度的测试温度： 
按照附录 C.1 或 C.3Arrhenius 模型选择测试温度（一般为 Tmax 或 Tmax 和 Top,max） 
 
冷却液的测试温度： 
测试温度按照下列演算法（在环境温度集基础上）进行计算，（见表 108）： 
 
表 108：冷却液温度集 
 
1. 迭代开始（m=0）: 
第一个测试部分测试温度需为 TFeld,n,分段测试时间 tprüf,T_Feld,n=tBetrieb*pn(其中运行时间
tBetrieb 单位为 h) 
 
2. 首次迭代（m=1）: 
通过第 1 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，因此通过更多测试可
以得出剩下的测试时间： 
 
通过第 1 段测试还可以得出冷却液温度分配百分比 pn.Pn 用于后面的计算时必须调整为
pn=0. 
为确定第二段测试的测试温度（m=1）,首先按照附录 C.1 Arrhenius 模型计算 Tangepasst(调整
后)的测试温度，这样可以得出冷却液温度分配（pn=0 调整后的）剩下的测试时间 tRest,1. 
当计算得出的调整后的测试温度 Tangepasst(调整后)≤TFeld,n‐1 时，则第 2 段测试要在测试温度
TFeld,n‐1 进行，测试时间公式： 


### 第 94 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
至少要再进行一次迭代步骤。 
如果计算得出的调整后的测试温度 Tangepasst(调整后)＞TFeld,n‐1 时，则第 2 段测试要在测试温度
Tangepasst(调整后)进行，测试时间公式： 
 
这就不用再进行迭代步骤了（迭代结束）。 
 
3. 继续迭代（m=2,3...） 
4. 通过首次 m 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，因此通过更多测试
可以得出剩下的测试时间： 
 
通过首批 m 段测试还可以得出冷却液温度分配百分比 pn‐k,k=0,1,...(m‐1).Pn‐k 用于后面的
计算时必须调整为 pn‐k=0. 
为确定(m+1)段测试的测试温度,首先按照附录 C.1Arrhenius 模型计算 Tangepasst(调整后)的测试
温度，这样可以得出冷却液温度分配（pn‐k=0 调整后的）剩下的测试时间 tRest,m. 
当计算得出的调整后的测试温度 Tangepasst(调整后),≤TFeld,n‐m 时，则第 m+1 段测试要在测试温
度 TFeld,n‐m 进行，测试时间公式： 
 
 
至少要再进行一次迭代步骤。 
如果计算得出的调整后的测试温度 Tangepasst(调整后)＞TFeld,n‐m 时，则第 m+1 段测试要在测试
温度 Tangepasst(调整后)进行，测试时间公式： 
 
这就不用再进行迭代步骤了(迭代结束)。 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 95 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
C.6 冷却液管路上零件应用的 Arrhenius 模型举例 
与冷却液管路连接的控制器：环境温度和冷却液温度集见下表： 
 
表 109：环境温度集举例 
 
表 110：冷却液温度集 
 
工作时长为 8000h，使用寿命测试和高温耐久性测试的时间计算如下： 
 
测试时间： 
Arrhenius 模型环境温度和冷却液温度测试时间计算： 
tprüf,umgebung=1143h 
tprüf,KKL=2009h 
由于 tprüf,umgebung＜tprüf,KKL，则按照 C.5 中的案例 A 进行计算。环境温度的测试时间必须调整
为 tprüf,Mode,i= tprüf,KKL=2009h. 
 
冷却液测试温度： 
根据温度集冷却液测试温度 TKKL,max=TFeld,5=80℃ 
 
环境温度的测试温度的迭代计算： 
1.迭代开始： 
第一部分测试 TFeld,5=105℃，测试时间 tprüf,T_Feld,5=tBetrieb*p5=8000h*1%=80h。 
 
2.首次迭代： 
通过第 1 段测试可以得出一部分测试时间（运行模式 i）tprüf,Mode i，其余的测试时间必须重新
计算：tRest,1=tprüf.Mode,i‐tprüf,T_Feld,5=2009h‐80h=1929h. 


### 第 96 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
通过第 1 段测试还可以得出温度分配百分比 p5.P5 用于后面的计算时必须调整为 p5=0. 
 
表 111：第 1 段测试后环境温度集调整 
 
然后确定第二段测试的测试温度，必须按照 Arrehnius 模型 C.1 计算调整后的测试温度
Tangepasst 可以得出剩下的测试时间 tRest,1=1929h。考虑到环境温度调整后的温度分配情况，得
出在 Tangepasst=89.5℃（精确值：89.46℃）时的测试时间必须为 1929h. 
由于 Tangepasst＜TFeld,4(89.5℃＜100℃），则第 2 段测试必须在 TFeld,4=100℃时进行。 
第 2 段测试时间 tprüf,T_Feld,4=tBetrieb*p4=8000h*8%=640h. 
 
3.第 2 次迭代： 
通过第 2 段测试可以计算出模式 i 时的其他测试时间 tprüf.Mode,i,剩下的测试时间计算： 
 
通过第 2 段测试还可以得出环境温度分配百分比 p5和 p4.P5和 p4用于后面的计算时必须调整
为 p5=p4=0. 
表 112：第 1 段和第 2 段测试后环境温度集调整 
 
然后确定第三段测试的测试温度，必须按照 Arrehnius 模型 C.1 计算调整后的测试温度
Tangepasst 可以得出剩下的测试时间 tRest,2=1289h。考虑到环境温度调整后的温度分配情况，得
出在 Tangepasst=82℃（精确值：82.17℃）时的测试时间必须为 1289h. 
由于 Tangepasst＞TFeld,3(82℃＞50℃），则无需再进行迭代。 
 
 
 


### 第 97 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
第 3 段和最后一段测试在 Tangepasst=82℃时的测试时间 tprüf,T_Feld,3=tBetrieb*p3=1289h 
 
 
在环境温度 105℃时测试 80 小时，环境温度 100℃时测试 640 小时，以及在环境温度 82℃
时测试 1289 小时。此案例中的冷却液温度整个测试期间维持在恒定 80℃。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 98 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
附录 D(标准) 
温度变化耐久测试计算模型 
D.1 Coffin‐Manson‐模型 
计算温度变化的耐久测试时间需要给出元件在△TFeld 区域（参见表 96）的平均温度变化以及
区域的温度耐久测试的循环次数 NTempZyklenFeld。 
 
每天 2 次温度变化的温度循环次数可以从下列公式推断出： 
NTempZyklenFeld=2*365*15（年）=10950 次循环 
与区域的平均温度变化有关，Coffin‐Manson‐模型的加速因数计算如下： 
 
其中： 
ACM                        Coffin‐Manson‐模型的加速因数 
△TTest                    一个测试循环期间的温度差（△TTest=Tmax‐Tmin） 
△TFeld                    区域耐久测试期间的平均温度差 
C                            Coffin‐Manson‐模型的参数 
                              在此标准中，规定 c 为固定值 2.5 
 
测试循环的总次数计算如下： 
 
其中： 
NPruf                  要求的循环次数 
NTempZyklenFeld      区域耐久测试期间的温度循环次数 
ACM                    Coffin‐Manson‐模型的加速因数，见等式（3） 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 99 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
D.2 举例 
Tmin=‐40℃和Tmax=105℃的控制器，区域内使用周期为15年，区域内平均温度差为△TFeld=40℃，
测试循环（NPruf）次数计算如下： 
1. 区域温度循环次数： 
NTempZyklenFeld =2*365*15（年）=10950 次循环 
2. 一次测试循环的温度差： 
△TTest=105℃‐（‐40℃）=145℃ 
3. 按照等式（3）计算 Coffin‐Manson‐模型的加速因数 ACM=25.02 
4. 按照等式（4）计算循环测试次数： 
 
5. 停留时间 tHaltezeit 等于元件温度恒定所需的时间再加上 15min。假设元件 20 分钟温度恒
定，那么停留时间就是 35min。 
6. 因此循环一次的时间是： 
 
7. 举例： 
 
8. 438 次循环的总测试时间是 1040h。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 100 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
D.3 冷却液循环系统元件的 Coffin‐Manson‐模型应用 
与冷却液管路连接的元件：必须考虑环境和冷却液相关温度分配的所有相关运行模式 i(见图
22，i 代表运行模式编号)。 
每个相关运行模式 i 的高温耐久测试：环境和冷却液最高温度和测试循环次数都要计算，计
算公式如下。总测试循环次数等于各自相关运行模式 i 的测试循环次数的总和。 
 
每个相关运行模式 i 的测试循环次数计算时首先计算环境温度的测试循环次数，然后计算冷
却液温度的测试循环次数，计算方式按照附录 C.7 的 Coffin‐Manson 模型。 
得出的测试循环次数 Nprüf,umgebung 和 Nprüf,KKL 一般是不一样的，每个运行模式 i 的元件测试循
环次数都要统一。环境温度和冷却液管路之间的测试循环次数也要一致。 
 
通过下面的迭代法可以将测试循环次数 Nprüf,umgebung 和 Nprüf,KKL 延长，将测试分成 3 部分，其
中一段测试温度在 Tmax 和 Tmin 之间进行，其他段测试温度冲程都要降低，在 Tmin 和 TRT 之间
甚至在 TRT 和 Tmax 之间进行。 
 
案例 A: Nprüf,umgebung＞Nprüf,KKL 
测试循环次数： 
运行模式 i 下的测试循环次数 Nprüf,Mode i=Nprüf,umgebung 
 
冷却液测试循环次数： 
冷却液的测试循环次数 Nprüf,KKL 必须调整到和环境测试循环次数 Nprüf,umgebung（比冷却液次数
多）一致，然后按照下面三个温度范围进行测试循环： 
 
1. XKKL 测试循环必须在 TKKL,min 和 TKKL,max 之间进行。根据 Coffin‐Manson 模型计算加速因数
ACM,KKL,1:△TTest,1=TKKL，max‐TKKL,min 
2. 1/2*(Nprüf,Mode i‐XKKL)测试循环必须在 TKKL,min 和 TRT 之间进行。 
根据 Coffin‐Manson 模型计算加速因数 ACM,KKL,2:△TTest,2=TRT‐TKKL,min 
3. 1/2*(Nprüf,Mode i‐XKKL)测试循环必须在 TRT 和 TKKL,max 之间进行。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 101 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
根据 Coffin‐Manson 模型计算加速因数 ACM,KKL,3:△TTest,3=TKKL,max‐TRT 
 
1 至 3 的总和即为温度循环总次数 Nprüf,Mode i。 
 
根据附录 D.1 中的等式 4 可以得出： 
 
测试循环次数 XKKL 计算如下： 
 
将 XKKL 值代入上述 1‐3 处可以计算出三段测试的循环次数。 
 
如果 TKKL,op,max＜TKKL,max,或者 TKKL,op,min＞TKKL,min 或者 TUmgebung,op,max＜TUmgebung,max ，或者
TUmgebung,op,min＞TUmgebung,min,则还需考虑相应温度（章节 16.3.2.1 中图 45）增加停留时间。 
测试过程中环境温度和冷却液管路的温度变化循环测试同步进行。 
 
案例 B: Nprüf,umgebung＜Nprüf,KKL 
测试循环次数： 
运行模式 i 下的测试循环次数 Nprüf,Mode i=Nprüf,KKL 
 
环境测试循环次数： 
环境的测试循环次数 Nprüf,Umgebung 必须调整到和冷却液测试循环次数 Nprüf,KKL（比环境测试次
数多）一致，然后按照下面三个温度范围进行测试循环： 
 
1. XUmgebung 测试循环必须在 TUmgebung,min 和 TUmgebung,max 之间进行。根据 Coffin‐Manson 模型
计算加速因数 ACM,Umgebung,1:△TTest,1=T Umgebung，max‐T Umgebung,min 
2. 1/2*(Nprüf,Mode i‐X Umgebung)测试循环必须在 T Umgebung,min 和 TRT 之间进行。 
根据 Coffin‐Manson 模型计算加速因数 ACM, Umgebung,2:△TTest,2=TRT‐T Umgebung,min 
 
 
 
 
 
 
 
 
 
 
 


### 第 102 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
3. 1/2*(Nprüf,Mode i‐X Umgebung)测试循环必须在 TRT 和 T Umgebung,max 之间进行。 
根据 Coffin‐Manson 模型计算加速因数 ACM, Umgebung,3:△TTest,3=T Umgebung max‐TRT 
 
1 至 3 的总和即为温度循环总次数 Nprüf,Mode i。 
 
根据附录 D.1 中的等式 4 可以得出： 
 
Xumgebung 测试循环次数计算如下： 
 
将 XKKL 值代入上述 1‐3 处可以计算出三段测试的循环次数。 
 
如果 T Umgebung op,max＜T Umgebung,max,或者 T Umgebung,op,min＞T Umgebung,min 或者 TKKL,op,max＜TKKL,max，或
者 TKKL,op,min＞TKKL,min,则还需考虑相应温度（章节 16.3.2.1 中图 45）增加停留时间。 
测试过程中环境温度和冷却液管路的温度变化循环测试同步进行。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 103 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
D.4 冷却液循环系统元件的 Coffin‐Manson‐模型应用举例 
一个与冷却液管路连接的控制器，环境温度范围 TUmgebung,min=‐40℃，TUmgebung,max=120℃，冷
却液温度范围 TKKL,min=‐40℃，TKKL,max=80℃，使用寿命 15 年，环境平均温差△TFeld,Umgebung=60K
以及冷却液的平均温差△TFeld,KKL=36K，运行模式 i 下的测试循环次数计算如下： 
 
环境和冷却液的测试循环次数： 
环境和冷却液的测试循环次数按照附录 D.1Coffin‐Manson 模型计算，得出结果如下： 
NPrüf.Umgebung=943 次循环 
NPrüf.KKL=540 次循环 
由于 NPrüf.Umgebung＞NPrüf.KKL，运行模式 i 的测试循环次数 N Prüf.Mode i=NPrüf.Umgebung=943 次循环，
冷却液的循环次数也要相应调整。 
 
冷却液测试循环次数调整 
冷却液循环次数调整到 N Prüf.Mode i=943 次循环，分成 3 部分： 
1.    XKKL 测试循环必须在 TKKL,min=‐40℃和 TKKL,max=80℃之间进行。根据 Coffin‐Manson 模型计
算加速因数
 
2.    1/2*(943‐XKKL)测试循环必须在 TKKL,min=‐40℃和 TRT=23℃之间进行。 
根
据
Coffin‐Manson
模
型
计
算
加
速
因
数
 
3.1/2*(943‐XKKL)测试循环必须在 TRT=23℃和 TKKL,max=80℃之间进行。根据 Coffin‐Manson 模型
计算加速因数
 
由此得出 XKKL： 
 
 
 
 
 
 
 
 
 
 


### 第 104 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
1 至 3 处的 3 个温度范围的测试循环次数计算如下： 
1. TKKL,min=‐40℃和 TKKL,max=80℃之间必须进行 453 次循环。 
2. TKKL,min=‐40℃和 TRT=23℃之间必须进行 245 次循环。 
3. TRT=23℃和 TKKL,max=80℃之间必须进行 245 次循环。 
 
三部分测试循环次数累加起来即可得到运行模式 i 时的总测试循环次数 N  Prüf.Mode  i=943 次循
环。 
 
测试过程中环境温度和冷却液管路的温度变化循环测试同步进行。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 105 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
附录 E(标准) 
等级为 2 的恒湿恒温测试的计算模型 
E.1 Lawson‐模型 
该测试时间的计算需要获得停车状态下的平均环境湿度 RHFeldParken 以及元件平均温度
TFeldParken. 
如果设计任务书中没有特殊说明，则采用下表中的数值： 
 
表 113：停车状态下的平均环境湿度和温度 
安装位置 
停车平均环境湿度 RHFeldParken
停车平均环境温度 TFeldParken 
乘客车厢/行李箱内 
60%rH 
23℃ 
乘客车厢/行李箱外 
65%rH 
23℃ 
 
与环境平均湿度和温度有关，Lawson‐模型的加速因数计算如下： 
 
 
其中： 
AT/RH                    Lawson‐模型的加速因数 
b                          常量（b=5.57*10‐4） 
EA                          激活能量（EA=0.4eV） 
K                          波茨曼常量（k=8.617*10‐5eV/K） 
TPruf                      测试温度（℃） 
TFeldParken              停车时的平均温度（℃） 
RHPruf                    测试期间的相对湿度（%） 
RHFeldParken            停车时的平均相对温度（%） 
‐273.15℃            温度绝对零点 
 
等级为 2 的恒湿恒温测试的测试时间计算如下： 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 106 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
其中： 
tPruf                          测试时间（h） 
tFeldParken                区域内使用寿命周期内的非运行时间（停车时间）
（h）
（不利情况下131400h，
如果车辆未使用） 
AT/RH                      按照等式（5）计算的 Lawson‐模型的加速因数 
 
E.2 举例 
安装在发动机舱内的控制器测试时间计算如下： 
1. 停车时元件的平均温度 TFeldParken =23℃，相对湿度为 RHFeldParken =65%。 
测试条件为 TTest =65℃和 RHPruf =93% 
 
通过等式（5）得出 Lawson‐模型综合的加速因数 AT/RH =82.5 
 
2. 区域内的停车时间 tFeldParken =131400h 
通过等式（6）得出总测试时间： 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 107 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
附录 F(参考) 
冷凝测试，测试箱设置和图表 
 
图 49：测试箱设置 
 
温度上升期间的水浴温度可以控制调节，温度达到 80℃时换到温控上（正常运行）。 
 
 
 
 
开始启动 
相对湿度 
冷凝 
功能测试 


### 第 108 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
图 50：冷凝测试流程，一次循环 
1. 调节的水浴温度 
2. 得出的测试箱温度 
3. 测试箱中的实际空气湿度 
 
 
 
 
 
 
 
 


### 第 109 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
 
图 51：冷凝测试流程，5 次循环 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 110 页
版本 2.2                                                              LV 124                                                        编制日期：28.02.2013 
 
附录 G(参考) 
物理分析方法举例 
 
� 
螺钉松动扭矩（例如塑壳螺钉连接、固定零件的螺钉……） 
� 
焊接点缺陷 
� 
（尤其是温度变化引起的）零件和/或电路板变色 
� 
（机械运动的零部件）操作灵活性、摩擦和运转 
� 
磨损痕迹 
� 
（尤其是注塑件、密封件的）材质出现裂缝、裂纹、变形，选择合适的测试方法（X 射
线、CT、剖面分析……）进行检测 
� 
不透明性（尤其是光学传感系统中的零件） 
� 
锁紧结构的状态 
� 
腐蚀及移动痕迹，尤其是银、锡的移动 
� 
评估塑料的耐水解性（尤其是内嵌网格的零件以及 KL.30 电连接的零件） 
� 
通孔电路板损坏，尤其是热过孔 
� 
机械负荷后（振动、机械冲击、坠落测试）的大电容内部连接损坏 
� 
（如电流、温度、摩擦、氧化引起的）连接器 Pin 脚损坏 
� 
其他异常现象 
� 
ICT  结果（在可能的情况下） 

