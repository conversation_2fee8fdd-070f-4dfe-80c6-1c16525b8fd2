# MSS_20202_EN_2018-08_CAN网络性能规范_214ff型号系列.pdf

## 文档信息
- 标题：Microsoft Word - MSS 20202 CAN Networking Performance Specification 2018-08.docx
- 作者：ludonat
- 页数：47

## 文档内容
### 第 1 页
 
 
Mercedes-Benz 
MSS 20202, V2.0 
Company Standard 
Date published: 2018-08 
 
Total no. of pages (incl. Annex): 47  
 
Person in charge: <PERSON> 
 
Email: patrick.hetmain<PERSON><PERSON>@daimler.com 
 
Plant: Plant 059, Dept.: RD/UPT 
 
 
 
 
 
Copyright Daimler AG 2018 
CAN Networking Performance Specification 
for Model Series 214ff 
 
 
  
 
Foreword 
 
 
This document defines the CAN and CAN FD networking performance specification for vehicle 
multiplexing communication system and the electronic control modules using Controller Area 
Network (CAN) and CAN with Flexible Data Rate (CAN FD) on certain Mercedes-Benz Cars vehicle 
models. 
 
Refer all questions to the Mercedes-Benz Cars Networking Group. 
 
Application note:  
Application of the present version of this Standard is binding for new vehicle projects or components 
of this scope, for which no concept/basic specifications or component requirement specifications 
have been approved yet at the date of issue of this version.  
The respective contract documents regulate the mandatory application of the present version of this 
Standard by the supplier. 
 
 
 
Changes 
 
 
Refer to Annex A - revision history 
 
 
 
Uncontrolled copy when printed
RD/UBF: <PERSON>, 2018-11-23


### 第 2 页
MSS 20202, 2018-08, page 2 
Copyright Daimler AG 2018 
Contents 
 
CAN Networking Performance Specification for Model Series 214ff .................................................... 1 
1 
Scope ............................................................................................................................................. 4 
1.1 
Deviations ................................................................................................................................... 4 
2 
Normative References.................................................................................................................... 5 
3 
Abbreviations, Acronyms, Definitions & Symbols .......................................................................... 6 
4 
General design requirements ......................................................................................................... 7 
4.1 
Voltage Ranges .......................................................................................................................... 7 
5 
Network Communication Description ............................................................................................. 8 
6 
Communication behavior ............................................................................................................... 9 
6.1 
AUTOSAR CAN NM parametrization ......................................................................................... 9 
6.2 
Start Delay Time ......................................................................................................................... 9 
6.3 
Cycle time tolerances ................................................................................................................. 9 
6.4 
Receiving behavior and hardware filtering ............................................................................... 10 
6.5 
Frame Lengths and DLC .......................................................................................................... 10 
6.6 
Influence of PNCs on communication behavior ....................................................................... 11 
6.7 
Uninterrupted Communication .................................................................................................. 11 
6.8 
Reset Operation ........................................................................................................................ 11 
6.9 
Short circuit of buslines ............................................................................................................ 11 
6.10 
Frame handling with 100% bus load ..................................................................................... 11 
6.11 
AUTOSAR bus-off recovery .................................................................................................. 12 
6.12 
Wake-up ................................................................................................................................ 14 
7 
Network startup and power down ................................................................................................. 15 
7.1 
Network wake up / startup ........................................................................................................ 15 
7.2 
Network sleep / power down .................................................................................................... 20 
8 
Physical Layer .............................................................................................................................. 21 
8.1 
Permanent power supply .......................................................................................................... 21 
8.2 
Switched power supply ............................................................................................................. 23 
8.3 
Dimensioning ............................................................................................................................ 25 
8.4 
Galvanic separation between transceiver and microcontroller ................................................. 26 
8.5 
System Basis Chip / ASIC ........................................................................................................ 26 
8.6 
Bit timing and oscillator tolerance ............................................................................................. 26 
9 
Transceiver ................................................................................................................................... 31 
9.1 
Recommended Transceivers/ System Basis Chips ................................................................. 31 
9.2 
Common Transceiver requirements ......................................................................................... 32 
10 
Common Mode Choke .............................................................................................................. 33 
11 
Termination Classic CAN ......................................................................................................... 34 
11.1 
Body-CAN ............................................................................................................................. 34 
11.2 
CPC-CAN .............................................................................................................................. 34 
11.3 
Engine-CAN (PT3) ................................................................................................................ 34 
11.4 
Headunit-CAN 1 .................................................................................................................... 34 
11.5 
Headunit-CAN 2 .................................................................................................................... 34 
11.6 
Hybrid-CAN ........................................................................................................................... 35 
11.7 
Powertrain-CAN .................................................................................................................... 35 
11.8 
PT_Sensor-CAN / ECM-Periphery-CAN 1 ............................................................................ 35 
11.9 
Steering-Wheel-CAN............................................................................................................. 35 
11.10 
Suspension_F-CAN .............................................................................................................. 35 
11.11 
Suspension_R-CAN .............................................................................................................. 36 
12 
Termination CAN FD ................................................................................................................ 37 
12.1 
Body-CAN 1 (250k/500kBit/s) ............................................................................................... 37 
12.2 
INV-CAN (500k/2000kBit/s) .................................................................................................. 38 
12.3 
Energy-CAN (500k/1000kBit/s) ............................................................................................. 38 
12.4 
HAD-Backup-CAN (500k/1000kBit/s) ................................................................................... 38 
12.5 
Periphery-CAN (500k/1000kBit/s) ......................................................................................... 38 
12.6 
PT-Periphery-CAN (500k/1000kBit/s) ................................................................................... 38 
12.7 
Radar_F-CAN (500k/2000kBit/s) .......................................................................................... 39 
12.8 
Radar_R-CAN (500k/2000kBit/s) .......................................................................................... 39 
12.9 
TCM-CAN (500k/2000kBit/s) ................................................................................................ 39 
13 
Wiring and Connectors for CAN FD ......................................................................................... 40 
13.1 
CAN FD Cables ..................................................................................................................... 40 
13.2 
Connectors for CAN FD ........................................................................................................ 41 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 3 页
MSS 20202, 2018-08, page 3 
Copyright Daimler AG 2018 
13.3 
Connector Requirements and Approved Connectors ........................................................... 41 
Annex A (informative) Revision History ............................................................................................... 46 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 4 页
MSS 20202, 2018-08, page 4 
Copyright Daimler AG 2018 
1 
Scope 
MSS20202-38: 
This CAN network specification document contains Implementation requirements 
for ECUs in AUTOSAR networks used in Mercedes-Benz Cars vehicles. The 
Vehicle Networking Group of Mercedes-Benz Cars will determine the respective 
vehicle lines that this specification applies. 
MSS20202-39: 
This network specification document - together with MSS 20200 - contains 
implementation requirements for ECUs used in vehicles of Daimler, which 
support Classic CAN (formerly “CAN 2.0b”) or CAN FD. 
MSS20202-40: 
All requirements that do not distinguish between Classic CAN or CAN FD apply to 
both kind of systems: Classic CAN as well as CAN FD. If there are different 
requirements for Classic CAN and CAN FD, these will be separated by means of 
different paragraphs or tables. Baud rates that are specified apply for Classic 
CAN as well as for the arbitration baud rate of CAN FD if not mentioned 
otherwise. 
MSS20202-41: 
Refer all questions to the Vehicle Networking Group. 
1.1 
Deviations 
MSS20202-43: 
In the current document, the following defined terminology prescription applies. 
The usage of 
MSS20202-44: 
 
- “Shall” expresses in the text a mandatory requirement. 
MSS20202-45: 
 
- "Should” expresses in the text an optional requirement 
MSS20202-46: 
 
- “Can” expresses in the text a permitted practice or method. 
MSS20202-47: 
Text segments written in italic letters have information character. 
MSS20202-48: 
Deviations from the requirements contained in this standard are only allowed if 
agreed and documented explicitly between the supplier, the Vehicle Networking 
Group and the appropriate Daimler design and release areas. 
MSS20202-49: 
ECUs in networks with baud rates > 500kBd shall use the baud rate dependent 
parameters of this specification for 500kBd networks if not mentioned otherwise. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 5 页
MSS 20202, 2018-08, page 5 
Copyright Daimler AG 2018 
2 
Normative References 
MSS20202-51: 
Mercedes-Benz specification documents are available via DocMaster from 
Daimler Supplier Portal https://daimler.portal.covisint.com. 
 
Document Number 
Document Title 
[MSS 20200] 
General Networking Performance Specification 
[DSUDS] 
Daimler Diagnostic Specification - Supplement to ISO 14229 
DDS S-ISO14229, refer to [CANDELA_TEMPLATE] 
[CANDELA_ 
TEMPLATE] 
Candela Template contains definition of diagnostic services 
Diagnose Portal, Daimler AG 
[MBN LV 124-1] 
Elektrische und elektronische Komponenten in Personenkraftwagen bis 3,5t 
[IEC/TS 62228] 
Integrierte Schaltungen - EMV-Bewertung von CAN-Sende- und -Empfangsgeräten 
[ISO 11898-1] 
Road vehicles - Controller area network (CAN) - Data link layer and physical signalling 
[ISO 11898-2] 
Road vehicles - Controller area network (CAN) - High-speed medium access unit 
[ISO 15765-1] 
Road vehicles - Diagnostics on Controller Area Networks (CAN) - General information 
[ISO 15765-2] 
Road vehicles - Diagnostics on Controller Area Networks (CAN) - Network layer 
services 
[ISO 15765-3] 
Road vehicles - Diagnostics on Controller Area Networks (CAN) - Implementation of 
unified diagnostic services (UDS on CAN) 
[ISO 15765-4] 
Road vehicles - Diagnostics on Controller Area Networks (CAN) - Requirements for 
emissions-related systems 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 6 页
MSS 20202, 2018-08, page 6 
Copyright Daimler AG 2018 
3 
Abbreviations, Acronyms, Definitions & Symbols 
 
Term 
Definition 
AUTOSAR 
Automotive Open System Architecture (www.autosar.org) 
ASIC 
Application specific integrated circuit 
BRP 
Baudrate Prescaler 
CAN 
Controller Area Network 
CAN_H 
CAN High 
CAN_L 
CAN Low 
CAN FD 
CAN with Flexible Datarate 
CLK 
Clock 
CMC 
Common Mode Choke 
DLC 
Data Length Code 
ECU 
Electronic Control Unit 
EMI 
Electromagnetic Interference 
EN 
Enable 
ESD 
Electro Static Discharges 
GND 
Ground 
IBK 
Industrial modular system ("Industriebaukasten") 
INH 
Inhibit 
I-PDU 
Interaction layer PDU (assembled by the AUTOSAR module COM) 
LIN 
Local Interconnect Network 
NBT 
Nominal Bit Time 
NCD 
Network Communication Description (formerly "VMM - Vehicle Message Matrix") 
NM 
Network Management 
PCB 
Printed Circuit Board 
PDU 
Protocol Data Unit 
Permanent Power Supply 
Battery fed, e.g. Clamp-30/30T 
PLL 
Phase Locked Loop 
PNC 
Partial Network Cluster 
RX 
Receiver Exchange 
SBC 
System Basis Chip 
SJW 
Synchronization Jump Width 
STB 
Standby 
Switched Power Supply 
Ignition fed, e.g. Clamp-15/87 
TDC 
Transceiver Delay Compensation 
TSEG 
Time Segment 
TX 
Transmitter Exchange 
Vehicle Networking Group 
Subdivision of Mercedes-Benz Cars responsible for in-vehicle-networking 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 7 页
MSS 20202, 2018-08, page 7 
Copyright Daimler AG 2018 
4 
General design requirements 
MSS20202-54: 
[ISO 11898] shall be followed when designing CAN(FD) ECUs and CAN(FD) 
network systems. The standard frame format with 11-bit identifier and the 
extended frame format with 29-bit identifier shall be supported. 
MSS20202-55: 
Remote frame and overload frame as specified in [ISO 11898] shall not be used. 
MSS20202-56: 
 
 
Title of Specification 
Version 
Road Vehicles – Controller area network (CAN) 
Part 1: Data link layer and physical signalling 
Part 2: High-speed medium access unit 
[ISO 11898] 
Road Vehicles – Diagnostics on Controller Area Networks (CAN) 
Part 1: General information 
Part 2: Network layer services 
Part 3: Implementation of unified diagnostic services (UDS on CAN) 
Part 4: Requirements for emissions-related systems 
[ISO 15765] 
 
Table T4a: International Standards 
4.1 
Voltage Ranges 
MSS20202-58: 
All ECUs shall minimally guarantee proper CAN communication with a supply 
voltage required for coding "a" components, refer to [MBN LV 124-1]. 
MSS20202-59: 
If CAN communication has to be continued below or above these limits because 
of functional requirements it shall run without errors. 
MSS20202-60: 
In order to ensure proper CAN communication of critical vehicle components at 
specified under and over voltages, it is required that none of the CAN ECUs shall 
interfere with or disrupt the bus traffic even if the battery voltage is outside it's 
operating range. 
MSS20202-61: 
This may require that transceivers and other electrical loads are deactivated 
temporarily or completely at certain voltage conditions. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 8 页
MSS 20202, 2018-08, page 8 
Copyright Daimler AG 2018 
5 
Network Communication Description 
MSS20202-63: 
Refer to [MSS 20200] for general communication description informations and 
requirements. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 9 页
MSS 20202, 2018-08, page 9 
Copyright Daimler AG 2018 
6 
Communication behavior 
6.1 
AUTOSAR CAN NM parametrization 
MSS20202-66: 
Refer to [MSS 20200] for general Network Management informations and 
requirements. 
MSS20202-67: 
The AUTOSAR Network Management schedule and parametrization is based on 
following constraints: 
 
MSS20202-68: 
 
CANNM_TIMEOUT_TIME   
= 2.5  CANNM_MSG_CYCLE_TIME 
MSS20202-69: 
 
CANNM_REPEAT_MESSAGE_TIME  = 3  CANNM_MSG_CYCLE_TIME 
 
MSS20202-70: 
The parametrization and configuration requirements for the AUTOSAR Network 
Management in the following tables shall be used: 
MSS20202-71: 
 
 
NM Parameters 
[AUTOSAR name] 
Description 
Value 
CANNM_MSG_CYCLE_TIME 
Transmission period for NM frames 
1000 ms 
CANNM_MSG_CYCLE_OFFSET 
Node specific offset for the transmission of NM frames to prevent 
bursts 
0 ms 
CANNM_REPEAT_MESSAGE_TIME 
Time after “Repeat Message” state is left 
3000 ms 
CANNM_TIMEOUT_TIME 
Time after “Prepare Bus Sleep” is entered 
2500 ms 
CANNM_WAIT_BUS_SLEEP_TIME 
Time after “Bus Sleep” is entered 
1500 ms 
CANNM_REMOTE_SLEEP_IND_TIME 
Time after a node is notified when the node is the only one in the 
network that requests bus communication. 
2500 ms 1 
CANNM_IMMEDIATE_NM_TRANSMISSIONS Number of immediate NM transmissions for updated PNC data 
6 2 
CANNM_IMMEDIATE_NM_CYCLETIME 
Cycle time for immediate NM transmissions 
20 ms 2 
1 Applicable for gateways 
2 Applicable for ECUs using PNC functionality 
 
Table T6.1a: AUTOSAR Network Management parametrization requirements 
MSS20202-72: 
Network specific changes may be defined in the NCD. The NCD values shall be 
applied. 
6.2 
Start Delay Time 
MSS20202-75: 
To avoid bursts, the NCD defines start delay times for all cyclic PDUs (cylic, 
cyclic on change and dual cycle). 
MSS20202-76: 
ECUs shall follow the start delay times at every network wake up/startup. 
MSS20202-77: 
If the defined start delays are contradictory to functional requirements the Vehicle 
Networking Group shall be contacted. 
MSS20202-78: 
In case a new transmit PDU is added all start delay times for the ECU may be 
changed. 
6.3 
Cycle time tolerances 
MSS20202-80: 
The average cycle time (tc and tc2) of all PDUs for both, a standalone ECU and 
ECUs in the network, shall be within ± 2% of the specified PDU cycle time in the 
NCD. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 10 页
MSS 20202, 2018-08, page 10 
Copyright Daimler AG 2018 
MSS20202-81: 
The absolute deviation of the cycle time shall not exceed the values stipulated in 
the following table: 
MSS20202-82: 
 
 
PDU 
 
Container 
PDU 
Always 
Never 
Default 
Trigger 
 
Cycletime > 10ms: 
[Cycletime - 10ms ; Cycletime + 10ms] 
Cycletime   10ms: 
[Cycletime - 5ms ; Cycletime + 5ms]  
 
Cycletime > 10ms: 
[Cycletime - 10ms ; Cycletime + TPDU_Collection + 10ms]* 
 
Cycletime  10ms: 
[Cycletime - 5ms ; Cycletime +  TPDU_Collection + 5ms]* 
 
First 
Contained 
PDU 
Triggers 
 
Cycletime > 10ms: 
[Cycletime - 10ms ; Cycletime + 10ms]  
Cycletime  10ms: 
[Cycletime - 5ms ; Cycletime + 5ms]  
Cycletime > 10ms: 
[Cycletime - 10ms ; Cycletime + 10ms] 
Cycletime  10ms: 
 [Cycletime - 5ms ; Cycletime + 5ms] 
*TPDU_Collection = Min(TMaxTxDelayFrame, TMaxTxDelayPDU)  
 
Table T6.3a: Absolute deviation of the cycle time 
MSS20202-83: 
Even at high busload the cycle time tolerances shall not be violated (excluding 
arbitration delay). 
6.4 
Receiving behavior and hardware filtering 
MSS20202-93: 
On broadcast networks a PDU that is configured for PDU header usage can 
potentially be transmitted within every frame. Thus the addressing of a particular 
ECU does not necessarily depend on a particular frame. The application of a 
hardware filter may cause a receiving ECU to update its filter settings. 
MSS20202-94: 
For that the Vehicle Networking Group recommends to disable any hardware 
filtering of frames in order to receive a PDU independently of its frame.  
6.5 
Frame Lengths and DLC 
MSS20202-96: 
Classic CAN frames consist of one PDU. The frame shall have the same size as 
the PDU. There are no PDU update bits or PDU headers. 
MSS20202-97: 
CAN FD frames may consist of one PDU as well as of multiple PDUs. 
MSS20202-98: 
If a frame is configured as single-PDU frame, the frame shall have the same size 
as the PDU. There are no PDU update bits. 
MSS20202-99: 
If a frame is configured as multi-PDU frame, the following requirements shall be 
applied: 
MSS20202-100: 
In order to reduce bus load, an ECU sending multi-PDU frames on CAN FD  shall 
reduce the frame length of a multi-PDU frame to the minimal needed value. Thus 
a multi-PDU frame with a certain ID might be transmitted with different lengths 
and according DLCs at each transmission.  
MSS20202-101: 
For multi-PDU frames, a sending ECU shall use a frame length that is equal to 
the required payload at each transmission. If this is prohibited by protocol DLC 
restrictions, the next higher valid frame length that complies with the DLC 
restrictions shall be applied using padding value as defined in [MSS 20200]. 
MSS20202-102: 
A receiving ECU of a multi-PDU frame shall not use DLC check for frames that 
are configured for multi-PDU usage in order to check a statically defined frame 
length.  
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 11 页
MSS 20202, 2018-08, page 11 
Copyright Daimler AG 2018 
MSS20202-103: 
In terms of compatibility the DLC check mechanisms should be disabled in 
general for both, Classic CAN and CAN FD. 
MSS20202-104: 
Example: Frame content including PDUs and PDU Headers is 26 Byte. 26 Byte is 
no frame length that complies with DLC restrictions. 32 Byte is the next compliant 
value. Thus 32 Byte is applied as frame length. Bytes 27-32 are padded with the 
frame default value as required in [MSS 20200]. 
6.6 
Influence of PNCs on communication behavior 
MSS20202-106: 
All ECUs are part of at least one PNC. 
MSS20202-107: 
One ECU can be a member of more than one PNC. 
MSS20202-108: 
An ECU only sends PDUs if at least one of its associated PNCs is active. ECUs 
with no active PNC on this network will stay in a non-sending mode. 
MSS20202-109: 
Thus it is possible to have network communication where only a subset of all 
ECUs will take part. 
MSS20202-110: 
For further details see AUTOSAR ComM specification. 
6.7 
Uninterrupted Communication 
MSS20202-112: 
Between the completion of the network startup phase and the beginning of 
prepare bus-sleep state an ECU shall not perform any activities e.g. reset, 
resulting in 
MSS20202-113: 
 
- missing frames 
MSS20202-114: 
 
- discontinuing CAN communication  
MSS20202-115: 
 
- restarting Network Management 
MSS20202-116: 
However, the above requirements do not apply to the ECU in the following 
events: 
MSS20202-117: 
 
- Activities which are explicitly triggered by the execution of diagnostic 
services, e.g. ECU reset 
MSS20202-118: 
 
- Detection of  an error or fault which requires the above mentioned behavior. 
Refer to relevant ECU or system software requirement specification. 
6.8 
Reset Operation 
MSS20202-120: 
ECUs shall not corrupt frames on the network while going into reset, during reset 
or coming out of reset.  
MSS20202-121: 
ECUs shall be capable of coming out of reset and begin normal operation without 
requiring power or ignition reset. 
6.9 
Short circuit of buslines 
MSS20202-123: 
An ECU shall be able to handle a short circuit on the buslines. After release of the 
short circuit ECUs shall be able to continue normal operation without requiring 
power or ignition reset. 
6.10 Frame handling with 100% bus load 
MSS20202-125: 
An ECU shall be able to handle a permanent bus load of 100%. Therefore the 
duration of a CAN receive interrupt shall not exceed the duration of the shortest 
CAN frame on the bus. For some ECUs more restrictive requirements regarding 
their functionality during such conditions may apply. In this case refer to the ECU 
specification. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 12 页
MSS 20202, 2018-08, page 12 
Copyright Daimler AG 2018 
6.11 AUTOSAR bus-off recovery 
MSS20202-127: 
In order to prevent permanent disturbance of the bus communication (e.g. due to 
transmission problems of a specific or all frames) every ECU shall observe the 
bus-off recovery procedure. 
MSS20202-128: 
The bus-off recovery procedure shall consist of two phases. Phase 1 for a bus-off 
recovery with short frame transmission pause and Phase 2 with a longer frame 
transmission pause. If an initial bus-off is detected, the application shall enter 
Phase 1. 
6.11.1 Phase 1 
MSS20202-130: 
a. Immediately reset and reinitialize the CAN protocol controller. 
MSS20202-131: 
b. Pause all frame transmissions for BOR_TIME_L1. 
MSS20202-132: 
c. After BOR_TIME_L1 enable transmission of frames. 
MSS20202-133: 
d. If no further error occurs during BOR_TIME_TX_ENSURED return to normal 
operation. 
MSS20202-134: 
e. If another bus-off occurs during BOR_TIME_TX_ENSURED and the maximum 
number of attempts BOR_COUNTER_L1_TO_L2 has not been reached proceed 
with step a. If BOR_COUNTER_L1_TO_L2 has been reached enter Phase 2. 
6.11.2 Phase 2 
MSS20202-136: 
f. Immediately reset and reinitialize the CAN protocol controller. 
MSS20202-137: 
g. Pause all frame transmissions for BOR_TIME_L2. 
MSS20202-138: 
h. After BOR_TIME_L2 enable transmission of frames. 
MSS20202-139: 
i. If no further error occurs during BOR_TIME_TX_ENSURED return to normal 
operation. 
MSS20202-140: 
j. If another bus-off occurs during BOR_TIME_TX_ENSURED go back to step f. 
Iterate the steps f to j indefinitely until a normal bus-off free communication is 
restored. If the application for a ECU with permanent power supply does not 
require further CAN communication the bus-off repair procedure shall be 
terminated and the ECU shall enter bus-sleep mode. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 13 页
MSS 20202, 2018-08, page 13 
Copyright Daimler AG 2018 
6.11.3 Flowchart of the AUTOSAR bus-off recovery procedure 
MSS20202-142: 
 
Update bus-off
counter  
Pause all frame 
transmissions for 
BOR_TIME_L2
Enable transmission of 
frames
Reinitialize 
CAN protocol chip
Bus-off counter  
reached
Transmission 
successful?
(no bus-off)
Initialize 
bus-off counter
Bus-off event
Return to normal 
operation
Pause all frame 
transmissions for 
BOR_TIME_L1
Enable transmission of 
frames
Reinitialize 
CAN protocol chip
Transmission 
successful?
(no bus-off)
Yes
No
Yes
No
No
Yes
Phase 1
Phase 2
BOR_COUNTER_
L1_TO_L2?
BOR_TIME_TX_
ENSURED expired?
Yes
No
BOR_TIME_TX_
ENSURED expired?
No
Yes
 
 
Figure F7.3.5.3a: AUTOSAR bus-off recovery procedure 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 14 页
MSS 20202, 2018-08, page 14 
Copyright Daimler AG 2018 
6.11.4 Parameters of the AUTOSAR bus-off recovery procedure 
MSS20202-144: 
 
Names 
Definition 
Range 
BOR_TIME_L1 
Time frame during which all frame transmissions 
shall be paused in the phase 1. After this time 
frame the application shall start with a frame 
transmission. 
 
Minimum value depends on baud rate, maximum 
value is always BOR_TIME_L2. 
 200ms (125kBd) 
 100ms (250kBd) 
 50ms (500kBd) 
 50ms (667kBd) 
 50ms (800kBd) 
 
 BOR_TIME_L2 
BOR_TIME_L2 
Time frame during which all frame transmissions 
shall be paused in the phase 2. After this time 
frame the application shall start with a frame 
transmission. 
1000 ms ±10% 
BOR_TIME_TX 
_ENSURED 
Time frame during which is checked if the bus-off 
recovery was successful. After this time frame 
the bus-off repair procedure is completed if no 
new bus-off occurs. 
1000 ms ±10% 
BOR_COUNTER
_L1_TO_L2 
Number of allowed attempts for phase 1 bus-off 
repair in a row without transmitting successfully a 
single valid CAN frame. 
 5 (125kBd) 
10 (250kBd) 
20 (500kBd) 
20 (667kBd) 
20 (800kBd) 
 
 
Table T7.3.5.4a: AUTOSAR bus-off recovery parameters 
MSS20202-145: 
For CAN FD the defined ranges apply to the arbitration baud rate. 
6.12 Wake-up 
MSS20202-394: 
In a sleeping ECU (no communication interface active) a remote wake-up of a 
communication interface shall lead to a wake-up of all communication interfaces 
connected to that ECU. 
MSS20202-395: 
A deviation from this requirement has to be agreed by the Vehicle Networking 
Group at project start and if changes occur. 
MSS20202-396: 
This behavior is defined by AUTOSAR with parameter 
“ComMSynchronousWakeUp”, refer to “Integration Requirements AUTOSAR 4.x“ 
[AUTOSAR_INT]. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 15 页
MSS 20202, 2018-08, page 15 
Copyright Daimler AG 2018 
7 
Network startup and power down 
7.1 
Network wake up / startup 
7.1.1 
General 
MSS20202-149: 
An ECU with permanent power supply shall only start communication after a valid 
wake up event has been detected and verified. 
7.1.2 
Error Frame handling 
MSS20202-151: 
Error frames shall not cause an ECU to wake up. 
After connection and disconnection of the power supply an ECU shall not cause 
error frames. 
7.1.3 
Active wake up 
MSS20202-153: 
If the network is sleeping or in power down mode the waking ECU shall transmit 
its  NM PDU first in order to introduce network wake up procedure. 
7.1.4 
Network wake up for ECUs with permanent power supply 
MSS20202-155: 
In order to realize distributed functions in a vehicle, ECUs shall be able to wake 
up the CAN network. To limit the power consumption to a minimum, an ECU shall 
only wake up the CAN network if CAN communication is necessary for the 
processing of functionality. Otherwise functions shall be handled locally without 
any CAN communication.  
MSS20202-156: 
Any local wake up event shall be checked for plausibility before the connected 
network is woken up. Local wake up events may be based on internal timers, 
interrupts, sensors, etc. The plausibility check shall be done independently of the 
type of connection, including sub buses like LIN. 
MSS20202-157: 
The application shall transmit wake up reasons as required in [MSS 20200]. 
MSS20202-158: 
TMessageStart 
Within TMessageStart all cyclic PDUs shall be transmitted at least once with the 
defined cycle time and start delay time. To prevent a delay of the wake up of 
other ECUs, signals shall be set to the default values if no valid information can 
be obtained. TMessageStart  starts with the transmission of the first frame. 
7.1.5 
Passive wake up for ECUs with permanent power supply 
MSS20202-160: 
In the following the wake up behavior of an ECU is described if the wake up is 
caused by the network (passive wake up). See the figures below for details. 
MSS20202-161: 
TCanInit 
A voltage level change or bit interferences on the network lines shall not cause 
the start of CAN communication. If the physical layer signals a wake up event on 
the network lines while the CAN hardware is in sleep mode, the CAN hardware 
(physical layer and CAN microcontroller) and the communication software 
modules shall be set to the operational mode. The Initialization shall be executed 
as fast as possible within TCanInit. TCanInit begins with the first activity on the 
network signaled through the physical layer and ends after the ECU is initialized 
(ECU is ready to receive and transmit frames). At this stage the ECU shall not 
start transmitting any frames. 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 16 页
MSS 20202, 2018-08, page 16 
Copyright Daimler AG 2018 
MSS20202-162: 
TWakeUpTimeOut 
TWakeUpTimeOut starts after the expiration of TCanInit. If no valid frame is received and 
passed through the acceptance filter of the CAN controller within TWakeUpTimeOut it 
shall be concluded that the network voltage change is not caused by a wake up 
event. 
MSS20202-163: 
TBackToBusSleep 
TBackToBusSleep starts after the expiration of TWakeUpTimeOut. The CAN hardware shall 
be set back to bus sleep within TBackToBusSleep and enter the same low power 
mode as before the wake up event. If a valid frame is received during 
TBackToBusSleep the ECU shall immediately proceed with TMessageStart. 
MSS20202-164: 
TMessageStart 
If within TWakeUpTimeOut a valid frame has been received and passed through the 
acceptance filter of the CAN controller, a wake up event is confirmed. In this case 
TWakeUpTimeOut shall be suspended immediately and the ECU shall participate in 
regular CAN communication. First the Network Management shall leave bus 
sleep mode and commence to normal/network mode. Afterwards transmission of 
application PDUs shall begin immediately with the defined start delay times. 
Within TMessageStart all cyclic PDUs shall be transmitted at least once with the 
defined cycle time and start delay time. To prevent a delay of the wake up of 
other ECUs, signals shall be set to the default values if no valid information can 
be obtained. TMessageStart begins immediately after TWakeUpTimeOut has been 
canceled. 
MSS20202-165: 
TSignalValue 
Plausible signal values shall be transmitted as soon as possible typically prior to 
the expiration of TSignalValue. For details see ECU specific software requirements. 
TSignalValue starts simultaneously with TMessageStart right after the expiration of 
TWakeUpTimeOut. 
MSS20202-166: 
 
  
t
1
1
T
CanInit
T WakeUpTimeOut
2
2
3
3
CAN physical layer detects CAN activity and triggers ECU initialization
ECU is ready to receive and transmit frames
ECU hasn't received any valid CAN frame
ECU switches back to bus sleep
T BackToBusSleep
4
4
 
 
Figure F7.1.5a: Wake up sequence caused by an interference 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 17 页
MSS 20202, 2018-08, page 17 
Copyright Daimler AG 2018 
MSS20202-167: 
 
t
1
1
  
T CanInit
TWakeUpTimeOut
2
2
3
3
CAN physical layer detects CAN activity and triggers ECU initialization
ECU is ready to receive and transmit frames
ECU received a frame before the end of   TWakeUpTimeOut; ECU starts transmitting 
frames
T MessageStart
T SignalValue
T NetworkStartup
4
4
5
ECU has transmitted all cyclic PDUs as described in the NCD at least once
5
ECU transmits all signals with plausible values
Figure F7.1.5b: Wake up sequence caused by a valid CAN frame 
MSS20202-168: 
ECUs with permanent power supply shall follow the wake up timing stipulated in 
the following table: 
MSS20202-169: 
 
Name 
Definition 
Range [ms] 
TCanInit 
Time frame within which the application initializes the CAN 
hardware and becomes ready to receive and transmit frames 
after the physical layer signals network activity 
02 – 120 
TWakeUpTimeOut 
Time frame within which an activity signaled from the physical 
layer has to be confirmed as a CAN frame. If a valid frame is 
received during this time frame the time window shall be termi-
nated 
2500 – 5000 
TBackToBusSleep 
Time frame within which the application set back the CAN hard-
ware to bus sleep while no valid CAN frame was received during 
TWakeUpTimeOut 
0 – 1000 
TMessageStart 
Time frame within which all cyclic PDUs have to be transmitted 
at least once 
0 – 300 
TSignalValue 
Time frame within which typically3 all signals shall have plausible 
values. In principle the signals shall have plausible values as 
soon as possible 
02 – 600 
TNetworkStartup Time since the first signal from the physical layer to the end of 
TMessageStart 
0 – 3420 
(0 – 10004) 
1 ECU or system software requirement spec may require more stringent timing for initialization 
2 Timings shall be as fast as possible within the range 
3 For details see ECU specific software requirements 
4 Typical 
 
 
Table T7.1.5a: Wake up timing for ECUs with permanent power supply1 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 18 页
MSS 20202, 2018-08, page 18 
Copyright Daimler AG 2018 
7.1.6 
Network startup for ECUs with switched power supply 
MSS20202-171: 
The ECU power supply and CAN interface of ECUs with switched power supply 
are switched on by a Clamp 15/15r/87 input. CAN communication is only allowed 
if the Clamp 15/15r/87 is switched on. 
MSS20202-172: 
In the following the startup behavior of an ECU with switched power supply is 
described. See the following figure for details. 
MSS20202-173: 
TCanInit 
The CAN hardware (physical layer and CAN microcontroller) and the 
communication software modules shall be set to the operational mode. The CAN 
initialization shall be executed as fast as possible. The maximum time for an ECU 
to begin transmitting own CAN frames is TCanInit. The starting point for TCanInit 
begins when Clamp 15/15r/87 switches to system voltage. 
MSS20202-174: 
TMessageStart 
Since the CAN communication is always running if ignition (Clamp 15/15r/87) is 
switched on, no additional startup plausibility checks are required and the ECU 
shall start transmitting PDUs immediately with the defined start delay times. 
Within TMessageStart all cyclic PDUs shall be transmitted at least once with the 
defined cycle time and start delay time. To prevent a delay of the startup of other 
ECUs, signals shall be set to the default values if no valid information can be 
obtained. TMessageStart starts after the expiration of TCanInit. 
 
MSS20202-175: 
TSignalValue 
Plausible signal values shall be transmitted as soon as possible typically prior to 
the expiration of TSignalValue. For details see ECU specific software requirements. 
TSignalValue starts simultaneously with TMessageStart right after the expiration of 
TCanInit. 
MSS20202-176: 
 
t
1
1
  
T CanInit
2
2
3
Ignition is turned on
ECU starts with sending frames and is ready to receive frames
TMessageStart
T SignalValue
TNetworkStartup
3
4
4
ECU has transmitted all cyclic PDUs as described in the NCD at least once
ECU transmits all signals with plausible values
   
 
Figure F7.1.6a: Startup procedure caused by ignition on 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 19 页
MSS 20202, 2018-08, page 19 
Copyright Daimler AG 2018 
MSS20202-177: 
ECUs with switched power supply shall follow the startup timing stipulated in the 
following table: 
MSS20202-178: 
 
Name 
Definition 
Range [ms] 
TCanInit 
Time frame within which the application initializes the CAN hard-
ware and becomes ready to receive and transmit frames after the 
Clamp 15/87 is switched on 
02 – 120  
TMessageStart 
Time frame within which all cyclic PDUs have to be transmitted at 
least once 
0 – 300  
TSignalValue 
Time frame within which typically3 all signals shall have plausible 
values. In principle the signals shall have plausible values as soon 
as possible 
02 – 600  
TNetworkStartup Time since the Clamp 15/87 is switched on to the end of TMes-
sageStart 
0 - 420  
1 ECU or system software requirement spec may require more stringent timing for initialization 
2 Timings shall be as fast as possible within the range 
3 For details see ECU specific software requirements. 
 
 
Table T7.1.6a: Startup timing for ECUs with switched power supply1 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 20 页
MSS 20202, 2018-08, page 20 
Copyright Daimler AG 2018 
7.2 
Network sleep / power down 
7.2.1 
Network sleep for ECUs with permanent power supply 
MSS20202-181: 
Generally ECUs shall enable network sleep as soon as possible. 
MSS20202-182: 
Network sleep is an application decision. Each ECU shall determine the criteria 
under which it will be “ready for network sleep”. 
MSS20202-183: 
After startup of the networks ECUs shall prevent network sleep for not less than 
TStartupSleepDelayMin and transmit the stay awake reason "Awake_NwSt".  
MSS20202-184: 
Subsequently ECUs without local stay awake reason shall enable network sleep 
within TStartupSleepDelayMax. 
MSS20202-185: 
ECUs shall transmit the reasons which prevent enabling network sleep (stay 
awake reasons). 
MSS20202-186: 
ECU limp home modes shall not result in preventing network sleep infinitely. 
MSS20202-187: 
 
Name 
Definition 
Minimum [ms] 
Maximum [ms] 
TStartupSleepDelay 
Time frame within the application shall enable 
network sleep after the startup of the network, 
if the application doesn’t require CAN com-
munication. 
The time begins with the first valid CAN frame 
on the network sent by the ECU. 
30001  
6000 
1 This is achieved by the parametrization of CANNM_REPEAT_MESSAGE_TIME 
 
 
Table T7.2.1.1a: Startup sleep delay 
7.2.2 
Network power down procedure for ECUs with switched power supply 
MSS20202-189: 
The CAN communication of ECUs with switched power supply shall end within 
500ms after switching off the power supply.  
MSS20202-190: 
If an ECU with switched power supply has stopped the CAN communication 
because it is powered down, it shall have no effect on other ECUs communicating 
on the CAN network. This may be done by disabling the CAN transceiver before 
powering down. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 21 页
MSS 20202, 2018-08, page 21 
Copyright Daimler AG 2018 
8 
Physical Layer 
MSS20202-192: 
The ECU shall be designed to fulfill the requirements given in [MBN LV 124-1] 
according to the associated Component Requirement Specification. ECUs with 
48V supply shall fulfill  [VDA320]. 
MSS20202-193: 
Because of the coexistence of 12V as well as 48V supplied ECUs in the vehicle: 
ECUs which are supplied solely from the 48 V system shall not damage other 
ECUs of the CAN network in case of ground loss according to [VDA320]. 
MSS20202-358: 
New developed ECUs shall fulfill all of the CAN FD physical Layer requirements 
described in the following chapters. 
8.1 
Permanent power supply 
MSS20202-195: 
 
Classic CAN: 
  
ECUs, which are designed to make use of a 
partial networking mode might need a different 
schematic from ECUs, which are  used as nodes 
with permanent power supply. In addition to this, 
such an ECU needs a transceiver supporting the 
selective wake up function. 
Due to this requirement all ECUs using a standard 
transceiver (see Table T9.1) shall use a hybrid 
layout, which supports transceivers with and 
without partial networking mode. 
CAN FD: 
 
No special requirements. 
 
 
MSS20202-196: 
For dimensioning of the populated devices see Table T8.3. 
MSS20202-197: 
For EMI reasons, all transceiver circuit components shall be arranged within a 
maximum distance of 50mm to the module connector on the circuit board. 
MSS20202-198: 
There shall be no serial devices within the RX and TX lines. 
MSS20202-199: 
The Wake-Pin shall be connected according to the requirements given within the 
transceiver datasheet respectively application notes. 
MSS20202-200: 
The Wake-Pin shall not be left open. 
MSS20202-201: 
Some CAN-transceivers, in combination with modules, use additional wake up 
sources (e.g. a second CAN-transceiver, a LIN-transceiver or if INH of the voltage 
supply is not only controlled by the CAN-transceiver).  
An additional circuit may be necessary to wake up the CAN when a slow starting 
voltage supply is used. The implementation of this circuitry depends on the 
application in which it is used. 
MSS20202-202: 
The necessity of the additional circuitry is based on the specific behavior of 
current CAN transceivers (e.g. undervoltage detection of the transceiver has 
higher priority than the mode control via the STB / EN – pins).  
Please refer to the application notes of the used device for details. 
MSS20202-203: 
If a controller with a voltage supply  < 5V is used (e.g. 3.3V, Figure F8.1a),  
a separate voltage regulator with INH shall be used.  
MSS20202-204: 
INH inputs of both voltage regulators are controlled by the transceiver.  
The controller supply voltage is connected to the pin VI/O for “level adaptation.” 
MSS20202-205: 
Depending on the controller/software, a connection to an interrupt input  
(ERR-INT) may be necessary. 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 22 页
MSS 20202, 2018-08, page 22 
Copyright Daimler AG 2018 
 
 
MSS20202-206: 
 
Classic CAN: 
 
The maximum value of capacitance shall not 
exceed 47pF in total per CAN line (CANHigh resp. 
CANLow to GND). Calculation according to Figure 
F8.1a and Figure F8.1b (capacity of ESD1, ESD2, 
ESD3) has to be done including all optional 
components, but excluding the parasitic 
capacitance of the CMC and the transceiver. 
CAN FD: 
 
No capacitors or ESD protection parts (ESD1, 
ESD2, ESD3 in Figure F8.1a and Figure F8.1b) 
shall be populated to the CAN line circuitry, 
however Pads must be present on PCB Layout. 
 
 
MSS20202-207: 
Any deviation in the transceiver circuit design shall be coordinated with the 
Vehicle Networking Group. 
MSS20202-208: 
For carryover ECUs from previous vehicle lines, the transceiver circuit does not 
have to be modified, if the required settings in section 8.6 “Bit timing and 
Oscillator Tolerance” can be met. 
MSS20202-209: 
 
 
 
Figure F8.1a: Exemplary schematic in case of usage of a standard CAN 
transceiver 
 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 23 页
MSS 20202, 2018-08, page 23 
Copyright Daimler AG 2018 
MSS20202-210: 
 
 
Figure F8.1b: Exemplary schematic in case of usage of a CAN transceiver 
with SPI-Interface 
 
 
 
 
8.2 
Switched power supply 
MSS20202-212: 
The ECU power supply and the CAN interface in these systems are switched with 
a switched power supply. CAN communication is only possible if the switched 
power supply is activated. 
MSS20202-213: 
 
 
Figure F8.2: Exemplary circuit of an ECU with switched power supply 
MSS20202-214: 
For dimensioning of the populated devices see Table T8.3. 
MSS20202-215: 
For EMI reasons, all transceiver circuit components shall be arranged within  
a distance of max. 50mm to the module connector on the circuit board. 
MSS20202-216: 
There shall be no serial devices within the RX and TX lines. 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 24 页
MSS 20202, 2018-08, page 24 
Copyright Daimler AG 2018 
MSS20202-217: 
 
Classic CAN: 
 
The maximum value of capacitance shall not 
exceed 47pF in total per CAN line (CANHigh resp. 
CANLow to GND). Calculation according to Figure 
F8.1a and Figure F8.1b (capacity of ESD1, ESD2, 
ESD3) has to be done including all optional 
components, but excluding the parasitic 
capacitance of the CMC and the transceiver. 
CAN FD: 
 
No capacitors or ESD protection parts (ESD1, 
ESD2, ESD3 in Figure F8.1a and Figure F8.1b) 
shall be populated to the CAN line circuitry, 
however Pads must be present on PCB Layout. 
 
 
 
MSS20202-218: 
Any deviation in the transceiver circuit design shall be coordinated with the 
Vehicle Networking Group. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 25 页
MSS 20202, 2018-08, page 25 
Copyright Daimler AG 2018 
8.3 
Dimensioning 
MSS20202-220: 
 
Device 
Value 
Rating 
Tolerance 
Package 
Mandatory 
footprint 
on PCB 
Populated 
R1, R2 
 1) 
250mW 
 +/- 1% 
≤ 1206 
yes 
1) 
R3, R4 
 2) 
250mW 
 +/- 1% 
≤ 1206 
2) 
2) 
C1 
1) 
≥ 80V 
- 
0603 
yes 
1) 
ESD1 
- 
- 
- 
SOT23 
yes 
5) 
ESD2, 
ESD3 
- 
- 
- 
0805/0603 
yes 
6) 
CMC 
3) 
- 
- 
- 
yes 
yes 
Transceiver 
4) 
- 
- 
- 
yes 
yes 
 
 
Table T8.3: Values of populated devices 
MSS20202-221: 
1)    Affected ECUs which shall populate this device, are listed in Chapter 11 
 
“Termination Classic CAN” and Chapter 12 "Termination CAN FD".  
 
All other ECUs shall provide the footprint. 
MSS20202-222: 
2)   Affected ECUs, which shall provide the footprint and populate the devices, are 
 
listed in Chapter 11 “Termination Classic CAN” and Chapter 12 
 
"Termination CAN FD". 
MSS20202-223: 
3)   Refer to Chapter 10 “Common Mode Choke” 
MSS20202-224: 
4)   Refer to Chapter 9 “Transceiver” 
MSS20202-225: 
5)   Classic CAN:  Optional. All ECUs shall provide the footprint.  
     CAN FD:  The usage of ESD1 requires a written product deviation approval.   
                    Usage of this protection is just allowed, if the need is proven. 
MSS20202-227: 
6)  Classic CAN:  Optional. We recommend using the ESD1 protection instead of                           
the ESD2 and ESD3 protection. In addition "IBK-components" need a written 
product deviation approval when using any ESD protection element. Usage of 
this protection is just allowed, if the need is proven. 
    CAN FD:   EDS2 and ESD3 shall not be populated. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 26 页
MSS 20202, 2018-08, page 26 
Copyright Daimler AG 2018 
8.4 
Galvanic separation between transceiver and microcontroller 
MSS20202-229: 
There shall be no serial devices within the RX and TX lines. 
MSS20202-230: 
If a galvanic separation between the CAN controller and the CAN_H/CAN_L 
busline is needed, it shall be implemented using a transceiver with integrated 
galvanic seperation (refer to chapter 9.1). 
 
8.5 
System Basis Chip / ASIC 
MSS20202-232: 
For ECUs using a system basis chip (SBC), an ASIC with additional functions or 
a combined CAN transceiver (optional combined with e.g. a voltage regulator 
and/or a watch dog), the CAN circuit design shall refer to chapter 8.1. 
 
8.6 
Bit timing and oscillator tolerance 
8.6.1 
Common oscillator requirements 
MSS20202-235: 
The oscillator provides the reference timing for the CAN controller.  
The oscillator frequency shall be chosen as an integer multiple of the baud rate 
and the selected time quanta per bit to ensure proper CAN communication. 
 
MSS20202-236: 
If a PLL is used to generate the clocking for the CAN controller, it shall be proved 
that the overall CAN timing meets the requirements of Table T8.6.2 including the 
effects of temperature and aging. 
 
MSS20202-237: 
Crystals shall be used for clock generation.  
 
MSS20202-238: 
The initial tolerance plus all effects across temperature and aging  
shall be < ± 0.15 %. 
 
MSS20202-239: 
The fulfilment of these requirements shall be confirmed with suitable 
documentation. It is up to the supplier to provide documentation (e.g. 
manufacturing process, sorting process etc.), which proves that all crystals 
delivered to Mercedes-Benz Cars or their suppliers will fulfil this requirement and 
guarantees availability for the life cycle of the vehicle. 
 
MSS20202-240: 
 
Classic CAN: 
 
For ECUs intended to work at 125kBit/s, the 
oscillator shall be chosen, to meet the required 
settings (refer to Table T8.6.2) even if the baud 
rate will be increased to 250kBit/s, 500kBit/s or 
800kBit/s. 
CAN FD: 
 
CAN FD requires higher oscillator frequencies due 
to the higher baud rate and high number of time 
quanta per bit. The controller and the oscillator 
must be aligned to each other with an appropriate 
circuitry. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 27 页
MSS 20202, 2018-08, page 27 
Copyright Daimler AG 2018 
8.6.2 
Bit timing to be used with Classic CAN 
MSS20202-242: 
The Bit timing parameters of the ECU CAN channels shall be set according to the 
following requirements for all the CAN data rates listed in Table T8.6.2 
 
MSS20202-243: 
An increase of the baudrate by software change shall be possible (e.g. 125k - 
500kBit/s; 250k - 500kBit/s) without the necessity to change the oscillator. 
 
MSS20202-244: 
The number of time quanta of phase segment 2 shall be equal to the number of 
time quanta of the synchronisation jump width. 
 
MSS20202-245: 
 
Parameter 
Value 
Baudrate 
125, 250, 500, 800 kBit/s (±0.15%) 
Synchronization 
recessive to dominant 
Sample Point (% of bit time) 
one Sample Point 
Configuration 1: 
Configuration 2: 
Configuration 3: 
81,25% 
80,00% 
80,00% 
Time quanta per Bit NBT 
16 
20 
15 
Synchronization Jump Width 3 
4 
3 
Oscillator tolerance 
0.15% (including initial tolerance, lifetime aging  
and temperature drift) 
Jitter 
±20ns accumulated long term Jitter over 13 bits  
 
 
Table T8.6.2: Timing requirements Classic CAN 
 
 
MSS20202-246: 
Carryover ECUs from previous vehicle lines shall also meet these requirements. 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 28 页
MSS 20202, 2018-08, page 28 
Copyright Daimler AG 2018 
8.6.3 
Bit timing settings to be used with CAN FD 
MSS20202-248: 
 
CAN FD 250kbit/s – 500kbit/s 
Arbitration Field 
Data Field 
Baudrate 
250 kBit/s 
500 kBit/s 
Clock min1 
20MHz 
20MHz 
BRP min 
2 
2 
Time quanta per Bit NBT 
40 
20 
Synchronization Jump Width SJW 
8 
4 
primary Sample Point 
80,0% 
80% 
TSEG1 
31 
15 
TSEG2 
8 
4 
Transceiver Delay Compensation 
TDC usage 
- 
- 
Transceiver Delay Compensation 
TDC Offset in CAN Clk periods 
- 
- 
Oscillator tolerance 
<0.15% (including initial tolerance, lifetime aging, 
temperature drift and tolerances caused by the 
circuitry components) 
Jitter 
< ±10ns accumulated long term Jitter over 13 bits  
1 The clock is measured at the input of the can controller. The possible clock   
   frequencies for this baudrate are 20MHz, 40MHz and 80MHz.  
 
Table T8.6.3a: Timing requirements CAN FD (250k/500kBaud) 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 29 页
MSS 20202, 2018-08, page 29 
Copyright Daimler AG 2018 
MSS20202-249: 
 
CAN FD 500kbit/s - 
1Mbit/s 
Arbitration 
Field 
Data 
Field 
 
Arbitration 
Field 
Data 
Field 
 
 
Configuration 1: 
Configuration 22: 
Baudrate 
500 kBit/s 
1 MBit/s 
500 kBit/s 
1 MBit/s 
Clock1 
40MHz 
40MHz 
80MHz 
80MHz 
BRP 
2 
2 
2 
2 
Time quanta per Bit NBT 
40 
20 
80 
40 
Synchronization Jump 
Width SJW 
8 
5 
16 
10 
primary Sample Point 
80,0% 
75% 
80,0% 
75% 
TSEG1 
31 
14 
63 
29 
TSEG2 
8 
5 
16 
10 
Transceiver Delay 
Compensation TDC 
usage 
- 
Yes (Auto TDC) 
- 
Yes (Auto 
TDC) 
Transceiver Delay 
Compensation TDC 
Offset in CAN Clk 
periods 
- 
30 
- 
60 
Oscillator tolerance 
< 0.15% (including initial tolerance, lifetime aging, temperature 
drift and tolerances caused by the circuitry components) 
Jitter 
< ±5ns accumulated long term Jitter over 13 bits  
1 The clock is measured at the input of the can controller. The possible clock   
   frequencies for this baudrate are 40MHz and 80MHz.  
2 Only allowed if the Bit-Timing-Registers enable these settings and if the TDC  
   register size is 7 bit or higher (SSP range 0…127 or more). 
 
 
Table T8.6.3b: Timing requirements CAN FD (500k/1MBit/s) 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 30 页
MSS 20202, 2018-08, page 30 
Copyright Daimler AG 2018 
MSS20202-250: 
 
 
CAN FD 500kbit/s - 
2Mbit/s 
Arbitration 
Field 
Data 
Field 
Arbitration 
Field 
Data 
Field 
 
Configuration 1: 
Configuration 22: 
Baudrate 
500 kBit/s 
2 Mbit/s 
500 kBit/s 
2 Mbit/s 
Clock1 
40MHz 
40MHz 
80MHz 
80MHz 
BRP 
1 
1 
2 
2 
Time quanta per Bit NBT 
80 
20 
80 
20 
Synchronization Jump 
Width SJW 
15 
≥ 4 
15 
≥ 4 
primary Sample Point 
81,25% 
60% 
81,25% 
60% 
TSEG1 
64 
11 
64 
11 
TSEG2 
15 
8 
15 
8 
Transceiver Delay 
Compensation TDC 
usage 
- 
Yes (Auto TDC) 
- 
Yes (Auto TDC) 
Transceiver Delay 
Compensation TDC 
Offset in CAN Clk 
periods 
- 
11 
- 
23 
Oscillator tolerance 
< 0.15% (including initial tolerance, lifetime aging, temperature 
drift and tolerances caused by the circuitry components) 
Jitter 
< ±5ns accumulated long term Jitter over 13 bits  
1 The clock is measured at the input of the can controller. The possible clock   
   frequencies for this baudrate are 40MHz and 80MHz. 
2 Only allowed if the Bit-Timing-Registers enable these settings and if the TDC  
   register size is 7 bit or higher (SSP range 0…127 or more). 
 
Table T8.6.3c: Timing requirements CAN FD (500k/2MBaud) 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 31 页
MSS 20202, 2018-08, page 31 
Copyright Daimler AG 2018 
9 
Transceiver 
9.1 
Recommended Transceivers/ System Basis Chips 
MSS20202-253: 
 
Supplier: 
Type: 
Max. Baudrate [kBaud]: 
Elmos 
E520.14 
500 
E521.23 
1000 
Infineon 
TLE6251D(1) 
500 
TLE7250G 
TLE7250GVIO 
TLE7250LE/SJ 
1000 
TLE7250V 
TLE7250X 
TLE7251V 
TLE9250 
2000 
TLE9250V 
TLE9250X 
TLE9251 
TLE9251V 
TLE9252V 
TLE9255W 
TLE9260QX 
500 
TLE926xB 
2000 
TLE926x-3B 
TLE927xQX 
TLE9278 
Microchip 
ATA6560/61 
500 
ATA6562/3 
2000 
ATA6564 
ATA6565 
ATA6570 
MCP254x 
NXP 
MC33FS450x(2) 
1000 
MC33FS650x(2) 
TJA1043 
2000 
TJA1044GT 
TJA1044T 
500 
TJA1046 
2000 
TJA1048 
TJA1049 
TJA1052i 
TJA1057GT 
 
 
Table T9.1: Recommended Transceivers and System Basis Chips 
 
MSS20202-370: 
(1) TLE6251DS is not recommended 
(2) Devices with a N in the Part Number are not recommended 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 32 页
MSS 20202, 2018-08, page 32 
Copyright Daimler AG 2018 
MSS20202-367: 
 
Supplier: 
Type: 
Max. Baudrate [kBaud]: 
NXP 
TJA1057T 
500 
UJA1076A 
UJA113x 
UJA1169 
OnSemi 
NCV7342D10R2G 
NCV7351D10R2G 
NCV7351D13R2G 
NCV7351D1ER2G 
NCV7471DQ5R2G 
Texas Instruments 
TCAN1042-Q1(3) 
1000 
TCAN1043-Q1(3) 
TCAN1051-Q1(3) 
 
 
 
Table T9.1: Recommended Transceivers and System Basis Chips 
MSS20202-254: 
(3) Devices with a G in the naming are recommanded for 2MBaud 
 
9.2 
Common Transceiver requirements 
MSS20202-256: 
The transceivers listed, shall only be used for applications given in Table T9.1, 
column "Category" and "Max. Baudrate". 
 
MSS20202-257: 
For selection of a transceiver with selective wake up function (all ECUs 
supporting partial networking feature) please contact the  
Vehicle Networking Group. 
 
MSS20202-258: 
The Transceivers/SBCs used, shall comply with the Directive 2000/53/EC of the 
European Parliament and of the Council on end-of-life vehicles (ELV).  
These compliant types may vary in the denotation (suffix, prefix) compared with 
the recommended types. 
 
MSS20202-259: 
New unlisted Transceivers shall successfully pass the CAN Conformance Test 
performed by an external independent testing institute. 
 
MSS20202-260: 
Furthermore a passed EMC Test according the [IEC TS 62228] performed by an 
external independent testing institute is required. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 33 页
MSS 20202, 2018-08, page 33 
Copyright Daimler AG 2018 
10 Common Mode Choke 
MSS20202-262: 
 
Supplier: 
Type: 
Epcos 
B82789-C0513-N/H(2) 
B82789-C0104-H001/2(2) 
B82789-C0104-N001/2(2) 
TDK 
ACT45B-510-2P 
ACT45B-101-2P 
ACT45C-510-2P 
ACT45C-101-2P 
ACT1210-510-2P 
ACT1210-101-2P 
Murata 
DLW43SH510XK2 
DLW43SH101XK2 
 
 
Table T10: Recommended(1) common mode chokes 
 
MSS20202-263: 
(1) Recommended only regarding electrical parameters; qualification regarding 
   mechanical and temperature requirements shall be done by module supplier. 
MSS20202-264: 
(2) Shall not be used for new developments 
 
 
 
MSS20202-265: 
The CAN short circuit current of the transceiver shall not exceed the rated current 
of the CMC. 
 
MSS20202-266: 
For the required lead-free soldering, take into account the allowed lead-free 
soldering profiles supported by the CMC manufacturer. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 34 页
MSS 20202, 2018-08, page 34 
Copyright Daimler AG 2018 
11 Termination Classic CAN 
MSS20202-268: 
For detailed information about the termination of not listed networks please 
contact the Vehicle Networking Group.  
11.1 Body-CAN 
 
11.1.1 Body-CAN 2 
MSS20202-273: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
BC_F 
62R 
62R 
62R 
62R 
47nF 
 
 
11.1.2 Body-CAN 3 
MSS20202-276: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
 
No Termination in any ECU 
 
 
11.2 CPC-CAN 
MSS20202-278: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
CPC 
62R 
62R 
- 
- 
22nF 
FSCM 
62R 
62R 
- 
- 
22nF 
 
 
11.3 Engine-CAN (PT3) 
MSS20202-282: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
CPC 
62R 
62R 
- 
- 
22nF 
ECM 
62R 
62R 
- 
- 
22nF 
 
 
11.4 Headunit-CAN 1  
MSS20202-284: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
HU 
62R 
62R 
- 
- 
22nF 
IC 
62R 
62R 
- 
- 
22nF 
 
 
11.5 Headunit-CAN 2 
MSS20202-286: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
HU 
62R 
62R 
 
 
22nF 
IC 
62R 
62R 
- 
- 
22nF 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 35 页
MSS 20202, 2018-08, page 35 
Copyright Daimler AG 2018 
11.6 Hybrid-CAN   
MSS20202-288: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
CPC 
62R 
62R 
- 
- 
22nF 
DCDC48 
62R 
62R 
- 
- 
22nF 
 
 
11.6.1 OBD_PTI-CAN 
MSS20202-280: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
EIS 
62R 
62R 
62R 
62R 
47nF 
 
 
11.7 Powertrain-CAN 
MSS20202-290: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
 
No Termination in any ECU 
 
 
11.8 PT_Sensor-CAN / ECM-Periphery-CAN 1 
11.8.1 PT_Sensor-CAN / ECM-Periphery-CAN 1 
MSS20202-293: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
 
No Termination in any ECU 
 
 
 
11.8.2 PT_Sensor-CAN – M254 DESLA 
MSS20202-295: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
ECM 
62R 
62R 
62R 
62R 
47nF 
 
 
11.9 Steering-Wheel-CAN 
MSS20202-297: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
SCCM 
62R 
62R 
- 
- 
22nF 
SWSP 
62R 
62R 
- 
- 
22nF 
 
 
11.10 Suspension_F-CAN 
MSS20202-299: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
MPU_FL 
62R 
62R 
- 
- 
22nF 
MPU_FR 
62R 
62R 
- 
- 
22nF 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 36 页
MSS 20202, 2018-08, page 36 
Copyright Daimler AG 2018 
11.11 Suspension_R-CAN 
MSS20202-301: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
MPU_RL 
62R 
62R 
- 
- 
22nF 
MPU_RR 
62R 
62R 
- 
- 
22nF 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 37 页
MSS 20202, 2018-08, page 37 
Copyright Daimler AG 2018 
12 Termination CAN FD 
MSS20202-303: 
For detailed information about the termination of not listed networks please 
contact the Vehicle Networking Group.  
12.1 Body-CAN 1 (250k/500kBit/s) 
MSS20202-305: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
EIS 
62R 
62R 
 
 
22nF 
BC_F 
62R 
62R 
- 
- 
22nF 
 
 
12.1.1 CAL-COC-CAN (500k/1000kBit/s) 
MSS20202-384: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
CAL 
62R 
62R 
 
 
22nF 
CLT_C2 
62R 
62R 
- 
- 
22nF 
 
 
12.1.2 CAL-DL-CAN (500k/1000kBit/s) 
MSS20202-385: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
CAL 
62R 
62R 
 
 
22nF 
CLT_FL 
62R 
62R 
- 
- 
22nF 
 
 
12.1.3 CAL-DR-CAN (500k/1000kBit/s) 
MSS20202-386: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
EIS 
62R 
62R 
 
 
22nF 
CLT_FR 
62R 
62R 
- 
- 
22nF 
 
 
12.1.4 CAL-R-CAN (500k/1000kBit/s) 
MSS20202-388: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
CAL 
62R 
62R 
 
 
22nF 
CLT_B2 
62R 
62R 
- 
- 
22nF 
 
 
12.1.5 ECM-CAN (500k/2000kBit/s) 
MSS20202-390: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
CAL 
62R 
62R 
 
 
22nF 
CLT_B2 
62R 
62R 
- 
- 
22nF 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 38 页
MSS 20202, 2018-08, page 38 
Copyright Daimler AG 2018 
12.1.6 ECM-Periphery2-CAN (500k/1000kBit/s) 
MSS20202-378: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
ECM 
62R 
62R 
- 
- 
22nF 
SCRCM 
62R 
62R 
- 
- 
22nF 
 
 
12.2 INV-CAN (500k/2000kBit/s) 
MSS20202-307: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
INV1 
62R 
62R 
 
 
22nF 
CPC 
62R 
62R 
- 
- 
22nF 
 
 
12.3 Energy-CAN (500k/1000kBit/s) 
MSS20202-309: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
DCDC 
62R 
62R 
 
 
22nF 
CPC 
62R 
62R 
- 
- 
22nF 
 
 
12.4 HAD-Backup-CAN (500k/1000kBit/s) 
MSS20202-366: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
ESP 
62R 
62R 
- 
- 
22nF 
POS 
62R 
62R 
- 
- 
22nF 
 
 
12.5 Periphery-CAN (500k/1000kBit/s) 
MSS20202-311: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
HLM_R 
62R 
62R 
 
 
22nF 
EIS 
62R 
62R 
- 
- 
22nF 
 
 
 
12.6 PT-Periphery-CAN (500k/1000kBit/s) 
MSS20202-313: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
ECM 
62R 
62R 
62R 
62R 
47nF 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 39 页
MSS 20202, 2018-08, page 39 
Copyright Daimler AG 2018 
12.7 Radar_F-CAN (500k/2000kBit/s) 
MSS20202-315: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
MMR_FL 
62R 
62R 
 
 
22nF 
MMR_FR 
62R 
62R 
- 
- 
22nF 
 
 
12.8 Radar_R-CAN (500k/2000kBit/s) 
MSS20202-317: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
MMR_RL 
62R 
62R 
 
 
22nF 
MMR_RR 
62R 
62R 
- 
- 
22nF 
 
 
12.8.1 Steering_Wheel-CAN (250k/500kBit/s) 
MSS20202-392: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
SCCM 
62R 
62R 
 
 
22nF 
SWSP 
62R 
62R 
- 
- 
22nF 
 
 
12.9 TCM-CAN (500k/2000kBit/s) 
MSS20202-319: 
 
ECU 
R1 
R2 
R3 
R4 
C1 
CPC 
62R 
62R 
- 
- 
22nF 
TCM 
62R 
62R 
- 
- 
22nF 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 40 页
MSS 20202, 2018-08, page 40 
Copyright Daimler AG 2018 
13 Wiring and Connectors for CAN FD 
13.1 CAN FD Cables 
MSS20202-322: 
CAN FD cables shall fulfill all requirements described in table T13.1a: 
MSS20202-323: 
 
temperature 
characteristic impedance  
20°C 
120 Ohm (+5 Ohm/ -5 Ohm) 
-40°C ... +125°C 
120 Ohm (+20 Ohm / -10 Ohm) 
further requirements 
group delay (-40°C .. +125°C) 
max. 5,5 ns/m 
insulation material 
PVC not allowed 
 
 
Table T13.1a: CAN FD cable requirements 
 
 
 
MSS20202-324: 
 
Type 
Recommended for 
FLCuSn03R9Y-2×0,13mm²*+ jacket 
(unshielded jacketed twisted pair; 
a PVC sheath would be allowed) 
CAN FD  2MBaud  
engine compartment except engine block 
FLR2X-2×0,35 mm²** + jacket 
(unshielded jacketed twisted pair; 
a PVC sheath would be allowed) 
CAN FD  2MBaud  
engine block 
FLCuSn03R9Y-2×0,13mm²* 
(unshielded jacketed twisted pair; 
a PVC sheath would be allowed) 
CAN FD  2MBaud  
Interior compartment 
 
 
Table T13.1b: Recommended CAN FD cables 
 
MSS20202-325: 
* The given code defines the principal structure and the principal composition of 
the used materials of the recommended cables. In addition to this it is requested, 
that the cables conform  to the electrical parameters (characteristic impedance, 
propagation speed etc.) given in table T13.1a 
MSS20202-359: 
** The cable shall only be used in a point-to-point topology (only two ECUs) with 
termination of 120Ohms at both ends and a maximum transmission line length of 
7,5 meters. 
MSS20202-326: 
Other cable types might only be used on request. 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 41 页
MSS 20202, 2018-08, page 41 
Copyright Daimler AG 2018 
13.2 Connectors for CAN FD 
13.3 Connector Requirements and Approved Connectors 
MSS20202-329: 
The connector systems shown in Table T13.3 are approved to be used for 
CAN FD data communication systems if the positioning of the pins (pin allocation 
within the connector) and the assembly of the cables (attachment of the pins to 
the cable) comply with the requirements specified in the following chapters. 
Other connector systems might only be used on request. 
MSS20202-330: 
 
Supplier 
Type 
Tyco / AMP 
MICRO QUADLOCK SYSTEM (MQS) 
2.54 x 2.54 mm centerline, matrix alignment 
2.54 mm centerline, line alignment 
Tyco / AMP 
NANO MICRO QUADLOCK SYSTEM (Nano MQS) 
1.8mm x 1.5mm centerline, matrix alignment 
1.8mm centerline, line alignment 
 
 
Table T13.3 Connector system for CAN FD 
 
 
13.3.1 Pin Allocation within the Connector 
MSS20202-332: 
CAN FD ECUs may use connectors with one or more rows of pins.  
In the following the pin allocation is specified exemplarily for a connector having 
two rows of multiple pins. The principle however is also applicable to single row 
connectors or multiple row connectors. 
 
MSS20202-333: 
All contacts needed for one CAN FD signal shall use directly adjacent pins within 
one row of pins. 
 
MSS20202-334: 
There shall not be any other signal pins in-between the CAN-High and CAN-Low 
buslines.  
 
MSS20202-335: 
The proper connection of the CAN-High and CAN-LOW pins to the termination 
and transceiver are crucial for EMC and signal integrity. 
 
MSS20202-336: 
If multiple rows are used, the pins (CAN-L 1, CAN-H 1) may be located in line 
(same row, Figure F13.3.1a) with the pins (CAN-L 2, CAN-H 2), as well as 
located on the opposite, as shown in Figure F13.3.1a. 
 
MSS20202-337: 
The ECUs described in Table T13.3.1 shall populate four Pins for two CAN FD 
signals, with the described requirements. 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 42 页
MSS 20202, 2018-08, page 42 
Copyright Daimler AG 2018 
MSS20202-338: 
 
ECU 
Required Pins 
Subnetwork 
BC_F 
6 
Body-CAN1 
BC_R 
6 
Body-CAN1 
BMS 
4 
Energy-CAN 
DCB 
4 
Energy-CAN 
ECM 
6 
ECM-Periphery1-CAN 
EIS 
6 
Body-CAN1 
EIS 
4 
Radar-F-CAN 
EIS 
4 
Radar-R-CAN 
INV2 
4 
INV-CAN 
OBD 
4 
Energy-CAN 
WPT 
4 
Energy-CAN 
 
 
Table T13.3.1: ECUs with four/six Pins as a mandatory requirement 
 
MSS20202-339: 
 
 
CAN_H_2
CAN_L_1
CAN_H_1
CAN_H_1
CAN_H
CAN_H
CAN_H_B
CAN_H_C
CAN_H_D
CAN_L_C
CAN_H_A
CAN_H_A
CAN_H_C
CAN_L_A
CAN_L_2
CAN_L_2
CAN_L_1
CAN_H_2
CAN_L
CAN_L
CAN_L_B
CAN_H_D
CAN_L_D
CAN_L_D
CAN_L_A
CAN_H_B
CAN_L_C
CAN_L_B
pin used for CAN signal
pin used for other signal
general CAN node 
CAN node with
multiple CAN channels
in-between CAN node 
(e.g. for line topologies)
caption:
CAN_H_1, CAN_L_1 and 
CAN_L_1, CAN_L_2
are interconnected inside the ECU
CAN_H_1, CAN_L_1 and 
CAN_L_1, CAN_L_2
are interconnected inside the ECU
A, B, C, D denote connectors for separate CAN channels
inside one ECU (e.g. gateway etc.)
A, B, C, D denote connectors for separate CAN channels
inside one ECU (e.g. gateway etc.)
alternative solution:
alternative solution:
alternative solution:
continuation of connector
only applicable with 
uprightly mounted connectors!
only applicable with 
uprightly mounted connectors!
only applicable with 
uprightly mounted connectors!
Schreiner 2015/07/21
 
Figure F13.3.1a Pin allocation within one connector 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 43 页
MSS 20202, 2018-08, page 43 
Copyright Daimler AG 2018 
MSS20202-340: 
If the connector is mounted upright onto the PCB (as shown in Figure  
F13.3.1b a)), with all pins having the same length, it is possible to align the 
CAN FD signal pins vertically or horizontally within the connector matrix (see 
alternative solution in Figure F13.3.1a). 
 
 
MSS20202-341: 
However if the connector is mounted horizontally on the PCB with pins bend by 
90° to the PCB (as shown in Figure F13.3.1b b)) there is a length difference 
between the pins of the 1st and the 2nd row of pins. In this case it is requested to 
place all CAN FD signals within the row with the shortest pin length (i.e. 2nd row 
in Figure 13.3.1b b)).  
 
MSS20202-342: 
In this case pins of different lengths shall not be used for one CAN signal  
(e.g. CAN-Low 1st row and CAN-High 2nd row in Figure F13.3.1b b)).  
This might yield in asymmetry and increase EM emissions. 
 
 
MSS20202-343: 
 
 
PCB
pin connector
socket
pin connector
socket
1  row of pins
st
1  row of pins
st
2  row of pins
nd
2  row of pins
nd
bend
a) upright connector mounting
b) 90°-bend connector mounting
 
Figure F13.3.1b: Upright and 90°-bend connector mounting on PCB 
 
MSS20202-344: 
It is recommended to keep contacts used for CAN FD away from other contacts 
conducting fast transient or pulsed signals (e.g. PWM signals, switched inductive 
loads, etc.) within one connector. 
 
13.3.2 Cable and Connector Assembly 
MSS20202-346: 
The assembly of ECU connector and cable is shown in Figure F13.3.2a - an 
inline connector assembly is shown in Figure F13.3.2b.  
 
MSS20202-347: 
The untwisted cable segment has to be regarded electrically as part of the 
connector and thus influences the connector’s characteristics. In order to meet 
signal integrity and EMC requirements the following dimensions of the cable 
assembly are required (for both cases: ECU and inline connector): 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 44 页
MSS 20202, 2018-08, page 44 
Copyright Daimler AG 2018 
MSS20202-348: 
 
lmax, untwisted + unsheathed 
 30mm +10mm 
 
 
Table T13.3.2: Dimensions of cable assembly 
 
MSS20202-349: 
 
 
Figure F13.3.2a: Pin assembly of ECU connector and CAN FD cable, blue 
(CAN-High),  
 
 
 
 yellow (CAN-Low) 
 
 
MSS20202-350: 
 
 
Figure F13.2.3b: Pin assembly of CAN FD inline connector and cable, blue 
(CAN-High),  
 
 
 
  yellow (CAN-Low) 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 45 页
MSS 20202, 2018-08, page 45 
Copyright Daimler AG 2018 
MSS20202-351: 
End of Main Document 
# # # # # 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 46 页
MSS 20202, 2018-08, page 46 
Copyright Daimler AG 2018 
Annex A (informative) Revision History 
MSS20202-353: 
 
Version 
Date 
Description 
Author 
1.0 
2015-10 
Initial Version 
Based on MSS 10416 
TH 
1.1 
2015-12 
Enhanced requirement details for 
 
20202-75 
 
20202-78 
Minor rephrasing for 
 
20202-101 
 
20202-104 
 
20202-183 
 
20202-184 
Renaming SWSP-CAN in Steering-Wheel-CAN 
 
20202-296 
TH 
 
 
 
 
 
 
 
LD 
 
1.2 
2016-06 
Renaming Backup-CAN in HAD-Backup-CAN 
 
MSS20202-269 
 
Changed the termination for several networks 
 
MSS20202-278 
 
MSS20202-284 
 
MSS20202-311 
 
MSS20202-313 
 
Removed  MSS20202-226 
 
MSS20202-253: 
Removed the SBC “UJA1069” (NXP) in T9.1  “Recommended Trans-
ceivers and System Basis Chips”  
 
MSS20202-324: 
Changed the requirements in T13.1b “Recommended CAN FD cables”  
 
MSS20202-330: 
Added NanoMQS to T13.3 “Connector system for CAN FD” in  
 
MSS20202-338: 
Added ECUs “EIS”, “BC_F”, “BC_R”, “OBD”, “WCP”, “DCB”, “BMS” in 
T13.3.1 “ECUs with four/six pins as mandatory requirement”  
 
Added  MSS20202-358 
 
Changed the “HAD-Backup-CAN” to a CAN FD network 
 
Removed MSS20202-269 
 
Removed MSS20202-270 
 
Added MSS20202-365 
 
Added MSS20202-366 
 
Minor rephrasings 
CANNM_IMMEDIATE_RESTART_ENABLED set to “Disabled” 
LD 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
TH 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23


### 第 47 页
MSS 20202, 2018-08, page 47 
Copyright Daimler AG 2018 
Version 
Date 
Description 
Author 
2.0 
2018-08 
MSS20202-71: minor rephrasings 
MSS20202-73: removed 
MSS20202-82: adapted the table to be compliant to the PDU-header 
mechanisms 
MSS20202-84 to 91: deleted the Back-to-Back requirement. The re-
quirement will be fulfilled sufficient by complying with the “start delay 
times” and “minimum send delays” defined in the NCD 
 
 
MSS20202-51: added [DSUDS] and [CANDELA_TEMPLATE] 
MSS20202-144: added the specific parameters for 667kBd 
MSS20202-394 to 396: added a paragraph for the Wake-up behavior of 
the ECU 
MSS20202-226: removed 
MSS20202-249: added the specific parameters for 80MHz Clocks 
MSS20202-250: added the specific parameters for 80MHz Clocks 
MSS20202-253: update of the transceiver recommendation list 
MSS20202-254: Changed the additional information for MSS20202-254 
MSS20202-270: removed 
MSS20202-370: added additional information for MSS20202-254 
MSS20202-367: added a continual table for MSS20202-254 
MSS20202-273: changed the termination concept 
MSS20202-288: changed the termination concept 
MSS20202-305: changed the termination concept 
MSS20202-307:changed the name of the network 
MSS20202-324: changed the cable types and added jacket cables 
MSS20202-338: updated the list of ECUs with four/six Pins as           
mandatory requirement 
MSS20202-366: added HAD-Backup-CAN 
MSS20202-384: added CAL-COC-CAN 
MSS20202-385: added CAL-DL-CAN 
MSS20202-386: added CAL-DR-CAN 
MSS20202-388: added CAL-R-CAN 
JS 
 
 
 
 
 
 
 
 
LD 
 
 
 
 
Author 
Information 
LD 
Lukas Donat 
JS 
Janosch Schwarz 
TH 
Tobias Hohmann 
 
MSS20202-354: 
End of Annex A 
# # # # # 
 
Uncontrolled copy when printed
RD/UBF: Stefan Kehrt, 2018-11-23

