# GMW_14082_CH_2005-10_通用硬件和接口要求和指南.pdf

## 文档信息
- 标题：Microsoft Word - GMW14082Oct2005.doc
- 作者：zz0kjt
- 页数：76

## 文档内容
### 第 1 页
  
 
 
通用硬件以及接口的要求和指南  
 
 
 
1 引言 
1.1 范围。 本文档是所有通用电气硬件要求的首要文件，
是供应商收到的文件的一部分，其中包含车用电气/电子
部件（E / E 设备）的报价请求。 本文档适用于所有带有
电气/电子内容的组件，并应在每个 SSTS / CTS 中引用。 
1.2 任务/主题。 该规范描述了适用于大多数 ECU 的硬件
要求。 该规范还提供了针对大多数标准输入和输出指定
的描述，推荐电路和参数。 这些定义旨在用于车身，内
部和外部 ECU 
1.3 要求措辞公约。 在本文档中，适用以下约定： 
“Shall”一词应以下列方式使用： 
a. 说明对 ECU 或构成 ECU 的接口的约束要求，其
可通过外部操纵和/或观察输入或输出来验证。 
b.说明对 ECU 的要求文件的约束要求，该文件可通
过审查文件来验证。 
“Must”一词应用于表明对本说明书范围之外的组
件/设备的约束要求。 
“Will”一词应用于陈述不可改变的物理定律 
“Should”一词表示偏好或所需的一致性。 
注意：如果本规范的文本与此处引用的文档之间
存在冲突，则以本规范的文本为准。 
注意：除非获得特定豁免，否则本规范中的任何
内容均不得取代适用的法律和法规。
2 参考 
注意：除非另有说明，否则仅适用最新批准的标准。 
2.1 外部标准/规范。 
ANSI/IPC-J-STD-001D 
IPC-7351 IPC-6012 
2.3 GM 标准/规范。 
GMW3001 
GMW3097 
GMW3059 
GMW3103 
GMW3091 
GMW3172 
 
3 要求 
3.1 系统/子系统/组件/部件定义。 
3.1.1 外观。不适用。 
3.1.2 内容。不适用。 
3.1.3 环境。根据 GMW3172，电子和电气子系统/
组件在承受负载时应通过环境和耐久性验证。根据
GMW3172，要求在 CTS / SSTS 中通过代码字母
定义。 
******* 功能分类。功能分类是用于在暴露于测试条
件或现实操作条件下定义汽车电气/电子设备[E / E
设备]的功能的功能性能状态分类（FSC）的一般方
法。 
*******.1 类别定义。 
FSC_A：E / E 设备/系统的所有功能在暴露于干扰
期间和之后按设计执行。 
FSC_B：E / E 设备/系统的所有功能在暴露期间按
设计执行;但是，一个或多个功能可能超出指定的容
差。移除暴露后，所有功能自动恢复到正常范围内。
记忆功能应保持在 A 级。当电压超出为 FSC_A 定
义的范围时，E / E 设备/系统不应输出任何错误的
驱动信号，错误的串行数据消息，
 
 
©版权所有 2005 通用汽车公司保留所有权利 
 
2005 年 10 月 
                         发起部门: 北美工程标准 
    1/74 
 
 
通用规范 
电气功能 
 
 
 
GMW14082 
全球工程标准 


### 第 2 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
2/74 
2005 年 10 月 
 
 
 
错误的故障代码或其他错误的 I / O 命令或状态。
FSC_C：E / E 设备/系统的功能在暴露期间不按设
计执行，但在暴露消除后自动恢复正常操作。 当电
压超出为 FSC_A 定义的范围时，E / E 设备/系统不
应输出任何错误的驱动信号，错误的串行数据消息，
错误的故障代码或其他错误的 I / O 命令或状态。 
FSC_D：E / E 设备/系统的功能在曝光期间不按设
计执行，并且在移除曝光并通过简单的“操作员/使
用”操作重置设备/系统之前不会恢复正常操作。 
FSC_E：E / E 设备/系统的一个或多个功能在曝光
期间和曝光后不能按设计执行，如果不修理或更换
E / E 设备/系统，则无法恢复正常操作。 
3.1.4 接口。 
1.1.1.1 电气接口定义。 
1.1.1.1.1 电气信号的一般命名。 
Vs：电压供应，通用电压源来自车辆电池。 
Vs_prot：电源，通用电压源，反极性保护。 
Vs_sw：开关电源，由软件控制开/关，反极性
保护。 
Vcc：开关稳压电源，通常为 5V。 
Vcc_sw：开关稳压供电电压，通常为 5V，由
软件控制开/关。 
Vbatt：车辆电池电压，来自车辆电池的永久
供电。
Vbatt_prot：车辆电池电压，来自车辆电池的永久
供电，反极性保护。 
Vrun / crank：来自 Vbatt 源的点火钥匙控制电池。 
Vacc：来自 Vbatt 源的点火钥匙控制电池。 
Vign：点火钥匙控制电源，通用型（Vrun /曲柄或
Vacc），反极性保护。 
Gnd：接地，未指定类型或接地位置。 
信号接地：ECU 的本地信号接地。  
电源接地：ECU 的本地电源地。 Battery_minus：
车辆为 0 V，全球 0V。  
Body_gnd：未指定的接地螺栓接地。 
BIW_gnd：白底车身。接地线与电池负极的连接体
上的电气点。 
Engine_gnd：发动机缸体上未涂漆的公共电气连接
点。 
Ui：接口信号，输入电压。 
Uo：接口信号：输出电压。 
U_Pn：正电池极和 ECU 电源输入引脚之间的电压
降。 
Usat：电压降完全接通晶体管。 
U_GNDn：负极电池极和 ECU 接地输入引脚之间
的电压降。 
Is：供电电流，通用。 
Io：接口信号输出电流。 
Ii：接口信号输入电流。 
3.1.4.1.2 电压和电流方向。


### 第 3 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
3/74 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
图 1: 输入和输出电压以及使用的直流电流定义 
3.1.4.1.3 电压供应以及信号噪音模型. 电力和信号
分配系统的设计限制是： 
给 出 的 值 是 针 对 不 包 括 曲 柄 序 列 的 稳 态 条 件
(>500ms) 
a.任何平台或动力总成 ECU 之间的 U_P 电压偏
移：: 1.0V. 
b. 任何接地到发动机缸体的动力总成 ECU 之间
的 U_GND 电压偏移：0.5V。 
c. 任何平台 ECU 之间的 U_GND
0.8V。 
d. 任何平台和动力总成 ECU 之间的 U_GND 电压
偏移：1.0V。
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
图 2：电压供应和信号噪声模型
HSD* 
Rpu* 
Io 
Ii 
Input 
Output 
+ 
+ 
LSD* 
Co 
Uo 
Ui 
Ci 
Rpd* 
- 
- 
*=One of or both 
Power Train Power Signal Distribution 
GMW8763 
* 
* = DC current directions 
* 
Platform Power Signal Distribution 
+ 
U_P1=0 to +1.0V 
- 
+ 
U_P2=0 to +1.0V 
- 
+ 
U_P3=0 to +1.0V 
- 
+ 
U_P4=0 to +1.0V 
- 
ECU1 
ECU2 
ECU3 
ECU4 
+ 
   
+ 
+  
- 
 
0 to +0.2V 
* 
+ Un - 
   
+ 
+ 
Ui1 
Ui2   * 
+ 
Starter 
Motor 
- 
Uo3 
Ui4 
- 
- 
VB 
+ 
Generator 
- 
- 
- 
- 
UB 
+ 
signal_gnd_1 
+ 
signal_gnd_2 
  
 
+    signal_gnd_3 
U_GND3=0 to +0.8V 
signal_gnd_4 
- 
- 
* 
U_GND1=0 to +0.5V 
ground cable 
voltage drop 
+  
- 
 
-0.2V to +0.5V 
+1.0V@Crank 
ground cable 
voltage drop 
-  
+    * 
0 to +0.2V 
-     Including ground -wire, -bolts and -sheet metal voltage drops 
+ 
U_GND4=0 to +0.8V 
- 
* 
U_GND2=0 to +0.5V 
Power Train Ground Signal Distribution 
Engine_gnd 
Engine block ground 
Battery minus 
BIW_gnd 
Platform Ground Signal Distribution 
V_gnd_ref= 0 V 
Body In White ground 
Is_1 
Gnd_1 
Vs_1 
Is_2 
Gnd_2 
Vs_2 


### 第 4 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
4/74 
2005 年 10 月 
 
 
 
3.1.4.2 供应线。 
3.1.4.2.1 系统电压要求。 本节中规定的所有数值，
范围和公差均适用于整个操作环境（例如，电压，
温度，湿度等）。它们也适用于用于所附组件的所
有正常环境条件。 
所有电压均相对于 ECU 上的电源接地电气接口输
入连接进行测量。 
向 ECU 提供电力的电压源（即，车辆电池，发电
机或跳跃启动）将在本文件中统称为系统电压。 
系统电压可能在 GMW3172 中定义的范围内变化。
对于在 ECU 内使用，可以从电池（Vbatt）或点火
开关连接（Vign）导出系统电压。 它也可以通过外
部负载组件应用于 ECU 接口。 
施加系统电压的具体路径将在特定电接口或下面的
通用要求部分中定义。 
所有外部电气接口应满足 GMW3172 的电气要求和
以下条件： 
a.根据带有发电机的电池供电系统的性质，汽车长时
间存在+0 至+ Vmax 范围内的任何电压。 系统上电压
变化的实际上升和下降时间以及持续时间在此范围内
是随机的。 
b.具有供电部分或 MOSFET 栅极驱动器的 ECU
在+16 至+ 26.5V 范围内由硬件自动关闭，应在
实际开关电压电平附近以 1 伏/分钟的缓慢变化
供电电压进行测试。
注意：此测试的目的是避免在模拟量程模式下运行开
关晶体管导致的问题，其中开关损耗处于毁灭性状态。 
c. 在正常工作电压范围（Vmin 至 Vmax）内，ECU
应能够在不损坏的情况下同时驱动系统电压，电池和
点火输入以及通过外部负载上拉至系统电压的任何 I 
/ O 信号。 这种系统电压的驱动可以包括信号中的多
次“反弹”转换（例如，由于电池电缆与处于运行位
置的点火开关连接），GMW3172。 
d. 每个接口都应能承受车辆接地电压范围（1V）内
任何输入或输出连接的电压而不会损坏。 
3.1.4.2.2 正常工作电压范围。 GMW3172 工作电压范围代
码应在每个组件技术规范（CTS）或子系统技术规范（SSTS）
中定义。 
对启动程序必要或必须的子系统应具有符合代码字母 A 或
B 的电气负载要求。 
车辆上使用的其他电子子系统/组件将具有关于具备 FSC_A
性能的微控制器和存储器功能的电气代码字母 B（+ 6V 
Vmin）。 
所有的传感器信号调节的模拟电压/电流值或脉冲计数（包
括电子电路/芯片），默认情况下，还应继续在规格下降到
+ 6.0v 系统电压操作（GMW3172 进行电力负荷代号 B）或
应关闭下面 fsc_a 系统电压和被视为一个有效的信号。 
当电压超出为 FSC_A 定义的范围时，电子子系统/组件不得
输出任何错误的驱动信号，错误的串行数据信息而不设置
DTC，或其他错误的 I / O 命令或状态。


### 第 5 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
5/74 
 
 
 
 
 
表 1：工作电压范围代码 
Vnom 
14.5 
V 
 
 
 
 
 
 
 
 
稳态供
电电压 
-13 < 0 
[V] 
0 < 4.5 
[V] 
4.5 < 6 
[V] 
6 < 9 
[V] 
9 < 10 
[V] 
10 < 12 
[V] 
12 < 16 
[V] 
16 < 18 
[V] 
Vmin [V] 
Vma
x 
[V] 
 
 
 
 
 
 
 
 
 
 
代 码 
GM
W 
3172 
↓ 
FSC_ 
FSC_ 
FSC_ 
FSC_ 
FSC_ 
FSC_ 
FSC_ 
FSC_  
 
A 
C 
C 
A 
A 
A 
A 
A 
C 
4.5 
16 
B 
C 
C 
C 
A 
A 
A 
A 
C 
6 
16 
C 
C 
C 
C 
C 
A 
A 
A 
C 
9 
16 
D 
C 
C 
C 
C 
A 
A 
A 
A 
9 
18 
E 
C 
C 
C 
C 
C 
A 
A 
C 
10 
16 
F 
C 
C 
C 
C 
C 
C 
A 
C 
12 
16 
Z 
C 
C 
已定义 
by 
SSTS 
or 
CTS 
in 
详述 
 
 
3.1.4.2.2.1 曲柄期间的正常电池电压。 
 
 
 
T im e  in m illise c o n d s ® 
 
 
图 3：曲柄期间的正常电池电压 
ECU 的正常曲柄工作电压范围由 GMW3097 测
试脉冲 4 定义。除非在 CTS / SSTS 中另有规定，
否则+ 5V 的默认停留时间应为 15 ms。 
图 3 显示了曲柄序列的每个部分期间的最大电压
降。 
关于时间和电压序列的任何电压波形可以存在于
曲柄期间绘制的该包络线上方，这取决于许多因
素，例如环境温度，油温，自上次曲柄起的时间，
电池充电状态，驾驶员钥匙关闭时间等。
GMW3097 脉冲 4 测试案例涵盖了有限数量的情况，
以显示正确的功能。 必须仔细分析和模拟真实的硬
件设计，以找出可能导致意外行为和/或在曲柄期间
设置意外的永久性故障代码的设计引起的任何缺陷。 
3.1.4.2.3 电池输入电气接口。 ECU 应提供至少一
（1）个电池输入。 在本文档中，电池输入信号上
驱动的电压将被称为 Vbatt。如有特殊需要，电池
输入信号应作为软件控制功率建模和高端驱动器电
气接口的电源。
12 V 
 
- 
Syste m 
Vo lta g e 
6V  
5V  
5 
15-4 0 
50  
1 0 ,0 0 0 
10 0 


### 第 6 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
6/74 
2005 年 10 月 
 
 
 
它可用于点火独立非易失性数据存储器保留。 因此，
它可以由 ECU 内的各种内部电路使用。 
电池输入信号将从汽车发电机/电池电压源驱动，范围
从反向电池状态到最大 Vbatt，跳转启动条件。 由于
三相发电机操作，电池输入信号可能具有非正弦纹波
GMW3172。 
Vbatt 到 ECU 内部 Vbatt_prot 的反极性保护机械化取
决于 Vmin 要求。 可以使用以下内容并按电压降降低
的顺序列出：可以使用 Si 二极管，肖特基二极管或 P
沟道 MOSFET。 
 
* = Complete transient protection will depend on GMW3097 results 
 
 
 
图 4：电池输入电气接口 
3.1.4.2.4 点火输入电气接口。 如果 ECU 应用要求，
ECU 应提供运行/曲柄（Vrun /曲柄）和附件（Vacc）
点火输入电气接口。 
应按模拟输入部分所述监控运行/曲柄和附件输入信号。 
每个点火输入电气接口应具有对地的电阻负载，以在
所有工作条件下具有规定的电压状态。 应选择对地的
电阻负载，以提供 1 秒的最大时间常数。 （见图 5） 
由于运行 / 曲柄和附件输入信号都是电池（Vbatt）的
机械开关版本，因此它们都将在相同的电压范围内驱
动至 Vbatt，并具有相同的频率（纹波）内容。 
注意：电池，运行/曲柄和附件输入信号可能无法在所
有时间点被驱动为相同的电压。
 
 
图 5：点火输入电气接口 
3.1.4.2.5 潜行电路，供电路径。潜行电路是在某些条件下
非预期电流可以流过的路径。由两个或多个外部电源信号
提供的 ECU 供电电压接口产生的潜在电路不应造成损坏或
不良行为。 
为了防止由于潜行电路而导致电子控制单元之间的意外电
流流动，所有从 Vrun_曲柄，Vacc 或其他点火输入电气接
口引出的电源线应通过二极管（Vr> 250V）或功率适用的
等效设备去耦对于所涉及的电流（见图 5） 
3.1.4.2.6 电源跟踪要求。电源走线应能够连续承受所有负
载的所有规定电流之和的 1.5 倍。根据 GMW3172，这些走
线应能承受电池和接地短路测试而不会损坏。 
3.1.4.2.7 电源中断的抗扰度。在任何点火输入接口控制的
供电线路（Vign）的任何中断条件下，应采取预防措施以
满足 FSC_C 的要求。 
在点火线关闭时执行关闭程序的部件必须通过硬件和/或软
件（采样）以至少 30ms 的持续时间过滤点火线状态。 
Vrun / 曲柄或 Vacc 电源线的任何中断小于 30 ms 都不会导
致内部状态发生变化，除非识别出有效的状态变化（键
转）。 
3.1.4.2.8 电源地电气接口。 ECU 应提供满足以下要求的电
源接地电气接口（电源接地）： 
a.电源接地电气接口外部连接的电压应作为所有 ECU 电气
测量的接地参考。 
b.它应能够在不损坏的情况下下沉所有 ECU 返回电流的总
和。
  
 
      Vrun/crank 
   
Vrun/crank_prot 
   Vacc 
Vacc_prot 
* 
* 
     Power_gnd 
Signal_gnd 
 
* = Complete transient protection will depend on GMW3097  results 
Alternative reverse protection schemes 
Vbatt 
Vbatt_prot 
* 
* 
     Power_gnd 


### 第 7 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
7/74 
 
 
 
3.1.4.2.9 信号返回电气接口。 ECU 应提供满足以下
要求的信号返回电气接口（Signal_rtn）： 
a。信号返回接口应在 ECU 内部与数字和电源地分
开。数字，电源和信号接地应在尽可能靠近 ECU 连
接器的单点连接在一起。 
b。它应提供由 CTS 应用程序 I / O 表定义的外部连
接数（实际使用的数量可能因应用程序而异）。 
C。它应能够在不损坏的情况下下沉所有电源输出
电气接口驱动的所有电流的总和。 
d。它应能够吸收所有功率输出电气接口驱动的最
小所需电流，相对于电源接地的最大电压降为
0.01V。 
e。如果 ECU 外部有多个接地接口，则电气接地概
念应由负责的 GM 电气架构工程师批准。 
3.1.4.2.10 潜行电路，接地路径。 当 ECU 的电
源接地电气接口连接到两个或多个外部接地信号
时产生的潜在电路不应造成任何损坏或不良行为。 
此外，还应努力防止通过 MOSFET 和类似组件
中的本征二极管发生的潜行电路。
ECU 接地电气接口的设计应针对潜在的潜行电路进
行评估，并在最终确定之前由相应的 GM 工程师批
准。 
3.1.4.2.11 功率输出电气接口。如果需要，ECU 应
提供这些类型的缓冲功率输出，以供应外部从属单
元或传感器。 
除 CTS 定义的任何特定输出外，每个功率输出应满
足以下一般要求： 
a。只要“开”状态保持且 Vs 在 Vmin 至 Vmax 范
围内，功率输出应满足其操作要求。 
b.功率输出应单独缓冲。任何电源输出外部连接上
的故障（开路，接地短路，电源短路）不应影响其
他电源输出的运行或任何内部 ECU 电压参考（例
如，电池，点火，微处理器电源或 A / D 转换器参
考）。 
c。一旦故障情况消除，功率输出应能承受-1.0V 至
最大正常工作电压（Vmax）范围内的任何电压源
的短路，并且无需软件动作即可恢复正常运行。 
表 2 显示了单电压类型的通用电源输出接口要求
（根据需要，一个 ECU 中可以存在多个通道）。
表 2：电源输出接口要求 
电源输
出名称 
类型 
公差+-% 
当前容量 
故障触
发限制 
瞬态时间
[我们] 
电流限制阈值 励磁延迟 
Vref_5V 
缓冲的 5V 
2 
100mA 
0.3V 
200 
>100mA 
负载依赖 
Vref_12V 
缓冲的
12V 
N/A 
100mA 
N/A 
200 
>100mA 
负载依赖 
 
3.1.4.2.11.1 缓冲 5V 输出电气接口。 除非适
用的 CTS / SSTS 中另有规定，否则缓冲 5V 电
气接口输出应满足以下要求： 
a。 在驱动最大电流 I_out 为 100mA 且 Vbatt
处于 Vmin 至 Vmax 的正常工作电压范围时，
应将其调节至 5.0 V  0.1 V 
b。如果电压超出所需的调节范围，应对其进行
监控
并向 ECU 软件报告故障。 故障应在不迟于电压等
于 5.0V 0.3V 时报告。 
c。它应包括过滤以防止报告故障情况，除非输
出超出规定限值至少 200s。 
d。 它将驱动由 CTS / SSTS 应用程序 I / O 表定义
的外部连接数。 
e。在所有点火电压被移除（点火关闭）之后，


### 第 8 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
8/74 
2005 年 10 月 
 
 
 
它应能够继续运行，直到完成断电程序。 
通过接通连接的传感器产生的励磁电流不应导致故
障。 如果使用数字短路保护关闭机制，则必须分析
实际的传感器负载。 应允许标称容性负载为 10uF。 
缓冲的 5V 输出应跟踪 A / D 转换器参考，以提供最
佳的转换精度。
表 3：缓冲 12V 电源特性 
 
 
 
 
 
 
 
 
 
 
 
 
图 6: 缓冲 5V 电压 
3.1.4.2.11.1 缓冲 12V 输出电气接口 
 
 
 
 
 
 
 
 
 
 
 
 
图 7: 缓冲 12V 电压 
缓冲 12V 电气接口输出应满足以下要求： 
a。 输出（Vout）应缓冲且不受调节，但
具有表 3 中列出的电气特性。 
b。 如果不满足要求，应监控输出并向
ECU 软件报告故障。 
c。 输出应包括滤波，以防止报告故障情
况，除非输出超出规定限值至少 200s。 
d。 输出应驱动由 CTS / SSTS 应用程序 I / O
表定义的外部连接数。
3.1.4.3 通用电气接口要求。本文档介绍了平台上最
常用的接口。 
表中给出的电压和电流值应作为一般接口要求处理。
等效原理图应作为使用现成元件，电路和/或微控制
器的标准电路解决方案的指导。输出驱动器未详细
显示达到 GMW3097 / GMW3172 要求的全面保护机
制。 
通过使用专用 I / O 电路，可以用更少的元件/更低
的成本实现相同的功能，但应保持关于电压/电流水
平和对 GMW3097 / GMW3172 要求的抗扰度的基本
要求。 
供应商始终全权负责实施所需的耗材和 I / O 保护，
以满足 GMW3097 / GMW3172 的要求。 
CAN 和 LIN 类型的串行接口由其他 GMW 文档描述。 
3.1.4.3.1 接口有源状态电流。在正常工作供电电压
下，数字信号和开关的优选有源状态接口电流在 4
至 20mA 的范围内。 
这是给出的一般准则的基础，附录 A 中记录了详细
的接口。 
模拟信号输入具有较高的阻抗水平，范围<50k
（接口信号电流为 0 至 0.1mA）。 
注意：根据传感器输出阻抗，模拟接口信号处理可
能对辐射抗扰度要求更敏感。
  
 
  Vs 
Vcc 
I_out 
  
  
Vref_5V   
D* 
5V LDO 
Voltage 
Regulator 
+ 
C_out 
Vout 
uC 
- 
     Power_gnd 
signal_gnd 
D* = Reverse polarity protection mechanization Vmin dependent 
I_out 
  Vs 
  
  
Vref_12V 
  
D* 
Vz 
5V LDO 
Voltage 
Regulator 
+ 
C_out 
Vout 
uC 
  
 
signal_gnd 
D* = Reverse polarity protection mechanization Vmin dependent 
- 
     Power_gnd 
Vcc 
Short to Gnd & Vbatt 
Protection 


### 第 9 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
9/74 
 
 
 
3.1.4.3.2 短路保护。 ECU 的所有输入和输出（除了
电源和电源接地接口）必须在整个正常工作电压和温
度范围内被永久性地短路保护，以防止电池短路或接
地电位短路。 
3.1.4.3.1 数字输入电气接口。 
图 8 显示了适用于数字信号接收器或开关输入接
口的通用原理图。 驱动侧可以是上拉/下拉开关设
计或高/低侧晶体管驱动器。 
1.0V 的接地偏移。 这意味着信号驱动器或开关侧
可以本地接地到车身接地螺栓或发动机组，而不是
单独带回接收器信号接地节点。 
对 PWM_型和 Frequency_型控制信号的要求在单
独的段落中描述。
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
图 8：数字输入电气接口
3.1.4.3.4 输入信号钳位。 由于外部输入引脚电压可
能/将高于有源接口电路电源 Vcc 且低于 Gnd，因此
CMOS IC 或 uP 输入引脚设计应将内部引脚电压/电
流限制在安全水平（取决于 IC）的帮助下外部串联
电阻 R1 和内部二极管钳位。 如果由于 IC 引脚注入
电流限制（I_inj_max）而无法实现，则应使用外部
钳位 IC。 如果使用外部钳位二极管，它们应为肖特
基型，以保证在 IC 内部硅二极管开始导通之前它们
钳位输入电流。 在所有通过输入保护钳位二极管流
回 Vcc 节点的 IC 引脚电流注入的情况下，Vcc 电平
应由电压调节器本身或 Vcc 网络上的附加电压限制
装置限制在安全水平。 
3.1.4.3.1 输入引脚上的数字输入信号阈值电压电
平。 Vmin，Vmax 和 Vnom 由 GMW3172 电气负
载编码定义。 注：使用图 8 所示的分压器 R1-R2，
可以调整常用的逻辑 H / L 阈值（30/70％或
35/65％），
以满足以输入引脚为参考的 
1V 接地偏移要求。 通过使用 5V LDO 稳压器和肖
特基反向保护二极管，可以将正确的信号接口电压
维持在 Vs = Vmin = 6V 的电池电压。 
表 4：数字输入阈值 
参数 
条件 
Min. Typ. Max Unit 
Ui I 输入
低状态 
Uil 
Vmin<Vs
< Vmax 
-1 
0 
2 
V 
Ui 输入高
状态 Uih 
Vmin<Vs
< Vmax 
5.5 
Vnom Vmax 
+1 
V 
3.1.4.3.6 时间常数，通用接口。 默认输入电气时间
常数（见于 IC 输入节点 Vth）应为 1ms（不包括
PWM 和频率类型输入）。 该时间常数由 R1，R2 和
C1 设置（见图 8）。 
3.1.4.3.7 浮动接口输入. 由于强调降低汽车中所有
E/E 设备的静态电流，因此在任何电源状态，开启，
关闭或 
Vign_prot 
Vs_sw 
Vs 
Rp_source 
Rpu 
D* 
transmitter     receiver 
Rpd 
Signal source 
or switch 
Rp 
5V* LDO 
Voltage 
Regulator 
Io 
Ii 
  R1  
Vth 
I_inj 
+ 
- 
Power_gnd 
 
5V*: Lower Vcc supply levels than 5V can be used 
+ 
Uo 
- 
+ 
Ui 
- 
R2 
C1 
uC 
Gnd 
10nF@Input_pin 
Signal_gnd    Vth/L/H: common values: 30/70% or 35/65% of Vcc for CMOS 
I_inj: Injection current into IC-input when Vin > Vcc 
D* = Reverse polarity protection mechanization Vmin dependent 


### 第 10 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
10/74 
2005 年 10 月 
 
 
 
休眠状态 (图 8) 上都不会有任何接口悬空，这些接
口连接到提供 Vbatt 的 ECU。 
a。在 Rp 是下拉电阻（Rpd）的情况下，输入引脚
的外部输入电压在睡眠模式下应为零。 
b。在 Rp 是上拉电阻（Rpu）并且 Vs_sw 或 Vign 关
闭的情况下，接口电路中应该有一个明确的接地电
阻（如图 8 中的 R2）。 
3.1.4.3.8 瞬态保护。所有外部连接器引脚上的默认
输入保护应为陶瓷 100V DCWV 电容（请参见通用输
入接口原理图中的 10nF @ input_pin 占位符）。无
论根据 GMW3097 结果选择的实际电容值如何，物
理 PCB 铜焊盘设计都可以安装 10nF 0805 尺寸的
X7R 陶瓷电容。如果速度要求不合格 10nF 元件，则
应更换/减少电容器，并应在有源器件（压敏电阻，
TVS 或类似器件）的帮助下设计瞬态保护。 
开关输入接口应产生足够的接触清洁电流。 
注意：这可以通过在输入端使用 10nF 电容轻松实现。 
3.1.4.3.9 开关输入软件消抖时间。每个机械开关输
入信号激活特定功能。为确保此激活安全，应在所
有开关信号的激活和停用时使用至少 30 ms 的默认
软件消抖时间。在此 30ms 期间，必须对开关状态
进行多次采样（大于或等于 3），并且在更改功能
状态之前必须在每个采样期间识别相同的逻辑电平。 
边沿触发可用于唤醒或中断，但不应用于开关状态
识别算法。 
3.1.4.3.10 环境因素对 ON-OFF 开关/传感器采集的
影响：在正常操作期间，控制单元获取这些命令不
受以下事件和条件的影响： 
即使环境条件使得由于氧化导致与开关连接串联形
成 50 欧姆电阻，单个传感器和/或开关的状态也不
会保持不确定或改变，并且形成如下所示的电阻。 
由于潮湿而与开关连接并联： 
a。 使用 Rp =3.0k
5 
kOhm。 
b。 当使用 Rp =1.2k
开关均为 2 kOhm。 
3.1.4.3.11 数字输入接口表。 ECU 应提供从附录 A
数字或开关输入接口段落中选择的数字输入类型。 
每个接口的每种类型的具体数量（可以为零）应由
CTS 或 SSTS 中的应用程序 I / O 表定义。 
输入电气接口提供输入电压的调节（电气或机械开
关），并根据预定义的阀值，将输入识别为两个逻
辑状态之一，定义为 A 和 P. 相关联的 I/O 功能直接
报告该逻辑状态或测量状态转换的定义的时序属性。
A = 主动状态 
P =被动状态 
3.1.4.3.2 模拟输入接口 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
图 9: 模拟输入接口 (Vin < Vref) 
Vcc_sw 
Vcc 
sensor_supply* 
Vs 
transmitter     receiver 
D* 
Rpu* 
Io 
Ii 
 R1**  
  
 
I_inj 
+ 
Uo 
- 
+ 
Ui 
- 
signal_rtn 
Vref+ 
In 
Vref- 
A 
D 
5V LDO 
Voltage 
Regulator 
51kOhm 
C1 
uC 
Power_gnd 
10nF@Input_pin 
signal_gnd 
Gnd 
A/D Converter Input 
Resolution 8 or 10 Bit of A/D_Vref 
*Rpu and need for sensor_supply application dependent 
**A/D input protection and accuracy dependent on R1 
D* = Reverse polarity protection mechanization Vmin dependent 
One common R_no_float load for each separate Vcc_sw net 
Sensor 
R_no_float 


### 第 11 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
11/74 
 
 
 
重要的是，模拟接口进行的测量应尽可能准确和
灵敏，以确保可以完全执行给定的功能。 是否使
用 8 位或 10 位转换器取决于所需的测量精度。 
尽可能采用比例测量，为传感器提供与 ECU A / 
D 转换器参考输入相同的正负电压参考，除非适
用的 CTS / SSTS 另有规定。 
如果使用的所有模拟传感器/测量通道均由相同的
Vcc_sw 开关电源供电，则 A / D Vref 可连接到
Vcc_sw 网络，以最大限度地降低传输晶体管对
测量精度的影响。
有源传感器应在接收侧具有电阻下拉（Rpd）。 默
认下拉值应为 51k。上拉（Rpu）电阻器的安装和
值取决于所需的传感器接口。 
无源传感器（如 NTC / PTC 热敏电阻，重新定位器
或电位计）应具有与传感器类型（<50k）匹配的上
拉（Rpu）值。 
在需要减小输入信号的情况下，如供电电压（Vbatt，
Vrun /曲柄或 Vacc）测量的情况，所使用的电阻应
具有随时间推移的公差和稳定性，以匹配所需的总
体测量精度。 
滤波器应包含在所有模拟输入中，默认时间常数为
1ms（R1 * C1）。
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
图 10: 模拟输入接口 (Vin > Vref) 
 
3.1.4.3.12.1 A / D 转换器输入电阻选择（图 9,10
和 11：R1）。 输入保护电阻 R1 将影响微控制
器供应商（带集成 A / D 转换器）设置的不同要
求： 
a。 A / D 电路输入过压保护需要 R1（限流器与
内部 A / D 引脚钳位二极管串联）。 
b。 A / D 输入注入电流限制 I_inj 基于： 
•破坏 A / D 输入电路。 
•与相邻 A / D 通道的串扰。 
C。 归因于 IC 引脚泄漏，I_leak，电流流过传感
器源电阻（Rsensor + R1 // R2）导致的 A / D 精
度。 
基于微控制器的 A / D 转换器的典型设计值为：
a。I_leak = 1uA from A/D pin 
b。 I_inj = <2.5mA 用于维护功能 
C。 I_inj => 25mA 破坏性 
d。 8 位 1 / 2LSB 误差：10mV 
e。 10 位 1 / 2LSB 误差：2.5mV 
 
这些值与 10nF 输入引脚电容相结合，要求 R1 选择
为 10k 欧姆，以便在 GMW3097 ESD 和信号线上的
瞬变中存活。 
实际上，由于 I_leak 误差电流流过 R1（R1 = 10k 时
为 10mV 误差），A / D 转换器的精度将限制为 8 位。 
3.1.4.3.13 模拟传感器信号返回。 模拟传感器信号
返回（signal_rtn）应为每个模拟传感器提供单独的
输入引脚。 如果在线束中完成传感器返回信号的拼
接和求和，则应由 GM 电气架构工程师批准。
Vs_sw 
sensor_supply* 
Vs 
transmitter 
receiver 
Vcc 
D* 
Rpu* 
Io 
Ii 
 R1**  
   
 
10k 
I_inj 
+ 
Uo 
- 
+ 
Ui 
- 
signal_rtn 
Vref+ 
In 
Vref- 
A 
D 
5V LDO 
Voltage 
Regulator 
R2*/** 
C1 
uC 
Power_gnd 
 
10nF@Input_pin 
signal_gnd 
Gnd 
A/D Converter Input 
Resolution 8 or 10 Bit of A/D_Vref 
*Rpu, R2 values and need for sensor_supply application dependent 
**A/D input protection and accuracy dependent on R1//R2 
D* = Reverse polarity protection mechanization Vmin dependent 
One common R_no_float load for each separate Vs_sw net 


### 第 12 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
12/74 
2005 年 10 月 
 
 
 
传感器信号的接地不应与车身接地或发动机组并联
或代替 ECU 信号接地返回。 
3.1.4.3.14 具有诊断功能的模拟开关输入接口图 11
中的原理图选择了电阻值以处理高达 0.8V 的接地
偏移. 这意味着开关返回可以连接到本地车身接地
螺栓，不需要将其路由回 ECU 上的单独开关输入
引脚。 
可被认可的状态: 
a。 对地短路错误 
b。 打开开关信号 
C。 闭合开关信号 
d。 开路或电池错误短路
 
 
 
 
 
 
 
 
 
 
 
 
 
 
图 11：带诊断功能的模拟开关输入接口 
3.1.4.3.15 通过模拟双线电阻编码接口获得的 ON-
OFF 命令：如果所涉及的开关具有在任何时候都小
于或等于 5 欧姆的闭合接触电阻，则可以实现具有
最多 11 个开/关开关状态的并联电阻梯形开关。
在实践中，这需要金到金开关元件。 当梯形电阻器中使用较少的
开关时，可以使用替代的接触材料。 在图 12 中，交换机 S1 具有
最高优先级，覆盖所有其他优先级，交换机 Sn 具有最低优先级。
 
 
 
 
 
 
 
 
 
 
 
 
 
图 12：电阻梯形开关输入接口
switch  receiver 
Vcc_sw 
Vcc 
Vs 
D* 
OR 
Ii 
  
 
   
 
R1** 
I_inj 
Vref+ 
In 
Vref- 
A 
D 
5V LDO 
Voltage 
Regulator 
+ 
Uo 
- 
+ 
Uo 
- 
+ 
Ui 
- 
signal_rtn 
51k 
C1 
uC 
Power_gnd 
10nF@Input_pin 
signal_gnd 
Gnd 
Alternative switch 
grounding 
A/D Converter Input 
Resolution 8 or 10 Bit of A/D_Vref 
**A/D input protection dependent on R1 
D* = Reverse polarity protection mechanization Vmin dependent 
One common R_no_float load for each separate Vcc_sw net 
Vcc_sw 
Vcc 
Vs 
receiver 
D* 
Rpu 
   Rn  
... 
   R3 
 R2 
 
  
 
Ii 
Sn 
S3 
S2 
S1 
  
 
  
 
Rcomp 
+ 
Ui 
- 
   Rin  
  
 
Vref+ 
In 
Vref- 
A 
D 
5V LDO 
Voltage 
Regulator 
Rpd 
C1 
uC 
Power_gnd  
   
 
signal_rtn 
10nF@Input_pin 
keypad 
signal_gnd 
A/D Converter Input 
Resolution 8 or 10 Bit of A/D_Vref 
D* = Reverse polarity protection mechanization Vmin dependent 
One common R_no_float load for each separate Vcc_sw net 
R_no_float 
680 +/-1% 
R_no_float 


### 第 13 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
13/74 
 
 
 
表 5：梯形电阻器电阻值 
电阻代码表 
功能数
量，（最
大闭合开
关阻抗） 
R2 
Ohm 
R3 
Ohm 
R4 
Ohm 
R5 
Ohm 
R6 
Ohm 
R7 
Ohm 
R8 
Ohm 
R9 
Ohm 
R10 
Ohm 
R11 
Ohm 
R12 
Ohm 
Rpu 
Ohm 
Rpd 
Ohm 
Rcomp 
Ohm 
3 (20 ) 
105 
226 
845 
 
 
 
 
 
 
 
 
237 
10k 
10 
4 (20 ) 
82.5 
154 
365 
2000  
 
 
 
 
 
 
237 
10k 
10 
5 (20 ) 
64.9 
105 
187 
422 
2000  
 
 
 
 
 
237 
10k 
10 
6 (20 ) 
52.3 
73.2 
113 
196 
402 
1240  
 
 
 
 
237 
10k 
10 
7 (20 ) 
45.3 
61.9 
86.6 
130 
221 
453 
1370  
 
 
 
237 
10k 
10 
8 (20 ) 
41.2 
53.6 
73.2 
105 
158 
261 
576 
1870  
 
 
237 
10k 
10 
9 (5 ) 
24.3 
32.4 
42.2 
57.6 
82.5 
121 
205 
374 
953 
 
 
237 
10k 
10 
10 (5 ) 
23.2 
30.1 
39.2 
51.1 
71.5 
105 
162 
274 
634 
2150  
237 
10k 
10 
11 (5 ) 
20 
26.1 
33.2 
44.2 
57.6 
82.5 
115 
182 
309 
715 
2740 
237 
10k 
10 
注意：除 Rpd 和 Rcomp 为 5％外，所有电阻均为 1％。 
 
表 6：电阻梯形电压阈值 
功 能 数 量 ，
（ 最 大 闭 合
开关阻抗） 
SW1 
< x 
Volts 
SW2  < 
x 
> 
SW1 
Volts 
SW3  < 
x 
> 
SW2 
Volts 
SW4  < 
x 
> 
SW3 
Volts 
SW5  < 
x 
> 
SW4 
Volts 
SW6  < 
x 
> 
SW5 
Volts 
SW7  < 
x 
> 
SW6 
Volts 
SW8  < 
x 
> 
SW7 
Volts 
SW9  < 
x 
> 
SW8 
Volts 
SW10  < 
x  > SW9 
Volts 
SW11  < 
x 
> 
SW10 
Volts 
3 (20 ) 
1.05 
2.31 
3.5 
 
 
 
 
 
 
 
 
4 (20 ) 
0.95 
2.02 
3.04 
3.99 
 
 
 
 
 
 
 
5 (20 ) 
0.85 
1.75 
2.59 
3.38 
4.11 
 
 
 
 
 
 
6 (20 ) 
0.87 
1.51 
2.21 
2.84 
3.5 
4.08 
 
 
 
 
 
7 (20 ) 
0.73 
1.4 
2.02 
2.61 
3.16 
3.69 
4.17 
 
 
 
 
8 (20 ) 
0.7 
1.32 
1.89 
2.44 
2.95 
3.42 
3.87 
4.3 
 
 
 
9 (5 ) 
0.44 
0.88 
1.33 
1.8 
2.28 
2.74 
3.21 
3.67 
4.1 
 
 
10 (5 ) 
0.39 
0.85 
1.28 
1.72 
2.16 
2.62 
3.07 
3.5 
3.93 
4.35 
 
11 (5 ) 
0.41 
0.78 
1.17 
1.58 
2.0 
2.42 
2.84 
3.24 
3.64 
4.03 
4.41 
注意: 开放状态是大于 SWN 的任何电压。 
3 开关示例 5.1 ≤ ADC 电源 ≤ 4.9: 
 
SW1 有效 Ui ≤ 1.05 volts 
 
SW2 有效 1.05 < Ui ≤ 2.31 volts 
 
SW3 有效 2.31 < Ui ≤ 3.5 volts 
 
打开 (开关断开) Ui > 3.5 volts 
 
3.1.4.3.15 PWM 类型控制信号。 GMW3097 针对车辆线束中的电流和电压信号的转换速率提供了指导。 这将影
响 PWM 型控制信号的机械化。 同时，PWM 信号携带脉冲宽度的信息，该信息应保持接口中传递的信息的定义
精度。 由于 PWM 信号的电压转换速率受到限制，因此 ECU 未在同一点接地的接地偏移也会产生在接口接收器
端看到的脉冲宽度变化。 
将 PWM 信号信息分辨率要求与接地偏移相结合，影响和限制电压信号转换速率，可提供以下 PWM 接口等效原
理图和参数表。 根据 PWM 接口是由 Vs（其他 ECU）还是 Vcc_sw（具有本地信号返回的传感器/从站）提供，
有两组要求适用于逻辑电平阈值和最大 PWM 频率。 
表 7: PWM 信号特征 


### 第 14 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
14/74 
2005 年 10 月 
 
 
Vs 
Vs_sw 
Vcc 
Vs_prot 
Rp_source 
D* 
transmitter 
receiver 
Rpu 
Rpd 
f_pwm < 400 Hz 
Rp 
5V LDO 
Voltage 
Regulator 
R1 
Vth 
I_inj 
PWM out 
+ 
- 
Power_gnd 
 
PWM transmitter 
+ 
Utx 
- 
+ 
Urx 
- 
R2 
C1 
uC 
1nF@Input_pin 
Type A, General PWM interface. Vs supplied 
signal_gnd 
Gnd 
Vs 
Rp_source 
Vcc_sw 
Vcc 
D* 
slave_supply 
Rpu 
Rpd 
f_pwm < 1kHz 
Rp 
5V LDO 
Voltage 
Regulator 
R1 
Vth 
I_inj 
PWM out 
+ 
- 
Power_gnd 
 
+ 
Slave unit 
Utx 
- 
+ 
Urx 
- 
signal_rtn 
C1 
uC 
1nF@Input_pin 
signal_gnd 
Type B, slave unit PWM interface with Vcc <= 5V supply 
D* = Reverse polarity protection mechanization Vmin dependent 
接口类型 
Rp 
接口供
应
Vs=12V 
Vcc=5V 
接口地 
f_pwm_max 
[Hz] 
PWM 
有效信
号范
围% 
上升/下降
时间
0.005/f_pwm 
[us] 
地面移
位 
耐受的[V] 
Vil 
输入 
逻辑 
水平
低 
[V] 
Vih 
输 入
逻辑
水平
高 
[V] 
A 
1.2kOhm 
Vs 
车身/发动机缸体 
400 
5-95 
12 
± 1.0 
-1 to 
+2.0 
+5.5 to 
Vmax+
1 
B 
1.2kOhm 
Vcc 
信号返回 
1000 
5-95 
5 
0 
0 to 
+1.5 
+3.5 to 
+5.0 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Figure 13: PWM 型接口 
 
3.1.4.3.16.1 时间常数，PWM 接收器。 默认的 PWM
接收器输入电气时间常数取决于接口上拉供电电压。 
a。 对于 12V 提供的 PWM 控制信号接收器，在
节点 Vth 处看到的标称输入时间常数（由 R1，
R2 和 C1 定义）应为 5μ s，最大 PWM 频率为
400Hz（t_rise / 2.2）。
Itx 
Irx 
Itx 
Irx 
Vs_prot 


### 第 15 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
15/74 
 
 
 
b。 对于 5V 提供的 PWM 控制信号接收器，在节点
Vth 处看到的标称时间常数（由 R1 和 C1 定义）应为
2μ s，最大 PWM 频率为 1kHz。（t_rise / 2.2）。 
3.1.4.3.17 频率类型控制信号。 
3.1.4.3.17.1 定义。 频率调制（FM）意味着将发送
的信息分配给电流或电压信号的频率。 应该使用用
于发送二进制信息的两个频率（频移键控 FSK）或
用于
发送模拟信号的连续频率间隔。 在任何情况下，占空
比都不包含任何信息。 
3.1.4.3.17.2 物理接口。 频率信息应通过电流接口
或电压接口传输。 
3.1.4.3.17.1.1 电流接口。 当前接口包括电流发送
器 CTx 和电流接收器 CRx。 
电流接收器为电流变送器提供工作电压。
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
图 14：频率类型控制信号
3.1.4.3.17.1.1.1 电流发送器 Tx。 电流接收器提供的电流变送器在两个电流值（低电流和高电流）之间切换。 主动状态
由有效高电流表示。
 
transmitter 
receiver 
SC-protection 
Vs_sw   Vs_prot 
Vs 
D* 
Itx 
  
 
      signal_supply 
+ 
Utx 
- 
signal_rtn 
Itx 
Irx 
+ 
G 
Urx 
Current Interface 
- 
Power_gnd 
 
One common R_no_float load for each separate Vs_sw  net 
D* = Reverse polarity protection mechanization Vmin dependent 
signal_gnd 
Sensor or 
Slave unit 
R_no_float 


### 第 16 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
16/74 
2005 年 10 月 
 
 
 
表 8：频率信号 - 电流发送器特性 
绝对最大额定值 
符号 
参数 
条件 
Min 
Typ. 
Max 
Unit 
Vs 
供电电压 
GMW3172 温度要求 
-13.5 
14 
26.5 
V 
运行范围 
 
符号 
参数 
条件 
Min 
Typ. 
Max 
Unit 
Vs 
供电电压 
操作（Tmin - 
Tmax 分别） 
Vmin 
14 
Vmax 
V 
Itxl 
传输电流低状态 
最小负载
和
GMW3172 
温度要求 
5.6 
7 
8.4 
mA 
Itxh 
传输电流高状态 
最小负载和
GMW3172 
温度要求 
11.2 
14 
16.8 
mA 
Itxh/Itxl 
电流比 
最小负载
和
GMW3172 
温度要求 
2 
 
3 
 
dI/dt 
电流转换率 
0.1*(Itxh-Itxl)+Itxl to 
0.9*(Itxh-Itxl)+Itxl
在 Itxh / Itxl max
处下降沿反转。 
10 
 
100 
mA/ µs 
F 
频率范围 
 
0 
 
15000 
Hz 
 
3.1.4.3.17.1.1.2 电流接口接收器。 电流接收器
为电流变送器提供工作电压并检测两者 
现状。 主导状态是有效的高电流。 当前接口
接收器 Rx


### 第 17 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
17/74 
 
 
 
表 9：频率信号 - 电流接收器特性 
绝对最大额定值 
符号 
参数 
条件 
Min 
Typ. 
Max 
Unit 
Vs 
供电电压 
GMW3172 温度要求 
-13.5 
14 
26.5 
V 
Operating range 
 
符号 
参数 
条件 
Min 
Typ. 
Max 
Unit 
Vs 
供电电压 
GMW3172 温度要求 
Vmin 
14 
Vmax 
V 
Irxl 
接收当前的低状
态 
最小负载和 GMW3172 温
度要求 
5.4 
7 
8.6 
mA 
Irxh 
接收当前的高状
态 
最小负载和 GMW3172 温
度要求 
11 
14 
17 
mA 
Irxh/Irxl 
目前的比例 
最小负载和 GMW3172 温
度要求 
1.8 
 
3.2 
 
dI/dt 
电流摆率 
0.1*(Itxh-Itxl)+Itxl to 0.9*(Itxh-Itxl)+Itxl
下降沿逆转 
10 
 
100 
mA/ 
µs 
f 
频率范围 
 
0 
 
15000 
Hz 
*)电压取决于设备的工作电压定义 
3.1.4.3.17.1.1.3 电流接口电阻。 由于接收器内
部的信号处理，必须定义负载电阻，例如， 
1151％。
基于信号电流的意外泄漏，相应的漏电阻不应小于
负载电阻的 100 倍。
 
抗性 
符号 
参数 
条件 
Min 
Typ. 
Max 
Unit 
Rsense 
负载电阻 
工作电压范围 
Rsense 
-1% 
Rsense Rsense 
+1% 

Rleakage 漏电阻 
工作电压范围 
100 
* 
Rsens
e 
 
 

 
3.1.4.3.17.2.1.4Duty Cycle. 
 
符号 
参数 
条件 
Min 
Typ. 
Max 
Unit 
tPuls/T 
占空比 
触发级别 0.5*(Itxh-Itxl)+Itxl 
0.33 
0.5 
0.66 
 
T 跳动 
占空比时间稳定
性 
触发级别 0.5*(Itxh-Itxl)+ 
1 
 
0.005/f_max 
µs 
 
3.1.4.3.17.3 电压接口。 电压接口由电压发送器
（VTx）和电压接收器（VRx）组成。 
如果 Vs = 12V，则独立提供电压接收器和电压变
送器。 如果发射器电源为 5V 并且需要向接收器
发送信号返回（signal_rtn），则发送器由接收器
提供。 主导状态可以定义为有效高压或有效低压。
信号接口在频率值中携带其信息，其应保持所传递
的信息的定义精度。由于信号电压变化率的限制，
ECU 之间不在同一点接地的接地偏移也会在接口的
接收端产生频率变化。 
 将信号信息分辨率要求与地面偏移效应和电压信号
转换速率限制相结合，


### 第 18 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
18/74 
2005 年 10 月 
 
 
 
可得到图 15 所示的频率
接口等效原理图和表 10
中给出的参数。
根据频率接口是由 Vs（其他 ECU）还是 Vcc_sw
（传感器/具有本地信号返回的从站）提供，两组要
求适用于逻辑电平阈值。
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
图 15: 电压接口 
Table 10: 频率信号 – 电压接口特性 
 
接口类型 
Rp 
接口供
应
Vs=12V 
Vcc=5V 
接口地 
f_ma
x 
[kHz] 
占空
比 
% 
上升/下降
时间
0.005/f_max 
 
[us] 
地面移
位 
耐受的 d 
[V] 
Uil 
输入
逻辑 
水平
低 
[V] 
Uih 
输入逻辑水平
高 
[V] 
A 
1.2kOhm 
Vs 
车身/发动机缸体 
15 
33- 
66 
>12 
± 1.0 
-1 to 
+2.0 
+5.5 to 
Vmax+
1 
B 
1.2kOhm 
Vcc 
信号返回 
15 
33- 
66 
>5 
0 
0 to 
+1.5 
+3.5 to +5.0 
 
3.1.4.3.17.3.1.1 占空比 
Frequency transmitter       Frequency receiver 
Vs 
Vs_sw 
Vcc 
Vs_prot 
Rp_source 
D* 
Rpu 
Rpd 
f < 15 kHz 
Rp 
5V LDO 
Voltage 
Regulator 
frequency out 
+ 
  
 
    Input_pin+ 
+ 
   R1  
  
 
Vth 
I_inj 
+ 
- 
Power_gnd 
 
V_tx 
V_rx 
R2 
1nF@Input_pin 
C1 
uC 
- 
- 
Signal_gnd 
Type A, General frequency type voltage interface. Vs supplied 
Gnd 
Vs 
Rp_source 
Vcc_sw 
Vcc 
D* 
slave_supply 
Rpu 
Rpd 
f < 15 kHz 
Rp 
5V LDO 
Voltage 
Regulator 
  
 
    Input_pin+ 
+ 
   R1  
  
 
Vth 
I_inj 
+ 
- 
Power_gnd 
 
frequency out 
+ 
Slave unit 
C1 
V_tx 
- 
V_rx 
- 
signal_rtn 
1nF@Input_pin 
uC 
Signal_gnd 
Type B, slave unit, frequency type voltage interface with 5V supply 
D* = Reverse polarity protection mechanization Vmin dependent 
Vin 


### 第 19 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
19/74 
 
 
 
表 11：频率信号-电压接口占空比 
符号 
参数 
条件 
Min 
Typ. 
Max 
Unit 
tPuls/T 
占空比 
触发电平 0.5*(Utxh-Utxl)+Utxl 
0.33 
0.5 
0.66 
 
t 振动 
占空比时间稳定性 
触发电平 0.5*(Utxh-Utxl)+Utxl 
1 
 
0.005/f_max 
µs 
 
3.1.4.3.18 调频信号（物理层）。 
3.1.4.3.18.1 频移键控（FSK）。 用于发送 FSK
信号的物理层可以是如上所述的电压接口或电流
接口。
逻辑高频应约为逻辑低频的两倍，例如低状态
100Hz; 逻辑高200Hz。
表 12: FSK 要求 
 
符合 
参数 
条件 
Min 
Typ. 
Max 
Unit 
f_低 
低状态频率 
 
100 
+-1% 
 
1000 
+-1% 
Hz 
f_高 
高状态频率 
 
200 
+-1% 
 
2000 
+-1% 
Hz 
f_高流动性 
频率比 
 
1.8 
2 
2.2 
 
t_延迟 
传输延迟时间 
 
 
 
1 
ms 
t_recogn 
接收识别时间 
 
 
 
10 
ms 
 
3.1.4.3.18.2 连续频率。 用于发送连续频率信号
的物理层可以是如上所述的电压接口或电流接口。 
表 13：连续频率要求 
符合 
参数 
条件 
Min 
Typ. 
Max 
Unit 
f_范围 
频率范围 
 
0 
 
15000 
+-1% 
Hz 
t_延迟 
传输延迟时间 
 
 
 
1 
ms 
t_recogn 
接收识别时间 
 
 
 
10 
ms 
 
3.1.4.4 输出驱动器接口。 为避免 RFI 问题，所
有功率输出驱动器应具有受控的上升和下降时间，
同时将通路元件的功耗保持在安全水平
（SOAR）。 
通过车辆线束连接负载的 PWM 型电源驱动器的
压摆率应限制在小于 1V /μ s 和 100mA /μ s。 
此要求将 Vs 提供的输出驱动器（高侧或低侧开
关）和 Vcc 提供的从/传感器 PWM 输出的最大
f_pwm 限制为 400Hz 至 f_pwm_max = 1kHz。
当使用更高的 PWM 频率（400Hz <f_pwm <25kHz）
时，电源驱动器应组装在负载本身上（例如：风扇
电机上的集成 PWM 驱动器），并且只有 PWM 命
令信号接口应通过线束。 电源驱动器/负载电源线
应使用π 型滤波器（LC 型）进行滤波。 
无论输出负载类型（低侧/高侧连接负载电阻器，线
圈，继电器或电机），关断时感应负载的雪崩能量
吸收设计应始终由电子输出驱动级处理。


### 第 20 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
20/74 
2005 年 10 月 
 
 
 
在包含线束的车辆电气系统中，没有电力驱动器看
到的只有电阻的负载。 如果未指定感应负载，所有
功率驱动器应串联处理其全电流负载值，最小电感
值为 20μ H。 
永磁二极管不得与继电器驱动器或执行器（如喷射
器）的线圈负载并联使用，其中快速关闭时间对于
正确的功能和/或接触耐久性至关重要。 
具有感应或白炽灯负载的接口应始终在整个工
作温度和电压范围内通过精确的负载值进行可
靠性验证。
供应商负责将验证期间的等效负载（如果使用）与
程序可用时的汽车实际负载相关联。 
3.1.4.4.1 低侧驱动器。 图 16 显示了一个等效的低
端驱动器电路，包括用于快速关断负载的雪崩能量
保护实现，以及一个用于 PWM 型负载的低端驱动
器，其中续流二极管与线圈并联连接。 
（未显示短路保护和诊断）：
 
 
 
图 16: 低侧驱动器 
 
表 14：图 16 键 
表 15：低侧驱动器特性 
 
 
 
参数 
值/条件 
Uo @Io_max 
<1.0V @Tmin to Tmax 
Io_max 
最大电流 
根据 CTS/SSTS 
@Vs_load_max 
 
负载 
R_负
载 
电阻: 
由 CTS/SSTS 定义 
负载 
L_负
载 
电感: 
由 CTS/SSTS 定义 (20uH 如果未规
定) 
负载感应能量 
E = ½ * (L_load) * (Io_max)
2
 
输出电容 
10 nF 默认值 
驱动频率：  
由 CTS/SSTS 定义 (通过线束将
PWM 驱动负载<400Hz) 
上升时间 
<1V/µs. 
(1/(AF)) 
面值 
 
0.005 
* 
短路保护 
To Vbatt and Gnd 
 
符号 
参数 
Io 
输出电流 
Uo 
输出电压 
Vs 
控制器的源电压 
Vs_load 
负载源电压 
Vgnd 
接地电压 
Tmax 
最高环境工作温度(GMW3172 
coding) 
Tmin 
最低环境工作温度(GMW3172 
coding) 
 
V_load 
R_load 
R_load 
Vs 
Vs_prot 
Vs_prot 
L_load 
L_load 
Io 
Io 
+ 
+ 
A 
Uo 
Uo 
B 
10n 
10n 
- 
- 
on/off control 
pwm control 
Power_gnd 
Type A: Fast switch off loads 
Type B: PWM controlled loads 


### 第 21 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
21/74 
 
 
D2 
 
 
根据负载类型和驱动频率，可以实施不同的雪崩能量处
理方案。 对于正常的雪崩额定 MOSFET，输出漏极到源
极齐纳（上图中的保护 B）可以处理大的单脉冲吸收。 
这种能力非常依赖于结温，如图 17 中的曲线图所示。对
于高能量负载或重复负载切换，应使用更稳健的设计，
如漏极到栅极齐纳（上图中的保护 A）。
 
 
 
图 17: 典型的 MOSFET 雪崩能量
 
 
Relay Coil 
Equivalent Circuit 
 
 
Rs 
Rp 
Ls 
Typical coil values @25C: 
Rs=72 Ohm (>50 Ohm @ -40C) 
Ls= 300mH 
Rp=680 Ohm 
Rs 
Rp 
Ls 
 
ECU 
 
 
relay_output 
Loads without reverse 
polarity protection 
 
ECU 
 
 
relay_output 
Loads with reverse 
polarity protection 
 
10n 
10n 
 
 
图 18: 继电器驱动器输出
3.1.4.4.2 继电器驱动器。标准 GM 继电器通常具有
与线圈并联的电阻器（Rp），范围为 500 至 700 欧
姆（10x Rs）。 
Vbatt 连接继电器的继电器驱动器为不受反向电压保
护的 E / E 设备供电，应具有串联二极管，以防止由
于 MOSFET 基板二极管而在反向电压条件下线圈激
活。 
表 16：图 18 键
表 17：高侧驱动器特性
 
符号 
参数 
Lo 
输出电流 
Uo 
输出电压 
Vs 
控制器和负载的源电压 
Tmax 
最高环境工作温度(GMW3172 coding) 
Tmin 
最低环境工作温度(GMW3172 
coding) 
Vs 
Vs 
参数 
价值/条件 
Uo @Io_max 
Vs - 1.0V @Tmin to Tmax 
Io_max 
根据 CTS/SSTS @Vs_load_max 的
最大电流 
负载 
电阻: R_负
载 
由 CTS/SSTS 定义 
负载 
电感: L_负
载 
由 CTS/SSTS 定义 
(20uH 
最小如果没有规定) 
负载感应能量 
E = ½ * (L_load) * (Io_max)
2
 
输出电容 
10 nF 默认值 
动作频率： AF 
由 CTS/SSTS 定义 (<400Hz 用于
通过线束进行 PWM 驱动) 
上升时间 
线束电源线<1V /μ s。 标称值
0.005 *（1 /（AF）） 
短路保护 
到 Vbatt 和 Gnd 
 


### 第 22 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
22/74 
2005 年 10 月 
 
 
 
 
 
图 19: 高端驱动器输出 
 
3.1.4.4.4 H 桥驱动器预警。 H 桥驱动器的正常应用
是执行器和电机控制。 建议不要将半 H 桥驱动器作
为可配置的低/高侧驱动器进行一般使用。 Vbatt 或
连接到 Vbatt_prot 电源的其他驱动器上的毛刺可以
触发 IC 驱动器 OVP 功能。 
如果在 L1 通电时发生这种情况，则关闭所有
Vbatt_prot 的负载，并且 L1 雪崩能量可以通过
HS_Transistor 续流二极管将 C1 充电到破坏性电压
电平。 对此风险的对策是电压钳 D1 和/或选择具有
大电容值的 C1。
 
 
 
图 20: H 桥驱动器输出 
3.1.4.4.5 驱动器诊断。 包含诊断的输出驱动器应根据表 18 生成诊断故障代码：
Vs 
Vs_prot 
Vs 
Vs_prot 
D1 
C1 
D1 
C1 
U1a 
U2a 
U1a 
OVP 
L1 
OVP 
OVP 
L1 
Full Bridge 
Half Bridge 
U1b 
U2b 
U1b 
Power_gnd 
Power_gnd 
signal_gnd 
Half/Full H-Bridge IC-driver with over voltage protection (OVP) for coil load 
Vs 
+ 
+ 
A 
Usat 
Usat 
B 
- 
- 
on/off control 
Io 
pwm control 
Io 
+ 
+ 
Typ A: Fast switch off loads 
R_load 
Typ B:  PWM controlled loads 
D1* 
R_load 
10n 
Uo 
10n 
Uo 
L_load 
L_load 
     Power_gnd 
- 
* For high current loads 
D1 is normally replaced by a 
syncronous  rectifier 
- 


### 第 23 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
23/74 
 
 
 
表 18：推荐的输出驱动器诊断 
负载驱动 
司机类型 
地面
S.C. 
电源
S.C. 
空载 
策略 
桥式驱动器继电器 
电流检测（例如：窗口升降
器） 
继电器转向器 
YES 当被激活时 
NO 
YES 当被
激活时 
负载电流检测 
- 检查超载 
- 激活后检查继电器切换（触点成型，线
圈断开） 
桥式执行器继电器 
没有电流感应（例如：门锁，
镜面倾斜） 
继电器转向器 
YES 当被激活时 
NO 
NO 
- 激活后检查继电器切换（触点成型，线
圈断开） 
继电器 
没有电流感应（例如：被动进
入） 
高压侧继电器开关 
YES 当被激活时 
NO 
YES, 
但激活后则
不 
检查继电器启动后的接通（触点成型、
线圈断开） 
继电器 
没有电流感应 
低压侧继电器开关 
NO 
YES 
当被激活
时 
YES, 
但激活后则
不 
检查继电器启动后的接通（触点成型、
线圈断开） 
半桥继电器 
无电流感测（例如：背光清洁
器） 
继电器转向器 
YES 当被激活时 
NO 
NO 
检查继电器启动后的接通（触点成型、线
圈断开） 
SSD 
( e.g. : 门灯或镜灯) 
高端驱动器 
YES 
YES 
YES 
负荷完全检查 
发光二极管驱动器 
高端驱动器 
- 
- 
YES 
 
SSD 
( E.G. : 后头灯，后备箱，帽
子) 
低端驱动器 
YES 
YES 
YES 
负荷完全检查 
发光二极管驱动器 
低端驱动器 
- 
- 
YES 
 
半桥 SSD 
（例如门锁） 
半桥 
YES 
YES 
YES 
负荷完全检查 
静电桥 
执行机构（如：外反射镜上下
左右调整） 
桥 
YES 
YES 
YES 
负荷完全检查 
 
3.1.5 用法定义。 GMW3172 描述了乘用车和轻
型卡车的 E / E 设备的环境，耐久性和性能测试。 
这些程序针对测试完整的 E / E 设备进行了优化。 
它们未针对验证 E / E 设备内部组件，材料和材
料组合是否适合特定应用进行优化。 设备供应
商负责定义和验证内部组件和材料要求，其中
包含特定于应用的性能，使用和环境耐久性需
求。 GMW3172 测试流程专注于在车辆运行期
间放置在 E / E 设备上的环境负荷。 E / E 设备供
应商负责选择与未使用的汽车在不同气候市场
的室外存储时间（日历时间）相关的故障率增
长的组件，材料和材料组合。 
3.2 产品特性。 不适用。
3.2.1 性能要求。 不适用。 
3.2.2 物理特性。 不适用。 
3.2.2.1 尺寸和容量。 不适用。 
3.2.2.2 质量属性。 不适用。 
3.2.3 可靠性。 不适用。 
3.2.3.1 目标寿命。 不适用。 
3.2.3.2 1 类和 2 类问题。 不适用。 
3.2.2.2.1 1 类型。 不适用。 
3.2.2.2.2 2 类型。 不适用。 
3.2.3.3 可靠性要求。 
3.2.3.3.1 电气系统可靠性。 
3.2.3.3.1.1 开发阶段的可靠性。 设计和开发阶段应
包括可靠性活动，如设计评审，FMEA，FTA，可靠
性分析和实验设计。可靠性程式计划应在签订合同
后 60 天内提交，并且必须由 GM 负责的工程师批
准。


### 第 24 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
24/74 
2005 年 10 月 
 
 
 
3.2.3.3.1.2 验证阶段的可靠性。可靠性要求可
分为两组，长期和短期要求。 
短期要求涉及使用前 12 个月和 36 个月的保修
结果。 
长期要求考虑 10 年或 160900 公里的可靠性，
无论两者哪个先达到。短期要求通常用 RPTV 表
示，每千辆车修理一次。长期可靠性可以用生
存概率（=可靠性）表示。 
3.2.3.3.1.2.1 短期要求。短期要求将在每个 SSTS 
/ CTS 中定义。最初应通过比较方法证明满足短
期要求。稍后比较方法应通过测试补充。 
3.2.3.3.1.2.2 长期要求。 SSTS 和/或 CTS 应定义
长期可靠性的要求。如果 SSTS 和/或 CTS 未定
义任何长期可靠性要求，则应适用 GMW3172
中的要求。长期可靠性的验证程序应在子系统
规范，组件规范或 GMW3172 中定义。 
3.2.3.4 电气系统安全。对于可能发生潜在危险
事件和条件的系统，应执行 FMEA，FTA 和 ETA
等定性系统安全分析。分析方法的选择应与
GM 讨论并获得 GM 批准。 
3.2.4 可维护性。不适用。 
3.2.5 用户系统/子系统/组件/部件接口。不适用。 
3.3 设计和施工。 
3.3.1 材料，工艺和零件选择指南。 
3.3.1.1 材料指南。 
3.3.1.1.1 基材选择。可接受的基材是： 
FR4 基材的玻璃温度 Tg 选自工作温度范围
要求。 
•低密度产品的 CEM3 底物。 
•聚酰胺柔性基材。 
•陶瓷混合基板。 
•上述组合（例如 Rigid-flex）。
具有良好耐久性的新型汽车用基板类型 
3.3.1.1.2 要使用的材料。用于设计和生产电子
组件的所有材料应符合所选 J-STD-001D 分类的
要求。这包括以下材料特性要求/资格： 
a。基材材料 
b。阻焊材料 
c。保形涂层（如果使用） 
d。焊料合金 
e。助焊剂 
f。可焊性（组件，助焊剂，糊料） 
材料选择是供货商的责任，材料组合可能导致
故障模式，随着时间增长，在汽车储存期间应
予以消除。 
3.3.1.1.3 要避免的材料。除 GMW3059 的一般
要求外： 
3.3.1.1.3.1 无铅要求。电子设计和机械设备应无
铅。 
这应包括但不限于： 
a。用于将元件连接到电路板的焊料。 
b。在焊接，压接等之前，在连接器端子上涂覆
电线和元件的“预焊接”。 
c。电路板上的保护涂层。 
d。缩小电路板上元件的软管。 
有关无铅设计的更多信息，请参阅 GMW3172。 
3.3.1.1.3.2 橡胶材料。由于存在对硫蒸气的气体
暴露敏感的组件，在电气/电子模块/组件的外
壳内不应使用任何类型的硫磺硫化橡胶材料。 
这种对气体腐蚀的敏感性组件包括芯片电阻器，
银/环氧丝网印刷 PCB 通孔和 LCD 热封地毯。 
湿式电解电容器中的密封材料应具有经过验证
的无硫设计（过氧化物固化）。 
3.3.2 过程指南。 
3.3.2.1 装配和焊接工艺。 
可接受的流程是： 
•采用回流焊接的 SMD 元件的单面或双面组装。 
•带有通孔回流焊（首选）的顶部引线元件，或
选择性焊接。


### 第 25 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
25/74 
 
 
 
•应避免使用胶合 SMD 元件和波峰焊接工艺。 
由于焊接后屏蔽区域上没有加热的助焊剂残留物，
应避免使用带掩膜的焊接托盘进行选择性波峰焊
接。残留物从直接掩模泄漏或掩模本身的污染进
入掩蔽区域。 
3.3.2.2 多板拆板和断拆标签。如果在生产过程中
将多块板作为面板处理，或者单板有突破片或部
件，则必须由机器完成分割。 
优选的方法是能将预先布线面板的最后部分（邮
票孔）铣削的镂铣机。 
如果面板设计具有单板轮廓外的组件，则不允许
沿着该侧刻痕板（V-刻痕）。 
应严格禁止用手或机器弯曲或折断板/面板上的任
何部件。 
3.3.2.2.1 焊点上的静态机械力。所有外部连接器
和内部较重的组件（铝电容器，散热片......）不
应依赖于焊点的机械强度。这种类型的部件应具
有其他机械固定装置（铆钉，按扣，RTV ......）
作为机械支撑。 
3.3.2.3 零件指南。零件应在其公布的评级中使用。 
在规范（关于时钟频率，温度范围，电压范围，
额定功率等）之外不得使用任何零件（如分立元
件，集成电路，电源，微控制器等）。 
3.3.2.3.1 包装选择。应尽可能使用表面安装设备
以减轻重量和尺寸。 
3.3.2.3.2 复位电路和看门狗功能。具有 Vbatt 连
接电源的微控制器应具有单独的（来自μ C）复
位电路。现场经验表明了这种需求，因为电源故
障，曲柄电压瞬变和电池断电情况导致代码失控，
重启 ECU 的唯一可能方法是电池极重新连接。
此要求也是通过“无故障发现”分析结果减少保
修退货数量的要求的一部分。 
3.3.2.3.3 存储器概念。 ECU 存储器应永久固定
于（不可插入）电路板。
ECU 应包含以下内存类型，可按保留功能和写入功
能进行分类。每种存储器类型的数量应在适用的
CTS / SSTS 中规定。 
3.3.2.3.3.1 类型 1 存储器（通常为动态 RAM）。 
a。 ECU 应能够写入 1 型存储器，直到微处理器由
于低电压状态而复位。 
b。ECU 应能够从类型 1 存储器读取，直到微处理
器由于低电压状态而复位。 
c。 ECU 应保留 Type 1 存储器的内容，直到微处
理器由于低电压状态而复位。 
3.3.2.3.3.2 类型 2 存储器（通常为静态 RAM）。 
a。 ECU 应能够写入 2 型存储器，直到微处理器由
于低电压状态而复位。 
b。ECU 应能够从类型 2 存储器读取，直到微处理
器由于低电压状态而复位。 
c。 ECU 应通过低压曲柄条件（工作电压范围要求
为 4.5 V）保留 2 型存储器的内容。 
3.3.2.3.3.3 类型 3 存储器（通常为 EEPROM）。 
a。 ECU 应能够写入 3 型存储器，直到微处理器由
于低电压状态而复位。 
b。ECU 应能够从类型 3 存储器读取，直到微处理
器由于低电压状态而复位。 
c。 ECU 应在车辆使用寿命内的所有工作电压范围
内保留 3 型存储器的内容。 
d。 ECU 应防止写入功能（软件）规范中标识的类
型 3 存储器位置。 
e。可以写入 ECU 类型 3 存储器的次数应满足车辆
使用寿命的使用要求。 
注意：对于每个 Ignition_15 周期需要存储一次（或
多次）参数的 ECU，供应商在选择适当的非易失性
可重写存储器概念时需要特别注意，以便存储这些
参数。在这种情况下，供应商应假设至少 50000 次
Ignition_15 循环。


### 第 26 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
26/74 
2005 年 10 月 
 
 
 
3.3.2.3.3.4 类型 4 存储器（通常为闪存）。 
a。 ECU 应能够在与电气负载代码字母 C
一致的工作电压范围内写入 4 类存储器。 
b。ECU 应能从 4 型存储器读取，直到微处
理器由于低电压状态而复位。 
c。 ECU 应在车辆使用寿命期间的所有工作
电压范围内保留 4 型存储器的内容，通常
为 15 年。 
d。 ECU 应防止写入功能（软件）规范中
标识的 Type 4 存储器位置。 
e。可以写入 ECU 4 类型存储器的次数至少
为 100 次。 
f。根据所使用的编程算法，ECU 供应商应
在最坏情况下提供有保证的最大 4 类存储
器消除时间。 
g。根据所使用的编程算法，ECU 供应商应
在最坏情况下提供有保证的最大 4 类存储
器写入时间。 
h。 4 型存储器应用于存储操作软件和校准。 
i。 ECU 应能够以下列组合在 4 型存储器中
编写操作软件和校准： 
i。仅限校准 
ii。操作软件和校准 
注意：在估计数据保留和最小编程周期数
时，组件平均生命周期温度将适用。供应
商应获得所选微控制器制造商的批准，以
确定是否可以考虑 ECU 的平均温度来满足
这些要求。 
3.3.2.3.3.5 类型 5 内存（通常是 Flash 的引
导块）。 
a。 ECU 应能够在与电气负载代码字母 C
一致的工作电压范围内写入 5 类存储器。
在开发过程中的某个时间点，写入 ECU 5
类存储器的能力应仅限于供应商。 
b。ECU 应能够在工作电压范围 VR2 中读取
5 类存储器。在开发过程中的某个时间点，
从 ECU 5 类型存储器读取的能力应仅限于
供应商。
c。ECU 应在车辆使用寿命的所有工作电压范围
内保留 5 型存储器的内容，通常为 15 年。 
d。可以写入 ECU 类型 5 存储器的次数至少为
100 次。 
e。类型 5 内存应用于存储启动软件。 
3.3.2.3.4 无引线芯片元件。在有机基板（FR4，
聚酰亚胺，CEM3）上不得使用大于 1210 尺寸
的无引线元件。如果需要更大的元件，则应选
择 SMD 引线部件。这些要求基于热膨胀不匹配
故障。 
3.3.2.3.5 陶瓷片式电容器材料。陶瓷片式电容
器的电介质应为 COG（NPO）或 X7R。 
不得使用 Y5V 材料。 
3.3.2.3.6 径线电解电容。铝电容器。使用这种
类型的部件时，请始终选择带有通气橡胶支座
密封的型号。电容器外套管不能保证是绝缘体，
橡胶支座也可以防止泄漏产品从电容器密封通
风口封装。 
3.3.3 设计指南和约束。所述指南是一般性的。
在选择最终设计之前，应使用适当的 GM DRE
讨论和评估与它们的偏差。 
3.3.3.1 组件和子系统级别。 
3.3.3.1.1 I / O 过滤。 
a。所有 I / O 信号上的电压和电流压摆率必须
最小化，特别是重复信号。指南：dv / dt <1V / 
us，di / dt <100mA /μ s。 
b。应避免 RF 整流。必须注意输入端的二极管。 
c。必须在每个端口上实施低通滤波。可能需要
在带宽和 ESD 保护/ RF 滤波之间进行权衡。这
对于串行通信的端口尤为重要。 
d。必须在电源输入端提供大容量存储电容，其
ESR 值低至-40°C。 
e。每个外部 I / O 引脚必须有一个直接连接到
地平面的旁路电容。建议值为 1nF 至 10nF 100V 
DCWV X7R 电容。电容器的放置必须防止滤波和
未滤波侧之间的串扰。


### 第 27 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
27/74 
 
 
* 
 
3.3.3.1.2 内部电路设计指南。  
a。 所有重复线都应包括串联阻抗或添加一
个阻抗的规定，例如 时钟线，地址线，数据
线。 阻抗必须放在源附近，并与线路的特征
阻抗相匹配。 
b。 双极和 FET 晶体管应配备一个基极/栅极
到发射极/源极电阻器，以及一个与该电阻器
并联的滤波电容器。 此要求适用于 NPN / 
PNP 和 N 信道/ P 信道类型。 在初始化之前，
微控制器 I / O 引脚通常在复位期间和复位后
作为浮动输入启动，此基极/发射极或栅极/
源极电阻将阻止输出信号上的未知状态。
/ 20，
= 300 / f，f [MHz]），其中 f 是最高响应
频率。 
c。 如果使用导电盒，必须仔细考虑接地原理。 
作为一般规则，为了避免通过 PCB 的 DC 电流
路径，PCB 的接地必须通过电容器连接到导电
壳体和车身。 如果直接连接是首选 - 由于使用
连接进行冷却，EMC 或其他原因 - 应与 GM 的
EMC 负责工程师讨论和评估。 
******* PCB HW 设计要求和指南。 
*******.1 基板层结构 
a。 所有 ECU 报价和设计均应基于多层设计概念， 
除非根据 GMW3097 和 GMW3091 满足 EMC 要求，并
且层数较少。
out 
 
 
in 
in 
* 
out 
b。 所有引线元件焊盘/孔和通孔必须通过电镀。 
c。 没有引线元件或组件应安装在非通孔电镀孔  
中。 
d。固体接地平面是优选的。 
* = C optional 
 
图 21：基极/栅极电阻器 
a。 必须控制 FET 的上升和下降时间。 
b。 必须提供大容量电容器。 该值必须至少是整个
PCB 的单个去耦值的 10 倍。 
c。 必须在所有 IC 上提供去耦电容。 必须根据转换
时所需的电荷和允许的电压降来计算这些值。 推荐
值在 10-100 nF 范围内。 对于最大共振频率，该值
不能太高，物理组件尺寸必须尽可能小（最大尺寸
0805）。 微控制器和功率驱动器可能需要多个分布
式去耦电容。 
3.3.3.1.3 连接器选择/应用。 连接器必须具有与
PCB 的逻辑区域对应的引脚配置。 
3.3.3.1.4 EMC 案例设计 
a。 如果使用μ C 外部的并行总线连接存储
器，强烈建议使用导电外壳。
e。 应尽可能使用内部电源平面/平面，并与其
他电源线组合使用。 
*******.2 EMC PCB 设计。 
a。分区。 高频区域应与低频区域分开。 模拟
电路必须组合在一起并与数字电路分开。 
b。 时钟线应短且位于接地和/或供电平面之间。 
c。 外部，串行，通信线路应短，即收发器必须
靠近连接器放置。 
d。 数字和模拟走线不得并行布线。 
*******.3 SMD 焊盘图案设计。 表面贴装元件的
焊盘图案设计应遵循 IPC-7351 给出的指导原则。 
a。 该标准提供了 SMD 焊盘图案的尺寸，形状
和公差指南，以提供正确的焊脚，并允许检查
和测试这些焊点。 
b。带有与 PCB 表面接触的金属片的元件不得与


### 第 28 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
28/74 
2005 年 10 月 
 
 
 
外来电网和接地层重叠（毛刺通过阻焊层短路
的风险）。 
c。 PCB 的金属机械附件不得与除地之外的外来电气
或阻焊绝缘网接触。 
d。 阻焊材料绝不能作为绝缘层处理。 
e。 接地和电源平面连接的引线和 SMD 焊盘应始终
具有热释放（车轮）。 
*******.4 引线和通孔焊盘设计。用于带引线元件和
通孔的垫/钻设计应保证不会发生钻孔和电镀孔壁的
环形孔爆裂。最小环形圈要求应符合 IPC 第 3 类
（IPC-6012）规定的最小 25m。不接受孔壁的突破，
因为这很容易与导体的内层和外层隔离间隙以及内
层和外层导体与岸台界面的长期可靠性相冲突。 
*******.5 关于电解铝电容器的特殊布线要求。对于
安装在 PCB 上的径向铝电容器，橡胶密封在电路板
表面，绝不会在密封侧下方找到任何铜迹线或接地
平面。始终在 PCB 背面或内层布线。 
3.3.4 工艺。组装印刷电路板（PCB）的一般工艺要
求应符合 J-STD-001D。 
该标准包括对总生产工艺，实践，使用材料，工艺
和验收标准的要求。 
该标准将用作汽车项目期间所有开发阶段/交付中电
子产品的验收标准，从原型到串行交付。 
GM 和供应商应根据 J-STD-001D 的§1.3 商定分类条
款。 
默认分类应为第 2 类。 
安全相关模块应具有第 3 类要求。 
3.3.5 互换性。不适用。 
3.5 文档。 供应商应提供以下文件： 
a。 电路原理图 
b。 组件件清单（BOM） 
c。 性能/功能规范 
d。 组件故障检测规范 
e。 接口规范
f。最差情况分析，潜行电路，电气和热分析 
g。PCB 布局 
h。组件布置图 
d。工程验证计划（包括可靠性和系统安全活动） 
j。设计验证计划 
f。产品验证计划 
3.5 系统/子系统/组件/部件售后的支持。不适
用。 
3.6 系统/子系统/组件/部件操作员培训。不适
用。 
3.7 系统/子系统/组件/部件特性。不适用。 
 
4 验证 
4.1 概述。供应商负责验证适用的 SSTS / CTS 中
规定的所有组件要求。 
4.1.1 供应商验证计划。供应商应创建组件验证
计划/子系统验证计划（CVP / SSVP），其中包
含相关测试的详细描述，适用测试程序的参考，
详细的验收标准以及符合主时序图的时间表。
该计划应在测试开始前至少 60 天提交，并在实
施前由 GM 工程部门审核和批准。 
CVP / SSVP 应包含以下与硬件和 EMC 性能和耐
久性特性相关的项目： 
•环境和耐久性测试程序以及根据 GMW3172 中
规定的适当车辆安装位置的设计验证图表的测
试样品数量。 
•根据 GMW 3103，EMC 测试程序和测试样品数
量。 
验证计划还应理解可靠性要求的验证，供应商
应确保具有足够统计能力的人员被分配到这些
验证活动。 
4.1.2 组件更改所需的测试。组件的设计和制造
的任何变化（例如零件值和/或类型，功能，制
造过程等）应通知 GM，并且必须经 GM DRE 批
准，然后可能需要重新测试组件和子系统。


### 第 29 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
29/74 
 
 
 
4.1.1 EMC 验证过程和测试计划。 供应商应符
合 GMW 3103 规定的工艺要求。 
4.2 验证交叉参考指数。 为每个 CTS / SSTS 项目
定义。 
4.3 支持段落。 不适用。 
5 运输规定 
6 注意事项 
6.1 词汇。 不适用。 
6.2 缩略语，缩写和符号。 
CAN 
控制器区域网络 
CPU 
中央处理器 
CTS 
组件技术规范 
CVP 
组件验证计划 
DRE 
设计发布工程师 
DTC 
诊断故障代码 
ECU 
电子控制单元 
E/E 
电气/电子 
EEPROM 
电可擦除可编程只读存储器 
EMC 
电磁兼容 
ESR 
等效串联电阻 
FET 
场效应晶体管 
FMEA 
失效模式效应分析 
FSC_ 
功能状态分类 
LDO 
低压降（电压调节器） 
NTC 
负温度系数 
OVP 
过压保护
PCB 
印刷电路板 
PTC 
正温度系数 
PTH 
镀通孔 
PWM 
脉冲宽度调制 
RAM 
随机存取存储器 
ROM 
只读存储器 
SC 
GND 或 VBATT 短路 
SMD 
表面安装器件 
SOAR 
安全操作区 
SSTS 
子系统技术规范 
 
7 附加段落 
7.1 本规范提供的所有材料必须符合 GMW3001“材料规范
规则和条例”的要求。 
7.2 本规范提供的所有材料必须符合 GMW3059，零件限制
和可报告物质的要求。 
 
8 编码系统 
本规范应在其他文件，图纸，VTS，CTS 等中引用如下： 
 
GMW14082 
 
9 发布和修订 
9.1 发布。 该通用规范起源于 2004 年 12 月。它于 2005 年
10 月由全球车身内部/外部团队首次批准。 它于 2005 年 10
月首次发布。


### 第 30 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
30/74 
2005 年 10 月 
 
 
 
 
附录 A 电气接口 
详细的接口规范 
_x 后缀表示一个数字，用于为同一类型的接口排序不同的
参数。 表格给出的参数应由 CTS / SSTS 文档用于所有通
用类型的接口。 
原理图中使用过的组件的初始公差为： 
R: 5% 
C: 10% 
如果没有其他指定。 
当显示1％电阻值时，在温度循环，高温存储和高湿度等使用寿命老化后，它们的总容差应在整个温度范围内达到最
大±2.5％。 如果选择陶瓷片式电阻器，这实际上需要薄膜设计类型。 
A1 电源电路 
A1.1 Vbatt_x 
 
 
 
名称(s) 
Vbatt_x 
类型 
电源，车辆电池的永久供电 
电压范围，最大额定值( 1s < t < 1 分钟) 
-13.5 to +26.5 V 
电压范围，正常 
0 to Vmax 
电压范围，正常运行 
Vmin to Vmax 
最大负载电流 
取决于 E / E 设备，> 3A 
最大励磁电流 
最大载荷 x 10，持续时间<1s 
反极性保护机械化 
二极管/肖特基/ MOSFET，Vmin 相关 
Alternative reverse protection schemes 
Vbatt 
Vbatt_prot 
* 
* 
Power_gnd 
Signal_gnd 
 
* = Complete transient protection will depend on GMW3097 results 


### 第 31 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
31/74 
 
 
 
A1.2 Vrun/曲柄_x 以及 Vacc_x 
 
 
 
名称(s) 
Vrun/曲柄_x and/or Vacc_x 
类型 
电源或信号。 点火钥匙状态受控制 
电压范围, 最大额定 ( 1s < t < 1 分钟) 
-13.5 to +26.5 V 
电压范围, 正常 
0 to Vmax 
电压范围，正常工作 
Vmin to Vmax 
最大负载电流 
E/E 设备依赖， 
最大励磁电流 
最大负载 x 10, 持续时间 < 1s 
反极性保护机械化 
二极管/肖特基 
电源关闭，电压放电时间常数 
约 1s (电源接地应存在电阻路径) 
Vrun/crank 
Vrun/crank_prot 
Vacc 
Vacc_prot 
* 
* 
Power_gnd 
Signal_gnd 
 
* = Complete transient protection will depend on GMW3097 results 


### 第 32 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
32/74 
2005 年 10 月 
 
 
 
A1.3 Vref_5V_x 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
名称(s) 
Vref_5V_x 
类型 
调节 5V 输出，切换 
Vs 范围, 最大额定 (1s<t<1 分钟) 
-13.5V to +26.5 V 
Vs 范围, 正常 
0 to Vmax 
Vs 范围, 正常运行 
Vmin to Vmax 
V 外, Vs = Vmin 至 Vmax 的电压输出  
5.0V +-2% 
I_外, 最大负载电流 
100mA 
C_外, 无励磁电流关断的电容负载 
10µF 
反极性保护机械化 
Si Diode，肖特基或 MOS 取决于 Vmin 
电源关闭，电压放电时间常数 
约 1s (电源接地应存在电阻路径) 
Vcc 
I_out 
Vs 
  
 
 Vref_5V  
D* 
5V LDO 
Voltage 
Regulator 
+ 
C_out 
Vout 
uC 
- 
Power_gnd 
signal_gnd 
D* = Reverse polarity protection mechanization Vmin dependent 
Short to Gnd & Vbatt 
Protection 


### 第 33 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
33/74 
 
 
 
A1.4 Vref_12V_x 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
名称(s) 
Vref_12V_x 
类型 
未调节的 12V 输出，切换 
Vs 范围, 最大额定 (1s<t<1 分钟) 
-13.5 to +26.5 V 
Vs 范围, 正常 
0 to Vmax 
Vs 范围, 正常运行 
Vmin to Vmax 
V 外, 电压输出 
12.0V 约, Vs-1V, 限制为最大+ 40V 瞬态 
I_外, 最大负载电流 
100mA 
C_外, 无励磁电流关断的电容负载 
10µF 
反极性保护机械化 
Si Diode，肖特基或 MOS 取决于 Vmin 
电源关闭，电压放电时间常数 
约 1s (电源接地应存在电阻路径) 
I_out 
Vs 
  
  
Vref_12V 
  
D* 
Vz 
5V LDO 
Voltage 
Regulator 
+ 
C_out 
Vout 
uC 
- 
Power_gnd 
signal_gnd 
D* = Reverse polarity protection mechanization Vmin dependent 
Vcc 
Short to Gnd & Vbatt 
Protection 


### 第 34 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
34/74 
2005 年 10 月 
 
 
 
A2 接地电路 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
A2.1 电源接地_x 
 
名称(s) 
电源接地_x 
类型 
电源，接地返回 Body_gnd 
最大返回电流。 内部和外部负载返回电流的总和 
E/E 设备依赖, >3A 
最大回流励磁电流 
最大负载 x 10, 持续时间 < 1s 
反极性保护机械化 
N/A 
同一 E / E 设备内的最大接地电压梯度。 电源接地，信
号接地和 Signal_rtn 的地线或平面 
10mV 
Vbatt_01 
Vbatt_02* 
Vref_12V_01 
I/O 
Signal_02 
  
  
Signal_01 
  
out1 
out2 
Power_gnd_01 
Power_gnd_02* 
Signal_rtn_01 
Signal_rtn_02 
Signal_gnd 
 
* = Multiple Vbatt_ and Power_gnd_ connections used where 
maximum single connector pin currents are exceeded 
Sensor/Slave 
Sensor/Slave 


### 第 35 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
35/74 
 
 
 
A2.2 Signal_Rtn_x 
 
名称(s) 
Signal_rtn_x 
类型 
信号返回，传感器或从属单元 
外部传感器返回信号的总和点。 
 
最大返回电流。 内部和外部负载返回电流的总和 
E/E 设备依赖, >3A 
最大回流浪涌电流 
最大负载 x 10, 持续时间 < 1s 
反极性保护机械化 
N/A 
同一 E / E 设备内的最大接地电压梯度。 电源接地，信
号接地和 Signal_rtn 的地线或平面 
10mV 


### 第 36 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
36/74 
2005 年 10 月 
 
 
 
A2.3 电源接地_Stall_x 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
名称(s) 
电源接地_stall_x 
类型 
电源，接地返回 body_gnd 以停止负载 
最大返回电流。 外部负载返回电流的总和 
取决于 E / E 设备，在受控负载关闭之前电机失速> 
30A。 
最大回流浪涌电流 
最大负载 x 10, 持续时间 < 1s 
反极性保护机械化 
N/A 
同一 E / E 设备内的最大接地电压梯度。 电源接地，信
号接地和 Signal_rtn 的地线或平面 
用于阻止高电流负载，无法满足 1V 的接地偏置要求。 
SC-protection 
M_supply  
Stalling 
Motor Load 
Vbatt_01 
Vbatt_02 
Vref_12V_01 
5V LDO 
Regulator 
Signal_01 
out1 
I/O 
R 
uC 
Power_gnd_01* 
20*R 
Signal_rtn_01 
UV-protection 
Signal_gnd 
D* 
20*R 
  
Power_gnd_stall* 
SC-protection 
Body_gnd 
* = Multiple Power_gnd_ connections used with seperate ground wire returns back to 
common body ground bolt. Ground diode D* to prevent floating conditions if 
Power_gnd_01 is lost. (UV = Under Voltage, SC = Short Circuit) 
Sensor/Slave 
M_rtn 


### 第 37 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
37/74 
 
 
 
A3 离散数字和开关接口 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
电气特性 
 
参数 
符号 
条件 
min 
typ 
max 
Unit 
备注 
供电电压 
Vs 
最大额定 
-13.5  
+26.5 
V 
无损坏 
供电电压 
Vs 
具有有效逻辑状态的
运行范围 
Vmin 
14 
Vmax 
V 
 
输入电压范围 
Ui 
最大额定 
-1.0 
 
+26.5 
V 
 
输入电压 
Ui 
运行范围 
-1.0 
 
Vmax+1 
V 
 
输入逻辑低状态 
Uil 
电压范围 
-1.0 
 
+2.0 
V 
逻辑 0 
输入逻辑高状态 
Uih 
电压范围 
+5.5 
 
Vmax+1 
V 
逻辑 1 
输出电压低 
Uol 
电压范围@ Io = 100mA 
0 
0.3 
1.0 
V 
逻辑 0 
输出电压高 
Uoh 
电压范围 @ Io = -100mA 
Vs-1 
 
Vs 
V 
逻辑 1 
输出电压 dVdt 
tr, tf 
0.1 Uoh to 0.9 Uoh,类型 A 
0.1 
 
1.0 
V/µs  
 
R1 
 
 
33 
 
k
 
 
R2 
 
 
100  
k
Vth =30/70% 
 
R2 
 
 
220  
k
Vth = 35/65% 
 
Rp 
 
680 
1.2k 
3k 

 
 
C1 
 
 
33 
 
nF 
R1*C1=1ms 
Vmin，Vnom 和 Vmax 由 GMW3172 代码串的电子代码字母定义。
Vign_prot 
Vs_sw 
Vs 
Rp_source 
Rpu 
D* 
transmitter 
receiver 
Rpd 
Signal source 
or switch 
Rp 
5V* LDO 
Voltage 
Regulator 
Io 
Ii 
  R1  
Vth 
I_inj 
    + 
- 
Power_gnd 
 
5V*: Lower Vcc supply levels than 5V can be used 
+ 
Uo 
- 
+ 
Ui 
- 
R2 
C1 
uC 
Gnd 
10nF@Input_pin 
Signal_gnd    Vth/L/H: common values: 30/70% or 35/65% of Vcc for CMOS 
I_inj: Injection current into IC-input when Vin > Vcc 
D* = Reverse polarity protection mechanization Vmin dependent 
Vcc 


### 第 38 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
38/74 
2005 年 10 月 
 
 
 
A3.1 离散数字输入，低电平有效 
 
 
IDL_x 
名称(s) 
IDL_x 
类型 
数字输入，低电平有效 
Rpu 上拉电压源 
Vs_sw or Vign 
唤醒类型 
N/A 
Iil_min , 活动状态下所需的最小电流 
-4 mA* 
Iil_max, 活动状态下所需的最大电流 
-20 mA* 
接地偏移电压 
0 to ±1.0 V 
低通滤波器 - 时间常数 
1ms 
Tmin -最小输入识别时间 
30 ms 
Tmax -最大输入识别时间 
40 ms 
Nmin -相同状态样本的最小数量 
3 
短路保护 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
*在 Vnom 至 Vmax 的工作电压范围内，Vs 给出的最小和最大有效状态电流
Rpu_source 
transmitter 
receiver 
Alternative Voltage 
pullup sources 
Vign_prot 
Vs_sw 
Vs 
Vcc 
D* 
LS-driver or 
mechanical switch 
R_no_float 
Rpu 
5V* LDO 
Voltage 
Regulator 
Io 
Ii 
Vth 
I_inj 
...... 
   R1  
  
 
    + 
   
Power_gnd 
 
- 
5V*: Lower Vcc supply levels than 5V can be used 
 +  
+ 
Ui 
- 
R2 
C1 
uC 
...... 
Uo 
- 
10nF@Input_pin 
Gnd 
Vth/L/H: common values: 30/70% or 35/65% of Vcc for CMOS 
signal_gnd 
I_inj: Injection current into IC-input when Vin > Vcc 
D* = Reverse polarity protection mechanization Vmin dependent 
One common R_no_float load for each separate Vs_sw/Vign_prot net 


### 第 39 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
39/74 
 
 
 
ODL_x 
名称(s) 
ODL_x 
类型 
数字输出，低电平有效 
输出电源 
Gnd 
Uol_max , 活动状态下的最大饱和电压 
1.0V @ 100mA 
Iol_min , 在活动状态下所需的最小吸收电流 
100 mA @ Vmin to Vmax 
Io_leak , 无效状态下的最大漏电流 
10 µA max @ Vnom 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 


### 第 40 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
40/74 
2005 年 10 月 
 
 
 
A3.2 离散数字接口，高电平有效 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
IDH_x 
名称(s) 
IDH_x 
类型 
数字输入，高电平有效 
Rpd 下拉电压源 
信号接地 
唤醒类型 
N/A 
Iih_min , 活动状态下所需的最小电流 
4 mA* 
Iih_max, 活动状态下所需的最大电流 
20 mA* 
接地偏移电压 
0 to ±1.0 V 
低通滤波器 - 时间常数 
1ms 
Tmin -最小输入识别时间 
30 ms 
Tmax -最大输入识别时间 
40 ms 
Nmin -相同状态样本的最小数量 
3 
电路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
*在 Vnom 至 Vmax 的工作电压范围内，Vs 给出的最小和最大有效状态电流
Vs_prot > 5.5V 
HS-driver or momentary 
mechanical switch 
Vs 
...... 
D* 
transmitter    receiver 
Vcc 
5V* LDO 
Voltage 
...... 
Regulator 
Io 
Ii 
R1 
Vth 
I_inj 
+ 
- 
Power_gnd 
5V*: Lower Vcc supply levels than 5V can be used 
+ 
Uo 
- 
+ 
Ui 
- 
10nF@Input_pin 
Rpd 
R2 
C1 
uC 
signal_gnd 
Gnd 
Vth/L/H: common values: 30/70% or 35/65% of Vcc for CMOS 
I_inj: Injection current into IC-input when Vin > Vcc 
D* = Reverse polarity protection mechanization Vmin dependent 
Vs_prot 


### 第 41 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
41/74 
 
 
 
IDH_WUR_x 
名称(s) 
IDH_WUR_x 
类型 
数字输入，高电平有唤醒 
Rpd 下拉电压源 
信号接地 
唤醒类型 
在输入电压上升沿唤醒 
Iih_min , 活动状态下所需的最小电流 
4 mA* 
Iih_max, 活动状态允许的最大电流 
20 mA* 
低通滤波器 - 时间常数 
1ms 
Tmin -最小输入识别时间 
30 ms 
Tmax -最大输入识别时间 
40 ms 
Nmin -相同状态样本的最小数量 
3 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
*在 Vnom 至 Vmax 的工作电压范围内，Vs 给出的最小和最大有效状态电流 
ODH_x 
名称(s) 
ODH_x 
类型 
数字输出，高电平有效 
输出电源 
Vs_prot 
Uoh_min , 最小输出电压处于活动状态 
Vs-1.0V @ 100mA 
Ioh_min , 活动状态下所需的最小源电流 
-100 mA @ Vmin to Vmax 
Io_leak , 无效状态下的最大漏电流 
-10 µA max @ Vnom 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 


### 第 42 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
42/74 
2005 年 10 月 
 
 
Vs 
Vs_sw 
Vcc 
Vs_prot 
Rp_source 
D* 
transmitter 
receiver 
Rpu 
Rpd 
f_pwm < 400 Hz 
Rp 
5V LDO 
Voltage 
Regulator 
PWM out 
   R1  
  
 
Vth 
I_inj 
+ 
- 
Power_gnd 
 
PWM transmitter 
+ 
Utx 
- 
+ 
Urx 
- 
R2 
C1 
uC 
1nF@Input_pin 
Type A, General PWM interface. Vs supplied 
signal_gnd 
Gnd 
Vs 
Rp_source 
Vcc_sw 
Vcc 
D* 
slave_supply 
Rpu 
Rpd 
f_pwm < 1kHz 
Rp 
5V LDO 
Voltage 
Regulator 
Itx  
Irx 
Vth 
I_inj 
PWM out 
  R1  
  
 
+ 
- 
Power_gnd 
 
+ 
Slave unit 
Utx 
- 
+ 
Urx 
- 
signal_rtn 
C1 
uC 
1nF@Input_pin 
signal_gnd 
Type B, slave unit PWM interface with Vcc <= 5V supply 
D* = Reverse polarity protection mechanization Vmin dependent 
 
A4 PWM 型电压接口 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
电气特性 
 
参数 
符号 
条件 
min 
typ 
max 
Unit 
注释 
供电电压 
Vs 
最大额定 
-13.5  
+26.5 
V 
无损坏 
供电电压 
Vs 
具有有效逻辑状态的工作范围 
Vmin 
14 
Vmax 
V 
 
输入电压范围 
Urx 
最大额定 
-1.0 
 
+26.5 
V 
 
 
 
输入电压 
 
 
Urx 
 
 
类型 A 运行范围, Vs 供应 
 
 
-1.0 
 
 
 
Vmax+1 
 
 
V 
 
 
接地偏移允许
+ - 1V 
输入逻辑低状态 
Urxl 
类型 A 电压范围, Vs 供应 
-1.0 
 
+2.0 
V 
 
输入逻辑高状态 
Urxh 
类型 A 电压范围, Vs 供应 
+5.5 
 
Vmax+1 
V 
 
输出电压 
Utx 
类型 A 运行范围, Vs 供应 
0 
 
Vmax 
 
f < 400Hz 
输出电压低 
Utxl 
类型 A 电压范围, Vs 供应 
0 
0.3 
1.0 
V 
逻辑 0 
输出电压高 
输出电压 dVdt 
Utxh 
tr, tf 
类型 A 电压范围, Vs 供应 0.1 Utxh to 0.9 
Utxh, 类型 A 
Vs-1 
0.1 
 
Vs 
1.0 
V 
V/µs 
逻辑 1 
输入电压 
Urx 
类型 B 运行范围, Vcc 供应 
0 
 
Vcc 
V 
接地偏移允许
0V 
输入逻辑低状态 
Urxl 
类型 B 运行范围, Vcc 供应 
0 
 
0.3Vcc 
V 
Signal_rtn ref. 
输入逻辑高状态 
Urxh 
类型 B 运行范围, Vcc 供应 
0.7Vcc  
Vcc 
V 
Signal_rtn ref. 
输出点暗影 
Utx 
类型 B 运行范围, Vcc 供应 
0 
 
Vcc 
 
f < 1 kHz 
输出电压低 
Utxl 
电压范围 @ Io = 25mA, 类型 B 
0 
0.3 
1.0 
V 
逻辑 0 
Itx 
Irx 
Vs_prot 


### 第 43 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
43/74 
 
 
输出电压高 
Utxh 
电压范围 @ Io = -25mA, 类型 B 
Vcc-1  
Vcc 
V 
逻辑 1 


### 第 44 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
44/74 
2005 年 10 月 
 
 
 
 
参数 
符号 
条件 
min 
typ 
max 
Unit 
注释 
输出电压 dVdt 
tr, tf 
0.1 Utxh to 0.9 Utxh, 类型 B 
0.1 
 
1.0 
V/µs  
 
R1 
 
 
33 
 
k
 
 
R2 
 
 
100  
k
Vth=30/70% 
 
R2 
 
 
220  
k
Vth=35/65% 
 
Rp 
 
680 
1.2 
3.3 
k
 
 
C1 
 
 
180  
pF 
 
Vmin，Vnom 和 Vmax 由 GMW3172 代码串的电子代码字母定义。 Vcc 通常为 5.0V


### 第 45 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
45/74 
 
 
transmitter 
receiver 
Vs_sw 
Vs 
D* 
Vcc 
R_no_float 
f_pwm < 400 Hz 
Rpu 
5V LDO 
Voltage 
Regulator 
LS_driver 
   R1  
Vth 
I_inj 
Itx 
+  
Utx 
- 
Irx 
    + 
- 
 Power_gnd 
 
+ 
Urx 
- 
R2 
C1 
uC 
1nF@Input_pin 
signal_gnd 
Gnd 
Type A, Vs supplied, Active low PWM signal. 
D* = Reverse polarity protection mechanization Vmin dependent 
One common R_no_float load for each separate Vcc_sw net 
 
A4.1 低电平有效 PWM 接口， Vs 供应. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
IPL_Vs_x 
名称(s) 
IPL_Vs_x 
类型 
数字 pwm 信号接收器，低电平有效 
Rpu 上拉电压源 
Vs_sw 
唤醒类型 
N/A 
Irxl_min活动状态下所需的最小电流 
-4 mA* 
Irxl_max活动状态允许的最大电流 
-20 mA* 
接地偏移电压 
0 to ±1.0 V 
低通滤波器 - 时间常数 
5 µs 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
•在 Vnom 至 Vmax 的整个工作电压范围内，Vs 给出的最小和最大有效状态电流 
OPL_Vs_x 
名称(s) 
OPL_Vs_x 
类型 
数字 pwm 信号发射器，低电平有效 
输出电源 
Gnd 
Utxl_max -处于活动状态的最大饱和电压 
1.0 V @ 100mA Itx 
Itxl_min –最小吸收电流处于活动状态 
100mA @ Vmin to Vmax 
Ileak_max -最大漏电流处于非激活状态 
10 A 
输出频率 
x Hz +-0.5% (< 400Hz, f-jitter<0.25%)) 
正常输出信号 PWM 占空比范围 
5-95% 
正常输出信号 PWM 占空比分辨率 
0.5% 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
   
Vs_prot 


### 第 46 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
46/74 
2005 年 10 月 
 
 
5V LDO 
Voltage 
Regulator 
receiver 
Vcc_sw 
Vcc 
transmitter 
 
slave_supply 
Vs 
D* 
f_pwm < 1 kHz 
Rpu 
LS_driver 
Irx 
   R1  
Vth 
I_inj 
    + 
- 
+ 
Utx 
- 
+ 
Urx 
- 
Power_gnd 
 
C1 
uC 
signal_rtn 
1nF@Input_pin 
signal_gnd 
Type B, Vcc supplied. Active low PWM signal. 
D* = Reverse polarity protection mechanization Vmin dependent. 
One common R_no_float load for each separate Vcc_sw net 
 
A4.2 有源低 PWM 接口, Vcc 供应 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
IPL_Vcc_x 
名称(s) 
IPL_Vcc_x 
类型 
数字 pwm 信号接收器，低电平有效 
Rpu 上拉电压源 
Vcc_sw (正常 5V) 
唤醒类型 
N/A 
Irxl_min活动状态下所需的最小电流 
-4 mA* 
Irxl_max活动状态允许的最大电流 
-20 mA* 
接地偏移电压 
0 V 
低通滤波器 - 时间常数 
2.2 µs 
短路保护 (包括 Vcc_sw 产出): 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
•在 Vmin 到 Vmax 的整个工作电压范围内，Vs 给出的最小和最大有效状态电流 
OPL_Vcc_x 
名称(s) 
OPL_Vcc_x 
类型 
数字 pwm 信号发射器，低电平有效 
输出电源 
来自接收器信号接地的 Signal_rtn 线 
Utxl_max -处于活动状态的最大饱和电压 
1.0 V @ 25 mA Itx 
Itxl_min –最小吸收电流处于活动状态 
25 mA @ Vcc=5V 
Ileak_max -最大漏电流处于非激活状态 
10 A 
输出频率 
x Hz +-0.5% (< 1 kHz, f-jitter<0.25%)) 
正常输出信号 PWM 占空比范围 
5-95% 
正常输出信号 PWM 占空比分辨率 
0.5% 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
 
Itx 
R_no_float 


### 第 47 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
47/74 
 
 
transmitter 
receiver 
Vs 
D* 
Vcc 
f_pwm < 400 Hz 
5V LDO 
Voltage 
Regulator 
   R1  
Vth 
I_inj 
Itx 
+  
Utx 
- 
Irx 
    + 
- 
Power_gnd 
 
+ 
Urx 
- 
Rpd 
R2 
C1 
uC 
1nF@Input_pin 
Signal_gnd 
Type A, Vs supplied. Active high PWM signal. 
D* = Reverse polarity protection mechanization Vmin dependent 
Gnd 
 
A5 高电平有效 PWM 接口 
A5.1 有源高 PWM 接口, Vs 供应 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
IPH_Vs_x 
名称(s) 
IPH_Vs_x 
类型 
数字 pwm 信号接收器，高电平有效 
Rpd 下拉电压源 
信号接地 
唤醒类型 
N/A 
Irxh_在活动状态下所需的最小最小电流 
-4 mA* 
Irxh_活动状态下允许的最大最大电流 
-20 mA* 
接地偏移电压 
0 to ±1.0 V 
低通滤波器 - 时间常数 
5 µs 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
•在 Vnom 至 Vmax 的整个工作电压范围内，Vs 给出的最小和最大有效状态电流 
OPH_Vs_x 
名称(s) 
OPH_Vs_x 
类型 
数字 pwm 信号发射器，高电平有效 
输出电源 
Vs_prot 
Utxh_min -最小输出电压处于活动状态 
Vs-1.0 V @ 100mA Itx 
Itxh_min –最小源电流处于活动状态 
-100mA @ Vmin to Vmax 
Ileak_max -最大漏电流处于非激活状态 
10 A 
输出频率 
x Hz +-0.5% (< 400Hz, f-jitter<0.25%)) 
正常输出信号 PWM 占空比范围 
5-95% 
正常输出信号 PWM 占空比分辨率 
0.5% 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
Vs_prot 
HS_driver 


### 第 48 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
48/74 
2005 年 10 月 
 
 
 
A5.2 有源高 PWM 接口，Vcc 供应 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
IPH_Vcc_x 
名称(s) 
IPH_Vcc_x 
类型 
数字 pwm 信号接收器，高电平有效 
Rpd 下拉电压源 
信号接地 
唤醒类型 
N/A 
Irxl_min活动状态下所需的最小电流 
-4 mA* 
Irxl_max活动状态允许的最大电流 
-20 mA* 
接地偏移电压 
0 V 
低通滤波器 - 时间常数 
2.2 µs 
短路保护 (包含 Vcc_sw 产出): 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
•在 Vmin 到 Vmax 的整个工作电压范围内，Vs 给出的最小和最大有效状态电流。 Vcc 通常为
5.0VOPH_Vcc_x 
名称(s) 
OPH_Vcc_x 
类型 
数字 pwm 信号发射器，高电平有效 
输出电源 
来自接收器的 Vcc_sw 线 
Utxh_min –处于活动状态的最小输出电压 
1.0 V @ 25 mA Itx 
Itxh_min –最小源电流处于活动状态 
-25 mA @ Vcc=5V 
Ileak_max -最大漏电流处于非激活状态 
10 A 
输出频率 
x Hz +-0.5% (< 1 kHz, f-jitter<0.25%)) 
正常输出信号 PWM 占空比范围 
5-95% 
正常输出信号 PWM 占空比分辨率 
0.5% 
短路保护 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
transmitter 
receiver 
Vcc_sw 
Vcc 
slave_supply 
Vs 
HS_driver 
D* 
f_pwm < 1 kHz 
Itx 
   
+  
Utx 
- 
   
 
Input_pin+ 
Irx 
   R1  
Vth 
I_inj 
5V LDO 
Voltage 
Regulator 
    + 
- 
+ 
Urx 
- 
Power_gnd 
 
Rpd 
C1 
uC 
signal_rtn 
1nF@Input_pin 
Type B, Vcc:_sw supplied. Active high PWM signal. 
signal_gnd     D* = Reverse polarity protection mechanization Vmin dependent. 
One common R_no_float load for each separate Vcc_sw net 
R_no_float 


### 第 49 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
49/74 
 
 
5V LDO 
Voltage 
Regulator 
Frequency transmitter      Frequency receiver 
Vs 
Vs_sw 
Vcc 
Vs_prot 
Rp_source 
D* 
Rpu 
Rpd 
f < 15 kHz 
Rp 
5V LDO 
Voltage 
Regulator 
frequency out 
   R1  
  
 
Vth 
I_inj 
Itx 
+ 
Utx 
- 
Irx 
+ 
Urx 
- 
+ 
- 
Power_gnd 
 
R2 
1nF@Input_pin 
C1 
uC 
Signal_gnd 
Type A, General frequency type voltage interface. Vs supplied 
Gnd 
Vs 
Rp_source 
Vcc_sw 
Vcc 
D* 
slave_supply 
Rpu 
Rpd 
f < 15 kHz       
 
Rp 
Itx 
Irx 
  R1  
  
 
Vth 
I_inj 
+ 
- 
Power_gnd 
 
frequency out 
 
Slave unit 
+ 
Utx 
- 
+ 
C1 
Urx 
- 
signal_rtn 
1nF@Input_pin 
uC 
Signal_gnd 
Type B, slave unit, frequency type voltage interface with Vcc <= 5V supply 
D* = Reverse polarity protection mechanization Vmin dependent 
 
A6 频率类型控制信号.  
A6.1 频率类型电压接口 
 
 
 
 
 
 
 
Vs 


### 第 50 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
50/74 
2005 年 10 月 
 
 
 
电气特性 
 
参数 
符号 
条件 
min 
typ 
max 
Unit 
注释 
供电电压 
Vs 
最大额定 
-13.5  
+26.5 
V 
无损坏 
供电电压 
Vs 
具有有效逻辑状态的工作范围 
Vmin 
14 
Vmax 
V 
 
输入电压范围 
Urx 
最大额定 
-1.0 
 
+26.5 
V 
 
输入电压 
Urx 
工作范围 A 型，Vs 供应 
-1.0 
 
Vmax+1 
V 
接地偏移
允许+ - 1V 
输入逻辑低状态 
Urxl 
电压范围 A 型，Vs 供应 
-1.0 
 
+2.0 
V 
 
输入逻辑高状态 
Urxh 
电压范围 A 型，Vs 供应 
+5.5 
 
Vmax+1 
V 
 
输出电压 
Utx 
工作范围 A 型，Vs 供应 
0 
 
Vmax 
 
f < 15 kHz 
输出电压低 
Utxl 
电压范围 @ Io = 100mA, A 型 
0 
0.3 
1.0 
V 
逻辑 0 
输出电压高 
输出电压 dVdt 
Utxh 
tr, tf 
电压范围 @ Io = -100mA, A 型 
0.1 Utxh to 0.9 Utxh, A 型 
Vs-1 
0.1 
 
Vs 
1.0 
V 
V/µs 
逻辑 1 
输入电压 
Urx 
工作范围 B 型，Vcc 供应 
0 
 
Vcc 
V 
接地偏移
允许 0V 
输入逻辑低状态 
Urxl 
电压范围,  B 型, Vcc 供应 
0 
 
0.3Vcc 
V 
信号_rtn ref. 
输入逻辑高状态 
Urxh 
电压范围, B 型, Vcc 供应 
0.7Vcc  
Vcc 
V 
信号_rtn ref. 
输出电压 
Utx 
工作范围 B 型，Vcc 供应 
0 
 
Vcc 
 
f < 15 kHz 
输出电压低 
Utxl 
电压范围 @ Io = 25mA, B 型 
0 
0.3 
1.0 
V 
逻辑 0 
输出电压高 
Utxh 
电压范围 @ Io = -25mA, B 型 
Vcc-1  
Vcc 
V 
逻辑 1 
输出电压 dVdt 
tr, tf 
0.1 Utxh to 0.9 Utxh, A 型 
0.1 
 
1.0 
V/µs  
 
R1 
 
 
33 
 
k
 
 
R2 
 
 
100  
k
Vth=30/70% 
 
R2 
 
 
220  
k
Vth=35/65% 
 
Rp 
 
680 
1.2 
3.3 
k
 
 
C1 
 
 
180  
pF 
 
Vmin，Vnom 和 Vmax 由 GMW3172 代码串的电子代码字母定义。 Vcc 通常为 5.0V


### 第 51 页


### 第 52 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
50/74 
 2005 年 10 月 
 
 
 
A6.1.1 有源低频接口，Vs 供应 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
IFL_Vs_x 
名称(s) 
IFL_Vs_x 
类型 
数字频率信号接收器，低电平有效 
Rpu 上拉电压源 
Vs_sw 
唤醒类型 
N/A 
Irxl_min活动状态下所需的最小电流 
-4 mA* 
Irxl_max活动状态允许的最大电流 
-20 mA* 
接地偏移电压 
0 to ±1.0 V 
低通滤波器 - 时间常数 
5 µs 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
在 Vnom 至 Vmax 的整个工作电压范围内，Vs 给出的最小和最大有效状态电流 
OFL_Vs_x 
名称(s) 
OFL_Vs_x 
类型 
数字频率信号发射器，低电平有效 
输出电源 
Gnd 
Utxl_max -处于活动状态的最大饱和电压 
1.0 V @ 100mA Itx 
Itxl_min –最小吸收电流处于活动状态 
100mA @ Vmin to Vmax 
Ileak_max -最大漏电流处于非激活状态 
10 A 
输出频率范围: fmin to fmax 
f = x to y Hz (0< f < 15 kHz) 
频率信号周期时间抖动 
1µs < t 振动< 0.005/fmax 
正常输出信号固定占空比 
0.33 to 0.66 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
transmitter 
receiver 
Vs_sw 
Vs 
D* 
R_no_float 
Vcc 
f < 15kHz 
Rpu 
5V LDO 
Voltage 
Regulator 
LS_driver 
   R1  
Vth 
I_inj 
Itx 
+  
Urx 
- 
Irx 
    + 
- 
Power_gnd 
+ 
Utx 
- 
R2 
C1 
uC 
1nF@Input_pin 
signal_gnd 
Gnd 
Vs supplied, Active low frequency type voltage interface. 
D* = Reverse polarity protection mechanization Vmin dependent 
One common R_no_float load for each separate Vs_sw net 
Vs_prot 


### 第 53 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
51/74 
 
 
 
A6.1.2 有源低频接口，Vcc 提供 
 
 
IFL_Vcc_x 
名称(s) 
IFL_Vcc_x 
类型 
数字频率信号接收器，低电平有效 
Rpu 上拉电压源 
Vcc_sw (正常 5V) 
唤醒类型 
N/A 
Irxl_min 活动状态下所需的最小电流 
-4 mA* 
Irxl_max 活动状态允许的最大电流 
-20 mA* 
接地偏移电压 
0 V 
低通滤波器 - 时间常数 
2.2 µs 
短路保护(包括 Vcc_sw 输出): 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
•在 Vmin 到 Vmax 的整个工作电压范围内，Vs 给出的最小和最大有效状态电流 OFL_Vcc_x 
名称(s) 
OFL_Vcc_x 
类型 
数字频率信号发射器，低电平有效 
输出电源 
来自接收器信号接地的 Signal_rtn 线 
Utxl_max -处于活动状态的最大饱和电压 
1.0 V @ 25 mA Itx 
Itxl_min –最小吸收电流处于活动状态 
25 mA @ Vcc=5V 
Ileak_max -最大漏电流处于非激活状态 
10 A 
输出频率范围: fmin to fmax 
f = x to y Hz (0< f < 15 kHz) 
频率信号周期时间抖动 
1µs < t 振动< 0.005/fmax 
正常输出信号固定占空比 
0.33 to 0.66 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
transmitter 
receiver 
Vcc_sw 
Vcc 
slave_supply 
Vs 
D* 
R_no_float 
f < 15 kHz    
 
Rpu 
LS_driver 
Itx 
Irx 
R1 
Vth 
I_inj 
5V LDO 
Voltage 
Regulator 
    + 
- 
+ 
Utx 
- 
+ 
Urx 
- 
signal_rtn 
Power_gnd 
 
C1 
uC 
1nF@Input_pin 
signal_gnd 
Vcc_sw supplied. Active low, frequency type voltage interface. 
D* = Reverse polarity protection mechanization Vmin dependent. 
One common R_no_float load for each separate Vcc_sw net 


### 第 54 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
52/74 
 2005 年 10 月 
 
 
 
A7 有源高频接口 
A7.1 有源高频接口，Vs 供应 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
IFH_Vs_x 
名称(s) 
IFH_Vs_x 
类型 
数字频率信号接收器，高电平有效 
Rpd 下拉电压源 
信号接地 
唤醒类型 
N/A 
Irxh_min 活动状态下所需的最小电流 
-4 mA* 
Irxh_max 活动状态允许的最大电流 
-20 mA* 
接地偏移电压 
0 to ±1.0 V 
低通滤波器 - 时间常数 
5 µs 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
•在 Vnom 至 Vmax 的整个工作电压范围内，Vs 给出的最小和最大有效状态电流 OFH_Vs_x 
名称(s) 
OFH_Vs_x 
类型 
数字频率信号发射器，高电平有效 
输出电源 
Vs_prot 
Utxh_min -最小输出电压处于活动状态 
Vs-1.0 V @ 100mA Itx 
Itxh_min –最小源电流处于活动状态 
-100mA @ Vmin to Vmax 
Ileak_max -最大漏电流处于非激活状态 
10 A 
输出频率范围：fmin 至 fmax 
f = x to y Hz (0< f < 15 kHz) 
频率信号周期时间抖动 
1µs < tjitter < 0.005/fmax 
正常输出信号固定占空比 
0.33 至 0.66 
短路保护: 
Gnd 和 Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
transmitter 
receiver 
Vs 
D* 
f < 15 kHz 
   R1  
Vth 
I_inj 
5V LDO 
Voltage 
Regulator 
Itx 
 +   
Utx 
- 
Irx 
    + 
- 
Power_gnd 
 
+ 
Urx 
- 
Rpd 
R2 
C1 
uC 
1nF@Input_pin 
signal_gnd 
Gnd 
Type A, Vs_prot supplied. Active high frequency type voltage interface. 
D* = Reverse polarity protection mechanization Vmin dependent 
Vs_prot 
HS_driver 
Vcc 


### 第 55 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
53/74 
 
 
transmitter 
receiver 
Vcc_sw 
Vcc 
slave_supply 
Vs 
HS_driver 
D* 
f < 15 kHz 
5V LDO 
Voltage 
Regulator 
Itx 
Irx 
R1 
Vth 
I_inj 
    + 
- 
+ 
Utx 
- 
+ 
Urx 
- 
signal_rtn 
Power_gnd 
 
Rpd 
C1 
uC 
1nF@Input_pin 
signal_gnd 
Vcc_sw supplied. Active high, frequency type voltage interface. 
D* = Reverse polarity protection mechanization Vmin dependent. 
One common R_no_float load for each separate Vcc_sw net 
 
A7.1.2 有源高频接口, Vcc 供应 
 
 
 
 
 
 
 
R_no_float 


### 第 56 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
54/74 
 2005 年 10 月 
 
 
 
IFH_Vcc_x 
 
名称(s) 
IFH_Vcc_x 
类型 
数字频率信号接收器，高电平有效 
Rpd 下拉电压源 
信号接地 
唤醒类型 
N/A 
Irxl_min 活动状态下所需的最小电流 
-4 mA* 
Irxl_max 活动状态允许的最大电流 
-20 mA* 
接地偏移电压 
0 V 
低通滤波器 - 时间常数 
2.2 µs 
短路保护(包括 Vcc_sw 产出): 
Gnd and Vbatt 
ESD 保护 - 处理 
将满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
将满足 GMW3097GS @ 25 kV 
•在 Vmin 到 Vmax 的整个工作电压范围内，Vs 给出的最小和最大有效状态电流。 Vcc 通常为 5.0V 
OFH_Vcc_x 
名称(s) 
OFH_Vcc_x 
类型 
数字频率信号发射器，高电平有效 
输出电源 
来自接收器的 Vcc_sw 线 
Utxh_min –最小输出电压处于活动状态 
1.0 V @ 25 mA Itx 
Itxh_min –最小源电流处于活动状态 
-25 mA @ Vcc=5V 
Ileak_max -最大漏电流处于非激活状态 
10 A 
输出频率范围：fmin 至 fmax 
f = x to y Hz (0< f < 15 kHz) 
频率信号周期时间抖动 
1µs < t 振动 < 0.005/fmax 
正常输出信号固定占空比 
0.33 to 0.66 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 


### 第 57 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
55/74 
 
 
 
 
A8 频率类型电流接口 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
电气特性 
 
参数 
符号 
条件 
min 
typ 
max 
Unit 
注释 
供电电压 
Vs 
最大额定 
-13.5  
+26.5 
V 
无损害 
供电电压 
Vs 
具有有效电流接口信号的
工作范围 
Vmin 
14 
Vmax 
V 
 
输入电压范围 
Urx, Utx 
最大额定 
0 
 
+26.5 
V 
 
输入电压 
Urx 
正常运行 
0 
Irxmax x 
Rsense 
2.0 
V 
允许接
地偏移
= 0V 
输入逻辑低状态 
Irxl 
 
5.4 
7.0 
8.6 
mA  
输入逻辑高状
态 
Irxh 
 
11.0 
14.0 
17.0 
mA  
输出电压 
Utx 
正常运行 
 
 
Vmax 
V 
 
输出电流低 
Itxl 
 
5.6 
7 
8.4 
V 
逻辑 0 
输出电流高输出电
流 t dI/dt 
Itxh 
 
11.2 
10 
14 
16.8 
100 
V 
mA/µs 
逻辑 1 
 
Rsense  
Rsense- 
1% 
115 
Rsense+1% 

 
Vmin，Vnom 和 Vmax 由 GMW3172 代码串的电子代码字母定义。
transmitter 
receiver 
SC-protection 
Vs_sw  Vs_prot 
Vs 
D* 
Itx 
  
 
      signal_supply 
+ 
Utx 
- 
Itx 
Irx 
signal_rtn 
+ 
G 
Urx 
Current Interface 
- 
Power_gnd 
 
One common R_no_float load for each separate Vs_sw net 
D* = Reverse polarity protection mechanization Vmin dependent 
signal_gnd 
Sensor or 
Slave unit 
Rsense 
R_no_float 


### 第 58 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
56/74 
 2005 年 10 月 
 
 
 
IFC_Vs_x 
名称(s) 
IFC_Vs_x 
类型 
数字频率信号电流接收器，高电平有效 
Rsense 下拉电压源 
信号接地 
唤醒类型 
N/A 
接地偏移电压 
0 V 
低通滤波器 - 时间常数 
5 µs 
短路保护: 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
•在 Vmin 到 Vmax 的整个工作电压范围内，Vs 给出的最小和最大有效状态电流 
OFC_Vs_x 
名称(s) 
OFC_Vs_x 
类型 
数字频率信号电流变送器，高电平有效 
输出电源 
Vs_sw 
输出频率范围：fmin 至 fmax 
f = x to y Hz (0< f < 15 kHz) 
频率信号周期时间抖动 
1µs < t 振动< 0.005/fmax 
正常输出信号固定占空比 
0.33 to 0.66 
短路保护 (包括 Vs_sw 输出): 
Gnd and Vbatt 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 


### 第 59 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
57/74 
 
 
 
A9 模拟输入。 
A9.1 模拟输入比率。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
电气特性 
 
参数 
符号 
条件 
min 
typ 
max 
Unit 
注释 
供电电压 
Vs 
最大额定 
-13.5 
 
+26.5 
V 
无损坏 
供电电压 
Vs 
操作范围准确无误 
Vmin 
14 
Vmax 
V 
 
A/D 输入电压 
Ui 
运行范围 
Vref- 
0-5 
Vref+ 
V 
 
A/D 输入电压范围 
Ui 
最大额定 
-1.0 
 
+Vmax 
V 
 
输入时间常数 
Tad 
R1xC1 
 
1 
 
ms 
 
R1 输入保护 
R1* 
 
 
10 
 
k
 
过滤 C1 
C1** 
 
 
100 
 
nF 
 
上拉电阻 
Rpu 
取决于应用 
200 
 
开启 

 
Vmin，Vnom 和 Vmax 由 GMW3172 代码串的电子代码字母定义。 
*输入保护应通过 GMW3097 验证要求，并在连接器引脚上提供 0805 尺寸的陶瓷电容焊盘 
* R1 的最小值取决于 AD 输入引脚允许的注入电流，最大值取决于 A / D 精度。 
** C1 的最小值取决于允许的 AD 输入源电容要求。
Vcc_sw 
Vcc 
sensor_supply* 
Vs 
transmitter     receiver 
D* 
Rpu* 
Io 
Ii 
 R1**  
  
 
I_inj 
+ 
Uo 
- 
+ 
Ui 
- 
signal_rtn 
Vref+ 
In 
Vref- 
A 
D 
5V LDO 
Voltage 
Regulator 
51kOhm 
C1 
uC 
Power_gnd 
10nF@Input_pin 
signal_gnd 
Gnd 
A/D Converter Input 
Resolution 8 or 10 Bit of A/D_Vref 
*Rpu and need for sensor_supply application  dependent 
**A/D input protection and accuracy dependent on R1 
D* = Reverse polarity protection mechanization Vmin dependent 
One common R_no_float load for each separate Vcc_sw  net 
Sensor 
R_no_float 


### 第 60 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
58/74 
 2005 年 10 月 
 
 
 
电气 I/O 特性 
 
模拟 I / O 名称 
说明 
Rpu 
[Ohm] 
传感器供应
[V] 
Tol. 
[%] 
A/D 解析
度[Bits] 
mV 
Ui 范
围 [V] 
注释 
具体 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 61 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
59/74 
 
 
 
A10 带诊断的模拟开关输入。 通过选择的元件值，可以容忍 0.8V 的接地偏移，并且开关可以接地到车身。 
状态 
Ui_低 
Ui_高 
单元 
注释 
短接地 
-1 
0.6 
V 
Ui 范围 
闭合开关 
0.6 
2.5 
V 
Ui 范围 
打开开关 
2.5 
4.4 
V 
Ui 范围 
短路供电电压 
4.4 
Vs 
V 
Ui 范围 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
电气特性 
 
参数 
符号 
条件 
min 
typ 
max 
Unit 
注释 
供电电压 
Vs 
最大额定 
-13.5 
 
+26.5 
V 
无损坏 
供电电压 
Vs 
操作范围准确无误 
Vmin 
14 
Vmax 
V 
 
A / D 输入电压 
Ui 
运行范围 
Vref- 
0-5 
Vref+ 
V 
 
A/D 输入电压范围 
Ui 
最大额定 
-1.0 
 
+Vmax 
V 
 
输入时间常数 
Tad 
R1xC1 
 
1 
 
ms 
 
R1 输入保护 
R1* 
 
 
10 
 
k
 
过滤 C1 
C1** 
 
 
100 
 
nF 
 
上拉电阻 
Rpu 
 
 
680 
 

1% 
Vmin，Vnom 和 Vmax 由 GMW3172 代码串的电子代码字母定义。 
*输入保护应通过 GMW3097 验证要求，并在连接器引脚上提供 0805 尺寸的陶瓷电容焊盘 
* R1 的最小值取决于 AD 输入引脚允许的注入电流，最大值取决于 A / D 精度。 
** C1 的最小值取决于允许的 AD 输入源电容要求。
switch  receiver 
Vcc_sw 
Vcc 
Vs 
D* 
OR 
Ii 
  
 
   
 
R1** 
I_inj 
Vref+ 
In 
Vref- 
A 
D 
5V LDO 
Voltage 
Regulator 
+ 
Uo 
- 
+ 
Uo 
- 
+ 
Ui 
- 
signal_rtn 
51k 
C1 
uC 
Power_gnd 
10nF@Input_pin 
signal_gnd 
Gnd 
Alternative switch 
grounding 
A/D Converter Input 
Resolution 8 or 10 Bit of A/D_Vref 
**A/D input protection dependent on R1 
D* = Reverse polarity protection mechanization Vmin dependent 
One common R_no_float load for each separate Vcc_sw net 
1500 +/-1% 
390 +/-1% 
1500 +/-1% 
390 +/-1% 
R_no_float 
680 +/-1% 


### 第 62 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
60/74 
2005 年 10 月 
 
 
 
A10.1 模拟输入 5V 参考电阻梯形图 
A10.1.1 类型 A. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
功能数量，
（最大闭合
开关阻抗） 
R1 

R2 

R3 

R4 

R5 

R6 

R7 

R8 

R9 

R10 

R11 

Rpu 

Ropen 

3 (20 ) 
604 
255 
113 
 
 
 
 
 
 
 
 
499 
4640 
4 (20 ) 
806 
340 
178 
84.5 
 
 
 
 
 
 
 
499 
4640 
5 (20 ) 
845 
374 
205 
127 
57.6 
 
 
 
 
 
 
499 
4640 
6 (20 ) 
768 
374 
215 
140 
97.6 
46.4 
 
 
 
 
 
499 
4640 
7 (20 ) 
1070 
487 
280 
178 
121 
88.7 
46.4 
 
 
 
 
499 
4640 
8 (20 ) 
1070 
523 
309 
196 
133 
100 
75 
46.4 
 
 
 
499 
4640 
9 (5 ) 
1150 
549 
309 
200 
137 
97.6 
73.2 
53.6 
46.4 
 
 
499 
4640 
10 (5 ) 
1370 
634 
348 
226 
150 
110 
84.5 
60.4 
47.5 
46.4 
 
499 
4640 
11 (5 ) 
1650 
715 
402 
249 
174 
121 
88.7 
68.1 
53.6 
41.2 
33.2 
499 
6980 
注意：所有电阻均为 1％。 
注意：可以删除重新打开。 在这种情况下，断开的开关组件和没有按下开关之间没有区别。
Ropen 
SW1 
R1 
SW2 
R2 
SW3 
R3 
receiver 
Vcc_sw 
Vcc 
SW4 
Vs 
R4 
SW5 
D* 
Rpu 
R5 
Ii 
Rin 
Vref+ 
In 
Vref- 
A 
SWn 
D 
5V LDO 
Voltage 
Regulator 
Rn 
+ 
Ui 
- 
signal_rtn 
C1 
uC 
Power_gnd 
 
keypad 
10nF@Input_pin 
signal_gnd 
A/D Converter Input 
Resolution 8 or 10 Bit of A/D_Vref 
D* = Reverse polarity protection mechanization Vmin dependent 
One common R_no_float load for each separate Vcc_sw  net 
... 
R_no_float 


### 第 63 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
61/74 
 
 
 
 
 
功能数量，
（最大闭合
开关阻抗） 
功能数
量，（材
料） 
Short 
to Batt 
> 
x 
Volts 
Open 
> x < 
Short 
Volts 
SW1 
> x < 
Open 
Volts 
SW2 
> x < 
SW1 
Volts 
SW3 
> x < 
SW2 
Volts 
SW4 
> x < 
SW3 
Volts 
SW5 
> x < 
SW4 
Volts 
SW6 
> x < 
SW5 
Volts 
SW7 
> x < 
SW6 
Volts 
SW8 
> x < 
SW7 
Volts 
SW9 
> x < 
SW8 
Volts 
SW10 
> x < 
SW9 
Volts 
SW11 
> x < 
SW10 
Volts 
3 (20 ) 
3 
4.75 
3.95 
2.79 
1.53 
0.4 
 
 
 
 
 
 
 
 
4 (20 ) 
4 
4.77 
4.13 
3.20 
2.31 
1.25 
0.33  
 
 
 
 
 
 
5 (20 ) 
5 
4.77 
4.20 
3.40 
2.60 
1.80 
0.96 
0.24  
 
 
 
 
 
6 (20 ) 
6 
4.77 
4.20 
3.48 
2.83 
2.16 
1.49 
0.83 
0.20  
 
 
 
 
7 (20 ) 
7 (gold) 
4.79 
4.4 
3.85 
3.3 
2.65 
2.05 
1.45 
0.80 
0.20  
 
 
 
8 (20 ) 
8 (gold) 
4.79 
4.38 
3.88 
3.40 
2.88 
2.36 
1.82 
1.30 
0.76 
0.20  
 
 
9 (5 ) 
9 (gold) 
4.79 
4.40 
3.92 
3.45 
2.96 
2.47 
1.94 
1.51 
1.06 
0.64 
0.20  
 
10 (5 ) 
10 (gold) 
4.81 
4.45 
4.05 
3.61 
3.14 
2.71 
2.23 
1.89 
1.45 
1.05 
0.65 
0.20 
 
11 (5 ) 
11 (gold) 
4.9 
4.54 
4.14 
3.73 
3.31 
2.88 
2.45 
2.01 
1.61 
1.21 
0.84 
0.48 
0.15 
 
3 开关示例 5.1≤ADC 电源≤4.9： 
•短至 Batt

Ui≥4.75 伏 

4.75>Ui≥3.95 伏 
•SW1

3.95>Ui≥2.79 电压 
•SW2

2.79>Ui≥1.53 伏 
•SW3

1.53>Ui≥0.4 伏 
•短接地

Ui<0.40 伏


### 第 64 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
62/74 
2005 年 10 月 
 
 
 
A10.1.2 B 类型 
 
 
 
 
 
 
 
 
 
 
 
 
 
电阻代码表 
 
功能数量，
（最大闭合
开关阻抗） 
R2 

R3 

R4 

R5 

R6 

R7 

R8 

R9 

R10 

R11 

R12 

Rpu 

Rpd 

Rcomp 

3 (20 ) 
105 
226 
845 
 
 
 
 
 
 
 
 
237 
10k 
10 
4 (20 ) 
82.5 
154 
365 
2000  
 
 
 
 
 
 
237 
10k 
10 
5 (20 ) 
64.9 
105 
187 
422 
2000  
 
 
 
 
 
237 
10k 
10 
6 (20 ) 
52.3 
73.2 
113 
196 
402 
1240  
 
 
 
 
237 
10k 
10 
7 (20 ) 
45.3 
61.9 
86.6 
130 
221 
453 
1370  
 
 
 
237 
10k 
10 
8 (20 ) 
41.2 
53.6 
73.2 
105 
158 
261 
576 
1870  
 
 
237 
10k 
10 
9 (5 ) 
24.3 
32.4 
42.2 
57.6 
82.5 
121 
205 
374 
953 
 
 
237 
10k 
10 
10 (5 ) 
23.2 
30.1 
39.2 
51.1 
71.5 
105 
162 
274 
634 
2150  
237 
10k 
10 
11 (5 ) 
20 
26.1 
33.2 
44.2 
57.6 
82.5 
115 
182 
309 
715 
2740 
237 
10k 
10 
 
注意：除 Rpd 和 Rcomp 为 5％外，所有电阻均为 1％。
Vcc_sw 
Vcc 
Vs 
receiver 
D* 
Rpu 
Rn 
... 
R3 
   R2  
Ii 
  
 
  
  
Rcomp 
Rin 
Vref+ 
In 
Vref- 
A 
D 
5V LDO 
Voltage 
Regulator 
Sn 
S3 
S2 
S1 
+ 
Ui 
- 
Rpd 
C1 
uC 
Power_gnd  
   
 
signal_rtn 
10nF@Input_pin 
keypad 
signal_gnd 
A/D Converter Input 
Resolution 8 or 10 Bit of A/D_Vref 
D* = Reverse polarity protection mechanization Vmin dependent 
One common R_no_float load for each separate Vcc_sw net 
Rn+1 
R_no_float 


### 第 65 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
63/74 
 
 
 
 
 
功能数量，（最
大 闭 合 开 关 阻
抗） 
SW1 
< x 
Volts 
SW2  < 
x 
> 
SW1 
Volts 
SW3  < 
x 
> 
SW2 
Volts 
SW4  < 
x 
> 
SW3 
Volts 
SW5  < 
x 
> 
SW4 
Volts 
SW6  < 
x 
> 
SW5 
Volts 
SW7  < 
x 
> 
SW6 
Volts 
SW8  < 
x 
> 
SW7 
Volts 
SW9  < 
x 
> 
SW8 
Volts 
SW10  < 
x  > SW9 
Volts 
SW11  < 
x 
> 
SW10 
Volts 
3 (20 ) 
1.05 
2.31 
3.5 
 
 
 
 
 
 
 
 
4 (20 ) 
0.95 
2.02 
3.04 
3.99 
 
 
 
 
 
 
 
5 (20 ) 
0.85 
1.75 
2.59 
3.38 
4.11 
 
 
 
 
 
 
6 (20 ) 
0.87 
1.51 
2.21 
2.84 
3.5 
4.08 
 
 
 
 
 
7 (20 ) 
0.73 
1.4 
2.02 
2.61 
3.16 
3.69 
4.17 
 
 
 
 
8 (20 ) 
0.7 
1.32 
1.89 
2.44 
2.95 
3.42 
3.87 
4.3 
 
 
 
9 (5 ) 
0.44 
0.88 
1.33 
1.8 
2.28 
2.74 
3.21 
3.67 
4.1 
 
 
10 (5 ) 
0.39 
0.85 
1.28 
1.72 
2.16 
2.62 
3.07 
3.5 
3.93 
4.35 
 
11 (5 ) 
0.41 
0.78 
1.17 
1.58 
2.0 
2.42 
2.84 
3.24 
3.64 
4.03 
4.41 
注意：开路状态是任何大于 SWn 的电压。 
•3 个开关示例，用于 5.1≤ADC 电源≤4.9： 
•SW1

Ui≤1.05 伏 
•SW2

1.05<Ui≤2.31 电压 
•SW3

2.31<Ui≤3.5 伏 
•打开（开关断开）Ui> 3.5 伏


### 第 66 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
64/74 
2005 年 10 月 
 
 
 
 
A10.2 模拟输入绝对值（AD +输入> Vref） 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
电气特性 
 
参数 
符号 
条件 
min 
typ 
max 
Unit 
注释 
供电电压 
Vs 
运行范围准确无误 
Vmin 
14 
Vmax 
V 
 
供电电压 
Vs_max 
最大额定 
-13.5 
 
+26.5 
V 
无损坏 
输入电压 
Vad+ 
运行范围 
Vref- 
0-16 
+26.5 
V 
 
输入电压范围 
Vad+ 
最大额定 
-1.0V 
 
+26.5 
V 
 
输入时间常数 
Tad 
R1xC1 
 
1 
 
ms 
 
R1 输入保护 
R1* 
 
 
10 
 
k
 
过滤 C1 
C1** 
 
 
100 
 
nF 
 
R2 分压器 
R2 
取决于 AD +输入电压范围  
2.2 
 
k
 
Vmin，Vnom 和 Vmax 由 GMW3172 代码串的电子代码字母定义。 
*输入保护应通过 GMW3097 验证要求，并在连接器引脚上提供 0805 尺寸的陶瓷电容焊盘 
* R1 的最小值取决于 AD 输入引脚允许的注入电流，最大值取决于 A / D 精度。 
** C1 的最小值取决于允许的 AD 输入源电容要求。 
 
电气 I/O 特性 
 
模拟 I / O 名称 
传感器
供应使
用 
Rpu 
[k] 
1% 
R1 
[] 
1% 
R2 
[] 
1% 
Ui  
线性
输入范围 
[V] 
解析度
[Bits] 
mV 
C1 
[nF] 
注释 
具体 
 
 
 
 
 
 
 
 
Vbatt 监控 
内部
Vs_sw 
0k (短) 
10.0k 
2.26k 
6-27.1 
[8] 
105 
100 
肖特基极性保
护 
Vbatt 监控 
内部
Vs_sw 
0k (短) 
10.0k 
4.42k 
6-16.3 
[8] 
63 
100 
肖特基极性保
护 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Vs_sw 
sensor_supply* 
Vs 
transmitter 
receiver 
Vcc 
D* 
Rpu* 
Io 
Ii 
+ 
Uo 
- 
+ 
Ui 
- 
signal_rtn 
 R1**  
   
10k 
I_inj 
Vref+ 
A 
In 
D 
5V LDO 
Voltage 
Regulator 
Vref- 
R2*/** 
C1 
uC 
Power_gnd 
10nF@Input_pin 
signal_gnd 
Gnd 
A/D Converter Input 
Resolution 8 or 10 Bit of A/D_Vref 
*Rpu, R2 values and need for sensor_supply application  dependent 
**A/D input protection and accuracy dependent on R1//R2 
D* = Reverse polarity protection mechanization Vmin dependent 
One common R_no_float load for each separate Vs_sw  net 
Sensor 
R_no_float 


### 第 67 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
65/74 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 68 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
66/74 
2005 年 10 月 
 
 
 
 
A11 低侧驱动器。 
 
 
电气特性 
 
参数 
符号 
条件 
min 
typ 
max 
Unit 
注释 
供电电压 
Vs 
运行范围 
Vmin 
14 
Vmax 
V 
 
供电电压 
Vs_max 
最大额定 
-13.5 
 
+26.5 
V 
无损坏 
Uo(Vsat) 
 
Tmin to Tmax, Vmin to Vmax 
0 
 
1.0 
V 
 
  
R_load 
Tmin to Tmax 
 
 
 

 
感应负载 
I_load 
Tmin to Tmax 
 
 
 
H 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Vmin，Vnom 和 Vmax 由 GMW3172 代码串的电子代码字母定义。 
*输入保护应通过 GMW3097 验证要求，并在连接器引脚上提供 0805 尺寸的陶瓷电容焊盘
V_load 
R_load 
R_load 
Vs 
Vs_prot 
Vs_prot 
L_load 
L_load 
Io 
Io 
+ 
+ 
A 
Uo 
Uo 
B 
10n 
10n 
- 
- 
on/off control 
pwm control 
Power_gnd 
Type A: Fast switch off loads 
Type B: PWM controlled loads 


### 第 69 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
67/74 
 
 
 
 
A11.1 低侧驱动离散输出。 
 
 
ODLS_x 
名称(s) 
ODLS_x 
类型 
低侧驱动离散输出 
输出电源 
电源接地 
Uol_max 
1.0 V 
Iol_min -活动状态下所需的最小电流 
x A 
Iol_max -在活动状态下允许的最大电流 
y A 
f_act -最大驱动频率 
x Hz 
t_ on_max - 每个周期重复信号的有效状态的最大导通时间
（如果不是连续的） 
x ms 
Ileak_max -在非活动状态下允许的最大泄漏电流 
10 A 
Isurge -在激活状态下允许的最大励磁电流 
10 * Iol_max 
R_负载_min @ -40 C 
x 
L_负载_max @ -40 C (如果机械被激活。执行器) 
y mH 
雪崩能量处理：
E=1/2*(Iol_max * L_load_max) 
mJ 
接地偏移电压 
1.0 V 
输出受短路保护： 
Gnd and Vbatt 
过电流关闭 
允许 
ESD 保护 - 处理 
需满足 GMW3097 @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097 @ 25 kV 
Vs 
D* 
R_load 
L_load 
Io 
* 
* 
* 
* 
+ 
A 
Uo 
B 
10n 
- 
on/off control 
Power_gnd 
Type A: Repetitive fast switch off loads 
* = Complete transient protection will depend on GMW3097 results 
D* = Reverse polarity protection mechanization Vmin dependent 


### 第 70 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
68/74 
2005 年 10 月 
 
 
 
 
A11.2 低侧驱动 PWM 输出。 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
OPLS_x 
名称(s) 
OPLS_x 
类型 
低侧驱动 PWM 输出 
输出电源 
电源接地 
Uol_max -在活动状态下允许的最大电压 
1.0 V @ Iol_max 
Iol_min -活动状态下所需的最小电流 
x A 
Iol_max -活动状态下所需的最大电流 
y A 
I 泄漏_max -在非活动状态下允许的最大泄漏电流 
10 A 
I 浪涌 -在激活状态下允许的最大浪涌电流 
10 * Iol_max 
R_负载_min @ -40 C 
x 
L_负载_max @ -40 C (如果机械被激活。执行器) 
y mH 
雪崩能量处理：
E=1/2*(Iol_max * L_load_max) 
mJ 
输出频率 
x Hz +/- 1％（通过线束<400Hz 负载，<25kHz 驱
动器与负载集成） 
占空比 
0-100% 
输出受到短路保护: 
Gnd and Vbatt 
过电流关闭 
允许 
ESD 保护 - 处理 
需满足 GMW3097 @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097 @ 25 kV 
Vs 
D* 
R_load 
L_load 
Io 
* 
* 
* 
* 
+ 
 
Uo 
10n 
- 
pwm control 
Power_gnd 
* = Complete transient protection will depend on GMW3097  results 
D* = Reverse polarity protection mechanization Vmin  dependent 
PWM controlled load 
Free Wheel Diode 


### 第 71 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
69/74 
 
 
 
 
A12 高端驱动程序。 
 
 
 
电气特性 
 
参数 
符号 
条件 
min 
typ 
max 
Unit 
注释 
供电电压 
Vs 
工作范围 
6.0 
14 
Vmax 
V 
 
供电电压 
Vs_max 
最大功率 
-13.5 
 
+26.5 
V 
无损坏 
Uoh_min 
 
Tmin to Tmax, 
Vmin to Vmax 
Vs - 1 
 
Vs 
V 
 
电阻负载 
R_负载 
Tmin to Tmax 
 
 
 

 
感应负载 
I_负载 
Tmin to Tmax 
 
 
 
H 
 
负载类型 A 或 B. 
 
 
 
 
 
 
 
驱动频率 
 
 
 
128 
400 
Hz 
负载通
过线束
连接 
驱动频率 
 
 
 
 
25 
kHz 
驱动程序
集成加载 
Vmin，Vnom 和 Vmax 由 GMW3172 代码串的电子代码字母定义。 
*输入保护应通过 GMW3097 验证要求，并在连接器引脚上提供 0805 尺寸的陶瓷电容焊盘
Vs 
+ 
+ 
A 
Usat 
Usat 
B 
- 
- 
on/off control 
Io 
pwm control 
Io 
+ 
+ 
Typ A: Fast switch off loads 
R_load 
Typ B: PWM controlled loads 
D1* 
R_load 
10n 
Uo 
10n 
Uo 
L_load 
L_load 
    Power_gnd 
- 
* For high current loads 
D1 is normally replaced by a 
syncronous rectifier 
- 


### 第 72 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
70/74 
2005 年 10 月 
 
 
 
 
A12.1 高侧驱动离散输出。 
 
 
ODHS_x 
名称(s) 
ODHS_x 
Type 
高侧驱动离散输出 
输出电源 
Vs: Vbatt or Vign 
Uoh_min 
Vs – 1 V 
Ioh_min -活动状态下所需的最小电流 
-x A 
Ioh_max -活动状态下允许的最大电流 
-y A 
f_act -最大驱动频率 
x Hz 
t_ on_max - 每个周期重复信号的活动状态下的最大开
启时间（如果不是连续的） 
x ms 
Ileak_max -非活动状态下允许的最大漏电流 
10 A @ 12.8 V 
Isurge -在活动状态下允许的最大浪涌电流 
10 * Ioh_min 
R_负载_min @ -40C 
x 
L_负载_max @ -40C (如果机械被激活。执行器) 
y mH 
雪崩能量处理： 
E=1/2*(Ioh_max * L_load_max) 
mJ 
接地偏移电压 
1.0 V 
输出受短路保护： 
Gnd and Vbatt 
过流关断 
允许 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
Vs 
D* 
+ 
A 
Usat 
B 
- 
* 
* 
* 
* 
   
on/off control 
Io 
+ 
Typ A: Repetitive fast switch off loads 
R_load 
10n 
Uo 
L_load 
Power_gnd 
- 
* = Complete transient protection will depend on GMW3097 results 
D* = Reverse polarity protection mechanization Uoh_min dependent 


### 第 73 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
71/74 
 
 
 
 
A12.2 高侧驱动 PWM 输出。 
 
 
OPHS_x 
名称(s) 
OPHS_x 
类型 
高端驱动 PWM 输出 
输出电源 
Vs: Vbatt or Vign 
Uoh_min -在活动状态下允许的最大电压 
Vs - 1.0 V @ Iol_max 
Ioh_min -活动状态下所需的最小电流 
-x A 
Ioh_max -在活动状态下允许的最大电流 
-y A 
Ileak_max -在非活动状态下允许的最大泄漏电流 
10 A 
Isurge -在激活状态下允许的最大浪涌电流 
10 * Ioh_max 
R_负载_min @ -40C 
x 
L_负载_max @ -40C (如果机械被激活。执行器) 
y mH 
雪崩能量处理：
E=1/2*(Ioh_max * L_load_max) 
mJ 
输出频率 
x Hz +/- 1％（通过线束<400Hz 负载，<25kHz 驱
动器与负载集成） 
占空比 
0-100%* 
输出受短路保护： 
Gnd and Vbatt 
过流关断 
允许 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 
* 0 至 100％的输出占空比不包括使用带有自举栅极驱动器的 N 沟道高侧晶体管。 要使用这种类型的高
端 N 沟道晶体管和栅极驱动，应用占空比限制为 5％至 95％。 
 
A13 H-Bridge Drivers. 
 
Vs 
D* 
+ 
 
Usat 
 
- 
* 
* 
* 
* 
pwm control 
Io 
+ 
D1* 
R_load 
* For high current loads 
D1 is normally replaced by a 
syncronous rectifier 
10n 
Uo 
L_load 
Power_gnd 
- 
* = Complete transient protection will depend on GMW3097 results 
D* = Reverse  polarity  protection mechanization Uoh_min dependent 
PWM controlled load 


### 第 74 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
72/74 
2005 年 10 月 
 
 
 
 
电气特性 
 
参数 
符号 
条件 
min 
typ 
max 
Unit 
注释 
供电电压 
Vs_max 
最大额定 
-13.5 
 
+26.5 
V 
无损坏 
供电电压 
Vs 
运营范围 
Vmin 
Vnom 
Vmax 
V 
 
 
 
 
 
 
 
 
 
Uoh_min 
 
Tmin to Tmax, 
Vmin to Vmax 
Vs - 1 
 
Vs 
V 
@Ioh_max 
Uol_max 
 
Tmin to Tmax, 
Vmin to Vmax 
0 
 
1.0 
V 
@Iol_max 
 
 
 
 
 
 
 
 
驱动频率 
f_act 
 
0 
128 
400 
Hz 
负载通
过线束
连接 
驱动频率 
f_act 
 
0 
 
25 
kHz 
驱动程序
集成加载 
Vmin，Vnom 和 Vmax 由 GMW3172 代码串的电子代码字母定义。 
*输入保护应通过 GMW3097 验证要求，并在连接器引脚上提供 0805 尺寸的陶瓷电容焊盘
D* 
Vs 
Vs_prot 
D1 
C1 
U1_h 
U2_h 
* 
Io 
L_load 
R_load 
Io 
U1_l 
Co 
+ 
Uo 
- 
Load 
+ 
Uo 
- 
Co 
U2_l 
Power_gnd 
signal_gnd 
D* = Reverse polarity protection mechanization Vmin dependent 
D1/C1 Designed to prevent Vs_prot overvoltage in any drive condition on/off/brake 


### 第 75 页
通用汽车全球工程标准 
GMW14082 
©版权所有 2005 通用汽车公司保留所有权利 
2005 年 10 月 
73/74 
 
 
 
ODHB_x 
名称(s) 
ODHB_x 
类型 
H 桥离散输出驱动器 
输出电源 
(Vs: Vbatt or Vign) / (电源接地) 
Uoh_min -在高电平有效状态下所需的最小电压输出 
Vs - 1.0 V @ Ioh_max 
Uol_max -低电平有效状态下所需的最大电压输出 
1.0 V @ Iol_max 
Ioh_min -活动状态下所需的最小电流 
-x A 
Ioh_max -在活动状态下允许的最大电流 
-y A 
Iol_min -活动状态下所需的最小电流 
-x A 
Iol_max -在活动状态下允许的最大电流 
-y A 
Ileak_max -在非活动状态允许的最大泄漏电流，通过 H 桥的
Vs_prot 到信号接地 
10 A 
Isurge -在激活状态下允许的最大浪涌电流 
10 * Io_max , 持续时间 < 1s 
R_负载_min @ -40 C 
x 
L_负载_max @ -40 C (如果机械被激活。执行器) 
y mH 
雪崩能量处理：
E=1/2*(Ioh_max * L_load_max) 
mJ 
执行频率，最大 
x Hz 
启动时间，如果不连续则允许最大值 
y ms 
受短路保护的输出： 
Gnd and Vbatt 
过流关断 
允许 
ESD 保护 - 处理 
需满足 GMW3097GS @ 8 kV 
ESD 保护 - 供电 
需满足 GMW3097GS @ 25 kV 


### 第 76 页
GMW14082 
通用汽车全球工程标准 
©版权所有 2005 通用汽车公司保留所有权利 
74/74 
2005 年 10 月 
 
 
 
OPHB_x 
名称(s) 
OPHB_x 
类型 
H 桥 PWM 输出驱动器 
输出电源 
(Vs: Vbatt or Vign) / (电源接地) 
Uoh_min -高电压状态下需要的最小电压输出 
Vs - 1.0 V @ Ioh_max 
Uol_max -低电压状态下的最大电压输出 
1.0 V @ Iol_max 
Ioh_min -在活动状态下需要的最小电流 
-x A 
Ioh_max –活动状态下允许的最大电流 
-y A 
Iol_min -在活动状态下需要的最小电流 
-x A 
Iol_max –活动状态下允许的最大电流 
-y A 
I 泄漏_max -在非活动状态允许的最大泄漏电流，通过 H 桥的供
压保护到信号接地 
10 A 
I 浪涌 -激活状态下允许的最大浪涌电流 
10 * Io_max 
R_load_min @ -40C 
x 
L_load_max @ -40C (如果机械活化。驱动器) 
y mH 
雪崩能量处理：
E=1/2*(Ioh_max * L_load_max) 
mJ 
输出频率 
x Hz 1% (<400Hz 负载通过线束，<25kHz 驱
动器与负载集成) 
占空比 
0 - 100% 
短路保护输出： 
Gnd and Vbatt 
过电流关断 
允许的 
静电放电保护-处理 
需符合 GMW3097GS @ 8 kV 
静电放电保护 
需符合 GMW3097GS @ 25 kV 
 

