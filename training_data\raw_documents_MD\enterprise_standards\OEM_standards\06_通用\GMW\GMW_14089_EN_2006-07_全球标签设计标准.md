# GMW_14089_EN_2006-07_全球标签设计标准.pdf

## 文档信息
- 标题：GMN General Specification Template
- 作者：<PERSON>
- 页数：9

## 文档内容
### 第 1 页
 
 
 
 
 
 
 
 
 
 
WORLDWIDE 
ENGINEERING 
STANDARDS 
General Specification 
Interior 
GMW14089 
 
 
 
 
 
 
 
 
 
Global Label Design Criteria 
 
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
July 2006 
Originating Department: North American Engineering Standards 
Page 1 of 9
 
1 Introduction 
Note: Regional or country specific laws and 
regulations supersede this specification unless a 
specific exemption has been obtained. 
Note: In the event of conflict between the English 
and the domestic language, the English language 
shall take precedence. 
1.1 Scope. This standard covers all vehicle labels, 
and describes how to determine the correct design 
for the application. 
2 References 
Note: Only the latest approved standards are 
applicable unless otherwise specified. 
2.1 External Standards/Specifications. 
ISO 3864-2 
 
2.2 GM Standards/Specifications. 
GM501M 
GMW3059 
GMN8495 
GMW14573 
GMN11154 
GMW14574 
GMW3001 
GMW15010 
2.3 Additional References. 
The following reference can be accessed at 
www.gmsupplypower.com: 
GM1737 
Part 
Identification 
and 
Traceability 
Application Standard. 
3 Requirements 
3.1 
System/Subsystem/Component/Part 
Definition. 
3.1.1 Appearance. GM has label design standards 
that determine the appearance of labels in the 
absence of regulatory criteria. The following 
direction is strongly recommended for all labels 
affixed to GM vehicles. 
3.1.1.1 Colors. 
3.1.1.1.1 Standard Colors. 
• 
Black – Pantone® Black or equivalent  
• 
White – Pantone® White or equivalent  
• 
Red – Pantone® 186 or equivalent 
• 
Yellow – Pantone® 116 or equivalent  
• 
Blue – Pantone® 301 or equivalent  
• 
Silver – CIE L*a*b* Color Space target - 
L*=54.37, a*=-.50, b*=-2.12; Gloss Spec 60°: 
83 ± 6. Measured at D65 illumination, observer 
angle=10°. 
3.1.1.1.2 Non-Standard Colors. 
• 
Orange – Pantone® 151 or equivalent  
• 
Green – Pantone® 355 or equivalent  
3.1.1.2 Banner/Hazard Severity Panels. 
3.1.1.2.1 
Danger 
labels. 
The 
banner 
area 
background for a Danger label shall be red. The 
general warning symbol and Danger text contained 
in the banner area shall be white (see Figure 5). 
3.1.1.2.2 Warning labels. The banner area 
background for a Warning label shall be orange. 
The general warning symbol and Warning text 
contained in the banner area shall be black (see 
Figure 4). 
3.1.1.2.3 Caution labels. The banner area 
background for a Caution label shall be yellow. The 
general 
warning 
symbol 
and 
Caution 
text 
contained in the banner area shall be black (see 
Figure 3). 
3.1.1.2.4 
Notice 
labels. 
The 
banner 
area 
background for a Notice label shall be blue. The 
Notice text contained in the banner area shall be 
white (see Figure 2). 
3.1.1.2.5 Information Labels. Information labels 
do not have a banner area (see Figure 1). 
3.1.1.2.6 Combination Labels. Signal words, 
colors, symbols, and message text for more than 
one type of label can be combined on a single 
piece of material. The different messages should 
be separated by lines or white space to maintain 
clarity. The different messages should also be 
arranged by the following levels of importance. 
 
1) Danger 
 
2) Warning 
 
3) Caution 
 
4) Notice 
5) Information 


### 第 2 页
GMW14089 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
Page 2 of 9 
July 2006
 
3.1.1.3 Message Area Requirements. 
3.1.1.3.1 Computer Generated Labels. The 
message 
area 
background 
for 
a 
computer 
generated label shall be silver. The text and 
graphics contained in the message area shall be 
black (see Figure 6). 
A black, 1 pt line (or 0.35 mm wide rectangle) 
should separate the languages in the message 
area. This line (or rectangle) must terminate 
1.0 mm away from the label edge and the banner 
area(s) (see Figure 1).  
3.1.1.3.2 Pre-Printed Labels. 
3.1.1.3.2.1 Underhood labels. The message area 
background for an underhood label shall be black 
(Pantone® Black or equivalent). The text and 
graphics contained in the message area shall be 
white (see Figure 2). 
To reduce printing press registration concerns, 
white zones must separate all differing colors. A 
white, 
1.5-pt-point 
line 
(or 
0.525 mm 
wide 
rectangle) must separate the banner area(s) from 
the message area and bleed off the edge of the 
label. A white, 1-pt-point line (or 0.35 mm wide 
rectangle) should separate the languages within 
the message area. This line (or rectangle) must 
terminate 1.0 mm away from the label edge and 
the line (or rectangle) separating the banner 
area(s) from the message area. 
3.1.1.3.2.2 Non underhood labels. The message 
area background for a non-underhood label shall 
be white. The text and graphics contained in the 
message area shall be black (see Figure 3). 
A black, 0.5 pt line (or 0.175 mm wide rectangle) 
should separate the languages in the message 
area. This line (or rectangle) must terminate 
1.0 mm away from the label edge and the banner 
area(s) (see Figure 1).  
3.1.2 Content. 
******* Physical Content. 
*******.1 Label Type Content 
*******.1.1 DANGER Labels. Danger label content 
should: 
a. Describe the hazard. 
b. Provide instructions as to what action is 
required or prohibited. 
c. Describe the severity and level of risk of harm 
if the instructions are not followed. If the 
potential consequences cannot be described 
simply, they can be summarized by using the 
phrase, "to help avoid personal injury." 
*******.1.2 WARNING Labels. Warning label 
content should: 
a. Describe the hazard. 
b. Provide instructions as to what action is 
required or prohibited. 
c. Describe the severity and level of risk of harm 
if the instructions are not followed. If the 
potential consequences cannot be described 
simply, they can be summarized by using the 
phrase, "to help avoid personal injury." 
*******.1.3 
CAUTION 
Labels. 
Caution 
label 
content should: 
a. Describe the hazard. 
b. Provide instructions as to what action is 
required or prohibited. 
c. Describe the severity and level of risk of harm 
if the instructions are not followed. If the 
potential consequences cannot be described 
simply, they can be summarized by using the 
phrase, "to help avoid personal injury." 
*******.1.4 NOTICE Labels. Notice label content 
should: 
a. Describe the condition. 
b. Provide instructions as to what action is 
required or prohibited. 
c. Explain the possible consequences if the 
instructions are not followed. If the possible 
consequences are many or complicated, they 
may be summarized, for example, as "damage 
to the vehicle." 
*******.1.5 INFORMATION Labels. Information 
label content should: 
a. Provide useful information not concerning 
potential personal injury or property damage.  
b. Should not use signal words such as 
DANGER, WARNING, CAUTION or NOTICE  
c. Should be designed and installed so that they 
can meet GM Material Specifications and 
cannot be easily removed, unless the label, tag 
or card is designed to be removed by the first 
retail purchaser. 
*******.2 Label Languages. 
Refer to Table 1 for general label language 
guidelines by country. Each country may have 
specific language requirements. Consult Safety 
Regulations & Consumer Info and Legal Staff 
about specifics in the law or regulation related to 
language content. 
For the North American market, English is to be 
presented above or to the left of the other 
languages. Each language text should be treated 
as separate areas of the label with the entire 
message(s) cohesively grouped within its language 
area, rather than staggered between languages 
(see Figure 1). 


### 第 3 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14089
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
July 2006 
Page 3 of 9
*******.3 Text. Whenever space permits, text 
should be in grammatically correct, concise 
sentences. 
Text 
should 
follow 
standard 
typographic practices. 
*******.3.1 Banner Text. 
*******.3.1.1 Signal Words. Danger, Warning, 
Caution and Notice signal words and banner areas 
are included in the label design to attract attention 
to the message. Information labels do not have 
signal words or banners (see Figure 1). 
Signal words should be 14-point (or a type size 
larger than the message text), Helvetica Neue 
Condensed Bold, all upper case. 
Signal words (“Danger”, “Warning”, "Caution" or 
"Notice") and the ISO general warning symbol can 
be oriented horizontally or vertically to the viewing 
angle (See Figure 3). Horizontally oriented signal 
words should be left justified and aligned with the 
message area text. Signal words should be 
centered top-to-bottom within their banner area. 
Vertically oriented signal words should be centered 
in relation to the message area text. Signal words 
should be centered left-to-right within their banner 
area. 
*******.3.1.1.1 Danger. The word "Danger" is to be 
accompanied by the ISO general warning symbol 
 on Danger labels. 
*******.3.1.1.2 Warning. The word "Warning" is to 
be accompanied by the ISO general warning 
symbol 
 on Warning labels. 
*******.3.1.1.3 Caution. The word "Caution" is to 
be accompanied by the ISO general warning 
symbol 
 on Caution labels. 
*******.3.1.1.4 Notice. The word “Notice” is to be 
used on Notice labels. 
*******.3.1.1.5 Information. Generally, information 
labels do not have a signal word. 
*******.3.1.1.6 
Combination 
Labels. 
Signal 
words, colors, symbols, and message text for more 
than one type of label can be combined on a single 
piece of material. The different messages should 
be separated by lines or white space to maintain 
clarity. The different messages should also be 
arranged by the following levels of importance. 
 
1) Danger 
 
2) Warning 
 
3) Caution 
 
4) Notice 
 
5) Information 
*******.3.2 Message Text. Typical message text 
for underhood labels should be 10 point, Helvetica 
Neue Condensed. Typical message text for non-
underhood labels should be 10 point, Helvetica 
Neue Medium Condensed. 
Message areas should be oriented horizontally to 
the viewing angle. Message text should be left 
justified, upper and lower case. All uppercase 
letters may be used as headings only. 
A contrasting line should separate the languages in 
the message area. This line must terminate 1.0 
mm away from the label edge and the banner 
area(s) (see section 3.1.1.3.2 Pre-Printed Labels). 
*******.3.2.1 Emphasizing Words. Emphasizing, 
certain words over others (by use of bold type, 
larger type size, all uppercase letters, underlining, 
etc.) is to be avoided within the message area. 
Emphasizing tends to decrease readability and 
tempts the reader to 'skim' over the message. 
Therefore, emphasizing words is unacceptable 
within the message area of a label except as a 
header, subhead or as required by government 
regulation. Bold type, larger type size, all upper 
case letters or a combination of these methods 
may be used to distinguish headers and subheads 
from the rest of the text. However, variation of 
these methods must not exist within the header or 
subhead. 
*******.3.2.2 Weights and Measures. Metric units 
should be presented above or to the left of the U.S. 
standard unit equivalent. Fractions are to be 
expressed as decimals, employing periods (.) in 
the English message area. “Millimeter” and 
“Millimeters” are to be abbreviated as “mm”. 
"Pound" and "Pounds" are to be abbreviated as 
"lb". "Kilogram" and "Kilograms" are to be 
abbreviated as "Kg". 
*******.3.2.3 Abbreviations. Abbreviations should 
be avoided within text areas. This promotes 
understanding and eases translation concerns. 
Numbers should be spelled out ("eight" rather than 
"8") at the beginning of sentences or for numbers 
ten and below. Weights and units of measure are 
an exception. Abbreviations and numerals may be 
used in combination for units of measure (i.e., "5 
mph", "8 Kg"). 
*******.3.2.4 Hyphenation. Hyphenation should be 
avoided 
within 
text 
areas. 
This 
promotes 
understanding and eases translation concerns. 
*******.3.2.5 Branding Policy. Components that 
have been identified and classified for branding 
shall be marked: 
• 
“ACDelco” 
• 
“GM” 
 


### 第 4 页
GMW14089 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
Page 4 of 9 
July 2006
• 
Or contain no markings at all  
GM’s 
Branding 
Policy 
is 
documented 
in 
GMN11154. The label shall comply with the 
branding policy when applicable and does not 
adversely affect the part cost and/or timing (i.e. a 
common, off the shelf shock absorber with a POA 
supplier label that is supplied to multiple OEM’s). 
*******.4 Art. ISO Symbols, GM International 
Symbols, and Custom Symbol Art (graphics 
specific to the label) should be used wherever 
effective to eliminate or reduce text. This increases 
the global marketability of GM Products while 
reducing the need for translation. However, 
whether ISO or other symbols convey the 
message, without the use of text, should be 
evaluated in each instance. 
For labels with multiple languages, symbol and 
graphic 
placement 
should 
correlate 
to 
all 
languages equally. For specific ISO Symbols and 
GM International Symbols, contact the Design 
Center. 
*******.4.1 Danger, Warning, Caution Signal 
Word Symbol. Danger, Warning and Caution 
labels must include the ISO general warning 
symbol 
. The symbol should be approximately 
125% larger than the size of the signal word 
(4.5 mm when used with the 14-point standard 
signal size). 
*******.4.2 
Owner’s 
and 
Service 
Manual 
Symbols. References to the Owner’s and Service 
Manuals must be made only when corresponding 
information is contained in the referenced manual. 
Instructions to see the Owner’s or Service Manual 
should be accompanied by the corresponding ISO 
Symbol placed after the word "Manual". 
The following is the English standard text for 
referencing the Owner’s Manual: 
See Owner’s Manual 
 for more information. 
Elaboration of this text (such as "See Owner’s 
Manual 
 for removal of wheel trim") should be 
used only when it is essential to convey the 
intended message. 
Note: Substitution of the complete text reference 
with the appropriate ISO Symbol is preferred 
wherever possible. 
*******.4.3 Fuse Abbreviations. In an effort to 
drive consistency and commonality, the Electrical 
SMT has developed a list of Approved Electrical 
Abbreviations to be used on Fuse Schematic 
Labels. For specific Fuse abbreviations, contact 
the Global Electrical PPEC Department or the 
Fuse Label Subject Matter Expert. 
*******.4.4 Bar Codes. Bar Codes shall meet the 
Human Readable Interpretation requirements of 
GM1737 – Part Identification and Traceability 
Application Standard. 
3.1.3 Interfaces. 
3.1.3.1 Label Location. Labels should be located 
on or in close proximity to the components that 
they identify. Labels should not be placed directly 
on rotating controls. These labels should instead 
be placed on a fixed (i.e., non-rotating) surface 
adjacent to the control. 
Labels should not be obscured by components or 
positioned such that they will be obscured by the 
user’s hands, etc. during normal vehicle operation 
or operation of the labeled component. 
There should be adequate spacing between other 
labels and components in order to prevent 
cluttering and confusion. 
3.2 Product Characteristics. 
3.2.1 Physical Characteristics. 
3.2.1.1 Surface Finish. Labels shall be produced 
with a matte finish appearance. 
3.2.1.2 Dimensions and Capacity. 
3.2.1.2.1 Label Dimensions. The overall label 
area should meet all visibility and readability 
concerns while being as small as possible. This 
reduces the label's impact on the appearance of 
the vehicle while increasing its adaptability for 
other 
applications. 
Information 
should 
be 
composed to reduce 'empty space' as much 
possible without appearing cramped. Label corners 
are to be rounded with a 3.0 mm radius to improve 
label adhesion and discourage label tampering. 
3.2.2 Serviceability. See GMW15010. 
3.2.3 User System/Subsystem/Component/Part 
Interface. 
3.2.3.1 Label Application Guidelines. Label 
Application 
to 
Automotive 
Components 
(GMN8495) serves as a general guideline for label 
installation.  The standard describes the procedure 
for applying a label to a painted metal, glass, 
plastic, cloth, or other material surface. 
3.3 Design and Construction. 
3.3.1 Materials, Processes and Parts Selection 
Guidelines. 
3.3.1.1 Material Guidelines. 
3.3.1.1.1 Label Performance Specifications. 
GMW14573 defines the performance validation 
tests required for label by installation location or 
label environmental exposure. 
 


### 第 5 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14089
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
July 2006 
Page 5 of 9
 
3.3.1.2 Processes Guidelines. 
3.******* Determination of Label Need. The 
engineer must evaluate: 
• 
Is the information in the proposed label 
something that is obvious to a user or already 
known to most users?  
• 
Is 
there 
an 
existing 
label 
design 
that 
communicates the intended message? 
• 
Is there a suitable location for the label on the 
vehicle or part?  
• 
Is the intended message comprehensive yet 
concise enough to fit on the proposed label 
size?  
• 
Would the proposed label detract attention 
from higher priority messages?  
3.3.1.2.2 Label Design Approval. For labels 
designed for use on vehicles destined for sale in 
the United States or Canada, all proposed labels 
must be reviewed and approved by the Corporate 
Label Core Product Development Team prior to 
design release. 
4 Validation 
4.1 General. 
4.1.1 Material. Label materials are to be selected 
and validated in accordance with GMW14573. 
GM501M Automotive Label Material Specification 
Selector is available as a guideline for label 
material selection. 
4.1.2 Format. The Corporate Label Core PDT 
must review and approve all new labels and label 
changes proposed for use on vehicles destined for 
sale in the United States or Canada. 
4.2 Validation Cross Reference Index. Not 
applicable. 
4.3 Supporting Paragraphs. The impact of the 
new or revised label on the Owner’s Manual or 
GTK Brochure must be confirmed, and, if 
necessary, corrected. If the label is compliance 
related, it must be reviewed on a compliance 
summary by the Validation Engineer, and the Label 
DRE. 
5 Provisions for Shipping 
Not applicable. 
6 Notes 
6.1 Glossary. 
Label: A label is a message applied to a vehicle or 
component that warns, informs, or instructs. Any 
message other than specifications, bar codes, or 
identification marks is classified as a label. 
Label Regulation. 
Regulated: Labels that are required by 
national, state, or local laws and regulations or 
rulings by courts or administrative agencies. 
Government 
mandated 
requirements 
for 
regulated labels supersede this Engineering 
Standard. 
Unregulated: Labels that are not required by a 
law or a regulation. These are used to help 
satisfy customers, or are recommended by the 
vehicle platform, marketing division, Safety 
Regulations & Consumer Info, or GM Legal 
Staff. 
Label Type. 
Danger: Alert an individual to a non obvious, 
imminently hazardous situation which, if not 
avoided will result in death or serious personal 
injury. Used in the most extreme situations. 
Warning: Alert an individual to a non-obvious, 
potentially hazardous situation which, if not 
avoided could result in death or serious 
personal injury. 
Caution: Alert an individual to a non-obvious, 
potentially hazardous situation which, if not 
avoided, could result in minor or moderate 
personal injury. 
Notice: Alert an individual to a situation that 
could result in damage to the vehicle or to the 
equipment. 
Information: Provide useful information, but 
do not concern potential personal injury or 
property damage anywhere in the message. 
Combination: Contain signal words, colors, 
symbols, and message text for more than one 
type of label on a single piece of material. 
Label Bonding. 
Heat Applied: Label stock applied to a 
finished component using a heated element to 
provide bonding. 
In Mold: Label stock is placed in a mold and 
bonded to a part or component during a 
molding process, such as plastic injection. 
Pressure Sensitive: Label stock applied to a 
finished component by pressing into place, e.g. 
“peel and stick” type label. 
Direct Printing/Marking. 
Etching: A message cut directly into a finished 
surface or plate through laser, acid, etc.  This 
process can be used on a separate part, e.g. 
VIN Plate. 
Pad: A message printed onto a finished 
surface with a rubber-type stamp. 


### 第 6 页
GMW14089 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
Page 6 of 9 
July 2006
 
Embossing/De-bossing: 
A 
raised/lowered 
message molded into a component surface. 
The message may or may not be colored 
differently than the component surface.  
Silk Screen: A message printed onto a 
finished surface by pressing ink through a 
screen. 
Laser/Ink Jet: A message printed into a 
finished surface or part using a print head 
spraying colored streams of ink. 
Other. 
Instruction Card: A message that is on a 
loose piece of stock. 
Tag: A removable message that hangs from or 
is attached to a component by adhesive, 
sewing, tether, etc. 
6.2 Acronyms, Abbreviations, and Symbols. 
CLC PDT Corporate 
Label 
Core 
Product 
Development Team 
CTS 
 
Component Technical Specification 
EWO 
 
Engineering Work Order 
GTK 
 
Getting to Know your Vehicle Brochure 
KCDS 
Key 
Characteristics 
Designation 
System 
KPC 
 
Key Product Characteristics 
VAS 
 
Vehicle Assembly Structure 
VIN  
 
Vehicle Identification Number 
VTS 
 
Vehicle Technical Specification 
7 Additional Paragraphs 
7.1 All materials supplied to this specification must 
comply with the requirements of GMW3001, Rules 
and Regulations for Materials Specifications. 
7.2 All parts or systems supplied to this 
specification must comply with the requirements of 
GMW3059, Restricted and Reportable Substances 
for Parts. 
8 Coding System 
This specification shall be referenced in other 
documents, drawings, VTS, CTS, etc. as follows: 
GMW14089 
9 Release and Revisions 
9.1 Release. This standard originated in January 
2005. It was first approved by the Global Label 
Subsystem Leadership Team in July 2006. It was 
first published in July 2006. 
 


### 第 7 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14089
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
July 2006 
Page 7 of 9
Appendix A 
A1 Example Labels 
 
 
Figure 1: Information Labels (non-underhood). 
 
 
 
Figure 2: Notice Labels (underhood). 
 
 
 
Figure 3: Caution Labels (non-underhood). 
 
 
 
 
 


### 第 8 页
GMW14089 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
Page 8 of 9 
July 2006
 
 
Figure 4: Warning Labels (underhood). 
 
 
 
Figure 5: Danger Labels (underhood). 
 
 
 
Figure 6: Computer Generated Label 
 
 
 
 
 
 
 
 


### 第 9 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14089
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
July 2006 
Page 9 of 9
 
Table 1: Language Table 
Country 
Pre-Printed 
Flexible Label System 
Africa (except for 
Angola) 
English 
English 
Angola 
Portuguese 
Portuguese 
Australia 
English 
English 
Brazil 
Portuguese 
Portuguese 
Canada 
English/French (French Canadian) 
English/French (French Canadian) 
China 
Mandarin Chinese + if feasible, English 
Mandarin Chinese + if feasible, English 
Europe 
If no symbols available, ~40 languages (for 
warning labels)? 
English/German/French 
Europe 
If symbols are available - Signal words to be 
English/German/French 
If symbols are available - Signal words to be 
English/German/French 
Gulf States 
English/Arabic 
English/Arabic 
Korea 
Korean + if feasible, English 
Korean + if feasible, English 
Mexico 
Carry over labels from U.S. 
English/Spanish 
South America 
Spanish 
Spanish 
U.S. 
English (American) / 
French (French Canadian) 
English (American) 
 

