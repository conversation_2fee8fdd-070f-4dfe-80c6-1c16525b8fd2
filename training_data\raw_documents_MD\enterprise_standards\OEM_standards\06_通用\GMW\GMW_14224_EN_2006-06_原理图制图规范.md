# GMW_14224_EN_2006-06_原理图制图规范.pdf

## 文档信息
- 标题：GMN General Specification Template
- 作者：<PERSON>
- 页数：13

## 文档内容
### 第 1 页
 
 
 
 
 
 
 
 
 
 
WORLDWIDE 
ENGINEERING 
STANDARDS 
General Specification 
Electrical/Electronic 
GMW14224 
 
 
 
 
 
 
 
 
 
IEC/GMW Schematic Creation 
 
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
June 2006 
Originating Department: North American Engineering Standards 
Page 1 of 13
 
1 Introduction 
Note: Nothing in the specification supersedes 
applicable laws and regulations unless specific 
exemption has been obtained. 
Note: In the event of conflict between the English 
and domestic language, the English language shall 
take precedence. 
1.1 Scope. This specification provides instructions 
for creating Electrical Schematics for vehicles and 
vehicle SubSystems and components used in 
GM's Electrical Design Process. This specification 
is for use as the processes for developing, 
formatting, and documenting Schematics within the 
IEC/GMW drawing format. The intended audience 
includes, 
but 
is 
not 
limited 
to, 
platform 
system/design engineers and integrated Harness 
suppliers. 
This specification written based that the reader has 
knowledge of GM’s Electrical Architecture and 
System Design Process as well as a basic 
understanding of the IVED Tools. 
This specification is applicable to all major 
programs beginning with the Global A Electrical 
Architecture. It may not be economically feasible to 
convert all "carry-over" or mid-cycle enhancements 
to the new format. 
1.2 Mission/Theme. The purpose of the IEC/GMW 
Schematic Creation Standard is to enable Global 
common Electrical Architecture designs that can 
be executed globally through re-use of Electrical 
Mechanizations and Schematics that are adaptable 
to meeting the features and functions driven by 
regional market and regulatory requirements, as 
well as subsequent vehicle programs with a 
minimum of change and variation. The Global TAG 
owns the IEC/GMW Requirements and Standards. 
TAG stands for Technical Advisory Group. 
IEC 
stands 
for 
International 
Electrotechnical 
Commission. The IEC and ISO (International 
Organization 
for 
Standardization) 
have 
collaborated on a database for all graphical 
symbols from the standards IEC 60417 and ISO 
7000 (Both titled “Graphical Symbols for Use on 
Equipment”). These standards are recognized 
world wide as the authority for all Electrical 
drawings and designs. The IEC/GMW Schematic 
Creation 
Standard 
bases 
itself 
upon 
these 
standards, and defines its application to GM in this 
document. While GMW implies a General Motors 
Worldwide 
standard, 
IEC/GMW 
defines 
this 
drawing standard as based on this specification. 
1.3 Classification. This manual is not restricted. 
2 References 
Note: Only the latest approved standards are 
applicable unless otherwise specified. 
2.1 External Standards/Specifications. 
IEC 60417 
ISO 7000 
2.2 GM Standards/Specifications. 
GME 8740 
GMW3176 
GMW3059 
 
2.3 Additional References. 
GM’s 
Electrical 
Architecture 
and 
System 
Design 
Process. 
This 
is 
the 
process 
for 
development of the Overall Vehicle Electrical 
System. 
SubSystem Names List. The SubSystem Names 
List which is managed by Global TAG. 
Object Code List. The Object Code List which is 
managed by Global TAG. 
Symblet Palette File. This is the schematic file 
which is the collection of the approved First Level 
I/O Graphics. This data set is managed by Global 
TAG and Included in the IVED Library. 
IVED Tools. The IVED Tool suite used to generate 
2D Schematics & Mechanizations. 
3 Requirements 
3.1 General Drawing Standards. Use the 
IEC/GMW drawing standard choice in IVED, which 
supports 
the 
IEC/GMW 
standard. 
Use 
the 
requirements below for additional direction. 
3.1.1 Font Size. All drawing elements, that contain 
text, shall use existing standards for consistent 
Font Size (Symblets, Symbols, Wire Elements, 
etc.) determined and managed by the Global TAG. 


### 第 2 页
GMW14224 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
Page 2 of 13 
June 2006
3.2 Component Symbols. 
3.2.1 Object Codes. Symbols shall be named 
using object codes, the names of which are 
derived from UPC/FNA, managed by the Global 
TAG, and assigned by the IVED system. 
3.2.1.1 Object Code Instance. Object Codes do 
not include many types of variance of a 
Component. One example would be Vehicle 
Location (i.e., Left Rear Speaker). In order to 
communicate this additional Component Naming 
Criteria 
a 
value 
shall 
be 
in 
the 
OBJECT_CODE_INST property. This value shall 
be in Upper Case without spaces and cannot 
exceed 15 characters. In place of spaces 
underscores “_” are acceptable. 
3.2.2 VPPS Property. Symbols shall contain a 
VPPS property for future population and alignment 
with the VPPS code of all components. This 
property shall be there when creating a new 
symbol (or adding "IVED Specific Properties"). 
3.2.3 First Level Simplified I/O Graphic. Symbols 
shall show and describe the First Level simplified 
I/O Graphic (interface) to provide a conceptual 
representation of the physical interface. (For 
example, if the first level interface electronics of a 
module contain a High Side Drive, the symbol shall 
show the graphical representation of that High Side 
Drive also known as HSD.) This information can 
assist an Electrical Architecture engineer to detect 
a potential interface mismatch. 
 
Figure 1: First Level Simplified I/O Graphic 
3.2.4 Symblet Palette. Use consistent Symblet 
Palette, which is accessible in IVED through the 
Library Selection to build the Component Symbol. 
The Symblets contain the proper First Level 
simplified I/O Graphic and the appropriate Pin 
Properties. 
3.2.5 Symblets. A Symblet is the most basic 
element used to create object-oriented Electrical 
Schematic symbols. The Symblet Palette is 
defined within the IVED IEC/GMW tool and 
available via the IVED Library. Only Symblets 
available through the IVED library are allowable for 
use. If requiring a new Symblet, make the request 
of the Global TAG. 
3.2.6 Component Pin Properties. Pins shall 
contain the following properties (If using Circuit 
Numbers on a pin, use those contained in 
GMW3176 “GM Common Circuit List” or GME8740 
“GM Europe Specific ID and Wire Color Reference 
List”). The font size shall be exactly what is in each 
Symblet coming from the Symblet Library. Most of 
these properties come pre-populated from the 
Symblet Library when using Simplified I/O and do 
not need changing. These properties shall stay in 
the locations provided upon instantiation from 
Symblet Library. It is allowable to make unused 
Properties “hidden” if not already that way. 
 
Figure 2: Component Pin Properties 
• 
IOID – I/O Identification used mostly in 
Modules to define a particular Driver or Input. 
• 
PIN – I/O Name which contains a Functional 
Description of the attached Circuit. Shown as 
IONAME on Symblets. 
• 
IOTYPE – Type of I/O (i.e., HSD, LDI, Switch, 
etc.) 
• 
IOVALUE – Additional Descriptor for the I/O 
(i.e., PNP, WUF, NC, etc.) 
• 
CONN_ID 
– 
Mating 
Harness 
Connector 
Identifier 
• 
CAV_ID – Mating Harness Cavity Identifier 
• 
CAV_ID_C – Component Cavity ID if different 
from Mating Harness Cavity. Make visible if 
using. 
• 
IOPMODE – Power Mode of the particular 
input or output (See Appendix section for 
abbreviations). These come with Symblets that 
contain an internal Power Feed. Default value 
is PWR. 
• 
IOGZONE – Ground Zone associated with the 
input or output. These come with Symblets that 
contain an internal Ground Feed. (This 
property is available, but not required.) Make 
visible if using.  
• 
TERMINAL (Added as required) – Terminal 
Plating Requirements if Terminal is a material 
other than Tin or Terminal Part Number as 
appropriate. This property also utilized to 
communicate a Shorting Clip. See Appendix 
section for allowable Terminal Plating values. 
Combinations of these Terminal Plating values 
allowed, when necessary, by utilizing an 
underscore “_” as a separator. 
3.2.7 Component Pin Graphics. Pin graphics 
shall be consistent with the style used in the 
 


### 第 3 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14224
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
June 2006 
Page 3 of 13
 
Symblet Palette, accessed through IVED. Pins 
shall be a minimum of 12 major grid spaces apart. 
3.2.8 Component Line Weights. Component 
outer perimeter line weights shall be 5 point, and 1 
point for circuitry shown inside a component. (This 
allows visual cues for what is internal and external 
to a component. For example, a Net, which is 3 
point, comes to a 5 point perimeter of a component 
and then changes to 1 point inside that 
component.) 
3.2.9 
Partitioned 
Component 
Identifier. 
A 
Functionally Partitioned Component is a module or 
a component that has functions in more than one 
SubSystem. It is a single part and has only one 
part number. An Instrument Panel Cluster is a 
good 
example 
of 
a 
functionally 
partitioned 
component. It has functions in the Displays 
SubSystem, the Obstacle Detection SubSystem, 
and the Wipe/Wash SubSystem. A Body Computer 
Module (BCM) is another example of a functionally 
partitioned component. 
Use a dashed line around the entire component 
box to identify a partial component that has the 
remainder contained elsewhere in the Schematic. 
The dashed line shall be 5 point. (Reference the 
Schematic Index section of this specification for 
information as to component location.) 
3.2.10 
Placeholder 
Properties. 
Placeholder 
properties are properties that are available on a 
Symbol, 
but 
contain 
no 
value 
(i.e., 
OBJECT_CODE_INST, etc.). These properties 
shall have a value of a space and shall be hidden 
at the Symbol level. When populating these 
properties with an actual value, the property shall 
change state to visible, either manually (or 
automatically as applicable). 
3.2.11 Connector Color Property. When creating 
a Symbol (or adding "IVED Specific Properties"), 
the Connector Color property shall be a space " " 
and shall be hidden as a default. 
3.2.12 BECs (Bussed Electrical Centers). BECs 
shall show the underlying connectivity and the 
internal Schematic shall be created, at the 
SubSystem Level, such that the BEC can 
successfully NetList for a match check with the 
BEC Supplier’s data. All BEC internal circuits shall 
be like a Harness using a Bus Bar Symbol in place 
of a Wire Symbol. All BEC internal component 
symbol(s) shall be from the IVED Library. All 
requirements 
for 
Harnesses, 
except 
those 
specifically 
related 
to 
stranded 
cable 
and 
coverings, shall apply, including optioning. 
3.2.12.1 BEC Fuses. Unlike an in harness fuse or 
a fuse within a component, BEC Fuses are pre-
assembled components for representation within a 
BEC. BEC Fuses shall be from the IVED Library. 
The BEC Fuse Object Code is a fixed value 
(presently F21). Include the Fuse Name in the 
OBJECT_CODE_INST Property which has default 
value of FUSE_NAME. Provision is available, if 
needed, to include the Fuse Part Number in the 
COMP_ID property. 
3.2.12.2 BEC Circuit Breakers. BEC Circuit 
Breakers are pre-assembled components for 
representation within a BEC. BEC Circuit Breakers 
shall be from the IVED Library. The BEC Circuit 
Breaker Object Code is a fixed value (presently 
F22). Include the Circuit Breaker Name in the 
OBJECT_CODE_INST Property which has default 
value of BRK_NAME. Provision is available, if 
needed, to include the Circuit Breaker Part Number 
in the COMP_ID property. 
3.2.12.3 BEC Resistors. Unlike an in harness 
resistor or a resistor within a component, BEC 
Resistors are pre-assembled components for 
representation within a BEC. BEC Resistors shall 
be from the IVED Library. The BEC Resistor 
Object Code is a fixed value (presently R611). 
Include 
the 
Resistor 
Name 
in 
the 
OBJECT_CODE_INST Property which has default 
value of RES_NAME. There is provision for 
Resistor 
Value 
if 
needed 
in 
the 
RESISTOR_VALUE property. If unused, property 
shall have a visibility setting of hidden. 
3.2.12.4 BEC Diodes. Unlike an in harness diode 
or a diode within a component, BEC Diodes are 
pre-assembled components for representation 
within a BEC. BEC Diodes shall be from the IVED 
Library. The BEC Diode Object Code is a fixed 
value (presently V600). Include the Diode Name in 
the OBJECT_CODE_INST Property which has 
default value of DIODE_NAME. 
3.2.12.5 BEC Relays. Unlike an in harness relay 
or a relay within a component, BEC Relays are 
pre-assembled components for representation 
within a BEC. BEC Relays shall be from the IVED 
Library. Utilize the correct Object Code for the 
Relay from the "K" Relay Object Codes provided 
within the IVED Tool. Utilize the Developmental 
BEC Relay Object Code (presently K0) only as 
needed and include the Relay Name in the 
OBJECT_CODE_INST Property which has default 
value of blank. Provision is available, if needed, to 
include the Relay Part Number in the COMP_ID 
property. 
3.2.13 Ring Terminals. Ring Terminals shall be 
from the IVED Library. Add Connector Numbers at 
the Sheet Level. Utilize OBJECT_CODE_INST 
Property for unique identifier (i.e., ZONE_21, 


### 第 4 页
GMW14224 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
Page 4 of 13 
June 2006
 
FWD_LP, G201, etc.). CAV_ID “CR” means 
“Crimp”. 
3.2.14 Ground Studs. Ground Studs shall be from 
the IVED Library. Utilize OBJECT_CODE_INST 
Property for unique identifier (i.e., ZONE_21, 
FWD_LP, G201, etc.). CAV_ID “ST” means “Stud”. 
There are three basic types of Ground Studs which 
are Body, Frame, or Engine. Each of these has an 
appropriate Object Code. A plain Ground Stud 
Object Code provided for use when Stud is not a 
basic type. 
3.3 Subsystem Schematics. Both SubSystem 
Mechanization and SubSystem Schematic shall be 
functionally partitioned the same based upon the 
SubSystem definitions managed by the Global 
TAG via direction from the SubSystem Leadership 
Teams (SSLTs). SubSystem Schematics shall 
contain the same functional content as shown in its 
Mechanization counterpart. For example, the 
Component, “Module – Universal Garage Door 
Opener”, is contained within the minor SubSystem 
“Universal Garage Door Opener”, which is a part of 
the major SubSystem “Vehicle Access & Starting 
Security”. This content shall be the same in both 
the SubSystem Mechanization and the SubSystem 
Schematic. 
Note: Serial Data messages shall not be on 
SubSystem Schematics. The Serial Data group 
manages serial Data information and there is no 
need to redundantly represent that data in our 
Schematics. 
3.3.1 Names. The SubSystem name shall be one 
of the choices made available via the IVED Tool. If 
requiring a new SubSystem name, make the 
request to the Global TAG. 
3.3.2 Title Block. SubSystem Schematics shall 
have a Title Block. Placement of properties in the 
Title Block shall remain in the same location as 
provided 
in 
the 
IEC/GMW 
Drawing 
format 
“Add/Move Panel” selection within the IVED tool. 
The Title Block shall contain the following 
properties. 
• 
File Name 
• 
Revision 
• 
Authoring / Modifying Engineer 
• 
Date last updated 
• 
Time last updated 
• 
Drawing Format ID 
• 
Major 
SubSystem 
Name 
and 
Minor 
SubSystem Name as applicable 
• 
SubSystem Short Name 
• 
SubSystem Variant, if applicable 
• 
Panel Number 
• 
Panel Total Number 
• 
Sheet Page Number 
• 
Sheet Total Number 
• 
Model Designator, if applicable 
• 
Option Usage, if applicable 
• 
Model Year 
• 
Program 
• 
Build Phase 
• 
System Status (Working or Released) 
• 
Object Status (Working or Released) 
3.3.3 Power and Ground SubSystems. When 
Power and Ground are functionally partitioned, the 
Power and Ground distribution shall be fully shown 
in the Power and Ground SubSystem Schematics, 
up to the point where it is functionally partitioned 
(i.e., show as much of Power & Ground in the 
Power & Ground SubSystems as possible). 
3.3.4 Ports. Use the Port Symbol(s) available via 
the IVED Library. Port Symbols used to connect a 
circuit that leaves a section of a drawing to arrive 
on another section of a drawing, either a different 
page or different area of a drawing, in essence 
porting the circuit. Each Port contains a NetName. 
NetNames uniquely define the partitioned Circuit. 
Note: CLASS=N Ports do not work correctly with 
the 
Port 
Mapping 
Features 
of 
IEC/GMW. 
Therefore, when using IEC/GMW you must always 
use CLASS=P Ports. For IEC/GMW, all Port 
Symbols available from the IVED Library are fixed 
at CLASS=P and should cause no concern. 
******* NetName. A NetName shall be the Circuit 
Number to which the Port connects. If there are 
multiple Ports with the same circuit number, then 
the 
NetName 
shall 
also 
include 
the 
OBJECT_CODE 
of 
the 
Target 
Component 
separated by an underscore “_”. If there are 
multiple ports with the same circuit number and 
OBJECT_CODE, then the NetName shall also 
include 
an 
additional 
descriptor 
that 
will 
differentiate that port (i.e., an RPO or another 
descriptor) separated by an underscore “_”. For 
example, Circuit 340, connected to M3 Motor-
Blower and with the RPO C42 would result in a 
NetName of 340_M3_C42. When working on a 
team, 
there 
must 
be 
communication 
and 
coordination as to the third level of variation of a 
NetName. 
******* Automated Port Mapping. Port Mapping 
information can either be entered manually or 
automatically when using the Port Mapping 
Feature. Ports also mapped automatically when 
performing Publishing. Much of the Port Mapping 


### 第 5 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14224
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
June 2006 
Page 5 of 13
 
information, such as exact page and grid number, 
may be only temporary. While being able to save 
temporary port mapping (manual or automatic), the 
destinations (exact page and grid number) may 
change at any time and this temporary port 
mapping may not accurately map to the correct 
destination. However, IVED will overwrite the 
temporary 
port 
mapping 
with 
the 
correct 
information the next performance of Port Mapping 
or Publishing. 
There are two numerical values separated by a 
decimal point in the logical properties just to the 
right of the Port Symbol. The first (3) digits 
represent the page number of the SubSystem 
Schematic in which the distant port resides and the 
second (2) digits represent the coordinates along 
the bottom of that panel where that distant 
component is located. 
Every port on every SubSystem shall connect via a 
net to some device or wire on each of their 
respective SubSystems. This means that there 
must be a real or non-virtual pin on a physical 
component connected via a net to the port pin on 
its own local Schematic. Each port must contain a 
NetName value (See NetName section of this 
specification for details of content). 
3.3.4.3 Coordinates. The Title Block contains a 
set of Coordinates and a Sheet/Panel Number 
used by Port Mapping. The Port Mapping 
Information added to the Schematics after all 
Schematics are complete and are referencing 
connectivity 
between 
SubSystems 
at 
the 
SuperSystem level. 
The Sheet/Panel Number property used as a 
reference by remote SubSystems and only filled in 
after all of the SubSystems are added together on 
the SuperSystem level Schematic and performing 
Port Mapping, or Publishing. 
The Panel Coordinate system on the bottom of a 
SubSystem Schematic is provided as a reference 
to a complement SubSystem Port (coordinate and 
Sheet/Panel Number). 
3.3.5 POA Harnesses. For those POA (Part of 
Assembly) Harnesses released by the supplier of 
the component to which the Harness is connected 
(not GM Power & Signal Distribution Group), the 
supplier shall communicate content such as wire 
gage size, stranding, insulation type, insulation 
color, and any other cable properties. This 
information shall communicate via a structured 
requirements gathering process. These Harnesses 
differ from the Harnesses released by GM Power & 
Signal Distribution Group. These wiring details 
shall be in the Schematics and treated as any 
other Harness in so much that all Schematic 
Standards apply. The POA Harness data shall 
properly NetList when used by the IVED system. 
3.3.6 Pigtails. A pigtail is a wire Harness 
extending from a device that is an integral part of 
that device. Pigtails shall be representative of 
actual harnesses in the Schematics. The elements 
of this representation are a Symbol, a wire, and an 
inline. 
There shall be a symbol for the component 
containing a pigtail, the same as a component 
containing a header connector. The connector 
naming shall differ from components with a header 
connector as follows. 
The CONN_ID shall include a “C” for component 
immediately following the “X” and incrementing in 
number based on the number of connectors 
associated with the component. The connector 
number shall be “PIGTAIL” followed by a numeric 
designator 
associated 
with 
the 
number 
of 
connectors associated with the component. For 
example, a component that has two pigtails would 
have XC1 and XC2 as the CONN_IDs and 
PIGTAIL1 
and 
PIGTAIL2 
as 
the 
connector 
numbers. 
The pigtail harness name shall be “Pigtail” which 
comes from the appropriate Pigtail Object Code 
(presently W604). To differentiate pigtails, the 
Object Code Inst property shall contain the Object 
Code of the component from which the pigtail 
connects. If the component also has an Object 
Code Inst value, that value shall be appended to 
the Object Code Inst value on the pigtail wire. For 
example, the Left Rear Park Lamp would have 
Object Code = E83 and Object Code Inst = LR, so 
the Object Code Inst of the Wire would be E83LR. 
This will result in these pigtail wires having the 
identification of W604E83LR. 
The pin-out of the connector(s) on the symbol 
would then receive the same pin-out of the Inline 
used to represent the pigtail, utilizing the CAV_ID 
property of each pin. 
The inline shall be the same as any other inline 
shown in the schematics representing the two 
harnesses it connects. For example, there would 
be an inline for Left Rear Speaker to Body. 
For a Circuit Color within the Pigtail wiring that 
deviates from the standard Circuit Color of the 
associated 
Circuit, 
there 
shall 
be 
a 
COLOR_NON_STD property added to the Wire 
Symbol to allow the Non Standard Circuit Color to 
change appropriately. 
3.3.7 Other Wiring Elements. Special Care 
needed when naming these Other Wiring Elements 
while using developmental numbers (900 to 999 or 
9000 to 9999) not to redundantly create the same 


### 第 6 页
GMW14224 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
Page 6 of 13 
June 2006
number/name in two, or more, places. For this fact, 
communication is indispensable when two, or 
more, people are drawing Schematics on the same 
Platform or Program. 
******* Twists. Use the Twist Symbol(s) available 
via the IVED Library. Twist Names shall be "TW" 
followed by the Smallest Circuit Number of the 
wires that twist together (i.e., 6760 & 6761 twisted 
together would make TW6760 as the Twist name). 
There is no requirement to differentiate the Twist 
Names in different Harnesses since the Harness 
names already differentiate them. If there are 
multiple Twist sets in the same Harness, there 
shall be an additional differentiator separated by an 
underscore "_" at the end of the name (i.e., 
TW6760_1, TW6760_2, etc.). There is provision on 
the Twist Symbol for including Twisting Rate in a 
property named TWIST_RATE, which has a 
default value of 55. This value defines the twist 
rate of millimeters (mm) per twist. Change this 
value as needed. 
   
 
Figure 3: Twists 
******* Shields. Use the Shield Symbol(s) 
available via the IVED Library. Shield Names shall 
be "SH" followed by the Shield Circuit Number (i.e., 
6760, 6761, have a shield circuit of 7293, then the 
Shield name shall be SH7293). There is no 
requirement to differentiate the Shield Names in 
different Harnesses since the Harness names 
already differentiate them. If there are multiple 
Shields in the same Harness, there shall be an 
additional 
differentiator 
separated 
by 
an 
underscore "_" at the end of the name (i.e., 
SH7293_1, SH7293_2, etc.). 
******* Splices. There are two types of Splices 
represented within this standard which shall differ 
graphically as well as text labeling. 
*******.1 Standard Splices. Use the Standard 
Splice Symbol available via the IVED Library. 
Standard Splices shall have a label of “STD”. 
Standard Splice Names shall be "SP" followed by 
the Circuit Number (i.e., SP1050). If there are 
multiple Standard Splices with the same Circuit 
Number, there shall be an additional differentiator 
separated by an underscore "_" at the end of the 
name (i.e., SP1050_1, SP1050_2, etc.). 
*******.2 Cut Splices. Use the Cut Splice Symbol 
available via the IVED Library. Cut Splices shall 
have a label of “IDC” (Insulation Displacement 
Connector). Cut Splice Names shall be "SJ" 
followed by the Circuit Number (i.e., SJ1050). If 
there are multiple Cut Splices with the same Circuit 
Number, there shall be an additional differentiator 
separated by an underscore "_" at the end of the 
name (i.e., SJ1050_1, SJ1050_2, etc.). 
******* Blunt Cut. Use the Blunt Cut Symbol 
available via the IVED Library. Blunt Cut Names 
shall be "BC" followed by the Circuit Number (i.e., 
BC1732). If there are multiple Blunt Cuts with the 
same Circuit Number, there shall be an additional 
differentiator separated by an underscore "_" at the 
end of the name (i.e., BC1050_1, BC1050_2, etc.). 
******* Loop Anchor. (Loop Anchors enable the 
3D Designers to designate a place in space and 
then adjust the length as necessary in a single 
circuit.) Use the Loop Anchor Symbol available via 
the IVED Library. Loop Anchor Names shall be 
"LA" followed by the Circuit Number (i.e., LA6178). 
If there are multiple Loop Anchors with the same 
Circuit Number, there shall be an additional 
differentiator separated by an underscore "_" at the 
end of the name (i.e., LA6178_1, LA6178_2, etc.). 
3.3.8 Option Brace. When representing optional 
circuits from a single circuit the Nets shall be only 
vertical and horizontal lines. An Option Brace shall 
be on each side of the horizontal line. Use the 
Option Brace symbol available in the IVED Library. 
3.3.9 Change Log. All SubSystem Schematic 
change references shall be on a dedicated Change 
Log page and not on individual SubSystem 
Schematic pages utilizing the change log symbols 
from the IVED Library. Information for inclusion is 
Approval Date, Change Number, and a Description 
of Change. Approval Date is the date of approval in 
the format of DD-MM-YYYY with the month shown 
as a two letter abbreviation (See Appendix section 
for 
abbreviations). 
Change 
Number 
is 
the 
Authorizing change number. The Description of 
Change is the title of the Authorizing Change 
Request. 
Note: The IEC/GMW IVED Library has provisions 
for tracking changes per page, if desired. If used, 
these parts shall only contain the Change Request 
number and shall only include the last five (5) 
changes. 
3.3.10 Notes for SubSystems. Notes shall be in a 
SubSystem for an aid to understanding or for 
points of clarification. The Notes List preferred 
location is bottom right corner of the page starting 
at grid 35. The Notes List Contains Two (or More) 
Symbols. Use the Note Title, the Note List, and the 
Note List More Symbols from the IVED Library, as 
applicable, for these lists. 
 


### 第 7 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14224
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
June 2006 
Page 7 of 13
If a note is specific to a section of the SubSystem 
or a particular item within the SubSystem, a Note 
ID shall be near or within that section or item 
referencing the note list below by including the 
appropriate note number. Use the Note ID symbol 
from the IVED Library. 
3.3.11 Crossing Circuits (Nets). Crossing Circuits 
(Nets) 
shall 
not 
occur 
unless 
absolutely 
unavoidable. If necessary, one of the two nets shall 
jog up and over the other net. See examples 
below. 
   
 
Figure 4: Crossing Circuits (Nets) 
3.3.12 Indexes. 
3.3.12.1 Component Index. There shall be a 
programmatic process for automatically creating a 
Component Index for inclusion in an "All-in-one" 
PDF with the name "Appendix-Component Index". 
This index shall have a Component Sorted version 
and a SubSystem Sorted version. These shall be 
at the end of the PDF drawing set. 
3.3.12.1.1 Component Sort. Component Sort 
shall be alphabetical by Columns, starting with 
Column 1. Component Sort shall contain the 
following data: 
• 
Column 1: Component Name (including any 
modifiers - Object_func1, etc.) (i.e., Motor {tab} 
Blower ; Front) 
• 
Column 2: Object Code (i.e., M3) 
• 
Column 3: SubSystem Short Name (i.e., hv02) 
• 
Column 4: SubSystem Name (Major, Minor, & 
Func1 if appropriate) (i.e., HVAC: Blower 
Control Front; Manual) 
• 
Column 5: Page Number/Page Position (i.e., 
075.27) 
3.3.12.1.2 SubSystem Sort. SubSystem Sort shall 
be alphabetical by Columns, starting with Column 
1. SubSystem Sort to contain the following data: 
• 
Column 1: SubSystem Short Name (hv02) 
• 
Column 2: SubSystem Name (Major, Minor, & 
Func1 if appropriate - i.e., HVAC: Blower 
Control Front; Manual) 
• 
Column 3: Component Name (including any 
modifiers - Object_func1, etc. - i.e., Motor {tab} 
Blower ; Front) 
• 
Column 4: Object Code ("M3") 
• 
Column 5: Page Number / Page Position (i.e., 
075.27) 
3.3.12.2 SubSystem Index. There shall be a 
programmatic process for automatically creating a 
SubSystem Index for inclusion in an "All-in-one" 
PDF with the name "Appendix-SubSystem Index” 
and shall contain the following information. 
SubSystem 
Index 
shall 
be 
alphabetical 
by 
Columns, starting with Column 1. SubSystem 
Index shall have the following data: 
• 
Column 1: Page Number 
• 
Column 2: SubSystem Short Name 
• 
Column 3: SubSystem Name (including Major, 
Minor, & Func1 if appropriate)  
3.4 
SuperSystem 
Level 
Schematic. 
A 
SuperSystem level Schematic is a collection of 
SubSystems (represented in symbol form) that 
collectively 
represents 
the 
complete 
Vehicle 
Electrical System. Within a specific project, there is 
one SuperSystem level Schematic for each build 
phase, block, or event. The SuperSystem level 
Schematic is to include a Title Block without any 
panels for printing. 
4 Validation 
4.1 General. Not applicable. 
4.2 Validation Cross Reference Index. Not 
applicable. 
4.3 Supporting Paragraphs. Not applicable. 
5 Provisions for Shipping 
Not applicable. 
6 Notes 
6.1 Glossary. Not applicable. 
6.2 Acronyms, Abbreviations, and Symbols. 
Contained within the body of this Specification 
2D  
 
Two Dimensional 
3D  
 
Three Dimensional 
BEC 
 
Bussed Electrical Center 
CTS 
 
Component Technical Specification 
FNA 
 
Functional Name Address 
GM  
 
General Motors 
GME 
 
General Motors Europe 
GMW  
General Motors Worldwide 
HSD  
 
High Side Drive 
I/O  
 
Input/Output 
ID  
 
Identification 
IDC  
 
Insulation Displacement Connector 
 


### 第 8 页
GMW14224 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
Page 8 of 13 
June 2006
 
IEC 
International 
Electrotechnical 
Commission 
ISO 
International 
Organization 
for 
Standardization 
IVED 
 
Integrated Vehicle Electrical Design 
LDI  
 
Low Discrete Input 
NC  
 
Normally Closed 
PDF 
 
Portable Document Format 
POA 
 
Part of Assembly 
RPO 
 
Regular Product Option 
SSLT  
SubSystem Leadership Team 
STD 
 
Standard 
TAG 
 
Technical Advisory Group 
UPC 
 
Uniform Parts Clarification 
VPPS 
Vehicle 
Partitioning 
and 
Product 
Structure 
WUF 
 
Wake Up Falling 
7 Additional Paragraphs 
7.1 All parts or systems supplied to this 
specification must comply with the requirements of 
GMW3059, Restricted and Reportable Substances 
for Parts. 
8 Coding System 
This specification shall be referenced in other 
documents, drawings, VTS, CTS, etc.. as follows: 
GMW14224 
9 Release and Revisions 
9.1 Release. This general specification originated 
in June 2005. It was first approved by the IVED 
Global Technical Advisory Group in May 2006. It 
was first published June 2006. 
 


### 第 9 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14224
 
© Copyright 2005 General Motors Corporation All Rights Reserved 
June 2006 
Page 9 of 13
 
Appendix A: Symblet Acronyms, Abbreviations, & Initializations 
 
A/D  
 
Analog to Digital 
AF  
 
Audio Frequency 
AUX 
 
Auxiliary 
BATT  
Battery 
CAP 
 
Capacitor 
COMP  
Comparator 
CONN   
Connector 
DISP 
 
Display 
DPST  
Double Pole Single Throw 
ELECT  
Electronics 
FET 
 
Field Effect Transistor 
GMHS-H 
General Motors High Speed Serial 
Data Bus High 
GMHS-L 
General Motors Low Speed Serial 
Data Bus Low 
GMLS 
General Motors Low Speed Serial 
Data Bus 
GMW  
General Motors Worldwide 
GND 
 
Ground 
HBRDGE H-Bridge 
HDI  
 
High Discrete Input 
HI  
 
High 
HID  
 
High Intensity Discharge 
HSD 
 
High Side Drive 
HZ  
 
Hertz (used to describe Frequency) 
IDC  
 
Insulation Displacement Connector 
IEC 
International 
Electrotechnical 
Commission 
IO  
 
In-put Output 
IR  
 
In-fra Red 
LATCH  
Latching 
LDI  
 
Low Discrete Input 
LED 
 
Light Emitting Diode 
LIN  
 
Linear Interconnect Network 
LO  
 
Low 
LSD 
 
Low Side Drive 
MOM 
 
Momentary 
MOV 
 
Metal Oxide Varistor 
MTR 
 
Motor 
NC  
 
Normally Closed 
NEG 
 
Negative 
NO  
 
Normally Open 
PD   
 
Pull Down 
POS 
 
Positive 
PTC 
 
Positive Temperature Coefficient 
PU  
 
Pull Up 
PWM 
 
Pulse Width Modulation 
PWR 
 
Power 
REF 
 
Reference 
RES 
 
Resistor 
RF  
 
Radio Frequency 
RLY 
 
Relay 
RPO 
 
Regular Product Option 
RTN 
 
Return 
SDATA  
Serial Data 
SENS  
Sensor/Sensing 
SPDT  
Single Pole Double Throw 
SPST  
Single Pole Single Throw 
SRC 
 
Source 
SS  
 
Solid State 
STD 
 
Standard 
SW  
 
Switch 
TEMP  
Temperature 
TRI  
 
Tri-State Drive 
VAR 
 
Variable 
VF  
 
Video Frequency 
WUD 
 
Wake Up Dual (Rising or Falling) 
WUF 
 
Wake Up Falling 
WUR 
 
Wake Up Rising 
XCVER  
Transceiver 


### 第 10 页
GMW14224 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
Page 10 of 13 
June 2006
 
Appendix B: Power Mode Abbreviations / Initializations 
 
_V  
 
Use a value and V for Voltage (i.e., 12V, 5V, etc.) 
ACCY  
Accessory 
BAT 
 
Battery 
O/R/C  
Off/Run/Crank 
R/C  
 
Run/Crank 
RUN 
 
Run 
SBAT  
Switched Battery 


### 第 11 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14224
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
June 2006 
Page 11 of 13
 
Appendix C: Index Examples 
 
“Appendix – Component Index” 
-- Component Sort 
Motor Blower : Front  
M3 HV02 
HVAC : Blower Control Front ; Automatic 
075.27 
Motor  Blower ; Front  
M3 HV02 
HVAC : Blower Control Front ; Manual 
 
079.42 
 
“Appendix – Component Index” 
-- SubSystem Sort 
HV02 
HVAC : Blower Control Front ; Automatic 
Motor  Blower ; Front  
M3 075.27 
HV02 
HVAC : Blower Control Front ; Manual   
Motor  Blower ; Front  
M3 079.42 
 
“Appendix – SubSystem Index” 
074  
HV02 
HVAC : Blower Control Front ; Automatic 
077  
HV02 
HVAC : Blower Control Front ; Automatic 


### 第 12 页
GMW14224 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
Page 12 of 13 
June 2006
 
Appendix D: Month Abbreviations 
 
JA  
January 
FE  
February 
MR  
March 
AP  
April 
MY  
May 
JN  
June 
JL  
July 
AU  
August 
SE  
September 
OC  
October 
NO  
November 
DE  
December 


### 第 13 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW14224
 
© Copyright 2006 General Motors Corporation All Rights Reserved 
June 2006 
Page 13 of 13
 
Appendix E: Terminal Property Plating Values 
 
Blank/No Property Tin 
AU  
Gold 
AG  
Silver 
BE  
Beryllium 
CU  
Copper 
SC  
Shorting Clip 
 

