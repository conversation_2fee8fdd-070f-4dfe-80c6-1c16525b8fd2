# GMW_15862_EN_2010-11_GM线束标签规范.pdf

## 文档信息
- 标题：
- 作者：
- 页数：59

## 文档内容
### 第 1 页
 
 
 
 
 
 
 
 
WORLDWIDE 
ENGINEERING 
STANDARDS 
General Specification 
Interior 
GMW15862 
 
 
 
 
 
 
 
 
Bar Code Content, Format, and Label Requirements for Part 
Identification, Verification, and Traceability 
 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Originating Department: North American Engineering Standards 
Page 1 of 59  
 
1 Introduction ................................................................................................................................................ 5 
1.1 Scope. ................................................................................................................................................... 5 
1.2 New or Revised Parts, Components, Assemblies, and Modules with Linear Bar Codes. ......................... 5 
1.3 New Sourced Parts, Components, Assemblies, and Modules. ................................................................ 5 
1.4 Carryover Parts, Components, Assemblies, and Modules. ...................................................................... 5 
1.5 Control Module Software SHALL comply with GMW4710. ...................................................................... 5 
1.6 Reasons for Bar Code Scanning. ........................................................................................................... 5 
1.6.1 Traceability. ..................................................................................................................................... 5 
1.6.2 Verification/Error Proofing. ............................................................................................................... 5 
1.6.3 Part Identification.. ........................................................................................................................... 5 
2 References .................................................................................................................................................. 5 
2.1 External Standards/Specifications. ......................................................................................................... 5 
2.2 GM Standards/Specifications. ................................................................................................................ 5 
2.3 Additional References. ........................................................................................................................... 5 
3 Parts, Components, Assemblies or Modules Requiring Bar Codes. ....................................................... 6 
3.1 Parts, components, assemblies and modules identified as requiring ....................................................... 6 
3.1.1 Process Owner Responsibility for Bar Code Requirements Communication. .................................... 6 
3.2 Traceability. ........................................................................................................................................... 6 
3.2.1 Within this standard, the answer to these four questions is as follows:.............................................. 7 
3.2.2 Trace Record. .................................................................................................................................. 7 
3.2.3 GM Defined Traceability Code Structure. ......................................................................................... 7 
3.2.4 Data Fields. ..................................................................................................................................... 8 
3.2.5 BIG RULE: Data Identifiers SHALL Be Used. ................................................................................... 8 
3.3 Verification/Error Proofing. ..................................................................................................................... 8 
3.4 Product Identification. ............................................................................................................................. 9 
4 Bar Code Symbologies and Encodation ................................................................................................. 10 
4.1 Bar Code Symbology. .......................................................................................................................... 10 
4.1.1 The Case for 2D. ........................................................................................................................... 10 
4.1.2 Allowable Data Characters. ............................................................................................................ 10 
4.1.3 Data Fields and Data Identifiers. .................................................................................................... 11 
4.2 Data Matrix and QR Code Densities and Dimensions. .......................................................................... 11 
4.2.1 A particular symbol size depends on the amount and type of data encoded ................................... 11 
4.2.2 Within the constraints of the available marking area ....................................................................... 11 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 2 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 2 of 59 
 
4.2.3 To allow for the best possible imager performance ......................................................................... 11 
4.2.4 BIG RULE: Make the bar code (1D or 2D) symbol as large as practical .......................................... 11 
4.3 Data Matrix and QR Code Quiet Zones. ............................................................................................... 12 
4.3.1 Data Matrix quiet zone is equal to two times the symbol cell dimension (Figure 7). ......................... 12 
4.3.2 QR Code quiet zone is equal to four (4) times the symbol cell dimension (Figure 8). ...................... 12 
4.4 Error Correction Levels. ....................................................................................................................... 12 
4.4.1 Data Matrix Error Correction Code (ECC) Level. ............................................................................ 12 
4.4.2 QR Code Error Correction (EC) Levels. ......................................................................................... 13 
4.5 Data Matrix and QR Code Data Format. ............................................................................................... 13 
4.5.1 Data Syntax ISO/IEC 15434. ......................................................................................................... 13 
4.5.2 Compliance Header. ...................................................................................................................... 13 
4.5.3 Data Matrix Header and Trailer 06 Macro BIG RULE: .................................................................... 13 
4.6 Single Data Field Encodation. .............................................................................................................. 14 
4.7 Rectangular Data Matrix. ...................................................................................................................... 14 
4.7.1 Curve Surfaces. ............................................................................................................................. 14 
4.8 Long Range Scanning. ......................................................................................................................... 15 
4.9 VIN. ..................................................................................................................................................... 15 
4.10 Labeling Electronic Modules. .............................................................................................................. 15 
4.11 Tire Labeling Requirements. .............................................................................................................. 15 
4.11.1 This tire and wheel application standard is based ......................................................................... 15 
4.11.2 Tire Lot Traceability Identification. ................................................................................................ 15 
4.11.3 Tire Conicity................................................................................................................................. 16 
4.11.4 Data Syntax Requirement. ........................................................................................................... 16 
4.11.5 Label Layout. ............................................................................................................................... 16 
5 Label Requirements ................................................................................................................................. 17 
5.1 Label Requirements. ............................................................................................................................ 17 
5.2 Label Anatomy. .................................................................................................................................... 17 
5.3 Label Size. ........................................................................................................................................... 18 
5.4 Color. ................................................................................................................................................... 18 
5.4.1 An alternative to color is the use of graphics .................................................................................. 19 
5.5 Verification/Error Proofing. ................................................................................................................... 19 
5.6 Printing. ............................................................................................................................................... 19 
5.7 Human Readable Content. ................................................................................................................... 19 
5.7.1 Human Readable Information for Data Matrix and QR Code. ......................................................... 19 
5.8 Supplier Logo or Trademarks. .............................................................................................................. 21 
5.9 Scanners/Imagers. ............................................................................................................................... 21 
5.9.1 Imagers have the capability to edit data within the device ............................................................... 21 
5.9.2 The following is an example of a simple script (Figure 22). ............................................................. 21 
6 Linear 1D Bar Codes ................................................................................................................................ 21 
6.1 Code 128 and Code 39 (Figure 23). ..................................................................................................... 21 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 3 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 3 of 59 
 
6.1.1 Code Densities and Dimensions for Code 128 and Code 39. ......................................................... 22 
6.2 Code 128 is a Four Ratio Bar Code ...................................................................................................... 22 
6.3 Code 39 is a Two Ratio Bar Code ........................................................................................................ 22 
6.4 Code 128 and Code 39 Quiet Zones. ................................................................................................... 22 
6.5 Code 128 and Code 39 Check Digits. ................................................................................................... 23 
6.5.1 Code 128. ...................................................................................................................................... 23 
6.5.2 Code 39. ........................................................................................................................................ 23 
6.6 Code 128 and Code 39 Print Quality. ................................................................................................... 23 
6.7 Code 128 and Code 39 Data Format and Data Length. ........................................................................ 23 
6.7.1 Data Format. ................................................................................................................................. 23 
6.7.2 Data Capacity. ............................................................................................................................... 23 
6.8 Human Readable Information for Code 128 and Code 39. .................................................................... 23 
7 Direct Part Marking (DPM)........................................................................................................................ 24 
7.1 Considerations. .................................................................................................................................... 24 
7.2 Direct Mark on Parts. ........................................................................................................................... 24 
7.3 DPM Human Readable Information. ..................................................................................................... 24 
7.4 Marking Methods. ................................................................................................................................ 24 
7.4.1 Scribing. ........................................................................................................................................ 24 
7.4.2 Dot-Peen. ...................................................................................................................................... 24 
7.4.3 Laser. ............................................................................................................................................ 25 
7.4.4 Inkjet. ............................................................................................................................................ 26 
8 Symbol (Bar Code) Quality Verification................................................................................................... 27 
8.1 General. ............................................................................................................................................... 27 
8.1.1 Verification testing SHALL be performed on labels and direct marked parts. .................................. 27 
8.2 Direct Part Mark Verification. ................................................................................................................ 27 
8.3 Label Performance Testing. ................................................................................................................. 27 
8.3.1 KCDS VER/TRA.. .......................................................................................................................... 27 
8.3.2 Other Bar Codes.. .......................................................................................................................... 27 
8.4 Bar Code Print Quality on Labels. ........................................................................................................ 27 
8.4.1 Data Matrix and QR Code Print Quality on Labels. ......................................................................... 27 
8.5 Label Placement. ................................................................................................................................. 27 
9 Additional Product Characteristics ......................................................................................................... 28 
9.1 General. ............................................................................................................................................... 28 
9.2 Data Identifier 7Q. ................................................................................................................................ 28 
9.2.1 Encodation of 7Q. Reference 4.5 for Data Syntax encodation methodology. Using the example from 
4.5 (Figure 29). ....................................................................................................................................... 28 
9.2.2 Parsing Algorithm. ......................................................................................................................... 28 
9.3 As-Built Label. ...................................................................................................................................... 28 
10 Radio Frequency Identification (RFID) .................................................................................................. 29 
10.1 When RFID tags become cost effective for identification of parts ........................................................ 29 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 4 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 4 of 59 
 
11 Validation ................................................................................................................................................ 29 
11.1 Responsibility. .................................................................................................................................... 29 
11.2 Form. ................................................................................................................................................. 29 
11.3 Process.............................................................................................................................................. 29 
12 Notes ....................................................................................................................................................... 29 
12.1 Glossary. ........................................................................................................................................... 29 
12.2 Acronyms, Abbreviations, and Symbols. ............................................................................................. 31 
13 Coding System ....................................................................................................................................... 33 
14 Release and Revisions ........................................................................................................................... 33 
Appendix A: Typical Data Identifiers .......................................................................................................... 34 
Appendix B: JULIAN Calendar.................................................................................................................... 35 
Appendix C: Vehicle Partitioning and Product Structure (VPPS) ............................................................. 37 
Appendix D: Data Matrix Reference Information ....................................................................................... 39 
Appendix E: GMW15862 Traceability, Verification and Part Identification Code Structure and Content 45 
     E1 Data Required for a Complete GMW15862 Trace Record ................................................................ 45 
     E2 GMW15862 Verification/Error Proofing and Product ID Structure .................................................. 48 
Appendix F: Appending/Adding Additional Data to the 2D Bar Code ....................................................... 49 
Appendix G: “As-Built” Label/Mark ............................................................................................................ 51 
Appendix H: GM1737 Defined Traceability and Verification/Error Proofing Code Structures ............... 544 
     H1 GM1737 Traceability Structure. (Phasing Out - See Traceability NOA) .......................................... 54 
     H2 GM1737 Enhanced Traceability Structure. (Phasing Out - See Traceability NOA) ......................... 55 
     H3 GM1737 Verification/Error Proofing Structure. (Phasing Out - See Traceability NOA) .................. 56 
Appendix J: Powertrain Traceability Format.............................................................................................. 57 
     J1 Traceability Format for Powertrain Released Components that are Scanned by the Vehicle 
Manufacturing Plant or VAA ....................................................................................................................... 57 
     J2 Traceability Format for Powertrain Released Components that are Scanned in the Powertrain 
Manufacturing Facility ................................................................................................................................ 59 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 5 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 5 of 59 
 
1 Introduction 
Note: Nothing in this standard supercedes applicable laws and regulations. 
Note: In the event of conflict between the English and domestic language, the English language shall take 
precedence. 
Note: In this document, the word shall is a requirement and the word should is a recommendation. 
1.1 Scope. This standard defines the bar code symbologies, data content, and label/mark layout for parts, 
components, assemblies, and modules used in the manufacturing of GM vehicles. This standard also defines 
the performance standards for printed bar code labels and Direct Part Marking (DPM). This document replaces 
GM1737 and EDS-A-2404, all regional unique, all plant unique, and all supplier unique requirements. 
1.2 New or Revised Parts, Components, Assemblies, and Modules with Linear Bar Codes. Section 6 
Linear one-dimensional (1D) Bar Codes SHALL NOT be used on new/revised scanned parts, components, 
assemblies or modules. 
1.3 New Sourced Parts, Components, Assemblies, and Modules. All new parts, components, assemblies, 
or modules SHALL comply with Sections 4 and 5 of this document (see Traceability Notice of Action (NOA)). 
1.4 Carryover Parts, Components, Assemblies, and Modules. Section 6, Linear 1D Bar Codes is allowed 
on carryover scanned parts, components, assemblies or modules (see Traceability NOA). 
1.5 Control Module Software SHALL comply with GMW4710. New data structure SHALL go into effect 
beginning with Global B Architecture (see Traceability NOA). Labels or DPM SHALL comply with this 
document. 
1.6 Reasons for Bar Code Scanning. 
1.6.1 Traceability. For the primary purpose of precisely identifying the vehicles (VIN/trace number) involved in 
a spill or potential field action. Product Engineering is responsible for defining which parts, components, 
assemblies, and modules require traceability. 
1.6.2 Verification/Error Proofing. Provide the capability to validate correct part/component/module/assembly. 
1.6.3 Part Identification. Provide the capability to identify part/component/module/assembly. 
2 References 
Note: Only the latest approved standards are applicable unless otherwise specified. 
2.1 External Standards/Specifications. 
AIAG-B-2 
ANSI MH10.8.2  
ISO/IEC 15416 
ISO/IEC 16388 
AIAG B-4 
ANSI X12.3 
ISO/IEC 15417 
ISO/IEC 18000-6C 
AIAG B-7 
FMVSR 49 CFR § 574.5 
ISO/IEC 15418 
ISO/IEC 18004 
AIAG B-11 
ISO/IEC 646 
ISO/IEC 15434 
NASA-STD-6002 
AIAG B-17 
ISO/IEC 15415 
ISO/IEC 16022 
 
Note: AIAG standards are available from www.aiag.org. 
Note: The ANSI MH10.8.2 standard is subject to frequent updates and is maintained as a DRAFT document at 
www.autoid.org. 
2.2 GM Standards/Specifications. 
GMW4710 
GMW14574 
 
 
GMW14089 
GMW15049 
 
 
GMW14573 
GMW16331 
 
 
2.3 Additional References. 
� 
AIM DPM Quality Guideline (available from www.aimglobal.org) 
� 
CG2503 
� 
Dun and Bradstreet; available from http://www.dnb.com 
� 
Global Quality Requirement (GQR) 120.57 
� 
Labels and Literature web site: http://gmna1.gm.com/eng/labels/barcodes.html. 
� 
Traceability NOA QLT/ELT 196A 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 6 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 6 of 59 
 
� 
Vehicle Partitioning and Product Structure (VPPS) managed compressed code; available from 
http://gmna1.gm.com/eng/grc/vpps/index.html 
3 Parts, Components, Assemblies or Modules Requiring Bar Codes. 
3.1 Parts, components, assemblies and modules identified as requiring Traceability or Verification per 
GMW15049 Key Characteristics Designation System  (KCDS), SHALL be encoded in a Data Matrix or 
optionally Quick Response (QR) Code two-dimensional (2D) symbol (bar code). 
3.1.1 Process Owner Responsibility for Bar Code Requirements Communication. See guidelines in 
Table 1. 
 
Table 1: Communication Responsibility Guidelines 
 
Owner 
Trigger 
Methodology 
Traceability 
KCDS 
Template 
TRA Note 1 code in GPDS Note 2 
Verification - Engineering 
KCDS 
Template 
VER Note 3 code in GPDS 
Verification - Error Proofing 
ME Note 4 
PFMEA Note 5 
PRTS Note 6 
Part ID 
PE Note 7 
DFMEA Note 8 
SOR Note 9 
VPPS Note 10 
KCDS 
Template 
SOR 
Note 1: TRA – Traceability 
Note 2: GPDS – Global Product Description System 
Note 3: VER – Verification 
Note 4: ME – Manufacturing Engineering 
Note 5: PFMEA – Process Failure Mode and Effects Analysis 
Note 6: PRTS – Problem Reporting and Tracking System 
Note 7: PE – Process Engineering 
Note 8: DFMEA – Design Failure Mode Effects Analysis 
Note 9: SOR - Statement of Requirements 
Note 10: VPPS – Vehicle Partitioning and Product Structure. See Appendix C. 
 
3.2 Traceability. Sometimes referred to as genealogy, history or birth record. Traceability must answer the 
following four minimum questions: 
� 
The what? 
� 
The who? 
� 
The which? 
� 
The when? 
See Figure 1 example of Traceability with Serial Number and Figure 2 example Traceability with Lot/Batch ID 
of Label/Marks. 
 
 
Figure 1: Illustration of a Serialized Traceability Label/Mark 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 7 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 7 of 59 
 
 
Figure 2: Illustration of a Lot/Batch Traceability Label/Mark 
 
3.2.1 Within this standard, the answer to these four questions is as follows: 
� 
The "what" is the GM assigned 8-digit part number. 
� 
The "who" is the manufacturing or assembly site’s Data Universal Numbering System (DUNS) 
identification. 
� 
The "which" is the GM defined traceability code structure defined in 3.2.3. 
� 
The "when" is the actual year and Julian date of manufacture or assembly (Appendix B) and is contained 
within the GM defined traceability code structure. 
3.2.2 Trace Record. To constitute a trace record, traceability SHALL require the GM assigned 14-character 
character compressed VPPS code, the 8-character part number, the 9-character DUNS ID of manufacturing or 
assembly source site, plus the 16 character GM defined trace code. 
3.2.3 GM Defined Traceability Code Structure. The GM defined traceability code structure (Appendix E) is 
16 data characters, excluding the Data Identifier (DI) as illustrated in Figure 3. See Appendix A for a list of 
common DIs. 
 
 
Figure 3: GM Defined Trace Serial Number or Lot/Batch Identification Structure 
 
Where: 
L is line/machine/test stand identification assigned by supplier. 
S is shift identification assigned by supplier. 
YY is last two digits of actual production/assembly year (example 2007 = 07) not model year. 
DDD is Julian actual day of the production/assembly year (example 282 = 09OCT) (See Appendix B). 
For Serial Number - A2B4C6000 is supplier assigned unique serial number (9 characters right pad with 
zeros), or 
For lot/batch - @2B4C6000 is the format where the “@” is a fixed character and “2B4CD68E” is the supplier 
assigned lot/batch code (8 characters right pad with zeros). 
3.2.3.1 For Powertrain Released Components the last nine characters are defined according to the layouts 
in Appendix J. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 8 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 8 of 59 
 
3.2.4 Data Fields. The bar code SHALL contain four data fields (Table 2) with their associated Data Identifiers 
(DI) as shown. 
 
Table 2: Bar Code Data Fields Note 1 
Data 
Definition 
Data 
Characteristics 
DI 
Encodation (with DI) 
GM assigned VPPS Compressed 
Code 
14 alphanumeric 
Y 
Y0000000000000X 
GM Part 
Number 
8 Numeric 
P 
********* 
Manufacturing or Assembly Site 
DUNS 
9 Numeric 
12V 
12V********* 
GM Defined 
Trace Code or Lot/Batch Code 
16 alphanumeric 
T 
TLSYYDDDA2B4C6D8E or 
TLSYYDDD@2B4C6000 
or 
For Powertrain released components see 
Appendix J. 
Note 1: Julian date of manufacture or assembly is contained within the GM Defined Trace Code. See Appendix B Julian calendar. 
 
3.2.5 BIG RULE: Data Identifiers SHALL Be Used. 
******* For all bar codes, including those that do not contain GM data that may be on a part component, 
assembly, or module. If a bar code is visible, it could get scanned in error. 
******* To identify a single data element contained in a 1D or 2D bar code, ISO/IEC 15434 Data Syntax 
Standard SHALL NOT be used. 
******* To identify multiple individual data fields contained in a 2D symbol, ISO/IEC 15434 Data Syntax 
Standard SHALL be used. 
3.3 Verification/Error Proofing. Parts, components, assemblies and modules, identified by Manufacturing 
Engineering through the Process Failure Mode and Effects Analysis (PFMEA) as sensitive to selection error, 
SHALL require a bar code. The Error Proofing Manufacturing Engineer will initiate a Change Request (CR) to 
communicate to Product Engineering the need to add the bar code. Figure 4 illustrates a Verification/Error 
Proofing Label. 
 
 
Figure 4: Illustration of a Verification/Error Proofing Label/Mark 
 
Note: If GM defined traceability is not used, then the Julian date of manufacture or assembly SHALL be used 
as shown in Table 3. 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 9 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 9 of 59 
 
 
Table 3: Validation/Error Proofing Note 1 
Data Definition 
Data 
Characteristics 
DI 
Encodation (with DI) 
GM assigned VPPS 
Compressed Code 
14 alphanumeric 
Y 
Y0000000000000X 
GM Part 
Number 
8 Numeric 
P 
********* 
Manufacturing or Assembly 
Site DUNS 
9 Numeric 
12V 
12V********* 
Julian date of 
manufacture or assembly 
5 Numeric 
4D 
4DYYDDD 
OPTIONAL 
GM Defined 
Trace Code or Lot/Batch Code 
16 alphanumeric 
T 
TLSYYDDDA2B4C6D8E or 
TLSYYDDD@2B4C6000 or 
For Powertrain released components see 
Appendix J. 
Note 1: Julian date of manufacture or assembly is contained within the GM Defined Trace Code. See Appendix B Julian calendar. 
 
3.4 Product Identification. A product identification label or DPM consists of the same data fields (Table 4) as 
traceability with the GM defined traceability data being optional. Figure 5 illustrates a Product Identification 
Label. 
 
Table 4: Product Identification Note 1 
Data Definition 
Data 
Characteristics 
DI 
Encodation (with DI) 
GM assigned VPPS 
Compressed Code 
14 alphanumeric 
Y 
Y0000000000000X 
GM Part Number 
8 Numeric 
P 
********* 
Manufacturing or Assembly 
Site DUNS 
9 Numeric 
12V 
12V********* 
Julian date of manufacture 
or assembly 
5 Numeric 
4D 
4DYYDDD 
OPTIONAL GM Defined 
Trace Code 
16 alphanumeric 
T 
TLSYYDDDA2B4C6D8E or 
TLSYYDDD@2B4C6000 
or 
For Powertrain released components see 
Appendix J. 
Note 1: Julian date of manufacture or assembly is contained within the GM Defined Trace Code. See Appendix B Julian calendar. If GM 
defined traceability is not used, then the Julian date of manufacture or assembly SHALL be used. 
 
 
Figure 5: Illustration of a Product Identification Label/Mark 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 10 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 10 of 59 
 
4 Bar Code Symbologies and Encodation 
4.1 Bar Code Symbology. The information in this section applies to the two-dimensional (2D) bar code 
symbologies recommended in this Standard. Data Matrix (preferred) ISO/IEC 16022, Symbology Specification 
(Appendix D) or with trading partner/GM approval QR Code. ISO/IEC 18004, Symbology Specification SHALL 
be used (Figure 6). 
 
 
Figure 6: Two-Dimensional (2D) Symbologies Data Matrix (Preferred) and QR Code 
 
4.1.1 The Case for 2D. 
� 
Consumes less space (label cost reduction). 
� 
Built-in error correction (100% data recovery with 15 to 20% damage to symbol). 
� 
Large data capacity (hundreds of data characters possible). 
� 
May be printed on label or direct mark (laser etch, pin stamped, ink jet, etc.) 
� 
Scan device is an imager (camera based technology) no moving parts. 
� 
Imagers are less costly than laser based technologies. 
� 
Interchangeability with ISO/IEC 18000-6C passive Radio Frequency Identification (RFID) technology with 
user memory, same Data Syntax structure. Using AIAG B-11 the data will look the same to the Information 
Technology (IT) system. 
4.1.2 Allowable Data Characters. The ISO/IEC 646 American Standard Code for Information Interchange 
(ASCII) character set for this standard SHALL consist of the following: 
� 
Uppercase alpha characters 
� 
Numbers 0 to 9 
� 
Dash (-) 
� 
Period (.) 
� 
Underscore (_) 
� 
Space ( ) 
Note: The ASCII characters dollar sign ($), forward slash (/), plus (+), and percent (%) are not recommended 
for use with Code 39 and therefore SHOULD be avoided in data fields that may be encoded in both linear 1D 
and 2D symbols. This recommendation is based on the potential of Code 39 character substitution errors for 
these specific characters. 
4.1.2.1 The full ASCII character set SHALL NOT be used for data. 
4.1.2.2 The full ASCII character set is allowed in the Message Header, Message Trailer, and Field Separator, 
as defined by ISO/IEC 15434 for High Capacity Media (Data Syntax). These specific ASCII characters are 
termed "non-printable control characters" and require different techniques to encode, dependent upon the 
software and printer being used (Table 5). 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 11 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 11 of 59 
 
Table 5: ASCII ISO/IEC 646 Characters as Used in ISO/IEC 15434 - Data Syntax Structure 
Termed 
ASCII ISO/IEC 646 Characters 
Decimal 
Hex 
[ (left bracket) 
[ 
91 
5B 
) (right parenthesis) 
) 
41 
29 
> (greater than) 
> 
62 
3E 
Below are the Non-Printable ASCII control characters 
End Of Transmission 
EOT 
04 
04 
Group Separator 
(Data field separator) 
G
S 
29 
1D 
Record Separator 
R
S 
30 
1E 
 
4.1.3 Data Fields and Data Identifiers. A data field SHALL consist of a Data Identifier (DI) followed by the 
associated data. Data Identifiers (DI) complying with ISO/IEC 15418 (ANSI MH 10.8.2) SHALL be used. All 
data can be variable length unless restricted by this standard. When used, the fields in Table 6 SHALL NOT 
exceed the length shown. 
 
Table 6: Restricted Length Data Fields 
Data Identifier DI 
Description 
Maximum Data Length 
Maximum Total Field 
Length 
12V 
Manufacturing or Assembly Site DUNS 
9 
12 
4D 
Julian date of manufacture or assembly in the 
form of YYDDD 
5 
7 
I 
Vehicle Identification Number (VIN) 
17 
18 
P 
GM-Assigned part number 
8 
9 
S 
Product Serial Number 
9 
10 
T 
GM-Assigned Traceability Code 
16 
17 
20P 
GM-Assigned Verification/Error Proofing Code 
6 
9 
20T 
GM-Assigned Verification and Traceability Code 
16 
19 
21T 
GM-Assigned Verification and Traceability Code 
16 
19 
Y 
GM assigned Compressed VPPS code 
14 
15 
 
******* The 2D symbologies, Data Matrix or QR Code, may contain multiple data fields. When the fields in 
Table 6 are encoded in a 2D symbology, they SHALL NOT exceed the maximum character lengths. 
4.2 Data Matrix and QR Code Densities and Dimensions. The 2D symbol density (size) is determined by 
many factors including the marking area available, the method used to create the mark, the surface type, the 
environment, and the imaging device(s) used. 
4.2.1 A particular symbol size depends on the amount and type of data encoded, element/cell size, and error 
correction level. 
4.2.2 Within the constraints of the available marking area, GM SHALL concur with the supplier on the 2D 
element/cell size to be used. 
4.2.3 To allow for the best possible imager performance, use the largest practical size element/cell dimension 
that fits within the available area. 
4.2.4 BIG RULE: Make the bar code (1D or 2D) symbol as large as practical not as small as possible. As 
symbol element/cell size decrease, printing/marking and scanning/imaging issues increase exponentially. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 12 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 12 of 59 
 
4.2.4.1 Depending on available area, the element/cell size for printed label SHOULD be 0.51 mm (0.02 in) or 
larger and SHALL NOT be smaller than 0.381 mm (0.015 in). 
Note: If space constraints dictate a smaller element/cell size, then it SHALL be agreed to by GM and Supplier 
and tested using the GM specified imager. 
4.3 Data Matrix and QR Code Quiet Zones. SHALL include a quiet zone around the entire perimeter (all four 
sides). 
Failure 
to 
comply 
with 
the 
minimum 
requirement 
may 
result 
in 
a 
 
non-decodable symbol. 
4.3.1 Data Matrix quiet zone is equal to two times the symbol cell dimension (Figure 7). 
 
 
Figure 7: Quiet Zone Requirement for Data Matrix 
 
4.3.2 QR Code quiet zone is equal to four (4) times the symbol cell dimension (Figure 8). 
 
 
Figure 8: Quiet Zone Requirement for QR Code 
 
4.4 Error Correction Levels. 
4.4.1 Data Matrix Error Correction Code (ECC) Level. ECC 200 SHALL be used on printed labels and Direct 
Part Marking (DPM). 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 13 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 13 of 59 
 
4.4.2 QR Code Error Correction (EC) Levels. Error correction Level M is recommended in this standard 
(Table 7). 
 
Table 7: Error Correction Levels for QR Code 
EC Level 
% EC 
Description 
L 
7% 
Smallest possible symbol size. Requires high level of print/mark quality 
M 
15% 
RECOMMENDED 
Good compromise between small size and level of Error Correction (EC) 
Q 
25% 
Suitable for critical or poor print/mark quality applications providing a high level of EC 
H 
30% 
Maximum 
 
4.5 Data Matrix and QR Code Data Format. 
BIG RULE: When multiple fields of data are to be encoded in a 2D symbol, they SHALL be formatted as 
defined by ISO/IEC 15434 (Data Syntax) and SHALL use format 06 (ANSI MH 10.8.2 Data Identifiers). 
When a single data field is to be encoded in a 2D symbol, it SHALL use ANSI MH 10.8.2 Data Identifier 
only and SHALL prefix the encoded data. 
4.5.1 Data Syntax ISO/IEC 15434. 
a. The Compliance Header [)>R
S SHALL be followed by the Format Header 06G
S. 
a. Each data field SHALL be separated by G
S, except for the last data field. 
b. The last data field in a Format Envelope SHALL be the Format Trailer R
S. 
c. The last format envelope in the message SHALL be followed by the Message Trailer EOT. 
d. Encoded data format looks like this: 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
STLSYYDDDA2B4C6000S
EOT. 
4.5.2 Compliance Header. Compliance Format header 06 requires the use of a Data Identifier (DI) for every 
data field. The Compliance Header defines the data field separators to be G
S (Table 8). 
 
Table 8: Data Syntax Format and DIs Note 1 
Header 
DI 
Information Content 
Data Field 
Separator 
Trailer 
[)>R
S06G
S 
Y 
GM Defined VPPS 
0000000000000X 
G
S
 
 
 
P 
GM Defined Part Number 
12345678 
G
S 
 
 
12V 
Manufacturing or assembly site DUNS 
number 
********* 
G
S 
 
 
T 
LSYYDDDA2B4C6D8E or 
LSYYDDD@2B4C6000 or 
For Powertrain released components see 
Appendix J. 
 
R
S
EOT 
Note 1: A single data field SHALL use the appropriate Data Identifier followed by its data. 
 
4.5.3 Data Matrix Header and Trailer 06 Macro BIG RULE: Use 06 Macro whenever multiple fields of 
data need to be encoded. The 06 Macro saves 8 alphanumeric characters in the encodation of 
ISO/IEC 15434 Data Syntax Standard. Data Matrix Data Matrix ISO/IEC 18004 provides a means of 
abbreviating the header and trailer into one character. This feature was created to reduce the number of 
symbol characters needed to encode data in a symbol using the ISO/IEC 15434 Data Syntax Standard. The 06 
Macro character applies only when in the first symbol character position. The header will be transmitted as a 
prefix to the data stream and the trailer will be transmitted as a suffix to the data stream (Table 9). 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 14 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 14 of 59 
 
Table 9: Macro Function for Data Matrix 
Macro 
Codeword 
Name 
Interpretation 
 
 
Header 
Trailer 
237 
06 Macro 
[)>R
S06G
S 
R
S
EOT 
 
4.6 Single Data Field Encodation. When only a single data element, e.g., Vehicle Identification Number (VIN) 
is to be encoded in Data Matrix or QR Code, an ANSI MH 10.8.2 Data Identifier SHALL be used (Figure 9). 
The DI SHALL be the first character(s) preceding the data. 
There is no header or trailer. 
If the Human Readable Information (HRI) is to include the DI, it SHOULD be enclosed in parenthesis 
(DI)xxxdataxxx. 
Note: Parenthesis SHALL NOT be encoded in the 2D symbol. 
 
 
Figure 9: Example of Single Data Element Encoded (17 Character VIN) With and Without 
Data Identifier (HRI) 
 
4.7 Rectangular Data Matrix. Although square symbols are more efficient, rectangular symbols may be 
generated when the space available will not accommodate a square, particularly when the part is cylindrical 
(Figure 10). 
 
 
Figure 10: Example of Rectangular Data Matrix 
 
4.7.1 Curve Surfaces. For labeling/marking and reading, flat surfaces are preferred over curved surfaces. The 
curvature of an item may prohibit proper labeling or marking and may distort the code to the point that it cannot 
be decoded. If the label or mark is on a round/curved surface, the symbol height SHOULD be < 16% of the 
part’s diameter (Figure 11). 
 
 
Figure 11: Guideline for Label or DPM on a Curved Surface 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 15 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 15 of 59 
 
4.8 Long Range Scanning. Data Matrix and QR Code codes are scalable. By increasing cell/element size 
and using the appropriate imager configuration, distances of 3 m can be obtained. Figure 12 is an example of 
a Data Matrix symbol using 3 mm (0.120 in) element/cell measuring 70.3 x 70.3 mm scanned at 1.4 m (4.5 ft) 
using the current Global Manufacturing and Quality (GM&Q) standard handheld imager. 
 
 
Note: This Data Matrix Symbol Scanned at 1.4 m using GM Standard Hand Held Imager. 
Figure 12: Scaling Cell Size (3 mm) 
 
4.9 VIN. Reference GMW14574 for VIN specifications, AIAG B-7 Vehicle Emission Configuration Label 
Standard. Label, AIAG B-2 Vehicle ID Number (VIN) Label Application Standard and DPM bar code 
symbologies SHALL conform to requirements within GMW15862. VIN is quite often a stand-alone bar code 
such as a VIN plate or on an emission label. (See Figure 9 for an example.) 
4.10 Labeling Electronic Modules. Reference GMW4710 for method to program electronic modules with 
traceability information. Exterior labeling of electronic modules SHALL conform to traceability structure as 
detailed in 3.2. 
4.11 Tire Labeling Requirements. AIAG B-11 standard provides the guideline for the printing and placement 
of tire and wheel identification bar code labels and read/write Radio Frequency Identification (RFID) Tags. This 
standard is designed to help automate the collection of tire and wheel information and the mounting and 
assembly process of tires and wheels with vehicles in the GM environment. The standard provides information 
about the manufacturer, tire and wheel size, type, and additional optional information as outlined in this 
standard and as agreed to by the supplier and GM. 
4.11.1 This tire and wheel application standard is based on the AIAG B-4 Parts Identification and Tracking 
Standard, with additional information specific to the printing, programming, and placement of tire and wheel 
identification bar code labels and RFID Tags. 
4.11.2 Tire Lot Traceability Identification. When identifying tires, the data field SHALL consist of the Data 
Identifier "21S" followed by the full Department of Transportation (DOT) code (Federal Motor Vehicle Safety 
Regulation (FMVSR) 49 CFR § 574.5), which is a 12-character coding structure defined by DOT as follows 
(Figure 13): 
� 
The first two characters define the manufacturer by plant. 
� 
Characters 3 and 4 identify the tire size. Characters 3 and 4 may also be defined by the tire manufacturer. 
� 
Characters 5, 6, 7, and 8 are optional for the tire manufacturer. If the tire manufacturer uses a 3-digit 
option code, then this SHALL be padded with a leading "underscore" character (5F HEX or 95 DEC). 
Definition of the option code is left up to the tire Original Equipment Manufacturer (OEM) and GM. 
� 
Characters 9, 10, 11, and 12 are date of manufacture (2-digit week/2-digit year). 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 16 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 16 of 59 
 
 
 
Figure 13: DOT Tire Lot Traceability Data Structure 
 
4.11.3 Tire Conicity. GM Tire Engineering group may sort tires based on an engineering value termed 
"conicity" (Table 10). The DI assigned to conicity and selected by tire engineering is 5N01. The "5Nxx" set of 
DIs are assigned to AIAG and are managed and published at http://www.aiag.org. 
 
Table 10: Conicity Values To Be Used With DI 5N01 
Qualifier 
Definition 
A 
No split 
B 
+ 
C 
- 
D 
Low + 
E 
Hi + 
F 
Low - 
G 
Hi - 
H 
Not available 
 
4.11.4 Data Syntax Requirement. Data encoded in the 2D bar code for tires SHALL BE as shown in 
Table 11. 
 
Table 11: Data Syntax Bar Code EncodationNote1 
Header 
DI 
Information Content 
Data Field 
Separator 
Trailer 
[)>R
S06G
S 
Y 
GM Defined VPPS 
00000000000000 
G
S
 
 
 
P 
GM defined part number 12345678 
G
S 
 
 
12V 
Manufacturing or assembly site DUNs 
number ********* 
G
S 
 
 
21S 
DOT Defined Trace Code Example 
W2CU_XLT2508 
G
S 
 
 
5N01 
Conicity Value Example B 
G
S 
R
S
EOT 
Note 1: Encoded data format looks like this: [)>RS06GS*********GS12V*********GS21SW2CU_XLT2508GS5N01B RS EOT 
 
4.11.5 Label Layout. Tire label SHALL conform to print quality and font rules detailed in Section 5. Bar code 
cell/element SHALL BE a minimum of 0.51 mm (0.02 in). Label layout SHOULD conform to Figure 14. 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 17 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 17 of 59 
 
 
Figure 14: Tire Label Layout (Label Size Approximately 27.9 x 27.9 mm (1.1 x 1.1 in) 
 
5 Label Requirements 
5.1 Label Requirements. This section defines the label requirements for part/component labels that contain 
only bar code information. For labels that contain additional information, the label design needs to meet the 
design requirements of GMW14089, in addition to those in this standard. 
Labels SHOULD NOT be visible to the customer after component/assembly installation. 
5.2 Label Anatomy. Figure 15 shows an example of features that can be shown on a Traceability Label/Mark. 
 
 
Figure 15: Anatomy of a Label/Mark 
 
Features: 
� 
Black print on white label. 
� 
2D Bar code (Data Matrix or QR Code) 
� 
HRI SHALL BE uppercase (capital letters) Arial Narrow BOLD or Helvetica Condensed or equivalent. 
� 
Part number with the last four digits emphasized with larger font 12345678. 
� 
GM defined Vehicle Partitioning and Product Structure (VPPS). 
� 
DUNS identification of manufacturing or assembly site. 
� 
Julian date of manufacture or assembly. 
� 
GM defined trace code. 
� 
Provision to use graphic indicators; e.g., Left/Right Hand part, Color alternative, e.g., � = RED; 
T = YELLOW; � = GREEN; etc. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 18 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 18 of 59 
 
5.3 Label Size. Label size is dictated by the available area and shape (e.g., curved surface). Available area for 
a label will dictate size of the bar code, the label and HRI font size. 
Note: Label examples in this document are for guidance only (Figures 16 thru 20). 
 
 
Figure 16: Example Steering Column Airbag Die-cut Label with Multiple Languages 
 
 
Note: The multiple Data Matrix symbols which points out why every bar code on a part SHALL comply with DI and Data Syntax standards. 
Figure 17: Roof Airbag Sensor 
 
5.4 Color. Best imager/scanner performance is achieved with white background and black print or black 
background and white print (reverse image). 
Color Issues: 
� 
Color adds cost. 
� 
10%+ of the male population has color disabilities and most are unaware. 
� 
Degrades bar code contrast which reduces scanning distance at best or no scan at worse. 
� 
Text contrast readability degrades - frustrates the user. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 19 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 19 of 59 
 
� 
Easy to break process. Do you not ship, if correct color label unavailable? 
� 
Requires either changing stock in printers or setting up multiple printers. 
� 
Increase costs to inventory materials. 
� 
Introduces possible error (e.g., wrong label color stock selected). 
� 
Specification for color. What shade of green, blue, yellow, white or black would you like? 
5.4.1 An alternative to color is the use of graphics, e.g., diamond, heart, spade, stripes, etc. In some cases 
(e.g., cables), it may be required to use color and SHOULD use a color stripe vs. flood coating to avoiding the 
creation of scanning and human reading issues. 
 
 
Figure 18: Use of Graphics (Sunbursts) in Lieu of Color 
 
 
Figure 19: Use of Graphics (Shamrocks) and Position on Label in Lieu of Color for Error Proofing 
 
 
Figure 20: Illustration of Background Effect on Bar Code and HRI (Color Not Permitted) 
 
5.5 Verification/Error Proofing. Special identification is needed for parts of similar appearance, if there is a 
risk of false selection by operator (examples, L, R,� T, �, �). To avoid the need for language translations, 
words SHOULD not be used (Figure 21). Note previous concerns on color (5.4). 
 
 
Figure 21: Example Use of Graphic for Error Proofing (Left hand part) 
 
5.6 Printing. Printing with thermal transfer printers SHALL use resin or resin/wax compound based ribbon. 
Use of wax based ribbons SHOULD be avoided for many reasons including smearing, scratch resistance, and 
solvents. Printed labels SHALL meet minimum Grade C at GM point of scan as detailed in Section 8. 
5.7 Human Readable Content. 
5.7.1 Human Readable Information for Data Matrix and QR Code. Because 2D symbols are capable of 
encoding hundreds of data characters, an HRI of the data characters may not be practical. As an alternative, 
descriptive text rather than literal text may accompany the symbol. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 20 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 20 of 59 
 
******* An HRI of the message may be printed anywhere in the area surrounding the symbol but SHOULD 
NOT interfere with the symbol or the quiet zones. 
******* Data Identifier SHOULD NOT appear in the HRI. Data Identifier SHALL be encoded in the bar code. 
******* The Message Header, Data Field Separator, and Message Trailer characters SHALL NOT appear in 
the HRI. 
******* The HRI SHALL appear adjacent to the 2D symbol and SHALL be consistent on any part or unit pack. 
5.7.1.5 Symbol Layout for Data Matrix and QR Code. GM and the supplier SHOULD construct a layout most 
suitable for the part, component, assembly, or module. However, it SHOULD be noted that for individual part 
marking, the location and orientation of the symbol may be critical to applications using automated fixed mount 
scanners. Examples shown in this document are for illustration only and are not to be construed as 
specifications. 
******* Font Specification. 
*******.1 BIG RULE: Make the font as large as practical; not as small as possible. As font size decreases, 
printing/marking and human reading issues increase exponentially. 
*******.2 Font SHALL BE uppercase (capital letters) Arial Narrow BOLD or Helvetica Condensed or equivalent. 
All reference to font type and size is based on MS Office fonts for reference purpose only. Font size is based 
on a system termed points (pt). 
*******.3 These reference fonts were selected based on readability and space efficiency. Actual font used by 
various printing technologies and DPM marking equipment vary widely. 
Note: Tables 12 and 13 are guidelines and are impacted by the available area to print/mark and the 
technology used to create the print/mark. 
 
Table 12: Illustration of Font Size 
MS Office 
Pt Size 
SAMPLE RESULTS 
Arial Narrow Bold 
8 
A2B4C6D8 
10 
A2B4C6D8 
12 
A2B4C6D8 
14 
A2B4C6D8 
16 
A2B4C6D8 
18 
A2B4C68 
20 
A2B4C6D8 
24 
A2B4C6D8 
******* Enhanced GM Part Number Text. To facilitate quick HRI, product identification, and/or error proofing, 
the last four digits of the GM assigned part number SHALL BE printed/marked in a larger font size as 
illustrated in Table 13. 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 21 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 21 of 59 
 
Table 13: Enhanced GM Part Number Font Guideline 
MS Office 
Pt Size 
First 4 numbers/Last 4 numbers 
EXAMPLES 
Arial Narrow Bold 
8/14 
12345678 
12/18 
12345678 
14/24 
12345678 
18/28 
12345678 
 
5.8 Supplier Logo or Trademarks. Supplier SHALL reference GMW16331 for branding requirements and 
policies. 
5.9 Scanners/Imagers. Bar Code scanners/imagers, either handheld or stationary mounted, used in GM 
plants SHALL meet the requirements of and have their Bill of Material (BOM) included in GM&Q IT Standards. 
5.9.1 Imagers have the capability to edit data within the device by the use of scripts which may be encoded 
into a special 2D programming symbol. Scripts can add, delete, parse, and/or modify data and may include 
prefix/suffix characters. 
5.9.2 The following is an example of a simple script (Figure 22). 
Note: This programming is independent of the interface. Scanner must be programmed for specific interface: 
serial, Universal Serial Bus (USB), etc., before scanning this configuration bar code. This script is based on 
Honeywell Products 4800i/4820i imagers. 
 
 
Figure 22: Example RS232 Script 
 
This is the encoded script which looks like this: 
SUFBK2990D;DFMBK30099999999FE32FE30FE54F7F503F100;DFMBK30099999999FE54F7F501F100. 
******* Strips the data identifiers out of Code 128, Code 3/9, Data Matrix, and QR bar codes, also adds a CR 
suffix for all Symbologies. 
*******.1 This will work for RS232-serial, Keyboard wedge, or USB Keyboard interfaces. 
6 Linear 1D Bar Codes 
Note: GM is phasing out 1D bar codes and phasing in 2D symbologies with new part releases. See 
Traceability NOA. 
6.1 Code 128 and Code 39 (Figure 23). For linear (1D) Symbologies, ISO/IEC 15417 Bar Code Symbology 
Specification - Code 128 or ISO/IEC 16388 Bar Code Symbology Specification - Code 39 SHALL be used. 
Reference AIAG B4 for additional linear 1D bar code details. UCC/EAN Code 128 Symbology SHALL NOT be 
used. Code 128 is preferred over Code 39 principally because of space efficiency and built-in check digit. 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 22 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 22 of 59 
 
 
Note 1: Code 128 and Code 39 SHALL NOT be used for Direct Part Marking. 
Note 2: Code 128 is typically 25% shorter than Code 39 given the same data and X-Dimension. 
Figure 23: Codes 128 and 39 
 
6.1.1 Code Densities and Dimensions for Code 128 and Code 39. Bar height for both symbologies can be 
varied to suit the particular application requirements. The minimum bar height SHALL be 6.4 mm (0.25 in) or 
15% of the bar code length whichever is greater, including quiet zone, and SHOULD not exceed 13 mm 
(0.5 in). 
6.2 Code 128 is a Four Ratio Bar Code which is automatically determined via the Symbology Standard. 
Each Code 128 data character consists of 1X, 2X, 3X, and 4X elements in width (bars and spaces). For each 
Code 128 symbol, the average width of the 1X narrow element SHOULD be within the range of 0.191 mm 
(0.0075 in) to 0.382 mm (0.0150 in). Code 128 has three modes; the labeling software or the printer SHALL 
determine which mode to use and when to switch modes. 
Historically, manual intervention results in Code 128 space efficiency being sub-optimized. Base specification 
for Code 128: 
� 
X-dimension (narrow bar). 
� 
Height of symbol. 
6.3 Code 39 is a Two Ratio Bar Code and the Ratio SHALL be Specified. The significant parameters of 
Code 39 symbol are the average width of the narrow elements (bars and spaces) and the average ratio of wide 
elements to narrow elements. For each Code 39 symbol, the average width of the narrow elements SHALL be 
within the range of 0.191 mm (0.0075 in) to 0.382 mm (0.0150 in). The ratio of the wide elements to the narrow 
elements SHOULD be 3:1. The measured ratio SHALL be between 2.8:1 and 3:1. 
Note: Ratio has been the most common specification error. If the ratio falls below 2.8:1, the scanner may 
incorrectly decode the data resulting in character substitution errors. 
Base specification for Code 39: 
� 
X-dimension (narrow bar). 
� 
Ratio SHALL be in the range of 2.8:1 to 3:1. 
� 
Height of symbol. 
6.4 Code 128 and Code 39 Quiet Zones. Each of the leading and trailing quiet zones for a Code 128 and 
Code 39 symbol SHOULD be 6.4 mm (0.25 in) and SHALL be a minimum of ten (10) times the width of the 
narrow element (Figure 24). 
 
 
Figure 24: 1D Quiet Zone and Height Requirement 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 23 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 23 of 59 
 
6.5 Code 128 and Code 39 Check Digits. 
6.5.1 Code 128. Includes a Built-in Check Digit, per the Symbology Standard, as the Last Character 
Before the Stop Character. The check digit SHALL NOT be shown in the HRI and it generally is not 
transmitted by the decoder/reader. 
6.5.2 Code 39. Check Digits SHALL NOT be used in Code 39 Symbols. 
6.6 Code 128 and Code 39 Print Quality. The ISO/IEC 15416 Bar Code Print Quality Test Specification - 
Linear Symbols SHALL be used to determine Code 128 and Code 39 symbol print quality. The minimum 
symbol grade SHALL be 2.0/05/660 at GM point of scan where: 
� 
Minimum print quality grade = 2.0 (C). 
� 
Measurement aperture = 0.12 mm (0.005 in). 
� 
Inspection wavelength = 660 nm (nanometers) + 10 nm. 
� 
The above symbol quality and measurement parameters ensure scannability over a broad range of 
scanning environments. 
Note: Previous AIAG standards specified an inspection wavelength of 900 nm to accommodate existing 
infrared scanners. In most cases, compliance at 900 nm is an indicator of compliance at 660 nm. When 
discrepancies occur, measurements SHALL be made at 660 nm. 
6.7 Code 128 and Code 39 Data Format and Data Length. 
6.7.1 Data Format. Data in a compliant symbol SHOULD consist of the appropriate ANSI MH10.8.2 Data 
Identifier followed by user data. Figure 25 is consistent with not displaying the DI in the HRI for GM parts, 
components, assemblies, or module labels. 
 
 
Figure 25: Example of VPPS Code, Part Number, DUNS and GM Defined Trace Code (Code 128) 
 
6.7.2 Data Capacity. A Code 128 or a Code 39 symbol SHOULD NOT exceed 20 characters including the 
data identifier. However, available marking space may limit the possible data length to fewer data characters. 
6.8 Human Readable Information for Code 128 and Code 39. The HRI in (a Code 128 or Code 39 symbol) 
SHOULD be printed. When printed, the HRI: 
� 
SHALL represent all of the encoded information. 
� 
SHOULD be consistently placed directly above or below the Code 128 or Code 39 symbol. 
� 
SHOULD display the Data Identifier in parentheses ( ) when the DI is part of the HRI. 
� 
SHALL NOT display the start or stop characters or check digit. 
� 
SHALL be upper case alphanumeric Arial Narrow Bold, Helvetica Condensed or equivalent. 
� 
The parentheses used in the HRI to separate the data identifier from the user information SHALL NOT be 
encoded in the symbol. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 24 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 24 of 59 
 
7 Direct Part Marking (DPM) 
This portion of the standard describes general guidelines for Direct Part Marking (DPM), factors to consider, 
and how to select the most appropriate DPM technique for a given application. Symbologies for DPM SHALL 
BE Data Matrix or QR Code. Reference AIAG B-17 for more details and NASA-STD-6002C - Applying Data 
Matrix Identification Symbols on Aerospace Parts. 
Note: All DPM systems SHALL require bar code verification (mark quality measurement) immediately following 
creation of the mark to maintain symbol quality and GM downstream scannability. 
7.1 Considerations. The following are typical criteria for using DPM. 
� 
The part is too small to be labeled with traditional bar code labels. 
� 
The part is subjected to environmental conditions that preclude the use of labels. 
� 
DPM may be more cost efficient than individual item labels. 
� 
Identification is required for the life cycle of the part and labels are not acceptable for the reasons stated 
above. 
� 
DPM is integrated as part of the manufacturing process rather than a secondary or manual process. 
7.2 Direct Mark on Parts. Bar Code content or direct marks on parts SHALL follow the coding scheme of 
Section 3. 
7.3 DPM Human Readable Information. Available area for marking and/or process cycle time may eliminate 
or reduce the amount of human readable information required. Mutual agreement between Supplier and GM 
SHALL be required. 
7.4 Marking Methods. The guideline in Table 14 identifies suggested marking methods for different materials. 
 
Table 14: Guideline for Material Marking Process 
Material 
Metallic 
Non-Metallic 
Marking Process 
ALUMINUM 
FERROUS 
MAGNESIUM 
TITANIUM 
CERAMICS 
GLASS 
FIBERGLASS 
PLASTICS 
RUBBER 
Scribing 
X 
X 
X 
X 
X 
X 
X 
X 
X 
Dot-Peen 
X 
X 
X 
X 
 
 
 
 
 
Laser 
X 
X 
X 
X 
X 
X 
X 
X 
X 
Inkjet 
X 
X 
X 
X 
X 
X 
X 
X 
X 
 
7.4.1 Scribing. Scribe marking technology provides the ability to scribe or draw an image on a part’s surface 
by displacing the material. This process uses a pneumatically or electromechanically driven stylus. Marking of 
Data Matrix symbols can be scribed using the three allowed methods in ISO/IEC 16022 which are square, 
circular, or octagonal. Generally square shaped modules are utilized as they are easier to decode or read. The 
square module’s appearance is affected by the marking force and material hardness. Ambient noise is typically 
reduced compared to dot-peen method. Marking noise is dependent on part geometry and fixture tooling. 
7.4.1.1 Generally, a square module is preferred when scribing. Scribe markers use a stylus that can create a 
square element/cell on a surface. It strikes the surface with a pointed stylus at a beginning point on the square, 
and then continues to make four connected straight lines outlining a square element/cell. The element/cell size 
can be adjusted. The typical fill rate is 80%. 
7.4.1.2 Scribe marking is slower than dot-peen. 
7.4.2 Dot-Peen. Dot-peen marking technology typically produces round indentations on a part’s surface with a 
pneumatically or electromechanically driven stylus, otherwise known as a pin. Critical to the readability of 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 25 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 25 of 59 
 
dot-peen, marked symbols are the indented dot’s shape, size, and spacing. The dot size and appearance are 
determined mostly by the stylus cone angle, marking force, and material hardness. The indented dot created 
SHOULD be suitable to trap or reflect light and be large enough to be distinguishable from the part’s surface 
roughness. It SHOULD also have spacing wide enough to accommodate varying module sizes, placement, 
and illumination. 
7.4.2.1 The issues involved in marking and reading dot-peen-marked symbols on metals are different than 
symbols printed on paper. The first fundamental difference is that the contrast between dark and light fields is 
created by artificial illumination of the symbol. Therefore, the module’s shape, size, spacing, and part surface 
finish can all affect symbol readability. 
7.4.2.2 The key to a successful dot-peen marking and reading project is to tightly control the variables affecting 
the consistency of the process. Symbol reading verification systems can provide feedback of the process 
parameters to some extent. Marking system operating and maintenance procedures and schedules SHALL be 
established and followed to help ensure consistent symbol quality. 
7.4.2.3 Dot-peen marking is slower than laser marking and has density limitations (Figure 26). 
 
 
Figure 26: Dot-peen Illustrating Importance of Lighting 
 
7.4.3 Laser. Lasers can be used to create a mark on some materials. This is done by directing a beam of 
coherent, collimated, focused light energy onto an item’s surface. In general, when a laser’s beam comes into 
contact with an item, its light energy is converted into heat energy, which creates a mark either by melting, 
ablation, carbon migration, or chemical reaction. Various materials may react differently to each type of laser 
and/or laser marking technique. All lasers will not create readable marks on all substrates. 
7.4.3.1 When considering a laser marking system, the following factors SHOULD be taken into consideration: 
� 
Type of material to be marked. 
� 
Laser type and marking process type. 
� 
Laser power. 
� 
Cycle time. 
� 
Information (volume of data) to be marked. 
� 
Laser safety. 
7.4.3.2 Different materials absorb or reflect specific laser wavelengths at different rates. The amount of 
absorption is directly proportional to the laser’s ability to heat the material and cause a change in its 
appearance. The type of lasing medium will determine a laser’s light wavelength. Laser marking systems 
typically derive their name from their lasing medium. For example, CO2 lasers use carbon dioxide gas as a 
medium. 
7.4.3.3 Laser marking generally produces the fastest marking cycle (Figure 27). 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 26 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 26 of 59 
 
 
Figure 27: Laser Etch on Plastic 
 
7.4.4 Inkjet. Inkjet technology, a non-intrusive marking technology, sprays precisely controlled drops of ink 
through the air in a pattern capable of creating a symbol. These drops are made of pigment suspended in fluid 
that evaporates, leaving the colored dye on the surface of the item. 
7.4.4.1 There are two primary methods for generating these drops: The Drop-on-Demand and Continuous. The 
Drop-on-Demand method uses valves or Piezo-electric technology to force ink through an orifice. This method 
has significant printing resolution advantages over the Continuous method. The distance the ink can be "shot" 
is 
usually 
limited 
to 
no 
more 
than 
3.1 mm 
(1/8 in). 
This 
limits 
the 
use 
of 
 
Drop-on-Demand in industrial DPM applications (Figure 28). 
 
 
Figure 28: Inkjet Example Speaker with Rectangular Data Matrix on Metal 
 
7.4.4.2 The issues involved in marking and reading inkjet symbols placed directly on parts are somewhat 
different from those of symbols printed on paper. Particular attention must be paid to the condition of the 
substrate on which the ink is to be deposited. Cleaning the part surfaces prior to marking with an abrasive pad 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 27 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 27 of 59 
 
to remove coatings, rust, and discoloration, or using an air knife to blow away excess machining fluids, debris, 
or oil can improve mark and adhesion reliability. 
7.4.4.3 Inkjet marking SHOULD not be considered a permanent marking method and is typically limited to 
parts that will not be exposed to harsh manufacturing conditions. In particular, it SHOULD not be used on 
Electric Discharge Machining (EDM), grit-blasted, machined, and shot-peened surfaces. Many of these 
conditions change surface properties and/or color and may make it necessary to reapply the mark. In addition, 
care must be exercised to ensure that the part will not go through any paint-dissolving fluid. Another limitation 
to inkjet marking is that typically a part must be moving at a consistent speed in one direction past the marking 
head during the marking process. Systems where the marking head moves and the part being marked remains 
stationary are available. 
7.4.4.4 Inkjet marking is suitable for applications requiring security by using Ultraviolet (UV) inks requiring 
special lighting to read. 
8 Symbol (Bar Code) Quality Verification 
8.1 General. Verification devices are quality control tools for verifying the readability and standards compliance 
of printed linear bar code symbols. Scanning is not considered verification. 
8.1.1 Verification testing SHALL be performed on labels and direct marked parts. 
8.2 Direct Part Mark Verification. A DPM verifier is a system consisting of lighting, optics, camera (imager) 
verification software, and calibration references. The resolution of the verification system SHOULD be at least 
twice that of the imager (reader). This may be accomplished with either higher magnification optics or an 
imaging device with twice the resolution of the reader. AIAG B-17 SHALL be referenced as the process 
guideline for direct marking using laser, peening, or ink jet. Direct part marking SHALL be mutually agreed to 
by GM and supplier. Imaging (scanning) requirements for direct mark may require special lighting and 
specialized imagers. 
Note: DPM systems SHALL require verification immediately following the creation of the mark to maintain 
quality and downstream scannability. 
8.3 Label Performance Testing. Testing must be done on production intent labels located on production 
intent parts, components, assemblies or modules installed using the production process and scanned at point 
of use. 
8.3.1 KCDS VER/TRA. Labels containing bar codes for parts requiring KCDS Verification (VER) or Traceability 
(TRA) must meet permanent label testing requirements of GMW14573 A, B, C, D, E, or G. 
8.3.2 Other Bar Codes. Labels minimally must meet the requirements of GMW14573 F. 
8.4 Bar Code Print Quality on Labels. Bar code print quality SHALL BE ANSI Grade C or better at GM point 
of scan. Evaluate using ISO/IEC 15415 or AIM DPM Quality Guideline. 
8.4.1 Data Matrix and QR Code Print Quality on Labels. The ISO/IEC 15415 (Print Quality Test 
Specification - Two-dimensional symbols), ISO/IEC 16022 (Data Matrix), and ISO/IEC 18004 (QR Code) 
SHALL be used to determine Data Matrix and QR Code print quality on a label. 
8.4.1.1 The Print Quality SHOULD be Measured at the Mutually Agreed-Upon GM Point of Scan. 
8.4.1.2 The Symbol Quality Parameters Ensure Readability Over a Broad Range of Environments. In 
addition, it is recommended that quality measurements be taken under consistent conditions; for example, with 
the 
same 
lighting 
and 
on 
the 
same 
surface 
the 
label 
will 
be 
 
attached to. 
8.4.1.3 The grades are the result of specific measurements made according to the AIM International 
Symbology Specification Document quality definition for: 
� 
Symbol decode. 
� 
Symbol contrast. 
� 
Symbol print. 
� 
Symbol axial non-uniformity. 
� 
Symbol error correction. 
8.5 Label Placement. Label application location must be identified on the part drawing/UG math. Consistent 
label placement is essential when fixed mount scanners are used at the manufacturing/assembly location. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 28 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 28 of 59 
 
9 Additional Product Characteristics 
9.1 General. This section describes how to add additional information for product characteristics. 
(Appendix F) Examples of product characteristics are test stand results for radiator pressure test; current draw 
for a lighting module; measured torque of a fastener; Radio Frequency (RF) of device or any other significant 
measurement data that is determined to be significant for quality or warranty. In some cases, product 
characteristics are needed for process such as piston size match to cylinder bore. 
Note: Tires SHALL follow 4.11. 
9.2 Data Identifier 7Q. Data Identifier 7Q SHALL be used with the appropriate appended unit of measure 
qualifier ANSI X12.3 Data Element Number 355 Unit of Measure. An excerpt of the ANSI X12.3 table is posted 
to the AIAG web site at the following url: http://www.autoid.org/ANSI_MH10/ansi_mh10sc8_wg2.htm. The 
data, if appropriate, may contain a decimal point for the required precision. Example of a voltage measurement 
of 14.7 Voltage Direct Current (VDC) would be 7Q14.72H, where 2H is the qualifier for VDC. 
9.2.1 Encodation of 7Q. Reference 4.5 for Data Syntax encodation methodology. Using the example from 4.5 
(Figure 29). 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
STLSYYDDDA2B4C6D8ER
SEOT, 
the 
field 
SHOULD be added after the trace code field T and before the Record Separator R
S. The Encodation 
would look like this: 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
STLSYYDDDA2B4C000EG
S7Q14.72HRSEOT. 
Encoded within the 2D Bar Code is a field with 7Q14.72H which translates to 14.7 VDC. 
9.2.2 Parsing Algorithm. For DI 7Q, the last two characters of the data field SHALL be the ANSI X12.3 Data 
Element Number 355 Unit of Measure (qualifier). See Table 15. The data elements between the DI 7Q and the 
two character qualifier constitutes the value. In summary: 
� 
DI 7Q signifies that the last two characters contain the unit of measure X12.3 qualifier. 
� 
Go to the end of the data field. 
� 
Go back two characters. 
� 
Those two characters are the qualifier code. 
� 
Look up code in qualifier table to determine the unit of measure. 
 
Table 15: Examples of ANSI X12.3 355 Data Element Number 355 Unit of Measure (Qualifier) 
Qualifier 
Definition 
 
Qualifier 
Definition 
2G 
Volts (AC) 
 
68 
Ampere 
2H 
Volts (DC) 
 
CE 
Centigrade 
2N 
Decibels 
 
DN 
Deci Newton-Meter 
2P 
Kilobyte 
 
FA 
Fahrenheit 
2Z 
Millivolts 
 
G9 
Gigabyte 
4K 
Milliamperes 
 
HJ 
Horsepower 
4L 
Megabytes 
 
HP 
Millimeter H20 
4S 
Pascal 
 
HZ 
Hertz 
70 
Volt 
 
NU 
Newton-Meter 
 
9.3 As-Built Label. The “As-Built” label/mark provides a means of capturing the trace data as part of an 
external assembly process such as a Value Added Assembler (VAA). The 2D bar code is structured to include 
individual trace record for each component that requires traceability. See Appendix G. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 29 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 29 of 59 
 
10 Radio Frequency Identification (RFID) 
10.1 When RFID tags become cost effective for identification of parts, components, assemblies and modules, 
the Data Syntax and data fields will be encodable in passive or active RFID tags containing user memory. The 
standard used to encode data into the 2D symbologies has been incorporated into the AIAG B-11 RFID 
Standard making the two technologies interchangeable from a data/IT perspective. AIAG B-11 is fully ISO/IEC 
compliant. 
11 Validation 
11.1 Responsibility. The Design Release Engineer is responsible for releasing bar codes compliant to this 
standard. 
11.2 Form. CG2503 – Bar Code Format Validation Form SHALL be used to ensure the correct bar code 
content, bar code scan quality and plant readiness. 
11.3 Process. CG2503 is stored in the GDM General Forms and Templates folder. The CG2503 process to 
follow and RASIC are documented on the Bar Code page of the Labels and Literature web site: 
http://gmna1.gm.com/eng/labels/barcodes.html. 
12 Notes 
12.1 Glossary. 
2D (Two-dimensional) Symbols: Optically readable symbols that must be examined both vertically and 
horizontally to read the entire message. Two-dimensional symbols may be one of two types: matrix symbols 
and multi-row symbols. Two-dimensional symbols have error detection and may include error correction 
features. (See matrix symbol.) 
AIAG: Automotive Industry Action Group www.aiag.org. 
AIM: Automatic Identification Manufacturers Association www.aimglobal.org. 
Alphanumeric: A character set that contains both alphabetic character (letters) and numeric digits (numbers) 
and usually other characters such as punctuation marks. 
ANSI or ANS: American National Standards Institute. 
Auto-discrimination: The ability of a bar code scanner/imager to automatically distinguish between two or 
more symbologies (e.g., Code 128, Code 39, Data Matrix and QR Code). 
Bar Code (also barcode): An optically machine-readable representation of data. Traditionally, bar codes 
represented data in the widths (lines) and the spacings of parallel lines and may be referred to as linear or 
one-dimensional (1D) bar codes or symbologies. But they also come in patterns of squares, dots, hexagons 
and other geometric patterns within images termed two-dimensional (2D) matrix codes or symbologies. It is 
important to note that both the patterns (lines, squares, dots, etc.) and spacings constitute the data encodation 
schema. 
Bar Code Label: A generic term covering labels that have 1D and/or 2D bar code symbols, with or without 
human readable data, printed on them. 
Batch: Batch production is a manufacturing method used to produce or process any product in batches, as 
opposed to a continuous production process, or a one-off production. Examples of batch are castings (based 
on pour), paint (based on a single blend of ingredients), adhesives, steel, etc. 
Cell: See module. 
Component: A part, assembly, or raw material that is a constituent of a higher level assembly. 
Data Field: A message consisting of a data identifier immediately followed by its associated data. 
Data Format: Letters and numbers used to denote the type of data allowed within the referenced data field, 
and the total quantity of that type of data allowed in the data field. 
Data Format Examples: 
"an.6" means up to six characters of alpha-numeric data are allowed. 
"n.12" means up to 12 characters of only numeric data are allowed. 
Data Identifier (DI): A specified character, or string of characters, that defines the intended use of the data 
element that follows. For the purposes of automatic data capture technologies, Data Identifier means the 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 30 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 30 of 59 
 
alphanumeric identifiers, as defined in ISO/IEC 15418, UCC/EAN Application Identifiers and FACT Data 
Identifiers and Maintenance and ANSI MH10.8.2. 
Data Matrix: Specific two-dimensional bar code symbology. 
Data Syntax Codes: ASCII characters used in ISO/IEC 15434 to denote specific functions within the data 
enveloping structure. 
Decimal: A base 10 numbering system, whose numbers are represented by X10, when a decimal number 
needs to be denoted from a hexadecimal number (X16). 
Direct Part Marking: A marking applied directly to a part’s surface using intrusive or non-intrusive marking 
techniques. 
DOT: Department of Transportation (US). 
DUNS: A nine digit site specific trading partner identification code assigned by Dun and Bradstreet 
www.dnb.com. 
Error Correction: Mathematical techniques used to reconstruct missing or damaged data. 
Hex: Hexadecimal describes a base 16 number system. The hexadecimal numbers are 0 thru 9 and then the 
letters A thru F, whose numbers are represented by X16, when a hexadecimal number needs to be denoted 
from a decimal number (X10). 
Human Readable Information: Information that may appear and be associated with a machine readable 
medium, typically on a label (e.g., bar code, 2D symbol, RF tag) intended to convey information to a person. 
IEC: International Electrotechnical Commission. International standards and conformity assessment for 
government, business, and society for all electrical, electronic, and related technologies. 
ISO: International Organization for Standardization. ISO is a network of the national standards institutes of 156 
countries, on the basis of one member per country, with a Central Secretariat in Geneva, Switzerland, that 
coordinates the system. 
ISO/IEC: Represents work done and/or supported by both the ISO and IEC organizations. 
Imager (See scanner): A type of bar code scanner used to read linear bar codes and 2D symbols using 
optical imaging technology (typically a camera based matrix array or linear array optical sensor technology). 
Individual Part: A single part, item, or material purchased, manufactured, and/or distributed. 
Intrusive Marking: Any device designed to alter a material surface to form a human or machine readable 
symbol. This marking category includes, but is not limited to: devices that abrade, burn, corrode, cut, deform, 
dissolve, etch, melt, oxidize, or vaporize a material surface. 
Julian Date: In the commercial world the term "Julian date" is the number of the day in a particular year, so 
that January 1st = day 1, February 28th = day 59, and so on. It is the actual day of manufacture/assemble. 
Label: Produced by any means, on a piece of paper, cloth, polymer, metal, or other material, affixed to 
something via a pressure-sensitive backing or heat application, uses black images on a white background or 
white images on a black background (reverse image) to indicate its contents, destination, or other information. 
Laser: Light amplification by stimulated emission of radiation. 
Linear Bar Code Symbol (1D): An optically readable array of parallel rectangular bars and spaces of varying 
thickness and spacing that are arranged in a predetermined pattern following specific rules to represent 
elements of data that are referred to as characters. A linear bar code symbol typically contains a leading quiet 
zone, start character, data character(s), stop character, and a trailing quiet zone, and is read in only one axis. 
Lot: See Batch. 
Manufacturer: The actual producer or fabricator of an item, not necessarily the supplier in a transaction. 
Manufacturing or Assembly Site DUNS Number: The numeric DUNS ID code used to identify the specific 
location where a part was created by the supplier/vendor. 
Matrix Symbol: A collection of polygonal or circular elements in a regular pattern to represent data for retrieval 
by a vision scanning system. 
Module: In a linear or multi-row bar code symbology, the nominal unit of measure in a symbol character. In 
certain symbologies, element widths may be specified as multiples of one module. Equivalent to X Dimension. 
� 
In a matrix symbology, a single cell or element used to encode one bit of the codeword. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 31 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 31 of 59 
 
� 
Multi-row symbology (also known as stacked symbology), a bar code symbology in which the symbol 
consists of two or more vertically adjacent rows of symbol characters. 
Mutually Defined: A meaning that is agreed upon by all appropriate parties to a transaction. 
NASA: National Aeronautics and Space Administration (US). 
Non-Intrusive Marking: A method of forming markings by adding material to a surface. Non-intrusive methods 
include ink-jet, laser bonding, liquid metal jet, silk screen, and thin film deposition. 
Part: An identifiable item that has a unique name and/or number assigned to it. 
QR Code: Specific two-dimensional bar code symbology. 
Revision Level: Code assigned such as Engineering Change Level, revision or edition or software version. 
RS232: A standard for serial binary data signals. It is commonly used in computer serial ports. 
Scanner (See Imager): An input device that sends signals proportional to the reflectivity of each successive 
element of the symbol (linear or 2D) to the decoder. 
Serial Number: A unique code assigned to an entity for the life of the entity, such as an air bag module, 
engine or transmission assembly, for the differentiation of that specific entity from any other like entity. 
Supplier/vendor: In a transaction, the party that produces, provides, or furnishes a product or service. 
Supplier/Vendor ID: The numeric DUNS ID code used to identify the supplier/vendor. 
Symbology: A standard means of representing data in an optically readable form. Each symbology 
specification sets out its particular rules of composition or symbol architecture. 
Two-Dimensional Symbol (2D): An optically readable symbol that must be examined both vertically and 
horizontally to read the entire message. Two-dimensional symbols differ from linear bar codes in that they are 
made up of “pixel elements” that are comparable to the bars in linear bar codes. 
Vehicle Partitioning and Product Structure (VPPS): Represents a globally consistent means for describing 
vehicle content. VPPS is a hierarchical structure that has consistency across major vehicle areas (Powertrain, 
Chassis, etc.) VPPS is a mechanism that allows data sharing/comparing across systems globally (GMNA, 
GME, GMLAAM, GMAP, etc.) VPPS is a standard global product breakdown structure approved by GEDOC 
(NOA 002) and GADVC (NOA 012). Changes are managed via global process. 
X Dimension: The specified width of the narrow elements in a bar code symbol or the specified width of a 
single element/cell in a two-dimensional symbol. 
Year: In the context of traceability, year is the actual year of manufacture/assemble as opposed to "model 
year". 
12.2 Acronyms, Abbreviations, and Symbols. 
1D 
One-Dimensional or also termed a linear bar code symbol 
2D 
Two-Dimensional 
AIAG 
Automotive Industry Action Group 
AIDC 
Automatic Identification Data Collection Technology 
AIM 
Automatic Identification Manufacturers Association 
ANS 
American National Standard 
ANSI 
American National Standards Institute 
ASCII 
American Standard Code for Information Interchange 
BOM 
Bill of Material 
CI 
Component Identifier 
CO2 
Carbon Dioxide 
CR 
Change Request 
DFMEA 
Design Failure Mode Effects Analysis 
DI 
Data Identifier 
DNB 
Dun and Bradstreet 
DOT 
Department of Transportation 
DPM 
Direct Part Marking 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 32 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 32 of 59 
 
DUNS 
Data Universal Numbering System 
DRE 
Design Release Engineer 
EC 
Error Correction 
ECC 
Error Correction Code 
EDM 
Electric Discharge Machining 
EOT 
End of Transmission 
FMVSR 
Federal Motor Vehicle Safety Regulation 
GM&Q 
Global Manufacturing and Quality 
GPDS 
Global Product Description System 
GQR 
Global Quality Requirement 
HRI 
Human Readable Information 
IEC 
International Electrotechnical Commission 
in 
inch 
ISO 
International Organization for Standardization 
IT 
Information Technology 
KCDS 
Key Characteristics Designation System 
m 
Meter 
ME 
Manufacturing Engineering 
mm 
Millimeter 
NASA 
National Aeronautics and Space Administration (US) 
NOA 
Notice of Action 
nm 
Nanometer 
OEM 
Original Equipment Manufacturer 
PE 
Process Engineering 
PFMEA 
Process Failure Mode and Effects Analysis 
PRTS 
Problem Reporting and Tracking System 
pt 
Points 
QR 
Quick Response 
RF   
Radio Frequency 
RFID 
Radio Frequency Identification 
SOR 
Statement of Requirements 
TRA 
Traceability 
UCC/EAN 
Uniform Code Council/European Article Number 
USB 
Universal Serial Bus 
ULD 
Unit Load Device 
UV 
Ultraviolet 
V 
Vendor/Supplier Identifier 
VAA 
Value Added Assembler 
VDC 
Voltage Direct Current 
VER 
Verification 
VIN 
Vehicle Identification Number 
VPPS 
Vehicle Partitioning and Product Structure 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 33 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 33 of 59 
 
13 Coding System 
This standard SHALL be referenced in other documents, drawings, etc., as follows: 
GMW15862 
14 Release and Revisions 
This standard was originated in March 2008. It was first approved by Interiors Global Technology Engineering 
in November 2008. It was first published in December 2008. 
Issue 
Publication Date 
Description (Organization) 
1 
DEC 2008 
Initial publication. 
2 
OCT 2009 
Revised to add VPPS code to the bar code content. Identified structure 
for lot/batch traceability. Examples added. Sections 2.1, 2.3, 9, 11 
updated; Major rewrites to Sections 3, 4, 5, 6, 7. Sequence of 
appendices modified to enhance usability. (Interior) 
3 
NOV 2010 
Removed the Transition Label formats.  
Replaced Appendix J with Powertrain formats.  
Added references to the Powertrain traceability formats.  
Added the Validation section requiring CG2503.  
Modified Table C2. Corrected bar code figures. (Labels and Literature 
GSSLT) 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 34 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 34 of 59 
 
Appendix A: Typical Data Identifiers 
ANSI MH10.8.2 defines more than 100 Data Identifiers for many purposes in many industries. GM requires the 
use of Data Identifiers. The following table includes some of the typical DIs in ANSI MH10.8.2 frequently used 
in the automotive industry. Due to frequent updates to ANSI MH10.8.2, a draft copy is maintained at 
www.autoid.org at the following url: http://www.autoid.org/ANSI_MH10/ansi_mh10sc8_wg2.htm. 
 
Table A1: Typical Data Identifiers Used in the Automotive Industry 
DI 
Description 
B 
Container type (internally assigned or mutually defined) 
1B 
Returnable container identification code assigned by the container owner or the appropriate regulatory agency (e.g., a metal tub, 
basket, reel, Unit Load Device (ULD), trailer, tank, or intermodal container) (excludes gas cylinders) (See "2B") 
2B 
Gas Cylinder Container Identification Code assigned by the manufacturer in conformance with U.S. Department of Transportation 
DOT standards 
D 
Date, in the format YYMMDD (Mutually defined significance) 
1D 
Date in the format DDMMYY (Mutually defined significance) 
2D 
Date in the format MMDDYY (Mutually defined significance) 
3D 
Date in the format YDDD (Julian mutually defined significance) 
4D 
Date in format YYDDD (Julian mutually defined significance) 
5D 
Date in ISO format YYMMDD immediately followed by an X12.3 Data Element Number 374 Qualifier providing a code specifying 
type of date (e.g., ship date, manufacturing date) 
1E 
Air pressure expressed in Pascal’s as the standard international measure 
I 
Vehicle Identification Number (VIN) 
2I 
Abbreviated VIN Code (example PVI, order ID. sequence ID) 
5N 
Coding Structure and Formats in Accordance with AIAG Recommendations. The full Data Identifier is in the form 5Nxx where xx 
is found in the full code list that can be found at www.aiag.org 
P 
Item Identification Code assigned by GM 
1P 
Item Identification Code assigned by Supplier 
2P 
Code assigned to specify the revision level of the part (e.g., Engineering Change Level, revision or edition, software revision 
level) 
20P 
Legacy GM1737 Verification/Error Proofing code/structure as defined by GM 
Q 
Quantity, Number of Pieces, or Amount (numeric only)(unit of measure and significance mutually defined) 
1Q 
Theoretical Length/Weight (numeric only) (historically used in the shipment of primary metals) 
2Q 
Actual Weight (numeric only) 
7Q 
Quantity and unit of measure in the format: Quantity followed by the two-character Unit of Measure code as defined in Data 
Element number 355 of the ANSI X12.3 Data Element Dictionary standard 
S 
Serial Number assigned by Supplier to an entity for its lifetime 
10S 
Machine, work cell or tool ID code 
11S 
Fixed Asset ID Code 
T 
Traceability code/structure as defined by GM 
1T 
Traceability number assigned by the Supplier/Manufacturer 
20T 
Legacy GM1737 Traceability code/structure as defined by GM  
21T 
Legacy GM1737 Enhanced Traceability code/structure as defined by GM. 
12V 
DUNS number identifying Manufacturing/Assembly site 
14V 
DUNS number identifying specific GM site as the customer 
Y 
GM Internal applications -- Assigned to VPPS compressed code. 
Z 
Mutually defined between GM and Supplier (title to reflect mutually agreed-to meaning) 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 35 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 35 of 59 
 
Appendix B: JULIAN Calendar 
 
Table B1: Perpetual Julian Date Calendar 
Day 
Jan 
Feb 
Mar 
Apr 
May 
Jun 
Jul 
Aug 
Sep 
Oct 
Nov 
Dec 
Day 
1 
001 
032 
060 
091 
121 
152 
182 
213 
244 
274 
305 
335 
1 
2 
002 
033 
061 
092 
122 
153 
183 
214 
245 
275 
306 
336 
2 
3 
003 
034 
062 
093 
123 
154 
184 
215 
246 
276 
307 
337 
3 
4 
004 
035 
063 
094 
124 
155 
185 
216 
247 
277 
308 
338 
4 
5 
005 
036 
064 
095 
125 
156 
186 
217 
248 
278 
309 
339 
5 
6 
006 
037 
065 
096 
126 
157 
187 
218 
249 
279 
310 
340 
6 
7 
007 
038 
066 
097 
127 
158 
188 
219 
250 
280 
311 
341 
7 
8 
008 
039 
067 
098 
128 
159 
189 
220 
251 
281 
312 
342 
8 
9 
009 
040 
068 
099 
129 
160 
190 
221 
252 
282 
313 
343 
9 
10 
010 
041 
069 
100 
130 
161 
191 
222 
253 
283 
314 
344 
10 
11 
011 
042 
070 
101 
131 
162 
192 
223 
254 
284 
315 
345 
11 
12 
012 
043 
071 
102 
132 
163 
193 
224 
255 
285 
316 
346 
12 
13 
013 
044 
072 
103 
133 
164 
194 
225 
256 
286 
317 
347 
13 
14 
014 
045 
073 
104 
134 
165 
195 
226 
257 
287 
318 
348 
14 
15 
015 
046 
074 
105 
135 
166 
196 
227 
258 
288 
319 
349 
15 
16 
016 
047 
075 
106 
136 
167 
197 
228 
259 
289 
320 
350 
16 
17 
017 
048 
076 
107 
137 
168 
198 
229 
260 
290 
321 
351 
17 
18 
018 
049 
077 
108 
138 
169 
199 
230 
261 
291 
322 
352 
18 
19 
019 
050 
078 
109 
139 
170 
200 
231 
262 
292 
323 
353 
19 
20 
020 
051 
079 
110 
140 
171 
201 
232 
263 
293 
324 
354 
20 
21 
021 
052 
080 
111 
141 
172 
202 
233 
264 
294 
325 
355 
21 
22 
022 
053 
081 
112 
142 
173 
203 
234 
265 
295 
326 
356 
22 
23 
023 
054 
082 
113 
143 
174 
204 
235 
266 
296 
327 
357 
23 
24 
024 
055 
083 
114 
144 
175 
205 
236 
267 
297 
328 
358 
24 
25 
025 
056 
084 
115 
145 
176 
206 
237 
268 
298 
329 
359 
25 
26 
026 
057 
085 
116 
146 
177 
207 
238 
269 
299 
330 
360 
26 
27 
027 
058 
086 
117 
147 
178 
208 
239 
270 
300 
331 
361 
27 
28 
028 
059 
087 
118 
148 
179 
209 
240 
271 
301 
332 
362 
28 
29 
029 
 
088 
119 
149 
180 
210 
241 
272 
302 
333 
363 
29 
30 
030 
 
089 
120 
150 
181 
211 
242 
273 
303 
334 
364 
30 
31 
031 
 
090 
 
151 
 
212 
243 
 
304 
 
365 
31 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 36 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 36 of 59 
 
Table B2: Leap Year Julian Date Calendar 
Day 
Jan 
Feb 
Mar 
Apr 
May 
Jun 
Jul 
Aug 
Sep 
Oct 
Nov 
Dec 
Day 
1 
001 
032 
061 
092 
122 
153 
183 
214 
245 
275 
306 
336 
1 
2 
002 
033 
062 
093 
123 
154 
184 
215 
246 
276 
307 
337 
2 
3 
003 
034 
063 
094 
124 
155 
185 
216 
247 
277 
308 
338 
3 
4 
004 
035 
064 
095 
125 
156 
186 
217 
248 
278 
309 
339 
4 
5 
005 
036 
065 
096 
126 
157 
187 
218 
249 
279 
310 
340 
5 
6 
006 
037 
066 
097 
127 
158 
188 
219 
250 
280 
311 
341 
6 
7 
007 
038 
067 
098 
128 
159 
189 
220 
251 
281 
312 
342 
7 
8 
008 
039 
068 
099 
129 
160 
190 
221 
252 
282 
313 
343 
8 
9 
009 
040 
069 
100 
130 
161 
191 
222 
253 
283 
314 
344 
9 
10 
010 
041 
070 
101 
131 
162 
192 
223 
254 
284 
315 
345 
10 
11 
011 
042 
071 
102 
132 
163 
193 
224 
255 
285 
316 
346 
11 
12 
012 
043 
072 
103 
133 
164 
194 
225 
256 
286 
317 
347 
12 
13 
013 
044 
073 
104 
134 
165 
195 
226 
257 
287 
318 
348 
13 
14 
014 
045 
074 
105 
135 
166 
196 
227 
258 
288 
319 
349 
14 
15 
015 
046 
075 
106 
136 
167 
197 
228 
259 
289 
320 
350 
15 
16 
016 
047 
076 
107 
137 
168 
198 
229 
260 
290 
321 
351 
16 
17 
017 
048 
077 
108 
138 
169 
199 
230 
261 
291 
322 
352 
17 
18 
018 
049 
078 
109 
139 
170 
200 
231 
262 
292 
323 
353 
18 
19 
019 
050 
079 
110 
140 
171 
201 
232 
263 
293 
324 
354 
19 
20 
020 
051 
080 
111 
141 
172 
202 
233 
264 
294 
325 
355 
20 
21 
021 
052 
081 
112 
142 
173 
203 
234 
265 
295 
326 
356 
21 
22 
022 
053 
082 
113 
143 
174 
204 
235 
266 
296 
327 
357 
22 
23 
023 
054 
083 
114 
144 
175 
205 
236 
267 
297 
328 
358 
23 
24 
024 
055 
084 
115 
145 
176 
206 
237 
268 
298 
329 
359 
24 
25 
025 
056 
085 
116 
146 
177 
207 
238 
269 
299 
330 
360 
25 
26 
026 
057 
086 
117 
147 
178 
208 
239 
270 
300 
331 
361 
26 
27 
027 
058 
087 
118 
148 
179 
209 
240 
271 
301 
332 
362 
27 
28 
028 
059 
088 
119 
149 
180 
210 
241 
272 
302 
333 
363 
28 
29 
029 
060 
089 
120 
150 
181 
211 
242 
273 
303 
334 
364 
29 
30 
030 
 
090 
121 
151 
182 
212 
243 
274 
304 
335 
365 
30 
31 
031 
 
091 
 
152 
 
213 
244 
 
305 
 
366 
31 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 37 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 37 of 59 
 
Appendix C: Vehicle Partitioning and Product Structure (VPPS) 
The Vehicle Partitioning and Product Structure (VPPS) is a globally consistent means for describing vehicle 
content (http://gmna1.gm.com/eng/grc/vpps/index.html). VPPS is a hierarchical structure that has consistency 
across major vehicle areas (Powertrain, Chassis, etc.) VPPS is a mechanism that allows data 
sharing/comparing across systems globally (GMNA, GME, GMLAAM, GMAP, etc.) VPPS is a standard global 
product breakdown structure approved by GEDOC (NOA 002) and GADVC (NOA 012). Changes are managed 
via global process. Contact KCDS for assistance. 
GMW15862 assigns the Data Identifier Y to Compressed VPPS codes and right pads the data with zeros (0) 
for the remaining levels to make a total of 14 data characters. The decimals are implied (not encoded). See 
example in Table C1. 
See Appendix C2 for description of the 14th character of the VPPS code. 
 
Table C1: Example of GMW15862 VPPS Data Encodation 
COMPRESSED VPPS 
VPPS 
DESCRIPTION 
Implied 
Decimal 
Encodation 
With DI Y 
951.98 
Fuel Tank and Canister - Attachments/Components 
 
 
952.97 
Fuel Pump and Sender - Module/Assembly 
 
 
952.98 
Fuel Pump and Sender - Attachments/Components 
 
 
953.98 
Fuel Plumbing and Hardware - Attachments/Components 
 
 
954 
Emission Reduction Fluid (Urea System) 
 
 
954.01 
Tank Assembly 
95401 
Y9540100000000X 
954.01.01 
Tank 
 
 
954.01.02 
Tank Spud 
 
 
954.01.03 
Level Sensor 
 
 
954.01.03.01 
Level Sensor Connector 
 
 
954.01.04 
Pressure Sensor 
 
 
954.01.04.01 
Pressure Sensor Connector 
 
 
954.01.05 
Temperature Sensor 
 
 
954.01.05.01 
Temperature Sensor Connector 
 
 
954.01.06 
Pump 
 
 
954.01.06.01 
Pump Connector 
 
 
954.01.07 
Filter 
 
 
954.01.08 
Tank/Pump Reservoir 
 
 
954.01.09 
Heater 
 
 
954.01.10 
Vent Valve 
 
 
954.01.11 
Vent Hose 
 
 
954.01.12 
Functional Module Ring 
 
 
954.02 
Fill Pipe Assembly 
 
 
954.02.01 
Fill Hose 
 
 
954.02.02 
Fill Pipe 
 
 
954.02.03 
Hose Clamp 
 
 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 38 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 38 of 59 
 
The last character of the VPPS code (14th character) may be used to reference part installation location. The 
bar code will be generated with the character X. When identical part numbers are installed to a vehicle, the 
scanning operation will encode the final character according to Table C2. The location code is forced into the 
data string at the scanning station or when electronic module information is transferred to the database. 
For example, using a Fuel Tank Assembly, the compressed VPPS format is 954.01 and encodation within the 
data field is Y9540100000000X (decimals implied and right padded with 0). If this assembly were to be a right 
mounted tank, then the transmitted encodation would be Y9540100000000R per Table C2. 
 
Table C2: Codes for 14th Character Position of the Compressed VPPS to Signify Vehicle Mounting 
Location – Default is X (Uppercase Character Shall be Used) 
Character 
Definition 
 
Character 
Definition 
A 
Side Left Rear 
 
N 
Neither 
B 
Side Left Front 
 
O 
Do Not Use 
C 
Side Right Rear 
 
P 
Reserved 
D 
Side Right Front 
 
Q 
Do Not Use 
E 
Roof Left Rear 
 
R 
Right 
F 
Roof Left Front 
 
S 
Reserved 
G 
Roof Right Rear  
 
T 
Left Rear 
H 
Roof Right Front 
 
U 
Left Front 
I 
Do Not Use 
 
V 
Right Rear 
J 
Reserved 
 
W 
Right Front 
K 
Roof Rear 
 
X 
DEFAULT 
L 
Left 
 
Y 
Front 
M 
Roof Front 
 
Z 
Rear 
Note: DRE shall specify if a mounting location character is required at the scanning station. 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 39 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 39 of 59 
 
Appendix D: Data Matrix Reference Information 
 
The following is intended as an aide in understanding some of the characteristics of the Data Matrix 
symbology. Included is a procedure to estimate the symbol size for planning the area required for the Data 
Matrix symbol. 
 
 
Figure D1: Anatomy of a Data Matrix Symbol along with an Illustration How the Eight Bits of Each Byte 
are Distributed within a 10 x 10 Data Matrix Symbol 
 
 
Table D1: Data Matrix Data Capacity (Square Symbol) 
Symbol Size 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
Rows 
10 
12 
14 
16 
18 
20 
22 
24 
26 
32 
36 
40 
44 
48 
52 
64 
72 
80 
88 
96 
104 
120 
132 
144 
Columns 
10 
12 
14 
16 
18 
20 
22 
24 
26 
32 
36 
40 
44 
48 
52 
64 
72 
80 
88 
96 
104 
120 
132 
144 
Data Capacity 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
Numeric 
6 
10 
16 
24 
36 
44 
60 
72 
88 
124 
172 
228 
288 
348 
408 
560 
736 
912 
1152 
1392 
1632 
2100 
2608 
3116 
Alphanumeric 
3 
6 
10 
16 
25 
31 
43 
52 
64 
91 
127 
169 
214 
259 
304 
418 
550 
682 
862 
1024 
1222 
1573 
1954 
2335 
Byte 
1 
3 
6 
10 
16 
20 
28 
34 
42 
60 
84 
112 
142 
172 
202 
278 
366 
454 
574 
694 
814 
1048 
1302 
1556 
 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 40 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 40 of 59 
 
Table D2: Data Matrix Data Capacity (Rectangular Symbol) 
Symbol Size 
  
  
  
  
  
Rows 
8 
8 
12 
12 
16 
16 
Columns 
18 
32 
26 
36 
36 
48 
Data Capacity 
  
  
  
  
Numeric 
10 
20 
32 
44 
64 
98 
Alphanumeric 
6 
13 
22 
31 
46 
72 
Byte 
3 
8 
14 
20 
30 
47 
 
To estimate the Data Matrix symbol size (Length x Height) use the following procedure. Actual results will 
depend on printing/marking system used. 
 
a. Count the number of data characters to be encoded____________. 
b. Go to Table D1 for a square or D2 for a rectangular Data Matrix symbol. 
c. Find the alphanumeric number equal to or next greater than the character count. 
d. Rows = 
__________. 
e. Columns = 
__________. 
f. 
Cell/element size = __________. 
g. Multiply number of Rows (d) by Cell/element size =__________width. 
h. Multiply number of Columns (e) by Cell/element size =__________height. 
i. 
Quiet Zone = 4 x Cell/element size = _________. 
j. 
Add Quiet Zone (i) to width (g) = _________estimated total width. 
k. Add Quiet Zone (i) to height (h) = __________estimated total height. 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 41 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 41 of 59 
 
Table D3 shows reference encodation and number of characters for the following: 
1. Traceability with Serial Number 
2. Traceability with Lot or Batch identification 
3. Verification (Error Proof) 
4. Product Identification  
5. VIN (Vehicle Identification Number) 
Use Table D3 to work through Examples 1 to 4 to determine Data Matrix symbol size. 
 
Table D3: Reference Data Encodation 
 
Function 
Data Content 
Encoded Data Syntax 
Character Count 
Alphanumeric (an) 
1 
Traceability 
Serial 
Number 
Compressed VPPS 
GM Part Number 
Manufacturing or assembly 
site DUNS 
GM defined trace code 
[)>R
S06G
SY0000000000000XG
SP123456
78G
S12V*********G
S 
TLSYYDDDA2B4C6000R
S
EOT 
 
With 06 Macro = 57 an 
 
Without 06 Macro = 65 an 
2 
Traceability 
Lot or Batch 
Compressed VPPS 
GM Part Number 
Manufacturing or assembly 
site DUNS 
GM defined trace code 
[)>R
S06G
SY0000000000000XG
SP123456
78G
S12V*********G
S 
TLSYYDDD@2B4C6000R
S
EOT 
 
With 06 Macro = 57 an 
 
Without 06 Macro = 65 an 
3 
Verification 
(Error 
Proofing) 
Compressed VPPS 
GM Part Number 
Manufacturing or assembly 
site DUNS 
Julian date manufacturing 
or assembly date 
[)>R
S06G
SY0000000000000XG
SP123456
78G
S2V*********G
S4DYYDDD R
S
EOT 
 
With 06 Macro = 47 an 
 
Without 06 Macro = 55 an 
4 
Product 
Identification 
with 
Julian 
date 
of 
manufacture 
or assembly 
Compressed VPPS 
GM Part Number 
Manufacturing or assembly 
site DUNS 
Julian manufacturing or 
assembly date 
[)>R
S06G
SY0000000000000XG
SP123456
78G
S2V*********G
S4DYYDDD R
S
EOT 
 
With 06 Macro = 47an 
 
Without 06 Macro = 55 an 
5 
VIN (Vehicle 
Identification 
Number) 
17 character vehicle 
identification number 
IA2B4C6D8E0F2G4H6I 
 
18 an 
 
Example 1: Part Identification with 06 Macro and Square Data Matrix Symbol. 
a. Count the number of data characters to be encoded: 47. 
b. Go to Table D1 for a square or D2 for a rectangular Data Matrix symbol. 
c. Find the alphanumeric number equal to or next greater than the character count. 
d. Rows = 24. 
e. Columns = 24. 
f. 
Cell/element size = 0.5 mm. 
g. Multiply number of Rows (d) by Cell/element size = 12 mm width. 
h. Multiply number of Columns (e) by Cell/element size = 12 mm height. 
i. 
Quiet Zone = 4 x Cell/element size = 2 mm. 
j. 
Add Quiet Zone (i) to width (g) = 14 mm estimated total width. 
k. Add Quiet Zone (i) to height (h) = 14 mm estimated total height. 
Note: In this example using the 06 Macro allowed for a smaller overall symbol size. Without the 06 Macro, the 
symbol would have been 26 rows x 26 columns with a symbol size of 15 x 15 mm. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 42 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 42 of 59 
 
 
 
Figure D2: Using 06 Macro Resulting in Small Symbol Size 
 
Example 2: Product Identification with Julian Date with 06 Macro Rectangular Data Matrix for a Curved 
Surface. 
a. Count the number of data characters to be encoded: 47. 
b. Go to Table D1 for a square or D2 for a rectangular Data Matrix symbol. 
c. Find the alphanumeric number equal to or next greater than the character count. 
d. Rows = 16. 
e. Columns = 48. 
f. 
Cell/element size = 0.38 mm. 
g. Multiply number of Rows (d) by Cell/element size = 6.08 mm width. 
h. Multiply number of Columns (e) by Cell/element size = 18.24 mm height. 
i. 
Quiet Zone = 4 x Cell/element size = 1.52 mm. 
j. 
Add Quiet Zone (i) to width (g) = 7.6 mm estimated total width. 
k. Add Quiet Zone (i) to height (h) = 19.76 mm estimated total height. 
Note: Without the 06 Macro the symbol would have still be the same size as 55 characters would have 
required the same 16 rows x 48 columns. However best practice is to use 06 Macro. 
 
 
Note: Using or not using 06 Macro did not affect Data Matrix symbol size of 16 rows x 48 columns, 
7.6 x 19.8 mm. However, BIG RULE is to use the 06 Macro. 
Figure D3: Data Matrix Symbol Size With and Without Macro 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 43 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 43 of 59 
 
Example 3: Traceability with 06 Macro and Square Data Matrix Symbol. 
a. Count the number of data characters to be encoded: 57. 
b. Go to Table D1 for a square or D2 for a rectangular Data Matrix symbol. 
c. Find the alphanumeric number equal to or next greater than the character count. 
d. Rows = 26. 
e. Columns = 26. 
f. 
Cell/element size = 0.5 mm. 
g. Multiply number of Rows (d) by Cell/element size = 13 mm width. 
h. Multiply number of Columns (e) by Cell/element size = 13 mm height. 
i. 
Quiet Zone = 4 x Cell/element size = 2 mm. 
j. 
Add Quiet Zone (i) to width (g) = 15 mm estimated total width. 
k. Add Quiet Zone (i) to height (h) = 15 mm estimated total height. 
Note: In this example using the 06 Macro allowed for a smaller overall symbol size. Without the 06 Macro the 
symbol would have been 32 rows x 32 columns with a symbol size of 18 x 18 mm. 
 
 
Figure D4: Not Using 06 Macro has a Significant Impact on the Data Matrix Symbol Size 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 44 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 44 of 59 
 
Example 4: Vehicle Identification Number (VIN) with a Limitation Not to Exceed 14 x 14 mm 
a. Count the number of data characters to be encoded  = 18 
b. Go to Table D1 for a square symbol: 18 rows x 18 columns 
c. Overall dimension = 4 x cell size + (number row/column for 18 characters x cell size) 
d. 14 mm = 4x + (18x) where x = cell size 
e. 14 mm = 22x 
f. 
x = 0.6363 mm 
g. Cell size shall not be larger than 0.6363 mm 
 
a. Go to Table D2 for a rectangular symbol: 12 rows x 26 columns 
b. Overall width dimension = 4 x cell size + (number column for 18 characters x cell size) 
c. 14 mm = 4x + (26x) where x = cell size 
d. 14 mm = 30x 
e. x = 0.4666 mm 
f. 
Cell size shall not be larger than 0.4666 mm 
g. The calculation for height need not be made because the width is the limiting factor. 
h. Select the larger cell size between the two calculations, the square symbol with a cell size of 0.6363 mm. 
Note: In this example there is only one data field so there is no need to use the ISO/IEC 15434 Data Syntax 
standard or 06 Macro as a result. Furthermore, given the restriction on the area available and following the 
BIG RULE to make the symbol as large as practical not as small as possible, a square Data Matrix symbol 
was a logical choice. In addition, a square symbol is preferable for scanning purposes plus the resulting 
increase in cell/element size improves the read distance. 
 
 
Note: When applicable, a Data Matrix square symbol is preferred over a rectangular symbol for readability. In 
this example, the cell/element size for the square symbol is larger (0.6 mm) compared to the rectangular 
symbol (0.4 mm) for the available area improving readibility and read distance. 
Figure D5: Data Matrix Square Symbol v. Data Matrix Rectangular Symbol 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 45 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 45 of 59 
 
Appendix E: GMW15862 Traceability, Verification and Part Identification Code 
Structure and Content 
General principle for the order data fields SHALL be encoded is fixed length data fields first with variable length 
data fields to follow. Examples: 
� 
Traceability: Macro 06 (Y) VPPS G
S (P) GM Part Number G
S (12V) DUNS G
S (T) GM Trace Structure G
S 
(7Q or Q) Product Characteristic(s) G
S (DI or DIs) Supplier Data  
� 
Verification: Macro 06 (Y) VPPS G
S (P) GM Part Number G
S (12V) DUNS G
S (4D) Julian Date of 
Production/Assembly G
S
 (7Q) Product Characteristic(s) G
S (DI or DIs) Supplier Data  
� 
Product ID: Macro 06 (P) GM Part Number G
S (12V) DUNS G
S (4D) Julian Date of Production/Assembly G
S 
(7Qor Q) Product Characteristic(s) G
S (DI or DIs) Supplier Data  
E1 Data Required for a Complete GMW15862 Trace Record 
GM assigned part number (8 digits) of the part, component, module, or assembly. 
DUNS ID of the site that manufactured/assembled the part, component, module, or assembly. 
The GMW15862 defined trace code (Figures E1 and E2) 
 
The encoded data looks like this: 
Compliance Header = [)>R
S 
 
 
[)>R
S 
Format Header = 06G
S 
 
 
[)>R
S06G
S 
VPPS DI = Y 
[)>RS06GSY 
VPPS Data = 0000000000000X 
[)>RS06GSY0000000000000X 
Part Number DI = P 
[)>R
S06G
SY0000000000000XG
SP 
Part Number Data = 12345678 
[)>R
S06G
SY0000000000000XG
S********* 
Data Separator = G
S 
[)>R
S06G
SY0000000000000XG
S*********G
S 
DUNS DI =12V 
[)>R
S06G
SY0000000000000XG
S*********G
S12V 
DUNS Data =********* 
[)>R
S06G
SY0000000000000XG
S*********G
S12V********* 
Data Separator = G
S 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
S 
GM Trace Code DI = T 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
ST 
GM Trace Data = LSYYDDDA2B4C6000 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
STLSYYDDDA2B4C6000 
Record Separator = R
S 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
STLSYYDDDA2B4C6000R
S 
End of Transmission = EOT 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
STLSYYDDDA2B4C6000R
S
EOT 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 46 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 46 of 59 
 
 
Figure E1: 2D Symbol Encodation (Example of Verification/Product Identification) 
 
GM DEFINED TRACE STRUCTURE: The following is the GM defined traceability structure that SHALL be in 
use for all new parts, components, assemblies, and modules by date in Traceability NOA (see Labels and 
Literature web page). The GM defined trace structure plus the GM assigned 8 character part number plus the 
manufacturer/assembler site specific DUNS ID constitute the complete traceability record (Figure E2). 
 
 
Figure E2: GMW15862 Defined Traceability for All New Programs and Phased in for Current Part, 
Components, Assemblies, and Modules per Traceability NOA 
 
 
Figure E3: GMW15862 Serial Number Traceability Requirements Label/Mark 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 47 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 47 of 59 
 
 
Figure E4: GMW15862 Lot/Batch Traceability Requirements Label/Mark 
 
 
 
Figure E5: GMW15862 Traceability with Revision Level Requirements (Request to be Made by GM 
Release Engineer) Label/Mark 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 48 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 48 of 59 
 
E2 GMW15862 Verification/Error Proofing and Product ID Structure 
The following is the verification/error proofing structure used for new parts, components and assemblies. 
 
 
Figure E6: GMW15862 Verification/Error Proofing Data Structure 
 
 
 
Figure E7: GMW15862 Verification/Error Proofing or Product ID Label/Mark Examples (Encoded Data 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
S4DYYDDDR
S
EOT) 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 49 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 49 of 59 
 
Appendix F: Appending/Adding Additional Data to the 2D Bar Code 
 
The following procedures (Examples 1 and 2) SHALL be followed to append data to the 2D bar code. 
Examples of types of additional data are product characteristics (voltage, current, pressure, flow rate, 
dimensional, etc.) or supplier specific data (supplier part number, supplier internal traceability code, etc.) 
Encodation follows ISO/IEC 15434 Syntax for High Capacity AIDC Media and ISO/IEC 15418 Information 
Technology – UCC/EAN Application Identifiers and Fact Data Identifiers and Maintenance. 
 
Example 1: Appending Product Characteristic - Pressure Final Test Results 
Scenario: The supplier and the GM release engineer agreed that having the final test stand pressure would 
support product matching for the application and enhance warranty issues. 
Additional Data: 14.7 Pascal. 
Data Identifier selected: 7Q (See Table A1). 
The two character Unit of Measure code as defined in Data Element number 355 of the ANSI X12.3 Data 
Element Dictionary standard: 4S. 
Appending to encoded data structure used in Appendix E: 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
STLSYYDDDA2B4C6000R
S
EOT. 
Step 1. Insert data separator G
S after the GM defined trace code. 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
STLSYYDDDA2B4C6000G
S. 
Step 2. Insert Data Identifier 7Q. 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
STLSYYDDDA2B4C6000G
S7Q. 
Step 3. Insert data value including decimal 14.7. 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
STLSYYDDDA2B4C6000G
S7Q14.7. 
Step 4. Insert qualifier from ANSI X12.3 4S. 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
STLSYYDDDA2B4C6000G
S7Q14.74S. 
Step 5. Since this is the last data field, it is closed by the Format Trailer. 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
STLSYYDDDA2B4C6000G
S7Q14.74SR
S
EOT. 
 
 
Figure F1: Example Label/Mark with Product Characteristic Data Using 7Q Data Identifier Embedded in 
2D Bar Code Not in Human Readable Information 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 50 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 50 of 59 
 
Example 2: Appending Supplier Data – Supplier Part Number and Serial Number 
Scenario: To help support their process, the supplier has requested to add their part number and serial 
number to the Product ID label. Since the GM policy is not to include supplier data on the label, it is 
permissible to put the data in the 2D bar code. 
Note: For this example, this is a Product ID label and the data in the 2D bar code consisted of the GM 
assigned part number, the DUNS ID of the manufacturing/assembly site and the year/Julian date of 
manufacture. 
Appended supplier part number and serial number Data. 
Data Identifiers selected: 1P and S (See Table A1). 
Product Label 2D bar code has the following data encodation. 
[)>R
S06G
SY0000000000000XG
S*********G
S12V*********G
S4DYYDDDR
S
EOT 
 
Step 1. Insert data separator GS after the Julian date field 
[)>RS 06GSY0000000000000XGS*********GS12V*********GS4DYYDDDGS 
Step 2. Insert Data Identifier for supplier part number 1P 
[)>RS 06GSY0000000000000XGS*********GS12V*********GS4DYYDDDGS1P 
Step 3. Insert supplier part number 1A2B3C4D5E6F7G8H9 
[)>RS 06GSY0000000000000XGS*********GS12V*********GS4DYYDDDGS1P1A2B3C4D5E6F7G8H9 
Step 4. Insert data separator GS 
[)>RS 06GSY0000000000000XGS*********GS12V*********GS4DYYDDDGS1P1A2B3C4D5E6F7G8H9GS 
Step 5: Insert Data Identifier for supplier serial number S 
[)>RS 06GSY0000000000000XGS*********GS12V*********GS4DYYDDDGS1P1A2B3C4D5E6F7G8H9GSS 
Step 6. Insert supplier serial number A2B4C000E0 
[)>RS 06GSY0000000000000XGS*********GS12V*********GS4DYYDDDGS1P1A2B3C4D5E6F7G8H9GSSA2B4C000E0 
Step 7. Since this is the last data field it is closed by the Format Trailer 
[)>RS 06GSY0000000000000XGS*********GS12V*********GS4DYYDDDGS1P1A2B3C4D5E6F7G8H9GSSA2B4C000E0RSEOT 
(See Figure F2) 
 
 
Note: Supplier data is not printed in the Human Readable Information but encoded in the 2D bar code only. 
Figure F2: Example Label/Mark with Supplier Data Appended to the 2D Bar Code Data 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 51 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 51 of 59 
 
Appendix G: “As-Built” Label/Mark 
 
The "As-Built" label/mark provides a means of capturing the trace data as part of an external assembly 
process such as a Value Added Assembler (VAA). The 2D bar code is structured to have an individual trace 
record for each component that requires traceability. A fuel tank assembly, which consists of five (5) traceable 
components (Figure G1), will be used as an example of how to create an "As-Built" 2D label/mark with Human 
Readable Information. 
 
 
Figure G1: Illustration of a Fuel Tank Assembly Consisting of Five Traceable Components 
 
The fuel tank is the prime link to which the remaining four components will be associated with. The data 
collection system captures each component as it is assembled to the tank (Table G1). 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 52 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 52 of 59 
 
Table G1: Captured Data from Each of the Components with the Fuel Tank as the Primary Link 
 
 
Using the captured data, the data is encoded into the 2D symbol, following the ISO/IEC 15434 Data Syntax 
standard with the Record Separator character R
S (ASCII ISO/IEC 646 Character decimal 30, 1Eh) (Figure G2). 
 
 
Note: The ASCII non-printable character (30 decimal, 1Eh) is used to separate each record. 
Figure G2: Data Encodation for the 2D Symbol following ISO/IEC 15434 Data Syntax Standard 
 
The "As Built" label/mark would be attached to the fuel tank. The plant system would scan the 2D bar code and 
the data would be sent as a complete traceability record for each of the components that were assembled to 
the fuel tank. The net effect is it appears as through the tank was assembled at the scan station (Figure G3). 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 53 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 53 of 59 
 
 
Figure G3: Completed "As-Built" Label/Mark Affixed to the Fuel Tank 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 54 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 54 of 59 
 
Appendix H: GM1737 Defined Traceability and Verification/Error Proofing Code 
Structures 
 
H1 GM1737 Traceability Structure. (Phasing Out - See Traceability NOA) 
The following is the traceability structure, formerly defined in GM1737, and is to be used for existing parts, 
components, assemblies, and modules. Suppliers SHOULD plan on changing to the GM Defined Trace 
Structure detailed in Section 3 according to the schedule in the Traceability NOA. Electronic modules SHALL 
continue to use this structure with GMW4710 until Electrical Common Architecture is released (Figures H1 
and H2.) 
 
 
Figure H1: GM1737 Traceability Structure to be Phased Out per Traceability NOA 
 
 
Figure H2: GM1737 20T Traceability Label/Mark Examples (Encoded Data 20TCI5678VA2B4C6D8E) 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-


### 第 55 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 55 of 59 
 
H2 GM1737 Enhanced Traceability Structure. (Phasing Out - See Traceability NOA) 
The following is the enhanced traceability structure, formerly defined in GM1737, and is to be used for existing 
parts, components, assemblies, and modules. Suppliers SHALL plan on changing to the GM Defined Trace 
Structure detailed in Section 3 according to the schedule in the Traceability NOA. Electronic modules SHALL 
continue to use this structure with GMW4710 until Electrical Common Architecture is released (Figures H3 
and H4.) 
 
 
Figure H3: Enhanced GM1737 Defined Traceability Structure to be Phased Out per Traceability NOA 
 
 
Figure H4: GM1737 21T Traceability Label/Mark Examples (Encoded Data 21TCI5678VLS7282A2B) 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 56 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 56 of 59 
 
H3 GM1737 Verification/Error Proofing Structure. (Phasing Out - See Traceability 
NOA) 
The following is the verification/error proofing structure formerly defined in GM1737, and is to be used for 
existing parts, components and assemblies. (See Figures H5 and H6.) Suppliers SHALL plan on changing to 
the GM Defined Verification/Error Proofing Structure detailed in Section 3 according to the schedule in the 
Traceability NOA. 
 
 
Figure H5: GM1737 20P Verification/Error Proofing Data Structure 
 
 
Figure H6: GM1737 20P Verification/Error Proofing Label/Mark Examples (Encoded Data 20P5678) 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 57 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 57 of 59 
 
Appendix J: Powertrain Traceability Format 
 
Powertrain released components that are scanned at the vehicle manufacturing plant or VAA SHALL use the 
format shown in Appendix J1 (Tables J1 to J3). Trace code definition is the only difference from the standard 
GM Defined Trace Code format. 
Powertrain released components that are scanned in the Powertrain manufacturing location may use the 
format shown in Appendix J1 (Tables J1 to J3), if space permits. The format shown in Appendix J2 (Tables J4 
to J6) SHALL be used if the Appendix J1 (Tables J1 to J3) format does not fit on the component. 
J1 Traceability Format for Powertrain Released Components that are Scanned by the 
Vehicle Manufacturing Plant or VAA 
The Powertrain trace code definition is the only difference from the standard GM Traceability content. 
 
Table J1: Encodation Definition for Powertrain Released Components Scanned by the Vehicle 
Manufacturing Plant or VAA 
Data Definition 
Data 
Characteristics 
DI Encodation (with DI) 
GM assigned VPPS Compressed Code 
14 alphanumeric 
Y 
Y0000000000000X 
GM Part Number 
8 Numeric 
P 
********* 
Manufacturing or assembly Site DUNS 
9 Numeric 
12V 
12V********* 
GM Defined Trace Code (for Powertrain released components scanned at the 
manufacturing plant or VAA) 
16 alphanumeric 
T 
TLSYYDDD9AAKX1234 
 
 
Table J2: Encodation Layout Showing Control Character and DI Placement for Powertrain Released 
Components Scanned at the Vehicle Manufacturing Plant or VAA 
 
 
 
Table J3: GM Defined Trace Code for Powertrain Released Components Scanned by the Vehicle 
Manufacturing Plant or VAA
 
Default character for position 14 is an “X”. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 58 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 58 of 59 
 
 
Figure J1: Label Layout for Powertrain Released Components that are Scanned by the Vehicle 
Manufacturing Plant or VAA (ATK95040) 
 
 
Figure J2: Example of a Compliant Transmission Label 
 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---


### 第 59 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW15862 
 
© Copyright 2010 General Motors Company All Rights Reserved 
November 2010 
Page 59 of 59 
 
J2 Traceability Format for Powertrain Released Components that are Scanned in the 
Powertrain Manufacturing Facility 
 
Table J4: Encodation Definition for Powertrain Released Components Scanned at the Powertrain Plant 
Data 
Definition 
Data 
Characteristics 
DI 
Encodation 
GM Defined Trace Code (for Powertrain released 
components scanned at Powertrain plant 
16 alphanumeric 
T 
PCLL936512340000 
 
Table J5: Encodation Layout Showing the DI Placement and Data for Powertrain Released Components 
Scanned at the Powertrain Plant 
 
 
 
Table J6: GM Defined Trace Code for Powertrain Released Components 
Scanned at the Powertrain Plant 
 
 
J2.1 Trace Data Fields. The seven data fields in the Powertrain trace code are identified as follows: 
� 
Program ID, Component Type and Site ID shall be supplied by the Powertrain Release Engineer. 
Powertrain engineers can reference Global Quality Requirement (GQR) 120.57 for the standard ID lists. 
� 
Build Year is the last digit of the calendar year 
� 
Julian Day per Appendix B 
� 
Sequence Number is four digits and left padded with zeros. The sequence starts with zero (0) at the 
beginning of each day. 
� 
The optional characters shall be the same format for all sites within a program. If optional characters are 
not used, then place zeros in fields 13 to16. 
J2.2 Human Readable Content. For format J2, the layout of the human readable content is flexible provided 
the human readable characters are legible. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=TRW loc 14 Dusseldorf Germany/********** 
Not for Resale, 01/06/2011 01:57:43 MST
No reproduction or networking permitted without license from IHS
--```,```````````,`,,,,``,```,,-`-`,,`,,`,`,,`---

