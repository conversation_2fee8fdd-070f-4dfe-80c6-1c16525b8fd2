# GMW_3089_EN_2004-12_GMLAN单金属线CAN物理层和数据链层规范.pdf

## 文档信息
- 标题：GMN General Specification Template
- 作者：<PERSON>
- 页数：37

## 文档内容
### 第 1 页
 
 
 
 
 
 
 
 
 
 
WORLWIDE 
ENGINEERING 
STANDARDS 
General Specification 
Electrical/Electronic 
GMW3089 
 
 
 
 
 
 
 
 
 
GMLAN Single Wire CAN Physical and Data Link Layers Specification
 
 
1 Introduction 
1.1 Scope. This document specifies the physical 
layer requirements for a Carrier Sense Multiple 
Access/Collision Resolution (CSMA/CR) data link 
which operates on a single wire medium to 
communicate among Electronic Control Units 
(ECU) on road vehicles at normal transmission 
rates of 33.333 Kbits/s and at a special high speed 
assembly or service transmission bit rate of 83.333 
Kbits/s. 
This document is to be referenced by the particular 
Component 
Technical 
Specification 
which 
describes any given ECU in which the single wire 
data link controller and physical layer interface is 
located. Only the performance of the data link 
physical layer is specified in this document. ECU 
environmental and other requirements shall be 
provided 
in 
the 
Component 
Technical 
Specification. 
The intended audience includes, but is not limited 
to ECU suppliers, Controller Area Network (CAN) 
controller suppliers, CAN transceiver suppliers, 
component release engineers and platform system 
engineers. 
1.2 Mission/Theme. This specification describes 
the physical layer requirements for a single wire 
data link capable of operating with various 
CSMA/CR protocols such as the Bosch Controller 
Area Network (CAN) version 2.0. This serial data 
link network is intended for use in applications 
where high data rate is not required and a lower 
data rate can achieve cost reductions in both the 
physical 
media 
components 
and 
in 
the 
microprocessor and/or dedicated logic devices 
which use the network. 
The network shall be able to operate in either the 
normal data rate mode or a high speed data 
download mode for assembly line and service data 
transfer operations.  The high speed mode is only 
intended to be operational when the bus is 
attached to an off-board service node. This node 
shall provide temporary bus electrical loads which 
facilitate higher speed operation. Such temporary 
loads shall be removed when not performing 
download operations. 
This physical layer specification includes such 
transceiver requirements as are necessary to 
insure that the bus voltage signals and that a 
common IC footprint can be achieved across all 
applications. The transceiver requirements are 
specified in Appendix C. 
1.3 Classification. This specification is not 
restricted. 
2 References 
Note: Only the latest approved standards are 
applicable unless otherwise specified. 
2.1 External Standards/Specifications. 
ISO 11898 
SAE #970295 
ISO 16845 
SAE J2411 
2.2 GM Standards/Specifications. 
GM-J1962 
GMW3103 
GME6718 
GMW3104 
GMW3001 
GMW3122 
GMW3059 
GMW3172 
GMW3091 
GMW3173 
GMW3097 
 
2.3 Additional References.  
Supplier Documents. 
The following devices have been approved for use. 
Any deviations to this spec have been noted. 
• 
Melexis – TH8056 – contact Adrian Hill (248) 
543-0682 for details. In Europe contact 
Michael Bender at +49-361-427-8355. 
• 
Freescale 
(formerly 
Motorola 
SPS) 
– 
MC33897*/R2 – contact Deanna Waun (248) 
347-7354 for details. In Europe contact Norbert 
Pickel at +49-6128-70-2975. This device 
cannot be used in vehicles implementing 
partial networks. This device does not meet the 
maximum recessive voltage when in Sleep 
Mode. The maximum recessive voltage of this 
device in sleep mode is 0.7V. This device shall 
not be used in new designs, it has been 
replaced by MC33897A*/R2. 
• 
Freescale 
(formerly 
Motorola 
SPS) 
– 
MC33897A*/R2 – contact Deanna Waun (248) 
347-7354 for details. In Europe contact Norbert 
Pickel at +49-6128-70-2975. This device will 
be designed to operate with partial networks 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Originating Department: North American Engineering Standards 
Page 1 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 2 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
and will meet the specified maximum recessive 
voltage in sleep mode. 
The following devices are under development and 
are not approved for use to date.  
On Semiconductor – NCV7356 – contact Tom 
Bricely (734) 953-6848 for details. In Europe 
contact Klaus Reindl at +49-89-930808-32. 
The following supplier produced transceivers for 
version 1.26 of GMW3089. They are currently 
considering designing a device to these new 
requirements. 
Philips – contact Dan Moore (248) 699-2264 for 
details 
The following supplier produced transceivers for 
version 1.26 of GMW3089. They currently have no 
plans 
to 
design 
a 
device 
to 
these 
new 
requirements 
Infineon – contact Dario David (248) 374-2505 for 
details 
Other Documents - Bosch Controller Area Network 
version 2.0 specification (only with recessive to 
dominant bit synchronization). 
3 Requirements 
The physical layer is responsible for providing a 
method of transferring digital data symbols (1’s and 
0’s) to the communication medium. The physical 
layer interface is a single wire, ground referenced 
bus with high side voltage drive. Requirements in 
this document generally apply over the specified 
operating conditions and life time unless otherwise 
noted. 
3.1 
Physical 
and 
Data 
Link 
Layer 
Characteristics. 
a. Carrier 
Sense 
Multiple 
Access/Collision 
Resolution 
(CSMA/CR) 
bus 
access 
and 
arbitration with dominant high and recessive 
low signal voltages. 
b. Only 
performs 
bit 
re-synchronization 
on 
recessive to dominant bit transitions. 
c. The CAN controller shall be capable of 
transmitting and receiving messages with both 
standard frame format, i.e. 11 bit CAN 
identifiers, and extended frame format, i.e. 29 
bit CAN identifiers (CAN 2.0B active). The 
CAN controller shall support mixed mode 
operation with CAN identifiers of both 29-bit 
and 11-bit in length. Alternatively tolerant of 
messages with extended frame format (CAN 
2.0B passive) if explicitly called out per SSTS, 
CTS or platform-specific bus implementation 
specification. The CAN controller shall be fully 
compatible to ISO 11898. For example the 
enhanced protocol for higher clock tolerance 
must be supported (e.g. tolerate 2 bit message 
intermission). 
d. 
Compliance to ISO 11898 shall be verified 
through CAN conformance testing according 
to the ISO 16845 test plan. Successful 
passing of the complete conformance test 
shall be documented through provision of a 
written 
statement 
of 
the 
semiconductor 
manufacturer. The test plan conformance 
declaration 
shall 
indicate 
which 
product 
version was tested and which version of the 
CAN conformance test plan was used as 
reference. 
e. 
The controller must support the complete 
range of resynchronization jump width settings 
specified in ISO 11898. In particular, a bit 
timing parameter setting of SJW = 2 time 
quanta = PHASE_SEG2 must be supported. 
Also, the resynchronization function must be 
operational when a synchronization edge is 
detected outside the range set by the SJW 
parameter, i.e. when the phase error is greater 
than the SJW, the CAN controller shall adjust 
the timing of the current bit by SJW time 
quanta. 
f. 
The controller must perform at most one 
hard- and/or re-synchronization action per 
CAN bit time, the time between any two 
sample points. The earliest point in time when 
the next re-synchronization is permitted is 
after the sample point of the current bit as 
determined 
by 
the 
most 
recent 
bit 
resynchronization action. This means after any 
bit resynchronization action (hard- and/or re-
sync) the resynchronization function must be 
disabled until the currently pending bit 
sampling has been concluded. This would 
allow the next resynchronization (at the 
earliest) in TSEG2 of this bit if the sample 
result was “recessive”. This case might occur 
due to a glitch on the bus. Note this 
requirement 
applies 
to 
both 
the 
hard-
synchronization function at the start of a frame 
as well as to the re-synchronization function 
while frame transmission/reception is ongoing. 
g. 
When the bus is idle and a recessive to 
dominant signal edge has occurred then the 
controller shall behave in the following 
manner: 
1. The controller must sample the bit value at 
the specified time after this signal edge 
and must disregard any consecutive edges 
until sampling of the current bit has been 
concluded (e.g. hard sync function must be 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 2 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 3 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
disabled immediately upon receipt of the 
first r-d edge after an idle period). 
2. If the sampling result is “dominant”, then 
this event shall be taken as a valid start of 
frame bit. 
3. Otherwise, if the sampling result is 
“recessive”, then the edge shall be 
considered as a glitch and shall be 
disregarded. In particular, there must not 
be any error frame transmission due to a 
glitch while the bus is idle. 
h. 
It is not recommended to feed CAN controllers 
with a PLL-generated clock signal. If a CAN 
controller is operated with a PLL clock, then 
careful analysis of the implications of the 
additional clock jitter is required. As a 
preventive 
measure 
extra 
accuracy 
requirements on the oscillator clock apply in 
this case, see 3.2.4. 
i. 
The information processing time of the 
protocol controller must be equal to 2 time 
quanta or less. 
j. 
The CAN controller may employ a message 
buffer concept with or without Dual Ported 
RAM (DPRAM). 
k. 
The CAN protocol controller shall provide at 
least 2 transmit message buffers and 2 
receive message buffers. This is to ensure 
that higher priority messages are not blocked 
by lower priority messages and that new 
messages are not ‘dropped’ while the CPU is 
still reading the current message. 
l. 
Any time when more than one transmit buffer 
is armed for transmission, then the protocol 
controller shall automatically (e.g. without any 
CPU-support) transmit the message with the 
lowest CAN identifier first. 
m. When two or more messages with the same 
CAN identifier are armed for transmission, 
then the protocol controller shall transmit 
these messages in a FIFO fashion, i.e. in the 
sequence how they were armed. 
3.2 Bus Operation. 
3.2.1 Normal Communication. Transmission bit 
rate in normal communication is 33.333 Kbits/s. 
Normal transmission mode shall use transmitters 
with controlled waveform rise and overshoot times. 
Waveform trailing edge control is required to 
assure that high frequency components are 
minimized at the beginning of the downward 
voltage slope. The remaining fall time occurs after 
the bus is inactive with drivers off and is 
determined by the RC time constant of the total 
bus load. 
3.2.1.1 CAN Controller Setup. The following table 
defines all compliant bit timing settings for 
operation 
at 
33.333 
kb/s 
including 
the 
recommended controller settings for TSEG1 and 
TSEG2. See appendix G for information on how 
these 
values 
were 
calculated.
 
Table 1:  33.333 kb/s Compliant Bit Timing Settings 
tQ 
tSJW 
tSEG2min 
SJW 
TSEG1 
TSEG2 
1.5 µs 
4.5 µs 
4.5 µs 
2 (3 tq) 
15 (16 tq) 
2 (3 tq) 
1.58 µs 
3.16 µs 
3.16 µs 
1 (2 tq) 
15 (16 tq) 
1 (2 tq) 
1.67 µs 
3.33 µs 
3.33 µs 
1 (2 tq) 
14 (15 tq) 
1 (2 tq) 
1.76 µs 
3.52 µs 
3.52 µs 
1 (2 tq) 
13 (14 tq) 
1 (2 tq) 
1.88 µs 
3.76 µs 
3.76 µs 
1 (2 tq) 
12 (13 tq) 
1 (2 tq) 
2.0 µs 
4.0 µs 
4.0 µs 
1 (2 tq) 
11 (12 tq) 
1 (2 tq) 
 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 3 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 4 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
3.2.2 High Speed Communication. Transmission 
bit rate in high speed communication is 83.333 
Kbits/s. This mode is used for assembly line and 
service data download when the on-board network 
is attached to an off-board ECU. High speed 
transmission mode allows shortened bit time and 
shortened waveform rise and fall times. EMC 
requirements in 3.5 Radiated EMI protection are 
waived while in this mode. Bus transmitter drive 
circuits for those nodes which are required to 
communicate in high speed mode shall be able to 
drive reduced bus resistance when in this mode 
(see Table 1). High speed communications shall 
utilize the normal mode signal voltage levels as 
specified in Table 1. 
The test tool must connect Rtool to the bus before 
transmitting any high speed messages. Rtool shall 
be connected directly to ground on the ECU via an 
appropriate switch (e.g. transistor or relay). Rtool 
shall never be connected to the Load pin of the 
transceiver. The Load pin is not designed to handle 
the high level of current through Rtool. 
All nodes shall be able to operate at the High 
Speed mode frame timing and shall enter this 
mode when they have been commanded by the 
download system manager. The normal sequence 
of entering and leaving high speed mode is as 
follows, commanded by the download system 
manager (additional steps may be inserted by the 
download system manager as needed): 
a. All nodes are awakened at normal bus speed 
b. The download tool switches the bus circuit to 
the lower tool bus resistance 
c. All nodes are commanded to go to high speed 
mode 
d. High speed traffic is completed 
e. All nodes are commanded to go to normal 
speed mode 
f. 
The download tool switches the tool bus 
resistance out of the bus circuit 
Nodes shall not be allowed to go to sleep during 
high speed mode. If they are asleep and then get 
an input which would require them to send a 
message, they will corrupt the service activity. 
Nodes shall initialize in the normal speed mode 
after a power up, running reset or CAN bus off 
condition. 
3.2.2.1 CAN Controller Setup. The following table 
defines all compliant bit timing settings for 
operation at 83.333 kb/s. 
 
Table 2: 83.333 kb/s Compliant Bit Timing Settings 
tQ 
tSJWmin 
tSEG2min 
SJW 
TSEG1 
TSEG2 
0.6 µs 
1.8 µs 
1.8 µs 
2 (3 tq) 
15 (16 tq) 
2 (3 tq) 
0.632 µs 
1.896 µs 
1.896 µs 
2 (3 tq) 
14 (15 tq) 
2 (3 tq) 
0.667 µs 
2.0 
2.0 µs 
2 (3 tq) 
13 (14 tq) 
2 (3 tq) 
0.706 µs 
2.118 µs 
2.118 µs 
2 (3 tq) 
12 (13 tq) 
2 (3 tq) 
0.75 µs 
1.5 µs 
2.25 µs 
2 (3tq) 
11 (12 tq) 
2 (3 tq) 
0.8 µs 
1. 6 µs 
1.6 µs 
2 (3 tq) 
10 (11 tq) 
2 (3 tq) 
0.857 µs 
1.714 µs 
1.714 µs 
2 (3 tq) 
9 (10 tq) 
2 (3 tq) 
0.923 µs 
1.846 µs 
1.846 µs 
1 (2 tq) 
9 (10 tq) 
1 (2 tq) 
1.0 µs 
2.0 µs 
2.0 µs 
1 (2 tq) 
8 (9 tq) 
1 (2 tq) 
 
3.2.3 ECU Selective Awake. This bus includes a 
selective node awake capability, which allows 
normal communication to take place among some 
nodes while leaving the other nodes in an 
undisturbed sleep state. This is accomplished by 
controlling the signal voltages such that all nodes 
must wake up when they receive a higher voltage 
message signal waveform. The communication 
system communicates to the nodes information as 
to which nodes are to stay operational (awake) and 
which nodes are to put themselves into a non 
communicating 
low 
power 
“sleep” 
state. 
Communication at the lower, normal voltage levels 
shall not disturb the sleeping nodes. 
The transceiver’s loss of ground protection circuit 
connection to ground shall not be interrupted when 
in the sleep mode. To accomplish this, all nodes 
shall maintain Vbatt on the transceiver at all times 
when any serial communication can take place. 
This is to ensure that the unit resistive loads shall 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 4 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 5 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
not be removed from the network timing circuit. 
The recommended method of power moding serial 
communications nodes shall be by use of the 
network sleep mode.  
The High Speed and High Voltage wake up 
features shall not be active at the same time. This 
requires that the bus be awakened only when in 
the normal speed mode.  
Methods and connections for host wake up circuits: 
• 
CAN controller in the “wake up on bus activity” 
sleep mode. Upon receipt of a wake up bus 
signal, the transceiver RxD output shall pass 
received bus signals to the controller’s RxD 
input which will awaken the controller and the 
controller will then issue a wake up signal or 
interrupt to the local microprocessor to begin 
CAN operations.  
In this case, the controller is typically in an 
intermediate sleep state, which is not the 
lowest sleep power consumption state. 
• 
Microprocessor connects the transceiver’s RxD 
pin to a latched port as a wake up input. 
Upon receipt of a wake up bus signal, the 
transceiver RxD output shall pass received bus 
signals to the µP’s latched wake up input.  The 
latched RxD signal shall wake up the µP which 
then initializes the CAN controller and enables 
CAN operation. The latch is reset after the µP 
has put the transceiver into another sleep 
mode before the µP puts the ECU to sleep. 
In this case, the controller is typically in the 
lowest power consumption sleep state. 
• 
ECU with transceiver implementing Vreg 
Control pin 
Devices, which implement a transceiver with a 
Vreg Control pin will wake up when VCC is 
applied due to a serial data wake-up event 
occurring. In order to remain awake, the mode 
control inputs of the transceiver must be 
changed to any mode other than Sleep, e.g. by 
the microcontroller. 
3.2.4 CAN Controller Clock Tolerance. All nodes 
which operate on the Single Wire CAN bus shall 
utilize a CAN controller with a bit timing clock 
tolerance of less than or equal to ± 0.35% 
(alternatively ± 0.5% if called out per SSTS, CTS 
or 
platform-specific 
bus 
implementation 
specification) to ensure that the CAN bit timing 
synchronization shall not be affected by the 
waveform rise and fall times. See Appendix E. 
3.2.4.1 PLL Clock Tolerance. Careful analysis of 
the bit time tolerance is recommended when PLL 
clocks are considered. The permitted tolerance of 
the oscillator circuit is reduced when a PLL clock is 
used for the CAN data link layer controller. For 
example, the suitable oscillator tolerance would be 
0.05% in the case where the PLL circuit exhibits an 
add-on tolerance of 0.3%. 
When a ceramic resonator with a maximum 
tolerance of 0.3% is used with a PLL, the add-on 
clock tolerance of the PLL is limited to 0.05% over 
a single bit time. In normal speed mode, this is 
equivalent to 0.05% of 30 us, or 15 ns max jitter 
over 30 us. In high speed mode, this is equivalent 
to 0.05% of 12 us, or 6 ns max jitter over 12 us. 
3.2.5 Bus Electrical Parameters. This section 
describes two possible modes of the bus electrical 
voltage level parameters required by devices which 
drive and receive signals on the single wire bus. 
They 
are 
the 
normal 
and 
the 
wake 
up 
communications signal levels. See Table 1. The 
high level signals (Voh , Vih , Voh wu and Vih wu ) of 
Table 1 are considered to be the dominant or 
driven-to-high-voltage bus states. 
The High Speed and High Voltage wake up bus 
signals shall not be active at the same time. 
3.2.5.1 Normal mode transmit and receive 
voltage thresholds for data communication. 
Serial communication shall operate with normal 
signal voltage levels indicated in Table 1 without 
generating a wake up signal or interrupt to the 
ECU’s controller when the battery voltage is 
sufficient to prevent this. See Table 1 below. 
3.2.5.2 High Speed mode transmit and receive 
signal voltages. The high speed input threshold 
and output signal voltages are the same as the 
normal mode signals. See Table 1 below. 
High speed operation dominant bus symbols shall 
not exceed the transceiver duty cycles, operating 
voltage, 
and 
temperature 
requirements 
as 
specified in Appendix C – Single Wire CAN 
Transceiver Specification. 
******* Wake Up Signal Voltage Levels. A 
special higher voltage communication waveform 
using higher signal voltages shall be used to 
transmit wake up message frames. Transceivers 
which detect that the signal voltage has exceeded 
the wake up threshold for the minimum wakeup 
filter time (see Table C1) shall provide a wakeup 
signal to its ECU. The CAN wakeup frame shall 
provide at least three consecutive normal speed 
dominant bit times either in the Identifier or in the 
data fields. The wakeup frame shall not be sent 
when in the high speed mode (see 3.2.2 High 
Speed Communication). For Vbatt > 12.7 volts, 
messages which are transmitted at the normal 
voltage levels shall not wake up any receiving 
ECU. 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 5 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 6 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
Messages which are transmitted at the higher 
wake up voltage levels shall meet normal bit timing 
rules at the normal input threshold voltage Vih; so 
as to be interpreted as normal data as well as 
wake up commands. See Table 3 below. 
3.2.6 Single Wire CAN Bus Signals and Loading 
Requirements. The below parameter limits shall be 
met over the specified operating conditions 
including the specified bus loading range. Testing 
shall be performed using the particular application 
circuit, see 3.12. 
The operating conditions are defined as follows:
 
Table 3:  Single Wire CAN Bus Signals Loading Requirements 
Operating Condition Parameter 
Symbol 
Min. 
Typical 
Max. 
Units 
ECU and Test Tool Operational Battery Voltage Input 
except high speed mode1 
Vbatt 
7.0 
12 
18 
volts 
Low Battery ECU Operating Supply Voltage except 
in high speed mode and sleep mode 
Vbatt low 
6.0 
--- 
 
7.0 
Volts 
Short-Duration High Battery ECU Operating Supply 
Voltage (t < 1 min, Tamb < 85 oC) except in high 
speed mode 
Vbatt high 
18 
24 
26.5 
Volts 
ECU Battery Voltage Input in high speed mode1 
Vbatt 
9 
12 
16 
volts 
Test Tool Battery Voltage Input in high speed mode1 
Vbatt 
9 
12 
14 
volts 
Ground Offset Voltage  
Vg off 
--- 
--- 
1.3 
volts 
Low Battery Ground Offset Voltage 
Vg off low batt
0.0 
0.1 Vbatt 
0.7 Vbatt –
3.6 
 
volts 
Battery ECU Offset Voltage when sleeping devices 
shall be activated 
Vb off 
--- 
--- 
1.0 
volts 
Network Total Resistance except in High Speed 
Mode 2,4 
Rtl 
200 
--- 
3332 
Ω 
• 
High Speed Mode Network Resistance to GND 
Rload 
75 
--- 
135 
Ω 
Network Total Capacitance in networks with 0.35% 
CAN clock tolerance 2,14, 15 
Ctl 
396 
--- 
 19 000 
pF 
Network Total Capacitance in networks with 0.5% 
CAN clock tolerance 2,14, 15 
Ctl 0.5% 
396 
--- 
 17 000 
pF 
Network Time Constant in networks with 0.35% CAN 
clock tolerance 2,15 
τnetwork 
1.0  
--- 
4.0 
µs 
Network Time Constant in networks with 0.5% CAN 
clock tolerance 2,15 
τnetwork 
0.5% 
1.0  
--- 
3.6 
µs 
Network Time Constant in High Speed mode 3 
τhs 
--- 
--- 
1.5 
µs 
Number of system nodes including off vehicle test 
tool 
 
2 
--- 
32 
 
The below parameter limits shall be met over specified operating conditions and component lifetime. 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 6 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 7 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
 
Device Behavioral Parameter 5 
Symbol 
Min. 
Typical 
Max. 
Units 
Offset Wakeup Input High Voltage Threshold 10, 11 
VihWuOffset 
Vbatt- 5.3 
--- 
Vbatt - 3.65 
volts 
Offset Wakeup Output High Voltage 12 
Voh wu offset
Vbatt – 2.7  
--- 
Vbatt 
volts 
Fixed Wakeup Input High Voltage Threshold 10, 11 
VihWuFixed 
6.6 
--- 
7.9 
volts 
Fixed Wakeup Output High Voltage 12 
VohWuFixed 
9.5 
--- 
13 (12.5 
preferred) 
volts 
High Speed & Normal Mode Output High Voltage 9 
Voh 
4.2 
normal, 
3.9 high-
speed 
4.65 
5.1 
volts 
Low Battery Output High Voltage (6.0 <Vbatt < 7.0)  
Voh low Batt 
Vbatt - 2.8  
--- 
5.1 
volts 
• 
High Speed & Normal Mode Input High Voltage 
Input Threshold 
Vih 
2.0 
2.1 
2.2 
volts 
Normal Bus recessive or passive state low voltage 6 
Vleaknorm 
-0.2 
0 
0.2 
volts 
LOG condition Bus recessive or passive state low 
voltage 6 
Vleak LOG 
-0.2 
--- 
0.5 
volts 
Device Resistance (unit load) (including LOG switch 
resistance) 
Rul 
 6435 
 6490 
6665 
Ω 
Device Resistance (primary load) 
Rpl 
2949 
 
3010 
 
3170 
 
Ω 
Device Resistance (min load) 
Rmin 
2000 
--- 
--- 
Ω 
Device Capacitance (includes unit load and PCB & 
connector capacitance & ESD protection if required) 
Cul 
135 
150 
300 
pF 
High Speed Mode Tool Resistance to GND (including 
tool’s switch resistance to ground) 
Rtool 
120 
130 
140 
Ω 
Normal mode signal delay + transition time bus rising  
edge 4,13 
Tt norm 
2.0 
--- 
6.3 
µs 
Normal mode signal delay + transition time bus falling 
edge4,13 
Tt norm 
1.8 
--- 
10.0 
µs 
High speed mode signal delay + transition time – bus 
rising edge 16 
Tt hs 
0.1 
--- 
1.7 
µs 
High speed mode signal delay + transition time – bus 
falling edge 16 
Tt hs 
0.04 
 
--- 
3.0 
 
µs 
HVWU Mode signal delay + transition time bus rising 
edge4,13 
tf-wu 
2.0 
--- 
6.3 
µs 
HVWU Mode signal delay + transition time bus falling 
edge in networks with 0.35% CAN clock tolerance 4,13 
tf-wu 
1.8 
--- 
14.0 at 4.0 
µs τ  
µs 
HVWU Mode signal delay + transition time bus falling 
edge in networks with 0.5% CAN clock tolerance 4,13 
tf-wu 0.5% 
1.8 
--- 
12.7 at 3.6 
µs τ  
µs 
Normal mode, HVWU mode and High speed mode 
receive delay time 
Trx 
0.2 
--- 
1.0 
µs 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 7 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 8 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
Device Behavioral Parameter 5 
Symbol 
Min. 
Typical
Max. 
Units 
• 
Device Leakage Current to bus with no loss of 
ground 
Ileak 
--- 
--- 
10 
µAmp 
Total device Leakage Current to bus with loss of 
ground 
Ileak LOG 
--- 
--- 
100 
µAmp 
Network distance between any two ECU nodes 
bus 
length 
--- 
--- 
determined 
by network 
time 
constant 
meters
Notes:   
1. 
Vbatt is measured at the ECU input power pins. All voltages are referenced to the local ECU ground pin. 
2. 
The minimum Network Time Constant and Signal Transition Time are determined by the EMC requirements. If these values are set 
too low the ECU will not pass the required EMC tests. 
3. 
The high speed mode network time constant (τhs) is the product of (Rtool in parallel with Rtl) and Ctl. This time constant only applies 
when operating in the high speed mode with the tool resistor connected. 
4. 
The signal transition time must be maintained regardless of bus RC distribution. 
5. 
ECU’s shall not transmit unless all parameters in Table 1 are fully operational. 
6. 
The bus recessive state low voltage is the offset due to the ECU high side driver leakage current (Ileak and does not include the 
ground offset voltage (Vg off). This leakage current limitation shall be in effect over the 6.0 < Vbatt 
 < 26.5 volts (see 3.10 Operating 
Battery Power Voltage Range and ******* Bus Passive State Voltage Offset (Vleak))) 
7. 
Replaced by 14. 
8. 
The wake up signal threshold shall not generate a wake up signal to the ECU unless the threshold has been exceeded for a minimum 
delay time. (see ******* Wake up signal voltage levels). 
9. 
The Voh wu value is measured at the ECU bus pin. It includes an allowance for the voltage drop across the inductor dc resistance (See 
3.6.2 Line Inductor). 
10. The minimum transceiver input wake up voltage level shall be the smaller of VihWuFixed minimum or VihWuOffset minimum.  
11. The maximum transceiver input wake up voltage level shall be the smaller of VihWuFixed maximum or VihWuOffset maximum. 
12. The minimum transceiver output wake up voltage level shall be the smaller of VohWuFixed minimum or VohWuOffset minimum. 
13. Signal delay time is measured from Vcmos il on the TxD input pin to the VihMax + Vgndoff level on the Bus Input/Output pin for a recessive 
to dominant edge and is measured from Vcmos ih on the TxD input pin to the VihMin - Vgndoff level on the Bus Input/Output pin for a 
dominant to recessive edge. It includes both the internal transceiver propagation delay and the waveform rise time. The minimum 
value should be measured with the minimum network time constant and the maximum value should be measured with the maximum 
normal network time constant. 
14. The Network total capacitance includes the capacitors placed on the ECUs as well as the capacitance of the bus wires, connectors, 
splices, PCB traces, etc. 
15. The network time constant incorporates the bus wiring capacitance. The minimum value is selected to limit radiated emissions. The 
maximum value is selected to ensure proper communication under all communication modes and is the absolute maximum allowed 
under normal and error operating conditions. This should be considered when determining the fusing for the vehicle. Not all 
combinations of R and C are possible. Only those combinations of R and C, bus length, and PCB trace capacitance, etc. are possible 
that meet the specified network time constant. 
16 
High speed mode signal delay time is measured from Vcmos il on the TxD input pin to the VihMax + Vgndoff level on the Bus Input/Output 
pin with Rtool present for a recessive to dominant edge and is measured from Vcmos ih on the TxD input pin to the VihMin - Vgndoff level on 
the Bus Input/Output pin with Rtool present for a dominant to recessive edge. It includes both the internal transceiver propagation 
delay and the waveform rise time. The minimum value should be measured with the minimum network time constant and the 
maximum value should be measured with the maximum normal network time constant. 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 8 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 9 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
******* Bus Passive State Voltage Offset (Vleak). 
The bus input/output circuit shall operate under the 
following limitations. 
Note: This circuit shall only source current to the 
bus. 
a. When a node is awake and the bus is in the 
recessive state, the absolute maximum value 
of Vleak which may be provided by the node 
output current shall be as shown in Table 1 
over the bus load resistance range 75 Ω < R.tl 
< 6.49 + 1% Kohms 
b. In the sleep mode without a loss of ground, the 
node leakage current shall be as shown in 
Table 1. 
c. In any node or bus state, the contribution to 
bus leakage current by any node, which has a 
loss of ground condition shall be as specified in 
Table 1. 
3.3 Network Topology. The number of nodes, the 
total wire length of the system, the number and 
type of primary loads and the clock tolerance are 
all interrelated. Appendix H provides tables to be 
used in designing a SWCAN system to ensure that 
there are no errors due to the chosen system 
topology. (An interactive Excel spreadsheet is 
available to System Architecture Engineers to 
determine the best system topology for each 
vehicle.) 
3.3.1 Network Wiring. Vehicle bus wiring shall 
conform 
to 
GMW3173 
- 
GMLAN 
Wiring 
Requirements. 
3.3.2 Bus Electrical Load. 
******* Distribution. The vehicle single wire 
communications bus may be configured with any 
combination and location of load circuits which 
meet the following criteria: 
a. Each ECU shall contain at least one unit 
device capacitance load and the series 
inductance - see 3.6.1 Capacitor. 
b. Bus resistor unit loads (Rul) may be uniformly 
distributed as unit loads among all ECU’s or 
there may be one or more unit loads grouped 
together 
at 
a 
single 
physical 
location. 
Capacitive unit loads (Cul) are a part of the 
ECU EMC/ESD protection and may not be 
grouped at a single physical location.  
c. The 
total 
network 
equivalent 
minimum 
resistance (Rtl) and maximum capacitance (Ctl) 
shall comply with the totals specified in Table 
1. 
d. Primary loads may be implemented in some 
ECUs in a vehicle network to lower the overall 
network time constant and enable longer wire 
lengths. A primary load is equivalent to 2.1 
Rul’s (3.01K or 3.00K, 1%). 
******* Power Dissipation. 
a. 
The standard unit load resistor of 6.49K shall 
be able to dissipate a minimum of 0.12W at 
70°C. 
b. The standard primary load resistor of 3.01K 
shall be able to dissipate a minimum of 0.25W 
at 70°C. 
c. The tool resistor load for high speed mode 
shall be able to dissipate a minimum of 2.0W 
at 70°C. 
3.3.3 Bus Wire Length. There shall be no more 
than 60 meters between any two network system 
ECU nodes (including off-vehicle service test tools) 
to accommodate 7 ns per meter time delay effects 
on CAN bit synchronization timing requirements. 
Bus wire length is also limited by the wire 
capacitance when routed close to body ground 
planes and/or harness ground wires. See Table 1, 
Note 14 for the limits for this effect on network time 
constant. 
3.3.4 Loss of Ground Protection. The loss of 
ground by any single ECU, with or without an 
accompanying loss of Vbatt , shall not cause any 
bus voltage offset that will disable normal 
communications (See Table 1). This shall be 
accomplished by including a loss of ground 
protection circuit in each ECU which shall 
disconnect the path from the bus to Vbatt when a 
loss of ground occurs. 
3.4 ESD Protection. For information: The ECU 
CANH I/O pin shall withstand the following 
electrostatic discharges (ESD) without any damage 
to the ECU when subjected to the GMW3097 
3.2.1.4.1, 3.2.1.4.2.2 and 3.2.1.4.3 tests. 
Table 4:  Electrostatic Discharges 
ECU 
condition 
contact 
air   (non-
contact) 
Test Required 
Unpowered 
± 6 KV 
± 8 KV 
GMW3097 
3.2.1.4.3 
Powered 
± 8 KV 
± 15 KV  
GMW3097 
3.2.1.4.2 
Depending on the network architecture being in a 
particular platform application, a reduced ESD 
severity level may be sufficient in certain cases, 
see GMW3097 and SSTS and/or CTS. 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 9 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 10 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
3.4.1 ESD Transient Suppressor. If needed to 
provide ECU ESD immunity, circuit element such 
as a transorb, back-to-back zener or a varistor 
device may be added to the network in one or 
more places to provide ESD protection. See Figure 
2. The preferred method is that a central ESD 
suppressor be located in or near the Data Link 
Connector (DLC) if the individual ECUs are able to 
meet the unpowered ESD requirement. This 
central ESD suppression system shall be designed 
such that the suppressor cannot be disconnected 
while the DLC connector remains exposed and 
connected to any vehicle ECUs. See Figure 2 for 
the ESD component electrical location on the data 
link bus. 
When ESD suppressor devices are used they may 
add capacitance or introduce voltage and/or 
temperature variability to the network time constant 
and/or the bus maximum network capacitance (see 
Table 1). When central ESD protection is used, the 
maximum wire length allowed is reduced so the 
total network time constant is not affected. When 
ESD protection is implemented in an individual 
ECU, the local load capacitor must be reduced by 
the amount added by the ESD protection to keep 
the device time constant correct. 
3.5 Radiated EMI Protection. ECUs shall conform 
to the electromagnetic interference requirements 
as indicated in the following GM EMC test 
specifications. 
a. GMW3097. 
General 
Specification 
for 
Electrical/Electronic 
Components 
and 
Subsystems, 
Electromagnetic 
Compatibility 
(EMC), Requirements and Verification 
b. GMW3103. 
General 
Specification 
for 
Electrical/Electronic 
Components 
and 
Subsystems, 
Electromagnetic 
Compatibility 
(EMC), 
Component/Subsystem 
Validation 
Acceptance Process 
3.6 Bus Protection and Transient Suppression. 
The following devices may need to be added or, if 
already specified, shall meet these additional 
requirements to prevent damage to the bus 
components in the event of ESD and voltage 
transients, or to help in the meeting of the EMC 
requirements. See Figure 2. 
3.6.1 Capacitor. The load capacitor shall have a 
minimum Direct Current Working Voltage (WVDC) 
of 100 volts. Please note that capacitors smaller 
than 0805 may not be suitable. Typically type NPO 
and X7R types are suitable. 
3.6.2 Line Inductor. An inductor (typical L = 47 
µH, ESR see Table 3.6.2, e.g. Epcos B82432-
A1473-K size 1812), if required by the ECU to 
meet EMI susceptibility requirements, shall be 
placed between the Single Wire CAN device and 
the load capacitor. The dc series resistance of this 
inductor shall be less than 3.5 Ohms. The line 
inductor needs to support a current of at least 70 
mA DC and a transient current of up to 350 mA for 
t < 20 ms. The power dissipation rating of the 
inductor shall be appropriate to a maximum loaded 
network under worst case environmental and 
electrical conditions. The inductor shall also be 
capable 
of 
withstanding 
the 
specified 
ESD 
conditions and fault tolerant modes e.g. short-
circuit of the bus line to ground. 
If the inductor is not needed in a particular 
application, it is required that a zero ohm resistor 
be installed in the circuit at the inductor pc board 
location as a means of later installing the inductor 
for those vehicles and transceivers that may need 
the inductor. The PCB layout shall accomodate the 
inductor package sizes of 1210 and 1812 (SMD). 
Table 5:  Line Inductor Characteristics 
Line Inductor Characteristic 
Symbol 
Min. 
Typical
Max. 
Units 
Nominal Inductance 
Lind 
--- 
--- 
47 
µH 
EMC Series Inductor Resistance ( if required) 
Rind 
--- 
--- 
3.5 
Ohms 
• 
DC Current Capability of EMC inductor  
Iind DC 
70 
--- 
--- 
mA 
Transient Current Capability of EMC inductor and 
reverse battery diode for t < 100 ms 
Iind 100ms 
350 
--- 
--- 
• 
mA
 
3.7 Fault Tolerant Modes. The Network shall 
meet the requirements as defined per the following 
failure modes: 
a. 
ECU Power Loss – When up to 20 percent 
of the vehicle ECUs lose connection to 
power 
or 
experience 
a 
low-power 
condition, then they shall not interfere with 
normal 
communication 
among 
the 
remaining bus ECUs. The effective ECU 
bus load resistance shall be within ± 20% 
of the nominal value in the ECU(s). Upon 
return of power, normal operation shall 
resume without any operator intervention 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 10 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 11 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
within a time period specified by the ECU 
Component Technical Specification. 
b. 
Bus Short to Ground - Network data 
communications may be interrupted but 
there shall be no damage to any ECU 
when the bus is shorted to ground. A 
network impedance of less than 50 ohms 
between the bus and ground shall be 
considered 
a 
short 
to 
ground 
and 
continued 
communications 
are 
not 
guaranteed or required. Upon removal of 
the fault, normal operation shall resume 
without any operator intervention within a 
time 
period 
specified 
by 
the 
ECU 
Component Technical Specification. 
c. 
Bus Short to Battery - Network data 
communications may be interrupted but 
there shall be no damage to any device 
when the bus is shorted to positive battery 
less than 26.5 volts (Vbatt < 26.5 volts). A 
network impedance of less than 50 ohms 
between the bus and battery shall be 
considered 
a 
short 
and 
continued 
communications are not guaranteed or 
required. Upon removal of the fault, normal 
operation 
shall 
resume 
without 
any 
operator intervention within a time period 
specified 
by 
the 
ECU 
Component 
Technical Specification. 
d. 
Loss of ECU Connection to Ground - 
When up to 10% of the vehicle ECUs lose 
their ground connection, this shall not 
prevent the remaining ECUs from normal 
communication. 
The 
ECU(s) 
shall 
implement this protection by interrupting 
the ground path to the bus load resistor 
when a loss of ground occurs. See 
Appendix A - method 3. Upon removal of 
the fault, normal operation shall resume 
without any operator intervention within a 
time 
period 
specified 
by 
the 
ECU 
Component Technical Specification. 
e. 
A short or open in any single circuit of an 
ECU, except for power, ground, or serial 
data, shall not preclude the ability to 
communicate with that ECU for diagnostic 
purposes. 
3.8 Transceiver Pin Out and Footprint. The 
transceiver shall have the printed circuit board pin 
assignments and footprint as shown in Appendix 
C. 
3.9 Ground Offset Voltage. Ground offset voltage 
limits at the ECU as specified in Table 1 must be 
maintained over the entire range of 6.0 < Vbatt < 
26.5 volts. 
3.10 Operating Battery Power Voltage Range. 
3.10.1 Normal Battery Voltage Power Operation. 
Unless otherwise specified by the Component 
Technical Specification, ECUs shall be capable of 
meeting 
all 
requirements 
specified 
in 
this 
document when the Vbatt ECU voltage as measured 
at the ECU power input pin is within the range of 
7.0 to 18 volts DC, except for the High Speed 
mode which shall operate within the range of 8 to 
16 volts DC. The ECU shall provide Vbatt IC to the 
bus transceiver within the range of 6.0 to 18 volts 
(see Appendix C). 
3.10.2 Battery Power Over-Voltage Operation. 
For message frames which must occur at higher 
battery voltage conditions, some means of 
clamping 
Vbatt 
may 
be 
required 
to 
allow 
communications to operate in the 18 < Vbatt ECU < 
26.5 volt range. The transceivers shall operate 
normally over the specified network loading 
between –40 and +85°C up to 26.5V. The 
transceivers may turn off due to internal IC over 
temperature above 85°C ambient. 
a. Recessive state transceiver leakage current 
limits shall be maintained over this range. 
b. Communication shall not be disturbed by any 
node over this range. 
c. ECU’s shall not sustain permanent damage 
when subjected to Vbatt up to 26.5 volts for t < 1 
minimum. 
d. ECU must limit Vbatt transients of greater than 
40 volts and/or duration greater than 10 ms. 
3.10.3 Low Battery Voltage Operation. 
a. For Vbatt ECU < 2.5 volts, the bus shall be 
passive (not be driven dominant) and RxD 
shall be undriven (high) 
b. For 2.5 < Vbatt ECU < 6.0 volts the bus may 
operate in either the reduced output voltage 
mode or the passive mode as defined above 
c. For 6.0 < Vbatt ECU < 7.0 volts the bus should 
operate in normal mode with a reduced 
dominant output voltage as specified in Table 
1. This allows continued operation with 
reduced ground offset margin. In high speed 
mode the bus shall operate in the reduced 
output voltage mode or the passive mode. 
3.10.4 Battery Offset Voltage. The battery offset 
voltage between the battery input pins of any ECU 
limits specified in Table 1 must be maintained over 
the entire range of 7.0 < Vbatt ECU  < 26.5 volts. If the 
battery offset requirements cannot be met, the 
network software must ensure that all devices 
remain awake until the vehicle returns to a sleep 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 11 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 12 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
state. The battery offset requirements must be met 
at any time when the vehicle is in a sleep state. 
3.10.5 Reverse Battery Blocking Diode. The 
reverse battery blocking diode voltage drop 
between the ECU’s Vbatt ECU input pin and the 
transceiver’s Vbatt IC input pin shall be in the range 
0.4 < Vdiode < 1.0 volts. The diode function shall 
pass the reverse polarity test as specified in 
GMW3172 Section 4.2.2.1 Jump Start and 
Reverse Polarity. 
Table 6:  Reverse Battery Blocking Diode Characteristics 
Reverse Battery Blocking Diode Characteristic 
Symbol 
Min. 
Typical
Max. 
Units 
Voltage drop 
Vdiode 
0.4 
0.7 
1.0 
V 
• 
DC Forward Current Capability of Diode  
Idiode DC 
80 
--- 
--- 
mA 
Transient Current Capability of reverse battery diode 
for t < 100 ms 
Idiode 100ms 
350 
--- 
--- 
• 
mA
 
3.10.6 Boost Supply. If a boost supply is 
implemented 
it 
must 
meet 
the 
following 
requirements. 
a. The boost voltage shall always be greater than 
or equal to the current battery voltage as seen 
by the transceiver. 
b. While the boost supply is active the ECU shall 
remain in COMM_KERNEL_ACTIVE due to 
the fact that the higher boost supply voltage 
may make it impossible to recognize a HVWU 
signal and thus, not be able to wake up the 
ECU. 
3.11 
Environmental 
Requirements. 
ECU 
environmental requirements shall be specified in 
the 
individual 
ECU 
Component 
Technical 
Specifications 
(CTS) 
which 
call 
out 
this 
specification. In general, communications devices 
which are installed in these ECUs shall operate in 
the -40 to +125°C temperature range and shall 
meet GMW3172 unless otherwise specified in the 
CTS. 
3.12 Recommended Transceiver Application 
Circuits. In the following schematics all resistor 
tolerances shall be 5% and all capacitor tolerances 
shall be 10% unless otherwise specified. Required 
power dissipation ratings for the bus load resistors 
are stated in section *******. 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 12 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 13 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
3.12.1 Recommended Application Circuit for 
ECUs Utilizing the Melexis Transceiver.
 
µC
1
2
3
4
8
7
6
5
TH8056
TxD
Mode 0
Mode 1
RxD
NC *
CANH
Load
Vbatt
VCC
TxD
RxD
Output
Output
Vbatt
6.49K, 1%
2.7K
47 µH
150 pF
conn.
VCC
10K
GND
9
10
11
12
13
14
GND
GND
GND
NC*
INH
100 nF
4.7 µF
NOTE:  These components are required for the ECU and are 
not uniqe to the SWCAN transceiver.  The capacitance 
specified is the minimum required for the transceiver, it may 
need to be increased due to other ECU component needs.
*  These pins can be connected to ground w/o damage
1K
100 pF
**
** ESD Protection -
MMBZ27VCLT1 or equivalent 
(including equivalent capacitance).
Output to control 
VCC logic supply
***
***
*** These components shall be package protected for.  They shall be installed if necessary to 
help pass the required EMC Immunity tests.  If the 100 pF capacitor is installed, the 150 pF 
capacitor shall be reduced to a 47 pF capacitor.  
 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 13 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 14 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
3.12.2 Recommended Application Circuit for 
ECUs Utilizing the Freescale (formerly 
Motorola) Transceiver
.
µC
1
2
3
4
8
7
6
5
XC33897
TxD
Mode 0
Mode 1
RxD
NC *
CANH
Load
Vbatt
VCC
TxD
RxD
Output
Output
Vbatt
6.49K, 1%
2.7K
47 µH
150 pF
conn.
VCC
10K
GND
9
10
11
12
13
14
GND
GND
GND
NC*
CNTL
100 nF
4.7 µF
NOTE:  These components are required for the ECU and are 
not uniqe to the SWCAN transceiver.  The capacitance 
specified is the minimum required for the transceiver, it may 
need to be increased due to other ECU component needs.
*  These pins can be connected to ground w/o damage
1K
100 pF
**
** ESD Protection -
MMBZ27VCLT1 or equivalent 
(including equivalent capacitance).
Outpu to control 
VCC logic supply
*** These components shall be package protected for.  They shall be installed if necessary to 
help pass the required EMC Immunity tests.  If the 100 pF capacitor is installed, the 150 pF 
capacitor shall be reduced to a 47 pF capacitor.  
***
***
 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 14 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 15 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
3.12.3 Test Tool Recommended Application 
Circuit. 
Note: Any tool, which is intended to be attached to 
the bus to monitor the bus and store information 
without additional support equipment, such as a 
Flight Recorder, shall use the appropriate ECU 
application circuit 
 
 
 
 
 
.
 
 
µC
1
2
3
4
8
7
6
5
TH8056 or XC33897
TxD
Mode 0
Mode 1
RxD
NC *
CANH
Load
Vbatt
VCC
TxD
RxD
Output
Output
Vbatt
6.49K, 1%
2.7K
150 pF
conn.
VCC
10K
GND
9
10
11
12
13
14
GND
GND
GND
NC*
INH/CNTL
100 nF
4.7 µF
NOTE:  These components are required for the ECU and are 
not uniqe to the SWCAN transceiver.  The capacitance 
specified is the minimum required for the transceiver, it may 
need to be increased due to other ECU component needs.
*  These pins can be connected to ground w/o damage
**
** ESD Protection - MMBZ27VCLT1 or 
equivalent (including equivalent 
capacitance).
Outpu to 
control VCC 
logic supply
Switch 
controlled 
by micro
130
 
4 Validation 
a. ECU’s shall be required to pass the tests in 
GME6718 Section 2.. Environmental and other 
requirements 
shall 
be 
specified 
by 
the 
Component 
Technical 
Specification 
which 
references this document. 
 
 
b. ECU suppliers shall validate that after the 
accelerated salt fog and biased humidity tests 
of GMW3172, the resistance between the 
CANH pin and the Vbatt pin shall be greater 
than 500 kohms. 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 15 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 16 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
5 Provisions for Shipping 
Not applicable. 
6 Notes 
6.1 Glossary 
Dominant Signal:  The driven and high voltage 
state of the single wire bus. When multiple devices 
access the bus, this state dominates the recessive 
or non driven state. 
Recessive Signal:  The undriven and low voltage 
state of the single wire bus. When multiple devices 
access the bus, this state is overridden by the 
dominant state. 
Bit Duty Cycle:  The percentage of bits which are 
dominant in any given message and is equivalent 
to, or greater than, the percentage of time the TxD 
line is dominant during any given message. 
6.2 Acronyms, Abbreviations, and Symbols. 
CAN 
Controller Area Network 
CSMA/CR 
Carrier 
Sense 
Multiple 
Access/Collision Resolution 
CTS  
Component Technical Specification 
ECU 
 
 
Electronic Control Unit 
EMC 
 
 
Electro Magnetic Compatibility 
ESD 
 
 
Electro Static Discharge 
Kbits/s  
 
Thousands of data bits per second 
Vbatt 
Supply voltage of an electronic 
control unit as measured at the 
device connector pins 
Vbatt IC 
Supply 
voltage 
of 
the 
bus 
transceiver as measured at the 
transceiver IC supply pins 
7 Additional Paragraphs 
7.1 All materials supplied to this specification must 
comply with the requirements of GMW3001, Rules 
and Regulations for Materials Specifications. 
7.2 All materials supplied to this specification must 
comply with the requirements of GMW3059, 
Restricted and Reportable Substances for 
Parts. 
8 Coding System 
This specification shall be referenced in other 
documents, drawings, VTS, CTS, etc. as follows: 
GMW3089 
9 Release and Revisions 
9.1 Release. This specification was first issued by 
the GMLAN Hardware Team in February 1997. 
9.2 Revisions. 
 
Rev 
Approval 
Date 
Description (Organization) 
C 
AUG 2004 Section 3.1 – explicitly stated 
that the device must operate 
with both 11-bit and 29-bit 
identifiers interspersed. 
D 
DEC 2004 Reformatted 
 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 16 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 17 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
Appendix A has been deleted 
Appendix B has been deleted 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 17 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 18 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
Appendix C – Single Wire CAN Transceiver Specification 
 
3089-002(12/02)
GND 
1 
14 
GND
TxD 
2 
13 
GND/reserved for future use (local wake-up) 
Mode 0 
3 
12 
CANH
Mode 1 
4 
11 
Load
GND 
7 
8 
GND
Vcc 
6 
9 
Vreg cntl
RxD 
5 
10 
V
batt
 
Figure C1: Single Wire CAN Transceiver 14 Pin SOIC Package Pin Assignments 
 
3089-002(12/02)
TxD 
1 
8
GND 
Mode 0 
2 
7
CANH 
Mode 1 
3 
6
Load 
RxD 
4 
5
V 
batt 
 
Figure C2: Single Wire CAN Transceiver 8 Pin SOIC Package Pin Assignments 
Please note that the 8-pin device is not generally suitable for 
very high ambient temperatures, above 85°C. Consider the 
power dissipation at high operating voltage and maximum 
network loading. Please see the transceiver data sheet for 
details on maximum operating temperature, loading conditions 
and power dissipation ratings. 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 18 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 19 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
Transceiver Pin Descriptions. 
The transceiver shall be supplied in a 14 pin Small 
Outline Integrated Circuit (SOIC) package as 
shown in Figure C1. 
C1 GND Pin – Ground. 
The four ground pins on the corners shall be 
connected to the lead frame to lower the thermal 
resistance of the device. 
C2 TxD Input Pin – Logic Command to Transmit 
on the Single Wire CANH Bus as Follows. 
a. TxD Polarity 
TxD = logic 1 (or floating) on this pin shall 
produce an undriven or recessive bus state 
(low bus voltage) 
TxD = logic 0 on this pin shall produce either a 
bus normal or a bus high voltage dominant 
state depending on the transceiver mode state 
(high bus voltage) 
If the TxD pin is driven to a logic low state 
while the Mode 0,1 pins are in the 0,0 or sleep 
state, the transceiver shall not drive the CANH 
pin to the dominant state 
b. For transceiver designs which do not limit the 
TxD current sufficiently to insure that the TxD 
drive circuit will not latch up, the ECU shall 
provide a weak (approximately 10 kohm) 
external pulled-up-to-Vcc resistor on the TxD 
pin which shall cause the transmitter to default 
to the bus recessive state when TxD is not 
driven.  
c. TxD input signals shall be compatible with both 
3.3 and 5.0V microcontroller devices. 
V logic il max < 0.8 V 
Vlogic ih min > 2.0 V 
C3 Mode 0 & Mode 1 Pins Respectively are 
Used to Select Transceiver Operating Modes. 
a. Mode 0 = 0, Mode 1 = 0 - Sleep mode. 
Transceiver is in low power state, waiting for 
wake up via high voltage signal or by mode 
pins change to any state other than 0,0. In this 
state, the CANH pin shall not be in the 
dominant state regardless of the state of the 
TxD pin. See the RxD Output pin description 
below 
b. Mode 0 = 1, Mode 1 = 0. High Speed mode 
c. Mode 0 = 0, Mode 1 = 1. Transmit with high 
voltage signals to wake up remote nodes 
d. Mode 0 = 1, Mode 1 = 1. Normal speed and 
signal voltage mode 
e. The transceiver shall provide a weak internal 
pulled down to ground resistor on each of 
these pins which causes the transceiver to 
default to sleep mode when they are not driven 
f. 
The Mode input signals shall be compatible 
with both 3.3 and 5.0V microcontroller devices. 
Vlogic il max < 0.8 V 
Vlogic ih min > 2.0 V 
C4 RxD Output Pin – Logic Data as Sensed on 
the Single Wire CANH Bus. 
a. RxD polarity 
RxD = logic 1 on this pin shall indicate a bus 
recessive state (low bus voltage) 
RxD = logic 0 on this pin shall indicate a bus 
normal or high voltage bus dominant state 
b. Preferred Operation of RxD in Sleep Mode 
RxD 
shall 
not 
pass 
signals 
to 
the 
microprocessor while in sleep mode (Mode 0 
and Mode1 pins are at 0,0) unless a valid 
transition from a recessive state to a valid 
wake up bus voltage is received. When the 
valid wake up bus voltage signal transition 
awakens the transceiver, it shall pass these 
bus signals to the RxD pin. In order to reduce 
parasitic drain on the battery in the case when 
the CAN bus is stuck at battery, if the Mode 0 
& 1 pins are at logic 0, the transceiver shall 
return the RxD signal to the recessive state no 
sooner than 750 µS and no later than 1s after 
the recessive to dominant edge which caused 
the RxD line to become dominant.  
When not in sleep mode all valid bus signals 
shall be sent out on the RxD pin. 
RxD shall be placed in the undriven or off state 
when in sleep mode 
c. RxD is an open drain (or collector) output 
which when pulled up to Vcc shall maintain an 
output voltage Vol (Vsat) < 0.45 volts at 2 mA 
maximum. Transceivers may also provide an 
internal active pull up if it will operate with the 
external pull up resistor. 
d. RxD Load 
Resistance: 2.7 kohms 
Capacitance: < 25 pF 
C5 VccICInput Pin = +5 Volts ± 5% (Optional Pin). 
a. The total transceiver Ibatt + Icc quiescent current 
draw in sleep mode shall not exceed the 
amount specified in Table C1. 
b. When Vcc is not within specified range, the 
transceiver shall either operate normally, or it 
shall exhibit passive bus output behavior and 
RxD shall be undriven (high), regardless of the 
state of the TxD pin 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 19 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 20 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
C6 Vreg Cntrl Pin – Vreg Control (Optional Pin). 
a. It is preferred that this pin be used as an output 
which is used to control the ECU’s regulated 
microcontroller voltage supply; otherwise, this 
pin shall be a no connect.  
b. When used as a Vreg Control output the 
operation shall be as follows: 
1. 
This output shall be a logic 1 state at 
power on and shall remain in this state 
for at least 100 ms before transitioning to 
operate as specified in (b.) and (c.) 
below. 
2. 
Mode 0,1 ≠ 0,0 – This output shall be a 
logic 1 state to enable the Vreg supply. 
3. 
Mode 0,1 = 0,0 (sleep state) – This 
output shall go to a logic 0 state no 
sooner than 100 ms and no later than 1 
s after the sleep state is entered, when 
the transceiver has enabled its wake-up 
filter. This output shall go to a logic 1 
state when a valid wake-up signal is 
received and shall return to a logic 0 
state when the sleep state is reentered 
as specified in C5 #2. 
c. The Vreg cntrl output shall have the following 
voltage levels: 
1. Vreg cntrl ol max < 0.8 V 
2. Vreg cntrloh min > VbattIC – 0.8 V 
d. The Vreg cntrl output shall be capable of 
sourcing or sinking the following currents: 
1. Vreg cntrl ol max < 5 µA 
2. Vreg cntrl oh min > 180 µA 
C7 Bus LOAD Pin – Resistor Ground with 
Internal Open-On-Loss-of-Ground Protection.  
a. When the ECU experiences a loss of ground 
condition, this pin shall switch to a high 
impedance state. 
b. The ground connection through this pin shall 
not be interrupted in any transceiver operating 
mode including the sleep mode. The ground 
connection shall only be interrupted when 
there is a valid loss of ground condition. 
c. This pin shall normally provide the device 
resistance with a path to ground which 
contributes less than 100 ohms to the bus load 
resistance when sinking the maximum current 
through 
the 
minimum 
specified 
device 
resistance, Rmin.  
d. The transceiver’s maximum bus leakage 
current contribution to Vol from the LOAD pin 
when in a loss of ground state is 50 microamps 
over all operating temperatures and 2.5 < Vbatt 
IC < 18 volts. 
e. Load pin ESD protection, see paragraph 0 
C8 Vbatt IC INPUT Pin – Vehicle Battery Voltage. 
a. The transceiver shall be fully operational as 
described in Table C1 over the range 6.0 < 
Vbatt IC < 18 volts as measured between the 
GND pin and this pin. 
b. For Vbatt IC < 2.5 volts, the transceiver CANH 
shall be passive (not be driven dominant) and 
RxD shall be undriven (high), regardless of the 
state of the TxD pin. 
c. For 2.5 < Vbatt IC < 5.0 volts the transceiver 
CANH may operate in either the normal or the 
passive bus mode as defined above. The bus 
shall return to normal operation once the 
battery voltage has increased at least 0.5V 
above the turn-off voltage. 
d. For 5.0 < Vbatt IC < 6.0 volts the transceiver 
should operate in normal mode with a reduced 
dominant output voltage as specified in Table 
C1. This allows continued operation with 
reduced ground offset margin. In high speed 
mode the bus shall operate in the reduced 
output voltage mode or the passive mode. 
e. The transceiver shall operate normally in the 
normal mode when 18 >Vbatt IC > 26.5 volts at 
85°C for one minute. See section 0 for required 
operating conditions. 
f. 
The transceiver shall operate normally as 
indicated in Table C1 or maintain the passive 
state 
(i.e. 
shall 
not 
disturb 
normal 
communications) when Vbatt IC > 18 volts except 
under the condition specified in #5 above. 
g. At the transceiver Vbatt pin, load dump events of 
less than 10 ms transients of up to 40 volts 
shall 
not 
cause 
transceiver 
damage. 
Transients greater than 40 volts shall be 
clamped by the ECU. 
h. The total transceiver Vbatt IC + Vcc quiescent 
current draw in sleep mode shall not exceed 
the IQ maximum amount specified in Table C1. 
C9 CANH BUS Input/Output Pin. 
Protection  
a. Pin shall be proof against the following 
conditions (automatic resume of operation 
when condition is removed; operation is not 
required 
when 
condition 
present; 
all 
requirements apply to powered as well as 
unpowered state): 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 20 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 21 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
Bus pin DC voltage -14V < V_CANH < +18V Bus 
pin voltage for t = 1 min. V_CANH < 26.5V 
Transient Bus pin voltage 
-150V < V_CANH  < 
+100V; R = 50 Ohms, see GMW3097 Bus pin ESD 
protection ± 4 kV HBM contact discharge, see 
paragraph 3.4 
b. Wave Shaping in normal and HVWU mode 
Wave 
shaping 
is 
incorporated 
into 
the 
transmitter 
to 
minimize 
EMI 
radiated 
emissions. 
An 
important 
contributor 
to 
emissions is the rise and fall times during 
output transitions at the “corners” of the 
voltage waveform. The resultant waveform will 
preferably be one half of a sine wave at the 
rising waveform edge and one quarter of this 
sine wave at falling or trailing edge. The 
waveform shall remain at constant Voh (or Voh 
wu) between the rising and falling edges. 
c. Wave Shaping in high speed mode 
Wave shaping control of the rising and falling 
waveform edges shall be disabled during high 
speed mode. EMI emissions requirements are 
waived during this mode. The waveform rise 
time in this mode shall be less than one 
microsecond. 
d. Short circuits  
If the CANH pin is shorted to ground for any 
duration of time, an over temperature shut 
down circuit shall disable the output high side 
drive source transistor before the local die 
temperature exceeds a damage limit threshold. 
The output transistor shall remain latched off 
until the local die temperature is 10 to 20 °C 
below the latch off trip temperature. 
e. Leakage  
This pin shall not source more than 10 
microamps over the range of –40°C < ambient 
temperature < 125°C. See 3.10.4 Battery 
Offset Voltage - and ******* Bus Passive State 
Voltage Offset (Vleak). 
f. 
Transceiver Bus Interface Signals and Loading 
Requirements - See Table C1 below: 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 21 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 22 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
Table C1:  Transceiver Bus Interface Signals and Loading Requirements 
The below parameter limits shall be met over operating conditions including the specified bus loading range. 
Testing shall be performed using the particular application circuit, see section 3.12 Recommended Transceiver 
Application circuits.  
The transceiver operating conditions are specified as follows: 
Transceiver Operating Condition 
Symbol 
Min. 
Typical
Max. 
Units 
Transceiver Operating Battery Voltage Input except in 
High-Speed Mode1 
Vbatt IC 
6.0 
12 
18 
volts 
Transceiver Operating Battery Voltage Input in High-
Speed Mode1 
Vbatt ICHS 
8 
12 
15.6 
volts 
Short-Duration Transceiver Operating Battery Voltage 
Input (t < 1 min, Tamb < 85 oC) except in high-speed 
mode1 
Vbatt ICJS 
18 
24 
26.5 
volts 
Low Battery Transceiver Operating Supply Voltage 
except in High-Speed mode and sleep mode 
Vbatt IC low 
Batt 
5.0 
--- 
6.0 
volts 
 
 
 
 
 
 
Network Time Constant 9 
τnetwork 
See 
Table 1 
--- 
See Table 
1  
µs 
Network Time Constant for networks with 0.5% CAN 
bit clock tolerance 9 
τnetwork 
See 
Table 1 
--- 
See Table 
1  
µs 
Device Resistance (min load) 
Rmin 
2000 
--- 
--- 
Ω 
Network Total Resistance 
Rtotal 
See 
Table 1 
--- 
See Table 
1  
ohms 
High Speed Mode Network Resistance to GND 
Rload 
See 
Table 1 
--- 
See Table 
1  
ohms 
Network Total Capacitance 8 
Ctotal 
See 
Table 1 
--- 
See Table 
1  
pF 
• 
DC current of reverse battery diode 
Irbpd DC 
 
 
See 
section 
3.10.5 
mA 
Reverse battery diode voltage drop 
Vdiode 
See 
section 
3.10.5 
--- 
See 
section 
3.10.5 
V 
 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 22 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 23 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
The transceiver behavioral characteristics are specified as follows: 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 23 of 37
 
Transceiver Behavioral Parameter 
Symbol 
Min. 
Typical 
Max. 
Units 
Offset Wakeup Input High Voltage Threshold 4, 5 
VihWuOffset 
Vbatt_IC - 
4.3 
--- 
Vbatt_IC - 
3.25 
volts 
Offset Wakeup Output High Voltage 6 
Voh WuOffset 
Vbatt_IC - 
1.5 
--- 
Vbatt_IC   
volts 
Fixed Wakeup Input High Voltage Threshold 4, 5 
VihWuFixed 
6.6 
--- 
7.9 
volts 
Fixed Wakeup Output High Voltage 6 
VohWuFixed 
9.7 
 
--- 
13 (12.5 
preferred) 
volts 
Normal Mode Output High Voltage 
Voh 
4.4 
4.75 
5.1 
volts 
High Speed Mode Output High Voltage 
Voh_HS 
4.2 
4.65 
5.1 
volts 
Low Battery Output High Voltage in Normal Mode 
and in Wakeup Mode (5.0 <VbattIC < 6.0) 
Voh low Batt 
Vbatt_IC - 
1.6 
--- 
5.1 
volts 
High Speed & Normal Mode Input High Voltage Input 
Threshold 
Vih 
2.0 
2.1 
2.2 
volts 
Normal Bus recessive or passive state low voltage 6 
Vleaknorm/ 
Vol 
-0.2 
0 
0.2 
volts 
LOG condition Bus recessive or passive state low 
voltage 2 
VleakLOG / 
Vol 
-0.2 
--- 
0.5 
volts 
LOG switch Voltage (normal operation) @ ILOG = 1 
mA 
V LOG norm 
--- 
--- 
0.1 
V 
LOG switch Voltage (loss of battery) @ ILOG = 7 mA 
V LOG err 
--- 
--- 
1 
V 
Ground Offset Voltage  
Vg off 
--- 
--- 
see Table 
1 
volts 
Low Battery Ground Offset Voltage 
Vg off low batt 
--- 
--- 
see Table 
1 
volts 
Transceiver Battery ECU Offset Voltage when 
sleeping devices shall be activated 
Vb off 
--- 
--- 
see Table 
1 
volts 
Bus Output Short Circuit Current in dominant state 
Ioh_sc 
50 
--- 
350 
mA 
Node Load Resistance during loss of Battery 12 
Rload lob 
Rload – 
10% 
Rload 
Rload + 
35% 
Ω 
Normal mode signal delay + transition time bus rising 
edge7 
Tt norm 
See 
Table 1  
--- 
See Table 
1  
µs 
Normal mode signal delay + transition time bus falling 
edge7 
Tt norm 
See 
Table 1  
--- 
See Table 
1  
µs 
Normal mode, HVWU mode and high speed mode 
receive delay time11 
Trx 
See 
Table 1  
--- 
See Table 
1  
µs 
High speed mode signal delay + transition time bus 
rising edge 10 
Tt hs 
See 
Table 1  
--- 
See Table 
1  
µs 
High speed mode signal delay + transition time bus 
falling edge 10 
Tt hs 
See 
Table 1  
--- 
See Table 
1  
µs 
Normal mode, HVWU mode and High speed mode 
i
i
t
i i
l
l
th 13
Trp 
0.15,  
--- 
1.0 
µs 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 24 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
Transceiver Behavioral Parameter 
Symbol 
Min. 
Typical
Max. 
Units 
receive input minimum pulse length 13 
0.2 
preferred
HVWU Mode signal delay + transition time bus rising 
edge to Vih
 7 
tr-wu1 
See 
Table 1 
--- 
See Table 
1 
µs 
HVWU Mode signal delay + transition time bus rising 
edge to VihWu
 7 
tr-wu2 
See 
Table 1 
--- 
18 
µs 
HVWU Mode signal delay + transition time bus falling 
edge7 
tf-wu 
See 
Table 1 
--- 
See Table 
1 
µs 
Transceiver Leakage Current to bus with no loss of 
ground 
Ileak 
--- 
--- 
10 
µAmp 
Transceiver Leakage Current to bus from LOAD pin 
(one node with loss of ground) 
Ileak LOG 
LOAD 
--- 
--- 
90 
 
µAmp 
Transceiver Leakage Current to bus from CANH pin 
(one node with loss of ground) 
Ileak LOG 
CANH 
--- 
--- 
10 
µAmp 
Transceiver Vbatt_IC + Vcc Quiescent Current in sleep 
mode with Vbus < 1.5 volts and 6V < Vbatt_IC < 13V 
and  
–40deg C < Tamb < +85 degC 
IQ = Ibatt_IC 
+ Icc 
--- 
--- 
80 
(50 
preferred) 
µAmp 
Transceiver Vbatt_IC + Vcc Quiescent Current in sleep 
mode with Vbus > 1.5 volts 
IQ = Ibatt_IC 
+ Icc 
--- 
--- 
1 
mA 
Bus input filter time, except in sleep mode 14 
Tfilter 
0.2 
--- 
0.8 
µs 
Wakeup filter time delay 
TwuDelay 
10 
--- 
70 
µs 
Notes: 
1. 
Vbatt IC is measured at the transceiver input power pins. All voltages are referenced to the local transceiver ground. The 
transceiver shall be fully operational when transmitting a dominant bus condition duty cycle of up 50% duty cycle forever and up 
to 75% forever for the test tool in high-speed download mode.  
2. 
Bus recessive state low voltage is the offset due to the transceiver high side driver leakage current (Ileak) and does not include 
the ground offset voltage (Vg off). This leakage current limitation shall be in effect over the range 5.5 < Vbatt IC < 26.5 volts. (see 
3.10 Operating Battery Power Voltage Range - and ******* Bus Passive State Voltage Offset (Vleak) 
3. 
The wake up signal threshold shall not generate a wake up signal to the ECU unless the threshold has been exceeded for a 
minimum delay time. (see ******* Wake up signal voltage levels) 
4. 
The minimum transceiver input wake up voltage level shall be the smaller of VihWuFixed minimum or VihWuOffset minimum.  
5. 
The maximum transceiver input wake up voltage level shall be the smaller of VihWuFixed maximum or VihWuOffset maximum. 
6. 
The minimum transceiver output wake up voltage level shall be the smaller of VohWuFixed minimum or VohWuOffset minimum over Rtotal 
bus loading conditions. 
7. 
Maximum signal delay time for a bus rising edge is measured from Vcmos il on the TxD input pin to the VihMax + V g off max level on 
the Bus Input/Output pin. Minimum signal delay time for a bus rising edge is measured from Vcmos il on the TxD input pin to 1V on 
the Bus Input/Output pin. Maximum signal delay time for a bus falling edge is measured from V cmos ih on the TxD input pin to 1V 
on the Bus Input/Output pin. Minimum signal delay time for a bus falling edge is measured from V cmos ih on the TxD input pin to 
the VihMax + V g off max level on the Bus Input/Output pin. They include both the internal transceiver propagation delay and the 
waveform rise/fall time.  The minimum values are measured with the minimum network time constant and the maximum values 
are measured with the maximum network time constant. The minimum bus voltage used for measuring signal delay time is 1V 
instead of VihMin – Vgoff . This is due to the fact that a voltage divider is created between all of the device resistors and the actual 
recessive bus voltage is lower than the maximum allowed ground offset voltage. 
8. 
The Network total capacitance includes the capacitors placed on the ECUs as well as the capacitance of the bus wires, 
connectors, splices, PCB traces, etc. 
9. 
The network time constant incorporates the bus wiring capacitance. The minimum value is selected to limit radiated emissions. 
The maximum value is selected to ensure proper communication under all communication modes. Not all combinations of R and 
C are possible. 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 24 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 25 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
10. 
Maximum high speed mode signal delay time is measured from Vcmos il on the TxD input pin to the VihMax + Vgndoff level on the Bus 
Input/Output pin with Rtool present for a recessive to dominant edge and is measured from Vcmos ih on the TxD input pin to 1V on 
the Bus Input/Output pin with Rtool present for a dominant to recessive edge. Minimum high speed mode signal delay time is 
measured from Vcmos il on the TxD input pin to 1V on the Bus Input/Output pin with Rtool present for a recessive to dominant 
edge and is measured from Vcmos ih on the TxD input pin to the VihMax + Vgndoff level on the Bus Input/Output pin with Rtool present 
for a dominant to recessive edge.  It includes both the internal transceiver propagation delay and the waveform rise time. The 
minimum values are measured with the minimum network time constant and the maximum values are measured with the 
maximum network time constant. The minimum bus voltage used for measuring signal delay time is 1V instead of VihMin – Vgoff . 
This is due to the fact that a voltage divider is created between all of the device resistors and the actual recessive 
bus voltage is lower than the maximum allowed ground offset voltage. 
11. 
Receive delay time is measured from the rising/falling edge crossing of the nominal Vih value on the bus to the falling 
(Vcmos_il_max) / rising (Vcmos_ih_min) edge of the RxD input to the µC. This parameter is tested by applying a square wave 
signal to the bus. The minimum slew rate for the bus rising and falling edges is 50V/us. The low level on bus is always 0V. For 
normal mode and high-speed mode testing the high level on bus is 4V. For high-level wakeup mode testing the high level on bus 
is Vbat – 2V. 
12. 
Node load resistance during a loss of battery is the effective resistance between the CANH pin and ground when the transceiver 
loses battery. This resistance is expressed as a maximum deviation from the nominal load resistance connected between the 
CANH pin and the LOAD pin. 
13. 
The receive output of the transceiver starts to react to a change of the logic bus condition (e.g. edge on RxD line), when the 
pulse length on the bus reaches the specified value. 
14. 
This parameter denotes the time between when the voltage on the transceiver’s CANH pin crosses the receive input threshold 
and the corresponding change of the RxD signal’s logic state under the condition that the slew rate on the CANH pin is no less 
than +/-1V/us.  
 
C10 Transmit Operating Conditions. 
Transmit duty cycles: 
Assume one transition for each bit time - recessive 
to dominant (low to high) or dominant to recessive 
(high to low). Bit timing is 30 us/bit in normal mode 
and 12 us/bit in high speed mode. Assume 50% 
transmitter on time (dominant) as the bit duty cycle 
during any frame transmissions unless otherwise 
specified. Where the bit duty cycle is the 
percentage of bits which are dominant in any given 
message and is equivalent to the percentage of 
time the TxD line is dominant during any given 
message. The following duty cycle requirements 
apply over all power and environmental conditions 
unless otherwise specified. See Table 1 for details 
of bus loading and voltage ranges. 
a. High speed mode operating requirements 
Diagnostics tool - Must transmit frames in high 
speed mode of 75% bit duty cycle forever. The 
tool may utilize the following special operating 
condition modifications: 8V ≤ Vbatt ECU ≤ 14 V 
and -40° ≤ Ambient Temperature ≤ + 85 °C. 
Vehicle ECUs - Must transmit in high speed at 
50% bit duty cycle with 30% frame duty cycle 
forever. At higher duty cycles, an over 
temperature shut down circuit may disable the 
output high side drive transistor before the 
local die temperature exceeds a damage limit 
threshold. The output transistor shall remain 
latched off up to five seconds to allow the local 
die temperature to reach 10 to 30°C below the 
latch off trip temperature. 
b. Normal speed mode operation must transmit 
frames of 50% bit duty cycle and frame duty 
cycle of 100% forever. At supply voltages 
greater than 18V, the device must transmit 
forever with a frame duty cycle of 50%. 
c. High voltage mode operation must transmit 
frames of 50% bit duty cycle for one second 
and frame duty cycle of 10% forever. 
C11 Transceiver Mode Changes. 
a. The transceiver shall perform the mode 
change without damage regardless of the state 
or activity on the bus or on the TxD pin - unless 
when there is a mode change to sleep mode 
attempt during a wake up high voltage 
condition on the bus. In this case, the 
transceiver shall remain in the awake state 
until the wake up voltage levels are no longer 
present on the bus. 
b. It is the responsibility of the microprocessor to 
manage any system or ECU effects associated 
with the mode change and to insure that mode 
changes take place between frames. 
c. Times allowed to change transceiver operating 
modes are: 
t < 30 ms in switching from normal to high 
speed or high voltage modes 
t < 30 ms in switching from high speed or high 
voltage modes to normal mode 
t < 500 ms when switching from normal to 
sleep mode 
t < 50 ms when switching from sleep to normal 
or high voltage modes  
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 25 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 26 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
Note: While changing modes from normal 
mode to sleep mode the transceiver should 
preferably support continuous detection of high 
voltage mode frames including notification at 
the RxD pin. 
d. It is the responsibility of the transceiver to 
assure that after a mode change: 
No lock up or permanent damage shall occur 
After a change to normal mode, no transmitted 
wake up signals shall occur 
C12 Maximum Transceiver Propagation Delays. 
Transmitter delays 
Transmitter delays shall be as specified in Table 
C1 as measured from 90% of the TxD input logic 
high level to Vih max + Vg off as measured at the 
transceiver CANH I/O pin, with worst case silicon 
and bus loading component tolerances. 
Receiver delays 
Receiver delays shall be as specified in Table C1 
as measured at the transceiver CANH I/O pin, from 
Vih max of the normal voltage level bus dominant 
rising edge to 90% of the RxD output logic low 
waveform with worst case temperature and Vbatt IC. 
C13 
Transceiver 
Electrostatic 
Discharge 
Immunity. 
The transceiver shall not sustain any damage or 
degradation when its bus pin (CANH), battery 
supply pin (VBATT) or ground pins are subjected to 
electrostatic discharges using a standardized 
human body model with contact discharge method 
at severity level of ± 4 kV, 10 pulses each.  
It is preferred that there be no damage or 
degradation when the bus pin (CANH) and the 
battery pin (VBATT) of the transceiver IC are 
individually subjected to electrostatic discharges 
using human body model contact discharge with a 
severity level of ± 6 kV, 10 pulses each. In 
addition, it is preferred that there be be no damage 
or degradation when the bus load pin (LOAD) of 
the transceiver IC is subjected to electrostatic 
discharges using human body model contact 
discharge with a severity level of ± 6 kV, 10 pulses 
each where the ESD pulse resistance is increased 
by the minimum device load resistance (e.g. plus  
2 kOhms).  
During IC level ESD testing all pins except the pin 
under test shall be connected to ground. In the 
context of this document human body model per 
default means using the test parameters R = 2000 
Ohms, C = 330 pF. Other RC combinations can be 
released upon request. 
 
 
 
 
 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 26 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 27 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
Appendix D has been deleted 
Appendix E – Single Wire CAN Bit Timing Analysis 
Node B
Transmitter
(slow)
Node C
Transmitter
(fast)
Sync_Seg = t
Bit 1
Q
tgndOff (A,B)
Bit 1
Node A
Transmitter
(fast)
tprop (A,B)
trise
Bit 1
Bit 6
Global Stuff
Bit Error
Bit 13
tprop (B,C)
Error Flag
3089-003(12/02)
tbit-tseg2
tfall
tprop (B,A)
Error Flag
Error Flag
Bit 13
tbit-tseg2
 
Figure E1: Worst Case Timing for Global Stuff Bit Error with Three Nodes 
The CAN bit timing analysis is given below using 
Table E1 to determine the maximum Single Wire 
CAN system delay time allowable for proper CAN 
error counter operation. Nodes must synchronize 
only on the recessive to dominant waveform edge. 
Consider a three node system communicating 
CAN frames (see Figure E1). The worst timing 
case occurs when Node A at one end of the bus is 
transmitting and all nodes experience noise which 
destroys a recessive stuff bit. Nodes A, B and C 
each generate a 6 bit time dominant error flag 
followed by a recessive error frame delimiter. CAN 
nodes must see a recessive in the 13th bit 
following the last recessive-to-dominant bit time 
synchronization in order to advance the error 
counters properly. Nodes A and C must not see 
Node 
B’s 
delayed 
dominant 
error 
frame 
transmission when it samples the 13th bit. 
Equation 18 is taken from SAE Paper # 970295 
(Equation 18). Equation 18 is used for determining 
the maximum delay that can be tolerated and still 
maintain synchronization from the last waveform 
rising edge. In the case of single wire CAN, 
Equation 18’s tPROP must include the CAN 
controller and transceiver transmit propagation 
delay plus the waveform rise time, ground offset 
voltage delay effects, two media propagation 
delays and the controller plus the transceiver 
receive delay. The ground offset voltage time is 
required to overcome the maximum allowed 1.3 
volts at the Node A transmitter while slewing the 
waveform rise time at the indicated slew rate. This 
total delay must be less than the accumulated 
variations in bit sample point due to clock tolerance 
variations. 
Below Equation 18 are sample calculations - for 
33.333 kb/s and for 83.333 kb/s. It can be seen 
that using the values in Table A1 below that the 
allowable delay is met and the equation is satisfied 
for clock tolerance variations of up to ± 0.35% for 
bit rates of 33.333 and 83.333 kb/s. The results 
show that the maximum delays are less than the 
right side of Equation 1 requires, therefore there 
should be no synchronization problems. 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 27 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 28 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
 
 
 
 
SAE 970295 Equation 18: 
Max. delay = tPropMax
< tbit - tseg2 - ∆f (25 tbit - tQ - tseg2) - tQ 
Equation 1: 
tTxRiseDelay + tprop + tRxDelay + tTxFall +tprop + tRxDelay + 
tChoke Delay  
< tbit - tseg2 - ∆f (25 tbit - tQ - tseg2) - tQ 
33.333 kb/s 
6.3 + 0.42 + 1 + 10.0 + 0.42 + 1 + 0.3 = 19.44 µs 
< 30 – 4.0 – 0.0035 (25 * 30 – 2 – 
4.0) – 2 = 21.4 µs 
83.333 kb/s: 
1.7 + 0.42 + 1.0 + 3.0 + 0.42 + 1.0 + 0.3 = 7.9µs 
<  12 – 2 – 0.0035 ( 25 * 12 – 1 – 
2) – 1 = 7.96 µs 
 
Table E1:  SWC CAN Bit Time Parameters 
 
33.333 Kbits/s 
83.333 Kbits/s 
Bit time tbit 
30 µs 
12 µs 
Crystal ( e.g. ) 
8.00 Mhz 
8.00 Mhz 
Clock tolerance 
∆f 
± 0.35% 
± 0.35% 
tQ ( e.g. ) 
2 µs 
1 µs 
TSEG1 ( e.g. ) 
11 (12 tq) 
8 (9 tq) 
TSEG2 ( e.g. ) 
1 (2 tq) 
1 (2 tq) 
tseg 1 + tQ 
26.0 µs (86.67% Bit time) 
10.0 µs (83.33% Bit time) 
tseg 2 
4.0 µs (13.33% Bit time) 
2.0 µs (16.67% Bit time) 
tSJW 
2 tQ ≤ tseg 2 = 4.0 µs 
2 tQ ≤ tseg 2 = 2.0 µs 
tprop 
0.42 µs (60 meters one way) 
0.42 µs (60 meters one way) 
txFall 
10 µs 
3.0 µs 
txRise 
6.3 µs 
1.7 µs 
tRxDelay 
1 µs 
1.0 µs 
 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 28 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 29 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
Appendix F Single Wire CAN – High 
Speed Mode Analysis 
The SWC high speed mode can operate at various 
maximum bit rates depending on the number of 
attached nodes. The tool resistance is connected 
to the bus and this combines with the remaining 
bus resistors to make a decreasing total bus 
resistance as more nodes are added. However, 
because of the tool resistor, the total bus 
resistance does not change at the same rate as 
the total bus capacitance, there is an increasing 
RC time constant on the high speed bus as more 
nodes are added. This results in a somewhat 
declining maximum high speed bit rate as more 
nodes are added.  
High speed tools and vehicle nodes may be 
programmed to adjust the high speed bit rate 
according to the number of attached nodes or the 
high speed mode may be fixed according to the 
maximum number of nodes the tool is expected to 
encounter. To minimize the proliferation of high 
speed operating modes in which the tool and the 
vehicle ECUs must accommodate, the high speed 
mode is fixed at 83.333 kbits/s which corresponds 
to the maximum speed allowed at the full 32 node 
bus loading. 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 29 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 30 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
Appendix G CAN Controller Setup 
Note: All times shown in this section are nominal 
values. 
An example calculation set up for the CAN 
controller to operate at both 33.333 and 83.333 
Kbits/second is shown below. Reference Intel 
82527 and/or Philips SJA1000 data sheets (other 
CAN controller suppliers use similar methods): 
Equation A 
CAN Bus Frequency = XTAL / [(DSC + 1) x (BRP + 
1) x (3 + TSEG1 + TSEG2)] 
Where: 
CAN Bus Frequency is in bits/s 
XTAL = CAN timing oscillator frequency 
DSC = Divide System Clock (0 or 1 - divides XTAL 
by 1 or 2 respectively) 
BRP = Baud Rate Prescaler (0 to 63) 
TSEG1 = 2 to 15 - sets the bit sample point 
TSEG2 = 1 to 7 - sets the time remaining in the bit 
after the sample point 
Note that in the CAN 2.0 specification, PROP_SEG 
+ 
PHASE_SEG1 
corresponds 
to 
tSEG1 
and 
PHASE_SEG2 corresponds to tSEG2. 
For the low speed bit rate and the high speed bit 
rate, the CAN controller configuration and oscillator 
frequency 
are 
determined 
by 
the 
following 
conditions/equations: 
Equation 1: (XTAL/low speed bit rate) and 
(XTAL/high speed bit rate) MUST both be integer 
numbers 
Equation 2: (TSEG1 + TSEG2 + 3) MUST be a 
factor of the results of the equations in 1. 
Once these two conditions are met, the DSC and 
BRP values can be calculated from Equation A. 
Example Solution: 
Set Low speed bit rate LSBR = 33 333 bits/s 
Set High speed bit rate HSBR = 83 333 bits/s 
XTAL is chosen as 8 MHz 
XTAL/LSBR = 240 (an integer) 
XTAL/HSBR = 96 (an integer) 
Low Speed: 
If: TSEG1 is chosen as 11 (12 tq) 
TSEG2 is chosen as 1 (2 tq) 
This gives tseg1 + tsync seg = 86.67% of bit time 
and tseg2 = 13.3% of bit time 
Then: 
TSEG1 + TSEG2 + 3 = 15 = total time quanta per 
bit where tq = 2 µs 
Both conditions are satisfied and DSC and BRP 
can be calculated by substitution in Equation A. 
For low speed:  
DSC = 0 
 
 
 
BRP = 15 
 
 
 
or  
DSC = 1 
 
 
 
BRP = 7 
High Speed: 
If: TSEG1 is chosen as 8 (9 tq) 
 
TSEG2 is chosen as 1 (2 tq) 
This gives tseg1 + tsync seg = 83.33% of bit 
time and tseg2 = 16.67% of bit time 
Then: 
TESG1 + TSEG2 + 3 = 12 = total time 
quanta per bit where tq = 1 µs 
Both conditions are satisfied and DSC and BRP 
can be calculated by substitution in Equation A. 
 
For high speed:  
DSC = 0 
 
 
 
 
BRP = 7 
 
 
 
 
or  
DSC = 1 
BRP = 3 
Several acceptable example clock frequencies with 
their associated calculated CAN baud rate settings 
are shown in the table below. Other sets may be 
possible with careful analysis. 
fxtal 
(MHz) 
tQ 
(us) 
DSC 
BRP 
(dec) 
2
1.5
0 
2.00
4
1.5
0 
5.00
4
1.5
1 
2.00
6
1.5
0 
8.00
8
1.5
0 
11.00
8
1.5
1 
5.00
10
1.5
0 
14.00
12
1.5
0 
17.00
12
1.5
1 
8.00
16
1.5
0 
23.00
16
1.5
1 
11.00
20
1.5
0 
29.00
20
1.5
1 
14.00
 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 30 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 31 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
 
fxtal 
(MHz) 
tQ 
(us) 
DSC 
BRP 
(dec) 
2 
2 
0
3.00
2 
2 
1
1.00
3 
2 
0
5.00
3 
2 
1
2.00
4 
2 
0
7.00
4 
2 
1
3.00
5 
2 
0
9.00
5 
2 
1
4.00
6 
2 
0
11.00
6 
2 
1
5.00
8 
2 
0
15.00
8 
2 
1
7.00
10 
2 
0
19.00
10 
2 
1
9.00
12 
2 
0
23.00
12 
2 
1
11.00
15 
2 
0
29.00
15 
2 
1
14.00
16 
2 
0
31.00
16 
2 
1
15.00
20 
2 
0
39.00
20 
2 
1
19.00
 
Several acceptable example clock frequencies with 
their associated calculated CAN baud rate settings 
are shown in the table below. Other sets may be 
possible with careful analysis. 
fxtal 
(MHz) 
tQ 
(us) 
DSC 
BRP 
(dec) 
5
0.60 
0
2.00
10
0.60 
0
5.00
10
0.60 
1
2.00
15
0.60 
0
8.00
20
0.60 
0
11.00
20
0.60 
1
5.00
25
0.60 
0
14.00
 
 
 
 
fxtal 
(MHz) 
tQ 
(us) 
DSC 
BRP 
(dec) 
4
0.75 
0
2.00
8
0.75 
0
5.00
8
0.75 
1
2.00
12
0.75 
0
8.00
16
0.75 
0
11.00
16
0.75 
1
5.00
20
0.75 
0
14.00
24
0.75 
0
17.00
24
0.75 
1
8.00
 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 31 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 32 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
 
fxtal 
(MHz) 
tQ 
(us) 
DSC 
BRP 
(dec) 
2 
1 
0 
1.00
3 
1 
0 
2.00
4 
1 
0 
3.00
4 
1 
1 
1.00
5 
1 
0 
4.00
6 
1 
0 
5.00
6 
1 
1 
2.00
7 
1 
0 
6.00
8 
1 
0 
7.00
8 
1 
1 
3.00
9 
1 
0 
8.00
10 
1 
0 
9.00
10 
1 
1 
4.00
11 
1 
0 
10.00
12 
1 
0 
11.00
12 
1 
1 
5.00
13 
1 
0 
12.00
14 
1 
0 
13.00
14 
1 
1 
6.00
15 
1 
0 
14.00
16 
1 
0 
15.00
16 
1 
1 
7.00
17 
1 
0 
16.00
18 
1 
0 
17.00
18 
1 
1 
8.00
19 
1 
0 
18.00
20 
1 
0 
19.00
20 
1 
1 
9.00
 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 32 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 33 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
Appendix 
H 
Bus 
Topology 
Requirements Tables 
Note: The following table includes the number of 
vehicle nodes and the length of the in vehicle wires 
in meters. They are selected such that an external 
test device with a maximum 5 m cable can be 
connected. These lengths are based on a cable 
with a maximum capacitance of 100 pF/m. For 
other capacitance values please multiply the given 
length by 100 and divide by you cable’s pF/m. (E.g. 
for a cable with a capacitance of 200 pF/m multiply 
the length by 100 and divide by 200, to get an 
allowed length of 1/2 of that given in the table.) 
These tables are for reference only. Please contact 
the author of this document for the latest 
interactive Excel spreadsheet to properly design 
your vehicle network. 
Table H1:  Number of Nodes vs. Wire Length 
Based on Clock Tolerance 
# nodes 
Maximum wire 
length in 
meters when 
all nodes 
employ 
0.35% clock 
tolerance 
Maximum 
wire length in 
meters when 
2 or more 
nodes employ 
0.5% clock 
tolerance 
2 
4.1 
2.3 
3 
7 
4.7 
4 
9.9 
7 
5 
12.8 
9.3 
6 
15.7 
11.7 
7 
18.5 
14 
8 
21.3 
16.2 
9 
24.1 
18.4 
10 
26.8 
20.6 
11 
29.5 
22.8 
12 
32.2 
25 
13 
34.9 
27.1 
14 
37.5 
29.2 
15 
40.1 
31.3 
16 
42.6 
33.4 
17 
45.1 
35.4 
18 
47.6 
37.4 
19 
50.1 
39.4 
# nodes 
Maximum wire 
length in 
meters when 
all nodes 
employ 
0.35% clock 
tolerance 
Maximum 
wire length in 
meters when 
2 or more 
nodes employ 
0.5% clock 
tolerance 
20 
52.6 
41.4 
21 
55 
43.4 
22 
57.4 
45.3 
23 
59.8 
47.2 
24 
60 
49.1 
25 
60 
51 
26 
60 
52.8 
27 
60 
54.7 
28 
60 
56.5 
29 
60 
58.3 
30 
60 
60 
31 
60 
60 
 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 33 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 34 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
Appendix I has been deleted 
Appendix 
J 
Reported 
Component 
Problems 
Please refer to the GMLAN website for the latest 
list of known CAN controller and transceiver 
issues. 
The GMLAN website is: 
http://ived.gm.com/electrical/warren/ssltexpt/gmlan/
index.html 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 34 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 35 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
Appendix 
K 
Single 
Wire 
CAN 
Transceiver Supplies 
The 
following 
integrated 
circuit 
transceiver 
component suppliers are known to provide devices 
intended to meet this specification. While ECU 
suppliers 
are 
encouraged 
to 
contact 
these 
suppliers, GM does not specify the use of these 
devices nor does GM waive any testing or 
validation of ECUs which use these devices. 
• 
Freescale Semiconductor 
• 
Melexis 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 35 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 36 页
GMW3089 
GM WORLDWIDE ENGINEERING STANDARDS
 
Appendix L Vehicle Design Checklist 
The following table is designed to assist the vehicle 
system design engineer in making design and 
layout decisions that affect the GMLAN Single Wire 
CAN Network. 
 
 
 
 
 
Parameter 
Limit 
Reference Section 
Reference 
Documents 
 
 
 
 
 
1.  
Number of Nodes 
32 
Table 1 
GMW3089 
2.  
Bus wiring constraints 
-- 
3.3.2 - 4 
GMW3089, 
GMW3173 
3.  
Bus load distribution 
-- 
 ******* 
GMW3089 
4.  
Bus wire length 
60 m – use 
interactive Excel 
spreadsheet 
3.3.3 
GMW3089 
5.  
ESD protection 
at DLC 
3.4.1 
GMW3089, 3173 
6.  
CAN Frame Bit Allocation 
-- 
 
ISO 11898 
7.  
CAN timing clock tolerance 
± 0.35% 
3.2.4, Appendix E 
GMW3089 
8.  
Bus waveform tr & tf, slew rate 
control  
2 µs min 
Table C1, CANH I/O 
pin description 
GMW3089 
9.  
Transceiver bus and µP 
interface & timing 
-- 
Appendix C 
GMW3089 
10.  
ECU Battery wiring offset when 
sleeping 
devices 
shall 
be 
activated 
1.0 volts max. 
Table 1, 3.10.4, 
Appendix C 
GMW3089 
11.  
ECU operating battery voltage 
limits 
6.0 to 18 volts 
Table 1, 3.9 
GMW3089 
12.  
ECU GND wiring offset 
1.3 volts max. 
Table 1, 3.9 
GMW3089 
13.  
High speed download operation 
& operation sequence 
-- 
3.2.2 
GMW3089 
14.  
Selective Awake 
-- 
3.2.3, ******* 
GMW3089 
 
© Copyright 2004 General Motors Corporation All Rights Reserved 
Page 36 of 37 
December 2004
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---


### 第 37 页
GM WORLDWIDE ENGINEERING STANDARD 
GMW3089
 
Appendix M  
Additional Requirements for a 0.5% Clock 
All special requirements when a 0.5% clock are 
used are incorporated into the rest of the 
specification. There are no additional requirements 
for 0.5% clocks to be included here. 
 
© Copyright 2004 General Motors Corporation All Rights Reserved 
December 2004 
Page 37 of 37
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Sold to:DELPHI CORPORATION, ********
Not for Resale,2006/4/26 15:47:14 GMT
No reproduction or networking permitted without license from IHS
--`,,``,````,,``,`,,,-`-`,,`,,`,`,,`---

