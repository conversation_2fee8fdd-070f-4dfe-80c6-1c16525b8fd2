# GMW_3173_EN_2013-02_数据传输总线的导线选型和线束要求.pdf

## 文档信息
- 标题：
- 作者：
- 页数：30

## 文档内容
### 第 1 页
 
 
 
 
 
 
 
 
WORLDWIDE 
ENGINEERING 
STANDARDS 
General Specification 
GMW3173 
 
 
 
 
 
 
 
 
ISO Cable Selection and Physical Harness Requirements for Vehicle 
Bus Data Transmission 
 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
 
Page 1 of 30 
 
Table of Contents 
1 Introduction......................................................................................................................................................... 4 
1.1 Scope ........................................................................................................................................................... 4 
1.2 Mission/Theme ............................................................................................................................................ 4 
1.3 Classification ............................................................................................................................................... 4 
2 References ......................................................................................................................................................... 5 
2.1 External Standards/Specifications. .............................................................................................................. 5 
2.2 GM Standards/Specifications. ..................................................................................................................... 5 
2.3 Additional References. ................................................................................................................................ 5 
3 Requirements ..................................................................................................................................................... 5 
3.1 System/Subsystem/Component/Part Definition. ......................................................................................... 5 
3.1.1 Appearance ........................................................................................................................................... 5 
3.1.2 Content. ................................................................................................................................................ 5 
3.1.3 Ambient Environment............................................................................................................................ 5 
3.1.4 Interfaces .............................................................................................................................................. 6 
3.1.5 Usage Definition .................................................................................................................................... 6 
3.2 Cable Selection Guidelines and Constraints. .............................................................................................. 6 
3.2.1 Cable Type. ........................................................................................................................................... 6 
3.2.2 Cable Size ............................................................................................................................................. 6 
3.2.3 Cable Stranding .................................................................................................................................... 6 
3.2.4 Cable Insulation. ................................................................................................................................... 6 
3.2.5 Circuit Twist Requirements. .................................................................................................................. 7 
3.2.6 Differential Characteristic Impedance ................................................................................................... 7 
3.2.7 Capacitance .......................................................................................................................................... 7 
3.2.8 Propagation Delay ................................................................................................................................ 8 
3.3 Connector Selection Guidelines and Constraints. ....................................................................................... 8 
3.3.1 Connector Requirements. ..................................................................................................................... 8 
3.3.2 Terminal Requirements. ....................................................................................................................... 8 
3.3.3 Connector Pin Assignments. ................................................................................................................ 8 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 2 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 2 of 30 
 
3.4 Serial Data Bus Content Integration into Vehicle Wiring. ............................................................................ 9 
3.4.1 Topology Requirements. ....................................................................................................................... 9 
3.4.1.1 Number of Nodes ........................................................................................................................... 9 
3.4.1.2 Topology ......................................................................................................................................... 9 
3.4.2 Bus Length Requirements .................................................................................................................. 10 
3.4.3 Power Requirements. ......................................................................................................................... 10 
3.4.3.1 Voltage Offset ............................................................................................................................... 10 
3.4.3.2 Fuse Sharing ................................................................................................................................ 10 
3.4.4 Ground Requirements. ....................................................................................................................... 11 
3.4.4.1 Voltage Offset ............................................................................................................................... 11 
3.4.4.2 Ground Sharing ............................................................................................................................ 11 
3.4.5 Bus Termination Requirements. ......................................................................................................... 11 
3.4.5.1 Terminator Location ..................................................................................................................... 11 
3.4.5.2 "In Harness" Terminator Value ..................................................................................................... 11 
3.4.5.3 Terminator Ground Wire Length .................................................................................................. 11 
3.4.6 Physical Integration Requirements. .................................................................................................... 11 
******* Wire Harness Protection .............................................................................................................. 11 
3.4.6.2 Wire Harness Routing. ................................................................................................................. 11 
3.4.6.3 Electrical Center Routing ............................................................................................................. 12 
3.4.6.4 Splice Requirements. ................................................................................................................... 12 
3.4.6.5 ESD Protection ............................................................................................................................. 12 
3.4.7 Assembly, Serviceability, and Aftersales Requirements. ................................................................... 12 
3.4.7.1 Data Link Connector (DLC) .......................................................................................................... 12 
3.4.7.2 Splicepack Serviceability Requirements ...................................................................................... 13 
3.4.7.3 Wire Repair and Replace Requirements ..................................................................................... 13 
3.4.7.4 Aftersales Device Provisions. ....................................................................................................... 13 
3.4.7.5 Aftersales Wiring .......................................................................................................................... 13 
4 Validation.......................................................................................................................................................... 14 
4.1 General. ..................................................................................................................................................... 14 
4.1.1 Cable Characteristic Proof of Compliance .......................................................................................... 14 
4.1.2 Characteristic Measurement ............................................................................................................... 14 
4.2 Validation Cross Reference Index ............................................................................................................. 15 
4.3 Supporting Paragraphs .............................................................................................................................. 15 
5 Provisions for Shipping .................................................................................................................................... 15 
6 Notes ................................................................................................................................................................ 15 
6.1 Glossary..................................................................................................................................................... 15 
6.2 Acronyms, Abbreviations, and Symbols. ................................................................................................... 16 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 3 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 3 of 30 
 
7 Additional Paragraphs ...................................................................................................................................... 17 
8 Coding System ................................................................................................................................................. 17 
9 Release and Revisions .................................................................................................................................... 17 
Appendix A - Local Interconnect Network (LIN) Parameters and Topologies .................................................... 18 
A1 LIN Parameter Tables .................................................................................................................................... 18 
A2 LIN Topology Diagram ................................................................................................................................... 19 
Appendix B - Single Wire CAN (SW CAN) Parameters and Topologies ............................................................ 20 
B1 SW CAN Parameter Tables ........................................................................................................................... 20 
B2 SWCAN Topology Diagram ........................................................................................................................... 21 
Appendix C - Mid Speed Dual Wire CAN (MS DWCAN) Parameters and Topologies....................................... 22 
C1 MS DWCAN Parameter Table ....................................................................................................................... 22 
C2 MS DWCAN Topology Diagram .................................................................................................................... 22 
Appendix D - High Speed Dual Wire CAN (HS DWCAN) Parameters and Topologies ..................................... 23 
D1 HS DWCAN Parameter Table ....................................................................................................................... 23 
D2 HS DWCAN Topology Diagram .................................................................................................................... 24 
Appendix E – FlexRay Parameters and Topologies ........................................................................................... 25 
E1 FlexRay Parameter Tables ............................................................................................................................ 25 
E2 FlexRay Topology Diagram ........................................................................................................................... 26 
Appendix F - MOST50 Parameters and Topologies ........................................................................................... 27 
F1 MOST50 Parameter Tables ........................................................................................................................... 27 
F2 MOST50 Topology Diagram .......................................................................................................................... 28 
Appendix G – Ethernet Parameters and Topologies .......................................................................................... 29 
G1 Ethernet Parameter Tables ........................................................................................................................... 29 
G2 Ethernet Topology Diagram .......................................................................................................................... 30 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 4 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 4 of 30 
 
1 Introduction 
Note: Nothing in this standard supercedes applicable laws and regulations. 
Note: In the event of conflict between the English and domestic language, the English language shall take 
precedence. 
1.1 Scope. This document describes cable selection, physical wire harness construction, and topology 
requirements for implementing serial data communication busses in GM vehicles. See the Classification 
section for the list of specific busses covered. 
1.2 Mission/Theme. This standard consolidates wire harness requirements for serial data busses used in GM 
vehicles into a single document. All serial data bus wiring in GM vehicles shall meet the electrical and 
mechanical requirements identified in this document. 
Complete requirements for GM vehicle wiring subsystems, including appearance, ambient environment, 
dependability, reliability, and serviceability, are given in the GM Power and Signal Distribution Subsystem 
Technical Specification (SSTS), CG1763 template. The requirements in this document do not replace any 
requirements in the SSTS, but are additional requirements specific to serial data bus wiring in GM vehicles. 
1.3 Classification. The wired serial data busses in GM vehicles can be classified as follows: 
a. LIN: Local Interconnect Network, as defined in SAE J2602-1, a single wire serial data bus that operates at 
up to 19.2 kbit/s; there can be several independent instances of LIN networks on a GM vehicle. 
b. SW CAN: Single Wire Controller Area Network, as defined in GMW3089 that operates at 33.333 kbit/s 
during normal vehicle operation and 83.333 kbit/s during assembly or service programming. This bus is 
also known as Low Speed GM Local Area Network (LS GMLAN or LS LAN). 
c. DW CAN: Dual Wire Controller Area Network, as defined in GMW3122, that operates at one of two 
different speeds as follows: 
1. MS CAN: Mid Speed CAN that operates at 125 kbit/s, also known as Mid Speed GM Local Area 
Network (MS GMLAN or MS LAN). Future versions may utilize the Automotive Open System 
Architecture (AUTOSAR) messaging format, but the same physical layer. 
2. HS CAN: High Speed CAN that operates at 500 kbit/s. GM vehicles may have one or more instances 
of HS CAN busses, including, but not limited to, the following: 
 
HS GMLAN: High Speed GM Local Area Network, the main HS bus used on all current GM 
vehicles. Future versions may utilize the AUTOSAR messaging format, but the same physical 
layer. 
 
PE CAN: Powertrain Expansion CAN, this is an optional bus dedicated to powertrain functions. 
 
CE CAN: Chassis Expansion CAN, this is an optional bus dedicated to chassis control functions. 
 
Hybrid CAN: This is an optional CAN bus dedicated to hybrid vehicle control functions. 
3. CAN FD: CAN with Flexible Data Rate, a variable speed DW CAN bus that operates at a base speed 
for bus arbitration and at 4x the base speed for data transmission. CAN FD is being considered for 
both MS CAN (base speed 125 kbit/s) and HS CAN (base speed 500 kbit/s) for Global B. 
d. FlexRay: A dual wire serial data bus, as defined in the FlexRay Communication System Electrical Physical 
Layer (FRCEPL) Specification and FlexRay Communication System Electrical Physical Layer Application 
Notes (FRCEPLAN), that operates at 10 Mbit/s and is typically used for chassis control functions. 
e. MOST50: Media Oriented Systems Transport, as defined in the MOST Physical Layer Specification 
(MOSTEPL) and MOST Specification (MOSTSPEC), a dual wire serial data bus that utilizes an electrical 
physical layer and that operates at 50 Mbit/s. It is typically used for "infotainment" functions per GIS-307. 
This bus may also be referred to as electrical MOST (eMOST). 
 
Note: This document does not cover requirements for optical physical layer versions of MOST. 
f. 
Ethernet: A point to point serial data bus that can operate at variety of speeds. Currently GM is planning 
to implement Ethernet at a 100 Mbit/s data rate as defined in IEEE 802.3. Ethernet for "infotainment" or 
other "on board" functions is expected to use the One Pair Ethernet Special Interest Group (OPEN SIG) 
protocol over a single unshielded twisted pair (UTP). Ethernet for "off board" communications via the Data 
Link Connector (DLC) will utilize the 100BaseTX protocol over two UTPs per ISO 13400-3. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 5 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 5 of 30 
 
g. USB: Universal Serial Bus used for communication to customer devices such as cell phones and music 
file (MPEG Layer 3) (MP3) players (not included in this document at the current time, but reserved 
for future use). 
h. LVDS: Low-Voltage Differential Signaling, an analog bus with strict wiring requirements used for 
communication of "infotainment" video signals (not included in this document at the current time, but 
reserved for future use). 
2 References 
Note: Only the latest approved standards are applicable unless otherwise specified. 
Note: In the event of a conflict between the text of this specification and the documents cited herein, the text of 
this specification takes precedence. 
2.1 External Standards/Specifications. 
EIA-364-103 
IEEE 802.3 
ISO 13400-3 
SAE J1962 
EIA-364-108 
ISO 7637-3 
SAE J1128 
SAE J2602-1 
2.2 GM Standards/Specifications. 
GMW3059 
GMW3122 
GMW15626 
GMW15839 
GMW3089 
GMW3176 
 
 
2.3 Additional References. 
 
CG1763 – GM Power and Signal Distribution Subsystem Technical Specification (SSTS) Template 
 
ELEC86-6 – GM Wire Harness Serviceability Best Practices (The supplier shall work with the GM 
responsible engineer to comprehend this requirement.) 
 
GIS-307 – Interface Testing Table 
 
J1962-007 – 16 Pin ALDL Connector, General Motors Terminal Definitions and Descriptions 
 
J1962-Global A – 16 Pin ALDL Connector, General Motors Terminal Definitions and Descriptions for 
Global A 
 
FRCEPL – FlexRay Communication System Electrical Physical Layer Specification 
 
FRCEPLAN – FlexRay Communication System Electrical Physical Layer Application Notes 
 
MOSTEPL – MOST Electrical Physical Layer Specification 
 
MOSTSPEC – MOST Specification 
3 Requirements 
Note: In case of conflict between the electrical specification in this document and the platform-specific wiring 
harness specification, this document shall take precedence.  
Note: All specifications in this standard apply over all ambient environment conditions, operating conditions, 
and product lifetime unless explicitly otherwise noted. 
3.1 System/Subsystem/Component/Part Definition. 
3.1.1 Appearance. Not applicable, however, cable (wire) color requirements will be covered in section 3.2. 
3.1.2 Content. 
******* Physical Content. The serial data bus physical wire harness components covered by this document 
include cable (wire), connectors, terminals, splices, and other applicable wire harness sub components. Serial 
data bus circuit traces in electrical centers are also covered. 
3.1.2.2 Functional Content. Not applicable. 
3.1.3 Ambient Environment. Serial data bus wire harness components shall meet all other requirements of 
this specification across the full range of temperature, humidity, and other environmental conditions that are 
defined in the GM Power and Signal Distribution Subsystem Technical Specification (SSTS), CG1763 
template.  
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 6 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 6 of 30 
 
3.1.4 Interfaces. Connector, terminal, and other interface requirements will be discussed in their individual 
requirements sections. 
3.1.5 Usage Definition. Almost all current and future GM vehicles will utilize one or more DW CAN serial data 
busses. The SW CAN serial data bus is currently used on most GM vehicles but this serial data bus is 
expected to be phased out over time. One or more LIN busses are used on many current GM vehicles and 
usage is expected to increase in the future. The MOST50 serial data bus is currently being used in some GM 
vehicles, but usage is expected to be phased out over time. FlexRay and Ethernet are planned for future 
usage in GM vehicles. USB is currently used in many GM vehicles and LVDS is beginning to be used in some 
GM vehicles. Usage of both USB and LVDS is expected to continue for the foreseeable future. 
3.2 Cable Selection Guidelines and Constraints. 
3.2.1 Cable Type. 
3.2.1.1 Single Wire Busses. Cables used for all single wire serial data busses shall use copper conductors 
and meet all requirements of GMW15626 for single core stranded International Organization for 
Standardization (ISO) cables. 
3.2.1.2 Dual Wire Busses. Cables used for all dual wire serial data busses shall use copper conductors and 
meet all requirements of GMW15626 for single core stranded ISO cables or GMW15839 for sheathed, multi-
core ISO cables. 
Unless otherwise specified, dual wire busses may be constructed using either manually twisted single core 
cables or sheathed, twisted multi-core cables provided all other requirements in this document are met. 
Note: It is expected that in most cases, manually twisted single core cables will be the lowest cost solution. 
3.2.1.3 Shielded Cable. Shielded cable shall not be used for serial data busses unless electromagnetic 
compatibility (EMC) testing demonstrates such shielding resolves a noise coupling issue. 
Note: Cables meeting the requirements of SAE J1128 may continue to be used for single and dual wire serial 
data circuits on programs that have not yet converted to ISO wire. 
3.2.2 Cable Size. Cables used for all serial data busses shall have conductor cross sections of either 
0.35 mm2 or 0.5 mm2. Determination of the proper size within these limits should be based upon other factors, 
including terminal crimp, splice construction, and/or durability requirements. This requirement is also specified 
in the Tables A1, B1 and C1 thru G1 in corresponding Appendices. 
Note: These cable sizes inherently meet the resistance requirements of all serial data busses. 
3.2.2.1 Engine Applications. All cable used for serial data wires in "on engine" locations shall be 0.5 mm2 to 
withstand the increased engine vibration levels. 
3.2.3 Cable Stranding. Cable used for serial data circuits shall be symmetrical (ISO Type A). Asymmetrical 
Cable (ISO Type B) shall be prohibited. "High flex" cable (ISO Type C) may be used in locations that require 
additional fatigue resistance. 
3.2.4 Cable Insulation. 
3.2.4.1 Insulation Material. 
3.2.4.1.1 Single Wire Busses. The default cable insulation material for all single wire serial data bus circuits 
shall be cross-linked polyethylene (XLPE), but polyvinyl chloride (PVC) may be used if shorter bus length 
constraints are met. 
3.2.4.1.2 Dual Wire Busses. The default cable insulation material for all dual wire serial data bus circuits shall 
be XLPE. Other insulation materials are allowed if they are demonstrated to meet all of the other requirements 
of this document. 
3.2.4.2 Insulation Thickness. The default insulation thickness for all serial data bus circuits shall be thin wall. 
Thick wall insulation may be used in locations requiring additional abrasion resistance, but must continue to 
meet all of the other requirements of this document. Ultra-thin wall cable shall not be used for serial data 
circuits. 
3.2.4.2.1 Truck Chassis Applications. If connector cavity sizes allow, thick wall insulation shall be used for 
serial data circuits in truck chassis locations to improve abrasion resistance when exposed to increased levels 
of grit and vibration. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 7 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 7 of 30 
 
******* Insulation Color. The default insulation colors, including stripes, shall be as defined in GMW3176. All 
color deviations shall be documented in the vehicle electrical schematics. Color deviations may be allowed in 
the following two special situations. 
*******.1 Sheathed Cable Color Deviation. To avoid the high cost of having special sheathed cables made, 
standard sheathed cable colors may be used. If options exist, select the standard colors closest to the defined 
GMW3176 colors. 
*******.2 Error Proofing Color Deviation. In certain cases, bus wire color deviations may be requested to 
alleviate potential wire harness mis-builds. An example is when bus circuits in a ring topology share the same 
connectors at both ends within a wire harness. If error proofing color deviations are requested, the change 
should be limited to use of an alternative stripe color. 
3.2.5 Circuit Twist Requirements. 
******* Twist Length. Dual wire bus circuits shall be twisted to prevent EMC coupling. The length of each 
360 degree twist shall be constrained to the range of 20 mm to 40 mm. This requirement is also specified in 
Tables C1 thru G1 in corresponding Appendices. 
Note: Alternatively, the twist rate may be defined as 25 twists/meter to 50 twists/meter. 
******* Untwist Length at Ends. When dual wire bus circuits terminate at a connector or splice, the untwisted 
length shall be constrained to 50 mm maximum, with less preferred. The ends of the twisted wires shall be 
taped if necessary to ensure that the bus wires cannot untwist during lead prep or wire harness assembly. This 
requirement is also specified in Tables C1 thru G1 in corresponding Appendices. 
******* Twisted Circuit Taping Requirements. 
*******.1 Ethernet, MOST50, and FlexRay. If manually twisted single core cable is used for Ethernet, 
MOST50, and FlexRay serial data bus circuits, each twisted pair shall be covered in solid tape to within 50 mm 
of each connector as defined in ******* Wire Harness Protection section of this document. 
*******.2 Dual Wire CAN. If manually twisted single core cable is used for the Dual Wire CAN bus, spot tapes 
shall be used at the ends of the twisted sections to ensure that the ends do not untwist after lead preparation is 
complete. 
******* Sheath Striping Restrictions. If sheathed twisted pair cable is used for dual wire serial data circuits, 
the sheath strip length shall be 50 mm or less to ensure that the circuits remain twisted to within 50 mm of 
each connector. 
******* Twist Requirements at a "Looped Back" Connector. FlexRay, MOST50, and Ethernet circuits that 
loop back to a common wire harness connector shall be twisted in a manner that meets all other requirements 
of this specification. Dual wire CAN circuits that loop back to a common wire harness connector may be taped 
together instead of twisting if they are less than 150 mm in length and if they are held together by either solid 
over tape or three spot tapes located approximately equally along their length. See Figure 1 for illustrations of 
"looped back" connector circuits and twisting/taping options. 
Note: "Looped back" connectors may be used to avoid wire harness proliferation when an optional module is 
not present in an interfacing wire harness. 
3.2.6 Differential Characteristic Impedance. To ensure maximum signal transmission integrity, the circuits 
that make up each dual wire bus must meet specific differential characteristic impedance requirements when 
tested according to the methodologies provided in Section 4. This parameter can also be called the "differential 
line impedance". See Tables C1 thru G1 in corresponding Appendices for the specific differential characteristic 
impedance requirements. 
3.2.7 Capacitance. To ensure maximum signal transmission integrity, the circuits that make up each dual wire 
bus must also meet certain capacitance requirements. 
******* Conductor to Ground. The cable used for single wire busses shall meet certain limits for capacitance 
to ground when tested according to the methodology provided in Section 4. See Tables C1 thru G1 in 
corresponding Appendices for the specific conductor to ground capacitance requirements. 
3.2.7.2 Conductor to Conductor. The cable used for the CAN and FlexRay dual wire busses shall meet 
certain limits for conductor to conductor capacitance when tested according to the methodology provided in 
Section 4. See Tables A1 and B1 in Appendices A and B for the specific conductor to conductor capacitance 
requirements. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 8 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 8 of 30 
 
 
 
Figure 1: Looped Back Connector Circuits 
 
3.2.8 Propagation Delay. The cable used for most serial data busses shall meet certain limits for the speed of 
signal transmission along bus wires, otherwise known as propagation delay, when tested according to the 
methodology provided in Section 4. See Tables A1, B1, and C1 thru G1 in corresponding Appendices for the 
specific propagation delay requirements. 
3.3 Connector Selection Guidelines and Constraints. 
3.3.1 Connector Requirements. 
3.3.1.1 Connector Sealing. All device and inline connectors containing serial data circuits shall be sealed if 
they are located in a wet environment or are within 50 mm of the floor pan or are located under carpet. 
3.3.1.2 Cavity Sizes. All device and inline connectors containing serial data circuits shall, at a minimum, have 
cavities sized to accommodate cable sizes of 0.35 mm2 to 0.5 mm2 for ISO and SAE thin wall cable. 
Connectors expected to be used in truck chassis or other high abrasion locations, should ideally have 
connector cavities that allow use of 0.5 mm2 ISO thick wall cable. 
3.3.1.3 Connector Position Assurance (CPA). CPA devices shall be used on all device and inline connectors 
containing serial data circuits. 
3.3.2 Terminal Requirements. 
3.3.2.1 Terminal Plating. Serial data circuits have no inherent special terminal plating requirements; however, 
the same plating material shall be used on both halves of a mated terminal pair. Gold terminals should be 
avoided to reduce the chance of an inadvertent tin-gold interface being created. 
Note: If a fretting corrosion issue arises with tin plated terminals due to excessive movement at the terminal 
interface, use of silver plated terminals may resolve the issue. 
3.3.3 Connector Pin Assignments. 
3.3.3.1 Number of Required Pins. The following connector pin count information is provided for reference 
only because it may impact wire harness design without being a wire harness requirement. See Figure 2 for 
example illustrations. 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 9 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 9 of 30 
 
DWCAN
ECU
CAN_H
CAN_H
CAN_L
CAN_L
SWCAN
ECU
CAN
CAN
DWCAN
ECU
D
W
C
A
N
T
R
A
N
S
CAN_H
CAN_L
T
E
R
M
D
W
C
A
N
T
R
A
N
S
S
W
C
A
N
T
R
A
N
S
 
Figure 2: GMLAN Connections to Electronic Control Units (ECU) 
 
a. LIN. Modules connected to a LIN bus will generally have a single pin for each LIN bus application, 
although two pins are allowed. 
b. SW CAN. Modules connected to the SW CAN bus will generally have two bus pins to support linear, ring, 
star, and combination bus topologies. 
c. DW CAN. Modules connected to the DW CAN will generally have two sets of internally spliced serial data 
bus pins to allow the wiring to be implemented as a module to module "daisy chain". Pin count exceptions 
may exist for modules that contain a terminating resistor or that utilize industry standard module hardware. 
d. FlexRay. Modules connected to the FlexRay bus will have two sets of internally spliced serial data bus 
pins for each FlexRay channel to allow the wiring to be implemented in a module to module backbone. 
e. MOST50. Modules for the MOST bus will have a single set of specific serial data bus pins for "transmit" 
(Tx) and "receive" (Rx) that are not internally connected and that are not interchangeable. 
f. 
Ethernet. A gateway module will have specific Ethernet Tx and Rx pin pairs for the 100BaseTX 
connection to the DLC. Modules that are part of an onboard OPEN SIG Ethernet network will only have 
one set of bidirectional bus pins if they do not contain an internal Ethernet switch, but may have two or 
more sets of bidirectional bus pins if they contain a switch. 
3.3.3.2 Pin Assignment Locations. Circuit pairs for dual wire serial data busses shall be located in adjacent 
cavities at device and inline connectors to minimize signal EMC coupling issues. 
If possible, serial data circuit connector cavities should be located as far away as possible from connector 
cavities for potential crosstalk victim and threat circuits. Victim circuits are sensitive circuits that could be 
affected by electrically and magnetically coupled "cross talk" from serial data circuits. Threat circuits are high 
current and/or high frequency circuits that could couple noise into serial data signals. 
3.4 Serial Data Bus Content Integration into Vehicle Wiring. 
3.4.1 Topology Requirements. 
3.4.1.1 Number of Nodes. Each serial data bus is designed to support a different maximum number of nodes 
on the vehicle, not including a service tool. See the Tables A1, B1, and C1 thru G1 in corresponding 
Appendices for the specific node limit requirements. 
3.4.1.1.1 Master and Slave Nodes. Each instance of a LIN bus shall have a single Master node and shall 
support one or more Slave nodes as defined in Table A1 in Appendix A. 
3.4.1.1.2 Unit and Primary Nodes. The Single Wire Can bus shall be comprised of Unit and Primary Nodes 
that have different internal resistances as defined in GMW3089. The maximum number of total nodes shall be 
reduced by one for each Primary node used. 
3.4.1.2 Topology. Each serial data bus is designed to support one or more different wiring topologies as 
defined below. See the Topology Diagrams in Appendices A, B, D, E, F and G for the specific topology options 
and requirements for each serial data bus. 
a. Linear. A "linear" serial data bus topology has the modules connected in series from one end to the other. 
Some serial data bus types require a linear topology. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 10 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 10 of 30 
 
b. Ring. A "ring" serial data topology is similar to linear except that the end modules are connected together 
closing the ring. Ring topologies have some fault tolerance benefits, but diagnosis of bus faults can be 
more difficult. Some serial data bus types require a ring topology. 
c. Star. A "star" serial data bus topology utilizes splices or splicepacks as "star concentrators" to connect 
three or more modules to the bus at a common location. A star topology may utilize one or more star 
concentrators as necessary to meet wire length requirements for the connected modules. 
d. Backbone. The term "backbone" is used to describe the main communication trunk of a linear serial data 
bus. Stubs may connect module to the backbone or else "daisy chain" connections can be used along the 
backbone. 
e. Stub. A stub is a branch off the backbone of a linear serial data bus that is not part of the main daisy chain 
between ECUs. A stub is connected to the "backbone" using a splice, splicepack, double crimp at an 
unsealed connector, or extra set of pins on a module. 
f. 
Daisy Chain. The term "daisy chain" is the term used for a linear data bus when serial data bus wiring is 
connected module to module with no external stubs. This is the case when two sets of module serial data 
pins are connected to the bus. 
Note: See Figures in Appendices A, B, D, E, F and G for example illustrations of the above topologies. 
3.4.1.2.1 Connection Order Along Backbone. For linear serial data busses that support "run critical" 
functions, it is desirable to select a module connection order that does not place "non-run critical" modules 
between "run critical" modules, if bus length limits allow, so that certain faults within a "non-run critical" module 
do not disrupt communications between the "run critical" modules on either side. 
3.4.1.2.2 Connection via Daisy Chain or Stub. For dual wire serial data bus modules that have two sets of 
serial data bus pins, connection via a "daisy chain" will often provide the lowest cost and highest reliability, 
however, disconnection of a daisy chained module will split the bus into two portions disrupting some or all 
communications between other modules that continue to be connected to the bus. 
Stub connections should be used if possible for modules that are located in "high damageability" areas or for 
"non-run critical" modules that are located between "run critical" modules along a linear backbone. 
3.4.2 Bus Length Requirements. Each serial data bus has different length requirements that may include 
requirements for one or more of the below length categories. See Tables A1, B1, and C1 thru G1 in 
corresponding Appendices for the bus length requirements defined below. 
a. Total Length On Vehicle. This is the total length of the serial data bus, including the ring/backbone and 
all star/stub connections. This does not include an additional 5 m that are allowed for a service tool when 
applicable. 
b. Branch Length. This is the total length of a branch circuit from a FlexRay Active Star. 
c. End to End Length. This is the maximum routed wire length between the two terminators or the two most 
distant modules along a backbone. 
d. Node to Node Length. This is the maximum length between two adjacent nodes in a backbone or ring 
topology. 
e. Stub Length. This is the maximum length from the backbone to a node that does not contain an internal 
"daisy chain" connection. 
3.4.3 Power Requirements. 
3.4.3.1 Voltage Offset. Power feed circuits to single wire serial data bus modules shall meet voltage drop 
requirements between the battery positive terminal and the module connector power pin under worst case 
operating and adjacent loading conditions. See Tables A1 and B1 in Appendices A and B for the power voltage 
offset requirements. 
3.4.3.2 Fuse Sharing. For the single wire CAN bus, the bus time constant requirement shall continue to be 
met if any single fuse is opened or disconnected. This effectively limits the number of SW CAN modules that 
can be connected to a single fuse. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 11 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 11 of 30 
 
3.4.4 Ground Requirements. 
3.4.4.1 Voltage Offset. Ground feed circuits to certain serial data bus modules shall meet voltage drop 
requirements between module ground pin and the primary vehicle ground plane (typically the vehicle body) 
under worst case operating and adjacent loading conditions. See Tables A1, B1, and C1 thru G1 in 
corresponding Appendices for the ground voltage offset requirements. 
Note: For SW CAN, the ground offset requirements technically apply from each SW CAN module ground to all 
other SW CAN module ground pins under worst case operating conditions, but a simpler evaluation of ground 
offset to the primary ground plane may be easier to perform and sufficient to determine compliance with the 
requirement in most cases.  
3.4.4.2 Ground Sharing. For the single wire CAN bus, the bus time constant requirement shall continue to be 
met if any single ground attachment is disconnected. This requirement does not apply for the ground 
attachment that includes the power mode master (typically the Body Control Module (BCM) logic ground). This 
requirement effectively limits the number of SW CAN modules that can be connected to a single ground ring 
terminal or that can share stacked ring terminals that utilize a common ground attachment fastener. 
3.4.5 Bus Termination Requirements. 
3.4.5.1 Terminator Location. The dual wire CAN busses allow the bus terminating resistors to be located in 
the wire harness or in a module. All other serial data busses require their terminating resistors to be located in 
a module. 
Note: One terminator for the HS GMLAN bus is typically located in the Engine Control Module (ECM). 
3.4.5.2 "In Harness" Terminator Value. Dual wire CAN bus "in harness" terminators shall meet the 
resistance values defined in Tables C1 and D1 in Appendices C and D. 
3.4.5.3 Terminator Ground Wire Length. Ground circuits to modules containing DW CAN and FlexRay 
terminators shall meet maximum length requirements as defined in Tables C1, D1, and E1 in corresponding 
Appendices to ensure proper EMC performance. Terminator module ground wire lengths exceeding the 
requirement shall be reviewed and approved in an EMC Peer Review with the GM EMC Department. 
3.4.6 Physical Integration Requirements. 
******* Wire Harness Protection. Sheathed serial data circuits inherently have an additional layer of physical 
protection within a wire harness bundle. Single wire serial data circuits and dual wire serial data circuits that 
are created from manually twisted single core cables require additional protection as follows: 
*******.1 Fully Protected Wire Harness Bundles. For wire harness bundles in which the entire length is 
either covered in solid abrasion resistance tape or is covered in solid vinyl tape, then conduit, and then another 
layer of solid vinyl tape over the conduit, the additional serial data circuit protection requirements are: 
*******.1.1 Ethernet, MOST50, and FlexRay. Each twisted pair shall be covered with solid vinyl tape to within 
50 mm of each connector before inclusion in the wire harness bundle to prevent the bus circuits from 
separating. 
*******.1.2 DWCAN, SWCAN, and LIN. No additional protection of these serial data circuits is required. 
*******.2 All Other Wire Harness Bundles. For all wire harness bundles that are not "fully protected" as 
defined above, the additional serial data circuit protection requirements are: 
*******.2.1 Ethernet, MOST50, and FlexRay. Each twisted pair shall be separately covered with solid 
abrasion resistance tape to within 50 mm of each connector before inclusion in the wire harness bundle to 
prevent the bus circuits from separating and to provide an addition layer of physical protection. 
*******.2.2 DWCAN, SWCAN, and LIN. All serial data wires for these busses shall be covered with solid 
abrasion resistance tape for physical protection, but may be grouped together under a common layer of 
abrasion resistance tape if co-routed within a common bundle. 
3.4.6.2 Wire Harness Routing. 
3.4.6.2.1 Proximity to Active Antennas. Serial data bus circuits shall not be routed within 100 mm of 
non-fixed mast active antenna systems. If routing within 100 mm is required, the bus circuits shall be shielded. 
3.4.6.2.2 Crash Zones. Single wire CAN and all dual wire busses shall not be routed through primary crash 
zones and other "high damageability" areas, unless no practical alternative exists. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 12 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 12 of 30 
 
3.4.6.2.3 Cross Talk. To avoid opportunities for wire to wire coupling (i.e., "cross talk"), single wire CAN and 
all dual wire busses shall not be routed in the same wire harness bundle with sensitive or noisy circuits, unless 
no practical alternative exists. Shielding may be required if co-routing is unavoidable. 
3.4.6.3 Electrical Center Routing. Serial data bus circuits shall not be routed through a Bussed Electrical 
Center (BEC) unless no practical alternative exists. 
3.4.6.3.1 LIN and CAN. If LIN or CAN circuits are routed through a BEC, they shall utilize adjacent "through 
pins" from a connector on one side of the BEC to a connector on the other side of the BEC if possible. 
If routing of these serial data bus signals on the BEC printed circuit board (PCB) is necessary, the routing shall 
be as short as possible (recommend < 50 mm) and for DW CAN the bus traces shall be kept adjacent to each 
other with the closest possible spacing and shall have no other circuit traces "in between". If possible, the 
serial data traces should be routed above a ground plane with no gaps below. The serial data traces shall not 
be adjacent to traces for sensitive (e.g., low signal level sensor signals) and noisy circuits (e.g., injector drivers 
or other circuits with rapid voltage or current transitions). 
3.4.6.3.2 FlexRay. If FlexRay circuits are routed through a BEC, they shall utilize adjacent "through pins" from 
a connector on one side of the BEC to a connector on the other side of the BEC if possible. 
If routing of FlexRay data bus signals on the BEC PCB is necessary, the routing shall be as short as possible 
(recommend < 50 mm) and the bus traces shall be kept adjacent to each other with the closest possible 
spacing and shall have no other circuit traces "in between". Ground traces shall be provided on both sides of 
FlexRay pair. FlexRay traces shall only utilize corners with angles of 45 degrees or less. The FlexRay traces 
shall not be adjacent to traces for sensitive (e.g., low signal level sensor signals) and noisy circuits 
(e.g., injector drivers or other circuits with rapid voltage or current transitions). 
Note: A BEC PCB layout EMC Design Review shall be scheduled with the GM EMC Department prior to 
integration release if serial data bus circuits are routed within a BEC. 
3.4.6.3.3 MOST50 and Ethernet. If MOST50 or Ethernet circuits are routed through a BEC, they shall only 
utilize adjacent "through pins" from a connector on one side of the BEC to a connector on the other side of the 
BEC. No routing of MOST50 or Ethernet circuits on the BEC PCB shall be allowed. The MOST50 or Ethernet 
"through pins" shall not be adjacent to pins for other sensitive and noisy circuits. 
3.4.6.4 Splice Requirements. 
3.4.6.4.1 LIN and CAN. Splices for the dual wire CAN and all single wire busses may use sonic welds, 
splicepacks, or a double crimp at an unsealed connector terminal if the connector terminal and cavity size 
allow. 
3.4.6.4.1.1 Splice Sealing Requirements. All LIN and CAN circuit splices and splicepacks in wet areas or 
below carpet or within 50 mm of the floor pan within the interior zone shall be sealed. 
3.4.6.4.1.2 DW CAN Sonic Splice Taping Requirements. To minimize untwisted loop area at sonic splices, 
the untwisted portion of the bus wires at a sonic splice shall be covered in vinyl tape to keep the circuits from 
separating. 
3.4.6.4.2 FlexRay, MOST50, and Ethernet. Splices shall not be permitted in FlexRay, MOST50, or Ethernet 
circuits. All FlexRay, MOST50, and Ethernet wiring shall be point to point. 
3.4.6.5 ESD Protection. Centralized Electrostatic Discharge (ESD) protection located in the wiring shall not be 
considered. Each module shall be internally protected against ESD. 
3.4.7 Assembly, Serviceability, and Aftersales Requirements. 
3.4.7.1 Data Link Connector (DLC). Serial data circuit presence and pinout at the DLC shall be as defined in 
the appropriate General Motors version of SAE J1962. Legacy programs shall use J1962-007 (GM), while 
Global A shall use J1962-Global A. Global B shall use J1962-Global A until a new document is created 
specifically for it. 
3.4.7.1.1 MOST50 Development Connector. A mated 2-pin inline connector pair shall be provided in the 
MOST50 bus loop in an easily accessible location for diagnostic and programming purposes for all mule, 
integration, and manufacturing validation vehicle builds. It may be deleted at or after start of regular production 
(SORP). Typical locations for this connector are in the instrument panel near the DLC connector or on the right 
hand side of the trunk. The development inline connector pair shall be defined in Table 1. An illustration 
showing this connector is provided in Figure F1 (Appendix F). 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 13 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 13 of 30 
 
Table 1: MOST50 Development Connector Requirements 
Item 
Value 
Comment 
Tx Connector 
Part Number 
19167918 
(F) 
The circuits in this connector route to the Tx pins of a module installed in 
the vehicle (and will connect to the Rx pins of a Diagnostic Tool) 
Rx Connector 
Part Number 
13580902 
(M) 
The circuits in this connector route to the Rx pins of a module installed in 
the vehicle (and will connect to the Tx pins of a Diagnostic Tool) 
(+) Connector Pin 
1 
This is typically circuit number 3998 
(-) Connector Pin 
2 
This is typically circuit number 3997 
 
3.4.7.2 Splicepack Serviceability Requirements. For diagnostic troubleshooting, splicepacks shall be used 
as "star concentrators" for the LS CAN and MS CAN busses, rather than sonic splices. Splicepacks are 
allowed for "stub" connections on the HS CAN and FlexRay busses. All serial data splicepacks shall be located 
in easily serviceable locations as defined in GM Wire Harness Serviceability Best Practices (e.g., ELEC86-6). 
3.4.7.3 Wire Repair and Replace Requirements. Wire repair circuits shall meet all cable and twisting 
requirements and shall be appropriately covered in abrasion tape and/or conduit as specified elsewhere in this 
document. 
3.4.7.3.1 LIN and CAN. Circuits for the LIN and CAN busses may be repaired using normal service techniques 
and repair splice connectors if wire damage occurs. Dual wire repair circuits shall be twisted as specified 
elsewhere in this document. 
3.4.7.3.2 FlexRay, MOST50, and Ethernet. Circuits for the FlexRay, MOST50, and Ethernet busses shall not 
be repaired if wire damage occurs. Instead entire new circuit pairs constructed from sheathed twisted pair 
cable shall be used to replace the damaged circuits for their entire length from connector to connector. The 
sheathed cable shall be routed along the original wire harness bundle and shall be properly retained to protect 
it from pinch, cut, and chafe opportunities. Untwist lengths at the ends of the repair cable shall meet the 
requirements specified elsewhere in this document. If this type of circuit replacement is not possible, the entire 
wire harness shall be replaced. 
3.4.7.4 Aftersales Device Provisions. 
3.4.7.4.1 LIN and CAN. Appropriate serial data bus connection points shall be provided for GM approved 
aftermarket LIN and CAN devices after vehicle build. Access to serial data circuits, power, and ground may be 
provided through a dedicated connector on an ECU or electrical center. Access may alternatively be provided 
by branching off an existing connector that contains the appropriate signals as illustrated in Figure 3. Access to 
the serial data bus signals shall not be obtained by splicing into the existing harness assemblies. 
3.4.7.4.2. FlexRay, MOST50, and Ethernet. Aftermarket devices are not allowed to be added to these 
busses. 
3.4.7.4.3 Aftersales Connection at DLC. The DLC may be considered as a location to access the bus signals 
for aftersales devices. If the DLC is used, packaging space for the aftersales device shall be provided in the 
immediate vicinity of the DLC to allow stub length requirements to be met. DLC availability and access for 
vehicle service shall continue to be provided. 
3.4.7.5 Aftersales Wiring. Wiring for the aftersales device that is provided as part of the aftersales kit shall be 
designed to meet all wiring requirements documented in this specification, including: 
 
All cable and twist requirements 
 
All bus length and stub length requirements 
 
All voltage offset and fuse sharing requirements 
 
All harness routing and physical protection requirements 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 14 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 14 of 30 
 
 
Figure 3: Adding Devices to GMLAN Buses Using Branch off Existing Connector 
 
4 Validation 
4.1 General. 
4.1.1 Cable Characteristic Proof of Compliance. Proof of compliance to all cable requirements defined in 
Section 3.2 shall be provided to General Motors by the wire harness supplier upon completion of the initial wire 
harness design. 
4.1.1.1 Proof of Compliance Update. The wire harness supplier shall provide updated proof of compliance to 
General Motors whenever a change is made to serial data cable characteristics, including but not limited to 
changes to the cable supplier, cable type, cable stranding, insulation material, or twist rates. 
4.1.2 Characteristic Measurement. All cable characteristic data for proof of compliance shall be obtained 
using the following test methodologies across the full range of temperature and humidity conditions defined in 
Section 3.1.3 (Reference CG1763). 
4.1.2.1 Conductor to Ground Capacitance. For single wire busses, conductor to ground capacitance shall be 
measured using the following procedure: 
a. Prepare a wire sample that is 1.3 m ± 0.3 m in length. 
b. Place the wire sample in a straight line within an ISO 7637-3 capacitive coupling clamp with one end 
un-terminated and the other end connected to a capacitance meter. 
c. Measure the open circuit capacitance at the frequency specified in Tables A1 and B1 in corresponding 
Appendices. 
d. If the sample length is other than 1.0 m, divide the capacitance value by the length to obtain the 
capacitance per meter value. 
4.1.2.2 Conductor to Conductor Capacitance. For dual wire busses, conductor to conductor capacitance 
shall be measured using the following procedure: 
a. Prepare a twisted dual wire sample that represents worst case manufacturing variation that is 
1.3 m ± 0.3 m in length. 
b. Place the sample in a straight line on nonconductive surface with one end un-terminated (i.e., open) and 
the other end connected to an impedance meter. 
c. Measure the open circuit capacitance at the frequency specified in Tables C1 thru G1 in corresponding 
Appendices. 
d. If the sample length is other than 1.0 m, divide the capacitance value by the length to obtain the 
capacitance per meter value. 
4.1.2.3 Propagation Delay. Propagation delay shall be measured using procedure EIA-364-103. 
******* Differential Characteristic Impedance. The measurement procedure for differential characteristic 
impedance varies according to the speed of the serial data bus as follows: 
*******.1 Differential Characteristic Impedance Using the Open-Short Method. For the DW CAN and 
FlexRay serial data busses, determination of differential characteristic impedance shall use the Open-Short 
Method at the frequencies defined in Tables C1, D1 and E1 in Appendices C, D and E. The Open-Short 
Method is defined as follows: 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 15 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 15 of 30 
 
a. Prepare a twisted wire sample that is the same length as the intended application. If length is unknown, 
assume a 1 m length. 
b. Set the measurement frequency on the impedance meter to the value specified in Tables C1, D1 and E1 in 
Appendices C, D and E. Measure the open circuit impedance (ZOC) of the sample with the far end of the 
twisted circuits open. 
c. Measure the short circuit impedance (ZSC) of the sample with the far end of the twisted circuits shorted 
together. 
d. Calculate the Characteristic Impedance (Z0) using the following formula: 
Z0 = (ZOC × ZSC) 0.5 
Note: The above measurement procedure is ideally performed on a completed wire harness assembly. 
*******.2 Differential Characteristic Impedance Using the Time Domain Reflectometry (TDR) Method. 
For the MOST50 and Ethernet serial data busses, determination of differential characteristic impedance shall 
be according to procedure EIA-364-108 using a rise time as defined in Tables F1 and G1 in Appendices F 
and G. 
4.2 Validation Cross Reference Index. Not applicable. 
4.3 Supporting Paragraphs. Not applicable. 
5 Provisions for Shipping 
Not applicable. 
6 Notes 
6.1 Glossary. 
100BaseTX: Ethernet protocol that utilizes two UTPs. 
Backbone: The main line of a linear serial data bus. 
Bundle: A grouping of circuits under a common outer covering within a wire harness assembly. 
Bus: Serial data circuits that are connected to two or more ECUs and upon which digital signals are 
communicated among participating ECUs. 
Connector: One half of an electrical connection. 
Crosstalk: Coupling of an electrical signal from circuit to another circuit within a wire harness bundle. 
Daisy Chain: A linear serial data bus topology where wiring is connected ECU to ECU with no stubs. 
Linear: A serial data bus topology where ECUs are connected in series from one end to the other. 
Must/Will: Implies other parts of the vehicles systems are to perform specific functions that the 
connector/wiring supplier cannot control. 
Node: An ECU connected to a serial data bus. 
Ring: A serial data bus topology similar to linear, but where the ends connect to each other forming a ring. 
Shall: A binding provision that must be met. 
Should: A preference or desired conformance which, if not met, must be documented and disclosed to 
General Motors. 
Sonic Splice: Short for ultrasonic splice, a method of permanently bonding two or more circuits together by 
using ultrasonic waves to weld the circuits together. 
Splicepack: A removable wire harness component used to splice two or more circuits together, as opposed to 
a sonic splice that permanently bonds the circuits together and is not removable. 
Star: A serial data bus topology where ECUs are connected together a one or more common points that are 
known as star concentrators. 
Stub: A branch off of a linear bus that is not part of the backbone between ECUs. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 16 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 16 of 30 
 
6.2 Acronyms, Abbreviations, and Symbols. 
ALDL 
Assembly Line Diagnostic Link, also known as the DLC 
AUTOSAR 
Automotive Open System Architecture 
BCM 
Body Control Module 
BEC 
Bussed Electrical Center, an electrical center that contains a printed circuit board 
CAN 
Controller Area Network 
CAN FD 
CAN with Flexible Data Rate, a variable speed DW CAN bus that operates at a base speed 
for bus arbitration and at 4x the base speed for data transmission 
CE CAN 
Chassis Expansion CAN 
CPA 
Connector Position Assurance 
DLC 
Data Link Connector, also known as the ALDL 
DW CAN 
Dual Wire CAN 
ECM 
Engine Control Module 
ECU 
Electronic Control Unit 
EIA 
Electronic Industries Alliance 
EMC 
Electromagnetic Compatibility 
eMOST 
Electrical MOST50 over twisted pair wiring, rather than optical cable 
ESD 
Electrostatic Discharge 
GMLAN 
GM Local Area Network 
GSSLT 
Global Subsystem Leadership Team 
HS CAN 
High Speed DW CAN at 500 kbit/s 
HS GMLAN 
High Speed GM Local Area Network 
IPC 
Instrument Panel Cluster 
ISO 
International Organization for Standardization 
LIN 
Local Interconnect Network 
LVDS 
Low-Voltage Differential Signaling 
LS CAN 
Low Speed SW CAN at 33.333/83.333 kbit/s 
LS GMLAN 
Low Speed GM Local Area Network or LS LAN 
MOST 
Media Oriented Systems Transport 
MOST50 
Media Oriented Systems Transport at 50 Mbit/s 
MP3 
MPEG Layer 3 
MS CAN 
Mid Speed DW CAN at 125 kbit/s 
MS GMLAN 
Mid Speed GM Local Area Network of MS LAN 
OPEN SIG 
One Pair Ethernet Special Interest Group, an Ethernet protocol that utilizes a single UTP 
PCB 
Printed Circuit Board 
PE CAN 
Powertrain Expansion CAN 
PSDS 
Power and Signal Distribution System 
PVC 
Polyvinyl Chloride 
Rx 
Receive 
SAE 
SAE International 
SDM 
Sensing and Diagnostic Module 
SORP 
Start of Regular Production 
SSTS 
Subsystem Technical Specification 
SW CAN 
Single Wire CAN 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 17 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 17 of 30 
 
TDR 
Time Domain Reflectometry 
Tx 
Transmit 
USB 
Universal Serial Bus 
UTP 
Unshielded Twisted Pair 
VES 
Variable Effort Steering 
XLPE 
Cross-linked Polyethylene 
Z0 
Characteristic Impedance 
ZOC 
Open Circuit Impedance 
ZSC 
Short Circuit Impedance 
7 Additional Paragraphs 
7.1 All parts or systems supplied to this standard must comply with the requirements of GMW3059, Restricted 
and Reportable Substances for Parts. 
8 Coding System 
This standard shall be referenced in other documents, drawings, etc., as follows: 
GMW3173 
9 Release and Revisions 
This standard was originated in September 1998. It was first approved by GM LAN Bus Wiring Team in 
February 1999. It was first published in February 1999. 
Issue  
Publication Date 
Description (Organization) 
1 
FEB 1999 
Initial publication. 
2 
JUL 2008 
Add GME14010 content. (Global Electrical Technology Engineering) 
3 
FEB 2013 
Completely revised and rewritten to improve clarity. Added LIN, MOST50, 
and Ethernet requirements. (Power and Signal Distribution Global 
Subsystem Leadership Team (PSDS GSSLT)) 
 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 18 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 18 of 30 
 
Appendix A - Local Interconnect Network (LIN) Parameters and Topologies 
A1 LIN Parameter Tables 
The LIN bus wiring and node count shall adhere to the requirements specified in Tables A1 and A2. 
 
Table A1: LIN Bus Critical Wiring Parameters 
Section 
Parameter 
Minimum 
Nominal 
Maximum 
Units 
Comments 
3.2.2 
Nominal Cable Size 
0.35 
n/a 
0.5 
mm2 
Copper conductor 
******* 
Capacitance – Conductor to 
Ground 
n/a 
n/a 
100 
(130) 
pF/m 
At 20 kHz, 
130 = Reduced 
maximum length 
3.2.8 
Propagation Delay 
n/a 
n/a 
n/a 
ns/m 
 
3.4.1.1 
Number of Nodes 
2 
n/a 
16 
number 
1 master + 1 slave 
to 15 slaves 
3.4.2 (a) 
Bus Length – Total On Vehicle 
n/a 
n/a 
See 
Comment 
m 
Maximum length 
based master 
node capacitance, 
node count and 
wire capacitance 
(see SAE J2602-1 
and Table A2 
below) 
3.4.3.1 
Voltage Offset – Power 
n/a 
n/a 
1.3 
V 
At Vbatt = 13.0 V 
3.4.4.1 
Voltage Offset – Ground 
n/a 
n/a 
1.3 
V 
At Vbatt = 13.0 V 
 
Table A2: LIN Maximum Bus Length Based Upon Node Count and Wire Capacitance 
(For 778 pF Master Node Capacitance) 
Number 
of Slave 
Nodes 
100 pF/
m Wire 
130 pF/
m Wire 
  
Number 
of Slave 
Nodes 
100 pF/
m Wire 
130 pF/
m Wire 
  
Number 
of Slave 
Nodes 
100 pF/
m Wire 
130 pF/
m Wire 
1 
35.8 m 
27.5 m 
  
6 
26.4 m 
20.3 m 
  
11 
16.9 m 
13.0 m 
2 
33.9 m 
26.1 m 
  
7 
24.5 m 
18.8 m 
  
12 
15.0 m 
11.6 m 
3 
32.0 m 
24.6 m 
  
8 
22.6 m 
17.4 m 
  
13 
13.2 m 
10.1 m 
4 
30.1 m 
23.2 m 
  
9 
20.7 m 
15.9 m 
  
14 
11.3 m 
8.7 m 
5 
28.2 m 
21.7 m 
  
10 
18.8 m 
14.5 m 
  
15 
9.4 m 
7.2 m 
Note: The Tables A1 and A2 apply for Global A and Global B. 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 19 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 19 of 30 
 
A2 LIN Topology Diagram 
The topology of the LIN bus may be linear, star, or combination of linear and star, but because most LIN 
modules do not have a second terminal for their LIN circuit, ring topologies are typically not used. Illustrations 
of possible LIN Bus topologies are shown in Figures A1, A2, and A3. 
 
Figure A1: LIN Linear Topology 
 
 
Figure A2: LIN Star Topology 
 
 
Figure A3: LIN Combination Ring and Star Topology 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 20 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 20 of 30 
 
Appendix B - Single Wire CAN (SW CAN) Parameters and Topologies 
B1 SW CAN Parameter Tables 
The SW CAN bus wiring and node count shall adhere to the requirements specified in Tables B1 and B2. 
 
Table B1: SW CAN Bus Critical Wiring Parameters (Global A) 
Section 
Parameter 
Minimum 
Nominal 
Maximum 
Units 
Comments 
3.2.2 
Nominal Cable Size 
0.35 
n/a 
0.5 
mm2 
Copper conductor 
******* 
Capacitance – Conductor to 
Ground 
n/a 
n/a 
100 
(130) 
pF/m 
At 20 kHz, 
130 = Reduced 
maximum length 
3.2.8 
Propagation Delay 
n/a 
5 
10 
ns/m 
 
3.4.1.1 
Number of Nodes 
2 
n/a 
30 
number 
 
3.4.2 (a) 
Bus Length – Total On 
Vehicle 
n/a 
n/a 
60 
(See 
Comment) 
m 
Maximum length 
based upon node 
count, and wire 
capacitance (See 
Table B2 below) 
3.4.3.1 
Voltage Offset – Power 
n/a 
n/a 
1.0 
V 
 
3.4.4.1 
Voltage Offset – Ground 
n/a 
n/a 
1.3 
V 
 
 
Table B2: SW CAN Maximum Bus Length Based Upon Node Count and Wire Capacitance (Global A) 
Total 
Number 
of Nodes 
100 pF/m Wire 
130 pF/m Wire 
  
Total 
Number 
of Nodes 
100 pF/m Wire 
130 pF/m Wire 
Three (3) 
Primary 
Nodes 
Two (2) 
Primary 
Nodes 
Three (3) 
Primary 
Nodes 
Two (2) 
Primary 
Nodes 
  
Three (3) 
Primary 
Nodes 
Two (2) 
Primary 
Nodes 
Three (3) 
Primary 
Nodes 
Two (2) 
Primary 
Nodes 
3 
18 m 
11 m 
12 m 
7 m 
  
17 
50 m 
43 m 
37 m 
32 m 
4 
21 m 
14 m 
15 m 
10 m 
  
18 
53 m 
47 m 
40 m 
35 m 
5 
24 m 
17 m 
17 m 
12 m 
  
19 
57 m 
50 m 
42 m 
37 m 
6 
27 m 
21 m 
20 m 
15 m 
  
20 
60 m 
53 m 
45 m 
40 m 
7 
30 m 
24 m 
22 m 
17 m 
  
21 
60 m 
56 m 
47 m 
42 m 
8 
33 m 
27 m 
25 m 
20 m 
  
22 
60 m 
60 m 
50 m 
45 m 
9 
36 m 
29 m 
26 m 
21 m 
  
23 
60 m 
60 m 
52 m 
47 m 
10 
37 m 
30 m 
27 m 
22 m 
  
24 
60 m 
60 m 
55 m 
50 m 
11 
39 m 
32 m 
29 m 
24 m 
  
25 
60 m 
60 m 
57 m 
52 m 
12 
40 m 
34 m 
30 m 
25 m 
  
26 
60 m 
60 m 
60 m 
54 m 
13 
42 m 
36 m 
31 m 
26 m 
  
27 
60 m 
60 m 
60 m 
57 m 
14 
44 m 
37 m 
32 m 
27 m 
  
28 
60 m 
60 m 
60 m 
59 m 
15 
46 m 
39 m 
34 m 
29 m 
  
29 
60 m 
60 m 
60 m 
60 m 
16 
47 m 
40 m 
35 m 
30 m 
  
30 
n/a 
60 m 
n/a 
60 m 
Note: The above tables apply for Global A. Contact the GM Serial Data Team for the pre-Global A requirements. SW CAN is not expected 
to be used for Global B. 
Note: Global A SW CAN standard primary nodes are the BCM, instrument panel cluster (IPC), and sensing and diagnostic module (SDM). 
Thus typical Global A vehicles will have three (3) primary nodes, but some may only have two (2) primary nodes if the SDM is not present 
or is instead connected to the DW CAN bus. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 21 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 21 of 30 
 
B2 SW CAN Topology Diagram 
The SW CAN bus can support ring, linear, star, and combination topologies, but serviceability requirements 
require use of a star topology that utilizes one or more star concentrators. Localized linear or ring connections 
as part of a combination topology may be allowed if required to reduce bus length, but the primary topology 
shall remain a star. Illustrations of the SW CAN topologies are shown in Figures B1, B2, and B3. 
 
 
Figure B1: SW CAN Linear/Ring Topology 
 
 
Figure B2: SW CAN Star Topology 
 
 
Figure B3: SW CAN Combination Ring and Star Topology 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 22 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 22 of 30 
 
Appendix C - Mid Speed Dual Wire CAN (MS DWCAN) Parameters and Topologies 
C1 MS DWCAN Parameter Table 
The MS DWCAN bus wiring and node count shall adhere to the requirements specified in Table C1. 
 
Table C1: MS DWCAN Bus Critical Wiring Parameters 
Section 
Parameter 
Minimum 
Nominal 
Maximum 
Units 
Comments 
3.2.2 
Nominal Cable Size 
0.35 
n/a 
0.5 
mm2 
Copper conductor 
******* 
Twist Length 
20 
30 
40 
mm 
i.e., 25 twists/m to 
50 twists/m 
******* 
Untwist Length at Ends 
n/a 
25 
50 
mm 
 
3.2.6 
Differential Characteristic 
Impedance 
90 
120 
135 
Ω 
Open-Short Method 
at 62.5 kHz and 
at 250 kHz 
3.2.7.2 
Capacitance – Conductor 
to Conductor 
30 
40 
50 
pF/m 
At 62.5 kHz and 
at 250 kHz 
3.2.8 
Propagation Delay 
n/a 
n/a 
6.4 
ns/m 
 
3.4.1.1 
Number of Nodes 
2 
n/a 
31 
(See 
Comment) 
number 
32 nodes if not 
connected to the 
DLC 
3.4.2 (a) 
Bus Length – Total On 
Vehicle 
n/a 
n/a 
60 
(See 
Comment) 
m 
65 m if not 
connected to DLC 
3.4.2 (d) 
Bus Length – Node to Node 
n/a 
(See 
Comment) 
n/a 
m 
Daisy chain 
segments should 
vary by > 5% 
3.4.2 (e) 
Bus Length – Stub 
0.0 
n/a 
3.5 
m 
Zero allows daisy 
chain, individual 
stub lengths should 
vary by > 5% 
3.4.4.1 
Voltage Offset – Ground 
n/a 
n/a 
2.0 
V 
 
3.4.5.2 
Terminator Value – In 
Harness 
n/a 
121 
n/a 
Ω 
121 Ω ± 1%, 0.5 W 
3.4.5.3 
Terminator Ground Wire 
Length 
n/a 
n/a 
1.0 
m 
For modules 
containing bus 
terminations 
Note: The above requirements apply for Global A. Global B requirements are under development. 
C2 MS DWCAN Topology Diagram 
The topology of the Global A MS DWCAN bus shall be linear with a terminating resistor at each end as shown 
in Figure B1 (Appendix B). Global B topology requirements are under development. 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 23 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 23 of 30 
 
Appendix D - High Speed Dual Wire CAN (HS DWCAN) Parameters and Topologies 
D1 HS DWCAN Parameter Table 
The HS DWCAN bus wiring and node count shall adhere to the requirements specified in Table D1. 
 
Table D1: HS DWCAN Bus Critical Wiring Parameters (pre-Global A and Global A) 
Section 
Parameter 
Minimum 
Nominal 
Maximum 
Units 
Comments 
3.2.2 
Nominal Cable Size 
0.35 
n/a 
0.5 
mm2 
0.5 mm2 required on 
engine, copper 
conductor 
******* 
Twist Length 
20 
30 
40 
mm 
i.e., 25 twists/m to 
50 twists/m 
******* 
Untwist Length at Ends 
n/a 
25 
50 
mm 
 
3.2.6 
Differential Characteristic 
Impedance 
90 
120 
135 
Ω 
Open-Short Method 
at 250 kHz and 
at 1 MHz 
3.2.7.2 
Capacitance – Conductor 
to Conductor 
30 
40 
50 
pF/m 
At 250 kHz and 
at 1 MHz 
3.2.8 
Propagation Delay 
n/a 
n/a 
6.4 
(5.6) 
ns/m 
5.6 = Increased 
length 
3.4.1.1 
Number of Nodes 
2 
n/a 
21 
number 
 
3.4.2 (a) 
Bus Length – Total On 
Vehicle 
n/a 
n/a 
See 
Comment 
m 
Maximum allowable 
bus length varies, 
See below 
3.4.2 (d) 
Bus Length – Node to 
Node 
n/a 
See 
Comment 
n/a 
m 
Daisy chain 
segments should 
vary by > 5% 
3.4.2 (e) 
Bus Length – Stub 
0.0 
n/a 
1.7 
m 
Zero allows daisy 
chain, individual stub 
lengths should vary 
by > 5% 
3.4.4.1 
Voltage Offset – Ground 
n/a 
n/a 
2.0 
V 
 
3.4.5.2 
Terminator Value – In 
Harness 
n/a 
121 
n/a 
Ω 
121 Ω ± 1%, 0.5W 
3.4.5.3 
Terminator Ground Wire 
Length 
n/a 
n/a 
1.0 
m 
For modules 
containing bus 
terminations 
Note: Maximum allowable Global A HS DWCAN bus length varies as follows: 
 
Initial maximum "on vehicle" bus length equals 27 m if the Propagation Delay is 6.4 ns/m or less 
 
Initial maximum "on vehicle" bus length equals 32 m if the Propagation Delay is 5.6 ns/m or less 
 
Add 0.5 m for each "on vehicle" node less than 21 
 
Add 4.0 m if no nodes use twelve (12) time quanta per bit (e.g., no E69) but one or more nodes use 18 time quanta per bit (e.g., 
Variable Effort Steering (VES)) 
 
Add 8.0 m if no nodes use 12 and 18 time quanta per bit (e.g., no E69 and no VES) 
Note: Table D1 and length requirements apply for Global A. Global B requirements are under development. 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 24 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 24 of 30 
 
D2 HS DWCAN Topology Diagram 
The topology of the HS DWCAN bus shall be linear with a terminating resistor at each end as shown in 
Figure D1. HS DWCAN modules with two sets of bus pins may be connected in a daisy chain configuration 
utilizing the internal bus splices, although connection via wiring stubs is allowed and in some cases may be 
required. HS DWCAN modules with only one set of bus pins must be connected via stubs. If module contains a 
terminator that is intended to be optionally connected, it may be implemented on separate pins from the pins 
connected to the bus transceiver. A terminator location in a wire harness is allowed and offers flexibility in 
placement and bus length if optional modules cause many different wiring build configurations to exist. 
 
 
 
DWCAN 
Transceiver 
DWCAN 
Transceiver 
ECU 1 
ECU 2 
DLC 
Twisted Pair 
Twisted Pair 
Twisted  
Pair 
... 
Twisted Pair 
 
DWCAN 
Transceiver 
ECU m 
Platform-specific  
integration module 
ECU n 
Platform-specific  
integration module 
Twisted Pair 
... 
Twisted Pair 
ESD 
 
DWCAN 
Transceiver 
DWCAN 
Transceiver 
ECU 1 
ECU 2 
ECU n 
Platform-specific  
integration module 
DLC 
Twisted Pair 
Twisted Pair 
Twisted  
Pair 
... 
Twisted Pair 
 
ESD 
DWCAN 
Transceiver 
DWCAN 
Transceiver 
 
Figure D1: HS DWCAN Linear Topologies (No Stub Connections shown other than DLC) 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 25 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 25 of 30 
 
Appendix E – FlexRay Parameters and Topologies 
E1 FlexRay Parameter Tables 
The FlexRay bus wiring and node count shall adhere to the requirements specified in Table E1. 
 
Table E1: FlexRay Bus Critical Wiring Parameters 
Section 
Parameter 
Minimum 
Nominal 
Maximum 
Units 
Comments 
3.2.2 
Nominal Cable Size 
0.35 
n/a 
0.5 
mm2 
Copper conductor 
******* 
Twist Length 
20 
30 
40 
mm 
i.e., 25 twists/m to 
50 twists/m, 100% tape 
coverage or sheathed 
cable required 
******* 
Untwist Length at Ends 
n/a 
25 
50 
mm 
 
3.2.6 
Differential Characteristic 
Impedance 
90 
100 
110 
Ω 
Open-Short Method 
at 5 MHz 
3.2.7.2 
Capacitance – Conductor 
to Conductor 
30 
40 
50 
pF/m 
At 5 MHz 
3.2.8 
Propagation Delay 
n/a 
n/a 
6.4 
ns/m 
 
3.4.1.1 
Number of Nodes 
2 
n/a 
6 (See 
Comment) 
number 
Maximum for a passive 
linear bus or "per branch" 
for an active star 
3.4.2 (a) 
Bus Length – Total On 
Vehicle 
n/a 
n/a 
See 
Comment 
m 
Passive linear topology 
only, see below 
3.4.2 (b) 
Bus Length – Branch 
n/a 
n/a 
See 
Comment 
m 
Active star topology only, 
see below 
3.4.2 (c) 
Bus Length – End to End 
n/a 
n/a 
40 
m 
For ending modules on 
different branches of an 
active star 
3.4.2 (d) 
Bus Length – Node to 
Node 
n/a 
See 
Comment 
n/a 
m 
Daisy chain segments 
should vary by >5% 
3.4.2 (e) 
Bus Length – Stub 
0.0 
n/a 
0.2 
m 
Zero allows daisy chain, 
individual stub lengths 
should vary by >5% 
3.4.4.1 
Voltage Offset – Ground 
n/a 
n/a 
2.0 
V 
 
3.4.5.3 
Terminator Ground Wire 
Length 
n/a 
n/a 
1.0 
m 
For modules containing 
primary and secondary bus 
terminations 
Note: The maximum bus length for a passive linear topology is 24 m for two (2) nodes, 20 m for three (3) or four (4)or nodes, 18 m for 
five (5) or six (6) nodes. 
The maximum branch length for an active star topology varies as follows: 
 
Monolithic: Maximum of 24 m for two (2) nodes, 20 m for three (3) or four (4) nodes, 18 m for five (5) or six (6) nodes 
 
Non-Monolithic: Maximum of 10 m for four (4) or less nodes, 8 m for five (5) or six (6) nodes 
Note: The module performing the active star function counts as a node on each branch of the active star, meaning there can be five other 
modules/nodes on each branch. 
Note: Table E1 and length requirements apply for Global A and Global B. 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 26 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 26 of 30 
 
E2 FlexRay Topology Diagram 
The topology of the FlexRay bus may utilize a passive Linear configuration as shown in Figure E1, or it may 
utilize one Active Star with each branch configured as a Linear bus as shown in Figure E2. Active Stars may 
be monolithic, meaning that one silicon device controls all branches; or non-monolithic, meaning branches are 
controlled by different silicon devices. While stub connections are shown in the attached figures, typically 
FlexRay modules will be connected in a daisy chain configuration utilizing two sets of bus pins on each 
intermediate module because of the short stub length requirements. 
ECU
N
ECU
N-1
ECU
3
ECU
2
ECU
1
L4
L2
L1
Primary Bus 
Termination
N = nNodes
ECU
1
ECU
2
L0
 
Figure E1: FlexRay Passive Linear Bus Topologies 
 
ECU
N
ECU
N-1
ECU
2
ECU
1
AS
1
ECU
M
ECU
M-1
ECU
2
ECU
1
ECU
1
ECU
1
ECU
2
Branch1
Branch(n)
Branch2
L4
L2
L1
L3
L0
Primary Bus 
Termination
N = M ≤ nNodes -1
Figure E2: FlexRay Active Star Topology 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 27 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 27 of 30 
 
Appendix F - MOST50 Parameters and Topologies 
F1 MOST50 Parameter Tables 
The MOST50 bus wiring and node count shall adhere to the requirements specified in Table F1. 
 
Table F1: MOST50 Bus Critical Wiring Parameters 
Section 
Parameter 
Minimum 
Nominal 
Maximum 
Units 
Comments 
3.2.2 
Nominal Cable Size 
0.35 
n/a 
0.5 
mm2 
Copper conductor 
******* 
Twist Length 
20 
30 
40 
mm 
i.e., 25 twists/m to 
50 twists/m, 100% 
tape coverage or 
sheathed cable 
required 
******* 
Untwist Length at Ends 
n/a 
25 
50 
mm 
 
3.2.6 
Differential Characteristic 
Impedance 
100 
130 
140 
Ω 
TDR Method 
Use 2.0 ns Rise 
Time 
3.2.7.2 
Capacitance – Conductor 
to Conductor 
n/a 
n/a 
n/a 
pF/m 
 
3.2.8 
Propagation Delay 
n/a 
n/a 
n/a 
ns/m 
 
3.4.1.1 
Number of Nodes 
2 
- 
64 
number 
 
3.4.2 (a) 
Bus Length – Total On 
Vehicle 
n/a 
n/a 
n/a 
m 
 
3.4.2 (d) 
Bus Length – Node to 
Node 
n/a 
n/a 
10 
m 
Maximum of eight 
(8) inline 
connections 
between nodes 
3.4.4.1 
Voltage Offset – Ground 
n/a 
n/a 
n/a 
V 
 
Note: Table F1 applies for Global A. MOST50 is not expected to be used for Global B. 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 28 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 28 of 30 
 
F2 MOST50 Topology Diagram 
The topology of the MOST50 bus shall be a ring with each module containing separate receive (Rx) and 
transmit (Tx) pins as shown in Figure F1. Communications will cease if the ring is broken. 
 
 
Figure F1: MOST50 Ring Topology (with development connector shown) 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 29 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 29 of 30 
 
Appendix G – Ethernet Parameters and Topologies 
G1 Ethernet Parameter Tables 
The Ethernet bus wiring shall adhere to the requirements specified in Table G1. 
 
Table G1: Ethernet Bus Critical Wiring Parameters 
Section 
Parameter 
Minimal 
Nominal 
Maximum 
Units 
Comments 
3.2.2 
Nominal Cable Size 
0.35 
n/a 
0.5 
mm2 
Copper conductor 
******* 
Twist Length 
20 
30 
40 
mm 
i.e., 25 twists/m to 
50 twists/m, 
100% tape 
coverage or 
sheathed cable 
required 
******* 
Untwist Length at Ends 
n/a 
25 
50 
mm 
 
3.2.6 
Differential Characteristic 
Impedance 
85 
100 
115 
Ω 
TRD Method 
Use 1.0 ns rise 
time 
3.2.7.2 
Capacitance – Conductor to 
Conductor 
n/a 
n/a 
n/a 
pF/m 
 
3.2.8 
Propagation Delay 
n/a 
4.8 
5.3 
ns/m 
 
3.4.1.1 
Number of Nodes 
n/a 
n/a 
n/a 
number 
 
3.4.2 (a) 
Bus Length – Total On Vehicle 
n/a 
n/a 
n/a 
m 
 
3.4.2 (d) 
Bus Length – Node to Node 
n/a 
n/a 
n/a 
m 
 
3.4.4.1 
Voltage Offset – Ground 
n/a 
n/a 
n/a 
V 
 
Note: Table G1 applies for Global B. All Ethernet requirements are preliminary and subject to change as development work continues. 
 
 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---


### 第 30 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3173 
 
 
© Copyright 2013 General Motors Company All Rights Reserved 
February 2013 
Page 30 of 30 
 
G2 Ethernet Topology Diagram 
All Ethernet wiring is point to point, meaning that it can only connect two devices. No splicing is allowed to 
connect more than two modules to any Ethernet bus circuit pair. 
Off board communication to a service or programming tool via the DLC is strictly point to point using two 
unshielded twisted pairs (UTPs) and the 100BaseTX Ethernet protocol. 
An onboard Ethernet network using the OPEN SIG protocol over one UTP requires the use of switches to 
allow more than two modules to participate in the network. A switch routes the message to the proper recipient 
node. If one module contains a switch, then all other modules will connect to this module in a star topology as 
shown in Figure G1. Alternatively, each module may contain its own internal switch to allow application in a 
linear topology as shown in Figure G2. Combination star and linear topologies are also allowed. 
 
 
Figure G1: Ethernet Star Topology 
 
 
 
 
Figure G2: Ethernet Linear Topology 
Copyright General Motors Company 
Provided by IHS under license with General Motors Company
Licensee=Kunshan Huguang Auto Harness Co.,LTD/**********, User=Hou, Yanping
Not for Resale, 09/13/2017 19:56:41 MDT
Reproduction, distribution or publication of these standards is expressly prohib
--`,`,,``,,`,`,,,,``,,`,,,,`,``-`-`,,`,,`,`,,`---

