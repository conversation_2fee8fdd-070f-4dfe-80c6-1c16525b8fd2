# GMW_3176_EN_2008-05_系统功能缩写及对应线色要求.pdf

## 文档信息
- 标题：GMN General Specification Template
- 作者：<PERSON>
- 页数：22

## 文档内容
### 第 1 页
 
 
 
 
 
 
 
 
WORLDWIDE 
ENGINEERING 
STANDARDS 
General Specification 
Electrical/Electronic 
GMW3176 
 
 
 
 
 
 
 
 
Circuit List for ISO Parameter Assignment 
 
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
May 2008 
Originating Department: North American Engineering Standards 
Page 1 of 22
 
1 Introduction 
Note: 
Nothing in this standard supersedes 
applicable laws and regulations. 
Note: In the event of conflict between the English 
and domestic language, the English language shall 
take precedence. 
1.1 Scope. This manual provides instructions for 
consistently 
determining 
circuit 
parameters: 
number, 
color, 
subsystem, 
description, 
and 
abbreviated circuit description for vehicle wiring. 
This 
manual 
describes 
the 
processes 
for 
requesting new circuits and obsolesce/deleting 
unused circuits. Its usage is across vehicle 
platforms 
for 
similar 
electrical 
devices 
and 
functions. The intended audience includes, but is 
not limited to, platform system/design engineers, 
wiring release engineers, and harness suppliers. 
1.2 Mission/Theme. The purpose of the GMW 
Circuit List is to develop and maintain a corporate 
owned circuit list that meets the requirements for 
global vehicle wiring per GMW15626. 
1.3 Classification. This standard applies to all GM 
vehicles. 
2 References 
Note: Only the latest approved standards are 
applicable unless otherwise specified. 
2.1 External Standards/Specifications. 
IEC 304 
ISO 6722
IEC 757
SAE J1128
2.2 GM Standards/Specifications. 
GMW3059 
GMW15626 
2.3 Additional References. 
Global Common Serial Data (Class 2) Power 
Moding Specification Revision A 
Standard 
Electrical 
System 
Mechanization 
Product, Process, and Tool Requirements 
3 Test Preparation and Evaluation 
3.1 Resources. 
3.1.1 Calibration. The test facilities and equipment 
shall be in good working order and shall have a 
valid calibration label. 
3.1.2 Alternatives. Alternative test facilities and 
equipment may also be used. However, all 
measuring 
variables 
as 
specified 
in 
this 
specification shall be determined correctly with 
respect to its physical definition. 
3.1.3 Facilities. Not applicable. 
3.1.4 Equipment. Not applicable. 
3.1.5 Test Sample. Not applicable. 
3.2 Preparation. Not applicable. 
3.3 Conditions. Not applicable. 
3.4 Instructions. Not applicable. 
3.5 Data. Not applicable. 
3.6 Safety. This standard may involve hazardous 
materials, 
operations, 
and 
equipment. 
This 
standard does not propose to address all the 
safety problems associated with its use. It is the 
responsibility of the user of this standard to 
establish appropriate safety and health practices 
and determine the applicability of regulatory 
limitations prior to use. 
3.7 Documentation. Samples of components or 
material released to this standard shall be tested 
for conformity with the requirements of this 
standard and approved by the responsible GM 
Department prior to the start of delivery of 
production level components or materials. 
Any change to the component or material, e.g., 
design, function, properties, manufacturing process 
and/or location of manufacture requires a new 
release of the product. It is the sole responsibility 
of the supplier to provide the customer, unsolicited, 
with documentation of any change or modification 
to the product/process, and to apply for a new 
release. 
If not otherwise agreed to, the entire verification 
test shall be repeated and documented by the 
supplier prior to start of delivery of the modified or 
changed product. In some cases a shorter test can 
be agreed to between the responsible GM 
Department and the supplier. 
3.7.1 Test Results. Not applicable. 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 2 页
GMW3176 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
Page 2 of 22 
May 2008
 
3.7.2 Deviations from this Standard. Deviations 
from the requirements of this standard shall have 
been agreed upon. Such requirements shall be 
specified on component drawings, test certificates, 
reports, etc. 
4 Requirements and Procedure 
4.1 Common Circuit Database. The Common 
Circuit database will consist of five items of 
information for, each circuit, i.e., circuit number, 
circuit color, circuit subsystem, abbreviated circuit 
description, and circuit description. The database 
will be accessible and searchable. Database 
queries will be presented in a tabular form. Users 
will be provided with a method for requesting new 
circuit numbers and commenting on existing 
circuits. The database will be managed according 
to 4.1.1 thru 4.1.6 requirements. 
4.1.1 Database Format. The database will be 
stored on a network server as a colon delimited 
text file. The database will be formatted for 
presentation as five columns of information. See 
Table 1. 
 
Table 1: Common Circuit Database Description 
No 
Color 
Subsystem 
Abbreviated 
Description 
Description 
1 
RD 
Power and Ground Distribution BAT 
Battery – Unfused 
2 
RD/BK Power and Ground Distribution BAT_FU_TYP_I 
Battery – Fused – Type I 
3 
VT/BK Power and Ground Distribution IGN_SWOT_RC 
Ignition Switch – Output – Run/Crank
 
 
4.1.2 Database Access. The database will be 
accessible within the Electrical Design Schematic 
Design Tool. Currently this is Integrated Vehicle 
Electrical Design (IVED). 
******* Access from Within GM Schematic 
Design Tool. The GMW Circuit List is available 
when using the GM Schematic Design Tool. 
Currently this is IVED Design Architect. 
******* Access from the GM Intranet. The GMW 
Circuit List is stored in Teamcenter Community 
(TcC) as part of the IVED configuration. 
4.1.3 Circuit Additions. Circuits can be added to 
the database by making requests to the IVED 
Configuration Owner. 
The requester will need to supply the function of 
the circuit and may request a preferred color and 
subsystem in accordance with rules contained 
herein. 
4.1.4 Circuit Corrections. Circuit corrections may 
need to be made to eliminate errors from the 
database. It is preferred 
to 
correct 
circuit 
descriptions without changing the function of the 
circuit. 
4.1.5 Circuit Obsolescence/Deletion. 
******* An obsolescence/deletion process will be 
implemented to eliminate unused or unnecessary 
circuits from the database. Circuits that are no 
longer used by a vehicle platform or are incorrectly 
numbered and/or described can be deemed 
obsolete. 
******* A method of communication will exist to 
inform users of when a circuit is considered 
obsolete and when it will be deleted. An alternative 
circuit number will be communicated to the user 
whenever an alternative is available. 
******* An obsolete circuit will be removed from the 
database and stored separately for four years. 
Should a vehicle platform need to use an obsolete 
circuit they can request that the circuit be added 
back into the database. The circuit will be added to 
the database when it conforms to the requirements 
of this manual. If after four years, the obsolete 
circuit has not been returned to the database, it will 
be deleted, removing all record of the circuit. 
4.1.6 Reuse of a Circuit. Obsolete circuits can be 
assigned as new circuits after four years of 
inactivity. The new circuit can be unrelated to the 
obsolete 
circuit 
in 
electrical 
function 
and 
description. 
4.2 Circuit Numbers. A unique number will be 
assigned to each GMW circuit according to 4.2.1 
thru 4.2.3 guidelines. 
4.2.1 Circuit Number Rules and Reserved 
Circuit Numbers. Circuit descriptions shall be 
numbered and reserved in accordance to the rules 
of Table 2. 
4.2.2 
Assigning 
New 
Circuit 
Numbers. 
Preferably, new circuit numbers will be added in 
consecutive order starting with the first available 
number. Exceptions will be made for reserved 
circuit numbers. 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 3 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3176
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
May 2008 
Page 3 of 22
 
4.2.3 Developmental Circuit Numbers. Circuit 
numbers 900 to 999 and 9000 to 9999 will not be 
assigned in the GMW Circuit List. These numbers 
are available for developmental use. 
4.3 Circuit Color. Circuit color will be represented 
in the database by an abbreviation. A stripe may 
be added to a primary circuit color when needed. 
The 10 base colors to be used are shown in 
Table 3. The determination and naming of the color 
codes is based on ISO 6722 and IEC 757. 
Deviating from the pregiven Reichsausschuss für 
Lieferbedingungen 
(RAL) 
color 
coding 
is 
permissible as long as correct determination to 
IEC 757 and avoiding of misinterpretation is 
guaranteed. An overview of the possible stripe 
combinations are shown in Appendix A. 
4.3.1 Basic Color. Color which covers the whole 
surface of a wire or the surface at its highest 
percentage. 
4.3.2 Strip color. Color given as two symmetrically 
opposite sided small stripes on the wire. The 
individual stripe shall be a minimum of 7% wide, 
but not more than 15% of the circumference of the 
wire. 
4.3.3 Nomenclature. For wires wearing additional 
strip colors, the basic color is separated from the 
strip color by using a slash character, e.g., BK/WH. 
In this case BK is the basic color and WH is the 
strip color. 
 
Table 3: Insulation Colors 
Color 
Color Abbreviation 
to IEC 757 
Color According 
to IEC 304 
Similar to SAE J1128 
Black 
BK 
RAL 9005 
BK 
PED C-1661 N2.25/ 
Brown 
BN 
RAL 8003 
TN 
PED C-1631 5YR 5.5/4.6 
Red 
RD 
RAL 3000 
RD 
PED C-1628 4.4 R 3.4/10.4 
Orange 
OG 
RAL 2003 
OG 
PED C-1629 8.75 R5.75/12.5 
Yellow 
YE 
RAL 1021 
YE 
PED C-1632 8Y 8.5/11.2 
Green 
GN 
RAL 6018 
L-GN 
PED C-1626 0.5 G 5.6/7.0 
Blue 
BU 
RAL 5015 
D-BU 
PED C-1623 4.6 PB 3.8/10.2 
White 
WH 
RAL 1013 
WH 
PED C-1660 5Y 9/1 
Gray 
GY 
RAL 7000 
GY 
PED C-1633 N 5.7/(10GY.0.2) 
Violet 
VT 
RAL 4005 
PU 
PED C-1624 4.4 P 3.9/6.7 
 
 
4.3.4 Color Circuit Rules. The color rules are 
shown in Table 2. 
******* Reserved Circuit Color. Certain colors are 
reserved for particular circuit descriptions per 
Table 2. The reserved colors shall only be used for 
those descriptions. Other colors are “assigned” to 
particular circuit descriptions but are not reserved. 
Assigned colors can be used for other circuit 
descriptions.
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 4 页
GMW3176 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
Page 4 of 22 
May 2008
 
 
 
 
New Color Rules 
Circuit Class 
Circuit No. Rule 
GMW3176 Color Rule 
Reserved 
Color? 
Un-Switched Power 
Battery-
Unfused 
1 
RD 
Y 
Battery-Type I 
Fuse 
2, xx02 
(reserved below 
3100 only) 
RD + Stripe 
(Not RD/BK) 
Y 
Battery-Type II 
Fuse 
xx42 
(reserved below 
3100 only) 
RD + Stripe 
(Not RD/BK) 
Y 
Battery-Type 
III Fuse 
xx40 
RD + Stripe 
(Not RD/BK) 
Y 
Switched Power 
Run/Crank 
(Ign 1) 
3, xx39 
VT/BK, VT/GN, VT/GY, VT/WH 
N 
Run (Ign 3) 
300, xx41 
(reserved below 
3100 only) 
VT/BN 
N 
Accessory 
4, xx43 
(reserved below 
3100 only) 
VT/YE 
N 
Powertrain 
(Main) Relay 
No Rule 
VT/BU 
N 
Retained 
Accessory 
Power (RAP) 
No Rule 
VT 
N 
Voltage 
Reference 
(Vref) 
(5 to 14V, or 
unspecified) 
No Rule 
Base + RD Stripe 
(not BK/RD, 
not OG/RD) 
Y 
Ground 
Dirty Ground 
xx50 
BK 
Y 
Clean Ground 
xx51 
BK/WH 
Y 
Ground 
Reference 
No Rule 
BK + Stripe 
(not BK/RD, BK/WH) 
BK/OG only for SIR 
Y 
Serial Data 
 
LS GMLAN 
No Rule 
GN 
N 
MS GMLAN+ 
No Rule 
YE 
N 
MS GMLAN- 
No Rule 
WH 
N 
HS GMLAN+ 
(Primary) 
No Rule 
BU 
N 
HS GMLAN- 
(Primary) 
No Rule 
WH 
N 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 5 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3176
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
May 2008 
Page 5 of 22
 
 
New Color Rules 
Circuit Class 
Circuit No. Rule 
GMW3176 Color Rule 
Reserved 
Color? 
HS GMLAN+ 
(Additional) 
No Rule 
BU + Stripe 
N 
HS GMLAN- 
(Additional) 
No Rule 
WH 
N 
Accessory/WU 
No Rule 
VT/YE 
N 
Comm/Enable 
No Rule 
WH/BU 
N 
LIN 
No Rule 
Green + Stripe 
N 
Other 
SIR 
No Rule 
OG + Stripe, or Base + OG Stripe, BK/OG for Ground 
Ref, (Not OG w/o Stripe, Not OG/BK, which are 
reserved for High Voltage) 
Y 
High Voltage 
(DC) 
No Rule 
OG for Power, 
OG/BK for Ground 
Y 
High Voltage 
(AC) 
No Rule 
120 VAC to follow U.S. NEC Standard: 
- RD= 120VAC Phase A 
- BK = 120 VAC Phase B 
- WH = 120 VAC Neutral 
- GN = 120 VAC Ground 
230 VAC to follow Europe Standard: 
- BN = 230 VAC Power 
- BU = 230 VAC Neutral 
- GN/YE = 230 VAC Ground 
N 
Mid Voltage 
No Rule 
No Rule 
N 
All Other 
Circuits 
No Rule 
Not any of the reserved color combinations 
N 
 
 
4.4 Circuit Subsystem. Each circuit will be 
assigned to a subsystem. This will enable circuits 
with related functions to be grouped together for 
purposes of information gathering. A subsystem 
table can be found in Appendix B. 
4.5 Circuit Description. A circuit description will 
exist for each circuit in the GMW Circuit List. The 
description will describe the device and/or function 
of the circuit in a consistent construct according to 
4.5.1 thru 4.6 requirements. 
4.5.1 General Text Constructs. General text 
constructs will be applied to each circuit description 
to maintain consistency and readability. First letter 
capitalization will be applied in all cases except 
when a system power mode is being described. 
System power modes will be all capital letters. 
Paragraphs 4.5 and 4.6 give details on how to 
construct specific circuit descriptions. Optional 
fields are enclosed in greater-than less-than 
symbols (i.e., <Optional Field Value>). 
4.5.2 Root Component Name. A root component 
name will be an identifier that associates the circuit 
with a specific vehicle feature or component. The 
root component name will identify groups of related 
circuits. To maintain consistent naming, the root 
component name should match those already 
existing in the common circuit database. A root 
component table can be found in Appendix C. 
******* Root Component Type. A root component 
name may require an identifier that specifies 
application. A root component type will precede the 
root component name in the description and will be 
used to specify the application of a root component 
(e.g., Exhaust Brake, etc.). To maintain consistent 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 6 页
GMW3176 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
Page 6 of 22 
May 2008
 
naming, the root component type should match 
those already existing in the common circuit 
database. Root component type can be found in 
the root component table in Appendix C. 
4.5.3 Device Name. A device name will be a single 
word that identifies the generic device (e.g., Fuse, 
Lamp, Relay, Switch, etc.). The device name can 
be determined according to circuit origin or 
destination, whichever is applicable. To maintain 
consistent naming, the device name should match 
those already existing in the common circuit 
database. A device name table can be found in 
Appendix D. 
******* Device Name Type. A generic device 
name may require an identifier that specifies 
function. A device name type will precede the 
device name in the description and will be used to 
identify the function of a device name (e.g., Limit 
Switch, Pressure Sensor, etc.). To maintain 
consistent naming, the device name type should 
match those already existing in the common circuit 
database. Device name type can be found in the 
device name table in Appendix D. 
******* Internal Component. A generic device 
may require an identifier that specifies an internal 
component and the function/state of the internal 
component. The internal component and internal 
component function/state will be appended to the 
device name. To maintain consistent naming, the 
internal component identifier should match those 
already existing in the common circuit database. 
Internal component can be found in the device 
name table in Appendix D. 
4.5.4 Device Description. A device description will 
be constructed by appending the root component, 
device name type, and device name together. The 
device description will be constructed as follows: 
Root 
Component<Device 
Name 
Type>Device 
Name<internal Component – Internal Component 
Function/State>, e.g., Stop Turn Lamp Relay - 
Contact – N.O. 
4.5.5 
Function 
Description. 
The 
function 
description will specify the purpose, state, or action 
of the circuit. The function description can be 
constructed as a signal followed by the function 
description or a location descriptor. 
The function description can be constructed as 
Function - Function Description when the circuit is 
uniquely and unambiguously described by the 
function description itself (e.g., Lamp Monitor Data 
Request, etc.). To maintain consistent naming, the 
function and function description should match 
those already existing in the common circuit 
database. A Function/Mode/State/Action table can 
be found in Appendix E. 
4.5.6 Location Descriptor. A location descriptor is 
an optional field that will identify an area of the 
vehicle (e.g., Driver, Left Front, etc.). The 
descriptors, Driver and Passenger, will be used to 
denote location of a function or device that impacts 
the interior realm of the vehicle. The other 
descriptors, Front, Rear, etc., will be used to 
denote the location of a function or device that 
impacts the exterior realm of the vehicle. To 
maintain consistent naming, the location descriptor 
should match those already existing in the 
common circuit database. A location/Identifier table 
can be found in the Product Data Management 
(PDM) system.  
4.5.7 Identifier. An identifier will be a number that 
uniquely 
identifies 
identical 
circuits, 
(e.g., 
Windshield Wiper Switch - Signal - 1, Windshield 
Wiper Switch - Signal - 2, etc.). To maintain 
consistent naming, the identifier should match 
those already existing in the common circuit 
database. A location/Identifier table can be found 
in the Product Data Management (PDM) system. 
4.5.8 Power Feed Circuit. A power feed circuit will 
be classified as one of two types: ignition power 
feed, battery power feed. 
******* Ignition Power Feed. An ignition power 
feed will describe a circuit that supplies a fused, 
ignition switched power feed. An ignition power 
feed circuit will have an ignition switch signal as a 
function description and a device description of 
Fused - Type I, ll, III. The redundancy of the device 
description makes the function description more 
meaningful to the GMW Circuit List user. An 
ignition power feed description will be constructed 
as 
follows: 
Function 
Description 
- 
Device 
Description (e.g., Accessory - Fused - Type Ill, 
etc.). All ignition power feed circuits will reside in 
the Power and Ground Distribution subsystem. 
******* Battery Power Feed. A battery power feed 
will describe a circuit that supplies a fused, 
unswitched power feed. A battery power feed will 
have a device description of Fused - Type I, II, or 
III and a root component of Battery. A battery 
power feed circuit wilt be constructed as follows: 
Root Component - Device Description (e.g., 
Battery - Fused - Type I, etc.). 
4.6 
Abbreviated 
Circuit 
Description. 
The 
abbreviated circuit description will be constructed 
by abbreviating the circuit description. To maintain 
consistent 
naming, 
the 
abbreviated 
circuit 
description should match those already existing in 
the common circuit database. An abbreviation 
table 
can 
be 
found 
in 
the 
Product 
Data 
Management (PDM) system. 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 7 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3176
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
May 2008 
Page 7 of 22
 
5 Provisions for Shipping 
Not applicable. 
6 Notes 
6.1 Glossary. Not applicable. 
6.2 Acronyms, Abbreviations, and Symbols. 
BEC 
 
Bus Electrical Center 
BTSI 
 
Brake Transmission Shift Interlock 
CAD 
 
Computer Aided Design 
EGR 
 
Exhaust Gas Recirculation 
ELC 
 
Electronic Level Control 
ESC 
 
Electronic Spark Control 
EVO 
 
Electronic Variable Orifice 
Gnd 
 
Ground 
IEC 
International Electrotechnical 
Commission 
Ign  
 
Ignition 
IUE  
 
IVED User Enhancements 
IVED 
 
Integrated Vehicle Electrical Design 
MSVA  
Magnetic Steering Variable Assist 
N.C. 
 
Normally Closed 
N.O. 
 
Normally Open 
Opt  
 
Optional 
PED 
 
Packard Electric Division 
PDM 
 
Product Data Management 
PWM 
 
Pulse Width Modulated 
RAL 
Reichsausschuss 
für 
Lieferbedingungen (National Board of 
Supply Conditions) 
RTD 
 
Real Time Damping 
TC  
 
Traction Control 
TcC 
 
Teamcenter Community 
TCC 
 
Torque Converter Clutch 
Telcomm Telecommunications 
7 Additional Paragraphs 
7.1 All parts or systems supplied to this standard 
must comply with the requirements of GMW3059, 
Restricted and Reportable Substances for 
Parts. 
7.2 Central Database. The circuit list is stored in 
tabular 
form 
for 
the 
support 
in 
automatic 
development systems. The extension of the list is 
obliged to a central helpdesk request system 
maintained 
by 
the 
owner 
responsible 
for 
standardizations. 
7.3 Format of the List. The list is centrally stored 
in IVED as a colon separated text file. 
7.4 Access to Database. The reading accessibility 
is given electronically by Online Web-Browser 
facilities. Copying and file distribution is centrally 
controlled and executed for the provision of several 
CAD applications, e.g., Design-Architect. 
8 Coding System 
This standard shall be referenced in other 
documents, drawings, etc., as follows: 
GMW3176 
9 Release and Revisions 
9.1 Release. This standard originated in November 
1998. It was first approved in December 1998 by 
the Electrical Group. It was first published in 
February 1999. Content that was added in the May 
2008 publication of GMW3176 is replacing GME 
8740. 
9.2 Revisions. 
Rev 
Approval 
Date 
Description (Organization) 
A 
FEB 1999 
Modify database access 
section ******* (R. A. Trickett) 
B 
MAY 2008 
Add GME 8740 content. 
(Global Technology 
Engineeering) 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 8 页
GMW3176 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
Page 8 of 22 
May 2008
 
Appendix A 
Table A1: Circuit Color and Stripe Combinations 
  
Stripe 
BK 
BN 
BU 
GN 
GY 
OG 
RD 
VT 
WH 
YE 
BARE 
Wire 
  
  
  
  
  
  
  
  
  
  
  
  
BK 
  
  
  
  
  
  
  
  
  
  
  
  
BN 
  
  
  
  
  
  
  
  
  
  
  
  
BU 
  
  
  
  
  
  
  
  
  
  
  
  
GN 
  
  
  
  
  
  
  
  
  
  
  
  
GY 
  
  
  
  
  
  
  
  
  
  
  
  
OG 
  
  
  
  
  
  
  
  
  
  
  
  
RD 
  
  
  
  
  
  
  
  
  
  
  
  
VT 
  
  
  
  
  
  
  
  
  
  
  
  
WH 
  
  
  
  
  
  
  
  
  
  
  
  
YE 
  
  
  
  
  
  
  
  
  
  
  
  
BARE 
  
  
  
  
  
  
  
  
  
  
  
  
 
  
= Reserved for Unfused Battery Circuits 
 
  
= Reserved for Fused Battery Circuits 
 
  
= Reserved for Voltage Reference Circuits 
 
  
= Reserved for Dirty Ground Circuits 
 
  
= Reserved for Clean Ground Circuits 
 
  
= Reserved for Ground Reference (Return) Circuits 
 
  
= Reserved for SIR Circuits 
 
  
= Reserved for High Voltage Circuits 
 
  
= Defined (Not Reserved) for Run/Crank (Ign 1) Circuits 
 
  
= Defined (Not Reserved) for Run (Ign 3) Circuits 
 
  
= Defined (Not Reserved) for Accessory Circuits 
 
  
= Defined (Not Reserved) for Powertrain (Main) Relay Circuits 
 
  
= Defined (Not Reserved) for Retained Accessory Power (RAP) Circuits 
 
  
= Not to be Used 
n/a 
= Not Applicable (not a possible wire type) 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 9 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3176
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
May 2008 
Page 9 of 22
 
Appendix B 
Table B1: Circuit Subsystem 
Subsystem 
Mechanization Subsystem 
Component 
Engine 
Engine Sensors/Controls 
Manifold Absolute Pressure Sensor 
Powertrain Control Module 
Mass Air Flow Sensor 
Idle Air Control Motor 
Engine Oil Pressure Sensor 
Engine Oil Level Sensor 
Air Filter Sensor 
Manifold/Intake Air Temp Sensor 
 
Emission Control 
A/C Pressure 
EGR 
Charcoal Canister Purge Solenoid 
Charcoal Canister Purge Vacuum Switch 
Oxygen Sensors 
Coolant Temperature Sensor 
 
Exhaust Treatment 
Air Pump/Solenoid 
 
Electric Heated Catalyst 
Power Switch 
Heat Element 
 
Engine Exhaust Braking 
 
 
Electronic Throttle Control 
Throttle Position Sensor 
Throttle Motor 
Throttle Actuator Control Module 
Cruise Control Operator Switch 
 
Mechanical Throttle Control 
 
 
Mechanical (Cruise Opt) 
Cruise Control Operator Switch 
Cruise Control Module 
 
Electrical (Cruise Opt) 
Cruise Control Operator Switch 
Cruise Control Module 
 
Ignition System Control 
ESC Knock Sensor 
Cam Speed Sensor 
Ignition Control Module 
Ignition Coil 
Ignition Timing 
Crankshaft Position Sensor 
Spark Plugs 
Crank A Sensor 
Crank B Sensor 
 
Fuel Control 
Fuel Injectors 
Vent Solenoid 
Fuel Tank Pressure Sensor 
 
Starter Control 
Starter Motor 
Starter Solenoid 
Clutch Interlock Switch 
 
Engine Block Heating 
Engine Block Heater 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,


### 第 10 页
GMW3176 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
Page 10 of 22 
May 2008
 
 
Subsystem 
Mechanization Subsystem 
Component 
Transmission 
Trans Shift Sense/Control 
Performance/Normal Switch 
Transmission Controller 
Trans Oil Temperature Sensor 
Trans Mode Switch (PRNDL) 
Trans Line pressure Force Motor 
Shift Solenoid 
TCC Solenoid 
TCC Release Switch 
Turbine Input Speed Sensor 
Pressure Switch Manifold 
 
Manual Transmission 
Interlock Clutch 
 
Brake Trans Shift Interlock 
BTSI Brake Switch (virtual) 
BTSI Solenoid 
 
Ignition Key/Shift Interlock 
 
 
Vehicle Speed Sensing 
Vehicle Speed Sensor 
Transfer Case 
All Wheel Drive 
Four Wheel Drive 
Differential Lock/Axle Controls 
 
Steering 
Power 
Power Steering Pressure Switch 
 
Steering Assist 
MSVA Solenoid 
Digital Steer Wheel Position Sensor 
Analog and Digital Steering WH. Position 
Sensor 
EVO Solenoid 
Electronic/Hydraulic Power Steering 
 
Tilt/Telescope Steer Column 
 
 
Power Tilt and Telescope 
Tilt and Telescope Module 
T and T Switch 
Tilt Motor 
Telescope Motor 
T and T Position Pots 
 
Memory Tilt and Telescope 
 
Suspension 
Ride and Handling Control 
RTD Module 
CV Solenoid 
Combo Sensor (Height/Acceleration) 
Yaw Rate Sensor (Virtual) 
 
Electronic Level Control 
ELC Compressor/Exhaust Solenoid 
Tires/Wheels/Trim 
Tire Pressure Monitor/Control 
Tire Inflation Operator Reset Switch 
Tire Inflation Sensor 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`


### 第 11 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3176
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
May 2008 
Page 11 of 22
 
 
Subsystem 
Mechanization Subsystem 
Component 
Brakes/Traction 
Power Brakes 
Brake Fluid Level Sensor 
Brake Lining Wear Indicators 
Brake Booster Vacuum Sensor 
Yaw Rate Sensor 
LAT/LONG Accelerometer 
Vacuum Pump/Motor 
 
Air Brakes 
 
 
Electro-Hydraulic Brakes 
 
 
Anti-Lock Brakes 
ABS Controller 
Wheel Speed Sensors 
ABS Motor 
 
Traction Control 
TC ON/OFF Switch 
 
Enhance (Retard Engine) 
 
 
Full TC (Brakes and Retard Engine) 
 
 
Park Brake 
Park Brake Switch 
 
Electronic Release 
Park Brake Motor 
 
Pneumatic Release 
 
Fuel Storage and 
Handling 
Fuel Storage 
Fuel Level Sensor 
Fuel Tank Pressure Sensor (Virtual) 
 
Fuel Handling 
Fuel Pump Relay (Virtual if BEC) 
Fuel Pump 
F/Pump Speed Control Resistor 
F/Pump Speed Control Relay (Virtual if BEC) 
HVAC 
Occupant Climate Control 
Heater and A/C Operator Controls 
Sun Load Sensor 
Outside Temperature Sensor 
Inside Breath Temperature Sensor 
A/C Temp Sensor (Low) 
A/C Temp Sensor 
Heater Temp Sensor 
PWM Blower Motor 
Blower Motor Resistor Pack 
Puller Fan Motor 
Heater/Defroster Motor 
Mode Motor 
Mix Motor 
Rear Blower Motor Switch 
Rear Blower Motor 
Engine Coolant Bypass 
 
Air Delivery 
 
 
Compressor Control 
A/C Clutch Assembly 
A/C Pressure Sensor (High) 
 
Rear Window Defog 
Rear Defogger Grid 
Rear Defog Relay (Virtual if BEC) 
Rear Defog Enunciator 
Rear Defog Switch 
 
Heated Windshield 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 12 页
GMW3176 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
Page 12 of 22 
May 2008
 
 
Subsystem 
Mechanization Subsystem 
Component 
Engine/Transmission 
Cooling 
 
Engine Cooling Fan 
Cooling Fan Relays (Virtual if BEC) 
Engine Coolant Level Sensor 
Coolant Temperature Sensor (Virtual) 
Electric Coolant Pump 
Radiator Shutters 
Seats 
Power Seats 
Seat Switch 
Seat Motors 
Recliner Motor 
Lumbar Switch 
Lumbar Motor 
Head Restraint Switch 
Head Restraint Motor 
Thigh Compressor 
 
Memory Seat 
Memory Seat Motor 
Memory Recliner Motor 
Memory Head Restraint Motor 
Memory Lumbar Motor 
Memory Switch 
Memory Seat Module 
 
Heated Seats 
Heated Seat Switch 
Heated Seat Module 
Heated Seat Element 
 
Ventilated Seats 
 
Interior Lighting 
Interior Lamps 
Interior Light On/Off/Auto Switch 
Ignition Key Cylinder Lamp 
Door Courtesy Lamp 
Hush Panel Lamp 
Overhead Courtesy/Reading Lamps 
General Office Work Lamp/Switch 
Vanity Mirror 
Rail Lamp/Switch 
Ashtray Lamp 
Console PRNDL Lamp 
Cigar Lighter Illumination 
 
Compartment Lamps 
Trunk Lamp Switch and Lamp 
Underhood Lamp Switch and Lamp 
Glove Box Switch 
Console Lamp 
Console Switch 
Cargo Lamp 
 
Dimming Control 
Dimmer 
Device Floodlights 
Device Backlights 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 13 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3176
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
May 2008 
Page 13 of 22
 
 
Subsystem 
Mechanization Subsystem 
Component 
Occupant Protection 
Front SIR 
Sensing Diagnostic Module 
Driver Airbag 
Passenger Airbag 
Crash Detection Sensor/Module 
 
Side SIR 
Crash Detection Sensor/Module 
Side Airbag 
 
Seat Belts 
Seat Belt Reminder Telltale 
Seat Belt on Detection Switch 
Seat Belt Pretensioner 
Seat Belt Anchor Height Motor/Switch 
Entry Control 
Power Door Locks 
Door Lock Motor 
Door Lock/Unlock Actuators 
Lock/Unlock Switches 
Door Lock Switch 
Key Lock Operator Switch 
Key Cylinder Disarm/Unlock Switch 
Door Jamb Switches 
Door Ajar Sensors 
Trunk Ajar Sensors 
Child Safety Locks 
 
Remote Function Actuation 
RFA Module 
Key FOB 
Universal Garage Door Opener 
 
Power Sliding Door 
 
 
Release Systems 
Trunk/Fuel Release Switch 
 
Fuel Door Release 
Fuel Door Release Solenoid 
 
Trunk Release 
Trunk Release Solenoid 
 
Hood Release 
Hood Release Solenoid 
 
Valet Lockout 
Valet Lockout Switch 
 
Pull Down Systems 
Trunk Pull Down Actuators 
Power Windows 
Power Windows 
Window Motor  
Up/Down/XUp/XDown Switch 
Obstruction Sensor 
Passenger Window Lockout 
 
Window Dim/Sunshade 
 
Movable Roof 
Power Sunroof 
Sunroof Module 
Sunroof Motor 
Sunroof Sensor 
Slide Switch 
Vent Switch 
 
Convertible Top 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 14 页
GMW3176 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
Page 14 of 22 
May 2008
 
 
Subsystem 
Mechanization Subsystem 
Component 
Exterior Lighting 
Headlamps (DRL) 
Headlamp Switch 
HDLP HI/LO/FTP/Dimmer Operator 
Switch 
HID Ballast 
LO Beam Lamps 
HI Beam Lamps 
Daytime Running Lamps 
Headlamp Doors 
Headlamp Leveling 
 
Auto Light Control 
Twilight Photocell 
 
Marker Lamps 
Front Sidemarkers 
Rear Sidemarkers 
Position Lamps 
License Lamps 
Front Park Lamps 
Tail Lamps 
 
Signal Lamps (DRL) 
Rear Turn Lamp 
Turn/Hazard Module 
Turn Switch 
Hazard Switch 
Turn Lamps 
Cornering Lamps 
Repeater Lamps 
Backup Lamps 
 
Stop Lamps 
Stop Lamps 
LED CHMSL 
 
Fog Lamps (DRL) 
Front Fog Lamps 
Rear Fog Lamps 
Fog Lamp Switch 
 
Trailer Lighting 
 
Mirrors 
OSRV Power Mirror 
Vertical Motor 
Horizontal Motor 
 
Heated Mirror 
Temperature Door Module 
Heated Mirror Element 
 
Memory Mirror 
Vertical Position Sensor 
Horizontal Position Sensor 
 
ISRV Power Mirror 
Rear View Mirror w/EC and Compass 
EC Mirror Element 
 
Mirror Dim 
 
Washer/Wiper 
Front Washer/Wiper 
Washer/Wiper Operator Switch 
Washer/Wiper Motor 
Washer Fluid Level Sensor 
Washer Pump 
Rain Sensor 
 
Rear Washer/Wiper 
 
 
Auxiliary Washer/Wiper 
 
 
Headlamp Washer/Wiper 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 15 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3176
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
May 2008 
Page 15 of 22
 
 
Subsystem 
Mechanization Subsystem 
Component 
Occupant 
Information 
Electronic PRNDL Display 
PRNDL Switch (Virtual) 
 
Instrument Cluster 
Cluster (Domestic/Export) 
 
Instrument Cluster – Analog 
 
 
Instrument Cluster - Digital 
 
 
Reconfigurable Cluster/Telltales 
 
 
 
Catalytic Overtemp Sensor 
Catalytic Converter Amplifier 
Reset Switches 
 
Audible Warnings 
 
 
Driver Information Center 
Driver Information Switches 
 
Trip Computer 
 
 
Compass 
 
 
Outside Air Temperature 
 
 
Rear Object Detection 
 
 
Side Object Detection 
 
 
Heads Up Display 
 
 
Enhanced Night Vision 
 
 
Navigation System 
 
Entertainment 
Radio 
Radio Control Head/Chassis 
Amplifier 
Remote Playback Devices 
Antenna Module 
Antenna 
Speakers 
Theft Deterrent 
Content Theft Deterrent 
Tamper Switches 
Theft Sensors 
 
Vehicle Theft 
Deterrent 
 
Coded Key 
Passlock 
 
 
 
Immobilizer 
Telephone/Telcomm 
Cellular 
Phone Module 
Microphone 
Handset 
RF Booster 
Antenna 
 
Mobile Communication Service 
 
 
Mobile Office 
 
Horn 
Horn 
Horn 
Horn Operator Switch 
Horn Relay (Virtual if BEC) 
 
Backup Alarm 
 
Charge/Energy Store Charging 
Battery 
Generator 
Battery Thermistor 
 
Charging Accommodation 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 16 页
GMW3176 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
Page 16 of 22 
May 2008
 
 
Subsystem 
Mechanization Subsystem 
Component 
Power and Ground 
Distribution 
Power Distribution 
Fuse Centers 
 
Battery Rundown Protection 
 
 
Inadvertent Power 
 
 
Electrical Circuit Protection 
BEC (Integrating Component) 
 
Auxiliary Battery 
 
 
Ground Distribution 
Vehicle Ground Locations 
 
Lighter/Auxiliary Outlet 
 
Provisions 
(Aftermarket Add-Ons) 
Upfitter Provisions 
 
 
Trailer Lighting 
 
Serial Data Links 
Serial Data Link 
Data Link Connector 
E and C Bus 
Class 2 Bus 
Integrating 
Component 
Steering Wheel Controls 
Operator Switches 
 
Integrating Modules 
Body Control Module 
Dash Integrating Module 
I/P Integrating Module 
Rear Integrating Module 
Driver Door Module 
Passenger Door Module 
Driver Door Switch Module 
 
BEC 
Fuses 
Circuit Breakers 
Relays 
Switches 
 
Brake Pedal Switch 
Extended Travel Brake Switch 
TCC/Cruise Brake Switch 
Stop Lamp Brake Switch 
BTSI 
 
Clutch Switch 
 
Integrating Function 
Power Moding 
Ignition Switch 
Key-In Ignition Switch 
Retained Accessory Power 
Relay (Virtual if BEC) 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 17 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3176
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
May 2008 
Page 17 of 22
 
Appendix C 
Table C1: Root Component 
Root Component 
Root Component Type 
AAS 
 
ABS 
 
Air Conditioning 
 
Air Injection Reaction 
 
Blower 
 
Brake 
Exhaust 
 
Park 
 
Pedal 
Camshaft 
Phaser 
Canister 
Purge 
 
Vent 
Carburetor 
 
Catalytic Converter 
 
Charge Port 
Paddle 
 
Valve 
Clutch 
Pedal 
Coolant 
Low 
 
 
Crankshaft 
 
 
 
Delay Accessory Bus 
 
Dimmer 
 
Drain Wire 
 
Electronic Spark Timing 
 
Electronic Throttle 
Pedal 
Exhaust Gas Recirculation 
 
Exhaust Restrictor 
 
Fuel Gauge 
 
Fuel Injector 
 
Fuel Tank 
 
Generator 
Regulator 
Glow Plug 
 
 
 
Ignition 
Key/Shifter Interlock 
 
 
Mirrors 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 18 页
GMW3176 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
Page 18 of 22 
May 2008
 
 
Root Component 
Root Component Type 
Module/Unit 
Accessory Power 
 
Central Control 
 
Cruise Control 
 
Electric Heated Catalyst 
 
Electronic Control Unit 
 
Engine Control 
 
Governor 
 
Sunroof Control 
Perimeter Lighting 
 
Power Door 
Power 
 
Slide 
Retained Accessory Power 
 
Retarder 
 
Seat 
Heated 
 
Memory 
 
Power 
 
 
Sunroof 
 
Top 
Convertible Top 
 
Hard Top 
Traction Control 
 
Trailer 
 
Transmission 
 
Vehicle 
 
Washer 
Headlamp 
 
Rear Window 
 
Windshield 
Wheel 
 
Wiper 
Rear Window 
 
Windshield 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 19 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3176
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
May 2008 
Page 19 of 22
 
Appendix D 
Table D1: Device Name 
Device Name 
Device Name Type 
Internal Component 
Accelerometer 
 
 
Battery 
Pack 
 
Fan 
Coolant 
 
Fuse 
Type I 
 
 
Type II 
 
 
Type III 
 
Headlamp 
 
 
Indicator 
 
 
Lamp 
Backup 
 
Modulator 
 
 
Motor 
Apply 
 
 
Fan 
 
 
Pump – Booster 
 
 
Stop 
 
 
Valve 
 
Regulator 
Exhaust 
 
Relay 
Cut-Off 
Coil – N.O. 
 
Enable 
Coil – N.C. 
 
Fan 
Contact 
 
Motor Pump 
 
 
Solenoid 
 
 
Switch 
 
 
Valve – Main 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 20 页
GMW3176 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
Page 20 of 22 
May 2008
 
 
Device Name 
Device Name Type 
Internal Component 
Sensor 
Coolant 
 
 
Inertial – Combined Vehicle 
 
 
Level – Fluid 
 
 
Lining Wear 
 
 
Mass Air Flow 
 
 
Position 
 
 
Position – Valve 
 
 
Pressure – Barometric 
 
 
Pressure – Differential 
 
 
Pressure – Manifold Absolute 
 
 
Pressure – Manifold Differential 
 
 
Pump Motor Run 
 
 
Rate – Yaw 
 
 
Run 
 
 
Speed 
 
 
Strut 
 
 
Temperature 
 
 
Temperature Induction Air 
 
 
Temperature – Manifold Absolute 
 
Solenoid 
Bypass 
 
 
Control – Cold Advance 
 
 
Ether 
 
 
Fuel 
 
 
Pulse Width Modulated 
 
 
Shutoff 
 
 
Valve 
 
 
Valve – Inlet 
 
 
Valve – Isolation 
 
 
Valve – Main 
 
 
Valve – Outlet 
 
 
Valve – Pressure 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 21 页
GM WORLDWIDE ENGINEERING STANDARDS 
GMW3176
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
May 2008 
Page 21 of 22
 
 
Device Name 
Device Name Type 
Internal Component 
Switch 
Flow – Booster Fluid 
 
 
Limit 
 
 
Pressure – Booster 
 
 
Pressure – High 
 
 
Reset – Valve 
 
 
Select – Shift 
 
 
Travel 
 
 
Vacuum – Solenoid Valve 
 
Valve 
Control – Idle Air 
 
 
Modulator 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---


### 第 22 页
GMW3176 
GM WORLDWIDE ENGINEERING STANDARDS
 
© Copyright 2008 General Motors Corporation All Rights Reserved 
Page 22 of 22 
May 2008
 
Appendix E 
Table E1: Function/Mode/State/Action 
Function/Mode 
State/Action 
Command 
Achieved 
Dimming 
Active 
Feed 
Ajar 
Feedback 
Alarm 
Fused 
Apply 
Ground 
Auto Apply 
Ignition 3 
Closed 
Ignition 1 
Connected 
Off/Run 
Data 
Output 
Data Request 
Reference Voltage 
Diagnostic 
Regenerative Braking 
Disable 
Return 
Drive Unit 
Run 
Enable 
Run/Heat/Crank 
Enable 
Signal 
Exhaust 
Unfused 
Extended Travel 
 
Failure 
 
Hold 
 
Jamb 
 
K-Line 
 
L-Line 
 
Lat/Long Rate 
 
Lock 
 
Low 
 
Off 
 
On 
 
Open 
 
Panel Vents 
 
Release 
 
Request 
 
Stop 
 
Torque Delivered 
 
Torque Desired 
 
Turn 
 
Turn/Stop 
 
Unlock 
 
Wake Up 
 
Copyright GM Worldwide 
Provided by IHS under license with GMW 
Licensee=Delphi Ann Arbor & Henretta/********** 
Not for Resale, 07/18/2008 09:13:37 MDT
No reproduction or networking permitted without license from IHS
--`,```,,,,`,````,``,,`,,,`,,``-`-`,,`,,`,`,,`---

