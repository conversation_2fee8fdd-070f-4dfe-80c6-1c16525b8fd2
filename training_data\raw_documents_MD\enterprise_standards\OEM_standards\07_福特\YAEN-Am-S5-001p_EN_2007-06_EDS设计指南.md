# YAEN-Am-S5-001p_EN_2007-06_EDS设计指南.pdf

## 文档信息
- 标题：Article 2:
- 作者：kzielske
- 页数：171

## 文档内容
### 第 1 页


### 第 2 页
Yazaki EDS Design Guideline Rev 3 
 
1
Change Log (Rev 3) 
 
Appendix 
Added Appendix Section to include reference table, design highlight, term definition.. 
Section 2.16 
Added section for 22 gauge wire. 
Section 2.2 
Added new section on digital cable. 
Section 2.5 
Added automated band clamp section 
Section 2.6.3 
Added section on heat transfer and reflective properties of reflective coverings  
Section 2.6.4 
Added convolute tubing section 
Section 3.8.2 
Added guidelines for avoiding taping back terminals for optional content and keeping the 
optional wires un-stripped. 
Section 4 
Updated reference number on picture, deleted unused general reference number, added 
footnote for design review checklist items. 
Table of Content 
Updated table of Content 


### 第 3 页
Yazaki EDS Design Guideline Rev 3 
 
2
TABLE OF CONTENTS 
 
1 
INTRODUCTION TO THE WIRE HARNESS .........................................................................................................8 
1.1 
DEFINITION OF THE ELECTRICAL DISTRIBUTION SYSTEM AND WIRE HARNESS.............................................. 8 
1.2 
WIRE HARNESS ANATOMY.........................................................................................................................8 
1.3 
VEHICLE ANATOMY ................................................................................................................................... 9 
1.3.1 
Major Body-in-White Components ..................................................................................................... 9 
1.3.2 
A Vehicle’s Electrical Architecture.................................................................................................... 11 
1.3.3 
Subsystem Architecture ................................................................................................................... 11 
1.3.4 
Harnesses Families.......................................................................................................................... 11 
1.4 
TRENDS IN EDS...................................................................................................................................... 13 
1.4.1 
Key Trend Drivers ............................................................................................................................ 13 
1.4.2 
Technology Trends .......................................................................................................................... 15 
2 
WIRE HARNESS COMPONENTS...........................................................................................................................16 
2.1 
WIRE AND CABLE.................................................................................................................................... 16 
2.1.1 
Wire Anatomy................................................................................................................................... 16 
2.1.2 
Wire Terminology ............................................................................................................................. 16 
2.1.3 
Wire Selection .................................................................................................................................. 17 
2.1.3.1 
Temperature Rating ............................................................................................................................... 17 
2.1.3.2 
Wire Size ............................................................................................................................................... 19 
2.1.3.3 
Conductor Size....................................................................................................................................... 19 
2.1.3.4 
Voltage Drop .......................................................................................................................................... 19 
2.1.3.5 
Flexibility ................................................................................................................................................ 20 
2.1.3.6 
Abrasion ................................................................................................................................................. 20 
2.1.3.7 
Mechanical Strength............................................................................................................................... 21 
2.1.3.8 
Column Strength .................................................................................................................................... 21 
2.1.3.9 
Fluid Resistance .................................................................................................................................... 22 
2.1.3.10 
Special Applications ............................................................................................................................... 22 
2.1.4 
Known Wire Issues........................................................................................................................... 22 
2.1.4.1 
Wicking................................................................................................................................................... 23 
2.1.4.2 
Overheating............................................................................................................................................ 23 
******* 
Combined Heat Generation.................................................................................................................... 23 
2.1.5 
Wire Length...................................................................................................................................... 23 
******* 
Minimum and Maximum Length ............................................................................................................. 23 
2.1.6 
22 Gauge Wire ................................................................................................................................. 23 
2.2 
DIGITAL CABLE........................................................................................................................................ 24 
2.2.1 
Introduction....................................................................................................................................... 24 
2.2.2 
Digital Cable Types .......................................................................................................................... 25 
2.2.3 
Impedance Matching........................................................................................................................ 26 
2.2.4 
Shield Termination Method .............................................................................................................. 27 
2.2.5 
Twisted Pair wire selection and guidelines ...................................................................................... 27 
2.2.5.1 
Application.............................................................................................................................................. 28 
******* 
Operating Frequency.............................................................................................................................. 28 
******* 
Signal Level............................................................................................................................................ 28 
******* 
Cable Length.......................................................................................................................................... 28 
******* 
Operating Environment .......................................................................................................................... 29 
2.2.6 
Coax Cable Design Guideline.......................................................................................................... 29 
2.2.6.1 
Impedance Match................................................................................................................................... 29 
2.2.6.2 
Shielding Requirement ........................................................................................................................... 29 
2.2.6.3 
Attenuation ............................................................................................................................................. 30 
2.2.6.4 
Routing................................................................................................................................................... 30 
2.2.6.5 
Operating Frequency.............................................................................................................................. 30 
2.2.6.6 
Coax Cable Rating ................................................................................................................................. 30 
2.3 
CONNECTORS AND TERMINALS................................................................................................................31 
2.3.1 
Anatomy of a Connector .................................................................................................................. 31 
2.3.2 
Connector Types.............................................................................................................................. 32 
******* 
Unsealed Connectors............................................................................................................................. 32 
******* 
Sealed Connectors................................................................................................................................. 32 


### 第 4 页
Yazaki EDS Design Guideline Rev 3 
 
3
2.3.3 
Connector Applications .................................................................................................................... 33 
2.3.3.1 
Inline Connectors ................................................................................................................................... 33 
2.3.3.2 
Direct Connect (Device Connectors)...................................................................................................... 33 
2.3.4 
Connector Selection......................................................................................................................... 33 
2.3.4.1 
General Selection Factors...................................................................................................................... 33 
2.3.4.2 
Environments: Wet vs. Dry..................................................................................................................... 35 
2.3.4.3 
Pin-out (circuit location) Considerations ................................................................................................. 36 
2.3.5 
Connector System Failures.............................................................................................................. 37 
2.3.5.1 
Not Connected – L.N.A. (loose not attached)......................................................................................... 37 
******* 
Loose Connection .................................................................................................................................. 38 
******* 
Terminal Push Out (TPO)....................................................................................................................... 38 
2.3.6 
Connector Selection Considerations ............................................................................................... 40 
2.3.6.1 
Design Considerations for Sealed Connectors....................................................................................... 40 
2.3.7 
Terms and Definitions ...................................................................................................................... 41 
2.3.8 
Terminals.......................................................................................................................................... 42 
******* 
Terminal Anatomy .................................................................................................................................. 42 
******* 
Materials................................................................................................................................................. 42 
******* 
Terminal Types....................................................................................................................................... 43 
******* 
Terminal Selection.................................................................................................................................. 44 
******* 
Terminal Failures.................................................................................................................................... 44 
******* 
Special Considerations........................................................................................................................... 46 
******* 
Terms and Definitions ............................................................................................................................ 46 
2.3.9 
Connector Materials ......................................................................................................................... 47 
******* 
Other Materials....................................................................................................................................... 48 
******* 
Additives................................................................................................................................................. 48 
2.4 
CLIPS..................................................................................................................................................... 48 
2.4.1 
Clip Anatomy.................................................................................................................................... 49 
2.4.2 
Materials...........................................................................................................................................49 
2.4.3 
General Performance Requirements ...............................................................................................49 
2.4.4 
Attachment Method to Harness........................................................................................................ 50 
******* 
Tape-on Clips......................................................................................................................................... 50 
******* 
Tie Strap Clips........................................................................................................................................ 51 
******* 
Double Tie Straps................................................................................................................................... 51 
******* 
Convolute Clips ...................................................................................................................................... 52 
******* 
Connector Clips...................................................................................................................................... 53 
******* 
Hook and Loop (“Velcro”) ....................................................................................................................... 53 
2.4.5 
Mounting Methods to Vehicle........................................................................................................... 54 
******* 
Hole Mounts ........................................................................................................................................... 54 
******* 
Stud Mounts ........................................................................................................................................... 55 
******* 
Screw/Bolt Mount Clips .......................................................................................................................... 55 
******* 
Edge Mounts (Edge biters)..................................................................................................................... 55 
2.4.6 
Selection Preference........................................................................................................................ 56 
2.5 
AUTOMATED BAND CLAMP.......................................................................................................................58 
2.5.1 
Materials and Components .............................................................................................................. 58 
2.5.2 
Application........................................................................................................................................ 59 
2.6 
PROTECTIVE COVERINGS ........................................................................................................................62 
2.6.1 
Tape ................................................................................................................................................. 62 
******* 
Materials................................................................................................................................................. 62 
******* 
Tape types.............................................................................................................................................. 62 
******* 
Tape selection........................................................................................................................................ 63 
******* 
Performance Standard ........................................................................................................................... 63 
2.6.2 
Sleeve Tubing (braided, woven, knitted).......................................................................................... 63 
2.6.2.1 
Sleeve Tubing Anatomy ......................................................................................................................... 63 
2.6.2.2 
Materials................................................................................................................................................. 64 
2.6.2.3 
Coatings/Covering.................................................................................................................................. 64 
2.6.2.4 
Sleeve Selection..................................................................................................................................... 65 
2.6.3 
Heat transfer and reflective properties of reflective coverings.........................................................65 
2.6.4 
Convolute Tubing ............................................................................................................................. 67 
2.6.4.1 
Convolute Tubing Anatomy .................................................................................................................... 67 
2.6.4.2 
Materials................................................................................................................................................. 68 
2.6.4.3 
Convolute Types .................................................................................................................................... 68 
2.6.4.4 
Performance Standard ........................................................................................................................... 69 
2.6.4.5 
Special Notes ......................................................................................................................................... 69 


### 第 5 页
Yazaki EDS Design Guideline Rev 3 
 
4
2.6.5 
Scroll Tubing and Troughs............................................................................................................... 69 
2.6.5.1 
Trough and Scroll Anatomy.................................................................................................................... 70 
2.6.5.2 
Materials................................................................................................................................................. 70 
******* 
Design Considerations ........................................................................................................................... 71 
******* 
Manufacturing Methods.......................................................................................................................... 71 
2.6.6 
Heat Shrink Tubing .......................................................................................................................... 72 
2.6.6.1 
Heat Shrink Tubing Anatomy.................................................................................................................. 72 
2.6.6.2 
Materials................................................................................................................................................. 72 
2.6.6.3 
Types of Heat Shrink Tubing.................................................................................................................. 73 
2.7 
GROMMETS ............................................................................................................................................ 73 
******* 
Grommet Anatomy ................................................................................................................................. 74 
******* 
Materials................................................................................................................................................. 74 
2.7.1.3 
Typical Applications................................................................................................................................ 74 
2.7.1.4 
Types of Grommets................................................................................................................................ 75 
2.7.1.5 
Design Considerations ........................................................................................................................... 75 
2.8 
POWER NETWORK CENTERS ...................................................................................................................78 
2.8.1 
Power Network Center Anatomy...................................................................................................... 78 
2.9 
COMPONENT MATERIAL ISSUES...............................................................................................................78 
2.9.1 
Material Incompatibility..................................................................................................................... 78 
2.9.2 
Terminal Continuity .......................................................................................................................... 79 
3 
ROUTING AND RETENTION DESIGN .................................................................................................................80 
3.1 
DEVELOPING A ROUTING PLAN ................................................................................................................80 
3.2 
DIMENSIONING & TOLERANCE..................................................................................................................84 
3.3 
SHEET METAL PROVISIONS .....................................................................................................................84 
3.4 
SPECIAL CONSIDERATIONS......................................................................................................................85 
3.5 
ATTACHMENT METHODS..........................................................................................................................86 
3.5.1 
Methods of Attachment .................................................................................................................... 86 
3.5.2 
Design Considerations..................................................................................................................... 86 
3.6 
CONNECTOR LOCATIONS.........................................................................................................................88 
3.7 
CLEARANCES.......................................................................................................................................... 88 
3.7.1 
General Clearance Guidelines......................................................................................................... 88 
3.8 
OTHER ROUTING CONSIDERATIONS .........................................................................................................92 
3.8.1 
Partitioning ....................................................................................................................................... 92 
3.8.2 
Option Content ................................................................................................................................. 92 
3.8.3 
Length, Weight, and Complexity ...................................................................................................... 93 
3.8.4 
Accessibility of Compartments......................................................................................................... 93 
3.8.5 
Installation Process .......................................................................................................................... 94 
3.8.6 
Modular assembly (I/P lines, Engine line, off-line doors).................................................................94 
3.8.7 
Pre-test Situations............................................................................................................................ 94 
3.8.8 
Environmental Conditions ................................................................................................................ 94 
3.9 
ENVIRONMENTAL CONSIDERATIONS.........................................................................................................94 
3.9.1 
Exposure to Fluids ........................................................................................................................... 95 
3.9.2 
Vibration ...........................................................................................................................................96 
3.9.3 
Special Considerations .................................................................................................................... 96 
3.10 
NOISE AND VIBRATION HARSHNESS (NVH) ..............................................................................................96 
3.10.1 
Noise (squeaks and rattles) ......................................................................................................... 96 
3.11 
DESIGN FOR MANUFACTURING, ASSEMBLY, AND SERVICEABILITY..............................................................97 
3.11.1 
Design for Manufacturing (DFM) .................................................................................................97 
3.11.2 
Design for Assembly (DFA) ......................................................................................................... 97 
3.12 
DESIGN FOR SERVICEABILITY ................................................................................................................100 
3.12.1 
Fuses and Fusible Links ............................................................................................................100 
3.12.2 
Ground Points............................................................................................................................100 
3.12.3 
Service Connectors....................................................................................................................100 
3.12.4 
Harness Labels..........................................................................................................................100 
3.12.5 
Relays ........................................................................................................................................101 
3.12.6 
Dealer Installed items ................................................................................................................101 
3.12.7 
Non-wiring service items............................................................................................................101 
3.12.8 
Special Considerations ..............................................................................................................102 
3.13 
TERMS AND DEFINITIONS.......................................................................................................................102 


### 第 6 页
Yazaki EDS Design Guideline Rev 3 
 
5
4 
ARCHITECTURE.....................................................................................................................................................104 
4.1 
ARCHITECTURE EXAMPLES....................................................................................................................104 
4.1.1 
Distributed ......................................................................................................................................104 
4.1.2 
Centralized .....................................................................................................................................104 
4.2 
ARCHITECTURE PROCESS .....................................................................................................................105 
4.2.1 
Vehicle Product Direction Letter.....................................................................................................105 
4.2.2 
Device Transmittals........................................................................................................................105 
4.2.3 
Device Locations............................................................................................................................105 
4.2.4 
Sub-System Schematics................................................................................................................106 
4.2.5 
Option Rates, Take-Rates..............................................................................................................106 
4.2.6 
Sub-System Topology....................................................................................................................106 
4.2.7 
Optimization leading to Proposals and Recommendations ...........................................................108 
4.2.8 
Vehicle Component Layout Consideration.....................................................................................109 
4.2.8.1 
Types of Communication Lines ............................................................................................................ 109 
4.2.8.2 
Modular Assembly................................................................................................................................ 110 
4.2.8.3 
Serviceability ........................................................................................................................................ 110 
4.2.8.4 
Special Considerations......................................................................................................................... 110 
******* 
Best Practices for Special Areas .......................................................................................................... 111 
4.2.9 
Harness Complexity .......................................................................................................................111 
******* 
Purchase in Assembly (PIA) Concept .................................................................................................. 112 
******* 
Harness Family Reduction ................................................................................................................... 112 
******* 
Bill of Material (BOM) Component Commonization.............................................................................. 113 
******* 
Design Complexity ............................................................................................................................... 113 
4.2.10 
Power Distribution......................................................................................................................114 
******** 
Power Distribution Tree........................................................................................................................ 114 
4.2.10.2 
Device Classification ............................................................................................................................ 115 
4.2.10.3 
Types of Loads..................................................................................................................................... 115 
4.2.11 
Current Rating & De-rating ........................................................................................................116 
4.2.12 
Over-Current Protection Devices...............................................................................................117 
******** 
Fuses ................................................................................................................................................... 117 
******** 
Fuse Sizing/Rating ............................................................................................................................... 118 
******** 
Circuit Breakers.................................................................................................................................... 118 
******** 
Fusible Link Wire.................................................................................................................................. 118 
******** 
Special Considerations with Fusing...................................................................................................... 119 
4.2.12.6 
Special Considerations for Fuse Link Wire........................................................................................... 119 
4.2.12.7 
Legal Requirements ............................................................................................................................. 119 
4.2.13 
Load and Signal Switching Devices...........................................................................................120 
4.2.13.1 
Mechanical Relays ............................................................................................................................... 120 
4.2.13.2 
Solid-State Relay.................................................................................................................................. 121 
4.2.14 
Grounding ..................................................................................................................................121 
******** 
Methods ............................................................................................................................................... 121 
******** 
Ground Paths ....................................................................................................................................... 121 
4.2.14.3 
Ground Classifications ......................................................................................................................... 121 
4.2.14.4 
Connections ......................................................................................................................................... 122 
4.2.15 
Ground Eyelets ..........................................................................................................................123 
4.2.16 
Special Considerations .............................................................................................................124 
4.2.17 
Attachment Notes ......................................................................................................................125 
4.2.18 
Ground Loops ............................................................................................................................125 
4.2.19 
Battery .......................................................................................................................................126 
4.2.20 
Lighting Grounds........................................................................................................................126 
4.2.21 
Panel..........................................................................................................................................126 
4.2.22 
Doors and Seats �....................................................................................................................126 
4.2.23 
Power Train................................................................................................................................126 
4.2.24 
Terms and Definitions................................................................................................................127 
4.3 
WIRE JOINING METHODS.......................................................................................................................129 
4.3.1 
Joint Connectors ............................................................................................................................129 
4.3.2 
Bussing systems in Power Distribution Boxes and Junction Blocks..............................................129 
4.3.3 
Wire Splicing (wire to wire).............................................................................................................129 
******* 
Wire Splice Methods: ........................................................................................................................... 129 
4.3.4 
Splice Orientation...........................................................................................................................130 
******* 
Center-strip Type.................................................................................................................................. 130 


### 第 7 页
Yazaki EDS Design Guideline Rev 3 
 
6
******* 
Face-to-face / overlap Type.................................................................................................................. 130 
******* 
End / Back End / Stump Type .............................................................................................................. 130 
4.3.5 
Splice Coverings ............................................................................................................................131 
4.3.6 
Designated Wet and Dry Areas......................................................................................................131 
4.3.7 
Special Considerations .................................................................................................................132 
4.3.8 
Terms and Definitions ....................................................................................................................133 
4.4 
ELECTROMAGNETIC COMPATIBILITY.......................................................................................................133 
4.4.1 
Conducted Mode Propagation .......................................................................................................134 
4.4.2 
Radiated Emissions Propagation...................................................................................................134 
4.4.3 
Conducted and Radiated Propagation...........................................................................................134 
4.4.4 
Radiated and Conducted Propagation...........................................................................................134 
4.4.5 
Cabling/ Wire Harness ...................................................................................................................135 
4.4.6 
Capacitive Coupling .......................................................................................................................135 
4.4.7 
Inductive Coupling..........................................................................................................................135 
4.4.8 
Shielding.........................................................................................................................................136 
4.4.9 
Coaxial Cable vs. Twisted Pairs.....................................................................................................137 
4.4.10 
Braided Shields..........................................................................................................................137 
4.4.11 
Special Considerations ..............................................................................................................138 
5 
COMPARTMENT CONSIDERATIONS ...............................................................................................................139 
5.1 
MODULAR.............................................................................................................................................139 
5.2 
ENGINE ................................................................................................................................................139 
5.3 
I/P AND CONSOLE.................................................................................................................................143 
5.4 
BODY ...................................................................................................................................................144 
5.4.1 
Routing Holes.................................................................................................................................144 
5.4.2 
Trim ................................................................................................................................................145 
5.4.3 
Pillars..............................................................................................................................................145 
5.4.4 
Floors .............................................................................................................................................146 
5.4.5 
Additional Considerations ..............................................................................................................146 
5.5 
DOORS.................................................................................................................................................146 
5.5.1 
Door Swing Movement...................................................................................................................146 
5.5.2 
Attachments ...................................................................................................................................147 
5.5.3 
Wet Areas.......................................................................................................................................147 
5.5.4 
Additional Considerations ..............................................................................................................147 
5.6 
SEATS..................................................................................................................................................147 
5.7 
TRUNK .................................................................................................................................................148 
5.8 
UNDERBODY.........................................................................................................................................148 
5.9 
HEADLINER...........................................................................................................................................148 
6 
EDS DESIGN PROCESS..........................................................................................................................................150 
7 
APPENDIX ................................................................................................................................................................152 
7.1 
GLOSSARY OF TERMS AND DEFINITIONS ....................................................................................152 
7.2 
SAE - ISO WIRE CONDUCTOR AREA COMPARISON CHART.....................................................................157 
7.3 
TYPICAL COAX CABLE PROPERTIES.......................................................................................................158 
7.4 
COVERING APPLICATION AND STRATEGY CHART ....................................................................................159 
7.4.1 
85 °C ..............................................................................................................................................160 
7.4.2 
125 °C ............................................................................................................................................160 
7.4.3 
150 °C ............................................................................................................................................161 
7.4.4 
170 °C -200 °C ...............................................................................................................................161 
7.5 
YPES SPECIFICATION CHART ...............................................................................................................162 
7.6 
DESIGN HIGHLIGHT ...............................................................................................................................165 
7.6.1 
Vibration and Fretting Corrosion ....................................................................................................166 
7.6.2 
Bare Terminals Tape Back on Optional Content ...........................................................................168 
 


### 第 8 页
Yazaki EDS Design Guideline Rev 3 
 
7
 
 
 
 
 
 
 
 
 
This page left blank purposely. 
 


### 第 9 页
Yazaki EDS Design Guideline Rev 3 
 
8
1 Introduction to the Wire Harness  
1.1 Definition of the Electrical Distribution System and Wire Harness 
The Electrical Distribution System (EDS): A network system providing power and 
signals to every electrical/electronic device in the vehicle. This key component 
provides the means to operate and control the vehicle.  Some of the major systems 
involved with the EDS include: Engine control and monitoring, anti-lock brakes, airbag 
safety systems, power windows and locks, lighting, and entertainment.  The EDS is the 
“nervous system” of a vehicle.  The wire harness is the backbone of this system. 
The Wire Harness (W/H):  A system of components that allows power and signals to 
traverse the vehicle.  Each wire harness is made from many varying components, 
however at its core contains the following: Wire, Terminals, Connectors, Seals, Tapes, 
Clips, Grommets, Protective coverings, and Routing troughs.  The wire harness is the 
backbone of the EDS system. 
 
1.2 Wire Harness Anatomy 
 
 
 


### 第 10 页
Yazaki EDS Design Guideline Rev 3 
 
9
Branch – Secondary bundles or individual circuits that originate from a main trunk and 
end at a node containing a connector or eyelet terminal. See Figure 1. 
Breakout Tape – Tape applied to a specific location on the harness to control the 
location and direction of take outs. See Figure 1. 
Main Trunk – The primary routing path of the harness from which all take outs 
originate from.  A main trunk may have other nodes in its path, none of which will have 
connectors or eyelet terminals attached. See Figure 1. 
Single Lead – A section of a harness or complete harness that contains only one 
circuit. 
Take Out – See Branch. 
Wiring Pigtail – A wire or group of wires permanently attached to an electrical 
component such as a switch or motor. 
Protector – it consist of tape, covering, tubing, convolute, troughs and etc. 
 
1.3 Vehicle Anatomy 
 
1.3.1 
Major Body-in-White Components 
 


### 第 11 页
Yazaki EDS Design Guideline Rev 3 
 
10
 


### 第 12 页
Yazaki EDS Design Guideline Rev 3 
 
11
 
 
1.3.2 
A Vehicle’s Electrical Architecture 
 
1.3.3 
Subsystem Architecture 
 
1.3.4 
Harnesses Families 
 
The following provides a description of typical harness families: 
 
Head lamp and Dash 


### 第 13 页
Yazaki EDS Design Guideline Rev 3 
 
12
This harness is located in the engine compartment.  The Headlamp and Dash 
harness generally has an interconnect (inline) to the Engine harness and the 
Instrument Panel harness. 
Engine 
The engine harness is located on or next to the engine.  The Engine harness may or 
may not include transmission connections.  The Engine harness generally has an 
interconnect (inline) to the Headlamp and Dash harness.   
Body 
The body harness is located in the interior body.  The Body harness may connect to 
the Headlamp and Dash harness, Instrument Panel harness, Door harness, Tail 
lamp harness/jumper, and/or Lift gate/Deck lid harnesses.   
Instrument Panel (I/P) 
The Instrument Panel harness is located in the interior body behind the dash panel.  
This family is commonly called the I/P or cockpit.  The instrument panel may have 
interconnects (in-lines) to the Headlamp and Dash harness, Door harness, Body 
harness, and/or jumper harnesses.   
Headliner 
The headliner harness is located in the passenger compartment overhead and is 
sometimes referred to as the dome or header harness. The header harness is 
commonly used in convertibles.  The headliner/header harness usually connects to 
the instrument panel harness, body harness, and/or a jumper. 
Seats 
The seat harness is located in the interior body.  The seat harness connects to the 
body harness and/or a jumper. 
Minor Harnesses 
The minor harnesses are located in the interior and exterior body.  The minor 
harnesses have interconnects to body, instrument panel, headlamp and dash, doors, 
and/or engines. 
Jumper Harnesses 
The jumpers are located in the interior and exterior body.  Jumper harnesses are 
typically used as an aid to the automotive assembly process.  Jumpers interconnect 
to body, instrument panel, headlamp and dash, doors, engines, seats, and/or minor 
harnesses. 
 


### 第 14 页
Yazaki EDS Design Guideline Rev 3 
 
13
1.4 Trends in EDS 
 
Since the 1970’s vehicles have had constantly increasing needs for additional power 
and greater harness complexity.  Alternators have increased in size from 50 amps then 
to roughly 150 amps today.  Vehicle harnessing has gone from about 300 circuits then 
to 1500 circuits today.  This trend is expected to continue as vehicle-makers move 
toward more electrical content such as steer-by-wire and brake-by-wire systems of the 
future.  
 
1.4.1 
Key Trend Drivers 
Key drivers that must be considered when designing and manufacturing a wire 
harness:   
Cost 
Cost continues to be the major driver in wire harness design.  Competition and OEM 
demands continue to drive down the selling price of harnesses.  In order to remain 
profitable, the cost of the wire harness must be reduced accordingly. 
Quality 
Because the wiring harness is typically one of the first systems installed on the 
body-in-white, harness quality can have a major impact on the company’s bottom 
line.  A poorly designed or manufactured harness may result in high warranty 
claims; enough so that any profits are erased with one claim.  Validating the harness 
in the application is paramount in confirming a quality product. 
Reliability and Durability  
Reliability and durability are closely aligned with the quality of the harness.  Most 
harnesses cannot easily be replaced and therefore must function throughout the 
lifetime of the vehicle.  The best way to confirm the reliability and the durability of a 
harness is by: 
1.) Verifying that the components meet the engineering specifications.  
2.) Validating the harness assembly in the intended vehicle application. 
Weight and Space Utilization 
Wire harnesses continue to link up more and more vehicle content such as 
entertainment systems and powered doors.  As a result there are a greater number 
of circuits employed, including higher amperage circuits.  Careful sizing of 
components, sub-systems, and routing will make the harness as weight and space 
efficient as possible.  


### 第 15 页
Yazaki EDS Design Guideline Rev 3 
 
14
Design for Manufacturing 
Designing for Manufacturing takes the wire harness manufacturing facility strengths 
and weakness into account during the design process.  Doing so can eliminate 
potential processing issues and reduce manufacturing times.  Two examples: 
1). Harness assembly is labor intensive.  The amount of time needed to assemble 
the harness is reduced, and hence the cost can be reduced. 
2). Wire harnesses can be very complex.  Simplified designs result in fewer 
manufacturing errors, and a corresponding reduction in warranty claims.  
Design for Assembly 
Designing for Assembly involves taking the OEM specific installation factors into 
account.  Understanding the strengths and weaknesses of the OEM process will 
enable the wire harness engineer to design a product that is robust and aids in the 
vehicle installation.  Doing so reduces potential warranty claims, line shut-downs, 
and may help the OEM to reduce their labor burden resulting in a cost savings. 
Safety 
A properly designed EDS wiring harness will benefit the consumer by providing safe 
reliable service during the life of the vehicle.  Designing a harness from a safety 
standpoint requires the engineer to take into account all events that could lead to 
thermal failure (fires), short circuits, and/or other failures of the harness that could 
cause harm to the occupant or vehicle.  Design issues involving safety concerns 
should be addressed with your engineering management. 
Environmental Considerations 
The protection of our environment continues to be a growing driver in new wire 
harness design.  Government, industry, and corporate rules and regulations require 
that we pay closer attention to the components that populate our products.  In the 
global market, the design engineer must be cognizant that materials or products 
may be limited or banned by law.  Understanding these requirements will make it 
easier for you to choose the correct components. 
Serviceability 
Vehicles will be checked and repaired periodically.  There are times when harness 
connectors are disconnected for regular service or repairs after an accident. The 
harness and its components must be designed to facilitate this.   
Appearance 
A well designed EDS should be reasonably hidden from view.  The portions that are 
seen should follow the requirements set forth by the OEM.  When there are no direct 
requirements, the design engineer should use their best judgment to create a 
harness that is aesthetically neutral.  Over-taping, excessive convolute and exposed 


### 第 16 页
Yazaki EDS Design Guideline Rev 3 
 
15
colored wires are examples that should be considered.  Generally too much = too 
much cost.  Too little = aesthetically unpleasing. 
 
1.4.2 
Technology Trends 
Subsystem Integration 
High Speed Digital 
Hybrid Electric Vehicles 
 
 


### 第 17 页
Yazaki EDS Design Guideline Rev 3 
 
16
2 Wire Harness Components  
2.1 Wire and Cable 
Wire makes up the backbone of the EDS harness.  All power and the vast majority of 
signals are carried from one point to another along the conductor of the wire.  A typical 
vehicle uses over a mile of wire in its EDS harnessing.   
 
2.1.1 
Wire Anatomy 
 
 
 
A typical wire construction is shown above.  The majority of automotive wire has a 
single copper conductor surrounded by an insulating layer.  The correct insulating layer 
is determined by the operating environment. 
 
2.1.2 
Wire Terminology 
Primary Wire – A reference to smaller wire sizes used in the electrical distribution 
system.  Typical sizes are 22awg (0.35 mm2) to 10awg (5.0 mm2). 
Battery Cable – A reference to larger wire sizes used in the electrical distribution 
system.  Typical sizes are 8ga (8.0 mm2) to 2/0ga (62.0 mm2). 
Fusible Link – A reference to a specialty wire designed to open when subjected to an 
extreme current overload (dead short).  Its purpose is to minimize wiring system 
damage when such an overload occurs.  
A fusible link is generally 4 AWG sizes smaller than the wire to which it is spliced.  As 
an example, a 4 AWG circuit is protected with an 8 AWG fusible link.   


### 第 18 页
Yazaki EDS Design Guideline Rev 3 
 
17
Thermoplastic insulation – An insulation type that softens as the material 
temperature increases.  This material will melt above a certain temperature depending 
on the material.  As the temperature is reduced the material will generally harden again.  
PVC is an example of this type of insulation.  This material can generally be recycled. 
Thermoset insulation – An insulation type in which the polymer chains are 
permanently joined during processing.  This material will not melt.  This is also referred 
to as “cross-linking”.  Preferred methods of cross-linking can be achieved through 
heating, chemical reaction, or irradiation. Cross-linked Polyethylene (XLPE) is an 
example of this material.  This material cannot generally be recycled. 
Conductor – The metallic core of the wire.  This is usually a stranded bare copper. 
 
2.1.3 
Wire Selection 
The choice of wire is based on many factors.  These include: 
• Electrical load  
• Voltage drop requirements 
• Rating of the upstream circuit protection device 
• Operating temperature(s) 
• Fluid resistance issues 
• Abrasion resistance 
• Bending requirements 
• Cost 
• Flexibility 
• Other application specific parameters 
 
 
2.1.3.1 
Temperature Rating 
In choosing a wire for an application, the engineer should specify a temperature class 
rating (TCR) above the continuous operating temperature.  As a general rule of thumb, 
85°C is used in interior applications, and 100°C through 150°C materials are used in 
higher temperature environments such as the engine compartment. 
 
Two factors should be considered when choosing the appropriate TCR: 
1.) The environment operating temperature. 


### 第 19 页
Yazaki EDS Design Guideline Rev 3 
 
18
2.) The temperature rise in conductor due to I2R (current flow) heating. 
 
The rated temperatures are considered to be the “continuous” operating temperatures, 
the maximum sustained temperature at which the wire can survive over the lifetime of 
the vehicle.  Some insulation types can withstand temperatures above the continuous 
operating temperature for short periods of time.  This benefit is offset by a reduction in 
the lifetime of the insulation.  
 
Temperature Rating is based on a combination of ambient temperature, electrical 
loading, and wire size.  Typical wire insulations are rated for (continuous) 3000 hours 
“Temperature Class Rating” (TCR).  The TCRs are: 
Class 
Temperature °C 
A 
85 
B 
100 
C 
125 
D 
150 
E 
175 
F 
200 
G 
225 
H 
250 
 
For wire harness applications, the maximum continuous temperature of the insulation 
should not be exceeded.  Always choice a TCR above your operating temperature 
(e.g., 100°C wire in a 90°C application). 
 
Typical insulation materials for automotive wires are: 
Polyvinyl Chloride (PVC) 
Primary wire (85°C), general 
passenger compartment 
applications 
Cross linked Polyethylene 
(XLPE) 
Primary wire and battery cable 
(125°C or 150°C), engine 
compartment applications 
Fluoropolymers (Teflon™) 
 
Primary wire, high temperature 
(200°C -250°C), special 
applications 


### 第 20 页
Yazaki EDS Design Guideline Rev 3 
 
19
Cross-linked Polyethylene 
(XLPE) 
Fusible link (100°C) 
 
2.1.3.2 
Wire Size  
�As the general rule, wire must be able to continuously withstand 135% of the fuse 
current rating.  This level of protection is necessary in the event of an overload 
condition where the fuse must blow before the wire melts. (See architecture section for 
more detail) 
 
2.1.3.3 
Conductor Size 
The conductor size is chosen based on electrical load or voltage drop requirements.  
Occasionally a larger size is required by the customer even though a smaller size 
would suffice. The table below lists some commonly used sizes. 
 
AWG 
22 
20 
18 
16 
14 
12 
10 
8 
6 
4 
2 
Metric 
Size 
(mm2) 
0.35 
0.5 
0.85 1.25
2.0 
3.0 
5.0 
8.0 
13.0 19.0 32.0
 
 
2.1.3.4 
Voltage Drop 
Certain circuits are limited by voltage drop.  On these circuits the input voltage is 
reduced by the wire’s resistance so that the output voltage is insufficient to drive the 
attached device.  This can usually be corrected by using the next larger wire size. 
 
The voltage drop is related to the circuit length; the longer the circuit, the larger the 
voltage drop.  Smaller diameter wires have greater resistance per unit length than 
larger diameter wires.  As an example, a typical 22awg copper conductor has a 
resistance of 50.0 mΩ/m, and a typical 20awg copper conductor has a resistance of 
31.7 mΩ/m. 
 
                                            
� In design review checklist. 


### 第 21 页
Yazaki EDS Design Guideline Rev 3 
 
20
Here are a couple of examples of how voltage drop can affect system design: 
• Systems, such as headlamps are voltage sensitive. Lower voltage for lamps 
mean less luminance. Too much voltage means shorter bulb life.    
• For window motor circuits, output voltage differences can cause one window to 
close before the other.  In this case, if we assume a left and right front window, 
and for all other parameters being equal, a difference in circuit length would 
result in a lower output voltage on the longer circuit.  This, in turn, could cause 
the longer circuit length motor to run slower and close the window more slowly. 
 
2.1.3.5 
Flexibility 
The flexibility of wire can be important to the design.   Most automotive wire use 
stranded conductors, and not a solid core. This is because the wire harness needs to 
be flexible during assembly and installation into the vehicle. It may also need to be 
flexible to meet its performance requirements over the life of the vehicle.  Stranded 
wire is also needed to allow for proper terminal crimping. 
 
Other factors also influence the flexibility of a wire.  These include: 
• Temperature 
• Wire Insulation 
• Conductor Construction 
• Insulation Process 
 
The thing to remember is that all these factors influence the wire’s flexibility 
performance. 
 
2.1.3.6 
Abrasion 
Abrasion will occur when there is motion between any two contacting surfaces.  In the 
case of wire this can be from external sources, or may be from the conductor moving 
relative to the insulation.  The former is common, the latter less so.  
There are two forms of abrasion resistance testing that are used in industry wire 
specifications.  These are referred to as 1) sandpaper abrasion and 2) scrape abrasion. 
1) 
The sandpaper abrasion test attempts to replicate rubbing of insulation such as 
we might see when grit or salt get into a harness bundle.  This test is commonly used 
for North America and Asian OEMs. 


### 第 22 页
Yazaki EDS Design Guideline Rev 3 
 
21
2) 
The scrape abrasion test replicates pulling an insulated wire across an edge, 
such as that of a sheet metal panel.  This test is used primarily by European OEMs. 
The two test are not similar, each testing different failure modes.  Results from one 
cannot be correlated to the other. 
Engineers should never rely on wire abrasion resistance as the first line of defense in a 
known abrasive environment.  The use of protective coverings should be considered to 
protect the insulation from premature failure.  Remember…the insulation is the last line 
of defense. 
 
2.1.3.7 
Mechanical Strength 
All else being equal, a larger wire gauge size can withstand a greater tensile force 
(pulling force) than a smaller wire gauge.  For a given material the tensile strength 
value is constant and is equal to the force applied divided by the cross-sectional area.  
So a larger wire size, with a corresponding larger cross-sectional area, can carry a 
greater physical load. 
Small wires such as 0.5mm2 and 0.35mm2 have much less mechanical strength.  As 
an extreme example, if a connector has only a single circuit, and if an operator lifts the 
harness by pulling on that connector, it is possible that the weight of the harness may 
exceed the tensile strength of that single wire.  The result would be a broken conductor.  
Other possible damage caused to the harness could be: 
• Conductor elongation causing a reduced current carrying capability 
• Broken strands within the wire reducing current carrying capability, and possibly 
leading to premature fatigue failure 
• A failure at the termination 
Usually, there are several wires in a single connector.  In some cases, there may only 
be a single wire to a multi-cavity connector due to options not provided on that 
particular vehicle.  If there are no other alternatives, it might be better to provide a 
dead circuit to give the takeout additional strength when this condition exists. 
 
2.1.3.8 
Column Strength 
Column strength refers to the insertion force required to buckle a small gauge wire.   
This condition is an issue when the insertion force of a terminal into a connector cavity 
is greater than the buckling strength (stiffness) of the wire crimped to the terminal. This 
can make populating the connector difficult, resulting partially seated terminals.   
Column Strength issues typically only concern small gauge circuits such as 0.35mm2 
(22awg) and smaller.   Possible solutions include: 
• Use the next larger wire gauge 


### 第 23 页
Yazaki EDS Design Guideline Rev 3 
 
22
• Use a high strength alloyed conductor (generally very expensive) 
• Use specialized tooling to seat the terminal in the connector 
The down-side of these changes includes: 
• Increased cost  
• Additional weight 
• Increased resistance for alloyed conductors 
 
2.1.3.9 
Fluid Resistance � 
The fluids used in the vehicle may adversely affect a wire’s insulation.  Depending on 
the application, make sure that the insulation material of the wire can withstand fluids 
such as: 
• Gasoline 
• Brake fluid 
• Transmission fluid 
• Power steering fluid 
• Engine oil 
• Battery acid 
• Antifreeze 
 
Some OEMs require additional fluids to be tested.  It is important to understand your 
OEMs requirement before choosing a particular wire. 
 
2.1.3.10 Special Applications 
There are unique or special applications (high flexibility, high temperature, severe 
fluid/chemical resistance, multi-conductor cables, RFI/EMI shielding, etc) which require 
specialty wires / cables.  For selection of these special applications, please contact the 
Wire or Digital Cables Engineer in the EDS Advanced Manufacturing & Feasibility 
group for assistance and recommendations. 
 
2.1.4 
Known Wire Issues 
 
                                            
� In design review checklist. 


### 第 24 页
Yazaki EDS Design Guideline Rev 3 
 
23
2.1.4.1 
Wicking 
Wire can draw water into the space between the conductor strands by capillary action.  
Water may also wick between individual circuits in a bundle through the taping or 
convolute.  This water can travel as much as several meters and cause corrosion 
problems at splices, terminals, and with the wire conductor itself.  Wicking issues can 
be reduced by: 
Using properly sealed connections in the wet areas 
Use dual wall heat shrink tube over splices 
2.1.4.2 
Overheating 
The load through the circuit can also cause conditions for concern.  Besides the steady 
state current on the circuit, there are several other factors that must be considered. 
Temperatures in the engine compartment can reach 150°C or more.  This environment 
can accelerate aging and lead to higher resistance in the circuit. 
Stall currents can also raise the internal wire temperature such as in the situation 
where a motor has reached its point of no further movement (power window down, 
Wipers frozen to the windshield, etc.). 
 
******* 
Combined Heat Generation 
This can happen when circuits that always have current flowing through them run in 
parallel to each other and cause a combined heating effect. This affect is enhanced 
when tubing or coverings are applied which hinders heat dissipation. 
 
2.1.5 
Wire Length  
******* 
Minimum and Maximum Length 
There are minimum and maximum lengths for which a wire can be processed safely by 
automated equipment in our affiliate wire harness plants.  Please refer to the DFM 
Manual and DFM checklist for these requirements.   
2.1.6 
22 Gauge Wire 
Special considerations should be taken if using 22 gauge wire. The guidelines for 
using 22 gauge wire are outlined below. 
Harness design: 
• It should only be used in connectors loaded with 4 or more circuits. 


### 第 25 页
Yazaki EDS Design Guideline Rev 3 
 
24
• It should only be used in interior cavities on large cavity count connectors. 
• It should only be used for non sealed applications or sealed applications with 
the appropriate individual cable seal. Note: Use 20 gauge with 0.4mm insulation 
thickness min. for mat sealed applications.  
• It should only be used with terminal families that have grip designs that will meet 
the electrical, mechanical and visual requirements of the prevailing crimp 
evaluation specifications.  
• It should only be used in terminal to terminal circuits.  Circuits requiring a splice 
joint or weld will require a modified splice spec or deviation from existing 
specification to allow the use of dual wall shrink tubing for strain relief.  
System design: 
• Fuse loading and voltage drop will need to be evaluated in the downsizing effort. 
Harness manufacturing: 
• All grip/wire combinations will need to be approved per the prevailing crimp 
process standard.  
• 22 grip applicator tooling will need to be added to the existing tooling inventory. 
Vehicle build: 
• Vehicle assembly plant will need to take responsibility for successfully 
unpacking, unbundling and placing the harness containing 22 gauge wire into 
the vehicle.  
• Customer plants personnel will need to recognize tensile damage caused by the 
vehicle assembly process and assign responsibility accordingly.   
 
2.2 Digital cable 
 
2.2.1 
Introduction 
There is no physical difference in the appearance of a digital cable when compared to 
an analog cable.  The difference lays in how the cable carries the signal.  The following 
illustrates the difference between a digital and an analog signal. 
 
 


### 第 26 页
Yazaki EDS Design Guideline Rev 3 
 
25
Hi EMI energy
Digital Signal
Conditioned 
Digital Signal 
Analog Signal
 
 
Digital cable refers to a wire transmission line that carries digital signals.  The primary 
purpose of the digital cable is to carry data, not power.  Any cable can be used for 
digital cable; however there are associated precautions and practices that are required.   
 
2.2.2 
Digital Cable Types 
Digital cable comes in several forms with several construction options.  The choice of 
the cable and its options will determine the cost and performance.  High performance 
typically comes at high cost.  It is a good idea to use a shielded wire for digital cable or 
high speed applications. Coaxial cable is the preferred choice where applicable. 
Twisted pair 
 
Unshielded 
 
Shielded 
Coaxial Cable 
 
 
Impedance: 50Ω, 75Ω, 100Ω, etc. 
 
Diameter size 
 
Shielding method 
Typical coaxial Cables: 


### 第 27 页
Yazaki EDS Design Guideline Rev 3 
 
26
Cable Type 
Impedance 
Outside Diameter 
Uses 
RG-58 
50 
5 mm 
Antenna, XM radio 
RG-59 
75 
6 mm 
Video, DVD, Camera 
RG-174 
50 
3 mm 
Antenna, XM radio 
RG-179 
75 
3 mm 
Video, DVD,  Camera 
RG-316 
50 
3 mm 
High temp, high performance
 
The cable shielding options are: 
Aluminized Mylar Foil shield - Lowest cost, light weight, must use a drain wire for 
termination, good for high frequency applications.   
Shielding coverage: 100% with a recommend 20-30% overlap to allow for cable 
bending. 
Spiral (Served) shield - higher cost, not widely used in automotive applications, use 
braided shield instead. 
Braided shield – wide range of coverage areas available depending on the application, 
higher cost, termination difficulties for applications other than coax.  Good for low 
frequency applications. 
Foil and Braid combination shield – highest cost, best performance, difficult to 
terminate in manual operations. 
 
2.2.3 
Impedance Matching 
 
When two wires form a transmission line, capacitance and inductance are two 
important characteristics associated with these wires.  They are related to the 
impedance by: 
i C
G
i L
R
C
L
Z
ω
ω
+
+
=
0 =
 where 
πf
ω
= 2
,  f =frequency. Z0= Impedance. 
Impedance increases as frequency goes up in digital cable, and it is very important to 
control the impedance.  For example, coaxial cable is an impedance controlled cable.  


### 第 28 页
Yazaki EDS Design Guideline Rev 3 
 
27
 
 
The terminating resistor must match the cable impedance to maintain power efficiency.  
 
2.2.4 
Shield Termination Method 
 
1) Wire pair or twisted pair 
 
Frequency ≤ 100 KHz � terminate shield at one side only (TX side). 
 
Frequency >100 KHz, � terminate shied at both ends with precaution. Consider 
coaxial cable if twisted-pair cables reveal any performance problems. 
2) Coaxial cable: 
 
Always use 360 a degree shielded connector, and make sure shield is 
terminated correctly. 
 
Note: Absolutely NO SPLICES are allowed in digital cables as this will cause a 
signal bounce in the system and unpredictable EMI results.  
 
2.2.5 
Twisted Pair wire selection and guidelines 
 
Use Shielded twisted pairs (STP) for low speed applications unless proven otherwise.  
 
In order for the twisted pair to be properly selected, the following information must be 
considered:  
 


### 第 29 页
Yazaki EDS Design Guideline Rev 3 
 
28
2.2.5.1 
Application 
Twisted pairs are mostly used for sensor and communication applications.  The 
twisting of the pair helps limit noise on the circuits. 
In the case of sensor circuits, the twisting helps limit the effects of external signals 
interfering with the information being transmitted on the circuits.  For this to be effective 
the twisted pair must have a consistent twist pitch (lay length) and termination method.  
If the lay length varies over the length of the circuit, then the circuit will be more 
susceptible to outside interference.  To confirm rejection of signals in a specific 
frequency range the engineer should conduct tests of the harness in the actual 
application.    
If the twisted pair is used for a communications transmission line, then we are 
concerned with both signals radiated from the circuits as well as external signals 
interfering with the circuits.  As with sensor circuits the twisted pair should be validated 
in the actual application. 
If multiple twisted pairs are required then each should have a different lay length.  If the 
lay length is identical and the distance between the twisted pairs is just right, then the 
chance for one signal interfering with the other is considerable.  The science of 
multiple twisted pair design is complex. 
 
******* 
Operating Frequency 
The amount of radiated energy depends on the operating frequency of the system.  
The higher clock edge amplitude, the more energy it will radiate. Typically, frequency 
less than 100 kHz is considered low speed.  Anything higher than 1.0 MHz is 
considered high speed.  At high speed, more attention is needed for impedance 
matching the cable, shielding, and termination.  Shield termination should be done at 
one end of cable for low frequencies and at either end or multiple points for a high 
frequency system. 
******* 
Signal Level 
Twisted pairs work best for balanced signal – differential signal.  Typically, balanced 
circuits have better common mode noise rejection.  Twisted pairs for balanced signals 
radiate less because of the cancellation effect due to the twist. Twisted pairs may have 
no significant improvement on a single end signal, but it will achieve some degree of 
improvement on susceptibility.  
******* 
Cable Length 
In general, a minimum cable length is always the best. In higher speed applications, 
the cable should be kept as short as possible. As frequency increases, the impedance 
on the cable increases also.  


### 第 30 页
Yazaki EDS Design Guideline Rev 3 
 
29
******* 
Operating Environment 
Understanding the operating environment for the twisted pair is as important as 
choosing the right cable.  If the twisted pair is bundled with some other cables or wires, 
you should ask if there are there any high voltage wires in the bundle.  Typically, 
shielding on the twisted pairs is good for e-field noise, where there is an induced 
magnetic field from high current and voltage wires.  The induced magnetic field will 
couple into the adjacent wires and circuits.  In order to minimize the EMI emissions, 
each wire should be evaluated to see if there is an EMI problem.  Appropriate actions 
should be taken once an EMI problem has been identified.  For noisy environments, 
shielding should be considered.  
 
2.2.6 
Coax Cable Design Guideline 
 
2.2.6.1 
Impedance Match 
The impedance of a coax cable transmission line must match with the design system 
for optimal power and signal transmission. Not only the cable has to meet the 
impedance requirement, all the inline connector and end connector have to meet such 
requirement also. Different application will dictate its own impedance requirement. For 
example: 
 
 
Antenna 
 
 
50 Ohm 
 
Video  
 
 
75 Ohm 
 
Satellite 
 
 
93 Ohm 
 
Communication Truck 
100 Ohm 
2.2.6.2 
Shielding Requirement 
Coax cable shielding method: 
• Braided (Coverage between 50%-95%) 
• Foil with braid (Coverage between 50%-95%) 
Foil with braided shield coax cable is typically for high speed and digital application 
where higher cable shielding effectiveness is required. 
Coax cable is designed for 360 degree termination on connector, correct crimp method 
must be used for optimal performance. All finish coax cable should undergo inline 
VSWR testing, center pin depth inspection and other performance test where 
applicable. There is absolutely no splicing for the coax cable.  


### 第 31 页
Yazaki EDS Design Guideline Rev 3 
 
30
2.2.6.3 
Attenuation 
The total cable attenuation must within the total budget of the design system which 
included inline connectors, end connectors and the length of the coax cable. 
2.2.6.4 
Routing 
Coax cable should rout away from high current and high voltage cable to avoid 
unnecessary noise interference. Sharp bend of the coax cable should be avoided and 
must keep a minimum bending radius of 10 times or higher the diameter of the coax 
cable.  
2.2.6.5 
Operating Frequency 
Every coax cable is manufactured differently, operating frequency for which the 
application was designed for must within the operating frequency limit of the coax 
cable to ensure proper performance guarantee. The frequency curve of the coax cable 
is normally not a linear curve, double check worse case scenario requirement.  
2.2.6.6 
Coax Cable Rating 
Coax cable is classified by: 
a) Core material 
 
Foam core  80°C 
 
Air core 
80°C 
 
Teflon core 125+°C 
b) Center Conductor 
 
Solid copper 
 
Solid copper clayed steel 
 
Stranded copper  
c) Shielding 
 
Braided shield 
 
Foil w/Braided shield 
d) Cover 
 
PVC 
 
Teflon 
 
Others 
 


### 第 32 页
Yazaki EDS Design Guideline Rev 3 
 
31
e) Operating Frequency Limit 
 
Application frequency must within the limit of the cable. 
 
2.3 Connectors and Terminals 
 
Connectors are typically injected molded plastic parts. They serve as an electrical 
insulator between terminals in the connection as well insulating the connection from 
other parts of the vehicle. Most connectors are molded with cavities in the housing that 
accept the terminated circuits as well as wedges, seals and mounting provisions. 
Connectors can also be over-molded where a terminated circuit or mounting provision 
(metal bracket, nuts, bolts, etc.) are placed in a mold and molten plastic is injected to 
fill the mold cavity around the metal component. Examples of connectors that can be 
over-molded are some trailer tow connectors and lamp sockets.  
 
Connectors and terminals make up the wire harness’s connection system. This system 
provides the electrical and mechanical interface for power and signal functions to 
electrical devices or between wire harnesses. It also allows a wire harness or device to 
be serviced by means of a quick disconnect.  There may be hundreds of connections 
in the modern automobile. It is important to note that connections are operator 
sensitive and can be a contributor to initial build successes and failures in the vehicle 
assembly process. Connection failures will also contribute to service and warranty 
issues.  
 
Each connector assembly, terminal, cable seal, etc. used in a wire harness assembly 
must be approved and registered in the Yazaki R.O.P (Registration of Parts) system 
before it can be incorporated into a wiring system. United States Council of Automotive 
Research (USCAR) specifications, 3-D models, Device transmittals and Production 
Part Approval Process (PPAP) are some of the resources used in identifying the 
design and performance characteristics of various connector systems. Vehicle 
packaging requirements and environmental conditions must be considered when 
selecting connection systems.     
                                
2.3.1 
Anatomy of a Connector 
 


### 第 33 页
Yazaki EDS Design Guideline Rev 3 
 
32
 
 
Note: Connectors having the female terminals are female connectors. Connectors 
having male terminals are male connectors.  
 
2.3.2 
Connector Types 
******* 
Unsealed Connectors 
There are no cable or interface seals required and the housings do not need to be 
watertight in unsealed connector designs. 
 
Connector designs used in dry areas of a vehicle are typically unsealed. Vehicle 
interiors and trunks are considered dry areas. There are exceptions to the 
sealed/unsealed use requirements as they relate to off road vehicles and heavy 
equipment. Some applications may require sealed connectors in areas considered to 
dry. Another exception is relay and fuse connections in the engine compartment. 
These are often unsealed when they can be splash protected from environmental 
contaminates.  
 
******* 
Sealed Connectors 
Connectors located in the engine compartment or areas in the vehicle exposed to 
splash and moisture are typically sealed. A sealed connector consists of a watertight 
housing with interface and cable sealing provisions. Sealed connectors are designed 
to keep salt and water, automotive fluids like gas and washer solvent and other 
contaminants such as dirt out of the electrical interface. The purpose of keeping the 
electrical interface clean and dry is to assure that corrosion or contaminants can’t 
cause high resistance or short circuit conditions and interfere with the connector’s 
intended electrical performance.  
Cable sealing types include: 


### 第 34 页
Yazaki EDS Design Guideline Rev 3 
 
33
• Mat seals1 
• Individual Cable seals 1 
• Gel seal 2 
 
2.3.3 
Connector Applications 
 
2.3.3.1 
Inline Connectors 
Inline connectors provide an electrical and mechanical connection between wire 
harnesses or circuits.  
 
2.3.3.2 
Direct Connect (Device Connectors) 
Direct-connect connectors provide electrical and mechanical connections to modules, 
sensors, motors, etc. In direct connect systems one half of the connector is part of the 
device.   
 
2.3.4 
Connector Selection 
 
2.3.4.1 
General Selection Factors 
Terminals (see section C) must be specified first when selecting connectors for wire 
harness designs. Terminals that have the required current capacity, are compatible 
with the required wire size, are available with the necessary plating type and will meet 
the temperature class performance requirements of the application must be identified 
prior to selecting a connector. The terminal families that meet the application 
requirements will drive the connector selection process.    
 
Application factors can determine: 
• Terminal/size/type. 
• Sealing requirements. 
• Special considerations such as isolating some circuits in a separate connector. 
• Special coating or plating such as gold for dry circuit3 applications.   
                                            
1 In a Mat seal system all of the wires pass through a seal installed in the connector. In a Cable seal type 
system each circuit has a seal installed before the individual terminal is inserted into the connector housing. 
2 The use of a “Gel seal” system should be avoided due to poor performance history. 


### 第 35 页
Yazaki EDS Design Guideline Rev 3 
 
34
 
Connector selection factors: 
• Current load, current density and duty cycle.  
• Several high current circuits may require the power to be spread over multiple 
connectors vs. all in an individual connector.   
• Signal4 and power functions. 
• Some connector families are specific for power or signal applications. Hybrid 
connectors offer multiple terminal sizes and types in the same connector 
housing. These type connectors are often be selected for mixed signal and 
power applications.  
• Environmental considerations.  
• When selecting a connector, consideration must be given to its application 
relative to operating temperature range, exposure to water/salt, chemicals, fluids, 
mechanical shock, thermal shock and vibration.  
• Package size 
• The terminal configuration and spacing can affect a connector’s package size. 
Sheet metal pass-through and mounting real estate can be drivers in connector 
selection.  
• Mounting provision 
• Some connector systems are designed with clip provisions, some for bracket 
type mounting and others are designed without mounting provisions.  
• Total connector system cost 
• Total system costs include: terminals, seals, connector assemblies, wedges, 
wire dress covers, mounting provisions and processing labor. An expensive 
terminal can off set the savings realized by selecting a low cost connector 
assembly. Loose piece parts like wedges or covers can add labor costs to a 
connector system. Connectors that require specialized jigs and fixtures or 
require off line harness build operations is another factor to consider. Terminals 
that are not available on a carrier strip (can’t be processed at high speed) can 
also drive up labor costs. Some connector systems don’t have provisions for 
electrical test fixtures. This type of connector can also add labor to the harness 
assembly process due to extraordinary inspection requirements.  
• Ergonomics 
• Where there is a choice, connector systems with low mating efforts should be 
selected. High connector mating forces can result in missed or incomplete 
connections operations in the vehicle assembly plants and result in warranty or 
customer satisfaction issues. Connectors that are easy to assemble and mate 
can also reduce repetitive stress injuries to the operators.   
                                                                                                                                                      
3 Dry circuit as defined in ASTM B 539 Para. 3.1.2 Is a circuit where open circuit voltage is less than 20mV. 
4 Signal circuits are often considered Dry circuits. A Dry circuit as defined in ASTM B 539 Para. 3.1.2 is a circuit 
where open    circuit voltage is less than 20mV. 


### 第 36 页
Yazaki EDS Design Guideline Rev 3 
 
35
 
 
 
2.3.4.2 
Environments: Wet vs. Dry 
Typical wet areas include the engine compartment, wheel wells, underbody and door 
panels. Dry areas include instrument panels, head liners and other areas inside the 
vehicle that are not in contact with the floor carpeting. In some cases such as off road 
vehicles all harnesses are considered to be in a wet area. It is important to understand 
customer and vehicle requirements regarding the use of sealed vs. unsealed 
connection systems. 
 
Automotive sealed connectors are required to initially withstand 48KPa (7psi) of 
pressure and vacuum and are required to maintain 28KPa (4psi) at the end of its life. A 
sealed connector located in an area such as under a wheel well may leak and fail if 
exposed to a splash or a direct stream of water. The best solution is to move the 
connection out of the hostile environment. If this is not possible, consider using a 
splash shield or a boot over the connector to direct the water away from the connector. 
While these alternatives add cost they can help avoid warranty and customer 
satisfaction issues. 
 
The single largest issue leading to connector sealing failures is the connector mounting 
position. When a sealed connector is mounted with the wires pointing upwards out of 
the connector, water (salt water) follows the wires to the back of the connector and 
accumulates on the seal where movement due to vibration and temperature change 
causes seals to leak. The solution is to avoid mounting either direct connects or in-line 
sealed connectors with the circuits in a vertical position. In a horizontal mounting 
position drip, loops should be used to divert water from the cable sealing area of the 
connection.  
 
Examples of wet areas requiring sealed connections: 
• Engine compartment  


### 第 37 页
Yazaki EDS Design Guideline Rev 3 
 
36
• Most connections in the engine compartment are required to be sealed. There 
are exceptions such as relay and fuse connections but the general rule is to use 
sealed connections in the engine compartment.  
• Underbody 
• High splash as well as areas that may be subject to submersion requires sealed 
connections. Shields may be required in addition to sealed connectors if 
mounted in severe splash locations.  
• Inside door panels 
• Inside doors is considered a less severe wet location. Sealing requirements 
inside doors and lift gates should be determined on an application or customer 
requirement basis.   
• Foot well area 
• Wet shoes, boots, and snow on the bottom of the shoes or driving rain getting 
into the vehicle when the doors, windows or sun roofs are accidentally left open 
can create wet, in otherwise dry areas. In cold climates it is possible for the floor 
carpeting to stay wet all winter. Connectors in contact with carpeting or low 
mounted upholstering should be evaluated for sealing 
• Trunk well areas 
• Conditions such as; spilled grocery bottles, wet boots, leaving the trunk open 
while hauling a large load or wet cargo; may require the use of sealed 
connectors in the trunk. 
• Console area 
• Spilled soft drinks or coffee can damage electrical connections. Connectors 
located in the console area or near drink holders may need to be sealed.  
• On off-road vehicles, the manufacturer can assume the customer will wash out 
the vehicle interior with a garden hose or a coin operated car wash.  Connectors 
located in these interiors will need to be sealed.  
 
2.3.4.3 
Pin-out (circuit location) Considerations 
 
The location of a circuit in a connector can have an effect on the function and long 
term reliability of a connection. Device connector pin-outs (circuit location) are defined 
by the device transmittal or schematic provided by the customer. In line connectors 
(see B.5) may allow some choices as to circuit location within the connector cavity field.   
• Where possible, current carrying circuits should be separated and placed in the 
outside cavities to reduce heat build-up.  
• Power and ground circuits should be separated to reduce heat build up and the 
chance of short circuits. 


### 第 38 页
Yazaki EDS Design Guideline Rev 3 
 
37
• Signal circuits can have a heat sinking effect and reduce overall temperature 
rise on power circuits when located in adjacent cavities.   
• Circuits with contrasting wire colors for use in adjacent cavities will reduce the 
likely- hood of circuits being installed in an incorrect connector cavity during the 
harness assembly process as well as in rework or service operations.   
 
 
 
2.3.5 
Connector System Failures 
Often the occurrence of the most common types of connection failures (discussed 
below) can be reduced by selecting durable and ergonomically friendly connector 
systems. 
 
2.3.5.1 
Not Connected – L.N.A. (loose not attached)  
Loose not attached (a.k.a. - LNA) are connection operations that are missed in the 
vehicle assembly process.  Consideration to harness design characteristics can reduce 
the occurrence of LNA due to missed operation failures in the vehicle assembly plant. 
Connectors that are difficult to mate will be missed more often than those that are easy 
to mate. Following are some points to be considered when designing a wire harness:  
• Accessibility to the connection may cause an L.N.A. type of failure. Connections 
requiring blind or ergonomically difficult operations should be avoided.  
• Connectors with high mating forces (hard to mate) are more likely to be missed 
than connections that are easier to mate.   
• Connectors with tactile and audible feedback can help the operator assure the 
connection has been properly mated.  
• Mounting in-line connectors with a clip or in a bracket can help or hinder the 
mating operation and must be evaluated on an application basis. Stable 
brackets or robust clips can aid the connector mating process. Unstable 


### 第 39 页
Yazaki EDS Design Guideline Rev 3 
 
38
mounting provisions can make it difficult for an operator to fully mate a 
connector.   
• Light colored connectors (natural, light gray, white, etc.) are easier to see in 
dark areas and can be used to make a connection operation easier to perform. 
Being able to see the connection can help reduce the occurrence of missed 
connection conditions in the vehicle assembly plant.     
 
******* 
Loose Connection 
These are partially mated connections that can function intermittently and lead to 
warranty claims, customer satisfaction and safety issues.  To avoid these types of 
failures the following should considered: 
• Select connectors that give both audible (ex. a click sound) and tactile (sensitive 
to the touch) feedback to the operator indicating the connection has been 
completely mated.  
• Select low insertion force connectors. Connectors with high mating force can be 
difficult for the operator in the vehicle assembly plant to fully mate. 
• Connectors with CPAs (Connector Position Assurance devices) may be 
selected to assure a connection fully mated and latched when a wire harness is 
installed in the vehicle assembly plant. CPAs cannot be pushed into their final 
position until the connector halves are mated and latched. Seating a CPA is a 
secondary operation that requires the vehicle plant operator to fully mate the 
connection then push the CPA into its final position.   
• Double attaching point clips or robust bracket mounts can hold connectors in a 
stable position and allow them to be mated after half of the connector is 
mounted in the vehicle. An inline connector mounted with a single point clip or in 
an unstable bracket may be easier to mate before the connector is attached to 
its mounting provision.  
 
******* 
Terminal Push Out (TPO) 
Terminals can be pushed out of position in their cavity when the connector halves are 
mated or when a connector is mated to a direct connect device. These TPOs cause 
intermittent or total failure of the electrical connection.  A TPO can be caused by a 
terminal stubbing on its mating terminal, by a terminal that was not properly seated 
(locked) into the connector during the harness assembly process or excess tensile 
force being applied to the circuit.  
Terminal stubbing can be the result of bent terminals or improper wire dress applying a 
side bias to the terminal alignment. Some connector/terminal designs offer audible and 
tactile feedback to assist the harness assembly operator in determining that the 
terminal is properly locked into its cavity. The way circuits are dressed at the back of a 
connector can have an affect on the normal float and terminal alignment within its 


### 第 40 页
Yazaki EDS Design Guideline Rev 3 
 
39
cavity. Some connector/terminal combinations have higher terminal to connector 
retention than others.  
• Select only connector systems with adequate terminal retention.  
• Select connector systems with tactile and audible terminal retention feedback.  
• The wire dress at the back of the connector should not bias the terminals in one 
direction or interfere with the normal terminal float within the connector cavity.  
 
Taping too close to the connector can restrict the normal float required to allow 
terminals to self align during the connector mating process. This lack of float can cause 
a terminal stubbing condition and result in a TPO.  In the case of sealed connectors 
taping too close to the back of the connector can side load the cable seals and cause 
the connector to leak. Taping too close to the insulator can also hide a partially seated 
terminal.  A good general rule for the tape hold back distance behind a connector is the 
distance between the terminals furthest apart in the connector. For example, if the 
distance between the opposite corners cavities in an unsealed 2X4, 8 position 
connector is 40 mm then the tape hold back distance would be 40mm minimum. Loose 
coverings such as convoluted tubing or loom can be used if the appearance of un-
covered wire is unacceptable.   
 
• When tape is used as a harness covering it must be held away from the back of 
the connectors. 
 
Connector systems are available with secondary terminal retention features 
incorporated into their designs. These features are called spacers, wedges TPAs or 
PLRs.  Some wedges or spacers only provide Primary Lock Reinforcement (PLR) 
function and will not correct the terminal position if it is not fully seated nor will they 
retain a terminal if the primary lock is damaged. TPA type wedges will correct and seat 
partially installed terminals and will retain a terminal in the proper position even if the 
primary lock fails.    
 
• Select connector systems that have Terminal Position Assurance (TPA) 
features. 
 
A terminal can shear its lock provision in the connector and result in a TPO if excessive 
force is applied to a circuit. This can happen if a circuit is snagged or the harness is 
lifted or handled by the wires. This is often a vehicle assembly plant problem.  
 


### 第 41 页
Yazaki EDS Design Guideline Rev 3 
 
40
• Special packaging instructions may have to be shown on the wire harness 
drawing to reduce the possibility of a 1 or 2 wire harness takeout being used for 
a handle.  
 
2.3.6 
Connector Selection Considerations � 
 
• �Choose connectors that have a tactile feel and an audible sound when the 
connector is properly and completely mated.  
• �Avoid connectors requiring excessive mating efforts.  
• �Connectors must be selected that will fit in the allocated space and pass 
through and openings in the vehicle body during the assembly process.   
• �When selecting connectors with CPA’s, be sure there is enough clearance for 
the operator to seat the CPA.   
• Select connectors with different indexes when cross-connection can occur. 
• Select connectors meet the application requirements such as chemical 
resistance, current capacity, temperature resistance, etc.  
 
 
 
2.3.6.1 
Design Considerations for Sealed Connectors 
Cross linked wire insulation such as SAE TXL (SAE-J1128) should be selected for 
general use in sealed connectors. In engine compartments, temperatures can exceed 
125°C. For extreme applications (> 125°C) consider wire rated to 150°C or beyond. 
PVC insulation (SAE TWP (SAE-J1128)) should be used in sealed connectors when 
located in areas that will not exceed 85°C. 
                                            
� In design review checklist. 


### 第 42 页
Yazaki EDS Design Guideline Rev 3 
 
41
Select a wire insulation type that will meet the application requirements over the entire 
installed length. Example: A circuit that runs through a hot spot should be shielded to 
reduce the temperature in that location or be upgraded to a high temperature insulation 
material.  
 
• Sealed connectors sometimes have unused cavities. All cavities that do not 
require a circuit must be plugged. These can be plugged with individual plugs, 
dummy circuits (wires) or for specific applications, the connectors can be 
produced by the supplier with plugs molded in place. When using dummy 
circuits it is important to seal the loose end of the wire to prevent moisture from 
wicking through the wire into the connector.  
 
When using tape to cover the circuits going into a sealed connector, the tape must be 
held away from the back of the connector so as to avoid placing a side load where the 
seal and wire make contact. Side loading a seal can create a leak path that can cause 
a connection to fail. A good rule of thumb is to avoid taping any closer to the back of 
the connector than the distance measured between the circuits furthest apart in the 
connector.  
 
 
 
2.3.7 
Terms and Definitions 
CPA - Connector Position Assurance device (Cannot be seated until the connector is 
fully mated) 
TPA - Terminal Position Assurance device (Will seat partially seated terminals) 
PLR - Primary Lock Reinforcement (Backs up the primary terminal lock). May detect 
but not correct partially seated terminals.  


### 第 43 页
Yazaki EDS Design Guideline Rev 3 
 
42
Wedge – Can be a TPA or PLR 
Spacer – Can be a TPA or PLR 
 
2.3.8 
Terminals 
Terminals used in high volume applications, typically installed in connector cavities, 
provide a separable electrical interface between wire harnesses or between a harness 
and a device. Automotive terminals used in high volume applications are crimped on 
wire to create an electrical path for a power or signal function. These crimps are 
performed on high speed machines that cut and strip insulated wire then crimp the 
terminals to the circuit in an automated process. Terminals can also be welded to wire 
in some applications. Other lower volume terminals like eyelet and battery terminals 
are processed on bench presses in semi-automated processes.  
 
******* 
Terminal Anatomy 
 
 
 
******* 
Materials 
Terminals are typically made of base materials such as brass or copper alloys. 
Plating/coatings such as pure tin and tin alloys are used for corrosion protection.  
Noble metals such as Gold, Platinum, and Palladium plating can be selectively applied 
to the contact surfaces and specified for low voltage/low current interfaces that have 
stringent reliability requirements (ex. air bag circuits). Gold can also be specified for 
high temperature applications. Silver can be specified on high voltage and high current 
applications such as fuse and relay terminals. Silver is not a noble metal and can react 
with environmental contaminants and is not suitable for some signal circuit applications.  


### 第 44 页
Yazaki EDS Design Guideline Rev 3 
 
43
• Gold contact terminals should only be mated with gold contact terminals.  
• Silver contact terminals can be mated with tin contact terminals.  
 
******* 
Terminal Types 
There are several types of terminals used in automotive electrical interfaces. These 
include: 
• Box and blade 
• Pin and socket (Pin Male and Socket or Barrel Female) 
• Eyelet terminals 
 
Terminals can be one piece designs or have multiple parts in an assembly. Common 
types of multiple piece terminal designs have contact springs (Beams) made of 
different material than the terminal body. Another common multiple piece terminal has 
a hood installed over the contact spring. There are also some terminals designs that 
use stainless steel or Beryllium copper beams to back up the electrical contact beams.   
Box (Female) and Blade (Male)  
These are commonly used over a broad current range.   
 
Socket or Barrel (Female) and Pin (Male) 
Commonly used for low current applications. Pin and socket terminal designs can 
also be used for high current applications such as battery disconnects.  
 
FPC and Edge Board Terminal 
(Usually female) - Used to connect to flex circuit or individual wires to PCB boards.  
 
Eyelet Terminals 
Eyelet terminals can be designed to handle a range of low current signal circuits up 
to high current applications.  Common applications include grounds, starters and 
alternators.  
 


### 第 45 页
Yazaki EDS Design Guideline Rev 3 
 
44
******* 
Terminal Selection 
Terminals are selected by their current carrying capability and packaging requirements. 
Often a device will define the terminal and contact plating type.  In-line connections 
should be selected based on current load, environmental conditions, cost and in-
vehicle packaging requirements.  The grip size available for a specific terminal may 
affect the terminal selection based on the wire size requirements of the intended 
application.   
 
In many cases, there are alternative terminals available within a single terminal family. 
For example, there may be terminals available in various plating and current capacity 
options that are designed to fit the same connector cavity.   
 
Terminal to terminal insertion efforts have an affect on the number of circuits used in a 
connector. Most connectors are required to have a < 75N mating force.  
 
******* 
Terminal Failures  
3) Crimp Failure 
When a terminal is crimped to a wire, the integrity of the crimp affects the 
performance of the terminal. A crimp has 2 basic functions. The first is to 
mechanically attach a terminal to a wire, the second to provide an electrical path.  A 
crimp that is too loose (not enough wire strand compression) results in high 
resistance and high voltage drop across the circuit, plus poor mechanical strength 
that can allow the terminal to pull off the wire. A crimp that is over compressed 
results in extruded and broken wire strands. This condition also increases circuit 
resistance and voltage drop and again results in poor mechanical strength.   
4) Corrosion 
Corrosion can cause electrical failures due to excessive resistance at the terminal 
interface. If the corrosion is severe it can also result in mechanical failures. There 
are different types of corrosion common in automotive electrical systems: 
5) Fretting Corrosion 
Micro motion caused by temperature changes and vibration is commonly found in 
automotive terminal systems. Fretting corrosion is caused by micro motion in a 
terminal interface that breaks up the oxides which form on metal surfaces. These 
oxides accumulate at the ends of this motion excursion and (in time) will build up a 
resistive film that causes resistance to rise and the interface to fail.  This is a greater 
problem in signal circuits because they do not have enough voltage and/or current 
to burn through resistive films.  Fretting corrosion is not an issue on power circuits 
with significant voltage and/or current.  Selecting Gold contacts, greased female 


### 第 46 页
Yazaki EDS Design Guideline Rev 3 
 
45
terminals or high normal force terminals can reduce the occurrence of fretting 
corrosion. 
 
(Gold doesn’t oxidize, grease keeps oxygen off the exposed surface and high 
normal force terminals can reduce micro motion)  
6) Corrosion Due to Chemical Reaction  
Sealed connectors are designed to keep water, salt water and other chemicals away 
from electrical connections. Connector leaks are caused by poor mounting locations, 
connector or seal damage, wire insulation damage, miss-application (as in a wire 
diameter being too small for the selected seal), etc. These leaks can result in 
contamination of a terminal interface and create a chemical reaction with terminal 
plating and base metals. These reactions can destroy even the most durable 
terminal interfaces. Eyelet terminals that are exposed to severe environmental 
conditions are often soldered to prevent corrosion.  
7) Spread Terminal Contacts  
Spread contacts in the female terminal can cause an intermittent or a totally 
inoperative condition in the circuit. These spread contacts can be identified by the 
blade or pin having little or no force applied on the contact surface by the female 
terminal. There are several areas where a female terminal can be damaged: 
8) Terminal production stamping process 
Improperly set up progressive dies can cause the beam in the female contacts to be 
miss-formed and result in a poor electrical connection. 
9) Harness manufacturing process  
The electrical check process can damage the contacts in a female terminal if a 
check pin pushed into the terminal. This condition can be caused by bent, broken or 
worn out check pins. Incorrect check pin design or size can also damage terminals.   
10) Vehicle assembly process 
Terminals can be damaged in the vehicle assembly process by improper connector 
alignment or miss-mating with the wrong connector. The in plant diagnostic and 
repair process can also be the source of spread terminal type damage.  
11) Diagnostic or service operations 
A repairman in the plant, dealership, or service garage can damage terminals by 
sticking picks, paperclips, or other things into a terminal when performing electrical 
checks.  
12) Burned Contacts 


### 第 47 页
Yazaki EDS Design Guideline Rev 3 
 
46
Burned contacts are typically found after a vehicle has been in service for a period 
of time. This type of contact failure can be caused by: 
13) Terminal not capable of carrying the intended current load. 
Proper terminal selection is a critical part of good harness design.   
14) Wire size is too small. 
Larger wire will sink heat away a terminal and cause it to run cooler and last longer.  
15) Spread terminal contacts. 
Damaged contacts can cause a terminal to overheat and burn. 
16) Device is drawing a higher than specified current load. 
Motors can draw current loads higher than specified due to partially locked 
armatures. These over-loads can cause the terminal interface to overheat and burn.  
17) Vibration or sliding motion on terminal contacts, under load, can cause the 
contact to arc and burn. 
Any motion with a terminal under load will shorten the normal life of a mated 
terminal pair. This motion can be due to unstable mounting conditions or can be 
induced into a connection through forced motion like the tilting of a steering column 
or the movement of a seat track.   
 
******* 
Special Considerations 
Terminal manufacturers should package their products so that dust and dirt cannot 
affect the terminal contacts during the shipping and handling process.   
 
�Contact plating material on the male and female interface contact surfaces must be 
compatible with each other. Using dissimilar contact plating materials may cause 
unacceptable interface resistance due to the reaction between the different surface 
coatings.  
 
******* 
Terms and Definitions 
Contact Spring (Contact Beam)  
The part of the terminal that applies force to the contact surface. 
                                            
� In design review checklist. 


### 第 48 页
Yazaki EDS Design Guideline Rev 3 
 
47
Contact surface 
The area on the male and female terminals that provides the electrical path. 
Grip 
The grip is the area where the wire conductor strands are mechanically and 
electrically attached to the terminal and where the wire insulation or cable seals are 
also attached. Note: The Grip is part of the terminal; the crimp is the process of 
applying the terminal to the wire. 
Crimp 
The Crimp is the manufacturing process of attaching the wire conductor core and 
insulation or cable seal to the terminal. 
Serrations 
 Serrations are often designed into terminal conductor grips to improve the 
mechanical tensile strength of the crimp. 
 
2.3.9 
Connector Materials 
 
Connector materials, commonly called resins or mold compounds, have specific 
characteristics such as resistance to chemicals, temperature resistance, cost, 
dimensional stability, flexibility, etc. that make them suitable for specific applications. 
These materials are selected at the time the connector is being developed. Due to 
shrinkage or other process variables materials are difficult to change once the part has 
been tooled and is in production.  
 
Materials commonly used to produce connectors include: 
• Nylon  (Polyamide)  
• PBT (Polyester) 
• Acetyl (POM) 
• Noryl (PPO) 
• PVC1 
1 PVC mold compound is limited to over-molded connectors.  
 
 


### 第 49 页
Yazaki EDS Design Guideline Rev 3 
 
48
******* 
Other Materials 
 
Other materials like Polypropylene or Polyethylene are commonly used for covers or 
wire dress features where dimensional stability is not critical but where cost is a factor.   
 
******* 
Additives 
 
Additives can be incorporated into base resin to change a connector’s performance or 
appearance characteristics. These materials are typically added to the compound by 
the material supplier before shipping to the molder.  
 
Impact Modifiers 
Modifiers can be added to plastic compounds (resins) to change their performance 
characteristics. As an example, resins with impact modifiers added to a base 
compound  can be selected during the connector development process to improve a 
connector’s to durability over similar designs made from unmodified material.   
 
Reinforcement Fillers 
Glass Fiber can be added to a resin when it is compounded by the material supplier 
prior to the injection molding process. This can provide additional dimensional stability 
in the connector and increase its high temperature performance.  
 
Colorant 
Color can added to the base resin or in some cases mixed in the injection molding 
process. Color is typically used to provide a visual aid for distinguishing one connector 
key or index option from another.  As an example, a connector of one color should only 
mate with a connector of the same color.  
 
2.4 Clips 
 
Clips are used for bundling wires, and securing wiring harnesses and insulators. Clips 
must be firmly mounted and withstand the rigors of their environment. Clip mounting 
locations and methods vary by their application such as the vehicle sheet metal body, 


### 第 50 页
Yazaki EDS Design Guideline Rev 3 
 
49
frame, bracket, instrument panel, etc. Clips also maintain a defined engineered routing 
path to prevent harness damage and reduce BSR (buzz, squeak and rattle). 
 
2.4.1 
Clip Anatomy 
 
 
2.4.2 
Materials 
• Polypropylene (PP) - Temperature rating 105°C and recommended for use in 
the passenger compartment. 
• Nylon (PA) - Temperature rating 125°C and generally recommended for use in 
the entire vehicle including both the engine and passenger compartments. 
• Steel – Maximum continuous rating exceeds 250°C 
 
2.4.3 
General Performance Requirements  
• Clip insertion force: Not to exceed 10 lbs for manual installation. 
• Clip extraction/removal force: Minimum 50 lbs from mounted panel.  
• Loop tensile strength of Strap clip:  Minimum 50 lbs. 
• Sliding force: Minimum 12.5 lbs after installing clip to wiring bundle. 
 


### 第 51 页
Yazaki EDS Design Guideline Rev 3 
 
50
2.4.4 
Attachment Method to Harness 
******* 
Tape-on Clips 
 
 
Tape-on clips are commonly used attachment devices with good retention capabilities. 
Varieties include the “arrowhead”, and the “Christmas tree”.  The arrowhead design 
provides a go/no-go condition in which the attachment head is either installed fully or it 
is not providing any retention.  The Christmas tree retainers have a series of ribs on 
the attachment head allowing for full or a partial insertion of the clip.  This is easier for 
the operator, but may allow for a partial attachment.  Additionally, these clips can be 
made one-sided (i.e., requiring only one tape tie to the harness) and two-sided 
(requiring two). Optimum performance of tape-on clips requires properly specified tape 
for the environmental conditions. 
 
Positive Traits 
• Easy to process and apply 
• Easy to adjust the orientation and position 
• Available in a wide variety of styles and types 
 
Negative Traits 
• Slow wire harness assembly due to manual tape application of the clip 
• Inconsistency of manual tape application  
• Reworking is hard because tape removing is difficult. 
• The tape ties can loosen and fall off the clip when exposed to the elements 
(solvent, salt or water) 
 
 


### 第 52 页
Yazaki EDS Design Guideline Rev 3 
 
51
******* 
Tie Strap Clips 
 
This clip style eliminates the need for tape ties to hold down the clip. The tie strap, with 
its lock, provides a strong harness attachment point.  Due to the nature of the narrow 
strap and the positioning teeth under the clip, there is increased stress on the wire 
bundle when pulled.  Due to the stress condition, this type of strap is not 
recommended in areas where the harness is subjected to vibration, such as the engine 
block or transmission.  Over time, damage may occur to the wire insulation due to the 
vibration.  If this condition exists, the use of tubing, or pre-tape, should be used on the 
wire bundle before applying the tie-strap to reduce the possibility insulation damage. 
 
Positive Traits 
Increased wiring harness flexibility 
Consistent tension setting when using an automatic gun applicator 
Fast and easy application 
Easy removal for reworking 
 
Negative Traits 
Difficult to adjust the orientation as required on the drawing  
Possible damage to wire insulation without tubing or pre-tape under vibration 
conditions 
 
******* 
Double Tie Straps 
 
 


### 第 53 页
Yazaki EDS Design Guideline Rev 3 
 
52
This type of strap is used to secure one bundle to another during installation of the 
harnesses into the vehicle. 
 
******* 
Convolute Clips 
Convolute clips come in a variety of sizes with both arrowhead and Christmas tree 
configurations.  They can only be installed on convolute between the ridges to provide 
proper dimensional characteristics.  However, they do not provide orientation in 
relation to other takeouts (the clip can rotate around the ridge). This is good when this 
kind of flexibility is needed during harness installation such as across a dash 
panel .The operator needs only to rotate the clip and insert rather than twist the 
harness into place.  If the clip is asymmetrical, a section view will need to be included 
on the harness drawing to ensure consistent application. 
 
 
 
Positive Traits 
• Can freely rotate 360 degrees during and after installation 
• Quick and easy snap closure for harness assembly 
• Clean engineered look  
 
Negative Traits 
• Generally higher price 
• Each size clip only fits certain sizes of convolute, more unique parts required  
• to cover all sizes of convolute 
 


### 第 54 页
Yazaki EDS Design Guideline Rev 3 
 
53
******* 
Connector Clips 
 
 
These clips have sliding features to attach to a connector. 
 
******* 
Hook and Loop (“Velcro”) 
 
 
These clips overlap and Hook-and-Loop to harness for easy release and reuse. 
 
Positive Traits 
• Very easy to release and reuse 
• Good for applications where over-tightening is a concern 
• Doesn’t pinch or abrade wires 
 
Negative Traits 
• Hook & Loop closure may inadvertently open during handling of the harness  


### 第 55 页
Yazaki EDS Design Guideline Rev 3 
 
54
• Relatively high cost 
 
2.4.5 
Mounting Methods to Vehicle 
******* 
Hole Mounts 
 
 
This type of clip requires a sheet metal or trim provision to secure the clip. Be careful to 
look for the recommended hole size and thickness from the component drawing for the 
clip.  Also note that there must be clearance on the surface beyond the hole.  This is 
so the fully inserted clip cannot come in contact with objects behind the hole.  
 
Typical applications 
Wire harness bundles and takeouts to sheet metal panels, trim panels, and brackets.  
 
Positive Traits 
• Easy to mount  
• Provides high retention force 
 
Negative Traits 
• Requires full head insertion to mount securely 
 


### 第 56 页
Yazaki EDS Design Guideline Rev 3 
 
55
******* 
Stud Mounts 
 
 
Requires a threaded stud to accept the clip locking mechanism  
 
Typical application 
M5, T5, M6, M8 studs 
 
******* 
Screw/Bolt Mount Clips 
Positive Traits 
• Easy to remove and remount clip 
 
Negative Traits 
• Extra tool required to mount (screw/bolt down) 
• Clip can be twisted during the screw/bolt operation 
 
******* 
Edge Mounts (Edge biters) 
 


### 第 57 页
Yazaki EDS Design Guideline Rev 3 
 
56
 
Used on an edge condition where the metal bite portion of the clip can make a positive 
fit.  This type of clip has no positive dimensional quality.  It can be placed anywhere 
along the edge of the sheet metal unlike the previously mentioned clip devices. 
 
Typical applications 
Along an edge where two panels of sheet metal are welded together.  
 
Positive Traits 
• No provisions required 
 
Negative Traits 
• More costly when compared to hole and stud mount clips 
• Bite portion of clip can cause corrosion 
 
2.4.6 
Selection Preference  
 
Classification 
Current 
Application 
Preference 
Comments
Tape-on clip 
Tie Strap clip 
Convolute clip 
Tie strap clip 
Attachment 
method to 
harness 
Connector clip 
Connector 
clip 
 
Stud mount 
Stud Mount 
 
Hole mount 
 
 
Attachment method 
to vehicle 
Screw/Bolt 
Hole mount 
 


### 第 58 页
Yazaki EDS Design Guideline Rev 3 
 
57
mount 
Edge mount 
 
Avoid Use 
Applicable 
panel hole 
size 
Various: 
6.35/6.6/7.0mm
7mm 
 
Applicable 
stud size 
Various 
M5 
 
Color 
Various 
Dark Gray 
 
Polypropylene 
Polypropylene
Below 
105° C 
Nylon 
Nylon 
Below 
125° C 
Material 
Steel 
Steel 
Over 150° 
C 
Bundling 
wires 
Taping 
Tie strap 
w/auto tool 
 
 
Considerations for Clip Selection 
• Attachment method to the harness 
• Attachment method to the vehicle body 
• Temperature of the surrounding environment 
• Diameter of the mating panel hole 
• Mating panel thickness 
• Screw/bolt size that will be used to mount clip 
• Threaded stud size 
• Color of clip 
• Harness bundle size 
• Possible offset (and distance) from center of bundle 
Holding features needed for mounting 


### 第 59 页
Yazaki EDS Design Guideline Rev 3 
 
58
2.5 Automated Band Clamp 
To improve the speed and efficiency of applying tie strap to the wire harness, an 
automated process is developed; for example, the HellermannTyton band clamp 
system. The HellermannTyton Band Clamp is an automatically applied tie strap for 
bundling the wire harness.  It consists of an electrically driven applicator (heretofore 
referred to as, “gun”) which automatically sequences, applies, cuts, and disposes of 
the tie strap.  It is used in place of spot tapes and spiral wraps to keep wires together 
throughout the wire harness bundle.  HellermannTyton Band Clamp is application 
specific, so each application needs to be verified. There are other automated band 
clamps supplier, HellermannTyton is used because it is our current supplier. 
 
2.5.1 
Materials and Components 
Typically, there are different sizes for an automated band clamp system. For example, 
there are two sizes of tie straps capable of use from HellermannTyton.  The first tie 
straps size category can clamp a single wire as small as 22AWG or a bundle diameter 
as large as 19mm.  The second tie strap category can clamp a single wire as small as 
22AWG or a bundle diameter as large as 32mm. 
Each size category consists of black and natural colored tie straps which are capable 
of use in continuous operating temperatures of 125°C.   
• Black: Nylon 6/6 Heat Stabilized 
• Natural: Nylon 6/6 
 
In addition, there are two guns which are used to apply the different sized band clamps. 
• AT2000 applies the 19mm strap. 
• AT2060 applies the 32mm strap. 
 
 


### 第 60 页
Yazaki EDS Design Guideline Rev 3 
 
59
 
 
2.5.2 
Application 
The HellermannTyton Band Clamp should be applied according to the following 
guideline: 
 


### 第 61 页
Yazaki EDS Design Guideline Rev 3 
 
60
 


### 第 62 页
Yazaki EDS Design Guideline Rev 3 
 
61
Application Advantage 
Positives 
• Decrease in labor time. 
• Increases harness flexibility. 
• Contributes less weight to harness than tape. 
• More repeatable harness construction than tape. 
• Less strenuous for operator. 
• Halogen Free. 
• Easy to rework. 
 
Negatives 
• Incurs capital costs. 
• Guns should have at least a 50% rate of backup guns for maintenance or down-
time.   
• Application specific. 
• Maintenance required. 
 


### 第 63 页
Yazaki EDS Design Guideline Rev 3 
 
62
2.6 Protective Coverings 
 
2.6.1 
Tape 
Tapes are generally used for bundling, noise suppression, identification by print data 
and/or color, tear (hanking), clip retention, and packaging.  PVC tapes are mainly used 
for bundling and appearance purposes.  Foam and felt tapes are utilized for noise 
suppression of both wiring bundles and connectors.  Cloth and coated cloth tapes 
which also provide some degree of noise suppression are used for bundling as well as 
for clip retention.  Adhesive (vinyl, cloth and coated cloth) tapes are used for straight, 
end, T, X, and P wraps for various types of coverings such as convolute, woven and 
braided sleeve, and scroll.  They can also be used as the means of securing various 
components (e.g., clips) to the wiring harness itself.  Embossed tapes have a higher 
degree of noise suppression characteristic than the non embossed versions of the 
same product.  Paper and notched vinyl tapes are used as tear (hanking) tape to be 
torn at the assembly plants.  Tear tapes are used to secure takeouts during assembly 
or on takeouts that are not used 100% of the time so that the assembly plant does not 
have to secure them.  Dry vinyl tapes are used for normal bundling and for packaging 
to hold the wiring harness together for shipment to the assembly plant. 
 
******* 
Materials 
Typical materials for automotive tapes are: 
• Cellulose/Paper 
• Polyvinyl Chloride (PVC)  
• Cloth 
• Coated Cloth 
• Polyester  
• Polyurethane 
• Fiberglass 
 
******* 
Tape types 
• Adhesive/ Non Adhesive 
• Embossed / Non-embossed 
• Anti-Rattle and Anti-Squeak 
• Notched 
 


### 第 64 页
Yazaki EDS Design Guideline Rev 3 
 
63
******* 
Tape selection 
Tape selection is based on factors such as environmental temperature rating, 
mechanical handling requirement, harness installation at the vehicle assembly plant, 
BSR (buzz, squeak and rattle) issues, flame retarding requirements, clip retention, 
flexibility with continuous wrap, abrasion resistance, pinch resistance and fluid 
resistance.  See Wiring Harness Tape Chart. 
 
******* 
Performance Standard  
Refer to ESA Component Application Strategy for respective tape application and 
YPES. 
 
2.6.2 
Sleeve Tubing (braided, woven, knitted) 
The purpose of sleeve tubing includes protection from abrasion, pinching, noise (BSR 
– Buzz, Squeak, and Rattle) prevention, and heat in limited cases.  Sleeves can be 
braided, woven, or knitted using mono or multi-filaments.    
Braided sleeves have an ability to expand up to 5 times their diameter depending on 
the tightness of the weave.  This ability greatly eases the installation process for 
manufacturing.   The expansion ratio increases as the tightness of the weave 
decreases.   
Several sleeves are open on one side with various types of closure available such as 
self wrapping feature, hook and loop closure or adhesive.  These “wrap around” 
sleeves are used where it is difficult to use a “feed through” sleeve type. 
 
2.6.2.1 
Sleeve Tubing Anatomy 
 


### 第 65 页
Yazaki EDS Design Guideline Rev 3 
 
64
 
 
 
2.6.2.2 
Materials 
Filament materials: 
• Polyester (PBT) 
• Nylon (PA) 
• Polyetheretherketone (PEEK) 
• Polyphenylene Sulfide (PPS) 
• Fiberglass 
• Silica 
 
2.6.2.3 
Coatings/Covering 
Coating materials: 
• Acrylic 
• Silicon Rubber 
• Foil 


### 第 66 页
Yazaki EDS Design Guideline Rev 3 
 
65
 
2.6.2.4 
Sleeve Selection 
Sleeve Selection is based on application requirements such as: temperature, abrasion, 
fluid resistance, cold flexibility, and BSR. See the YNA Coverings Strategy. 
Please refer to the ESA Web site for the latest application and strategy chart  
 
2.6.3 
Heat transfer and reflective properties of reflective coverings. 
Heat transfer will inevitably happened if there is a temperature gradient (difference) 
between two or more object/regions in space according to the equation below.    
)
(
z
T
y
T
x
T
k
q
∂
+ ∂
∂
+ ∂
∂
∂
= −
 or  
k T
q
− Δ
=
r
 
Where q-heat flux (flow of heat per unit area).  The minus sign of the equation signifies 
that heat will always flow to the lower temperature area.  If there is no temperature 
gradient (
∂ ∂ = ∂ ∂ = ∂ ∂ = 0
T z
T y
T x
), than q=0 and heat will not transfer. 
Understanding that heat transfer will happen if there is a temperature gradient, now we 
must consider how the transfer will take place.  There are three ways how heat could 
transfer (go from one place to another) and whichever is fastest will do most of the 
work.  For example, imagine the water bowl with three holes on the bottom - one large 
and two smaller ones, most of the water will go through the larger hole, same will 
happens with the heat transfer, fastest process will transfer majority of the heat. 
The three ways of heat transfer are conduction and convection (both require the 
media - solid in case of conduction and liquid/gas in case of convection) and radiation. 
Conduction and convection deal with atoms/molecules hitting each other and 
transferring heat by vibrating.  
Conduction could be represented by a crowd of people standing in the subway car, 
and if one passenger hits the other the momentum is carried on to the other one, and 
one by one to the rest of people on board.   
Convection, since the atoms are at much smaller distanced between each other, 
could be represented by the relay of sprinters transferring the baton from one to 
another.  
Radiation does not require any media, this is for example how electromagnetic waves 
(EM) travel to Earth from the sun, there is no media in space to support the conduction 
and convection, but EM waves reach Earth very quickly traveling with the speed of light.  


### 第 67 页
Yazaki EDS Design Guideline Rev 3 
 
66
 
Figure 1.Electro-magnetic wave.  Ref. http://en.wikipedia.org/wiki/Electromagnetic_radiatio 
 
Radiation heat transfer takes place via the 0.75 to 1000 micron long electromagnetic 
waves, called infrared (IR) or visible range waves which are 400-700nm long.  An 
important fact is that these frequencies of EM waves coincide with the self (resonance) 
vibration frequencies of the atoms or molecules of different materials be it a solid, liquid 
or a gas.  
 
Figure 2 Electromagnetic spectrum.  Ref.  http://en.wikipedia.org/wiki/Electromagnetic_spectrum 
 
So when wave hits the solid, for example, atoms start to vibrate and hit each other, 
and solid starts to heat up, by the way also radiating EM waves (mostly in IR regime).  
Another important phenomenon is that waves could be reflected if they collide with the 
matter.  Materials can reflect waves and waves could only go down in the bulk of the 
material to the distance δ  delta, called skin depth.   
Skin depth depends on the resistivity of the material.  The lower the resistivity, the 
smaller the distance.  Metals have high conductivity hence low resistivity, which is why 


### 第 68 页
Yazaki EDS Design Guideline Rev 3 
 
67
skin depth for metals is relatively small in comparison to non-conductive materials.  
Reflective coverings, made of conductive materials, have low skin depth, so waves can 
only penetrate small distance in the bulk and excite small number of atoms.  On the 
other hand metals have high conductivity and heat travels through the metal much 
faster and further in comparison to the non- or semi-conductor. This is due to the 
atomic structure of the metals which is not going to be described here.  As we 
described here heat transfer could take place various number of ways. 
As we described here heat transfer could take place various number of ways.  
Reflective coverings can only protect from the radiant heat transfer. It is the best to do 
an on-vehicle evaluation by placing the thermocouples inside and outside the reflective 
covering in order to measure its effectiveness.   
 
2.6.4 
Convolute Tubing  
Convolute (Corrugated) tubing is generally used for appearance although it provides 
limited abrasion protection. Corrugations are used to improve flexibility and improve 
abrasion characteristics in the direction perpendicular to the ridges.   
The tubing is available in sizes from one-eighth of an inch to two inches in diameter. It 
can be purchased either slit or un-slit.  Currently there are four primary convolute 
materials and two ultra-high temperature materials. 
 
2.6.4.1 
Convolute Tubing Anatomy 
 
 
 


### 第 69 页
Yazaki EDS Design Guideline Rev 3 
 
68
2.6.4.2 
Materials 
Polyethylene (PE) – vehicle interior use only (maximum continuous temperature use 
is 85°C) 
Polypropylene (PP) – interior use and limited exterior use (maximum continuous 
temperature use is 105°C) 
Modified Polypropylene (MPP) – designed for use in engine compartment (maximum 
continuous temperature use is 125°C) 
Nylon 6 (PA6) – designed for use in engine compartment (maximum continuous 
temperature use is 125°C) 
Tefzel – designed for temperatures up to 200°C 
Teflon – designed for use in areas where temperatures reach 260°C 
 
2.6.4.3 
Convolute Types 
Slit 
This type has an opening running along the longitudinal axis of the convolute.  This 
allows the wire to be loaded from the side.   Uses for slit: 
• Long takeouts where feeding wire through an un-slit convolute would be difficult 
• Bundle sections located between takeouts 
 
Un-slit 
This type of convolute has no slit and is like a drinking straw.  The openings are at the 
ends only.  Uses for un-slit: 
• Shorter takeouts 
• Sections where bundle will be surrounded by unfriendly surfaces (sharp, edges, 
flash, etc) after harness is completely installed 
Flat 
Flat type convolute is used in special occasions that require a flat convolute harness 
covering for certain dimension and protection. 
 
Convolute Application and Strategy information: See the YNA ESA Application 
Strategy. 


### 第 70 页
Yazaki EDS Design Guideline Rev 3 
 
69
 
2.6.4.4 
Performance Standard  
See Convolute Performance Standard YPES-16-122 
See Convolute Drawing Chart. 
 
2.6.4.5 
Special Notes 
When using slit convolute, be careful to avoid over-filling the tubing to the point that the 
edges of the tubing are not completely touching.  An opening such as this can allow 
the sharp edges to damage the circuits. It is difficult to know whether or not this slit 
may end up against a surface with weld flash, welded edge, or some other unfriendly 
surface (See the left diagram below).  To protect against this possibility use a localized 
piece of scroll with over lapping edges, non-slit tubing, (See the right diagram below), 
or braded sleeve. 
 
 
 
 
2.6.5 
Scroll Tubing and Troughs 
The function of scroll tubing and troughs is to control harness routing, facilitate 
installation into the vehicle, and protect against abrasion.  These can generically be 
called “protectors”.  Protectors may be designed in almost any size and shape, 
depending on the needs of the application.  Troughs can be designed with hinged 
covers, latched covers, or without covers. 
Scroll is a rigid slit tubular sleeve with a self-closing feature.  The main purpose of the 
scroll is to keep wiring harness straight.  It also provides limited degree of abrasion 
protection and can be used for appearance applications.  Scroll is usually taped at one 
of the ends depending on the application requirements.   


### 第 71 页
Yazaki EDS Design Guideline Rev 3 
 
70
 
2.6.5.1 
Trough and Scroll Anatomy 
 
Trough Anatomy 
 
Scroll Example 
 
2.6.5.2 
Materials 
Troughs 
There are many materials available for shields and troughs.  Choosing the best 
material will depend on a number of factors such as application environment, 
mechanical strength requirements, shape, manufacturing method, and price.  In 
general, the preferred material of choice for vehicle interior applications is 
polypropylene.  Nylon 6 (or 66) is preferred for engine compartment applications.  
Fillers such as glass and talc can be added to the base materials to modify the 
performance characteristics.  These fillers will likely affect the price as well.  Your YNA 
EDS Materials Engineer can help in the selection of the best material based n 
application needs. 
Scrolls: 
• PVC, MPP (New development) 
• Scroll can be extruded from various types of plastics, (ex.  PVC, PP etc.)  


### 第 72 页
Yazaki EDS Design Guideline Rev 3 
 
71
• More detail on this topic will be provided in future releases of the ED Design 
Guidelines. 
 
******* 
Design Considerations 
When possible, allow for a large bend radius to reduce stress on the harness.  Simpler 
shapes are generally better because they tend to be cheaper, easier to assemble and 
have fewer quality issues. Minimize sharp bends, twists, and curves.  
Consider the overall length of a protector.  Thought should be given to splitting long 
protectors to facilitate the ease of component packaging and the shipping of the 
harness into a carton.  Tooling costs may be less for smaller molds than for larger 
ones; however two smaller tools may still cost more than one large tool.  Balance cost 
with the potential benefits of the handling aspects at the affiliate manufacturing location. 
If there are left hand and right hand versions of the same shield, consider identification 
methods to help the affiliate.  For example, making one light color and the other dark 
color will help the affiliate to avoid confusion during assembly process.   
Future expansion of the vehicle circuitry should be planned when tooling new troughs.  
As a general rule, the engineer should determine the cross-sectional area of “high-line” 
vehicle harness which passes through the trough, and add 60% for potential growth. 
Eliminate protector covers where possible to avoid part complexity. Consider a living 
hinge design or tethered cover.  
******* 
Manufacturing Methods 
Troughs and shields are usually manufactured by one of the following methods. 
Profile Extrusion 
Extrusion is a method which injects material continuously through a die head.  The 
cross-section of the extrusion die creates the shape of the trough.  (As an analogy, 
think of a clay (Playdoh) extruder.  The clay is compressed in a container and then 
pushed through a die in the form of a flower, etc.)  This  
 Process is limited to linear-type products.  Extrusion is a process well suited for high 
volume parts that can be designed with a continuous cross-sectional shape. 
Injection Molding 
A wide variety of shapes and sizes of troughs and shields can be produced using 
injection the molding process.  Covers, hinges, latches, mounting provisions, etc. as 
well as finishes or graphics can be produced in a mold tool as part of the trough or 
shield.  Insert type molds can be configured to add metal parts such as nuts, studs, or 
brackets to the finished part.  To produce parts, molds are mounted and cycled in 
injection molding presses.  One press cycle is considered to be from when the press 


### 第 73 页
Yazaki EDS Design Guideline Rev 3 
 
72
closes and the plastic is injected into the mold to when the part is ejected from the 
mold. 
Injection molds can be designed to produce a number of parts in a single press cycle.  
Some large parts are produced in a single cavity tool.  Smaller parts can be produced 
in tools designed with many cavities.    When tooling-up smaller high volume parts a 
multi-cavity die is usually the best choice.  Even though the tooling cost is higher 
initially, the reduced piece price of the part will generally offset the initial investment 
over a short time period. 
NOTE: Troughs and shields must be free of sharp edges.  These could cause 
cuts and other injuries to operators during harness manufacturing or vehicle 
assembly.   Sharp edges could also damage the wire harness through cutting or 
abrasion. 
 
2.6.6 
Heat Shrink Tubing 
Heat shrinkable tubing (HST) is used as an electrical insulation to cover, seal, and 
protect splices.  It is also used to provide strain relief for splices and eyelet terminals.  
 
2.6.6.1 
 Heat Shrink Tubing Anatomy 
 
 
(Note: for dual wall HST product, the adhesive layer would be marked as well). 
 
2.6.6.2 
Materials  
 Heat shrink tubing can be made from various types of materials such as PVC, 
polyolefin, fluorocarbon polymers, silicon rubber, etc.  In wire harness applications the 
most commonly used HST tubing is the cross-linked polyolefin type.  Fluoroelastomers 
can be used for high temperature and chemical resistance applications. 
Polyolefin-based heat shrink tubes usually can operate at a continuous temperature at 
125˚C. With jacket made from thermoset polyolefin, such tube does not melt but would 
become softer above the melting temperature of it polymeric crystalline structures 
(~123˚C melting temperature for high molecular weight polyethylene crystalline). 


### 第 74 页
Yazaki EDS Design Guideline Rev 3 
 
73
 
2.6.6.3 
Types of Heat Shrink Tubing 
1) Dual Wall HST 
Sealed HST is also referred to as “dual wall” HST.  Dual wall polyolefin HST is used for 
splice sealing. When the tubing is heated, it shrinks, conforming to the applied 
components or wires.  The inner wall is usually a hot melt adhesive or non-cross-linked 
(XL) olefin. 
The hot-melt adhesive inner wall usually provides a tight seal and a conforming 
protective covering.  The disadvantage of this adhesive is that it may migrate out and 
cover terminals resulting in an open circuit condition. 
The non-cross-linked inner wall provides only a limited seal conforming protective 
covering. The water sealing quality of non-XL is less than that of hot-melt adhesive, but 
better than single wall tubing. The non-cross-linked inner tubing is usually used where 
adhesive is not permissible, and sometimes for wire breakout protection.  
2) Unsealed 
The unsealed version (also known as “single wall”) is used to provide protection where 
a water tight seal is not required. (e.g., to insulate an eyelet terminal). 
3) Heat Shrinkable End Caps 
Heat Shrinkable End Caps are a simple and effective method for sealing end splices.  
The wire harness design must allow the splice ends to be concentrated in one area.  
End caps can save process time with off-line operations and provide the ability to 
shrink multiple caps simultaneously.   
2.7 Grommets 
The primary function of grommets is to provide a safe harness transition through steel 
panels and to prevent water entry into the vehicle.  Other grommet applications include 
the prevention of engine gases reaching the passenger cabin, noise suppression, and 
vehicle beautification.  Grommets can also provide controlled motion for harnesses 
within a defined location or space (e.g., wire routing between the vehicle body and a 
door).  Grommets can be designed for almost any size and shape, only depending on 
the application. 


### 第 75 页
Yazaki EDS Design Guideline Rev 3 
 
74
******* 
Grommet Anatomy 
 
******* 
Materials 
Because of material properties, cost, and beautification reasons, grommets are usually 
molded in rubber or Synthetic Rubber (EPDM) and are black in color (the natural color 
of rubber).  Rubber has the ability of stretching in the order of 300%-700% of its 
relaxed position.  This allows a grommet to be installed on finished wire harness by 
means of a grommet stretching machine. 
Other materials include Thermoplastic Elastomers (TE) such as PVC and Santoprene. 
2.7.1.3 
Typical Applications 
The vehicle diagram below illustrates typical locations for grommets though every 
vehicle may have unique situations  
 


### 第 76 页
Yazaki EDS Design Guideline Rev 3 
 
75
2.7.1.4 
Types of Grommets 
Single (Pot) 
Also known as the pass-through grommet or “Pot” style grommet, the single type 
grommet passes through a single mating panel.  This type of grommet can be either 
‘Over-molded’ or ‘applied’.  Over-molded grommets are molded over the harness after 
the harness has been assembled.  Applied grommets are molded as a separate 
component, and then added during the harness assembly process.  Additional 
information on applied and over-molded grommets can be found in below. 
Double (Door) 
These are also known as door grommets, lift-gate grommets, or tube & grommet.  
Double type grommets will attach to two different mating panels.  These two mating 
regions are usually connected by a flexible section, often using corrugations or bellows 
to allow for flex motion (door swing). 
 
2.7.1.5 
Design Considerations 
There are several factors to consider when specifying a grommet for a harness. The 
most common are listed below. 
1) Verification of Sheet Metal Drawings 
Verify that the size of the hole, location, and direction of the punch match what was 
requested and agreed upon.  Obtain written documentation of these requirements from 
the OEM.  
4) Communized Openings 
Always start by investigating existing grommet designs.  Check these available 
grommets sizes before specifying the sheet metal hole opening dimensions and panel 
thickness. 
Grommet holes must be located such that the entire mating lip is on a flat surface.   
Any interference of the lip with another object could lead to water intrusion.   
Round shapes are generally the best sealing because the stresses are evenly 
distributed around the grommet seal at the sheet metal opening.  Other shapes may 
cause variable stress distributions resulting in sealing areas below the minimum 
sealing requirements.  Shapes other than round can also cause issues with installation 
force, ease of seating, and orientation.   
5) Mating Panel Dimensions 
Determine the dimensions of all connectors that will pass through the grommet and 
sheet metal hole.  The minimum clearance between the grommet pass-through hole 


### 第 77 页
Yazaki EDS Design Guideline Rev 3 
 
76
and the largest connector, or bundle section, should be greater than 3mm. See the 
diagram below for a graphical representation.  
The design should ensure that the size of the hole is sufficient for circuitry that may be 
added in future model years.  As a general rule, allow for a 10% increase in the 
number of circuits. 
 
 
6) Grommet Installation/Seating Direction 
The grommet may be either a “push-to-seat” or a “pull-to-seat” type.   
With Push-to-seat grommets the operator will push against the grommet when seating 
it to the mating panel.   
Pull-to-seat grommets require the operator to pull the grommet through the sheet metal 
hole from the leading side (operator side) to make the attachment.  The operator will 
pull directly on the grommet and/or on the lead section of the harness.  
The operator usually confirms the grommet is seated through a visual check.  The use 
of flanges on the sheet metal may reduce the insertion force and improve the sealing 
capability of the grommet. If a flange cannot be provided, the punch direction of the 
sheet metal should be in the same direction as the seating direction of the grommet.  
Sometimes lubricants are used at the vehicle assembly plants to reduce the grommet 
insertion force.   
The primary sealing between the grommet and the sheet metal is the indexed section 
of the grommet inside the sheet metal opening. The grommet lip/ flange are primarily 
used as a stop when seating the grommet.  It also offers an amount of splash 
resistance.  
7) Vehicle Location 


### 第 78 页
Yazaki EDS Design Guideline Rev 3 
 
77
Ensure there is sufficient operator hand or tool clearance in the vehicle to properly seat 
the grommet.  Clearance may be needed on both sides of a mating panel to give the 
operator flexibility in routing the harness and seating the grommet. 
8) Grommet Assembly to Wire Harness 
Applied grommets 
Applied grommets are the preferred grommet type for use on YNA harness designs.  
Applied grommets may have to be stretched using a grommet stretcher to allow the 
harness to be fed through it when manufacturing the harness.  The material 
specification of the grommet will determine the elongation (stretch) capability of the 
grommet.  Applied grommets are usually secured to the harness with tape that 
overlaps the grommet & the harness. 
Over-molded grommets 
The finished harness must be placed into the molding machine so the grommet can be 
permanently molded onto the harness.  The over molding process is generally done 
offline from the harness assembly.  
Design engineers should avoid using over-molded grommets in their designs due to 
some potential process concerns at our harness manufacturing affiliates.   
These concerns may include: 
• additional scrap from incorrectly processing the over-mold grommet 
• reduced process capability 
• the need for additional off-line processes 
• the need for additional machinery 
Over-molded grommets are more susceptible to losing their shape/dimensions in 
packaging because materials used can creep and/or deform under load/weight.  The 
rubber-like characteristics of applied grommets do not have this drawback. 
9) Sealing Wires Passing Through the Grommet 
In some applications the wire bundles may require sealing.  Some of the sealing 
methods include dual wall heat shrink tubing, hot melt glue, or a mastic patch.  
Additionally, a ‘drip loop’ may also be designed into the harness to direct water away 
from the grommet and vehicle opening. 
10) Door Grommet Orientation 
To prevent continuous stress on door grommets there is usually a vertical orientation 
established that puts the grommet and tube assembly in a relaxed position when the 
door is closed.  A mark molded in the grommet is used to direct the operator for proper 
grommet orientation. 
11) Grommet Appearance 


### 第 79 页
Yazaki EDS Design Guideline Rev 3 
 
78
Grommet appearance should be established by agreement between the customer and 
YNA.  In general, grommets are black with a surface finish that ranges from glossy to 
matte.  These parameters should be discussed before the design is finalized. 
12) Special Consideration 
If a grommet contains only a few wires, verify that the force required to seat the 
grommet is low.  Otherwise there is a potential for the assembly plant operator to 
damage the connector system, terminal, or wire without realizing the problem. 
 
2.8 Power Network Centers 
These centers provide a service point for plug in devices such as fuses, fuse links, 
circuit breakers, diodes, and relays.  Generally, each vehicle has its own design to 
match that vehicle’s architecture.  Some boxes are hard wired with plug-in terminals. 
Others have buss bars that mate harness connections so they can be disconnected for 
service.  Power Network Centers are also known as Power Distribution Blocks, Bussed 
Electrical Centers, and Fuse Panels.   
2.8.1 
Power Network Center Anatomy 
 
 
2.9 Component Material Issues 
2.9.1 
Material Incompatibility 
Degradation reactions of polymeric wire harness materials usually start with radicals, 
cations (positive ions) or anions (negative ions).  Radicals are atoms or groups of 
atoms that have an unpaired electron.  
For engineering resins such as polyesters and polyamides (e.g., nylon), one of the 
most common harmful reactions is hydrolysis; a degradation reaction with water.  For 
example, on one hand a moisture content >1% can increase nylon’s impact properties 


### 第 80 页
Yazaki EDS Design Guideline Rev 3 
 
79
(i.e., greater flexibility and toughness), but on the other hand this same moisture, over 
time, will break down the polyamide chain.  The hydrolysis reaction can be accelerated 
under acidic or basic environments.  A similar reaction mechanism applies to the 
hydrolysis of polyester such as PBT (polybutylene terephthalate), etc. 
PVC, a common material for wire insulation and other wire harness coverings (tape, 
scroll), outgases (releases) a caustic agent at elevated temperatures.  An agent, such 
as HCl (hydrochloric acid), can act as a degrading catalyst on polymers with a carbonyl 
functional group (such as polyester or nylons).  Therefore, in the higher temperature 
applications (such as under hood), it is not recommended to use PVC products (e.g., 
tape, etc.) where they can interact with nylon or polyester.   
TPOs (thermoplastic polyolefins), such as polyethylene (PE) and polypropylene (PP), 
have good resistance to acidic and basic chemicals but are susceptible to radicals.  As 
an example, under average weather UV lighting, PE may lose between 70 %–90% of 
its elongation after just one year.  In another example, copper can act as a catalyst to 
cause degradation of some polymers.  A wire made with a bare copper conductor and 
XLPE insulation at elevated temperatures (usually > 125°C) can form CuH (copper 
hydride) and induce an hydroxy-radical (*OH) or other radical(s), which initiate scission 
(breaking of cross-link bonds). This causes premature failure of the product. 
Grease or other lubricants can be used to prevent friction and oxidation.  However, an 
improper application of grease can also cause a polymer degradation reaction. Some 
lubricants contain lithium salt as a polar compound thickener.  The anions of this salt 
can initiate or accelerate polymer degradation at elevated temperatures.  For under 
hood applications, it is recommended to use a lubricant with a silica thickener to 
minimize such reactions.  
2.9.2 
Terminal Continuity 
 
For terminals, fret friction of the contact edges yield byproducts or friction polymers that 
reduce the continuity properties at the contact.  
 
 


### 第 81 页
Yazaki EDS Design Guideline Rev 3 
 
80
3 Routing and Retention Design 
A well designed routing plan will help achieve a quality EDS by creating an installation 
process with the proper clearances and harness protection. 
3.1 Developing a Routing Plan 
A successful routing design requires a predictable and controlled routing plan.  This 
can be accomplished by using the following methods or consideration points: 
• During the vehicle concept phase create a basic plan to claim real estate for the 
major wire harness routings. There are basic paths that every vehicle needs to 
have.  Plan early and act quickly. 
• Map out routes using sheet metal formations where possible. Take advantage of 
/ request sheet metal channels and troughs. 
• 
Map out a predicted path for the harnesses. When a key device on the harness 
is located onto the body, route the harness, making an attachment every 100 to 
200mm. This gives the operator a rhythm as they go along. As they slide their 
hands down the harness, they will find a clip to install at fixed intervals assuring 
that they are on the right track. Note: channels, troughs, scroll and other 
harness routing / assembly aids and protectors may necessitate other fastening 
requirements. 
• It is important to specify takeout directions relative to clipping direction through 
section cuts and views on the wire harness drawing to assure that the harness 
can be assembled the same way each time. This will allow the operator to place 
it in the vehicle in a repeatable fashion, taking out the guess work of orientating 
the take-outs. Depending on the size of the trunk and branches, one can easily 
loose 50mm of length if the takeout location ends up on the opposite side as 
required. 
 


### 第 82 页
Yazaki EDS Design Guideline Rev 3 
 
81
• 
�Avoid placing a tape-on attachment clip on a taped section of the harness 
less than 30 mm from the end of a protective covering (convolute tubing, etc.). 
This is because the harness may not provide a proper flat surface for the clip 
and prevent it from making a good solid attachment (i.e. the tubing may prevent 
the clip from fully seating). (See the left diagram below)  
• Avoid placing a tape-on attachment clip over protective covering less than 30 
mm from the end of the covering. This is because the taping of the clip to the 
harness protective covering may not secure properly in such a short distance 
and could jeopardize the harness attachment point. (See the right diagram 
below)� 
 
 
• �Avoid making bends greater than 90 degrees due to stress on the circuits 
which can lead to wire breakage.  (See the diagram below)  
 
Bend
Radius
< 90o
_
Taped
Bundle
Taped Bundle Bend Radius Guideline
 
• When routing, the rule of thumb for a bend radius is that it should be greater 
than 5 times the diameter of bundle involved. If you have the bend radius less 
than this, it can put a high amount of stress on the wires which can lead to long 
term durability issues such as wire strand breakage. Additionally, this severe of 
                                            
� In design review checklist. 


### 第 83 页
Yazaki EDS Design Guideline Rev 3 
 
82
a bend is not easy to route during vehicle assembly. (See the left diagram 
below) 
• Avoid locating wire splice joints in sections of the harness that will be bent when 
routed in the vehicle. This will put an unusual amount of stress on the splice 
joint, leading to long term durability issues such as individual wires pulling out of 
the splice and/or wire strand breakage. (See the right diagram below) 
 
• 
�When determining the taping method for taped harness sections which will be 
bent in vehicle position, first determine the harness bundle diameter. Bundles 
less than 30 mm can have full (continuous) wraps.  Sections with bundle 
diameters greater than 30 mm should be candy striped (spiral wrapped) to allow 
for flexibility. See the accompanying diagrams.  
 
• �When determining the taping method for harness sections covered in 
convolute and bent in vehicle position, determine the harness bundle diameter. 
Bundles than 30 mm can be full (continuous) or candy striped (spiral) wraps 
over or under the convolute. Bundle diameters greater than 30 mm should be 
candy striped (spiral wrapped) either over or under the convolute to allow for 
flexibility.  See the diagram below.  
                                            
� In design review checklist. 


### 第 84 页
Yazaki EDS Design Guideline Rev 3 
 
83
 
• 
�Do not allow unprotected wires from a take-out to rest against sheet metal 
surfaces.  Vibration may cause wear through the insulation, leading to shorts.  
To prevent wear-through, add tubing or create a positive attachment point. (See 
the left diagram below.)   When routing near sheet metal edges maintain a 
distance of 10mm or more to prevent contact. (See the right diagram below.)  
 
• �Avoid positioning a takeout from a harness bundle between the bundle and a 
sheet metal wall / panel as shown in the above right diagram.  Also avoid having 
the takeout coming from the bottom of the bundle. In both of these conditions, 
the circuits can become trapped or rub against the metal surface thus causing 
damage to the insulation and leading to shorts.  See the diagram below.  
                                            
� In design review checklist. 


### 第 85 页
Yazaki EDS Design Guideline Rev 3 
 
84
 
• Anticipate where future model year vehicles might add content.  Provide extra 
bundle space for these carry-over model year additions.  
3.2 Dimensioning & Tolerance 
It is important to maintain the proper wire lengths throughout the harness.   If the 
harness is properly laid-out, design your harness to the nominal dimension.  This 
allows for some variation in the affiliate’s harness manufacturing, and provides a 
product suitable to the OEM.  Please refer to the DFM Manual for specific details.  
Designing everything to the minimum tolerances can create problems by leaving the 
harness just barely long enough to make a connection.  This can put undue stress on 
the harness which may result in a premature failure.   
Designing everything to the maximum tolerance can result in a harness being so long 
that the excess length may create problems due to space limitations.  This excess 
length may interfere with other vehicle systems, or the harness may become damaged 
because there is no provision to store the over-run. 
3.3 Sheet Metal Provisions 
It is recommended that the wire design engineer request from the OEM a Sheet Metal 
Structural Plan or a whole vehicle Sheet Metal Pictorial.  These documents define each 
sheet metal part and allow for consistent usage of part names during the routing and 
attachment design phase.  It is also recommended that the wire design engineer seek 
agreement on major wiring placement and routing strategies early in the design phase 
with the OEM Design Department.  This is to reach pre-approval of “Real Estate” 
needed to allow for the actual routing space as the design matures through the 
development phase.  These activities should occur before the first release of the BIW 
(Body-In-White) Master Sections. 
When requesting wire harness routing holes in the sheet metal, add at least 10mm to 
the largest diagonal cross section of the wire harness (including connectors and 
takeouts) passing through the hole.  This will reduce damage to the bundle during 
vehicle assembly. 


### 第 86 页
Yazaki EDS Design Guideline Rev 3 
 
85
3.4 Special Considerations  
�Weld seams are transition zones where two sheet metal panels come together and 
are welded along the seam.  The edges of this seam can be very sharp and possibly 
cause wire cuts when the wire bundle is located across or installed over these edges. 
�Spot welds are places (roughly 10 mm in diameter) where automation welding tips 
have created a melting point to join two sheet metal pieces together.  The surface 
condition after this welding operation can leave sharp flash.  Flash can be small and 
difficult too see.  If possible, avoid routing over these marks.  If this is not possible, 
then harness protection should be considered. 
�Where possible, designate the direction of the sheet metal punch for a clip hole.  
Punching in the opposite direction will increase the clip insertion effort at the vehicle 
assembly plant due to flash around the edges of the hole.  Most metal stamping 
operations will only allow punches from one direction due to manufacturing methods, 
so this should be considered as well. (See the right side diagram below).  
Rolled edges should be in the direction that the harness is installed. 
�Routing holes should be as high as possible in the vehicle to reduce water and/or 
vapor exposure. Weld stud clips should be considered in these vulnerable areas. 
�Use a common clip hole diameter to reduce complexity at the sheet metal stamping 
plant.  However, if a situation arises where the misrouting of two bundles is possible, 
use different size holes to prevent one harness from being clipped into the holes of 
another.  (See the left side diagram below.) 
 
Generally, it is not recommended to place sheet metal holes for clips through two 
layers of sheet metal. This is because of the difficulty of getting the holes to align 
during the welding operation. When the holes are not properly aligned, the condition is 
known as winking.  To prevent a potential winking condition be sure to make the 
clearance hole opening on the bottom layer adequately larger than the hole needed for 
the clip.  (See the diagram below.)  
                                            
� In design review checklist. 


### 第 87 页
Yazaki EDS Design Guideline Rev 3 
 
86
  
3.5 Attachment Methods 
The wire harness can be a large component with its general shape laid out at the 
manufacturing affiliate.   Because the harness is flexible it can be routed and bent in 
numerous ways.  Clips and shields are used to control this routing to assure that there 
is consistency from vehicle-to-vehicle.  These devices are attached to the vehicle in 
several ways. 
3.5.1 
Methods of Attachment 
• Arrow head clips 
• Christmas tree head clips 
• Bracket mounting clips 
• Bolt / screw mount clips 
• Edges mount clips 
• Strap clips 
• Screws 
• Shields  
For more information on types and attachment devices, see the Chapter 2 section on 
Clips. 
3.5.2 
Design Considerations 
Attachments prevent harness bundles and connectors from rattling.  The engineer 
must weigh the importance of each location to determine the consequence of a 
partially mated attachment and the need for durability of the attachment over the life of 
the vehicle. A robust design and a predictable installation process will ensure proper 
attachment for the life of the vehicle. 
The routing and attachment of a wire harness is a process. The attachment points 
become a guide to the operator.  If the attachments to the body are at regular intervals, 
the process is more consistent.  The operator expects that for a given length of 
harness there will be a proportional number of attachments.  This also causes the 
operator to look for the next hole or bolt to attach the harness.  It becomes a map or a 
“connect-the-dots” process until the harness is installed.  When this rhythm is disrupted 


### 第 88 页
Yazaki EDS Design Guideline Rev 3 
 
87
and the attachment is not where they expected it to be, it may cause them to pause to 
think if there is a missed operation, slowing down the process. 
It is optimal to have the same type of attachment operation for each particular line 
worker when possible.  When many different types of attachment methods are used at 
a single line operation, there is a greater chance that some attachments will not be 
completely performed. 
Insertion force and ergonomic location are key factors to making a good attachment. 
The more difficult the insertion or the poorer the accessibility, the more difficult it is to 
perform it on a consistent basis. Changes in strength in line operators from shift to shift 
can result in inconsistent quality.   
It is recommended that wire harness attachment clips (clip-to-clip pitch) be placed 
approximately 200mm apart from each other.  Ensure that clip distances are sufficient 
to protect against sagging and potential noise due to the wire bundle hitting against 
sheet metal or other hard surfaces.   
A major function of the attachment clip is to give a forced deliberate routing 
configuration to the harness.  Correct clip attachment protects the harness from 
moving mechanisms, hot or sharp surfaces, and interference with subsequent 
assembly operations. 
�Verify the attachment clips will not interfere with any other components on either side 
of the sheet metal or attachment surface. 
Care should be taken when the wire harness bundle diameter is over 25mm and push 
type clips are being considered.  Verify the clip stem width.  A 6mm stem width is 
recommended for bundles up to 25mm in diameter.  A 9mm stem width or screw type 
clip may be needed for larger bundles.  It is recommended that retention tests be 
conducted to assure proper attachment durability. 
Clips should be placed near the trim attachment fasteners to force a disciplined routing 
thus preventing tangling, pinching or trim fasteners piercing into the wire harness. 
It is recommended that attachment clips having anti-rotation provisions to reduce wire 
harness rotation that can cause circuit tight roping and choking.  These conditions also 
prevent the wire harness from having a free-flowing straight appearance. 
�Attachment to any of the following is not permitted 
• fuel and vapor lines 
• water hoses 
• vacuum hoses 
• brake lines  
Reasons for not attaching wiring wire harnesses to brake and fuel lines; 
                                            
� In design review checklist. 


### 第 89 页
Yazaki EDS Design Guideline Rev 3 
 
88
1. Brake and fuel lines are not designed to support the weight of a harness 
2. Harness clips may damage these lines 
3. If a circuit was to short to a metal line, the line itself may conduct current, 
causing the line to become heated and possibly cause fire. 
4. Brake and fuel lines flex and move during normal operation, which could cause 
pinching and chafing of the wires and/or squeak and rattle issues. 
3.6 Connector Locations 
It is recommended that all connectors be located (fastened) to the vehicle to prevent 
extended movement of the circuits as they dress into the connection system.  Care 
should be taken concerning the direction of orientation of the circuits going into the 
connection system.  Connectors should be located such that they can be easily 
accessed for servicing.  Hidden or difficult to reach connections can cause field 
technicians to cut circuitry.  Also, never specify inline connections that do not meet the 
design, performance, routing and assembly requirements. 
Recommend attachment clips have anti-rotation provisions to reduce connector 
rotation. This allows for one-handed connections as well as a higher probability of 
making positive connections. 
3.7 Clearances 
There are several things to consider in regards to clearances between the wire 
harness bundle and other parts of the vehicle.  They include, how well the wire bundle 
is attached to the vehicle and if there is any relative movement of the bundle between 
the attachment points.  
3.7.1 
General Clearance Guidelines 
�Clearance between convolute tubing and a sharp edge such as sheet metal edges, 
bracket edges, and/or device edges should be 10 mm or more.  
�Error! Bookmark not defined.If the harness can be moved side-to side between 
attachments, then this amount of movement should be added to the minimum 
clearance of 10 mm. (See the left diagram below.) 
�Clearance between a wire harness protector, such as convolute, and a sharp edge 
such as sheet metal edges, bracket edges, and/or device edges can be at a clearance 
of 5 mm or more. (See the right diagram below.) 
                                            
� In design review checklist. 


### 第 90 页
Yazaki EDS Design Guideline Rev 3 
 
89
 
�If the taped harness can move from side-to-side between the attachment points, the 
amount of movement should be added to the minimum clearance of 30 mm. (See the 
left diagram below.) 
�Clearance between a taped section of wire harness and a sharp edge such as a 
sheet metal edge, bracket edges, and/or device edges or where the wire bundle at the 
point of the parts edge is held down by an attachment can be at a clearance of 30 mm 
or more. (See the right diagram below.) 
 
�When there is a surface-to-surface condition (bundle-to-sheet metal edge) and there 
is no vibration movement of the bundle, the metal edge should be rolled in such a way 
that the edge can not make contact with the bundle so as to not cut into the wire and 
cause an electrical short.  (See the left diagram below).   
With vibration and/or bundle movement relative to the metal and in which the metal 
edge will not receive any additional protection, protect the bundle by adding tubing to 
the local area where the two come into contact.  
Note - Any opening to the tubing (for example, along the slit) will allow for contact with 
the wires. Greater attention should be given to the details to prevent this from 
happening (See the right diagram below). 
                                            
� In design review checklist. 


### 第 91 页
Yazaki EDS Design Guideline Rev 3 
 
90
 
When a surface -to-surface condition (bundle-to-sheet metal edge) exists and there is 
no vibration movement of the bundle, protective edging can be placed on the metal 
surface making contact with the wire bundle to ensure that the metal edge will not cut 
into the wire and cause an electrical short.  Types of edging could include edge 
moldings, insulation patches. (See the diagram below).  
 
Pointed objects such as brackets, retainer heads, screws etc… are not to be located 
near the fuel tank.  Tank-rupture during a collision is a concern. (See the left diagram 
below.) 
A clearance of 30 mm or more is needed when routing next to fuel lines and brake fluid 
lines. (See the right diagram below.) 


### 第 92 页
Yazaki EDS Design Guideline Rev 3 
 
91
 
Evaluate the maximum length of the takeouts in their installed position to ensure they 
will not rest against or vibrate on sheet metal panels.  Over time the wire insulation 
could be damaged from this vibration. (See the left diagram below.) 
In areas where the wire bundle must past within minimum clearance to a bolt or sheet 
metal screw head, use localized tubing to protect against damage from contact. (See 
the right diagram below.) 
 
A minimum of 5 mm should be maintained between a bolt head and a protected wire 
harness bundle. 
Diagram 002
Wire  Bundles
Minimum distance between wire bundles and bolt / screw
head
Wire  Bundles
Clip
 


### 第 93 页
Yazaki EDS Design Guideline Rev 3 
 
92
�Keep wire bundles 20 mm or more away from moving mechanisms such as the 
throttle cables and linkage, window regulators, heater control cables and linkage, seat 
tracks, clutch linkage, accelerator pedal and linkage, door latch linkage and door lock 
linkage.   
�Spacing between the harness and high temperature components such as the muffler 
and exhaust system (exhaust pipe, EGR pipe etc.) shall be 200 mm or more. It is 
recommended that detailed temperature mapping be conducted for the worst case 
situations on endurance vehicles (e.g., towing a trailer uphill in the heat of summer).  
This is to confirm the maximum temperatures that the wire bundle must endure.  This 
is to ensure that the correct wire harness components are chosen. 
3.8 Other Routing Considerations 
3.8.1 
Partitioning 
Wire partitioning is a key element in the design of the EDS system. Without partitioning 
there would be just one giant harness in the vehicle with thousands of variations to 
cover every type of possible build configuration available.  Analyze all factors to 
determine the optimum way to divide the harnesses into reasonably sized sections.  
These sections must be able to be manufactured by the wire harness manufacturing 
plants, and accommodate vehicle assembly conditions at the OEM. Additional factors 
are listed below. Also see the section on Harness Complexity for further considerations.  
3.8.2 
Option Content 
In choosing a wire harness routing the engineer must consider option content. 
Consider how many different engine options there will be.  Left-hand and right-hand 
drive versions may have to be considered.   The different body styles for the same 
platform may need to be considered.  Body styles such as the sedan, convertible and 
station wagon may be different.  Consider the major electrical options for the vehicle 
such as the sun roof or a premium radio/CD/DVD system.  
                                            
� In design review checklist. 


### 第 94 页
Yazaki EDS Design Guideline Rev 3 
 
93
 
�When design an optional content wire harness, bare terminals sometime are taped 
back on the harness and insert in future as option. Guideline must follow:  
• 
Circuits are to be taped back with a minimum 20mm gap between terminals. 
• Any circuits that need to be taped back to the bundle without an insulator will be 
un-terminated and un-stripped and taped individually 
• 
Detailed instructions are needed when "out of the ordinary" designs are added 
to the print 
 
3.8.3 
Length, Weight, and Complexity 
Length, weight, and complexity of a harness may be a contributing factor in 
determining the partitioning.  Harnesses that run the entire length of the vehicle can 
have numerous sections and takeouts making them difficult to route/install in the 
amount of time allowed on the assembly line.  Weight may be a factor with some 
harnesses weighing over 30 pounds.  Always check on weight limitations that may be 
in effect at the OEM.  Always understand the requirements for the specific OEM factory 
where your harness will be installed.  These may be different depending on location. 
3.8.4 
Accessibility of Compartments 
There will be times when routing from one compartment to another is not possible due 
to packaging constraints.  For example it may be necessary to divide the harnesses 
with a bulkhead connector in order to have a more efficient assembly operation.  
Another example may be the need to install a harness at another part of the line to 
gain access, such as the transmission under the vehicle or back of the engine.      
 
                                            
� In design review checklist. 


### 第 95 页
Yazaki EDS Design Guideline Rev 3 
 
94
3.8.5 
Installation Process 
There may be a need to divide the wire harness into smaller parts due to the assembly 
line sequence.  In some situations only a partial installation of a harness can be 
accomplished which would leave the rest the harness to be installed in a later process. 
This would provide a reason to divide the harness into smaller sections.  Doing so 
would help ensure proper installation of the smaller harness segments rather than 
leaving part of a larger harness unprotected and subject to damage until installation is 
completed.   
 
3.8.6 
Modular assembly (I/P lines, Engine line, off-line doors) 
Modules are used to reduce labor in vehicle assemble plants.  This can require the 
wiring harness to be partitioned.  Typical examples include: FEM (Front End Module), 
Instruments Panel, Console, Seats, Off-line doors, and Headliners. 
 
3.8.7 
Pre-test Situations 
Occasionally on-line verification testing needs to occur which requires the harness 
circuitry to be partitioned.  As an example, engine plants will run the engines before 
shipping them to the final assembly plant.  The circuits required to run the engine need 
to be part of a partitioned assembly.  Additional examples include: fuel rails, 
transmissions, and I/P modules. 
 
3.8.8 
Environmental Conditions 
Sometimes it is better to partition the harness into two sections when moving from one 
environment to another.  For example, a harness using high temperature wire running 
from the engine compartment through the passenger compartment may benefit from 
partitioning.  The cost of high temperature wire running though the passenger 
compartment may be able to be saved by using a lower cost wire such as PVC.  This 
must be weighed against the additional cost of using an extra connection, whether it’s 
a weatherproof bulk-head connector or an unsealed in-line connector located inside 
the body compartment. 
 
3.9 Environmental Considerations 
All vehicle designs need to withstand a wide range of environmental conditions during 
the life of the vehicle.  Below are examples of these conditions: 
• High humidity 
• Dust bowl conditions 


### 第 96 页
Yazaki EDS Design Guideline Rev 3 
 
95
• Gravel roads 
• Mud splash 
• Sand spray 
• Rock salt 
• Salt spray near oceans 
• High interior temperatures 
• Subzero cold temperatures 
• Heavy vibration 
• Severe shock  
 
The most common way to verify the wire harness design is to place them in OEM 
durability vehicles. There are several types of tests including cold weather testing, hot 
testing in the desert, mountain climb testing, salt spray bath, and accelerated long term 
durability.  
 
3.9.1 
Exposure to Fluids 
There are a many fluids that a wiring harness may contact.  The engineer needs to 
evaluate what would happen to the wiring system if exposed to these fluids.  Some of 
these fluids are: 
 
• Windshield washer fluid 
• Antifreeze/engine coolant 
• Engine oil 
• Brake fluid 
• Steering fluid 
• Battery Acid 
• Diesel Fuel  
• Gasoline 
• Transmission Fluid 
• Differential Fluid 
 


### 第 97 页
Yazaki EDS Design Guideline Rev 3 
 
96
3.9.2 
Vibration 
Vibration plays havoc on an EDS system in many ways. Although the harness appears 
to be in one place when the vehicle is at rest like at an assembly plant; on the road it 
can be a different story.  Any portion which is not positively secured will have relative 
movement to its local surrounding. What the harness movement is against will have an 
effect on whether it will result in a long term failure.  
�Splice joints under heavy vibration (especially if suspended) are very susceptible to 
fatigue failures.  Do not place them in sections of the harness that are suspended 
between two independently moving parts of the vehicle such as the engine and the 
fender apron.  The engine rocks each time the vehicle starts and stops and will put 
stress on that section of the harness, and on any splice joints located in that section. 
Ensuring that connectors can be properly mated during assembly is an important part 
of designing a reliable EDS system.  Partially mated connectors can first function 
intermittently when subjected to vibration and will eventually fail.  This could leave 
exposed live circuits that could lead to an electrical short.  Post-assembly conditions 
such as a transport train or car hauler can cause this condition due to vibration. 
�Fuses, relays and other discrete components should be placed in an upright position 
in power centers whenever possible to avoid the combination of gravity and vibration 
that may cause a part to loosen from its mating terminals.  To avoid this problem, ribs 
can be added to the upper covers of boxes that hold these components to provide an 
extra means of retention for the device. 
Vibration can also target terminal systems in which the two halves of a connector may 
not have a perfect fit or when the terminal in the cavity has a cantilever condition.  This 
can cause the terminal to rock within the cavity during vibration leading to fretting 
corrosion. 
Proper torque specifications should be developed for grounding connections to body 
panels.  This will reduce the possibility of vibration causing loose and intermittent 
circuit function. 
3.9.3 
Special Considerations 
Driving a hot-running vehicle through puddles of water can induce thermal shock to the 
EDS components.  For example, an engine running at 200-300°F (~95 – 150°C) that is 
sprayed with cold water at 40°F (~4.5°C would equate to a 250° F (~120°C) thermal 
shock.  Know your vehicle …design accordingly.  
3.10 Noise and Vibration Harshness (NVH) 
3.10.1 Noise (squeaks and rattles) 
Noise and Vibration Harshness (NVH) / Buzz, squeaks and rattles (BSR) can be very 
annoying to the customer. There are numerous conditions that could cause the wire 
harness to make noise. The most common are: 
                                            
� In design review checklist. 


### 第 98 页
Yazaki EDS Design Guideline Rev 3 
 
97
• Rattling of unrestrained / unused connectors 
• Slapping of unrestrained wire harness sections   
• Loose covers to junction blocks, relay boxes, etc. 
Certain types of bundling tape used to bundle the harness can squeak when micro 
rubbing against a metal surface, plastic surface or other harness sections especially 
when cold.  A cold weather “Burke Porter” test is used to find this phenomenon. 
3.11 Design for Manufacturing, Assembly, and Serviceability 
A wire harness from the harness manufacturing plant through the vehicle assembly 
installation, and into the service life will require a wide variety of fixtures, machines and 
processes.  Each harness should be designed with these factors in mind.  We call 
these various accommodations Design for Manufacturing, Design for Assembly, and 
Design for Serviceability.  Below is an explanation of each and how they affect the 
development of the EDS system. 
 
3.11.1 Design for Manufacturing (DFM) 
Consideration of the wiring harness manufacturing plant limitations is important in your 
design.  Fixtures, Processing equipment, and Labor limitations need to be taken into 
account.  YNA has developed and released a DFM Manual in cooperation with our 
affiliated wire harness manufacturing plants to improve the wire harness fabrication 
process.  The DFM requirements are part of the management review process.  A DFM 
checklist is available to assist engineers in meeting the requirements of the YNA DFM 
Manual.  These links will take you there:   
�  DFM Guidelines � 
 
3.11.2 Design for Assembly (DFA) 
As a supplier to the automotive industry it is our responsibility to design products that 
can be assembled by our customer’s assembly plant operators.  Line operators and 
their equipment have limitations that need to be taken into account if our parts are 
going to be assembled properly with a reduced frequency of warranty defects or 
installation difficulty.   These limitations may include line speed, tool access, sheet 
metal design, installation efforts and ergonomic access required to install the various 
EDS components. 
Examples of items related to DFA: 
�Insertion Force of Connectors/Clips/Grommets 
Generally the higher the number of repetitions involved to install the components in a 
process – the lower the insertion force should be for each component.  A harness with 
                                            
� In design review checklist. 


### 第 99 页
Yazaki EDS Design Guideline Rev 3 
 
98
clips being installed at 10 repetitions per minute should use a lower insertion clip than 
one with 3 repetitions per minute. This also applies to connectors and grommets. 
Please refer to the Insulator and terminals chapter in addition to the preferred clip 
strategy. You can also reference the specific O.E.M. (Original Equipment 
Manufacturer) design guidelines for maximum allowable insertion forces and the 
manufacturer’s specifications for proper component selection and design.  
�Connectors 
Connectors should have some noticeable feature (ex. audible click) to help the 
assemblers know that the component is fully engaged with the mating half. Inline 
connectors should always be polarized to avoid possible process error in crossing the 
mating halves. Color alone is not recommended as a polarizing option. Connectors 
with C.P.A.’s (Connector Position Assurance) may be difficult to process. The general 
guideline is CPA’s should only be used on insulators with safety circuits. 
Weight 
Weight must always be considered when designing the harness. Process issues may 
arise if the harness is heavy. Additionally, there is a weight impact the harness will 
have on the overall vehicle. This is especially true of engine compartment harnesses 
that have their power distribution components attached.  Keep service loops to a 
minimum.  Avoid using larger than necessary cable sizes. 
Process Awareness 
�Wire harnesses should always be properly packaged to avoid takeouts from 
dragging on the floor or getting pinched. This is especially true of Instrument 
Panel/console, engine and in-line wiring where there is excess length that could be 
pinched during the chassis or engine decking process.  Takeouts that transition 
through sheet metal holes should be sufficiently staggered and taped back. At times it 
may be required to group takeouts or connectors using shrink wrap or plastic wrap to 
aid in the installation of the harness. In addition, there are takeouts that will need to be 
taped back based on option content and process sequence. Approved paper, coated 
cotton, or slit vinyl tape should be used to allow for easy tear-away from the bundle 
when required. However, it may be recommended to provide fixed or stationary 
connectors to store unused connectors rather than having tape-backs on the harness 
to avoid a noise and vibration harshness / buzz, squeak or rattle issue. 
�Avoid having processes that may not be operator friendly such as blind or 
overhead/stretching operations for connections and attachments to the vehicle. These 
types of operations generally cause operators to perform difficult movements/postures 
which can create ergonomic issues. This can result in certain connections not being 
fully mated or clips not fully seated. Always design connections to be in locations 
allowing for ample hand clearance, good operator postures (to enhance mechanical 
leverage between hands and part) and low process variability. 
                                            
� In design review checklist. 


### 第 100 页
Yazaki EDS Design Guideline Rev 3 
 
99
Bundle size/stiffness should always be reviewed during harness installation.  In 
general, the larger the bundle size the more it will resist bending.  This stiffness will 
increase the efforts required to install the harness in the vehicle.  These increased 
efforts could become an ergonomic issue for the assembly plant operator.  Bundle 
coverings such as band clamps, tape, convolute, or troughs will also play a big role in 
the flexibility of the harness.  A harness engineer can evaluate these coverings as well 
as the wire construction (regular wire vs. high-flex type) for ways to improve harness 
flexibility. Harnesses are stiffer when cold. 
�Main bundle routings should be optimized to allow for minimum take-out lengths.  
Minimum take-out lengths are in general, more ergonomically friendly to a line operator.  
Take advantage of in-vehicle troughs that will protect the wiring bundle.  Observe the 
installation process to ensure the process capability of a design.  Ask questions.  
Review the covering selections at design reviews to assure they meet functionality 
objectives and are able to be processed. 
�Adequate length is needed for installation, serviceability and in-plant testing 
requirements. Typically non-P.I.A. (Parts in Assembly) and serviceable items require 
extra length or slack. These items include center bezel connections i.e. radio, 
heater/AC controls, switch packs, in-lines and after market installations. Always make 
sure the extra length is properly secured to protect it from damage or creating noise. 
Orient connections to allow clearance in the most accessible tool direction for service 
or repairs. Also keep in mind what size studs/screws are typically used in the plant so 
that one can size ground eyelets/attachments properly and avoid adding complexity to 
the vehicle assembly plant.  
�I.D. labels should be located in a low visibility area but also need to be accessible. 
Design and protect against misrouting. If a wire can be misrouted, assume it will be.  
Work with the OEM to determine the routing paths and provide additional error-
proofing protection if necessary.  Where feasible, always include any visual aids such 
as colored or marked spot tapes on the harness to help the operators quickly route or 
unpackaged the harness correctly. This may especially be helpful for engine 
harnesses to designate the different injectors or on larger harnesses to show which 
directions certain sections of the harness should be pointing. 
�Avoid ‘fishing’ operations as much as possible. Fishing operations are difficult to 
process and may cause damage to the harness.  
Where feasible, the number of wire harness part numbers per family should be kept at 
a minimum to avoid complexity and capacity issues for the vehicle assembly plants.  
Where possible use larger cavity connectors to minimize or reduce the number of 
connections required, while at the same time meeting customer insertion force 
requirements. 
Each vehicle assembly plant may have a different assembly line build rate. What is an 
acceptable process in one plant may not apply to another. Be aware of various build 
                                            
� In design review checklist. 


### 第 101 页
Yazaki EDS Design Guideline Rev 3 
 
100
phases from prototype to pre-production to launch ramp-up to volume launch. It is 
generally the nature of harness assembly that as the line speed increases so does 
process sensitivity.  What may not have been an issue in earlier phases may have 
increased process risk in later builds.  When there is more than one vehicle being built 
on a single line, the operators work will be more consistent when the functions are 
common.  Also, try to communize processes where a single carline is assembled in 
more than one plant. 
3.12 Design for Serviceability 
Although the wire harness is to be hidden in terms of the consumer not seeing all the 
wires that make up an EDS system, there are several areas where harness servicing is 
required and many more that can impact the harnesses during the service of other 
parts. 
The most common are: 
3.12.1 Fuses and Fusible Links 
�Access to the fuse panel, junction block, and power network is required by the 
consumer and service technician.  Therefore, Fuse Links and Fuse Panels should be 
located for accessibility and with proper fuse description/identification. Consider the 
removal of covers with respect to accessibility. 
3.12.2 Ground Points 
�Ground Points must be accessible for troubleshooting, diagnostics or testing. Usually 
there are 10-15 of these ground points in a typical vehicle. 
3.12.3 Service Connectors 
�There are usually several diagnostic connectors such as OBD-II, or technician sub-
system test connectors. Examples of these are wiper washer test and ABS pump.  
Every OEM will have different requirements.  These connectors need to be accessible 
to the technician without damaging other parts of the wiring system. Be sure to work 
with the OEM service department when locating these connectors.  
 
3.12.4 Harness Labels 
�It is important to be able to identify the harness part number both for installation into 
the vehicle as well as during service.  At the same time, it is important from an 
aesthetic standpoint to hide the label from the consumer who might view the tag as 
unsightly.   
Place ID labels where the consumer or the technician can find them. In the case of the 
I/P place them so you can see them from under the dash. In the body they should be 
behind a removable trim panel. In the engine they should be behind the headlamps or 
                                            
� In design review checklist. 


### 第 102 页
Yazaki EDS Design Guideline Rev 3 
 
101
under the radiator support. ID labels can be either directly attached to the wire harness 
or applied on major components 
3.12.5 Relays 
Relays (and flasher relays) are parts that occasionally need to be replaced, thus they 
require accessibility. Some relays are placed in boxes which are easy to services; 
others with low option rates may be located in more remote locations.  Usage rates 
should be analyzed to determine the accessibility needed and discussed with the OEM 
for approval. 
3.12.6 Dealer Installed items 
There are dealer installed options that must be located in such a way as to not rattle 
when not in use or when placed into use as with an added jumper harnesses. 
• Examples: 
• Trailer Tow 
• CD Changer system 
• Fog lamps 
• Telephone 
• GPS / Navigation Systems 
• DVD Video Systems 
• Daytime Running Lamps 
3.12.7 Non-wiring service items 
There are many other commonly serviced parts not directly part of the wiring system 
but will interfere with the system in one way or another.  These should be reviewed for 
service clearances to the EDS system.  Wire harness interference to a standard 
service approach must be minimized. 
Vehicle devices requiring accessibility:  
• Spark plugs 
• Battery 
• Headlamp and tail lamp bulbs 
• Alternator 
• Starter 
• Oil filter 
• Air filter 
• Brakes 
• Accessory belts such as A/C 


### 第 103 页
Yazaki EDS Design Guideline Rev 3 
 
102
• Power steering components 
• Spare tire 
3.12.8 Special Considerations 
There are other items that must be accessible to either service or the end user.  
Identify the areas (not including the engine compartment) in which the owner may 
need access behind trim panels. Areas may include but are not limited to:  
• Access under the I/P for the fuse panel / junction block access to the fuel shutoff 
switch in the interior quarter panel. 
• Access to the spare tire wheel / compartment 
• Under the front seat, or rear seats in the case of a minivan or SUV 
• Storage compartment for tire jack   
�These areas should be verified to assure that the EDS components do not have 
sharp edges that could cause harm to the person performing the service.  Sharp edges 
may include clips, tie straps, brackets etc.  The engineer should perform a feel test 
after the first prototype vehicles to verify that this condition does not exist.     
�Provide service lengths for devices requiring removal and electrical disconnection. 
Typical examples are clusters, radios, CD players, switches and lamps.  Study what 
can happen to the take-out length when it is folded back for storage. The extra length 
may require special protection against cuts, pinching, squeak and rattle. See diagram 
000.  
 
 
3.13 Terms and Definitions 
Purchased Finished (PF) – Purchase finished harnesses are those that are sold 
directly to the OEM for installation into the vehicle.  In this case, the wire harness 
manufacturer is a Tier 1 supplier to the OEM.   
                                            
� In design review checklist. 


### 第 104 页
Yazaki EDS Design Guideline Rev 3 
 
103
Purchased in Assembly (PIA) – Purchased in Assembly (PIA) harnesses are those 
that are sold as a subassembly to a third party.  For example, the wire harness 
manufacturer sells a harness to a climate control manufacturer.  The climate control 
manufacturer takes the harness and attaches it to their climate control unit.  The unit is 
then sold to the OEM to be installed in the vehicle.  In this case, the climate control 
manufacturer becomes the Tier 1 supplier and the wire harness manufacturer is the 
Tier 2 supplier.  
OEM – Original Equipment Manufacturer 
I/P – Instrument Panel 
FEM – Front End Module 
HLD – Head Lamp and Dash 
DFA – Design for Assembly 
DFM – Design for Manufacturing 
APQP – Advanced Product Quality Planning and Control.  This is the process of 
facilitating communication throughout the entire project team early in the product’s 
development.  On time delivery, avoiding late changes and customer satisfaction are 
the key drivers of APQP.  
FMEA – Failure Mode and Effects Analysis.  An FMEA is a document used to analyze 
potential failure modes and the means of preventing them through design changes, 
process changes, and proper validation methods.    Through the use of a Design 
FMEA (DFMEA) and a Process FMEA (PFMEA) the instances of failures resulting from 
design, manufacturing or assembly can be reduced. 
DVPR – Design Validation Plan and Report.  The DVPR is the validation (test) plan 
created from the FMEA’s design controls.  It will describe the standards, procedures, 
and criteria used to test the component or assembly.   
PPAP – Production Part Approval Process.  Also known as PSW (Part Submission 
Warrant), PPAP is the method of gathering all the various documents required for a 
particular part before it can be approved to be shipped to our customer.  These 
documents may include Control Plans, DFMEA, PFMEA, Material Certifications, MSA 
(Measurement Systems Analysis), and others. 


### 第 105 页
Yazaki EDS Design Guideline Rev 3 
 
104
4 Architecture 
The EDS architecture can greatly influence the cost and efficiency of a wire harness.  
A couple of architecture examples are highlighted in this section along with the typical 
steps needed to create a design. 
 
4.1 Architecture Examples 
 
4.1.1 
Distributed 
 
4.1.2 
Centralized 
 
Batt
IPM
BCM


### 第 106 页
Yazaki EDS Design Guideline Rev 3 
 
105
4.2  Architecture Process 
 
Each OEM develops their EDS system differently.  This section provides an example of 
the steps that may be used to develop an EDS system. The example will take you from 
the customer’s initial vehicle concept through to the first defined architecture plan, 
including proposals and discussions with the customer.  At this stage there will not be 
harness prints to use for pre-production builds or testing.   You should expect 
numerous changes before the vehicle launches about 24 months after these initial 
architecture plans. 
4.2.1 
Vehicle Product Direction Letter 
A Vehicle Product Direction Letter is provided to the supplier by the OEM. This 
document typically describes the proposed vehicle in terms of standard features 
available, styling considerations, engine and transmission combinations, optional 
feature content, volumes, models, status of the vehicle (luxury/economy), countries 
where the vehicle will be sold, cycle plans interactions for the vehicle, etc….   
This letter provides an overview of the vehicle and helps the EDS engineer choose the 
best-suited architecture. 
4.2.2 
Device Transmittals 
Device Transmittals are data description sheets that provide information on each 
electrical device in vehicle.  Typical information described: 
• Current requirements (steady state current, stall current, in-rush current) 
• Voltage requirements (Minimum and maximum operating) 
• Electrical pin-out of the circuits (B+, ground, etc…..) 
• Mating connector part number to device 
• Information about the device release engineer (name, phone number, location) 
4.2.3 
Device Locations 
The device locations within the vehicle are proscribed by the OEM.  Determine these 
locations through your contact with the OEM device release engineer.   An example is 
given below. 
 


### 第 107 页
Yazaki EDS Design Guideline Rev 3 
 
106
 
4.2.4 
Sub-System Schematics 
Sub-system schematics define how all the devices of a system such as the radio, ABS, 
etc. are to be interconnected as a system. See the diagram below. 
 
4.2.5 
Option Rates, Take-Rates 
In order to judge the impact from one system to another you will need some form of an 
option or usage rate.  High option rate items should be considered first in optimal 
design practice.  Lower option rate items should receive a lesser priority for component 
location and routing path placement.  
4.2.6 
Sub-System Topology 
With the device location drawing, a series of simple sub-system topology diagrams can 
be made to show the general impact of wiring in the vehicle. Heavier lines are used to 


### 第 108 页
Yazaki EDS Design Guideline Rev 3 
 
107
show a series of wires while a thin line is used to show a single circuit. When these 
lines are overlaid, you can see where the concentrations of circuits exist.  This will help 
determine some of the hard point locations, such as required openings in body panels, 
to route the wire harnesses.  See the diagram below for an example. 
                      
 
 
 
Once the initial sub-system topology is defined, you can improve the design by using 
the proposed body openings as key locations.  These locations will help you route the 
circuits in the sub-system layouts. See Diagram 
 


### 第 109 页
Yazaki EDS Design Guideline Rev 3 
 
108
 
 
4.2.7 
Optimization leading to Proposals and Recommendations 
This proposal should include the following items: 
• Harness Layout partitioning (see diagram) 
• Fusing and grounding schemes 
• Junction Block (J/B) and Power Network Box content 
• Give-away and overlay schemes  
 


### 第 110 页
Yazaki EDS Design Guideline Rev 3 
 
109
 
4.2.8 
Vehicle Component Layout Consideration 
Device locations can impact the overall complexity, weight, cost, and assembly 
methods of a wiring system.  Some examples:  High current devices typically need 
larger wires.  This adds cost, size and weight to the harness.  Devices in remote areas 
may have voltage drop issues that require larger wire too.  In general, sub-systems 
should be located as close as practically possible to reduce wire size and voltage drop.   
4.2.8.1 
Types of Communication Lines 
The types of communication lines can have an impact on the component locations.  An 
example of this is a ring network, where the distance around the ring can be limited 
requiring components to be located within an acceptable zone.  Another example is a 
star configuration, where the length of the star point may have limitations due to signal 
loss.   
 


### 第 111 页
Yazaki EDS Design Guideline Rev 3 
 
110
   
4.2.8.2 
Modular Assembly 
OEMs may choose to assemble a vehicle made of sub-assembled modules produced 
at locations other than the final vehicle assembly line.  Engines, doors, I/Ps, seats, and 
headliners may be supplied as modular assemblies.  This can reduce the complexity of 
the vehicle assembly process at the final assembly line.  If there are many modules, 
the placement of the devices may be less than optimal to accommodate this type of 
assembly.  
4.2.8.3 
Serviceability 
Serviceability is a factor that may impact device placement. Devices with replaceable 
parts such as plug-in relays, fuses, circuit breakers etc are usually located for easy 
access.   
4.2.8.4 
Special Considerations 
When positioning a device consider the direction from which the harness connection 
will be made.  Connections made from a harness coming in from the top can have 
problems with water trickling down the takeout into the device interface.   This will likely 
affect top-mounted connectors most, but may impact side-mounted connectors as well. 
To eliminate this problem, the harness takeout should be routed from below the device.  
This keeps water from entering the interface.  When the takeout must route from above 
the device then a drip-loop should be used to help direct water away from the 
connector. 
For examples see the diagrams below. 


### 第 112 页
Yazaki EDS Design Guideline Rev 3 
 
111
 
******* 
Best Practices for Special Areas 
1) Engine 
Battery to alternator wire routing should be designed to minimize the length of the 
battery cable.  A minimum cable length reduces voltage drop, allowing for the use 
the of smallest cable size (lower cost, lower weight) while still providing the required 
electrical current. 
Place battery and under hood primary fusing close to each other to reduce the 
length of heavy gauge wires between the two.  This will also reduce the length of 
unprotected circuits. 
2) I/P 
The greatest number of circuits in an Instrument Panel (I/P) occurs where the 
driver’s door harness, engine harness, body harness, and I/P harness all come 
together.  The use of a Junction Block will reduce the number of connectors needed 
to connect the harnesses together. 
3) Body 
When possible, avoid placing modules in the rear of the vehicle.  Many of the 
circuits connecting to this module will probably come from farther forward in the 
vehicle.  This may result in the need for larger sized circuits due to voltage drop 
concerns and additional space requirements for the harness bundle.  Examination of 
alternate locations should be investigated for possible use.  Examples of such 
locations include: under the rear package tray, or in the side wells.  An exception 
might be when the battery is placed in the trunk instead of the engine compartment.  
In this case, a more in-depth study may be needed to determine which way is best. 
4.2.9 
Harness Complexity 
Harness interconnects are used to ease the manufacturing of the harnesses as well as 
the installation of the harnesses into the vehicle.  In addition, interconnects are used to 
aid in serviceability.  Harness interconnects are usually referred to as “in-lines”. 


### 第 113 页
Yazaki EDS Design Guideline Rev 3 
 
112
 
******* 
Purchase in Assembly (PIA) Concept  
• Harnesses are sent to a customer’s facility other than an OEM 
• Harnesses are attached to the customer’s end item product prior to shipment to 
the OEM 
• Multiple milestone dates are required 
• Multiple customers 
• Different requirements 
• Different procedures 
Harnesses within a family usually have similar routing.  However, these harnesses may 
differ by option content.  The option content is generally identified in a harness family 
as:  
• Loaded- Full option content (i.e. the luxury vehicle with all available options) 
• Velocity or Volume- Does not contain the full option content, but it usually has 
the highest volume 
• Base- The minimum amount of option content 
The families can be divided into categories: 
• Headlamp and Dash 
• Engine 
• Body 
• Instrument Panel (I/P) 
• Seats 
• Headliner 
• Jumpers 
• Minor 
******* 
Harness Family Reduction 
The complexity of a harness family can be reduced when approximately 90% of the 
harnesses in a family have a feature and the other 10% do not.  This may include an 
entire takeout or an individual circuit.   If the circuit and/or takeout is not used, it is 
taped securely to the wire harness, or connected to a dummy connector provided by 
the OEM. The complexity reduction varies from vehicle to vehicle.  Cost is critical to the 
OEM in determining if a feature should be given away or not. 
The cost evaluation can be conducted by costing the give-away feature vs. the cost of 
having an additional harness with the feature included.  Floor space at the vehicle 
assembly plant is a key consideration when deciding if a feature should be given away.  


### 第 114 页
Yazaki EDS Design Guideline Rev 3 
 
113
Some assembly plants have more space available for parts that are required for the 
build.  The assembly plants with more space may be willing to increase complexity. 
 
NOTE: Some OEMs current direction is moving towards sequencing with no give-away. 
 
******* 
Bill of Material (BOM) Component Commonization 
Commonization of component use can benefit Yazaki in a couple of ways.   
• The component piece price may be reduced by using additional quantities of 
one component due to increased volumes.   
• Inventory complexity and space can be reduced at the harness manufacturing 
plants.  An example of this is using one type of clip on a harness instead of 
several different designs. 
******* 
Design Complexity 
More complex designs increase the chance for something to fail in your harness.  Try 
to avoid some of these traps in your design: 
a) Complexity of Parts 
Operator ergonomics should be considered when designing.  Multiple connector styles 
require the operator to learn more unique operations.  Where possible these connector 
styles should be communized to aid the operator as well as create economies of scale. 
Installation forces on shields and clips should also be considered.  Reducing the 
variety of operations required from an operator reduces the chances that an operation 
will not be performed (or performed incorrectly). 
New parts will require additional verification and validation work.  The use of existing 
parts will reduce the need for additional check fixtures, jigs, crimp validation, etc. 
b) Complexity/Variation in Routing and Quantity Optimization 
�Avoid having different routing paths for different models of a vehicle. An example of 
this might be in the engine compartment where two different engines are available. 
Avoid designing completely different routing plans for each engine. Place components 
in common places so much of the design can be the same, reducing the need for 
special operations. 
Reducing the number of available harness part numbers with unique option 
combinations is desirable for both the OEM and YNA.  Optimizing this number requires 
balancing the option give-away cost with the cost of maintaining an additional part 
number.  The optimal number occurs when the cost of adding an additional part 
                                            
� In design review checklist. 


### 第 115 页
Yazaki EDS Design Guideline Rev 3 
 
114
number exceeds any give-away costs.  The cost of maintaining an additional part 
number is determined by the OEM.   
c) Complexity of Harness Construction 
�When several harness versions exist within a harness family consider communizing 
the location of splices, circuits within splices, lengths of tubing, etc.  Where possible, 
eliminate multiple fusing, grounding, and other harness construction differences.  
d) Obsolescence of Parts 
With the rapidly changing automotive landscape, parts routinely become obsolete.  
Changing needs can eliminate parts from harnesses due to low take rates and through 
cost reduction efforts.  These changes may occur before the end of the vehicle product 
life.  This may result in YNA having to write-off excess inventory at a loss.  To reduce 
the chances of obsolescence it is important to use common parts where-ever possible. 
 
4.2.10 Power Distribution 
******** Power Distribution Tree 
A Power Distribution Tree is a layout tool used by the Systems Engineer to illustrate 
the hierarchy of systems.  This hierarchy is established by device classification, and 
the electrical loads that the vehicle’s electrical and electronic devices will require 
through each branch of an electrical distribution system. This aids the Systems 
Engineer in determining the optimum electrical network using the correct circuit 
protection, wire gage, and terminal sizes. See diagram. 
The “Power Distribution Tree” starts at the battery and then splits into many branches.  
Each major branch is capable of conducting fuse protected high currents (20+ amps).  
This is accomplished by using a fusing node that is commonly called a “Power 
Distribution Center” (PDC) or “Power Distribution Box” (PDB).  From the PDC some 
power is directed to higher on-line battery current loads such as headlamps, horns, etc. 
while the remaining power is directed through the ignition switch and/or through 
secondary fusing nodes, such as a Fuse Panel or Junction Block. (See diagram 001h) 
The secondary fusing nodes proceed to branch into roughly twenty to thirty circuits that 
are fuse protected from 2.5 to 10 Amps. 
New power distribution centers/modules utilize electronics sometimes combined with 
software to control circuit protection and energy flow. 
                                            
� In design review checklist. 


### 第 116 页
Yazaki EDS Design Guideline Rev 3 
 
115
 
4.2.10.2 Device Classification 
Letters and colors are used for easy identification of designated classes.  There are 
four classes: Class A, Class B, Class C, and Class D.  Classes are defined as follows: 
a) NONCRITICAL 
Any function that provides a convenience.  (I.e. entertainment systems and non-
essential displays) 
b) ENHANCEMENT 
Any function that enhances, but is not essential to, operation and/or control of the 
vehicle.  (i.e.  ABS (Anti-Lock Brakes) enhanced brakes, important information displays, 
such as fuel gauge and speedometer) 
c) CRITICAL 
Any function that controls or affects the essential operation of the vehicle.  (i.e. critical 
engine and transmission control functions, fuel pump, brake switch, headlamps, and 
tail lamps) 
d) ELECTRO-EXPLOSIVE 
Any function that controls or affects the deployment of an electro-explosive device 
(EED) actuated passive restraint system. (e.g., airbag module). These types of loads 
are typically referenced in FMVSS or other Governmental Regulations and typically 
receive a 9 or 10 in a DFMEA severity rating.   
 
4.2.10.3 Types of Loads 
a) Inrush current 
A momentary current surge which may be many times the steady-state current load.  
Some devices with inrush currents include motors, lamps, solenoids and other devices 
which are going from a state of rest (no energy) to a work state (motion, Illumination). 


### 第 117 页
Yazaki EDS Design Guideline Rev 3 
 
116
b) Continuous Current  
 A steady-state electrical current drawn by a device, component, or sub-system. 
c) Intermittent Current  
A steady-state electrical current starting and stopping at intervals. 
d) Instantaneous Current  
e) A short duration steady state electrical current drawn by a device, component, or 
sub-system. 
f) Load Switching 
Future releases of the Design Guidelines will include more information on this topic.  
g) Stall Current 
A current draw condition that is several times greater than the normal operating current 
and only applies to motors.  A stall condition is caused by a force that interrupts or 
operates against the normal operation of the motor. 
4.2.11 Current Rating & De-rating 
The Systems Engineer needs to consider the maximum allowable current for each 
component in an electrical distribution system.  This is maximum current load is also 
referred to as “ampacity”.  The allowable current is dependent on the environment 
temperatures and must be adjusted downward, or de-rated, as the temperature 
increases.  The characteristic curve for the device will determine the maximum 
allowable current load.  See the diagrams and explanations below for examples. 
In some cases elevated temperature may require the use of an upgraded component 
in order to meet the system requirements. 
 
Using Ohm’s law, the heat (power) that will be produced by an electrical current 
passing through a conductor of known resistance will be equal to the square of the 
current multiplied by the resistance (P=I2R). The result is a temperature rise curve as 
shown in Diagram 007. 


### 第 118 页
Yazaki EDS Design Guideline Rev 3 
 
117
A de-rating curve, as shown in Diagram 008, is the Temperature Rise curve rotated 90 
degrees counterclockwise, but with a temperature scale that is based on the Maximum 
Operating Temperature of the component minus the Temperature Rise. The Maximum 
Operating Temperature is the maximum functioning temperature of the materials used 
in the component. To establish the de-rated ampacity of an electrical component, the 
Systems Engineer adds the allowable temperature rise to the elevated ambient 
temperature to find the corresponding ampacity. 
4.2.12 Over-Current Protection Devices 
The circuit protection devices in this guideline are current sensitive devices that are 
intentionally designed to be the weak link in an electrical circuit.  The function of the 
protection device is to provide complete circuit protection.  It may also be specified to 
provide discrete device protection by interrupting the current flow in over-current 
conditions.  
It is not possible to protect all electrical devices from remote wiring protection locations 
because in-rush and stall conditions may overlap the normal current dictated by the 
device.  For example, a windshield wiper motor has an inrush current well above 
normal operating conditions.  When it is in a stall condition (where the current is below 
the inrush current, but similar to the steady-state current for an extended period of 
time) the motor windings will heat up.  This causes an increase in resistance that 
causes the current to drop.  The windings will continue to heat up until they are 
damaged.  To protect against this potential damage, a current/thermal sensing device 
is required.  It is located on the wiper motor and provides protection in addition to the 
normal wiring circuit protection device. 
******** Fuses 
• ATO Fuse – Currently being phased-out of all vehicles, yet applications still 
exist in 25A - 30A range due to the terminals having better current carrying 
capabilities than its Mini Fuse replacement.  (“fast blow”) 
• Mini Fuse – Generally preferred over an ATO Fuse due to its smaller size which 
allows packaging, weight, cost, and system optimization.  These are available in 
ratings from 2 - 30A.  They are more susceptible to de-rating due to inrush 
transients. (“fast blow”) than the Maxi or ATO Fuse. 
• Maxi Fuse – A more predictable, serviceable, and reliable alternative to wire 
type F/L (Fuse Link).  They have lower susceptibility to nuisance blows as a 
result of short term over-current (transient) conditions such as inductive loads 
(“slow blow). 
• Mega Fuse – Bolt down fuses that protect circuits with continuous current loads 
which are well above terminal interface current carrying limitations. 
• Midi Fuse – Bolt down fuses that protect circuits with continuous current loads 
which are above terminal interface current carrying limitations.  It is 
approximately ½ the size of the mega fuse. 


### 第 119 页
Yazaki EDS Design Guideline Rev 3 
 
118
• Cartridge Fuse – Applicable in same situations as Maxi & Mega Fuses (“slow 
blow”).  Eliminates the use of double female terminals on the buss bar in a PDC 
or J/B.  This helps improve weight, packaging, and warranty. 
******** Fuse Sizing/Rating  
Understanding fuse sizing is a fundamental requirement for wire harness design. The 
basic principle is the fuse must protect wire from overheating. Therefore, a fuse is 
selected whose rating does not exceed the current carrying capability of the wire or 
wires that it is designed to protect. 
As mentioned previously, the environmental temperature will impact the current 
carrying capability of the circuit, and like other components fuses must be de-rated 
accordingly. This condition is commonly found with fuses in the engine compartment. A 
typical fuse de-rating curve is shown in Diagram 005. 
% OF RATING
 
 
******** Circuit Breakers 
Circuit breakers are reset-able devices that interrupt current via heating of a bi-metallic 
strip. They are available in cycling and non-cycling formats. They are targeted for 
applications in which a short-term over-current condition could occur under normal 
operating conditions that would normally cause a fuse to undergo a nuisance blow 
rendering the circuit open.  Examples of such situations are ice pack on the windshield 
wipers or stall conditions on a power seat motor.  Circuit breakers are provided and 
rated in a similar fashion to fuses. 
******** Fusible Link Wire 
Fusible Link wire is designed to open in the event of a current overload.  The insulation 
of this wire is designed to remain intact even after the conductor separates.  The 
standard fuse link length is 150 mm. Standard fuse link colors are: 
• 22ga - White 


### 第 120 页
Yazaki EDS Design Guideline Rev 3 
 
119
• 20ga –Orange 
• 18ga –Gray 
• 16ga –Dark Blue 
• 14ga – Red 
• 12ga – Black 
• 10ga - Dark Green 
• 8ga - Light Blue 
The maximum allowable continuous current through a fusible link wire is determined by 
the ambient temperature and the heat aging properties of the insulation material.  
Fusible links and circuit protection devices are designed to protect the wiring and may 
not necessarily protect other components in the event of a short circuit. 
******** Special Considerations with Fusing 
�Where not required for fuse outage indicators, safety circuits should be isolated from 
non-safety circuits to avoid nuisance open circuits.   
4.2.12.6 Special Considerations for Fuse Link Wire 
�Fusible link wires must not contact the battery or its plastic cover. 
Fusible link wires need to be outside of the wire harness bundle (not taped into the 
harness); this makes them readily accessible for servicing as well as making sure that 
when the wire conductor melts open it is away from the bundle, preventing damage to 
other circuits.  
Due to the opening method of fusible link wire, it can only be used in the engine 
compartment, not inside the vehicle. If tie wraps must be used to bundle the fusible 
links, they must be of the ladder type.  
Fusible link wire may be connected in series to fulfill specific requirements. With a 2 
GA size difference, the smaller fusible link will open without damage to the larger 
fusible link when the other criteria for circuit protection are met. 
Protection of the circuit can be accomplished with either fusible link wire at the battery 
or at the power distribution center (with the resultant amount of unprotected circuit from 
the battery held to a minimum).  Please consult OEM’s design specifications. Example: 
Fusible link wire should be three sizes (6 AWG) smaller than the smallest wire in the 
circuit.  With a difference of two sizes (4 AWG), the maximum circuit resistance (to be 
fully protected at engine compartment ambient temperature) is 55 milliohms.  
4.2.12.7 Legal Requirements 
There may be rules and regulations, such as Federal law, that specify how a device or 
system is protected. These laws and regulations may vary from country-to-country.  
                                            
� In design review checklist. 


### 第 121 页
Yazaki EDS Design Guideline Rev 3 
 
120
�The EDS must meet these requirements.  It is your responsibility, as a 
representative of YNA, to get this information from the OEM at the beginning of the 
program.  Each OEM will likely handle this information differently.  These regulations 
may impact to the EDS configuration, and may be difficult to modify at a later date. 
As an example, FMVSS (Federal Motor Vehicle Safety Standard) §571.108 requires 
that the headlamp circuits be fused individually.  If one headlamp has a failure (such as 
by a rock breaking the lens) and a fuse is blown, the other headlamp must not be 
rendered inoperative.  This requirement provides a safety measure by allowing at least 
one headlamp to work should the other have an electrical failure. 
Listing of Safety Standards 
Future releases of the Design Guidelines will include more information on this topic.  
4.2.13 Load and Signal Switching Devices 
4.2.13.1 Mechanical Relays 
Relays are electro-mechanical devices that are used in applications to switch power 
and/or signals between circuits. The most commonly used relay is a 5-pin configuration.  
This relay has two pins to energize the coil and a common pin used for the power feed.  
The last two pins complete the switching, and are configured in two ways.   
• Normally-Closed relays allow a completed circuit in the un-energized state.  
When the relay is energized the circuit is opened. 
• Normally-Open relays act in the opposite manner.  The un-energized relay is 
open.  To complete the circuit the relay needs to be energized.  
 
 
Relays also come in many other configurations.   
                                            
� In design review checklist. 


### 第 122 页
Yazaki EDS Design Guideline Rev 3 
 
121
4.2.13.2 Solid-State Relay 
Solid-state relays function in the same way as mechanical relays.  They both have 
similar configurations and can handle similar loads.   
The solenoid switching in a mechanical relay causes an audible “click” each time they 
are energized.  Solid-state relays, on the other hand, are silent.  This characteristic 
allows them to be packaged in the interior of the vehicle without disturbing the 
passengers. 
4.2.14 Grounding 
This guideline outlines general procedures for grounding automotive electrical 
components.  It applies to electrical and electronic devices, and power distribution 
components including electromechanical devices and batteries.  Application of the 
following techniques will increase Electromagnetic Compatibility (EMC) and provide 
proper operating voltages for electronic equipment.  These procedures must be 
applied in accordance with the device manufactures recommendations. 
******** Methods 
Automotive electrical systems must be grounded using a single point, multi-point, 
and/or hybrid configuration.  The configuration chosen is based upon the type of 
current in the circuit (low, medium or high).  The following paragraphs describe the 
ground paths, and outline the application of each configuration based upon the load 
current. 
******** Ground Paths 
The primary ground paths include the common ground, battery to body ground, and 
battery to engine ground.  The primary ground should not pass through any bolted 
members unless the device is designed as case grounded. Please consult OEM’s 
requirements. As a starting point: The primary ground must be designed such that the 
maximum circuit resistance and device currents maintain a maximum voltage 
differential between any two points of less than 100 mV.  
The secondary ground is a termination point or node for one or more device grounds.  
This ground must be tied directly to the common ground using a parallel single point 
ground strategy. . Please consult OEM’s requirements. As a starting point:  These 
grounds should not use any hinges, latches or bolted members as nodes and must not 
exceed a voltage differential of 300 mV. 
4.2.14.3 Ground Classifications 
a)  Current Grounds 
These are typically used for electronic modules and instrumentation.  Low current 
grounds are made directly from the module or device to the common ground using the 
parallel single point grounding strategy. Please consult OEM’s requirements. As a 
starting point:  These grounds should be designed such that the maximum circuit 
resistance and design currents produce a differential voltage less than 150 mV from 


### 第 123 页
Yazaki EDS Design Guideline Rev 3 
 
122
device to common ground.  A serial single point ground may be used if the differential 
voltage requirement is met. 
b) Medium Current Grounds 
These are usually found on lighting circuits, blowers and solenoids.  Medium current 
grounds can be made directly to common ground or a secondary ground node using a 
parallel or single point strategy.  Please consult OEM’s requirements. As a starting 
point:  These grounds should be designed to ensure the maximum voltage differential 
from the common ground to the device is less than 500 mV. 
c) High Current Grounds 
These consist of devices with large current loads such as cooling fans, alternators, 
pump motors and cranking motors.  These grounds must use a parallel single point 
ground scheme directly to the common ground. Please consult OEM’s requirements. 
As a starting point:  They must also adhere to a maximum voltage differential of 500 
mV from device to common ground. 
4.2.14.4 Connections 
Ground connections must be designed to maintain contact integrity under normal 
conditions of aging, temperature cycling, moisture, fatigue and other environmental 
factors.  The ground terminal cross-section must be equal to or greater than the cross-
section of the wire.  Stacking of up to three ground eyelets of the same size is 
acceptable in low and medium current applications.  Common practice dictates that the 
terminal with the largest current load be placed, if possible, nearest the grounding 
surface on the bottom of the stack of eyelets.  
Steel ground screws shall have an anti-corrosive coating. For connections to major 
power train assemblies only, the use of phosphate bolts for grounds shall dictate the 
selection of 30 lb-ft. or higher torque specification bolt locations. Other applications 
shall avoid the use of phosphate fasteners altogether.  Dissimilar metals should not be 
combined at any location due to corrosion and induced noise voltages from galvanic 
action. 
�Metal surfaces for grounding shall be free of paint and other insulating coatings or 
acquire this condition through paint or metal cutting fasteners. 
Grounds to vehicle sheet metal shall employ one of the following fastening methods: 
• Weld stud and nut/washer assembly and serrated eyelet. 
• Weld or pierce nut and paint cutting screw. 
• Externally serrated eyelet with pierced conical hole/depression and double 
threaded paint cutting screw ensuring high strip to drive torque ratio. 
 
                                            
� In design review checklist. 


### 第 124 页
Yazaki EDS Design Guideline Rev 3 
 
123
�The total resistance between ground terminations and the ground surfaces shall not 
exceed 0.5 milliohms for new parts when fastened using the specified torques. Some, 
but not all, of these connections include: 
• wiring terminal connections to vehicle sheet metal 
• wiring terminal connections to engine block 
• wiring terminal connections to I/P structural members 
• connections between I/P structural members and vehicle sheet metal 
• wiring terminal connections to components 
 
4.2.15 Ground Eyelets 
�Grounds are not to be placed in locations where water can collect.  The moisture will 
act as a catalyst in the corrosion and will likely result in a ground failure. 
In the case where one end of a ground eyelet circuit is in a wet zone and the other end 
of the circuit is in a waterproof connector, water can wick into the circuit at the eyelet 
and travel into water-proof connector. One way to prevent/alleviate this is to place a 
piece of adhesive heat shrink tubing over the insulation and conductor crimps of the 
eyelet for a seal (See the diagram below).. Take care to dimension the end of the heat 
shrink tubing as to not interfere with the eyelet surface.  Note such practice may bear 
some risk: such as sealing is not assured due to too high in eyelet strip thickness, 
adhesive overflow to eyelet surface during heating. Therefore, in some occasion, 
shrink tubing with less adhesive or selectively crossed linked products should be 
considered. (See the diagram below) 
 
• No more than three wiring eyelet terminals shall be stacked together under a 
ground screw or on a stud (in the absence of an added nut washer assembly). 
                                            
� In design review checklist. 


### 第 125 页
Yazaki EDS Design Guideline Rev 3 
 
124
• Combining fastening of vehicle body ground terminals with fastening of body 
components is forbidden.  (i.e., a screw that affixes a bracket to the sheet metal 
cannot be used as a ground screw). 
• Ground studs, joints and nuts should be dedicated to grounding only.  They 
should not be used for other structural joints.   
• Metal surfaces for grounding shall be free of paint and other insulation coatings 
or acquire this condition through the use of paint cutting fasteners. 
• Try to use weld nuts for grounds.  Weld screws tend to "strip" and fail more 
readily over time. 
• "Pop-riveted" connections are not suitable for grounding wiring terminals. 
• Make sure to ground shields only on one end.  This will insure proper EMC. 
• Redundant ground points in the body area are recommended to insure against 
electrical system breakdown due to ground point failure. 
• Be sure that the area for the seating of the ground terminals is sufficient to 
accept any orientation of the eyelet, as it may spin during seating. 
• When grounds are being secured, rotation of the eyelets must not have any 
negative effect on the harness or any other related parts.  If rotation poses a risk 
because of rotation, incorporate some anti-rotation mechanism. 
 
 
 
4.2.16 Special Considerations � 
This guideline applies to the entire vehicle power distribution system, ensuring proper 
operating voltages for devices and modules by reducing common impedance coupling 
and ground loops.  When all vehicle systems are considered, the ground distribution is 
developed to maintain separate ground types within a secondary ground.  Different 
ground types can only be combined at the common ground using a ground eyelet and 
a double crimp.  As a general rule the ground types that remain separate include: 
                                            
� In design review checklist. 
Direction of rotation  
Rotation stopper


### 第 126 页
Yazaki EDS Design Guideline Rev 3 
 
125
• Grounds for current sensitive devices (electronic grounds) such as fuel level 
sensors, engine controllers, or electronic clusters. 
• Inductive/capacitive electrical grounds (noisy power grounds) such as motors, 
solenoids, most lighting and relay grounds. 
• Headlamp grounds shall ground separately on each side of the vehicle. The 
maximum voltage drop shall conform to OEM specifications for each lamp 
• Branch length for grounds is to be 50mm or more 
• The battery to engine ground shall be sufficient to keep total resistance within 
OEM requirements. 
• On soldered ground circuits that handle high current loads, proper wire size is 
required to dissipate heat away from solder area to prevent solder/terminal 
corrosion. 
• Do not place grounds on body panels that are hinged such as Doors, Hoods, 
Trunks, as the ground path will not be acceptable due to possible intermittent 
conditions when these panels are swung back and forth.   
• The size and type of ground point device (bolt, screw, weld-nut) must be 
confirmed by the OEM engineer and the assembly plant performing the 
operation 
• Try to standardize the ground point devices as much as possible to avoid using 
multiple assembly tools in the plants and at the service centers. 
• Ground locations should be accessible for routine maintenance, troubleshooting 
and repair. 
 
4.2.17 Attachment Notes 
A number of vehicle systems and devices require specific design consideration in 
regards to their ground configurations.  All vehicle grounds should follow the above 
guideline in conjunction with the manufacturers’ recommendations.  Systems requiring 
special attention include battery grounds and headlamp lighting grounds. 
 
4.2.18 Ground Loops 
Ground Loops are a source of noise that does not meet the differential voltage 
requirements.  This is typically true when multiple grounds are used and are separated 
by a large distance within the vehicle.  A ground loop is formed when a system is 
grounded at two different points generating a potential difference.  Depending on the 
current loads and circuit resistance, a noise voltage is generated.  The magnitude of 
this noise voltage may be large compared to the signal level causing module failures or 
incorrect communications.  Ground loops can be avoided by using a single point 
ground strategy or isolating the second circuit. 


### 第 127 页
Yazaki EDS Design Guideline Rev 3 
 
126
 
4.2.19 Battery � 
The battery to body ground and battery to engine ground must meet the primary 
ground path requirements.  In addition to this requirement 
• Battery ground to body resistance must not exceed 2mΩ at 27°C.   
• Battery ground to engine resistance must not exceed 1mΩ  at -29°C. 
 
4.2.20 Lighting Grounds 
Headlamp lighting grounds also require special attention and must be grounded to the 
common ground. Headlamp grounds must remain separate for redundancy and use a 
single point ground strategy. 
4.2.21 Panel 
Because of high starting and running current draws and circuit protection 
considerations, the A/C blower (and the A/C Compressor clutch, where applicable) 
shall employ a dedicated ground to body-in-white sheet metal as adjacent as possible 
to the motor ("hot"-switched feed) or control (ground-switched motor and/or clutch). A 
"hot" switched clutch shall normally ground through an engine ground splice or 
termination. 
4.2.22 Doors and Seats � 
Door and seat actuator grounds shall not pass through the main instrument panel 
wiring assembly. 
4.2.23 Power Train 
Use of case-grounded critical sensors shall dictate subsystem grounding to the engine. 
�With electrically powered engine cooling fans that have 10 amps or less in current 
draw, the voltage drop between the engine and the fan motor case (isolated radiator 
                                            
� In design review checklist. 


### 第 128 页
Yazaki EDS Design Guideline Rev 3 
 
127
tied to motor ground) shall not exceed 50 millivolts to avoid excessive cooling system 
solder corrosion. Larger fans dictate isolating the motor from the radiator. 
4.2.24 Terms and Definitions 
• Over Current Protection Devices – A current sensitive device designed to be 
the weak link in an electrical circuit used to protect the circuit and possibly a 
dedicated component by reliably interrupting the current flow in over-current 
conditions. 
• Fuse Link (F/L) – Circuit protection device.  Could refer to a serviceable device 
(i.e. A1 Fuse-Link Cartridge) or to fusible link wire. 
• Solid State (High Side and Low Side Drivers) – Circuit protection that 
combines software and active electronic devices such as FETS, to detect over-
current conditions and shut down the device for as long as the fault exists.   
• Circuit Breaker – Reset-able devices that interrupt current via heating of a bi-
metallic strip. They are available in cycling and non-cycling formats. They are 
targeted for applications in which a short-term over-current condition could 
occur under normal operating conditions that would normally cause a fuse to 
undergo a nuisance blow rendering the circuit open.  Examples of such 
situations are ice pack on the windshield wipers or stall conditions on a power 
seat motor. 
• Fusible Link Wire – A piece of wire sized to be the weak link in the circuit.  
Should be 6 AWG sizes smaller than the smallest wire in the circuit and have a 
length of 150 mm. 
• PTC – (Positive temperature coefficient) A device that changes internal 
characteristics when heated due to I2R heating.  This change increases the 
internal resistance until current flow is limited.  The device resets when the I2R 
heating is interrupted, this allows them to be packaged without service 
considerations.  Due to the trickle current allowed by a PTC in its tripped state 
they are not suitable for B+ circuits (circuits “hot” all the time) because they can 
cause battery run down in short circuit situations.  Therefore they can only be 
implemented in a switched/controlled circuit. 
• Relay – Electro-Mechanical device used for switching applications. 
• Ground – An equipotential point or plane that serves as a reference potential 
for a circuit or system. 
• Common Ground – A common member for grounds, such as a frame or engine 
block. 
• Low Current Grounds – A ground path with a maximum current limited to 3 
Amps.  This ground is typically made from the electronic device directly to 
common ground. 
• Medium Current Ground – A ground path with a maximum current limited to 30 
Amps.  The medium current ground may be tied directly to a common ground, or 
grounded through a secondary ground path, if maximum voltage drop 


### 第 129 页
Yazaki EDS Design Guideline Rev 3 
 
128
requirements are met.  Devices that utilize medium current grounds include 
lights, blower motors, solenoids and warning flashers. 
• High Current Ground – A ground path where the maximum current may 
exceed 30 Amps.  High current grounds should be attached directly to the 
common ground.  Devices that require high current grounds include the 
alternator, starter and cab to frame grounds. 
• Primary Ground Path – The lowest resistance path which interconnects the 
battery, engine or common ground path.  Primary ground paths should not pass 
through bolted members. 
• Secondary Ground – A terminating point for one or more medium current 
grounds.  The terminating point (node) is directly connected to the common 
ground.  A secondary ground can consist of any sheet metal part, wiring splice, 
cast part or bus bar which is continuous.  Note: critical functions should not be 
placed within a node. 
• Common Impedance Coupling – Common impedance coupling occurs within 
a secondary ground.  The current from two individual circuits flows through a 
common impedance producing a voltage drop which influences all circuits 
attached to the node. 
• Galvanic Action – The use of dissimilar metals with a signal path produces a 
noise voltage due to galvanic action between the two metals. 
• Serial Single Point Ground – This is a series connection of all the individual 
circuit grounds according to their physical location. 
• Parallel Single Point Ground – This is a combination of parallel grounds that 
combine in a single point at the common ground.  This ground is most desirable 
at low frequencies due to reduced cross coupling between ground currents from 
different circuits. This configuration is also called a “star” ground. 
• Multi-Point Ground – The ground circuits are connected to the nearest 
available low-impedance ground plane, usually the common ground.  This 
system is typically used in high frequency applications and critical circuits. 
• Hybrid Ground – A hybrid ground configuration appears differently at different 
frequencies.  At low frequencies, the hybrid connection acts as a single point 
ground, but at higher frequencies they act as a multi-point ground. 
 


### 第 130 页
Yazaki EDS Design Guideline Rev 3 
 
129
 
4.3 Wire Joining Methods 
In various locations through an EDS there will be a need to combine circuits with 
common functions or circuits with multiple termination points.  To accomplish this, 
wires may have to be joined.  There are many different ways to join circuits.  Some of 
these are described below. 
 
4.3.1 
Joint Connectors 
Future releases of the Design Guidelines will include more information on this topic. 
 
4.3.2 
Bussing systems in Power Distribution Boxes and Junction Blocks. 
 
4.3.3 
Wire Splicing (wire to wire)  
******* 
Wire Splice Methods: 
1) Resistance Weld 
Resistance welding is probably the most common method used for a wire-to-wire 
splice.  It’s a process used to join metallic parts using electric current.  Parts (two ore 
more wire samples) are locally heated until a molten pool forms. The parts are then 
allowed to cool, and the pool solidifies to form a weld nugget, and a solid bond. To 
create the heat, copper electrodes are used to pass an electric current through the 
work pieces.  The work pieces are held under a controlled force during welding by the 
electrodes. 
2) Splice Band/Clip/Terminal 
A splice band/clip/terminal is a solid metallic part which is used to mechanically hold 
two or more work pieces together (two or more wire conductors).  The work piece 
samples are aligned together and the splice band/clip/terminal is compressed around 
the wire conductor strands using a controlled shape and force. 
3) Ultrasonic Weld 


### 第 131 页
Yazaki EDS Design Guideline Rev 3 
 
130
Ultrasonic welding is a pressure weld, which uses microscopic motion and kinetic 
energy to bond pieces together. During ultrasonic metal welding, a complex process is 
triggered involving normal forces, oscillating shearing forces and a moderate 
temperature increase in the welding area.   This method will not work on tin-plated 
copper conductors. 
The work pieces (two or more wire conductors) are placed between a fixed machine 
part, (the anvil and the sonotrode), which oscillates horizontally during the welding 
process at high frequency (usually 20 to 40 kHz).  This forms a solid bond between the 
two wires. 
Reference to YPES-16-101 Ultrasonic Weld Spice for more detail. 
4) Twist and Solder 
Twist and Solder splicing is a technique consisting of two or more wires conductors 
being twisted together, then molten solder is applied to the twist and allowed to cool to 
form a solid bond between them. 
4.3.4 
Splice Orientation 
******* 
Center-strip Type 
The wire insulation is removed from a center section of the primary circuit to expose 
the conductor.  The circuit being spliced to the primary circuit is end stripped and its 
conductor is aligned over the primary conductor. 
******* 
Face-to-face / overlap Type 
The wire insulation is removed from the ends of multiple circuits exposing their 
conductors. With the wires coming from 2 directions, the bare conductor surfaces are 
aligned over each other. 
******* 
End / Back End / Stump Type 
The wire insulation is removed from the ends of multiple circuits exposing their 
conductors. With the wires all coming from the same direction, the bare conductors 
surfaces are aligned over each other. 
The following diagram shows 3 different orientations of splicing for ultrasonic welding, 
resistance welding, twist and solder wire connections, and crimped wire connections.  
See the figure below for examples of each orientation. 
Center-strip Type 
 
Face-to-face Type  
Back-end Type 


### 第 132 页
Yazaki EDS Design Guideline Rev 3 
 
131
 
 
Reference to YPES-16-165 for more detail on splice. 
 
4.3.5 
Splice Coverings 
Splices need to be covered when assembled into a wire harness.  Depending on the 
application, the method for splice covering may differ.  Refer to ESA Application 
Strategy for splice covering selection, and see the protective coverings section in 
Chapter 2 for more detail. 
4.3.6 
Designated Wet and Dry Areas 
It is important to understand the customer’s requirements regarding designated wet 
areas.  This will target the wet areas to use heat shrink tubing for splice coverings, and 
the dry areas where splice tape might be sufficient for splice covering.  In the absence 
of any OEM requirements, this section will outline provide the wet/dry guidelines. 
Typical wet areas include the engine compartment, wheel wells, underbody, floor panel 
and door panels. Dry areas normally include instrument panels, headliners and other 
areas inside the vehicle that are not in contact with the floor carpeting. In some cases, 
such as off road vehicles, all harnesses should be considered to be wet in all areas.  
 
Wet Areas  
Dry Areas 
Engine Compartment 
Instrument Panels 
Wheel Wells 
Headliners 
Underbody 
Trunk 
Door Panels 
Inside of vehicle 
 
Floor Panel  
not in contact with floor 
 


### 第 133 页
Yazaki EDS Design Guideline Rev 3 
 
132
On Road Vehicles 
Harnesses are exposed to wet and dry areas. 
These vehicles designed for use on paved or public roads. 
Examples include: Cars, Vans, Wagon, and Truck (limited) 
Off road vehicles 
All harnesses should be designed for wet areas. 
These vehicles are designed for use off paved or public roads, and in rugged terrain. 
Examples include: Truck (limited), SUV, Jeep, Hummer, 4x4 
 
Some areas may not seem wet, but can be very wet under certain conditions. 
 
Examples:  
1) Floor pan/Foot well areas 
Wet shoes, boots, and snow on the bottom of the shoes (which can contain corrosive 
salt water), or driving rain getting into the vehicle when the doors, windows or sun roofs 
are accidentally left open can create wet, in otherwise dry areas. In cold climates it is 
possible for the floor carpeting to stay wet all winter.  
2) Trunk well areas 
Conditions such as; spilled grocery bottles, wet boots, leaving the trunk open while 
hauling a large load or wet cargo; may require the use of sealed splices in the trunk. 
3) Console/Instrument Panel 
With all the cup holders available in vehicles today, consider what happens when one 
spills soft drinks or coffee. On many off road vehicles the manufacturer assumes the 
customer will wash out the vehicle interior with a garden hose or a coin operated car 
wash.   
4.3.7 
Special Considerations � 
The maximum number of wires in a splice joint may vary depending on method and 
customer requirements.  The general rule is to have no more than 8 wires.  The DFM 
Manual can provide additional information. 
• Strain relief for wires in a splice may be required for wires smaller than 20AWG. 
                                            
� In design review checklist. 


### 第 134 页
Yazaki EDS Design Guideline Rev 3 
 
133
• 
�The distance between splices needs to be maintained per the minimum 
specified in the Design for Manufacturing (DFM) manual. 
• �Do not allow splices in the harness bundle sections transitioning between the 
body and the engine because over time vibration may degrade the splice. 
• �Do not allow wire splicing to occur between the door and pillar, or any part of 
the wiring having to repeatedly rotate throughout its consumer usable life. 
• �Don’t place splices where the harness needs to bend during installation or 
during service as the splices will be stiffer than the rest of the harness. 
• �Don’t place splices on a curved section of wiring.  
• Splices in the designated dry sections of the vehicle may not need to be water 
proof.   Exterior splices on the engine, on the transmission, under the car, or in 
designated wet sections of the interior will need to be water proof. 
• �Do not allow splices to occur in wire harness sections resting on the vehicle 
floor pan unless protected against water intrusion.  Each customer or individual 
vehicle line may have a designated “water intrusion zone”.  Refer to each 
vehicle’s design standards (from the OEM) for any water intrusion zone 
requirements. 
• �Do not place splices in the spare tire wheel well or along the sides of the trunk 
because of the possibility of wet items being placed in the trunk.  These may 
include snow shovels, wet boots, spilled milk or pop bottles etc.  
 
4.3.8 
Terms and Definitions 
• Splice Balancing – A manufacturing method used to maintain an equal number 
of circuits on each side of a face-to-face/overlap type of splice. 
• Splice Optimization – A review of each splice should be done to determine the 
best splice method for a particular application, location, and circuit function, etc. 
 
4.4 Electromagnetic Compatibility 
This guide will establish the Electrical/Electromagnetic Compatibility (EMC) 
requirements for the wiring harness. 
EMC defines an electrical system’s ability to remain neutral in the vicinity of other 
vehicle systems and external sources.   
• The system should neither cause interfere to other systems, nor be susceptible 
to interference from those other systems on the vehicle.    
• The vehicle, as a complete system, must remain neutral in the greater 
environment.  It must not interfere electrically with other vehicles, and it must not 
interfere with broadcast or communications transmissions.   
                                            
� In design review checklist. 


### 第 135 页
Yazaki EDS Design Guideline Rev 3 
 
134
• The vehicle system must remain fully operational when exposed to strong 
electromagnetic fields from the outside, such as radio transmitters, power 
substations.   
When designing for Electromagnetic interference (EMI) protection there must be an 
understanding of the various types of electromagnetic emissions from modules or other 
devices inside and outside of the vehicle.   
 
4.4.1 
Conducted Mode Propagation 
Conducted mode propagation occurs when electromagnetic interference travels on the 
wiring harness connecting the noise source to the device affected, as shown in the left 
figure below. 
4.4.2 
Radiated Emissions Propagation 
Radiated emission propagation occurs when the electromagnetic waves propagate 
through free space (air) from a noise source to the device, as shown in the right figure 
below. This may interfere with the device operation. 
 
 
4.4.3 
Conducted and Radiated Propagation 
Conducted and radiated propagation occurs when the electromagnetic waves 
propagate on and radiates from the wire harness connecting the noise source to the 
device affected, as shown in the left figure below. 
4.4.4 
Radiated and Conducted Propagation 
Radiated and Conducted propagation occurs when the electromagnetic waves 
propagate through free space (air) and on the wire harness from a noise source to the 
device affected, as shown in the right figure below. 


### 第 136 页
Yazaki EDS Design Guideline Rev 3 
 
135
 
 
4.4.5 
Cabling/ Wire Harness 
The use of wire harnesses (transmission lines) in the presence of electromagnetic 
fields can cause unwanted noise energy to be induced onto signal and current carrying 
circuits.  Typical wire harnesses in today’s automobiles are made up of a single, 
twisted pair, and coaxial cable circuits. 
4.4.6 
Capacitive Coupling 
When wire is routed in bundles throughout the vehicle electrical distribution system, 
there is a coupling phenomenon that occurs between the interference source and the 
receptor.  This is referred to as capacitive coupling.  The left figure below shows this 
coupling effect. 
There are ways to reduce capacitive coupling in the wire harness assembly: 
• Lower the receptor impedance. 
• Reduce the source voltage and/or frequency. 
• Reduce the capacitive coupling by separation and orientation in the wire 
assembly. 
• Reduce the coupling by shielding the different wires, where possible. 
 
4.4.7 
Inductive Coupling 
When current (I) flows in a closed circuit, it produces a magnetic flux (φ) which is 
proportional to the current.  The constant of proportionality is called the inductance L. 
The relationship between magnetic flux, current and inductance is expressed in the 
equation below: 
φ= L * I 


### 第 137 页
Yazaki EDS Design Guideline Rev 3 
 
136
The inductance depends on the geometry of the circuit and magnetic properties of the 
medium, (i.e. wire harness bundle routing) containing the field.  The figure on the right 
below shows this inductive coupling. 
 
There are ways to decrease inductive coupling in the wire harness; 
• Decrease the receptor loop area. 
• Design the receptor circuit closer to the ground plane. 
• Design the receptor circuit using twisted pairs. 
• Use Design shielded cable with only one end terminated or grounded. 
4.4.8 
Shielding 
A shield is a partition, metallic in composition, placed between two regions of space.  It 
is used to control the propagation of electric and magnetic fields from one region to 
another.  Shields may be used to contain electromagnetic fields, if the shield surrounds 
the noise source, as shown in the figure below left. 
 
This configuration provides protection for all susceptible equipment located outside the 
shield.  A shield may also be used to keep electromagnetic radiation out of a region, as 
shown in the right figure above.  This configuration provides protection only for the 
specific equipment contained within the shield.  From an overall systems point of view, 
shielding the noise source is more efficient than shielding the receptor. 


### 第 138 页
Yazaki EDS Design Guideline Rev 3 
 
137
It is of little value to make a shield (no matter how well designed) that allows 
electromagnetic energy to enter or exit the enclosure by an alternative path such as 
the connecting cable.  Wiring will pick up noise on one side of the shield and conduct it 
to the other side, where it will be re-radiated.  Care must be taken on the termination or 
end points of a shielded circuit. 
4.4.9 
Coaxial Cable vs. Twisted Pairs 
Coaxial cable is only effective in DC applications in a frequency range up to 
approximately 100MHz. 
Twisted pair typically is only effective in DC applications in a frequency range up to 100 
KHz, after which the filter is saturated. 
Shielded twisted pair may be a more robust design than regular twisted pair in the 
same frequency range up to 100 KHz.  However, it also saturates above this frequency 
range too. 
4.4.10 Braided Shields 
Braided shields provide more shielding in the higher frequency range if such protection 
is required.  Good end point termination is a must otherwise the shield will not perform 
well.  Listed below are two different shields with different methods of termination. 
 
SHIELD TYPE 
FREQUENCY 
TERMINATION
Foil (min 30% 
overlap) 
1 MHz ----> ∞ 
Drain wire 
Round Braid (min 
80% braid) 
DC ----- > ∞ 
360° clamp 
A poor termination of a shield can completely defeat the use of a shield.  Fig 1.5.9 
shows the difference between a good termination and a poor one. 
 
Figure 1.5.9 
 


### 第 139 页
Yazaki EDS Design Guideline Rev 3 
 
138
The poor termination is a result of an unshielded loop area ‘A’ where the circuit’s 
transitions from the shielded bundle to the terminals.  The best results are obtained by 
a 360° contact to the shield, thus insuring minimum radiation exposure. 
4.4.11 Special Considerations 
�Avoid placing bundles that have EMC sensitive circuits within 50 mm of each other. 
This can cause noise (cross talk) between them. (See the diagram below left).  
�If two bundles must cross over, they should do so at right angles to each other to 
minimize the cross talk between them. (See the diagram below right).   
 
To avoid EMC issues, avoid placing harness bundles less than 100 mm away from the 
ignition coil, ignition wires, and distributor cap. 
�Make sure that power feeds such as B+ and “Key-on” circuits are routed away from 
electronic sensitive components such as radios, amplifiers, Engine Control modules, 
etc.    
                                            
� In design review checklist. 


### 第 140 页
Yazaki EDS Design Guideline Rev 3 
 
139
5 Compartment Considerations 
5.1 Modular  
• Instrument panels, interior door panels, center consoles, headliners, package 
trays, seats, and carpeting maybe designed as “modules” having integrated 
interior electrical and mechanical components.   
• Route wiring away from pivot bolts, locating tabs, position adaptors, and module 
fasteners.  Use wiring clips to force disciplined routing to ensure that circuits are 
not damaged during module assembly into the vehicle.   
• Modules may be automatically and/or manually installed into the vehicle during 
assembly.  Large assemblies may cause blind operator assembly environments 
during module docking.  Recommend a module swing/docking study to the OEM 
to determine X, Y, and Z swing tolerances to ensure that wiring will not be 
located near these areas.  A 15mm clearance away from these areas is 
recommended. 
5.2 Engine 
• Reduce the amount of wiring visible to the eye when the engine compartment 
hood is open.  Always consult with the OEM on the use of color and exposed 
wiring.  
• The design engineer should obtain an “Engine Compartment Temperature 
Profile” from the OEM.  Knowing the temperature profile within the engine 
compartment will assist the engineer with proper component selection. 
• Obtaining a “splash profile” may be helpful in determining the best routing path 
or optimum connector locations.  A “splash profile” could also help identify a 
need for a unique component for protection. 
• Always route the wiring bundles using the most efficient pathway to get from 
point-to-point.  When position connectors, bundles, and fasteners be sensitive 
to how water will flow away from connectors and terminals.  Verify connectors in 
position will naturally shed water. 
• 
�In the event a recommendation for hole sizes is required by the OEM before a 
circuit count is available, it is advised that the design engineer recommend that 
the routing passage holes leading to the right and left interior compartments 
have a hole diameter of 60mm with a 78mm grommet clearance.  Recommend 
front fender passage way holes at 55mm minimum and rear fender aprons at 
35mm minimum to allow for future wire passage. 
• �Sheet metal stamping direction should be the same as the wiring installation 
direction.  Routing holes should be as high as possible in the vehicle to reduce 
water and vapor exposure.  Check all passage ways to ensure that post wiring 
assembly components do not press against or “die lock” the wiring.  Consider 
water proof sealing characteristics. 
                                            
� In design review checklist. 


### 第 141 页
Yazaki EDS Design Guideline Rev 3 
 
140
• 
�Drip loops should be used where the wiring transitions from exterior to interior 
locations. 
• �Keep the wire harness 5mm or more away from the radiator.  Routing of the 
harness above the radiator is recommended over routing the harness below it.  
Ensure that vehicle assembly tools will not come in contact with the harness 
during and after its installation. 
• �Route the harness away from the horn.  Anything touching the horn may 
muffle it and reduce its effectiveness.  Also, horns vibrate when sounded.  If the 
horn has unfriendly surfaces, the vibration may cause them to wear through the 
wiring insulation over time, thus causing an electrical short. 
• Avoid routing wiring on any cross member. 
• �If the starter motor is on the opposite side of the engine compartment from the 
battery, choose separate wiring paths for the starter circuits and all other engine 
compartment circuitry. 
• Keep all wiring away from moving mechanisms by at least 25mm. 
• �Do not allow the wire harness to rest on top of the battery.  Keep a minimum 
of 10mm clearance between the battery and the wire harness.  Investigate the 
method of OEM battery installation into the vehicle and compensate for wire 
clearances to protect against installation damage. 
• �Assure at least a 25mm clearance between the battery terminals and the 
hood silencer.  Remember that the hood silencer material will sag over time.  
Request that the silencer material be indented above the battery terminals to 
increase the liner’s structural rigidity. 
• It is recommended that power distribution or junction blocks located in the 
engine compartment be packaged nearest to the battery, and rearward of the 
battery in the vehicle.  
• Avoid, when possible, the confining of hose into the wire harness bundle.   
• �Keep routing 10mm away from all sheet metal parting lines to reduce cut 
circuits caused by sharp edges, and weld flash. 
• Ensure that circuitry is protected against abrasion if resting on sheet metal 
parting lines, or is required to be routed through sheet metal holes during 
vehicle assembly. 
• Standardize (when possible) harness routing between the various engine 
combinations.   See Harness Complexity for more information. 
• Keep in mind that the engine compartment silencer has very large tolerances.  It 
is therefore recommended that silencer dimensions not be used to reference 
wire harness attachment dimensions. 
• �Keep all wiring at least 30mm away from the engine block and exhaust 
manifold. 
                                            
� In design review checklist. 


### 第 142 页
Yazaki EDS Design Guideline Rev 3 
 
141
• Follow sheet metal lines when choosing the routing path.  Do not bridge wiring.  
Reduce visibility and keep wiring low in the vehicle.  
• 
�Always verify wiring bundle clearance with moveable items or mechanisms.  
• Keep wire harness circuits away from ignition (spark plug) wires.  
Electromagnetic Interference (EMI) could be induced in the wire harnesses, 
causing vehicle systems to operate poorly or not at all. 
 
Unique Engine Routing Considerations – Allowing for Engine Rock 
Always verify harness in position with ‘engine rock’.  When transitioning the harness 
bundle between the engine and chassis, build a wave into the bundle that will absorb 
the dynamics of bundle-movement though engine movement.  Avoid straight-line 
bundles in the transition area.  Bending action as a result of engine oscillation should 
be consistent with the picture below. 
 
 
                                            
� In design review checklist. 
Engine  
Eng Oscillation 
Direction
Clip  


### 第 143 页
Yazaki EDS Design Guideline Rev 3 
 
142
 
�No connectors, joints, splices or branching are to be positioned in sliding sections 
between body and engine. 
1) Oil Filter 
The oil filter is a routine maintenance item. Do not route wiring within the 
recommended clearance zone. This zone will include the area necessary to reach the 
oil filter with the removal tool. This could be from the top of the engine with the hood 
open or from the underside similar to what occurs at a quick oil change service facility 
store.  
 
 
 
                                            
� In design review checklist. 
Engine  
Eng Oscillation 
Direction
Clip  


### 第 144 页
Yazaki EDS Design Guideline Rev 3 
 
143
2) Exhaust Systems 
�Allow for ≥ 200 mm spacing in high temperature zones.   
 
5.3  I/P and Console 
• The lower kick panel on the driver’s side should be used as the best place for a 
junction block or fuse panel when major routing strategies are undefined.  In the 
event the parking brake is located in this area, try to request the driver’s side 
lower bolster area. 
• Consider service of fuses, relays and other serviceable parts of a fuse block or 
junction block.  Assure that during service small parts will not be lost behind any 
sheet metal structure if they are dropped by the consumer or service technician. 
• Verify clutch, brake, and accelerator pedal movements clear any wiring bundles 
in the foot-well area. 
• �Allow a minimum clearance of 20mm away from air plenums and duct work.  
This is to avoid the amplified transfer of noise into the passenger area made by 
the potential vibration of wiring against hard surface materials during vehicle 
operation. 
• �Request a 10mm minimum separation between interior trim and wiring and its 
related components.  This practice reduces the likelihood of noise created by 
the wire harness rubbing against the trim, and reduces the likelihood of wire 
becoming pinched during trim installation. 
• �Request a 60mm clearance between the Heater/AC/HVAC unit and the wire 
harness when routing circuits in these areas.  This is to prevent wiring damage 
during Heater/AC/HVAC installation into the vehicle.  In most cases these are 
very large and cumbersome units that are installed blindly by the OEM 
assembly operator. 
• Avoid including non-wiring related components into a standard wire harness 
design. 
                                            
� In design review checklist. 


### 第 145 页
Yazaki EDS Design Guideline Rev 3 
 
144
• 
�Request a 35mm clearance between the wiring and the instrument cluster.  
Consideration should be given to speedometer cables if present. 
• It is recommended that the Instrument Panel (I/P) routing occur rearward 
(towards the rear of the vehicle) of the structural support to allow for easy 
access, wiring address to I/P devices, and serviceability of the wiring.  Ensure 
that for each I/P switch or device visible to the driver or passenger can be 
removed without disassembly of the wiring harness.  Connection to these 
devices need service loops allowing for connection and disconnection to be 
made at some reasonable distance away from its packaged/seated location. 
• �Do not allow any wire circuitry to cantilever (hang horizontal with only one end 
supported) beyond 20mm from the center-of-stem of any clip.  This will help to 
reduce uncontrollable vibration of the wire harness. (Editors Note:  This is a 
good practice to follow throughout the vehicle) 
• Keep wiring at least 10mm away from storage areas, glove boxes, and related 
door/cover hinge areas. 
• The recommended distance between clips is 200~250mm.  If this recommended 
distance cannot be maintained, a trough should be used. 
• During the preliminary design of the Interior compartment one should request 
from the OEM that all fasteners assemble in the same direction.  This reduces 
the possibility of circuitry being pierced during fastener assembly. 
• The design engineer should request from the OEM a minimum clearance 
distance of 10mm from any assembly tools. For automated assembly practices 
a minimum clearance of 15mm is recommended. 
• �Review the I/P harness design at 25mm intervals. 
• Wire used in the steering column may have special flexibility requirements.  
Request these flex testing requirements (life cycle tests) from the OEM for 
harnesses in the steering column.  It is important not to have any splices in any 
portion of the harness subjected to flexing. 
• When possible, integrate all console wiring into the IP wire harness. 
• �Under no circumstances should the wiring ever be visible to the driver and 
passengers anywhere in the passenger compartment.  Look down between the 
seats and console area to assure that no wires or bundles can be seen. 
 
5.4 Body 
5.4.1 
Routing Holes 
Wire routing hole dimensions should be based on bundle sizes and determined only 
after circuit counts are available from the OEM.  Routing holes from interior to exterior 
(dry to wet areas) should be located as high as possible to limit water intrusion. 
                                            
� In design review checklist. 


### 第 146 页
Yazaki EDS Design Guideline Rev 3 
 
145
The harness should be installed through stamped panels in the direction the panels 
are stamped to limit the possibility of snagging on cut-edge burrs. 
Ensure that the harness is not pinched by components after the wiring harness 
installation (i.e. seat track placed over the body harness). 
�Mandate drip loops where wiring must transition from door to interior A/B/C pillars.  
Door to pillar hole centers should require a 50mm minimum offset with the pillar side 
having the higher position.  Less than a 50mm offset will create a water-proofing 
design challenge.  Increasing the offset reduces the possibility of water wicking into the 
vehicle interior. 
If the body harness includes the door wiring and will be routed from the body area 
outward to the doors, request that the door have a larger hole diameter than the hole in 
the pillar.  This will reduce installation efforts to install the door wiring.  However, not all 
doors are assembly in this manner.  Some are assembled on separate lines as 
modules and then mated to the body.  In this instance, a connection will need to be 
made between the body harness and the door harness.  The location of this 
connection will be used to determine the hole sizes required for both the door and the 
body side holes.  Door harness installation methods will vary between OEMs, and even 
vary among assembly plants. 
5.4.2 
Trim 
�During the preliminary design process, request routing provisions between the outer 
skin of each pillar and its respective piece(s) of interior trim.  Determine the clearance 
required using the bundle sizes of the harness being routed under the trim panel. 
Request minimum clearance for all interior trim to be at least 5mm away from wire. 
5.4.3 
Pillars 
�The routing transition from the “A” pillar to the hood line should conform to the shape 
of the aperture. 
�In the event that antenna cables must be routed into a pillar, use separate pillars for 
all other circuitry.  This will help prevent any Electromagnetic Interference (EMI) 
transmitted by the wiring from being broadcast through the radio. 
�If the OEM cannot provide the necessary space for routing the harness outside of 
the pillar, then wire guides should be used to protect against wire chafing during 
vehicle assembly. 
�Ensure that wire routing does not interfere with seat belts and their retractor 
mechanisms.  Use wire attachments to force routing away from these areas.  Request 
a 5mm minimum clearance away from trim fasteners to ensure that wires are not 
pinched or cut during installation. 
                                            
� In design review checklist. 


### 第 147 页
Yazaki EDS Design Guideline Rev 3 
 
146
5.4.4 
Floors 
It is recommended that the engineer avoid routing the wiring over the lower door sill 
area.  Passengers and the driver will step in this area upon entrance and exiting the 
vehicle.  In the event that routing must occur in this area, request a structural trough be 
designed to protect the circuitry. 
�Under no circumstances should the wiring ever be visible to the driver and 
passengers anywhere in the passenger compartment.  Look down between the seats 
and console area to assure that no wires or bundles can be seen. 
Cross-over routing in the passenger compartment should occur near the cross 
member(s).  Ensure that wire retainers are used to force a disciplined routing near any 
seat attachment.  Recommend that asphalt patches be placed onto welded surfaces to 
protect against wire chafing.  Also consider the recommended routing concerns and 
provisions for heater ducts outlined in the I/P section for rear seat heater ducts.  
Ensure that wires do not become pinched by mechanical or motor driven seats and 
seat tracks. 
5.4.5 
Additional Considerations 
�Do not allow the wiring to touch or route unsecured near speakers.   
�In the seat back area keep aware of seat belt retractors.  Recommend that the wire 
passage from the passenger compartment to the trunk area be as high as possible.  
This eases wire assembly into the vehicle.  Recommend a routing hole size as large as 
possible.  Protect the wire harness from sharp surfaces during wire assembly into the 
vehicle.  Request a 5mm minimum clearance between the wire harness and all 
components. 
Request that the fuel door cable and/or trunk release cables not be integrated into the 
body wire harness. 
 
5.5 Doors 
5.5.1 
Door Swing Movement 
�Request service life swing requirements for the door wiring from the OEM.  This is to 
test for wiring strand and insulation durability.  Recommend a door swing study (life 
cycle test) to be sure that the wire harness meets these requirements.  When routing a 
door grommet only tape one end of the door grommet onto the trunk or take out of the 
wire harness.  This allows the circuits to float freely inside the grommet during opening 
and closing of the door.  Consider that the wiring will move in this area and that forced 
disciplined routing using attachment clips in these movement areas will ensure a 
consistent movement away from moving mechanisms.  Protect circuits from chafing.  
Do not use tape as a means of abrasion resistance.  (Editors Note:  Tape should never 
be used as a means to protect against abrasion, sharp edges, or piercing.) 
                                            
� In design review checklist. 


### 第 148 页
Yazaki EDS Design Guideline Rev 3 
 
147
�For vehicle liftgates, sliding doors, 3rd or 5th doors, or trunk lids; care should be 
taken to ensure a smooth routing flow from the vehicle body to the door in question.   
Route the wiring over the hinges.  The recommended clip distance is 100mm.  
Consider a covering such as a trough or shield.  Use convolute as a means to “dress-
up” the bundle.  Avoid using tape as a covering as chafing will occur and wires will 
become exposed to metal. (Editors Note: This area is highly scrutinized by the OEM 
Styling Department.  The end use will certainly see this wiring.  Much care should be 
given to the appearance of the wiring in this area.) 
�Do not allow wire splices to occur between the door and pillar, or any part of the 
wiring that has to swing throughout its consumer usable life.  
Keep all wiring routed away from door moving mechanisms inside the door.  These 
include glass or window regulators, motors, and moving linkages. 
5.5.2 
Attachments 
�Use anti-rotational clips, where necessary, in the door area. 
 
5.5.3 
Wet Areas 
�Do not allow splices in the section of the wire harness that is resting in a wet region 
(between door panels can be a wet area) unless they are protected against water 
intrusion. 
�Do not allow wiring to be routed along the bottom of any door.  Ensure wiring will not 
block condensation drain holes.  Do not position wiring fasteners near condensation 
holes, so as to reduce the chance of mistaken clip insertion into condensation drain 
holes during vehicle assembly. 
It is recommended that a tube-in-grommet be used when water protection is required 
between the door and the pillar.  Follow the same recommended methods that are 
used for passenger and driver doors. 
5.5.4 
Additional Considerations 
�Do not allow the wiring to touch or route unsecured near speakers.   
5.6 Seats 
�Under no circumstances should the wiring ever be visible to the driver and 
passengers anywhere in the passenger compartment.  Look down between the seats 
and console area to assure that no wires or bundles can be seen. 
Cross over routing in the passenger compartment should occur near the cross 
member(s).  Ensure that wire retainers are used to force a disciplined routing near any 
seat attachment.  Recommend that asphalt patches be placed onto welded surfaces to 
                                            
� In design review checklist. 


### 第 149 页
Yazaki EDS Design Guideline Rev 3 
 
148
protect against wire chafing.  Also consider the recommended routing concerns and 
provisions for heater ducts outlined in the I/P section or rear seat heater ducts.  Ensure 
that wires do not become pinched by mechanical or motor driven seats and their seat 
tracks. 
5.7 Trunk 
Request that the fuel door cable and/or trunk release cable(s) not be integrated into 
the body wire harness. 
Consider damage that can occur to the wiring when occupants are loading luggage 
into the trunk, especially under the package tray.  One can easily smash suitcases and 
boxes up against this area causing damage or detachment of the harness.   
5.8 Underbody 
Under chassis routing should be prevented.  All circuitry is highly susceptible to fluid or 
projectile damage.  Hard covering is required to ensure against damage. 
Routing onto frames should be toward the upper most sides, having protection against 
water and away from moving mechanisms.   Do not route harness over or along the 
top of the frame. This is because the body is mounted to the frame via compressible 
mounts.  Damage to the wire harness can occur during the twisting and flexing of the 
body and frame during vehicle operation. 
�Clip spacing is recommended to be 200~250mm. 
�Under no circumstance should wire be visible when in straight view from the side of 
the vehicle.  No wire should drape or sag from the bottom of the vehicle. 
�Trailer tow circuitry should be dressed (positioned) such that water will drain away 
from the Trailer Tow connector.  This means that the wire should route lower than the 
connector as it routes from the connector to the main trunk of the wire harness.  This 
forms a drip loop effect.  The maximum lower loop dimension from the trailer tow 
connector should be 20mm. 
5.9 Headliner 
�Be aware of sunroof drain tubes may need packaging in the pillar areas.  Keep wire 
paths away from these tubes.  Do not allow these tubes to be attached to the wire 
harness. 
�Request a minimum of 5 mm separation from the wire harness and the headliner. 
�Request a 10mm clearance between the wire harness and any Hand Grip Assist 
Handles, Sun Visors, or other Upper Console Storage Areas and their respective doors.  
Use wire harness retainer clips to force disciplined routing away from these areas.  
                                            
� In design review checklist. 


### 第 150 页
Yazaki EDS Design Guideline Rev 3 
 
149
�In the event the headliner is cloth “umbrella” constructed care should be taken to 
assure that wire bundles will not protrude or bulge through the cloth material.  Clip 
spacing should be no greater than 200 mm. 
                                            
� In design review checklist. 


### 第 151 页
Yazaki EDS Design Guideline Rev 3 
 
150
6 EDS Design Process 
The function of creating a power distribution system design is the most critical aspect 
of EDS design. The process may vary greatly depending upon the circumstances of 
the relationship with the OEM, roles and responsibilities and the vehicles design 
process itself. Therefore, a general guideline is included in this text.  The process of 
developing the power distribution system is a collaborative one between the OEM and 
Yazaki whose roles for each step in the process must be defined and/or customized 
per application. 


### 第 152 页
Yazaki EDS Design Guideline Rev 3 
 
151
 


### 第 153 页
Yazaki EDS Design Guideline Rev 3 
 
152
7 Appendix 
7.1 GLOSSARY OF TERMS AND DEFINITIONS  
Accessible: Capable of being removed or exposed without damaging the vehicle or its 
finished interior or exterior surfaces.  
Ampacity: The maximum current, expressed in amperes, that a conductor can carry 
on a continuous basis without exceeding the insulation’s temperature rating (ampere 
capacity).  
Ampere: A unit of electrical current equivalent to a steady current produced by one 
volt applied across a resistance of one ohm.  
ANSI: American National Standards Institute  
Approved: Acceptable to the “authority having jurisdiction.”  
Automatic: A device that is self acting, that operates by its own mechanism reacting to 
an outside stimulant such as application/loss of current, change in current strength, 
pressure, or mechanical configuration.  
Auxiliary battery: A secondary device for the storage of low voltage energy.  
AWG: American Wire Gauge  
Battery: A device for storage of low voltage electrical energy.  
Butt splice: A device used to join two wires together.  
Cable: See “wire.”  
Cable seal: A device to environmentally protect a connection system.  
Cavities: The areas within a connector which hold the terminals. There is one cavity 
for each terminal in the connector.  
Circuit: The complete path of electric current to and from its power source.  
Circuit breaker: A device designed to open a circuit automatically on a predetermined 
over current, without damage to itself, when properly applied within its rating.  
Circuit segment: Any portion of a path of electric current for which a specific purpose 
or function exists.  
CMA: Circular Mil Area  
CMVSS: Canadian Motor Vehicle Safety Standards  
Component: Any material, fixture, device, apparatus or similar item used in 
conjunction with, or that becomes part of, the completed electrical system installation.  


### 第 154 页
Yazaki EDS Design Guideline Rev 3 
 
153
Conductive: Capable of conducting electrical energy.  
Conductor: Anything that provides a path for electric current.  
Conduit: A tube or trough for protecting wires or cables.  
Connection system: A group of parts the purpose of which is to make an electrical 
connection between wires or wire harness assemblies and is mechanically detachable.  
Connector: A molded plastic device that houses one or more terminals and fastens or 
joins one or more conductors together and provides the mechanical connection in the 
connector system.  
Connector seal: A device to environmentally protect a connection system.  
Conversion vehicle: A vehicle that contains the permanent addition to, or modification 
of, any item or system from its original state as supplied by the original equipment 
manufacturer (OEM). This includes the addition of separate, fully independent systems 
that were not present in the vehicle as supplied by the OEM.  
Conversion wiring system: Any wiring or wiring system installed or provided by the 
vehicle modifier.  
Converter: A modifier of an OEM vehicle.  
Core: The conductive portion of a wire (usually copper).  
Core wings: The part of a terminal which is crimped to the wire core to make an 
electrical connection between the wire and terminal.  
CPA - Connector Position Assurance device (Cannot be seated until the connector is 
fully mated) 
CPA lock: Connector position assurance lock; a plastic tab that can be inserted 
through a hole in the inertia lock which provides a redundant connector locking device.  
Dash panel: The partition that separates the engine compartment of a vehicle from the 
vehicle passenger compartment; sometimes referred to as the “firewall.”  
Device: Any item of the electrical system, other than conductors or connectors that 
carry or utilize electrical current to perform a function.  
ECM: Engine Control Module.  
Equipment: Any material, device, appliance, fixture, etc., used as part of, or in 
connection with the electrical system.  
Exposed: Unprotected from inadvertent contact by another component, part or item.  
FMVSS: Federal Motor Vehicle Safety Standards  


### 第 155 页
Yazaki EDS Design Guideline Rev 3 
 
154
Fuse: A specifically rated over current protective device that incorporates a circuit 
opening fusible part that is severed by the heat generated by the over current passing 
through it.  
Fuse block: Two or more fuse holders sharing the same mounting base, but not 
necessarily the same power source.  
Fuse holder: A device in which a single fuse is securely held, providing isolation of the 
source conductor from the distributing conductor.  
GPT: General purpose thermoplastic; PVC insulated wire.  
Ground: The negative terminal of the battery or any conductor or metal current 
carrying part of the vehicle chassis that is at the same potential.  
Grounded: A conducting connection between an electrical circuit or component and 
ground.  
GXL: General purpose cross-linked (polyethylene insulated wire).  
Harness: A grouping of electrical conductors provided with a means to maintain their 
grouping.  
Incomplete vehicle: An assemblage consisting, as a minimum, of frame and chassis 
structure, powertrain, steering system, suspension system, and braking system, to the 
extent that those systems are to be part of the completed vehicle, that requires further 
manufacturing operations.  
Indexing feature: Mechanical feature of a connector (usually a tab and slot) which 
allows connectors to be mated in only one way.  
Inductive load: Any device (motors, magnetic solenoid, etc.) that utilizes a process by 
which electrical energy is used to create magnetic forces.  
Inertia lock: The locking device on a connector that keeps connectors together once 
mated.  
Insulated: Protected with a nonconductive coating.  
Isolated circuitry: A wiring system with distribution and over current protection totally 
separate and independent from the vehicle’s OEM wiring system.  
Locking tang: Metal tab(s) on a terminal that locks the terminal in the connector cavity.  
Low voltage: An electromotive force rated 24 volts, nominal or less, generally 12 volts 
in automotive applications.  
OEM: Original Equipment Manufacturer; in this case, General Motors.  
Ohm: A unit of electrical resistance equal to the resistance of a circuit in which a 
potential difference of one volt produces a current of one ampere.  


### 第 156 页
Yazaki EDS Design Guideline Rev 3 
 
155
Open circuit: Condition by which electrical continuity is disrupted or broken in an 
electrical circuit.  
Overcurrent: Any current that exceeds the rated current of equipment or ampacity of a 
conductor. Overcurrent may result from overload, short circuit or ground fault.  
Overcurrent protection device: A device, such as a fuse or circuit breaker, designed 
to interrupt the circuit when the current flow exceeds a predetermined value.  
Parasitic load: A small, continuous electrical draw on the battery.  
PCM: Powertrain Control Module.  
Pigtail: External conductors (wire leads) that originate within an electrical component 
or device.  
PLR - Primary Lock Reinforcement (Backs up the primary terminal lock). May detect 
but not correct partially seated terminals.  
Power source: The specific location or point that electrical current is obtained to 
supply the conversion wiring system.  
Rating: Value that determines the current or voltage carrying capacity of a conductor 
or device.  
Rating maximum: The point of highest current that a circuit breaker or fuse is 
intended to interrupt at under specified test conditions.  
Ring terminal: Part used to connect wiring leads to threaded studs or directly to sheet 
metal. Also see “Terminal.”  
RVIA: Recreation Vehicle Industry Association  
SAE: Society of Automotive Engineers  
Sealed: Closed or secured tightly for protection from environmental factors such as 
moisture or noxious fumes.  
Secondary lock or TPA lock: Terminal position assurance lock; a separate or hinged 
part of a connector which prevents terminals from pulling out of the back of the 
connector.  
Short circuit: A connection of comparatively low resistance accidentally or 
intentionally made between points in an electric circuit.  
SIR: Supplemental Inflatable Restraint (air bag system).  
Spacer – Can be a TPA or PLR 
Splice: A means of joining one or more conductors (wires).  
Splice clip: A device used to facilitate splicing three or more wires.  


### 第 157 页
Yazaki EDS Design Guideline Rev 3 
 
156
SVM: Special Vehicle Manufacturer. The manufacturer or converter that installs 
additional equipment or modifies any item or system from its original state as supplied 
by the original equipment manufacturer (OEM).  
Terminal: A metal device at the end of a wire or device which provides the electrical 
connection. Terminals are referred to as either male or female.  
TPA lock: Terminal position assurance lock. Prevents a terminal from backing out of a 
connector.  
Thermally protected: A device provided with a means of protection that opens the 
circuit’s source of current when excessive heat is generated.  
TPA - Terminal Position Assurance device (Will seat partially seated terminals) 
Unsealed: Not closed or secured for protection from environmental factors.  
VCM: Vehicle Control Module.  
Volt: A unit of electromotive force equal to a force that, when steadily applied to a 
conductor with a resistance of 1 ohm, produces a current of 1 ampere.  
Waterproof: Constructed to prevent moisture from entering the enclosure under 
specified test conditions.  
Weatherproof: See “Waterproof.”  
Wedge – Can be a TPA or PLR 
Wire: An electronically conductive core material (usually made of copper) covered with 
a nonconductive insulating material. Also referred to as “lead” or “cable.”  
Wire nut: A twist on wiring connector/insulator not designed for automotive use.  


### 第 158 页
Yazaki EDS Design Guideline Rev 3 
 
157
7.2 SAE - ISO Wire Conductor Area Comparison Chart 
  
SAE - ISO Wire Conductor Area Comparison 
Chart 
(Note: SAE is not equivalent to ISO)  
  
SAE 
ISO 
Approximate 
AWG Size 
SAE Conductor 
Name* 
Min Conductor 
Area in Metric 
size (mm2) 
ISO Conductor 
Name* 
Min Conductor 
Area (mm2) 
based on Max. 
resistance 
24 
0.22 
0.205 
0.22 
0.2033 
22 
0.35 
0.324 
0.35 
0.3169 
20 
0.5 
0.508 
0.50 
0.4647 
  
  
  
0.75 
0.6980 
18 
0.8 
0.76 
  
  
16 
1.0 
1.12 
1 
0.9319 
  
  
  
1.5 
1.3576 
14 
2.0 
1.85 
2 
1.8303 
  
  
  
2.5 
2.2686 
12 
3.0 
2.91 
3 
2.8034 
  
  
  
4 
3.6605 
10 
5.0 
4.65 
5 
4.3759 
  
  
  
6 
5.4908 
8 
8.0 
7.23 
  
  
  
  
  
10 
9.4731 
6 
13 
12.1 
  
  
  
  
  
16 
14.8629 
4 
19 
18.3 
  
  
  
  
  
25 
23.2046 
2 
32 
31.1 
  
  
  
  
  
35 
32.7154 
1 
40 
38.1 
  
  
1/0 
50 
48.3 
50 
46.8505 
2/0 
62 
59.8 
  
  
  
  
  
70 
66.5676 
3/0 
81 
77.6 
  
  
  
  
  
95 
87.9643 
4/0 
103 
98.5 
  
  
  
  
  
120 
112.6863 
  
(SAE J1127 & 1128, 2005) 
ISO 6722:2006(E) 
 
 
 
 
 
 
 
 
 
 
 
SAE only 
 
 
 
 
SAE and ISO 
 
 
 
 
ISO only 
 
 
 
 
 
 
 
 
*Note: These are identification Names only, not dimensions.   
They are only approximate to the actual size. 
 


### 第 159 页
Yazaki EDS Design Guideline Rev 3 
 
158
7.3 Typical Coax Cable Properties 
 
Cable Type 
Impedance 
Outside Diameter 
Uses 
RG-58 
50 
5 mm 
Antenna, XM radio 
RG-59 
75 
6 mm 
Video, DVD, Camera 
RG-62 
93 
6 mm 
Satellite  
RG-174 
50 
3 mm 
Antenna, XM radio 
RG-179 
75 
3 mm 
Video, DVD,  Camera 
RG-316 
50 
3 mm 
High temp, high performance
 


### 第 160 页
Yazaki EDS Design Guideline Rev 3 
 
159
7.4 Covering Application and Strategy Chart 
Please refer to the ESA Web site for the latest strategy; the following reference charts 
are current as this guideline was released. 
 


### 第 161 页
Yazaki EDS Design Guideline Rev 3 
 
160
7.4.1 
85 °C 
 
7.4.2 
125 °C 
 


### 第 162 页
Yazaki EDS Design Guideline Rev 3 
 
161
7.4.3 
150 °C 
 
7.4.4 
170 °C -200 °C 
 
 


### 第 163 页
Yazaki EDS Design Guideline Rev 3 
 
162
7.5 YPES Specification Chart 
YPES 
Specification 
Previous 
Standard 
Description 
YPES-16-
083 
MS-DB56 
Thermoplastic – Polyolefin – Heat 
Shrinkable Tubing 
YPES-16-
084 
MS-8288 
Cable – Primary  - Thin Wall Cross-
linked Polyethylene Insulated 
YPES-16-
085 
MS-7889 
Cable – Primary – Thin Wall Polyvinyl 
Chloride Insulated 
YPES-16-
086 
MS-DC16 
Plastic – Electrical Polyvinyl Chloride 
(PVC) Primary Wire Insulation 
YPES-16-
087 
MS-CH71A 
Tape – Electrical Applications – Flame 
Retardant – Polymeric Coated Cloth – 
Pressure Sensitive.  Rated 105°C. 
YPES-16-
088 
MS-DC110 
Plastics - Elastomers - Electrical - 
Thermosetting Cross-linked Polyolefin 
Primary Wire Insulation 
YPES-16-
089 
MS-3528 
Soldering Fluxes – Non-corrosive (PN 
2278003) and Non-Hazardous, Water 
Washable (PN 4187077) 
YPES-16-
090 
MS-9469 
Grease -760G 
YPES-16-
091 
n/a 
Drain Wire 
YPES-16-
092 
MS-9502A 
Cable – Primary – High Temperature 
Thin Wall Cross-linked Polyethylene 
Insulated 
YPES-16-
093 
PF-6351 
Weatherseal Connectors – Primary 
Wiring 
YPES-16-
094 
PF-6494 
Wire Harness Clip Attachment 
YPES-16-
097 
PF-3771 
Automotive Wiring Harness - Circuit 
Test 
YPES-16-
098 
n/a 
ES-2001- Fleece Tape Performance 
Specification 
YPES-16-
099 
MS-DC13A 
Tape – Wiring/Electrical Insulation - Dry 
Vinyl 


### 第 164 页
Yazaki EDS Design Guideline Rev 3 
 
163
YPES-16-
100 
PS-8934 
Ledger Format for Wiring Harness 
Fabrication  
YPES-16-
101 
PS-9497 
Ultrasonic Welding of Automotive 
Wiring Harnesses 
YPES-16-
102 
PF-9882 
Stamped Battery Terminals 
YPES-16-
107 
MS-9502B 
Wire - Primary – 150°C Thin Wall 
Cross-Linked Polyethylene Insulated 
YPES-16-
108 
MS-9502C 
150°C Thin Wall Cross-Linked 
Polyethylene Insulated 
Flexible Battery Cable 
YPES-16-
110 
MS-CH62 
Tape – Cotton Backed – Friction 
YPES-16-
111 
MS-3450 
Cable – Primary – Thermoplastic 
Insulated 
YPES-16-
113 
YNA ES-
1002 
Cable - Fusible Link - Primary - Thick 
Wall Flame Resistant 
YPES-16-
119 
n/a 
MPP COT Raw Material WPP-C1CF2, 
PP+TPE+20%Talc 
YPES-16-
120 
MS-2584 
Denatured Ethyl Alcohol  
YPES-16-
121 
MS-CH71C 
Tape – Electrical Applications – With 
Burn Rate – Polymeric Coated Cloth – 
Pressure Sensitive.  Rated 85°C. 
YPES-16-
122 
ES-3001 / 
PF-10105 
Convoluted Tubing Application 
YPES-16-
123 
ES-3002 
Sleeving Performance Spec. 
YPES-16-
127 
MS-CH28 
Tape - Black Crepe Paper Backing - 
Pressure Sensitive 
YPES-16-
130 
MS-CH75 
Tape – Cellulose Printable – Pressure 
Sensitive 
YPES-16-
134 
PS-7478 
Wiring Harness – Application of 
Convolute/Scroll 
YPES-16-
136 
PS-903 
Wiring Harness – Attaching Tape 


### 第 165 页
Yazaki EDS Design Guideline Rev 3 
 
164
YPES-16-
140 
MS-DC-13B 
Tape – Wiring/Electrical Insulation - 
Adhesive Vinyl Embossed 
YPES-16-
142 
MS-DC-13D 
Tape – Wiring/Electrical Insulation - 
Adhesive Vinyl Embossed 
YPES-16-
148 
n/a 
Tinned Wire - Thin Wall Cross-linked 
Polyethylene (for safety circuits) 
YPES-16-
150 
ES-2002 
Tape – Splice 
YPES-16-
152 
ES-2001 
Fleece Tape 
Tape - Electrical Applications-
Performance Specification Fleece BSR 
Tape 
YPES-16-
157 
n/a 
High Voltage (600v) Wire and Cable 
Specification - XLPE  
YPES-16-
158 
n/a 
Heat-shrink Tubing - Single Wall- 
Flexible 
YPES-16-
162 
PS-406 
Soldering of Electrical Cables 
YPES-16-
163 
MS-6463 
Solder – Tin-Lead 
YPES-16-
164 
MS-6470 
Solder – Tin-Lead – Flux Cored 
YPES-16-
165 
PS-1678 
Wiring Assemblies (Low Tension) – 
Circuit Splices 
YPES-16-
170 
PF-5139 
Battery Cable  Assembly 
YPES-16-
174 
PS-
PLATING 
Zinc, Cadmium and Cadmium-Tin - 
Mechanical and Electroplated 
YPES-25-
08-206 
YNA ES-
1001 
Cable - Flexible - Low Voltage - Primary 
Wire 
 


### 第 166 页
Yazaki EDS Design Guideline Rev 3 
 
165
7.6 Design Highlight 
This section illustrates past lesson learned in design and real issues arise from design 
oversight. Through detail illustration and explanation for each case; hopefully, future 
such mistake can be prevented. 


### 第 167 页
Yazaki EDS Design Guideline Rev 3 
 
166
7.6.1 
Vibration and Fretting Corrosion 
 
 
The general rule of thumb is to have wire harness attachments every 200 mm, but 
there are cases where this rule needs special attention. For example, if you are routing 
in the interior of the vehicle, under the carpeting, the need to have attachment points 
every 200 mm is not necessary, as where is the harness to go, but say in the engine 
compartment, it is a much different issue. In this example, there is a takeout from the 
main bundle that goes down to an engine sensor. The distance t this sensor from the 
bundle is 300 mm’s . As you can imagine the engine rocks and vibrates all the time the 
vehicle is in motion. The takeout shown here will also vibrate moving back and forth as 
indicated with the red arrows. This motion is transferred down the takeout to the 
connector and to the terminals inside the connector. If there is not a special design 
strain relief cap on the back of the connector, the terminals will experience vibration. 
This vibration between the terminal in the wire harness connector and the mating 
terminal on the sensor will cause a condition known as fretting corrosion. Over time, 
this fretting corrosion will remove the plating material on the contact area of the 
terminals (see picture in green above) and a build up of corrosion will occur. This 
corrosion will cause higher then normal resistance in the circuit. Sensor circuits are 


### 第 168 页
Yazaki EDS Design Guideline Rev 3 
 
167
highly susceptible to higher then normal resistance and will cause a false reading to 
the onboard computer which will at a minimum cause the engine light to come on and 
worst, cause an engine stall.   
Below are some options should your design have such a condition.  
 
1. Create an attachment point very near the sensor connection to prevent the 
movement.   
2. If available, use a strain relieve cap to stifle the vibration before it enters the 
connector.  
3. Add a stiffening rod, attaching it on the connector to 75mm from the back of the 
connector and taping the circuits to it along the entire length. 
 


### 第 169 页
Yazaki EDS Design Guideline Rev 3 
 
168
7.6.2 
Bare Terminals Tape Back on Optional Content 
 
 
When design an optional content wire harness, bare terminals sometime are taped 
back on the harness and insert in future as option. During installation of the harness or 
normal use, the bare terminals could touch each other. It leads to possible short circuit 
or un-intentional event.  
Case: Issue No: 213789 CSA L-20 Door ajar lamps stay on with key removed. 
The root cause was two bare terminal circuits are taped back on Police vehicle body 
harnesses, and they were touching each other. 
Solution: 
3) Circuits are to be taped back with a minimum 20mm gap between terminals. 
4) Any circuits that need to be taped back to the bundle without an insulator will be un-
terminated and un-stripped and taped individually 
5) Detailed instructions are needed when "out of the ordinary" designs are added to 
the print 


### 第 170 页
Yazaki EDS Design Guideline Rev 3 
 
169
 


### 第 171 页
Yazaki EDS Design Guideline Rev 3 
 
170
 
 
 
 
 
This document contains the EDS (Electrical Distribution Systems) Design 
Guidelines (the “Guidelines”). The Guidelines contain trade secrets and 
confidential and proprietary information of Yazaki North America, Inc. 
(“YNA”) and must be held in strictest confidence in accordance with the 
terms of the Proprietary Information Acknowledgement Form. 
 
 
 
 
 
End of Document 

