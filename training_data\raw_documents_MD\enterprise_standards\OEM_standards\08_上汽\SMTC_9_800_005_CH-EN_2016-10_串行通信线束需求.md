# SMTC_9_800_005_CH-EN_2016-10_串行通信线束需求.pdf

## 文档信息
- 标题：
- 作者：<PERSON>ai Wei
- 页数：13

## 文档内容
### 第 1 页
 
© 上汽集团版权所有  秘密文件 注意保密 Copyright SAIC MOTOR Confidential 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
发  布
Issue 
 
上汽集团技术中心技术标准化委员会  
Technical Standardization Committee of  SAIC MOTOR Technical Center            
 
 
 2016-11-01  实施/Implementation 
  2016-10-31  发布/Issue 
 
 
 
 
串行通信线束需求 
Wiring harness requirements for serial communications 
 
SMTC 
上海汽车集团股份有限公司技术中心企业标准 
Enterprise  Standard  of  SAIC  MOTOR  Technical  Center 
 
 
 
SMTC 9 800 005—2016（V2） 
代替 SMTC 9 800 005—2013（V1） 
 


### 第 2 页
SMTC 9 800 005—2016（V2） 
I 
© 上汽集团版权所有  秘密文件 注意保密 Copyright SAIC MOTOR Confidential 
 
目次/Contents 
前言 Foreword ...................................................................................................................................... II 
1   范围 Scope ..................................................................................................................................... 1 
2   规范性引用文件 Normative References ......................................................................................... 1 
3   缩略语 Abbreviated Terms ............................................................................................................. 1 
4   CAN 线束 CAN Harness ................................................................................................................ 2 
4.1  网络拓扑结构 Network Topology .................................................................................................. 2 
4.2  终端要求 Termination Requirement .............................................................................................. 2 
4.3  节点连接要求 Node Connection Requirements ........................................................................... 3 
4.4  线束设计要求 Harness Design Requirements .............................................................................. 4 
5   LIN 线束 LIN Harness .................................................................................................................... 6 
5.1  LIN 网络拓扑结构 LIN Topology .................................................................................................... 6 
5.2  线束设计要求 Harness Design Requirements .............................................................................. 7 
6   KLine 线束 K-Line Harness ............................................................................................................ 7 
6.1  KLine 网络拓扑结构 K-Line Topology ........................................................................................... 8 
6.2  线束设计要求 Harness Design Requirements .............................................................................. 8 
7   汽车诊断通信接口 Vehicle Diagnostic Connector .......................................................................... 8 
7.1  接插件定义 Connector Definition .................................................................................................. 9 
7.2  接插件位置 Location Of Connector ............................................................................................... 9 
8   修订信息 Release and revisions .................................................................................................... 9 
 


### 第 3 页
SMTC 9 800 005—2016（V2） 
II 
© 上汽集团版权所有  秘密文件 注意保密 Copyright SAIC MOTOR Confidential 
 
 前    言 
为规范网络线束设计要求，特制定此标准。 
本标准是对 SMTC 9800 005—2013（V1）
《串行通信线束需求》的修订,自本标准实施之日
起废除 SMTC 9 800 005—2013（V1）。 
本标准与 SMTC 9 800 005—2013（V1）
相比主要变化如下： 
——修改不带终端电阻或高阻抗的节点连
接总线的方式; 
——修改屏蔽线要求； 
——修改绞率最小值和标称值； 
——增加导体截面积注释； 
——修改 CAN 线束颜色； 
——修改诊断接口针脚定义。 
 
请注意本文件的某些内容可能涉及专利，上
汽集团不承担识别这些专利的责任。 
 
当中英文产生疑义时，以中文为准。 
本标准电子电器分标委提出。 
本标准由 SMTC 技术标准化委员会批准。 
本标准由标准化工作组负责标准化审核及
归口管理。 
本标准由标准化工作组负责标准化审核及
归口管理。 
 
本标准起草部门：电子电器部。 
本标准主要起草人：周三国、刘亚、吴平友 
 
本标准于 2010 年 07 月 19 日首次批准发布，
2010 年 07 月 21 日实施。本次为第三次修订。 
本标准所代替标准的历次版本发布情况为：
——SMTC 2 800 006—2010（V1）； 
——SMTC 2 800 006—2012（V2）； 
——SMTC 9 800 005—2013（V1）。 
 
 
 
 
Foreword 
This standard is established for network 
harness design.  
This standard is a revision for SMTC 9 800 
005—2013(V1), Wiring Harness Requirements 
for Serial Communications. SMTC 9 800 
005—2013(V1)will be abolished from the 
implementation date of this released standard. 
The main changes between this standard and 
SMTC 9 800 005—2013(V1)are as follows: 
——Revise 
the 
node 
without 
terminal 
resistance or high impedance connecting bus 
——Revise the requirement of shield; 
——Revise the minimum and nominal values of 
twist rate ; 
——Add notes for conductor section; 
——Revise CAN cable color; 
——Revise DLC connector PIN definition . 
Please note that some contents in this 
document may relate to patent rights, which 
SAIC MOTOR shall not be held responsible for 
identifying any or all such patent rights. 
 
In the event of a conflict between Chinese and 
English, Chinese shall take precedence. 
This standard was proposed by Electronic & 
Electric sub-committee. 
This standard was approved by the SMTC 
Technical Standardization Committee. 
The 
Standardization 
Working 
Team 
is 
responsible for auditing and manage this 
standard. 
This standard is under the management of 
Standardization working team. 
The drafting department of this standard is: 
Electronic & Electric Dept. 
The main drafters of this standard are: Zhou 
Sanguo, Liu Ya,Wu Pingyou. 
This standard was first published on Jul. 
19,2010 and implemented on Jul. 21, 2010. 
This is the third revision. 
This standard will replace the revision of: 
——SMTC 2 800 006—2010(V1); 
——SMTC 2 800 006—2012(V2); 
——SMTC 9 800 005—2013(V1). 
 


### 第 4 页
SMTC 9 800 005—2016（V2） 
1 
© 上汽集团版权所有  秘密文件 注意保密 Copyright SAIC MOTOR Confidential 
 
串行通信线束需求 
 
Wiring Harness 
Requirements for Serial 
Communications 
1 范围 
本标准规定了CAN网络、LIN网络、K线线
束以及诊断接口设计的要求。 
本标准适用于SMTC所有车型。 
供应商负责的私有网络，其标准不得低于本
标准要求。 
售后、诊断监测工具必须通过诊断接口连接
到网络上。 
 
1  Scope  
This standard specifies the requirements of 
CAN, LIN, K-line (diagnostic) harness and 
diagnostic connector. 
This standard applies to all SMTC vehicle 
programs.  
The private network, which is responsible by 
supplier should as a minimum comply with this 
standard. After-sale or diagnostic scan tools 
should only be connected to diagnostic 
connector. 
2 规范性引用文件 
下列文件对于本标准的应用是必不可少的。
凡是注日期的引用文件，仅注日期的版本适用于
本文件。凡是不注日期的引用文件，其最新版本
（包括所有的修改单）适用于本标准。 
 
 
2  Normative References 
The following referenced documents are 
indispensable for the application of this 
standard. For dated references, only the 
edition cited applies. For undated references, 
the latest edition of the referenced standard 
(including any amendments) applies.  
SMTC 3 861 003 汽车用低压导线试验规
范 
ISO 11898-2:2003道路车辆电子控制模块
局域网第2部分：高速媒介访问单元 
ISO 15031-3.6 诊断接插件和电路 
LIN 1.3 LIN规范 
LIN 2.1 LIN规范 
SAE J2284-1速率125 kbps高速CAN 在车
辆上的应用 
SAE J2284-2速率为250 kbps高速CAN 在
车辆上的应用 
SAE J2284-3速率为500 kbps高速CAN 在
车辆上的应用 
SAE J2602-1 LIN网络在车辆上的应用 
 
 
SMTC 3 861 003 Low-voltage for automobile 
cable test procedure  
ISO 11898-2:2003 Road vehicles – Controller 
area network (CAN) Part 2: High-speed 
medium access unit. 
ISO 15031-3.6 Diagnostic Connector and 
Electrical Circuits 
LIN 1.3 LIN Specification Package 
LIN 2.1 LIN Specification Package 
SAE J2284-1 High Speed CAN (HSC) for 
Vehicle Applications at 125 kbps 
SAE J2284-2 High Speed CAN (HSC) for 
Vehicle Applications at 250 kbps 
SAE J2284-3 High Speed CAN (HSC) for 
Vehicle Applications at 500 kbps 
SAE 
J2602-1 
LIN 
Network for Vehicle 
Applications 
3 缩略语 
 
3  Abbreviated Terms 


### 第 5 页
SMTC 9 800 005—2016（V2） 
2 
© 上汽集团版权所有  秘密文件 注意保密 Copyright SAIC MOTOR Confidential 
 
CAN               控制器局域网        Controller Area Network 
ECU               电子控制单元        Electronic Control Unit 
HS-CAN            高速 CAN           High Speed CAN 
GND               地                  Ground 
LIN                局域互联网          Local Interconnect Network 
MS-CAN           中速 CAN           Medium Speed CAN 
OBD               车载诊断            On-Board Diagnostics 
SMTC              SAIC 技术中心       SAIC Motor Technical Center 
4 CAN 线束 
 
4  CAN Harness  
4.1 网络拓扑结构 
CAN网络拓扑采用线性结构，参见图1（如
果节点采用菊花链连接方式，须由网络工程师确
认）。 
多于1个在线节点的CAN网络，CAN网络至
少包含2个节点，至多包含16个节点(即n≤16)。
CAN总线网络拓扑图参见图1。带有终端电阻的
节点称为―终端节点‖，其他节点称为―分支节点‖。
终 端位于 主干线的 两端 ， 终端电 阻阻值 为
120 Ω ，阻值公差为±1 %，额定功率为1/2 W。 
对于单个在线节点网络，其拓扑图参见图2。 
离线外接设备不带终端电阻。 
 
 
4.1  Network Topology 
The topology of a CAN network should be a 
linear structure (Daisy-chaining should be 
confirmed by network engineer).  
CAN network containing more than one node 
on-board The topology can support the 
transfer of information between a maximum of 
16 nodes (i.e. n≤16), and a minimum of 2 
nodes. CAN network topology is shown in 
Figure 1. The node with termination resistor is 
the terminal node and other nodes are stub 
nodes. The two terminations shall be located 
at each end of the bus backbone. The 
termination is 120 Ω  with the tolerance of 
±1 % and with power ratings of 1/2 W. 
CAN network containing only one node 
on-board, the topology is shown in Figure 2. 
The off-board tool must not provide a 
termination. 
4.2 终端要求 
如果采用图1拓扑结构，终端电阻通过接插
件放置在线束上，且应放置在主干线末端的最远
的在线节点附近。 
 
 
 
 
 
 
4.2  Termination Requirement 
The termination is located on the wiring 
harness via an additional connector，if the 
topology in Figure 1 is applied. Termination 
shall be placed adjacent to the farthermost 
On-Board node which is located at the end of 
the bus backbone. 


### 第 6 页
SMTC 9 800 005—2016（V2） 
3 
© 上汽集团版权所有  秘密文件 注意保密 Copyright SAIC MOTOR Confidential 
 
  
(终端)节点
(Terminal)Node1
(终端)节点
(Terminal) Node n
(分支)节点3
(Stub)Node3
(分支)节点2
(Stub)Node2
诊断接插件(n-2)
Diagnostic
Connector (n-2)
(分支)节点(n-1)
(Stub) Node (n-1)
d
l1
L
D
离线工具
Off-Board tool
l3
l2
 
图 1 多个在线节点 CAN 网络拓扑结构图 
Figure 1 CAN network topology with more than one node on-board  
 
注/Notes:  
L 终端节点间距/Cable length between terminal nodes 
l1 分支节点线长/Cable stub length 
l2 诊断接插件到主干线的线长/Stub length of diagnostic connector 
l3 从接插件连接到离线工具的线长/Cable length from the connector to off-Board tool 
d 任意2分支节点在主干线上的间距/Distance between any two conjunctions of stub nodes  
D 任意2分支节点间距/Distance between any two stub nodes 
 
(终端)节点
(Terminal)Node
诊断接插件
Diagnostic
Connector
离线工具
Off-Board tool
l3
l'2
 
图 2 单个在线节点 CAN 网络拓扑结构图 
Figure 2 CAN network topology with only one node on-board  
 
注/Notes:  
     l'2 诊断接插件到在线节点的线长/ Cable length from the Diagnostic connector to on-Board ECU 
l3 从接插件连接到离线工具的线长/Cable length from the connector to off-Board tool 
4.3 节点连接要求 
 
 
 
4.3  Node Connection Requirements 
 


### 第 7 页
SMTC 9 800 005—2016（V2） 
4 
© 上汽集团版权所有  秘密文件 注意保密 Copyright SAIC MOTOR Confidential 
 
a) 
带终端电阻或高阻抗（例如：2.6 kΩ）
的节点在同一接插件内仅与总线有 1
路 CANH、CANL 接口，参见图 3a。 
b) 
不 带 终 端 电 阻 或 高 阻 抗 （ 例 如 ：
2.6 k Ω ）的节点除了以上接线方式外
还可以选择另一种方式：在同一接插件
内与总线有 2 路 CANH、CANL 接口。
如图 3b 所示，节点内应提供接线端的
连接，且其连接应尽可能的靠近接插件
PIN 针。 
a) 
The nodes with termination or high 
impedance (e.g,. 2.6 kΩ) have only one 
terminal for CAN_H or CAN_L in a single 
connector as part of node interface to 
CAN bus, refer to Figure 3 a. 
b) 
The nodes without termination or high 
impedance (e.g. 2.6 kΩ ) can also choose 
another way： provide two terminals for 
CAN_H or CAN_L in a single connector as 
part of node interface to CAN bus. 
Connection between each bus signal 
terminals shall be provided within the 
node as close to the connector pins as 
possible as shown in Figure 3b. 
Transceiver
CAN_H
CAN_L
CAN_L
CAN_H
CAN_H
CAN_L
Termination
Or
High 
impedance
Transceiver
b
a
Node
Node
 
图 3 连接图 
Figure 3 Connections 
4.4 线束设计要求 
a) 
一般采用非屏蔽双绞线，对于特定干扰
或区域可采用屏蔽双绞线，物理参数参
见表 1。 
 
4.4  Harness Design Requirements 
a) 
Normally unshielded twisted pair is used, 
and shielded twisted pair can be used for 
specific interference or area. physical 
parameters refer to table1. 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 8 页
SMTC 9 800 005—2016（V2） 
5 
© 上汽集团版权所有  秘密文件 注意保密 Copyright SAIC MOTOR Confidential 
 
表 1 物理参数 
Table 1 Physical media parameters  
符号 
Symbol 
最小值 
Minimum 
 标称值 
Nominal 
最大值 
Maximum 
单位 
Units 
备注 
Note 
Z(阻抗Impedance) 
108 
120 
132 
Ω 
—— 
导线电阻 
RLENGTH 
—— 
—— 
70 
mΩ/m 
—— 
延迟时间/TDELAY 
—— 
—— 
5.5 
ns/m 
—— 
绞率/RATETWIST 
40 
45 
50 
Twists/m 
参见图4 
Refer to Figure 4 
导体截面积a、b 
Conductor Section 
0.3 
 
0.5 
mm2 
—— 
a. 
如果选择其他线径，电气参数需满足《SMTC 3 861 003 汽车用低压导线试验规范》，且传输信号质量须满足
CAN 网络整车信号质量要求。 
If choose other diameters, electrical parameter should meet 《SMTC 3 861 003 Low-voltage for automobile 
cable test procedure》, and the signal quality shall meet the requirement of CAN network signal quality. 
b. 
同一网段线径一致。 
The conductor section in the same network shall be consistent. 
 
b) 
接插件端未双绞的长度（A）尽可能短，
不超过 40 mm。参见图 4。 
c) 
分支节点到主干线上的未双绞的长度
（B）尽可能短，不超过 40 mm。参见
图 4。 
d) 
分支节点线长（l1）尽可能短，最长不
超过 1 m。 
e) 
任意两节点在主干线上的最小间距(d)
是 0.1 m。 
f) 
HS-CAN 网络终端节点最大间距（L）
30 m；MS-CAN 网络终端节点最大间
距（L）50 m。 
g) 
终端节点间距（L）大于其它任意 2 节
点的间距。 
h) 
诊断接插件到主干线的线长（l2）尽可
能短，最长不超过 1 m。从接插件连接
到离线工具（如诊断工具，网络测试工
具）的线长（l3）也要尽可能短，最长
不超过 5 m。 
i) 
所有节点间距（D）不能相等。 
j) 
分支节点到主干线上的长度(含 l1 和 l2)
不能相等。 
 
b) 
The untwisted portion of the cable at the 
ECU connector (A) shall be less than 40 
mm. Refer to Figure 4. 
c) 
The untwisted portion of the cable at the 
stub wiring harness (B) shall be less than 
40 mm. Refer to Figure 4. 
d) 
 ―Stub length‖ (l1) must be less than 1 m, 
and should be kept as short as possible. 
e) 
The minimum distance (d) between any 
two nodes on bus backbone is 0.1 m. 
f) 
The maximum cable length (L) between 
terminations is 30 m for HS-CAN, 50 m for 
MS-CAN. 
g) 
The 
cable 
length 
(L) 
between 
the 
terminations should be longer than that 
between any other two nodes. 
h) 
The stub length (l2) of the diagnostic 
connector should be as short as possible, 
with a maximum length of 1m. The cable 
from the connector to off-Board tool (e.g. 
diagnostic tool, test tool) is also should be 
as short as possible, with a maximum 
length (l3) of 5 m. 
i) 
The wiring harness distance (D) between 
ECUs should not be the same. 
j) 
The cable stub length (including l1&l2) 
should not be the same. 


### 第 9 页
SMTC 9 800 005—2016（V2） 
6 
© 上汽集团版权所有  秘密文件 注意保密 Copyright SAIC MOTOR Confidential 
 
k) 
诊断接插件到在线节点的线长(l'2) 尽
可能短，最长不超过 5 m，最短不小于
0.1 m。 
l) 
线束颜色定义参见表 2。 
k) 
The stub length (l'2) of the diagnostic 
connector should be as short as possible, 
with a maximum length of 5 m and with a 
minimum length of 0.1 m. 
l) 
CAN cable color, refer to table 2.  
分支节点接插件
Connector of Stub Node 
A
B
...
...
终端节点接插件
Connector of Terminal 
Node
A
...
...
One twist
 
图 4 未绞线长及 Twist 
Figure 4 Untwisted cable length and twist definition 
 
表 2 线束颜色 
Table 2 Cable color  
网络 
Network 
颜色/Color 
备注 
Notes 
CAN_H 
CAN_L 
Network 1 
黄黑/Yellow&Black 
绿棕/Green &Brown 
1.当n＞3线束颜色由线束工程师定义，并通知网
络工程师。 
The wire color should be defined by the harness 
engineer who informs the network engineer 
when n is more than 3. 
2.线束颜色不得相同。 
The cable color should not be the same. 
Network 2 
黄白/Yellow&White 
绿蓝/Green &Blue 
Network 3 
黄红/Yellow&Red 
绿白/Green &White 
Network n 
—— 
—— 
 
5 LIN 线束 
 
5  LIN Harness 
5.1 LIN 网络拓扑结构 
LIN网络拓扑采用线性结构。 
其拓扑图参见图5： 
 
 
 
5.1  LIN Topology 
The topology of LIN should be a linear 
structure. 
LIN network topology is shown below: 


### 第 10 页
SMTC 9 800 005—2016（V2） 
7 
© 上汽集团版权所有  秘密文件 注意保密 Copyright SAIC MOTOR Confidential 
 
节点1
Node 1
节点n
Node n
节点3
Node 3
节点2
Node 2
节点4
Node 4
节点 (n-1)
Node  (n-1)
 
图 5 LIN 网络拓扑图 
Figure5 LIN network topology 
5.2 线束设计要求 
a) 
每条 LIN 线长度总和不得超过 40 m，
一般采用非屏蔽线。 
b) 
每条网络最多有 16 个节点(即 n≤16)。  
c) 
布线时尽量远离高噪声（如：喷油器驱
动）和灵敏（如：弱信号传感器，天线
馈电）电路。 
d) 
当 LIN 线靠近天线或天线功放等强电
磁干扰环境时，须使用屏蔽线；屏蔽层
一端接地。 
e) 
避免总线线束回路布置在接近于整车
金属地面或者与地线交叉。 
f) 
线电容不得超过 150 pF/m。 
g) 
LIN 线颜色参见表 3。 
 
5.2  Harness Design Requirements 
a) 
Total length of LIN bus should be less than 
40 m and normally unshielded cable is 
used. 
b) 
The maximum number of nodes on the 
network must not exceed 16 (i.e. n≤16). 
c) 
Avoid routing bus wire signals close to 
noisy (e.g. injector drivers) and sensitive 
(e.g. low signal level sensors, antenna 
feeds) circuits. 
d) 
When routing signals near antennae or 
antenna amplifiers, a shielded wire may 
be required. Only one end of the shield 
(screen wire) should be connected to 
GND. 
e) 
Avoid wire loops by locating bus wires 
close to the vehicle’s metal ground plane 
or route a ground wire with the bus wire 
f) 
Maximum line capacitance is 150 pF/m. 
g) 
LIN cable color，refer to table 3. 
表 3 线束颜色 
Table 3 Cable color 
LIN 
颜色/Color 
备注/Notes 
LIN 1 
粉黑/Pink&Black  
1.当n＞3线束颜色由线束工程师定义，并通知网络工程师。 
The wire color should be defined by the harness engineer who 
informs the network engineer when n is more than 3. 
2.线束颜色不得相同。 
The cable color should not be the same. 
LIN 2 
粉白/Pink&White 
LIN 3 
粉红/Pink&Red 
LIN n 
—— 
 
6 KLine 线束 
 
 
 
6  K-Line Harness  


### 第 11 页
SMTC 9 800 005—2016（V2） 
8 
© 上汽集团版权所有  秘密文件 注意保密 Copyright SAIC MOTOR Confidential 
 
6.1 KLine 网络拓扑结构 
K线是双向传输线。 
整车所有依靠K线通信的节点，通过诊断通
信接口与诊断测试工具进行数据交换。 
网络结构参见图6： 
 
 
 
6.1  K-Line Topology 
K-Line is a bidirectional line. 
All K-Line nodes in vehicle communicate with 
tester through diagnostic connector. 
Figure 6 shows the network topology： 
 
 
 
 
 
节点1
Node 1
节点2
Node 2
诊断接口
（整车）
Connector
(Vehicle)
测试工具
Tester
整车K线
Vehicle K-Line
节点n
Node n
 
图 6 K-Line 网络拓扑图 
Figure 6 K-Line network topology 
6.2 线束设计要求 
a) 
每条 K 线长度总和不超过 40 m，一般
采用非屏蔽线。 
b) 
每条网络最多有 10 个节点(即 n≤10)。 
c) 
布线时应该考虑到 EMC 的要求（参见
ISO14230-1）。 
d) 
线束应选用 0.5 mm2，颜色定为蓝白。 
 
6.2  Harness Design Requirements 
a) 
Total length of K-Line should be less 
than 40 m and normally unshielded 
cable is used. 
b) 
The maximum number of nodes on the 
network must not exceed 10(i.e. n≤10). 
c) 
EMC should be considered when routing 
signals (Refer to ISO14230-1). 
d) 
d) Wire should be set to 0.5 mm2 and 
defined as blue&white. 
 
 
 
 
 
 
7 汽车诊断通信接口 
整车诊断通信接口采用A型接插件，参见
图 7： 
 
7  Vehicle Diagnostic Connector 
The vehicle connector type A should be used 
Figure 7 is showed below:  
 
图 7 A 型接插件 
Figure 7 Connector type A 
 
 


### 第 12 页
SMTC 9 800 005—2016（V2） 
9 
© 上汽集团版权所有  秘密文件 注意保密 Copyright SAIC MOTOR Confidential 
 
7.1 接插件定义 
该诊断接口的电气、机械和化学性能应满足 
ISO15031-3 中的定义。诊断接口针脚的定义参
见表 4，同时诊断工具需支持通过软件设置针脚
定义。在开发阶段，根据需求诊断接口需为 LIN
网络分配相应针脚。 
 
7.1 Connector Definition 
The connector’s electrical, mechanical, and 
chemical performances must comply with 
ISO15031-3. The pin assignment of the 
Diagnostic Connector is showed in table 4. 
And it shall support pin assignment by 
software configuration. The pin assignment of 
the diagnostic connector should be reserved 
for LIN according to requirements during 
development. 
表 4 诊断接口针脚定义 
Table 4 Connector pin definition 
针脚序号 
Pin No. 
定义/Allocation 
针脚序号 
Pin No. 
定义/Allocation 
1 
HS-CAN4 High/ Single-wire CAN 
9 
HS-CAN4 Low 
2 
Discretionary 
10 
Discretionary 
3 
HS-CAN3 High 
11 
HS-CAN3 Low 
4 
Chassis ground 
12 
HS-CAN2 High 
5 
Signal ground 
13 
HS-CAN2 Low 
6 
HS-CAN1 High 
14 
HS-CAN1 Low 
7 
K-line 
15 
Additional Communication Line 
8 
DS2 
16 
Permanent positive voltage 
 
7.2  接插件位置 
车辆上的标准通信接口应位于驾驶员侧，以
在驾驶员位置进行轻松操作为宜，须固定住，推
荐位置是在转向柱与车辆中心线之间的区域。 
 
 
 
 
7.2 Location Of Connector 
The connector shall be located in the driver’s 
area and it’s easy to access from the driver's 
seat. The connector should be fixed.The 
preferred location is between the steering 
column and the vehicle centreline. 
8 修订信息 
 
8  Release and revisions 
No. 
标准编号/Standard No. 
起草人/Drafter 
主要修订内容/Revision information 
1 
SMTC 2 800 006-2010(V1) 
周三国Zhou Sanguo 
严伟 Yan Wei 
吴平友Wu Pingyou 
庞晓峰 Pang Xiaofeng 
NA 
2 
SMTC 2 800 006-2012(V2) 
周三国Zhou Sanguo 
吴平友Wu Pingyou 
1.增加twist图示； 
Add twist description by picture. 
2.增加终端电阻120Ω 放在线束上； 
Add description of laying termination with  120
Ω  on harness. 
3.增加离线工具不需要终端电阻的要求； 
Add requirement that the off-board tool. 
4.增加节点连接要求。 
Add node connection requirement. 


### 第 13 页
SMTC 9 800 005—2016（V2） 
10 
© 上汽集团版权所有  秘密文件 注意保密 Copyright SAIC MOTOR Confidential 
 
 
No. 
标准编号/Standard No. 
起草人/Drafter 
主要修订内容/Revision information 
3 
SMTC 9 800 005-2013(V1) 
周三国Zhou Sanguo 
吴平友Wu Pingyou 
更新不符合定义的标准编号。 
Update the standard number. 
4 
SMTC 9 800 005-2016(V2) 
周三国Zhou Sanguo 
刘亚Liu Ya 
吴平友Wu Pingyou 
1.修改不带终端电阻或高阻抗的节点连接总线的
方式； 
Revise the node without terminal resistance or 
high impedance connecting bus. 
2.修改屏蔽线要求； 
Revise the requirement of shield. 
3. 修改绞率最小值和标称值； 
Revise the minimum and nominal values of twist 
rate. 
4. 增加导体截面积注释； 
Add notes for conductor section. 
5. 修改CAN线束颜色； 
Revise CAN cable color. 
6. 修改诊断借口针脚定义。 
Revise DLC connector PIN definition. 
 
 
 
 
 

