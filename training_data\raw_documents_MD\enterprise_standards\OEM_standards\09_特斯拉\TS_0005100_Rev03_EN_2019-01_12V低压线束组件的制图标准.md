# TS_0005100_Rev03_EN_2019-01_12V低压线束组件的制图标准.pdf

## 文档信息
- 标题：
- 作者：<PERSON>
- 页数：63

## 文档内容
### 第 1 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
1 
 
 
 
 
 
 
 
DRAWING STANDARD FOR LOW VOLTAGE WIRE 
HARNESS ASSEMBLIES 
********** | REVISION 3 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 2 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
2 
 
Foreword  
 
Revision 03 of this document brings significant changes to TS-005100 (this 
document) 
As of revision 02, BMS-0005100 has been made obsolete and the supplier shall 
follow only this document for all wire harness drawings going forward. 
 
The owners of this document are listed below. Please contact them for questions. 
 
<PERSON> (<EMAIL>) 
<PERSON> (<EMAIL>) 
 
 
 


### 第 3 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
3 
 
Revision History 
 
Rev/Date 
Section 
Details 
Author / CA 
Rev 01 
11/2/16 
N/A 
Initial Release (Draft) 
<PERSON>  
*********** 
Rev 02 
8/28/17 
 
CA-
00003355 
3.2 
3.4 
Added new wire color TN - Tan 
Removed extra plating requirements 
 
 
 
 
 
 
 
 
 
Nikhil 
Kakanavaram/ 
Kaled Salih 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
4.1 
4.2 
4.3 
4.4 
4.5 
4.6 
4.7 
Added wrap requirements for all types 
Updated section to current taping 
standards 
Updated Junction taping images/symbols 
Updated banch to bundle taping images 
Added section for End-Fix taping 
Added section for Free taping 
Added section for grommet taping 
5.1 
5.1 
5.2 
Added requirement for COT end-fixing  
Added free tape criteria 
New graphic for Cut tubing 
6 
6.1 
 
6.2 
Added angular tolerance requirements 
Updated symbols, images and diagrams 
for clip orientations  
Added section for Multi-Node components 
8.1 
8.1 
8.2.3 
8.2.7 
8.28 
8.2.10 
 
Changed tolerance table 
Modified notes 
Modified Branch to Node diagram 
Added grommet reference diagram 
Added diagram for spot tape dimensioning 
Added Top & Side connector dressing spec 
tolerance 
9.1.1 
9.1.2 
9.1.3 
 
9.2 
9.3 
 
9.4 
9.4.1 
9.4.2 
9.5 
9.6 
9.7.1 
9.7.2 
 
9.7.3 
Added section for dip soldering 
Added section for USW 
Added section for ring terminal crimping 
and angular tolerances 
Modified list of wire colors 
Added wires types, and column for Delphi 
MSPEC 
Added note about crimp splices 
Added section for ultrasonic splice 
Added section for crimp splice 
Added spec for CAN butt splices 
Modified all multicore specs 
Added section for shield wire cutoff 
Added section for shield wire drain in 
sealed connectors 


### 第 4 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
4 
 
 
9.8 
Added section for shield wire drain in 
sealed connectors 
Added note to clarify HSD cable pinouts 
10 
Added note to allow printed label on cable 
11 
Changed color table to have 12 colors. 
Added section for dual-color ID label tapes 
12 
12.1 
Added new section for sealing 
requirements 
Added section for keep-out zone 
Rev 03 
1/8/2019 
 
CA-
00010190 
2 
Changed Tesla Motors to Tesla Inc. 
Eugene 
Plimpton 
3.6 
Moved Part Label section to 3.6 from 11. 
Corrected BMS-0000006 to BMS-0000007 
Added “Label must display description from 
‘short description’ column associated with 
each part variant. Added “DESCRIPTION’ 
to label image. 
Updated label images to follow BMS-
0000007 
4.1 
Added Gemini Plastics to Tyz-All definition 
Added VT-ID definition 
4.3 
Deleted “(Less preferred but acceptable)” 
Added “Note: 3 separate conduits shown” 
4.4 
Added “Manufacturing drawings created by 
the supplier may exclude the tape shown 
on the branch provided they are accounted 
for in detail views.” 
4.6 
Ends of COT shall be free taped unless 
otherwise specified 
5.1 
Moved comment "For instances where a 
multiple branch outlet…" from under COT 
to main paragraph. 
Added Flat Wrap protection 
Added exceptions for small bundles 
5.2 
Added note “unless otherwise specified” 
6 
Added clip view with multiple bundles 
6.3 
Added section 6.3 
7 
Corrected “S” symbol on connector to show 
“Sealed connector” instead of “Safety 
critical connector” 
Miriam Cater 
7.1 
Corrected dressed connector reference 
from 8.2.9 to 8.2.10 
Eugene 
Plimpton 
8.1.1 
Added Half Tolerance Chart 
8.2 
Changed “Harness Trunk” to “Tapes & 
Junctions” 
8.2.7 
Added Packaging Details section. 


### 第 5 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
5 
 
8.2.8 
Deleted “the spot tape center node is 
ignored”. Added “adjacent” 
8.2.10 
Changed B dimension to account for 
bundle diameter 
8.3 
Changed 8.3 to 8.3.1 
Deleted “as much as possible” 
9.1 
Added provision for substitute terminals 
9.2 
Added note to use light color wire in the 
absence of the specified neutral color. 
9.3 
Added more FLRY wire sizes, 9093, TXL, 
TWP, WCLC, WDIC, WDLC wire types 
Miriam Cater 
Eugene 
Plimpton 
9.4.2 
Crimp splice requirements added  
Eugene 
Plimpton 
9.6 
Deleted “on a print’s gamma page”. 
Added “Spot tape is allowed to maintain 
twist.” 
9.6.2 
Combined BroadR-Reach and A2B 
sections. 
9.7.2 
Added “BUTT SPLICE IS ACCEPTABLE” 
for drain to wire splice. Updated image. 
Nick Chin 
9.7.3 
Added “or vinyl tape” to exposed wire. 
Updated image. 
Acceptable to have drain wire below foil 
9.10 
 
Added new section on Safety Critical 
Circuits 
Miriam Cater 
10.0 
Added Aptiv to approved cable suppliers. 
‘-1’ to be appended to ref des. used for 
multiple cables 
Eugene 
Plimpton 
7, 9 
Removed references to alpha, beta and 
gamma pages 
11 
Removed testing specifications. Refer to 
supplier handbook and SIE guidance for 
testing. 
 
 
 


### 第 6 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
6 
 
CONTENTS 
CONTENTS................................................................................................................................................... 6 
1. SCOPE / OBJECTIVE .................................................................................................................................... 7 
2. CONFIDENTIALITY ....................................................................................................................................... 7 
3. MATERIAL .................................................................................................................................................. 7 
3.1. Regulatory Compliances.................................................................................................................... 7 
3.2. Colors................................................................................................................................................. 7 
3.3. Wire/Cable Insulation ......................................................................................................................... 7 
3.4. Terminal Plating ................................................................................................................................. 8 
3.5. Part Traceability ................................................................................................................................. 8 
3.6. Part Label (ID Label) .......................................................................................................................... 8 
4. TAPING .................................................................................................................................................... 10 
4.1. Tape Types & Specifications ........................................................................................................... 10 
4.2. Bundle Taping .................................................................................................................................. 13 
4.3. Junction Taping ............................................................................................................................... 14 
4.4. Branch to Bundle Taping ................................................................................................................. 18 
4.5. End-fix Taping .................................................................................................................................. 19 
4.6. Free Taping ..................................................................................................................................... 20 
4.7. Grommet Taping .............................................................................................................................. 23 
5. PROTECTION ............................................................................................................................................ 24 
5.1. Protection Types & Specifications ................................................................................................... 24 
5.2. Protection Symbols .......................................................................................................................... 27 
6. CLIPS ....................................................................................................................................................... 29 
6.1. Clip Symbols/Views ......................................................................................................................... 31 
6.2. Multi-Location Components “MLC” .................................................................................................. 34 
6.3. Band Clip Application ....................................................................................................................... 35 
7. CONNECTORS .......................................................................................................................................... 36 
7.1. Connector Symbols ......................................................................................................................... 36 
8. SPECIFICATIONS AND TOLERANCES ........................................................................................................... 38 
8.1. Standard Length Tolerance Chart ................................................................................................... 38 
8.2. Dimensioning ................................................................................................................................... 39 
8.3. Critical Symbols ............................................................................................................................... 48 
9. CIRCUITRY ............................................................................................................................................... 49 
9.1. Terminals ......................................................................................................................................... 49 
9.2 Wire Colors ...................................................................................................................................... 50 
9.3 Wire Types ....................................................................................................................................... 51 
9.4 Splices ............................................................................................................................................. 53 
9.5 Twisted Pair Splices ........................................................................................................................ 54 
9.6 Twisting / Multicores ........................................................................................................................ 55 
9.7 Shield Wires ..................................................................................................................................... 57 
9.8 HSD Cable Pinouts .......................................................................................................................... 59 
9.9 Junction Connectors ........................................................................................................................ 60 
9.10 Safety Critical Circuits ...................................................................................................................... 61 
10. 
SECONDARY CABLES ......................................................................................................................... 62 
11. 
PACKAGING SPECIFICATIONS ............................................................................................................. 63 
 


### 第 7 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
7 
 
1. Scope / Objective 
The following specifications cover how to interpret Tesla’s wire harness drawings and what that 
means for the manufacturing and processing of low voltage wiring harnesses. The supplier is 
responsible for all inspection and test requirements specified in this document. 
2. Confidentiality 
The information contained herein is deemed to be confidential, proprietary, and a trade secret of 
Tesla Inc. This information may not be used, reproduced, or disclosed as the direct or indirect 
basis for the development, manufacture, or sale of processes or products without the expressed 
written consent of Tesla Inc. 
3. Material 
3.1. Regulatory Compliances 
All materials and coatings must comply with all applicable international environmental related 
regulations, including but not limited to EU directives WVTA, ELV, RRR, ROHS, WEEE, 
REACH, and Battery Directive 2006/66/EC, which are described in detail at the GADSL list in its 
latest version at the time of use. Refer to Tesla spec BMS-0000408 for detail. 
 
3.2. Colors 
Colors are designated by two letter abbreviations as follows: 
BK 
Black 
WH 
White 
RD 
Red 
YE 
Yellow 
BN 
Brown 
GN 
Green 
LG 
Light Green 
LB 
Light Blue 
BU 
Blue 
VT 
Violet 
GY 
Grey 
OG 
Orange 
PK 
Pink 
TN 
Tan 
 
3.3. Wire/Cable Insulation 
Wire specifications shall be interpreted as stated in Section 9.2 of this standard. 
 
 


### 第 8 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
8 
 
3.4. Terminal Plating 
Terminal Plating is designated by two letter abbreviations in the connector circuitry tables 
as follows: 
 
Sn: Tin Plating per terminal specification 
Au: Gold Plating per terminal specification 
Ag: Silver Plating per terminal specification 
 
 
3.5. Part Traceability 
Unless otherwise specified part requires grade B-batch traceability control with individual 
part marking per supplier handbook BMS-0000151. 
 
3.6. Part Label (ID Label) 
Part to be labeled with part number and serial or batch number in the area shown per 
Tesla part labeling specification BMS-0000007. Label must display description from 
‘short description’ column associated with each part variant. 
Part Label shall be secured to the harness bundle using VT-ID. Secure label with the 
side showing part number facing away from the wire bundle and bar code opposite the 
tape to ensure the bar code is always visible. 
Drawing 
Real Image 
 
 
 
 
 
ID Label Tape color (VT-ID) varies by harness variant according to ID LABEL TAPE 
COLOR chart. VT-ID is vinyl tape in the color corresponding to each variant. 
 
An example of an ID label tape color chart is shown below for reference. Drawings may 
show different colors. 
 
 


### 第 9 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
9 
 
 
Single color ID Tape 
Dual-color ID tape 
 
 
 
Harness Part Label Detail Drawing: 
 
 
 


### 第 10 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
10 
 
4. Taping 
4.1. Tape Types & Specifications 
Tape types are designated by two letter codes. Each of the following material types are 
specified with a baseline vendor part number, but can be substituted by other vendor 
material that meets or exceeds the baseline specification. Unless otherwise specified on 
the print, all tapes shall be black. 
 
AT 
Anti-abrasion tape (TESA 51036, TESA 51026, or equivalent) 
 
  
 
 
FM 
Foam Sheet (Armacell Ensolite EFO or equivalent) 
 
 
 
 
FT 
Felt Tape (TESA 51618 or equivalent) 
 
 
 
 


### 第 11 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
11 
 
 
 
HT 
Hanking Tape (C.H. Hanson Company #17024 or equivalent) 
 
 
 
 
   
 
TYZ-ALL 
Tyzall Straps (Tyz-All Plastics, Gemini Plastics or equivalent) 
  
 
Pre-cut 3/8”, 9/16”, 3/4” wide 
 
 
 
S-VT Slit Vinyl Tape (Nitto 2107TV 10mm wide or equivalent) 
Slit tapes shall be wrapped 1.5-2 times around harness unless otherwise 
specified 
 
  
 
 
 
 


### 第 12 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
12 
 
VT 
Vinyl Tape (Nitto 2450, Tesa 4182 or equivalent) 
 
 
 
 
Vinyl Tape Colors: 
 VT-BK 
Black (default tape color unless otherwise specified) 
 VT-WH 
White 
 VT-RD 
Red 
 VT-YE 
Yellow 
 VT-BN 
Brown 
 VT-GN 
Green 
 VT-LG 
Light Green 
 VT-LB 
Light Blue 
 VT-BU 
Blue 
 VT-VT 
Violet 
 VT-GY 
Grey 
 VT-OG 
Orange 
 VT-PK 
Pink 
 VT-ID 
ID label tape color, see Section 3.6 
 
 
 


### 第 13 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
13 
 
4.2. Bundle Taping 
Drawing Indication 
Tape Interpretation 
 
 
 
 
No taping (bare wire) 
 
 
Spot Tape (Overlap tape 2-3 times) 
 
 
 
 
 
Spiral Tape (pitch: twice of tape width) 
 
  
 
 
 
  
 
 
Overlap  or “Half-lap” Tape (pitch: half of tape 
width) 
 
 
 
 
 
Double Overlap Tape* 
 
*Double overlap tape should be wrapped in opposite directions between two layers. 
*Half lap tolerance is arbitrary as long as there is no area left uncovered by the overlapping 


### 第 14 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
14 
 
4.3. Junction Taping 
All junctions (unless under a protector/wire guide) must have taping defined to clarify the 
design intent.  
 
Branches included or not included under the junction taping are defined by which side of 
the tape the branch is drawn: 
 
Junction tape symbols: 
 
Drawing Indication 
Tape Interpretation 
  
 
Cross tape “diaper tape” with three take-
outs 
 
 
 
 
 
Cross tape “diaper tape” with five take-
outs 
     
 
 
 
 


### 第 15 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
15 
 
  
 
Or 
 
 
Note: 3 separate conduit pieces shown. 
 
Spot tape on one side with two take-outs 
with COT covering. It is allowable to have 
wires exposed. 
 
Or 
 
 
Spot tape on one side with two take-outs 
shown with AT-BK covering 
 
 
 
 
 


### 第 16 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
16 
 
 
 
 
 
 
Two braches exit junction on top side of 
spot tapes on diagram. 
 
 
Two branches exit junction between two 
spot tapes 
 
NOTE: In this scenario, it is ok to have one 
lap of crossover tape to ease mfg by not  
breaking the tape like the below image: 
 
 
Top two wires run through Right & Left 
sides of spot tape on diagram. 
 
 
Top two wires route around outsides of two 
spot tapes. 
 
 


### 第 17 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
17 
 
 
Two branches exit junction between two 
spot tapes on a clip 
 
 
Three branches exit junction and secured 
to clip in two locations 
 
 
 


### 第 18 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
18 
 
 
 
 
“Selective” Diaper Taping – Segments 
leaving same edge of symbol are not taped 
between. Any bundles that exit the same 
side of the “+” symbol do not get taped 
between. 
 
 
 
 
4.4. Branch to Bundle Taping 
Drawing Indication 
Tape Interpretation 
 
 
Tape over male connector to secure it to 
main trunk 
 


### 第 19 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
19 
 
 
 
 
 
Tape over female connector to secure it to 
main trunk. 
 
 
 
In some cases to reduce drawing complexity slit tape is 
shown on a branch, but must be accompanied by a detail 
view showing the hanked branch. Manufacturing drawings 
created by the supplier may exclude the tape shown on the 
branch provided they are accounted for in detail views. 
 
DETAIL Z4 
 
 
 
Slit tape over five branches to secure it to 
main trunk. 
 
 
 
 
4.5. End-fix Taping 
End-fix tape secures harness protection in place by taping over the protection and onto 
the harness bundle. End-fix tape will be VT-BK, unless otherwise specified. 
If an end-fix tape is applied to a junction, the end-fixing must occur within the width of the 
tape from the bundle in which the junction exits.  
 
 
 
 
 


### 第 20 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
20 
 
Drawing Indication 
Tape Interpretation 
 
 
      
End-fix tape may be used with the following types of protection: 
 
Vinyl Tube (VO) 
 
Cut Vinyl Tube (C-VO) 
 
Hard tube (C-VO-H) 
 
Twist tube (TWTB) 
 
Sleeve (AT-SL) 
 
Corrugated Tubing (COT) 
4.6. Free Taping 
Free tape secures the end of a protection from opening but does not end fix the 
protection to the wire bundle. Ends of COT shall be free taped unless otherwise 
specified. It is acceptable (and preferred) to wrap the tape over the bundle and extend 
the tape through the slit in the protection before free taping. Free tape will be VT-BK, 
unless otherwise specified. 
Free tape may be used with the following types of protection: 
 
Twist tube (TWTB) 
 
Corrugated Tubing (COT) 
Examples of End-Fixing and Free Taping for different configurations. 
Drawing Indication 
Actual Condition 
Description 
 
 
 
 
Vinyl Tube  
Vinyl tubing has a spot 
tape at the end, that 
end shall be end-fixed. 


### 第 21 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
21 
 
 
 
 
Tubing with Free tape 
COT tube has no spot 
tape symbol, it shall be 
free-taped.  
 
 
 
COT End-Fix 
End of COT has a spot 
tape symbol, it shall be 
end-fixed.  
 
 
 
Free tape with offset 
COT shall be free taped 
50mm from connector. 
Unless otherwise 
specified the other end 
shall be end-fixed in 
order to control the 
dimension. 
 
 
 
 
End-fix with Offset 
COT is end-fixed 
50mm from the 
connector. 
 
 
 
 
Tubing at Cross Tape 
Protection end is at a 
junction cross tape, the 
protection is end-fixed 
at the junction. 


### 第 22 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
22 
 
 
 
 
 
Tubing at Junction 
Spot Tape 
Only the branch on the 
side of the spot tape 
shall be end-fixed. The 
others do not have a 
spot tape, and 
therefore will be free-
taped. 
 
 
 
 
Multi-node Spot Tape 
at Junction 
A multi-node spot tape 
placed at a junction 
shall end-fix the two 
branches to the 
junction, but not each 
branch individually.  
 
 
 
 
Multi-node Spot Tape 
after a Junction 
A multi-node spot tape 
placed a certain distance 
away from a junction shall 
wrap around the two 
branches at that location. 
The tubing remains free-
taped at the junction. 
 
 
 
 


### 第 23 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
23 
 
4.7. Grommet Taping 
Grommets are to be secured to harness bundles where spot tape is positioned on a 
taping tab. 
Drawing Indication 
Interpretation 
 
 
 
 
 
By default, 3-5 wraps of tape must go around the bundle alone, and then around each tab 
(entire wrapping must be done in one continuous length of tape). For each taping tab on a 
grommet, there must be 3 wraps of tape around that tab + the rest of the bundle. Below 
shows examples of single tab grommet taping, and double tab taping. 
Single tab 
1
 
Place bundle into 
grommet at the specified 
location. 
2 
 
Apply 3 wraps of taping to 
the bundle alone 
3 
 
Continue the wrapping for 3 more 
wraps on the tab 
 
 
 
 
 
 
 
 


### 第 24 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
24 
 
Double tab 
1 
 
Place bundle into grommet at the specified 
location 
2 
 
Peel back both tabs and apply 3 wraps 
on the bundle alone 
Step 3 
 
Continue & apply 3 wraps around the first 
tab with the bundle 
Step 4 
 
Continue and apply the next 3 wraps 
around the second tab and the rest of the 
bundle 
5. Protection 
5.1. Protection Types & Specifications 
Protection types are designated by letter codes. Each of the following 
material types are specified with a baseline vendor material and/or part 
number, but can be substituted by other vendor material that meets or 
exceeds the baseline specification. Unless otherwise specified on the print, 
all protection shall be black.  
For instances where a multiple branch outlet is reduced to a single branch 
on a given variant, if the covering before and after the junction is the same, 
use one continuous piece of any tube covering, or one continuous wrap of 
any tape covering. 
 
COT 
  Corrugated Tubing (Delfingen Syflex or equivalent) 
 


### 第 25 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
25 
 
 
COT shall be slit for easy application over wire bundle. 
 
COT inside diameter shall be sized no more than 20% larger than the wire 
bundle size. 
 
COT slit must naturally stay closed to prevent exposing wires. COT that is 
deformed to the extent that the slit remains open at resting state shall be 
discarded. 
 
For bundle sizes greater than 5mm in diameter, spiral vinyl tape shall be 
applied under COT to prevent loose wires from coming out of slit unless 
otherwise specified. 
 
For bundle sizes less than 5mm in diameter: 
o If length of segment is under 200mm only 1 spot vinyl tape is required 
over the COT. Its reference position is the middle of the segment. 
o If length of segment is 200mm or longer apply 1 spot vinyl tape every 
100mm over the COT. Its reference position is evenly distributed along 
the segment.  
 
Unless otherwise specified, ends of COT shall be free taped. NOTE: Free 
tape secures the end of the COT from opening but does not necessarily 
end fix the COT to the wire bundle. It is acceptable (and preferred) to wrap 
the tape over the bundle and extend the tape through the slit in the COT 
before free taping. Free tape will be VT-BK, unless otherwise specified. 
 
 
Long COT may be divided between clips to improve workability. Divided 
COT sections must be butted together and taped with VT-BK. 
 
When COT is placed under a junction tape, the COT is to be end fixed at 
the junction. The end-fix tape and junction tape may be common but not 
required. Maximum clearance from the end-fix of the COT to the junction is 
the width of end-fix tape. 
 
VO 
  Vinyl Tube (Delfingen PVC Tube or equivalent) 
 
Vinyl tube is non slit, thickness = 0.5mm 
 
C-VO   Cut Vinyl Tube (Delfingen PVC Tube or equivalent) 
 
Drawing to specify thickness of either 0.5mm or 1.0mm 
 
Unless otherwise specified, must be applied with full overlap vinyl tape 
 
3-10mm overlap of tube required 


### 第 26 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
26 
 
  
 
 
C-VO-H  Cut Vinyl Tube (Hard) (Delfingen Scroll or equivalent) 
 
Hard vinyl tube, thickness = 0.5mm 
 
Unless otherwise specified, must be applied with full overlap vinyl tape 
 
3-10mm overlap of tube required 
  
 
 
TWTB   TwistTube (Federal Mogul 2420) 
 
TWTB shall be hot cut on each end to avoid fraying 
 
90° minimum overlap required 
  
 
 
 
Unless otherwise specified, ends of TwistTube shall be free taped. 
   
 
 
 


### 第 27 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
27 
 
AT-SL Tesa Sleeve (or equivalent) 
 
Wraps around harness bundle lengthwise 
 
Adhesive edge must overlap and stick to itself 
 
 
FW-BK Flat Wrap (Federal Mogul 2535 or equivalent) 
 
Wraps around harness bundle lengthwise 
 
VT-BK is applied as spiral tape unless otherwise specified, and end-fix tape 
to secure to bundle 
 
 
5.2. Protection Symbols 
*Unless otherwise specified. 
 
Drawing Indication 
Protection Type 
 
 
Overlap Anti-abrasion Tape (black) 
 
 
 
 
Slit Corrugated Tubing (black) 
   


### 第 28 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
28 
 
 
Vinyl Tube – 0.5mm thickness 
 
 
 
Cut Vinyl Tube – 0.5mm 
*Must be attached with full overlap vinyl 
tape 
    
  
 
 
 
Cut Vinyl Tube – 1.0mm 
*Must be attached with full overlap vinyl 
tape 
 
   
 
 
 
 
Cut Vinyl Tube (Hard) 
*Must be attached with full overlap vinyl 
tape 
   
 
 


### 第 29 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
29 
 
 
 
Textile Sleeve “Twist Tube” 
   
 
 
 
 
Tesa Sleeve 
 
 
 
Foam 
 
 
Flat Wrap 2535 
 
 
6. Clips 
Clips are rigid control points in a wire harness. Clips have tighter tolerance control and are 
used for proper routing in a vehicle. All clips have a clip symbol with a dimension point(s) on 
the harness bundle along with a Clip View to show the orientation of the clip relative to 
harness assembly board. 
 
Clips symbols are indicated as shown in the below example: 


### 第 30 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
30 
 
 
The clip view direction means the operator must view the harness bundle from the direction 
of the arrow in order to view the orientation of the clip relative to the jig board: 
 
 
 
Clip views are relative to the jig board in which the harness is assembled on (not the floor or 
the vehicle). Up close the clip view has details to assure proper orientation: 
 
 
 
 


### 第 31 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
31 
 
Clip views may contain details for positioning of multiple bundles. Bundles are labelled 
according to the connector, junction or component that they route to.  
 
 
The angle tolerance of the clip is ±45 degrees about the bundle center axis. 
 
6.1. Clip Symbols/Views 
Note: The clip symbol does NOT depict the orientation. Some clip symbols (e.g, row 
5) show a circle above/underneath the clip. This circle is only there to depict that the 
clip extends off the side of the bundle. Whether this circle is above/underneath the 
clip does not dictate the orientation, as it is arbitrary. Only use the arrow + the clip 
view detail to determine the orientation. 
 
Clip Symbol 
Clip View 
Actual Clip 
 
 
 
 
Band clip (zip tie can go 
either direction) 
 
 
 
 
 
 
 
Band clip with tie band 
directional requirement 
 


### 第 32 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
32 
 
 
 
 
 
 
Edge clip with band
 
 
 
 
  
 
Stud clip with band 
 
 
 
 
 
 
Offset band clip 
 
 
 
 
 
 
 
 
  
 
 


### 第 33 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
33 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 34 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
34 
 
6.2. 
Multi-Location Components “MLC” 
MLC’s are used when a component such as tape or a clip need to cover multiple 
branches: 
 
Drawing Indication 
Actual Part Images 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


### 第 35 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
35 
 
There are two types of clips: Band clips, and taping tab clips. In a drawing, clips with 
taping tabs do not show spot tape symbols unless they are multi-node components 
or if a tape type other than vinyl is used to secure the clip. 
 
Type of Clip 
Only one bundle 
Multi-node bundles 
Band clips 
 
 
 
Taping-Tab Clips 
 
 
 
By default , it is implied that 
the clip is vinyl spot taped to 
the bundle around the taping 
tabs (no spot tape symbols 
required) 
Multi-node spot tape symbol 
IS required since there are 
multiple bundles being 
wrapped.  
6.3. Band Clip Application 
Excess band material shall be cut such that 0-5mm of band protrudes from the base of 
the clip. 
 
 
If a band clip is positioned on a length of wire with no protection or tape, spot tape must 
be applied under the clip to hold the clip in position and prevent chafing of wire 
insulation. 


### 第 36 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
36 
 
7. Connectors 
Connectors are endpoints of a branch where each circuit terminates. The connector symbol 
on the topology page denotes the following information: 
  
 
The connector views and charts page shows the same connector information as the 
topology page but also indicates the detailed circuitry information related to that connector:  
 
 
 
7.1. Connector Symbols 
Note: Unless otherwise specified, all connector detail views show the mating face of the 
connector. 
 
Drawing Indication 
Interpretation 
 
 
Female unsealed hybrid connector 
 
 
Male unsealed hybrid connector 


### 第 37 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
37 
 
 
Sealed 16 pin connector 
NOTE: Rounded corners usually indicate 
connector is sealed  
 
Dressed Connector 
(See Section 8.2.10) 
 
 
 
 
Female sealed hybrid connector 
 
 
 
Detail connector view indicates connector 
orientation relative to the harness bundle/clip 
all relative to the jig board. 
 
 
GND Eyelet with anti-rotation & temp set tab 
  
 
 
 
Connector with clip (additional component) 
added.  
 
Additional component table shows detail 
specifications of additional component(s) 
 
 
Generic connector image used for coaxial 
connectors not specifying the specific keyway 
 
 
Connect & Supply arrow indicates parts shall 
be joined together 


### 第 38 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
38 
 
8. Specifications and Tolerances 
8.1. Standard Length Tolerance Chart 
Unless otherwise specified all dimensions will follow the below table for acceptable 
tolerances: 
 
NORMAL DIMENSION RANGE 
HARNESS 
BRANCH 
CLIPS & 
COMPONENTS 
TAPES & 
JUNCTIONS 
ABOVE (>) 
UP TO (<) 
UNIT 
+ 
- 
+ 
- 
+ 
- 
0 
200 
MM 
20 
0 
5 
5 
10 
10 
200 
500 
MM 
30 
5 
5 
5 
20 
10 
500 
1000 
MM 
40 
10 
10 
10 
30 
20 
1000 
2000 
MM 
40 
10 
30 
20 
30 
20 
2000 
- 
% 
2 
0.5 
1.5 
1 
1.5 
1 
 
Notes:  
HARNESS BRANCH: This is the dimension on the print between last 
component/junction and back of connector 
CLIPS AND COMPONENTS: This is any dimension on the print between components 
such as grommets, clips, wire guides, etc. 
TAPES AND JUNCTIONS: This is any dimension on the print between junctions, spot 
tapes, foam wraps, tube wraps etc. 
 
The tolerance table always uses the tolerance of the least common denominator. For 
example:  
 
From a clip to a tape, use the tolerance for tapes.  
 
From a junction to a wire guide, use tolerance for junctions. 
 
From a clip to a connector, use harness branch. 
 
8.1.1 Half Tolerance Chart 
If half tolerance is specified use the following table for acceptable tolerances: 
 
NORMAL DIMENSION RANGE 
HARNESS 
BRANCH 
CLIPS & 
COMPONENTS 
TAPES & 
JUNCTIONS 
ABOVE (>) 
UP TO (<) 
UNIT 
+ 
- 
+ 
- 
+ 
- 
0 
200 
MM 
10 
0 
3 
3 
5 
5 
200 
500 
MM 
15 
2 
3 
3 
10 
5 
500 
1000 
MM 
20 
5 
5 
5 
15 
10 
1000 
2000 
MM 
20 
5 
15 
10 
15 
10 
2000 
- 
% 
1 
0.3 
0.8 
0.5 
0.8 
.5 
 
 
 


### 第 39 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
39 
 
8.2. Dimensioning 
8.2.1 Node to Node (Tapes & Junctions) 
Drawing Indication 
Actual Part Dimensioning 
 
 
 
8.2.2 Clip to Clip or Component to Component 
Drawing Indication 
Actual Part Dimensioning 
 
 
 
 
 
 
 
 
 


### 第 40 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
40 
 
8.2.3 Branch to Node (Harness Branch) 
Unless otherwise specified, branches are measured to the terminal insertion side of 
connector. See example below: 
 
 
A branch must not have negative tolerance, however up to plus 20mm length is 
acceptable: 
 
Drawing Indication 
Actual Part Dimensioning 
 
 
 
 
 
 
 
 
 


### 第 41 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
41 
 
8.2.4 Bolt on Ring Terminals (Harness Branch) 
Unless otherwise specified, the dimension of a branch with a ring terminal is the distance 
to the center of the eyelet: 
Drawing Indication 
Actual Part Dimensioning 
 
 
 
8.2.5 Splices 
Splices may be placed anywhere on the span in which they are drawn. Position within 
span may change in order to improve ease of manufacturing. Loop backs are permitted in 
order to balance splices and reach minimum cut length requirements unless otherwise 
stated; loop backs are always prohibited in bending segments. 
 
Drawing Indication 
Actual Part Dimensioning 
 
 
 
 


### 第 42 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
42 
 
8.2.6 Right Angle Coax 
Dress indication on face view indicates intended direction of right angle terminal. 
Jig board and e-test tooling to be designed for this terminal orientation. 
 
Dimensions A and B measured to edge of terminal body as shown: 
 
 
 
 


### 第 43 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
43 
 
8.2.7 Reference Dimensions 
Reference dimensions are included on the engineering print for the purposes of 
calculating total wire length and designing fixturing, but they are not expected to be 
inspected. Usage of reference dimensions include but are not limited to the following: 
 
Drawing Indication 
Interpretation 
 
Leader Lines – at least one reference 
dimension is included under a leader 
line dimension. Dimensions 100 and 
150 would be held to standard 
tolerances. (50) is not held to 
standard tolerances and does not 
need to be inspected. 
Packaging Detail with reference dimension 
 
 
Packaging Detail with hard dimension 
 
 
Packaging Details – Reference 
dimensions used in packaging details 
shall be treated as a guideline for 
setting up hanking tooling. Features 
shown are critical, including junctions, 
bends and connectors. Hard 
dimensions shown on detail views are 
critical and supersede dimensions on 
the main 2D layout. If no detail view 
exists, follow dimensions on the 2D 
layout. 
 
 
 
 
 
Connector resides to the left of J2603. 
Overall dimension of folded X602 
branch is a critical dimension of 
170mm. X602 branch is folded with 
two bends. The folded X602 branch 
resides to the right of J2397. 


### 第 44 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
44 
 
 
 
  
 
Grommets – 
Reference dimensions used inside 
grommets should be treated as a 
guideline for setting up the grommet. 
The same applies to wire guides, 
channels, protectors, etc. as the 
dimension inside the component is 
difficult to measure.  
 
Note: All dimensions inside channels and grommets shall be considered reference 
whether or not they are explicitly called out as reference dimensions. 
 
8.2.8 Spot Tapes 
If no dimensions exist on each side of a spot tape the spot tape(s) are placed 
equidistant between adjacent nodes. Note the spot tape position tolerance is +/- 20mm 
which is greater than the standard tolerance: 
 
Drawing Indication 
Actual Part Dimensioning 
 
 
 
 
 
 
If dimensions are shown between spot tapes, the tapes are placed accordingly: 
Drawing Indication 
Actual Part Dimensioning 
 
 


### 第 45 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
45 
 
 
 
8.2.9 Clearance Between Connector Neck and Insulation 
Unless otherwise specified, connector branches shall have their insulation taped a certain 
clearance behind the rear face of the connector. This clearance shall be 2/3 the length of the 
diagonal measurement of the connector rear face. If 2/3rds comes out to be less than 10mm, 
the clearance shall be 10mm by default. 
 
If 2/3X is >=10mm, use 2/3X 
If 2/3X is <10mm, use 10mm 
 
The tolerance for this clearance is +5 mm. 
 
8.2.10  Connector Dressings 
 
Connectors that are “dressed” are dimensioned to the side of a connector. The dressing 
arrow indicates which side of the connector to dimension to. 
 


### 第 46 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
46 
 
NOTE: All dressed connectors must have a spot tape at the neck of the connector to 
control/guarantee the dressing stays intact. This spot tape is also used to signify the end of 
the insulation (COT, AT-BK, etc).  
 
Side dressed connectors: 
Drawing Indication 
Interpretation 
 
 
 
NOTE: Spot tape signifies end of bundle covering, but is independent of the insulation or 
covering. It must be applied separately, before the insulation and/or end-fix. 
 
Unless otherwise specified, the spot tape is placed to line up with the closer edge of the 
connector. If the drawing indicates otherwise, the spot tape would be placed further back 
on the branch at a specified length (the tolerance is + 5mm).  
 
An example of a side dressed connector without harness bundle covering is shown below. 
 
 


### 第 47 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
47 
 
Top or bottom dressed connectors: 
Drawing Indication 
 
Interpretation 
 
 
For top bottom dressed connectors, the dimension ‘H’ indicates how far back the spot tape is 
from the dressed side of the connector. 
 
An example of a bottom dressed connector without harness bundle covering is below. 


### 第 48 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
48 
 
  
 
 
8.2.11  Option-Specific Dimensions 
Dimensions may differ between part variants. A dimension with options codes shown next to 
it apply only to variants where those options are applicable. A table may also be shown 
indicating the dimension applicable to each option code. 
 
 
8.3. Critical Symbols 
8.3.1 Bending Areas 
Bending areas shall be specified by triangle markers indicating the start and stop points 
of a bending area. 
 
Example Bending Area: 
 
 
 
The following are prohibited in bending areas: 
 
Splices 
 
Loop wires 


### 第 49 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
49 
 
 
Labels 
 
Bends/Turns on the assembly jig board 
 
Kinked wires 
 
Packaging bends for shipping (creates wire memory) 
 
When creating packaging documentation, all bending areas shall be considered to mitigate 
packaging bends and wire memory. 
9. Circuitry 
9.1. Terminals 
Terminal to wire crimp specifications must be validated to USCAR21 revision 2 or newer. 
It is permissible for suppliers to substitute the equivalent reverse-reeled terminal in place 
of the terminal part numbers indicated on the drawing as required for production tooling.  
Tesla shall be notified of any such substitutions, but no prior approval is required. 
Similarly, it is permissible for suppliers to substitute terminals in place of the terminal part 
numbers indicated on the drawing for cost-saving purposes. The substitute terminal must 
be fully interchangeable with the applicable connector series and mating terminals. Tesla 
shall be provided a document with the following information: 
 
Part number of harness affected 
 
Part number of terminal specified by Tesla 
 
Part number of substitute terminal 
 
Cost of old terminal 
 
Cost of new terminal 
 
Serial number coinciding with cutover to proposed terminal 
 
Date coinciding with cutover to proposed terminal 
 
USCAR 21 Rev 2 or newer validation status 
 
9.1.1 
Dip Soldering 
Terminals listing dip solder as an additional component shall be dip soldered after welding, 
using lead-free solder at 380-420 degrees Celsius, with ~10 seconds dwell time. Terminal 
hole must be clear of solder burrs after dip solder process. 
 
9.1.2 
Ultrasonic Welding 
For terminals using ultrasonic welding, refer USCAR38-1 for performance specification. 
 
 
9.1.3 
Ring Terminal Crimping 
 
Ring terminal crimping tools and processes shall comply with all the requirements 
specified by the terminal supplier. 
 


### 第 50 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
50 
 
Unless otherwise specified, all angles that are dimensioned in the spec sheet of the ring 
terminal must keep the same values even after crimping (excluding the area directly 
affected by the crimp).  
 
However, the tolerance for these angles will double their values after crimping. The 
maximum tolerance it can double up to is ±6 degrees, and by default the minimum 
doubled tolerance is ±2 degrees (anything less than 2 will automatically be bumped up to 
2 degrees). 
 
9.2 Wire Colors 
Wire colors are designated by two letter abbreviations as follows: 
BK 
Black 
WH 
White 
RD 
Red 
YE 
Yellow 
BN 
Brown 
GN 
Green 
BU 
Blue 
VT 
Violet 
GY 
Grey 
OG 
Orange 
NA 
Natural 
SI 
Shield/Drain 
TN 
Tan 
 
Two-letter designations prior to color designations shall be interpreted as follows: 
LT 
Light (LTBU denotes Light Blue) 
DK 
Dark (DKGN denotes Dark Green) 
 
If only light or dark shades of a certain color exist then the color code shall be interpreted 
as LTXX. For example there are only light or dark colors available for green and blue, 
drawings indicating GN and BU shall be interpreted as LTGN and LTBU. 
 
Color combinations (XX/YY) separated by a forward slash denote color striped wire 
where “XX” is the base color and “YY” is the stripe color.  


### 第 51 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
51 
 
9.3 Wire Types 
Wire and cable insulation types are defined by the following: 
Type 
Spec 
(mm2) 
Notes/Specifications 
Equivalent 
Aptiv Spec 
Temp. Rating 
(°C) 
 OD Min 
(mm) 
OD Max 
(mm) 
3TBL 
25 
Aluminum 
M6438 
105 
  
9.65 
3TBL 
40 
Aluminum 
M6439 
105 
11.8 
12.4 
3TBL 
50 
Aluminum 
M6439 
105 
12.9 
13.5 
3TCD 
0.35 
FLR2X-C 
M6467 
125 
1.2 
1.4 
3TCD 
0.5 
FLR2X-C 
M6467 
125 
1.4 
1.6 
9093 
0.13 
450V, XLPE 
- 
125 
0.8 
0.9 
CAVS 
0.35 
JASO D-611 CAVS 
- 
105 
1.4 
1.5 
CAVS 
0.5 
JASO D-611 CAVS 
- 
105 
1.6 
1.7 
COAX 
D302 
Leoni Dacar 302 
M6283 
105 
3.1 
3.5 
COAX 
D380A Leoni Dacar 380A 
- 
105 
2.5 
2.7 
COAX 
D417 
Leoni Dacar 417 
- 
105 
1.65 
1.75 
COAX 
D535 
Leoni Dacar 535 
- 
105 
4.4 
4.8 
COAX 
HSD 
Leoni Dacar 535 
- 
105 
4.4 
4.8 
COAX 
RG174 GC RG174 
M3074 
105 
2.5 
2.8 
COAX 
RG316 GC RG316 
M3411 
105 
2.61 
2.87 
COAX 
USB 
Universal Serial Bus (4 Wire) 
M6481 
105 
4.5 
4.65 
COAX 
USB5 
1Px28AWG + 3Cx26AWG  
M6481 
105 
5 
5.4 
FLRB 
2.5 
ISO FLRY-B / 2TBD 
M3232 
105 
2.8 
3 
FLRB 
3 
ISO FLRY-B / 2TBD 
M3232 
105 
3.1 
3.4 
FLRB 
4 
ISO FLRY-B / 2TBD 
M3232 
105 
3.4 
3.7 
FLRB 
5 
ISO FLRY-B / 2TBD 
M3232 
105 
3.9 
4.2 
FLRB 
6 
ISO FLRY-B / 2TBD 
M3232 
105 
4 
4.3 
FLRB 
10 
ISO FLRY-B / 2TBD 
M3232 
105 
5.4 
6 
FLRB 
16 
ISO FLRY-B / 2TBD 
M3232 
105 
7.3 
7.9 
FLRB 
25 
ISO FLRY-B / 2TBD 
M3232 
105 
8.6 
9.4 
FLRH 
0.35 
Flexible version FLRY-A 
M6467 
105 
1.2 
1.3 
FLRH 
0.5 
Flexible version FLRY-A 
M6467 
105 
1.4 
1.6 
FLRH 
0.75 
Flexible version FLRY-A 
M6467 
105 
1.7 
1.9 
FLRH 
1 
Flexible version FLRY-A 
M6467 
105 
1.9 
2.1 
FLRH 
1.5 
Flexible version FLRY-A 
M6467 
105 
2.2 
2.4 
FLRH 
2 
Flexible version FLRY-A 
M6467 
105 
2.4 
2.6 
FLRY 
0.22 
ISO FLRY-A / 2TAD 
M3130 
105 
1.1 
1.2 
FLRY 
0.35 
ISO FLRY-A / 2TAD 
M3130 
105 
1.2 
1.3 
FLRY 
0.50 
ISO FLRY-A / 2TAD 
M3130 
105 
1.4 
1.6 
FLRY 
0.75 
ISO FLRY-A / 2TAD 
M3130 
105 
1.7 
1.9 
FLRY 
1 
ISO FLRY-A / 2TAD 
M3130 
105 
1.9 
2.1 


### 第 52 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
52 
 
FLRY 
1.5 
ISO FLRY-A / 2TAD 
M3130 
105 
2.2 
2.4 
FLRY 
2.0 
ISO FLRY-A / 2TAD 
M3130 
105 
2.4 
2.6 
FLXB 
16 
ISO FLR2X-B 
M6176 
125 
6.4 
7.2 
FLXB 
25 
ISO FLR2X-B 
M6176 
125 
7.9 
8.7 
FLXB 
0.5 
ISO FLR2X-B 
M6170 
125 
1.4 
1.6 
FLXB 
0.75 
ISO FLR2X-B 
M6170 
125 
1.7 
1.9 
FLXB 
1 
ISO FLR2X-B 
M6170 
125 
1.9 
2.1 
FLXB 
1.5 
ISO FLR2X-B 
M6170 
125 
2.2 
2.4 
FLXB 
2.0 
ISO FLR2X-B 
M6170 
125 
2.65 
2.8 
FLXB 
2.5 
ISO FLR2X-B 
M6170 
125 
2.7 
3.0 
FLXB 
3 
ISO FLR2X-B 
M6170 
125 
3.25 
3.4 
FLXB 
4 
ISO FLR2X-B 
M6170 
125 
3.4 
3.7 
FLXB 
6 
ISO FLR2X-B 
M6170 
125 
4.0 
4.3 
FLXB 
10 
ISO FLR2X-B 
M6176 
125 
5.3 
6.0 
FLXB 
20 
ISO FLR2X-B 
M6176 
125 
7.0 
7.8 
MCIV 
0.13 
MCIVUS / CHFUS 
M5647/M7378 
105 
0.81 
0.95 
MCIV 
0.35 
MCIVUS / CHFUS 
M5373 
105 
1.1 
1.2 
MCIV 
0.5 
MCIVUS / CHFUS 
M5373 
105 
1.25 
1.4 
MCIV 
0.75 
MCIVUS / CHFUS 
M5373 
105 
1.4 
1.6 
MCIV 
1 
MCIVUS / CHFUS 
M5373 
105 
1.6 
1.75 
MCIV 
1.25 
MCIVUS / CHFUS 
M5373 
105 
1.8 
2 
MCIV 
1.5 
MCIVUS / CHFUS 
M5373 
105 
1.85 
2.1 
TWP 
0.35 
SAE J1128 
M3089 
80 
1.5 
1.7 
TWP 
0.5 
SAE J1128 
M3089 
80 
1.7 
1.9 
TWP 
0.8 
SAE J1128 
M3089 
80 
1.9 
2.2 
TWP 
1.0 
SAE J1128 
M3089 
80 
2.18 
2.4 
TWP 
2.0 
SAE J1128 
M3089 
80 
2.49 
2.7 
TWP 
3.0 
SAE J1128 
M3089 
80 
3.07 
3.3 
TWP 
5.0 
SAE J1128 
M3089 
80 
3.83 
4.0 
TXL 
0.35 
SAE J1128 
M3075 
125 
1.5 
1.7 
TXL 
0.5 
SAE J1128 
M3075 
125 
1.7 
1.9 
TXL 
0.75 
SAE J1128 
M3075 
125 
1.9 
2.2 
TXL 
1.0 
SAE J1128 
M3075 
125 
2.18 
2.4 
TXL 
2.0 
SAE J1128 
M3075 
125 
2.49 
2.7 
TXL 
3.0 
SAE J1128 
M3075 
125 
3.07 
3.3 
TXL 
5.0 
SAE J1128 
M3075 
125 
3.83 
4 
TXL 
8.0 
SAE J1128 
M3075 
125 
4.73 
4.9 
PXT 
32 
MS-12581 
M1643 
125 
9.7 
10.5 
SCRN 
0.35 
Bare conductor 
M3098 
105 
1.2 
1.3 
STX 
32 
SAE J-1127 
- 
125 
10.25 
11 


### 第 53 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
53 
 
WCLC 
0.35 
J1654, 600V 
- 
125 
1.5 
1.7 
WDIC 
0.35 
16A0366, 450V 
- 
150 
1.3 
1.6 
WDIC 
0.75 
16A0366, 450V 
- 
150 
1.7 
2.0 
WDLC 
0.35 
17A0143, 600V 
- 
150 
1.2 
1.5 
XLPO 
16 
Cross linked Polyolefin  
M7401 
125 
6.4 
7.2 
XR15 
13 
EXRAD 150FX  
M7461 
150  
 6.14 
6.66 
F91X 
16 
FLR91X 
M6490 
150 
6.4 
7.2 
9.4 Splices 
All splices shall be sealed with adhesive-lined heat shrink unless otherwise specified. 
Splices are not allowed in bending areas, under clips or portions of the harness without 
any coverings. Splice locations should be strategically located in the main trunk or 
bundles and placed within protectors/wire guides where possible to avoid stress and/or 
damage to the splice when the harness is packaged for shipping/handling. 
Unless otherwise specified, all wire to wire splices shall use ultrasonic welds. If an 
ultrasonic weld is not possible, a crimp splice may be used if necessary. Tesla Harness 
Design Engineering shall be notified of all necessary crimp splices. 
 
 
9.4.1 Ultrasonic Welded Splices 
Splices using USW shall be sealed with adhesive-lined (dual wall) heat shrink. 
Refer to USCAR 45 for welding requirements. 
 
9.4.2 Crimp Splices 
Splices using crimps shall be sealed with adhesive-lined (dual wall) heat shrink. 
Crimp splices are permitted provided they conform to the same methodology and 
best practices as for wire-to-terminal crimping. Specifically pull testing must be 
performed according to USCAR-21-3 Section 4.4. Dry circuit termination resistance 
must be measured according to USCAR21-3 Section 4.5.3. 


### 第 54 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
54 
 
9.5 Twisted Pair Splices 
 
When a twisted pair is spliced, the following diagram is shown: 
 
CAN butt splices: 
 
CAN butt splices are required when minimal area is allowed for exposure in the twisted 
pair. This is indicated by the (0)mm reference dimension between the two splices: 
 
The diagram below describes the physical layout of the wires and how they shall be 
spliced off in the twisted pair. (Note: the diagram is not to scale, it is just a representation) 
 
The wires are to be spliced together (USW), and wrapped with dual wall heat shrink. The 
extended branch must be long enough so that the heat shrink covers up to a minimum of 
30mm of bare wire after the splice. They are then tape back onto the main branches: 
 
 
 


### 第 55 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
55 
 
9.6 Twisting / Multicores 
Twisted groups and multicores are contained within the multicore table. The multicore 
name in the table is referenced by individual circuits, belonging to that multicore, in the 
circuit table. For 0.13 mm2 twisted pairs, Tesla recommends more than 12 twists per foot. 
Spot tape is allowed in order to maintain twist. 
 
9.6.1 Standard Twist  
Standard twist pairs maximum untwisted length from neck of connector is 75mm. 
 
Example Standard Twisted Pair: 
 
 
 
9.6.2 BroadR-Reach & A2B Twist 
The maximum untwisted length of BroadR-Reach and A2B wire pairs is 30mm, 
measured from the neck of the connector. 
 
 
Circuits requiring BroadR-Reach or A2B twist specifications are contain “BRR” or 
“A2B” in the multicore name. 
 
 


### 第 56 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
56 
 
 
Example BroadR-Reach Twisted Pair: 
 
 
 
Example A2B twisted Pair: 
Name 
Option 
Length 
Multicore 
CAU01-B 
- 
1720 
TWIST-A2B_CAU01-B 
CAU02-B 
- 
1720 
TWIST-A2B_CAU01-B 
 
 
 
9.6.3 CAN twist 
CAN twist pairs max untwisted length from neck of connector is 75mm 
 
 
Example CAN twisted Pair: 
Name 
Option 
Length 
Multicore 
CVH01-A 
- 
775 
TWIST-CAN_CVH01-A 
CVH02-A 
- 
775 
TWIST-CAN_CVH01-A 
 
 
 


### 第 57 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
57 
 
9.7 Shield Wires 
Shield wires are contained within the multicore table. The multicore name in the table is 
referenced by individual circuits, belonging to that multicore, in the wire table. The drain 
wire of the shield is indicated by SH_XXX with the variable being the name of the 
multicore. 
 
Example Shield Wire:  
 
 
 
 
 
 
 
 
9.7.1 Shield Wire Cutoff 
A shield wire cut off is not terminated. It is rather cutoff before it reaches the 
connector. This occurs when the connector does not have a drain terminal, and is 
indicated as shown in the diagram below: 
 
Note: In this example, the splice is placed as a reference dimension 25mm before 
the connector, therefore the cutoff should be at that location. 
 
 
On the Splice page a ‘splice’ with only one entry indicates that the wire is cutoff 
and not truly spliced to another circuit.  
 
 


### 第 58 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
58 
 
9.7.2 Shield Wires in Sealed Connectors 
 
 
 
 
Sealed Connectors: 
 
For sealed connectors, the drain 
wire must be spliced to an insulated 
wire. The splice shall have dual-
wall heat shrink applied. 
 
9.7.3 Shield Wires in Unsealed connectors 
 
Unsealed Connectors: 
 
The drain wire shall be 
crimped directly into the 
terminal with heatshrink or 
overlap vinyl tape applied 
to the exposed wire. 
 
Note: The heatshrink or 
overlap vinyl tape should 
enter the connector but 
must end before the 
terminal crimp barrel.  
 
 
 
 
It is acceptable to assemble shield wire with the drain wire below the foil rather than above it. 
 
 


### 第 59 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
59 
 
9.8 HSD Cable Pinouts 
HSD cable assembly pinouts shall meet the following configuration: 
 
 
 
NOTE: The pinout diagram above that depicts the relative asymmetry between one side 
of the cable vs the other. For example, pin H1 will be on the right corner of the connector 
faceview, but that pin will route to the opposite side of the cable to H4 (since the pinouts 
are mirrored back to back). This is true regardless of what gender the connectors are (as 
long as the cable same-gender, i.e male-male or female-female, but not male-female). 
 
 
Female - Female 
Male - Male 


### 第 60 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
60 
 
9.9 Junction Connectors 
Junction connectors “JC’s” are connector caps used for splicing wires that are placed in 
normal connectors. 
 
Example Female Connector + JC Cap: 
+
    
 
Actual JC Cap mated to female connector: 
   
 
 
Wires of the same color placed in a female connector that are spliced together using a 
JC are interchangeable. It is acceptable to place any circuit of the same color in any 
cavity that is spliced together through a JC. See below example of 6 splice groups with 
interchangeable cavities/cavity groups: 
 
 
If the spliced circuit colors are different, the circuits must be placed in their proper cavity 
as shown on the harness drawing. These circuits are NOT interchangeable. 


### 第 61 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
61 
 
9.10 
 Safety Critical Circuits 
 
Circuit names that begin with a “+” are considered to be safety critical. Repairs to safety 
critical circuits at Tesla are restricted to certified technicians. Email 
<EMAIL> for permission and assistance. 
 
 
 
 


### 第 62 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
62 
 
10. Secondary Cables 
Secondary cables such as Coax, HSD, USB, etc. that are Tier 2 to the main wire 
harness assembly will have their own Tesla PN and be identified in a Secondary Cable 
List.  
 
All RF and HSD cable assemblies and connectors shall conform to SAE/USCAR-17-5 
and SAE/USCAR2-6 . 
All USB cable assemblies shall conform to SAE/USCAR-30 and USB 2.0. 
All secondary cable assemblies shall have a label applied at one end of the cable such 
that label is visible after cable is installed in harness. Label shall contain Tesla part 
number, supplier part number, and Mfg. date and location. The label may also be printed 
on the cable if printed in a clear font/size and located near the end of the cable for 
visibility. 
If the same reference designator is used for more than one assembly, ‘-1’ will be 
appended to the reference designator within the cable list table. 
 
Example Secondary Cable List: 
  
 
Circuit information in regards to the cable(s) are listed on the Connector and Circuitry 
detail pages of the harness drawing. 
 
The harness supplier will source the cable(s) based on the information found in the 
harness drawing. This includes choosing the proper cable length to fit within the 
tolerances of the harness. 
 
Tesla Approved Cable Suppliers: 
Coax: 
 
 
Harada 
 
 
Amphenol (Adronics) 
Delphi/Aptiv 
MD Elecktronik 
USB: 
 
 
Delphi/Aptiv 
 
 
Molex 
HSD: 
 
 
Rosenberger 
 
 
MD Elektronik 
 
 
 


### 第 63 页
 
 
 
 
********** 
DRAWING STANDARDS FOR LV WIRE HARNESS ASSEMBLY 
REVISION 3 
 
63 
 
11. Packaging Specifications 
All harnesses shall be bundled, hanked and prepared for shipping according to the 
supplier’s packaging engineer instructions. The packaging engineer is to work with Tesla 
to develop custom packaging instructions for each PN during the prototype phases of 
harness design and vehicle development.  
 
The packaging instructions will be written considering not only packaging container 
constraints, but also Tesla conveyance and any special care considerations identified 
during the development and trial process of the vehicle. 
 
The packaging instructions will be submitted to Tesla as part of the PPAP process prior to 
shipping parts. 

