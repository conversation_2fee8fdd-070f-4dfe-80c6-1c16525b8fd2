#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
训练能力验证脚本
验证本地化训练、1024维度支持、多格式处理等功能
"""

import os
import sys
import numpy as np
import json
from pathlib import Path

def verify_data_privacy():
    """验证数据隐私安全性"""
    print("🔒 验证数据隐私安全性...")
    
    # 检查是否有网络上传代码
    sensitive_patterns = [
        'requests.post',
        'urllib.request.urlopen',
        'http://',
        'https://',
        'upload',
        'send_data'
    ]
    
    code_files = []
    for root, dirs, files in os.walk('src'):
        for file in files:
            if file.endswith('.py'):
                code_files.append(os.path.join(root, file))
    
    upload_found = False
    for file_path in code_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                for pattern in sensitive_patterns:
                    if pattern in content and 'localhost' not in content and 'ollama' not in content:
                        print(f"⚠️  发现可能的网络操作: {file_path} - {pattern}")
                        upload_found = True
        except:
            continue
    
    if not upload_found:
        print("✅ 未发现数据上传代码，本地化安全")
    
    return not upload_found

def verify_dimension_support():
    """验证1024维度支持"""
    print("📐 验证1024维度支持...")
    
    try:
        # 模拟向量维度适配
        def adapt_vector_dimensions(vectors, target_dim):
            current_dim = vectors.shape[1]
            if current_dim == target_dim:
                return vectors
            elif current_dim < target_dim:
                # 零填充
                padding = np.zeros((vectors.shape[0], target_dim - current_dim))
                return np.hstack([vectors, padding])
            else:
                # 截断或PCA降维
                return vectors[:, :target_dim]
        
        # 测试不同维度
        test_cases = [
            (384, "MiniLM维度"),
            (768, "BERT维度"),
            (1536, "大模型维度")
        ]
        
        for dim, desc in test_cases:
            vectors = np.random.randn(5, dim).astype(np.float32)
            adapted = adapt_vector_dimensions(vectors, 1024)
            assert adapted.shape[1] == 1024
            print(f"✅ {desc} ({dim}→1024) 适配成功")
        
        return True
    except Exception as e:
        print(f"❌ 维度适配测试失败: {e}")
        return False

def verify_training_data_compatibility():
    """验证训练数据兼容性"""
    print("🔄 验证训练数据兼容性...")
    
    # 检查training_data目录结构
    training_dirs = [
        'training_data/raw_documents',
        'training_data/training_vectors',
        'training_data/models',
        'test_training_data/raw_documents'
    ]
    
    compatibility_score = 0
    for dir_path in training_dirs:
        if os.path.exists(dir_path):
            print(f"✅ 发现目录: {dir_path}")
            compatibility_score += 1
        else:
            print(f"⚠️  缺少目录: {dir_path}")
    
    # 检查元数据文件
    metadata_file = "test_training_data/raw_documents/enterprise_standards/metadata/GMW_3091_EN_2015-06_汽车电磁兼容要求_metadata.json"
    if os.path.exists(metadata_file):
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
                print(f"✅ 元数据文件格式正确: {metadata.get('title', 'Unknown')}")
                compatibility_score += 1
        except Exception as e:
            print(f"❌ 元数据文件格式错误: {e}")
    
    return compatibility_score >= 3

def verify_index_structure():
    """验证索引结构"""
    print("🗂️  验证索引结构...")
    
    # 检查现有索引
    vector_dirs = [
        'data/vectors',
        'data/vectors/metadata',
        'data/vectors/backups'
    ]
    
    index_score = 0
    for dir_path in vector_dirs:
        if os.path.exists(dir_path):
            print(f"✅ 索引目录存在: {dir_path}")
            index_score += 1
        else:
            print(f"⚠️  索引目录缺失: {dir_path}")
    
    # 检查元数据数据库
    db_path = "data/vectors/metadata/metadata.db"
    if os.path.exists(db_path):
        print(f"✅ 元数据数据库存在: {db_path}")
        index_score += 1
    
    return index_score >= 2

def verify_multi_format_support():
    """验证多格式文档支持"""
    print("📄 验证多格式文档支持...")
    
    # 检查PDF和MD文件
    test_files = []
    
    # 查找PDF文件
    for root, dirs, files in os.walk('test_training_data'):
        for file in files:
            if file.endswith(('.pdf', '.md')):
                test_files.append(os.path.join(root, file))
    
    pdf_count = len([f for f in test_files if f.endswith('.pdf')])
    md_count = len([f for f in test_files if f.endswith('.md')])
    
    print(f"✅ 发现PDF文件: {pdf_count}个")
    print(f"✅ 发现MD文件: {md_count}个")
    
    return pdf_count > 0 or md_count > 0

def main():
    """主验证函数"""
    print("=" * 60)
    print("MD向量处理器训练能力验证")
    print("=" * 60)
    
    results = {}
    
    # 执行各项验证
    results['data_privacy'] = verify_data_privacy()
    results['dimension_support'] = verify_dimension_support()
    results['training_compatibility'] = verify_training_data_compatibility()
    results['index_structure'] = verify_index_structure()
    results['multi_format'] = verify_multi_format_support()
    
    print("\n" + "=" * 60)
    print("验证结果汇总:")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:25} : {status}")
    
    overall_score = sum(results.values())
    total_tests = len(results)
    
    print(f"\n总体评分: {overall_score}/{total_tests}")
    
    if overall_score == total_tests:
        print("🎉 所有验证通过！系统完全支持本地化训练")
    elif overall_score >= total_tests * 0.8:
        print("✅ 大部分验证通过，系统基本可用")
    else:
        print("⚠️  部分验证失败，需要进一步配置")

if __name__ == "__main__":
    main()
