#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速测试Path导入修复
"""

def test_path_import_in_vectorize():
    """测试vectorize.py中的Path导入"""
    print("🔍 测试vectorize.py中的Path导入...")
    
    try:
        # 检查文件内容
        with open('src/gui/widgets/vectorize.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查顶部导入
        lines = content.split('\n')
        import_section = lines[:20]  # 前20行应该包含导入
        
        path_imported = False
        for line in import_section:
            if 'from pathlib import Path' in line:
                path_imported = True
                print(f"✅ 找到Path导入: {line.strip()}")
                break
        
        if not path_imported:
            print("❌ 未找到Path导入")
            return False
        
        # 检查Path使用
        path_usages = []
        for i, line in enumerate(lines, 1):
            if 'Path(' in line and 'selected_index_path = Path(' in line:
                path_usages.append(f"第{i}行: {line.strip()}")
        
        if path_usages:
            print(f"✅ 找到Path使用: {len(path_usages)}处")
            for usage in path_usages:
                print(f"   {usage}")
        else:
            print("⚠️  未找到Path使用")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("快速Path导入测试")
    print("=" * 50)
    
    success = test_path_import_in_vectorize()
    
    if success:
        print("\n🎉 Path导入修复验证通过！")
        print("\n📋 现在可以测试向量化功能:")
        print("1. GUI已启动，点击'向量化'标签页")
        print("2. 点击'选择文件夹'按钮")
        print("3. 选择test_training_data/raw_documents/enterprise_standards")
        print("4. 选择模型: nomic-embed-text")
        print("5. 点击'向量化'按钮")
    else:
        print("\n❌ 测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
