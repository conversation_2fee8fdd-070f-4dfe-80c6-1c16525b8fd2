# 本地模型集成与训练指导

## 📋 **您的本地模型分析**

### **🎯 向量化专用模型 (推荐)**
| 模型名称 | 类型 | 向量维度 | 推荐用途 | 优先级 |
|----------|------|----------|----------|--------|
| **nomic-embed-text:latest** | 嵌入模型 | 768 | 文档向量化 | ⭐⭐⭐⭐⭐ |
| **bge-m3:567m** | 多语言嵌入 | 1024 | 中英文混合 | ⭐⭐⭐⭐⭐ |
| **all-minilm:33m** | 轻量嵌入 | 384 | 快速处理 | ⭐⭐⭐⭐ |

### **🤖 大语言模型 (查询增强)**
| 模型名称 | 参数量 | 内存需求 | 推荐用途 | 优先级 |
|----------|--------|----------|----------|--------|
| **deepseek-r1:32b** | 32B | ~20GB | 高质量推理 | ⭐⭐⭐⭐⭐ |
| **qwen2.5-coder:32b** | 32B | ~20GB | 代码理解 | ⭐⭐⭐⭐ |
| **qwen3:30b-a3b** | 30B | ~18GB | 通用推理 | ⭐⭐⭐⭐ |
| **deepseek-coder-v2:latest** | 变量 | 变量 | 技术文档 | ⭐⭐⭐⭐ |
| **gemma3:27b** | 27B | ~16GB | 轻量推理 | ⭐⭐⭐ |
| **deepseek-r1:8b** | 8B | ~6GB | 快速推理 | ⭐⭐⭐ |
| **llamafamily/llama3-chinese-8b-instruct:latest** | 8B | ~6GB | 中文优化 | ⭐⭐⭐ |

---

## 🔧 **模型集成配置**

### **1. 更新models.yaml配置**

```yaml
# 本地模型配置
local_models:
  # Ollama配置
  ollama:
    enabled: true
    api_url: "http://localhost:11434/api"
    default_model: "nomic-embed-text"  # 默认向量化模型
    models:
      # 向量化专用模型
      - name: "nomic-embed-text"
        type: "embedding"
        vector_dimension: 768
        parameters: {}
      - name: "bge-m3"
        type: "embedding" 
        vector_dimension: 1024
        parameters: {}
      - name: "all-minilm"
        type: "embedding"
        vector_dimension: 384
        parameters: {}
      
      # 大语言模型
      - name: "deepseek-r1:32b"
        type: "llm"
        parameters:
          temperature: 0.1
          max_tokens: 4096
          top_p: 0.9
      - name: "qwen2.5-coder:32b"
        type: "llm"
        parameters:
          temperature: 0.1
          max_tokens: 4096
      - name: "deepseek-r1:8b"
        type: "llm"
        parameters:
          temperature: 0.2
          max_tokens: 2048
```

### **2. 向量化配置优化**

```yaml
# config/vectorization_config.yaml
vectorization:
  # 推荐配置组合
  primary_model: "nomic-embed-text"     # 主要向量化模型
  fallback_model: "all-minilm"         # 备用轻量模型
  vector_dimension: 768                 # 匹配nomic-embed-text
  batch_size: 32                       # 根据GPU内存调整
  device: "cuda"                       # 使用GPU加速
  
  # 质量控制
  normalize_vectors: true
  cache_enabled: true
  mixed_precision: true                # FP16加速

# 性能优化
performance:
  gpu_memory_fraction: 0.8             # 为Ollama预留内存
  adaptive_batch_size: true
  max_batch_size: 64
```

---

## 🚀 **向量化操作指导**

### **步骤1: 启动Ollama服务**
```bash
# 确保Ollama服务运行
ollama serve

# 验证模型可用性
ollama list
```

### **步骤2: GUI界面操作**
1. **启动程序**: `python gui_run.py`
2. **进入向量化页面**: 点击侧边栏"向量化"按钮
3. **选择数据源**: 
   - 点击"选择文件夹"
   - 导航到: `test_training_data/raw_documents/enterprise_standards`
   - 系统会自动扫描PDF和MD文件
4. **配置向量化参数**:
   - **模型选择**: nomic-embed-text (推荐)
   - **向量维度**: 768
   - **批处理大小**: 32
   - **设备**: CUDA
5. **开始向量化**: 点击"向量化"按钮

### **步骤3: 验证向量化结果**
```bash
# 运行验证脚本
python test_ollama_connection.py

# 检查向量数据
python -c "
import numpy as np
from pathlib import Path
vectors_dir = Path('data/vectors')
if vectors_dir.exists():
    print(f'向量文件数量: {len(list(vectors_dir.rglob(\"*.npy\")))}')
"
```

---

## 🎯 **本地化训练策略**

### **训练需求分析**
基于您的硬件配置（24GB GPU + 64GB RAM），推荐以下训练方案：

#### **方案1: LoRA微调 (推荐)**
- **目标模型**: deepseek-r1:8b 或 qwen2.5-coder:32b
- **训练类型**: 领域适应 + 查询优化
- **内存需求**: ~12GB GPU + 32GB RAM
- **训练时间**: 2-4小时

#### **方案2: QLoRA超轻量训练**
- **目标模型**: deepseek-r1:32b
- **训练类型**: 4-bit量化 + LoRA
- **内存需求**: ~16GB GPU + 24GB RAM  
- **训练时间**: 4-8小时

#### **方案3: 向量模型微调**
- **目标模型**: nomic-embed-text
- **训练类型**: 对比学习 + 领域适应
- **内存需求**: ~8GB GPU + 16GB RAM
- **训练时间**: 1-2小时

### **推荐训练流程**

#### **阶段1: 数据准备**
```python
# 创建训练数据集
python create_training_dataset.py \
  --source test_training_data/raw_documents \
  --output training_data/automotive_standards \
  --format jsonl \
  --include_metadata
```

#### **阶段2: LoRA训练脚本**
```python
# 示例训练配置
training_config = {
    "model_name": "deepseek-r1:8b",
    "lora_config": {
        "r": 16,                    # LoRA秩
        "lora_alpha": 32,           # LoRA缩放
        "target_modules": ["q_proj", "v_proj", "k_proj", "o_proj"],
        "lora_dropout": 0.1
    },
    "training_args": {
        "learning_rate": 2e-4,
        "batch_size": 4,
        "gradient_accumulation_steps": 8,
        "num_epochs": 3,
        "warmup_steps": 100,
        "save_steps": 500
    }
}
```

#### **阶段3: 模型集成**
```bash
# 合并LoRA权重
python merge_lora_weights.py \
  --base_model deepseek-r1:8b \
  --lora_path ./lora_checkpoints/automotive_standards \
  --output_path ./models/deepseek-automotive-8b

# 导入到Ollama
ollama create deepseek-automotive:8b -f ./models/deepseek-automotive-8b/Modelfile
```

---

## 📊 **性能优化建议**

### **硬件配置优化**
```yaml
# 针对您的硬件的最优配置
system:
  gpu_memory_limit: "20GB"        # 为系统预留4GB
  cpu_workers: 16                 # 使用一半CPU核心
  ram_cache_size: "32GB"          # 使用一半RAM作为缓存

vectorization:
  batch_size: 64                  # 大批处理提高效率
  mixed_precision: true           # FP16减少内存使用
  gradient_checkpointing: true    # 训练时节省内存
```

### **模型选择策略**
1. **快速原型**: all-minilm (384维) + deepseek-r1:8b
2. **生产环境**: nomic-embed-text (768维) + deepseek-r1:32b  
3. **高精度需求**: bge-m3 (1024维) + qwen2.5-coder:32b
4. **资源受限**: all-minilm (384维) + deepseek-r1:8b

---

## ✅ **验证清单**

### **模型可用性检查**
- [ ] Ollama服务正常运行
- [ ] 所有向量化模型已下载
- [ ] GPU内存充足 (>16GB可用)
- [ ] 向量化测试成功

### **训练准备检查**  
- [ ] 训练数据集已准备
- [ ] LoRA配置已优化
- [ ] 硬件资源充足
- [ ] 备份策略已制定

### **集成验证检查**
- [ ] GUI界面模型选择正常
- [ ] 向量化流程完整
- [ ] 查询功能正常
- [ ] 性能指标达标
