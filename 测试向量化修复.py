#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试向量化Path导入修复
"""

import sys
import os
from pathlib import Path

def test_path_import():
    """测试Path导入修复"""
    print("🔍 测试Path导入修复...")
    
    try:
        # 添加src目录到路径
        src_path = Path(__file__).parent / 'src'
        sys.path.insert(0, str(src_path))
        
        # 尝试导入vectorize模块
        from gui.widgets.vectorize import VectorizeWidget
        print("✅ VectorizeWidget 导入成功")
        
        # 检查Path是否可用
        test_path = Path("test")
        print(f"✅ Path类可用: {test_path}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_vectorize_file_content():
    """测试vectorize.py文件内容"""
    print("\n🔍 测试vectorize.py文件内容...")
    
    try:
        vectorize_file = Path('src/gui/widgets/vectorize.py')
        if not vectorize_file.exists():
            print("❌ vectorize.py 文件不存在")
            return False
        
        content = vectorize_file.read_text(encoding='utf-8')
        
        # 检查Path导入
        if 'from pathlib import Path' in content:
            print("✅ Path导入已添加到文件顶部")
        else:
            print("❌ Path导入未找到")
            return False
        
        # 检查是否还有局部Path导入
        lines = content.split('\n')
        local_imports = []
        for i, line in enumerate(lines, 1):
            if 'from pathlib import Path' in line and i > 20:  # 跳过顶部导入
                local_imports.append(f"第{i}行: {line.strip()}")
        
        if local_imports:
            print(f"⚠️  发现局部Path导入: {local_imports}")
        else:
            print("✅ 没有冲突的局部Path导入")
        
        # 检查Path使用
        path_usage_count = content.count('Path(')
        print(f"✅ Path使用次数: {path_usage_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件内容测试失败: {e}")
        return False

def test_gui_startup():
    """测试GUI启动"""
    print("\n🔍 测试GUI启动准备...")
    
    try:
        # 检查必要文件
        required_files = [
            'gui_run.py',
            'src/gui/widgets/vectorize.py',
            'src/gui/widgets/dashboard.py',
            'config/models.yaml'
        ]
        
        missing_files = []
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            print(f"❌ 缺少必要文件: {missing_files}")
            return False
        else:
            print("✅ 所有必要文件存在")
        
        # 检查数据目录
        data_dirs = [
            'test_training_data/raw_documents',
            'test_training_data/raw_documents/enterprise_standards'
        ]
        
        existing_dirs = []
        for dir_path in data_dirs:
            if Path(dir_path).exists():
                existing_dirs.append(dir_path)
        
        if existing_dirs:
            print(f"✅ 数据目录存在: {existing_dirs}")
        else:
            print("⚠️  数据目录不存在，但不影响GUI启动")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI启动准备测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("向量化Path导入修复验证")
    print("=" * 60)
    
    tests = [
        ("Path导入测试", test_path_import),
        ("文件内容测试", test_vectorize_file_content),
        ("GUI启动准备", test_gui_startup)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 Path导入修复验证通过！")
        print("\n📋 下一步操作:")
        print("1. 启动GUI: python gui_run.py")
        print("2. 点击'向量化'按钮")
        print("3. 选择文件夹进行向量化")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
