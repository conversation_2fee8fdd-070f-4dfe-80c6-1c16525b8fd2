#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
问题修复验证脚本
验证所有4个问题的修复情况
"""

import sys
import os
from pathlib import Path

def test_pdf_libraries():
    """测试PDF处理库"""
    print("🔍 测试PDF处理库...")
    
    pdf_libs = []
    
    try:
        import pdfplumber
        pdf_libs.append('pdfplumber')
        print("✅ pdfplumber 可用")
    except ImportError:
        print("❌ pdfplumber 不可用")
    
    try:
        import PyPDF2
        pdf_libs.append('PyPDF2')
        print("✅ PyPDF2 可用")
    except ImportError:
        print("❌ PyPDF2 不可用")
    
    if pdf_libs:
        print(f"✅ PDF处理库状态: {', '.join(pdf_libs)} 可用")
        return True
    else:
        print("❌ 没有可用的PDF处理库")
        return False

def test_gui_navigation():
    """测试GUI导航修复"""
    print("\n🔍 测试GUI导航修复...")
    
    try:
        # 检查修复的代码
        dashboard_file = Path('src/gui/widgets/dashboard.py')
        if dashboard_file.exists():
            content = dashboard_file.read_text(encoding='utf-8')
            
            # 检查向量化跳转修复
            if 'main_window.tab_widget.setCurrentWidget(main_window.vectorize_widget)' in content:
                print("✅ 向量化页面跳转修复已应用")
            else:
                print("❌ 向量化页面跳转修复未找到")
                return False
            
            # 检查可视化跳转修复
            if 'main_window.tab_widget.setCurrentWidget(main_window.visualize_widget)' in content:
                print("✅ 可视化页面跳转修复已应用")
            else:
                print("❌ 可视化页面跳转修复未找到")
                return False
            
            return True
        else:
            print("❌ dashboard.py 文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ GUI导航测试失败: {e}")
        return False

def test_folder_selection():
    """测试文件夹选择功能"""
    print("\n🔍 测试文件夹选择功能...")
    
    try:
        vectorize_file = Path('src/gui/widgets/vectorize.py')
        if vectorize_file.exists():
            content = vectorize_file.read_text(encoding='utf-8')
            
            # 检查文件夹选择改进
            if 'test_training_data/raw_documents' in content:
                print("✅ 默认数据目录设置已应用")
            else:
                print("❌ 默认数据目录设置未找到")
                return False
            
            # 检查递归文件扫描
            if 'folder.rglob' in content:
                print("✅ 递归文件扫描功能已添加")
            else:
                print("❌ 递归文件扫描功能未找到")
                return False
            
            return True
        else:
            print("❌ vectorize.py 文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 文件夹选择测试失败: {e}")
        return False

def test_models_config():
    """测试模型配置"""
    print("\n🔍 测试模型配置...")
    
    try:
        models_file = Path('config/models.yaml')
        if models_file.exists():
            content = models_file.read_text(encoding='utf-8')
            
            # 检查向量化模型
            embedding_models = ['nomic-embed-text', 'bge-m3', 'all-minilm']
            found_models = []
            
            for model in embedding_models:
                if model in content:
                    found_models.append(model)
            
            if found_models:
                print(f"✅ 向量化模型配置: {', '.join(found_models)}")
            else:
                print("❌ 向量化模型配置未找到")
                return False
            
            # 检查大语言模型
            llm_models = ['deepseek-r1:32b', 'qwen2.5-coder:32b', 'deepseek-r1:8b']
            found_llms = []
            
            for model in llm_models:
                if model in content:
                    found_llms.append(model)
            
            if found_llms:
                print(f"✅ 大语言模型配置: {', '.join(found_llms)}")
            else:
                print("❌ 大语言模型配置未找到")
                return False
            
            return True
        else:
            print("❌ models.yaml 文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 模型配置测试失败: {e}")
        return False

def test_dual_format_processor():
    """测试双格式处理器"""
    print("\n🔍 测试双格式处理器...")
    
    try:
        # 添加src目录到路径
        sys.path.insert(0, str(Path(__file__).parent / 'src'))
        
        from dual_format_processor import DualFormatProcessor
        
        # 创建处理器
        config = {
            'output_dir': 'data/test_reports',
            'quality_thresholds': {
                'min_content_length': 50,
                'min_similarity': 0.5,
                'max_error_rate': 0.2,
                'min_completeness': 0.5
            }
        }
        
        processor = DualFormatProcessor(config)
        print("✅ 双格式处理器创建成功")
        
        # 检查PDF处理能力
        if hasattr(processor, '_validate_pdf_content'):
            print("✅ PDF处理功能可用")
        else:
            print("❌ PDF处理功能不可用")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 双格式处理器导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 双格式处理器测试失败: {e}")
        return False

def test_directory_structure():
    """测试目录结构"""
    print("\n🔍 测试目录结构...")
    
    required_dirs = [
        'test_training_data/raw_documents',
        'test_training_data/raw_documents_MD',
        'test_training_data/raw_documents/enterprise_standards',
        'test_training_data/raw_documents_MD/enterprise_standards'
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        path = Path(dir_path)
        if path.exists():
            print(f"✅ {dir_path} 存在")
        else:
            print(f"❌ {dir_path} 不存在")
            all_exist = False
    
    return all_exist

def main():
    """主函数"""
    print("=" * 60)
    print("问题修复验证")
    print("=" * 60)
    
    tests = [
        ("PDF处理库", test_pdf_libraries),
        ("GUI导航修复", test_gui_navigation),
        ("文件夹选择功能", test_folder_selection),
        ("模型配置", test_models_config),
        ("双格式处理器", test_dual_format_processor),
        ("目录结构", test_directory_structure)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有问题修复验证通过！")
    else:
        print("⚠️  部分问题仍需解决")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
